"""
Mock Database Utilities for YAML Builder v2 Testing

This module provides mock database utility functions for testing the YAML Builder v2.
"""

import os
import sys
import json
import logging
from typing import Dict, List, Tuple, Any, Optional

# Set up logging
logger = logging.getLogger('mock_db_utils')

def execute_query(query: str, params: Optional[Tuple] = None, schema_name: Optional[str] = None) -> Tuple[bool, List[str], Optional[List[Tuple]]]:
    """
    Mock implementation of execute_query.
    
    Args:
        query: SQL query to execute
        params: Query parameters
        schema_name: Optional schema name to set as the search path
        
    Returns:
        Tuple containing:
            - Boolean indicating if query execution was successful
            - List of messages (warnings, errors, or success messages)
            - Query results (or None if query failed or no results)
    """
    messages = []
    
    try:
        logger.info(f"Mock execute_query: {query}")
        if params:
            logger.info(f"Params: {params}")
        if schema_name:
            logger.info(f"Schema: {schema_name}")
        
        # Handle different query types
        if query.strip().upper().startswith("SELECT"):
            # For SELECT queries, return mock results
            if "EXISTS" in query.upper():
                # For EXISTS queries, return True
                return True, ["Query executed successfully"], [(True,)]
            elif "go_id FROM" in query:
                # For GO ID queries, return empty result to simulate new GO
                return True, ["Query executed successfully"], []
            elif "id FROM" in query and "output_stack" in query:
                # For output stack ID queries, return empty result to simulate new output stack
                return True, ["Query executed successfully"], []
            else:
                # For other SELECT queries, return empty result
                return True, ["Query executed successfully"], []
        elif query.strip().upper().startswith("INSERT"):
            # For INSERT queries, return success
            if "RETURNING" in query.upper():
                # For INSERT queries with RETURNING, return mock ID
                return True, ["Query executed successfully"], [("mock_id",)]
            else:
                return True, ["Query executed successfully"], None
        elif query.strip().upper().startswith("UPDATE"):
            # For UPDATE queries, return success
            return True, ["Query executed successfully"], None
        elif query.strip().upper().startswith("DELETE"):
            # For DELETE queries, return success
            return True, ["Query executed successfully"], None
        elif query.strip().upper().startswith("CREATE"):
            # For CREATE queries, return success
            return True, ["Query executed successfully"], None
        elif query.strip().upper().startswith("ALTER"):
            # For ALTER queries, return success
            return True, ["Query executed successfully"], None
        else:
            # For other queries, return success
            return True, ["Query executed successfully"], None
    except Exception as e:
        error_msg = f"Error executing mock query: {str(e)}"
        logger.error(error_msg, exc_info=True)
        messages.append(error_msg)
        return False, messages, None

def save_to_mongodb(collection_name: str, document: Dict) -> Tuple[bool, List[str], Optional[str]]:
    """
    Mock implementation of save_to_mongodb.
    
    Args:
        collection_name: Name of the collection to save to
        document: Document to save
        
    Returns:
        Tuple containing:
            - Boolean indicating if save was successful
            - List of messages (warnings, errors, or success messages)
            - Document ID (or None if save failed)
    """
    messages = []
    
    try:
        logger.info(f"Mock save_to_mongodb: {collection_name}")
        logger.info(f"Document: {json.dumps(document, indent=2)}")
        
        messages.append(f"Document saved to MongoDB collection '{collection_name}'")
        return True, messages, "mock_id"
    except Exception as e:
        error_msg = f"Error saving to mock MongoDB: {str(e)}"
        logger.error(error_msg, exc_info=True)
        messages.append(error_msg)
        return False, messages, None
