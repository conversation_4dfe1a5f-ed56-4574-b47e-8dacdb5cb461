{"timestamp": "2025-06-24T04:45:01.284602", "operation": "process_mongo_entities_to_workflow_temp", "input_data": {"schema": "workflow_temp"}, "result": {"total_processed": 0, "successful_inserts": 0, "failed_inserts": 0, "details": []}, "status": "success"}
{"timestamp": "2025-06-24T11:40:14.855881", "operation": "insert_entity_to_workflow_runtime", "input_data": {"_id": "685a8d23a42e36439cfded3a", "entity_id": "E36", "name": "TestEntity", "display_name": "Test Entity", "tenant_id": "t999", "tenant_name": "", "business_domain": "Testing", "category": "Test Management", "tags": [], "archival_strategy": "", "icon": "", "colour_theme": "", "version": 1, "status": "draft", "type": "transaction", "description": "Test entity for entities_drafts collection", "table_name": "e48_TestEntity", "natural_language": "Entity: TestEntity\n- Entity Name: TestEntity\n- Display Name: Test Entity\n- Type: transaction\n- Description: Test entity for entities_drafts collection\n- Business Domain: Testing\n- Category: Test Management", "created_at": "2025-06-24T11:33:55.338334", "updated_at": "2025-06-24T11:33:55.338343", "created_by": "Tarun", "updated_by": "Tarun", "entity_status": "new", "changes_detected": [], "icon_type": "text", "icon_content": ""}, "result": {"success": true, "inserted_id": "E36", "schema": "workflow_runtime", "entity_id": "E36", "name": "TestEntity", "attributes_inserted": 0, "original_entity_id": "E48"}, "status": "success"}
{"timestamp": "2025-06-24T11:40:14.857936", "operation": "process_mongo_entities_to_workflow_runtime", "input_data": {"schema": "workflow_runtime"}, "result": {"total_processed": 2, "successful_inserts": 1, "failed_inserts": 0, "details": [{"entity_id": "E13", "status": "skipped", "reason": "already_exists"}, {"entity_id": "E36", "status": "success", "details": {"success": true, "inserted_id": "E36", "schema": "workflow_runtime", "entity_id": "E36", "name": "TestEntity", "attributes_inserted": 0, "original_entity_id": "E48"}}]}, "status": "success"}
{"timestamp": "2025-06-24T13:13:45.272470", "operation": "insert_entity_to_workflow_temp", "input_data": {"_id": "685a9abda30d43107e83f575", "entity_id": "E46", "name": "TestEntity2", "display_name": "Test Entity 2", "tenant_id": "t999", "tenant_name": "", "business_domain": "Testing", "category": "Test Management", "tags": [], "archival_strategy": "", "icon": "", "colour_theme": "", "version": 1, "status": "draft", "type": "master", "description": "Second test entity for mongo-save verification", "table_name": "e48_TestEntity2", "natural_language": "Entity: TestEntity2\n- Entity Name: TestEntity2\n- Display Name: Test Entity 2\n- Type: master\n- Description: Second test entity for mongo-save verification\n- Business Domain: Testing\n- Category: Test Management", "created_at": "2025-06-24T12:31:57.709761", "updated_at": "2025-06-24T12:31:57.709768", "created_by": "Tarun", "updated_by": "Tarun", "entity_status": "new", "changes_detected": [], "icon_type": "text", "icon_content": ""}, "result": {"success": true, "inserted_id": "E46", "schema": "workflow_temp", "entity_id": "E46", "name": "TestEntity2", "attributes_inserted": 0, "original_entity_id": "E48"}, "status": "success"}
{"timestamp": "2025-06-24T13:13:45.275452", "operation": "process_mongo_entities_to_workflow_temp", "input_data": {"schema": "workflow_temp"}, "result": {"total_processed": 1, "successful_inserts": 1, "failed_inserts": 0, "details": [{"entity_id": "E46", "status": "success", "details": {"success": true, "inserted_id": "E46", "schema": "workflow_temp", "entity_id": "E46", "name": "TestEntity2", "attributes_inserted": 0, "original_entity_id": "E48"}}]}, "status": "success"}
