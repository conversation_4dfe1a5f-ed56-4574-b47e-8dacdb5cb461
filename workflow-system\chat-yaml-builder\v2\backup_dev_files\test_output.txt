2025-05-11 04:59:29,879 - prescriptive_parser - INFO - Initializing PrescriptiveParser
2025-05-11 04:59:29,879 - component_deployer - INFO - ComponentDeployer initialized with use_temp_schema=True
2025-05-11 04:59:29,884 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:29,942 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:29,948 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:29,975 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:29,984 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:29,994 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:30,003 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:30,013 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:30,023 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:30,031 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:30,032 - component_deployer - INFO - Analyzing workflow_runtime schema to create necessary tables in temporary schema
2025-05-11 04:59:30,038 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:30,046 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:30,052 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:30,059 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:30,070 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:30,080 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:30,089 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:30,100 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:30,110 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:30,118 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:30,127 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:30,136 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:30,144 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:30,153 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:30,163 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:30,172 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:30,183 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:30,192 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:30,202 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:30,213 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:30,221 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:30,230 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:30,242 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:30,251 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:30,259 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:30,268 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:30,278 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:30,286 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:30,295 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:30,304 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:30,312 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:30,321 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:30,331 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:30,341 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:30,351 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:30,359 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:30,366 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:30,375 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:30,384 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:30,390 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:30,399 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:30,409 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:30,415 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:30,424 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:30,434 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:30,443 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:30,453 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:30,462 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:30,469 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:30,478 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:30,486 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:30,494 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:30,505 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:30,515 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:30,524 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:30,536 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:30,546 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:30,553 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:30,565 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:30,575 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:30,582 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:30,593 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:30,604 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:30,613 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:30,625 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:30,635 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:30,642 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:30,653 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:30,664 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:30,673 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:30,685 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:30,694 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:30,702 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:30,711 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:30,721 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:30,729 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:30,740 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:30,748 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:30,755 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:30,766 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:30,777 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:30,787 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:30,799 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:30,809 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:30,819 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:30,830 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:30,841 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:30,850 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:30,859 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:30,869 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:30,878 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:30,887 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:30,896 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:30,903 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:30,913 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:30,922 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:30,931 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:30,941 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:30,951 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:30,960 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:30,970 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:30,978 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:30,985 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:30,995 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:31,003 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:31,010 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:31,019 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:31,029 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:31,036 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:31,045 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:31,053 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:31,060 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:31,070 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:31,080 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:31,087 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:31,097 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:31,109 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:31,116 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:31,128 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:31,138 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:31,146 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:31,157 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:31,166 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:31,174 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:31,186 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:31,194 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:31,202 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:31,213 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:31,222 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:31,231 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:31,242 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:31,251 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:31,260 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:31,270 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:31,279 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:31,286 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:31,296 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:31,304 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:31,313 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:31,323 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:31,334 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:31,341 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:31,352 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:31,362 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:31,371 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:31,382 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:31,392 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:31,401 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:31,412 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:31,422 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:31,430 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:31,439 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:31,450 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:31,458 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:31,470 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:31,478 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:31,487 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:31,498 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:31,508 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:31,517 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:31,529 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:31,540 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:31,548 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:31,560 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:31,571 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:31,578 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:31,589 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:31,599 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:31,608 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:31,620 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:31,629 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:31,638 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:31,649 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:31,658 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:31,665 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:31,676 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:31,685 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:31,693 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:31,705 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:31,714 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:31,722 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:31,732 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:31,742 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:31,751 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:31,762 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:31,771 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:31,781 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:31,791 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:31,800 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:31,807 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:31,816 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:31,826 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:31,835 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:31,847 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:31,857 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:31,867 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:31,879 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:31,888 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:31,896 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:31,958 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:31,966 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:31,975 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:31,982 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:31,991 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:31,992 - db_utils - ERROR - Database error: column "input_stack_id" referenced in foreign key constraint does not exist
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.UndefinedColumn: column "input_stack_id" referenced in foreign key constraint does not exist

2025-05-11 04:59:31,997 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:32,005 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:32,012 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:32,019 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:32,027 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:32,034 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:32,042 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:32,049 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:32,056 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:32,064 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:32,065 - db_utils - ERROR - Database error: there is no unique constraint matching given keys for referenced table "roles"
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.InvalidForeignKey: there is no unique constraint matching given keys for referenced table "roles"

2025-05-11 04:59:32,070 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:32,077 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:32,085 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:32,086 - db_utils - ERROR - Database error: column "output_stack_id" referenced in foreign key constraint does not exist
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.UndefinedColumn: column "output_stack_id" referenced in foreign key constraint does not exist

2025-05-11 04:59:32,092 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:32,093 - db_utils - ERROR - Database error: column "id" referenced in foreign key constraint does not exist
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.UndefinedColumn: column "id" referenced in foreign key constraint does not exist

2025-05-11 04:59:32,098 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:32,099 - db_utils - ERROR - Database error: column "input_stack_id" referenced in foreign key constraint does not exist
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.UndefinedColumn: column "input_stack_id" referenced in foreign key constraint does not exist

2025-05-11 04:59:32,105 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:32,106 - db_utils - ERROR - Database error: column "id" referenced in foreign key constraint does not exist
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.UndefinedColumn: column "id" referenced in foreign key constraint does not exist

2025-05-11 04:59:32,110 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:32,111 - db_utils - ERROR - Database error: column "input_stack_id" referenced in foreign key constraint does not exist
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.UndefinedColumn: column "input_stack_id" referenced in foreign key constraint does not exist

2025-05-11 04:59:32,117 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:32,118 - db_utils - ERROR - Database error: column "id" referenced in foreign key constraint does not exist
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.UndefinedColumn: column "id" referenced in foreign key constraint does not exist

2025-05-11 04:59:32,123 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:32,124 - db_utils - ERROR - Database error: column "input_stack_id" referenced in foreign key constraint does not exist
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.UndefinedColumn: column "input_stack_id" referenced in foreign key constraint does not exist

2025-05-11 04:59:32,130 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:32,131 - db_utils - ERROR - Database error: column "id" referenced in foreign key constraint does not exist
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.UndefinedColumn: column "id" referenced in foreign key constraint does not exist

2025-05-11 04:59:32,136 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:32,137 - db_utils - ERROR - Database error: column "input_stack_id" referenced in foreign key constraint does not exist
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.UndefinedColumn: column "input_stack_id" referenced in foreign key constraint does not exist

2025-05-11 04:59:32,142 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:32,150 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:32,157 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:32,165 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:32,173 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:32,180 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:32,188 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:32,196 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:32,203 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:32,209 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:32,210 - db_utils - ERROR - Database error: column "inherits_from" referenced in foreign key constraint does not exist
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.UndefinedColumn: column "inherits_from" referenced in foreign key constraint does not exist

2025-05-11 04:59:32,216 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:32,217 - db_utils - ERROR - Database error: column "tenant_id" referenced in foreign key constraint does not exist
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.UndefinedColumn: column "tenant_id" referenced in foreign key constraint does not exist

2025-05-11 04:59:32,222 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:32,231 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:32,238 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:32,246 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:32,253 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:32,254 - db_utils - ERROR - Database error: there is no unique constraint matching given keys for referenced table "output_items"
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.InvalidForeignKey: there is no unique constraint matching given keys for referenced table "output_items"

2025-05-11 04:59:32,258 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:32,259 - db_utils - ERROR - Database error: there is no unique constraint matching given keys for referenced table "output_items"
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.InvalidForeignKey: there is no unique constraint matching given keys for referenced table "output_items"

2025-05-11 04:59:32,264 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:32,265 - db_utils - ERROR - Database error: there is no unique constraint matching given keys for referenced table "output_items"
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.InvalidForeignKey: there is no unique constraint matching given keys for referenced table "output_items"

2025-05-11 04:59:32,270 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:32,271 - db_utils - ERROR - Database error: there is no unique constraint matching given keys for referenced table "output_items"
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.InvalidForeignKey: there is no unique constraint matching given keys for referenced table "output_items"

2025-05-11 04:59:32,277 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:32,285 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:32,292 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:32,300 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:32,308 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:32,315 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:32,316 - db_utils - ERROR - Database error: column "id" referenced in foreign key constraint does not exist
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.UndefinedColumn: column "id" referenced in foreign key constraint does not exist

2025-05-11 04:59:32,321 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:32,322 - db_utils - ERROR - Database error: column "input_stack_id" referenced in foreign key constraint does not exist
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.UndefinedColumn: column "input_stack_id" referenced in foreign key constraint does not exist

2025-05-11 04:59:32,327 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:32,328 - db_utils - ERROR - Database error: column "id" referenced in foreign key constraint does not exist
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.UndefinedColumn: column "id" referenced in foreign key constraint does not exist

2025-05-11 04:59:32,333 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:32,334 - db_utils - ERROR - Database error: column "input_stack_id" referenced in foreign key constraint does not exist
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.UndefinedColumn: column "input_stack_id" referenced in foreign key constraint does not exist

2025-05-11 04:59:32,338 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:32,345 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:32,346 - db_utils - ERROR - Database error: column "tenant_id" referenced in foreign key constraint does not exist
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.UndefinedColumn: column "tenant_id" referenced in foreign key constraint does not exist

2025-05-11 04:59:32,351 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:32,352 - db_utils - ERROR - Database error: constraint "local_objectives_go_id_fkey" for relation "local_objectives" already exists
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.DuplicateObject: constraint "local_objectives_go_id_fkey" for relation "local_objectives" already exists

2025-05-11 04:59:32,356 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:32,362 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:32,369 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:32,375 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:32,380 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:32,387 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:32,393 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:32,399 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:32,407 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:32,414 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:32,420 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:32,428 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:32,435 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:32,435 - db_utils - ERROR - Database error: column "id" referenced in foreign key constraint does not exist
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.UndefinedColumn: column "id" referenced in foreign key constraint does not exist

2025-05-11 04:59:32,439 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:32,440 - db_utils - ERROR - Database error: column "output_stack_id" referenced in foreign key constraint does not exist
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.UndefinedColumn: column "output_stack_id" referenced in foreign key constraint does not exist

2025-05-11 04:59:32,444 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:32,444 - db_utils - ERROR - Database error: column "id" referenced in foreign key constraint does not exist
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.UndefinedColumn: column "id" referenced in foreign key constraint does not exist

2025-05-11 04:59:32,449 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:32,450 - db_utils - ERROR - Database error: column "output_stack_id" referenced in foreign key constraint does not exist
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.UndefinedColumn: column "output_stack_id" referenced in foreign key constraint does not exist

2025-05-11 04:59:32,456 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:32,457 - db_utils - ERROR - Database error: column "id" referenced in foreign key constraint does not exist
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.UndefinedColumn: column "id" referenced in foreign key constraint does not exist

2025-05-11 04:59:32,463 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:32,464 - db_utils - ERROR - Database error: column "input_stack_id" referenced in foreign key constraint does not exist
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.UndefinedColumn: column "input_stack_id" referenced in foreign key constraint does not exist

2025-05-11 04:59:32,471 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:32,472 - db_utils - ERROR - Database error: column "id" referenced in foreign key constraint does not exist
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.UndefinedColumn: column "id" referenced in foreign key constraint does not exist

2025-05-11 04:59:32,477 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:32,478 - db_utils - ERROR - Database error: column "input_stack_id" referenced in foreign key constraint does not exist
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.UndefinedColumn: column "input_stack_id" referenced in foreign key constraint does not exist

2025-05-11 04:59:32,482 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:32,489 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:32,496 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:32,504 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:32,512 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:32,512 - db_utils - ERROR - Database error: column "id" referenced in foreign key constraint does not exist
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.UndefinedColumn: column "id" referenced in foreign key constraint does not exist

2025-05-11 04:59:32,518 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:32,519 - db_utils - ERROR - Database error: column "input_stack_id" referenced in foreign key constraint does not exist
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.UndefinedColumn: column "input_stack_id" referenced in foreign key constraint does not exist

2025-05-11 04:59:32,525 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:32,526 - db_utils - ERROR - Database error: column "id" referenced in foreign key constraint does not exist
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.UndefinedColumn: column "id" referenced in foreign key constraint does not exist

2025-05-11 04:59:32,532 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:32,533 - db_utils - ERROR - Database error: column "input_stack_id" referenced in foreign key constraint does not exist
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.UndefinedColumn: column "input_stack_id" referenced in foreign key constraint does not exist

2025-05-11 04:59:32,537 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:32,540 - component_deployer - INFO - Successfully analyzed workflow_runtime schema and created necessary tables
2025-05-11 04:59:32,540 - test_sample_files - INFO - Testing deployment of sample files
2025-05-11 04:59:32,540 - test_sample_files - INFO - Testing entity parser
2025-05-11 04:59:32,540 - prescriptive_parser - INFO - Parsing entities prescriptive text
2025-05-11 04:59:32,540 - entity_parser - INFO - Starting to parse entity definitions
2025-05-11 04:59:32,540 - entity_parser - INFO - Parsing entity: Employee
2025-05-11 04:59:32,541 - entity_parser - INFO - Successfully parsed entity 'Employee'
2025-05-11 04:59:32,541 - entity_parser - INFO - Parsing entity: Employee
2025-05-11 04:59:32,541 - entity_parser - INFO - Successfully parsed entity 'Employee'
2025-05-11 04:59:32,541 - entity_parser - INFO - Parsing entity: Department
2025-05-11 04:59:32,541 - entity_parser - INFO - Successfully parsed entity 'Department'
2025-05-11 04:59:32,541 - entity_parser - INFO - Successfully parsed 2 entities
2025-05-11 04:59:32,541 - test_sample_files - INFO - Successfully parsed 2 entities
2025-05-11 04:59:32,541 - test_sample_files - INFO - Testing deployment of entities
2025-05-11 04:59:32,543 - component_deployer - INFO - Deploying component of type: entities
2025-05-11 04:59:32,548 - component_deployer - INFO - Using schema: workflow_temp
2025-05-11 04:59:32,548 - entity_deployer - INFO - Deploying entities to workflow_temp
2025-05-11 04:59:32,553 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:32,560 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:32,561 - entity_deployer - INFO - Inserted entity 'Department' into workflow_temp.entities
2025-05-11 04:59:32,566 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:32,572 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:32,574 - entity_deployer - INFO - Inserted attribute 'budget' for entity 'entity_department' into workflow_temp.entity_attributes
2025-05-11 04:59:32,578 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:32,580 - entity_deployer - INFO - Inserted attribute 'departmentId' for entity 'entity_department' into workflow_temp.entity_attributes
2025-05-11 04:59:32,585 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:32,586 - entity_deployer - INFO - Inserted attribute 'location' for entity 'entity_department' into workflow_temp.entity_attributes
2025-05-11 04:59:32,591 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:32,592 - entity_deployer - INFO - Inserted attribute 'managerId' for entity 'entity_department' into workflow_temp.entity_attributes
2025-05-11 04:59:32,597 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:32,598 - entity_deployer - INFO - Inserted attribute 'name' for entity 'entity_department' into workflow_temp.entity_attributes
2025-05-11 04:59:32,603 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:32,609 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:32,614 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:32,615 - entity_deployer - WARNING - Warning: Relationship 'one-to-many_Employee' in entity 'entity_department' references non-existent entity 'entity_employee'
2025-05-11 04:59:32,619 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:32,621 - entity_deployer - WARNING - Warning: Relationship 'one-to-one_Employee' in entity 'entity_department' references non-existent entity 'entity_employee'
2025-05-11 04:59:32,624 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:32,630 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:32,654 - entity_deployer - INFO - Created workflow_temp.entity_business_rules table
2025-05-11 04:59:32,660 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:32,668 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:32,670 - entity_deployer - INFO - Inserted business rule 'DEPT001' for entity 'entity_department' into workflow_temp.entity_business_rules
2025-05-11 04:59:32,676 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:32,684 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:32,685 - entity_deployer - INFO - Inserted entity 'Employee' into workflow_temp.entities
2025-05-11 04:59:32,691 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:32,696 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:32,697 - entity_deployer - INFO - Inserted attribute 'employeeId' for entity 'entity_employee' into workflow_temp.entity_attributes
2025-05-11 04:59:32,702 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:32,703 - entity_deployer - INFO - Inserted attribute 'firstName' for entity 'entity_employee' into workflow_temp.entity_attributes
2025-05-11 04:59:32,709 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:32,710 - entity_deployer - INFO - Inserted attribute 'fullName[derived]' for entity 'entity_employee' into workflow_temp.entity_attributes
2025-05-11 04:59:32,714 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:32,716 - entity_deployer - INFO - Inserted attribute 'lastName' for entity 'entity_employee' into workflow_temp.entity_attributes
2025-05-11 04:59:32,729 - entity_deployer - INFO - Entity deployment completed successfully
2025-05-11 04:59:32,729 - component_deployer - INFO - Component deployment succeeded
2025-05-11 04:59:32,729 - test_sample_files - INFO - Successfully deployed entities
2025-05-11 04:59:32,730 - test_sample_files - INFO - Testing GO parser
2025-05-11 04:59:32,730 - prescriptive_parser - INFO - Parsing go_definitions prescriptive text
2025-05-11 04:59:32,730 - go_parser - INFO - Starting to parse GO definitions
2025-05-11 04:59:32,730 - go_parser - INFO - Parsing GO: Leave Approval Process (ID: go001)
2025-05-11 04:59:32,731 - go_parser - INFO - Successfully parsed GO 'Leave Approval Process'
2025-05-11 04:59:32,731 - go_parser - INFO - Successfully parsed 1 global objectives
2025-05-11 04:59:32,731 - test_sample_files - INFO - Successfully parsed 1 global objectives
2025-05-11 04:59:32,731 - test_sample_files - INFO - Testing deployment of go_definitions
2025-05-11 04:59:32,733 - component_deployer - INFO - Deploying component of type: go_definitions
2025-05-11 04:59:32,736 - component_deployer - INFO - Using schema: workflow_temp
2025-05-11 04:59:32,740 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:32,741 - go_deployer - INFO - Deploying GO definitions to workflow_temp
2025-05-11 04:59:32,746 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:32,752 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:32,753 - go_deployer - INFO - Inserted GO 'Leave Approval Process' into workflow_temp.global_objectives
2025-05-11 04:59:32,757 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:32,763 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:32,766 - go_deployer - INFO - Created workflow_temp.go_lo_mapping table
2025-05-11 04:59:32,769 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:32,774 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:32,776 - go_deployer - INFO - Inserted LO mapping 'SubmitLeaveRequest' for GO 'go_leave_approval_process' into workflow_temp.go_lo_mapping
2025-05-11 04:59:32,779 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:32,781 - go_deployer - INFO - Inserted LO mapping 'UploadDocumentation' for GO 'go_leave_approval_process' into workflow_temp.go_lo_mapping
2025-05-11 04:59:32,786 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:32,787 - go_deployer - INFO - Inserted LO mapping 'ReviewLeaveRequest' for GO 'go_leave_approval_process' into workflow_temp.go_lo_mapping
2025-05-11 04:59:32,791 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:32,792 - go_deployer - INFO - Inserted LO mapping 'ApproveLeaveRequest' for GO 'go_leave_approval_process' into workflow_temp.go_lo_mapping
2025-05-11 04:59:32,796 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:32,797 - go_deployer - INFO - Inserted LO mapping 'RejectLeaveRequest' for GO 'go_leave_approval_process' into workflow_temp.go_lo_mapping
2025-05-11 04:59:32,801 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:32,802 - go_deployer - INFO - Inserted LO mapping 'NotifyEmployee' for GO 'go_leave_approval_process' into workflow_temp.go_lo_mapping
2025-05-11 04:59:32,806 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:32,807 - go_deployer - INFO - Inserted LO mapping 'UpdateCalendar' for GO 'go_leave_approval_process' into workflow_temp.go_lo_mapping
2025-05-11 04:59:32,811 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:32,812 - go_deployer - INFO - Inserted LO mapping 'UpdateLeaveBalance' for GO 'go_leave_approval_process' into workflow_temp.go_lo_mapping
2025-05-11 04:59:32,816 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:32,818 - go_deployer - INFO - Inserted LO mapping 'CancelLeaveRequest' for GO 'go_leave_approval_process' into workflow_temp.go_lo_mapping
2025-05-11 04:59:32,822 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:32,824 - go_deployer - INFO - Inserted LO mapping 'RollbackLeaveApproval' for GO 'go_leave_approval_process' into workflow_temp.go_lo_mapping
2025-05-11 04:59:32,828 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:32,829 - go_deployer - INFO - Inserted LO mapping 'RestoreLeaveBalance' for GO 'go_leave_approval_process' into workflow_temp.go_lo_mapping
2025-05-11 04:59:32,833 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:32,834 - go_deployer - INFO - Inserted LO mapping 'NotifyCancellation' for GO 'go_leave_approval_process' into workflow_temp.go_lo_mapping
2025-05-11 04:59:32,846 - go_deployer - INFO - GO deployment completed successfully
2025-05-11 04:59:32,846 - component_deployer - INFO - Component deployment succeeded
2025-05-11 04:59:32,846 - test_sample_files - INFO - Successfully deployed go_definitions
2025-05-11 04:59:32,846 - test_sample_files - INFO - Testing LO parser
2025-05-11 04:59:32,846 - prescriptive_parser - INFO - Parsing lo_definitions prescriptive text
2025-05-11 04:59:32,846 - lo_parser - INFO - Starting to parse LO definitions
2025-05-11 04:59:32,846 - lo_parser - INFO - Parsing LO: SubmitLeaveRequest
2025-05-11 04:59:32,848 - lo_parser - INFO - Successfully parsed LO 'SubmitLeaveRequest'
2025-05-11 04:59:32,848 - lo_parser - INFO - Parsing LO: ReviewLeaveRequest
2025-05-11 04:59:32,848 - lo_parser - INFO - Successfully parsed LO 'ReviewLeaveRequest'
2025-05-11 04:59:32,848 - lo_parser - INFO - Successfully parsed 2 local objectives
2025-05-11 04:59:32,848 - test_sample_files - INFO - Successfully parsed 2 local objectives
2025-05-11 04:59:32,848 - test_sample_files - INFO - Testing deployment of lo_definitions
2025-05-11 04:59:32,853 - component_deployer - INFO - Deploying component of type: lo_definitions
2025-05-11 04:59:32,859 - component_deployer - INFO - Using schema: workflow_temp
2025-05-11 04:59:32,864 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:32,872 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:32,873 - lo_deployer - INFO - Deploying LO definitions to workflow_temp
2025-05-11 04:59:32,879 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:32,887 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:32,894 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:32,895 - lo_deployer - INFO - Inserted LO 'ReviewLeaveRequest' into workflow_temp.local_objectives
2025-05-11 04:59:32,900 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:32,907 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:32,913 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:32,914 - lo_deployer - INFO - Inserted LO 'SubmitLeaveRequest' into workflow_temp.local_objectives
2025-05-11 04:59:32,927 - lo_deployer - INFO - LO deployment completed successfully
2025-05-11 04:59:32,927 - component_deployer - INFO - Component deployment succeeded
2025-05-11 04:59:32,927 - test_sample_files - INFO - Successfully deployed lo_definitions
2025-05-11 04:59:32,927 - test_sample_files - INFO - Testing role parser
2025-05-11 04:59:32,927 - prescriptive_parser - INFO - Parsing roles prescriptive text
2025-05-11 04:59:32,927 - role_parser - INFO - Starting to parse role definitions
2025-05-11 04:59:32,928 - role_parser - INFO - Parsing role: Employee (ID: emp001)
2025-05-11 04:59:32,928 - role_parser - INFO - Successfully parsed role 'Employee'
2025-05-11 04:59:32,928 - role_parser - INFO - Parsing role: Manager (ID: mgr001)
2025-05-11 04:59:32,928 - role_parser - INFO - Successfully parsed role 'Manager'
2025-05-11 04:59:32,928 - role_parser - INFO - Parsing role: HRManager (ID: hr001)
2025-05-11 04:59:32,928 - role_parser - INFO - Successfully parsed role 'HRManager'
2025-05-11 04:59:32,928 - role_parser - INFO - Parsing role: FinanceManager (ID: fin001)
2025-05-11 04:59:32,928 - role_parser - INFO - Successfully parsed role 'FinanceManager'
2025-05-11 04:59:32,928 - role_parser - INFO - Parsing role: SystemAdmin (ID: sys001)
2025-05-11 04:59:32,928 - role_parser - INFO - Successfully parsed role 'SystemAdmin'
2025-05-11 04:59:32,928 - role_parser - INFO - Successfully parsed 5 roles
2025-05-11 04:59:32,928 - test_sample_files - INFO - Successfully parsed 5 roles
2025-05-11 04:59:32,928 - test_sample_files - INFO - Testing deployment of roles
2025-05-11 04:59:32,931 - component_deployer - INFO - Deploying component of type: roles
2025-05-11 04:59:32,935 - component_deployer - INFO - Using schema: workflow_temp
2025-05-11 04:59:32,939 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:32,945 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:32,952 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:32,953 - role_deployer - INFO - Deploying roles to workflow_temp
2025-05-11 04:59:32,957 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:32,965 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:32,970 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:32,971 - db_utils - ERROR - Database error: null value in column "context_type" of relation "permission_contexts" violates not-null constraint
DETAIL:  Failing row contains (default, Default Context, Default permission context, null, {}, 2025-05-11 04:59:32.970843, 2025-05-11 04:59:32.970843).
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.NotNullViolation: null value in column "context_type" of relation "permission_contexts" violates not-null constraint
DETAIL:  Failing row contains (default, Default Context, Default permission context, null, {}, 2025-05-11 04:59:32.970843, 2025-05-11 04:59:32.970843).

2025-05-11 04:59:32,971 - component_deployer - INFO - Component deployment failed
2025-05-11 04:59:32,972 - test_sample_files - ERROR - Failed to deploy roles: ['Database error: null value in column "context_type" of relation "permission_contexts" violates not-null constraint\nDETAIL:  Failing row contains (default, Default Context, Default permission context, null, {}, 2025-05-11 04:59:32.970843, 2025-05-11 04:59:32.970843).\n']
2025-05-11 04:59:32,972 - test_sample_files - INFO - Rolling back temporary schema
2025-05-11 04:59:32,972 - component_deployer - INFO - Rolling back temporary schema
2025-05-11 04:59:32,976 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:33,012 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:33,018 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:33,026 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:33,034 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:33,041 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:33,048 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:33,054 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:33,062 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:33,069 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:33,070 - component_deployer - INFO - Analyzing workflow_runtime schema to create necessary tables in temporary schema
2025-05-11 04:59:33,074 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:33,083 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:33,087 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:33,095 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:33,106 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:33,116 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:33,124 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:33,135 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:33,145 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:33,153 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:33,162 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:33,170 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:33,178 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:33,187 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:33,197 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:33,205 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:33,216 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:33,226 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:33,233 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:33,242 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:33,251 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:33,258 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:33,267 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:33,276 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:33,282 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:33,291 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:33,299 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:33,306 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:33,316 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:33,326 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:33,333 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:33,342 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:33,351 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:33,358 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:33,368 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:33,378 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:33,395 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:33,406 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:33,416 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:33,425 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:33,436 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:33,445 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:33,451 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:33,462 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:33,471 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:33,477 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:33,487 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:33,495 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:33,501 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:33,510 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:33,517 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:33,523 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:33,533 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:33,541 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:33,549 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:33,559 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:33,567 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:33,573 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:33,584 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:33,592 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:33,600 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:33,609 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:33,619 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:33,627 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:33,636 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:33,644 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:33,652 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:33,661 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:33,670 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:33,677 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:33,686 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:33,695 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:33,701 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:33,710 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:33,719 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:33,726 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:33,734 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:33,744 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:33,751 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:33,760 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:33,768 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:33,775 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:33,783 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:33,792 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:33,802 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:33,811 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:33,820 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:33,828 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:33,837 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:33,847 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:33,855 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:33,864 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:33,873 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:33,880 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:33,889 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:33,899 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:33,906 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:33,914 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:33,924 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:33,931 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:33,941 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:33,949 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:33,957 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:33,966 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:33,976 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:33,984 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:33,993 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:34,002 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:34,011 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:34,019 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:34,028 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:34,035 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:34,044 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:34,052 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:34,060 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:34,069 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:34,079 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:34,087 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:34,096 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:34,105 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:34,113 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:34,122 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:34,132 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:34,140 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:34,149 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:34,159 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:34,167 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:34,176 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:34,185 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:34,194 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:34,203 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:34,212 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:34,219 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:34,228 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:34,238 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:34,246 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:34,257 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:34,267 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:34,276 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:34,286 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:34,295 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:34,303 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:34,312 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:34,321 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:34,329 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:34,338 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:34,347 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:34,354 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:34,362 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:34,372 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:34,378 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:34,388 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:34,398 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:34,406 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:34,416 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:34,426 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:34,433 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:34,444 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:34,452 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:34,459 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:34,470 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:34,480 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:34,488 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:34,498 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:34,507 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:34,515 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:34,525 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:34,535 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:34,544 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:34,554 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:34,564 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:34,571 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:34,581 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:34,589 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:34,596 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:34,608 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:34,619 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:34,628 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:34,639 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:34,648 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:34,654 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:34,664 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:34,674 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:34,682 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:34,692 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:34,702 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:34,709 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:34,719 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:34,728 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:34,735 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:34,745 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:34,755 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:34,763 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:34,773 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:34,783 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:34,791 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:34,800 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:34,809 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:34,816 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:34,877 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:34,886 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:34,892 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:34,899 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:34,907 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:34,908 - db_utils - ERROR - Database error: column "input_stack_id" referenced in foreign key constraint does not exist
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.UndefinedColumn: column "input_stack_id" referenced in foreign key constraint does not exist

2025-05-11 04:59:34,914 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:34,922 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:34,929 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:34,935 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:34,941 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:34,948 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:34,955 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:34,961 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:34,968 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:34,976 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:34,977 - db_utils - ERROR - Database error: there is no unique constraint matching given keys for referenced table "roles"
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.InvalidForeignKey: there is no unique constraint matching given keys for referenced table "roles"

2025-05-11 04:59:34,982 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:34,988 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:34,995 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:34,996 - db_utils - ERROR - Database error: column "output_stack_id" referenced in foreign key constraint does not exist
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.UndefinedColumn: column "output_stack_id" referenced in foreign key constraint does not exist

2025-05-11 04:59:35,002 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:35,003 - db_utils - ERROR - Database error: column "id" referenced in foreign key constraint does not exist
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.UndefinedColumn: column "id" referenced in foreign key constraint does not exist

2025-05-11 04:59:35,009 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:35,010 - db_utils - ERROR - Database error: column "input_stack_id" referenced in foreign key constraint does not exist
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.UndefinedColumn: column "input_stack_id" referenced in foreign key constraint does not exist

2025-05-11 04:59:35,015 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:35,016 - db_utils - ERROR - Database error: column "id" referenced in foreign key constraint does not exist
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.UndefinedColumn: column "id" referenced in foreign key constraint does not exist

2025-05-11 04:59:35,021 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:35,022 - db_utils - ERROR - Database error: column "input_stack_id" referenced in foreign key constraint does not exist
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.UndefinedColumn: column "input_stack_id" referenced in foreign key constraint does not exist

2025-05-11 04:59:35,028 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:35,028 - db_utils - ERROR - Database error: column "id" referenced in foreign key constraint does not exist
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.UndefinedColumn: column "id" referenced in foreign key constraint does not exist

2025-05-11 04:59:35,034 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:35,034 - db_utils - ERROR - Database error: column "input_stack_id" referenced in foreign key constraint does not exist
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.UndefinedColumn: column "input_stack_id" referenced in foreign key constraint does not exist

2025-05-11 04:59:35,040 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:35,040 - db_utils - ERROR - Database error: column "id" referenced in foreign key constraint does not exist
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.UndefinedColumn: column "id" referenced in foreign key constraint does not exist

2025-05-11 04:59:35,046 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:35,047 - db_utils - ERROR - Database error: column "input_stack_id" referenced in foreign key constraint does not exist
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.UndefinedColumn: column "input_stack_id" referenced in foreign key constraint does not exist

2025-05-11 04:59:35,052 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:35,059 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:35,067 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:35,074 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:35,081 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:35,089 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:35,096 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:35,102 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:35,109 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:35,116 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:35,117 - db_utils - ERROR - Database error: column "inherits_from" referenced in foreign key constraint does not exist
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.UndefinedColumn: column "inherits_from" referenced in foreign key constraint does not exist

2025-05-11 04:59:35,122 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:35,123 - db_utils - ERROR - Database error: column "tenant_id" referenced in foreign key constraint does not exist
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.UndefinedColumn: column "tenant_id" referenced in foreign key constraint does not exist

2025-05-11 04:59:35,128 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:35,136 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:35,143 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:35,150 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:35,158 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:35,158 - db_utils - ERROR - Database error: there is no unique constraint matching given keys for referenced table "output_items"
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.InvalidForeignKey: there is no unique constraint matching given keys for referenced table "output_items"

2025-05-11 04:59:35,164 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:35,165 - db_utils - ERROR - Database error: there is no unique constraint matching given keys for referenced table "output_items"
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.InvalidForeignKey: there is no unique constraint matching given keys for referenced table "output_items"

2025-05-11 04:59:35,170 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:35,171 - db_utils - ERROR - Database error: there is no unique constraint matching given keys for referenced table "output_items"
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.InvalidForeignKey: there is no unique constraint matching given keys for referenced table "output_items"

2025-05-11 04:59:35,177 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:35,178 - db_utils - ERROR - Database error: there is no unique constraint matching given keys for referenced table "output_items"
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.InvalidForeignKey: there is no unique constraint matching given keys for referenced table "output_items"

2025-05-11 04:59:35,183 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:35,190 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:35,196 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:35,204 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:35,211 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:35,217 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:35,218 - db_utils - ERROR - Database error: column "id" referenced in foreign key constraint does not exist
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.UndefinedColumn: column "id" referenced in foreign key constraint does not exist

2025-05-11 04:59:35,223 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:35,224 - db_utils - ERROR - Database error: column "input_stack_id" referenced in foreign key constraint does not exist
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.UndefinedColumn: column "input_stack_id" referenced in foreign key constraint does not exist

2025-05-11 04:59:35,229 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:35,230 - db_utils - ERROR - Database error: column "id" referenced in foreign key constraint does not exist
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.UndefinedColumn: column "id" referenced in foreign key constraint does not exist

2025-05-11 04:59:35,235 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:35,236 - db_utils - ERROR - Database error: column "input_stack_id" referenced in foreign key constraint does not exist
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.UndefinedColumn: column "input_stack_id" referenced in foreign key constraint does not exist

2025-05-11 04:59:35,241 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:35,248 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:35,249 - db_utils - ERROR - Database error: column "tenant_id" referenced in foreign key constraint does not exist
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.UndefinedColumn: column "tenant_id" referenced in foreign key constraint does not exist

2025-05-11 04:59:35,255 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:35,255 - db_utils - ERROR - Database error: constraint "local_objectives_go_id_fkey" for relation "local_objectives" already exists
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.DuplicateObject: constraint "local_objectives_go_id_fkey" for relation "local_objectives" already exists

2025-05-11 04:59:35,261 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:35,268 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:35,275 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:35,283 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:35,289 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:35,296 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:35,304 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:35,310 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:35,318 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:35,325 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:35,331 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:35,338 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:35,344 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:35,344 - db_utils - ERROR - Database error: column "id" referenced in foreign key constraint does not exist
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.UndefinedColumn: column "id" referenced in foreign key constraint does not exist

2025-05-11 04:59:35,350 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:35,350 - db_utils - ERROR - Database error: column "output_stack_id" referenced in foreign key constraint does not exist
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.UndefinedColumn: column "output_stack_id" referenced in foreign key constraint does not exist

2025-05-11 04:59:35,356 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:35,357 - db_utils - ERROR - Database error: column "id" referenced in foreign key constraint does not exist
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.UndefinedColumn: column "id" referenced in foreign key constraint does not exist

2025-05-11 04:59:35,361 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:35,361 - db_utils - ERROR - Database error: column "output_stack_id" referenced in foreign key constraint does not exist
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.UndefinedColumn: column "output_stack_id" referenced in foreign key constraint does not exist

2025-05-11 04:59:35,366 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:35,367 - db_utils - ERROR - Database error: column "id" referenced in foreign key constraint does not exist
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.UndefinedColumn: column "id" referenced in foreign key constraint does not exist

2025-05-11 04:59:35,371 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:35,372 - db_utils - ERROR - Database error: column "input_stack_id" referenced in foreign key constraint does not exist
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.UndefinedColumn: column "input_stack_id" referenced in foreign key constraint does not exist

2025-05-11 04:59:35,375 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:35,376 - db_utils - ERROR - Database error: column "id" referenced in foreign key constraint does not exist
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.UndefinedColumn: column "id" referenced in foreign key constraint does not exist

2025-05-11 04:59:35,381 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:35,382 - db_utils - ERROR - Database error: column "input_stack_id" referenced in foreign key constraint does not exist
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.UndefinedColumn: column "input_stack_id" referenced in foreign key constraint does not exist

2025-05-11 04:59:35,386 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:35,391 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:35,396 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:35,402 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:35,408 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:35,409 - db_utils - ERROR - Database error: column "id" referenced in foreign key constraint does not exist
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.UndefinedColumn: column "id" referenced in foreign key constraint does not exist

2025-05-11 04:59:35,412 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:35,413 - db_utils - ERROR - Database error: column "input_stack_id" referenced in foreign key constraint does not exist
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.UndefinedColumn: column "input_stack_id" referenced in foreign key constraint does not exist

2025-05-11 04:59:35,417 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:35,418 - db_utils - ERROR - Database error: column "id" referenced in foreign key constraint does not exist
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.UndefinedColumn: column "id" referenced in foreign key constraint does not exist

2025-05-11 04:59:35,423 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:35,424 - db_utils - ERROR - Database error: column "input_stack_id" referenced in foreign key constraint does not exist
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.UndefinedColumn: column "input_stack_id" referenced in foreign key constraint does not exist

2025-05-11 04:59:35,428 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 04:59:35,430 - component_deployer - INFO - Successfully analyzed workflow_runtime schema and created necessary tables
2025-05-11 04:59:35,430 - component_deployer - INFO - Rollback completed successfully
2025-05-11 04:59:35,430 - test_sample_files - INFO - Successfully rolled back temporary schema
Deployment tests completed
