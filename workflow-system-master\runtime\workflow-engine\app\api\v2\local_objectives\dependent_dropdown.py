"""
Dependent Dropdown Service for Local Objectives v2 API

This module handles dependent dropdown queries where child dropdown options
depend on the selected value of a parent field.
"""

import logging
from typing import Optional, List, Dict, Any
from uuid import UUID

from sqlalchemy.orm import Session
from sqlalchemy.sql import text

from .models import DropdownOption

logger = logging.getLogger(__name__)

class DependentDropdownService:
    """Service for handling dependent dropdown queries"""
    
    def __init__(self, db_session: Session):
        self.db = db_session
        self.logger = logger
    
    def get_dependent_options(
        self, 
        child_field_id: str,
        parent_field_value: str,
        additional_filters: Optional[Dict[str, Any]] = None
    ) -> List[DropdownOption]:
        """
        Get dropdown options for a child field based on parent field value.
        
        Args:
            child_field_id (str): The child field item_id (e.g., "GO1.LO1.IP1.IT8")
            parent_field_value (str): The selected value of the parent field
            additional_filters (Dict[str, Any], optional): Additional filter conditions
            
        Returns:
            List[DropdownOption]: List of available options for the child field
        """
        try:
            self.logger.info(f"Getting dependent options for child_field: {child_field_id}, parent_value: {parent_field_value}")
            
            # Get the dropdown data source configuration for the child field
            query = """
            SELECT dds.source_type, dds.query_text, dds.function_name, dds.function_params,
                dds.value_field, dds.display_field, dds.depends_on_fields
            FROM workflow_runtime.dropdown_data_sources dds
            WHERE dds.input_item_id = :child_field_id
            """

            
            result = self.db.execute(text(query), {"child_field_id": child_field_id}).fetchone()
            
            if not result:
                self.logger.warning(f"No dropdown data source found for child field: {child_field_id}")
                return []
            
            source_type = result.source_type
            
            if source_type == "database":
                return self._execute_dependent_database_query(result, parent_field_value, additional_filters)
            elif source_type == "function":
                return self._execute_dependent_function_query(result, parent_field_value, additional_filters)
            else:
                self.logger.warning(f"Unknown dropdown source type: {source_type}")
                return []
                
        except Exception as e:
            self.logger.error(f"Error getting dependent dropdown options: {str(e)}")
            return []
    
    def _execute_dependent_database_query(
        self, 
        source_config, 
        parent_value: str, 
        additional_filters: Optional[Dict[str, Any]]
    ) -> List[DropdownOption]:
        """Execute database query for dependent dropdown options."""
        try:
            query_text = source_config.query_text
            value_field = source_config.value_field
            display_field = source_config.display_field
            #filter_column = source_config.filter_column or source_config.parent_field_name
            filter_column = None  # Not needed since query already has :parent_value placeholder

            
            # Build parameters
            params = {"parent_value": parent_value}
            
            # Replace parent value placeholder in query
            if ":parent_value" not in query_text and filter_column:
                # If query doesn't have placeholder, add WHERE clause
                if "WHERE" in query_text.upper():
                    query_text += f" AND {filter_column} = :parent_value"
                else:
                    query_text += f" WHERE {filter_column} = :parent_value"
            
            # Add additional filters if provided (skip tenant_id since entities belong to specific tenants)
            if additional_filters:
                for idx, (key, val) in enumerate(additional_filters.items()):
                    # Skip tenant_id filter since entities and attributes belong to a particular tenant only
                    if key == "tenant_id":
                        continue
                        
                    param_name = f"add_filter_{idx}"
                    params[param_name] = val
                    
                    if "WHERE" in query_text.upper():
                        query_text += f" AND {key} = :{param_name}"
                    else:
                        query_text += f" WHERE {key} = :{param_name}"

            
            # Add ordering
            if "ORDER BY" not in query_text.upper():
                query_text += f" ORDER BY {display_field or value_field}"
            
            self.logger.info(f"Executing dependent dropdown query: {query_text}")
            self.logger.info(f"Parameters: {params}")
            
            result = self.db.execute(text(query_text), params).fetchall()
            
            # Format the results
            options = []
            for row in result:
                # Convert row to dict if needed
                if hasattr(row, '_asdict'):
                    row_dict = row._asdict()
                else:
                    row_dict = dict(row)
                
                option = DropdownOption(
                    value=str(row_dict.get(value_field, "")),
                    label=str(row_dict.get(display_field or value_field, "")),
                    metadata={}
                )
                options.append(option)
            
            self.logger.info(f"Found {len(options)} dependent options")
            return options
            
        except Exception as e:
            self.logger.error(f"Error executing dependent database query: {str(e)}")
            return []
    
    def _execute_dependent_function_query(
        self, 
        source_config, 
        parent_value: str, 
        additional_filters: Optional[Dict[str, Any]]
    ) -> List[DropdownOption]:
        """Execute function for dependent dropdown options."""
        try:
            function_name = source_config.function_name
            function_params = source_config.function_params or {}
            
            # Add parent value to function parameters
            function_params["parent_value"] = parent_value
            
            # Merge additional filters into function parameters
            if additional_filters:
                function_params.update(additional_filters)
            
            # Import and execute the function
            from app.services.function_repository import function_repository
            
            # Execute the function
            result = function_repository.auto_execute(function_name, self.db, **function_params)
            
            if isinstance(result, list):
                # Convert to DropdownOption objects
                options = []
                for item in result:
                    if isinstance(item, dict):
                        option = DropdownOption(
                            value=str(item.get("value", "")),
                            label=str(item.get("label", item.get("display", ""))),
                            metadata=item.get("metadata", {})
                        )
                        options.append(option)
                
                return options
            else:
                self.logger.error(f"Function {function_name} returned invalid result type: {type(result)}")
                return []
                
        except Exception as e:
            self.logger.error(f"Error executing dependent function query: {str(e)}")
            return []

    def get_system_dependent_options(
        self,
        child_field_id: str,
        parent_values: Dict[str, Any],
        nested_function_id: Optional[str] = None
    ) -> List[DropdownOption]:
        """
        Get options for system_dependent source type fields.
        
        Args:
            child_field_id (str): The child field item_id
            parent_values (Dict[str, Any]): Dictionary of parent field values
            nested_function_id (str, optional): Nested function to execute
            
        Returns:
            List[DropdownOption]: List of available options
        """
        try:
            self.logger.info(f"Getting system dependent options for {child_field_id} with parent values: {parent_values}")
            
            if not nested_function_id:
                self.logger.warning(f"No nested function ID provided for system dependent field: {child_field_id}")
                return []
            
            # Get the nested function definition
            query = """
            SELECT nf.function_name, nf.function_type, nf.parameters
            FROM workflow_runtime.lo_nested_functions nf
            WHERE nf.nested_function_id = :nested_function_id
            """
            
            result = self.db.execute(text(query), {"nested_function_id": nested_function_id}).fetchone()
            
            if not result:
                self.logger.warning(f"Nested function {nested_function_id} not found")
                return []
            
            function_name = result.function_name
            
            # Execute the nested function with parent values as parameters
            from app.services.function_repository import function_repository
            
            function_result = function_repository.auto_execute(
                function_name, 
                self.db,
                **parent_values
            )
            
            # Convert result to dropdown options
            if isinstance(function_result, list):
                options = []
                for item in function_result:
                    if isinstance(item, dict):
                        option = DropdownOption(
                            value=str(item.get("value", "")),
                            label=str(item.get("label", item.get("display", ""))),
                            metadata=item.get("metadata", {})
                        )
                        options.append(option)
                    else:
                        # Simple value
                        option = DropdownOption(
                            value=str(item),
                            label=str(item),
                            metadata={}
                        )
                        options.append(option)
                
                return options
            elif function_result:
                # Single value result
                option = DropdownOption(
                    value=str(function_result),
                    label=str(function_result),
                    metadata={}
                )
                return [option]
            else:
                return []
                
        except Exception as e:
            self.logger.error(f"Error getting system dependent options: {str(e)}")
            return []
