-- Purchase Furniture Entities Insert Script
-- Entities: E13, E14, E15, E16

SET search_path TO workflow_runtime;

-- =====================================================
-- ENTITIES (E13, E14, E15, E16)
-- =====================================================
INSERT INTO entities (
    entity_id, name, display_name, version, status, type, description, 
    created_at, updated_at, created_by, updated_by, table_name, tenant_id, 
    tenant_name, business_domain, category, archival_strategy, icon, 
    colour_theme, natural_language
) VALUES 
('E13', 'FurnitureOrder', 'Furniture Order', 1, 'Active', 'transactional', 'Represents a furniture purchase order', NOW(), NOW(), 'system', 'system', 'e13_furnitureorder', 'T2', 'Acme Corporation', 'Sales', 'Order Management', 'archive_after_completion', 'shopping-cart', 'blue', 'Primary entity for furniture purchase orders'),
('E14', 'FurnitureProduct', 'Furniture Product', 1, 'Active', 'master', 'Represents furniture products available for purchase', NOW(), NOW(), 'system', 'system', 'e14_furnitureproduct', 'T2', 'Acme Corporation', 'Inventory', 'Product Catalog', 'retain_permanently', 'package', 'green', 'Master data for furniture products'),
('E15', 'FurnitureType', 'Furniture Type', 1, 'Active', 'master', 'Represents different types of furniture categories', NOW(), NOW(), 'system', 'system', 'e15_furnituretype', 'T2', 'Acme Corporation', 'Inventory', 'Product Classification', 'retain_permanently', 'grid', 'purple', 'Master data for furniture categories'),
('E16', 'PaymentDetails', 'Payment Details', 1, 'Active', 'transactional', 'Stores payment information for furniture orders', NOW(), NOW(), 'system', 'system', 'e16_paymentdetails', 'T2', 'Acme Corporation', 'Finance', 'Payment Processing', 'archive_after_audit', 'credit-card', 'orange', 'Payment details for furniture orders');

COMMIT;
