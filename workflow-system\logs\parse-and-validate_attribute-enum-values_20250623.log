{"timestamp": "2025-06-23T05:09:28.163041", "endpoint": "parse-and-validate/attribute-enum-values", "input": {"natural_language": "Tenant: Acme Corp\n\nEntity.Attribute | Enum Name | Value | Display | Description | Sort Order | Active\n\nLeaveApplication.status | Leave Application Status | Pending | Pending | Leave request is pending approval | 1 | true\n\nLeaveApplication.status | Leave Application Status | Approved | Approved | Leave request has been approved | 2 | true\n\nLeaveApplication.status | Leave Application Status | Rejected | Rejected | Leave request has been rejected | 3 | true\n\nLeaveApplication.status | Leave Application Status | Cancelled | Cancelled | Leave request has been cancelled | 4 | true\n\nLeaveApplication.requiresDocumentation | Documentation Requirement | true | Yes | Documentation is required | 1 | true\n\nLeaveApplication.requiresDocumentation | Documentation Requirement | false | No | Documentation is not required | 2 | true\n\nEmployee.employmentStatus | Employment Status | Active | Active | Employee is currently active | 1 | true\n\nEmployee.employmentStatus | Employment Status | Inactive | Inactive | Employee is temporarily inactive | 2 | true\n\nEmployee.employmentStatus | Employment Status | Terminated | Terminated | Employee has been terminated | 3 | true", "tenant_id": "T_ACME_CORP"}, "output": {"success": true, "enum_value_results": [{"parsed_data": {"enum_value_id": "EV_LEAVEAPPLICATION_STATUS_PENDING", "attribute_id": "A_E_LeaveApplication_1750655367_status_1750655367", "value": "Pending", "display_name": "Pending", "description": "Leave request is pending approval", "sort_order": 1, "is_active": true, "natural_language": "Entity.Attribute: LeaveApplication.status\nEnum Name: Leave Application Status\nValue: Pending\nDisplay: Pending\nDescription: Leave request is pending approval", "version": 1, "status": "active", "created_at": "2025-06-23T05:09:27.776478", "updated_at": "2025-06-23T05:09:27.776478", "created_by": "Tarun", "updated_by": "Tarun", "enum_value_status": "new", "changes_detected": []}, "validation_result": {"structure_errors": [], "required_field_errors": [], "data_type_errors": [], "custom_errors": []}, "dependency_errors": ["Attribute with attribute_id A_E_LeaveApplication_1750655367_status_1750655367 does not exist"], "uniqueness_result": {"is_unique": true, "status": "unique", "message": "Enum value with enum_value_id EV_LEAVEAPPLICATION_STATUS_PENDING is unique"}, "is_valid": false}, {"parsed_data": {"enum_value_id": "EV_LEAVEAPPLICATION_STATUS_APPROVED", "attribute_id": "A_E_LeaveApplication_1750655367_status_1750655367", "value": "Approved", "display_name": "Approved", "description": "Leave request has been approved", "sort_order": 2, "is_active": true, "natural_language": "Entity.Attribute: LeaveApplication.status\nEnum Name: Leave Application Status\nValue: Approved\nDisplay: Approved\nDescription: Leave request has been approved", "version": 1, "status": "active", "created_at": "2025-06-23T05:09:27.802203", "updated_at": "2025-06-23T05:09:27.802203", "created_by": "Tarun", "updated_by": "Tarun", "enum_value_status": "new", "changes_detected": []}, "validation_result": {"structure_errors": [], "required_field_errors": [], "data_type_errors": [], "custom_errors": []}, "dependency_errors": ["Attribute with attribute_id A_E_LeaveApplication_1750655367_status_1750655367 does not exist"], "uniqueness_result": {"is_unique": true, "status": "unique", "message": "Enum value with enum_value_id EV_LEAVEAPPLICATION_STATUS_APPROVED is unique"}, "is_valid": false}, {"parsed_data": {"enum_value_id": "EV_LEAVEAPPLICATION_STATUS_REJECTED", "attribute_id": "A_E_LeaveApplication_1750655367_status_1750655367", "value": "Rejected", "display_name": "Rejected", "description": "Leave request has been rejected", "sort_order": 3, "is_active": true, "natural_language": "Entity.Attribute: LeaveApplication.status\nEnum Name: Leave Application Status\nValue: Rejected\nDisplay: Rejected\nDescription: Leave request has been rejected", "version": 1, "status": "active", "created_at": "2025-06-23T05:09:27.826939", "updated_at": "2025-06-23T05:09:27.826939", "created_by": "Tarun", "updated_by": "Tarun", "enum_value_status": "new", "changes_detected": []}, "validation_result": {"structure_errors": [], "required_field_errors": [], "data_type_errors": [], "custom_errors": []}, "dependency_errors": ["Attribute with attribute_id A_E_LeaveApplication_1750655367_status_1750655367 does not exist"], "uniqueness_result": {"is_unique": true, "status": "unique", "message": "Enum value with enum_value_id EV_LEAVEAPPLICATION_STATUS_REJECTED is unique"}, "is_valid": false}, {"parsed_data": {"enum_value_id": "EV_LEAVEAPPLICATION_STATUS_CANCELLED", "attribute_id": "A_E_LeaveApplication_1750655367_status_1750655367", "value": "Cancelled", "display_name": "Cancelled", "description": "Leave request has been cancelled", "sort_order": 4, "is_active": true, "natural_language": "Entity.Attribute: LeaveApplication.status\nEnum Name: Leave Application Status\nValue: Cancelled\nDisplay: Cancelled\nDescription: Leave request has been cancelled", "version": 1, "status": "active", "created_at": "2025-06-23T05:09:27.850088", "updated_at": "2025-06-23T05:09:27.850088", "created_by": "Tarun", "updated_by": "Tarun", "enum_value_status": "new", "changes_detected": []}, "validation_result": {"structure_errors": [], "required_field_errors": [], "data_type_errors": [], "custom_errors": []}, "dependency_errors": ["Attribute with attribute_id A_E_LeaveApplication_1750655367_status_1750655367 does not exist"], "uniqueness_result": {"is_unique": true, "status": "unique", "message": "Enum value with enum_value_id EV_LEAVEAPPLICATION_STATUS_CANCELLED is unique"}, "is_valid": false}, {"parsed_data": {"enum_value_id": "EV_LEAVEAPPLICATION_REQUIRESDOCUMENTATION_TRUE", "attribute_id": "A_E_LeaveApplication_1750655367_requiresDocumentation_1750655367", "value": "true", "display_name": "Yes", "description": "Documentation is required", "sort_order": 1, "is_active": true, "natural_language": "Entity.Attribute: LeaveApplication.requiresDocumentation\nEnum Name: Documentation Requirement\nValue: true\nDisplay: Yes\nDescription: Documentation is required", "version": 1, "status": "active", "created_at": "2025-06-23T05:09:27.879601", "updated_at": "2025-06-23T05:09:27.879601", "created_by": "Tarun", "updated_by": "Tarun", "enum_value_status": "new", "changes_detected": []}, "validation_result": {"structure_errors": [], "required_field_errors": [], "data_type_errors": ["attribute_id must be 50 characters or less"], "custom_errors": []}, "dependency_errors": ["Attribute with attribute_id A_E_LeaveApplication_1750655367_requiresDocumentation_1750655367 does not exist"], "uniqueness_result": {"is_unique": true, "status": "unique", "message": "Enum value with enum_value_id EV_LEAVEAPPLICATION_REQUIRESDOCUMENTATION_TRUE is unique"}, "is_valid": false}, {"parsed_data": {"enum_value_id": "EV_LEAVEAPPLICATION_REQUIRESDOCUMENTATION_FALSE", "attribute_id": "A_E_LeaveApplication_1750655367_requiresDocumentation_1750655367", "value": "false", "display_name": "No", "description": "Documentation is not required", "sort_order": 2, "is_active": true, "natural_language": "Entity.Attribute: LeaveApplication.requiresDocumentation\nEnum Name: Documentation Requirement\nValue: false\nDisplay: No\nDescription: Documentation is not required", "version": 1, "status": "active", "created_at": "2025-06-23T05:09:27.903184", "updated_at": "2025-06-23T05:09:27.903184", "created_by": "Tarun", "updated_by": "Tarun", "enum_value_status": "new", "changes_detected": []}, "validation_result": {"structure_errors": [], "required_field_errors": [], "data_type_errors": ["attribute_id must be 50 characters or less"], "custom_errors": []}, "dependency_errors": ["Attribute with attribute_id A_E_LeaveApplication_1750655367_requiresDocumentation_1750655367 does not exist"], "uniqueness_result": {"is_unique": true, "status": "unique", "message": "Enum value with enum_value_id EV_LEAVEAPPLICATION_REQUIRESDOCUMENTATION_FALSE is unique"}, "is_valid": false}, {"parsed_data": {"enum_value_id": "EV_EMPLOYEE_EMPLOYMENTSTATUS_ACTIVE", "attribute_id": "A_E_Employee_1750655367_employmentStatus_1750655367", "value": "Active", "display_name": "Active", "description": "Employee is currently active", "sort_order": 1, "is_active": true, "natural_language": "Entity.Attribute: Employee.employmentStatus\nEnum Name: Employment Status\nValue: Active\nDisplay: Active\nDescription: Employee is currently active", "version": 1, "status": "active", "created_at": "2025-06-23T05:09:27.928175", "updated_at": "2025-06-23T05:09:27.928175", "created_by": "Tarun", "updated_by": "Tarun", "enum_value_status": "new", "changes_detected": []}, "validation_result": {"structure_errors": [], "required_field_errors": [], "data_type_errors": ["attribute_id must be 50 characters or less"], "custom_errors": []}, "dependency_errors": ["Attribute with attribute_id A_E_Employee_1750655367_employmentStatus_1750655367 does not exist"], "uniqueness_result": {"is_unique": true, "status": "unique", "message": "Enum value with enum_value_id EV_EMPLOYEE_EMPLOYMENTSTATUS_ACTIVE is unique"}, "is_valid": false}, {"parsed_data": {"enum_value_id": "EV_EMPLOYEE_EMPLOYMENTSTATUS_INACTIVE", "attribute_id": "A_E_Employee_1750655367_employmentStatus_1750655367", "value": "Inactive", "display_name": "Inactive", "description": "Employee is temporarily inactive", "sort_order": 2, "is_active": true, "natural_language": "Entity.Attribute: Employee.employmentStatus\nEnum Name: Employment Status\nValue: Inactive\nDisplay: Inactive\nDescription: Employee is temporarily inactive", "version": 1, "status": "active", "created_at": "2025-06-23T05:09:27.952580", "updated_at": "2025-06-23T05:09:27.952580", "created_by": "Tarun", "updated_by": "Tarun", "enum_value_status": "new", "changes_detected": []}, "validation_result": {"structure_errors": [], "required_field_errors": [], "data_type_errors": ["attribute_id must be 50 characters or less"], "custom_errors": []}, "dependency_errors": ["Attribute with attribute_id A_E_Employee_1750655367_employmentStatus_1750655367 does not exist"], "uniqueness_result": {"is_unique": true, "status": "unique", "message": "Enum value with enum_value_id EV_EMPLOYEE_EMPLOYMENTSTATUS_INACTIVE is unique"}, "is_valid": false}, {"parsed_data": {"enum_value_id": "EV_EMPLOYEE_EMPLOYMENTSTATUS_TERMINATED", "attribute_id": "A_E_Employee_1750655367_employmentStatus_1750655367", "value": "Terminated", "display_name": "Terminated", "description": "Employee has been terminated", "sort_order": 3, "is_active": true, "natural_language": "Entity.Attribute: Employee.employmentStatus\nEnum Name: Employment Status\nValue: Terminated\nDisplay: Terminated\nDescription: Employee has been terminated", "version": 1, "status": "active", "created_at": "2025-06-23T05:09:27.977030", "updated_at": "2025-06-23T05:09:27.977030", "created_by": "Tarun", "updated_by": "Tarun", "enum_value_status": "new", "changes_detected": []}, "validation_result": {"structure_errors": [], "required_field_errors": [], "data_type_errors": ["attribute_id must be 50 characters or less"], "custom_errors": []}, "dependency_errors": ["Attribute with attribute_id A_E_Employee_1750655367_employmentStatus_1750655367 does not exist"], "uniqueness_result": {"is_unique": true, "status": "unique", "message": "Enum value with enum_value_id EV_EMPLOYEE_EMPLOYMENTSTATUS_TERMINATED is unique"}, "is_valid": false}], "operation": "parse_and_validate", "total_enum_values": 9}, "status": "success"}
{"timestamp": "2025-06-23T08:25:36.939437", "endpoint": "parse-and-validate/attribute-enum-values", "input": {"natural_language": "Tenant: Acme Corp\n\nEntity.Attribute | Enum Name | Value | Display | Description | Sort Order | Active\n\nLeaveApplication.status | Leave Application Status | Pending | Pending | Leave request is pending approval | 1 | true\n\nLeaveApplication.status | Leave Application Status | Approved | Approved | Leave request has been approved | 2 | true\n\nLeaveApplication.status | Leave Application Status | Rejected | Rejected | Leave request has been rejected | 3 | true\n\nLeaveApplication.status | Leave Application Status | Cancelled | Cancelled | Leave request has been cancelled | 4 | true\n\nLeaveApplication.requiresDocumentation | Documentation Requirement | true | Yes | Documentation is required | 1 | true\n\nLeaveApplication.requiresDocumentation | Documentation Requirement | false | No | Documentation is not required | 2 | true\n\nEmployee.employmentStatus | Employment Status | Active | Active | Employee is currently active | 1 | true\n\nEmployee.employmentStatus | Employment Status | Inactive | Inactive | Employee is temporarily inactive | 2 | true\n\nEmployee.employmentStatus | Employment Status | Terminated | Terminated | Employee has been terminated | 3 | true", "tenant_id": "T_ACME_CORP"}, "output": {"success": true, "enum_value_results": [{"parsed_data": {"enum_value_id": "EV_LEAVEAPPLICATION_STATUS_PENDING", "attribute_id": "A_E_LeaveApplication_1750667136_status_1750667136", "value": "Pending", "display_name": "Pending", "description": "Leave request is pending approval", "sort_order": 1, "is_active": true, "natural_language": "Entity.Attribute: LeaveApplication.status\nEnum Name: Leave Application Status\nValue: Pending\nDisplay: Pending\nDescription: Leave request is pending approval", "version": 1, "status": "active", "created_at": "2025-06-23T08:25:36.563690", "updated_at": "2025-06-23T08:25:36.563690", "created_by": "Tarun", "updated_by": "Tarun", "enum_value_status": "new", "changes_detected": []}, "validation_result": {"structure_errors": [], "required_field_errors": [], "data_type_errors": [], "custom_errors": []}, "dependency_errors": ["Attribute with attribute_id A_E_LeaveApplication_1750667136_status_1750667136 does not exist"], "uniqueness_result": {"is_unique": true, "status": "unique", "message": "Enum value with enum_value_id EV_LEAVEAPPLICATION_STATUS_PENDING is unique"}, "is_valid": false}, {"parsed_data": {"enum_value_id": "EV_LEAVEAPPLICATION_STATUS_APPROVED", "attribute_id": "A_E_LeaveApplication_1750667136_status_1750667136", "value": "Approved", "display_name": "Approved", "description": "Leave request has been approved", "sort_order": 2, "is_active": true, "natural_language": "Entity.Attribute: LeaveApplication.status\nEnum Name: Leave Application Status\nValue: Approved\nDisplay: Approved\nDescription: Leave request has been approved", "version": 1, "status": "active", "created_at": "2025-06-23T08:25:36.590729", "updated_at": "2025-06-23T08:25:36.590729", "created_by": "Tarun", "updated_by": "Tarun", "enum_value_status": "new", "changes_detected": []}, "validation_result": {"structure_errors": [], "required_field_errors": [], "data_type_errors": [], "custom_errors": []}, "dependency_errors": ["Attribute with attribute_id A_E_LeaveApplication_1750667136_status_1750667136 does not exist"], "uniqueness_result": {"is_unique": true, "status": "unique", "message": "Enum value with enum_value_id EV_LEAVEAPPLICATION_STATUS_APPROVED is unique"}, "is_valid": false}, {"parsed_data": {"enum_value_id": "EV_LEAVEAPPLICATION_STATUS_REJECTED", "attribute_id": "A_E_LeaveApplication_1750667136_status_1750667136", "value": "Rejected", "display_name": "Rejected", "description": "Leave request has been rejected", "sort_order": 3, "is_active": true, "natural_language": "Entity.Attribute: LeaveApplication.status\nEnum Name: Leave Application Status\nValue: Rejected\nDisplay: Rejected\nDescription: Leave request has been rejected", "version": 1, "status": "active", "created_at": "2025-06-23T08:25:36.616582", "updated_at": "2025-06-23T08:25:36.616582", "created_by": "Tarun", "updated_by": "Tarun", "enum_value_status": "new", "changes_detected": []}, "validation_result": {"structure_errors": [], "required_field_errors": [], "data_type_errors": [], "custom_errors": []}, "dependency_errors": ["Attribute with attribute_id A_E_LeaveApplication_1750667136_status_1750667136 does not exist"], "uniqueness_result": {"is_unique": true, "status": "unique", "message": "Enum value with enum_value_id EV_LEAVEAPPLICATION_STATUS_REJECTED is unique"}, "is_valid": false}, {"parsed_data": {"enum_value_id": "EV_LEAVEAPPLICATION_STATUS_CANCELLED", "attribute_id": "A_E_LeaveApplication_1750667136_status_1750667136", "value": "Cancelled", "display_name": "Cancelled", "description": "Leave request has been cancelled", "sort_order": 4, "is_active": true, "natural_language": "Entity.Attribute: LeaveApplication.status\nEnum Name: Leave Application Status\nValue: Cancelled\nDisplay: Cancelled\nDescription: Leave request has been cancelled", "version": 1, "status": "active", "created_at": "2025-06-23T08:25:36.642590", "updated_at": "2025-06-23T08:25:36.642590", "created_by": "Tarun", "updated_by": "Tarun", "enum_value_status": "new", "changes_detected": []}, "validation_result": {"structure_errors": [], "required_field_errors": [], "data_type_errors": [], "custom_errors": []}, "dependency_errors": ["Attribute with attribute_id A_E_LeaveApplication_1750667136_status_1750667136 does not exist"], "uniqueness_result": {"is_unique": true, "status": "unique", "message": "Enum value with enum_value_id EV_LEAVEAPPLICATION_STATUS_CANCELLED is unique"}, "is_valid": false}, {"parsed_data": {"enum_value_id": "EV_LEAVEAPPLICATION_REQUIRESDOCUMENTATION_TRUE", "attribute_id": "A_E_LeaveApplication_1750667136_requiresDocumentation_1750667136", "value": "true", "display_name": "Yes", "description": "Documentation is required", "sort_order": 1, "is_active": true, "natural_language": "Entity.Attribute: LeaveApplication.requiresDocumentation\nEnum Name: Documentation Requirement\nValue: true\nDisplay: Yes\nDescription: Documentation is required", "version": 1, "status": "active", "created_at": "2025-06-23T08:25:36.667268", "updated_at": "2025-06-23T08:25:36.667268", "created_by": "Tarun", "updated_by": "Tarun", "enum_value_status": "new", "changes_detected": []}, "validation_result": {"structure_errors": [], "required_field_errors": [], "data_type_errors": ["attribute_id must be 50 characters or less"], "custom_errors": []}, "dependency_errors": ["Attribute with attribute_id A_E_LeaveApplication_1750667136_requiresDocumentation_1750667136 does not exist"], "uniqueness_result": {"is_unique": true, "status": "unique", "message": "Enum value with enum_value_id EV_LEAVEAPPLICATION_REQUIRESDOCUMENTATION_TRUE is unique"}, "is_valid": false}, {"parsed_data": {"enum_value_id": "EV_LEAVEAPPLICATION_REQUIRESDOCUMENTATION_FALSE", "attribute_id": "A_E_LeaveApplication_1750667136_requiresDocumentation_1750667136", "value": "false", "display_name": "No", "description": "Documentation is not required", "sort_order": 2, "is_active": true, "natural_language": "Entity.Attribute: LeaveApplication.requiresDocumentation\nEnum Name: Documentation Requirement\nValue: false\nDisplay: No\nDescription: Documentation is not required", "version": 1, "status": "active", "created_at": "2025-06-23T08:25:36.693579", "updated_at": "2025-06-23T08:25:36.693579", "created_by": "Tarun", "updated_by": "Tarun", "enum_value_status": "new", "changes_detected": []}, "validation_result": {"structure_errors": [], "required_field_errors": [], "data_type_errors": ["attribute_id must be 50 characters or less"], "custom_errors": []}, "dependency_errors": ["Attribute with attribute_id A_E_LeaveApplication_1750667136_requiresDocumentation_1750667136 does not exist"], "uniqueness_result": {"is_unique": true, "status": "unique", "message": "Enum value with enum_value_id EV_LEAVEAPPLICATION_REQUIRESDOCUMENTATION_FALSE is unique"}, "is_valid": false}, {"parsed_data": {"enum_value_id": "EV_EMPLOYEE_EMPLOYMENTSTATUS_ACTIVE", "attribute_id": "A_E_Employee_1750667136_employmentStatus_1750667136", "value": "Active", "display_name": "Active", "description": "Employee is currently active", "sort_order": 1, "is_active": true, "natural_language": "Entity.Attribute: Employee.employmentStatus\nEnum Name: Employment Status\nValue: Active\nDisplay: Active\nDescription: Employee is currently active", "version": 1, "status": "active", "created_at": "2025-06-23T08:25:36.722167", "updated_at": "2025-06-23T08:25:36.722167", "created_by": "Tarun", "updated_by": "Tarun", "enum_value_status": "existing", "changes_detected": []}, "validation_result": {"structure_errors": [], "required_field_errors": [], "data_type_errors": ["attribute_id must be 50 characters or less"], "custom_errors": []}, "dependency_errors": ["Attribute with attribute_id A_E_Employee_1750667136_employmentStatus_1750667136 does not exist"], "uniqueness_result": {"is_unique": false, "status": "exists_in_mongo", "message": "Enum value with enum_value_id EV_EMPLOYEE_EMPLOYMENTSTATUS_ACTIVE already exists in MongoDB drafts", "existing_document": {"_id": "6858f666ca3a31e3886b140b", "enum_value_id": "EV_EMPLOYEE_EMPLOYMENTSTATUS_ACTIVE", "attribute_id": "E45.At4", "value": "Active", "display_name": "Active", "description": "Employee is currently active", "sort_order": 1, "is_active": true, "version": 1, "status": "draft", "created_at": "2025-06-23T06:38:30.817Z", "updated_at": "2025-06-23T06:38:30.817Z", "created_by": "Tarun", "updated_by": "Tarun", "enum_value_status": "new", "changes_detected": []}}, "is_valid": false}, {"parsed_data": {"enum_value_id": "EV_EMPLOYEE_EMPLOYMENTSTATUS_INACTIVE", "attribute_id": "A_E_Employee_1750667136_employmentStatus_1750667136", "value": "Inactive", "display_name": "Inactive", "description": "Employee is temporarily inactive", "sort_order": 2, "is_active": true, "natural_language": "Entity.Attribute: Employee.employmentStatus\nEnum Name: Employment Status\nValue: Inactive\nDisplay: Inactive\nDescription: Employee is temporarily inactive", "version": 1, "status": "active", "created_at": "2025-06-23T08:25:36.746574", "updated_at": "2025-06-23T08:25:36.746574", "created_by": "Tarun", "updated_by": "Tarun", "enum_value_status": "new", "changes_detected": []}, "validation_result": {"structure_errors": [], "required_field_errors": [], "data_type_errors": ["attribute_id must be 50 characters or less"], "custom_errors": []}, "dependency_errors": ["Attribute with attribute_id A_E_Employee_1750667136_employmentStatus_1750667136 does not exist"], "uniqueness_result": {"is_unique": true, "status": "unique", "message": "Enum value with enum_value_id EV_EMPLOYEE_EMPLOYMENTSTATUS_INACTIVE is unique"}, "is_valid": false}, {"parsed_data": {"enum_value_id": "EV_EMPLOYEE_EMPLOYMENTSTATUS_TERMINATED", "attribute_id": "A_E_Employee_1750667136_employmentStatus_1750667136", "value": "Terminated", "display_name": "Terminated", "description": "Employee has been terminated", "sort_order": 3, "is_active": true, "natural_language": "Entity.Attribute: Employee.employmentStatus\nEnum Name: Employment Status\nValue: Terminated\nDisplay: Terminated\nDescription: Employee has been terminated", "version": 1, "status": "active", "created_at": "2025-06-23T08:25:36.769158", "updated_at": "2025-06-23T08:25:36.769158", "created_by": "Tarun", "updated_by": "Tarun", "enum_value_status": "new", "changes_detected": []}, "validation_result": {"structure_errors": [], "required_field_errors": [], "data_type_errors": ["attribute_id must be 50 characters or less"], "custom_errors": []}, "dependency_errors": ["Attribute with attribute_id A_E_Employee_1750667136_employmentStatus_1750667136 does not exist"], "uniqueness_result": {"is_unique": true, "status": "unique", "message": "Enum value with enum_value_id EV_EMPLOYEE_EMPLOYMENTSTATUS_TERMINATED is unique"}, "is_valid": false}], "operation": "parse_and_validate", "total_enum_values": 9}, "status": "success"}
{"timestamp": "2025-06-23T09:34:10.526824", "endpoint": "parse-and-validate/attribute-enum-values", "input": {"natural_language": "Tenant: Acme Corp\n\nEntity.Attribute | Enum Name | Value | Display | Description | Sort Order | Active\n\nLeaveApplication.status | Leave Application Status | Pending | Pending | Leave request is pending approval | 1 | true\nLeaveApplication.status | Leave Application Status | Approved | Approved | Leave request has been approved | 2 | true\nLeaveApplication.status | Leave Application Status | Rejected | Rejected | Leave request has been rejected | 3 | true", "tenant_id": "T2"}, "output": {"success": true, "enum_value_results": [{"parsed_data": {"enum_value_id": "EV_LEAVEAPPLICATION_STATUS_PENDING", "attribute_id": "E7.At9", "value": "Pending", "display_name": "Pending", "description": "Leave request is pending approval", "sort_order": 1, "is_active": true, "natural_language": "Entity.Attribute: LeaveApplication.status\nEnum Name: Leave Application Status\nValue: Pending\nDisplay: Pending\nDescription: Leave request is pending approval", "version": 1, "status": "active", "created_at": "2025-06-23T09:34:10.412439", "updated_at": "2025-06-23T09:34:10.412439", "created_by": "Tarun", "updated_by": "Tarun", "enum_value_status": "new", "changes_detected": []}, "validation_result": {"structure_errors": [], "required_field_errors": [], "data_type_errors": [], "custom_errors": []}, "dependency_errors": [], "uniqueness_result": {"is_unique": false, "status": "exists_in_postgres", "message": "Enum value with enum_value_id EV_LEAVEAPPLICATION_STATUS_PENDING or value Pending already exists in PostgreSQL"}, "is_valid": true}, {"parsed_data": {"enum_value_id": "EV_LEAVEAPPLICATION_STATUS_APPROVED", "attribute_id": "E7.At9", "value": "Approved", "display_name": "Approved", "description": "Leave request has been approved", "sort_order": 2, "is_active": true, "natural_language": "Entity.Attribute: LeaveApplication.status\nEnum Name: Leave Application Status\nValue: Approved\nDisplay: Approved\nDescription: Leave request has been approved", "version": 1, "status": "active", "created_at": "2025-06-23T09:34:10.437658", "updated_at": "2025-06-23T09:34:10.437658", "created_by": "Tarun", "updated_by": "Tarun", "enum_value_status": "new", "changes_detected": []}, "validation_result": {"structure_errors": [], "required_field_errors": [], "data_type_errors": [], "custom_errors": []}, "dependency_errors": [], "uniqueness_result": {"is_unique": false, "status": "exists_in_postgres", "message": "Enum value with enum_value_id EV_LEAVEAPPLICATION_STATUS_APPROVED or value Approved already exists in PostgreSQL"}, "is_valid": true}, {"parsed_data": {"enum_value_id": "EV_LEAVEAPPLICATION_STATUS_REJECTED", "attribute_id": "E7.At9", "value": "Rejected", "display_name": "Rejected", "description": "Leave request has been rejected", "sort_order": 3, "is_active": true, "natural_language": "Entity.Attribute: LeaveApplication.status\nEnum Name: Leave Application Status\nValue: Rejected\nDisplay: Rejected\nDescription: Leave request has been rejected", "version": 1, "status": "active", "created_at": "2025-06-23T09:34:10.462531", "updated_at": "2025-06-23T09:34:10.462531", "created_by": "Tarun", "updated_by": "Tarun", "enum_value_status": "new", "changes_detected": []}, "validation_result": {"structure_errors": [], "required_field_errors": [], "data_type_errors": [], "custom_errors": []}, "dependency_errors": [], "uniqueness_result": {"is_unique": false, "status": "exists_in_postgres", "message": "Enum value with enum_value_id EV_LEAVEAPPLICATION_STATUS_REJECTED or value Rejected already exists in PostgreSQL"}, "is_valid": true}], "operation": "parse_and_validate", "total_enum_values": 3}, "status": "success"}
{"timestamp": "2025-06-23T14:07:58.842892", "endpoint": "parse-and-validate/attribute-enum-values", "input": {"natural_language": "Status field should have enum values: active, inactive, pending", "tenant_id": "tenant_123"}, "output": {"success": true, "enum_value_results": [], "operation": "parse_and_validate", "total_enum_values": 0}, "status": "success"}
