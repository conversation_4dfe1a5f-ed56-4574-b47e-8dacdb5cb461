2025-05-11 07:22:03,397 - run_deploy_and_test - INFO - Step 1: Running deploy_to_temp_schema.py
2025-05-11 07:22:03,397 - run_deploy_and_test - INFO - Running command: python3 deploy_to_temp_schema.py --schema-name workflow_temp
2025-05-11 07:22:06,708 - run_deploy_and_test - INFO - Command output:

2025-05-11 07:22:06,708 - run_deploy_and_test - WARNING - Command stderr:
2025-05-11 07:22:03,492 - prescriptive_parser - INFO - Initializing PrescriptiveParser
2025-05-11 07:22:03,492 - component_deployer - INFO - ComponentDeployer initialized with use_temp_schema=True
2025-05-11 07:22:03,498 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:03,536 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:03,542 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:03,551 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:03,560 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:03,569 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:03,577 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:03,586 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:03,595 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:03,604 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:03,605 - component_deployer - INFO - Analyzing workflow_runtime schema to create necessary tables in temporary schema
2025-05-11 07:22:03,609 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:03,617 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:03,623 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:03,629 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:03,638 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:03,648 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:03,654 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:03,662 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:03,673 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:03,680 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:03,688 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:03,698 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:03,703 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:03,712 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:03,721 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:03,727 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:03,736 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:03,747 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:03,754 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:03,762 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:03,771 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:03,778 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:03,787 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:03,797 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:03,805 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:03,813 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:03,824 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:03,831 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:03,840 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:03,848 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:03,856 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:03,866 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:03,876 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:03,884 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:03,894 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:03,902 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:03,910 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:03,919 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:03,928 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:03,937 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:03,947 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:03,956 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:03,965 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:03,974 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:03,984 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:03,993 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:04,003 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:04,011 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:04,019 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:04,028 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:04,037 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:04,043 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:04,053 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:04,063 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:04,069 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:04,079 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:04,090 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:04,097 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:04,107 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:04,118 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:04,124 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:04,134 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:04,144 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:04,152 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:04,162 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:04,173 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:04,182 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:04,194 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:04,205 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:04,214 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:04,225 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:04,236 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:04,245 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:04,256 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:04,266 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:04,274 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:04,285 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:04,295 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:04,304 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:04,315 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:04,325 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:04,333 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:04,345 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:04,357 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:04,367 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:04,379 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:04,389 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:04,398 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:04,408 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:04,419 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:04,427 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:04,438 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:04,449 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:04,459 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:04,470 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:04,480 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:04,489 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:04,500 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:04,510 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:04,518 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:04,528 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:04,538 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:04,546 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:04,557 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:04,565 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:04,572 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:04,581 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:04,589 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:04,598 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:04,607 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:04,617 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:04,625 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:04,637 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:04,647 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:04,655 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:04,667 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:04,677 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:04,685 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:04,696 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:04,705 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:04,713 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:04,724 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:04,734 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:04,742 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:04,752 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:04,761 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:04,768 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:04,778 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:04,788 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:04,796 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:04,805 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:04,813 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:04,819 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:04,831 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:04,839 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:04,847 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:04,856 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:04,866 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:04,874 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:04,884 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:04,892 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:04,899 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:04,908 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:04,917 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:04,924 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:04,934 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:04,943 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:04,950 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:04,960 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:04,970 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:04,977 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:04,987 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:04,996 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:05,004 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:05,014 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:05,024 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:05,033 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:05,042 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:05,053 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:05,062 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:05,073 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:05,083 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:05,091 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:05,101 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:05,111 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:05,120 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:05,131 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:05,141 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:05,150 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:05,162 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:05,172 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:05,182 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:05,191 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:05,201 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:05,208 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:05,218 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:05,229 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:05,236 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:05,246 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:05,257 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:05,266 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:05,277 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:05,287 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:05,295 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:05,306 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:05,316 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:05,324 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:05,335 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:05,345 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:05,353 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:05,364 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:05,374 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:05,382 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:05,392 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:05,404 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:05,412 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:05,422 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:05,433 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:05,441 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:05,452 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:05,462 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:05,469 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:05,478 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:05,487 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:05,495 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:05,505 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:05,513 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:05,522 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:05,533 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:05,542 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:05,551 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:05,626 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:05,634 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:05,643 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:05,652 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:05,660 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:05,668 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:05,677 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:05,684 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:05,693 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:05,701 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:05,708 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:05,716 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:05,724 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:05,732 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:05,740 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:05,747 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:05,748 - db_utils - ERROR - Database error: constraint "entity_attributes_entity_id_fkey" for relation "entity_attributes" already exists
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.DuplicateObject: constraint "entity_attributes_entity_id_fkey" for relation "entity_attributes" already exists

2025-05-11 07:22:05,753 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:05,761 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:05,770 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:05,771 - db_utils - ERROR - Database error: column "input_stack_id" referenced in foreign key constraint does not exist
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.UndefinedColumn: column "input_stack_id" referenced in foreign key constraint does not exist

2025-05-11 07:22:05,777 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:05,778 - db_utils - ERROR - Database error: constraint "lo_input_items_lo_id_fkey" for relation "lo_input_items" already exists
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.DuplicateObject: constraint "lo_input_items_lo_id_fkey" for relation "lo_input_items" already exists

2025-05-11 07:22:05,783 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:05,790 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:05,791 - db_utils - ERROR - Database error: there is no unique constraint matching given keys for referenced table "roles"
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.InvalidForeignKey: there is no unique constraint matching given keys for referenced table "roles"

2025-05-11 07:22:05,795 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:05,801 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:05,808 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:05,809 - db_utils - ERROR - Database error: constraint "lo_output_items_lo_id_fkey" for relation "lo_output_items" already exists
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.DuplicateObject: constraint "lo_output_items_lo_id_fkey" for relation "lo_output_items" already exists

2025-05-11 07:22:05,813 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:05,814 - db_utils - ERROR - Database error: column "output_stack_id" referenced in foreign key constraint does not exist
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.UndefinedColumn: column "output_stack_id" referenced in foreign key constraint does not exist

2025-05-11 07:22:05,818 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:05,824 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:05,825 - db_utils - ERROR - Database error: column "id" referenced in foreign key constraint does not exist
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.UndefinedColumn: column "id" referenced in foreign key constraint does not exist

2025-05-11 07:22:05,828 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:05,829 - db_utils - ERROR - Database error: column "input_stack_id" referenced in foreign key constraint does not exist
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.UndefinedColumn: column "input_stack_id" referenced in foreign key constraint does not exist

2025-05-11 07:22:05,833 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:05,834 - db_utils - ERROR - Database error: column "id" referenced in foreign key constraint does not exist
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.UndefinedColumn: column "id" referenced in foreign key constraint does not exist

2025-05-11 07:22:05,837 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:05,838 - db_utils - ERROR - Database error: column "input_stack_id" referenced in foreign key constraint does not exist
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.UndefinedColumn: column "input_stack_id" referenced in foreign key constraint does not exist

2025-05-11 07:22:05,841 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:05,842 - db_utils - ERROR - Database error: column "id" referenced in foreign key constraint does not exist
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.UndefinedColumn: column "id" referenced in foreign key constraint does not exist

2025-05-11 07:22:05,845 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:05,846 - db_utils - ERROR - Database error: column "input_stack_id" referenced in foreign key constraint does not exist
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.UndefinedColumn: column "input_stack_id" referenced in foreign key constraint does not exist

2025-05-11 07:22:05,849 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:05,850 - db_utils - ERROR - Database error: column "id" referenced in foreign key constraint does not exist
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.UndefinedColumn: column "id" referenced in foreign key constraint does not exist

2025-05-11 07:22:05,856 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:05,856 - db_utils - ERROR - Database error: column "input_stack_id" referenced in foreign key constraint does not exist
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.UndefinedColumn: column "input_stack_id" referenced in foreign key constraint does not exist

2025-05-11 07:22:05,861 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:05,868 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:05,874 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:05,881 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:05,887 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:05,894 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:05,899 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:05,905 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:05,910 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:05,915 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:05,915 - db_utils - ERROR - Database error: column "inherits_from" referenced in foreign key constraint does not exist
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.UndefinedColumn: column "inherits_from" referenced in foreign key constraint does not exist

2025-05-11 07:22:05,921 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:05,922 - db_utils - ERROR - Database error: column "tenant_id" referenced in foreign key constraint does not exist
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.UndefinedColumn: column "tenant_id" referenced in foreign key constraint does not exist

2025-05-11 07:22:05,926 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:05,933 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:05,940 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:05,949 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:05,956 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:05,956 - db_utils - ERROR - Database error: there is no unique constraint matching given keys for referenced table "output_items"
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.InvalidForeignKey: there is no unique constraint matching given keys for referenced table "output_items"

2025-05-11 07:22:05,960 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:05,961 - db_utils - ERROR - Database error: there is no unique constraint matching given keys for referenced table "output_items"
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.InvalidForeignKey: there is no unique constraint matching given keys for referenced table "output_items"

2025-05-11 07:22:05,966 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:05,967 - db_utils - ERROR - Database error: there is no unique constraint matching given keys for referenced table "output_items"
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.InvalidForeignKey: there is no unique constraint matching given keys for referenced table "output_items"

2025-05-11 07:22:05,972 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:05,972 - db_utils - ERROR - Database error: there is no unique constraint matching given keys for referenced table "output_items"
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.InvalidForeignKey: there is no unique constraint matching given keys for referenced table "output_items"

2025-05-11 07:22:05,978 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:05,986 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:05,992 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:06,000 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:06,008 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:06,015 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:06,015 - db_utils - ERROR - Database error: column "id" referenced in foreign key constraint does not exist
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.UndefinedColumn: column "id" referenced in foreign key constraint does not exist

2025-05-11 07:22:06,020 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:06,020 - db_utils - ERROR - Database error: column "input_stack_id" referenced in foreign key constraint does not exist
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.UndefinedColumn: column "input_stack_id" referenced in foreign key constraint does not exist

2025-05-11 07:22:06,026 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:06,027 - db_utils - ERROR - Database error: column "id" referenced in foreign key constraint does not exist
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.UndefinedColumn: column "id" referenced in foreign key constraint does not exist

2025-05-11 07:22:06,033 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:06,034 - db_utils - ERROR - Database error: column "input_stack_id" referenced in foreign key constraint does not exist
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.UndefinedColumn: column "input_stack_id" referenced in foreign key constraint does not exist

2025-05-11 07:22:06,038 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:06,045 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:06,046 - db_utils - ERROR - Database error: constraint "local_objectives_go_id_fkey" for relation "local_objectives" already exists
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.DuplicateObject: constraint "local_objectives_go_id_fkey" for relation "local_objectives" already exists

2025-05-11 07:22:06,052 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:06,052 - db_utils - ERROR - Database error: column "tenant_id" referenced in foreign key constraint does not exist
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.UndefinedColumn: column "tenant_id" referenced in foreign key constraint does not exist

2025-05-11 07:22:06,056 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:06,064 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:06,072 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:06,080 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:06,088 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:06,095 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:06,103 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:06,111 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:06,118 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:06,124 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:06,131 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:06,138 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:06,145 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:06,146 - db_utils - ERROR - Database error: column "id" referenced in foreign key constraint does not exist
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.UndefinedColumn: column "id" referenced in foreign key constraint does not exist

2025-05-11 07:22:06,152 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:06,153 - db_utils - ERROR - Database error: column "output_stack_id" referenced in foreign key constraint does not exist
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.UndefinedColumn: column "output_stack_id" referenced in foreign key constraint does not exist

2025-05-11 07:22:06,158 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:06,158 - db_utils - ERROR - Database error: column "id" referenced in foreign key constraint does not exist
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.UndefinedColumn: column "id" referenced in foreign key constraint does not exist

2025-05-11 07:22:06,164 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:06,164 - db_utils - ERROR - Database error: column "output_stack_id" referenced in foreign key constraint does not exist
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.UndefinedColumn: column "output_stack_id" referenced in foreign key constraint does not exist

2025-05-11 07:22:06,169 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:06,170 - db_utils - ERROR - Database error: column "id" referenced in foreign key constraint does not exist
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.UndefinedColumn: column "id" referenced in foreign key constraint does not exist

2025-05-11 07:22:06,174 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:06,174 - db_utils - ERROR - Database error: column "input_stack_id" referenced in foreign key constraint does not exist
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.UndefinedColumn: column "input_stack_id" referenced in foreign key constraint does not exist

2025-05-11 07:22:06,180 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:06,181 - db_utils - ERROR - Database error: column "id" referenced in foreign key constraint does not exist
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.UndefinedColumn: column "id" referenced in foreign key constraint does not exist

2025-05-11 07:22:06,185 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:06,186 - db_utils - ERROR - Database error: column "input_stack_id" referenced in foreign key constraint does not exist
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.UndefinedColumn: column "input_stack_id" referenced in foreign key constraint does not exist

2025-05-11 07:22:06,189 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:06,196 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:06,202 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:06,207 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:06,214 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:06,215 - db_utils - ERROR - Database error: column "id" referenced in foreign key constraint does not exist
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.UndefinedColumn: column "id" referenced in foreign key constraint does not exist

2025-05-11 07:22:06,219 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:06,219 - db_utils - ERROR - Database error: column "input_stack_id" referenced in foreign key constraint does not exist
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.UndefinedColumn: column "input_stack_id" referenced in foreign key constraint does not exist

2025-05-11 07:22:06,223 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:06,224 - db_utils - ERROR - Database error: column "id" referenced in foreign key constraint does not exist
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.UndefinedColumn: column "id" referenced in foreign key constraint does not exist

2025-05-11 07:22:06,228 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:06,229 - db_utils - ERROR - Database error: column "input_stack_id" referenced in foreign key constraint does not exist
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.UndefinedColumn: column "input_stack_id" referenced in foreign key constraint does not exist

2025-05-11 07:22:06,233 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:06,235 - component_deployer - INFO - Successfully analyzed workflow_runtime schema and created necessary tables
2025-05-11 07:22:06,235 - deploy_to_temp_schema - INFO - Deploying sample components to schema workflow_temp
2025-05-11 07:22:06,235 - prescriptive_parser - INFO - Parsing entities prescriptive text
2025-05-11 07:22:06,235 - entity_parser - INFO - Starting to parse entity definitions
2025-05-11 07:22:06,235 - entity_parser - INFO - Parsing entity: Employee
2025-05-11 07:22:06,236 - entity_parser - INFO - Successfully parsed entity 'Employee'
2025-05-11 07:22:06,236 - entity_parser - INFO - Parsing entity: Employee
2025-05-11 07:22:06,236 - entity_parser - INFO - Successfully parsed entity 'Employee'
2025-05-11 07:22:06,236 - entity_parser - INFO - Parsing entity: Department
2025-05-11 07:22:06,236 - entity_parser - INFO - Successfully parsed entity 'Department'
2025-05-11 07:22:06,236 - entity_parser - INFO - Successfully parsed 2 entities
2025-05-11 07:22:06,236 - deploy_to_temp_schema - INFO - Deploying entities
2025-05-11 07:22:06,239 - component_deployer - INFO - Deploying component of type: entities
2025-05-11 07:22:06,243 - component_deployer - INFO - Using schema: workflow_temp
2025-05-11 07:22:06,243 - entity_deployer - INFO - Deploying entities to workflow_temp
2025-05-11 07:22:06,248 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:06,255 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:06,256 - db_utils - ERROR - Database error: column "status" of relation "entities" does not exist
LINE 4:                     status, type, attribute_prefix
                            ^
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 54, in execute_query
    cursor.execute(query, params)
psycopg2.errors.UndefinedColumn: column "status" of relation "entities" does not exist
LINE 4:                     status, type, attribute_prefix
                            ^

2025-05-11 07:22:06,256 - component_deployer - INFO - Component deployment failed
2025-05-11 07:22:06,256 - deploy_to_temp_schema - ERROR - Failed to deploy entities: ['Database error: column "status" of relation "entities" does not exist\nLINE 4:                     status, type, attribute_prefix\n                            ^\n']
2025-05-11 07:22:06,256 - deploy_to_temp_schema - ERROR - Failed to deploy entities: ['Database error: column "status" of relation "entities" does not exist\nLINE 4:                     status, type, attribute_prefix\n                            ^\n']
2025-05-11 07:22:06,256 - prescriptive_parser - INFO - Parsing go_definitions prescriptive text
2025-05-11 07:22:06,256 - go_parser - INFO - Starting to parse GO definitions
2025-05-11 07:22:06,257 - go_parser - INFO - Parsing GO: Leave Approval Process (ID: go001)
2025-05-11 07:22:06,259 - go_parser - INFO - Successfully parsed GO 'Leave Approval Process'
2025-05-11 07:22:06,259 - go_parser - INFO - Successfully parsed 1 global objectives
2025-05-11 07:22:06,259 - deploy_to_temp_schema - INFO - Deploying go_definitions
2025-05-11 07:22:06,263 - component_deployer - INFO - Deploying component of type: go_definitions
2025-05-11 07:22:06,267 - component_deployer - INFO - Using schema: workflow_temp
2025-05-11 07:22:06,272 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:06,273 - component_deployer - ERROR - Cannot deploy go_definitions because no entities exist. Entities must be deployed first.
2025-05-11 07:22:06,273 - deploy_to_temp_schema - ERROR - Failed to deploy go_definitions: ['Cannot deploy go_definitions because no entities exist. Entities must be deployed first.']
2025-05-11 07:22:06,273 - deploy_to_temp_schema - ERROR - Failed to deploy GO definitions: ['Cannot deploy go_definitions because no entities exist. Entities must be deployed first.']
2025-05-11 07:22:06,273 - prescriptive_parser - INFO - Parsing lo_definitions prescriptive text
2025-05-11 07:22:06,273 - lo_parser - INFO - Starting to parse LO definitions
2025-05-11 07:22:06,273 - lo_parser - INFO - Parsing LO: SubmitLeaveRequest
2025-05-11 07:22:06,274 - lo_parser - INFO - Successfully parsed LO 'SubmitLeaveRequest'
2025-05-11 07:22:06,274 - lo_parser - INFO - Parsing LO: ReviewLeaveRequest
2025-05-11 07:22:06,274 - lo_parser - INFO - Successfully parsed LO 'ReviewLeaveRequest'
2025-05-11 07:22:06,274 - lo_parser - INFO - Successfully parsed 2 local objectives
2025-05-11 07:22:06,274 - deploy_to_temp_schema - INFO - Deploying lo_definitions
2025-05-11 07:22:06,277 - component_deployer - INFO - Deploying component of type: lo_definitions
2025-05-11 07:22:06,280 - component_deployer - INFO - Using schema: workflow_temp
2025-05-11 07:22:06,284 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:06,285 - component_deployer - ERROR - Cannot deploy lo_definitions because no entities exist. Entities must be deployed first.
2025-05-11 07:22:06,285 - deploy_to_temp_schema - ERROR - Failed to deploy lo_definitions: ['Cannot deploy lo_definitions because no entities exist. Entities must be deployed first.']
2025-05-11 07:22:06,285 - deploy_to_temp_schema - ERROR - Failed to deploy LO definitions: ['Cannot deploy lo_definitions because no entities exist. Entities must be deployed first.']
2025-05-11 07:22:06,285 - prescriptive_parser - INFO - Parsing roles prescriptive text
2025-05-11 07:22:06,285 - role_parser - INFO - Starting to parse role definitions
2025-05-11 07:22:06,286 - role_parser - INFO - Parsing role: Employee (ID: emp001)
2025-05-11 07:22:06,286 - role_parser - INFO - Successfully parsed role 'Employee'
2025-05-11 07:22:06,286 - role_parser - INFO - Parsing role: Manager (ID: mgr001)
2025-05-11 07:22:06,286 - role_parser - INFO - Successfully parsed role 'Manager'
2025-05-11 07:22:06,286 - role_parser - INFO - Parsing role: HRManager (ID: hr001)
2025-05-11 07:22:06,286 - role_parser - INFO - Successfully parsed role 'HRManager'
2025-05-11 07:22:06,286 - role_parser - INFO - Parsing role: FinanceManager (ID: fin001)
2025-05-11 07:22:06,286 - role_parser - INFO - Successfully parsed role 'FinanceManager'
2025-05-11 07:22:06,286 - role_parser - INFO - Parsing role: SystemAdmin (ID: sys001)
2025-05-11 07:22:06,286 - role_parser - INFO - Successfully parsed role 'SystemAdmin'
2025-05-11 07:22:06,286 - role_parser - INFO - Successfully parsed 5 roles
2025-05-11 07:22:06,286 - deploy_to_temp_schema - INFO - Deploying roles
2025-05-11 07:22:06,287 - component_deployer - INFO - Deploying component of type: roles
2025-05-11 07:22:06,289 - component_deployer - INFO - Using schema: workflow_temp
2025-05-11 07:22:06,293 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:06,294 - component_deployer - ERROR - Cannot deploy roles because no entities exist. Entities must be deployed first.
2025-05-11 07:22:06,294 - deploy_to_temp_schema - ERROR - Failed to deploy roles: ['Cannot deploy roles because no entities exist. Entities must be deployed first.']
2025-05-11 07:22:06,294 - deploy_to_temp_schema - ERROR - Failed to deploy roles: ['Cannot deploy roles because no entities exist. Entities must be deployed first.']
2025-05-11 07:22:06,294 - deploy_to_temp_schema - INFO - Querying schema workflow_temp
2025-05-11 07:22:06,298 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:06,300 - deploy_to_temp_schema - INFO - Found 76 tables in schema workflow_temp
2025-05-11 07:22:06,305 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:06,306 - deploy_to_temp_schema - INFO - No data found in table workflow_temp.agent_rights
2025-05-11 07:22:06,309 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:06,310 - deploy_to_temp_schema - INFO - No data found in table workflow_temp.agent_stack
2025-05-11 07:22:06,314 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:06,315 - deploy_to_temp_schema - INFO - No data found in table workflow_temp.alembic_version
2025-05-11 07:22:06,318 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:06,319 - deploy_to_temp_schema - INFO - No data found in table workflow_temp.attribute_enum_values
2025-05-11 07:22:06,323 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:06,324 - deploy_to_temp_schema - INFO - No data found in table workflow_temp.attribute_ui_controls
2025-05-11 07:22:06,328 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:06,329 - deploy_to_temp_schema - INFO - No data found in table workflow_temp.attribute_validations
2025-05-11 07:22:06,333 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:06,333 - deploy_to_temp_schema - INFO - No data found in table workflow_temp.conditional_success_messages
2025-05-11 07:22:06,337 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:06,338 - deploy_to_temp_schema - INFO - No data found in table workflow_temp.data_mapping_stack
2025-05-11 07:22:06,341 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:06,342 - deploy_to_temp_schema - INFO - No data found in table workflow_temp.data_mappings
2025-05-11 07:22:06,347 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:06,348 - deploy_to_temp_schema - INFO - No data found in table workflow_temp.dropdown_data_sources
2025-05-11 07:22:06,353 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:06,354 - deploy_to_temp_schema - INFO - No data found in table workflow_temp.entities
2025-05-11 07:22:06,358 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:06,359 - deploy_to_temp_schema - INFO - No data found in table workflow_temp.entity_attribute_metadata
2025-05-11 07:22:06,364 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:06,365 - deploy_to_temp_schema - INFO - No data found in table workflow_temp.entity_attributes
2025-05-11 07:22:06,368 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:06,369 - deploy_to_temp_schema - INFO - No data found in table workflow_temp.entity_business_rules
2025-05-11 07:22:06,373 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:06,373 - deploy_to_temp_schema - INFO - No data found in table workflow_temp.entity_permissions
2025-05-11 07:22:06,378 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:06,379 - deploy_to_temp_schema - INFO - No data found in table workflow_temp.entity_relationships
2025-05-11 07:22:06,384 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:06,385 - deploy_to_temp_schema - INFO - No data found in table workflow_temp.execution_path_tracking
2025-05-11 07:22:06,389 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:06,390 - deploy_to_temp_schema - INFO - No data found in table workflow_temp.execution_pathway_conditions
2025-05-11 07:22:06,394 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:06,395 - deploy_to_temp_schema - INFO - No data found in table workflow_temp.execution_pathways
2025-05-11 07:22:06,400 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:06,400 - deploy_to_temp_schema - INFO - No data found in table workflow_temp.execution_rules
2025-05-11 07:22:06,405 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:06,406 - deploy_to_temp_schema - INFO - No data found in table workflow_temp.global_objectives
2025-05-11 07:22:06,411 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:06,412 - deploy_to_temp_schema - INFO - No data found in table workflow_temp.go_lo_mapping
2025-05-11 07:22:06,417 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:06,418 - deploy_to_temp_schema - INFO - No data found in table workflow_temp.go_performance_metrics
2025-05-11 07:22:06,422 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:06,423 - deploy_to_temp_schema - INFO - No data found in table workflow_temp.input_data_sources
2025-05-11 07:22:06,428 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:06,429 - deploy_to_temp_schema - INFO - No data found in table workflow_temp.input_dependencies
2025-05-11 07:22:06,434 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:06,434 - deploy_to_temp_schema - INFO - No data found in table workflow_temp.input_items
2025-05-11 07:22:06,438 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:06,439 - deploy_to_temp_schema - INFO - No data found in table workflow_temp.input_stack
2025-05-11 07:22:06,444 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:06,445 - deploy_to_temp_schema - INFO - No data found in table workflow_temp.lo_data_mapping_stack
2025-05-11 07:22:06,450 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:06,451 - deploy_to_temp_schema - INFO - No data found in table workflow_temp.lo_data_mappings
2025-05-11 07:22:06,455 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:06,456 - deploy_to_temp_schema - INFO - No data found in table workflow_temp.lo_input_execution
2025-05-11 07:22:06,460 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:06,461 - deploy_to_temp_schema - INFO - No data found in table workflow_temp.lo_input_items
2025-05-11 07:22:06,465 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:06,466 - deploy_to_temp_schema - INFO - No data found in table workflow_temp.lo_input_stack
2025-05-11 07:22:06,470 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:06,471 - deploy_to_temp_schema - INFO - No data found in table workflow_temp.lo_input_validations
2025-05-11 07:22:06,475 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:06,476 - deploy_to_temp_schema - INFO - No data found in table workflow_temp.lo_nested_functions
2025-05-11 07:22:06,480 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:06,481 - deploy_to_temp_schema - INFO - No data found in table workflow_temp.lo_output_execution
2025-05-11 07:22:06,486 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:06,487 - deploy_to_temp_schema - INFO - No data found in table workflow_temp.lo_output_items
2025-05-11 07:22:06,492 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:06,492 - deploy_to_temp_schema - INFO - No data found in table workflow_temp.lo_output_stack
2025-05-11 07:22:06,497 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:06,498 - deploy_to_temp_schema - INFO - No data found in table workflow_temp.lo_output_triggers
2025-05-11 07:22:06,502 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:06,502 - deploy_to_temp_schema - INFO - No data found in table workflow_temp.lo_system_functions
2025-05-11 07:22:06,507 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:06,508 - deploy_to_temp_schema - INFO - No data found in table workflow_temp.local_objectives
2025-05-11 07:22:06,512 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:06,513 - deploy_to_temp_schema - INFO - No data found in table workflow_temp.mapping_rules
2025-05-11 07:22:06,517 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:06,517 - deploy_to_temp_schema - INFO - No data found in table workflow_temp.metrics_aggregation
2025-05-11 07:22:06,522 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:06,523 - deploy_to_temp_schema - INFO - No data found in table workflow_temp.metrics_reporting
2025-05-11 07:22:06,528 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:06,528 - deploy_to_temp_schema - INFO - No data found in table workflow_temp.objective_permissions
2025-05-11 07:22:06,533 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:06,534 - deploy_to_temp_schema - INFO - No data found in table workflow_temp.organizational_units
2025-05-11 07:22:06,538 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:06,539 - deploy_to_temp_schema - INFO - No data found in table workflow_temp.output_items
2025-05-11 07:22:06,543 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:06,543 - deploy_to_temp_schema - INFO - No data found in table workflow_temp.output_stack
2025-05-11 07:22:06,548 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:06,548 - deploy_to_temp_schema - INFO - No data found in table workflow_temp.output_triggers
2025-05-11 07:22:06,552 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:06,552 - deploy_to_temp_schema - INFO - No data found in table workflow_temp.permission_capabilities
2025-05-11 07:22:06,556 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:06,557 - deploy_to_temp_schema - INFO - No data found in table workflow_temp.permission_contexts
2025-05-11 07:22:06,560 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:06,561 - deploy_to_temp_schema - INFO - No data found in table workflow_temp.permission_types
2025-05-11 07:22:06,565 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:06,565 - deploy_to_temp_schema - INFO - No data found in table workflow_temp.role
2025-05-11 07:22:06,569 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:06,570 - deploy_to_temp_schema - INFO - No data found in table workflow_temp.role_inheritance
2025-05-11 07:22:06,574 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:06,575 - deploy_to_temp_schema - INFO - No data found in table workflow_temp.role_permissions
2025-05-11 07:22:06,581 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:06,582 - deploy_to_temp_schema - INFO - No data found in table workflow_temp.roles
2025-05-11 07:22:06,586 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:06,586 - deploy_to_temp_schema - INFO - No data found in table workflow_temp.runtime_metrics_stack
2025-05-11 07:22:06,591 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:06,592 - deploy_to_temp_schema - INFO - No data found in table workflow_temp.success_messages
2025-05-11 07:22:06,597 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:06,598 - deploy_to_temp_schema - INFO - No data found in table workflow_temp.system_functions
2025-05-11 07:22:06,601 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:06,602 - deploy_to_temp_schema - INFO - No data found in table workflow_temp.tenants
2025-05-11 07:22:06,607 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:06,607 - deploy_to_temp_schema - INFO - No data found in table workflow_temp.terminal_pathways
2025-05-11 07:22:06,612 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:06,613 - deploy_to_temp_schema - INFO - No data found in table workflow_temp.ui_elements
2025-05-11 07:22:06,617 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:06,618 - deploy_to_temp_schema - INFO - No data found in table workflow_temp.ui_stack
2025-05-11 07:22:06,623 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:06,623 - deploy_to_temp_schema - INFO - No data found in table workflow_temp.user
2025-05-11 07:22:06,629 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:06,629 - deploy_to_temp_schema - INFO - No data found in table workflow_temp.user_oauth_tokens
2025-05-11 07:22:06,633 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:06,634 - deploy_to_temp_schema - INFO - No data found in table workflow_temp.user_organizations
2025-05-11 07:22:06,639 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:06,640 - deploy_to_temp_schema - INFO - No data found in table workflow_temp.user_role
2025-05-11 07:22:06,644 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:06,645 - deploy_to_temp_schema - INFO - No data found in table workflow_temp.user_roles
2025-05-11 07:22:06,649 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:06,650 - deploy_to_temp_schema - INFO - No data found in table workflow_temp.user_sessions
2025-05-11 07:22:06,654 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:06,654 - deploy_to_temp_schema - INFO - No data found in table workflow_temp.users
2025-05-11 07:22:06,659 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:06,660 - deploy_to_temp_schema - INFO - No data found in table workflow_temp.workflow_instances
2025-05-11 07:22:06,664 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:06,664 - deploy_to_temp_schema - INFO - No data found in table workflow_temp.workflow_results
2025-05-11 07:22:06,669 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:06,670 - deploy_to_temp_schema - INFO - No data found in table workflow_temp.workflow_transaction
2025-05-11 07:22:06,674 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:06,674 - deploy_to_temp_schema - INFO - No data found in table workflow_temp.z_entity_leave_application
2025-05-11 07:22:06,678 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:06,679 - deploy_to_temp_schema - INFO - No data found in table workflow_temp.z_entity_leave_sub_type
2025-05-11 07:22:06,684 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:06,684 - deploy_to_temp_schema - INFO - No data found in table workflow_temp.z_entity_role
2025-05-11 07:22:06,688 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:06,689 - deploy_to_temp_schema - INFO - No data found in table workflow_temp.z_entity_user
2025-05-11 07:22:06,689 - deploy_to_temp_schema - INFO - Components deployed to schema workflow_temp
2025-05-11 07:22:06,689 - deploy_to_temp_schema - INFO - You can now connect to the database and query the schema workflow_temp
2025-05-11 07:22:06,689 - deploy_to_temp_schema - INFO - To create a migration script, you can use the schema_analyzer.py script to compare the schemas

2025-05-11 07:22:06,710 - run_deploy_and_test - INFO - Waiting for database to settle...
2025-05-11 07:22:08,711 - run_deploy_and_test - INFO - Step 2: Running test_db_completeness.py
2025-05-11 07:22:08,711 - run_deploy_and_test - INFO - Running command: python3 test_db_completeness.py --schema workflow_temp
2025-05-11 07:22:09,367 - run_deploy_and_test - ERROR - Command failed with exit code 1
2025-05-11 07:22:09,367 - run_deploy_and_test - ERROR - Command output:

2025-05-11 07:22:09,367 - run_deploy_and_test - ERROR - Command stderr:
2025-05-11 07:22:08,799 - test_db_completeness - INFO - Checking database completeness for schema workflow_temp...
2025-05-11 07:22:08,805 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:08,811 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:08,815 - test_db_completeness - INFO - ✅ Table workflow_temp.entities exists
2025-05-11 07:22:08,819 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:08,821 - test_db_completeness - INFO - ✅ Table workflow_temp.entity_attributes exists
2025-05-11 07:22:08,826 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:08,829 - test_db_completeness - INFO - ✅ Table workflow_temp.entity_relationships exists
2025-05-11 07:22:08,834 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:08,837 - test_db_completeness - INFO - ✅ Table workflow_temp.entity_business_rules exists
2025-05-11 07:22:08,841 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:08,843 - test_db_completeness - INFO - ✅ Table workflow_temp.global_objectives exists
2025-05-11 07:22:08,848 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:08,850 - test_db_completeness - INFO - ✅ Table workflow_temp.go_lo_mapping exists
2025-05-11 07:22:08,854 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:08,856 - test_db_completeness - INFO - ✅ Table workflow_temp.go_performance_metrics exists
2025-05-11 07:22:08,860 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:08,862 - test_db_completeness - INFO - ✅ Table workflow_temp.local_objectives exists
2025-05-11 07:22:08,866 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:08,868 - test_db_completeness - INFO - ✅ Table workflow_temp.lo_input_stack exists
2025-05-11 07:22:08,872 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:08,874 - test_db_completeness - INFO - ✅ Table workflow_temp.lo_input_items exists
2025-05-11 07:22:08,879 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:08,881 - test_db_completeness - INFO - ✅ Table workflow_temp.lo_output_stack exists
2025-05-11 07:22:08,886 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:08,888 - test_db_completeness - INFO - ✅ Table workflow_temp.lo_output_items exists
2025-05-11 07:22:08,893 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:08,895 - test_db_completeness - INFO - ✅ Table workflow_temp.lo_data_mapping_stack exists
2025-05-11 07:22:08,900 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:08,902 - test_db_completeness - INFO - ✅ Table workflow_temp.lo_data_mappings exists
2025-05-11 07:22:08,906 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:08,909 - test_db_completeness - INFO - ✅ Table workflow_temp.lo_nested_functions exists
2025-05-11 07:22:08,914 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:08,916 - test_db_completeness - INFO - ✅ Table workflow_temp.execution_pathways exists
2025-05-11 07:22:08,920 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:08,922 - test_db_completeness - INFO - ✅ Table workflow_temp.execution_pathway_conditions exists
2025-05-11 07:22:08,927 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:08,930 - test_db_completeness - INFO - ✅ Table workflow_temp.agent_stack exists
2025-05-11 07:22:08,935 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:08,937 - test_db_completeness - INFO - ✅ Table workflow_temp.agent_rights exists
2025-05-11 07:22:08,943 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:08,945 - test_db_completeness - INFO - ✅ Table workflow_temp.roles exists
2025-05-11 07:22:08,950 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:08,952 - test_db_completeness - INFO - ✅ Table workflow_temp.role_permissions exists
2025-05-11 07:22:08,957 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:08,959 - test_db_completeness - INFO - ✅ Table workflow_temp.role_inheritance exists
2025-05-11 07:22:08,959 - test_db_completeness - INFO - Checking GO data mapping...
2025-05-11 07:22:08,964 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:08,966 - test_db_completeness - INFO - ✅ Table workflow_temp.go_lo_mapping exists
2025-05-11 07:22:08,970 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:08,971 - test_db_completeness - WARNING - ⚠️ Table workflow_temp.go_lo_mapping has no data
2025-05-11 07:22:08,977 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:08,981 - test_db_completeness - INFO - ✅ Column workflow_temp.global_objectives.process_mining_schema exists
2025-05-11 07:22:08,986 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:08,991 - test_db_completeness - INFO - ✅ Column workflow_temp.global_objectives.performance_metadata exists
2025-05-11 07:22:08,991 - test_db_completeness - INFO - Checking LO data mapping...
2025-05-11 07:22:08,996 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:08,998 - test_db_completeness - INFO - ✅ Table workflow_temp.lo_data_mapping_stack exists
2025-05-11 07:22:09,004 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:09,006 - test_db_completeness - INFO - ✅ Table workflow_temp.lo_data_mappings exists
2025-05-11 07:22:09,012 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:09,014 - test_db_completeness - WARNING - ⚠️ Table workflow_temp.lo_data_mapping_stack has no data
2025-05-11 07:22:09,019 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:09,020 - test_db_completeness - WARNING - ⚠️ Table workflow_temp.lo_data_mappings has no data
2025-05-11 07:22:09,025 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:09,030 - test_db_completeness - INFO - ✅ Column workflow_temp.local_objectives.ui_stack exists
2025-05-11 07:22:09,036 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:09,042 - test_db_completeness - INFO - ✅ Column workflow_temp.local_objectives.mapping_stack exists
2025-05-11 07:22:09,042 - test_db_completeness - INFO - Checking input stack...
2025-05-11 07:22:09,048 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:09,051 - test_db_completeness - INFO - ✅ Table workflow_temp.lo_input_stack exists
2025-05-11 07:22:09,056 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:09,058 - test_db_completeness - INFO - ✅ Table workflow_temp.lo_input_items exists
2025-05-11 07:22:09,064 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:09,066 - test_db_completeness - INFO - ✅ Table workflow_temp.lo_input_validations exists
2025-05-11 07:22:09,072 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:09,074 - test_db_completeness - INFO - ✅ Table workflow_temp.lo_nested_functions exists
2025-05-11 07:22:09,079 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:09,081 - test_db_completeness - INFO - ✅ Table workflow_temp.dropdown_data_sources exists
2025-05-11 07:22:09,088 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:09,091 - test_db_completeness - INFO - ✅ Table workflow_temp.input_dependencies exists
2025-05-11 07:22:09,097 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:09,098 - test_db_completeness - WARNING - ⚠️ Table workflow_temp.lo_input_stack has no data
2025-05-11 07:22:09,104 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:09,105 - test_db_completeness - WARNING - ⚠️ Table workflow_temp.lo_input_items has no data
2025-05-11 07:22:09,110 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:09,115 - test_db_completeness - INFO - ✅ Column workflow_temp.lo_input_items.ui_control exists
2025-05-11 07:22:09,121 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:09,126 - test_db_completeness - INFO - ✅ Column workflow_temp.lo_input_items.help_text exists
2025-05-11 07:22:09,131 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:09,136 - test_db_completeness - INFO - ✅ Column workflow_temp.lo_input_items.dependency_info exists
2025-05-11 07:22:09,136 - test_db_completeness - INFO - Checking output stack...
2025-05-11 07:22:09,141 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:09,143 - test_db_completeness - INFO - ✅ Table workflow_temp.lo_output_stack exists
2025-05-11 07:22:09,148 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:09,150 - test_db_completeness - INFO - ✅ Table workflow_temp.lo_output_items exists
2025-05-11 07:22:09,156 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:09,157 - test_db_completeness - WARNING - ⚠️ Table workflow_temp.lo_output_stack has no data
2025-05-11 07:22:09,162 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:09,163 - test_db_completeness - WARNING - ⚠️ Table workflow_temp.lo_output_items has no data
2025-05-11 07:22:09,163 - test_db_completeness - INFO - Checking entity business rules...
2025-05-11 07:22:09,169 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:09,171 - test_db_completeness - INFO - ✅ Table workflow_temp.entity_business_rules exists
2025-05-11 07:22:09,175 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:09,176 - test_db_completeness - WARNING - ⚠️ Table workflow_temp.entity_business_rules has no data
2025-05-11 07:22:09,182 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:09,186 - test_db_completeness - INFO - ✅ Column workflow_temp.entities.metadata exists
2025-05-11 07:22:09,191 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:09,196 - test_db_completeness - INFO - ✅ Column workflow_temp.entities.lifecycle_management exists
2025-05-11 07:22:09,201 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:09,206 - test_db_completeness - INFO - ✅ Column workflow_temp.entity_attributes.calculated_field exists
2025-05-11 07:22:09,211 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:09,215 - test_db_completeness - INFO - ✅ Column workflow_temp.entity_attributes.calculation_formula exists
2025-05-11 07:22:09,216 - test_db_completeness - INFO - Checking role inheritance...
2025-05-11 07:22:09,221 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:09,223 - test_db_completeness - INFO - ✅ Table workflow_temp.role_inheritance exists
2025-05-11 07:22:09,228 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:09,232 - test_db_completeness - INFO - ✅ Column workflow_temp.role_permissions.permission_name exists
2025-05-11 07:22:09,232 - test_db_completeness - INFO - Checking execution pathways...
2025-05-11 07:22:09,238 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:09,240 - test_db_completeness - INFO - ✅ Table workflow_temp.execution_pathways exists
2025-05-11 07:22:09,247 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:09,249 - test_db_completeness - INFO - ✅ Table workflow_temp.execution_pathway_conditions exists
2025-05-11 07:22:09,254 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:09,255 - test_db_completeness - WARNING - ⚠️ Table workflow_temp.execution_pathways has no data
2025-05-11 07:22:09,260 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:09,261 - test_db_completeness - WARNING - ⚠️ Table workflow_temp.execution_pathway_conditions has no data
2025-05-11 07:22:09,262 - test_db_completeness - INFO - Checking agent stack...
2025-05-11 07:22:09,267 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:09,268 - test_db_completeness - INFO - ✅ Table workflow_temp.agent_stack exists
2025-05-11 07:22:09,272 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:09,274 - test_db_completeness - INFO - ✅ Table workflow_temp.agent_rights exists
2025-05-11 07:22:09,280 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:09,281 - test_db_completeness - WARNING - ⚠️ Table workflow_temp.agent_stack has no data
2025-05-11 07:22:09,285 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:09,286 - test_db_completeness - WARNING - ⚠️ Table workflow_temp.agent_rights has no data
2025-05-11 07:22:09,286 - test_db_completeness - INFO - Checking nested functions...
2025-05-11 07:22:09,289 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:09,291 - test_db_completeness - INFO - ✅ Table workflow_temp.lo_nested_functions exists
2025-05-11 07:22:09,298 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:09,299 - test_db_completeness - WARNING - ⚠️ Table workflow_temp.lo_nested_functions has no data
2025-05-11 07:22:09,304 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:09,308 - test_db_completeness - INFO - ✅ Column workflow_temp.lo_nested_functions.function_name exists
2025-05-11 07:22:09,312 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:09,317 - test_db_completeness - INFO - ✅ Column workflow_temp.lo_nested_functions.function_type exists
2025-05-11 07:22:09,321 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:09,326 - test_db_completeness - INFO - ✅ Column workflow_temp.lo_nested_functions.parameters exists
2025-05-11 07:22:09,331 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:09,336 - test_db_completeness - INFO - ✅ Column workflow_temp.lo_nested_functions.output_to exists
2025-05-11 07:22:09,337 - test_db_completeness - ERROR - ❌ Database schema workflow_temp is incomplete
2025-05-11 07:22:09,337 - test_db_completeness - ERROR - ❌ GO data mapping is incomplete
2025-05-11 07:22:09,337 - test_db_completeness - ERROR - ❌ LO data mapping is incomplete
2025-05-11 07:22:09,337 - test_db_completeness - ERROR - ❌ Input stack is incomplete
2025-05-11 07:22:09,337 - test_db_completeness - ERROR - ❌ Output stack is incomplete
2025-05-11 07:22:09,337 - test_db_completeness - ERROR - ❌ Execution pathways are incomplete
2025-05-11 07:22:09,337 - test_db_completeness - ERROR - ❌ Agent stack is incomplete
2025-05-11 07:22:09,337 - test_db_completeness - ERROR - ❌ Nested functions are incomplete
2025-05-11 07:22:09,337 - test_db_completeness - ERROR - ❌ Database schema workflow_temp is incomplete

2025-05-11 07:22:09,367 - run_deploy_and_test - ERROR - Database completeness test failed
2025-05-11 07:22:09,367 - run_deploy_and_test - INFO - Step 3: Checking specific tables that might be empty
2025-05-11 07:22:09,367 - run_deploy_and_test - INFO - Running command: python3 query_db.py --schema workflow_temp --query "SELECT * FROM workflow_temp.go_lo_mapping"
2025-05-11 07:22:09,484 - run_deploy_and_test - INFO - Command output:

2025-05-11 07:22:09,485 - run_deploy_and_test - WARNING - Command stderr:
2025-05-11 07:22:09,458 - query_db - INFO - Executing query: SELECT * FROM workflow_temp.go_lo_mapping
2025-05-11 07:22:09,464 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:09,465 - query_db - INFO - Query returned no results

2025-05-11 07:22:09,485 - run_deploy_and_test - INFO - Running command: python3 query_db.py --schema workflow_temp --query "SELECT go_id, process_mining_schema, performance_metadata FROM workflow_temp.global_objectives"
2025-05-11 07:22:09,600 - run_deploy_and_test - INFO - Command output:

2025-05-11 07:22:09,600 - run_deploy_and_test - WARNING - Command stderr:
2025-05-11 07:22:09,573 - query_db - INFO - Executing query: SELECT go_id, process_mining_schema, performance_metadata FROM workflow_temp.global_objectives
2025-05-11 07:22:09,579 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:09,580 - query_db - INFO - Query returned no results

2025-05-11 07:22:09,600 - run_deploy_and_test - INFO - Running command: python3 query_db.py --schema workflow_temp --query "SELECT * FROM workflow_temp.lo_data_mapping_stack"
2025-05-11 07:22:09,714 - run_deploy_and_test - INFO - Command output:

2025-05-11 07:22:09,714 - run_deploy_and_test - WARNING - Command stderr:
2025-05-11 07:22:09,688 - query_db - INFO - Executing query: SELECT * FROM workflow_temp.lo_data_mapping_stack
2025-05-11 07:22:09,693 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:09,694 - query_db - INFO - Query returned no results

2025-05-11 07:22:09,715 - run_deploy_and_test - INFO - Running command: python3 query_db.py --schema workflow_temp --query "SELECT * FROM workflow_temp.lo_data_mappings"
2025-05-11 07:22:09,829 - run_deploy_and_test - INFO - Command output:

2025-05-11 07:22:09,830 - run_deploy_and_test - WARNING - Command stderr:
2025-05-11 07:22:09,803 - query_db - INFO - Executing query: SELECT * FROM workflow_temp.lo_data_mappings
2025-05-11 07:22:09,809 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:09,810 - query_db - INFO - Query returned no results

2025-05-11 07:22:09,830 - run_deploy_and_test - INFO - Running command: python3 query_db.py --schema workflow_temp --query "SELECT lo_id, ui_stack, mapping_stack FROM workflow_temp.local_objectives"
2025-05-11 07:22:09,946 - run_deploy_and_test - INFO - Command output:

2025-05-11 07:22:09,946 - run_deploy_and_test - WARNING - Command stderr:
2025-05-11 07:22:09,919 - query_db - INFO - Executing query: SELECT lo_id, ui_stack, mapping_stack FROM workflow_temp.local_objectives
2025-05-11 07:22:09,925 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:09,926 - query_db - INFO - Query returned no results

2025-05-11 07:22:09,946 - run_deploy_and_test - INFO - Running command: python3 query_db.py --schema workflow_temp --query "SELECT * FROM workflow_temp.lo_input_stack"
2025-05-11 07:22:10,065 - run_deploy_and_test - INFO - Command output:

2025-05-11 07:22:10,065 - run_deploy_and_test - WARNING - Command stderr:
2025-05-11 07:22:10,038 - query_db - INFO - Executing query: SELECT * FROM workflow_temp.lo_input_stack
2025-05-11 07:22:10,044 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:10,045 - query_db - INFO - Query returned no results

2025-05-11 07:22:10,065 - run_deploy_and_test - INFO - Running command: python3 query_db.py --schema workflow_temp --query "SELECT * FROM workflow_temp.lo_input_items"
2025-05-11 07:22:10,181 - run_deploy_and_test - INFO - Command output:

2025-05-11 07:22:10,181 - run_deploy_and_test - WARNING - Command stderr:
2025-05-11 07:22:10,155 - query_db - INFO - Executing query: SELECT * FROM workflow_temp.lo_input_items
2025-05-11 07:22:10,160 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:10,161 - query_db - INFO - Query returned no results

2025-05-11 07:22:10,181 - run_deploy_and_test - INFO - Running command: python3 query_db.py --schema workflow_temp --query "SELECT * FROM workflow_temp.lo_output_stack"
2025-05-11 07:22:10,296 - run_deploy_and_test - INFO - Command output:

2025-05-11 07:22:10,297 - run_deploy_and_test - WARNING - Command stderr:
2025-05-11 07:22:10,270 - query_db - INFO - Executing query: SELECT * FROM workflow_temp.lo_output_stack
2025-05-11 07:22:10,276 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:10,277 - query_db - INFO - Query returned no results

2025-05-11 07:22:10,297 - run_deploy_and_test - INFO - Running command: python3 query_db.py --schema workflow_temp --query "SELECT * FROM workflow_temp.lo_output_items"
2025-05-11 07:22:10,413 - run_deploy_and_test - INFO - Command output:

2025-05-11 07:22:10,413 - run_deploy_and_test - WARNING - Command stderr:
2025-05-11 07:22:10,387 - query_db - INFO - Executing query: SELECT * FROM workflow_temp.lo_output_items
2025-05-11 07:22:10,393 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:10,393 - query_db - INFO - Query returned no results

2025-05-11 07:22:10,413 - run_deploy_and_test - INFO - Running command: python3 query_db.py --schema workflow_temp --query "SELECT * FROM workflow_temp.entity_business_rules"
2025-05-11 07:22:10,532 - run_deploy_and_test - INFO - Command output:

2025-05-11 07:22:10,532 - run_deploy_and_test - WARNING - Command stderr:
2025-05-11 07:22:10,505 - query_db - INFO - Executing query: SELECT * FROM workflow_temp.entity_business_rules
2025-05-11 07:22:10,511 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:10,512 - query_db - INFO - Query returned no results

2025-05-11 07:22:10,532 - run_deploy_and_test - INFO - Running command: python3 query_db.py --schema workflow_temp --query "SELECT entity_id, metadata, lifecycle_management FROM workflow_temp.entities"
2025-05-11 07:22:10,648 - run_deploy_and_test - INFO - Command output:

2025-05-11 07:22:10,648 - run_deploy_and_test - WARNING - Command stderr:
2025-05-11 07:22:10,621 - query_db - INFO - Executing query: SELECT entity_id, metadata, lifecycle_management FROM workflow_temp.entities
2025-05-11 07:22:10,627 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:10,628 - query_db - INFO - Query returned no results

2025-05-11 07:22:10,648 - run_deploy_and_test - INFO - Running command: python3 query_db.py --schema workflow_temp --query "SELECT attribute_id, calculated_field, calculation_formula FROM workflow_temp.entity_attributes"
2025-05-11 07:22:10,764 - run_deploy_and_test - INFO - Command output:

2025-05-11 07:22:10,764 - run_deploy_and_test - WARNING - Command stderr:
2025-05-11 07:22:10,737 - query_db - INFO - Executing query: SELECT attribute_id, calculated_field, calculation_formula FROM workflow_temp.entity_attributes
2025-05-11 07:22:10,743 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:10,744 - query_db - INFO - Query returned no results

2025-05-11 07:22:10,764 - run_deploy_and_test - INFO - Running command: python3 query_db.py --schema workflow_temp --query "SELECT * FROM workflow_temp.role_inheritance"
2025-05-11 07:22:10,880 - run_deploy_and_test - INFO - Command output:

2025-05-11 07:22:10,881 - run_deploy_and_test - WARNING - Command stderr:
2025-05-11 07:22:10,854 - query_db - INFO - Executing query: SELECT * FROM workflow_temp.role_inheritance
2025-05-11 07:22:10,860 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:10,861 - query_db - INFO - Query returned no results

2025-05-11 07:22:10,881 - run_deploy_and_test - INFO - Running command: python3 query_db.py --schema workflow_temp --query "SELECT role_id, permission_id, permission_name FROM workflow_temp.role_permissions"
2025-05-11 07:22:10,996 - run_deploy_and_test - INFO - Command output:

2025-05-11 07:22:10,996 - run_deploy_and_test - WARNING - Command stderr:
2025-05-11 07:22:10,969 - query_db - INFO - Executing query: SELECT role_id, permission_id, permission_name FROM workflow_temp.role_permissions
2025-05-11 07:22:10,975 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 07:22:10,976 - db_utils - ERROR - Database error: column "permission_id" does not exist
LINE 1: SELECT role_id, permission_id, permission_name FROM workflow...
                        ^
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.UndefinedColumn: column "permission_id" does not exist
LINE 1: SELECT role_id, permission_id, permission_name FROM workflow...
                        ^

2025-05-11 07:22:10,976 - query_db - ERROR - Query failed: ['Database error: column "permission_id" does not exist\nLINE 1: SELECT role_id, permission_id, permission_name FROM workflow...\n                        ^\n']

