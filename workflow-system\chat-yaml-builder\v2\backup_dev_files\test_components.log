2025-05-10 16:17:14,495 - test_components - INFO - Running all tests
2025-05-10 16:17:14,495 - test_components - INFO - === Testing Component Validators ===
2025-05-10 16:17:14,495 - test_components - INFO - Testing component validator for roles
2025-05-10 16:17:14,497 - test_components - INFO - Component validation succeeded
2025-05-10 16:17:14,497 - test_components - INFO - Testing component validator for entities
2025-05-10 16:17:14,500 - test_components - INFO - Component validation succeeded
2025-05-10 16:17:14,500 - test_components - INFO - Testing component validator for go_definitions
2025-05-10 16:17:14,501 - test_components - INFO - Component validation succeeded
2025-05-10 16:17:14,501 - test_components - INFO - Testing component validator for lo_definitions
2025-05-10 16:17:14,508 - test_components - INFO - Component validation succeeded
2025-05-10 16:17:14,508 - test_components - INFO - === Testing Registry Validator ===
2025-05-10 16:17:14,508 - test_components - INFO - Testing registry validator
2025-05-10 16:17:14,519 - test_components - INFO - Registry validation succeeded
2025-05-10 16:17:14,519 - test_components - INFO - === Testing YAML Assembler ===
2025-05-10 16:17:14,519 - test_components - INFO - Testing YAML assembler
2025-05-10 16:17:14,537 - test_components - INFO - YAML assembly completed
2025-05-10 16:17:14,537 - test_components - INFO - === Testing ID Generator ===
2025-05-10 16:17:14,537 - test_components - INFO - Testing ID generator
2025-05-10 16:17:14,537 - test_components - INFO - Generated role ID: role_admin, valid: True
2025-05-10 16:17:14,537 - test_components - INFO - Generated entity ID: entity_employee, valid: True
2025-05-10 16:17:14,537 - test_components - INFO - Generated GO ID: go_employeeonboarding, valid: True
2025-05-10 16:17:14,537 - test_components - INFO - Generated LO ID: lo_collectpersonalinfo, valid: True
2025-05-10 16:17:14,537 - test_components - INFO - === Testing Component Deployers ===
2025-05-10 16:17:14,543 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-10 16:17:14,552 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-10 16:17:14,556 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-10 16:17:14,563 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-10 16:17:14,570 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-10 16:17:14,578 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-10 16:17:14,585 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-10 16:17:14,593 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-10 16:17:14,599 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-10 16:17:14,602 - test_components - INFO - Testing component deployer for roles
2025-05-10 16:17:14,602 - component_deployer - INFO - ComponentDeployer initialized with use_temp_schema=True
2025-05-10 16:17:14,605 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-10 16:17:14,613 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-10 16:17:14,617 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-10 16:17:14,623 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-10 16:17:14,629 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-10 16:17:14,636 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-10 16:17:14,642 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-10 16:17:14,649 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-10 16:17:14,655 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-10 16:17:14,657 - component_deployer - INFO - Deploying component of type: roles
2025-05-10 16:17:14,659 - component_deployer - INFO - Using schema: workflow_temp
2025-05-10 16:17:14,659 - role_deployer - INFO - Deploying roles to workflow_temp
2025-05-10 16:17:14,662 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-10 16:17:14,666 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-10 16:17:14,667 - role_deployer - INFO - Inserted role 'Admin' into workflow_temp.roles
2025-05-10 16:17:14,671 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-10 16:17:14,676 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-10 16:17:14,678 - role_deployer - INFO - Created workflow_temp.role_permissions table
2025-05-10 16:17:14,681 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-10 16:17:14,685 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-10 16:17:14,690 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-10 16:17:14,694 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-10 16:17:14,701 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-10 16:17:14,706 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-10 16:17:14,708 - role_deployer - INFO - Deployed permissions for role 'role_admin'
2025-05-10 16:17:14,712 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-10 16:17:14,716 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-10 16:17:14,717 - role_deployer - INFO - Inserted role 'Manager' into workflow_temp.roles
2025-05-10 16:17:14,721 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-10 16:17:14,726 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-10 16:17:14,731 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-10 16:17:14,736 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-10 16:17:14,737 - role_deployer - INFO - Deployed permissions for role 'role_manager'
2025-05-10 16:17:14,741 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-10 16:17:14,748 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-10 16:17:14,751 - role_deployer - INFO - Created workflow_temp.role_inheritance table
2025-05-10 16:17:14,756 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-10 16:17:14,762 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-10 16:17:14,763 - role_deployer - WARNING - Warning: Role 'role_manager' inherits non-existent role 'role_user'
2025-05-10 16:17:14,763 - role_deployer - INFO - Deployed inheritance for role 'role_manager'
2025-05-10 16:17:14,769 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-10 16:17:14,774 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-10 16:17:14,775 - role_deployer - INFO - Inserted role 'User' into workflow_temp.roles
2025-05-10 16:17:14,781 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-10 16:17:14,788 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-10 16:17:14,794 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-10 16:17:14,796 - role_deployer - INFO - Deployed permissions for role 'role_user'
2025-05-10 16:17:14,818 - role_deployer - INFO - Role deployment completed successfully
2025-05-10 16:17:14,818 - component_deployer - INFO - Component deployment succeeded
2025-05-10 16:17:14,818 - test_components - INFO - Component deployment succeeded
2025-05-10 16:17:14,818 - test_components - INFO - - Inserted role 'Admin' into workflow_temp.roles
2025-05-10 16:17:14,819 - test_components - INFO - - Created workflow_temp.role_permissions table
2025-05-10 16:17:14,819 - test_components - INFO - - Deployed permissions for role 'role_admin'
2025-05-10 16:17:14,819 - test_components - INFO - - Inserted role 'Manager' into workflow_temp.roles
2025-05-10 16:17:14,819 - test_components - INFO - - Deployed permissions for role 'role_manager'
2025-05-10 16:17:14,819 - test_components - INFO - - Created workflow_temp.role_inheritance table
2025-05-10 16:17:14,819 - test_components - INFO - - Warning: Role 'role_manager' inherits non-existent role 'role_user'
2025-05-10 16:17:14,819 - test_components - INFO - - Deployed inheritance for role 'role_manager'
2025-05-10 16:17:14,819 - test_components - INFO - - Inserted role 'User' into workflow_temp.roles
2025-05-10 16:17:14,819 - test_components - INFO - - Deployed permissions for role 'role_user'
2025-05-10 16:17:14,819 - test_components - INFO - - Inserted document into components
2025-05-10 16:17:14,819 - test_components - INFO - Testing component deployer for entities
2025-05-10 16:17:14,819 - component_deployer - INFO - ComponentDeployer initialized with use_temp_schema=True
2025-05-10 16:17:14,824 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-10 16:17:14,833 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-10 16:17:14,839 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-10 16:17:14,847 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-10 16:17:14,855 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-10 16:17:14,862 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-10 16:17:14,869 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-10 16:17:14,875 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-10 16:17:14,882 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-10 16:17:14,885 - component_deployer - INFO - Deploying component of type: entities
2025-05-10 16:17:14,888 - component_deployer - INFO - Using schema: workflow_temp
2025-05-10 16:17:14,888 - entity_deployer - INFO - Deploying entities to workflow_temp
2025-05-10 16:17:14,891 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-10 16:17:14,896 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-10 16:17:14,897 - entity_deployer - INFO - Inserted entity 'Department' into workflow_temp.entities
2025-05-10 16:17:14,900 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-10 16:17:14,904 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-10 16:17:14,906 - entity_deployer - INFO - Inserted attribute 'id' for entity 'entity_department' into workflow_temp.entity_attributes
2025-05-10 16:17:14,909 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-10 16:17:14,910 - entity_deployer - INFO - Inserted attribute 'name' for entity 'entity_department' into workflow_temp.entity_attributes
2025-05-10 16:17:14,914 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-10 16:17:14,915 - entity_deployer - INFO - Inserted attribute 'budget' for entity 'entity_department' into workflow_temp.entity_attributes
2025-05-10 16:17:14,918 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-10 16:17:14,920 - entity_deployer - INFO - Inserted attribute 'manager_id' for entity 'entity_department' into workflow_temp.entity_attributes
2025-05-10 16:17:14,923 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-10 16:17:14,928 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-10 16:17:14,931 - entity_deployer - INFO - Created workflow_temp.entity_relationships table
2025-05-10 16:17:14,934 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-10 16:17:14,941 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-10 16:17:14,942 - entity_deployer - WARNING - Warning: Relationship 'employees' in entity 'entity_department' references non-existent entity 'entity_employee'
2025-05-10 16:17:14,947 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-10 16:17:14,953 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-10 16:17:14,954 - entity_deployer - INFO - Inserted entity 'Employee' into workflow_temp.entities
2025-05-10 16:17:14,959 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-10 16:17:14,964 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-10 16:17:14,965 - entity_deployer - INFO - Inserted attribute 'id' for entity 'entity_employee' into workflow_temp.entity_attributes
2025-05-10 16:17:14,970 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-10 16:17:14,972 - entity_deployer - INFO - Inserted attribute 'name' for entity 'entity_employee' into workflow_temp.entity_attributes
2025-05-10 16:17:14,976 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-10 16:17:14,978 - entity_deployer - INFO - Inserted attribute 'email' for entity 'entity_employee' into workflow_temp.entity_attributes
2025-05-10 16:17:14,983 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-10 16:17:14,984 - entity_deployer - INFO - Inserted attribute 'department' for entity 'entity_employee' into workflow_temp.entity_attributes
2025-05-10 16:17:14,988 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-10 16:17:14,989 - entity_deployer - INFO - Inserted attribute 'manager' for entity 'entity_employee' into workflow_temp.entity_attributes
2025-05-10 16:17:14,994 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-10 16:17:14,996 - entity_deployer - INFO - Inserted attribute 'salary' for entity 'entity_employee' into workflow_temp.entity_attributes
2025-05-10 16:17:15,001 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-10 16:17:15,008 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-10 16:17:15,014 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-10 16:17:15,020 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-10 16:17:15,022 - entity_deployer - INFO - Inserted relationship 'manages' for entity 'entity_employee' into workflow_temp.entity_relationships
2025-05-10 16:17:15,028 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-10 16:17:15,034 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-10 16:17:15,035 - entity_deployer - INFO - Inserted relationship 'belongsTo' for entity 'entity_employee' into workflow_temp.entity_relationships
2025-05-10 16:17:15,041 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-10 16:17:15,048 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-10 16:17:15,051 - entity_deployer - INFO - Created workflow_temp.entity_business_rules table
2025-05-10 16:17:15,056 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-10 16:17:15,062 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-10 16:17:15,063 - entity_deployer - INFO - Inserted business rule 'salary_cap' for entity 'entity_employee' into workflow_temp.entity_business_rules
2025-05-10 16:17:15,079 - entity_deployer - INFO - Entity deployment completed successfully
2025-05-10 16:17:15,079 - component_deployer - INFO - Component deployment succeeded
2025-05-10 16:17:15,079 - test_components - INFO - Component deployment succeeded
2025-05-10 16:17:15,079 - test_components - INFO - - Inserted entity 'Department' into workflow_temp.entities
2025-05-10 16:17:15,079 - test_components - INFO - - Inserted attribute 'id' for entity 'entity_department' into workflow_temp.entity_attributes
2025-05-10 16:17:15,079 - test_components - INFO - - Inserted attribute 'name' for entity 'entity_department' into workflow_temp.entity_attributes
2025-05-10 16:17:15,079 - test_components - INFO - - Inserted attribute 'budget' for entity 'entity_department' into workflow_temp.entity_attributes
2025-05-10 16:17:15,079 - test_components - INFO - - Inserted attribute 'manager_id' for entity 'entity_department' into workflow_temp.entity_attributes
2025-05-10 16:17:15,079 - test_components - INFO - - Created workflow_temp.entity_relationships table
2025-05-10 16:17:15,079 - test_components - INFO - - Warning: Relationship 'employees' in entity 'entity_department' references non-existent entity 'entity_employee'
2025-05-10 16:17:15,079 - test_components - INFO - - Inserted entity 'Employee' into workflow_temp.entities
2025-05-10 16:17:15,080 - test_components - INFO - - Inserted attribute 'id' for entity 'entity_employee' into workflow_temp.entity_attributes
2025-05-10 16:17:15,080 - test_components - INFO - - Inserted attribute 'name' for entity 'entity_employee' into workflow_temp.entity_attributes
2025-05-10 16:17:15,080 - test_components - INFO - - Inserted attribute 'email' for entity 'entity_employee' into workflow_temp.entity_attributes
2025-05-10 16:17:15,080 - test_components - INFO - - Inserted attribute 'department' for entity 'entity_employee' into workflow_temp.entity_attributes
2025-05-10 16:17:15,080 - test_components - INFO - - Inserted attribute 'manager' for entity 'entity_employee' into workflow_temp.entity_attributes
2025-05-10 16:17:15,080 - test_components - INFO - - Inserted attribute 'salary' for entity 'entity_employee' into workflow_temp.entity_attributes
2025-05-10 16:17:15,080 - test_components - INFO - - Inserted relationship 'manages' for entity 'entity_employee' into workflow_temp.entity_relationships
2025-05-10 16:17:15,080 - test_components - INFO - - Inserted relationship 'belongsTo' for entity 'entity_employee' into workflow_temp.entity_relationships
2025-05-10 16:17:15,080 - test_components - INFO - - Created workflow_temp.entity_business_rules table
2025-05-10 16:17:15,080 - test_components - INFO - - Inserted business rule 'salary_cap' for entity 'entity_employee' into workflow_temp.entity_business_rules
2025-05-10 16:17:15,080 - test_components - INFO - - Inserted document into components
2025-05-10 16:17:15,080 - test_components - INFO - Testing component deployer for go_definitions
2025-05-10 16:17:15,080 - component_deployer - INFO - ComponentDeployer initialized with use_temp_schema=True
2025-05-10 16:17:15,086 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-10 16:17:15,097 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-10 16:17:15,103 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-10 16:17:15,112 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-10 16:17:15,120 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-10 16:17:15,129 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-10 16:17:15,138 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-10 16:17:15,146 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-10 16:17:15,152 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-10 16:17:15,154 - component_deployer - INFO - Deploying component of type: go_definitions
2025-05-10 16:17:15,156 - component_deployer - INFO - Using schema: workflow_temp
2025-05-10 16:17:15,156 - go_deployer - INFO - Deploying GO definitions to workflow_temp
2025-05-10 16:17:15,160 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-10 16:17:15,165 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-10 16:17:15,166 - go_deployer - INFO - Inserted GO 'EmployeeOnboarding' into workflow_temp.global_objectives
2025-05-10 16:17:15,170 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-10 16:17:15,175 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-10 16:17:15,177 - go_deployer - INFO - Created workflow_temp.go_lo_mapping table
2025-05-10 16:17:15,181 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-10 16:17:15,186 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-10 16:17:15,187 - go_deployer - INFO - Inserted LO mapping 'CollectPersonalInfo' for GO 'go_employeeonboarding' into workflow_temp.go_lo_mapping
2025-05-10 16:17:15,191 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-10 16:17:15,192 - go_deployer - INFO - Inserted LO mapping 'AssignDepartment' for GO 'go_employeeonboarding' into workflow_temp.go_lo_mapping
2025-05-10 16:17:15,198 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-10 16:17:15,200 - go_deployer - INFO - Inserted LO mapping 'SetupAccounts' for GO 'go_employeeonboarding' into workflow_temp.go_lo_mapping
2025-05-10 16:17:15,205 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-10 16:17:15,206 - go_deployer - INFO - Inserted LO mapping 'ScheduleTraining' for GO 'go_employeeonboarding' into workflow_temp.go_lo_mapping
2025-05-10 16:17:15,211 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-10 16:17:15,218 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-10 16:17:15,222 - go_deployer - INFO - Created workflow_temp.go_performance_metrics table
2025-05-10 16:17:15,226 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-10 16:17:15,232 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-10 16:17:15,234 - go_deployer - INFO - Inserted performance metric 'onboarding_time' for GO 'go_employeeonboarding' into workflow_temp.go_performance_metrics
2025-05-10 16:17:15,238 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-10 16:17:15,239 - go_deployer - INFO - Inserted performance metric 'completion_rate' for GO 'go_employeeonboarding' into workflow_temp.go_performance_metrics
2025-05-10 16:17:15,252 - go_deployer - INFO - GO deployment completed successfully
2025-05-10 16:17:15,252 - component_deployer - INFO - Component deployment succeeded
2025-05-10 16:17:15,252 - test_components - INFO - Component deployment succeeded
2025-05-10 16:17:15,252 - test_components - INFO - - Inserted GO 'EmployeeOnboarding' into workflow_temp.global_objectives
2025-05-10 16:17:15,252 - test_components - INFO - - Created workflow_temp.go_lo_mapping table
2025-05-10 16:17:15,252 - test_components - INFO - - Inserted LO mapping 'CollectPersonalInfo' for GO 'go_employeeonboarding' into workflow_temp.go_lo_mapping
2025-05-10 16:17:15,252 - test_components - INFO - - Inserted LO mapping 'AssignDepartment' for GO 'go_employeeonboarding' into workflow_temp.go_lo_mapping
2025-05-10 16:17:15,252 - test_components - INFO - - Inserted LO mapping 'SetupAccounts' for GO 'go_employeeonboarding' into workflow_temp.go_lo_mapping
2025-05-10 16:17:15,252 - test_components - INFO - - Inserted LO mapping 'ScheduleTraining' for GO 'go_employeeonboarding' into workflow_temp.go_lo_mapping
2025-05-10 16:17:15,253 - test_components - INFO - - Created workflow_temp.go_performance_metrics table
2025-05-10 16:17:15,253 - test_components - INFO - - Inserted performance metric 'onboarding_time' for GO 'go_employeeonboarding' into workflow_temp.go_performance_metrics
2025-05-10 16:17:15,253 - test_components - INFO - - Inserted performance metric 'completion_rate' for GO 'go_employeeonboarding' into workflow_temp.go_performance_metrics
2025-05-10 16:17:15,253 - test_components - INFO - - Inserted document into components
2025-05-10 16:17:15,257 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-10 16:17:15,257 - test_components - INFO - GO 'go_employeeonboarding' exists in the database: [('go_employeeonboarding', 'EmployeeOnboarding')]
2025-05-10 16:17:15,262 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-10 16:17:15,276 - lo_deployer - INFO - Deploying LO definitions to workflow_temp with existing connection
2025-05-10 16:17:15,276 - lo_deployer - WARNING - Warning: LO 'CollectPersonalInfo' references GO 'EmployeeOnboarding'
2025-05-10 16:17:15,278 - lo_deployer - INFO - Inserted LO 'CollectPersonalInfo' into workflow_temp.local_objectives
2025-05-10 16:17:15,278 - lo_deployer - INFO - Inserted input item 'name' for LO 'lo_collectpersonalinfo' into workflow_temp.lo_input_items
2025-05-10 16:17:15,278 - lo_deployer - INFO - Inserted input item 'email' for LO 'lo_collectpersonalinfo' into workflow_temp.lo_input_items
2025-05-10 16:17:15,279 - lo_deployer - INFO - Inserted input item 'phone' for LO 'lo_collectpersonalinfo' into workflow_temp.lo_input_items
2025-05-10 16:17:15,279 - lo_deployer - INFO - Inserted input item 'address' for LO 'lo_collectpersonalinfo' into workflow_temp.lo_input_items
2025-05-10 16:17:15,279 - lo_deployer - INFO - Inserted input item 'emergency_contact' for LO 'lo_collectpersonalinfo' into workflow_temp.lo_input_items
2025-05-10 16:17:15,279 - lo_deployer - INFO - Inserted output item 'employee_record' for LO 'lo_collectpersonalinfo' into workflow_temp.lo_output_items
2025-05-10 16:17:15,279 - lo_deployer - INFO - Inserted output item 'employee_id' for LO 'lo_collectpersonalinfo' into workflow_temp.lo_output_items
2025-05-10 16:17:15,279 - lo_deployer - WARNING - Warning: LO 'AssignDepartment' references GO 'EmployeeOnboarding'
2025-05-10 16:17:15,280 - lo_deployer - INFO - Inserted LO 'AssignDepartment' into workflow_temp.local_objectives
2025-05-10 16:17:15,280 - lo_deployer - INFO - Inserted input item 'employee_id' for LO 'lo_assigndepartment' into workflow_temp.lo_input_items
2025-05-10 16:17:15,280 - lo_deployer - INFO - Inserted input item 'department' for LO 'lo_assigndepartment' into workflow_temp.lo_input_items
2025-05-10 16:17:15,280 - lo_deployer - INFO - Inserted input item 'manager' for LO 'lo_assigndepartment' into workflow_temp.lo_input_items
2025-05-10 16:17:15,280 - lo_deployer - INFO - Inserted output item 'department_assignment' for LO 'lo_assigndepartment' into workflow_temp.lo_output_items
2025-05-10 16:17:15,284 - lo_deployer - INFO - Created workflow_temp.lo_dependencies table
2025-05-10 16:17:15,284 - lo_deployer - INFO - Inserted dependency on LO 'CollectPersonalInfo' for LO 'lo_assigndepartment' into workflow_temp.lo_dependencies
2025-05-10 16:17:15,285 - lo_deployer - WARNING - Warning: LO 'SetupAccounts' references GO 'EmployeeOnboarding'
2025-05-10 16:17:15,285 - lo_deployer - INFO - Inserted LO 'SetupAccounts' into workflow_temp.local_objectives
2025-05-10 16:17:15,285 - lo_deployer - INFO - Inserted input item 'employee_id' for LO 'lo_setupaccounts' into workflow_temp.lo_input_items
2025-05-10 16:17:15,285 - lo_deployer - INFO - Inserted input item 'department' for LO 'lo_setupaccounts' into workflow_temp.lo_input_items
2025-05-10 16:17:15,285 - lo_deployer - INFO - Inserted input item 'email_account' for LO 'lo_setupaccounts' into workflow_temp.lo_input_items
2025-05-10 16:17:15,286 - lo_deployer - INFO - Inserted input item 'vpn_access' for LO 'lo_setupaccounts' into workflow_temp.lo_input_items
2025-05-10 16:17:15,286 - lo_deployer - INFO - Inserted input item 'system_access' for LO 'lo_setupaccounts' into workflow_temp.lo_input_items
2025-05-10 16:17:15,286 - lo_deployer - INFO - Inserted output item 'account_setup' for LO 'lo_setupaccounts' into workflow_temp.lo_output_items
2025-05-10 16:17:15,286 - lo_deployer - INFO - Inserted output item 'email_address' for LO 'lo_setupaccounts' into workflow_temp.lo_output_items
2025-05-10 16:17:15,287 - lo_deployer - INFO - Inserted dependency on LO 'AssignDepartment' for LO 'lo_setupaccounts' into workflow_temp.lo_dependencies
2025-05-10 16:17:15,287 - lo_deployer - WARNING - Warning: LO 'ScheduleTraining' references GO 'EmployeeOnboarding'
2025-05-10 16:17:15,287 - lo_deployer - INFO - Inserted LO 'ScheduleTraining' into workflow_temp.local_objectives
2025-05-10 16:17:15,287 - lo_deployer - INFO - Inserted input item 'employee_id' for LO 'lo_scheduletraining' into workflow_temp.lo_input_items
2025-05-10 16:17:15,287 - lo_deployer - INFO - Inserted input item 'department' for LO 'lo_scheduletraining' into workflow_temp.lo_input_items
2025-05-10 16:17:15,287 - lo_deployer - INFO - Inserted input item 'orientation_date' for LO 'lo_scheduletraining' into workflow_temp.lo_input_items
2025-05-10 16:17:15,288 - lo_deployer - INFO - Inserted input item 'department_training_date' for LO 'lo_scheduletraining' into workflow_temp.lo_input_items
2025-05-10 16:17:15,288 - lo_deployer - INFO - Inserted input item 'system_training' for LO 'lo_scheduletraining' into workflow_temp.lo_input_items
2025-05-10 16:17:15,288 - lo_deployer - INFO - Inserted output item 'training_schedule' for LO 'lo_scheduletraining' into workflow_temp.lo_output_items
2025-05-10 16:17:15,288 - lo_deployer - INFO - Inserted dependency on LO 'SetupAccounts' for LO 'lo_scheduletraining' into workflow_temp.lo_dependencies
2025-05-10 16:17:15,297 - lo_deployer - INFO - LO deployment completed successfully
2025-05-10 16:17:15,297 - test_components - INFO - Committed transaction for LO deployment
2025-05-10 16:17:15,297 - test_components - INFO - === Test Summary ===
2025-05-10 16:17:15,297 - test_components - INFO - Component Validators:
2025-05-10 16:17:15,298 - test_components - INFO - - Roles: PASS
2025-05-10 16:17:15,298 - test_components - INFO - - Entities: PASS
2025-05-10 16:17:15,298 - test_components - INFO - - GO Definitions: PASS
2025-05-10 16:17:15,298 - test_components - INFO - - LO Definitions: PASS
2025-05-10 16:17:15,298 - test_components - INFO - Registry Validator: PASS
2025-05-10 16:17:15,298 - test_components - INFO - YAML Assembler: PASS
2025-05-10 16:17:15,298 - test_components - INFO - ID Generator: PASS
2025-05-10 16:17:15,298 - test_components - INFO - Component Deployers:
2025-05-10 16:17:15,298 - test_components - INFO - - Roles: PASS
2025-05-10 16:17:15,298 - test_components - INFO - - Entities: PASS
2025-05-10 16:17:15,298 - test_components - INFO - - GO Definitions: PASS
2025-05-10 16:17:15,298 - test_components - INFO - - LO Definitions: PASS
2025-05-10 16:17:15,298 - test_components - INFO - Test results written to /home/<USER>/workflow-system/chat-yaml-builder/v2/test_results.txt
2025-05-10 16:17:15,298 - test_components - INFO - All tests completed. Results written to /home/<USER>/workflow-system/chat-yaml-builder/v2/test_results.txt
