2025-05-12 11:11:53,313 - fix_entity_deployer - INFO - Starting to fix entity deployer
2025-05-12 11:11:53,313 - fix_entity_deployer - INFO - === Backing up entity_deployer.py ===
2025-05-12 11:11:53,313 - fix_entity_deployer - INFO - Backing up entity_deployer.py
2025-05-12 11:11:53,314 - fix_entity_deployer - INFO - Backed up entity_deployer.py to /home/<USER>/workflow-system/chat-yaml-builder/v2/deployers/entity_deployer.py.bak
2025-05-12 11:11:53,314 - fix_entity_deployer - INFO - Backed up entity_deployer.py to /home/<USER>/workflow-system/chat-yaml-builder/v2/deployers/entity_deployer.py.bak
2025-05-12 11:11:53,314 - fix_entity_deployer - INFO - === Replacing entity_deployer.py with entity_deployer_v2.py ===
2025-05-12 11:11:53,314 - fix_entity_deployer - INFO - Replacing entity_deployer.py with entity_deployer_v2.py
2025-05-12 11:11:53,314 - fix_entity_deployer - INFO - Replaced entity_deployer.py with entity_deployer_v2.py
2025-05-12 11:11:53,314 - fix_entity_deployer - INFO - Replaced entity_deployer.py with entity_deployer_v2.py
2025-05-12 11:11:53,314 - fix_entity_deployer - INFO - === Running implement_missing_features.py ===
2025-05-12 11:11:53,314 - fix_entity_deployer - INFO - Running implement_missing_features.py
2025-05-12 11:11:53,678 - fix_entity_deployer - INFO - implement_missing_features.py ran successfully
2025-05-12 11:11:53,678 - fix_entity_deployer - INFO -   All fixes completed successfully. See implement_missing_features.log for details.
2025-05-12 11:11:53,678 - fix_entity_deployer - INFO - implement_missing_features.py ran successfully
2025-05-12 11:11:53,679 - fix_entity_deployer - INFO -   All fixes completed successfully. See implement_missing_features.log for details.
2025-05-12 11:11:53,679 - fix_entity_deployer - INFO - === Verifying fixes ===
2025-05-12 11:11:53,679 - fix_entity_deployer - INFO - Verifying fixes
2025-05-12 11:11:53,679 - fix_entity_deployer - INFO - Checking entity IDs
2025-05-12 11:11:53,686 - db_connection - INFO - Connected to database workflow_system on 172.18.0.5:5432
2025-05-12 11:11:53,687 - fix_entity_deployer - INFO - All entity IDs use sequential IDs (E1, E2)
2025-05-12 11:11:53,687 - fix_entity_deployer - INFO - Checking enum values
2025-05-12 11:11:53,691 - db_connection - INFO - Connected to database workflow_system on 172.18.0.5:5432
2025-05-12 11:11:53,693 - fix_entity_deployer - INFO - No enum attributes found
2025-05-12 11:11:53,693 - fix_entity_deployer - INFO - Checking validations
2025-05-12 11:11:53,696 - db_connection - INFO - Connected to database workflow_system on 172.18.0.5:5432
2025-05-12 11:11:53,697 - fix_entity_deployer - INFO - Found 2 validations
2025-05-12 11:11:53,697 - fix_entity_deployer - INFO - Checking calculated fields
2025-05-12 11:11:53,703 - db_connection - INFO - Connected to database workflow_system on 172.18.0.5:5432
2025-05-12 11:11:53,704 - fix_entity_deployer - INFO - Found 1 calculated fields
2025-05-12 11:11:53,704 - fix_entity_deployer - INFO - Checking lifecycle management
2025-05-12 11:11:53,708 - db_connection - INFO - Connected to database workflow_system on 172.18.0.5:5432
2025-05-12 11:11:53,709 - fix_entity_deployer - INFO - Found 3 lifecycle management entries
2025-05-12 11:11:53,709 - fix_entity_deployer - INFO - Checking for duplicate foreign key constraints
2025-05-12 11:11:53,714 - db_connection - INFO - Connected to database workflow_system on 172.18.0.5:5432
2025-05-12 11:11:53,782 - fix_entity_deployer - INFO - No duplicate foreign key constraints found
2025-05-12 11:11:53,783 - fix_entity_deployer - INFO - Verification completed
2025-05-12 11:11:53,783 - fix_entity_deployer - INFO - All entity IDs use sequential IDs (E1, E2)
2025-05-12 11:11:53,783 - fix_entity_deployer - INFO - No enum attributes found
2025-05-12 11:11:53,783 - fix_entity_deployer - INFO - Found 2 validations
2025-05-12 11:11:53,783 - fix_entity_deployer - INFO - Found 1 calculated fields
2025-05-12 11:11:53,783 - fix_entity_deployer - INFO - Found 3 lifecycle management entries
2025-05-12 11:11:53,783 - fix_entity_deployer - INFO - No duplicate foreign key constraints found
2025-05-12 11:11:53,783 - fix_entity_deployer - INFO - Verification completed
2025-05-12 11:11:53,783 - fix_entity_deployer - INFO - === Running test_entity_deployer_v2.py ===
2025-05-12 11:11:53,783 - fix_entity_deployer - INFO - Running test_entity_deployer_v2.py
2025-05-12 11:11:55,574 - fix_entity_deployer - INFO - test_entity_deployer_v2.py ran successfully
2025-05-12 11:11:55,574 - fix_entity_deployer - INFO -   Deployment successful: True
2025-05-12 11:11:55,574 - fix_entity_deployer - INFO -   See test_entity_deployer_v2.log for detailed information
2025-05-12 11:11:55,574 - fix_entity_deployer - INFO - test_entity_deployer_v2.py ran successfully
2025-05-12 11:11:55,574 - fix_entity_deployer - INFO -   Deployment successful: True
2025-05-12 11:11:55,574 - fix_entity_deployer - INFO -   See test_entity_deployer_v2.log for detailed information
2025-05-12 11:11:55,574 - fix_entity_deployer - INFO - All fixes completed successfully
2025-05-12 11:19:47,455 - fix_entity_deployer - INFO - Starting to fix entity deployer
2025-05-12 11:19:47,455 - fix_entity_deployer - INFO - === Backing up entity_deployer.py ===
2025-05-12 11:19:47,455 - fix_entity_deployer - INFO - Backing up entity_deployer.py
2025-05-12 11:19:47,456 - fix_entity_deployer - INFO - Backed up entity_deployer.py to /home/<USER>/workflow-system/chat-yaml-builder/v2/deployers/entity_deployer.py.bak
2025-05-12 11:19:47,456 - fix_entity_deployer - INFO - Backed up entity_deployer.py to /home/<USER>/workflow-system/chat-yaml-builder/v2/deployers/entity_deployer.py.bak
2025-05-12 11:19:47,456 - fix_entity_deployer - INFO - === Replacing entity_deployer.py with entity_deployer_v2.py ===
2025-05-12 11:19:47,456 - fix_entity_deployer - INFO - Replacing entity_deployer.py with entity_deployer_v2.py
2025-05-12 11:19:47,456 - fix_entity_deployer - INFO - Replaced entity_deployer.py with entity_deployer_v2.py
2025-05-12 11:19:47,456 - fix_entity_deployer - INFO - Replaced entity_deployer.py with entity_deployer_v2.py
2025-05-12 11:19:47,456 - fix_entity_deployer - INFO - === Running implement_missing_features.py ===
2025-05-12 11:19:47,456 - fix_entity_deployer - INFO - Running implement_missing_features.py
2025-05-12 11:19:47,826 - fix_entity_deployer - INFO - implement_missing_features.py ran successfully
2025-05-12 11:19:47,826 - fix_entity_deployer - INFO -   All fixes completed successfully. See implement_missing_features.log for details.
2025-05-12 11:19:47,826 - fix_entity_deployer - INFO - implement_missing_features.py ran successfully
2025-05-12 11:19:47,826 - fix_entity_deployer - INFO -   All fixes completed successfully. See implement_missing_features.log for details.
2025-05-12 11:19:47,827 - fix_entity_deployer - INFO - === Verifying fixes ===
2025-05-12 11:19:47,827 - fix_entity_deployer - INFO - Verifying fixes
2025-05-12 11:19:47,827 - fix_entity_deployer - INFO - Checking entity IDs
2025-05-12 11:19:47,834 - db_connection - INFO - Connected to database workflow_system on 172.18.0.5:5432
2025-05-12 11:19:47,835 - fix_entity_deployer - INFO - All entity IDs use sequential IDs (E1, E2)
2025-05-12 11:19:47,835 - fix_entity_deployer - INFO - Checking enum values
2025-05-12 11:19:47,841 - db_connection - INFO - Connected to database workflow_system on 172.18.0.5:5432
2025-05-12 11:19:47,842 - fix_entity_deployer - INFO - No enum attributes found
2025-05-12 11:19:47,842 - fix_entity_deployer - INFO - Checking validations
2025-05-12 11:19:47,847 - db_connection - INFO - Connected to database workflow_system on 172.18.0.5:5432
2025-05-12 11:19:47,848 - fix_entity_deployer - INFO - Found 7 validations
2025-05-12 11:19:47,848 - fix_entity_deployer - INFO - Checking calculated fields
2025-05-12 11:19:47,853 - db_connection - INFO - Connected to database workflow_system on 172.18.0.5:5432
2025-05-12 11:19:47,854 - fix_entity_deployer - INFO - Found 1 calculated fields
2025-05-12 11:19:47,854 - fix_entity_deployer - INFO - Checking lifecycle management
2025-05-12 11:19:47,859 - db_connection - INFO - Connected to database workflow_system on 172.18.0.5:5432
2025-05-12 11:19:47,860 - fix_entity_deployer - INFO - Found 3 lifecycle management entries
2025-05-12 11:19:47,860 - fix_entity_deployer - INFO - Checking for duplicate foreign key constraints
2025-05-12 11:19:47,864 - db_connection - INFO - Connected to database workflow_system on 172.18.0.5:5432
2025-05-12 11:19:47,933 - fix_entity_deployer - INFO - No duplicate foreign key constraints found
2025-05-12 11:19:47,933 - fix_entity_deployer - INFO - Verification completed
2025-05-12 11:19:47,933 - fix_entity_deployer - INFO - All entity IDs use sequential IDs (E1, E2)
2025-05-12 11:19:47,933 - fix_entity_deployer - INFO - No enum attributes found
2025-05-12 11:19:47,933 - fix_entity_deployer - INFO - Found 7 validations
2025-05-12 11:19:47,933 - fix_entity_deployer - INFO - Found 1 calculated fields
2025-05-12 11:19:47,933 - fix_entity_deployer - INFO - Found 3 lifecycle management entries
2025-05-12 11:19:47,933 - fix_entity_deployer - INFO - No duplicate foreign key constraints found
2025-05-12 11:19:47,933 - fix_entity_deployer - INFO - Verification completed
2025-05-12 11:19:47,933 - fix_entity_deployer - INFO - === Running test_entity_deployer_v2.py ===
2025-05-12 11:19:47,933 - fix_entity_deployer - INFO - Running test_entity_deployer_v2.py
2025-05-12 11:19:49,712 - fix_entity_deployer - INFO - test_entity_deployer_v2.py ran successfully
2025-05-12 11:19:49,712 - fix_entity_deployer - INFO -   Deployment successful: True
2025-05-12 11:19:49,712 - fix_entity_deployer - INFO -   See test_entity_deployer_v2.log for detailed information
2025-05-12 11:19:49,712 - fix_entity_deployer - INFO - test_entity_deployer_v2.py ran successfully
2025-05-12 11:19:49,712 - fix_entity_deployer - INFO -   Deployment successful: True
2025-05-12 11:19:49,712 - fix_entity_deployer - INFO -   See test_entity_deployer_v2.log for detailed information
2025-05-12 11:19:49,712 - fix_entity_deployer - INFO - All fixes completed successfully
2025-05-12 11:21:52,723 - fix_entity_deployer - INFO - Starting to fix entity deployer
2025-05-12 11:21:52,723 - fix_entity_deployer - INFO - === Backing up entity_deployer.py ===
2025-05-12 11:21:52,723 - fix_entity_deployer - INFO - Backing up entity_deployer.py
2025-05-12 11:21:52,724 - fix_entity_deployer - INFO - Backed up entity_deployer.py to /home/<USER>/workflow-system/chat-yaml-builder/v2/deployers/entity_deployer.py.bak
2025-05-12 11:21:52,724 - fix_entity_deployer - INFO - Backed up entity_deployer.py to /home/<USER>/workflow-system/chat-yaml-builder/v2/deployers/entity_deployer.py.bak
2025-05-12 11:21:52,724 - fix_entity_deployer - INFO - === Replacing entity_deployer.py with entity_deployer_v2.py ===
2025-05-12 11:21:52,724 - fix_entity_deployer - INFO - Replacing entity_deployer.py with entity_deployer_v2.py
2025-05-12 11:21:52,724 - fix_entity_deployer - INFO - Replaced entity_deployer.py with entity_deployer_v2.py
2025-05-12 11:21:52,724 - fix_entity_deployer - INFO - Replaced entity_deployer.py with entity_deployer_v2.py
2025-05-12 11:21:52,724 - fix_entity_deployer - INFO - === Running implement_missing_features.py ===
2025-05-12 11:21:52,724 - fix_entity_deployer - INFO - Running implement_missing_features.py
2025-05-12 11:21:53,096 - fix_entity_deployer - INFO - implement_missing_features.py ran successfully
2025-05-12 11:21:53,096 - fix_entity_deployer - INFO -   All fixes completed successfully. See implement_missing_features.log for details.
2025-05-12 11:21:53,096 - fix_entity_deployer - INFO - implement_missing_features.py ran successfully
2025-05-12 11:21:53,096 - fix_entity_deployer - INFO -   All fixes completed successfully. See implement_missing_features.log for details.
2025-05-12 11:21:53,096 - fix_entity_deployer - INFO - === Verifying fixes ===
2025-05-12 11:21:53,096 - fix_entity_deployer - INFO - Verifying fixes
2025-05-12 11:21:53,096 - fix_entity_deployer - INFO - Checking entity IDs
2025-05-12 11:21:53,100 - db_connection - INFO - Connected to database workflow_system on 172.18.0.5:5432
2025-05-12 11:21:53,101 - fix_entity_deployer - INFO - All entity IDs use sequential IDs (E1, E2)
2025-05-12 11:21:53,101 - fix_entity_deployer - INFO - Checking enum values
2025-05-12 11:21:53,105 - db_connection - INFO - Connected to database workflow_system on 172.18.0.5:5432
2025-05-12 11:21:53,107 - fix_entity_deployer - INFO - No enum attributes found
2025-05-12 11:21:53,107 - fix_entity_deployer - INFO - Checking validations
2025-05-12 11:21:53,113 - db_connection - INFO - Connected to database workflow_system on 172.18.0.5:5432
2025-05-12 11:21:53,114 - fix_entity_deployer - INFO - Found 7 validations
2025-05-12 11:21:53,114 - fix_entity_deployer - INFO - Checking calculated fields
2025-05-12 11:21:53,118 - db_connection - INFO - Connected to database workflow_system on 172.18.0.5:5432
2025-05-12 11:21:53,119 - fix_entity_deployer - INFO - Found 1 calculated fields
2025-05-12 11:21:53,119 - fix_entity_deployer - INFO - Checking lifecycle management
2025-05-12 11:21:53,125 - db_connection - INFO - Connected to database workflow_system on 172.18.0.5:5432
2025-05-12 11:21:53,125 - fix_entity_deployer - INFO - Found 3 lifecycle management entries
2025-05-12 11:21:53,126 - fix_entity_deployer - INFO - Checking for duplicate foreign key constraints
2025-05-12 11:21:53,129 - db_connection - INFO - Connected to database workflow_system on 172.18.0.5:5432
2025-05-12 11:21:53,197 - fix_entity_deployer - INFO - No duplicate foreign key constraints found
2025-05-12 11:21:53,197 - fix_entity_deployer - INFO - Verification completed
2025-05-12 11:21:53,198 - fix_entity_deployer - INFO - All entity IDs use sequential IDs (E1, E2)
2025-05-12 11:21:53,198 - fix_entity_deployer - INFO - No enum attributes found
2025-05-12 11:21:53,198 - fix_entity_deployer - INFO - Found 7 validations
2025-05-12 11:21:53,198 - fix_entity_deployer - INFO - Found 1 calculated fields
2025-05-12 11:21:53,198 - fix_entity_deployer - INFO - Found 3 lifecycle management entries
2025-05-12 11:21:53,198 - fix_entity_deployer - INFO - No duplicate foreign key constraints found
2025-05-12 11:21:53,198 - fix_entity_deployer - INFO - Verification completed
2025-05-12 11:21:53,198 - fix_entity_deployer - INFO - === Running test_entity_deployer_v2.py ===
2025-05-12 11:21:53,198 - fix_entity_deployer - INFO - Running test_entity_deployer_v2.py
2025-05-12 11:21:54,890 - fix_entity_deployer - INFO - test_entity_deployer_v2.py ran successfully
2025-05-12 11:21:54,891 - fix_entity_deployer - INFO -   Deployment successful: True
2025-05-12 11:21:54,891 - fix_entity_deployer - INFO -   See test_entity_deployer_v2.log for detailed information
2025-05-12 11:21:54,891 - fix_entity_deployer - INFO - test_entity_deployer_v2.py ran successfully
2025-05-12 11:21:54,891 - fix_entity_deployer - INFO -   Deployment successful: True
2025-05-12 11:21:54,891 - fix_entity_deployer - INFO -   See test_entity_deployer_v2.log for detailed information
2025-05-12 11:21:54,891 - fix_entity_deployer - INFO - All fixes completed successfully
2025-05-12 11:57:22,458 - fix_entity_deployer - INFO - Starting to fix entity deployer
2025-05-12 11:57:22,458 - fix_entity_deployer - INFO - === Backing up entity_deployer.py ===
2025-05-12 11:57:22,458 - fix_entity_deployer - INFO - Backing up entity_deployer.py
2025-05-12 11:57:22,459 - fix_entity_deployer - INFO - Backed up entity_deployer.py to /home/<USER>/workflow-system/chat-yaml-builder/v2/deployers/entity_deployer.py.bak
2025-05-12 11:57:22,459 - fix_entity_deployer - INFO - Backed up entity_deployer.py to /home/<USER>/workflow-system/chat-yaml-builder/v2/deployers/entity_deployer.py.bak
2025-05-12 11:57:22,459 - fix_entity_deployer - INFO - === Replacing entity_deployer.py with entity_deployer_v2.py ===
2025-05-12 11:57:22,459 - fix_entity_deployer - INFO - Replacing entity_deployer.py with entity_deployer_v2.py
2025-05-12 11:57:22,459 - fix_entity_deployer - INFO - Replaced entity_deployer.py with entity_deployer_v2.py
2025-05-12 11:57:22,459 - fix_entity_deployer - INFO - Replaced entity_deployer.py with entity_deployer_v2.py
2025-05-12 11:57:22,459 - fix_entity_deployer - INFO - === Running implement_missing_features.py ===
2025-05-12 11:57:22,459 - fix_entity_deployer - INFO - Running implement_missing_features.py
2025-05-12 11:57:22,850 - fix_entity_deployer - INFO - implement_missing_features.py ran successfully
2025-05-12 11:57:22,850 - fix_entity_deployer - INFO -   All fixes completed successfully. See implement_missing_features.log for details.
2025-05-12 11:57:22,850 - fix_entity_deployer - INFO - implement_missing_features.py ran successfully
2025-05-12 11:57:22,850 - fix_entity_deployer - INFO -   All fixes completed successfully. See implement_missing_features.log for details.
2025-05-12 11:57:22,850 - fix_entity_deployer - INFO - === Verifying fixes ===
2025-05-12 11:57:22,850 - fix_entity_deployer - INFO - Verifying fixes
2025-05-12 11:57:22,850 - fix_entity_deployer - INFO - Checking entity IDs
2025-05-12 11:57:22,857 - db_connection - INFO - Connected to database workflow_system on 172.18.0.5:5432
2025-05-12 11:57:22,859 - fix_entity_deployer - INFO - All entity IDs use sequential IDs (E1, E2)
2025-05-12 11:57:22,859 - fix_entity_deployer - INFO - Checking enum values
2025-05-12 11:57:22,864 - db_connection - INFO - Connected to database workflow_system on 172.18.0.5:5432
2025-05-12 11:57:22,865 - fix_entity_deployer - INFO - No enum attributes found
2025-05-12 11:57:22,865 - fix_entity_deployer - INFO - Checking validations
2025-05-12 11:57:22,870 - db_connection - INFO - Connected to database workflow_system on 172.18.0.5:5432
2025-05-12 11:57:22,871 - fix_entity_deployer - INFO - Found 7 validations
2025-05-12 11:57:22,871 - fix_entity_deployer - INFO - Checking calculated fields
2025-05-12 11:57:22,875 - db_connection - INFO - Connected to database workflow_system on 172.18.0.5:5432
2025-05-12 11:57:22,876 - fix_entity_deployer - INFO - Found 1 calculated fields
2025-05-12 11:57:22,876 - fix_entity_deployer - INFO - Checking lifecycle management
2025-05-12 11:57:22,882 - db_connection - INFO - Connected to database workflow_system on 172.18.0.5:5432
2025-05-12 11:57:22,882 - fix_entity_deployer - INFO - Found 3 lifecycle management entries
2025-05-12 11:57:22,883 - fix_entity_deployer - INFO - Checking for duplicate foreign key constraints
2025-05-12 11:57:22,887 - db_connection - INFO - Connected to database workflow_system on 172.18.0.5:5432
2025-05-12 11:57:22,957 - fix_entity_deployer - INFO - No duplicate foreign key constraints found
2025-05-12 11:57:22,957 - fix_entity_deployer - INFO - Verification completed
2025-05-12 11:57:22,957 - fix_entity_deployer - INFO - All entity IDs use sequential IDs (E1, E2)
2025-05-12 11:57:22,958 - fix_entity_deployer - INFO - No enum attributes found
2025-05-12 11:57:22,958 - fix_entity_deployer - INFO - Found 7 validations
2025-05-12 11:57:22,958 - fix_entity_deployer - INFO - Found 1 calculated fields
2025-05-12 11:57:22,958 - fix_entity_deployer - INFO - Found 3 lifecycle management entries
2025-05-12 11:57:22,958 - fix_entity_deployer - INFO - No duplicate foreign key constraints found
2025-05-12 11:57:22,958 - fix_entity_deployer - INFO - Verification completed
2025-05-12 11:57:22,958 - fix_entity_deployer - INFO - === Running test_entity_deployer_v2.py ===
2025-05-12 11:57:22,958 - fix_entity_deployer - INFO - Running test_entity_deployer_v2.py
2025-05-12 11:57:24,743 - fix_entity_deployer - INFO - test_entity_deployer_v2.py ran successfully
2025-05-12 11:57:24,743 - fix_entity_deployer - INFO -   Deployment successful: True
2025-05-12 11:57:24,743 - fix_entity_deployer - INFO -   See test_entity_deployer_v2.log for detailed information
2025-05-12 11:57:24,743 - fix_entity_deployer - INFO - test_entity_deployer_v2.py ran successfully
2025-05-12 11:57:24,743 - fix_entity_deployer - INFO -   Deployment successful: True
2025-05-12 11:57:24,743 - fix_entity_deployer - INFO -   See test_entity_deployer_v2.log for detailed information
2025-05-12 11:57:24,743 - fix_entity_deployer - INFO - All fixes completed successfully
2025-05-12 12:11:45,126 - fix_entity_deployer - INFO - Starting to fix entity deployer
2025-05-12 12:11:45,126 - fix_entity_deployer - INFO - === Backing up entity_deployer.py ===
2025-05-12 12:11:45,126 - fix_entity_deployer - INFO - Backing up entity_deployer.py
2025-05-12 12:11:45,126 - fix_entity_deployer - INFO - Backed up entity_deployer.py to /home/<USER>/workflow-system/chat-yaml-builder/v2/deployers/entity_deployer.py.bak
2025-05-12 12:11:45,126 - fix_entity_deployer - INFO - Backed up entity_deployer.py to /home/<USER>/workflow-system/chat-yaml-builder/v2/deployers/entity_deployer.py.bak
2025-05-12 12:11:45,126 - fix_entity_deployer - INFO - === Replacing entity_deployer.py with entity_deployer_v2.py ===
2025-05-12 12:11:45,126 - fix_entity_deployer - INFO - Replacing entity_deployer.py with entity_deployer_v2.py
2025-05-12 12:11:45,127 - fix_entity_deployer - INFO - Replaced entity_deployer.py with entity_deployer_v2.py
2025-05-12 12:11:45,127 - fix_entity_deployer - INFO - Replaced entity_deployer.py with entity_deployer_v2.py
2025-05-12 12:11:45,127 - fix_entity_deployer - INFO - === Running implement_missing_features.py ===
2025-05-12 12:11:45,127 - fix_entity_deployer - INFO - Running implement_missing_features.py
2025-05-12 12:11:45,519 - fix_entity_deployer - INFO - implement_missing_features.py ran successfully
2025-05-12 12:11:45,519 - fix_entity_deployer - INFO -   All fixes completed successfully. See implement_missing_features.log for details.
2025-05-12 12:11:45,519 - fix_entity_deployer - INFO - implement_missing_features.py ran successfully
2025-05-12 12:11:45,520 - fix_entity_deployer - INFO -   All fixes completed successfully. See implement_missing_features.log for details.
2025-05-12 12:11:45,520 - fix_entity_deployer - INFO - === Verifying fixes ===
2025-05-12 12:11:45,520 - fix_entity_deployer - INFO - Verifying fixes
2025-05-12 12:11:45,520 - fix_entity_deployer - INFO - Checking entity IDs
2025-05-12 12:11:45,525 - db_connection - INFO - Connected to database workflow_system on 172.18.0.5:5432
2025-05-12 12:11:45,526 - fix_entity_deployer - INFO - All entity IDs use sequential IDs (E1, E2)
2025-05-12 12:11:45,526 - fix_entity_deployer - INFO - Checking enum values
2025-05-12 12:11:45,530 - db_connection - INFO - Connected to database workflow_system on 172.18.0.5:5432
2025-05-12 12:11:45,531 - fix_entity_deployer - INFO - No enum attributes found
2025-05-12 12:11:45,532 - fix_entity_deployer - INFO - Checking validations
2025-05-12 12:11:45,537 - db_connection - INFO - Connected to database workflow_system on 172.18.0.5:5432
2025-05-12 12:11:45,538 - fix_entity_deployer - INFO - Found 7 validations
2025-05-12 12:11:45,538 - fix_entity_deployer - INFO - Checking calculated fields
2025-05-12 12:11:45,543 - db_connection - INFO - Connected to database workflow_system on 172.18.0.5:5432
2025-05-12 12:11:45,543 - fix_entity_deployer - INFO - Found 1 calculated fields
2025-05-12 12:11:45,543 - fix_entity_deployer - INFO - Checking lifecycle management
2025-05-12 12:11:45,549 - db_connection - INFO - Connected to database workflow_system on 172.18.0.5:5432
2025-05-12 12:11:45,550 - fix_entity_deployer - INFO - Found 3 lifecycle management entries
2025-05-12 12:11:45,550 - fix_entity_deployer - INFO - Checking for duplicate foreign key constraints
2025-05-12 12:11:45,555 - db_connection - INFO - Connected to database workflow_system on 172.18.0.5:5432
2025-05-12 12:11:45,624 - fix_entity_deployer - INFO - No duplicate foreign key constraints found
2025-05-12 12:11:45,624 - fix_entity_deployer - INFO - Verification completed
2025-05-12 12:11:45,624 - fix_entity_deployer - INFO - All entity IDs use sequential IDs (E1, E2)
2025-05-12 12:11:45,624 - fix_entity_deployer - INFO - No enum attributes found
2025-05-12 12:11:45,624 - fix_entity_deployer - INFO - Found 7 validations
2025-05-12 12:11:45,624 - fix_entity_deployer - INFO - Found 1 calculated fields
2025-05-12 12:11:45,625 - fix_entity_deployer - INFO - Found 3 lifecycle management entries
2025-05-12 12:11:45,625 - fix_entity_deployer - INFO - No duplicate foreign key constraints found
2025-05-12 12:11:45,625 - fix_entity_deployer - INFO - Verification completed
2025-05-12 12:11:45,625 - fix_entity_deployer - INFO - === Running test_entity_deployer_v2.py ===
2025-05-12 12:11:45,625 - fix_entity_deployer - INFO - Running test_entity_deployer_v2.py
2025-05-12 12:11:47,385 - fix_entity_deployer - INFO - test_entity_deployer_v2.py ran successfully
2025-05-12 12:11:47,385 - fix_entity_deployer - INFO -   Deployment successful: True
2025-05-12 12:11:47,385 - fix_entity_deployer - INFO -   See test_entity_deployer_v2.log for detailed information
2025-05-12 12:11:47,385 - fix_entity_deployer - INFO - test_entity_deployer_v2.py ran successfully
2025-05-12 12:11:47,385 - fix_entity_deployer - INFO -   Deployment successful: True
2025-05-12 12:11:47,385 - fix_entity_deployer - INFO -   See test_entity_deployer_v2.log for detailed information
2025-05-12 12:11:47,385 - fix_entity_deployer - INFO - All fixes completed successfully
2025-05-12 12:15:03,970 - fix_entity_deployer - INFO - Starting to fix entity deployer
2025-05-12 12:15:03,970 - fix_entity_deployer - INFO - === Backing up entity_deployer.py ===
2025-05-12 12:15:03,970 - fix_entity_deployer - INFO - Backing up entity_deployer.py
2025-05-12 12:15:03,971 - fix_entity_deployer - INFO - Backed up entity_deployer.py to /home/<USER>/workflow-system/chat-yaml-builder/v2/deployers/entity_deployer.py.bak
2025-05-12 12:15:03,971 - fix_entity_deployer - INFO - Backed up entity_deployer.py to /home/<USER>/workflow-system/chat-yaml-builder/v2/deployers/entity_deployer.py.bak
2025-05-12 12:15:03,971 - fix_entity_deployer - INFO - === Replacing entity_deployer.py with entity_deployer_v2.py ===
2025-05-12 12:15:03,971 - fix_entity_deployer - INFO - Replacing entity_deployer.py with entity_deployer_v2.py
2025-05-12 12:15:03,971 - fix_entity_deployer - INFO - Replaced entity_deployer.py with entity_deployer_v2.py
2025-05-12 12:15:03,971 - fix_entity_deployer - INFO - Replaced entity_deployer.py with entity_deployer_v2.py
2025-05-12 12:15:03,971 - fix_entity_deployer - INFO - === Running implement_missing_features.py ===
2025-05-12 12:15:03,971 - fix_entity_deployer - INFO - Running implement_missing_features.py
2025-05-12 12:15:04,334 - fix_entity_deployer - INFO - implement_missing_features.py ran successfully
2025-05-12 12:15:04,335 - fix_entity_deployer - INFO -   All fixes completed successfully. See implement_missing_features.log for details.
2025-05-12 12:15:04,335 - fix_entity_deployer - INFO - implement_missing_features.py ran successfully
2025-05-12 12:15:04,335 - fix_entity_deployer - INFO -   All fixes completed successfully. See implement_missing_features.log for details.
2025-05-12 12:15:04,335 - fix_entity_deployer - INFO - === Verifying fixes ===
2025-05-12 12:15:04,335 - fix_entity_deployer - INFO - Verifying fixes
2025-05-12 12:15:04,335 - fix_entity_deployer - INFO - Checking entity IDs
2025-05-12 12:15:04,340 - db_connection - INFO - Connected to database workflow_system on 172.18.0.5:5432
2025-05-12 12:15:04,341 - fix_entity_deployer - INFO - All entity IDs use sequential IDs (E1, E2)
2025-05-12 12:15:04,341 - fix_entity_deployer - INFO - Checking enum values
2025-05-12 12:15:04,345 - db_connection - INFO - Connected to database workflow_system on 172.18.0.5:5432
2025-05-12 12:15:04,346 - fix_entity_deployer - INFO - No enum attributes found
2025-05-12 12:15:04,346 - fix_entity_deployer - INFO - Checking validations
2025-05-12 12:15:04,350 - db_connection - INFO - Connected to database workflow_system on 172.18.0.5:5432
2025-05-12 12:15:04,351 - fix_entity_deployer - INFO - Found 7 validations
2025-05-12 12:15:04,351 - fix_entity_deployer - INFO - Checking calculated fields
2025-05-12 12:15:04,356 - db_connection - INFO - Connected to database workflow_system on 172.18.0.5:5432
2025-05-12 12:15:04,356 - fix_entity_deployer - INFO - Found 1 calculated fields
2025-05-12 12:15:04,356 - fix_entity_deployer - INFO - Checking lifecycle management
2025-05-12 12:15:04,360 - db_connection - INFO - Connected to database workflow_system on 172.18.0.5:5432
2025-05-12 12:15:04,361 - fix_entity_deployer - INFO - Found 3 lifecycle management entries
2025-05-12 12:15:04,361 - fix_entity_deployer - INFO - Checking for duplicate foreign key constraints
2025-05-12 12:15:04,365 - db_connection - INFO - Connected to database workflow_system on 172.18.0.5:5432
2025-05-12 12:15:04,433 - fix_entity_deployer - INFO - No duplicate foreign key constraints found
2025-05-12 12:15:04,433 - fix_entity_deployer - INFO - Verification completed
2025-05-12 12:15:04,433 - fix_entity_deployer - INFO - All entity IDs use sequential IDs (E1, E2)
2025-05-12 12:15:04,433 - fix_entity_deployer - INFO - No enum attributes found
2025-05-12 12:15:04,433 - fix_entity_deployer - INFO - Found 7 validations
2025-05-12 12:15:04,433 - fix_entity_deployer - INFO - Found 1 calculated fields
2025-05-12 12:15:04,433 - fix_entity_deployer - INFO - Found 3 lifecycle management entries
2025-05-12 12:15:04,433 - fix_entity_deployer - INFO - No duplicate foreign key constraints found
2025-05-12 12:15:04,433 - fix_entity_deployer - INFO - Verification completed
2025-05-12 12:15:04,433 - fix_entity_deployer - INFO - === Running test_entity_deployer_v2.py ===
2025-05-12 12:15:04,433 - fix_entity_deployer - INFO - Running test_entity_deployer_v2.py
2025-05-12 12:15:06,209 - fix_entity_deployer - INFO - test_entity_deployer_v2.py ran successfully
2025-05-12 12:15:06,209 - fix_entity_deployer - INFO -   Deployment successful: True
2025-05-12 12:15:06,210 - fix_entity_deployer - INFO -   See test_entity_deployer_v2.log for detailed information
2025-05-12 12:15:06,210 - fix_entity_deployer - INFO - test_entity_deployer_v2.py ran successfully
2025-05-12 12:15:06,210 - fix_entity_deployer - INFO -   Deployment successful: True
2025-05-12 12:15:06,210 - fix_entity_deployer - INFO -   See test_entity_deployer_v2.log for detailed information
2025-05-12 12:15:06,210 - fix_entity_deployer - INFO - All fixes completed successfully
2025-05-12 12:19:53,048 - fix_entity_deployer - INFO - Starting to fix entity deployer
2025-05-12 12:19:53,048 - fix_entity_deployer - INFO - === Backing up entity_deployer.py ===
2025-05-12 12:19:53,048 - fix_entity_deployer - INFO - Backing up entity_deployer.py
2025-05-12 12:19:53,049 - fix_entity_deployer - INFO - Backed up entity_deployer.py to /home/<USER>/workflow-system/chat-yaml-builder/v2/deployers/entity_deployer.py.bak
2025-05-12 12:19:53,049 - fix_entity_deployer - INFO - Backed up entity_deployer.py to /home/<USER>/workflow-system/chat-yaml-builder/v2/deployers/entity_deployer.py.bak
2025-05-12 12:19:53,049 - fix_entity_deployer - INFO - === Replacing entity_deployer.py with entity_deployer_v2.py ===
2025-05-12 12:19:53,049 - fix_entity_deployer - INFO - Replacing entity_deployer.py with entity_deployer_v2.py
2025-05-12 12:19:53,049 - fix_entity_deployer - INFO - Replaced entity_deployer.py with entity_deployer_v2.py
2025-05-12 12:19:53,050 - fix_entity_deployer - INFO - Replaced entity_deployer.py with entity_deployer_v2.py
2025-05-12 12:19:53,050 - fix_entity_deployer - INFO - === Running implement_missing_features.py ===
2025-05-12 12:19:53,050 - fix_entity_deployer - INFO - Running implement_missing_features.py
2025-05-12 12:19:53,414 - fix_entity_deployer - INFO - implement_missing_features.py ran successfully
2025-05-12 12:19:53,416 - fix_entity_deployer - INFO -   All fixes completed successfully. See implement_missing_features.log for details.
2025-05-12 12:19:53,416 - fix_entity_deployer - INFO - implement_missing_features.py ran successfully
2025-05-12 12:19:53,416 - fix_entity_deployer - INFO -   All fixes completed successfully. See implement_missing_features.log for details.
2025-05-12 12:19:53,416 - fix_entity_deployer - INFO - === Verifying fixes ===
2025-05-12 12:19:53,416 - fix_entity_deployer - INFO - Verifying fixes
2025-05-12 12:19:53,416 - fix_entity_deployer - INFO - Checking entity IDs
2025-05-12 12:19:53,423 - db_connection - INFO - Connected to database workflow_system on 172.18.0.5:5432
2025-05-12 12:19:53,424 - fix_entity_deployer - INFO - All entity IDs use sequential IDs (E1, E2)
2025-05-12 12:19:53,424 - fix_entity_deployer - INFO - Checking enum values
2025-05-12 12:19:53,429 - db_connection - INFO - Connected to database workflow_system on 172.18.0.5:5432
2025-05-12 12:19:53,431 - fix_entity_deployer - INFO - No enum attributes found
2025-05-12 12:19:53,431 - fix_entity_deployer - INFO - Checking validations
2025-05-12 12:19:53,437 - db_connection - INFO - Connected to database workflow_system on 172.18.0.5:5432
2025-05-12 12:19:53,438 - fix_entity_deployer - INFO - Found 7 validations
2025-05-12 12:19:53,438 - fix_entity_deployer - INFO - Checking calculated fields
2025-05-12 12:19:53,442 - db_connection - INFO - Connected to database workflow_system on 172.18.0.5:5432
2025-05-12 12:19:53,442 - fix_entity_deployer - INFO - Found 1 calculated fields
2025-05-12 12:19:53,442 - fix_entity_deployer - INFO - Checking lifecycle management
2025-05-12 12:19:53,448 - db_connection - INFO - Connected to database workflow_system on 172.18.0.5:5432
2025-05-12 12:19:53,449 - fix_entity_deployer - INFO - Found 3 lifecycle management entries
2025-05-12 12:19:53,449 - fix_entity_deployer - INFO - Checking for duplicate foreign key constraints
2025-05-12 12:19:53,453 - db_connection - INFO - Connected to database workflow_system on 172.18.0.5:5432
2025-05-12 12:19:53,521 - fix_entity_deployer - INFO - No duplicate foreign key constraints found
2025-05-12 12:19:53,522 - fix_entity_deployer - INFO - Verification completed
2025-05-12 12:19:53,522 - fix_entity_deployer - INFO - All entity IDs use sequential IDs (E1, E2)
2025-05-12 12:19:53,522 - fix_entity_deployer - INFO - No enum attributes found
2025-05-12 12:19:53,522 - fix_entity_deployer - INFO - Found 7 validations
2025-05-12 12:19:53,522 - fix_entity_deployer - INFO - Found 1 calculated fields
2025-05-12 12:19:53,522 - fix_entity_deployer - INFO - Found 3 lifecycle management entries
2025-05-12 12:19:53,522 - fix_entity_deployer - INFO - No duplicate foreign key constraints found
2025-05-12 12:19:53,522 - fix_entity_deployer - INFO - Verification completed
2025-05-12 12:19:53,522 - fix_entity_deployer - INFO - === Running test_entity_deployer_v2.py ===
2025-05-12 12:19:53,522 - fix_entity_deployer - INFO - Running test_entity_deployer_v2.py
2025-05-12 12:19:55,165 - fix_entity_deployer - INFO - test_entity_deployer_v2.py ran successfully
2025-05-12 12:19:55,165 - fix_entity_deployer - INFO -   Deployment successful: True
2025-05-12 12:19:55,165 - fix_entity_deployer - INFO -   See test_entity_deployer_v2.log for detailed information
2025-05-12 12:19:55,165 - fix_entity_deployer - INFO - test_entity_deployer_v2.py ran successfully
2025-05-12 12:19:55,165 - fix_entity_deployer - INFO -   Deployment successful: True
2025-05-12 12:19:55,165 - fix_entity_deployer - INFO -   See test_entity_deployer_v2.log for detailed information
2025-05-12 12:19:55,165 - fix_entity_deployer - INFO - All fixes completed successfully
2025-05-12 14:21:54,917 - fix_entity_deployer - INFO - Starting to fix entity deployer
2025-05-12 14:21:54,917 - fix_entity_deployer - INFO - === Backing up entity_deployer.py ===
2025-05-12 14:21:54,918 - fix_entity_deployer - INFO - Backing up entity_deployer.py
2025-05-12 14:21:54,918 - fix_entity_deployer - INFO - Backed up entity_deployer.py to /home/<USER>/workflow-system/chat-yaml-builder/v2/deployers/entity_deployer.py.bak
2025-05-12 14:21:54,918 - fix_entity_deployer - INFO - Backed up entity_deployer.py to /home/<USER>/workflow-system/chat-yaml-builder/v2/deployers/entity_deployer.py.bak
2025-05-12 14:21:54,918 - fix_entity_deployer - INFO - === Replacing entity_deployer.py with entity_deployer_v2.py ===
2025-05-12 14:21:54,918 - fix_entity_deployer - INFO - Replacing entity_deployer.py with entity_deployer_v2.py
2025-05-12 14:21:54,918 - fix_entity_deployer - INFO - Replaced entity_deployer.py with entity_deployer_v2.py
2025-05-12 14:21:54,918 - fix_entity_deployer - INFO - Replaced entity_deployer.py with entity_deployer_v2.py
2025-05-12 14:21:54,918 - fix_entity_deployer - INFO - === Running implement_missing_features.py ===
2025-05-12 14:21:54,918 - fix_entity_deployer - INFO - Running implement_missing_features.py
2025-05-12 14:21:55,296 - fix_entity_deployer - INFO - implement_missing_features.py ran successfully
2025-05-12 14:21:55,297 - fix_entity_deployer - INFO -   SQL: 
2025-05-12 14:21:55,297 - fix_entity_deployer - INFO -                           INSERT INTO workflow_temp.attribute_validations (
2025-05-12 14:21:55,297 - fix_entity_deployer - INFO -                               attribute_id, validation_name, validation_type, validation_expression, error_message,
2025-05-12 14:21:55,297 - fix_entity_deployer - INFO -                               created_at, created_by, updated_at, updated_by
2025-05-12 14:21:55,297 - fix_entity_deployer - INFO -                           ) VALUES (%s, %s, %s, %s, %s, NOW(), 'system', NOW(), 'system')
2025-05-12 14:21:55,297 - fix_entity_deployer - INFO -                           ON CONFLICT (attribute_id, validation_name) DO UPDATE
2025-05-12 14:21:55,297 - fix_entity_deployer - INFO -                           SET validation_type = EXCLUDED.validation_type,
2025-05-12 14:21:55,297 - fix_entity_deployer - INFO -                               validation_expression = EXCLUDED.validation_expression,
2025-05-12 14:21:55,297 - fix_entity_deployer - INFO -                               error_message = EXCLUDED.error_message,
2025-05-12 14:21:55,297 - fix_entity_deployer - INFO -                               updated_at = NOW(),
2025-05-12 14:21:55,297 - fix_entity_deployer - INFO -                               updated_by = 'system'
2025-05-12 14:21:55,297 - fix_entity_deployer - INFO -                           
2025-05-12 14:21:55,297 - fix_entity_deployer - INFO -   Params: ('E1.At9', 'hireDate_be_at_least_90_days_before_Emp', 'expression', 'be at least 90 days before Employee.performanceRating can be set', 'Invalid value for hireDate: be at least 90 days before Employee.performanceRating can be set')
2025-05-12 14:21:55,297 - fix_entity_deployer - INFO -   SQL: 
2025-05-12 14:21:55,297 - fix_entity_deployer - INFO -                           INSERT INTO workflow_temp.attribute_validations (
2025-05-12 14:21:55,297 - fix_entity_deployer - INFO -                               attribute_id, validation_name, validation_type, validation_expression, error_message,
2025-05-12 14:21:55,297 - fix_entity_deployer - INFO -                               created_at, created_by, updated_at, updated_by
2025-05-12 14:21:55,297 - fix_entity_deployer - INFO -                           ) VALUES (%s, %s, %s, %s, %s, NOW(), 'system', NOW(), 'system')
2025-05-12 14:21:55,297 - fix_entity_deployer - INFO -                           ON CONFLICT (attribute_id, validation_name) DO UPDATE
2025-05-12 14:21:55,297 - fix_entity_deployer - INFO -                           SET validation_type = EXCLUDED.validation_type,
2025-05-12 14:21:55,297 - fix_entity_deployer - INFO -                               validation_expression = EXCLUDED.validation_expression,
2025-05-12 14:21:55,297 - fix_entity_deployer - INFO -                               error_message = EXCLUDED.error_message,
2025-05-12 14:21:55,297 - fix_entity_deployer - INFO -                               updated_at = NOW(),
2025-05-12 14:21:55,297 - fix_entity_deployer - INFO -                               updated_by = 'system'
2025-05-12 14:21:55,297 - fix_entity_deployer - INFO -                           
2025-05-12 14:21:55,297 - fix_entity_deployer - INFO -   Params: ('E2.At5', 'budget_be_approved_by_Finance_before_', 'expression', 'be approved by Finance before changes', 'Invalid value for budget: be approved by Finance before changes')
2025-05-12 14:21:55,297 - fix_entity_deployer - INFO -   SQL: 
2025-05-12 14:21:55,297 - fix_entity_deployer - INFO -                           INSERT INTO workflow_temp.attribute_validations (
2025-05-12 14:21:55,297 - fix_entity_deployer - INFO -                               attribute_id, validation_name, validation_type, validation_expression, error_message,
2025-05-12 14:21:55,297 - fix_entity_deployer - INFO -                               created_at, created_by, updated_at, updated_by
2025-05-12 14:21:55,297 - fix_entity_deployer - INFO -                           ) VALUES (%s, %s, %s, %s, %s, NOW(), 'system', NOW(), 'system')
2025-05-12 14:21:55,297 - fix_entity_deployer - INFO -                           ON CONFLICT (attribute_id, validation_name) DO UPDATE
2025-05-12 14:21:55,297 - fix_entity_deployer - INFO -                           SET validation_type = EXCLUDED.validation_type,
2025-05-12 14:21:55,297 - fix_entity_deployer - INFO -                               validation_expression = EXCLUDED.validation_expression,
2025-05-12 14:21:55,297 - fix_entity_deployer - INFO -                               error_message = EXCLUDED.error_message,
2025-05-12 14:21:55,297 - fix_entity_deployer - INFO -                               updated_at = NOW(),
2025-05-12 14:21:55,297 - fix_entity_deployer - INFO -                               updated_by = 'system'
2025-05-12 14:21:55,297 - fix_entity_deployer - INFO -                           
2025-05-12 14:21:55,297 - fix_entity_deployer - INFO -   Params: ('E2.At3', 'managerId_reference_an_Employee_with_sta', 'expression', "reference an Employee with status = 'Active'", "Invalid value for managerId: reference an Employee with status = 'Active'")
2025-05-12 14:21:55,297 - fix_entity_deployer - INFO -   SQL: 
2025-05-12 14:21:55,297 - fix_entity_deployer - INFO -               INSERT INTO workflow_temp.attribute_validations (
2025-05-12 14:21:55,297 - fix_entity_deployer - INFO -                   attribute_id, validation_name, validation_type, validation_expression, error_message,
2025-05-12 14:21:55,297 - fix_entity_deployer - INFO -                   created_at, created_by, updated_at, updated_by
2025-05-12 14:21:55,297 - fix_entity_deployer - INFO -               ) VALUES (%s, %s, %s, %s, %s, NOW(), 'system', NOW(), 'system')
2025-05-12 14:21:55,297 - fix_entity_deployer - INFO -               ON CONFLICT (attribute_id, validation_name) DO UPDATE
2025-05-12 14:21:55,297 - fix_entity_deployer - INFO -               SET validation_type = EXCLUDED.validation_type,
2025-05-12 14:21:55,297 - fix_entity_deployer - INFO -                   validation_expression = EXCLUDED.validation_expression,
2025-05-12 14:21:55,297 - fix_entity_deployer - INFO -                   error_message = EXCLUDED.error_message,
2025-05-12 14:21:55,297 - fix_entity_deployer - INFO -                   updated_at = NOW(),
2025-05-12 14:21:55,298 - fix_entity_deployer - INFO -                   updated_by = 'system'
2025-05-12 14:21:55,298 - fix_entity_deployer - INFO -               
2025-05-12 14:21:55,298 - fix_entity_deployer - INFO -   Params: ('E1.At10', 'status_must_be_active_for_performance_reviews', 'expression', 'Employee.status must be Active to receive performance reviews', 'Employee status must be Active to receive performance reviews')
2025-05-12 14:21:55,298 - fix_entity_deployer - INFO -   SQL: 
2025-05-12 14:21:55,298 - fix_entity_deployer - INFO -               INSERT INTO workflow_temp.attribute_validations (
2025-05-12 14:21:55,298 - fix_entity_deployer - INFO -                   attribute_id, validation_name, validation_type, validation_expression, error_message,
2025-05-12 14:21:55,298 - fix_entity_deployer - INFO -                   created_at, created_by, updated_at, updated_by
2025-05-12 14:21:55,298 - fix_entity_deployer - INFO -               ) VALUES (%s, %s, %s, %s, %s, NOW(), 'system', NOW(), 'system')
2025-05-12 14:21:55,298 - fix_entity_deployer - INFO -               ON CONFLICT (attribute_id, validation_name) DO UPDATE
2025-05-12 14:21:55,298 - fix_entity_deployer - INFO -               SET validation_type = EXCLUDED.validation_type,
2025-05-12 14:21:55,298 - fix_entity_deployer - INFO -                   validation_expression = EXCLUDED.validation_expression,
2025-05-12 14:21:55,298 - fix_entity_deployer - INFO -                   error_message = EXCLUDED.error_message,
2025-05-12 14:21:55,298 - fix_entity_deployer - INFO -                   updated_at = NOW(),
2025-05-12 14:21:55,298 - fix_entity_deployer - INFO -                   updated_by = 'system'
2025-05-12 14:21:55,298 - fix_entity_deployer - INFO -               
2025-05-12 14:21:55,298 - fix_entity_deployer - INFO -   Params: ('E1.At9', 'hireDate_at_least_90_days_before_performance_rating', 'expression', 'Employee.hireDate must be at least 90 days before Employee.performanceRating can be set', 'Employee must be hired at least 90 days before setting performance rating')
2025-05-12 14:21:55,298 - fix_entity_deployer - INFO -   SQL: 
2025-05-12 14:21:55,298 - fix_entity_deployer - INFO -               INSERT INTO workflow_temp.attribute_validations (
2025-05-12 14:21:55,298 - fix_entity_deployer - INFO -                   attribute_id, validation_name, validation_type, validation_expression, error_message,
2025-05-12 14:21:55,298 - fix_entity_deployer - INFO -                   created_at, created_by, updated_at, updated_by
2025-05-12 14:21:55,298 - fix_entity_deployer - INFO -               ) VALUES (%s, %s, %s, %s, %s, NOW(), 'system', NOW(), 'system')
2025-05-12 14:21:55,298 - fix_entity_deployer - INFO -               ON CONFLICT (attribute_id, validation_name) DO UPDATE
2025-05-12 14:21:55,298 - fix_entity_deployer - INFO -               SET validation_type = EXCLUDED.validation_type,
2025-05-12 14:21:55,298 - fix_entity_deployer - INFO -                   validation_expression = EXCLUDED.validation_expression,
2025-05-12 14:21:55,298 - fix_entity_deployer - INFO -                   error_message = EXCLUDED.error_message,
2025-05-12 14:21:55,298 - fix_entity_deployer - INFO -                   updated_at = NOW(),
2025-05-12 14:21:55,298 - fix_entity_deployer - INFO -                   updated_by = 'system'
2025-05-12 14:21:55,298 - fix_entity_deployer - INFO -               
2025-05-12 14:21:55,298 - fix_entity_deployer - INFO -   Params: ('E2.At5', 'budget_approved_by_finance_before_changes', 'expression', 'Department.budget must be approved by Finance before changes', 'Department budget must be approved by Finance before changes')
2025-05-12 14:21:55,298 - fix_entity_deployer - INFO -   SQL: 
2025-05-12 14:21:55,298 - fix_entity_deployer - INFO -               INSERT INTO workflow_temp.attribute_validations (
2025-05-12 14:21:55,298 - fix_entity_deployer - INFO -                   attribute_id, validation_name, validation_type, validation_expression, error_message,
2025-05-12 14:21:55,298 - fix_entity_deployer - INFO -                   created_at, created_by, updated_at, updated_by
2025-05-12 14:21:55,298 - fix_entity_deployer - INFO -               ) VALUES (%s, %s, %s, %s, %s, NOW(), 'system', NOW(), 'system')
2025-05-12 14:21:55,298 - fix_entity_deployer - INFO -               ON CONFLICT (attribute_id, validation_name) DO UPDATE
2025-05-12 14:21:55,298 - fix_entity_deployer - INFO -               SET validation_type = EXCLUDED.validation_type,
2025-05-12 14:21:55,298 - fix_entity_deployer - INFO -                   validation_expression = EXCLUDED.validation_expression,
2025-05-12 14:21:55,298 - fix_entity_deployer - INFO -                   error_message = EXCLUDED.error_message,
2025-05-12 14:21:55,298 - fix_entity_deployer - INFO -                   updated_at = NOW(),
2025-05-12 14:21:55,298 - fix_entity_deployer - INFO -                   updated_by = 'system'
2025-05-12 14:21:55,298 - fix_entity_deployer - INFO -               
2025-05-12 14:21:55,298 - fix_entity_deployer - INFO -   Params: ('E2.At3', 'manager_must_be_active', 'expression', "Department.managerId must reference an Employee with status = 'Active'", 'Department manager must be an active employee')
2025-05-12 14:21:55,298 - fix_entity_deployer - INFO -   All fixes completed successfully. See implement_missing_features.log for details.
2025-05-12 14:21:55,298 - fix_entity_deployer - INFO - implement_missing_features.py ran successfully
2025-05-12 14:21:55,298 - fix_entity_deployer - INFO -   SQL: 
2025-05-12 14:21:55,298 - fix_entity_deployer - INFO -                           INSERT INTO workflow_temp.attribute_validations (
2025-05-12 14:21:55,298 - fix_entity_deployer - INFO -                               attribute_id, validation_name, validation_type, validation_expression, error_message,
2025-05-12 14:21:55,298 - fix_entity_deployer - INFO -                               created_at, created_by, updated_at, updated_by
2025-05-12 14:21:55,298 - fix_entity_deployer - INFO -                           ) VALUES (%s, %s, %s, %s, %s, NOW(), 'system', NOW(), 'system')
2025-05-12 14:21:55,298 - fix_entity_deployer - INFO -                           ON CONFLICT (attribute_id, validation_name) DO UPDATE
2025-05-12 14:21:55,298 - fix_entity_deployer - INFO -                           SET validation_type = EXCLUDED.validation_type,
2025-05-12 14:21:55,299 - fix_entity_deployer - INFO -                               validation_expression = EXCLUDED.validation_expression,
2025-05-12 14:21:55,299 - fix_entity_deployer - INFO -                               error_message = EXCLUDED.error_message,
2025-05-12 14:21:55,299 - fix_entity_deployer - INFO -                               updated_at = NOW(),
2025-05-12 14:21:55,299 - fix_entity_deployer - INFO -                               updated_by = 'system'
2025-05-12 14:21:55,299 - fix_entity_deployer - INFO -                           
2025-05-12 14:21:55,299 - fix_entity_deployer - INFO -   Params: ('E1.At9', 'hireDate_be_at_least_90_days_before_Emp', 'expression', 'be at least 90 days before Employee.performanceRating can be set', 'Invalid value for hireDate: be at least 90 days before Employee.performanceRating can be set')
2025-05-12 14:21:55,299 - fix_entity_deployer - INFO -   SQL: 
2025-05-12 14:21:55,299 - fix_entity_deployer - INFO -                           INSERT INTO workflow_temp.attribute_validations (
2025-05-12 14:21:55,299 - fix_entity_deployer - INFO -                               attribute_id, validation_name, validation_type, validation_expression, error_message,
2025-05-12 14:21:55,299 - fix_entity_deployer - INFO -                               created_at, created_by, updated_at, updated_by
2025-05-12 14:21:55,299 - fix_entity_deployer - INFO -                           ) VALUES (%s, %s, %s, %s, %s, NOW(), 'system', NOW(), 'system')
2025-05-12 14:21:55,299 - fix_entity_deployer - INFO -                           ON CONFLICT (attribute_id, validation_name) DO UPDATE
2025-05-12 14:21:55,299 - fix_entity_deployer - INFO -                           SET validation_type = EXCLUDED.validation_type,
2025-05-12 14:21:55,299 - fix_entity_deployer - INFO -                               validation_expression = EXCLUDED.validation_expression,
2025-05-12 14:21:55,299 - fix_entity_deployer - INFO -                               error_message = EXCLUDED.error_message,
2025-05-12 14:21:55,299 - fix_entity_deployer - INFO -                               updated_at = NOW(),
2025-05-12 14:21:55,299 - fix_entity_deployer - INFO -                               updated_by = 'system'
2025-05-12 14:21:55,299 - fix_entity_deployer - INFO -                           
2025-05-12 14:21:55,299 - fix_entity_deployer - INFO -   Params: ('E2.At5', 'budget_be_approved_by_Finance_before_', 'expression', 'be approved by Finance before changes', 'Invalid value for budget: be approved by Finance before changes')
2025-05-12 14:21:55,299 - fix_entity_deployer - INFO -   SQL: 
2025-05-12 14:21:55,299 - fix_entity_deployer - INFO -                           INSERT INTO workflow_temp.attribute_validations (
2025-05-12 14:21:55,299 - fix_entity_deployer - INFO -                               attribute_id, validation_name, validation_type, validation_expression, error_message,
2025-05-12 14:21:55,299 - fix_entity_deployer - INFO -                               created_at, created_by, updated_at, updated_by
2025-05-12 14:21:55,299 - fix_entity_deployer - INFO -                           ) VALUES (%s, %s, %s, %s, %s, NOW(), 'system', NOW(), 'system')
2025-05-12 14:21:55,299 - fix_entity_deployer - INFO -                           ON CONFLICT (attribute_id, validation_name) DO UPDATE
2025-05-12 14:21:55,299 - fix_entity_deployer - INFO -                           SET validation_type = EXCLUDED.validation_type,
2025-05-12 14:21:55,299 - fix_entity_deployer - INFO -                               validation_expression = EXCLUDED.validation_expression,
2025-05-12 14:21:55,300 - fix_entity_deployer - INFO -                               error_message = EXCLUDED.error_message,
2025-05-12 14:21:55,300 - fix_entity_deployer - INFO -                               updated_at = NOW(),
2025-05-12 14:21:55,300 - fix_entity_deployer - INFO -                               updated_by = 'system'
2025-05-12 14:21:55,300 - fix_entity_deployer - INFO -                           
2025-05-12 14:21:55,300 - fix_entity_deployer - INFO -   Params: ('E2.At3', 'managerId_reference_an_Employee_with_sta', 'expression', "reference an Employee with status = 'Active'", "Invalid value for managerId: reference an Employee with status = 'Active'")
2025-05-12 14:21:55,300 - fix_entity_deployer - INFO -   SQL: 
2025-05-12 14:21:55,300 - fix_entity_deployer - INFO -               INSERT INTO workflow_temp.attribute_validations (
2025-05-12 14:21:55,300 - fix_entity_deployer - INFO -                   attribute_id, validation_name, validation_type, validation_expression, error_message,
2025-05-12 14:21:55,300 - fix_entity_deployer - INFO -                   created_at, created_by, updated_at, updated_by
2025-05-12 14:21:55,301 - fix_entity_deployer - INFO -               ) VALUES (%s, %s, %s, %s, %s, NOW(), 'system', NOW(), 'system')
2025-05-12 14:21:55,301 - fix_entity_deployer - INFO -               ON CONFLICT (attribute_id, validation_name) DO UPDATE
2025-05-12 14:21:55,301 - fix_entity_deployer - INFO -               SET validation_type = EXCLUDED.validation_type,
2025-05-12 14:21:55,301 - fix_entity_deployer - INFO -                   validation_expression = EXCLUDED.validation_expression,
2025-05-12 14:21:55,301 - fix_entity_deployer - INFO -                   error_message = EXCLUDED.error_message,
2025-05-12 14:21:55,301 - fix_entity_deployer - INFO -                   updated_at = NOW(),
2025-05-12 14:21:55,301 - fix_entity_deployer - INFO -                   updated_by = 'system'
2025-05-12 14:21:55,301 - fix_entity_deployer - INFO -               
2025-05-12 14:21:55,301 - fix_entity_deployer - INFO -   Params: ('E1.At10', 'status_must_be_active_for_performance_reviews', 'expression', 'Employee.status must be Active to receive performance reviews', 'Employee status must be Active to receive performance reviews')
2025-05-12 14:21:55,301 - fix_entity_deployer - INFO -   SQL: 
2025-05-12 14:21:55,301 - fix_entity_deployer - INFO -               INSERT INTO workflow_temp.attribute_validations (
2025-05-12 14:21:55,301 - fix_entity_deployer - INFO -                   attribute_id, validation_name, validation_type, validation_expression, error_message,
2025-05-12 14:21:55,301 - fix_entity_deployer - INFO -                   created_at, created_by, updated_at, updated_by
2025-05-12 14:21:55,301 - fix_entity_deployer - INFO -               ) VALUES (%s, %s, %s, %s, %s, NOW(), 'system', NOW(), 'system')
2025-05-12 14:21:55,301 - fix_entity_deployer - INFO -               ON CONFLICT (attribute_id, validation_name) DO UPDATE
2025-05-12 14:21:55,301 - fix_entity_deployer - INFO -               SET validation_type = EXCLUDED.validation_type,
2025-05-12 14:21:55,301 - fix_entity_deployer - INFO -                   validation_expression = EXCLUDED.validation_expression,
2025-05-12 14:21:55,301 - fix_entity_deployer - INFO -                   error_message = EXCLUDED.error_message,
2025-05-12 14:21:55,301 - fix_entity_deployer - INFO -                   updated_at = NOW(),
2025-05-12 14:21:55,301 - fix_entity_deployer - INFO -                   updated_by = 'system'
2025-05-12 14:21:55,301 - fix_entity_deployer - INFO -               
2025-05-12 14:21:55,301 - fix_entity_deployer - INFO -   Params: ('E1.At9', 'hireDate_at_least_90_days_before_performance_rating', 'expression', 'Employee.hireDate must be at least 90 days before Employee.performanceRating can be set', 'Employee must be hired at least 90 days before setting performance rating')
2025-05-12 14:21:55,301 - fix_entity_deployer - INFO -   SQL: 
2025-05-12 14:21:55,301 - fix_entity_deployer - INFO -               INSERT INTO workflow_temp.attribute_validations (
2025-05-12 14:21:55,301 - fix_entity_deployer - INFO -                   attribute_id, validation_name, validation_type, validation_expression, error_message,
2025-05-12 14:21:55,301 - fix_entity_deployer - INFO -                   created_at, created_by, updated_at, updated_by
2025-05-12 14:21:55,301 - fix_entity_deployer - INFO -               ) VALUES (%s, %s, %s, %s, %s, NOW(), 'system', NOW(), 'system')
2025-05-12 14:21:55,301 - fix_entity_deployer - INFO -               ON CONFLICT (attribute_id, validation_name) DO UPDATE
2025-05-12 14:21:55,301 - fix_entity_deployer - INFO -               SET validation_type = EXCLUDED.validation_type,
2025-05-12 14:21:55,301 - fix_entity_deployer - INFO -                   validation_expression = EXCLUDED.validation_expression,
2025-05-12 14:21:55,301 - fix_entity_deployer - INFO -                   error_message = EXCLUDED.error_message,
2025-05-12 14:21:55,301 - fix_entity_deployer - INFO -                   updated_at = NOW(),
2025-05-12 14:21:55,301 - fix_entity_deployer - INFO -                   updated_by = 'system'
2025-05-12 14:21:55,301 - fix_entity_deployer - INFO -               
2025-05-12 14:21:55,301 - fix_entity_deployer - INFO -   Params: ('E2.At5', 'budget_approved_by_finance_before_changes', 'expression', 'Department.budget must be approved by Finance before changes', 'Department budget must be approved by Finance before changes')
2025-05-12 14:21:55,301 - fix_entity_deployer - INFO -   SQL: 
2025-05-12 14:21:55,301 - fix_entity_deployer - INFO -               INSERT INTO workflow_temp.attribute_validations (
2025-05-12 14:21:55,301 - fix_entity_deployer - INFO -                   attribute_id, validation_name, validation_type, validation_expression, error_message,
2025-05-12 14:21:55,301 - fix_entity_deployer - INFO -                   created_at, created_by, updated_at, updated_by
2025-05-12 14:21:55,301 - fix_entity_deployer - INFO -               ) VALUES (%s, %s, %s, %s, %s, NOW(), 'system', NOW(), 'system')
2025-05-12 14:21:55,301 - fix_entity_deployer - INFO -               ON CONFLICT (attribute_id, validation_name) DO UPDATE
2025-05-12 14:21:55,301 - fix_entity_deployer - INFO -               SET validation_type = EXCLUDED.validation_type,
2025-05-12 14:21:55,301 - fix_entity_deployer - INFO -                   validation_expression = EXCLUDED.validation_expression,
2025-05-12 14:21:55,301 - fix_entity_deployer - INFO -                   error_message = EXCLUDED.error_message,
2025-05-12 14:21:55,301 - fix_entity_deployer - INFO -                   updated_at = NOW(),
2025-05-12 14:21:55,301 - fix_entity_deployer - INFO -                   updated_by = 'system'
2025-05-12 14:21:55,301 - fix_entity_deployer - INFO -               
2025-05-12 14:21:55,301 - fix_entity_deployer - INFO -   Params: ('E2.At3', 'manager_must_be_active', 'expression', "Department.managerId must reference an Employee with status = 'Active'", 'Department manager must be an active employee')
2025-05-12 14:21:55,301 - fix_entity_deployer - INFO -   All fixes completed successfully. See implement_missing_features.log for details.
2025-05-12 14:21:55,301 - fix_entity_deployer - INFO - === Verifying fixes ===
2025-05-12 14:21:55,301 - fix_entity_deployer - INFO - Verifying fixes
2025-05-12 14:21:55,301 - fix_entity_deployer - INFO - Checking entity IDs
2025-05-12 14:21:55,308 - db_connection - INFO - Connected to database workflow_system on 172.18.0.5:5432
2025-05-12 14:21:55,309 - fix_entity_deployer - INFO - All entity IDs use sequential IDs (E1, E2)
2025-05-12 14:21:55,309 - fix_entity_deployer - INFO - Checking enum values
2025-05-12 14:21:55,315 - db_connection - INFO - Connected to database workflow_system on 172.18.0.5:5432
2025-05-12 14:21:55,317 - fix_entity_deployer - INFO - No enum attributes found
2025-05-12 14:21:55,317 - fix_entity_deployer - INFO - Checking validations
2025-05-12 14:21:55,323 - db_connection - INFO - Connected to database workflow_system on 172.18.0.5:5432
2025-05-12 14:21:55,324 - fix_entity_deployer - INFO - Found 7 validations
2025-05-12 14:21:55,324 - fix_entity_deployer - INFO - Checking calculated fields
2025-05-12 14:21:55,330 - db_connection - INFO - Connected to database workflow_system on 172.18.0.5:5432
2025-05-12 14:21:55,331 - fix_entity_deployer - INFO - Found 1 calculated fields
2025-05-12 14:21:55,331 - fix_entity_deployer - INFO - Checking lifecycle management
2025-05-12 14:21:55,336 - db_connection - INFO - Connected to database workflow_system on 172.18.0.5:5432
2025-05-12 14:21:55,337 - fix_entity_deployer - INFO - Found 3 lifecycle management entries
2025-05-12 14:21:55,337 - fix_entity_deployer - INFO - Checking for duplicate foreign key constraints
2025-05-12 14:21:55,343 - db_connection - INFO - Connected to database workflow_system on 172.18.0.5:5432
2025-05-12 14:21:55,412 - fix_entity_deployer - INFO - No duplicate foreign key constraints found
2025-05-12 14:21:55,412 - fix_entity_deployer - INFO - Verification completed
2025-05-12 14:21:55,412 - fix_entity_deployer - INFO - All entity IDs use sequential IDs (E1, E2)
2025-05-12 14:21:55,412 - fix_entity_deployer - INFO - No enum attributes found
2025-05-12 14:21:55,412 - fix_entity_deployer - INFO - Found 7 validations
2025-05-12 14:21:55,412 - fix_entity_deployer - INFO - Found 1 calculated fields
2025-05-12 14:21:55,412 - fix_entity_deployer - INFO - Found 3 lifecycle management entries
2025-05-12 14:21:55,412 - fix_entity_deployer - INFO - No duplicate foreign key constraints found
2025-05-12 14:21:55,412 - fix_entity_deployer - INFO - Verification completed
2025-05-12 14:21:55,412 - fix_entity_deployer - INFO - === Running test_entity_deployer_v2.py ===
2025-05-12 14:21:55,412 - fix_entity_deployer - INFO - Running test_entity_deployer_v2.py
2025-05-12 14:21:57,251 - fix_entity_deployer - INFO - test_entity_deployer_v2.py ran successfully
2025-05-12 14:21:57,251 - fix_entity_deployer - INFO -   Deployment successful: True
2025-05-12 14:21:57,251 - fix_entity_deployer - INFO -   See test_entity_deployer_v2.log for detailed information
2025-05-12 14:21:57,251 - fix_entity_deployer - INFO - test_entity_deployer_v2.py ran successfully
2025-05-12 14:21:57,251 - fix_entity_deployer - INFO -   Deployment successful: True
2025-05-12 14:21:57,252 - fix_entity_deployer - INFO -   See test_entity_deployer_v2.log for detailed information
2025-05-12 14:21:57,252 - fix_entity_deployer - INFO - All fixes completed successfully
2025-05-12 15:06:20,829 - fix_entity_deployer - INFO - Starting to fix entity deployer
2025-05-12 15:06:20,829 - fix_entity_deployer - INFO - === Backing up entity_deployer.py ===
2025-05-12 15:06:20,829 - fix_entity_deployer - INFO - Backing up entity_deployer.py
2025-05-12 15:06:20,829 - fix_entity_deployer - INFO - Backed up entity_deployer.py to /home/<USER>/workflow-system/chat-yaml-builder/v2/deployers/entity_deployer.py.bak
2025-05-12 15:06:20,829 - fix_entity_deployer - INFO - Backed up entity_deployer.py to /home/<USER>/workflow-system/chat-yaml-builder/v2/deployers/entity_deployer.py.bak
2025-05-12 15:06:20,829 - fix_entity_deployer - INFO - === Replacing entity_deployer.py with entity_deployer_v2.py ===
2025-05-12 15:06:20,829 - fix_entity_deployer - INFO - Replacing entity_deployer.py with entity_deployer_v2.py
2025-05-12 15:06:20,830 - fix_entity_deployer - INFO - Replaced entity_deployer.py with entity_deployer_v2.py
2025-05-12 15:06:20,830 - fix_entity_deployer - INFO - Replaced entity_deployer.py with entity_deployer_v2.py
2025-05-12 15:06:20,830 - fix_entity_deployer - INFO - === Running implement_missing_features.py ===
2025-05-12 15:06:20,830 - fix_entity_deployer - INFO - Running implement_missing_features.py
2025-05-12 15:06:21,216 - fix_entity_deployer - INFO - implement_missing_features.py ran successfully
2025-05-12 15:06:21,217 - fix_entity_deployer - INFO -   SQL: 
2025-05-12 15:06:21,217 - fix_entity_deployer - INFO -                           INSERT INTO workflow_temp.attribute_validations (
2025-05-12 15:06:21,217 - fix_entity_deployer - INFO -                               attribute_id, validation_name, validation_type, validation_expression, error_message,
2025-05-12 15:06:21,217 - fix_entity_deployer - INFO -                               created_at, created_by, updated_at, updated_by
2025-05-12 15:06:21,217 - fix_entity_deployer - INFO -                           ) VALUES (%s, %s, %s, %s, %s, NOW(), 'system', NOW(), 'system')
2025-05-12 15:06:21,217 - fix_entity_deployer - INFO -                           ON CONFLICT (attribute_id, validation_name) DO UPDATE
2025-05-12 15:06:21,217 - fix_entity_deployer - INFO -                           SET validation_type = EXCLUDED.validation_type,
2025-05-12 15:06:21,217 - fix_entity_deployer - INFO -                               validation_expression = EXCLUDED.validation_expression,
2025-05-12 15:06:21,217 - fix_entity_deployer - INFO -                               error_message = EXCLUDED.error_message,
2025-05-12 15:06:21,217 - fix_entity_deployer - INFO -                               updated_at = NOW(),
2025-05-12 15:06:21,217 - fix_entity_deployer - INFO -                               updated_by = 'system'
2025-05-12 15:06:21,217 - fix_entity_deployer - INFO -                           
2025-05-12 15:06:21,217 - fix_entity_deployer - INFO -   Params: ('E1.At9', 'hireDate_be_at_least_90_days_before_Emp', 'expression', 'be at least 90 days before Employee.performanceRating can be set', 'Invalid value for hireDate: be at least 90 days before Employee.performanceRating can be set')
2025-05-12 15:06:21,217 - fix_entity_deployer - INFO -   SQL: 
2025-05-12 15:06:21,217 - fix_entity_deployer - INFO -                           INSERT INTO workflow_temp.attribute_validations (
2025-05-12 15:06:21,217 - fix_entity_deployer - INFO -                               attribute_id, validation_name, validation_type, validation_expression, error_message,
2025-05-12 15:06:21,217 - fix_entity_deployer - INFO -                               created_at, created_by, updated_at, updated_by
2025-05-12 15:06:21,217 - fix_entity_deployer - INFO -                           ) VALUES (%s, %s, %s, %s, %s, NOW(), 'system', NOW(), 'system')
2025-05-12 15:06:21,218 - fix_entity_deployer - INFO -                           ON CONFLICT (attribute_id, validation_name) DO UPDATE
2025-05-12 15:06:21,218 - fix_entity_deployer - INFO -                           SET validation_type = EXCLUDED.validation_type,
2025-05-12 15:06:21,218 - fix_entity_deployer - INFO -                               validation_expression = EXCLUDED.validation_expression,
2025-05-12 15:06:21,218 - fix_entity_deployer - INFO -                               error_message = EXCLUDED.error_message,
2025-05-12 15:06:21,218 - fix_entity_deployer - INFO -                               updated_at = NOW(),
2025-05-12 15:06:21,218 - fix_entity_deployer - INFO -                               updated_by = 'system'
2025-05-12 15:06:21,218 - fix_entity_deployer - INFO -                           
2025-05-12 15:06:21,218 - fix_entity_deployer - INFO -   Params: ('E2.At5', 'budget_be_approved_by_Finance_before_', 'expression', 'be approved by Finance before changes', 'Invalid value for budget: be approved by Finance before changes')
2025-05-12 15:06:21,218 - fix_entity_deployer - INFO -   SQL: 
2025-05-12 15:06:21,218 - fix_entity_deployer - INFO -                           INSERT INTO workflow_temp.attribute_validations (
2025-05-12 15:06:21,218 - fix_entity_deployer - INFO -                               attribute_id, validation_name, validation_type, validation_expression, error_message,
2025-05-12 15:06:21,218 - fix_entity_deployer - INFO -                               created_at, created_by, updated_at, updated_by
2025-05-12 15:06:21,218 - fix_entity_deployer - INFO -                           ) VALUES (%s, %s, %s, %s, %s, NOW(), 'system', NOW(), 'system')
2025-05-12 15:06:21,218 - fix_entity_deployer - INFO -                           ON CONFLICT (attribute_id, validation_name) DO UPDATE
2025-05-12 15:06:21,218 - fix_entity_deployer - INFO -                           SET validation_type = EXCLUDED.validation_type,
2025-05-12 15:06:21,218 - fix_entity_deployer - INFO -                               validation_expression = EXCLUDED.validation_expression,
2025-05-12 15:06:21,218 - fix_entity_deployer - INFO -                               error_message = EXCLUDED.error_message,
2025-05-12 15:06:21,218 - fix_entity_deployer - INFO -                               updated_at = NOW(),
2025-05-12 15:06:21,218 - fix_entity_deployer - INFO -                               updated_by = 'system'
2025-05-12 15:06:21,218 - fix_entity_deployer - INFO -                           
2025-05-12 15:06:21,218 - fix_entity_deployer - INFO -   Params: ('E2.At3', 'managerId_reference_an_Employee_with_sta', 'expression', "reference an Employee with status = 'Active'", "Invalid value for managerId: reference an Employee with status = 'Active'")
2025-05-12 15:06:21,218 - fix_entity_deployer - INFO -   SQL: 
2025-05-12 15:06:21,218 - fix_entity_deployer - INFO -               INSERT INTO workflow_temp.attribute_validations (
2025-05-12 15:06:21,218 - fix_entity_deployer - INFO -                   attribute_id, validation_name, validation_type, validation_expression, error_message,
2025-05-12 15:06:21,218 - fix_entity_deployer - INFO -                   created_at, created_by, updated_at, updated_by
2025-05-12 15:06:21,218 - fix_entity_deployer - INFO -               ) VALUES (%s, %s, %s, %s, %s, NOW(), 'system', NOW(), 'system')
2025-05-12 15:06:21,218 - fix_entity_deployer - INFO -               ON CONFLICT (attribute_id, validation_name) DO UPDATE
2025-05-12 15:06:21,218 - fix_entity_deployer - INFO -               SET validation_type = EXCLUDED.validation_type,
2025-05-12 15:06:21,218 - fix_entity_deployer - INFO -                   validation_expression = EXCLUDED.validation_expression,
2025-05-12 15:06:21,218 - fix_entity_deployer - INFO -                   error_message = EXCLUDED.error_message,
2025-05-12 15:06:21,218 - fix_entity_deployer - INFO -                   updated_at = NOW(),
2025-05-12 15:06:21,218 - fix_entity_deployer - INFO -                   updated_by = 'system'
2025-05-12 15:06:21,218 - fix_entity_deployer - INFO -               
2025-05-12 15:06:21,218 - fix_entity_deployer - INFO -   Params: ('E1.At10', 'status_must_be_active_for_performance_reviews', 'expression', 'Employee.status must be Active to receive performance reviews', 'Employee status must be Active to receive performance reviews')
2025-05-12 15:06:21,218 - fix_entity_deployer - INFO -   SQL: 
2025-05-12 15:06:21,218 - fix_entity_deployer - INFO -               INSERT INTO workflow_temp.attribute_validations (
2025-05-12 15:06:21,218 - fix_entity_deployer - INFO -                   attribute_id, validation_name, validation_type, validation_expression, error_message,
2025-05-12 15:06:21,218 - fix_entity_deployer - INFO -                   created_at, created_by, updated_at, updated_by
2025-05-12 15:06:21,218 - fix_entity_deployer - INFO -               ) VALUES (%s, %s, %s, %s, %s, NOW(), 'system', NOW(), 'system')
2025-05-12 15:06:21,218 - fix_entity_deployer - INFO -               ON CONFLICT (attribute_id, validation_name) DO UPDATE
2025-05-12 15:06:21,218 - fix_entity_deployer - INFO -               SET validation_type = EXCLUDED.validation_type,
2025-05-12 15:06:21,218 - fix_entity_deployer - INFO -                   validation_expression = EXCLUDED.validation_expression,
2025-05-12 15:06:21,218 - fix_entity_deployer - INFO -                   error_message = EXCLUDED.error_message,
2025-05-12 15:06:21,218 - fix_entity_deployer - INFO -                   updated_at = NOW(),
2025-05-12 15:06:21,218 - fix_entity_deployer - INFO -                   updated_by = 'system'
2025-05-12 15:06:21,218 - fix_entity_deployer - INFO -               
2025-05-12 15:06:21,218 - fix_entity_deployer - INFO -   Params: ('E1.At9', 'hireDate_at_least_90_days_before_performance_rating', 'expression', 'Employee.hireDate must be at least 90 days before Employee.performanceRating can be set', 'Employee must be hired at least 90 days before setting performance rating')
2025-05-12 15:06:21,218 - fix_entity_deployer - INFO -   SQL: 
2025-05-12 15:06:21,218 - fix_entity_deployer - INFO -               INSERT INTO workflow_temp.attribute_validations (
2025-05-12 15:06:21,218 - fix_entity_deployer - INFO -                   attribute_id, validation_name, validation_type, validation_expression, error_message,
2025-05-12 15:06:21,218 - fix_entity_deployer - INFO -                   created_at, created_by, updated_at, updated_by
2025-05-12 15:06:21,219 - fix_entity_deployer - INFO -               ) VALUES (%s, %s, %s, %s, %s, NOW(), 'system', NOW(), 'system')
2025-05-12 15:06:21,219 - fix_entity_deployer - INFO -               ON CONFLICT (attribute_id, validation_name) DO UPDATE
2025-05-12 15:06:21,219 - fix_entity_deployer - INFO -               SET validation_type = EXCLUDED.validation_type,
2025-05-12 15:06:21,219 - fix_entity_deployer - INFO -                   validation_expression = EXCLUDED.validation_expression,
2025-05-12 15:06:21,219 - fix_entity_deployer - INFO -                   error_message = EXCLUDED.error_message,
2025-05-12 15:06:21,219 - fix_entity_deployer - INFO -                   updated_at = NOW(),
2025-05-12 15:06:21,219 - fix_entity_deployer - INFO -                   updated_by = 'system'
2025-05-12 15:06:21,219 - fix_entity_deployer - INFO -               
2025-05-12 15:06:21,219 - fix_entity_deployer - INFO -   Params: ('E2.At5', 'budget_approved_by_finance_before_changes', 'expression', 'Department.budget must be approved by Finance before changes', 'Department budget must be approved by Finance before changes')
2025-05-12 15:06:21,219 - fix_entity_deployer - INFO -   SQL: 
2025-05-12 15:06:21,219 - fix_entity_deployer - INFO -               INSERT INTO workflow_temp.attribute_validations (
2025-05-12 15:06:21,219 - fix_entity_deployer - INFO -                   attribute_id, validation_name, validation_type, validation_expression, error_message,
2025-05-12 15:06:21,219 - fix_entity_deployer - INFO -                   created_at, created_by, updated_at, updated_by
2025-05-12 15:06:21,219 - fix_entity_deployer - INFO -               ) VALUES (%s, %s, %s, %s, %s, NOW(), 'system', NOW(), 'system')
2025-05-12 15:06:21,219 - fix_entity_deployer - INFO -               ON CONFLICT (attribute_id, validation_name) DO UPDATE
2025-05-12 15:06:21,219 - fix_entity_deployer - INFO -               SET validation_type = EXCLUDED.validation_type,
2025-05-12 15:06:21,219 - fix_entity_deployer - INFO -                   validation_expression = EXCLUDED.validation_expression,
2025-05-12 15:06:21,219 - fix_entity_deployer - INFO -                   error_message = EXCLUDED.error_message,
2025-05-12 15:06:21,219 - fix_entity_deployer - INFO -                   updated_at = NOW(),
2025-05-12 15:06:21,219 - fix_entity_deployer - INFO -                   updated_by = 'system'
2025-05-12 15:06:21,219 - fix_entity_deployer - INFO -               
2025-05-12 15:06:21,219 - fix_entity_deployer - INFO -   Params: ('E2.At3', 'manager_must_be_active', 'expression', "Department.managerId must reference an Employee with status = 'Active'", 'Department manager must be an active employee')
2025-05-12 15:06:21,219 - fix_entity_deployer - INFO -   All fixes completed successfully. See implement_missing_features.log for details.
2025-05-12 15:06:21,219 - fix_entity_deployer - INFO - implement_missing_features.py ran successfully
2025-05-12 15:06:21,219 - fix_entity_deployer - INFO -   SQL: 
2025-05-12 15:06:21,219 - fix_entity_deployer - INFO -                           INSERT INTO workflow_temp.attribute_validations (
2025-05-12 15:06:21,219 - fix_entity_deployer - INFO -                               attribute_id, validation_name, validation_type, validation_expression, error_message,
2025-05-12 15:06:21,219 - fix_entity_deployer - INFO -                               created_at, created_by, updated_at, updated_by
2025-05-12 15:06:21,219 - fix_entity_deployer - INFO -                           ) VALUES (%s, %s, %s, %s, %s, NOW(), 'system', NOW(), 'system')
2025-05-12 15:06:21,219 - fix_entity_deployer - INFO -                           ON CONFLICT (attribute_id, validation_name) DO UPDATE
2025-05-12 15:06:21,219 - fix_entity_deployer - INFO -                           SET validation_type = EXCLUDED.validation_type,
2025-05-12 15:06:21,219 - fix_entity_deployer - INFO -                               validation_expression = EXCLUDED.validation_expression,
2025-05-12 15:06:21,219 - fix_entity_deployer - INFO -                               error_message = EXCLUDED.error_message,
2025-05-12 15:06:21,219 - fix_entity_deployer - INFO -                               updated_at = NOW(),
2025-05-12 15:06:21,219 - fix_entity_deployer - INFO -                               updated_by = 'system'
2025-05-12 15:06:21,219 - fix_entity_deployer - INFO -                           
2025-05-12 15:06:21,219 - fix_entity_deployer - INFO -   Params: ('E1.At9', 'hireDate_be_at_least_90_days_before_Emp', 'expression', 'be at least 90 days before Employee.performanceRating can be set', 'Invalid value for hireDate: be at least 90 days before Employee.performanceRating can be set')
2025-05-12 15:06:21,219 - fix_entity_deployer - INFO -   SQL: 
2025-05-12 15:06:21,219 - fix_entity_deployer - INFO -                           INSERT INTO workflow_temp.attribute_validations (
2025-05-12 15:06:21,219 - fix_entity_deployer - INFO -                               attribute_id, validation_name, validation_type, validation_expression, error_message,
2025-05-12 15:06:21,219 - fix_entity_deployer - INFO -                               created_at, created_by, updated_at, updated_by
2025-05-12 15:06:21,219 - fix_entity_deployer - INFO -                           ) VALUES (%s, %s, %s, %s, %s, NOW(), 'system', NOW(), 'system')
2025-05-12 15:06:21,219 - fix_entity_deployer - INFO -                           ON CONFLICT (attribute_id, validation_name) DO UPDATE
2025-05-12 15:06:21,219 - fix_entity_deployer - INFO -                           SET validation_type = EXCLUDED.validation_type,
2025-05-12 15:06:21,219 - fix_entity_deployer - INFO -                               validation_expression = EXCLUDED.validation_expression,
2025-05-12 15:06:21,219 - fix_entity_deployer - INFO -                               error_message = EXCLUDED.error_message,
2025-05-12 15:06:21,219 - fix_entity_deployer - INFO -                               updated_at = NOW(),
2025-05-12 15:06:21,219 - fix_entity_deployer - INFO -                               updated_by = 'system'
2025-05-12 15:06:21,219 - fix_entity_deployer - INFO -                           
2025-05-12 15:06:21,219 - fix_entity_deployer - INFO -   Params: ('E2.At5', 'budget_be_approved_by_Finance_before_', 'expression', 'be approved by Finance before changes', 'Invalid value for budget: be approved by Finance before changes')
2025-05-12 15:06:21,219 - fix_entity_deployer - INFO -   SQL: 
2025-05-12 15:06:21,219 - fix_entity_deployer - INFO -                           INSERT INTO workflow_temp.attribute_validations (
2025-05-12 15:06:21,220 - fix_entity_deployer - INFO -                               attribute_id, validation_name, validation_type, validation_expression, error_message,
2025-05-12 15:06:21,220 - fix_entity_deployer - INFO -                               created_at, created_by, updated_at, updated_by
2025-05-12 15:06:21,220 - fix_entity_deployer - INFO -                           ) VALUES (%s, %s, %s, %s, %s, NOW(), 'system', NOW(), 'system')
2025-05-12 15:06:21,220 - fix_entity_deployer - INFO -                           ON CONFLICT (attribute_id, validation_name) DO UPDATE
2025-05-12 15:06:21,220 - fix_entity_deployer - INFO -                           SET validation_type = EXCLUDED.validation_type,
2025-05-12 15:06:21,220 - fix_entity_deployer - INFO -                               validation_expression = EXCLUDED.validation_expression,
2025-05-12 15:06:21,220 - fix_entity_deployer - INFO -                               error_message = EXCLUDED.error_message,
2025-05-12 15:06:21,220 - fix_entity_deployer - INFO -                               updated_at = NOW(),
2025-05-12 15:06:21,220 - fix_entity_deployer - INFO -                               updated_by = 'system'
2025-05-12 15:06:21,220 - fix_entity_deployer - INFO -                           
2025-05-12 15:06:21,220 - fix_entity_deployer - INFO -   Params: ('E2.At3', 'managerId_reference_an_Employee_with_sta', 'expression', "reference an Employee with status = 'Active'", "Invalid value for managerId: reference an Employee with status = 'Active'")
2025-05-12 15:06:21,220 - fix_entity_deployer - INFO -   SQL: 
2025-05-12 15:06:21,220 - fix_entity_deployer - INFO -               INSERT INTO workflow_temp.attribute_validations (
2025-05-12 15:06:21,220 - fix_entity_deployer - INFO -                   attribute_id, validation_name, validation_type, validation_expression, error_message,
2025-05-12 15:06:21,220 - fix_entity_deployer - INFO -                   created_at, created_by, updated_at, updated_by
2025-05-12 15:06:21,220 - fix_entity_deployer - INFO -               ) VALUES (%s, %s, %s, %s, %s, NOW(), 'system', NOW(), 'system')
2025-05-12 15:06:21,220 - fix_entity_deployer - INFO -               ON CONFLICT (attribute_id, validation_name) DO UPDATE
2025-05-12 15:06:21,220 - fix_entity_deployer - INFO -               SET validation_type = EXCLUDED.validation_type,
2025-05-12 15:06:21,220 - fix_entity_deployer - INFO -                   validation_expression = EXCLUDED.validation_expression,
2025-05-12 15:06:21,220 - fix_entity_deployer - INFO -                   error_message = EXCLUDED.error_message,
2025-05-12 15:06:21,220 - fix_entity_deployer - INFO -                   updated_at = NOW(),
2025-05-12 15:06:21,220 - fix_entity_deployer - INFO -                   updated_by = 'system'
2025-05-12 15:06:21,220 - fix_entity_deployer - INFO -               
2025-05-12 15:06:21,220 - fix_entity_deployer - INFO -   Params: ('E1.At10', 'status_must_be_active_for_performance_reviews', 'expression', 'Employee.status must be Active to receive performance reviews', 'Employee status must be Active to receive performance reviews')
2025-05-12 15:06:21,220 - fix_entity_deployer - INFO -   SQL: 
2025-05-12 15:06:21,220 - fix_entity_deployer - INFO -               INSERT INTO workflow_temp.attribute_validations (
2025-05-12 15:06:21,220 - fix_entity_deployer - INFO -                   attribute_id, validation_name, validation_type, validation_expression, error_message,
2025-05-12 15:06:21,220 - fix_entity_deployer - INFO -                   created_at, created_by, updated_at, updated_by
2025-05-12 15:06:21,220 - fix_entity_deployer - INFO -               ) VALUES (%s, %s, %s, %s, %s, NOW(), 'system', NOW(), 'system')
2025-05-12 15:06:21,220 - fix_entity_deployer - INFO -               ON CONFLICT (attribute_id, validation_name) DO UPDATE
2025-05-12 15:06:21,220 - fix_entity_deployer - INFO -               SET validation_type = EXCLUDED.validation_type,
2025-05-12 15:06:21,220 - fix_entity_deployer - INFO -                   validation_expression = EXCLUDED.validation_expression,
2025-05-12 15:06:21,220 - fix_entity_deployer - INFO -                   error_message = EXCLUDED.error_message,
2025-05-12 15:06:21,220 - fix_entity_deployer - INFO -                   updated_at = NOW(),
2025-05-12 15:06:21,220 - fix_entity_deployer - INFO -                   updated_by = 'system'
2025-05-12 15:06:21,220 - fix_entity_deployer - INFO -               
2025-05-12 15:06:21,220 - fix_entity_deployer - INFO -   Params: ('E1.At9', 'hireDate_at_least_90_days_before_performance_rating', 'expression', 'Employee.hireDate must be at least 90 days before Employee.performanceRating can be set', 'Employee must be hired at least 90 days before setting performance rating')
2025-05-12 15:06:21,220 - fix_entity_deployer - INFO -   SQL: 
2025-05-12 15:06:21,220 - fix_entity_deployer - INFO -               INSERT INTO workflow_temp.attribute_validations (
2025-05-12 15:06:21,220 - fix_entity_deployer - INFO -                   attribute_id, validation_name, validation_type, validation_expression, error_message,
2025-05-12 15:06:21,220 - fix_entity_deployer - INFO -                   created_at, created_by, updated_at, updated_by
2025-05-12 15:06:21,220 - fix_entity_deployer - INFO -               ) VALUES (%s, %s, %s, %s, %s, NOW(), 'system', NOW(), 'system')
2025-05-12 15:06:21,220 - fix_entity_deployer - INFO -               ON CONFLICT (attribute_id, validation_name) DO UPDATE
2025-05-12 15:06:21,220 - fix_entity_deployer - INFO -               SET validation_type = EXCLUDED.validation_type,
2025-05-12 15:06:21,220 - fix_entity_deployer - INFO -                   validation_expression = EXCLUDED.validation_expression,
2025-05-12 15:06:21,220 - fix_entity_deployer - INFO -                   error_message = EXCLUDED.error_message,
2025-05-12 15:06:21,220 - fix_entity_deployer - INFO -                   updated_at = NOW(),
2025-05-12 15:06:21,220 - fix_entity_deployer - INFO -                   updated_by = 'system'
2025-05-12 15:06:21,220 - fix_entity_deployer - INFO -               
2025-05-12 15:06:21,220 - fix_entity_deployer - INFO -   Params: ('E2.At5', 'budget_approved_by_finance_before_changes', 'expression', 'Department.budget must be approved by Finance before changes', 'Department budget must be approved by Finance before changes')
2025-05-12 15:06:21,220 - fix_entity_deployer - INFO -   SQL: 
2025-05-12 15:06:21,220 - fix_entity_deployer - INFO -               INSERT INTO workflow_temp.attribute_validations (
2025-05-12 15:06:21,221 - fix_entity_deployer - INFO -                   attribute_id, validation_name, validation_type, validation_expression, error_message,
2025-05-12 15:06:21,221 - fix_entity_deployer - INFO -                   created_at, created_by, updated_at, updated_by
2025-05-12 15:06:21,221 - fix_entity_deployer - INFO -               ) VALUES (%s, %s, %s, %s, %s, NOW(), 'system', NOW(), 'system')
2025-05-12 15:06:21,221 - fix_entity_deployer - INFO -               ON CONFLICT (attribute_id, validation_name) DO UPDATE
2025-05-12 15:06:21,221 - fix_entity_deployer - INFO -               SET validation_type = EXCLUDED.validation_type,
2025-05-12 15:06:21,221 - fix_entity_deployer - INFO -                   validation_expression = EXCLUDED.validation_expression,
2025-05-12 15:06:21,221 - fix_entity_deployer - INFO -                   error_message = EXCLUDED.error_message,
2025-05-12 15:06:21,221 - fix_entity_deployer - INFO -                   updated_at = NOW(),
2025-05-12 15:06:21,221 - fix_entity_deployer - INFO -                   updated_by = 'system'
2025-05-12 15:06:21,221 - fix_entity_deployer - INFO -               
2025-05-12 15:06:21,221 - fix_entity_deployer - INFO -   Params: ('E2.At3', 'manager_must_be_active', 'expression', "Department.managerId must reference an Employee with status = 'Active'", 'Department manager must be an active employee')
2025-05-12 15:06:21,221 - fix_entity_deployer - INFO -   All fixes completed successfully. See implement_missing_features.log for details.
2025-05-12 15:06:21,221 - fix_entity_deployer - INFO - === Verifying fixes ===
2025-05-12 15:06:21,221 - fix_entity_deployer - INFO - Verifying fixes
2025-05-12 15:06:21,221 - fix_entity_deployer - INFO - Checking entity IDs
2025-05-12 15:06:21,225 - db_connection - INFO - Connected to database workflow_system on 172.18.0.5:5432
2025-05-12 15:06:21,226 - fix_entity_deployer - INFO - All entity IDs use sequential IDs (E1, E2)
2025-05-12 15:06:21,226 - fix_entity_deployer - INFO - Checking enum values
2025-05-12 15:06:21,231 - db_connection - INFO - Connected to database workflow_system on 172.18.0.5:5432
2025-05-12 15:06:21,233 - fix_entity_deployer - INFO - No enum attributes found
2025-05-12 15:06:21,233 - fix_entity_deployer - INFO - Checking validations
2025-05-12 15:06:21,237 - db_connection - INFO - Connected to database workflow_system on 172.18.0.5:5432
2025-05-12 15:06:21,238 - fix_entity_deployer - INFO - Found 7 validations
2025-05-12 15:06:21,238 - fix_entity_deployer - INFO - Checking calculated fields
2025-05-12 15:06:21,242 - db_connection - INFO - Connected to database workflow_system on 172.18.0.5:5432
2025-05-12 15:06:21,243 - fix_entity_deployer - INFO - Found 1 calculated fields
2025-05-12 15:06:21,243 - fix_entity_deployer - INFO - Checking lifecycle management
2025-05-12 15:06:21,248 - db_connection - INFO - Connected to database workflow_system on 172.18.0.5:5432
2025-05-12 15:06:21,249 - fix_entity_deployer - INFO - Found 3 lifecycle management entries
2025-05-12 15:06:21,249 - fix_entity_deployer - INFO - Checking for duplicate foreign key constraints
2025-05-12 15:06:21,253 - db_connection - INFO - Connected to database workflow_system on 172.18.0.5:5432
2025-05-12 15:06:21,321 - fix_entity_deployer - INFO - No duplicate foreign key constraints found
2025-05-12 15:06:21,321 - fix_entity_deployer - INFO - Verification completed
2025-05-12 15:06:21,322 - fix_entity_deployer - INFO - All entity IDs use sequential IDs (E1, E2)
2025-05-12 15:06:21,322 - fix_entity_deployer - INFO - No enum attributes found
2025-05-12 15:06:21,322 - fix_entity_deployer - INFO - Found 7 validations
2025-05-12 15:06:21,322 - fix_entity_deployer - INFO - Found 1 calculated fields
2025-05-12 15:06:21,322 - fix_entity_deployer - INFO - Found 3 lifecycle management entries
2025-05-12 15:06:21,322 - fix_entity_deployer - INFO - No duplicate foreign key constraints found
2025-05-12 15:06:21,322 - fix_entity_deployer - INFO - Verification completed
2025-05-12 15:06:21,322 - fix_entity_deployer - INFO - === Running test_entity_deployer_v2.py ===
2025-05-12 15:06:21,322 - fix_entity_deployer - INFO - Running test_entity_deployer_v2.py
2025-05-12 15:06:22,152 - fix_entity_deployer - INFO - test_entity_deployer_v2.py ran successfully
2025-05-12 15:06:22,152 - fix_entity_deployer - INFO -   Deployment successful: False
2025-05-12 15:06:22,152 - fix_entity_deployer - INFO -   See test_entity_deployer_v2.log for detailed information
2025-05-12 15:06:22,152 - fix_entity_deployer - INFO - test_entity_deployer_v2.py ran successfully
2025-05-12 15:06:22,153 - fix_entity_deployer - INFO -   Deployment successful: False
2025-05-12 15:06:22,153 - fix_entity_deployer - INFO -   See test_entity_deployer_v2.log for detailed information
2025-05-12 15:06:22,153 - fix_entity_deployer - INFO - All fixes completed successfully
