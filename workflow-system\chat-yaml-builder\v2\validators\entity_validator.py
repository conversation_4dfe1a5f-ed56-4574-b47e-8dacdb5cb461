import re
import logging
from typing import Dict, List, Any
from db_utils import execute_query

# Set up logging
logger = logging.getLogger('entity_validator')

class EntityValidator:
    """
    Validator for entity definitions.
    """
    
    def __init__(self):
        pass
    
    def validate_entity(self, entity_name: str, entity_def: Dict[str, Any]) -> List[str]:
        """
        Validate an entity definition.
        
        Args:
            entity_name: Name of the entity
            entity_def: Entity definition
            
        Returns:
            List of validation error messages (empty if no errors)
        """
        validation_errors = []
        
        # Validate entity name
        if not entity_name:
            validation_errors.append("Entity name cannot be empty")
            return validation_errors
        
        if not re.match(r'^[A-Za-z][A-Za-z0-9_]*$', entity_name):
            validation_errors.append(f"Entity name '{entity_name}' must start with a letter and contain only letters, numbers, and underscores")
        
        # Validate attributes
        if 'attributes' not in entity_def:
            validation_errors.append(f"Entity '{entity_name}' has no attributes")
            return validation_errors
        
        if not entity_def['attributes']:
            validation_errors.append(f"Entity '{entity_name}' has empty attributes")
            return validation_errors
        
        # Check for primary key
        has_pk = False
        for attr_name, attr_def in entity_def['attributes'].items():
            if attr_def.get('primary_key', False):
                has_pk = True
                break
        
        if not has_pk:
            validation_errors.append(f"Entity '{entity_name}' has no primary key attribute")
        
        # Validate each attribute
        for attr_name, attr_def in entity_def['attributes'].items():
            attr_errors = self.validate_attribute(entity_name, attr_name, attr_def)
            validation_errors.extend(attr_errors)
        
        # Validate relationships
        if 'relationships' in entity_def:
            for rel_name, rel_def in entity_def['relationships'].items():
                rel_errors = self.validate_relationship(entity_name, rel_name, rel_def)
                validation_errors.extend(rel_errors)
        
        # Validate business rules
        if 'business_rules' in entity_def:
            for rule_name, rule_def in entity_def['business_rules'].items():
                rule_errors = self.validate_business_rule(entity_name, rule_name, rule_def)
                validation_errors.extend(rule_errors)
        
        # Validate calculated fields
        if 'calculated_fields' in entity_def:
            for field_name, field_def in entity_def['calculated_fields'].items():
                field_errors = self.validate_calculated_field(entity_name, field_name, field_def)
                validation_errors.extend(field_errors)
        
        return validation_errors
    
    def validate_attribute(self, entity_name: str, attr_name: str, attr_def: Dict[str, Any]) -> List[str]:
        """
        Validate an attribute definition.
        
        Args:
            entity_name: Name of the entity
            attr_name: Name of the attribute
            attr_def: Attribute definition
            
        Returns:
            List of validation error messages (empty if no errors)
        """
        validation_errors = []
        
        # Validate attribute name
        if not attr_name:
            validation_errors.append(f"Attribute name in entity '{entity_name}' cannot be empty")
            return validation_errors
        
        if not re.match(r'^[A-Za-z][A-Za-z0-9_]*$', attr_name):
            validation_errors.append(f"Attribute name '{attr_name}' in entity '{entity_name}' must start with a letter and contain only letters, numbers, and underscores")
        
        # Validate attribute type
        if 'type' not in attr_def:
            validation_errors.append(f"Attribute '{attr_name}' in entity '{entity_name}' has no type")
        else:
            valid_types = ['string', 'text', 'integer', 'float', 'boolean', 'date', 'datetime', 'time', 'json', 'enum']
            if attr_def['type'].lower() not in valid_types:
                validation_errors.append(f"Attribute '{attr_name}' in entity '{entity_name}' has invalid type '{attr_def['type']}'. Valid types are: {', '.join(valid_types)}")
        
        # Validate enum values
        if attr_def.get('type', '').lower() == 'enum' and 'enum_values' not in attr_def:
            validation_errors.append(f"Enum attribute '{attr_name}' in entity '{entity_name}' has no enum values")
        
        return validation_errors
    
    def validate_relationship(self, entity_name: str, rel_name: str, rel_def: Dict[str, Any]) -> List[str]:
        """
        Validate a relationship definition.
        
        Args:
            entity_name: Name of the entity
            rel_name: Name of the relationship
            rel_def: Relationship definition
            
        Returns:
            List of validation error messages (empty if no errors)
        """
        validation_errors = []
        
        # Validate relationship name
        if not rel_name:
            validation_errors.append(f"Relationship name in entity '{entity_name}' cannot be empty")
            return validation_errors
        
        # Validate required fields
        required_fields = ['entity', 'type', 'source_attribute', 'target_attribute']
        for field in required_fields:
            if field not in rel_def:
                validation_errors.append(f"Relationship '{rel_name}' in entity '{entity_name}' is missing required field '{field}'")
        
        # Validate relationship type
        if 'type' in rel_def:
            valid_types = ['one-to-one', 'one-to-many', 'many-to-one', 'many-to-many']
            if rel_def['type'].lower() not in valid_types:
                validation_errors.append(f"Relationship '{rel_name}' in entity '{entity_name}' has invalid type '{rel_def['type']}'. Valid types are: {', '.join(valid_types)}")
        
        # Check if target entity exists
        if 'entity' in rel_def:
            target_entity = rel_def['entity']
            success, messages, result = execute_query(
                """
                SELECT COUNT(*) FROM workflow_runtime.entities 
                WHERE name = %s
                """,
                (target_entity,)
            )
            
            if success and result and result[0][0] == 0:
                validation_errors.append(f"Relationship '{rel_name}' in entity '{entity_name}' references non-existent entity '{target_entity}'")
        
        return validation_errors
    
    def validate_business_rule(self, entity_name: str, rule_name: str, rule_def: Dict[str, Any]) -> List[str]:
        """
        Validate a business rule definition.
        
        Args:
            entity_name: Name of the entity
            rule_name: Name of the business rule
            rule_def: Business rule definition
            
        Returns:
            List of validation error messages (empty if no errors)
        """
        validation_errors = []
        
        # Validate business rule name
        if not rule_name:
            validation_errors.append(f"Business rule name in entity '{entity_name}' cannot be empty")
            return validation_errors
        
        # Validate conditions
        if 'conditions' not in rule_def or not rule_def['conditions']:
            validation_errors.append(f"Business rule '{rule_name}' in entity '{entity_name}' has no conditions")
        
        return validation_errors
    
    def validate_calculated_field(self, entity_name: str, field_name: str, field_def: Dict[str, Any]) -> List[str]:
        """
        Validate a calculated field definition.
        
        Args:
            entity_name: Name of the entity
            field_name: Name of the calculated field
            field_def: Calculated field definition
            
        Returns:
            List of validation error messages (empty if no errors)
        """
        validation_errors = []
        
        # Validate calculated field name
        if not field_name:
            validation_errors.append(f"Calculated field name in entity '{entity_name}' cannot be empty")
            return validation_errors
        
        # Validate required fields
        required_fields = ['attribute', 'formula']
        for field in required_fields:
            if field not in field_def:
                validation_errors.append(f"Calculated field '{field_name}' in entity '{entity_name}' is missing required field '{field}'")
        
        return validation_errors
    
    def validate_existing_entity(self, entity_id: str, entity_name: str) -> List[str]:
        """
        Validate if an entity with the given ID or name already exists.
        
        Args:
            entity_id: Entity ID to check
            entity_name: Entity name to check
            
        Returns:
            List of validation error messages (empty if no errors)
        """
        validation_errors = []
        
        # Check if entity ID exists
        success, messages, result = execute_query(
            """
            SELECT entity_id, name FROM workflow_runtime.entities 
            WHERE entity_id = %s OR name = %s
            """,
            (entity_id, entity_name)
        )
        
        if not success:
            validation_errors.append(f"Error checking if entity exists: {messages}")
            return validation_errors
        
        if result:
            for row in result:
                existing_id, existing_name = row
                if existing_id == entity_id:
                    validation_errors.append(f"Entity with ID '{entity_id}' already exists")
                if existing_name == entity_name:
                    validation_errors.append(f"Entity with name '{entity_name}' already exists")
        
        return validation_errors