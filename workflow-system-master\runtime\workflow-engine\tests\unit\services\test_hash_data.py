import unittest
import hashlib
from typing import Any
import sys
import os

# Add project root to path if needed
project_root = '/home/<USER>/workflow-system/runtime/workflow-engine'
if project_root not in sys.path:
    sys.path.insert(0, project_root)

# Import the function to test
from app.services.system_functions import hash_data

class TestHashData(unittest.TestCase):
    def test_md5_hash(self):
        """Test MD5 hashing algorithm with default parameters."""
        data = "test data"
        
        result = hash_data(data)
        
        # Verify against hashlib implementation
        expected = hashlib.md5(data.encode('utf-8')).hexdigest()
        self.assertEqual(result, expected)
    
    def test_sha1_hash(self):
        """Test SHA1 hashing algorithm."""
        data = "test data"
        
        result = hash_data(data, algorithm="sha1")
        
        # Verify against hashlib implementation
        expected = hashlib.sha1(data.encode('utf-8')).hexdigest()
        self.assertEqual(result, expected)
    
    def test_sha256_hash(self):
        """Test SHA256 hashing algorithm."""
        data = "test data"
        
        result = hash_data(data, algorithm="sha256")
        
        # Verify against hashlib implementation
        expected = hashlib.sha256(data.encode('utf-8')).hexdigest()
        self.assertEqual(result, expected)
    
    def test_sha512_hash(self):
        """Test SHA512 hashing algorithm."""
        data = "test data"
        
        result = hash_data(data, algorithm="sha512")
        
        # Verify against hashlib implementation
        expected = hashlib.sha512(data.encode('utf-8')).hexdigest()
        self.assertEqual(result, expected)
    
    def test_empty_string(self):
        """Test with empty string."""
        data = ""
        
        result = hash_data(data)
        
        self.assertEqual(result, "")
    
    def test_unicode_data(self):
        """Test with Unicode data."""
        data = "Hello, 世界!"  # Contains Unicode characters
        
        result = hash_data(data)
        
        # Verify against hashlib implementation
        expected = hashlib.md5(data.encode('utf-8')).hexdigest()
        self.assertEqual(result, expected)
    
    def test_different_encoding(self):
        """Test with different encoding."""
        data = "Hello, World!"
        encoding = "ascii"
        
        result = hash_data(data, encoding=encoding)
        
        # Verify against hashlib implementation
        expected = hashlib.md5(data.encode(encoding)).hexdigest()
        self.assertEqual(result, expected)
    
    def test_byte_input(self):
        """Test with bytes input instead of string."""
        data_bytes = b"test data"
        
        result = hash_data(data_bytes)
        
        # Verify against hashlib implementation
        expected = hashlib.md5(data_bytes).hexdigest()
        self.assertEqual(result, expected)
    
    def test_case_insensitive_algorithm(self):
        """Test that algorithm parameter is case-insensitive."""
        data = "test data"
        
        result_lower = hash_data(data, algorithm="sha256")
        result_upper = hash_data(data, algorithm="SHA256")
        
        self.assertEqual(result_lower, result_upper)
    
    def test_unsupported_algorithm(self):
        """Test with unsupported hash algorithm."""
        data = "test data"
        
        with self.assertRaises(ValueError) as context:
            hash_data(data, algorithm="unsupported")
        
        self.assertTrue("Unsupported hash algorithm" in str(context.exception))
    
    def test_consistent_hash(self):
        """Test that the same input produces the same hash."""
        data = "test data"
        
        result1 = hash_data(data)
        result2 = hash_data(data)
        
        self.assertEqual(result1, result2)
    
    def test_different_hashes(self):
        """Test that different inputs produce different hashes."""
        data1 = "test data 1"
        data2 = "test data 2"
        
        result1 = hash_data(data1)
        result2 = hash_data(data2)
        
        self.assertNotEqual(result1, result2)

if __name__ == '__main__':
    unittest.main()