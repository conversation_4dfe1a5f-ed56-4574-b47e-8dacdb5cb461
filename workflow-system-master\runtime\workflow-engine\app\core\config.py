import os
from typing import List
from pydantic_settings import BaseSettings, SettingsConfigDict
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

class Settings(BaseSettings):
    PROJECT_NAME: str = "Workflow Engine"
    PROJECT_DESCRIPTION: str = "Enterprise Workflow System Engine"
    VERSION: str = "0.1.0"
    API_V1_STR: str = "/api/v1"
    
    # CORS
    CORS_ORIGINS: List[str] = ["*"]
    
    # MongoDB
    MONGODB_URI: str = os.getenv(
        "MONGODB_URI", 
        "*****************************************************************************"
    )
    
    # PostgreSQL
    #POSTGRES_SERVER: str = os.getenv("POSTGRES_SERVER", "localhost")
    POSTGRES_SERVER: str = os.getenv("POSTGRES_SERVER", "workflow_postgres")
    POSTGRES_USER: str = os.getenv("POSTGRES_USER", "postgres")
    POSTGRES_PASSWORD: str = os.getenv("POSTGRES_PASSWORD", "postgres")
    POSTGRES_DB: str = os.getenv("POSTGRES_DB", "workflow_system")
    POSTGRES_PORT: str = os.getenv("POSTGRES_PORT", "5433")
    
    # Redis
    REDIS_HOST: str = os.getenv("REDIS_HOST", "workflow_redis")
    REDIS_PORT: str = os.getenv("REDIS_PORT", "6379")
    REDIS_PASSWORD: str = os.getenv("REDIS_PASSWORD", "")
    
    # Security
    SECRET_KEY: str = os.getenv("SECRET_KEY", "super_secret_key_change_in_production")
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 60 * 24 * 7  # 7 days

    #CLAUDE - ANTHROPIC
    anthropic_api_key: str = os.getenv("ANTHROPIC_API_KEY", "")

    # Model configuration
    model_config = SettingsConfigDict(
        case_sensitive=True,
        env_file=".env", 
        env_file_encoding="utf-8",
        extra="ignore"
    )

# Create a singleton settings instance
settings = Settings()
