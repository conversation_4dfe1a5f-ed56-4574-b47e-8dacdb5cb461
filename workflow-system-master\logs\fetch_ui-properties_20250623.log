{"timestamp": "2025-06-23T04:59:00.653943", "endpoint": "fetch/ui-properties", "input": {}, "output": {"success": true, "postgres_ui_properties": [], "mongo_drafts": [{"_id": "68552fce229f6505fc03bb6b", "ui_property_id": "UI5", "entity_id": "E15", "attribute_id": "E15.At1", "control_type": "text_input", "display_format": "EMP###", "input_mask": "EMP###", "placeholder_text": "Auto-generated", "auto_complete": false, "read_only": true, "validation_display": "inline", "help_text_position": "below", "label": "Employee ID", "required_indicator": false, "natural_language": "Entity: Employee\nAttribute: employeeId\nControl Type: text_input\nLabel: Employee ID\nDisplay Format: EMP###\nInput Mask: EMP###\nPlaceholder Text: Auto-generated\nRead Only: true\nValidation Display: inline\nHelp Text Position: below", "version": 2, "status": "deployed_to_temp", "created_at": "2025-06-20T09:54:22.597847", "updated_at": "2025-06-20T12:19:39.187272", "created_by": "Tarun", "updated_by": "Tarun", "ui_property_status": "new", "changes_detected": []}], "total_postgres": 0, "total_drafts": 1, "operation": "fetch"}, "status": "success"}
{"timestamp": "2025-06-23T06:40:13.438165", "endpoint": "fetch/ui-properties", "input": {}, "output": {"success": true, "postgres_ui_properties": [], "mongo_drafts": [{"_id": "68552fce229f6505fc03bb6b", "ui_property_id": "UI5", "entity_id": "E15", "attribute_id": "E15.At1", "control_type": "text_input", "display_format": "EMP###", "input_mask": "EMP###", "placeholder_text": "Auto-generated", "auto_complete": false, "read_only": true, "validation_display": "inline", "help_text_position": "below", "label": "Employee ID", "required_indicator": false, "natural_language": "Entity: Employee\nAttribute: employeeId\nControl Type: text_input\nLabel: Employee ID\nDisplay Format: EMP###\nInput Mask: EMP###\nPlaceholder Text: Auto-generated\nRead Only: true\nValidation Display: inline\nHelp Text Position: below", "version": 2, "status": "deployed_to_temp", "created_at": "2025-06-20T09:54:22.597847", "updated_at": "2025-06-20T12:19:39.187272", "created_by": "Tarun", "updated_by": "Tarun", "ui_property_status": "new", "changes_detected": []}], "total_postgres": 0, "total_drafts": 1, "operation": "fetch"}, "status": "success"}
{"timestamp": "2025-06-23T08:00:25.682489", "endpoint": "fetch/ui-properties", "input": {}, "output": {"success": true, "postgres_ui_properties": [], "mongo_drafts": [{"_id": "68552fce229f6505fc03bb6b", "ui_property_id": "UI5", "entity_id": "E15", "attribute_id": "E15.At1", "control_type": "text_input", "display_format": "EMP###", "input_mask": "EMP###", "placeholder_text": "Auto-generated", "auto_complete": false, "read_only": true, "validation_display": "inline", "help_text_position": "below", "label": "Employee ID", "required_indicator": false, "natural_language": "Entity: Employee\nAttribute: employeeId\nControl Type: text_input\nLabel: Employee ID\nDisplay Format: EMP###\nInput Mask: EMP###\nPlaceholder Text: Auto-generated\nRead Only: true\nValidation Display: inline\nHelp Text Position: below", "version": 2, "status": "deployed_to_temp", "created_at": "2025-06-20T09:54:22.597847", "updated_at": "2025-06-20T12:19:39.187272", "created_by": "Tarun", "updated_by": "Tarun", "ui_property_status": "new", "changes_detected": []}, {"_id": "685903f05e423f11979d4093", "ui_property_id": "UI1", "entity_id": "E13", "attribute_id": "E13.At11", "control_type": "number_input", "display_format": "##.# days", "input_mask": "##.#", "placeholder_text": "Enter entitlement", "auto_complete": false, "read_only": false, "validation_display": "inline", "help_text_position": "below", "label": "Total Leave Entitlement", "required_indicator": false, "natural_language": "Entity: Employee\nAttribute: totalLeaveEntitlement\nControl Type: number_input\nLabel: Total Leave Entitlement\nDisplay Format: ##.# days\nInput Mask: ##.#\nPlaceholder Text: Enter entitlement\nValidation Display: inline\nHelp Text Position: below", "version": 1, "status": "draft", "created_at": "2025-06-23T07:36:16.794690", "updated_at": "2025-06-23T07:36:16.794698", "created_by": "Tarun", "updated_by": "Tarun", "ui_property_status": "new", "changes_detected": []}], "total_postgres": 0, "total_drafts": 2, "operation": "fetch"}, "status": "success"}
{"timestamp": "2025-06-23T08:00:34.358120", "endpoint": "fetch/ui-properties", "input": {}, "output": {"success": true, "postgres_ui_properties": [], "mongo_drafts": [{"_id": "68552fce229f6505fc03bb6b", "ui_property_id": "UI5", "entity_id": "E15", "attribute_id": "E15.At1", "control_type": "text_input", "display_format": "EMP###", "input_mask": "EMP###", "placeholder_text": "Auto-generated", "auto_complete": false, "read_only": true, "validation_display": "inline", "help_text_position": "below", "label": "Employee ID", "required_indicator": false, "natural_language": "Entity: Employee\nAttribute: employeeId\nControl Type: text_input\nLabel: Employee ID\nDisplay Format: EMP###\nInput Mask: EMP###\nPlaceholder Text: Auto-generated\nRead Only: true\nValidation Display: inline\nHelp Text Position: below", "version": 2, "status": "deployed_to_temp", "created_at": "2025-06-20T09:54:22.597847", "updated_at": "2025-06-20T12:19:39.187272", "created_by": "Tarun", "updated_by": "Tarun", "ui_property_status": "new", "changes_detected": []}, {"_id": "685903f05e423f11979d4093", "ui_property_id": "UI1", "entity_id": "E13", "attribute_id": "E13.At11", "control_type": "number_input", "display_format": "##.# days", "input_mask": "##.#", "placeholder_text": "Enter entitlement", "auto_complete": false, "read_only": false, "validation_display": "inline", "help_text_position": "below", "label": "Total Leave Entitlement", "required_indicator": false, "natural_language": "Entity: Employee\nAttribute: totalLeaveEntitlement\nControl Type: number_input\nLabel: Total Leave Entitlement\nDisplay Format: ##.# days\nInput Mask: ##.#\nPlaceholder Text: Enter entitlement\nValidation Display: inline\nHelp Text Position: below", "version": 1, "status": "draft", "created_at": "2025-06-23T07:36:16.794690", "updated_at": "2025-06-23T07:36:16.794698", "created_by": "Tarun", "updated_by": "Tarun", "ui_property_status": "new", "changes_detected": []}], "total_postgres": 0, "total_drafts": 2, "operation": "fetch"}, "status": "success"}
{"timestamp": "2025-06-23T08:24:55.020995", "endpoint": "fetch/ui-properties", "input": {}, "output": {"success": true, "postgres_ui_properties": [], "mongo_drafts": [{"_id": "68552fce229f6505fc03bb6b", "ui_property_id": "UI5", "entity_id": "E15", "attribute_id": "E15.At1", "control_type": "text_input", "display_format": "EMP###", "input_mask": "EMP###", "placeholder_text": "Auto-generated", "auto_complete": false, "read_only": true, "validation_display": "inline", "help_text_position": "below", "label": "Employee ID", "required_indicator": false, "natural_language": "Entity: Employee\nAttribute: employeeId\nControl Type: text_input\nLabel: Employee ID\nDisplay Format: EMP###\nInput Mask: EMP###\nPlaceholder Text: Auto-generated\nRead Only: true\nValidation Display: inline\nHelp Text Position: below", "version": 2, "status": "deployed_to_temp", "created_at": "2025-06-20T09:54:22.597847", "updated_at": "2025-06-20T12:19:39.187272", "created_by": "Tarun", "updated_by": "Tarun", "ui_property_status": "new", "changes_detected": []}, {"_id": "685903f05e423f11979d4093", "ui_property_id": "UI1", "entity_id": "E13", "attribute_id": "E13.At11", "control_type": "number_input", "display_format": "##.# days", "input_mask": "##.#", "placeholder_text": "Enter entitlement", "auto_complete": false, "read_only": false, "validation_display": "inline", "help_text_position": "below", "label": "Total Leave Entitlement", "required_indicator": false, "natural_language": "Entity: Employee\nAttribute: totalLeaveEntitlement\nControl Type: number_input\nLabel: Total Leave Entitlement\nDisplay Format: ##.# days\nInput Mask: ##.#\nPlaceholder Text: Enter entitlement\nValidation Display: inline\nHelp Text Position: below", "version": 1, "status": "draft", "created_at": "2025-06-23T07:36:16.794690", "updated_at": "2025-06-23T07:36:16.794698", "created_by": "Tarun", "updated_by": "Tarun", "ui_property_status": "new", "changes_detected": []}], "total_postgres": 0, "total_drafts": 2, "operation": "fetch"}, "status": "success"}
{"timestamp": "2025-06-23T08:46:35.521305", "endpoint": "fetch/ui-properties", "input": {}, "output": {"success": true, "postgres_ui_properties": [], "mongo_drafts": [{"_id": "68552fce229f6505fc03bb6b", "ui_property_id": "UI5", "entity_id": "E15", "attribute_id": "E15.At1", "control_type": "text_input", "display_format": "EMP###", "input_mask": "EMP###", "placeholder_text": "Auto-generated", "auto_complete": false, "read_only": true, "validation_display": "inline", "help_text_position": "below", "label": "Employee ID", "required_indicator": false, "natural_language": "Entity: Employee\nAttribute: employeeId\nControl Type: text_input\nLabel: Employee ID\nDisplay Format: EMP###\nInput Mask: EMP###\nPlaceholder Text: Auto-generated\nRead Only: true\nValidation Display: inline\nHelp Text Position: below", "version": 2, "status": "deployed_to_temp", "created_at": "2025-06-20T09:54:22.597847", "updated_at": "2025-06-20T12:19:39.187272", "created_by": "Tarun", "updated_by": "Tarun", "ui_property_status": "new", "changes_detected": []}, {"_id": "685903f05e423f11979d4093", "ui_property_id": "UI1", "entity_id": "E13", "attribute_id": "E13.At11", "control_type": "number_input", "display_format": "##.# days", "input_mask": "##.#", "placeholder_text": "Enter entitlement", "auto_complete": false, "read_only": false, "validation_display": "inline", "help_text_position": "below", "label": "Total Leave Entitlement", "required_indicator": false, "natural_language": "Entity: Employee\nAttribute: totalLeaveEntitlement\nControl Type: number_input\nLabel: Total Leave Entitlement\nDisplay Format: ##.# days\nInput Mask: ##.#\nPlaceholder Text: Enter entitlement\nValidation Display: inline\nHelp Text Position: below", "version": 1, "status": "draft", "created_at": "2025-06-23T07:36:16.794690", "updated_at": "2025-06-23T07:36:16.794698", "created_by": "Tarun", "updated_by": "Tarun", "ui_property_status": "new", "changes_detected": []}], "total_postgres": 0, "total_drafts": 2, "operation": "fetch"}, "status": "success"}
{"timestamp": "2025-06-23T09:37:18.204077", "endpoint": "fetch/ui-properties", "input": {}, "output": {"success": true, "postgres_ui_properties": [], "mongo_drafts": [{"_id": "68552fce229f6505fc03bb6b", "ui_property_id": "UI5", "entity_id": "E15", "attribute_id": "E15.At1", "control_type": "text_input", "display_format": "EMP###", "input_mask": "EMP###", "placeholder_text": "Auto-generated", "auto_complete": false, "read_only": true, "validation_display": "inline", "help_text_position": "below", "label": "Employee ID", "required_indicator": false, "natural_language": "Entity: Employee\nAttribute: employeeId\nControl Type: text_input\nLabel: Employee ID\nDisplay Format: EMP###\nInput Mask: EMP###\nPlaceholder Text: Auto-generated\nRead Only: true\nValidation Display: inline\nHelp Text Position: below", "version": 2, "status": "deployed_to_temp", "created_at": "2025-06-20T09:54:22.597847", "updated_at": "2025-06-20T12:19:39.187272", "created_by": "Tarun", "updated_by": "Tarun", "ui_property_status": "new", "changes_detected": []}, {"_id": "685903f05e423f11979d4093", "ui_property_id": "UI1", "entity_id": "E13", "attribute_id": "E13.At11", "control_type": "number_input", "display_format": "##.# days", "input_mask": "##.#", "placeholder_text": "Enter entitlement", "auto_complete": false, "read_only": false, "validation_display": "inline", "help_text_position": "below", "label": "Total Leave Entitlement", "required_indicator": false, "natural_language": "Entity: Employee\nAttribute: totalLeaveEntitlement\nControl Type: number_input\nLabel: Total Leave Entitlement\nDisplay Format: ##.# days\nInput Mask: ##.#\nPlaceholder Text: Enter entitlement\nValidation Display: inline\nHelp Text Position: below", "version": 4, "status": "draft", "created_at": "2025-06-23T09:00:43.903023", "updated_at": "2025-06-23T09:00:45.190172", "created_by": "Tarun", "updated_by": "Tarun", "ui_property_status": "existing", "changes_detected": []}], "total_postgres": 0, "total_drafts": 2, "operation": "fetch"}, "status": "success"}
{"timestamp": "2025-06-23T10:47:52.309527", "endpoint": "fetch/ui-properties", "input": {}, "output": {"success": true, "postgres_ui_properties": [], "mongo_drafts": [{"_id": "68552fce229f6505fc03bb6b", "ui_property_id": "UI5", "entity_id": "E15", "attribute_id": "E15.At1", "control_type": "text_input", "display_format": "EMP###", "input_mask": "EMP###", "placeholder_text": "Auto-generated", "auto_complete": false, "read_only": true, "validation_display": "inline", "help_text_position": "below", "label": "Employee ID", "required_indicator": false, "natural_language": "Entity: Employee\nAttribute: employeeId\nControl Type: text_input\nLabel: Employee ID\nDisplay Format: EMP###\nInput Mask: EMP###\nPlaceholder Text: Auto-generated\nRead Only: true\nValidation Display: inline\nHelp Text Position: below", "version": 2, "status": "deployed_to_temp", "created_at": "2025-06-20T09:54:22.597847", "updated_at": "2025-06-20T12:19:39.187272", "created_by": "Tarun", "updated_by": "Tarun", "ui_property_status": "new", "changes_detected": []}, {"_id": "685903f05e423f11979d4093", "ui_property_id": "UI1", "entity_id": "E13", "attribute_id": "E13.At11", "control_type": "number_input", "display_format": "##.# days", "input_mask": "##.#", "placeholder_text": "Enter entitlement", "auto_complete": false, "read_only": false, "validation_display": "inline", "help_text_position": "below", "label": "Total Leave Entitlement", "required_indicator": false, "natural_language": "Entity: Employee\nAttribute: totalLeaveEntitlement\nControl Type: number_input\nLabel: Total Leave Entitlement\nDisplay Format: ##.# days\nInput Mask: ##.#\nPlaceholder Text: Enter entitlement\nValidation Display: inline\nHelp Text Position: below", "version": 4, "status": "draft", "created_at": "2025-06-23T09:00:43.903023", "updated_at": "2025-06-23T09:00:45.190172", "created_by": "Tarun", "updated_by": "Tarun", "ui_property_status": "existing", "changes_detected": []}], "total_postgres": 0, "total_drafts": 2, "operation": "fetch"}, "status": "success"}
{"timestamp": "2025-06-23T11:34:24.732021", "endpoint": "fetch/ui-properties", "input": {}, "output": {"success": true, "postgres_ui_properties": [], "mongo_drafts": [{"_id": "68552fce229f6505fc03bb6b", "ui_property_id": "UI5", "entity_id": "E15", "attribute_id": "E15.At1", "control_type": "text_input", "display_format": "EMP###", "input_mask": "EMP###", "placeholder_text": "Auto-generated", "auto_complete": false, "read_only": true, "validation_display": "inline", "help_text_position": "below", "label": "Employee ID", "required_indicator": false, "natural_language": "Entity: Employee\nAttribute: employeeId\nControl Type: text_input\nLabel: Employee ID\nDisplay Format: EMP###\nInput Mask: EMP###\nPlaceholder Text: Auto-generated\nRead Only: true\nValidation Display: inline\nHelp Text Position: below", "version": 2, "status": "deployed_to_temp", "created_at": "2025-06-20T09:54:22.597847", "updated_at": "2025-06-20T12:19:39.187272", "created_by": "Tarun", "updated_by": "Tarun", "ui_property_status": "new", "changes_detected": []}, {"_id": "685903f05e423f11979d4093", "ui_property_id": "UI1", "entity_id": "E13", "attribute_id": "E13.At11", "control_type": "number_input", "display_format": "##.# days", "input_mask": "##.#", "placeholder_text": "Enter entitlement", "auto_complete": false, "read_only": false, "validation_display": "inline", "help_text_position": "below", "label": "Total Leave Entitlement", "required_indicator": false, "natural_language": "Entity: Employee\nAttribute: totalLeaveEntitlement\nControl Type: number_input\nLabel: Total Leave Entitlement\nDisplay Format: ##.# days\nInput Mask: ##.#\nPlaceholder Text: Enter entitlement\nValidation Display: inline\nHelp Text Position: below", "version": 4, "status": "draft", "created_at": "2025-06-23T09:00:43.903023", "updated_at": "2025-06-23T09:00:45.190172", "created_by": "Tarun", "updated_by": "Tarun", "ui_property_status": "existing", "changes_detected": []}], "total_postgres": 0, "total_drafts": 2, "operation": "fetch"}, "status": "success"}
{"timestamp": "2025-06-23T11:35:50.683643", "endpoint": "fetch/ui-properties", "input": {}, "output": {"success": true, "postgres_ui_properties": [], "mongo_drafts": [{"_id": "68552fce229f6505fc03bb6b", "ui_property_id": "UI5", "entity_id": "E15", "attribute_id": "E15.At1", "control_type": "text_input", "display_format": "EMP###", "input_mask": "EMP###", "placeholder_text": "Auto-generated", "auto_complete": false, "read_only": true, "validation_display": "inline", "help_text_position": "below", "label": "Employee ID", "required_indicator": false, "natural_language": "Entity: Employee\nAttribute: employeeId\nControl Type: text_input\nLabel: Employee ID\nDisplay Format: EMP###\nInput Mask: EMP###\nPlaceholder Text: Auto-generated\nRead Only: true\nValidation Display: inline\nHelp Text Position: below", "version": 2, "status": "deployed_to_temp", "created_at": "2025-06-20T09:54:22.597847", "updated_at": "2025-06-20T12:19:39.187272", "created_by": "Tarun", "updated_by": "Tarun", "ui_property_status": "new", "changes_detected": []}, {"_id": "685903f05e423f11979d4093", "ui_property_id": "UI1", "entity_id": "E13", "attribute_id": "E13.At11", "control_type": "number_input", "display_format": "##.# days", "input_mask": "##.#", "placeholder_text": "Enter entitlement", "auto_complete": false, "read_only": false, "validation_display": "inline", "help_text_position": "below", "label": "Total Leave Entitlement", "required_indicator": false, "natural_language": "Entity: Employee\nAttribute: totalLeaveEntitlement\nControl Type: number_input\nLabel: Total Leave Entitlement\nDisplay Format: ##.# days\nInput Mask: ##.#\nPlaceholder Text: Enter entitlement\nValidation Display: inline\nHelp Text Position: below", "version": 4, "status": "draft", "created_at": "2025-06-23T09:00:43.903023", "updated_at": "2025-06-23T09:00:45.190172", "created_by": "Tarun", "updated_by": "Tarun", "ui_property_status": "existing", "changes_detected": []}], "total_postgres": 0, "total_drafts": 2, "operation": "fetch"}, "status": "success"}
{"timestamp": "2025-06-23T11:42:08.764744", "endpoint": "fetch/ui-properties", "input": {}, "output": {"success": true, "postgres_ui_properties": [], "mongo_drafts": [{"_id": "68552fce229f6505fc03bb6b", "ui_property_id": "UI5", "entity_id": "E15", "attribute_id": "E15.At1", "control_type": "text_input", "display_format": "EMP###", "input_mask": "EMP###", "placeholder_text": "Auto-generated", "auto_complete": false, "read_only": true, "validation_display": "inline", "help_text_position": "below", "label": "Employee ID", "required_indicator": false, "natural_language": "Entity: Employee\nAttribute: employeeId\nControl Type: text_input\nLabel: Employee ID\nDisplay Format: EMP###\nInput Mask: EMP###\nPlaceholder Text: Auto-generated\nRead Only: true\nValidation Display: inline\nHelp Text Position: below", "version": 2, "status": "deployed_to_temp", "created_at": "2025-06-20T09:54:22.597847", "updated_at": "2025-06-20T12:19:39.187272", "created_by": "Tarun", "updated_by": "Tarun", "ui_property_status": "new", "changes_detected": []}, {"_id": "685903f05e423f11979d4093", "ui_property_id": "UI1", "entity_id": "E13", "attribute_id": "E13.At11", "control_type": "number_input", "display_format": "##.# days", "input_mask": "##.#", "placeholder_text": "Enter entitlement", "auto_complete": false, "read_only": false, "validation_display": "inline", "help_text_position": "below", "label": "Total Leave Entitlement", "required_indicator": false, "natural_language": "Entity: Employee\nAttribute: totalLeaveEntitlement\nControl Type: number_input\nLabel: Total Leave Entitlement\nDisplay Format: ##.# days\nInput Mask: ##.#\nPlaceholder Text: Enter entitlement\nValidation Display: inline\nHelp Text Position: below", "version": 4, "status": "draft", "created_at": "2025-06-23T09:00:43.903023", "updated_at": "2025-06-23T09:00:45.190172", "created_by": "Tarun", "updated_by": "Tarun", "ui_property_status": "existing", "changes_detected": []}], "total_postgres": 0, "total_drafts": 2, "operation": "fetch"}, "status": "success"}
{"timestamp": "2025-06-23T11:42:31.691557", "endpoint": "fetch/ui-properties", "input": {}, "output": {"success": true, "postgres_ui_properties": [], "mongo_drafts": [{"_id": "68552fce229f6505fc03bb6b", "ui_property_id": "UI5", "entity_id": "E15", "attribute_id": "E15.At1", "control_type": "text_input", "display_format": "EMP###", "input_mask": "EMP###", "placeholder_text": "Auto-generated", "auto_complete": false, "read_only": true, "validation_display": "inline", "help_text_position": "below", "label": "Employee ID", "required_indicator": false, "natural_language": "Entity: Employee\nAttribute: employeeId\nControl Type: text_input\nLabel: Employee ID\nDisplay Format: EMP###\nInput Mask: EMP###\nPlaceholder Text: Auto-generated\nRead Only: true\nValidation Display: inline\nHelp Text Position: below", "version": 2, "status": "deployed_to_temp", "created_at": "2025-06-20T09:54:22.597847", "updated_at": "2025-06-20T12:19:39.187272", "created_by": "Tarun", "updated_by": "Tarun", "ui_property_status": "new", "changes_detected": []}, {"_id": "685903f05e423f11979d4093", "ui_property_id": "UI1", "entity_id": "E13", "attribute_id": "E13.At11", "control_type": "number_input", "display_format": "##.# days", "input_mask": "##.#", "placeholder_text": "Enter entitlement", "auto_complete": false, "read_only": false, "validation_display": "inline", "help_text_position": "below", "label": "Total Leave Entitlement", "required_indicator": false, "natural_language": "Entity: Employee\nAttribute: totalLeaveEntitlement\nControl Type: number_input\nLabel: Total Leave Entitlement\nDisplay Format: ##.# days\nInput Mask: ##.#\nPlaceholder Text: Enter entitlement\nValidation Display: inline\nHelp Text Position: below", "version": 4, "status": "draft", "created_at": "2025-06-23T09:00:43.903023", "updated_at": "2025-06-23T09:00:45.190172", "created_by": "Tarun", "updated_by": "Tarun", "ui_property_status": "existing", "changes_detected": []}], "total_postgres": 0, "total_drafts": 2, "operation": "fetch"}, "status": "success"}
{"timestamp": "2025-06-23T14:08:00.808529", "endpoint": "fetch/ui-properties", "input": {}, "output": {"success": true, "postgres_ui_properties": [], "mongo_drafts": [{"_id": "68552fce229f6505fc03bb6b", "ui_property_id": "UI5", "entity_id": "E15", "attribute_id": "E15.At1", "control_type": "text_input", "display_format": "EMP###", "input_mask": "EMP###", "placeholder_text": "Auto-generated", "auto_complete": false, "read_only": true, "validation_display": "inline", "help_text_position": "below", "label": "Employee ID", "required_indicator": false, "natural_language": "Entity: Employee\nAttribute: employeeId\nControl Type: text_input\nLabel: Employee ID\nDisplay Format: EMP###\nInput Mask: EMP###\nPlaceholder Text: Auto-generated\nRead Only: true\nValidation Display: inline\nHelp Text Position: below", "version": 2, "status": "deployed_to_temp", "created_at": "2025-06-20T09:54:22.597847", "updated_at": "2025-06-20T12:19:39.187272", "created_by": "Tarun", "updated_by": "Tarun", "ui_property_status": "new", "changes_detected": []}, {"_id": "685903f05e423f11979d4093", "ui_property_id": "UI1", "entity_id": "E13", "attribute_id": "E13.At11", "control_type": "number_input", "display_format": "##.# days", "input_mask": "##.#", "placeholder_text": "Enter entitlement", "auto_complete": false, "read_only": false, "validation_display": "inline", "help_text_position": "below", "label": "Total Leave Entitlement", "required_indicator": false, "natural_language": "Entity: Employee\nAttribute: totalLeaveEntitlement\nControl Type: number_input\nLabel: Total Leave Entitlement\nDisplay Format: ##.# days\nInput Mask: ##.#\nPlaceholder Text: Enter entitlement\nValidation Display: inline\nHelp Text Position: below", "version": 5, "status": "deployed_to_temp", "created_at": "2025-06-23T09:00:43.903023", "updated_at": "2025-06-23T14:03:50.593286", "created_by": "Tarun", "updated_by": "Tarun", "ui_property_status": "existing", "changes_detected": []}], "total_postgres": 0, "total_drafts": 2, "operation": "fetch"}, "status": "success"}
