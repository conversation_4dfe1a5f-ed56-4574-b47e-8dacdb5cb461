{"timestamp": "2025-06-23T11:33:59.457979", "operation": "deploy_single_constant_to_workflow_temp", "input_data": {"constant_id": 1003}, "result": {"success": false, "error": "Constant 1003 not found with status draft", "constant_id": 1003, "required_status": "draft"}, "status": "error"}
{"timestamp": "2025-06-23T11:52:06.510763", "operation": "deploy_single_constant_to_workflow_temp", "input_data": {"constant_id": 1003}, "result": {"success": false, "error": "Constant 1003 not found with status draft", "constant_id": 1003, "required_status": "draft"}, "status": "error"}
{"timestamp": "2025-06-23T11:56:50.590447", "operation": "deploy_single_constant_to_workflow_temp", "input_data": {"constant_id": 1003}, "result": {"success": false, "error": "Constant 1003 not found with status draft", "constant_id": 1003, "required_status": "draft"}, "status": "error"}
{"timestamp": "2025-06-23T11:57:27.637983", "operation": "deploy_single_constant_to_workflow_temp", "input_data": {"constant_id": 1003}, "result": {"success": false, "error": "Constant 1003 not found with status draft", "constant_id": 1003, "required_status": "draft"}, "status": "error"}
{"timestamp": "2025-06-23T12:18:44.674210", "operation": "deploy_single_constant_to_workflow_temp", "input_data": {"constant_id": 1003}, "result": {"success": false, "error": "Constant 1003 not found with status draft", "constant_id": 1003, "required_status": "draft"}, "status": "error"}
{"timestamp": "2025-06-23T12:19:33.205888", "operation": "deploy_single_constant_to_workflow_temp", "input_data": {"constant_id": 1004}, "result": {"success": false, "error": "Constant 1004 not found with status draft", "constant_id": 1004, "required_status": "draft"}, "status": "error"}
{"timestamp": "2025-06-23T12:20:43.093673", "operation": "deploy_single_constant_to_workflow_temp", "input_data": {"constant_id": 1004}, "result": {"success": false, "error": "Constant 1004 not found with status draft", "constant_id": 1004, "required_status": "draft"}, "status": "error"}
{"timestamp": "2025-06-23T14:03:51.090611", "operation": "insert_constant_to_workflow_temp", "input_data": {"_id": "6858f30f1195bbcbf7aa7691", "constant_id": 1003, "attribute": "allow_override", "value": "false", "description": "Whether this constant can be overridden", "tenant_id": "T101", "allow_override": false, "override_permissions": "", "status": "draft", "entity_name": "", "attribute_name": "", "created_at": "2025-06-23T09:02:49.471376", "updated_at": "2025-06-23T09:02:49.695971", "created_by": "Tarun", "updated_by": "Tarun", "constant_status": "new", "changes_detected": [], "version": 1}, "result": {"success": true, "inserted_id": 1003, "schema": "workflow_temp", "constant_id": 1003, "attribute": "allow_override", "original_constant_id": 1004, "entity_reference_validation": {"entity_exists": true, "attribute_exists": true, "entity_name": "", "attribute_name": "", "warnings": []}}, "status": "success"}
{"timestamp": "2025-06-23T14:03:51.093420", "operation": "process_mongo_constants_to_workflow_temp", "input_data": {"schema": "workflow_temp"}, "result": {"total_processed": 1, "successful_inserts": 1, "failed_inserts": 0, "details": [{"constant_id": 1003, "status": "success", "details": {"success": true, "inserted_id": 1003, "schema": "workflow_temp", "constant_id": 1003, "attribute": "allow_override", "original_constant_id": 1004, "entity_reference_validation": {"entity_exists": true, "attribute_exists": true, "entity_name": "", "attribute_name": "", "warnings": []}}}]}, "status": "success"}
{"timestamp": "2025-06-23T14:09:02.097805", "operation": "process_mongo_constants_to_workflow_temp", "input_data": {"schema": "workflow_temp"}, "result": {"total_processed": 0, "successful_inserts": 0, "failed_inserts": 0, "details": []}, "status": "success"}
{"timestamp": "2025-06-23T14:32:34.402426", "operation": "process_mongo_constants_to_workflow_temp", "input_data": {"schema": "workflow_temp"}, "result": {"total_processed": 0, "successful_inserts": 0, "failed_inserts": 0, "details": []}, "status": "success"}
{"timestamp": "2025-06-23T14:38:53.913562", "operation": "process_mongo_constants_to_workflow_temp", "input_data": {"schema": "workflow_temp"}, "result": {"total_processed": 0, "successful_inserts": 0, "failed_inserts": 0, "details": []}, "status": "success"}
