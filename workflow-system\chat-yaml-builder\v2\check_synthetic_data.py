#!/usr/bin/env python3
"""
Script to check if synthetic data was deployed to the database.
"""

import os
import logging
import psycopg2

# Set up logging
logging.basicConfig(level=logging.INFO, 
                   format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger('check_synthetic_data')

def get_db_connection(schema_name: str = None):
    """
    Get a database connection.
    
    Args:
        schema_name: Optional schema name to set as the search path
        
    Returns:
        Database connection object
    """
    # Get database connection parameters from environment variables or use defaults
    db_host = os.environ.get('DB_HOST', '**********')
    db_port = os.environ.get('DB_PORT', '5432')
    db_name = os.environ.get('DB_NAME', 'workflow_system')
    db_user = os.environ.get('DB_USER', 'postgres')
    db_password = os.environ.get('DB_PASSWORD', 'workflow_postgres_secure_password')
    
    # Connect to the database
    conn = psycopg2.connect(
        host=db_host,
        port=db_port,
        dbname=db_name,
        user=db_user,
        password=db_password
    )
    
    # Set schema search path if provided
    if schema_name:
        with conn.cursor() as cursor:
            cursor.execute(f"SET search_path TO {schema_name}")
            conn.commit()
    
    return conn

def check_synthetic_data():
    """
    Check if synthetic data was deployed to the database.
    """
    schema_name = 'workflow_temp'
    
    try:
        conn = get_db_connection(schema_name)
        
        with conn.cursor() as cursor:
            # Check if the employee table exists
            cursor.execute("""
                SELECT EXISTS (
                    SELECT FROM information_schema.tables 
                    WHERE table_schema = %s
                    AND table_name = 'e000002_employee'
                )
            """, (schema_name,))
            
            table_exists = cursor.fetchone()[0]
            
            if not table_exists:
                logger.error(f"Table 'e000002_employee' does not exist in schema '{schema_name}'")
                return
            
            # Check if synthetic data exists
            cursor.execute(f"""
                SELECT *
                FROM {schema_name}.e000002_employee
                WHERE employeeid IN ('1', '2')
            """)
            
            rows = cursor.fetchall()
            
            if not rows:
                logger.info("No synthetic data found in the employee table")
            else:
                logger.info(f"Found {len(rows)} rows of synthetic data in the employee table:")
                
                # Get column names
                cursor.execute(f"""
                    SELECT column_name
                    FROM information_schema.columns
                    WHERE table_schema = %s
                    AND table_name = 'e000002_employee'
                    ORDER BY ordinal_position
                """, (schema_name,))
                
                columns = [col[0] for col in cursor.fetchall()]
                
                # Print column names
                logger.info("Columns: " + ", ".join(columns))
                
                # Print rows
                for row in rows:
                    logger.info(f"Row: {row}")
    except Exception as e:
        logger.error(f"Error checking synthetic data: {str(e)}")
    finally:
        if conn:
            conn.close()

if __name__ == "__main__":
    check_synthetic_data()
