"""
Add audit columns (created_at, created_by, updated_at, updated_by) to entity tables.
"""

import os
import sys
import logging
from typing import Dict, List, Tuple, Any, Optional

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('add_audit_columns.log')
    ]
)
logger = logging.getLogger('add_audit_columns')

# Add parent directory to path to import common modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from db_utils import execute_query

def check_column_exists(schema_name: str, table_name: str, column_name: str) -> bool:
    """
    Check if a column exists in a table.
    
    Args:
        schema_name: Schema name
        table_name: Table name
        column_name: Column name
        
    Returns:
        <PERSON><PERSON>an indicating if the column exists
    """
    try:
        success, query_messages, result = execute_query(
            """
            SELECT EXISTS (
                SELECT FROM information_schema.columns 
                WHERE table_schema = %s
                AND table_name = %s
                AND column_name = %s
            )
            """,
            (schema_name, table_name, column_name)
        )
        
        if not success:
            logger.error(f"Error checking if column {column_name} exists: {query_messages}")
            return False
        
        return result and result[0][0]
    except Exception as e:
        logger.error(f"Error checking if column {column_name} exists: {str(e)}")
        return False

def add_audit_columns(schema_name: str, table_name: str) -> Tuple[bool, List[str]]:
    """
    Add audit columns to a table.
    
    Args:
        schema_name: Schema name
        table_name: Table name
        
    Returns:
        Tuple containing:
            - Boolean indicating if addition was successful
            - List of messages (warnings, errors, or success messages)
    """
    messages = []
    
    try:
        # Check if table exists
        success, query_messages, result = execute_query(
            f"""
            SELECT EXISTS (
                SELECT FROM information_schema.tables 
                WHERE table_schema = %s
                AND table_name = %s
            )
            """,
            (schema_name, table_name)
        )
        
        if not success:
            messages.extend(query_messages)
            return False, messages
        
        table_exists = result and result[0][0]
        
        if not table_exists:
            messages.append(f"Table {schema_name}.{table_name} does not exist")
            logger.warning(f"Table {schema_name}.{table_name} does not exist")
            return False, messages
        
        # Check for audit columns
        audit_columns = [
            ("created_at", "TIMESTAMP", "CURRENT_TIMESTAMP"),
            ("created_by", "VARCHAR(100)", "'system'"),
            ("updated_at", "TIMESTAMP", "CURRENT_TIMESTAMP"),
            ("updated_by", "VARCHAR(100)", "'system'")
        ]
        
        for col_name, col_type, col_default in audit_columns:
            if not check_column_exists(schema_name, table_name, col_name):
                # Add audit column
                query = f"""
                    ALTER TABLE {schema_name}.{table_name}
                    ADD COLUMN {col_name} {col_type} DEFAULT {col_default}
                """
                
                success, query_messages, _ = execute_query(query)
                
                if not success:
                    messages.extend(query_messages)
                    logger.error(f"Failed to add column '{col_name}' to table {schema_name}.{table_name}: {query_messages}")
                    continue  # Continue with other columns instead of failing
                
                messages.append(f"Added audit column '{col_name}' to table {schema_name}.{table_name}")
                logger.info(f"Added audit column '{col_name}' to table {schema_name}.{table_name}")
            else:
                messages.append(f"Audit column '{col_name}' already exists in table {schema_name}.{table_name}")
                logger.info(f"Audit column '{col_name}' already exists in table {schema_name}.{table_name}")
        
        return True, messages
    except Exception as e:
        error_msg = f"Error adding audit columns to table {schema_name}.{table_name}: {str(e)}"
        logger.error(error_msg, exc_info=True)
        messages.append(error_msg)
        return False, messages

def add_id_column(schema_name: str, table_name: str) -> Tuple[bool, List[str]]:
    """
    Add an ID column to a table.
    
    Args:
        schema_name: Schema name
        table_name: Table name
        
    Returns:
        Tuple containing:
            - Boolean indicating if addition was successful
            - List of messages (warnings, errors, or success messages)
    """
    messages = []
    
    try:
        # Check if table exists
        success, query_messages, result = execute_query(
            f"""
            SELECT EXISTS (
                SELECT FROM information_schema.tables 
                WHERE table_schema = %s
                AND table_name = %s
            )
            """,
            (schema_name, table_name)
        )
        
        if not success:
            messages.extend(query_messages)
            return False, messages
        
        table_exists = result and result[0][0]
        
        if not table_exists:
            messages.append(f"Table {schema_name}.{table_name} does not exist")
            logger.warning(f"Table {schema_name}.{table_name} does not exist")
            return False, messages
        
        # Check if ID column exists
        if not check_column_exists(schema_name, table_name, "id"):
            # Check if table has a primary key
            success, query_messages, result = execute_query(
                f"""
                SELECT kcu.column_name
                FROM information_schema.table_constraints tc
                JOIN information_schema.key_column_usage kcu
                ON tc.constraint_name = kcu.constraint_name
                AND tc.table_schema = kcu.table_schema
                WHERE tc.constraint_type = 'PRIMARY KEY'
                AND tc.table_schema = %s
                AND tc.table_name = %s
                """,
                (schema_name, table_name)
            )
            
            if not success:
                messages.extend(query_messages)
                return False, messages
            
            has_pk = result and len(result) > 0
            
            if has_pk:
                messages.append(f"Table {schema_name}.{table_name} already has a primary key: {result[0][0]}")
                logger.info(f"Table {schema_name}.{table_name} already has a primary key: {result[0][0]}")
                return True, messages
            
            # Add ID column
            query = f"""
                ALTER TABLE {schema_name}.{table_name}
                ADD COLUMN id SERIAL PRIMARY KEY
            """
            
            success, query_messages, _ = execute_query(query)
            
            if not success:
                messages.extend(query_messages)
                logger.error(f"Failed to add ID column to table {schema_name}.{table_name}: {query_messages}")
                return False, messages
            
            messages.append(f"Added ID column to table {schema_name}.{table_name}")
            logger.info(f"Added ID column to table {schema_name}.{table_name}")
        else:
            messages.append(f"ID column already exists in table {schema_name}.{table_name}")
            logger.info(f"ID column already exists in table {schema_name}.{table_name}")
        
        return True, messages
    except Exception as e:
        error_msg = f"Error adding ID column to table {schema_name}.{table_name}: {str(e)}"
        logger.error(error_msg, exc_info=True)
        messages.append(error_msg)
        return False, messages

def main():
    """
    Main function.
    """
    schema_name = "workflow_temp"
    
    # Tables to add audit columns to
    tables = [
        "entities",
        "entity_attributes",
        "entity_attribute_metadata",
        "entity_business_rules"
    ]
    
    # Add audit columns to tables
    for table in tables:
        logger.info(f"Adding audit columns to table {schema_name}.{table}")
        success, messages = add_audit_columns(schema_name, table)
        
        for message in messages:
            logger.info(message)
        
        if not success:
            logger.error(f"Failed to add audit columns to table {schema_name}.{table}")
    
    # Add ID columns to tables that don't have a primary key
    for table in tables:
        logger.info(f"Adding ID column to table {schema_name}.{table}")
        success, messages = add_id_column(schema_name, table)
        
        for message in messages:
            logger.info(message)
        
        if not success:
            logger.error(f"Failed to add ID column to table {schema_name}.{table}")
    
    logger.info("Done")

if __name__ == "__main__":
    main()
