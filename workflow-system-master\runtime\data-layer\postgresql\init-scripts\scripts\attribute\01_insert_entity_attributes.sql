-- Purchase Furniture Entity Attributes Insert Script
-- Inserts attributes for entities E13, E14, E15, E16

SET search_path TO workflow_runtime;

-- =====================================================
-- ENTITY ATTRIBUTES (Following exact column structure)
-- =====================================================

-- E13: FurnitureOrder Attributes
INSERT INTO entity_attributes (
    attribute_id, entity_id, name, display_name, datatype, is_required, 
    is_primary_key, is_foreign_key, is_unique, default_value, description, 
    is_calculated, calculation_formula, version, status, required, 
    calculated_field, created_at, updated_at, created_by, updated_by, 
    default_type, helper_text, natural_language
) VALUES 
('E13.At1', 'E13', 'orderid', 'Order ID', 'VARCHAR(255)', true, true, false, true, NULL, 'Unique order identifier', false, NULL, 1, 'Active', true, false, NOW(), NOW(), 'system', 'system', NULL, 'Unique identifier for the furniture order', 'Primary key for furniture orders'),
('E13.At2', 'E13', 'userid', 'User ID', 'VARCHAR(255)', true, false, true, false, NULL, 'Reference to user who placed the order', false, NULL, 1, 'Active', true, false, NOW(), NOW(), 'system', 'system', NULL, 'Links to the user table', 'Foreign key reference to users'),
('E13.At3', 'E13', 'furnituretypeid', 'Furniture Type ID', 'VARCHAR(255)', true, false, true, false, NULL, 'Reference to furniture type', false, NULL, 1, 'Active', true, false, NOW(), NOW(), 'system', 'system', NULL, 'Links to furniture type', 'Foreign key to furniture type'),
('E13.At4', 'E13', 'productid', 'Product ID', 'VARCHAR(255)', true, false, true, false, NULL, 'Reference to selected product', false, NULL, 1, 'Active', true, false, NOW(), NOW(), 'system', 'system', NULL, 'Links to product catalog', 'Foreign key to furniture product'),
('E13.At5', 'E13', 'quantity', 'Quantity', 'INTEGER', true, false, false, false, '1', 'Number of items ordered', false, NULL, 1, 'Active', true, false, NOW(), NOW(), 'system', 'system', 'default', 'Enter quantity to order', 'Quantity of items in the order'),
('E13.At6', 'E13', 'unitprice', 'Unit Price', 'DECIMAL(10,2)', true, false, false, false, NULL, 'Price per unit', false, NULL, 1, 'Active', true, false, NOW(), NOW(), 'system', 'system', NULL, 'Price per individual item', 'Unit price from product catalog'),
('E13.At7', 'E13', 'subtotal', 'Subtotal', 'DECIMAL(10,2)', true, false, false, false, NULL, 'Quantity × Unit Price', true, 'quantity * unitprice', 1, 'Active', true, true, NOW(), NOW(), 'system', 'system', 'calculated', 'Automatically calculated', 'Calculated field: quantity × unit price'),
('E13.At8', 'E13', 'gstamount', 'GST Amount', 'DECIMAL(10,2)', true, false, false, false, NULL, 'GST calculated at 18%', true, 'subtotal * 0.18', 1, 'Active', true, true, NOW(), NOW(), 'system', 'system', 'calculated', 'GST at 18%', 'Calculated GST amount'),
('E13.At9', 'E13', 'totalamount', 'Total Amount', 'DECIMAL(10,2)', true, false, false, false, NULL, 'Subtotal + GST Amount', true, 'subtotal + gstamount', 1, 'Active', true, true, NOW(), NOW(), 'system', 'system', 'calculated', 'Final total amount', 'Total including GST'),
('E13.At10', 'E13', 'paymentmethod', 'Payment Method', 'VARCHAR(20)', true, false, false, false, NULL, 'UPI or Credit Card', false, NULL, 1, 'Active', true, false, NOW(), NOW(), 'system', 'system', NULL, 'Select payment method', 'Payment method selection'),
('E13.At11', 'E13', 'orderstatus', 'Order Status', 'VARCHAR(20)', true, false, false, false, 'Pending', 'Current status of the order', false, NULL, 1, 'Active', true, false, NOW(), NOW(), 'system', 'system', 'default', 'Order processing status', 'Current order status'),
('E13.At12', 'E13', 'orderdate', 'Order Date', 'TIMESTAMP', true, false, false, false, 'NOW()', 'Date when order was placed', false, NULL, 1, 'Active', true, false, NOW(), NOW(), 'system', 'system', 'system', 'Automatically set', 'Order placement timestamp');

-- E14: FurnitureProduct Attributes
INSERT INTO entity_attributes (
    attribute_id, entity_id, name, display_name, datatype, is_required, 
    is_primary_key, is_foreign_key, is_unique, default_value, description, 
    is_calculated, calculation_formula, version, status, required, 
    calculated_field, created_at, updated_at, created_by, updated_by, 
    default_type, helper_text, natural_language
) VALUES 
('E14.At1', 'E14', 'productid', 'Product ID', 'VARCHAR(255)', true, true, false, true, NULL, 'Unique product identifier', false, NULL, 1, 'Active', true, false, NOW(), NOW(), 'system', 'system', NULL, 'Unique product code', 'Primary key for products'),
('E14.At2', 'E14', 'productname', 'Product Name', 'VARCHAR(255)', true, false, false, false, NULL, 'Name of the furniture product', false, NULL, 1, 'Active', true, false, NOW(), NOW(), 'system', 'system', NULL, 'Display name of product', 'Product display name'),
('E14.At3', 'E14', 'furnituretypeid', 'Furniture Type ID', 'VARCHAR(255)', true, false, true, false, NULL, 'Reference to furniture type', false, NULL, 1, 'Active', true, false, NOW(), NOW(), 'system', 'system', NULL, 'Product category', 'Links to furniture type'),
('E14.At4', 'E14', 'price', 'Price', 'DECIMAL(10,2)', true, false, false, false, NULL, 'Product price', false, NULL, 1, 'Active', true, false, NOW(), NOW(), 'system', 'system', NULL, 'Product selling price', 'Unit price of the product'),
('E14.At5', 'E14', 'availableinventory', 'Available Inventory', 'INTEGER', true, false, false, false, '0', 'Current stock available', false, NULL, 1, 'Active', true, false, NOW(), NOW(), 'system', 'system', 'default', 'Stock quantity', 'Current inventory count'),
('E14.At6', 'E14', 'description', 'Description', 'TEXT', false, false, false, false, NULL, 'Product description', false, NULL, 1, 'Active', false, false, NOW(), NOW(), 'system', 'system', NULL, 'Product details', 'Detailed product description');

-- E15: FurnitureType Attributes
INSERT INTO entity_attributes (
    attribute_id, entity_id, name, display_name, datatype, is_required, 
    is_primary_key, is_foreign_key, is_unique, default_value, description, 
    is_calculated, calculation_formula, version, status, required, 
    calculated_field, created_at, updated_at, created_by, updated_by, 
    default_type, helper_text, natural_language
) VALUES 
('E15.At1', 'E15', 'furnituretypeid', 'Furniture Type ID', 'VARCHAR(255)', true, true, false, true, NULL, 'Unique furniture type identifier', false, NULL, 1, 'Active', true, false, NOW(), NOW(), 'system', 'system', NULL, 'Unique type code', 'Primary key for furniture types'),
('E15.At2', 'E15', 'typename', 'Type Name', 'VARCHAR(100)', true, false, false, false, NULL, 'Name of furniture type', false, NULL, 1, 'Active', true, false, NOW(), NOW(), 'system', 'system', NULL, 'Category name', 'Display name for furniture category'),
('E15.At3', 'E15', 'description', 'Description', 'TEXT', false, false, false, false, NULL, 'Furniture type description', false, NULL, 1, 'Active', false, false, NOW(), NOW(), 'system', 'system', NULL, 'Category description', 'Detailed type description');

-- E16: PaymentDetails Attributes
INSERT INTO entity_attributes (
    attribute_id, entity_id, name, display_name, datatype, is_required, 
    is_primary_key, is_foreign_key, is_unique, default_value, description, 
    is_calculated, calculation_formula, version, status, required, 
    calculated_field, created_at, updated_at, created_by, updated_by, 
    default_type, helper_text, natural_language
) VALUES 
('E16.At1', 'E16', 'paymentid', 'Payment ID', 'VARCHAR(255)', true, true, false, true, NULL, 'Unique payment identifier', false, NULL, 1, 'Active', true, false, NOW(), NOW(), 'system', 'system', NULL, 'Unique payment reference', 'Primary key for payments'),
('E16.At2', 'E16', 'orderid', 'Order ID', 'VARCHAR(255)', true, false, true, false, NULL, 'Reference to furniture order', false, NULL, 1, 'Active', true, false, NOW(), NOW(), 'system', 'system', NULL, 'Links to order', 'Foreign key to furniture order'),
('E16.At3', 'E16', 'paymentmethod', 'Payment Method', 'VARCHAR(20)', true, false, false, false, NULL, 'UPI or Credit Card', false, NULL, 1, 'Active', true, false, NOW(), NOW(), 'system', 'system', NULL, 'Payment type', 'Method of payment'),
('E16.At4', 'E16', 'upiid', 'UPI ID', 'VARCHAR(100)', false, false, false, false, NULL, 'UPI ID for UPI payments', false, NULL, 1, 'Active', false, false, NOW(), NOW(), 'system', 'system', NULL, 'UPI identifier', 'UPI payment ID'),
('E16.At5', 'E16', 'cardname', 'Card Name', 'VARCHAR(100)', false, false, false, false, NULL, 'Name on credit card', false, NULL, 1, 'Active', false, false, NOW(), NOW(), 'system', 'system', NULL, 'Cardholder name', 'Name on the credit card'),
('E16.At6', 'E16', 'cardnumber', 'Card Number', 'VARCHAR(20)', false, false, false, false, NULL, 'Credit card number', false, NULL, 1, 'Active', false, false, NOW(), NOW(), 'system', 'system', NULL, 'Card number', 'Credit card number'),
('E16.At7', 'E16', 'cvv', 'CVV', 'VARCHAR(4)', false, false, false, false, NULL, 'Credit card CVV', false, NULL, 1, 'Active', false, false, NOW(), NOW(), 'system', 'system', NULL, 'Security code', 'Card verification value'),
('E16.At8', 'E16', 'expirydate', 'Expiry Date', 'VARCHAR(7)', false, false, false, false, NULL, 'Credit card expiry (MM/YYYY)', false, NULL, 1, 'Active', false, false, NOW(), NOW(), 'system', 'system', NULL, 'Card expiry', 'Card expiration date'),
('E16.At9', 'E16', 'paymentstatus', 'Payment Status', 'VARCHAR(20)', true, false, false, false, 'Pending', 'Status of payment', false, NULL, 1, 'Active', true, false, NOW(), NOW(), 'system', 'system', 'default', 'Payment processing status', 'Current payment status');

COMMIT;
