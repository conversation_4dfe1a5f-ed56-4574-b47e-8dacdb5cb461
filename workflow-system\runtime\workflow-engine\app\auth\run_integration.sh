#!/bin/bash
# <PERSON>ript to run the RBAC integration example

# Set up environment
echo "Setting up environment..."
export PYTHONPATH=$PYTHONPATH:$(pwd)/../../..

# Check if Python 3 is installed
if ! command -v python3 &> /dev/null; then
    echo "Error: Python 3 is not installed"
    exit 1
fi

# Check if pip3 is installed
if ! command -v pip3 &> /dev/null; then
    echo "Error: pip3 is not installed"
    exit 1
fi

# Install required packages
echo "Installing required packages..."
pip3 install -r requirements.txt

# Run the integration example
echo "Running integration example..."
python3 integration_example.py

# Check if the integration example ran successfully
if [ $? -eq 0 ]; then
    echo "Integration example ran successfully"
else
    echo "Error: Integration example failed"
    exit 1
fi

echo "Done"
