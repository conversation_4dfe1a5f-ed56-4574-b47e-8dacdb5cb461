"""
Role Deployer for YAML Builder v2

This module provides functionality for deploying parsed Role data to the database.
"""

import os
import sys
import logging
from typing import Dict, List, Tuple, Any, Optional

# Import database utilities
from db_utils import execute_query, create_table, add_column, add_constraint

# Set up logging
logger = logging.getLogger('role_deployer')

def deploy_roles(role_data: Dict, schema_name: str) -> Tuple[bool, List[str]]:
    """
    Deploy parsed Role data to the database.
    
    Args:
        role_data: Parsed Role data
        schema_name: Schema name to deploy to
        
    Returns:
        Tuple containing:
            - Bo<PERSON>an indicating if deployment was successful
            - List of messages (warnings, errors, or success messages)
    """
    messages = []
    
    try:
        logger.info(f"Deploying roles to schema {schema_name}")
        
        # Check if Role data is valid
        if not role_data or 'roles' not in role_data:
            logger.error("Invalid Role data: 'roles' key not found")
            return False, ["Invalid Role data: 'roles' key not found"]
        
        roles = role_data['roles']
        
        # Create roles table if it doesn't exist
        success, create_messages = create_roles_table(schema_name)
        messages.extend(create_messages)
        
        if not success:
            return False, messages
        
        # Create role_permissions table if it doesn't exist
        success, create_messages = create_role_permissions_table(schema_name)
        messages.extend(create_messages)
        
        if not success:
            return False, messages
        
        # Create role_special_conditions table if it doesn't exist
        success, create_messages = create_role_special_conditions_table(schema_name)
        messages.extend(create_messages)
        
        if not success:
            return False, messages
        
        # Deploy each role
        for role_id, role in roles.items():
            success, deploy_messages = deploy_role(role, schema_name)
            messages.extend(deploy_messages)
            
            if not success:
                return False, messages
        
        messages.append(f"Successfully deployed {len(roles)} roles to schema {schema_name}")
        return True, messages
    except Exception as e:
        logger.error(f"Error deploying roles to schema {schema_name}: {str(e)}", exc_info=True)
        messages.append(f"Error deploying roles to schema {schema_name}: {str(e)}")
        return False, messages

def create_roles_table(schema_name: str) -> Tuple[bool, List[str]]:
    """
    Create roles table if it doesn't exist.
    
    Args:
        schema_name: Schema name to deploy to
        
    Returns:
        Tuple containing:
            - Boolean indicating if creation was successful
            - List of messages (warnings, errors, or success messages)
    """
    return create_table(
        schema_name,
        'roles',
        """
        role_id VARCHAR(255) PRIMARY KEY,
        name VARCHAR(255) NOT NULL,
        description TEXT,
        scope VARCHAR(50),
        classification VARCHAR(50),
        tenant_id VARCHAR(255),
        inherits VARCHAR(255),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        CONSTRAINT roles_inherits_fkey FOREIGN KEY (inherits) REFERENCES {schema_name}.roles (role_id) ON DELETE SET NULL
        """.format(schema_name=schema_name)
    )

def create_role_permissions_table(schema_name: str) -> Tuple[bool, List[str]]:
    """
    Create role_permissions table if it doesn't exist.
    
    Args:
        schema_name: Schema name to deploy to
        
    Returns:
        Tuple containing:
            - Boolean indicating if creation was successful
            - List of messages (warnings, errors, or success messages)
    """
    return create_table(
        schema_name,
        'role_permissions',
        """
        permission_id SERIAL PRIMARY KEY,
        role_id VARCHAR(255) NOT NULL,
        permission TEXT NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        CONSTRAINT role_permissions_role_id_fkey FOREIGN KEY (role_id) REFERENCES {schema_name}.roles (role_id) ON DELETE CASCADE,
        CONSTRAINT role_permissions_role_id_permission_unique UNIQUE (role_id, permission)
        """.format(schema_name=schema_name)
    )

def create_role_special_conditions_table(schema_name: str) -> Tuple[bool, List[str]]:
    """
    Create role_special_conditions table if it doesn't exist.
    
    Args:
        schema_name: Schema name to deploy to
        
    Returns:
        Tuple containing:
            - Boolean indicating if creation was successful
            - List of messages (warnings, errors, or success messages)
    """
    return create_table(
        schema_name,
        'role_special_conditions',
        """
        condition_id SERIAL PRIMARY KEY,
        role_id VARCHAR(255) NOT NULL,
        condition TEXT NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        CONSTRAINT role_special_conditions_role_id_fkey FOREIGN KEY (role_id) REFERENCES {schema_name}.roles (role_id) ON DELETE CASCADE,
        CONSTRAINT role_special_conditions_role_id_condition_unique UNIQUE (role_id, condition)
        """.format(schema_name=schema_name)
    )

def deploy_role(role: Dict, schema_name: str) -> Tuple[bool, List[str]]:
    """
    Deploy a role to the database.
    
    Args:
        role: Role data
        schema_name: Schema name to deploy to
        
    Returns:
        Tuple containing:
            - Boolean indicating if deployment was successful
            - List of messages (warnings, errors, or success messages)
    """
    messages = []
    
    try:
        role_id = role.get('role_id')
        logger.info(f"Deploying role {role_id} to schema {schema_name}")
        
        # Insert role
        success, query_messages = insert_role(role, schema_name)
        messages.extend(query_messages)
        
        if not success:
            return False, messages
        
        # Insert permissions
        if 'permissions' in role:
            for permission in role['permissions']:
                success, query_messages = insert_role_permission(role_id, permission, schema_name)
                messages.extend(query_messages)
                
                if not success:
                    return False, messages
        
        # Insert special conditions
        if 'special_conditions' in role:
            for condition in role['special_conditions']:
                success, query_messages = insert_role_special_condition(role_id, condition, schema_name)
                messages.extend(query_messages)
                
                if not success:
                    return False, messages
        
        messages.append(f"Successfully deployed role {role_id} to schema {schema_name}")
        return True, messages
    except Exception as e:
        logger.error(f"Error deploying role {role.get('role_id')} to schema {schema_name}: {str(e)}", exc_info=True)
        messages.append(f"Error deploying role {role.get('role_id')} to schema {schema_name}: {str(e)}")
        return False, messages

def insert_role(role: Dict, schema_name: str) -> Tuple[bool, List[str]]:
    """
    Insert a role into the database.
    
    Args:
        role: Role data
        schema_name: Schema name to deploy to
        
    Returns:
        Tuple containing:
            - Boolean indicating if insertion was successful
            - List of messages (warnings, errors, or success messages)
    """
    messages = []
    
    try:
        role_id = role.get('role_id')
        name = role.get('name', '')
        description = role.get('description', '')
        scope = role.get('scope', '')
        classification = role.get('classification', '')
        tenant_id = role.get('tenant_id', '')
        inherits = role.get('inherits', None)
        
        # Check if role already exists
        success, query_messages, result = execute_query(
            f"""
            SELECT role_id FROM {schema_name}.roles
            WHERE role_id = %s
            """,
            (role_id,)
        )
        
        if not success:
            messages.extend(query_messages)
            return False, messages
        
        if result:
            # Role already exists, update it
            success, query_messages, _ = execute_query(
                f"""
                UPDATE {schema_name}.roles
                SET name = %s, description = %s, scope = %s, classification = %s,
                    tenant_id = %s, inherits = %s, updated_at = CURRENT_TIMESTAMP
                WHERE role_id = %s
                """,
                (name, description, scope, classification, tenant_id, inherits, role_id)
            )
            
            if not success:
                messages.extend(query_messages)
                return False, messages
            
            messages.append(f"Updated role {role_id} in schema {schema_name}")
        else:
            # Role doesn't exist, insert it
            success, query_messages, _ = execute_query(
                f"""
                INSERT INTO {schema_name}.roles
                (role_id, name, description, scope, classification, tenant_id, inherits)
                VALUES (%s, %s, %s, %s, %s, %s, %s)
                """,
                (role_id, name, description, scope, classification, tenant_id, inherits)
            )
            
            if not success:
                messages.extend(query_messages)
                return False, messages
            
            messages.append(f"Inserted role {role_id} into schema {schema_name}")
        
        return True, messages
    except Exception as e:
        logger.error(f"Error inserting role {role.get('role_id')} into schema {schema_name}: {str(e)}", exc_info=True)
        messages.append(f"Error inserting role {role.get('role_id')} into schema {schema_name}: {str(e)}")
        return False, messages

def insert_role_permission(role_id: str, permission: str, schema_name: str) -> Tuple[bool, List[str]]:
    """
    Insert a role permission into the database.
    
    Args:
        role_id: Role ID
        permission: Permission text
        schema_name: Schema name to deploy to
        
    Returns:
        Tuple containing:
            - Boolean indicating if insertion was successful
            - List of messages (warnings, errors, or success messages)
    """
    messages = []
    
    try:
        # Check if permission already exists
        success, query_messages, result = execute_query(
            f"""
            SELECT permission_id FROM {schema_name}.role_permissions
            WHERE role_id = %s AND permission = %s
            """,
            (role_id, permission)
        )
        
        if not success:
            messages.extend(query_messages)
            return False, messages
        
        if result:
            # Permission already exists, update it
            permission_id = result[0][0]
            
            success, query_messages, _ = execute_query(
                f"""
                UPDATE {schema_name}.role_permissions
                SET updated_at = CURRENT_TIMESTAMP
                WHERE permission_id = %s
                """,
                (permission_id,)
            )
            
            if not success:
                messages.extend(query_messages)
                return False, messages
            
            messages.append(f"Updated permission for role {role_id} in schema {schema_name}")
        else:
            # Permission doesn't exist, insert it
            success, query_messages, _ = execute_query(
                f"""
                INSERT INTO {schema_name}.role_permissions
                (role_id, permission)
                VALUES (%s, %s)
                """,
                (role_id, permission)
            )
            
            if not success:
                messages.extend(query_messages)
                return False, messages
            
            messages.append(f"Inserted permission for role {role_id} into schema {schema_name}")
        
        return True, messages
    except Exception as e:
        logger.error(f"Error inserting permission for role {role_id} into schema {schema_name}: {str(e)}", exc_info=True)
        messages.append(f"Error inserting permission for role {role_id} into schema {schema_name}: {str(e)}")
        return False, messages

def insert_role_special_condition(role_id: str, condition: str, schema_name: str) -> Tuple[bool, List[str]]:
    """
    Insert a role special condition into the database.
    
    Args:
        role_id: Role ID
        condition: Special condition text
        schema_name: Schema name to deploy to
        
    Returns:
        Tuple containing:
            - Boolean indicating if insertion was successful
            - List of messages (warnings, errors, or success messages)
    """
    messages = []
    
    try:
        # Check if special condition already exists
        success, query_messages, result = execute_query(
            f"""
            SELECT condition_id FROM {schema_name}.role_special_conditions
            WHERE role_id = %s AND condition = %s
            """,
            (role_id, condition)
        )
        
        if not success:
            messages.extend(query_messages)
            return False, messages
        
        if result:
            # Special condition already exists, update it
            condition_id = result[0][0]
            
            success, query_messages, _ = execute_query(
                f"""
                UPDATE {schema_name}.role_special_conditions
                SET updated_at = CURRENT_TIMESTAMP
                WHERE condition_id = %s
                """,
                (condition_id,)
            )
            
            if not success:
                messages.extend(query_messages)
                return False, messages
            
            messages.append(f"Updated special condition for role {role_id} in schema {schema_name}")
        else:
            # Special condition doesn't exist, insert it
            success, query_messages, _ = execute_query(
                f"""
                INSERT INTO {schema_name}.role_special_conditions
                (role_id, condition)
                VALUES (%s, %s)
                """,
                (role_id, condition)
            )
            
            if not success:
                messages.extend(query_messages)
                return False, messages
            
            messages.append(f"Inserted special condition for role {role_id} into schema {schema_name}")
        
        return True, messages
    except Exception as e:
        logger.error(f"Error inserting special condition for role {role_id} into schema {schema_name}: {str(e)}", exc_info=True)
        messages.append(f"Error inserting special condition for role {role_id} into schema {schema_name}: {str(e)}")
        return False, messages
