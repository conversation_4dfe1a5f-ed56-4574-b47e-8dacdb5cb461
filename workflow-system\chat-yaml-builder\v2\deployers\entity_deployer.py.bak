"""
Enhanced Entity Deployer for YAML Builder v2

This module provides improved functionality for deploying entity components to the database.
Key enhancements:
1. Uses sequential IDs (E1, E2) for entities
2. Uses format E1.At1, E1.At2, etc. for attribute IDs
3. <PERSON><PERSON><PERSON> populates description, metadata, and lifecycle fields
4. <PERSON><PERSON><PERSON> populates the entity_attribute_metadata table
5. <PERSON><PERSON><PERSON> handles calculated fields, formulas, and dependencies
6. <PERSON><PERSON><PERSON> handles relationships
7. <PERSON><PERSON><PERSON> handles business rules
8. Creates actual tables for entities with the naming convention e1.entityname
"""

import os
import sys
import json
import logging
import re
from typing import Dict, List, Tuple, Any, Optional

# Add parent directory to path to import common modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from db_utils import execute_query, save_to_mongodb

# Set up logging
logger = logging.getLogger('entity_deployer_v2')

def check_column_exists(schema_name: str, table_name: str, column_name: str) -> bool:
    """
    Check if a column exists in a table.
    
    Args:
        schema_name: Schema name
        table_name: Table name
        column_name: Column name
        
    Returns:
        <PERSON>olean indicating if the column exists
    """
    try:
        success, query_messages, result = execute_query(
            """
            SELECT EXISTS (
                SELECT FROM information_schema.columns 
                WHERE table_schema = %s
                AND table_name = %s
                AND column_name = %s
            )
            """,
            (schema_name, table_name, column_name)
        )
        
        if not success:
            logger.error(f"Error checking if column {column_name} exists: {query_messages}")
            return False
        
        return result and result[0][0]
    except Exception as e:
        logger.error(f"Error checking if column {column_name} exists: {str(e)}")
        return False

def get_next_entity_id(schema_name: str) -> str:
    """
    Get the next sequential entity ID (E1, E2, etc.).
    
    Args:
        schema_name: Schema name
        
    Returns:
        Next entity ID
    """
    try:
        # Check if any entities exist
        success, query_messages, result = execute_query(
            f"""
            SELECT entity_id FROM {schema_name}.entities 
            WHERE entity_id LIKE 'E%' 
            ORDER BY CAST(SUBSTRING(entity_id FROM 2) AS INTEGER) DESC 
            LIMIT 1
            """,
            schema_name=schema_name
        )
        
        if not success:
            logger.error(f"Error getting next entity ID: {query_messages}")
            return "E1"
        
        if not result:
            return "E1"
        
        # Extract the number from the entity ID
        last_id = result[0][0]
        match = re.search(r'E(\d+)', last_id)
        if not match:
            return "E1"
        
        last_num = int(match.group(1))
        return f"E{last_num + 1}"
    except Exception as e:
        logger.error(f"Error getting next entity ID: {str(e)}")
        return "E1"

def get_next_attribute_id(entity_id: str, schema_name: str) -> str:
    """
    Get the next sequential attribute ID (E1.At1, E1.At2, etc.).
    
    Args:
        entity_id: Entity ID
        schema_name: Schema name
        
    Returns:
        Next attribute ID
    """
    try:
        # Check if any attributes exist for this entity
        success, query_messages, result = execute_query(
            f"""
            SELECT attribute_id FROM {schema_name}.entity_attributes 
            WHERE entity_id = %s AND attribute_id LIKE %s
            ORDER BY CAST(SUBSTRING(attribute_id FROM LENGTH(%s) + 4) AS INTEGER) DESC 
            LIMIT 1
            """,
            (entity_id, f"{entity_id}.At%", entity_id),
            schema_name
        )
        
        if not success:
            logger.error(f"Error getting next attribute ID: {query_messages}")
            return f"{entity_id}.At1"
        
        if not result:
            return f"{entity_id}.At1"
        
        # Extract the number from the attribute ID
        last_id = result[0][0]
        match = re.search(rf'{entity_id}\.At(\d+)', last_id)
        if not match:
            return f"{entity_id}.At1"
        
        last_num = int(match.group(1))
        return f"{entity_id}.At{last_num + 1}"
    except Exception as e:
        logger.error(f"Error getting next attribute ID: {str(e)}")
        return f"{entity_id}.At1"

def deploy_entities(entities_data: Dict, schema_name: str) -> Tuple[bool, List[str]]:
    """
    Deploy entities to the database.
    
    Args:
        entities_data: Parsed YAML data for entities
        schema_name: Schema name to deploy to
        
    Returns:
        Tuple containing:
            - Boolean indicating if deployment was successful
            - List of messages (warnings, errors, or success messages)
    """
    messages = []
    logger.info(f"Deploying entities to {schema_name}")
    
    try:
        # Check if entities key exists
        if 'entities' not in entities_data:
            logger.error("Missing 'entities' key in entities definition")
            return False, ["Missing 'entities' key in entities definition"]
        
        # Deploy each entity
        for entity_name, entity_def in entities_data['entities'].items():
            success, entity_messages = deploy_single_entity(entity_name, entity_def, schema_name)
            messages.extend(entity_messages)
            
            if not success:
                return False, messages
        
        # Save to MongoDB for design-time storage
        document = {
            "component_type": "entities",
            "version_type": "v2",
            "data": entities_data
        }
        mongo_success, mongo_messages, _ = save_to_mongodb("components", document)
        messages.extend(mongo_messages)
        
        if not mongo_success:
            logger.warning("Failed to save entities to MongoDB")
        
        logger.info("Entity deployment completed successfully")
        return True, messages
    except Exception as e:
        error_msg = f"Entity deployment error: {str(e)}"
        logger.error(error_msg, exc_info=True)
        messages.append(error_msg)
        return False, messages

def deploy_single_entity(entity_name: str, entity_def: Dict, schema_name: str) -> Tuple[bool, List[str]]:
    """
    Deploy a single entity to the database.
    
    Args:
        entity_name: Name of the entity
        entity_def: Entity definition
        schema_name: Schema name to deploy to
        
    Returns:
        Tuple containing:
            - Boolean indicating if deployment was successful
            - List of messages (warnings, errors, or success messages)
    """
    messages = []
    
    try:
        # Generate entity ID
        entity_id = get_next_entity_id(schema_name)
        
        # Check if entity exists
        success, query_messages, result = execute_query(
            f"SELECT entity_id FROM {schema_name}.entities WHERE name = %s",
            (entity_name,),
            schema_name
        )
        
        if not success:
            messages.extend(query_messages)
            return False, messages
        
        entity_exists = result and len(result) > 0
        
        if entity_exists:
            # Use existing entity ID
            entity_id = result[0][0]
            logger.info(f"Using existing entity ID {entity_id} for {entity_name}")
            
            # Update existing entity
            query = f"""
                UPDATE {schema_name}.entities
                SET description = %s,
                    metadata = %s,
                    lifecycle_management = %s,
                    version_type = 'v2',
                    updated_at = CURRENT_TIMESTAMP
                WHERE entity_id = %s
            """
            params = (
                entity_def.get('description', ''),
                json.dumps(entity_def.get('metadata', {})),
                json.dumps(entity_def.get('lifecycle_management', {})),
                entity_id
            )
            
            success, query_messages, _ = execute_query(query, params, schema_name)
            
            if not success:
                messages.extend(query_messages)
                return False, messages
            
            messages.append(f"Updated entity '{entity_name}' in {schema_name}.entities")
            logger.info(f"Updated entity '{entity_name}' in {schema_name}.entities")
        else:
            # Insert new entity
            query = f"""
                INSERT INTO {schema_name}.entities (
                    entity_id, name, description, metadata, lifecycle_management, version_type
                ) VALUES (%s, %s, %s, %s, %s, 'v2')
            """
            params = (
                entity_id,
                entity_name,
                entity_def.get('description', ''),
                json.dumps(entity_def.get('metadata', {})),
                json.dumps(entity_def.get('lifecycle_management', {}))
            )
            
            success, query_messages, _ = execute_query(query, params, schema_name)
            
            if not success:
                messages.extend(query_messages)
                return False, messages
            
            messages.append(f"Inserted entity '{entity_name}' into {schema_name}.entities")
            logger.info(f"Inserted entity '{entity_name}' into {schema_name}.entities")
        
        # Deploy entity attributes
        if 'attributes' in entity_def:
            success, attr_messages = deploy_entity_attributes(entity_id, entity_name, entity_def['attributes'], schema_name)
            messages.extend(attr_messages)
            
            if not success:
                return False, messages
        
        # Deploy entity relationships
        if 'relationships' in entity_def:
            success, rel_messages = deploy_entity_relationships(entity_id, entity_def['relationships'], schema_name)
            messages.extend(rel_messages)
            
            if not success:
                return False, messages
        
        # Deploy entity business rules
        if 'business_rules' in entity_def:
            success, rule_messages = deploy_entity_business_rules(entity_id, entity_def['business_rules'], schema_name)
            messages.extend(rule_messages)
            
            if not success:
                return False, messages
        
        # Deploy calculated fields
        if 'calculated_fields' in entity_def:
            success, calc_messages = deploy_calculated_fields(entity_id, entity_def['calculated_fields'], schema_name)
            messages.extend(calc_messages)
            
            if not success:
                return False, messages
        
        # Deploy lifecycle management
        success, lifecycle_messages = deploy_lifecycle_management(entity_id, entity_def, schema_name)
        messages.extend(lifecycle_messages)
        
        if not success:
            return False, messages
        
        # Create entity table
        success, table_messages = create_entity_table(entity_id, entity_name, entity_def, schema_name)
        messages.extend(table_messages)
        
        if not success:
            return False, messages
        
        return True, messages
    except Exception as e:
        error_msg = f"Error deploying entity '{entity_name}': {str(e)}"
        logger.error(error_msg, exc_info=True)
        messages.append(error_msg)
        return False, messages

def deploy_entity_attributes(entity_id: str, entity_name: str, attributes: Dict, schema_name: str) -> Tuple[bool, List[str]]:
    """
    Deploy entity attributes to the database.
    
    Args:
        entity_id: ID of the entity
        entity_name: Name of the entity
        attributes: Dictionary of attributes
        schema_name: Schema name to deploy to
        
    Returns:
        Tuple containing:
            - Boolean indicating if deployment was successful
            - List of messages (warnings, errors, or success messages)
    """
    messages = []
    
    try:
        # Delete existing attributes
        success, query_messages, _ = execute_query(
            f"DELETE FROM {schema_name}.entity_attributes WHERE entity_id = %s",
            (entity_id,),
            schema_name
        )
        
        if not success:
            messages.extend(query_messages)
            return False, messages
        
        # Delete existing attribute metadata
        success, query_messages, _ = execute_query(
            f"DELETE FROM {schema_name}.entity_attribute_metadata WHERE entity_id = %s",
            (entity_id,),
            schema_name
        )
        
        if not success:
            messages.extend(query_messages)
            return False, messages
        
        # Insert new attributes
        for attr_name, attr_def in attributes.items():
            # Generate attribute ID
            attr_id = get_next_attribute_id(entity_id, schema_name)
            
            # Check if the additional columns exist
            has_display_name_column = check_column_exists(schema_name, "entity_attributes", "display_name")
            has_datatype_column = check_column_exists(schema_name, "entity_attributes", "datatype")
            has_status_column = check_column_exists(schema_name, "entity_attributes", "status")
            
            # Insert attribute
            if has_display_name_column and has_datatype_column and has_status_column:
                # All additional columns exist
                query = f"""
                    INSERT INTO {schema_name}.entity_attributes (
                        attribute_id, entity_id, name, type, required, default_value, 
                        calculated_field, calculation_formula, dependencies,
                        display_name, datatype, status
                    ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                """
            else:
                # Some or all additional columns don't exist
                query = f"""
                    INSERT INTO {schema_name}.entity_attributes (
                        attribute_id, entity_id, name, type, required, default_value, 
                        calculated_field, calculation_formula, dependencies
                    ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s)
                """
            
            # Determine if this is a calculated field
            is_calculated = attr_def.get('calculated', False)
            formula = None
            dependencies = []
            
            # Check if this attribute has a formula in calculated_fields
            if 'calculated_fields' in attr_def:
                for field_id, field_info in attr_def['calculated_fields'].items():
                    if field_info.get('attribute') == attr_name:
                        is_calculated = True
                        formula = field_info.get('formula', '')
                        dependencies_str = field_info.get('dependencies', '')
                        if dependencies_str:
                            dependencies = [dep.strip() for dep in dependencies_str.split(',')]
            
            if has_display_name_column and has_datatype_column and has_status_column:
                params = (
                    attr_id,
                    entity_id,
                    attr_name,
                    attr_def.get('type', 'string'),
                    attr_def.get('required', False),
                    attr_def.get('default', None),
                    is_calculated,
                    formula,
                    json.dumps(dependencies),
                    attr_def.get('display_name', attr_name),  # Default display_name to attribute name
                    attr_def.get('datatype', 'string'),  # Default datatype to 'string'
                    attr_def.get('status', 'active')  # Default status to 'active'
                )
            else:
                params = (
                    attr_id,
                    entity_id,
                    attr_name,
                    attr_def.get('type', 'string'),
                    attr_def.get('required', False),
                    attr_def.get('default', None),
                    is_calculated,
                    formula,
                    json.dumps(dependencies)
                )
            
            success, query_messages, _ = execute_query(query, params, schema_name)
            
            if not success:
                messages.extend(query_messages)
                return False, messages
            
            messages.append(f"Inserted attribute '{attr_name}' for entity '{entity_id}' into {schema_name}.entity_attributes")
            logger.info(f"Inserted attribute '{attr_name}' for entity '{entity_id}' into {schema_name}.entity_attributes")
            
            # Insert attribute metadata
            query = f"""
                INSERT INTO {schema_name}.entity_attribute_metadata (
                    entity_id, attribute_id, attribute_name, required
                ) VALUES (%s, %s, %s, %s)
            """
            params = (
                entity_id,
                attr_id,
                attr_name,
                attr_def.get('required', False)
            )
            
            success, query_messages, _ = execute_query(query, params, schema_name)
            
            if not success:
                messages.extend(query_messages)
                return False, messages
            
            messages.append(f"Inserted attribute metadata for '{attr_name}' into {schema_name}.entity_attribute_metadata")
            logger.info(f"Inserted attribute metadata for '{attr_name}' into {schema_name}.entity_attribute_metadata")
            
            # Deploy enum values if present
            if 'enum_values' in attr_def:
                enum_values = attr_def['enum_values']
                success, enum_messages = deploy_enum_values(attr_id, enum_values, schema_name)
                messages.extend(enum_messages)
                
                if not success:
                    return False, messages
            
            # Deploy validations if present
            if 'validations' in attr_def:
                success, val_messages = deploy_validations(attr_id, attr_def['validations'], schema_name)
                messages.extend(val_messages)
                
                if not success:
                    return False, messages
        
        return True, messages
    except Exception as e:
        error_msg = f"Error deploying attributes for entity '{entity_id}': {str(e)}"
        logger.error(error_msg, exc_info=True)
        messages.append(error_msg)
        return False, messages

def deploy_entity_relationships(entity_id: str, relationships: Dict, schema_name: str) -> Tuple[bool, List[str]]:
    """
    Deploy entity relationships to the database.
    
    Args:
        entity_id: ID of the entity
        relationships: Dictionary of relationships
        schema_name: Schema name to deploy to
        
    Returns:
        Tuple containing:
            - Boolean indicating if deployment was successful
            - List of messages (warnings, errors, or success messages)
    """
    messages = []
    
    try:
        # Delete existing relationships
        success, query_messages, _ = execute_query(
            f"DELETE FROM {schema_name}.entity_relationships WHERE source_entity_id = %s",
            (entity_id,),
            schema_name
        )
        
        if not success:
            messages.extend(query_messages)
            return False, messages
        
        # Insert new relationships
        for rel_name, rel_def in relationships.items():
            # Get target entity ID
            target_entity_name = rel_def.get('entity')
            if not target_entity_name:
                messages.append(f"Warning: Relationship '{rel_name}' in entity '{entity_id}' is missing 'entity'")
                continue
            
            # Look up target entity ID by name
            success, query_messages, result = execute_query(
                f"SELECT entity_id FROM {schema_name}.entities WHERE name = %s",
                (target_entity_name,),
                schema_name
            )
            
            if not success:
                messages.extend(query_messages)
                return False, messages
            
            if not result:
                warning_msg = f"Warning: Relationship '{rel_name}' in entity '{entity_id}' references non-existent entity '{target_entity_name}'"
                messages.append(warning_msg)
                logger.warning(warning_msg)
                continue
            
            target_entity_id = result[0][0]
            
            # Get source and target attribute IDs
            source_attr_name = rel_def.get('source_attribute')
            target_attr_name = rel_def.get('target_attribute')
            
            source_attr_id = None
            target_attr_id = None
            
            if source_attr_name:
                # Look up source attribute ID by name
                success, query_messages, result = execute_query(
                    f"SELECT attribute_id FROM {schema_name}.entity_attributes WHERE entity_id = %s AND name = %s",
                    (entity_id, source_attr_name),
                    schema_name
                )
                
                if success and result:
                    source_attr_id = result[0][0]
            
            if target_attr_name:
                # Look up target attribute ID by name
                success, query_messages, result = execute_query(
                    f"SELECT attribute_id FROM {schema_name}.entity_attributes WHERE entity_id = %s AND name = %s",
                    (target_entity_id, target_attr_name),
                    schema_name
                )
                
                if success and result:
                    target_attr_id = result[0][0]
            
            # Skip if target attribute doesn't exist
            if not target_attr_id:
                warning_msg = f"Warning: Skipping relationship '{rel_name}' in entity '{entity_id}' because target attribute '{target_attr_name}' doesn't exist"
                messages.append(warning_msg)
                logger.warning(warning_msg)
                continue
                
            # Skip if source attribute doesn't exist
            if not source_attr_id:
                warning_msg = f"Warning: Skipping relationship '{rel_name}' in entity '{entity_id}' because source attribute '{source_attr_name}' doesn't exist"
                messages.append(warning_msg)
                logger.warning(warning_msg)
                continue
                
            # Insert relationship
            query = f"""
                INSERT INTO {schema_name}.entity_relationships (
                    source_entity_id, target_entity_id, relationship_type, 
                    source_attribute_id, target_attribute_id
                ) VALUES (%s, %s, %s, %s, %s)
            """
            params = (
                entity_id,
                target_entity_id,
                rel_def.get('type', 'association'),
                source_attr_id,
                target_attr_id
            )
            
            success, query_messages, _ = execute_query(query, params, schema_name)
            
            if not success:
                messages.extend(query_messages)
                logger.warning(f"Failed to insert relationship '{rel_name}': {query_messages}")
                continue  # Continue with other relationships instead of failing
            
            messages.append(f"Inserted relationship '{rel_name}' for entity '{entity_id}' into {schema_name}.entity_relationships")
            logger.info(f"Inserted relationship '{rel_name}' for entity '{entity_id}' into {schema_name}.entity_relationships")
        
        return True, messages
    except Exception as e:
        error_msg = f"Error deploying relationships for entity '{entity_id}': {str(e)}"
        logger.error(error_msg, exc_info=True)
        messages.append(error_msg)
        return False, messages

def deploy_entity_business_rules(entity_id: str, business_rules: Dict, schema_name: str) -> Tuple[bool, List[str]]:
    """
    Deploy entity business rules to the database.
    
    Args:
        entity_id: ID of the entity
        business_rules: Dictionary of business rules
        schema_name: Schema name to deploy to
        
    Returns:
        Tuple containing:
            - Boolean indicating if deployment was successful
            - List of messages (warnings, errors, or success messages)
    """
    messages = []
    
    try:
        # Delete existing business rules
        success, query_messages, _ = execute_query(
            f"DELETE FROM {schema_name}.entity_business_rules WHERE entity_id = %s",
            (entity_id,),
            schema_name
        )
        
        if not success:
            messages.extend(query_messages)
            return False, messages
        
        # Insert new business rules
        for rule_name, rule_def in business_rules.items():
            # Generate rule ID
            rule_id = f"{entity_id}_rule_{rule_name.lower().replace(' ', '_')}"
            
            # Get conditions as a string
            conditions = rule_def.get('conditions', [])
            condition_str = '\n'.join(conditions) if conditions else ''
            
            query = f"""
                INSERT INTO {schema_name}.entity_business_rules (
                    rule_id, entity_id, name, description, condition
                ) VALUES (%s, %s, %s, %s, %s)
            """
            params = (
                rule_id,
                entity_id,
                rule_name,
                rule_def.get('description', ''),
                condition_str
            )
            
            success, query_messages, _ = execute_query(query, params, schema_name)
            
            if not success:
                messages.extend(query_messages)
                return False, messages
            
            messages.append(f"Inserted business rule '{rule_name}' for entity '{entity_id}' into {schema_name}.entity_business_rules")
            logger.info(f"Inserted business rule '{rule_name}' for entity '{entity_id}' into {schema_name}.entity_business_rules")
        
        return True, messages
    except Exception as e:
        error_msg = f"Error deploying business rules for entity '{entity_id}': {str(e)}"
        logger.error(error_msg, exc_info=True)
        messages.append(error_msg)
        return False, messages

def sanitize_column_name(name: str) -> str:
    """
    Sanitize column name for SQL.
    
    Args:
        name: Column name
        
    Returns:
        Sanitized column name
    """
    # Replace special characters with underscores
    sanitized = re.sub(r'[^a-zA-Z0-9_]', '_', name)
    
    # Ensure the name doesn't start with a number
    if sanitized and sanitized[0].isdigit():
        sanitized = f"_{sanitized}"
    
    return sanitized

def create_entity_table(entity_id: str, entity_name: str, entity_def: Dict, schema_name: str) -> Tuple[bool, List[str]]:
    """
    Create a table for the entity.
    
    Args:
        entity_id: ID of the entity
        entity_name: Name of the entity
        entity_def: Entity definition
        schema_name: Schema name to deploy to
        
    Returns:
        Tuple containing:
            - Boolean indicating if creation was successful
            - List of messages (warnings, errors, or success messages)
    """
    messages = []
    
    try:
        # Generate table name
        # Format: e1.entityname
        entity_num = entity_id[1:]  # Remove 'E' prefix
        table_name = f"e{entity_num}.{entity_name.lower()}"
        
        # Check if table exists
        success, query_messages, result = execute_query(
            f"""
            SELECT EXISTS (
                SELECT FROM information_schema.tables 
                WHERE table_schema = %s
                AND table_name = %s
            )
            """,
            (schema_name, table_name)
        )
        
        if not success:
            messages.extend(query_messages)
            return False, messages
        
        table_exists = result and result[0][0]
        
        if table_exists:
            # Table exists, check if we need to add new columns
            success, attr_messages = update_entity_table(entity_id, entity_name, entity_def, schema_name, table_name)
            messages.extend(attr_messages)
            return success, messages
        
        # Generate CREATE TABLE statement
        columns = []
        primary_key = None
        
        # Always add ID column as auto-incrementing
        columns.insert(0, f"id SERIAL")
        
        # Check if primary key is defined in the prescriptive text
        has_pk = False
        
        if 'attributes' in entity_def:
            for attr_name, attr_def in entity_def['attributes'].items():
                # Sanitize attribute name for SQL
                sanitized_attr_name = sanitize_column_name(attr_name)
                
                if attr_def.get('primary_key', False):
                    has_pk = True
                    primary_key = sanitized_attr_name
                    
                    # Add primary key column
                    column_type = get_sql_type(attr_def.get('type', 'string'))
                    columns.append(f"{sanitized_attr_name} {column_type} PRIMARY KEY")
                else:
                    # Add regular column
                    column_type = get_sql_type(attr_def.get('type', 'string'))
                    nullable = "NOT NULL" if attr_def.get('required', False) else ""
                    default = f"DEFAULT {attr_def.get('default')}" if 'default' in attr_def else ""
                    columns.append(f"{sanitized_attr_name} {column_type} {nullable} {default}".strip())
        
        # If no primary key is defined, use the id column as primary key
        if not has_pk:
            # Replace the id column definition to make it the primary key
            columns[0] = f"id SERIAL PRIMARY KEY"
        
        # Add audit columns
        columns.append("created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP")
        columns.append("created_by VARCHAR(100) DEFAULT 'system'")
        columns.append("updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP")
        columns.append("updated_by VARCHAR(100) DEFAULT 'system'")
        
        # Create table
        query = f"""
            CREATE TABLE {schema_name}."{table_name}" (
                {', '.join(columns)}
            )
        """
        
        success, query_messages, _ = execute_query(query)
        
        if not success:
            messages.extend(query_messages)
            return False, messages
        
        messages.append(f"Created table {schema_name}.{table_name}")
        logger.info(f"Created table {schema_name}.{table_name}")
        
        return True, messages
    except Exception as e:
        error_msg = f"Error creating table for entity '{entity_id}': {str(e)}"
        logger.error(error_msg, exc_info=True)
        messages.append(error_msg)
        return False, messages

def update_entity_table(entity_id: str, entity_name: str, entity_def: Dict, schema_name: str, table_name: str) -> Tuple[bool, List[str]]:
    """
    Update an existing entity table with new columns.
    
    Args:
        entity_id: ID of the entity
        entity_name: Name of the entity
        entity_def: Entity definition
        schema_name: Schema name
        table_name: Table name
        
    Returns:
        Tuple containing:
            - Boolean indicating if update was successful
            - List of messages (warnings, errors, or success messages)
    """
    messages = []
    
    try:
        # Get existing columns
        success, query_messages, existing_columns = execute_query(
            f"""
            SELECT column_name 
            FROM information_schema.columns 
            WHERE table_schema = %s
            AND table_name = %s
            """,
            (schema_name, table_name)
        )
        
        if not success:
            messages.extend(query_messages)
            return False, messages
        
        existing_column_names = [col[0] for col in existing_columns]
        
        # Check for new columns to add
        if 'attributes' in entity_def:
            for attr_name, attr_def in entity_def['attributes'].items():
                # Sanitize attribute name for SQL
                sanitized_attr_name = sanitize_column_name(attr_name)
                
                # Skip if the column already exists
                if sanitized_attr_name in existing_column_names:
                    logger.info(f"Column '{sanitized_attr_name}' already exists in table {schema_name}.{table_name}")
                    continue
                
                # Add new column
                column_type = get_sql_type(attr_def.get('type', 'string'))
                nullable = "NOT NULL" if attr_def.get('required', False) else ""
                default = f"DEFAULT {attr_def.get('default')}" if 'default' in attr_def else ""
                
                query = f"""
                    ALTER TABLE {schema_name}."{table_name}"
                    ADD COLUMN {sanitized_attr_name} {column_type} {nullable} {default}
                """
                
                success, query_messages, _ = execute_query(query)
                
                if not success:
                    messages.append(f"Warning: Failed to add column '{attr_name}' to table {schema_name}.{table_name}: {query_messages}")
                    logger.warning(f"Failed to add column '{attr_name}' to table {schema_name}.{table_name}: {query_messages}")
                    continue  # Continue with other columns instead of failing
                
                messages.append(f"Added column '{attr_name}' to table {schema_name}.{table_name}")
                logger.info(f"Added column '{attr_name}' to table {schema_name}.{table_name}")
        
        # Check for audit columns
        audit_columns = ["created_at", "created_by", "updated_at", "updated_by"]
        for col in audit_columns:
            if col not in existing_column_names:
                # Add audit column
                if col in ["created_at", "updated_at"]:
                    query = f"""
                        ALTER TABLE {schema_name}."{table_name}"
                        ADD COLUMN {col} TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    """
                else:  # created_by, updated_by
                    query = f"""
                        ALTER TABLE {schema_name}."{table_name}"
                        ADD COLUMN {col} VARCHAR(100) DEFAULT 'system'
                    """
                
                success, query_messages, _ = execute_query(query)
                
                if not success:
                    messages.extend(query_messages)
                    return False, messages
                
                messages.append(f"Added audit column '{col}' to table {schema_name}.{table_name}")
                logger.info(f"Added audit column '{col}' to table {schema_name}.{table_name}")
        
        return True, messages
    except Exception as e:
        error_msg = f"Error updating table for entity '{entity_id}': {str(e)}"
        logger.error(error_msg, exc_info=True)
        messages.append(error_msg)
        return False, messages

def deploy_enum_values(attribute_id: str, enum_values: List[str], schema_name: str) -> Tuple[bool, List[str]]:
    """
    Deploy enum values for an attribute.
    
    Args:
        attribute_id: ID of the attribute
        enum_values: List of enum values
        schema_name: Schema name to deploy to
        
    Returns:
        Tuple containing:
            - Boolean indicating if deployment was successful
            - List of messages (warnings, errors, or success messages)
    """
    messages = []
    
    try:
        # Delete existing enum values
        success, query_messages, _ = execute_query(
            f"DELETE FROM {schema_name}.attribute_enum_values WHERE attribute_id = %s",
            (attribute_id,),
            schema_name
        )
        
        if not success:
            messages.extend(query_messages)
            return False, messages
        
        # Insert new enum values
        for i, value in enumerate(enum_values):
            query = f"""
                INSERT INTO {schema_name}.attribute_enum_values (
                    attribute_id, value, display_name, sort_order
                ) VALUES (%s, %s, %s, %s)
            """
            params = (
                attribute_id,
                value,
                value,  # Use value as display_name
                i + 1   # Sort order starting from 1
            )
            
            success, query_messages, _ = execute_query(query, params, schema_name)
            
            if not success:
                messages.extend(query_messages)
                return False, messages
            
            messages.append(f"Inserted enum value '{value}' for attribute '{attribute_id}' into {schema_name}.attribute_enum_values")
            logger.info(f"Inserted enum value '{value}' for attribute '{attribute_id}' into {schema_name}.attribute_enum_values")
        
        return True, messages
    except Exception as e:
        error_msg = f"Error deploying enum values for attribute '{attribute_id}': {str(e)}"
        logger.error(error_msg, exc_info=True)
        messages.append(error_msg)
        return False, messages

def deploy_validations(attribute_id: str, validations: Dict, schema_name: str) -> Tuple[bool, List[str]]:
    """
    Deploy validations for an attribute.
    
    Args:
        attribute_id: ID of the attribute
        validations: Dictionary of validations
        schema_name: Schema name to deploy to
        
    Returns:
        Tuple containing:
            - Boolean indicating if deployment was successful
            - List of messages (warnings, errors, or success messages)
    """
    messages = []
    
    try:
        # Delete existing validations
        success, query_messages, _ = execute_query(
            f"DELETE FROM {schema_name}.attribute_validations WHERE attribute_id = %s",
            (attribute_id,),
            schema_name
        )
        
        if not success:
            messages.extend(query_messages)
            return False, messages
        
        # Insert new validations
        for val_name, val_def in validations.items():
            query = f"""
                INSERT INTO {schema_name}.attribute_validations (
                    attribute_id, validation_name, validation_type, validation_expression, error_message,
                    created_at, created_by, updated_at, updated_by
                ) VALUES (%s, %s, %s, %s, %s, NOW(), 'system', NOW(), 'system')
            """
            params = (
                attribute_id,
                val_name,
                val_def.get('type', 'expression'),
                val_def.get('constraint', ''),
                val_def.get('error_message', f"Invalid value for {val_name}")
            )
            
            success, query_messages, _ = execute_query(query, params, schema_name)
            
            if not success:
                messages.extend(query_messages)
                return False, messages
            
            messages.append(f"Inserted validation '{val_name}' for attribute '{attribute_id}' into {schema_name}.attribute_validations")
            logger.info(f"Inserted validation '{val_name}' for attribute '{attribute_id}' into {schema_name}.attribute_validations")
        
        return True, messages
    except Exception as e:
        error_msg = f"Error deploying validations for attribute '{attribute_id}': {str(e)}"
        logger.error(error_msg, exc_info=True)
        messages.append(error_msg)
        return False, messages

def deploy_calculated_fields(entity_id: str, calculated_fields: Dict, schema_name: str) -> Tuple[bool, List[str]]:
    """
    Deploy calculated fields for an entity.
    
    Args:
        entity_id: ID of the entity
        calculated_fields: Dictionary of calculated fields
        schema_name: Schema name to deploy to
        
    Returns:
        Tuple containing:
            - Boolean indicating if deployment was successful
            - List of messages (warnings, errors, or success messages)
    """
    messages = []
    
    try:
        # Delete existing calculated fields for this entity
        success, query_messages, _ = execute_query(
            f"""
            DELETE FROM {schema_name}.calculated_fields
            WHERE attribute_id IN (
                SELECT attribute_id FROM {schema_name}.entity_attributes
                WHERE entity_id = %s
            )
            """,
            (entity_id,),
            schema_name
        )
        
        if not success:
            messages.extend(query_messages)
            return False, messages
        
        # Insert new calculated fields
        for field_id, field_def in calculated_fields.items():
            # Get attribute ID
            attr_name = field_def.get('attribute')
            if not attr_name:
                messages.append(f"Warning: Calculated field '{field_id}' is missing 'attribute'")
                continue
            
            # Look up attribute ID by name
            success, query_messages, result = execute_query(
                f"SELECT attribute_id FROM {schema_name}.entity_attributes WHERE entity_id = %s AND name = %s",
                (entity_id, attr_name),
                schema_name
            )
            
            if not success:
                messages.extend(query_messages)
                return False, messages
            
            if not result:
                warning_msg = f"Warning: Calculated field '{field_id}' references non-existent attribute '{attr_name}'"
                messages.append(warning_msg)
                logger.warning(warning_msg)
                continue
            
            attribute_id = result[0][0]
            
            # Parse dependencies
            dependencies_str = field_def.get('dependencies', '')
            dependencies = []
            if dependencies_str:
                dependencies = [dep.strip() for dep in dependencies_str.split(',')]
            
            # Insert calculated field
            query = f"""
                INSERT INTO {schema_name}.calculated_fields (
                    field_id, attribute_id, formula, logic_layer, caching, dependencies
                ) VALUES (%s, %s, %s, %s, %s, %s)
            """
            params = (
                field_id,
                attribute_id,
                field_def.get('formula', ''),
                field_def.get('logic_layer', 'Application'),
                field_def.get('caching', 'None'),
                json.dumps(dependencies)
            )
            
            success, query_messages, _ = execute_query(query, params, schema_name)
            
            if not success:
                messages.extend(query_messages)
                return False, messages
            
            messages.append(f"Inserted calculated field '{field_id}' for attribute '{attribute_id}' into {schema_name}.calculated_fields")
            logger.info(f"Inserted calculated field '{field_id}' for attribute '{attribute_id}' into {schema_name}.calculated_fields")
            
            # Update the attribute to mark it as calculated
            query = f"""
                UPDATE {schema_name}.entity_attributes
                SET calculated_field = TRUE,
                    calculation_formula = %s,
                    dependencies = %s
                WHERE attribute_id = %s
            """
            params = (
                field_def.get('formula', ''),
                json.dumps(dependencies),
                attribute_id
            )
            
            success, query_messages, _ = execute_query(query, params, schema_name)
            
            if not success:
                messages.extend(query_messages)
                return False, messages
            
            messages.append(f"Updated attribute '{attribute_id}' to mark it as calculated")
            logger.info(f"Updated attribute '{attribute_id}' to mark it as calculated")
        
        return True, messages
    except Exception as e:
        error_msg = f"Error deploying calculated fields for entity '{entity_id}': {str(e)}"
        logger.error(error_msg, exc_info=True)
        messages.append(error_msg)
        return False, messages

def deploy_lifecycle_management(entity_id: str, entity_def: Dict, schema_name: str) -> Tuple[bool, List[str]]:
    """
    Deploy lifecycle management for an entity.
    
    Args:
        entity_id: ID of the entity
        entity_def: Entity definition
        schema_name: Schema name to deploy to
        
    Returns:
        Tuple containing:
            - Boolean indicating if deployment was successful
            - List of messages (warnings, errors, or success messages)
    """
    messages = []
    
    try:
        # Delete existing lifecycle management for this entity
        success, query_messages, _ = execute_query(
            f"DELETE FROM {schema_name}.entity_lifecycle_management WHERE entity_id = %s",
            (entity_id,),
            schema_name
        )
        
        if not success:
            messages.extend(query_messages)
            return False, messages
        
        # Deploy archive strategy
        if 'archive_strategy' in entity_def:
            archive_strategy = entity_def['archive_strategy']
            
            query = f"""
                INSERT INTO {schema_name}.entity_lifecycle_management (
                    entity_id, management_type, trigger_type, criteria, retention, storage, access_pattern, restoration
                ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
            """
            params = (
                entity_id,
                'archive',
                archive_strategy.get('trigger', ''),
                archive_strategy.get('criteria', ''),
                archive_strategy.get('retention', ''),
                archive_strategy.get('storage', ''),
                archive_strategy.get('access_pattern', ''),
                archive_strategy.get('restoration', '')
            )
            
            success, query_messages, _ = execute_query(query, params, schema_name)
            
            if not success:
                messages.extend(query_messages)
                return False, messages
            
            messages.append(f"Inserted archive strategy for entity '{entity_id}' into {schema_name}.entity_lifecycle_management")
            logger.info(f"Inserted archive strategy for entity '{entity_id}' into {schema_name}.entity_lifecycle_management")
        
        # Deploy history tracking
        if 'history_tracking' in entity_def:
            history_tracking = entity_def['history_tracking']
            
            query = f"""
                INSERT INTO {schema_name}.entity_lifecycle_management (
                    entity_id, management_type, tracked_attributes, tracking_method, granularity, retention, access_control
                ) VALUES (%s, %s, %s, %s, %s, %s, %s)
            """
            params = (
                entity_id,
                'history',
                json.dumps(history_tracking.get('tracked_attributes', [])),
                history_tracking.get('tracking_method', ''),
                history_tracking.get('granularity', ''),
                history_tracking.get('retention', ''),
                history_tracking.get('access_control', '')
            )
            
            success, query_messages, _ = execute_query(query, params, schema_name)
            
            if not success:
                messages.extend(query_messages)
                return False, messages
            
            messages.append(f"Inserted history tracking for entity '{entity_id}' into {schema_name}.entity_lifecycle_management")
            logger.info(f"Inserted history tracking for entity '{entity_id}' into {schema_name}.entity_lifecycle_management")
        
        return True, messages
    except Exception as e:
        error_msg = f"Error deploying lifecycle management for entity '{entity_id}': {str(e)}"
        logger.error(error_msg, exc_info=True)
        messages.append(error_msg)
        return False, messages

def get_sql_type(attr_type: str) -> str:
    """
    Convert attribute type to SQL type.
    
    Args:
        attr_type: Attribute type
        
    Returns:
        SQL type
    """
    type_map = {
        'string': 'VARCHAR(255)',
        'text': 'TEXT',
        'integer': 'INTEGER',
        'float': 'FLOAT',
        'boolean': 'BOOLEAN',
        'date': 'DATE',
        'datetime': 'TIMESTAMP',
        'time': 'TIME',
        'json': 'JSONB',
        'enum': 'VARCHAR(50)'
    }
    
    return type_map.get(attr_type.lower(), 'VARCHAR(255)')
