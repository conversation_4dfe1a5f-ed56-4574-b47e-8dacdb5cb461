#!/usr/bin/env python3
"""
Test script for parsing and deploying sample prescriptive files.

This script tests the parsing and deployment of sample prescriptive files
that would be the expected output from ChatGPT.
"""

import os
import sys
import logging
import argparse
from typing import Dict, List, Tuple, Any, Optional

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('test_sample_files')

# Import required modules
from prescriptive_parser import PrescriptiveParser
from component_deployer import ComponentDeployer

def read_sample_file(file_path: str) -> str:
    """
    Read a sample file.
    
    Args:
        file_path: Path to the sample file
        
    Returns:
        The contents of the file as a string
    """
    try:
        with open(file_path, 'r') as f:
            return f.read()
    except Exception as e:
        logger.error(f"Error reading file {file_path}: {str(e)}")
        return ""

def test_entity_parser(parser: PrescriptiveParser) -> <PERSON><PERSON>[bool, Dict, List[str]]:
    """
    Test the entity parser with a sample file.
    
    Args:
        parser: The prescriptive parser
        
    Returns:
        Tuple containing:
            - <PERSON><PERSON>an indicating if parsing was successful
            - Parsed data
            - List of warning messages
    """
    logger.info("Testing entity parser")
    
    # Read the sample file
    sample_path = os.path.join('samples', 'sample_entity_output.txt')
    sample_text = read_sample_file(sample_path)
    
    if not sample_text:
        return False, {}, ["Failed to read sample entity file"]
    
    # Parse the sample text
    entity_data, warnings = parser.parse('entities', sample_text)
    
    if warnings:
        logger.warning(f"Entity parser warnings: {warnings}")
    
    if not entity_data or 'entities' not in entity_data:
        logger.error("Entity parser failed to parse sample text")
        return False, {}, warnings + ["Failed to parse sample text"]
    
    logger.info(f"Successfully parsed {len(entity_data.get('entities', {}))} entities")
    return True, entity_data, warnings

def test_go_parser(parser: PrescriptiveParser) -> Tuple[bool, Dict, List[str]]:
    """
    Test the GO parser with a sample file.
    
    Args:
        parser: The prescriptive parser
        
    Returns:
        Tuple containing:
            - Boolean indicating if parsing was successful
            - Parsed data
            - List of warning messages
    """
    logger.info("Testing GO parser")
    
    # Read the sample file
    sample_path = os.path.join('samples', 'sample_go_output.txt')
    sample_text = read_sample_file(sample_path)
    
    if not sample_text:
        return False, {}, ["Failed to read sample GO file"]
    
    # Parse the sample text
    go_data, warnings = parser.parse('go_definitions', sample_text)
    
    if warnings:
        logger.warning(f"GO parser warnings: {warnings}")
    
    if not go_data or 'global_objectives' not in go_data:
        logger.error("GO parser failed to parse sample text")
        return False, {}, warnings + ["Failed to parse sample text"]
    
    logger.info(f"Successfully parsed {len(go_data.get('global_objectives', {}))} global objectives")
    return True, go_data, warnings

def test_lo_parser(parser: PrescriptiveParser) -> Tuple[bool, Dict, List[str]]:
    """
    Test the LO parser with a sample file.
    
    Args:
        parser: The prescriptive parser
        
    Returns:
        Tuple containing:
            - Boolean indicating if parsing was successful
            - Parsed data
            - List of warning messages
    """
    logger.info("Testing LO parser")
    
    # Read the sample file
    sample_path = os.path.join('samples', 'sample_lo_output.txt')
    sample_text = read_sample_file(sample_path)
    
    if not sample_text:
        return False, {}, ["Failed to read sample LO file"]
    
    # Parse the sample text
    lo_data, warnings = parser.parse('lo_definitions', sample_text)
    
    if warnings:
        logger.warning(f"LO parser warnings: {warnings}")
    
    if not lo_data or 'local_objectives' not in lo_data:
        logger.error("LO parser failed to parse sample text")
        return False, {}, warnings + ["Failed to parse sample text"]
    
    logger.info(f"Successfully parsed {len(lo_data.get('local_objectives', {}))} local objectives")
    return True, lo_data, warnings

def test_role_parser(parser: PrescriptiveParser) -> Tuple[bool, Dict, List[str]]:
    """
    Test the role parser with a sample file.
    
    Args:
        parser: The prescriptive parser
        
    Returns:
        Tuple containing:
            - Boolean indicating if parsing was successful
            - Parsed data
            - List of warning messages
    """
    logger.info("Testing role parser")
    
    # Read the sample file
    sample_path = os.path.join('samples', 'sample_role_output.txt')
    sample_text = read_sample_file(sample_path)
    
    if not sample_text:
        return False, {}, ["Failed to read sample role file"]
    
    # Parse the sample text
    role_data, warnings = parser.parse('roles', sample_text)
    
    if warnings:
        logger.warning(f"Role parser warnings: {warnings}")
    
    if not role_data or 'roles' not in role_data:
        logger.error("Role parser failed to parse sample text")
        return False, {}, warnings + ["Failed to parse sample text"]
    
    logger.info(f"Successfully parsed {len(role_data.get('roles', {}))} roles")
    return True, role_data, warnings

def test_deployment(deployer: ComponentDeployer, component_type: str, component_data: Dict) -> Tuple[bool, List[str]]:
    """
    Test the deployment of a component.
    
    Args:
        deployer: The component deployer
        component_type: The type of component to deploy
        component_data: The component data to deploy
        
    Returns:
        Tuple containing:
            - Boolean indicating if deployment was successful
            - List of messages
    """
    logger.info(f"Testing deployment of {component_type}")
    
    # Convert component data to YAML
    import yaml
    component_yaml = yaml.dump(component_data)
    
    # Deploy the component
    success, messages = deployer.deploy_component(component_type, component_yaml)
    
    if not success:
        logger.error(f"Failed to deploy {component_type}: {messages}")
    else:
        logger.info(f"Successfully deployed {component_type}")
    
    return success, messages

def main():
    """Main function."""
    parser = argparse.ArgumentParser(description='Test sample prescriptive files')
    parser.add_argument('--parse-only', action='store_true', help='Only test parsing, not deployment')
    parser.add_argument('--deploy-only', action='store_true', help='Only test deployment, not parsing')
    parser.add_argument('--component', choices=['entities', 'go_definitions', 'lo_definitions', 'roles', 'all'], default='all', help='Component to test')
    parser.add_argument('--use-temp-schema', action='store_true', help='Use temporary schema for deployment')
    args = parser.parse_args()
    
    # Initialize parser and deployer
    prescriptive_parser = PrescriptiveParser()
    component_deployer = ComponentDeployer(use_temp_schema=args.use_temp_schema)
    
    # Test parsing
    if not args.deploy_only:
        logger.info("Testing parsing of sample files")
        
        if args.component in ['entities', 'all']:
            entity_success, entity_data, entity_warnings = test_entity_parser(prescriptive_parser)
            if entity_success and args.component == 'entities':
                print("Entity parser test successful")
                print(f"Parsed {len(entity_data.get('entities', {}))} entities")
                if entity_warnings:
                    print(f"Warnings: {entity_warnings}")
        
        if args.component in ['go_definitions', 'all']:
            go_success, go_data, go_warnings = test_go_parser(prescriptive_parser)
            if go_success and args.component == 'go_definitions':
                print("GO parser test successful")
                print(f"Parsed {len(go_data.get('global_objectives', {}))} global objectives")
                if go_warnings:
                    print(f"Warnings: {go_warnings}")
        
        if args.component in ['lo_definitions', 'all']:
            lo_success, lo_data, lo_warnings = test_lo_parser(prescriptive_parser)
            if lo_success and args.component == 'lo_definitions':
                print("LO parser test successful")
                print(f"Parsed {len(lo_data.get('local_objectives', {}))} local objectives")
                if lo_warnings:
                    print(f"Warnings: {lo_warnings}")
        
        if args.component in ['roles', 'all']:
            role_success, role_data, role_warnings = test_role_parser(prescriptive_parser)
            if role_success and args.component == 'roles':
                print("Role parser test successful")
                print(f"Parsed {len(role_data.get('roles', {}))} roles")
                if role_warnings:
                    print(f"Warnings: {role_warnings}")
        
        if args.component == 'all':
            if entity_success and go_success and lo_success and role_success:
                print("All parser tests successful")
            else:
                print("Some parser tests failed")
    
    # Test deployment
    if not args.parse_only:
        logger.info("Testing deployment of sample files")
        
        # We need to deploy components in the correct order due to dependencies
        if args.component in ['entities', 'all']:
            entity_success, entity_data, entity_warnings = test_entity_parser(prescriptive_parser)
            if entity_success:
                deploy_success, deploy_messages = test_deployment(component_deployer, 'entities', entity_data)
                if deploy_success and args.component == 'entities':
                    print("Entity deployment test successful")
                    print(f"Deployed {len(entity_data.get('entities', {}))} entities")
                    print(f"Messages: {deploy_messages}")
        
        if args.component in ['go_definitions', 'all']:
            go_success, go_data, go_warnings = test_go_parser(prescriptive_parser)
            if go_success:
                deploy_success, deploy_messages = test_deployment(component_deployer, 'go_definitions', go_data)
                if deploy_success and args.component == 'go_definitions':
                    print("GO deployment test successful")
                    print(f"Deployed {len(go_data.get('global_objectives', {}))} global objectives")
                    print(f"Messages: {deploy_messages}")
        
        if args.component in ['lo_definitions', 'all']:
            lo_success, lo_data, lo_warnings = test_lo_parser(prescriptive_parser)
            if lo_success:
                deploy_success, deploy_messages = test_deployment(component_deployer, 'lo_definitions', lo_data)
                if deploy_success and args.component == 'lo_definitions':
                    print("LO deployment test successful")
                    print(f"Deployed {len(lo_data.get('local_objectives', {}))} local objectives")
                    print(f"Messages: {deploy_messages}")
        
        if args.component in ['roles', 'all']:
            role_success, role_data, role_warnings = test_role_parser(prescriptive_parser)
            if role_success:
                deploy_success, deploy_messages = test_deployment(component_deployer, 'roles', role_data)
                if deploy_success and args.component == 'roles':
                    print("Role deployment test successful")
                    print(f"Deployed {len(role_data.get('roles', {}))} roles")
                    print(f"Messages: {deploy_messages}")
        
        if args.component == 'all':
            print("Deployment tests completed")
    
    # Rollback temporary schema if used
    if args.use_temp_schema and not args.parse_only:
        logger.info("Rolling back temporary schema")
        success, messages = component_deployer.rollback_temp_schema()
        if success:
            logger.info("Successfully rolled back temporary schema")
        else:
            logger.error(f"Failed to roll back temporary schema: {messages}")

if __name__ == '__main__':
    main()
