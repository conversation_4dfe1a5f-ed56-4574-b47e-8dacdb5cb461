{"timestamp": "2025-06-24T04:45:03.162218", "operation": "deploy_single_security_property_to_workflow_temp", "input_data": {"security_property_id": "SEC001"}, "result": {"success": false, "error": "Security property SEC001 not found with status draft", "security_property_id": "SEC001", "required_status": "draft"}, "status": "error"}
{"timestamp": "2025-06-24T04:45:03.255600", "operation": "process_mongo_security_properties_to_workflow_temp", "input_data": {"schema": "workflow_temp"}, "result": {"total_processed": 0, "successful_inserts": 0, "failed_inserts": 0, "details": []}, "status": "success"}
{"timestamp": "2025-06-24T11:44:47.760175", "operation": "insert_security_property_to_workflow_runtime", "input_data": {"_id": "6855330bcc6fc2d42ba9e4a2", "security_property_id": "SEC5", "entity_id": "E15", "attribute_id": "E15.At1", "classification": "confidential", "pii_type": "name", "encryption_required": false, "encryption_type": "", "masking_required": true, "masking_pattern": "EMP***", "access_level": "read_restricted", "audit_trail": true, "data_residency": "global", "retention_override": "", "anonymization_required": true, "anonymization_method": "tokenize", "compliance_frameworks": "gdpr, hipaa", "data_purpose": "", "consent_required": false, "cross_border_transfer": false, "backup_encryption": false, "secure_deletion": false, "natural_language": "Entity: Employee\nAttribute: employeeId\nClassification: confidential\nPII Type: name\nMasking Required: true\nMasking Pattern: EMP***\nAccess Level: read_restricted\nAudit Trail: true\nData Residency: global\nAnonymization Required: true\nAnonymization Method: tokenize\nCompliance Frameworks: gdpr, hipaa", "version": 2, "status": "deployed_to_temp", "created_at": "2025-06-20T10:08:11.956898", "updated_at": "2025-06-20T12:23:39.064941", "created_by": "Tarun", "updated_by": "Tarun", "security_property_status": "new", "changes_detected": []}, "result": {"success": true, "inserted_id": "SEC5", "schema": "workflow_runtime", "security_property_id": "SEC5", "entity_id": "E15", "attribute_id": "E15.At1", "classification": "confidential", "original_security_property_id": "SEC3"}, "status": "success"}
{"timestamp": "2025-06-24T11:44:47.763142", "operation": "process_mongo_security_properties_to_workflow_runtime", "input_data": {"schema": "workflow_runtime"}, "result": {"total_processed": 1, "successful_inserts": 1, "failed_inserts": 0, "details": [{"security_property_id": "SEC5", "entity_id": "E15", "attribute_id": "E15.At1", "classification": "confidential", "status": "success", "details": {"success": true, "inserted_id": "SEC5", "schema": "workflow_runtime", "security_property_id": "SEC5", "entity_id": "E15", "attribute_id": "E15.At1", "classification": "confidential", "original_security_property_id": "SEC3"}}]}, "status": "success"}
