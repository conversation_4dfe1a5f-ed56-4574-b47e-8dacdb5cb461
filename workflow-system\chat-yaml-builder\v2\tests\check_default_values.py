#!/usr/bin/env python3
"""
Script to check if default values and property names are being correctly parsed.
"""

import sys
import os
import json
import importlib.util

# Add parent directory to path to import modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Import entity_parser module
def import_module_from_file(module_name, file_path):
    spec = importlib.util.spec_from_file_location(module_name, file_path)
    module = importlib.util.module_from_spec(spec)
    spec.loader.exec_module(module)
    return module

# Find entity_parser.py
parser_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'parsers', 'entity_parser.py')
entity_parser = import_module_from_file('entity_parser', parser_path)

# Find sample file
sample_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'samples', 'sample_entity_output2.txt')
with open(sample_path, 'r') as f:
    sample_text = f.read()

# Parse entities
entities_data, warnings = entity_parser.parse_entities(sample_text)

# Check if Employee entity has the expected default values and property names
employee_entity = entities_data['entities'].get('Employee', {})
employee_attrs = employee_entity.get('attributes', {})

print("=== Employee Entity Attributes ===")
for attr_name, attr_data in employee_attrs.items():
    print(f"Attribute: {attr_name}")
    if 'default' in attr_data:
        print(f"  Default Value: {attr_data['default']}")
    if 'property_value' in attr_data:
        print(f"  Property Value: {attr_data['property_value']}")

# Check specific attributes mentioned in the requirements
print("\n=== Specific Attributes ===")
if 'probationDays' in employee_attrs and 'property_value' in employee_attrs['probationDays']:
    print(f"Employee.probationDays PROPERTY_NAME = {employee_attrs['probationDays']['property_value']}")
else:
    print("Employee.probationDays PROPERTY_NAME not found")

if 'minSalary' in employee_attrs and 'property_value' in employee_attrs['minSalary']:
    print(f"Employee.minSalary PROPERTY_NAME = {employee_attrs['minSalary']['property_value']}")
else:
    print("Employee.minSalary PROPERTY_NAME not found")

if 'status' in employee_attrs and 'default' in employee_attrs['status']:
    print(f"Employee.status DEFAULT_VALUE = {employee_attrs['status']['default']}")
else:
    print("Employee.status DEFAULT_VALUE not found")

if 'hireDate' in employee_attrs and 'default' in employee_attrs['hireDate']:
    print(f"Employee.hireDate DEFAULT_VALUE = {employee_attrs['hireDate']['default']}")
else:
    print("Employee.hireDate DEFAULT_VALUE not found")

# Write output to a file
with open('default_values_output.txt', 'w') as f:
    f.write("=== Employee Entity Attributes ===\n")
    for attr_name, attr_data in employee_attrs.items():
        f.write(f"Attribute: {attr_name}\n")
        if 'default' in attr_data:
            f.write(f"  Default Value: {attr_data['default']}\n")
        if 'property_value' in attr_data:
            f.write(f"  Property Value: {attr_data['property_value']}\n")

    f.write("\n=== Specific Attributes ===\n")
    if 'probationDays' in employee_attrs and 'property_value' in employee_attrs['probationDays']:
        f.write(f"Employee.probationDays PROPERTY_NAME = {employee_attrs['probationDays']['property_value']}\n")
    else:
        f.write("Employee.probationDays PROPERTY_NAME not found\n")

    if 'minSalary' in employee_attrs and 'property_value' in employee_attrs['minSalary']:
        f.write(f"Employee.minSalary PROPERTY_NAME = {employee_attrs['minSalary']['property_value']}\n")
    else:
        f.write("Employee.minSalary PROPERTY_NAME not found\n")

    if 'status' in employee_attrs and 'default' in employee_attrs['status']:
        f.write(f"Employee.status DEFAULT_VALUE = {employee_attrs['status']['default']}\n")
    else:
        f.write("Employee.status DEFAULT_VALUE not found\n")

    if 'hireDate' in employee_attrs and 'default' in employee_attrs['hireDate']:
        f.write(f"Employee.hireDate DEFAULT_VALUE = {employee_attrs['hireDate']['default']}\n")
    else:
        f.write("Employee.hireDate DEFAULT_VALUE not found\n")

print("Output written to default_values_output.txt")
