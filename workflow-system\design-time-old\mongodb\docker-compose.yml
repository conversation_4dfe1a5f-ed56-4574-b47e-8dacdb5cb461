version: '3.8'

services:
  mongodb:
    image: mongo:6.0
    container_name: workflow_mongodb
    restart: always
    ports:
      - "27018:27017"
    environment:
      MONGO_INITDB_ROOT_USERNAME: admin
      MONGO_INITDB_ROOT_PASSWORD: ${MONGO_PASSWORD}
    volumes:
      - mongodb_data:/data/db
      - mongodb_config:/data/configdb
      - ./init-mongo.js:/docker-entrypoint-initdb.d/init-mongo.js:ro
    networks:
      - workflow_network

  mongo-express:
    image: mongo-express
    container_name: workflow_mongo_express
    restart: always
    ports:
      - "8081:8081"
    environment:
      ME_CONFIG_MONGODB_ADMINUSERNAME: admin
      ME_CONFIG_MONGODB_ADMINPASSWORD: ${MONGO_PASSWORD}
      ME_CONFIG_MONGODB_URL: mongodb://admin:${MONGO_PASSWORD}@mongodb:27017/
    networks:
      - workflow_network
    depends_on:
      - mongodb

volumes:
  mongodb_data:
  mongodb_config:

networks:
  workflow_network:
    driver: bridge
