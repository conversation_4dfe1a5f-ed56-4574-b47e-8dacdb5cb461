{"timestamp": "2025-06-24T12:40:14.351304", "endpoint": "parse-validate-mongosave/ui-properties", "input": {"natural_language": "UI Property: TestEntity2 Form Layout\nEntity: TestEntity2\nProperty Type: form_layout\nProperty Name: default_form\nProperty Value: {\"layout\": \"vertical\", \"columns\": 2}\nDescription: Default form layout for TestEntity2\nDisplay Order: 1", "tenant_id": "t999", "edit_draft": true, "edit_temp": false, "edit_production": false}, "output": {"success": true, "ui_property_results": [], "operation": "parse_validate_mongosave", "total_ui_properties": 0}, "status": "success"}
{"timestamp": "2025-06-24T12:52:48.952671", "endpoint": "parse-validate-mongosave/ui-properties", "input": {"natural_language": "Entity.Attribute | Control Type | Display Format | Input Mask | Placeholder Text | Auto Complete | Read Only | Validation Display | Help Text Position | Label | Required Indicator\nTestEntity2.testId | text_input | uppercase | TEST-#### | Enter test ID | false | false | inline | below | Test ID | true\nTenant: t999", "tenant_id": "t999", "edit_draft": true, "edit_temp": false, "edit_production": false}, "output": {"success": true, "ui_property_results": [{"success": false, "errors": ["Entity with entity_id E48 does not exist", "Attribute with attribute_id A_E48_testId_1750769568 does not exist"], "parsed_data": {"ui_property_id": "UI10", "entity_id": "E48", "attribute_id": "A_E48_testId_1750769568", "control_type": "text_input", "display_format": "uppercase", "input_mask": "TEST-####", "placeholder_text": "Enter test ID", "auto_complete": false, "read_only": false, "validation_display": "inline", "help_text_position": "below", "label": "Test ID", "required_indicator": true, "natural_language": "Entity: TestEntity2\nAttribute: testId\nControl Type: text_input\nLabel: Test ID\nDisplay Format: uppercase\nInput Mask: TEST-####\nPlaceholder Text: Enter test ID\nRequired Indicator: true\nValidation Display: inline\nHelp Text Position: below", "version": 1, "status": "draft", "created_at": "2025-06-24T12:52:48.922865", "updated_at": "2025-06-24T12:52:48.922865", "created_by": "Tarun", "updated_by": "Tarun", "ui_property_status": "new", "changes_detected": []}}], "operation": "parse_validate_mongosave", "total_ui_properties": 1}, "status": "success"}
