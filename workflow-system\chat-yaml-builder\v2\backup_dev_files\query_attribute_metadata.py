"""
Query entity attribute metadata from the database.
"""

from db_utils import execute_query

def main():
    # Query entity_attribute_metadata table
    print("Querying workflow_temp.entity_attribute_metadata table")
    success, messages, metadata = execute_query(
        "SELECT entity_id, attribute_id, attribute_name, required FROM workflow_temp.entity_attribute_metadata",
        schema_name="workflow_temp"
    )
    
    if success and metadata:
        print("Entity Attribute Metadata:")
        for item in metadata:
            print(f"  {item}")
    else:
        print("No entity attribute metadata found or query failed")
        if messages:
            print("Messages:")
            for message in messages:
                print(f"  {message}")

if __name__ == "__main__":
    main()
