{"timestamp": "2025-06-23T11:24:57.692344", "operation": "deploy_single_security_property_to_workflow_temp", "input_data": {"security_property_id": "SEC3"}, "result": {"success": false, "error": "Security property SEC3 not found with status draft", "security_property_id": "SEC3", "required_status": "draft"}, "status": "error"}
{"timestamp": "2025-06-23T14:03:50.681334", "operation": "deploy_single_security_property_to_workflow_temp", "input_data": {"security_property_id": "SEC001"}, "result": {"success": false, "error": "Security property SEC001 not found with status draft", "security_property_id": "SEC001", "required_status": "draft"}, "status": "error"}
{"timestamp": "2025-06-23T14:03:50.821656", "operation": "process_mongo_security_properties_to_workflow_temp", "input_data": {"schema": "workflow_temp"}, "result": {"total_processed": 0, "successful_inserts": 0, "failed_inserts": 0, "details": []}, "status": "success"}
{"timestamp": "2025-06-23T14:09:01.692429", "operation": "deploy_single_security_property_to_workflow_temp", "input_data": {"security_property_id": "SEC001"}, "result": {"success": false, "error": "Security property SEC001 not found with status draft", "security_property_id": "SEC001", "required_status": "draft"}, "status": "error"}
{"timestamp": "2025-06-23T14:09:01.832566", "operation": "process_mongo_security_properties_to_workflow_temp", "input_data": {"schema": "workflow_temp"}, "result": {"total_processed": 0, "successful_inserts": 0, "failed_inserts": 0, "details": []}, "status": "success"}
{"timestamp": "2025-06-23T14:32:34.054740", "operation": "deploy_single_security_property_to_workflow_temp", "input_data": {"security_property_id": "SEC001"}, "result": {"success": false, "error": "Security property SEC001 not found with status draft", "security_property_id": "SEC001", "required_status": "draft"}, "status": "error"}
{"timestamp": "2025-06-23T14:32:34.166024", "operation": "process_mongo_security_properties_to_workflow_temp", "input_data": {"schema": "workflow_temp"}, "result": {"total_processed": 0, "successful_inserts": 0, "failed_inserts": 0, "details": []}, "status": "success"}
{"timestamp": "2025-06-23T14:38:53.520608", "operation": "deploy_single_security_property_to_workflow_temp", "input_data": {"security_property_id": "SEC001"}, "result": {"success": false, "error": "Security property SEC001 not found with status draft", "security_property_id": "SEC001", "required_status": "draft"}, "status": "error"}
{"timestamp": "2025-06-23T14:38:53.644265", "operation": "process_mongo_security_properties_to_workflow_temp", "input_data": {"schema": "workflow_temp"}, "result": {"total_processed": 0, "successful_inserts": 0, "failed_inserts": 0, "details": []}, "status": "success"}
