#!/bin/bash
# Script to run the RBAC tests

# Set up environment
echo "Setting up environment..."
export PYTHONPATH=$PYTHONPATH:$(pwd)/../../..

# Check if Python 3 is installed
if ! command -v python3 &> /dev/null; then
    echo "Error: Python 3 is not installed"
    exit 1
fi

# Check if pip3 is installed
if ! command -v pip3 &> /dev/null; then
    echo "Error: pip3 is not installed"
    exit 1
fi

# Install required packages
echo "Installing required packages..."
pip3 install -r requirements.txt

# Set up test database
echo "Setting up test database..."
python3 setup_test_db.py

# Run the tests
echo "Running tests..."
python3 -m unittest test_auth.py

# Check if the tests ran successfully
if [ $? -eq 0 ]; then
    echo "Tests ran successfully"
else
    echo "Error: Tests failed"
    exit 1
fi

echo "Done"
