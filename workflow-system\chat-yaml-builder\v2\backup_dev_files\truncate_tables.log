2025-05-12 15:26:00,225 - truncate_tables - INFO - Truncating all tables in schema workflow_temp
2025-05-12 15:26:00,231 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:26:00,233 - truncate_tables - INFO - Truncating table workflow_temp.lo_input_stack
2025-05-12 15:26:00,238 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:26:00,245 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:26:00,255 - truncate_tables - INFO - Successfully truncated table workflow_temp.lo_input_stack
2025-05-12 15:26:00,255 - truncate_tables - INFO - Truncating table workflow_temp.alembic_version
2025-05-12 15:26:00,260 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:26:00,266 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:26:00,267 - truncate_tables - INFO - Successfully truncated table workflow_temp.alembic_version
2025-05-12 15:26:00,267 - truncate_tables - INFO - Truncating table workflow_temp.entity_permissions
2025-05-12 15:26:00,272 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:26:00,279 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:26:00,280 - truncate_tables - INFO - Successfully truncated table workflow_temp.entity_permissions
2025-05-12 15:26:00,281 - truncate_tables - INFO - Truncating table workflow_temp.lo_input_items
2025-05-12 15:26:00,284 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:26:00,290 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:26:00,296 - truncate_tables - INFO - Successfully truncated table workflow_temp.lo_input_items
2025-05-12 15:26:00,296 - truncate_tables - INFO - Truncating table workflow_temp.input_stack
2025-05-12 15:26:00,300 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:26:00,306 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:26:00,309 - truncate_tables - INFO - Successfully truncated table workflow_temp.input_stack
2025-05-12 15:26:00,309 - truncate_tables - INFO - Truncating table workflow_temp.objective_permissions
2025-05-12 15:26:00,312 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:26:00,318 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:26:00,320 - truncate_tables - INFO - Successfully truncated table workflow_temp.objective_permissions
2025-05-12 15:26:00,320 - truncate_tables - INFO - Truncating table workflow_temp.lo_output_stack
2025-05-12 15:26:00,324 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:26:00,329 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:26:00,333 - truncate_tables - INFO - Successfully truncated table workflow_temp.lo_output_stack
2025-05-12 15:26:00,333 - truncate_tables - INFO - Truncating table workflow_temp.lab_report
2025-05-12 15:26:00,337 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:26:00,342 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:26:00,344 - truncate_tables - INFO - Successfully truncated table workflow_temp.lab_report
2025-05-12 15:26:00,344 - truncate_tables - INFO - Truncating table workflow_temp.execution_path_tracking
2025-05-12 15:26:00,350 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:26:00,355 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:26:00,357 - truncate_tables - INFO - Successfully truncated table workflow_temp.execution_path_tracking
2025-05-12 15:26:00,357 - truncate_tables - INFO - Truncating table workflow_temp.permission_types
2025-05-12 15:26:00,360 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:26:00,368 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:26:00,373 - truncate_tables - INFO - Successfully truncated table workflow_temp.permission_types
2025-05-12 15:26:00,373 - truncate_tables - INFO - Truncating table workflow_temp.lo_output_items
2025-05-12 15:26:00,377 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:26:00,382 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:26:00,385 - truncate_tables - INFO - Successfully truncated table workflow_temp.lo_output_items
2025-05-12 15:26:00,385 - truncate_tables - INFO - Truncating table workflow_temp.attribute_ui_controls
2025-05-12 15:26:00,391 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:26:00,397 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:26:00,399 - truncate_tables - INFO - Successfully truncated table workflow_temp.attribute_ui_controls
2025-05-12 15:26:00,399 - truncate_tables - INFO - Truncating table workflow_temp.lo_input_execution
2025-05-12 15:26:00,403 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:26:00,410 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:26:00,413 - truncate_tables - INFO - Successfully truncated table workflow_temp.lo_input_execution
2025-05-12 15:26:00,413 - truncate_tables - INFO - Truncating table workflow_temp.organizational_units
2025-05-12 15:26:00,417 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:26:00,423 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:26:00,425 - truncate_tables - INFO - Successfully truncated table workflow_temp.organizational_units
2025-05-12 15:26:00,425 - truncate_tables - INFO - Truncating table workflow_temp.execution_pathway_conditions
2025-05-12 15:26:00,429 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:26:00,435 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:26:00,438 - truncate_tables - INFO - Successfully truncated table workflow_temp.execution_pathway_conditions
2025-05-12 15:26:00,438 - truncate_tables - INFO - Truncating table workflow_temp.lo_data_mapping_stack
2025-05-12 15:26:00,442 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:26:00,449 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:26:00,452 - truncate_tables - INFO - Successfully truncated table workflow_temp.lo_data_mapping_stack
2025-05-12 15:26:00,452 - truncate_tables - INFO - Truncating table workflow_temp.execution_pathways
2025-05-12 15:26:00,455 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:26:00,463 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:26:00,465 - truncate_tables - INFO - Successfully truncated table workflow_temp.execution_pathways
2025-05-12 15:26:00,466 - truncate_tables - INFO - Truncating table workflow_temp.prescription
2025-05-12 15:26:00,469 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:26:00,475 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:26:00,477 - truncate_tables - INFO - Successfully truncated table workflow_temp.prescription
2025-05-12 15:26:00,477 - truncate_tables - INFO - Truncating table workflow_temp.output_items
2025-05-12 15:26:00,482 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:26:00,488 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:26:00,490 - truncate_tables - INFO - Successfully truncated table workflow_temp.output_items
2025-05-12 15:26:00,490 - truncate_tables - INFO - Truncating table workflow_temp.entity_business_rules
2025-05-12 15:26:00,494 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:26:00,499 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:26:00,502 - truncate_tables - INFO - Successfully truncated table workflow_temp.entity_business_rules
2025-05-12 15:26:00,502 - truncate_tables - INFO - Truncating table workflow_temp.lo_data_mappings
2025-05-12 15:26:00,507 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:26:00,513 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:26:00,514 - truncate_tables - INFO - Successfully truncated table workflow_temp.lo_data_mappings
2025-05-12 15:26:00,514 - truncate_tables - INFO - Truncating table workflow_temp.lo_output_triggers
2025-05-12 15:26:00,518 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:26:00,524 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:26:00,526 - truncate_tables - INFO - Successfully truncated table workflow_temp.lo_output_triggers
2025-05-12 15:26:00,526 - truncate_tables - INFO - Truncating table workflow_temp.output_stack
2025-05-12 15:26:00,531 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:26:00,536 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:26:00,539 - truncate_tables - INFO - Successfully truncated table workflow_temp.output_stack
2025-05-12 15:26:00,539 - truncate_tables - INFO - Truncating table workflow_temp.conditional_success_messages
2025-05-12 15:26:00,543 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:26:00,551 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:26:00,554 - truncate_tables - INFO - Successfully truncated table workflow_temp.conditional_success_messages
2025-05-12 15:26:00,554 - truncate_tables - INFO - Truncating table workflow_temp.execution_rules
2025-05-12 15:26:00,560 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:26:00,566 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:26:00,568 - truncate_tables - INFO - Successfully truncated table workflow_temp.execution_rules
2025-05-12 15:26:00,568 - truncate_tables - INFO - Truncating table workflow_temp.lo_nested_functions
2025-05-12 15:26:00,573 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:26:00,581 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:26:00,583 - truncate_tables - INFO - Successfully truncated table workflow_temp.lo_nested_functions
2025-05-12 15:26:00,583 - truncate_tables - INFO - Truncating table workflow_temp.lo_system_functions
2025-05-12 15:26:00,587 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:26:00,594 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:26:00,596 - truncate_tables - INFO - Successfully truncated table workflow_temp.lo_system_functions
2025-05-12 15:26:00,596 - truncate_tables - INFO - Truncating table workflow_temp.roles
2025-05-12 15:26:00,601 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:26:00,609 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:26:00,614 - truncate_tables - INFO - Successfully truncated table workflow_temp.roles
2025-05-12 15:26:00,614 - truncate_tables - INFO - Truncating table workflow_temp.output_triggers
2025-05-12 15:26:00,620 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:26:00,626 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:26:00,629 - truncate_tables - INFO - Successfully truncated table workflow_temp.output_triggers
2025-05-12 15:26:00,629 - truncate_tables - INFO - Truncating table workflow_temp.go_lo_mapping
2025-05-12 15:26:00,634 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:26:00,640 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:26:00,641 - truncate_tables - INFO - Successfully truncated table workflow_temp.go_lo_mapping
2025-05-12 15:26:00,642 - truncate_tables - INFO - Truncating table workflow_temp.data_mapping_stack
2025-05-12 15:26:00,647 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:26:00,653 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:26:00,657 - truncate_tables - INFO - Successfully truncated table workflow_temp.data_mapping_stack
2025-05-12 15:26:00,657 - truncate_tables - INFO - Truncating table workflow_temp.lo_dependencies
2025-05-12 15:26:00,662 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:26:00,669 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:26:00,671 - truncate_tables - INFO - Successfully truncated table workflow_temp.lo_dependencies
2025-05-12 15:26:00,671 - truncate_tables - INFO - Truncating table workflow_temp.mapping_rules
2025-05-12 15:26:00,676 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:26:00,681 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:26:00,683 - truncate_tables - INFO - Successfully truncated table workflow_temp.mapping_rules
2025-05-12 15:26:00,683 - truncate_tables - INFO - Truncating table workflow_temp.data_mappings
2025-05-12 15:26:00,688 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:26:00,694 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:26:00,695 - truncate_tables - INFO - Successfully truncated table workflow_temp.data_mappings
2025-05-12 15:26:00,695 - truncate_tables - INFO - Truncating table workflow_temp.role
2025-05-12 15:26:00,701 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:26:00,706 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:26:00,708 - truncate_tables - INFO - Successfully truncated table workflow_temp.role
2025-05-12 15:26:00,708 - truncate_tables - INFO - Truncating table workflow_temp.go_performance_metrics
2025-05-12 15:26:00,713 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:26:00,721 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:26:00,723 - truncate_tables - INFO - Successfully truncated table workflow_temp.go_performance_metrics
2025-05-12 15:26:00,724 - truncate_tables - INFO - Truncating table workflow_temp.attribute_enum_values
2025-05-12 15:26:00,729 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:26:00,735 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:26:00,737 - truncate_tables - INFO - Successfully truncated table workflow_temp.attribute_enum_values
2025-05-12 15:26:00,737 - truncate_tables - INFO - Truncating table workflow_temp.attribute_validations
2025-05-12 15:26:00,742 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:26:00,748 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:26:00,751 - truncate_tables - INFO - Successfully truncated table workflow_temp.attribute_validations
2025-05-12 15:26:00,751 - truncate_tables - INFO - Truncating table workflow_temp.lo_input_validations
2025-05-12 15:26:00,755 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:26:00,762 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:26:00,765 - truncate_tables - INFO - Successfully truncated table workflow_temp.lo_input_validations
2025-05-12 15:26:00,765 - truncate_tables - INFO - Truncating table workflow_temp.dropdown_data_sources
2025-05-12 15:26:00,770 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:26:00,778 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:26:00,781 - truncate_tables - INFO - Successfully truncated table workflow_temp.dropdown_data_sources
2025-05-12 15:26:00,781 - truncate_tables - INFO - Truncating table workflow_temp.medical_record
2025-05-12 15:26:00,787 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:26:00,794 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:26:00,797 - truncate_tables - INFO - Successfully truncated table workflow_temp.medical_record
2025-05-12 15:26:00,797 - truncate_tables - INFO - Truncating table workflow_temp.patient
2025-05-12 15:26:00,802 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:26:00,810 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:26:00,812 - truncate_tables - INFO - Successfully truncated table workflow_temp.patient
2025-05-12 15:26:00,813 - truncate_tables - INFO - Truncating table workflow_temp.input_data_sources
2025-05-12 15:26:00,816 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:26:00,823 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:26:00,825 - truncate_tables - INFO - Successfully truncated table workflow_temp.input_data_sources
2025-05-12 15:26:00,825 - truncate_tables - INFO - Truncating table workflow_temp.tenants
2025-05-12 15:26:00,829 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:26:00,835 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:26:00,874 - truncate_tables - INFO - Successfully truncated table workflow_temp.tenants
2025-05-12 15:26:00,874 - truncate_tables - INFO - Truncating table workflow_temp.user_organizations
2025-05-12 15:26:00,880 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:26:00,887 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:26:00,905 - truncate_tables - INFO - Successfully truncated table workflow_temp.user_organizations
2025-05-12 15:26:00,906 - truncate_tables - INFO - Truncating table workflow_temp.ui_stack
2025-05-12 15:26:00,912 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:26:00,920 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:26:00,923 - truncate_tables - INFO - Successfully truncated table workflow_temp.ui_stack
2025-05-12 15:26:00,923 - truncate_tables - INFO - Truncating table workflow_temp.global_objectives
2025-05-12 15:26:00,927 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:26:00,935 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:26:00,987 - truncate_tables - INFO - Successfully truncated table workflow_temp.global_objectives
2025-05-12 15:26:00,987 - truncate_tables - INFO - Truncating table workflow_temp.permission_capabilities
2025-05-12 15:26:00,992 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:26:00,998 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:26:01,000 - truncate_tables - INFO - Successfully truncated table workflow_temp.permission_capabilities
2025-05-12 15:26:01,001 - truncate_tables - INFO - Truncating table workflow_temp.agent_rights
2025-05-12 15:26:01,004 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:26:01,012 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:26:01,015 - truncate_tables - INFO - Successfully truncated table workflow_temp.agent_rights
2025-05-12 15:26:01,015 - truncate_tables - INFO - Truncating table workflow_temp.user
2025-05-12 15:26:01,019 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:26:01,026 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:26:01,029 - truncate_tables - INFO - Successfully truncated table workflow_temp.user
2025-05-12 15:26:01,029 - truncate_tables - INFO - Truncating table workflow_temp.user_role
2025-05-12 15:26:01,035 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:26:01,043 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:26:01,046 - truncate_tables - INFO - Successfully truncated table workflow_temp.user_role
2025-05-12 15:26:01,046 - truncate_tables - INFO - Truncating table workflow_temp.input_dependencies
2025-05-12 15:26:01,051 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:26:01,059 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:26:01,062 - truncate_tables - INFO - Successfully truncated table workflow_temp.input_dependencies
2025-05-12 15:26:01,062 - truncate_tables - INFO - Truncating table workflow_temp.role_inheritance
2025-05-12 15:26:01,066 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:26:01,073 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:26:01,077 - truncate_tables - INFO - Successfully truncated table workflow_temp.role_inheritance
2025-05-12 15:26:01,077 - truncate_tables - INFO - Truncating table workflow_temp.workflow_instances
2025-05-12 15:26:01,083 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:26:01,089 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:26:01,093 - truncate_tables - INFO - Successfully truncated table workflow_temp.workflow_instances
2025-05-12 15:26:01,093 - truncate_tables - INFO - Truncating table workflow_temp.success_messages
2025-05-12 15:26:01,097 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:26:01,103 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:26:01,105 - truncate_tables - INFO - Successfully truncated table workflow_temp.success_messages
2025-05-12 15:26:01,105 - truncate_tables - INFO - Truncating table workflow_temp.metrics_aggregation
2025-05-12 15:26:01,109 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:26:01,116 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:26:01,118 - truncate_tables - INFO - Successfully truncated table workflow_temp.metrics_aggregation
2025-05-12 15:26:01,118 - truncate_tables - INFO - Truncating table workflow_temp.terminal_pathways
2025-05-12 15:26:01,122 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:26:01,129 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:26:01,131 - truncate_tables - INFO - Successfully truncated table workflow_temp.terminal_pathways
2025-05-12 15:26:01,131 - truncate_tables - INFO - Truncating table workflow_temp.calculated_fields
2025-05-12 15:26:01,135 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:26:01,142 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:26:01,145 - truncate_tables - INFO - Successfully truncated table workflow_temp.calculated_fields
2025-05-12 15:26:01,145 - truncate_tables - INFO - Truncating table workflow_temp.entities
2025-05-12 15:26:01,149 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:26:01,156 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:26:01,169 - truncate_tables - INFO - Successfully truncated table workflow_temp.entities
2025-05-12 15:26:01,169 - truncate_tables - INFO - Truncating table workflow_temp.entity_lifecycle_management
2025-05-12 15:26:01,174 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:26:01,181 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:26:01,184 - truncate_tables - INFO - Successfully truncated table workflow_temp.entity_lifecycle_management
2025-05-12 15:26:01,184 - truncate_tables - INFO - Truncating table workflow_temp.entity_attributes
2025-05-12 15:26:01,188 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:26:01,195 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:26:01,203 - truncate_tables - INFO - Successfully truncated table workflow_temp.entity_attributes
2025-05-12 15:26:01,203 - truncate_tables - INFO - Truncating table workflow_temp.e1.employee
2025-05-12 15:26:01,207 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:26:01,213 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:26:01,214 - db_utils - ERROR - Database error: cross-database references are not implemented: "workflow_temp.e1.employee"
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.FeatureNotSupported: cross-database references are not implemented: "workflow_temp.e1.employee"

2025-05-12 15:29:29,505 - truncate_tables - INFO - Truncating all tables in schema workflow_temp
2025-05-12 15:29:29,511 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:29:29,514 - truncate_tables - INFO - Truncating table workflow_temp.alembic_version
2025-05-12 15:29:29,518 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:29:29,524 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:29:29,526 - truncate_tables - INFO - Successfully truncated table workflow_temp.alembic_version
2025-05-12 15:29:29,526 - truncate_tables - INFO - Truncating table workflow_temp.entity_permissions
2025-05-12 15:29:29,530 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:29:29,536 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:29:29,537 - truncate_tables - INFO - Successfully truncated table workflow_temp.entity_permissions
2025-05-12 15:29:29,537 - truncate_tables - INFO - Truncating table workflow_temp.lo_input_stack
2025-05-12 15:29:29,542 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:29:29,548 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:29:29,555 - truncate_tables - INFO - Successfully truncated table workflow_temp.lo_input_stack
2025-05-12 15:29:29,555 - truncate_tables - INFO - Truncating table workflow_temp.objective_permissions
2025-05-12 15:29:29,560 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:29:29,566 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:29:29,568 - truncate_tables - INFO - Successfully truncated table workflow_temp.objective_permissions
2025-05-12 15:29:29,568 - truncate_tables - INFO - Truncating table workflow_temp.input_stack
2025-05-12 15:29:29,572 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:29:29,577 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:29:29,581 - truncate_tables - INFO - Successfully truncated table workflow_temp.input_stack
2025-05-12 15:29:29,581 - truncate_tables - INFO - Truncating table workflow_temp.lo_input_items
2025-05-12 15:29:29,585 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:29:29,590 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:29:29,596 - truncate_tables - INFO - Successfully truncated table workflow_temp.lo_input_items
2025-05-12 15:29:29,596 - truncate_tables - INFO - Truncating table workflow_temp.lab_report
2025-05-12 15:29:29,600 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:29:29,607 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:29:29,610 - truncate_tables - INFO - Successfully truncated table workflow_temp.lab_report
2025-05-12 15:29:29,610 - truncate_tables - INFO - Truncating table workflow_temp.permission_types
2025-05-12 15:29:29,614 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:29:29,619 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:29:29,623 - truncate_tables - INFO - Successfully truncated table workflow_temp.permission_types
2025-05-12 15:29:29,623 - truncate_tables - INFO - Truncating table workflow_temp.lo_output_stack
2025-05-12 15:29:29,629 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:29:29,634 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:29:29,638 - truncate_tables - INFO - Successfully truncated table workflow_temp.lo_output_stack
2025-05-12 15:29:29,638 - truncate_tables - INFO - Truncating table workflow_temp.execution_path_tracking
2025-05-12 15:29:29,641 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:29:29,649 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:29:29,651 - truncate_tables - INFO - Successfully truncated table workflow_temp.execution_path_tracking
2025-05-12 15:29:29,651 - truncate_tables - INFO - Truncating table workflow_temp.attribute_ui_controls
2025-05-12 15:29:29,655 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:29:29,660 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:29:29,663 - truncate_tables - INFO - Successfully truncated table workflow_temp.attribute_ui_controls
2025-05-12 15:29:29,663 - truncate_tables - INFO - Truncating table workflow_temp.organizational_units
2025-05-12 15:29:29,668 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:29:29,674 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:29:29,675 - truncate_tables - INFO - Successfully truncated table workflow_temp.organizational_units
2025-05-12 15:29:29,676 - truncate_tables - INFO - Truncating table workflow_temp.lo_output_items
2025-05-12 15:29:29,680 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:29:29,686 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:29:29,690 - truncate_tables - INFO - Successfully truncated table workflow_temp.lo_output_items
2025-05-12 15:29:29,690 - truncate_tables - INFO - Truncating table workflow_temp.execution_pathway_conditions
2025-05-12 15:29:29,693 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:29:29,700 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:29:29,703 - truncate_tables - INFO - Successfully truncated table workflow_temp.execution_pathway_conditions
2025-05-12 15:29:29,703 - truncate_tables - INFO - Truncating table workflow_temp.prescription
2025-05-12 15:29:29,709 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:29:29,714 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:29:29,718 - truncate_tables - INFO - Successfully truncated table workflow_temp.prescription
2025-05-12 15:29:29,718 - truncate_tables - INFO - Truncating table workflow_temp.lo_data_mapping_stack
2025-05-12 15:29:29,721 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:29:29,729 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:29:29,732 - truncate_tables - INFO - Successfully truncated table workflow_temp.lo_data_mapping_stack
2025-05-12 15:29:29,732 - truncate_tables - INFO - Truncating table workflow_temp.execution_pathways
2025-05-12 15:29:29,736 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:29:29,742 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:29:29,744 - truncate_tables - INFO - Successfully truncated table workflow_temp.execution_pathways
2025-05-12 15:29:29,744 - truncate_tables - INFO - Truncating table workflow_temp.metrics_reporting
2025-05-12 15:29:29,749 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:29:29,756 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:29:29,758 - truncate_tables - INFO - Successfully truncated table workflow_temp.metrics_reporting
2025-05-12 15:29:29,758 - truncate_tables - INFO - Truncating table workflow_temp.output_items
2025-05-12 15:29:29,763 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:29:29,770 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:29:29,772 - truncate_tables - INFO - Successfully truncated table workflow_temp.output_items
2025-05-12 15:29:29,772 - truncate_tables - INFO - Truncating table workflow_temp.success_messages
2025-05-12 15:29:29,778 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:29:29,785 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:29:29,788 - truncate_tables - INFO - Successfully truncated table workflow_temp.success_messages
2025-05-12 15:29:29,788 - truncate_tables - INFO - Truncating table workflow_temp.entity_business_rules
2025-05-12 15:29:29,793 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:29:29,799 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:29:29,803 - truncate_tables - INFO - Successfully truncated table workflow_temp.entity_business_rules
2025-05-12 15:29:29,803 - truncate_tables - INFO - Truncating table workflow_temp.output_stack
2025-05-12 15:29:29,807 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:29:29,813 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:29:29,816 - truncate_tables - INFO - Successfully truncated table workflow_temp.output_stack
2025-05-12 15:29:29,816 - truncate_tables - INFO - Truncating table workflow_temp.roles
2025-05-12 15:29:29,820 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:29:29,825 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:29:29,830 - truncate_tables - INFO - Successfully truncated table workflow_temp.roles
2025-05-12 15:29:29,831 - truncate_tables - INFO - Truncating table workflow_temp.data_mapping_stack
2025-05-12 15:29:29,834 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:29:29,842 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:29:29,846 - truncate_tables - INFO - Successfully truncated table workflow_temp.data_mapping_stack
2025-05-12 15:29:29,846 - truncate_tables - INFO - Truncating table workflow_temp.role
2025-05-12 15:29:29,851 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:29:29,859 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:29:29,861 - truncate_tables - INFO - Successfully truncated table workflow_temp.role
2025-05-12 15:29:29,861 - truncate_tables - INFO - Truncating table workflow_temp.go_performance_metrics
2025-05-12 15:29:29,866 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:29:29,872 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:29:29,875 - truncate_tables - INFO - Successfully truncated table workflow_temp.go_performance_metrics
2025-05-12 15:29:29,875 - truncate_tables - INFO - Truncating table workflow_temp.attribute_enum_values
2025-05-12 15:29:29,878 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:29:29,886 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:29:29,889 - truncate_tables - INFO - Successfully truncated table workflow_temp.attribute_enum_values
2025-05-12 15:29:29,889 - truncate_tables - INFO - Truncating table workflow_temp.execution_rules
2025-05-12 15:29:29,893 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:29:29,899 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:29:29,902 - truncate_tables - INFO - Successfully truncated table workflow_temp.execution_rules
2025-05-12 15:29:29,902 - truncate_tables - INFO - Truncating table workflow_temp.attribute_validations
2025-05-12 15:29:29,907 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:29:29,914 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:29:29,917 - truncate_tables - INFO - Successfully truncated table workflow_temp.attribute_validations
2025-05-12 15:29:29,917 - truncate_tables - INFO - Truncating table workflow_temp.medical_record
2025-05-12 15:29:29,923 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:29:29,930 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:29:29,932 - truncate_tables - INFO - Successfully truncated table workflow_temp.medical_record
2025-05-12 15:29:29,932 - truncate_tables - INFO - Truncating table workflow_temp.patient
2025-05-12 15:29:29,937 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:29:29,943 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:29:29,946 - truncate_tables - INFO - Successfully truncated table workflow_temp.patient
2025-05-12 15:29:29,946 - truncate_tables - INFO - Truncating table workflow_temp.tenants
2025-05-12 15:29:29,951 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:29:29,958 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:29:29,997 - truncate_tables - INFO - Successfully truncated table workflow_temp.tenants
2025-05-12 15:29:29,997 - truncate_tables - INFO - Truncating table workflow_temp.user_organizations
2025-05-12 15:29:30,003 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:29:30,011 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:29:30,014 - truncate_tables - INFO - Successfully truncated table workflow_temp.user_organizations
2025-05-12 15:29:30,014 - truncate_tables - INFO - Truncating table workflow_temp.ui_stack
2025-05-12 15:29:30,020 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:29:30,028 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:29:30,032 - truncate_tables - INFO - Successfully truncated table workflow_temp.ui_stack
2025-05-12 15:29:30,032 - truncate_tables - INFO - Truncating table workflow_temp.lo_input_validations
2025-05-12 15:29:30,037 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:29:30,045 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:29:30,047 - truncate_tables - INFO - Successfully truncated table workflow_temp.lo_input_validations
2025-05-12 15:29:30,047 - truncate_tables - INFO - Truncating table workflow_temp.dropdown_data_sources
2025-05-12 15:29:30,053 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:29:30,060 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:29:30,062 - truncate_tables - INFO - Successfully truncated table workflow_temp.dropdown_data_sources
2025-05-12 15:29:30,062 - truncate_tables - INFO - Truncating table workflow_temp.global_objectives
2025-05-12 15:29:30,068 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:29:30,076 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:29:30,110 - truncate_tables - INFO - Successfully truncated table workflow_temp.global_objectives
2025-05-12 15:29:30,110 - truncate_tables - INFO - Truncating table workflow_temp.permission_capabilities
2025-05-12 15:29:30,116 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:29:30,124 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:29:30,126 - truncate_tables - INFO - Successfully truncated table workflow_temp.permission_capabilities
2025-05-12 15:29:30,126 - truncate_tables - INFO - Truncating table workflow_temp.user
2025-05-12 15:29:30,132 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:29:30,139 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:29:30,141 - truncate_tables - INFO - Successfully truncated table workflow_temp.user
2025-05-12 15:29:30,141 - truncate_tables - INFO - Truncating table workflow_temp.user_role
2025-05-12 15:29:30,147 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:29:30,155 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:29:30,157 - truncate_tables - INFO - Successfully truncated table workflow_temp.user_role
2025-05-12 15:29:30,157 - truncate_tables - INFO - Truncating table workflow_temp.role_inheritance
2025-05-12 15:29:30,162 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:29:30,170 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:29:30,172 - truncate_tables - INFO - Successfully truncated table workflow_temp.role_inheritance
2025-05-12 15:29:30,172 - truncate_tables - INFO - Truncating table workflow_temp.workflow_instances
2025-05-12 15:29:30,176 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:29:30,183 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:29:30,188 - truncate_tables - INFO - Successfully truncated table workflow_temp.workflow_instances
2025-05-12 15:29:30,188 - truncate_tables - INFO - Truncating table workflow_temp.calculated_fields
2025-05-12 15:29:30,191 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:29:30,198 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:29:30,201 - truncate_tables - INFO - Successfully truncated table workflow_temp.calculated_fields
2025-05-12 15:29:30,201 - truncate_tables - INFO - Truncating table workflow_temp.entity_lifecycle_management
2025-05-12 15:29:30,205 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:29:30,211 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:29:30,213 - truncate_tables - INFO - Successfully truncated table workflow_temp.entity_lifecycle_management
2025-05-12 15:29:30,213 - truncate_tables - INFO - Truncating table workflow_temp.lo_data_mappings
2025-05-12 15:29:30,217 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:29:30,222 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:29:30,224 - truncate_tables - INFO - Successfully truncated table workflow_temp.lo_data_mappings
2025-05-12 15:29:30,224 - truncate_tables - INFO - Truncating table workflow_temp.input_data_sources
2025-05-12 15:29:30,228 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:29:30,234 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:29:30,237 - truncate_tables - INFO - Successfully truncated table workflow_temp.input_data_sources
2025-05-12 15:29:30,237 - truncate_tables - INFO - Truncating table workflow_temp.agent_rights
2025-05-12 15:29:30,242 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:29:30,248 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:29:30,250 - truncate_tables - INFO - Successfully truncated table workflow_temp.agent_rights
2025-05-12 15:29:30,250 - truncate_tables - INFO - Truncating table workflow_temp.input_dependencies
2025-05-12 15:29:30,253 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:29:30,259 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:29:30,261 - truncate_tables - INFO - Successfully truncated table workflow_temp.input_dependencies
2025-05-12 15:29:30,261 - truncate_tables - INFO - Truncating table workflow_temp.terminal_pathways
2025-05-12 15:29:30,265 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:29:30,271 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:29:30,273 - truncate_tables - INFO - Successfully truncated table workflow_temp.terminal_pathways
2025-05-12 15:29:30,273 - truncate_tables - INFO - Truncating table workflow_temp.entities
2025-05-12 15:29:30,279 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:29:30,286 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:29:30,298 - truncate_tables - INFO - Successfully truncated table workflow_temp.entities
2025-05-12 15:29:30,298 - truncate_tables - INFO - Truncating table workflow_temp.agent_stack
2025-05-12 15:29:30,304 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:29:30,312 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:29:30,315 - truncate_tables - INFO - Successfully truncated table workflow_temp.agent_stack
2025-05-12 15:29:30,315 - truncate_tables - INFO - Truncating table workflow_temp.entity_attributes
2025-05-12 15:29:30,321 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:29:30,329 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:29:30,337 - truncate_tables - INFO - Successfully truncated table workflow_temp.entity_attributes
2025-05-12 15:29:30,337 - truncate_tables - INFO - Truncating table workflow_temp.e1.employee
2025-05-12 15:29:30,342 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:29:30,350 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:29:30,350 - db_utils - ERROR - Database error: cross-database references are not implemented: "workflow_temp.e1.employee"
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.FeatureNotSupported: cross-database references are not implemented: "workflow_temp.e1.employee"

2025-05-12 15:35:08,263 - truncate_tables - INFO - Truncating all tables in schema workflow_temp
2025-05-12 15:35:08,268 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:35:08,271 - truncate_tables - INFO - Truncating table workflow_temp.alembic_version
2025-05-12 15:35:08,276 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:35:08,283 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:35:08,285 - truncate_tables - INFO - Successfully truncated table workflow_temp.alembic_version
2025-05-12 15:35:08,285 - truncate_tables - INFO - Truncating table workflow_temp.e1_employee
2025-05-12 15:35:08,289 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:35:08,295 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:35:08,298 - truncate_tables - INFO - Successfully truncated table workflow_temp.e1_employee
2025-05-12 15:35:08,298 - truncate_tables - INFO - Truncating table workflow_temp.entity_permissions
2025-05-12 15:35:08,302 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:35:08,308 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:35:08,310 - truncate_tables - INFO - Successfully truncated table workflow_temp.entity_permissions
2025-05-12 15:35:08,310 - truncate_tables - INFO - Truncating table workflow_temp.lo_input_stack
2025-05-12 15:35:08,314 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:35:08,319 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:35:08,327 - truncate_tables - INFO - Successfully truncated table workflow_temp.lo_input_stack
2025-05-12 15:35:08,327 - truncate_tables - INFO - Truncating table workflow_temp.e2_department
2025-05-12 15:35:08,331 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:35:08,338 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:35:08,341 - truncate_tables - INFO - Successfully truncated table workflow_temp.e2_department
2025-05-12 15:35:08,341 - truncate_tables - INFO - Truncating table workflow_temp.objective_permissions
2025-05-12 15:35:08,345 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:35:08,352 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:35:08,354 - truncate_tables - INFO - Successfully truncated table workflow_temp.objective_permissions
2025-05-12 15:35:08,354 - truncate_tables - INFO - Truncating table workflow_temp.input_stack
2025-05-12 15:35:08,358 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:35:08,365 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:35:08,368 - truncate_tables - INFO - Successfully truncated table workflow_temp.input_stack
2025-05-12 15:35:08,368 - truncate_tables - INFO - Truncating table workflow_temp.lo_input_items
2025-05-12 15:35:08,371 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:35:08,378 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:35:08,383 - truncate_tables - INFO - Successfully truncated table workflow_temp.lo_input_items
2025-05-12 15:35:08,383 - truncate_tables - INFO - Truncating table workflow_temp.lab_report
2025-05-12 15:35:08,388 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:35:08,394 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:35:08,396 - truncate_tables - INFO - Successfully truncated table workflow_temp.lab_report
2025-05-12 15:35:08,396 - truncate_tables - INFO - Truncating table workflow_temp.permission_types
2025-05-12 15:35:08,400 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:35:08,406 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:35:08,411 - truncate_tables - INFO - Successfully truncated table workflow_temp.permission_types
2025-05-12 15:35:08,411 - truncate_tables - INFO - Truncating table workflow_temp.lo_output_stack
2025-05-12 15:35:08,416 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:35:08,423 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:35:08,427 - truncate_tables - INFO - Successfully truncated table workflow_temp.lo_output_stack
2025-05-12 15:35:08,428 - truncate_tables - INFO - Truncating table workflow_temp.attribute_ui_controls
2025-05-12 15:35:08,431 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:35:08,438 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:35:08,441 - truncate_tables - INFO - Successfully truncated table workflow_temp.attribute_ui_controls
2025-05-12 15:35:08,441 - truncate_tables - INFO - Truncating table workflow_temp.organizational_units
2025-05-12 15:35:08,445 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:35:08,452 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:35:08,454 - truncate_tables - INFO - Successfully truncated table workflow_temp.organizational_units
2025-05-12 15:35:08,454 - truncate_tables - INFO - Truncating table workflow_temp.lo_output_items
2025-05-12 15:35:08,459 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:35:08,464 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:35:08,467 - truncate_tables - INFO - Successfully truncated table workflow_temp.lo_output_items
2025-05-12 15:35:08,467 - truncate_tables - INFO - Truncating table workflow_temp.conditional_success_messages
2025-05-12 15:35:08,473 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:35:08,480 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:35:08,483 - truncate_tables - INFO - Successfully truncated table workflow_temp.conditional_success_messages
2025-05-12 15:35:08,483 - truncate_tables - INFO - Truncating table workflow_temp.agent_stack
2025-05-12 15:35:08,488 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:35:08,494 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:35:08,496 - truncate_tables - INFO - Successfully truncated table workflow_temp.agent_stack
2025-05-12 15:35:08,496 - truncate_tables - INFO - Truncating table workflow_temp.prescription
2025-05-12 15:35:08,500 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:35:08,507 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:35:08,509 - truncate_tables - INFO - Successfully truncated table workflow_temp.prescription
2025-05-12 15:35:08,509 - truncate_tables - INFO - Truncating table workflow_temp.lo_data_mapping_stack
2025-05-12 15:35:08,513 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:35:08,519 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:35:08,522 - truncate_tables - INFO - Successfully truncated table workflow_temp.lo_data_mapping_stack
2025-05-12 15:35:08,522 - truncate_tables - INFO - Truncating table workflow_temp.execution_path_tracking
2025-05-12 15:35:08,526 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:35:08,531 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:35:08,534 - truncate_tables - INFO - Successfully truncated table workflow_temp.execution_path_tracking
2025-05-12 15:35:08,534 - truncate_tables - INFO - Truncating table workflow_temp.lo_input_execution
2025-05-12 15:35:08,537 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:35:08,542 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:35:08,544 - truncate_tables - INFO - Successfully truncated table workflow_temp.lo_input_execution
2025-05-12 15:35:08,544 - truncate_tables - INFO - Truncating table workflow_temp.entity_business_rules
2025-05-12 15:35:08,548 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:35:08,554 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:35:08,556 - truncate_tables - INFO - Successfully truncated table workflow_temp.entity_business_rules
2025-05-12 15:35:08,556 - truncate_tables - INFO - Truncating table workflow_temp.output_stack
2025-05-12 15:35:08,559 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:35:08,565 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:35:08,567 - truncate_tables - INFO - Successfully truncated table workflow_temp.output_stack
2025-05-12 15:35:08,567 - truncate_tables - INFO - Truncating table workflow_temp.roles
2025-05-12 15:35:08,571 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:35:08,576 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:35:08,581 - truncate_tables - INFO - Successfully truncated table workflow_temp.roles
2025-05-12 15:35:08,581 - truncate_tables - INFO - Truncating table workflow_temp.data_mapping_stack
2025-05-12 15:35:08,584 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:35:08,590 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:35:08,594 - truncate_tables - INFO - Successfully truncated table workflow_temp.data_mapping_stack
2025-05-12 15:35:08,594 - truncate_tables - INFO - Truncating table workflow_temp.role
2025-05-12 15:35:08,597 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:35:08,603 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:35:08,605 - truncate_tables - INFO - Successfully truncated table workflow_temp.role
2025-05-12 15:35:08,605 - truncate_tables - INFO - Truncating table workflow_temp.go_performance_metrics
2025-05-12 15:35:08,609 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:35:08,614 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:35:08,616 - truncate_tables - INFO - Successfully truncated table workflow_temp.go_performance_metrics
2025-05-12 15:35:08,616 - truncate_tables - INFO - Truncating table workflow_temp.input_dependencies
2025-05-12 15:35:08,619 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:35:08,627 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:35:08,630 - truncate_tables - INFO - Successfully truncated table workflow_temp.input_dependencies
2025-05-12 15:35:08,630 - truncate_tables - INFO - Truncating table workflow_temp.lo_nested_functions
2025-05-12 15:35:08,634 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:35:08,640 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:35:08,642 - truncate_tables - INFO - Successfully truncated table workflow_temp.lo_nested_functions
2025-05-12 15:35:08,642 - truncate_tables - INFO - Truncating table workflow_temp.lo_dependencies
2025-05-12 15:35:08,647 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:35:08,652 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:35:08,655 - truncate_tables - INFO - Successfully truncated table workflow_temp.lo_dependencies
2025-05-12 15:35:08,655 - truncate_tables - INFO - Truncating table workflow_temp.data_mappings
2025-05-12 15:35:08,659 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:35:08,666 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:35:08,668 - truncate_tables - INFO - Successfully truncated table workflow_temp.data_mappings
2025-05-12 15:35:08,668 - truncate_tables - INFO - Truncating table workflow_temp.execution_pathway_conditions
2025-05-12 15:35:08,672 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:35:08,677 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:35:08,679 - truncate_tables - INFO - Successfully truncated table workflow_temp.execution_pathway_conditions
2025-05-12 15:35:08,679 - truncate_tables - INFO - Truncating table workflow_temp.execution_pathways
2025-05-12 15:35:08,685 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:35:08,692 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:35:08,694 - truncate_tables - INFO - Successfully truncated table workflow_temp.execution_pathways
2025-05-12 15:35:08,694 - truncate_tables - INFO - Truncating table workflow_temp.lo_output_execution
2025-05-12 15:35:08,700 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:35:08,707 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:35:08,709 - truncate_tables - INFO - Successfully truncated table workflow_temp.lo_output_execution
2025-05-12 15:35:08,710 - truncate_tables - INFO - Truncating table workflow_temp.attribute_enum_values
2025-05-12 15:35:08,714 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:35:08,721 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:35:08,723 - truncate_tables - INFO - Successfully truncated table workflow_temp.attribute_enum_values
2025-05-12 15:35:08,723 - truncate_tables - INFO - Truncating table workflow_temp.execution_rules
2025-05-12 15:35:08,729 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:35:08,736 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:35:08,739 - truncate_tables - INFO - Successfully truncated table workflow_temp.execution_rules
2025-05-12 15:35:08,739 - truncate_tables - INFO - Truncating table workflow_temp.lo_system_functions
2025-05-12 15:35:08,744 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:35:08,752 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:35:08,754 - truncate_tables - INFO - Successfully truncated table workflow_temp.lo_system_functions
2025-05-12 15:35:08,754 - truncate_tables - INFO - Truncating table workflow_temp.mapping_rules
2025-05-12 15:35:08,759 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:35:08,767 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:35:08,769 - truncate_tables - INFO - Successfully truncated table workflow_temp.mapping_rules
2025-05-12 15:35:08,769 - truncate_tables - INFO - Truncating table workflow_temp.metrics_aggregation
2025-05-12 15:35:08,773 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:35:08,781 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:35:08,782 - truncate_tables - INFO - Successfully truncated table workflow_temp.metrics_aggregation
2025-05-12 15:35:08,782 - truncate_tables - INFO - Truncating table workflow_temp.metrics_reporting
2025-05-12 15:35:08,788 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:35:08,794 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:35:08,796 - truncate_tables - INFO - Successfully truncated table workflow_temp.metrics_reporting
2025-05-12 15:35:08,796 - truncate_tables - INFO - Truncating table workflow_temp.output_items
2025-05-12 15:35:08,802 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:35:08,809 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:35:08,811 - truncate_tables - INFO - Successfully truncated table workflow_temp.output_items
2025-05-12 15:35:08,811 - truncate_tables - INFO - Truncating table workflow_temp.success_messages
2025-05-12 15:35:08,815 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:35:08,822 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:35:08,825 - truncate_tables - INFO - Successfully truncated table workflow_temp.success_messages
2025-05-12 15:35:08,825 - truncate_tables - INFO - Truncating table workflow_temp.input_data_sources
2025-05-12 15:35:08,830 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:35:08,837 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:35:08,839 - truncate_tables - INFO - Successfully truncated table workflow_temp.input_data_sources
2025-05-12 15:35:08,839 - truncate_tables - INFO - Truncating table workflow_temp.attribute_validations
2025-05-12 15:35:08,843 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:35:08,849 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:35:08,852 - truncate_tables - INFO - Successfully truncated table workflow_temp.attribute_validations
2025-05-12 15:35:08,852 - truncate_tables - INFO - Truncating table workflow_temp.medical_record
2025-05-12 15:35:08,857 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:35:08,863 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:35:08,865 - truncate_tables - INFO - Successfully truncated table workflow_temp.medical_record
2025-05-12 15:35:08,865 - truncate_tables - INFO - Truncating table workflow_temp.patient
2025-05-12 15:35:08,869 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:35:08,876 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:35:08,879 - truncate_tables - INFO - Successfully truncated table workflow_temp.patient
2025-05-12 15:35:08,879 - truncate_tables - INFO - Truncating table workflow_temp.tenants
2025-05-12 15:35:08,885 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:35:08,891 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:35:08,923 - truncate_tables - INFO - Successfully truncated table workflow_temp.tenants
2025-05-12 15:35:08,923 - truncate_tables - INFO - Truncating table workflow_temp.user_organizations
2025-05-12 15:35:08,929 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:35:08,935 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:35:08,938 - truncate_tables - INFO - Successfully truncated table workflow_temp.user_organizations
2025-05-12 15:35:08,938 - truncate_tables - INFO - Truncating table workflow_temp.ui_stack
2025-05-12 15:35:08,943 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:35:08,949 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:35:08,953 - truncate_tables - INFO - Successfully truncated table workflow_temp.ui_stack
2025-05-12 15:35:08,953 - truncate_tables - INFO - Truncating table workflow_temp.lo_input_validations
2025-05-12 15:35:08,957 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:35:08,964 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:35:08,966 - truncate_tables - INFO - Successfully truncated table workflow_temp.lo_input_validations
2025-05-12 15:35:08,966 - truncate_tables - INFO - Truncating table workflow_temp.dropdown_data_sources
2025-05-12 15:35:08,971 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:35:08,978 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:35:08,980 - truncate_tables - INFO - Successfully truncated table workflow_temp.dropdown_data_sources
2025-05-12 15:35:08,980 - truncate_tables - INFO - Truncating table workflow_temp.global_objectives
2025-05-12 15:35:08,986 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:35:08,992 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:35:09,024 - truncate_tables - INFO - Successfully truncated table workflow_temp.global_objectives
2025-05-12 15:35:09,025 - truncate_tables - INFO - Truncating table workflow_temp.permission_capabilities
2025-05-12 15:35:09,029 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:35:09,037 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:35:09,039 - truncate_tables - INFO - Successfully truncated table workflow_temp.permission_capabilities
2025-05-12 15:35:09,039 - truncate_tables - INFO - Truncating table workflow_temp.user
2025-05-12 15:35:09,043 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:35:09,050 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:35:09,053 - truncate_tables - INFO - Successfully truncated table workflow_temp.user
2025-05-12 15:35:09,053 - truncate_tables - INFO - Truncating table workflow_temp.user_role
2025-05-12 15:35:09,057 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:35:09,065 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:35:09,068 - truncate_tables - INFO - Successfully truncated table workflow_temp.user_role
2025-05-12 15:35:09,068 - truncate_tables - INFO - Truncating table workflow_temp.role_inheritance
2025-05-12 15:35:09,073 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:35:09,081 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:35:09,083 - truncate_tables - INFO - Successfully truncated table workflow_temp.role_inheritance
2025-05-12 15:35:09,084 - truncate_tables - INFO - Truncating table workflow_temp.workflow_instances
2025-05-12 15:35:09,089 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:35:09,097 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:35:09,101 - truncate_tables - INFO - Successfully truncated table workflow_temp.workflow_instances
2025-05-12 15:35:09,101 - truncate_tables - INFO - Truncating table workflow_temp.calculated_fields
2025-05-12 15:35:09,107 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:35:09,114 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:35:09,116 - truncate_tables - INFO - Successfully truncated table workflow_temp.calculated_fields
2025-05-12 15:35:09,117 - truncate_tables - INFO - Truncating table workflow_temp.lo_data_mappings
2025-05-12 15:35:09,121 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:35:09,129 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:35:09,130 - truncate_tables - INFO - Successfully truncated table workflow_temp.lo_data_mappings
2025-05-12 15:35:09,131 - truncate_tables - INFO - Truncating table workflow_temp.terminal_pathways
2025-05-12 15:35:09,135 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:35:09,142 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:35:09,144 - truncate_tables - INFO - Successfully truncated table workflow_temp.terminal_pathways
2025-05-12 15:35:09,144 - truncate_tables - INFO - Truncating table workflow_temp.entities
2025-05-12 15:35:09,149 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:35:09,157 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:35:09,170 - truncate_tables - INFO - Successfully truncated table workflow_temp.entities
2025-05-12 15:35:09,170 - truncate_tables - INFO - Truncating table workflow_temp.entity_lifecycle_management
2025-05-12 15:35:09,175 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:35:09,182 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:35:09,185 - truncate_tables - INFO - Successfully truncated table workflow_temp.entity_lifecycle_management
2025-05-12 15:35:09,185 - truncate_tables - INFO - Truncating table workflow_temp.agent_rights
2025-05-12 15:35:09,189 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:35:09,196 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:35:09,198 - truncate_tables - INFO - Successfully truncated table workflow_temp.agent_rights
2025-05-12 15:35:09,198 - truncate_tables - INFO - Truncating table workflow_temp.go_lo_mapping
2025-05-12 15:35:09,202 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:35:09,208 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:35:09,210 - truncate_tables - INFO - Successfully truncated table workflow_temp.go_lo_mapping
2025-05-12 15:35:09,210 - truncate_tables - INFO - Truncating table workflow_temp.output_triggers
2025-05-12 15:35:09,213 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:35:09,220 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:35:09,222 - truncate_tables - INFO - Successfully truncated table workflow_temp.output_triggers
2025-05-12 15:35:09,222 - truncate_tables - INFO - Truncating table workflow_temp.entity_attributes
2025-05-12 15:35:09,225 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:35:09,231 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:35:09,240 - truncate_tables - INFO - Successfully truncated table workflow_temp.entity_attributes
2025-05-12 15:35:09,240 - truncate_tables - INFO - Truncating table workflow_temp.user_roles
2025-05-12 15:35:09,245 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:35:09,252 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:35:09,254 - truncate_tables - INFO - Successfully truncated table workflow_temp.user_roles
2025-05-12 15:35:09,254 - truncate_tables - INFO - Truncating table workflow_temp.local_objectives
2025-05-12 15:35:09,259 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:35:09,267 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:35:09,289 - truncate_tables - INFO - Successfully truncated table workflow_temp.local_objectives
2025-05-12 15:35:09,290 - truncate_tables - INFO - Truncating table workflow_temp.entity_attribute_metadata
2025-05-12 15:35:09,295 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:35:09,301 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:35:09,303 - truncate_tables - INFO - Successfully truncated table workflow_temp.entity_attribute_metadata
2025-05-12 15:35:09,304 - truncate_tables - INFO - Truncating table workflow_temp.system_functions
2025-05-12 15:35:09,308 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:35:09,316 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:35:09,319 - truncate_tables - INFO - Successfully truncated table workflow_temp.system_functions
2025-05-12 15:35:09,319 - truncate_tables - INFO - Truncating table workflow_temp.permission_contexts
2025-05-12 15:35:09,325 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:35:09,331 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:35:09,337 - truncate_tables - INFO - Successfully truncated table workflow_temp.permission_contexts
2025-05-12 15:35:09,337 - truncate_tables - INFO - Truncating table workflow_temp.workflow_results
2025-05-12 15:35:09,343 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:35:09,350 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:35:09,352 - truncate_tables - INFO - Successfully truncated table workflow_temp.workflow_results
2025-05-12 15:35:09,352 - truncate_tables - INFO - Truncating table workflow_temp.users
2025-05-12 15:35:09,357 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:35:09,364 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:35:09,369 - truncate_tables - INFO - Successfully truncated table workflow_temp.users
2025-05-12 15:35:09,369 - truncate_tables - INFO - Truncating table workflow_temp.user_oauth_tokens
2025-05-12 15:35:09,374 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:35:09,381 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:35:09,383 - truncate_tables - INFO - Successfully truncated table workflow_temp.user_oauth_tokens
2025-05-12 15:35:09,383 - truncate_tables - INFO - Truncating table workflow_temp.workflow_transaction
2025-05-12 15:35:09,388 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:35:09,395 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:35:09,398 - truncate_tables - INFO - Successfully truncated table workflow_temp.workflow_transaction
2025-05-12 15:35:09,398 - truncate_tables - INFO - Truncating table workflow_temp.role_permissions
2025-05-12 15:35:09,402 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:35:09,409 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:35:09,411 - truncate_tables - INFO - Successfully truncated table workflow_temp.role_permissions
2025-05-12 15:35:09,411 - truncate_tables - INFO - Truncating table workflow_temp.runtime_metrics_stack
2025-05-12 15:35:09,416 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:35:09,422 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:35:09,426 - truncate_tables - INFO - Successfully truncated table workflow_temp.runtime_metrics_stack
2025-05-12 15:35:09,426 - truncate_tables - INFO - Truncating table workflow_temp.user_sessions
2025-05-12 15:35:09,431 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:35:09,438 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:35:09,441 - truncate_tables - INFO - Successfully truncated table workflow_temp.user_sessions
2025-05-12 15:35:09,441 - truncate_tables - INFO - Truncating table workflow_temp.input_items
2025-05-12 15:35:09,446 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:35:09,451 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:35:09,453 - truncate_tables - INFO - Successfully truncated table workflow_temp.input_items
2025-05-12 15:35:09,454 - truncate_tables - INFO - Truncating table workflow_temp.entity_relationships
2025-05-12 15:35:09,458 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:35:09,463 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:35:09,465 - truncate_tables - INFO - Successfully truncated table workflow_temp.entity_relationships
2025-05-12 15:35:09,465 - truncate_tables - INFO - Truncating table workflow_temp.lo_output_triggers
2025-05-12 15:35:09,470 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:35:09,475 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:35:09,477 - truncate_tables - INFO - Successfully truncated table workflow_temp.lo_output_triggers
2025-05-12 15:35:09,477 - truncate_tables - INFO - Truncating table workflow_temp.ui_elements
2025-05-12 15:35:09,482 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:35:09,487 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:35:09,489 - truncate_tables - INFO - Successfully truncated table workflow_temp.ui_elements
2025-05-12 15:35:09,489 - truncate_tables - INFO - Successfully truncated all tables in schema workflow_temp
2025-05-12 15:40:11,814 - truncate_tables - INFO - Truncating all tables in schema workflow_temp
2025-05-12 15:40:11,819 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:40:11,822 - truncate_tables - INFO - Truncating table workflow_temp.alembic_version
2025-05-12 15:40:11,827 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:40:11,834 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:40:11,838 - truncate_tables - INFO - Successfully truncated table workflow_temp.alembic_version
2025-05-12 15:40:11,838 - truncate_tables - INFO - Truncating table workflow_temp.e1_employee
2025-05-12 15:40:11,842 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:40:11,847 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:40:11,850 - truncate_tables - INFO - Successfully truncated table workflow_temp.e1_employee
2025-05-12 15:40:11,850 - truncate_tables - INFO - Truncating table workflow_temp.entity_permissions
2025-05-12 15:40:11,854 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:40:11,859 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:40:11,861 - truncate_tables - INFO - Successfully truncated table workflow_temp.entity_permissions
2025-05-12 15:40:11,861 - truncate_tables - INFO - Truncating table workflow_temp.e2_department
2025-05-12 15:40:11,865 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:40:11,871 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:40:11,873 - truncate_tables - INFO - Successfully truncated table workflow_temp.e2_department
2025-05-12 15:40:11,873 - truncate_tables - INFO - Truncating table workflow_temp.objective_permissions
2025-05-12 15:40:11,876 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:40:11,882 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:40:11,884 - truncate_tables - INFO - Successfully truncated table workflow_temp.objective_permissions
2025-05-12 15:40:11,884 - truncate_tables - INFO - Truncating table workflow_temp.input_stack
2025-05-12 15:40:11,887 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:40:11,893 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:40:11,896 - truncate_tables - INFO - Successfully truncated table workflow_temp.input_stack
2025-05-12 15:40:11,896 - truncate_tables - INFO - Truncating table workflow_temp.lo_input_items
2025-05-12 15:40:11,900 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:40:11,907 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:40:11,915 - truncate_tables - INFO - Successfully truncated table workflow_temp.lo_input_items
2025-05-12 15:40:11,915 - truncate_tables - INFO - Truncating table workflow_temp.lab_report
2025-05-12 15:40:11,920 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:40:11,926 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:40:11,928 - truncate_tables - INFO - Successfully truncated table workflow_temp.lab_report
2025-05-12 15:40:11,928 - truncate_tables - INFO - Truncating table workflow_temp.permission_types
2025-05-12 15:40:11,932 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:40:11,938 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:40:11,942 - truncate_tables - INFO - Successfully truncated table workflow_temp.permission_types
2025-05-12 15:40:11,943 - truncate_tables - INFO - Truncating table workflow_temp.lo_output_stack
2025-05-12 15:40:11,948 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:40:11,955 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:40:11,958 - truncate_tables - INFO - Successfully truncated table workflow_temp.lo_output_stack
2025-05-12 15:40:11,958 - truncate_tables - INFO - Truncating table workflow_temp.attribute_ui_controls
2025-05-12 15:40:11,965 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:40:11,972 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:40:11,974 - truncate_tables - INFO - Successfully truncated table workflow_temp.attribute_ui_controls
2025-05-12 15:40:11,974 - truncate_tables - INFO - Truncating table workflow_temp.organizational_units
2025-05-12 15:40:11,978 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:40:11,985 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:40:11,987 - truncate_tables - INFO - Successfully truncated table workflow_temp.organizational_units
2025-05-12 15:40:11,987 - truncate_tables - INFO - Truncating table workflow_temp.lo_output_items
2025-05-12 15:40:11,992 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:40:12,000 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:40:12,004 - truncate_tables - INFO - Successfully truncated table workflow_temp.lo_output_items
2025-05-12 15:40:12,004 - truncate_tables - INFO - Truncating table workflow_temp.agent_stack
2025-05-12 15:40:12,008 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:40:12,016 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:40:12,018 - truncate_tables - INFO - Successfully truncated table workflow_temp.agent_stack
2025-05-12 15:40:12,018 - truncate_tables - INFO - Truncating table workflow_temp.conditional_success_messages
2025-05-12 15:40:12,024 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:40:12,030 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:40:12,032 - truncate_tables - INFO - Successfully truncated table workflow_temp.conditional_success_messages
2025-05-12 15:40:12,032 - truncate_tables - INFO - Truncating table workflow_temp.prescription
2025-05-12 15:40:12,037 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:40:12,045 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:40:12,047 - truncate_tables - INFO - Successfully truncated table workflow_temp.prescription
2025-05-12 15:40:12,048 - truncate_tables - INFO - Truncating table workflow_temp.lo_data_mapping_stack
2025-05-12 15:40:12,054 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:40:12,062 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:40:12,065 - truncate_tables - INFO - Successfully truncated table workflow_temp.lo_data_mapping_stack
2025-05-12 15:40:12,065 - truncate_tables - INFO - Truncating table workflow_temp.execution_path_tracking
2025-05-12 15:40:12,069 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:40:12,076 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:40:12,078 - truncate_tables - INFO - Successfully truncated table workflow_temp.execution_path_tracking
2025-05-12 15:40:12,078 - truncate_tables - INFO - Truncating table workflow_temp.entity_business_rules
2025-05-12 15:40:12,082 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:40:12,089 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:40:12,092 - truncate_tables - INFO - Successfully truncated table workflow_temp.entity_business_rules
2025-05-12 15:40:12,092 - truncate_tables - INFO - Truncating table workflow_temp.lo_input_stack
2025-05-12 15:40:12,096 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:40:12,104 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:40:12,111 - truncate_tables - INFO - Successfully truncated table workflow_temp.lo_input_stack
2025-05-12 15:40:12,111 - truncate_tables - INFO - Truncating table workflow_temp.lo_dependencies
2025-05-12 15:40:12,114 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:40:12,121 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:40:12,125 - truncate_tables - INFO - Successfully truncated table workflow_temp.lo_dependencies
2025-05-12 15:40:12,125 - truncate_tables - INFO - Truncating table workflow_temp.output_stack
2025-05-12 15:40:12,129 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:40:12,136 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:40:12,139 - truncate_tables - INFO - Successfully truncated table workflow_temp.output_stack
2025-05-12 15:40:12,139 - truncate_tables - INFO - Truncating table workflow_temp.roles
2025-05-12 15:40:12,143 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:40:12,150 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:40:12,155 - truncate_tables - INFO - Successfully truncated table workflow_temp.roles
2025-05-12 15:40:12,155 - truncate_tables - INFO - Truncating table workflow_temp.data_mapping_stack
2025-05-12 15:40:12,159 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:40:12,166 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:40:12,170 - truncate_tables - INFO - Successfully truncated table workflow_temp.data_mapping_stack
2025-05-12 15:40:12,170 - truncate_tables - INFO - Truncating table workflow_temp.role
2025-05-12 15:40:12,174 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:40:12,179 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:40:12,182 - truncate_tables - INFO - Successfully truncated table workflow_temp.role
2025-05-12 15:40:12,182 - truncate_tables - INFO - Truncating table workflow_temp.go_performance_metrics
2025-05-12 15:40:12,185 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:40:12,191 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:40:12,193 - truncate_tables - INFO - Successfully truncated table workflow_temp.go_performance_metrics
2025-05-12 15:40:12,193 - truncate_tables - INFO - Truncating table workflow_temp.output_items
2025-05-12 15:40:12,197 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:40:12,203 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:40:12,204 - truncate_tables - INFO - Successfully truncated table workflow_temp.output_items
2025-05-12 15:40:12,204 - truncate_tables - INFO - Truncating table workflow_temp.success_messages
2025-05-12 15:40:12,208 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:40:12,213 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:40:12,216 - truncate_tables - INFO - Successfully truncated table workflow_temp.success_messages
2025-05-12 15:40:12,216 - truncate_tables - INFO - Truncating table workflow_temp.attribute_enum_values
2025-05-12 15:40:12,219 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:40:12,224 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:40:12,226 - truncate_tables - INFO - Successfully truncated table workflow_temp.attribute_enum_values
2025-05-12 15:40:12,226 - truncate_tables - INFO - Truncating table workflow_temp.lo_nested_functions
2025-05-12 15:40:12,230 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:40:12,235 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:40:12,237 - truncate_tables - INFO - Successfully truncated table workflow_temp.lo_nested_functions
2025-05-12 15:40:12,237 - truncate_tables - INFO - Truncating table workflow_temp.attribute_validations
2025-05-12 15:40:12,241 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:40:12,246 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:40:12,249 - truncate_tables - INFO - Successfully truncated table workflow_temp.attribute_validations
2025-05-12 15:40:12,249 - truncate_tables - INFO - Truncating table workflow_temp.execution_rules
2025-05-12 15:40:12,252 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:40:12,257 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:40:12,259 - truncate_tables - INFO - Successfully truncated table workflow_temp.execution_rules
2025-05-12 15:40:12,259 - truncate_tables - INFO - Truncating table workflow_temp.lo_system_functions
2025-05-12 15:40:12,262 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:40:12,267 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:40:12,269 - truncate_tables - INFO - Successfully truncated table workflow_temp.lo_system_functions
2025-05-12 15:40:12,269 - truncate_tables - INFO - Truncating table workflow_temp.medical_record
2025-05-12 15:40:12,273 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:40:12,278 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:40:12,280 - truncate_tables - INFO - Successfully truncated table workflow_temp.medical_record
2025-05-12 15:40:12,280 - truncate_tables - INFO - Truncating table workflow_temp.patient
2025-05-12 15:40:12,285 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:40:12,291 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:40:12,294 - truncate_tables - INFO - Successfully truncated table workflow_temp.patient
2025-05-12 15:40:12,294 - truncate_tables - INFO - Truncating table workflow_temp.tenants
2025-05-12 15:40:12,297 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:40:12,305 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:40:12,338 - truncate_tables - INFO - Successfully truncated table workflow_temp.tenants
2025-05-12 15:40:12,338 - truncate_tables - INFO - Truncating table workflow_temp.ui_stack
2025-05-12 15:40:12,343 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:40:12,350 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:40:12,372 - truncate_tables - INFO - Successfully truncated table workflow_temp.ui_stack
2025-05-12 15:40:12,372 - truncate_tables - INFO - Truncating table workflow_temp.lo_input_validations
2025-05-12 15:40:12,378 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:40:12,386 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:40:12,389 - truncate_tables - INFO - Successfully truncated table workflow_temp.lo_input_validations
2025-05-12 15:40:12,389 - truncate_tables - INFO - Truncating table workflow_temp.user_organizations
2025-05-12 15:40:12,394 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:40:12,401 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:40:12,403 - truncate_tables - INFO - Successfully truncated table workflow_temp.user_organizations
2025-05-12 15:40:12,403 - truncate_tables - INFO - Truncating table workflow_temp.global_objectives
2025-05-12 15:40:12,408 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:40:12,416 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:40:12,455 - truncate_tables - INFO - Successfully truncated table workflow_temp.global_objectives
2025-05-12 15:40:12,455 - truncate_tables - INFO - Truncating table workflow_temp.permission_capabilities
2025-05-12 15:40:12,461 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:40:12,467 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:40:12,469 - truncate_tables - INFO - Successfully truncated table workflow_temp.permission_capabilities
2025-05-12 15:40:12,469 - truncate_tables - INFO - Truncating table workflow_temp.user
2025-05-12 15:40:12,474 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:40:12,482 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:40:12,485 - truncate_tables - INFO - Successfully truncated table workflow_temp.user
2025-05-12 15:40:12,485 - truncate_tables - INFO - Truncating table workflow_temp.user_role
2025-05-12 15:40:12,489 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:40:12,497 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:40:12,499 - truncate_tables - INFO - Successfully truncated table workflow_temp.user_role
2025-05-12 15:40:12,500 - truncate_tables - INFO - Truncating table workflow_temp.role_inheritance
2025-05-12 15:40:12,505 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:40:12,511 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:40:12,514 - truncate_tables - INFO - Successfully truncated table workflow_temp.role_inheritance
2025-05-12 15:40:12,514 - truncate_tables - INFO - Truncating table workflow_temp.workflow_instances
2025-05-12 15:40:12,517 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:40:12,525 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:40:12,530 - truncate_tables - INFO - Successfully truncated table workflow_temp.workflow_instances
2025-05-12 15:40:12,531 - truncate_tables - INFO - Truncating table workflow_temp.calculated_fields
2025-05-12 15:40:12,536 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:40:12,543 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:40:12,546 - truncate_tables - INFO - Successfully truncated table workflow_temp.calculated_fields
2025-05-12 15:40:12,546 - truncate_tables - INFO - Truncating table workflow_temp.data_mappings
2025-05-12 15:40:12,551 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:40:12,559 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:40:12,561 - truncate_tables - INFO - Successfully truncated table workflow_temp.data_mappings
2025-05-12 15:40:12,561 - truncate_tables - INFO - Truncating table workflow_temp.entities
2025-05-12 15:40:12,565 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:40:12,572 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:40:12,588 - truncate_tables - INFO - Successfully truncated table workflow_temp.entities
2025-05-12 15:40:12,588 - truncate_tables - INFO - Truncating table workflow_temp.entity_lifecycle_management
2025-05-12 15:40:12,591 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:40:12,598 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:40:12,621 - truncate_tables - INFO - Successfully truncated table workflow_temp.entity_lifecycle_management
2025-05-12 15:40:12,621 - truncate_tables - INFO - Truncating table workflow_temp.execution_pathway_conditions
2025-05-12 15:40:12,627 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:40:12,635 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:40:12,638 - truncate_tables - INFO - Successfully truncated table workflow_temp.execution_pathway_conditions
2025-05-12 15:40:12,639 - truncate_tables - INFO - Truncating table workflow_temp.execution_pathways
2025-05-12 15:40:12,643 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:40:12,650 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:40:12,653 - truncate_tables - INFO - Successfully truncated table workflow_temp.execution_pathways
2025-05-12 15:40:12,653 - truncate_tables - INFO - Truncating table workflow_temp.lo_input_execution
2025-05-12 15:40:12,657 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:40:12,663 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:40:12,666 - truncate_tables - INFO - Successfully truncated table workflow_temp.lo_input_execution
2025-05-12 15:40:12,666 - truncate_tables - INFO - Truncating table workflow_temp.mapping_rules
2025-05-12 15:40:12,672 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:40:12,678 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:40:12,680 - truncate_tables - INFO - Successfully truncated table workflow_temp.mapping_rules
2025-05-12 15:40:12,680 - truncate_tables - INFO - Truncating table workflow_temp.output_triggers
2025-05-12 15:40:12,685 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:40:12,691 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:40:12,694 - truncate_tables - INFO - Successfully truncated table workflow_temp.output_triggers
2025-05-12 15:40:12,694 - truncate_tables - INFO - Truncating table workflow_temp.entity_attributes
2025-05-12 15:40:12,698 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:40:12,705 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:40:12,717 - truncate_tables - INFO - Successfully truncated table workflow_temp.entity_attributes
2025-05-12 15:40:12,717 - truncate_tables - INFO - Truncating table workflow_temp.go_lo_mapping
2025-05-12 15:40:12,721 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:40:12,728 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:40:12,730 - truncate_tables - INFO - Successfully truncated table workflow_temp.go_lo_mapping
2025-05-12 15:40:12,730 - truncate_tables - INFO - Truncating table workflow_temp.lo_output_execution
2025-05-12 15:40:12,733 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:40:12,740 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:40:12,743 - truncate_tables - INFO - Successfully truncated table workflow_temp.lo_output_execution
2025-05-12 15:40:12,743 - truncate_tables - INFO - Truncating table workflow_temp.metrics_aggregation
2025-05-12 15:40:12,746 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:40:12,753 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:40:12,754 - truncate_tables - INFO - Successfully truncated table workflow_temp.metrics_aggregation
2025-05-12 15:40:12,754 - truncate_tables - INFO - Truncating table workflow_temp.metrics_reporting
2025-05-12 15:40:12,758 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:40:12,764 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:40:12,766 - truncate_tables - INFO - Successfully truncated table workflow_temp.metrics_reporting
2025-05-12 15:40:12,766 - truncate_tables - INFO - Truncating table workflow_temp.user_roles
2025-05-12 15:40:12,772 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:40:12,779 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:40:12,781 - truncate_tables - INFO - Successfully truncated table workflow_temp.user_roles
2025-05-12 15:40:12,781 - truncate_tables - INFO - Truncating table workflow_temp.local_objectives
2025-05-12 15:40:12,786 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:40:12,791 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:40:12,816 - truncate_tables - INFO - Successfully truncated table workflow_temp.local_objectives
2025-05-12 15:40:12,816 - truncate_tables - INFO - Truncating table workflow_temp.input_data_sources
2025-05-12 15:40:12,821 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:40:12,828 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:40:12,831 - truncate_tables - INFO - Successfully truncated table workflow_temp.input_data_sources
2025-05-12 15:40:12,831 - truncate_tables - INFO - Truncating table workflow_temp.input_dependencies
2025-05-12 15:40:12,834 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:40:12,841 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:40:12,843 - truncate_tables - INFO - Successfully truncated table workflow_temp.input_dependencies
2025-05-12 15:40:12,843 - truncate_tables - INFO - Truncating table workflow_temp.entity_attribute_metadata
2025-05-12 15:40:12,847 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:40:12,853 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:40:12,856 - truncate_tables - INFO - Successfully truncated table workflow_temp.entity_attribute_metadata
2025-05-12 15:40:12,856 - truncate_tables - INFO - Truncating table workflow_temp.system_functions
2025-05-12 15:40:12,860 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:40:12,867 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:40:12,869 - truncate_tables - INFO - Successfully truncated table workflow_temp.system_functions
2025-05-12 15:40:12,869 - truncate_tables - INFO - Truncating table workflow_temp.permission_contexts
2025-05-12 15:40:12,872 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:40:12,879 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:40:12,881 - truncate_tables - INFO - Successfully truncated table workflow_temp.permission_contexts
2025-05-12 15:40:12,882 - truncate_tables - INFO - Truncating table workflow_temp.workflow_results
2025-05-12 15:40:12,885 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:40:12,891 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:40:12,894 - truncate_tables - INFO - Successfully truncated table workflow_temp.workflow_results
2025-05-12 15:40:12,894 - truncate_tables - INFO - Truncating table workflow_temp.users
2025-05-12 15:40:12,897 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:40:12,903 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:40:12,909 - truncate_tables - INFO - Successfully truncated table workflow_temp.users
2025-05-12 15:40:12,909 - truncate_tables - INFO - Truncating table workflow_temp.user_oauth_tokens
2025-05-12 15:40:12,912 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:40:12,919 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:40:12,921 - truncate_tables - INFO - Successfully truncated table workflow_temp.user_oauth_tokens
2025-05-12 15:40:12,921 - truncate_tables - INFO - Truncating table workflow_temp.runtime_metrics_stack
2025-05-12 15:40:12,925 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:40:12,931 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:40:12,935 - truncate_tables - INFO - Successfully truncated table workflow_temp.runtime_metrics_stack
2025-05-12 15:40:12,935 - truncate_tables - INFO - Truncating table workflow_temp.terminal_pathways
2025-05-12 15:40:12,939 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:40:12,945 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:40:12,947 - truncate_tables - INFO - Successfully truncated table workflow_temp.terminal_pathways
2025-05-12 15:40:12,947 - truncate_tables - INFO - Truncating table workflow_temp.lo_data_mappings
2025-05-12 15:40:12,950 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:40:12,956 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:40:12,958 - truncate_tables - INFO - Successfully truncated table workflow_temp.lo_data_mappings
2025-05-12 15:40:12,958 - truncate_tables - INFO - Truncating table workflow_temp.agent_rights
2025-05-12 15:40:12,964 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:40:12,972 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:40:12,974 - truncate_tables - INFO - Successfully truncated table workflow_temp.agent_rights
2025-05-12 15:40:12,974 - truncate_tables - INFO - Truncating table workflow_temp.dropdown_data_sources
2025-05-12 15:40:12,979 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:40:12,986 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:40:12,989 - truncate_tables - INFO - Successfully truncated table workflow_temp.dropdown_data_sources
2025-05-12 15:40:12,989 - truncate_tables - INFO - Truncating table workflow_temp.workflow_transaction
2025-05-12 15:40:12,994 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:40:13,001 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:40:13,004 - truncate_tables - INFO - Successfully truncated table workflow_temp.workflow_transaction
2025-05-12 15:40:13,004 - truncate_tables - INFO - Truncating table workflow_temp.role_permissions
2025-05-12 15:40:13,009 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:40:13,015 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:40:13,016 - truncate_tables - INFO - Successfully truncated table workflow_temp.role_permissions
2025-05-12 15:40:13,016 - truncate_tables - INFO - Truncating table workflow_temp.user_sessions
2025-05-12 15:40:13,022 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:40:13,028 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:40:13,030 - truncate_tables - INFO - Successfully truncated table workflow_temp.user_sessions
2025-05-12 15:40:13,030 - truncate_tables - INFO - Truncating table workflow_temp.input_items
2025-05-12 15:40:13,035 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:40:13,041 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:40:13,043 - truncate_tables - INFO - Successfully truncated table workflow_temp.input_items
2025-05-12 15:40:13,043 - truncate_tables - INFO - Truncating table workflow_temp.entity_relationships
2025-05-12 15:40:13,048 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:40:13,054 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:40:13,056 - truncate_tables - INFO - Successfully truncated table workflow_temp.entity_relationships
2025-05-12 15:40:13,056 - truncate_tables - INFO - Truncating table workflow_temp.lo_output_triggers
2025-05-12 15:40:13,061 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:40:13,067 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:40:13,068 - truncate_tables - INFO - Successfully truncated table workflow_temp.lo_output_triggers
2025-05-12 15:40:13,068 - truncate_tables - INFO - Truncating table workflow_temp.ui_elements
2025-05-12 15:40:13,073 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:40:13,078 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:40:13,080 - truncate_tables - INFO - Successfully truncated table workflow_temp.ui_elements
2025-05-12 15:40:13,080 - truncate_tables - INFO - Successfully truncated all tables in schema workflow_temp
2025-05-12 15:41:51,843 - truncate_tables - INFO - Truncating all tables in schema workflow_temp
2025-05-12 15:41:51,848 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:41:51,851 - truncate_tables - INFO - Truncating table workflow_temp.alembic_version
2025-05-12 15:41:51,855 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:41:51,862 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:41:51,864 - truncate_tables - INFO - Successfully truncated table workflow_temp.alembic_version
2025-05-12 15:41:51,864 - truncate_tables - INFO - Truncating table workflow_temp.entity_permissions
2025-05-12 15:41:51,869 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:41:51,875 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:41:51,877 - truncate_tables - INFO - Successfully truncated table workflow_temp.entity_permissions
2025-05-12 15:41:51,877 - truncate_tables - INFO - Truncating table workflow_temp.e1_employee
2025-05-12 15:41:51,881 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:41:51,886 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:41:51,889 - truncate_tables - INFO - Successfully truncated table workflow_temp.e1_employee
2025-05-12 15:41:51,889 - truncate_tables - INFO - Truncating table workflow_temp.objective_permissions
2025-05-12 15:41:51,893 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:41:51,899 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:41:51,901 - truncate_tables - INFO - Successfully truncated table workflow_temp.objective_permissions
2025-05-12 15:41:51,901 - truncate_tables - INFO - Truncating table workflow_temp.input_stack
2025-05-12 15:41:51,904 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:41:51,910 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:41:51,913 - truncate_tables - INFO - Successfully truncated table workflow_temp.input_stack
2025-05-12 15:41:51,913 - truncate_tables - INFO - Truncating table workflow_temp.lo_input_items
2025-05-12 15:41:51,917 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:41:51,922 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:41:51,929 - truncate_tables - INFO - Successfully truncated table workflow_temp.lo_input_items
2025-05-12 15:41:51,929 - truncate_tables - INFO - Truncating table workflow_temp.e2_department
2025-05-12 15:41:51,933 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:41:51,938 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:41:51,940 - truncate_tables - INFO - Successfully truncated table workflow_temp.e2_department
2025-05-12 15:41:51,940 - truncate_tables - INFO - Truncating table workflow_temp.lab_report
2025-05-12 15:41:51,945 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:41:51,952 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:41:51,954 - truncate_tables - INFO - Successfully truncated table workflow_temp.lab_report
2025-05-12 15:41:51,954 - truncate_tables - INFO - Truncating table workflow_temp.permission_types
2025-05-12 15:41:51,957 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:41:51,963 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:41:51,967 - truncate_tables - INFO - Successfully truncated table workflow_temp.permission_types
2025-05-12 15:41:51,967 - truncate_tables - INFO - Truncating table workflow_temp.lo_output_stack
2025-05-12 15:41:51,971 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:41:51,976 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:41:51,980 - truncate_tables - INFO - Successfully truncated table workflow_temp.lo_output_stack
2025-05-12 15:41:51,980 - truncate_tables - INFO - Truncating table workflow_temp.attribute_ui_controls
2025-05-12 15:41:51,984 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:41:51,991 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:41:51,994 - truncate_tables - INFO - Successfully truncated table workflow_temp.attribute_ui_controls
2025-05-12 15:41:51,994 - truncate_tables - INFO - Truncating table workflow_temp.organizational_units
2025-05-12 15:41:51,998 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:41:52,005 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:41:52,007 - truncate_tables - INFO - Successfully truncated table workflow_temp.organizational_units
2025-05-12 15:41:52,007 - truncate_tables - INFO - Truncating table workflow_temp.lo_output_items
2025-05-12 15:41:52,013 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:41:52,019 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:41:52,023 - truncate_tables - INFO - Successfully truncated table workflow_temp.lo_output_items
2025-05-12 15:41:52,023 - truncate_tables - INFO - Truncating table workflow_temp.agent_stack
2025-05-12 15:41:52,028 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:41:52,036 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:41:52,039 - truncate_tables - INFO - Successfully truncated table workflow_temp.agent_stack
2025-05-12 15:41:52,039 - truncate_tables - INFO - Truncating table workflow_temp.conditional_success_messages
2025-05-12 15:41:52,043 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:41:52,049 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:41:52,052 - truncate_tables - INFO - Successfully truncated table workflow_temp.conditional_success_messages
2025-05-12 15:41:52,052 - truncate_tables - INFO - Truncating table workflow_temp.prescription
2025-05-12 15:41:52,055 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:41:52,062 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:41:52,064 - truncate_tables - INFO - Successfully truncated table workflow_temp.prescription
2025-05-12 15:41:52,064 - truncate_tables - INFO - Truncating table workflow_temp.lo_data_mapping_stack
2025-05-12 15:41:52,068 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:41:52,076 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:41:52,079 - truncate_tables - INFO - Successfully truncated table workflow_temp.lo_data_mapping_stack
2025-05-12 15:41:52,079 - truncate_tables - INFO - Truncating table workflow_temp.execution_path_tracking
2025-05-12 15:41:52,085 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:41:52,091 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:41:52,093 - truncate_tables - INFO - Successfully truncated table workflow_temp.execution_path_tracking
2025-05-12 15:41:52,093 - truncate_tables - INFO - Truncating table workflow_temp.entity_business_rules
2025-05-12 15:41:52,098 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:41:52,106 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:41:52,110 - truncate_tables - INFO - Successfully truncated table workflow_temp.entity_business_rules
2025-05-12 15:41:52,111 - truncate_tables - INFO - Truncating table workflow_temp.lo_input_stack
2025-05-12 15:41:52,114 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:41:52,121 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:41:52,128 - truncate_tables - INFO - Successfully truncated table workflow_temp.lo_input_stack
2025-05-12 15:41:52,128 - truncate_tables - INFO - Truncating table workflow_temp.lo_dependencies
2025-05-12 15:41:52,132 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:41:52,139 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:41:52,141 - truncate_tables - INFO - Successfully truncated table workflow_temp.lo_dependencies
2025-05-12 15:41:52,141 - truncate_tables - INFO - Truncating table workflow_temp.output_stack
2025-05-12 15:41:52,145 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:41:52,150 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:41:52,153 - truncate_tables - INFO - Successfully truncated table workflow_temp.output_stack
2025-05-12 15:41:52,153 - truncate_tables - INFO - Truncating table workflow_temp.roles
2025-05-12 15:41:52,157 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:41:52,163 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:41:52,167 - truncate_tables - INFO - Successfully truncated table workflow_temp.roles
2025-05-12 15:41:52,167 - truncate_tables - INFO - Truncating table workflow_temp.data_mapping_stack
2025-05-12 15:41:52,171 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:41:52,176 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:41:52,180 - truncate_tables - INFO - Successfully truncated table workflow_temp.data_mapping_stack
2025-05-12 15:41:52,180 - truncate_tables - INFO - Truncating table workflow_temp.lo_input_execution
2025-05-12 15:41:52,184 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:41:52,190 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:41:52,192 - truncate_tables - INFO - Successfully truncated table workflow_temp.lo_input_execution
2025-05-12 15:41:52,192 - truncate_tables - INFO - Truncating table workflow_temp.role
2025-05-12 15:41:52,195 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:41:52,200 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:41:52,202 - truncate_tables - INFO - Successfully truncated table workflow_temp.role
2025-05-12 15:41:52,202 - truncate_tables - INFO - Truncating table workflow_temp.go_performance_metrics
2025-05-12 15:41:52,205 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:41:52,210 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:41:52,212 - truncate_tables - INFO - Successfully truncated table workflow_temp.go_performance_metrics
2025-05-12 15:41:52,212 - truncate_tables - INFO - Truncating table workflow_temp.output_items
2025-05-12 15:41:52,215 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:41:52,222 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:41:52,225 - truncate_tables - INFO - Successfully truncated table workflow_temp.output_items
2025-05-12 15:41:52,225 - truncate_tables - INFO - Truncating table workflow_temp.success_messages
2025-05-12 15:41:52,229 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:41:52,237 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:41:52,239 - truncate_tables - INFO - Successfully truncated table workflow_temp.success_messages
2025-05-12 15:41:52,240 - truncate_tables - INFO - Truncating table workflow_temp.agent_rights
2025-05-12 15:41:52,244 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:41:52,249 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:41:52,251 - truncate_tables - INFO - Successfully truncated table workflow_temp.agent_rights
2025-05-12 15:41:52,251 - truncate_tables - INFO - Truncating table workflow_temp.attribute_enum_values
2025-05-12 15:41:52,257 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:41:52,264 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:41:52,268 - truncate_tables - INFO - Successfully truncated table workflow_temp.attribute_enum_values
2025-05-12 15:41:52,268 - truncate_tables - INFO - Truncating table workflow_temp.lo_nested_functions
2025-05-12 15:41:52,272 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:41:52,279 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:41:52,282 - truncate_tables - INFO - Successfully truncated table workflow_temp.lo_nested_functions
2025-05-12 15:41:52,282 - truncate_tables - INFO - Truncating table workflow_temp.dropdown_data_sources
2025-05-12 15:41:52,287 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:41:52,293 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:41:52,295 - truncate_tables - INFO - Successfully truncated table workflow_temp.dropdown_data_sources
2025-05-12 15:41:52,295 - truncate_tables - INFO - Truncating table workflow_temp.attribute_validations
2025-05-12 15:41:52,300 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:41:52,308 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:41:52,310 - truncate_tables - INFO - Successfully truncated table workflow_temp.attribute_validations
2025-05-12 15:41:52,310 - truncate_tables - INFO - Truncating table workflow_temp.execution_rules
2025-05-12 15:41:52,317 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:41:52,324 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:41:52,326 - truncate_tables - INFO - Successfully truncated table workflow_temp.execution_rules
2025-05-12 15:41:52,326 - truncate_tables - INFO - Truncating table workflow_temp.lo_system_functions
2025-05-12 15:41:52,332 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:41:52,340 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:41:52,342 - truncate_tables - INFO - Successfully truncated table workflow_temp.lo_system_functions
2025-05-12 15:41:52,342 - truncate_tables - INFO - Truncating table workflow_temp.terminal_pathways
2025-05-12 15:41:52,347 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:41:52,355 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:41:52,357 - truncate_tables - INFO - Successfully truncated table workflow_temp.terminal_pathways
2025-05-12 15:41:52,357 - truncate_tables - INFO - Truncating table workflow_temp.lo_data_mappings
2025-05-12 15:41:52,362 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:41:52,370 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:41:52,371 - truncate_tables - INFO - Successfully truncated table workflow_temp.lo_data_mappings
2025-05-12 15:41:52,371 - truncate_tables - INFO - Truncating table workflow_temp.medical_record
2025-05-12 15:41:52,377 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:41:52,384 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:41:52,386 - truncate_tables - INFO - Successfully truncated table workflow_temp.medical_record
2025-05-12 15:41:52,387 - truncate_tables - INFO - Truncating table workflow_temp.patient
2025-05-12 15:41:52,392 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:41:52,399 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:41:52,402 - truncate_tables - INFO - Successfully truncated table workflow_temp.patient
2025-05-12 15:41:52,402 - truncate_tables - INFO - Truncating table workflow_temp.tenants
2025-05-12 15:41:52,408 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:41:52,416 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:41:52,451 - truncate_tables - INFO - Successfully truncated table workflow_temp.tenants
2025-05-12 15:41:52,451 - truncate_tables - INFO - Truncating table workflow_temp.ui_stack
2025-05-12 15:41:52,457 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:41:52,463 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:41:52,489 - truncate_tables - INFO - Successfully truncated table workflow_temp.ui_stack
2025-05-12 15:41:52,489 - truncate_tables - INFO - Truncating table workflow_temp.lo_input_validations
2025-05-12 15:41:52,495 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:41:52,503 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:41:52,505 - truncate_tables - INFO - Successfully truncated table workflow_temp.lo_input_validations
2025-05-12 15:41:52,506 - truncate_tables - INFO - Truncating table workflow_temp.user_organizations
2025-05-12 15:41:52,512 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:41:52,519 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:41:52,521 - truncate_tables - INFO - Successfully truncated table workflow_temp.user_organizations
2025-05-12 15:41:52,521 - truncate_tables - INFO - Truncating table workflow_temp.global_objectives
2025-05-12 15:41:52,525 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:41:52,533 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:41:52,575 - truncate_tables - INFO - Successfully truncated table workflow_temp.global_objectives
2025-05-12 15:41:52,575 - truncate_tables - INFO - Truncating table workflow_temp.permission_capabilities
2025-05-12 15:41:52,579 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:41:52,586 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:41:52,588 - truncate_tables - INFO - Successfully truncated table workflow_temp.permission_capabilities
2025-05-12 15:41:52,588 - truncate_tables - INFO - Truncating table workflow_temp.user
2025-05-12 15:41:52,592 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:41:52,598 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:41:52,601 - truncate_tables - INFO - Successfully truncated table workflow_temp.user
2025-05-12 15:41:52,601 - truncate_tables - INFO - Truncating table workflow_temp.user_role
2025-05-12 15:41:52,604 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:41:52,611 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:41:52,614 - truncate_tables - INFO - Successfully truncated table workflow_temp.user_role
2025-05-12 15:41:52,614 - truncate_tables - INFO - Truncating table workflow_temp.role_inheritance
2025-05-12 15:41:52,618 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:41:52,624 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:41:52,626 - truncate_tables - INFO - Successfully truncated table workflow_temp.role_inheritance
2025-05-12 15:41:52,626 - truncate_tables - INFO - Truncating table workflow_temp.workflow_instances
2025-05-12 15:41:52,629 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:41:52,635 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:41:52,640 - truncate_tables - INFO - Successfully truncated table workflow_temp.workflow_instances
2025-05-12 15:41:52,641 - truncate_tables - INFO - Truncating table workflow_temp.calculated_fields
2025-05-12 15:41:52,644 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:41:52,651 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:41:52,653 - truncate_tables - INFO - Successfully truncated table workflow_temp.calculated_fields
2025-05-12 15:41:52,653 - truncate_tables - INFO - Truncating table workflow_temp.data_mappings
2025-05-12 15:41:52,657 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:41:52,664 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:41:52,666 - truncate_tables - INFO - Successfully truncated table workflow_temp.data_mappings
2025-05-12 15:41:52,666 - truncate_tables - INFO - Truncating table workflow_temp.entities
2025-05-12 15:41:52,669 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:41:52,676 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:41:52,689 - truncate_tables - INFO - Successfully truncated table workflow_temp.entities
2025-05-12 15:41:52,689 - truncate_tables - INFO - Truncating table workflow_temp.entity_lifecycle_management
2025-05-12 15:41:52,692 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:41:52,699 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:41:52,701 - truncate_tables - INFO - Successfully truncated table workflow_temp.entity_lifecycle_management
2025-05-12 15:41:52,701 - truncate_tables - INFO - Truncating table workflow_temp.execution_pathway_conditions
2025-05-12 15:41:52,705 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:41:52,712 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:41:52,714 - truncate_tables - INFO - Successfully truncated table workflow_temp.execution_pathway_conditions
2025-05-12 15:41:52,714 - truncate_tables - INFO - Truncating table workflow_temp.execution_pathways
2025-05-12 15:41:52,717 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:41:52,724 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:41:52,725 - truncate_tables - INFO - Successfully truncated table workflow_temp.execution_pathways
2025-05-12 15:41:52,725 - truncate_tables - INFO - Truncating table workflow_temp.mapping_rules
2025-05-12 15:41:52,729 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:41:52,735 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:41:52,737 - truncate_tables - INFO - Successfully truncated table workflow_temp.mapping_rules
2025-05-12 15:41:52,737 - truncate_tables - INFO - Truncating table workflow_temp.output_triggers
2025-05-12 15:41:52,741 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:41:52,747 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:41:52,749 - truncate_tables - INFO - Successfully truncated table workflow_temp.output_triggers
2025-05-12 15:41:52,749 - truncate_tables - INFO - Truncating table workflow_temp.entity_attributes
2025-05-12 15:41:52,753 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:41:52,761 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:41:52,771 - truncate_tables - INFO - Successfully truncated table workflow_temp.entity_attributes
2025-05-12 15:41:52,771 - truncate_tables - INFO - Truncating table workflow_temp.go_lo_mapping
2025-05-12 15:41:52,775 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:41:52,781 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:41:52,783 - truncate_tables - INFO - Successfully truncated table workflow_temp.go_lo_mapping
2025-05-12 15:41:52,783 - truncate_tables - INFO - Truncating table workflow_temp.lo_output_execution
2025-05-12 15:41:52,787 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:41:52,794 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:41:52,797 - truncate_tables - INFO - Successfully truncated table workflow_temp.lo_output_execution
2025-05-12 15:41:52,797 - truncate_tables - INFO - Truncating table workflow_temp.metrics_aggregation
2025-05-12 15:41:52,800 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:41:52,807 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:41:52,809 - truncate_tables - INFO - Successfully truncated table workflow_temp.metrics_aggregation
2025-05-12 15:41:52,809 - truncate_tables - INFO - Truncating table workflow_temp.metrics_reporting
2025-05-12 15:41:52,813 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:41:52,819 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:41:52,821 - truncate_tables - INFO - Successfully truncated table workflow_temp.metrics_reporting
2025-05-12 15:41:52,821 - truncate_tables - INFO - Truncating table workflow_temp.user_roles
2025-05-12 15:41:52,824 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:41:52,831 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:41:52,834 - truncate_tables - INFO - Successfully truncated table workflow_temp.user_roles
2025-05-12 15:41:52,834 - truncate_tables - INFO - Truncating table workflow_temp.local_objectives
2025-05-12 15:41:52,837 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:41:52,844 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:41:52,872 - truncate_tables - INFO - Successfully truncated table workflow_temp.local_objectives
2025-05-12 15:41:52,872 - truncate_tables - INFO - Truncating table workflow_temp.input_data_sources
2025-05-12 15:41:52,876 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:41:52,882 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:41:52,900 - truncate_tables - INFO - Successfully truncated table workflow_temp.input_data_sources
2025-05-12 15:41:52,900 - truncate_tables - INFO - Truncating table workflow_temp.input_dependencies
2025-05-12 15:41:52,904 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:41:52,912 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:41:52,915 - truncate_tables - INFO - Successfully truncated table workflow_temp.input_dependencies
2025-05-12 15:41:52,915 - truncate_tables - INFO - Truncating table workflow_temp.entity_attribute_metadata
2025-05-12 15:41:52,919 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:41:52,926 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:41:52,929 - truncate_tables - INFO - Successfully truncated table workflow_temp.entity_attribute_metadata
2025-05-12 15:41:52,929 - truncate_tables - INFO - Truncating table workflow_temp.system_functions
2025-05-12 15:41:52,933 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:41:52,940 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:41:52,943 - truncate_tables - INFO - Successfully truncated table workflow_temp.system_functions
2025-05-12 15:41:52,943 - truncate_tables - INFO - Truncating table workflow_temp.permission_contexts
2025-05-12 15:41:52,947 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:41:52,953 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:41:52,956 - truncate_tables - INFO - Successfully truncated table workflow_temp.permission_contexts
2025-05-12 15:41:52,956 - truncate_tables - INFO - Truncating table workflow_temp.workflow_results
2025-05-12 15:41:52,960 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:41:52,965 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:41:52,968 - truncate_tables - INFO - Successfully truncated table workflow_temp.workflow_results
2025-05-12 15:41:52,968 - truncate_tables - INFO - Truncating table workflow_temp.users
2025-05-12 15:41:52,971 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:41:52,976 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:41:52,982 - truncate_tables - INFO - Successfully truncated table workflow_temp.users
2025-05-12 15:41:52,982 - truncate_tables - INFO - Truncating table workflow_temp.user_oauth_tokens
2025-05-12 15:41:52,987 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:41:52,993 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:41:52,996 - truncate_tables - INFO - Successfully truncated table workflow_temp.user_oauth_tokens
2025-05-12 15:41:52,996 - truncate_tables - INFO - Truncating table workflow_temp.runtime_metrics_stack
2025-05-12 15:41:53,001 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:41:53,006 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:41:53,011 - truncate_tables - INFO - Successfully truncated table workflow_temp.runtime_metrics_stack
2025-05-12 15:41:53,011 - truncate_tables - INFO - Truncating table workflow_temp.workflow_transaction
2025-05-12 15:41:53,015 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:41:53,020 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:41:53,023 - truncate_tables - INFO - Successfully truncated table workflow_temp.workflow_transaction
2025-05-12 15:41:53,023 - truncate_tables - INFO - Truncating table workflow_temp.role_permissions
2025-05-12 15:41:53,026 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:41:53,032 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:41:53,033 - truncate_tables - INFO - Successfully truncated table workflow_temp.role_permissions
2025-05-12 15:41:53,033 - truncate_tables - INFO - Truncating table workflow_temp.user_sessions
2025-05-12 15:41:53,037 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:41:53,043 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:41:53,045 - truncate_tables - INFO - Successfully truncated table workflow_temp.user_sessions
2025-05-12 15:41:53,045 - truncate_tables - INFO - Truncating table workflow_temp.input_items
2025-05-12 15:41:53,049 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:41:53,056 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:41:53,058 - truncate_tables - INFO - Successfully truncated table workflow_temp.input_items
2025-05-12 15:41:53,058 - truncate_tables - INFO - Truncating table workflow_temp.entity_relationships
2025-05-12 15:41:53,062 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:41:53,070 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:41:53,072 - truncate_tables - INFO - Successfully truncated table workflow_temp.entity_relationships
2025-05-12 15:41:53,072 - truncate_tables - INFO - Truncating table workflow_temp.lo_output_triggers
2025-05-12 15:41:53,076 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:41:53,082 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:41:53,084 - truncate_tables - INFO - Successfully truncated table workflow_temp.lo_output_triggers
2025-05-12 15:41:53,084 - truncate_tables - INFO - Truncating table workflow_temp.ui_elements
2025-05-12 15:41:53,088 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:41:53,096 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:41:53,099 - truncate_tables - INFO - Successfully truncated table workflow_temp.ui_elements
2025-05-12 15:41:53,099 - truncate_tables - INFO - Successfully truncated all tables in schema workflow_temp
2025-05-12 15:43:42,939 - truncate_tables - INFO - Truncating all tables in schema workflow_temp
2025-05-12 15:43:42,944 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:43:42,947 - truncate_tables - INFO - Truncating table workflow_temp.alembic_version
2025-05-12 15:43:42,952 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:43:42,958 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:43:42,960 - truncate_tables - INFO - Successfully truncated table workflow_temp.alembic_version
2025-05-12 15:43:42,960 - truncate_tables - INFO - Truncating table workflow_temp.e1_employee
2025-05-12 15:43:42,965 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:43:42,972 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:43:42,974 - truncate_tables - INFO - Successfully truncated table workflow_temp.e1_employee
2025-05-12 15:43:42,974 - truncate_tables - INFO - Truncating table workflow_temp.entity_permissions
2025-05-12 15:43:42,979 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:43:42,986 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:43:42,988 - truncate_tables - INFO - Successfully truncated table workflow_temp.entity_permissions
2025-05-12 15:43:42,988 - truncate_tables - INFO - Truncating table workflow_temp.agent_rights
2025-05-12 15:43:42,992 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:43:42,999 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:43:43,001 - truncate_tables - INFO - Successfully truncated table workflow_temp.agent_rights
2025-05-12 15:43:43,001 - truncate_tables - INFO - Truncating table workflow_temp.e2_department
2025-05-12 15:43:43,005 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:43:43,011 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:43:43,014 - truncate_tables - INFO - Successfully truncated table workflow_temp.e2_department
2025-05-12 15:43:43,014 - truncate_tables - INFO - Truncating table workflow_temp.objective_permissions
2025-05-12 15:43:43,017 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:43:43,023 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:43:43,025 - truncate_tables - INFO - Successfully truncated table workflow_temp.objective_permissions
2025-05-12 15:43:43,025 - truncate_tables - INFO - Truncating table workflow_temp.input_stack
2025-05-12 15:43:43,029 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:43:43,034 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:43:43,038 - truncate_tables - INFO - Successfully truncated table workflow_temp.input_stack
2025-05-12 15:43:43,038 - truncate_tables - INFO - Truncating table workflow_temp.lo_input_items
2025-05-12 15:43:43,041 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:43:43,047 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:43:43,053 - truncate_tables - INFO - Successfully truncated table workflow_temp.lo_input_items
2025-05-12 15:43:43,053 - truncate_tables - INFO - Truncating table workflow_temp.lab_report
2025-05-12 15:43:43,058 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:43:43,065 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:43:43,067 - truncate_tables - INFO - Successfully truncated table workflow_temp.lab_report
2025-05-12 15:43:43,067 - truncate_tables - INFO - Truncating table workflow_temp.permission_types
2025-05-12 15:43:43,073 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:43:43,080 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:43:43,085 - truncate_tables - INFO - Successfully truncated table workflow_temp.permission_types
2025-05-12 15:43:43,085 - truncate_tables - INFO - Truncating table workflow_temp.lo_output_stack
2025-05-12 15:43:43,090 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:43:43,096 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:43:43,100 - truncate_tables - INFO - Successfully truncated table workflow_temp.lo_output_stack
2025-05-12 15:43:43,100 - truncate_tables - INFO - Truncating table workflow_temp.attribute_ui_controls
2025-05-12 15:43:43,105 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:43:43,111 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:43:43,113 - truncate_tables - INFO - Successfully truncated table workflow_temp.attribute_ui_controls
2025-05-12 15:43:43,113 - truncate_tables - INFO - Truncating table workflow_temp.organizational_units
2025-05-12 15:43:43,117 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:43:43,122 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:43:43,124 - truncate_tables - INFO - Successfully truncated table workflow_temp.organizational_units
2025-05-12 15:43:43,124 - truncate_tables - INFO - Truncating table workflow_temp.lo_output_items
2025-05-12 15:43:43,127 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:43:43,133 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:43:43,135 - truncate_tables - INFO - Successfully truncated table workflow_temp.lo_output_items
2025-05-12 15:43:43,135 - truncate_tables - INFO - Truncating table workflow_temp.agent_stack
2025-05-12 15:43:43,140 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:43:43,146 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:43:43,149 - truncate_tables - INFO - Successfully truncated table workflow_temp.agent_stack
2025-05-12 15:43:43,149 - truncate_tables - INFO - Truncating table workflow_temp.conditional_success_messages
2025-05-12 15:43:43,152 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:43:43,159 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:43:43,161 - truncate_tables - INFO - Successfully truncated table workflow_temp.conditional_success_messages
2025-05-12 15:43:43,161 - truncate_tables - INFO - Truncating table workflow_temp.prescription
2025-05-12 15:43:43,167 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:43:43,173 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:43:43,175 - truncate_tables - INFO - Successfully truncated table workflow_temp.prescription
2025-05-12 15:43:43,175 - truncate_tables - INFO - Truncating table workflow_temp.lo_data_mapping_stack
2025-05-12 15:43:43,180 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:43:43,188 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:43:43,191 - truncate_tables - INFO - Successfully truncated table workflow_temp.lo_data_mapping_stack
2025-05-12 15:43:43,191 - truncate_tables - INFO - Truncating table workflow_temp.execution_path_tracking
2025-05-12 15:43:43,197 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:43:43,202 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:43:43,204 - truncate_tables - INFO - Successfully truncated table workflow_temp.execution_path_tracking
2025-05-12 15:43:43,204 - truncate_tables - INFO - Truncating table workflow_temp.entity_business_rules
2025-05-12 15:43:43,209 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:43:43,215 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:43:43,218 - truncate_tables - INFO - Successfully truncated table workflow_temp.entity_business_rules
2025-05-12 15:43:43,218 - truncate_tables - INFO - Truncating table workflow_temp.lo_input_stack
2025-05-12 15:43:43,223 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:43:43,231 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:43:43,238 - truncate_tables - INFO - Successfully truncated table workflow_temp.lo_input_stack
2025-05-12 15:43:43,238 - truncate_tables - INFO - Truncating table workflow_temp.lo_dependencies
2025-05-12 15:43:43,244 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:43:43,251 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:43:43,254 - truncate_tables - INFO - Successfully truncated table workflow_temp.lo_dependencies
2025-05-12 15:43:43,255 - truncate_tables - INFO - Truncating table workflow_temp.output_stack
2025-05-12 15:43:43,260 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:43:43,267 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:43:43,271 - truncate_tables - INFO - Successfully truncated table workflow_temp.output_stack
2025-05-12 15:43:43,271 - truncate_tables - INFO - Truncating table workflow_temp.roles
2025-05-12 15:43:43,276 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:43:43,282 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:43:43,287 - truncate_tables - INFO - Successfully truncated table workflow_temp.roles
2025-05-12 15:43:43,287 - truncate_tables - INFO - Truncating table workflow_temp.data_mapping_stack
2025-05-12 15:43:43,292 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:43:43,300 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:43:43,304 - truncate_tables - INFO - Successfully truncated table workflow_temp.data_mapping_stack
2025-05-12 15:43:43,304 - truncate_tables - INFO - Truncating table workflow_temp.lo_input_execution
2025-05-12 15:43:43,308 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:43:43,315 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:43:43,317 - truncate_tables - INFO - Successfully truncated table workflow_temp.lo_input_execution
2025-05-12 15:43:43,317 - truncate_tables - INFO - Truncating table workflow_temp.role
2025-05-12 15:43:43,323 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:43:43,329 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:43:43,331 - truncate_tables - INFO - Successfully truncated table workflow_temp.role
2025-05-12 15:43:43,332 - truncate_tables - INFO - Truncating table workflow_temp.go_performance_metrics
2025-05-12 15:43:43,336 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:43:43,342 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:43:43,344 - truncate_tables - INFO - Successfully truncated table workflow_temp.go_performance_metrics
2025-05-12 15:43:43,344 - truncate_tables - INFO - Truncating table workflow_temp.output_items
2025-05-12 15:43:43,349 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:43:43,354 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:43:43,356 - truncate_tables - INFO - Successfully truncated table workflow_temp.output_items
2025-05-12 15:43:43,356 - truncate_tables - INFO - Truncating table workflow_temp.success_messages
2025-05-12 15:43:43,361 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:43:43,369 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:43:43,372 - truncate_tables - INFO - Successfully truncated table workflow_temp.success_messages
2025-05-12 15:43:43,372 - truncate_tables - INFO - Truncating table workflow_temp.attribute_enum_values
2025-05-12 15:43:43,377 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:43:43,385 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:43:43,387 - truncate_tables - INFO - Successfully truncated table workflow_temp.attribute_enum_values
2025-05-12 15:43:43,388 - truncate_tables - INFO - Truncating table workflow_temp.lo_nested_functions
2025-05-12 15:43:43,393 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:43:43,399 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:43:43,402 - truncate_tables - INFO - Successfully truncated table workflow_temp.lo_nested_functions
2025-05-12 15:43:43,402 - truncate_tables - INFO - Truncating table workflow_temp.dropdown_data_sources
2025-05-12 15:43:43,407 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:43:43,415 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:43:43,417 - truncate_tables - INFO - Successfully truncated table workflow_temp.dropdown_data_sources
2025-05-12 15:43:43,417 - truncate_tables - INFO - Truncating table workflow_temp.attribute_validations
2025-05-12 15:43:43,421 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:43:43,429 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:43:43,431 - truncate_tables - INFO - Successfully truncated table workflow_temp.attribute_validations
2025-05-12 15:43:43,431 - truncate_tables - INFO - Truncating table workflow_temp.execution_rules
2025-05-12 15:43:43,437 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:43:43,444 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:43:43,446 - truncate_tables - INFO - Successfully truncated table workflow_temp.execution_rules
2025-05-12 15:43:43,446 - truncate_tables - INFO - Truncating table workflow_temp.lo_system_functions
2025-05-12 15:43:43,452 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:43:43,459 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:43:43,462 - truncate_tables - INFO - Successfully truncated table workflow_temp.lo_system_functions
2025-05-12 15:43:43,462 - truncate_tables - INFO - Truncating table workflow_temp.terminal_pathways
2025-05-12 15:43:43,468 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:43:43,476 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:43:43,479 - truncate_tables - INFO - Successfully truncated table workflow_temp.terminal_pathways
2025-05-12 15:43:43,479 - truncate_tables - INFO - Truncating table workflow_temp.lo_data_mappings
2025-05-12 15:43:43,484 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:43:43,491 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:43:43,493 - truncate_tables - INFO - Successfully truncated table workflow_temp.lo_data_mappings
2025-05-12 15:43:43,493 - truncate_tables - INFO - Truncating table workflow_temp.medical_record
2025-05-12 15:43:43,499 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:43:43,506 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:43:43,509 - truncate_tables - INFO - Successfully truncated table workflow_temp.medical_record
2025-05-12 15:43:43,509 - truncate_tables - INFO - Truncating table workflow_temp.patient
2025-05-12 15:43:43,514 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:43:43,521 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:43:43,523 - truncate_tables - INFO - Successfully truncated table workflow_temp.patient
2025-05-12 15:43:43,523 - truncate_tables - INFO - Truncating table workflow_temp.tenants
2025-05-12 15:43:43,529 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:43:43,537 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:43:43,574 - truncate_tables - INFO - Successfully truncated table workflow_temp.tenants
2025-05-12 15:43:43,574 - truncate_tables - INFO - Truncating table workflow_temp.ui_stack
2025-05-12 15:43:43,581 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:43:43,590 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:43:43,594 - truncate_tables - INFO - Successfully truncated table workflow_temp.ui_stack
2025-05-12 15:43:43,594 - truncate_tables - INFO - Truncating table workflow_temp.lo_input_validations
2025-05-12 15:43:43,600 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:43:43,608 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:43:43,610 - truncate_tables - INFO - Successfully truncated table workflow_temp.lo_input_validations
2025-05-12 15:43:43,611 - truncate_tables - INFO - Truncating table workflow_temp.user_organizations
2025-05-12 15:43:43,615 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:43:43,622 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:43:43,624 - truncate_tables - INFO - Successfully truncated table workflow_temp.user_organizations
2025-05-12 15:43:43,624 - truncate_tables - INFO - Truncating table workflow_temp.global_objectives
2025-05-12 15:43:43,628 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:43:43,635 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:43:43,670 - truncate_tables - INFO - Successfully truncated table workflow_temp.global_objectives
2025-05-12 15:43:43,670 - truncate_tables - INFO - Truncating table workflow_temp.permission_capabilities
2025-05-12 15:43:43,675 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:43:43,682 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:43:43,684 - truncate_tables - INFO - Successfully truncated table workflow_temp.permission_capabilities
2025-05-12 15:43:43,684 - truncate_tables - INFO - Truncating table workflow_temp.user
2025-05-12 15:43:43,689 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:43:43,697 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:43:43,699 - truncate_tables - INFO - Successfully truncated table workflow_temp.user
2025-05-12 15:43:43,699 - truncate_tables - INFO - Truncating table workflow_temp.user_role
2025-05-12 15:43:43,704 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:43:43,711 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:43:43,713 - truncate_tables - INFO - Successfully truncated table workflow_temp.user_role
2025-05-12 15:43:43,713 - truncate_tables - INFO - Truncating table workflow_temp.role_inheritance
2025-05-12 15:43:43,718 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:43:43,725 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:43:43,727 - truncate_tables - INFO - Successfully truncated table workflow_temp.role_inheritance
2025-05-12 15:43:43,727 - truncate_tables - INFO - Truncating table workflow_temp.workflow_instances
2025-05-12 15:43:43,732 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:43:43,738 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:43:43,744 - truncate_tables - INFO - Successfully truncated table workflow_temp.workflow_instances
2025-05-12 15:43:43,744 - truncate_tables - INFO - Truncating table workflow_temp.calculated_fields
2025-05-12 15:43:43,749 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:43:43,756 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:43:43,758 - truncate_tables - INFO - Successfully truncated table workflow_temp.calculated_fields
2025-05-12 15:43:43,759 - truncate_tables - INFO - Truncating table workflow_temp.data_mappings
2025-05-12 15:43:43,763 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:43:43,770 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:43:43,772 - truncate_tables - INFO - Successfully truncated table workflow_temp.data_mappings
2025-05-12 15:43:43,772 - truncate_tables - INFO - Truncating table workflow_temp.entities
2025-05-12 15:43:43,778 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:43:43,785 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:43:43,798 - truncate_tables - INFO - Successfully truncated table workflow_temp.entities
2025-05-12 15:43:43,798 - truncate_tables - INFO - Truncating table workflow_temp.entity_lifecycle_management
2025-05-12 15:43:43,803 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:43:43,811 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:43:43,815 - truncate_tables - INFO - Successfully truncated table workflow_temp.entity_lifecycle_management
2025-05-12 15:43:43,815 - truncate_tables - INFO - Truncating table workflow_temp.execution_pathway_conditions
2025-05-12 15:43:43,819 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:43:43,825 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:43:43,827 - truncate_tables - INFO - Successfully truncated table workflow_temp.execution_pathway_conditions
2025-05-12 15:43:43,827 - truncate_tables - INFO - Truncating table workflow_temp.execution_pathways
2025-05-12 15:43:43,832 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:43:43,841 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:43:43,844 - truncate_tables - INFO - Successfully truncated table workflow_temp.execution_pathways
2025-05-12 15:43:43,844 - truncate_tables - INFO - Truncating table workflow_temp.mapping_rules
2025-05-12 15:43:43,848 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:43:43,854 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:43:43,857 - truncate_tables - INFO - Successfully truncated table workflow_temp.mapping_rules
2025-05-12 15:43:43,857 - truncate_tables - INFO - Truncating table workflow_temp.output_triggers
2025-05-12 15:43:43,862 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:43:43,870 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:43:43,872 - truncate_tables - INFO - Successfully truncated table workflow_temp.output_triggers
2025-05-12 15:43:43,872 - truncate_tables - INFO - Truncating table workflow_temp.entity_attributes
2025-05-12 15:43:43,878 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:43:43,886 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:43:43,895 - truncate_tables - INFO - Successfully truncated table workflow_temp.entity_attributes
2025-05-12 15:43:43,895 - truncate_tables - INFO - Truncating table workflow_temp.go_lo_mapping
2025-05-12 15:43:43,899 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:43:43,907 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:43:43,908 - truncate_tables - INFO - Successfully truncated table workflow_temp.go_lo_mapping
2025-05-12 15:43:43,908 - truncate_tables - INFO - Truncating table workflow_temp.lo_output_execution
2025-05-12 15:43:43,912 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:43:43,919 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:43:43,921 - truncate_tables - INFO - Successfully truncated table workflow_temp.lo_output_execution
2025-05-12 15:43:43,921 - truncate_tables - INFO - Truncating table workflow_temp.metrics_aggregation
2025-05-12 15:43:43,925 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:43:43,931 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:43:43,933 - truncate_tables - INFO - Successfully truncated table workflow_temp.metrics_aggregation
2025-05-12 15:43:43,933 - truncate_tables - INFO - Truncating table workflow_temp.metrics_reporting
2025-05-12 15:43:43,936 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:43:43,942 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:43:43,943 - truncate_tables - INFO - Successfully truncated table workflow_temp.metrics_reporting
2025-05-12 15:43:43,943 - truncate_tables - INFO - Truncating table workflow_temp.user_roles
2025-05-12 15:43:43,947 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:43:43,952 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:43:43,953 - truncate_tables - INFO - Successfully truncated table workflow_temp.user_roles
2025-05-12 15:43:43,953 - truncate_tables - INFO - Truncating table workflow_temp.local_objectives
2025-05-12 15:43:43,956 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:43:43,961 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:43:43,985 - truncate_tables - INFO - Successfully truncated table workflow_temp.local_objectives
2025-05-12 15:43:43,985 - truncate_tables - INFO - Truncating table workflow_temp.input_data_sources
2025-05-12 15:43:43,990 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:43:43,996 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:43:43,998 - truncate_tables - INFO - Successfully truncated table workflow_temp.input_data_sources
2025-05-12 15:43:43,998 - truncate_tables - INFO - Truncating table workflow_temp.input_dependencies
2025-05-12 15:43:44,002 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:43:44,010 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:43:44,012 - truncate_tables - INFO - Successfully truncated table workflow_temp.input_dependencies
2025-05-12 15:43:44,013 - truncate_tables - INFO - Truncating table workflow_temp.entity_attribute_metadata
2025-05-12 15:43:44,018 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:43:44,025 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:43:44,027 - truncate_tables - INFO - Successfully truncated table workflow_temp.entity_attribute_metadata
2025-05-12 15:43:44,027 - truncate_tables - INFO - Truncating table workflow_temp.system_functions
2025-05-12 15:43:44,032 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:43:44,039 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:43:44,041 - truncate_tables - INFO - Successfully truncated table workflow_temp.system_functions
2025-05-12 15:43:44,041 - truncate_tables - INFO - Truncating table workflow_temp.permission_contexts
2025-05-12 15:43:44,046 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:43:44,053 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:43:44,056 - truncate_tables - INFO - Successfully truncated table workflow_temp.permission_contexts
2025-05-12 15:43:44,056 - truncate_tables - INFO - Truncating table workflow_temp.workflow_results
2025-05-12 15:43:44,061 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:43:44,068 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:43:44,071 - truncate_tables - INFO - Successfully truncated table workflow_temp.workflow_results
2025-05-12 15:43:44,071 - truncate_tables - INFO - Truncating table workflow_temp.users
2025-05-12 15:43:44,076 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:43:44,082 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:43:44,088 - truncate_tables - INFO - Successfully truncated table workflow_temp.users
2025-05-12 15:43:44,088 - truncate_tables - INFO - Truncating table workflow_temp.user_oauth_tokens
2025-05-12 15:43:44,093 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:43:44,100 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:43:44,102 - truncate_tables - INFO - Successfully truncated table workflow_temp.user_oauth_tokens
2025-05-12 15:43:44,102 - truncate_tables - INFO - Truncating table workflow_temp.runtime_metrics_stack
2025-05-12 15:43:44,107 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:43:44,114 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:43:44,117 - truncate_tables - INFO - Successfully truncated table workflow_temp.runtime_metrics_stack
2025-05-12 15:43:44,117 - truncate_tables - INFO - Truncating table workflow_temp.workflow_transaction
2025-05-12 15:43:44,122 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:43:44,129 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:43:44,131 - truncate_tables - INFO - Successfully truncated table workflow_temp.workflow_transaction
2025-05-12 15:43:44,131 - truncate_tables - INFO - Truncating table workflow_temp.role_permissions
2025-05-12 15:43:44,136 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:43:44,143 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:43:44,145 - truncate_tables - INFO - Successfully truncated table workflow_temp.role_permissions
2025-05-12 15:43:44,145 - truncate_tables - INFO - Truncating table workflow_temp.user_sessions
2025-05-12 15:43:44,150 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:43:44,157 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:43:44,159 - truncate_tables - INFO - Successfully truncated table workflow_temp.user_sessions
2025-05-12 15:43:44,159 - truncate_tables - INFO - Truncating table workflow_temp.input_items
2025-05-12 15:43:44,164 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:43:44,170 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:43:44,173 - truncate_tables - INFO - Successfully truncated table workflow_temp.input_items
2025-05-12 15:43:44,173 - truncate_tables - INFO - Truncating table workflow_temp.entity_relationships
2025-05-12 15:43:44,178 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:43:44,184 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:43:44,187 - truncate_tables - INFO - Successfully truncated table workflow_temp.entity_relationships
2025-05-12 15:43:44,187 - truncate_tables - INFO - Truncating table workflow_temp.lo_output_triggers
2025-05-12 15:43:44,192 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:43:44,198 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:43:44,200 - truncate_tables - INFO - Successfully truncated table workflow_temp.lo_output_triggers
2025-05-12 15:43:44,200 - truncate_tables - INFO - Truncating table workflow_temp.ui_elements
2025-05-12 15:43:44,205 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:43:44,211 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:43:44,214 - truncate_tables - INFO - Successfully truncated table workflow_temp.ui_elements
2025-05-12 15:43:44,214 - truncate_tables - INFO - Successfully truncated all tables in schema workflow_temp
