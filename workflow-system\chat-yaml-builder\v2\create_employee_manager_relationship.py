#!/usr/bin/env python3
"""
Create a self-referential relationship between <PERSON><PERSON>loyee and Em<PERSON>loyee (manager) with relationship properties.
"""

import os
import psycopg2
import logging

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger('create_manager_relationship')

def get_db_connection(schema_name=None):
    """
    Get a database connection.
    
    Args:
        schema_name: Optional schema name to set as the search path
        
    Returns:
        Database connection object
    """
    # Get database connection parameters from environment variables or use defaults
    db_host = os.environ.get('DB_HOST', '**********')
    db_port = os.environ.get('DB_PORT', '5432')
    db_name = os.environ.get('DB_NAME', 'workflow_system')
    db_user = os.environ.get('DB_USER', 'postgres')
    db_password = os.environ.get('DB_PASSWORD', 'workflow_postgres_secure_password')
    
    # Connect to the database
    conn = psycopg2.connect(
        host=db_host,
        port=db_port,
        dbname=db_name,
        user=db_user,
        password=db_password
    )
    
    # Set schema search path if provided
    if schema_name:
        with conn.cursor() as cursor:
            cursor.execute(f"SET search_path TO {schema_name}")
            conn.commit()
    
    return conn

def execute_query(query, params=None, schema_name=None):
    """
    Execute a database query.
    
    Args:
        query: SQL query to execute
        params: Query parameters
        schema_name: Schema name to set as search path
        
    Returns:
        Query result rows
    """
    conn = None
    try:
        # Get database connection
        conn = get_db_connection(schema_name)
        
        # Execute query
        with conn.cursor() as cursor:
            cursor.execute(query, params)
            
            # Get result if query returns rows
            if cursor.description:
                result = cursor.fetchall()
                return True, result
            
            # Commit if not a SELECT query
            conn.commit()
            return True, []
    except Exception as e:
        if conn:
            conn.rollback()
        logger.error(f"Query error: {str(e)}")
        return False, str(e)
    finally:
        if conn:
            conn.close()

def create_employee_manager_relationship():
    """
    Create a self-referential relationship between Employee and Employee (manager) with relationship properties.
    """
    schema_name = 'workflow_temp'
    
    # Get Employee entity ID
    success, result = execute_query(
        f"SELECT entity_id FROM {schema_name}.entities WHERE name = 'Employee'",
        schema_name=schema_name
    )
    
    if not success or not result:
        logger.error("Employee entity not found in the database")
        return
    
    employee_id = result[0][0]
    logger.info(f"Found Employee entity with ID: {employee_id}")
    
    # Check if managerId attribute exists for Employee
    success, result = execute_query(
        f"""
        SELECT attribute_id FROM {schema_name}.entity_attributes 
        WHERE entity_id = %s AND name = 'managerId'
        """,
        (employee_id,),
        schema_name
    )
    
    manager_attr_id = None
    if not success or not result:
        logger.info("Creating managerId attribute for Employee...")
        
        # Create managerId attribute
        query = f"""
            INSERT INTO {schema_name}.entity_attributes (
                attribute_id, entity_id, name, display_name, datatype, type, version, status, 
                required, created_at, updated_at, created_by, updated_by
            ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, NOW(), NOW(), %s, %s)
            RETURNING attribute_id
        """
        
        manager_attr_id = f"{employee_id}.At2"  # Assuming this is the next available ID
        
        success, result = execute_query(
            query,
            (
                manager_attr_id, 
                employee_id, 
                "managerId", 
                "Manager ID", 
                "string",  # datatype
                "string",  # type
                "1.0", 
                "Active", 
                False,  # Not required (nullable)
                "system", 
                "system"
            ),
            schema_name
        )
        
        if not success:
            logger.error(f"Failed to create managerId attribute for Employee")
            return
        
        logger.info(f"Created managerId attribute for Employee with ID: {manager_attr_id}")
    else:
        manager_attr_id = result[0][0]
        logger.info(f"Found managerId attribute for Employee with ID: {manager_attr_id}")
    
    # Check if employeeId attribute exists for Employee
    success, result = execute_query(
        f"""
        SELECT attribute_id FROM {schema_name}.entity_attributes 
        WHERE entity_id = %s AND name = 'employeeId'
        """,
        (employee_id,),
        schema_name
    )
    
    employee_attr_id = None
    if not success or not result:
        logger.info("Creating employeeId attribute for Employee...")
        
        # Create employeeId attribute
        query = f"""
            INSERT INTO {schema_name}.entity_attributes (
                attribute_id, entity_id, name, display_name, datatype, type, version, status, 
                required, created_at, updated_at, created_by, updated_by
            ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, NOW(), NOW(), %s, %s)
            RETURNING attribute_id
        """
        
        employee_attr_id = f"{employee_id}.At3"  # Assuming this is the next available ID
        
        success, result = execute_query(
            query,
            (
                employee_attr_id, 
                employee_id, 
                "employeeId", 
                "Employee ID", 
                "string",  # datatype
                "string",  # type
                "1.0", 
                "Active", 
                True,  # Required/Primary key 
                "system", 
                "system"
            ),
            schema_name
        )
        
        if not success:
            logger.error(f"Failed to create employeeId attribute for Employee")
            return
        
        logger.info(f"Created employeeId attribute for Employee with ID: {employee_attr_id}")
    else:
        employee_attr_id = result[0][0]
        logger.info(f"Found employeeId attribute for Employee with ID: {employee_attr_id}")
    
    # Check if the relationship already exists
    success, result = execute_query(
        f"""
        SELECT id FROM {schema_name}.entity_relationships 
        WHERE source_entity_id = %s AND target_entity_id = %s AND 
              source_attribute_id = %s AND target_attribute_id = %s
        """,
        (employee_id, employee_id, manager_attr_id, employee_attr_id),
        schema_name
    )
    
    if success and result:
        # Relationship already exists, update it
        relationship_id = result[0][0]
        logger.info(f"Relationship already exists with ID: {relationship_id}, updating it...")
        
        query = f"""
            UPDATE {schema_name}.entity_relationships
            SET relationship_type = %s,
                on_delete = %s,
                on_update = %s,
                foreign_key_type = %s,
                updated_at = NOW()
            WHERE id = %s
        """
        
        success, result = execute_query(
            query,
            (
                "many-to-one",
                "RESTRICT",
                "CASCADE",
                "NULLABLE",  # Manager can be NULL (an employee might not have a manager)
                relationship_id
            ),
            schema_name
        )
        
        if not success:
            logger.error(f"Failed to update relationship: {result}")
            return
        
        logger.info(f"Updated relationship with ID: {relationship_id}")
    else:
        # Relationship doesn't exist, create it
        logger.info("Creating new relationship...")
        
        query = f"""
            INSERT INTO {schema_name}.entity_relationships (
                source_entity_id, target_entity_id, relationship_type, 
                source_attribute_id, target_attribute_id,
                on_delete, on_update, foreign_key_type,
                created_at, updated_at
            ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, NOW(), NOW())
            RETURNING id
        """
        
        success, result = execute_query(
            query,
            (
                employee_id,
                employee_id,
                "many-to-one",
                manager_attr_id,
                employee_attr_id,
                "RESTRICT",
                "CASCADE",
                "NULLABLE"  # Manager can be NULL (an employee might not have a manager)
            ),
            schema_name
        )
        
        if not success:
            logger.error(f"Failed to create relationship: {result}")
            return
        
        logger.info(f"Created relationship with ID: {result[0][0] if result else 'unknown'}")
    
    # Verify the relationship exists
    success, result = execute_query(
        f"""
        SELECT 
            er.id, 
            s.name as source_entity, 
            t.name as target_entity, 
            er.relationship_type, 
            sa.name as source_attribute, 
            ta.name as target_attribute,
            er.on_delete,
            er.on_update,
            er.foreign_key_type
        FROM 
            {schema_name}.entity_relationships er
            JOIN {schema_name}.entities s ON er.source_entity_id = s.entity_id
            JOIN {schema_name}.entities t ON er.target_entity_id = t.entity_id
            JOIN {schema_name}.entity_attributes sa ON er.source_attribute_id = sa.attribute_id
            JOIN {schema_name}.entity_attributes ta ON er.target_attribute_id = ta.attribute_id
        WHERE 
            sa.name = 'managerId' AND ta.name = 'employeeId'
        """,
        schema_name=schema_name
    )
    
    if success and result:
        logger.info("Relationship verification:")
        for row in result:
            logger.info(f"  - ID: {row[0]}")
            logger.info(f"  - Source Entity: {row[1]}")
            logger.info(f"  - Target Entity: {row[2]}")
            logger.info(f"  - Relationship Type: {row[3]}")
            logger.info(f"  - Source Attribute: {row[4]}")
            logger.info(f"  - Target Attribute: {row[5]}")
            logger.info(f"  - ON DELETE: {row[6]}")
            logger.info(f"  - ON UPDATE: {row[7]}")
            logger.info(f"  - Foreign Key Type: {row[8]}")
    else:
        logger.error("Failed to verify relationship")

if __name__ == "__main__":
    create_employee_manager_relationship()
