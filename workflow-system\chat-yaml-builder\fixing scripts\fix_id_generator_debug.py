#!/usr/bin/env python3
"""
Fix for ID Generator Debug script to ensure attribute_map and required_attributes are updated.

This script modifies the enriched YAML file to update the attribute_map and required_attributes
sections to match the new attribute IDs that were generated during the ID enrichment process.
"""

import yaml
import os
import sys

def fix_attribute_references(yaml_path):
    """
    Fix attribute references in the enriched YAML file.
    
    This function reads the enriched YAML file, identifies the mappings between old and new
    attribute IDs, and updates the attribute_map and required_attributes sections accordingly.
    """
    print(f"Reading enriched YAML file: {yaml_path}")
    
    try:
        with open(yaml_path, 'r') as f:
            data = yaml.safe_load(f)
        
        # Load the original YAML to get the old-to-new ID mappings
        with open("runtime/workflow-engine/yamls/leavemanagement_new_rbac.yaml", 'r') as f:
            original_data = yaml.safe_load(f)
        
        # Process each entity
        for entity in data.get('entities', []):
            entity_id = entity.get('id')
            entity_name = entity.get('name')
            print(f"Processing entity: {entity_id} ({entity_name})")
            
            # Extract attribute mappings
            attribute_mappings = {}
            old_to_new_id_map = {}
            
            # Find the corresponding entity in the original YAML
            original_entity = None
            for orig_entity in original_data.get('entities', []):
                if orig_entity.get('name') == entity_name:
                    original_entity = orig_entity
                    break
            
            if not original_entity:
                print(f"  Warning: Could not find entity {entity_name} in original YAML")
                continue
            
            # Build mapping from attribute names to new IDs
            for attr in entity.get('attributes', []):
                attr_id = attr.get('id')
                attr_name = attr.get('name')
                print(f"  Found attribute: {attr_id} ({attr_name})")
                attribute_mappings[attr_name] = attr_id
            
            # Build mapping from old IDs to new IDs
            if ('attributes_metadata' in original_entity and 
                'attribute_map' in original_entity['attributes_metadata']):
                orig_attr_map = original_entity['attributes_metadata']['attribute_map']
                
                for old_id, name in orig_attr_map.items():
                    if name in attribute_mappings:
                        new_id = attribute_mappings[name]
                        old_to_new_id_map[old_id] = new_id
                        print(f"  Mapping old ID {old_id} to new ID {new_id} for {name}")
            
            # Update attribute_map
            if 'attributes_metadata' in entity and 'attribute_map' in entity['attributes_metadata']:
                old_map = entity['attributes_metadata']['attribute_map']
                new_map = {}
                
                for old_id, name in old_map.items():
                    if name in attribute_mappings:
                        new_id = attribute_mappings[name]
                        new_map[new_id] = name
                        print(f"  Updating attribute_map: {old_id} -> {new_id} for {name}")
                    else:
                        # Keep the old mapping if we can't find a new one
                        new_map[old_id] = name
                        print(f"  Warning: Could not find new ID for {name} (keeping {old_id})")
                
                entity['attributes_metadata']['attribute_map'] = new_map
            
            # Update required_attributes using the old-to-new ID mapping
            if 'attributes_metadata' in entity and 'required_attributes' in entity['attributes_metadata']:
                old_required = entity['attributes_metadata']['required_attributes']
                new_required = []
                
                for old_id in old_required:
                    if old_id in old_to_new_id_map:
                        new_id = old_to_new_id_map[old_id]
                        new_required.append(new_id)
                        print(f"  Updating required_attributes: {old_id} -> {new_id}")
                    else:
                        # Keep the old ID if we can't find a new one
                        new_required.append(old_id)
                        print(f"  Warning: Could not find new ID for required attribute {old_id}")
                
                entity['attributes_metadata']['required_attributes'] = new_required
        
        # Write the updated YAML back to the file
        print(f"Writing updated YAML to: {yaml_path}")
        with open(yaml_path, 'w') as f:
            yaml.dump(data, f, sort_keys=False)
        
        print("✅ Successfully updated attribute references in the YAML file.")
        return True
    
    except Exception as e:
        print(f"❌ Error fixing attribute references: {e}")
        import traceback
        print(traceback.format_exc())
        return False

def main():
    """Main function to run the fix."""
    yaml_path = "runtime/workflow-engine/yamls/enriched_debug.yaml"
    
    if not os.path.exists(yaml_path):
        print(f"❌ Enriched YAML file not found at: {yaml_path}")
        return 1
    
    success = fix_attribute_references(yaml_path)
    
    if success:
        print("\n✅ Fix completed successfully.")
        print(f"The updated YAML file is available at: {yaml_path}")
        print("You can now try to insert this YAML into the database.")
        return 0
    else:
        print("\n❌ Fix failed.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
