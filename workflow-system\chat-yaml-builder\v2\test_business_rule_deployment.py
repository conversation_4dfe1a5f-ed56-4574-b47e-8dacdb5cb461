#!/usr/bin/env python3
"""
Test script to verify that business rules are correctly deployed to the database.
"""

import os
import json
import logging
from parsers.entity_parser import parse_entities
from deployers.entity_deployer_v2 import deploy_entities, execute_query

# Set up logging
logging.basicConfig(level=logging.INFO, 
                   format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
                   handlers=[
                       logging.FileHandler('/home/<USER>/workflow-system/chat-yaml-builder/v2/business_rule_deployment.log'),
                       logging.StreamHandler()
                   ])
logger = logging.getLogger('test_business_rule_deployment')

def test_business_rule_deployment():
    """
    Test that business rules are correctly deployed to the database.
    """
    # Read the sample entity output file
    sample_file_path = '/home/<USER>/workflow-system/chat-yaml-builder/v2/samples/sample_entity_output2.txt'
    with open(sample_file_path, 'r') as f:
        entity_def = f.read()
    
    # Add the specific business rule we want to test
    business_rule = """
BusinessRule EMP001 for Employee:
* Employee.status must be Active to receive performance reviews
* Employee.hireDate must be at least 90 days before Employee.performanceRating can be set
"""
    
    # Append the business rule to the entity definition
    entity_def_with_rule = entity_def + business_rule
    
    # Parse the entity definition
    entities_data, warnings = parse_entities(entity_def_with_rule)
    
    # Print any warnings
    if warnings:
        logger.warning("Warnings during parsing:")
        for warning in warnings:
            logger.warning(f"  - {warning}")
    
    # Check if Employee entity was parsed
    if 'entities' in entities_data and 'Employee' in entities_data['entities']:
        employee_entity = entities_data['entities']['Employee']
        
        # Check if business rules were parsed
        if 'business_rules' in employee_entity:
            logger.info("\nEmployee business rules:")
            for rule_id, rule_def in employee_entity['business_rules'].items():
                logger.info(f"\n  - Business Rule: {rule_id}")
                if 'conditions' in rule_def:
                    for i, condition in enumerate(rule_def['conditions']):
                        logger.info(f"    - Condition {i+1}: {condition}")
                else:
                    logger.warning(f"No conditions found in business rule '{rule_id}'")
        else:
            logger.warning("No business rules found in Employee entity")
            return
    else:
        logger.error("Employee entity not found in parsed data")
        return
    
    # Deploy the entities to the database
    schema_name = 'workflow_temp'
    success, messages = deploy_entities(entities_data, schema_name)
    
    if not success:
        logger.error("Failed to deploy entities:")
        for message in messages:
            logger.error(f"  - {message}")
        return
    
    logger.info("Successfully deployed entities")
    
    # Verify the business rules were deployed
    success, messages, result = execute_query(
        f"""
        SELECT er.rule_id, er.name, er.entity_id, e.name as entity_name
        FROM {schema_name}.entity_business_rules er
        JOIN {schema_name}.entities e ON er.entity_id = e.entity_id
        WHERE e.name = 'Employee'
        """,
        schema_name=schema_name
    )
    
    if success and result:
        logger.info("\nEmployee business rules in the database:")
        logger.info("=" * 80)
        logger.info(f"{'Rule ID':<20} {'Name':<20} {'Entity ID':<20} {'Entity Name':<20}")
        logger.info("-" * 80)
        for row in result:
            rule_id = row[0]
            name = row[1]
            entity_id = row[2]
            entity_name = row[3]
            logger.info(f"{rule_id:<20} {name:<20} {entity_id:<20} {entity_name:<20}")
        logger.info("=" * 80)
        
        # Get conditions for each rule
        for row in result:
            rule_id = row[0]
            
            success, messages, result = execute_query(
                f"""
                SELECT condition_text, attribute_name, operator, value
                FROM {schema_name}.business_rule_conditions
                WHERE rule_id = %s
                """,
                (rule_id,),
                schema_name
            )
            
            if success and result:
                logger.info(f"\nConditions for business rule '{rule_id}':")
                logger.info("=" * 80)
                logger.info(f"{'Condition':<60} {'Attribute':<15} {'Operator':<15} {'Value':<20}")
                logger.info("-" * 80)
                for row in result:
                    condition = row[0]
                    attribute = row[1] if row[1] else "N/A"
                    operator = row[2] if row[2] else "N/A"
                    value = row[3] if row[3] else "N/A"
                    logger.info(f"{condition:<60} {attribute:<15} {operator:<15} {value:<20}")
                logger.info("=" * 80)
    else:
        logger.warning("No Employee business rules found in the database")

if __name__ == "__main__":
    test_business_rule_deployment()
