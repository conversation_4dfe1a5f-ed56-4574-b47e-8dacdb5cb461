import unittest
from typing import Dict, Any
import sys
import os

# Add project root to path if needed
project_root = '/home/<USER>/workflow-system/runtime/workflow-engine'
if project_root not in sys.path:
    sys.path.insert(0, project_root)

# Import the function to test
from app.services.system_functions import deep_merge

class TestDeepMerge(unittest.TestCase):
    def test_basic_merge(self):
        """Test basic merge of two simple dictionaries."""
        dict1 = {"a": 1, "b": 2}
        dict2 = {"c": 3, "d": 4}
        
        result = deep_merge(dict1, dict2)
        
        expected = {"a": 1, "b": 2, "c": 3, "d": 4}
        self.assertEqual(result, expected)
    
    def test_overlapping_keys(self):
        """Test merge with overlapping keys."""
        dict1 = {"a": 1, "b": 2, "c": 3}
        dict2 = {"b": 20, "c": 30, "d": 40}
        
        result = deep_merge(dict1, dict2)
        
        # dict2 values should take precedence for common keys
        expected = {"a": 1, "b": 20, "c": 30, "d": 40}
        self.assertEqual(result, expected)
    
    def test_nested_dictionaries(self):
        """Test merge with nested dictionaries."""
        dict1 = {
            "a": 1,
            "b": {
                "x": 10,
                "y": 20
            }
        }
        dict2 = {
            "b": {
                "y": 200,
                "z": 300
            },
            "c": 3
        }
        
        result = deep_merge(dict1, dict2)
        
        expected = {
            "a": 1,
            "b": {
                "x": 10,
                "y": 200,  # Value from dict2 takes precedence
                "z": 300   # New key from dict2
            },
            "c": 3
        }
        self.assertEqual(result, expected)
    
    def test_deeply_nested_dictionaries(self):
        """Test merge with deeply nested dictionaries."""
        dict1 = {
            "level1": {
                "level2": {
                    "level3": {
                        "a": 1,
                        "b": 2
                    }
                }
            }
        }
        dict2 = {
            "level1": {
                "level2": {
                    "level3": {
                        "b": 20,
                        "c": 30
                    },
                    "new_key": "value"
                }
            }
        }
        
        result = deep_merge(dict1, dict2)
        
        expected = {
            "level1": {
                "level2": {
                    "level3": {
                        "a": 1,
                        "b": 20,  # Value from dict2 takes precedence
                        "c": 30   # New key from dict2
                    },
                    "new_key": "value"  # New key from dict2
                }
            }
        }
        self.assertEqual(result, expected)
    
    def test_mixed_types(self):
        """Test merge with mixed types of values."""
        dict1 = {
            "a": 1,
            "b": {
                "x": 10,
                "y": 20
            },
            "c": "string1"
        }
        dict2 = {
            "b": {
                "y": 200,
                "z": [1, 2, 3]  # List as value
            },
            "c": True,  # Boolean as value
            "d": None   # None as value
        }
        
        result = deep_merge(dict1, dict2)
        
        expected = {
            "a": 1,
            "b": {
                "x": 10,
                "y": 200,
                "z": [1, 2, 3]
            },
            "c": True,  # Value from dict2 takes precedence
            "d": None
        }
        self.assertEqual(result, expected)
    
    def test_dict_replaces_non_dict(self):
        """Test when a dictionary replaces a non-dictionary value."""
        dict1 = {
            "a": 1,
            "b": "string"
        }
        dict2 = {
            "b": {
                "x": 10,
                "y": 20
            }
        }
        
        result = deep_merge(dict1, dict2)
        
        expected = {
            "a": 1,
            "b": {
                "x": 10,
                "y": 20
            }
        }
        self.assertEqual(result, expected)
    
    def test_non_dict_replaces_dict(self):
        """Test when a non-dictionary value replaces a dictionary."""
        dict1 = {
            "a": 1,
            "b": {
                "x": 10,
                "y": 20
            }
        }
        dict2 = {
            "b": "string"
        }
        
        result = deep_merge(dict1, dict2)
        
        expected = {
            "a": 1,
            "b": "string"  # Value from dict2 takes precedence
        }
        self.assertEqual(result, expected)
    
    def test_empty_dictionaries(self):
        """Test merge with empty dictionaries."""
        dict1 = {}
        dict2 = {"a": 1, "b": 2}
        
        # Empty first dict
        result1 = deep_merge(dict1, dict2)
        self.assertEqual(result1, {"a": 1, "b": 2})
        
        # Empty second dict
        result2 = deep_merge(dict2, dict1)
        self.assertEqual(result2, {"a": 1, "b": 2})
        
        # Both empty
        result3 = deep_merge({}, {})
        self.assertEqual(result3, {})
    
    def test_original_dictionaries_unchanged(self):
        """Test that original dictionaries are not modified."""
        dict1 = {"a": 1, "b": {"x": 10}}
        dict2 = {"b": {"y": 20}}
        
        original_dict1 = dict1.copy()
        original_dict2 = dict2.copy()
        
        deep_merge(dict1, dict2)
        
        # Original dictionaries should be unchanged
        self.assertEqual(dict1, original_dict1)
        self.assertEqual(dict2, original_dict2)
    
    def test_complex_structures(self):
        """Test with more complex nested structures."""
        dict1 = {
            "users": [
                {"id": 1, "name": "Alice"},
                {"id": 2, "name": "Bob"}
            ],
            "config": {
                "theme": "dark",
                "settings": {
                    "notifications": True,
                    "language": "en"
                }
            }
        }
        dict2 = {
            "users": [
                {"id": 3, "name": "Charlie"}
            ],
            "config": {
                "version": "1.0",
                "settings": {
                    "language": "fr",
                    "timezone": "UTC"
                }
            }
        }
        
        result = deep_merge(dict1, dict2)
        
        # Note: Lists are not merged, dict2 value takes precedence
        expected = {
            "users": [
                {"id": 3, "name": "Charlie"}
            ],
            "config": {
                "theme": "dark",
                "version": "1.0",
                "settings": {
                    "notifications": True,
                    "language": "fr",
                    "timezone": "UTC"
                }
            }
        }
        self.assertEqual(result, expected)

if __name__ == '__main__':
    unittest.main()