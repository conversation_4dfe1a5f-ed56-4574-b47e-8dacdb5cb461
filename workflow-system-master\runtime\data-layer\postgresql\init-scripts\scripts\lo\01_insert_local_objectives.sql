-- Purchase Furniture Local Objectives Insert Script
-- Inserts GO2.LO1, GO2.LO2, GO2.LO3

SET search_path TO workflow_runtime;

-- =====================================================
-- LOCAL OBJECTIVES (GO2.LO1, GO2.LO2, GO2.LO3)
-- =====================================================
INSERT INTO local_objectives (
    lo_id, go_id, name, description, version, status, created_at, updated_at, 
    tenant_id, created_by, updated_by, natural_language
) VALUES 
('GO2.LO1', 'GO2', 'SelectFurnitureType', 'Local Objective: SelectFurnitureType', '1.0', 'Active', NOW(), NOW(), 'T2', 'system', 'system', 'User selects furniture type and product from available inventory'),
('GO2.LO2', 'GO2', 'ProcessCartPayment', 'Local Objective: ProcessCartPayment', '1.0', 'Active', NOW(), NOW(), 'T2', 'system', 'system', 'Process cart with quantity calculations and payment method selection'),
('GO2.LO3', 'GO2', 'CompleteOrderInventory', 'Local Objective: CompleteOrderInventory', '1.0', 'Active', NOW(), NOW(), 'T2', 'system', 'system', 'Complete order placement and update inventory levels');

COMMIT;
