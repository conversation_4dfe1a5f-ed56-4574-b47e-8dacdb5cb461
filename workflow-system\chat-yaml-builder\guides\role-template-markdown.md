# Role Definition Template

## 1. Standard Role Definition Template

```
Role [RoleName] ([role_id]) inherits [ParentRole]:
- Create: [Entity1], [Entity2] (attr1^deny, attr2^deny)
- Read: [Entity1], [Entity2] (attr3^deny, attr4^deny)
- Update: [Entity1] (attr5^deny), [Entity2]
- GO: [go001] as [RoleType], [go002] as [RoleType]
- Scope: [ScopeLevel]
- Classification: [RoleClassification]
- Special: [SpecialConditions]
```

## 2. Metadata Template (for hover/click details)

```
Role Details: [RoleName]

Core Information:
ID: [role_id]
Version: [version]
Display Name: [DisplayName]
Category: [category]
Classification: [classification]
Status: [Active/Draft/Deprecated]
Risk Level: [High/Medium/Low]

Lifecycle:
Created: [Date] by [Name]
Last Modified: [Date] by [Name]
Approval Chain: [approver_list]

Permission Summary:
Total Permissions: [count]
Entity Access: [create_count/read_count/update_count]
GO Roles: [originator_count/owner_count/sponsor_count]

Usage Statistics:
Active Users: [count]
Last Access: [Date]
Success Rate: [percentage]
```

## 3. Example Role Definition

```
Role Employee (emp001):
- Create: LeaveApplication
- Read: LeaveApplication, Employee (salary^deny, performanceRating^deny)
- Update: Employee (own only - salary^deny, performanceRating^deny)
- GO: go001 as Originator
- Scope: Own
- Classification: Standard
```

## 4. Example Role with Inheritance

```
Role Manager (mgr001) inherits Employee:
- Create: PerformanceReview, TeamBudget
- Read: Employee (salary^deny, ssn^deny), TeamMetrics
- Update: PerformanceReview, TeamBudget (within limits)
- GO: go001 as ProcessOwner, go002 as Originator
- Scope: Team
- Classification: Standard
- Special: budget approval up to $10K
```