#!/bin/bash
# Run RBAC validation on a YAML file

if [ $# -eq 0 ]; then
    echo "Usage: $0 <yaml_file_path>"
    echo "Example: $0 yamls/user_management_workflow.yaml"
    exit 1
fi

YAML_FILE=$1

if [ ! -f "$YAML_FILE" ]; then
    echo "Error: File $YAML_FILE not found."
    exit 1
fi

echo "Running RBAC validation on $YAML_FILE..."
echo "==============================================================="

# Run the validation script
python3 validate_engine.py "$YAML_FILE"

# Check the exit code
if [ $? -eq 0 ]; then
    echo "==============================================================="
    echo "✅ YAML file passed validation!"
    echo "The YAML file is valid and complies with RBAC requirements."
else
    echo "==============================================================="
    echo "❌ YAML file failed validation."
    echo "Please check the error messages above and fix the issues."
fi
