tenant:
  id: "t001"
  name: "LeaveManagement001"
  roles:
    - id: "r001"
      name: "Employee"
      inherits_from: null
      access:
        entities:
          - entity_id: "e001"  # LeaveApplication entity
            permissions: ["Read", "Create"]
          - entity_id: "e003"  # User entity
            permissions: ["Read"]
          - entity_id: "e004"  # Role entity
            permissions: ["Read"]
        objectives:
          - objective_id: "go001.lo001"
            permissions: ["Execute"]
    - id: "r002"
      name: "Manager"
      inherits_from: null
      access:
        entities:
          - entity_id: "e001"  # LeaveApplication entity
            permissions: ["Read", "Update"]
          - entity_id: "e003"  # User entity
            permissions: ["Read"]
          - entity_id: "e004"  # Role entity
            permissions: ["Read"]
        objectives:
          - objective_id: "go001.lo002"
            permissions: ["Execute"]
    - id: "r003"
      name: "HR Manager"
      inherits_from: null
      access:
        entities:
          - entity_id: "e001"  # LeaveApplication entity
            permissions: ["Read", "Update"]
          - entity_id: "e003"  # User entity
            permissions: ["Read", "Update"]
          - entity_id: "e004"  # Role entity
            permissions: ["Read"]
        objectives:
          - objective_id: "go001.lo003"
            permissions: ["Execute"]

workflow_data:
  software_type: "Leave Management"
  industry: "Human Resources"
  version: "1.0"
  created_by: "system"
  created_on: "{{timestamp}}"

permission_types:
  - id: "read"
    description: "Can read entity data"
    capabilities: ["GET"]
  - id: "create"
    description: "Can create new entity records"
    capabilities: ["POST"]
  - id: "update"
    description: "Can update existing records"
    capabilities: ["PUT"]
  - id: "delete"
    description: "Can delete entity records"
    capabilities: ["DELETE"]
  - id: "execute"
    description: "Can execute workflows"
    capabilities: ["EXECUTE"]

entities:
  - id: "e001"
    name: "LeaveApplication"
    type: "Master"
    version: "1.0"
    status: "Active"
    attributes_metadata:
      attribute_prefix: "at"
      attribute_map:
        "at101": "leaveID"
        "at102": "employeeID"
        "at103": "startDate"
        "at104": "endDate"
        "at105": "numDays"
        "at106": "reason"
        "at107": "status"
        "at108": "remarks"
        "at109": "approvedBy"
        "at110": "leaveType"
        "at111": "leaveSubType"
      required_attributes:
        - "at101"
        - "at102"
        - "at103"
        - "at104"
        - "at105"
        - "at106"
        - "at107"
        - "at110"
    attributes:
      - id: "at101"
        name: "leaveID"
        display_name: "Leave ID"
        datatype: "String"
        required: true
        version: "1.0"
        status: "Deployed"
      - id: "at102"
        name: "employeeID"
        display_name: "Employee ID"
        datatype: "String"
        required: true
        version: "1.0"
        status: "Deployed"
      - id: "at103"
        name: "startDate"
        display_name: "Start Date"
        datatype: "Date"
        required: true
        version: "1.0"
        status: "Deployed"
      - id: "at104"
        name: "endDate"
        display_name: "End Date"
        datatype: "Date"
        required: true
        version: "1.0"
        status: "Deployed"
      - id: "at105"
        name: "numDays"
        display_name: "Number of Days"
        datatype: "Integer"
        required: true
        version: "1.0"
        status: "Deployed"
      - id: "at106"
        name: "reason"
        display_name: "Leave Reason"
        datatype: "String"
        required: true
        version: "1.0"
        status: "Deployed"
      - id: "at107"
        name: "status"
        display_name: "Status"
        datatype: "Enum"
        required: true
        values: ["Pending", "Approved", "Rejected"]
        version: "1.0"
        status: "Deployed"
      - id: "at108"
        name: "remarks"
        display_name: "Remarks"
        datatype: "String"
        required: false
        version: "1.0"
        status: "Deployed"
      - id: "at109"
        name: "approvedBy"
        display_name: "Approved By"
        datatype: "String"
        required: false
        version: "1.0"
        status: "Deployed"
      - id: "at110"
        name: "leaveType"
        display_name: "Leave Type"
        datatype: "Enum"
        required: true
        values: ["Annual Leave", "Sick Leave", "Parental Leave", "Bereavement"]
        version: "1.0"
        status: "Deployed"
      - id: "at111"
        name: "leaveSubType"
        display_name: "Leave Sub-Type"
        datatype: "String"
        required: false
        version: "1.0"
        status: "Deployed"
  - id: "e002"
    name: "LeaveSubType"
    type: "Master"
    version: "1.0"
    status: "Active"
    attributes_metadata:
      attribute_prefix: "at"
      attribute_map:
        "at201": "leaveType"
        "at202": "subTypeId"
        "at203": "subTypeName"
        "at204": "active"
      required_attributes:
        - "at201"
        - "at202"
        - "at203"
    attributes:
      - id: "at201"
        name: "leaveType"
        display_name: "Leave Type"
        datatype: "String"
        required: true
        version: "1.0"
        status: "Deployed"
      - id: "at202"
        name: "subTypeId"
        display_name: "Sub Type ID"
        datatype: "String"
        required: true
        version: "1.0"
        status: "Deployed"
      - id: "at203"
        name: "subTypeName"
        display_name: "Sub Type Name"
        datatype: "String"
        required: true
        version: "1.0"
        status: "Deployed"
      - id: "at204"
        name: "active"
        display_name: "Active"
        datatype: "Boolean"
        required: false
        version: "1.0"
        status: "Deployed"
    # Synthetic data for dependent dropdowns
    synthetic_records:
      # Annual Leave sub-types
      - at201: "Annual Leave"
        at202: "AL001"
        at203: "Standard Annual Leave"
        at204: true
      - at201: "Annual Leave"
        at202: "AL002"
        at203: "Carry-over Leave"
        at204: true
      - at201: "Annual Leave"
        at202: "AL003"
        at203: "Compensatory Leave"
        at204: true
      # Sick Leave sub-types
      - at201: "Sick Leave"
        at202: "SL001"
        at203: "Short-term Illness"
        at204: true
      - at201: "Sick Leave"
        at202: "SL002"
        at203: "Hospitalization"
        at204: true
      - at201: "Sick Leave"
        at202: "SL003"
        at203: "Chronic Condition"
        at204: true
      # Parental Leave sub-types
      - at201: "Parental Leave"
        at202: "PL001"
        at203: "Maternity Leave"
        at204: true
      - at201: "Parental Leave"
        at202: "PL002"
        at203: "Paternity Leave"
        at204: true
      - at201: "Parental Leave"
        at202: "PL003"
        at203: "Adoption Leave"
        at204: true
      # Bereavement sub-types
      - at201: "Bereavement"
        at202: "BL001"
        at203: "Immediate Family"
        at204: true
      - at201: "Bereavement"
        at202: "BL002"
        at203: "Extended Family"
        at204: true
  - id: "e003"
    name: "User"
    type: "Master"
    version: "1.0"
    status: "Active"
    attributes_metadata:
      attribute_prefix: "at"
      attribute_map:
        "at301": "user_id"
        "at302": "username"
        "at303": "email"
        "at304": "first_name"
        "at305": "last_name"
        "at306": "status"
        "at307": "password_hash"
        "at308": "disabled"
        "at309": "organization"
        "at310": "team"
      required_attributes:
        - "at301"
        - "at302"
        - "at303"
        - "at306"
    attributes:
      - id: "at301"
        name: "user_id"
        display_name: "User ID"
        datatype: "String"
        required: true
        version: "1.0"
        status: "Deployed"
      - id: "at302"
        name: "username"
        display_name: "Username"
        datatype: "String"
        required: true
        version: "1.0"
        status: "Deployed"
      - id: "at303"
        name: "email"
        display_name: "Email"
        datatype: "String"
        required: true
        version: "1.0"
        status: "Deployed"
      - id: "at304"
        name: "first_name"
        display_name: "First Name"
        datatype: "String"
        required: false
        version: "1.0"
        status: "Deployed"
      - id: "at305"
        name: "last_name"
        display_name: "Last Name"
        datatype: "String"
        required: false
        version: "1.0"
        status: "Deployed"
      - id: "at306"
        name: "status"
        display_name: "Status"
        datatype: "Enum"
        required: true
        values: ["active", "inactive", "suspended"]
        version: "1.0"
        status: "Deployed"
      - id: "at307"
        name: "password_hash"
        display_name: "Password Hash"
        datatype: "String"
        required: false
        version: "1.0"
        status: "Deployed"
      - id: "at308"
        name: "disabled"
        display_name: "Disabled"
        datatype: "Boolean"
        required: false
        version: "1.0"
        status: "Deployed"
      - id: "at309"
        name: "organization"
        display_name: "Organization"
        datatype: "String"
        required: false
        version: "1.0"
        status: "Deployed"
      - id: "at310"
        name: "team"
        display_name: "Team"
        datatype: "String"
        required: false
        version: "1.0"
        status: "Deployed"
  - id: "e004"
    name: "Role"
    type: "Master"
    version: "1.0"
    status: "Active"
    attributes_metadata:
      attribute_prefix: "at"
      attribute_map:
        "at401": "role_id"
        "at402": "name"
        "at403": "description"
        "at404": "inherits_from"
        "at405": "tenant_id"
      required_attributes:
        - "at401"
        - "at402"
    attributes:
      - id: "at401"
        name: "role_id"
        display_name: "Role ID"
        datatype: "String"
        required: true
        version: "1.0"
        status: "Deployed"
      - id: "at402"
        name: "name"
        display_name: "Name"
        datatype: "String"
        required: true
        version: "1.0"
        status: "Deployed"
      - id: "at403"
        name: "description"
        display_name: "Description"
        datatype: "String"
        required: false
        version: "1.0"
        status: "Deployed"
      - id: "at404"
        name: "inherits_from"
        display_name: "Inherits From"
        datatype: "String"
        required: false
        version: "1.0"
        status: "Deployed"
      - id: "at405"
        name: "tenant_id"
        display_name: "Tenant ID"
        datatype: "String"
        required: false
        version: "1.0"
        status: "Deployed"

global_objectives:
  - id: "go001"
    name: "Leave Application Workflow"
    version: "1.0"
    status: "Active"
    input_stack:
      description: "Global inputs"
      inputs: []
    output_stack:
      description: "Global outputs"
      outputs: []
    data_mapping_stack:
      description: "Data handover between GOs"
      mappings: []

local_objectives:
  - id: "lo001"
    contextual_id: "go001.lo001"
    name: "Apply for Leave"
    workflow_source: "origin"
    function_type: "Create"
    agent_stack:
      agents:
        - role: "r001"  # Employee role
          rights: ["Execute"]
          users: []  # Empty means all users with this role have these rights
    input_stack:
      description: "Capture leave application details"
      inputs:
        # Information-only help text
        - id: "in027"
          slot_id: "e001.at999.in027"
          contextual_id: "go001.lo001.in027"
          source:
            type: "information"
            description: "Leave Request Instructions"
          required: false
          data_type: "string"
          ui_control: "oj-text"
          is_visible: true
          metadata:
            usage: ""
          validations: []
          nested_function:
            id: "nf006"
            function_name: "to_uppercase"
            function_type: "transform"
            parameters:
              text: "Please specify your leave dates and reason. If selecting Sick Leave for more than 3 days, a medical certificate will be required."
            output_to: "info_text"

        # Auto-generated Leave ID
        - id: "in001"
          slot_id: "e001.at101.in001"
          contextual_id: "go001.lo001.in001"
          source:
            type: "system"
            description: "Auto-generated Leave ID"
          required: true
          data_type: "string"
          ui_control: "oj-input-text"
          metadata:
            usage: ""
          validations:
            - rule: "Leave ID must be unique"
              rule_type: "validate_required"
              validation_method: "validate_required"
              error_message: "Leave ID is required"
          nested_function:
            id: "nf001"
            function_name: "generate_id"
            function_type: "utility"
            parameters:
              entity: "e001"
              attribute: "at101"
              prefix: "LV"
            output_to: "at101"

        # Employee ID
        - id: "in002"
          slot_id: "e001.at102.in002"
          contextual_id: "go001.lo001.in002"
          source:
            type: "user"
            description: "Employee ID"
          required: true
          data_type: "string"
          ui_control: "oj-input-text"
          metadata:
            usage: ""
          validations:
            - rule: "Employee ID is required"
              rule_type: "validate_required"
              validation_method: "validate_required"
              error_message: "Employee ID cannot be empty"

        # Leave Type dropdown with function source
        - id: "in025"
          slot_id: "e001.at110.in025"
          contextual_id: "go001.lo001.in025"
          source:
            type: "user"
            description: "Leave Type"
          required: true
          data_type: "enum"
          ui_control: "oj-combobox-one"
          has_dropdown_source: true
          metadata:
            usage: ""
          validations:
            - rule: "Leave Type is required"
              rule_type: "validate_required"
              validation_method: "validate_required"
              error_message: "Leave Type is required"
          dropdown_source:
            source_type: "function"
            function_name: "fetch_enum_values"
            function_params:
              entity_id: "e001"
              attribute_id: "at110"
            value_field: "value"
            display_field: "display"

        # Leave Sub-Type dropdown - dependent on Leave Type
        - id: "in026"
          slot_id: "e001.at111.in026"
          contextual_id: "go001.lo001.in026"
          source:
            type: "system_dependent"
            description: "Leave Sub-Type"
          required: false
          data_type: "enum"
          ui_control: "oj-combobox-one"
          dependencies: ["in025"]
          dependency_type: "dropdown"
          has_dropdown_source: true
          metadata:
            usage: ""
          validations: []
          dropdown_source:
            source_type: "function"
            function_name: "fetch_filtered_records"
            function_params:
              table: "workflow_runtime.leave_sub_type"
              filter_column: "leave_type"
              filter_value: "${in025}"
              value_column: "sub_type_id"
              display_column: "sub_type_name"
              additional_filters: 
                active: true
            value_field: "value"
            display_field: "display"
            depends_on_fields: ["in025"]

        # Start Date
        - id: "in003"
          slot_id: "e001.at103.in003"
          contextual_id: "go001.lo001.in003"
          source:
            type: "user"
            description: "Start Date"
          required: true
          data_type: "date"
          ui_control: "oj-input-date"
          metadata:
            usage: ""
          validations:
            - rule: "Start Date is required"
              rule_type: "validate_required"
              validation_method: "validate_required"
              error_message: "Start Date cannot be empty"

        # End Date
        - id: "in004"
          slot_id: "e001.at104.in004"
          contextual_id: "go001.lo001.in004"
          source:
            type: "user"
            description: "End Date"
          required: true
          data_type: "date"
          ui_control: "oj-input-date"
          metadata:
            usage: ""
          validations:
            - rule: "End Date is required"
              rule_type: "validate_required"
              validation_method: "validate_required"
              error_message: "End Date cannot be empty"

        # Number of Days - now system_dependent with proper dependencies
        - id: "in005"
          slot_id: "e001.at105.in005"
          contextual_id: "go001.lo001.in005"
          source:
            type: "system_dependent"
            description: "Number of Days (calculated)"
          required: true
          data_type: "integer"
          ui_control: "oj-input-number"
          dependencies: ["in003", "in004"]
          dependency_type: "calculation"
          metadata:
            usage: ""
          validations:
            - rule: "Number of Days must be positive"
              rule_type: "validate_required"
              validation_method: "validate_required"
              error_message: "Number of Days cannot be empty"
          nested_function:
            id: "nf002"
            function_name: "subtract_days"
            function_type: "math"
            parameters:
              start_date: "${in003}"
              end_date: "${in004}"
            output_to: "at105"

        # Leave Reason
        - id: "in006"
          slot_id: "e001.at106.in006"
          contextual_id: "go001.lo001.in006"
          source:
            type: "user"
            description: "Leave Reason"
          required: true
          data_type: "string"
          ui_control: "oj-text-area"
          metadata:
            usage: ""
          validations:
            - rule: "Reason is required"
              rule_type: "validate_required"
              validation_method: "validate_required"
              error_message: "Leave reason cannot be empty"

        # Status - auto-set to Pending
        - id: "in007"
          slot_id: "e001.at107.in007"
          contextual_id: "go001.lo001.in007"
          source:
            type: "system"
            description: "Status"
          required: true
          data_type: "enum"
          allowed_values: ["Pending", "Approved", "Rejected"]
          ui_control: "oj-select-single"
          metadata:
            usage: ""
          validations:
            - rule: "Status must be valid"
              rule_type: "enum_check"
              validation_method: "enum_check"
              allowed_values: ["Pending", "Approved", "Rejected"]
              error_message: "Invalid status value"
          nested_function:
            id: "nf003"
            function_name: "to_uppercase"
            function_type: "transform"
            parameters:
              text: "Pending"
            output_to: "at107"

    output_stack:
      description: "Leave application created"
      outputs:
        - id: "out001"
          slot_id: "executionstatus.out001"
          contextual_id: "go001.lo001.out001"
          source:
            type: "system"
            description: "Success or failure"
          data_type: "string"

        - id: "out002"
          slot_id: "e001.at101.out002"
          contextual_id: "go001.lo001.out002"
          source:
            type: "system"
            description: "Leave ID"
          data_type: "string"

        - id: "out003"
          slot_id: "e001.at102.out003"
          contextual_id: "go001.lo001.out003"
          source:
            type: "system"
            description: "Employee ID"
          data_type: "string"

        - id: "out004"
          slot_id: "e001.at103.out004"
          contextual_id: "go001.lo001.out004"
          source:
            type: "system"
            description: "Start Date"
          data_type: "date"

        - id: "out005"
          slot_id: "e001.at104.out005"
          contextual_id: "go001.lo001.out005"
          source:
            type: "system"
            description: "End Date"
          data_type: "date"

        - id: "out006"
          slot_id: "e001.at105.out006"
          contextual_id: "go001.lo001.out006"
          source:
            type: "system"
            description: "Number of Days"
          data_type: "integer"

        - id: "out007"
          slot_id: "e001.at106.out007"
          contextual_id: "go001.lo001.out007"
          source:
            type: "system"
            description: "Leave Reason"
          data_type: "string"

        - id: "out008"
          slot_id: "e001.at107.out008"
          contextual_id: "go001.lo001.out008"
          source:
            type: "system"
            description: "Status"
          data_type: "enum"
          
        - id: "out029"
          slot_id: "e001.at110.out029"
          contextual_id: "go001.lo001.out029"
          source:
            type: "system"
            description: "Leave Type"
          data_type: "enum"
          
        - id: "out030"
          slot_id: "e001.at111.out030"
          contextual_id: "go001.lo001.out030"
          source:
            type: "system"
            description: "Leave Sub-Type"
          data_type: "string"

    data_mapping_stack:
      description: "Data handover between LOs"
      mappings:
        - id: "map001"
          source: "lo001.out002"
          target: "lo002.in008"
          mapping_type: "direct"
        - id: "map002"
          source: "lo001.out003"
          target: "lo002.in009"
          mapping_type: "direct"
        - id: "map003"
          source: "lo001.out004"
          target: "lo002.in010"
          mapping_type: "direct"
        - id: "map004"
          source: "lo001.out005"
          target: "lo002.in011"
          mapping_type: "direct"
        - id: "map005"
          source: "lo001.out006"
          target: "lo002.in012"
          mapping_type: "direct"
        - id: "map006"
          source: "lo001.out007"
          target: "lo002.in013"
          mapping_type: "direct"
        - id: "map013"
          source: "lo001.out029"
          target: "lo002.in028"
          mapping_type: "direct"
        - id: "map014"
          source: "lo001.out030"
          target: "lo002.in029"
          mapping_type: "direct"
        - id: "map007"
          source: "lo001.out002"
          target: "lo003.in015"
          mapping_type: "direct"
        - id: "map008"
          source: "lo001.out003"
          target: "lo003.in016"
          mapping_type: "direct"
        - id: "map009"
          source: "lo001.out004"
          target: "lo003.in017"
          mapping_type: "direct"
        - id: "map010"
          source: "lo001.out005"
          target: "lo003.in018"
          mapping_type: "direct"
        - id: "map011"
          source: "lo001.out006"
          target: "lo003.in019"
          mapping_type: "direct"
        - id: "map012"
          source: "lo001.out007"
          target: "lo003.in020"
          mapping_type: "direct"
        - id: "map015"
          source: "lo001.out029"
          target: "lo003.in030"
          mapping_type: "direct"
        - id: "map016"
          source: "lo001.out030"
          target: "lo003.in031"
          mapping_type: "direct"

    execution_pathway:
      type: "alternate"
      conditions:
        - condition:
            condition_type: "attribute_comparison"
            entity: "e001"
            attribute: "at105"
            operator: "greater_than"
            value: 3
          next_lo: "lo003"
        - condition:
            condition_type: "attribute_comparison"
            entity: "e001"
            attribute: "at105"
            operator: "less_than_or_equal"
            value: 3
          next_lo: "lo002"

  - id: "lo002"
    contextual_id: "go001.lo002"
    name: "Manager Approval"
    workflow_source: "intermediate"
    function_type: "Update"
    agent_stack:
      agents:
        - role: "r002"  # Manager role
          rights: ["Execute", "Update"]
          users: []  # Empty means all users with this role have these rights
        - role: "r003"  # HR Manager role
          rights: ["Read"]
          users: ["<EMAIL>"]  # Only this specific HR director can read
    input_stack:
      description: "Manager reviews leave application"
      inputs:
        # Information policy message
        - id: "in027"
          slot_id: "e001.at999.in027"
          contextual_id: "go001.lo002.in027"
          source:
            type: "information"
            description: "Short Leave Policy"
          required: false
          data_type: "string"
          ui_control: "oj-text"
          is_visible: true
          metadata:
            usage: ""
          validations: []
          nested_function:
            id: "nf007"
            function_name: "to_uppercase"
            function_type: "transform"
            parameters:
              text: "This leave request is for 3 days or less and requires your approval. Please review the details carefully."
            output_to: "info_text"

        # Leave ID for lookup
        - id: "in008"
          slot_id: "e001.at101.in008"
          contextual_id: "go001.lo002.in008"
          source:
            type: "system"
            description: "Leave ID"
          required: true
          data_type: "string"
          ui_control: "oj-input-text"
          metadata:
            usage: "lookup"
          validations:
            - rule: "Leave ID must be present"
              rule_type: "validate_required"
              validation_method: "validate_required"
              error_message: "Leave ID is required"
            - rule: "Leave ID must exist"
              rule_type: "entity_exists"
              validation_method: "entity_exists"
              entity: "e001"
              attribute: "at101"
              error_message: "Leave ID not found in the system"
              
        # Employee ID
        - id: "in009"
          slot_id: "e001.at102.in009"
          contextual_id: "go001.lo002.in009"
          source:
            type: "system"
            description: "Employee ID"
          required: true
          data_type: "string"
          ui_control: "oj-input-text"
          metadata:
            usage: "lookup"
          validations:
            - rule: "Employee ID is required"
              rule_type: "validate_required"
              validation_method: "validate_required"
              error_message: "Employee ID is required"
              
        # Start Date
        - id: "in010"
          slot_id: "e001.at103.in010"
          contextual_id: "go001.lo002.in010"
          source:
            type: "system"
            description: "Start Date"
          required: true
          data_type: "date"
          ui_control: "oj-input-date"
          metadata:
            usage: "lookup"
          validations:
            - rule: "Start Date is required"
              rule_type: "validate_required"
              validation_method: "validate_required"
              error_message: "Start Date is required"
              
        # End Date
        - id: "in011"
          slot_id: "e001.at104.in011"
          contextual_id: "go001.lo002.in011"
          source:
            type: "system"
            description: "End Date"
          required: true
          data_type: "date"
          ui_control: "oj-input-date"
          metadata:
            usage: "lookup"
          validations:
            - rule: "End Date is required"
              rule_type: "validate_required"
              validation_method: "validate_required"
              error_message: "End Date is required"
              
        # Number of Days
        - id: "in012"
          slot_id: "e001.at105.in012"
          contextual_id: "go001.lo002.in012"
          source:
            type: "system"
            description: "Number of Days"
          required: true
          data_type: "integer"
          ui_control: "oj-input-number"
          metadata:
            usage: "lookup"
          validations:
            - rule: "Number of Days is required"
              rule_type: "validate_required"
              validation_method: "validate_required"
              error_message: "Number of Days is required"
              
        # Leave Reason
        - id: "in013"
          slot_id: "e001.at106.in013"
          contextual_id: "go001.lo002.in013"
          source:
            type: "system"
            description: "Leave Reason"
          required: true
          data_type: "string"
          ui_control: "oj-text-area"
          metadata:
            usage: "lookup"
          validations:
            - rule: "Reason is required"
              rule_type: "validate_required"
              validation_method: "validate_required"
              error_message: "Leave reason is required"
              
        # Leave Type
        - id: "in028"
          slot_id: "e001.at110.in028"
          contextual_id: "go001.lo002.in028"
          source:
            type: "system"
            description: "Leave Type"
          required: true
          data_type: "enum"
          ui_control: "oj-input-text"
          metadata:
            usage: "lookup"
          validations:
            - rule: "Leave Type is required"
              rule_type: "validate_required"
              validation_method: "validate_required"
              error_message: "Leave Type is required"
              
        # Leave Sub-Type
        - id: "in029"
          slot_id: "e001.at111.in029"
          contextual_id: "go001.lo002.in029"
          source:
            type: "system"
            description: "Leave Sub-Type"
          required: false
          data_type: "string"
          ui_control: "oj-input-text"
          metadata:
            usage: "lookup"
          validations: []
              
        # Status to be updated
        - id: "in014"
          slot_id: "e001.at107.in014"
          contextual_id: "go001.lo002.in014"
          source:
            type: "user"
            description: "Status"
          required: true
          data_type: "enum"
          allowed_values: ["Pending", "Approved", "Rejected"]
          ui_control: "oj-select-single"
          metadata:
            usage: "update"
          validations:
            - rule: "Status must be valid"
              rule_type: "enum_check"
              validation_method: "enum_check"
              allowed_values: ["Pending", "Approved", "Rejected"]
              error_message: "Invalid status value"
              
        # Remarks (optional)
        - id: "in015"
          slot_id: "e001.at108.in015"
          contextual_id: "go001.lo002.in015"
          source:
            type: "user"
            description: "Remarks"
          required: false
          data_type: "string"
          ui_control: "oj-text-area"
          metadata:
            usage: "update"
          validations: []
          
        # Approved By
        - id: "in016"
          slot_id: "e001.at109.in016"
          contextual_id: "go001.lo002.in016"
          source:
            type: "system"
            description: "Approved By"
          required: false
          data_type: "string"
          ui_control: "oj-input-text"
          metadata:
            usage: "update"
          validations: []
          nested_function:
            id: "nf004"
            function_name: "current_timestamp"
            function_type: "utility"
            parameters: {}
            output_to: "at109"

    output_stack:
      description: "Leave application updated by manager"
      outputs:
        - id: "out009"
          slot_id: "executionstatus.out009"
          contextual_id: "go001.lo002.out009"
          source:
            type: "system"
            description: "Success or failure"
          data_type: "string"
          
        - id: "out010"
          slot_id: "e001.at101.out010"
          contextual_id: "go001.lo002.out010"
          source:
            type: "system"
            description: "Leave ID"
          data_type: "string"
          
        - id: "out011"
          slot_id: "e001.at102.out011"
          contextual_id: "go001.lo002.out011"
          source:
            type: "system"
            description: "Employee ID"
          data_type: "string"
          
        - id: "out012"
          slot_id: "e001.at103.out012"
          contextual_id: "go001.lo002.out012"
          source:
            type: "system"
            description: "Start Date"
          data_type: "date"
          
        - id: "out013"
          slot_id: "e001.at104.out013"
          contextual_id: "go001.lo002.out013"
          source:
            type: "system"
            description: "End Date"
          data_type: "date"
          
        - id: "out014"
          slot_id: "e001.at105.out014"
          contextual_id: "go001.lo002.out014"
          source:
            type: "system"
            description: "Number of Days"
          data_type: "integer"
          
        - id: "out015"
          slot_id: "e001.at106.out015"
          contextual_id: "go001.lo002.out015"
          source:
            type: "system"
            description: "Leave Reason"
          data_type: "string"
          
        - id: "out016"
          slot_id: "e001.at107.out016"
          contextual_id: "go001.lo002.out016"
          source:
            type: "system"
            description: "Status"
          data_type: "enum"
          
        - id: "out017"
          slot_id: "e001.at108.out017"
          contextual_id: "go001.lo002.out017"
          source:
            type: "system"
            description: "Remarks"
          data_type: "string"
          
        - id: "out018"
          slot_id: "e001.at109.out018"
          contextual_id: "go001.lo002.out018"
          source:
            type: "system"
            description: "Approved By"
          data_type: "string"
          
        - id: "out031"
          slot_id: "e001.at110.out031"
          contextual_id: "go001.lo002.out031"
          source:
            type: "system"
            description: "Leave Type"
          data_type: "enum"
          
        - id: "out032"
          slot_id: "e001.at111.out032"
          contextual_id: "go001.lo002.out032"
          source:
            type: "system"
            description: "Leave Sub-Type"
          data_type: "string"

    data_mapping_stack:
      description: "Data handover between LOs"
      mappings: []

    execution_pathway:
      type: "terminal"
      next_lo: ""

  - id: "lo003"
    contextual_id: "go001.lo003"
    name: "HR Manager Approval"
    workflow_source: "intermediate"
    function_type: "Update"
    agent_stack:
      agents:
        - role: "r003"  # HR Manager role
          rights: ["Execute", "Update"]
          users: []  # Empty means all users with this role have these rights
        - role: ""  # No role specified, just specific users
          rights: ["Read"]
          users: ["<EMAIL>", "<EMAIL>"]  # These specific users can read regardless of role
    input_stack:
      description: "HR Manager reviews leave application"
      inputs:
        # Information policy message
        - id: "in032"
          slot_id: "e001.at999.in032"
          contextual_id: "go001.lo003.in032"
          source:
            type: "information"
            description: "Extended Leave Policy"
          required: false
          data_type: "string"
          ui_control: "oj-text"
          is_visible: true
          metadata:
            usage: ""
          validations: []
          nested_function:
            id: "nf008"
            function_name: "to_uppercase"
            function_type: "transform"
            parameters:
              text: "This leave request is for more than 3 days and requires HR approval. Please verify the employee's leave balance before approval."
            output_to: "info_text"
            
        # Leave ID for lookup
        - id: "in015"
          slot_id: "e001.at101.in015"
          contextual_id: "go001.lo003.in015"
          source:
            type: "system"
            description: "Leave ID"
          required: true
          data_type: "string"
          ui_control: "oj-input-text"
          metadata:
            usage: "lookup"
          validations:
            - rule: "Leave ID must be present"
              rule_type: "validate_required"
              validation_method: "validate_required"
              error_message: "Leave ID is required"
            - rule: "Leave ID must exist"
              rule_type: "entity_exists"
              validation_method: "entity_exists"
              entity: "e001"
              attribute: "at101"
              error_message: "Leave ID not found in the system"
              
        # Employee ID
        - id: "in016"
          slot_id: "e001.at102.in016"
          contextual_id: "go001.lo003.in016"
          source:
            type: "system"
            description: "Employee ID"
          required: true
          data_type: "string"
          ui_control: "oj-input-text"
          metadata:
            usage: "lookup"
          validations:
            - rule: "Employee ID is required"
              rule_type: "validate_required"
              validation_method: "validate_required"
              error_message: "Employee ID is required"
              
        # Start Date
        - id: "in017"
          slot_id: "e001.at103.in017"
          contextual_id: "go001.lo003.in017"
          source:
            type: "system"
            description: "Start Date"
          required: true
          data_type: "date"
          ui_control: "oj-input-date"
          metadata:
            usage: "lookup"
          validations:
            - rule: "Start Date is required"
              rule_type: "validate_required"
              validation_method: "validate_required"
              error_message: "Start Date is required"
              
        # End Date
        - id: "in018"
          slot_id: "e001.at104.in018"
          contextual_id: "go001.lo003.in018"
          source:
            type: "system"
            description: "End Date"
          required: true
          data_type: "date"
          ui_control: "oj-input-date"
          metadata:
            usage: "lookup"
          validations:
            - rule: "End Date is required"
              rule_type: "validate_required"
              validation_method: "validate_required"
              error_message: "End Date is required"
              
        # Number of Days
        - id: "in019"
          slot_id: "e001.at105.in019"
          contextual_id: "go001.lo003.in019"
          source:
            type: "system"
            description: "Number of Days"
          required: true
          data_type: "integer"
          ui_control: "oj-input-number"
          metadata:
            usage: "lookup"
          validations:
            - rule: "Number of Days is required"
              rule_type: "validate_required"
              validation_method: "validate_required"
              error_message: "Number of Days is required"
              
        # Leave Reason
        - id: "in020"
          slot_id: "e001.at106.in020"
          contextual_id: "go001.lo003.in020"
          source:
            type: "system"
            description: "Leave Reason"
          required: true
          data_type: "string"
          ui_control: "oj-text-area"
          metadata:
            usage: "lookup"
          validations:
            - rule: "Reason is required"
              rule_type: "validate_required"
              validation_method: "validate_required"
              error_message: "Leave reason is required"
              
        # Leave Type
        - id: "in030"
          slot_id: "e001.at110.in030"
          contextual_id: "go001.lo003.in030"
          source:
            type: "system"
            description: "Leave Type"
          required: true
          data_type: "enum"
          ui_control: "oj-input-text"
          metadata:
            usage: "lookup"
          validations:
            - rule: "Leave Type is required"
              rule_type: "validate_required"
              validation_method: "validate_required"
              error_message: "Leave Type is required"
              
        # Leave Sub-Type
        - id: "in031"
          slot_id: "e001.at111.in031"
          contextual_id: "go001.lo003.in031"
          source:
            type: "system"
            description: "Leave Sub-Type"
          required: false
          data_type: "string"
          ui_control: "oj-input-text"
          metadata:
            usage: "lookup"
          validations: []
              
        # Status to be updated
        - id: "in021"
          slot_id: "e001.at107.in021"
          contextual_id: "go001.lo003.in021"
          source:
            type: "user"
            description: "Status"
          required: true
          data_type: "enum"
          allowed_values: ["Pending", "Approved", "Rejected"]
          ui_control: "oj-select-single"
          metadata:
            usage: "update"
          validations:
            - rule: "Status must be valid"
              rule_type: "enum_check"
              validation_method: "enum_check"
              allowed_values: ["Pending", "Approved", "Rejected"]
              error_message: "Invalid status value"
              
        # Remarks (optional)
        - id: "in022"
          slot_id: "e001.at108.in022"
          contextual_id: "go001.lo003.in022"
          source:
            type: "user"
            description: "Remarks"
          required: false
          data_type: "string"
          ui_control: "oj-text-area"
          metadata:
            usage: "update"
          validations: []
          
        # Approved By
        - id: "in023"
          slot_id: "e001.at109.in023"
          contextual_id: "go001.lo003.in023"
          source:
            type: "system"
            description: "Approved By"
          required: false
          data_type: "string"
          ui_control: "oj-input-text"
          metadata:
            usage: "update"
          validations: []
          nested_function:
            id: "nf005"
            function_name: "current_timestamp"
            function_type: "utility"
            parameters: {}
            output_to: "at109"

    output_stack:
      description: "Leave application updated by HR manager"
      outputs:
        - id: "out019"
          slot_id: "executionstatus.out019"
          contextual_id: "go001.lo003.out019"
          source:
            type: "system"
            description: "Success or failure"
          data_type: "string"
          
        - id: "out020"
          slot_id: "e001.at101.out020"
          contextual_id: "go001.lo003.out020"
          source:
            type: "system"
            description: "Leave ID"
          data_type: "string"
          
        - id: "out021"
          slot_id: "e001.at102.out021"
          contextual_id: "go001.lo003.out021"
          source:
            type: "system"
            description: "Employee ID"
          data_type: "string"
          
        - id: "out022"
          slot_id: "e001.at103.out022"
          contextual_id: "go001.lo003.out022"
          source:
            type: "system"
            description: "Start Date"
          data_type: "date"
          
        - id: "out023"
          slot_id: "e001.at104.out023"
          contextual_id: "go001.lo003.out023"
          source:
            type: "system"
            description: "End Date"
          data_type: "date"
          
        - id: "out024"
          slot_id: "e001.at105.out024"
          contextual_id: "go001.lo003.out024"
          source:
            type: "system"
            description: "Number of Days"
          data_type: "integer"
          
        - id: "out025"
          slot_id: "e001.at106.out025"
          contextual_id: "go001.lo003.out025"
          source:
            type: "system"
            description: "Leave Reason"
          data_type: "string"
          
        - id: "out026"
          slot_id: "e001.at107.out026"
          contextual_id: "go001.lo003.out026"
          source:
            type: "system"
            description: "Status"
          data_type: "enum"
          
        - id: "out027"
          slot_id: "e001.at108.out027"
          contextual_id: "go001.lo003.out027"
          source:
            type: "system"
            description: "Remarks"
          data_type: "string"
          
        - id: "out028"
          slot_id: "e001.at109.out028"
          contextual_id: "go001.lo003.out028"
          source:
            type: "system"
            description: "Approved By"
          data_type: "string"
          
        - id: "out033"
          slot_id: "e001.at110.out033"
          contextual_id: "go001.lo003.out033"
          source:
            type: "system"
            description: "Leave Type"
          data_type: "enum"
          
        - id: "out034"
          slot_id: "e001.at111.out034"
          contextual_id: "go001.lo003.out034"
          source:
            type: "system"
            description: "Leave Sub-Type"
          data_type: "string"

    data_mapping_stack:
      description: "Data handover between LOs"
      mappings: []

    execution_pathway:
      type: "terminal"
      next_lo: ""
