-- Purchase Furniture Nested Functions Insert Script
-- Inserts nested functions for calculations and operations

SET search_path TO workflow_runtime;

-- =====================================================
-- NESTED FUNCTIONS
-- =====================================================
INSERT INTO lo_nested_functions (
    nested_function_id, lo_id, function_name, function_type, description, 
    input_parameters, output_parameters, calculation_logic, created_at, 
    updated_at, created_by, updated_by, version, natural_language
) VALUES 
('NF_CALCULATE_SUBTOTAL', 'GO2.LO2', 'Calculate Subtotal', 'CALCULATE', 'Multiply quantity by unit price', 
 '{"quantity": "integer", "unitprice": "decimal"}', 
 '{"subtotal": "decimal"}', 
 'quantity * unitprice', NOW(), NOW(), 'system', 'system', '1.0', 
 'Nested function to calculate subtotal by multiplying quantity and unit price'),
('NF_CALCULATE_GST', 'GO2.LO2', 'Calculate GST', 'CALCULATE', 'Calculate 18% GST on subtotal', 
 '{"subtotal": "decimal"}', 
 '{"gstamount": "decimal"}', 
 'subtotal * 0.18', NOW(), NOW(), 'system', 'system', '1.0', 
 'Nested function to calculate GST at 18% rate'),
('NF_CALCULATE_TOTAL', 'GO2.LO2', 'Calculate Total', 'CALCULATE', 'Add subtotal and GST amount', 
 '{"subtotal": "decimal", "gstamount": "decimal"}', 
 '{"totalamount": "decimal"}', 
 'subtotal + gstamount', NOW(), NOW(), 'system', 'system', '1.0', 
 'Nested function to calculate total amount including GST'),
('NF_GENERATE_ORDER_ID', 'GO2.LO3', 'Generate Order ID', 'GENERATE', 'Generate unique order identifier', 
 '{}', 
 '{"orderid": "string"}', 
 'CONCAT("ORD", YEAR(NOW()), MONTH(NOW()), DAY(NOW()), "_", UNIX_TIMESTAMP())', NOW(), NOW(), 'system', 'system', '1.0', 
 'Nested function to generate unique order ID'),
('NF_UPDATE_INVENTORY', 'GO2.LO3', 'Update Inventory', 'UPDATE', 'Reduce inventory by ordered quantity', 
 '{"productid": "string", "quantity": "integer"}', 
 '{"inventoryupdated": "boolean"}', 
 'UPDATE e14_furnitureproduct SET availableinventory = availableinventory - quantity WHERE productid = productid', NOW(), NOW(), 'system', 'system', '1.0', 
 'Nested function to update product inventory levels');

COMMIT;
