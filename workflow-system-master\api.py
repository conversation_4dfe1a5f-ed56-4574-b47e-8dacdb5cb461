import sys
import os
sys.path.append(os.getcwd())
import asyncio
from fastapi import FastAP<PERSON>
from fastapi.responses import StreamingResponse
from pydantic import BaseModel
import uvicorn
from enterprise_design_time.tests.anthropic_chatbot_test import ConversationalBDAStreaming

app = FastAPI()

# Initialize the chatbot
# NOTE: You should use a secure way to handle API keys, like environment variables
chatbot = ConversationalBDAStreaming(api_key="your_api_key_here")

class ChatRequest(BaseModel):
    message: str

async def stream_chat_response(message: str):
    """
    Generator function to stream the chatbot's response.
    """
    chatbot.add_user_message(message)

    try:
        # Create streaming response
        stream = chatbot.client.messages.create(
            model="claude-sonnet-4-20250514",
            max_tokens=60000,
            temperature=1,
            system=chatbot.system_prompt,
            messages=chatbot.messages,
            stream=True,
            thinking={
                "type": "enabled",
                "budget_tokens": 21542
            }
        )

        full_response = ""
        response_content = []

        # Process the stream
        for chunk in stream:
            if chunk.type == "content_block_delta":
                if chunk.delta.type == "text_delta":
                    text = chunk.delta.text
                    full_response += text
                    if response_content:
                        response_content[-1]["text"] += text
                    yield text
            elif chunk.type == "content_block_start":
                if chunk.content_block.type == "text":
                    response_content.append({"type": "text", "text": ""})
            elif chunk.type == "message_stop":
                chatbot.add_assistant_message(response_content)

    except Exception as e:
        yield f"Error: {str(e)}"


@app.post("/chat")
async def chat(request: ChatRequest):
    """
    API endpoint to handle chat requests and stream responses.
    """
    return StreamingResponse(stream_chat_response(request.message), media_type="text/event-stream")

if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=8000)