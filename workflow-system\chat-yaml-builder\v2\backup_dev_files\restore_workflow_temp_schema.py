#!/usr/bin/env python3
"""
Script to restore the workflow_temp schema.

This script:
1. Drops the workflow_temp schema if it exists
2. Creates a new workflow_temp schema
3. Copies all tables from workflow_runtime to workflow_temp
4. Adds audit columns to entity-related tables
5. Adds missing columns to tables

Usage:
    python restore_workflow_temp_schema.py
"""

import os
import sys
import logging
import psycopg2
from typing import Dict, List, Tuple, Any, Optional

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('restore_workflow_temp_schema.log')
    ]
)
logger = logging.getLogger('restore_workflow_temp_schema')

# Database connection parameters
DB_PARAMS = {
    "dbname": "workflow_system",
    "user": "postgres",
    "password": "workflow_postgres_secure_password",
    "host": "**********",
    "port": "5432"
}

def get_db_connection(schema_name: Optional[str] = None) -> Optional[psycopg2.extensions.connection]:
    """
    Get a database connection.
    
    Args:
        schema_name: Optional schema name to set as the search path
        
    Returns:
        Database connection object or None if connection failed
    """
    try:
        # Connect to the database
        conn = psycopg2.connect(**DB_PARAMS)
        
        # Set schema search path if provided
        if schema_name:
            with conn.cursor() as cursor:
                cursor.execute(f"SET search_path TO {schema_name}")
                conn.commit()
        
        return conn
    except Exception as e:
        logger.error(f"Error connecting to database: {str(e)}", exc_info=True)
        return None

def execute_query(query: str, params: Optional[Tuple] = None, schema_name: Optional[str] = None) -> Tuple[bool, List[str], Optional[List[Tuple]]]:
    """
    Execute a database query.
    
    Args:
        query: SQL query to execute
        params: Query parameters
        schema_name: Optional schema name to set as the search path
        
    Returns:
        Tuple containing:
            - Boolean indicating if query execution was successful
            - List of messages (warnings, errors, or success messages)
            - Query results (or None if query failed or no results)
    """
    messages = []
    conn = None
    
    try:
        # Get database connection
        conn = get_db_connection(schema_name)
        if not conn:
            messages.append("Failed to connect to database")
            return False, messages, None
        
        # Execute query
        with conn.cursor() as cursor:
            if params:
                cursor.execute(query, params)
            else:
                cursor.execute(query)
            
            # Check if query returns results
            if cursor.description:
                results = cursor.fetchall()
            else:
                results = None
            
            # Commit transaction
            conn.commit()
            
            messages.append("Query executed successfully")
            return True, messages, results
    except Exception as e:
        if conn:
            conn.rollback()
        
        error_msg = f"Error executing query: {str(e)}"
        logger.error(error_msg, exc_info=True)
        messages.append(error_msg)
        return False, messages, None
    finally:
        if conn:
            conn.close()

def check_column_exists(schema_name: str, table_name: str, column_name: str) -> bool:
    """
    Check if a column exists in a table.
    
    Args:
        schema_name: Schema name
        table_name: Table name
        column_name: Column name
        
    Returns:
        Boolean indicating if the column exists
    """
    try:
        success, query_messages, result = execute_query(
            """
            SELECT EXISTS (
                SELECT FROM information_schema.columns 
                WHERE table_schema = %s
                AND table_name = %s
                AND column_name = %s
            )
            """,
            (schema_name, table_name, column_name)
        )
        
        if not success:
            logger.error(f"Error checking if column {column_name} exists: {query_messages}")
            return False
        
        return result and result[0][0]
    except Exception as e:
        logger.error(f"Error checking if column {column_name} exists: {str(e)}")
        return False

def recreate_temp_schema() -> bool:
    """
    Recreate the workflow_temp schema.
    
    Returns:
        Boolean indicating if recreation was successful
    """
    logger.info("Recreating workflow_temp schema")
    
    # Drop the workflow_temp schema
    drop_query = """
        DROP SCHEMA IF EXISTS workflow_temp CASCADE
    """
    
    success, messages, _ = execute_query(drop_query)
    
    if not success:
        logger.error(f"Failed to drop schema workflow_temp: {messages}")
        return False
    
    logger.info("Dropped schema workflow_temp")
    
    # Create the workflow_temp schema
    create_query = """
        CREATE SCHEMA workflow_temp
    """
    
    success, messages, _ = execute_query(create_query)
    
    if not success:
        logger.error(f"Failed to create schema workflow_temp: {messages}")
        return False
    
    logger.info("Created schema workflow_temp")
    
    # Copy all tables from workflow_runtime to workflow_temp
    copy_query = """
        SELECT table_name
        FROM information_schema.tables
        WHERE table_schema = 'workflow_runtime'
        AND table_type = 'BASE TABLE'
    """
    
    success, messages, result = execute_query(copy_query)
    
    if not success:
        logger.error(f"Failed to get tables from workflow_runtime: {messages}")
        return False
    
    tables = [row[0] for row in result]
    logger.info(f"Found {len(tables)} tables in workflow_runtime")
    
    for table in tables:
        # Get the table definition
        table_def_query = f"""
            SELECT column_name, data_type, column_default, is_nullable
            FROM information_schema.columns
            WHERE table_schema = 'workflow_runtime'
            AND table_name = '{table}'
            ORDER BY ordinal_position
        """
        
        success, messages, columns = execute_query(table_def_query)
        
        if not success:
            logger.error(f"Failed to get columns for table {table}: {messages}")
            continue
        
        # Create the table in workflow_temp
        column_defs = []
        for column in columns:
            column_name = column[0]
            data_type = column[1]
            default_value = column[2]
            is_nullable = column[3]
            
            column_def = f"{column_name} {data_type}"
            
            if default_value:
                column_def += f" DEFAULT {default_value}"
            
            if is_nullable == 'NO':
                column_def += " NOT NULL"
            
            column_defs.append(column_def)
        
        create_table_query = f"""
            CREATE TABLE workflow_temp.{table} (
                {', '.join(column_defs)}
            )
        """
        
        success, messages, _ = execute_query(create_table_query)
        
        if not success:
            logger.error(f"Failed to create table workflow_temp.{table}: {messages}")
            continue
        
        logger.info(f"Created table workflow_temp.{table}")
    
    logger.info("Successfully recreated workflow_temp schema")
    return True

def add_audit_columns(schema_name: str, table_name: str) -> Tuple[bool, List[str]]:
    """
    Add audit columns to a table.
    
    Args:
        schema_name: Schema name
        table_name: Table name
        
    Returns:
        Tuple containing:
            - Boolean indicating if addition was successful
            - List of messages (warnings, errors, or success messages)
    """
    messages = []
    
    try:
        # Check if table exists
        success, query_messages, result = execute_query(
            f"""
            SELECT EXISTS (
                SELECT FROM information_schema.tables 
                WHERE table_schema = %s
                AND table_name = %s
            )
            """,
            (schema_name, table_name)
        )
        
        if not success:
            messages.extend(query_messages)
            return False, messages
        
        table_exists = result and result[0][0]
        
        if not table_exists:
            messages.append(f"Table {schema_name}.{table_name} does not exist")
            logger.warning(f"Table {schema_name}.{table_name} does not exist")
            return False, messages
        
        # Check for audit columns
        audit_columns = [
            ("created_at", "TIMESTAMP", "CURRENT_TIMESTAMP"),
            ("created_by", "VARCHAR(100)", "'system'"),
            ("updated_at", "TIMESTAMP", "CURRENT_TIMESTAMP"),
            ("updated_by", "VARCHAR(100)", "'system'")
        ]
        
        for col_name, col_type, col_default in audit_columns:
            if not check_column_exists(schema_name, table_name, col_name):
                # Add audit column
                query = f"""
                    ALTER TABLE {schema_name}.{table_name}
                    ADD COLUMN {col_name} {col_type} DEFAULT {col_default}
                """
                
                success, query_messages, _ = execute_query(query)
                
                if not success:
                    messages.extend(query_messages)
                    logger.error(f"Failed to add column '{col_name}' to table {schema_name}.{table_name}: {query_messages}")
                    continue  # Continue with other columns instead of failing
                
                messages.append(f"Added audit column '{col_name}' to table {schema_name}.{table_name}")
                logger.info(f"Added audit column '{col_name}' to table {schema_name}.{table_name}")
            else:
                messages.append(f"Audit column '{col_name}' already exists in table {schema_name}.{table_name}")
                logger.info(f"Audit column '{col_name}' already exists in table {schema_name}.{table_name}")
        
        return True, messages
    except Exception as e:
        error_msg = f"Error adding audit columns to table {schema_name}.{table_name}: {str(e)}"
        logger.error(error_msg, exc_info=True)
        messages.append(error_msg)
        return False, messages

def add_id_column(schema_name: str, table_name: str) -> Tuple[bool, List[str]]:
    """
    Add an ID column to a table.
    
    Args:
        schema_name: Schema name
        table_name: Table name
        
    Returns:
        Tuple containing:
            - Boolean indicating if addition was successful
            - List of messages (warnings, errors, or success messages)
    """
    messages = []
    
    try:
        # Check if table exists
        success, query_messages, result = execute_query(
            f"""
            SELECT EXISTS (
                SELECT FROM information_schema.tables 
                WHERE table_schema = %s
                AND table_name = %s
            )
            """,
            (schema_name, table_name)
        )
        
        if not success:
            messages.extend(query_messages)
            return False, messages
        
        table_exists = result and result[0][0]
        
        if not table_exists:
            messages.append(f"Table {schema_name}.{table_name} does not exist")
            logger.warning(f"Table {schema_name}.{table_name} does not exist")
            return False, messages
        
        # Check if ID column exists
        if not check_column_exists(schema_name, table_name, "id"):
            # Check if table has a primary key
            success, query_messages, result = execute_query(
                f"""
                SELECT kcu.column_name
                FROM information_schema.table_constraints tc
                JOIN information_schema.key_column_usage kcu
                ON tc.constraint_name = kcu.constraint_name
                AND tc.table_schema = kcu.table_schema
                WHERE tc.constraint_type = 'PRIMARY KEY'
                AND tc.table_schema = %s
                AND tc.table_name = %s
                """,
                (schema_name, table_name)
            )
            
            if not success:
                messages.extend(query_messages)
                return False, messages
            
            has_pk = result and len(result) > 0
            
            if has_pk:
                messages.append(f"Table {schema_name}.{table_name} already has a primary key: {result[0][0]}")
                logger.info(f"Table {schema_name}.{table_name} already has a primary key: {result[0][0]}")
                return True, messages
            
            # Add ID column
            query = f"""
                ALTER TABLE {schema_name}.{table_name}
                ADD COLUMN id SERIAL PRIMARY KEY
            """
            
            success, query_messages, _ = execute_query(query)
            
            if not success:
                messages.extend(query_messages)
                logger.error(f"Failed to add ID column to table {schema_name}.{table_name}: {query_messages}")
                return False, messages
            
            messages.append(f"Added ID column to table {schema_name}.{table_name}")
            logger.info(f"Added ID column to table {schema_name}.{table_name}")
        else:
            messages.append(f"ID column already exists in table {schema_name}.{table_name}")
            logger.info(f"ID column already exists in table {schema_name}.{table_name}")
        
        return True, messages
    except Exception as e:
        error_msg = f"Error adding ID column to table {schema_name}.{table_name}: {str(e)}"
        logger.error(error_msg, exc_info=True)
        messages.append(error_msg)
        return False, messages

def fix_global_objectives_table(schema_name: str) -> bool:
    """
    Fix the global_objectives table.
    
    Args:
        schema_name: Schema name
        
    Returns:
        Boolean indicating if the fix was successful
    """
    logger.info(f"Fixing {schema_name}.global_objectives table")
    
    # Check if the table exists
    success, messages, result = execute_query(
        f"""
        SELECT EXISTS (
            SELECT FROM information_schema.tables
            WHERE table_schema = %s
            AND table_name = 'global_objectives'
        )
        """,
        (schema_name,)
    )
    
    if not success:
        logger.error(f"Failed to check if {schema_name}.global_objectives exists: {messages}")
        return False
    
    table_exists = result and result[0][0]
    
    if not table_exists:
        logger.warning(f"Table {schema_name}.global_objectives does not exist")
        return False
    
    # Add missing columns to global_objectives table
    columns_to_add = [
        ("process_mining_schema", "JSONB", None),
        ("performance_metadata", "JSONB", None),
        ("version_type", "VARCHAR(10)", "'v2'"),
        ("status", "VARCHAR(20)", "'active'"),
        ("tenant_id", "VARCHAR(50)", "'t001'"),
        ("deleted_mark", "BOOLEAN", "false")
    ]
    
    for column_name, column_type, default_value in columns_to_add:
        # Check if the column exists
        column_exists = check_column_exists(schema_name, "global_objectives", column_name)
        
        if not column_exists:
            # Add the column
            if default_value:
                query = f"""
                    ALTER TABLE {schema_name}.global_objectives
                    ADD COLUMN {column_name} {column_type} DEFAULT {default_value}
                """
            else:
                query = f"""
                    ALTER TABLE {schema_name}.global_objectives
                    ADD COLUMN {column_name} {column_type}
                """
            
            success, messages, _ = execute_query(query)
            
            if not success:
                logger.error(f"Failed to add column {column_name} to {schema_name}.global_objectives: {messages}")
                continue
            
            logger.info(f"Added column {column_name} to {schema_name}.global_objectives")
        else:
            logger.info(f"Column {column_name} already exists in {schema_name}.global_objectives")
    
    return True

def fix_entities_table(schema_name: str) -> bool:
    """
    Fix the entities table.
    
    Args:
        schema_name: Schema name
        
    Returns:
        Boolean indicating if the fix was successful
    """
    logger.info(f"Fixing {schema_name}.entities table")
    
    # Check if the table exists
    success, messages, result = execute_query(
        f"""
        SELECT EXISTS (
            SELECT FROM information_schema.tables
            WHERE table_schema = %s
            AND table_name = 'entities'
        )
        """,
        (schema_name,)
    )
    
    if not success:
        logger.error(f"Failed to check if {schema_name}.entities exists: {messages}")
        return False
    
    table_exists = result and result[0][0]
    
    if not table_exists:
        logger.warning(f"Table {schema_name}.entities does not exist")
        return False
    
    # Add missing columns to entities table
    columns_to_add = [
        ("metadata", "JSONB", None),
        ("lifecycle_management", "JSONB", None),
        ("version_type", "VARCHAR(10)", "'v2'"),
        ("status", "VARCHAR(20)", "'active'"),
        ("type", "VARCHAR(50)", "'standard'"),
        ("attribute_prefix", "VARCHAR(50)", "''")
    ]
    
    for column_name, column_type, default_value in columns_to_add:
        # Check if the column exists
        column_exists = check_column_exists(schema_name, "entities", column_name)
        
        if not column_exists:
            # Add the column
            if default_value:
                query = f"""
                    ALTER TABLE {schema_name}.entities
                    ADD COLUMN {column_name} {column_type} DEFAULT {default_value}
                """
            else:
                query = f"""
                    ALTER TABLE {schema_name}.entities
                    ADD COLUMN {column_name} {column_type}
                """
            
            success, messages, _ = execute_query(query)
            
            if not success:
                logger.error(f"Failed to add column {column_name} to {schema_name}.entities: {messages}")
                continue
            
            logger.info(f"Added column {column_name} to {schema_name}.entities")
        else:
            logger.info(f"Column {column_name} already exists in {schema_name}.entities")
    
    return True

def fix_entity_attributes_table(schema_name: str) -> bool:
    """
    Fix the entity_attributes table.
    
    Args:
        schema_name: Schema name
        
    Returns:
        Boolean indicating if the fix was successful
    """
    logger.info(f"Fixing {schema_name}.entity_attributes table")
    
    # Check if the table exists
    success, messages, result = execute_query(
        f"""
        SELECT EXISTS (
            SELECT FROM information_schema.tables
            WHERE table_schema = %s
            AND table_name = 'entity_attributes'
        )
        """,
        (schema_name,)
    )
    
    if not success:
        logger.error(f"Failed to check if {schema_name}.entity_attributes exists: {messages}")
        return False
    
    table_exists = result and result[0][0]
    
    if not table_exists:
        logger.warning(f"Table {schema_name}.entity_attributes does not exist")
        return False
    
    # Add missing columns to entity_attributes table
    columns_to_add = [
        ("type", "VARCHAR(50)", "'string'"),
        ("default_value", "TEXT", None),
        ("calculated_field", "BOOLEAN", "false"),
        ("calculation_formula", "TEXT", None),
        ("dependencies", "JSONB", None),
        ("display_name", "VARCHAR(100)", None),
        ("datatype", "VARCHAR(50)", "'string'"),
        ("status", "VARCHAR(20)", "'active'")
    ]
    
    for column_name, column_type, default_value in columns_to_add:
        # Check if the column exists
        column_exists = check_column_exists(schema_name, "entity_attributes", column_name)
        
        if not column_exists:
            # Add the column
            if default_value:
                query = f"""
                    ALTER TABLE {schema_name}.entity_attributes
                    ADD COLUMN {column_name} {column_type} DEFAULT {default_value}
                """
            else:
                query = f"""
                    ALTER TABLE {schema_name}.entity_attributes
                    ADD COLUMN {column_name} {column_type}
                """
            
            success, messages, _ = execute_query(query)
            
            if not success:
                logger.error(f"Failed to add column {column_name} to {schema_name}.entity_attributes: {messages}")
                continue
            
            logger.info(f"Added column {column_name} to {schema_name}.entity_attributes")
        else:
            logger.info(f"Column {column_name} already exists in {schema_name}.entity_attributes")
    
    return True

def fix_local_objectives_table(schema_name: str) -> bool:
    """
    Fix the local_objectives table.
    
    Args:
        schema_name: Schema name
        
    Returns:
        Boolean indicating if the fix was successful
    """
    logger.info(f"Fixing {schema_name}.local_objectives table")
    
    # Check if the table exists
    success, messages, result = execute_query(
        f"""
        SELECT EXISTS (
            SELECT FROM information_schema.tables
            WHERE table_schema = %s
            AND table_name = 'local_objectives'
        )
        """,
        (schema_name,)
    )
    
    if not success:
        logger.error(f"Failed to check if {schema_name}.local_objectives exists: {messages}")
        return False
    
    table_exists = result and result[0][0]
    
    if not table_exists:
        logger.warning(f"Table {schema_name}.local_objectives does not exist")
        return False
    
    # Add missing columns to local_objectives table
    columns_to_add = [
        ("description", "TEXT", None),
        ("ui_stack", "JSONB", None),
        ("mapping_stack", "JSONB", None),
        ("version_type", "VARCHAR(10)", "'v2'")
    ]
    
    for column_name, column_type, default_value in columns_to_add:
        # Check if the column exists
        column_exists = check_column_exists(schema_name, "local_objectives", column_name)
        
        if not column_exists:
            # Add the column
            if default_value:
                query = f"""
                    ALTER TABLE {schema_name}.local_objectives
                    ADD COLUMN {column_name} {column_type} DEFAULT {default_value}
                """
            else:
                query = f"""
                    ALTER TABLE {schema_name}.local_objectives
                    ADD COLUMN {column_name} {column_type}
                """
            
            success, messages, _ = execute_query(query)
            
            if not success:
                logger.error(f"Failed to add column {column_name} to {schema_name}.local_objectives: {messages}")
                continue
            
            logger.info(f"Added column {column_name} to {schema_name}.local_objectives")
        else:
            logger.info(f"Column {column_name} already exists in {schema_name}.local_objectives")
    
    return True

def fix_roles_table(schema_name: str) -> bool:
    """
    Fix the roles table.
    
    Args:
        schema_name: Schema name
        
    Returns:
        Boolean indicating if the fix was successful
    """
    logger.info(f"Fixing {schema_name}.roles table")
    
    # Check if the table exists
    success, messages, result = execute_query(
        f"""
        SELECT EXISTS (
            SELECT FROM information_schema.tables
            WHERE table_schema = %s
            AND table_name = 'roles'
        )
        """,
        (schema_name,)
    )
    
    if not success:
        logger.error(f"Failed to check if {schema_name}.roles exists: {messages}")
        return False
    
    table_exists = result and result[0][0]
    
    if not table_exists:
        logger.warning(f"Table {schema_name}.roles does not exist")
        return False
    
    # Add missing columns to roles table
    columns_to_add = [
        ("version_type", "VARCHAR(10)", "'v2'"),
        ("tenant_id", "VARCHAR(50)", "'t001'")
    ]
    
    for column_name, column_type, default_value in columns_to_add:
        # Check if the column exists
        column_exists = check_column_exists(schema_name, "roles", column_name)
        
        if not column_exists:
            # Add the column
            if default_value:
                query = f"""
                    ALTER TABLE {schema_name}.roles
                    ADD COLUMN {column_name} {column_type} DEFAULT {default_value}
                """
            else:
                query = f"""
                    ALTER TABLE {schema_name}.roles
                    ADD COLUMN {column_name} {column_type}
                """
            
            success, messages, _ = execute_query(query)
            
            if not success:
                logger.error(f"Failed to add column {column_name} to {schema_name}.roles: {messages}")
                continue
            
            logger.info(f"Added column {column_name} to {schema_name}.roles")
        else:
            logger.info(f"Column {column_name} already exists in {schema_name}.roles")
    
    return True

def create_missing_tables(schema_name: str) -> bool:
    """
    Create missing tables in the schema.
    
    Args:
        schema_name: Schema name
        
    Returns:
        Boolean indicating if creation was successful
    """
    logger.info(f"Creating missing tables in {schema_name} schema")
    
    # Create go_lo_mapping table if it doesn't exist
    success, messages, result = execute_query(
        f"""
        SELECT EXISTS (
            SELECT FROM information_schema.tables
            WHERE table_schema = %s
            AND table_name = 'go_lo_mapping'
        )
        """,
        (schema_name,)
    )
    
    if not success:
        logger.error(f"Failed to check if {schema_name}.go_lo_mapping exists: {messages}")
        return False
    
    table_exists = result and result[0][0]
    
    if not table_exists:
        query = f"""
            CREATE TABLE {schema_name}.go_lo_mapping (
                mapping_id VARCHAR(100) PRIMARY KEY,
                go_id VARCHAR(50) REFERENCES {schema_name}.global_objectives(go_id),
                lo_id VARCHAR(50) REFERENCES {schema_name}.local_objectives(lo_id),
                sequence_number INTEGER DEFAULT 0
            )
        """
        
        success, messages, _ = execute_query(query)
        
        if not success:
            logger.error(f"Failed to create table {schema_name}.go_lo_mapping: {messages}")
            return False
        
        logger.info(f"Created table {schema_name}.go_lo_mapping")
    
    # Create go_performance_metrics table if it doesn't exist
    success, messages, result = execute_query(
        f"""
        SELECT EXISTS (
            SELECT FROM information_schema.tables
            WHERE table_schema = %s
            AND table_name = 'go_performance_metrics'
        )
        """,
        (schema_name,)
    )
    
    if not success:
        logger.error(f"Failed to check if {schema_name}.go_performance_metrics exists: {messages}")
        return False
    
    table_exists = result and result[0][0]
    
    if not table_exists:
        query = f"""
            CREATE TABLE {schema_name}.go_performance_metrics (
                metric_id VARCHAR(100) PRIMARY KEY,
                go_id VARCHAR(50) REFERENCES {schema_name}.global_objectives(go_id),
                name VARCHAR(100) NOT NULL,
                description TEXT,
                value TEXT,
                unit VARCHAR(50)
            )
        """
        
        success, messages, _ = execute_query(query)
        
        if not success:
            logger.error(f"Failed to create table {schema_name}.go_performance_metrics: {messages}")
            return False
        
        logger.info(f"Created table {schema_name}.go_performance_metrics")
    
    # Create execution_pathways table if it doesn't exist
    success, messages, result = execute_query(
        f"""
        SELECT EXISTS (
            SELECT FROM information_schema.tables
            WHERE table_schema = %s
            AND table_name = 'execution_pathways'
        )
        """,
        (schema_name,)
    )
    
    if not success:
        logger.error(f"Failed to check if {schema_name}.execution_pathways exists: {messages}")
        return False
    
    table_exists = result and result[0][0]
    
    if not table_exists:
        query = f"""
            CREATE TABLE {schema_name}.execution_pathways (
                pathway_id VARCHAR(100) PRIMARY KEY,
                go_id VARCHAR(50) REFERENCES {schema_name}.global_objectives(go_id),
                source_lo_id VARCHAR(50) REFERENCES {schema_name}.local_objectives(lo_id),
                target_lo_id VARCHAR(50) REFERENCES {schema_name}.local_objectives(lo_id),
                pathway_type VARCHAR(50) NOT NULL,
                description TEXT
            )
        """
        
        success, messages, _ = execute_query(query)
        
        if not success:
            logger.error(f"Failed to create table {schema_name}.execution_pathways: {messages}")
            return False
        
        logger.info(f"Created table {schema_name}.execution_pathways")
    
    # Create execution_pathway_conditions table if it doesn't exist
    success, messages, result = execute_query(
        f"""
        SELECT EXISTS (
            SELECT FROM information_schema.tables
            WHERE table_schema = %s
            AND table_name = 'execution_pathway_conditions'
        )
        """,
        (schema_name,)
    )
    
    if not success:
        logger.error(f"Failed to check if {schema_name}.execution_pathway_conditions exists: {messages}")
        return False
    
    table_exists = result and result[0][0]
    
    if not table_exists:
        query = f"""
            CREATE TABLE {schema_name}.execution_pathway_conditions (
                condition_id VARCHAR(100) PRIMARY KEY,
                pathway_id VARCHAR(100) REFERENCES {schema_name}.execution_pathways(pathway_id),
                condition_type VARCHAR(50) NOT NULL,
                condition_value TEXT NOT NULL,
                description TEXT
            )
        """
        
        success, messages, _ = execute_query(query)
        
        if not success:
            logger.error(f"Failed to create table {schema_name}.execution_pathway_conditions: {messages}")
            return False
        
        logger.info(f"Created table {schema_name}.execution_pathway_conditions")
    
    logger.info("Successfully created missing tables")
    return True

def main():
    """
    Main function.
    """
    schema_name = "workflow_temp"
    
    # Step 1: Recreate the workflow_temp schema
    logger.info("Step 1: Recreating workflow_temp schema")
    success = recreate_temp_schema()
    
    if not success:
        logger.error("Failed to recreate workflow_temp schema")
        return
    
    # Step 2: Add audit columns to entity-related tables
    logger.info("Step 2: Adding audit columns to entity-related tables")
    entity_tables = [
        "entities",
        "entity_attributes",
        "entity_attribute_metadata",
        "entity_business_rules",
        "entity_relationships"
    ]
    
    for table in entity_tables:
        logger.info(f"Adding audit columns to table {schema_name}.{table}")
        success, messages = add_audit_columns(schema_name, table)
        
        for message in messages:
            logger.info(message)
        
        if not success:
            logger.error(f"Failed to add audit columns to table {schema_name}.{table}")
    
    # Step 3: Add ID columns to tables that don't have a primary key
    logger.info("Step 3: Adding ID columns to tables that don't have a primary key")
    for table in entity_tables:
        logger.info(f"Adding ID column to table {schema_name}.{table}")
        success, messages = add_id_column(schema_name, table)
        
        for message in messages:
            logger.info(message)
        
        if not success:
            logger.error(f"Failed to add ID column to table {schema_name}.{table}")
    
    # Step 4: Fix tables with missing columns
    logger.info("Step 4: Fixing tables with missing columns")
    fix_global_objectives_table(schema_name)
    fix_entities_table(schema_name)
    fix_entity_attributes_table(schema_name)
    fix_local_objectives_table(schema_name)
    fix_roles_table(schema_name)
    
    # Step 5: Create missing tables
    logger.info("Step 5: Creating missing tables")
    create_missing_tables(schema_name)
    
    logger.info("Successfully restored workflow_temp schema")

if __name__ == "__main__":
    main()
