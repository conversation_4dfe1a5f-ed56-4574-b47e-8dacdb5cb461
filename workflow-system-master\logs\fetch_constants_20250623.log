{"timestamp": "2025-06-23T05:07:17.381489", "endpoint": "fetch/constants", "input": {}, "output": {"success": true, "postgres_constants": [{"constant_id": 1001, "attribute": "gst_percentage", "value": "18", "description": "GST rate for furniture purchases", "tenant_id": "t001", "allow_override": false, "override_permissions": null, "created_at": "2025-06-22T11:10:48.589194", "updated_at": "2025-06-22T11:40:40.898347", "created_by": "system", "updated_by": "system", "status": "Active", "entity_name": "FurnitureOrder", "attribute_name": "gstamount", "auto_id": 1001}, {"constant_id": 1002, "attribute": "allowed_methods", "value": "[\"UPI\", \"Credit Card\"]", "description": "Allowed payment methods for furniture orders", "tenant_id": "t001", "allow_override": false, "override_permissions": null, "created_at": "2025-06-22T11:10:48.589194", "updated_at": "2025-06-22T11:40:40.898347", "created_by": "system", "updated_by": "system", "status": "Active", "entity_name": "FurnitureOrder", "attribute_name": "paymentmethod", "auto_id": 1002}], "mongo_drafts": [], "total_postgres": 2, "total_drafts": 0, "operation": "fetch"}, "status": "success"}
{"timestamp": "2025-06-23T08:23:12.293875", "endpoint": "fetch/constants", "input": {}, "output": {"success": true, "postgres_constants": [{"constant_id": 1001, "attribute": "gst_percentage", "value": "18", "description": "GST rate for furniture purchases", "tenant_id": "t001", "allow_override": false, "override_permissions": null, "created_at": "2025-06-22T11:10:48.589194", "updated_at": "2025-06-22T11:40:40.898347", "created_by": "system", "updated_by": "system", "status": "Active", "entity_name": "FurnitureOrder", "attribute_name": "gstamount", "auto_id": 1001}, {"constant_id": 1002, "attribute": "allowed_methods", "value": "[\"UPI\", \"Credit Card\"]", "description": "Allowed payment methods for furniture orders", "tenant_id": "t001", "allow_override": false, "override_permissions": null, "created_at": "2025-06-22T11:10:48.589194", "updated_at": "2025-06-22T11:40:40.898347", "created_by": "system", "updated_by": "system", "status": "Active", "entity_name": "FurnitureOrder", "attribute_name": "paymentmethod", "auto_id": 1002}], "mongo_drafts": [{"_id": "6858f30f1195bbcbf7aa7691", "constant_id": 1003, "attribute": "MAX_LEAVE_DAYS_ANNUAL", "value": "25", "description": "Maximum number of annual leave days per year", "tenant_id": "T1007", "allow_override": false, "override_permissions": "", "status": "draft", "entity_name": "", "attribute_name": "", "created_at": "2025-06-23T06:24:15.926637", "updated_at": "2025-06-23T06:24:15.926647", "created_by": "Tarun", "updated_by": "Tarun", "constant_status": "new", "changes_detected": [], "version": 1}], "total_postgres": 2, "total_drafts": 1, "operation": "fetch"}, "status": "success"}
{"timestamp": "2025-06-23T14:08:01.317207", "endpoint": "fetch/constants", "input": {}, "output": {"success": true, "postgres_constants": [{"constant_id": 1001, "attribute": "gst_percentage", "value": "18", "description": "GST rate for furniture purchases", "tenant_id": "t001", "allow_override": false, "override_permissions": null, "created_at": "2025-06-22T11:10:48.589194", "updated_at": "2025-06-22T11:40:40.898347", "created_by": "system", "updated_by": "system", "status": "Active", "entity_name": "FurnitureOrder", "attribute_name": "gstamount", "auto_id": 1001}, {"constant_id": 1002, "attribute": "allowed_methods", "value": "[\"UPI\", \"Credit Card\"]", "description": "Allowed payment methods for furniture orders", "tenant_id": "t001", "allow_override": false, "override_permissions": null, "created_at": "2025-06-22T11:10:48.589194", "updated_at": "2025-06-22T11:40:40.898347", "created_by": "system", "updated_by": "system", "status": "Active", "entity_name": "FurnitureOrder", "attribute_name": "paymentmethod", "auto_id": 1002}], "mongo_drafts": [{"_id": "6858f30f1195bbcbf7aa7691", "constant_id": 1004, "attribute": "allow_override", "value": "false", "description": "Whether this constant can be overridden", "tenant_id": "T101", "allow_override": false, "override_permissions": "", "status": "deployed_to_temp", "entity_name": "", "attribute_name": "", "created_at": "2025-06-23T09:02:49.471376", "updated_at": "2025-06-23T14:03:51.090866", "created_by": "Tarun", "updated_by": "Tarun", "constant_status": "new", "changes_detected": [], "version": 2}], "total_postgres": 2, "total_drafts": 1, "operation": "fetch"}, "status": "success"}
