#!/usr/bin/env python3
"""
Comprehensive entity relationship test script.

This script handles all aspects of testing entity relationship parsing and deployment:
1. Locates all necessary files in the project
2. Imports modules correctly regardless of location
3. Tests entity parsing
4. Tests entity deployment if possible
5. Provides detailed logs and error information

Usage:
  python test_entity_relationships.py [--schema SCHEMA] [--sample-file FILE] [--parse-only]

Options:
  --schema SCHEMA       Database schema to use (default: test_schema)
  --sample-file FILE    Path to sample entity file (default: auto-detect)
  --parse-only          Only test entity parsing, skip deployment
"""

import os
import sys
import argparse
import logging
import importlib.util
import traceback
from typing import Dict, List, Tuple, Any, Optional
import time
import json

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('entity_relationship_test.log')
    ]
)
logger = logging.getLogger('entity_relationship_test')

# Hardcoded sample text to use if file not found
SAMPLE_TEXT = """
Employee has employeeId^PK, firstName, lastName, email, phoneNumber, departmentId^FK, managerId^FK.

* Employee has many-to-one relationship with Department using Employee.departmentId to Department.departmentId^PK
* Employee has many-to-one relationship with Employee using Employee.managerId to Employee.employeeId^PK

Department has departmentId^PK, name, location.
"""

def find_file(filename: str, search_dir: Optional[str] = None) -> Optional[str]:
    """
    Find a file in the directory or its subdirectories.
    
    Args:
        filename: Name of the file to find
        search_dir: Directory to search in (default: script's parent directory)
        
    Returns:
        Full path to the file if found, None otherwise
    """
    if search_dir is None:
        # Use script's parent directory as default search directory
        search_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
    
    logger.info(f"Searching for {filename} in {search_dir}")
    
    for root, _, files in os.walk(search_dir):
        if filename in files:
            found_path = os.path.join(root, filename)
            logger.info(f"Found {filename} at: {found_path}")
            return found_path
    
    logger.warning(f"Could not find {filename}")
    return None

def import_module_from_file(module_name: str, file_path: str) -> Any:
    """
    Import a module directly from a file path.
    
    Args:
        module_name: Name to give the imported module
        file_path: Path to the module file
        
    Returns:
        Imported module object or None if import failed
    """
    try:
        logger.info(f"Importing {module_name} from {file_path}")
        
        # Check if file exists
        if not os.path.exists(file_path):
            logger.error(f"File {file_path} does not exist")
            return None
        
        # Load module from file
        spec = importlib.util.spec_from_file_location(module_name, file_path)
        if spec is None:
            logger.error(f"Could not load spec for {module_name} from {file_path}")
            return None
        
        module = importlib.util.module_from_spec(spec)
        if module is None:
            logger.error(f"Could not create module from spec for {module_name}")
            return None
        
        try:
            spec.loader.exec_module(module)
        except Exception as e:
            logger.error(f"Error executing module {module_name}: {str(e)}")
            logger.error(traceback.format_exc())
            return None
        
        sys.modules[module_name] = module
        logger.info(f"Successfully imported {module_name}")
        return module
    except Exception as e:
        logger.error(f"Error importing {module_name} from {file_path}: {str(e)}")
        logger.error(traceback.format_exc())
        return None

def fix_imports_in_deployer(deployer_path: str) -> bool:
    """
    Fix imports in entity_deployer_v2.py file.
    
    Args:
        deployer_path: Path to the entity_deployer_v2.py file
        
    Returns:
        Boolean indicating if the fix was successful
    """
    try:
        logger.info(f"Checking imports in {deployer_path}")
        
        with open(deployer_path, 'r') as f:
            content = f.read()
        
        # Check if typing imports are missing
        import_matches = [
            "from typing import Dict",
            "from typing import Dict, List",
            "from typing import Dict, List, Tuple",
            "import typing"
        ]
        
        needs_import = not any(imp in content for imp in import_matches)
        
        if needs_import:
            logger.info("Adding missing imports to deployer file")
            
            # Add import statement if not present
            new_content = """import os
import sys
import json
import logging
import re
from typing import Dict, List, Tuple, Any, Optional

""" + content
            
            # Create backup of original file
            backup_path = deployer_path + '.bak'
            with open(backup_path, 'w') as f:
                f.write(content)
            
            # Write updated content
            with open(deployer_path, 'w') as f:
                f.write(new_content)
                
            logger.info(f"Fixed imports in {deployer_path}")
            logger.info(f"Original file backed up at {backup_path}")
        else:
            logger.info(f"Imports already present in {deployer_path}")
        
        # Check if deploy_entities function exists
        if 'def deploy_entities' not in content:
            logger.warning("deploy_entities function not found in deployer file")
            
            # We need to add the deploy_entities function
            # First, check if the file already has a backup
            backup_path = deployer_path + '.bak'
            if not os.path.exists(backup_path):
                # Create backup of original file
                with open(backup_path, 'w') as f:
                    f.write(content)
                logger.info(f"Original file backed up at {backup_path}")
            
            # Read content again in case it was updated earlier
            with open(deployer_path, 'r') as f:
                content = f.read()
            
            # Add deploy_entities function
            new_content = content + """

def deploy_entities(entities_data: Dict, schema_name: str) -> Tuple[bool, List[str]]:
    \"\"\"
    Deploy entities to the database.
    
    Args:
        entities_data: Parsed YAML data for entities
        schema_name: Schema name to deploy to
        
    Returns:
        Tuple containing:
            - Boolean indicating if deployment was successful
            - List of messages (warnings, errors, or success messages)
    \"\"\"
    messages = []
    logger.info(f"Deploying entities to {schema_name}")
    
    try:
        # Check if entities key exists
        if 'entities' not in entities_data:
            logger.error("Missing 'entities' key in entities definition")
            return False, ["Missing 'entities' key in entities definition"]
        
        # First, validate all relationships to ensure both source and target entities exist
        validation_success, validation_messages = validate_entity_relationships(entities_data, schema_name)
        messages.extend(validation_messages)
        
        if not validation_success:
            logger.error("Validation of entity relationships failed")
            return False, messages
        
        # Deploy each entity without foreign key constraints
        entity_ids = {}  # Store entity IDs for later use in applying constraints
        for entity_name, entity_def in entities_data['entities'].items():
            success, entity_messages, entity_id = deploy_single_entity(entity_name, entity_def, schema_name, apply_constraints=False)
            messages.extend(entity_messages)
            
            if not success:
                return False, messages
            
            entity_ids[entity_name] = entity_id
        
        # Now apply foreign key constraints after all entities are created
        logger.info("All entities created, now applying foreign key constraints")
        for entity_name, entity_def in entities_data['entities'].items():
            if 'relationships' in entity_def:
                entity_id = entity_ids[entity_name]
                entity_num = entity_id[1:]  # Remove 'E' prefix
                table_name = f"e{entity_num}_{entity_name.lower()}"
                
                success, fk_messages = add_foreign_key_constraints(entity_id, entity_name, entity_def, schema_name, table_name)
                messages.extend(fk_messages)
                
                if not success:
                    logger.warning(f"Failed to apply some foreign key constraints for entity '{entity_name}'")
                    # Continue with other entities instead of failing
        
        logger.info("Entity deployment completed successfully")
        return True, messages
    except Exception as e:
        error_msg = f"Entity deployment error: {str(e)}"
        logger.error(error_msg, exc_info=True)
        messages.append(error_msg)
        return False, messages
"""
            
            # Write updated content
            with open(deployer_path, 'w') as f:
                f.write(new_content)
                
            logger.info(f"Added deploy_entities function to {deployer_path}")
        else:
            logger.info(f"deploy_entities function already exists in {deployer_path}")
        
        return True
    except Exception as e:
        logger.error(f"Error fixing imports in {deployer_path}: {str(e)}")
        logger.error(traceback.format_exc())
        return False

def test_entity_parser(parser_module: Any, sample_text: str) -> Tuple[bool, Dict]:
    """
    Test entity parsing functionality.
    
    Args:
        parser_module: Imported entity_parser module
        sample_text: Sample entity prescriptive text
        
    Returns:
        Tuple containing:
            - Boolean indicating if parsing was successful
            - Parsed entity data dictionary
    """
    logger.info("=== Testing Entity Parser ===")
    
    # Check if parse_entities function exists
    if not hasattr(parser_module, 'parse_entities'):
        logger.error("parse_entities function not found in parser module")
        return False, {}
    
    # Get parse_entities function
    parse_entities = getattr(parser_module, 'parse_entities')
    
    try:
        # Parse entities
        logger.info("Parsing entities from sample text")
        entities_data, warnings = parse_entities(sample_text)
        
        if warnings:
            logger.warning("Parse warnings:")
            for warning in warnings:
                logger.warning(f"  {warning}")
        
        # Check if entities were parsed
        if not entities_data or 'entities' not in entities_data:
            logger.error("No entities parsed from sample text")
            return False, {}
        
        logger.info(f"Successfully parsed {len(entities_data['entities'])} entities:")
        
        # Log parsed relationships
        for entity_name, entity_def in entities_data['entities'].items():
            logger.info(f"Entity: {entity_name}")
            logger.info(f"  Attributes: {len(entity_def.get('attributes', {}))}")
            
            if 'relationships' in entity_def:
                logger.info(f"  Relationships: {len(entity_def['relationships'])}")
                for rel_name, rel_def in entity_def['relationships'].items():
                    rel_type = rel_def.get('type', 'unknown')
                    target = rel_def.get('entity', 'unknown')
                    source_attr = rel_def.get('source_attribute', 'unknown')
                    target_attr = rel_def.get('target_attribute', 'unknown')
                    logger.info(f"    {rel_name}: {rel_type} relationship with {target} ({source_attr} -> {target_attr})")
        
        # Check for the specific relationships we're interested in
        found_emp_dept = False
        found_emp_manager = False
        
        for entity_name, entity_def in entities_data['entities'].items():
            if 'relationships' not in entity_def:
                continue
                
            for rel_name, rel_def in entity_def['relationships'].items():
                source_entity = entity_name
                target_entity = rel_def.get('entity', '')
                source_attr = rel_def.get('source_attribute', '')
                target_attr = rel_def.get('target_attribute', '')
                
                if source_entity == 'Employee' and target_entity == 'Department' and source_attr == 'departmentId':
                    found_emp_dept = True
                    logger.info("Found Employee to Department relationship")
                
                if source_entity == 'Employee' and target_entity == 'Employee' and source_attr == 'managerId':
                    found_emp_manager = True
                    logger.info("Found Employee self-reference (manager) relationship")
        
        if not found_emp_dept:
            logger.warning("Employee to Department relationship not found")
        
        if not found_emp_manager:
            logger.warning("Employee self-reference (manager) relationship not found")
        
        parse_success = found_emp_dept and found_emp_manager
        logger.info(f"Entity parsing {'successful' if parse_success else 'partially successful'}")
        
        return parse_success, entities_data
    except Exception as e:
        logger.error(f"Error parsing entities: {str(e)}")
        logger.error(traceback.format_exc())
        return False, {}

def test_entity_deployment(deployer_module: Any, entities_data: Dict, schema_name: str) -> bool:
    """
    Test entity deployment functionality.
    
    Args:
        deployer_module: Imported entity_deployer_v2 module
        entities_data: Parsed entity data
        schema_name: Database schema to deploy to
        
    Returns:
        Boolean indicating if deployment was successful
    """
    logger.info("=== Testing Entity Deployment ===")
    
    # Check if deploy_entities function exists
    if not hasattr(deployer_module, 'deploy_entities'):
        logger.error("deploy_entities function not found in deployer module")
        return False
    
    # Get deploy_entities function
    deploy_entities = getattr(deployer_module, 'deploy_entities')
    
    try:
        # Deploy entities
        logger.info(f"Deploying entities to schema {schema_name}")
        success, messages = deploy_entities(entities_data, schema_name)
        
        if messages:
            logger.info("Deploy messages:")
            for message in messages:
                logger.info(f"  {message}")
        
        if not success:
            logger.error("Entity deployment failed")
            return False
        
        logger.info("Entity deployment successful")
        return True
    except Exception as e:
        logger.error(f"Error deploying entities: {str(e)}")
        logger.error(traceback.format_exc())
        return False

def main():
    """
    Main function.
    """
    # Parse command line arguments
    parser = argparse.ArgumentParser(description='Test entity relationship parsing and deployment')
    parser.add_argument('--schema', type=str, default='test_schema',
                        help='Database schema to deploy to (default: test_schema)')
    parser.add_argument('--sample-file', type=str, default=None,
                        help='Path to sample entity file (default: auto-detect)')
    parser.add_argument('--parse-only', action='store_true',
                        help='Only test entity parsing, skip deployment')
    
    args = parser.parse_args()
    
    # Print test configuration
    print("\n=== Entity Relationship Test ===")
    print(f"Schema: {args.schema}")
    print(f"Parse Only: {args.parse_only}")
    print(f"Python version: {sys.version}")
    print(f"Current directory: {os.getcwd()}")
    print("==============================\n")
    
    # Find the entity_parser.py file
    parser_path = None
    if args.sample_file:
        # If sample file is provided, use its directory to search for parser
        sample_dir = os.path.dirname(os.path.abspath(args.sample_file))
        parser_path = find_file("entity_parser.py", sample_dir)
    
    if not parser_path:
        # Search in default locations
        for search_dir in [
            os.getcwd(),  # Current directory
            os.path.dirname(os.path.abspath(__file__)),  # Script directory
            os.path.dirname(os.path.dirname(os.path.abspath(__file__))),  # Parent directory
        ]:
            parser_path = find_file("entity_parser.py", search_dir)
            if parser_path:
                break
    
    if not parser_path:
        logger.error("entity_parser.py not found. Please specify the correct location.")
        return 1
    
    # Find the entity_deployer_v2.py file
    deployer_path = None
    deployer_filename = "entity_deployer_v2.py"
    
    for search_dir in [
        os.path.join(os.getcwd(), "deployers"),  # deployers subdirectory
        os.path.dirname(parser_path),  # Same directory as parser
        os.path.dirname(os.path.abspath(__file__)),  # Script directory
        os.path.dirname(os.path.dirname(os.path.abspath(__file__))),  # Parent directory
    ]:
        if os.path.exists(search_dir):
            deployer_path = find_file(deployer_filename, search_dir)
            if deployer_path:
                break
    
    if not deployer_path and not args.parse_only:
        logger.warning("entity_deployer_v2.py not found. Only entity parsing will be tested.")
        args.parse_only = True
    
    # Get the sample entity file
    sample_text = SAMPLE_TEXT  # Default to hardcoded sample
    
    if args.sample_file:
        # Use provided sample file
        if os.path.exists(args.sample_file):
            try:
                with open(args.sample_file, 'r') as f:
                    sample_text = f.read()
                logger.info(f"Read sample text from {args.sample_file}")
            except Exception as e:
                logger.error(f"Error reading sample file: {str(e)}")
                logger.error(traceback.format_exc())
                return 1
        else:
            logger.error(f"Sample file {args.sample_file} not found")
            return 1
    else:
        # Try to find sample file automatically
        sample_filename = "sample_entity_output2.txt"
        sample_path = None
        
        for search_dir in [
            os.path.join(os.getcwd(), "samples"),  # samples subdirectory
            os.path.dirname(os.path.abspath(__file__)),  # Script directory
            os.path.dirname(os.path.dirname(os.path.abspath(__file__))),  # Parent directory
        ]:
            if os.path.exists(search_dir):
                sample_path = find_file(sample_filename, search_dir)
                if sample_path:
                    break
        
        if sample_path:
            try:
                with open(sample_path, 'r') as f:
                    sample_text = f.read()
                logger.info(f"Read sample text from {sample_path}")
            except Exception as e:
                logger.error(f"Error reading sample file: {str(e)}")
                logger.error(traceback.format_exc())
                # Continue with hardcoded sample
        else:
            logger.warning(f"Sample file {sample_filename} not found, using hardcoded sample")
    
    # Import entity_parser module
    parser_module = import_module_from_file("entity_parser", parser_path)
    if not parser_module:
        logger.error("Failed to import entity_parser module")
        return 1
    
    # Fix imports in entity_deployer_v2.py if needed
    if not args.parse_only and deployer_path:
        if not fix_imports_in_deployer(deployer_path):
            logger.error("Failed to fix imports in entity_deployer_v2.py")
            args.parse_only = True
    
    # Import entity_deployer_v2 module if needed
    deployer_module = None
    if not args.parse_only and deployer_path:
        # Wait a moment for file changes to be recognized
        time.sleep(1)
        deployer_module = import_module_from_file("entity_deployer_v2", deployer_path)
        if not deployer_module:
            logger.error("Failed to import entity_deployer_v2 module")
            args.parse_only = True
    
    # Test entity parsing
    parse_success, entities_data = test_entity_parser(parser_module, sample_text)
    
    # Test entity deployment if needed
    deploy_success = False
    if not args.parse_only and deployer_module and parse_success:
        deploy_success = test_entity_deployment(deployer_module, entities_data, args.schema)
    
    # Print summary
    print("\n=== Test Summary ===")
    print(f"Entity Parsing: {'✓ PASSED' if parse_success else '❌ FAILED'}")
    
    if not args.parse_only:
        print(f"Entity Deployment: {'✓ PASSED' if deploy_success else '❌ FAILED'}")
    
    print("\nKey Relationships:")
    
    # Check for the specific relationships in the parsed data
    found_emp_dept = False
    found_emp_manager = False
    
    for entity_name, entity_def in entities_data.get('entities', {}).items():
        if 'relationships' not in entity_def:
            continue
            
        for rel_name, rel_def in entity_def['relationships'].items():
            source_entity = entity_name
            target_entity = rel_def.get('entity', '')
            source_attr = rel_def.get('source_attribute', '')
            target_attr = rel_def.get('target_attribute', '')
            
            if source_entity == 'Employee' and target_entity == 'Department' and source_attr == 'departmentId':
                found_emp_dept = True
                print(f"  ✓ Employee has relationship with Department using departmentId")
            
            if source_entity == 'Employee' and target_entity == 'Employee' and source_attr == 'managerId':
                found_emp_manager = True
                print(f"  ✓ Employee has self-reference relationship using managerId")
    
    if not found_emp_dept:
        print("  ❌ Employee to Department relationship not found")
    
    if not found_emp_manager:
        print("  ❌ Employee self-reference (manager) relationship not found")
    
    print("\nSee entity_relationship_test.log for detailed information")
    
    # Return exit code based on test results
    if args.parse_only:
        return 0 if parse_success else 1
    else:
        return 0 if parse_success and deploy_success else 1

if __name__ == '__main__':
    sys.exit(main())