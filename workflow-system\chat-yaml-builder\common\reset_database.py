#!/usr/bin/env python3

"""
This script truncates the workflow_runtime schema tables and redeploys the YAML files.
"""

import psycopg2
from datetime import datetime
import os
import traceback
import subprocess

# Set log path to current working directory
LOG_PATH = os.path.join(
    os.path.dirname(os.path.abspath(__file__)),
    f"reset_database_log_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
)

# Logging function
def log(msg):
    with open(LOG_PATH, "a") as f:
        f.write(f"{datetime.now().strftime('%Y-%m-%d %H:%M:%S')} | {msg}\n")
    print(msg)

# Database configuration
PG_CONFIG = {
    "dbname": "workflow_system",
    "user": "postgres",
    "password": "workflow_postgres_secure_password",
    "host": "**********",
    "port": "5432"
}

def connect_to_db():
    """Connect to the PostgreSQL database."""
    try:
        conn = psycopg2.connect(**PG_CONFIG)
        log("✅ Connected to PostgreSQL database")
        return conn
    except Exception as e:
        log(f"❌ Error connecting to database: {e}")
        raise

def reset_database():
    """Truncate the workflow_runtime schema tables."""
    conn = connect_to_db()
    cursor = conn.cursor()
    
    try:
        # Set search path to the workflow_runtime schema
        cursor.execute("SET search_path TO workflow_runtime")
        
        # Get all tables in the schema
        cursor.execute("""
            SELECT table_name
            FROM information_schema.tables
            WHERE table_schema = 'workflow_runtime'
            AND table_type = 'BASE TABLE'
            ORDER BY table_name
        """)
        
        tables = cursor.fetchall()
        
        log(f"🔍 Found {len(tables)} tables to truncate")
        
        # Disable foreign key constraints
        cursor.execute("SET session_replication_role = 'replica'")
        
        # Truncate each table
        for table in tables:
            table_name = table[0]
            # Quote table name to handle reserved keywords like "user"
            quoted_table_name = f'"{table_name}"'
            cursor.execute(f"TRUNCATE TABLE {quoted_table_name} CASCADE")
            log(f"✅ Truncated table: {table_name}")
        
        # Re-enable foreign key constraints
        cursor.execute("SET session_replication_role = 'origin'")
        
        conn.commit()
        log("✅ All tables truncated successfully")
        
    except Exception as e:
        if conn:
            conn.rollback()
        log(f"❌ Error truncating tables: {e}")
        log(traceback.format_exc())
    finally:
        if cursor:
            cursor.close()
        if conn:
            conn.close()

def redeploy_yaml_files():
    """Redeploy the YAML files."""
    try:
        # Run the deploy_rbac_yamls.py script
        log("🔄 Redeploying YAML files...")
        result = subprocess.run(["python", "deploy_rbac_yamls.py"], capture_output=True, text=True)
        
        if result.returncode == 0:
            log("✅ YAML files redeployed successfully")
            log(result.stdout)
        else:
            log(f"❌ Error redeploying YAML files: {result.stderr}")
    except Exception as e:
        log(f"❌ Error redeploying YAML files: {e}")
        log(traceback.format_exc())

if __name__ == "__main__":
    reset_database()
    #redeploy_yaml_files()
