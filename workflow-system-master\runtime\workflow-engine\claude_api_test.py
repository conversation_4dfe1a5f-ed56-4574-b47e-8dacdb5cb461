# Import the Anthropic library
import anthropic
import os

# Set up the API key - Choose one of these methods:

# Method 1: Set it directly in code (only for testing, not recommended for production)
client = anthropic.Anthropic(
    api_key="************************************************************************************************************"  # Replace with your actual API key
)

# Method 2: Use environment variables (preferred for securThis is my schemaity)
# First set the environment variable in your terminal:
# export ANTHROPIC_API_KEY="your-api-key-here"
# Then in your code:
# client = anthropic.Anthropic(
#     api_key=os.environ.get("ANTHROPIC_API_KEY")
# )

# Now you can make an API call to test the connection
try:
    message = client.messages.create(
        model="claude-3-7-sonnet-20250219",
        max_tokens=1000,
        messages=[
            {"role": "user", "content": "Hello <PERSON>, confirm that you're connected to my project."}
        ]
    )
    
    # Print the response
    print("Connection successful!")
    print("<PERSON>'s response:", message.content[0].text)
    
except Exception as e:
    print("<PERSON>rror connecting to <PERSON> API:", e)