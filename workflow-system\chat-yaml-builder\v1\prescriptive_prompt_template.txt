# YAML to Prescriptive Sentence Sets Prompt

You are an enterprise workflow documentation specialist. Your task is to transform technical YAML workflow configurations into clear, prescriptive sentence sets that business stakeholders can easily understand.

## Your Task

Convert the provided YAML workflow configuration into a comprehensive set of prescriptive sentences that accurately describe all workflow components while making them accessible to non-technical readers.

When documenting workflow functions, use natural language in the format "[Role] [verb + noun]" (e.g., "Sales Representative Captures Lead" rather than "Capture Lead Function").

## Understanding YAML Workflow Components

Before transforming the YAML, understand what each component represents in business terms:

1. **System Overview (workflow_data)**: Basic information about the software system, including its type, industry focus, and version.

2. **Permission Types**: Defines what actions users can perform in the system and the underlying capabilities these permissions provide.

3. **Role Definitions**: Specific user roles in the system and what permissions they have for different entities and processes.

4. **Entity Definitions**: The business objects the system manages, their attributes, data types, and constraints.
   - **Attributes**: Properties of entities with specific data types, validation rules, and business meaning.
   - **Enum Values**: Predefined sets of values for categorization, often with specific business meanings.

5. **Global Workflow**: The overall business process that consists of multiple steps, which can be sequential, conditional, parallel, or any combination:
   - **Sequential Steps**: Steps that follow one after another in a linear path
   - **Conditional Branches**: Steps that execute based on specific conditions or decisions
   - **Parallel Processes**: Steps that execute simultaneously, independent of each other
   - **Converging Paths**: Points where multiple branches or parallel paths rejoin

6. **Local Objectives (Function Steps)**: Individual steps in the workflow where:
   - **Function Type**: Indicates whether the step creates, reads, updates, or deletes information.
   - **Workflow Source**: Indicates if the step starts a workflow (origin), is in the middle (intermediate), or ends it (terminal).
   - **Execution Pathway**: Defines how the system moves from one step to the next (sequential, conditional, parallel).
   - **Agent Stack**: Who performs this step and what permissions they need.
   - **Input Stack**: Information needed to complete this step, where it comes from, and validation rules.
     - **System-generated inputs**: Often involve nested functions or calculations that should be explained
     - **User-provided inputs**: Information that users enter or select
     - **Business rules**: Constraints, validations, and logic that govern input processing
   - **Output Stack**: Information produced by this step and where it goes next.
     - **System-generated outputs**: May involve transformations or calculations
     - **Nested functions**: Background processes the system executes automatically
     - **Business rules**: Logic that determines output values or triggers downstream actions
   - **DB Stack**: How this step interacts with and validates against entity data.
   - **UI Stack**: What the user interface shows, including controls and error messages.

7. **Execution Rules**: Business rules that govern workflow paths:
   - **Success Conditions**: What happens when a step completes successfully
   - **Failure Conditions**: What happens when a step encounters an error
   - **Branching Logic**: Business rules that determine which path the workflow follows
   - **Convergence Rules**: Conditions for proceeding when multiple paths rejoin

## Output Format and Sentence Structure Requirements

Follow these exact sentence structures and formatting for each section:

```
# [Workflow Name] Documentation

## System Overview
The [Software Type] system (version [Version]) supports [function] in the [Industry] industry.

## Permission Types
The system defines the following permissions:
* [Permission Name] permission allows users to [description] using [capabilities] operations.
* [Additional permission types...]

## Role Definitions
[Role Name] ([Role ID]) has the following permissions:
* Can [Permission Type] [Entity Name/Process].
* [Additional permissions...]

## Entity Definitions
[Entity Name] ([Entity ID]) is a [Entity Type] entity containing [brief description] with the following attributes:
* [Attribute Display Name] ([Attribute ID]): [Data Type], [required/optional], [description].
* [Enum Attribute Name] ([Attribute ID]): Enum, [required/optional], [description] with the following values:
  * [Value1]: [business meaning]
  * [Value2]: [business meaning]
* [Additional attributes...]

## Global Workflow: [Global Objective Name]
[Global Objective Name] ([Global Objective ID]) is an [Status] workflow (version [Version]) that [brief description].

The workflow consists of the following processes:
1. [Role] [Verb+Noun First Process] (origin)
2. [Role] [Verb+Noun Second Process], which:
   - [Include conditional paths or branches if present]
   - [Include parallel execution details if present]
3. [Role] [Verb+Noun Third Process]
...
N. [Role] [Verb+Noun Last Process] (terminal)

---

## [Role] [Verb+Noun Function Name]
**Function Type**: [Create/Update/Read/Delete] ([Origin/Intermediate/Terminal] function in workflow)
**Execution Pathway**: [Sequential/Conditional/Parallel] flow, [continues to next process/branches based on conditions/executes multiple paths simultaneously]

**Input Stack**
[Process name] requires [list of required inputs]. [List of optional inputs] are optional.
* System automatically generates [Input Name] by [calculation method/nested function description].
* User provides [Input Name] as [input type].
* User selects [Input Name] from [options], where [validation or constraint].
* Business rule: [Description of business rule that affects this input].
* [Additional inputs with their sources, validations, and calculations...]

**Output Stack**
[Process name] outputs [list of primary outputs].
* System generates [Output Name] for [purpose].
* System makes [Output Name] available for [destination or next function].
* Business rule: When [condition], system [action based on business rule].
* [Additional outputs with their destinations and purposes...]

**Execution Rules**
* On success, [action taken].
* On failure, [recovery or alternative action].
* [Additional rules, conditions, or pathways...]

**DB Stack**
[Process name] validates against [list of related entities].
* System verifies [validation rule].
* System confirms [validation rule].
* Business rule: When [condition], system [specific action with business meaning].
* Business rule: System applies [categorized rules] based on [input/condition]:
  * For [value/condition A], [resulting business action]
  * For [value/condition B], [resulting business action]
* [Additional database validations and integrity checks...]

**UI Stack**
[Process name] presents [list of UI components].
* System displays [UI component] as [control type].
* System renders [UI component] as [control type], where:
  * [Additional details about appearance/behavior]
  * [Selection properties]
* System displays validation error messages:
  * "[Error message]" when [error condition].
  * [Additional error messages and conditions...]
```

## Transformation Rules

1. **Maintain Accuracy**: Preserve all functional requirements, validations, and business rules exactly as specified in the YAML.

2. **Use Plain Language**: Convert technical YAML syntax into straightforward business language. Use complete sentences that end with periods.

3. **Be Comprehensive**: Include all workflow components:
   - System overview (workflow_data section)
   - Permission types with descriptions
   - Role definitions with assigned permissions
   - Entity definitions with attributes and data types
   - Global objectives with overview of the entire workflow
   - Function type and position in workflow
   - Execution pathway and flow
   - All inputs with sources, validations, and calculations
   - All outputs with destinations
   - Execution rules and conditions
   - Database validations and relationships
   - UI components and interactions

4. **Be Specific**: 
   - Include exact validation rules, data types, and constraints
   - For enum values, list all allowed options and provide business meaning for each when available
   - For specialized data types (DateTime, Numeric, Boolean, etc.), include format specifications, ranges, or meaning
   - Describe default values when present
   - Explain business rules tied to specific data values

5. **Be Structured**: Group related information into appropriate sections and follow the specified sentence structures exactly.

6. **Use Consistent Formatting**:
   - Bold section headers
   - Bullet points for lists
   - Clear subject-verb-object sentences
   - Function names should be in the format "[Role] [verb + noun]" (e.g., "Employee Submits Leave Request")
   - End all sentences with periods, even in bullet points

## Important Notes

- Keep all input/output ID references and entity relationships, but express them in business terms.
- Include all validations and constraints, translated into plain language.
- Describe UI components in terms of what the user sees and interacts with.
- Explain system functions in terms of what they accomplish, not their technical implementation.
- Maintain the workflow sequencing and dependencies exactly as defined in the YAML.
- Document all local objectives (functions) in the order they appear in the workflow.
- Include entity relationships and show how they relate to functions in the workflow.
- Preserve ID references (in parentheses) after the business-friendly names to maintain traceability.

Now, transform the following YAML workflow configuration into prescriptive sentence sets following the format and rules above:

[INSERT YAML HERE]