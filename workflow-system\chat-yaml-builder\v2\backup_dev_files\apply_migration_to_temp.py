#!/usr/bin/env python3
"""
Apply migration script to workflow_temp schema.

This script reads the migration_script.sql file, replaces "workflow_runtime" with "workflow_temp",
and executes the SQL using the db_utils.py module.
"""

import os
import sys
import logging
from typing import List

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('apply_migration_to_temp')

# Import required modules
from db_utils import execute_query

def read_migration_script(file_path: str) -> str:
    """
    Read the migration script from a file.
    
    Args:
        file_path: Path to the migration script file
        
    Returns:
        The contents of the file as a string
    """
    try:
        with open(file_path, 'r') as f:
            return f.read()
    except Exception as e:
        logger.error(f"Error reading file {file_path}: {str(e)}")
        return ""

def apply_migration_to_temp(migration_script: str) -> bool:
    """
    Apply the migration script to the workflow_temp schema.
    
    Args:
        migration_script: The migration script to apply
        
    Returns:
        True if the migration was successful, False otherwise
    """
    # Replace workflow_runtime with workflow_temp
    migration_script = migration_script.replace('workflow_runtime', 'workflow_temp')
    
    # Add "IF NOT EXISTS" to all ALTER TABLE ADD COLUMN statements
    migration_script = migration_script.replace('ADD COLUMN ', 'ADD COLUMN IF NOT EXISTS ')
    
    # Split the script into individual statements
    statements = migration_script.split(';')
    
    # Execute each statement
    for statement in statements:
        statement = statement.strip()
        if not statement:
            continue
        
        logger.info(f"Executing statement: {statement}")
        success, messages, _ = execute_query(statement)
        
        if not success:
            logger.error(f"Error executing statement: {messages}")
            # Continue even if there's an error
            continue
    
    logger.info("Migration script applied successfully")
    return True

def main():
    """Main function."""
    # Read the migration script
    migration_script = read_migration_script('migration_script.sql')
    
    if not migration_script:
        logger.error("Failed to read migration script")
        sys.exit(1)
    
    # Apply the migration script
    success = apply_migration_to_temp(migration_script)
    
    if not success:
        logger.error("Failed to apply migration script")
        sys.exit(1)
    
    logger.info("Migration script applied successfully")

if __name__ == '__main__':
    main()
