from fastapi import APIRouter, HTTPException, Query, Depends
from sqlalchemy.orm import Session
from sqlalchemy.sql import text
from typing import List, Dict, Any, Optional
from app.database.postgres import get_db  # ✅ As per your project structure

router = APIRouter()

@router.get("/transactionsdetails", response_model=List[Dict[str, Any]])
def get_workflow_execution_details(
    instance_id: Optional[str] = Query(None, description="(Optional) Workflow Instance ID"),
    status: Optional[str] = Query(None, description="(Optional) Filter by status"),
    db: Session = Depends(get_db)
):
    """
    Fetch transaction info: instance_id, go_id, go_name, lo_id, lo_name, status, input_stack, created_at, updated_at.
    """
    try:
        query = """
        SELECT
            wt.workflow_instance_id,
            wt.go_id,
            wt.tenant_id,
            wt.user_id,
            go.name AS go_name,
            wt.lo_id,
            lo.name AS lo_name,
            wt.status,
            wt.input_stack,
            wt.output_stack,
            wt.created_at,
            wt.updated_at
        FROM workflow_runtime.workflow_transaction wt
        LEFT JOIN workflow_runtime.global_objectives go ON wt.go_id = go.go_id
        LEFT JOIN workflow_runtime.local_objectives lo ON wt.lo_id = lo.lo_id
        WHERE (:instance_id IS NULL OR wt.workflow_instance_id = :instance_id)
        AND (:status IS NULL OR wt.status = :status)
        ORDER BY wt.created_at ASC
        """
        results = db.execute(text(query), {
            "instance_id": instance_id,
            "status": status
        }).fetchall()
        
        return [dict(row._mapping) for row in results]
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))



@router.get("/validateIsGoAvailableInMyTransaction", response_model=Dict[str, Any])
def validate_is_go_available_in_my_transaction(
    go_id: str = Query(..., description="Global Objective ID to validate"),
    db: Session = Depends(get_db)
):
    """
    Validate if a specific Global Objective has any transactions.
    Returns an array of transaction details, each with its own array of lo_details.
    Transactions are grouped by workflow_instance_id.
    """
    try:
        # Get all transaction data in one query
        query = """
        SELECT
            wt.workflow_instance_id,
            wt.go_id,
            wt.tenant_id,
            wt.user_id,
            go.name AS go_name,
            wt.lo_id,
            lo.name AS lo_name,
            wt.status,
            wt.input_stack,
            wt.output_stack,
            wt.created_at,
            wt.updated_at
        FROM workflow_runtime.workflow_transaction wt
        LEFT JOIN workflow_runtime.global_objectives go ON wt.go_id = go.go_id
        LEFT JOIN workflow_runtime.local_objectives lo ON wt.lo_id = lo.lo_id
        WHERE wt.go_id = :go_id
        ORDER BY wt.workflow_instance_id, wt.created_at ASC
        """
        
        results = db.execute(text(query), {"go_id": go_id}).fetchall()
        
        if not results:
            # No transactions found, return empty response
            return {
                "is_pending_transactions_avl": False,
                "transactions_data": []
            }
        
        # Create a dictionary to group by workflow_instance_id
        grouped_data = {}
        is_pending_transactions_avl = False
        
        # Process each row and group by workflow_instance_id
        for row in results:
            # Extract each field using index
            workflow_instance_id = row[0]
            go_id_val = row[1]
            tenant_id = row[2]
            user_id = row[3]
            go_name = row[4]
            lo_id = row[5] or ""
            lo_name = row[6]
            status = row[7]
            input_stack = row[8]
            output_stack = row[9]
            created_at = row[10]
            updated_at = row[11]
            
            # Check for pending transactions
            if status == "pending":
                is_pending_transactions_avl = True
            
            # Parse input_stack from JSON string if it's stored as string
            if isinstance(input_stack, str):
                try:
                    input_stack = json.loads(input_stack)
                except:
                    input_stack = {}
            
            # Parse output_stack from JSON string if it's stored as string
            if isinstance(output_stack, str):
                try:
                    output_stack = json.loads(output_stack)
                except:
                    output_stack = None
            
            # Format datetime fields
            created_at_str = created_at.isoformat() if hasattr(created_at, "isoformat") else str(created_at)
            updated_at_str = updated_at.isoformat() if hasattr(updated_at, "isoformat") else str(updated_at)
            
            # Create or update the transaction object
            if workflow_instance_id not in grouped_data:
                grouped_data[workflow_instance_id] = {
                    "workflow_instance_id": workflow_instance_id,
                    "go_id": go_id_val,
                    "tenant_id": tenant_id,
                    "user_id": user_id,
                    "go_name": go_name,
                    "created_at": created_at_str,
                    "updated_at": updated_at_str,
                    "lo_details": []
                }
            
            # Add LO details
            lo_detail = {
                "lo_id": lo_id,
                "lo_name": lo_name,
                "status": status,
                "input_stack": input_stack,
                "output_stack": output_stack
            }
            
            grouped_data[workflow_instance_id]["lo_details"].append(lo_detail)
        
        # Convert the grouped data to a list
        transactions_data = list(grouped_data.values())
        
        # Return the data
        return {
            "is_pending_transactions_avl": is_pending_transactions_avl,
            "transactions_data": transactions_data
        }
        
    except Exception as e:
        # Include more detailed error for debugging
        import traceback
        error_detail = f"Error validating GO availability: {str(e)}\n{traceback.format_exc()}"
        raise HTTPException(status_code=500, detail=error_detail)