tenant:
  id: "t001"
  name: "TaskManagement001"
  roles:
    - id: "r001"
      name: "User"
      inherits_from: null
      access:
        entities:
          - entity_id: "e001"  # Task entity
            permissions: ["Read", "Create"]
          - entity_id: "e002"  # TaskCategory entity
            permissions: ["Read"]
          - entity_id: "e003"  # User entity
            permissions: ["Read"]
          - entity_id: "e004"  # Role entity
            permissions: ["Read"]
        objectives:
          - objective_id: "go001.lo001"
            permissions: ["Execute"]

    - id: "r002"
      name: "Team Lead"
      inherits_from: null
      access:
        entities:
          - entity_id: "e001"  # Task entity
            permissions: ["Read", "Create", "Update"]
          - entity_id: "e002"  # TaskCategory entity
            permissions: ["Read"]
          - entity_id: "e003"  # User entity
            permissions: ["Read"]
          - entity_id: "e004"  # Role entity
            permissions: ["Read"]
        objectives:
          - objective_id: "go001.lo001"
            permissions: ["Execute"]
          - objective_id: "go001.lo002"
            permissions: ["Execute"]

    - id: "r003"
      name: "Admin"
      inherits_from: null
      access:
        entities:
          - entity_id: "e001"  # Task entity
            permissions: ["Read", "Create", "Update", "Delete"]
          - entity_id: "e002"  # TaskCategory entity
            permissions: ["Read", "Create", "Update", "Delete"]
          - entity_id: "e003"  # User entity
            permissions: ["Read", "Update"]
          - entity_id: "e004"  # Role entity
            permissions: ["Read"]
        objectives:
          - objective_id: "go001.lo001"
            permissions: ["Execute"]
          - objective_id: "go001.lo002"
            permissions: ["Execute"]
          - objective_id: "go001.lo003"
            permissions: ["Execute"]


workflow_data:
  software_type: "Task Management"
  industry: "General"
  version: "1.0"
  created_by: "system"
  created_on: "{{timestamp}}"

permission_types:
  - id: "read"
    description: "Can read entity data"
    capabilities: ["GET"]
  - id: "create"
    description: "Can create new entity records"
    capabilities: ["POST"]
  - id: "update"
    description: "Can update existing records"
    capabilities: ["PUT"]
  - id: "delete"
    description: "Can delete entity records"
    capabilities: ["DELETE"]
  - id: "execute"
    description: "Can execute workflows"
    capabilities: ["EXECUTE"]

entities:
  - id: "e001"
    name: "Task"
    type: "Master"
    version: "1.0"
    status: "Active"
    attributes_metadata:
      attribute_prefix: "at"
      attribute_map:
        "at101": "task_id"
        "at102": "title"
        "at103": "description"
        "at104": "category_id"
        "at105": "priority"
        "at106": "status"
        "at107": "assigned_to"
        "at108": "created_by"
        "at109": "created_date"
        "at110": "due_date"
        "at111": "completion_date"
        "at112": "comments"
        "at113": "sub_category_id"
      required_attributes:
        - "at101"
        - "at102"
        - "at104"
        - "at105"
        - "at106"
        - "at108"
        - "at109"
        - "at110"
    attributes:
      - id: "at101"
        name: "task_id"
        display_name: "Task ID"
        datatype: "String"
        required: true
        version: "1.0"
        status: "Deployed"
      - id: "at102"
        name: "title"
        display_name: "Title"
        datatype: "String"
        required: true
        version: "1.0"
        status: "Deployed"
      - id: "at103"
        name: "description"
        display_name: "Description"
        datatype: "String"
        required: false
        version: "1.0"
        status: "Deployed"
      - id: "at104"
        name: "category_id"
        display_name: "Category ID"
        datatype: "String"
        required: true
        version: "1.0"
        status: "Deployed"
      - id: "at105"
        name: "priority"
        display_name: "Priority"
        datatype: "Enum"
        required: true
        values: ["Low", "Medium", "High", "Critical"]
        version: "1.0"
        status: "Deployed"
      - id: "at106"
        name: "status"
        display_name: "Status"
        datatype: "Enum"
        required: true
        values: ["Open", "In Progress", "Blocked", "Completed","OPEN"]
        version: "1.0"
        status: "Deployed"
      - id: "at107"
        name: "assigned_to"
        display_name: "Assigned To"
        datatype: "String"
        required: false
        version: "1.0"
        status: "Deployed"
      - id: "at108"
        name: "created_by"
        display_name: "Created By"
        datatype: "String"
        required: true
        version: "1.0"
        status: "Deployed"
      - id: "at109"
        name: "created_date"
        display_name: "Created Date"
        datatype: "Date"
        required: true
        version: "1.0"
        status: "Deployed"
      - id: "at110"
        name: "due_date"
        display_name: "Due Date"
        datatype: "Date"
        required: true
        version: "1.0"
        status: "Deployed"
      - id: "at111"
        name: "completion_date"
        display_name: "Completion Date"
        datatype: "Date"
        required: false
        version: "1.0"
        status: "Deployed"
      - id: "at112"
        name: "comments"
        display_name: "Comments"
        datatype: "String"
        required: false
        version: "1.0"
        status: "Deployed"
      - id: "at113"
        name: "sub_category_id"
        display_name: "Sub Category ID"
        datatype: "String"
        required: false
        version: "1.0"
        status: "Deployed"
  - id: "e002"
    name: "TaskCategory"
    type: "Master"
    version: "1.0"
    status: "Active"
    attributes_metadata:
      attribute_prefix: "at"
      attribute_map:
        "at201": "category_id"
        "at202": "category_name"
        "at203": "description"
        "at204": "active"
      required_attributes:
        - "at201"
        - "at202"
        - "at204"
    attributes:
      - id: "at201"
        name: "category_id"
        display_name: "Category ID"
        datatype: "String"
        required: true
        version: "1.0"
        status: "Deployed"
      - id: "at202"
        name: "category_name"
        display_name: "Category Name"
        datatype: "String"
        required: true
        version: "1.0"
        status: "Deployed"
      - id: "at203"
        name: "description"
        display_name: "Description"
        datatype: "String"
        required: false
        version: "1.0"
        status: "Deployed"
      - id: "at204"
        name: "active"
        display_name: "Active"
        datatype: "Boolean"
        required: true
        version: "1.0"
        status: "Deployed"
  - id: "e005"
    name: "TaskSubCategory"
    type: "Master"
    version: "1.0"
    status: "Active"
    attributes_metadata:
      attribute_prefix: "at"
      attribute_map:
        "at501": "sub_category_id"
        "at502": "sub_category_name"
        "at503": "parent_category_id"
        "at504": "active"
      required_attributes:
        - "at501"
        - "at502"
        - "at503"
        - "at504"
    attributes:
      - id: "at501"
        name: "sub_category_id"
        display_name: "Sub Category ID"
        datatype: "String"
        required: true
        version: "1.0"
        status: "Deployed"
      - id: "at502"
        name: "sub_category_name"
        display_name: "Sub Category Name"
        datatype: "String"
        required: true
        version: "1.0"
        status: "Deployed"
      - id: "at503"
        name: "parent_category_id"
        display_name: "Parent Category ID"
        datatype: "String"
        required: true
        version: "1.0"
        status: "Deployed"
      - id: "at504"
        name: "active"
        display_name: "Active"
        datatype: "Boolean"
        required: true
        version: "1.0"
        status: "Deployed"
  - id: "e003"
    name: "User"
    type: "Master"
    version: "1.0"
    status: "Active"
    attributes_metadata:
      attribute_prefix: "at"
      attribute_map:
        "at301": "user_id"
        "at302": "username"
        "at303": "email"
        "at304": "first_name"
        "at305": "last_name"
        "at306": "status"
        "at307": "password_hash"
        "at308": "disabled"
        "at309": "organization"
        "at310": "team"
      required_attributes:
        - "at301"
        - "at302"
        - "at303"
        - "at306"
    attributes:
      - id: "at301"
        name: "user_id"
        display_name: "User ID"
        datatype: "String"
        required: true
        version: "1.0"
        status: "Deployed"
      - id: "at302"
        name: "username"
        display_name: "Username"
        datatype: "String"
        required: true
        version: "1.0"
        status: "Deployed"
      - id: "at303"
        name: "email"
        display_name: "Email"
        datatype: "String"
        required: true
        version: "1.0"
        status: "Deployed"
      - id: "at304"
        name: "first_name"
        display_name: "First Name"
        datatype: "String"
        required: false
        version: "1.0"
        status: "Deployed"
      - id: "at305"
        name: "last_name"
        display_name: "Last Name"
        datatype: "String"
        required: false
        version: "1.0"
        status: "Deployed"
      - id: "at306"
        name: "status"
        display_name: "Status"
        datatype: "Enum"
        required: true
        values: ["active", "inactive", "suspended"]
        version: "1.0"
        status: "Deployed"
      - id: "at307"
        name: "password_hash"
        display_name: "Password Hash"
        datatype: "String"
        required: false
        version: "1.0"
        status: "Deployed"
      - id: "at308"
        name: "disabled"
        display_name: "Disabled"
        datatype: "Boolean"
        required: false
        version: "1.0"
        status: "Deployed"
      - id: "at309"
        name: "organization"
        display_name: "Organization"
        datatype: "String"
        required: false
        version: "1.0"
        status: "Deployed"
      - id: "at310"
        name: "team"
        display_name: "Team"
        datatype: "String"
        required: false
        version: "1.0"
        status: "Deployed"
  - id: "e004"
    name: "Role"
    type: "Master"
    version: "1.0"
    status: "Active"
    attributes_metadata:
      attribute_prefix: "at"
      attribute_map:
        "at401": "role_id"
        "at402": "name"
        "at403": "description"
        "at404": "inherits_from"
        "at405": "tenant_id"
      required_attributes:
        - "at401"
        - "at402"
    attributes:
      - id: "at401"
        name: "role_id"
        display_name: "Role ID"
        datatype: "String"
        required: true
        version: "1.0"
        status: "Deployed"
      - id: "at402"
        name: "name"
        display_name: "Name"
        datatype: "String"
        required: true
        version: "1.0"
        status: "Deployed"
      - id: "at403"
        name: "description"
        display_name: "Description"
        datatype: "String"
        required: false
        version: "1.0"
        status: "Deployed"
      - id: "at404"
        name: "inherits_from"
        display_name: "Inherits From"
        datatype: "String"
        required: false
        version: "1.0"
        status: "Deployed"
      - id: "at405"
        name: "tenant_id"
        display_name: "Tenant ID"
        datatype: "String"
        required: false
        version: "1.0"
        status: "Deployed"

global_objectives:
  - id: "go001"
    name: "Task Management Workflow"
    version: "1.0"
    status: "Active"
    input_stack:
      description: "Global inputs"
      inputs: []
    output_stack:
      description: "Global outputs"
      outputs: []
    data_mapping_stack:
      description: "Data handover between GOs"
      mappings: []

local_objectives:
  - id: "lo001"
    contextual_id: "go001.lo001"
    name: "Create Task"
    workflow_source: "origin"
    function_type: "Create"
    agent_stack:
      agents:
        - role: "r001"  # User role
          rights: ["Execute"]
          users: []
        - role: "r002"  # Team Lead role
          rights: ["Execute"]
          users: []
        - role: "r003"  # Admin role
          rights: ["Execute"]
          users: []
    input_stack:
      description: "Capture task details"
      inputs:
        # Information-only help text
        - id: "in001"
          slot_id: "e001.at999.in001"
          contextual_id: "go001.lo001.in001"
          source:
            type: "information"
            description: "Task Creation Instructions"
          required: false
          data_type: "string"
          ui_control: "oj-text"
          is_visible: true
          metadata:
            usage: ""
          validations: []
          nested_function:
            id: "nf001"
            function_name: "to_uppercase"
            function_type: "transform"
            parameters:
              text: "Please provide detailed information about the task. Include a clear title, description, and set appropriate priority and due date."
            output_to: "info_text"

        # Auto-generated Task ID
        - id: "in002"
          slot_id: "e001.at101.in002"
          contextual_id: "go001.lo001.in002"
          source:
            type: "system"
            description: "Auto-generated Task ID"
          required: true
          data_type: "string"
          ui_control: "oj-input-text"
          metadata:
            usage: ""
          validations:
            - rule: "Task ID must be unique"
              rule_type: "validate_required"
              validation_method: "validate_required"
              error_message: "Task ID is required"
          nested_function:
            id: "nf002"
            function_name: "generate_id"
            function_type: "utility"
            parameters:
              entity: "e001"
              attribute: "at101"
              prefix: "TSK"
            output_to: "at101"

        # Task Title
        - id: "in003"
          slot_id: "e001.at102.in003"
          contextual_id: "go001.lo001.in003"
          source:
            type: "user"
            description: "Task Title"
          required: true
          data_type: "string"
          ui_control: "oj-input-text"
          metadata:
            usage: ""
          validations:
            - rule: "Title is required"
              rule_type: "validate_required"
              validation_method: "validate_required"
              error_message: "Task title cannot be empty"

        # Task Description
        - id: "in004"
          slot_id: "e001.at103.in004"
          contextual_id: "go001.lo001.in004"
          source:
            type: "user"
            description: "Task Description"
          required: false
          data_type: "string"
          ui_control: "oj-text-area"
          metadata:
            usage: ""
          validations: []

        # Category dropdown with function source
        - id: "in005"
          slot_id: "e001.at104.in005"
          contextual_id: "go001.lo001.in005"
          source:
            type: "user"
            description: "Task Category"
          required: true
          data_type: "string"
          ui_control: "oj-combobox-one"
          has_dropdown_source: true
          metadata:
            usage: ""
          validations:
            - rule: "Category is required"
              rule_type: "validate_required"
              validation_method: "validate_required"
              error_message: "Category is required"
          dropdown_source:
            source_type: "function"
            function_name: "fetch_filtered_records"
            function_params:
              table: "workflow_runtime.task_category"
              filter_column: "active"
              filter_value: true
              value_column: "category_id"
              display_column: "category_name"
              additional_filters: {}
            value_field: "value"
            display_field: "display"

        # Sub-Category dropdown - dependent on Category
        - id: "in006a"
          slot_id: "e001.at113.in006a"
          contextual_id: "go001.lo001.in006a"
          source:
            type: "system_dependent"
            description: "Task Sub-Category"
          required: false
          data_type: "string"
          ui_control: "oj-combobox-one"
          dependencies: ["in005"]
          dependency_type: "dropdown"
          has_dropdown_source: true
          metadata:
            usage: ""
          validations: []
          dropdown_source:
            source_type: "function"
            function_name: "fetch_filtered_records"
            function_params:
              table: "workflow_runtime.task_sub_category"
              filter_column: "parent_category_id"
              filter_value: "${in005}"
              value_column: "sub_category_id"
              display_column: "sub_category_name"
              additional_filters: 
                active: true
            value_field: "value"
            display_field: "display"
            depends_on_fields: ["in005"]

        # Priority
        - id: "in006"
          slot_id: "e001.at105.in006"
          contextual_id: "go001.lo001.in006"
          source:
            type: "user"
            description: "Priority"
          required: true
          data_type: "enum"
          allowed_values: ["Low", "Medium", "High", "Critical"]
          ui_control: "oj-select-single"
          metadata:
            usage: ""
          validations:
            - rule: "Priority must be valid"
              rule_type: "enum_check"
              validation_method: "enum_check"
              allowed_values: ["Low", "Medium", "High", "Critical"]
              error_message: "Invalid priority value"

        # Status - auto-set to Open
        - id: "in007"
          slot_id: "e001.at106.in007"
          contextual_id: "go001.lo001.in007"
          source:
            type: "system"
            description: "Status"
          required: true
          data_type: "enum"
          allowed_values: ["Open", "In Progress", "Blocked", "Completed"]
          ui_control: "oj-select-single"
          metadata:
            usage: ""
          validations:
            - rule: "Status must be valid"
              rule_type: "enum_check"
              validation_method: "enum_check"
              allowed_values: ["Open", "In Progress", "Blocked", "Completed"]
              error_message: "Invalid status value"
          nested_function:
            id: "nf003"
            function_name: "to_uppercase"
            function_type: "transform"
            parameters:
              text: "Open"
            output_to: "at106"

        # Assigned To (Optional)
        - id: "in008"
          slot_id: "e001.at107.in008"
          contextual_id: "go001.lo001.in008"
          source:
            type: "user"
            description: "Assigned To"
          required: false
          data_type: "string"
          ui_control: "oj-combobox-one"
          has_dropdown_source: true
          metadata:
            usage: ""
          validations: []
          dropdown_source:
            source_type: "function"
            function_name: "fetch_filtered_records"
            function_params:
              table: "workflow_runtime.users"
              filter_column: "status"
              filter_value: "active"
              value_column: "user_id"
              display_column: "username"
              additional_filters:
                disabled: false
            value_field: "value"
            display_field: "display"

        # Created By (current user)
        - id: "in009"
          slot_id: "e001.at108.in009"
          contextual_id: "go001.lo001.in009"
          source:
            type: "system"
            description: "Created By"
          required: true
          data_type: "string"
          ui_control: "oj-input-text"
          metadata:
            usage: ""
          validations:
            - rule: "Creator is required"
              rule_type: "validate_required"
              validation_method: "validate_required"
              error_message: "Created By cannot be empty"

        # Created Date
        - id: "in010"
          slot_id: "e001.at109.in010"
          contextual_id: "go001.lo001.in010"
          source:
            type: "system"
            description: "Created Date"
          required: true
          data_type: "date"
          ui_control: "oj-input-date"
          metadata:
            usage: ""
          validations:
            - rule: "Created Date is required"
              rule_type: "validate_required"
              validation_method: "validate_required"
              error_message: "Created Date cannot be empty"
          nested_function:
            id: "nf004"
            function_name: "current_timestamp"
            function_type: "utility"
            parameters: {}
            output_to: "at109"

        # Due Date
        - id: "in011"
          slot_id: "e001.at110.in011"
          contextual_id: "go001.lo001.in011"
          source:
            type: "user"
            description: "Due Date"
          required: true
          data_type: "date"
          ui_control: "oj-input-date"
          metadata:
            usage: ""
          validations:
            - rule: "Due Date is required"
              rule_type: "validate_required"
              validation_method: "validate_required"
              error_message: "Due Date cannot be empty"

        # Comments (Optional)
        - id: "in012"
          slot_id: "e001.at112.in012"
          contextual_id: "go001.lo001.in012"
          source:
            type: "user"
            description: "Comments"
          required: false
          data_type: "string"
          ui_control: "oj-text-area"
          metadata:
            usage: ""
          validations: []

    output_stack:
      description: "Task created"
      outputs:
        - id: "out001"
          slot_id: "executionstatus.out001"
          contextual_id: "go001.lo001.out001"
          source:
            type: "system"
            description: "Success or failure"
          data_type: "string"

        - id: "out002"
          slot_id: "e001.at101.out002"
          contextual_id: "go001.lo001.out002"
          source:
            type: "system"
            description: "Task ID"
          data_type: "string"

        - id: "out003"
          slot_id: "e001.at102.out003"
          contextual_id: "go001.lo001.out003"
          source:
            type: "system"
            description: "Task Title"
          data_type: "string"

        - id: "out004"
          slot_id: "e001.at103.out004"
          contextual_id: "go001.lo001.out004"
          source:
            type: "system"
            description: "Task Description"
          data_type: "string"

        - id: "out005"
          slot_id: "e001.at104.out005"
          contextual_id: "go001.lo001.out005"
          source:
            type: "system"
            description: "Category ID"
          data_type: "string"

        - id: "out006"
          slot_id: "e001.at105.out006"
          contextual_id: "go001.lo001.out006"
          source:
            type: "system"
            description: "Priority"
          data_type: "enum"

        - id: "out007"
          slot_id: "e001.at106.out007"
          contextual_id: "go001.lo001.out007"
          source:
            type: "system"
            description: "Status"
          data_type: "enum"

        - id: "out008"
          slot_id: "e001.at107.out008"
          contextual_id: "go001.lo001.out008"
          source:
            type: "system"
            description: "Assigned To"
          data_type: "string"

        - id: "out009"
          slot_id: "e001.at108.out009"
          contextual_id: "go001.lo001.out009"
          source:
            type: "system"
            description: "Created By"
          data_type: "string"

        - id: "out010"
          slot_id: "e001.at109.out010"
          contextual_id: "go001.lo001.out010"
          source:
            type: "system"
            description: "Created Date"
          data_type: "date"

        - id: "out011"
          slot_id: "e001.at110.out011"
          contextual_id: "go001.lo001.out011"
          source:
            type: "system"
            description: "Due Date"
          data_type: "date"
          
        - id: "out012"
          slot_id: "e001.at112.out012"
          contextual_id: "go001.lo001.out012"
          source:
            type: "system"
            description: "Comments"
          data_type: "string"
          
        - id: "out012a"
          slot_id: "e001.at113.out012a"
          contextual_id: "go001.lo001.out012a"
          source:
            type: "system"
            description: "Sub Category ID"
          data_type: "string"

    data_mapping_stack:
      description: "Data handover between LOs"
      mappings:
        - id: "map001"
          source: "lo001.out002"
          target: "lo002.in013"
          mapping_type: "direct"
        - id: "map002"
          source: "lo001.out003"
          target: "lo002.in014"
          mapping_type: "direct"
        - id: "map003"
          source: "lo001.out004"
          target: "lo002.in015"
          mapping_type: "direct"
        - id: "map004"
          source: "lo001.out005"
          target: "lo002.in016"
          mapping_type: "direct"
        - id: "map005"
          source: "lo001.out006"
          target: "lo002.in017"
          mapping_type: "direct"
        - id: "map006"
          source: "lo001.out007"
          target: "lo002.in018"
          mapping_type: "direct"
        - id: "map007"
          source: "lo001.out008"
          target: "lo002.in019"
          mapping_type: "direct"
        - id: "map008"
          source: "lo001.out009"
          target: "lo002.in020"
          mapping_type: "direct"
        - id: "map009"
          source: "lo001.out010"
          target: "lo002.in021"
          mapping_type: "direct"
        - id: "map010"
          source: "lo001.out011"
          target: "lo002.in022"
          mapping_type: "direct"
        - id: "map011"
          source: "lo001.out012"
          target: "lo002.in023"
          mapping_type: "direct"
        - id: "map011a"
          source: "lo001.out012a"
          target: "lo002.in023a"
          mapping_type: "direct"
        
        - id: "map012"
          source: "lo001.out002"
          target: "lo003.in025"
          mapping_type: "direct"

    execution_pathway:
      type: "alternate"
      conditions:
        - condition:
            condition_type: "attribute_comparison"
            entity: "e001"
            attribute: "at107"
            operator: "is_not_empty"
            value: ""
          next_lo: "lo002"
        - condition:
            condition_type: "attribute_comparison"
            entity: "e001"
            attribute: "at107"
            operator: "is_empty"
            value: ""
          next_lo: "lo003"

  - id: "lo002"
    contextual_id: "go001.lo002"
    name: "Update Task Status"
    workflow_source: "intermediate"
    function_type: "Update"
    agent_stack:
      agents:
        - role: "r002"  # Team Lead role
          rights: ["Execute", "Update"]
          users: []
        - role: "r003"  # Admin role
          rights: ["Execute", "Update"]
          users: []
    input_stack:
      description: "Update task status"
      inputs:
        # Information policy message
        - id: "in024"
          slot_id: "e001.at999.in024"
          contextual_id: "go001.lo002.in024"
          source:
            type: "information"
            description: "Task Update Instructions"
          required: false
          data_type: "string"
          ui_control: "oj-text"
          is_visible: true
          metadata:
            usage: ""
          validations: []
          nested_function:
            id: "nf005"
            function_name: "to_uppercase"
            function_type: "transform"
            parameters:
              text: "Update the task status and add any necessary comments. When marking a task as 'Completed', the completion date will be automatically set."
            output_to: "info_text"

        # Task ID for lookup
        - id: "in013"
          slot_id: "e001.at101.in013"
          contextual_id: "go001.lo002.in013"
          source:
            type: "system"
            description: "Task ID"
          required: true
          data_type: "string"
          ui_control: "oj-input-text"
          metadata:
            usage: "lookup"
          validations:
            - rule: "Task ID must be present"
              rule_type: "validate_required"
              validation_method: "validate_required"
              error_message: "Task ID is required"
            - rule: "Task ID must exist"
              rule_type: "entity_exists"
              validation_method: "entity_exists"
              entity: "e001"
              attribute: "at101"
              error_message: "Task ID not found in the system"
              
        # Task Title
        - id: "in014"
          slot_id: "e001.at102.in014"
          contextual_id: "go001.lo002.in014"
          source:
            type: "system"
            description: "Task Title"
          required: true
          data_type: "string"
          ui_control: "oj-input-text"
          metadata:
            usage: "lookup"
          validations:
            - rule: "Title is required"
              rule_type: "validate_required"
              validation_method: "validate_required"
              error_message: "Title is required"
              
        # Description
        - id: "in015"
          slot_id: "e001.at103.in015"
          contextual_id: "go001.lo002.in015"
          source:
            type: "system"
            description: "Description"
          required: false
          data_type: "string"
          ui_control: "oj-text-area"
          metadata:
            usage: "lookup"
          validations: []
              
        # Category
        - id: "in016"
          slot_id: "e001.at104.in016"
          contextual_id: "go001.lo002.in016"
          source:
            type: "system"
            description: "Category"
          required: true
          data_type: "string"
          ui_control: "oj-input-text"
          metadata:
            usage: "lookup"
          validations:
            - rule: "Category is required"
              rule_type: "validate_required"
              validation_method: "validate_required"
              error_message: "Category is required"
        
        # Sub Category
        - id: "in023a"
          slot_id: "e001.at113.in023a"
          contextual_id: "go001.lo002.in023a"
          source:
            type: "system"
            description: "Sub Category"
          required: false
          data_type: "string"
          ui_control: "oj-input-text"
          metadata:
            usage: "lookup"
          validations: []
              
        # Priority
        - id: "in017"
          slot_id: "e001.at105.in017"
          contextual_id: "go001.lo002.in017"
          source:
            type: "system"
            description: "Priority"
          required: true
          data_type: "enum"
          ui_control: "oj-select-single"
          metadata:
            usage: "lookup"
          validations:
            - rule: "Priority is required"
              rule_type: "validate_required"
              validation_method: "validate_required"
              error_message: "Priority is required"
              
        # Status to be updated
        - id: "in018"
          slot_id: "e001.at106.in018"
          contextual_id: "go001.lo002.in018"
          source:
            type: "user"
            description: "Status"
          required: true
          data_type: "enum"
          allowed_values: ["Open", "In Progress", "Blocked", "Completed"]
          ui_control: "oj-select-single"
          metadata:
            usage: "update"
          validations:
            - rule: "Status must be valid"
              rule_type: "enum_check"
              validation_method: "enum_check"
              allowed_values: ["Open", "In Progress", "Blocked", "Completed"]
              error_message: "Invalid status value"
        
        # Assigned To
        - id: "in019"
          slot_id: "e001.at107.in019"
          contextual_id: "go001.lo002.in019"
          source:
            type: "system"
            description: "Assigned To"
          required: false
          data_type: "string"
          ui_control: "oj-input-text"
          metadata:
            usage: "lookup"
          validations: []
              
        # Created By
        - id: "in020"
          slot_id: "e001.at108.in020"
          contextual_id: "go001.lo002.in020"
          source:
            type: "system"
            description: "Created By"
          required: true
          data_type: "string"
          ui_control: "oj-input-text"
          metadata:
            usage: "lookup"
          validations:
            - rule: "Created By is required"
              rule_type: "validate_required"
              validation_method: "validate_required"
              error_message: "Created By is required"
              
        # Created Date
        - id: "in021"
          slot_id: "e001.at109.in021"
          contextual_id: "go001.lo002.in021"
          source:
            type: "system"
            description: "Created Date"
          required: true
          data_type: "date"
          ui_control: "oj-input-date"
          metadata:
            usage: "lookup"
          validations:
            - rule: "Created Date is required"
              rule_type: "validate_required"
              validation_method: "validate_required"
              error_message: "Created Date is required"
        
        # Due Date
        - id: "in022"
          slot_id: "e001.at110.in022"
          contextual_id: "go001.lo002.in022"
          source:
            type: "system"
            description: "Due Date"
          required: true
          data_type: "date"
          ui_control: "oj-input-date"
          metadata:
            usage: "lookup"
          validations:
            - rule: "Due Date is required"
              rule_type: "validate_required"
              validation_method: "validate_required"
              error_message: "Due Date is required"
              
        # Comments
        - id: "in023"
          slot_id: "e001.at112.in023"
          contextual_id: "go001.lo002.in023"
          source:
            type: "system"
            description: "Comments"
          required: false
          data_type: "string"
          ui_control: "oj-text-area"
          metadata:
            usage: "lookup"
          validations: []
          
        # System-dependent: Completion Date (if status = "Completed")
        - id: "in024"
          slot_id: "e001.at111.in024"
          contextual_id: "go001.lo002.in024"
          source:
            type: "system_dependent"
            description: "Completion Date"
          required: false
          data_type: "date"
          ui_control: "oj-input-date"
          dependencies: ["in018"]
          dependency_type: "calculation"
          metadata:
            usage: "update"
          validations: []
          nested_function:
            id: "nf006"
            function_name: "current_timestamp"
            function_type: "utility"
            parameters: {}
            output_to: "at111"

    output_stack:
      description: "Task status updated"
      outputs:
        - id: "out013"
          slot_id: "executionstatus.out013"
          contextual_id: "go001.lo002.out013"
          source:
            type: "system"
            description: "Success or failure"
          data_type: "string"
          
        - id: "out014"
          slot_id: "e001.at101.out014"
          contextual_id: "go001.lo002.out014"
          source:
            type: "system"
            description: "Task ID"
          data_type: "string"
          
        - id: "out015"
          slot_id: "e001.at102.out015"
          contextual_id: "go001.lo002.out015"
          source:
            type: "system"
            description: "Task Title"
          data_type: "string"
          
        - id: "out016"
          slot_id: "e001.at103.out016"
          contextual_id: "go001.lo002.out016"
          source:
            type: "system"
            description: "Description"
          data_type: "string"
          
        - id: "out017"
          slot_id: "e001.at104.out017"
          contextual_id: "go001.lo002.out017"
          source:
            type: "system"
            description: "Category"
          data_type: "string"
          
        - id: "out018"
          slot_id: "e001.at105.out018"
          contextual_id: "go001.lo002.out018"
          source:
            type: "system"
            description: "Priority"
          data_type: "enum"
          
        - id: "out019"
          slot_id: "e001.at106.out019"
          contextual_id: "go001.lo002.out019"
          source:
            type: "system"
            description: "Status"
          data_type: "enum"
          
        - id: "out020"
          slot_id: "e001.at107.out020"
          contextual_id: "go001.lo002.out020"
          source:
            type: "system"
            description: "Assigned To"
          data_type: "string"
          
        - id: "out021"
          slot_id: "e001.at108.out021"
          contextual_id: "go001.lo002.out021"
          source:
            type: "system"
            description: "Created By"
          data_type: "string"
          
        - id: "out022"
          slot_id: "e001.at109.out022"
          contextual_id: "go001.lo002.out022"
          source:
            type: "system"
            description: "Created Date"
          data_type: "date"
          
        - id: "out023"
          slot_id: "e001.at110.out023"
          contextual_id: "go001.lo002.out023"
          source:
            type: "system"
            description: "Due Date"
          data_type: "date"
          
        - id: "out024"
          slot_id: "e001.at111.out024"
          contextual_id: "go001.lo002.out024"
          source:
            type: "system"
            description: "Completion Date"
          data_type: "date"
          
        - id: "out025"
          slot_id: "e001.at112.out025"
          contextual_id: "go001.lo002.out025"
          source:
            type: "system"
            description: "Comments"
          data_type: "string"
          
        - id: "out025a"
          slot_id: "e001.at113.out025a"
          contextual_id: "go001.lo002.out025a"
          source:
            type: "system"
            description: "Sub Category"
          data_type: "string"

    data_mapping_stack:
      description: "Data handover between LOs"
      mappings:
        - id: "map013"
          source: "lo002.out014"
          target: "lo003.in025"
          mapping_type: "direct"

    execution_pathway:
      type: "sequential"
      next_lo: "lo003"

  - id: "lo003"
    contextual_id: "go001.lo003"
    name: "Complete Task"
    workflow_source: "terminal"
    function_type: "Update"
    agent_stack:
      agents:
        - role: "r002"  # Team Lead role
          rights: ["Execute", "Update"]
          users: []
        - role: "r003"  # Admin role
          rights: ["Execute", "Update"]
          users: []
    input_stack:
      description: "Complete task"
      inputs:
        # Task ID for lookup
        - id: "in025"
          slot_id: "e001.at101.in025"
          contextual_id: "go001.lo003.in025"
          source:
            type: "system"
            description: "Task ID"
          required: true
          data_type: "string"
          ui_control: "oj-input-text"
          metadata:
            usage: "lookup"
          validations:
            - rule: "Task ID must be present"
              rule_type: "validate_required"
              validation_method: "validate_required"
              error_message: "Task ID is required"
            - rule: "Task ID must exist"
              rule_type: "entity_exists"
              validation_method: "entity_exists"
              entity: "e001"
              attribute: "at101"
              error_message: "Task ID not found in the system"
              
        # Status verification - Only Completed tasks can be finalized
        - id: "in026"
          slot_id: "e001.at106.in026"
          contextual_id: "go001.lo003.in026"
          source:
            type: "user"
            description: "Final Status"
          required: true
          data_type: "enum"
          allowed_values: ["Completed"]
          ui_control: "oj-select-single"
          metadata:
            usage: "update"
          validations:
            - rule: "Status must be Completed"
              rule_type: "enum_check"
              validation_method: "enum_check"
              allowed_values: ["Completed"]
              error_message: "Status must be set to Completed to finalize"
              
        # Additional Comments
        - id: "in027"
          slot_id: "e001.at112.in027"
          contextual_id: "go001.lo003.in027"
          source:
            type: "user"
            description: "Final Comments"
          required: false
          data_type: "string"
          ui_control: "oj-text-area"
          metadata:
            usage: "update"
          validations: []

    output_stack:
      description: "Task completed"
      outputs:
        - id: "out026"
          slot_id: "executionstatus.out026"
          contextual_id: "go001.lo003.out026"
          source:
            type: "system"
            description: "Success or failure"
          data_type: "string"
          
        - id: "out027"
          slot_id: "e001.at101.out027"
          contextual_id: "go001.lo003.out027"
          source:
            type: "system"
            description: "Task ID"
          data_type: "string"
          
        - id: "out028"
          slot_id: "e001.at106.out028"
          contextual_id: "go001.lo003.out028"
          source:
            type: "system"
            description: "Status"
          data_type: "enum"
          
        - id: "out029"
          slot_id: "e001.at112.out029"
          contextual_id: "go001.lo003.out029"
          source:
            type: "system"
            description: "Comments"
          data_type: "string"

    data_mapping_stack:
      description: "Data handover between LOs"
      mappings: []

    execution_pathway:
      type: "terminal"
      next_lo: ""

