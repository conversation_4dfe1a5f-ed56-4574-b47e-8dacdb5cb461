#!/usr/bin/env python3
"""
Sc<PERSON>t to verify that attribute properties are correctly parsed and match what's in the database.
"""

import os
import logging
from parsers.entity_parser import parse_entities
from deployers.entity_deployer_v2 import execute_query

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger('verify_attribute_properties')

def verify_attribute_properties():
    """
    Verify that attribute properties are correctly parsed and match what's in the database.
    """
    # Read the sample entity output file
    sample_file_path = '/home/<USER>/workflow-system/chat-yaml-builder/v2/samples/sample_entity_output2.txt'
    with open(sample_file_path, 'r') as f:
        entity_def = f.read()
    
    # Parse the entity definition
    entities_data, warnings = parse_entities(entity_def)
    
    # Print any warnings
    if warnings:
        logger.warning("Warnings during parsing:")
        for warning in warnings:
            logger.warning(f"  - {warning}")
    
    # Check if Employee entity was parsed
    if 'entities' in entities_data and 'Employee' in entities_data['entities']:
        employee_entity = entities_data['entities']['Employee']
        
        # Check if attributes were parsed
        if 'attributes' in employee_entity:
            logger.info("\nParsed Employee attributes:")
            parsed_attributes = {}
            for attr_name, attr_def in employee_entity['attributes'].items():
                if attr_name in ['probationDays', 'minSalary', 'status', 'hireDate']:
                    logger.info(f"\n  - Attribute: {attr_name}")
                    
                    # Check for default value
                    if 'default' in attr_def:
                        logger.info(f"    - Default Value: {attr_def['default']}")
                        parsed_attributes[attr_name] = {'default_value': attr_def['default']}
                    
                    # Check for property value
                    if 'property_value' in attr_def:
                        logger.info(f"    - Property Value: {attr_def['property_value']}")
                        if attr_name not in parsed_attributes:
                            parsed_attributes[attr_name] = {}
                        parsed_attributes[attr_name]['property_value'] = attr_def['property_value']
        else:
            logger.error("No attributes found in Employee entity")
            return
    else:
        logger.error("Employee entity not found in parsed data")
        return
    
    # Get the Employee entity ID from the database
    schema_name = 'workflow_temp'
    success, messages, result = execute_query(
        f"SELECT entity_id FROM {schema_name}.entities WHERE name = 'Employee'",
        schema_name=schema_name
    )
    
    if not success or not result:
        logger.error("Employee entity not found in the database")
        return
    
    employee_id = result[0][0]
    logger.info(f"Found Employee entity in database with ID: {employee_id}")
    
    # Get the attributes from the database
    success, messages, result = execute_query(
        f"""
        SELECT 
            name, 
            type,
            default_value,
            display_name
        FROM 
            {schema_name}.entity_attributes
        WHERE 
            entity_id = %s AND
            name IN ('status', 'hireDate', 'probationDays', 'minSalary')
        """,
        (employee_id,),
        schema_name
    )
    
    if success and result:
        logger.info("\nEmployee attributes in the database:")
        db_attributes = {}
        for row in result:
            attr_name = row[0]
            attr_type = row[1]
            default_value = row[2]
            display_name = row[3]
            logger.info(f"  - Attribute: {attr_name}")
            logger.info(f"    - Type: {attr_type}")
            logger.info(f"    - Default Value: {default_value}")
            logger.info(f"    - Display Name: {display_name}")
            db_attributes[attr_name] = {'default_value': default_value}
    else:
        logger.error("No Employee attributes found in the database")
        return
    
    # Compare parsed attributes with database attributes
    logger.info("\nComparing parsed attributes with database attributes:")
    for attr_name in ['status', 'hireDate', 'probationDays', 'minSalary']:
        if attr_name in parsed_attributes and attr_name in db_attributes:
            parsed_default = parsed_attributes[attr_name].get('default_value', None)
            db_default = db_attributes[attr_name].get('default_value', None)
            
            if parsed_default == db_default:
                logger.info(f"  - {attr_name}: MATCH (Default Value: {db_default})")
            else:
                logger.warning(f"  - {attr_name}: MISMATCH (Parsed: {parsed_default}, DB: {db_default})")
            
            # For probationDays and minSalary, check property_value
            if attr_name in ['probationDays', 'minSalary']:
                parsed_property = parsed_attributes[attr_name].get('property_value', None)
                if parsed_property and str(parsed_property) == str(db_default):
                    logger.info(f"    - Property Value: MATCH (Value: {parsed_property})")
                elif parsed_property:
                    logger.warning(f"    - Property Value: MISMATCH (Parsed: {parsed_property}, DB: {db_default})")
        else:
            if attr_name not in parsed_attributes:
                logger.warning(f"  - {attr_name}: NOT FOUND in parsed attributes")
            if attr_name not in db_attributes:
                logger.warning(f"  - {attr_name}: NOT FOUND in database attributes")

if __name__ == "__main__":
    verify_attribute_properties()
