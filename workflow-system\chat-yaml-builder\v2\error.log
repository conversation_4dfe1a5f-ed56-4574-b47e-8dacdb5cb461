2025-05-15 12:03:37,312 - entity_parser - INFO - Starting to parse entity definitions
2025-05-15 12:03:37,314 - entity_parser - INFO - Parsing entity: Employee
2025-05-15 12:03:37,314 - entity_parser - INFO - Found enum values for attribute 'status': ['Active', 'Inactive', 'OnLeave']
2025-05-15 12:03:37,314 - entity_parser - INFO - Added default value 'Active' to attribute 'status' in entity 'Employee'
2025-05-15 12:03:37,314 - entity_parser - INFO - Added default value 'CURRENT_DATE' to attribute 'hireDate' in entity 'Employee'
2025-05-15 12:03:37,314 - entity_parser - INFO - Rest of block for entity 'Employee':
* Employee has many-to-one relationship with Department using Employee.departmentId to Department.departmentId^PK
* Employee has many-to-one relationship with Employee using Employee.managerId to Employee.employeeId^PK

* Employee.probationDays PROPERTY_NAME = 90
* Employee.minSalary PROPERTY_NAME = 30000
* Employee.status DEFAULT_VALUE = "Active"
* Employee.hireDate DEFAULT_VALUE = CURRENT_DATE

* Employee.email must be unique
* Employee.email must match pattern "^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$"
* Employee.hireDate must be before current date
* Employee.salary must be greater than 0

BusinessRule Employee:
* Employee.status must be Active to receive performance reviews
* Employee.hireDate must be at least 90 days before Employee.performanceRating can be set
2025-05-15 12:03:37,314 - entity_parser - INFO - Added default value 'Active' to attribute 'status' in entity 'Employee'
2025-05-15 12:03:37,315 - entity_parser - INFO - Parsed relationship: Employee has many-to-one relationship with Department using Employee.departmentId to Department.departmentId
2025-05-15 12:03:37,315 - entity_parser - INFO - Parsed relationship: Employee has many-to-one relationship with Employee using Employee.managerId to Employee.employeeId
2025-05-15 12:03:37,315 - entity_parser - INFO - Successfully parsed entity 'Employee'
2025-05-15 12:03:37,315 - entity_parser - INFO - Parsing entity: Employee
2025-05-15 12:03:37,316 - entity_parser - INFO - Rest of block for entity 'Employee':
CalculatedField for Employee.fullName:
* Formula: CONCAT(firstName, ' ', lastName)
* Logic Layer: Application
* Caching: Session
* Dependencies: Employee.firstName, Employee.lastName
2025-05-15 12:03:37,316 - entity_parser - INFO - Successfully parsed entity 'Employee'
2025-05-15 12:03:37,316 - entity_parser - INFO - Parsing entity: Employee
2025-05-15 12:03:37,316 - entity_parser - INFO - Rest of block for entity 'Employee':
* Employee.managerId must belong to selected Employee.departmentId


Entity Additional Properties:
Display Name: Company Employee
Type: Core Entity
Description: Represents an employee within the organization

Attribute Additional Properties:
Attribute name: email
Key: Unique
Display Name: Email Address
Data Type: String
Type: Mandatory
Format: "<EMAIL>"
Values: N/A
Default: "@company.com"
Validation: Regex Pattern
Error Message: "Please enter a valid email address"
Description: Employee's primary email address for communications

Relationship: Employee to Department

Relationship Properties:
On Delete: Restrict (Prevent deletion of department with active employees)
On Update: Cascade (Update employee records when department details change)
Foreign Key Type: Non-Nullable


* Confidential: Employee.salary, Employee.performanceRating
* Internal: Employee.hireDate, Employee.departmentId, Employee.managerId
* Public: Employee.firstName, Employee.lastName, Employee.status

* Loading for Employee.Department: Eager Loading
* Loading for Employee.Manager: Lazy Loading

* Archive Strategy for Employee:
  - Trigger: Event-based
  - Criteria: When Employee.status changes to 'Inactive' and remains so for 1 year
  - Retention: 7 years
  - Storage: Cold storage
  - Access Pattern: Read-only through HR archive portal
  - Restoration: Manual process requiring HR Director approval

* Purge Rule for Employee:
  - Trigger: Time-based (annual evaluation)
  - Criteria: archivedDate < (CURRENT_DATE - (7 * 365)) AND Regulatory.retentionExpired = true
  - Approvals: DataGovernance AND HRDirector
  - Audit: Full metadata retention with deletion certificate
  - Dependencies: EmployeeDocument (cascade), PayrollRecord (restrict)

* History Tracking for Employee:
  - Tracked Attributes: Employee.salary, Employee.departmentId, Employee.managerId, Employee.status
  - Tracking Method: Audit table
  - Granularity: Change level
  - Retention: 7 years
  - Access Control: HR Managers and Compliance Officers only

* Workflow: EmployeeOnboarding for Employee
  - States: New, DocumentsVerified, AccessProvisioned, TrainingCompleted, Active
  - Transitions:
    - New → DocumentsVerified [HR_Specialist, HR_Manager]
    - DocumentsVerified → AccessProvisioned [IT_Admin, IT_Manager]
    - AccessProvisioned → TrainingCompleted [Training_Specialist]
    - TrainingCompleted → Active [Department_Manager, HR_Manager]
  - Actions: VerifyDocuments, ProvisionAccess, AssignTraining, CompleteOnboarding

BusinessRule Placement:
* Local Objective: EmployeeManagement
* Global Objective: EmployeeLifecycle
* Chapter: HumanResources
* Book: CompanyOperations
* Tenant: CorporateHQ

* Workflow EmployeeOnboarding Placement:
  - Global Objective: EmployeeLifecycle
  - Book: CompanyOperations
  - Chapter: HumanResources
  - Tenant: CorporateHQ

* Entity Placement for Employee:
  - Tenant: CorporateHQ
  - Book: CompanyOperations
  - Chapter: HumanResources
  - Global Objectives: EmployeeLifecycle, PayrollProcessing
  - Local Objectives: EmployeeManagement, TimeTracking
2025-05-15 12:03:37,316 - entity_parser - INFO - Added relationship property 'on_delete': 'RESTRICT' to new relationship 'many-to-one_Department'
2025-05-15 12:03:37,316 - entity_parser - INFO - Added relationship property 'on_update': 'CASCADE' to new relationship 'many-to-one_Department'
2025-05-15 12:03:37,316 - entity_parser - INFO - Added relationship property 'foreign_key_type': 'NON-NULLABLE' to new relationship 'many-to-one_Department'
2025-05-15 12:03:37,316 - entity_parser - INFO - Added relationship property 'on_delete': 'Restrict (Prevent deletion of department with active employees)' to new relationship 'many-to-one_Department'
2025-05-15 12:03:37,316 - entity_parser - INFO - Added relationship property 'on_update': 'Cascade (Update employee records when department details change)' to new relationship 'many-to-one_Department'
2025-05-15 12:03:37,316 - entity_parser - INFO - Added relationship property 'foreign_key_type': 'Non-Nullable' to new relationship 'many-to-one_Department'
2025-05-15 12:03:37,317 - entity_parser - INFO - Successfully parsed entity 'Employee'
2025-05-15 12:03:37,317 - entity_parser - INFO - Parsing entity: Department
2025-05-15 12:03:37,317 - entity_parser - INFO - Rest of block for entity 'Department':
* Department has one-to-one relationship with Employee using Department.managerId to Employee.employeeId^PK
* Department has one-to-many relationship with Employee using Department.departmentId to Employee.departmentId^FK

* Department.minBudget PROPERTY_NAME = 10000
* Department.location DEFAULT_VALUE = "Headquarters"

* Department.name must be unique
* Department.budget must be greater than 0

BusinessRule for Department:
* Department.budget must be approved by Finance before changes
* Department.managerId must reference an Employee with status = 'Active'
2025-05-15 12:03:37,317 - entity_parser - INFO - Parsed relationship: Department has one-to-one relationship with Employee using Department.managerId to Employee.employeeId
2025-05-15 12:03:37,317 - entity_parser - INFO - Parsed relationship: Department has one-to-many relationship with Employee using Department.departmentId to Employee.departmentId
2025-05-15 12:03:37,317 - entity_parser - INFO - Successfully parsed entity 'Department'
2025-05-15 12:03:37,317 - entity_parser - INFO - Parsing entity: Department
2025-05-15 12:03:37,317 - entity_parser - INFO - Rest of block for entity 'Department':
CalculatedField for Department.fullAddress:
* Formula: CONCAT(name, ' - ', location)
* Logic Layer: Application
* Caching: Session
* Dependencies: Department.name, Department.location
2025-05-15 12:03:37,317 - entity_parser - INFO - Successfully parsed entity 'Department'
2025-05-15 12:03:37,317 - entity_parser - INFO - Parsing entity: Department
2025-05-15 12:03:37,317 - entity_parser - INFO - Rest of block for entity 'Department':
* Department.locationId must be compatible with selected Department.managerId's office location

Entity Hover: Department

Editable Properties:
Display Name: Business Department
Type: Core Entity
Description: Represents a department within the organization

Relationship Hover: Department to Employee

Relationship Properties:
On Delete: Restrict (Prevent deletion of department with active employees)
On Update: Cascade (Update employee records when department details change)
Foreign Key Type: Non-Nullable


* Confidential: Department.budget
* Internal: Department.managerId
* Public: Department.name, Department.location

* Loading for Department.Employees: Lazy Loading
* Loading for Department.Manager: Eager Loading

* Archive Strategy for Department:
  - Trigger: Event-based
  - Criteria: When Department is marked as 'Dissolved'
  - Retention: 10 years
  - Storage: Cold storage
  - Access Pattern: Read-only through Admin portal
  - Restoration: Manual process requiring CEO approval

* Purge Rule for Department:
  - Trigger: Time-based (biennial evaluation)
  - Criteria: archivedDate < (CURRENT_DATE - (10 * 365)) AND noActiveReferences = true
  - Approvals: ExecutiveTeam AND ComplianceOfficer
  - Audit: Full historical record with executive approval documentation
  - Dependencies: Employee (verify no references), Budget (archive), DepartmentDocument (cascade)

* History Tracking for Department:
  - Tracked Attributes: Department.name, Department.managerId, Department.budget
  - Tracking Method: Temporal tables
  - Granularity: Snapshot level
  - Retention: 10 years
  - Access Control: Finance Managers and Executive Team only

BusinessRule Placement:
* Local Objective: DepartmentManagement
* Global Objective: OrganizationalStructure
* Chapter: Administration
* Book: CompanyOperations
* Tenant: CorporateHQ

* Entity Placement for Department:
  - Tenant: CorporateHQ
  - Book: CompanyOperations
  - Chapter: Administration
  - Global Objectives: OrganizationalStructure, BudgetPlanning
  - Local Objectives: DepartmentManagement, ResourceAllocation
2025-05-15 12:03:37,318 - entity_parser - INFO - Successfully parsed entity 'Department'
2025-05-15 12:03:37,318 - entity_parser - INFO - Parsing entity: LeaveType
2025-05-15 12:03:37,318 - entity_parser - INFO - Rest of block for entity 'LeaveType':
* LeaveType has one-to-many relationship with LeaveApplication using LeaveType.typeId to LeaveApplication.leaveTypeId^FK

* LeaveType.maxDuration DEFAULT_VALUE = 30
* LeaveType.requiresDocumentation DEFAULT_VALUE = false

* LeaveType.name must be unique
* LeaveType.maxDuration must be greater than 0

BusinessRule for LeaveType:
* LeaveType.requiresDocumentation must be true for medical leave types
* LeaveType with maxDuration > 90 requires HR Director approval

Entity Additional Properties:
Display Name: Leave Type
Type: Core Entity
Description: Represents a type of leave that employees can request


* Entity Placement for LeaveType:
  - Tenant: CorporateHQ
  - Book: CompanyOperations
  - Chapter: HumanResources
  - Global Objectives: LeaveManagement
  - Local Objectives: LeaveTypeManagement
2025-05-15 12:03:37,318 - entity_parser - INFO - Added default value '30' to attribute 'maxDuration' in entity 'LeaveType'
2025-05-15 12:03:37,318 - entity_parser - INFO - Parsed relationship: LeaveType has one-to-many relationship with LeaveApplication using LeaveType.typeId to LeaveApplication.leaveTypeId
2025-05-15 12:03:37,318 - entity_parser - INFO - Successfully parsed entity 'LeaveType'
2025-05-15 12:03:37,318 - entity_parser - INFO - Parsing entity: LeaveApplication
2025-05-15 12:03:37,318 - entity_parser - INFO - Rest of block for entity 'LeaveApplication':
* LeaveApplication has many-to-one relationship with Employee using LeaveApplication.employeeId to Employee.employeeId^PK
* LeaveApplication has many-to-one relationship with LeaveType using LeaveApplication.leaveTypeId to LeaveType.typeId^PK
* LeaveApplication has one-to-one relationship with CalendarEvent using LeaveApplication.calendarEventId to CalendarEvent.eventId^PK

* LeaveApplication.status DEFAULT_VALUE = "Pending"
* LeaveApplication.numDays CALCULATED_FIELD = DATEDIFF(day, startDate, endDate) + 1

* LeaveApplication.startDate must be a valid date
* LeaveApplication.endDate must be after startDate
* LeaveApplication.numDays must be greater than 0
* LeaveApplication.status must be one of "Pending", "Approved", "Rejected"

BusinessRule for LeaveApplication:
* LeaveApplication.startDate must be at least 7 days in future for planned leave
* LeaveApplication.numDays must not exceed LeaveType.maxDuration
* LeaveApplication with status = "Approved" cannot be modified except by HR Manager

Entity Additional Properties:
Display Name: Leave Application
Type: Core Entity
Description: Represents an employee's request for leave


* Entity Placement for LeaveApplication:
  - Tenant: CorporateHQ
  - Book: CompanyOperations
  - Chapter: HumanResources
  - Global Objectives: LeaveManagement, LeaveApprovalProcess
  - Local Objectives: LeaveRequestManagement
2025-05-15 12:03:37,318 - entity_parser - INFO - Added default value 'Pending' to attribute 'status' in entity 'LeaveApplication'
2025-05-15 12:03:37,318 - entity_parser - INFO - Parsed relationship: LeaveApplication has many-to-one relationship with Employee using LeaveApplication.employeeId to Employee.employeeId
2025-05-15 12:03:37,318 - entity_parser - INFO - Parsed relationship: LeaveApplication has many-to-one relationship with LeaveType using LeaveApplication.leaveTypeId to LeaveType.typeId
2025-05-15 12:03:37,318 - entity_parser - INFO - Parsed relationship: LeaveApplication has one-to-one relationship with CalendarEvent using LeaveApplication.calendarEventId to CalendarEvent.eventId
2025-05-15 12:03:37,318 - entity_parser - INFO - Successfully parsed entity 'LeaveApplication'
2025-05-15 12:03:37,318 - entity_parser - INFO - Parsing entity: CalendarEvent
2025-05-15 12:03:37,319 - entity_parser - INFO - Rest of block for entity 'CalendarEvent':
* CalendarEvent has many-to-one relationship with Employee using CalendarEvent.employeeId to Employee.employeeId^PK
* CalendarEvent has one-to-one relationship with LeaveApplication using CalendarEvent.referenceId to LeaveApplication.leaveId^PK

* CalendarEvent.status DEFAULT_VALUE = "Scheduled"
* CalendarEvent.eventType DEFAULT_VALUE = "Leave"

* CalendarEvent.startDate must be a valid date
* CalendarEvent.endDate must be after startDate
* CalendarEvent.title must not be empty
* CalendarEvent.status must be one of "Scheduled", "Cancelled"

BusinessRule for CalendarEvent:
* CalendarEvent must not overlap with existing events for the same employee
* CalendarEvent must be created at least 1 hour in advance
* CalendarEvent with status = "Cancelled" must have a reason in description

Entity Additional Properties:
Display Name: Calendar Event
Type: Core Entity
Description: Represents an event in an employee's calendar


* Entity Placement for CalendarEvent:
  - Tenant: CorporateHQ
  - Book: CompanyOperations
  - Chapter: HumanResources
  - Global Objectives: EmployeeCalendarManagement
  - Local Objectives: CalendarEventManagement

BusinessRule EMP001 for Employee:
* Employee.status must be Active to receive performance reviews
* Employee.hireDate must be at least 90 days before Employee.performanceRating can be set
2025-05-15 12:03:37,319 - entity_parser - INFO - Added default value 'Scheduled' to attribute 'status' in entity 'CalendarEvent'
2025-05-15 12:03:37,319 - entity_parser - INFO - Parsed relationship: CalendarEvent has many-to-one relationship with Employee using CalendarEvent.employeeId to Employee.employeeId
2025-05-15 12:03:37,319 - entity_parser - INFO - Parsed relationship: CalendarEvent has one-to-one relationship with LeaveApplication using CalendarEvent.referenceId to LeaveApplication.leaveId
2025-05-15 12:03:37,319 - entity_parser - INFO - Successfully parsed entity 'CalendarEvent'
2025-05-15 12:03:37,319 - entity_parser - INFO - Successfully parsed 5 entities
2025-05-15 12:03:37,319 - test_business_rule_deployment - INFO - 
Employee business rules:
2025-05-15 12:03:37,319 - test_business_rule_deployment - INFO - 
  - Business Rule: Employee_Rule
2025-05-15 12:03:37,319 - test_business_rule_deployment - INFO -     - Condition 1: Employee.status must be Active to receive performance reviews
2025-05-15 12:03:37,319 - test_business_rule_deployment - INFO -     - Condition 2: Employee.hireDate must be at least 90 days before Employee.performanceRating can be set
2025-05-15 12:03:37,319 - entity_deployer - INFO - Deploying entities to workflow_temp
2025-05-15 12:03:37,319 - entity_deployer - INFO - Validating entity relationships in schema workflow_temp
2025-05-15 12:03:37,319 - entity_deployer - INFO - Found 5 entities: LeaveApplication, Employee, Department, CalendarEvent, LeaveType
2025-05-15 12:03:37,319 - entity_deployer - INFO - Validated relationship 'many-to-one_Employee' from 'LeaveApplication' to 'Employee'
2025-05-15 12:03:37,319 - entity_deployer - INFO - Validated relationship 'many-to-one_LeaveType' from 'LeaveApplication' to 'LeaveType'
2025-05-15 12:03:37,319 - entity_deployer - INFO - Validated relationship 'one-to-one_CalendarEvent' from 'LeaveApplication' to 'CalendarEvent'
2025-05-15 12:03:37,319 - entity_deployer - INFO - Validated relationship 'many-to-one_Department' from 'Employee' to 'Department'
2025-05-15 12:03:37,319 - entity_deployer - INFO - Validated relationship 'one-to-one_Employee' from 'Department' to 'Employee'
2025-05-15 12:03:37,319 - entity_deployer - INFO - Validated relationship 'one-to-many_Employee' from 'Department' to 'Employee'
2025-05-15 12:03:37,319 - entity_deployer - INFO - Validated relationship 'many-to-one_Employee' from 'CalendarEvent' to 'Employee'
2025-05-15 12:03:37,319 - entity_deployer - INFO - Validated relationship 'one-to-one_LeaveApplication' from 'CalendarEvent' to 'LeaveApplication'
2025-05-15 12:03:37,319 - entity_deployer - INFO - Validated relationship 'one-to-many_LeaveApplication' from 'LeaveType' to 'LeaveApplication'
2025-05-15 12:03:37,319 - entity_deployer - INFO - Entity relationships validation completed successfully
2025-05-15 12:03:37,319 - entity_deployer - INFO - Deploying entity 'LeaveApplication' to schema workflow_temp
2025-05-15 12:03:37,336 - entity_deployer - INFO - Entity 'LeaveApplication' already exists with ID E000003
2025-05-15 12:03:37,353 - entity_deployer - ERROR - Error creating attribute 'leaveId': ['Query error: null value in column "display_name" of relation "entity_attributes" violates not-null constraint\nDETAIL:  Failing row contains (E000003.At1, E000003, leaveId, null, null, 1.0, null, t, null, 2025-05-15 12:03:37.352643, 2025-05-15 12:03:37.352643, null, f, null, null, string, system, system, 179).\n']
2025-05-15 12:03:37,353 - entity_deployer - WARNING - Failed to deploy attribute 'leaveId' for entity 'LeaveApplication'
2025-05-15 12:03:37,369 - entity_deployer - ERROR - Error creating attribute 'employeeId': ['Query error: null value in column "display_name" of relation "entity_attributes" violates not-null constraint\nDETAIL:  Failing row contains (E000003.At1, E000003, employeeId, null, null, 1.0, null, f, null, 2025-05-15 12:03:37.368525, 2025-05-15 12:03:37.368525, null, f, null, null, string, system, system, 180).\n']
2025-05-15 12:03:37,369 - entity_deployer - WARNING - Failed to deploy attribute 'employeeId' for entity 'LeaveApplication'
2025-05-15 12:03:37,384 - entity_deployer - ERROR - Error creating attribute 'leaveTypeId': ['Query error: null value in column "display_name" of relation "entity_attributes" violates not-null constraint\nDETAIL:  Failing row contains (E000003.At1, E000003, leaveTypeId, null, null, 1.0, null, f, null, 2025-05-15 12:03:37.383463, 2025-05-15 12:03:37.383463, null, f, null, null, string, system, system, 181).\n']
2025-05-15 12:03:37,384 - entity_deployer - WARNING - Failed to deploy attribute 'leaveTypeId' for entity 'LeaveApplication'
2025-05-15 12:03:37,398 - entity_deployer - ERROR - Error creating attribute 'startDate': ['Query error: null value in column "display_name" of relation "entity_attributes" violates not-null constraint\nDETAIL:  Failing row contains (E000003.At1, E000003, startDate, null, null, 1.0, null, f, null, 2025-05-15 12:03:37.39818, 2025-05-15 12:03:37.39818, null, f, null, null, string, system, system, 182).\n']
2025-05-15 12:03:37,399 - entity_deployer - WARNING - Failed to deploy attribute 'startDate' for entity 'LeaveApplication'
2025-05-15 12:03:37,413 - entity_deployer - ERROR - Error creating attribute 'endDate': ['Query error: null value in column "display_name" of relation "entity_attributes" violates not-null constraint\nDETAIL:  Failing row contains (E000003.At1, E000003, endDate, null, null, 1.0, null, f, null, 2025-05-15 12:03:37.413081, 2025-05-15 12:03:37.413081, null, f, null, null, string, system, system, 183).\n']
2025-05-15 12:03:37,413 - entity_deployer - WARNING - Failed to deploy attribute 'endDate' for entity 'LeaveApplication'
2025-05-15 12:03:37,428 - entity_deployer - ERROR - Error creating attribute 'numDays': ['Query error: null value in column "display_name" of relation "entity_attributes" violates not-null constraint\nDETAIL:  Failing row contains (E000003.At1, E000003, numDays, null, null, 1.0, null, f, null, 2025-05-15 12:03:37.427303, 2025-05-15 12:03:37.427303, null, f, null, null, string, system, system, 184).\n']
2025-05-15 12:03:37,428 - entity_deployer - WARNING - Failed to deploy attribute 'numDays' for entity 'LeaveApplication'
2025-05-15 12:03:37,441 - entity_deployer - ERROR - Error creating attribute 'status': ['Query error: null value in column "display_name" of relation "entity_attributes" violates not-null constraint\nDETAIL:  Failing row contains (E000003.At1, E000003, status, null, null, 1.0, null, f, null, 2025-05-15 12:03:37.440817, 2025-05-15 12:03:37.440817, Pending, f, null, null, string, system, system, 185).\n']
2025-05-15 12:03:37,441 - entity_deployer - WARNING - Failed to deploy attribute 'status' for entity 'LeaveApplication'
2025-05-15 12:03:37,454 - entity_deployer - ERROR - Error creating attribute 'approvedBy': ['Query error: null value in column "display_name" of relation "entity_attributes" violates not-null constraint\nDETAIL:  Failing row contains (E000003.At1, E000003, approvedBy, null, null, 1.0, null, f, null, 2025-05-15 12:03:37.454182, 2025-05-15 12:03:37.454182, null, f, null, null, string, system, system, 186).\n']
2025-05-15 12:03:37,454 - entity_deployer - WARNING - Failed to deploy attribute 'approvedBy' for entity 'LeaveApplication'
2025-05-15 12:03:37,469 - entity_deployer - ERROR - Error creating attribute 'approvalDate': ['Query error: null value in column "display_name" of relation "entity_attributes" violates not-null constraint\nDETAIL:  Failing row contains (E000003.At1, E000003, approvalDate, null, null, 1.0, null, f, null, 2025-05-15 12:03:37.468847, 2025-05-15 12:03:37.468847, null, f, null, null, string, system, system, 187).\n']
2025-05-15 12:03:37,469 - entity_deployer - WARNING - Failed to deploy attribute 'approvalDate' for entity 'LeaveApplication'
2025-05-15 12:03:37,483 - entity_deployer - ERROR - Error creating attribute 'comments': ['Query error: null value in column "display_name" of relation "entity_attributes" violates not-null constraint\nDETAIL:  Failing row contains (E000003.At1, E000003, comments, null, null, 1.0, null, f, null, 2025-05-15 12:03:37.482605, 2025-05-15 12:03:37.482605, null, f, null, null, string, system, system, 188).\n']
2025-05-15 12:03:37,483 - entity_deployer - WARNING - Failed to deploy attribute 'comments' for entity 'LeaveApplication'
2025-05-15 12:03:37,497 - entity_deployer - ERROR - Error creating attribute 'calendarEventId': ['Query error: null value in column "display_name" of relation "entity_attributes" violates not-null constraint\nDETAIL:  Failing row contains (E000003.At1, E000003, calendarEventId, null, null, 1.0, null, f, null, 2025-05-15 12:03:37.496941, 2025-05-15 12:03:37.496941, null, f, null, null, string, system, system, 189).\n']
2025-05-15 12:03:37,497 - entity_deployer - WARNING - Failed to deploy attribute 'calendarEventId' for entity 'LeaveApplication'
2025-05-15 12:03:37,513 - entity_deployer - ERROR - Error creating attribute 'calendarStatus': ['Query error: null value in column "display_name" of relation "entity_attributes" violates not-null constraint\nDETAIL:  Failing row contains (E000003.At1, E000003, calendarStatus, null, null, 1.0, null, f, null, 2025-05-15 12:03:37.512277, 2025-05-15 12:03:37.512277, null, f, null, null, string, system, system, 190).\n']
2025-05-15 12:03:37,513 - entity_deployer - WARNING - Failed to deploy attribute 'calendarStatus' for entity 'LeaveApplication'
2025-05-15 12:03:37,517 - entity_deployer - INFO - Deploying relationships for entity 'LeaveApplication' (ID: E000003)
2025-05-15 12:03:37,543 - entity_deployer - WARNING - Warning: Failed to create source attribute 'employeeId'
2025-05-15 12:03:37,563 - entity_deployer - WARNING - Warning: Failed to create source attribute 'leaveTypeId'
2025-05-15 12:03:37,582 - entity_deployer - WARNING - Warning: Failed to create source attribute 'calendarEventId'
2025-05-15 12:03:37,586 - entity_deployer - INFO - Found 0 relationships for entity 'LeaveApplication' in the database
2025-05-15 12:03:37,591 - entity_deployer - INFO - Table 'e000003_leaveapplication' already exists
2025-05-15 12:03:37,591 - entity_deployer - INFO - Successfully deployed entity 'LeaveApplication'
2025-05-15 12:03:37,591 - entity_deployer - INFO - Deploying entity 'Employee' to schema workflow_temp
2025-05-15 12:03:37,598 - entity_deployer - INFO - Entity 'Employee' already exists with ID E000002
2025-05-15 12:03:37,614 - entity_deployer - ERROR - Error creating attribute 'employeeId': ['Query error: null value in column "display_name" of relation "entity_attributes" violates not-null constraint\nDETAIL:  Failing row contains (E000002.At6, E000002, employeeId, null, null, 1.0, null, f, null, 2025-05-15 12:03:37.613726, 2025-05-15 12:03:37.613726, null, f, null, null, string, system, system, 191).\n']
2025-05-15 12:03:37,614 - entity_deployer - WARNING - Failed to deploy attribute 'employeeId' for entity 'Employee'
2025-05-15 12:03:37,629 - entity_deployer - ERROR - Error creating attribute 'firstName': ['Query error: null value in column "display_name" of relation "entity_attributes" violates not-null constraint\nDETAIL:  Failing row contains (E000002.At6, E000002, firstName, null, null, 1.0, null, f, null, 2025-05-15 12:03:37.628445, 2025-05-15 12:03:37.628445, null, f, null, null, string, system, system, 192).\n']
2025-05-15 12:03:37,629 - entity_deployer - WARNING - Failed to deploy attribute 'firstName' for entity 'Employee'
2025-05-15 12:03:37,647 - entity_deployer - ERROR - Error creating attribute 'lastName': ['Query error: null value in column "display_name" of relation "entity_attributes" violates not-null constraint\nDETAIL:  Failing row contains (E000002.At6, E000002, lastName, null, null, 1.0, null, f, null, 2025-05-15 12:03:37.646385, 2025-05-15 12:03:37.646385, null, f, null, null, string, system, system, 193).\n']
2025-05-15 12:03:37,647 - entity_deployer - WARNING - Failed to deploy attribute 'lastName' for entity 'Employee'
2025-05-15 12:03:37,664 - entity_deployer - ERROR - Error creating attribute 'email': ['Query error: null value in column "display_name" of relation "entity_attributes" violates not-null constraint\nDETAIL:  Failing row contains (E000002.At6, E000002, email, null, null, 1.0, null, t, null, 2025-05-15 12:03:37.663425, 2025-05-15 12:03:37.663425, "@company.com", f, null, null, string, system, system, 194).\n']
2025-05-15 12:03:37,664 - entity_deployer - WARNING - Failed to deploy attribute 'email' for entity 'Employee'
2025-05-15 12:03:37,681 - entity_deployer - ERROR - Error creating attribute 'phoneNumber': ['Query error: null value in column "display_name" of relation "entity_attributes" violates not-null constraint\nDETAIL:  Failing row contains (E000002.At6, E000002, phoneNumber, null, null, 1.0, null, f, null, 2025-05-15 12:03:37.68114, 2025-05-15 12:03:37.68114, null, f, null, null, string, system, system, 195).\n']
2025-05-15 12:03:37,682 - entity_deployer - WARNING - Failed to deploy attribute 'phoneNumber' for entity 'Employee'
2025-05-15 12:03:37,687 - entity_deployer - INFO - Attribute 'departmentId' already exists with ID E000002.At1
2025-05-15 12:03:37,693 - entity_deployer - INFO - Updated attribute 'departmentId' with ID E000002.At1
2025-05-15 12:03:37,702 - entity_deployer - WARNING - Failed to create metadata for attribute 'departmentId': ['Query error: column "display_name" of relation "entity_attribute_metadata" does not exist\nLINE 4:                         display_name, key_type, data_type, t...\n                                ^\n']
2025-05-15 12:03:37,719 - entity_deployer - ERROR - Error creating attribute 'managerId': ['Query error: null value in column "display_name" of relation "entity_attributes" violates not-null constraint\nDETAIL:  Failing row contains (E000002.At6, E000002, managerId, null, null, 1.0, null, f, null, 2025-05-15 12:03:37.718383, 2025-05-15 12:03:37.718383, null, f, null, null, string, system, system, 196).\n']
2025-05-15 12:03:37,719 - entity_deployer - WARNING - Failed to deploy attribute 'managerId' for entity 'Employee'
2025-05-15 12:03:37,724 - entity_deployer - INFO - Attribute 'hireDate' already exists with ID E000002.At3
2025-05-15 12:03:37,729 - entity_deployer - INFO - Updated attribute 'hireDate' with ID E000002.At3
2025-05-15 12:03:37,738 - entity_deployer - WARNING - Failed to create metadata for attribute 'hireDate': ['Query error: column "display_name" of relation "entity_attribute_metadata" does not exist\nLINE 4:                         display_name, key_type, data_type, t...\n                                ^\n']
2025-05-15 12:03:37,743 - entity_deployer - INFO - Attribute 'status' already exists with ID E000002.At2
2025-05-15 12:03:37,748 - entity_deployer - INFO - Updated attribute 'status' with ID E000002.At2
2025-05-15 12:03:37,759 - entity_deployer - WARNING - Failed to create metadata for attribute 'status': ['Query error: column "display_name" of relation "entity_attribute_metadata" does not exist\nLINE 4:                         display_name, key_type, data_type, t...\n                                ^\n']
2025-05-15 12:03:37,775 - entity_deployer - ERROR - Error creating attribute 'salary': ['Query error: null value in column "display_name" of relation "entity_attributes" violates not-null constraint\nDETAIL:  Failing row contains (E000002.At6, E000002, salary, null, null, 1.0, null, f, null, 2025-05-15 12:03:37.775184, 2025-05-15 12:03:37.775184, null, f, null, null, string, system, system, 197).\n']
2025-05-15 12:03:37,775 - entity_deployer - WARNING - Failed to deploy attribute 'salary' for entity 'Employee'
2025-05-15 12:03:37,791 - entity_deployer - ERROR - Error creating attribute 'performanceRating': ['Query error: null value in column "display_name" of relation "entity_attributes" violates not-null constraint\nDETAIL:  Failing row contains (E000002.At6, E000002, performanceRating, null, null, 1.0, null, f, null, 2025-05-15 12:03:37.790412, 2025-05-15 12:03:37.790412, null, f, null, null, string, system, system, 198).\n']
2025-05-15 12:03:37,791 - entity_deployer - WARNING - Failed to deploy attribute 'performanceRating' for entity 'Employee'
2025-05-15 12:03:37,795 - entity_deployer - INFO - Attribute 'probationDays' already exists with ID E000002.At4
2025-05-15 12:03:37,801 - entity_deployer - INFO - Updated attribute 'probationDays' with ID E000002.At4
2025-05-15 12:03:37,810 - entity_deployer - WARNING - Failed to create metadata for attribute 'probationDays': ['Query error: column "display_name" of relation "entity_attribute_metadata" does not exist\nLINE 4:                         display_name, key_type, data_type, t...\n                                ^\n']
2025-05-15 12:03:37,815 - entity_deployer - INFO - Attribute 'minSalary' already exists with ID E000002.At5
2025-05-15 12:03:37,820 - entity_deployer - INFO - Updated attribute 'minSalary' with ID E000002.At5
2025-05-15 12:03:37,831 - entity_deployer - WARNING - Failed to create metadata for attribute 'minSalary': ['Query error: column "display_name" of relation "entity_attribute_metadata" does not exist\nLINE 4:                         display_name, key_type, data_type, t...\n                                ^\n']
2025-05-15 12:03:37,849 - entity_deployer - ERROR - Error creating attribute 'fullName': ['Query error: null value in column "display_name" of relation "entity_attributes" violates not-null constraint\nDETAIL:  Failing row contains (E000002.At6, E000002, fullName, null, null, 1.0, null, f, null, 2025-05-15 12:03:37.848556, 2025-05-15 12:03:37.848556, null, f, null, null, string, system, system, 199).\n']
2025-05-15 12:03:37,849 - entity_deployer - WARNING - Failed to deploy attribute 'fullName' for entity 'Employee'
2025-05-15 12:03:37,868 - entity_deployer - ERROR - Error creating attribute 'managerId with Department constrains Manager': ['Query error: null value in column "display_name" of relation "entity_attributes" violates not-null constraint\nDETAIL:  Failing row contains (E000002.At6, E000002, managerId with Department constrains Manager, null, null, 1.0, null, f, null, 2025-05-15 12:03:37.867429, 2025-05-15 12:03:37.867429, null, f, null, null, string, system, system, 200).\n']
2025-05-15 12:03:37,868 - entity_deployer - WARNING - Failed to deploy attribute 'managerId with Department constrains Manager' for entity 'Employee'
2025-05-15 12:03:37,874 - entity_deployer - INFO - Deploying relationships for entity 'Employee' (ID: E000002)
2025-05-15 12:03:37,897 - entity_deployer - INFO - Marked attribute departmentId as a foreign key
2025-05-15 12:03:37,909 - entity_deployer - INFO - Marked attribute departmentId as a primary key
2025-05-15 12:03:37,926 - entity_deployer - INFO - Successfully updated relationship 6 with properties: on_delete=RESTRICT, on_update=CASCADE, foreign_key_type=NON-NULLABLE
2025-05-15 12:03:37,936 - entity_deployer - INFO - Verified relationship 'many-to-one_Department' was inserted with ID 6
2025-05-15 12:03:37,941 - entity_deployer - INFO - Set Employee to Department relationship properties: on_delete=RESTRICT, on_update=CASCADE, foreign_key_type=NON-NULLABLE
2025-05-15 12:03:37,948 - entity_deployer - INFO - Updated relationship 'many-to-one_Department' with properties: on_delete=RESTRICT, on_update=CASCADE, foreign_key_type=NON-NULLABLE
2025-05-15 12:03:37,953 - entity_deployer - INFO - Found 1 relationships for entity 'Employee' in the database
2025-05-15 12:03:37,961 - entity_deployer - INFO - Table 'e000002_employee' already exists
2025-05-15 12:03:37,961 - entity_deployer - INFO - Successfully deployed entity 'Employee'
2025-05-15 12:03:37,961 - entity_deployer - INFO - Deploying entity 'Department' to schema workflow_temp
2025-05-15 12:03:37,968 - entity_deployer - INFO - Entity 'Department' already exists with ID E000001
2025-05-15 12:03:37,972 - entity_deployer - INFO - Attribute 'departmentId' already exists with ID E000001.At1
2025-05-15 12:03:37,980 - entity_deployer - INFO - Updated attribute 'departmentId' with ID E000001.At1
2025-05-15 12:03:37,991 - entity_deployer - WARNING - Failed to create metadata for attribute 'departmentId': ['Query error: column "display_name" of relation "entity_attribute_metadata" does not exist\nLINE 4:                         display_name, key_type, data_type, t...\n                                ^\n']
2025-05-15 12:03:38,010 - entity_deployer - ERROR - Error creating attribute 'name': ['Query error: null value in column "display_name" of relation "entity_attributes" violates not-null constraint\nDETAIL:  Failing row contains (E000001.At2, E000001, name, null, null, 1.0, null, f, null, 2025-05-15 12:03:38.009394, 2025-05-15 12:03:38.009394, null, f, null, null, string, system, system, 201).\n']
2025-05-15 12:03:38,010 - entity_deployer - WARNING - Failed to deploy attribute 'name' for entity 'Department'
2025-05-15 12:03:38,028 - entity_deployer - ERROR - Error creating attribute 'managerId': ['Query error: null value in column "display_name" of relation "entity_attributes" violates not-null constraint\nDETAIL:  Failing row contains (E000001.At2, E000001, managerId, null, null, 1.0, null, f, null, 2025-05-15 12:03:38.027851, 2025-05-15 12:03:38.027851, null, f, null, null, string, system, system, 202).\n']
2025-05-15 12:03:38,028 - entity_deployer - WARNING - Failed to deploy attribute 'managerId' for entity 'Department'
2025-05-15 12:03:38,045 - entity_deployer - ERROR - Error creating attribute 'location': ['Query error: null value in column "display_name" of relation "entity_attributes" violates not-null constraint\nDETAIL:  Failing row contains (E000001.At2, E000001, location, null, null, 1.0, null, f, null, 2025-05-15 12:03:38.04416, 2025-05-15 12:03:38.04416, null, f, null, null, string, system, system, 203).\n']
2025-05-15 12:03:38,045 - entity_deployer - WARNING - Failed to deploy attribute 'location' for entity 'Department'
2025-05-15 12:03:38,062 - entity_deployer - ERROR - Error creating attribute 'budget': ['Query error: null value in column "display_name" of relation "entity_attributes" violates not-null constraint\nDETAIL:  Failing row contains (E000001.At2, E000001, budget, null, null, 1.0, null, f, null, 2025-05-15 12:03:38.06205, 2025-05-15 12:03:38.06205, null, f, null, null, string, system, system, 204).\n']
2025-05-15 12:03:38,062 - entity_deployer - WARNING - Failed to deploy attribute 'budget' for entity 'Department'
2025-05-15 12:03:38,081 - entity_deployer - ERROR - Error creating attribute 'fullAddress': ['Query error: null value in column "display_name" of relation "entity_attributes" violates not-null constraint\nDETAIL:  Failing row contains (E000001.At2, E000001, fullAddress, null, null, 1.0, null, f, null, 2025-05-15 12:03:38.080001, 2025-05-15 12:03:38.080001, null, f, null, null, string, system, system, 205).\n']
2025-05-15 12:03:38,081 - entity_deployer - WARNING - Failed to deploy attribute 'fullAddress' for entity 'Department'
2025-05-15 12:03:38,100 - entity_deployer - ERROR - Error creating attribute 'locationId with Employee constrains Location': ['Query error: null value in column "display_name" of relation "entity_attributes" violates not-null constraint\nDETAIL:  Failing row contains (E000001.At2, E000001, locationId with Employee constrains Location, null, null, 1.0, null, f, null, 2025-05-15 12:03:38.099216, 2025-05-15 12:03:38.099216, null, f, null, null, string, system, system, 206).\n']
2025-05-15 12:03:38,100 - entity_deployer - WARNING - Failed to deploy attribute 'locationId with Employee constrains Location' for entity 'Department'
2025-05-15 12:03:38,106 - entity_deployer - INFO - Deploying relationships for entity 'Department' (ID: E000001)
2025-05-15 12:03:38,136 - entity_deployer - WARNING - Warning: Failed to create source attribute 'managerId'
2025-05-15 12:03:38,154 - entity_deployer - INFO - Marked attribute departmentId as a foreign key
2025-05-15 12:03:38,167 - entity_deployer - INFO - Marked attribute departmentId as a primary key
2025-05-15 12:03:38,184 - entity_deployer - INFO - Successfully updated relationship 5 with properties: on_delete=None, on_update=None, foreign_key_type=None
2025-05-15 12:03:38,196 - entity_deployer - INFO - Verified relationship 'one-to-many_Employee' was inserted with ID 5
2025-05-15 12:03:38,201 - entity_deployer - INFO - Found 1 relationships for entity 'Department' in the database
2025-05-15 12:03:38,208 - entity_deployer - INFO - Table 'e000001_department' already exists
2025-05-15 12:03:38,208 - entity_deployer - INFO - Successfully deployed entity 'Department'
2025-05-15 12:03:38,208 - entity_deployer - INFO - Deploying entity 'CalendarEvent' to schema workflow_temp
2025-05-15 12:03:38,214 - entity_deployer - INFO - Entity 'CalendarEvent' already exists with ID E000005
2025-05-15 12:03:38,232 - entity_deployer - ERROR - Error creating attribute 'eventId': ['Query error: null value in column "display_name" of relation "entity_attributes" violates not-null constraint\nDETAIL:  Failing row contains (E000005.At1, E000005, eventId, null, null, 1.0, null, t, null, 2025-05-15 12:03:38.231895, 2025-05-15 12:03:38.231895, null, f, null, null, string, system, system, 207).\n']
2025-05-15 12:03:38,234 - entity_deployer - WARNING - Failed to deploy attribute 'eventId' for entity 'CalendarEvent'
2025-05-15 12:03:38,253 - entity_deployer - ERROR - Error creating attribute 'employeeId': ['Query error: null value in column "display_name" of relation "entity_attributes" violates not-null constraint\nDETAIL:  Failing row contains (E000005.At1, E000005, employeeId, null, null, 1.0, null, f, null, 2025-05-15 12:03:38.252923, 2025-05-15 12:03:38.252923, null, f, null, null, string, system, system, 208).\n']
2025-05-15 12:03:38,253 - entity_deployer - WARNING - Failed to deploy attribute 'employeeId' for entity 'CalendarEvent'
2025-05-15 12:03:38,272 - entity_deployer - ERROR - Error creating attribute 'eventType': ['Query error: null value in column "display_name" of relation "entity_attributes" violates not-null constraint\nDETAIL:  Failing row contains (E000005.At1, E000005, eventType, null, null, 1.0, null, f, null, 2025-05-15 12:03:38.271309, 2025-05-15 12:03:38.271309, null, f, null, null, string, system, system, 209).\n']
2025-05-15 12:03:38,272 - entity_deployer - WARNING - Failed to deploy attribute 'eventType' for entity 'CalendarEvent'
2025-05-15 12:03:38,291 - entity_deployer - ERROR - Error creating attribute 'startDate': ['Query error: null value in column "display_name" of relation "entity_attributes" violates not-null constraint\nDETAIL:  Failing row contains (E000005.At1, E000005, startDate, null, null, 1.0, null, f, null, 2025-05-15 12:03:38.290408, 2025-05-15 12:03:38.290408, null, f, null, null, string, system, system, 210).\n']
2025-05-15 12:03:38,291 - entity_deployer - WARNING - Failed to deploy attribute 'startDate' for entity 'CalendarEvent'
2025-05-15 12:03:38,308 - entity_deployer - ERROR - Error creating attribute 'endDate': ['Query error: null value in column "display_name" of relation "entity_attributes" violates not-null constraint\nDETAIL:  Failing row contains (E000005.At1, E000005, endDate, null, null, 1.0, null, f, null, 2025-05-15 12:03:38.307633, 2025-05-15 12:03:38.307633, null, f, null, null, string, system, system, 211).\n']
2025-05-15 12:03:38,308 - entity_deployer - WARNING - Failed to deploy attribute 'endDate' for entity 'CalendarEvent'
2025-05-15 12:03:38,326 - entity_deployer - ERROR - Error creating attribute 'title': ['Query error: null value in column "display_name" of relation "entity_attributes" violates not-null constraint\nDETAIL:  Failing row contains (E000005.At1, E000005, title, null, null, 1.0, null, f, null, 2025-05-15 12:03:38.325487, 2025-05-15 12:03:38.325487, null, f, null, null, string, system, system, 212).\n']
2025-05-15 12:03:38,326 - entity_deployer - WARNING - Failed to deploy attribute 'title' for entity 'CalendarEvent'
2025-05-15 12:03:38,342 - entity_deployer - ERROR - Error creating attribute 'description': ['Query error: null value in column "display_name" of relation "entity_attributes" violates not-null constraint\nDETAIL:  Failing row contains (E000005.At1, E000005, description, null, null, 1.0, null, f, null, 2025-05-15 12:03:38.341314, 2025-05-15 12:03:38.341314, null, f, null, null, string, system, system, 213).\n']
2025-05-15 12:03:38,342 - entity_deployer - WARNING - Failed to deploy attribute 'description' for entity 'CalendarEvent'
2025-05-15 12:03:38,358 - entity_deployer - ERROR - Error creating attribute 'status': ['Query error: null value in column "display_name" of relation "entity_attributes" violates not-null constraint\nDETAIL:  Failing row contains (E000005.At1, E000005, status, null, null, 1.0, null, f, null, 2025-05-15 12:03:38.358168, 2025-05-15 12:03:38.358168, Scheduled, f, null, null, string, system, system, 214).\n']
2025-05-15 12:03:38,358 - entity_deployer - WARNING - Failed to deploy attribute 'status' for entity 'CalendarEvent'
2025-05-15 12:03:38,375 - entity_deployer - ERROR - Error creating attribute 'referenceId': ['Query error: null value in column "display_name" of relation "entity_attributes" violates not-null constraint\nDETAIL:  Failing row contains (E000005.At1, E000005, referenceId, null, null, 1.0, null, f, null, 2025-05-15 12:03:38.374996, 2025-05-15 12:03:38.374996, null, f, null, null, string, system, system, 215).\n']
2025-05-15 12:03:38,375 - entity_deployer - WARNING - Failed to deploy attribute 'referenceId' for entity 'CalendarEvent'
2025-05-15 12:03:38,382 - entity_deployer - INFO - Deploying relationships for entity 'CalendarEvent' (ID: E000005)
2025-05-15 12:03:38,411 - entity_deployer - WARNING - Warning: Failed to create source attribute 'employeeId'
2025-05-15 12:03:38,433 - entity_deployer - WARNING - Warning: Failed to create source attribute 'referenceId'
2025-05-15 12:03:38,438 - entity_deployer - INFO - Found 0 relationships for entity 'CalendarEvent' in the database
2025-05-15 12:03:38,445 - entity_deployer - INFO - Table 'e000005_calendarevent' already exists
2025-05-15 12:03:38,445 - entity_deployer - INFO - Successfully deployed entity 'CalendarEvent'
2025-05-15 12:03:38,445 - entity_deployer - INFO - Deploying entity 'LeaveType' to schema workflow_temp
2025-05-15 12:03:38,450 - entity_deployer - INFO - Entity 'LeaveType' already exists with ID E000004
2025-05-15 12:03:38,467 - entity_deployer - ERROR - Error creating attribute 'typeId': ['Query error: null value in column "display_name" of relation "entity_attributes" violates not-null constraint\nDETAIL:  Failing row contains (E000004.At1, E000004, typeId, null, null, 1.0, null, t, null, 2025-05-15 12:03:38.466346, 2025-05-15 12:03:38.466346, null, f, null, null, string, system, system, 216).\n']
2025-05-15 12:03:38,467 - entity_deployer - WARNING - Failed to deploy attribute 'typeId' for entity 'LeaveType'
2025-05-15 12:03:38,483 - entity_deployer - ERROR - Error creating attribute 'name': ['Query error: null value in column "display_name" of relation "entity_attributes" violates not-null constraint\nDETAIL:  Failing row contains (E000004.At1, E000004, name, null, null, 1.0, null, f, null, 2025-05-15 12:03:38.482318, 2025-05-15 12:03:38.482318, null, f, null, null, string, system, system, 217).\n']
2025-05-15 12:03:38,483 - entity_deployer - WARNING - Failed to deploy attribute 'name' for entity 'LeaveType'
2025-05-15 12:03:38,496 - entity_deployer - ERROR - Error creating attribute 'description': ['Query error: null value in column "display_name" of relation "entity_attributes" violates not-null constraint\nDETAIL:  Failing row contains (E000004.At1, E000004, description, null, null, 1.0, null, f, null, 2025-05-15 12:03:38.495937, 2025-05-15 12:03:38.495937, null, f, null, null, string, system, system, 218).\n']
2025-05-15 12:03:38,496 - entity_deployer - WARNING - Failed to deploy attribute 'description' for entity 'LeaveType'
2025-05-15 12:03:38,511 - entity_deployer - ERROR - Error creating attribute 'maxDuration': ['Query error: null value in column "display_name" of relation "entity_attributes" violates not-null constraint\nDETAIL:  Failing row contains (E000004.At1, E000004, maxDuration, null, null, 1.0, null, f, null, 2025-05-15 12:03:38.511031, 2025-05-15 12:03:38.511031, 30, f, null, null, string, system, system, 219).\n']
2025-05-15 12:03:38,511 - entity_deployer - WARNING - Failed to deploy attribute 'maxDuration' for entity 'LeaveType'
2025-05-15 12:03:38,525 - entity_deployer - ERROR - Error creating attribute 'requiresDocumentation': ['Query error: null value in column "display_name" of relation "entity_attributes" violates not-null constraint\nDETAIL:  Failing row contains (E000004.At1, E000004, requiresDocumentation, null, null, 1.0, null, f, null, 2025-05-15 12:03:38.524807, 2025-05-15 12:03:38.524807, null, f, null, null, string, system, system, 220).\n']
2025-05-15 12:03:38,525 - entity_deployer - WARNING - Failed to deploy attribute 'requiresDocumentation' for entity 'LeaveType'
2025-05-15 12:03:38,529 - entity_deployer - INFO - Deploying relationships for entity 'LeaveType' (ID: E000004)
2025-05-15 12:03:38,554 - entity_deployer - WARNING - Warning: Failed to create source attribute 'typeId'
2025-05-15 12:03:38,559 - entity_deployer - INFO - Found 0 relationships for entity 'LeaveType' in the database
2025-05-15 12:03:38,566 - entity_deployer - INFO - Table 'e000004_leavetype' already exists
2025-05-15 12:03:38,566 - entity_deployer - INFO - Successfully deployed entity 'LeaveType'
2025-05-15 12:03:38,566 - entity_deployer - INFO - All entities created, now applying foreign key constraints
2025-05-15 12:03:38,566 - entity_deployer - INFO - Adding foreign key constraints to table workflow_temp.e000003_leaveapplication
2025-05-15 12:03:38,609 - entity_deployer - WARNING - Warning: Failed to add foreign key constraint for relationship 'many-to-one_Employee': ['Query error: there is no unique constraint matching given keys for referenced table "e000002_employee"\n']
2025-05-15 12:03:38,647 - entity_deployer - INFO - Foreign key constraint 'fk_e000003_leaveapplication_leavetypeid_to_e000004_leavetype_typeid' already exists
2025-05-15 12:03:38,686 - entity_deployer - INFO - Foreign key constraint 'fk_e000003_leaveapplication_calendareventid_to_e000005_calendarevent_eventid' already exists
2025-05-15 12:03:38,686 - entity_deployer - INFO - Adding foreign key constraints to table workflow_temp.e000002_employee
2025-05-15 12:03:38,725 - entity_deployer - INFO - Foreign key constraint 'fk_e000002_employee_departmentid_to_e000001_department_departmentid' already exists
2025-05-15 12:03:38,725 - entity_deployer - INFO - Adding foreign key constraints to table workflow_temp.e000001_department
2025-05-15 12:03:38,770 - entity_deployer - WARNING - Warning: Failed to add foreign key constraint for relationship 'one-to-one_Employee': ['Query error: there is no unique constraint matching given keys for referenced table "e000002_employee"\n']
2025-05-15 12:03:38,812 - entity_deployer - WARNING - Warning: Failed to add foreign key constraint for relationship 'one-to-many_Employee': ['Query error: there is no unique constraint matching given keys for referenced table "e000002_employee"\n']
2025-05-15 12:03:38,812 - entity_deployer - INFO - Adding foreign key constraints to table workflow_temp.e000005_calendarevent
2025-05-15 12:03:38,852 - entity_deployer - WARNING - Warning: Failed to add foreign key constraint for relationship 'many-to-one_Employee': ['Query error: there is no unique constraint matching given keys for referenced table "e000002_employee"\n']
2025-05-15 12:03:38,889 - entity_deployer - INFO - Foreign key constraint 'fk_e000005_calendarevent_referenceid_to_e000003_leaveapplication_leaveid' already exists
2025-05-15 12:03:38,889 - entity_deployer - INFO - Adding foreign key constraints to table workflow_temp.e000004_leavetype
2025-05-15 12:03:38,935 - entity_deployer - WARNING - Warning: Failed to add foreign key constraint for relationship 'one-to-many_LeaveApplication': ['Query error: there is no unique constraint matching given keys for referenced table "e000003_leaveapplication"\n']
2025-05-15 12:03:38,935 - entity_deployer - INFO - All entities created, now deploying business rules
2025-05-15 12:03:38,936 - entity_deployer - INFO - Deploying business rules for entity 'LeaveApplication'
2025-05-15 12:03:38,950 - entity_deployer - ERROR - Error creating business_rule_conditions table: ['Query error: there is no unique constraint matching given keys for referenced table "entity_business_rules"\n']
2025-05-15 12:03:38,950 - entity_deployer - WARNING - Failed to deploy some business rules for entity 'LeaveApplication'
2025-05-15 12:03:38,950 - entity_deployer - INFO - Deploying business rules for entity 'Employee'
2025-05-15 12:03:38,965 - entity_deployer - ERROR - Error creating business_rule_conditions table: ['Query error: there is no unique constraint matching given keys for referenced table "entity_business_rules"\n']
2025-05-15 12:03:38,965 - entity_deployer - WARNING - Failed to deploy some business rules for entity 'Employee'
2025-05-15 12:03:38,965 - entity_deployer - INFO - Deploying business rules for entity 'Department'
2025-05-15 12:03:38,979 - entity_deployer - ERROR - Error creating business_rule_conditions table: ['Query error: there is no unique constraint matching given keys for referenced table "entity_business_rules"\n']
2025-05-15 12:03:38,979 - entity_deployer - WARNING - Failed to deploy some business rules for entity 'Department'
2025-05-15 12:03:38,979 - entity_deployer - INFO - Deploying business rules for entity 'CalendarEvent'
2025-05-15 12:03:38,995 - entity_deployer - ERROR - Error creating business_rule_conditions table: ['Query error: there is no unique constraint matching given keys for referenced table "entity_business_rules"\n']
2025-05-15 12:03:38,995 - entity_deployer - WARNING - Failed to deploy some business rules for entity 'CalendarEvent'
2025-05-15 12:03:38,995 - entity_deployer - INFO - Deploying business rules for entity 'LeaveType'
2025-05-15 12:03:39,010 - entity_deployer - ERROR - Error creating business_rule_conditions table: ['Query error: there is no unique constraint matching given keys for referenced table "entity_business_rules"\n']
2025-05-15 12:03:39,010 - entity_deployer - WARNING - Failed to deploy some business rules for entity 'LeaveType'
2025-05-15 12:03:39,010 - entity_deployer - INFO - Entity deployment completed successfully
2025-05-15 12:03:39,010 - test_business_rule_deployment - INFO - Successfully deployed entities
2025-05-15 12:03:39,018 - test_business_rule_deployment - WARNING - No Employee business rules found in the database
