Employee has employeeId^PK, firstName, lastName, email, phoneNumber, departmentId^FK, managerId^FK, hireDate, status (Active, Inactive, OnLeave), salary, performanceRating, probationDays, minSalary.

* Employee has many-to-one relationship with Department using Employee.departmentId to Department.departmentId^PK
* Employee has many-to-one relationship with Employee using Employee.managerId to Employee.employeeId^PK

* Employee.probationDays PROPERTY_NAME = 90
* Employee.minSalary PROPERTY_NAME = 30000
* Employee.status DEFAULT_VALUE = "Active"
* Employee.hireDate DEFAULT_VALUE = CURRENT_DATE

* Employee.email must be unique
* Employee.email must match pattern "^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$"
* Employee.hireDate must be before current date
* Employee.salary must be greater than 0

BusinessRule Employee:
* Employee.status must be Active to receive performance reviews
* Employee.hireDate must be at least 90 days before Employee.performanceRating can be set

Employee has employeeId, firstName, lastName, fullName[derived].

CalculatedField for Employee.fullName:
* Formula: CONCAT(firstName, ' ', lastName)
* Logic Layer: Application
* Caching: Session
* Dependencies: Employee.firstName, Employee.lastName

Employee has departmentId^FK, managerId^FK with Department constrains Manager.

* Employee.managerId must belong to selected Employee.departmentId


Entity Additional Properties:
Display Name: Company Employee
Type: Core Entity
Description: Represents an employee within the organization

Attribute Additional Properties:
Attribute name: email
Key: Unique
Display Name: Email Address
Data Type: String
Type: Mandatory
Format: "<EMAIL>"
Values: N/A
Default: "@company.com"
Validation: Regex Pattern
Error Message: "Please enter a valid email address"
Description: Employee's primary email address for communications

Relationship: Employee to Department

Relationship Properties:
On Delete: Restrict (Prevent deletion of department with active employees)
On Update: Cascade (Update employee records when department details change)
Foreign Key Type: Non-Nullable

* Synthetic: 
Employee has employeeId = 1, firstName = Tarun, lastName = Singh, email = "<EMAIL>", phoneNumber = "************", departmentId = 101, managerId = 1001, hireDate = "2024-01-15", status = "Active", salary = 60000, performanceRating = 4.5.
Employee has employeeId = 2, firstName = Priya, lastName = Sharma, email = "<EMAIL>", phoneNumber = "************", departmentId = 102, managerId = 1002, hireDate = "2024-02-20", status = "Inactive", salary = 55000, performanceRating = 4.0.


* Confidential: Employee.salary, Employee.performanceRating
* Internal: Employee.hireDate, Employee.departmentId, Employee.managerId
* Public: Employee.firstName, Employee.lastName, Employee.status

* Loading for Employee.Department: Eager Loading
* Loading for Employee.Manager: Lazy Loading

* Archive Strategy for Employee:
  - Trigger: Event-based
  - Criteria: When Employee.status changes to 'Inactive' and remains so for 1 year
  - Retention: 7 years
  - Storage: Cold storage
  - Access Pattern: Read-only through HR archive portal
  - Restoration: Manual process requiring HR Director approval

* Purge Rule for Employee:
  - Trigger: Time-based (annual evaluation)
  - Criteria: archivedDate < (CURRENT_DATE - (7 * 365)) AND Regulatory.retentionExpired = true
  - Approvals: DataGovernance AND HRDirector
  - Audit: Full metadata retention with deletion certificate
  - Dependencies: EmployeeDocument (cascade), PayrollRecord (restrict)

* History Tracking for Employee:
  - Tracked Attributes: Employee.salary, Employee.departmentId, Employee.managerId, Employee.status
  - Tracking Method: Audit table
  - Granularity: Change level
  - Retention: 7 years
  - Access Control: HR Managers and Compliance Officers only

* Workflow: EmployeeOnboarding for Employee
  - States: New, DocumentsVerified, AccessProvisioned, TrainingCompleted, Active
  - Transitions:
    - New → DocumentsVerified [HR_Specialist, HR_Manager]
    - DocumentsVerified → AccessProvisioned [IT_Admin, IT_Manager]
    - AccessProvisioned → TrainingCompleted [Training_Specialist]
    - TrainingCompleted → Active [Department_Manager, HR_Manager]
  - Actions: VerifyDocuments, ProvisionAccess, AssignTraining, CompleteOnboarding

BusinessRule Placement:
* Local Objective: EmployeeManagement
* Global Objective: EmployeeLifecycle
* Chapter: HumanResources
* Book: CompanyOperations
* Tenant: CorporateHQ

* Workflow EmployeeOnboarding Placement:
  - Global Objective: EmployeeLifecycle
  - Book: CompanyOperations
  - Chapter: HumanResources
  - Tenant: CorporateHQ

* Entity Placement for Employee:
  - Tenant: CorporateHQ
  - Book: CompanyOperations
  - Chapter: HumanResources
  - Global Objectives: EmployeeLifecycle, PayrollProcessing
  - Local Objectives: EmployeeManagement, TimeTracking

Department has departmentId^PK, name, managerId^FK, location, budget.

* Department has one-to-one relationship with Employee using Department.managerId to Employee.employeeId^PK
* Department has one-to-many relationship with Employee using Department.departmentId to Employee.departmentId^FK

* Department.minBudget PROPERTY_NAME = 10000
* Department.location DEFAULT_VALUE = "Headquarters"

* Department.name must be unique
* Department.budget must be greater than 0

BusinessRule for Department:
* Department.budget must be approved by Finance before changes
* Department.managerId must reference an Employee with status = 'Active'

Department has name, location, fullAddress[derived].

CalculatedField for Department.fullAddress:
* Formula: CONCAT(name, ' - ', location)
* Logic Layer: Application
* Caching: Session
* Dependencies: Department.name, Department.location

Department has managerId^FK, locationId^FK with Employee constrains Location.

* Department.locationId must be compatible with selected Department.managerId's office location

Entity Hover: Department

Editable Properties:
Display Name: Business Department
Type: Core Entity
Description: Represents a department within the organization

Relationship Hover: Department to Employee

Relationship Properties:
On Delete: Restrict (Prevent deletion of department with active employees)
On Update: Cascade (Update employee records when department details change)
Foreign Key Type: Non-Nullable

* Synthetic: Department.location

* Confidential: Department.budget
* Internal: Department.managerId
* Public: Department.name, Department.location

* Loading for Department.Employees: Lazy Loading
* Loading for Department.Manager: Eager Loading

* Archive Strategy for Department:
  - Trigger: Event-based
  - Criteria: When Department is marked as 'Dissolved'
  - Retention: 10 years
  - Storage: Cold storage
  - Access Pattern: Read-only through Admin portal
  - Restoration: Manual process requiring CEO approval

* Purge Rule for Department:
  - Trigger: Time-based (biennial evaluation)
  - Criteria: archivedDate < (CURRENT_DATE - (10 * 365)) AND noActiveReferences = true
  - Approvals: ExecutiveTeam AND ComplianceOfficer
  - Audit: Full historical record with executive approval documentation
  - Dependencies: Employee (verify no references), Budget (archive), DepartmentDocument (cascade)

* History Tracking for Department:
  - Tracked Attributes: Department.name, Department.managerId, Department.budget
  - Tracking Method: Temporal tables
  - Granularity: Snapshot level
  - Retention: 10 years
  - Access Control: Finance Managers and Executive Team only

BusinessRule Placement:
* Local Objective: DepartmentManagement
* Global Objective: OrganizationalStructure
* Chapter: Administration
* Book: CompanyOperations
* Tenant: CorporateHQ

* Entity Placement for Department:
  - Tenant: CorporateHQ
  - Book: CompanyOperations
  - Chapter: Administration
  - Global Objectives: OrganizationalStructure, BudgetPlanning
  - Local Objectives: DepartmentManagement, ResourceAllocation

LeaveType has typeId^PK, name, description, maxDuration, requiresDocumentation.

* LeaveType has one-to-many relationship with LeaveApplication using LeaveType.typeId to LeaveApplication.leaveTypeId^FK

* LeaveType.maxDuration DEFAULT_VALUE = 30
* LeaveType.requiresDocumentation DEFAULT_VALUE = false

* LeaveType.name must be unique
* LeaveType.maxDuration must be greater than 0

BusinessRule for LeaveType:
* LeaveType.requiresDocumentation must be true for medical leave types
* LeaveType with maxDuration > 90 requires HR Director approval

Entity Additional Properties:
Display Name: Leave Type
Type: Core Entity
Description: Represents a type of leave that employees can request

* Synthetic: 
LeaveType has typeId = "LT001", name = "Annual Leave", description = "Regular vacation time", maxDuration = 30, requiresDocumentation = false.
LeaveType has typeId = "LT002", name = "Sick Leave", description = "Leave due to illness", maxDuration = 14, requiresDocumentation = true.
LeaveType has typeId = "LT003", name = "Personal Leave", description = "Leave for personal reasons", maxDuration = 5, requiresDocumentation = false.

* Entity Placement for LeaveType:
  - Tenant: CorporateHQ
  - Book: CompanyOperations
  - Chapter: HumanResources
  - Global Objectives: LeaveManagement
  - Local Objectives: LeaveTypeManagement

LeaveApplication has leaveId^PK, employeeId^FK, leaveTypeId^FK, startDate, endDate, numDays, status, approvedBy, approvalDate, comments, calendarEventId, calendarStatus.

* LeaveApplication has many-to-one relationship with Employee using LeaveApplication.employeeId to Employee.employeeId^PK
* LeaveApplication has many-to-one relationship with LeaveType using LeaveApplication.leaveTypeId to LeaveType.typeId^PK
* LeaveApplication has one-to-one relationship with CalendarEvent using LeaveApplication.calendarEventId to CalendarEvent.eventId^PK

* LeaveApplication.status DEFAULT_VALUE = "Pending"
* LeaveApplication.numDays CALCULATED_FIELD = DATEDIFF(day, startDate, endDate) + 1

* LeaveApplication.startDate must be a valid date
* LeaveApplication.endDate must be after startDate
* LeaveApplication.numDays must be greater than 0
* LeaveApplication.status must be one of "Pending", "Approved", "Rejected"

BusinessRule for LeaveApplication:
* LeaveApplication.startDate must be at least 7 days in future for planned leave
* LeaveApplication.numDays must not exceed LeaveType.maxDuration
* LeaveApplication with status = "Approved" cannot be modified except by HR Manager

Entity Additional Properties:
Display Name: Leave Application
Type: Core Entity
Description: Represents an employee's request for leave

* Synthetic: 
LeaveApplication has leaveId = "LA001", employeeId = 1, leaveTypeId = "LT001", startDate = "2025-06-15", endDate = "2025-06-16", numDays = 2, status = "Approved", approvedBy = "Manager1", approvalDate = "2025-05-10", comments = "Annual leave approved".
LeaveApplication has leaveId = "LA002", employeeId = 2, leaveTypeId = "LT002", startDate = "2025-07-01", endDate = "2025-07-05", numDays = 5, status = "Pending", approvedBy = null, approvalDate = null, comments = "Sick leave pending approval".

* Entity Placement for LeaveApplication:
  - Tenant: CorporateHQ
  - Book: CompanyOperations
  - Chapter: HumanResources
  - Global Objectives: LeaveManagement, LeaveApprovalProcess
  - Local Objectives: LeaveRequestManagement

CalendarEvent has eventId^PK, employeeId^FK, eventType, startDate, endDate, title, description, status, referenceId.

* CalendarEvent has many-to-one relationship with Employee using CalendarEvent.employeeId to Employee.employeeId^PK
* CalendarEvent has one-to-one relationship with LeaveApplication using CalendarEvent.referenceId to LeaveApplication.leaveId^PK

* CalendarEvent.status DEFAULT_VALUE = "Scheduled"
* CalendarEvent.eventType DEFAULT_VALUE = "Leave"

* CalendarEvent.startDate must be a valid date
* CalendarEvent.endDate must be after startDate
* CalendarEvent.title must not be empty
* CalendarEvent.status must be one of "Scheduled", "Cancelled"

BusinessRule for CalendarEvent:
* CalendarEvent must not overlap with existing events for the same employee
* CalendarEvent must be created at least 1 hour in advance
* CalendarEvent with status = "Cancelled" must have a reason in description

Entity Additional Properties:
Display Name: Calendar Event
Type: Core Entity
Description: Represents an event in an employee's calendar

* Synthetic: 
CalendarEvent has eventId = "CE001", employeeId = 1, eventType = "Leave", startDate = "2025-06-15", endDate = "2025-06-16", title = "Annual Leave", description = "Employee on annual leave", status = "Scheduled", referenceId = "LA001".
CalendarEvent has eventId = "CE002", employeeId = 2, eventType = "Leave", startDate = "2025-07-01", endDate = "2025-07-05", title = "Sick Leave", description = "Employee on sick leave", status = "Scheduled", referenceId = "LA002".

* Entity Placement for CalendarEvent:
  - Tenant: CorporateHQ
  - Book: CompanyOperations
  - Chapter: HumanResources
  - Global Objectives: EmployeeCalendarManagement
  - Local Objectives: CalendarEventManagement
