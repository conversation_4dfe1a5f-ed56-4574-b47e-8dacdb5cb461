{"timestamp": "2025-06-23T06:40:56.625267", "operation": "deploy_single_ui_property_to_workflow_runtime", "input_data": {"ui_property_id": "UI5"}, "result": {"success": false, "error": "UI property UI5 not found with status deployed_to_temp or draft", "ui_property_id": "UI5", "required_status": "deployed_to_temp or draft"}, "status": "error"}
{"timestamp": "2025-06-23T11:30:14.889410", "operation": "deploy_single_ui_property_to_workflow_temp", "input_data": {"ui_property_id": "UI1"}, "result": {"success": false, "error": "UI property UI1 not found with status draft", "ui_property_id": "UI1", "required_status": "draft"}, "status": "error"}
{"timestamp": "2025-06-23T11:30:57.516802", "operation": "deploy_single_ui_property_to_workflow_temp", "input_data": {"ui_property_id": "UI5"}, "result": {"success": false, "error": "UI property UI5 not found with status draft", "ui_property_id": "UI5", "required_status": "draft"}, "status": "error"}
{"timestamp": "2025-06-23T11:31:58.138598", "operation": "deploy_single_ui_property_to_workflow_temp", "input_data": {"ui_property_id": "UI1"}, "result": {"success": false, "error": "UI property UI1 not found with status draft", "ui_property_id": "UI1", "required_status": "draft"}, "status": "error"}
{"timestamp": "2025-06-23T11:32:27.534882", "operation": "deploy_single_ui_property_to_workflow_temp", "input_data": {"ui_property_id": "UI5"}, "result": {"success": false, "error": "UI property UI5 not found with status draft", "ui_property_id": "UI5", "required_status": "draft"}, "status": "error"}
{"timestamp": "2025-06-23T14:03:50.424034", "operation": "deploy_single_ui_property_to_workflow_temp", "input_data": {"ui_property_id": "UI001"}, "result": {"success": false, "error": "UI property UI001 not found with status draft", "ui_property_id": "UI001", "required_status": "draft"}, "status": "error"}
{"timestamp": "2025-06-23T14:03:50.593015", "operation": "insert_ui_property_to_workflow_temp", "input_data": {"_id": "685903f05e423f11979d4093", "ui_property_id": "UI7", "entity_id": "E13", "attribute_id": "E13.At11", "control_type": "number_input", "display_format": "##.# days", "input_mask": "##.#", "placeholder_text": "Enter entitlement", "auto_complete": false, "read_only": false, "validation_display": "inline", "help_text_position": "below", "label": "Total Leave Entitlement", "required_indicator": false, "natural_language": "Entity: Employee\nAttribute: totalLeaveEntitlement\nControl Type: number_input\nLabel: Total Leave Entitlement\nDisplay Format: ##.# days\nInput Mask: ##.#\nPlaceholder Text: Enter entitlement\nValidation Display: inline\nHelp Text Position: below", "version": 4, "status": "draft", "created_at": "2025-06-23T09:00:43.903023", "updated_at": "2025-06-23T09:00:45.190172", "created_by": "Tarun", "updated_by": "Tarun", "ui_property_status": "existing", "changes_detected": []}, "result": {"success": true, "inserted_id": "UI7", "schema": "workflow_temp", "ui_property_id": "UI7", "entity_id": "E13", "attribute_id": "E13.At11", "label": "Total Leave Entitlement", "original_ui_property_id": "UI1"}, "status": "success"}
{"timestamp": "2025-06-23T14:03:50.595687", "operation": "process_mongo_ui_properties_to_workflow_temp", "input_data": {"schema": "workflow_temp"}, "result": {"total_processed": 1, "successful_inserts": 1, "failed_inserts": 0, "details": [{"ui_property_id": "UI7", "entity_id": "E13", "attribute_id": "E13.At11", "label": "Total Leave Entitlement", "status": "success", "details": {"success": true, "inserted_id": "UI7", "schema": "workflow_temp", "ui_property_id": "UI7", "entity_id": "E13", "attribute_id": "E13.At11", "label": "Total Leave Entitlement", "original_ui_property_id": "UI1"}}]}, "status": "success"}
{"timestamp": "2025-06-23T14:09:01.501679", "operation": "deploy_single_ui_property_to_workflow_temp", "input_data": {"ui_property_id": "UI001"}, "result": {"success": false, "error": "UI property UI001 not found with status draft", "ui_property_id": "UI001", "required_status": "draft"}, "status": "error"}
{"timestamp": "2025-06-23T14:09:01.604500", "operation": "process_mongo_ui_properties_to_workflow_temp", "input_data": {"schema": "workflow_temp"}, "result": {"total_processed": 0, "successful_inserts": 0, "failed_inserts": 0, "details": []}, "status": "success"}
{"timestamp": "2025-06-23T14:32:33.838953", "operation": "deploy_single_ui_property_to_workflow_temp", "input_data": {"ui_property_id": "UI001"}, "result": {"success": false, "error": "UI property UI001 not found with status draft", "ui_property_id": "UI001", "required_status": "draft"}, "status": "error"}
{"timestamp": "2025-06-23T14:32:33.921612", "operation": "process_mongo_ui_properties_to_workflow_temp", "input_data": {"schema": "workflow_temp"}, "result": {"total_processed": 0, "successful_inserts": 0, "failed_inserts": 0, "details": []}, "status": "success"}
{"timestamp": "2025-06-23T14:38:53.274039", "operation": "deploy_single_ui_property_to_workflow_temp", "input_data": {"ui_property_id": "UI001"}, "result": {"success": false, "error": "UI property UI001 not found with status draft", "ui_property_id": "UI001", "required_status": "draft"}, "status": "error"}
{"timestamp": "2025-06-23T14:38:53.401724", "operation": "process_mongo_ui_properties_to_workflow_temp", "input_data": {"schema": "workflow_temp"}, "result": {"total_processed": 0, "successful_inserts": 0, "failed_inserts": 0, "details": []}, "status": "success"}
