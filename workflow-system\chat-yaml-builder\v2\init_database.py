#!/usr/bin/env python3
"""
Initialize the database schema and tables for testing.
"""

import os
import psycopg2
import logging

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger('init_database')

def get_db_connection():
    """Get database connection"""
    db_host = os.environ.get('DB_HOST', '**********')
    db_port = os.environ.get('DB_PORT', '5432')
    db_name = os.environ.get('DB_NAME', 'workflow_system')
    db_user = os.environ.get('DB_USER', 'postgres')
    db_password = os.environ.get('DB_PASSWORD', 'workflow_postgres_secure_password')
    
    conn = psycopg2.connect(
        host=db_host,
        port=db_port,
        dbname=db_name,
        user=db_user,
        password=db_password
    )
    
    return conn

def init_database():
    """Initialize the database schema and tables"""
    conn = get_db_connection()
    
    try:
        # Create schema if it doesn't exist
        schema_name = 'workflow_temp'
        with conn.cursor() as cursor:
            cursor.execute(f"CREATE SCHEMA IF NOT EXISTS {schema_name}")
            conn.commit()
            
            # Set search path to the schema
            cursor.execute(f"SET search_path TO {schema_name}")
            conn.commit()
            
            # Create entities table
            cursor.execute(f"""
                CREATE TABLE IF NOT EXISTS {schema_name}.entities (
                    entity_id VARCHAR(255) PRIMARY KEY,
                    name VARCHAR(255) NOT NULL,
                    description TEXT,
                    created_at TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            # Create entity_attributes table
            cursor.execute(f"""
                CREATE TABLE IF NOT EXISTS {schema_name}.entity_attributes (
                    attribute_id VARCHAR(255) PRIMARY KEY,
                    entity_id VARCHAR(255) REFERENCES {schema_name}.entities(entity_id),
                    name VARCHAR(255) NOT NULL,
                    type VARCHAR(50),
                    required BOOLEAN DEFAULT FALSE,
                    default_value TEXT,
                    calculated_field BOOLEAN DEFAULT FALSE,
                    calculation_formula TEXT,
                    dependencies JSONB,
                    display_name VARCHAR(255),
                    datatype VARCHAR(50),
                    status VARCHAR(50),
                    primary_key BOOLEAN DEFAULT FALSE,
                    created_at TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            # Create entity_attribute_metadata table
            cursor.execute(f"""
                CREATE TABLE IF NOT EXISTS {schema_name}.entity_attribute_metadata (
                    id SERIAL PRIMARY KEY,
                    entity_id VARCHAR(255) REFERENCES {schema_name}.entities(entity_id),
                    attribute_id VARCHAR(255) REFERENCES {schema_name}.entity_attributes(attribute_id),
                    attribute_name VARCHAR(255),
                    required BOOLEAN DEFAULT FALSE,
                    display_name VARCHAR(255),
                    key_type VARCHAR(50),
                    data_type VARCHAR(50),
                    type VARCHAR(50),
                    format VARCHAR(255),
                    values TEXT,
                    default_value TEXT,
                    validation VARCHAR(255),
                    error_message TEXT,
                    description TEXT,
                    created_at TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            # Create entity_relationships table
            cursor.execute(f"""
                CREATE TABLE IF NOT EXISTS {schema_name}.entity_relationships (
                    id SERIAL PRIMARY KEY,
                    source_entity_id VARCHAR(255) REFERENCES {schema_name}.entities(entity_id),
                    target_entity_id VARCHAR(255) REFERENCES {schema_name}.entities(entity_id),
                    relationship_type VARCHAR(50),
                    source_attribute_id VARCHAR(255) REFERENCES {schema_name}.entity_attributes(attribute_id),
                    target_attribute_id VARCHAR(255) REFERENCES {schema_name}.entity_attributes(attribute_id),
                    on_delete VARCHAR(50),
                    on_update VARCHAR(50),
                    foreign_key_type VARCHAR(50),
                    created_at TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            # Create entity_constraints table
            cursor.execute(f"""
                CREATE TABLE IF NOT EXISTS {schema_name}.entity_constraints (
                    id SERIAL PRIMARY KEY,
                    entity_id VARCHAR(255) REFERENCES {schema_name}.entities(entity_id),
                    constraint_name VARCHAR(255),
                    constraint_type VARCHAR(50),
                    constraint_definition TEXT,
                    created_at TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            conn.commit()
            
            logger.info(f"Database schema '{schema_name}' and tables initialized successfully")
    except Exception as e:
        logger.error(f"Error initializing database: {str(e)}")
        conn.rollback()
    finally:
        conn.close()

if __name__ == "__main__":
    init_database()
