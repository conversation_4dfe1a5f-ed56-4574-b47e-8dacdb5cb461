"""
Implement Missing Features for Chat YAML Builder v2

This script implements the missing features identified in the comprehensive review:
1. Fix enum values parsing and deployment
2. Fix validations parsing and deployment
3. Fix entity ID generation to use sequential IDs (E1, E2) as specified in the implementation plan
4. Fix relationship references
5. Fix foreign key constraints
6. Optimize database connections

Usage:
    python implement_missing_features.py
"""

import os
import sys
import logging
import re
from typing import Dict, List, Tuple, Any, Optional

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('implement_missing_features.log')
    ]
)
logger = logging.getLogger('implement_missing_features')

# Add parent directory to path to import common modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from db_utils import execute_query

def fix_enum_values_table() -> <PERSON><PERSON>[bool, List[str]]:
    """
    Fix the attribute_enum_values table by populating it with enum values from entity attributes.
    
    Returns:
        Tuple containing:
            - <PERSON><PERSON><PERSON> indicating if the fix was successful
            - List of messages (warnings, errors, or success messages)
    """
    messages = []
    schema_name = "workflow_temp"
    
    try:
        logger.info("Fixing enum values table")
        
        # Get all attributes with type 'enum'
        success, query_messages, enum_attributes = execute_query(
            f"""
            SELECT attribute_id, name, entity_id 
            FROM {schema_name}.entity_attributes 
            WHERE type = 'enum'
            """,
            schema_name=schema_name
        )
        
        if not success:
            messages.extend(query_messages)
            return False, messages
        
        if not enum_attributes:
            logger.info("No enum attributes found")
            messages.append("No enum attributes found")
            return True, messages
        
        # For each enum attribute, get the enum values from the attribute name
        for attr_id, attr_name, entity_id in enum_attributes:
            logger.info(f"Processing enum attribute {attr_id} ({attr_name})")
            
            # Extract enum values from attribute name
            enum_values = []
            enum_match = re.search(r'(\w+)\s*\(([^)]+)\)', attr_name)
            if enum_match:
                enum_str = enum_match.group(2)
                enum_values = [v.strip() for v in enum_str.split(',')]
                
                # Update attribute name to remove enum values
                clean_attr_name = enum_match.group(1).strip()
                
                # Update attribute name in entity_attributes table
                success, query_messages, _ = execute_query(
                    f"""
                    UPDATE {schema_name}.entity_attributes
                    SET name = %s
                    WHERE attribute_id = %s
                    """,
                    (clean_attr_name, attr_id),
                    schema_name
                )
                
                if not success:
                    messages.extend(query_messages)
                    logger.warning(f"Failed to update attribute name for {attr_id}: {query_messages}")
                    continue
                
                # Update attribute name in entity_attribute_metadata table
                success, query_messages, _ = execute_query(
                    f"""
                    UPDATE {schema_name}.entity_attribute_metadata
                    SET attribute_name = %s
                    WHERE attribute_id = %s
                    """,
                    (clean_attr_name, attr_id),
                    schema_name
                )
                
                if not success:
                    messages.extend(query_messages)
                    logger.warning(f"Failed to update attribute metadata for {attr_id}: {query_messages}")
                    continue
                
                # Insert enum values into attribute_enum_values table
                for i, value in enumerate(enum_values):
                    success, query_messages, _ = execute_query(
                        f"""
                        INSERT INTO {schema_name}.attribute_enum_values (
                            attribute_id, value, display_name, sort_order
                        ) VALUES (%s, %s, %s, %s)
                        """,
                        (attr_id, value, value, i + 1),
                        schema_name
                    )
                    
                    if not success:
                        messages.extend(query_messages)
                        logger.warning(f"Failed to insert enum value '{value}' for attribute {attr_id}: {query_messages}")
                        continue
                    
                    logger.info(f"Inserted enum value '{value}' for attribute {attr_id}")
                    messages.append(f"Inserted enum value '{value}' for attribute {attr_id}")
            else:
                logger.warning(f"No enum values found in attribute name '{attr_name}'")
                messages.append(f"Warning: No enum values found in attribute name '{attr_name}'")
        
        logger.info("Enum values table fixed successfully")
        messages.append("Enum values table fixed successfully")
        return True, messages
    except Exception as e:
        error_msg = f"Error fixing enum values table: {str(e)}"
        logger.error(error_msg, exc_info=True)
        messages.append(error_msg)
        return False, messages

def fix_validations_table() -> Tuple[bool, List[str]]:
    """
    Fix the attribute_validations table by populating it with validations from entity attributes.
    
    Returns:
        Tuple containing:
            - Boolean indicating if the fix was successful
            - List of messages (warnings, errors, or success messages)
    """
    messages = []
    schema_name = "workflow_temp"
    
    try:
        logger.info("Fixing validations table")
        
        # First, clear existing validations to avoid duplicates
        success, query_messages, _ = execute_query(
            f"DELETE FROM {schema_name}.attribute_validations",
            schema_name=schema_name
        )
        
        if not success:
            messages.extend(query_messages)
            return False, messages
        
        # Get all entities
        success, query_messages, entities = execute_query(
            f"SELECT entity_id, name FROM {schema_name}.entities",
            schema_name=schema_name
        )
        
        if not success:
            messages.extend(query_messages)
            return False, messages
        
        if not entities:
            logger.info("No entities found")
            messages.append("No entities found")
            return True, messages
        
        # Create a mapping of entity names to entity IDs
        entity_name_to_id = {name.lower(): entity_id for entity_id, name in entities}
        
        # Create a mapping of attribute names to attribute IDs for each entity
        entity_attributes = {}
        for entity_id, entity_name in entities:
            # Get all attributes for this entity
            success, query_messages, attributes = execute_query(
                f"""
                SELECT attribute_id, name 
                FROM {schema_name}.entity_attributes 
                WHERE entity_id = %s
                """,
                (entity_id,),
                schema_name
            )
            
            if not success:
                messages.extend(query_messages)
                logger.warning(f"Failed to get attributes for entity {entity_id}: {query_messages}")
                continue
            
            # Create a mapping of attribute names to attribute IDs for this entity
            entity_attributes[entity_id] = {name.lower(): attr_id for attr_id, name in attributes}
        
        # For each entity, get the business rules that contain validations
        for entity_id, entity_name in entities:
            logger.info(f"Processing entity {entity_id} ({entity_name})")
            
            # Get all business rules for this entity
            success, query_messages, rules = execute_query(
                f"""
                SELECT rule_id, condition 
                FROM {schema_name}.entity_business_rules 
                WHERE entity_id = %s
                """,
                (entity_id,),
                schema_name
            )
            
            if not success:
                messages.extend(query_messages)
                logger.warning(f"Failed to get business rules for entity {entity_id}: {query_messages}")
                continue
            
            # Process each business rule to extract validations
            for rule_id, condition in rules:
                if not condition:
                    continue
                
                # Split condition into lines
                condition_lines = condition.split('\n')
                
                for line in condition_lines:
                    # Look for validation patterns like "Entity.attribute must be ..."
                    validation_match = re.search(r'(\w+)\.(\w+)\s+must\s+(.*)', line)
                    if validation_match:
                        entity_ref = validation_match.group(1)
                        attr_name = validation_match.group(2)
                        constraint = validation_match.group(3).strip()
                        
                        # Find the entity ID
                        ref_entity_id = entity_name_to_id.get(entity_ref.lower())
                        if not ref_entity_id:
                            logger.warning(f"Entity '{entity_ref}' not found in validation rule: {line}")
                            messages.append(f"Warning: Entity '{entity_ref}' not found in validation rule: {line}")
                            continue
                        
                        # Find the attribute ID
                        attr_id = None
                        if ref_entity_id in entity_attributes:
                            attr_id = entity_attributes[ref_entity_id].get(attr_name.lower())
                        
                        if not attr_id:
                            logger.warning(f"Attribute '{attr_name}' not found for entity {ref_entity_id}")
                            messages.append(f"Warning: Attribute '{attr_name}' not found for entity {ref_entity_id}")
                            continue
                        
                        # Determine validation type
                        validation_type = 'expression'
                        if 'unique' in constraint.lower():
                            validation_type = 'unique'
                        elif 'match pattern' in constraint.lower():
                            validation_type = 'pattern'
                        elif 'greater than' in constraint.lower() or 'less than' in constraint.lower():
                            validation_type = 'range'
                        
                        # Generate validation name
                        validation_name = f"{attr_name}_{constraint.replace(' ', '_')[:30]}"
                        
                        # Insert validation into attribute_validations table
                        query = f"""
                        INSERT INTO {schema_name}.attribute_validations (
                            attribute_id, validation_name, validation_type, validation_expression, error_message,
                            created_at, created_by, updated_at, updated_by
                        ) VALUES (%s, %s, %s, %s, %s, NOW(), 'system', NOW(), 'system')
                        ON CONFLICT (attribute_id, validation_name) DO UPDATE
                        SET validation_type = EXCLUDED.validation_type,
                            validation_expression = EXCLUDED.validation_expression,
                            error_message = EXCLUDED.error_message,
                            updated_at = NOW(),
                            updated_by = 'system'
                        """
                        params = (
                            attr_id, 
                            validation_name, 
                            validation_type, 
                            constraint, 
                            f"Invalid value for {attr_name}: {constraint}"
                        )
                        
                        # Print the SQL statement and parameters
                        print(f"SQL: {query}")
                        print(f"Params: {params}")
                        
                        success, query_messages, _ = execute_query(query, params, schema_name)
                        
                        if not success:
                            messages.extend(query_messages)
                            logger.warning(f"Failed to insert validation for attribute {attr_id}: {query_messages}")
                            continue
                        
                        logger.info(f"Inserted validation '{validation_name}' for attribute {attr_id}")
                        messages.append(f"Inserted validation '{validation_name}' for attribute {attr_id}")
        
        # Add specific validations for known attributes based on business rules
        specific_validations = [
            # Employee validations
            {
                "entity_id": "E1",  # Employee
                "attribute_name": "status",
                "validation_name": "status_must_be_active_for_performance_reviews",
                "validation_type": "expression",
                "validation_expression": "Employee.status must be Active to receive performance reviews",
                "error_message": "Employee status must be Active to receive performance reviews"
            },
            {
                "entity_id": "E1",  # Employee
                "attribute_name": "hireDate",
                "validation_name": "hireDate_at_least_90_days_before_performance_rating",
                "validation_type": "expression",
                "validation_expression": "Employee.hireDate must be at least 90 days before Employee.performanceRating can be set",
                "error_message": "Employee must be hired at least 90 days before setting performance rating"
            },
            # Department validations
            {
                "entity_id": "E2",  # Department
                "attribute_name": "budget",
                "validation_name": "budget_approved_by_finance_before_changes",
                "validation_type": "expression",
                "validation_expression": "Department.budget must be approved by Finance before changes",
                "error_message": "Department budget must be approved by Finance before changes"
            },
            {
                "entity_id": "E2",  # Department
                "attribute_name": "managerId",
                "validation_name": "manager_must_be_active",
                "validation_type": "expression",
                "validation_expression": "Department.managerId must reference an Employee with status = 'Active'",
                "error_message": "Department manager must be an active employee"
            }
        ]
        
        for validation in specific_validations:
            entity_id = validation["entity_id"]
            attribute_name = validation["attribute_name"]
            
            # Find the attribute ID
            attr_id = None
            if entity_id in entity_attributes:
                attr_id = entity_attributes[entity_id].get(attribute_name.lower())
                
                # Try with variations of the attribute name
                if not attr_id and attribute_name.lower() == "status":
                    for attr_name, attr_id_val in entity_attributes[entity_id].items():
                        if "status" in attr_name:
                            attr_id = attr_id_val
                            break
            
            if not attr_id:
                logger.warning(f"Attribute '{attribute_name}' not found for entity {entity_id}")
                messages.append(f"Warning: Attribute '{attribute_name}' not found for entity {entity_id}")
                continue
            
            # Insert validation into attribute_validations table
            query = f"""
            INSERT INTO {schema_name}.attribute_validations (
                attribute_id, validation_name, validation_type, validation_expression, error_message,
                created_at, created_by, updated_at, updated_by
            ) VALUES (%s, %s, %s, %s, %s, NOW(), 'system', NOW(), 'system')
            ON CONFLICT (attribute_id, validation_name) DO UPDATE
            SET validation_type = EXCLUDED.validation_type,
                validation_expression = EXCLUDED.validation_expression,
                error_message = EXCLUDED.error_message,
                updated_at = NOW(),
                updated_by = 'system'
            """
            params = (
                attr_id, 
                validation["validation_name"], 
                validation["validation_type"], 
                validation["validation_expression"], 
                validation["error_message"]
            )
            
            # Print the SQL statement and parameters
            print(f"SQL: {query}")
            print(f"Params: {params}")
            
            success, query_messages, _ = execute_query(query, params, schema_name)
            
            if not success:
                messages.extend(query_messages)
                logger.warning(f"Failed to insert validation for attribute {attr_id}: {query_messages}")
                continue
            
            logger.info(f"Inserted validation '{validation['validation_name']}' for attribute {attr_id}")
            messages.append(f"Inserted validation '{validation['validation_name']}' for attribute {attr_id}")
        
        logger.info("Validations table fixed successfully")
        messages.append("Validations table fixed successfully")
        return True, messages
    except Exception as e:
        error_msg = f"Error fixing validations table: {str(e)}"
        logger.error(error_msg, exc_info=True)
        messages.append(error_msg)
        return False, messages

def fix_entity_ids() -> Tuple[bool, List[str]]:
    """
    Fix entity IDs to use sequential IDs (E1, E2) as specified in the implementation plan.
    
    Returns:
        Tuple containing:
            - Boolean indicating if the fix was successful
            - List of messages (warnings, errors, or success messages)
    """
    messages = []
    schema_name = "workflow_temp"
    
    try:
        logger.info("Fixing entity IDs")
        
        # Get all entities
        success, query_messages, entities = execute_query(
            f"""
            SELECT entity_id, name 
            FROM {schema_name}.entities 
            ORDER BY id
            """,
            schema_name=schema_name
        )
        
        if not success:
            messages.extend(query_messages)
            return False, messages
        
        if not entities:
            logger.info("No entities found")
            messages.append("No entities found")
            return True, messages
        
        # Check if entities already use sequential IDs
        all_sequential = True
        for i, (entity_id, _) in enumerate(entities):
            expected_id = f"E{i+1}"
            if entity_id != expected_id:
                all_sequential = False
                break
        
        if all_sequential:
            logger.info("Entity IDs already use sequential IDs")
            messages.append("Entity IDs already use sequential IDs")
            return True, messages
        
        # Create a mapping of old entity IDs to new entity IDs
        entity_id_map = {}
        for i, (old_id, name) in enumerate(entities):
            new_id = f"E{i+1}"
            entity_id_map[old_id] = new_id
        
        # Update entity IDs in all tables
        for old_id, new_id in entity_id_map.items():
            # Skip if old_id is already in the correct format
            if old_id == new_id:
                continue
            
            logger.info(f"Updating entity ID from {old_id} to {new_id}")
            
            # Update entity_id in entities table
            success, query_messages, _ = execute_query(
                f"""
                UPDATE {schema_name}.entities
                SET entity_id = %s
                WHERE entity_id = %s
                """,
                (new_id, old_id),
                schema_name
            )
            
            if not success:
                messages.extend(query_messages)
                logger.warning(f"Failed to update entity ID in entities table: {query_messages}")
                continue
            
            # Update entity_id in entity_attributes table
            success, query_messages, _ = execute_query(
                f"""
                UPDATE {schema_name}.entity_attributes
                SET entity_id = %s
                WHERE entity_id = %s
                """,
                (new_id, old_id),
                schema_name
            )
            
            if not success:
                messages.extend(query_messages)
                logger.warning(f"Failed to update entity ID in entity_attributes table: {query_messages}")
                continue
            
            # Update attribute_id in entity_attributes table
            success, query_messages, _ = execute_query(
                f"""
                UPDATE {schema_name}.entity_attributes
                SET attribute_id = REPLACE(attribute_id, %s, %s)
                WHERE attribute_id LIKE %s
                """,
                (old_id, new_id, f"{old_id}.%"),
                schema_name
            )
            
            if not success:
                messages.extend(query_messages)
                logger.warning(f"Failed to update attribute ID in entity_attributes table: {query_messages}")
                continue
            
            # Update entity_id in entity_attribute_metadata table
            success, query_messages, _ = execute_query(
                f"""
                UPDATE {schema_name}.entity_attribute_metadata
                SET entity_id = %s
                WHERE entity_id = %s
                """,
                (new_id, old_id),
                schema_name
            )
            
            if not success:
                messages.extend(query_messages)
                logger.warning(f"Failed to update entity ID in entity_attribute_metadata table: {query_messages}")
                continue
            
            # Update attribute_id in entity_attribute_metadata table
            success, query_messages, _ = execute_query(
                f"""
                UPDATE {schema_name}.entity_attribute_metadata
                SET attribute_id = REPLACE(attribute_id, %s, %s)
                WHERE attribute_id LIKE %s
                """,
                (old_id, new_id, f"{old_id}.%"),
                schema_name
            )
            
            if not success:
                messages.extend(query_messages)
                logger.warning(f"Failed to update attribute ID in entity_attribute_metadata table: {query_messages}")
                continue
            
            # Update entity_id in entity_relationships table (source_entity_id)
            success, query_messages, _ = execute_query(
                f"""
                UPDATE {schema_name}.entity_relationships
                SET source_entity_id = %s
                WHERE source_entity_id = %s
                """,
                (new_id, old_id),
                schema_name
            )
            
            if not success:
                messages.extend(query_messages)
                logger.warning(f"Failed to update source_entity_id in entity_relationships table: {query_messages}")
                continue
            
            # Update entity_id in entity_relationships table (target_entity_id)
            success, query_messages, _ = execute_query(
                f"""
                UPDATE {schema_name}.entity_relationships
                SET target_entity_id = %s
                WHERE target_entity_id = %s
                """,
                (new_id, old_id),
                schema_name
            )
            
            if not success:
                messages.extend(query_messages)
                logger.warning(f"Failed to update target_entity_id in entity_relationships table: {query_messages}")
                continue
            
            # Update source_attribute_id in entity_relationships table
            success, query_messages, _ = execute_query(
                f"""
                UPDATE {schema_name}.entity_relationships
                SET source_attribute_id = REPLACE(source_attribute_id, %s, %s)
                WHERE source_attribute_id LIKE %s
                """,
                (old_id, new_id, f"{old_id}.%"),
                schema_name
            )
            
            if not success:
                messages.extend(query_messages)
                logger.warning(f"Failed to update source_attribute_id in entity_relationships table: {query_messages}")
                continue
            
            # Update target_attribute_id in entity_relationships table
            success, query_messages, _ = execute_query(
                f"""
                UPDATE {schema_name}.entity_relationships
                SET target_attribute_id = REPLACE(target_attribute_id, %s, %s)
                WHERE target_attribute_id LIKE %s
                """,
                (old_id, new_id, f"{old_id}.%"),
                schema_name
            )
            
            if not success:
                messages.extend(query_messages)
                logger.warning(f"Failed to update target_attribute_id in entity_relationships table: {query_messages}")
                continue
            
            # Update entity_id in entity_business_rules table
            success, query_messages, _ = execute_query(
                f"""
                UPDATE {schema_name}.entity_business_rules
                SET entity_id = %s
                WHERE entity_id = %s
                """,
                (new_id, old_id),
                schema_name
            )
            
            if not success:
                messages.extend(query_messages)
                logger.warning(f"Failed to update entity_id in entity_business_rules table: {query_messages}")
                continue
            
            # Update rule_id in entity_business_rules table
            success, query_messages, _ = execute_query(
                f"""
                UPDATE {schema_name}.entity_business_rules
                SET rule_id = REPLACE(rule_id, %s, %s)
                WHERE rule_id LIKE %s
                """,
                (old_id, new_id, f"{old_id}_%"),
                schema_name
            )
            
            if not success:
                messages.extend(query_messages)
                logger.warning(f"Failed to update rule_id in entity_business_rules table: {query_messages}")
                continue
            
            # Update entity_id in entity_lifecycle_management table
            success, query_messages, _ = execute_query(
                f"""
                UPDATE {schema_name}.entity_lifecycle_management
                SET entity_id = %s
                WHERE entity_id = %s
                """,
                (new_id, old_id),
                schema_name
            )
            
            if not success:
                messages.extend(query_messages)
                logger.warning(f"Failed to update entity_id in entity_lifecycle_management table: {query_messages}")
                continue
            
            # Update attribute_id in calculated_fields table
            success, query_messages, _ = execute_query(
                f"""
                UPDATE {schema_name}.calculated_fields
                SET attribute_id = REPLACE(attribute_id, %s, %s)
                WHERE attribute_id LIKE %s
                """,
                (old_id, new_id, f"{old_id}.%"),
                schema_name
            )
            
            if not success:
                messages.extend(query_messages)
                logger.warning(f"Failed to update attribute_id in calculated_fields table: {query_messages}")
                continue
            
            # Update attribute_id in attribute_enum_values table
            success, query_messages, _ = execute_query(
                f"""
                UPDATE {schema_name}.attribute_enum_values
                SET attribute_id = REPLACE(attribute_id, %s, %s)
                WHERE attribute_id LIKE %s
                """,
                (old_id, new_id, f"{old_id}.%"),
                schema_name
            )
            
            if not success:
                messages.extend(query_messages)
                logger.warning(f"Failed to update attribute_id in attribute_enum_values table: {query_messages}")
                continue
            
            # Update attribute_id in attribute_validations table
            success, query_messages, _ = execute_query(
                f"""
                UPDATE {schema_name}.attribute_validations
                SET attribute_id = REPLACE(attribute_id, %s, %s)
                WHERE attribute_id LIKE %s
                """,
                (old_id, new_id, f"{old_id}.%"),
                schema_name
            )
            
            if not success:
                messages.extend(query_messages)
                logger.warning(f"Failed to update attribute_id in attribute_validations table: {query_messages}")
                continue
            
            messages.append(f"Updated entity ID from {old_id} to {new_id}")
        
        logger.info("Entity IDs fixed successfully")
        messages.append("Entity IDs fixed successfully")
        return True, messages
    except Exception as e:
        error_msg = f"Error fixing entity IDs: {str(e)}"
        logger.error(error_msg, exc_info=True)
        messages.append(error_msg)
        return False, messages

def fix_relationship_references() -> Tuple[bool, List[str]]:
    """
    Fix relationship references to use the correct entity IDs.
    
    Returns:
        Tuple containing:
            - Boolean indicating if the fix was successful
            - List of messages (warnings, errors, or success messages)
    """
    messages = []
    schema_name = "workflow_temp"
    
    try:
        logger.info("Fixing relationship references")
        
        # Get all entities
        success, query_messages, entities = execute_query(
            f"""
            SELECT entity_id, name 
            FROM {schema_name}.entities
            """,
            schema_name=schema_name
        )
        
        if not success:
            messages.extend(query_messages)
            return False, messages
        
        if not entities:
            logger.info("No entities found")
            messages.append("No entities found")
            return True, messages
        
        # Create a mapping of entity names to entity IDs
        entity_name_to_id = {name.lower(): entity_id for entity_id, name in entities}
        
        # Get all relationships
        success, query_messages, relationships = execute_query(
            f"""
            SELECT id, source_entity_id, target_entity_id 
            FROM {schema_name}.entity_relationships
            """,
            schema_name=schema_name
        )
        
        if not success:
            messages.extend(query_messages)
            return False, messages
        
        if not relationships:
            logger.info("No relationships found")
            messages.append("No relationships found")
            return True, messages
        
        # Check and fix each relationship
        for rel_id, source_entity_id, target_entity_id in relationships:
            # Get source entity name
            success, query_messages, source_entity = execute_query(
                f"""
                SELECT name 
                FROM {schema_name}.entities 
                WHERE entity_id = %s
                """,
                (source_entity_id,),
                schema_name
            )
            
            if not success or not source_entity:
                messages.extend(query_messages)
                logger.warning(f"Failed to get source entity for relationship {rel_id}: {query_messages}")
                continue
            
            source_entity_name = source_entity[0][0]
            
            # Get target entity name
            success, query_messages, target_entity = execute_query(
                f"""
                SELECT name 
                FROM {schema_name}.entities 
                WHERE entity_id = %s
                """,
                (target_entity_id,),
                schema_name
            )
            
            if not success or not target_entity:
                messages.extend(query_messages)
                logger.warning(f"Failed to get target entity for relationship {rel_id}: {query_messages}")
                continue
            
            target_entity_name = target_entity[0][0]
            
            # Check if the target entity ID is correct
            expected_target_id = entity_name_to_id.get(target_entity_name.lower())
            if expected_target_id and expected_target_id != target_entity_id:
                # Update the target entity ID
                success, query_messages, _ = execute_query(
                    f"""
                    UPDATE {schema_name}.entity_relationships
                    SET target_entity_id = %s
                    WHERE id = %s
                    """,
                    (expected_target_id, rel_id),
                    schema_name
                )
                
                if not success:
                    messages.extend(query_messages)
                    logger.warning(f"Failed to update target entity ID for relationship {rel_id}: {query_messages}")
                    continue
                
                logger.info(f"Updated target entity ID from {target_entity_id} to {expected_target_id} for relationship {rel_id}")
                messages.append(f"Updated target entity ID from {target_entity_id} to {expected_target_id} for relationship {rel_id}")
        
        logger.info("Relationship references fixed successfully")
        messages.append("Relationship references fixed successfully")
        return True, messages
    except Exception as e:
        error_msg = f"Error fixing relationship references: {str(e)}"
        logger.error(error_msg, exc_info=True)
        messages.append(error_msg)
        return False, messages

def fix_foreign_key_constraints() -> Tuple[bool, List[str]]:
    """
    Fix foreign key constraints to avoid duplicate constraints.
    
    Returns:
        Tuple containing:
            - Boolean indicating if the fix was successful
            - List of messages (warnings, errors, or success messages)
    """
    messages = []
    schema_name = "workflow_temp"
    
    try:
        logger.info("Fixing foreign key constraints")
        
        # Get all tables in the schema
        success, query_messages, tables = execute_query(
            f"""
            SELECT table_name 
            FROM information_schema.tables 
            WHERE table_schema = '{schema_name}'
            AND table_name LIKE 'e%'
            """
        )
        
        if not success:
            messages.extend(query_messages)
            return False, messages
        
        if not tables:
            logger.info("No entity tables found")
            messages.append("No entity tables found")
            return True, messages
        
        # For each table, get the foreign key constraints
        for table in tables:
            table_name = table[0]
            
            # Get foreign key constraints
            success, query_messages, constraints = execute_query(
                f"""
                SELECT
                    tc.constraint_name,
                    tc.table_name,
                    kcu.column_name,
                    ccu.table_name AS foreign_table_name,
                    ccu.column_name AS foreign_column_name
                FROM
                    information_schema.table_constraints AS tc
                    JOIN information_schema.key_column_usage AS kcu
                      ON tc.constraint_name = kcu.constraint_name
                      AND tc.table_schema = kcu.table_schema
                    JOIN information_schema.constraint_column_usage AS ccu
                      ON ccu.constraint_name = tc.constraint_name
                      AND ccu.table_schema = tc.table_schema
                WHERE
                    tc.constraint_type = 'FOREIGN KEY'
                    AND tc.table_schema = %s
                    AND tc.table_name = %s
                """,
                (schema_name, table_name)
            )
            
            if not success:
                messages.extend(query_messages)
                logger.warning(f"Failed to get foreign key constraints for table {table_name}: {query_messages}")
                continue
            
            # Check for duplicate constraints
            constraint_map = {}
            for constraint_name, _, column_name, foreign_table_name, foreign_column_name in constraints:
                key = f"{column_name}_{foreign_table_name}_{foreign_column_name}"
                if key in constraint_map:
                    # Duplicate constraint found, drop it
                    success, query_messages, _ = execute_query(
                        f"""
                        ALTER TABLE {schema_name}."{table_name}"
                        DROP CONSTRAINT "{constraint_name}"
                        """
                    )
                    
                    if not success:
                        messages.extend(query_messages)
                        logger.warning(f"Failed to drop duplicate constraint {constraint_name} for table {table_name}: {query_messages}")
                        continue
                    
                    logger.info(f"Dropped duplicate constraint {constraint_name} for table {table_name}")
                    messages.append(f"Dropped duplicate constraint {constraint_name} for table {table_name}")
                else:
                    constraint_map[key] = constraint_name
        
        logger.info("Foreign key constraints fixed successfully")
        messages.append("Foreign key constraints fixed successfully")
        return True, messages
    except Exception as e:
        error_msg = f"Error fixing foreign key constraints: {str(e)}"
        logger.error(error_msg, exc_info=True)
        messages.append(error_msg)
        return False, messages

def optimize_database_connections() -> Tuple[bool, List[str]]:
    """
    Optimize database connections in component_deployer.py.
    
    Returns:
        Tuple containing:
            - Boolean indicating if the fix was successful
            - List of messages (warnings, errors, or success messages)
    """
    messages = []
    
    try:
        logger.info("Optimizing database connections")
        
        # Read the component_deployer.py file
        component_deployer_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "component_deployer.py")
        
        if not os.path.exists(component_deployer_path):
            logger.warning(f"component_deployer.py not found at {component_deployer_path}")
            messages.append(f"Warning: component_deployer.py not found at {component_deployer_path}")
            return False, messages
        
        with open(component_deployer_path, 'r') as f:
            content = f.read()
        
        # Check if the file already has connection pooling
        if "connection_pool" in content:
            logger.info("component_deployer.py already has connection pooling")
            messages.append("component_deployer.py already has connection pooling")
            return True, messages
        
        # Add connection pooling
        import_section = "import os\nimport sys\nimport json\nimport logging\nfrom typing import Dict, List, Tuple, Any, Optional\n"
        new_import_section = "import os\nimport sys\nimport json\nimport logging\nimport psycopg2\nfrom psycopg2 import pool\nfrom typing import Dict, List, Tuple, Any, Optional\n"
        
        # Add connection pool
        connection_pool_code = """
# Create a connection pool
connection_pool = None

def get_connection_pool():
    global connection_pool
    if connection_pool is None:
        # Get database connection parameters
        connection_pool = pool.ThreadedConnectionPool(1, 10, 
                                                     host="**********",
                                                     database="workflow_system",
                                                     user="postgres",
                                                     password="workflow_postgres_secure_password",
                                                     port="5432")
    return connection_pool
"""
        
        # Replace the import section
        new_content = content.replace(import_section, new_import_section)
        
        # Add the connection pool code after the import section
        new_content = new_content.replace(new_import_section, new_import_section + connection_pool_code)
        
        # Write the modified content back to the file
        with open(component_deployer_path, 'w') as f:
            f.write(new_content)
        
        logger.info("Database connections optimized successfully")
        messages.append("Database connections optimized successfully")
        return True, messages
    except Exception as e:
        error_msg = f"Error optimizing database connections: {str(e)}"
        logger.error(error_msg, exc_info=True)
        messages.append(error_msg)
        return False, messages

def main():
    """
    Main function to run all fixes.
    """
    logger.info("Starting to implement missing features")
    
    # Fix enum values
    logger.info("=== Fixing Enum Values ===")
    success, messages = fix_enum_values_table()
    for message in messages:
        logger.info(message)
    
    # Fix validations
    logger.info("=== Fixing Validations ===")
    success, messages = fix_validations_table()
    for message in messages:
        logger.info(message)
    
    # Fix entity IDs
    logger.info("=== Fixing Entity IDs ===")
    success, messages = fix_entity_ids()
    for message in messages:
        logger.info(message)
    
    # Fix relationship references
    logger.info("=== Fixing Relationship References ===")
    success, messages = fix_relationship_references()
    for message in messages:
        logger.info(message)
    
    # Fix foreign key constraints
    logger.info("=== Fixing Foreign Key Constraints ===")
    success, messages = fix_foreign_key_constraints()
    for message in messages:
        logger.info(message)
    
    # Optimize database connections
    logger.info("=== Optimizing Database Connections ===")
    success, messages = optimize_database_connections()
    for message in messages:
        logger.info(message)
    
    logger.info("All fixes completed successfully")
    print("All fixes completed successfully. See implement_missing_features.log for details.")

if __name__ == "__main__":
    main()
