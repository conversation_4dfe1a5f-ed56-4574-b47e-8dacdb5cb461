import os
import sys
import time
import json
import traceback
import psycopg2
import datetime
from psycopg2.extras import Json

# === Configuration ===
WORKFLOW_PG_CONFIG = {
    "dbname": "postgres",  # Use existing database for workflow solution
    "user": "postgres",
    "password": "workflow_postgres_secure_password",
    "host": "**********",
    "port": "5432"
}

RUNTIME_PG_CONFIG = {
    "dbname": "workflow_system",  # Separate database for runtime tables
    "user": "postgres",
    "password": "workflow_postgres_secure_password",
    "host": "**********",
    "port": "5432"
}

def db_inserts_from_drafts():
     mongodb_uri = "mongodb://localhost:27017/"
     db_name =  "workflow_system"
     collection_name =  "workflow"

        # Simplified version to demonstrate the concept
     from pymongo import MongoClient
     client = MongoClient(mongodb_uri)
     db = client[db_name]
     collection = db[collection_name]
     draft_workflows = list(collection.find({"status": "draft"}))
        #workflow_doc = collection.find_one()
     draft_count = len(draft_workflows)

     if draft_count == 0:
            print("ℹ️ No draft documents found in MongoDB collection.")
     else:
          for workflow_doc in draft_workflows:
           try:
              if workflow_doc and "workflow_data" in workflow_doc:
               process_workflow_data_for_runtime(workflow_doc["workflow_data"])
            # Update the document status to "complete" in MongoDB
#               collection.update_one(
 #                   {"_id": workflow_doc["_id"]},
  #                  {"$set": {"status": "complete"}}
   #             )
            #print(f"✅ Updated MongoDB document status to 'complete' for tenant: {t> 
           except Exception as e:
             print(f"❌ Error processing document: {e}")
             traceback.print_exc()
             continue

# === Function to process workflow data and create runtime tables ===
def process_workflow_data_for_runtime(workflow_data):
    """Process workflow data to create runtime tables for entities."""
    # Extract tenant information
    tenant = workflow_data.get("tenant", {})
    tenant_id = tenant.get("id", "T001")
    tenant_name = tenant.get("name", "DefaultTenant")

    print(f"➡️ Processing runtime tables for tenant: {tenant_id} - {tenant_name}")

    # Check and create runtime_solution database
    #if not check_and_create_runtime_database():
     #   print("❌ Failed to create runtime_solution database.")
     #   return False

    # Create tenant schema in runtime database
    #schema_name = create_runtime_tenant_schema(tenant_id)
    #if not schema_name:
     #   print("❌ Failed to create tenant schema in runtime database.")
      #  return False

    # Extract entities
    entities = workflow_data.get("entities", [])
    if not entities:
        print("⚠️ No entities found in workflow data.")
        return False

    # Create tables for entities
    result = create_entity_tables(tenant_id, entities)
    
    # Process synthetic data if available
    process_synthetic_data(entities)

    return result
def check_and_create_runtime_database():
    """Check if runtime_solution database exists and create if needed."""
    try:
        # Connect to default postgres database
        default_config = WORKFLOW_PG_CONFIG.copy()
        temp_conn = psycopg2.connect(**default_config)
        temp_conn.autocommit = True
        temp_cursor = temp_conn.cursor()

        # Check if runtime_solution database exists
        temp_cursor.execute("SELECT 1 FROM pg_database WHERE datname = 'runtime_solution'")
        exists = temp_cursor.fetchone()

        if exists:
            print(f"ℹ️ Runtime solution database already exists.")
        else:
            print(f"➡️ Creating runtime_solution database...")
            temp_cursor.execute("CREATE DATABASE runtime_solution")
            print(f"✅ Created runtime_solution database.")

        temp_cursor.close()
        temp_conn.close()
        return True

    except Exception as e:
        print(f"❌ Error checking/creating runtime database: {e}")
        traceback.print_exc()
        return False
# === Function to connect to PostgreSQL with retry ===
def connect_with_retry(db_config, retries=5, delay=1):
    """Attempt to connect to PostgreSQL with retries."""
    for attempt in range(retries):
        try:
            return psycopg2.connect(**db_config)
        except psycopg2.OperationalError:
            print(f"⏳ Waiting for DB to be ready... retry {attempt + 1}/{retries}")
            time.sleep(delay)
    raise Exception("❌ Could not connect to database after multiple retries.")
def create_runtime_tenant_schema(tenant_id):
    """Create a schema for tenant in the runtime_solution database."""
    schema_name = "workflow_runtime"

    try:
        # Connect to runtime_solution database
        runtime_conn = connect_with_retry(RUNTIME_PG_CONFIG)
        runtime_conn.autocommit = True

        with runtime_conn.cursor() as cursor:
            # Check if schema exists
            cursor.execute("SELECT schema_name FROM information_schema.schemata WHERE schema_name = %s", (schema_name,))
            exists = cursor.fetchone()

            if exists:
                print(f"⚠️ Runtime schema {schema_name} already exists.")
                recreate = input("Do you want to recreate the runtime schema? (y/n): ").lower() == 'y'

                if recreate:
                    print(f"➡️ Dropping existing runtime schema: {schema_name}")
                    cursor.execute(f"DROP SCHEMA {schema_name} CASCADE")
                    print(f"✅ Dropped old runtime schema: {schema_name}")

                    cursor.execute(f"CREATE SCHEMA {schema_name}")
                    print(f"✅ Created runtime schema: {schema_name}")
                else:
                    print(f"ℹ️ Using existing runtime schema: {schema_name}")
            else:
                print(f"➡️ Creating runtime schema: {schema_name}")
                cursor.execute(f"CREATE SCHEMA {schema_name}")
                print(f"✅ Created runtime schema: {schema_name}")

        runtime_conn.close()
        return schema_name

    except Exception as e:
        print(f"❌ Error creating runtime tenant schema: {e}")
        traceback.print_exc()
        return None

import re

def camel_to_snake(name):
    # First insert underscore before uppercase letters
    s1 = re.sub('(.)([A-Z][a-z]+)', r'\1_\2', name)
    # Then handle cases like 'ID' where we have multiple uppercase letters
    s2 = re.sub('([a-z0-9])([A-Z])', r'\1_\2', s1)
    # Replace other non-alphanumeric characters with underscores
    s3 = re.sub(r'[^a-zA-Z0-9]', '_', s2)
    # Convert to lowercase
    s4 = s3.lower()
    # Replace multiple consecutive underscores with a single underscore
    s5 = re.sub(r'_+', '_', s4)
    # Remove leading or trailing underscores
    return s5.strip('_')

# === Function to create entity tables in runtime schema ===
def create_entity_tables(tenant_id, entities):
    """Create tables for each entity in the tenant's runtime schema."""
    #schema_name = f"tenant_{tenant_id.lower()}"
    schema_name = "workflow_runtime"

    try:
        # Connect to runtime_solution database
        runtime_conn = connect_with_retry(RUNTIME_PG_CONFIG)
        runtime_conn.autocommit = True

        with runtime_conn.cursor() as cursor:
            # Set search path to tenant schema
            cursor.execute(f"SET search_path TO {schema_name}")

            # Process each entity
            for entity in entities:
                entity_id = entity.get("id", "")
                entity_name = entity.get("name", "")
                attributes = entity.get("attributes", [])

                print(f"➡️ Processing entity: {entity_id} - {entity_name}")

                # Check if table already exists
                table_name = f"z_entity_{camel_to_snake(entity_name)}"
                cursor.execute(f"SELECT to_regclass('{schema_name}.{table_name}')")

                exists = cursor.fetchone()[0]

                if exists:
                    print(f"ℹ️ Table for entity {entity_id} already exists, skipping creation")
                    continue

                # Generate SQL for entity table
                table_sql = generate_entity_table_sql(entity_id, entity_name, attributes)

                # Execute SQL to create table
                try:
                    cursor.execute(table_sql)
                    print(f"✅ Created table for entity: {entity_id}")
                except Exception as e:
                    print(f"❌ Error creating table for entity {entity_id}: {e}")
                    print(f"SQL Statement with error:\n{table_sql}")

        runtime_conn.close()
        print(f"✅ Entity tables created in runtime schema: {schema_name}")
        return True

    except Exception as e:
        print(f"❌ Error creating entity tables: {e}")
        traceback.print_exc()
        return False

def generate_entity_table_sql(entity_id, entity_name, attributes):
    """Generate SQL to create a table for an entity with its attributes."""
    # Convert entity_name to snake_case (lowercase with underscores)
    # Insert underscore before uppercase letters in camelCase

    # Add 'z_entity_' prefix to distinguish from runtime tables and sort them at the bottom
    table_name = f"z_entity_{camel_to_snake(entity_name)}"

    # Start SQL statement
    sql = f"CREATE TABLE IF NOT EXISTS workflow_runtime.{table_name} (\n"

    # Add primary key
    sql += "    id SERIAL PRIMARY KEY,\n"

    # Add standard audit fields
    sql += "    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,\n"
    sql += "    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,\n"
    sql += "    created_by VARCHAR(255),\n"
    sql += "    updated_by VARCHAR(255),\n"

    # Add each attribute
    for attr in attributes:
        attr_id = attr.get("id", "")
        attr_name = attr.get("name", "")
        datatype = attr.get("datatype", "").lower()
        required = attr.get("required", False)

        # Skip if attribute name is empty
        if not attr_name:
            continue

        # Convert attribute name to snake_case handling camelCase properly
        column_name = camel_to_snake(attr_name)

        # Map attribute datatype to PostgreSQL datatype
        pg_type = "VARCHAR(255)"  # Default

        if datatype == "string":
            pg_type = "VARCHAR(255)"
        elif datatype == "number":
            pg_type = "NUMERIC"
        elif datatype == "integer":
            pg_type = "INTEGER"
        elif datatype == "date":
            pg_type = "DATE"
        elif datatype == "datetime":
            pg_type = "TIMESTAMP"
        elif datatype == "boolean":
            pg_type = "BOOLEAN"
        elif datatype == "enum":
            # Create enum values as a check constraint later
            pg_type = "VARCHAR(50)"
        elif datatype == "reference":
            # For references, we'll use the ID as foreign key
            pg_type = "VARCHAR(255)"
        elif datatype == "text":
            pg_type = "TEXT"

        # Add column with required constraint if needed
       # required_str = "NOT NULL" if required else ""
        required_str = "NULL"

        sql += f"    {column_name} {pg_type} {required_str},\n"

    # Remove trailing comma and close statement
    sql = sql.rstrip(",\n") + "\n);\n"

    # Add enum constraints if applicable
    for attr in attributes:
        if attr.get("datatype", "").lower() == "enum":
            attr_name = attr.get("name", "")
            # Convert to snake_case
            attr_name_snake = camel_to_snake(attr_name)

            enum_values = attr.get("values", [])

            if enum_values and attr_name_snake:
                values_list = ", ".join([f"'{val}'" for val in enum_values])
                constraint_name = f"chk_{table_name}_{attr_name_snake}"

                # Check if constraint exists before creating it
                check_sql = f"""
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM pg_constraint
        WHERE conname = '{constraint_name}' AND conrelid = '{table_name}'::regclass
    ) THEN
        ALTER TABLE workflow_runtime.{table_name} ADD CONSTRAINT {constraint_name}
        CHECK ({attr_name_snake} IN ({values_list}));
    END IF;
END $$;
"""
            #    sql += check_sql
     # Add timestamps trigger for updated_at
    sql += f"""
-- Create or replace function for updating timestamp
CREATE OR REPLACE FUNCTION update_modified_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for updating timestamp
DROP TRIGGER IF EXISTS update_{table_name}_timestamp ON workflow_runtime.{table_name};
CREATE TRIGGER update_{table_name}_timestamp
BEFORE UPDATE ON workflow_runtime.{table_name}
FOR EACH ROW
EXECUTE FUNCTION update_modified_column();

-- Add table comment with metadata
COMMENT ON TABLE workflow_runtime.{table_name} IS 'Dynamic entity table created from entity {entity_id} - {entity_name}. This table is managed by the workflow engine.';
"""

    return sql

# if __name__ == "__main__":

#         mongodb_uri = "mongodb://localhost:27017/"
#         db_name =  "workflow_system"
#         collection_name =  "workflow"

#         # Simplified version to demonstrate the concept
#         from pymongo import MongoClient
#         client = MongoClient(mongodb_uri)
#         db = client[db_name]
#         collection = db[collection_name]
#         draft_workflows = list(collection.find({"status": "draft"}))
#         #workflow_doc = collection.find_one()
#         draft_count = len(draft_workflows)

#         if draft_count == 0:
#             print("ℹ️ No draft documents found in MongoDB collection.")
#         else:
#           for workflow_doc in draft_workflows:
#              try:
#               if workflow_doc and "workflow_data" in workflow_doc:
#                process_workflow_data_for_runtime(workflow_doc["workflow_data"])
#             # Update the document status to "complete" in MongoDB
#                collection.update_one(
#                     {"_id": workflow_doc["_id"]},
#                     {"$set": {"status": "complete"}}
#                 )
#             #print(f"✅ Updated MongoDB document status to 'complete' for tenant: {tenant_id}")
#              except Exception as e:
#                 print(f"❌ Error processing document: {e}")
#                 traceback.print_exc()
#              continue

# Add to db_inserter.py

def insert_user_entity(conn, user_entity):
    '''Insert a User entity into the database.'''
    cursor = conn.cursor()
    
    # Extract user attributes
    user_id = user_entity.get("id")
    attributes = user_entity.get("attributes", [])
    
    # Find required attributes
    user_id_attr = None
    username_attr = None
    email_attr = None
    status_attr = None
    
    for attr in attributes:
        if attr.get("name") == "user_id":
            user_id_attr = attr
        elif attr.get("name") == "username":
            username_attr = attr
        elif attr.get("name") == "email":
            email_attr = attr
        elif attr.get("name") == "status":
            status_attr = attr
    
    # Insert into users table
    if user_id_attr and username_attr and email_attr and status_attr:
        sql = '''
        INSERT INTO users (user_id, username, email, status)
        VALUES (%s, %s, %s, %s)
        '''
        cursor.execute(sql, (
            user_id_attr.get("default_value", ""),
            username_attr.get("default_value", ""),
            email_attr.get("default_value", ""),
            status_attr.get("default_value", "active")
        ))
    
    # Insert into entities table
    sql = '''
    INSERT INTO entities (id, name, type, version, status, entity_class)
    VALUES (%s, %s, %s, %s, %s, %s)
    '''
    cursor.execute(sql, (
        user_id,
        user_entity.get("name"),
        user_entity.get("type"),
        user_entity.get("version"),
        user_entity.get("status"),
        "organizational"
    ))
    
    conn.commit()

def insert_role_entity(conn, role_entity):
    '''Insert a Role entity into the database.'''
    cursor = conn.cursor()
    
    # Extract role attributes
    role_id = role_entity.get("id")
    attributes = role_entity.get("attributes", [])
    
    # Find required attributes
    role_id_attr = None
    name_attr = None
    
    for attr in attributes:
        if attr.get("name") == "role_id":
            role_id_attr = attr
        elif attr.get("name") == "name":
            name_attr = attr
    
    # Insert into roles table
    if role_id_attr and name_attr:
        sql = '''
        INSERT INTO roles (role_id, name, description)
        VALUES (%s, %s, %s)
        '''
        cursor.execute(sql, (
            role_id_attr.get("default_value", ""),
            name_attr.get("default_value", ""),
            ""  # Default empty description
        ))
    
    # Insert into entities table
    sql = '''
    INSERT INTO entities (id, name, type, version, status, entity_class)
    VALUES (%s, %s, %s, %s, %s, %s)
    '''
    cursor.execute(sql, (
        role_id,
        role_entity.get("name"),
        role_entity.get("type"),
        role_entity.get("version"),
        role_entity.get("status"),
        "organizational"
    ))
    
    conn.commit()

def insert_user_role_relationship(conn, user_email, role_id):
    '''Insert a relationship between a User and a Role.'''
    cursor = conn.cursor()
    
    # Find user_id by email
    sql = "SELECT user_id FROM users WHERE email = %s"
    cursor.execute(sql, (user_email,))
    result = cursor.fetchone()
    
    if not result:
        print(f"Warning: User with email {user_email} not found")
        return
    
    user_id = result[0]
    
    # Insert into user_roles table
    sql = '''
    INSERT INTO user_roles (user_id, role_id)
    VALUES (%s, %s)
    '''
    cursor.execute(sql, (user_id, role_id))
    
    # Insert into entity_relationships table
    sql = '''
    INSERT INTO entity_relationships (source_entity, source_id, target_entity, target_id, relationship_type)
    VALUES (%s, %s, %s, %s, %s)
    '''
    cursor.execute(sql, (
        "User",
        user_id,
        "Role",
        role_id,
        "has_role"
    ))
    
    conn.commit()

# === Synthetic Data Processing Functions ===

def process_synthetic_data(entities):
    """
    Process synthetic data defined in entities and insert into runtime tables.
    
    Args:
        entities: List of entity definitions from the YAML file
    """
    print("➡️ Processing synthetic data for entities...")
    print(f"DEBUG: Number of entities to process: {len(entities)}")
    
    try:
        # Connect to runtime database
        runtime_conn = connect_with_retry(RUNTIME_PG_CONFIG)
        runtime_conn.autocommit = True
        
        entities_with_synthetic_data = 0
        records_processed = 0
        
        for entity in entities:
            entity_id = entity.get("id", "")
            entity_name = entity.get("name", "")
            
            print(f"DEBUG: Checking entity {entity_id} - {entity_name} for synthetic_records")
            
            # Check if entity has synthetic_records
            synthetic_records = entity.get("synthetic_records", [])
            
            if synthetic_records:
                entities_with_synthetic_data += 1
                print(f"➡️ Found {len(synthetic_records)} synthetic records for entity {entity_id} - {entity_name}")
                
                # Insert synthetic records
                records_inserted = insert_synthetic_records(runtime_conn, entity, synthetic_records)
                records_processed += records_inserted
            else:
                print(f"DEBUG: No synthetic_records found for entity {entity_id}")
        
        runtime_conn.close()
        print(f"✅ Synthetic data processing complete. Processed {records_processed} records from {entities_with_synthetic_data} entities.")
        return True
        
    except Exception as e:
        print(f"❌ Error processing synthetic data: {e}")
        traceback.print_exc()
        return False

def insert_synthetic_records(conn, entity, records):
    """
    Insert synthetic records for an entity into the runtime database.
    
    Args:
        conn: Database connection
        entity: Entity definition
        records: List of synthetic records to insert
    
    Returns:
        Number of records successfully inserted
    """
    entity_id = entity.get("id", "")
    entity_name = entity.get("name", "")
    table_name = f"z_entity_{camel_to_snake(entity_name)}"
    schema_name = "workflow_runtime"
    
    print(f"DEBUG: Inserting synthetic records for entity {entity_id} into table {schema_name}.{table_name}")
    
    # Check if table exists
    try:
        cursor = conn.cursor()
        cursor.execute(f"SELECT to_regclass('{schema_name}.{table_name}')")
        table_exists = cursor.fetchone()[0]
        
        if not table_exists:
            print(f"❌ Table {schema_name}.{table_name} does not exist. Cannot insert synthetic records.")
            return 0
        
        print(f"DEBUG: Table {schema_name}.{table_name} exists, proceeding with inserts")
    except Exception as e:
        print(f"❌ Error checking if table exists: {e}")
        traceback.print_exc()
        return 0
    
    # Get attribute mapping for column names
    attributes = entity.get("attributes", [])
    attr_map = {}
    attr_id_to_name = {}  # Map attribute IDs to attribute names
    
    for attr in attributes:
        attr_id = attr.get("id", "")
        attr_name = attr.get("name", "")
        if attr_id and attr_name:
            attr_map[attr_id] = camel_to_snake(attr_name)
            attr_id_to_name[attr_id] = attr_name
    
    print(f"DEBUG: Attribute mapping: {attr_map}")
    
    # Get attribute_metadata mapping (original IDs to names)
    attr_metadata = entity.get("attributes_metadata", {})
    attr_map_metadata = attr_metadata.get("attribute_map", {})
    
    # Create a reverse mapping from original attribute IDs to column names
    original_attr_to_column = {}
    for orig_attr_id, attr_name in attr_map_metadata.items():
        column_name = camel_to_snake(attr_name)
        original_attr_to_column[orig_attr_id] = column_name
    
    print(f"DEBUG: Original attribute to column mapping: {original_attr_to_column}")
    
    cursor = conn.cursor()
    
    # Set search path to schema
    cursor.execute(f"SET search_path TO {schema_name}")
    
    # Get table columns to validate
    try:
        cursor.execute(f"""
            SELECT column_name 
            FROM information_schema.columns 
            WHERE table_schema = '{schema_name}' 
            AND table_name = '{table_name}'
        """)
        
        table_columns = [row[0] for row in cursor.fetchall()]
        print(f"DEBUG: Table columns: {table_columns}")
    except Exception as e:
        print(f"❌ Error getting table columns: {e}")
        traceback.print_exc()
        return 0
    
    # Process each record
    records_inserted = 0
    for record_idx, record in enumerate(records):
        try:
            print(f"DEBUG: Processing record {record_idx + 1}: {record}")
            
            # Prepare column names and values
            columns = []
            values = []
            placeholders = []
            
            for field_id, value in record.items():
                # First try to map using the original attribute IDs from metadata
                if field_id in original_attr_to_column:
                    column_name = original_attr_to_column[field_id]
                    print(f"DEBUG: Mapped original attribute ID {field_id} to column {column_name}")
                # Then try to map using the current attribute IDs
                elif field_id in attr_map:
                    column_name = attr_map[field_id]
                    print(f"DEBUG: Mapped current attribute ID {field_id} to column {column_name}")
                else:
                    # If not in mappings, try direct mapping or use as is
                    column_name = camel_to_snake(field_id)
                    print(f"DEBUG: Using direct mapping for {field_id} to column {column_name}")
                
                # Check if column exists in table
                if column_name not in table_columns:
                    print(f"⚠️ Column {column_name} does not exist in table {table_name}, skipping")
                    continue
                
                columns.append(column_name)
                values.append(value)
                placeholders.append("%s")
            
            # Add created_at and updated_at if they exist in the table
            now = datetime.datetime.now()
            if "created_at" in table_columns:
                columns.append("created_at")
                values.append(now)
                placeholders.append("%s")
            
            if "updated_at" in table_columns:
                columns.append("updated_at")
                values.append(now)
                placeholders.append("%s")
            
            # Build and execute INSERT statement
            if columns and values and placeholders:
                columns_str = ", ".join(columns)
                placeholders_str = ", ".join(placeholders)
                
                sql = f"INSERT INTO {schema_name}.{table_name} ({columns_str}) VALUES ({placeholders_str}) ON CONFLICT DO NOTHING"
                print(f"DEBUG: SQL: {sql}")
                print(f"DEBUG: Values: {values}")
                
                cursor.execute(sql, values)
                records_inserted += 1
                print(f"DEBUG: Record {record_idx + 1} inserted successfully")
            else:
                print(f"⚠️ No valid columns found for record {record_idx + 1}, skipping")
            
        except Exception as e:
            print(f"❌ Error inserting synthetic record {record_idx + 1} for {entity_id}: {e}")
            traceback.print_exc()
            conn.rollback()
            continue
    
    conn.commit()
    print(f"✅ Inserted {records_inserted} synthetic records for entity {entity_id}")
    return records_inserted
