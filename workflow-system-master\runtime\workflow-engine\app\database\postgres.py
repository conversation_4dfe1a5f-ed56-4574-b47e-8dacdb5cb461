from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker, DeclarativeBase
from sqlalchemy.ext.declarative import declarative_base
from app.core.config import settings

# PostgreSQL connection string
DATABASE_URL = (
    f"postgresql://{settings.POSTGRES_USER}:"
    f"{settings.POSTGRES_PASSWORD}@"
    f"{settings.POSTGRES_SERVER}:"
    f"{settings.POSTGRES_PORT}/"
    f"{settings.POSTGRES_DB}"
)

# Create SQLAlchemy engine
engine = create_engine(
    DATABASE_URL, 
    pool_pre_ping=True,  # Test connections before using them
    pool_size=10,  # Number of connections to keep open
    max_overflow=20  # Maximum number of connections beyond pool_size
)

# Create SessionLocal class for database sessions
SessionLocal = sessionmaker(
    autocommit=False,  # Disable auto-commit
    autoflush=False,   # Disable auto-flush
    bind=engine
)

# Base class for declarative models
class Base(DeclarativeBase):
    """Base class for SQLAlchemy declarative models"""
    pass

def get_db():
    """
    Dependency to get a database session
    
    Yields:
        Session: Database session that can be used in a context
    """
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()
