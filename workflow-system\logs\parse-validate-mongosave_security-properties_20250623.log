{"timestamp": "2025-06-23T05:19:56.599985", "endpoint": "parse-validate-mongosave/security-properties", "input": {"natural_language": "Tenant: Acme Corp\n\nEntity: LeaveApplication\n\nSecurity Properties:\nProperty Name | Property Type | Value | Description | Applies To\n\nencryption_required | encryption | true | Data must be encrypted at rest | data\n\naccess_control | permission | role_based | Role-based access control | entity\n\naudit_logging | logging | enabled | Enable audit logging for all operations | operations\n\ndata_retention | retention | 7_years | Retain data for 7 years | data\n\npii_protection | privacy | enabled | Enable PII protection | data\n\nsession_timeout | timeout | 30_minutes | Session timeout after 30 minutes | session", "tenant_id": "T_ACME_CORP", "edit_draft": false, "edit_temp": false, "edit_production": false}, "output": {"success": true, "security_property_results": [], "operation": "parse_validate_mongosave", "total_security_properties": 0}, "status": "success"}
{"timestamp": "2025-06-23T08:01:11.854531", "endpoint": "parse-validate-mongosave/security-properties", "input": {"natural_language": "Tenant: Acme Corp\n\nEntity: LeaveApplication\n\nSecurity Properties:\nProperty Name | Property Type | Value | Description | Applies To\n\nencryption_required | encryption | true | Data must be encrypted at rest | data\n\naccess_control | permission | role_based | Role-based access control | entity\n\naudit_logging | logging | enabled | Enable audit logging for all operations | operations\n\ndata_retention | retention | 7_years | Retain data for 7 years | data\n\npii_protection | privacy | enabled | Enable PII protection | data\n\nsession_timeout | timeout | 30_minutes | Session timeout after 30 minutes | session", "tenant_id": "T_ACME_CORP", "edit_draft": false, "edit_temp": false, "edit_production": false}, "output": {"success": true, "security_property_results": [], "operation": "parse_validate_mongosave", "total_security_properties": 0}, "status": "success"}
{"timestamp": "2025-06-23T09:01:49.092531", "endpoint": "parse-validate-mongosave/security-properties", "input": {"natural_language": "Tenant: Acme Corp\n\nEntity.Attribute | Classification | PII Type | Encryption Required | Encryption Type | Masking Required | Masking Pattern | Access Level | Audit Trail | Data Residency | Retention Override | Anonymization Required | Anonymization Method | Compliance Frameworks\n\nLeaveApplication.leaveId | internal | none | false | | false | | read_internal | true | global | | false | | gdpr\n\nLeaveApplication.employeeId | confidential | name | false | | true | EMP*** | read_restricted | true | global | | true | tokenize | gdpr, hipaa\n\nLeaveApplication.startDate | internal | none | false | | false | | read_internal | true | global | | false | | gdpr\n\nLeaveApplication.endDate | internal | none | false | | false | | read_internal | true | global | | false | | gdpr\n\nLeaveApplication.numDays | internal | none | false | | false | | read_internal | true | global | | false | | gdpr\n\nLeaveApplication.reason | confidential | medical | true | aes256 | true | ***REDACTED*** | read_restricted | true | global | 2555 | true | hash | gdpr, hipaa\n\nLeaveApplication.leaveTypeName | internal | none | false | | false | | read_internal | true | global | | false | | gdpr\n\nLeaveApplication.leaveSubTypeName | internal | none | false | | false | | read_internal | true | global | | false | | gdpr\n\nLeaveApplication.status | internal | none | false | | false | | read_internal | true | global | | false | | gdpr\n\nLeaveApplication.requiresDocumentation | internal | none | false | | false | | read_internal | true | global | | false | | gdpr\n\nLeaveApplication.documentationProvided | confidential | medical | true | aes256 | true | ***DOCUMENT*** | read_restricted | true | global | 2555 | true | remove | gdpr, hipaa\n\nLeaveApplication.submissionDate | internal | none | false | | false | | read_internal | true | global | | false | | gdpr\n\nLeaveApplication.approvalDate | internal | none | false | | false | | read_internal | true | global | | false | | gdpr\n\nLeaveApplication.approvedBy | confidential | name | false | | true | EMP*** | read_restricted | true | global | | true | tokenize | gdpr, hipaa\n\nLeaveApplication.comments | confidential | none | true | aes256 | true | ***COMMENTS*** | read_restricted | true | global | 2555 | true | hash | gdpr\n\nEmployee.employeeId | confidential | name | false | | true | EMP*** | read_restricted | true | global | | true | tokenize | gdpr, hipaa\n\nEmployee.firstName | confidential | name | true | aes256 | true | ***NAME*** | read_restricted | true | global | | true | pseudonymize | gdpr, hipaa\n\nEmployee.lastName | confidential | name | true | aes256 | true | ***NAME*** | read_restricted | true | global | | true | pseudonymize | gdpr, hipaa\n\nEmployee.email | confidential | email | true | aes256 | true | ***@***.com | read_restricted | true | global | | true | hash | gdpr, hipaa\n\nEmployee.phone | confidential | phone | true | aes256 | true | +**-***-***-**** | read_restricted | true | global | | true | hash | gdpr, hipaa\n\nEmployee.departmentId | internal | none | false | | false | | read_internal | true | global | | false | | gdpr\n\nEmployee.managerId | confidential | name | false | | true | EMP*** | read_restricted | true | global | | true | tokenize | gdpr, hipaa\n\nEmployee.jobTitle | internal | none | false | | false | | read_internal | true | global | | false | | gdpr\n\nEmployee.hireDate | internal | none | false | | false | | read_internal | true | global | | false | | gdpr\n\nEmployee.employmentStatus | internal | none | false | | false | | read_internal | true | global | | false | | gdpr\n\nEmployee.annualLeaveBalance | confidential | financial | true | aes256 | true | **.* days | read_restricted | true | global | | true | hash | gdpr\n\nEmployee.sickLeaveBalance | confidential | medical | true | aes256 | true | **.* days | read_restricted | true | global | 2555 | true | hash | gdpr, hipaa\n\nEmployee.personalLeaveBalance | confidential | financial | true | aes256 | true | **.* days | read_restricted | true | global | | true | hash | gdpr\n\nEmployee.totalLeaveEntitlement | internal | none | false | | false | | read_internal | true | global | | false | | gdpr\n\nLeaveType.leaveTypeId | public | none | false | | false | | read_public | false | global | | false | | \n\nLeaveType.leaveTypeName | public | none | false | | false | | read_public | false | global | | false | | \n\nLeaveType.description | public | none | false | | false | | read_public | false | global | | false | | \n\nLeaveType.maxDaysPerYear | public | none | false | | false | | read_public | false | global | | false | | \n\nLeaveType.requiresDocumentation | public | none | false | | false | | read_public | false | global | | false | | \n\nLeaveType.advanceNoticeDays | public | none | false | | false | | read_public | false | global | | false | | \n\nLeaveType.isCarryForward | public | none | false | | false | | read_public | false | global | | false | | \n\nLeaveType.status | public | none | false | | false | | read_public | false | global | | false | | \n\nLeaveSubType.leaveSubTypeId | public | none | false | | false | | read_public | false | global | | false | | \n\nLeaveSubType.leaveTypeName | public | none | false | | false | | read_public | false | global | | false | | \n\nLeaveSubType.leaveSubTypeName | public | none | false | | false | | read_public | false | global | | false | | \n\nLeaveSubType.description | public | none | false | | false | | read_public | false | global | | false | | \n\nLeaveSubType.maxDaysPerRequest | public | none | false | | false | | read_public | false | global | | false | | \n\nLeaveSubType.requiresManagerApproval | public | none | false | | false | | read_public | false | global | | false | | \n\nLeaveSubType.status | public | none | false | | false | | read_public | false | global | | false | | \n\nConstants.constantId | internal | none | false | | false | | read_internal | true | global | | false | | \n\nConstants.attribute | internal | none | false | | false | | read_internal | true | global | | false | | \n\nConstants.value | internal | none | false | | false | | read_internal | true | global | | false | | \n\nConstants.description | internal | none | false | | false | | read_internal | true | global | | false | | \n\nConstants.dataType | internal | none | false | | false | | read_internal | true | global | | false | | \n\nConstants.status | internal | none | false | | false | | read_internal | true | global | | false | |", "tenant_id": "T1001", "edit_draft": true, "edit_temp": false, "edit_production": false}, "output": {"success": true, "security_property_results": [{"success": false, "errors": ["Attribute with attribute_id A_E13_leaveId_1750669306 does not exist"], "parsed_data": {"security_property_id": "SEC1", "entity_id": "E13", "attribute_id": "A_E13_leaveId_1750669306", "classification": "internal", "pii_type": "none", "encryption_required": false, "encryption_type": "", "masking_required": false, "masking_pattern": "", "access_level": "read_internal", "audit_trail": true, "data_residency": "global", "retention_override": "", "anonymization_required": false, "anonymization_method": "", "compliance_frameworks": "gdpr", "data_purpose": "", "consent_required": false, "cross_border_transfer": false, "backup_encryption": false, "secure_deletion": false, "natural_language": "Entity: LeaveApplication\nAttribute: leaveId\nClassification: internal\nAccess Level: read_internal\nAudit Trail: true\nData Residency: global\nCompliance Frameworks: gdpr", "version": 1, "status": "draft", "created_at": "2025-06-23T09:01:46.787477", "updated_at": "2025-06-23T09:01:46.787477", "created_by": "Tarun", "updated_by": "Tarun", "security_property_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Attribute with attribute_id A_E13_employeeId_1750669306 does not exist"], "parsed_data": {"security_property_id": "SEC1", "entity_id": "E13", "attribute_id": "A_E13_employeeId_1750669306", "classification": "confidential", "pii_type": "name", "encryption_required": false, "encryption_type": "", "masking_required": true, "masking_pattern": "EMP***", "access_level": "read_restricted", "audit_trail": true, "data_residency": "global", "retention_override": "", "anonymization_required": true, "anonymization_method": "tokenize", "compliance_frameworks": "gdpr, hipaa", "data_purpose": "", "consent_required": false, "cross_border_transfer": false, "backup_encryption": false, "secure_deletion": false, "natural_language": "Entity: LeaveApplication\nAttribute: employeeId\nClassification: confidential\nPII Type: name\nMasking Required: true\nMasking Pattern: EMP***\nAccess Level: read_restricted\nAudit Trail: true\nData Residency: global\nAnonymization Required: true\nAnonymization Method: tokenize\nCompliance Frameworks: gdpr, hipaa", "version": 1, "status": "draft", "created_at": "2025-06-23T09:01:46.823136", "updated_at": "2025-06-23T09:01:46.823136", "created_by": "Tarun", "updated_by": "Tarun", "security_property_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Attribute with attribute_id A_E13_startDate_1750669306 does not exist"], "parsed_data": {"security_property_id": "SEC1", "entity_id": "E13", "attribute_id": "A_E13_startDate_1750669306", "classification": "internal", "pii_type": "none", "encryption_required": false, "encryption_type": "", "masking_required": false, "masking_pattern": "", "access_level": "read_internal", "audit_trail": true, "data_residency": "global", "retention_override": "", "anonymization_required": false, "anonymization_method": "", "compliance_frameworks": "gdpr", "data_purpose": "", "consent_required": false, "cross_border_transfer": false, "backup_encryption": false, "secure_deletion": false, "natural_language": "Entity: LeaveApplication\nAttribute: startDate\nClassification: internal\nAccess Level: read_internal\nAudit Trail: true\nData Residency: global\nCompliance Frameworks: gdpr", "version": 1, "status": "draft", "created_at": "2025-06-23T09:01:46.852680", "updated_at": "2025-06-23T09:01:46.852680", "created_by": "Tarun", "updated_by": "Tarun", "security_property_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Attribute with attribute_id A_E13_endDate_1750669306 does not exist"], "parsed_data": {"security_property_id": "SEC1", "entity_id": "E13", "attribute_id": "A_E13_endDate_1750669306", "classification": "internal", "pii_type": "none", "encryption_required": false, "encryption_type": "", "masking_required": false, "masking_pattern": "", "access_level": "read_internal", "audit_trail": true, "data_residency": "global", "retention_override": "", "anonymization_required": false, "anonymization_method": "", "compliance_frameworks": "gdpr", "data_purpose": "", "consent_required": false, "cross_border_transfer": false, "backup_encryption": false, "secure_deletion": false, "natural_language": "Entity: LeaveApplication\nAttribute: endDate\nClassification: internal\nAccess Level: read_internal\nAudit Trail: true\nData Residency: global\nCompliance Frameworks: gdpr", "version": 1, "status": "draft", "created_at": "2025-06-23T09:01:46.881025", "updated_at": "2025-06-23T09:01:46.881025", "created_by": "Tarun", "updated_by": "Tarun", "security_property_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Attribute with attribute_id A_E13_numDays_1750669306 does not exist"], "parsed_data": {"security_property_id": "SEC1", "entity_id": "E13", "attribute_id": "A_E13_numDays_1750669306", "classification": "internal", "pii_type": "none", "encryption_required": false, "encryption_type": "", "masking_required": false, "masking_pattern": "", "access_level": "read_internal", "audit_trail": true, "data_residency": "global", "retention_override": "", "anonymization_required": false, "anonymization_method": "", "compliance_frameworks": "gdpr", "data_purpose": "", "consent_required": false, "cross_border_transfer": false, "backup_encryption": false, "secure_deletion": false, "natural_language": "Entity: LeaveApplication\nAttribute: numDays\nClassification: internal\nAccess Level: read_internal\nAudit Trail: true\nData Residency: global\nCompliance Frameworks: gdpr", "version": 1, "status": "draft", "created_at": "2025-06-23T09:01:46.907909", "updated_at": "2025-06-23T09:01:46.907909", "created_by": "Tarun", "updated_by": "Tarun", "security_property_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Attribute with attribute_id A_E13_reason_1750669306 does not exist"], "parsed_data": {"security_property_id": "SEC1", "entity_id": "E13", "attribute_id": "A_E13_reason_1750669306", "classification": "confidential", "pii_type": "medical", "encryption_required": true, "encryption_type": "aes256", "masking_required": true, "masking_pattern": "***REDACTED***", "access_level": "read_restricted", "audit_trail": true, "data_residency": "global", "retention_override": "2555", "anonymization_required": true, "anonymization_method": "hash", "compliance_frameworks": "gdpr, hipaa", "data_purpose": "", "consent_required": false, "cross_border_transfer": false, "backup_encryption": false, "secure_deletion": false, "natural_language": "Entity: LeaveApplication\nAttribute: reason\nClassification: confidential\nPII Type: medical\nEncryption Required: true\nEncryption Type: aes256\nMasking Required: true\nMasking Pattern: ***REDACTED***\nAccess Level: read_restricted\nAudit Trail: true\nData Residency: global\nRetention Override: 2555\nAnonymization Required: true\nAnonymization Method: hash\nCompliance Frameworks: gdpr, hipaa", "version": 1, "status": "draft", "created_at": "2025-06-23T09:01:46.932753", "updated_at": "2025-06-23T09:01:46.932753", "created_by": "Tarun", "updated_by": "Tarun", "security_property_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Attribute with attribute_id A_E13_leaveTypeName_1750669306 does not exist"], "parsed_data": {"security_property_id": "SEC1", "entity_id": "E13", "attribute_id": "A_E13_leaveTypeName_1750669306", "classification": "internal", "pii_type": "none", "encryption_required": false, "encryption_type": "", "masking_required": false, "masking_pattern": "", "access_level": "read_internal", "audit_trail": true, "data_residency": "global", "retention_override": "", "anonymization_required": false, "anonymization_method": "", "compliance_frameworks": "gdpr", "data_purpose": "", "consent_required": false, "cross_border_transfer": false, "backup_encryption": false, "secure_deletion": false, "natural_language": "Entity: LeaveApplication\nAttribute: leaveTypeName\nClassification: internal\nAccess Level: read_internal\nAudit Trail: true\nData Residency: global\nCompliance Frameworks: gdpr", "version": 1, "status": "draft", "created_at": "2025-06-23T09:01:46.961036", "updated_at": "2025-06-23T09:01:46.961036", "created_by": "Tarun", "updated_by": "Tarun", "security_property_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Attribute with attribute_id A_E13_leaveSubTypeName_1750669306 does not exist"], "parsed_data": {"security_property_id": "SEC1", "entity_id": "E13", "attribute_id": "A_E13_leaveSubTypeName_1750669306", "classification": "internal", "pii_type": "none", "encryption_required": false, "encryption_type": "", "masking_required": false, "masking_pattern": "", "access_level": "read_internal", "audit_trail": true, "data_residency": "global", "retention_override": "", "anonymization_required": false, "anonymization_method": "", "compliance_frameworks": "gdpr", "data_purpose": "", "consent_required": false, "cross_border_transfer": false, "backup_encryption": false, "secure_deletion": false, "natural_language": "Entity: LeaveApplication\nAttribute: leaveSubTypeName\nClassification: internal\nAccess Level: read_internal\nAudit Trail: true\nData Residency: global\nCompliance Frameworks: gdpr", "version": 1, "status": "draft", "created_at": "2025-06-23T09:01:46.985596", "updated_at": "2025-06-23T09:01:46.985596", "created_by": "Tarun", "updated_by": "Tarun", "security_property_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Attribute with attribute_id A_E13_status_1750669306 does not exist"], "parsed_data": {"security_property_id": "SEC1", "entity_id": "E13", "attribute_id": "A_E13_status_1750669306", "classification": "internal", "pii_type": "none", "encryption_required": false, "encryption_type": "", "masking_required": false, "masking_pattern": "", "access_level": "read_internal", "audit_trail": true, "data_residency": "global", "retention_override": "", "anonymization_required": false, "anonymization_method": "", "compliance_frameworks": "gdpr", "data_purpose": "", "consent_required": false, "cross_border_transfer": false, "backup_encryption": false, "secure_deletion": false, "natural_language": "Entity: LeaveApplication\nAttribute: status\nClassification: internal\nAccess Level: read_internal\nAudit Trail: true\nData Residency: global\nCompliance Frameworks: gdpr", "version": 1, "status": "draft", "created_at": "2025-06-23T09:01:47.009538", "updated_at": "2025-06-23T09:01:47.009538", "created_by": "Tarun", "updated_by": "Tarun", "security_property_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Attribute with attribute_id A_E13_requiresDocumentation_1750669307 does not exist"], "parsed_data": {"security_property_id": "SEC1", "entity_id": "E13", "attribute_id": "A_E13_requiresDocumentation_1750669307", "classification": "internal", "pii_type": "none", "encryption_required": false, "encryption_type": "", "masking_required": false, "masking_pattern": "", "access_level": "read_internal", "audit_trail": true, "data_residency": "global", "retention_override": "", "anonymization_required": false, "anonymization_method": "", "compliance_frameworks": "gdpr", "data_purpose": "", "consent_required": false, "cross_border_transfer": false, "backup_encryption": false, "secure_deletion": false, "natural_language": "Entity: LeaveApplication\nAttribute: requiresDocumentation\nClassification: internal\nAccess Level: read_internal\nAudit Trail: true\nData Residency: global\nCompliance Frameworks: gdpr", "version": 1, "status": "draft", "created_at": "2025-06-23T09:01:47.035403", "updated_at": "2025-06-23T09:01:47.035403", "created_by": "Tarun", "updated_by": "Tarun", "security_property_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Attribute with attribute_id A_E13_documentationProvided_1750669307 does not exist"], "parsed_data": {"security_property_id": "SEC1", "entity_id": "E13", "attribute_id": "A_E13_documentationProvided_1750669307", "classification": "confidential", "pii_type": "medical", "encryption_required": true, "encryption_type": "aes256", "masking_required": true, "masking_pattern": "***DOCUMENT***", "access_level": "read_restricted", "audit_trail": true, "data_residency": "global", "retention_override": "2555", "anonymization_required": true, "anonymization_method": "remove", "compliance_frameworks": "gdpr, hipaa", "data_purpose": "", "consent_required": false, "cross_border_transfer": false, "backup_encryption": false, "secure_deletion": false, "natural_language": "Entity: LeaveApplication\nAttribute: documentationProvided\nClassification: confidential\nPII Type: medical\nEncryption Required: true\nEncryption Type: aes256\nMasking Required: true\nMasking Pattern: ***DOCUMENT***\nAccess Level: read_restricted\nAudit Trail: true\nData Residency: global\nRetention Override: 2555\nAnonymization Required: true\nAnonymization Method: remove\nCompliance Frameworks: gdpr, hipaa", "version": 1, "status": "draft", "created_at": "2025-06-23T09:01:47.062736", "updated_at": "2025-06-23T09:01:47.062736", "created_by": "Tarun", "updated_by": "Tarun", "security_property_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Attribute with attribute_id A_E13_submissionDate_1750669307 does not exist"], "parsed_data": {"security_property_id": "SEC1", "entity_id": "E13", "attribute_id": "A_E13_submissionDate_1750669307", "classification": "internal", "pii_type": "none", "encryption_required": false, "encryption_type": "", "masking_required": false, "masking_pattern": "", "access_level": "read_internal", "audit_trail": true, "data_residency": "global", "retention_override": "", "anonymization_required": false, "anonymization_method": "", "compliance_frameworks": "gdpr", "data_purpose": "", "consent_required": false, "cross_border_transfer": false, "backup_encryption": false, "secure_deletion": false, "natural_language": "Entity: LeaveApplication\nAttribute: submissionDate\nClassification: internal\nAccess Level: read_internal\nAudit Trail: true\nData Residency: global\nCompliance Frameworks: gdpr", "version": 1, "status": "draft", "created_at": "2025-06-23T09:01:47.090104", "updated_at": "2025-06-23T09:01:47.090104", "created_by": "Tarun", "updated_by": "Tarun", "security_property_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Attribute with attribute_id A_E13_approvalDate_1750669307 does not exist"], "parsed_data": {"security_property_id": "SEC1", "entity_id": "E13", "attribute_id": "A_E13_approvalDate_1750669307", "classification": "internal", "pii_type": "none", "encryption_required": false, "encryption_type": "", "masking_required": false, "masking_pattern": "", "access_level": "read_internal", "audit_trail": true, "data_residency": "global", "retention_override": "", "anonymization_required": false, "anonymization_method": "", "compliance_frameworks": "gdpr", "data_purpose": "", "consent_required": false, "cross_border_transfer": false, "backup_encryption": false, "secure_deletion": false, "natural_language": "Entity: LeaveApplication\nAttribute: approvalDate\nClassification: internal\nAccess Level: read_internal\nAudit Trail: true\nData Residency: global\nCompliance Frameworks: gdpr", "version": 1, "status": "draft", "created_at": "2025-06-23T09:01:47.114602", "updated_at": "2025-06-23T09:01:47.114602", "created_by": "Tarun", "updated_by": "Tarun", "security_property_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Attribute with attribute_id A_E13_approvedBy_1750669307 does not exist"], "parsed_data": {"security_property_id": "SEC1", "entity_id": "E13", "attribute_id": "A_E13_approvedBy_1750669307", "classification": "confidential", "pii_type": "name", "encryption_required": false, "encryption_type": "", "masking_required": true, "masking_pattern": "EMP***", "access_level": "read_restricted", "audit_trail": true, "data_residency": "global", "retention_override": "", "anonymization_required": true, "anonymization_method": "tokenize", "compliance_frameworks": "gdpr, hipaa", "data_purpose": "", "consent_required": false, "cross_border_transfer": false, "backup_encryption": false, "secure_deletion": false, "natural_language": "Entity: LeaveApplication\nAttribute: approvedBy\nClassification: confidential\nPII Type: name\nMasking Required: true\nMasking Pattern: EMP***\nAccess Level: read_restricted\nAudit Trail: true\nData Residency: global\nAnonymization Required: true\nAnonymization Method: tokenize\nCompliance Frameworks: gdpr, hipaa", "version": 1, "status": "draft", "created_at": "2025-06-23T09:01:47.143702", "updated_at": "2025-06-23T09:01:47.143702", "created_by": "Tarun", "updated_by": "Tarun", "security_property_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Attribute with attribute_id A_E13_comments_1750669307 does not exist"], "parsed_data": {"security_property_id": "SEC1", "entity_id": "E13", "attribute_id": "A_E13_comments_1750669307", "classification": "confidential", "pii_type": "none", "encryption_required": true, "encryption_type": "aes256", "masking_required": true, "masking_pattern": "***COMMENTS***", "access_level": "read_restricted", "audit_trail": true, "data_residency": "global", "retention_override": "2555", "anonymization_required": true, "anonymization_method": "hash", "compliance_frameworks": "gdpr", "data_purpose": "", "consent_required": false, "cross_border_transfer": false, "backup_encryption": false, "secure_deletion": false, "natural_language": "Entity: LeaveApplication\nAttribute: comments\nClassification: confidential\nEncryption Required: true\nEncryption Type: aes256\nMasking Required: true\nMasking Pattern: ***COMMENTS***\nAccess Level: read_restricted\nAudit Trail: true\nData Residency: global\nRetention Override: 2555\nAnonymization Required: true\nAnonymization Method: hash\nCompliance Frameworks: gdpr", "version": 1, "status": "draft", "created_at": "2025-06-23T09:01:47.171540", "updated_at": "2025-06-23T09:01:47.171540", "created_by": "Tarun", "updated_by": "Tarun", "security_property_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Entity with entity_id E_Employee_1750669307 does not exist", "Attribute with attribute_id A_E_Employee_1750669307_employeeId_1750669307 does not exist"], "parsed_data": {"security_property_id": "SEC1", "entity_id": "E_Employee_1750669307", "attribute_id": "A_E_Employee_1750669307_employeeId_1750669307", "classification": "confidential", "pii_type": "name", "encryption_required": false, "encryption_type": "", "masking_required": true, "masking_pattern": "EMP***", "access_level": "read_restricted", "audit_trail": true, "data_residency": "global", "retention_override": "", "anonymization_required": true, "anonymization_method": "tokenize", "compliance_frameworks": "gdpr, hipaa", "data_purpose": "", "consent_required": false, "cross_border_transfer": false, "backup_encryption": false, "secure_deletion": false, "natural_language": "Entity: Employee\nAttribute: employeeId\nClassification: confidential\nPII Type: name\nMasking Required: true\nMasking Pattern: EMP***\nAccess Level: read_restricted\nAudit Trail: true\nData Residency: global\nAnonymization Required: true\nAnonymization Method: tokenize\nCompliance Frameworks: gdpr, hipaa", "version": 1, "status": "draft", "created_at": "2025-06-23T09:01:47.200454", "updated_at": "2025-06-23T09:01:47.200454", "created_by": "Tarun", "updated_by": "Tarun", "security_property_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Entity with entity_id E_Employee_1750669307 does not exist", "Attribute with attribute_id A_E_Employee_1750669307_firstName_1750669307 does not exist"], "parsed_data": {"security_property_id": "SEC1", "entity_id": "E_Employee_1750669307", "attribute_id": "A_E_Employee_1750669307_firstName_1750669307", "classification": "confidential", "pii_type": "name", "encryption_required": true, "encryption_type": "aes256", "masking_required": true, "masking_pattern": "***NAME***", "access_level": "read_restricted", "audit_trail": true, "data_residency": "global", "retention_override": "", "anonymization_required": true, "anonymization_method": "pseudonymize", "compliance_frameworks": "gdpr, hipaa", "data_purpose": "", "consent_required": false, "cross_border_transfer": false, "backup_encryption": false, "secure_deletion": false, "natural_language": "Entity: Employee\nAttribute: firstName\nClassification: confidential\nPII Type: name\nEncryption Required: true\nEncryption Type: aes256\nMasking Required: true\nMasking Pattern: ***NAME***\nAccess Level: read_restricted\nAudit Trail: true\nData Residency: global\nAnonymization Required: true\nAnonymization Method: pseudonymize\nCompliance Frameworks: gdpr, hipaa", "version": 1, "status": "draft", "created_at": "2025-06-23T09:01:47.229107", "updated_at": "2025-06-23T09:01:47.229107", "created_by": "Tarun", "updated_by": "Tarun", "security_property_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Entity with entity_id E_Employee_1750669307 does not exist", "Attribute with attribute_id A_E_Employee_1750669307_lastName_1750669307 does not exist"], "parsed_data": {"security_property_id": "SEC1", "entity_id": "E_Employee_1750669307", "attribute_id": "A_E_Employee_1750669307_lastName_1750669307", "classification": "confidential", "pii_type": "name", "encryption_required": true, "encryption_type": "aes256", "masking_required": true, "masking_pattern": "***NAME***", "access_level": "read_restricted", "audit_trail": true, "data_residency": "global", "retention_override": "", "anonymization_required": true, "anonymization_method": "pseudonymize", "compliance_frameworks": "gdpr, hipaa", "data_purpose": "", "consent_required": false, "cross_border_transfer": false, "backup_encryption": false, "secure_deletion": false, "natural_language": "Entity: Employee\nAttribute: lastName\nClassification: confidential\nPII Type: name\nEncryption Required: true\nEncryption Type: aes256\nMasking Required: true\nMasking Pattern: ***NAME***\nAccess Level: read_restricted\nAudit Trail: true\nData Residency: global\nAnonymization Required: true\nAnonymization Method: pseudonymize\nCompliance Frameworks: gdpr, hipaa", "version": 1, "status": "draft", "created_at": "2025-06-23T09:01:47.252913", "updated_at": "2025-06-23T09:01:47.252913", "created_by": "Tarun", "updated_by": "Tarun", "security_property_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Entity with entity_id E_Employee_1750669307 does not exist", "Attribute with attribute_id A_E_Employee_1750669307_email_1750669307 does not exist"], "parsed_data": {"security_property_id": "SEC1", "entity_id": "E_Employee_1750669307", "attribute_id": "A_E_Employee_1750669307_email_1750669307", "classification": "confidential", "pii_type": "email", "encryption_required": true, "encryption_type": "aes256", "masking_required": true, "masking_pattern": "***@***.com", "access_level": "read_restricted", "audit_trail": true, "data_residency": "global", "retention_override": "", "anonymization_required": true, "anonymization_method": "hash", "compliance_frameworks": "gdpr, hipaa", "data_purpose": "", "consent_required": false, "cross_border_transfer": false, "backup_encryption": false, "secure_deletion": false, "natural_language": "Entity: Employee\nAttribute: email\nClassification: confidential\nPII Type: email\nEncryption Required: true\nEncryption Type: aes256\nMasking Required: true\nMasking Pattern: ***@***.com\nAccess Level: read_restricted\nAudit Trail: true\nData Residency: global\nAnonymization Required: true\nAnonymization Method: hash\nCompliance Frameworks: gdpr, hipaa", "version": 1, "status": "draft", "created_at": "2025-06-23T09:01:47.280431", "updated_at": "2025-06-23T09:01:47.280431", "created_by": "Tarun", "updated_by": "Tarun", "security_property_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Entity with entity_id E_Employee_1750669307 does not exist", "Attribute with attribute_id A_E_Employee_1750669307_phone_1750669307 does not exist"], "parsed_data": {"security_property_id": "SEC1", "entity_id": "E_Employee_1750669307", "attribute_id": "A_E_Employee_1750669307_phone_1750669307", "classification": "confidential", "pii_type": "phone", "encryption_required": true, "encryption_type": "aes256", "masking_required": true, "masking_pattern": "+**-***-***-****", "access_level": "read_restricted", "audit_trail": true, "data_residency": "global", "retention_override": "", "anonymization_required": true, "anonymization_method": "hash", "compliance_frameworks": "gdpr, hipaa", "data_purpose": "", "consent_required": false, "cross_border_transfer": false, "backup_encryption": false, "secure_deletion": false, "natural_language": "Entity: Employee\nAttribute: phone\nClassification: confidential\nPII Type: phone\nEncryption Required: true\nEncryption Type: aes256\nMasking Required: true\nMasking Pattern: +**-***-***-****\nAccess Level: read_restricted\nAudit Trail: true\nData Residency: global\nAnonymization Required: true\nAnonymization Method: hash\nCompliance Frameworks: gdpr, hipaa", "version": 1, "status": "draft", "created_at": "2025-06-23T09:01:47.307856", "updated_at": "2025-06-23T09:01:47.307856", "created_by": "Tarun", "updated_by": "Tarun", "security_property_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Entity with entity_id E_Employee_1750669307 does not exist", "Attribute with attribute_id A_E_Employee_1750669307_departmentId_1750669307 does not exist"], "parsed_data": {"security_property_id": "SEC1", "entity_id": "E_Employee_1750669307", "attribute_id": "A_E_Employee_1750669307_departmentId_1750669307", "classification": "internal", "pii_type": "none", "encryption_required": false, "encryption_type": "", "masking_required": false, "masking_pattern": "", "access_level": "read_internal", "audit_trail": true, "data_residency": "global", "retention_override": "", "anonymization_required": false, "anonymization_method": "", "compliance_frameworks": "gdpr", "data_purpose": "", "consent_required": false, "cross_border_transfer": false, "backup_encryption": false, "secure_deletion": false, "natural_language": "Entity: Employee\nAttribute: departmentId\nClassification: internal\nAccess Level: read_internal\nAudit Trail: true\nData Residency: global\nCompliance Frameworks: gdpr", "version": 1, "status": "draft", "created_at": "2025-06-23T09:01:47.336320", "updated_at": "2025-06-23T09:01:47.336320", "created_by": "Tarun", "updated_by": "Tarun", "security_property_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Entity with entity_id E_Employee_1750669307 does not exist", "Attribute with attribute_id A_E_Employee_1750669307_managerId_1750669307 does not exist"], "parsed_data": {"security_property_id": "SEC1", "entity_id": "E_Employee_1750669307", "attribute_id": "A_E_Employee_1750669307_managerId_1750669307", "classification": "confidential", "pii_type": "name", "encryption_required": false, "encryption_type": "", "masking_required": true, "masking_pattern": "EMP***", "access_level": "read_restricted", "audit_trail": true, "data_residency": "global", "retention_override": "", "anonymization_required": true, "anonymization_method": "tokenize", "compliance_frameworks": "gdpr, hipaa", "data_purpose": "", "consent_required": false, "cross_border_transfer": false, "backup_encryption": false, "secure_deletion": false, "natural_language": "Entity: Employee\nAttribute: managerId\nClassification: confidential\nPII Type: name\nMasking Required: true\nMasking Pattern: EMP***\nAccess Level: read_restricted\nAudit Trail: true\nData Residency: global\nAnonymization Required: true\nAnonymization Method: tokenize\nCompliance Frameworks: gdpr, hipaa", "version": 1, "status": "draft", "created_at": "2025-06-23T09:01:47.365753", "updated_at": "2025-06-23T09:01:47.365753", "created_by": "Tarun", "updated_by": "Tarun", "security_property_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Entity with entity_id E_Employee_1750669307 does not exist", "Attribute with attribute_id A_E_Employee_1750669307_jobTitle_1750669307 does not exist"], "parsed_data": {"security_property_id": "SEC1", "entity_id": "E_Employee_1750669307", "attribute_id": "A_E_Employee_1750669307_jobTitle_1750669307", "classification": "internal", "pii_type": "none", "encryption_required": false, "encryption_type": "", "masking_required": false, "masking_pattern": "", "access_level": "read_internal", "audit_trail": true, "data_residency": "global", "retention_override": "", "anonymization_required": false, "anonymization_method": "", "compliance_frameworks": "gdpr", "data_purpose": "", "consent_required": false, "cross_border_transfer": false, "backup_encryption": false, "secure_deletion": false, "natural_language": "Entity: Employee\nAttribute: jobTitle\nClassification: internal\nAccess Level: read_internal\nAudit Trail: true\nData Residency: global\nCompliance Frameworks: gdpr", "version": 1, "status": "draft", "created_at": "2025-06-23T09:01:47.391325", "updated_at": "2025-06-23T09:01:47.391325", "created_by": "Tarun", "updated_by": "Tarun", "security_property_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Entity with entity_id E_Employee_1750669307 does not exist", "Attribute with attribute_id A_E_Employee_1750669307_hireDate_1750669307 does not exist"], "parsed_data": {"security_property_id": "SEC1", "entity_id": "E_Employee_1750669307", "attribute_id": "A_E_Employee_1750669307_hireDate_1750669307", "classification": "internal", "pii_type": "none", "encryption_required": false, "encryption_type": "", "masking_required": false, "masking_pattern": "", "access_level": "read_internal", "audit_trail": true, "data_residency": "global", "retention_override": "", "anonymization_required": false, "anonymization_method": "", "compliance_frameworks": "gdpr", "data_purpose": "", "consent_required": false, "cross_border_transfer": false, "backup_encryption": false, "secure_deletion": false, "natural_language": "Entity: Employee\nAttribute: hireDate\nClassification: internal\nAccess Level: read_internal\nAudit Trail: true\nData Residency: global\nCompliance Frameworks: gdpr", "version": 1, "status": "draft", "created_at": "2025-06-23T09:01:47.417573", "updated_at": "2025-06-23T09:01:47.417573", "created_by": "Tarun", "updated_by": "Tarun", "security_property_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Entity with entity_id E_Employee_1750669307 does not exist", "Attribute with attribute_id A_E_Employee_1750669307_employmentStatus_1750669307 does not exist"], "parsed_data": {"security_property_id": "SEC1", "entity_id": "E_Employee_1750669307", "attribute_id": "A_E_Employee_1750669307_employmentStatus_1750669307", "classification": "internal", "pii_type": "none", "encryption_required": false, "encryption_type": "", "masking_required": false, "masking_pattern": "", "access_level": "read_internal", "audit_trail": true, "data_residency": "global", "retention_override": "", "anonymization_required": false, "anonymization_method": "", "compliance_frameworks": "gdpr", "data_purpose": "", "consent_required": false, "cross_border_transfer": false, "backup_encryption": false, "secure_deletion": false, "natural_language": "Entity: Employee\nAttribute: employmentStatus\nClassification: internal\nAccess Level: read_internal\nAudit Trail: true\nData Residency: global\nCompliance Frameworks: gdpr", "version": 1, "status": "draft", "created_at": "2025-06-23T09:01:47.443528", "updated_at": "2025-06-23T09:01:47.443528", "created_by": "Tarun", "updated_by": "Tarun", "security_property_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Entity with entity_id E_Employee_1750669307 does not exist", "Attribute with attribute_id A_E_Employee_1750669307_annualLeaveBalance_1750669307 does not exist"], "parsed_data": {"security_property_id": "SEC1", "entity_id": "E_Employee_1750669307", "attribute_id": "A_E_Employee_1750669307_annualLeaveBalance_1750669307", "classification": "confidential", "pii_type": "financial", "encryption_required": true, "encryption_type": "aes256", "masking_required": true, "masking_pattern": "**.* days", "access_level": "read_restricted", "audit_trail": true, "data_residency": "global", "retention_override": "", "anonymization_required": true, "anonymization_method": "hash", "compliance_frameworks": "gdpr", "data_purpose": "", "consent_required": false, "cross_border_transfer": false, "backup_encryption": false, "secure_deletion": false, "natural_language": "Entity: Employee\nAttribute: annualLeaveBalance\nClassification: confidential\nPII Type: financial\nEncryption Required: true\nEncryption Type: aes256\nMasking Required: true\nMasking Pattern: **.* days\nAccess Level: read_restricted\nAudit Trail: true\nData Residency: global\nAnonymization Required: true\nAnonymization Method: hash\nCompliance Frameworks: gdpr", "version": 1, "status": "draft", "created_at": "2025-06-23T09:01:47.465864", "updated_at": "2025-06-23T09:01:47.465864", "created_by": "Tarun", "updated_by": "Tarun", "security_property_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Entity with entity_id E_Employee_1750669307 does not exist", "Attribute with attribute_id A_E_Employee_1750669307_sickLeaveBalance_1750669307 does not exist"], "parsed_data": {"security_property_id": "SEC1", "entity_id": "E_Employee_1750669307", "attribute_id": "A_E_Employee_1750669307_sickLeaveBalance_1750669307", "classification": "confidential", "pii_type": "medical", "encryption_required": true, "encryption_type": "aes256", "masking_required": true, "masking_pattern": "**.* days", "access_level": "read_restricted", "audit_trail": true, "data_residency": "global", "retention_override": "2555", "anonymization_required": true, "anonymization_method": "hash", "compliance_frameworks": "gdpr, hipaa", "data_purpose": "", "consent_required": false, "cross_border_transfer": false, "backup_encryption": false, "secure_deletion": false, "natural_language": "Entity: Employee\nAttribute: sickLeaveBalance\nClassification: confidential\nPII Type: medical\nEncryption Required: true\nEncryption Type: aes256\nMasking Required: true\nMasking Pattern: **.* days\nAccess Level: read_restricted\nAudit Trail: true\nData Residency: global\nRetention Override: 2555\nAnonymization Required: true\nAnonymization Method: hash\nCompliance Frameworks: gdpr, hipaa", "version": 1, "status": "draft", "created_at": "2025-06-23T09:01:47.487242", "updated_at": "2025-06-23T09:01:47.487242", "created_by": "Tarun", "updated_by": "Tarun", "security_property_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Entity with entity_id E_Employee_1750669307 does not exist", "Attribute with attribute_id A_E_Employee_1750669307_personalLeaveBalance_1750669307 does not exist"], "parsed_data": {"security_property_id": "SEC1", "entity_id": "E_Employee_1750669307", "attribute_id": "A_E_Employee_1750669307_personalLeaveBalance_1750669307", "classification": "confidential", "pii_type": "financial", "encryption_required": true, "encryption_type": "aes256", "masking_required": true, "masking_pattern": "**.* days", "access_level": "read_restricted", "audit_trail": true, "data_residency": "global", "retention_override": "", "anonymization_required": true, "anonymization_method": "hash", "compliance_frameworks": "gdpr", "data_purpose": "", "consent_required": false, "cross_border_transfer": false, "backup_encryption": false, "secure_deletion": false, "natural_language": "Entity: Employee\nAttribute: personalLeaveBalance\nClassification: confidential\nPII Type: financial\nEncryption Required: true\nEncryption Type: aes256\nMasking Required: true\nMasking Pattern: **.* days\nAccess Level: read_restricted\nAudit Trail: true\nData Residency: global\nAnonymization Required: true\nAnonymization Method: hash\nCompliance Frameworks: gdpr", "version": 1, "status": "draft", "created_at": "2025-06-23T09:01:47.514582", "updated_at": "2025-06-23T09:01:47.514582", "created_by": "Tarun", "updated_by": "Tarun", "security_property_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Entity with entity_id E_Employee_1750669307 does not exist", "Attribute with attribute_id A_E_Employee_1750669307_totalLeaveEntitlement_1750669307 does not exist"], "parsed_data": {"security_property_id": "SEC1", "entity_id": "E_Employee_1750669307", "attribute_id": "A_E_Employee_1750669307_totalLeaveEntitlement_1750669307", "classification": "internal", "pii_type": "none", "encryption_required": false, "encryption_type": "", "masking_required": false, "masking_pattern": "", "access_level": "read_internal", "audit_trail": true, "data_residency": "global", "retention_override": "", "anonymization_required": false, "anonymization_method": "", "compliance_frameworks": "gdpr", "data_purpose": "", "consent_required": false, "cross_border_transfer": false, "backup_encryption": false, "secure_deletion": false, "natural_language": "Entity: Employee\nAttribute: totalLeaveEntitlement\nClassification: internal\nAccess Level: read_internal\nAudit Trail: true\nData Residency: global\nCompliance Frameworks: gdpr", "version": 1, "status": "draft", "created_at": "2025-06-23T09:01:47.538538", "updated_at": "2025-06-23T09:01:47.538538", "created_by": "Tarun", "updated_by": "Tarun", "security_property_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Entity with entity_id E_LeaveType_1750669307 does not exist", "Attribute with attribute_id A_E_LeaveType_1750669307_leaveTypeId_1750669307 does not exist"], "parsed_data": {"security_property_id": "SEC1", "entity_id": "E_LeaveType_1750669307", "attribute_id": "A_E_LeaveType_1750669307_leaveTypeId_1750669307", "classification": "public", "pii_type": "none", "encryption_required": false, "encryption_type": "", "masking_required": false, "masking_pattern": "", "access_level": "read_public", "audit_trail": false, "data_residency": "global", "retention_override": "", "anonymization_required": false, "anonymization_method": "", "compliance_frameworks": "", "data_purpose": "", "consent_required": false, "cross_border_transfer": false, "backup_encryption": false, "secure_deletion": false, "natural_language": "Entity: LeaveType\nAttribute: leaveTypeId\nClassification: public\nAccess Level: read_public\nData Residency: global", "version": 1, "status": "draft", "created_at": "2025-06-23T09:01:47.560449", "updated_at": "2025-06-23T09:01:47.560449", "created_by": "Tarun", "updated_by": "Tarun", "security_property_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Entity with entity_id E_LeaveType_1750669307 does not exist", "Attribute with attribute_id A_E_LeaveType_1750669307_leaveTypeName_1750669307 does not exist"], "parsed_data": {"security_property_id": "SEC1", "entity_id": "E_LeaveType_1750669307", "attribute_id": "A_E_LeaveType_1750669307_leaveTypeName_1750669307", "classification": "public", "pii_type": "none", "encryption_required": false, "encryption_type": "", "masking_required": false, "masking_pattern": "", "access_level": "read_public", "audit_trail": false, "data_residency": "global", "retention_override": "", "anonymization_required": false, "anonymization_method": "", "compliance_frameworks": "", "data_purpose": "", "consent_required": false, "cross_border_transfer": false, "backup_encryption": false, "secure_deletion": false, "natural_language": "Entity: LeaveType\nAttribute: leaveTypeName\nClassification: public\nAccess Level: read_public\nData Residency: global", "version": 1, "status": "draft", "created_at": "2025-06-23T09:01:47.582816", "updated_at": "2025-06-23T09:01:47.582816", "created_by": "Tarun", "updated_by": "Tarun", "security_property_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Entity with entity_id E_LeaveType_1750669307 does not exist", "Attribute with attribute_id A_E_LeaveType_1750669307_description_1750669307 does not exist"], "parsed_data": {"security_property_id": "SEC1", "entity_id": "E_LeaveType_1750669307", "attribute_id": "A_E_LeaveType_1750669307_description_1750669307", "classification": "public", "pii_type": "none", "encryption_required": false, "encryption_type": "", "masking_required": false, "masking_pattern": "", "access_level": "read_public", "audit_trail": false, "data_residency": "global", "retention_override": "", "anonymization_required": false, "anonymization_method": "", "compliance_frameworks": "", "data_purpose": "", "consent_required": false, "cross_border_transfer": false, "backup_encryption": false, "secure_deletion": false, "natural_language": "Entity: LeaveType\nAttribute: description\nClassification: public\nAccess Level: read_public\nData Residency: global", "version": 1, "status": "draft", "created_at": "2025-06-23T09:01:47.610015", "updated_at": "2025-06-23T09:01:47.610015", "created_by": "Tarun", "updated_by": "Tarun", "security_property_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Entity with entity_id E_LeaveType_1750669307 does not exist", "Attribute with attribute_id A_E_LeaveType_1750669307_maxDaysPerYear_1750669307 does not exist"], "parsed_data": {"security_property_id": "SEC1", "entity_id": "E_LeaveType_1750669307", "attribute_id": "A_E_LeaveType_1750669307_maxDaysPerYear_1750669307", "classification": "public", "pii_type": "none", "encryption_required": false, "encryption_type": "", "masking_required": false, "masking_pattern": "", "access_level": "read_public", "audit_trail": false, "data_residency": "global", "retention_override": "", "anonymization_required": false, "anonymization_method": "", "compliance_frameworks": "", "data_purpose": "", "consent_required": false, "cross_border_transfer": false, "backup_encryption": false, "secure_deletion": false, "natural_language": "Entity: LeaveType\nAttribute: maxDaysPerYear\nClassification: public\nAccess Level: read_public\nData Residency: global", "version": 1, "status": "draft", "created_at": "2025-06-23T09:01:47.633804", "updated_at": "2025-06-23T09:01:47.633804", "created_by": "Tarun", "updated_by": "Tarun", "security_property_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Entity with entity_id E_LeaveType_1750669307 does not exist", "Attribute with attribute_id A_E_LeaveType_1750669307_requiresDocumentation_1750669307 does not exist"], "parsed_data": {"security_property_id": "SEC1", "entity_id": "E_LeaveType_1750669307", "attribute_id": "A_E_LeaveType_1750669307_requiresDocumentation_1750669307", "classification": "public", "pii_type": "none", "encryption_required": false, "encryption_type": "", "masking_required": false, "masking_pattern": "", "access_level": "read_public", "audit_trail": false, "data_residency": "global", "retention_override": "", "anonymization_required": false, "anonymization_method": "", "compliance_frameworks": "", "data_purpose": "", "consent_required": false, "cross_border_transfer": false, "backup_encryption": false, "secure_deletion": false, "natural_language": "Entity: LeaveType\nAttribute: requiresDocumentation\nClassification: public\nAccess Level: read_public\nData Residency: global", "version": 1, "status": "draft", "created_at": "2025-06-23T09:01:47.662246", "updated_at": "2025-06-23T09:01:47.662246", "created_by": "Tarun", "updated_by": "Tarun", "security_property_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Entity with entity_id E_LeaveType_1750669307 does not exist", "Attribute with attribute_id A_E_LeaveType_1750669307_advanceNoticeDays_1750669307 does not exist"], "parsed_data": {"security_property_id": "SEC1", "entity_id": "E_LeaveType_1750669307", "attribute_id": "A_E_LeaveType_1750669307_advanceNoticeDays_1750669307", "classification": "public", "pii_type": "none", "encryption_required": false, "encryption_type": "", "masking_required": false, "masking_pattern": "", "access_level": "read_public", "audit_trail": false, "data_residency": "global", "retention_override": "", "anonymization_required": false, "anonymization_method": "", "compliance_frameworks": "", "data_purpose": "", "consent_required": false, "cross_border_transfer": false, "backup_encryption": false, "secure_deletion": false, "natural_language": "Entity: LeaveType\nAttribute: advanceNoticeDays\nClassification: public\nAccess Level: read_public\nData Residency: global", "version": 1, "status": "draft", "created_at": "2025-06-23T09:01:47.683888", "updated_at": "2025-06-23T09:01:47.683888", "created_by": "Tarun", "updated_by": "Tarun", "security_property_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Entity with entity_id E_LeaveType_1750669307 does not exist", "Attribute with attribute_id A_E_LeaveType_1750669307_isCarryForward_1750669307 does not exist"], "parsed_data": {"security_property_id": "SEC1", "entity_id": "E_LeaveType_1750669307", "attribute_id": "A_E_LeaveType_1750669307_isCarryForward_1750669307", "classification": "public", "pii_type": "none", "encryption_required": false, "encryption_type": "", "masking_required": false, "masking_pattern": "", "access_level": "read_public", "audit_trail": false, "data_residency": "global", "retention_override": "", "anonymization_required": false, "anonymization_method": "", "compliance_frameworks": "", "data_purpose": "", "consent_required": false, "cross_border_transfer": false, "backup_encryption": false, "secure_deletion": false, "natural_language": "Entity: LeaveType\nAttribute: isCarryForward\nClassification: public\nAccess Level: read_public\nData Residency: global", "version": 1, "status": "draft", "created_at": "2025-06-23T09:01:47.708188", "updated_at": "2025-06-23T09:01:47.708188", "created_by": "Tarun", "updated_by": "Tarun", "security_property_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Entity with entity_id E_LeaveType_1750669307 does not exist", "Attribute with attribute_id A_E_LeaveType_1750669307_status_1750669307 does not exist"], "parsed_data": {"security_property_id": "SEC1", "entity_id": "E_LeaveType_1750669307", "attribute_id": "A_E_LeaveType_1750669307_status_1750669307", "classification": "public", "pii_type": "none", "encryption_required": false, "encryption_type": "", "masking_required": false, "masking_pattern": "", "access_level": "read_public", "audit_trail": false, "data_residency": "global", "retention_override": "", "anonymization_required": false, "anonymization_method": "", "compliance_frameworks": "", "data_purpose": "", "consent_required": false, "cross_border_transfer": false, "backup_encryption": false, "secure_deletion": false, "natural_language": "Entity: LeaveType\nAttribute: status\nClassification: public\nAccess Level: read_public\nData Residency: global", "version": 1, "status": "draft", "created_at": "2025-06-23T09:01:47.732506", "updated_at": "2025-06-23T09:01:47.732506", "created_by": "Tarun", "updated_by": "Tarun", "security_property_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Entity with entity_id E_LeaveSubType_1750669307 does not exist", "Attribute with attribute_id A_E_LeaveSubType_1750669307_leaveSubTypeId_1750669307 does not exist"], "parsed_data": {"security_property_id": "SEC1", "entity_id": "E_LeaveSubType_1750669307", "attribute_id": "A_E_LeaveSubType_1750669307_leaveSubTypeId_1750669307", "classification": "public", "pii_type": "none", "encryption_required": false, "encryption_type": "", "masking_required": false, "masking_pattern": "", "access_level": "read_public", "audit_trail": false, "data_residency": "global", "retention_override": "", "anonymization_required": false, "anonymization_method": "", "compliance_frameworks": "", "data_purpose": "", "consent_required": false, "cross_border_transfer": false, "backup_encryption": false, "secure_deletion": false, "natural_language": "Entity: LeaveSubType\nAttribute: leaveSubTypeId\nClassification: public\nAccess Level: read_public\nData Residency: global", "version": 1, "status": "draft", "created_at": "2025-06-23T09:01:47.755484", "updated_at": "2025-06-23T09:01:47.755484", "created_by": "Tarun", "updated_by": "Tarun", "security_property_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Entity with entity_id E_LeaveSubType_1750669307 does not exist", "Attribute with attribute_id A_E_LeaveSubType_1750669307_leaveTypeName_1750669307 does not exist"], "parsed_data": {"security_property_id": "SEC1", "entity_id": "E_LeaveSubType_1750669307", "attribute_id": "A_E_LeaveSubType_1750669307_leaveTypeName_1750669307", "classification": "public", "pii_type": "none", "encryption_required": false, "encryption_type": "", "masking_required": false, "masking_pattern": "", "access_level": "read_public", "audit_trail": false, "data_residency": "global", "retention_override": "", "anonymization_required": false, "anonymization_method": "", "compliance_frameworks": "", "data_purpose": "", "consent_required": false, "cross_border_transfer": false, "backup_encryption": false, "secure_deletion": false, "natural_language": "Entity: LeaveSubType\nAttribute: leaveTypeName\nClassification: public\nAccess Level: read_public\nData Residency: global", "version": 1, "status": "draft", "created_at": "2025-06-23T09:01:47.777973", "updated_at": "2025-06-23T09:01:47.777973", "created_by": "Tarun", "updated_by": "Tarun", "security_property_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Entity with entity_id E_LeaveSubType_1750669307 does not exist", "Attribute with attribute_id A_E_LeaveSubType_1750669307_leaveSubTypeName_1750669307 does not exist"], "parsed_data": {"security_property_id": "SEC1", "entity_id": "E_LeaveSubType_1750669307", "attribute_id": "A_E_LeaveSubType_1750669307_leaveSubTypeName_1750669307", "classification": "public", "pii_type": "none", "encryption_required": false, "encryption_type": "", "masking_required": false, "masking_pattern": "", "access_level": "read_public", "audit_trail": false, "data_residency": "global", "retention_override": "", "anonymization_required": false, "anonymization_method": "", "compliance_frameworks": "", "data_purpose": "", "consent_required": false, "cross_border_transfer": false, "backup_encryption": false, "secure_deletion": false, "natural_language": "Entity: LeaveSubType\nAttribute: leaveSubTypeName\nClassification: public\nAccess Level: read_public\nData Residency: global", "version": 1, "status": "draft", "created_at": "2025-06-23T09:01:47.802495", "updated_at": "2025-06-23T09:01:47.802495", "created_by": "Tarun", "updated_by": "Tarun", "security_property_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Entity with entity_id E_LeaveSubType_1750669307 does not exist", "Attribute with attribute_id A_E_LeaveSubType_1750669307_description_1750669307 does not exist"], "parsed_data": {"security_property_id": "SEC1", "entity_id": "E_LeaveSubType_1750669307", "attribute_id": "A_E_LeaveSubType_1750669307_description_1750669307", "classification": "public", "pii_type": "none", "encryption_required": false, "encryption_type": "", "masking_required": false, "masking_pattern": "", "access_level": "read_public", "audit_trail": false, "data_residency": "global", "retention_override": "", "anonymization_required": false, "anonymization_method": "", "compliance_frameworks": "", "data_purpose": "", "consent_required": false, "cross_border_transfer": false, "backup_encryption": false, "secure_deletion": false, "natural_language": "Entity: LeaveSubType\nAttribute: description\nClassification: public\nAccess Level: read_public\nData Residency: global", "version": 1, "status": "draft", "created_at": "2025-06-23T09:01:47.830686", "updated_at": "2025-06-23T09:01:47.830686", "created_by": "Tarun", "updated_by": "Tarun", "security_property_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Entity with entity_id E_LeaveSubType_1750669307 does not exist", "Attribute with attribute_id A_E_LeaveSubType_1750669307_maxDaysPerRequest_1750669307 does not exist"], "parsed_data": {"security_property_id": "SEC1", "entity_id": "E_LeaveSubType_1750669307", "attribute_id": "A_E_LeaveSubType_1750669307_maxDaysPerRequest_1750669307", "classification": "public", "pii_type": "none", "encryption_required": false, "encryption_type": "", "masking_required": false, "masking_pattern": "", "access_level": "read_public", "audit_trail": false, "data_residency": "global", "retention_override": "", "anonymization_required": false, "anonymization_method": "", "compliance_frameworks": "", "data_purpose": "", "consent_required": false, "cross_border_transfer": false, "backup_encryption": false, "secure_deletion": false, "natural_language": "Entity: LeaveSubType\nAttribute: maxDaysPerRequest\nClassification: public\nAccess Level: read_public\nData Residency: global", "version": 1, "status": "draft", "created_at": "2025-06-23T09:01:47.853268", "updated_at": "2025-06-23T09:01:47.853268", "created_by": "Tarun", "updated_by": "Tarun", "security_property_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Entity with entity_id E_LeaveSubType_1750669307 does not exist", "Attribute with attribute_id A_E_LeaveSubType_1750669307_requiresManagerApproval_1750669307 does not exist"], "parsed_data": {"security_property_id": "SEC1", "entity_id": "E_LeaveSubType_1750669307", "attribute_id": "A_E_LeaveSubType_1750669307_requiresManagerApproval_1750669307", "classification": "public", "pii_type": "none", "encryption_required": false, "encryption_type": "", "masking_required": false, "masking_pattern": "", "access_level": "read_public", "audit_trail": false, "data_residency": "global", "retention_override": "", "anonymization_required": false, "anonymization_method": "", "compliance_frameworks": "", "data_purpose": "", "consent_required": false, "cross_border_transfer": false, "backup_encryption": false, "secure_deletion": false, "natural_language": "Entity: LeaveSubType\nAttribute: requiresManagerApproval\nClassification: public\nAccess Level: read_public\nData Residency: global", "version": 1, "status": "draft", "created_at": "2025-06-23T09:01:47.874222", "updated_at": "2025-06-23T09:01:47.874222", "created_by": "Tarun", "updated_by": "Tarun", "security_property_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Entity with entity_id E_LeaveSubType_1750669307 does not exist", "Attribute with attribute_id A_E_LeaveSubType_1750669307_status_1750669307 does not exist"], "parsed_data": {"security_property_id": "SEC1", "entity_id": "E_LeaveSubType_1750669307", "attribute_id": "A_E_LeaveSubType_1750669307_status_1750669307", "classification": "public", "pii_type": "none", "encryption_required": false, "encryption_type": "", "masking_required": false, "masking_pattern": "", "access_level": "read_public", "audit_trail": false, "data_residency": "global", "retention_override": "", "anonymization_required": false, "anonymization_method": "", "compliance_frameworks": "", "data_purpose": "", "consent_required": false, "cross_border_transfer": false, "backup_encryption": false, "secure_deletion": false, "natural_language": "Entity: LeaveSubType\nAttribute: status\nClassification: public\nAccess Level: read_public\nData Residency: global", "version": 1, "status": "draft", "created_at": "2025-06-23T09:01:47.899719", "updated_at": "2025-06-23T09:01:47.899719", "created_by": "Tarun", "updated_by": "Tarun", "security_property_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Entity with entity_id E_Constants_1750669307 does not exist", "Attribute with attribute_id A_E_Constants_1750669307_constantId_1750669307 does not exist"], "parsed_data": {"security_property_id": "SEC1", "entity_id": "E_Constants_1750669307", "attribute_id": "A_E_Constants_1750669307_constantId_1750669307", "classification": "internal", "pii_type": "none", "encryption_required": false, "encryption_type": "", "masking_required": false, "masking_pattern": "", "access_level": "read_internal", "audit_trail": true, "data_residency": "global", "retention_override": "", "anonymization_required": false, "anonymization_method": "", "compliance_frameworks": "", "data_purpose": "", "consent_required": false, "cross_border_transfer": false, "backup_encryption": false, "secure_deletion": false, "natural_language": "Entity: Constants\nAttribute: constantId\nClassification: internal\nAccess Level: read_internal\nAudit Trail: true\nData Residency: global", "version": 1, "status": "draft", "created_at": "2025-06-23T09:01:47.926888", "updated_at": "2025-06-23T09:01:47.926888", "created_by": "Tarun", "updated_by": "Tarun", "security_property_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Entity with entity_id E_Constants_1750669307 does not exist", "Attribute with attribute_id A_E_Constants_1750669307_attribute_1750669307 does not exist"], "parsed_data": {"security_property_id": "SEC1", "entity_id": "E_Constants_1750669307", "attribute_id": "A_E_Constants_1750669307_attribute_1750669307", "classification": "internal", "pii_type": "none", "encryption_required": false, "encryption_type": "", "masking_required": false, "masking_pattern": "", "access_level": "read_internal", "audit_trail": true, "data_residency": "global", "retention_override": "", "anonymization_required": false, "anonymization_method": "", "compliance_frameworks": "", "data_purpose": "", "consent_required": false, "cross_border_transfer": false, "backup_encryption": false, "secure_deletion": false, "natural_language": "Entity: Constants\nAttribute: attribute\nClassification: internal\nAccess Level: read_internal\nAudit Trail: true\nData Residency: global", "version": 1, "status": "draft", "created_at": "2025-06-23T09:01:47.957858", "updated_at": "2025-06-23T09:01:47.957858", "created_by": "Tarun", "updated_by": "Tarun", "security_property_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Entity with entity_id E_Constants_1750669307 does not exist", "Attribute with attribute_id A_E_Constants_1750669307_value_1750669307 does not exist"], "parsed_data": {"security_property_id": "SEC1", "entity_id": "E_Constants_1750669307", "attribute_id": "A_E_Constants_1750669307_value_1750669307", "classification": "internal", "pii_type": "none", "encryption_required": false, "encryption_type": "", "masking_required": false, "masking_pattern": "", "access_level": "read_internal", "audit_trail": true, "data_residency": "global", "retention_override": "", "anonymization_required": false, "anonymization_method": "", "compliance_frameworks": "", "data_purpose": "", "consent_required": false, "cross_border_transfer": false, "backup_encryption": false, "secure_deletion": false, "natural_language": "Entity: Constants\nAttribute: value\nClassification: internal\nAccess Level: read_internal\nAudit Trail: true\nData Residency: global", "version": 1, "status": "draft", "created_at": "2025-06-23T09:01:47.989375", "updated_at": "2025-06-23T09:01:47.989375", "created_by": "Tarun", "updated_by": "Tarun", "security_property_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Entity with entity_id E_Constants_1750669307 does not exist", "Attribute with attribute_id A_E_Constants_1750669307_description_1750669308 does not exist"], "parsed_data": {"security_property_id": "SEC1", "entity_id": "E_Constants_1750669307", "attribute_id": "A_E_Constants_1750669307_description_1750669308", "classification": "internal", "pii_type": "none", "encryption_required": false, "encryption_type": "", "masking_required": false, "masking_pattern": "", "access_level": "read_internal", "audit_trail": true, "data_residency": "global", "retention_override": "", "anonymization_required": false, "anonymization_method": "", "compliance_frameworks": "", "data_purpose": "", "consent_required": false, "cross_border_transfer": false, "backup_encryption": false, "secure_deletion": false, "natural_language": "Entity: Constants\nAttribute: description\nClassification: internal\nAccess Level: read_internal\nAudit Trail: true\nData Residency: global", "version": 1, "status": "draft", "created_at": "2025-06-23T09:01:48.017643", "updated_at": "2025-06-23T09:01:48.017643", "created_by": "Tarun", "updated_by": "Tarun", "security_property_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Entity with entity_id E_Constants_1750669308 does not exist", "Attribute with attribute_id A_E_Constants_1750669308_dataType_1750669308 does not exist"], "parsed_data": {"security_property_id": "SEC1", "entity_id": "E_Constants_1750669308", "attribute_id": "A_E_Constants_1750669308_dataType_1750669308", "classification": "internal", "pii_type": "none", "encryption_required": false, "encryption_type": "", "masking_required": false, "masking_pattern": "", "access_level": "read_internal", "audit_trail": true, "data_residency": "global", "retention_override": "", "anonymization_required": false, "anonymization_method": "", "compliance_frameworks": "", "data_purpose": "", "consent_required": false, "cross_border_transfer": false, "backup_encryption": false, "secure_deletion": false, "natural_language": "Entity: Constants\nAttribute: dataType\nClassification: internal\nAccess Level: read_internal\nAudit Trail: true\nData Residency: global", "version": 1, "status": "draft", "created_at": "2025-06-23T09:01:48.046829", "updated_at": "2025-06-23T09:01:48.046829", "created_by": "Tarun", "updated_by": "Tarun", "security_property_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Entity with entity_id E_Constants_1750669308 does not exist", "Attribute with attribute_id A_E_Constants_1750669308_status_1750669308 does not exist"], "parsed_data": {"security_property_id": "SEC1", "entity_id": "E_Constants_1750669308", "attribute_id": "A_E_Constants_1750669308_status_1750669308", "classification": "internal", "pii_type": "none", "encryption_required": false, "encryption_type": "", "masking_required": false, "masking_pattern": "", "access_level": "read_internal", "audit_trail": true, "data_residency": "global", "retention_override": "", "anonymization_required": false, "anonymization_method": "", "compliance_frameworks": "", "data_purpose": "", "consent_required": false, "cross_border_transfer": false, "backup_encryption": false, "secure_deletion": false, "natural_language": "Entity: Constants\nAttribute: status\nClassification: internal\nAccess Level: read_internal\nAudit Trail: true\nData Residency: global", "version": 1, "status": "draft", "created_at": "2025-06-23T09:01:48.078227", "updated_at": "2025-06-23T09:01:48.078227", "created_by": "Tarun", "updated_by": "Tarun", "security_property_status": "new", "changes_detected": []}}], "operation": "parse_validate_mongosave", "total_security_properties": 50}, "status": "success"}
{"timestamp": "2025-06-23T14:07:59.502274", "endpoint": "parse-validate-mongosave/security-properties", "input": {"natural_language": "Product entity requires manager role for create and update operations", "tenant_id": "tenant_123", "edit_draft": false, "edit_temp": false, "edit_production": false}, "output": {"success": true, "security_property_results": [], "operation": "parse_validate_mongosave", "total_security_properties": 0}, "status": "success"}
