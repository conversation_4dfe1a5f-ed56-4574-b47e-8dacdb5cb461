{"timestamp": "2025-06-23T14:03:49.706597", "operation": "process_mongo_entity_relationships_to_workflow_temp", "input_data": {"schema": "workflow_temp"}, "result": {"total_processed": 1, "successful_inserts": 0, "failed_inserts": 0, "details": [{"relationship_id": 1, "source_entity_id": "E7", "target_entity_id": "E15", "status": "skipped", "reason": "already_exists"}]}, "status": "success"}
{"timestamp": "2025-06-23T14:09:00.821019", "operation": "process_mongo_entity_relationships_to_workflow_temp", "input_data": {"schema": "workflow_temp"}, "result": {"total_processed": 1, "successful_inserts": 0, "failed_inserts": 0, "details": [{"relationship_id": 1, "source_entity_id": "E7", "target_entity_id": "E15", "status": "skipped", "reason": "already_exists"}]}, "status": "success"}
{"timestamp": "2025-06-23T14:32:33.327900", "operation": "process_mongo_entity_relationships_to_workflow_temp", "input_data": {"schema": "workflow_temp"}, "result": {"total_processed": 1, "successful_inserts": 0, "failed_inserts": 0, "details": [{"relationship_id": 1, "source_entity_id": "E7", "target_entity_id": "E15", "status": "skipped", "reason": "already_exists"}]}, "status": "success"}
{"timestamp": "2025-06-23T14:38:52.705571", "operation": "process_mongo_entity_relationships_to_workflow_temp", "input_data": {"schema": "workflow_temp"}, "result": {"total_processed": 1, "successful_inserts": 0, "failed_inserts": 0, "details": [{"relationship_id": 1, "source_entity_id": "E7", "target_entity_id": "E15", "status": "skipped", "reason": "already_exists"}]}, "status": "success"}
