#!/usr/bin/env python3
"""
Script to update entity IDs in the entities table from E000001 format to E1 format.
"""

import logging
from deployers.entity_deployer_v2 import update_entity_ids

# Set up logging
logging.basicConfig(level=logging.INFO, 
                   format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger('run_update_entity_ids')

def main():
    """
    Main function to update entity IDs.
    """
    schema_name = 'workflow_temp'
    
    logger.info(f"Starting update of entity IDs in schema {schema_name}")
    
    success, messages = update_entity_ids(schema_name)
    
    for message in messages:
        logger.info(message)
    
    if success:
        logger.info(f"Successfully updated entity IDs in schema {schema_name}")
    else:
        logger.error(f"Failed to update entity IDs in schema {schema_name}")

if __name__ == "__main__":
    main()
