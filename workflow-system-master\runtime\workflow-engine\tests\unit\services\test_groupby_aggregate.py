import unittest
from typing import List, Dict, Any, Union
import sys
import os

# Add project root to path if needed
project_root = '/home/<USER>/workflow-system/runtime/workflow-engine'
if project_root not in sys.path:
    sys.path.insert(0, project_root)

# Import the function to test
from app.services.system_functions import groupby_aggregate

class TestGroupByAggregate(unittest.TestCase):
    def test_empty_data(self):
        """Test with empty data list."""
        data = []
        
        result = groupby_aggregate(data, "category", "amount", "sum")
        
        self.assertEqual(result, {})
    
    def test_sum_aggregation_single_group(self):
        """Test sum aggregation with a single group by field."""
        data = [
            {"category": "A", "amount": 10},
            {"category": "B", "amount": 20},
            {"category": "A", "amount": 30},
            {"category": "C", "amount": 40},
            {"category": "B", "amount": 50}
        ]
        
        result = groupby_aggregate(data, "category", "amount", "sum")
        
        expected = {
            "A": 40,  # 10 + 30
            "B": 70,  # 20 + 50
            "C": 40
        }
        
        self.assertEqual(result, expected)
    
    def test_sum_aggregation_multiple_groups(self):
        """Test sum aggregation with multiple group by fields."""
        data = [
            {"category": "A", "year": 2023, "amount": 10},
            {"category": "B", "year": 2023, "amount": 20},
            {"category": "A", "year": 2024, "amount": 30},
            {"category": "B", "year": 2024, "amount": 40},
            {"category": "A", "year": 2023, "amount": 50}
        ]
        
        result = groupby_aggregate(data, ["category", "year"], "amount", "sum")
        
        expected = {
            "A|2023": {"category": "A", "year": "2023", "sum_amount": 60},  # 10 + 50
            "B|2023": {"category": "B", "year": "2023", "sum_amount": 20},
            "A|2024": {"category": "A", "year": "2024", "sum_amount": 30},
            "B|2024": {"category": "B", "year": "2024", "sum_amount": 40}
        }
        
        self.assertEqual(result, expected)
    
    def test_avg_aggregation(self):
        """Test average aggregation."""
        data = [
            {"category": "A", "score": 10},
            {"category": "B", "score": 20},
            {"category": "A", "score": 30},
            {"category": "B", "score": 40}
        ]
        
        result = groupby_aggregate(data, "category", "score", "avg")
        
        expected = {
            "A": 20.0,  # (10 + 30) / 2
            "B": 30.0   # (20 + 40) / 2
        }
        
        self.assertEqual(result, expected)
    
    def test_min_aggregation(self):
        """Test minimum aggregation."""
        data = [
            {"category": "A", "value": 10},
            {"category": "B", "value": 20},
            {"category": "A", "value": 5},
            {"category": "B", "value": 25}
        ]
        
        result = groupby_aggregate(data, "category", "value", "min")
        
        expected = {
            "A": 5,   # min of 10 and 5
            "B": 20   # min of 20 and 25
        }
        
        self.assertEqual(result, expected)
    
    def test_max_aggregation(self):
        """Test maximum aggregation."""
        data = [
            {"category": "A", "value": 10},
            {"category": "B", "value": 20},
            {"category": "A", "value": 15},
            {"category": "B", "value": 5}
        ]
        
        result = groupby_aggregate(data, "category", "value", "max")
        
        expected = {
            "A": 15,  # max of 10 and 15
            "B": 20   # max of 20 and 5
        }
        
        self.assertEqual(result, expected)
    
    def test_count_aggregation(self):
        """Test count aggregation."""
        data = [
            {"category": "A", "value": 10},
            {"category": "B", "value": 20},
            {"category": "A", "value": 30},
            {"category": "A", "value": 40},
            {"category": "B", "value": 50}
        ]
        
        result = groupby_aggregate(data, "category", "value", "count")
        
        expected = {
            "A": 3,  # 3 items with category A
            "B": 2   # 2 items with category B
        }
        
        self.assertEqual(result, expected)
    
    def test_string_value_conversion(self):
        """Test with string values that can be converted to numbers."""
        data = [
            {"category": "A", "amount": "10"},
            {"category": "B", "amount": "20"},
            {"category": "A", "amount": "30"}
        ]
        
        result = groupby_aggregate(data, "category", "amount", "sum")
        
        expected = {
            "A": 40.0,  # 10 + 30, converted to float
            "B": 20.0   # 20, converted to float
        }
        
        self.assertEqual(result, expected)
    
    def test_invalid_string_values(self):
        """Test with string values that cannot be converted to numbers."""
        data = [
            {"category": "A", "amount": "10"},
            {"category": "B", "amount": "invalid"},
            {"category": "A", "amount": "30"}
        ]
        
        result = groupby_aggregate(data, "category", "amount", "sum")
        
        expected = {
            "A": 40.0,  # 10 + 30, invalid values are skipped
            "B": 0      # No valid values for B
        }
        
        # B won't be in the result since its value couldn't be converted
        self.assertEqual(result.get("A"), expected.get("A"))
        self.assertNotIn("B", result)
    
    def test_missing_group_by_field(self):
        """Test with items missing the group_by field."""
        data = [
            {"category": "A", "amount": 10},
            {"amount": 20},  # Missing category
            {"category": "C", "amount": 30}
        ]
        
        result = groupby_aggregate(data, "category", "amount", "sum")
        
        expected = {
            "A": 10,
            "C": 30
        }
        
        self.assertEqual(result, expected)
    
    def test_missing_aggregate_field(self):
        """Test with items missing the aggregate_field."""
        data = [
            {"category": "A", "amount": 10},
            {"category": "B"},  # Missing amount
            {"category": "A", "amount": 30}
        ]
        
        result = groupby_aggregate(data, "category", "amount", "sum")
        
        expected = {
            "A": 40,  # 10 + 30
            # B is not in the result since it has no amount to aggregate
        }
        
        self.assertEqual(result, expected)
    
    def test_missing_fields_in_compound_key(self):
        """Test with items missing fields in a compound key."""
        data = [
            {"category": "A", "year": 2023, "amount": 10},
            {"category": "B", "amount": 20},  # Missing year
            {"year": 2023, "amount": 30}      # Missing category
        ]
        
        result = groupby_aggregate(data, ["category", "year"], "amount", "sum")
        
        expected = {
            "A|2023": {"category": "A", "year": "2023", "sum_amount": 10}
            # Other items are skipped since they're missing fields needed for the compound key
        }
        
        self.assertEqual(result, expected)
    
    def test_default_aggregation(self):
        """Test with default aggregation (sum)."""
        data = [
            {"category": "A", "amount": 10},
            {"category": "A", "amount": 20}
        ]
        
        # Don't specify aggregation, default should be "sum"
        result = groupby_aggregate(data, "category", "amount")
        
        expected = {
            "A": 30  # 10 + 20
        }
        
        self.assertEqual(result, expected)

if __name__ == '__main__':
    unittest.main()