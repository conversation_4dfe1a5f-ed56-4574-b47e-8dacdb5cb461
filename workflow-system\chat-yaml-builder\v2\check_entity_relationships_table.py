#!/usr/bin/env python3
"""
Check the structure of the entity_relationships table and add missing columns if needed.
"""

import os
import psycopg2
import logging

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger('check_entity_relationships')

def get_db_connection(schema_name=None):
    """
    Get a database connection.
    
    Args:
        schema_name: Optional schema name to set as the search path
        
    Returns:
        Database connection object
    """
    # Get database connection parameters from environment variables or use defaults
    db_host = os.environ.get('DB_HOST', '**********')
    db_port = os.environ.get('DB_PORT', '5432')
    db_name = os.environ.get('DB_NAME', 'workflow_system')
    db_user = os.environ.get('DB_USER', 'postgres')
    db_password = os.environ.get('DB_PASSWORD', 'workflow_postgres_secure_password')
    
    # Connect to the database
    conn = psycopg2.connect(
        host=db_host,
        port=db_port,
        dbname=db_name,
        user=db_user,
        password=db_password
    )
    
    # Set schema search path if provided
    if schema_name:
        with conn.cursor() as cursor:
            cursor.execute(f"SET search_path TO {schema_name}")
            conn.commit()
    
    return conn

def execute_query(query, params=None, schema_name=None):
    """
    Execute a database query.
    
    Args:
        query: SQL query to execute
        params: Query parameters
        schema_name: Schema name to set as search path
        
    Returns:
        Query result rows
    """
    conn = None
    try:
        # Get database connection
        conn = get_db_connection(schema_name)
        
        # Execute query
        with conn.cursor() as cursor:
            cursor.execute(query, params)
            
            # Get result if query returns rows
            if cursor.description:
                result = cursor.fetchall()
                return True, result
            
            # Commit if not a SELECT query
            conn.commit()
            return True, []
    except Exception as e:
        if conn:
            conn.rollback()
        logger.error(f"Query error: {str(e)}")
        return False, str(e)
    finally:
        if conn:
            conn.close()

def check_and_update_entity_relationships_table():
    """
    Check the structure of the entity_relationships table and add missing columns if needed.
    """
    schema_name = 'workflow_temp'
    
    # Check if the entity_relationships table exists
    success, result = execute_query(
        """
        SELECT EXISTS (
            SELECT FROM information_schema.tables 
            WHERE table_schema = %s
            AND table_name = 'entity_relationships'
        )
        """,
        (schema_name,),
        schema_name
    )
    
    if not success or not result or not result[0][0]:
        logger.error("entity_relationships table does not exist")
        return
    
    # Get the structure of the entity_relationships table
    success, result = execute_query(
        """
        SELECT column_name, data_type, is_nullable
        FROM information_schema.columns 
        WHERE table_schema = %s
        AND table_name = 'entity_relationships'
        ORDER BY ordinal_position
        """,
        (schema_name,),
        schema_name
    )
    
    if not success or not result:
        logger.error("Failed to get entity_relationships table structure")
        return
    
    logger.info("Entity relationships table structure:")
    for row in result:
        logger.info(f"  - Column: {row[0]}, Type: {row[1]}, Nullable: {row[2]}")
    
    # Check if the on_delete, on_update, and foreign_key_type columns exist
    columns = [row[0] for row in result]
    on_delete_exists = 'on_delete' in columns
    on_update_exists = 'on_update' in columns
    foreign_key_type_exists = 'foreign_key_type' in columns
    
    # Add missing columns if needed
    if not on_delete_exists:
        logger.info("Adding on_delete column to entity_relationships table")
        success, error = execute_query(
            f"ALTER TABLE {schema_name}.entity_relationships ADD COLUMN on_delete VARCHAR(50)",
            schema_name=schema_name
        )
        if not success:
            logger.error(f"Failed to add on_delete column: {error}")
        else:
            logger.info("Added on_delete column successfully")
    
    if not on_update_exists:
        logger.info("Adding on_update column to entity_relationships table")
        success, error = execute_query(
            f"ALTER TABLE {schema_name}.entity_relationships ADD COLUMN on_update VARCHAR(50)",
            schema_name=schema_name
        )
        if not success:
            logger.error(f"Failed to add on_update column: {error}")
        else:
            logger.info("Added on_update column successfully")
    
    if not foreign_key_type_exists:
        logger.info("Adding foreign_key_type column to entity_relationships table")
        success, error = execute_query(
            f"ALTER TABLE {schema_name}.entity_relationships ADD COLUMN foreign_key_type VARCHAR(50)",
            schema_name=schema_name
        )
        if not success:
            logger.error(f"Failed to add foreign_key_type column: {error}")
        else:
            logger.info("Added foreign_key_type column successfully")
    
    # Verify the columns were added
    if not on_delete_exists or not on_update_exists or not foreign_key_type_exists:
        success, result = execute_query(
            """
            SELECT column_name
            FROM information_schema.columns 
            WHERE table_schema = %s
            AND table_name = 'entity_relationships'
            AND column_name IN ('on_delete', 'on_update', 'foreign_key_type')
            """,
            (schema_name,),
            schema_name
        )
        
        if success and result:
            logger.info("Verification of added columns:")
            for row in result:
                logger.info(f"  - Column: {row[0]} exists")
        else:
            logger.error("Failed to verify added columns")

if __name__ == "__main__":
    check_and_update_entity_relationships_table()
