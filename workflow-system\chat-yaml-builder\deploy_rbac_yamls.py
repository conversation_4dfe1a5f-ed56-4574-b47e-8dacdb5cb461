#!/usr/bin/env python3
"""
Script to deploy RBAC YAML files directly using the pipeline_runner.
"""

import os
import sys
from pipeline_runner import run_pipeline_from_text

def read_yaml_file(file_path):
    """Read YAML file."""
    with open(file_path, 'r') as f:
        return f.read()

def deploy_yaml(yaml_text, yaml_name):
    """Deploy YAML to the database using the pipeline_runner."""
    print(f"Deploying {yaml_name}...")
    success, message = run_pipeline_from_text(yaml_text)
    
    if success:
        print(f"✅ Successfully deployed {yaml_name}")
        print(f"Message: {message}")
    else:
        print(f"❌ Failed to deploy {yaml_name}")
        print(f"Error: {message}")
        # Fail immediately if deployment fails
        sys.exit(1)
    
    return success

def main():
    """Main function."""
    # Path to the YAML files
    yaml_files = [
        ("/home/<USER>/workflow-system/runtime/workflow-engine/yamls/user_management_workflow_fixed.yaml", "User Management Workflow"),
        ("/home/<USER>/workflow-system/runtime/workflow-engine/yamls/leavemanagement_new_rbac.yaml", "Leave Management Workflow")
    ]
    
    # Check if the files exist
    for yaml_file, _ in yaml_files:
        if not os.path.exists(yaml_file):
            print(f"❌ File not found: {yaml_file}")
            return
    
    # Deploy each YAML file
    for yaml_file, yaml_name in yaml_files:
        yaml_text = read_yaml_file(yaml_file)
        success = deploy_yaml(yaml_text, yaml_name)
        
        if not success:
            print(f"❌ Failed to deploy {yaml_name}. Stopping deployment.")
            return
        
        # Wait a bit between deployments
        print("Waiting 2 seconds before next deployment...")
        import time
        time.sleep(2)
    
    print("✅ All YAML files deployed successfully!")

if __name__ == "__main__":
    main()
