import unittest
from unittest.mock import patch
import datetime
from typing import Dict, Any
import sys
import os

# Add project root to path if needed
project_root = '/home/<USER>/workflow-system/runtime/workflow-engine'
if project_root not in sys.path:
    sys.path.insert(0, project_root)

# Import the function to test
from app.services.system_functions import validate_date_range

class TestValidateDateRange(unittest.TestCase):
    def test_valid_date_in_range_inclusive(self):
        """Test a valid date that falls within the range (inclusive)."""
        date_value = "2023-06-15"
        start_date = "2023-06-01"
        end_date = "2023-06-30"
        
        result = validate_date_range(date_value, start_date, end_date)
        
        self.assertTrue(result["is_valid"])
        self.assertEqual(result["message"], "Date is within the specified range")
    
    def test_valid_date_at_start_inclusive(self):
        """Test a valid date that equals the start date (inclusive)."""
        date_value = "2023-06-01"
        start_date = "2023-06-01"
        end_date = "2023-06-30"
        
        result = validate_date_range(date_value, start_date, end_date)
        
        self.assertTrue(result["is_valid"])
        self.assertEqual(result["message"], "Date is within the specified range")
    
    def test_valid_date_at_end_inclusive(self):
        """Test a valid date that equals the end date (inclusive)."""
        date_value = "2023-06-30"
        start_date = "2023-06-01"
        end_date = "2023-06-30"
        
        result = validate_date_range(date_value, start_date, end_date)
        
        self.assertTrue(result["is_valid"])
        self.assertEqual(result["message"], "Date is within the specified range")
    
    def test_valid_date_in_range_exclusive(self):
        """Test a valid date that falls within the range (exclusive)."""
        date_value = "2023-06-15"
        start_date = "2023-06-01"
        end_date = "2023-06-30"
        inclusive = False
        
        result = validate_date_range(date_value, start_date, end_date, inclusive)
        
        self.assertTrue(result["is_valid"])
        self.assertEqual(result["message"], "Date is within the specified range")
    
    def test_valid_date_at_start_exclusive(self):
        """Test a valid date that equals the start date (exclusive)."""
        date_value = "2023-06-01"
        start_date = "2023-06-01"
        end_date = "2023-06-30"
        inclusive = False
        
        result = validate_date_range(date_value, start_date, end_date, inclusive)
        
        self.assertFalse(result["is_valid"])
        self.assertEqual(result["message"], "Date is not within the specified range")
    
    def test_valid_date_at_end_exclusive(self):
        """Test a valid date that equals the end date (exclusive)."""
        date_value = "2023-06-30"
        start_date = "2023-06-01"
        end_date = "2023-06-30"
        inclusive = False
        
        result = validate_date_range(date_value, start_date, end_date, inclusive)
        
        self.assertFalse(result["is_valid"])
        self.assertEqual(result["message"], "Date is not within the specified range")
    
    def test_date_before_range(self):
        """Test a valid date that falls before the range."""
        date_value = "2023-05-15"
        start_date = "2023-06-01"
        end_date = "2023-06-30"
        
        result = validate_date_range(date_value, start_date, end_date)
        
        self.assertFalse(result["is_valid"])
        self.assertEqual(result["message"], "Date is not within the specified range")
    
    def test_date_after_range(self):
        """Test a valid date that falls after the range."""
        date_value = "2023-07-15"
        start_date = "2023-06-01"
        end_date = "2023-06-30"
        
        result = validate_date_range(date_value, start_date, end_date)
        
        self.assertFalse(result["is_valid"])
        self.assertEqual(result["message"], "Date is not within the specified range")
    
    def test_invalid_date_format_date_value(self):
        """Test an invalid date format for date_value."""
        date_value = "2023/06/15"  # Wrong format, should be YYYY-MM-DD
        start_date = "2023-06-01"
        end_date = "2023-06-30"
        
        result = validate_date_range(date_value, start_date, end_date)
        
        self.assertFalse(result["is_valid"])
        self.assertTrue("Invalid date format" in result["message"])
    
    def test_invalid_date_format_start_date(self):
        """Test an invalid date format for start_date."""
        date_value = "2023-06-15"
        start_date = "06/01/2023"  # Wrong format, should be YYYY-MM-DD
        end_date = "2023-06-30"
        
        result = validate_date_range(date_value, start_date, end_date)
        
        self.assertFalse(result["is_valid"])
        self.assertTrue("Invalid date format" in result["message"])
    
    def test_invalid_date_format_end_date(self):
        """Test an invalid date format for end_date."""
        date_value = "2023-06-15"
        start_date = "2023-06-01"
        end_date = "June 30, 2023"  # Wrong format, should be YYYY-MM-DD
        
        result = validate_date_range(date_value, start_date, end_date)
        
        self.assertFalse(result["is_valid"])
        self.assertTrue("Invalid date format" in result["message"])
    
    def test_invalid_date_value(self):
        """Test an invalid date value (non-existent date)."""
        date_value = "2023-02-30"  # February 30 doesn't exist
        start_date = "2023-02-01"
        end_date = "2023-02-28"
        
        result = validate_date_range(date_value, start_date, end_date)
        
        self.assertFalse(result["is_valid"])
        self.assertTrue("Invalid date format" in result["message"])
    
    def test_end_date_before_start_date(self):
        """Test when end_date is before start_date."""
        date_value = "2023-06-15"
        start_date = "2023-06-30"  # After end_date
        end_date = "2023-06-01"    # Before start_date
        
        result = validate_date_range(date_value, start_date, end_date)
        
        self.assertFalse(result["is_valid"])
        self.assertEqual(result["message"], "Date is not within the specified range")
    
    def test_empty_date_strings(self):
        """Test with empty date strings."""
        date_value = ""
        start_date = ""
        end_date = ""
        
        result = validate_date_range(date_value, start_date, end_date)
        
        self.assertFalse(result["is_valid"])
        self.assertTrue("Invalid date format" in result["message"])
    
    def test_same_day_inclusive(self):
        """Test with all dates set to the same day (inclusive)."""
        date_value = "2023-06-15"
        start_date = "2023-06-15"
        end_date = "2023-06-15"
        
        result = validate_date_range(date_value, start_date, end_date)
        
        self.assertTrue(result["is_valid"])
        self.assertEqual(result["message"], "Date is within the specified range")
    
    def test_same_day_exclusive(self):
        """Test with all dates set to the same day (exclusive)."""
        date_value = "2023-06-15"
        start_date = "2023-06-15"
        end_date = "2023-06-15"
        inclusive = False
        
        result = validate_date_range(date_value, start_date, end_date, inclusive)
        
        self.assertFalse(result["is_valid"])
        self.assertEqual(result["message"], "Date is not within the specified range")

    @patch('app.services.system_functions.datetime')
    def test_strptime_called_correctly(self, mock_datetime):
        """Test that datetime.strptime is called with correct format."""
        # Setup mock objects for datetime
        mock_date_obj = mock_datetime.datetime.strptime.return_value
        mock_date_obj.date.return_value = datetime.date(2023, 6, 15)
        
        # Call the function
        validate_date_range("2023-06-15", "2023-06-01", "2023-06-30")
        
        # Verify strptime was called with correct format
        mock_datetime.datetime.strptime.assert_any_call("2023-06-15", "%Y-%m-%d")
        mock_datetime.datetime.strptime.assert_any_call("2023-06-01", "%Y-%m-%d")
        mock_datetime.datetime.strptime.assert_any_call("2023-06-30", "%Y-%m-%d")

if __name__ == '__main__':
    unittest.main()