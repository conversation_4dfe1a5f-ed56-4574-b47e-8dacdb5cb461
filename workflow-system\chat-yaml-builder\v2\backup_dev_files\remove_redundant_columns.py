#!/usr/bin/env python3
"""
Script to remove redundant columns from workflow_runtime and workflow_temp schemas.
"""

import os
import sys
import logging
import argparse

# Add parent directory to path to import common modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from common.db_connection import get_db_connection
from db_utils import execute_query

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger('remove_redundant_columns')

def remove_columns(schema_name: str, table_name: str, columns: list) -> bool:
    """
    Remove columns from a table.
    
    Args:
        schema_name: Schema name
        table_name: Table name
        columns: List of column names to remove
        
    Returns:
        Boolean indicating if removal was successful
    """
    # Check if the columns exist in the table
    for column in columns:
        query = """
            SELECT EXISTS (
                SELECT FROM information_schema.columns
                WHERE table_schema = %s
                AND table_name = %s
                AND column_name = %s
            )
        """
        
        success, messages, result = execute_query(query, (schema_name, table_name, column))
        
        if not success:
            logger.error(f"Failed to check if column {column} exists in {schema_name}.{table_name}: {messages}")
            return False
        
        column_exists = result and result[0][0]
        
        if column_exists:
            # Remove the column
            query = f"""
                ALTER TABLE {schema_name}.{table_name}
                DROP COLUMN {column}
            """
            
            success, messages, _ = execute_query(query)
            
            if not success:
                logger.error(f"Failed to remove column {column} from {schema_name}.{table_name}: {messages}")
                return False
            
            logger.info(f"Removed column {column} from {schema_name}.{table_name}")
        else:
            logger.info(f"Column {column} does not exist in {schema_name}.{table_name}")
    
    return True

def main():
    """
    Main function.
    """
    parser = argparse.ArgumentParser(description='Remove redundant columns from workflow_runtime and workflow_temp schemas.')
    parser.add_argument('--schema', type=str, help='Schema name (workflow_runtime or workflow_temp)')
    parser.add_argument('--table', type=str, help='Table name')
    
    args = parser.parse_args()
    
    # Define the columns to remove
    columns_to_remove = ['item_id', 'name']
    
    if args.schema and args.table:
        # Remove columns from the specified table
        success = remove_columns(args.schema, args.table, columns_to_remove)
        if success:
            logger.info(f"Successfully removed columns from {args.schema}.{args.table}")
        else:
            logger.error(f"Failed to remove columns from {args.schema}.{args.table}")
    elif args.schema:
        # Remove columns from all tables in the specified schema
        tables = ['lo_input_items', 'lo_output_items']
        for table in tables:
            success = remove_columns(args.schema, table, columns_to_remove)
            if not success:
                logger.error(f"Failed to remove columns from {args.schema}.{table}")
    else:
        # Remove columns from all tables in both schemas
        schemas = ['workflow_runtime', 'workflow_temp']
        tables = ['lo_input_items', 'lo_output_items']
        for schema in schemas:
            for table in tables:
                success = remove_columns(schema, table, columns_to_remove)
                if not success:
                    logger.error(f"Failed to remove columns from {schema}.{table}")

if __name__ == '__main__':
    main()
