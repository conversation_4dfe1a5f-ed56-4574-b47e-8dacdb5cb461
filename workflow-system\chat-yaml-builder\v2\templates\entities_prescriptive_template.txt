# Entity Prescriptive Template

This template defines the structure for entity definitions in the system. The entity definitions follow a specific format that can be parsed and deployed to the database.

## Entity Definition Format

```
[EntityName] has [attribute1]^PK, [attribute2], [attribute3] (enum1, enum2), [attribute4]^FK, [attribute5].

* [EntityName] has [relationship-type] relationship with [RelatedEntity] using [EntityName].[attributeX]^PK to [RelatedEntity].[attributeY]^FK

* [EntityName].[attribute] [PROPERTY_NAME] = [value]
* [EntityName].[attribute] DEFAULT_VALUE = [value]

* [EntityName].[attribute] must be [constraint]

BusinessRule [ruleID] for [EntityName]:
* [EntityName].[condition] must be [state] to [action]
* [EntityName].[attribute1] must be [relation] [EntityName].[attribute2]

[EntityName] has [attribute1], [attribute2], [calculatedAttribute][derived].

CalculatedField [fieldID] for [EntityName].[calculatedAttribute]:
* Formula: [calculation logic]
* Logic Layer: [implementation layer]
* Caching: [caching strategy]
* Dependencies: [EntityName].[attribute1], [EntityName].[attribute2]

* Archive Strategy for [EntityName]:
  - Trigger: [Time-based/Event-based/Manual]
  - Criteria: [Conditions for archiving]
  - Retention: [Duration/Policy]
  - Storage: [Storage solution]
  - Access Pattern: [How archived data is accessed]
  - Restoration: [Process for restoring if needed]

* History Tracking for [EntityName]:
  - Tracked Attributes: [list of attributes with entity prefix]
  - Tracking Method: [Audit table/Temporal tables/Event sourcing]
  - Granularity: [Change level/Snapshot level]
  - Retention: [Duration policy]
  - Access Control: [Who can access history]
```

## Field Definitions

### Basic Entity Declaration
- **EntityName**: The name of the entity (e.g., Employee, Department, LeaveApplication)
- **attribute**: The name of an attribute (e.g., employeeId, firstName, lastName)
- **^PK**: Marker for primary key attributes
- **^FK**: Marker for foreign key attributes
- **enum values**: Possible values for enumeration attributes, listed in parentheses

### Relationships
- **relationship-type**: Type of relationship (one-to-one, one-to-many, many-to-one, many-to-many)
- **RelatedEntity**: The name of the related entity
- **attributeX**: The attribute in the current entity used for the relationship
- **attributeY**: The attribute in the related entity used for the relationship

### Constants & Configuration
- **PROPERTY_NAME**: The name of a property (e.g., MAX_LENGTH, MIN_VALUE, PATTERN)
- **value**: The value of the property

### Validations
- **constraint**: A validation constraint (e.g., unique, not null, greater than 0)

### Business Rules
- **ruleID**: Unique identifier for the business rule (e.g., BR001, BR002)
- **condition**: A condition that triggers the rule
- **state**: The required state for the condition
- **action**: The action that is allowed or required
- **relation**: A relationship between attributes (e.g., greater than, equal to, less than)

### Calculated/Derived Fields
- **calculatedAttribute**: The name of a calculated attribute
- **[derived]**: Marker for derived attributes
- **fieldID**: Unique identifier for the calculated field (e.g., CF001, CF002)
- **calculation logic**: The formula or logic used to calculate the attribute
- **implementation layer**: Where the calculation is performed (e.g., Database, Application, UI)
- **caching strategy**: How the calculated value is cached (e.g., None, Session, Request)
- **Dependencies**: List of attributes that the calculation depends on

### Data Lifecycle Management
- **Trigger**: What triggers archiving or purging (Time-based, Event-based, Manual)
- **Criteria**: Conditions for archiving or purging
- **Retention**: How long data is retained
- **Storage**: Where archived data is stored
- **Access Pattern**: How archived data is accessed
- **Restoration**: Process for restoring archived data
- **Tracked Attributes**: Attributes that are tracked for history
- **Tracking Method**: How history is tracked
- **Granularity**: Level of detail for history tracking
- **Access Control**: Who can access history data

## Validation Rules

1. **Entity Name Format**: Must be a valid identifier (alphanumeric, no spaces)
2. **Attribute Name Format**: Must be a valid identifier (alphanumeric, no spaces)
3. **Primary Key**: Each entity must have at least one primary key attribute
4. **Foreign Key References**: All foreign key attributes must reference a valid entity and primary key
5. **Enumeration Values**: Must be valid identifiers or quoted strings
6. **Business Rule IDs**: Must be unique within the system
7. **Calculated Field IDs**: Must be unique within the system
8. **Dependencies**: All dependencies must reference valid attributes

## Example Entity Definition

```
Employee has employeeId^PK, firstName, lastName, email, phoneNumber, departmentId^FK, managerId^FK, hireDate, status (Active, Inactive, OnLeave), salary, performanceRating.

* Employee has many-to-one relationship with Department using Employee.departmentId to Department.departmentId^PK
* Employee has many-to-one relationship with Employee using Employee.managerId to Employee.employeeId^PK

* Employee.email must be unique
* Employee.email must match pattern "^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$"
* Employee.hireDate must be before current date
* Employee.salary must be greater than 0

BusinessRule EMP001 for Employee:
* Employee.status must be Active to receive performance reviews
* Employee.hireDate must be at least 90 days before Employee.performanceRating can be set

Employee has employeeId, firstName, lastName, fullName[derived].

CalculatedField CF001 for Employee.fullName:
* Formula: CONCAT(firstName, ' ', lastName)
* Logic Layer: Application
* Caching: Session
* Dependencies: Employee.firstName, Employee.lastName

* Archive Strategy for Employee:
  - Trigger: Event-based
  - Criteria: When Employee.status changes to 'Inactive' and remains so for 1 year
  - Retention: 7 years
  - Storage: Cold storage
  - Access Pattern: Read-only through HR archive portal
  - Restoration: Manual process requiring HR Director approval

* History Tracking for Employee:
  - Tracked Attributes: Employee.salary, Employee.departmentId, Employee.managerId, Employee.status
  - Tracking Method: Audit table
  - Granularity: Change level
  - Retention: 7 years
  - Access Control: HR Managers and Compliance Officers only
```

## Parsing Guidelines

When parsing entity definitions:

1. Extract the entity name and attributes from the first line
2. Identify primary keys, foreign keys, and enumeration values
3. Parse relationships to extract related entities and attributes
4. Parse constants and validations
5. Parse business rules to extract rule IDs, conditions, and actions
6. Parse calculated fields to extract formulas and dependencies
7. Parse data lifecycle management details
8. Validate all references against the system database
