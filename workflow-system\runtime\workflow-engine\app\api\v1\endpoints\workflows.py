import json
import uuid

import datetime

from sqlalchemy.orm import Session
from sqlalchemy.sql import text
from fastapi import APIRouter, Depends, HTTPException, Path, Query, Body, Request

from typing import List, Dict, Tuple, Any
from app.database.postgres import get_db
from app.models.workflow import WorkflowInstance, GlobalObjective, LocalObjective, WorkflowInstanceStatus
from app.services.workflow.execution_engine import WorkflowExecutionEngine
from app.services.function_repository import function_repository
from app.auth.auth_middleware import get_security_context, require_auth, SecurityContext
import traceback
from types import SimpleNamespace
from sqlalchemy.sql import text  # ✅ Ensure this is imported
from typing import Optional
from datetime import datetime, date
from app.services.parameter_resolver import ParameterResolver  # Ensure this import exists
import re


def convert_json_safe(value):
    if isinstance(value, (datetime, date)):
        return value.strftime("%Y-%m-%d %H:%M:%S") if isinstance(value, datetime) else value.strftime("%Y-%m-%d")
    elif isinstance(value, dict):
        return {k: convert_json_safe(v) for k, v in value.items()}
    elif isinstance(value, list):
        return [convert_json_safe(v) for v in value]
    return value

# 🔧 Normalize result key names for reliable matching
def normalize_key(key: str) -> str:
    return re.sub(r'\W+', '', key).lower()

# 🔧 Resolve key against contextual map
def resolve_contextual_id(key: str, contextual_map: dict) -> Optional[str]:
    normalized = normalize_key(key)
    candidates = [normalized, normalized.replace("_", ""), key.lower()]
    for c in candidates:
        if c in contextual_map:
            return contextual_map[c]
    return None

def format_output_result(raw_result: Dict[str, Any], attribute_display_map: Dict[str, str]) -> Dict[str, Dict[str, Any]]:
    """Converts flat output {attribute_id: value} into structured format"""
    formatted = {}
    print(f"Raw result before formatting: {raw_result}")

    for attr_id, value in raw_result.items():
        # Unwrap value if it's a dictionary with a single 'value' key
        if isinstance(value, dict) and 'value' in value:
            print(f"  ✅ Unwrapping {attr_id} in output formatting: {value} → {value['value']}")
            value = value['value']

        formatted[attr_id] = {
            "value": value,
            "display_name": attribute_display_map.get(attr_id, attr_id)
        }
    return formatted



router = APIRouter()

@router.post("/database/create-tables")
def create_tables(
    db: Session = Depends(get_db),
    security_context: SecurityContext = Depends(require_auth)
):
    """
    Create all missing tables dynamically based on entities and attributes.
    """
    entities_query = "SELECT name FROM workflow_runtime.entities"
    entities_result = db.execute(text(entities_query)).fetchall()
    entities = [row[0] for row in entities_result]

    for entity in entities:
        attributes_query = f"""
        SELECT name, data_type
        FROM workflow_runtime.entity_attributes
        WHERE entity_id = (SELECT entity_id FROM workflow_runtime.entities WHERE name = :entity)
        """
        attributes_result = db.execute(text(attributes_query), {"entity": entity}).fetchall()

        columns_definitions = ", ".join([f"{attr[0]} {attr[1]}" for attr in attributes_result])
        create_table_query = f"CREATE TABLE IF NOT EXISTS {entity} (id SERIAL PRIMARY KEY, {columns_definitions})"
        db.execute(text(create_table_query))

    db.commit()
    return {"status": "success", "message": "Tables created successfully"}


@router.post("/instances", response_model=WorkflowInstance)
async def create_workflow_instance(
    go_id: str = Body(..., description="Global Objective ID"),
    tenant_id: str = Body(..., description="Tenant ID"),
    user_id: str = Body(..., description="User ID who started the workflow"),
    test_mode: bool = Body(False, description="Whether this is a test instance"),
    db: Session = Depends(get_db),
    security_context: SecurityContext = Depends(require_auth)
):
    """
    Create a new workflow instance for a given global objective.
    """
    try:
        instance_id = str(uuid.uuid4())
        current_time = datetime.utcnow()

        # Update last_used field in global_objectives
        update_last_used_query = """
        UPDATE workflow_runtime.global_objectives
        SET last_used = :current_time
        WHERE go_id = :go_id
        """
        db.execute(text(update_last_used_query), {"go_id": go_id, "current_time": current_time})
        query = """
        INSERT INTO workflow_runtime.workflow_instances (
            instance_id, go_id, tenant_id, status, started_by, started_at,
            current_lo_id, instance_data, is_test, version
        ) VALUES (:instance_id, :go_id, :tenant_id, :status, :started_by, :started_at,
                  NULL, :instance_data, :is_test, :version)
        RETURNING *;
        """
        params = {
            "instance_id": instance_id,
            "go_id": go_id,
            "tenant_id": tenant_id,
            "status": WorkflowInstanceStatus.DRAFT.value,
            "started_by": user_id,
            "started_at": datetime.utcnow(),
            "instance_data": json.dumps({}),
            "is_test": test_mode,
            "version": "1.0"
        }


        # ✅ Execute the SQL query and assign `result`
        result = db.execute(text(query), params).fetchone()
        db.commit()

        # ✅ Ensure `result` is not None before using it
        if not result:
            raise HTTPException(status_code=500, detail="Failed to create workflow instance")
        insert_transaction = """
         INSERT INTO workflow_transaction (
          workflow_instance_id,
          go_id,
          lo_id,
          tenant_id,
          user_id,
          status,
          input_stack,
          created_at,
          updated_at
          ) VALUES (
          :workflow_instance_id,
          :go_id,
          :lo_id,
          :tenant_id,
          :user_id,
          :status,
          :input_stack,
           NOW(),
           NOW()
         )
           """
        db.execute(text(insert_transaction), {
           "workflow_instance_id": instance_id,
           "go_id":go_id,
           "lo_id": '',
           "tenant_id":  tenant_id,
           "user_id":  user_id,
           "status": 'pending',
           "input_stack": json.dumps({})
            })
        db.commit()
        print(f"📝 Logged transaction with status = {WorkflowInstanceStatus.DRAFT.value}")

         # ✅ Fetch all Local Objectives for this Global Objective
        query_los = """
        SELECT lo_id FROM workflow_runtime.local_objectives WHERE go_id = :go_id
        """
        los = db.execute(text(query_los), {"go_id": go_id}).fetchall()

        # ✅ Insert Input & Output Stack Variables for Each LO
        for lo in los:
            lo_id = lo[0]

            query_input_stack = """
            INSERT INTO workflow_runtime.lo_input_execution (instance_id, lo_id, input_contextual_id, input_value)
            SELECT :instance_id, :lo_id, contextual_id, NULL
            FROM workflow_runtime.lo_input_items WHERE lo_id = :lo_id
            """
            db.execute(text(query_input_stack), {"instance_id": instance_id, "lo_id": lo_id})

            query_output_stack = """
            INSERT INTO workflow_runtime.lo_output_execution (instance_id, lo_id, output_contextual_id, output_value)
            SELECT :instance_id, :lo_id, contextual_id, NULL
            FROM workflow_runtime.lo_output_items WHERE lo_id = :lo_id
            """
            db.execute(text(query_output_stack), {"instance_id": instance_id, "lo_id": lo_id})


        db.commit()


        return {
            "instance_id": str(result.instance_id),  # ✅ Convert UUID to string
            "go_id": result.go_id,
            "tenant_id": result.tenant_id,  # ✅ Include required field
            "status": result.status,
            "started_by": result.started_by,  # ✅ Include required field
            "version": result.version  # ✅ Include required field
        }

    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"Error creating workflow instance: {str(e)}")

@router.get("/instances/{instance_id}", response_model=WorkflowInstance)
async def get_workflow_instance(
    instance_id: str, 
    db: Session = Depends(get_db),
    security_context: SecurityContext = Depends(require_auth)
):
    """
    Get details of a workflow instance.
    """
    try:
        query = "SELECT * FROM workflow_runtime.workflow_instances WHERE instance_id = :instance_id"
        result = db.execute(text(query), {"instance_id": str(instance_id)}).fetchone()

        if not result:
            raise HTTPException(status_code=404, detail=f"Workflow instance {instance_id} not found")

        return result._asdict()
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error retrieving workflow instance: {str(e)}")



#Fetching all global objectives

@router.get("/global-objectives/", response_model=List[Dict[str, str]])
def get_global_objectives(
    tenant_id: str, 
    db: Session = Depends(get_db),
    security_context: SecurityContext = Depends(require_auth)
):
    try:
        results = db.execute(
            """
            SELECT objective_id, contextual_id, name, tenant_id, version, status,deleted_mark
            FROM global_objectives
            WHERE tenant_id = :tenant_id
            AND deleted_mark = false
            """,
            {"tenant_id": tenant_id.lower()}
        ).fetchall()

        return [
            {
                "objective_id": row[0],
                "contextual_id": row[1],
                "name": row[2],
                "tenant_id": row[3],
                "version": row[4],
                "status": row[5],
                "deleted_mark": row[6]
            }
            for row in results
        ]

    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
###############################


@router.post("/instances/{instance_id}/start", response_model=WorkflowInstance)
async def start_workflow_instance(
    instance_id: str, 
    user_id: str = Body(..., embed=True), 
    db: Session = Depends(get_db),
    security_context: SecurityContext = Depends(require_auth)
):
    """
    Start a workflow instance.
    """
    try:
        query = "SELECT * FROM workflow_runtime.workflow_instances WHERE instance_id = :instance_id"
        result = db.execute(text(query), {"instance_id": instance_id}).fetchone()

        # ✅ Convert result from tuple to dictionary
        if result is None:
            raise HTTPException(status_code=404, detail=f"Workflow instance {instance_id} not found")

        result_dict = dict(result._mapping)  # ✅ Fix: Convert Row object to dictionary

        go_id = result_dict["go_id"]
        tenant_id = result_dict["tenant_id"]
        started_by = result_dict["started_by"]
        version = result_dict["version"]

        # ✅ Fetch the first Local Objective from PostgreSQL
        #query_lo = "SELECT lo_id FROM workflow_runtime.local_objectives WHERE go_id = :go_id ORDER BY created_at ASC LIMIT 1"
        query_lo = """
            SELECT lo_id FROM workflow_runtime.local_objectives
            WHERE go_id = :go_id
            AND LOWER(workflow_source) = 'origin'
            LIMIT 1
            """

        first_lo = db.execute(text(query_lo), {"go_id": go_id}).fetchone()

        if not first_lo:
            raise HTTPException(status_code=404, detail=f"No starting local objective found for workflow {go_id}")

        #first_lo_id = str(uuid.uuid5(uuid.NAMESPACE_DNS, first_lo[0]))  # ✅ Fetch the first column (ID)

        first_lo_id = str(first_lo[0])  # ✅ Convert UUID to string

        # ✅ Update the workflow instance
        query_update = """
        UPDATE workflow_runtime.workflow_instances
        SET status = :status, current_lo_id = :current_lo_id, updated_at = NOW()
        WHERE instance_id = :instance_id
        RETURNING instance_id, go_id, tenant_id, status, started_by, version, current_lo_id;
        """
        params = {
            "status": WorkflowInstanceStatus.ACTIVE.value,
            "current_lo_id": first_lo_id,
            "instance_id": instance_id
        }
        update_result = db.execute(text(query_update), params).fetchone()
        db.commit()

        # ✅ Convert update_result to dictionary
        if not update_result:
            raise HTTPException(status_code=500, detail="Failed to update workflow instance")

        update_dict = dict(update_result._mapping)


        return {
            "instance_id": str(update_dict["instance_id"]),  # ✅ Convert UUID to string
            "go_id": update_dict["go_id"],
            "tenant_id": update_dict["tenant_id"],
            "status": update_dict["status"],
            "started_by": update_dict["started_by"],
            "version": update_dict["version"],
            "current_lo_id": str(update_dict["current_lo_id"])  # ✅ Convert UUID to string
        }

    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"Error starting workflow instance: {str(e)}")


@router.get("/instances", response_model=List[WorkflowInstance])
async def list_workflow_instances(
    tenant_id: str = Query(None, description="Filter by tenant ID"),
    status: str = Query(None, description="Filter by status"),
    limit: int = Query(10, description="Maximum number of instances to return"),
    skip: int = Query(0, description="Number of instances to skip"),
    db: Session = Depends(get_db),  # ✅ Add database session
    security_context: SecurityContext = Depends(require_auth)
):
    """
    List workflow instances with optional filtering.
    """
    try:
        query = "SELECT * FROM workflow_runtime.workflow_instances WHERE 1=1"
        params = {}

        if tenant_id:
            query += " AND tenant_id = :tenant_id"
            params["tenant_id"] = tenant_id

        if status:
            query += " AND status = :status"
            params["status"] = status

        query += " ORDER BY started_at DESC LIMIT :limit OFFSET :skip"
        params["limit"] = limit
        params["skip"] = skip

        result = db.execute(text(query), params).fetchall()

        return [row._asdict() for row in result]
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error listing workflow instances: {str(e)}")

######################### Functions for Execution Engine ######################

def fetch_workflow_instance(db: Session, instance_id: str) -> Tuple[Dict, str, str, str]:
    query = "SELECT * FROM workflow_runtime.workflow_instances WHERE instance_id = :instance_id"
    result = db.execute(text(query), {"instance_id": instance_id}).fetchone()
    if not result:
        raise HTTPException(status_code=404, detail=f"Workflow instance {instance_id} not found")

    result_dict = dict(result._mapping)
    if result_dict["status"].lower() != "active":
        raise HTTPException(status_code=400, detail=f"Workflow instance {instance_id} is not active")

    lo_id = result_dict["current_lo_id"] if isinstance(result_dict["current_lo_id"], str) \
            else result_dict["current_lo_id"].get("lo_id")

    return result_dict, lo_id, result_dict["go_id"], result_dict["tenant_id"]


def resolve_inputs(db: Session, instance_id: str, lo_id: str, input_data: Dict[str, Any]) -> Tuple[Dict[str, Any], Dict[str, str], List[Dict[str, Any]]]:
    """
    Resolve input values from mappings, previous steps, and user input.
    
    Returns:
        Tuple containing:
            - final_input_data: Combined input data from all sources
            - attr_map: Mapping from contextual IDs to attribute IDs
            - input_combined_array: Array format for logging/display
    """
    # Create resolver
    resolver = ParameterResolver(db_session=db)
    
    # First, transform input_data keys to match both display names and attribute IDs
    normalized_input = {}
    
    # Fetch display name to attribute ID mapping
    query_attr_mapping = """
    SELECT a.attribute_id, a.display_name, a.name
    FROM workflow_runtime.entity_attributes a
    JOIN workflow_runtime.lo_input_items i ON SPLIT_PART(i.slot_id, '.', 2) = a.attribute_id
    WHERE i.lo_id = :lo_id
    """
    attr_rows = db.execute(text(query_attr_mapping), {"lo_id": lo_id}).fetchall()
    
    # Create mappings for various ways to refer to each attribute
    display_to_attr = {}
    name_to_attr = {}
    
    for row in attr_rows:
        if row.display_name:
            display_to_attr[row.display_name.lower()] = row.attribute_id
            display_to_attr[row.display_name.lower().replace(" ", "_")] = row.attribute_id
            display_to_attr[row.display_name.lower().replace(" ", "")] = row.attribute_id
        
        if row.name:
            name_to_attr[row.name.lower()] = row.attribute_id
            name_to_attr[row.name.lower().replace(" ", "_")] = row.attribute_id
    
    # Track which attribute IDs have already been processed to avoid duplicates
    processed_attr_ids = set()
    
    # Process input_data to handle various key formats
    for key, value in input_data.items():
        # Try to find the canonical attribute ID for this input
        attr_id = None
        
        # Check if key maps to a display name
        if key.lower() in display_to_attr:
            attr_id = display_to_attr[key.lower()]
        # Or if it maps to a field name
        elif key.lower() in name_to_attr:
            attr_id = name_to_attr[key.lower()]
        # Or if it's already an attribute ID
        elif key.lower() in [attr.attribute_id.lower() for attr in attr_rows]:
            attr_id = key
            
        # If we found a valid attribute ID and haven't processed it yet
        if attr_id and attr_id.lower() not in processed_attr_ids:
            # Add the canonical attribute ID to our normalized input
            normalized_input[attr_id] = value
            processed_attr_ids.add(attr_id.lower())
            print(f"✅ Mapped input '{key}' → attribute ID '{attr_id}'")
        # If we can't map it to an attribute ID, just keep the original key
        elif not attr_id:
            normalized_input[key] = value
            print(f"⚠️ Could not map '{key}' to attribute ID, keeping original")
    
    # Rest of the function remains the same, but we use normalized_input instead of the original
    # input_data when we merge inputs from different sources
    
    # Fetch mapped values from previous steps
    mapped_query = """
    SELECT ldm.target, loe.output_value
    FROM workflow_runtime.lo_data_mappings ldm
    JOIN workflow_runtime.lo_output_execution loe ON ldm.source = loe.output_contextual_id
    WHERE SPLIT_PART(ldm.target, '.', 2) = :lo_id AND loe.instance_id = :instance_id
    """
    mapped = db.execute(text(mapped_query), {"lo_id": lo_id, "instance_id": instance_id}).fetchall()

    # Start with mapped values from previous steps
    final_input = {row.target.split(".")[-1]: deserialize_value(row.output_value) for row in mapped}
    
    # Add normalized user input, overriding mapped values if needed
    final_input.update(normalized_input)

    # Fetch input stack for contextual ID mapping
    query = """
    SELECT i.contextual_id, SPLIT_PART(i.slot_id, '.', 2) AS attribute_id, i.metadata
    FROM workflow_runtime.lo_input_items i
    WHERE i.lo_id = :lo_id
    """
    rows = db.execute(text(query), {"lo_id": lo_id}).fetchall()
    attr_map = {row.contextual_id.split(".")[-1]: row.attribute_id for row in rows}

    # Also create a map for input usage
    input_usage_map = {}
    for row in rows:
        input_id = row.contextual_id.split(".")[-1]  # e.g., "in008"
        attr_id = row.attribute_id  # e.g., "at001"
        
        # Determine usage type from metadata
        usage = None
        try:
            if isinstance(row.metadata, dict):
                usage = row.metadata.get("usage", "")
            elif isinstance(row.metadata, str) and row.metadata.strip():
                usage = json.loads(row.metadata).get("usage", "")
        except Exception:
            usage = ""
        
        input_usage_map[input_id] = usage
        input_usage_map[attr_id] = usage

    # Ensure consistent mapping from contextual IDs to attribute IDs
    for ctx_id, attr_id in attr_map.items():
        if ctx_id in final_input and attr_id not in final_input:
            final_input[attr_id] = final_input[ctx_id]

    # Load previous direct input values
    query = """
    SELECT DISTINCT ON (input_contextual_id) input_contextual_id, input_value
    FROM workflow_runtime.lo_input_execution
    WHERE instance_id = :instance_id AND lo_id = :lo_id AND input_value IS NOT NULL
    ORDER BY input_contextual_id, created_at DESC
    """
    rows = db.execute(text(query), {"instance_id": instance_id, "lo_id": lo_id}).fetchall()
    for row in rows:
        ctx_id = row.input_contextual_id.split(".")[-1]  # e.g., "in008" or "at101"
        attr_id = attr_map.get(ctx_id)
        value = deserialize_value(row.input_value)
        
        if value is not None:  # Only add non-null values
            final_input[ctx_id] = value
            
            # CRITICAL FIX: Also add by attribute ID for lookup fields
            if attr_id and attr_id not in final_input:
                final_input[attr_id] = value
                # Print that we're adding a lookup field
                usage = input_usage_map.get(ctx_id) or input_usage_map.get(attr_id)
                if usage == "lookup" or usage == "both":
                    print(f"✅ Added lookup field {attr_id} = {value} from {ctx_id}")

    # CRITICAL FIX: For functions like update, ensure all lookup fields are included
    function_type_query = """
    SELECT function_type FROM workflow_runtime.local_objectives WHERE lo_id = :lo_id
    """
    function_type = db.execute(text(function_type_query), {"lo_id": lo_id}).scalar()
    
    # Special handling for update function to ensure lookup fields are included
    if function_type and function_type.lower() == "update":
        print(f"\n🔍 Ensuring lookup fields are included for UPDATE operation")
        # Get all input items
        input_items_query = """
        SELECT i.id, i.slot_id, i.contextual_id, i.metadata, SPLIT_PART(i.slot_id, '.', 2) AS attribute_id
        FROM workflow_runtime.lo_input_items i
        WHERE i.lo_id = :lo_id
        """
        input_items = db.execute(text(input_items_query), {"lo_id": lo_id}).fetchall()
        
        # Check for lookup fields that might be missing
        for item in input_items:
            attr_id = item.attribute_id
            input_id = item.id
            ctx_id = item.contextual_id.split(".")[-1]
            
            # Parse metadata to determine if this is a lookup field
            usage = None
            try:
                if isinstance(item.metadata, dict):
                    usage = item.metadata.get("usage", "")
                elif isinstance(item.metadata, str) and item.metadata.strip():
                    usage = json.loads(item.metadata).get("usage", "")
            except Exception:
                usage = ""
            
            # If this is a lookup field and it's missing, try to fetch it
            if (usage == "lookup" or usage == "both") and attr_id not in final_input:
                # Check if we have it under a different key first
                found = False
                for key in [input_id, ctx_id]:
                    if key in final_input:
                        final_input[attr_id] = final_input[key]
                        print(f"✅ Mapped lookup field {attr_id} = {final_input[key]} from {key}")
                        found = True
                        break
                
                # If still not found, try to get it from database
                if not found:
                    db_query = """
                    SELECT input_value FROM workflow_runtime.lo_input_execution
                    WHERE instance_id = :instance_id AND lo_id = :lo_id AND input_contextual_id = :ctx_id
                    ORDER BY created_at DESC LIMIT 1
                    """
                    db_result = db.execute(text(db_query), {
                        "instance_id": instance_id,
                        "lo_id": lo_id,
                        "ctx_id": item.contextual_id
                    }).fetchone()
                    
                    if db_result and db_result.input_value:
                        value = deserialize_value(db_result.input_value)
                        final_input[attr_id] = value
                        print(f"✅ Retrieved lookup field {attr_id} = {value} from database")

    # Prepare combined array format for logging/display
    combined_array = [{"attribute_id": k, "value": v} for k, v in final_input.items()]
    
    # Log the final result
    print(f"\n📝 Final input data ({len(final_input)} items):")
    for k, v in sorted(final_input.items()):
        print(f"  ▫️ {k}: {v}")
    
    return final_input, attr_map, combined_array


def run_nested_functions(db: Session, lo_id: str, instance_id: str, input_data: Dict[str, Any]) -> Dict[str, Any]:
    """Run all nested functions for a Local Objective with proper parameter resolution."""
    resolver = ParameterResolver(db_session=db)
    
    # Create a comprehensive context with input IDs, display names, and attribute IDs
    context_data = dict(input_data)  # Start with a copy of input_data
    
    # Add mappings: input ID → actual value
    query_input_mappings = """
    SELECT i.id, i.contextual_id, a.attribute_id, a.display_name, 
           SPLIT_PART(i.contextual_id, '.', 3) as input_id
    FROM workflow_runtime.lo_input_items i
    LEFT JOIN workflow_runtime.entity_attributes a
       ON SPLIT_PART(i.slot_id, '.', 2) = a.attribute_id
    WHERE i.lo_id = :lo_id
    """
    input_rows = db.execute(text(query_input_mappings), {"lo_id": lo_id}).fetchall()
    
    # Build all possible ways to reference an input value
    for row in input_rows:
        input_id = f"in{row.id:03d}" if isinstance(row.id, int) else row.id
        display_name = row.display_name
        attribute_id = row.attribute_id
        contextual_id = row.contextual_id
        
        # See which formats we have in the input data
        if display_name and display_name in input_data:
            value = input_data[display_name]
            # Add all reference formats to context_data
            context_data[input_id] = value
            context_data[attribute_id] = value
            # Also add lowercase versions
            context_data[display_name.lower()] = value
            context_data[attribute_id.lower()] = value
            context_data[input_id.lower()] = value
        elif attribute_id and attribute_id in input_data:
            value = input_data[attribute_id]
            context_data[input_id] = value
            if display_name:
                context_data[display_name] = value
            # Also add lowercase versions
            if display_name:
                context_data[display_name.lower()] = value
            context_data[attribute_id.lower()] = value
            context_data[input_id.lower()] = value
    
    # Fetch all nested functions
    query = """
    SELECT input_contextual_id, function_name, parameters, output_to
    FROM workflow_runtime.lo_nested_functions
    WHERE lo_id = :lo_id
    ORDER BY created_at ASC
    """
    rows = db.execute(text(query), {"lo_id": lo_id}).fetchall()
    
    # Execute each function
    for row in rows:
        params = json.loads(row.parameters or "{}") if isinstance(row.parameters, str) else row.parameters or {}
        
        # Resolve parameters with our comprehensive context
        resolved_params = resolver.resolve_parameters(params, context_data)
        
        print(f"⚙️ Executing nested function {row.function_name} with parameters:")
        print(json.dumps(resolved_params, indent=2))
        
        # Execute function with resolved parameters
        try:
            result = function_repository.auto_execute(row.function_name, db, **resolved_params)
            
            # Store result in both context data and output variable
            if row.output_to:
                input_data[row.output_to] = result
                context_data[row.output_to] = result
        except Exception as e:
            print(f"❌ Error executing {row.function_name}: {str(e)}")
            print(f"Parameters: {resolved_params}")
            raise ValueError(f"Error in nested function {row.function_name}: {str(e)}")
        
    return input_data


def update_input_execution(db: Session, instance_id: str, lo_id: str, input_data: Dict[str, Any]):
    """Update input values in the lo_input_execution table."""
    if not input_data:
        print("⚠️ No input data provided to update_input_execution")
        return
    
    print(f"\n📝 Updating input execution for LO: {lo_id}, Instance: {instance_id}")
    print(f"📝 Input data keys: {list(input_data.keys())}")
    
    # Step 1: Get mappings from attribute IDs to contextual IDs
    query = """
    SELECT 
        id, 
        contextual_id, 
        SPLIT_PART(slot_id, '.', 2) AS attribute_id
    FROM workflow_runtime.lo_input_items
    WHERE lo_id = :lo_id
    """
    
    try:
        rows = db.execute(text(query), {"lo_id": lo_id}).fetchall()
        
        # Create mapping dictionaries (multiple ways to reference the same input)
        attr_to_ctx = {}
        input_to_ctx = {}
        
        for row in rows:
            # Map attribute_id -> contextual_id (e.g., "at101" -> "go001.lo001.in001")
            if row.attribute_id:
                attr_to_ctx[row.attribute_id.lower()] = row.contextual_id
            
            # Map input_id -> contextual_id (e.g., "in001" -> "go001.lo001.in001")
            if row.contextual_id:
                input_id = row.contextual_id.split('.')[-1]
                input_to_ctx[input_id.lower()] = row.contextual_id
        
        print(f"🔄 Found {len(rows)} input mappings")
        
        # Step 2: Loop through input data and update values in the database
        update_count = 0
        unmapped_keys = []
        
        for key, value in input_data.items():
            key_lower = key.lower()
            
            # Try different ways to resolve contextual_id
            ctx_id = None
            
            # Method 1: Direct match on attribute_id (e.g., "at101")
            if key_lower in attr_to_ctx:
                ctx_id = attr_to_ctx[key_lower]
            
            # Method 2: Direct match on input_id (e.g., "in001")
            elif key_lower in input_to_ctx:
                ctx_id = input_to_ctx[key_lower]
            
            # Method 3: Try without any prefix if it starts with "at" or "in"
            elif key_lower.startswith(("at", "in")) and key_lower[2:] in attr_to_ctx:
                ctx_id = attr_to_ctx[key_lower[2:]]
            
            # Method 4: Simple ID number (e.g., "101" for "at101")
            elif key_lower.isdigit():
                digit_key = f"at{key_lower}"
                if digit_key in attr_to_ctx:
                    ctx_id = attr_to_ctx[digit_key]
            
            # If we found a contextual ID, update the record
            if ctx_id:
                # Serialize the value before storing
                serialized_value = serialize_value(value)
                
                update_query = """
                UPDATE workflow_runtime.lo_input_execution
                SET input_value = :val
                WHERE instance_id = :inst_id 
                AND lo_id = :lo_id 
                AND input_contextual_id = :ctx_id
                """
                
                db.execute(text(update_query), {
                    "val": serialized_value,
                    "inst_id": instance_id,
                    "lo_id": lo_id,
                    "ctx_id": ctx_id
                })
                
                update_count += 1
                print(f"✅ Updated {key} → {ctx_id} = {value}")
            else:
                unmapped_keys.append(key)
        
        # Commit all updates in a single transaction
        db.commit()
        
        if unmapped_keys:
            print(f"⚠️ Warning: Could not map {len(unmapped_keys)} keys: {unmapped_keys}")
        
        print(f"💾 Successfully updated {update_count} input values")
        
    except Exception as e:
        db.rollback()
        print(f"❌ ERROR updating input execution: {str(e)}")
        raise


def resolve_function_io(db: Session, lo_id: str, input_data: Dict[str, Any]) -> Tuple[str, str, Dict[str, Any], Dict[str, str]]:
    query = "SELECT function_type FROM workflow_runtime.local_objectives WHERE lo_id = :lo_id"
    function_name = db.execute(text(query), {"lo_id": lo_id}).scalar().lower()

    query = """
    SELECT DISTINCT split_part(slot_id, '.', 1) AS entity_id
    FROM workflow_runtime.lo_input_items
    WHERE lo_id = :lo_id
    """
    entity_id = db.execute(text(query), {"lo_id": lo_id}).scalar()

    query = """
    SELECT attribute_id, display_name FROM workflow_runtime.entity_attributes WHERE entity_id = :entity_id
    """
    rows = db.execute(text(query), {"entity_id": entity_id}).fetchall()
    display_map = {row.display_name.lower(): row.attribute_id for row in rows}

    converted = {}
    for k, v in input_data.items():
        key = k.lower()
        if key in display_map:
            converted[display_map[key]] = v
        elif key in [r.attribute_id.lower() for r in rows]:
            converted[key] = v
    return entity_id, function_name, converted, display_map


def execute_system_function(db: Session, function_name: str, entity_id: str, input_data: Dict[str, Any], lo_id: str):
    try:
        # Use the parameter resolver
        resolver = ParameterResolver(db_session=db)
        
        # Add entity_id to the input data for resolution
        execution_context = {**input_data, "entity_id": entity_id}
        
        # Ensure all input data values are properly resolved
        resolved_input = resolver.resolve_parameters(input_data, execution_context)
        
        # Add audit fields like created_by, updated_by, etc.
        current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        
        # Check if we have user_id in context - if not, try to get it from various places
        user_id = input_data.get('user_id')

        # If not found directly, try alternative keys
        if not user_id:
            user_keys = ['user_id', 'started_by', 'created_by', 'userId']
            for key in input_data:
                if key.lower() in [k.lower() for k in user_keys]:
                    user_id = input_data[key]
                    break

        execution_context['user_id'] = user_id
        
        # Add audit fields if function is create or update
        if function_name.lower() in ['create', 'update']:
            resolved_input['created_at'] = current_time
            resolved_input['updated_at'] = current_time
            if user_id:
                resolved_input['created_by'] = user_id
                resolved_input['updated_by'] = user_id
                print(f"✅ Setting audit fields with user_id: {user_id}")
        
        # Log what we're about to execute
        print(f"⚙️ Executing {function_name} for {entity_id} with inputs:")
        for k, v in resolved_input.items():
            print(f"  ▫️ {k}: {v}")
        
        # Execute the function directly using the function repository
        if function_name.lower() == 'update':
            raw_result = function_repository.auto_execute(function_name, db, entity_id, resolved_input, lo_id)
        else:
            raw_result = function_repository.auto_execute(function_name, db, entity_id, resolved_input)
        
        # Format the result
        display_map_query = "SELECT attribute_id, display_name FROM workflow_runtime.entity_attributes WHERE entity_id = :entity_id"
        rows = db.execute(text(display_map_query), {"entity_id": entity_id}).fetchall()
        attr_map = {r.attribute_id: r.display_name for r in rows}
        formatted = format_output_result(raw_result, attr_map)
        
        return raw_result, formatted
    except Exception as e:
        error_details = {
            "function": function_name,
            "entity_id": entity_id,
            "input_data": str(input_data)[:200],  # Truncate for log readability
            "error": str(e)
        }
        print(f"ERROR executing system function: {json.dumps(error_details, indent=2)}")
        raise ValueError(f"Error executing function '{function_name}': {str(e)}")


def store_outputs(db: Session, instance_id: str, lo_id: str, entity_id: str, raw_result: Dict[str, Any]):
    """Store function execution results in the lo_output_execution table."""
    if not raw_result:
        print("⚠️ No output data provided to store_outputs")
        return
    
    print(f"\n📤 Storing output values for LO: {lo_id}, Instance: {instance_id}")
    print(f"📤 Raw result keys: {list(raw_result.keys())}")
    
    try:
        # Step 1: Get mappings from attribute IDs to contextual IDs
        query_outputs = """
        SELECT o.contextual_id, SPLIT_PART(o.slot_id, '.', 2) AS attribute_id,
               a.display_name
        FROM workflow_runtime.lo_output_items o
        LEFT JOIN workflow_runtime.entity_attributes a 
            ON SPLIT_PART(o.slot_id, '.', 2) = a.attribute_id
        WHERE o.lo_id = :lo_id
        """
        
        rows = db.execute(text(query_outputs), {"lo_id": lo_id}).fetchall()
        
        # Create mapping dictionaries
        attr_to_ctx = {}    # at101 -> go001.lo001.out001
        name_to_ctx = {}    # leave_id -> go001.lo001.out001
        display_to_ctx = {} # Leave ID -> go001.lo001.out001
        
        for row in rows:
            if row.attribute_id:
                attr_to_ctx[row.attribute_id.lower()] = row.contextual_id
                
                # Also map snake_case and camelCase variations of the name
                if row.display_name:
                    display_to_ctx[row.display_name.lower()] = row.contextual_id
                    display_to_ctx[row.display_name.lower().replace(" ", "_")] = row.contextual_id
                    display_to_ctx[row.display_name.lower().replace(" ", "")] = row.contextual_id
        
        print(f"🔄 Found {len(rows)} output mappings")
        
        # Step 2: Add column name mappings from entity_attributes table
        query_attr_mapping = """
        SELECT attribute_id, name, display_name 
        FROM workflow_runtime.entity_attributes
        WHERE entity_id = :entity_id
        """
        
        attr_rows = db.execute(text(query_attr_mapping), {"entity_id": entity_id}).fetchall()
        
        # Map column names to attribute IDs
        col_to_attr = {}
        
        for row in attr_rows:
            if row.name:
                col_to_attr[row.name.lower()] = row.attribute_id
                col_to_attr[row.name.lower().replace(" ", "_")] = row.attribute_id
                col_to_attr[row.name.lower().replace(" ", "")] = row.attribute_id
            
            if row.display_name:
                col_to_attr[row.display_name.lower()] = row.attribute_id
                col_to_attr[row.display_name.lower().replace(" ", "_")] = row.attribute_id
                col_to_attr[row.display_name.lower().replace(" ", "")] = row.attribute_id
        
        # Step 3: Store each result value
        update_count = 0
        unmapped_keys = []
        
        for key, value in raw_result.items():
            key_lower = key.lower()
            ctx_id = None
            
            # Method 1: Direct match with attribute ID
            if key_lower in attr_to_ctx:
                ctx_id = attr_to_ctx[key_lower]
            
            # Method 2: Match via display name mapping
            elif key_lower in display_to_ctx:
                ctx_id = display_to_ctx[key_lower]
            
            # Method 3: Resolve via column name → attribute ID → contextual ID
            elif key_lower in col_to_attr:
                attr_id = col_to_attr[key_lower]
                if attr_id.lower() in attr_to_ctx:
                    ctx_id = attr_to_ctx[attr_id.lower()]
            
            # Method 4: Try without any prefix
            elif key_lower.startswith(("at", "out")):
                base_key = key_lower[2:]  # Remove "at" or "out" prefix
                if base_key in attr_to_ctx:
                    ctx_id = attr_to_ctx[base_key]
            
            # If we still can't find it, try normalized keys
            if not ctx_id:
                # Remove all non-alphanumeric characters
                normalized_key = re.sub(r'\W+', '', key_lower)
                
                # Check if any normalized keys in our mappings match
                for k, v in display_to_ctx.items():
                    if re.sub(r'\W+', '', k) == normalized_key:
                        ctx_id = v
                        break
            
            # If we found a contextual ID, update the record
            if ctx_id:
                # Extract just the value if it's a dict with a 'value' key
                if isinstance(value, dict) and 'value' in value and len(value) == 1:
                    value = value['value']
                
                # Serialize the value
                serialized_value = serialize_value(value)
                
                update_query = """
                UPDATE workflow_runtime.lo_output_execution
                SET output_value = :val
                WHERE instance_id = :inst_id 
                AND lo_id = :lo_id 
                AND output_contextual_id = :ctx_id
                """
                
                db.execute(text(update_query), {
                    "val": serialized_value,
                    "inst_id": instance_id,
                    "lo_id": lo_id,
                    "ctx_id": ctx_id
                })
                
                update_count += 1
                print(f"✅ Updated output {key} → {ctx_id} = {value}")
            else:
                unmapped_keys.append(key)
        
        # Set special output for execution status if available
        exec_status_ctx = None
        for ctx in attr_to_ctx.values():
            if ctx.endswith('.out001'):
                exec_status_ctx = ctx
                break
        
        if exec_status_ctx:
            db.execute(text("""
                UPDATE workflow_runtime.lo_output_execution
                SET output_value = :val
                WHERE instance_id = :inst_id 
                AND lo_id = :lo_id 
                AND output_contextual_id = :ctx_id
            """), {
                "val": serialize_value("Success"),
                "inst_id": instance_id,
                "lo_id": lo_id,
                "ctx_id": exec_status_ctx
            })
            update_count += 1
            print(f"✅ Updated execution status to 'Success'")
        
        # Commit all updates in a single transaction
        db.commit()
        
        if unmapped_keys:
            print(f"⚠️ Warning: Could not map {len(unmapped_keys)} output keys: {unmapped_keys}")
        
        print(f"💾 Successfully stored {update_count} output values")
        
    except Exception as e:
        db.rollback()
        print(f"❌ ERROR storing outputs: {str(e)}")
        raise


def map_output_to_next_inputs(db: Session, instance_id: str, current_lo_id: str, go_id: str):
    """Map output values from the current LO to inputs of subsequent LOs."""
    print(f"\n🔄 Mapping outputs to next LO inputs for GO: {go_id}, Instance: {instance_id}")
    
    try:
        # First, validate that outputs exist to be mapped
        validation_query = """
        SELECT COUNT(*) 
        FROM workflow_runtime.lo_output_execution
        WHERE instance_id = :instance_id 
        AND lo_id = :lo_id
        AND output_value IS NOT NULL
        """
        
        output_count = db.execute(text(validation_query), {
            "instance_id": instance_id,
            "lo_id": current_lo_id
        }).scalar()
        
        print(f"🔍 Found {output_count} non-null output values to potentially map")
        
        if output_count == 0:
            print("⚠️ Warning: No output values to map - skipping data mapping")
            return
            
        # The key issue: we need to handle different contextual ID formats
        # lo_data_mappings stores: "lo001.out002" -> "lo002.in008"
        # lo_output_execution stores: "go001.lo001.out002"
        # lo_input_execution expects: "go001.lo002.in008"
        
        # Updated mapping query to handle the format differences
        mapping_query = """
        UPDATE workflow_runtime.lo_input_execution target
        SET input_value = source.output_value
        FROM workflow_runtime.lo_output_execution source
        JOIN workflow_runtime.lo_data_mappings map 
        ON source.output_contextual_id = :go_id || '.' || map.source
        WHERE target.input_contextual_id = :go_id || '.' || map.target
        AND source.instance_id = :instance_id
        AND target.instance_id = :instance_id
        AND source.lo_id = :current_lo_id
        AND target.lo_id != :current_lo_id
        AND source.output_value IS NOT NULL
        RETURNING target.input_contextual_id, source.output_contextual_id, source.output_value
        """
        
        result = db.execute(text(mapping_query), {
            "go_id": go_id,
            "instance_id": instance_id,
            "current_lo_id": current_lo_id
        }).fetchall()
        
        # Commit the mapping operation
        db.commit()
        
        if result:
            print(f"✅ Successfully mapped {len(result)} values:")
            for row in result:
                print(f"  • {row.output_contextual_id} → {row.input_contextual_id} = {row.output_value}")
        else:
            print("ℹ️ No mappings were applied between LOs")
            
        return len(result) if result else 0
        
    except Exception as e:
        db.rollback()
        print(f"❌ ERROR mapping output to inputs: {str(e)}")
        raise


def update_transaction(db: Session, instance_id: str, go_id: str, lo_id: str, tenant_id: str, user_id: str, status: str, input_combined_array: List[Dict[str, Any]]):
    #edited by ramakant on 21 april 2025
    query_terminal_pathway = """
          SELECT pathway_type
          FROM workflow_runtime.execution_pathways
          WHERE lo_id = :lo_id
            """
    pathway_result = db.execute(text(query_terminal_pathway), {"lo_id": lo_id}).fetchone()
    execution_status = "completed" if pathway_result and pathway_result[0].lower() == "terminal" else "pending"
    db.execute(text("""
        INSERT INTO workflow_transaction (workflow_instance_id, go_id, lo_id, tenant_id, user_id, status, input_stack, created_at, updated_at)
        VALUES (:i, :g, :l, :t, :u, :s, :stack, NOW(), NOW())
    """), {
        "i": instance_id, "g": go_id, "l": lo_id, "t": tenant_id,
        "u": user_id, "s": execution_status, "stack": json.dumps(input_combined_array)
    })
    db.commit()


def resolve_next_lo(db: Session, lo_id: str, input_data: Dict[str, Any]) -> Optional[str]:
    """Determine the next Local Objective based on conditional or sequential pathways."""
    print(f"\n🧭 Resolving next LO after {lo_id}")
    
    try:
        # First check if this is a terminal LO
        terminal_check = """
        SELECT pathway_type 
        FROM workflow_runtime.execution_pathways
        WHERE lo_id = :lo_id AND LOWER(pathway_type) = 'terminal'
        """
        
        is_terminal = db.execute(text(terminal_check), {"lo_id": lo_id}).fetchone()
        
        if is_terminal:
            print("🏁 Current LO is a terminal node - workflow will end")
            return None
        
        # Check for conditional pathways first
        alt_pathway_check = """
        SELECT 1 
        FROM workflow_runtime.execution_pathways
        WHERE lo_id = :lo_id AND LOWER(pathway_type) = 'alternate'
        """
        
        has_alt_pathways = db.execute(text(alt_pathway_check), {"lo_id": lo_id}).fetchone()
        
        if has_alt_pathways:
            print("🔀 Found alternate pathways - evaluating conditions")
            
            # Fetch and evaluate conditions
            cond_query = """
            SELECT condition_entity, condition_attribute, condition_operator, condition_value, next_lo
            FROM workflow_runtime.execution_pathway_conditions
            WHERE lo_id = :lo_id
            ORDER BY id ASC
            """
            
            conditions = db.execute(text(cond_query), {"lo_id": lo_id}).fetchall()
            
            for cond in conditions:
                entity = cond.condition_entity
                attribute = cond.condition_attribute.lower() if cond.condition_attribute else ""
                operator = cond.condition_operator.lower() if cond.condition_operator else ""
                expected = cond.condition_value
                next_lo = cond.next_lo
                
                # Get the actual value for comparison
                actual = None
                
                # Try different ways to find the attribute value
                key_variations = [
                    attribute,
                    f"{entity.lower()}.{attribute}",
                    attribute.lower(),
                    attribute.replace("_", "").lower(),
                    # Try with/without 'at' prefix
                    (attribute[2:] if attribute.startswith("at") else f"at{attribute}")
                ]
                
                for key in key_variations:
                    if key in input_data:
                        actual = input_data[key]
                        print(f"✅ Found value for '{attribute}' using key '{key}'")
                        break
                
                # Also check common display name keys like "Status" that might be in the input
                common_display_names = ["status", "state", "condition", "result"]
                for display_key in common_display_names:
                    if display_key in [k.lower() for k in input_data.keys()]:
                        matching_key = next((k for k in input_data.keys() if k.lower() == display_key), None)
                        if matching_key:
                            actual = input_data[matching_key]
                            print(f"✅ Found value from display name '{matching_key}'")
                            break
                
                if actual is None:
                    print(f"⚠️ No value found for condition attribute '{attribute}' - skipping condition")
                    continue
                
                print(f"🔍 Evaluating: {attribute} ({actual}) {operator} {expected}")
                
                # CASE INSENSITIVITY FIX: Convert string values to lowercase for comparison
                actual_str = str(actual).lower() if actual is not None else ""
                expected_str = str(expected).lower() if expected is not None else ""
                
                # Evaluate the condition
                match = False
                
                try:
                    if operator == "equals":
                        # Case-insensitive string comparison
                        match = actual_str == expected_str
                    elif operator == "not_equals":
                        match = actual_str != expected_str
                    elif operator == "greater_than":
                        match = float(actual) > float(expected)
                    elif operator == "less_than":
                        match = float(actual) < float(expected)
                    elif operator == "greater_than_or_equal":
                        match = float(actual) >= float(expected)
                    elif operator == "less_than_or_equal":
                        match = float(actual) <= float(expected)
                    elif operator == "in":
                        # Case-insensitive 'in' check
                        match = actual_str in [v.strip().lower() for v in expected_str.split(",")]
                    elif operator == "contains":
                        # Case-insensitive 'contains' check
                        match = expected_str in actual_str
                    else:
                        print(f"⚠️ Unsupported operator: {operator}")
                except (ValueError, TypeError) as e:
                    print(f"⚠️ Error comparing values: {str(e)}")
                    continue
                
                if match:
                    print(f"✅ Condition matched - next LO: {next_lo}")
                    return next_lo
            
            print("❌ No conditions matched")
        
        # Fall back to sequential pathway if no conditions matched
        seq_pathway_query = """
        SELECT next_lo
        FROM workflow_runtime.execution_pathways
        WHERE lo_id = :lo_id AND LOWER(pathway_type) = 'sequential'
        ORDER BY id ASC
        LIMIT 1
        """
        
        seq_result = db.execute(text(seq_pathway_query), {"lo_id": lo_id}).fetchone()
        
        if seq_result and seq_result.next_lo:
            print(f"➡️ Following sequential pathway to {seq_result.next_lo}")
            return seq_result.next_lo
        
        print("⚠️ No valid pathway found - workflow will end")
        return None
        
    except Exception as e:
        print(f"❌ ERROR resolving next LO: {str(e)}")
        # Return None to safely end the workflow rather than raising an exception
        return None

def serialize_value(value):
    """Safely serialize a value to JSON string."""
    if value is None:
        return None
    
    # Handle serialization of common data types
    if isinstance(value, (str, bytes, bytearray)):
        # Check if it's already a JSON string to avoid double serialization
        if is_valid_json(value):
            return value
    
    try:
        return json.dumps(convert_json_safe(value))
    except Exception as e:
        print(f"⚠️ WARNING: Error serializing value {type(value)}: {str(e)}")
        # Fallback: convert to string representation if JSON serialization fails
        return json.dumps(str(value))

def deserialize_value(value):
    """Safely deserialize a JSON string to Python object."""
    if value is None:
        return None
    
    if not isinstance(value, (str, bytes, bytearray)):
        return value  # Already a Python object
    
    try:
        return json.loads(value)
    except json.JSONDecodeError:
        # Not valid JSON, return as is
        return value

def is_valid_json(value):
    """Check if a string is valid JSON."""
    if not isinstance(value, (str, bytes, bytearray)):
        return False
    
    try:
        json.loads(value)
        return True
    except json.JSONDecodeError:
        return False

@router.post("/instances/{instance_id}/execute", response_model=Dict[str, Any])
async def execute_local_objective(
    instance_id: str = Path(..., description="Workflow Instance ID"),
    input_data: Dict[str, Any] = Body({}, description="User-provided Input Data"),
    user_id: str = Body(..., description="User ID executing the LO"),
    db: Session = Depends(get_db),
    security_context: SecurityContext = Depends(require_auth)
):
    """
    Execute the current Local Objective in a workflow instance.
    Now supports four input types:
    - User: Manual inputs from users
    - Information: Display-only fields (excluded from execution)
    - System: Auto-populated via nested functions
    - System Dependent: Values calculated based on other inputs
    """
    try:
        print(f"\n==== BEGIN EXECUTE API FOR INSTANCE {instance_id} ====")
        
        # ✅ Step 1: Get instance, validate status
        instance, current_lo_id, go_id, tenant_id = fetch_workflow_instance(db, instance_id)
        
        # ✅ Create parameter resolver
        resolver = ParameterResolver(db_session=db)
        
        # ✅ Step 2: Get input fields by type to know which to process
        input_fields = fetch_all_input_fields(db, current_lo_id)
        input_map, dependency_map, user_inputs, system_inputs, info_inputs, dependent_inputs = categorize_inputs(input_fields)
        
        # ✅ Step 3: Filter input_data to exclude information-only fields
        filtered_input_data = resolver.filter_inputs_by_type(
            input_data,
            types_to_exclude={"information"}
        )
        
        print(f"📋 Filtered out {len(input_data) - len(filtered_input_data)} information-only fields")
        
        # ✅ Step 4: Resolve all input data into final_input_data
        final_input_data, attr_map, input_combined_array = resolve_inputs(
            db=db,
            instance_id=instance_id,
            lo_id=current_lo_id,
            input_data=filtered_input_data
        )

        # ✅ Step 5: Run all nested functions (e.g., validations, calculations)
        final_input_data = run_nested_functions(
            db=db,
            lo_id=current_lo_id,
            instance_id=instance_id,
            input_data=final_input_data
        )

        # ✅ Step 6: Update lo_input_execution with values
        update_input_execution(
            db=db,
            instance_id=instance_id,
            lo_id=current_lo_id,
            input_data=final_input_data
        )

        # ✅ Step 7: Resolve function type, convert input
        entity_id, function_name, converted_input, display_map = resolve_function_io(
            db=db,
            lo_id=current_lo_id,
            input_data=final_input_data
        )

        # ✅ Step 8: Execute system function (create, update, validate...)
        raw_result, execution_result = execute_system_function(
            db=db,
            function_name=function_name,
            entity_id=entity_id,
            input_data=converted_input,
            lo_id=current_lo_id
        )

        # ✅ Step 9: Store output values
        store_outputs(
            db=db,
            instance_id=instance_id,
            lo_id=current_lo_id,
            entity_id=entity_id,
            raw_result=raw_result
        )

        # ✅ Step 10: Map to next LO input execution
        map_output_to_next_inputs(
            db=db,
            instance_id=instance_id,
            current_lo_id=current_lo_id,
            go_id=go_id
        )

        # ✅ Step 11: Insert transaction log
        update_transaction(
            db=db,
            instance_id=instance_id,
            go_id=go_id,
            lo_id=current_lo_id,
            tenant_id=tenant_id,
            user_id=user_id,
            status='completed',
            input_combined_array=input_combined_array
        )

        # ✅ Step 12: Compute and set next LO
        next_lo_id = resolve_next_lo(
            db=db,
            lo_id=current_lo_id,
            input_data=final_input_data
        )

        if next_lo_id:
            db.execute(text("""
                UPDATE workflow_runtime.workflow_instances
                SET current_lo_id = :next_lo_id, updated_at = NOW()
                WHERE instance_id = :instance_id
            """), {"next_lo_id": next_lo_id, "instance_id": instance_id})
            db.commit()
            
        print(f"==== END EXECUTE API FOR INSTANCE {instance_id} ====\n")

        return {
            "status": "Completed",
            "message": f"Local Objective {current_lo_id} executed successfully",
            "output": convert_json_safe({
                v["display_name"]: v["value"] for v in execution_result.values()
            }),
            "next_lo_id": next_lo_id
        }

    except Exception as e:
        print(f"❌ ERROR in execute_local_objective: {str(e)}")
        traceback.print_exc()
        db.rollback()
        raise HTTPException(status_code=500, detail=f"Error executing local objective: {str(e)}")

@router.post("/instances/{instance_id}/pause", response_model=WorkflowInstance)
async def pause_workflow_instance(
    instance_id: str = Path(..., description="Workflow Instance ID"),
    user_id: str = Body(..., description="User ID pausing the workflow"),
    security_context: SecurityContext = Depends(require_auth)
):
    """
    Pause a workflow instance.
    """
    try:
        # Initialize system functions

            # Update the workflow instance
            query = """
            UPDATE workflow_runtime.workflow_instances
            SET status = %s
            WHERE instance_id = %s AND status = %s
            RETURNING *
            """
            params = (WorkflowInstanceStatus.PAUSED.value, instance_id, WorkflowInstanceStatus.ACTIVE.value)

            #result = sf._execute_pg_query(query, params)
            result = db.execute(text(query), params).fetchone()

            if not result:
                raise HTTPException(status_code=404, detail=f"Workflow instance {instance_id} not found or not active")

            # Create workflow instance object
            instance = WorkflowInstance(
                instance_id=result[0]['instance_id'],
                go_id=result[0]['go_id'],
                tenant_id=result[0]['tenant_id'],
                status=result[0]['status'],
                started_by=result[0]['started_by'],
                started_at=result[0]['started_at'],
                current_lo_id=result[0]['current_lo_id'],
                instance_data=result[0]['instance_data'],
                is_test=result[0]['is_test'],
                version=result[0]['version']
            )

            return instance
    except HTTPException as e:
        raise e
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error pausing workflow instance: {str(e)}")

@router.post("/instances/{instance_id}/resume", response_model=WorkflowInstance)
async def resume_workflow_instance(
    instance_id: str = Path(..., description="Workflow Instance ID"),
    user_id: str = Body(..., description="User ID resuming the workflow"),
    security_context: SecurityContext = Depends(require_auth)
):
    """
    Resume a paused workflow instance.
    """
    try:
        # Initialize system functions
        #with SystemFunctions(instance_id=instance_id, user_id=user_id) as sf:
            # Update the workflow instance
            query = """
            UPDATE workflow_runtime.workflow_instances
            SET status = %s
            WHERE instance_id = %s AND status = %s
            RETURNING *
            """
            params = (WorkflowInstanceStatus.ACTIVE.value, instance_id, WorkflowInstanceStatus.PAUSED.value)

            #result = sf._execute_pg_query(query, params)
            result = db.execute(text(query), params).fetchone()


            if not result:
                raise HTTPException(status_code=404, detail=f"Workflow instance {instance_id} not found or not paused")

            # Create workflow instance object
            instance = WorkflowInstance(
                instance_id=result[0]['instance_id'],
                go_id=result[0]['go_id'],
                tenant_id=result[0]['tenant_id'],
                status=result[0]['status'],
                started_by=result[0]['started_by'],
                started_at=result[0]['started_at'],
                current_lo_id=result[0]['current_lo_id'],
                instance_data=result[0]['instance_data'],
                is_test=result[0]['is_test'],
                version=result[0]['version']
            )

            return instance
    except HTTPException as e:
        raise e
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error resuming workflow instance: {str(e)}")
    



########################## INPUT STACK FUNCTIONS ############################

# Helper functions for the get_local_objective_inputs endpoint

def fetch_workflow_instance_for_inputs(db: Session, instance_id: str) -> Tuple[Dict, str]:
    """
    Fetch workflow instance data and validate it's in ACTIVE status.
    
    Args:
        db: Database session
        instance_id: Workflow instance ID
        
    Returns:
        Tuple containing instance data and current local objective ID
    """
    print(f"\n📋 Fetching workflow instance {instance_id} for inputs")
    
    query = "SELECT * FROM workflow_runtime.workflow_instances WHERE instance_id = :instance_id"
    result = db.execute(text(query), {"instance_id": instance_id}).fetchone()
    
    if not result:
        print(f"❌ Workflow instance {instance_id} not found")
        raise HTTPException(status_code=404, detail=f"Workflow instance {instance_id} not found")

    result_dict = dict(result._mapping)
    
    if result_dict["status"] != WorkflowInstanceStatus.ACTIVE.value:
        print(f"❌ Workflow instance {instance_id} is not active (status: {result_dict['status']})")
        raise HTTPException(status_code=400, detail=f"Workflow instance {instance_id} is not active")

    current_lo_id = result_dict["current_lo_id"]
    
    if not current_lo_id:
        print(f"❌ No current Local Objective found for workflow instance {instance_id}")
        raise HTTPException(status_code=400, detail=f"No current Local Objective found for workflow instance {instance_id}")
    
    print(f"✅ Found active instance with current LO: {current_lo_id}")
    
    return result_dict, current_lo_id


def fetch_all_input_fields(db: Session, current_lo_id: str) -> List[Dict[str, Any]]:
    """
    Fetch all input fields for a local objective with enhanced metadata.
    
    Args:
        db: Database session
        current_lo_id: Current local objective ID
        
    Returns:
        List of input field dictionaries
    """
    print(f"\n📋 Fetching all input fields for LO: {current_lo_id}")
    
    try:
        query_inputs = text("""
        SELECT DISTINCT ON (i.slot_id)
            i.id,
            i.contextual_id,
            COALESCE(a.display_name, SPLIT_PART(i.slot_id, '.', 2)) AS display_name,
            LOWER(SPLIT_PART(i.slot_id, '.', 2)) AS attribute_id,
            LOWER(SPLIT_PART(i.slot_id, '.', 1)) AS entity_id,
            a.datatype AS data_type,
            i.source_type::text,
            i.required,
            i.ui_control,
            i.dependencies,
            i.dependency_type,
            i.lookup_function,
            i.is_visible,
            i.metadata,
            i.input_stack_id,
            (
                SELECT json_agg(ev.value)
                FROM workflow_runtime.attribute_enum_values ev
                WHERE ev.attribute_id = LOWER(SPLIT_PART(i.slot_id, '.', 2))
            ) AS allowed_values,
            (
                SELECT json_agg(json_build_object(
                    'rule', v.rule,
                    'expression', v.expression
                ))
                FROM workflow_runtime.attribute_validations v
                WHERE v.attribute_id = LOWER(SPLIT_PART(i.slot_id, '.', 2))
            ) AS validations,
            (
                SELECT EXISTS (
                    SELECT 1 FROM workflow_runtime.dropdown_data_sources ds
                    WHERE ds.input_item_id = i.id AND ds.input_stack_id = i.input_stack_id
                )
            ) AS has_dropdown_source
        FROM workflow_runtime.lo_input_items i
        LEFT JOIN workflow_runtime.entity_attributes a
        ON LOWER(SPLIT_PART(i.slot_id, '.', 2)) = LOWER(a.attribute_id)
        WHERE i.lo_id = :lo_id
        """)

        input_results = db.execute(query_inputs, {"lo_id": current_lo_id}).fetchall()
        print(f"✅ Fetched {len(input_results)} input fields")
        
        # Process results
        inputs = []
        for row in input_results:
            # Convert row to dictionary
            input_item = {
                "input_id": row.id,
                "input_stack_id": row.input_stack_id,
                "attribute_id": row.attribute_id,
                "entity_id": row.entity_id,
                "display_name": row.display_name,
                "data_type": row.data_type,
                "source_type": row.source_type,
                "required": row.required,
                "ui_control": row.ui_control or "oj-input-text",
                "is_visible": row.is_visible,
                "allowed_values": row.allowed_values,
                "validations": row.validations,
                "contextual_id": row.contextual_id,
                "input_value": None,  # Will be populated later
                "has_dropdown_source": row.has_dropdown_source,
                "dependencies": row.dependencies,
                "dependency_type": row.dependency_type,
                "metadata": row.metadata
            }
            inputs.append(input_item)
            
        return inputs
        
    except Exception as e:
        print(f"❌ Error in fetch_all_input_fields: {str(e)}")
        traceback.print_exc()
        raise


def categorize_inputs(input_fields: List[Dict[str, Any]]) -> Tuple[Dict[str, Any], Dict[str, Any], List[Dict], List[Dict], List[Dict], List[Dict]]:
    """
    Categorize input fields by type and create lookup maps.
    
    Args:
        input_fields: List of input field dictionaries
        
    Returns:
        Tuple containing:
            - input_map: Dictionary mapping input IDs to input dictionaries
            - dependency_map: Dictionary mapping input IDs to their dependencies
            - user_inputs: List of user input dictionaries
            - system_inputs: List of system input dictionaries
            - info_inputs: List of information input dictionaries
            - dependent_inputs: List of dependent input dictionaries
    """
    print(f"\n📋 Categorizing {len(input_fields)} input fields by type")
    
    user_inputs = []
    system_inputs = []
    info_inputs = []
    dependent_inputs = []
    
    # Maps for lookups
    input_map = {}
    dependency_map = {}
    
    for item in input_fields:
        # Add to lookup map
        input_map[item["input_id"]] = item
        
        # Extract dependencies
        if item.get("dependencies"):
            try:
                deps = item["dependencies"]
                if isinstance(deps, str):
                    deps = json.loads(deps)
                dependency_map[item["input_id"]] = deps
                print(f"📍 Found dependencies for input {item['input_id']}: {deps}")
            except Exception as e:
                print(f"⚠️ Error parsing dependencies for {item['input_id']}: {str(e)}")
        
        # Categorize by source type
        source_type = item.get("source_type", "").lower()
        if source_type == 'user':
            user_inputs.append(item)
        elif source_type == 'information':
            info_inputs.append(item)
        elif source_type == 'system_dependent':
            dependent_inputs.append(item)
        elif source_type == 'system':
            system_inputs.append(item)
        else:
            # Fallback for legacy data
            if source_type == 'user':
                user_inputs.append(item)
            else:
                system_inputs.append(item)
    
    print(f"✅ Categorized inputs: {len(user_inputs)} user, {len(system_inputs)} system, " +
          f"{len(info_inputs)} information, {len(dependent_inputs)} dependent")
    
    return input_map, dependency_map, user_inputs, system_inputs, info_inputs, dependent_inputs


def load_dropdown_options(db: Session, input_map: Dict[str, Any], parent_values: Dict[str, Any] = None) -> Dict[str, Any]:
    """
    Load dropdown options for fields that have dropdown sources.
    
    Args:
        db: Database session
        input_map: Dictionary mapping input IDs to input dictionaries
        parent_values: Optional dictionary of parent values provided by the user for dependent dropdowns
        
    Returns:
        Updated input_map with dropdown options added
    """
    # Create cache key for dropdowns to improve performance
    cache_key = "dropdown_cache"
    dropdown_cache = getattr(db, cache_key, {})
    
    dropdown_items = [item for item in input_map.values() if item.get("has_dropdown_source")]
    if not dropdown_items:
        print("ℹ️ No dropdown sources to load")
        return input_map
    
    print(f"\n📋 Loading dropdown options for {len(dropdown_items)} inputs")
    
    for input_item in dropdown_items:
        input_id = input_item["input_id"]
        
        # Check cache first
        cache_item_key = f"{input_id}"
        if parent_values:
            # Include parent values in cache key if they exist
            parent_values_str = "_".join(f"{k}={v}" for k, v in sorted(parent_values.items()))
            cache_item_key = f"{input_id}_{parent_values_str}"
            
        if cache_item_key in dropdown_cache:
            print(f"  📌 Using cached dropdown options for {input_id}")
            input_map[input_id]["dropdown_options"] = dropdown_cache[cache_item_key]
            continue
            
        # Fetch dropdown source configuration
        dropdown_query = text("""
        SELECT 
            ds.input_item_id,
            ds.source_type,
            ds.query_text,
            ds.function_name,
            ds.function_params,
            ds.value_field,
            ds.display_field,
            ds.depends_on_fields
        FROM workflow_runtime.dropdown_data_sources ds
        WHERE ds.input_item_id = :input_id
        """)
        
        dropdown_result = db.execute(dropdown_query, {"input_id": input_id}).fetchone()
        
        if not dropdown_result:
            print(f"  ⚠️ No dropdown source found for {input_id}")
            continue
            
        # Process parent values
        param_values = {}
        
        # Handle depends_on_fields properly regardless of its type
        depends_on = []
        if dropdown_result.depends_on_fields:
            try:
                # If already a list, use it directly
                if isinstance(dropdown_result.depends_on_fields, list):
                    depends_on = dropdown_result.depends_on_fields
                    print(f"  ℹ️ Using depends_on_fields as list: {depends_on}")
                # If a string, try to parse it
                elif isinstance(dropdown_result.depends_on_fields, str):
                    if dropdown_result.depends_on_fields.strip().startswith('['):
                        # Looks like a JSON array
                        depends_on = json.loads(dropdown_result.depends_on_fields)
                        print(f"  ℹ️ Parsed depends_on_fields from JSON string: {depends_on}")
                    else:
                        # Might be a single value
                        depends_on = [dropdown_result.depends_on_fields]
                        print(f"  ℹ️ Using depends_on_fields as single value: {depends_on}")
                # Handle PostgreSQL JSONB type
                elif hasattr(dropdown_result.depends_on_fields, 'items'):
                    depends_on = list(dropdown_result.depends_on_fields)
                    print(f"  ℹ️ Extracted depends_on_fields from JSONB: {depends_on}")
                else:
                    # Unexpected type, log details and default to empty list
                    print(f"  ⚠️ Unexpected type for depends_on_fields: {type(dropdown_result.depends_on_fields)}")
                    print(f"  ⚠️ Value: {dropdown_result.depends_on_fields}")
                    depends_on = []
            except json.JSONDecodeError as e:
                # Handle case where it might be a malformed JSON string
                print(f"  ⚠️ Error parsing depends_on_fields: {dropdown_result.depends_on_fields}, error: {e}")
                # Try to extract values using regex as a fallback
                import re
                matches = re.findall(r'["\']([^"\']+)["\']', dropdown_result.depends_on_fields)
                if matches:
                    depends_on = matches
                    print(f"  ℹ️ Extracted values using regex: {depends_on}")
                else:
                    depends_on = []
        
        print(f"  📌 Final depends_on fields: {depends_on}")
        
        # Check if we have all required parent values
        missing_parents = []
        for dep_id in depends_on:
            if parent_values and dep_id in parent_values:
                param_values[dep_id] = parent_values[dep_id]
                print(f"  ✅ Using parent value for {dep_id}: {parent_values[dep_id]}")
            elif parent_values and dep_id.lower() in {k.lower() for k in parent_values.keys()}:
                # Try case-insensitive match
                match_key = next(k for k in parent_values.keys() if k.lower() == dep_id.lower())
                param_values[dep_id] = parent_values[match_key]
                print(f"  ✅ Using parent value for {dep_id} with case-insensitive match: {parent_values[match_key]}")
            elif dep_id in input_map and input_map[dep_id].get("input_value") is not None:
                param_values[dep_id] = input_map[dep_id]["input_value"]
                print(f"  ✅ Using existing value for {dep_id}: {input_map[dep_id]['input_value']}")
            else:
                # Also try checking input map with case-insensitive keys
                found = False
                for key, item in input_map.items():
                    if key.lower() == dep_id.lower() and item.get("input_value") is not None:
                        param_values[dep_id] = item["input_value"]
                        print(f"  ✅ Using existing value for {dep_id} with case-insensitive match: {item['input_value']}")
                        found = True
                        break
                
                if not found:
                    missing_parents.append(dep_id)
                
        if missing_parents:
            print(f"  ⚠️ Missing parent values for {input_id}: {missing_parents}")
            input_map[input_id]["needs_parent_value"] = True
            input_map[input_id]["parent_ids"] = missing_parents
            input_map[input_id]["dropdown_options"] = []
            continue
            
        # Load dropdown options based on source type
        dropdown_options = []
        
        if dropdown_result.source_type == 'database' and dropdown_result.query_text:
            try:
                # Safely replace parameters in query with proper SQL injection prevention
                query_text = dropdown_result.query_text
                query_params = {}
                
                for param_id, param_value in param_values.items():
                    # Use parameterized queries instead of string replacement
                    placeholder = f":{param_id}"
                    if placeholder in query_text:
                        query_params[param_id] = param_value
                    
                print(f"  🔍 Executing query with params: {query_params}")
                
                # Execute query with proper parameter binding
                dropdown_data = db.execute(text(query_text), query_params).fetchall()
                
                # Process results
                for data_row in dropdown_data:
                    value_field = dropdown_result.value_field
                    display_field = dropdown_result.display_field
                    
                    if hasattr(data_row, '_mapping'):
                        value = data_row._mapping.get(value_field)
                        display = data_row._mapping.get(display_field, value)
                    else:
                        try:
                            value = getattr(data_row, value_field)
                            display = getattr(data_row, display_field, value)
                        except:
                            value = data_row[0] if len(data_row) > 0 else None
                            display = data_row[1] if len(data_row) > 1 else value
                            
                    if value is not None:
                        dropdown_options.append({
                            "value": value,
                            "label": display or value
                        })
                        
                print(f"  ✅ Loaded {len(dropdown_options)} options from database")
                
            except Exception as e:
                print(f"  ❌ Error executing dropdown query: {str(e)}")
                import traceback
                traceback.print_exc()
                dropdown_options = []
                
        elif dropdown_result.source_type == 'function' and dropdown_result.function_name:
            try:
                # Import function repository
                from app.services.function_repository import function_repository
                
                # Skip operation functions
                if dropdown_result.function_name.lower() in ['create', 'update', 'delete']:
                    print(f"  ⚠️ Skipping operation function: {dropdown_result.function_name}")
                    dropdown_options = []
                    continue
                    
                # Prepare function parameters
                function_params = {}
                if dropdown_result.function_params:
                    if isinstance(dropdown_result.function_params, str):
                        try:
                            function_params = json.loads(dropdown_result.function_params)
                        except json.JSONDecodeError:
                            print(f"  ⚠️ Error parsing function_params: {dropdown_result.function_params}")
                            function_params = {}
                    elif isinstance(dropdown_result.function_params, dict):
                        function_params = dropdown_result.function_params
                    else:
                        print(f"  ⚠️ Unexpected function_params type: {type(dropdown_result.function_params)}")
                        function_params = {}
                
                # FIX: Don't merge params directly - instead resolve template variables
                merged_params = dict(function_params)  # Start with a copy of function_params
                
                # Resolve template variables in parameters
                for param_key, param_value in list(merged_params.items()):
                    if isinstance(param_value, str) and param_value.startswith("${") and param_value.endswith("}"):
                        var_name = param_value[2:-1]  # Extract variable name from ${var_name}
                        if var_name in param_values:
                            merged_params[param_key] = param_values[var_name]
                            print(f"  ✅ Resolved template variable in {param_key}: {param_value} → {param_values[var_name]}")
                        else:
                            print(f"  ⚠️ Could not resolve template variable {param_value} - variable not found")
                
                print(f"  🔍 Executing function: {dropdown_result.function_name} with params: {merged_params}")
                
                # Execute the function
                result = function_repository.auto_execute(dropdown_result.function_name, db, **merged_params)
                
                print(f"  📊 Function result: {result}")
                
                # Process the result into dropdown options
                if isinstance(result, list):
                    for item in result:
                        if isinstance(item, dict):
                            value = item.get(dropdown_result.value_field)
                            display = item.get(dropdown_result.display_field, value)
                            if value is not None:
                                dropdown_options.append({
                                    "value": value,
                                    "label": display
                                })
                elif isinstance(result, dict):
                    for key, item in result.items():
                        if isinstance(item, dict):
                            value = item.get(dropdown_result.value_field, key)
                            display = item.get(dropdown_result.display_field, value)
                        else:
                            value = key
                            display = item
                            
                        dropdown_options.append({
                            "value": value,
                            "label": display
                        })
                        
                print(f"  ✅ Loaded {len(dropdown_options)} options from function")
                
            except Exception as e:
                print(f"  ❌ Error executing dropdown function: {str(e)}")
                import traceback
                traceback.print_exc()
                dropdown_options = []
        
        # Store in input map
        input_map[input_id]["dropdown_options"] = dropdown_options
        
        # Cache the result
        dropdown_cache[cache_item_key] = dropdown_options
    
    # Update the cache on the session
    setattr(db, cache_key, dropdown_cache)
    
    return input_map


def load_current_input_values(db: Session, instance_id: str, current_lo_id: str, input_map: Dict[str, Any]) -> Dict[str, Any]:
    """
    Load current values for inputs from the database.
    
    Args:
        db: Database session
        instance_id: Workflow instance ID
        current_lo_id: Current local objective ID
        input_map: Dictionary mapping input IDs to input dictionaries
        
    Returns:
        Updated input_map with current values filled in
    """
    print(f"\n📋 Loading current input values for instance {instance_id}, LO {current_lo_id}")
    
    try:
        # Query to get input values from the lo_input_execution table
        values_query = text("""
        SELECT input_contextual_id, input_value
        FROM workflow_runtime.lo_input_execution
        WHERE instance_id = :instance_id AND lo_id = :lo_id
        """)
        
        value_results = db.execute(
            values_query, 
            {"instance_id": instance_id, "lo_id": current_lo_id}
        ).fetchall()
        
        print(f"✅ Found {len(value_results)} stored input values")
        
        # Create a map of contextual_id -> value
        value_map = {}
        for row in value_results:
            if row.input_value:
                try:
                    # Parse the value if it's JSON
                    value = json.loads(row.input_value) if isinstance(row.input_value, str) else row.input_value
                except Exception as e:
                    print(f"  ⚠️ Error parsing JSON value: {str(e)}")
                    value = row.input_value
                    
                value_map[row.input_contextual_id] = value
        
        # Update input values in the input_map
        updated_count = 0
        for input_id, input_item in input_map.items():
            contextual_id = input_item.get("contextual_id")
            if contextual_id and contextual_id in value_map:
                input_item["input_value"] = value_map[contextual_id]
                updated_count += 1
                print(f"  ✅ Loaded value for {contextual_id}: {value_map[contextual_id]}")
        
        print(f"✅ Updated {updated_count} input values in input map")
        
        return input_map
    except Exception as e:
        print(f"❌ Error in load_current_input_values: {str(e)}")
        traceback.print_exc()
        return input_map


def execute_nested_functions(
    db: Session, 
    current_lo_id: str, 
    input_map: Dict[str, Any], 
    system_inputs: List[Dict],
    exclude_function_types: List[str] = None
) -> Dict[str, Any]:
    """
    Execute nested functions for system inputs.
    
    Args:
        db: Database session
        current_lo_id: Current local objective ID
        input_map: Dictionary mapping input IDs to input dictionaries
        system_inputs: List of system input dictionaries
        exclude_function_types: List of function types to exclude
        
    Returns:
        Updated input_map with nested function results
    """
    if not system_inputs:
        print("ℹ️ No system inputs to process")
        return input_map
    
    if exclude_function_types is None:
        exclude_function_types = ['create', 'update', 'delete', 'insert', 'remove']
    
    print(f"\n📋 Executing nested functions for {len(system_inputs)} system inputs")
    print(f"  ℹ️ Excluding function types: {exclude_function_types}")
    
    # First build context with all current input values
    execution_context = {}
    for item in input_map.values():
        if item.get("input_value") is not None:
            execution_context[item["attribute_id"]] = item["input_value"]
            if item.get("input_id"):
                execution_context[item["input_id"]] = item["input_value"]
    
    print(f"📊 Built execution context with {len(execution_context)} values")
    
    # Process each system input
    for input_item in system_inputs:
        # Skip if already has a value
        if input_item.get("input_value") is not None:
            print(f"  ℹ️ Skipping {input_item['input_id']} - already has value")
            continue
            
        print(f"  🔍 Processing system input: {input_item['input_id']} ({input_item['display_name']})")
        
        # Check for nested function execution
        nested_function_query = text("""
        SELECT function_name, parameters, output_to, function_type
        FROM workflow_runtime.lo_nested_functions
        WHERE lo_id = :lo_id AND input_contextual_id = :contextual_id
        """)
        
        nested_funcs = db.execute(
            nested_function_query,
            {"lo_id": current_lo_id, "contextual_id": input_item["contextual_id"]}
        ).fetchall()
        
        if not nested_funcs:
            print(f"  ⚠️ No nested functions found for {input_item['input_id']}")
            continue
        
        # Execute nested functions
        for func in nested_funcs:
            func_name = func.function_name
            function_type = func.function_type.lower() if func.function_type else ""
            parameters = json.loads(func.parameters) if isinstance(func.parameters, str) else func.parameters or {}
            output_to = func.output_to
            
            # Skip excluded function types
            if function_type in exclude_function_types or func_name.lower() in exclude_function_types:
                print(f"  ⚠️ Skipping excluded function type: {function_type} / {func_name}")
                continue
                
            print(f"  ⚙️ Executing function: {func_name} (type: {function_type})")
            print(f"    Parameters before resolution: {parameters}")
            
            try:
                # Resolve parameters with current context
                for param_key, param_value in list(parameters.items()):
                    if isinstance(param_value, str) and param_value.startswith("${") and param_value.endswith("}"):
                        context_key = param_value[2:-1]
                        if context_key in execution_context:
                            parameters[param_key] = execution_context[context_key]
                            print(f"    ✅ Resolved {param_key}: ${{{context_key}}} → {parameters[param_key]}")
                        elif context_key.lower() in execution_context:
                            parameters[param_key] = execution_context[context_key.lower()]
                            print(f"    ✅ Resolved {param_key}: ${{{context_key}}} → {parameters[param_key]} (lowercase match)")
                        else:
                            print(f"    ⚠️ Could not resolve context key: {context_key}")
                    elif isinstance(param_value, str) and param_value in execution_context:
                        parameters[param_key] = execution_context[param_value]
                        print(f"    ✅ Resolved direct reference {param_key}: {param_value} → {parameters[param_key]}")
                
                print(f"    Parameters after resolution: {parameters}")
                
                # Execute function
                result = function_repository.auto_execute(func_name, db, **parameters)
                print(f"    ✅ Function result: {result}")
                
                # Store result
                if output_to:
                    input_item["input_value"] = result
                    execution_context[output_to] = result
                    input_map[input_item["input_id"]]["input_value"] = result
                    print(f"    ✅ Stored result in {output_to}: {result}")
            except Exception as e:
                print(f"    ❌ Error executing nested function {func_name}: {str(e)}")
                traceback.print_exc()
    
    return input_map


def resolve_dependent_inputs(
    db: Session, 
    current_lo_id: str, 
    input_map: Dict[str, Any], 
    dependency_map: Dict[str, Any], 
    dependent_inputs: List[Dict],
    parent_values: Dict[str, Any] = None,
    specific_input_id: str = None,
    exclude_function_types: List[str] = None
) -> Dict[str, Any]:
    """
    Resolve dependent input values based on their dependencies.
    
    Args:
        db: Database session
        current_lo_id: Current local objective ID
        input_map: Dictionary mapping input IDs to input dictionaries
        dependency_map: Dictionary mapping input IDs to their dependencies
        dependent_inputs: List of dependent input dictionaries
        parent_values: Optional dictionary of parent values provided by the user
        specific_input_id: Optional specific input ID to resolve (for direct dependent calls)
        exclude_function_types: List of function types to exclude
        
    Returns:
        Updated input_map with dependent values calculated
    """
    if not dependent_inputs:
        print("ℹ️ No dependent inputs to process")
        return input_map
    
    if exclude_function_types is None:
        exclude_function_types = ['create', 'update', 'delete', 'insert', 'remove']
    
    print(f"\n📋 Resolving dependent inputs")
    print(f"  ℹ️ Specific input requested: {specific_input_id}" if specific_input_id else "  ℹ️ Processing all dependent inputs")
    print(f"  ℹ️ Parent values provided: {parent_values}" if parent_values else "  ℹ️ No parent values explicitly provided")
    
    # If specific input is requested, filter the list
    if specific_input_id:
        dependent_inputs = [item for item in dependent_inputs if item["input_id"] == specific_input_id]
        if not dependent_inputs:
            print(f"  ⚠️ Specific dependent input {specific_input_id} not found")
            return input_map
    
    for input_item in dependent_inputs:
        input_id = input_item["input_id"]
        print(f"  🔍 Processing dependent input: {input_id} ({input_item['display_name']})")
        
        # Check if dependencies are satisfied
        dependencies_satisfied = True
        required_values = {}
        missing_dependencies = []
        
        if input_id in dependency_map:
            dependencies = dependency_map[input_id]
            if isinstance(dependencies, str):
                try:
                    dependencies = json.loads(dependencies)
                except:
                    print(f"  ⚠️ Failed to parse dependencies JSON: {dependencies}")
                    dependencies = []
            
            print(f"  🔗 Dependencies: {dependencies}")
            
            for dep_id in dependencies:
                # First check parent_values
                if parent_values and dep_id in parent_values:
                    required_values[dep_id] = parent_values[dep_id]
                    print(f"  ✅ Found required value from parent_values for {dep_id}: {parent_values[dep_id]}")
                # Then check input_map
                elif dep_id in input_map:
                    dep_item = input_map[dep_id]
                    if dep_item.get("input_value") is not None:
                        required_values[dep_id] = dep_item["input_value"]
                        print(f"  ✅ Found required value from input_map for {dep_id}: {dep_item['input_value']}")
                    else:
                        dependencies_satisfied = False
                        missing_dependencies.append(dep_id)
                        print(f"  ⚠️ Missing required value from {dep_id}")
                else:
                    dependencies_satisfied = False
                    missing_dependencies.append(dep_id)
                    print(f"  ⚠️ Dependency {dep_id} not found in input_map")
        
        if dependencies_satisfied:
            print(f"  ✅ All dependencies satisfied")
            
            # Execute nested function if available
            nested_function_query = text("""
            SELECT function_name, parameters, output_to, function_type
            FROM workflow_runtime.lo_nested_functions
            WHERE lo_id = :lo_id AND input_contextual_id = :contextual_id
            """)
            
            nested_funcs = db.execute(
                nested_function_query,
                {"lo_id": current_lo_id, "contextual_id": input_item["contextual_id"]}
            ).fetchall()
            
            if not nested_funcs:
                print(f"  ⚠️ No nested functions found for dependent input {input_id}")
                continue
            
            for func in nested_funcs:
                func_name = func.function_name
                function_type = func.function_type.lower() if func.function_type else ""
                parameters = json.loads(func.parameters) if isinstance(func.parameters, str) else func.parameters or {}
                output_to = func.output_to
                
                # Skip excluded function types
                if function_type in exclude_function_types or func_name.lower() in exclude_function_types:
                    print(f"  ⚠️ Skipping excluded function type: {function_type} / {func_name}")
                    continue
                
                print(f"  ⚙️ Executing dependent function: {func_name} (type: {function_type})")
                print(f"    Raw parameters: {parameters}")
                
                # Add dependency values to parameters
                for dep_id, dep_value in required_values.items():
                    # If the parameter is a direct reference to a dependency
                    for param_key, param_value in list(parameters.items()):
                        if param_value == dep_id or (isinstance(param_value, str) and 
                                                   param_value.startswith("${") and 
                                                   param_value.endswith("}") and
                                                   param_value[2:-1] == dep_id):
                            parameters[param_key] = dep_value
                            print(f"    ✅ Set parameter {param_key} = {dep_value} (from {dep_id})")
                
                # Also add dependencies directly for convenience
                for dep_id, dep_value in required_values.items():
                    if dep_id not in parameters:
                        parameters[dep_id] = dep_value
                        print(f"    ✅ Added direct dependency parameter {dep_id} = {dep_value}")
                
                try:
                    print(f"    Final parameters: {parameters}")
                    
                    # Execute function
                    result = function_repository.auto_execute(func_name, db, **parameters)
                    print(f"    ✅ Function result: {result}")
                    
                    # Store result
                    if output_to:
                        input_item["input_value"] = result
                        input_map[input_id]["input_value"] = result
                        print(f"    ✅ Stored result in {output_to}: {result}")
                except Exception as e:
                    print(f"    ❌ Error executing dependent function {func_name}: {str(e)}")
                    traceback.print_exc()
        else:
            # Mark as needing parent value
            input_item["needs_parent_value"] = True
            input_item["parent_ids"] = missing_dependencies
            input_map[input_id]["needs_parent_value"] = True
            input_map[input_id]["parent_ids"] = missing_dependencies
            print(f"  ⚠️ Marked as needing parent values: {missing_dependencies}")
    
    return input_map


@router.get("/instances/{instance_id}/inputs", response_model=Dict[str, Any])
async def get_local_objective_inputs(
    request: Request,
    instance_id: str = Path(..., description="Workflow Instance ID"),
    dependent_input_id: str = Query(None, description="Specific dependent input to resolve"),
    db: Session = Depends(get_db),
    parent_values: Dict[str, Any] = None,
    security_context: SecurityContext = Depends(require_auth)
):
    """
    Enhanced endpoint to retrieve input fields for the current Local Objective.
    
    Now supports four input types:
    - User: Manual inputs from users
    - Information: Display-only fields
    - System: Auto-populated via nested functions
    - System Dependent: Values dependent on other inputs

    Also supports database-driven dropdowns and field dependencies.
    
    Args:
        instance_id: Workflow instance ID
        dependent_input_id: Optional specific dependent input to resolve
        db: Database session
        parent_values: Optional query parameter with parent field values
    """
    try:
        
        # Handle parent_values from query parameters if not provided directly
        if parent_values is None and request:
            parent_values = {}
            for key, value in request.query_params.items():
                if key not in ['instance_id', 'dependent_input_id']:
                    parent_values[key] = value
                    print(f"✅ Extracted parent value from query: {key}={value}")
        
        # Exclude operation functions from inputs API
        exclude_function_types = ['create', 'update', 'delete', 'insert', 'remove']
                
        print(f"\n==== BEGIN INPUTS API FOR INSTANCE {instance_id} ====")
        print(f"Request for dependent input: {dependent_input_id}" if dependent_input_id else "Standard inputs request")
        print(f"Parent values provided: {parent_values}" if parent_values else "No parent values provided")
        
        # Special case: If requesting a specific dependent input with parent values
        if dependent_input_id and parent_values:
            print(f"📌 Processing direct request for dependent input {dependent_input_id} with parent values")
            
            # Step 1: Get workflow instance and validate status
            result_dict, current_lo_id = fetch_workflow_instance_for_inputs(db, instance_id)
            
            # Step 2: Fetch all input fields
            input_fields = fetch_all_input_fields(db, current_lo_id)
            
            # Step 3: Categorize inputs by type
            input_map, dependency_map, user_inputs, system_inputs, info_inputs, dependent_inputs = categorize_inputs(input_fields)
            
            # Step 4: Find the specific dependent input
            target_input = None
            for input_item in dependent_inputs:
                if input_item["input_id"] == dependent_input_id:
                    target_input = input_item
                    break
            
            if not target_input:
                raise HTTPException(status_code=404, detail=f"Dependent input {dependent_input_id} not found")
            
            # Step 5: Resolve just this dependent input with provided parent values
            input_map = resolve_dependent_inputs(
                db=db,
                current_lo_id=current_lo_id,
                input_map=input_map,
                dependency_map=dependency_map,
                dependent_inputs=[target_input],
                parent_values=parent_values,
                specific_input_id=dependent_input_id,
                exclude_function_types=exclude_function_types
            )
            
            # Step 6: Check if we have dropdown options to load
            if target_input.get("has_dropdown_source"):
                input_map = load_dropdown_options(
                    db=db,
                    input_map=input_map,
                    parent_values=parent_values
                )
            
            # Get the updated target input from input_map
            updated_input = input_map.get(dependent_input_id)
            
            print(f"==== END DEPENDENT INPUT API FOR INSTANCE {instance_id}, INPUT {dependent_input_id} ====\n")
            
            # Return just the requested dependent input
            return {
                "local_objective": current_lo_id,
                "dependent_input": updated_input
            }
        
        # Standard case: Return all inputs
        # Step 1: Get workflow instance and validate status
        result_dict, current_lo_id = fetch_workflow_instance_for_inputs(db, instance_id)
        
        # Step 2: Fetch all input fields
        input_fields = fetch_all_input_fields(db, current_lo_id)
        
        # Step 3: Categorize inputs by type
        input_map, dependency_map, user_inputs, system_inputs, info_inputs, dependent_inputs = categorize_inputs(input_fields)
        
        # Step 4: Load dropdown options for fields that need them
        input_map = load_dropdown_options(db, input_map, parent_values)
        
        # Step 5: Load current values for inputs
        input_map = load_current_input_values(db, instance_id, current_lo_id, input_map)
        
        # Step 6: Execute nested functions for system inputs (excluding operations)
        input_map = execute_nested_functions(
            db=db,
            current_lo_id=current_lo_id,
            input_map=input_map,
            system_inputs=system_inputs,
            exclude_function_types=exclude_function_types
        )
        
        # Step 7: Resolve dependent inputs
        input_map = resolve_dependent_inputs(
            db=db,
            current_lo_id=current_lo_id,
            input_map=input_map,
            dependency_map=dependency_map,
            dependent_inputs=dependent_inputs,
            parent_values=parent_values,
            exclude_function_types=exclude_function_types
        )
        
        # Step 8: Update input lists with latest values from input_map
        for input_list in [user_inputs, system_inputs, info_inputs, dependent_inputs]:
            for item in input_list:
                if item["input_id"] in input_map:
                    # Update with any new fields added during processing
                    for key, value in input_map[item["input_id"]].items():
                        item[key] = value
        
        print(f"==== END INPUTS API FOR INSTANCE {instance_id} ====\n")
        
        # Return structured response
        return {
            "local_objective": current_lo_id,
            "user_inputs": user_inputs,
            "system_inputs": system_inputs,
            "info_inputs": info_inputs,
            "dependent_inputs": dependent_inputs,
            "dependencies": dependency_map
        }

    except Exception as e:
        print(f"❌ ERROR in get_local_objective_inputs: {str(e)}")
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=f"Error fetching input fields: {str(e)}")
    


@router.post("/instances/{instance_id}/cancel", response_model=WorkflowInstance)
async def cancel_workflow_instance(
    instance_id: str = Path(..., description="Workflow Instance ID"),
    user_id: str = Body(..., description="User ID cancelling the workflow"),
    reason: str = Body(None, description="Reason for cancellation"),
    security_context: SecurityContext = Depends(require_auth)
):
    """
    Cancel a workflow instance.
    """
    try:
        # Initialize system functions
        #with SystemFunctions(instance_id=instance_id, user_id=user_id) as sf:
            # Update the workflow instance
            query = """
            UPDATE workflow_runtime.workflow_instances
            SET status = %s, instance_data = jsonb_set(instance_data, '{cancellation_reason}', %s::jsonb)
            WHERE instance_id = %s AND status IN (%s, %s)
            RETURNING *
            """
            params = (
                WorkflowInstanceStatus.CANCELLED.value,
                f'"{reason or "Cancelled by user"}"',
                instance_id,
                WorkflowInstanceStatus.ACTIVE.value,
                WorkflowInstanceStatus.PAUSED.value
            )

            #result = db.execute(text(query), params).fetchall()
            result = db.execute(text(query), params).fetchone()


            if not result:
                raise HTTPException(status_code=404, detail=f"Workflow instance {instance_id} not found or cannot be cancelled")

            # Create workflow instance object
            instance = WorkflowInstance(
                instance_id=result[0]['instance_id'],
                go_id=result[0]['go_id'],
                tenant_id=result[0]['tenant_id'],
                status=result[0]['status'],
                started_by=result[0]['started_by'],
                started_at=result[0]['started_at'],
                current_lo_id=result[0]['current_lo_id'],
                instance_data=result[0]['instance_data'],
                is_test=result[0]['is_test'],
                version=result[0]['version']
            )

            return instance
    except HTTPException as e:
        raise e
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error cancelling workflow instance: {str(e)}")
