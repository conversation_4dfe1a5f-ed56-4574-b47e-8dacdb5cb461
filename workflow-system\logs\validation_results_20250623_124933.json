{"success": false, "component_type": "entities", "target_schema": "workflow_temp", "validation_timestamp": "2025-06-23T12:49:33.830878", "errors": [{"rule_id": "RULE_05", "message": "Entity category 'employee management' must be one of: ['master', 'transaction', 'reference', 'aggregate', 'contextual']", "severity": "error", "entity_id": "E13", "attribute_id": null, "timestamp": "2025-06-23T12:49:33.815568"}, {"rule_id": "RULE_11", "message": "Entity 'E13' must have at least one attribute", "severity": "error", "entity_id": "E13", "attribute_id": null, "timestamp": "2025-06-23T12:49:33.830842"}], "warnings": [{"rule_id": "RULE_02", "message": "Invalid status transition from deployed_to_production to deployed_to_temp", "severity": "warning", "entity_id": "E13", "attribute_id": null, "timestamp": "2025-06-23T12:49:33.794668"}, {"rule_id": "RULE_07", "message": "Version mismatch between schemas for entity E13: 3 vs 1", "severity": "warning", "entity_id": "E13", "attribute_id": null, "timestamp": "2025-06-23T12:49:33.823912"}], "error_count": 2, "warning_count": 2}