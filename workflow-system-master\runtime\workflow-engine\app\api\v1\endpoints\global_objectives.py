from fastapi import APIRouter, HTTPException, Query, Depends
from sqlalchemy.orm import Session
from sqlalchemy.sql import text
from typing import List, Dict, Any, Optional
from app.database.postgres import get_db  # ✅ As per your project structure
from app.auth.auth_middleware import get_security_context, require_auth, SecurityContext

router = APIRouter()


@router.get("/", response_model=List[Dict[str, str]])
async def get_global_objectives(
    tenant_id: str = Query(..., description="Tenant ID to filter objectives"),
    db: Session = Depends(get_db),
    security_context: SecurityContext = Depends(require_auth)
):
    """
    Get all global objectives for a specific tenant.
    """
    try:
        query = """
        SELECT go_id AS objective_id, name, tenant_id, version, status
        FROM workflow_runtime.global_objectives
        WHERE tenant_id = :tenant_id
        AND deleted_mark = false
        ORDER BY go_id;
        """
        result = db.execute(text(query), {"tenant_id": tenant_id.lower()}).fetchall()
        return [dict(row._mapping) for row in result]
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error fetching global objectives: {str(e)}")
