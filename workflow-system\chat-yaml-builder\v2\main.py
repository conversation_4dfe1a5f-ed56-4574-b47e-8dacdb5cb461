"""
Main Entry Point for YAML Builder v2

This module provides the main entry point for the YAML Builder v2 system.
"""

import os
import sys
import logging
import argparse
import json
from typing import Dict, List, Tuple, Any, Optional

# Import parsers
from parsers.entity_parser import parse_entity_yaml
from parsers.go_parser import parse_go_yaml
from parsers.lo_parser import parse_lo_yaml
from parsers.role_parser import parse_role_yaml

# Import prescriptive parser
from prescriptive_parser import parse_prescriptive_yaml

# Import component validator
from component_validator import validate_entities, validate_go_definitions, validate_lo_definitions, validate_roles

# Import component deployer
from component_deployer import ComponentDeployer

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('yaml_builder_v2.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger('main')

def parse_yaml(yaml_file: str, component_type: str) -> Tuple[bool, Dict, List[str]]:
    """
    Parse a YAML file.
    
    Args:
        yaml_file: Path to the YAML file
        component_type: Type of component to parse ('entity', 'go', 'lo', 'role', 'prescriptive')
        
    Returns:
        Tuple containing:
            - Boolean indicating if parsing was successful
            - Parsed data
            - List of messages (warnings, errors, or success messages)
    """
    messages = []
    
    try:
        logger.info(f"Parsing {component_type} YAML file: {yaml_file}")
        
        # Check if file exists
        if not os.path.isfile(yaml_file):
            logger.error(f"File not found: {yaml_file}")
            return False, {}, [f"File not found: {yaml_file}"]
        
        # Parse YAML file based on component type
        if component_type == 'entity':
            success, data, parse_messages = parse_entity_yaml(yaml_file)
        elif component_type == 'go':
            success, data, parse_messages = parse_go_yaml(yaml_file)
        elif component_type == 'lo':
            success, data, parse_messages = parse_lo_yaml(yaml_file)
        elif component_type == 'role':
            success, data, parse_messages = parse_role_yaml(yaml_file)
        elif component_type == 'prescriptive':
            success, data, parse_messages = parse_prescriptive_yaml(yaml_file)
        else:
            logger.error(f"Unknown component type: {component_type}")
            return False, {}, [f"Unknown component type: {component_type}"]
        
        messages.extend(parse_messages)
        
        if not success:
            return False, {}, messages
        
        logger.info(f"Successfully parsed {component_type} YAML file: {yaml_file}")
        return True, data, messages
    except Exception as e:
        logger.error(f"Error parsing {component_type} YAML file: {str(e)}", exc_info=True)
        messages.append(f"Error parsing {component_type} YAML file: {str(e)}")
        return False, {}, messages

def validate_component(data: Dict, component_type: str) -> Tuple[bool, List[str]]:
    """
    Validate a parsed component.
    
    Args:
        data: Parsed component data
        component_type: Type of component to validate ('entity', 'go', 'lo', 'role')
        
    Returns:
        Tuple containing:
            - Boolean indicating if validation was successful
            - List of messages (warnings, errors, or success messages)
    """
    try:
        logger.info(f"Validating {component_type} data")
        
        # Validate component based on type
        if component_type == 'entity':
            success, messages = validate_entities(data)
        elif component_type == 'go':
            success, messages = validate_go_definitions(data)
        elif component_type == 'lo':
            success, messages = validate_lo_definitions(data)
        elif component_type == 'role':
            success, messages = validate_roles(data)
        else:
            logger.error(f"Unknown component type: {component_type}")
            return False, [f"Unknown component type: {component_type}"]
        
        if not success:
            return False, messages
        
        logger.info(f"Successfully validated {component_type} data")
        return True, messages
    except Exception as e:
        logger.error(f"Error validating {component_type} data: {str(e)}", exc_info=True)
        return False, [f"Error validating {component_type} data: {str(e)}"]

def deploy_component(data: Dict, component_type: str, schema_name: str = None, use_temp_schema: bool = True) -> Tuple[bool, List[str]]:
    """
    Deploy a parsed component to the database.
    
    Args:
        data: Parsed component data
        component_type: Type of component to deploy ('entity', 'go', 'lo', 'role')
        schema_name: Schema name to deploy to (overrides use_temp_schema)
        use_temp_schema: Whether to use a temporary schema for deployment
        
    Returns:
        Tuple containing:
            - Boolean indicating if deployment was successful
            - List of messages (warnings, errors, or success messages)
    """
    try:
        logger.info(f"Deploying {component_type} data")
        
        # Create component deployer
        deployer = ComponentDeployer(use_temp_schema=use_temp_schema)
        
        # Deploy component based on type
        success, messages = deployer.deploy(component_type, data, schema_name)
        
        if not success:
            return False, messages
        
        logger.info(f"Successfully deployed {component_type} data")
        return True, messages
    except Exception as e:
        logger.error(f"Error deploying {component_type} data: {str(e)}", exc_info=True)
        return False, [f"Error deploying {component_type} data: {str(e)}"]

def process_yaml_file(yaml_file: str, component_type: str, schema_name: str = None, use_temp_schema: bool = True, validate_only: bool = False) -> Tuple[bool, List[str]]:
    """
    Process a YAML file.
    
    Args:
        yaml_file: Path to the YAML file
        component_type: Type of component to process ('entity', 'go', 'lo', 'role', 'prescriptive')
        schema_name: Schema name to deploy to (overrides use_temp_schema)
        use_temp_schema: Whether to use a temporary schema for deployment
        validate_only: Whether to only validate the YAML file
        
    Returns:
        Tuple containing:
            - Boolean indicating if processing was successful
            - List of messages (warnings, errors, or success messages)
    """
    messages = []
    
    try:
        logger.info(f"Processing {component_type} YAML file: {yaml_file}")
        
        # Parse YAML file
        success, data, parse_messages = parse_yaml(yaml_file, component_type)
        messages.extend(parse_messages)
        
        if not success:
            return False, messages
        
        # Handle prescriptive YAML
        if component_type == 'prescriptive':
            # Process each component in the prescriptive YAML
            for comp_type, comp_data in data.items():
                # Validate component
                success, validate_messages = validate_component(comp_data, comp_type)
                messages.extend(validate_messages)
                
                if not success:
                    return False, messages
                
                if not validate_only:
                    # Deploy component
                    success, deploy_messages = deploy_component(comp_data, comp_type, schema_name, use_temp_schema)
                    messages.extend(deploy_messages)
                    
                    if not success:
                        return False, messages
        else:
            # Validate component
            success, validate_messages = validate_component(data, component_type)
            messages.extend(validate_messages)
            
            if not success:
                return False, messages
            
            if not validate_only:
                # Deploy component
                success, deploy_messages = deploy_component(data, component_type, schema_name, use_temp_schema)
                messages.extend(deploy_messages)
                
                if not success:
                    return False, messages
        
        logger.info(f"Successfully processed {component_type} YAML file: {yaml_file}")
        return True, messages
    except Exception as e:
        logger.error(f"Error processing {component_type} YAML file: {str(e)}", exc_info=True)
        messages.append(f"Error processing {component_type} YAML file: {str(e)}")
        return False, messages

def promote_temp_schema(runtime_schema_name: str = 'workflow_runtime') -> Tuple[bool, List[str]]:
    """
    Promote from temporary schema to runtime schema.
    
    Args:
        runtime_schema_name: Runtime schema name
        
    Returns:
        Tuple containing:
            - Boolean indicating if promotion was successful
            - List of messages (warnings, errors, or success messages)
    """
    try:
        logger.info(f"Promoting from temporary schema to runtime schema {runtime_schema_name}")
        
        # Create component deployer
        deployer = ComponentDeployer(use_temp_schema=True)
        
        # Promote from temporary schema to runtime schema
        success, messages = deployer.promote_from_temp_to_runtime()
        
        if not success:
            return False, messages
        
        logger.info(f"Successfully promoted from temporary schema to runtime schema {runtime_schema_name}")
        return True, messages
    except Exception as e:
        logger.error(f"Error promoting from temporary schema to runtime schema: {str(e)}", exc_info=True)
        return False, [f"Error promoting from temporary schema to runtime schema: {str(e)}"]

def rollback_temp_schema() -> Tuple[bool, List[str]]:
    """
    Rollback temporary schema.
    
    Returns:
        Tuple containing:
            - Boolean indicating if rollback was successful
            - List of messages (warnings, errors, or success messages)
    """
    try:
        logger.info("Rolling back temporary schema")
        
        # Create component deployer
        deployer = ComponentDeployer(use_temp_schema=True)
        
        # Rollback temporary schema
        success, messages = deployer.rollback_temp_schema()
        
        if not success:
            return False, messages
        
        logger.info("Successfully rolled back temporary schema")
        return True, messages
    except Exception as e:
        logger.error(f"Error rolling back temporary schema: {str(e)}", exc_info=True)
        return False, [f"Error rolling back temporary schema: {str(e)}"]

def main():
    """
    Main entry point.
    """
    parser = argparse.ArgumentParser(description='YAML Builder v2')
    
    # Add arguments
    parser.add_argument('--yaml-file', type=str, help='Path to the YAML file')
    parser.add_argument('--component-type', type=str, choices=['entity', 'go', 'lo', 'role', 'prescriptive'], help='Type of component to process')
    parser.add_argument('--schema-name', type=str, help='Schema name to deploy to (overrides use-temp-schema)')
    parser.add_argument('--use-temp-schema', action='store_true', help='Whether to use a temporary schema for deployment')
    parser.add_argument('--validate-only', action='store_true', help='Whether to only validate the YAML file')
    parser.add_argument('--promote', action='store_true', help='Promote from temporary schema to runtime schema')
    parser.add_argument('--rollback', action='store_true', help='Rollback temporary schema')
    
    # Parse arguments
    args = parser.parse_args()
    
    # Process YAML file
    if args.yaml_file and args.component_type:
        success, messages = process_yaml_file(args.yaml_file, args.component_type, args.schema_name, args.use_temp_schema, args.validate_only)
        
        for message in messages:
            print(message)
        
        if not success:
            sys.exit(1)
    
    # Promote from temporary schema to runtime schema
    if args.promote:
        success, messages = promote_temp_schema()
        
        for message in messages:
            print(message)
        
        if not success:
            sys.exit(1)
    
    # Rollback temporary schema
    if args.rollback:
        success, messages = rollback_temp_schema()
        
        for message in messages:
            print(message)
        
        if not success:
            sys.exit(1)

if __name__ == '__main__':
    main()
