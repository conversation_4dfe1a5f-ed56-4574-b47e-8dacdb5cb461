import yaml

def parse_yaml(file_path):
    """Parses a YAML file and returns its data as a dictionary."""
    with open(file_path, 'r', encoding='utf-8') as file:
        data = yaml.safe_load(file)
    return data

def print_tenant_roles(data):
    """Prints tenant roles and their access permissions."""
    tenant = data.get('tenant', {})
    print(f"Tenant ID: {tenant.get('id')}, Name: {tenant.get('name')}")

    roles = tenant.get('roles', [])
    for role in roles:
        print(f"\nRole ID: {role['id']}, Name: {role['name']}")
        print("Entities Access:")
        for entity in role.get('access', {}).get('entities', []):
            print(f"  - Entity ID: {entity['entity_id']}, Permissions: {', '.join(entity['permissions'])}")

        print("Objectives Access:")
        for objective in role.get('access', {}).get('objectives', []):
            print(f"  - Objective ID: {objective['objective_id']}, Permissions: {', '.join(objective['permissions'])}")

def print_entities(data):
    """Prints entity details and attributes."""
    entities = data.get('entities', [])
    for entity in entities:
        print(f"\nEntity ID: {entity['id']}, Name: {entity['name']}, Type: {entity['type']}, Status: {entity['status']}")
        print("Attributes:")
        for attr in entity.get('attributes', []):
            print(f"  - {attr['name']} ({attr['datatype']}) - Required: {attr.get('required', False)}")

def print_permission_types(data):
    """Prints permission types and capabilities."""
    permissions = data.get('permission_types', [])
    print("\nPermission Types:")
    for perm in permissions:
        print(f"  - ID: {perm['id']}, Description: {perm['description']}, Capabilities: {', '.join(perm['capabilities'])}")

if __name__ == "__main__":
    yaml_file =  "/home/<USER>/workflow-system/design-time/config-parser/yaml/workflow.yaml"  # Replace with actual file path
    config_data = parse_yaml(yaml_file)

    print_tenant_roles(config_data)
    print_entities(config_data)
    print_permission_types(config_data)
