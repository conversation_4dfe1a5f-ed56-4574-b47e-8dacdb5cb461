{"timestamp": "2025-06-24T10:45:24.234799", "endpoint": "fetch/constants", "input": {}, "output": {"success": true, "postgres_constants": [{"constant_id": 1001, "attribute": "gst_percentage", "value": "18", "description": "GST rate for furniture purchases", "tenant_id": "t001", "allow_override": false, "override_permissions": null, "created_at": "2025-06-22T11:10:48.589194", "updated_at": "2025-06-22T11:40:40.898347", "created_by": "system", "updated_by": "system", "status": "Active", "entity_name": "FurnitureOrder", "attribute_name": "gstamount", "auto_id": 1001}, {"constant_id": 1002, "attribute": "allowed_methods", "value": "[\"UPI\", \"Credit Card\"]", "description": "Allowed payment methods for furniture orders", "tenant_id": "t001", "allow_override": false, "override_permissions": null, "created_at": "2025-06-22T11:10:48.589194", "updated_at": "2025-06-22T11:40:40.898347", "created_by": "system", "updated_by": "system", "status": "Active", "entity_name": "FurnitureOrder", "attribute_name": "paymentmethod", "auto_id": 1002}], "mongo_drafts": [{"_id": "6858f30f1195bbcbf7aa7691", "constant_id": 1004, "attribute": "allow_override", "value": "false", "description": "Whether this constant can be overridden", "tenant_id": "T101", "allow_override": false, "override_permissions": "", "status": "deployed_to_temp", "entity_name": "", "attribute_name": "", "created_at": "2025-06-23T09:02:49.471376", "updated_at": "2025-06-23T14:03:51.090866", "created_by": "Tarun", "updated_by": "Tarun", "constant_status": "new", "changes_detected": [], "version": 2}], "total_postgres": 2, "total_drafts": 1, "operation": "fetch"}, "status": "success"}
{"timestamp": "2025-06-24T12:31:29.734239", "endpoint": "fetch/constants", "input": {}, "output": {"success": true, "postgres_constants": [{"constant_id": 1001, "attribute": "gst_percentage", "value": "18", "description": "GST rate for furniture purchases", "tenant_id": "t001", "allow_override": false, "override_permissions": null, "created_at": "2025-06-22T11:10:48.589194", "updated_at": "2025-06-22T11:40:40.898347", "created_by": "system", "updated_by": "system", "status": "Active", "entity_name": "FurnitureOrder", "attribute_name": "gstamount", "auto_id": 1001}, {"constant_id": 1002, "attribute": "allowed_methods", "value": "[\"UPI\", \"Credit Card\"]", "description": "Allowed payment methods for furniture orders", "tenant_id": "t001", "allow_override": false, "override_permissions": null, "created_at": "2025-06-22T11:10:48.589194", "updated_at": "2025-06-22T11:40:40.898347", "created_by": "system", "updated_by": "system", "status": "Active", "entity_name": "FurnitureOrder", "attribute_name": "paymentmethod", "auto_id": 1002}, {"constant_id": 1004, "attribute": "allow_override", "value": "false", "description": "Whether this constant can be overridden", "tenant_id": "T101", "allow_override": false, "override_permissions": "", "created_at": "2025-06-24T11:42:53.276516", "updated_at": "2025-06-24T11:42:53.276516", "created_by": "system", "updated_by": "system", "status": "deployed_to_temp", "entity_name": "", "attribute_name": "", "auto_id": 1}], "mongo_drafts": [{"_id": "6858f30f1195bbcbf7aa7691", "constant_id": 1004, "attribute": "allow_override", "value": "false", "description": "Whether this constant can be overridden", "tenant_id": "T101", "allow_override": false, "override_permissions": "", "status": "deployed_to_production", "entity_name": "", "attribute_name": "", "created_at": "2025-06-23T09:02:49.471376", "updated_at": "2025-06-24T11:42:53.278497", "created_by": "Tarun", "updated_by": "Tarun", "constant_status": "new", "changes_detected": [], "version": 3}], "total_postgres": 3, "total_drafts": 1, "operation": "fetch"}, "status": "success"}
{"timestamp": "2025-06-24T12:37:29.534835", "endpoint": "fetch/constants", "input": {}, "output": {"success": true, "postgres_constants": [{"constant_id": 1001, "attribute": "gst_percentage", "value": "18", "description": "GST rate for furniture purchases", "tenant_id": "t001", "allow_override": false, "override_permissions": null, "created_at": "2025-06-22T11:10:48.589194", "updated_at": "2025-06-22T11:40:40.898347", "created_by": "system", "updated_by": "system", "status": "Active", "entity_name": "FurnitureOrder", "attribute_name": "gstamount", "auto_id": 1001}, {"constant_id": 1002, "attribute": "allowed_methods", "value": "[\"UPI\", \"Credit Card\"]", "description": "Allowed payment methods for furniture orders", "tenant_id": "t001", "allow_override": false, "override_permissions": null, "created_at": "2025-06-22T11:10:48.589194", "updated_at": "2025-06-22T11:40:40.898347", "created_by": "system", "updated_by": "system", "status": "Active", "entity_name": "FurnitureOrder", "attribute_name": "paymentmethod", "auto_id": 1002}, {"constant_id": 1004, "attribute": "allow_override", "value": "false", "description": "Whether this constant can be overridden", "tenant_id": "T101", "allow_override": false, "override_permissions": "", "created_at": "2025-06-24T11:42:53.276516", "updated_at": "2025-06-24T11:42:53.276516", "created_by": "system", "updated_by": "system", "status": "deployed_to_temp", "entity_name": "", "attribute_name": "", "auto_id": 1}], "mongo_drafts": [{"_id": "6858f30f1195bbcbf7aa7691", "constant_id": 1004, "attribute": "allow_override", "value": "false", "description": "Whether this constant can be overridden", "tenant_id": "T101", "allow_override": false, "override_permissions": "", "status": "deployed_to_production", "entity_name": "", "attribute_name": "", "created_at": "2025-06-23T09:02:49.471376", "updated_at": "2025-06-24T11:42:53.278497", "created_by": "Tarun", "updated_by": "Tarun", "constant_status": "new", "changes_detected": [], "version": 3}], "total_postgres": 3, "total_drafts": 1, "operation": "fetch"}, "status": "success"}
{"timestamp": "2025-06-24T13:10:52.376954", "endpoint": "fetch/constants", "input": {}, "output": {"success": true, "postgres_constants": [{"constant_id": 1001, "attribute": "gst_percentage", "value": "18", "description": "GST rate for furniture purchases", "tenant_id": "t001", "allow_override": false, "override_permissions": null, "created_at": "2025-06-22T11:10:48.589194", "updated_at": "2025-06-22T11:40:40.898347", "created_by": "system", "updated_by": "system", "status": "Active", "entity_name": "FurnitureOrder", "attribute_name": "gstamount", "auto_id": 1001}, {"constant_id": 1002, "attribute": "allowed_methods", "value": "[\"UPI\", \"Credit Card\"]", "description": "Allowed payment methods for furniture orders", "tenant_id": "t001", "allow_override": false, "override_permissions": null, "created_at": "2025-06-22T11:10:48.589194", "updated_at": "2025-06-22T11:40:40.898347", "created_by": "system", "updated_by": "system", "status": "Active", "entity_name": "FurnitureOrder", "attribute_name": "paymentmethod", "auto_id": 1002}, {"constant_id": 1004, "attribute": "allow_override", "value": "false", "description": "Whether this constant can be overridden", "tenant_id": "T101", "allow_override": false, "override_permissions": "", "created_at": "2025-06-24T11:42:53.276516", "updated_at": "2025-06-24T11:42:53.276516", "created_by": "system", "updated_by": "system", "status": "deployed_to_temp", "entity_name": "", "attribute_name": "", "auto_id": 1}], "mongo_drafts": [{"_id": "6858f30f1195bbcbf7aa7691", "constant_id": 1004, "attribute": "allow_override", "value": "false", "description": "Whether this constant can be overridden", "tenant_id": "T101", "allow_override": false, "override_permissions": "", "status": "deployed_to_production", "entity_name": "", "attribute_name": "", "created_at": "2025-06-23T09:02:49.471376", "updated_at": "2025-06-24T11:42:53.278497", "created_by": "Tarun", "updated_by": "Tarun", "constant_status": "new", "changes_detected": [], "version": 3}, {"_id": "685a9df2a30d43107e83f576", "constant_id": 1005, "attribute": "MAX_TEST_ITEMS", "value": "100", "description": "Maximum number of test items allowed", "tenant_id": "t999", "allow_override": false, "override_permissions": "", "status": "draft", "entity_name": "", "attribute_name": "", "created_at": "2025-06-24T12:45:38.607968", "updated_at": "2025-06-24T12:45:38.607976", "created_by": "Tarun", "updated_by": "Tarun", "constant_status": "new", "changes_detected": [], "version": 1}], "total_postgres": 3, "total_drafts": 2, "operation": "fetch"}, "status": "success"}
{"timestamp": "2025-06-24T13:12:19.913437", "endpoint": "fetch/constants", "input": {}, "output": {"success": true, "postgres_constants": [{"constant_id": 1001, "attribute": "gst_percentage", "value": "18", "description": "GST rate for furniture purchases", "tenant_id": "t001", "allow_override": false, "override_permissions": null, "created_at": "2025-06-22T11:10:48.589194", "updated_at": "2025-06-22T11:40:40.898347", "created_by": "system", "updated_by": "system", "status": "Active", "entity_name": "FurnitureOrder", "attribute_name": "gstamount", "auto_id": 1001}, {"constant_id": 1002, "attribute": "allowed_methods", "value": "[\"UPI\", \"Credit Card\"]", "description": "Allowed payment methods for furniture orders", "tenant_id": "t001", "allow_override": false, "override_permissions": null, "created_at": "2025-06-22T11:10:48.589194", "updated_at": "2025-06-22T11:40:40.898347", "created_by": "system", "updated_by": "system", "status": "Active", "entity_name": "FurnitureOrder", "attribute_name": "paymentmethod", "auto_id": 1002}, {"constant_id": 1004, "attribute": "allow_override", "value": "false", "description": "Whether this constant can be overridden", "tenant_id": "T101", "allow_override": false, "override_permissions": "", "created_at": "2025-06-24T11:42:53.276516", "updated_at": "2025-06-24T11:42:53.276516", "created_by": "system", "updated_by": "system", "status": "deployed_to_temp", "entity_name": "", "attribute_name": "", "auto_id": 1}], "mongo_drafts": [{"_id": "6858f30f1195bbcbf7aa7691", "constant_id": 1004, "attribute": "allow_override", "value": "false", "description": "Whether this constant can be overridden", "tenant_id": "T101", "allow_override": false, "override_permissions": "", "status": "deployed_to_production", "entity_name": "", "attribute_name": "", "created_at": "2025-06-23T09:02:49.471376", "updated_at": "2025-06-24T11:42:53.278497", "created_by": "Tarun", "updated_by": "Tarun", "constant_status": "new", "changes_detected": [], "version": 3}, {"_id": "685a9df2a30d43107e83f576", "constant_id": 1005, "attribute": "MAX_TEST_ITEMS", "value": "100", "description": "Maximum number of test items allowed", "tenant_id": "t999", "allow_override": false, "override_permissions": "", "status": "draft", "entity_name": "", "attribute_name": "", "created_at": "2025-06-24T12:45:38.607968", "updated_at": "2025-06-24T12:45:38.607976", "created_by": "Tarun", "updated_by": "Tarun", "constant_status": "new", "changes_detected": [], "version": 1}], "total_postgres": 3, "total_drafts": 2, "operation": "fetch"}, "status": "success"}
{"timestamp": "2025-06-24T13:12:47.353433", "endpoint": "fetch/constants", "input": {}, "output": {"success": true, "postgres_constants": [{"constant_id": 1001, "attribute": "gst_percentage", "value": "18", "description": "GST rate for furniture purchases", "tenant_id": "t001", "allow_override": false, "override_permissions": null, "created_at": "2025-06-22T11:10:48.589194", "updated_at": "2025-06-22T11:40:40.898347", "created_by": "system", "updated_by": "system", "status": "Active", "entity_name": "FurnitureOrder", "attribute_name": "gstamount", "auto_id": 1001}, {"constant_id": 1002, "attribute": "allowed_methods", "value": "[\"UPI\", \"Credit Card\"]", "description": "Allowed payment methods for furniture orders", "tenant_id": "t001", "allow_override": false, "override_permissions": null, "created_at": "2025-06-22T11:10:48.589194", "updated_at": "2025-06-22T11:40:40.898347", "created_by": "system", "updated_by": "system", "status": "Active", "entity_name": "FurnitureOrder", "attribute_name": "paymentmethod", "auto_id": 1002}, {"constant_id": 1004, "attribute": "allow_override", "value": "false", "description": "Whether this constant can be overridden", "tenant_id": "T101", "allow_override": false, "override_permissions": "", "created_at": "2025-06-24T11:42:53.276516", "updated_at": "2025-06-24T11:42:53.276516", "created_by": "system", "updated_by": "system", "status": "deployed_to_temp", "entity_name": "", "attribute_name": "", "auto_id": 1}], "mongo_drafts": [{"_id": "6858f30f1195bbcbf7aa7691", "constant_id": 1004, "attribute": "allow_override", "value": "false", "description": "Whether this constant can be overridden", "tenant_id": "T101", "allow_override": false, "override_permissions": "", "status": "deployed_to_production", "entity_name": "", "attribute_name": "", "created_at": "2025-06-23T09:02:49.471376", "updated_at": "2025-06-24T11:42:53.278497", "created_by": "Tarun", "updated_by": "Tarun", "constant_status": "new", "changes_detected": [], "version": 3}, {"_id": "685a9df2a30d43107e83f576", "constant_id": 1005, "attribute": "MAX_TEST_ITEMS", "value": "100", "description": "Maximum number of test items allowed", "tenant_id": "t999", "allow_override": false, "override_permissions": "", "status": "draft", "entity_name": "", "attribute_name": "", "created_at": "2025-06-24T12:45:38.607968", "updated_at": "2025-06-24T12:45:38.607976", "created_by": "Tarun", "updated_by": "Tarun", "constant_status": "new", "changes_detected": [], "version": 1}], "total_postgres": 3, "total_drafts": 2, "operation": "fetch"}, "status": "success"}
