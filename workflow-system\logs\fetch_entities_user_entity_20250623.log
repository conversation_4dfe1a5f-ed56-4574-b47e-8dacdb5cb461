{"timestamp": "2025-06-23T10:38:44.488195", "endpoint": "fetch/entities/user_entity", "input": {}, "output": {"success": true, "entity_id": "user_entity", "postgres_record": null, "mongo_draft": null, "found_in_postgres": false, "found_in_mongo": false, "operation": "fetch"}, "status": "success"}
{"timestamp": "2025-06-23T14:08:00.173883", "endpoint": "fetch/entities/user_entity", "input": {}, "output": {"success": true, "entity_id": "user_entity", "postgres_record": null, "mongo_draft": null, "found_in_postgres": false, "found_in_mongo": false, "operation": "fetch"}, "status": "success"}
