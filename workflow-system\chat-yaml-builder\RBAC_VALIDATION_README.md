# RBAC Validation for Workflow System

This document provides information about the RBAC (Role-Based Access Control) validation functionality in the Workflow System.

## Overview

The RBAC validation ensures that workflow YAML files comply with the RBAC requirements, including:

1. **User and Role Entity Definitions**: Validates that User and Role entities have all mandatory attributes.
2. **Agent Stack**: Validates that the agent stack is properly defined with role and user-specific permissions.
3. **Email-Based User Identification**: Validates that user identifiers in the agent stack are valid email addresses.
4. **Workflow Source Constraints**: Validates that only one Local Objective has `workflow_source: "origin"`.

## Validation Components

The RBAC validation functionality consists of the following components:

- **rbac_validator.py**: Contains the core validation functions for RBAC.
- **validate_engine.py**: Integrates RBAC validation into the main validation engine.
- **validate_yaml.py**: Provides a function to validate YAML strings with RBAC validation.
- **validate_rbac_yaml.sh**: A shell script to run RBAC validation on a YAML file.

## How to Use

### Validating a YAML File

To validate a YAML file for RBAC compliance, use the `validate_rbac_yaml.sh` script:

```bash
./validate_rbac_yaml.sh path/to/your/workflow.yaml
```

This will run the validation engine on the specified YAML file and check for RBAC compliance.

### Programmatic Usage

You can also use the validation functions programmatically in your Python code:

```python
from validate_yaml import validate_yaml

# Validate a YAML string
yaml_text = """
tenant:
  id: "t001"
  name: "UserManagement001"
  roles:
    - id: "r001"
      name: "Administrator"
      # ...
"""

result = validate_yaml(yaml_text)
if result["valid"]:
    print("YAML is valid!")
else:
    print("YAML validation failed:", result["error"])
```

## RBAC Requirements

### User Entity

The User entity must have the following mandatory attributes:

- `user_id` (at101)
- `username` (at102)
- `email` (at103)
- `status` (at106)

Example User entity definition:

```yaml
- id: "e001"
  name: "User"
  type: "Master"
  version: "1.0"
  status: "Active"
  attributes_metadata:
    attribute_prefix: "at"
    attribute_map:
      "at101": "user_id"
      "at102": "username"
      "at103": "email"
      "at104": "first_name"
      "at105": "last_name"
      "at106": "status"
      "at107": "password_hash"
      "at108": "disabled"
    required_attributes:
      - "at101"
      - "at102"
      - "at103"
      - "at106"
  attributes:
    - id: "at101"
      name: "user_id"
      display_name: "User ID"
      datatype: "String"
      required: true
      version: "1.0"
      status: "Deployed"
    # ... other attributes
```

### Role Entity

The Role entity must have the following mandatory attributes:

- `role_id` (at201)
- `name` (at202)

Example Role entity definition:

```yaml
- id: "e002"
  name: "Role"
  type: "Master"
  version: "1.0"
  status: "Active"
  attributes_metadata:
    attribute_prefix: "at"
    attribute_map:
      "at201": "role_id"
      "at202": "name"
      "at203": "description"
      "at204": "inherits_from"
      "at205": "tenant_id"
    required_attributes:
      - "at201"
      - "at202"
  attributes:
    - id: "at201"
      name: "role_id"
      display_name: "Role ID"
      datatype: "String"
      required: true
      version: "1.0"
      status: "Deployed"
    # ... other attributes
```

### Agent Stack

The agent stack must be properly defined with role and user-specific permissions:

```yaml
agent_stack:
  agents:
    - role: "Administrator"
      rights: ["Execute", "Read"]
      users: []  # Empty means all users with this role have these rights
    - role: "UserManager"
      rights: ["Read"]
      users: ["<EMAIL>", "<EMAIL>"]  # Only these specific users have these rights
    - role: "Employee"
      rights: ["Read"]
      users: 
        - email: "<EMAIL>"
          rights: ["Read", "Execute"]  # This specific user has additional rights
```

### Workflow Source

Only one Local Objective can have `workflow_source: "origin"`:

```yaml
local_objectives:
  - id: "lo001"
    contextual_id: "go001.lo001"
    name: "Create User"
    workflow_source: "origin"  # Only one LO can have this value
    # ...
  - id: "lo002"
    contextual_id: "go001.lo002"
    name: "Update User Organization"
    workflow_source: "intermediate"  # Other LOs should use "intermediate" or other values
    # ...
```

## Troubleshooting

If the validation fails, check the error messages for details about what needs to be fixed. Common issues include:

- Missing mandatory attributes in User or Role entities
- Invalid email format in user identifiers
- Multiple Local Objectives with `workflow_source: "origin"`
- Missing `users` field in agent stack
- Invalid role references in agent stack

## Further Information

For more information about RBAC implementation in the Workflow System, see the [IMPLEMENTATION_STATUS.md](../runtime/workflow-engine/app/auth/IMPLEMENTATION_STATUS.md) file.
