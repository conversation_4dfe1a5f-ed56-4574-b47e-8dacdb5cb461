#!/usr/bin/env python3
"""
Script to rename entity tables from old format (e000001_department, e000002_employee, etc.)
to new format (e1_department, e2_employee, etc.).
"""

import logging
import sys
import os
import re
import psycopg2
from typing import List, Tuple

# Set up logging
logging.basicConfig(level=logging.INFO,
                   format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
                   handlers=[logging.StreamHandler()])
logger = logging.getLogger('rename_entity_tables')

def get_db_connection(schema_name=None):
    """
    Get a database connection.
    
    Args:
        schema_name: Optional schema name to set as the search path
        
    Returns:
        Database connection object
    """
    # Get database connection parameters from environment variables or use defaults
    db_host = os.environ.get('DB_HOST', '**********')
    db_port = os.environ.get('DB_PORT', '5432')
    db_name = os.environ.get('DB_NAME', 'workflow_system')
    db_user = os.environ.get('DB_USER', 'postgres')
    db_password = os.environ.get('DB_PASSWORD', 'workflow_postgres_secure_password')
    
    # Connect to the database
    conn = psycopg2.connect(
        host=db_host,
        port=db_port,
        dbname=db_name,
        user=db_user,
        password=db_password
    )
    
    # Set schema search path if provided
    if schema_name:
        with conn.cursor() as cursor:
            cursor.execute(f"SET search_path TO {schema_name}")
            conn.commit()
    
    return conn

def rename_entity_tables(schema_name: str) -> Tuple[bool, List[str]]:
    """
    Rename entity tables from old format (e000001_department, e000002_employee, etc.)
    to new format (e1_department, e2_employee, etc.).

    Args:
        schema_name: Schema name

    Returns:
        Tuple containing:
            - Boolean indicating if update was successful
            - List of messages (warnings, errors, or success messages)
    """
    messages = []
    conn = None

    try:
        logger.info(f'Renaming entity tables in schema {schema_name}')

        # Get database connection
        conn = get_db_connection(schema_name)

        with conn.cursor() as cursor:
            # Find all tables in the schema that match the old format pattern
            cursor.execute("""
                SELECT table_name
                FROM information_schema.tables
                WHERE table_schema = %s
                AND table_name ~ '^e[0-9]{6}_.*$'
                AND table_type = 'BASE TABLE'
            """, (schema_name,))

            tables = cursor.fetchall()
            logger.info(f'Found {len(tables)} tables matching the old format pattern')
            
            # Debug: Print the tables found
            for table in tables:
                logger.info(f'Found table: {table[0]}')

            # For each table, rename it to the new format
            for table in tables:
                old_table_name = table[0]
                
                # Extract the entity ID and table name
                match = re.match(r'^e([0-9]{6})_(.*)$', old_table_name)
                if match:
                    entity_num = int(match.group(1))
                    table_suffix = match.group(2)
                    
                    # Create the new table name
                    new_table_name = f'e{entity_num}_{table_suffix}'
                    
                    logger.info(f'Renaming table {old_table_name} to {new_table_name}')
                    
                    # Rename the table
                    cursor.execute(f'ALTER TABLE {schema_name}."{old_table_name}" RENAME TO "{new_table_name}"')
                    
                    messages.append(f'Renamed table {old_table_name} to {new_table_name}')
            
            # Commit the changes
            conn.commit()
            
            logger.info('Successfully renamed entity tables')
            messages.append('Successfully renamed entity tables')
            
            return True, messages
    except Exception as e:
        logger.error(f'Error renaming entity tables: {str(e)}')
        messages.append(f'Error renaming entity tables: {str(e)}')
        
        if conn:
            conn.rollback()
        
        return False, messages
    finally:
        if conn:
            conn.close()

def main():
    """
    Main function to rename entity tables.
    """
    schema_name = 'workflow_temp'
    
    logger.info(f"Starting rename of entity tables in schema {schema_name}")
    
    success, messages = rename_entity_tables(schema_name)
    
    for message in messages:
        logger.info(message)
    
    if success:
        logger.info(f"Successfully renamed entity tables in schema {schema_name}")
    else:
        logger.error(f"Failed to rename entity tables in schema {schema_name}")

if __name__ == "__main__":
    main()
