#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to alter the entity_attribute_metadata table to add columns for attribute additional properties.
"""

import os
import logging
import psycopg2

# Set up logging
logging.basicConfig(level=logging.INFO, 
                   format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
                   handlers=[
                       logging.FileHandler('/home/<USER>/workflow-system/chat-yaml-builder/v2/alter_entity_attribute_metadata.log'),
                       logging.StreamHandler()
                   ])
logger = logging.getLogger('alter_entity_attribute_metadata')

def get_db_connection(schema_name: str = None):
    """
    Get a database connection.
    
    Args:
        schema_name: Optional schema name to set as the search path
        
    Returns:
        Database connection object
    """
    # Get database connection parameters from environment variables or use defaults
    db_host = os.environ.get('DB_HOST', '**********')
    db_port = os.environ.get('DB_PORT', '5432')
    db_name = os.environ.get('DB_NAME', 'workflow_system')
    db_user = os.environ.get('DB_USER', 'postgres')
    db_password = os.environ.get('DB_PASSWORD', 'workflow_postgres_secure_password')
    
    # Connect to the database
    conn = psycopg2.connect(
        host=db_host,
        port=db_port,
        dbname=db_name,
        user=db_user,
        password=db_password
    )
    
    # Set schema search path if provided
    if schema_name:
        with conn.cursor() as cursor:
            cursor.execute(f"SET search_path TO {schema_name}")
            conn.commit()
    
    return conn

def execute_query(query: str, params=None, schema_name: str = None):
    """
    Execute a database query.
    
    Args:
        query: SQL query to execute
        params: Query parameters
        schema_name: Schema name to set as search path
        
    Returns:
        Tuple containing:
            - Boolean indicating if query was successful
            - List of messages (warnings, errors, or success messages)
            - Query result rows
    """
    messages = []
    result = []
    conn = None
    
    try:
        # Get database connection
        conn = get_db_connection(schema_name)
        
        # Execute query
        with conn.cursor() as cursor:
            cursor.execute(query, params)
            
            # Get result if query returns rows
            if cursor.description:
                result = cursor.fetchall()
        
        # Commit transaction
        conn.commit()
        
        return True, messages, result
    except Exception as e:
        if conn:
            conn.rollback()
        
        error_msg = f"Query error: {str(e)}"
        messages.append(error_msg)
        return False, messages, result
    finally:
        if conn:
            conn.close()

def alter_entity_attribute_metadata_table():
    """
    Alter the entity_attribute_metadata table to add columns for attribute additional properties.
    """
    schema_name = 'workflow_temp'
    
    # Check if the columns already exist
    success, messages, result = execute_query(
        f"""
        SELECT column_name FROM information_schema.columns 
        WHERE table_schema = %s AND table_name = %s AND column_name = %s
        """,
        (schema_name, 'entity_attribute_metadata', 'display_name'),
        schema_name
    )
    
    if success and result:
        logger.info("Columns already exist in entity_attribute_metadata table")
        return True, []
    
    # Add columns for attribute additional properties
    query = f"""
    ALTER TABLE {schema_name}.entity_attribute_metadata
    ADD COLUMN display_name VARCHAR(255),
    ADD COLUMN key_type VARCHAR(50),
    ADD COLUMN data_type VARCHAR(50),
    ADD COLUMN type VARCHAR(50),
    ADD COLUMN format TEXT,
    ADD COLUMN values TEXT,
    ADD COLUMN default_value TEXT,
    ADD COLUMN validation VARCHAR(50),
    ADD COLUMN error_message TEXT,
    ADD COLUMN description TEXT,
    ADD COLUMN metadata JSONB
    """
    
    success, messages, _ = execute_query(query, schema_name=schema_name)
    
    if not success:
        logger.error(f"Failed to alter entity_attribute_metadata table: {messages}")
        return False, messages
    
    logger.info("Successfully altered entity_attribute_metadata table")
    return True, []

if __name__ == "__main__":
    success, messages = alter_entity_attribute_metadata_table()
    
    if not success:
        for message in messages:
            logger.error(message)
        exit(1)
    
    logger.info("Successfully altered entity_attribute_metadata table")
