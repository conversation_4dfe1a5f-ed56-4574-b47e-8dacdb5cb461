import psycopg2
from pymongo import MongoClient
import json

# ✅ PostgreSQL Connection Details
PG_CONFIG = {
    "dbname": "workflow_solution_temp",
    "user": "postgres",
    "password": "postgres",
    "host": "localhost",
    "port": "5432"
}

# ✅ MongoDB Connection Details
MONGO_URI = "mongodb://localhost:27017/"
MONGO_DB = "workflow_system"
MONGO_COLLECTION = "workflow"

# ✅ Connect to MongoDB
mongo_client = MongoClient(MONGO_URI)
mongo_db = mongo_client[MONGO_DB]
mongo_collection = mongo_db[MONGO_COLLECTION]

# ✅ Connect to PostgreSQL
pg_conn = psycopg2.connect(**PG_CONFIG)
pg_conn.autocommit = True
pg_cursor = pg_conn.cursor()

# ✅ Step 1: Truncate all tables before insertion
print("\n⚠️ Truncating all tables before insertion...")

tables = [
    "output_triggers", "output_items", "output_stack",
    "system_functions", "input_items", "input_stack",
    "data_mappings", "mapping_rules", "data_mapping_stack",
    "global_objectives","agent_rights", "agent_stack", "execution_pathway_conditions",
    "terminal_pathways", "execution_pathways", "local_objectives", "tenants", "permission_types",
    "roles", "entity_permissions", "objective_permissions", "entities", "entity_attribute_metadata",
    "entity_attributes", "attribute_validations", "attribute_enum_values", "entity_relationships",
     "roles","entity_permissions", "objective_permissions","execution_pathway_conditions"
]

for table in tables:
    try:
        pg_cursor.execute(f"DELETE FROM workflow_solution_temp.{table};")  # DELETE instead of TRUNCATE
        print(f"✅ Deleted records from {table}")
    except Exception as e:
        print(f"❌ Error deleting records from {table}: {e}")

# ✅ Fetch MongoDB Data
workflow_data = mongo_collection.find_one()
if not workflow_data:
    print("❌ No data found in MongoDB.")
    exit()
workflow_data = workflow_data.get("workflow_data", {})

# ✅ Insert Tenant
tenant = workflow_data.get("tenant", {})
tenant_id = tenant.get("id", "T000")
tenant_name = tenant.get("name", "DefaultTenant")

print(f"\n➡️ Inserting Tenant: {tenant_name}...")
try:
    pg_cursor.execute(
        "INSERT INTO workflow_solution_temp.tenants (tenant_id, name) VALUES (%s, %s) ON CONFLICT DO NOTHING",
        (tenant_id, tenant_name)
    )
except Exception as e:
    print(f"❌ Error inserting tenant: {e}")

print("\n➡️ Inserting Roles...")
for role in tenant["roles"]:
    try:
        pg_cursor.execute(
            """
            INSERT INTO workflow_solution_temp.roles (role_id, name, tenant_id, inherits_from)
            VALUES (%s, %s, %s, %s)
            ON CONFLICT (role_id) DO NOTHING;
            """,
            (role["id"], role["name"], tenant["id"], role["inherits_from"])
        )
        print(f"✅ Inserted Role: {role['id']} - {role['name']}")
    except Exception as e:
        print(f"❌ Error inserting role {role['id']}: {e}")

# Insert entity permissions
print("\n➡️ Inserting Entity Permissions...")
for role in tenant["roles"]:
    for entity in role["access"]["entities"]:
        for permission in entity["permissions"]:
            try:
                pg_cursor.execute(
                    """
                    INSERT INTO workflow_solution_temp.entity_permissions (role_id, entity_id, permission_id)
                    VALUES (%s, %s, %s)
                    """,
                    (role["id"], entity["entity_id"], permission)
                )
                print(f"✅ Inserted Entity Permission: Role {role['id']} - Entity {entity['entity_id']} - Permission {permission}")
            except Exception as e:
                print(f"❌ Error inserting entity permission {permission} for role {role['id']}: {e}")

# Insert objective permissions
print("\n➡️ Inserting Objective Permissions...")
for role in tenant["roles"]:
    for objective in role["access"]["objectives"]:
        for permission in objective["permissions"]:
            try:
                pg_cursor.execute(
                    """
                    INSERT INTO workflow_solution_temp.objective_permissions (role_id, objective_id, permission_id)
                    VALUES (%s, %s, %s)
                    """,
                    (role["id"], objective["objective_id"], permission)
                )
                print(f"✅ Inserted Objective Permission: Role {role['id']} - Objective {objective['objective_id']} - Permission {permission}")
            except Exception as e:
                print(f"❌ Error inserting objective permission {permission} for role {role['id']}: {e}")
# ✅ Insert Entities
print("\n➡️ Inserting Entities...")
for entity in workflow_data.get("entities", []):
    try:
        pg_cursor.execute(
            "INSERT INTO workflow_solution_temp.entities (entity_id, name, version, status, type, attribute_prefix, description) VALUES (%s, %s, %s, %s, %s, %s, %s) ON CONFLICT DO NOTHING",
            (entity["id"], entity["name"], entity.get("version", "1.0"), entity["status"], entity["type"], entity.get("attributes_metadata", {}).get("attribute_prefix", ""), entity.get("description", ""))
        )
    except Exception as e:
        print(f"❌ Error inserting entity {entity['id']}: {e}")

# ✅ Insert Entity Attribute Metadata
print("\n➡️ Inserting Entity Attribute Metadata...")
for entity in workflow_data.get("entities", []):
    entity_id = entity["id"]
    for attr_id, attr_name in entity.get("attributes_metadata", {}).get("attribute_map", {}).items():
        required = attr_id in entity.get("attributes_metadata", {}).get("required_attributes", [])
        try:
            pg_cursor.execute(
                "INSERT INTO workflow_solution_temp.entity_attribute_metadata (entity_id, attribute_id, attribute_name, required) VALUES (%s, %s, %s, %s) ON CONFLICT DO NOTHING",
                (entity_id, attr_id, attr_name, required)
            )
        except Exception as e:
            print(f"❌ Error inserting entity attribute metadata {attr_id}: {e}")

# ✅ Insert Entity Attributes
print("\n➡️ Inserting Entity Attributes...")
for entity in workflow_data.get("entities", []):
    entity_id = entity["id"]
    for attr in entity.get("attributes", []):
        try:
            pg_cursor.execute(
                """
                INSERT INTO workflow_solution_temp.entity_attributes (
                    attribute_id, entity_id, name, display_name, datatype, version, status, required, reference_entity_id
                ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s) ON CONFLICT DO NOTHING
                """,
                (
                    attr["id"], entity_id, attr["name"], attr["display_name"], attr["datatype"],
                    attr.get("version", "1.0"), attr["status"], attr.get("required", False),
                    attr.get("references")
                )
            )
        except Exception as e:
            print(f"❌ Error inserting entity attribute {attr['id']}: {e}")

# ✅ Insert Attribute Validations
print("\n➡️ Inserting Attribute Validations...")
for entity in workflow_data.get("entities", []):
    for attr in entity.get("attributes", []):
        attribute_id = attr["id"]
        for validation in attr.get("validations", []):
            try:
                pg_cursor.execute(
                    """
                    INSERT INTO workflow_solution_temp.attribute_validations
                    (attribute_id, rule, expression) VALUES (%s, %s, %s) ON CONFLICT DO NOTHING
                    """,
                    (attribute_id, validation["rule"], validation.get("expression", None))
                )
            except Exception as e:
                print(f"❌ Error inserting validation for {attribute_id}: {e}")

# ✅ Insert Enum Values for Attributes
print("\n➡️ Inserting Enum Values...")
for entity in workflow_data.get("entities", []):
    for attr in entity.get("attributes", []):
        if attr["datatype"].lower() == "enum":
            attribute_id = attr["id"]
            for value in attr.get("values", []):
                try:
                    pg_cursor.execute(
                        """
                        INSERT INTO workflow_solution_temp.attribute_enum_values
                        (attribute_id, value) VALUES (%s, %s) ON CONFLICT DO NOTHING
                        """,
                        (attribute_id, value)
                    )
                except Exception as e:
                    print(f"❌ Error inserting enum value {value} for {attribute_id}: {e}")

# ✅ Insert Entity Relationships
print("\n➡️ Inserting Entity Relationships...")
for entity in workflow_data.get("entities", []):
    for relationship in entity.get("relationships", []):
        try:
            pg_cursor.execute(
                """
                INSERT INTO workflow_solution_temp.entity_relationships
                (source_entity_id, target_entity_id, relationship_type, source_attribute_id, target_attribute_id)
                VALUES (%s, %s, %s, %s, %s) ON CONFLICT DO NOTHING
                """,
                (entity["id"], relationship["entity_id"], relationship["type"],
                 relationship["through_attribute"], relationship["to_attribute"])
            )
        except Exception as e:
            print(f"❌ Error inserting relationship from {entity['id']} to {relationship['entity_id']}: {e}")
# ✅ Insert Permission Type && Capabilities # ✅ Insert Permission Type && Capabilities 
print("\n➡️ Inserting Permission Capabilities...")

for permission in workflow_data.get("permission_types", []):
    try:
        pg_cursor.execute(
            """
            INSERT INTO workflow_solution_temp.permission_types
            (description, permission, capabilities)
            VALUES (%s, %s, %s)
            RETURNING permission_id;
            """,
            (
                permission.get("description", ""),  # Description
                permission.get("id"),  # Permission name
                json.dumps(permission.get("capabilities", []))  # Convert capabilities to JSON
            )  # ✅ Correctly closing the VALUES tuple
        )  # ✅ Properly closing the execute() function

        # ✅ Fetch inserted permission_id safely
        result = pg_cursor.fetchone()
        if result:
            inserted_permission_id = result[0]  # Extract the first value
        else:
            inserted_permission_id = None  # Handle case where no ID is returned

        print(f"✅ Inserted Permission ID: {inserted_permission_id}")  # ✅ Move inside try block

    except Exception as e:
        print(f"❌ Error inserting permission type {permission}: {e}")  # ✅ Proper indentation

#             permission_id = permission["id"]
# for capability in permission.get("capabilities", []):
#         try:
#             pg_cursor.execute(
#                 "INSERT INTO workflow_solution_temp.permission_capabilities (permission_id) VALUES (%s) ON CONFLICT DO NOTHING",
#                 (inserted_permission_id)
#             )
#         except Exception as e:
#             print(f"❌ Error inserting capability {capability}: {e}")

# ✅ Step 2: Insert Global Objectives and Child Data Sequentially
print("\n➡️ Inserting Global Objectives and Child Data Sequentially...")

for go in workflow_data.get("global_objectives", []):
    try:
        # 1️⃣ Insert Global Objective
        pg_cursor.execute(
            """
            INSERT INTO workflow_solution_temp.global_objectives 
            (go_id, name, version, status, description) 
            VALUES (%s, %s, %s, %s, %s) 
            ON CONFLICT (go_id) DO NOTHING
            RETURNING go_id
            """,
            (go["id"], go["name"], go["version"], go["status"], go.get("description", ""))
        )
        result = pg_cursor.fetchone()
        global_objective_id = result[0] if result else go["id"]
        print(f"✅ Inserted Global Objective: {go['id']}")

        # 2️⃣ Insert Input Stack
        if "input_stack" in go:
            try:
                pg_cursor.execute(
                    """
                    INSERT INTO workflow_solution_temp.input_stack (go_id, description) 
                    VALUES (%s, %s) RETURNING id
                    """,
                    (global_objective_id, go["input_stack"]["description"])
                )
                input_stack_id = pg_cursor.fetchone()[0]

                # 3️⃣ Insert Input Items
                for input_item in go["input_stack"]["inputs"]:
                    try:
                        pg_cursor.execute(
                            """
                            INSERT INTO workflow_solution_temp.input_items 
                            (id, input_stack_id, slot_id, contextual_id, entity_reference, attribute_reference, 
                             source_type, source_description, required) 
                            VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s)
                            """,
                            (input_item["id"], input_stack_id, input_item["slot_id"], input_item["contextual_id"],
                             input_item.get("entity_reference"), input_item.get("attribute_reference"),
                             input_item["source"]["type"], input_item["source"]["description"], input_item["required"])
                        )
                    except Exception as e:
                        print(f"❌ Error inserting input item {input_item['id']} for {go['id']}: {e}")

                # 4️⃣ Insert System Functions for Input Stack
                for function in go["input_stack"].get("system_functions", []):
                    try:
                        pg_cursor.execute(
                            """
                            INSERT INTO workflow_solution_temp.system_functions 
                            (function_id, function_name, function_type, stack_type, stack_id, parameters, output_to) 
                            VALUES (%s, %s, %s, %s, %s, %s, %s)
                            """,
                            (function["function_id"], function["function_name"], function["function_type"],
                             "input", input_stack_id, json.dumps(function.get("parameters", {})), function.get("output_to"))
                        )
                    except Exception as e:
                        print(f"❌ Error inserting system function {function['function_id']} for {go['id']}: {e}")

            except Exception as e:
                print(f"❌ Error inserting input stack for {go['id']}: {e}")

        # 5️⃣ Insert Output Stack
        if "output_stack" in go:
            try:
                pg_cursor.execute(
                    """
                    INSERT INTO workflow_solution_temp.output_stack (go_id, description) 
                    VALUES (%s, %s) RETURNING id
                    """,
                    (global_objective_id, go["output_stack"]["description"])
                )
                output_stack_id = pg_cursor.fetchone()[0]

                # 6️⃣ Insert Output Items
                for output_item in go["output_stack"]["outputs"]:
                    try:
                        pg_cursor.execute(
                            """
                            INSERT INTO workflow_solution_temp.output_items 
                            (id, output_stack_id, slot_id, contextual_id, output_entity, output_attribute, data_type) 
                            VALUES (%s, %s, %s, %s, %s, %s, %s)
                            """,
                            (output_item["id"], output_stack_id, output_item["slot_id"], output_item["contextual_id"],
                             output_item.get("output_entity"), output_item.get("output_attribute"), output_item["data_type"])
                        )
                    except Exception as e:
                        print(f"❌ Error inserting output item {output_item['id']} for {go['id']}: {e}")

                # 7️⃣ Insert Output Triggers
                for output_item in go["output_stack"]["outputs"]:
                    if "triggers" in output_item:
                        for trigger in output_item["triggers"]["items"]:
                            try:
                                pg_cursor.execute(
                                    """
                                    INSERT INTO workflow_solution_temp.output_triggers 
                                    (id, output_item_id, output_stack_id, target_objective, target_input, mapping_type, 
                                     condition_type, condition_entity, condition_attribute, condition_operator, condition_value) 
                                    VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                                    """,
                                    (trigger["id"], output_item["id"], output_stack_id, go['id'],
                                     trigger["target_input"], trigger["mapping_type"],
                                     trigger.get("condition", {}).get("condition_type"),
                                     trigger.get("condition", {}).get("entity"),
                                     trigger.get("condition", {}).get("attribute"),
                                     trigger.get("condition", {}).get("operator"),
                                     trigger.get("condition", {}).get("value"))
                                )
                            except Exception as e:
                                print(f"❌ Error inserting output trigger {trigger['id']} for {go['id']}: {e}")

            except Exception as e:
                print(f"❌ Error inserting output stack for {go['id']}: {e}")
        # 1️⃣ Insert Data Mapping Stack
        if "data_mapping_stack" in go:
            try:
                pg_cursor.execute(
                    """
                    INSERT INTO workflow_solution_temp.data_mapping_stack (go_id, description) 
                    VALUES (%s, %s) RETURNING id
                    """,
                    (go["id"], go["data_mapping_stack"]["description"])
                )
                mapping_stack_id = pg_cursor.fetchone()[0]

                # 2️⃣ Insert Data Mappings
                for mapping in go["data_mapping_stack"].get("mappings", []):
                    try:
                        pg_cursor.execute(
                            """
                            INSERT INTO workflow_solution_temp.data_mappings 
                            (id, mapping_stack_id, source, target, mapping_type) 
                            VALUES (%s, %s, %s, %s, %s)
                            """,
                            (mapping["id"], mapping_stack_id, mapping["source"], mapping["target"], mapping["mapping_type"])
                        )
                    except Exception as e:
                        print(f"❌ Error inserting data mapping {mapping['id']} for {go['id']}: {e}")

                # 3️⃣ Insert Mapping Rules
                for rule in go["data_mapping_stack"].get("rules", []):
                    try:
                        pg_cursor.execute(
                            """
                            INSERT INTO workflow_solution_temp.mapping_rules 
                            (id, mapping_stack_id, description, condition_type, condition_entity, 
                             condition_attribute, condition_operator, condition_value, error_message) 
                            VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s)
                            """,
                            (rule["id"], mapping_stack_id, rule["description"],
                             rule["rule_condition"]["condition_type"], rule["rule_condition"]["entity"],
                             rule["rule_condition"]["attribute"], rule["rule_condition"]["operator"],
                             rule["rule_condition"]["value"], rule["error_message"])
                        )
                    except Exception as e:
                        print(f"❌ Error inserting mapping rule {rule['id']} for {go['id']}: {e}")

            except Exception as e:
                print(f"❌ Error inserting data mapping stack for {go['id']}: {e}")

    except Exception as e:
        print(f"❌ Error processing Global Objective {go['id']}: {e}")


print("\n➡️ Inserting Local Objectives and Associated Data...")
for lo in workflow_data.get("local_objectives", []):
    try:
        # 1️⃣ Insert Local Objective
        go_id = lo["contextual_id"].split('.')[0]  # Extract Global Objective ID from Contextual ID
        workflow_source = lo.get("workflow_source", None)  # Set default to None if missing

        pg_cursor.execute(
            """
            INSERT INTO workflow_solution_temp.local_objectives 
            (lo_id, contextual_id, name, function_type, workflow_source, go_id) 
            VALUES (%s, %s, %s, %s, %s, %s) 
            RETURNING lo_id
            """,
            (lo["id"], lo["contextual_id"], lo["name"], lo["function_type"], workflow_source,global_objective_id)
        )
        lo_id = pg_cursor.fetchone()[0]
        print(f"✅ Inserted Local Objective: {lo['id']}")
    except Exception as e:
                print(f"❌ Error inserting Local Objective for {lo['id']}: {e}")    

for lo in workflow_data.get("local_objectives", []):
    try:
        lo_id = lo['id']
#        print(f"✅ Inserted Local Objective: {lo['id']}")

        # 2️⃣ Insert Execution Pathway if Exists
        if "execution_pathway" in lo:
            try:
                pg_cursor.execute(
                    """
                    INSERT INTO workflow_solution_temp.execution_pathways (lo_id, pathway_type, next_lo) 
                    VALUES (%s, %s, %s) 
                    ON CONFLICT (lo_id) DO NOTHING;
                    """,
                    (lo["id"], lo["execution_pathway"]["type"], lo["execution_pathway"].get("next_lo"))
                )
                execution_pathway = lo.get("execution_pathway", {})
                print(f"✅ Inserted Execution Pathway for {lo['id']}")
            except Exception as e:
                print(f"❌ Error inserting Execution Pathway for {lo['id']}: {e}")

        # 3️⃣ Insert Terminal Pathway if Exists
        if "terminal_type" in lo:
            try:
                pg_cursor.execute(
                    """
                    INSERT INTO workflow_solution_temp.terminal_pathways (lo_id, terminal_type) 
                    VALUES (%s, %s) 
                    ON CONFLICT (lo_id) DO NOTHING;
                    """,
                    (lo["id"], lo["terminal_type"])
                )
                print(f"✅ Inserted Terminal Pathway for {lo['id']}")
            except Exception as e:
                print(f"❌ Error inserting Terminal Pathway for {lo['id']}: {e}")

        # 4️⃣ Insert Execution Pathway Conditions
        if "conditions" in execution_pathway:
            for condition in execution_pathway["conditions"]:
               condition_data = condition.get("condition", {})
               next_lo = condition.get("next_lo")

            try:
                pg_cursor.execute(
                    """
                    INSERT INTO workflow_solution_temp.execution_pathway_conditions 
                    (lo_id, condition_type, condition_entity, condition_attribute, 
                    condition_operator, condition_value, next_lo) 
                    VALUES (%s, %s, %s, %s, %s, %s, %s)
                    """,
                    (
                        lo_id,
                        condition_data.get("condition_type"),
                        condition_data.get("entity"),
                        condition_data.get("attribute"),
                        condition_data.get("operator"),
                        condition_data.get("value"),
                        next_lo,
                    ),
                )
                print(f"✅ Inserted Execution Pathway Condition for {lo_id} → Next: {next_lo}")
            except Exception as e:
                print(f"❌ Error inserting execution pathway condition for {lo_id}: {e}")
 # 3️⃣ Insert Agent Stack
        if "agent_stack" in lo:
            try:
                pg_cursor.execute(
                    "INSERT INTO workflow_solution_temp.agent_stack (lo_id) VALUES (%s) RETURNING id",
                    (lo_id,)
                )
                agent_stack_id = pg_cursor.fetchone()[0]

                # 4️⃣ Insert Agent Rights
                for agent in lo["agent_stack"].get("agents", []):
                    for right in agent.get("rights", []):
                        try:
                            pg_cursor.execute(
                                """
                                INSERT INTO workflow_solution_temp.agent_rights 
                                (agent_stack_id, role_id, right_id) 
                                VALUES (%s, %s, %s)
                                """,
                                (agent_stack_id, agent["role"], right)
                            )
                        except Exception as e:
                            print(f"❌ Error inserting agent right {right} for {lo['id']}: {e}")
            except Exception as e:
                print(f"❌ Error inserting agent stack for {lo['id']}: {e}")

        # 5️⃣ Insert Input Stack
        if "input_stack" in lo:
            try:
                pg_cursor.execute(
                    """
                    INSERT INTO workflow_solution_temp.lo_input_stack (lo_id, description) 
                    VALUES (%s, %s) RETURNING id
                    """,
                    (lo_id, lo["input_stack"]["description"])
                )
                input_stack_id = pg_cursor.fetchone()[0]

                # 6️⃣ Insert Input Items
                for input_item in lo["input_stack"]["inputs"]:
                    try:
                        pg_cursor.execute(
                            """
                            INSERT INTO workflow_solution_temp.lo_input_items 
                            (id, input_stack_id, slot_id, contextual_id, source_type, source_description, required) 
                            VALUES (%s, %s, %s, %s, %s, %s, %s)
                            """,
                            (input_item["id"], input_stack_id, input_item["slot_id"], input_item["contextual_id"],
                             input_item["source"]["type"], input_item["source"]["description"], input_item["required"])
                        )
                    except Exception as e:
                        print(f"❌ Error inserting input item {input_item['id']} for {lo['id']}: {e}")

            except Exception as e:
                print(f"❌ Error inserting input stack for {lo['id']}: {e}")

        # 7️⃣ Insert Execution Rules
        for rule in lo.get("execution_rules", []):
            try:
                pg_cursor.execute(
                    """
                    INSERT INTO workflow_solution_temp.execution_rules 
                    (id, lo_id, contextual_id, description, structured_rule) 
                    VALUES (%s, %s, %s, %s, %s)
                    """,
                    (rule["id"], lo_id, rule["contextual_id"], rule["description"], json.dumps(rule["structured_rule"]))
                )
            except Exception as e:
                print(f"❌ Error inserting execution rule {rule['id']} for {lo['id']}: {e}")

        # 8️⃣ Insert Output Stack
        if "output_stack" in lo:
            try:
                pg_cursor.execute(
                    """
                    INSERT INTO workflow_solution_temp.lo_output_stack (lo_id, description) 
                    VALUES (%s, %s) RETURNING id
                    """,
                    (lo_id, lo["output_stack"]["description"])
                )
                output_stack_id = pg_cursor.fetchone()[0]

                # 9️⃣ Insert Output Items
                for output_item in lo["output_stack"]["outputs"]:
                    try:
                        pg_cursor.execute(
                            """
                            INSERT INTO workflow_solution_temp.lo_output_items 
                            (id, output_stack_id, slot_id, contextual_id, source)                             (output_item["id"], output_stack_id, output_item["slot_id"], output_item["contextual_id"], json.dumps(output_item.get("source")))
                            VALUES (%s, %s, %s, %s, %s)
                            """,
                            (output_item["id"], output_stack_id, output_item["slot_id"], output_item["contextual_id"], json.dumps(output_item.get("source")))                        )
                    except Exception as e:
                        print(f"❌ Error inserting output item {output_item['id']} for {lo['id']}: {e}")

            except Exception as e:
                print(f"❌ Error inserting output stack for {lo['id']}: {e}")

    except Exception as e:
        print(f"❌ Error processing Local Objective {lo['id']}: {e}")
# ✅ Fetch and Display Inserted Data
print("\n🔎 Verifying Data in PostgreSQL...")

def print_table(table_name):
    print(f"\n📌 {table_name}:")
    pg_cursor.execute(f"SELECT * FROM workflow_solution_temp.{table_name};")
    print(pg_cursor.fetchall())

tables = [
"permission_types","output_triggers", "output_items", "output_stack",
 "roles","entity_permissions", "objective_permissions",
    "system_functions", "input_items", "input_stack",
    "data_mappings", "mapping_rules", "data_mapping_stack",
    "global_objectives","agent_rights", "agent_stack", "execution_pathway_conditions",
    "terminal_pathways", "execution_pathways", "local_objectives", "tenants", "permission_types",
    "roles", "entity_permissions", "objective_permissions", "entities", "entity_attribute_metadata",
    "entity_attributes", "attribute_validations", "attribute_enum_values", "entity_relationships","execution_pathway_conditions"
]

for table in tables:
    print_table(table)

# ✅ Close Connections
pg_cursor.close()
pg_conn.close()
mongo_client.close()

print("\n✅ Migration Completed Successfully!")
