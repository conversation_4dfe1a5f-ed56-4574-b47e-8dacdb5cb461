#!/usr/bin/env python3
"""
Test database connections for the Workflow System.
"""

import sys
import os
import logging

# Add the parent directory to the path so we can import app modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Import database connections
from app.db.mongo_db import get_collection
from app.db.postgres_db import get_connection
from app.db.redis_db import get_redis_client

def test_mongodb_connection():
    """Test connection to MongoDB."""
    try:
        collection = get_collection("configuration_versions")
        count = collection.count_documents({})
        logger.info(f"Successfully connected to MongoDB. Found {count} documents in configuration_versions collection.")
        return True
    except Exception as e:
        logger.error(f"Failed to connect to MongoDB: {e}")
        return False

def test_postgres_connection():
    """Test connection to PostgreSQL."""
    try:
        conn = get_connection()
        cursor = conn.cursor()
        cursor.execute("SELECT 1")
        result = cursor.fetchone()
        cursor.close()
        conn.close()
        logger.info(f"Successfully connected to PostgreSQL. Test query result: {result}")
        return True
    except Exception as e:
        logger.error(f"Failed to connect to PostgreSQL: {e}")
        return False

def test_redis_connection():
    """Test connection to Redis."""
    try:
        redis_client = get_redis_client()
        redis_client.ping()
        logger.info("Successfully connected to Redis.")
        return True
    except Exception as e:
        logger.error(f"Failed to connect to Redis: {e}")
        return False

if __name__ == "__main__":
    logger.info("Testing database connections...")
    
    mongodb_ok = test_mongodb_connection()
    postgres_ok = test_postgres_connection()
    redis_ok = test_redis_connection()
    
    if mongodb_ok and postgres_ok and redis_ok:
        logger.info("All database connections successful!")
        sys.exit(0)
    else:
        logger.error("Some database connections failed. See logs for details.")
        sys.exit(1)
