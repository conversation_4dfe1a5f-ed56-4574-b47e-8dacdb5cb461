-- RBAC Schema Migration Script for Workflow System
-- This script creates the necessary tables for the RBAC implementation

-- Create schema if it doesn't exist
CREATE SCHEMA IF NOT EXISTS workflow_runtime;

-- Users table
CREATE TABLE IF NOT EXISTS workflow_runtime.users (
    user_id VARCHAR(36) PRIMARY KEY,
    username VARCHAR(255) NOT NULL UNIQUE,
    email VA<PERSON>HAR(255) NOT NULL UNIQUE,
    password_hash VARCHAR(255) NOT NULL,
    first_name VA<PERSON>HA<PERSON>(255),
    last_name <PERSON><PERSON><PERSON><PERSON>(255),
    status VARCHAR(50) DEFAULT 'active',
    last_login TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Roles table
CREATE TABLE IF NOT EXISTS workflow_runtime.roles (
    role_id VARCHAR(36) PRIMARY KEY,
    name <PERSON><PERSON><PERSON><PERSON>(255) NOT NULL UNIQUE,
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- User roles table
CREATE TABLE IF NOT EXISTS workflow_runtime.user_roles (
    user_id VARCHAR(36) NOT NULL,
    username VARCHAR(255) NOT NULL,
    role VARCHAR(255) NOT NULL,
    tenant_id VARCHAR(36) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (user_id, role, tenant_id),
    FOREIGN KEY (user_id) REFERENCES workflow_runtime.users(user_id) ON DELETE CASCADE
);

-- OAuth tokens table
CREATE TABLE IF NOT EXISTS workflow_runtime.user_oauth_tokens (
    token_id VARCHAR(36) PRIMARY KEY,
    user_id VARCHAR(36) NOT NULL,
    access_token TEXT NOT NULL,
    refresh_token TEXT NOT NULL,
    expires_at TIMESTAMP NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES workflow_runtime.users(user_id) ON DELETE CASCADE
);

-- User sessions table
CREATE TABLE IF NOT EXISTS workflow_runtime.user_sessions (
    session_id VARCHAR(36) PRIMARY KEY,
    user_id VARCHAR(36) NOT NULL,
    token TEXT NOT NULL,
    ip_address VARCHAR(45),
    user_agent TEXT,
    expires_at TIMESTAMP NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES workflow_runtime.users(user_id) ON DELETE CASCADE
);

-- Permission contexts table
CREATE TABLE IF NOT EXISTS workflow_runtime.permission_contexts (
    context_id VARCHAR(36) PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    context_type VARCHAR(50) NOT NULL,
    context_rules JSONB NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Permission types table
CREATE TABLE IF NOT EXISTS workflow_runtime.permission_types (
    permission_id VARCHAR(36) PRIMARY KEY,
    name VARCHAR(255) NOT NULL UNIQUE,
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Role permissions table
CREATE TABLE IF NOT EXISTS workflow_runtime.role_permissions (
    role_id VARCHAR(36) NOT NULL,
    context_id VARCHAR(36) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (role_id, context_id),
    FOREIGN KEY (role_id) REFERENCES workflow_runtime.roles(role_id) ON DELETE CASCADE,
    FOREIGN KEY (context_id) REFERENCES workflow_runtime.permission_contexts(context_id) ON DELETE CASCADE
);

-- Organizational units table
CREATE TABLE IF NOT EXISTS workflow_runtime.organizational_units (
    org_unit_id VARCHAR(36) PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    parent_org_unit_id VARCHAR(36),
    tenant_id VARCHAR(36) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (parent_org_unit_id) REFERENCES workflow_runtime.organizational_units(org_unit_id) ON DELETE SET NULL
);

-- User organizations table
CREATE TABLE IF NOT EXISTS workflow_runtime.user_organizations (
    user_id VARCHAR(36) NOT NULL,
    org_unit_id VARCHAR(36) NOT NULL,
    is_primary BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (user_id, org_unit_id),
    FOREIGN KEY (user_id) REFERENCES workflow_runtime.users(user_id) ON DELETE CASCADE,
    FOREIGN KEY (org_unit_id) REFERENCES workflow_runtime.organizational_units(org_unit_id) ON DELETE CASCADE
);

-- Tenants table
CREATE TABLE IF NOT EXISTS workflow_runtime.tenants (
    tenant_id VARCHAR(36) PRIMARY KEY,
    name VARCHAR(255) NOT NULL UNIQUE,
    description TEXT,
    status VARCHAR(50) DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create entity definitions for users and roles
INSERT INTO workflow_runtime.entities (entity_id, name, description, type, is_system_entity, status)
VALUES 
    ('user', 'user', 'User entity for authentication and authorization', 'auth', true, 'active'),
    ('role', 'role', 'Role entity for authorization', 'auth', true, 'active')
ON CONFLICT (entity_id) DO NOTHING;

-- Create attribute definitions for user entity
INSERT INTO workflow_runtime.entity_attributes (attribute_id, entity_id, name, display_name, datatype, required, status)
VALUES 
    ('user_id', 'user', 'user_id', 'User ID', 'string', true, 'active'),
    ('username', 'user', 'username', 'Username', 'string', true, 'active'),
    ('email', 'user', 'email', 'Email', 'string', true, 'active'),
    ('first_name', 'user', 'first_name', 'First Name', 'string', false, 'active'),
    ('last_name', 'user', 'last_name', 'Last Name', 'string', false, 'active'),
    ('status', 'user', 'status', 'Status', 'string', true, 'active'),
    ('roles', 'user', 'roles', 'Roles', 'array', false, 'active')
ON CONFLICT (attribute_id) DO NOTHING;

-- Create attribute definitions for role entity
INSERT INTO workflow_runtime.entity_attributes (attribute_id, entity_id, name, display_name, datatype, required, status)
VALUES 
    ('role_id', 'role', 'role_id', 'Role ID', 'string', true, 'active'),
    ('name', 'role', 'name', 'Name', 'string', true, 'active'),
    ('description', 'role', 'description', 'Description', 'string', false, 'active'),
    ('permissions', 'role', 'permissions', 'Permissions', 'array', false, 'active')
ON CONFLICT (attribute_id) DO NOTHING;

-- Create default tenant
INSERT INTO workflow_runtime.tenants (tenant_id, name)
VALUES 
    ('default', 'Default Tenant')
ON CONFLICT (tenant_id) DO NOTHING;

-- Create default roles
INSERT INTO workflow_runtime.roles (role_id, name, tenant_id)
VALUES 
    ('admin', 'Administrator', 'default'),
    ('manager', 'Manager', 'default'),
    ('user', 'User', 'default')
ON CONFLICT DO NOTHING;

-- Create default permission types
INSERT INTO workflow_runtime.permission_types (permission_id, description)
VALUES 
    ('read', 'Permission to read resources'),
    ('write', 'Permission to create and update resources'),
    ('delete', 'Permission to delete resources'),
    ('execute', 'Permission to execute functions and workflows'),
    ('manage', 'Permission to manage resources'),
    ('admin', 'Full administrative access to resources')
ON CONFLICT (permission_id) DO NOTHING;

-- Create default permission contexts
INSERT INTO workflow_runtime.permission_contexts (context_id, name, description, context_type, context_rules)
VALUES 
    ('global_admin', 'Global Admin', 'Global administrative access', 'global', '{"resource_id": "*", "permissions": ["admin"]}'),
    ('workflow_execute', 'Workflow Execute', 'Permission to execute workflows', 'function', '{"resource_id": "*", "permissions": ["execute"]}'),
    ('entity_read', 'Entity Read', 'Permission to read entities', 'entity', '{"resource_id": "*", "permissions": ["read"]}'),
    ('entity_write', 'Entity Write', 'Permission to write entities', 'entity', '{"resource_id": "*", "permissions": ["write"]}'),
    ('entity_delete', 'Entity Delete', 'Permission to delete entities', 'entity', '{"resource_id": "*", "permissions": ["delete"]}')
ON CONFLICT (context_id) DO NOTHING;

-- Assign permissions to roles
INSERT INTO workflow_runtime.role_permissions (role_id, context_id)
VALUES 
    ('r003', 'global_admin'),
    ('r003', 'workflow_execute'),
    ('r003', 'entity_read'),
    ('r003', 'entity_write'),
    ('r003', 'entity_delete'),
    ('r002', 'workflow_execute'),
    ('r002', 'entity_read'),
    ('r002', 'entity_write'),
    ('r001', 'entity_read')
ON CONFLICT (role_id, context_id) DO NOTHING;

-- Create default organizational unit
INSERT INTO workflow_runtime.organizational_units (org_unit_id, name, description, tenant_id)
VALUES 
    ('default', 'Default Organization', 'Default organizational unit', 'default')
ON CONFLICT (org_unit_id) DO NOTHING;

-- Create admin user (password: admin123)
INSERT INTO workflow_runtime.users (user_id, username, email, password_hash, first_name, last_name, status)
VALUES 
    ('admin', 'admin', '<EMAIL>', '$2b$12$1tJXSR/YKBYZZDCvUVU0wOJTAz0Zb6WTgHvMf5MQzLxr9Hf.PXcHi', 'Admin', 'User', 'active')
ON CONFLICT (user_id) DO NOTHING;

-- Assign admin role to admin user
INSERT INTO workflow_runtime.user_roles (user_id, username, role, tenant_id)
VALUES 
    ('admin', 'admin', 'Administrator', 'default')
ON CONFLICT (user_id) DO NOTHING;

-- Assign admin user to default organization
INSERT INTO workflow_runtime.user_organizations (user_id, org_unit_id, is_primary)
VALUES 
    ('admin', 'default', true)
ON CONFLICT (user_id, org_unit_id) DO NOTHING;
