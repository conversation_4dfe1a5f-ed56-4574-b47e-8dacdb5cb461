
-- === Tenants and Roles ===
CREATE TABLE tenants (
    tenant_id VARCHAR(50) PRIMARY KEY,
    name VARCHAR(255) NOT NULL
);

CREATE TABLE roles (
    role_id VARCHAR(50) PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    tenant_id VARCHAR(50) REFERENCES tenants(tenant_id),
    inherits_from VARCHAR(50) REFERENCES roles(role_id)
);

CREATE TABLE permission_types (
    permission_id SERIAL PRIMARY KEY,
    permission VARCHAR(50) NOT NULL,
    description TEXT,
    capabilities JSONB
);

-- === Entities and Attributes ===
CREATE TABLE entities (
    entity_id VARCHAR(50) PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    version VARCHAR(20) DEFAULT '1.0',
    status VARCHAR(50) NOT NULL,
    type VARCHAR(50) NOT NULL,
    attribute_prefix VARCHAR(10),
    description TEXT
);

CREATE TABLE entity_attribute_metadata (
    id SERIAL PRIMARY KEY,
    entity_id VARCHAR(50) REFERENCES entities(entity_id),
    attribute_id VARCHAR(50) NOT NULL,
    attribute_name VARCHAR(255) NOT NULL,
    required BOOLEAN DEFAULT FALSE,
    UNIQUE(entity_id, attribute_id)
);

CREATE TABLE entity_attributes (
    id SERIAL PRIMARY KEY,
    attribute_id VARCHAR(50) NOT NULL,
    entity_id VARCHAR(50) REFERENCES entities(entity_id),
    name VARCHAR(255) NOT NULL,
    display_name VARCHAR(255) NOT NULL,
    datatype VARCHAR(50) NOT NULL,
    version VARCHAR(20) DEFAULT '1.0',
    status VARCHAR(50) NOT NULL,
    required BOOLEAN DEFAULT FALSE,
    reference_entity_id VARCHAR(50) REFERENCES entities(entity_id),
    UNIQUE(entity_id, attribute_id)
);

CREATE TABLE attribute_validations (
    id SERIAL PRIMARY KEY,
    attribute_id VARCHAR(50) NOT NULL,
    rule VARCHAR(255) NOT NULL,
    expression TEXT
);

CREATE TABLE attribute_enum_values (
    id SERIAL PRIMARY KEY,
    attribute_id VARCHAR(50) NOT NULL,
    value VARCHAR(255) NOT NULL,
    UNIQUE(attribute_id, value)
);

CREATE TABLE entity_relationships (
    id SERIAL PRIMARY KEY,
    source_entity_id VARCHAR(50) REFERENCES entities(entity_id),
    target_entity_id VARCHAR(50) REFERENCES entities(entity_id),
    relationship_type VARCHAR(50) NOT NULL,
    source_attribute_id VARCHAR(255) NOT NULL,
    target_attribute_id VARCHAR(255) NOT NULL
);

CREATE TABLE entity_permissions (
    id SERIAL PRIMARY KEY,
    role_id VARCHAR(50) REFERENCES roles(role_id),
    entity_id VARCHAR(50) REFERENCES entities(entity_id),
    permission_id VARCHAR(50),
    UNIQUE(role_id, entity_id, permission_id)
);

-- === Global Objectives ===
CREATE TABLE global_objectives (
    id SERIAL PRIMARY KEY,
    go_id VARCHAR(50) UNIQUE NOT NULL,
    name VARCHAR(255) NOT NULL,
    version VARCHAR(20) DEFAULT '1.0',
    status VARCHAR(50) NOT NULL,
    description TEXT
);

CREATE TABLE objective_permissions (
    id SERIAL PRIMARY KEY,
    role_id VARCHAR(50) REFERENCES roles(role_id),
    objective_id VARCHAR(50),
    permission_id VARCHAR(50)
);

CREATE TABLE input_stack (
    id SERIAL PRIMARY KEY,
    go_id VARCHAR(50) REFERENCES global_objectives(go_id),
    description TEXT
);

CREATE TABLE input_items (
    id VARCHAR(50) NOT NULL,
    input_stack_id INTEGER REFERENCES input_stack(id),
    slot_id VARCHAR(255) NOT NULL,
    contextual_id VARCHAR(255) NOT NULL,
    entity_reference VARCHAR(50),
    attribute_reference VARCHAR(50),
    source_type VARCHAR(50) NOT NULL,
    source_description TEXT,
    required BOOLEAN DEFAULT FALSE,
    PRIMARY KEY(id, input_stack_id)
);

CREATE TABLE system_functions (
    id SERIAL PRIMARY KEY,
    function_id VARCHAR(50) NOT NULL,
    function_name VARCHAR(255) NOT NULL,
    function_type VARCHAR(50) NOT NULL,
    stack_type VARCHAR(20) NOT NULL,
    stack_id INTEGER NOT NULL,
    parameters JSONB,
    output_to VARCHAR(255)
);

CREATE TABLE output_stack (
    id SERIAL PRIMARY KEY,
    go_id VARCHAR(50) REFERENCES global_objectives(go_id),
    description TEXT
);

CREATE TABLE output_items (
    id VARCHAR(50) NOT NULL,
    output_stack_id INTEGER REFERENCES output_stack(id),
    slot_id VARCHAR(255) NOT NULL,
    contextual_id VARCHAR(255) NOT NULL,
    output_entity VARCHAR(50),
    output_attribute VARCHAR(50),
    data_type VARCHAR(50) NOT NULL,
    PRIMARY KEY(id, output_stack_id)
);

CREATE TABLE output_triggers (
    id VARCHAR(50) NOT NULL,
    output_item_id VARCHAR(50) NOT NULL,
    output_stack_id INTEGER REFERENCES output_stack(id),
    target_objective VARCHAR(50) NOT NULL,
    target_input VARCHAR(255) NOT NULL,
    mapping_type VARCHAR(50) NOT NULL,
    condition_type VARCHAR(50),
    condition_entity VARCHAR(50),
    condition_attribute VARCHAR(50),
    condition_operator VARCHAR(20),
    condition_value VARCHAR(255),
    PRIMARY KEY(id, output_stack_id,output_item_id)
);


CREATE TABLE data_mapping_stack (
    id SERIAL PRIMARY KEY,
    go_id VARCHAR(50) REFERENCES global_objectives(go_id),
    description TEXT
);

CREATE TABLE data_mappings (
    id VARCHAR(50) NOT NULL,
    mapping_stack_id INTEGER REFERENCES data_mapping_stack(id),
    source VARCHAR(255) NOT NULL,
    target VARCHAR(255) NOT NULL,
    mapping_type VARCHAR(50) NOT NULL,
    PRIMARY KEY(id, mapping_stack_id)
);

CREATE TABLE mapping_rules (
    id VARCHAR(50) NOT NULL,
    mapping_stack_id INTEGER REFERENCES data_mapping_stack(id),
    description TEXT,
    condition_type VARCHAR(50) NOT NULL,
    condition_entity VARCHAR(50) NOT NULL,
    condition_attribute VARCHAR(50) NOT NULL,
    condition_operator VARCHAR(20) NOT NULL,
    condition_value VARCHAR(255) NOT NULL,
    error_message TEXT,
    PRIMARY KEY(id, mapping_stack_id)
);

CREATE TABLE runtime_metrics_stack (
    id SERIAL PRIMARY KEY,
    go_id VARCHAR(50) REFERENCES global_objectives(go_id),
    description TEXT,
    metrics_entity VARCHAR(50) REFERENCES entities(entity_id)
);

CREATE TABLE metrics_aggregation (
    id SERIAL PRIMARY KEY,
    metrics_stack_id INTEGER REFERENCES runtime_metrics_stack(id),
    attribute_name VARCHAR(255) NOT NULL,
    aggregation_function VARCHAR(50) NOT NULL
);

CREATE TABLE execution_path_tracking (
    id SERIAL PRIMARY KEY,
    metrics_stack_id INTEGER REFERENCES runtime_metrics_stack(id),
    enabled BOOLEAN DEFAULT TRUE,
    store_complete_path BOOLEAN DEFAULT TRUE,
    identify_bottlenecks BOOLEAN DEFAULT FALSE,
    compare_to_historical BOOLEAN DEFAULT FALSE,
    path_efficiency_function VARCHAR(255)
);

CREATE TABLE metrics_reporting (
    id SERIAL PRIMARY KEY,
    metrics_stack_id INTEGER REFERENCES runtime_metrics_stack(id),
    generate_execution_summary BOOLEAN DEFAULT TRUE,
    store_summary_location VARCHAR(255),
    error_count_threshold INTEGER,
    duration_percentile FLOAT
);

-- === Local Objectives ===
CREATE TABLE local_objectives (
    id SERIAL PRIMARY KEY,
    lo_id VARCHAR(50) UNIQUE NOT NULL,
    contextual_id VARCHAR(255) NOT NULL,
    name VARCHAR(255) NOT NULL,
    function_type VARCHAR(50) NOT NULL,
    workflow_source VARCHAR(50),
    go_id VARCHAR(50) REFERENCES global_objectives(go_id)
);

CREATE TABLE execution_pathways (
    id SERIAL PRIMARY KEY,
    lo_id VARCHAR(50) REFERENCES local_objectives(lo_id) UNIQUE,
    pathway_type VARCHAR(50) NOT NULL,
    next_lo VARCHAR(50)
);

CREATE TABLE execution_pathway_conditions (
    id SERIAL PRIMARY KEY,
    lo_id VARCHAR(50) REFERENCES local_objectives(lo_id),
    condition_type VARCHAR(50),
    condition_entity VARCHAR(50),
    condition_attribute VARCHAR(50),
    condition_operator VARCHAR(20),
    condition_value VARCHAR(255),
    next_lo VARCHAR(50)
);

CREATE TABLE terminal_pathways (
    id SERIAL PRIMARY KEY,
    lo_id VARCHAR(50) REFERENCES local_objectives(lo_id) UNIQUE,
    terminal_type VARCHAR(50) NOT NULL
);

CREATE TABLE agent_stack (
    id SERIAL PRIMARY KEY,
    lo_id VARCHAR(50) REFERENCES local_objectives(lo_id) UNIQUE
);

CREATE TABLE agent_rights (
    id SERIAL PRIMARY KEY,
    agent_stack_id INTEGER REFERENCES agent_stack(id),
    role_id VARCHAR(50) REFERENCES roles(role_id),
    right_id VARCHAR(50) NOT NULL
);

CREATE TABLE lo_input_stack (
    id SERIAL PRIMARY KEY,
    lo_id VARCHAR(50) REFERENCES local_objectives(lo_id) UNIQUE,
    description TEXT
);

CREATE TABLE lo_input_items (
    id VARCHAR(50) NOT NULL,
    input_stack_id INTEGER REFERENCES lo_input_stack(id),
    slot_id VARCHAR(255) NOT NULL,
    contextual_id VARCHAR(255) NOT NULL,
    source_type VARCHAR(50) NOT NULL,
    source_description TEXT,
    required BOOLEAN DEFAULT FALSE,
    data_type VARCHAR(50),
    ui_control VARCHAR(50),
    nested_function JSONB,
    nested_functions JSONB,
    PRIMARY KEY(id, input_stack_id)
);

CREATE TABLE lo_input_validations (
    id SERIAL PRIMARY KEY,
    input_item_id VARCHAR(50) NOT NULL,
    input_stack_id INTEGER REFERENCES lo_input_stack(id),
    rule VARCHAR(255) NOT NULL,
    rule_type VARCHAR(50) NOT NULL,
    entity VARCHAR(50),
    attribute VARCHAR(50),
    validation_method VARCHAR(50),
    reference_date_source VARCHAR(255),
    allowed_values JSONB,
    error_message TEXT
);

CREATE TABLE lo_system_functions (
    id SERIAL PRIMARY KEY,
    function_id VARCHAR(50) NOT NULL,
    lo_id VARCHAR(50) REFERENCES local_objectives(lo_id),
    function_name VARCHAR(255) NOT NULL,
    function_type VARCHAR(50) NOT NULL,
    parameters JSONB,
    output_to VARCHAR(255)
);

CREATE TABLE execution_rules (
    id VARCHAR(50) NOT NULL,
    lo_id VARCHAR(50) REFERENCES local_objectives(lo_id),
    contextual_id VARCHAR(255) NOT NULL,
    description TEXT,
    structured_rule JSONB,
    rule_condition JSONB,
    action JSONB,
    PRIMARY KEY(id, lo_id)
);

CREATE TABLE success_message_template (
    id SERIAL PRIMARY KEY,
    lo_id VARCHAR(50) REFERENCES local_objectives(lo_id) UNIQUE,
    message_type VARCHAR(50) NOT NULL,
    format_string TEXT NOT NULL,
    format_parameters JSONB
);

CREATE TABLE success_messages (
    id SERIAL PRIMARY KEY,
    lo_id VARCHAR(50) REFERENCES local_objectives(lo_id) UNIQUE,
    message TEXT NOT NULL
);

CREATE TABLE lo_output_stack (
    id SERIAL PRIMARY KEY,
    lo_id VARCHAR(50) REFERENCES local_objectives(lo_id) UNIQUE,
    description TEXT
);

CREATE TABLE lo_output_items (
    id VARCHAR(50) NOT NULL,
    output_stack_id INTEGER REFERENCES lo_output_stack(id),
    slot_id VARCHAR(255) NOT NULL,
    contextual_id VARCHAR(255) NOT NULL,
    source TEXT,
    value VARCHAR(255),
    output_entity VARCHAR(50),
    output_attribute VARCHAR(50),
    required BOOLEAN DEFAULT FALSE,
    data_type VARCHAR(50),
    PRIMARY KEY(id, output_stack_id)
);

CREATE TABLE lo_output_triggers (
    id VARCHAR(50) NOT NULL,
    output_item_id VARCHAR(50) NOT NULL,
    output_stack_id INTEGER REFERENCES lo_output_stack(id),
    target_objective VARCHAR(50) NOT NULL,
    target_input VARCHAR(255) NOT NULL,
    mapping_type VARCHAR(50) NOT NULL,
    PRIMARY KEY(id, output_stack_id)
);

CREATE TABLE lo_data_mapping_stack (
    id SERIAL PRIMARY KEY,
    lo_id VARCHAR(50) REFERENCES local_objectives(lo_id) UNIQUE,
    description TEXT
);

CREATE TABLE lo_data_mappings (
    id VARCHAR(50) NOT NULL,
    mapping_stack_id INTEGER REFERENCES lo_data_mapping_stack(id),
    source VARCHAR(255) NOT NULL,
    target VARCHAR(255) NOT NULL,
    mapping_type VARCHAR(50) NOT NULL,
    PRIMARY KEY(id, mapping_stack_id)
);

CREATE TABLE ui_stack (
    id SERIAL PRIMARY KEY,
    lo_id VARCHAR(50) REFERENCES local_objectives(lo_id) UNIQUE,
    type VARCHAR(50) NOT NULL,
    status VARCHAR(50) NOT NULL,
    description TEXT,
    overall_control VARCHAR(50),
    form_title VARCHAR(255),
    submit_button_text VARCHAR(100),
    cancel_button_text VARCHAR(100)
);

CREATE TABLE ui_elements (
    id SERIAL PRIMARY KEY,
    ui_stack_id INTEGER REFERENCES ui_stack(id),
    entity_attribute VARCHAR(255) NOT NULL,
    ui_control VARCHAR(50) NOT NULL,
    helper_text TEXT,
    error_message TEXT
);
