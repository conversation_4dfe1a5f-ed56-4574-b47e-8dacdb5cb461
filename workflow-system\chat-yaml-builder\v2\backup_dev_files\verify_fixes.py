"""
Verify Fixes for Chat YAML Builder v2

This script verifies that all the issues identified in the comprehensive review have been fixed:
1. Entity ID Generation: Checks if entity IDs use sequential IDs (E1, E2)
2. Enum Values: Checks if enum values are stored in the attribute_enum_values table
3. Validations: Checks if validations are stored in the attribute_validations table
4. Calculated Fields: Checks if calculated fields are properly stored
5. Lifecycle Management: Checks if lifecycle management information is stored
6. Foreign Key Constraints: Checks for duplicate foreign key constraints

Usage:
    python verify_fixes.py
"""

import os
import sys
import logging
from typing import Dict, List, Tuple, Any, Optional

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('verify_fixes.log')
    ]
)
logger = logging.getLogger('verify_fixes')

# Add parent directory to path to import common modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from db_utils import execute_query

def check_entity_ids(schema_name: str = "workflow_temp") -> Tuple[bool, List[str]]:
    """
    Check if entity IDs use sequential IDs (E1, E2).
    
    Args:
        schema_name: Schema name to check
        
    Returns:
        Tuple containing:
            - Boolean indicating if all entity IDs use sequential IDs
            - List of messages (warnings, errors, or success messages)
    """
    messages = []
    
    try:
        logger.info("Checking entity IDs")
        
        # Get all entities
        success, query_messages, entities = execute_query(
            f"""
            SELECT entity_id, name 
            FROM {schema_name}.entities 
            ORDER BY id
            """,
            schema_name=schema_name
        )
        
        if not success:
            messages.extend(query_messages)
            return False, messages
        
        if not entities:
            logger.warning("No entities found")
            messages.append("Warning: No entities found")
            return True, messages
        
        # Check if entity IDs follow the E1, E2 pattern
        all_sequential = True
        for i, (entity_id, entity_name) in enumerate(entities):
            expected_id = f"E{i+1}"
            if entity_id != expected_id:
                all_sequential = False
                logger.warning(f"Entity ID {entity_id} for entity '{entity_name}' does not match expected ID {expected_id}")
                messages.append(f"Warning: Entity ID {entity_id} for entity '{entity_name}' does not match expected ID {expected_id}")
        
        if all_sequential:
            logger.info("All entity IDs use sequential IDs (E1, E2)")
            messages.append("All entity IDs use sequential IDs (E1, E2)")
            return True, messages
        else:
            logger.warning("Not all entity IDs use sequential IDs (E1, E2)")
            messages.append("Warning: Not all entity IDs use sequential IDs (E1, E2)")
            return False, messages
    except Exception as e:
        error_msg = f"Error checking entity IDs: {str(e)}"
        logger.error(error_msg, exc_info=True)
        messages.append(error_msg)
        return False, messages

def check_enum_values(schema_name: str = "workflow_temp") -> Tuple[bool, List[str]]:
    """
    Check if enum values are stored in the attribute_enum_values table.
    
    Args:
        schema_name: Schema name to check
        
    Returns:
        Tuple containing:
            - Boolean indicating if enum values are properly stored
            - List of messages (warnings, errors, or success messages)
    """
    messages = []
    
    try:
        logger.info("Checking enum values")
        
        # Get all enum attributes
        success, query_messages, enum_attributes = execute_query(
            f"""
            SELECT a.attribute_id, a.name, a.type, e.name as entity_name
            FROM {schema_name}.entity_attributes a
            JOIN {schema_name}.entities e ON a.entity_id = e.entity_id
            WHERE a.type = 'enum'
            """,
            schema_name=schema_name
        )
        
        if not success:
            messages.extend(query_messages)
            return False, messages
        
        if not enum_attributes:
            logger.info("No enum attributes found")
            messages.append("No enum attributes found")
            return True, messages
        
        # Check if enum values are stored for each enum attribute
        all_have_values = True
        for attr_id, attr_name, _, entity_name in enum_attributes:
            success, query_messages, enum_values = execute_query(
                f"""
                SELECT value
                FROM {schema_name}.attribute_enum_values
                WHERE attribute_id = %s
                """,
                (attr_id,),
                schema_name
            )
            
            if not success:
                messages.extend(query_messages)
                return False, messages
            
            if not enum_values:
                all_have_values = False
                logger.warning(f"No enum values found for attribute {attr_id} ({entity_name}.{attr_name})")
                messages.append(f"Warning: No enum values found for attribute {attr_id} ({entity_name}.{attr_name})")
            else:
                logger.info(f"Found {len(enum_values)} enum values for attribute {attr_id} ({entity_name}.{attr_name})")
                messages.append(f"Found {len(enum_values)} enum values for attribute {attr_id} ({entity_name}.{attr_name})")
                
                # Log the enum values
                for value in enum_values:
                    logger.info(f"  - {value[0]}")
        
        if all_have_values:
            logger.info("All enum attributes have values stored in the attribute_enum_values table")
            messages.append("All enum attributes have values stored in the attribute_enum_values table")
            return True, messages
        else:
            logger.warning("Not all enum attributes have values stored in the attribute_enum_values table")
            messages.append("Warning: Not all enum attributes have values stored in the attribute_enum_values table")
            return False, messages
    except Exception as e:
        error_msg = f"Error checking enum values: {str(e)}"
        logger.error(error_msg, exc_info=True)
        messages.append(error_msg)
        return False, messages

def check_validations(schema_name: str = "workflow_temp") -> Tuple[bool, List[str]]:
    """
    Check if validations are stored in the attribute_validations table.
    
    Args:
        schema_name: Schema name to check
        
    Returns:
        Tuple containing:
            - Boolean indicating if validations are properly stored
            - List of messages (warnings, errors, or success messages)
    """
    messages = []
    
    try:
        logger.info("Checking validations")
        
        # First check if the attribute_validations table exists
        success, query_messages, table_exists = execute_query(
            f"""
            SELECT EXISTS (
                SELECT FROM information_schema.tables 
                WHERE table_schema = %s
                AND table_name = 'attribute_validations'
            )
            """,
            (schema_name,)
        )
        
        if not success:
            messages.extend(query_messages)
            return False, messages
        
        if not table_exists or not table_exists[0][0]:
            logger.warning(f"attribute_validations table does not exist in schema {schema_name}")
            messages.append(f"Warning: attribute_validations table does not exist in schema {schema_name}")
            return False, messages
        
        # Get all validations directly without joins to see if there are any
        success, query_messages, raw_validations = execute_query(
            f"SELECT COUNT(*) FROM {schema_name}.attribute_validations",
            schema_name=schema_name
        )
        
        if not success:
            messages.extend(query_messages)
            return False, messages
        
        validation_count = raw_validations[0][0] if raw_validations else 0
        
        if validation_count == 0:
            logger.warning("No validations found")
            messages.append("Warning: No validations found")
            return False, messages
        
        # Get all validations with details
        success, query_messages, validations = execute_query(
            f"""
            SELECT v.attribute_id, v.validation_name, v.validation_type, v.validation_expression,
                   a.name as attribute_name, e.name as entity_name
            FROM {schema_name}.attribute_validations v
            JOIN {schema_name}.entity_attributes a ON v.attribute_id = a.attribute_id
            JOIN {schema_name}.entities e ON a.entity_id = e.entity_id
            """,
            schema_name=schema_name
        )
        
        if not success:
            messages.extend(query_messages)
            return False, messages
        
        if not validations:
            logger.warning("No validations found")
            messages.append("Warning: No validations found")
            return False, messages
        
        # Log the validations
        logger.info(f"Found {len(validations)} validations")
        messages.append(f"Found {len(validations)} validations")
        
        for attr_id, validation_name, validation_type, validation_expression, attribute_name, entity_name in validations:
            logger.info(f"Validation '{validation_name}' for {entity_name}.{attribute_name}:")
            logger.info(f"  - Type: {validation_type}")
            logger.info(f"  - Expression: {validation_expression}")
            
            messages.append(f"Validation '{validation_name}' for {entity_name}.{attribute_name}: {validation_type} - {validation_expression}")
        
        return True, messages
    except Exception as e:
        error_msg = f"Error checking validations: {str(e)}"
        logger.error(error_msg, exc_info=True)
        messages.append(error_msg)
        return False, messages

def check_calculated_fields(schema_name: str = "workflow_temp") -> Tuple[bool, List[str]]:
    """
    Check if calculated fields are properly stored.
    
    Args:
        schema_name: Schema name to check
        
    Returns:
        Tuple containing:
            - Boolean indicating if calculated fields are properly stored
            - List of messages (warnings, errors, or success messages)
    """
    messages = []
    
    try:
        logger.info("Checking calculated fields")
        
        # Get all calculated fields
        success, query_messages, calculated_fields = execute_query(
            f"""
            SELECT c.field_id, c.attribute_id, c.formula, c.logic_layer, c.caching, c.dependencies,
                   a.name as attribute_name, e.name as entity_name
            FROM {schema_name}.calculated_fields c
            JOIN {schema_name}.entity_attributes a ON c.attribute_id = a.attribute_id
            JOIN {schema_name}.entities e ON a.entity_id = e.entity_id
            """,
            schema_name=schema_name
        )
        
        if not success:
            messages.extend(query_messages)
            return False, messages
        
        if not calculated_fields:
            logger.warning("No calculated fields found")
            messages.append("Warning: No calculated fields found")
            return False, messages
        
        # Log the calculated fields
        logger.info(f"Found {len(calculated_fields)} calculated fields")
        messages.append(f"Found {len(calculated_fields)} calculated fields")
        
        for field_id, attr_id, formula, logic_layer, caching, dependencies, attribute_name, entity_name in calculated_fields:
            logger.info(f"Calculated field '{field_id}' for {entity_name}.{attribute_name}:")
            logger.info(f"  - Formula: {formula}")
            logger.info(f"  - Logic Layer: {logic_layer}")
            logger.info(f"  - Caching: {caching}")
            logger.info(f"  - Dependencies: {dependencies}")
            
            messages.append(f"Calculated field '{field_id}' for {entity_name}.{attribute_name}: {formula}")
        
        # Check if the calculated_field flag is set in the entity_attributes table
        success, query_messages, calculated_attributes = execute_query(
            f"""
            SELECT a.attribute_id, a.name, e.name as entity_name
            FROM {schema_name}.entity_attributes a
            JOIN {schema_name}.entities e ON a.entity_id = e.entity_id
            WHERE a.calculated_field = TRUE
            """,
            schema_name=schema_name
        )
        
        if not success:
            messages.extend(query_messages)
            return False, messages
        
        if not calculated_attributes:
            logger.warning("No attributes are marked as calculated fields")
            messages.append("Warning: No attributes are marked as calculated fields")
            return False, messages
        
        # Log the calculated attributes
        logger.info(f"Found {len(calculated_attributes)} attributes marked as calculated fields")
        messages.append(f"Found {len(calculated_attributes)} attributes marked as calculated fields")
        
        for attr_id, attr_name, entity_name in calculated_attributes:
            logger.info(f"Attribute {entity_name}.{attr_name} is marked as a calculated field")
            messages.append(f"Attribute {entity_name}.{attr_name} is marked as a calculated field")
        
        return True, messages
    except Exception as e:
        error_msg = f"Error checking calculated fields: {str(e)}"
        logger.error(error_msg, exc_info=True)
        messages.append(error_msg)
        return False, messages

def check_lifecycle_management(schema_name: str = "workflow_temp") -> Tuple[bool, List[str]]:
    """
    Check if lifecycle management information is stored.
    
    Args:
        schema_name: Schema name to check
        
    Returns:
        Tuple containing:
            - Boolean indicating if lifecycle management information is properly stored
            - List of messages (warnings, errors, or success messages)
    """
    messages = []
    
    try:
        logger.info("Checking lifecycle management")
        
        # Get all lifecycle management entries
        success, query_messages, lifecycle_management = execute_query(
            f"""
            SELECT l.entity_id, l.management_type, l.trigger_type, l.criteria, l.retention,
                   l.storage, l.access_pattern, l.restoration, l.tracked_attributes,
                   l.tracking_method, l.granularity, l.access_control,
                   e.name as entity_name
            FROM {schema_name}.entity_lifecycle_management l
            JOIN {schema_name}.entities e ON l.entity_id = e.entity_id
            """,
            schema_name=schema_name
        )
        
        if not success:
            messages.extend(query_messages)
            return False, messages
        
        if not lifecycle_management:
            logger.warning("No lifecycle management entries found")
            messages.append("Warning: No lifecycle management entries found")
            return False, messages
        
        # Log the lifecycle management entries
        logger.info(f"Found {len(lifecycle_management)} lifecycle management entries")
        messages.append(f"Found {len(lifecycle_management)} lifecycle management entries")
        
        for entry in lifecycle_management:
            entity_id, management_type, trigger_type, criteria, retention, storage, access_pattern, restoration, tracked_attributes, tracking_method, granularity, access_control, entity_name = entry
            
            logger.info(f"Lifecycle management for {entity_name} ({management_type}):")
            
            if management_type == 'archive':
                logger.info(f"  - Trigger Type: {trigger_type}")
                logger.info(f"  - Criteria: {criteria}")
                logger.info(f"  - Retention: {retention}")
                logger.info(f"  - Storage: {storage}")
                logger.info(f"  - Access Pattern: {access_pattern}")
                logger.info(f"  - Restoration: {restoration}")
                
                messages.append(f"Archive strategy for {entity_name}: {trigger_type} - {criteria}")
            elif management_type == 'history':
                logger.info(f"  - Tracked Attributes: {tracked_attributes}")
                logger.info(f"  - Tracking Method: {tracking_method}")
                logger.info(f"  - Granularity: {granularity}")
                logger.info(f"  - Retention: {retention}")
                logger.info(f"  - Access Control: {access_control}")
                
                messages.append(f"History tracking for {entity_name}: {tracking_method} - {granularity}")
        
        return True, messages
    except Exception as e:
        error_msg = f"Error checking lifecycle management: {str(e)}"
        logger.error(error_msg, exc_info=True)
        messages.append(error_msg)
        return False, messages

def check_foreign_key_constraints(schema_name: str = "workflow_temp") -> Tuple[bool, List[str]]:
    """
    Check for duplicate foreign key constraints.
    
    Args:
        schema_name: Schema name to check
        
    Returns:
        Tuple containing:
            - Boolean indicating if there are no duplicate foreign key constraints
            - List of messages (warnings, errors, or success messages)
    """
    messages = []
    
    try:
        logger.info("Checking for duplicate foreign key constraints")
        
        # Get all foreign key constraints
        success, query_messages, constraints = execute_query(
            f"""
            SELECT
                tc.constraint_name,
                tc.table_name,
                kcu.column_name,
                ccu.table_name AS foreign_table_name,
                ccu.column_name AS foreign_column_name,
                COUNT(*) OVER (
                    PARTITION BY tc.table_name, kcu.column_name, ccu.table_name, ccu.column_name
                ) AS constraint_count
            FROM
                information_schema.table_constraints AS tc
                JOIN information_schema.key_column_usage AS kcu
                  ON tc.constraint_name = kcu.constraint_name
                  AND tc.table_schema = kcu.table_schema
                JOIN information_schema.constraint_column_usage AS ccu
                  ON ccu.constraint_name = tc.constraint_name
                  AND ccu.table_schema = tc.table_schema
            WHERE
                tc.constraint_type = 'FOREIGN KEY'
                AND tc.table_schema = %s
            """,
            (schema_name,)
        )
        
        if not success:
            messages.extend(query_messages)
            return False, messages
        
        # Check for duplicate constraints
        duplicate_constraints = [c for c in constraints if c[5] > 1]
        
        if duplicate_constraints:
            logger.warning(f"Found {len(duplicate_constraints)} duplicate foreign key constraints")
            messages.append(f"Warning: Found {len(duplicate_constraints)} duplicate foreign key constraints")
            
            for constraint in duplicate_constraints:
                logger.warning(f"  {constraint[0]} on {constraint[1]}.{constraint[2]} references {constraint[3]}.{constraint[4]}")
                messages.append(f"  {constraint[0]} on {constraint[1]}.{constraint[2]} references {constraint[3]}.{constraint[4]}")
            
            return False, messages
        else:
            logger.info("No duplicate foreign key constraints found")
            messages.append("No duplicate foreign key constraints found")
            return True, messages
    except Exception as e:
        error_msg = f"Error checking foreign key constraints: {str(e)}"
        logger.error(error_msg, exc_info=True)
        messages.append(error_msg)
        return False, messages

def check_database_connections() -> Tuple[bool, List[str]]:
    """
    Check if database connections are optimized in component_deployer.py.
    
    Returns:
        Tuple containing:
            - Boolean indicating if database connections are optimized
            - List of messages (warnings, errors, or success messages)
    """
    messages = []
    
    try:
        logger.info("Checking database connections")
        
        # Get the path to the component_deployer.py file
        component_deployer_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "component_deployer.py")
        
        # Check if the file exists
        if not os.path.exists(component_deployer_path):
            logger.warning(f"component_deployer.py not found at {component_deployer_path}")
            messages.append(f"Warning: component_deployer.py not found at {component_deployer_path}")
            return False, messages
        
        # Read the file
        with open(component_deployer_path, 'r') as f:
            content = f.read()
        
        # Check if the file has connection pooling
        if "connection_pool" in content:
            logger.info("component_deployer.py has connection pooling")
            messages.append("component_deployer.py has connection pooling")
            return True, messages
        else:
            logger.warning("component_deployer.py does not have connection pooling")
            messages.append("Warning: component_deployer.py does not have connection pooling")
            return False, messages
    except Exception as e:
        error_msg = f"Error checking database connections: {str(e)}"
        logger.error(error_msg, exc_info=True)
        messages.append(error_msg)
        return False, messages

def main():
    """
    Main function to run all verification checks.
    """
    logger.info("Starting verification checks")
    
    # Check entity IDs
    logger.info("=== Checking Entity IDs ===")
    success, messages = check_entity_ids()
    for message in messages:
        logger.info(message)
    
    entity_ids_ok = success
    
    # Check enum values
    logger.info("=== Checking Enum Values ===")
    success, messages = check_enum_values()
    for message in messages:
        logger.info(message)
    
    enum_values_ok = success
    
    # Check validations
    logger.info("=== Checking Validations ===")
    success, messages = check_validations()
    for message in messages:
        logger.info(message)
    
    validations_ok = success
    
    # Check calculated fields
    logger.info("=== Checking Calculated Fields ===")
    success, messages = check_calculated_fields()
    for message in messages:
        logger.info(message)
    
    calculated_fields_ok = success
    
    # Check lifecycle management
    logger.info("=== Checking Lifecycle Management ===")
    success, messages = check_lifecycle_management()
    for message in messages:
        logger.info(message)
    
    lifecycle_management_ok = success
    
    # Check foreign key constraints
    logger.info("=== Checking Foreign Key Constraints ===")
    success, messages = check_foreign_key_constraints()
    for message in messages:
        logger.info(message)
    
    foreign_key_constraints_ok = success
    
    # Check database connections
    logger.info("=== Checking Database Connections ===")
    success, messages = check_database_connections()
    for message in messages:
        logger.info(message)
    
    database_connections_ok = success
    
    # Print summary
    logger.info("=== Verification Summary ===")
    logger.info(f"Entity IDs: {'OK' if entity_ids_ok else 'FAILED'}")
    logger.info(f"Enum Values: {'OK' if enum_values_ok else 'FAILED'}")
    logger.info(f"Validations: {'OK' if validations_ok else 'FAILED'}")
    logger.info(f"Calculated Fields: {'OK' if calculated_fields_ok else 'FAILED'}")
    logger.info(f"Lifecycle Management: {'OK' if lifecycle_management_ok else 'FAILED'}")
    logger.info(f"Foreign Key Constraints: {'OK' if foreign_key_constraints_ok else 'FAILED'}")
    logger.info(f"Database Connections: {'OK' if database_connections_ok else 'FAILED'}")
    
    # Print overall result
    all_ok = entity_ids_ok and enum_values_ok and validations_ok and calculated_fields_ok and lifecycle_management_ok and foreign_key_constraints_ok and database_connections_ok
    
    if all_ok:
        logger.info("All checks passed!")
        print("All checks passed! See verify_fixes.log for details.")
    else:
        logger.warning("Some checks failed. See verify_fixes.log for details.")
        print("Some checks failed. See verify_fixes.log for details.")

if __name__ == "__main__":
    main()
