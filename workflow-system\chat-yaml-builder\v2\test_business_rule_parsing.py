#!/usr/bin/env python3
"""
Test script to verify that business rules are correctly parsed from the entity definition.
"""

import os
import json
import logging
from parsers.entity_parser import parse_entities

# Set up logging
logging.basicConfig(level=logging.INFO, 
                   format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
                   handlers=[
                       logging.FileHandler('/home/<USER>/workflow-system/chat-yaml-builder/v2/business_rule_parsing.log'),
                       logging.StreamHandler()
                   ])
logger = logging.getLogger('test_business_rule_parsing')

def test_business_rule_parsing():
    """
    Test that business rules are correctly parsed from the entity definition.
    """
    # Read the sample entity output file
    sample_file_path = '/home/<USER>/workflow-system/chat-yaml-builder/v2/samples/sample_entity_output2.txt'
    with open(sample_file_path, 'r') as f:
        entity_def = f.read()
    
    # Add the specific business rule we want to test
    business_rule = """
BusinessRule EMP001 for Employee:
* Employee.status must be Active to receive performance reviews
* Employee.hireDate must be at least 90 days before Employee.performanceRating can be set
"""
    
    # Append the business rule to the entity definition
    entity_def_with_rule = entity_def + business_rule
    
    # Parse the entity definition
    entities_data, warnings = parse_entities(entity_def_with_rule)
    
    # Print any warnings
    if warnings:
        logger.warning("Warnings during parsing:")
        for warning in warnings:
            logger.warning(f"  - {warning}")
    
    # Check if Employee entity was parsed
    if 'entities' in entities_data and 'Employee' in entities_data['entities']:
        employee_entity = entities_data['entities']['Employee']
        
        # Check if business rules were parsed
        if 'business_rules' in employee_entity:
            logger.info("\nEmployee business rules:")
            for rule_id, rule_def in employee_entity['business_rules'].items():
                logger.info(f"\n  - Business Rule: {rule_id}")
                if 'conditions' in rule_def:
                    for i, condition in enumerate(rule_def['conditions']):
                        logger.info(f"    - Condition {i+1}: {condition}")
                else:
                    logger.warning(f"No conditions found in business rule '{rule_id}'")
        else:
            logger.warning("No business rules found in Employee entity")
            
            # Check if there are any business rule-like entries in the entity definition
            logger.info("\nSearching for business rule-like entries in the entity definition:")
            business_rule_lines = [line for line in entity_def_with_rule.split('\n') if 'BusinessRule' in line]
            for line in business_rule_lines:
                logger.info(f"  - {line.strip()}")
    else:
        logger.error("Employee entity not found in parsed data")
    
    # Print the full parsed data as JSON
    logger.info("\nFull parsed data:")
    logger.info(json.dumps(entities_data, indent=2))

if __name__ == "__main__":
    test_business_rule_parsing()
