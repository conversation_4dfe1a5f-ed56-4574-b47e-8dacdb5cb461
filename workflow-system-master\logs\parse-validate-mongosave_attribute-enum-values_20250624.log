{"timestamp": "2025-06-24T12:42:03.958326", "endpoint": "parse-validate-mongosave/attribute-enum-values", "input": {"natural_language": "Enum Value: Active Status\nAttribute: status\nValue: active\nDisplay Name: Active\nDescription: Record is currently active\nSort Order: 1", "tenant_id": "t999", "edit_draft": true, "edit_temp": false, "edit_production": false}, "output": {"success": true, "enum_value_results": [], "operation": "parse_validate_mongosave", "total_enum_values": 0}, "status": "success"}
{"timestamp": "2025-06-24T12:48:59.280519", "endpoint": "parse-validate-mongosave/attribute-enum-values", "input": {"natural_language": "enum_value_id | attribute_id | value | display_name | description | sort_order | is_active | natural_language | version | status\nEV_STATUS_ACTIVE | TestEntity2.status | active | Active | Record is currently active | 1 | true | Active status for records | 1 | active\nTenant: t999", "tenant_id": "t999", "edit_draft": true, "edit_temp": false, "edit_production": false}, "output": {"success": true, "enum_value_results": [], "operation": "parse_validate_mongosave", "total_enum_values": 0}, "status": "success"}
{"timestamp": "2025-06-24T12:56:37.965093", "endpoint": "parse-validate-mongosave/attribute-enum-values", "input": {"natural_language": "Entity.Attribute | Enum Name | Value | Display | Description | Sort Order | Active\nTestEntity2.status | Status Values | active | Active | Record is currently active | 1 | true\nTenant: t999", "tenant_id": "t999", "edit_draft": true, "edit_temp": false, "edit_production": false}, "output": {"success": true, "enum_value_results": [{"success": false, "errors": ["Attribute with attribute_id A_E48_status_1750769797 does not exist"], "parsed_data": {"enum_value_id": "EV_TESTENTITY2_STATUS_ACTIVE", "attribute_id": "A_E48_status_1750769797", "value": "active", "display_name": "Active", "description": "Record is currently active", "sort_order": 1, "is_active": true, "natural_language": "Entity.Attribute: TestEntity2.status\nEnum Name: Status Values\nValue: active\nDisplay: Active\nDescription: Record is currently active", "version": 1, "status": "active", "created_at": "2025-06-24T12:56:37.948606", "updated_at": "2025-06-24T12:56:37.948606", "created_by": "Tarun", "updated_by": "Tarun", "enum_value_status": "new", "changes_detected": []}}], "operation": "parse_validate_mongosave", "total_enum_values": 1}, "status": "success"}
