services:
  # Design Time Environment
  mongodb:
    image: mongo:6.0
    container_name: workflow_mongodb
    restart: always
    ports:
      - "27018:27017"
    environment:
      MONGO_INITDB_ROOT_USERNAME: admin
      MONGO_INITDB_ROOT_PASSWORD: ${MONGO_PASSWORD:-mongo}
    volumes:
      - mongodb_data:/data/db
      - mongodb_config:/data/configdb
    networks:
      - workflow_network

  mongo-express:
    image: mongo-express
    container_name: workflow_mongo_express
    restart: always
    ports:
      - "8081:8081"
    environment:
      ME_CONFIG_MONGODB_ADMINUSERNAME: admin
      ME_CONFIG_MONGODB_ADMINPASSWORD: ${MONGO_PASSWORD:-mongo}
      ME_CONFIG_MONGODB_URL: mongodb://admin:${MONGO_PASSWORD:-mongo}@mongodb:27017/
    networks:
      - workflow_network
    depends_on:
      - mongodb

  # Runtime Environment - Data Layer
  postgres:
    image: postgres:15
    container_name: workflow_postgres
    restart: always
    ports:
      - "5433:5432"
    environment:
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD:-postgres}
      POSTGRES_DB: workflow_system
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./runtime/data-layer/postgresql/init-scripts:/docker-entrypoint-initdb.d
    networks:
      - workflow_network

  pgadmin:
    image: dpage/pgadmin4
    container_name: workflow_pgadmin
    restart: always
    ports:
      - "5051:80"
    environment:
      PGADMIN_DEFAULT_EMAIL: <EMAIL>
      PGADMIN_DEFAULT_PASSWORD: ${PGADMIN_PASSWORD:-secure_pgadmin_password}
    volumes:
      - pgadmin_data:/var/lib/pgadmin
    networks:
      - workflow_network
    depends_on:
      - postgres

  redis:
    image: redis:7.0
    container_name: workflow_redis
    restart: always
    ports:
      - "6379:6379"
    command: redis-server --requirepass ${REDIS_PASSWORD:-redis}
    volumes:
      - redis_data:/data
    networks:
      - workflow_network

  redis-commander:
    image: rediscommander/redis-commander:latest
    container_name: workflow_redis_commander
    restart: always
    ports:
      - "8082:8081"
    environment:
      REDIS_HOSTS: local:redis:6379:0:${REDIS_PASSWORD:-redis}
    networks:
      - workflow_network
    depends_on:
      - redis

  # Runtime Environment - Application Layer
  workflow-engine:
    build: 
      context: ./runtime/workflow-engine
      dockerfile: Dockerfile
    container_name: workflow_engine
    restart: always
    ports:
      - "8000:8000"
    volumes:
      - ./runtime/workflow-engine:/app
    environment:
      - MONGODB_URI=mongodb://admin:${MONGO_PASSWORD:-mongo}@workflow_mongodb:27017/workflow_system
      - POSTGRES_SERVER=workflow_postgres
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD:-postgres}
      - POSTGRES_DB=workflow_system
      - POSTGRES_PORT=5432
      - REDIS_HOST=workflow_redis
      - REDIS_PORT=6379
      - REDIS_PASSWORD=${REDIS_PASSWORD:-redis}
      - SECRET_KEY=${SECRET_KEY:-super_secret_key_change_in_production}
    networks:
      - workflow_network
    depends_on:
      - mongodb
      - postgres
      - redis

volumes:
  mongodb_data:
  mongodb_config:
  postgres_data:
  pgadmin_data:
  redis_data:

networks:
  workflow_network:
    driver: bridge
