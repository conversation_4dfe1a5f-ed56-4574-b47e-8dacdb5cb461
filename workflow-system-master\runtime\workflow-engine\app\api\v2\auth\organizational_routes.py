"""
Organizational Routes for v2 API

This module contains the FastAPI routes for organizational hierarchy and user profile endpoints.
"""

from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session
from typing import Optional

from app.db.session import get_db
from .organizational_models import (
    UserProfileResponse, UserProfileUpdateRequest, DepartmentResponse, TeamResponse,
    DepartmentListResponse, TeamListResponse, UserTeamMembershipRequest,
    PermissionCheckRequest, PermissionCheckResponse, OrganizationalTreeResponse,
    EnhancedLoginResponse, PermissionType, PermissionAction
)
from .models import UserRegistrationResponse, ErrorResponse
from .organizational_service import OrganizationalService


# Create router for organizational endpoints
router = APIRouter(
    prefix="/auth",
    tags=["organizational-v2"],
    responses={
        400: {"model": ErrorResponse, "description": "Bad Request"},
        404: {"model": ErrorResponse, "description": "Not Found"},
        500: {"model": ErrorResponse, "description": "Internal Server Error"}
    }
)


@router.get(
    "/profile/{user_id}",
    response_model=UserProfileResponse,
    summary="Get user profile with organizational context",
    description="Retrieve comprehensive user profile including organizational hierarchy, permissions, and team memberships",
    responses={
        200: {
            "description": "User profile retrieved successfully",
            "model": UserProfileResponse
        },
        404: {
            "description": "User not found",
            "model": ErrorResponse,
            "content": {
                "application/json": {
                    "example": {
                        "error": "NOT_FOUND",
                        "message": "User not found",
                        "details": {
                            "user_id": "U123"
                        }
                    }
                }
            }
        }
    }
)
async def get_user_profile(
    user_id: str,
    db: Session = Depends(get_db)
) -> UserProfileResponse:
    """
    Get comprehensive user profile with organizational context.
    
    This endpoint returns detailed user information including:
    - Basic user data
    - Department and team memberships
    - Organizational hierarchy
    - Granular permissions (entity, attribute, GO, LO, book, chapter levels)
    - Role KPIs
    - Team and department colleagues
    
    Args:
        user_id: User ID to retrieve profile for
        db: Database session dependency
        
    Returns:
        UserProfileResponse: Comprehensive user profile
        
    Raises:
        HTTPException: 404 if user not found
    """
    try:
        org_service = OrganizationalService(db)
        profile = org_service.get_user_profile(user_id)
        
        if not profile:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail={
                    "error": "NOT_FOUND",
                    "message": "User not found",
                    "details": {
                        "user_id": user_id
                    }
                }
            )
        
        return profile
        
    except HTTPException:
        raise
    except Exception as e:
        import logging
        logger = logging.getLogger(__name__)
        logger.error(f"Error retrieving user profile {user_id}: {str(e)}")
        
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail={
                "error": "INTERNAL_ERROR",
                "message": "An error occurred while retrieving user profile",
                "details": {}
            }
        )


@router.put(
    "/profile/{user_id}",
    response_model=UserRegistrationResponse,
    summary="Update user profile",
    description="Update user profile information including organizational details",
    responses={
        200: {
            "description": "User profile updated successfully",
            "model": UserRegistrationResponse
        },
        404: {
            "description": "User not found",
            "model": ErrorResponse
        },
        400: {
            "description": "Invalid input data",
            "model": ErrorResponse
        }
    }
)
async def update_user_profile(
    user_id: str,
    update_data: UserProfileUpdateRequest,
    db: Session = Depends(get_db)
) -> UserRegistrationResponse:
    """
    Update user profile information.
    
    Args:
        user_id: User ID to update
        update_data: Profile update data
        db: Database session dependency
        
    Returns:
        UserRegistrationResponse: Updated user information
        
    Raises:
        HTTPException: 404 if user not found, 400 if invalid data
    """
    try:
        org_service = OrganizationalService(db)
        updated_user = org_service.update_user_profile(user_id, update_data)
        
        if not updated_user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail={
                    "error": "NOT_FOUND",
                    "message": "User not found",
                    "details": {
                        "user_id": user_id
                    }
                }
            )
        
        return updated_user
        
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail={
                "error": "VALIDATION_ERROR",
                "message": str(e),
                "details": {}
            }
        )
    except HTTPException:
        raise
    except Exception as e:
        import logging
        logger = logging.getLogger(__name__)
        logger.error(f"Error updating user profile {user_id}: {str(e)}")
        
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail={
                "error": "INTERNAL_ERROR",
                "message": "An error occurred while updating user profile",
                "details": {}
            }
        )


@router.post(
    "/users/{user_id}/teams",
    response_model=dict,
    summary="Add user to team",
    description="Add a user to a team with optional primary team designation",
    responses={
        200: {
            "description": "User added to team successfully",
            "content": {
                "application/json": {
                    "example": {
                        "success": True,
                        "message": "User added to team successfully",
                        "data": {
                            "user_id": "U5",
                            "team_id": "TEAM001",
                            "is_primary_team": True
                        }
                    }
                }
            }
        },
        400: {
            "description": "Invalid input data",
            "model": ErrorResponse
        }
    }
)
async def add_user_to_team(
    user_id: str,
    membership_request: UserTeamMembershipRequest,
    db: Session = Depends(get_db)
) -> dict:
    """
    Add user to a team.
    
    Args:
        user_id: User ID
        membership_request: Team membership request
        db: Database session dependency
        
    Returns:
        dict: Success response
        
    Raises:
        HTTPException: 400 if invalid data
    """
    try:
        org_service = OrganizationalService(db)
        success = org_service.add_user_to_team(user_id, membership_request)
        
        if not success:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail={
                    "error": "OPERATION_FAILED",
                    "message": "Failed to add user to team",
                    "details": {}
                }
            )
        
        return {
            "success": True,
            "message": "User added to team successfully",
            "data": {
                "user_id": user_id,
                "team_id": membership_request.team_id,
                "is_primary_team": membership_request.is_primary_team
            }
        }
        
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail={
                "error": "VALIDATION_ERROR",
                "message": str(e),
                "details": {}
            }
        )
    except HTTPException:
        raise
    except Exception as e:
        import logging
        logger = logging.getLogger(__name__)
        logger.error(f"Error adding user {user_id} to team: {str(e)}")
        
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail={
                "error": "INTERNAL_ERROR",
                "message": "An error occurred while adding user to team",
                "details": {}
            }
        )


@router.delete(
    "/users/{user_id}/teams/{team_id}",
    response_model=dict,
    summary="Remove user from team",
    description="Remove a user from a team",
    responses={
        200: {
            "description": "User removed from team successfully",
            "content": {
                "application/json": {
                    "example": {
                        "success": True,
                        "message": "User removed from team successfully",
                        "data": {
                            "user_id": "U5",
                            "team_id": "TEAM001"
                        }
                    }
                }
            }
        },
        404: {
            "description": "User or team membership not found",
            "model": ErrorResponse
        }
    }
)
async def remove_user_from_team(
    user_id: str,
    team_id: str,
    db: Session = Depends(get_db)
) -> dict:
    """
    Remove user from a team.
    
    Args:
        user_id: User ID
        team_id: Team ID
        db: Database session dependency
        
    Returns:
        dict: Success response
        
    Raises:
        HTTPException: 404 if membership not found
    """
    try:
        org_service = OrganizationalService(db)
        success = org_service.remove_user_from_team(user_id, team_id)
        
        if not success:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail={
                    "error": "NOT_FOUND",
                    "message": "User team membership not found",
                    "details": {
                        "user_id": user_id,
                        "team_id": team_id
                    }
                }
            )
        
        return {
            "success": True,
            "message": "User removed from team successfully",
            "data": {
                "user_id": user_id,
                "team_id": team_id
            }
        }
        
    except HTTPException:
        raise
    except Exception as e:
        import logging
        logger = logging.getLogger(__name__)
        logger.error(f"Error removing user {user_id} from team {team_id}: {str(e)}")
        
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail={
                "error": "INTERNAL_ERROR",
                "message": "An error occurred while removing user from team",
                "details": {}
            }
        )


@router.post(
    "/permissions/check",
    response_model=PermissionCheckResponse,
    summary="Check user permission",
    description="Check if a user has a specific permission for a resource and action",
    responses={
        200: {
            "description": "Permission check completed",
            "model": PermissionCheckResponse
        },
        400: {
            "description": "Invalid input data",
            "model": ErrorResponse
        }
    }
)
async def check_user_permission(
    check_request: PermissionCheckRequest,
    db: Session = Depends(get_db)
) -> PermissionCheckResponse:
    """
    Check if user has a specific permission.
    
    This endpoint checks permissions at various levels:
    - Entity level (e.g., e1_leaveapplication)
    - Attribute level (e.g., e1_leaveapplication.status)
    - Global objective level (e.g., GO001)
    - Local objective level (e.g., LO001)
    - Book level (e.g., B001)
    - Chapter level (e.g., C001)
    
    Args:
        check_request: Permission check request
        db: Database session dependency
        
    Returns:
        PermissionCheckResponse: Permission check result
    """
    try:
        org_service = OrganizationalService(db)
        result = org_service.check_user_permission(check_request)
        
        return result
        
    except Exception as e:
        import logging
        logger = logging.getLogger(__name__)
        logger.error(f"Error checking user permission: {str(e)}")
        
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail={
                "error": "INTERNAL_ERROR",
                "message": "An error occurred while checking user permission",
                "details": {}
            }
        )


@router.get(
    "/departments",
    response_model=DepartmentListResponse,
    summary="Get all departments",
    description="Retrieve list of all departments in the organization",
    responses={
        200: {
            "description": "Departments retrieved successfully",
            "model": DepartmentListResponse
        }
    }
)
async def get_all_departments(
    db: Session = Depends(get_db)
) -> DepartmentListResponse:
    """
    Get all departments.
    
    Args:
        db: Database session dependency
        
    Returns:
        DepartmentListResponse: List of all departments
    """
    try:
        org_service = OrganizationalService(db)
        return org_service.get_all_departments()
        
    except Exception as e:
        import logging
        logger = logging.getLogger(__name__)
        logger.error(f"Error retrieving departments: {str(e)}")
        
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail={
                "error": "INTERNAL_ERROR",
                "message": "An error occurred while retrieving departments",
                "details": {}
            }
        )


@router.get(
    "/departments/{department_id}",
    response_model=DepartmentResponse,
    summary="Get department by ID",
    description="Retrieve specific department information",
    responses={
        200: {
            "description": "Department retrieved successfully",
            "model": DepartmentResponse
        },
        404: {
            "description": "Department not found",
            "model": ErrorResponse
        }
    }
)
async def get_department(
    department_id: str,
    db: Session = Depends(get_db)
) -> DepartmentResponse:
    """
    Get department by ID.
    
    Args:
        department_id: Department ID
        db: Database session dependency
        
    Returns:
        DepartmentResponse: Department information
        
    Raises:
        HTTPException: 404 if department not found
    """
    try:
        org_service = OrganizationalService(db)
        department = org_service.get_department_by_id(department_id)
        
        if not department:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail={
                    "error": "NOT_FOUND",
                    "message": "Department not found",
                    "details": {
                        "department_id": department_id
                    }
                }
            )
        
        return department
        
    except HTTPException:
        raise
    except Exception as e:
        import logging
        logger = logging.getLogger(__name__)
        logger.error(f"Error retrieving department {department_id}: {str(e)}")
        
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail={
                "error": "INTERNAL_ERROR",
                "message": "An error occurred while retrieving department",
                "details": {}
            }
        )


@router.get(
    "/teams",
    response_model=TeamListResponse,
    summary="Get all teams",
    description="Retrieve list of all teams, optionally filtered by department",
    responses={
        200: {
            "description": "Teams retrieved successfully",
            "model": TeamListResponse
        }
    }
)
async def get_all_teams(
    department_id: Optional[str] = Query(None, description="Filter teams by department ID"),
    db: Session = Depends(get_db)
) -> TeamListResponse:
    """
    Get all teams, optionally filtered by department.
    
    Args:
        department_id: Optional department ID to filter teams
        db: Database session dependency
        
    Returns:
        TeamListResponse: List of teams
    """
    try:
        org_service = OrganizationalService(db)
        return org_service.get_all_teams(department_id)
        
    except Exception as e:
        import logging
        logger = logging.getLogger(__name__)
        logger.error(f"Error retrieving teams: {str(e)}")
        
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail={
                "error": "INTERNAL_ERROR",
                "message": "An error occurred while retrieving teams",
                "details": {}
            }
        )


@router.get(
    "/teams/{team_id}",
    response_model=TeamResponse,
    summary="Get team by ID",
    description="Retrieve specific team information",
    responses={
        200: {
            "description": "Team retrieved successfully",
            "model": TeamResponse
        },
        404: {
            "description": "Team not found",
            "model": ErrorResponse
        }
    }
)
async def get_team(
    team_id: str,
    db: Session = Depends(get_db)
) -> TeamResponse:
    """
    Get team by ID.
    
    Args:
        team_id: Team ID
        db: Database session dependency
        
    Returns:
        TeamResponse: Team information
        
    Raises:
        HTTPException: 404 if team not found
    """
    try:
        org_service = OrganizationalService(db)
        team = org_service.get_team_by_id(team_id)
        
        if not team:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail={
                    "error": "NOT_FOUND",
                    "message": "Team not found",
                    "details": {
                        "team_id": team_id
                    }
                }
            )
        
        return team
        
    except HTTPException:
        raise
    except Exception as e:
        import logging
        logger = logging.getLogger(__name__)
        logger.error(f"Error retrieving team {team_id}: {str(e)}")
        
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail={
                "error": "INTERNAL_ERROR",
                "message": "An error occurred while retrieving team",
                "details": {}
            }
        )
