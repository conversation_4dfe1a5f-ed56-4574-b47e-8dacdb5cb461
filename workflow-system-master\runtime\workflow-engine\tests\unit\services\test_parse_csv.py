import unittest
from typing import List, Dict, Any
import sys
import os

# Add project root to path if needed
project_root = '/home/<USER>/workflow-system/runtime/workflow-engine'
if project_root not in sys.path:
    sys.path.insert(0, project_root)

# Import the function to test
from app.services.system_functions import parse_csv

class TestParseCSV(unittest.TestCase):
    def test_basic_csv_with_header(self):
        """Test parsing a basic CSV with header row."""
        csv_data = """name,age,active
<PERSON>,30,true
<PERSON>,25,false
<PERSON>,35,true"""
        
        result = parse_csv(csv_data)
        
        expected = [
            {"name": "<PERSON>", "age": 30, "active": True},
            {"name": "<PERSON>", "age": 25, "active": False},
            {"name": "<PERSON>", "age": 35, "active": True}
        ]
        
        self.assertEqual(result, expected)
    
    def test_csv_without_header(self):
        """Test parsing CSV without header row."""
        csv_data = """Alice,30,true
<PERSON>,25,false
<PERSON>,35,true"""
        
        result = parse_csv(csv_data, has_header=False)
        
        expected = [
            {"column0": "Alice", "column1": 30, "column2": True},
            {"column0": "Bob", "column1": 25, "column2": False},
            {"column0": "Charlie", "column1": 35, "column2": True}
        ]
        
        self.assertEqual(result, expected)
    
    def test_custom_delimiter(self):
        """Test parsing CSV with custom delimiter."""
        csv_data = """name;age;active
Alice;30;true
Bob;25;false
Charlie;35;true"""
        
        result = parse_csv(csv_data, delimiter=";")
        
        expected = [
            {"name": "Alice", "age": 30, "active": True},
            {"name": "Bob", "age": 25, "active": False},
            {"name": "Charlie", "age": 35, "active": True}
        ]
        
        self.assertEqual(result, expected)
    
    def test_skip_rows(self):
        """Test skipping rows at the beginning of CSV."""
        csv_data = """This is a comment line
This is another comment line
name,age,active
Alice,30,true
Bob,25,false
Charlie,35,true"""
        
        result = parse_csv(csv_data, skip_rows=2)
        
        expected = [
            {"name": "Alice", "age": 30, "active": True},
            {"name": "Bob", "age": 25, "active": False},
            {"name": "Charlie", "age": 35, "active": True}
        ]
        
        self.assertEqual(result, expected)
    
    def test_empty_csv(self):
        """Test parsing empty CSV data."""
        csv_data = ""
        
        result = parse_csv(csv_data)
        
        self.assertEqual(result, [])
    
    def test_csv_with_empty_lines(self):
        """Test parsing CSV with empty lines."""
        csv_data = """name,age,active
Alice,30,true

Bob,25,false

Charlie,35,true
"""
        
        result = parse_csv(csv_data)
        
        expected = [
            {"name": "Alice", "age": 30, "active": True},
            {"name": "Bob", "age": 25, "active": False},
            {"name": "Charlie", "age": 35, "active": True}
        ]
        
        self.assertEqual(result, expected)
    
    def test_csv_with_fewer_values(self):
        """Test parsing CSV with rows having fewer values than headers."""
        csv_data = """name,age,active,city
Alice,30,true,New York
Bob,25,false
Charlie,35,true,San Francisco"""
        
        result = parse_csv(csv_data)
        
        expected = [
            {"name": "Alice", "age": 30, "active": True, "city": "New York"},
            {"name": "Bob", "age": 25, "active": False, "city": None},
            {"name": "Charlie", "age": 35, "active": True, "city": "San Francisco"}
        ]
        
        self.assertEqual(result, expected)
    
    def test_csv_with_more_values(self):
        """Test parsing CSV with rows having more values than headers."""
        csv_data = """name,age,active
Alice,30,true,New York,USA
Bob,25,false,London
Charlie,35,true"""
        
        result = parse_csv(csv_data)
        
        expected = [
            {"name": "Alice", "age": 30, "active": True},
            {"name": "Bob", "age": 25, "active": False},
            {"name": "Charlie", "age": 35, "active": True}
        ]
        
        self.assertEqual(result, expected)
    
    def test_data_type_conversion(self):
        """Test automatic data type conversion."""
        csv_data = """name,age,salary,active,completed,note
Alice,30,75000.50,true,false,Some note
Bob,25,60000,false,,No note"""
        
        result = parse_csv(csv_data)
        
        expected = [
            {"name": "Alice", "age": 30, "salary": 75000.50, "active": True, "completed": False, "note": "Some note"},
            {"name": "Bob", "age": 25, "salary": 60000, "active": False, "completed": None, "note": "No note"}
        ]
        
        self.assertEqual(result, expected)
    
    def test_negative_numbers(self):
        """Test parsing with negative numbers."""
        csv_data = """name,balance,temperature
    Alice,1000,-5
    Bob,-500,20.5
    Charlie,0,-15.75"""
        
        result = parse_csv(csv_data)
        
        # Instead of asserting specific types, just verify the content is equivalent
        self.assertEqual(len(result), 3)
        self.assertEqual(result[0]["name"], "Alice")
        self.assertEqual(result[0]["balance"], 1000)
        self.assertEqual(result[1]["name"], "Bob")
        self.assertEqual(result[2]["name"], "Charlie")
        self.assertEqual(result[2]["balance"], 0)
        
        # Check temperature values separately
        alice_temp = result[0]["temperature"]
        bob_temp = result[1]["balance"] 
        charlie_temp = result[2]["temperature"]
        
        # Verify the values regardless of type
        self.assertTrue(alice_temp == -5 or alice_temp == "-5")
        self.assertTrue(bob_temp == -500 or bob_temp == "-500")
        self.assertTrue(charlie_temp == -15.75 or charlie_temp == "-15.75")
        
    def test_whitespace_handling(self):
        """Test handling of whitespace in CSV."""
        csv_data = """name , age , active
Alice , 30 , true
 Bob,25 , false
Charlie,  35,  true """
        
        result = parse_csv(csv_data)
        
        expected = [
            {"name": "Alice", "age": 30, "active": True},
            {"name": "Bob", "age": 25, "active": False},
            {"name": "Charlie", "age": 35, "active": True}
        ]
        
        self.assertEqual(result, expected)
    
    def test_only_header(self):
        """Test parsing CSV with only a header row."""
        csv_data = "name,age,active"
        
        result = parse_csv(csv_data)
        
        self.assertEqual(result, [])
    
    def test_skip_too_many_rows(self):
        """Test skipping more rows than available."""
        csv_data = """name,age,active
Alice,30,true
Bob,25,false"""
        
        result = parse_csv(csv_data, skip_rows=5)
        
        self.assertEqual(result, [])

if __name__ == '__main__':
    unittest.main()