#!/usr/bin/env python3
"""
Script to deploy entities, GOs, and LOs from prescriptive text.
"""

import os
import sys
import logging
import argparse
from datetime import datetime
from typing import Dict, List, Tuple, Any, Optional

# Configure logging
log_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'logs')
os.makedirs(log_dir, exist_ok=True)
log_file = os.path.join(log_dir, f'deploy_entities_go_lo_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log')

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(log_file),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

# Add parent directory to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Import parsers and deployers
from parsers.entity_parser import parse_entity_definitions
from parsers.go_parser import parse_go_definitions
from parsers.lo_parser import parse_lo_definitions
from deployers.entity_deployer import deploy_entity_definitions
from deployers.go_deployer import deploy_go_definitions
from deployers.lo_deployer import deploy_lo_definitions
from registry_validator import validate_entity_definitions, validate_go_definitions
from db_utils import get_db_connection

def deploy_entities_go_lo(
    entity_file: Optional[str] = None,
    go_file: Optional[str] = None,
    lo_file: Optional[str] = None,
    schema_name: str = "workflow_temp",
    skip_validation: bool = False
):
    """
    Deploy entities, GOs, and LOs from prescriptive text.
    
    Args:
        entity_file: Path to the input file containing entity definitions
        go_file: Path to the input file containing GO definitions
        lo_file: Path to the input file containing LO definitions
        schema_name: Name of the database schema to use
        skip_validation: Whether to skip validation
    """
    logger.info("Deploying entities, GOs, and LOs")
    logger.info(f"Using schema: {schema_name}")
    logger.info(f"Skip validation: {skip_validation}")
    
    # Deploy entities
    if entity_file:
        logger.info(f"Deploying entities from file: {entity_file}")
        deploy_entities(entity_file, schema_name, skip_validation)
    
    # Deploy GOs
    if go_file:
        logger.info(f"Deploying GOs from file: {go_file}")
        deploy_gos(go_file, schema_name, skip_validation)
    
    # Deploy LOs
    if lo_file:
        logger.info(f"Deploying LOs from file: {lo_file}")
        deploy_los(lo_file, schema_name, skip_validation)
    
    logger.info("Deployment complete")

def deploy_entities(entity_file: str, schema_name: str, skip_validation: bool):
    """
    Deploy entities from prescriptive text.
    
    Args:
        entity_file: Path to the input file containing entity definitions
        schema_name: Name of the database schema to use
        skip_validation: Whether to skip validation
    """
    # Read input file
    try:
        with open(entity_file, 'r') as f:
            entity_text = f.read()
    except Exception as e:
        logger.error(f"Error reading entity file: {str(e)}")
        return
    
    # Parse entity definitions
    logger.info("Parsing entity definitions...")
    try:
        entity_definitions, entity_warnings = parse_entity_definitions(entity_text)
    except Exception as e:
        logger.error(f"Error parsing entity definitions: {str(e)}")
        return
    
    # Print warnings
    if entity_warnings:
        logger.warning("Warnings during entity parsing:")
        for warning in entity_warnings:
            logger.warning(f"  - {warning}")
    
    # Print parsed entity definitions
    logger.info(f"Parsed {len(entity_definitions)} entity definitions:")
    for entity_name, entity_definition in entity_definitions.items():
        logger.info(f"  - {entity_name}")
    
    # Validate entity definitions
    if not skip_validation:
        logger.info("Validating entity definitions...")
        try:
            is_valid, validation_errors = validate_entity_definitions(entity_definitions)
            if not is_valid:
                logger.error("Entity validation failed:")
                for error in validation_errors:
                    logger.error(f"  - {error}")
                return
        except Exception as e:
            logger.error(f"Error validating entity definitions: {str(e)}")
            return
    
    # Deploy entity definitions
    logger.info("Deploying entity definitions...")
    try:
        entity_success, entity_messages = deploy_entity_definitions(entity_definitions, schema_name=schema_name)
    except Exception as e:
        logger.error(f"Error deploying entity definitions: {str(e)}")
        return
    
    # Print deployment messages
    for message in entity_messages:
        if "Error" in message or "Failed" in message:
            logger.error(f"  - {message}")
        else:
            logger.info(f"  - {message}")
    
    # Print deployment status
    if entity_success:
        logger.info("Entity deployment successful!")
    else:
        logger.error("Entity deployment failed!")

def deploy_gos(go_file: str, schema_name: str, skip_validation: bool):
    """
    Deploy GOs from prescriptive text.
    
    Args:
        go_file: Path to the input file containing GO definitions
        schema_name: Name of the database schema to use
        skip_validation: Whether to skip validation
    """
    # Read input file
    try:
        with open(go_file, 'r') as f:
            go_text = f.read()
    except Exception as e:
        logger.error(f"Error reading GO file: {str(e)}")
        return
    
    # Parse GO definitions
    logger.info("Parsing GO definitions...")
    try:
        go_definitions, go_warnings = parse_go_definitions(go_text)
    except Exception as e:
        logger.error(f"Error parsing GO definitions: {str(e)}")
        return
    
    # Print warnings
    if go_warnings:
        logger.warning("Warnings during GO parsing:")
        for warning in go_warnings:
            logger.warning(f"  - {warning}")
    
    # Print parsed GO definitions
    logger.info(f"Parsed {len(go_definitions)} GO definitions:")
    for go_name, go_definition in go_definitions.items():
        logger.info(f"  - {go_name}")
    
    # Validate GO definitions
    if not skip_validation:
        logger.info("Validating GO definitions...")
        try:
            is_valid, validation_errors = validate_go_definitions(go_definitions)
            if not is_valid:
                logger.error("GO validation failed:")
                for error in validation_errors:
                    logger.error(f"  - {error}")
                return
        except Exception as e:
            logger.error(f"Error validating GO definitions: {str(e)}")
            return
    
    # Deploy GO definitions
    logger.info("Deploying GO definitions...")
    try:
        go_success, go_messages = deploy_go_definitions(go_definitions, schema_name=schema_name)
    except Exception as e:
        logger.error(f"Error deploying GO definitions: {str(e)}")
        return
    
    # Print deployment messages
    for message in go_messages:
        if "Error" in message or "Failed" in message:
            logger.error(f"  - {message}")
        else:
            logger.info(f"  - {message}")
    
    # Print deployment status
    if go_success:
        logger.info("GO deployment successful!")
    else:
        logger.error("GO deployment failed!")

def deploy_los(lo_file: str, schema_name: str, skip_validation: bool):
    """
    Deploy LOs from prescriptive text.
    
    Args:
        lo_file: Path to the input file containing LO definitions
        schema_name: Name of the database schema to use
        skip_validation: Whether to skip validation
    """
    # Read input file
    try:
        with open(lo_file, 'r') as f:
            lo_text = f.read()
    except Exception as e:
        logger.error(f"Error reading LO file: {str(e)}")
        return
    
    # Parse LO definitions
    logger.info("Parsing LO definitions...")
    try:
        lo_definitions, lo_warnings = parse_lo_definitions(lo_text)
    except Exception as e:
        logger.error(f"Error parsing LO definitions: {str(e)}")
        return
    
    # Print warnings
    if lo_warnings:
        logger.warning("Warnings during LO parsing:")
        for warning in lo_warnings:
            logger.warning(f"  - {warning}")
    
    # Print parsed LO definitions
    logger.info(f"Parsed {len(lo_definitions)} LO definitions:")
    for lo_name, lo_definition in lo_definitions.items():
        logger.info(f"  - {lo_name}")
    
    # Validate LO definitions
    if not skip_validation:
        logger.info("Validating LO definitions...")
        # TODO: Implement LO validation
    
    # Deploy LO definitions
    logger.info("Deploying LO definitions...")
    try:
        lo_success, lo_messages = deploy_lo_definitions(lo_definitions, schema_name=schema_name)
    except Exception as e:
        logger.error(f"Error deploying LO definitions: {str(e)}")
        return
    
    # Print deployment messages
    for message in lo_messages:
        if "Error" in message or "Failed" in message:
            logger.error(f"  - {message}")
        else:
            logger.info(f"  - {message}")
    
    # Print deployment status
    if lo_success:
        logger.info("LO deployment successful!")
    else:
        logger.error("LO deployment failed!")

def main():
    """
    Main function.
    """
    # Parse command line arguments
    parser = argparse.ArgumentParser(description='Deploy entities, GOs, and LOs from prescriptive text.')
    parser.add_argument('--entity-file', help='Path to the input file containing entity definitions')
    parser.add_argument('--go-file', help='Path to the input file containing GO definitions')
    parser.add_argument('--lo-file', help='Path to the input file containing LO definitions')
    parser.add_argument('--schema', default='workflow_temp', help='Name of the database schema to use')
    parser.add_argument('--skip-validation', action='store_true', help='Skip validation')
    args = parser.parse_args()
    
    # Check if at least one file is provided
    if not args.entity_file and not args.go_file and not args.lo_file:
        parser.error("At least one of --entity-file, --go-file, or --lo-file is required")
    
    # Deploy entities, GOs, and LOs
    deploy_entities_go_lo(
        entity_file=args.entity_file,
        go_file=args.go_file,
        lo_file=args.lo_file,
        schema_name=args.schema,
        skip_validation=args.skip_validation
    )

if __name__ == "__main__":
    main()
