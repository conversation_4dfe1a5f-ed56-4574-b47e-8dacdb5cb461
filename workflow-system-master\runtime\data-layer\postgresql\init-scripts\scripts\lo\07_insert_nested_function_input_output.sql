-- Purchase Furniture Nested Function Input/Output Items Insert Script
-- Inserts input and output items for nested functions

SET search_path TO workflow_runtime;

-- =====================================================
-- NESTED FUNCTION INPUT ITEMS
-- =====================================================

-- Nested Function Input Items
INSERT INTO lo_nested_function_input_items (
    item_id, nested_function_id, input_stack_id, slot_id, source_type, 
    source_description, required, lo_id, data_type, name, 
    created_at, updated_at, created_by, updated_by, version, natural_language
) VALUES 
-- NF_CALCULATE_SUBTOTAL inputs
('NF_CALC_SUB_IN1', 'NF_CALCULATE_SUBTOTAL', 'NF_CALC_SUB_IN_STACK', 'NF_CALC_SUB_SL1', 'input', 'Quantity from user input', true, 'GO2.LO2', 'integer', 'quantity', NOW(), NOW(), 'system', 'system', '1.0', 'Quantity input for subtotal calculation'),
('NF_CALC_SUB_IN2', 'NF_CALCULATE_SUBTOTAL', 'NF_CALC_SUB_IN_STACK', 'NF_CALC_SUB_SL2', 'mapping', 'Unit price from product selection', true, 'GO2.LO2', 'decimal', 'unitprice', NOW(), NOW(), 'system', 'system', '1.0', 'Unit price input for subtotal calculation'),

-- NF_CALCULATE_GST inputs
('NF_CALC_GST_IN1', 'NF_CALCULATE_GST', 'NF_CALC_GST_IN_STACK', 'NF_CALC_GST_SL1', 'nested_output', 'Subtotal from previous calculation', true, 'GO2.LO2', 'decimal', 'subtotal', NOW(), NOW(), 'system', 'system', '1.0', 'Subtotal input for GST calculation'),

-- NF_CALCULATE_TOTAL inputs
('NF_CALC_TOT_IN1', 'NF_CALCULATE_TOTAL', 'NF_CALC_TOT_IN_STACK', 'NF_CALC_TOT_SL1', 'nested_output', 'Subtotal from calculation', true, 'GO2.LO2', 'decimal', 'subtotal', NOW(), NOW(), 'system', 'system', '1.0', 'Subtotal input for total calculation'),
('NF_CALC_TOT_IN2', 'NF_CALCULATE_TOTAL', 'NF_CALC_TOT_IN_STACK', 'NF_CALC_TOT_SL2', 'nested_output', 'GST amount from calculation', true, 'GO2.LO2', 'decimal', 'gstamount', NOW(), NOW(), 'system', 'system', '1.0', 'GST amount input for total calculation'),

-- NF_UPDATE_INVENTORY inputs
('NF_UPD_INV_IN1', 'NF_UPDATE_INVENTORY', 'NF_UPD_INV_IN_STACK', 'NF_UPD_INV_SL1', 'mapping', 'Product ID from selection', true, 'GO2.LO3', 'string', 'productid', NOW(), NOW(), 'system', 'system', '1.0', 'Product ID for inventory update'),
('NF_UPD_INV_IN2', 'NF_UPDATE_INVENTORY', 'NF_UPD_INV_IN_STACK', 'NF_UPD_INV_SL2', 'mapping', 'Quantity from order', true, 'GO2.LO3', 'integer', 'quantity', NOW(), NOW(), 'system', 'system', '1.0', 'Quantity for inventory reduction');

-- =====================================================
-- NESTED FUNCTION OUTPUT ITEMS
-- =====================================================

-- Nested Function Output Items
INSERT INTO lo_nested_function_output_items (
    item_id, nested_function_id, output_stack_id, slot_id, source_type, 
    source_description, lo_id, data_type, name, 
    created_at, updated_at, created_by, updated_by, version, natural_language
) VALUES 
-- NF_CALCULATE_SUBTOTAL outputs
('NF_CALC_SUB_OUT1', 'NF_CALCULATE_SUBTOTAL', 'NF_CALC_SUB_OUT_STACK', 'NF_CALC_SUB_OSL1', 'calculated', 'Calculated subtotal', 'GO2.LO2', 'decimal', 'subtotal', NOW(), NOW(), 'system', 'system', '1.0', 'Subtotal calculation result'),

-- NF_CALCULATE_GST outputs
('NF_CALC_GST_OUT1', 'NF_CALCULATE_GST', 'NF_CALC_GST_OUT_STACK', 'NF_CALC_GST_OSL1', 'calculated', 'Calculated GST amount', 'GO2.LO2', 'decimal', 'gstamount', NOW(), NOW(), 'system', 'system', '1.0', 'GST calculation result'),

-- NF_CALCULATE_TOTAL outputs
('NF_CALC_TOT_OUT1', 'NF_CALCULATE_TOTAL', 'NF_CALC_TOT_OUT_STACK', 'NF_CALC_TOT_OSL1', 'calculated', 'Calculated total amount', 'GO2.LO2', 'decimal', 'totalamount', NOW(), NOW(), 'system', 'system', '1.0', 'Total amount calculation result'),

-- NF_GENERATE_ORDER_ID outputs
('NF_GEN_ORD_OUT1', 'NF_GENERATE_ORDER_ID', 'NF_GEN_ORD_OUT_STACK', 'NF_GEN_ORD_OSL1', 'generated', 'Generated order ID', 'GO2.LO3', 'string', 'orderid', NOW(), NOW(), 'system', 'system', '1.0', 'Generated order identifier'),

-- NF_UPDATE_INVENTORY outputs
('NF_UPD_INV_OUT1', 'NF_UPDATE_INVENTORY', 'NF_UPD_INV_OUT_STACK', 'NF_UPD_INV_OSL1', 'updated', 'Inventory update status', 'GO2.LO3', 'boolean', 'inventoryupdated', NOW(), NOW(), 'system', 'system', '1.0', 'Inventory update confirmation');

COMMIT;
