#!/usr/bin/env python3
"""
Check the structure of the entities table in the workflow_temp schema.
"""

import os
import sys
import logging
import psycopg2

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('check_entities_table')

def main():
    """Main function."""
    try:
        # Connect to the database
        conn = psycopg2.connect(
            dbname="workflow_system",
            user="postgres",
            password="workflow_postgres_secure_password",
            host="**********",
            port="5432"
        )
        
        # Create a cursor
        cursor = conn.cursor()
        
        # Get the columns of the entities table
        cursor.execute("""
            SELECT column_name, data_type
            FROM information_schema.columns
            WHERE table_schema = 'workflow_temp'
            AND table_name = 'entities'
        """)
        
        # Fetch the results
        columns = cursor.fetchall()
        
        # Print the columns
        print("Columns in workflow_temp.entities:")
        for column in columns:
            print(f"{column[0]} ({column[1]})")
        
        # Close the cursor and connection
        cursor.close()
        conn.close()
    except Exception as e:
        logger.error(f"Error: {str(e)}")
        sys.exit(1)

if __name__ == '__main__':
    main()
