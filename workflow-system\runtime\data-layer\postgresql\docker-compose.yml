version: '3.8'

services:
  postgres:
    image: postgres:15
    container_name: workflow_postgres
    restart: always
    ports:
      - "5433:5432"  # Changed from 5432 to 5433
    environment:
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
      POSTGRES_DB: workflow_system
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init-scripts:/docker-entrypoint-initdb.d
    networks:
      - workflow_network

  pgadmin:
    image: dpage/pgadmin4
    container_name: workflow_pgadmin
    restart: always
    ports:
      - "5051:80"  # Changed from 5050 to 5051
    environment:
      PGADMIN_DEFAULT_EMAIL: <EMAIL>
      PGADMIN_DEFAULT_PASSWORD: ${PGADMIN_PASSWORD}
    volumes:
      - pgadmin_data:/var/lib/pgadmin
    networks:
      - workflow_network
    depends_on:
      - postgres

volumes:
  postgres_data:
  pgadmin_data:

networks:
  workflow_network:
    driver: bridge
