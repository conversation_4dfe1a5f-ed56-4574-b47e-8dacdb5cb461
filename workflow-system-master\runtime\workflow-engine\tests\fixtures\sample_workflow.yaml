# Sample Workflow Definition

tenant:
  id: "test-tenant"
  name: "Test Tenant"

entities:
  - id: "Customer"
    name: "Customer"
    version: "1.0"
    status: "Active"
    type: "Regular"
    attributes:
      - id: "FirstName"
        name: "First Name"
        datatype: "String"
        version: "1.0"
        status: "Deployed"
        required: true
      - id: "LastName"
        name: "Last Name"
        datatype: "String"
        version: "1.0"
        status: "Deployed"
        required: true
      - id: "Email"
        name: "Email"
        datatype: "String"
        version: "1.0"
        status: "Deployed"
        required: true
        validations:
          - rule: "Email format validation"
            expression: "^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$"
      - id: "Status"
        name: "Status"
        datatype: "Enum"
        version: "1.0"
        status: "Deployed"
        required: true
        values: ["New", "Active", "Inactive"]

global_objective:
  name: "Customer Onboarding"
  contextual_id: "GO_CustomerOnboarding"
  status: "Active"
  version: "1.0"
  
  # Local Objectives
  local_objectives:
    - id: "LO_CaptureCustomerInfo"
      contextual_id: "GO_CustomerOnboarding.LO_CaptureCustomerInfo"
      workflow_source: "Origin"
      name: "Capture Customer Information"
      function_type: "Create"
      execution_pathway:
        type: "Sequential"
        next_lo: "LO_ValidateCustomerInfo"
      
      # Agent Stack
      agent_stack:
        agents:
          - role: "User"
            rights: ["Execute", "Information"]
      
      # Input Stack
      input_stack:
        description: "Defines customer information inputs."
        inputs:
          - slot_id: "Customer.FirstName"
            source:
              type: "User"
              description: "Customer's first name."
            required: true
            validations:
              set1: "NotEmpty"
            access_roles: ["User"]
          
          - slot_id: "Customer.LastName"
            source:
              type: "User"
              description: "Customer's last name."
            required: true
            validations:
              set1: "NotEmpty"
            access_roles: ["User"]
          
          - slot_id: "Customer.Email"
            source:
              type: "User"
              description: "Customer's email address."
            required: true
            validations:
              set1: "ValidEmail"
            access_roles: ["User"]
      
      # Execute Rules
      execution_rules:
        - "When all required inputs are provided, database create operation is triggered"
        - "After successful creation, proceed to next LO"
      
      # Success Message
      success_message: "Customer information has been successfully captured"
      
      # UI Stack
      ui_stack:
        type: "Human"
        status: "Applicable"
        description: "Defines the UI components for customer information capture."
        overall_control: "Form"
        form_title: "New Customer Information"
        submit_button_text: "Submit"
        cancel_button_text: "Cancel"
        elements:
          - entity_attribute: "Customer.FirstName"
            ui_control: "TextBox"
            helper_text: "Enter customer's first name"
            error_message: "First name is required"
          - entity_attribute: "Customer.LastName"
            ui_control: "TextBox"
            helper_text: "Enter customer's last name"
            error_message: "Last name is required"
          - entity_attribute: "Customer.Email"
            ui_control: "Email"
            helper_text: "Enter customer's email address"
            error_message: "Valid email is required"
    
    - id: "LO_ValidateCustomerInfo"
      contextual_id: "GO_CustomerOnboarding.LO_ValidateCustomerInfo"
      name: "Validate Customer Information"
      function_type: "Validate"
      execution_pathway:
        type: "Alternative"
        conditions:
          - condition: "validation_result = Valid"
            next_lo: "LO_ActivateCustomer"
          - condition: "validation_result = Invalid"
            next_lo: "LO_CaptureCustomerInfo"
          - condition: "DEFAULT"
            next_lo: "LO_CaptureCustomerInfo"
      
      # Function
      function:
        name: "validate"
        parameters:
          entity: "Customer"
          fields: ["FirstName", "LastName", "Email"]
          rules: {
            "Email": "^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$"
          }
    
    - id: "LO_ActivateCustomer"
      contextual_id: "GO_CustomerOnboarding.LO_ActivateCustomer"
      name: "Activate Customer"
      function_type: "Update"
      execution_pathway:
        type: "Terminal"
        next_lo: "end"
      
      # Function
      function:
        name: "update"
        parameters:
          entity: "Customer"
          id: "{Customer.Id}"
          attributes: ["Status"]
      
      # Input Stack
      input_stack:
        description: "Input for activating customer."
        inputs:
          - slot_id: "Customer.Status"
            source:
              type: "System"
              value: "Active"
            required: true
      
      # Success Message
      success_message: "Customer has been successfully activated"
