"""
Role Parser for YAML Builder v2

This module provides functionality for parsing natural language prescriptives
into structured Role data for database storage.
"""

import os
import sys
import re
import logging
from typing import Dict, List, Tuple, Any, Optional

# Set up logging
logger = logging.getLogger('role_parser')

def parse_roles(prescriptive_text: str) -> Tuple[Dict, List[str]]:
    """
    Parse natural language prescriptives into structured Role data.
    
    Args:
        prescriptive_text: Natural language prescriptive text
        
    Returns:
        Tuple containing:
            - Structured Role data dictionary
            - List of warning messages (empty if no warnings)
    """
    warnings = []
    roles = {}
    
    try:
        # Split the text into role definitions
        role_blocks = re.split(r'\n\s*\n', prescriptive_text)
        
        for block in role_blocks:
            if not block.strip():
                continue
            
            # Parse role definition
            role_data, role_warnings = parse_role_block(block)
            warnings.extend(role_warnings)
            
            if role_data:
                role_id = role_data.get('role_id')
                if role_id:
                    roles[role_id] = role_data
                else:
                    warnings.append(f"Role without an ID found in block: {block[:100]}...")
        
        if not roles:
            warnings.append("No roles found in the prescriptive text")
            return {"roles": {}}, warnings
        
        return {"roles": roles}, warnings
    except Exception as e:
        logger.error(f"Error parsing roles: {str(e)}", exc_info=True)
        warnings.append(f"Error parsing roles: {str(e)}")
        return {"roles": {}}, warnings

def parse_role_block(block: str) -> Tuple[Dict, List[str]]:
    """
    Parse a single role block.
    
    Args:
        block: Role block text
        
    Returns:
        Tuple containing:
            - Role data dictionary
            - List of warning messages (empty if no warnings)
    """
    warnings = []
    role_data = {
        "permissions": [],
        "special_conditions": []
    }
    
    try:
        # Extract role ID and name
        first_line_match = re.match(r'Role: ([\w-]+) - (.*)', block)
        if first_line_match:
            role_id = first_line_match.group(1)
            role_name = first_line_match.group(2)
            
            role_data['role_id'] = role_id
            role_data['name'] = role_name
        else:
            warnings.append(f"Could not extract role ID and name from block: {block[:100]}...")
            return {}, warnings
        
        # Extract description
        description_match = re.search(r'Description:(.*?)(?:\n\s*\n|\n\w|\Z)', block, re.DOTALL)
        if description_match:
            description = description_match.group(1).strip()
            role_data['description'] = description
        
        # Extract scope
        scope_match = re.search(r'Scope: (\w+)', block)
        if scope_match:
            scope = scope_match.group(1)
            role_data['scope'] = scope
        
        # Extract classification
        classification_match = re.search(r'Classification: (\w+)', block)
        if classification_match:
            classification = classification_match.group(1)
            role_data['classification'] = classification
        
        # Extract tenant
        tenant_match = re.search(r'Tenant: (\w+)', block)
        if tenant_match:
            tenant = tenant_match.group(1)
            role_data['tenant_id'] = tenant
        
        # Extract permissions
        permissions_match = re.search(r'Permissions:(.*?)(?:\n\s*\n|\nInherits|\nSpecial Conditions|\Z)', block, re.DOTALL)
        if permissions_match:
            permissions_text = permissions_match.group(1)
            permissions, perm_warnings = parse_permissions(permissions_text)
            role_data['permissions'] = permissions
            warnings.extend(perm_warnings)
        
        # Extract inheritance
        inherits_match = re.search(r'Inherits: (\w+)', block)
        if inherits_match:
            inherits = inherits_match.group(1)
            role_data['inherits'] = inherits
        
        # Extract special conditions
        special_conditions_match = re.search(r'Special Conditions:(.*?)(?:\n\s*\n|\Z)', block, re.DOTALL)
        if special_conditions_match:
            special_conditions_text = special_conditions_match.group(1)
            special_conditions, condition_warnings = parse_special_conditions(special_conditions_text)
            role_data['special_conditions'] = special_conditions
            warnings.extend(condition_warnings)
        
        # Clean up empty lists
        for key in list(role_data.keys()):
            if isinstance(role_data[key], list) and not role_data[key]:
                del role_data[key]
        
        return role_data, warnings
    except Exception as e:
        logger.error(f"Error parsing role block: {str(e)}", exc_info=True)
        warnings.append(f"Error parsing role block: {str(e)}")
        return {}, warnings

def parse_permissions(permissions_text: str) -> Tuple[List[str], List[str]]:
    """
    Parse permissions from text.
    
    Args:
        permissions_text: Permissions text
        
    Returns:
        Tuple containing:
            - List of permissions
            - List of warning messages (empty if no warnings)
    """
    warnings = []
    permissions = []
    
    try:
        # Split permissions by newline
        permission_lines = permissions_text.strip().split('\n')
        
        for line in permission_lines:
            line = line.strip()
            if not line:
                continue
            
            # Parse permission line
            permission_match = re.match(r'- (.*)', line)
            if permission_match:
                permission = permission_match.group(1).strip()
                permissions.append(permission)
            else:
                warnings.append(f"Could not parse permission line: {line}")
        
        return permissions, warnings
    except Exception as e:
        logger.error(f"Error parsing permissions: {str(e)}", exc_info=True)
        warnings.append(f"Error parsing permissions: {str(e)}")
        return [], warnings

def parse_special_conditions(special_conditions_text: str) -> Tuple[List[str], List[str]]:
    """
    Parse special conditions from text.
    
    Args:
        special_conditions_text: Special conditions text
        
    Returns:
        Tuple containing:
            - List of special conditions
            - List of warning messages (empty if no warnings)
    """
    warnings = []
    special_conditions = []
    
    try:
        # Split special conditions by newline
        condition_lines = special_conditions_text.strip().split('\n')
        
        for line in condition_lines:
            line = line.strip()
            if not line:
                continue
            
            # Parse condition line
            condition_match = re.match(r'- (.*)', line)
            if condition_match:
                condition = condition_match.group(1).strip()
                special_conditions.append(condition)
            else:
                warnings.append(f"Could not parse special condition line: {line}")
        
        return special_conditions, warnings
    except Exception as e:
        logger.error(f"Error parsing special conditions: {str(e)}", exc_info=True)
        warnings.append(f"Error parsing special conditions: {str(e)}")
        return [], warnings
