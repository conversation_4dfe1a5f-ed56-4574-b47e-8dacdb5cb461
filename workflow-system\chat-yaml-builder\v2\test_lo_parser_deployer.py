import os
import sys
import logging
import json
from typing import Dict, List, Tuple, Any, Optional

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Add parent directory to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Import parser and deployer
from parsers.lo_parser import parse_lo_definitions
from deployers.lo_deployer import deploy_lo_definitions
from db_utils import get_db_connection

def test_lo_parser_deployer():
    """
    Test the LO parser and deployer with sample LO definitions.
    """
    # Read sample LO definitions
    with open('samples/sample_lo_output_cleaned.txt', 'r') as f:
        sample_lo_text = f.read()
    
    # Parse LO definitions
    logger.info("Parsing LO definitions...")
    lo_definitions, warnings = parse_lo_definitions(sample_lo_text)
    
    # Print warnings
    if warnings:
        logger.warning("Warnings during parsing:")
        for warning in warnings:
            logger.warning(f"  - {warning}")
    
    # Print parsed LO definitions
    logger.info(f"Parsed {len(lo_definitions)} LO definitions:")
    for lo_name, lo_definition in lo_definitions.items():
        logger.info(f"  - {lo_name}")
    
    # Deploy LO definitions
    logger.info("Deploying LO definitions...")
    success, messages = deploy_lo_definitions(lo_definitions, schema_name="workflow_temp")
    
    # Print deployment messages
    for message in messages:
        if "Error" in message or "Failed" in message:
            logger.error(f"  - {message}")
        else:
            logger.info(f"  - {message}")
    
    # Print deployment status
    if success:
        logger.info("Deployment successful!")
    else:
        logger.error("Deployment failed!")
    
    # Verify deployment
    verify_deployment(lo_definitions)

def verify_deployment(lo_definitions: Dict):
    """
    Verify that LO definitions were deployed correctly.
    
    Args:
        lo_definitions: Dictionary of parsed LO definitions
    """
    # Connect to database
    conn = get_db_connection(schema_name="workflow_temp")
    if not conn:
        logger.error("Failed to connect to database")
        return
    
    try:
        # Create cursor
        cursor = conn.cursor()
        
        # Check if LOs were deployed
        logger.info("Verifying LO deployment...")
        for lo_name, lo_definition in lo_definitions.items():
            lo_id = lo_definition.get("lo_id", "")
            
            # Check if LO exists
            cursor.execute("SELECT lo_id FROM workflow_temp.local_objectives WHERE lo_id = %s", (lo_id,))
            if cursor.fetchone():
                logger.info(f"  - LO '{lo_name}' (ID: {lo_id}) was deployed successfully")
            else:
                logger.error(f"  - LO '{lo_name}' (ID: {lo_id}) was not deployed")
        
        # Check input stacks
        logger.info("Verifying input stacks...")
        for lo_name, lo_definition in lo_definitions.items():
            lo_id = lo_definition.get("lo_id", "")
            
            # Check if input stack exists
            cursor.execute("SELECT id FROM workflow_temp.lo_input_stack WHERE lo_id = %s", (lo_id,))
            result = cursor.fetchone()
            if result:
                input_stack_id = result[0]
                logger.info(f"  - Input stack for LO '{lo_name}' (ID: {lo_id}) was deployed successfully")
                
                # Check input items
                cursor.execute("SELECT COUNT(*) FROM workflow_temp.lo_input_items WHERE input_stack_id = %s", (input_stack_id,))
                count = cursor.fetchone()[0]
                logger.info(f"    - {count} input items were deployed")
            else:
                logger.error(f"  - Input stack for LO '{lo_name}' (ID: {lo_id}) was not deployed")
        
        # Check output stacks
        logger.info("Verifying output stacks...")
        for lo_name, lo_definition in lo_definitions.items():
            lo_id = lo_definition.get("lo_id", "")
            
            # Check if output stack exists
            cursor.execute("SELECT id FROM workflow_temp.lo_output_stack WHERE lo_id = %s", (lo_id,))
            result = cursor.fetchone()
            if result:
                output_stack_id = result[0]
                logger.info(f"  - Output stack for LO '{lo_name}' (ID: {lo_id}) was deployed successfully")
                
                # Check output items
                cursor.execute("SELECT COUNT(*) FROM workflow_temp.lo_output_items WHERE output_stack_id = %s", (output_stack_id,))
                count = cursor.fetchone()[0]
                logger.info(f"    - {count} output items were deployed")
            else:
                logger.error(f"  - Output stack for LO '{lo_name}' (ID: {lo_id}) was not deployed")
        
        # Check execution pathways
        logger.info("Verifying execution pathways...")
        for lo_name, lo_definition in lo_definitions.items():
            lo_id = lo_definition.get("lo_id", "")
            
            # Check if execution pathways exist
            cursor.execute("SELECT COUNT(*) FROM workflow_temp.execution_pathways WHERE lo_id = %s", (lo_id,))
            count = cursor.fetchone()[0]
            logger.info(f"  - {count} execution pathways for LO '{lo_name}' (ID: {lo_id}) were deployed")
            
            # Check execution pathway conditions
            cursor.execute("SELECT COUNT(*) FROM workflow_temp.execution_pathway_conditions WHERE lo_id = %s", (lo_id,))
            count = cursor.fetchone()[0]
            logger.info(f"  - {count} execution pathway conditions for LO '{lo_name}' (ID: {lo_id}) were deployed")
        
        # Close cursor
        cursor.close()
    except Exception as e:
        logger.error(f"Error verifying deployment: {str(e)}", exc_info=True)
    finally:
        conn.close()

def print_parsed_lo(lo_definitions: Dict):
    """
    Print parsed LO definitions in a readable format.
    
    Args:
        lo_definitions: Dictionary of parsed LO definitions
    """
    for lo_name, lo_definition in lo_definitions.items():
        print(f"\n=== {lo_name} ===")
        
        # Print metadata
        print("\nMetadata:")
        for key, value in lo_definition.items():
            if key not in ["input_stack", "output_stack", "execution_pathways", "system_functions", "mapping_stack", "ui_stack", "db_stack", "synthetic_values"]:
                print(f"  {key}: {value}")
        
        # Print input stack
        if "input_stack" in lo_definition:
            print("\nInput Stack:")
            print(f"  Description: {lo_definition['input_stack'].get('description', '')}")
            print("  Items:")
            for item in lo_definition["input_stack"].get("items", []):
                print(f"    - {item.get('name', '')}: {item}")
        
        # Print output stack
        if "output_stack" in lo_definition:
            print("\nOutput Stack:")
            print(f"  Description: {lo_definition['output_stack'].get('description', '')}")
            print("  Items:")
            for item in lo_definition["output_stack"].get("items", []):
                print(f"    - {item.get('name', '')}: {item}")
        
        # Print execution pathways
        if "execution_pathways" in lo_definition:
            print("\nExecution Pathways:")
            for pathway in lo_definition["execution_pathways"]:
                print(f"  - Type: {pathway.get('pathway_type', '')}")
                print(f"    Next LO: {pathway.get('next_lo', '')}")
                if "condition" in pathway:
                    print(f"    Condition: {pathway['condition']}")
        
        # Print system functions
        if "system_functions" in lo_definition:
            print("\nSystem Functions:")
            for function in lo_definition["system_functions"]:
                print(f"  - {function.get('function_name', '')}: {function}")
        
        # Print mapping stack
        if "mapping_stack" in lo_definition:
            print("\nMapping Stack:")
            for mapping in lo_definition["mapping_stack"]:
                print(f"  - {mapping.get('source_lo', '')}.{mapping.get('source_attribute', '')} -> {mapping.get('target_lo', '')}.{mapping.get('target_attribute', '')}")
        
        # Print UI stack
        if "ui_stack" in lo_definition:
            print("\nUI Stack:")
            for ui_item in lo_definition["ui_stack"]:
                print(f"  - {ui_item}")
        
        # Print DB stack
        if "db_stack" in lo_definition:
            print("\nDB Stack:")
            for db_item in lo_definition["db_stack"]:
                print(f"  - {db_item}")
        
        # Print synthetic values
        if "synthetic_values" in lo_definition:
            print("\nSynthetic Values:")
            for key, values in lo_definition["synthetic_values"].items():
                print(f"  - {key}: {values}")

if __name__ == "__main__":
    test_lo_parser_deployer()
