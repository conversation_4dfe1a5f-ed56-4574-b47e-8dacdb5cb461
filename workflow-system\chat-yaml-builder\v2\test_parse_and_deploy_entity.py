#!/usr/bin/env python3
"""
Test script to parse a sample entity file and deploy it to the database.
"""

import os
import sys
import logging
import json
from typing import Dict, List, Tuple, Any, Optional

# Add the parent directory to the Python path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Import the entity parser and deployer
from parsers.entity_parser import parse_entity_text
from deployers.entity_deployer_v2 import deploy_entities

# Set up logging
logging.basicConfig(level=logging.INFO,
                   format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
                   handlers=[logging.StreamHandler()])
logger = logging.getLogger('test_parse_and_deploy_entity')

def read_sample_file(file_path: str) -> str:
    """
    Read the sample entity file.
    
    Args:
        file_path: Path to the sample entity file
        
    Returns:
        The content of the sample entity file
    """
    try:
        with open(file_path, 'r') as f:
            return f.read()
    except Exception as e:
        logger.error(f"Error reading sample file: {str(e)}")
        return ""

def parse_and_deploy_entity(sample_text: str, schema_name: str) -> Tuple[bool, List[str]]:
    """
    Parse the sample entity text and deploy it to the database.
    
    Args:
        sample_text: The sample entity text
        schema_name: The database schema to deploy to
        
    Returns:
        Tuple containing:
            - Boolean indicating if the operation was successful
            - List of messages (warnings, errors, or success messages)
    """
    messages = []
    
    try:
        # Parse the entity text
        logger.info("Parsing entity text")
        success, entity_data, parse_messages = parse_entity_text(sample_text)
        messages.extend(parse_messages)
        
        if not success:
            logger.error("Failed to parse entity text")
            return False, messages
        
        # Save the parsed entity data to a file for inspection
        with open('parsed_entity_data.json', 'w') as f:
            json.dump(entity_data, f, indent=2)
        
        logger.info("Parsed entity data saved to parsed_entity_data.json")
        
        # Deploy the parsed entity to the database
        logger.info(f"Deploying entity to schema {schema_name}")
        success, deploy_messages = deploy_entities(entity_data, schema_name)
        messages.extend(deploy_messages)
        
        if not success:
            logger.error("Failed to deploy entity")
            return False, messages
        
        logger.info("Successfully deployed entity")
        return True, messages
    except Exception as e:
        error_msg = f"Error parsing and deploying entity: {str(e)}"
        logger.error(error_msg, exc_info=True)
        messages.append(error_msg)
        return False, messages

def main():
    """
    Main function to parse and deploy the sample entity.
    """
    # Set the schema name
    schema_name = 'workflow_temp'
    
    # Set the sample file path
    sample_file_path = 'samples/sample_entity_output2.txt'
    
    # Read the sample file
    sample_text = read_sample_file(sample_file_path)
    
    if not sample_text:
        logger.error(f"Failed to read sample file: {sample_file_path}")
        return
    
    # Parse and deploy the entity
    success, messages = parse_and_deploy_entity(sample_text, schema_name)
    
    # Print the messages
    for message in messages:
        logger.info(message)
    
    if success:
        logger.info("Successfully parsed and deployed entity")
    else:
        logger.error("Failed to parse and deploy entity")

if __name__ == "__main__":
    main()
