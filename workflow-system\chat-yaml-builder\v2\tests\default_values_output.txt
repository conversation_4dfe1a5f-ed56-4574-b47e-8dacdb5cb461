=== Employee Entity Attributes ===
Attribute: employeeId
Attribute: firstName
Attribute: lastName
Attribute: email
  Default Value: "@company.com"
Attribute: phoneNumber
Attribute: departmentId
Attribute: managerId
Attribute: hireDate
  Default Value: CURRENT_DATE
Attribute: status
  Default Value: Active
Attribute: salary
Attribute: performanceRating
Attribute: probationDays
  Property Value: 90
Attribute: minSalary
  Property Value: 30000
Attribute: fullName
Attribute: managerId with Department constrains Manager

=== Specific Attributes ===
Employee.probationDays PROPERTY_NAME = 90
Employee.minSalary PROPERTY_NAME = 30000
Employee.status DEFAULT_VALUE = Active
Employee.hireDate DEFAULT_VALUE = CURRENT_DATE
