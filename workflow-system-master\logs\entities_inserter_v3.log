2025-06-23 14:03:48,254 - inserters.v3.roles.entities_inserter - INFO - Starting process_mongo_entities_to_workflow_temp
2025-06-23 14:03:48,257 - inserters.v3.roles.entities_inserter - INFO - Found 1 entities with status 'draft' in MongoDB
2025-06-23 14:03:48,266 - inserters.v3.roles.entities_inserter - INFO - Starting insert_entity_to_workflow_temp for entity: Employee
2025-06-23 14:03:48,266 - inserters.v3.roles.entities_inserter - INFO - Field validation passed: 2/2 required fields, 15/15 optional fields
2025-06-23 14:03:48,294 - inserters.v3.roles.entities_inserter - INFO - Incremented entity ID from E13 to E26
2025-06-23 14:03:48,295 - inserters.v3.roles.entities_inserter - INFO - Additional MongoDB fields preserved: {'entity_status': 'new', 'changes_detected': [], 'icon_type': 'text', 'icon_content': '', 'original_created_at': '2025-06-19T14:37:01.860351', 'original_updated_at': '2025-06-20T11:34:42.214059', 'original_created_by': 'Tarun', 'original_updated_by': 'Tarun'}
2025-06-23 14:03:48,296 - inserters.v3.roles.entities_inserter - INFO - Successfully inserted entity to workflow_temp with ID: E26
2025-06-23 14:03:48,299 - inserters.v3.roles.entities_inserter - INFO - Updated MongoDB status to deployed_to_temp for entity: E26
2025-06-23 14:03:48,299 - inserters.v3.roles.entities_inserter - INFO - Completed process_mongo_entities_to_workflow_temp: 1 successful, 0 failed
2025-06-23 14:08:59,786 - inserters.v3.roles.entities_inserter - INFO - Starting process_mongo_entities_to_workflow_temp
2025-06-23 14:08:59,789 - inserters.v3.roles.entities_inserter - INFO - Found 0 entities with status 'draft' in MongoDB
2025-06-23 14:08:59,789 - inserters.v3.roles.entities_inserter - INFO - Completed process_mongo_entities_to_workflow_temp: 0 successful, 0 failed
2025-06-23 14:32:31,997 - inserters.v3.roles.entities_inserter - INFO - Starting process_mongo_entities_to_workflow_temp
2025-06-23 14:32:31,999 - inserters.v3.roles.entities_inserter - INFO - Found 0 entities with status 'draft' in MongoDB
2025-06-23 14:32:31,999 - inserters.v3.roles.entities_inserter - INFO - Completed process_mongo_entities_to_workflow_temp: 0 successful, 0 failed
2025-06-23 14:38:51,699 - inserters.v3.roles.entities_inserter - INFO - Starting process_mongo_entities_to_workflow_temp
2025-06-23 14:38:51,701 - inserters.v3.roles.entities_inserter - INFO - Found 0 entities with status 'draft' in MongoDB
2025-06-23 14:38:51,701 - inserters.v3.roles.entities_inserter - INFO - Completed process_mongo_entities_to_workflow_temp: 0 successful, 0 failed
2025-06-24 04:45:01,281 - inserters.v3.roles.entities_inserter - INFO - Starting process_mongo_entities_to_workflow_temp
2025-06-24 04:45:01,284 - inserters.v3.roles.entities_inserter - INFO - Found 0 entities with status 'draft' in MongoDB
2025-06-24 04:45:01,284 - inserters.v3.roles.entities_inserter - INFO - Completed process_mongo_entities_to_workflow_temp: 0 successful, 0 failed
2025-06-24 11:40:14,805 - inserters.v3.roles.entities_inserter - INFO - Starting process_mongo_entities_to_workflow_runtime
2025-06-24 11:40:14,810 - inserters.v3.roles.entities_inserter - INFO - Found 1 deployed_to_temp + 1 draft = 2 total entities in MongoDB
2025-06-24 11:40:14,818 - inserters.v3.roles.entities_inserter - INFO - Entity E13 already exists in workflow_runtime
2025-06-24 11:40:14,826 - inserters.v3.roles.entities_inserter - INFO - Starting insert_entity_to_workflow_runtime for entity: TestEntity
2025-06-24 11:40:14,826 - inserters.v3.roles.entities_inserter - INFO - Field validation passed: 2/2 required fields, 15/15 optional fields
2025-06-24 11:40:14,853 - inserters.v3.roles.entities_inserter - INFO - Incremented entity ID from E48 to E36
2025-06-24 11:40:14,853 - inserters.v3.roles.entities_inserter - INFO - Additional MongoDB fields preserved: {'entity_status': 'new', 'changes_detected': [], 'icon_type': 'text', 'icon_content': '', 'original_created_at': '2025-06-24T11:33:55.338334', 'original_updated_at': '2025-06-24T11:33:55.338343', 'original_created_by': 'Tarun', 'original_updated_by': 'Tarun'}
2025-06-24 11:40:14,855 - inserters.v3.roles.entities_inserter - INFO - Successfully inserted entity to workflow_runtime with ID: E36
2025-06-24 11:40:14,857 - inserters.v3.roles.entities_inserter - INFO - Updated MongoDB status to deployed_to_production for entity: E36
2025-06-24 11:40:14,857 - inserters.v3.roles.entities_inserter - INFO - Completed process_mongo_entities_to_workflow_runtime: 1 successful, 0 failed
2025-06-24 13:13:45,237 - inserters.v3.roles.entities_inserter - INFO - Starting process_mongo_entities_to_workflow_temp
2025-06-24 13:13:45,241 - inserters.v3.roles.entities_inserter - INFO - Found 1 entities with status 'draft' in MongoDB
2025-06-24 13:13:45,250 - inserters.v3.roles.entities_inserter - INFO - Starting insert_entity_to_workflow_temp for entity: TestEntity2
2025-06-24 13:13:45,251 - inserters.v3.roles.entities_inserter - INFO - Field validation passed: 2/2 required fields, 15/15 optional fields
2025-06-24 13:13:45,269 - inserters.v3.roles.entities_inserter - INFO - Incremented entity ID from E48 to E46
2025-06-24 13:13:45,269 - inserters.v3.roles.entities_inserter - INFO - Additional MongoDB fields preserved: {'entity_status': 'new', 'changes_detected': [], 'icon_type': 'text', 'icon_content': '', 'original_created_at': '2025-06-24T12:31:57.709761', 'original_updated_at': '2025-06-24T12:31:57.709768', 'original_created_by': 'Tarun', 'original_updated_by': 'Tarun'}
2025-06-24 13:13:45,272 - inserters.v3.roles.entities_inserter - INFO - Successfully inserted entity to workflow_temp with ID: E46
2025-06-24 13:13:45,275 - inserters.v3.roles.entities_inserter - INFO - Updated MongoDB status to deployed_to_temp for entity: E46
2025-06-24 13:13:45,275 - inserters.v3.roles.entities_inserter - INFO - Completed process_mongo_entities_to_workflow_temp: 1 successful, 0 failed
