# System Functions: Standardized Structure and Implementation Guide

This guide provides a comprehensive and standardized approach to defining system functions within your architecture, strictly adhering to existing entity and attribute names from your database schema and matching the platform's available system functions.

## 1. Core System Function Structure

```
## [FunctionName]_[SequenceNumber]

*Inputs: [Entity] with attribute1*, attribute2; [Entity2] with attribute3*

*Operation: [Precise function syntax with explicit parameters and entity.attribute references]*

*Description: [Natural language explanation for human readability]*

*Output: [Entity] with attribute1, attribute2; [Entity2] with attribute3*

*Error: When [Entity.attribute] [condition], returns "[error_message]"*
```

## 2. Naming Convention Rules

### 2.1 System Function Names
- **Exact Matching**: Use the exact system function names available in the platform (e.g., `fetch`, `validate_email`, `subtract_days`)
- **Case Sensitivity**: Match the exact case as provided in the platform documentation
- **Consistency**: Never create new function names or aliases for existing functions
- **Available Functions**: Only use function names from the documented list in section 8
- **Sequence Numbers**: When the same function is used multiple times within an LO, add a sequence number suffix (e.g., `validate_required_1`, `validate_required_2`)

### 2.2 Entity and Attribute Names
- **Entities**: Must use existing database entity names in PascalCase (e.g., `Order`, `Customer`, `Invoice`)
- **Attributes**: Must use existing database attribute names in camelCase (e.g., `firstName`, `orderTotal`)
- **No New Entities/Attributes**: Only use entity and attribute names that exist in your database schema
- **Explicit References**: Always prefix attributes with their entity name in operations (e.g., `Order.total`, not just `total`)
- **Consistency**: The same entity and attribute names must be used consistently throughout

## 3. Data Flow Mechanics

- **Source of Values**: System functions are the *source* of attribute values - they generate data that flows into both LO inputs and outputs
- **Value Flow**: When a system function generates a value, that value flows to wherever it's mapped - either to LO inputs, outputs, or other function inputs
- **Function Chaining**: Multiple functions can be chained together, where outputs from one function flow into inputs of another function within the same LO
- **Output Availability**: The final outputs of functions flow into the LO's output stack, making them available to the LO's consumers
- **Unidirectional Flow**: Data always flows from system functions outward to LO inputs, outputs, or other functions
- **Explicit References**: Always use fully qualified entity.attribute names when referencing data (e.g., `Invoice.subtotal`, not `subtotal`)
- **Complete Mappings**: Every output attribute from a function must be explicitly mapped to at least one destination

## 4. Mapping Relationships

### 4.1 Internal LO Mapping Stack
Used for mapping within the same LO:
- Function outputs mapping to LO inputs
- Function outputs mapping to other function inputs within the same LO
- Function outputs mapping to LO outputs

```
*Internal LO Mapping Stack:*
* [FunctionName]_[SequenceNumber].output.[Entity].[attribute] maps to [OtherFunctionName]_[SequenceNumber].input.[Entity].[attribute]
* [FunctionName]_[SequenceNumber].output.[Entity].[attribute] maps to [LOName].output.[Entity].[attribute]
```

### 4.2 LO Mapping Stack
Used for mapping between different LOs:
- LO outputs mapping to other LOs' inputs

```
*LO Mapping Stack:*
* [ThisLOName].output.[Entity].[attribute] maps to [NextLOName].input.[Entity].[attribute]
```

## 5. Key Principles

- **Naming Consistency**: Attribute names must remain consistent throughout - no new entities or attributes should be created during processing
- **No IDs or Metadata**: System uses direct references to function/entity names without relying on IDs, versions, or system-generated metadata
- **Self-contained Operations**: Each function performs one logical operation, which may involve a simple or complex calculation, transformation, or conditional logic - the function represents a complete logical unit of work
- **Clear Boundaries**: Functions operate only on the inputs they receive and produce only the outputs they declare
- **Explicit Mapping**: All data flows must be explicitly defined through mapping relationships
- **Direct Attribute References**: Always use the actual entity.attribute names in operations and mappings, never use placeholders like "RESULT" or unqualified attribute names
- **Parsable Syntax**: Operation descriptions must follow a consistent, structured syntax that can be parsed by the system
- **No Inference**: The system cannot infer any operations or data relationships - everything must be explicitly defined
- **Unique Function Instances**: When the same function is used multiple times within an LO, each instance must have a unique sequence number

## 6. Operation Syntax Rules

- **Function Calls**: Function name followed by parameters in parentheses: `functionName(param1, param2, ..., outputParam)`
- **Parameter Specification**: All parameters explicitly identified with their full entity.attribute names
- **Attribute References**: Always use fully qualified names (Entity.attribute)
- **String Values**: Enclose string literals and enum values in double quotes
- **Operation Chaining**: Use pipe symbol (|) to indicate sequential operations
- **Human Descriptions**: Include human-readable descriptions separate from parsable operation syntax
- **Output Parameter**: The last parameter in a function call is typically the output parameter
- **Literals vs References**: Distinguish between literal values (e.g., 0.07) and attribute references (e.g., Tax.rate)
- **Boolean Values**: Use "true" and "false" for boolean literals
- **Regex Patterns**: Include the full regex pattern with proper escaping (e.g., "^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$")
- **Sequence Numbers**: Add a sequence number when the same function is used multiple times (e.g., `validate_required_1`, `validate_required_2`)

## 7. Input/Output Declaration Rules

### 7.1 Input Declaration
- Format: `[Entity] with attribute1*, attribute2`, where * indicates mandatory fields
- Multiple entities separated by semicolons: `[Entity1] with attr1, attr2; [Entity2] with attr3`
- Default values indicated with `=`: `currencyCode [="USD"]`
- Dependent attributes indicated with `depends on`: `state* [depends on: country]`
- Enum values in parentheses: `status* (active, inactive, pending)`
- Information-only fields marked with `[info]`: `instructions [info]`
- Empty inputs indicated as `none` when function requires no inputs

### 7.2 Output Declaration
- Format: `[Entity] with attribute1, attribute2`
- Multiple entities separated by semicolons: `[Entity1] with attr1; [Entity2] with attr2`
- Only include attributes that are being set/modified by the function
- All attributes must exist in the database schema for the associated entity

## 8. Available System Functions by Category

### 8.1 Database Functions
- `create`: Insert a single record into a database table
- `fetch`: Fetch a single record from a database table
- `fetch_records`: Fetch multiple records from a database table
- `fetch_enum_values`: Fetch enum values for an entity attribute
- `fetch_filtered_records`: Fetch filtered records for dependent dropdowns
- `batch_update`: Update multiple records in a single operation
- `soft_delete`: Mark a record as deleted without removing it
- `fetch_max_value`: Fetch the max value of a numeric column
- `update`: Update records in a database table

### 8.2 Validation Functions
- `validate_audit_fields`: Validate audit fields for data operations
- `validate_pattern`: Validate input text against a regex pattern
- `validate_complex`: Validate data against multiple rules
- `validate_batch`: Validate multiple values in a single operation
- `validate_date_range`: Validate a date within a specified range
- `validate_email`: Validate email format
- `validate_required`: Validate non-empty value
- `enum_check`: Check if value exists in allowed enum list
- `entity_exists`: Check if a record exists in a table
- `compare`: Compare two values with a specified operator

### 8.3 Transform Functions
- `number_format`: Format numbers with locale options
- `string_template`: Populate a template with variable values
- `format_currency`: Format numeric values as currency
- `to_uppercase`: Convert text to uppercase
- `to_lowercase`: Convert text to lowercase
- `format_date`: Format a date to the specified format
- `to_json`: Convert data to JSON string

### 8.4 Math Functions
- `subtract_days`: Calculate days between two dates
- `add_days`: Add days to a date
- `round_number`: Round a number to specified decimal places
- `calculate_percentage`: Calculate percentage of one number to another
- `min_max`: Find minimum or maximum in a set of numbers
- `average`: Calculate average/mean of values
- `add`: Add two numbers
- `subtract`: Subtract one number from another
- `multiply`: Multiply two numbers
- `divide`: Divide one number by another

### 8.5 Data Functions
- `merge_dicts`: Merge two dictionaries
- `filter_dict`: Filter dictionary to include specific keys
- `deep_merge`: Recursively merge nested dictionaries
- `array_functions`: Perform operations on arrays
- `parse_csv`: Parse CSV data into structured format
- `extract_json_field`: Extract fields from JSON data
- `data_transform`: Transform data structure format
- `groupby_aggregate`: Group data and perform aggregations

### 8.6 Utility Functions
- `generate_id`: Generate next numeric ID
- `current_timestamp`: Get current ISO timestamp
- `notify`: Simple notification function
- `generate_random`: Generate random values
- `hash_data`: Create secure hash of sensitive data

### 8.7 Temporal Functions
- `date_functions`: Comprehensive date manipulation
- `count_business_days`: Count business days between dates
- `is_business_day`: Check if date is a business day

### 8.8 Control Flow Functions
- `conditional_logic`: Evaluate complex conditional expressions

## 9. System Function Examples

Here are examples using the correct format with platform-supported function names and precise operation syntax:

### 9.1 Math Function Example

```
## add_1

*Inputs: Order with subtotal*, shippingCost*

*Operation: add(Order.subtotal, Order.shippingCost, Order.subtotalWithShipping)*

*Description: Add the order subtotal and shipping cost to calculate the subtotal with shipping.*

*Output: Order with subtotalWithShipping*

*Error: When Order.subtotal is negative, returns "Order subtotal cannot be negative"*
```

### 9.2 Validation Function Example

```
## validate_email_1

*Inputs: Customer with email*

*Operation: validate_email(Customer.email, "^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$", Customer.isEmailValid)*

*Description: Check if the customer's email address follows a valid email format using standard email regex pattern.*

*Output: Customer with isEmailValid*

*Error: When Customer.email format is invalid, returns "Invalid email format"*
```

### 9.3 Database Function Example

```
## fetch_records_1

*Inputs: Customer with id*; Order with startDate, endDate, status*

*Operation: fetch_records("orders", {"customerId": Customer.id, "orderDate": {"$gte": Order.startDate, "$lte": Order.endDate}, "status": Order.status}, 100, 0, "orderDate DESC", Order.orderRecords)*

*Description: Retrieve all orders for the specified customer that fall within the date range and match the status, sorted by order date descending, limited to 100 records.*

*Output: Order with orderRecords*

*Error: When Customer.id not found, returns "Customer not found"*
```

### 9.4 Array Function Example

```
## array_functions_1

*Inputs: Invoice with items*

*Operation: array_functions(Invoice.items, "map", "multiply", ["price", "quantity"], "lineTotal", Invoice.itemsWithTotal)*

*Description: Calculate the line total for each item by multiplying price by quantity, creating a new array with line totals.*

*Output: Invoice with itemsWithTotal*

*Error: When Invoice.items is empty, returns "Invoice contains no items"*
```

```
## array_functions_2

*Inputs: Invoice with itemsWithTotal*

*Operation: array_functions(Invoice.itemsWithTotal, "sum", "lineTotal", "sum", Invoice.subtotal)*

*Description: Sum all line totals to get the invoice subtotal.*

*Output: Invoice with subtotal*

*Error: When Invoice.itemsWithTotal is empty, returns "Invoice contains no items with totals"*
```

### 9.5 Date Function Example

```
## add_days_1

*Inputs: Order with orderDate*; Shipping with deliveryDays*

*Operation: add_days(Order.orderDate, Shipping.deliveryDays, Order.estimatedDeliveryDate)*

*Description: Calculate the estimated delivery date by adding the shipping days to the order date.*

*Output: Order with estimatedDeliveryDate*

*Error: When Shipping.deliveryDays is negative, returns "Delivery days must be positive"*
```

## 10. Multiple Function Instances Example

This example demonstrates how to handle multiple instances of the same function within an LO:

```
## ValidateCustomerInfo

name: "Validate Customer Information"
function_type: "Update"

*[CustomerServiceRole] has update rights*

*Inputs: Customer with firstName*, lastName*, email*, phone*

*Nested System Functions:*

## validate_required_1
*Inputs: Customer with firstName*
*Operation: validate_required(Customer.firstName, "First name is required", Customer.isFirstNameValid)*
*Description: Validate that customer first name is provided.*
*Output: Customer with isFirstNameValid*
*Error: When Customer.firstName is empty, returns "First name is required"*

## validate_required_2
*Inputs: Customer with lastName*
*Operation: validate_required(Customer.lastName, "Last name is required", Customer.isLastNameValid)*
*Description: Validate that customer last name is provided.*
*Output: Customer with isLastNameValid*
*Error: When Customer.lastName is empty, returns "Last name is required"*

## validate_email_1
*Inputs: Customer with email*
*Operation: validate_email(Customer.email, "^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$", Customer.isEmailValid)*
*Description: Validate that customer email follows a valid format.*
*Output: Customer with isEmailValid*
*Error: When Customer.email format is invalid, returns "Invalid email format"*

## validate_pattern_1
*Inputs: Customer with phone*
*Operation: validate_pattern("^\\+?[1-9]\\d{1,14}$", Customer.phone, 0, Customer.isPhoneValid)*
*Description: Validate that customer phone follows the E.164 international format.*
*Output: Customer with isPhoneValid*
*Error: When Customer.phone format is invalid, returns "Invalid phone format"*

## conditional_logic_1
*Inputs: Customer with isFirstNameValid, isLastNameValid, isEmailValid, isPhoneValid*
*Operation: conditional_logic({"condition": "AND", "rules": [{"field": "Customer.isFirstNameValid", "operator": "equals", "value": true}, {"field": "Customer.isLastNameValid", "operator": "equals", "value": true}, {"field": "Customer.isEmailValid", "operator": "equals", "value": true}, {"field": "Customer.isPhoneValid", "operator": "equals", "value": true}]}, {"isValid": true, "message": "All fields are valid"}, {"isValid": false, "message": "One or more fields are invalid"}, Customer.validationResult)*
*Description: Combine all validation results to determine if all customer information is valid.*
*Output: Customer with validationResult*
*Error: None*

*Internal LO Mapping Stack:*
* validate_required_1.output.Customer.isFirstNameValid maps to conditional_logic_1.input.Customer.isFirstNameValid
* validate_required_2.output.Customer.isLastNameValid maps to conditional_logic_1.input.Customer.isLastNameValid
* validate_email_1.output.Customer.isEmailValid maps to conditional_logic_1.input.Customer.isEmailValid
* validate_pattern_1.output.Customer.isPhoneValid maps to conditional_logic_1.input.Customer.isPhoneValid
* validate_required_1.output.Customer.isFirstNameValid maps to ValidateCustomerInfo.output.Customer.isFirstNameValid
* validate_required_2.output.Customer.isLastNameValid maps to ValidateCustomerInfo.output.Customer.isLastNameValid
* validate_email_1.output.Customer.isEmailValid maps to ValidateCustomerInfo.output.Customer.isEmailValid
* validate_pattern_1.output.Customer.isPhoneValid maps to ValidateCustomerInfo.output.Customer.isPhoneValid
* conditional_logic_1.output.Customer.validationResult maps to ValidateCustomerInfo.output.Customer.validationResult

*Outputs: Customer with isFirstNameValid, isLastNameValid, isEmailValid, isPhoneValid, validationResult*

*Execution pathway:*
* When Customer.validationResult.isValid equals true, route to CreateCustomer.
* Otherwise, route to DisplayValidationErrors.
```

## 11. Complete LO Example with System Functions and Mappings

This example demonstrates proper function chaining and data flow through explicit mappings:

```
## CalculateInvoiceAmounts

name: "Calculate Invoice Amounts"
function_type: "Update"

*[AccountingRole] has update rights*

*Inputs: Invoice with items*, customerId*, currencyCode [="USD"]; Customer with discountTier*

*Nested System Functions:*

## array_functions_1
*Inputs: Invoice with items**
*Operation: array_functions(Invoice.items, "map", "multiply", ["price", "quantity"], "lineTotal", Invoice.itemsWithTotal)*
*Description: Calculate the line total for each item by multiplying price by quantity, creating a new array with line totals.*
*Output: Invoice with itemsWithTotal*
*Error: When Invoice.items is empty, returns "Invoice contains no items"*

## array_functions_2
*Inputs: Invoice with itemsWithTotal*
*Operation: array_functions(Invoice.itemsWithTotal, "sum", "lineTotal", "sum", Invoice.subtotal)*
*Description: Sum all line totals to get the invoice subtotal.*
*Output: Invoice with subtotal*
*Error: When Invoice.itemsWithTotal is empty, returns "Invoice contains no items with totals"*

## calculate_percentage_1
*Inputs: Customer with discountTier*; Invoice with subtotal*
*Operation: calculate_percentage(Invoice.subtotal, 10.0, 2, Invoice.discount)*
*Description: Calculate the discount amount based on the customer's discount tier percentage (hardcoded to 10% for this example) and the invoice subtotal, with 2 decimal places.*
*Output: Invoice with discount*
*Error: None*

## multiply_1
*Inputs: Invoice with subtotal*
*Operation: multiply(Invoice.subtotal, 0.07, Invoice.tax)*
*Description: Calculate tax by multiplying the invoice subtotal by the tax rate of 7%.*
*Output: Invoice with tax*
*Error: None*

## subtract_1
*Inputs: Invoice with subtotal*, discount**
*Operation: subtract(Invoice.subtotal, Invoice.discount, Invoice.subtotalAfterDiscount)*
*Description: Calculate the subtotal after discount by subtracting the discount amount from the original subtotal.*
*Output: Invoice with subtotalAfterDiscount*
*Error: None*

## add_1
*Inputs: Invoice with subtotalAfterDiscount*, tax**
*Operation: add(Invoice.subtotalAfterDiscount, Invoice.tax, Invoice.total)*
*Description: Calculate the final invoice total by adding the tax amount to the subtotal after discount.*
*Output: Invoice with total*
*Error: None*

## format_currency_1
*Inputs: Invoice with total*, currencyCode**
*Operation: format_currency(Invoice.total, Invoice.currencyCode, 2, "en_US", Invoice.formattedTotal)*
*Description: Format the invoice total as a currency string using the specified currency code, with 2 decimal places and en_US locale.*
*Output: Invoice with formattedTotal*
*Error: When Invoice.currencyCode is invalid, uses default "USD"*

*Internal LO Mapping Stack:*
* array_functions_1.output.Invoice.itemsWithTotal maps to array_functions_2.input.Invoice.itemsWithTotal
* array_functions_2.output.Invoice.subtotal maps to calculate_percentage_1.input.Invoice.subtotal
* array_functions_2.output.Invoice.subtotal maps to multiply_1.input.Invoice.subtotal
* array_functions_2.output.Invoice.subtotal maps to subtract_1.input.Invoice.subtotal
* calculate_percentage_1.output.Invoice.discount maps to subtract_1.input.Invoice.discount
* subtract_1.output.Invoice.subtotalAfterDiscount maps to add_1.input.Invoice.subtotalAfterDiscount
* multiply_1.output.Invoice.tax maps to add_1.input.Invoice.tax
* add_1.output.Invoice.total maps to format_currency_1.input.Invoice.total
* array_functions_1.output.Invoice.itemsWithTotal maps to CalculateInvoiceAmounts.output.Invoice.itemsWithTotal
* array_functions_2.output.Invoice.subtotal maps to CalculateInvoiceAmounts.output.Invoice.subtotal
* calculate_percentage_1.output.Invoice.discount maps to CalculateInvoiceAmounts.output.Invoice.discount
* multiply_1.output.Invoice.tax maps to CalculateInvoiceAmounts.output.Invoice.tax
* subtract_1.output.Invoice.subtotalAfterDiscount maps to CalculateInvoiceAmounts.output.Invoice.subtotalAfterDiscount
* add_1.output.Invoice.total maps to CalculateInvoiceAmounts.output.Invoice.total
* format_currency_1.output.Invoice.formattedTotal maps to CalculateInvoiceAmounts.output.Invoice.formattedTotal

*Outputs: Invoice with itemsWithTotal, subtotal, discount, tax, subtotalAfterDiscount, total, formattedTotal*

*LO Mapping Stack:*
* CalculateInvoiceAmounts.output.Invoice.total maps to SendInvoice.input.Invoice.total
* CalculateInvoiceAmounts.output.Invoice.formattedTotal maps to SendInvoice.input.Invoice.formattedTotal
* CalculateInvoiceAmounts.output.Invoice.subtotal maps to SendInvoice.input.Invoice.subtotal
* CalculateInvoiceAmounts.output.Invoice.tax maps to SendInvoice.input.Invoice.tax
* CalculateInvoiceAmounts.output.Invoice.discount maps to SendInvoice.input.Invoice.discount

*Execution pathway:*
* When Invoice.total is greater than 10000, route to ManagerApproval.
* Otherwise, route to SendInvoice.

*Synthetic values:*
* Invoice.items: [{productId: "PROD001", description: "Widget A", price: 10.99, quantity: 2}, {productId: "PROD002", description: "Widget B", price: 24.99, quantity: 1}]
* Invoice.customerId: "CUST0001"
* Invoice.currencyCode: "USD"
* Invoice.subtotal: 46.97
* Invoice.discount: 4.70
* Invoice.tax: 2.96
* Invoice.subtotalAfterDiscount: 42.27
* Invoice.total: 45.23
* Invoice.formattedTotal: "$45.23"
* Customer.discountTier: "Gold"
```

## 12. More System Function Examples by Category

### 12.1 Database Functions

```
## fetch_1

*Inputs: Customer with id*

*Operation: fetch("customers", {"id": Customer.id}, Customer.customerRecord)*

*Description: Retrieve a single customer record from the customers table that matches the specified ID.*

*Output: Customer with customerRecord*

*Error: When Customer.id not found, returns "Customer record not found"*
```

```
## fetch_enum_values_1

*Inputs: Product with category*

*Operation: fetch_enum_values("products", "category", Product.categoryOptions)*

*Description: Retrieve the list of allowed values for the product category field from the database schema.*

*Output: Product with categoryOptions*

*Error: When Product.category is not an enum field, returns "Not an enumerated field"*
```

```
## create_1

*Inputs: Order with customerId*, items*, total*, status [="pending"]*

*Operation: create("orders", "ORDER", {"customerId": Order.customerId, "items": Order.items, "total": Order.total, "status": Order.status, "createdDate": current_timestamp()}, Order.createdOrder)*

*Description: Create a new order record in the orders table with the specified customer ID, items, total, and status (defaulting to "pending" if not specified).*

*Output: Order with createdOrder*

*Error: When required fields are missing, returns "Missing required fields for order creation"*
```

```
## update_1

*Inputs: Order with id*, status*

*Operation: update("orders", {"id": Order.id}, {"status": Order.status, "updatedDate": current_timestamp()}, Order.updateResult)*

*Description: Update the status of an existing order and set the updated date to the current timestamp.*

*Output: Order with updateResult*

*Error: When Order.id not found, returns "Order not found"*
```

### 12.2 Validation Functions

```
## validate_required_1

*Inputs: Address with street*

*Operation: validate_required(Address.street, "Street is required", Address.isStreetValid)*

*Description: Check that the street field is provided in the address, with a specific error message.*

*Output: Address with isStreetValid*

*Error: When Address.street is empty, returns "Street is required"*
```

```
## validate_batch_1

*Inputs: Address with street, city, state, zipCode*

*Operation: validate_batch({"street": Address.street, "city": Address.city, "state": Address.state, "zipCode": Address.zipCode}, {"street": "required", "city": "required", "state": "required|length:2", "zipCode": "required|regex:^\\d{5}(-\\d{4})?$"}, Address.validationResults)*

*Description: Validate all address fields against specific rules: all fields required, state must be 2 characters, and zipCode must match US ZIP format (5 digits or 5+4 format).*

*Output: Address with validationResults*

*Error: Returns validation results object with field-specific error messages*
```

```
## validate_date_range_1

*Inputs: Promotion with startDate*, endDate**

*Operation: validate_date_range(Promotion.startDate, Promotion.endDate, true, Promotion.isValidDateRange)*

*Description: Verify that the promotion end date is after the start date, with inclusive comparison (equal dates allowed).*

*Output: Promotion with isValidDateRange*

*Error: When dates are invalid, returns "End date must be after start date"*
```

### 12.3 Transform Functions

```
## to_uppercase_1

*Inputs: Product with sku*

*Operation: to_uppercase(Product.sku, Product.normalizedSku)*

*Description: Convert the product SKU to uppercase for standardization and consistent lookup.*

*Output: Product with normalizedSku*

*Error: None*
```

```
## format_date_1

*Inputs: Order with createdDate*

*Operation: format_date(Order.createdDate, "%B %d, %Y", Order.formattedDate)*

*Description: Format the order creation date in a human-readable format (Month Day, Year) like "January 15, 2023".*

*Output: Order with formattedDate*

*Error: When Order.createdDate is invalid, returns "Invalid date format"*
```

### 12.4 Math Functions

```
## add_1

*Inputs: Order with subtotal*, shippingCost*, taxAmount*

*Operation: add(Order.subtotal, Order.shippingCost, Order.taxAmount, Order.grandTotal)*

*Description: Calculate the order grand total by adding the subtotal, shipping cost, and tax amount.*

*Output: Order with grandTotal*

*Error: None*
```

```
## subtract_1

*Inputs: Order with subtotal*, discountAmount*

*Operation: subtract(Order.subtotal, Order.discountAmount, Order.netAmount)*

*Description: Calculate the net amount by subtracting the discount amount from the order subtotal.*

*Output: Order with netAmount*

*Error: When result would be negative, returns "Discount cannot exceed subtotal"*
```

### 12.5 Data Functions

```
## array_functions_1

*Inputs: Order with items*

*Operation: array_functions(Order.items, "filter", "equals", "status", "in stock", Order.availableItems)*

*Description: Filter the order items to include only those with "in stock" status, creating a new array with only the available items.*

*Output: Order with availableItems*

*Error: When Order.items is not an array, returns "Items must be an array"*
```

```
## extract_json_field_1

*Inputs: Customer with preferences*

*Operation: extract_json_field(Customer.preferences, "communication.email", Customer.emailPreference)*

*Description: Extract the customer's email communication preference from their JSON preference data, which is stored in a nested structure.*

*Output: Customer with emailPreference*

*Error: When field not found, returns "Preference field not found"*
```

### 12.6 Utility Functions

```
## generate_id_1

*Inputs: Order with none*

*Operation: generate_id("orders", "id", "ORD", Order.id)*

*Description: Generate a unique order ID with the "ORD" prefix, like "ORD00001", incrementing from the last used ID in the orders table.*

*Output: Order with id*

*Error: None*
```

```
## current_timestamp_1

*Inputs: Audit with none*

*Operation: current_timestamp(Audit.timestamp)*

*Description: Get the current system timestamp in ISO format (YYYY-MM-DDTHH:MM:SS.sssZ) for the audit record.*

*Output: Audit with timestamp*

*Error: None*
```

### 12.7 Temporal Functions

```
## date_functions_1

*Inputs: Date with dateValue*, operation*, unit [="days"]*, amount [=0]*, formatStr [="%Y-%m-%d"]*

*Operation: date_functions(Date.dateValue, Date.operation, Date.unit, Date.amount, Date.formatStr, Date.result)*

*Description: Perform various date operations such as format, add, subtract, diff, compare, or extract on the specified date value.*

*Output: Date with result*

*Error: When Date.operation is invalid, returns "Invalid date operation"*
```

```
## count_business_days_1

*Inputs: Project with startDate*, endDate*; Calendar with holidays*

*Operation: count_business_days(Project.startDate, Project.endDate, Calendar.holidays, Project.workingDays)*

*Description: Calculate the number of business days between the project start and end dates, excluding weekends and holidays from the calendar.*

*Output: Project with workingDays*

*Error: When Project.startDate is after Project.endDate, returns "Start date must be before end date"*
```

```
## is_business_day_1

*Inputs: Schedule with checkDate*; Calendar with holidays*

*Operation: is_business_day(Schedule.checkDate, Calendar.holidays, Schedule.isWorkday)*

*Description: Determine if the specified date is a business day (not a weekend or holiday) based on the provided holidays list.*

*Output: Schedule with isWorkday*

*Error: When Schedule.checkDate is invalid, returns "Invalid date format"*
```

### 12.8 Control Flow Functions

```
## conditional_logic_1

*Inputs: Order with status*, total*; Customer with customerType*

*Operation: conditional_logic({"condition": "AND", "rules": [{"field": "Order.status", "operator": "equals", "value": "confirmed"}, {"field": "Order.total", "operator": "greaterThan", "value": 100}, {"field": "Customer.customerType", "operator": "in", "value": ["premium", "wholesale"]}]}, {"discountApplicable": true}, {"discountApplicable": false}, Order.evaluationResult)*

*Description: Evaluate complex logical conditions to determine if a discount is applicable based on order status, total amount, and customer type.*

*Output: Order with evaluationResult*

*Error: When condition evaluation fails, returns "Condition evaluation error"*
```

## 13. Complete End-to-End Example with Multiple LOs

This example shows how system functions are used across multiple LOs in a complete business process:

```
## ProcessOrder

name: "Process Order"
function_type: "Create"

*[SalesRole] has create rights*

*Inputs: Order with items*, customerId*, shippingAddress*, paymentMethod*; Customer with discountTier*; Shipping with rates*; Tax with taxRules*

*LO Mapping Stack:*
* ProcessOrder.output.Order.id maps to CalculateTotals.input.Order.id
* ProcessOrder.output.Order.items maps to CalculateTotals.input.Order.items
* ProcessOrder.output.Order.customerId maps to CalculateTotals.input.Order.customerId
* ProcessOrder.output.Order.shippingAddress maps to CalculateTotals.input.Order.shippingAddress
* ProcessOrder.output.Customer.discountTier maps to CalculateTotals.input.Customer.discountTier
* ProcessOrder.output.Shipping.rates maps to CalculateTotals.input.Shipping.rates
* ProcessOrder.output.Tax.taxRules maps to CalculateTotals.input.Tax.taxRules

*Nested System Functions:*

## validate_required_1
*Inputs: Order with items*
*Operation: validate_required(Order.items, "Items are required", Order.isItemsValid)*
*Description: Validate that the order items are provided.*
*Output: Order with isItemsValid*
*Error: When Order.items is empty, returns "Items are required"*

## validate_required_2
*Inputs: Order with customerId*
*Operation: validate_required(Order.customerId, "Customer ID is required", Order.isCustomerIdValid)*
*Description: Validate that the customer ID is provided.*
*Output: Order with isCustomerIdValid*
*Error: When Order.customerId is empty, returns "Customer ID is required"*

## validate_required_3
*Inputs: Order with shippingAddress*
*Operation: validate_required(Order.shippingAddress, "Shipping address is required", Order.isShippingAddressValid)*
*Description: Validate that the shipping address is provided.*
*Output: Order with isShippingAddressValid*
*Error: When Order.shippingAddress is empty, returns "Shipping address is required"*

## validate_required_4
*Inputs: Order with paymentMethod*
*Operation: validate_required(Order.paymentMethod, "Payment method is required", Order.isPaymentMethodValid)*
*Description: Validate that the payment method is provided.*
*Output: Order with isPaymentMethodValid*
*Error: When Order.paymentMethod is empty, returns "Payment method is required"*

## array_functions_1
*Inputs: Order with items**
*Operation: array_functions(Order.items, "map", "validate_required", ["productId", "quantity"], "isValid", Order.validatedItems)*
*Description: Validate that each item in the order has required productId and quantity fields.*
*Output: Order with validatedItems*
*Error: When any item is missing required fields, returns specific validation errors*

## array_functions_2
*Inputs: Order with validatedItems*
*Operation: array_functions(Order.validatedItems, "every", "equals", "isValid", true, Order.areAllItemsValid)*
*Description: Check if all items are valid by verifying that the isValid flag is true for all items.*
*Output: Order with areAllItemsValid*
*Error: When any item is invalid, returns "One or more items are invalid"*

## entity_exists_1
*Inputs: Order with customerId*
*Operation: entity_exists("customers", "id", Order.customerId, Order.isCustomerValid)*
*Description: Verify that a customer with the specified ID exists in the database before creating an order for them.*
*Output: Order with isCustomerValid*
*Error: When customer not found, returns "Customer does not exist"*

## validate_complex_1
*Inputs: Order with shippingAddress*
*Operation: validate_complex(Order.shippingAddress, {"street": {"rule": "required"}, "city": {"rule": "required"}, "state": {"rule": "required|length:2"}, "zipCode": {"rule": "required|regex:^\\d{5}(-\\d{4})?$"}, "country": {"rule": "required"}}, Order.addressValidation)*
*Description: Validate the shipping address structure against complex rules for each field.*
*Output: Order with addressValidation*
*Error: Returns address-specific validation errors*

## conditional_logic_1
*Inputs: Order with isItemsValid, isCustomerIdValid, isShippingAddressValid, isPaymentMethodValid, areAllItemsValid, isCustomerValid, addressValidation*
*Operation: conditional_logic({"condition": "AND", "rules": [{"field": "Order.isItemsValid", "operator": "equals", "value": true}, {"field": "Order.isCustomerIdValid", "operator": "equals", "value": true}, {"field": "Order.isShippingAddressValid", "operator": "equals", "value": true}, {"field": "Order.isPaymentMethodValid", "operator": "equals", "value": true}, {"field": "Order.areAllItemsValid", "operator": "equals", "value": true}, {"field": "Order.isCustomerValid", "operator": "equals", "value": true}, {"field": "Order.addressValidation.isValid", "operator": "equals", "value": true}]}, {"status": "valid"}, {"status": "invalid"}, Order.validationResult)*
*Description: Combine all validation results to determine if the order is valid and can be processed.*
*Output: Order with validationResult*
*Error: None*

## generate_id_1
*Inputs: Order with none*
*Operation: generate_id("orders", "id", "ORD", Order.id)*
*Description: Generate a unique order ID with the "ORD" prefix for the new order.*
*Output: Order with id*
*Error: None*

## current_timestamp_1
*Inputs: Order with none*
*Operation: current_timestamp(Order.createdDate)*
*Description: Set the order creation date to the current timestamp.*
*Output: Order with createdDate*
*Error: None*

## create_1
*Inputs: Order with id*, customerId*, items*, shippingAddress*, paymentMethod*, createdDate*, validationResult.status*
*Operation: create("orders", "ORDER", {"id": Order.id, "customerId": Order.customerId, "items": Order.items, "shippingAddress": Order.shippingAddress, "paymentMethod": Order.paymentMethod, "createdDate": Order.createdDate, "status": Order.validationResult.status}, Order.createdOrder)*
*Description: Create a new order record in the database with all the validated information.*
*Output: Order with createdOrder*
*Error: When database insert fails, returns "Failed to create order"*

*Internal LO Mapping Stack:*
* validate_required_1.output.Order.isItemsValid maps to conditional_logic_1.input.Order.isItemsValid
* validate_required_2.output.Order.isCustomerIdValid maps to conditional_logic_1.input.Order.isCustomerIdValid
* validate_required_3.output.Order.isShippingAddressValid maps to conditional_logic_1.input.Order.isShippingAddressValid
* validate_required_4.output.Order.isPaymentMethodValid maps to conditional_logic_1.input.Order.isPaymentMethodValid
* array_functions_1.output.Order.validatedItems maps to array_functions_2.input.Order.validatedItems
* array_functions_2.output.Order.areAllItemsValid maps to conditional_logic_1.input.Order.areAllItemsValid
* entity_exists_1.output.Order.isCustomerValid maps to conditional_logic_1.input.Order.isCustomerValid
* validate_complex_1.output.Order.addressValidation maps to conditional_logic_1.input.Order.addressValidation
* conditional_logic_1.output.Order.validationResult maps to create_1.input.Order.validationResult
* generate_id_1.output.Order.id maps to create_1.input.Order.id
* current_timestamp_1.output.Order.createdDate maps to create_1.input.Order.createdDate
* generate_id_1.output.Order.id maps to ProcessOrder.output.Order.id
* create_1.output.Order.createdOrder maps to ProcessOrder.output.Order.createdOrder

*Outputs: Order with id, createdOrder, validationResult; Customer with discountTier; Shipping with rates; Tax with taxRules*

*Execution pathway:*
* When Order.validationResult.status equals "valid", route to CalculateTotals.
* Otherwise, route to OrderError.
```

```
## CalculateTotals

name: "Calculate Order Totals"
function_type: "Update"

*[SalesRole] has update rights*

*Inputs: Order with id*, items*, customerId*, shippingAddress*; Customer with discountTier*; Shipping with rates*; Tax with taxRules*

*LO Mapping Stack:*
* CalculateTotals.output.Order.id maps to ProcessPayment.input.Order.id
* CalculateTotals.output.Order.total maps to ProcessPayment.input.Order.total
* CalculateTotals.output.Order.subtotal maps to ProcessPayment.input.Order.subtotal
* CalculateTotals.output.Order.tax maps to ProcessPayment.input.Order.tax
* CalculateTotals.output.Order.shippingCost maps to ProcessPayment.input.Order.shippingCost
* CalculateTotals.output.Order.discount maps to ProcessPayment.input.Order.discount

*Nested System Functions:*

## array_functions_1
*Inputs: Order with items**
*Operation: array_functions(Order.items, "map", "multiply", ["price", "quantity"], "lineTotal", Order.itemsWithTotal)*
*Description: Calculate the line total for each item by multiplying price by quantity, creating a new array with line totals.*
*Output: Order with itemsWithTotal*
*Error: When Order.items is empty, returns "Order contains no items"*

## array_functions_2
*Inputs: Order with itemsWithTotal*
*Operation: array_functions(Order.itemsWithTotal, "sum", "lineTotal", "sum", Order.subtotal)*
*Description: Sum all line totals to get the order subtotal.*
*Output: Order with subtotal*
*Error: When Order.itemsWithTotal is empty, returns "Order contains no items with totals"*

## calculate_percentage_1
*Inputs: Customer with discountTier*; Order with subtotal*
*Operation: calculate_percentage(Order.subtotal, {"Gold": 10, "Silver": 5, "Bronze": 2}[Customer.discountTier] || 0, 2, Order.discount)*
*Description: Calculate the discount amount based on the customer's discount tier percentage and the order subtotal, with 2 decimal places.*
*Output: Order with discount*
*Error: None*

## extract_json_field_1
*Inputs: Shipping with rates*; Order with shippingAddress*
*Operation: extract_json_field(Shipping.rates, Order.shippingAddress.country + "." + Order.shippingAddress.state, Shipping.applicableRate)*
*Description: Extract the shipping rate based on the delivery address country and state.*
*Output: Shipping with applicableRate*
*Error: When no applicable shipping rate found, returns null*

## conditional_logic_1
*Inputs: Shipping with applicableRate*; Shipping with rates*
*Operation: conditional_logic({"condition": "if", "rules": [{"field": "Shipping.applicableRate", "operator": "exists"}]}, {"rate": Shipping.applicableRate}, {"rate": Shipping.rates.default}, Shipping.finalRate)*
*Description: Use the applicable shipping rate if found, otherwise fall back to the default rate.*
*Output: Shipping with finalRate*
*Error: None*

## multiply_1
*Inputs: Order with subtotal*; Shipping with finalRate.rate*
*Operation: multiply(Order.subtotal, Shipping.finalRate.rate, Order.shippingCost)*
*Description: Calculate the shipping cost by multiplying the order subtotal by the applicable shipping rate.*
*Output: Order with shippingCost*
*Error: None*

## extract_json_field_2
*Inputs: Tax with taxRules*; Order with shippingAddress*
*Operation: extract_json_field(Tax.taxRules, Order.shippingAddress.country + "." + Order.shippingAddress.state, Tax.applicableRate)*
*Description: Extract the tax rate based on the delivery address country and state.*
*Output: Tax with applicableRate*
*Error: When no applicable tax rate found, returns null*

## conditional_logic_2
*Inputs: Tax with applicableRate*; Tax with taxRules*
*Operation: conditional_logic({"condition": "if", "rules": [{"field": "Tax.applicableRate", "operator": "exists"}]}, {"rate": Tax.applicableRate}, {"rate": Tax.taxRules.default}, Tax.finalRate)*
*Description: Use the applicable tax rate if found, otherwise fall back to the default tax rate.*
*Output: Tax with finalRate*
*Error: None*

## subtract_1
*Inputs: Order with subtotal*, discount*
*Operation: subtract(Order.subtotal, Order.discount, Order.subtotalAfterDiscount)*
*Description: Calculate the subtotal after discount by subtracting the discount amount from the original subtotal.*
*Output: Order with subtotalAfterDiscount*
*Error: None*

## multiply_2
*Inputs: Order with subtotalAfterDiscount*; Tax with finalRate.rate*
*Operation: multiply(Order.subtotalAfterDiscount, Tax.finalRate.rate, Order.tax)*
*Description: Calculate the tax amount by multiplying the subtotal after discount by the applicable tax rate.*
*Output: Order with tax*
*Error: None*

## add_1
*Inputs: Order with subtotalAfterDiscount*, tax*, shippingCost*
*Operation: add(Order.subtotalAfterDiscount, Order.tax, Order.shippingCost, Order.total)*
*Description: Calculate the final order total by adding the subtotal after discount, tax amount, and shipping cost.*
*Output: Order with total*
*Error: None*

## format_currency_1
*Inputs: Order with subtotal*
*Operation: format_currency(Order.subtotal, "USD", 2, "en_US", Order.formattedSubtotal)*
*Description: Format the order subtotal as a currency string using USD currency code.*
*Output: Order with formattedSubtotal*
*Error: None*

## format_currency_2
*Inputs: Order with discount*
*Operation: format_currency(Order.discount, "USD", 2, "en_US", Order.formattedDiscount)*
*Description: Format the order discount as a currency string using USD currency code.*
*Output: Order with formattedDiscount*
*Error: None*

## format_currency_3
*Inputs: Order with tax*
*Operation: format_currency(Order.tax, "USD", 2, "en_US", Order.formattedTax)*
*Description: Format the order tax as a currency string using USD currency code.*
*Output: Order with formattedTax*
*Error: None*

## format_currency_4
*Inputs: Order with shippingCost*
*Operation: format_currency(Order.shippingCost, "USD", 2, "en_US", Order.formattedShippingCost)*
*Description: Format the order shipping cost as a currency string using USD currency code.*
*Output: Order with formattedShippingCost*
*Error: None*

## format_currency_5
*Inputs: Order with total*
*Operation: format_currency(Order.total, "USD", 2, "en_US", Order.formattedTotal)*
*Description: Format the order total as a currency string using USD currency code.*
*Output: Order with formattedTotal*
*Error: None*

## update_1
*Inputs: Order with id*, subtotal*, discount*, tax*, shippingCost*, total*
*Operation: update("orders", {"id": Order.id}, {"subtotal": Order.subtotal, "discount": Order.discount, "tax": Order.tax, "shippingCost": Order.shippingCost, "total": Order.total, "updatedDate": current_timestamp()}, Order.updateResult)*
*Description: Update the order record in the database with the calculated totals and set the updated date to the current timestamp.*
*Output: Order with updateResult*
*Error: When Order.id not found, returns "Order not found"*

*Internal LO Mapping Stack:*
* array_functions_1.output.Order.itemsWithTotal maps to array_functions_2.input.Order.itemsWithTotal
* array_functions_2.output.Order.subtotal maps to calculate_percentage_1.input.Order.subtotal
* array_functions_2.output.Order.subtotal maps to multiply_1.input.Order.subtotal
* array_functions_2.output.Order.subtotal maps to format_currency_1.input.Order.subtotal
* array_functions_2.output.Order.subtotal maps to update_1.input.Order.subtotal
* array_functions_2.output.Order.subtotal maps to CalculateTotals.output.Order.subtotal
* calculate_percentage_1.output.Order.discount maps to subtract_1.input.Order.discount
* calculate_percentage_1.output.Order.discount maps to format_currency_2.input.Order.discount
* calculate_percentage_1.output.Order.discount maps to update_1.input.Order.discount
* calculate_percentage_1.output.Order.discount maps to CalculateTotals.output.Order.discount
* extract_json_field_1.output.Shipping.applicableRate maps to conditional_logic_1.input.Shipping.applicableRate
* conditional_logic_1.output.Shipping.finalRate maps to multiply_1.input.Shipping.finalRate
* multiply_1.output.Order.shippingCost maps to add_1.input.Order.shippingCost
* multiply_1.output.Order.shippingCost maps to format_currency_4.input.Order.shippingCost
* multiply_1.output.Order.shippingCost maps to update_1.input.Order.shippingCost
* multiply_1.output.Order.shippingCost maps to CalculateTotals.output.Order.shippingCost
* extract_json_field_2.output.Tax.applicableRate maps to conditional_logic_2.input.Tax.applicableRate
* conditional_logic_2.output.Tax.finalRate maps to multiply_2.input.Tax.finalRate
* subtract_1.output.Order.subtotalAfterDiscount maps to multiply_2.input.Order.subtotalAfterDiscount
* subtract_1.output.Order.subtotalAfterDiscount maps to add_1.input.Order.subtotalAfterDiscount
* subtract_1.output.Order.subtotalAfterDiscount maps to CalculateTotals.output.Order.subtotalAfterDiscount
* multiply_2.output.Order.tax maps to add_1.input.Order.tax
* multiply_2.output.Order.tax maps to format_currency_3.input.Order.tax
* multiply_2.output.Order.tax maps to update_1.input.Order.tax
* multiply_2.output.Order.tax maps to CalculateTotals.output.Order.tax
* add_1.output.Order.total maps to format_currency_5.input.Order.total
* add_1.output.Order.total maps to update_1.input.Order.total
* add_1.output.Order.total maps to CalculateTotals.output.Order.total
* format_currency_1.output.Order.formattedSubtotal maps to CalculateTotals.output.Order.formattedSubtotal
* format_currency_2.output.Order.formattedDiscount maps to CalculateTotals.output.Order.formattedDiscount
* format_currency_3.output.Order.formattedTax maps to CalculateTotals.output.Order.formattedTax
* format_currency_4.output.Order.formattedShippingCost maps to CalculateTotals.output.Order.formattedShippingCost
* format_currency_5.output.Order.formattedTotal maps to CalculateTotals.output.Order.formattedTotal
* update_1.output.Order.updateResult maps to CalculateTotals.output.Order.updateResult

*Outputs: Order with id, itemsWithTotal, subtotal, discount, tax, shippingCost, total, subtotalAfterDiscount, formattedSubtotal, formattedDiscount, formattedTax, formattedShippingCost, formattedTotal, updateResult*

*Execution pathway:*
* When Order.total is greater than 10000, route to ManagerApproval.
* Otherwise, route to ProcessPayment.
```

## 14. Implementation Checklist

When implementing system functions, verify:

- [ ] Function name matches exactly with available platform functions
- [ ] Function has unique sequence number when used multiple times in an LO
- [ ] Function operation syntax follows parsable structure
- [ ] All entity and attribute names match exactly with database schema
- [ ] Attributes are always prefixed with their entity name (Entity.attribute)
- [ ] All inputs are clearly defined with mandatory fields marked (*)
- [ ] Operations describe exactly what the function does with explicit parameters
- [ ] Human-readable descriptions are provided
- [ ] All outputs use only existing entity attributes
- [ ] Error conditions and messages are specified
- [ ] All data flows are explicitly mapped in the Internal LO Mapping Stack
- [ ] LO outputs are mapped to next LO inputs in the LO Mapping Stack
- [ ] No data is produced without being explicitly mapped
- [ ] Function chains are properly connected through mappings
- [ ] No new entities or attributes are introduced
- [ ] No placeholder references (like "RESULT") are used
- [ ] Default values are properly specified for optional parameters
- [ ] Output parameters are explicitly defined in function calls
- [ ] Proper error handling is implemented for all edge cases
- [ ] All function parameters follow the documented parameter order
- [ ] Complex logic is broken down into multiple functions rather than a single complex function
- [ ] Functions with same name have unique sequence numbers
- [ ] Mappings reference the correct function instance with its sequence number

## 15. Visual Data Flow Example

In the CalculateInvoiceAmounts LO example shown earlier, the data flow would work as follows:

1. `Invoice.items` → `array_functions_1` → `Invoice.itemsWithTotal`
2. `Invoice.itemsWithTotal` → `array_functions_2` → `Invoice.subtotal`
3. `Invoice.subtotal` flows to:
   - `calculate_percentage_1` (for discount calculation)
   - `multiply_1` (for tax calculation) 
   - `subtract_1` (for subtotal after discount)
   - LO output (preserving the calculated subtotal)
   - `format_currency_1` (for formatted display)
4. `Customer.discountTier` → `calculate_percentage_1` → `Invoice.discount`
5. `Invoice.discount` flows to:
   - `subtract_1` (for subtotal after discount)
   - LO output (preserving the calculated discount)
   - `format_currency_2` (for formatted display)
6. `Invoice.subtotal` and `Invoice.discount` → `subtract_1` → `Invoice.subtotalAfterDiscount`
7. `Invoice.subtotalAfterDiscount` → `multiply_2` → `Invoice.tax`
8. `Invoice.tax` flows to:
   - `add_1` (for final total)
   - LO output (preserving the calculated tax)
   - `format_currency_3` (for formatted display)
9. `Invoice.subtotalAfterDiscount`, `Invoice.tax`, and `Invoice.shippingCost` → `add_1` → `Invoice.total`
10. `Invoice.total` flows to:
    - `format_currency_5` (for formatted display)
    - LO output (preserving the calculated total)
11. Each monetary value → appropriate `format_currency_X` function → formatted version

Finally, the LO's outputs are mapped to other LOs:
- `Invoice.total` → `SendInvoice` LO
- `Invoice.formattedTotal` → `SendInvoice` LO
- `Invoice.subtotal` → `SendInvoice` LO
- `Invoice.tax` → `SendInvoice` LO
- `Invoice.discount` → `SendInvoice` LO

This demonstrates the unidirectional flow of data from system functions to outputs and how multiple functions can be chained together to create a complete processing pipeline.

## 16. Common Pitfalls and Best Practices

### 16.1 Common Pitfalls to Avoid

1. **Missing Function Sequence Numbers**: Always add a sequence number when using the same function multiple times.
2. **Missing Entity Prefixes**: Always use `Entity.attribute` instead of just `attribute` in operations.
3. **Unmapped Outputs**: Every function output must be explicitly mapped to at least one destination.
4. **Implicit Type Conversions**: Don't assume automatic type conversions; use explicit formatting functions.
5. **Incomplete Error Handling**: Always specify error conditions and messages for all possible failure scenarios.
6. **Using Non-existent Attributes**: Only reference attributes that exist in your database schema.
7. **Inconsistent Naming**: Maintain consistent naming throughout all LOs and functions.
8. **Parameter Order Confusion**: Follow the documented parameter order for each function.
9. **Missing Output Parameters**: Always specify the output parameter in function calls.
10. **Chaining Without Intermediate Storage**: When chaining functions, store intermediate results in entity attributes.
11. **Over-complex Single Functions**: Break down complex logic into multiple simpler functions.
12. **Ambiguous Function References**: Always include sequence numbers in mappings for multiple instances of the same function.

### 16.2 Best Practices

1. **Map Everything Explicitly**: No data flow should be implicit or assumed.
2. **Document Comprehensively**: Include clear descriptions of what each function does.
3. **Use Standard Function Names**: Only use functions from the documented list in section 8.
4. **Number Function Instances**: Add sequence numbers to all functions, even if there's only one instance currently (to future-proof).
5. **Validate Inputs First**: Place validation functions before processing functions.
6. **Standardize Error Messages**: Use consistent error message patterns across functions.
7. **Group Related Functions**: Organize functions by their logical relationship.
8. **Use Meaningful Attribute Names**: Choose descriptive names that indicate purpose.
9. **Maintain Unidirectional Flow**: Data should flow in one direction through the function chain.
10. **Include Synthetic Values**: Provide example values for testing and documentation.
11. **Define Clear Execution Pathways**: Specify routing rules based on function outputs.
12. **Keep Functions Focused**: Each function should do one thing well rather than multiple operations.

## 17. Advanced Function Usage

### 17.1 Conditional Function Execution

In some cases, functions should only execute conditionally. This can be accomplished using the conditional_logic function together with proper mappings:

```
## conditional_logic_1

*Inputs: Order with subtotal*, preferredShipping [="standard"]*

*Operation: conditional_logic({"condition": "if", "rules": [{"field": "Order.preferredShipping", "operator": "equals", "value": "express"}]}, {"multiplier": 0.15}, {"multiplier": 0.05}, Shipping.rateDetails)*

*Description: Determine shipping rate multiplier based on preferred shipping method - 15% for express shipping or 5% for standard shipping.*

*Output: Shipping with rateDetails*

*Error: None*
```

### 17.2 Function Composition Pattern

For complex operations that require multiple steps, use function composition by chaining multiple functions with explicit intermediate values:

```
## ProcessCustomerData

name: "Process Customer Data"
function_type: "Update"

*[CustomerServiceRole] has update rights*

*Inputs: Customer with fullName*, email*, phone*

*Nested System Functions:*

## validate_email_1
*Inputs: Customer with email*
*Operation: validate_email(Customer.email, "^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$", Customer.isEmailValid)*
*Description: Validate that customer email follows a valid format.*
*Output: Customer with isEmailValid*
*Error: When Customer.email format is invalid, returns "Invalid email format"*

## to_lowercase_1
*Inputs: Customer with email*
*Operation: to_lowercase(Customer.email, Customer.normalizedEmail)*
*Description: Convert customer email to lowercase for standardization.*
*Output: Customer with normalizedEmail*
*Error: None*

## string_template_1
*Inputs: Customer with fullName*
*Operation: string_template("{{fullName}} <{{parts[0]}}@{{parts[1]}}>" , {"fullName": Customer.fullName, "parts": Customer.normalizedEmail.split("@")}, Customer.formattedContact)*
*Description: Create a formatted contact string with customer's full name and email in standard format.*
*Output: Customer with formattedContact*
*Error: When Customer.normalizedEmail is invalid, returns "Cannot format invalid email"*

## validate_pattern_1
*Inputs: Customer with phone*
*Operation: validate_pattern("^\\+?[1-9]\\d{1,14}$", Customer.phone, 0, Customer.isPhoneValid)*
*Description: Validate that customer phone follows the E.164 international format.*
*Output: Customer with isPhoneValid*
*Error: When Customer.phone format is invalid, returns "Invalid phone format"*

## conditional_logic_1
*Inputs: Customer with isEmailValid, isPhoneValid*
*Operation: conditional_logic({"condition": "AND", "rules": [{"field": "Customer.isEmailValid", "operator": "equals", "value": true}, {"field": "Customer.isPhoneValid", "operator": "equals", "value": true}]}, {"isValid": true, "message": "Contact information is valid"}, {"isValid": false, "message": "Invalid contact information"}, Customer.validationResult)*
*Description: Check if both email and phone are valid to determine overall contact validation status.*
*Output: Customer with validationResult*
*Error: None*

*Internal LO Mapping Stack:*
* validate_email_1.output.Customer.isEmailValid maps to conditional_logic_1.input.Customer.isEmailValid
* validate_pattern_1.output.Customer.isPhoneValid maps to conditional_logic_1.input.Customer.isPhoneValid
* to_lowercase_1.output.Customer.normalizedEmail maps to string_template_1.input.Customer.normalizedEmail
* validate_email_1.output.Customer.isEmailValid maps to ProcessCustomerData.output.Customer.isEmailValid
* validate_pattern_1.output.Customer.isPhoneValid maps to ProcessCustomerData.output.Customer.isPhoneValid
* to_lowercase_1.output.Customer.normalizedEmail maps to ProcessCustomerData.output.Customer.normalizedEmail
* string_template_1.output.Customer.formattedContact maps to ProcessCustomerData.output.Customer.formattedContact
* conditional_logic_1.output.Customer.validationResult maps to ProcessCustomerData.output.Customer.validationResult

*Outputs: Customer with isEmailValid, isPhoneValid, normalizedEmail, formattedContact, validationResult*
```

### 17.3 Handling Arrays and Collections

When processing arrays or collections, use array_functions with appropriate operations:

```
## ProcessOrderItems

name: "Process Order Items"
function_type: "Update"

*[InventoryRole] has update rights*

*Inputs: Order with items*, discountPercent [=0]*

*Nested System Functions:*

## array_functions_1
*Inputs: Order with items*
*Operation: array_functions(Order.items, "filter", "greaterThan", "quantity", 0, Order.validItems)*
*Description: Filter out items with zero or negative quantity.*
*Output