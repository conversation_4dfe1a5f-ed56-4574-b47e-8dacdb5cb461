#!/usr/bin/env python3
"""
Script to check if the workflow_temp schema matches the workflow_runtime schema.
"""

import os
import sys
import logging
from tabulate import tabulate

# Add parent directory to path to import common modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from common.db_connection import get_db_connection
from db_utils import execute_query

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger('check_schema_sync')

def get_tables(schema_name):
    """
    Get all tables in a schema.
    """
    query = """
        SELECT table_name
        FROM information_schema.tables
        WHERE table_schema = %s
        AND table_type = 'BASE TABLE'
        ORDER BY table_name
    """
    
    success, messages, result = execute_query(query, (schema_name,))
    
    if not success:
        logger.error(f"Failed to get tables for {schema_name}: {messages}")
        return []
    
    return [row[0] for row in result]

def get_columns(schema_name, table_name):
    """
    Get all columns in a table.
    """
    query = """
        SELECT column_name, data_type, column_default, is_nullable
        FROM information_schema.columns
        WHERE table_schema = %s
        AND table_name = %s
        ORDER BY ordinal_position
    """
    
    success, messages, result = execute_query(query, (schema_name, table_name))
    
    if not success:
        logger.error(f"Failed to get columns for {schema_name}.{table_name}: {messages}")
        return []
    
    return result

def main():
    """
    Main function.
    """
    # Create output file
    output_file = "temp/schema_sync_check.txt"
    with open(output_file, "w") as f:
        # Get all tables in workflow_runtime
        runtime_tables = get_tables('workflow_runtime')
        f.write(f"Found {len(runtime_tables)} tables in workflow_runtime\n")
        
        # Get all tables in workflow_temp
        temp_tables = get_tables('workflow_temp')
        f.write(f"Found {len(temp_tables)} tables in workflow_temp\n")
        
        # Check if all runtime tables exist in temp
        missing_tables = [table for table in runtime_tables if table not in temp_tables]
        if missing_tables:
            f.write(f"Missing tables in workflow_temp: {missing_tables}\n")
        else:
            f.write("All tables from workflow_runtime exist in workflow_temp\n")
        
        # Check if all temp tables exist in runtime
        extra_tables = [table for table in temp_tables if table not in runtime_tables]
        if extra_tables:
            f.write(f"Extra tables in workflow_temp: {extra_tables}\n")
        
        # Check if all columns in runtime tables exist in temp tables
        all_columns_match = True
        column_differences = []
        
        for table in runtime_tables:
            if table not in temp_tables:
                continue
            
            runtime_columns = get_columns('workflow_runtime', table)
            temp_columns = get_columns('workflow_temp', table)
            
            runtime_column_names = [col[0] for col in runtime_columns]
            temp_column_names = [col[0] for col in temp_columns]
            
            missing_columns = [col for col in runtime_column_names if col not in temp_column_names]
            extra_columns = [col for col in temp_column_names if col not in runtime_column_names]
            
            if missing_columns or extra_columns:
                all_columns_match = False
                column_differences.append({
                    'table': table,
                    'missing_columns': missing_columns,
                    'extra_columns': extra_columns
                })
        
        if all_columns_match:
            f.write("All columns in workflow_runtime tables exist in workflow_temp tables\n")
        else:
            f.write("Column differences found:\n")
            for diff in column_differences:
                f.write(f"Table: {diff['table']}\n")
                if diff['missing_columns']:
                    f.write(f"  Missing columns: {diff['missing_columns']}\n")
                if diff['extra_columns']:
                    f.write(f"  Extra columns: {diff['extra_columns']}\n")
        
        # Print a summary
        f.write("\nSchema Sync Summary:\n")
        f.write("====================\n")
        f.write(f"Tables in workflow_runtime: {len(runtime_tables)}\n")
        f.write(f"Tables in workflow_temp: {len(temp_tables)}\n")
        
        if missing_tables:
            f.write(f"\nMissing tables in workflow_temp: {missing_tables}\n")
        else:
            f.write("\nAll tables from workflow_runtime exist in workflow_temp\n")
        
        if extra_tables:
            f.write(f"\nExtra tables in workflow_temp: {extra_tables}\n")
        
        if all_columns_match:
            f.write("\nAll columns in workflow_runtime tables exist in workflow_temp tables\n")
        else:
            f.write("\nColumn differences found:\n")
            for diff in column_differences:
                f.write(f"\nTable: {diff['table']}\n")
                if diff['missing_columns']:
                    f.write(f"  Missing columns: {diff['missing_columns']}\n")
                if diff['extra_columns']:
                    f.write(f"  Extra columns: {diff['extra_columns']}\n")
        
        # Check specific tables of interest
        tables_of_interest = [
            'lo_input_stack',
            'lo_input_items',
            'lo_output_stack',
            'lo_output_items'
        ]
        
        f.write("\nDetailed Column Comparison for Tables of Interest:\n")
        f.write("==================================================\n")
        
        for table in tables_of_interest:
            if table in runtime_tables and table in temp_tables:
                runtime_columns = get_columns('workflow_runtime', table)
                temp_columns = get_columns('workflow_temp', table)
                
                f.write(f"\nTable: {table}\n")
                
                # Create a comparison table
                comparison = []
                all_column_names = sorted(set([col[0] for col in runtime_columns] + [col[0] for col in temp_columns]))
                
                for col_name in all_column_names:
                    runtime_col = next((col for col in runtime_columns if col[0] == col_name), None)
                    temp_col = next((col for col in temp_columns if col[0] == col_name), None)
                    
                    runtime_type = runtime_col[1] if runtime_col else "MISSING"
                    temp_type = temp_col[1] if temp_col else "MISSING"
                    
                    match = "✓" if runtime_col and temp_col and runtime_col[1] == temp_col[1] else "✗"
                    
                    comparison.append([col_name, runtime_type, temp_type, match])
                
                # Format the comparison table
                headers = ["Column", "Runtime Type", "Temp Type", "Match"]
                table_str = tabulate(comparison, headers=headers)
                f.write(table_str + "\n")
            else:
                if table not in runtime_tables:
                    f.write(f"\nTable {table} does not exist in workflow_runtime\n")
                if table not in temp_tables:
                    f.write(f"\nTable {table} does not exist in workflow_temp\n")
    
    print(f"Schema sync check completed. Results written to {output_file}")
    return not missing_tables and all_columns_match

if __name__ == '__main__':
    main()
