"""
Check if entity tables were created for our entities.
"""

from db_utils import execute_query

def main():
    # Get our entity IDs
    print("Getting entity IDs")
    success, messages, entities = execute_query(
        "SELECT entity_id, name FROM workflow_temp.entities",
        schema_name="workflow_temp"
    )
    
    if success and entities:
        print("Entities:")
        for entity in entities:
            entity_id = entity[0]
            entity_name = entity[1]
            print(f"  {entity_id} - {entity_name}")
            
            # Check if there's a table for this entity
            table_name = f"z_entity_{entity_name.lower()}"
            print(f"  Checking if table {table_name} exists")
            
            success, messages, result = execute_query(
                f"""
                SELECT EXISTS (
                    SELECT FROM information_schema.tables 
                    WHERE table_schema = 'workflow_temp'
                    AND table_name = '{table_name}'
                )
                """,
                schema_name="workflow_temp"
            )
            
            if success and result and result[0][0]:
                print(f"  Table {table_name} exists")
                
                # Get table columns
                success, messages, columns = execute_query(
                    f"""
                    SELECT column_name, data_type 
                    FROM information_schema.columns 
                    WHERE table_schema = 'workflow_temp'
                    AND table_name = '{table_name}'
                    ORDER BY ordinal_position
                    """,
                    schema_name="workflow_temp"
                )
                
                if success and columns:
                    print(f"  Columns in {table_name}:")
                    for column in columns:
                        print(f"    {column[0]} - {column[1]}")
                else:
                    print(f"  Failed to get columns for {table_name}")
            else:
                print(f"  Table {table_name} does not exist")
                
                # Check if there's a table with entity ID format
                # Handle both formats: entity_name and E1
                if '_' in entity_id:
                    table_name = f"e{entity_id.split('_')[1]}.{entity_name.lower()}"
                else:
                    table_name = f"e{entity_id[1:]}.{entity_name.lower()}"
                print(f"  Checking if table {table_name} exists")
                
                success, messages, result = execute_query(
                    f"""
                    SELECT EXISTS (
                        SELECT FROM information_schema.tables 
                        WHERE table_schema = 'workflow_temp'
                        AND table_name = '{table_name}'
                    )
                    """,
                    schema_name="workflow_temp"
                )
                
                if success and result and result[0][0]:
                    print(f"  Table {table_name} exists")
                else:
                    print(f"  Table {table_name} does not exist")
    else:
        print("No entities found or query failed")
        if messages:
            print("Messages:")
            for message in messages:
                print(f"  {message}")

if __name__ == "__main__":
    main()
