tenant:
  id: "t001"
  name: "Purchase Order Management"
  roles:
    - id: "r001"
      name: "Requester"
      inherits_from: null
      access:
        entities:
          - entity_id: "e001"
            permissions: ["Read", "Create", "Update"]
          - entity_id: "e002"
            permissions: ["Read"]
          - entity_id: "e003"
            permissions: ["Read"]
        objectives:
          - objective_id: "go001.lo001"
            permissions: ["Execute"]
    - id: "r002"
      name: "Department Manager"
      inherits_from: null
      access:
        entities:
          - entity_id: "e001"
            permissions: ["Read", "Update"]
          - entity_id: "e002"
            permissions: ["Read", "Create", "Update"]
          - entity_id: "e003"
            permissions: ["Read"]
        objectives:
          - objective_id: "go001.lo002"
            permissions: ["Execute"]
    - id: "r003"
      name: "Procurement Officer"
      inherits_from: null
      access:
        entities:
          - entity_id: "e001"
            permissions: ["Read"]
          - entity_id: "e002"
            permissions: ["Read", "Update"]
          - entity_id: "e003"
            permissions: ["Read", "Create", "Update"]
        objectives:
          - objective_id: "go001.lo003"
            permissions: ["Execute"]
          - objective_id: "go001.lo004"
            permissions: ["Execute"]
    - id: "r004"
      name: "Finance Officer"
      inherits_from: null
      access:
        entities:
          - entity_id: "e001"
            permissions: ["Read"]
          - entity_id: "e002"
            permissions: ["Read"]
          - entity_id: "e003"
            permissions: ["Read", "Update"]
        objectives:
          - objective_id: "go001.lo005"
            permissions: ["Execute"]
 
workflow_data:
  software_type: "Purchase Order Management"
  industry: "Manufacturing"
  version: "1.0"
  created_by: "system"
  created_on: "{{timestamp}}"
 
permission_types:
  - id: "read"
    description: "Can read entity data"
    capabilities: ["GET"]
  - id: "create"
    description: "Can create new entity records"
    capabilities: ["POST"]
  - id: "update"
    description: "Can update existing records"
    capabilities: ["PUT"]
  - id: "delete"
    description: "Can delete entity records"
    capabilities: ["DELETE"]
  - id: "execute"
    description: "Can execute workflows"
    capabilities: ["EXECUTE"]
 
entities:
  - id: "e001"
    name: "PurchaseRequisition"
    type: "Master"
    version: "1.0"
    status: "Active"
    attributes_metadata:
      attribute_prefix: "at"
      attribute_map:
        "at101": "requisitionId"
        "at102": "requesterName"
        "at103": "department"
        "at104": "requisitionDate"
        "at105": "justification"
        "at106": "status"
        "at107": "totalEstimatedAmount"
        "at108": "priority"
        "at109": "remarks"
      required_attributes:
        - "at101"
        - "at102"
        - "at103"
        - "at104"
        - "at105"
        - "at106"
        - "at107"
        - "at108"
    attributes:
      - id: "at101"
        name: "requisitionId"
        display_name: "Requisition ID"
        datatype: "String"
        required: true
        version: "1.0"
        status: "Deployed"
      - id: "at102"
        name: "requesterName"
        display_name: "Requester Name"
        datatype: "String"
        required: true
        version: "1.0"
        status: "Deployed"
      - id: "at103"
        name: "department"
        display_name: "Department"
        datatype: "String"
        required: true
        version: "1.0"
        status: "Deployed"
      - id: "at104"
        name: "requisitionDate"
        display_name: "Requisition Date"
        datatype: "Date"
        required: true
        version: "1.0"
        status: "Deployed"
      - id: "at105"
        name: "justification"
        display_name: "Justification"
        datatype: "String"
        required: true
        version: "1.0"
        status: "Deployed"
      - id: "at106"
        name: "status"
        display_name: "Status"
        datatype: "Enum"
        required: true
        values: ["Draft", "Submitted", "Approved", "Rejected", "Converted to PO"]
        version: "1.0"
        status: "Deployed"
      - id: "at107"
        name: "totalEstimatedAmount"
        display_name: "Total Estimated Amount"
        datatype: "Number"
        required: true
        version: "1.0"
        status: "Deployed"
      - id: "at108"
        name: "priority"
        display_name: "Priority"
        datatype: "Enum"
        required: true
        values: ["Low", "Medium", "High", "Urgent"]
        version: "1.0"
        status: "Deployed"
      - id: "at109"
        name: "remarks"
        display_name: "Remarks"
        datatype: "String"
        required: false
        version: "1.0"
        status: "Deployed"
  
  - id: "e002"
    name: "PurchaseOrder"
    type: "Master"
    version: "1.0"
    status: "Active"
    attributes_metadata:
      attribute_prefix: "at"
      attribute_map:
        "at201": "purchaseOrderId"
        "at202": "requisitionId"
        "at203": "vendorName"
        "at204": "vendorId"
        "at205": "orderDate"
        "at206": "deliveryDate"
        "at207": "totalAmount"
        "at208": "status"
        "at209": "paymentTerms"
        "at210": "shippingTerms"
        "at211": "approvedBy"
      required_attributes:
        - "at201"
        - "at202"
        - "at203"
        - "at204"
        - "at205"
        - "at206"
        - "at207"
        - "at208"
        - "at209"
        - "at210"
    attributes:
      - id: "at201"
        name: "purchaseOrderId"
        display_name: "Purchase Order ID"
        datatype: "String"
        required: true
        version: "1.0"
        status: "Deployed"
      - id: "at202"
        name: "requisitionId"
        display_name: "Requisition ID"
        datatype: "String"
        required: true
        version: "1.0"
        status: "Deployed"
      - id: "at203"
        name: "vendorName"
        display_name: "Vendor Name"
        datatype: "String"
        required: true
        version: "1.0"
        status: "Deployed"
      - id: "at204"
        name: "vendorId"
        display_name: "Vendor ID"
        datatype: "String"
        required: true
        version: "1.0"
        status: "Deployed"
      - id: "at205"
        name: "orderDate"
        display_name: "Order Date"
        datatype: "Date"
        required: true
        version: "1.0"
        status: "Deployed"
      - id: "at206"
        name: "deliveryDate"
        display_name: "Expected Delivery Date"
        datatype: "Date"
        required: true
        version: "1.0"
        status: "Deployed"
      - id: "at207"
        name: "totalAmount"
        display_name: "Total Amount"
        datatype: "Number"
        required: true
        version: "1.0"
        status: "Deployed"
      - id: "at208"
        name: "status"
        display_name: "Status"
        datatype: "Enum"
        required: true
        values: ["Draft", "Issued", "Partially Received", "Fully Received", "Closed", "Cancelled"]
        version: "1.0"
        status: "Deployed"
      - id: "at209"
        name: "paymentTerms"
        display_name: "Payment Terms"
        datatype: "String"
        required: true
        version: "1.0"
        status: "Deployed"
      - id: "at210"
        name: "shippingTerms"
        display_name: "Shipping Terms"
        datatype: "String"
        required: true
        version: "1.0"
        status: "Deployed"
      - id: "at211"
        name: "approvedBy"
        display_name: "Approved By"
        datatype: "String"
        required: false
        version: "1.0"
        status: "Deployed"
  
  - id: "e003"
    name: "Invoice"
    type: "Master"
    version: "1.0"
    status: "Active"
    attributes_metadata:
      attribute_prefix: "at"
      attribute_map:
        "at301": "invoiceId"
        "at302": "purchaseOrderId"
        "at303": "vendorName"
        "at304": "invoiceDate"
        "at305": "invoiceAmount"
        "at306": "paymentStatus"
        "at307": "paymentDueDate"
        "at308": "paymentMethod"
        "at309": "remarks"
      required_attributes:
        - "at301"
        - "at302"
        - "at303"
        - "at304"
        - "at305"
        - "at306"
        - "at307"
    attributes:
      - id: "at301"
        name: "invoiceId"
        display_name: "Invoice ID"
        datatype: "String"
        required: true
        version: "1.0"
        status: "Deployed"
      - id: "at302"
        name: "purchaseOrderId"
        display_name: "Purchase Order ID"
        datatype: "String"
        required: true
        version: "1.0"
        status: "Deployed"
      - id: "at303"
        name: "vendorName"
        display_name: "Vendor Name"
        datatype: "String"
        required: true
        version: "1.0"
        status: "Deployed"
      - id: "at304"
        name: "invoiceDate"
        display_name: "Invoice Date"
        datatype: "Date"
        required: true
        version: "1.0"
        status: "Deployed"
      - id: "at305"
        name: "invoiceAmount"
        display_name: "Invoice Amount"
        datatype: "Number"
        required: true
        version: "1.0"
        status: "Deployed"
      - id: "at306"
        name: "paymentStatus"
        display_name: "Payment Status"
        datatype: "Enum"
        required: true
        values: ["Pending", "Partially Paid", "Paid", "Disputed"]
        version: "1.0"
        status: "Deployed"
      - id: "at307"
        name: "paymentDueDate"
        display_name: "Payment Due Date"
        datatype: "Date"
        required: true
        version: "1.0"
        status: "Deployed"
      - id: "at308"
        name: "paymentMethod"
        display_name: "Payment Method"
        datatype: "Enum"
        required: false
        values: ["Bank Transfer", "Check", "Credit Card", "Cash"]
        version: "1.0"
        status: "Deployed"
      - id: "at309"
        name: "remarks"
        display_name: "Remarks"
        datatype: "String"
        required: false
        version: "1.0"
        status: "Deployed"
 
global_objectives:
  - id: "go001"
    name: "Purchase Order Management Workflow"
    version: "1.0"
    status: "Active"
    input_stack:
      description: "Global inputs"
      inputs: []
    output_stack:
      description: "Global outputs"
      outputs: []
    data_mapping_stack:
      description: "Data handover between GOs"
      mappings: []
 
local_objectives:
  - id: "lo001"
    contextual_id: "go001.lo001"
    name: "Create Purchase Requisition"
    workflow_source: "origin"
    function_type: "Create"
    agent_stack:
      agents:
        - role: "r001"
          rights: ["Execute"]
    input_stack:
      description: "Capture purchase requisition details"
      inputs:
        - id: "in001"
          slot_id: "e001.at101.in001"
          contextual_id: "go001.lo001.in001"
          source:
            type: "system"
            description: "Auto-generated Requisition ID"
          required: true
          data_type: "string"
          ui_control: "oj-input-text"
          metadata: 
            usage: ""
          validations:
            - rule: "Requisition ID must be unique"
              rule_type: "validate_required"
              validation_method: "validate_required"
              error_message: "Requisition ID is required"
          nested_function:
            id: "nf001"
            function_name: "generate_id"
            function_type: "utility"
            parameters:
              entity: "e001"
              attribute: "at101"
              prefix: "REQ"
            output_to: "at101"
        - id: "in002"
          slot_id: "e001.at102.in002"
          contextual_id: "go001.lo001.in002"
          source:
            type: "user"
            description: "Requester Name"
          required: true
          data_type: "string"
          ui_control: "oj-input-text"
          metadata: 
            usage: ""
          validations:
            - rule: "Requester Name is required"
              rule_type: "validate_required"
              validation_method: "validate_required"
              error_message: "Requester name cannot be empty"
        - id: "in003"
          slot_id: "e001.at103.in003"
          contextual_id: "go001.lo001.in003"
          source:
            type: "user"
            description: "Department"
          required: true
          data_type: "string"
          ui_control: "oj-select-single"
          metadata: 
            usage: ""
          validations:
            - rule: "Department is required"
              rule_type: "validate_required"
              validation_method: "validate_required"
              error_message: "Department cannot be empty"
        - id: "in004"
          slot_id: "e001.at104.in004"
          contextual_id: "go001.lo001.in004"
          source:
            type: "system"
            description: "Requisition Date"
          required: true
          data_type: "date"
          ui_control: "oj-input-date"
          metadata: 
            usage: ""
          validations:
            - rule: "Requisition Date is required"
              rule_type: "validate_required"
              validation_method: "validate_required"
              error_message: "Requisition date cannot be empty"
          nested_function:
            id: "nf002"
            function_name: "current_timestamp"
            function_type: "utility"
            parameters: {}
            output_to: "at104"
        - id: "in005"
          slot_id: "e001.at105.in005"
          contextual_id: "go001.lo001.in005"
          source:
            type: "user"
            description: "Justification"
          required: true
          data_type: "string"
          ui_control: "oj-text-area"
          metadata: 
            usage: ""
          validations:
            - rule: "Justification is required"
              rule_type: "validate_required"
              validation_method: "validate_required"
              error_message: "Justification cannot be empty"
        - id: "in006"
          slot_id: "e001.at107.in006"
          contextual_id: "go001.lo001.in006"
          source:
            type: "user"
            description: "Total Estimated Amount"
          required: true
          data_type: "number"
          ui_control: "oj-input-number"
          metadata: 
            usage: ""
          validations:
            - rule: "Total Estimated Amount is required"
              rule_type: "validate_required"
              validation_method: "validate_required"
              error_message: "Total estimated amount cannot be empty"
        - id: "in007"
          slot_id: "e001.at108.in007"
          contextual_id: "go001.lo001.in007"
          source:
            type: "user"
            description: "Priority"
          required: true
          data_type: "enum"
          ui_control: "oj-select-single"
          metadata: 
            usage: ""
          validations:
            - rule: "Priority is required"
              rule_type: "validate_required"
              validation_method: "validate_required"
              error_message: "Priority cannot be empty"
            - rule: "Priority must be one of the allowed values"
              rule_type: "enum_check"
              validation_method: "enum_check"
              allowed_values: ["Low", "Medium", "High", "Urgent"]
              error_message: "Invalid priority selection"
        - id: "in008"
          slot_id: "e001.at109.in008"
          contextual_id: "go001.lo001.in008"
          source:
            type: "user"
            description: "Remarks"
          required: false
          data_type: "string"
          ui_control: "oj-text-area"
          metadata: 
            usage: ""
          validations: []
        - id: "in009"
          slot_id: "e001.at106.in009"
          contextual_id: "go001.lo001.in009"
          source:
            type: "system"
            description: "Status"
          required: true
          data_type: "enum"
          ui_control: "oj-select-single"
          metadata: 
            usage: ""
          validations:
            - rule: "Status must be one of the allowed values"
              rule_type: "enum_check"
              validation_method: "enum_check"
              allowed_values: ["Draft", "Submitted", "Approved", "Rejected", "Converted to PO"]
              error_message: "Invalid status selection"
          nested_function:
            id: "nf003"
            function_name: "to_uppercase"
            function_type: "transform"
            parameters:
              text: "Submitted"
            output_to: "at106"
    output_stack:
      description: "Purchase requisition created"
      outputs:
        - id: "out001"
          slot_id: "executionstatus.out001"
          contextual_id: "go001.lo001.out001"
          source:
            type: "system"
            description: "Success or failure"
          data_type: "string"
        - id: "out002"
          slot_id: "e001.at101.out002"
          contextual_id: "go001.lo001.out002"
          source:
            type: "system"
            description: "Requisition ID"
          data_type: "string"
        - id: "out003"
          slot_id: "e001.at102.out003"
          contextual_id: "go001.lo001.out003"
          source:
            type: "system"
            description: "Requester Name"
          data_type: "string"
        - id: "out004"
          slot_id: "e001.at103.out004"
          contextual_id: "go001.lo001.out004"
          source:
            type: "system"
            description: "Department"
          data_type: "string"
        - id: "out005"
          slot_id: "e001.at104.out005"
          contextual_id: "go001.lo001.out005"
          source:
            type: "system"
            description: "Requisition Date"
          data_type: "date"
        - id: "out006"
          slot_id: "e001.at105.out006"
          contextual_id: "go001.lo001.out006"
          source:
            type: "system"
            description: "Justification"
          data_type: "string"
        - id: "out007"
          slot_id: "e001.at106.out007"
          contextual_id: "go001.lo001.out007"
          source:
            type: "system"
            description: "Status"
          data_type: "enum"
        - id: "out008"
          slot_id: "e001.at107.out008"
          contextual_id: "go001.lo001.out008"
          source:
            type: "system"
            description: "Total Estimated Amount"
          data_type: "number"
        - id: "out009"
          slot_id: "e001.at108.out009"
          contextual_id: "go001.lo001.out009"
          source:
            type: "system"
            description: "Priority"
          data_type: "enum"
        - id: "out010"
          slot_id: "e001.at109.out010"
          contextual_id: "go001.lo001.out010"
          source:
            type: "system"
            description: "Remarks"
          data_type: "string"
    data_mapping_stack:
      description: "Data handover between LOs"
      mappings:
        - id: "map001"
          source: "lo001.out002"
          target: "lo002.in010"
          mapping_type: "direct"
        - id: "map002"
          source: "lo001.out003"
          target: "lo002.in011"
          mapping_type: "direct"
        - id: "map003"
          source: "lo001.out004"
          target: "lo002.in012"
          mapping_type: "direct"
        - id: "map004"
          source: "lo001.out005"
          target: "lo002.in013"
          mapping_type: "direct"
        - id: "map005"
          source: "lo001.out006"
          target: "lo002.in014"
          mapping_type: "direct"
        - id: "map006"
          source: "lo001.out008"
          target: "lo002.in015"
          mapping_type: "direct"
        - id: "map007"
          source: "lo001.out009"
          target: "lo002.in016"
          mapping_type: "direct"
        - id: "map008"
          source: "lo001.out010"
          target: "lo002.in017"
          mapping_type: "direct"
    execution_pathway:
      type: "sequential"
      next_lo: "lo002"
  
  - id: "lo002"
    contextual_id: "go001.lo002"
    name: "Review and Approve Requisition"
    workflow_source: "intermediate"
    function_type: "Update"
    agent_stack:
      agents:
        - role: "r002"
          rights: ["Execute", "Update"]
    input_stack:
      description: "Department manager reviews and approves requisition"
      inputs:
        - id: "in010"
          slot_id: "e001.at101.in010"
          contextual_id: "go001.lo002.in010"
          source:
            type: "system"
            description: "Requisition ID"
          required: true
          data_type: "string"
          ui_control: "oj-input-text"
          metadata:
            usage: "lookup"
          validations:
            - rule: "Requisition ID must be present"
              rule_type: "validate_required"
              validation_method: "validate_required"
              error_message: "Requisition ID is required"
            - rule: "Requisition ID must exist"
              rule_type: "entity_exists"
              validation_method: "entity_exists"
              entity: "e001"
              attribute: "at101"
              error_message: "Requisition ID does not exist"
        - id: "in011"
          slot_id: "e001.at102.in011"
          contextual_id: "go001.lo002.in011"
          source:
            type: "system"
            description: "Requester Name"
          required: true
          data_type: "string"
          ui_control: "oj-input-text"
          metadata:
            usage: "lookup"
          validations:
            - rule: "Requester Name is required"
              rule_type: "validate_required"
              validation_method: "validate_required"
              error_message: "Requester name cannot be empty"
        - id: "in012"
          slot_id: "e001.at103.in012"
          contextual_id: "go001.lo002.in012"
          source:
            type: "system"
            description: "Department"
          required: true
          data_type: "string"
          ui_control: "oj-input-text"
          metadata:
            usage: "lookup"
          validations:
            - rule: "Department is required"
              rule_type: "validate_required"
              validation_method: "validate_required"
              error_message: "Department cannot be empty"
        - id: "in013"
          slot_id: "e001.at104.in013"
          contextual_id: "go001.lo002.in013"
          source:
            type: "system"
            description: "Requisition Date"
          required: true
          data_type: "date"
          ui_control: "oj-input-date"
          metadata:
            usage: "lookup"
          validations:
            - rule: "Requisition Date is required"
              rule_type: "validate_required"
              validation_method: "validate_required"
              error_message: "Requisition date cannot be empty"
        - id: "in014"
          slot_id: "e001.at105.in014"
          contextual_id: "go001.lo002.in014"
          source:
            type: "system"
            description: "Justification"
          required: true
          data_type: "string"
          ui_control: "oj-text-area"
          metadata:
            usage: "lookup"
          validations:
            - rule: "Justification is required"
              rule_type: "validate_required"
              validation_method: "validate_required"
              error_message: "Justification cannot be empty"
        - id: "in015"
          slot_id: "e001.at107.in015"
          contextual_id: "go001.lo002.in015"
          source:
            type: "system"
            description: "Total Estimated Amount"
          required: true
          data_type: "number"
          ui_control: "oj-input-number"
          metadata:
            usage: "lookup"
          validations:
            - rule: "Total Estimated Amount is required"
              rule_type: "validate_required"
              validation_method: "validate_required"
              error_message: "Total estimated amount cannot be empty"
        - id: "in016"
          slot_id: "e001.at108.in016"
          contextual_id: "go001.lo002.in016"
          source:
            type: "system"
            description: "Priority"
          required: true
          data_type: "enum"
          ui_control: "oj-select-single"
          metadata:
            usage: "lookup"
          validations:
            - rule: "Priority is required"
              rule_type: "validate_required"
              validation_method: "validate_required"
              error_message: "Priority cannot be empty"
        - id: "in017"
          slot_id: "e001.at109.in017"
          contextual_id: "go001.lo002.in017"
          source:
            type: "system"
            description: "Remarks"
          required: false
          data_type: "string"
          ui_control: "oj-text-area"
          metadata:
            usage: "lookup"
          validations: []
        - id: "in018"
          slot_id: "e001.at106.in018"
          contextual_id: "go001.lo002.in018"
          source:
            type: "user"
            description: "Status"
          required: true
          data_type: "enum"
          ui_control: "oj-select-single"
          metadata:
            usage: "update"
          validations:
            - rule: "Status must be one of the allowed values"
              rule_type: "enum_check"
              validation_method: "enum_check"
              allowed_values: ["Approved", "Rejected"]
              error_message: "Invalid status selection"
        - id: "in019"
          slot_id: "e001.at109.in019"
          contextual_id: "go001.lo002.in019"
          source:
            type: "user"
            description: "Approval Remarks"
          required: false
          data_type: "string"
          ui_control: "oj-text-area"
          metadata:
            usage: "update"
          validations: []
    output_stack:
      description: "Requisition approved or rejected"
      outputs:
        - id: "out011"
          slot_id: "executionstatus.out011"
          contextual_id: "go001.lo002.out011"
          source:
            type: "system"
            description: "Success or failure"
          data_type: "string"
        - id: "out012"
          slot_id: "e001.at101.out012"
          contextual_id: "go001.lo002.out012"
          source:
            type: "system"
            description: "Requisition ID"
          data_type: "string"
        - id: "out013"
          slot_id: "e001.at102.out013"
          contextual_id: "go001.lo002.out013"
          source:
            type: "system"
            description: "Requester Name"
          data_type: "string"
        - id: "out014"
          slot_id: "e001.at103.out014"
          contextual_id: "go001.lo002.out014"
          source:
            type: "system"
            description: "Department"
          data_type: "string"
        - id: "out015"
          slot_id: "e001.at104.out015"
          contextual_id: "go001.lo002.out015"
          source:
            type: "system"
            description: "Requisition Date"
          data_type: "date"
        - id: "out016"
          slot_id: "e001.at105.out016"
          contextual_id: "go001.lo002.out016"
          source:
            type: "system"
            description: "Justification"
          data_type: "string"
        - id: "out017"
          slot_id: "e001.at106.out017"
          contextual_id: "go001.lo002.out017"
          source:
            type: "system"
            description: "Status"
          data_type: "enum"
        - id: "out018"
          slot_id: "e001.at107.out018"
          contextual_id: "go001.lo002.out018"
          source:
            type: "system"
            description: "Total Estimated Amount"
          data_type: "number"
        - id: "out019"
          slot_id: "e001.at108.out019"
          contextual_id: "go001.lo002.out019"
          source:
            type: "system"
            description: "Priority"
          data_type: "enum"
        - id: "out020"
          slot_id: "e001.at109.out020"
          contextual_id: "go001.lo002.out020"
          source:
            type: "system"
            description: "Remarks"
          data_type: "string"
    data_mapping_stack:
      description: "Data handover between LOs"
      mappings:
        - id: "map009"
          source: "lo002.out012"
          target: "lo003.in020"
          mapping_type: "direct"
        - id: "map010"
          source: "lo002.out013"
          target: "lo003.in021"
          mapping_type: "direct"
        - id: "map011"
          source: "lo002.out014"
          target: "lo003.in022"
          mapping_type: "direct"
        - id: "map012"
          source: "lo002.out018"
          target: "lo003.in023"
          mapping_type: "direct"
        - id: "map013"
          source: "lo002.out019"
          target: "lo003.in024"
          mapping_type: "direct"
        - id: "map014"
          source: "lo002.out012"
          target: "lo005.in047"
          mapping_type: "direct"
    execution_pathway:
      type: "alternate"
      conditions:
        - condition:
            condition_type: "attribute_comparison"
            entity: "e001"
            attribute: "at106"
            operator: "equals"
            value: "Approved"
          next_lo: "lo003"
        - condition:
            condition_type: "attribute_comparison"
            entity: "e001"
            attribute: "at106"
            operator: "equals"
            value: "Rejected"
          next_lo: "lo006"

  - id: "lo003"
    contextual_id: "go001.lo003"
    name: "Create Purchase Order"
    workflow_source: "intermediate"
    function_type: "Create"
    agent_stack:
      agents:
        - role: "r003"
          rights: ["Execute", "Create"]
    input_stack:
      description: "Procurement officer creates purchase order"
      inputs:
        - id: "in020"
          slot_id: "e001.at101.in020"
          contextual_id: "go001.lo003.in020"
          source:
            type: "system"
            description: "Requisition ID"
          required: true
          data_type: "string"
          ui_control: "oj-input-text"
          metadata:
            usage: "lookup"
          validations:
            - rule: "Requisition ID must be present"
              rule_type: "validate_required"
              validation_method: "validate_required"
              error_message: "Requisition ID is required"
            - rule: "Requisition ID must exist"
              rule_type: "entity_exists"
              validation_method: "entity_exists"
              entity: "e001"
              attribute: "at101"
              error_message: "Requisition ID does not exist"
        - id: "in021"
          slot_id: "e001.at102.in021"
          contextual_id: "go001.lo003.in021"
          source:
            type: "system"
            description: "Requester Name"
          required: true
          data_type: "string"
          ui_control: "oj-input-text"
          metadata:
            usage: "lookup"
          validations:
            - rule: "Requester Name is required"
              rule_type: "validate_required"
              validation_method: "validate_required"
              error_message: "Requester name cannot be empty"
        - id: "in022"
          slot_id: "e001.at103.in022"
          contextual_id: "go001.lo003.in022"
          source:
            type: "system"
            description: "Department"
          required: true
          data_type: "string"
          ui_control: "oj-input-text"
          metadata:
            usage: "lookup"
          validations:
            - rule: "Department is required"
              rule_type: "validate_required"
              validation_method: "validate_required"
              error_message: "Department cannot be empty"
        - id: "in023"
          slot_id: "e001.at107.in023"
          contextual_id: "go001.lo003.in023"
          source:
            type: "system"
            description: "Total Estimated Amount"
          required: true
          data_type: "number"
          ui_control: "oj-input-number"
          metadata:
            usage: "lookup"
          validations:
            - rule: "Total Estimated Amount is required"
              rule_type: "validate_required"
              validation_method: "validate_required"
              error_message: "Total estimated amount cannot be empty"
        - id: "in024"
          slot_id: "e001.at108.in024"
          contextual_id: "go001.lo003.in024"
          source:
            type: "system"
            description: "Priority"
          required: true
          data_type: "enum"
          ui_control: "oj-select-single"
          metadata:
            usage: "lookup"
          validations:
            - rule: "Priority is required"
              rule_type: "validate_required"
              validation_method: "validate_required"
              error_message: "Priority cannot be empty"
        - id: "in025"
          slot_id: "e002.at201.in025"
          contextual_id: "go001.lo003.in025"
          source:
            type: "system"
            description: "Purchase Order ID"
          required: true
          data_type: "string"
          ui_control: "oj-input-text"
          metadata:
            usage: ""
          validations:
            - rule: "Purchase Order ID is required"
              rule_type: "validate_required"
              validation_method: "validate_required"
              error_message: "Purchase Order ID cannot be empty"
          nested_function:
            id: "nf003"
            function_name: "generate_id"
            function_type: "utility"
            parameters:
              entity: "e002"
              attribute: "at201"
              prefix: "PO"
            output_to: "at201"
        - id: "in026"
          slot_id: "e002.at203.in026"
          contextual_id: "go001.lo003.in026"
          source:
            type: "user"
            description: "Vendor Name"
          required: true
          data_type: "string"
          ui_control: "oj-input-text"
          metadata:
            usage: ""
          validations:
            - rule: "Vendor Name is required"
              rule_type: "validate_required"
              validation_method: "validate_required"
              error_message: "Vendor name cannot be empty"
        - id: "in027"
          slot_id: "e002.at204.in027"
          contextual_id: "go001.lo003.in027"
          source:
            type: "user"
            description: "Vendor ID"
          required: true
          data_type: "string"
          ui_control: "oj-input-text"
          metadata:
            usage: ""
          validations:
            - rule: "Vendor ID is required"
              rule_type: "validate_required"
              validation_method: "validate_required"
              error_message: "Vendor ID cannot be empty"
        - id: "in028"
          slot_id: "e002.at205.in028"
          contextual_id: "go001.lo003.in028"
          source:
            type: "system"
            description: "Order Date"
          required: true
          data_type: "date"
          ui_control: "oj-input-date"
          metadata:
            usage: ""
          validations:
            - rule: "Order Date is required"
              rule_type: "validate_required"
              validation_method: "validate_required"
              error_message: "Order date cannot be empty"
          nested_function:
            id: "nf004"
            function_name: "current_timestamp"
            function_type: "utility"
            parameters: {}
            output_to: "at205"
        - id: "in029"
          slot_id: "e002.at206.in029"
          contextual_id: "go001.lo003.in029"
          source:
            type: "user"
            description: "Expected Delivery Date"
          required: true
          data_type: "date"
          ui_control: "oj-input-date"
          metadata:
            usage: ""
          validations:
            - rule: "Expected Delivery Date is required"
              rule_type: "validate_required"
              validation_method: "validate_required"
              error_message: "Expected delivery date cannot be empty"
        - id: "in030"
          slot_id: "e002.at207.in030"
          contextual_id: "go001.lo003.in030"
          source:
            type: "user"
            description: "Total Amount"
          required: true
          data_type: "number"
          ui_control: "oj-input-number"
          metadata:
            usage: ""
          validations:
            - rule: "Total Amount is required"
              rule_type: "validate_required"
              validation_method: "validate_required"
              error_message: "Total amount cannot be empty"
        - id: "in031"
          slot_id: "e002.at208.in031"
          contextual_id: "go001.lo003.in031"
          source:
            type: "system"
            description: "Status"
          required: true
          data_type: "enum"
          ui_control: "oj-select-single"
          metadata:
            usage: ""
          validations:
            - rule: "Status must be one of the allowed values"
              rule_type: "enum_check"
              validation_method: "enum_check"
              allowed_values: ["Draft", "Issued", "Partially Received", "Fully Received", "Closed", "Cancelled"]
              error_message: "Invalid status selection"
          nested_function:
            id: "nf005"
            function_name: "to_uppercase"
            function_type: "transform"
            parameters:
              text: "Issued"
            output_to: "at208"
        - id: "in032"
          slot_id: "e002.at209.in032"
          contextual_id: "go001.lo003.in032"
          source:
            type: "user"
            description: "Payment Terms"
          required: true
          data_type: "string"
          ui_control: "oj-input-text"
          metadata:
            usage: ""
          validations:
            - rule: "Payment Terms is required"
              rule_type: "validate_required"
              validation_method: "validate_required"
              error_message: "Payment terms cannot be empty"
        - id: "in033"
          slot_id: "e002.at210.in033"
          contextual_id: "go001.lo003.in033"
          source:
            type: "user"
            description: "Shipping Terms"
          required: true
          data_type: "string"
          ui_control: "oj-input-text"
          metadata:
            usage: ""
          validations:
            - rule: "Shipping Terms is required"
              rule_type: "validate_required"
              validation_method: "validate_required"
              error_message: "Shipping terms cannot be empty"
    output_stack:
      description: "Purchase order created"
      outputs:
        - id: "out021"
          slot_id: "executionstatus.out021"
          contextual_id: "go001.lo003.out021"
          source:
            type: "system"
            description: "Success or failure"
          data_type: "string"
        - id: "out022"
          slot_id: "e002.at201.out022"
          contextual_id: "go001.lo003.out022"
          source:
            type: "system"
            description: "Purchase Order ID"
          data_type: "string"
        - id: "out023"
          slot_id: "e002.at202.out023"
          contextual_id: "go001.lo003.out023"
          source:
            type: "system"
            description: "Requisition ID"
          data_type: "string"
        - id: "out024"
          slot_id: "e002.at203.out024"
          contextual_id: "go001.lo003.out024"
          source:
            type: "system"
            description: "Vendor Name"
          data_type: "string"
        - id: "out025"
          slot_id: "e002.at204.out025"
          contextual_id: "go001.lo003.out025"
          source:
            type: "system"
            description: "Vendor ID"
          data_type: "string"
        - id: "out026"
          slot_id: "e002.at205.out026"
          contextual_id: "go001.lo003.out026"
          source:
            type: "system"
            description: "Order Date"
          data_type: "date"
        - id: "out027"
          slot_id: "e002.at206.out027"
          contextual_id: "go001.lo003.out027"
          source:
            type: "system"
            description: "Expected Delivery Date"
          data_type: "date"
        - id: "out028"
          slot_id: "e002.at207.out028"
          contextual_id: "go001.lo003.out028"
          source:
            type: "system"
            description: "Total Amount"
          data_type: "number"
        - id: "out029"
          slot_id: "e002.at208.out029"
          contextual_id: "go001.lo003.out029"
          source:
            type: "system"
            description: "Status"
          data_type: "enum"
        - id: "out030"
          slot_id: "e002.at209.out030"
          contextual_id: "go001.lo003.out030"
          source:
            type: "system"
            description: "Payment Terms"
          data_type: "string"
        - id: "out031"
          slot_id: "e002.at210.out031"
          contextual_id: "go001.lo003.out031"
          source:
            type: "system"
            description: "Shipping Terms"
          data_type: "string"
        - id: "out032"
          slot_id: "e001.at101.out032"
          contextual_id: "go001.lo003.out032"
          source:
            type: "system"
            description: "Requisition ID"
          data_type: "string"
        - id: "out033"
          slot_id: "e001.at102.out033"
          contextual_id: "go001.lo003.out033"
          source:
            type: "system"
            description: "Requester Name"
          data_type: "string"
        - id: "out034"
          slot_id: "e001.at103.out034"
          contextual_id: "go001.lo003.out034"
          source:
            type: "system"
            description: "Department"
          data_type: "string"
        - id: "out035"
          slot_id: "e001.at107.out035"
          contextual_id: "go001.lo003.out035"
          source:
            type: "system"
            description: "Total Estimated Amount"
          data_type: "number"
        - id: "out036"
          slot_id: "e001.at108.out036"
          contextual_id: "go001.lo003.out036"
          source:
            type: "system"
            description: "Priority"
          data_type: "enum"
    data_mapping_stack:
      description: "Data handover between LOs"
      mappings:
        - id: "map015"
          source: "lo003.out022"
          target: "lo004.in034"
          mapping_type: "direct"
        - id: "map016"
          source: "lo003.out023"
          target: "lo004.in035"
          mapping_type: "direct"
        - id: "map017"
          source: "lo003.out024"
          target: "lo004.in036"
          mapping_type: "direct"
        - id: "map018"
          source: "lo003.out025"
          target: "lo004.in037"
          mapping_type: "direct"
        - id: "map019"
          source: "lo003.out026"
          target: "lo004.in038"
          mapping_type: "direct"
        - id: "map020"
          source: "lo003.out027"
          target: "lo004.in039"
          mapping_type: "direct"
        - id: "map021"
          source: "lo003.out028"
          target: "lo004.in040"
          mapping_type: "direct"
    execution_pathway:
      type: "sequential"
      next_lo: "lo004"
      
  - id: "lo004"
    contextual_id: "go001.lo004"
    name: "Process Goods Receipt"
    workflow_source: "intermediate"
    function_type: "Update"
    agent_stack:
      agents:
        - role: "r003"
          rights: ["Execute", "Update"]
    input_stack:
      description: "Procurement officer receives goods and updates status"
      inputs:
        - id: "in034"
          slot_id: "e002.at201.in034"
          contextual_id: "go001.lo004.in034"
          source:
            type: "system"
            description: "Purchase Order ID"
          required: true
          data_type: "string"
          ui_control: "oj-input-text"
          metadata:
            usage: "lookup"
          validations:
            - rule: "Purchase Order ID must be present"
              rule_type: "validate_required"
              validation_method: "validate_required"
              error_message: "Purchase Order ID is required"
            - rule: "Purchase Order ID must exist"
              rule_type: "entity_exists"
              validation_method: "entity_exists"
              entity: "e002"
              attribute: "at201"
              error_message: "Purchase Order ID does not exist"
        - id: "in035"
          slot_id: "e002.at202.in035"
          contextual_id: "go001.lo004.in035"
          source:
            type: "system"
            description: "Requisition ID"
          required: true
          data_type: "string"
          ui_control: "oj-input-text"
          metadata:
            usage: "lookup"
          validations:
            - rule: "Requisition ID must be present"
              rule_type: "validate_required"
              validation_method: "validate_required"
              error_message: "Requisition ID is required"
        - id: "in036"
          slot_id: "e002.at203.in036"
          contextual_id: "go001.lo004.in036"
          source:
            type: "system"
            description: "Vendor Name"
          required: true
          data_type: "string"
          ui_control: "oj-input-text"
          metadata:
            usage: "lookup"
          validations:
            - rule: "Vendor Name is required"
              rule_type: "validate_required"
              validation_method: "validate_required"
              error_message: "Vendor name cannot be empty"
        - id: "in037"
          slot_id: "e002.at204.in037"
          contextual_id: "go001.lo004.in037"
          source:
            type: "system"
            description: "Vendor ID"
          required: true
          data_type: "string"
          ui_control: "oj-input-text"
          metadata:
            usage: "lookup"
          validations:
            - rule: "Vendor ID is required"
              rule_type: "validate_required"
              validation_method: "validate_required"
              error_message: "Vendor ID cannot be empty"
        - id: "in038"
          slot_id: "e002.at205.in038"
          contextual_id: "go001.lo004.in038"
          source:
            type: "system"
            description: "Order Date"
          required: true
          data_type: "date"
          ui_control: "oj-input-date"
          metadata:
            usage: "lookup"
          validations:
            - rule: "Order Date is required"
              rule_type: "validate_required"
              validation_method: "validate_required"
              error_message: "Order date cannot be empty"
        - id: "in039"
          slot_id: "e002.at206.in039"
          contextual_id: "go001.lo004.in039"
          source:
            type: "system"
            description: "Expected Delivery Date"
          required: true
          data_type: "date"
          ui_control: "oj-input-date"
          metadata:
            usage: "lookup"
          validations:
            - rule: "Expected Delivery Date is required"
              rule_type: "validate_required"
              validation_method: "validate_required"
              error_message: "Expected delivery date cannot be empty"
        - id: "in040"
          slot_id: "e002.at207.in040"
          contextual_id: "go001.lo004.in040"
          source:
            type: "system"
            description: "Total Amount"
          required: true
          data_type: "number"
          ui_control: "oj-input-number"
          metadata:
            usage: "lookup"
          validations:
            - rule: "Total Amount is required"
              rule_type: "validate_required"
              validation_method: "validate_required"
              error_message: "Total amount cannot be empty"
        - id: "in041"
          slot_id: "e002.at208.in041"
          contextual_id: "go001.lo004.in041"
          source:
            type: "user"
            description: "Status"
          required: true
          data_type: "enum"
          ui_control: "oj-select-single"
          metadata:
            usage: "update"
          validations:
            - rule: "Status must be one of the allowed values"
              rule_type: "enum_check"
              validation_method: "enum_check"
              allowed_values: ["Partially Received", "Fully Received"]
              error_message: "Invalid status selection"
        - id: "in042"
          slot_id: "e003.at301.in042"
          contextual_id: "go001.lo004.in042"
          source:
            type: "system"
            description: "Invoice ID"
          required: true
          data_type: "string"
          ui_control: "oj-input-text"
          metadata:
            usage: ""
          validations:
            - rule: "Invoice ID is required"
              rule_type: "validate_required"
              validation_method: "validate_required"
              error_message: "Invoice ID cannot be empty"
          nested_function:
            id: "nf006"
            function_name: "generate_id"
            function_type: "utility"
            parameters:
              entity: "e003"
              attribute: "at301"
              prefix: "INV"
            output_to: "at301"
        - id: "in043"
          slot_id: "e003.at304.in043"
          contextual_id: "go001.lo004.in043"
          source:
            type: "user"
            description: "Invoice Date"
          required: true
          data_type: "date"
          ui_control: "oj-input-date"
          metadata:
            usage: ""
          validations:
            - rule: "Invoice Date is required"
              rule_type: "validate_required"
              validation_method: "validate_required"
              error_message: "Invoice date cannot be empty"
        - id: "in044"
          slot_id: "e003.at305.in044"
          contextual_id: "go001.lo004.in044"
          source:
            type: "user"
            description: "Invoice Amount"
          required: true
          data_type: "number"
          ui_control: "oj-input-number"
          metadata:
            usage: ""
          validations:
            - rule: "Invoice Amount is required"
              rule_type: "validate_required"
              validation_method: "validate_required"
              error_message: "Invoice amount cannot be empty"
        - id: "in045"
          slot_id: "e003.at306.in045"
          contextual_id: "go001.lo004.in045"
          source:
            type: "system"
            description: "Payment Status"
          required: true
          data_type: "enum"
          ui_control: "oj-select-single"
          metadata:
            usage: ""
          validations:
            - rule: "Payment Status must be one of the allowed values"
              rule_type: "enum_check"
              validation_method: "enum_check"
              allowed_values: ["Pending", "Partially Paid", "Paid", "Disputed"]
              error_message: "Invalid payment status selection"
          nested_function:
            id: "nf007"
            function_name: "to_uppercase"
            function_type: "transform"
            parameters:
              text: "Pending"
            output_to: "at306"
        - id: "in046"
          slot_id: "e003.at307.in046"
          contextual_id: "go001.lo004.in046"
          source:
            type: "user"
            description: "Payment Due Date"
          required: true
          data_type: "date"
          ui_control: "oj-input-date"
          metadata:
            usage: ""
          validations:
            - rule: "Payment Due Date is required"
              rule_type: "validate_required"
              validation_method: "validate_required"
              error_message: "Payment due date cannot be empty"
    output_stack:
      description: "Goods receipt processed and invoice created"
      outputs:
        - id: "out037"
          slot_id: "executionstatus.out037"
          contextual_id: "go001.lo004.out037"
          source:
            type: "system"
            description: "Success or failure"
          data_type: "string"
        - id: "out038"
          slot_id: "e002.at201.out038"
          contextual_id: "go001.lo004.out038"
          source:
            type: "system"
            description: "Purchase Order ID"
          data_type: "string"
        - id: "out039"
          slot_id: "e002.at202.out039"
          contextual_id: "go001.lo004.out039"
          source:
            type: "system"
            description: "Requisition ID"
          data_type: "string"
        - id: "out040"
          slot_id: "e002.at203.out040"
          contextual_id: "go001.lo004.out040"
          source:
            type: "system"
            description: "Vendor Name"
          data_type: "string"
        - id: "out041"
          slot_id: "e002.at204.out041"
          contextual_id: "go001.lo004.out041"
          source:
            type: "system"
            description: "Vendor ID"
          data_type: "string"
        - id: "out042"
          slot_id: "e002.at205.out042"
          contextual_id: "go001.lo004.out042"
          source:
            type: "system"
            description: "Order Date"
          data_type: "date"
        - id: "out043"
          slot_id: "e002.at206.out043"
          contextual_id: "go001.lo004.out043"
          source:
            type: "system"
            description: "Expected Delivery Date"
          data_type: "date"
        - id: "out044"
          slot_id: "e002.at207.out044"
          contextual_id: "go001.lo004.out044"
          source:
            type: "system"
            description: "Total Amount"
          data_type: "number"
        - id: "out045"
          slot_id: "e002.at208.out045"
          contextual_id: "go001.lo004.out045"
          source:
            type: "system"
            description: "PO Status"
          data_type: "enum"
        - id: "out046"
          slot_id: "e003.at301.out046"
          contextual_id: "go001.lo004.out046"
          source:
            type: "system"
            description: "Invoice ID"
          data_type: "string"
        - id: "out047"
          slot_id: "e003.at302.out047"
          contextual_id: "go001.lo004.out047"
          source:
            type: "system"
            description: "Purchase Order ID (in Invoice)"
          data_type: "string"
        - id: "out048"
          slot_id: "e003.at303.out048"
          contextual_id: "go001.lo004.out048"
          source:
            type: "system"
            description: "Vendor Name"
          data_type: "string"
        - id: "out049"
          slot_id: "e003.at304.out049"
          contextual_id: "go001.lo004.out049"
          source:
            type: "system"
            description: "Invoice Date"
          data_type: "date"
        - id: "out050"
          slot_id: "e003.at305.out050"
          contextual_id: "go001.lo004.out050"
          source:
            type: "system"
            description: "Invoice Amount"
          data_type: "number"
        - id: "out051"
          slot_id: "e003.at306.out051"
          contextual_id: "go001.lo004.out051"
          source:
            type: "system"
            description: "Payment Status"
          data_type: "enum"
        - id: "out052"
          slot_id: "e003.at307.out052"
          contextual_id: "go001.lo004.out052"
          source:
            type: "system"
            description: "Payment Due Date"
          data_type: "date"
        - id: "out053"
          slot_id: "e002.at201.out053"
          contextual_id: "go001.lo004.out053"
          source:
            type: "system"
            description: "Purchase Order ID"
          data_type: "string"
        - id: "out054"
          slot_id: "e002.at202.out054"
          contextual_id: "go001.lo004.out054"
          source:
            type: "system"
            description: "Requisition ID"
          data_type: "string"
        - id: "out055"
          slot_id: "e002.at203.out055"
          contextual_id: "go001.lo004.out055"
          source:
            type: "system"
            description: "Vendor Name"
          data_type: "string"
        - id: "out056"
          slot_id: "e002.at204.out056"
          contextual_id: "go001.lo004.out056"
          source:
            type: "system"
            description: "Vendor ID"
          data_type: "string"
        - id: "out057"
          slot_id: "e002.at205.out057"
          contextual_id: "go001.lo004.out057"
          source:
            type: "system"
            description: "Order Date"
          data_type: "date"
        - id: "out058"
          slot_id: "e002.at206.out058"
          contextual_id: "go001.lo004.out058"
          source:
            type: "system"
            description: "Expected Delivery Date"
          data_type: "date"
        - id: "out059"
          slot_id: "e002.at207.out059"
          contextual_id: "go001.lo004.out059"
          source:
            type: "system"
            description: "Total Amount"
          data_type: "number"
    data_mapping_stack:
      description: "Data handover between LOs"
      mappings:
        - id: "map022"
          source: "lo004.out038"
          target: "lo005.in047"
          mapping_type: "direct"
        - id: "map023"
          source: "lo004.out046"
          target: "lo005.in048"
          mapping_type: "direct"
        - id: "map024"
          source: "lo004.out047"
          target: "lo005.in049"
          mapping_type: "direct"
        - id: "map025"
          source: "lo004.out048"
          target: "lo005.in050"
          mapping_type: "direct"
        - id: "map026"
          source: "lo004.out049"
          target: "lo005.in051"
          mapping_type: "direct"
        - id: "map027"
          source: "lo004.out050"
          target: "lo005.in052"
          mapping_type: "direct"
        - id: "map028"
          source: "lo004.out051"
          target: "lo005.in053"
          mapping_type: "direct"
        - id: "map029"
          source: "lo004.out052"
          target: "lo005.in054"
          mapping_type: "direct"
    execution_pathway:
      type: "sequential"
      next_lo: "lo005"
      
  - id: "lo005"
    contextual_id: "go001.lo005"
    name: "Process Payment"
    workflow_source: "terminal"
    function_type: "Update"
    agent_stack:
      agents:
        - role: "r004"
          rights: ["Execute", "Update"]
    input_stack:
      description: "Finance officer processes invoice payment"
      inputs:
        - id: "in047"
          slot_id: "e002.at201.in047"
          contextual_id: "go001.lo005.in047"
          source:
            type: "system"
            description: "Purchase Order ID"
          required: true
          data_type: "string"
          ui_control: "oj-input-text"
          metadata:
            usage: "lookup"
          validations:
            - rule: "Purchase Order ID must be present"
              rule_type: "validate_required"
              validation_method: "validate_required"
              error_message: "Purchase Order ID is required"
            - rule: "Purchase Order ID must exist"
              rule_type: "entity_exists"
              validation_method: "entity_exists"
              entity: "e002"
              attribute: "at201"
              error_message: "Purchase Order ID does not exist"
        - id: "in048"
          slot_id: "e003.at301.in048"
          contextual_id: "go001.lo005.in048"
          source:
            type: "system"
            description: "Invoice ID"
          required: true
          data_type: "string"
          ui_control: "oj-input-text"
          metadata:
            usage: "lookup"
          validations:
            - rule: "Invoice ID must be present"
              rule_type: "validate_required"
              validation_method: "validate_required"
              error_message: "Invoice ID is required"
            - rule: "Invoice ID must exist"
              rule_type: "entity_exists"
              validation_method: "entity_exists"
              entity: "e003"
              attribute: "at301"
              error_message: "Invoice ID does not exist"
        - id: "in049"
          slot_id: "e003.at302.in049"
          contextual_id: "go001.lo005.in049"
          source:
            type: "system"
            description: "Purchase Order ID (in Invoice)"
          required: true
          data_type: "string"
          ui_control: "oj-input-text"
          metadata:
            usage: "lookup"
          validations:
            - rule: "Purchase Order ID is required"
              rule_type: "validate_required"
              validation_method: "validate_required"
              error_message: "Purchase Order ID cannot be empty"
        - id: "in050"
          slot_id: "e003.at303.in050"
          contextual_id: "go001.lo005.in050"
          source:
            type: "system"
            description: "Vendor Name"
          required: true
          data_type: "string"
          ui_control: "oj-input-text"
          metadata:
            usage: "lookup"
          validations:
            - rule: "Vendor Name is required"
              rule_type: "validate_required"
              validation_method: "validate_required"
              error_message: "Vendor name cannot be empty"
        - id: "in051"
          slot_id: "e003.at304.in051"
          contextual_id: "go001.lo005.in051"
          source:
            type: "system"
            description: "Invoice Date"
          required: true
          data_type: "date"
          ui_control: "oj-input-date"
          metadata:
            usage: "lookup"
          validations:
            - rule: "Invoice Date is required"
              rule_type: "validate_required"
              validation_method: "validate_required"
              error_message: "Invoice date cannot be empty"
        - id: "in052"
          slot_id: "e003.at305.in052"
          contextual_id: "go001.lo005.in052"
          source:
            type: "system"
            description: "Invoice Amount"
          required: true
          data_type: "number"
          ui_control: "oj-input-number"
          metadata:
            usage: "lookup"
          validations:
            - rule: "Invoice Amount is required"
              rule_type: "validate_required"
              validation_method: "validate_required"
              error_message: "Invoice amount cannot be empty"
        - id: "in053"
          slot_id: "e003.at306.in053"
          contextual_id: "go001.lo005.in053"
          source:
            type: "system"
            description: "Payment Status"
          required: true
          data_type: "enum"
          ui_control: "oj-select-single"
          metadata:
            usage: "lookup"
          validations:
            - rule: "Payment Status must be one of the allowed values"
              rule_type: "enum_check"
              validation_method: "enum_check"
              allowed_values: ["Pending", "Partially Paid", "Paid", "Disputed"]
              error_message: "Invalid payment status selection"
        - id: "in054"
          slot_id: "e003.at307.in054"
          contextual_id: "go001.lo005.in054"
          source:
            type: "system"
            description: "Payment Due Date"
          required: true
          data_type: "date"
          ui_control: "oj-input-date"
          metadata:
            usage: "lookup"
          validations:
            - rule: "Payment Due Date is required"
              rule_type: "validate_required"
              validation_method: "validate_required"
              error_message: "Payment due date cannot be empty"
        - id: "in055"
          slot_id: "e003.at306.in055"
          contextual_id: "go001.lo005.in055"
          source:
            type: "user"
            description: "Updated Payment Status"
          required: true
          data_type: "enum"
          ui_control: "oj-select-single"
          metadata:
            usage: "update"
          validations:
            - rule: "Payment Status must be one of the allowed values"
              rule_type: "enum_check"
              validation_method: "enum_check"
              allowed_values: ["Paid", "Partially Paid", "Disputed"]
              error_message: "Invalid payment status selection"
        - id: "in056"
          slot_id: "e003.at308.in056"
          contextual_id: "go001.lo005.in056"
          source:
            type: "user"
            description: "Payment Method"
          required: true
          data_type: "enum"
          ui_control: "oj-select-single"
          metadata:
            usage: "update"
          validations:
            - rule: "Payment Method must be one of the allowed values"
              rule_type: "enum_check"
              validation_method: "enum_check"
              allowed_values: ["Bank Transfer", "Check", "Credit Card", "Cash"]
              error_message: "Invalid payment method selection"
        - id: "in057"
          slot_id: "e003.at309.in057"
          contextual_id: "go001.lo005.in057"
          source:
            type: "user"
            description: "Payment Remarks"
          required: false
          data_type: "string"
          ui_control: "oj-text-area"
          metadata:
            usage: "update"
          validations: []
        - id: "in058"
          slot_id: "e002.at208.in058"
          contextual_id: "go001.lo005.in058"
          source:
            type: "user"
            description: "Update PO Status"
          required: true
          data_type: "enum"
          ui_control: "oj-select-single"
          metadata:
            usage: "update"
          validations:
            - rule: "Status must be one of the allowed values"
              rule_type: "enum_check"
              validation_method: "enum_check"
              allowed_values: ["Closed"]
              error_message: "Invalid status selection"
    output_stack:
      description: "Payment processed"
      outputs:
        - id: "out060"
          slot_id: "executionstatus.out060"
          contextual_id: "go001.lo005.out060"
          source:
            type: "system"
            description: "Success or failure"
          data_type: "string"
        - id: "out061"
          slot_id: "e002.at201.out061"
          contextual_id: "go001.lo005.out061"
          source:
            type: "system"
            description: "Purchase Order ID"
          data_type: "string"
        - id: "out062"
          slot_id: "e003.at301.out062"
          contextual_id: "go001.lo005.out062"
          source:
            type: "system"
            description: "Invoice ID"
          data_type: "string"
        - id: "out063"
          slot_id: "e003.at302.out063"
          contextual_id: "go001.lo005.out063"
          source:
            type: "system"
            description: "Purchase Order ID (in Invoice)"
          data_type: "string"
        - id: "out064"
          slot_id: "e003.at303.out064"
          contextual_id: "go001.lo005.out064"
          source:
            type: "system"
            description: "Vendor Name"
          data_type: "string"
        - id: "out065"
          slot_id: "e003.at304.out065"
          contextual_id: "go001.lo005.out065"
          source:
            type: "system"
            description: "Invoice Date"
          data_type: "date"
        - id: "out066"
          slot_id: "e003.at305.out066"
          contextual_id: "go001.lo005.out066"
          source:
            type: "system"
            description: "Invoice Amount"
          data_type: "number"
        - id: "out067"
          slot_id: "e003.at306.out067"
          contextual_id: "go001.lo005.out067"
          source:
            type: "system"
            description: "Payment Status"
          data_type: "enum"
        - id: "out068"
          slot_id: "e003.at307.out068"
          contextual_id: "go001.lo005.out068"
          source:
            type: "system"
            description: "Payment Due Date"
          data_type: "date"
        - id: "out069"
          slot_id: "e003.at308.out069"
          contextual_id: "go001.lo005.out069"
          source:
            type: "system"
            description: "Payment Method"
          data_type: "enum"
        - id: "out070"
          slot_id: "e003.at309.out070"
          contextual_id: "go001.lo005.out070"
          source:
            type: "system"
            description: "Payment Remarks"
          data_type: "string"
        - id: "out071"
          slot_id: "e002.at208.out071"
          contextual_id: "go001.lo005.out071"
          source:
            type: "system"
            description: "PO Status"
          data_type: "enum"
    data_mapping_stack:
      description: "Data handover between LOs"
      mappings: []
    execution_pathway:
      type: "terminal"
      next_lo: ""
      
  - id: "lo006"
    contextual_id: "go001.lo006"
    name: "Handle Rejected Requisition"
    workflow_source: "terminal"
    function_type: "Update"
    agent_stack:
      agents:
        - role: "r001"  # Requester role
          rights: ["Execute"]
    input_stack:
      description: "Requester handles rejected requisition"
      inputs:
        - id: "in070"
          slot_id: "e001.at101.in070"
          contextual_id: "go001.lo006.in070"
          source:
            type: "system"
            description: "Requisition ID"
          required: true
          data_type: "string"
          ui_control: "oj-input-text"
          metadata:
            usage: "lookup"
          validations:
            - rule: "Requisition ID must be present"
              rule_type: "validate_required"
              validation_method: "validate_required"
              error_message: "Requisition ID is required"
            - rule: "Requisition ID must exist"
              rule_type: "entity_exists"
              validation_method: "entity_exists"
              entity: "e001"
              attribute: "at101"
              error_message: "Requisition ID does not exist"
        # ... Add other necessary inputs ...
        - id: "in071"
          slot_id: "e001.at109.in071"
          contextual_id: "go001.lo006.in071"
          source:
            type: "system"
            description: "Rejection Remarks"
          required: false
          data_type: "string"
          ui_control: "oj-text-area"
          metadata:
            usage: "lookup"
          validations: []
    output_stack:
      description: "Rejected requisition processed"
      outputs:
        - id: "out080"
          slot_id: "executionstatus.out080"
          contextual_id: "go001.lo006.out080"
          source:
            type: "system"
            description: "Success or failure"
          data_type: "string"
        - id: "out081"
          slot_id: "e001.at101.out081"
          contextual_id: "go001.lo006.out081"
          source:
            type: "system"
            description: "Requisition ID"
          data_type: "string"
        # ... Add other necessary outputs ...
    data_mapping_stack:
      description: "Data handover between LOs"
      mappings: []
    execution_pathway:
      type: "terminal"
      next_lo: ""
      
      