"""
GO Parser for YAML Builder v2

This module provides functionality for parsing natural language prescriptives
into structured Global Objective (GO) data for database storage.
"""

import os
import sys
import re
import logging
from typing import Dict, List, Tuple, Any, Optional

# Set up logging
logger = logging.getLogger('go_parser')

def parse_go_definitions(prescriptive_text: str) -> Tuple[Dict, List[str]]:
    """
    Parse natural language prescriptives into structured GO data.
    
    Args:
        prescriptive_text: Natural language prescriptive text
        
    Returns:
        Tuple containing:
            - Structured GO data dictionary
            - List of warning messages (empty if no warnings)
    """
    warnings = []
    global_objectives = {}
    
    try:
        # Split the text into GO definitions
        go_blocks = re.split(r'\n\s*\n', prescriptive_text)
        
        for block in go_blocks:
            if not block.strip():
                continue
            
            # Parse GO definition
            go_data, go_warnings = parse_go_block(block)
            warnings.extend(go_warnings)
            
            if go_data:
                go_id = go_data.get('go_id')
                if go_id:
                    global_objectives[go_id] = go_data
                else:
                    warnings.append(f"GO without an ID found in block: {block[:100]}...")
        
        if not global_objectives:
            warnings.append("No global objectives found in the prescriptive text")
            return {"global_objectives": {}}, warnings
        
        return {"global_objectives": global_objectives}, warnings
    except Exception as e:
        logger.error(f"Error parsing global objectives: {str(e)}", exc_info=True)
        warnings.append(f"Error parsing global objectives: {str(e)}")
        return {"global_objectives": {}}, warnings

def parse_go_block(block: str) -> Tuple[Dict, List[str]]:
    """
    Parse a single GO block.
    
    Args:
        block: GO block text
        
    Returns:
        Tuple containing:
            - GO data dictionary
            - List of warning messages (empty if no warnings)
    """
    warnings = []
    go_data = {
        "input_stack": [],
        "output_stack": [],
        "output_triggers": [],
        "process_flow": [],
        "parallel_flows": [],
        "rollback_pathways": [],
        "business_rules": [],
        "data_constraints": []
    }
    
    try:
        # Extract GO ID and name
        first_line_match = re.match(r'Global Objective: ([\w-]+) - (.*)', block)
        if first_line_match:
            go_id = first_line_match.group(1)
            go_name = first_line_match.group(2)
            
            go_data['go_id'] = go_id
            go_data['name'] = go_name
        else:
            warnings.append(f"Could not extract GO ID and name from block: {block[:100]}...")
            return {}, warnings
        
        # Extract description
        description_match = re.search(r'Description:(.*?)(?:\n\s*\n|\n\w|\Z)', block, re.DOTALL)
        if description_match:
            description = description_match.group(1).strip()
            go_data['description'] = description
        
        # Extract version
        version_match = re.search(r'Version: ([\d\.]+)', block)
        if version_match:
            version = version_match.group(1)
            go_data['version'] = version
        else:
            go_data['version'] = '1.0'  # Default version
        
        # Extract status
        status_match = re.search(r'Status: (\w+)', block)
        if status_match:
            status = status_match.group(1)
            go_data['status'] = status
        else:
            go_data['status'] = 'Active'  # Default status
        
        # Extract primary entity
        primary_entity_match = re.search(r'Primary Entity: (\w+)', block)
        if primary_entity_match:
            primary_entity = primary_entity_match.group(1)
            go_data['primary_entity'] = primary_entity
        
        # Extract tenant, book, and chapter
        tenant_match = re.search(r'Tenant: (\w+)', block)
        if tenant_match:
            tenant = tenant_match.group(1)
            go_data['tenant_name'] = tenant
        
        book_match = re.search(r'Book: (\w+)', block)
        if book_match:
            book = book_match.group(1)
            go_data['book_name'] = book
        
        chapter_match = re.search(r'Chapter: (\w+)', block)
        if chapter_match:
            chapter = chapter_match.group(1)
            go_data['chapter_name'] = chapter
        
        # Extract input stack
        input_stack_match = re.search(r'Input Stack:(.*?)(?:\n\s*\n|\nOutput Stack|\Z)', block, re.DOTALL)
        if input_stack_match:
            input_stack_text = input_stack_match.group(1)
            input_stack, input_warnings = parse_input_stack(input_stack_text)
            go_data['input_stack'] = input_stack
            warnings.extend(input_warnings)
        
        # Extract output stack
        output_stack_match = re.search(r'Output Stack:(.*?)(?:\n\s*\n|\nOutput Triggers|\Z)', block, re.DOTALL)
        if output_stack_match:
            output_stack_text = output_stack_match.group(1)
            output_stack, output_warnings = parse_output_stack(output_stack_text)
            go_data['output_stack'] = output_stack
            warnings.extend(output_warnings)
        
        # Extract output triggers
        output_triggers_match = re.search(r'Output Triggers:(.*?)(?:\n\s*\n|\nProcess Flow|\Z)', block, re.DOTALL)
        if output_triggers_match:
            output_triggers_text = output_triggers_match.group(1)
            output_triggers, trigger_warnings = parse_output_triggers(output_triggers_text)
            go_data['output_triggers'] = output_triggers
            warnings.extend(trigger_warnings)
        
        # Extract process flow
        process_flow_match = re.search(r'Process Flow:(.*?)(?:\n\s*\n|\nParallel Flows|\Z)', block, re.DOTALL)
        if process_flow_match:
            process_flow_text = process_flow_match.group(1)
            process_flow, flow_warnings = parse_process_flow(process_flow_text)
            go_data['process_flow'] = process_flow
            warnings.extend(flow_warnings)
        
        # Extract parallel flows
        parallel_flows_match = re.search(r'Parallel Flows:(.*?)(?:\n\s*\n|\nRollback Pathways|\Z)', block, re.DOTALL)
        if parallel_flows_match:
            parallel_flows_text = parallel_flows_match.group(1)
            parallel_flows, parallel_warnings = parse_parallel_flows(parallel_flows_text)
            go_data['parallel_flows'] = parallel_flows
            warnings.extend(parallel_warnings)
        
        # Extract rollback pathways
        rollback_pathways_match = re.search(r'Rollback Pathways:(.*?)(?:\n\s*\n|\nBusiness Rules|\Z)', block, re.DOTALL)
        if rollback_pathways_match:
            rollback_pathways_text = rollback_pathways_match.group(1)
            rollback_pathways, rollback_warnings = parse_rollback_pathways(rollback_pathways_text)
            go_data['rollback_pathways'] = rollback_pathways
            warnings.extend(rollback_warnings)
        
        # Extract business rules
        business_rules_match = re.search(r'Business Rules:(.*?)(?:\n\s*\n|\nData Constraints|\Z)', block, re.DOTALL)
        if business_rules_match:
            business_rules_text = business_rules_match.group(1)
            business_rules, rule_warnings = parse_business_rules(business_rules_text)
            go_data['business_rules'] = business_rules
            warnings.extend(rule_warnings)
        
        # Extract data constraints
        data_constraints_match = re.search(r'Data Constraints:(.*?)(?:\n\s*\n|\Z)', block, re.DOTALL)
        if data_constraints_match:
            data_constraints_text = data_constraints_match.group(1)
            data_constraints, constraint_warnings = parse_data_constraints(data_constraints_text)
            go_data['data_constraints'] = data_constraints
            warnings.extend(constraint_warnings)
        
        # Clean up empty lists
        for key in list(go_data.keys()):
            if isinstance(go_data[key], list) and not go_data[key]:
                del go_data[key]
        
        return go_data, warnings
    except Exception as e:
        logger.error(f"Error parsing GO block: {str(e)}", exc_info=True)
        warnings.append(f"Error parsing GO block: {str(e)}")
        return {}, warnings

def parse_input_stack(input_stack_text: str) -> Tuple[List[Dict], List[str]]:
    """
    Parse input stack from text.
    
    Args:
        input_stack_text: Input stack text
        
    Returns:
        Tuple containing:
            - List of input items
            - List of warning messages (empty if no warnings)
    """
    warnings = []
    input_items = []
    
    try:
        # Split input stack by newline
        input_lines = input_stack_text.strip().split('\n')
        
        for line in input_lines:
            line = line.strip()
            if not line:
                continue
            
            # Parse input line
            input_match = re.match(r'(\d+)\. (\w+) from (.*?)(?:\s+\(required\)|\s*$)', line)
            if input_match:
                slot_id = input_match.group(1)
                attribute = input_match.group(2)
                source = input_match.group(3)
                required = 'required' in line
                
                # Parse entity reference
                entity_ref = None
                attr_ref = None
                
                if '.' in source:
                    entity_ref, attr_ref = source.split('.', 1)
                else:
                    entity_ref = source
                
                input_item = {
                    "slot_id": slot_id,
                    "entity_reference": entity_ref,
                    "required": required
                }
                
                if attr_ref:
                    input_item["attribute_reference"] = attr_ref
                
                input_items.append(input_item)
            else:
                warnings.append(f"Could not parse input line: {line}")
        
        return input_items, warnings
    except Exception as e:
        logger.error(f"Error parsing input stack: {str(e)}", exc_info=True)
        warnings.append(f"Error parsing input stack: {str(e)}")
        return [], warnings

def parse_output_stack(output_stack_text: str) -> Tuple[List[Dict], List[str]]:
    """
    Parse output stack from text.
    
    Args:
        output_stack_text: Output stack text
        
    Returns:
        Tuple containing:
            - List of output items
            - List of warning messages (empty if no warnings)
    """
    warnings = []
    output_items = []
    
    try:
        # Split output stack by newline
        output_lines = output_stack_text.strip().split('\n')
        
        for line in output_lines:
            line = line.strip()
            if not line:
                continue
            
            # Parse output line
            output_match = re.match(r'(\d+)\. (\w+) to (.*?)(?:\s+\((\w+)\)|\s*$)', line)
            if output_match:
                slot_id = output_match.group(1)
                attribute = output_match.group(2)
                target = output_match.group(3)
                data_type = output_match.group(4) if len(output_match.groups()) > 3 else None
                
                # Parse entity reference
                entity_ref = None
                attr_ref = None
                
                if '.' in target:
                    entity_ref, attr_ref = target.split('.', 1)
                else:
                    entity_ref = target
                
                output_item = {
                    "slot_id": slot_id,
                    "output_entity": entity_ref
                }
                
                if attr_ref:
                    output_item["output_attribute"] = attr_ref
                
                if data_type:
                    output_item["data_type"] = data_type
                
                output_items.append(output_item)
            else:
                warnings.append(f"Could not parse output line: {line}")
        
        return output_items, warnings
    except Exception as e:
        logger.error(f"Error parsing output stack: {str(e)}", exc_info=True)
        warnings.append(f"Error parsing output stack: {str(e)}")
        return [], warnings

def parse_output_triggers(output_triggers_text: str) -> Tuple[List[Dict], List[str]]:
    """
    Parse output triggers from text.
    
    Args:
        output_triggers_text: Output triggers text
        
    Returns:
        Tuple containing:
            - List of output triggers
            - List of warning messages (empty if no warnings)
    """
    warnings = []
    output_triggers = []
    
    try:
        # Split output triggers by newline
        trigger_lines = output_triggers_text.strip().split('\n')
        
        for line in trigger_lines:
            line = line.strip()
            if not line:
                continue
            
            # Parse trigger line
            trigger_match = re.match(r'When (\w+) is produced, trigger (\w+) with input (\w+)', line)
            if trigger_match:
                output_item = trigger_match.group(1)
                target_objective = trigger_match.group(2)
                target_input = trigger_match.group(3)
                
                trigger = {
                    "output_item_id": output_item,
                    "target_objective": target_objective,
                    "target_input": target_input,
                    "mapping_type": "direct"
                }
                
                output_triggers.append(trigger)
            else:
                warnings.append(f"Could not parse output trigger line: {line}")
        
        return output_triggers, warnings
    except Exception as e:
        logger.error(f"Error parsing output triggers: {str(e)}", exc_info=True)
        warnings.append(f"Error parsing output triggers: {str(e)}")
        return [], warnings

def parse_process_flow(process_flow_text: str) -> Tuple[List[Dict], List[str]]:
    """
    Parse process flow from text.
    
    Args:
        process_flow_text: Process flow text
        
    Returns:
        Tuple containing:
            - List of process flow steps
            - List of warning messages (empty if no warnings)
    """
    warnings = []
    process_flow = []
    
    try:
        # Split process flow by newline
        flow_lines = process_flow_text.strip().split('\n')
        
        for line in flow_lines:
            line = line.strip()
            if not line:
                continue
            
            # Parse flow line
            flow_match = re.match(r'(\d+)\. (\w+)', line)
            if flow_match:
                sequence = flow_match.group(1)
                lo_name = flow_match.group(2)
                
                flow_step = {
                    "sequence_number": int(sequence),
                    "lo_name": lo_name
                }
                
                process_flow.append(flow_step)
            else:
                warnings.append(f"Could not parse process flow line: {line}")
        
        return process_flow, warnings
    except Exception as e:
        logger.error(f"Error parsing process flow: {str(e)}", exc_info=True)
        warnings.append(f"Error parsing process flow: {str(e)}")
        return [], warnings

def parse_parallel_flows(parallel_flows_text: str) -> Tuple[List[Dict], List[str]]:
    """
    Parse parallel flows from text.
    
    Args:
        parallel_flows_text: Parallel flows text
        
    Returns:
        Tuple containing:
            - List of parallel flows
            - List of warning messages (empty if no warnings)
    """
    warnings = []
    parallel_flows = []
    
    try:
        # Split parallel flows by newline
        flow_lines = parallel_flows_text.strip().split('\n')
        
        for line in flow_lines:
            line = line.strip()
            if not line:
                continue
            
            # Parse flow line
            flow_match = re.match(r'(\w+) runs in parallel, joins at (\w+)', line)
            if flow_match:
                lo_name = flow_match.group(1)
                join_at = flow_match.group(2)
                
                flow = {
                    "lo_name": lo_name,
                    "join_at": join_at
                }
                
                parallel_flows.append(flow)
            else:
                warnings.append(f"Could not parse parallel flow line: {line}")
        
        return parallel_flows, warnings
    except Exception as e:
        logger.error(f"Error parsing parallel flows: {str(e)}", exc_info=True)
        warnings.append(f"Error parsing parallel flows: {str(e)}")
        return [], warnings

def parse_rollback_pathways(rollback_pathways_text: str) -> Tuple[List[Dict], List[str]]:
    """
    Parse rollback pathways from text.
    
    Args:
        rollback_pathways_text: Rollback pathways text
        
    Returns:
        Tuple containing:
            - List of rollback pathways
            - List of warning messages (empty if no warnings)
    """
    warnings = []
    rollback_pathways = []
    
    try:
        # Split rollback pathways by newline
        pathway_lines = rollback_pathways_text.strip().split('\n')
        
        for line in pathway_lines:
            line = line.strip()
            if not line:
                continue
            
            # Parse pathway line
            pathway_match = re.match(r'If (\w+) fails, rollback to (\w+)', line)
            if pathway_match:
                source_lo = pathway_match.group(1)
                target_lo = pathway_match.group(2)
                
                pathway = {
                    "source_lo": source_lo,
                    "target_lo": target_lo
                }
                
                rollback_pathways.append(pathway)
            else:
                warnings.append(f"Could not parse rollback pathway line: {line}")
        
        return rollback_pathways, warnings
    except Exception as e:
        logger.error(f"Error parsing rollback pathways: {str(e)}", exc_info=True)
        warnings.append(f"Error parsing rollback pathways: {str(e)}")
        return [], warnings

def parse_business_rules(business_rules_text: str) -> Tuple[List[Dict], List[str]]:
    """
    Parse business rules from text.
    
    Args:
        business_rules_text: Business rules text
        
    Returns:
        Tuple containing:
            - List of business rules
            - List of warning messages (empty if no warnings)
    """
    warnings = []
    business_rules = []
    
    try:
        # Split business rules by newline
        rule_lines = business_rules_text.strip().split('\n')
        
        for line in rule_lines:
            line = line.strip()
            if not line:
                continue
            
            # Parse rule line
            rule_match = re.match(r'Rule: (.*?) - Enforced by: (\w+)', line)
            if rule_match:
                rule_description = rule_match.group(1)
                enforced_by = rule_match.group(2)
                
                rule = {
                    "description": rule_description,
                    "enforced_by": enforced_by
                }
                
                business_rules.append(rule)
            else:
                warnings.append(f"Could not parse business rule line: {line}")
        
        return business_rules, warnings
    except Exception as e:
        logger.error(f"Error parsing business rules: {str(e)}", exc_info=True)
        warnings.append(f"Error parsing business rules: {str(e)}")
        return [], warnings

def parse_data_constraints(data_constraints_text: str) -> Tuple[List[Dict], List[str]]:
    """
    Parse data constraints from text.
    
    Args:
        data_constraints_text: Data constraints text
        
    Returns:
        Tuple containing:
            - List of data constraints
            - List of warning messages (empty if no warnings)
    """
    warnings = []
    data_constraints = []
    
    try:
        # Split data constraints by newline
        constraint_lines = data_constraints_text.strip().split('\n')
        
        for line in constraint_lines:
            line = line.strip()
            if not line:
                continue
            
            # Parse constraint line
            constraint_match = re.match(r'(\w+)\.(\w+) must (.*)', line)
            if constraint_match:
                entity = constraint_match.group(1)
                attribute = constraint_match.group(2)
                constraint_rule = constraint_match.group(3)
                
                constraint = {
                    "entity": entity,
                    "attribute": attribute,
                    "rule": constraint_rule
                }
                
                data_constraints.append(constraint)
            else:
                warnings.append(f"Could not parse data constraint line: {line}")
        
        return data_constraints, warnings
    except Exception as e:
        logger.error(f"Error parsing data constraints: {str(e)}", exc_info=True)
        warnings.append(f"Error parsing data constraints: {str(e)}")
        return [], warnings
