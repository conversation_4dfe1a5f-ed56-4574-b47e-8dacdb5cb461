"""
Authentication Service for the Workflow System.

This module provides a complete authentication service with:
- Internal OAuth2 provider
- JWT token generation and validation
- User authentication and authorization
- Session management
"""

import os
import time
import uuid
import json
import logging
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, List, Union

import jwt
import bcrypt
from fastapi import Depends, HTTPException, status, Request
from fastapi.security import OAuth2<PERSON><PERSON>wordBearer, OAuth2PasswordRequestForm
from sqlalchemy.orm import Session
from sqlalchemy.sql import text
from pydantic import BaseModel, Field, EmailStr

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("auth_service")

# OAuth2 scheme for token authentication
oauth2_scheme = OAuth2PasswordBearer(tokenUrl="token")

# JWT Configuration
JWT_SECRET_KEY = os.getenv("JWT_SECRET_KEY", "your-secret-key-change-in-production")
JWT_ALGORITHM = "HS256"
ACCESS_TOKEN_EXPIRE_MINUTES = 30
REFRESH_TOKEN_EXPIRE_DAYS = 7

# Models
class Token(BaseModel):
    access_token: str
    refresh_token: str
    token_type: str
    expires_at: int

class TokenData(BaseModel):
    user_id: Optional[str] = None
    username: Optional[str] = None
    roles: List[str] = []
    permissions: List[str] = []
    org_units: List[str] = []
    tenant_id: Optional[str] = None

class User(BaseModel):
    user_id: str
    username: str
    email: EmailStr
    first_name: Optional[str] = None
    last_name: Optional[str] = None
    status: str = "active"
    roles: List[str] = []
    org_units: List[str] = []
    tenant_id: Optional[str] = None
    disabled: bool = False

class UserCreate(BaseModel):
    username: str
    email: EmailStr
    password: str
    first_name: Optional[str] = None
    last_name: Optional[str] = None
    roles: List[str] = ["User"]
    org_unit_id: Optional[str] = None
    tenant_id: Optional[str] = None

class UserInDB(User):
    password_hash: str

class Session(BaseModel):
    session_id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    user_id: str
    token: str
    ip_address: Optional[str] = None
    user_agent: Optional[str] = None
    expires_at: datetime
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: datetime = Field(default_factory=datetime.utcnow)

# Authentication Service
class AuthService:
    """
    Authentication Service for the Workflow System.
    
    Provides user authentication, token generation and validation,
    and session management.
    """
    
    def __init__(self, db_session: Session):
        """
        Initialize the authentication service.
        
        Args:
            db_session: SQLAlchemy database session
        """
        self.db = db_session
        self.logger = logging.getLogger("auth_service")
    
    def verify_password(self, plain_password: str, hashed_password: str) -> bool:
        """
        Verify a password against a hash.
        
        Args:
            plain_password: Plain text password
            hashed_password: Hashed password
            
        Returns:
            bool: True if password matches hash
        """
        return bcrypt.checkpw(
            plain_password.encode('utf-8'), 
            hashed_password.encode('utf-8')
        )
    
    def get_password_hash(self, password: str) -> str:
        """
        Generate a password hash.
        
        Args:
            password: Plain text password
            
        Returns:
            str: Hashed password
        """
        salt = bcrypt.gensalt()
        return bcrypt.hashpw(password.encode('utf-8'), salt).decode('utf-8')
    
    def get_user(self, username: str) -> Optional[UserInDB]:
        """
        Get a user by username.
        
        Args:
            username: Username to look up
            
        Returns:
            Optional[UserInDB]: User if found, None otherwise
        """
        try:
            query = """
            SELECT 
                u.user_id, u.username, u.email, u.password_hash, 
                u.first_name, u.last_name, u.status,
                array_agg(DISTINCT ur.role) as roles,
                array_agg(DISTINCT uo.org_unit_id) as org_units,
                ur.tenant_id
            FROM workflow_runtime.users u
            LEFT JOIN workflow_runtime.user_roles ur ON u.user_id = ur.user_id
            LEFT JOIN workflow_runtime.user_organizations uo ON u.user_id = uo.user_id
            WHERE u.username = :username AND u.status = 'active'
            GROUP BY u.user_id, u.username, u.email, u.password_hash, 
                    u.first_name, u.last_name, u.status, ur.tenant_id
            """
            
            result = self.db.execute(text(query), {"username": username}).fetchone()
            
            if not result:
                return None
                
            user_dict = {
                "user_id": result.user_id,
                "username": result.username,
                "email": result.email,
                "password_hash": result.password_hash,
                "first_name": result.first_name,
                "last_name": result.last_name,
                "status": result.status,
                "roles": result.roles if result.roles and result.roles[0] is not None else [],
                "org_units": result.org_units if result.org_units and result.org_units[0] is not None else [],
                "tenant_id": result.tenant_id,
                "disabled": result.status != "active"
            }
            
            return UserInDB(**user_dict)
            
        except Exception as e:
            self.logger.error(f"Error getting user: {str(e)}")
            return None
    
    def authenticate_user(self, username: str, password: str) -> Optional[User]:
        """
        Authenticate a user with username and password.
        
        Args:
            username: Username
            password: Password
            
        Returns:
            Optional[User]: User if authentication successful, None otherwise
        """
        user = self.get_user(username)
        if not user:
            return None
        if not self.verify_password(password, user.password_hash):
            return None
        if user.disabled:
            return None
            
        # Update last login time
        try:
            query = """
            UPDATE workflow_runtime.users
            SET last_login = CURRENT_TIMESTAMP
            WHERE user_id = :user_id
            """
            self.db.execute(text(query), {"user_id": user.user_id})
            self.db.commit()
        except Exception as e:
            self.logger.error(f"Error updating last login: {str(e)}")
            # Continue even if update fails
            
        return User(
            user_id=user.user_id,
            username=user.username,
            email=user.email,
            first_name=user.first_name,
            last_name=user.last_name,
            status=user.status,
            roles=user.roles,
            org_units=user.org_units,
            tenant_id=user.tenant_id,
            disabled=user.disabled
        )
    
    def create_access_token(self, data: dict, expires_delta: Optional[timedelta] = None) -> str:
        """
        Create a JWT access token.
        
        Args:
            data: Data to encode in the token
            expires_delta: Token expiration time
            
        Returns:
            str: JWT token
        """
        to_encode = data.copy()
        
        if expires_delta:
            expire = datetime.utcnow() + expires_delta
        else:
            expire = datetime.utcnow() + timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
            
        to_encode.update({"exp": expire})
        encoded_jwt = jwt.encode(to_encode, JWT_SECRET_KEY, algorithm=JWT_ALGORITHM)
        
        return encoded_jwt
    
    def create_refresh_token(self, data: dict) -> str:
        """
        Create a JWT refresh token.
        
        Args:
            data: Data to encode in the token
            
        Returns:
            str: JWT refresh token
        """
        to_encode = data.copy()
        expire = datetime.utcnow() + timedelta(days=REFRESH_TOKEN_EXPIRE_DAYS)
        to_encode.update({"exp": expire})
        encoded_jwt = jwt.encode(to_encode, JWT_SECRET_KEY, algorithm=JWT_ALGORITHM)
        
        return encoded_jwt
    
    def decode_token(self, token: str) -> Dict[str, Any]:
        """
        Decode a JWT token.
        
        Args:
            token: JWT token
            
        Returns:
            Dict[str, Any]: Decoded token data
            
        Raises:
            HTTPException: If token is invalid
        """
        try:
            # Check if token has been revoked
            query = """
            SELECT 1 FROM workflow_runtime.user_oauth_tokens
            WHERE access_token = :token OR refresh_token = :token
            """
            
            result = self.db.execute(text(query), {"token": token}).fetchone()
            
            if not result:
                # Token not found in database, it might have been revoked
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="Token has been revoked",
                    headers={"WWW-Authenticate": "Bearer"},
                )
            
            payload = jwt.decode(token, JWT_SECRET_KEY, algorithms=[JWT_ALGORITHM])
            return payload
        except jwt.ExpiredSignatureError:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Token has expired",
                headers={"WWW-Authenticate": "Bearer"},
            )
        except jwt.InvalidTokenError:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid token",
                headers={"WWW-Authenticate": "Bearer"},
            )
    
    def get_user_permissions(self, user_id: str) -> List[str]:
        """
        Get all permissions for a user based on their roles.
        
        Args:
            user_id: User ID
            
        Returns:
            List[str]: List of permission IDs
        """
        try:
            query = """
            SELECT DISTINCT pt.permission_id
            FROM workflow_runtime.user_roles ur
            JOIN workflow_runtime.roles r ON ur.role = r.name
            JOIN workflow_runtime.role_permissions rp ON r.role_id = rp.role_id
            JOIN workflow_runtime.permission_contexts pc ON rp.context_id = pc.context_id
            JOIN workflow_runtime.permission_types pt ON pt.permission_id IN (
                SELECT jsonb_array_elements_text(pc.context_rules->'permissions')
            )
            WHERE ur.user_id = :user_id
            """
            
            result = self.db.execute(text(query), {"user_id": user_id}).fetchall()
            
            return [row[0] for row in result]
            
        except Exception as e:
            self.logger.error(f"Error getting user permissions: {str(e)}")
            return []
    
    def create_token(self, user: User) -> Token:
        """
        Create access and refresh tokens for a user.
        
        Args:
            user: User to create tokens for
            
        Returns:
            Token: Access and refresh tokens
        """
        # Get user permissions
        permissions = self.get_user_permissions(user.user_id)
        
        # Create token data
        token_data = {
            "sub": user.user_id,
            "username": user.username,
            "roles": user.roles,
            "permissions": permissions,
            "org_units": user.org_units,
            "tenant_id": user.tenant_id
        }
        
        # Create tokens
        access_token_expires = timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
        access_token = self.create_access_token(
            data=token_data, 
            expires_delta=access_token_expires
        )
        
        refresh_token = self.create_refresh_token(data={"sub": user.user_id})
        
        # Store tokens in database
        try:
            token_id = str(uuid.uuid4())
            expires_at = datetime.utcnow() + access_token_expires
            
            query = """
            INSERT INTO workflow_runtime.user_oauth_tokens
            (token_id, user_id, access_token, refresh_token, expires_at)
            VALUES (:token_id, :user_id, :access_token, :refresh_token, :expires_at)
            """
            
            self.db.execute(text(query), {
                "token_id": token_id,
                "user_id": user.user_id,
                "access_token": access_token,
                "refresh_token": refresh_token,
                "expires_at": expires_at
            })
            
            self.db.commit()
            
        except Exception as e:
            self.logger.error(f"Error storing tokens: {str(e)}")
            # Continue even if storage fails
        
        return Token(
            access_token=access_token,
            refresh_token=refresh_token,
            token_type="bearer",
            expires_at=int(time.time() + ACCESS_TOKEN_EXPIRE_MINUTES * 60)
        )
    
    def refresh_token(self, refresh_token: str) -> Optional[Token]:
        """
        Refresh an access token using a refresh token.
        
        Args:
            refresh_token: Refresh token
            
        Returns:
            Optional[Token]: New tokens if refresh successful, None otherwise
        """
        try:
            # Decode refresh token
            payload = self.decode_token(refresh_token)
            user_id = payload.get("sub")
            
            if not user_id:
                return None
                
            # Check if refresh token exists in database
            query = """
            SELECT user_id
            FROM workflow_runtime.user_oauth_tokens
            WHERE refresh_token = :refresh_token AND user_id = :user_id
            """
            
            result = self.db.execute(text(query), {
                "refresh_token": refresh_token,
                "user_id": user_id
            }).fetchone()
            
            if not result:
                return None
                
            # Get user
            query = """
            SELECT 
                u.user_id, u.username, u.email,
                u.first_name, u.last_name, u.status,
                array_agg(DISTINCT ur.role) as roles,
                array_agg(DISTINCT uo.org_unit_id) as org_units,
                ur.tenant_id
            FROM workflow_runtime.users u
            LEFT JOIN workflow_runtime.user_roles ur ON u.user_id = ur.user_id
            LEFT JOIN workflow_runtime.user_organizations uo ON u.user_id = uo.user_id
            WHERE u.user_id = :user_id AND u.status = 'active'
            GROUP BY u.user_id, u.username, u.email,
                    u.first_name, u.last_name, u.status, ur.tenant_id
            """
            
            result = self.db.execute(text(query), {"user_id": user_id}).fetchone()
            
            if not result:
                return None
                
            user = User(
                user_id=result.user_id,
                username=result.username,
                email=result.email,
                first_name=result.first_name,
                last_name=result.last_name,
                status=result.status,
                roles=result.roles if result.roles and result.roles[0] is not None else [],
                org_units=result.org_units if result.org_units and result.org_units[0] is not None else [],
                tenant_id=result.tenant_id,
                disabled=result.status != "active"
            )
            
            # Create new tokens
            return self.create_token(user)
            
        except Exception as e:
            self.logger.error(f"Error refreshing token: {str(e)}")
            return None
    
    def revoke_token(self, token: str) -> bool:
        """
        Revoke a token.
        
        Args:
            token: Token to revoke
            
        Returns:
            bool: True if token was revoked, False otherwise
        """
        try:
            query = """
            DELETE FROM workflow_runtime.user_oauth_tokens
            WHERE access_token = :token OR refresh_token = :token
            """
            
            result = self.db.execute(text(query), {"token": token})
            self.db.commit()
            
            return result.rowcount > 0
            
        except Exception as e:
            self.logger.error(f"Error revoking token: {str(e)}")
            return False
    
    def create_user(self, user: UserCreate) -> Optional[User]:
        """
        Create a new user.
        
        Args:
            user: User data
            
        Returns:
            Optional[User]: Created user if successful, None otherwise
        """
        try:
            # Check if username or email already exists
            query = """
            SELECT user_id FROM workflow_runtime.users
            WHERE username = :username OR email = :email
            """
            
            result = self.db.execute(text(query), {
                "username": user.username,
                "email": user.email
            }).fetchone()
            
            if result:
                return None
                
            # Get tenant ID if not provided
            tenant_id = user.tenant_id
            if not tenant_id:
                query = "SELECT tenant_id FROM workflow_runtime.tenants LIMIT 1"
                result = self.db.execute(text(query)).fetchone()
                if result:
                    tenant_id = result[0]
                else:
                    return None
            
            # Create user
            user_id = str(uuid.uuid4())
            password_hash = self.get_password_hash(user.password)
            
            query = """
            INSERT INTO workflow_runtime.users
            (user_id, username, email, password_hash, first_name, last_name, status)
            VALUES (:user_id, :username, :email, :password_hash, :first_name, :last_name, 'active')
            """
            
            self.db.execute(text(query), {
                "user_id": user_id,
                "username": user.username,
                "email": user.email,
                "password_hash": password_hash,
                "first_name": user.first_name,
                "last_name": user.last_name
            })
            
            # Assign roles
            for role in user.roles:
                query = """
                INSERT INTO workflow_runtime.user_roles
                (user_id, username, role, tenant_id)
                VALUES (:user_id, :username, :role, :tenant_id)
                """
                
                self.db.execute(text(query), {
                    "user_id": user_id,
                    "username": user.username,
                    "role": role,
                    "tenant_id": tenant_id
                })
            
            # Assign to organizational unit if provided
            if user.org_unit_id:
                query = """
                INSERT INTO workflow_runtime.user_organizations
                (user_id, org_unit_id, is_primary)
                VALUES (:user_id, :org_unit_id, TRUE)
                """
                
                self.db.execute(text(query), {
                    "user_id": user_id,
                    "org_unit_id": user.org_unit_id
                })
            
            self.db.commit()
            
            # Return created user
            return User(
                user_id=user_id,
                username=user.username,
                email=user.email,
                first_name=user.first_name,
                last_name=user.last_name,
                status="active",
                roles=user.roles,
                org_units=[user.org_unit_id] if user.org_unit_id else [],
                tenant_id=tenant_id,
                disabled=False
            )
            
        except Exception as e:
            self.logger.error(f"Error creating user: {str(e)}")
            self.db.rollback()
            return None
    
    def create_session(self, user_id: str, token: str, request: Request) -> Optional[Session]:
        """
        Create a new session.
        
        Args:
            user_id: User ID
            token: Access token
            request: FastAPI request object
            
        Returns:
            Optional[Session]: Created session if successful, None otherwise
        """
        try:
            session_id = str(uuid.uuid4())
            expires_at = datetime.utcnow() + timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
            
            # Get client info
            ip_address = request.client.host if request.client else None
            user_agent = request.headers.get("user-agent")
            
            query = """
            INSERT INTO workflow_runtime.user_sessions
            (session_id, user_id, token, ip_address, user_agent, expires_at)
            VALUES (:session_id, :user_id, :token, :ip_address, :user_agent, :expires_at)
            """
            
            self.db.execute(text(query), {
                "session_id": session_id,
                "user_id": user_id,
                "token": token,
                "ip_address": ip_address,
                "user_agent": user_agent,
                "expires_at": expires_at
            })
            
            self.db.commit()
            
            return Session(
                session_id=session_id,
                user_id=user_id,
                token=token,
                ip_address=ip_address,
                user_agent=user_agent,
                expires_at=expires_at
            )
            
        except Exception as e:
            self.logger.error(f"Error creating session: {str(e)}")
            return None
    
    def get_session(self, token: str) -> Optional[Session]:
        """
        Get a session by token.
        
        Args:
            token: Access token
            
        Returns:
            Optional[Session]: Session if found, None otherwise
        """
        try:
            query = """
            SELECT session_id, user_id, token, ip_address, user_agent, expires_at, created_at, updated_at
            FROM workflow_runtime.user_sessions
            WHERE token = :token AND expires_at > CURRENT_TIMESTAMP
            """
            
            result = self.db.execute(text(query), {"token": token}).fetchone()
            
            if not result:
                return None
                
            return Session(
                session_id=result.session_id,
                user_id=result.user_id,
                token=result.token,
                ip_address=result.ip_address,
                user_agent=result.user_agent,
                expires_at=result.expires_at,
                created_at=result.created_at,
                updated_at=result.updated_at
            )
            
        except Exception as e:
            self.logger.error(f"Error getting session: {str(e)}")
            return None
    
    def end_session(self, session_id: str) -> bool:
        """
        End a session.
        
        Args:
            session_id: Session ID
            
        Returns:
            bool: True if session was ended, False otherwise
        """
        try:
            query = """
            DELETE FROM workflow_runtime.user_sessions
            WHERE session_id = :session_id
            """
            
            result = self.db.execute(text(query), {"session_id": session_id})
            self.db.commit()
            
            return result.rowcount > 0
            
        except Exception as e:
            self.logger.error(f"Error ending session: {str(e)}")
            return False
    
    def end_all_user_sessions(self, user_id: str) -> bool:
        """
        End all sessions for a user.
        
        Args:
            user_id: User ID
            
        Returns:
            bool: True if sessions were ended, False otherwise
        """
        try:
            query = """
            DELETE FROM workflow_runtime.user_sessions
            WHERE user_id = :user_id
            """
            
            result = self.db.execute(text(query), {"user_id": user_id})
            self.db.commit()
            
            return result.rowcount > 0
            
        except Exception as e:
            self.logger.error(f"Error ending user sessions: {str(e)}")
            return False
    
    def cleanup_expired_sessions(self) -> int:
        """
        Clean up expired sessions.
        
        Returns:
            int: Number of sessions cleaned up
        """
        try:
            query = """
            DELETE FROM workflow_runtime.user_sessions
            WHERE expires_at < CURRENT_TIMESTAMP
            """
            
            result = self.db.execute(text(query))
            self.db.commit()
            
            return result.rowcount
            
        except Exception as e:
            self.logger.error(f"Error cleaning up sessions: {str(e)}")
            return 0
