import unittest
from unittest.mock import patch, MagicMock
import re
from typing import Dict, Any
import sys
import os

# Add the project root to the Python path
project_root = '/home/<USER>/workflow-system/runtime/workflow-engine'
if project_root not in sys.path:
    sys.path.insert(0, project_root)

# Import the function to test
from app.services.system_functions import validate_pattern

class TestValidatePattern(unittest.TestCase):
    def test_valid_pattern_matching_text(self):
        """Test when a valid pattern matches the input text."""
        pattern = r"^\d{3}-\d{2}-\d{4}$"
        text = "123-45-6789"
        
        result = validate_pattern(pattern, text)
        
        self.assertTrue(result["is_valid"])
        self.assertEqual(result["message"], "Pattern matched")
    
    def test_valid_pattern_non_matching_text(self):
        """Test when a valid pattern does not match the input text."""
        pattern = r"^\d{3}-\d{2}-\d{4}$"
        text = "12-345-6789"
        
        result = validate_pattern(pattern, text)
        
        self.assertFalse(result["is_valid"])
        self.assertEqual(result["message"], "Pattern did not match")
    
    def test_case_sensitive_pattern(self):
        """Test case sensitivity in pattern matching."""
        pattern = r"^[A-Z]+$"
        text_upper = "ABC"
        text_lower = "abc"
        
        result_upper = validate_pattern(pattern, text_upper)
        result_lower = validate_pattern(pattern, text_lower)
        
        self.assertTrue(result_upper["is_valid"])
        self.assertFalse(result_lower["is_valid"])
    
    def test_with_ignore_case_flag(self):
        """Test pattern matching with IGNORECASE flag."""
        pattern = r"^[A-Z]+$"
        text_lower = "abc"
        
        result = validate_pattern(pattern, text_lower, re.IGNORECASE)
        
        self.assertTrue(result["is_valid"])
        self.assertEqual(result["message"], "Pattern matched")
    
    def test_invalid_pattern(self):
        """Test with an invalid regex pattern."""
        # Unclosed parenthesis makes this an invalid regex
        pattern = r"^(\d+$"
        text = "123"
        
        result = validate_pattern(pattern, text)
        
        self.assertFalse(result["is_valid"])
        self.assertTrue(result["message"].startswith("Invalid regex pattern:"))
    
    def test_empty_pattern(self):
        """Test with an empty pattern."""
        pattern = ""
        text = "any text"
        
        result = validate_pattern(pattern, text)
        
        self.assertTrue(result["is_valid"])  # Empty pattern matches the start of any string
        self.assertEqual(result["message"], "Pattern matched")
    
    def test_empty_text(self):
        """Test with empty text."""
        pattern = r"^\d+$"
        text = ""
        
        result = validate_pattern(pattern, text)
        
        self.assertFalse(result["is_valid"])
        self.assertEqual(result["message"], "Pattern did not match")
    
    def test_multiline_text(self):
        """Test with multiline text."""
        # Pattern to match at the beginning of the string
        pattern = r"^line1"
        text = "line1\ntest\nline3"
        
        result = validate_pattern(pattern, text)
        
        # This should match because the pattern is at the beginning of the text
        self.assertTrue(result["is_valid"])
        self.assertEqual(result["message"], "Pattern matched")
        
        # Pattern that doesn't match at the beginning
        pattern2 = r"^test"
        result2 = validate_pattern(pattern2, text)
        
        # This should not match without MULTILINE flag
        self.assertFalse(result2["is_valid"])
        self.assertEqual(result2["message"], "Pattern did not match")
    
    @patch('app.services.system_functions.re')
    def test_with_multiline_flag(self, mock_re):
        """Test with explicit MULTILINE flag."""
        # Mock re.match to return a truthy value
        mock_match = MagicMock()
        mock_re.match.return_value = mock_match
        mock_re.error = re.error  # Pass through the real re.error
        
        # For this test, we'll check if a line starts with 'test'
        pattern = r"^test"
        text = "line1\ntest\nline3"
        flags = re.MULTILINE
        
        # Call the function
        result = validate_pattern(pattern, text, flags)
        
        # Verify re.match was called with correct flags
        mock_re.match.assert_called_once_with(pattern, text, flags)
        
        # Since we mocked re.match to return a truthy value,
        # the result should indicate a match
        self.assertTrue(result["is_valid"])
        self.assertEqual(result["message"], "Pattern matched")
    
    @patch('app.services.system_functions.re.match')
    def test_re_match_called_correctly(self, mock_match):
        """Test that re.match is called with correct parameters."""
        pattern = "test_pattern"
        text = "test_text"
        flags = re.IGNORECASE | re.MULTILINE
        
        mock_match.return_value = True
        
        validate_pattern(pattern, text, flags)
        
        mock_match.assert_called_once_with(pattern, text, flags)

if __name__ == '__main__':
    unittest.main()