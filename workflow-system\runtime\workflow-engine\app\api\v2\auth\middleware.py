"""
V2 Authentication Middleware

This module provides standardized authentication middleware for V2 API endpoints.
All V2 microservices should use this middleware for consistent authentication and RBAC.
"""

import logging
import jwt
from typing import Optional, List
from datetime import datetime

from fastapi import Request, HTTPException, status, Depends
from sqlalchemy.orm import Session
from sqlalchemy.sql import text

from app.db.session import get_db

# Configure logging
logger = logging.getLogger(__name__)

# JWT Configuration - should match V2 auth service
JWT_SECRET_KEY = "your-secret-key-change-in-production"  # Should be from environment
JWT_ALGORITHM = "HS256"


class V2SecurityContext:
    """
    V2 Security context for the current request.
    
    Contains user information, roles, permissions, and other security-related data
    compatible with V2 API structure.
    """
    
    def __init__(self):
        self.user_id: Optional[str] = None
        self.username: Optional[str] = None
        self.roles: List[str] = []
        self.tenant_id: Optional[str] = None
        self.authenticated: bool = False
        self.token: Optional[str] = None
        
    def has_role(self, role: str) -> bool:
        """Check if the user has a specific role."""
        return role in self.roles
        
    def has_any_role(self, roles: List[str]) -> bool:
        """Check if the user has any of the specified roles."""
        return any(role in self.roles for role in roles)
        
    def to_dict(self) -> dict:
        """Convert the security context to a dictionary."""
        return {
            "user_id": self.user_id,
            "username": self.username,
            "roles": self.roles,
            "tenant_id": self.tenant_id,
            "authenticated": self.authenticated
        }


class V2AuthService:
    """
    V2 Authentication service for middleware.
    
    Handles token validation and user context retrieval using V2 database schema.
    """
    
    def __init__(self, db_session: Session):
        self.db = db_session
        self.logger = logger
    
    def decode_token(self, token: str) -> Optional[dict]:
        """
        Decode and validate a JWT token.
        
        Args:
            token: JWT token string
            
        Returns:
            Optional[dict]: Decoded token payload if valid, None otherwise
        """
        try:
            payload = jwt.decode(token, JWT_SECRET_KEY, algorithms=[JWT_ALGORITHM])
            
            # Check if token is expired
            exp = payload.get("exp")
            if exp and datetime.utcnow().timestamp() > exp:
                self.logger.warning("Token has expired")
                return None
                
            return payload
            
        except jwt.ExpiredSignatureError:
            self.logger.warning("Token has expired")
            return None
        except jwt.InvalidTokenError as e:
            self.logger.warning(f"Invalid token: {str(e)}")
            return None
        except Exception as e:
            self.logger.error(f"Error decoding token: {str(e)}")
            return None
    
    def validate_token_in_db(self, token: str, user_id: str) -> bool:
        """
        Validate that the token exists in the database and is active.
        
        Args:
            token: JWT token string
            user_id: User ID from token
            
        Returns:
            bool: True if token is valid in database, False otherwise
        """
        try:
            query = """
            SELECT COUNT(*)
            FROM workflow_runtime.user_oauth_tokens
            WHERE (access_token = :token OR refresh_token = :token) 
              AND user_id = :user_id
              AND expires_at > CURRENT_TIMESTAMP
            """
            
            result = self.db.execute(text(query), {
                "token": token,
                "user_id": user_id
            }).fetchone()
            
            return result[0] > 0 if result else False
            
        except Exception as e:
            self.logger.error(f"Error validating token in database: {str(e)}")
            return False
    
    def get_user_context(self, user_id: str, tenant_id: str) -> Optional[V2SecurityContext]:
        """
        Get user context from database.
        
        Args:
            user_id: User ID
            tenant_id: Tenant ID
            
        Returns:
            Optional[V2SecurityContext]: User context if found, None otherwise
        """
        try:
            query = """
            SELECT 
                u.user_id, u.username, u.status,
                array_agg(DISTINCT ur.role) as roles,
                ur.tenant_id
            FROM workflow_runtime.users u
            LEFT JOIN workflow_runtime.user_roles ur ON u.user_id = ur.user_id
            WHERE u.user_id = :user_id AND ur.tenant_id = :tenant_id AND u.status = 'active'
            GROUP BY u.user_id, u.username, u.status, ur.tenant_id
            """
            
            result = self.db.execute(text(query), {
                "user_id": user_id,
                "tenant_id": tenant_id
            }).fetchone()
            
            if not result:
                return None
            
            context = V2SecurityContext()
            context.user_id = result.user_id
            context.username = result.username
            context.roles = result.roles if result.roles and result.roles[0] is not None else []
            context.tenant_id = result.tenant_id
            context.authenticated = True
            
            return context
            
        except Exception as e:
            self.logger.error(f"Error getting user context: {str(e)}")
            return None


def extract_token_from_request(request: Request) -> Optional[str]:
    """
    Extract JWT token from request headers.
    
    Args:
        request: FastAPI request object
        
    Returns:
        Optional[str]: JWT token if found, None otherwise
    """
    # Check Authorization header
    auth_header = request.headers.get("Authorization")
    if auth_header and auth_header.startswith("Bearer "):
        return auth_header.replace("Bearer ", "")
    
    return None


def get_v2_security_context(
    request: Request,
    db: Session = Depends(get_db)
) -> V2SecurityContext:
    """
    Get V2 security context from request.
    
    This function extracts and validates the JWT token, then returns the security context.
    It does not require authentication - returns an unauthenticated context if no valid token.
    
    Args:
        request: FastAPI request object
        db: Database session
        
    Returns:
        V2SecurityContext: Security context (may be unauthenticated)
    """
    context = V2SecurityContext()
    
    # Extract token
    token = extract_token_from_request(request)
    if not token:
        return context
    
    # Create auth service
    auth_service = V2AuthService(db)
    
    # Decode token
    payload = auth_service.decode_token(token)
    if not payload:
        return context
    
    # Extract user info from token
    user_id = payload.get("sub")
    tenant_id = payload.get("tenant_id")
    
    if not user_id or not tenant_id:
        logger.warning("Token missing required fields (sub or tenant_id)")
        return context
    
    # Validate token in database
    if not auth_service.validate_token_in_db(token, user_id):
        logger.warning(f"Token not found or expired in database for user {user_id}")
        return context
    
    # Get user context from database
    user_context = auth_service.get_user_context(user_id, tenant_id)
    if not user_context:
        logger.warning(f"User context not found for user {user_id} in tenant {tenant_id}")
        return context
    
    # Set token in context
    user_context.token = token
    
    return user_context


def require_v2_auth(
    security_context: V2SecurityContext = Depends(get_v2_security_context)
) -> V2SecurityContext:
    """
    Require V2 authentication for a route.
    
    Args:
        security_context: V2 security context from dependency
        
    Returns:
        V2SecurityContext: Authenticated security context
        
    Raises:
        HTTPException: If the user is not authenticated
    """
    if not security_context.authenticated:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Not authenticated",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    return security_context


def require_v2_role(role: str):
    """
    Require a specific role for a V2 route.
    
    Args:
        role: Role to require
        
    Returns:
        Callable: Dependency function
    """
    def dependency(security_context: V2SecurityContext = Depends(require_v2_auth)):
        if not security_context.has_role(role):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=f"Role '{role}' required",
            )
        return security_context
    
    return dependency


def require_v2_any_role(roles: List[str]):
    """
    Require any of the specified roles for a V2 route.
    
    Args:
        roles: List of roles to require
        
    Returns:
        Callable: Dependency function
    """
    def dependency(security_context: V2SecurityContext = Depends(require_v2_auth)):
        if not security_context.has_any_role(roles):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=f"One of roles {roles} required",
            )
        return security_context
    
    return dependency


def require_v2_tenant(tenant_id: str):
    """
    Require a specific tenant for a V2 route.
    
    Args:
        tenant_id: Tenant ID to require
        
    Returns:
        Callable: Dependency function
    """
    def dependency(security_context: V2SecurityContext = Depends(require_v2_auth)):
        if security_context.tenant_id != tenant_id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=f"Access denied for tenant '{tenant_id}'",
            )
        return security_context
    
    return dependency


# Aliases for backward compatibility and consistency
get_security_context = get_v2_security_context
require_auth = require_v2_auth
SecurityContext = V2SecurityContext
