2025-05-11 08:53:04,458 - prescriptive_parser - INFO - Initializing PrescriptiveParser
2025-05-11 08:53:04,458 - component_deployer - INFO - ComponentDeployer initialized with use_temp_schema=True
2025-05-11 08:53:04,464 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:04,502 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:04,508 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:04,519 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:04,527 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:04,535 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:04,544 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:04,553 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:04,561 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:04,570 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:04,571 - component_deployer - INFO - Analyzing workflow_runtime schema to create necessary tables in temporary schema
2025-05-11 08:53:04,576 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:04,583 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:04,587 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:04,594 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:04,603 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:04,613 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:04,618 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:04,628 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:04,637 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:04,643 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:04,653 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:04,661 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:04,667 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:04,676 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:04,687 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:04,694 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:04,703 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:04,712 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:04,719 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:04,728 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:04,736 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:04,744 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:04,755 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:04,763 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:04,772 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:04,781 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:04,790 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:04,798 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:04,808 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:04,817 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:04,824 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:04,834 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:04,843 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:04,851 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:04,860 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:04,871 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:04,878 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:04,887 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:04,898 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:04,906 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:04,915 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:04,925 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:04,931 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:04,942 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:04,952 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:04,958 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:04,970 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:04,979 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:04,986 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:04,996 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:05,004 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:05,013 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:05,025 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:05,035 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:05,044 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:05,055 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:05,065 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:05,074 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:05,085 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:05,096 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:05,104 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:05,116 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:05,126 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:05,134 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:05,144 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:05,153 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:05,162 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:05,174 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:05,184 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:05,194 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:05,206 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:05,216 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:05,225 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:05,237 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:05,247 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:05,256 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:05,267 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:05,278 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:05,288 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:05,299 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:05,309 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:05,317 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:05,329 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:05,339 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:05,348 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:05,359 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:05,369 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:05,377 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:05,388 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:05,398 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:05,408 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:05,418 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:05,428 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:05,437 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:05,446 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:05,455 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:05,463 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:05,472 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:05,480 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:05,488 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:05,497 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:05,505 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:05,511 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:05,521 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:05,529 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:05,536 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:05,547 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:05,556 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:05,563 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:05,575 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:05,584 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:05,590 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:05,602 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:05,611 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:05,617 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:05,628 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:05,641 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:05,649 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:05,659 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:05,667 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:05,675 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:05,686 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:05,696 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:05,704 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:05,714 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:05,725 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:05,734 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:05,746 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:05,755 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:05,763 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:05,774 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:05,785 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:05,792 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:05,802 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:05,812 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:05,822 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:05,833 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:05,844 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:05,853 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:05,864 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:05,874 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:05,882 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:05,892 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:05,902 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:05,909 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:05,918 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:05,929 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:05,936 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:05,946 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:05,954 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:05,962 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:05,973 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:05,983 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:05,991 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:06,003 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:06,013 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:06,022 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:06,034 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:06,044 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:06,052 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:06,063 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:06,073 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:06,081 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:06,093 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:06,103 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:06,113 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:06,124 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:06,134 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:06,142 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:06,153 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:06,164 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:06,175 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:06,186 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:06,196 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:06,205 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:06,216 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:06,225 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:06,235 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:06,246 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:06,255 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:06,262 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:06,272 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:06,280 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:06,289 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:06,298 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:06,308 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:06,317 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:06,327 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:06,336 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:06,345 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:06,357 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:06,366 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:06,374 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:06,384 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:06,395 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:06,405 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:06,415 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:06,425 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:06,434 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:06,445 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:06,454 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:06,463 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:06,472 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:06,482 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:06,489 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:06,499 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:06,509 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:06,519 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:06,528 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:06,538 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:06,547 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:06,622 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:06,631 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:06,640 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:06,646 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:06,653 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:06,660 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:06,667 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:06,673 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:06,680 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:06,689 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:06,697 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:06,705 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:06,713 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:06,720 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:06,727 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:06,734 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:06,735 - db_utils - ERROR - Database error: constraint "entity_attributes_entity_id_fkey" for relation "entity_attributes" already exists
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.DuplicateObject: constraint "entity_attributes_entity_id_fkey" for relation "entity_attributes" already exists

2025-05-11 08:53:06,739 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:06,745 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:06,752 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:06,753 - db_utils - ERROR - Database error: column "input_stack_id" referenced in foreign key constraint does not exist
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.UndefinedColumn: column "input_stack_id" referenced in foreign key constraint does not exist

2025-05-11 08:53:06,757 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:06,757 - db_utils - ERROR - Database error: constraint "lo_input_items_lo_id_fkey" for relation "lo_input_items" already exists
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.DuplicateObject: constraint "lo_input_items_lo_id_fkey" for relation "lo_input_items" already exists

2025-05-11 08:53:06,764 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:06,771 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:06,772 - db_utils - ERROR - Database error: there is no unique constraint matching given keys for referenced table "roles"
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.InvalidForeignKey: there is no unique constraint matching given keys for referenced table "roles"

2025-05-11 08:53:06,776 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:06,784 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:06,792 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:06,793 - db_utils - ERROR - Database error: constraint "lo_output_items_lo_id_fkey" for relation "lo_output_items" already exists
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.DuplicateObject: constraint "lo_output_items_lo_id_fkey" for relation "lo_output_items" already exists

2025-05-11 08:53:06,798 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:06,798 - db_utils - ERROR - Database error: column "output_stack_id" referenced in foreign key constraint does not exist
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.UndefinedColumn: column "output_stack_id" referenced in foreign key constraint does not exist

2025-05-11 08:53:06,804 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:06,812 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:06,813 - db_utils - ERROR - Database error: column "id" referenced in foreign key constraint does not exist
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.UndefinedColumn: column "id" referenced in foreign key constraint does not exist

2025-05-11 08:53:06,818 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:06,818 - db_utils - ERROR - Database error: column "input_stack_id" referenced in foreign key constraint does not exist
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.UndefinedColumn: column "input_stack_id" referenced in foreign key constraint does not exist

2025-05-11 08:53:06,824 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:06,825 - db_utils - ERROR - Database error: column "id" referenced in foreign key constraint does not exist
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.UndefinedColumn: column "id" referenced in foreign key constraint does not exist

2025-05-11 08:53:06,831 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:06,832 - db_utils - ERROR - Database error: column "input_stack_id" referenced in foreign key constraint does not exist
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.UndefinedColumn: column "input_stack_id" referenced in foreign key constraint does not exist

2025-05-11 08:53:06,838 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:06,838 - db_utils - ERROR - Database error: column "id" referenced in foreign key constraint does not exist
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.UndefinedColumn: column "id" referenced in foreign key constraint does not exist

2025-05-11 08:53:06,844 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:06,845 - db_utils - ERROR - Database error: column "input_stack_id" referenced in foreign key constraint does not exist
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.UndefinedColumn: column "input_stack_id" referenced in foreign key constraint does not exist

2025-05-11 08:53:06,851 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:06,852 - db_utils - ERROR - Database error: column "id" referenced in foreign key constraint does not exist
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.UndefinedColumn: column "id" referenced in foreign key constraint does not exist

2025-05-11 08:53:06,857 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:06,858 - db_utils - ERROR - Database error: column "input_stack_id" referenced in foreign key constraint does not exist
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.UndefinedColumn: column "input_stack_id" referenced in foreign key constraint does not exist

2025-05-11 08:53:06,864 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:06,871 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:06,877 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:06,885 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:06,893 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:06,901 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:06,909 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:06,916 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:06,924 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:06,932 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:06,933 - db_utils - ERROR - Database error: column "inherits_from" referenced in foreign key constraint does not exist
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.UndefinedColumn: column "inherits_from" referenced in foreign key constraint does not exist

2025-05-11 08:53:06,938 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:06,939 - db_utils - ERROR - Database error: column "tenant_id" referenced in foreign key constraint does not exist
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.UndefinedColumn: column "tenant_id" referenced in foreign key constraint does not exist

2025-05-11 08:53:06,945 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:06,954 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:06,961 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:06,968 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:06,976 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:06,977 - db_utils - ERROR - Database error: there is no unique constraint matching given keys for referenced table "output_items"
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.InvalidForeignKey: there is no unique constraint matching given keys for referenced table "output_items"

2025-05-11 08:53:06,983 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:06,984 - db_utils - ERROR - Database error: there is no unique constraint matching given keys for referenced table "output_items"
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.InvalidForeignKey: there is no unique constraint matching given keys for referenced table "output_items"

2025-05-11 08:53:06,989 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:06,989 - db_utils - ERROR - Database error: there is no unique constraint matching given keys for referenced table "output_items"
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.InvalidForeignKey: there is no unique constraint matching given keys for referenced table "output_items"

2025-05-11 08:53:06,995 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:06,996 - db_utils - ERROR - Database error: there is no unique constraint matching given keys for referenced table "output_items"
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.InvalidForeignKey: there is no unique constraint matching given keys for referenced table "output_items"

2025-05-11 08:53:07,000 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:07,008 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:07,016 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:07,023 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:07,031 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:07,039 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:07,040 - db_utils - ERROR - Database error: column "id" referenced in foreign key constraint does not exist
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.UndefinedColumn: column "id" referenced in foreign key constraint does not exist

2025-05-11 08:53:07,045 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:07,046 - db_utils - ERROR - Database error: column "input_stack_id" referenced in foreign key constraint does not exist
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.UndefinedColumn: column "input_stack_id" referenced in foreign key constraint does not exist

2025-05-11 08:53:07,051 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:07,052 - db_utils - ERROR - Database error: column "id" referenced in foreign key constraint does not exist
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.UndefinedColumn: column "id" referenced in foreign key constraint does not exist

2025-05-11 08:53:07,057 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:07,058 - db_utils - ERROR - Database error: column "input_stack_id" referenced in foreign key constraint does not exist
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.UndefinedColumn: column "input_stack_id" referenced in foreign key constraint does not exist

2025-05-11 08:53:07,063 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:07,073 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:07,073 - db_utils - ERROR - Database error: constraint "local_objectives_go_id_fkey" for relation "local_objectives" already exists
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.DuplicateObject: constraint "local_objectives_go_id_fkey" for relation "local_objectives" already exists

2025-05-11 08:53:07,080 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:07,081 - db_utils - ERROR - Database error: column "tenant_id" referenced in foreign key constraint does not exist
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.UndefinedColumn: column "tenant_id" referenced in foreign key constraint does not exist

2025-05-11 08:53:07,086 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:07,094 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:07,101 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:07,109 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:07,116 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:07,123 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:07,130 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:07,138 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:07,144 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:07,151 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:07,160 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:07,167 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:07,175 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:07,176 - db_utils - ERROR - Database error: column "id" referenced in foreign key constraint does not exist
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.UndefinedColumn: column "id" referenced in foreign key constraint does not exist

2025-05-11 08:53:07,182 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:07,183 - db_utils - ERROR - Database error: column "output_stack_id" referenced in foreign key constraint does not exist
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.UndefinedColumn: column "output_stack_id" referenced in foreign key constraint does not exist

2025-05-11 08:53:07,187 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:07,188 - db_utils - ERROR - Database error: column "id" referenced in foreign key constraint does not exist
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.UndefinedColumn: column "id" referenced in foreign key constraint does not exist

2025-05-11 08:53:07,193 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:07,194 - db_utils - ERROR - Database error: column "output_stack_id" referenced in foreign key constraint does not exist
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.UndefinedColumn: column "output_stack_id" referenced in foreign key constraint does not exist

2025-05-11 08:53:07,200 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:07,200 - db_utils - ERROR - Database error: column "id" referenced in foreign key constraint does not exist
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.UndefinedColumn: column "id" referenced in foreign key constraint does not exist

2025-05-11 08:53:07,205 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:07,205 - db_utils - ERROR - Database error: column "input_stack_id" referenced in foreign key constraint does not exist
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.UndefinedColumn: column "input_stack_id" referenced in foreign key constraint does not exist

2025-05-11 08:53:07,211 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:07,211 - db_utils - ERROR - Database error: column "id" referenced in foreign key constraint does not exist
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.UndefinedColumn: column "id" referenced in foreign key constraint does not exist

2025-05-11 08:53:07,216 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:07,217 - db_utils - ERROR - Database error: column "input_stack_id" referenced in foreign key constraint does not exist
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.UndefinedColumn: column "input_stack_id" referenced in foreign key constraint does not exist

2025-05-11 08:53:07,223 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:07,231 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:07,239 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:07,247 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:07,255 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:07,256 - db_utils - ERROR - Database error: column "id" referenced in foreign key constraint does not exist
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.UndefinedColumn: column "id" referenced in foreign key constraint does not exist

2025-05-11 08:53:07,261 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:07,262 - db_utils - ERROR - Database error: column "input_stack_id" referenced in foreign key constraint does not exist
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.UndefinedColumn: column "input_stack_id" referenced in foreign key constraint does not exist

2025-05-11 08:53:07,267 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:07,268 - db_utils - ERROR - Database error: column "id" referenced in foreign key constraint does not exist
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.UndefinedColumn: column "id" referenced in foreign key constraint does not exist

2025-05-11 08:53:07,274 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:07,274 - db_utils - ERROR - Database error: column "input_stack_id" referenced in foreign key constraint does not exist
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.UndefinedColumn: column "input_stack_id" referenced in foreign key constraint does not exist

2025-05-11 08:53:07,280 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:07,282 - component_deployer - INFO - Successfully analyzed workflow_runtime schema and created necessary tables
2025-05-11 08:53:07,282 - deploy_to_temp_schema - INFO - Deploying sample components to schema workflow_temp
2025-05-11 08:53:07,282 - prescriptive_parser - INFO - Parsing entities prescriptive text
2025-05-11 08:53:07,282 - entity_parser - INFO - Starting to parse entity definitions
2025-05-11 08:53:07,283 - entity_parser - INFO - Parsing entity: Employee
2025-05-11 08:53:07,284 - entity_parser - INFO - Successfully parsed entity 'Employee'
2025-05-11 08:53:07,284 - entity_parser - INFO - Parsing entity: Employee
2025-05-11 08:53:07,285 - entity_parser - INFO - Successfully parsed entity 'Employee'
2025-05-11 08:53:07,285 - entity_parser - INFO - Parsing entity: Department
2025-05-11 08:53:07,285 - entity_parser - INFO - Successfully parsed entity 'Department'
2025-05-11 08:53:07,285 - entity_parser - INFO - Successfully parsed 2 entities
2025-05-11 08:53:07,285 - deploy_to_temp_schema - INFO - Deploying entities
2025-05-11 08:53:07,290 - component_deployer - INFO - Deploying component of type: entities
2025-05-11 08:53:07,299 - component_deployer - INFO - Using schema: workflow_temp
2025-05-11 08:53:07,299 - entity_deployer - INFO - Deploying entities to workflow_temp
2025-05-11 08:53:07,304 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:07,311 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:07,312 - db_utils - ERROR - Database error: column "status" of relation "entities" does not exist
LINE 4:                     status, type, attribute_prefix
                            ^
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 54, in execute_query
    cursor.execute(query, params)
psycopg2.errors.UndefinedColumn: column "status" of relation "entities" does not exist
LINE 4:                     status, type, attribute_prefix
                            ^

2025-05-11 08:53:07,313 - component_deployer - INFO - Component deployment failed
2025-05-11 08:53:07,313 - deploy_to_temp_schema - ERROR - Failed to deploy entities: ['Database error: column "status" of relation "entities" does not exist\nLINE 4:                     status, type, attribute_prefix\n                            ^\n']
2025-05-11 08:53:07,313 - deploy_to_temp_schema - ERROR - Failed to deploy entities: ['Database error: column "status" of relation "entities" does not exist\nLINE 4:                     status, type, attribute_prefix\n                            ^\n']
2025-05-11 08:53:07,313 - prescriptive_parser - INFO - Parsing go_definitions prescriptive text
2025-05-11 08:53:07,313 - go_parser - INFO - Starting to parse GO definitions
2025-05-11 08:53:07,313 - go_parser - INFO - Parsing GO: Leave Approval Process (ID: go001)
2025-05-11 08:53:07,315 - go_parser - INFO - Successfully parsed GO 'Leave Approval Process'
2025-05-11 08:53:07,315 - go_parser - INFO - Successfully parsed 1 global objectives
2025-05-11 08:53:07,315 - deploy_to_temp_schema - INFO - Deploying go_definitions
2025-05-11 08:53:07,319 - component_deployer - INFO - Deploying component of type: go_definitions
2025-05-11 08:53:07,326 - component_deployer - INFO - Using schema: workflow_temp
2025-05-11 08:53:07,330 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:07,331 - component_deployer - ERROR - Cannot deploy go_definitions because no entities exist. Entities must be deployed first.
2025-05-11 08:53:07,331 - deploy_to_temp_schema - ERROR - Failed to deploy go_definitions: ['Cannot deploy go_definitions because no entities exist. Entities must be deployed first.']
2025-05-11 08:53:07,331 - deploy_to_temp_schema - ERROR - Failed to deploy GO definitions: ['Cannot deploy go_definitions because no entities exist. Entities must be deployed first.']
2025-05-11 08:53:07,331 - prescriptive_parser - INFO - Parsing lo_definitions prescriptive text
2025-05-11 08:53:07,331 - lo_parser - INFO - Starting to parse LO definitions
2025-05-11 08:53:07,331 - lo_parser - INFO - Parsing LO: SubmitLeaveRequest
2025-05-11 08:53:07,332 - lo_parser - INFO - Successfully parsed LO 'SubmitLeaveRequest'
2025-05-11 08:53:07,332 - lo_parser - INFO - Parsing LO: ReviewLeaveRequest
2025-05-11 08:53:07,332 - lo_parser - INFO - Successfully parsed LO 'ReviewLeaveRequest'
2025-05-11 08:53:07,332 - lo_parser - INFO - Successfully parsed 2 local objectives
2025-05-11 08:53:07,332 - deploy_to_temp_schema - INFO - Deploying lo_definitions
2025-05-11 08:53:07,335 - component_deployer - INFO - Deploying component of type: lo_definitions
2025-05-11 08:53:07,339 - component_deployer - INFO - Using schema: workflow_temp
2025-05-11 08:53:07,342 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:07,344 - component_deployer - ERROR - Cannot deploy lo_definitions because no entities exist. Entities must be deployed first.
2025-05-11 08:53:07,344 - deploy_to_temp_schema - ERROR - Failed to deploy lo_definitions: ['Cannot deploy lo_definitions because no entities exist. Entities must be deployed first.']
2025-05-11 08:53:07,344 - deploy_to_temp_schema - ERROR - Failed to deploy LO definitions: ['Cannot deploy lo_definitions because no entities exist. Entities must be deployed first.']
2025-05-11 08:53:07,344 - prescriptive_parser - INFO - Parsing roles prescriptive text
2025-05-11 08:53:07,344 - role_parser - INFO - Starting to parse role definitions
2025-05-11 08:53:07,344 - role_parser - INFO - Parsing role: Employee (ID: emp001)
2025-05-11 08:53:07,344 - role_parser - INFO - Successfully parsed role 'Employee'
2025-05-11 08:53:07,344 - role_parser - INFO - Parsing role: Manager (ID: mgr001)
2025-05-11 08:53:07,344 - role_parser - INFO - Successfully parsed role 'Manager'
2025-05-11 08:53:07,344 - role_parser - INFO - Parsing role: HRManager (ID: hr001)
2025-05-11 08:53:07,344 - role_parser - INFO - Successfully parsed role 'HRManager'
2025-05-11 08:53:07,344 - role_parser - INFO - Parsing role: FinanceManager (ID: fin001)
2025-05-11 08:53:07,344 - role_parser - INFO - Successfully parsed role 'FinanceManager'
2025-05-11 08:53:07,344 - role_parser - INFO - Parsing role: SystemAdmin (ID: sys001)
2025-05-11 08:53:07,344 - role_parser - INFO - Successfully parsed role 'SystemAdmin'
2025-05-11 08:53:07,344 - role_parser - INFO - Successfully parsed 5 roles
2025-05-11 08:53:07,344 - deploy_to_temp_schema - INFO - Deploying roles
2025-05-11 08:53:07,346 - component_deployer - INFO - Deploying component of type: roles
2025-05-11 08:53:07,348 - component_deployer - INFO - Using schema: workflow_temp
2025-05-11 08:53:07,352 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:07,353 - component_deployer - ERROR - Cannot deploy roles because no entities exist. Entities must be deployed first.
2025-05-11 08:53:07,353 - deploy_to_temp_schema - ERROR - Failed to deploy roles: ['Cannot deploy roles because no entities exist. Entities must be deployed first.']
2025-05-11 08:53:07,353 - deploy_to_temp_schema - ERROR - Failed to deploy roles: ['Cannot deploy roles because no entities exist. Entities must be deployed first.']
2025-05-11 08:53:07,353 - deploy_to_temp_schema - INFO - Querying schema workflow_temp
2025-05-11 08:53:07,356 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:07,359 - deploy_to_temp_schema - INFO - Found 76 tables in schema workflow_temp
2025-05-11 08:53:07,363 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:07,363 - deploy_to_temp_schema - INFO - No data found in table workflow_temp.agent_rights
2025-05-11 08:53:07,367 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:07,368 - deploy_to_temp_schema - INFO - No data found in table workflow_temp.agent_stack
2025-05-11 08:53:07,371 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:07,372 - deploy_to_temp_schema - INFO - No data found in table workflow_temp.alembic_version
2025-05-11 08:53:07,375 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:07,376 - deploy_to_temp_schema - INFO - No data found in table workflow_temp.attribute_enum_values
2025-05-11 08:53:07,379 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:07,380 - deploy_to_temp_schema - INFO - No data found in table workflow_temp.attribute_ui_controls
2025-05-11 08:53:07,383 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:07,384 - deploy_to_temp_schema - INFO - No data found in table workflow_temp.attribute_validations
2025-05-11 08:53:07,388 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:07,389 - deploy_to_temp_schema - INFO - No data found in table workflow_temp.conditional_success_messages
2025-05-11 08:53:07,393 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:07,394 - deploy_to_temp_schema - INFO - No data found in table workflow_temp.data_mapping_stack
2025-05-11 08:53:07,398 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:07,398 - deploy_to_temp_schema - INFO - No data found in table workflow_temp.data_mappings
2025-05-11 08:53:07,404 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:07,404 - deploy_to_temp_schema - INFO - No data found in table workflow_temp.dropdown_data_sources
2025-05-11 08:53:07,408 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:07,409 - deploy_to_temp_schema - INFO - No data found in table workflow_temp.entities
2025-05-11 08:53:07,413 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:07,413 - deploy_to_temp_schema - INFO - No data found in table workflow_temp.entity_attribute_metadata
2025-05-11 08:53:07,418 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:07,419 - deploy_to_temp_schema - INFO - No data found in table workflow_temp.entity_attributes
2025-05-11 08:53:07,422 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:07,423 - deploy_to_temp_schema - INFO - No data found in table workflow_temp.entity_business_rules
2025-05-11 08:53:07,426 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:07,427 - deploy_to_temp_schema - INFO - No data found in table workflow_temp.entity_permissions
2025-05-11 08:53:07,432 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:07,432 - deploy_to_temp_schema - INFO - No data found in table workflow_temp.entity_relationships
2025-05-11 08:53:07,438 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:07,439 - deploy_to_temp_schema - INFO - No data found in table workflow_temp.execution_path_tracking
2025-05-11 08:53:07,443 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:07,444 - deploy_to_temp_schema - INFO - No data found in table workflow_temp.execution_pathway_conditions
2025-05-11 08:53:07,449 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:07,449 - deploy_to_temp_schema - INFO - No data found in table workflow_temp.execution_pathways
2025-05-11 08:53:07,453 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:07,454 - deploy_to_temp_schema - INFO - No data found in table workflow_temp.execution_rules
2025-05-11 08:53:07,458 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:07,458 - deploy_to_temp_schema - INFO - No data found in table workflow_temp.global_objectives
2025-05-11 08:53:07,463 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:07,464 - deploy_to_temp_schema - INFO - No data found in table workflow_temp.go_lo_mapping
2025-05-11 08:53:07,468 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:07,468 - deploy_to_temp_schema - INFO - No data found in table workflow_temp.go_performance_metrics
2025-05-11 08:53:07,474 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:07,475 - deploy_to_temp_schema - INFO - No data found in table workflow_temp.input_data_sources
2025-05-11 08:53:07,479 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:07,479 - deploy_to_temp_schema - INFO - No data found in table workflow_temp.input_dependencies
2025-05-11 08:53:07,483 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:07,484 - deploy_to_temp_schema - INFO - No data found in table workflow_temp.input_items
2025-05-11 08:53:07,487 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:07,488 - deploy_to_temp_schema - INFO - No data found in table workflow_temp.input_stack
2025-05-11 08:53:07,492 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:07,492 - deploy_to_temp_schema - INFO - No data found in table workflow_temp.lo_data_mapping_stack
2025-05-11 08:53:07,497 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:07,498 - deploy_to_temp_schema - INFO - No data found in table workflow_temp.lo_data_mappings
2025-05-11 08:53:07,503 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:07,503 - deploy_to_temp_schema - INFO - No data found in table workflow_temp.lo_input_execution
2025-05-11 08:53:07,507 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:07,508 - deploy_to_temp_schema - INFO - No data found in table workflow_temp.lo_input_items
2025-05-11 08:53:07,513 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:07,514 - deploy_to_temp_schema - INFO - No data found in table workflow_temp.lo_input_stack
2025-05-11 08:53:07,518 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:07,518 - deploy_to_temp_schema - INFO - No data found in table workflow_temp.lo_input_validations
2025-05-11 08:53:07,522 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:07,523 - deploy_to_temp_schema - INFO - No data found in table workflow_temp.lo_nested_functions
2025-05-11 08:53:07,527 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:07,528 - deploy_to_temp_schema - INFO - No data found in table workflow_temp.lo_output_execution
2025-05-11 08:53:07,534 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:07,535 - deploy_to_temp_schema - INFO - No data found in table workflow_temp.lo_output_items
2025-05-11 08:53:07,539 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:07,539 - deploy_to_temp_schema - INFO - No data found in table workflow_temp.lo_output_stack
2025-05-11 08:53:07,544 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:07,545 - deploy_to_temp_schema - INFO - No data found in table workflow_temp.lo_output_triggers
2025-05-11 08:53:07,550 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:07,550 - deploy_to_temp_schema - INFO - No data found in table workflow_temp.lo_system_functions
2025-05-11 08:53:07,554 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:07,555 - deploy_to_temp_schema - INFO - No data found in table workflow_temp.local_objectives
2025-05-11 08:53:07,560 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:07,561 - deploy_to_temp_schema - INFO - No data found in table workflow_temp.mapping_rules
2025-05-11 08:53:07,566 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:07,566 - deploy_to_temp_schema - INFO - No data found in table workflow_temp.metrics_aggregation
2025-05-11 08:53:07,570 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:07,571 - deploy_to_temp_schema - INFO - No data found in table workflow_temp.metrics_reporting
2025-05-11 08:53:07,575 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:07,576 - deploy_to_temp_schema - INFO - No data found in table workflow_temp.objective_permissions
2025-05-11 08:53:07,580 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:07,580 - deploy_to_temp_schema - INFO - No data found in table workflow_temp.organizational_units
2025-05-11 08:53:07,585 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:07,586 - deploy_to_temp_schema - INFO - No data found in table workflow_temp.output_items
2025-05-11 08:53:07,590 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:07,591 - deploy_to_temp_schema - INFO - No data found in table workflow_temp.output_stack
2025-05-11 08:53:07,594 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:07,595 - deploy_to_temp_schema - INFO - No data found in table workflow_temp.output_triggers
2025-05-11 08:53:07,600 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:07,601 - deploy_to_temp_schema - INFO - No data found in table workflow_temp.permission_capabilities
2025-05-11 08:53:07,605 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:07,605 - deploy_to_temp_schema - INFO - No data found in table workflow_temp.permission_contexts
2025-05-11 08:53:07,610 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:07,611 - deploy_to_temp_schema - INFO - No data found in table workflow_temp.permission_types
2025-05-11 08:53:07,615 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:07,616 - deploy_to_temp_schema - INFO - No data found in table workflow_temp.role
2025-05-11 08:53:07,621 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:07,621 - deploy_to_temp_schema - INFO - No data found in table workflow_temp.role_inheritance
2025-05-11 08:53:07,625 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:07,626 - deploy_to_temp_schema - INFO - No data found in table workflow_temp.role_permissions
2025-05-11 08:53:07,630 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:07,631 - deploy_to_temp_schema - INFO - No data found in table workflow_temp.roles
2025-05-11 08:53:07,636 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:07,637 - deploy_to_temp_schema - INFO - No data found in table workflow_temp.runtime_metrics_stack
2025-05-11 08:53:07,640 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:07,641 - deploy_to_temp_schema - INFO - No data found in table workflow_temp.success_messages
2025-05-11 08:53:07,645 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:07,646 - deploy_to_temp_schema - INFO - No data found in table workflow_temp.system_functions
2025-05-11 08:53:07,651 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:07,651 - deploy_to_temp_schema - INFO - No data found in table workflow_temp.tenants
2025-05-11 08:53:07,655 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:07,656 - deploy_to_temp_schema - INFO - No data found in table workflow_temp.terminal_pathways
2025-05-11 08:53:07,661 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:07,662 - deploy_to_temp_schema - INFO - No data found in table workflow_temp.ui_elements
2025-05-11 08:53:07,667 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:07,668 - deploy_to_temp_schema - INFO - No data found in table workflow_temp.ui_stack
2025-05-11 08:53:07,672 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:07,672 - deploy_to_temp_schema - INFO - No data found in table workflow_temp.user
2025-05-11 08:53:07,677 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:07,678 - deploy_to_temp_schema - INFO - No data found in table workflow_temp.user_oauth_tokens
2025-05-11 08:53:07,683 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:07,684 - deploy_to_temp_schema - INFO - No data found in table workflow_temp.user_organizations
2025-05-11 08:53:07,688 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:07,688 - deploy_to_temp_schema - INFO - No data found in table workflow_temp.user_role
2025-05-11 08:53:07,693 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:07,693 - deploy_to_temp_schema - INFO - No data found in table workflow_temp.user_roles
2025-05-11 08:53:07,698 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:07,699 - deploy_to_temp_schema - INFO - No data found in table workflow_temp.user_sessions
2025-05-11 08:53:07,703 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:07,703 - deploy_to_temp_schema - INFO - No data found in table workflow_temp.users
2025-05-11 08:53:07,708 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:07,709 - deploy_to_temp_schema - INFO - No data found in table workflow_temp.workflow_instances
2025-05-11 08:53:07,713 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:07,714 - deploy_to_temp_schema - INFO - No data found in table workflow_temp.workflow_results
2025-05-11 08:53:07,718 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:07,718 - deploy_to_temp_schema - INFO - No data found in table workflow_temp.workflow_transaction
2025-05-11 08:53:07,723 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:07,724 - deploy_to_temp_schema - INFO - No data found in table workflow_temp.z_entity_leave_application
2025-05-11 08:53:07,728 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:07,729 - deploy_to_temp_schema - INFO - No data found in table workflow_temp.z_entity_leave_sub_type
2025-05-11 08:53:07,733 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:07,734 - deploy_to_temp_schema - INFO - No data found in table workflow_temp.z_entity_role
2025-05-11 08:53:07,738 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 08:53:07,739 - deploy_to_temp_schema - INFO - No data found in table workflow_temp.z_entity_user
2025-05-11 08:53:07,739 - deploy_to_temp_schema - INFO - Components deployed to schema workflow_temp
2025-05-11 08:53:07,739 - deploy_to_temp_schema - INFO - You can now connect to the database and query the schema workflow_temp
2025-05-11 08:53:07,739 - deploy_to_temp_schema - INFO - To create a migration script, you can use the schema_analyzer.py script to compare the schemas
