# Authentication and Workflow API Documentation

This document provides detailed information about the authentication and workflow API endpoints available in the system.

## Authentication Endpoints

### Register User

Creates a new user in the system.

**Endpoint:** `POST /api/v1/auth/auth/register`

**Headers:**
- Content-Type: application/json

**Request Body:**
```json
{
  "username": "johndo<PERSON>",
  "email": "<EMAIL>",
  "password": "secure123",
  "first_name": "<PERSON>",
  "last_name": "<PERSON><PERSON>",
  "roles": ["User"],
  "org_unit_id": "org_it",
  "tenant_id": "t001"
}
```

**Response:** (201 Created)
```json
{
  "user_id": "0be32e4c-a2d6-40f5-8958-f312ddf7dd45",
  "username": "johndoe",
  "email": "<EMAIL>",
  "first_name": "<PERSON>",
  "last_name": "<PERSON><PERSON>",
  "status": "active",
  "roles": ["User"],
  "org_units": ["org_it"],
  "tenant_id": "t001",
  "disabled": false
}
```

**Notes:**
- The `roles` array must contain valid role names that exist in the database (e.g., "Administrator", "User", "UserManager")
- The `org_unit_id` must be a valid organizational unit ID that exists in the database (e.g., "org_it", "org_hr")
- The `tenant_id` must be a valid tenant ID that exists in the database (e.g., "t001")

### Login & Generate Token

Authenticates a user and generates access and refresh tokens.

**Endpoint:** `POST /api/v1/auth/auth/token`

**Headers:**
- Content-Type: application/x-www-form-urlencoded

**Request Body:**
```
username=johndoe&password=secure123
```

**Response:** (200 OK)
```json
{
  "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "refresh_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "token_type": "bearer",
  "expires_at": 1745742059
}
```

**Notes:**
- The access token expires after 30 minutes
- The refresh token can be used to obtain a new access token without re-authentication
- The `expires_at` field contains the Unix timestamp when the access token expires

### Get Current User Information

Retrieves information about the currently authenticated user.

**Endpoint:** `GET /api/v1/auth/auth/me`

**Headers:**
- Authorization: Bearer {access_token}

**Response:** (200 OK)
```json
{
  "user_id": "0be32e4c-a2d6-40f5-8958-f312ddf7dd45",
  "username": "johndoe",
  "email": "<EMAIL>",
  "first_name": "John",
  "last_name": "Doe",
  "status": "active",
  "roles": ["User"],
  "org_units": ["org_it"],
  "tenant_id": "t001",
  "disabled": false
}
```

### Refresh Token

Generates a new access token using a refresh token.

**Endpoint:** `POST /api/v1/auth/auth/refresh`

**Headers:**
- Content-Type: application/json

**Request Body:**
```json
{
  "refresh_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
}
```

**Response:** (200 OK)
```json
{
  "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "refresh_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "token_type": "bearer",
  "expires_at": 1745742230
}
```

### Logout

Logs out the current user by revoking their token.

**Endpoint:** `POST /api/v1/auth/auth/logout`

**Headers:**
- Authorization: Bearer {access_token}

**Response:** (204 No Content)

## Workflow Endpoints

### Get Global Objectives

Retrieves a list of global objectives for a tenant.

**Endpoint:** `GET /api/v1/global-objectives/?tenant_id={tenant_id}`

**Headers:**
- Authorization: Bearer {access_token}

**Response:** (200 OK)
```json
[
  {
    "objective_id": "go001",
    "name": "leave application workflow",
    "tenant_id": "t001",
    "version": "1.0",
    "status": "active"
  },
  {
    "objective_id": "go002",
    "name": "task management workflow",
    "tenant_id": "t001",
    "version": "1.0",
    "status": "active"
  },
  ...
]
```

### Create Workflow Instance

Creates a new workflow instance.

**Endpoint:** `POST /api/v1/workflows/instances`

**Headers:**
- Content-Type: application/json
- Authorization: Bearer {access_token}

**Request Body:**
```json
{
  "go_id": "go001",
  "tenant_id": "t001",
  "user_id": "0be32e4c-a2d6-40f5-8958-f312ddf7dd45"
}
```

**Response:** (201 Created)
```json
{
  "instance_id": "d8cefe03-05b9-4cc3-a587-0b1f32073d13",
  "go_id": "go001",
  "tenant_id": "t001",
  "status": "Draft",
  "started_by": "0be32e4c-a2d6-40f5-8958-f312ddf7dd45",
  "started_at": "2025-04-27T07:59:49.597312",
  "completed_at": null,
  "current_lo_id": null,
  "instance_data": {},
  "is_test": false,
  "version": "1.0"
}
```

### Start Workflow Instance

Starts a workflow instance.

**Endpoint:** `POST /api/v1/workflows/instances/{instance_id}/start`

**Headers:**
- Content-Type: application/json
- Authorization: Bearer {access_token}

**Request Body:**
```json
{
  "user_id": "0be32e4c-a2d6-40f5-8958-f312ddf7dd45"
}
```

**Response:** (200 OK)
```json
{
  "instance_id": "d8cefe03-05b9-4cc3-a587-0b1f32073d13",
  "go_id": "go001",
  "tenant_id": "t001",
  "status": "Active",
  "started_by": "0be32e4c-a2d6-40f5-8958-f312ddf7dd45",
  "started_at": "2025-04-27T08:01:28.797513",
  "completed_at": null,
  "current_lo_id": "lo001",
  "instance_data": {},
  "is_test": false,
  "version": "1.0"
}
```

### Get Workflow Instance Inputs

Retrieves the inputs required for a workflow instance.

**Endpoint:** `GET /api/v1/workflows/instances/{instance_id}/inputs`

**Headers:**
- Authorization: Bearer {access_token}

**Response:** (200 OK)
```json
{
  "local_objective": "lo001",
  "user_inputs": [
    {
      "input_id": "in002",
      "input_stack_id": 1830,
      "attribute_id": "at002",
      "entity_id": "e001",
      "display_name": "Employee ID",
      "data_type": "String",
      "source_type": "user",
      "required": true,
      "ui_control": "oj-input-text",
      "is_visible": true,
      "allowed_values": null,
      "validations": null,
      "contextual_id": "go001.lo001.in002",
      "input_value": null,
      "has_dropdown_source": false,
      "dependencies": null,
      "dependency_type": null,
      "metadata": {
        "usage": "",
        "is_informational": false,
        "has_dropdown_source": false
      }
    },
    ...
  ],
  "system_inputs": [...],
  "info_inputs": [...],
  "dependent_inputs": [...],
  "dependencies": {...}
}
```

### Execute Workflow Step

Executes a step in a workflow instance.

**Endpoint:** `POST /api/v1/workflows/instances/{instance_id}/execute`

**Headers:**
- Content-Type: application/json
- Authorization: Bearer {access_token}

**Request Body:**
```json
{
  "input_data": {
    "Employee ID": "EMP-001",
    "Start Date": "2025-04-01",
    "End Date": "2025-04-05",
    "Leave Reason": "Medical emergency",
    "Leave Type": "sick leave"
  },
  "user_id": "0be32e4c-a2d6-40f5-8958-f312ddf7dd45"
}
```

**Response:** (200 OK)
```json
{
  "status": "Completed",
  "message": "Local Objective lo001 executed successfully",
  "output": {
    "id": 17,
    "created_at": "2025-04-27 08:04:32",
    "updated_at": "2025-04-27 08:04:32",
    "created_by": null,
    "updated_by": null,
    "leave_id": "17",
    "employee_id": "EMP-001",
    "start_date": "2025-04-01",
    "end_date": "2025-04-05",
    "num_days": 4,
    "reason": "Medical emergency",
    "status": "PENDING",
    "remarks": null,
    "approved_by": null,
    "leave_type": "sick leave",
    "leave_sub_type": null
  },
  "next_lo_id": "lo003"
}
```

## Error Responses

### Common HTTP Status Codes

| Status Code | Description | Possible Causes |
|-------------|-------------|----------------|
| 200 | OK | Request succeeded |
| 201 | Created | Resource successfully created |
| 204 | No Content | Request succeeded, no content returned (e.g., after logout) |
| 400 | Bad Request | Invalid input, missing required fields, validation errors |
| 401 | Unauthorized | Missing or invalid authentication token, expired token |
| 403 | Forbidden | Valid authentication but insufficient permissions for the operation |
| 404 | Not Found | Resource not found, invalid endpoint |
| 409 | Conflict | Resource already exists, version conflict |
| 422 | Unprocessable Entity | Request understood but cannot be processed due to semantic errors |
| 429 | Too Many Requests | Rate limit exceeded |
| 500 | Internal Server Error | Unexpected server error, database error |
| 503 | Service Unavailable | Server temporarily unavailable, maintenance |

### Authentication-Specific Error Codes

#### Register User (POST /api/v1/auth/auth/register)

| Status Code | Error Message | Description |
|-------------|---------------|-------------|
| 400 | "User with this username or email already exists" | Username or email is already registered |
| 400 | "Invalid role specified" | One or more roles in the request don't exist in the database |
| 400 | "Invalid organizational unit specified" | The org_unit_id doesn't exist in the database |
| 400 | "Invalid tenant specified" | The tenant_id doesn't exist in the database |
| 400 | "Password does not meet requirements" | Password doesn't meet complexity requirements |

#### Login (POST /api/v1/auth/auth/token)

| Status Code | Error Message | Description |
|-------------|---------------|-------------|
| 401 | "Incorrect username or password" | Invalid credentials provided |
| 401 | "Account is disabled" | User account is disabled or inactive |
| 429 | "Too many failed login attempts" | Account temporarily locked due to multiple failed attempts |

#### Refresh Token (POST /api/v1/auth/auth/refresh)

| Status Code | Error Message | Description |
|-------------|---------------|-------------|
| 401 | "Invalid refresh token" | The refresh token is invalid or expired |
| 401 | "Token has been revoked" | The refresh token has been revoked (e.g., after logout) |

#### Get Current User (GET /api/v1/auth/auth/me)

| Status Code | Error Message | Description |
|-------------|---------------|-------------|
| 401 | "Not authenticated" | Missing or invalid authentication token |
| 404 | "User not found" | User associated with the token no longer exists |

### Workflow-Specific Error Codes

#### Create Workflow Instance (POST /api/v1/workflows/instances)

| Status Code | Error Message | Description |
|-------------|---------------|-------------|
| 400 | "Invalid global objective ID" | The go_id doesn't exist or is inactive |
| 400 | "Invalid tenant ID" | The tenant_id doesn't exist |
| 403 | "Insufficient permissions to create workflow instance" | User doesn't have permission to create this workflow |

#### Start Workflow Instance (POST /api/v1/workflows/instances/{instance_id}/start)

| Status Code | Error Message | Description |
|-------------|---------------|-------------|
| 404 | "Workflow instance not found" | The instance_id doesn't exist |
| 400 | "Workflow instance already started" | The instance is already in an Active state |
| 403 | "Insufficient permissions to start workflow instance" | User doesn't have permission to start this workflow |

#### Execute Workflow Step (POST /api/v1/workflows/instances/{instance_id}/execute)

| Status Code | Error Message | Description |
|-------------|---------------|-------------|
| 404 | "Workflow instance not found" | The instance_id doesn't exist |
| 400 | "Workflow instance not in active state" | The instance is not in an Active state |
| 400 | "Missing required input fields" | One or more required input fields are missing |
| 400 | "Invalid input data format" | Input data is in an incorrect format |
| 403 | "Insufficient permissions to execute workflow step" | User doesn't have permission to execute this step |
| 409 | "Workflow step already executed" | The current step has already been executed |
| 422 | "Business rule validation failed" | Input data failed business rule validation |

### General Error Response Format

Error responses typically follow this format:

```json
{
  "detail": "Error message describing the issue",
  "code": "ERROR_CODE",  // Optional error code
  "errors": [            // Optional array of specific field errors
    {
      "field": "field_name",
      "message": "Error message for this field"
    }
  ]
}
```
