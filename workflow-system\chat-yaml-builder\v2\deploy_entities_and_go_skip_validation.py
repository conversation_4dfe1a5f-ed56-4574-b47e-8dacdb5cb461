"""
Deploy entities and GO definitions (Skip Validation)

This script deploys the entities from sample_entity_output2.txt and then deploys the GO definitions,
skipping the registry validation step.
"""

import os
import sys
import logging
import yaml  # Used to convert Python dictionaries to YAML strings for registry validation
from typing import Dict, List, Tuple, Any

# Add parent directory to path to import modules
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
from parsers.entity_parser import parse_entities
from deployers.entity_deployer import deploy_entities
from parsers.go_parser import parse_go_definitions
from deployers.go_deployer import deploy_go_definitions
from registry_validator import RegistryValidator

# Make sure we're using the real db_utils, not the mock one
import sys
if 'mock_db_utils' in sys.modules:
    del sys.modules['mock_db_utils']

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('deploy_entities_and_go')

def load_entity_definitions() -> str:
    """
    Load entity definitions from a file.
    
    Returns:
        Entity definitions as a string
    """
    try:
        entity_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'samples', 'sample_entity_output2.txt')
        with open(entity_path, 'r') as f:
            return f.read()
    except Exception as e:
        logger.error(f"Error loading entity definitions: {str(e)}")
        return ""

def load_go_definitions() -> str:
    """
    Load GO definitions from a file.
    
    Returns:
        GO definitions as a string
    """
    try:
        go_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'samples', 'sample_go_output.txt')
        with open(go_path, 'r') as f:
            return f.read()
    except Exception as e:
        logger.error(f"Error loading GO definitions: {str(e)}")
        return ""

def parse_and_deploy_entities() -> Tuple[bool, Dict]:
    """
    Parse and deploy entity definitions.
    
    Returns:
        Tuple containing:
            - Boolean indicating if deployment was successful
            - Entity data
    """
    logger.info("Deploying entity definitions")
    
    # Load entity definitions
    entity_def = load_entity_definitions()
    if not entity_def:
        logger.error("Failed to load entity definitions")
        return False, {}
    
    # Parse entity definitions
    try:
        entity_data, warnings = parse_entities(entity_def)
        
        # Check if parsing was successful
        if not entity_data or "entities" not in entity_data or not entity_data["entities"]:
            logger.error("Entity parsing failed: No entities found")
            return False, entity_data
        
        # Log parsing results
        logger.info(f"Successfully parsed {len(entity_data['entities'])} entities")
        for entity_name in entity_data['entities']:
            logger.info(f"- {entity_name}")
        
        if warnings:
            logger.warning(f"Entity parsing completed with {len(warnings)} warnings")
            for warning in warnings:
                logger.warning(f"- {warning}")
        
        # Deploy entity definitions
        schema_name = "workflow_temp"
        success, messages = deploy_entities(entity_data, schema_name)
        
        # Log deployment results
        if success:
            logger.info(f"Successfully deployed entity definitions to {schema_name}")
        else:
            logger.error(f"Entity deployment failed with {len(messages)} messages")
            for message in messages:
                logger.error(f"- {message}")
            return False, entity_data
        
        return True, entity_data
    except Exception as e:
        logger.error(f"Error deploying entity definitions: {str(e)}", exc_info=True)
        return False, {}

def deploy_go_without_validation(entity_data: Dict) -> bool:
    """
    Parse and deploy GO definitions without validation.
    
    Args:
        entity_data: Entity data
        
    Returns:
        Boolean indicating if deployment was successful
    """
    logger.info("Deploying GO definitions (skipping validation)")
    
    # Load GO definitions
    go_def = load_go_definitions()
    if not go_def:
        logger.error("Failed to load GO definitions")
        return False
    
    # Parse GO definitions
    try:
        go_data, warnings = parse_go_definitions(go_def)
        
        # Check if parsing was successful
        if not go_data or "global_objectives" not in go_data or not go_data["global_objectives"]:
            logger.error("GO parsing failed: No global objectives found")
            return False
        
        # Log parsing results
        logger.info(f"Successfully parsed {len(go_data['global_objectives'])} global objectives")
        for go_name in go_data['global_objectives']:
            logger.info(f"- {go_name}")
        
        if warnings:
            logger.warning(f"GO parsing completed with {len(warnings)} warnings")
            for warning in warnings:
                logger.warning(f"- {warning}")
        
        # Skip validation and deploy GO definitions directly
        schema_name = "workflow_temp"
        success, messages = deploy_go_definitions(go_data, schema_name)
        
        # Log deployment results
        if success:
            logger.info(f"Successfully deployed GO definitions to {schema_name}")
        else:
            logger.error(f"GO deployment failed with {len(messages)} messages")
            for message in messages:
                logger.error(f"- {message}")
            return False
        
        return True
    except Exception as e:
        logger.error(f"Error deploying GO definitions: {str(e)}", exc_info=True)
        return False

def main():
    """Main function."""
    logger.info("Starting entity and GO deployment (skipping validation)")
    
    # Deploy entities
    entity_success, entity_data = parse_and_deploy_entities()
    if not entity_success:
        logger.error("Entity deployment failed")
        return
    
    # Deploy GO definitions without validation
    go_success = deploy_go_without_validation(entity_data)
    if not go_success:
        logger.error("GO deployment failed")
        return
    
    logger.info("Entity and GO deployment completed successfully")

if __name__ == "__main__":
    main()
