#!/usr/bin/env python3
"""
<PERSON>rip<PERSON> to run the schema fix and test it.

This script runs both the fix_schema_issues.py and test_schema_fix.py scripts
in sequence to fix the schema issues and verify that the fix works.
"""

import os
import sys
import logging
import argparse
import subprocess
from typing import List

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger('run_schema_fix')

def run_script(script_name: str, args: List[str] = None) -> bool:
    """
    Run a Python script.
    
    Args:
        script_name: Name of the script to run
        args: List of arguments to pass to the script
        
    Returns:
        <PERSON><PERSON><PERSON> indicating if the script ran successfully
    """
    cmd = [sys.executable, script_name]
    if args:
        cmd.extend(args)
    
    logger.info(f"Running {script_name} with args: {args}")
    
    try:
        result = subprocess.run(cmd, check=True, capture_output=True, text=True)
        logger.info(f"{script_name} output:\n{result.stdout}")
        if result.stderr:
            logger.warning(f"{script_name} stderr:\n{result.stderr}")
        return True
    except subprocess.CalledProcessError as e:
        logger.error(f"{script_name} failed with exit code {e.returncode}")
        logger.error(f"stdout: {e.stdout}")
        logger.error(f"stderr: {e.stderr}")
        return False

def main():
    """
    Main function.
    """
    parser = argparse.ArgumentParser(description='Run the schema fix and test it.')
    parser.add_argument('--schema', type=str, default='workflow_temp', help='Schema name to fix')
    parser.add_argument('--fix-only', action='store_true', help='Only run the fix, not the test')
    parser.add_argument('--test-only', action='store_true', help='Only run the test, not the fix')
    parser.add_argument('--verify-only', action='store_true', help='Only verify the schema, do not fix issues')
    
    args = parser.parse_args()
    
    # Run the fix_schema_issues.py script
    if not args.test_only:
        fix_args = ['--schema', args.schema]
        if args.verify_only:
            fix_args.append('--verify-only')
        
        success = run_script('fix_schema_issues.py', fix_args)
        if not success:
            logger.error("Failed to fix schema issues")
            return 1
    
    # Run the test_schema_fix.py script
    if not args.fix_only and not args.verify_only:
        test_args = ['--schema', args.schema]
        if args.test_only:
            test_args.append('--skip-fix')
        
        success = run_script('test_schema_fix.py', test_args)
        if not success:
            logger.error("Failed to test schema fix")
            return 1
    
    logger.info("Schema fix and test completed successfully")
    return 0

if __name__ == '__main__':
    sys.exit(main())
