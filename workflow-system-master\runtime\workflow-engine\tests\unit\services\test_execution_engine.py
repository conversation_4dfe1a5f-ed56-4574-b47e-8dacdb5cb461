"""
Tests for the Workflow Execution Engine.
"""

import pytest
from unittest.mock import MagicMock, patch
from datetime import datetime

from app.services.workflow.execution_engine import WorkflowExecutionEngine, WorkflowExecutionException

@pytest.fixture
def mock_system_functions():
    """
    Create a mock SystemFunctions instance.
    """
    with patch('app.services.workflow.execution_engine.SystemFunctions') as mock:
        sf_instance = MagicMock()
        mock.return_value.__enter__.return_value = sf_instance
        yield sf_instance

@pytest.fixture
def mock_get_collection():
    """
    Create a mock for the get_collection function.
    """
    with patch('app.services.workflow.execution_engine.get_collection') as mock:
        collection = MagicMock()
        mock.return_value = collection
        yield collection

def test_get_workflow_instance(mock_system_functions):
    """
    Test the _get_workflow_instance method.
    """
    # Setup
    instance_id = "test-instance-id"
    user_id = "test-user-id"
    
    # Mock execution result
    mock_system_functions._execute_pg_query.return_value = [
        {
            "instance_id": instance_id,
            "go_id": "test-go-id",
            "tenant_id": "test-tenant-id",
            "status": "Active",
            "started_by": user_id,
            "started_at": datetime.now(),
            "current_lo_id": "test-lo-id",
            "instance_data": {},
            "is_test": False,
            "version": "1.0"
        }
    ]
    
    # Create engine
    engine = WorkflowExecutionEngine(instance_id=instance_id, user_id=user_id)
    
    # Execute method under test
    with engine:
        result = engine._get_workflow_instance()
    
    # Verify
    assert result["instance_id"] == instance_id
    assert result["status"] == "Active"
    mock_system_functions._execute_pg_query.assert_called_once()

def test_get_workflow_instance_not_found(mock_system_functions):
    """
    Test the _get_workflow_instance method when the instance is not found.
    """
    # Setup
    instance_id = "test-instance-id"
    user_id = "test-user-id"
    
    # Mock execution result - not found
    mock_system_functions._execute_pg_query.return_value = []
    
    # Create engine
    engine = WorkflowExecutionEngine(instance_id=instance_id, user_id=user_id)
    
    # Execute method under test - should raise exception
    with engine:
        with pytest.raises(WorkflowExecutionException) as exc:
            engine._get_workflow_instance()
    
    # Verify
    assert f"Workflow instance {instance_id} not found" in str(exc.value)
    mock_system_functions._execute_pg_query.assert_called_once()

def test_get_global_objective(mock_get_collection):
    """
    Test the _get_global_objective method.
    """
    # Setup
    instance_id = "test-instance-id"
    user_id = "test-user-id"
    go_id = "test-go-id"
    
    # Mock find_one result
    mock_get_collection.find_one.return_value = {
        "objective_id": go_id,
        "name": "Test GO",
        "status": "Active"
    }
    
    # Create engine
    engine = WorkflowExecutionEngine(instance_id=instance_id, user_id=user_id)
    
    # Execute method under test
    with engine:
        result = engine._get_global_objective(go_id)
    
    # Verify
    assert result["objective_id"] == go_id
    assert result["name"] == "Test GO"
    mock_get_collection.find_one.assert_called_once_with({"objective_id": go_id})

def test_get_global_objective_not_found(mock_get_collection):
    """
    Test the _get_global_objective method when the GO is not found.
    """
    # Setup
    instance_id = "test-instance-id"
    user_id = "test-user-id"
    go_id = "test-go-id"
    
    # Mock find_one result - not found
    mock_get_collection.find_one.return_value = None
    
    # Create engine
    engine = WorkflowExecutionEngine(instance_id=instance_id, user_id=user_id)
    
    # Execute method under test - should raise exception
    with engine:
        with pytest.raises(WorkflowExecutionException) as exc:
            engine._get_global_objective(go_id)
    
    # Verify
    assert f"Global objective {go_id} not found" in str(exc.value)
    mock_get_collection.find_one.assert_called_once_with({"objective_id": go_id})

def test_evaluate_execution_pathway_sequential(mock_system_functions):
    """
    Test the _evaluate_execution_pathway method with a sequential pathway.
    """
    # Setup
    instance_id = "test-instance-id"
    user_id = "test-user-id"
    
    # Create LO with sequential pathway
    lo = {
        "id": "test-lo-id",
        "execution_pathway": {
            "type": "Sequential",
            "next_lo": "next-lo-id"
        }
    }
    
    # Create engine
    engine = WorkflowExecutionEngine(instance_id=instance_id, user_id=user_id)
    
    # Execute method under test
    with engine:
        result = engine._evaluate_execution_pathway(lo, {})
    
    # Verify
    assert result == "next-lo-id"

def test_evaluate_execution_pathway_terminal(mock_system_functions):
    """
    Test the _evaluate_execution_pathway method with a terminal pathway.
    """
    # Setup
    instance_id = "test-instance-id"
    user_id = "test-user-id"
    
    # Create LO with terminal pathway
    lo = {
        "id": "test-lo-id",
        "execution_pathway": {
            "type": "Terminal"
        }
    }
    
    # Create engine
    engine = WorkflowExecutionEngine(instance_id=instance_id, user_id=user_id)
    
    # Execute method under test
    with engine:
        result = engine._evaluate_execution_pathway(lo, {})
    
    # Verify
    assert result is None

def test_evaluate_execution_pathway_alternative(mock_system_functions):
    """
    Test the _evaluate_execution_pathway method with an alternative pathway.
    """
    # Setup
    instance_id = "test-instance-id"
    user_id = "test-user-id"
    
    # Create LO with alternative pathway
    lo = {
        "id": "test-lo-id",
        "execution_pathway": {
            "type": "Alternative",
            "conditions": [
                {
                    "condition": "status = Approved",
                    "next_lo": "approved-lo-id"
                },
                {
                    "condition": "status = Rejected",
                    "next_lo": "rejected-lo-id"
                },
                {
                    "condition": "DEFAULT",
                    "next_lo": "default-lo-id"
                }
            ]
        }
    }
    
    # Create engine
    engine = WorkflowExecutionEngine(instance_id=instance_id, user_id=user_id)
    
    # Test with matching condition
    with engine:
        result = engine._evaluate_execution_pathway(lo, {"status": "Approved"})
    
    # Verify
    assert result == "approved-lo-id"
    
    # Test with different matching condition
    with engine:
        result = engine._evaluate_execution_pathway(lo, {"status": "Rejected"})
    
    # Verify
    assert result == "rejected-lo-id"
    
    # Test with no matching condition - should use DEFAULT
    with engine:
        result = engine._evaluate_execution_pathway(lo, {"status": "Pending"})
    
    # Verify
    assert result == "default-lo-id"

def test_start_workflow(mock_system_functions, mock_get_collection):
    """
    Test the start_workflow method.
    """
    # Setup
    instance_id = "test-instance-id"
    user_id = "test-user-id"
    go_id = "test-go-id"
    
    # Mock workflow instance
    mock_system_functions._execute_pg_query.return_value = [
        {
            "instance_id": instance_id,
            "go_id": go_id,
            "tenant_id": "test-tenant-id",
            "status": "Draft",
            "started_by": user_id,
            "started_at": datetime.now(),
            "current_lo_id": None,
            "instance_data": {},
            "is_test": False,
            "version": "1.0"
        }
    ]
    
    # Mock global objective
    mock_get_collection.find_one.side_effect = [
        {
            "objective_id": go_id,
            "name": "Test GO",
            "status": "Active"
        },
        {
            "id": "first-lo-id",
            "parent_objective_id": go_id,
            "workflow_source": "Origin",
            "name": "First LO"
        }
    ]
    
    # Mock update
    mock_system_functions._execute_pg_query.return_value = [
        {
            "instance_id": instance_id,
            "go_id": go_id,
            "tenant_id": "test-tenant-id",
            "status": "Active",
            "started_by": user_id,
            "started_at": datetime.now(),
            "current_lo_id": "first-lo-id",
            "instance_data": {},
            "is_test": False,
            "version": "1.0"
        }
    ]
    
    # Create engine
    engine = WorkflowExecutionEngine(instance_id=instance_id, user_id=user_id)
    
    # Execute method under test
    with engine:
        result = engine.start_workflow()
    
    # Verify
    assert result["status"] == "Active"
    assert result["current_lo_id"] == "first-lo-id"
    assert mock_system_functions._execute_pg_query.call_count >= 2
    mock_get_collection.find_one.assert_called()
