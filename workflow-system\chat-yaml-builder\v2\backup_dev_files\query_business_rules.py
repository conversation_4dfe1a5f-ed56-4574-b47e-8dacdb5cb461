"""
Query entity business rules from the database.
"""

from db_utils import execute_query

def main():
    # First check if the table exists
    print("Checking if entity_business_rules table exists")
    success, messages, result = execute_query(
        """
        SELECT EXISTS (
            SELECT FROM information_schema.tables 
            WHERE table_schema = 'workflow_temp'
            AND table_name = 'entity_business_rules'
        )
        """,
        schema_name="workflow_temp"
    )
    
    if success and result and result[0][0]:
        print("entity_business_rules table exists")
        
        # Query entity_business_rules table
        print("Querying workflow_temp.entity_business_rules table")
        success, messages, rules = execute_query(
            "SELECT entity_id, rule_id, description, condition FROM workflow_temp.entity_business_rules",
            schema_name="workflow_temp"
        )
        
        if success and rules:
            print("Entity Business Rules:")
            for rule in rules:
                print(f"  {rule}")
        else:
            print("No entity business rules found or query failed")
            if messages:
                print("Messages:")
                for message in messages:
                    print(f"  {message}")
    else:
        print("entity_business_rules table does not exist")
        
        # Let's check what tables exist in the schema
        print("Listing tables in workflow_temp schema")
        success, messages, tables = execute_query(
            """
            SELECT table_name 
            FROM information_schema.tables 
            WHERE table_schema = 'workflow_temp'
            ORDER BY table_name
            """,
            schema_name="workflow_temp"
        )
        
        if success and tables:
            print("Tables in workflow_temp schema:")
            for table in tables:
                print(f"  {table[0]}")
        else:
            print("Failed to list tables")
            if messages:
                print("Messages:")
                for message in messages:
                    print(f"  {message}")

if __name__ == "__main__":
    main()
