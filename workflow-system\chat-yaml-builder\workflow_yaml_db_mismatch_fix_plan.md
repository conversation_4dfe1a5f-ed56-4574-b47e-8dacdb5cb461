# Workflow YAML to Database Mismatch Fix Plan

## Issue Analysis

### Problem Statement
There is a mismatch between the workflow definition in the YAML file and what is stored in the database, specifically regarding input field mappings. This causes the API to return user registration inputs instead of leave application inputs.

### Root Cause
The `insert_generator.py` script is not correctly mapping entity attributes when inserting input items:

1. In the YAML file (`leavemanagement_new_rbac.yaml`), the input items for the leave application workflow (lo001) are defined with slot IDs that reference the LeaveApplication entity (e001) attributes.
2. However, in the database, the input items are using slot IDs with the LeaveApplication entity prefix (e001) but with attribute IDs from the User entity (e003).

For example:
- In YAML: `in002` has slot_id "e001.at102.in002" (where at102 is "employeeID" in LeaveApplication)
- In DB: `in002` has slot_id "e001.at017.in002" (where at017 is "username" in User entity)

The issue is in the `insert_input_item_complete` function in `insert_generator.py`. When inserting input items, it's not validating that the attribute IDs in the slot IDs match the correct entity, and it's not properly incrementing/assigning attribute IDs.

### Database Schema Analysis

#### Entity Attributes in Database
```
entity_id | attribute_id |     name     |  display_name  | datatype
-----------+--------------+--------------+----------------+----------
 e001      | at001        | leaveID      | Leave ID       | String
 e001      | at002        | employeeID   | Employee ID    | String
 e001      | at003        | startDate    | Start Date     | Date
 e001      | at004        | endDate      | End Date       | Date
 e001      | at005        | numDays      | Number of Days | Integer
 e001      | at006        | reason       | Leave Reason   | String
 e001      | at007        | status       | Status         | Enum
 e001      | at008        | remarks      | Remarks        | String
 e001      | at009        | approvedBy   | Approved By    | String
 e001      | at010        | leaveType    | Leave Type     | Enum
 e001      | at011        | leaveSubType | Leave Sub-Type | String
```

#### Input Items in Database
```
id   |     slot_id      | source_description
-------+------------------+-----------------------------
 in001 | e001.at016.in001 | Auto-generated Leave ID
 in002 | e001.at017.in002 | Employee ID
 in003 | e001.at018.in003 | Start Date
 in004 | e001.at019.in004 | End Date
 in005 | e001.at020.in005 | Number of Days (calculated)
 in006 | e001.at021.in006 | Leave Reason
 in007 | e001.at022.in007 | Status
 in025 | e001.at025.in025 | Leave Type
 in026 | e001.at011.in026 | Leave Sub-Type
 in027 | e001.at999.in027 | Leave Request Instructions
```

#### User Entity Attributes in Database
```
entity_id | attribute_id |     name      |  display_name  | datatype
-----------+--------------+---------------+----------------+----------
 e003      | at016        | user_id       | User ID        | String
 e003      | at017        | username      | Username       | String
 e003      | at018        | email         | Email          | String
 e003      | at019        | first_name    | First Name     | String
 e003      | at020        | last_name     | Last Name      | String
 e003      | at021        | status        | Status         | Enum
 e003      | at022        | password_hash | Password Hash  | String
 e003      | at025        | team          | Team           | String
```

#### Mismatch Mapping
| Input ID | Current Slot ID | Current Attribute | Should Map To | Correct Attribute |
|----------|----------------|-------------------|---------------|-------------------|
| in001    | e001.at016.in001 | User ID (e003.at016) | e001.at001.in001 | Leave ID (e001.at001) |
| in002    | e001.at017.in002 | Username (e003.at017) | e001.at002.in002 | Employee ID (e001.at002) |
| in003    | e001.at018.in003 | Email (e003.at018) | e001.at003.in003 | Start Date (e001.at003) |
| in004    | e001.at019.in004 | First Name (e003.at019) | e001.at004.in004 | End Date (e001.at004) |
| in005    | e001.at020.in005 | Last Name (e003.at020) | e001.at005.in005 | Number of Days (e001.at005) |
| in006    | e001.at021.in006 | Status (e003.at021) | e001.at006.in006 | Leave Reason (e001.at006) |
| in007    | e001.at022.in007 | Password Hash (e003.at022) | e001.at007.in007 | Status (e001.at007) |
| in025    | e001.at025.in025 | Team (e003.at025) | e001.at010.in025 | Leave Type (e001.at010) |

## Implementation Plan

### 1. Fix the insert_generator.py Script

#### A. Modify the insert_input_item_complete Function

Add validation to ensure that the attribute ID in the slot_id exists in the specified entity and properly assign attribute IDs based on the entity and source description.

```python
def insert_input_item_complete(cursor, input_item, input_stack_id, lo_id, current_time):
    """Insert an input item and all its components (validations, nested functions, dropdown sources) in one operation."""
    try:
        item_id = input_item.get("id", "").lower()
        slot_id = input_item.get("slot_id", "").lower()
        contextual_id = input_item.get("contextual_id", "").lower()
        source_type = input_item["source"]["type"].lower()
        source_description = input_item["source"].get("description", "")
        required = input_item.get("required", False)
        ui_control = input_item.get("ui_control", "")
        is_visible = input_item.get("is_visible", True)
        data_type = input_item.get("data_type", "string")
        
        # Extract entity_id from slot_id
        parts = slot_id.split('.')
        if len(parts) >= 2:
            entity_id = parts[0]
            
            # Find the correct attribute ID based on source_description
            cursor.execute(
                """
                SELECT attribute_id FROM entity_attributes 
                WHERE entity_id = %s AND display_name = %s
                """,
                (entity_id, source_description)
            )
            
            result = cursor.fetchone()
            if result:
                # Found a matching attribute, use its ID
                attribute_id = result[0]
                # Reconstruct the slot_id with the correct attribute ID
                slot_id = f"{entity_id}.{attribute_id}.{item_id}"
                log(f"🔄 Assigned correct slot_id for {item_id}: {slot_id}")
            else:
                # Try case-insensitive match
                cursor.execute(
                    """
                    SELECT attribute_id FROM entity_attributes 
                    WHERE entity_id = %s AND LOWER(display_name) = LOWER(%s)
                    """,
                    (entity_id, source_description)
                )
                
                result = cursor.fetchone()
                if result:
                    attribute_id = result[0]
                    slot_id = f"{entity_id}.{attribute_id}.{item_id}"
                    log(f"🔄 Assigned correct slot_id for {item_id} (case-insensitive match): {slot_id}")
                else:
                    log(f"⚠️ Could not find a matching attribute for '{source_description}' in entity {entity_id}")
                    # Keep the original slot_id but log a warning
        
        # Rest of the function remains the same...
```

#### B. Enhance the ID Generator to Properly Map Entity Attributes

The ID generator should be enhanced to properly map entity attributes based on the source description, regardless of the attribute IDs in the YAML file. This is because the IDs in the YAML will never match the postgres database, which is why the ID generator was created in the first place.

```python
def map_attribute_by_description(cursor, entity_id, source_description):
    """Map an attribute by its description to the correct attribute ID in the database."""
    try:
        # Find the attribute by display name
        cursor.execute(
            """
            SELECT attribute_id FROM entity_attributes 
            WHERE entity_id = %s AND display_name = %s
            """,
            (entity_id, source_description)
        )
        
        result = cursor.fetchone()
        if result:
            return result[0]
        
        # Try case-insensitive match
        cursor.execute(
            """
            SELECT attribute_id FROM entity_attributes 
            WHERE entity_id = %s AND LOWER(display_name) = LOWER(%s)
            """,
            (entity_id, source_description)
        )
        
        result = cursor.fetchone()
        if result:
            return result[0]
        
        # If still not found, log a warning
        log(f"⚠️ Could not find attribute for '{source_description}' in entity {entity_id}")
        return None
    except Exception as e:
        log(f"❌ Error mapping attribute by description: {e}")
        return None
```

This function can be used in the `insert_input_item_complete` function to ensure that the correct attribute ID is used based on the source description, regardless of what's in the YAML file.

### 2. Create a Test Script to Verify Slot ID Integrity

Create a new script `verify_slot_id_integrity.py` to check the integrity of slot ID mappings:

```python
#!/usr/bin/env python3

"""
This script verifies the integrity of slot ID mappings in the database.
It checks that each slot ID references an attribute that exists in the specified entity.
"""

import psycopg2
from datetime import datetime
import os

# Set log path to current working directory
LOG_PATH = os.path.join(
    os.path.dirname(os.path.abspath(__file__)),
    f"verify_slot_id_integrity_log_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
)

# Logging function
def log(msg):
    with open(LOG_PATH, "a") as f:
        f.write(f"{datetime.now().strftime('%Y-%m-%d %H:%M:%S')} | {msg}\n")
    print(msg)

# Database configuration
PG_CONFIG = {
    "dbname": "workflow_system",
    "user": "postgres",
    "password": "workflow_postgres_secure_password",
    "host": "**********",
    "port": "5432"
}

def connect_to_db():
    """Connect to the PostgreSQL database."""
    try:
        conn = psycopg2.connect(**PG_CONFIG)
        log("✅ Connected to PostgreSQL database")
        return conn
    except Exception as e:
        log(f"❌ Error connecting to database: {e}")
        raise

def verify_slot_id_integrity():
    """Verify the integrity of slot ID mappings in the database."""
    conn = connect_to_db()
    cursor = conn.cursor()
    
    try:
        # Set search path to the workflow_runtime schema
        cursor.execute("SET search_path TO workflow_runtime")
        
        # Get all input items
        cursor.execute("""
            SELECT i.id, i.slot_id, i.source_description, lo.lo_id
            FROM lo_input_items i
            JOIN lo_input_stack s ON i.input_stack_id = s.id
            JOIN local_objectives lo ON i.lo_id = lo.lo_id
        """)
        
        items = cursor.fetchall()
        
        log(f"🔍 Checking {len(items)} input items for slot ID integrity")
        
        integrity_issues = []
        
        for item_id, slot_id, description, lo_id in items:
            parts = slot_id.split('.')
            if len(parts) >= 2:
                entity_id = parts[0]
                attribute_id = parts[1]
                
                # Verify that the entity exists
                cursor.execute(
                    """
                    SELECT 1 FROM entities WHERE entity_id = %s
                    """,
                    (entity_id,)
                )
                
                if not cursor.fetchone():
                    integrity_issues.append(f"Entity {entity_id} not found for input {item_id} in {lo_id}")
                    continue
                
                # Verify that the attribute exists for this entity
                cursor.execute(
                    """
                    SELECT display_name FROM entity_attributes 
                    WHERE entity_id = %s AND attribute_id = %s
                    """,
                    (entity_id, attribute_id)
                )
                
                result = cursor.fetchone()
                if not result:
                    integrity_issues.append(f"Attribute {attribute_id} not found for entity {entity_id} in input {item_id}")
                elif result[0] != description:
                    integrity_issues.append(f"Attribute {attribute_id} has display name '{result[0]}' but input {item_id} has source description '{description}'")
        
        if integrity_issues:
            log(f"⚠️ Found {len(integrity_issues)} integrity issues:")
            for issue in integrity_issues:
                log(f"  - {issue}")
        else:
            log("✅ All slot IDs have integrity")
        
    except Exception as e:
        log(f"❌ Error verifying slot ID integrity: {e}")
    finally:
        cursor.close()
        conn.close()

if __name__ == "__main__":
    verify_slot_id_integrity()
```

### 3. Create a Database Reset Script

Since we plan to truncate the database and deploy fresh, let's create a script to do this:

```python
#!/usr/bin/env python3

"""
This script truncates the workflow_runtime schema tables and redeploys the YAML files.
"""

import psycopg2
from datetime import datetime
import os
import traceback
import subprocess

# Set log path to current working directory
LOG_PATH = os.path.join(
    os.path.dirname(os.path.abspath(__file__)),
    f"reset_database_log_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
)

# Logging function
def log(msg):
    with open(LOG_PATH, "a") as f:
        f.write(f"{datetime.now().strftime('%Y-%m-%d %H:%M:%S')} | {msg}\n")
    print(msg)

# Database configuration
PG_CONFIG = {
    "dbname": "workflow_system",
    "user": "postgres",
    "password": "workflow_postgres_secure_password",
    "host": "**********",
    "port": "5432"
}

def connect_to_db():
    """Connect to the PostgreSQL database."""
    try:
        conn = psycopg2.connect(**PG_CONFIG)
        log("✅ Connected to PostgreSQL database")
        return conn
    except Exception as e:
        log(f"❌ Error connecting to database: {e}")
        raise

def reset_database():
    """Truncate the workflow_runtime schema tables."""
    conn = connect_to_db()
    cursor = conn.cursor()
    
    try:
        # Set search path to the workflow_runtime schema
        cursor.execute("SET search_path TO workflow_runtime")
        
        # Get all tables in the schema
        cursor.execute("""
            SELECT table_name
            FROM information_schema.tables
            WHERE table_schema = 'workflow_runtime'
            AND table_type = 'BASE TABLE'
            ORDER BY table_name
        """)
        
        tables = cursor.fetchall()
        
        log(f"🔍 Found {len(tables)} tables to truncate")
        
        # Disable foreign key constraints
        cursor.execute("SET session_replication_role = 'replica'")
        
        # Truncate each table
        for table in tables:
            table_name = table[0]
            cursor.execute(f"TRUNCATE TABLE {table_name} CASCADE")
            log(f"✅ Truncated table: {table_name}")
        
        # Re-enable foreign key constraints
        cursor.execute("SET session_replication_role = 'origin'")
        
        conn.commit()
        log("✅ All tables truncated successfully")
        
    except Exception as e:
        if conn:
            conn.rollback()
        log(f"❌ Error truncating tables: {e}")
        log(traceback.format_exc())
    finally:
        if cursor:
            cursor.close()
        if conn:
            conn.close()

def redeploy_yaml_files():
    """Redeploy the YAML files."""
    try:
        # Run the deploy_rbac_yamls.py script
        log("🔄 Redeploying YAML files...")
        result = subprocess.run(["python", "deploy_rbac_yamls.py"], capture_output=True, text=True)
        
        if result.returncode == 0:
            log("✅ YAML files redeployed successfully")
            log(result.stdout)
        else:
            log(f"❌ Error redeploying YAML files: {result.stderr}")
    except Exception as e:
        log(f"❌ Error redeploying YAML files: {e}")
        log(traceback.format_exc())

if __name__ == "__main__":
    reset_database()
    redeploy_yaml_files()
```

### 4. Add a Post-Deployment Verification Step

After deploying the YAML files, we should verify that the slot IDs are correctly mapped:

```python
def verify_deployment(yaml_file_path):
    """Verify that the deployment was successful by checking slot ID mappings."""
    try:
        # Read the YAML file
        with open(yaml_file_path, 'r') as f:
            yaml_data = yaml.safe_load(f)
        
        # Connect to the database
        conn = connect_to_db()
        cursor = conn.cursor()
        
        # Set search path to the workflow_runtime schema
        cursor.execute("SET search_path TO workflow_runtime")
        
        # Process local objectives
        for lo in yaml_data.get("local_objectives", []):
            lo_id = lo.get("id", "").lower()
            
            # Process input stack
            if "input_stack" in lo and "inputs" in lo["input_stack"]:
                for input_item in lo["input_stack"]["inputs"]:
                    item_id = input_item.get("id", "").lower()
                    source_description = input_item["source"].get("description", "")
                    
                    # Get the slot ID from the database
                    cursor.execute("""
                        SELECT i.slot_id
                        FROM lo_input_items i
                        JOIN lo_input_stack s ON i.input_stack_id = s.id
                        WHERE i.id = %s AND i.lo_id = %s
                    """, (item_id, lo_id))
                    
                    result = cursor.fetchone()
                    if result:
                        slot_id = result[0]
                        parts = slot_id.split('.')
                        if len(parts) >= 2:
                            entity_id = parts[0]
                            attribute_id = parts[1]
                            
                            # Verify that the attribute exists for this entity
                            cursor.execute("""
                                SELECT display_name FROM entity_attributes 
                                WHERE entity_id = %s AND attribute_id = %s
                            """, (entity_id, attribute_id))
                            
                            attr_result = cursor.fetchone()
                            if attr_result:
                                db_description = attr_result[0]
                                if db_description != source_description:
                                    log(f"⚠️ Mismatch for {item_id} in {lo_id}: DB has '{db_description}', YAML has '{source_description}'")
                            else:
                                log(f"⚠️ Attribute {attribute_id} not found for entity {entity_id} in input {item_id}")
                    else:
                        log(f"⚠️ Input {item_id} not found in database for {lo_id}")
        
        log("✅ Deployment verification complete")
        
    except Exception as e:
        log(f"❌ Error verifying deployment: {e}")
        return False
```

## Testing Plan

1. **Unit Tests**: Create unit tests for the new validation functions to ensure they correctly identify and fix slot ID mapping issues.

2. **Integration Tests**: Test the end-to-end deployment process with various YAML files to ensure that slot IDs are correctly mapped.

3. **Regression Tests**: Verify that existing workflows continue to function correctly after the fixes are applied.

## Deployment Plan

1. **Backup**: Create a backup of the current database before making any changes.

2. **Implement Fixes**: Apply the changes to the insert_generator.py script and create the new validation and fix scripts.

3. **Truncate Database**: As per the user's request, truncate the current database to start fresh.

4. **Deploy YAML Files**: Deploy the YAML files with the fixed insert_generator.py script.

5. **Verify**: Run the verification script to ensure that all slot IDs have integrity.

## Conclusion

This implementation plan addresses the root cause of the slot ID mapping issue and provides tools to prevent similar issues in the future. By properly validating and assigning attribute IDs based on the entity and source description, we ensure that the database schema matches the YAML definition.
