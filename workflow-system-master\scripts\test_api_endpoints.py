#!/usr/bin/env python3
"""
Test API endpoints for the Workflow System.
"""

import sys
import os
import logging
import requests
import json

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# API base URL
BASE_URL = "http://localhost:8000/api/v1"

def test_health_endpoint():
    """Test the health check endpoint."""
    try:
        response = requests.get(f"{BASE_URL}/health")
        if response.status_code == 200:
            logger.info("Health check endpoint is working.")
            return True
        else:
            logger.error(f"Health check endpoint returned status code {response.status_code}.")
            return False
    except Exception as e:
        logger.error(f"Failed to connect to health check endpoint: {e}")
        return False

def test_workflow_endpoints():
    """Test workflow-related endpoints."""
    try:
        # Create a workflow instance
        create_data = {
            "go_id": "GO_TestWorkflow",
            "tenant_id": "test-tenant",
            "user_id": "test-user",
            "test_mode": True
        }
        
        create_response = requests.post(
            f"{BASE_URL}/workflows/instances",
            json=create_data
        )
        
        if create_response.status_code != 200:
            logger.error(f"Failed to create workflow instance: {create_response.text}")
            return False
        
        instance_id = create_response.json().get("instance_id")
        logger.info(f"Created workflow instance: {instance_id}")
        
        # Get the workflow instance
        get_response = requests.get(f"{BASE_URL}/workflows/instances/{instance_id}")
        
        if get_response.status_code != 200:
            logger.error(f"Failed to get workflow instance: {get_response.text}")
            return False
        
        logger.info(f"Retrieved workflow instance: {get_response.json()}")
        
        # Start the workflow instance
        start_data = {
            "user_id": "test-user"
        }
        
        start_response = requests.post(
            f"{BASE_URL}/workflows/instances/{instance_id}/start",
            json=start_data
        )
        
        if start_response.status_code != 200:
            logger.error(f"Failed to start workflow instance: {start_response.text}")
            return False
        
        logger.info(f"Started workflow instance: {start_response.json()}")
        
        # List workflow instances
        list_response = requests.get(
            f"{BASE_URL}/workflows/instances",
            params={"tenant_id": "test-tenant", "limit": 10}
        )
        
        if list_response.status_code != 200:
            logger.error(f"Failed to list workflow instances: {list_response.text}")
            return False
        
        logger.info(f"Listed workflow instances: {list_response.json()}")
        
        return True
    except Exception as e:
        logger.error(f"Failed to test workflow endpoints: {e}")
        return False

def test_chat_endpoints():
    """Test chat-related endpoints."""
    try:
        # Test design chat
        design_data = {
            "message": "Create a new workflow for order processing",
            "tenant_id": "test-tenant",
            "user_id": "test-user",
            "context": {}
        }
        
        design_response = requests.post(
            f"{BASE_URL}/chat/design",
            json=design_data
        )
        
        if design_response.status_code != 200:
            logger.error(f"Failed to test design chat: {design_response.text}")
            return False
        
        logger.info(f"Design chat response: {design_response.json()}")
        
        # Test runtime chat
        runtime_data = {
            "message": "Start a new workflow for order processing",
            "tenant_id": "test-tenant",
            "user_id": "test-user",
            "context": {}
        }
        
        runtime_response = requests.post(
            f"{BASE_URL}/chat/runtime",
            json=runtime_data
        )
        
        if runtime_response.status_code != 200:
            logger.error(f"Failed to test runtime chat: {runtime_response.text}")
            return False
        
        logger.info(f"Runtime chat response: {runtime_response.json()}")
        
        return True
    except Exception as e:
        logger.error(f"Failed to test chat endpoints: {e}")
        return False

if __name__ == "__main__":
    logger.info("Testing API endpoints...")
    
    health_ok = test_health_endpoint()
    workflow_ok = test_workflow_endpoints()
    chat_ok = test_chat_endpoints()
    
    if health_ok and workflow_ok and chat_ok:
        logger.info("All API endpoint tests passed!")
        sys.exit(0)
    else:
        logger.error("Some API endpoint tests failed. See logs for details.")
        sys.exit(1)
