"""
Print parsed GO data from sample_go_output.txt
"""

import os
import sys
import json
from parsers.go_parser import parse_go_definitions

def main():
    """Main function to print parsed GO data."""
    try:
        # Get the path to the sample file
        sample_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'samples', 'sample_go_output.txt')
        
        # Read the sample file
        with open(sample_path, 'r') as f:
            content = f.read()
        
        # Parse the GO definitions
        go_data, warnings = parse_go_definitions(content)
        
        # Print the parsed GO data as JSON
        print(json.dumps(go_data, indent=2))
        
        # Print any warnings
        if warnings:
            print("\nWarnings:")
            for warning in warnings:
                print(f"- {warning}")
    except Exception as e:
        print(f"Error: {str(e)}")

if __name__ == "__main__":
    main()
