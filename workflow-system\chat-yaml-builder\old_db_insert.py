import os
import sys
import time
import json
import traceback
import psycopg2
from psycopg2.extras import Json

# === Configuration ===
WORKFLOW_PG_CONFIG = {
    "dbname": "postgres",  # Use existing database for workflow solution
    "user": "postgres",
    "password": "postgres",
    "host": "localhost",
    "port": "5432"
}

RUNTIME_PG_CONFIG = {
    "dbname": "workflow_system",  # Separate database for runtime tables
    "user": "postgres",
    "password": "postgres",
    "host": "localhost",
    "port": "5432"
}

# === Function to process workflow data and create runtime tables ===
def process_workflow_data_for_runtime(workflow_data):
    """Process workflow data to create runtime tables for entities."""
    # Extract tenant information
    tenant = workflow_data.get("tenant", {})
    tenant_id = tenant.get("id", "T001")
    tenant_name = tenant.get("name", "DefaultTenant")
    
    print(f"➡️ Processing runtime tables for tenant: {tenant_id} - {tenant_name}")
    
    # Check and create runtime_solution database
    #if not check_and_create_runtime_database():
     #   print("❌ Failed to create runtime_solution database.")
     #   return False
    
    # Create tenant schema in runtime database
    #schema_name = create_runtime_tenant_schema(tenant_id)
    #if not schema_name:
     #   print("❌ Failed to create tenant schema in runtime database.")
      #  return False
    
    # Extract entities
    entities = workflow_data.get("entities", [])
    if not entities:
        print("⚠️ No entities found in workflow data.")
        return False
    
    # Create tables for entities
    result = create_entity_tables(tenant_id, entities)
    
    return result
def check_and_create_runtime_database():
    """Check if runtime_solution database exists and create if needed."""
    try:
        # Connect to default postgres database
        default_config = WORKFLOW_PG_CONFIG.copy()
        temp_conn = psycopg2.connect(**default_config)
        temp_conn.autocommit = True
        temp_cursor = temp_conn.cursor()
        
        # Check if runtime_solution database exists
        temp_cursor.execute("SELECT 1 FROM pg_database WHERE datname = 'runtime_solution'")
        exists = temp_cursor.fetchone()
        
        if exists:
            print(f"ℹ️ Runtime solution database already exists.")
        else:
            print(f"➡️ Creating runtime_solution database...")
            temp_cursor.execute("CREATE DATABASE runtime_solution")
            print(f"✅ Created runtime_solution database.")
        
        temp_cursor.close()
        temp_conn.close()
        return True
    
    except Exception as e:
        print(f"❌ Error checking/creating runtime database: {e}")
        traceback.print_exc()
        return False
# === Function to connect to PostgreSQL with retry ===
def connect_with_retry(db_config, retries=5, delay=1):
    """Attempt to connect to PostgreSQL with retries."""
    for attempt in range(retries):
        try:
            return psycopg2.connect(**db_config)
        except psycopg2.OperationalError:
            print(f"⏳ Waiting for DB to be ready... retry {attempt + 1}/{retries}")
            time.sleep(delay)
    raise Exception("❌ Could not connect to database after multiple retries.")
def create_runtime_tenant_schema(tenant_id):
    """Create a schema for tenant in the runtime_solution database."""
    schema_name = f"tenant_{tenant_id.lower()}"
    
    try:
        # Connect to runtime_solution database
        runtime_conn = connect_with_retry(RUNTIME_PG_CONFIG)
        runtime_conn.autocommit = True
        
        with runtime_conn.cursor() as cursor:
            # Check if schema exists
            cursor.execute("SELECT schema_name FROM information_schema.schemata WHERE schema_name = %s", (schema_name,))
            exists = cursor.fetchone()
            
            if exists:
                print(f"⚠️ Runtime schema {schema_name} already exists.")
                recreate = input("Do you want to recreate the runtime schema? (y/n): ").lower() == 'y'
                
                if recreate:
                    print(f"➡️ Dropping existing runtime schema: {schema_name}")
                    cursor.execute(f"DROP SCHEMA {schema_name} CASCADE")
                    print(f"✅ Dropped old runtime schema: {schema_name}")
                    
                    cursor.execute(f"CREATE SCHEMA {schema_name}")
                    print(f"✅ Created runtime schema: {schema_name}")
                else:
                    print(f"ℹ️ Using existing runtime schema: {schema_name}")
            else:
                print(f"➡️ Creating runtime schema: {schema_name}")
                cursor.execute(f"CREATE SCHEMA {schema_name}")
                print(f"✅ Created runtime schema: {schema_name}")
        
        runtime_conn.close()
        return schema_name
    
    except Exception as e:
        print(f"❌ Error creating runtime tenant schema: {e}")
        traceback.print_exc()
        return None

import re
    
def camel_to_snake(name):
    # First insert underscore before uppercase letters
    s1 = re.sub('(.)([A-Z][a-z]+)', r'\1_\2', name)
    # Then handle cases like 'ID' where we have multiple uppercase letters
    s2 = re.sub('([a-z0-9])([A-Z])', r'\1_\2', s1)
    # Replace other non-alphanumeric characters with underscores
    s3 = re.sub(r'[^a-zA-Z0-9]', '_', s2)
    # Convert to lowercase
    s4 = s3.lower()
    # Replace multiple consecutive underscores with a single underscore
    s5 = re.sub(r'_+', '_', s4)
    # Remove leading or trailing underscores
    return s5.strip('_')

# === Function to create entity tables in runtime schema ===
def create_entity_tables(tenant_id, entities):
    """Create tables for each entity in the tenant's runtime schema."""
    #schema_name = f"tenant_{tenant_id.lower()}"
    schema_name = "workflow_runtime"
    
    try:
        # Connect to runtime_solution database
        runtime_conn = connect_with_retry(RUNTIME_PG_CONFIG)
        runtime_conn.autocommit = True
        
        with runtime_conn.cursor() as cursor:
            # Set search path to tenant schema
            cursor.execute(f"SET search_path TO {schema_name}")
            
            # Process each entity
            for entity in entities:
                entity_id = entity.get("id", "")
                entity_name = entity.get("name", "")
                attributes = entity.get("attributes", [])
                
                print(f"➡️ Processing entity: {entity_id} - {entity_name}")
                
                # Check if table already exists
                table_name = camel_to_snake(entity_name)
                cursor.execute(f"SELECT to_regclass('{schema_name}.{table_name}')")

                exists = cursor.fetchone()[0]
                
                if exists:
                    print(f"ℹ️ Table for entity {entity_id} already exists, skipping creation")
                    continue
                
                # Generate SQL for entity table
                table_sql = generate_entity_table_sql(entity_id, entity_name, attributes)
                
                # Execute SQL to create table
                try:
                    cursor.execute(table_sql)
                    print(f"✅ Created table for entity: {entity_id}")
                except Exception as e:
                    print(f"❌ Error creating table for entity {entity_id}: {e}")
                    print(f"SQL Statement with error:\n{table_sql}")
        
        runtime_conn.close()
        print(f"✅ Entity tables created in runtime schema: {schema_name}")
        return True
    
    except Exception as e:
        print(f"❌ Error creating entity tables: {e}")
        traceback.print_exc()
        return False

def generate_entity_table_sql(entity_id, entity_name, attributes):
    """Generate SQL to create a table for an entity with its attributes."""
    # Convert entity_name to snake_case (lowercase with underscores)
    # Insert underscore before uppercase letters in camelCase
    
    
    table_name = camel_to_snake(entity_name)
    
    # Start SQL statement
    sql = f"CREATE TABLE IF NOT EXISTS workflow_runtime.{table_name} (\n"
    
    # Add primary key
    sql += "    id SERIAL PRIMARY KEY,\n"
  
    # Add standard audit fields
    sql += "    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,\n"
    sql += "    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,\n"
    sql += "    created_by VARCHAR(255),\n"
    sql += "    updated_by VARCHAR(255),\n"
    
    # Add each attribute
    for attr in attributes:
        attr_id = attr.get("id", "")
        attr_name = attr.get("name", "")
        datatype = attr.get("datatype", "").lower()
        required = attr.get("required", False)
        
        # Skip if attribute name is empty
        if not attr_name:
            continue
        
        # Convert attribute name to snake_case handling camelCase properly
        column_name = camel_to_snake(attr_name)
        
        # Map attribute datatype to PostgreSQL datatype
        pg_type = "VARCHAR(255)"  # Default
        
        if datatype == "string":
            pg_type = "VARCHAR(255)"
        elif datatype == "number":
            pg_type = "NUMERIC"
        elif datatype == "integer":
            pg_type = "INTEGER"
        elif datatype == "date":
            pg_type = "DATE"
        elif datatype == "datetime":
            pg_type = "TIMESTAMP"
        elif datatype == "boolean":
            pg_type = "BOOLEAN"
        elif datatype == "enum":
            # Create enum values as a check constraint later
            pg_type = "VARCHAR(50)"
        elif datatype == "reference":
            # For references, we'll use the ID as foreign key
            pg_type = "VARCHAR(255)"
        elif datatype == "text":
            pg_type = "TEXT"
        
        # Add column with required constraint if needed
        required_str = "NOT NULL" if required else ""
        sql += f"    {column_name} {pg_type} {required_str},\n"
    
    # Remove trailing comma and close statement
    sql = sql.rstrip(",\n") + "\n);\n"
    
    # Add enum constraints if applicable
    for attr in attributes:
        if attr.get("datatype", "").lower() == "enum":
            attr_name = attr.get("name", "")
            # Convert to snake_case
            attr_name_snake = camel_to_snake(attr_name)
            
            enum_values = attr.get("values", [])
            
            if enum_values and attr_name_snake:
                values_list = ", ".join([f"'{val}'" for val in enum_values])
                constraint_name = f"chk_{table_name}_{attr_name_snake}"
                
                # Check if constraint exists before creating it
                check_sql = f"""
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM pg_constraint 
        WHERE conname = '{constraint_name}' AND conrelid = '{table_name}'::regclass
    ) THEN
        ALTER TABLE workflow_runtime.{table_name} ADD CONSTRAINT {constraint_name} 
        CHECK ({attr_name_snake} IN ({values_list}));
    END IF;
END $$;
"""
                sql += check_sql
     # Add timestamps trigger for updated_at
    sql += f"""
-- Create or replace function for updating timestamp
CREATE OR REPLACE FUNCTION update_modified_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for updating timestamp
DROP TRIGGER IF EXISTS update_{table_name}_timestamp ON workflow_runtime.{table_name};
CREATE TRIGGER update_{table_name}_timestamp
BEFORE UPDATE ON workflow_runtime.{table_name}
FOR EACH ROW
EXECUTE FUNCTION update_modified_column();
"""
    
    return sql

if __name__ == "__main__":
   
        mongodb_uri = "mongodb://localhost:27017/"
        db_name =  "workflow_system"
        collection_name =  "workflow"
        
        # Simplified version to demonstrate the concept
        from pymongo import MongoClient
        client = MongoClient(mongodb_uri)
        db = client[db_name]
        collection = db[collection_name]
        draft_workflows = list(collection.find({"status": "draft"}))
        #workflow_doc = collection.find_one()
        draft_count = len(draft_workflows)
        
        if draft_count == 0:
            print("ℹ️ No draft documents found in MongoDB collection.")
        else:          
          for workflow_doc in draft_workflows:
             try:
              if workflow_doc and "workflow_data" in workflow_doc:
               process_workflow_data_for_runtime(workflow_doc["workflow_data"])
            # Update the document status to "complete" in MongoDB
               collection.update_one(
                    {"_id": workflow_doc["_id"]},
                    {"$set": {"status": "complete"}}
                )
            #print(f"✅ Updated MongoDB document status to 'complete' for tenant: {tenant_id}")   
             except Exception as e:
                print(f"❌ Error processing document: {e}")
                traceback.print_exc()
             continue
              