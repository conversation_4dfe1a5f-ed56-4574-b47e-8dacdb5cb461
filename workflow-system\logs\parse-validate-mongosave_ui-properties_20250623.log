{"timestamp": "2025-06-23T04:58:42.597159", "endpoint": "parse-validate-mongosave/ui-properties", "input": {"natural_language": "Tenant: Acme Corp\n\nEntity: LeaveApplication\n\nUI Properties:\nProperty Name | Property Type | Value | Description | Applies To\n\nform_layout | layout | vertical | Vertical form layout for leave application | form\n\nfield_order | order | leaveId,employeeId,startDate,endDate,reason | Order of fields in the form | form\n\nvalidation_style | style | inline | Show validation errors inline | form\n\nsubmit_button_text | text | Submit Leave Request | Text for submit button | button\n\ncancel_button_text | text | Cancel | Text for cancel button | button\n\npage_title | title | Leave Application Form | Page title for the form | page", "tenant_id": "T_ACME_CORP", "edit_draft": false, "edit_temp": false, "edit_production": false}, "output": {"success": true, "ui_property_results": [], "operation": "parse_validate_mongosave", "total_ui_properties": 0}, "status": "success"}
{"timestamp": "2025-06-23T07:27:53.061475", "endpoint": "parse-validate-mongosave/ui-properties", "input": {"natural_language": "Tenant: Alpha Corp\n\nEntity.Attribute | UI Property | Value | Description\n\nEmployee.totalLeaveEntitlement | label | Total Leave Entitlement | Display label for leave entitlement field\nEmployee.totalLeaveEntitlement | placeholder | Enter leave days | Placeholder text for input field\nEmployee.totalLeaveEntitlement | validation_message | Please enter a valid number | Error message for validation", "tenant_id": "T1001", "edit_draft": false, "edit_temp": false, "edit_production": false}, "output": {"success": true, "ui_property_results": [], "operation": "parse_validate_mongosave", "total_ui_properties": 0}, "status": "success"}
{"timestamp": "2025-06-23T07:28:58.247761", "endpoint": "parse-validate-mongosave/ui-properties", "input": {"natural_language": "Tenant: Test\n\nEntity.Attribute | UI Property | Value | Description\n\nEmployee.name | label | Employee Name | Display label", "tenant_id": null, "edit_draft": false, "edit_temp": false, "edit_production": false}, "output": {"success": true, "ui_property_results": [], "operation": "parse_validate_mongosave", "total_ui_properties": 0}, "status": "success"}
{"timestamp": "2025-06-23T07:31:34.700878", "endpoint": "parse-validate-mongosave/ui-properties", "input": {"natural_language": "Tenant: Alpha Corp\n\nEntity.Attribute | Control Type | Display Format | Input Mask | Placeholder Text | Auto Complete | Read Only | Validation Display | Help Text Position | Label | Required Indicator\n\nEmployee.totalLeaveEntitlement | number_input | ##.# days | ##.# | Enter entitlement | false | false | inline | below | Total Leave Entitlement | false\nEmployee.firstName | text_input | text | | Enter first name | false | false | inline | below | First Name | true", "tenant_id": "T1001", "edit_draft": false, "edit_temp": false, "edit_production": false}, "output": {"success": true, "ui_property_results": [{"success": false, "errors": ["Entity with entity_id E_Employee_1750663894 does not exist", "Attribute with attribute_id A_E_Employee_1750663894_totalLeaveEntitlement_1750663894 does not exist"], "parsed_data": {"ui_property_id": "UI1", "entity_id": "E_Employee_1750663894", "attribute_id": "A_E_Employee_1750663894_totalLeaveEntitlement_1750663894", "control_type": "number_input", "display_format": "##.# days", "input_mask": "##.#", "placeholder_text": "Enter entitlement", "auto_complete": false, "read_only": false, "validation_display": "inline", "help_text_position": "below", "label": "Total Leave Entitlement", "required_indicator": false, "natural_language": "Entity: Employee\nAttribute: totalLeaveEntitlement\nControl Type: number_input\nLabel: Total Leave Entitlement\nDisplay Format: ##.# days\nInput Mask: ##.#\nPlaceholder Text: Enter entitlement\nValidation Display: inline\nHelp Text Position: below", "version": 1, "status": "draft", "created_at": "2025-06-23T07:31:34.607694", "updated_at": "2025-06-23T07:31:34.607694", "created_by": "Tarun", "updated_by": "Tarun", "ui_property_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Entity with entity_id E_Employee_1750663894 does not exist", "Attribute with attribute_id A_E_Employee_1750663894_firstName_1750663894 does not exist"], "parsed_data": {"ui_property_id": "UI1", "entity_id": "E_Employee_1750663894", "attribute_id": "A_E_Employee_1750663894_firstName_1750663894", "control_type": "text_input", "display_format": "text", "input_mask": "", "placeholder_text": "Enter first name", "auto_complete": false, "read_only": false, "validation_display": "inline", "help_text_position": "below", "label": "First Name", "required_indicator": true, "natural_language": "Entity: Employee\nAttribute: firstName\nControl Type: text_input\nLabel: First Name\nDisplay Format: text\nPlaceholder Text: Enter first name\nRequired Indicator: true\nValidation Display: inline\nHelp Text Position: below", "version": 1, "status": "draft", "created_at": "2025-06-23T07:31:34.645617", "updated_at": "2025-06-23T07:31:34.645617", "created_by": "Tarun", "updated_by": "Tarun", "ui_property_status": "new", "changes_detected": []}}], "operation": "parse_validate_mongosave", "total_ui_properties": 2}, "status": "success"}
{"timestamp": "2025-06-23T07:36:16.796578", "endpoint": "parse-validate-mongosave/ui-properties", "input": {"natural_language": "Tenant: Alpha Corp\n\nEntity.Attribute | Control Type | Display Format | Input Mask | Placeholder Text | Auto Complete | Read Only | Validation Display | Help Text Position | Label | Required Indicator\n\nEmployee.totalLeaveEntitlement | number_input | ##.# days | ##.# | Enter entitlement | false | false | inline | below | Total Leave Entitlement | false", "tenant_id": "T1001", "edit_draft": false, "edit_temp": false, "edit_production": false}, "output": {"success": true, "ui_property_results": [{"success": true, "saved_data": {"ui_property_id": "UI1", "entity_id": "E13", "attribute_id": "E13.At11", "control_type": "number_input", "display_format": "##.# days", "input_mask": "##.#", "placeholder_text": "Enter entitlement", "auto_complete": false, "read_only": false, "validation_display": "inline", "help_text_position": "below", "label": "Total Leave Entitlement", "required_indicator": false, "natural_language": "Entity: Employee\nAttribute: totalLeaveEntitlement\nControl Type: number_input\nLabel: Total Leave Entitlement\nDisplay Format: ##.# days\nInput Mask: ##.#\nPlaceholder Text: Enter entitlement\nValidation Display: inline\nHelp Text Position: below", "version": 1, "status": "draft", "created_at": "2025-06-23T07:36:16.794690", "updated_at": "2025-06-23T07:36:16.794698", "created_by": "Tarun", "updated_by": "Tarun", "ui_property_status": "new", "changes_detected": [], "_id": "685903f05e423f11979d4093"}, "validation_result": {"structure_errors": [], "required_field_errors": [], "data_type_errors": [], "custom_errors": []}, "dependency_errors": [], "uniqueness_result": {"is_unique": true, "status": "unique", "message": "UI property with ui_property_id UI1 is unique"}}], "operation": "parse_validate_mongosave", "total_ui_properties": 1}, "status": "success"}
{"timestamp": "2025-06-23T07:39:09.605486", "endpoint": "parse-validate-mongosave/ui-properties", "input": {"natural_language": "Tenant: Acme Corp\n\nEntity.Attribute | Control Type | Display Format | Input Mask | Placeholder Text | Auto Complete | Read Only | Validation Display | Help Text Position | Label | Required Indicator\n\nLeaveApplication.leaveId | text_input | LV-YYYY-#### | LV-####-#### | Auto-generated | false | true | inline | below | Leave ID | false\n\nLeaveApplication.employeeId | dropdown | EMP### - {firstName} {lastName} | | Select Employee | true | false | inline | below | Employee | true\n\nLeaveApplication.startDate | date_picker | DD/MM/YYYY | DD/MM/YYYY | Select start date | false | false | inline | below | Start Date | true\n\nLeaveApplication.endDate | date_picker | DD/MM/YYYY | DD/MM/YYYY | Select end date | false | false | inline | below | End Date | true\n\nLeaveApplication.numDays | number_input | ### days | ### | Auto-calculated | false | true | inline | below | Number of Days | false\n\nLeaveApplication.reason | textarea | text | | Enter detailed reason for leave request | false | false | inline | below | Reason | true\n\nLeaveApplication.leaveTypeName | dropdown | {leaveTypeName} | | Select Leave Type | true | false | inline | below | Leave Type | true\n\nLeaveApplication.leaveSubTypeName | dropdown | {leaveSubTypeName} | | Select Leave Sub-Type | true | false | inline | below | Leave Sub-Type | false\n\nLeaveApplication.status | radio | {status} | | | false | true | inline | below | Status | false\n\nLeaveApplication.requiresDocumentation | checkbox | Yes/No | | | false | true | inline | below | Requires Documentation | false\n\nLeaveApplication.documentationProvided | file_upload | Upload Status | | Upload supporting documents | false | false | inline | below | Documentation | false\n\nLeaveApplication.submissionDate | text_input | DD/MM/YYYY HH:MM | | Auto-generated | false | true | inline | below | Submission Date | false\n\nLeaveApplication.approvalDate | text_input | DD/MM/YYYY HH:MM | | Set on approval | false | true | inline | below | Approval Date | false\n\nLeaveApplication.approvedBy | dropdown | {firstName} {lastName} ({employeeId}) | | Select Approver | true | false | inline | below | Approved By | false\n\nLeaveApplication.comments | textarea | text | | Enter manager comments | false | false | inline | below | Comments | false\n\nEmployee.employeeId | text_input | EMP### | EMP### | Auto-generated | false | true | inline | below | Employee ID | false\n\nEmployee.firstName | text_input | text | | Enter first name | false | false | inline | below | First Name | true\n\nEmployee.lastName | text_input | text | | Enter last name | false | false | inline | below | Last Name | true\n\nEmployee.email | text_input | <EMAIL> | | Enter email address | false | false | inline | below | Email | true\n\nEmployee.phone | text_input | +##-###-###-#### | +##-###-###-#### | Enter phone number | false | false | inline | below | Phone | false\n\nEmployee.departmentId | dropdown | {departmentName} | | Select Department | true | false | inline | below | Department | true\n\nEmployee.managerId | dropdown | {firstName} {lastName} ({employeeId}) | | Select Manager | true | false | inline | below | Manager | false\n\nEmployee.jobTitle | text_input | text | | Enter job title | true | false | inline | below | Job Title | true\n\nEmployee.hireDate | date_picker | DD/MM/YYYY | DD/MM/YYYY | Select hire date | false | false | inline | below | Hire Date | true\n\nEmployee.employmentStatus | dropdown | {status} | | Select Status | false | false | inline | below | Employment Status | true\n\nEmployee.annualLeaveBalance | number_input | ##.# days | ##.# | Enter balance | false | false | inline | below | Annual Leave Balance | false\n\nEmployee.sickLeaveBalance | number_input | ##.# days | ##.# | Enter balance | false | false | inline | below | Sick Leave Balance | false\n\nEmployee.personalLeaveBalance | number_input | ##.# days | ##.# | Enter balance | false | false | inline | below | Personal Leave Balance | false\n\nEmployee.totalLeaveEntitlement | number_input | ##.# days | ##.# | Enter entitlement | false | false | inline | below | Total Leave Entitlement | false\n\nLeaveType.leaveTypeId | text_input | LT### | LT### | Auto-generated | false | true | inline | below | Leave Type ID | false\n\nLeaveType.leaveTypeName | text_input | text | | Enter leave type name | false | false | inline | below | Leave Type Name | true\n\nLeaveType.description | textarea | text | | Enter description | false | false | inline | below | Description | false\n\nLeaveType.maxDaysPerYear | number_input | ### days | ### | Enter maximum days | false | false | inline | below | Max Days Per Year | true\n\nLeaveType.requiresDocumentation | toggle | Yes/No | | | false | false | inline | below | Requires Documentation | false\n\nLeaveType.advanceNoticeDays | number_input | ### days | ### | Enter notice days | false | false | inline | below | Advance Notice Days | false\n\nLeaveType.isCarryForward | toggle | Yes/No | | | false | false | inline | below | Is Carry Forward | false\n\nLeaveType.status | dropdown | {status} | | Select Status | false | false | inline | below | Status | false\n\nLeaveSubType.leaveSubTypeId | text_input | LST### | LST### | Auto-generated | false | true | inline | below | Leave Sub-Type ID | false\n\nLeaveSubType.leaveTypeName | dropdown | {leaveTypeName} | | Select Leave Type | true | false | inline | below | Leave Type | true\n\nLeaveSubType.leaveSubTypeName | text_input | text | | Enter sub-type name | false | false | inline | below | Leave Sub-Type Name | true\n\nLeaveSubType.description | textarea | text | | Enter description | false | false | inline | below | Description | false\n\nLeaveSubType.maxDaysPerRequest | number_input | ### days | ### | Enter maximum days | false | false | inline | below | Max Days Per Request | true\n\nLeaveSubType.requiresManagerApproval | toggle | Yes/No | | | false | false | inline | below | Requires Manager Approval | false\n\nLeaveSubType.status | dropdown | {status} | | Select Status | false | false | inline | below | Status | false\n\nConstants.constantId | text_input | CONST### | CONST### | Auto-generated | false | true | inline | below | Constant ID | false\n\nConstants.attribute | text_input | text | | Enter attribute name | false | false | inline | below | Attribute | true\n\nConstants.value | text_input | text | | Enter value | false | false | inline | below | Value | true\n\nConstants.description | textarea | text | | Enter description | false | false | inline | below | Description | false\n\nConstants.dataType | dropdown | {dataType} | | Select Data Type | false | false | inline | below | Data Type | true\n\nConstants.status | dropdown | {status} | | Select Status | false | false | inline | below | Status | false\n", "tenant_id": "T1001", "edit_draft": false, "edit_temp": false, "edit_production": false}, "output": {"success": true, "ui_property_results": [{"success": false, "errors": ["Entity with entity_id E_LeaveApplication_1750664347 does not exist", "Attribute with attribute_id A_E_LeaveApplication_1750664347_leaveId_1750664347 does not exist"], "parsed_data": {"ui_property_id": "UI1", "entity_id": "E_LeaveApplication_1750664347", "attribute_id": "A_E_LeaveApplication_1750664347_leaveId_1750664347", "control_type": "text_input", "display_format": "LV-YYYY-####", "input_mask": "LV-####-####", "placeholder_text": "Auto-generated", "auto_complete": false, "read_only": true, "validation_display": "inline", "help_text_position": "below", "label": "Leave ID", "required_indicator": false, "natural_language": "Entity: LeaveApplication\nAttribute: leaveId\nControl Type: text_input\nLabel: Leave ID\nDisplay Format: LV-YYYY-####\nInput Mask: LV-####-####\nPlaceholder Text: Auto-generated\nRead Only: true\nValidation Display: inline\nHelp Text Position: below", "version": 1, "status": "draft", "created_at": "2025-06-23T07:39:07.377732", "updated_at": "2025-06-23T07:39:07.377732", "created_by": "Tarun", "updated_by": "Tarun", "ui_property_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Entity with entity_id E_LeaveApplication_1750664347 does not exist", "Attribute with attribute_id A_E_LeaveApplication_1750664347_employeeId_1750664347 does not exist"], "parsed_data": {"ui_property_id": "UI1", "entity_id": "E_LeaveApplication_1750664347", "attribute_id": "A_E_LeaveApplication_1750664347_employeeId_1750664347", "control_type": "dropdown", "display_format": "EMP### - {firstName} {lastName}", "input_mask": "", "placeholder_text": "Select Employee", "auto_complete": true, "read_only": false, "validation_display": "inline", "help_text_position": "below", "label": "Employee", "required_indicator": true, "natural_language": "Entity: LeaveApplication\nAttribute: employeeId\nControl Type: dropdown\nLabel: Employee\nDisplay Format: EMP### - {firstName} {lastName}\nPlaceholder Text: Select Employee\nAuto Complete: true\nRequired Indicator: true\nValidation Display: inline\nHelp Text Position: below", "version": 1, "status": "draft", "created_at": "2025-06-23T07:39:07.410596", "updated_at": "2025-06-23T07:39:07.410596", "created_by": "Tarun", "updated_by": "Tarun", "ui_property_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Entity with entity_id E_LeaveApplication_1750664347 does not exist", "Attribute with attribute_id A_E_LeaveApplication_1750664347_startDate_1750664347 does not exist"], "parsed_data": {"ui_property_id": "UI1", "entity_id": "E_LeaveApplication_1750664347", "attribute_id": "A_E_LeaveApplication_1750664347_startDate_1750664347", "control_type": "date_picker", "display_format": "DD/MM/YYYY", "input_mask": "DD/MM/YYYY", "placeholder_text": "Select start date", "auto_complete": false, "read_only": false, "validation_display": "inline", "help_text_position": "below", "label": "Start Date", "required_indicator": true, "natural_language": "Entity: LeaveApplication\nAttribute: startDate\nControl Type: date_picker\nLabel: Start Date\nDisplay Format: DD/MM/YYYY\nInput Mask: DD/MM/YYYY\nPlaceholder Text: Select start date\nRequired Indicator: true\nValidation Display: inline\nHelp Text Position: below", "version": 1, "status": "draft", "created_at": "2025-06-23T07:39:07.442212", "updated_at": "2025-06-23T07:39:07.442212", "created_by": "Tarun", "updated_by": "Tarun", "ui_property_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Entity with entity_id E_LeaveApplication_1750664347 does not exist", "Attribute with attribute_id A_E_LeaveApplication_1750664347_endDate_1750664347 does not exist"], "parsed_data": {"ui_property_id": "UI1", "entity_id": "E_LeaveApplication_1750664347", "attribute_id": "A_E_LeaveApplication_1750664347_endDate_1750664347", "control_type": "date_picker", "display_format": "DD/MM/YYYY", "input_mask": "DD/MM/YYYY", "placeholder_text": "Select end date", "auto_complete": false, "read_only": false, "validation_display": "inline", "help_text_position": "below", "label": "End Date", "required_indicator": true, "natural_language": "Entity: LeaveApplication\nAttribute: endDate\nControl Type: date_picker\nLabel: End Date\nDisplay Format: DD/MM/YYYY\nInput Mask: DD/MM/YYYY\nPlaceholder Text: Select end date\nRequired Indicator: true\nValidation Display: inline\nHelp Text Position: below", "version": 1, "status": "draft", "created_at": "2025-06-23T07:39:07.475040", "updated_at": "2025-06-23T07:39:07.475040", "created_by": "Tarun", "updated_by": "Tarun", "ui_property_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Entity with entity_id E_LeaveApplication_1750664347 does not exist", "Attribute with attribute_id A_E_LeaveApplication_1750664347_numDays_1750664347 does not exist"], "parsed_data": {"ui_property_id": "UI1", "entity_id": "E_LeaveApplication_1750664347", "attribute_id": "A_E_LeaveApplication_1750664347_numDays_1750664347", "control_type": "number_input", "display_format": "### days", "input_mask": "###", "placeholder_text": "Auto-calculated", "auto_complete": false, "read_only": true, "validation_display": "inline", "help_text_position": "below", "label": "Number of Days", "required_indicator": false, "natural_language": "Entity: LeaveApplication\nAttribute: numDays\nControl Type: number_input\nLabel: Number of Days\nDisplay Format: ### days\nInput Mask: ###\nPlaceholder Text: Auto-calculated\nRead Only: true\nValidation Display: inline\nHelp Text Position: below", "version": 1, "status": "draft", "created_at": "2025-06-23T07:39:07.506808", "updated_at": "2025-06-23T07:39:07.506808", "created_by": "Tarun", "updated_by": "Tarun", "ui_property_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Entity with entity_id E_LeaveApplication_1750664347 does not exist", "Attribute with attribute_id A_E_LeaveApplication_1750664347_reason_1750664347 does not exist"], "parsed_data": {"ui_property_id": "UI1", "entity_id": "E_LeaveApplication_1750664347", "attribute_id": "A_E_LeaveApplication_1750664347_reason_1750664347", "control_type": "textarea", "display_format": "text", "input_mask": "", "placeholder_text": "Enter detailed reason for leave request", "auto_complete": false, "read_only": false, "validation_display": "inline", "help_text_position": "below", "label": "Reason", "required_indicator": true, "natural_language": "Entity: LeaveApplication\nAttribute: reason\nControl Type: textarea\nLabel: Reason\nDisplay Format: text\nPlaceholder Text: Enter detailed reason for leave request\nRequired Indicator: true\nValidation Display: inline\nHelp Text Position: below", "version": 1, "status": "draft", "created_at": "2025-06-23T07:39:07.539038", "updated_at": "2025-06-23T07:39:07.539038", "created_by": "Tarun", "updated_by": "Tarun", "ui_property_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Entity with entity_id E_LeaveApplication_1750664347 does not exist", "Attribute with attribute_id A_E_LeaveApplication_1750664347_leaveTypeName_1750664347 does not exist"], "parsed_data": {"ui_property_id": "UI1", "entity_id": "E_LeaveApplication_1750664347", "attribute_id": "A_E_LeaveApplication_1750664347_leaveTypeName_1750664347", "control_type": "dropdown", "display_format": "{leaveTypeName}", "input_mask": "", "placeholder_text": "Select Leave Type", "auto_complete": true, "read_only": false, "validation_display": "inline", "help_text_position": "below", "label": "Leave Type", "required_indicator": true, "natural_language": "Entity: LeaveApplication\nAttribute: leaveTypeName\nControl Type: dropdown\nLabel: Leave Type\nDisplay Format: {leaveTypeName}\nPlaceholder Text: Select Leave Type\nAuto Complete: true\nRequired Indicator: true\nValidation Display: inline\nHelp Text Position: below", "version": 1, "status": "draft", "created_at": "2025-06-23T07:39:07.574112", "updated_at": "2025-06-23T07:39:07.574112", "created_by": "Tarun", "updated_by": "Tarun", "ui_property_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Entity with entity_id E_LeaveApplication_1750664347 does not exist", "Attribute with attribute_id A_E_LeaveApplication_1750664347_leaveSubTypeName_1750664347 does not exist"], "parsed_data": {"ui_property_id": "UI1", "entity_id": "E_LeaveApplication_1750664347", "attribute_id": "A_E_LeaveApplication_1750664347_leaveSubTypeName_1750664347", "control_type": "dropdown", "display_format": "{leaveSubTypeName}", "input_mask": "", "placeholder_text": "Select Leave Sub-Type", "auto_complete": true, "read_only": false, "validation_display": "inline", "help_text_position": "below", "label": "Leave Sub-Type", "required_indicator": false, "natural_language": "Entity: LeaveApplication\nAttribute: leaveSubTypeName\nControl Type: dropdown\nLabel: Leave Sub-Type\nDisplay Format: {leaveSubTypeName}\nPlaceholder Text: Select Leave Sub-Type\nAuto Complete: true\nValidation Display: inline\nHelp Text Position: below", "version": 1, "status": "draft", "created_at": "2025-06-23T07:39:07.605908", "updated_at": "2025-06-23T07:39:07.605908", "created_by": "Tarun", "updated_by": "Tarun", "ui_property_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Entity with entity_id E_LeaveApplication_1750664347 does not exist", "Attribute with attribute_id A_E_LeaveApplication_1750664347_status_1750664347 does not exist"], "parsed_data": {"ui_property_id": "UI1", "entity_id": "E_LeaveApplication_1750664347", "attribute_id": "A_E_LeaveApplication_1750664347_status_1750664347", "control_type": "radio", "display_format": "{status}", "input_mask": "", "placeholder_text": "", "auto_complete": false, "read_only": true, "validation_display": "inline", "help_text_position": "below", "label": "Status", "required_indicator": false, "natural_language": "Entity: LeaveApplication\nAttribute: status\nControl Type: radio\nLabel: Status\nDisplay Format: {status}\nRead Only: true\nValidation Display: inline\nHelp Text Position: below", "version": 1, "status": "draft", "created_at": "2025-06-23T07:39:07.637087", "updated_at": "2025-06-23T07:39:07.637087", "created_by": "Tarun", "updated_by": "Tarun", "ui_property_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Entity with entity_id E_LeaveApplication_1750664347 does not exist", "Attribute with attribute_id A_E_LeaveApplication_1750664347_requiresDocumentation_1750664347 does not exist"], "parsed_data": {"ui_property_id": "UI1", "entity_id": "E_LeaveApplication_1750664347", "attribute_id": "A_E_LeaveApplication_1750664347_requiresDocumentation_1750664347", "control_type": "checkbox", "display_format": "Yes/No", "input_mask": "", "placeholder_text": "", "auto_complete": false, "read_only": true, "validation_display": "inline", "help_text_position": "below", "label": "Requires Documentation", "required_indicator": false, "natural_language": "Entity: LeaveApplication\nAttribute: requiresDocumentation\nControl Type: checkbox\nLabel: Requires Documentation\nDisplay Format: Yes/No\nRead Only: true\nValidation Display: inline\nHelp Text Position: below", "version": 1, "status": "draft", "created_at": "2025-06-23T07:39:07.662911", "updated_at": "2025-06-23T07:39:07.662911", "created_by": "Tarun", "updated_by": "Tarun", "ui_property_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Entity with entity_id E_LeaveApplication_1750664347 does not exist", "Attribute with attribute_id A_E_LeaveApplication_1750664347_documentationProvided_1750664347 does not exist"], "parsed_data": {"ui_property_id": "UI1", "entity_id": "E_LeaveApplication_1750664347", "attribute_id": "A_E_LeaveApplication_1750664347_documentationProvided_1750664347", "control_type": "file_upload", "display_format": "Upload Status", "input_mask": "", "placeholder_text": "Upload supporting documents", "auto_complete": false, "read_only": false, "validation_display": "inline", "help_text_position": "below", "label": "Documentation", "required_indicator": false, "natural_language": "Entity: LeaveApplication\nAttribute: documentationProvided\nControl Type: file_upload\nLabel: Documentation\nDisplay Format: Upload Status\nPlaceholder Text: Upload supporting documents\nValidation Display: inline\nHelp Text Position: below", "version": 1, "status": "draft", "created_at": "2025-06-23T07:39:07.689021", "updated_at": "2025-06-23T07:39:07.689021", "created_by": "Tarun", "updated_by": "Tarun", "ui_property_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Entity with entity_id E_LeaveApplication_1750664347 does not exist", "Attribute with attribute_id A_E_LeaveApplication_1750664347_submissionDate_1750664347 does not exist"], "parsed_data": {"ui_property_id": "UI1", "entity_id": "E_LeaveApplication_1750664347", "attribute_id": "A_E_LeaveApplication_1750664347_submissionDate_1750664347", "control_type": "text_input", "display_format": "DD/MM/YYYY HH:MM", "input_mask": "", "placeholder_text": "Auto-generated", "auto_complete": false, "read_only": true, "validation_display": "inline", "help_text_position": "below", "label": "Submission Date", "required_indicator": false, "natural_language": "Entity: LeaveApplication\nAttribute: submissionDate\nControl Type: text_input\nLabel: Submission Date\nDisplay Format: DD/MM/YYYY HH:MM\nPlaceholder Text: Auto-generated\nRead Only: true\nValidation Display: inline\nHelp Text Position: below", "version": 1, "status": "draft", "created_at": "2025-06-23T07:39:07.714219", "updated_at": "2025-06-23T07:39:07.714219", "created_by": "Tarun", "updated_by": "Tarun", "ui_property_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Entity with entity_id E_LeaveApplication_1750664347 does not exist", "Attribute with attribute_id A_E_LeaveApplication_1750664347_approvalDate_1750664347 does not exist"], "parsed_data": {"ui_property_id": "UI1", "entity_id": "E_LeaveApplication_1750664347", "attribute_id": "A_E_LeaveApplication_1750664347_approvalDate_1750664347", "control_type": "text_input", "display_format": "DD/MM/YYYY HH:MM", "input_mask": "", "placeholder_text": "Set on approval", "auto_complete": false, "read_only": true, "validation_display": "inline", "help_text_position": "below", "label": "Approval Date", "required_indicator": false, "natural_language": "Entity: LeaveApplication\nAttribute: approvalDate\nControl Type: text_input\nLabel: Approval Date\nDisplay Format: DD/MM/YYYY HH:MM\nPlaceholder Text: Set on approval\nRead Only: true\nValidation Display: inline\nHelp Text Position: below", "version": 1, "status": "draft", "created_at": "2025-06-23T07:39:07.745762", "updated_at": "2025-06-23T07:39:07.745762", "created_by": "Tarun", "updated_by": "Tarun", "ui_property_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Entity with entity_id E_LeaveApplication_1750664347 does not exist", "Attribute with attribute_id A_E_LeaveApplication_1750664347_approvedBy_1750664347 does not exist"], "parsed_data": {"ui_property_id": "UI1", "entity_id": "E_LeaveApplication_1750664347", "attribute_id": "A_E_LeaveApplication_1750664347_approvedBy_1750664347", "control_type": "dropdown", "display_format": "{firstName} {lastName} ({employeeId})", "input_mask": "", "placeholder_text": "Select Approver", "auto_complete": true, "read_only": false, "validation_display": "inline", "help_text_position": "below", "label": "Approved By", "required_indicator": false, "natural_language": "Entity: LeaveApplication\nAttribute: approvedBy\nControl Type: dropdown\nLabel: Approved By\nDisplay Format: {firstName} {lastName} ({employeeId})\nPlaceholder Text: Select Approver\nAuto Complete: true\nValidation Display: inline\nHelp Text Position: below", "version": 1, "status": "draft", "created_at": "2025-06-23T07:39:07.773836", "updated_at": "2025-06-23T07:39:07.773836", "created_by": "Tarun", "updated_by": "Tarun", "ui_property_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Entity with entity_id E_LeaveApplication_1750664347 does not exist", "Attribute with attribute_id A_E_LeaveApplication_1750664347_comments_1750664347 does not exist"], "parsed_data": {"ui_property_id": "UI1", "entity_id": "E_LeaveApplication_1750664347", "attribute_id": "A_E_LeaveApplication_1750664347_comments_1750664347", "control_type": "textarea", "display_format": "text", "input_mask": "", "placeholder_text": "Enter manager comments", "auto_complete": false, "read_only": false, "validation_display": "inline", "help_text_position": "below", "label": "Comments", "required_indicator": false, "natural_language": "Entity: LeaveApplication\nAttribute: comments\nControl Type: textarea\nLabel: Comments\nDisplay Format: text\nPlaceholder Text: Enter manager comments\nValidation Display: inline\nHelp Text Position: below", "version": 1, "status": "draft", "created_at": "2025-06-23T07:39:07.803793", "updated_at": "2025-06-23T07:39:07.803793", "created_by": "Tarun", "updated_by": "Tarun", "ui_property_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Attribute with attribute_id A_E13_employeeId_1750664347 does not exist"], "parsed_data": {"ui_property_id": "UI1", "entity_id": "E13", "attribute_id": "A_E13_employeeId_1750664347", "control_type": "text_input", "display_format": "EMP###", "input_mask": "EMP###", "placeholder_text": "Auto-generated", "auto_complete": false, "read_only": true, "validation_display": "inline", "help_text_position": "below", "label": "Employee ID", "required_indicator": false, "natural_language": "Entity: Employee\nAttribute: employeeId\nControl Type: text_input\nLabel: Employee ID\nDisplay Format: EMP###\nInput Mask: EMP###\nPlaceholder Text: Auto-generated\nRead Only: true\nValidation Display: inline\nHelp Text Position: below", "version": 1, "status": "draft", "created_at": "2025-06-23T07:39:07.825671", "updated_at": "2025-06-23T07:39:07.825671", "created_by": "Tarun", "updated_by": "Tarun", "ui_property_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Attribute with attribute_id A_E13_firstName_1750664347 does not exist"], "parsed_data": {"ui_property_id": "UI1", "entity_id": "E13", "attribute_id": "A_E13_firstName_1750664347", "control_type": "text_input", "display_format": "text", "input_mask": "", "placeholder_text": "Enter first name", "auto_complete": false, "read_only": false, "validation_display": "inline", "help_text_position": "below", "label": "First Name", "required_indicator": true, "natural_language": "Entity: Employee\nAttribute: firstName\nControl Type: text_input\nLabel: First Name\nDisplay Format: text\nPlaceholder Text: Enter first name\nRequired Indicator: true\nValidation Display: inline\nHelp Text Position: below", "version": 1, "status": "draft", "created_at": "2025-06-23T07:39:07.849600", "updated_at": "2025-06-23T07:39:07.849600", "created_by": "Tarun", "updated_by": "Tarun", "ui_property_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Attribute with attribute_id A_E13_lastName_1750664347 does not exist"], "parsed_data": {"ui_property_id": "UI1", "entity_id": "E13", "attribute_id": "A_E13_lastName_1750664347", "control_type": "text_input", "display_format": "text", "input_mask": "", "placeholder_text": "Enter last name", "auto_complete": false, "read_only": false, "validation_display": "inline", "help_text_position": "below", "label": "Last Name", "required_indicator": true, "natural_language": "Entity: Employee\nAttribute: lastName\nControl Type: text_input\nLabel: Last Name\nDisplay Format: text\nPlaceholder Text: Enter last name\nRequired Indicator: true\nValidation Display: inline\nHelp Text Position: below", "version": 1, "status": "draft", "created_at": "2025-06-23T07:39:07.873430", "updated_at": "2025-06-23T07:39:07.873430", "created_by": "Tarun", "updated_by": "Tarun", "ui_property_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Attribute with attribute_id A_E13_email_1750664347 does not exist"], "parsed_data": {"ui_property_id": "UI1", "entity_id": "E13", "attribute_id": "A_E13_email_1750664347", "control_type": "text_input", "display_format": "<EMAIL>", "input_mask": "", "placeholder_text": "Enter email address", "auto_complete": false, "read_only": false, "validation_display": "inline", "help_text_position": "below", "label": "Email", "required_indicator": true, "natural_language": "Entity: Employee\nAttribute: email\nControl Type: text_input\nLabel: Email\nDisplay Format: <EMAIL>\nPlaceholder Text: Enter email address\nRequired Indicator: true\nValidation Display: inline\nHelp Text Position: below", "version": 1, "status": "draft", "created_at": "2025-06-23T07:39:07.896192", "updated_at": "2025-06-23T07:39:07.896192", "created_by": "Tarun", "updated_by": "Tarun", "ui_property_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Attribute with attribute_id A_E13_phone_1750664347 does not exist"], "parsed_data": {"ui_property_id": "UI1", "entity_id": "E13", "attribute_id": "A_E13_phone_1750664347", "control_type": "text_input", "display_format": "+##-###-###-####", "input_mask": "+##-###-###-####", "placeholder_text": "Enter phone number", "auto_complete": false, "read_only": false, "validation_display": "inline", "help_text_position": "below", "label": "Phone", "required_indicator": false, "natural_language": "Entity: Employee\nAttribute: phone\nControl Type: text_input\nLabel: Phone\nDisplay Format: +##-###-###-####\nInput Mask: +##-###-###-####\nPlaceholder Text: Enter phone number\nValidation Display: inline\nHelp Text Position: below", "version": 1, "status": "draft", "created_at": "2025-06-23T07:39:07.916232", "updated_at": "2025-06-23T07:39:07.916232", "created_by": "Tarun", "updated_by": "Tarun", "ui_property_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Attribute with attribute_id A_E13_departmentId_1750664347 does not exist"], "parsed_data": {"ui_property_id": "UI1", "entity_id": "E13", "attribute_id": "A_E13_departmentId_1750664347", "control_type": "dropdown", "display_format": "{departmentName}", "input_mask": "", "placeholder_text": "Select Department", "auto_complete": true, "read_only": false, "validation_display": "inline", "help_text_position": "below", "label": "Department", "required_indicator": true, "natural_language": "Entity: Employee\nAttribute: departmentId\nControl Type: dropdown\nLabel: Department\nDisplay Format: {departmentName}\nPlaceholder Text: Select Department\nAuto Complete: true\nRequired Indicator: true\nValidation Display: inline\nHelp Text Position: below", "version": 1, "status": "draft", "created_at": "2025-06-23T07:39:07.935422", "updated_at": "2025-06-23T07:39:07.935422", "created_by": "Tarun", "updated_by": "Tarun", "ui_property_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Attribute with attribute_id A_E13_managerId_1750664347 does not exist"], "parsed_data": {"ui_property_id": "UI1", "entity_id": "E13", "attribute_id": "A_E13_managerId_1750664347", "control_type": "dropdown", "display_format": "{firstName} {lastName} ({employeeId})", "input_mask": "", "placeholder_text": "Select Manager", "auto_complete": true, "read_only": false, "validation_display": "inline", "help_text_position": "below", "label": "Manager", "required_indicator": false, "natural_language": "Entity: Employee\nAttribute: managerId\nControl Type: dropdown\nLabel: Manager\nDisplay Format: {firstName} {lastName} ({employeeId})\nPlaceholder Text: Select Manager\nAuto Complete: true\nValidation Display: inline\nHelp Text Position: below", "version": 1, "status": "draft", "created_at": "2025-06-23T07:39:07.955999", "updated_at": "2025-06-23T07:39:07.955999", "created_by": "Tarun", "updated_by": "Tarun", "ui_property_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Attribute with attribute_id A_E13_jobTitle_1750664347 does not exist"], "parsed_data": {"ui_property_id": "UI1", "entity_id": "E13", "attribute_id": "A_E13_jobTitle_1750664347", "control_type": "text_input", "display_format": "text", "input_mask": "", "placeholder_text": "Enter job title", "auto_complete": true, "read_only": false, "validation_display": "inline", "help_text_position": "below", "label": "Job Title", "required_indicator": true, "natural_language": "Entity: Employee\nAttribute: jobTitle\nControl Type: text_input\nLabel: Job Title\nDisplay Format: text\nPlaceholder Text: Enter job title\nAuto Complete: true\nRequired Indicator: true\nValidation Display: inline\nHelp Text Position: below", "version": 1, "status": "draft", "created_at": "2025-06-23T07:39:07.975667", "updated_at": "2025-06-23T07:39:07.975667", "created_by": "Tarun", "updated_by": "Tarun", "ui_property_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Attribute with attribute_id A_E13_hireDate_1750664347 does not exist"], "parsed_data": {"ui_property_id": "UI1", "entity_id": "E13", "attribute_id": "A_E13_hireDate_1750664347", "control_type": "date_picker", "display_format": "DD/MM/YYYY", "input_mask": "DD/MM/YYYY", "placeholder_text": "Select hire date", "auto_complete": false, "read_only": false, "validation_display": "inline", "help_text_position": "below", "label": "Hire Date", "required_indicator": true, "natural_language": "Entity: Employee\nAttribute: hireDate\nControl Type: date_picker\nLabel: Hire Date\nDisplay Format: DD/MM/YYYY\nInput Mask: DD/MM/YYYY\nPlaceholder Text: Select hire date\nRequired Indicator: true\nValidation Display: inline\nHelp Text Position: below", "version": 1, "status": "draft", "created_at": "2025-06-23T07:39:07.996222", "updated_at": "2025-06-23T07:39:07.996222", "created_by": "Tarun", "updated_by": "Tarun", "ui_property_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Attribute with attribute_id A_E13_employmentStatus_1750664348 does not exist"], "parsed_data": {"ui_property_id": "UI1", "entity_id": "E13", "attribute_id": "A_E13_employmentStatus_1750664348", "control_type": "dropdown", "display_format": "{status}", "input_mask": "", "placeholder_text": "Select Status", "auto_complete": false, "read_only": false, "validation_display": "inline", "help_text_position": "below", "label": "Employment Status", "required_indicator": true, "natural_language": "Entity: Employee\nAttribute: employmentStatus\nControl Type: dropdown\nLabel: Employment Status\nDisplay Format: {status}\nPlaceholder Text: Select Status\nRequired Indicator: true\nValidation Display: inline\nHelp Text Position: below", "version": 1, "status": "draft", "created_at": "2025-06-23T07:39:08.016649", "updated_at": "2025-06-23T07:39:08.016649", "created_by": "Tarun", "updated_by": "Tarun", "ui_property_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Attribute with attribute_id A_E13_annualLeaveBalance_1750664348 does not exist"], "parsed_data": {"ui_property_id": "UI1", "entity_id": "E13", "attribute_id": "A_E13_annualLeaveBalance_1750664348", "control_type": "number_input", "display_format": "##.# days", "input_mask": "##.#", "placeholder_text": "Enter balance", "auto_complete": false, "read_only": false, "validation_display": "inline", "help_text_position": "below", "label": "Annual Leave Balance", "required_indicator": false, "natural_language": "Entity: Employee\nAttribute: annualLeaveBalance\nControl Type: number_input\nLabel: Annual Leave Balance\nDisplay Format: ##.# days\nInput Mask: ##.#\nPlaceholder Text: Enter balance\nValidation Display: inline\nHelp Text Position: below", "version": 1, "status": "draft", "created_at": "2025-06-23T07:39:08.037954", "updated_at": "2025-06-23T07:39:08.037954", "created_by": "Tarun", "updated_by": "Tarun", "ui_property_status": "new", "changes_detected": []}}, {"success": false, "error": "Item already exists in draft. Use edit_draft=true to update.", "existing_in": "mongo", "existing_document": {"_id": "685903f05e423f11979d4093", "ui_property_id": "UI1", "entity_id": "E13", "attribute_id": "E13.At11", "control_type": "number_input", "display_format": "##.# days", "input_mask": "##.#", "placeholder_text": "Enter entitlement", "auto_complete": false, "read_only": false, "validation_display": "inline", "help_text_position": "below", "label": "Total Leave Entitlement", "required_indicator": false, "natural_language": "Entity: Employee\nAttribute: totalLeaveEntitlement\nControl Type: number_input\nLabel: Total Leave Entitlement\nDisplay Format: ##.# days\nInput Mask: ##.#\nPlaceholder Text: Enter entitlement\nValidation Display: inline\nHelp Text Position: below", "version": 1, "status": "draft", "created_at": "2025-06-23T07:36:16.794690", "updated_at": "2025-06-23T07:36:16.794698", "created_by": "Tarun", "updated_by": "Tarun", "ui_property_status": "new", "changes_detected": []}, "parsed_data": {"ui_property_id": "UI1", "entity_id": "E13", "attribute_id": "E13.At9", "control_type": "number_input", "display_format": "##.# days", "input_mask": "##.#", "placeholder_text": "Enter balance", "auto_complete": false, "read_only": false, "validation_display": "inline", "help_text_position": "below", "label": "Sick Leave Balance", "required_indicator": false, "natural_language": "Entity: Employee\nAttribute: sickLeaveBalance\nControl Type: number_input\nLabel: Sick Leave Balance\nDisplay Format: ##.# days\nInput Mask: ##.#\nPlaceholder Text: Enter balance\nValidation Display: inline\nHelp Text Position: below", "version": 1, "status": "draft", "created_at": "2025-06-23T07:39:08.053723", "updated_at": "2025-06-23T07:39:08.053723", "created_by": "Tarun", "updated_by": "Tarun", "ui_property_status": "new", "changes_detected": []}}, {"success": false, "error": "Item already exists in draft. Use edit_draft=true to update.", "existing_in": "mongo", "existing_document": {"_id": "685903f05e423f11979d4093", "ui_property_id": "UI1", "entity_id": "E13", "attribute_id": "E13.At11", "control_type": "number_input", "display_format": "##.# days", "input_mask": "##.#", "placeholder_text": "Enter entitlement", "auto_complete": false, "read_only": false, "validation_display": "inline", "help_text_position": "below", "label": "Total Leave Entitlement", "required_indicator": false, "natural_language": "Entity: Employee\nAttribute: totalLeaveEntitlement\nControl Type: number_input\nLabel: Total Leave Entitlement\nDisplay Format: ##.# days\nInput Mask: ##.#\nPlaceholder Text: Enter entitlement\nValidation Display: inline\nHelp Text Position: below", "version": 1, "status": "draft", "created_at": "2025-06-23T07:36:16.794690", "updated_at": "2025-06-23T07:36:16.794698", "created_by": "Tarun", "updated_by": "Tarun", "ui_property_status": "new", "changes_detected": []}, "parsed_data": {"ui_property_id": "UI1", "entity_id": "E13", "attribute_id": "E13.At10", "control_type": "number_input", "display_format": "##.# days", "input_mask": "##.#", "placeholder_text": "Enter balance", "auto_complete": false, "read_only": false, "validation_display": "inline", "help_text_position": "below", "label": "Personal Leave Balance", "required_indicator": false, "natural_language": "Entity: Employee\nAttribute: personalLeaveBalance\nControl Type: number_input\nLabel: Personal Leave Balance\nDisplay Format: ##.# days\nInput Mask: ##.#\nPlaceholder Text: Enter balance\nValidation Display: inline\nHelp Text Position: below", "version": 1, "status": "draft", "created_at": "2025-06-23T07:39:08.067543", "updated_at": "2025-06-23T07:39:08.067543", "created_by": "Tarun", "updated_by": "Tarun", "ui_property_status": "new", "changes_detected": []}}, {"success": false, "error": "Item already exists in draft. Use edit_draft=true to update.", "existing_in": "mongo", "existing_document": {"_id": "685903f05e423f11979d4093", "ui_property_id": "UI1", "entity_id": "E13", "attribute_id": "E13.At11", "control_type": "number_input", "display_format": "##.# days", "input_mask": "##.#", "placeholder_text": "Enter entitlement", "auto_complete": false, "read_only": false, "validation_display": "inline", "help_text_position": "below", "label": "Total Leave Entitlement", "required_indicator": false, "natural_language": "Entity: Employee\nAttribute: totalLeaveEntitlement\nControl Type: number_input\nLabel: Total Leave Entitlement\nDisplay Format: ##.# days\nInput Mask: ##.#\nPlaceholder Text: Enter entitlement\nValidation Display: inline\nHelp Text Position: below", "version": 1, "status": "draft", "created_at": "2025-06-23T07:36:16.794690", "updated_at": "2025-06-23T07:36:16.794698", "created_by": "Tarun", "updated_by": "Tarun", "ui_property_status": "new", "changes_detected": []}, "parsed_data": {"ui_property_id": "UI1", "entity_id": "E13", "attribute_id": "E13.At11", "control_type": "number_input", "display_format": "##.# days", "input_mask": "##.#", "placeholder_text": "Enter entitlement", "auto_complete": false, "read_only": false, "validation_display": "inline", "help_text_position": "below", "label": "Total Leave Entitlement", "required_indicator": false, "natural_language": "Entity: Employee\nAttribute: totalLeaveEntitlement\nControl Type: number_input\nLabel: Total Leave Entitlement\nDisplay Format: ##.# days\nInput Mask: ##.#\nPlaceholder Text: Enter entitlement\nValidation Display: inline\nHelp Text Position: below", "version": 1, "status": "draft", "created_at": "2025-06-23T07:39:08.075211", "updated_at": "2025-06-23T07:39:08.075211", "created_by": "Tarun", "updated_by": "Tarun", "ui_property_status": "existing", "changes_detected": []}}, {"success": false, "errors": ["Entity with entity_id E_LeaveType_1750664348 does not exist", "Attribute with attribute_id A_E_LeaveType_1750664348_leaveTypeId_1750664348 does not exist"], "parsed_data": {"ui_property_id": "UI1", "entity_id": "E_LeaveType_1750664348", "attribute_id": "A_E_LeaveType_1750664348_leaveTypeId_1750664348", "control_type": "text_input", "display_format": "LT###", "input_mask": "LT###", "placeholder_text": "Auto-generated", "auto_complete": false, "read_only": true, "validation_display": "inline", "help_text_position": "below", "label": "Leave Type ID", "required_indicator": false, "natural_language": "Entity: LeaveType\nAttribute: leaveTypeId\nControl Type: text_input\nLabel: Leave Type ID\nDisplay Format: LT###\nInput Mask: LT###\nPlaceholder Text: Auto-generated\nRead Only: true\nValidation Display: inline\nHelp Text Position: below", "version": 1, "status": "draft", "created_at": "2025-06-23T07:39:08.097741", "updated_at": "2025-06-23T07:39:08.097741", "created_by": "Tarun", "updated_by": "Tarun", "ui_property_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Entity with entity_id E_LeaveType_1750664348 does not exist", "Attribute with attribute_id A_E_LeaveType_1750664348_leaveTypeName_1750664348 does not exist"], "parsed_data": {"ui_property_id": "UI1", "entity_id": "E_LeaveType_1750664348", "attribute_id": "A_E_LeaveType_1750664348_leaveTypeName_1750664348", "control_type": "text_input", "display_format": "text", "input_mask": "", "placeholder_text": "Enter leave type name", "auto_complete": false, "read_only": false, "validation_display": "inline", "help_text_position": "below", "label": "Leave Type Name", "required_indicator": true, "natural_language": "Entity: LeaveType\nAttribute: leaveTypeName\nControl Type: text_input\nLabel: Leave Type Name\nDisplay Format: text\nPlaceholder Text: Enter leave type name\nRequired Indicator: true\nValidation Display: inline\nHelp Text Position: below", "version": 1, "status": "draft", "created_at": "2025-06-23T07:39:08.122731", "updated_at": "2025-06-23T07:39:08.122731", "created_by": "Tarun", "updated_by": "Tarun", "ui_property_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Entity with entity_id E_LeaveType_1750664348 does not exist", "Attribute with attribute_id A_E_LeaveType_1750664348_description_1750664348 does not exist"], "parsed_data": {"ui_property_id": "UI1", "entity_id": "E_LeaveType_1750664348", "attribute_id": "A_E_LeaveType_1750664348_description_1750664348", "control_type": "textarea", "display_format": "text", "input_mask": "", "placeholder_text": "Enter description", "auto_complete": false, "read_only": false, "validation_display": "inline", "help_text_position": "below", "label": "Description", "required_indicator": false, "natural_language": "Entity: LeaveType\nAttribute: description\nControl Type: textarea\nLabel: Description\nDisplay Format: text\nPlaceholder Text: Enter description\nValidation Display: inline\nHelp Text Position: below", "version": 1, "status": "draft", "created_at": "2025-06-23T07:39:08.146911", "updated_at": "2025-06-23T07:39:08.146911", "created_by": "Tarun", "updated_by": "Tarun", "ui_property_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Entity with entity_id E_LeaveType_1750664348 does not exist", "Attribute with attribute_id A_E_LeaveType_1750664348_maxDaysPerYear_1750664348 does not exist"], "parsed_data": {"ui_property_id": "UI1", "entity_id": "E_LeaveType_1750664348", "attribute_id": "A_E_LeaveType_1750664348_maxDaysPerYear_1750664348", "control_type": "number_input", "display_format": "### days", "input_mask": "###", "placeholder_text": "Enter maximum days", "auto_complete": false, "read_only": false, "validation_display": "inline", "help_text_position": "below", "label": "Max Days Per Year", "required_indicator": true, "natural_language": "Entity: LeaveType\nAttribute: maxDaysPerYear\nControl Type: number_input\nLabel: Max Days Per Year\nDisplay Format: ### days\nInput Mask: ###\nPlaceholder Text: Enter maximum days\nRequired Indicator: true\nValidation Display: inline\nHelp Text Position: below", "version": 1, "status": "draft", "created_at": "2025-06-23T07:39:08.173000", "updated_at": "2025-06-23T07:39:08.173000", "created_by": "Tarun", "updated_by": "Tarun", "ui_property_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Entity with entity_id E_LeaveType_1750664348 does not exist", "Attribute with attribute_id A_E_LeaveType_1750664348_requiresDocumentation_1750664348 does not exist"], "parsed_data": {"ui_property_id": "UI1", "entity_id": "E_LeaveType_1750664348", "attribute_id": "A_E_LeaveType_1750664348_requiresDocumentation_1750664348", "control_type": "toggle", "display_format": "Yes/No", "input_mask": "", "placeholder_text": "", "auto_complete": false, "read_only": false, "validation_display": "inline", "help_text_position": "below", "label": "Requires Documentation", "required_indicator": false, "natural_language": "Entity: LeaveType\nAttribute: requiresDocumentation\nControl Type: toggle\nLabel: Requires Documentation\nDisplay Format: Yes/No\nValidation Display: inline\nHelp Text Position: below", "version": 1, "status": "draft", "created_at": "2025-06-23T07:39:08.197531", "updated_at": "2025-06-23T07:39:08.197531", "created_by": "Tarun", "updated_by": "Tarun", "ui_property_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Entity with entity_id E_LeaveType_1750664348 does not exist", "Attribute with attribute_id A_E_LeaveType_1750664348_advanceNoticeDays_1750664348 does not exist"], "parsed_data": {"ui_property_id": "UI1", "entity_id": "E_LeaveType_1750664348", "attribute_id": "A_E_LeaveType_1750664348_advanceNoticeDays_1750664348", "control_type": "number_input", "display_format": "### days", "input_mask": "###", "placeholder_text": "Enter notice days", "auto_complete": false, "read_only": false, "validation_display": "inline", "help_text_position": "below", "label": "Advance Notice Days", "required_indicator": false, "natural_language": "Entity: LeaveType\nAttribute: advanceNoticeDays\nControl Type: number_input\nLabel: Advance Notice Days\nDisplay Format: ### days\nInput Mask: ###\nPlaceholder Text: Enter notice days\nValidation Display: inline\nHelp Text Position: below", "version": 1, "status": "draft", "created_at": "2025-06-23T07:39:08.224255", "updated_at": "2025-06-23T07:39:08.224255", "created_by": "Tarun", "updated_by": "Tarun", "ui_property_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Entity with entity_id E_LeaveType_1750664348 does not exist", "Attribute with attribute_id A_E_LeaveType_1750664348_isCarryForward_1750664348 does not exist"], "parsed_data": {"ui_property_id": "UI1", "entity_id": "E_LeaveType_1750664348", "attribute_id": "A_E_LeaveType_1750664348_isCarryForward_1750664348", "control_type": "toggle", "display_format": "Yes/No", "input_mask": "", "placeholder_text": "", "auto_complete": false, "read_only": false, "validation_display": "inline", "help_text_position": "below", "label": "Is Carry Forward", "required_indicator": false, "natural_language": "Entity: LeaveType\nAttribute: isCarryForward\nControl Type: toggle\nLabel: Is Carry Forward\nDisplay Format: Yes/No\nValidation Display: inline\nHelp Text Position: below", "version": 1, "status": "draft", "created_at": "2025-06-23T07:39:08.251196", "updated_at": "2025-06-23T07:39:08.251196", "created_by": "Tarun", "updated_by": "Tarun", "ui_property_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Entity with entity_id E_LeaveType_1750664348 does not exist", "Attribute with attribute_id A_E_LeaveType_1750664348_status_1750664348 does not exist"], "parsed_data": {"ui_property_id": "UI1", "entity_id": "E_LeaveType_1750664348", "attribute_id": "A_E_LeaveType_1750664348_status_1750664348", "control_type": "dropdown", "display_format": "{status}", "input_mask": "", "placeholder_text": "Select Status", "auto_complete": false, "read_only": false, "validation_display": "inline", "help_text_position": "below", "label": "Status", "required_indicator": false, "natural_language": "Entity: LeaveType\nAttribute: status\nControl Type: dropdown\nLabel: Status\nDisplay Format: {status}\nPlaceholder Text: Select Status\nValidation Display: inline\nHelp Text Position: below", "version": 1, "status": "draft", "created_at": "2025-06-23T07:39:08.277864", "updated_at": "2025-06-23T07:39:08.277864", "created_by": "Tarun", "updated_by": "Tarun", "ui_property_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Entity with entity_id E_LeaveSubType_1750664348 does not exist", "Attribute with attribute_id A_E_LeaveSubType_1750664348_leaveSubTypeId_1750664348 does not exist"], "parsed_data": {"ui_property_id": "UI1", "entity_id": "E_LeaveSubType_1750664348", "attribute_id": "A_E_LeaveSubType_1750664348_leaveSubTypeId_1750664348", "control_type": "text_input", "display_format": "LST###", "input_mask": "LST###", "placeholder_text": "Auto-generated", "auto_complete": false, "read_only": true, "validation_display": "inline", "help_text_position": "below", "label": "Leave Sub-Type ID", "required_indicator": false, "natural_language": "Entity: LeaveSubType\nAttribute: leaveSubTypeId\nControl Type: text_input\nLabel: Leave Sub-Type ID\nDisplay Format: LST###\nInput Mask: LST###\nPlaceholder Text: Auto-generated\nRead Only: true\nValidation Display: inline\nHelp Text Position: below", "version": 1, "status": "draft", "created_at": "2025-06-23T07:39:08.305175", "updated_at": "2025-06-23T07:39:08.305175", "created_by": "Tarun", "updated_by": "Tarun", "ui_property_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Entity with entity_id E_LeaveSubType_1750664348 does not exist", "Attribute with attribute_id A_E_LeaveSubType_1750664348_leaveTypeName_1750664348 does not exist"], "parsed_data": {"ui_property_id": "UI1", "entity_id": "E_LeaveSubType_1750664348", "attribute_id": "A_E_LeaveSubType_1750664348_leaveTypeName_1750664348", "control_type": "dropdown", "display_format": "{leaveTypeName}", "input_mask": "", "placeholder_text": "Select Leave Type", "auto_complete": true, "read_only": false, "validation_display": "inline", "help_text_position": "below", "label": "Leave Type", "required_indicator": true, "natural_language": "Entity: LeaveSubType\nAttribute: leaveTypeName\nControl Type: dropdown\nLabel: Leave Type\nDisplay Format: {leaveTypeName}\nPlaceholder Text: Select Leave Type\nAuto Complete: true\nRequired Indicator: true\nValidation Display: inline\nHelp Text Position: below", "version": 1, "status": "draft", "created_at": "2025-06-23T07:39:08.333551", "updated_at": "2025-06-23T07:39:08.333551", "created_by": "Tarun", "updated_by": "Tarun", "ui_property_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Entity with entity_id E_LeaveSubType_1750664348 does not exist", "Attribute with attribute_id A_E_LeaveSubType_1750664348_leaveSubTypeName_1750664348 does not exist"], "parsed_data": {"ui_property_id": "UI1", "entity_id": "E_LeaveSubType_1750664348", "attribute_id": "A_E_LeaveSubType_1750664348_leaveSubTypeName_1750664348", "control_type": "text_input", "display_format": "text", "input_mask": "", "placeholder_text": "Enter sub-type name", "auto_complete": false, "read_only": false, "validation_display": "inline", "help_text_position": "below", "label": "Leave Sub-Type Name", "required_indicator": true, "natural_language": "Entity: LeaveSubType\nAttribute: leaveSubTypeName\nControl Type: text_input\nLabel: Leave Sub-Type Name\nDisplay Format: text\nPlaceholder Text: Enter sub-type name\nRequired Indicator: true\nValidation Display: inline\nHelp Text Position: below", "version": 1, "status": "draft", "created_at": "2025-06-23T07:39:08.361301", "updated_at": "2025-06-23T07:39:08.361301", "created_by": "Tarun", "updated_by": "Tarun", "ui_property_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Entity with entity_id E_LeaveSubType_1750664348 does not exist", "Attribute with attribute_id A_E_LeaveSubType_1750664348_description_1750664348 does not exist"], "parsed_data": {"ui_property_id": "UI1", "entity_id": "E_LeaveSubType_1750664348", "attribute_id": "A_E_LeaveSubType_1750664348_description_1750664348", "control_type": "textarea", "display_format": "text", "input_mask": "", "placeholder_text": "Enter description", "auto_complete": false, "read_only": false, "validation_display": "inline", "help_text_position": "below", "label": "Description", "required_indicator": false, "natural_language": "Entity: LeaveSubType\nAttribute: description\nControl Type: textarea\nLabel: Description\nDisplay Format: text\nPlaceholder Text: Enter description\nValidation Display: inline\nHelp Text Position: below", "version": 1, "status": "draft", "created_at": "2025-06-23T07:39:08.386699", "updated_at": "2025-06-23T07:39:08.386699", "created_by": "Tarun", "updated_by": "Tarun", "ui_property_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Entity with entity_id E_LeaveSubType_1750664348 does not exist", "Attribute with attribute_id A_E_LeaveSubType_1750664348_maxDaysPerRequest_1750664348 does not exist"], "parsed_data": {"ui_property_id": "UI1", "entity_id": "E_LeaveSubType_1750664348", "attribute_id": "A_E_LeaveSubType_1750664348_maxDaysPerRequest_1750664348", "control_type": "number_input", "display_format": "### days", "input_mask": "###", "placeholder_text": "Enter maximum days", "auto_complete": false, "read_only": false, "validation_display": "inline", "help_text_position": "below", "label": "Max Days Per Request", "required_indicator": true, "natural_language": "Entity: LeaveSubType\nAttribute: maxDaysPerRequest\nControl Type: number_input\nLabel: Max Days Per Request\nDisplay Format: ### days\nInput Mask: ###\nPlaceholder Text: Enter maximum days\nRequired Indicator: true\nValidation Display: inline\nHelp Text Position: below", "version": 1, "status": "draft", "created_at": "2025-06-23T07:39:08.414329", "updated_at": "2025-06-23T07:39:08.414329", "created_by": "Tarun", "updated_by": "Tarun", "ui_property_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Entity with entity_id E_LeaveSubType_1750664348 does not exist", "Attribute with attribute_id A_E_LeaveSubType_1750664348_requiresManagerApproval_1750664348 does not exist"], "parsed_data": {"ui_property_id": "UI1", "entity_id": "E_LeaveSubType_1750664348", "attribute_id": "A_E_LeaveSubType_1750664348_requiresManagerApproval_1750664348", "control_type": "toggle", "display_format": "Yes/No", "input_mask": "", "placeholder_text": "", "auto_complete": false, "read_only": false, "validation_display": "inline", "help_text_position": "below", "label": "Requires Manager Approval", "required_indicator": false, "natural_language": "Entity: LeaveSubType\nAttribute: requiresManagerApproval\nControl Type: toggle\nLabel: Requires Manager Approval\nDisplay Format: Yes/No\nValidation Display: inline\nHelp Text Position: below", "version": 1, "status": "draft", "created_at": "2025-06-23T07:39:08.441866", "updated_at": "2025-06-23T07:39:08.441866", "created_by": "Tarun", "updated_by": "Tarun", "ui_property_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Entity with entity_id E_LeaveSubType_1750664348 does not exist", "Attribute with attribute_id A_E_LeaveSubType_1750664348_status_1750664348 does not exist"], "parsed_data": {"ui_property_id": "UI1", "entity_id": "E_LeaveSubType_1750664348", "attribute_id": "A_E_LeaveSubType_1750664348_status_1750664348", "control_type": "dropdown", "display_format": "{status}", "input_mask": "", "placeholder_text": "Select Status", "auto_complete": false, "read_only": false, "validation_display": "inline", "help_text_position": "below", "label": "Status", "required_indicator": false, "natural_language": "Entity: LeaveSubType\nAttribute: status\nControl Type: dropdown\nLabel: Status\nDisplay Format: {status}\nPlaceholder Text: Select Status\nValidation Display: inline\nHelp Text Position: below", "version": 1, "status": "draft", "created_at": "2025-06-23T07:39:08.467680", "updated_at": "2025-06-23T07:39:08.467680", "created_by": "Tarun", "updated_by": "Tarun", "ui_property_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Entity with entity_id E_Constants_1750664348 does not exist", "Attribute with attribute_id A_E_Constants_1750664348_constantId_1750664348 does not exist"], "parsed_data": {"ui_property_id": "UI1", "entity_id": "E_Constants_1750664348", "attribute_id": "A_E_Constants_1750664348_constantId_1750664348", "control_type": "text_input", "display_format": "CONST###", "input_mask": "CONST###", "placeholder_text": "Auto-generated", "auto_complete": false, "read_only": true, "validation_display": "inline", "help_text_position": "below", "label": "Constant ID", "required_indicator": false, "natural_language": "Entity: Constants\nAttribute: constantId\nControl Type: text_input\nLabel: Constant ID\nDisplay Format: CONST###\nInput Mask: CONST###\nPlaceholder Text: Auto-generated\nRead Only: true\nValidation Display: inline\nHelp Text Position: below", "version": 1, "status": "draft", "created_at": "2025-06-23T07:39:08.491770", "updated_at": "2025-06-23T07:39:08.491770", "created_by": "Tarun", "updated_by": "Tarun", "ui_property_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Entity with entity_id E_Constants_1750664348 does not exist", "Attribute with attribute_id A_E_Constants_1750664348_attribute_1750664348 does not exist"], "parsed_data": {"ui_property_id": "UI1", "entity_id": "E_Constants_1750664348", "attribute_id": "A_E_Constants_1750664348_attribute_1750664348", "control_type": "text_input", "display_format": "text", "input_mask": "", "placeholder_text": "Enter attribute name", "auto_complete": false, "read_only": false, "validation_display": "inline", "help_text_position": "below", "label": "Attribute", "required_indicator": true, "natural_language": "Entity: Constants\nAttribute: attribute\nControl Type: text_input\nLabel: Attribute\nDisplay Format: text\nPlaceholder Text: Enter attribute name\nRequired Indicator: true\nValidation Display: inline\nHelp Text Position: below", "version": 1, "status": "draft", "created_at": "2025-06-23T07:39:08.517411", "updated_at": "2025-06-23T07:39:08.517411", "created_by": "Tarun", "updated_by": "Tarun", "ui_property_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Entity with entity_id E_Constants_1750664348 does not exist", "Attribute with attribute_id A_E_Constants_1750664348_value_1750664348 does not exist"], "parsed_data": {"ui_property_id": "UI1", "entity_id": "E_Constants_1750664348", "attribute_id": "A_E_Constants_1750664348_value_1750664348", "control_type": "text_input", "display_format": "text", "input_mask": "", "placeholder_text": "Enter value", "auto_complete": false, "read_only": false, "validation_display": "inline", "help_text_position": "below", "label": "Value", "required_indicator": true, "natural_language": "Entity: Constants\nAttribute: value\nControl Type: text_input\nLabel: Value\nDisplay Format: text\nPlaceholder Text: Enter value\nRequired Indicator: true\nValidation Display: inline\nHelp Text Position: below", "version": 1, "status": "draft", "created_at": "2025-06-23T07:39:08.542266", "updated_at": "2025-06-23T07:39:08.542266", "created_by": "Tarun", "updated_by": "Tarun", "ui_property_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Entity with entity_id E_Constants_1750664348 does not exist", "Attribute with attribute_id A_E_Constants_1750664348_description_1750664348 does not exist"], "parsed_data": {"ui_property_id": "UI1", "entity_id": "E_Constants_1750664348", "attribute_id": "A_E_Constants_1750664348_description_1750664348", "control_type": "textarea", "display_format": "text", "input_mask": "", "placeholder_text": "Enter description", "auto_complete": false, "read_only": false, "validation_display": "inline", "help_text_position": "below", "label": "Description", "required_indicator": false, "natural_language": "Entity: Constants\nAttribute: description\nControl Type: textarea\nLabel: Description\nDisplay Format: text\nPlaceholder Text: Enter description\nValidation Display: inline\nHelp Text Position: below", "version": 1, "status": "draft", "created_at": "2025-06-23T07:39:08.569291", "updated_at": "2025-06-23T07:39:08.569291", "created_by": "Tarun", "updated_by": "Tarun", "ui_property_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Entity with entity_id E_Constants_1750664348 does not exist", "Attribute with attribute_id A_E_Constants_1750664348_dataType_1750664348 does not exist"], "parsed_data": {"ui_property_id": "UI1", "entity_id": "E_Constants_1750664348", "attribute_id": "A_E_Constants_1750664348_dataType_1750664348", "control_type": "dropdown", "display_format": "{dataType}", "input_mask": "", "placeholder_text": "Select Data Type", "auto_complete": false, "read_only": false, "validation_display": "inline", "help_text_position": "below", "label": "Data Type", "required_indicator": true, "natural_language": "Entity: Constants\nAttribute: dataType\nControl Type: dropdown\nLabel: Data Type\nDisplay Format: {dataType}\nPlaceholder Text: Select Data Type\nRequired Indicator: true\nValidation Display: inline\nHelp Text Position: below", "version": 1, "status": "draft", "created_at": "2025-06-23T07:39:08.597987", "updated_at": "2025-06-23T07:39:08.597987", "created_by": "Tarun", "updated_by": "Tarun", "ui_property_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Entity with entity_id E_Constants_1750664348 does not exist", "Attribute with attribute_id A_E_Constants_1750664348_status_1750664348 does not exist"], "parsed_data": {"ui_property_id": "UI1", "entity_id": "E_Constants_1750664348", "attribute_id": "A_E_Constants_1750664348_status_1750664348", "control_type": "dropdown", "display_format": "{status}", "input_mask": "", "placeholder_text": "Select Status", "auto_complete": false, "read_only": false, "validation_display": "inline", "help_text_position": "below", "label": "Status", "required_indicator": false, "natural_language": "Entity: Constants\nAttribute: status\nControl Type: dropdown\nLabel: Status\nDisplay Format: {status}\nPlaceholder Text: Select Status\nValidation Display: inline\nHelp Text Position: below", "version": 1, "status": "draft", "created_at": "2025-06-23T07:39:08.625397", "updated_at": "2025-06-23T07:39:08.625397", "created_by": "Tarun", "updated_by": "Tarun", "ui_property_status": "new", "changes_detected": []}}], "operation": "parse_validate_mongosave", "total_ui_properties": 50}, "status": "success"}
{"timestamp": "2025-06-23T07:41:55.090138", "endpoint": "parse-validate-mongosave/ui-properties", "input": {"natural_language": "Tenant: Acme Corp\n\nEntity: LeaveApplication\n\nUI Properties:\nProperty Name | Property Type | Value | Description | Applies To\n\nform_layout | layout | vertical | Vertical form layout for leave application | form\n\nfield_order | order | leaveId,employeeId,startDate,endDate,reason | Order of fields in the form | form\n\nvalidation_style | style | inline | Show validation errors inline | form\n\nsubmit_button_text | text | Submit Leave Request | Text for submit button | button\n\ncancel_button_text | text | Cancel | Text for cancel button | button\n\npage_title | title | Leave Application Form | Page title for the form | page", "tenant_id": "T_ACME_CORP", "edit_draft": false, "edit_temp": false, "edit_production": false}, "output": {"success": true, "ui_property_results": [], "operation": "parse_validate_mongosave", "total_ui_properties": 0}, "status": "success"}
{"timestamp": "2025-06-23T07:58:41.326446", "endpoint": "parse-validate-mongosave/ui-properties", "input": {"natural_language": "Tenant: Acme Corp\n\nEntity: LeaveApplication\n\nUI Properties:\nProperty Name | Property Type | Value | Description | Applies To\n\nform_layout | layout | vertical | Vertical form layout for leave application | form\n\nfield_order | order | leaveId,employeeId,startDate,endDate,reason | Order of fields in the form | form\n\nvalidation_style | style | inline | Show validation errors inline | form\n\nsubmit_button_text | text | Submit Leave Request | Text for submit button | button\n\ncancel_button_text | text | Cancel | Text for cancel button | button\n\npage_title | title | Leave Application Form | Page title for the form | page", "tenant_id": "T_ACME_CORP", "edit_draft": false, "edit_temp": false, "edit_production": false}, "output": {"success": true, "ui_property_results": [], "operation": "parse_validate_mongosave", "total_ui_properties": 0}, "status": "success"}
{"timestamp": "2025-06-23T07:59:52.658010", "endpoint": "parse-validate-mongosave/ui-properties", "input": {"natural_language": "Tenant: Acme Corp\n\nEntity: LeaveApplication\n\nUI Properties:\nProperty Name | Property Type | Value | Description | Applies To\n\nform_layout | layout | vertical | Vertical form layout for leave application | form\n\nfield_order | order | leaveId,employeeId,startDate,endDate,reason | Order of fields in the form | form\n\nvalidation_style | style | inline | Show validation errors inline | form\n\nsubmit_button_text | text | Submit Leave Request | Text for submit button | button\n\ncancel_button_text | text | Cancel | Text for cancel button | button\n\npage_title | title | Leave Application Form | Page title for the form | page", "tenant_id": "T_ACME_CORP", "edit_draft": false, "edit_temp": false, "edit_production": false}, "output": {"success": true, "ui_property_results": [], "operation": "parse_validate_mongosave", "total_ui_properties": 0}, "status": "success"}
{"timestamp": "2025-06-23T08:00:05.278390", "endpoint": "parse-validate-mongosave/ui-properties", "input": {"natural_language": "Tenant: Acme Corp\n\nEntity: LeaveApplication\n\nUI Properties:\nProperty Name | Property Type | Value | Description | Applies To\n\nform_layout | layout | vertical | Vertical form layout for leave application | form\n\nfield_order | order | leaveId,employeeId,startDate,endDate,reason | Order of fields in the form | form\n\nvalidation_style | style | inline | Show validation errors inline | form\n\nsubmit_button_text | text | Submit Leave Request | Text for submit button | button\n\ncancel_button_text | text | Cancel | Text for cancel button | button\n\npage_title | title | Leave Application Form | Page title for the form | page", "tenant_id": "T_ACME_CORP", "edit_draft": false, "edit_temp": false, "edit_production": false}, "output": {"success": true, "ui_property_results": [], "operation": "parse_validate_mongosave", "total_ui_properties": 0}, "status": "success"}
{"timestamp": "2025-06-23T08:24:32.702125", "endpoint": "parse-validate-mongosave/ui-properties", "input": {"natural_language": "Tenant: Acme Corp\n\nEntity: LeaveApplication\n\nUI Properties:\nProperty Name | Property Type | Value | Description | Applies To\n\nform_layout | layout | vertical | Vertical form layout for leave application | form\n\nfield_order | order | leaveId,employeeId,startDate,endDate,reason | Order of fields in the form | form\n\nvalidation_style | style | inline | Show validation errors inline | form\n\nsubmit_button_text | text | Submit Leave Request | Text for submit button | button\n\ncancel_button_text | text | Cancel | Text for cancel button | button\n\npage_title | title | Leave Application Form | Page title for the form | page", "tenant_id": "T_ACME_CORP", "edit_draft": false, "edit_temp": false, "edit_production": false}, "output": {"success": true, "ui_property_results": [], "operation": "parse_validate_mongosave", "total_ui_properties": 0}, "status": "success"}
{"timestamp": "2025-06-23T08:36:04.059809", "endpoint": "parse-validate-mongosave/ui-properties", "input": {"natural_language": "Tenant: My Custom Corp\n\nEntity.Attribute | Control Type | Display Format | Input Mask | Placeholder Text | Auto Complete | Read Only | Validation Display | Help Text Position | Label | Required Indicator\n\nEmployee.totalLeaveEntitlement | number_input | ##.# days | ##.# | Enter total entitlement | false | false | inline | below | Total Leave Entitlement | true\nEmployee.sickLeaveBalance | number_input | ##.# days | ##.# | Enter sick leave balance | false | false | inline | below | Sick Leave Balance | false\nEmployee.personalLeaveBalance | number_input | ##.# days | ##.# | Enter personal leave balance | false | false | inline | below | Personal Leave Balance | false\nCustomer.customerName | text_input | text | | Enter customer name | true | false | inline | below | Customer Name | true\nCustomer.email | email | <EMAIL> | | Enter email address | false | false | inline | below | Email Address | true\nCustomer.phone | text_input | +##-###-###-#### | +##-###-###-#### | Enter phone number | false | false | inline | below | Phone Number | false", "tenant_id": "T1001", "edit_draft": false, "edit_temp": false, "edit_production": false}, "output": {"success": true, "ui_property_results": [{"success": false, "error": "Item already exists in draft. Use edit_draft=true to update.", "existing_in": "mongo", "existing_document": {"_id": "685903f05e423f11979d4093", "ui_property_id": "UI1", "entity_id": "E13", "attribute_id": "E13.At11", "control_type": "number_input", "display_format": "##.# days", "input_mask": "##.#", "placeholder_text": "Enter entitlement", "auto_complete": false, "read_only": false, "validation_display": "inline", "help_text_position": "below", "label": "Total Leave Entitlement", "required_indicator": false, "natural_language": "Entity: Employee\nAttribute: totalLeaveEntitlement\nControl Type: number_input\nLabel: Total Leave Entitlement\nDisplay Format: ##.# days\nInput Mask: ##.#\nPlaceholder Text: Enter entitlement\nValidation Display: inline\nHelp Text Position: below", "version": 1, "status": "draft", "created_at": "2025-06-23T07:36:16.794690", "updated_at": "2025-06-23T07:36:16.794698", "created_by": "Tarun", "updated_by": "Tarun", "ui_property_status": "new", "changes_detected": []}, "parsed_data": {"ui_property_id": "UI1", "entity_id": "E13", "attribute_id": "E13.At11", "control_type": "number_input", "display_format": "##.# days", "input_mask": "##.#", "placeholder_text": "Enter total entitlement", "auto_complete": false, "read_only": false, "validation_display": "inline", "help_text_position": "below", "label": "Total Leave Entitlement", "required_indicator": true, "natural_language": "Entity: Employee\nAttribute: totalLeaveEntitlement\nControl Type: number_input\nLabel: Total Leave Entitlement\nDisplay Format: ##.# days\nInput Mask: ##.#\nPlaceholder Text: Enter total entitlement\nRequired Indicator: true\nValidation Display: inline\nHelp Text Position: below", "version": 1, "status": "draft", "created_at": "2025-06-23T08:36:03.753042", "updated_at": "2025-06-23T08:36:03.753042", "created_by": "Tarun", "updated_by": "Tarun", "ui_property_status": "existing", "changes_detected": []}}, {"success": false, "error": "Item already exists in draft. Use edit_draft=true to update.", "existing_in": "mongo", "existing_document": {"_id": "685903f05e423f11979d4093", "ui_property_id": "UI1", "entity_id": "E13", "attribute_id": "E13.At11", "control_type": "number_input", "display_format": "##.# days", "input_mask": "##.#", "placeholder_text": "Enter entitlement", "auto_complete": false, "read_only": false, "validation_display": "inline", "help_text_position": "below", "label": "Total Leave Entitlement", "required_indicator": false, "natural_language": "Entity: Employee\nAttribute: totalLeaveEntitlement\nControl Type: number_input\nLabel: Total Leave Entitlement\nDisplay Format: ##.# days\nInput Mask: ##.#\nPlaceholder Text: Enter entitlement\nValidation Display: inline\nHelp Text Position: below", "version": 1, "status": "draft", "created_at": "2025-06-23T07:36:16.794690", "updated_at": "2025-06-23T07:36:16.794698", "created_by": "Tarun", "updated_by": "Tarun", "ui_property_status": "new", "changes_detected": []}, "parsed_data": {"ui_property_id": "UI1", "entity_id": "E13", "attribute_id": "E13.At9", "control_type": "number_input", "display_format": "##.# days", "input_mask": "##.#", "placeholder_text": "Enter sick leave balance", "auto_complete": false, "read_only": false, "validation_display": "inline", "help_text_position": "below", "label": "Sick Leave Balance", "required_indicator": false, "natural_language": "Entity: Employee\nAttribute: sickLeaveBalance\nControl Type: number_input\nLabel: Sick Leave Balance\nDisplay Format: ##.# days\nInput Mask: ##.#\nPlaceholder Text: Enter sick leave balance\nValidation Display: inline\nHelp Text Position: below", "version": 1, "status": "draft", "created_at": "2025-06-23T08:36:03.774945", "updated_at": "2025-06-23T08:36:03.774945", "created_by": "Tarun", "updated_by": "Tarun", "ui_property_status": "new", "changes_detected": []}}, {"success": false, "error": "Item already exists in draft. Use edit_draft=true to update.", "existing_in": "mongo", "existing_document": {"_id": "685903f05e423f11979d4093", "ui_property_id": "UI1", "entity_id": "E13", "attribute_id": "E13.At11", "control_type": "number_input", "display_format": "##.# days", "input_mask": "##.#", "placeholder_text": "Enter entitlement", "auto_complete": false, "read_only": false, "validation_display": "inline", "help_text_position": "below", "label": "Total Leave Entitlement", "required_indicator": false, "natural_language": "Entity: Employee\nAttribute: totalLeaveEntitlement\nControl Type: number_input\nLabel: Total Leave Entitlement\nDisplay Format: ##.# days\nInput Mask: ##.#\nPlaceholder Text: Enter entitlement\nValidation Display: inline\nHelp Text Position: below", "version": 1, "status": "draft", "created_at": "2025-06-23T07:36:16.794690", "updated_at": "2025-06-23T07:36:16.794698", "created_by": "Tarun", "updated_by": "Tarun", "ui_property_status": "new", "changes_detected": []}, "parsed_data": {"ui_property_id": "UI1", "entity_id": "E13", "attribute_id": "E13.At10", "control_type": "number_input", "display_format": "##.# days", "input_mask": "##.#", "placeholder_text": "Enter personal leave balance", "auto_complete": false, "read_only": false, "validation_display": "inline", "help_text_position": "below", "label": "Personal Leave Balance", "required_indicator": false, "natural_language": "Entity: Employee\nAttribute: personalLeaveBalance\nControl Type: number_input\nLabel: Personal Leave Balance\nDisplay Format: ##.# days\nInput Mask: ##.#\nPlaceholder Text: Enter personal leave balance\nValidation Display: inline\nHelp Text Position: below", "version": 1, "status": "draft", "created_at": "2025-06-23T08:36:03.795947", "updated_at": "2025-06-23T08:36:03.795947", "created_by": "Tarun", "updated_by": "Tarun", "ui_property_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Entity with entity_id E_Customer_1750667763 does not exist", "Attribute with attribute_id A_E_Customer_1750667763_customerName_1750667763 does not exist"], "parsed_data": {"ui_property_id": "UI1", "entity_id": "E_Customer_1750667763", "attribute_id": "A_E_Customer_1750667763_customerName_1750667763", "control_type": "text_input", "display_format": "text", "input_mask": "", "placeholder_text": "Enter customer name", "auto_complete": true, "read_only": false, "validation_display": "inline", "help_text_position": "below", "label": "Customer Name", "required_indicator": true, "natural_language": "Entity: Customer\nAttribute: customerName\nControl Type: text_input\nLabel: Customer Name\nDisplay Format: text\nPlaceholder Text: Enter customer name\nAuto Complete: true\nRequired Indicator: true\nValidation Display: inline\nHelp Text Position: below", "version": 1, "status": "draft", "created_at": "2025-06-23T08:36:03.833201", "updated_at": "2025-06-23T08:36:03.833201", "created_by": "Tarun", "updated_by": "Tarun", "ui_property_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Entity with entity_id E_Customer_1750667763 does not exist", "Attribute with attribute_id A_E_Customer_1750667763_email_1750667763 does not exist"], "parsed_data": {"ui_property_id": "UI1", "entity_id": "E_Customer_1750667763", "attribute_id": "A_E_Customer_1750667763_email_1750667763", "control_type": "email", "display_format": "<EMAIL>", "input_mask": "", "placeholder_text": "Enter email address", "auto_complete": false, "read_only": false, "validation_display": "inline", "help_text_position": "below", "label": "Email Address", "required_indicator": true, "natural_language": "Entity: Customer\nAttribute: email\nControl Type: email\nLabel: Email Address\nDisplay Format: <EMAIL>\nPlaceholder Text: Enter email address\nRequired Indicator: true\nValidation Display: inline\nHelp Text Position: below", "version": 1, "status": "draft", "created_at": "2025-06-23T08:36:03.870101", "updated_at": "2025-06-23T08:36:03.870101", "created_by": "Tarun", "updated_by": "Tarun", "ui_property_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Entity with entity_id E_Customer_1750667763 does not exist", "Attribute with attribute_id A_E_Customer_1750667763_phone_1750667763 does not exist"], "parsed_data": {"ui_property_id": "UI1", "entity_id": "E_Customer_1750667763", "attribute_id": "A_E_Customer_1750667763_phone_1750667763", "control_type": "text_input", "display_format": "+##-###-###-####", "input_mask": "+##-###-###-####", "placeholder_text": "Enter phone number", "auto_complete": false, "read_only": false, "validation_display": "inline", "help_text_position": "below", "label": "Phone Number", "required_indicator": false, "natural_language": "Entity: Customer\nAttribute: phone\nControl Type: text_input\nLabel: Phone Number\nDisplay Format: +##-###-###-####\nInput Mask: +##-###-###-####\nPlaceholder Text: Enter phone number\nValidation Display: inline\nHelp Text Position: below", "version": 1, "status": "draft", "created_at": "2025-06-23T08:36:03.903790", "updated_at": "2025-06-23T08:36:03.903790", "created_by": "Tarun", "updated_by": "Tarun", "ui_property_status": "new", "changes_detected": []}}], "operation": "parse_validate_mongosave", "total_ui_properties": 6}, "status": "success"}
{"timestamp": "2025-06-23T08:39:04.609295", "endpoint": "parse-validate-mongosave/ui-properties", "input": {"natural_language": "Tenant: Test Corp\n\nEntity.Attribute | Control Type | Display Format | Input Mask | Placeholder Text | Auto Complete | Read Only | Validation Display | Help Text Position | Label | Required Indicator\n\nEmployee.totalLeaveEntitlement | number_input | ##.# days | ##.# | Enter entitlement | false | false | inline | below | Total Leave Entitlement | true\nEmployee.sickLeaveBalance | number_input | ##.# days | ##.# | Enter balance | false | false | inline | below | Sick Leave Balance | false", "tenant_id": "T1001", "edit_draft": false, "edit_temp": false, "edit_production": false}, "output": {"success": true, "ui_property_results": [{"success": false, "error": "Item already exists in draft. Use edit_draft=true to update.", "existing_in": "mongo", "existing_document": {"_id": "685903f05e423f11979d4093", "ui_property_id": "UI1", "entity_id": "E13", "attribute_id": "E13.At11", "control_type": "number_input", "display_format": "##.# days", "input_mask": "##.#", "placeholder_text": "Enter entitlement", "auto_complete": false, "read_only": false, "validation_display": "inline", "help_text_position": "below", "label": "Total Leave Entitlement", "required_indicator": false, "natural_language": "Entity: Employee\nAttribute: totalLeaveEntitlement\nControl Type: number_input\nLabel: Total Leave Entitlement\nDisplay Format: ##.# days\nInput Mask: ##.#\nPlaceholder Text: Enter entitlement\nValidation Display: inline\nHelp Text Position: below", "version": 1, "status": "draft", "created_at": "2025-06-23T07:36:16.794690", "updated_at": "2025-06-23T07:36:16.794698", "created_by": "Tarun", "updated_by": "Tarun", "ui_property_status": "new", "changes_detected": []}, "parsed_data": {"ui_property_id": "UI1", "entity_id": "E13", "attribute_id": "E13.At11", "control_type": "number_input", "display_format": "##.# days", "input_mask": "##.#", "placeholder_text": "Enter entitlement", "auto_complete": false, "read_only": false, "validation_display": "inline", "help_text_position": "below", "label": "Total Leave Entitlement", "required_indicator": true, "natural_language": "Entity: Employee\nAttribute: totalLeaveEntitlement\nControl Type: number_input\nLabel: Total Leave Entitlement\nDisplay Format: ##.# days\nInput Mask: ##.#\nPlaceholder Text: Enter entitlement\nRequired Indicator: true\nValidation Display: inline\nHelp Text Position: below", "version": 1, "status": "draft", "created_at": "2025-06-23T08:39:04.546867", "updated_at": "2025-06-23T08:39:04.546867", "created_by": "Tarun", "updated_by": "Tarun", "ui_property_status": "existing", "changes_detected": []}}, {"success": false, "error": "Item already exists in draft. Use edit_draft=true to update.", "existing_in": "mongo", "existing_document": {"_id": "685903f05e423f11979d4093", "ui_property_id": "UI1", "entity_id": "E13", "attribute_id": "E13.At11", "control_type": "number_input", "display_format": "##.# days", "input_mask": "##.#", "placeholder_text": "Enter entitlement", "auto_complete": false, "read_only": false, "validation_display": "inline", "help_text_position": "below", "label": "Total Leave Entitlement", "required_indicator": false, "natural_language": "Entity: Employee\nAttribute: totalLeaveEntitlement\nControl Type: number_input\nLabel: Total Leave Entitlement\nDisplay Format: ##.# days\nInput Mask: ##.#\nPlaceholder Text: Enter entitlement\nValidation Display: inline\nHelp Text Position: below", "version": 1, "status": "draft", "created_at": "2025-06-23T07:36:16.794690", "updated_at": "2025-06-23T07:36:16.794698", "created_by": "Tarun", "updated_by": "Tarun", "ui_property_status": "new", "changes_detected": []}, "parsed_data": {"ui_property_id": "UI1", "entity_id": "E13", "attribute_id": "E13.At9", "control_type": "number_input", "display_format": "##.# days", "input_mask": "##.#", "placeholder_text": "Enter balance", "auto_complete": false, "read_only": false, "validation_display": "inline", "help_text_position": "below", "label": "Sick Leave Balance", "required_indicator": false, "natural_language": "Entity: Employee\nAttribute: sickLeaveBalance\nControl Type: number_input\nLabel: Sick Leave Balance\nDisplay Format: ##.# days\nInput Mask: ##.#\nPlaceholder Text: Enter balance\nValidation Display: inline\nHelp Text Position: below", "version": 1, "status": "draft", "created_at": "2025-06-23T08:39:04.567435", "updated_at": "2025-06-23T08:39:04.567435", "created_by": "Tarun", "updated_by": "Tarun", "ui_property_status": "new", "changes_detected": []}}], "operation": "parse_validate_mongosave", "total_ui_properties": 2}, "status": "success"}
{"timestamp": "2025-06-23T08:46:22.259701", "endpoint": "parse-validate-mongosave/ui-properties", "input": {"natural_language": "Tenant: Acme Corp\n\nEntity: LeaveApplication\n\nUI Properties:\nProperty Name | Property Type | Value | Description | Applies To\n\nform_layout | layout | vertical | Vertical form layout for leave application | form\n\nfield_order | order | leaveId,employeeId,startDate,endDate,reason | Order of fields in the form | form\n\nvalidation_style | style | inline | Show validation errors inline | form\n\nsubmit_button_text | text | Submit Leave Request | Text for submit button | button\n\ncancel_button_text | text | Cancel | Text for cancel button | button\n\npage_title | title | Leave Application Form | Page title for the form | page", "tenant_id": "T_ACME_CORP", "edit_draft": false, "edit_temp": false, "edit_production": false}, "output": {"success": true, "ui_property_results": [], "operation": "parse_validate_mongosave", "total_ui_properties": 0}, "status": "success"}
{"timestamp": "2025-06-23T09:00:45.708510", "endpoint": "parse-validate-mongosave/ui-properties", "input": {"natural_language": "Tenant: Acme Corp\n\nEntity.Attribute | Control Type | Display Format | Input Mask | Placeholder Text | Auto Complete | Read Only | Validation Display | Help Text Position | Label | Required Indicator\n\nLeaveApplication.leaveId | text_input | LV-YYYY-#### | LV-####-#### | Auto-generated | false | true | inline | below | Leave ID | false\n\nLeaveApplication.employeeId | dropdown | EMP### - {firstName} {lastName} | | Select Employee | true | false | inline | below | Employee | true\n\nLeaveApplication.startDate | date_picker | DD/MM/YYYY | DD/MM/YYYY | Select start date | false | false | inline | below | Start Date | true\n\nLeaveApplication.endDate | date_picker | DD/MM/YYYY | DD/MM/YYYY | Select end date | false | false | inline | below | End Date | true\n\nLeaveApplication.numDays | number_input | ### days | ### | Auto-calculated | false | true | inline | below | Number of Days | false\n\nLeaveApplication.reason | textarea | text | | Enter detailed reason for leave request | false | false | inline | below | Reason | true\n\nLeaveApplication.leaveTypeName | dropdown | {leaveTypeName} | | Select Leave Type | true | false | inline | below | Leave Type | true\n\nLeaveApplication.leaveSubTypeName | dropdown | {leaveSubTypeName} | | Select Leave Sub-Type | true | false | inline | below | Leave Sub-Type | false\n\nLeaveApplication.status | radio | {status} | | | false | true | inline | below | Status | false\n\nLeaveApplication.requiresDocumentation | checkbox | Yes/No | | | false | true | inline | below | Requires Documentation | false\n\nLeaveApplication.documentationProvided | file_upload | Upload Status | | Upload supporting documents | false | false | inline | below | Documentation | false\n\nLeaveApplication.submissionDate | text_input | DD/MM/YYYY HH:MM | | Auto-generated | false | true | inline | below | Submission Date | false\n\nLeaveApplication.approvalDate | text_input | DD/MM/YYYY HH:MM | | Set on approval | false | true | inline | below | Approval Date | false\n\nLeaveApplication.approvedBy | dropdown | {firstName} {lastName} ({employeeId}) | | Select Approver | true | false | inline | below | Approved By | false\n\nLeaveApplication.comments | textarea | text | | Enter manager comments | false | false | inline | below | Comments | false\n\nEmployee.employeeId | text_input | EMP### | EMP### | Auto-generated | false | true | inline | below | Employee ID | false\n\nEmployee.firstName | text_input | text | | Enter first name | false | false | inline | below | First Name | true\n\nEmployee.lastName | text_input | text | | Enter last name | false | false | inline | below | Last Name | true\n\nEmployee.email | text_input | <EMAIL> | | Enter email address | false | false | inline | below | Email | true\n\nEmployee.phone | text_input | +##-###-###-#### | +##-###-###-#### | Enter phone number | false | false | inline | below | Phone | false\n\nEmployee.departmentId | dropdown | {departmentName} | | Select Department | true | false | inline | below | Department | true\n\nEmployee.managerId | dropdown | {firstName} {lastName} ({employeeId}) | | Select Manager | true | false | inline | below | Manager | false\n\nEmployee.jobTitle | text_input | text | | Enter job title | true | false | inline | below | Job Title | true\n\nEmployee.hireDate | date_picker | DD/MM/YYYY | DD/MM/YYYY | Select hire date | false | false | inline | below | Hire Date | true\n\nEmployee.employmentStatus | dropdown | {status} | | Select Status | false | false | inline | below | Employment Status | true\n\nEmployee.annualLeaveBalance | number_input | ##.# days | ##.# | Enter balance | false | false | inline | below | Annual Leave Balance | false\n\nEmployee.sickLeaveBalance | number_input | ##.# days | ##.# | Enter balance | false | false | inline | below | Sick Leave Balance | false\n\nEmployee.personalLeaveBalance | number_input | ##.# days | ##.# | Enter balance | false | false | inline | below | Personal Leave Balance | false\n\nEmployee.totalLeaveEntitlement | number_input | ##.# days | ##.# | Enter entitlement | false | false | inline | below | Total Leave Entitlement | false\n\nLeaveType.leaveTypeId | text_input | LT### | LT### | Auto-generated | false | true | inline | below | Leave Type ID | false\n\nLeaveType.leaveTypeName | text_input | text | | Enter leave type name | false | false | inline | below | Leave Type Name | true\n\nLeaveType.description | textarea | text | | Enter description | false | false | inline | below | Description | false\n\nLeaveType.maxDaysPerYear | number_input | ### days | ### | Enter maximum days | false | false | inline | below | Max Days Per Year | true\n\nLeaveType.requiresDocumentation | toggle | Yes/No | | | false | false | inline | below | Requires Documentation | false\n\nLeaveType.advanceNoticeDays | number_input | ### days | ### | Enter notice days | false | false | inline | below | Advance Notice Days | false\n\nLeaveType.isCarryForward | toggle | Yes/No | | | false | false | inline | below | Is Carry Forward | false\n\nLeaveType.status | dropdown | {status} | | Select Status | false | false | inline | below | Status | false\n\nLeaveSubType.leaveSubTypeId | text_input | LST### | LST### | Auto-generated | false | true | inline | below | Leave Sub-Type ID | false\n\nLeaveSubType.leaveTypeName | dropdown | {leaveTypeName} | | Select Leave Type | true | false | inline | below | Leave Type | true\n\nLeaveSubType.leaveSubTypeName | text_input | text | | Enter sub-type name | false | false | inline | below | Leave Sub-Type Name | true\n\nLeaveSubType.description | textarea | text | | Enter description | false | false | inline | below | Description | false\n\nLeaveSubType.maxDaysPerRequest | number_input | ### days | ### | Enter maximum days | false | false | inline | below | Max Days Per Request | true\n\nLeaveSubType.requiresManagerApproval | toggle | Yes/No | | | false | false | inline | below | Requires Manager Approval | false\n\nLeaveSubType.status | dropdown | {status} | | Select Status | false | false | inline | below | Status | false\n\nConstants.constantId | text_input | CONST### | CONST### | Auto-generated | false | true | inline | below | Constant ID | false\n\nConstants.attribute | text_input | text | | Enter attribute name | false | false | inline | below | Attribute | true\n\nConstants.value | text_input | text | | Enter value | false | false | inline | below | Value | true\n\nConstants.description | textarea | text | | Enter description | false | false | inline | below | Description | false\n\nConstants.dataType | dropdown | {dataType} | | Select Data Type | false | false | inline | below | Data Type | true\n\nConstants.status | dropdown | {status} | | Select Status | false | false | inline | below | Status | false\n", "tenant_id": "T1001", "edit_draft": true, "edit_temp": false, "edit_production": false}, "output": {"success": true, "ui_property_results": [{"success": false, "errors": ["Entity with entity_id E_LeaveApplication_1750669243 does not exist", "Attribute with attribute_id A_E_LeaveApplication_1750669243_leaveId_1750669243 does not exist"], "parsed_data": {"ui_property_id": "UI1", "entity_id": "E_LeaveApplication_1750669243", "attribute_id": "A_E_LeaveApplication_1750669243_leaveId_1750669243", "control_type": "text_input", "display_format": "LV-YYYY-####", "input_mask": "LV-####-####", "placeholder_text": "Auto-generated", "auto_complete": false, "read_only": true, "validation_display": "inline", "help_text_position": "below", "label": "Leave ID", "required_indicator": false, "natural_language": "Entity: LeaveApplication\nAttribute: leaveId\nControl Type: text_input\nLabel: Leave ID\nDisplay Format: LV-YYYY-####\nInput Mask: LV-####-####\nPlaceholder Text: Auto-generated\nRead Only: true\nValidation Display: inline\nHelp Text Position: below", "version": 1, "status": "draft", "created_at": "2025-06-23T09:00:43.186317", "updated_at": "2025-06-23T09:00:43.186317", "created_by": "Tarun", "updated_by": "Tarun", "ui_property_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Entity with entity_id E_LeaveApplication_1750669243 does not exist", "Attribute with attribute_id A_E_LeaveApplication_1750669243_employeeId_1750669243 does not exist"], "parsed_data": {"ui_property_id": "UI1", "entity_id": "E_LeaveApplication_1750669243", "attribute_id": "A_E_LeaveApplication_1750669243_employeeId_1750669243", "control_type": "dropdown", "display_format": "EMP### - {firstName} {lastName}", "input_mask": "", "placeholder_text": "Select Employee", "auto_complete": true, "read_only": false, "validation_display": "inline", "help_text_position": "below", "label": "Employee", "required_indicator": true, "natural_language": "Entity: LeaveApplication\nAttribute: employeeId\nControl Type: dropdown\nLabel: Employee\nDisplay Format: EMP### - {firstName} {lastName}\nPlaceholder Text: Select Employee\nAuto Complete: true\nRequired Indicator: true\nValidation Display: inline\nHelp Text Position: below", "version": 1, "status": "draft", "created_at": "2025-06-23T09:00:43.218968", "updated_at": "2025-06-23T09:00:43.218968", "created_by": "Tarun", "updated_by": "Tarun", "ui_property_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Entity with entity_id E_LeaveApplication_1750669243 does not exist", "Attribute with attribute_id A_E_LeaveApplication_1750669243_startDate_1750669243 does not exist"], "parsed_data": {"ui_property_id": "UI1", "entity_id": "E_LeaveApplication_1750669243", "attribute_id": "A_E_LeaveApplication_1750669243_startDate_1750669243", "control_type": "date_picker", "display_format": "DD/MM/YYYY", "input_mask": "DD/MM/YYYY", "placeholder_text": "Select start date", "auto_complete": false, "read_only": false, "validation_display": "inline", "help_text_position": "below", "label": "Start Date", "required_indicator": true, "natural_language": "Entity: LeaveApplication\nAttribute: startDate\nControl Type: date_picker\nLabel: Start Date\nDisplay Format: DD/MM/YYYY\nInput Mask: DD/MM/YYYY\nPlaceholder Text: Select start date\nRequired Indicator: true\nValidation Display: inline\nHelp Text Position: below", "version": 1, "status": "draft", "created_at": "2025-06-23T09:00:43.251245", "updated_at": "2025-06-23T09:00:43.251245", "created_by": "Tarun", "updated_by": "Tarun", "ui_property_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Entity with entity_id E_LeaveApplication_1750669243 does not exist", "Attribute with attribute_id A_E_LeaveApplication_1750669243_endDate_1750669243 does not exist"], "parsed_data": {"ui_property_id": "UI1", "entity_id": "E_LeaveApplication_1750669243", "attribute_id": "A_E_LeaveApplication_1750669243_endDate_1750669243", "control_type": "date_picker", "display_format": "DD/MM/YYYY", "input_mask": "DD/MM/YYYY", "placeholder_text": "Select end date", "auto_complete": false, "read_only": false, "validation_display": "inline", "help_text_position": "below", "label": "End Date", "required_indicator": true, "natural_language": "Entity: LeaveApplication\nAttribute: endDate\nControl Type: date_picker\nLabel: End Date\nDisplay Format: DD/MM/YYYY\nInput Mask: DD/MM/YYYY\nPlaceholder Text: Select end date\nRequired Indicator: true\nValidation Display: inline\nHelp Text Position: below", "version": 1, "status": "draft", "created_at": "2025-06-23T09:00:43.283358", "updated_at": "2025-06-23T09:00:43.283358", "created_by": "Tarun", "updated_by": "Tarun", "ui_property_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Entity with entity_id E_LeaveApplication_1750669243 does not exist", "Attribute with attribute_id A_E_LeaveApplication_1750669243_numDays_1750669243 does not exist"], "parsed_data": {"ui_property_id": "UI1", "entity_id": "E_LeaveApplication_1750669243", "attribute_id": "A_E_LeaveApplication_1750669243_numDays_1750669243", "control_type": "number_input", "display_format": "### days", "input_mask": "###", "placeholder_text": "Auto-calculated", "auto_complete": false, "read_only": true, "validation_display": "inline", "help_text_position": "below", "label": "Number of Days", "required_indicator": false, "natural_language": "Entity: LeaveApplication\nAttribute: numDays\nControl Type: number_input\nLabel: Number of Days\nDisplay Format: ### days\nInput Mask: ###\nPlaceholder Text: Auto-calculated\nRead Only: true\nValidation Display: inline\nHelp Text Position: below", "version": 1, "status": "draft", "created_at": "2025-06-23T09:00:43.310557", "updated_at": "2025-06-23T09:00:43.310557", "created_by": "Tarun", "updated_by": "Tarun", "ui_property_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Entity with entity_id E_LeaveApplication_1750669243 does not exist", "Attribute with attribute_id A_E_LeaveApplication_1750669243_reason_1750669243 does not exist"], "parsed_data": {"ui_property_id": "UI1", "entity_id": "E_LeaveApplication_1750669243", "attribute_id": "A_E_LeaveApplication_1750669243_reason_1750669243", "control_type": "textarea", "display_format": "text", "input_mask": "", "placeholder_text": "Enter detailed reason for leave request", "auto_complete": false, "read_only": false, "validation_display": "inline", "help_text_position": "below", "label": "Reason", "required_indicator": true, "natural_language": "Entity: LeaveApplication\nAttribute: reason\nControl Type: textarea\nLabel: Reason\nDisplay Format: text\nPlaceholder Text: Enter detailed reason for leave request\nRequired Indicator: true\nValidation Display: inline\nHelp Text Position: below", "version": 1, "status": "draft", "created_at": "2025-06-23T09:00:43.341391", "updated_at": "2025-06-23T09:00:43.341391", "created_by": "Tarun", "updated_by": "Tarun", "ui_property_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Entity with entity_id E_LeaveApplication_1750669243 does not exist", "Attribute with attribute_id A_E_LeaveApplication_1750669243_leaveTypeName_1750669243 does not exist"], "parsed_data": {"ui_property_id": "UI1", "entity_id": "E_LeaveApplication_1750669243", "attribute_id": "A_E_LeaveApplication_1750669243_leaveTypeName_1750669243", "control_type": "dropdown", "display_format": "{leaveTypeName}", "input_mask": "", "placeholder_text": "Select Leave Type", "auto_complete": true, "read_only": false, "validation_display": "inline", "help_text_position": "below", "label": "Leave Type", "required_indicator": true, "natural_language": "Entity: LeaveApplication\nAttribute: leaveTypeName\nControl Type: dropdown\nLabel: Leave Type\nDisplay Format: {leaveTypeName}\nPlaceholder Text: Select Leave Type\nAuto Complete: true\nRequired Indicator: true\nValidation Display: inline\nHelp Text Position: below", "version": 1, "status": "draft", "created_at": "2025-06-23T09:00:43.373772", "updated_at": "2025-06-23T09:00:43.373772", "created_by": "Tarun", "updated_by": "Tarun", "ui_property_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Entity with entity_id E_LeaveApplication_1750669243 does not exist", "Attribute with attribute_id A_E_LeaveApplication_1750669243_leaveSubTypeName_1750669243 does not exist"], "parsed_data": {"ui_property_id": "UI1", "entity_id": "E_LeaveApplication_1750669243", "attribute_id": "A_E_LeaveApplication_1750669243_leaveSubTypeName_1750669243", "control_type": "dropdown", "display_format": "{leaveSubTypeName}", "input_mask": "", "placeholder_text": "Select Leave Sub-Type", "auto_complete": true, "read_only": false, "validation_display": "inline", "help_text_position": "below", "label": "Leave Sub-Type", "required_indicator": false, "natural_language": "Entity: LeaveApplication\nAttribute: leaveSubTypeName\nControl Type: dropdown\nLabel: Leave Sub-Type\nDisplay Format: {leaveSubTypeName}\nPlaceholder Text: Select Leave Sub-Type\nAuto Complete: true\nValidation Display: inline\nHelp Text Position: below", "version": 1, "status": "draft", "created_at": "2025-06-23T09:00:43.404559", "updated_at": "2025-06-23T09:00:43.404559", "created_by": "Tarun", "updated_by": "Tarun", "ui_property_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Entity with entity_id E_LeaveApplication_1750669243 does not exist", "Attribute with attribute_id A_E_LeaveApplication_1750669243_status_1750669243 does not exist"], "parsed_data": {"ui_property_id": "UI1", "entity_id": "E_LeaveApplication_1750669243", "attribute_id": "A_E_LeaveApplication_1750669243_status_1750669243", "control_type": "radio", "display_format": "{status}", "input_mask": "", "placeholder_text": "", "auto_complete": false, "read_only": true, "validation_display": "inline", "help_text_position": "below", "label": "Status", "required_indicator": false, "natural_language": "Entity: LeaveApplication\nAttribute: status\nControl Type: radio\nLabel: Status\nDisplay Format: {status}\nRead Only: true\nValidation Display: inline\nHelp Text Position: below", "version": 1, "status": "draft", "created_at": "2025-06-23T09:00:43.436578", "updated_at": "2025-06-23T09:00:43.436578", "created_by": "Tarun", "updated_by": "Tarun", "ui_property_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Entity with entity_id E_LeaveApplication_1750669243 does not exist", "Attribute with attribute_id A_E_LeaveApplication_1750669243_requiresDocumentation_1750669243 does not exist"], "parsed_data": {"ui_property_id": "UI1", "entity_id": "E_LeaveApplication_1750669243", "attribute_id": "A_E_LeaveApplication_1750669243_requiresDocumentation_1750669243", "control_type": "checkbox", "display_format": "Yes/No", "input_mask": "", "placeholder_text": "", "auto_complete": false, "read_only": true, "validation_display": "inline", "help_text_position": "below", "label": "Requires Documentation", "required_indicator": false, "natural_language": "Entity: LeaveApplication\nAttribute: requiresDocumentation\nControl Type: checkbox\nLabel: Requires Documentation\nDisplay Format: Yes/No\nRead Only: true\nValidation Display: inline\nHelp Text Position: below", "version": 1, "status": "draft", "created_at": "2025-06-23T09:00:43.470422", "updated_at": "2025-06-23T09:00:43.470422", "created_by": "Tarun", "updated_by": "Tarun", "ui_property_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Entity with entity_id E_LeaveApplication_1750669243 does not exist", "Attribute with attribute_id A_E_LeaveApplication_1750669243_documentationProvided_1750669243 does not exist"], "parsed_data": {"ui_property_id": "UI1", "entity_id": "E_LeaveApplication_1750669243", "attribute_id": "A_E_LeaveApplication_1750669243_documentationProvided_1750669243", "control_type": "file_upload", "display_format": "Upload Status", "input_mask": "", "placeholder_text": "Upload supporting documents", "auto_complete": false, "read_only": false, "validation_display": "inline", "help_text_position": "below", "label": "Documentation", "required_indicator": false, "natural_language": "Entity: LeaveApplication\nAttribute: documentationProvided\nControl Type: file_upload\nLabel: Documentation\nDisplay Format: Upload Status\nPlaceholder Text: Upload supporting documents\nValidation Display: inline\nHelp Text Position: below", "version": 1, "status": "draft", "created_at": "2025-06-23T09:00:43.500398", "updated_at": "2025-06-23T09:00:43.500398", "created_by": "Tarun", "updated_by": "Tarun", "ui_property_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Entity with entity_id E_LeaveApplication_1750669243 does not exist", "Attribute with attribute_id A_E_LeaveApplication_1750669243_submissionDate_1750669243 does not exist"], "parsed_data": {"ui_property_id": "UI1", "entity_id": "E_LeaveApplication_1750669243", "attribute_id": "A_E_LeaveApplication_1750669243_submissionDate_1750669243", "control_type": "text_input", "display_format": "DD/MM/YYYY HH:MM", "input_mask": "", "placeholder_text": "Auto-generated", "auto_complete": false, "read_only": true, "validation_display": "inline", "help_text_position": "below", "label": "Submission Date", "required_indicator": false, "natural_language": "Entity: LeaveApplication\nAttribute: submissionDate\nControl Type: text_input\nLabel: Submission Date\nDisplay Format: DD/MM/YYYY HH:MM\nPlaceholder Text: Auto-generated\nRead Only: true\nValidation Display: inline\nHelp Text Position: below", "version": 1, "status": "draft", "created_at": "2025-06-23T09:00:43.534376", "updated_at": "2025-06-23T09:00:43.534376", "created_by": "Tarun", "updated_by": "Tarun", "ui_property_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Entity with entity_id E_LeaveApplication_1750669243 does not exist", "Attribute with attribute_id A_E_LeaveApplication_1750669243_approvalDate_1750669243 does not exist"], "parsed_data": {"ui_property_id": "UI1", "entity_id": "E_LeaveApplication_1750669243", "attribute_id": "A_E_LeaveApplication_1750669243_approvalDate_1750669243", "control_type": "text_input", "display_format": "DD/MM/YYYY HH:MM", "input_mask": "", "placeholder_text": "Set on approval", "auto_complete": false, "read_only": true, "validation_display": "inline", "help_text_position": "below", "label": "Approval Date", "required_indicator": false, "natural_language": "Entity: LeaveApplication\nAttribute: approvalDate\nControl Type: text_input\nLabel: Approval Date\nDisplay Format: DD/MM/YYYY HH:MM\nPlaceholder Text: Set on approval\nRead Only: true\nValidation Display: inline\nHelp Text Position: below", "version": 1, "status": "draft", "created_at": "2025-06-23T09:00:43.564549", "updated_at": "2025-06-23T09:00:43.564549", "created_by": "Tarun", "updated_by": "Tarun", "ui_property_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Entity with entity_id E_LeaveApplication_1750669243 does not exist", "Attribute with attribute_id A_E_LeaveApplication_1750669243_approvedBy_1750669243 does not exist"], "parsed_data": {"ui_property_id": "UI1", "entity_id": "E_LeaveApplication_1750669243", "attribute_id": "A_E_LeaveApplication_1750669243_approvedBy_1750669243", "control_type": "dropdown", "display_format": "{firstName} {lastName} ({employeeId})", "input_mask": "", "placeholder_text": "Select Approver", "auto_complete": true, "read_only": false, "validation_display": "inline", "help_text_position": "below", "label": "Approved By", "required_indicator": false, "natural_language": "Entity: LeaveApplication\nAttribute: approvedBy\nControl Type: dropdown\nLabel: Approved By\nDisplay Format: {firstName} {lastName} ({employeeId})\nPlaceholder Text: Select Approver\nAuto Complete: true\nValidation Display: inline\nHelp Text Position: below", "version": 1, "status": "draft", "created_at": "2025-06-23T09:00:43.593820", "updated_at": "2025-06-23T09:00:43.593820", "created_by": "Tarun", "updated_by": "Tarun", "ui_property_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Entity with entity_id E_LeaveApplication_1750669243 does not exist", "Attribute with attribute_id A_E_LeaveApplication_1750669243_comments_1750669243 does not exist"], "parsed_data": {"ui_property_id": "UI1", "entity_id": "E_LeaveApplication_1750669243", "attribute_id": "A_E_LeaveApplication_1750669243_comments_1750669243", "control_type": "textarea", "display_format": "text", "input_mask": "", "placeholder_text": "Enter manager comments", "auto_complete": false, "read_only": false, "validation_display": "inline", "help_text_position": "below", "label": "Comments", "required_indicator": false, "natural_language": "Entity: LeaveApplication\nAttribute: comments\nControl Type: textarea\nLabel: Comments\nDisplay Format: text\nPlaceholder Text: Enter manager comments\nValidation Display: inline\nHelp Text Position: below", "version": 1, "status": "draft", "created_at": "2025-06-23T09:00:43.620840", "updated_at": "2025-06-23T09:00:43.620840", "created_by": "Tarun", "updated_by": "Tarun", "ui_property_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Attribute with attribute_id A_E13_employeeId_1750669243 does not exist"], "parsed_data": {"ui_property_id": "UI1", "entity_id": "E13", "attribute_id": "A_E13_employeeId_1750669243", "control_type": "text_input", "display_format": "EMP###", "input_mask": "EMP###", "placeholder_text": "Auto-generated", "auto_complete": false, "read_only": true, "validation_display": "inline", "help_text_position": "below", "label": "Employee ID", "required_indicator": false, "natural_language": "Entity: Employee\nAttribute: employeeId\nControl Type: text_input\nLabel: Employee ID\nDisplay Format: EMP###\nInput Mask: EMP###\nPlaceholder Text: Auto-generated\nRead Only: true\nValidation Display: inline\nHelp Text Position: below", "version": 1, "status": "draft", "created_at": "2025-06-23T09:00:43.639510", "updated_at": "2025-06-23T09:00:43.639510", "created_by": "Tarun", "updated_by": "Tarun", "ui_property_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Attribute with attribute_id A_E13_firstName_1750669243 does not exist"], "parsed_data": {"ui_property_id": "UI1", "entity_id": "E13", "attribute_id": "A_E13_firstName_1750669243", "control_type": "text_input", "display_format": "text", "input_mask": "", "placeholder_text": "Enter first name", "auto_complete": false, "read_only": false, "validation_display": "inline", "help_text_position": "below", "label": "First Name", "required_indicator": true, "natural_language": "Entity: Employee\nAttribute: firstName\nControl Type: text_input\nLabel: First Name\nDisplay Format: text\nPlaceholder Text: Enter first name\nRequired Indicator: true\nValidation Display: inline\nHelp Text Position: below", "version": 1, "status": "draft", "created_at": "2025-06-23T09:00:43.658744", "updated_at": "2025-06-23T09:00:43.658744", "created_by": "Tarun", "updated_by": "Tarun", "ui_property_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Attribute with attribute_id A_E13_lastName_1750669243 does not exist"], "parsed_data": {"ui_property_id": "UI1", "entity_id": "E13", "attribute_id": "A_E13_lastName_1750669243", "control_type": "text_input", "display_format": "text", "input_mask": "", "placeholder_text": "Enter last name", "auto_complete": false, "read_only": false, "validation_display": "inline", "help_text_position": "below", "label": "Last Name", "required_indicator": true, "natural_language": "Entity: Employee\nAttribute: lastName\nControl Type: text_input\nLabel: Last Name\nDisplay Format: text\nPlaceholder Text: Enter last name\nRequired Indicator: true\nValidation Display: inline\nHelp Text Position: below", "version": 1, "status": "draft", "created_at": "2025-06-23T09:00:43.676288", "updated_at": "2025-06-23T09:00:43.676288", "created_by": "Tarun", "updated_by": "Tarun", "ui_property_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Attribute with attribute_id A_E13_email_1750669243 does not exist"], "parsed_data": {"ui_property_id": "UI1", "entity_id": "E13", "attribute_id": "A_E13_email_1750669243", "control_type": "text_input", "display_format": "<EMAIL>", "input_mask": "", "placeholder_text": "Enter email address", "auto_complete": false, "read_only": false, "validation_display": "inline", "help_text_position": "below", "label": "Email", "required_indicator": true, "natural_language": "Entity: Employee\nAttribute: email\nControl Type: text_input\nLabel: Email\nDisplay Format: <EMAIL>\nPlaceholder Text: Enter email address\nRequired Indicator: true\nValidation Display: inline\nHelp Text Position: below", "version": 1, "status": "draft", "created_at": "2025-06-23T09:00:43.699056", "updated_at": "2025-06-23T09:00:43.699056", "created_by": "Tarun", "updated_by": "Tarun", "ui_property_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Attribute with attribute_id A_E13_phone_1750669243 does not exist"], "parsed_data": {"ui_property_id": "UI1", "entity_id": "E13", "attribute_id": "A_E13_phone_1750669243", "control_type": "text_input", "display_format": "+##-###-###-####", "input_mask": "+##-###-###-####", "placeholder_text": "Enter phone number", "auto_complete": false, "read_only": false, "validation_display": "inline", "help_text_position": "below", "label": "Phone", "required_indicator": false, "natural_language": "Entity: Employee\nAttribute: phone\nControl Type: text_input\nLabel: Phone\nDisplay Format: +##-###-###-####\nInput Mask: +##-###-###-####\nPlaceholder Text: Enter phone number\nValidation Display: inline\nHelp Text Position: below", "version": 1, "status": "draft", "created_at": "2025-06-23T09:00:43.721633", "updated_at": "2025-06-23T09:00:43.721633", "created_by": "Tarun", "updated_by": "Tarun", "ui_property_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Attribute with attribute_id A_E13_departmentId_1750669243 does not exist"], "parsed_data": {"ui_property_id": "UI1", "entity_id": "E13", "attribute_id": "A_E13_departmentId_1750669243", "control_type": "dropdown", "display_format": "{departmentName}", "input_mask": "", "placeholder_text": "Select Department", "auto_complete": true, "read_only": false, "validation_display": "inline", "help_text_position": "below", "label": "Department", "required_indicator": true, "natural_language": "Entity: Employee\nAttribute: departmentId\nControl Type: dropdown\nLabel: Department\nDisplay Format: {departmentName}\nPlaceholder Text: Select Department\nAuto Complete: true\nRequired Indicator: true\nValidation Display: inline\nHelp Text Position: below", "version": 1, "status": "draft", "created_at": "2025-06-23T09:00:43.743589", "updated_at": "2025-06-23T09:00:43.743589", "created_by": "Tarun", "updated_by": "Tarun", "ui_property_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Attribute with attribute_id A_E13_managerId_1750669243 does not exist"], "parsed_data": {"ui_property_id": "UI1", "entity_id": "E13", "attribute_id": "A_E13_managerId_1750669243", "control_type": "dropdown", "display_format": "{firstName} {lastName} ({employeeId})", "input_mask": "", "placeholder_text": "Select Manager", "auto_complete": true, "read_only": false, "validation_display": "inline", "help_text_position": "below", "label": "Manager", "required_indicator": false, "natural_language": "Entity: Employee\nAttribute: managerId\nControl Type: dropdown\nLabel: Manager\nDisplay Format: {firstName} {lastName} ({employeeId})\nPlaceholder Text: Select Manager\nAuto Complete: true\nValidation Display: inline\nHelp Text Position: below", "version": 1, "status": "draft", "created_at": "2025-06-23T09:00:43.764414", "updated_at": "2025-06-23T09:00:43.764414", "created_by": "Tarun", "updated_by": "Tarun", "ui_property_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Attribute with attribute_id A_E13_jobTitle_1750669243 does not exist"], "parsed_data": {"ui_property_id": "UI1", "entity_id": "E13", "attribute_id": "A_E13_jobTitle_1750669243", "control_type": "text_input", "display_format": "text", "input_mask": "", "placeholder_text": "Enter job title", "auto_complete": true, "read_only": false, "validation_display": "inline", "help_text_position": "below", "label": "Job Title", "required_indicator": true, "natural_language": "Entity: Employee\nAttribute: jobTitle\nControl Type: text_input\nLabel: Job Title\nDisplay Format: text\nPlaceholder Text: Enter job title\nAuto Complete: true\nRequired Indicator: true\nValidation Display: inline\nHelp Text Position: below", "version": 1, "status": "draft", "created_at": "2025-06-23T09:00:43.790373", "updated_at": "2025-06-23T09:00:43.790373", "created_by": "Tarun", "updated_by": "Tarun", "ui_property_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Attribute with attribute_id A_E13_hireDate_1750669243 does not exist"], "parsed_data": {"ui_property_id": "UI1", "entity_id": "E13", "attribute_id": "A_E13_hireDate_1750669243", "control_type": "date_picker", "display_format": "DD/MM/YYYY", "input_mask": "DD/MM/YYYY", "placeholder_text": "Select hire date", "auto_complete": false, "read_only": false, "validation_display": "inline", "help_text_position": "below", "label": "Hire Date", "required_indicator": true, "natural_language": "Entity: Employee\nAttribute: hireDate\nControl Type: date_picker\nLabel: Hire Date\nDisplay Format: DD/MM/YYYY\nInput Mask: DD/MM/YYYY\nPlaceholder Text: Select hire date\nRequired Indicator: true\nValidation Display: inline\nHelp Text Position: below", "version": 1, "status": "draft", "created_at": "2025-06-23T09:00:43.812160", "updated_at": "2025-06-23T09:00:43.812160", "created_by": "Tarun", "updated_by": "Tarun", "ui_property_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Attribute with attribute_id A_E13_employmentStatus_1750669243 does not exist"], "parsed_data": {"ui_property_id": "UI1", "entity_id": "E13", "attribute_id": "A_E13_employmentStatus_1750669243", "control_type": "dropdown", "display_format": "{status}", "input_mask": "", "placeholder_text": "Select Status", "auto_complete": false, "read_only": false, "validation_display": "inline", "help_text_position": "below", "label": "Employment Status", "required_indicator": true, "natural_language": "Entity: Employee\nAttribute: employmentStatus\nControl Type: dropdown\nLabel: Employment Status\nDisplay Format: {status}\nPlaceholder Text: Select Status\nRequired Indicator: true\nValidation Display: inline\nHelp Text Position: below", "version": 1, "status": "draft", "created_at": "2025-06-23T09:00:43.835606", "updated_at": "2025-06-23T09:00:43.835606", "created_by": "Tarun", "updated_by": "Tarun", "ui_property_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Attribute with attribute_id A_E13_annualLeaveBalance_1750669243 does not exist"], "parsed_data": {"ui_property_id": "UI1", "entity_id": "E13", "attribute_id": "A_E13_annualLeaveBalance_1750669243", "control_type": "number_input", "display_format": "##.# days", "input_mask": "##.#", "placeholder_text": "Enter balance", "auto_complete": false, "read_only": false, "validation_display": "inline", "help_text_position": "below", "label": "Annual Leave Balance", "required_indicator": false, "natural_language": "Entity: Employee\nAttribute: annualLeaveBalance\nControl Type: number_input\nLabel: Annual Leave Balance\nDisplay Format: ##.# days\nInput Mask: ##.#\nPlaceholder Text: Enter balance\nValidation Display: inline\nHelp Text Position: below", "version": 1, "status": "draft", "created_at": "2025-06-23T09:00:43.856812", "updated_at": "2025-06-23T09:00:43.856812", "created_by": "Tarun", "updated_by": "Tarun", "ui_property_status": "new", "changes_detected": []}}, {"success": true, "saved_data": {"ui_property_id": "UI1", "entity_id": "E13", "attribute_id": "E13.At9", "control_type": "number_input", "display_format": "##.# days", "input_mask": "##.#", "placeholder_text": "Enter balance", "auto_complete": false, "read_only": false, "validation_display": "inline", "help_text_position": "below", "label": "Sick Leave Balance", "required_indicator": false, "natural_language": "Entity: Employee\nAttribute: sickLeaveBalance\nControl Type: number_input\nLabel: Sick Leave Balance\nDisplay Format: ##.# days\nInput Mask: ##.#\nPlaceholder Text: Enter balance\nValidation Display: inline\nHelp Text Position: below", "version": 2, "status": "draft", "created_at": "2025-06-23T09:00:43.875747", "updated_at": "2025-06-23T09:00:45.142147", "created_by": "Tarun", "updated_by": "Tarun", "ui_property_status": "new", "changes_detected": [], "_id": "685903f05e423f11979d4093"}, "validation_result": {"structure_errors": [], "required_field_errors": [], "data_type_errors": [], "custom_errors": []}, "dependency_errors": [], "uniqueness_result": {"is_unique": false, "status": "exists_in_mongo", "message": "UI property with ui_property_id UI1 already exists in MongoDB drafts", "existing_document": {"_id": "685903f05e423f11979d4093", "ui_property_id": "UI1", "entity_id": "E13", "attribute_id": "E13.At11", "control_type": "number_input", "display_format": "##.# days", "input_mask": "##.#", "placeholder_text": "Enter entitlement", "auto_complete": false, "read_only": false, "validation_display": "inline", "help_text_position": "below", "label": "Total Leave Entitlement", "required_indicator": false, "natural_language": "Entity: Employee\nAttribute: totalLeaveEntitlement\nControl Type: number_input\nLabel: Total Leave Entitlement\nDisplay Format: ##.# days\nInput Mask: ##.#\nPlaceholder Text: Enter entitlement\nValidation Display: inline\nHelp Text Position: below", "version": 1, "status": "draft", "created_at": "2025-06-23T07:36:16.794690", "updated_at": "2025-06-23T07:36:16.794698", "created_by": "Tarun", "updated_by": "Tarun", "ui_property_status": "new", "changes_detected": []}}}, {"success": true, "saved_data": {"ui_property_id": "UI1", "entity_id": "E13", "attribute_id": "E13.At10", "control_type": "number_input", "display_format": "##.# days", "input_mask": "##.#", "placeholder_text": "Enter balance", "auto_complete": false, "read_only": false, "validation_display": "inline", "help_text_position": "below", "label": "Personal Leave Balance", "required_indicator": false, "natural_language": "Entity: Employee\nAttribute: personalLeaveBalance\nControl Type: number_input\nLabel: Personal Leave Balance\nDisplay Format: ##.# days\nInput Mask: ##.#\nPlaceholder Text: Enter balance\nValidation Display: inline\nHelp Text Position: below", "version": 3, "status": "draft", "created_at": "2025-06-23T09:00:43.892474", "updated_at": "2025-06-23T09:00:45.165924", "created_by": "Tarun", "updated_by": "Tarun", "ui_property_status": "new", "changes_detected": [], "_id": "685903f05e423f11979d4093"}, "validation_result": {"structure_errors": [], "required_field_errors": [], "data_type_errors": [], "custom_errors": []}, "dependency_errors": [], "uniqueness_result": {"is_unique": false, "status": "exists_in_mongo", "message": "UI property with ui_property_id UI1 already exists in MongoDB drafts", "existing_document": {"_id": "685903f05e423f11979d4093", "ui_property_id": "UI1", "entity_id": "E13", "attribute_id": "E13.At9", "control_type": "number_input", "display_format": "##.# days", "input_mask": "##.#", "placeholder_text": "Enter balance", "auto_complete": false, "read_only": false, "validation_display": "inline", "help_text_position": "below", "label": "Sick Leave Balance", "required_indicator": false, "natural_language": "Entity: Employee\nAttribute: sickLeaveBalance\nControl Type: number_input\nLabel: Sick Leave Balance\nDisplay Format: ##.# days\nInput Mask: ##.#\nPlaceholder Text: Enter balance\nValidation Display: inline\nHelp Text Position: below", "version": 2, "status": "draft", "created_at": "2025-06-23T09:00:43.875747", "updated_at": "2025-06-23T09:00:45.142147", "created_by": "Tarun", "updated_by": "Tarun", "ui_property_status": "new", "changes_detected": []}}}, {"success": true, "saved_data": {"ui_property_id": "UI1", "entity_id": "E13", "attribute_id": "E13.At11", "control_type": "number_input", "display_format": "##.# days", "input_mask": "##.#", "placeholder_text": "Enter entitlement", "auto_complete": false, "read_only": false, "validation_display": "inline", "help_text_position": "below", "label": "Total Leave Entitlement", "required_indicator": false, "natural_language": "Entity: Employee\nAttribute: totalLeaveEntitlement\nControl Type: number_input\nLabel: Total Leave Entitlement\nDisplay Format: ##.# days\nInput Mask: ##.#\nPlaceholder Text: Enter entitlement\nValidation Display: inline\nHelp Text Position: below", "version": 4, "status": "draft", "created_at": "2025-06-23T09:00:43.903023", "updated_at": "2025-06-23T09:00:45.190172", "created_by": "Tarun", "updated_by": "Tarun", "ui_property_status": "existing", "changes_detected": [], "_id": "685903f05e423f11979d4093"}, "validation_result": {"structure_errors": [], "required_field_errors": [], "data_type_errors": [], "custom_errors": []}, "dependency_errors": [], "uniqueness_result": {"is_unique": false, "status": "exists_in_mongo", "message": "UI property with ui_property_id UI1 already exists in MongoDB drafts", "existing_document": {"_id": "685903f05e423f11979d4093", "ui_property_id": "UI1", "entity_id": "E13", "attribute_id": "E13.At10", "control_type": "number_input", "display_format": "##.# days", "input_mask": "##.#", "placeholder_text": "Enter balance", "auto_complete": false, "read_only": false, "validation_display": "inline", "help_text_position": "below", "label": "Personal Leave Balance", "required_indicator": false, "natural_language": "Entity: Employee\nAttribute: personalLeaveBalance\nControl Type: number_input\nLabel: Personal Leave Balance\nDisplay Format: ##.# days\nInput Mask: ##.#\nPlaceholder Text: Enter balance\nValidation Display: inline\nHelp Text Position: below", "version": 3, "status": "draft", "created_at": "2025-06-23T09:00:43.892474", "updated_at": "2025-06-23T09:00:45.165924", "created_by": "Tarun", "updated_by": "Tarun", "ui_property_status": "new", "changes_detected": []}}}, {"success": false, "errors": ["Entity with entity_id E_LeaveType_1750669243 does not exist", "Attribute with attribute_id A_E_LeaveType_1750669243_leaveTypeId_1750669243 does not exist"], "parsed_data": {"ui_property_id": "UI1", "entity_id": "E_LeaveType_1750669243", "attribute_id": "A_E_LeaveType_1750669243_leaveTypeId_1750669243", "control_type": "text_input", "display_format": "LT###", "input_mask": "LT###", "placeholder_text": "Auto-generated", "auto_complete": false, "read_only": true, "validation_display": "inline", "help_text_position": "below", "label": "Leave Type ID", "required_indicator": false, "natural_language": "Entity: LeaveType\nAttribute: leaveTypeId\nControl Type: text_input\nLabel: Leave Type ID\nDisplay Format: LT###\nInput Mask: LT###\nPlaceholder Text: Auto-generated\nRead Only: true\nValidation Display: inline\nHelp Text Position: below", "version": 1, "status": "draft", "created_at": "2025-06-23T09:00:43.934216", "updated_at": "2025-06-23T09:00:43.934216", "created_by": "Tarun", "updated_by": "Tarun", "ui_property_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Entity with entity_id E_LeaveType_1750669243 does not exist", "Attribute with attribute_id A_E_LeaveType_1750669243_leaveTypeName_1750669243 does not exist"], "parsed_data": {"ui_property_id": "UI1", "entity_id": "E_LeaveType_1750669243", "attribute_id": "A_E_LeaveType_1750669243_leaveTypeName_1750669243", "control_type": "text_input", "display_format": "text", "input_mask": "", "placeholder_text": "Enter leave type name", "auto_complete": false, "read_only": false, "validation_display": "inline", "help_text_position": "below", "label": "Leave Type Name", "required_indicator": true, "natural_language": "Entity: LeaveType\nAttribute: leaveTypeName\nControl Type: text_input\nLabel: Leave Type Name\nDisplay Format: text\nPlaceholder Text: Enter leave type name\nRequired Indicator: true\nValidation Display: inline\nHelp Text Position: below", "version": 1, "status": "draft", "created_at": "2025-06-23T09:00:43.965893", "updated_at": "2025-06-23T09:00:43.965893", "created_by": "Tarun", "updated_by": "Tarun", "ui_property_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Entity with entity_id E_LeaveType_1750669243 does not exist", "Attribute with attribute_id A_E_LeaveType_1750669243_description_1750669243 does not exist"], "parsed_data": {"ui_property_id": "UI1", "entity_id": "E_LeaveType_1750669243", "attribute_id": "A_E_LeaveType_1750669243_description_1750669243", "control_type": "textarea", "display_format": "text", "input_mask": "", "placeholder_text": "Enter description", "auto_complete": false, "read_only": false, "validation_display": "inline", "help_text_position": "below", "label": "Description", "required_indicator": false, "natural_language": "Entity: LeaveType\nAttribute: description\nControl Type: textarea\nLabel: Description\nDisplay Format: text\nPlaceholder Text: Enter description\nValidation Display: inline\nHelp Text Position: below", "version": 1, "status": "draft", "created_at": "2025-06-23T09:00:43.997795", "updated_at": "2025-06-23T09:00:43.997795", "created_by": "Tarun", "updated_by": "Tarun", "ui_property_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Entity with entity_id E_LeaveType_1750669244 does not exist", "Attribute with attribute_id A_E_LeaveType_1750669244_maxDaysPerYear_1750669244 does not exist"], "parsed_data": {"ui_property_id": "UI1", "entity_id": "E_LeaveType_1750669244", "attribute_id": "A_E_LeaveType_1750669244_maxDaysPerYear_1750669244", "control_type": "number_input", "display_format": "### days", "input_mask": "###", "placeholder_text": "Enter maximum days", "auto_complete": false, "read_only": false, "validation_display": "inline", "help_text_position": "below", "label": "Max Days Per Year", "required_indicator": true, "natural_language": "Entity: LeaveType\nAttribute: maxDaysPerYear\nControl Type: number_input\nLabel: Max Days Per Year\nDisplay Format: ### days\nInput Mask: ###\nPlaceholder Text: Enter maximum days\nRequired Indicator: true\nValidation Display: inline\nHelp Text Position: below", "version": 1, "status": "draft", "created_at": "2025-06-23T09:00:44.029172", "updated_at": "2025-06-23T09:00:44.029172", "created_by": "Tarun", "updated_by": "Tarun", "ui_property_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Entity with entity_id E_LeaveType_1750669244 does not exist", "Attribute with attribute_id A_E_LeaveType_1750669244_requiresDocumentation_1750669244 does not exist"], "parsed_data": {"ui_property_id": "UI1", "entity_id": "E_LeaveType_1750669244", "attribute_id": "A_E_LeaveType_1750669244_requiresDocumentation_1750669244", "control_type": "toggle", "display_format": "Yes/No", "input_mask": "", "placeholder_text": "", "auto_complete": false, "read_only": false, "validation_display": "inline", "help_text_position": "below", "label": "Requires Documentation", "required_indicator": false, "natural_language": "Entity: LeaveType\nAttribute: requiresDocumentation\nControl Type: toggle\nLabel: Requires Documentation\nDisplay Format: Yes/No\nValidation Display: inline\nHelp Text Position: below", "version": 1, "status": "draft", "created_at": "2025-06-23T09:00:44.058544", "updated_at": "2025-06-23T09:00:44.058544", "created_by": "Tarun", "updated_by": "Tarun", "ui_property_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Entity with entity_id E_LeaveType_1750669244 does not exist", "Attribute with attribute_id A_E_LeaveType_1750669244_advanceNoticeDays_1750669244 does not exist"], "parsed_data": {"ui_property_id": "UI1", "entity_id": "E_LeaveType_1750669244", "attribute_id": "A_E_LeaveType_1750669244_advanceNoticeDays_1750669244", "control_type": "number_input", "display_format": "### days", "input_mask": "###", "placeholder_text": "Enter notice days", "auto_complete": false, "read_only": false, "validation_display": "inline", "help_text_position": "below", "label": "Advance Notice Days", "required_indicator": false, "natural_language": "Entity: LeaveType\nAttribute: advanceNoticeDays\nControl Type: number_input\nLabel: Advance Notice Days\nDisplay Format: ### days\nInput Mask: ###\nPlaceholder Text: Enter notice days\nValidation Display: inline\nHelp Text Position: below", "version": 1, "status": "draft", "created_at": "2025-06-23T09:00:44.088362", "updated_at": "2025-06-23T09:00:44.088362", "created_by": "Tarun", "updated_by": "Tarun", "ui_property_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Entity with entity_id E_LeaveType_1750669244 does not exist", "Attribute with attribute_id A_E_LeaveType_1750669244_isCarryForward_1750669244 does not exist"], "parsed_data": {"ui_property_id": "UI1", "entity_id": "E_LeaveType_1750669244", "attribute_id": "A_E_LeaveType_1750669244_isCarryForward_1750669244", "control_type": "toggle", "display_format": "Yes/No", "input_mask": "", "placeholder_text": "", "auto_complete": false, "read_only": false, "validation_display": "inline", "help_text_position": "below", "label": "Is Carry Forward", "required_indicator": false, "natural_language": "Entity: LeaveType\nAttribute: isCarryForward\nControl Type: toggle\nLabel: Is Carry Forward\nDisplay Format: Yes/No\nValidation Display: inline\nHelp Text Position: below", "version": 1, "status": "draft", "created_at": "2025-06-23T09:00:44.122482", "updated_at": "2025-06-23T09:00:44.122482", "created_by": "Tarun", "updated_by": "Tarun", "ui_property_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Entity with entity_id E_LeaveType_1750669244 does not exist", "Attribute with attribute_id A_E_LeaveType_1750669244_status_1750669244 does not exist"], "parsed_data": {"ui_property_id": "UI1", "entity_id": "E_LeaveType_1750669244", "attribute_id": "A_E_LeaveType_1750669244_status_1750669244", "control_type": "dropdown", "display_format": "{status}", "input_mask": "", "placeholder_text": "Select Status", "auto_complete": false, "read_only": false, "validation_display": "inline", "help_text_position": "below", "label": "Status", "required_indicator": false, "natural_language": "Entity: LeaveType\nAttribute: status\nControl Type: dropdown\nLabel: Status\nDisplay Format: {status}\nPlaceholder Text: Select Status\nValidation Display: inline\nHelp Text Position: below", "version": 1, "status": "draft", "created_at": "2025-06-23T09:00:44.151166", "updated_at": "2025-06-23T09:00:44.151166", "created_by": "Tarun", "updated_by": "Tarun", "ui_property_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Entity with entity_id E_LeaveSubType_1750669244 does not exist", "Attribute with attribute_id A_E_LeaveSubType_1750669244_leaveSubTypeId_1750669244 does not exist"], "parsed_data": {"ui_property_id": "UI1", "entity_id": "E_LeaveSubType_1750669244", "attribute_id": "A_E_LeaveSubType_1750669244_leaveSubTypeId_1750669244", "control_type": "text_input", "display_format": "LST###", "input_mask": "LST###", "placeholder_text": "Auto-generated", "auto_complete": false, "read_only": true, "validation_display": "inline", "help_text_position": "below", "label": "Leave Sub-Type ID", "required_indicator": false, "natural_language": "Entity: LeaveSubType\nAttribute: leaveSubTypeId\nControl Type: text_input\nLabel: Leave Sub-Type ID\nDisplay Format: LST###\nInput Mask: LST###\nPlaceholder Text: Auto-generated\nRead Only: true\nValidation Display: inline\nHelp Text Position: below", "version": 1, "status": "draft", "created_at": "2025-06-23T09:00:44.180640", "updated_at": "2025-06-23T09:00:44.180640", "created_by": "Tarun", "updated_by": "Tarun", "ui_property_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Entity with entity_id E_LeaveSubType_1750669244 does not exist", "Attribute with attribute_id A_E_LeaveSubType_1750669244_leaveTypeName_1750669244 does not exist"], "parsed_data": {"ui_property_id": "UI1", "entity_id": "E_LeaveSubType_1750669244", "attribute_id": "A_E_LeaveSubType_1750669244_leaveTypeName_1750669244", "control_type": "dropdown", "display_format": "{leaveTypeName}", "input_mask": "", "placeholder_text": "Select Leave Type", "auto_complete": true, "read_only": false, "validation_display": "inline", "help_text_position": "below", "label": "Leave Type", "required_indicator": true, "natural_language": "Entity: LeaveSubType\nAttribute: leaveTypeName\nControl Type: dropdown\nLabel: Leave Type\nDisplay Format: {leaveTypeName}\nPlaceholder Text: Select Leave Type\nAuto Complete: true\nRequired Indicator: true\nValidation Display: inline\nHelp Text Position: below", "version": 1, "status": "draft", "created_at": "2025-06-23T09:00:44.207532", "updated_at": "2025-06-23T09:00:44.207532", "created_by": "Tarun", "updated_by": "Tarun", "ui_property_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Entity with entity_id E_LeaveSubType_1750669244 does not exist", "Attribute with attribute_id A_E_LeaveSubType_1750669244_leaveSubTypeName_1750669244 does not exist"], "parsed_data": {"ui_property_id": "UI1", "entity_id": "E_LeaveSubType_1750669244", "attribute_id": "A_E_LeaveSubType_1750669244_leaveSubTypeName_1750669244", "control_type": "text_input", "display_format": "text", "input_mask": "", "placeholder_text": "Enter sub-type name", "auto_complete": false, "read_only": false, "validation_display": "inline", "help_text_position": "below", "label": "Leave Sub-Type Name", "required_indicator": true, "natural_language": "Entity: LeaveSubType\nAttribute: leaveSubTypeName\nControl Type: text_input\nLabel: Leave Sub-Type Name\nDisplay Format: text\nPlaceholder Text: Enter sub-type name\nRequired Indicator: true\nValidation Display: inline\nHelp Text Position: below", "version": 1, "status": "draft", "created_at": "2025-06-23T09:00:44.236466", "updated_at": "2025-06-23T09:00:44.236466", "created_by": "Tarun", "updated_by": "Tarun", "ui_property_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Entity with entity_id E_LeaveSubType_1750669244 does not exist", "Attribute with attribute_id A_E_LeaveSubType_1750669244_description_1750669244 does not exist"], "parsed_data": {"ui_property_id": "UI1", "entity_id": "E_LeaveSubType_1750669244", "attribute_id": "A_E_LeaveSubType_1750669244_description_1750669244", "control_type": "textarea", "display_format": "text", "input_mask": "", "placeholder_text": "Enter description", "auto_complete": false, "read_only": false, "validation_display": "inline", "help_text_position": "below", "label": "Description", "required_indicator": false, "natural_language": "Entity: LeaveSubType\nAttribute: description\nControl Type: textarea\nLabel: Description\nDisplay Format: text\nPlaceholder Text: Enter description\nValidation Display: inline\nHelp Text Position: below", "version": 1, "status": "draft", "created_at": "2025-06-23T09:00:44.263106", "updated_at": "2025-06-23T09:00:44.263106", "created_by": "Tarun", "updated_by": "Tarun", "ui_property_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Entity with entity_id E_LeaveSubType_1750669244 does not exist", "Attribute with attribute_id A_E_LeaveSubType_1750669244_maxDaysPerRequest_1750669244 does not exist"], "parsed_data": {"ui_property_id": "UI1", "entity_id": "E_LeaveSubType_1750669244", "attribute_id": "A_E_LeaveSubType_1750669244_maxDaysPerRequest_1750669244", "control_type": "number_input", "display_format": "### days", "input_mask": "###", "placeholder_text": "Enter maximum days", "auto_complete": false, "read_only": false, "validation_display": "inline", "help_text_position": "below", "label": "Max Days Per Request", "required_indicator": true, "natural_language": "Entity: LeaveSubType\nAttribute: maxDaysPerRequest\nControl Type: number_input\nLabel: Max Days Per Request\nDisplay Format: ### days\nInput Mask: ###\nPlaceholder Text: Enter maximum days\nRequired Indicator: true\nValidation Display: inline\nHelp Text Position: below", "version": 1, "status": "draft", "created_at": "2025-06-23T09:00:44.291785", "updated_at": "2025-06-23T09:00:44.291785", "created_by": "Tarun", "updated_by": "Tarun", "ui_property_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Entity with entity_id E_LeaveSubType_1750669244 does not exist", "Attribute with attribute_id A_E_LeaveSubType_1750669244_requiresManagerApproval_1750669244 does not exist"], "parsed_data": {"ui_property_id": "UI1", "entity_id": "E_LeaveSubType_1750669244", "attribute_id": "A_E_LeaveSubType_1750669244_requiresManagerApproval_1750669244", "control_type": "toggle", "display_format": "Yes/No", "input_mask": "", "placeholder_text": "", "auto_complete": false, "read_only": false, "validation_display": "inline", "help_text_position": "below", "label": "Requires Manager Approval", "required_indicator": false, "natural_language": "Entity: LeaveSubType\nAttribute: requiresManagerApproval\nControl Type: toggle\nLabel: Requires Manager Approval\nDisplay Format: Yes/No\nValidation Display: inline\nHelp Text Position: below", "version": 1, "status": "draft", "created_at": "2025-06-23T09:00:44.322392", "updated_at": "2025-06-23T09:00:44.322392", "created_by": "Tarun", "updated_by": "Tarun", "ui_property_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Entity with entity_id E_LeaveSubType_1750669244 does not exist", "Attribute with attribute_id A_E_LeaveSubType_1750669244_status_1750669244 does not exist"], "parsed_data": {"ui_property_id": "UI1", "entity_id": "E_LeaveSubType_1750669244", "attribute_id": "A_E_LeaveSubType_1750669244_status_1750669244", "control_type": "dropdown", "display_format": "{status}", "input_mask": "", "placeholder_text": "Select Status", "auto_complete": false, "read_only": false, "validation_display": "inline", "help_text_position": "below", "label": "Status", "required_indicator": false, "natural_language": "Entity: LeaveSubType\nAttribute: status\nControl Type: dropdown\nLabel: Status\nDisplay Format: {status}\nPlaceholder Text: Select Status\nValidation Display: inline\nHelp Text Position: below", "version": 1, "status": "draft", "created_at": "2025-06-23T09:00:44.348496", "updated_at": "2025-06-23T09:00:44.348496", "created_by": "Tarun", "updated_by": "Tarun", "ui_property_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Entity with entity_id E_Constants_1750669244 does not exist", "Attribute with attribute_id A_E_Constants_1750669244_constantId_1750669244 does not exist"], "parsed_data": {"ui_property_id": "UI1", "entity_id": "E_Constants_1750669244", "attribute_id": "A_E_Constants_1750669244_constantId_1750669244", "control_type": "text_input", "display_format": "CONST###", "input_mask": "CONST###", "placeholder_text": "Auto-generated", "auto_complete": false, "read_only": true, "validation_display": "inline", "help_text_position": "below", "label": "Constant ID", "required_indicator": false, "natural_language": "Entity: Constants\nAttribute: constantId\nControl Type: text_input\nLabel: Constant ID\nDisplay Format: CONST###\nInput Mask: CONST###\nPlaceholder Text: Auto-generated\nRead Only: true\nValidation Display: inline\nHelp Text Position: below", "version": 1, "status": "draft", "created_at": "2025-06-23T09:00:44.379480", "updated_at": "2025-06-23T09:00:44.379480", "created_by": "Tarun", "updated_by": "Tarun", "ui_property_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Entity with entity_id E_Constants_1750669244 does not exist", "Attribute with attribute_id A_E_Constants_1750669244_attribute_1750669244 does not exist"], "parsed_data": {"ui_property_id": "UI1", "entity_id": "E_Constants_1750669244", "attribute_id": "A_E_Constants_1750669244_attribute_1750669244", "control_type": "text_input", "display_format": "text", "input_mask": "", "placeholder_text": "Enter attribute name", "auto_complete": false, "read_only": false, "validation_display": "inline", "help_text_position": "below", "label": "Attribute", "required_indicator": true, "natural_language": "Entity: Constants\nAttribute: attribute\nControl Type: text_input\nLabel: Attribute\nDisplay Format: text\nPlaceholder Text: Enter attribute name\nRequired Indicator: true\nValidation Display: inline\nHelp Text Position: below", "version": 1, "status": "draft", "created_at": "2025-06-23T09:00:44.406943", "updated_at": "2025-06-23T09:00:44.406943", "created_by": "Tarun", "updated_by": "Tarun", "ui_property_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Entity with entity_id E_Constants_1750669244 does not exist", "Attribute with attribute_id A_E_Constants_1750669244_value_1750669244 does not exist"], "parsed_data": {"ui_property_id": "UI1", "entity_id": "E_Constants_1750669244", "attribute_id": "A_E_Constants_1750669244_value_1750669244", "control_type": "text_input", "display_format": "text", "input_mask": "", "placeholder_text": "Enter value", "auto_complete": false, "read_only": false, "validation_display": "inline", "help_text_position": "below", "label": "Value", "required_indicator": true, "natural_language": "Entity: Constants\nAttribute: value\nControl Type: text_input\nLabel: Value\nDisplay Format: text\nPlaceholder Text: Enter value\nRequired Indicator: true\nValidation Display: inline\nHelp Text Position: below", "version": 1, "status": "draft", "created_at": "2025-06-23T09:00:44.437168", "updated_at": "2025-06-23T09:00:44.437168", "created_by": "Tarun", "updated_by": "Tarun", "ui_property_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Entity with entity_id E_Constants_1750669244 does not exist", "Attribute with attribute_id A_E_Constants_1750669244_description_1750669244 does not exist"], "parsed_data": {"ui_property_id": "UI1", "entity_id": "E_Constants_1750669244", "attribute_id": "A_E_Constants_1750669244_description_1750669244", "control_type": "textarea", "display_format": "text", "input_mask": "", "placeholder_text": "Enter description", "auto_complete": false, "read_only": false, "validation_display": "inline", "help_text_position": "below", "label": "Description", "required_indicator": false, "natural_language": "Entity: Constants\nAttribute: description\nControl Type: textarea\nLabel: Description\nDisplay Format: text\nPlaceholder Text: Enter description\nValidation Display: inline\nHelp Text Position: below", "version": 1, "status": "draft", "created_at": "2025-06-23T09:00:44.466162", "updated_at": "2025-06-23T09:00:44.466162", "created_by": "Tarun", "updated_by": "Tarun", "ui_property_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Entity with entity_id E_Constants_1750669244 does not exist", "Attribute with attribute_id A_E_Constants_1750669244_dataType_1750669244 does not exist"], "parsed_data": {"ui_property_id": "UI1", "entity_id": "E_Constants_1750669244", "attribute_id": "A_E_Constants_1750669244_dataType_1750669244", "control_type": "dropdown", "display_format": "{dataType}", "input_mask": "", "placeholder_text": "Select Data Type", "auto_complete": false, "read_only": false, "validation_display": "inline", "help_text_position": "below", "label": "Data Type", "required_indicator": true, "natural_language": "Entity: Constants\nAttribute: dataType\nControl Type: dropdown\nLabel: Data Type\nDisplay Format: {dataType}\nPlaceholder Text: Select Data Type\nRequired Indicator: true\nValidation Display: inline\nHelp Text Position: below", "version": 1, "status": "draft", "created_at": "2025-06-23T09:00:44.498198", "updated_at": "2025-06-23T09:00:44.498198", "created_by": "Tarun", "updated_by": "Tarun", "ui_property_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Entity with entity_id E_Constants_1750669244 does not exist", "Attribute with attribute_id A_E_Constants_1750669244_status_1750669244 does not exist"], "parsed_data": {"ui_property_id": "UI1", "entity_id": "E_Constants_1750669244", "attribute_id": "A_E_Constants_1750669244_status_1750669244", "control_type": "dropdown", "display_format": "{status}", "input_mask": "", "placeholder_text": "Select Status", "auto_complete": false, "read_only": false, "validation_display": "inline", "help_text_position": "below", "label": "Status", "required_indicator": false, "natural_language": "Entity: Constants\nAttribute: status\nControl Type: dropdown\nLabel: Status\nDisplay Format: {status}\nPlaceholder Text: Select Status\nValidation Display: inline\nHelp Text Position: below", "version": 1, "status": "draft", "created_at": "2025-06-23T09:00:44.527986", "updated_at": "2025-06-23T09:00:44.527986", "created_by": "Tarun", "updated_by": "Tarun", "ui_property_status": "new", "changes_detected": []}}], "operation": "parse_validate_mongosave", "total_ui_properties": 50}, "status": "success"}
{"timestamp": "2025-06-23T09:35:09.841983", "endpoint": "parse-validate-mongosave/ui-properties", "input": {"natural_language": "Tenant: Acme Corp\n\nEntity.Attribute | Control Type | Display Format | Input Mask | Placeholder Text | Auto Complete | Read Only | Validation Display | Help Text Position | Label | Required Indicator\n\nEmployee.firstName | text_input | text | | Enter first name | false | false | inline | below | First Name | true", "tenant_id": "T2", "edit_draft": true, "edit_temp": false, "edit_production": false}, "output": {"success": true, "ui_property_results": [{"success": false, "errors": ["Entity with entity_id E_Employee_1750671309 does not exist", "Attribute with attribute_id A_E_Employee_1750671309_firstName_1750671309 does not exist"], "parsed_data": {"ui_property_id": "UI1", "entity_id": "E_Employee_1750671309", "attribute_id": "A_E_Employee_1750671309_firstName_1750671309", "control_type": "text_input", "display_format": "text", "input_mask": "", "placeholder_text": "Enter first name", "auto_complete": false, "read_only": false, "validation_display": "inline", "help_text_position": "below", "label": "First Name", "required_indicator": true, "natural_language": "Entity: Employee\nAttribute: firstName\nControl Type: text_input\nLabel: First Name\nDisplay Format: text\nPlaceholder Text: Enter first name\nRequired Indicator: true\nValidation Display: inline\nHelp Text Position: below", "version": 1, "status": "draft", "created_at": "2025-06-23T09:35:09.817316", "updated_at": "2025-06-23T09:35:09.817316", "created_by": "Tarun", "updated_by": "Tarun", "ui_property_status": "new", "changes_detected": []}}], "operation": "parse_validate_mongosave", "total_ui_properties": 1}, "status": "success"}
{"timestamp": "2025-06-23T10:46:49.709137", "endpoint": "parse-validate-mongosave/ui-properties", "input": {"natural_language": "Tenant: Acme Corp\n\nEntity: LeaveApplication\n\nUI Properties:\nProperty Name | Property Type | Value | Description | Applies To\n\nform_layout | layout | vertical | Vertical form layout for leave application | form\n\nfield_order | order | leaveId,employeeId,startDate,endDate,reason | Order of fields in the form | form\n\nvalidation_style | style | inline | Show validation errors inline | form\n\nsubmit_button_text | text | Submit Leave Request | Text for submit button | button\n\ncancel_button_text | text | Cancel | Text for cancel button | button\n\npage_title | title | Leave Application Form | Page title for the form | page", "tenant_id": "T_ACME_CORP", "edit_draft": false, "edit_temp": false, "edit_production": false}, "output": {"success": true, "ui_property_results": [], "operation": "parse_validate_mongosave", "total_ui_properties": 0}, "status": "success"}
{"timestamp": "2025-06-23T14:07:59.379056", "endpoint": "parse-validate-mongosave/ui-properties", "input": {"natural_language": "Product form should display name as text input and price as currency input", "tenant_id": "tenant_123", "edit_draft": false, "edit_temp": false, "edit_production": false}, "output": {"success": true, "ui_property_results": [], "operation": "parse_validate_mongosave", "total_ui_properties": 0}, "status": "success"}
