=== Command output log - 2025-05-13 18:00:43.102036 ===


=== Running command: python3 reset_database_direct.py ===

2025-05-13 18:00:43,136 - reset_database_direct - INFO - Resetting database schema workflow_temp
2025-05-13 18:00:43,147 - reset_database_direct - INFO - Dropped table workflow_temp.entity_attributes
2025-05-13 18:00:43,148 - reset_database_direct - INFO - Dropped table workflow_temp.entity_attribute_metadata
2025-05-13 18:00:43,149 - reset_database_direct - INFO - Dropped table workflow_temp.entity_relationships
2025-05-13 18:00:43,150 - reset_database_direct - INFO - Dropped table workflow_temp.entity_business_rules
2025-05-13 18:00:43,150 - reset_database_direct - INFO - Dropped table workflow_temp.entity_lifecycle_management
2025-05-13 18:00:43,151 - reset_database_direct - INFO - Dropped table workflow_temp.e1_employee
2025-05-13 18:00:43,152 - reset_database_direct - INFO - Dropped table workflow_temp.e2_department
2025-05-13 18:00:43,153 - reset_database_direct - INFO - Dropped table workflow_temp.e3_leavetype
2025-05-13 18:00:43,153 - reset_database_direct - INFO - Dropped table workflow_temp.e4_leaveapplication
2025-05-13 18:00:43,154 - reset_database_direct - INFO - Dropped table workflow_temp.e5_calendarevent
2025-05-13 18:00:43,166 - reset_database_direct - INFO - Truncated table workflow_temp.books
2025-05-13 18:00:43,167 - reset_database_direct - INFO - Truncated table workflow_temp.objective_permissions
2025-05-13 18:00:43,170 - reset_database_direct - INFO - Truncated table workflow_temp.input_stack
2025-05-13 18:00:43,175 - reset_database_direct - INFO - Truncated table workflow_temp.lo_input_items
2025-05-13 18:00:43,178 - reset_database_direct - INFO - Truncated table workflow_temp.permission_types
2025-05-13 18:00:43,179 - reset_database_direct - INFO - Truncated table workflow_temp.chapters
2025-05-13 18:00:43,181 - reset_database_direct - INFO - Truncated table workflow_temp.lo_output_stack
2025-05-13 18:00:43,183 - reset_database_direct - INFO - Truncated table workflow_temp.attribute_ui_controls
2025-05-13 18:00:43,183 - reset_database_direct - INFO - Truncated table workflow_temp.organizational_units
2025-05-13 18:00:43,185 - reset_database_direct - INFO - Truncated table workflow_temp.lo_output_items
2025-05-13 18:00:43,186 - reset_database_direct - INFO - Truncated table workflow_temp.agent_stack
2025-05-13 18:00:43,187 - reset_database_direct - INFO - Truncated table workflow_temp.conditional_success_messages
2025-05-13 18:00:43,188 - reset_database_direct - INFO - Truncated table workflow_temp.prescription
2025-05-13 18:00:43,190 - reset_database_direct - INFO - Truncated table workflow_temp.lo_data_mapping_stack
2025-05-13 18:00:43,191 - reset_database_direct - INFO - Truncated table workflow_temp.agent_rights
2025-05-13 18:00:43,196 - reset_database_direct - INFO - Truncated table workflow_temp.lo_input_stack
2025-05-13 18:00:43,197 - reset_database_direct - INFO - Truncated table workflow_temp.lo_dependencies
2025-05-13 18:00:43,199 - reset_database_direct - INFO - Truncated table workflow_temp.output_stack
2025-05-13 18:00:43,202 - reset_database_direct - INFO - Truncated table workflow_temp.roles
2025-05-13 18:00:43,204 - reset_database_direct - INFO - Truncated table workflow_temp.data_mapping_stack
2025-05-13 18:00:43,205 - reset_database_direct - INFO - Truncated table workflow_temp.lo_input_execution
2025-05-13 18:00:43,206 - reset_database_direct - INFO - Truncated table workflow_temp.role
2025-05-13 18:00:43,208 - reset_database_direct - INFO - Truncated table workflow_temp.go_performance_metrics
2025-05-13 18:00:43,209 - reset_database_direct - INFO - Truncated table workflow_temp.output_items
2025-05-13 18:00:43,210 - reset_database_direct - INFO - Truncated table workflow_temp.success_messages
2025-05-13 18:00:43,211 - reset_database_direct - INFO - Truncated table workflow_temp.attribute_enum_values
2025-05-13 18:00:43,213 - reset_database_direct - INFO - Truncated table workflow_temp.lo_nested_functions
2025-05-13 18:00:43,214 - reset_database_direct - INFO - Truncated table workflow_temp.dropdown_data_sources
2025-05-13 18:00:43,215 - reset_database_direct - INFO - Truncated table workflow_temp.attribute_validations
2025-05-13 18:00:43,217 - reset_database_direct - INFO - Truncated table workflow_temp.lo_system_functions
2025-05-13 18:00:43,217 - reset_database_direct - INFO - Truncated table workflow_temp.terminal_pathways
2025-05-13 18:00:43,218 - reset_database_direct - INFO - Truncated table workflow_temp.lo_data_mappings
2025-05-13 18:00:43,285 - reset_database_direct - INFO - Truncated table workflow_temp.tenants
2025-05-13 18:00:43,288 - reset_database_direct - INFO - Truncated table workflow_temp.ui_stack
2025-05-13 18:00:43,289 - reset_database_direct - INFO - Truncated table workflow_temp.lo_input_validations
2025-05-13 18:00:43,290 - reset_database_direct - INFO - Truncated table workflow_temp.user_organizations
2025-05-13 18:00:43,331 - reset_database_direct - INFO - Truncated table workflow_temp.global_objectives
2025-05-13 18:00:43,332 - reset_database_direct - INFO - Truncated table workflow_temp.permission_capabilities
2025-05-13 18:00:43,333 - reset_database_direct - INFO - Truncated table workflow_temp.user
2025-05-13 18:00:43,335 - reset_database_direct - INFO - Truncated table workflow_temp.user_role
2025-05-13 18:00:43,335 - reset_database_direct - INFO - Truncated table workflow_temp.role_inheritance
2025-05-13 18:00:43,338 - reset_database_direct - INFO - Truncated table workflow_temp.workflow_instances
2025-05-13 18:00:43,340 - reset_database_direct - INFO - Truncated table workflow_temp.calculated_fields
2025-05-13 18:00:43,341 - reset_database_direct - INFO - Truncated table workflow_temp.data_mappings
2025-05-13 18:00:43,342 - reset_database_direct - INFO - Truncated table workflow_temp.entities
2025-05-13 18:00:43,343 - reset_database_direct - INFO - Truncated table workflow_temp.mapping_rules
2025-05-13 18:00:43,345 - reset_database_direct - INFO - Truncated table workflow_temp.output_triggers
2025-05-13 18:00:43,345 - reset_database_direct - INFO - Truncated table workflow_temp.go_lo_mapping
2025-05-13 18:00:43,347 - reset_database_direct - INFO - Truncated table workflow_temp.lo_output_execution
2025-05-13 18:00:43,347 - reset_database_direct - INFO - Truncated table workflow_temp.metrics_aggregation
2025-05-13 18:00:43,348 - reset_database_direct - INFO - Truncated table workflow_temp.metrics_reporting
2025-05-13 18:00:43,349 - reset_database_direct - INFO - Truncated table workflow_temp.user_roles
2025-05-13 18:00:43,367 - reset_database_direct - INFO - Truncated table workflow_temp.local_objectives
2025-05-13 18:00:43,368 - reset_database_direct - INFO - Truncated table workflow_temp.input_data_sources
2025-05-13 18:00:43,369 - reset_database_direct - INFO - Truncated table workflow_temp.input_dependencies
2025-05-13 18:00:43,371 - reset_database_direct - INFO - Truncated table workflow_temp.system_functions
2025-05-13 18:00:43,372 - reset_database_direct - INFO - Truncated table workflow_temp.permission_contexts
2025-05-13 18:00:43,373 - reset_database_direct - INFO - Truncated table workflow_temp.workflow_results
2025-05-13 18:00:43,393 - reset_database_direct - INFO - Truncated table workflow_temp.users
2025-05-13 18:00:43,394 - reset_database_direct - INFO - Truncated table workflow_temp.user_oauth_tokens
2025-05-13 18:00:43,396 - reset_database_direct - INFO - Truncated table workflow_temp.runtime_metrics_stack
2025-05-13 18:00:43,397 - reset_database_direct - INFO - Truncated table workflow_temp.workflow_transaction
2025-05-13 18:00:43,398 - reset_database_direct - INFO - Truncated table workflow_temp.role_permissions
2025-05-13 18:00:43,399 - reset_database_direct - INFO - Truncated table workflow_temp.user_sessions
2025-05-13 18:00:43,400 - reset_database_direct - INFO - Truncated table workflow_temp.input_items
2025-05-13 18:00:43,401 - reset_database_direct - INFO - Truncated table workflow_temp.lo_output_triggers
2025-05-13 18:00:43,402 - reset_database_direct - INFO - Truncated table workflow_temp.ui_elements
2025-05-13 18:00:43,402 - reset_database_direct - INFO - Successfully reset database schema workflow_temp
Dropped table workflow_temp.entity_attributes
Dropped table workflow_temp.entity_attribute_metadata
Dropped table workflow_temp.entity_relationships
Dropped table workflow_temp.entity_business_rules
Dropped table workflow_temp.entity_lifecycle_management
Dropped table workflow_temp.e1_employee
Dropped table workflow_temp.e2_department
Dropped table workflow_temp.e3_leavetype
Dropped table workflow_temp.e4_leaveapplication
Dropped table workflow_temp.e5_calendarevent
Truncated table workflow_temp.books
Truncated table workflow_temp.objective_permissions
Truncated table workflow_temp.input_stack
Truncated table workflow_temp.lo_input_items
Truncated table workflow_temp.permission_types
Truncated table workflow_temp.chapters
Truncated table workflow_temp.lo_output_stack
Truncated table workflow_temp.attribute_ui_controls
Truncated table workflow_temp.organizational_units
Truncated table workflow_temp.lo_output_items
Truncated table workflow_temp.agent_stack
Truncated table workflow_temp.conditional_success_messages
Truncated table workflow_temp.prescription
Truncated table workflow_temp.lo_data_mapping_stack
Truncated table workflow_temp.agent_rights
Truncated table workflow_temp.lo_input_stack
Truncated table workflow_temp.lo_dependencies
Truncated table workflow_temp.output_stack
Truncated table workflow_temp.roles
Truncated table workflow_temp.data_mapping_stack
Truncated table workflow_temp.lo_input_execution
Truncated table workflow_temp.role
Truncated table workflow_temp.go_performance_metrics
Truncated table workflow_temp.output_items
Truncated table workflow_temp.success_messages
Truncated table workflow_temp.attribute_enum_values
Truncated table workflow_temp.lo_nested_functions
Truncated table workflow_temp.dropdown_data_sources
Truncated table workflow_temp.attribute_validations
Truncated table workflow_temp.lo_system_functions
Truncated table workflow_temp.terminal_pathways
Truncated table workflow_temp.lo_data_mappings
Truncated table workflow_temp.tenants
Truncated table workflow_temp.ui_stack
Truncated table workflow_temp.lo_input_validations
Truncated table workflow_temp.user_organizations
Truncated table workflow_temp.global_objectives
Truncated table workflow_temp.permission_capabilities
Truncated table workflow_temp.user
Truncated table workflow_temp.user_role
Truncated table workflow_temp.role_inheritance
Truncated table workflow_temp.workflow_instances
Truncated table workflow_temp.calculated_fields
Truncated table workflow_temp.data_mappings
Truncated table workflow_temp.entities
Truncated table workflow_temp.mapping_rules
Truncated table workflow_temp.output_triggers
Truncated table workflow_temp.go_lo_mapping
Truncated table workflow_temp.lo_output_execution
Truncated table workflow_temp.metrics_aggregation
Truncated table workflow_temp.metrics_reporting
Truncated table workflow_temp.user_roles
Truncated table workflow_temp.local_objectives
Truncated table workflow_temp.input_data_sources
Truncated table workflow_temp.input_dependencies
Truncated table workflow_temp.system_functions
Truncated table workflow_temp.permission_contexts
Truncated table workflow_temp.workflow_results
Truncated table workflow_temp.users
Truncated table workflow_temp.user_oauth_tokens
Truncated table workflow_temp.runtime_metrics_stack
Truncated table workflow_temp.workflow_transaction
Truncated table workflow_temp.role_permissions
Truncated table workflow_temp.user_sessions
Truncated table workflow_temp.input_items
Truncated table workflow_temp.lo_output_triggers
Truncated table workflow_temp.ui_elements


=== Command completed with exit code: 0 ===



=== Running command: python3 init_database.py ===

2025-05-13 18:00:43,448 - init_database - INFO - Initializing database schema workflow_temp
2025-05-13 18:00:43,455 - init_database - INFO - Created schema workflow_temp
2025-05-13 18:00:43,460 - init_database - INFO - Created table workflow_temp.entities
2025-05-13 18:00:43,465 - init_database - INFO - Created table workflow_temp.entity_attributes
2025-05-13 18:00:43,466 - init_database - INFO - Created table workflow_temp.entity_attribute_metadata
2025-05-13 18:00:43,477 - init_database - INFO - Created table workflow_temp.entity_relationships
2025-05-13 18:00:43,489 - init_database - INFO - Created table workflow_temp.entity_business_rules
2025-05-13 18:00:43,489 - init_database - INFO - Created table workflow_temp.calculated_fields
2025-05-13 18:00:43,491 - init_database - INFO - Created table workflow_temp.entity_lifecycle_management
2025-05-13 18:00:43,491 - init_database - INFO - Created table workflow_temp.attribute_enum_values
2025-05-13 18:00:43,491 - init_database - INFO - Created table workflow_temp.attribute_validations
2025-05-13 18:00:43,491 - init_database - INFO - Created table workflow_temp.global_objectives
2025-05-13 18:00:43,491 - init_database - INFO - Created table workflow_temp.tenants
2025-05-13 18:00:43,492 - init_database - INFO - Created table workflow_temp.books
2025-05-13 18:00:43,492 - init_database - INFO - Created table workflow_temp.chapters
2025-05-13 18:00:43,492 - init_database - INFO - Created table workflow_temp.local_objectives
2025-05-13 18:00:43,492 - init_database - INFO - Created table workflow_temp.go_lo_mapping
2025-05-13 18:00:43,492 - init_database - INFO - Created table workflow_temp.output_stack
2025-05-13 18:00:43,492 - init_database - INFO - Created table workflow_temp.output_items
2025-05-13 18:00:43,492 - init_database - INFO - Created table workflow_temp.output_triggers
2025-05-13 18:00:43,492 - init_database - INFO - Successfully initialized database schema workflow_temp
Created schema workflow_temp
Created table workflow_temp.entities
Created table workflow_temp.entity_attributes
Created table workflow_temp.entity_attribute_metadata
Created table workflow_temp.entity_relationships
Created table workflow_temp.entity_business_rules
Created table workflow_temp.calculated_fields
Created table workflow_temp.entity_lifecycle_management
Created table workflow_temp.attribute_enum_values
Created table workflow_temp.attribute_validations
Created table workflow_temp.global_objectives
Created table workflow_temp.tenants
Created table workflow_temp.books
Created table workflow_temp.chapters
Created table workflow_temp.local_objectives
Created table workflow_temp.go_lo_mapping
Created table workflow_temp.output_stack
Created table workflow_temp.output_items
Created table workflow_temp.output_triggers


=== Command completed with exit code: 0 ===



=== Running command: python3 deploy_entities_and_go.py ===

2025-05-13 18:00:43,593 - deploy_entities_and_go - INFO - Starting entity and GO deployment
2025-05-13 18:00:43,593 - deploy_entities_and_go - INFO - Deploying entity definitions
2025-05-13 18:00:43,593 - entity_parser - INFO - Starting to parse entity definitions
2025-05-13 18:00:43,594 - entity_parser - INFO - Parsing entity: Employee
2025-05-13 18:00:43,594 - entity_parser - INFO - Found enum values for attribute 'status': ['Active', 'Inactive', 'OnLeave']
2025-05-13 18:00:43,594 - entity_parser - INFO - Successfully parsed entity 'Employee'
2025-05-13 18:00:43,594 - entity_parser - INFO - Parsing entity: Employee
2025-05-13 18:00:43,595 - entity_parser - INFO - Successfully parsed entity 'Employee'
2025-05-13 18:00:43,595 - entity_parser - INFO - Parsing entity: Employee
2025-05-13 18:00:43,595 - entity_parser - WARNING - Attribute 'email' specified in Additional Properties not found in entity 'Employee'
2025-05-13 18:00:43,595 - entity_parser - INFO - Successfully parsed entity 'Employee'
2025-05-13 18:00:43,595 - entity_parser - INFO - Parsing entity: Employee
2025-05-13 18:00:43,595 - entity_parser - INFO - Successfully parsed entity 'Employee'
2025-05-13 18:00:43,595 - entity_parser - INFO - Parsing entity: Employee
2025-05-13 18:00:43,595 - entity_parser - INFO - Successfully parsed entity 'Employee'
2025-05-13 18:00:43,595 - entity_parser - INFO - Parsing entity: Department
2025-05-13 18:00:43,595 - entity_parser - INFO - Successfully parsed entity 'Department'
2025-05-13 18:00:43,595 - entity_parser - INFO - Parsing entity: Department
2025-05-13 18:00:43,595 - entity_parser - INFO - Successfully parsed entity 'Department'
2025-05-13 18:00:43,595 - entity_parser - INFO - Parsing entity: Department
2025-05-13 18:00:43,595 - entity_parser - INFO - Successfully parsed entity 'Department'
2025-05-13 18:00:43,595 - entity_parser - INFO - Parsing entity: LeaveType
2025-05-13 18:00:43,595 - entity_parser - INFO - Successfully parsed entity 'LeaveType'
2025-05-13 18:00:43,595 - entity_parser - INFO - Parsing entity: LeaveType
2025-05-13 18:00:43,595 - entity_parser - INFO - Successfully parsed entity 'LeaveType'
2025-05-13 18:00:43,595 - entity_parser - INFO - Parsing entity: LeaveType
2025-05-13 18:00:43,595 - entity_parser - INFO - Successfully parsed entity 'LeaveType'
2025-05-13 18:00:43,595 - entity_parser - INFO - Parsing entity: LeaveType
2025-05-13 18:00:43,595 - entity_parser - INFO - Successfully parsed entity 'LeaveType'
2025-05-13 18:00:43,595 - entity_parser - INFO - Parsing entity: LeaveApplication
2025-05-13 18:00:43,595 - entity_parser - INFO - Successfully parsed entity 'LeaveApplication'
2025-05-13 18:00:43,595 - entity_parser - INFO - Parsing entity: LeaveApplication
2025-05-13 18:00:43,595 - entity_parser - INFO - Successfully parsed entity 'LeaveApplication'
2025-05-13 18:00:43,595 - entity_parser - INFO - Parsing entity: LeaveApplication
2025-05-13 18:00:43,595 - entity_parser - INFO - Successfully parsed entity 'LeaveApplication'
2025-05-13 18:00:43,595 - entity_parser - INFO - Parsing entity: CalendarEvent
2025-05-13 18:00:43,596 - entity_parser - INFO - Successfully parsed entity 'CalendarEvent'
2025-05-13 18:00:43,596 - entity_parser - INFO - Parsing entity: CalendarEvent
2025-05-13 18:00:43,596 - entity_parser - INFO - Successfully parsed entity 'CalendarEvent'
2025-05-13 18:00:43,596 - entity_parser - INFO - Parsing entity: CalendarEvent
2025-05-13 18:00:43,596 - entity_parser - INFO - Successfully parsed entity 'CalendarEvent'
2025-05-13 18:00:43,596 - entity_parser - INFO - Successfully parsed 5 entities
2025-05-13 18:00:43,596 - deploy_entities_and_go - INFO - Successfully parsed 5 entities
2025-05-13 18:00:43,596 - deploy_entities_and_go - INFO - - Employee
2025-05-13 18:00:43,596 - deploy_entities_and_go - INFO - - Department
2025-05-13 18:00:43,596 - deploy_entities_and_go - INFO - - LeaveType
2025-05-13 18:00:43,596 - deploy_entities_and_go - INFO - - LeaveApplication
2025-05-13 18:00:43,596 - deploy_entities_and_go - INFO - - CalendarEvent
2025-05-13 18:00:43,596 - entity_deployer_v2 - INFO - Deploying entities to workflow_temp
2025-05-13 18:00:43,617 - entity_deployer_v2 - INFO - Inserted entity 'Employee' into workflow_temp.entities
2025-05-13 18:00:43,655 - entity_deployer_v2 - INFO - Inserted attribute 'employeeId' for entity 'E1' into workflow_temp.entity_attributes
2025-05-13 18:00:43,661 - entity_deployer_v2 - INFO - Inserted attribute metadata for 'employeeId' into workflow_temp.entity_attribute_metadata
2025-05-13 18:00:43,697 - entity_deployer_v2 - INFO - Inserted attribute 'firstName' for entity 'E1' into workflow_temp.entity_attributes
2025-05-13 18:00:43,704 - entity_deployer_v2 - INFO - Inserted attribute metadata for 'firstName' into workflow_temp.entity_attribute_metadata
2025-05-13 18:00:43,745 - entity_deployer_v2 - INFO - Inserted attribute 'lastName' for entity 'E1' into workflow_temp.entity_attributes
2025-05-13 18:00:43,752 - entity_deployer_v2 - INFO - Inserted attribute metadata for 'lastName' into workflow_temp.entity_attribute_metadata
2025-05-13 18:00:43,790 - entity_deployer_v2 - INFO - Inserted attribute 'email' for entity 'E1' into workflow_temp.entity_attributes
2025-05-13 18:00:43,798 - entity_deployer_v2 - INFO - Inserted attribute metadata for 'email' into workflow_temp.entity_attribute_metadata
2025-05-13 18:00:43,809 - entity_deployer_v2 - INFO - Inserted archive strategy for entity 'E1' into workflow_temp.entity_lifecycle_management
2025-05-13 18:00:43,821 - entity_deployer_v2 - INFO - Inserted history tracking for entity 'E1' into workflow_temp.entity_lifecycle_management
2025-05-13 18:00:43,838 - entity_deployer_v2 - INFO - Created table workflow_temp.e1_employee
2025-05-13 18:00:43,856 - entity_deployer_v2 - INFO - Inserted entity 'Department' into workflow_temp.entities
2025-05-13 18:00:43,896 - entity_deployer_v2 - INFO - Inserted attribute 'managerId' for entity 'E2' into workflow_temp.entity_attributes
2025-05-13 18:00:43,902 - entity_deployer_v2 - INFO - Inserted attribute metadata for 'managerId' into workflow_temp.entity_attribute_metadata
2025-05-13 18:00:43,943 - entity_deployer_v2 - INFO - Inserted attribute 'locationId with Employee constrains Location' for entity 'E2' into workflow_temp.entity_attributes
2025-05-13 18:00:43,948 - entity_deployer_v2 - INFO - Inserted attribute metadata for 'locationId with Employee constrains Location' into workflow_temp.entity_attribute_metadata
2025-05-13 18:00:43,960 - entity_deployer_v2 - INFO - Inserted archive strategy for entity 'E2' into workflow_temp.entity_lifecycle_management
2025-05-13 18:00:43,972 - entity_deployer_v2 - INFO - Inserted history tracking for entity 'E2' into workflow_temp.entity_lifecycle_management
2025-05-13 18:00:43,989 - entity_deployer_v2 - INFO - Created table workflow_temp.e2_department
2025-05-13 18:00:44,008 - entity_deployer_v2 - INFO - Inserted entity 'LeaveType' into workflow_temp.entities
2025-05-13 18:00:44,045 - entity_deployer_v2 - INFO - Inserted attribute 'typeId' for entity 'E3' into workflow_temp.entity_attributes
2025-05-13 18:00:44,050 - entity_deployer_v2 - INFO - Inserted attribute metadata for 'typeId' into workflow_temp.entity_attribute_metadata
2025-05-13 18:00:44,089 - entity_deployer_v2 - INFO - Inserted attribute 'name' for entity 'E3' into workflow_temp.entity_attributes
2025-05-13 18:00:44,094 - entity_deployer_v2 - INFO - Inserted attribute metadata for 'name' into workflow_temp.entity_attribute_metadata
2025-05-13 18:00:44,130 - entity_deployer_v2 - INFO - Inserted attribute 'description' for entity 'E3' into workflow_temp.entity_attributes
2025-05-13 18:00:44,135 - entity_deployer_v2 - INFO - Inserted attribute metadata for 'description' into workflow_temp.entity_attribute_metadata
2025-05-13 18:00:44,172 - entity_deployer_v2 - INFO - Inserted attribute 'maxDuration' for entity 'E3' into workflow_temp.entity_attributes
2025-05-13 18:00:44,177 - entity_deployer_v2 - INFO - Inserted attribute metadata for 'maxDuration' into workflow_temp.entity_attribute_metadata
2025-05-13 18:00:44,217 - entity_deployer_v2 - INFO - Inserted attribute 'requiresDocumentation' for entity 'E3' into workflow_temp.entity_attributes
2025-05-13 18:00:44,226 - entity_deployer_v2 - INFO - Inserted attribute metadata for 'requiresDocumentation' into workflow_temp.entity_attribute_metadata
2025-05-13 18:00:44,240 - entity_deployer_v2 - INFO - Created table workflow_temp.e3_leavetype
2025-05-13 18:00:44,259 - entity_deployer_v2 - INFO - Inserted entity 'LeaveApplication' into workflow_temp.entities
2025-05-13 18:00:44,305 - entity_deployer_v2 - INFO - Inserted attribute 'leaveId' for entity 'E4' into workflow_temp.entity_attributes
2025-05-13 18:00:44,311 - entity_deployer_v2 - INFO - Inserted attribute metadata for 'leaveId' into workflow_temp.entity_attribute_metadata
2025-05-13 18:00:44,359 - entity_deployer_v2 - INFO - Inserted attribute 'employeeId' for entity 'E4' into workflow_temp.entity_attributes
2025-05-13 18:00:44,367 - entity_deployer_v2 - INFO - Inserted attribute metadata for 'employeeId' into workflow_temp.entity_attribute_metadata
2025-05-13 18:00:44,412 - entity_deployer_v2 - INFO - Inserted attribute 'leaveTypeId' for entity 'E4' into workflow_temp.entity_attributes
2025-05-13 18:00:44,419 - entity_deployer_v2 - INFO - Inserted attribute metadata for 'leaveTypeId' into workflow_temp.entity_attribute_metadata
2025-05-13 18:00:44,463 - entity_deployer_v2 - INFO - Inserted attribute 'startDate' for entity 'E4' into workflow_temp.entity_attributes
2025-05-13 18:00:44,470 - entity_deployer_v2 - INFO - Inserted attribute metadata for 'startDate' into workflow_temp.entity_attribute_metadata
2025-05-13 18:00:44,514 - entity_deployer_v2 - INFO - Inserted attribute 'endDate' for entity 'E4' into workflow_temp.entity_attributes
2025-05-13 18:00:44,521 - entity_deployer_v2 - INFO - Inserted attribute metadata for 'endDate' into workflow_temp.entity_attribute_metadata
2025-05-13 18:00:44,563 - entity_deployer_v2 - INFO - Inserted attribute 'numDays' for entity 'E4' into workflow_temp.entity_attributes
2025-05-13 18:00:44,570 - entity_deployer_v2 - INFO - Inserted attribute metadata for 'numDays' into workflow_temp.entity_attribute_metadata
2025-05-13 18:00:44,616 - entity_deployer_v2 - INFO - Inserted attribute 'status' for entity 'E4' into workflow_temp.entity_attributes
2025-05-13 18:00:44,622 - entity_deployer_v2 - INFO - Inserted attribute metadata for 'status' into workflow_temp.entity_attribute_metadata
2025-05-13 18:00:44,664 - entity_deployer_v2 - INFO - Inserted attribute 'approvedBy' for entity 'E4' into workflow_temp.entity_attributes
2025-05-13 18:00:44,672 - entity_deployer_v2 - INFO - Inserted attribute metadata for 'approvedBy' into workflow_temp.entity_attribute_metadata
2025-05-13 18:00:44,716 - entity_deployer_v2 - INFO - Inserted attribute 'approvalDate' for entity 'E4' into workflow_temp.entity_attributes
2025-05-13 18:00:44,722 - entity_deployer_v2 - INFO - Inserted attribute metadata for 'approvalDate' into workflow_temp.entity_attribute_metadata
2025-05-13 18:00:44,767 - entity_deployer_v2 - INFO - Inserted attribute 'comments' for entity 'E4' into workflow_temp.entity_attributes
2025-05-13 18:00:44,775 - entity_deployer_v2 - INFO - Inserted attribute metadata for 'comments' into workflow_temp.entity_attribute_metadata
2025-05-13 18:00:44,792 - entity_deployer_v2 - INFO - Created table workflow_temp.e4_leaveapplication
2025-05-13 18:00:44,812 - entity_deployer_v2 - INFO - Inserted entity 'CalendarEvent' into workflow_temp.entities
2025-05-13 18:00:44,858 - entity_deployer_v2 - INFO - Inserted attribute 'eventId' for entity 'E5' into workflow_temp.entity_attributes
2025-05-13 18:00:44,865 - entity_deployer_v2 - INFO - Inserted attribute metadata for 'eventId' into workflow_temp.entity_attribute_metadata
2025-05-13 18:00:44,906 - entity_deployer_v2 - INFO - Inserted attribute 'employeeId' for entity 'E5' into workflow_temp.entity_attributes
2025-05-13 18:00:44,913 - entity_deployer_v2 - INFO - Inserted attribute metadata for 'employeeId' into workflow_temp.entity_attribute_metadata
2025-05-13 18:00:44,957 - entity_deployer_v2 - INFO - Inserted attribute 'eventType' for entity 'E5' into workflow_temp.entity_attributes
2025-05-13 18:00:44,964 - entity_deployer_v2 - INFO - Inserted attribute metadata for 'eventType' into workflow_temp.entity_attribute_metadata
2025-05-13 18:00:45,009 - entity_deployer_v2 - INFO - Inserted attribute 'startDate' for entity 'E5' into workflow_temp.entity_attributes
2025-05-13 18:00:45,016 - entity_deployer_v2 - INFO - Inserted attribute metadata for 'startDate' into workflow_temp.entity_attribute_metadata
2025-05-13 18:00:45,060 - entity_deployer_v2 - INFO - Inserted attribute 'endDate' for entity 'E5' into workflow_temp.entity_attributes
2025-05-13 18:00:45,068 - entity_deployer_v2 - INFO - Inserted attribute metadata for 'endDate' into workflow_temp.entity_attribute_metadata
2025-05-13 18:00:45,109 - entity_deployer_v2 - INFO - Inserted attribute 'title' for entity 'E5' into workflow_temp.entity_attributes
2025-05-13 18:00:45,114 - entity_deployer_v2 - INFO - Inserted attribute metadata for 'title' into workflow_temp.entity_attribute_metadata
2025-05-13 18:00:45,150 - entity_deployer_v2 - INFO - Inserted attribute 'description' for entity 'E5' into workflow_temp.entity_attributes
2025-05-13 18:00:45,158 - entity_deployer_v2 - INFO - Inserted attribute metadata for 'description' into workflow_temp.entity_attribute_metadata
2025-05-13 18:00:45,201 - entity_deployer_v2 - INFO - Inserted attribute 'status' for entity 'E5' into workflow_temp.entity_attributes
2025-05-13 18:00:45,209 - entity_deployer_v2 - INFO - Inserted attribute metadata for 'status' into workflow_temp.entity_attribute_metadata
2025-05-13 18:00:45,253 - entity_deployer_v2 - INFO - Inserted attribute 'referenceId' for entity 'E5' into workflow_temp.entity_attributes
2025-05-13 18:00:45,260 - entity_deployer_v2 - INFO - Inserted attribute metadata for 'referenceId' into workflow_temp.entity_attribute_metadata
2025-05-13 18:00:45,275 - entity_deployer_v2 - INFO - Created table workflow_temp.e5_calendarevent
2025-05-13 18:00:45,283 - entity_deployer_v2 - INFO - Entity deployment completed successfully
2025-05-13 18:00:45,283 - deploy_entities_and_go - INFO - Successfully deployed entity definitions to workflow_temp
2025-05-13 18:00:45,283 - deploy_entities_and_go - INFO - Validating and deploying GO definitions
2025-05-13 18:00:45,283 - go_parser - INFO - Starting to parse GO definitions
2025-05-13 18:00:45,284 - go_parser - INFO - Parsing GO: Leave Approval Process (ID: go001)
2025-05-13 18:00:45,290 - go_parser - INFO - Successfully parsed GO 'Leave Approval Process'
2025-05-13 18:00:45,290 - go_parser - WARNING - Invalid GO header format: GO Relationships:
2025-05-13 18:00:45,291 - go_parser - INFO - Parsing GO: Employee Calendar Management (ID: go004)
2025-05-13 18:00:45,291 - go_parser - INFO - Successfully parsed GO 'Employee Calendar Management'
2025-05-13 18:00:45,291 - go_parser - WARNING - Invalid GO header format: GO Relationships:
2025-05-13 18:00:45,291 - go_parser - INFO - Successfully parsed 2 global objectives
2025-05-13 18:00:45,292 - deploy_entities_and_go - INFO - Successfully parsed 2 global objectives
2025-05-13 18:00:45,292 - deploy_entities_and_go - INFO - - Leave Approval Process
2025-05-13 18:00:45,292 - deploy_entities_and_go - INFO - - Employee Calendar Management
2025-05-13 18:00:45,292 - deploy_entities_and_go - WARNING - GO parsing completed with 2 warnings
2025-05-13 18:00:45,292 - deploy_entities_and_go - WARNING - - Invalid GO header format: GO Relationships:
2025-05-13 18:00:45,292 - deploy_entities_and_go - WARNING - - Invalid GO header format: GO Relationships:
2025-05-13 18:00:45,348 - deploy_entities_and_go - INFO - Registry validation passed
2025-05-13 18:00:45,348 - go_deployer - INFO - Deploying GO definitions to workflow_temp
2025-05-13 18:00:45,400 - go_deployer - WARNING - Attribute 'departmentId' of entity 'Employee' referenced in GO does not exist in the database
2025-05-13 18:00:45,405 - go_deployer - WARNING - Attribute 'managerId' of entity 'Employee' referenced in GO does not exist in the database
2025-05-13 18:00:45,436 - go_deployer - WARNING - Attribute 'false' of entity 'LeaveType' referenced in GO does not exist in the database
2025-05-13 18:00:45,478 - go_deployer - WARNING - Attribute 'Approved' of entity 'LeaveApplication' referenced in GO does not exist in the database
2025-05-13 18:00:45,484 - go_deployer - WARNING - Attribute 'Rejected' of entity 'LeaveApplication' referenced in GO does not exist in the database
2025-05-13 18:00:45,552 - go_deployer - INFO - Inserted GO 'Leave Approval Process' into workflow_temp.global_objectives
2025-05-13 18:00:45,568 - go_deployer - WARNING - Warning: LO 'Employee submits a leave request' (ID: SubmitLeaveRequest) referenced in GO 'go001' does not exist in the database. Skipping mapping.
2025-05-13 18:00:45,573 - go_deployer - WARNING - Warning: LO 'Employee uploads required documentation' (ID: UploadDocumentation) referenced in GO 'go001' does not exist in the database. Skipping mapping.
2025-05-13 18:00:45,577 - go_deployer - WARNING - Warning: LO 'Manager reviews the leave request' (ID: ReviewLeaveRequest) referenced in GO 'go001' does not exist in the database. Skipping mapping.
2025-05-13 18:00:45,583 - go_deployer - WARNING - Warning: LO 'System updates leave request status to approved' (ID: ApproveLeaveRequest) referenced in GO 'go001' does not exist in the database. Skipping mapping.
2025-05-13 18:00:45,588 - go_deployer - WARNING - Warning: LO 'System updates leave request status to rejected' (ID: RejectLeaveRequest) referenced in GO 'go001' does not exist in the database. Skipping mapping.
2025-05-13 18:00:45,592 - go_deployer - WARNING - Warning: LO 'System notifies employee of the decision' (ID: NotifyEmployee) referenced in GO 'go001' does not exist in the database. Skipping mapping.
2025-05-13 18:00:45,598 - go_deployer - WARNING - Warning: LO 'System updates employee calendar' (ID: UpdateCalendar) referenced in GO 'go001' does not exist in the database. Skipping mapping.
2025-05-13 18:00:45,602 - go_deployer - WARNING - Warning: LO 'System updates employee leave balance' (ID: UpdateLeaveBalance) referenced in GO 'go001' does not exist in the database. Skipping mapping.
2025-05-13 18:00:45,607 - go_deployer - WARNING - Warning: LO 'Employee cancels a previously submitted leave request' (ID: CancelLeaveRequest) referenced in GO 'go001' does not exist in the database. Skipping mapping.
2025-05-13 18:00:45,613 - go_deployer - WARNING - Warning: LO 'System rolls back leave approval' (ID: RollbackLeaveApproval) referenced in GO 'go001' does not exist in the database. Skipping mapping.
2025-05-13 18:00:45,618 - go_deployer - WARNING - Warning: LO 'System restores employee leave balance' (ID: RestoreLeaveBalance) referenced in GO 'go001' does not exist in the database. Skipping mapping.
2025-05-13 18:00:45,622 - go_deployer - WARNING - Warning: LO 'System notifies manager of cancellation' (ID: NotifyCancellation) referenced in GO 'go001' does not exist in the database. Skipping mapping.
2025-05-13 18:00:45,628 - go_deployer - WARNING - Warning: LO 'System logs audit trail for compliance' (ID: LogAuditTrail) referenced in GO 'go001' does not exist in the database. Skipping mapping.
2025-05-13 18:00:45,643 - go_deployer - WARNING - Referenced GO 'Employee Calendar Management' does not exist. Skipping mapping creation.
2025-05-13 18:00:45,648 - go_deployer - WARNING - Referenced GO 'Employee Calendar Management' does not exist. Skipping mapping creation.
2025-05-13 18:00:45,653 - go_deployer - WARNING - Referenced GO 'Employee Calendar Management' does not exist. Skipping mapping creation.
2025-05-13 18:00:45,660 - go_deployer - WARNING - Referenced GO 'Employee Calendar Management' does not exist. Skipping mapping creation.
2025-05-13 18:00:45,702 - go_deployer - WARNING - Attribute 'Rejected' of entity 'LeaveApplication' referenced in GO does not exist in the database
2025-05-13 18:00:45,761 - go_deployer - WARNING - Attribute 'Cancelled' of entity 'CalendarEvent' referenced in GO does not exist in the database
2025-05-13 18:00:45,802 - go_deployer - INFO - Inserted GO 'Employee Calendar Management' into workflow_temp.global_objectives
2025-05-13 18:00:45,816 - go_deployer - WARNING - Warning: LO 'System creates a calendar event' (ID: CreateCalendarEvent) referenced in GO 'go004' does not exist in the database. Skipping mapping.
2025-05-13 18:00:45,822 - go_deployer - WARNING - Warning: LO 'System notifies employee of the calendar event' (ID: NotifyEmployee) referenced in GO 'go004' does not exist in the database. Skipping mapping.
2025-05-13 18:00:45,827 - go_deployer - WARNING - Warning: LO 'System cancels a calendar event' (ID: CancelCalendarEvent) referenced in GO 'go004' does not exist in the database. Skipping mapping.
2025-05-13 18:00:45,832 - go_deployer - WARNING - Warning: LO 'System notifies employee of the cancellation' (ID: NotifyCancellation) referenced in GO 'go004' does not exist in the database. Skipping mapping.
2025-05-13 18:00:45,895 - go_deployer - INFO - GO deployment completed successfully
2025-05-13 18:00:45,895 - deploy_entities_and_go - INFO - Successfully deployed GO definitions to workflow_temp
2025-05-13 18:00:45,895 - deploy_entities_and_go - INFO - Entity and GO deployment completed successfully


=== Command completed with exit code: 0 ===

