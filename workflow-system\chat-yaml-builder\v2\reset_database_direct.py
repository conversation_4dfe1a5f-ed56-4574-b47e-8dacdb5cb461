"""
Reset Database Script (Direct Connection)

This script drops all entity tables and truncates all data in the workflow_temp schema.
"""

import os
import sys
import logging
import psycopg2
from typing import List, Tuple

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('reset_database_direct')

def get_db_connection(schema_name: str = None) -> psycopg2.extensions.connection:
    """
    Get a database connection.
    
    Args:
        schema_name: Optional schema name to set as the search path
        
    Returns:
        Database connection object
    """
    # Get database connection parameters from environment variables or use defaults
    db_host = os.environ.get('DB_HOST', '**********')
    db_port = os.environ.get('DB_PORT', '5432')
    db_name = os.environ.get('DB_NAME', 'workflow_system')
    db_user = os.environ.get('DB_USER', 'postgres')
    db_password = os.environ.get('DB_PASSWORD', 'workflow_postgres_secure_password')
    
    # Connect to the database
    conn = psycopg2.connect(
        host=db_host,
        port=db_port,
        dbname=db_name,
        user=db_user,
        password=db_password
    )
    
    # Set schema search path if provided
    if schema_name:
        with conn.cursor() as cursor:
            cursor.execute(f"SET search_path TO {schema_name}")
            conn.commit()
    
    return conn

def drop_entity_tables(schema_name: str) -> List[str]:
    """
    Drop all entity tables in the schema.
    
    Args:
        schema_name: Schema name
        
    Returns:
        List of messages
    """
    messages = []
    conn = None
    
    try:
        # Get database connection
        conn = get_db_connection(schema_name)
        
        # Get all entity tables
        with conn.cursor() as cursor:
            cursor.execute(f"""
                SELECT table_name 
                FROM information_schema.tables 
                WHERE table_schema = '{schema_name}'
                AND (table_name LIKE 'e%\_%%' OR table_name LIKE 'e%.%%')
            """)
            tables = cursor.fetchall()
        
        if not tables:
            messages.append("No entity tables found")
            return messages
        
        # Drop each table
        for row in tables:
            table_name = row[0]
            
            try:
                with conn.cursor() as cursor:
                    cursor.execute(f'DROP TABLE IF EXISTS {schema_name}."{table_name}" CASCADE')
                conn.commit()
                
                messages.append(f"Dropped table {schema_name}.{table_name}")
                logger.info(f"Dropped table {schema_name}.{table_name}")
            except Exception as e:
                conn.rollback()
                error_msg = f"Failed to drop table {schema_name}.{table_name}: {str(e)}"
                messages.append(error_msg)
                logger.warning(error_msg)
        
        return messages
    except Exception as e:
        if conn:
            conn.rollback()
        
        error_msg = f"Error dropping entity tables: {str(e)}"
        logger.error(error_msg, exc_info=True)
        messages.append(error_msg)
        return messages
    finally:
        if conn:
            conn.close()

def truncate_tables(schema_name: str) -> List[str]:
    """
    Truncate all tables in the schema.
    
    Args:
        schema_name: Schema name
        
    Returns:
        List of messages
    """
    messages = []
    conn = None
    
    try:
        # Get database connection
        conn = get_db_connection(schema_name)
        
        # Get all tables
        with conn.cursor() as cursor:
            cursor.execute(f"""
                SELECT table_name 
                FROM information_schema.tables 
                WHERE table_schema = '{schema_name}'
                AND table_type = 'BASE TABLE'
            """)
            tables = cursor.fetchall()
        
        if not tables:
            messages.append("No tables found")
            return messages
        
        # Disable foreign key checks
        with conn.cursor() as cursor:
            cursor.execute("SET session_replication_role = 'replica'")
        conn.commit()
        
        # Truncate each table
        for row in tables:
            table_name = row[0]
            
            # Skip alembic_version table
            if table_name == 'alembic_version':
                continue
            
            try:
                with conn.cursor() as cursor:
                    cursor.execute(f'TRUNCATE TABLE {schema_name}."{table_name}" CASCADE')
                conn.commit()
                
                messages.append(f"Truncated table {schema_name}.{table_name}")
                logger.info(f"Truncated table {schema_name}.{table_name}")
            except Exception as e:
                conn.rollback()
                error_msg = f"Failed to truncate table {schema_name}.{table_name}: {str(e)}"
                messages.append(error_msg)
                logger.warning(error_msg)
        
        # Re-enable foreign key checks
        with conn.cursor() as cursor:
            cursor.execute("SET session_replication_role = 'origin'")
        conn.commit()
        
        return messages
    except Exception as e:
        if conn:
            conn.rollback()
        
        error_msg = f"Error truncating tables: {str(e)}"
        logger.error(error_msg, exc_info=True)
        messages.append(error_msg)
        return messages
    finally:
        if conn:
            conn.close()

def main():
    """Main function."""
    schema_name = "workflow_temp"
    logger.info(f"Resetting database schema {schema_name}")
    
    # Drop entity tables
    messages = drop_entity_tables(schema_name)
    for message in messages:
        print(message)
    
    # Truncate tables
    messages = truncate_tables(schema_name)
    for message in messages:
        print(message)
    
    logger.info(f"Successfully reset database schema {schema_name}")

if __name__ == "__main__":
    main()
