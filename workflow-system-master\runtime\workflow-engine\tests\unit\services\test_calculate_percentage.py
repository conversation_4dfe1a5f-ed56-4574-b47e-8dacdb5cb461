import unittest
from unittest.mock import patch
from typing import Union
import sys
import os

# Add project root to path if needed
project_root = '/home/<USER>/workflow-system/runtime/workflow-engine'
if project_root not in sys.path:
    sys.path.insert(0, project_root)

# Import the function to test
from app.services.system_functions import calculate_percentage

class TestCalculatePercentage(unittest.TestCase):
    def test_basic_percentage(self):
        """Test basic percentage calculation."""
        # Test with integers
        result = calculate_percentage(75, 100)
        
        # Verify
        self.assertEqual(result, 75.0)
    
    def test_custom_decimal_places(self):
        """Test percentage calculation with custom decimal places."""
        # Calculate 1/3 as percentage with 3 decimal places
        result = calculate_percentage(1, 3, 3)
        
        # The expected result should be around 33.333
        self.assertAlmostEqual(result, 33.333, places=3)
    
    def test_with_floats(self):
        """Test percentage calculation with float inputs."""
        # Calculate with float inputs
        result = calculate_percentage(2.5, 3.75)
        
        # The expected result should be around 66.67
        self.assertAlmostEqual(result, 66.67, places=2)
    
    def test_percentage_greater_than_100(self):
        """Test percentage calculation with result greater than 100%."""
        # Calculate percentage > 100%
        result = calculate_percentage(150, 100)
        
        # Verify
        self.assertEqual(result, 150.0)
    
    def test_negative_values(self):
        """Test percentage calculation with negative values."""
        # Calculate with negative part
        result = calculate_percentage(-50, 100)
        
        # Verify
        self.assertEqual(result, -50.0)
        
        # Calculate with negative whole
        result = calculate_percentage(50, -100)
        
        # With negative denominator, we expect -50.0
        self.assertEqual(result, -50.0)
        
        # Calculate with both negative
        result = calculate_percentage(-50, -50)
        
        # Verify - both negative should give 100%
        self.assertEqual(result, 100.0)
    
    def test_zero_denominator(self):
        """Test percentage calculation with zero denominator."""
        with self.assertRaises(ValueError) as context:
            calculate_percentage(10, 0)
        
        self.assertEqual(str(context.exception), "Cannot calculate percentage with zero denominator")
    
    def test_zero_numerator(self):
        """Test percentage calculation with zero numerator."""
        # Calculate with zero part
        result = calculate_percentage(0, 100)
        
        # Verify
        self.assertEqual(result, 0.0)
    
    def test_very_small_percentage(self):
        """Test percentage calculation with very small result."""
        # Calculate very small percentage
        result = calculate_percentage(1, 10000)
        
        # Verify
        self.assertAlmostEqual(result, 0.01, places=2)
    
    def test_very_large_percentage(self):
        """Test percentage calculation with very large result."""
        # Calculate very large percentage
        result = calculate_percentage(1000000, 100)
        
        # Verify
        self.assertEqual(result, 1000000.0)
    
    @patch('app.services.system_functions.round_number')
    def test_round_number_called_correctly(self, mock_round_number):
        """Test that round_number is called with correct parameters."""
        # Setup mock
        mock_round_number.return_value = 75.0
        
        # Call the function
        calculate_percentage(75, 100, 2)
        
        # Verify round_number was called with correct args
        mock_round_number.assert_called_once()
        args, kwargs = mock_round_number.call_args
        self.assertEqual(args[1], 2)  # Check decimal_places parameter
        self.assertAlmostEqual(args[0], 75.0)  # Check percentage value

if __name__ == '__main__':
    unittest.main()