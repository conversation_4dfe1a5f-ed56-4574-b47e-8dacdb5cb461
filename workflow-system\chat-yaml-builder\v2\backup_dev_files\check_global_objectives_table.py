#!/usr/bin/env python3
"""
Check the structure of the global_objectives table in the specified schema.
"""

import os
import sys
import logging
import psycopg2
import argparse

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('check_global_objectives_table')

def main():
    """Main function."""
    # Parse command line arguments
    parser = argparse.ArgumentParser(description='Check the structure of the global_objectives table')
    parser.add_argument('--schema-name', default='workflow_temp', help='Schema name to check')
    args = parser.parse_args()
    
    schema_name = args.schema_name
    
    try:
        # Connect to the database
        conn = psycopg2.connect(
            dbname="workflow_system",
            user="postgres",
            password="workflow_postgres_secure_password",
            host="**********",
            port="5432"
        )

        # Create a cursor
        cursor = conn.cursor()

        # Get the columns of the global_objectives table
        cursor.execute(f"""
            SELECT column_name, data_type, is_nullable
            FROM information_schema.columns
            WHERE table_schema = '{schema_name}'
            AND table_name = 'global_objectives'
            ORDER BY ordinal_position
        """)

        # Fetch the results
        columns = cursor.fetchall()

        # Print the columns
        print(f"Columns in {schema_name}.global_objectives:")
        for column in columns:
            print(f"{column[0]} ({column[1]}, nullable: {column[2]})")

        # Close the cursor and connection
        cursor.close()
        conn.close()
    except Exception as e:
        logger.error(f"Error: {str(e)}")
        sys.exit(1)

if __name__ == '__main__':
    main()
