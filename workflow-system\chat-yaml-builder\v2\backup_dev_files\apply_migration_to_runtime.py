#!/usr/bin/env python3
"""
Apply migration script to workflow_runtime schema.

This script applies the migration script to the workflow_runtime schema.
"""

import os
import sys
import logging
import argparse
from typing import Dict, List, Tuple, Any, Optional

# Add parent directory to path to import common modules
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
from db_utils import execute_query

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('apply_migration_to_runtime')

def apply_migration_script(script_path: str) -> Tuple[bool, List[str]]:
    """
    Apply migration script to workflow_runtime schema.

    Args:
        script_path: Path to the migration script

    Returns:
        Tuple containing:
            - Boolean indicating if the operation was successful
            - List of messages (warnings, errors, or success messages)
    """
    messages = []
    
    try:
        # Read migration script
        with open(script_path, 'r') as f:
            script = f.read()
        
        # Execute the entire script at once
        success, query_messages, _ = execute_query(script)
        
        if not success:
            messages.extend(query_messages)
            logger.error(f"Failed to execute migration script")
            logger.error(f"Error: {query_messages}")
            return False, messages
        
        messages.append("Successfully applied migration script to workflow_runtime schema")
        logger.info("Successfully applied migration script to workflow_runtime schema")
        
        return True, messages
    except Exception as e:
        error_msg = f"Error applying migration script: {str(e)}"
        logger.error(error_msg, exc_info=True)
        messages.append(error_msg)
        return False, messages

def main():
    """Main function."""
    parser = argparse.ArgumentParser(description='Apply migration script to workflow_runtime schema')
    parser.add_argument('--script-path', default='migration_script.sql', help='Path to the migration script')
    args = parser.parse_args()
    
    success, messages = apply_migration_script(args.script_path)
    
    for message in messages:
        print(message)
    
    if success:
        print("Successfully applied migration script to workflow_runtime schema")
    else:
        print("Failed to apply migration script to workflow_runtime schema")
        sys.exit(1)

if __name__ == '__main__':
    main()
