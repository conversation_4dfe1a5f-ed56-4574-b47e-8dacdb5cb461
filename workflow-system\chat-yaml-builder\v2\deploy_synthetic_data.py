#!/usr/bin/env python3
"""
Script to deploy synthetic data directly to the database.
"""

import os
import logging
import psycopg2
from parsers.entity_parser import parse_entities

# Set up logging
logging.basicConfig(level=logging.INFO, 
                   format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger('deploy_synthetic_data')

def get_db_connection(schema_name: str = None):
    """
    Get a database connection.
    
    Args:
        schema_name: Optional schema name to set as the search path
        
    Returns:
        Database connection object
    """
    # Get database connection parameters from environment variables or use defaults
    db_host = os.environ.get('DB_HOST', '**********')
    db_port = os.environ.get('DB_PORT', '5432')
    db_name = os.environ.get('DB_NAME', 'workflow_system')
    db_user = os.environ.get('DB_USER', 'postgres')
    db_password = os.environ.get('DB_PASSWORD', 'workflow_postgres_secure_password')
    
    # Connect to the database
    conn = psycopg2.connect(
        host=db_host,
        port=db_port,
        dbname=db_name,
        user=db_user,
        password=db_password
    )
    
    # Set schema search path if provided
    if schema_name:
        with conn.cursor() as cursor:
            cursor.execute(f"SET search_path TO {schema_name}")
            conn.commit()
    
    return conn

def create_department_records(conn, schema_name):
    """
    Create department records in the database.
    
    Args:
        conn: Database connection
        schema_name: Schema name
    """
    table_name = 'e000001_department'
    
    # Check if the department table exists
    with conn.cursor() as cursor:
        cursor.execute("""
            SELECT EXISTS (
                SELECT FROM information_schema.tables 
                WHERE table_schema = %s
                AND table_name = %s
            )
        """, (schema_name, table_name))
        
        table_exists = cursor.fetchone()[0]
        
        if not table_exists:
            logger.error(f"Table '{table_name}' does not exist in schema '{schema_name}'")
            return False
    
    # Insert department records
    departments = [
        {'departmentid': '101', 'name': 'Engineering'},
        {'departmentid': '102', 'name': 'Marketing'}
    ]
    
    for dept in departments:
        # Check if department already exists
        with conn.cursor() as cursor:
            cursor.execute(f"""
                SELECT COUNT(*) FROM {schema_name}."{table_name}"
                WHERE departmentid = %s
            """, (dept['departmentid'],))
            
            count = cursor.fetchone()[0]
            
            if count > 0:
                logger.info(f"Department with ID {dept['departmentid']} already exists")
                continue
        
        # Insert department record
        with conn.cursor() as cursor:
            cursor.execute(f"""
                INSERT INTO {schema_name}."{table_name}" (
                    departmentid, name, created_at, updated_at
                ) VALUES (
                    %s, %s, NOW(), NOW()
                )
            """, (dept['departmentid'], dept['name']))
        
        conn.commit()
        logger.info(f"Inserted department record with ID {dept['departmentid']}")
    
    return True

def deploy_synthetic_data():
    """
    Deploy synthetic data directly to the database.
    """
    # Define the entity with synthetic data
    entity_def = """
Employee has employeeId^PK, firstName, lastName, email, phoneNumber, departmentId^FK, managerId^FK, hireDate, status(Active, Inactive, OnLeave), salary, performanceRating.

* Employee has many-to-one relationship with Department using Employee.departmentId to Department.departmentId^PK
* Employee has many-to-one relationship with Employee using Employee.managerId to Employee.employeeId^PK

* Synthetic: 
Employee has employeeId = 1, firstName = Tarun, lastName = Singh, email = "<EMAIL>", phoneNumber = "************", departmentId = 101, managerId = null, hireDate = "2024-01-15", status = "Active", salary = 60000, performanceRating = 4.5.
Employee has employeeId = 2, firstName = Priya, lastName = Sharma, email = "<EMAIL>", phoneNumber = "************", departmentId = 102, managerId = 1, hireDate = "2024-02-20", status = "Inactive", salary = 55000, performanceRating = 4.0.
"""
    
    # Parse the entity definition
    logger.info("Parsing entity definition")
    entities_data, warnings = parse_entities(entity_def)
    
    # Print any warnings
    if warnings:
        logger.warning("Warnings during parsing:")
        for warning in warnings:
            logger.warning(f"  - {warning}")
    
    # Check if Employee entity was parsed
    if 'entities' in entities_data and 'Employee' in entities_data['entities']:
        employee_entity = entities_data['entities']['Employee']
        
        # Check if synthetic data was parsed
        if 'synthetic_data' in employee_entity:
            logger.info("\nEmployee entity synthetic data:")
            for data in employee_entity['synthetic_data']:
                logger.info(f"  - {data}")
        else:
            logger.warning("No synthetic data found in Employee entity")
            return
    else:
        logger.error("Employee entity not found in parsed data")
        return
    
    # Connect to the database
    schema_name = 'workflow_temp'
    table_name = 'e000002_employee'
    
    try:
        conn = get_db_connection(schema_name)
        
        # Create department records first
        logger.info("Creating department records")
        if not create_department_records(conn, schema_name):
            logger.error("Failed to create department records")
            return
        
        # Process each synthetic data line
        for data_line in employee_entity['synthetic_data']:
            # Parse the synthetic data line
            # Format: "Employee has attr1 = value1, attr2 = value2, ..."
            if not data_line.startswith("Employee has "):
                logger.warning(f"Invalid synthetic data line format: {data_line}")
                continue
            
            # Extract attribute-value pairs
            attr_values_text = data_line.split("Employee has ")[1].strip()
            
            # Use a simpler approach to parse attribute-value pairs
            attr_values = {}
            pairs = attr_values_text.split(',')
            
            for pair in pairs:
                if '=' in pair:
                    attr, value = pair.split('=', 1)
                    attr = attr.strip()
                    value = value.strip()
                    
                    # Handle quoted values
                    if value.startswith('"') and value.endswith('"'):
                        value = value[1:-1]
                    
                    attr_values[attr] = value
            
            logger.info(f"Parsed attribute-value pairs: {attr_values}")
            
            # Map attribute names to column names
            column_mapping = {
                'employeeId': 'employeeid',
                'firstName': 'firstname',
                'lastName': 'lastname',
                'email': 'email',
                'phoneNumber': 'phonenumber',
                'departmentId': 'departmentid',
                'managerId': 'managerid',
                'hireDate': 'hiredate',
                'status': 'status',
                'salary': 'salary',
                'performanceRating': 'performancerating'
            }
            
            # Prepare column names and values for INSERT
            columns = []
            values = []
            placeholders = []
            
            for attr, value in attr_values.items():
                # Map attribute name to column name
                if attr in column_mapping:
                    col_name = column_mapping[attr]
                    columns.append(f'"{col_name}"')
                    
                    # Handle quoted values
                    if value.startswith('"') and value.endswith('"'):
                        value = value[1:-1]
                    
                    values.append(value)
                    placeholders.append('%s')
            
            # Add created_at and updated_at
            columns.append('created_at')
            columns.append('updated_at')
            placeholders.append('NOW()')
            placeholders.append('NOW()')
            
            # Build INSERT query
            query = f"""
                INSERT INTO {schema_name}."{table_name}" (
                    {', '.join(columns)}
                ) VALUES (
                    {', '.join(placeholders)}
                )
            """
            
            # Execute the query
            with conn.cursor() as cursor:
                cursor.execute(query, values)
            
            conn.commit()
            logger.info(f"Inserted synthetic data into table '{table_name}'")
        
        # Verify the synthetic data was inserted
        with conn.cursor() as cursor:
            cursor.execute(f"""
                SELECT *
                FROM {schema_name}.{table_name}
                WHERE employeeid IN ('1', '2')
            """)
            
            rows = cursor.fetchall()
            
            if not rows:
                logger.warning("No synthetic data found in the employee table after insertion")
            else:
                logger.info(f"Found {len(rows)} rows of synthetic data in the employee table")
                
                # Get column names
                cursor.execute(f"""
                    SELECT column_name
                    FROM information_schema.columns
                    WHERE table_schema = %s
                    AND table_name = %s
                    ORDER BY ordinal_position
                """, (schema_name, table_name))
                
                columns = [col[0] for col in cursor.fetchall()]
                
                # Print column names
                logger.info("Columns: " + ", ".join(columns))
                
                # Print rows
                for row in rows:
                    logger.info(f"Row: {row}")
        
        logger.info("Successfully deployed synthetic data")
    except Exception as e:
        logger.error(f"Error deploying synthetic data: {str(e)}")
    finally:
        if conn:
            conn.close()

if __name__ == "__main__":
    deploy_synthetic_data()
