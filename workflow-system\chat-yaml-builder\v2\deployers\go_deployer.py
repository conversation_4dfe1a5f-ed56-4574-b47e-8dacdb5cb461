"""
GO Deployer for YAML Builder v2

This module provides functionality for deploying parsed Global Objective (GO) data to the database.
"""

import os
import sys
import logging
from typing import Dict, List, Tuple, Any, Optional

# Import database utilities
from db_utils import execute_query, create_table, add_column, add_constraint

# Set up logging
logger = logging.getLogger('go_deployer')

def deploy_go_definitions(go_data: Dict, schema_name: str) -> Tuple[bool, List[str]]:
    """
    Deploy parsed GO data to the database.
    
    Args:
        go_data: Parsed GO data
        schema_name: Schema name to deploy to
        
    Returns:
        Tuple containing:
            - <PERSON><PERSON><PERSON> indicating if deployment was successful
            - List of messages (warnings, errors, or success messages)
    """
    messages = []
    
    try:
        logger.info(f"Deploying global objectives to schema {schema_name}")
        
        # Check if GO data is valid
        if not go_data or 'global_objectives' not in go_data:
            logger.error("Invalid GO data: 'global_objectives' key not found")
            return False, ["Invalid GO data: 'global_objectives' key not found"]
        
        global_objectives = go_data['global_objectives']
        
        # Create global_objectives table if it doesn't exist
        success, create_messages = create_global_objectives_table(schema_name)
        messages.extend(create_messages)
        
        if not success:
            return False, messages
        
        # Create input_stack table if it doesn't exist
        success, create_messages = create_input_stack_table(schema_name)
        messages.extend(create_messages)
        
        if not success:
            return False, messages
        
        # Create output_stack table if it doesn't exist
        success, create_messages = create_output_stack_table(schema_name)
        messages.extend(create_messages)
        
        if not success:
            return False, messages
        
        # Create output_triggers table if it doesn't exist
        success, create_messages = create_output_triggers_table(schema_name)
        messages.extend(create_messages)
        
        if not success:
            return False, messages
        
        # Create process_flow table if it doesn't exist
        success, create_messages = create_process_flow_table(schema_name)
        messages.extend(create_messages)
        
        if not success:
            return False, messages
        
        # Create parallel_flows table if it doesn't exist
        success, create_messages = create_parallel_flows_table(schema_name)
        messages.extend(create_messages)
        
        if not success:
            return False, messages
        
        # Create rollback_pathways table if it doesn't exist
        success, create_messages = create_rollback_pathways_table(schema_name)
        messages.extend(create_messages)
        
        if not success:
            return False, messages
        
        # Create go_business_rules table if it doesn't exist
        success, create_messages = create_go_business_rules_table(schema_name)
        messages.extend(create_messages)
        
        if not success:
            return False, messages
        
        # Create data_constraints table if it doesn't exist
        success, create_messages = create_data_constraints_table(schema_name)
        messages.extend(create_messages)
        
        if not success:
            return False, messages
        
        # Deploy each global objective
        for go_id, go in global_objectives.items():
            success, deploy_messages = deploy_global_objective(go, schema_name)
            messages.extend(deploy_messages)
            
            if not success:
                return False, messages
        
        messages.append(f"Successfully deployed {len(global_objectives)} global objectives to schema {schema_name}")
        return True, messages
    except Exception as e:
        logger.error(f"Error deploying global objectives to schema {schema_name}: {str(e)}", exc_info=True)
        messages.append(f"Error deploying global objectives to schema {schema_name}: {str(e)}")
        return False, messages

def create_global_objectives_table(schema_name: str) -> Tuple[bool, List[str]]:
    """
    Create global_objectives table if it doesn't exist.
    
    Args:
        schema_name: Schema name to deploy to
        
    Returns:
        Tuple containing:
            - Boolean indicating if creation was successful
            - List of messages (warnings, errors, or success messages)
    """
    return create_table(
        schema_name,
        'global_objectives',
        """
        go_id VARCHAR(255) PRIMARY KEY,
        name VARCHAR(255) NOT NULL,
        description TEXT,
        version VARCHAR(50) NOT NULL,
        status VARCHAR(50) NOT NULL,
        primary_entity VARCHAR(255),
        tenant_name VARCHAR(255),
        book_name VARCHAR(255),
        chapter_name VARCHAR(255),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        """
    )

def create_input_stack_table(schema_name: str) -> Tuple[bool, List[str]]:
    """
    Create input_stack table if it doesn't exist.
    
    Args:
        schema_name: Schema name to deploy to
        
    Returns:
        Tuple containing:
            - Boolean indicating if creation was successful
            - List of messages (warnings, errors, or success messages)
    """
    return create_table(
        schema_name,
        'input_stack',
        """
        input_id SERIAL PRIMARY KEY,
        go_id VARCHAR(255) NOT NULL,
        slot_id VARCHAR(50) NOT NULL,
        entity_reference VARCHAR(255) NOT NULL,
        attribute_reference VARCHAR(255),
        required BOOLEAN DEFAULT FALSE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        CONSTRAINT input_stack_go_id_fkey FOREIGN KEY (go_id) REFERENCES {schema_name}.global_objectives (go_id) ON DELETE CASCADE,
        CONSTRAINT input_stack_go_id_slot_id_unique UNIQUE (go_id, slot_id)
        """.format(schema_name=schema_name)
    )

def create_output_stack_table(schema_name: str) -> Tuple[bool, List[str]]:
    """
    Create output_stack table if it doesn't exist.
    
    Args:
        schema_name: Schema name to deploy to
        
    Returns:
        Tuple containing:
            - Boolean indicating if creation was successful
            - List of messages (warnings, errors, or success messages)
    """
    return create_table(
        schema_name,
        'output_stack',
        """
        output_id SERIAL PRIMARY KEY,
        go_id VARCHAR(255) NOT NULL,
        slot_id VARCHAR(50) NOT NULL,
        output_entity VARCHAR(255) NOT NULL,
        output_attribute VARCHAR(255),
        data_type VARCHAR(50),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        CONSTRAINT output_stack_go_id_fkey FOREIGN KEY (go_id) REFERENCES {schema_name}.global_objectives (go_id) ON DELETE CASCADE,
        CONSTRAINT output_stack_go_id_slot_id_unique UNIQUE (go_id, slot_id)
        """.format(schema_name=schema_name)
    )

def create_output_triggers_table(schema_name: str) -> Tuple[bool, List[str]]:
    """
    Create output_triggers table if it doesn't exist.
    
    Args:
        schema_name: Schema name to deploy to
        
    Returns:
        Tuple containing:
            - Boolean indicating if creation was successful
            - List of messages (warnings, errors, or success messages)
    """
    return create_table(
        schema_name,
        'output_triggers',
        """
        trigger_id SERIAL PRIMARY KEY,
        go_id VARCHAR(255) NOT NULL,
        output_item_id VARCHAR(50) NOT NULL,
        target_objective VARCHAR(255) NOT NULL,
        target_input VARCHAR(50) NOT NULL,
        mapping_type VARCHAR(50) NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        CONSTRAINT output_triggers_go_id_fkey FOREIGN KEY (go_id) REFERENCES {schema_name}.global_objectives (go_id) ON DELETE CASCADE
        """.format(schema_name=schema_name)
    )

def create_process_flow_table(schema_name: str) -> Tuple[bool, List[str]]:
    """
    Create process_flow table if it doesn't exist.
    
    Args:
        schema_name: Schema name to deploy to
        
    Returns:
        Tuple containing:
            - Boolean indicating if creation was successful
            - List of messages (warnings, errors, or success messages)
    """
    return create_table(
        schema_name,
        'process_flow',
        """
        flow_id SERIAL PRIMARY KEY,
        go_id VARCHAR(255) NOT NULL,
        sequence_number INTEGER NOT NULL,
        lo_name VARCHAR(255) NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        CONSTRAINT process_flow_go_id_fkey FOREIGN KEY (go_id) REFERENCES {schema_name}.global_objectives (go_id) ON DELETE CASCADE,
        CONSTRAINT process_flow_go_id_sequence_number_unique UNIQUE (go_id, sequence_number)
        """.format(schema_name=schema_name)
    )

def create_parallel_flows_table(schema_name: str) -> Tuple[bool, List[str]]:
    """
    Create parallel_flows table if it doesn't exist.
    
    Args:
        schema_name: Schema name to deploy to
        
    Returns:
        Tuple containing:
            - Boolean indicating if creation was successful
            - List of messages (warnings, errors, or success messages)
    """
    return create_table(
        schema_name,
        'parallel_flows',
        """
        parallel_flow_id SERIAL PRIMARY KEY,
        go_id VARCHAR(255) NOT NULL,
        lo_name VARCHAR(255) NOT NULL,
        join_at VARCHAR(255) NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        CONSTRAINT parallel_flows_go_id_fkey FOREIGN KEY (go_id) REFERENCES {schema_name}.global_objectives (go_id) ON DELETE CASCADE
        """.format(schema_name=schema_name)
    )

def create_rollback_pathways_table(schema_name: str) -> Tuple[bool, List[str]]:
    """
    Create rollback_pathways table if it doesn't exist.
    
    Args:
        schema_name: Schema name to deploy to
        
    Returns:
        Tuple containing:
            - Boolean indicating if creation was successful
            - List of messages (warnings, errors, or success messages)
    """
    return create_table(
        schema_name,
        'rollback_pathways',
        """
        pathway_id SERIAL PRIMARY KEY,
        go_id VARCHAR(255) NOT NULL,
        source_lo VARCHAR(255) NOT NULL,
        target_lo VARCHAR(255) NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        CONSTRAINT rollback_pathways_go_id_fkey FOREIGN KEY (go_id) REFERENCES {schema_name}.global_objectives (go_id) ON DELETE CASCADE
        """.format(schema_name=schema_name)
    )

def create_go_business_rules_table(schema_name: str) -> Tuple[bool, List[str]]:
    """
    Create go_business_rules table if it doesn't exist.
    
    Args:
        schema_name: Schema name to deploy to
        
    Returns:
        Tuple containing:
            - Boolean indicating if creation was successful
            - List of messages (warnings, errors, or success messages)
    """
    return create_table(
        schema_name,
        'go_business_rules',
        """
        rule_id SERIAL PRIMARY KEY,
        go_id VARCHAR(255) NOT NULL,
        description TEXT NOT NULL,
        enforced_by VARCHAR(255) NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        CONSTRAINT go_business_rules_go_id_fkey FOREIGN KEY (go_id) REFERENCES {schema_name}.global_objectives (go_id) ON DELETE CASCADE
        """.format(schema_name=schema_name)
    )

def create_data_constraints_table(schema_name: str) -> Tuple[bool, List[str]]:
    """
    Create data_constraints table if it doesn't exist.
    
    Args:
        schema_name: Schema name to deploy to
        
    Returns:
        Tuple containing:
            - Boolean indicating if creation was successful
            - List of messages (warnings, errors, or success messages)
    """
    return create_table(
        schema_name,
        'data_constraints',
        """
        constraint_id SERIAL PRIMARY KEY,
        go_id VARCHAR(255) NOT NULL,
        entity VARCHAR(255) NOT NULL,
        attribute VARCHAR(255) NOT NULL,
        rule TEXT NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        CONSTRAINT data_constraints_go_id_fkey FOREIGN KEY (go_id) REFERENCES {schema_name}.global_objectives (go_id) ON DELETE CASCADE
        """.format(schema_name=schema_name)
    )

def deploy_global_objective(go: Dict, schema_name: str) -> Tuple[bool, List[str]]:
    """
    Deploy a global objective to the database.
    
    Args:
        go: Global objective data
        schema_name: Schema name to deploy to
        
    Returns:
        Tuple containing:
            - Boolean indicating if deployment was successful
            - List of messages (warnings, errors, or success messages)
    """
    messages = []
    
    try:
        go_id = go.get('go_id')
        logger.info(f"Deploying global objective {go_id} to schema {schema_name}")
        
        # Insert global objective
        success, query_messages = insert_global_objective(go, schema_name)
        messages.extend(query_messages)
        
        if not success:
            return False, messages
        
        # Insert input stack
        if 'input_stack' in go:
            for input_item in go['input_stack']:
                success, query_messages = insert_input_stack_item(go_id, input_item, schema_name)
                messages.extend(query_messages)
                
                if not success:
                    return False, messages
        
        # Insert output stack
        if 'output_stack' in go:
            for output_item in go['output_stack']:
                success, query_messages = insert_output_stack_item(go_id, output_item, schema_name)
                messages.extend(query_messages)
                
                if not success:
                    return False, messages
        
        # Insert output triggers
        if 'output_triggers' in go:
            for trigger in go['output_triggers']:
                success, query_messages = insert_output_trigger(go_id, trigger, schema_name)
                messages.extend(query_messages)
                
                if not success:
                    return False, messages
        
        # Insert process flow
        if 'process_flow' in go:
            for flow_step in go['process_flow']:
                success, query_messages = insert_process_flow_step(go_id, flow_step, schema_name)
                messages.extend(query_messages)
                
                if not success:
                    return False, messages
        
        # Insert parallel flows
        if 'parallel_flows' in go:
            for flow in go['parallel_flows']:
                success, query_messages = insert_parallel_flow(go_id, flow, schema_name)
                messages.extend(query_messages)
                
                if not success:
                    return False, messages
        
        # Insert rollback pathways
        if 'rollback_pathways' in go:
            for pathway in go['rollback_pathways']:
                success, query_messages = insert_rollback_pathway(go_id, pathway, schema_name)
                messages.extend(query_messages)
                
                if not success:
                    return False, messages
        
        # Insert business rules
        if 'business_rules' in go:
            for rule in go['business_rules']:
                success, query_messages = insert_go_business_rule(go_id, rule, schema_name)
                messages.extend(query_messages)
                
                if not success:
                    return False, messages
        
        # Insert data constraints
        if 'data_constraints' in go:
            for constraint in go['data_constraints']:
                success, query_messages = insert_data_constraint(go_id, constraint, schema_name)
                messages.extend(query_messages)
                
                if not success:
                    return False, messages
        
        messages.append(f"Successfully deployed global objective {go_id} to schema {schema_name}")
        return True, messages
    except Exception as e:
        logger.error(f"Error deploying global objective {go.get('go_id')} to schema {schema_name}: {str(e)}", exc_info=True)
        messages.append(f"Error deploying global objective {go.get('go_id')} to schema {schema_name}: {str(e)}")
        return False, messages

def insert_global_objective(go: Dict, schema_name: str) -> Tuple[bool, List[str]]:
    """
    Insert a global objective into the database.
    
    Args:
        go: Global objective data
        schema_name: Schema name to deploy to
        
    Returns:
        Tuple containing:
            - Boolean indicating if insertion was successful
            - List of messages (warnings, errors, or success messages)
    """
    messages = []
    
    try:
        go_id = go.get('go_id')
        name = go.get('name', '')
        description = go.get('description', '')
        version = go.get('version', '1.0')
        status = go.get('status', 'Active')
        primary_entity = go.get('primary_entity', '')
        tenant_name = go.get('tenant_name', '')
        book_name = go.get('book_name', '')
        chapter_name = go.get('chapter_name', '')
        
        # Check if global objective already exists
        success, query_messages, result = execute_query(
            f"""
            SELECT go_id FROM {schema_name}.global_objectives
            WHERE go_id = %s
            """,
            (go_id,)
        )
        
        if not success:
            messages.extend(query_messages)
            return False, messages
        
        if result:
            # Global objective already exists, update it
            success, query_messages, _ = execute_query(
                f"""
                UPDATE {schema_name}.global_objectives
                SET name = %s, description = %s, version = %s, status = %s,
                    primary_entity = %s, tenant_name = %s, book_name = %s, chapter_name = %s,
                    updated_at = CURRENT_TIMESTAMP
                WHERE go_id = %s
                """,
                (name, description, version, status, primary_entity, tenant_name, book_name, chapter_name, go_id)
            )
            
            if not success:
                messages.extend(query_messages)
                return False, messages
            
            messages.append(f"Updated global objective {go_id} in schema {schema_name}")
        else:
            # Global objective doesn't exist, insert it
            success, query_messages, _ = execute_query(
                f"""
                INSERT INTO {schema_name}.global_objectives
                (go_id, name, description, version, status, primary_entity, tenant_name, book_name, chapter_name)
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s)
                """,
                (go_id, name, description, version, status, primary_entity, tenant_name, book_name, chapter_name)
            )
            
            if not success:
                messages.extend(query_messages)
                return False, messages
            
            messages.append(f"Inserted global objective {go_id} into schema {schema_name}")
        
        return True, messages
    except Exception as e:
        logger.error(f"Error inserting global objective {go.get('go_id')} into schema {schema_name}: {str(e)}", exc_info=True)
        messages.append(f"Error inserting global objective {go.get('go_id')} into schema {schema_name}: {str(e)}")
        return False, messages

def insert_input_stack_item(go_id: str, input_item: Dict, schema_name: str) -> Tuple[bool, List[str]]:
    """
    Insert an input stack item into the database.
    
    Args:
        go_id: Global objective ID
        input_item: Input stack item data
        schema_name: Schema name to deploy to
        
    Returns:
        Tuple containing:
            - Boolean indicating if insertion was successful
            - List of messages (warnings, errors, or success messages)
    """
    messages = []
    
    try:
        slot_id = input_item.get('slot_id')
        entity_reference = input_item.get('entity_reference')
        attribute_reference = input_item.get('attribute_reference', None)
        required = input_item.get('required', False)
        
        # Check if input stack item already exists
        success, query_messages, result = execute_query(
            f"""
            SELECT input_id FROM {schema_name}.input_stack
            WHERE go_id = %s AND slot_id = %s
            """,
            (go_id, slot_id)
        )
        
        if not success:
            messages.extend(query_messages)
            return False, messages
        
        if result:
            # Input stack item already exists, update it
            success, query_messages, _ = execute_query(
                f"""
                UPDATE {schema_name}.input_stack
                SET entity_reference = %s, attribute_reference = %s, required = %s, updated_at = CURRENT_TIMESTAMP
                WHERE go_id = %s AND slot_id = %s
                """,
                (entity_reference, attribute_reference, required, go_id, slot_id)
            )
            
            if not success:
                messages.extend(query_messages)
                return False, messages
            
            messages.append(f"Updated input stack item {slot_id} for global objective {go_id} in schema {schema_name}")
        else:
            # Input stack item doesn't exist, insert it
            success, query_messages, _ = execute_query(
                f"""
                INSERT INTO {schema_name}.input_stack
                (go_id, slot_id, entity_reference, attribute_reference, required)
                VALUES (%s, %s, %s, %s, %s)
                """,
                (go_id, slot_id, entity_reference, attribute_reference, required)
            )
            
            if not success:
                messages.extend(query_messages)
                return False, messages
            
            messages.append(f"Inserted input stack item {slot_id} for global objective {go_id} into schema {schema_name}")
        
        return True, messages
    except Exception as e:
        logger.error(f"Error inserting input stack item for global objective {go_id} into schema {schema_name}: {str(e)}", exc_info=True)
        messages.append(f"Error inserting input stack item for global objective {go_id} into schema {schema_name}: {str(e)}")
        return False, messages

def insert_output_stack_item(go_id: str, output_item: Dict, schema_name: str) -> Tuple[bool, List[str]]:
    """
    Insert an output stack item into the database.
    
    Args:
        go_id: Global objective ID
        output_item: Output stack item data
        schema_name: Schema name to deploy to
        
    Returns:
        Tuple containing:
            - Boolean indicating if insertion was successful
            - List of messages (warnings, errors, or success messages)
    """
    messages = []
    
    try:
        slot_id = output_item.get('slot_id')
        output_entity = output_item.get('output_entity')
        output_attribute = output_item.get('output_attribute', None)
        data_type = output_item.get('data_type', None)
        
        # Check if output stack item already exists
        success, query_messages, result = execute_query(
            f"""
            SELECT output_id FROM {schema_name}.output_stack
            WHERE go_id = %s AND slot_id = %s
            """,
            (go_id, slot_id)
        )
        
        if not success:
            messages.extend(query_messages)
            return False, messages
        
        if result:
            # Output stack item already exists, update it
            success, query_messages, _ = execute_query(
                f"""
                UPDATE {schema_name}.output_stack
                SET output_entity = %s, output_attribute = %s, data_type = %s, updated_at = CURRENT_TIMESTAMP
                WHERE go_id = %s AND slot_id = %s
                """,
                (output_entity, output_attribute, data_type, go_id, slot_id)
            )
            
            if not success:
                messages.extend(query_messages)
                return False, messages
            
            messages.append(f"Updated output stack item {slot_id} for global objective {go_id} in schema {schema_name}")
        else:
            # Output stack item doesn't exist, insert it
            success, query_messages, _ = execute_query(
                f"""
                INSERT INTO {schema_name}.output_stack
                (go_id, slot_id, output_entity, output_attribute, data_type)
                VALUES (%s, %s, %s, %s, %s)
                """,
                (go_id, slot_id, output_entity, output_attribute, data_type)
            )
            
            if not success:
                messages.extend(query_messages)
                return False, messages
            
            messages.append(f"Inserted output stack item {slot_id} for global objective {go_id} into schema {schema_name}")
        
        return True, messages
    except Exception as e:
        logger.error(f"Error inserting output stack item for global objective {go_id} into schema {schema_name}: {str(e)}", exc_info=True)
        messages.append(f"Error inserting output stack item for global objective {go_id} into schema {schema_name}: {str(e)}")
        return False, messages

def insert_output_trigger(go_id: str, trigger: Dict, schema_name: str) -> Tuple[bool, List[str]]:
    """
    Insert an output trigger into the database.
    
    Args:
        go_id: Global objective ID
        trigger: Output trigger data
        schema_name: Schema name to deploy to
        
    Returns:
        Tuple containing:
            - Boolean indicating if insertion was successful
            - List of messages (warnings, errors, or success messages)
    """
    messages = []
    
    try:
        output_item_id = trigger.get('output_item_id')
        target_objective = trigger.get('target_objective')
        target_input = trigger.get('target_input')
        mapping_type = trigger.get('mapping_type', 'direct')
        
        # Check if output trigger already exists
        success, query_messages, result = execute_query(
            f"""
            SELECT trigger_id FROM {schema_name}.output_triggers
            WHERE go_id = %s AND output_item_id = %s AND target_objective = %s AND target_input = %s
            """,
            (go_id, output_item_id, target_objective, target_input)
        )
        
        if not success:
            messages.extend(query_messages)
            return False, messages
        
        if result:
            # Output trigger already exists, update it
            trigger_id = result[0][0]
            
            success, query_messages, _ = execute_query(
                f"""
                UPDATE {schema_name}.output_triggers
                SET mapping_type = %s, updated_at = CURRENT_TIMESTAMP
                WHERE trigger_id = %s
                """,
                (mapping_type, trigger_id)
            )
            
            if not success:
                messages.extend(query_messages)
                return False, messages
            
            messages.append(f"Updated output trigger for global objective {go_id} in schema {schema_name}")
        else:
            # Output trigger doesn't exist, insert it
            success, query_messages, _ = execute_query(
                f"""
                INSERT INTO {schema_name}.output_triggers
                (go_id, output_item_id, target_objective, target_input, mapping_type)
                VALUES (%s, %s, %s, %s, %s)
                """,
                (go_id, output_item_id, target_objective, target_input, mapping_type)
            )
            
            if not success:
                messages.extend(query_messages)
                return False, messages
            
            messages.append(f"Inserted output trigger for global objective {go_id} into schema {schema_name}")
        
        return True, messages
    except Exception as e:
        logger.error(f"Error inserting output trigger for global objective {go_id} into schema {schema_name}: {str(e)}", exc_info=True)
        messages.append(f"Error inserting output trigger for global objective {go_id} into schema {schema_name}: {str(e)}")
        return False, messages

def insert_process_flow_step(go_id: str, flow_step: Dict, schema_name: str) -> Tuple[bool, List[str]]:
    """
    Insert a process flow step into the database.
    
    Args:
        go_id: Global objective ID
        flow_step: Process flow step data
        schema_name: Schema name to deploy to
        
    Returns:
        Tuple containing:
            - Boolean indicating if insertion was successful
            - List of messages (warnings, errors, or success messages)
    """
    messages = []
    
    try:
        sequence_number = flow_step.get('sequence_number')
        lo_name = flow_step.get('lo_name')
        
        # Check if process flow step already exists
        success, query_messages, result = execute_query(
            f"""
            SELECT flow_id FROM {schema_name}.process_flow
            WHERE go_id = %s AND sequence_number = %s
            """,
            (go_id, sequence_number)
        )
        
        if not success:
            messages.extend(query_messages)
            return False, messages
        
        if result:
            # Process flow step already exists, update it
            flow_id = result[0][0]
            
            success, query_messages, _ = execute_query(
                f"""
                UPDATE {schema_name}.process_flow
                SET lo_name = %s, updated_at = CURRENT_TIMESTAMP
                WHERE flow_id = %s
                """,
                (lo_name, flow_id)
            )
            
            if not success:
                messages.extend(query_messages)
                return False, messages
            
            messages.append(f"Updated process flow step {sequence_number} for global objective {go_id} in schema {schema_name}")
        else:
            # Process flow step doesn't exist, insert it
            success, query_messages, _ = execute_query(
                f"""
                INSERT INTO {schema_name}.process_flow
                (go_id, sequence_number, lo_name)
                VALUES (%s, %s, %s)
                """,
                (go_id, sequence_number, lo_name)
            )
            
            if not success:
                messages.extend(query_messages)
                return False, messages
            
            messages.append(f"Inserted process flow step {sequence_number} for global objective {go_id} into schema {schema_name}")
        
        return True, messages
    except Exception as e:
        logger.error(f"Error inserting process flow step for global objective {go_id} into schema {schema_name}: {str(e)}", exc_info=True)
        messages.append(f"Error inserting process flow step for global objective {go_id} into schema {schema_name}: {str(e)}")
        return False, messages

def insert_parallel_flow(go_id: str, flow: Dict, schema_name: str) -> Tuple[bool, List[str]]:
    """
    Insert a parallel flow into the database.
    
    Args:
        go_id: Global objective ID
        flow: Parallel flow data
        schema_name: Schema name to deploy to
        
    Returns:
        Tuple containing:
            - Boolean indicating if insertion was successful
            - List of messages (warnings, errors, or success messages)
    """
    messages = []
    
    try:
        lo_name = flow.get('lo_name')
        join_at = flow.get('join_at')
        
        # Check if parallel flow already exists
        success, query_messages, result = execute_query(
            f"""
            SELECT parallel_flow_id FROM {schema_name}.parallel_flows
            WHERE go_id = %s AND lo_name = %s
            """,
            (go_id, lo_name)
        )
        
        if not success:
            messages.extend(query_messages)
            return False, messages
        
        if result:
            # Parallel flow already exists, update it
            parallel_flow_id = result[0][0]
            
            success, query_messages, _ = execute_query(
                f"""
                UPDATE {schema_name}.parallel_flows
                SET join_at = %s, updated_at = CURRENT_TIMESTAMP
                WHERE parallel_flow_id = %s
                """,
                (join_at, parallel_flow_id)
            )
            
            if not success:
                messages.extend(query_messages)
                return False, messages
            
            messages.append(f"Updated parallel flow for global objective {go_id} in schema {schema_name}")
        else:
            # Parallel flow doesn't exist, insert it
            success, query_messages, _ = execute_query(
                f"""
                INSERT INTO {schema_name}.parallel_flows
                (go_id, lo_name, join_at)
                VALUES (%s, %s, %s)
                """,
                (go_id, lo_name, join_at)
            )
            
            if not success:
                messages.extend(query_messages)
                return False, messages
            
            messages.append(f"Inserted parallel flow for global objective {go_id} into schema {schema_name}")
        
        return True, messages
    except Exception as e:
        logger.error(f"Error inserting parallel flow for global objective {go_id} into schema {schema_name}: {str(e)}", exc_info=True)
        messages.append(f"Error inserting parallel flow for global objective {go_id} into schema {schema_name}: {str(e)}")
        return False, messages

def insert_rollback_pathway(go_id: str, pathway: Dict, schema_name: str) -> Tuple[bool, List[str]]:
    """
    Insert a rollback pathway into the database.
    
    Args:
        go_id: Global objective ID
        pathway: Rollback pathway data
        schema_name: Schema name to deploy to
        
    Returns:
        Tuple containing:
            - Boolean indicating if insertion was successful
            - List of messages (warnings, errors, or success messages)
    """
    messages = []
    
    try:
        source_lo = pathway.get('source_lo')
        target_lo = pathway.get('target_lo')
        
        # Check if rollback pathway already exists
        success, query_messages, result = execute_query(
            f"""
            SELECT pathway_id FROM {schema_name}.rollback_pathways
            WHERE go_id = %s AND source_lo = %s AND target_lo = %s
            """,
            (go_id, source_lo, target_lo)
        )
        
        if not success:
            messages.extend(query_messages)
            return False, messages
        
        if result:
            # Rollback pathway already exists, update it
            pathway_id = result[0][0]
            
            success, query_messages, _ = execute_query(
                f"""
                UPDATE {schema_name}.rollback_pathways
                SET updated_at = CURRENT_TIMESTAMP
                WHERE pathway_id = %s
                """,
                (pathway_id,)
            )
            
            if not success:
                messages.extend(query_messages)
                return False, messages
            
            messages.append(f"Updated rollback pathway for global objective {go_id} in schema {schema_name}")
        else:
            # Rollback pathway doesn't exist, insert it
            success, query_messages, _ = execute_query(
                f"""
                INSERT INTO {schema_name}.rollback_pathways
                (go_id, source_lo, target_lo)
                VALUES (%s, %s, %s)
                """,
                (go_id, source_lo, target_lo)
            )
            
            if not success:
                messages.extend(query_messages)
                return False, messages
            
            messages.append(f"Inserted rollback pathway for global objective {go_id} into schema {schema_name}")
        
        return True, messages
    except Exception as e:
        logger.error(f"Error inserting rollback pathway for global objective {go_id} into schema {schema_name}: {str(e)}", exc_info=True)
        messages.append(f"Error inserting rollback pathway for global objective {go_id} into schema {schema_name}: {str(e)}")
        return False, messages

def insert_go_business_rule(go_id: str, rule: Dict, schema_name: str) -> Tuple[bool, List[str]]:
    """
    Insert a business rule into the database.
    
    Args:
        go_id: Global objective ID
        rule: Business rule data
        schema_name: Schema name to deploy to
        
    Returns:
        Tuple containing:
            - Boolean indicating if insertion was successful
            - List of messages (warnings, errors, or success messages)
    """
    messages = []
    
    try:
        description = rule.get('description')
        enforced_by = rule.get('enforced_by')
        
        # Check if business rule already exists
        success, query_messages, result = execute_query(
            f"""
            SELECT rule_id FROM {schema_name}.go_business_rules
            WHERE go_id = %s AND description = %s
            """,
            (go_id, description)
        )
        
        if not success:
            messages.extend(query_messages)
            return False, messages
        
        if result:
            # Business rule already exists, update it
            rule_id = result[0][0]
            
            success, query_messages, _ = execute_query(
                f"""
                UPDATE {schema_name}.go_business_rules
                SET enforced_by = %s, updated_at = CURRENT_TIMESTAMP
                WHERE rule_id = %s
                """,
                (enforced_by, rule_id)
            )
            
            if not success:
                messages.extend(query_messages)
                return False, messages
            
            messages.append(f"Updated business rule for global objective {go_id} in schema {schema_name}")
        else:
            # Business rule doesn't exist, insert it
            success, query_messages, _ = execute_query(
                f"""
                INSERT INTO {schema_name}.go_business_rules
                (go_id, description, enforced_by)
                VALUES (%s, %s, %s)
                """,
                (go_id, description, enforced_by)
            )
            
            if not success:
                messages.extend(query_messages)
                return False, messages
            
            messages.append(f"Inserted business rule for global objective {go_id} into schema {schema_name}")
        
        return True, messages
    except Exception as e:
        logger.error(f"Error inserting business rule for global objective {go_id} into schema {schema_name}: {str(e)}", exc_info=True)
        messages.append(f"Error inserting business rule for global objective {go_id} into schema {schema_name}: {str(e)}")
        return False, messages

def insert_data_constraint(go_id: str, constraint: Dict, schema_name: str) -> Tuple[bool, List[str]]:
    """
    Insert a data constraint into the database.
    
    Args:
        go_id: Global objective ID
        constraint: Data constraint data
        schema_name: Schema name to deploy to
        
    Returns:
        Tuple containing:
            - Boolean indicating if insertion was successful
            - List of messages (warnings, errors, or success messages)
    """
    messages = []
    
    try:
        entity = constraint.get('entity')
        attribute = constraint.get('attribute')
        rule = constraint.get('rule')
        
        # Check if data constraint already exists
        success, query_messages, result = execute_query(
            f"""
            SELECT constraint_id FROM {schema_name}.data_constraints
            WHERE go_id = %s AND entity = %s AND attribute = %s
            """,
            (go_id, entity, attribute)
        )
        
        if not success:
            messages.extend(query_messages)
            return False, messages
        
        if result:
            # Data constraint already exists, update it
            constraint_id = result[0][0]
            
            success, query_messages, _ = execute_query(
                f"""
                UPDATE {schema_name}.data_constraints
                SET rule = %s, updated_at = CURRENT_TIMESTAMP
                WHERE constraint_id = %s
                """,
                (rule, constraint_id)
            )
            
            if not success:
                messages.extend(query_messages)
                return False, messages
            
            messages.append(f"Updated data constraint for global objective {go_id} in schema {schema_name}")
        else:
            # Data constraint doesn't exist, insert it
            success, query_messages, _ = execute_query(
                f"""
                INSERT INTO {schema_name}.data_constraints
                (go_id, entity, attribute, rule)
                VALUES (%s, %s, %s, %s)
                """,
                (go_id, entity, attribute, rule)
            )
            
            if not success:
                messages.extend(query_messages)
                return False, messages
            
            messages.append(f"Inserted data constraint for global objective {go_id} into schema {schema_name}")
        
        return True, messages
    except Exception as e:
        logger.error(f"Error inserting data constraint for global objective {go_id} into schema {schema_name}: {str(e)}", exc_info=True)
        messages.append(f"Error inserting data constraint for global objective {go_id} into schema {schema_name}: {str(e)}")
        return False, messages
