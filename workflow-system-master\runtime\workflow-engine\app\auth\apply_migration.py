#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to apply RBAC database schema migration.

This script executes the SQL script to create the necessary tables for the RBAC implementation.
"""

import os
import sys
import argparse
import logging
from sqlalchemy import create_engine, text

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("apply_migration")

def parse_args():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description='Apply RBAC database schema migration')
    parser.add_argument(
        '--db-url',
        type=str,
        default=os.getenv('DATABASE_URL', 'postgresql://postgres:workflow_postgres_secure_password@localhost:5433/workflow_system'),
        help='Database URL (default: from DATABASE_URL environment variable)'
    )
    parser.add_argument(
        '--sql-file',
        type=str,
        default='rbac_schema_migration.sql',
        help='SQL file to execute (default: rbac_schema_migration.sql)'
    )
    parser.add_argument(
        '--dry-run',
        action='store_true',
        help='Print SQL statements without executing them'
    )
    return parser.parse_args()

def read_sql_file(file_path):
    """Read SQL file and return its contents."""
    try:
        with open(file_path, 'r') as f:
            return f.read()
    except Exception as e:
        logger.error(f"Error reading SQL file: {e}")
        sys.exit(1)

def apply_migration(db_url, sql_content, dry_run=False):
    """Apply database migration."""
    try:
        if dry_run:
            logger.info("Dry run mode - SQL statements:")
            logger.info(sql_content)
            return

        # Create database engine
        engine = create_engine(db_url)
        
        # Execute SQL statements
        with engine.connect() as connection:
            # Start transaction
            with connection.begin():
                # Split SQL statements by semicolon
                statements = sql_content.split(';')
                
                # Execute each statement
                for statement in statements:
                    if statement.strip():
                        logger.info(f"Executing SQL statement: {statement[:50]}...")
                        connection.execute(text(statement))
                
                logger.info("Migration applied successfully")
    except Exception as e:
        logger.error(f"Error applying migration: {e}")
        sys.exit(1)

def main():
    """Main function."""
    args = parse_args()
    
    # Get current directory
    current_dir = os.path.dirname(os.path.abspath(__file__))
    
    # Get SQL file path
    sql_file_path = os.path.join(current_dir, args.sql_file)
    
    # Read SQL file
    sql_content = read_sql_file(sql_file_path)
    
    # Apply migration
    apply_migration(args.db_url, sql_content, args.dry_run)

if __name__ == '__main__':
    main()
