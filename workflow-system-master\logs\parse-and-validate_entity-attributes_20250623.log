{"timestamp": "2025-06-23T11:28:00.962303", "endpoint": "parse-and-validate/entity-attributes", "input": {"natural_language": "LeaveApplication has leaveId^PK, employeeId^FK, startDate, endDate, numDays[derived], reason, leaveTypeName, leaveSubTypeName, status, requiresDocumentation, documentationProvided, submissionDate, approvalDate, approvedBy^FK, comments, isRetroactive[derived], insufficientBalance[derived], lowTeamAvailability[derived], allowedNumberOfDays.\n\nAttributes:\nAttribute Name | Display Name | Data Type | Required | Unique | Default Type | Default Value | Description | Helper Text\n\nleaveId | Leave ID | string | true | true | computation | LV-{YYYY}-{sequence} | Unique identifier for the leave application | Auto-generated leave identifier\n\nemployeeId | Employee ID | string | true | false | static value | | References the employee requesting leave | Select employee from dropdown\n\nstartDate | Leave Start Date | date | true | false | static value | | First day of the requested leave period | Select start date for leave\n\nendDate | Leave End Date | date | true | false | static value | | Last day of the requested leave period | Select end date for leave\n\nnumDays | Number of Days | integer | true | false | computation | DATEDIFF(endDate, startDate) + 1 | Total number of leave days requested | Automatically calculated based on start and end dates", "entity_context": {"entity_id": "E7", "entity_name": "LeaveApplication", "tenant_id": "T2"}}, "output": {"success": true, "attribute_results": [{"parsed_data": {"attribute_id": "E7.At1", "entity_id": "E7", "name": "leaveId", "display_name": "Leave ID", "datatype": "string", "is_primary_key": true, "is_foreign_key": false, "is_required": true, "is_unique": true, "default_type": "computation", "default_value": "LV-{YYYY}-{sequence}", "description": "Unique identifier for the leave application", "helper_text": "Auto-generated leave identifier", "is_calculated": true, "calculation_formula": "LV-{YYYY}-{sequence}", "version": 1, "status": "draft", "natural_language": "Display Name: Leave ID\nName: leaveId\nData Type: string\nRequired: true\nUnique: true\nCalculated: true\nDefault Type: computation\nDefault Value: LV-{YYYY}-{sequence}\nCalculation Formula: LV-{YYYY}-{sequence}\nDescription: Unique identifier for the leave application\nHelper Text: Auto-generated leave identifier", "created_at": "2025-06-23T11:28:00.415955", "updated_at": "2025-06-23T11:28:00.415955", "created_by": "Tarun", "updated_by": "Tarun", "attribute_status": "existing", "changes_detected": []}, "validation_result": {"structure_errors": [], "required_field_errors": [], "data_type_errors": [], "custom_errors": []}, "dependency_errors": [], "uniqueness_result": {"is_unique": false, "status": "exists_in_postgres", "message": "Attribute with attribute_id E7.At1 already exists in PostgreSQL"}, "is_valid": true}, {"parsed_data": {"attribute_id": "E7.At2", "entity_id": "E7", "name": "employeeId", "display_name": "Employee ID", "datatype": "string", "is_primary_key": false, "is_foreign_key": true, "is_required": true, "is_unique": false, "default_type": "static value", "default_value": "", "description": "References the employee requesting leave", "helper_text": "Select employee from dropdown", "is_calculated": false, "calculation_formula": "", "version": 1, "status": "draft", "natural_language": "Display Name: Employee ID\nName: employeeId\nData Type: string\nRequired: true\nDefault Type: static value\nDescription: References the employee requesting leave\nHelper Text: Select employee from dropdown", "created_at": "2025-06-23T11:28:00.430996", "updated_at": "2025-06-23T11:28:00.430996", "created_by": "Tarun", "updated_by": "Tarun", "attribute_status": "existing", "changes_detected": []}, "validation_result": {"structure_errors": [], "required_field_errors": [], "data_type_errors": [], "custom_errors": []}, "dependency_errors": [], "uniqueness_result": {"is_unique": false, "status": "exists_in_postgres", "message": "Attribute with attribute_id E7.At2 already exists in PostgreSQL"}, "is_valid": true}, {"parsed_data": {"attribute_id": "E7.At3", "entity_id": "E7", "name": "startDate", "display_name": "Leave Start Date", "datatype": "string", "is_primary_key": false, "is_foreign_key": false, "is_required": true, "is_unique": false, "default_type": "static value", "default_value": "", "description": "First day of the requested leave period", "helper_text": "Select start date for leave", "is_calculated": false, "calculation_formula": "", "version": 1, "status": "draft", "natural_language": "Display Name: Leave Start Date\nName: startDate\nData Type: date\nRequired: true\nDefault Type: static value\nDescription: First day of the requested leave period\nHelper Text: Select start date for leave", "created_at": "2025-06-23T11:28:00.443506", "updated_at": "2025-06-23T11:28:00.443506", "created_by": "Tarun", "updated_by": "Tarun", "attribute_status": "existing", "changes_detected": []}, "validation_result": {"structure_errors": [], "required_field_errors": [], "data_type_errors": [], "custom_errors": []}, "dependency_errors": [], "uniqueness_result": {"is_unique": false, "status": "exists_in_postgres", "message": "Attribute with attribute_id E7.At3 already exists in PostgreSQL"}, "is_valid": true}, {"parsed_data": {"attribute_id": "E7.At4", "entity_id": "E7", "name": "endDate", "display_name": "Leave End Date", "datatype": "string", "is_primary_key": false, "is_foreign_key": false, "is_required": true, "is_unique": false, "default_type": "static value", "default_value": "", "description": "Last day of the requested leave period", "helper_text": "Select end date for leave", "is_calculated": false, "calculation_formula": "", "version": 1, "status": "draft", "natural_language": "Display Name: Leave End Date\nName: endDate\nData Type: date\nRequired: true\nDefault Type: static value\nDescription: Last day of the requested leave period\nHelper Text: Select end date for leave", "created_at": "2025-06-23T11:28:00.456657", "updated_at": "2025-06-23T11:28:00.456657", "created_by": "Tarun", "updated_by": "Tarun", "attribute_status": "existing", "changes_detected": []}, "validation_result": {"structure_errors": [], "required_field_errors": [], "data_type_errors": [], "custom_errors": []}, "dependency_errors": [], "uniqueness_result": {"is_unique": false, "status": "exists_in_postgres", "message": "Attribute with attribute_id E7.At4 already exists in PostgreSQL"}, "is_valid": true}, {"parsed_data": {"attribute_id": "E7.At5", "entity_id": "E7", "name": "numDays", "display_name": "Number of Days", "datatype": "string", "is_primary_key": false, "is_foreign_key": false, "is_required": true, "is_unique": false, "default_type": "computation", "default_value": "DATEDIFF(endDate, startDate) + 1", "description": "Total number of leave days requested", "helper_text": "Automatically calculated based on start and end dates", "is_calculated": true, "calculation_formula": "DATEDIFF(endDate, startDate) + 1", "version": 1, "status": "draft", "natural_language": "Display Name: Number of Days\nName: numDays\nData Type: integer\nRequired: true\nCalculated: true\nDefault Type: computation\nDefault Value: DATEDIFF(endDate, startDate) + 1\nCalculation Formula: DATEDIFF(endDate, startDate) + 1\nDescription: Total number of leave days requested\nHelper Text: Automatically calculated based on start and end dates", "created_at": "2025-06-23T11:28:00.469843", "updated_at": "2025-06-23T11:28:00.469843", "created_by": "Tarun", "updated_by": "Tarun", "attribute_status": "existing", "changes_detected": []}, "validation_result": {"structure_errors": [], "required_field_errors": [], "data_type_errors": [], "custom_errors": []}, "dependency_errors": [], "uniqueness_result": {"is_unique": false, "status": "exists_in_postgres", "message": "Attribute with attribute_id E7.At5 already exists in PostgreSQL"}, "is_valid": true}, {"parsed_data": {"attribute_id": "E7.At6", "entity_id": "E7", "name": "reason", "display_name": "reason", "datatype": "string", "is_primary_key": false, "is_foreign_key": false, "is_required": false, "is_unique": false, "default_type": "", "default_value": "", "description": "", "helper_text": "", "is_calculated": false, "calculation_formula": "", "version": 1, "status": "draft", "natural_language": "", "created_at": "2025-06-23T11:28:00.484563", "updated_at": "2025-06-23T11:28:00.484563", "created_by": "Tarun", "updated_by": "Tarun", "attribute_status": "existing", "changes_detected": []}, "validation_result": {"structure_errors": [], "required_field_errors": [], "data_type_errors": [], "custom_errors": []}, "dependency_errors": [], "uniqueness_result": {"is_unique": false, "status": "exists_in_postgres", "message": "Attribute with attribute_id E7.At6 already exists in PostgreSQL"}, "is_valid": true}, {"parsed_data": {"attribute_id": "E7.At7", "entity_id": "E7", "name": "leaveTypeName", "display_name": "leaveTypeName", "datatype": "string", "is_primary_key": false, "is_foreign_key": false, "is_required": false, "is_unique": false, "default_type": "", "default_value": "", "description": "", "helper_text": "", "is_calculated": false, "calculation_formula": "", "version": 1, "status": "draft", "natural_language": "", "created_at": "2025-06-23T11:28:00.496923", "updated_at": "2025-06-23T11:28:00.496923", "created_by": "Tarun", "updated_by": "Tarun", "attribute_status": "existing", "changes_detected": []}, "validation_result": {"structure_errors": [], "required_field_errors": [], "data_type_errors": [], "custom_errors": []}, "dependency_errors": [], "uniqueness_result": {"is_unique": false, "status": "exists_in_postgres", "message": "Attribute with attribute_id E7.At7 already exists in PostgreSQL"}, "is_valid": true}, {"parsed_data": {"attribute_id": "E7.At8", "entity_id": "E7", "name": "leaveSubTypeName", "display_name": "leaveSubTypeName", "datatype": "string", "is_primary_key": false, "is_foreign_key": false, "is_required": false, "is_unique": false, "default_type": "", "default_value": "", "description": "", "helper_text": "", "is_calculated": false, "calculation_formula": "", "version": 1, "status": "draft", "natural_language": "", "created_at": "2025-06-23T11:28:00.510385", "updated_at": "2025-06-23T11:28:00.510385", "created_by": "Tarun", "updated_by": "Tarun", "attribute_status": "existing", "changes_detected": []}, "validation_result": {"structure_errors": [], "required_field_errors": [], "data_type_errors": [], "custom_errors": []}, "dependency_errors": [], "uniqueness_result": {"is_unique": false, "status": "exists_in_postgres", "message": "Attribute with attribute_id E7.At8 already exists in PostgreSQL"}, "is_valid": true}, {"parsed_data": {"attribute_id": "E7.At9", "entity_id": "E7", "name": "status", "display_name": "status", "datatype": "string", "is_primary_key": false, "is_foreign_key": false, "is_required": false, "is_unique": false, "default_type": "", "default_value": "", "description": "", "helper_text": "", "is_calculated": false, "calculation_formula": "", "version": 1, "status": "draft", "natural_language": "", "created_at": "2025-06-23T11:28:00.523469", "updated_at": "2025-06-23T11:28:00.523469", "created_by": "Tarun", "updated_by": "Tarun", "attribute_status": "existing", "changes_detected": []}, "validation_result": {"structure_errors": [], "required_field_errors": [], "data_type_errors": [], "custom_errors": []}, "dependency_errors": [], "uniqueness_result": {"is_unique": false, "status": "exists_in_postgres", "message": "Attribute with attribute_id E7.At9 already exists in PostgreSQL"}, "is_valid": true}, {"parsed_data": {"attribute_id": "E7.At10", "entity_id": "E7", "name": "requiresDocumentation", "display_name": "requiresDocumentation", "datatype": "string", "is_primary_key": false, "is_foreign_key": false, "is_required": false, "is_unique": false, "default_type": "", "default_value": "", "description": "", "helper_text": "", "is_calculated": false, "calculation_formula": "", "version": 1, "status": "draft", "natural_language": "", "created_at": "2025-06-23T11:28:00.534597", "updated_at": "2025-06-23T11:28:00.534597", "created_by": "Tarun", "updated_by": "Tarun", "attribute_status": "existing", "changes_detected": []}, "validation_result": {"structure_errors": [], "required_field_errors": [], "data_type_errors": [], "custom_errors": []}, "dependency_errors": [], "uniqueness_result": {"is_unique": false, "status": "exists_in_postgres", "message": "Attribute with attribute_id E7.At10 already exists in PostgreSQL"}, "is_valid": true}, {"parsed_data": {"attribute_id": "E7.At11", "entity_id": "E7", "name": "documentationProvided", "display_name": "documentationProvided", "datatype": "string", "is_primary_key": false, "is_foreign_key": false, "is_required": false, "is_unique": false, "default_type": "", "default_value": "", "description": "", "helper_text": "", "is_calculated": false, "calculation_formula": "", "version": 1, "status": "draft", "natural_language": "", "created_at": "2025-06-23T11:28:00.545975", "updated_at": "2025-06-23T11:28:00.545975", "created_by": "Tarun", "updated_by": "Tarun", "attribute_status": "existing", "changes_detected": []}, "validation_result": {"structure_errors": [], "required_field_errors": [], "data_type_errors": [], "custom_errors": []}, "dependency_errors": [], "uniqueness_result": {"is_unique": false, "status": "exists_in_postgres", "message": "Attribute with attribute_id E7.At11 already exists in PostgreSQL"}, "is_valid": true}, {"parsed_data": {"attribute_id": "E7.At12", "entity_id": "E7", "name": "submissionDate", "display_name": "submissionDate", "datatype": "string", "is_primary_key": false, "is_foreign_key": false, "is_required": false, "is_unique": false, "default_type": "", "default_value": "", "description": "", "helper_text": "", "is_calculated": false, "calculation_formula": "", "version": 1, "status": "draft", "natural_language": "", "created_at": "2025-06-23T11:28:00.557157", "updated_at": "2025-06-23T11:28:00.557157", "created_by": "Tarun", "updated_by": "Tarun", "attribute_status": "existing", "changes_detected": []}, "validation_result": {"structure_errors": [], "required_field_errors": [], "data_type_errors": [], "custom_errors": []}, "dependency_errors": [], "uniqueness_result": {"is_unique": false, "status": "exists_in_postgres", "message": "Attribute with attribute_id E7.At12 already exists in PostgreSQL"}, "is_valid": true}, {"parsed_data": {"attribute_id": "E7.At13", "entity_id": "E7", "name": "approvalDate", "display_name": "approvalDate", "datatype": "string", "is_primary_key": false, "is_foreign_key": false, "is_required": false, "is_unique": false, "default_type": "", "default_value": "", "description": "", "helper_text": "", "is_calculated": false, "calculation_formula": "", "version": 1, "status": "draft", "natural_language": "", "created_at": "2025-06-23T11:28:00.569094", "updated_at": "2025-06-23T11:28:00.569094", "created_by": "Tarun", "updated_by": "Tarun", "attribute_status": "existing", "changes_detected": []}, "validation_result": {"structure_errors": [], "required_field_errors": [], "data_type_errors": [], "custom_errors": []}, "dependency_errors": [], "uniqueness_result": {"is_unique": false, "status": "exists_in_postgres", "message": "Attribute with attribute_id E7.At13 already exists in PostgreSQL"}, "is_valid": true}, {"parsed_data": {"attribute_id": "E7.At14", "entity_id": "E7", "name": "approvedBy", "display_name": "approvedBy", "datatype": "string", "is_primary_key": false, "is_foreign_key": true, "is_required": false, "is_unique": false, "default_type": "", "default_value": "", "description": "", "helper_text": "", "is_calculated": false, "calculation_formula": "", "version": 1, "status": "draft", "natural_language": "", "created_at": "2025-06-23T11:28:00.580035", "updated_at": "2025-06-23T11:28:00.580035", "created_by": "Tarun", "updated_by": "Tarun", "attribute_status": "existing", "changes_detected": []}, "validation_result": {"structure_errors": [], "required_field_errors": [], "data_type_errors": [], "custom_errors": []}, "dependency_errors": [], "uniqueness_result": {"is_unique": false, "status": "exists_in_postgres", "message": "Attribute with attribute_id E7.At14 already exists in PostgreSQL"}, "is_valid": true}, {"parsed_data": {"attribute_id": "E7.At15", "entity_id": "E7", "name": "comments", "display_name": "comments", "datatype": "string", "is_primary_key": false, "is_foreign_key": false, "is_required": false, "is_unique": false, "default_type": "", "default_value": "", "description": "", "helper_text": "", "is_calculated": false, "calculation_formula": "", "version": 1, "status": "draft", "natural_language": "", "created_at": "2025-06-23T11:28:00.592008", "updated_at": "2025-06-23T11:28:00.592008", "created_by": "Tarun", "updated_by": "Tarun", "attribute_status": "existing", "changes_detected": []}, "validation_result": {"structure_errors": [], "required_field_errors": [], "data_type_errors": [], "custom_errors": []}, "dependency_errors": [], "uniqueness_result": {"is_unique": false, "status": "exists_in_postgres", "message": "Attribute with attribute_id E7.At15 already exists in PostgreSQL"}, "is_valid": true}, {"parsed_data": {"attribute_id": "E7.At16", "entity_id": "E7", "name": "isRetroactive", "display_name": "isRetroactive", "datatype": "string", "is_primary_key": false, "is_foreign_key": false, "is_required": false, "is_unique": false, "default_type": "", "default_value": "", "description": "", "helper_text": "", "is_calculated": true, "calculation_formula": "", "version": 1, "status": "draft", "natural_language": "", "created_at": "2025-06-23T11:28:00.603188", "updated_at": "2025-06-23T11:28:00.603188", "created_by": "Tarun", "updated_by": "Tarun", "attribute_status": "existing", "changes_detected": []}, "validation_result": {"structure_errors": [], "required_field_errors": [], "data_type_errors": [], "custom_errors": []}, "dependency_errors": [], "uniqueness_result": {"is_unique": false, "status": "exists_in_postgres", "message": "Attribute with attribute_id E7.At16 already exists in PostgreSQL"}, "is_valid": true}, {"parsed_data": {"attribute_id": "E7.At17", "entity_id": "E7", "name": "insufficientBalance", "display_name": "insufficientBalance", "datatype": "string", "is_primary_key": false, "is_foreign_key": false, "is_required": false, "is_unique": false, "default_type": "", "default_value": "", "description": "", "helper_text": "", "is_calculated": true, "calculation_formula": "", "version": 1, "status": "draft", "natural_language": "", "created_at": "2025-06-23T11:28:00.614735", "updated_at": "2025-06-23T11:28:00.614735", "created_by": "Tarun", "updated_by": "Tarun", "attribute_status": "existing", "changes_detected": []}, "validation_result": {"structure_errors": [], "required_field_errors": [], "data_type_errors": [], "custom_errors": []}, "dependency_errors": [], "uniqueness_result": {"is_unique": false, "status": "exists_in_postgres", "message": "Attribute with attribute_id E7.At17 already exists in PostgreSQL"}, "is_valid": true}, {"parsed_data": {"attribute_id": "E7.At18", "entity_id": "E7", "name": "lowTeamAvailability", "display_name": "lowTeamAvailability", "datatype": "string", "is_primary_key": false, "is_foreign_key": false, "is_required": false, "is_unique": false, "default_type": "", "default_value": "", "description": "", "helper_text": "", "is_calculated": true, "calculation_formula": "", "version": 1, "status": "draft", "natural_language": "", "created_at": "2025-06-23T11:28:00.628097", "updated_at": "2025-06-23T11:28:00.628097", "created_by": "Tarun", "updated_by": "Tarun", "attribute_status": "existing", "changes_detected": []}, "validation_result": {"structure_errors": [], "required_field_errors": [], "data_type_errors": [], "custom_errors": []}, "dependency_errors": [], "uniqueness_result": {"is_unique": false, "status": "exists_in_postgres", "message": "Attribute with attribute_id E7.At18 already exists in PostgreSQL"}, "is_valid": true}, {"parsed_data": {"attribute_id": "E7.At19", "entity_id": "E7", "name": "allowedNumberOfDays", "display_name": "allowedNumberOfDays", "datatype": "string", "is_primary_key": false, "is_foreign_key": false, "is_required": false, "is_unique": false, "default_type": "", "default_value": "", "description": "", "helper_text": "", "is_calculated": false, "calculation_formula": "", "version": 1, "status": "draft", "natural_language": "", "created_at": "2025-06-23T11:28:00.642184", "updated_at": "2025-06-23T11:28:00.642184", "created_by": "Tarun", "updated_by": "Tarun", "attribute_status": "existing", "changes_detected": []}, "validation_result": {"structure_errors": [], "required_field_errors": [], "data_type_errors": [], "custom_errors": []}, "dependency_errors": [], "uniqueness_result": {"is_unique": false, "status": "exists_in_postgres", "message": "Attribute with attribute_id E7.At19 already exists in PostgreSQL"}, "is_valid": true}], "operation": "parse_and_validate", "total_attributes": 19}, "status": "success"}
{"timestamp": "2025-06-23T14:07:57.962409", "endpoint": "parse-and-validate/entity-attributes", "input": {"natural_language": "Add email field as string and age field as integer", "entity_context": {"entity_id": "user_entity", "entity_name": "User"}}, "output": {"success": true, "attribute_results": [], "operation": "parse_and_validate", "total_attributes": 0}, "status": "success"}
