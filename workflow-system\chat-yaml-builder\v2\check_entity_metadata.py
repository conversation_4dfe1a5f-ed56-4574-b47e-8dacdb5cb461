#!/usr/bin/env python3
"""
Script to check the entity metadata in the database.
"""

import os
import logging
import psycopg2
import json

# Set up logging
logging.basicConfig(level=logging.INFO, 
                   format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
                   handlers=[
                       logging.StreamHandler()
                   ])
logger = logging.getLogger('check_entity_metadata')

def get_db_connection(schema_name: str = None):
    """
    Get a database connection.
    
    Args:
        schema_name: Optional schema name to set as the search path
        
    Returns:
        Database connection object
    """
    # Get database connection parameters from environment variables or use defaults
    db_host = os.environ.get('DB_HOST', '**********')
    db_port = os.environ.get('DB_PORT', '5432')
    db_name = os.environ.get('DB_NAME', 'workflow_system')
    db_user = os.environ.get('DB_USER', 'postgres')
    db_password = os.environ.get('DB_PASSWORD', 'workflow_postgres_secure_password')
    
    # Connect to the database
    conn = psycopg2.connect(
        host=db_host,
        port=db_port,
        dbname=db_name,
        user=db_user,
        password=db_password
    )
    
    # Set schema search path if provided
    if schema_name:
        with conn.cursor() as cursor:
            cursor.execute(f"SET search_path TO {schema_name}")
            conn.commit()
    
    return conn

def check_entity_metadata():
    """
    Check the entity metadata in the database.
    """
    schema_name = 'workflow_temp'
    
    try:
        conn = get_db_connection(schema_name)
        
        with conn.cursor() as cursor:
            # Check if the entities table has a metadata column
            cursor.execute("""
                SELECT column_name, data_type, is_nullable
                FROM information_schema.columns
                WHERE table_schema = %s AND table_name = %s AND column_name = %s
            """, (schema_name, 'entities', 'metadata'))
            
            metadata_column = cursor.fetchone()
            if metadata_column:
                logger.info(f"Metadata column exists in entities table: {metadata_column}")
            else:
                logger.warning("Metadata column does not exist in entities table")
                
                # Add metadata column if it doesn't exist
                logger.info("Adding metadata column to entities table")
                cursor.execute(f"""
                    ALTER TABLE {schema_name}.entities
                    ADD COLUMN IF NOT EXISTS metadata JSONB
                """)
                conn.commit()
                logger.info("Added metadata column to entities table")
            
            # Get entity metadata for Employee
            cursor.execute(f"""
                SELECT entity_id, name, metadata
                FROM {schema_name}.entities
                WHERE name = %s
            """, ('Employee',))
            
            employee_entity = cursor.fetchone()
            if employee_entity:
                entity_id, name, metadata = employee_entity
                logger.info(f"Employee entity: ID={entity_id}, Name={name}, Metadata={metadata}")
                
                # If metadata is None, update it with test metadata
                if metadata is None:
                    test_metadata = {
                        "display_name": "Company Employee",
                        "type": "Core Entity",
                        "description": "Represents an employee within the organization"
                    }
                    
                    cursor.execute(f"""
                        UPDATE {schema_name}.entities
                        SET metadata = %s
                        WHERE entity_id = %s
                    """, (json.dumps(test_metadata), entity_id))
                    conn.commit()
                    logger.info(f"Updated Employee entity metadata: {test_metadata}")
            else:
                logger.warning("Employee entity not found")
            
            # Get attribute metadata for email attribute
            cursor.execute(f"""
                SELECT *
                FROM {schema_name}.entity_attribute_metadata
                WHERE entity_id = (SELECT entity_id FROM {schema_name}.entities WHERE name = %s)
                AND attribute_name = %s
            """, ('Employee', 'email'))
            
            email_metadata = cursor.fetchone()
            if email_metadata:
                logger.info(f"Email attribute metadata: {email_metadata}")
            else:
                logger.warning("Email attribute metadata not found")
    except Exception as e:
        logger.error(f"Error checking entity metadata: {str(e)}")
    finally:
        if conn:
            conn.close()

if __name__ == "__main__":
    check_entity_metadata()
