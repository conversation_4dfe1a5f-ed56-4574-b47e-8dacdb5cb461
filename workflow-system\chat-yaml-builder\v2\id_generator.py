"""
ID Generator for YAML Builder v2

This module provides functionality for generating and validating IDs for components
(roles, entities, GO definitions, LO definitions) in the YAML Builder v2.
"""

import os
import sys
import re
import logging
from typing import Dict, List, Tuple, Any, Optional

# Add parent directory to path to import common modules
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
from db_utils import execute_query

# Set up logging
logger = logging.getLogger('id_generator')

class IDGenerator:
    """
    Generates and validates IDs for components.
    """
    
    def __init__(self, schema_name: str):
        """
        Initialize the ID generator.
        
        Args:
            schema_name: Schema name to use for ID validation
        """
        self.schema_name = schema_name
        self.id_patterns = {
            'role': r'^role_[a-z0-9_]+$',
            'entity': r'^entity_[a-z0-9_]+$',
            'go': r'^go_[a-z0-9_]+$',
            'lo': r'^lo_[a-z0-9_]+$',
            'attribute': r'^entity_[a-z0-9_]+_attr_[a-z0-9_]+$',
            'relationship': r'^entity_[a-z0-9_]+_rel_[a-z0-9_]+$',
            'business_rule': r'^entity_[a-z0-9_]+_rule_[a-z0-9_]+$',
            'mapping': r'^go_[a-z0-9_]+_lo_[a-z0-9_]+$',
            'metric': r'^go_[a-z0-9_]+_metric_[a-z0-9_]+$',
            'input_item': r'^lo_[a-z0-9_]+_input_[a-z0-9_]+$',
            'output_item': r'^lo_[a-z0-9_]+_output_[a-z0-9_]+$'
        }
    
    def generate_id(self, id_type: str, name: str, parent_id: str = None) -> str:
        """
        Generate an ID for a component.
        
        Args:
            id_type: Type of ID to generate ('role', 'entity', 'go', 'lo', 'attribute', etc.)
            name: Name of the component
            parent_id: ID of the parent component (for nested components)
            
        Returns:
            Generated ID
        """
        # Convert name to lowercase and replace spaces with underscores
        name_slug = name.lower().replace(' ', '_')
        
        # Remove any special characters
        name_slug = re.sub(r'[^a-z0-9_]', '', name_slug)
        
        # Generate ID based on type
        if id_type == 'role':
            return f"role_{name_slug}"
        elif id_type == 'entity':
            return f"entity_{name_slug}"
        elif id_type == 'go':
            return f"go_{name_slug}"
        elif id_type == 'lo':
            return f"lo_{name_slug}"
        elif id_type == 'attribute' and parent_id:
            return f"{parent_id}_attr_{name_slug}"
        elif id_type == 'relationship' and parent_id:
            return f"{parent_id}_rel_{name_slug}"
        elif id_type == 'business_rule' and parent_id:
            return f"{parent_id}_rule_{name_slug}"
        elif id_type == 'mapping' and parent_id:
            return f"{parent_id}_lo_{name_slug}"
        elif id_type == 'metric' and parent_id:
            return f"{parent_id}_metric_{name_slug}"
        elif id_type == 'input_item' and parent_id:
            return f"{parent_id}_input_{name_slug}"
        elif id_type == 'output_item' and parent_id:
            return f"{parent_id}_output_{name_slug}"
        else:
            raise ValueError(f"Invalid ID type: {id_type}")
    
    def validate_id(self, id_type: str, id_value: str) -> bool:
        """
        Validate an ID.
        
        Args:
            id_type: Type of ID to validate ('role', 'entity', 'go', 'lo', 'attribute', etc.)
            id_value: ID to validate
            
        Returns:
            Boolean indicating if ID is valid
        """
        # Check if ID matches pattern
        if id_type not in self.id_patterns:
            logger.error(f"Invalid ID type: {id_type}")
            return False
        
        pattern = self.id_patterns[id_type]
        if not re.match(pattern, id_value):
            logger.error(f"ID '{id_value}' does not match pattern '{pattern}'")
            return False
        
        return True
    
    def check_id_exists(self, id_type: str, id_value: str) -> bool:
        """
        Check if an ID exists in the database.
        
        Args:
            id_type: Type of ID to check ('role', 'entity', 'go', 'lo', 'attribute', etc.)
            id_value: ID to check
            
        Returns:
            Boolean indicating if ID exists
        """
        # Determine table and column based on ID type
        table_column = self._get_table_column_for_id_type(id_type)
        if not table_column:
            logger.error(f"Invalid ID type: {id_type}")
            return False
        
        table, column = table_column
        
        # Check if ID exists
        success, _, result = execute_query(
            f"SELECT {column} FROM {self.schema_name}.{table} WHERE {column} = %s",
            (id_value,),
            self.schema_name
        )
        
        if not success:
            logger.error(f"Error checking if ID '{id_value}' exists")
            return False
        
        return result and len(result) > 0
    
    def get_next_id(self, id_type: str, base_id: str) -> str:
        """
        Get the next available ID for a component.
        
        Args:
            id_type: Type of ID to generate ('role', 'entity', 'go', 'lo', 'attribute', etc.)
            base_id: Base ID to increment
            
        Returns:
            Next available ID
        """
        # Check if base ID exists
        if not self.check_id_exists(id_type, base_id):
            return base_id
        
        # Increment ID
        i = 1
        while True:
            next_id = f"{base_id}_{i}"
            if not self.check_id_exists(id_type, next_id):
                return next_id
            i += 1
    
    def _get_table_column_for_id_type(self, id_type: str) -> Optional[Tuple[str, str]]:
        """
        Get the table and column for an ID type.
        
        Args:
            id_type: Type of ID ('role', 'entity', 'go', 'lo', 'attribute', etc.)
            
        Returns:
            Tuple containing table and column, or None if ID type is invalid
        """
        if id_type == 'role':
            return ('roles', 'role_id')
        elif id_type == 'entity':
            return ('entities', 'entity_id')
        elif id_type == 'go':
            return ('global_objectives', 'go_id')
        elif id_type == 'lo':
            return ('local_objectives', 'lo_id')
        elif id_type == 'attribute':
            return ('entity_attributes', 'attribute_id')
        elif id_type == 'relationship':
            return ('entity_relationships', 'relationship_id')
        elif id_type == 'business_rule':
            return ('entity_business_rules', 'rule_id')
        elif id_type == 'mapping':
            return ('go_lo_mapping', 'mapping_id')
        elif id_type == 'metric':
            return ('go_performance_metrics', 'metric_id')
        elif id_type == 'input_item':
            return ('lo_input_items', 'item_id')
        elif id_type == 'output_item':
            return ('lo_output_items', 'item_id')
        else:
            return None
