import unittest
from unittest.mock import patch
from typing import List, Union
import sys
import os

# Add project root to path if needed
project_root = '/home/<USER>/workflow-system/runtime/workflow-engine'
if project_root not in sys.path:
    sys.path.insert(0, project_root)

# Import the function to test
from app.services.system_functions import average

class TestAverage(unittest.TestCase):
    @patch('app.services.system_functions.round_number')
    def test_average_integers(self, mock_round_number):
        """Test average calculation with integers."""
        # Setup mock
        mock_round_number.return_value = 5.0
        
        # Test data
        values = [3, 5, 7]
        
        # Execute
        result = average(values)
        
        # Verify
        self.assertEqual(result, 5.0)
        mock_round_number.assert_called_once_with(5.0, 2)
    
    @patch('app.services.system_functions.round_number')
    def test_average_floats(self, mock_round_number):
        """Test average calculation with floats."""
        # Setup mock
        mock_round_number.return_value = 5.5
        
        # Test data
        values = [3.5, 5.5, 7.5]
        
        # Execute
        result = average(values)
        
        # Verify
        self.assertEqual(result, 5.5)
        mock_round_number.assert_called_once_with(5.5, 2)
    
    @patch('app.services.system_functions.round_number')
    def test_average_mixed_types(self, mock_round_number):
        """Test average calculation with mixed types (integers and floats)."""
        # Setup mock
        mock_round_number.return_value = 5.0
        
        # Test data
        values = [3, 5.0, 7]
        
        # Execute
        result = average(values)
        
        # Verify
        self.assertEqual(result, 5.0)
        mock_round_number.assert_called_once_with(5.0, 2)
    
    @patch('app.services.system_functions.round_number')
    def test_average_string_values(self, mock_round_number):
        """Test average calculation with string representations of numbers."""
        # Setup mock
        mock_round_number.return_value = 5.0
        
        # Test data
        values = ["3", "5", "7"]
        
        # Execute
        result = average(values)
        
        # Verify
        self.assertEqual(result, 5.0)
        mock_round_number.assert_called_once_with(5.0, 2)
    
    @patch('app.services.system_functions.round_number')
    def test_average_mixed_strings_and_numbers(self, mock_round_number):
        """Test average calculation with both numbers and string representations."""
        # Setup mock
        mock_round_number.return_value = 5.0
        
        # Test data
        values = [3, "5", 7]
        
        # Execute
        result = average(values)
        
        # Verify
        self.assertEqual(result, 5.0)
        mock_round_number.assert_called_once_with(5.0, 2)
    
    @patch('app.services.system_functions.round_number')
    def test_custom_decimal_places(self, mock_round_number):
        """Test average calculation with custom decimal places."""
        # Setup mock
        mock_round_number.return_value = 5.333
        
        # Test data
        values = [3, 5, 8]
        decimal_places = 3
        
        # Execute
        result = average(values, decimal_places)
        
        # Verify
        self.assertEqual(result, 5.333)
        mock_round_number.assert_called_once_with(5.333333333333333, 3)
    
    @patch('app.services.system_functions.round_number')
    def test_single_value(self, mock_round_number):
        """Test average calculation with a single value."""
        # Setup mock
        mock_round_number.return_value = 42.0
        
        # Test data
        values = [42]
        
        # Execute
        result = average(values)
        
        # Verify
        self.assertEqual(result, 42.0)
        mock_round_number.assert_called_once_with(42.0, 2)
    
    def test_empty_list(self):
        """Test average calculation with an empty list (should raise ValueError)."""
        # Test data
        values = []
        
        # Verify exception
        with self.assertRaises(ValueError) as context:
            average(values)
        
        self.assertEqual(str(context.exception), "Cannot calculate average of empty list")
    
    def test_non_numeric_string(self):
        """Test with a non-numeric string (should raise ValueError)."""
        # Test data
        values = [3, 5, "seven"]
        
        # Verify exception
        with self.assertRaises(ValueError) as context:
            average(values)
        
        self.assertEqual(str(context.exception), "Non-numeric value in list: seven")
    
    @patch('app.services.system_functions.round_number')
    def test_negative_numbers(self, mock_round_number):
        """Test average calculation with negative numbers."""
        # Setup mock
        mock_round_number.return_value = -5.0
        
        # Test data
        values = [-3, -5, -7]
        
        # Execute
        result = average(values)
        
        # Verify
        self.assertEqual(result, -5.0)
        mock_round_number.assert_called_once_with(-5.0, 2)
    
    @patch('app.services.system_functions.round_number')
    def test_average_with_zero(self, mock_round_number):
        """Test average calculation with zero values."""
        # Setup mock
        mock_round_number.return_value = 0.0
        
        # Test data
        values = [0, 0, 0]
        
        # Execute
        result = average(values)
        
        # Verify
        self.assertEqual(result, 0.0)
        mock_round_number.assert_called_once_with(0.0, 2)
    
    def test_integration_without_mocking(self):
        """Test actual integration with round_number without mocking."""
        # Test data - simple values that should give exact results
        values = [10, 20, 30]
        
        # Execute
        result = average(values)
        
        # Verify - assuming round_number works correctly
        self.assertEqual(result, 20.0)
        
        # Test with decimal places
        values = [1, 2, 3]
        result = average(values, 3)
        
        # Verify
        self.assertEqual(result, 2.0)
        
        # Test with values requiring rounding
        values = [1, 2, 4]
        result = average(values, 2)
        
        # Verify
        self.assertEqual(result, 2.33)

if __name__ == '__main__':
    unittest.main()