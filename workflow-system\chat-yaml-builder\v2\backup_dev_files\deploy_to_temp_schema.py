#!/usr/bin/env python3
"""
Deploy sample components to a temporary schema without rolling back.

This script deploys sample components to a temporary schema and does not roll back
the schema at the end, allowing you to inspect the schema and data.
"""

import os
import sys
import logging
import argparse
import yaml
from typing import Dict, List, Tuple, Any, Optional

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('deploy_to_temp_schema')

# Import required modules
from prescriptive_parser import PrescriptiveParser
from component_deployer import ComponentDeployer
from db_utils import execute_query

def read_sample_file(file_path: str) -> str:
    """
    Read a sample file.
    
    Args:
        file_path: Path to the sample file
        
    Returns:
        The contents of the file as a string
    """
    try:
        with open(file_path, 'r') as f:
            return f.read()
    except Exception as e:
        logger.error(f"Error reading file {file_path}: {str(e)}")
        return ""

def deploy_component(deployer: ComponentDeployer, component_type: str, component_data: Dict) -> Tuple[bool, List[str]]:
    """
    Deploy a component to the database.
    
    Args:
        deployer: The component deployer
        component_type: The type of component to deploy
        component_data: The component data to deploy
        
    Returns:
        Tuple containing:
            - Boolean indicating if deployment was successful
            - List of messages
    """
    logger.info(f"Deploying {component_type}")
    
    # Convert component data to YAML
    component_yaml = yaml.dump(component_data)
    
    # Deploy the component
    success, messages = deployer.deploy_component(component_type, component_yaml)
    
    if not success:
        logger.error(f"Failed to deploy {component_type}: {messages}")
    else:
        logger.info(f"Successfully deployed {component_type}")
    
    return success, messages

def query_schema(schema_name: str) -> None:
    """
    Query the schema to see what tables and data exist.
    
    Args:
        schema_name: The name of the schema to query
    """
    logger.info(f"Querying schema {schema_name}")
    
    # Get list of tables
    success, query_messages, result = execute_query(
        f"""
        SELECT table_name
        FROM information_schema.tables
        WHERE table_schema = %s
        AND table_type = 'BASE TABLE'
        ORDER BY table_name
        """,
        (schema_name,)
    )
    
    if not success:
        logger.error(f"Failed to get tables: {query_messages}")
        return
    
    if not result:
        logger.info(f"No tables found in schema {schema_name}")
        return
    
    tables = [row[0] for row in result]
    logger.info(f"Found {len(tables)} tables in schema {schema_name}")
    
    # Query each table
    for table in tables:
        success, query_messages, result = execute_query(
            f"""
            SELECT *
            FROM {schema_name}.{table}
            LIMIT 10
            """
        )
        
        if not success:
            logger.error(f"Failed to query table {table}: {query_messages}")
            continue
        
        if not result:
            logger.info(f"No data found in table {schema_name}.{table}")
            continue
        
        logger.info(f"Data in table {schema_name}.{table}:")
        
        # Get column names
        success, query_messages, columns = execute_query(
            f"""
            SELECT column_name
            FROM information_schema.columns
            WHERE table_schema = %s
            AND table_name = %s
            ORDER BY ordinal_position
            """,
            (schema_name, table)
        )
        
        if not success:
            logger.error(f"Failed to get columns for table {table}: {query_messages}")
            continue
        
        column_names = [col[0] for col in columns]
        
        # Print column names
        print("\t".join(column_names))
        
        # Print data
        for row in result:
            print("\t".join(str(val) for val in row))
        
        print()

def main():
    """Main function."""
    parser = argparse.ArgumentParser(description='Deploy sample components to a temporary schema without rolling back')
    parser.add_argument('--schema-name', default='workflow_temp', help='Name of the temporary schema')
    parser.add_argument('--query-only', action='store_true', help='Only query the schema, do not deploy components')
    args = parser.parse_args()
    
    # Initialize parser and deployer
    prescriptive_parser = PrescriptiveParser()
    component_deployer = ComponentDeployer(use_temp_schema=True)
    component_deployer.temp_schema_name = args.schema_name
    
    if not args.query_only:
        logger.info(f"Deploying sample components to schema {args.schema_name}")
        
        # Deploy entities
        entity_sample_path = os.path.join('samples', 'sample_entity_output.txt')
        entity_sample_text = read_sample_file(entity_sample_path)
        
        if entity_sample_text:
            entity_data, entity_warnings = prescriptive_parser.parse('entities', entity_sample_text)
            
            if entity_warnings:
                logger.warning(f"Entity parser warnings: {entity_warnings}")
            
            if entity_data:
                deploy_success, deploy_messages = deploy_component(component_deployer, 'entities', entity_data)
                
                if not deploy_success:
                    logger.error(f"Failed to deploy entities: {deploy_messages}")
        
        # Deploy GO definitions
        go_sample_path = os.path.join('samples', 'sample_go_output.txt')
        go_sample_text = read_sample_file(go_sample_path)
        
        if go_sample_text:
            go_data, go_warnings = prescriptive_parser.parse('go_definitions', go_sample_text)
            
            if go_warnings:
                logger.warning(f"GO parser warnings: {go_warnings}")
            
            if go_data:
                deploy_success, deploy_messages = deploy_component(component_deployer, 'go_definitions', go_data)
                
                if not deploy_success:
                    logger.error(f"Failed to deploy GO definitions: {deploy_messages}")
        
        # Deploy LO definitions
        lo_sample_path = os.path.join('samples', 'sample_lo_output.txt')
        lo_sample_text = read_sample_file(lo_sample_path)
        
        if lo_sample_text:
            lo_data, lo_warnings = prescriptive_parser.parse('lo_definitions', lo_sample_text)
            
            if lo_warnings:
                logger.warning(f"LO parser warnings: {lo_warnings}")
            
            if lo_data:
                deploy_success, deploy_messages = deploy_component(component_deployer, 'lo_definitions', lo_data)
                
                if not deploy_success:
                    logger.error(f"Failed to deploy LO definitions: {deploy_messages}")
        
        # Deploy roles
        role_sample_path = os.path.join('samples', 'sample_role_output.txt')
        role_sample_text = read_sample_file(role_sample_path)
        
        if role_sample_text:
            role_data, role_warnings = prescriptive_parser.parse('roles', role_sample_text)
            
            if role_warnings:
                logger.warning(f"Role parser warnings: {role_warnings}")
            
            if role_data:
                deploy_success, deploy_messages = deploy_component(component_deployer, 'roles', role_data)
                
                if not deploy_success:
                    logger.error(f"Failed to deploy roles: {deploy_messages}")
    
    # Query the schema
    query_schema(args.schema_name)
    
    logger.info(f"Components deployed to schema {args.schema_name}")
    logger.info(f"You can now connect to the database and query the schema {args.schema_name}")
    logger.info(f"To create a migration script, you can use the schema_analyzer.py script to compare the schemas")

if __name__ == '__main__':
    main()
