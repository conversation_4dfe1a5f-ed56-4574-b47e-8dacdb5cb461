{"timestamp": "2025-06-24T04:45:02.387145", "operation": "deploy_single_system_permission_to_workflow_temp", "input_data": {"permission_id": "PERM001"}, "result": {"success": false, "error": "System permission PERM001 not found with status draft", "permission_id": "PERM001", "required_status": "draft"}, "status": "error"}
{"timestamp": "2025-06-24T04:45:02.513198", "operation": "insert_system_permission_to_workflow_temp", "input_data": {"_id": "6858ff1e909a273e7e5d8167", "permission_id": "PERM1", "permission_name": "Employee totalLeaveEntitlement Attribute", "permission_type": "attribute", "resource_identifier": "Employee.totalLeaveEntitlement", "actions": ["read", "update"], "scope": "department_records", "entity_id": "E13", "attribute_id": "E13.At11", "go_id": "", "lo_id": "", "tenant_id": "T1001", "description": "Access to Employee totalLeaveEntitlement attribute", "natural_language": "Permission to access total leave entitlement attribute", "version": 1, "status": "draft", "created_at": "2025-06-23T07:15:42.617971", "updated_at": "2025-06-23T07:15:42.617979", "created_by": "Tarun", "updated_by": "Tarun", "permission_status": "new", "changes_detected": []}, "result": {"success": false, "error": "Error inserting system permission to workflow_temp: column \"actions\" is of type jsonb but expression is of type text[]\nLINE 6: ...', 'attribute', 'Employee.totalLeaveEntitlement', ARRAY['rea...\n                                                             ^\nHINT:  You will need to rewrite or cast the expression.\n", "schema": "workflow_temp", "permission_id": "PERM1", "permission_name": "Employee totalLeaveEntitlement Attribute"}, "status": "error"}
{"timestamp": "2025-06-24T04:45:02.513559", "operation": "process_mongo_system_permissions_to_workflow_temp", "input_data": {"schema": "workflow_temp"}, "result": {"total_processed": 1, "successful_inserts": 0, "failed_inserts": 1, "details": [{"permission_id": "PERM1", "permission_name": "Employee totalLeaveEntitlement Attribute", "status": "failed", "details": {"success": false, "error": "Error inserting system permission to workflow_temp: column \"actions\" is of type jsonb but expression is of type text[]\nLINE 6: ...', 'attribute', 'Employee.totalLeaveEntitlement', ARRAY['rea...\n                                                             ^\nHINT:  You will need to rewrite or cast the expression.\n", "schema": "workflow_temp", "permission_id": "PERM1", "permission_name": "Employee totalLeaveEntitlement Attribute"}}]}, "status": "success"}
{"timestamp": "2025-06-24T04:45:04.150481", "operation": "insert_system_permission_to_workflow_temp", "input_data": {"_id": "6858ff1e909a273e7e5d8167", "permission_id": "PERM1", "permission_name": "Employee totalLeaveEntitlement Attribute", "permission_type": "attribute", "resource_identifier": "Employee.totalLeaveEntitlement", "actions": ["read", "update"], "scope": "department_records", "entity_id": "E13", "attribute_id": "E13.At11", "go_id": "", "lo_id": "", "tenant_id": "T1001", "description": "Access to Employee totalLeaveEntitlement attribute", "natural_language": "Permission to access total leave entitlement attribute", "version": 1, "status": "draft", "created_at": "2025-06-23T07:15:42.617971", "updated_at": "2025-06-23T07:15:42.617979", "created_by": "Tarun", "updated_by": "Tarun", "permission_status": "new", "changes_detected": []}, "result": {"success": false, "error": "Error inserting system permission to workflow_temp: column \"actions\" is of type jsonb but expression is of type text[]\nLINE 6: ...', 'attribute', 'Employee.totalLeaveEntitlement', ARRAY['rea...\n                                                             ^\nHINT:  You will need to rewrite or cast the expression.\n", "schema": "workflow_temp", "permission_id": "PERM1", "permission_name": "Employee totalLeaveEntitlement Attribute"}, "status": "error"}
{"timestamp": "2025-06-24T04:45:04.150833", "operation": "process_mongo_system_permissions_to_workflow_temp", "input_data": {"schema": "workflow_temp"}, "result": {"total_processed": 1, "successful_inserts": 0, "failed_inserts": 1, "details": [{"permission_id": "PERM1", "permission_name": "Employee totalLeaveEntitlement Attribute", "status": "failed", "details": {"success": false, "error": "Error inserting system permission to workflow_temp: column \"actions\" is of type jsonb but expression is of type text[]\nLINE 6: ...', 'attribute', 'Employee.totalLeaveEntitlement', ARRAY['rea...\n                                                             ^\nHINT:  You will need to rewrite or cast the expression.\n", "schema": "workflow_temp", "permission_id": "PERM1", "permission_name": "Employee totalLeaveEntitlement Attribute"}}]}, "status": "success"}
{"timestamp": "2025-06-24T11:45:42.334538", "operation": "insert_system_permission_to_workflow_runtime", "input_data": {"_id": "6858ff1e909a273e7e5d8167", "permission_id": "PERM1", "permission_name": "Employee totalLeaveEntitlement Attribute", "permission_type": "attribute", "resource_identifier": "Employee.totalLeaveEntitlement", "actions": ["read", "update"], "scope": "department_records", "entity_id": "E13", "attribute_id": "E13.At11", "go_id": "", "lo_id": "", "tenant_id": "T1001", "description": "Access to Employee totalLeaveEntitlement attribute", "natural_language": "Permission to access total leave entitlement attribute", "version": 1, "status": "draft", "created_at": "2025-06-23T07:15:42.617971", "updated_at": "2025-06-23T07:15:42.617979", "created_by": "Tarun", "updated_by": "Tarun", "permission_status": "new", "changes_detected": []}, "result": {"success": false, "error": "Error inserting system permission to workflow_runtime: column \"actions\" is of type jsonb but expression is of type text[]\nLINE 6: ...', 'attribute', 'Employee.totalLeaveEntitlement', ARRAY['rea...\n                                                             ^\nHINT:  You will need to rewrite or cast the expression.\n", "schema": "workflow_runtime", "permission_id": "PERM1", "permission_name": "Employee totalLeaveEntitlement Attribute"}, "status": "error"}
{"timestamp": "2025-06-24T11:45:42.334950", "operation": "process_mongo_system_permissions_to_workflow_runtime", "input_data": {"schema": "workflow_runtime"}, "result": {"total_processed": 1, "successful_inserts": 0, "failed_inserts": 1, "details": [{"permission_id": "PERM1", "permission_name": "Employee totalLeaveEntitlement Attribute", "status": "failed", "details": {"success": false, "error": "Error inserting system permission to workflow_runtime: column \"actions\" is of type jsonb but expression is of type text[]\nLINE 6: ...', 'attribute', 'Employee.totalLeaveEntitlement', ARRAY['rea...\n                                                             ^\nHINT:  You will need to rewrite or cast the expression.\n", "schema": "workflow_runtime", "permission_id": "PERM1", "permission_name": "Employee totalLeaveEntitlement Attribute"}}]}, "status": "success"}
{"timestamp": "2025-06-24T11:50:39.056998", "operation": "insert_system_permission_to_workflow_runtime", "input_data": {"_id": "6858ff1e909a273e7e5d8167", "permission_id": "PERM1", "permission_name": "Employee totalLeaveEntitlement Attribute", "permission_type": "attribute", "resource_identifier": "Employee.totalLeaveEntitlement", "actions": ["read", "update"], "scope": "department_records", "entity_id": "E13", "attribute_id": "E13.At11", "go_id": "", "lo_id": "", "tenant_id": "T1001", "description": "Access to Employee totalLeaveEntitlement attribute", "natural_language": "Permission to access total leave entitlement attribute", "version": 1, "status": "draft", "created_at": "2025-06-23T07:15:42.617971", "updated_at": "2025-06-23T07:15:42.617979", "created_by": "Tarun", "updated_by": "Tarun", "permission_status": "new", "changes_detected": []}, "result": {"success": false, "error": "Error inserting system permission to workflow_runtime: column \"actions\" is of type jsonb but expression is of type text[]\nLINE 6: ...', 'attribute', 'Employee.totalLeaveEntitlement', ARRAY['rea...\n                                                             ^\nHINT:  You will need to rewrite or cast the expression.\n", "schema": "workflow_runtime", "permission_id": "PERM1", "permission_name": "Employee totalLeaveEntitlement Attribute"}, "status": "error"}
{"timestamp": "2025-06-24T11:50:39.057405", "operation": "process_mongo_system_permissions_to_workflow_runtime", "input_data": {"schema": "workflow_runtime"}, "result": {"total_processed": 1, "successful_inserts": 0, "failed_inserts": 1, "details": [{"permission_id": "PERM1", "permission_name": "Employee totalLeaveEntitlement Attribute", "status": "failed", "details": {"success": false, "error": "Error inserting system permission to workflow_runtime: column \"actions\" is of type jsonb but expression is of type text[]\nLINE 6: ...', 'attribute', 'Employee.totalLeaveEntitlement', ARRAY['rea...\n                                                             ^\nHINT:  You will need to rewrite or cast the expression.\n", "schema": "workflow_runtime", "permission_id": "PERM1", "permission_name": "Employee totalLeaveEntitlement Attribute"}}]}, "status": "success"}
{"timestamp": "2025-06-24T12:04:39.214176", "operation": "insert_system_permission_to_workflow_runtime", "input_data": {"_id": "6858ff1e909a273e7e5d8167", "permission_id": "PERM1", "permission_name": "Employee totalLeaveEntitlement Attribute", "permission_type": "attribute", "resource_identifier": "Employee.totalLeaveEntitlement", "actions": ["read", "update"], "scope": "department_records", "entity_id": "E13", "attribute_id": "E13.At11", "go_id": "", "lo_id": "", "tenant_id": "T1001", "description": "Access to Employee totalLeaveEntitlement attribute", "natural_language": "Permission to access total leave entitlement attribute", "version": 1, "status": "draft", "created_at": "2025-06-23T07:15:42.617971", "updated_at": "2025-06-23T07:15:42.617979", "created_by": "Tarun", "updated_by": "Tarun", "permission_status": "new", "changes_detected": []}, "result": {"success": true, "inserted_id": 80, "schema": "workflow_runtime", "permission_id": "PERM1", "permission_name": "Employee totalLeaveEntitlement Attribute", "original_permission_id": "PERM_ATTR_EMP_LEAVE"}, "status": "success"}
{"timestamp": "2025-06-24T12:04:39.217092", "operation": "process_mongo_system_permissions_to_workflow_runtime", "input_data": {"schema": "workflow_runtime"}, "result": {"total_processed": 1, "successful_inserts": 1, "failed_inserts": 0, "details": [{"permission_id": "PERM1", "permission_name": "Employee totalLeaveEntitlement Attribute", "status": "success", "details": {"success": true, "inserted_id": 80, "schema": "workflow_runtime", "permission_id": "PERM1", "permission_name": "Employee totalLeaveEntitlement Attribute", "original_permission_id": "PERM_ATTR_EMP_LEAVE"}}]}, "status": "success"}
