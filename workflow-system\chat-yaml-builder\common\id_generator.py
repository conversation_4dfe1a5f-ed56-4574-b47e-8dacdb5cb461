import psycopg2
import yaml
import re
from collections import defaultdict

def get_connection():
    return psycopg2.connect(
        dbname="workflow_system",
        user="postgres",
        password="workflow_postgres_secure_password",
        host="**********",  # Updated to match insert_generator.py
        port="5432"
    )

ID_CONFIG = {
    "go": ("global_objectives", "go_id"),
    "lo": ("local_objectives", "lo_id"),
    "e": ("entities", "entity_id"),
    "at": ("entity_attributes", "attribute_id"),
    "is": ("input_stack", "id"),
    "os": ("output_stack", "id"),
    "in": ("input_items", "id"),
    "out": ("output_items", "id"),
    "dm": ("data_mappings", "id"),
    "dms": ("data_mapping_stack", "id"),
    "nf": ("lo_nested_functions", "id"),
    "vr": ("lo_input_validations", "id"),
    "tr": ("output_triggers", "id"),
    "uis": ("ui_stack", "id"),
    "uie": ("ui_elements", "id")
}

def fetch_current_max_ids(conn):
    max_ids = {}
    cursor = conn.cursor()
    for prefix, (table, column) in ID_CONFIG.items():
        try:
            cursor.execute(f"""
                SELECT MAX(CAST({column} AS TEXT)) FROM workflow_runtime.{table}
                WHERE CAST({column} AS TEXT) ~ %s
            """, [f"^{prefix}[0-9]+$"])

            result = cursor.fetchone()[0]
            max_ids[prefix] = int(re.findall(r'\d+', result)[0]) if result else 0
        except Exception as e:
            print(f"⚠️ Skipping {table}.{column}: {e}")
            max_ids[prefix] = 0
            conn.rollback()  # Prevents "current transaction is aborted" for next queries
    cursor.close()
    return max_ids

def fetch_existing_entities_and_attributes(conn):
    entity_map = defaultdict(dict)
    cursor = conn.cursor()
    cursor.execute("SELECT entity_id, name FROM workflow_runtime.entities")
    for eid, name in cursor.fetchall():
        entity_map[name.lower()] = {"entity_id": eid, "attributes": {}}

    cursor.execute("SELECT entity_id, attribute_id, name FROM workflow_runtime.entity_attributes")
    for eid, aid, name in cursor.fetchall():
        for entity in entity_map.values():
            if entity["entity_id"] == eid:
                entity["attributes"][name.lower()] = aid
    cursor.close()
    return entity_map

def get_next_id(prefix, counters):
    counters[prefix] += 1
    return f"{prefix}{str(counters[prefix]).zfill(3)}"

def enrich_yaml_ids(obj, counters, seen_ids, entity_map):
    if isinstance(obj, dict):
        if 'id' in obj and 'name' in obj and 'attributes' in obj and 'type' in obj:
            # Handle Entity
            ename = obj['name'].lower()
            if ename in entity_map:
                seen_ids[obj['id']] = entity_map[ename]['entity_id']
                obj['id'] = seen_ids[obj['id']]
                for attr in obj['attributes']:
                    aname = attr['name'].lower()
                    if aname in entity_map[ename]['attributes']:
                        seen_ids[attr['id']] = entity_map[ename]['attributes'][aname]
                        attr['id'] = seen_ids[attr['id']]
                    else:
                        new_attr_id = get_next_id("at", counters)
                        seen_ids[attr['id']] = new_attr_id
                        attr['id'] = new_attr_id
            else:
                new_eid = get_next_id("e", counters)
                seen_ids[obj['id']] = new_eid
                obj['id'] = new_eid
                for attr in obj['attributes']:
                    new_attr_id = get_next_id("at", counters)
                    seen_ids[attr['id']] = new_attr_id
                    attr['id'] = new_attr_id
                
            # Update required_attributes if present
            if 'attributes_metadata' in obj and 'required_attributes' in obj['attributes_metadata']:
                old_required = obj['attributes_metadata']['required_attributes']
                new_required = []
                
                for old_id in old_required:
                    if old_id in seen_ids:
                        new_id = seen_ids[old_id]
                        new_required.append(new_id)
                    else:
                        # Keep the old ID if we can't find a new one
                        new_required.append(old_id)
                
                obj['attributes_metadata']['required_attributes'] = new_required

        elif 'id' in obj and 'name' in obj:
            # Handle Global and Local Objectives
            oid = obj['id'].lower()
            if oid.startswith("go") or oid.startswith("lo"):
                prefix = oid[:2]
                new_oid = get_next_id(prefix, counters)
                seen_ids[oid] = new_oid
                obj['id'] = new_oid

                # Also update contextual_id if it's a Local Objective
                if prefix == "lo":
                    # Try finding GO ID from the current scope
                    parent_go = obj.get("go_id") or find_parent_go_id(obj)
                    if parent_go:
                        go_enriched = seen_ids.get(parent_go.lower(), parent_go.lower())
                        obj['contextual_id'] = f"{go_enriched}.{new_oid}"
                        obj['go_id'] = go_enriched


        # 👇 Important: continue recursive enrichment for all keys in object
        for v in obj.values():
            enrich_yaml_ids(v, counters, seen_ids, entity_map)

    elif isinstance(obj, list):
        for item in obj:
            enrich_yaml_ids(item, counters, seen_ids, entity_map)

# def replace_references(obj, seen_ids):
#     if isinstance(obj, dict):
#         for k, v in obj.items():
#             if isinstance(v, str):
#                 for old_id, new_id in seen_ids.items():
#                     v = re.sub(rf"\b{old_id}\b", new_id, v)
#                     v = re.sub(rf"\${{\s*{old_id}\s*}}", f"${{{new_id}}}", v)
#                 obj[k] = v
#             else:
#                 replace_references(v, seen_ids)
#     elif isinstance(obj, list):
#         for item in obj:
#             replace_references(item, seen_ids)

def replace_references(obj, seen_ids, entity_map):
    if isinstance(obj, dict):
        # Special handling for slot_id fields
        if 'slot_id' in obj and isinstance(obj['slot_id'], str):
            parts = obj['slot_id'].split('.')
            if len(parts) >= 3:
                entity_id = parts[0]
                attr_id = parts[1]
                item_id = parts[2]
                
                # Check if we need to update the attribute ID
                if attr_id in seen_ids:
                    new_attr_id = seen_ids[attr_id]
                    obj['slot_id'] = f"{entity_id}.{new_attr_id}.{item_id}"
                    print(f"Replaced string reference at {obj.get('id', 'unknown')}.slot_id: {entity_id}.{attr_id}.{item_id} -> {entity_id}.{new_attr_id}.{item_id}")
        
        # Process all key-value pairs
        for k, v in obj.items():
            if isinstance(v, str):
                for old_id, new_id in seen_ids.items():
                    v = re.sub(rf"\b{old_id}\b", new_id, v)
                    v = re.sub(rf"\${{\s*{old_id}\s*}}", f"${{{new_id}}}", v)
                obj[k] = v
            # Special handling for nested_function parameters
            elif k == "parameters" and isinstance(v, dict):
                # Process entity references in parameters first
                entity_id = None
                if "entity" in v and isinstance(v["entity"], str):
                    entity_ref = v["entity"]
                    if entity_ref in seen_ids:
                        entity_id = seen_ids[entity_ref]
                        v["entity"] = entity_id
                        print(f"Processing entity reference: {entity_ref}")
                        print(f"Mapped entity reference: {entity_ref} -> {entity_id}")
                
                # Process attribute references in parameters with entity context
                if "attribute" in v and isinstance(v["attribute"], str) and entity_id:
                    attr_ref = v["attribute"]
                    print(f"Processing attribute reference: {attr_ref}")
                    print(f"Have entity context: {entity_id}")
                    
                    # Check if the attribute belongs to the specified entity
                    # First, find the entity in entity_map
                    entity_found = False
                    for entity_name, entity_data in entity_map.items():
                        if entity_data["entity_id"] == entity_id:
                            entity_found = True
                            # If we found the entity, check if the attribute exists for this entity
                            if attr_ref in seen_ids:
                                # Only apply the mapping if the attribute belongs to this entity
                                attr_id = seen_ids[attr_ref]
                                if attr_id in entity_data["attributes"].values():
                                    v["attribute"] = attr_id
                                    print(f"Mapped attribute {attr_ref} to {attr_id} for entity {entity_id}")
                                    break
                            break
                    
                    if not entity_found:
                        print(f"Entity {entity_id} not found in entity_map")
                        # Apply direct mapping if entity not found
                        if attr_ref in seen_ids:
                            v["attribute"] = seen_ids[attr_ref]
                            print(f"Applied direct mapping for attribute: {attr_ref} -> {seen_ids[attr_ref]}")
                
                # If no entity-specific mapping was applied, fall back to direct mapping
                if "attribute" in v and isinstance(v["attribute"], str):
                    attr_ref = v["attribute"]
                    if attr_ref in seen_ids and v["attribute"] == attr_ref:  # Only if not already mapped
                        v["attribute"] = seen_ids[attr_ref]
                        print(f"Applied direct mapping for attribute: {attr_ref} -> {seen_ids[attr_ref]}")
                
                # Process other parameters recursively
                replace_references(v, seen_ids, entity_map)
            # Handle output_to field in nested functions
            elif k == "output_to" and isinstance(v, str):
                if v in seen_ids:
                    obj[k] = seen_ids[v]
                    print(f"Replaced string reference at {obj.get('id', 'unknown')}.output_to: {v} -> {seen_ids[v]}")
            # Handle dropdown_source.function_params.attribute_id
            elif k == "dropdown_source" and isinstance(v, dict) and "function_params" in v:
                if "attribute_id" in v["function_params"] and v["function_params"]["attribute_id"] in seen_ids:
                    old_attr_id = v["function_params"]["attribute_id"]
                    new_attr_id = seen_ids[old_attr_id]
                    v["function_params"]["attribute_id"] = new_attr_id
                    print(f"Replaced string reference at dropdown_source.function_params.attribute_id: {old_attr_id} -> {new_attr_id}")
                
                # Process the rest of dropdown_source recursively
                replace_references(v, seen_ids, entity_map)
            # Handle validations with entity and attribute fields
            elif k == "validations" and isinstance(v, list):
                for validation in v:
                    if isinstance(validation, dict):
                        if "entity" in validation and validation["entity"] in seen_ids:
                            old_entity_id = validation["entity"]
                            new_entity_id = seen_ids[old_entity_id]
                            validation["entity"] = new_entity_id
                            print(f"Replaced entity reference in validation: {old_entity_id} -> {new_entity_id}")
                        
                        if "attribute" in validation and validation["attribute"] in seen_ids:
                            old_attr_id = validation["attribute"]
                            new_attr_id = seen_ids[old_attr_id]
                            validation["attribute"] = new_attr_id
                            print(f"Replaced attribute reference in validation: {old_attr_id} -> {new_attr_id}")
                        
                        # Process the rest of the validation recursively
                        replace_references(validation, seen_ids, entity_map)
            # Handle execution_pathway conditions
            elif k == "execution_pathway" and isinstance(v, dict) and "conditions" in v:
                for condition in v.get("conditions", []):
                    if "condition" in condition and isinstance(condition["condition"], dict):
                        cond = condition["condition"]
                        if "attribute" in cond and cond["attribute"] in seen_ids:
                            old_attr_id = cond["attribute"]
                            new_attr_id = seen_ids[old_attr_id]
                            cond["attribute"] = new_attr_id
                            print(f"Replaced string reference at {obj.get('id', 'unknown')}.execution_pathway.conditions[].condition.attribute: {old_attr_id} -> {new_attr_id}")
            else:
                replace_references(v, seen_ids, entity_map)
    elif isinstance(obj, list):
        for item in obj:
            replace_references(item, seen_ids, entity_map)

def assign_unique_ids_to_yaml(yaml_text, conn):
    data = yaml.safe_load(yaml_text)
    print("\n🔍 Parsing and enriching YAML IDs...")
    max_ids = fetch_current_max_ids(conn)
    counters = defaultdict(int, max_ids)
    seen_ids = {}
    entity_map = fetch_existing_entities_and_attributes(conn)

    print("\n🔢 Max ID counters from Postgres:")
    for prefix, count in counters.items():
        print(f"  {prefix.upper():4} → {count}")

    enrich_yaml_ids(data, counters, seen_ids, entity_map)
    replace_references(data, seen_ids, entity_map)

    print("\n🔁 ID Mappings:")
    for old_id, new_id in seen_ids.items():
        print(f"  🔁 {old_id} → {new_id}")

    print(f"\n✅ ID enrichment complete. Total replacements: {len(seen_ids)}\n")
    #return yaml.dump(data, sort_keys=False)
    return data

def find_parent_go_id(lo_obj):
    # Best-effort: try to extract go_id from contextual_id
    ctx = lo_obj.get("contextual_id", "")
    if "." in ctx:
        return ctx.split(".")[0]
    return None


if __name__ == "__main__":
    with open("yamls/hrms.yaml", "r") as f:
        yaml_input = f.read()

    conn = get_connection()
    enriched = assign_unique_ids_to_yaml(yaml_input, conn)

    with open("yamls/enriched_hrms.yaml", "w") as f:
        f.write(enriched)

    print("📦 YAML saved to: yamls/enriched_hrms.yaml")

# 👇 Add at the bottom of id_generator.py
enrich_ids = assign_unique_ids_to_yaml
