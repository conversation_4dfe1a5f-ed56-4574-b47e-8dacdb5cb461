2025-05-15 08:04:54,213 - restore_workflow_temp_schema - INFO - Step 1: Recreating workflow_temp schema
2025-05-15 08:04:54,213 - restore_workflow_temp_schema - INFO - Recreating workflow_temp schema
2025-05-15 08:04:54,224 - restore_workflow_temp_schema - INFO - Dropped schema workflow_temp
2025-05-15 08:04:54,230 - restore_workflow_temp_schema - INFO - Created schema workflow_temp
2025-05-15 08:04:54,237 - restore_workflow_temp_schema - INFO - Found 81 tables in workflow_runtime
2025-05-15 08:04:54,254 - restore_workflow_temp_schema - INFO - Created table workflow_temp.attribute_ui_controls
2025-05-15 08:04:54,270 - restore_workflow_temp_schema - INFO - Created table workflow_temp.entity_attribute_metadata
2025-05-15 08:04:54,288 - restore_workflow_temp_schema - INFO - Created table workflow_temp.z_entity_leave_application
2025-05-15 08:04:54,307 - restore_workflow_temp_schema - INFO - Created table workflow_temp.entities
2025-05-15 08:04:54,325 - restore_workflow_temp_schema - INFO - Created table workflow_temp.z_entity_leave_sub_type
2025-05-15 08:04:54,344 - restore_workflow_temp_schema - INFO - Created table workflow_temp.z_entity_user
2025-05-15 08:04:54,362 - restore_workflow_temp_schema - INFO - Created table workflow_temp.execution_path_tracking
2025-05-15 08:04:54,380 - restore_workflow_temp_schema - INFO - Created table workflow_temp.execution_pathway_conditions
2025-05-15 08:04:54,396 - restore_workflow_temp_schema - INFO - Created table workflow_temp.execution_rules
2025-05-15 08:04:54,414 - restore_workflow_temp_schema - INFO - Created table workflow_temp.user_sessions
2025-05-15 08:04:54,431 - restore_workflow_temp_schema - INFO - Created table workflow_temp.z_entity_role
2025-05-15 08:04:54,447 - restore_workflow_temp_schema - INFO - Created table workflow_temp.go_lo_mapping
2025-05-15 08:04:54,462 - restore_workflow_temp_schema - INFO - Created table workflow_temp.entity_business_rules
2025-05-15 08:04:54,478 - restore_workflow_temp_schema - INFO - Created table workflow_temp.alembic_version
2025-05-15 08:04:54,494 - restore_workflow_temp_schema - INFO - Created table workflow_temp.role
2025-05-15 08:04:54,509 - restore_workflow_temp_schema - INFO - Created table workflow_temp.user
2025-05-15 08:04:54,525 - restore_workflow_temp_schema - INFO - Created table workflow_temp.entity_relationships
2025-05-15 08:04:54,541 - restore_workflow_temp_schema - INFO - Created table workflow_temp.patient
2025-05-15 08:04:54,556 - restore_workflow_temp_schema - INFO - Created table workflow_temp.go_performance_metrics
2025-05-15 08:04:54,572 - restore_workflow_temp_schema - INFO - Created table workflow_temp.output_stack
2025-05-15 08:04:54,589 - restore_workflow_temp_schema - INFO - Created table workflow_temp.data_mapping_stack
2025-05-15 08:04:54,607 - restore_workflow_temp_schema - INFO - Created table workflow_temp.entity_permissions
2025-05-15 08:04:54,622 - restore_workflow_temp_schema - INFO - Created table workflow_temp.role_inheritance
2025-05-15 08:04:54,641 - restore_workflow_temp_schema - INFO - Created table workflow_temp.entity_attributes
2025-05-15 08:04:54,658 - restore_workflow_temp_schema - INFO - Created table workflow_temp.lo_input_execution
2025-05-15 08:04:54,676 - restore_workflow_temp_schema - INFO - Created table workflow_temp.appointment
2025-05-15 08:04:54,691 - restore_workflow_temp_schema - ERROR - Error executing query: syntax error at or near "USER"
LINE 3: ...extual_id character varying NOT NULL, source_type USER-DEFIN...
                                                             ^
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/backup_dev_files/restore_workflow_temp_schema.py", line 97, in execute_query
    cursor.execute(query)
psycopg2.errors.SyntaxError: syntax error at or near "USER"
LINE 3: ...extual_id character varying NOT NULL, source_type USER-DEFIN...
                                                             ^

2025-05-15 08:04:54,694 - restore_workflow_temp_schema - ERROR - Failed to create table workflow_temp.lo_input_items: ['Error executing query: syntax error at or near "USER"\nLINE 3: ...extual_id character varying NOT NULL, source_type USER-DEFIN...\n                                                             ^\n']
2025-05-15 08:04:54,711 - restore_workflow_temp_schema - INFO - Created table workflow_temp.attribute_enum_values
2025-05-15 08:04:54,728 - restore_workflow_temp_schema - INFO - Created table workflow_temp.attribute_validations
2025-05-15 08:04:54,743 - restore_workflow_temp_schema - INFO - Created table workflow_temp.system_functions
2025-05-15 08:04:54,758 - restore_workflow_temp_schema - INFO - Created table workflow_temp.agent_stack
2025-05-15 08:04:54,772 - restore_workflow_temp_schema - INFO - Created table workflow_temp.user_roles
2025-05-15 08:04:54,787 - restore_workflow_temp_schema - INFO - Created table workflow_temp.lo_nested_functions
2025-05-15 08:04:54,801 - restore_workflow_temp_schema - INFO - Created table workflow_temp.lo_output_items
2025-05-15 08:04:54,817 - restore_workflow_temp_schema - INFO - Created table workflow_temp.input_stack
2025-05-15 08:04:54,834 - restore_workflow_temp_schema - INFO - Created table workflow_temp.input_dependencies
2025-05-15 08:04:54,851 - restore_workflow_temp_schema - INFO - Created table workflow_temp.lo_output_execution
2025-05-15 08:04:54,870 - restore_workflow_temp_schema - INFO - Created table workflow_temp.medical_record
2025-05-15 08:04:54,887 - restore_workflow_temp_schema - INFO - Created table workflow_temp.tenants
2025-05-15 08:04:54,906 - restore_workflow_temp_schema - INFO - Created table workflow_temp.runtime_metrics_stack
2025-05-15 08:04:54,924 - restore_workflow_temp_schema - INFO - Created table workflow_temp.lo_data_mapping_stack
2025-05-15 08:04:54,943 - restore_workflow_temp_schema - INFO - Created table workflow_temp.prescription
2025-05-15 08:04:54,960 - restore_workflow_temp_schema - INFO - Created table workflow_temp.permission_contexts
2025-05-15 08:04:54,977 - restore_workflow_temp_schema - INFO - Created table workflow_temp.permission_capabilities
2025-05-15 08:04:54,994 - restore_workflow_temp_schema - INFO - Created table workflow_temp.objective_permissions
2025-05-15 08:04:55,013 - restore_workflow_temp_schema - INFO - Created table workflow_temp.success_messages
2025-05-15 08:04:55,030 - restore_workflow_temp_schema - INFO - Created table workflow_temp.lab_report
2025-05-15 08:04:55,047 - restore_workflow_temp_schema - INFO - Created table workflow_temp.roles
2025-05-15 08:04:55,065 - restore_workflow_temp_schema - INFO - Created table workflow_temp.permission_types
2025-05-15 08:04:55,081 - restore_workflow_temp_schema - INFO - Created table workflow_temp.lo_system_functions
2025-05-15 08:04:55,099 - restore_workflow_temp_schema - INFO - Created table workflow_temp.execution_pathways
2025-05-15 08:04:55,118 - restore_workflow_temp_schema - INFO - Created table workflow_temp.user_organizations
2025-05-15 08:04:55,136 - restore_workflow_temp_schema - INFO - Created table workflow_temp.output_triggers
2025-05-15 08:04:55,153 - restore_workflow_temp_schema - INFO - Created table workflow_temp.agent_rights
2025-05-15 08:04:55,171 - restore_workflow_temp_schema - INFO - Created table workflow_temp.conditional_success_messages
2025-05-15 08:04:55,190 - restore_workflow_temp_schema - INFO - Created table workflow_temp.lo_input_validations
2025-05-15 08:04:55,205 - restore_workflow_temp_schema - INFO - Created table workflow_temp.users
2025-05-15 08:04:55,221 - restore_workflow_temp_schema - INFO - Created table workflow_temp.user_oauth_tokens
2025-05-15 08:04:55,236 - restore_workflow_temp_schema - INFO - Created table workflow_temp.local_objectives
2025-05-15 08:04:55,252 - restore_workflow_temp_schema - INFO - Created table workflow_temp.global_objectives
2025-05-15 08:04:55,269 - restore_workflow_temp_schema - INFO - Created table workflow_temp.user_role
2025-05-15 08:04:55,288 - restore_workflow_temp_schema - INFO - Created table workflow_temp.workflow_instances
2025-05-15 08:04:55,308 - restore_workflow_temp_schema - INFO - Created table workflow_temp.workflow_results
2025-05-15 08:04:55,325 - restore_workflow_temp_schema - INFO - Created table workflow_temp.output_items
2025-05-15 08:04:55,341 - restore_workflow_temp_schema - INFO - Created table workflow_temp.mapping_rules
2025-05-15 08:04:55,355 - restore_workflow_temp_schema - INFO - Created table workflow_temp.lo_input_stack
2025-05-15 08:04:55,370 - restore_workflow_temp_schema - INFO - Created table workflow_temp.workflow_transaction
2025-05-15 08:04:55,386 - restore_workflow_temp_schema - INFO - Created table workflow_temp.role_permissions
2025-05-15 08:04:55,401 - restore_workflow_temp_schema - INFO - Created table workflow_temp.lo_output_stack
2025-05-15 08:04:55,419 - restore_workflow_temp_schema - INFO - Created table workflow_temp.metrics_aggregation
2025-05-15 08:04:55,435 - restore_workflow_temp_schema - INFO - Created table workflow_temp.ui_stack
2025-05-15 08:04:55,449 - restore_workflow_temp_schema - INFO - Created table workflow_temp.metrics_reporting
2025-05-15 08:04:55,468 - restore_workflow_temp_schema - INFO - Created table workflow_temp.lo_data_mappings
2025-05-15 08:04:55,483 - restore_workflow_temp_schema - INFO - Created table workflow_temp.lo_output_triggers
2025-05-15 08:04:55,501 - restore_workflow_temp_schema - INFO - Created table workflow_temp.input_data_sources
2025-05-15 08:04:55,520 - restore_workflow_temp_schema - INFO - Created table workflow_temp.ui_elements
2025-05-15 08:04:55,538 - restore_workflow_temp_schema - INFO - Created table workflow_temp.input_items
2025-05-15 08:04:55,556 - restore_workflow_temp_schema - INFO - Created table workflow_temp.data_mappings
2025-05-15 08:04:55,571 - restore_workflow_temp_schema - INFO - Created table workflow_temp.organizational_units
2025-05-15 08:04:55,588 - restore_workflow_temp_schema - INFO - Created table workflow_temp.dropdown_data_sources
2025-05-15 08:04:55,605 - restore_workflow_temp_schema - INFO - Created table workflow_temp.terminal_pathways
2025-05-15 08:04:55,605 - restore_workflow_temp_schema - INFO - Successfully recreated workflow_temp schema
2025-05-15 08:04:55,605 - restore_workflow_temp_schema - INFO - Step 2: Adding audit columns to entity-related tables
2025-05-15 08:04:55,606 - restore_workflow_temp_schema - INFO - Adding audit columns to table workflow_temp.entities
2025-05-15 08:04:55,621 - restore_workflow_temp_schema - INFO - Audit column 'created_at' already exists in table workflow_temp.entities
2025-05-15 08:04:55,636 - restore_workflow_temp_schema - INFO - Added audit column 'created_by' to table workflow_temp.entities
2025-05-15 08:04:55,646 - restore_workflow_temp_schema - INFO - Audit column 'updated_at' already exists in table workflow_temp.entities
2025-05-15 08:04:55,659 - restore_workflow_temp_schema - INFO - Added audit column 'updated_by' to table workflow_temp.entities
2025-05-15 08:04:55,659 - restore_workflow_temp_schema - INFO - Audit column 'created_at' already exists in table workflow_temp.entities
2025-05-15 08:04:55,659 - restore_workflow_temp_schema - INFO - Added audit column 'created_by' to table workflow_temp.entities
2025-05-15 08:04:55,659 - restore_workflow_temp_schema - INFO - Audit column 'updated_at' already exists in table workflow_temp.entities
2025-05-15 08:04:55,659 - restore_workflow_temp_schema - INFO - Added audit column 'updated_by' to table workflow_temp.entities
2025-05-15 08:04:55,659 - restore_workflow_temp_schema - INFO - Adding audit columns to table workflow_temp.entity_attributes
2025-05-15 08:04:55,674 - restore_workflow_temp_schema - INFO - Audit column 'created_at' already exists in table workflow_temp.entity_attributes
2025-05-15 08:04:55,688 - restore_workflow_temp_schema - INFO - Added audit column 'created_by' to table workflow_temp.entity_attributes
2025-05-15 08:04:55,697 - restore_workflow_temp_schema - INFO - Audit column 'updated_at' already exists in table workflow_temp.entity_attributes
2025-05-15 08:04:55,710 - restore_workflow_temp_schema - INFO - Added audit column 'updated_by' to table workflow_temp.entity_attributes
2025-05-15 08:04:55,710 - restore_workflow_temp_schema - INFO - Audit column 'created_at' already exists in table workflow_temp.entity_attributes
2025-05-15 08:04:55,710 - restore_workflow_temp_schema - INFO - Added audit column 'created_by' to table workflow_temp.entity_attributes
2025-05-15 08:04:55,710 - restore_workflow_temp_schema - INFO - Audit column 'updated_at' already exists in table workflow_temp.entity_attributes
2025-05-15 08:04:55,710 - restore_workflow_temp_schema - INFO - Added audit column 'updated_by' to table workflow_temp.entity_attributes
2025-05-15 08:04:55,710 - restore_workflow_temp_schema - INFO - Adding audit columns to table workflow_temp.entity_attribute_metadata
2025-05-15 08:04:55,727 - restore_workflow_temp_schema - INFO - Added audit column 'created_at' to table workflow_temp.entity_attribute_metadata
2025-05-15 08:04:55,744 - restore_workflow_temp_schema - INFO - Added audit column 'created_by' to table workflow_temp.entity_attribute_metadata
2025-05-15 08:04:55,759 - restore_workflow_temp_schema - INFO - Added audit column 'updated_at' to table workflow_temp.entity_attribute_metadata
2025-05-15 08:04:55,774 - restore_workflow_temp_schema - INFO - Added audit column 'updated_by' to table workflow_temp.entity_attribute_metadata
2025-05-15 08:04:55,774 - restore_workflow_temp_schema - INFO - Added audit column 'created_at' to table workflow_temp.entity_attribute_metadata
2025-05-15 08:04:55,774 - restore_workflow_temp_schema - INFO - Added audit column 'created_by' to table workflow_temp.entity_attribute_metadata
2025-05-15 08:04:55,774 - restore_workflow_temp_schema - INFO - Added audit column 'updated_at' to table workflow_temp.entity_attribute_metadata
2025-05-15 08:04:55,774 - restore_workflow_temp_schema - INFO - Added audit column 'updated_by' to table workflow_temp.entity_attribute_metadata
2025-05-15 08:04:55,774 - restore_workflow_temp_schema - INFO - Adding audit columns to table workflow_temp.entity_business_rules
2025-05-15 08:04:55,795 - restore_workflow_temp_schema - INFO - Added audit column 'created_at' to table workflow_temp.entity_business_rules
2025-05-15 08:04:55,810 - restore_workflow_temp_schema - INFO - Added audit column 'created_by' to table workflow_temp.entity_business_rules
2025-05-15 08:04:55,823 - restore_workflow_temp_schema - INFO - Added audit column 'updated_at' to table workflow_temp.entity_business_rules
2025-05-15 08:04:55,837 - restore_workflow_temp_schema - INFO - Added audit column 'updated_by' to table workflow_temp.entity_business_rules
2025-05-15 08:04:55,837 - restore_workflow_temp_schema - INFO - Added audit column 'created_at' to table workflow_temp.entity_business_rules
2025-05-15 08:04:55,837 - restore_workflow_temp_schema - INFO - Added audit column 'created_by' to table workflow_temp.entity_business_rules
2025-05-15 08:04:55,837 - restore_workflow_temp_schema - INFO - Added audit column 'updated_at' to table workflow_temp.entity_business_rules
2025-05-15 08:04:55,837 - restore_workflow_temp_schema - INFO - Added audit column 'updated_by' to table workflow_temp.entity_business_rules
2025-05-15 08:04:55,837 - restore_workflow_temp_schema - INFO - Adding audit columns to table workflow_temp.entity_relationships
2025-05-15 08:04:55,850 - restore_workflow_temp_schema - INFO - Audit column 'created_at' already exists in table workflow_temp.entity_relationships
2025-05-15 08:04:55,863 - restore_workflow_temp_schema - INFO - Added audit column 'created_by' to table workflow_temp.entity_relationships
2025-05-15 08:04:55,872 - restore_workflow_temp_schema - INFO - Audit column 'updated_at' already exists in table workflow_temp.entity_relationships
2025-05-15 08:04:55,884 - restore_workflow_temp_schema - INFO - Added audit column 'updated_by' to table workflow_temp.entity_relationships
2025-05-15 08:04:55,884 - restore_workflow_temp_schema - INFO - Audit column 'created_at' already exists in table workflow_temp.entity_relationships
2025-05-15 08:04:55,884 - restore_workflow_temp_schema - INFO - Added audit column 'created_by' to table workflow_temp.entity_relationships
2025-05-15 08:04:55,884 - restore_workflow_temp_schema - INFO - Audit column 'updated_at' already exists in table workflow_temp.entity_relationships
2025-05-15 08:04:55,884 - restore_workflow_temp_schema - INFO - Added audit column 'updated_by' to table workflow_temp.entity_relationships
2025-05-15 08:04:55,884 - restore_workflow_temp_schema - INFO - Step 3: Adding ID columns to tables that don't have a primary key
2025-05-15 08:04:55,884 - restore_workflow_temp_schema - INFO - Adding ID column to table workflow_temp.entities
2025-05-15 08:04:55,914 - restore_workflow_temp_schema - INFO - Added ID column to table workflow_temp.entities
2025-05-15 08:04:55,914 - restore_workflow_temp_schema - INFO - Added ID column to table workflow_temp.entities
2025-05-15 08:04:55,914 - restore_workflow_temp_schema - INFO - Adding ID column to table workflow_temp.entity_attributes
2025-05-15 08:04:55,947 - restore_workflow_temp_schema - INFO - Added ID column to table workflow_temp.entity_attributes
2025-05-15 08:04:55,947 - restore_workflow_temp_schema - INFO - Added ID column to table workflow_temp.entity_attributes
2025-05-15 08:04:55,947 - restore_workflow_temp_schema - INFO - Adding ID column to table workflow_temp.entity_attribute_metadata
2025-05-15 08:04:55,980 - restore_workflow_temp_schema - INFO - Added ID column to table workflow_temp.entity_attribute_metadata
2025-05-15 08:04:55,980 - restore_workflow_temp_schema - INFO - Added ID column to table workflow_temp.entity_attribute_metadata
2025-05-15 08:04:55,980 - restore_workflow_temp_schema - INFO - Adding ID column to table workflow_temp.entity_business_rules
2025-05-15 08:04:56,014 - restore_workflow_temp_schema - INFO - Added ID column to table workflow_temp.entity_business_rules
2025-05-15 08:04:56,014 - restore_workflow_temp_schema - INFO - Added ID column to table workflow_temp.entity_business_rules
2025-05-15 08:04:56,014 - restore_workflow_temp_schema - INFO - Adding ID column to table workflow_temp.entity_relationships
2025-05-15 08:04:56,033 - restore_workflow_temp_schema - INFO - ID column already exists in table workflow_temp.entity_relationships
2025-05-15 08:04:56,033 - restore_workflow_temp_schema - INFO - ID column already exists in table workflow_temp.entity_relationships
2025-05-15 08:04:56,033 - restore_workflow_temp_schema - INFO - Step 4: Fixing tables with missing columns
2025-05-15 08:04:56,033 - restore_workflow_temp_schema - INFO - Fixing workflow_temp.global_objectives table
2025-05-15 08:04:56,051 - restore_workflow_temp_schema - INFO - Column process_mining_schema already exists in workflow_temp.global_objectives
2025-05-15 08:04:56,061 - restore_workflow_temp_schema - INFO - Column performance_metadata already exists in workflow_temp.global_objectives
2025-05-15 08:04:56,072 - restore_workflow_temp_schema - INFO - Column version_type already exists in workflow_temp.global_objectives
2025-05-15 08:04:56,082 - restore_workflow_temp_schema - INFO - Column status already exists in workflow_temp.global_objectives
2025-05-15 08:04:56,091 - restore_workflow_temp_schema - INFO - Column tenant_id already exists in workflow_temp.global_objectives
2025-05-15 08:04:56,101 - restore_workflow_temp_schema - INFO - Column deleted_mark already exists in workflow_temp.global_objectives
2025-05-15 08:04:56,102 - restore_workflow_temp_schema - INFO - Fixing workflow_temp.entities table
2025-05-15 08:04:56,117 - restore_workflow_temp_schema - INFO - Column metadata already exists in workflow_temp.entities
2025-05-15 08:04:56,127 - restore_workflow_temp_schema - INFO - Column lifecycle_management already exists in workflow_temp.entities
2025-05-15 08:04:56,136 - restore_workflow_temp_schema - INFO - Column version_type already exists in workflow_temp.entities
2025-05-15 08:04:56,146 - restore_workflow_temp_schema - INFO - Column status already exists in workflow_temp.entities
2025-05-15 08:04:56,155 - restore_workflow_temp_schema - INFO - Column type already exists in workflow_temp.entities
2025-05-15 08:04:56,164 - restore_workflow_temp_schema - INFO - Column attribute_prefix already exists in workflow_temp.entities
2025-05-15 08:04:56,165 - restore_workflow_temp_schema - INFO - Fixing workflow_temp.entity_attributes table
2025-05-15 08:04:56,179 - restore_workflow_temp_schema - INFO - Column type already exists in workflow_temp.entity_attributes
2025-05-15 08:04:56,189 - restore_workflow_temp_schema - INFO - Column default_value already exists in workflow_temp.entity_attributes
2025-05-15 08:04:56,198 - restore_workflow_temp_schema - INFO - Column calculated_field already exists in workflow_temp.entity_attributes
2025-05-15 08:04:56,207 - restore_workflow_temp_schema - INFO - Column calculation_formula already exists in workflow_temp.entity_attributes
2025-05-15 08:04:56,216 - restore_workflow_temp_schema - INFO - Column dependencies already exists in workflow_temp.entity_attributes
2025-05-15 08:04:56,226 - restore_workflow_temp_schema - INFO - Column display_name already exists in workflow_temp.entity_attributes
2025-05-15 08:04:56,236 - restore_workflow_temp_schema - INFO - Column datatype already exists in workflow_temp.entity_attributes
2025-05-15 08:04:56,246 - restore_workflow_temp_schema - INFO - Column status already exists in workflow_temp.entity_attributes
2025-05-15 08:04:56,246 - restore_workflow_temp_schema - INFO - Fixing workflow_temp.local_objectives table
2025-05-15 08:04:56,263 - restore_workflow_temp_schema - INFO - Column description already exists in workflow_temp.local_objectives
2025-05-15 08:04:56,272 - restore_workflow_temp_schema - INFO - Column ui_stack already exists in workflow_temp.local_objectives
2025-05-15 08:04:56,282 - restore_workflow_temp_schema - INFO - Column mapping_stack already exists in workflow_temp.local_objectives
2025-05-15 08:04:56,291 - restore_workflow_temp_schema - INFO - Column version_type already exists in workflow_temp.local_objectives
2025-05-15 08:04:56,291 - restore_workflow_temp_schema - INFO - Fixing workflow_temp.roles table
2025-05-15 08:04:56,308 - restore_workflow_temp_schema - INFO - Column version_type already exists in workflow_temp.roles
2025-05-15 08:04:56,318 - restore_workflow_temp_schema - INFO - Column tenant_id already exists in workflow_temp.roles
2025-05-15 08:04:56,318 - restore_workflow_temp_schema - INFO - Step 5: Creating missing tables
2025-05-15 08:04:56,318 - restore_workflow_temp_schema - INFO - Creating missing tables in workflow_temp schema
2025-05-15 08:04:56,345 - restore_workflow_temp_schema - INFO - Successfully created missing tables
2025-05-15 08:04:56,345 - restore_workflow_temp_schema - INFO - Successfully restored workflow_temp schema
2025-05-15 08:18:56,346 - restore_workflow_temp_schema - INFO - Step 1: Recreating workflow_temp schema
2025-05-15 08:18:56,346 - restore_workflow_temp_schema - INFO - Recreating workflow_temp schema
2025-05-15 08:18:56,374 - restore_workflow_temp_schema - INFO - Dropped schema workflow_temp
2025-05-15 08:18:56,380 - restore_workflow_temp_schema - INFO - Created schema workflow_temp
2025-05-15 08:18:56,388 - restore_workflow_temp_schema - INFO - Found 81 tables in workflow_runtime
2025-05-15 08:18:56,408 - restore_workflow_temp_schema - INFO - Created table workflow_temp.attribute_ui_controls
2025-05-15 08:18:56,425 - restore_workflow_temp_schema - INFO - Created table workflow_temp.entity_attribute_metadata
2025-05-15 08:18:56,444 - restore_workflow_temp_schema - INFO - Created table workflow_temp.z_entity_leave_application
2025-05-15 08:18:56,463 - restore_workflow_temp_schema - INFO - Created table workflow_temp.entities
2025-05-15 08:18:56,480 - restore_workflow_temp_schema - INFO - Created table workflow_temp.z_entity_leave_sub_type
2025-05-15 08:18:56,499 - restore_workflow_temp_schema - INFO - Created table workflow_temp.z_entity_user
2025-05-15 08:18:56,518 - restore_workflow_temp_schema - INFO - Created table workflow_temp.execution_path_tracking
2025-05-15 08:18:56,538 - restore_workflow_temp_schema - INFO - Created table workflow_temp.execution_pathway_conditions
2025-05-15 08:18:56,557 - restore_workflow_temp_schema - INFO - Created table workflow_temp.execution_rules
2025-05-15 08:18:56,576 - restore_workflow_temp_schema - INFO - Created table workflow_temp.user_sessions
2025-05-15 08:18:56,595 - restore_workflow_temp_schema - INFO - Created table workflow_temp.z_entity_role
2025-05-15 08:18:56,615 - restore_workflow_temp_schema - INFO - Created table workflow_temp.go_lo_mapping
2025-05-15 08:18:56,633 - restore_workflow_temp_schema - INFO - Created table workflow_temp.entity_business_rules
2025-05-15 08:18:56,651 - restore_workflow_temp_schema - INFO - Created table workflow_temp.alembic_version
2025-05-15 08:18:56,669 - restore_workflow_temp_schema - INFO - Created table workflow_temp.role
2025-05-15 08:18:56,687 - restore_workflow_temp_schema - INFO - Created table workflow_temp.user
2025-05-15 08:18:56,705 - restore_workflow_temp_schema - INFO - Created table workflow_temp.entity_relationships
2025-05-15 08:18:56,724 - restore_workflow_temp_schema - INFO - Created table workflow_temp.patient
2025-05-15 08:18:56,742 - restore_workflow_temp_schema - INFO - Created table workflow_temp.go_performance_metrics
2025-05-15 08:18:56,762 - restore_workflow_temp_schema - INFO - Created table workflow_temp.output_stack
2025-05-15 08:18:56,781 - restore_workflow_temp_schema - INFO - Created table workflow_temp.data_mapping_stack
2025-05-15 08:18:56,799 - restore_workflow_temp_schema - INFO - Created table workflow_temp.entity_permissions
2025-05-15 08:18:56,818 - restore_workflow_temp_schema - INFO - Created table workflow_temp.role_inheritance
2025-05-15 08:18:56,836 - restore_workflow_temp_schema - INFO - Created table workflow_temp.entity_attributes
2025-05-15 08:18:56,855 - restore_workflow_temp_schema - INFO - Created table workflow_temp.lo_input_execution
2025-05-15 08:18:56,874 - restore_workflow_temp_schema - INFO - Created table workflow_temp.appointment
2025-05-15 08:18:56,891 - restore_workflow_temp_schema - ERROR - Error executing query: syntax error at or near "USER"
LINE 3: ...extual_id character varying NOT NULL, source_type USER-DEFIN...
                                                             ^
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/backup_dev_files/restore_workflow_temp_schema.py", line 97, in execute_query
    cursor.execute(query)
psycopg2.errors.SyntaxError: syntax error at or near "USER"
LINE 3: ...extual_id character varying NOT NULL, source_type USER-DEFIN...
                                                             ^

2025-05-15 08:18:56,896 - restore_workflow_temp_schema - ERROR - Failed to create table workflow_temp.lo_input_items: ['Error executing query: syntax error at or near "USER"\nLINE 3: ...extual_id character varying NOT NULL, source_type USER-DEFIN...\n                                                             ^\n']
2025-05-15 08:18:56,914 - restore_workflow_temp_schema - INFO - Created table workflow_temp.attribute_enum_values
2025-05-15 08:18:56,933 - restore_workflow_temp_schema - INFO - Created table workflow_temp.attribute_validations
2025-05-15 08:18:56,950 - restore_workflow_temp_schema - INFO - Created table workflow_temp.system_functions
2025-05-15 08:18:56,969 - restore_workflow_temp_schema - INFO - Created table workflow_temp.agent_stack
2025-05-15 08:18:56,984 - restore_workflow_temp_schema - INFO - Created table workflow_temp.user_roles
2025-05-15 08:18:57,001 - restore_workflow_temp_schema - INFO - Created table workflow_temp.lo_nested_functions
2025-05-15 08:18:57,018 - restore_workflow_temp_schema - INFO - Created table workflow_temp.lo_output_items
2025-05-15 08:18:57,031 - restore_workflow_temp_schema - INFO - Created table workflow_temp.input_stack
2025-05-15 08:18:57,047 - restore_workflow_temp_schema - INFO - Created table workflow_temp.input_dependencies
2025-05-15 08:18:57,062 - restore_workflow_temp_schema - INFO - Created table workflow_temp.lo_output_execution
2025-05-15 08:18:57,080 - restore_workflow_temp_schema - INFO - Created table workflow_temp.medical_record
2025-05-15 08:18:57,099 - restore_workflow_temp_schema - INFO - Created table workflow_temp.tenants
2025-05-15 08:18:57,117 - restore_workflow_temp_schema - INFO - Created table workflow_temp.runtime_metrics_stack
2025-05-15 08:18:57,134 - restore_workflow_temp_schema - INFO - Created table workflow_temp.lo_data_mapping_stack
2025-05-15 08:18:57,152 - restore_workflow_temp_schema - INFO - Created table workflow_temp.prescription
2025-05-15 08:18:57,169 - restore_workflow_temp_schema - INFO - Created table workflow_temp.permission_contexts
2025-05-15 08:18:57,187 - restore_workflow_temp_schema - INFO - Created table workflow_temp.permission_capabilities
2025-05-15 08:18:57,206 - restore_workflow_temp_schema - INFO - Created table workflow_temp.objective_permissions
2025-05-15 08:18:57,223 - restore_workflow_temp_schema - INFO - Created table workflow_temp.success_messages
2025-05-15 08:18:57,242 - restore_workflow_temp_schema - INFO - Created table workflow_temp.lab_report
2025-05-15 08:18:57,260 - restore_workflow_temp_schema - INFO - Created table workflow_temp.roles
2025-05-15 08:18:57,278 - restore_workflow_temp_schema - INFO - Created table workflow_temp.permission_types
2025-05-15 08:18:57,296 - restore_workflow_temp_schema - INFO - Created table workflow_temp.lo_system_functions
2025-05-15 08:18:57,315 - restore_workflow_temp_schema - INFO - Created table workflow_temp.execution_pathways
2025-05-15 08:18:57,333 - restore_workflow_temp_schema - INFO - Created table workflow_temp.user_organizations
2025-05-15 08:18:57,351 - restore_workflow_temp_schema - INFO - Created table workflow_temp.output_triggers
2025-05-15 08:18:57,368 - restore_workflow_temp_schema - INFO - Created table workflow_temp.agent_rights
2025-05-15 08:18:57,386 - restore_workflow_temp_schema - INFO - Created table workflow_temp.conditional_success_messages
2025-05-15 08:18:57,403 - restore_workflow_temp_schema - INFO - Created table workflow_temp.lo_input_validations
2025-05-15 08:18:57,423 - restore_workflow_temp_schema - INFO - Created table workflow_temp.users
2025-05-15 08:18:57,442 - restore_workflow_temp_schema - INFO - Created table workflow_temp.user_oauth_tokens
2025-05-15 08:18:57,460 - restore_workflow_temp_schema - INFO - Created table workflow_temp.local_objectives
2025-05-15 08:18:57,479 - restore_workflow_temp_schema - INFO - Created table workflow_temp.global_objectives
2025-05-15 08:18:57,497 - restore_workflow_temp_schema - INFO - Created table workflow_temp.user_role
2025-05-15 08:18:57,516 - restore_workflow_temp_schema - INFO - Created table workflow_temp.workflow_instances
2025-05-15 08:18:57,534 - restore_workflow_temp_schema - INFO - Created table workflow_temp.workflow_results
2025-05-15 08:18:57,551 - restore_workflow_temp_schema - INFO - Created table workflow_temp.output_items
2025-05-15 08:18:57,570 - restore_workflow_temp_schema - INFO - Created table workflow_temp.mapping_rules
2025-05-15 08:18:57,590 - restore_workflow_temp_schema - INFO - Created table workflow_temp.lo_input_stack
2025-05-15 08:18:57,607 - restore_workflow_temp_schema - INFO - Created table workflow_temp.workflow_transaction
2025-05-15 08:18:57,625 - restore_workflow_temp_schema - INFO - Created table workflow_temp.role_permissions
2025-05-15 08:18:57,643 - restore_workflow_temp_schema - INFO - Created table workflow_temp.lo_output_stack
2025-05-15 08:18:57,662 - restore_workflow_temp_schema - INFO - Created table workflow_temp.metrics_aggregation
2025-05-15 08:18:57,679 - restore_workflow_temp_schema - INFO - Created table workflow_temp.ui_stack
2025-05-15 08:18:57,696 - restore_workflow_temp_schema - INFO - Created table workflow_temp.metrics_reporting
2025-05-15 08:18:57,711 - restore_workflow_temp_schema - INFO - Created table workflow_temp.lo_data_mappings
2025-05-15 08:18:57,729 - restore_workflow_temp_schema - INFO - Created table workflow_temp.lo_output_triggers
2025-05-15 08:18:57,746 - restore_workflow_temp_schema - INFO - Created table workflow_temp.input_data_sources
2025-05-15 08:18:57,764 - restore_workflow_temp_schema - INFO - Created table workflow_temp.ui_elements
2025-05-15 08:18:57,783 - restore_workflow_temp_schema - INFO - Created table workflow_temp.input_items
2025-05-15 08:18:57,801 - restore_workflow_temp_schema - INFO - Created table workflow_temp.data_mappings
2025-05-15 08:18:57,817 - restore_workflow_temp_schema - INFO - Created table workflow_temp.organizational_units
2025-05-15 08:18:57,837 - restore_workflow_temp_schema - INFO - Created table workflow_temp.dropdown_data_sources
2025-05-15 08:18:57,854 - restore_workflow_temp_schema - INFO - Created table workflow_temp.terminal_pathways
2025-05-15 08:18:57,854 - restore_workflow_temp_schema - INFO - Successfully recreated workflow_temp schema
2025-05-15 08:18:57,854 - restore_workflow_temp_schema - INFO - Step 2: Adding audit columns to entity-related tables
2025-05-15 08:18:57,855 - restore_workflow_temp_schema - INFO - Adding audit columns to table workflow_temp.entities
2025-05-15 08:18:57,871 - restore_workflow_temp_schema - INFO - Audit column 'created_at' already exists in table workflow_temp.entities
2025-05-15 08:18:57,889 - restore_workflow_temp_schema - INFO - Added audit column 'created_by' to table workflow_temp.entities
2025-05-15 08:18:57,899 - restore_workflow_temp_schema - INFO - Audit column 'updated_at' already exists in table workflow_temp.entities
2025-05-15 08:18:57,915 - restore_workflow_temp_schema - INFO - Added audit column 'updated_by' to table workflow_temp.entities
2025-05-15 08:18:57,915 - restore_workflow_temp_schema - INFO - Audit column 'created_at' already exists in table workflow_temp.entities
2025-05-15 08:18:57,915 - restore_workflow_temp_schema - INFO - Added audit column 'created_by' to table workflow_temp.entities
2025-05-15 08:18:57,915 - restore_workflow_temp_schema - INFO - Audit column 'updated_at' already exists in table workflow_temp.entities
2025-05-15 08:18:57,915 - restore_workflow_temp_schema - INFO - Added audit column 'updated_by' to table workflow_temp.entities
2025-05-15 08:18:57,915 - restore_workflow_temp_schema - INFO - Adding audit columns to table workflow_temp.entity_attributes
2025-05-15 08:18:57,933 - restore_workflow_temp_schema - INFO - Audit column 'created_at' already exists in table workflow_temp.entity_attributes
2025-05-15 08:18:57,949 - restore_workflow_temp_schema - INFO - Added audit column 'created_by' to table workflow_temp.entity_attributes
2025-05-15 08:18:57,957 - restore_workflow_temp_schema - INFO - Audit column 'updated_at' already exists in table workflow_temp.entity_attributes
2025-05-15 08:18:57,971 - restore_workflow_temp_schema - INFO - Added audit column 'updated_by' to table workflow_temp.entity_attributes
2025-05-15 08:18:57,971 - restore_workflow_temp_schema - INFO - Audit column 'created_at' already exists in table workflow_temp.entity_attributes
2025-05-15 08:18:57,971 - restore_workflow_temp_schema - INFO - Added audit column 'created_by' to table workflow_temp.entity_attributes
2025-05-15 08:18:57,971 - restore_workflow_temp_schema - INFO - Audit column 'updated_at' already exists in table workflow_temp.entity_attributes
2025-05-15 08:18:57,971 - restore_workflow_temp_schema - INFO - Added audit column 'updated_by' to table workflow_temp.entity_attributes
2025-05-15 08:18:57,971 - restore_workflow_temp_schema - INFO - Adding audit columns to table workflow_temp.entity_attribute_metadata
2025-05-15 08:18:57,989 - restore_workflow_temp_schema - INFO - Added audit column 'created_at' to table workflow_temp.entity_attribute_metadata
2025-05-15 08:18:58,001 - restore_workflow_temp_schema - INFO - Added audit column 'created_by' to table workflow_temp.entity_attribute_metadata
2025-05-15 08:18:58,013 - restore_workflow_temp_schema - INFO - Added audit column 'updated_at' to table workflow_temp.entity_attribute_metadata
2025-05-15 08:18:58,025 - restore_workflow_temp_schema - INFO - Added audit column 'updated_by' to table workflow_temp.entity_attribute_metadata
2025-05-15 08:18:58,025 - restore_workflow_temp_schema - INFO - Added audit column 'created_at' to table workflow_temp.entity_attribute_metadata
2025-05-15 08:18:58,025 - restore_workflow_temp_schema - INFO - Added audit column 'created_by' to table workflow_temp.entity_attribute_metadata
2025-05-15 08:18:58,025 - restore_workflow_temp_schema - INFO - Added audit column 'updated_at' to table workflow_temp.entity_attribute_metadata
2025-05-15 08:18:58,025 - restore_workflow_temp_schema - INFO - Added audit column 'updated_by' to table workflow_temp.entity_attribute_metadata
2025-05-15 08:18:58,025 - restore_workflow_temp_schema - INFO - Adding audit columns to table workflow_temp.entity_business_rules
2025-05-15 08:18:58,042 - restore_workflow_temp_schema - INFO - Added audit column 'created_at' to table workflow_temp.entity_business_rules
2025-05-15 08:18:58,054 - restore_workflow_temp_schema - INFO - Added audit column 'created_by' to table workflow_temp.entity_business_rules
2025-05-15 08:18:58,070 - restore_workflow_temp_schema - INFO - Added audit column 'updated_at' to table workflow_temp.entity_business_rules
2025-05-15 08:18:58,087 - restore_workflow_temp_schema - INFO - Added audit column 'updated_by' to table workflow_temp.entity_business_rules
2025-05-15 08:18:58,088 - restore_workflow_temp_schema - INFO - Added audit column 'created_at' to table workflow_temp.entity_business_rules
2025-05-15 08:18:58,088 - restore_workflow_temp_schema - INFO - Added audit column 'created_by' to table workflow_temp.entity_business_rules
2025-05-15 08:18:58,088 - restore_workflow_temp_schema - INFO - Added audit column 'updated_at' to table workflow_temp.entity_business_rules
2025-05-15 08:18:58,088 - restore_workflow_temp_schema - INFO - Added audit column 'updated_by' to table workflow_temp.entity_business_rules
2025-05-15 08:18:58,088 - restore_workflow_temp_schema - INFO - Adding audit columns to table workflow_temp.entity_relationships
2025-05-15 08:18:58,107 - restore_workflow_temp_schema - INFO - Audit column 'created_at' already exists in table workflow_temp.entity_relationships
2025-05-15 08:18:58,122 - restore_workflow_temp_schema - INFO - Added audit column 'created_by' to table workflow_temp.entity_relationships
2025-05-15 08:18:58,132 - restore_workflow_temp_schema - INFO - Audit column 'updated_at' already exists in table workflow_temp.entity_relationships
2025-05-15 08:18:58,147 - restore_workflow_temp_schema - INFO - Added audit column 'updated_by' to table workflow_temp.entity_relationships
2025-05-15 08:18:58,147 - restore_workflow_temp_schema - INFO - Audit column 'created_at' already exists in table workflow_temp.entity_relationships
2025-05-15 08:18:58,147 - restore_workflow_temp_schema - INFO - Added audit column 'created_by' to table workflow_temp.entity_relationships
2025-05-15 08:18:58,147 - restore_workflow_temp_schema - INFO - Audit column 'updated_at' already exists in table workflow_temp.entity_relationships
2025-05-15 08:18:58,147 - restore_workflow_temp_schema - INFO - Added audit column 'updated_by' to table workflow_temp.entity_relationships
2025-05-15 08:18:58,147 - restore_workflow_temp_schema - INFO - Step 3: Adding ID columns to tables that don't have a primary key
2025-05-15 08:18:58,147 - restore_workflow_temp_schema - INFO - Adding ID column to table workflow_temp.entities
2025-05-15 08:18:58,177 - restore_workflow_temp_schema - INFO - Added ID column to table workflow_temp.entities
2025-05-15 08:18:58,177 - restore_workflow_temp_schema - INFO - Added ID column to table workflow_temp.entities
2025-05-15 08:18:58,177 - restore_workflow_temp_schema - INFO - Adding ID column to table workflow_temp.entity_attributes
2025-05-15 08:18:58,209 - restore_workflow_temp_schema - INFO - Added ID column to table workflow_temp.entity_attributes
2025-05-15 08:18:58,209 - restore_workflow_temp_schema - INFO - Added ID column to table workflow_temp.entity_attributes
2025-05-15 08:18:58,209 - restore_workflow_temp_schema - INFO - Adding ID column to table workflow_temp.entity_attribute_metadata
2025-05-15 08:18:58,242 - restore_workflow_temp_schema - INFO - Added ID column to table workflow_temp.entity_attribute_metadata
2025-05-15 08:18:58,242 - restore_workflow_temp_schema - INFO - Added ID column to table workflow_temp.entity_attribute_metadata
2025-05-15 08:18:58,242 - restore_workflow_temp_schema - INFO - Adding ID column to table workflow_temp.entity_business_rules
2025-05-15 08:18:58,276 - restore_workflow_temp_schema - INFO - Added ID column to table workflow_temp.entity_business_rules
2025-05-15 08:18:58,276 - restore_workflow_temp_schema - INFO - Added ID column to table workflow_temp.entity_business_rules
2025-05-15 08:18:58,276 - restore_workflow_temp_schema - INFO - Adding ID column to table workflow_temp.entity_relationships
2025-05-15 08:18:58,294 - restore_workflow_temp_schema - INFO - ID column already exists in table workflow_temp.entity_relationships
2025-05-15 08:18:58,294 - restore_workflow_temp_schema - INFO - ID column already exists in table workflow_temp.entity_relationships
2025-05-15 08:18:58,294 - restore_workflow_temp_schema - INFO - Step 4: Fixing tables with missing columns
2025-05-15 08:18:58,294 - restore_workflow_temp_schema - INFO - Fixing workflow_temp.global_objectives table
2025-05-15 08:18:58,312 - restore_workflow_temp_schema - INFO - Column process_mining_schema already exists in workflow_temp.global_objectives
2025-05-15 08:18:58,322 - restore_workflow_temp_schema - INFO - Column performance_metadata already exists in workflow_temp.global_objectives
2025-05-15 08:18:58,331 - restore_workflow_temp_schema - INFO - Column version_type already exists in workflow_temp.global_objectives
2025-05-15 08:18:58,341 - restore_workflow_temp_schema - INFO - Column status already exists in workflow_temp.global_objectives
2025-05-15 08:18:58,350 - restore_workflow_temp_schema - INFO - Column tenant_id already exists in workflow_temp.global_objectives
2025-05-15 08:18:58,362 - restore_workflow_temp_schema - INFO - Column deleted_mark already exists in workflow_temp.global_objectives
2025-05-15 08:18:58,362 - restore_workflow_temp_schema - INFO - Fixing workflow_temp.entities table
2025-05-15 08:18:58,383 - restore_workflow_temp_schema - INFO - Column metadata already exists in workflow_temp.entities
2025-05-15 08:18:58,395 - restore_workflow_temp_schema - INFO - Column lifecycle_management already exists in workflow_temp.entities
2025-05-15 08:18:58,406 - restore_workflow_temp_schema - INFO - Column version_type already exists in workflow_temp.entities
2025-05-15 08:18:58,418 - restore_workflow_temp_schema - INFO - Column status already exists in workflow_temp.entities
2025-05-15 08:18:58,429 - restore_workflow_temp_schema - INFO - Column type already exists in workflow_temp.entities
2025-05-15 08:18:58,439 - restore_workflow_temp_schema - INFO - Column attribute_prefix already exists in workflow_temp.entities
2025-05-15 08:18:58,439 - restore_workflow_temp_schema - INFO - Fixing workflow_temp.entity_attributes table
2025-05-15 08:18:58,459 - restore_workflow_temp_schema - INFO - Column type already exists in workflow_temp.entity_attributes
2025-05-15 08:18:58,469 - restore_workflow_temp_schema - INFO - Column default_value already exists in workflow_temp.entity_attributes
2025-05-15 08:18:58,481 - restore_workflow_temp_schema - INFO - Column calculated_field already exists in workflow_temp.entity_attributes
2025-05-15 08:18:58,492 - restore_workflow_temp_schema - INFO - Column calculation_formula already exists in workflow_temp.entity_attributes
2025-05-15 08:18:58,502 - restore_workflow_temp_schema - INFO - Column dependencies already exists in workflow_temp.entity_attributes
2025-05-15 08:18:58,512 - restore_workflow_temp_schema - INFO - Column display_name already exists in workflow_temp.entity_attributes
2025-05-15 08:18:58,523 - restore_workflow_temp_schema - INFO - Column datatype already exists in workflow_temp.entity_attributes
2025-05-15 08:18:58,535 - restore_workflow_temp_schema - INFO - Column status already exists in workflow_temp.entity_attributes
2025-05-15 08:18:58,535 - restore_workflow_temp_schema - INFO - Fixing workflow_temp.local_objectives table
2025-05-15 08:18:58,553 - restore_workflow_temp_schema - INFO - Column description already exists in workflow_temp.local_objectives
2025-05-15 08:18:58,562 - restore_workflow_temp_schema - INFO - Column ui_stack already exists in workflow_temp.local_objectives
2025-05-15 08:18:58,573 - restore_workflow_temp_schema - INFO - Column mapping_stack already exists in workflow_temp.local_objectives
2025-05-15 08:18:58,582 - restore_workflow_temp_schema - INFO - Column version_type already exists in workflow_temp.local_objectives
2025-05-15 08:18:58,582 - restore_workflow_temp_schema - INFO - Fixing workflow_temp.roles table
2025-05-15 08:18:58,600 - restore_workflow_temp_schema - INFO - Column version_type already exists in workflow_temp.roles
2025-05-15 08:18:58,610 - restore_workflow_temp_schema - INFO - Column tenant_id already exists in workflow_temp.roles
2025-05-15 08:18:58,610 - restore_workflow_temp_schema - INFO - Step 5: Creating missing tables
2025-05-15 08:18:58,610 - restore_workflow_temp_schema - INFO - Creating missing tables in workflow_temp schema
2025-05-15 08:18:58,640 - restore_workflow_temp_schema - INFO - Successfully created missing tables
2025-05-15 08:18:58,640 - restore_workflow_temp_schema - INFO - Successfully restored workflow_temp schema
