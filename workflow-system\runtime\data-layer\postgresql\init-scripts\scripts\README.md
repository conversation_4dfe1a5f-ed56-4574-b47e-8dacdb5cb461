# Purchase Furniture Workflow Implementation

This directory contains the complete implementation of the Purchase Furniture workflow (GO2) for the workflow system using tenant T2.

## Overview

The Purchase Furniture workflow consists of:
- **Global Objective**: GO2 - Purchase Furniture
- **Local Objectives**: 
  - GO2.LO1 - SelectFurnitureType
  - GO2.LO2 - ProcessCartPayment  
  - GO2.LO3 - CompleteOrderInventory

## Directory Structure

```
scripts/
├── 00_execute_all_scripts.sql          # Master execution script
├── README.md                           # This documentation
├── entity/                             # Entity-related scripts
│   ├── 01_insert_entities.sql         # Insert entities E13-E16
│   ├── 02_create_entity_tables.sql    # Create database tables
│   └── 03_insert_sample_data.sql      # Insert sample furniture data
├── attribute/                          # Attribute-related scripts
│   └── 01_insert_entity_attributes.sql # Insert entity attributes
├── go/                                 # Global objective scripts
│   └── 01_insert_global_objective.sql # Insert GO2 and pathways
└── lo/                                 # Local objective scripts
    ├── 01_insert_local_objectives.sql      # Insert LO1, LO2, LO3
    ├── 02_insert_lo_input_stacks.sql       # Insert input stacks
    ├── 03_insert_lo_input_items.sql        # Insert input items
    ├── 04_insert_lo_output_stacks.sql      # Insert output stacks
    ├── 05_insert_lo_output_items.sql       # Insert output items
    ├── 06_insert_nested_functions.sql      # Insert nested functions
    ├── 07_insert_nested_function_input_output.sql # Insert NF I/O items
    └── 08_insert_data_mappings.sql         # Insert data mappings
```

## Entities

### E13 - FurnitureOrder
Primary entity for furniture purchase orders
- **Table**: e13_furnitureorder
- **Attributes**: orderid, userid, furnituretypeid, productid, quantity, unitprice, subtotal, gstamount, totalamount, paymentmethod, orderstatus, orderdate

### E14 - FurnitureProduct  
Master data for furniture products
- **Table**: e14_furnitureproduct
- **Attributes**: productid, productname, furnituretypeid, price, availableinventory, description

### E15 - FurnitureType
Master data for furniture categories
- **Table**: e15_furnituretype
- **Attributes**: furnituretypeid, typename, description

### E16 - PaymentDetails
Payment information for orders
- **Table**: e16_paymentdetails
- **Attributes**: paymentid, orderid, paymentmethod, upiid, cardname, cardnumber, cvv, expirydate, paymentstatus

## Workflow Flow

### GO2.LO1 - SelectFurnitureType
User selects furniture type and specific product:
1. **Input**: Furniture type selection (dropdown)
2. **Input**: Product selection (dependent dropdown based on type)
3. **Information**: Display product details (name, price, inventory)
4. **Output**: Selected furniture type ID, product ID, product name, unit price

### GO2.LO2 - ProcessCartPayment
Process cart with calculations and payment:
1. **Input**: Quantity entry
2. **Calculations**: 
   - Subtotal = quantity × unit price (NF_CALCULATE_SUBTOTAL)
   - GST = subtotal × 18% (NF_CALCULATE_GST)
   - Total = subtotal + GST (NF_CALCULATE_TOTAL)
3. **Input**: Payment method selection (UPI/Credit Card)
4. **Input**: Payment details (conditional based on method)
5. **Output**: Quantity, subtotal, GST amount, total amount, payment method, payment details

### GO2.LO3 - CompleteOrderInventory
Complete order and update inventory:
1. **Input**: Order summary from previous step
2. **System**: Generate order ID (NF_GENERATE_ORDER_ID)
3. **System**: Update inventory (NF_UPDATE_INVENTORY)
4. **Output**: Order ID, order status, inventory update confirmation

## Nested Functions

### NF_CALCULATE_SUBTOTAL
- **Purpose**: Calculate subtotal (quantity × unit price)
- **Inputs**: quantity (integer), unitprice (decimal)
- **Output**: subtotal (decimal)

### NF_CALCULATE_GST
- **Purpose**: Calculate 18% GST on subtotal
- **Input**: subtotal (decimal)
- **Output**: gstamount (decimal)

### NF_CALCULATE_TOTAL
- **Purpose**: Calculate total amount (subtotal + GST)
- **Inputs**: subtotal (decimal), gstamount (decimal)
- **Output**: totalamount (decimal)

### NF_GENERATE_ORDER_ID
- **Purpose**: Generate unique order identifier
- **Output**: orderid (string)

### NF_UPDATE_INVENTORY
- **Purpose**: Reduce inventory by ordered quantity
- **Inputs**: productid (string), quantity (integer)
- **Output**: inventoryupdated (boolean)

## Sample Data

The implementation includes sample data for:
- **Furniture Types**: Cupboard, Bar, Study Table, Dining Table
- **Products**: 12 sample products across all categories with pricing and inventory

## Execution Instructions

### Option 1: Execute All Scripts at Once
```sql
-- Connect to PostgreSQL
docker exec -it workflow_postgres psql -U postgres -d workflow_system

-- Execute master script
\i /home/<USER>/workflow-system/scripts/00_execute_all_scripts.sql
```

### Option 2: Execute Scripts Individually
Execute scripts in the following order:

1. **Entities**:
   ```sql
   \i /home/<USER>/workflow-system/scripts/entity/01_insert_entities.sql
   \i /home/<USER>/workflow-system/scripts/entity/02_create_entity_tables.sql
   \i /home/<USER>/workflow-system/scripts/entity/03_insert_sample_data.sql
   ```

2. **Attributes**:
   ```sql
   \i /home/<USER>/workflow-system/scripts/attribute/01_insert_entity_attributes.sql
   ```

3. **Global Objective**:
   ```sql
   \i /home/<USER>/workflow-system/scripts/go/01_insert_global_objective.sql
   ```

4. **Local Objectives**:
   ```sql
   \i /home/<USER>/workflow-system/scripts/lo/01_insert_local_objectives.sql
   \i /home/<USER>/workflow-system/scripts/lo/02_insert_lo_input_stacks.sql
   \i /home/<USER>/workflow-system/scripts/lo/03_insert_lo_input_items.sql
   \i /home/<USER>/workflow-system/scripts/lo/04_insert_lo_output_stacks.sql
   \i /home/<USER>/workflow-system/scripts/lo/05_insert_lo_output_items.sql
   \i /home/<USER>/workflow-system/scripts/lo/06_insert_nested_functions.sql
   \i /home/<USER>/workflow-system/scripts/lo/07_insert_nested_function_input_output.sql
   \i /home/<USER>/workflow-system/scripts/lo/08_insert_data_mappings.sql
   ```

## Configuration

- **Tenant ID**: T2
- **GST Rate**: 18%
- **Payment Methods**: UPI, Credit Card
- **Workflow Type**: Sequential (LO1 → LO2 → LO3)

## Verification

After execution, verify the implementation:

```sql
-- Check entities
SELECT entity_id, name FROM workflow_runtime.entities WHERE entity_id IN ('E13','E14','E15','E16');

-- Check global objective
SELECT go_id, name FROM workflow_runtime.global_objectives WHERE go_id = 'GO2';

-- Check local objectives
SELECT lo_id, name FROM workflow_runtime.local_objectives WHERE go_id = 'GO2';

-- Check sample data
SELECT COUNT(*) FROM workflow_runtime.e15_furnituretype;
SELECT COUNT(*) FROM workflow_runtime.e14_furnitureproduct;
```

## Notes

- All scripts use tenant T2
- Entity tables are created with proper triggers for timestamp updates
- Sample data includes realistic furniture products with pricing
- Nested functions implement business logic for calculations
- Data mappings ensure proper flow between local objectives
- All components follow the existing database patterns and conventions
