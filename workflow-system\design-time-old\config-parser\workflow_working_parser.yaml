tenant:
  id: "T001"
  name: "Sales001"
  roles:
    - id: "R001"
      name: "SalesManager"
      inherits_from: null
      access:
        entities:
          - entity_id: "E001"  # Lead
            permissions: ["Read", "Write", "Delete", "Assign"]
          - entity_id: "E002"  # Contact
            permissions: ["Read", "Write", "Delete"]
          - entity_id: "E003"  # Company
            permissions: ["Read", "Write", "Delete"]
        objectives:
          - objective_id: "GO001"  # Lead Management
            permissions: ["Execute", "Transact"]
          - objective_id: "GO002"  # Lead Analytics
            permissions: ["Information"]
    
    - id: "R002"
      name: "SalesRep"
      inherits_from: null
      access:
        entities:
          - entity_id: "E001"  # Lead
            permissions: ["Read", "Write"]
          - entity_id: "E002"  # Contact
            permissions: ["Read", "Write"]
          - entity_id: "E003"  # Company
            permissions: ["Read"]
        objectives:
          - objective_id: "GO001"  # Lead Management
            permissions: ["Execute"]
          - objective_id: "GO001.LO004"  # Qualify Lead
            permissions: ["Execute"]
    
    - id: "R003"
      name: "MarketingManager"
      inherits_from: null
      access:
        entities:
          - entity_id: "E001"  # Lead
            permissions: ["Read", "Create", "Update"]
          - entity_id: "E004"  # LeadSource
            permissions: ["Read", "Write", "Delete"]
          - entity_id: "E005"  # Campaign
            permissions: ["Read", "Write", "Delete"]
        objectives:
          - objective_id: "GO003"  # Lead Generation
            permissions: ["Execute", "Transact"]
          - objective_id: "GO004"  # Campaign Management
            permissions: ["Execute", "Transact"]

# Permission Types Definition
permission_types:
  - id: "Execute" 
    description: "Can execute only their own assigned transactions"
    capabilities: ["ExecuteOwn"]
  
  - id: "Transact"
    description: "Can view other's transactions and execute both their own and others in the same group"
    capabilities: ["ExecuteOwn", "ExecuteOthers", "ViewGroup"]
  
  - id: "Information"
    description: "Can view all transaction information but cannot execute any transactions"
    capabilities: ["ViewAll"]

# Entity Definitions
entities:
  - id: "E001"
    name: "Lead"
    version: "1.0"
    status: "Active"
    type: "Regular"
    attributes_metadata:
      attribute_prefix: "At"
      attribute_map:
        "At001": "FirstName"
        "At002": "LastName"
        "At003": "Email"
        "At004": "Phone"
        "At005": "Status"
        "At006": "Source"
        "At007": "Company"
        "At008": "Industry"
        "At009": "Score"
        "At010": "AssignedTo"
        "At011": "AssignedDate"
      required_attributes: ["At001", "At002", "At003", "At005", "At006"]
    
    attributes:
      - id: "At001"
        name: "FirstName"
        display_name: "First Name"
        datatype: "String"
        version: "1.0"
        status: "Deployed"
        required: true
      
      - id: "At002"
        name: "LastName"
        display_name: "Last Name"
        datatype: "String"
        version: "1.0"
        status: "Deployed"
        required: true
      
      - id: "At003"
        name: "Email"
        display_name: "Email"
        datatype: "String"
        version: "1.0"
        status: "Deployed"
        required: true
        validations:
          - rule: "Email format validation"
            expression: "^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$"
      
      - id: "At004"
        name: "Phone"
        display_name: "Phone Number"
        datatype: "String"
        version: "1.0"
        status: "Deployed"
        required: false
      
      - id: "At005"
        name: "Status"
        display_name: "Lead Status"
        datatype: "Enum"
        version: "1.0"
        status: "Deployed"
        required: true
        values: ["New", "Contacted", "Qualified", "Unqualified", "Converted"]
      
      - id: "At006"
        name: "Source"
        display_name: "Lead Source"
        datatype: "Enum"
        version: "1.0"
        status: "Deployed"
        required: true
        values: ["Website", "Referral", "Trade Show", "Cold Call", "Social Media", "Email Campaign"]
      
      - id: "At007"
        name: "Company"
        display_name: "Company"
        datatype: "String"
        version: "1.0"
        status: "Deployed"
        required: false
      
      - id: "At008"
        name: "Industry"
        display_name: "Industry"
        datatype: "Enum"
        version: "1.0"
        status: "Deployed"
        required: false
        values: ["Technology", "Finance", "Healthcare", "Manufacturing", "Retail", "Education", "Other"]
      
      - id: "At009"
        name: "Score"
        display_name: "Lead Score"
        datatype: "Number"
        version: "1.0"
        status: "Deployed"
        required: false
      
      - id: "At010"
        name: "AssignedTo"
        display_name: "Assigned To"
        datatype: "Reference"
        version: "1.0"
        status: "Deployed"
        references: "E006"  # SalesRep
        required: false
      
      - id: "At011"
        name: "AssignedDate"
        display_name: "Assigned Date"
        datatype: "DateTime"
        version: "1.0"
        status: "Deployed"
        required: false

    nested_entities:
      - entity_id: "E007"  # LeadActivity
        relationship: "OneToMany"
        constraints:
          - "At least one activity must exist for qualified leads"
    
    relationships:
      - entity_id: "E002"  # Contact
        type: "OneToOne"
        through_attribute: "E001.At003"  # Lead.Email
        to_attribute: "E002.At003"  # Contact.Email
      
      - entity_id: "E003"  # Company
        type: "ManyToOne"
        through_attribute: "E001.At007"  # Lead.Company
        to_attribute: "E003.At001"  # Company.Name

# Regular Entity: Contact
entities:
  - id: "E002"
    name: "Contact"
    version: "1.0"
    status: "Active"
    type: "Regular"
    attributes_metadata:
      attribute_prefix: "At"
      attribute_map:
        "At001": "FirstName"
        "At002": "LastName"
        "At003": "Email"
        "At004": "Phone"
        "At005": "JobTitle"
        "At006": "Type"
      required_attributes: ["At001", "At002", "At003", "At006"]
    attributes:
      - id: "At001"
        name: "FirstName"
        display_name: "First Name"
        datatype: "String"
        version: "1.0"
        status: "Deployed"
        required: true

      - id: "At002"
        name: "LastName"
        display_name: "Last Name"
        datatype: "String"
        version: "1.0"
        status: "Deployed"
        required: true

      - id: "At003"
        name: "Email"
        display_name: "Email"
        datatype: "String"
        version: "1.0"
        status: "Deployed"
        required: true
        validations:
          - rule: "Email format validation"
            expression: "^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$"

      - id: "At004"
        name: "Phone"
        display_name: "Phone Number"
        datatype: "String"
        version: "1.0"
        status: "Deployed"
        required: false

      - id: "At005"
        name: "JobTitle"
        display_name: "Job Title"
        datatype: "String"
        version: "1.0"
        status: "Deployed"
        required: false

      - id: "At006"
        name: "Type"
        display_name: "Contact Type"
        datatype: "Enum"
        version: "1.0"
        status: "Deployed"
        required: true
        values: ["Customer", "Prospect", "Partner", "Vendor"]

    # Nested entities within Contact
    nested_entities:
      - entity_id: "E008"  # Address
        relationship: "OneToMany"
        constraints:
          - "At least one address must be marked as primary"
          - "Address types must be unique within a contact"

      - entity_id: "E009"  # PhoneNumber
        relationship: "OneToMany"
        constraints:
          - "At least one phone must be marked as primary"

  # Regular Entity: Company
  - id: "E003"
    name: "Company"
    version: "1.0"
    status: "Active"
    type: "Regular"
    attributes_metadata:
      attribute_prefix: "At"
      attribute_map:
        "At001": "Name"
        "At002": "Industry"
        "At003": "Size"
        "At004": "Website"
      required_attributes: ["At001", "At002"]
    attributes:
      - id: "At001"
        name: "Name"
        display_name: "Company Name"
        datatype: "String"
        version: "1.0"
        status: "Deployed"
        required: true

      - id: "At002"
        name: "Industry"
        display_name: "Industry"
        datatype: "Enum"
        version: "1.0"
        status: "Deployed"
        required: true
        values: ["Technology", "Finance", "Healthcare", "Manufacturing", "Retail", "Education", "Other"]

      - id: "At003"
        name: "Size"
        display_name: "Company Size"
        datatype: "Enum"
        version: "1.0"
        status: "Deployed"
        required: false
        values: ["1-10", "11-50", "51-200", "201-500", "501-1000", "1000+"]

      - id: "At004"
        name: "Website"
        display_name: "Website"
        datatype: "String"
        version: "1.0"
        status: "Deployed"
        required: false

    # Nested entities within Company
    nested_entities:
      - entity_id: "E008"  # Address
        relationship: "OneToMany"
        constraints:
          - "At least one address must be marked as primary"
          - "Address types must be unique within a company"

  # Nested Entity: Address
  - id: "E008"
    name: "Address"
    version: "1.0"
    status: "Active"
    type: "Nested"
    parent_entities: ["E002", "E003"]  # Contact, Company
    attributes_metadata:
      attribute_prefix: "At"
      attribute_map:
        "At001": "Type"
        "At002": "Street"
        "At003": "City"
        "At004": "State"
        "At005": "PostalCode"
        "At006": "Country"
        "At007": "IsPrimary"
      required_attributes: ["At001", "At002", "At003", "At004", "At005", "At006", "At007"]
    attributes:
      - id: "At001"
        name: "Type"
        display_name: "Address Type"
        datatype: "Enum"
        version: "1.0"
        status: "Deployed"
        required: true
        values: ["Billing", "Shipping", "Primary", "Secondary"]

      - id: "At002"
        name: "Street"
        display_name: "Street Address"
        datatype: "String"
        version: "1.0"
        status: "Deployed"
        required: true

      - id: "At003"
        name: "City"
        display_name: "City"
        datatype: "String"
        version: "1.0"
        status: "Deployed"
        required: true

      - id: "At004"
        name: "State"
        display_name: "State/Province"
        datatype: "String"
        version: "1.0"
        status: "Deployed"
        required: true

      - id: "At005"
        name: "PostalCode"
        display_name: "Postal Code"
        datatype: "String"
        version: "1.0"
        status: "Deployed"
        required: true

      - id: "At006"
        name: "Country"
        display_name: "Country"
        datatype: "String"
        version: "1.0"
        status: "Deployed"
        required: true

      - id: "At007"
        name: "IsPrimary"
        display_name: "Is Primary Address"
        datatype: "Boolean"
        version: "1.0"
        status: "Deployed"
        required: true

  # Nested Entity: PhoneNumber
  - id: "E009"
    name: "PhoneNumber"
    version: "1.0"
    status: "Active"
    type: "Nested"
    parent_entities: ["E002"]  # Contact
    attributes_metadata:
      attribute_prefix: "At"
      attribute_map:
        "At001": "Type"
        "At002": "Number"
        "At003": "Extension"
        "At004": "IsPrimary"
      required_attributes: ["At001", "At002", "At004"]
    attributes:
      - id: "At001"
        name: "Type"
        display_name: "Phone Type"
        datatype: "Enum"
        version: "1.0"
        status: "Deployed"
        required: true
        values: ["Mobile", "Work", "Home", "Other"]

      - id: "At002"
        name: "Number"
        display_name: "Phone Number"
        datatype: "String"
        version: "1.0"
        status: "Deployed"
        required: true
        validations:
          - rule: "Phone format validation"
            expression: "^\\+?[0-9]{10,15}$"

      - id: "At003"
        name: "Extension"
        display_name: "Extension"
        datatype: "String"
        version: "1.0"
        status: "Deployed"
        required: false

      - id: "At004"
        name: "IsPrimary"
        display_name: "Is Primary Phone"
        datatype: "Boolean"
        version: "1.0"
        status: "Deployed"
        required: true

# Add WorkflowResults entity definition
entities:
  - id: "E012"
    name: "WorkflowResults"
    version: "1.0"
    status: "Active"
    type: "System"
    description: "Contains workflow execution results and metrics"
    attributes_metadata:
      attribute_prefix: "At"
      attribute_map:
        "At001": "ProcessedCount"
        "At002": "QualifiedCount"
        "At003": "ConvertedCount"
        "At004": "Duration"
        "At005": "StartTime"
        "At006": "EndTime"
        "At007": "Status"
      required_attributes: ["At001", "At005", "At006", "At007"]
    attributes:
      - id: "At001"
        name: "ProcessedCount"
        display_name: "Processed Count"
        datatype: "Integer"
        version: "1.0"
        status: "Deployed"
        required: true
        description: "Number of leads processed in the workflow"

      - id: "At002"
        name: "QualifiedCount"
        display_name: "Qualified Count"
        datatype: "Integer"
        version: "1.0"
        status: "Deployed"
        required: false
        description: "Number of leads qualified in the workflow"

      - id: "At003"
        name: "ConvertedCount"
        display_name: "Converted Count"
        datatype: "Integer"
        version: "1.0"
        status: "Deployed"
        required: false
        description: "Number of leads converted to opportunities"

      - id: "At004"
        name: "Duration"
        display_name: "Duration (ms)"
        datatype: "Integer"
        version: "1.0"
        status: "Deployed"
        required: false
        description: "Workflow execution duration in milliseconds"

      - id: "At005"
        name: "StartTime"
        display_name: "Start Time"
        datatype: "DateTime"
        version: "1.0"
        status: "Deployed"
        required: true
        description: "Workflow start timestamp"

      - id: "At006"
        name: "EndTime"
        display_name: "End Time"
        datatype: "DateTime"
        version: "1.0"
        status: "Deployed"
        required: true
        description: "Workflow end timestamp"

      - id: "At007"
        name: "Status"
        display_name: "Status"
        datatype: "String"
        version: "1.0"
        status: "Deployed"
        required: true
        description: "Final workflow execution status"
        values: ["Completed", "Failed", "Partial", "Cancelled"]

      - id: "At008"
        name: "ActivityType"
        display_name: "Activity Type"
        datatype: "Enum"
        version: "1.0"
        status: "Deployed"
        required: true
        values: ["Call", "Email", "Meeting", "Demo", "Note"]

      - id: "At009"
        name: "ActivityDate"
        display_name: "Activity Date"
        datatype: "DateTime"
        version: "1.0"
        status: "Deployed"
        required: true

      - id: "At010"
        name: "Description"
        display_name: "Description"
        datatype: "String"
        version: "1.0"
        status: "Deployed"
        required: true

      - id: "At011"
        name: "Outcome"
        display_name: "Activity Outcome"
        datatype: "Enum"
        version: "1.0"
        status: "Deployed"
        required: true
        values: ["Positive", "Negative", "Neutral", "No Response"]

      - id: "At012"
        name: "LeadStageChange"
        display_name: "Lead Stage Change"
        datatype: "Boolean"
        version: "1.0"
        status: "Deployed"
        required: false

      - id: "At013"
        name: "NextFollowUp"
        display_name: "Next Follow-up Date"
        datatype: "DateTime"
        version: "1.0"
        status: "Deployed"
        required: false

# Global Objectives
global_objectives:
  - id: "GO001"
    name: "Lead Management Process"
    version: "1.0"
    status: "Active"

    # Input Stack for Global Objective
    input_stack:
      description: "Defines initial data requirements for the lead management process"
      inputs:
        - id: "I001"
          slot_id: "LeadBatchData"
          contextual_id: "GO001.I001"
          entity_reference: "E011"  # LeadBatch entity
          attribute_reference: "At001"  # BatchData attribute
          source:
            type: "System"
            description: "Batch of leads imported from external system"
            data_source:
              primary:
                source_method: "FromExternalSystem"
                integration_id: "INT001"  # Integration ID for lead import service
                endpoint: "/api/leads/batch"
                method: "GET"
                parameters:
                  batchSize: 100
                  importStatus: "New"
                auth_method: "OAuth2"
                trigger:
                  type: "Scheduled"
                  frequency: "Daily"
                  time: "02:00"  # 2 AM
                target_entity: "E011"  # LeadBatch entity
                target_attribute: "At001"  # BatchData attribute
              alternatives:
                - source_method: "FileUpload"
                  allowed_formats: ["CSV", "XLSX"]
                  mapping_schema: "SCHEMA001"  # Maps uploaded file columns to system fields
                  target_entity: "E011"  # LeadBatch entity
                  target_attribute: "At001"  # BatchData attribute

                - source_method: "ManualEntry"
                  form_id: "FORM001"  # References a UI form for manual data entry
                  target_entity: "E011"  # LeadBatch entity
                  target_attribute: "At001"  # BatchData attribute

                - source_method: "WebhookEndpoint"
                  endpoint: "/api/webhook/leads"
                  security:
                    api_key_header: "X-API-Key"
                  target_entity: "E011"  # LeadBatch entity
                  target_attribute: "At001"  # BatchData attribute
          required: true
          validations:
            - rule: "Lead batch is not empty"
              rule_type: "condition"
              condition:
                operator: "not_empty"
                entity: "E011"
                attribute: "At001"
              error_message: "Lead batch cannot be empty"

        - id: "I002"
          slot_id: "CampaignId"
          contextual_id: "GO001.I002"
          source:
            type: "System"
            description: "Associated campaign ID"
            data_source:
              primary:
                source_method: "FromParameter"
                parameter_name: "campaignId"
                description: "Campaign ID passed as a parameter to the workflow"
              alternatives:
                - source_method: "AutoGenerate"
                  generation_function: "GenerateUniqueID"
                  function_parameters:
                    prefix: "CAM"
                    timestamp: true
                    random_digits: 4
                  description: "Auto-generated campaign ID when not provided"

                - source_method: "EntityLookup"
                  entity: "E005"  # Campaign entity
                  filter: "Status = 'Active' AND Type = 'Default'"
                  field: "Id"
                  description: "Uses the ID of the default active campaign"
          required: false
          auto_generate: true
          generation_rule:
            rule_type: "conditional_generation"
            condition:
              condition_type: "attribute_is_null"
              entity: "E011"
              attribute: "At002"  # CampaignId
            generation_function: "generate_unique_id"
            generation_parameters:
              prefix: "CAM"
              include_timestamp: true
              random_digits: 4

system_functions:
  - function_id: "SF012"
    function_name: "validate_data_structure"
    function_type: "validation"
    helper_functions:
      - function_name: "validate_required_fields"
        parameters:
          entity: "E001"
          fields: ["At001", "At002", "At003", "At006"]
          field_names_for_reference: ["FirstName", "LastName", "Email", "Source"]

      - function_name: "validate_field_pattern"
        parameters:
          entity: "E001"
          field: "At003"
          field_name_for_reference: "Email"
          pattern: "^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$"

      - function_name: "validate_enum_values"
        parameters:
          entity: "E001"
          field: "At006"
          field_name_for_reference: "Source"
          allowed_values: ["Website", "Referral", "Trade Show", "Cold Call", "Social Media", "Email Campaign"]

    parameters:
      entity: "E011"
      attribute: "At001"
      validation_schema:
        required_fields: ["At001", "At002", "At003", "At006"]
        field_validations:
          - field: "At003"
            validation_type: "Pattern"
            pattern: "^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$"
          - field: "At006"
            validation_type: "Enum"
            allowed_values: ["Website", "Referral", "Trade Show", "Cold Call", "Social Media", "Email Campaign"]
            output_to: "ValidationResult"

  - function_id: "SF037"
    function_name: "log_workflow_event"
    function_type: "logging"
    helper_functions:
        - function_name: "format_log_data"
          parameters:
            data:
              batch_size:
                source_type: "function"
                function_name: "count_elements"
                parameters:
                  entity: "E011"
                  attribute: "At001"
              campaign_id:
                source_type: "entity_attribute"
                entity: "E011"
                attribute: "At002"
              validation_status:
                source_type: "variable"
                variable_name: "ValidationResult"
                property: "isValid"
              validation_errors:
                source_type: "variable"
                variable_name: "ValidationResult"
                property: "errors"

  - function_name: "write_to_log"
    parameters:
    level: "INFO"
    component: "Workflow"

    parameters:
      event_type: "WorkflowStart"
      workflow_id: "GO001"
      data:
        batch_size:
          source_type: "function"
          function_name: "count_elements"
          parameters:
            entity: "E011"
            attribute: "At001"
        campaign_id:
          source_type: "entity_attribute"
          entity: "E011"
          attribute: "At002"
        validation_status:
          source_type: "variable"
          variable_name: "ValidationResult"
          property: "isValid"
        validation_errors:
          source_type: "variable"
          variable_name: "ValidationResult"
          property: "errors"

    # Output Stack for Global Objective
    output_stack:
      description: "Defines outputs from the lead management process"
      outputs:
        - id: "O001"
          slot_id: "ProcessedLeadCount"
          contextual_id: "GO001.O001"
          output_entity: "E012"
          output_attribute: "At001"
          source:
            type: "System"
            description: "Number of leads processed"
            data_source:
              source_type: "function"
              function_name: "count_entities"
              parameters:
                entity_type: "E001"
                filter_criteria:
                  attribute: "At005"
                  operator: "in"
                  values: ["New", "Contacted", "Qualified", "Unqualified", "Converted"]
                workflow_context: "GO001"
          data_type: "Integer"

        - id: "O002"
          slot_id: "QualifiedLeadCount"
          contextual_id: "GO001.O002"
          output_entity: "E012"
          output_attribute: "At002"
          source:
            type: "System"
            description: "Number of leads qualified"
            data_source:
              source_type: "function"
              function_name: "count_entities"
              parameters:
                entity_type: "E001"
                filter_criteria:
                  attribute: "At005"
                  operator: "equals"
                  value: "Qualified"
                workflow_context: "GO001"
          data_type: "Integer"

        - id: "O003"
          slot_id: "ConvertedLeadCount"
          contextual_id: "GO001.O003"
          output_entity: "E012"
          output_attribute: "At003"
          source:
            type: "System"
            description: "Number of leads converted to opportunities"
            data_source:
              source_type: "function"
              function_name: "count_entities"
              parameters:
                entity_type: "E001"
                filter_criteria:
                  attribute: "At005"
                  operator: "equals"
                  value: "Converted"
                workflow_context: "GO001"
          data_type: "Integer"

      system_functions:
        - function_id: "SF038"
          function_name: "aggregate_results"
          function_type: "data_processing"
          parameters:
            target_entity: "E012"
            data_sources:
              - source_function: "count_entities"
                source_parameters:
                  entity_type: "E001"
                  filter_criteria:
                    attribute: "At005"
                    operator: "in"
                    values: ["New", "Contacted", "Qualified", "Unqualified", "Converted"]
                  workflow_context: "GO001"
                target_attribute: "At001"

              - source_function: "count_entities"
                source_parameters:
                  entity_type: "E001"
                  filter_criteria:
                    attribute: "At005"
                    operator: "equals"
                    value: "Qualified"
                  workflow_context: "GO001"
                target_attribute: "At002"

              - source_function: "count_entities"
                source_parameters:
                  entity_type: "E001"
                  filter_criteria:
                    attribute: "At005"
                    operator: "equals"
                    value: "Converted"
                  workflow_context: "GO001"
                target_attribute: "At003"

            include_timing: true
            timing_attributes:
              start_time: "At005"
              end_time: "At006"
              duration: "At004"

        - function_id: "SF039"
          function_name: "log_workflow_completion"
          function_type: "logging"
          parameters:
            event_type: "WorkflowCompleted"
            workflow_id: "GO001"
            result_entity: "E012"
            log_attributes: ["At001", "At002", "At003", "At004"]
            include_timing: true

# Data Mapping Stack for Global Objective
data_mapping_stack:
  description: "Defines data movement between GOs in the Lead Management process"
  mappings:
    - id: "M001"
      source: "GO002.O001"
      target: "GO001.I001"
      mapping_type: "Direct"

    - id: "M002"
      source: "GO001.O001"
      target: "GO003.I001"
      mapping_type: "Transformation"
      transformation:
        function_id: "SF025"
        function_name: "transform_entities"
        parameters:
          source_entity: "E001"
          target_entity: "E010"
          filter_criteria:
            attribute: "At005"
            operator: "equals"
            value: "Qualified"
          field_mappings:
            - source_attribute: "At001"
              target_entity: "E002"
              target_attribute: "At001"
              transformation_type: "direct"

            - source_attribute: "At002"
              target_entity: "E002"
              target_attribute: "At002"
              transformation_type: "direct"

            - source_attribute: "At003"
              target_entity: "E002"
              target_attribute: "At003"
              transformation_type: "direct"

            - source_attribute: "At007"
              target_entity: "E003"
              target_attribute: "At001"
              transformation_type: "direct"

            - source_attributes: ["At001", "At002", "At007"]
              target_entity: "E010"
              target_attribute: "At002"
              transformation_type: "format"
              format_string: "{0} {1} - {2} Opportunity"

            - source_attribute: "At010"
              target_entity: "E010"
              target_attribute: "At007"
              transformation_type: "direct"

default_values:
  - target_entity: "E010"  # Opportunity entity
    target_attribute: "At004"  # Opportunity.Amount
    value: 0

  - target_entity: "E010"  # Opportunity entity
    target_attribute: "At003"  # Opportunity.Stage
    value: "New"

  - target_entity: "E010"  # Opportunity entity
    target_attribute: "At005"  # Opportunity.CloseDate
    function_name: "add_days"
    function_parameters:
      date: "current_date"
      days: 30

create_relationships:
  - source_entity: "E001"  # Lead entity
    target_entity: "E010"  # Opportunity entity
    relationship_type: "OneToOne"
    relationship_name: "ConvertedTo"

  - source_entity: "E002"  # Contact entity
    target_entity: "E010"  # Opportunity entity
    relationship_type: "ManyToOne"
    relationship_name: "PrimaryContact"

  - source_entity: "E003"  # Account entity
    target_entity: "E010"  # Opportunity entity
    relationship_type: "ManyToOne"
    relationship_name: "Account"

post_transformation_actions:
  - function_name: "update_entity_attribute"
    parameters:
      entity: "E001"  # Lead entity
      attribute: "At005"  # Status attribute
      value: "Converted"

rules:
  - id: "R001"
    description: "Lead must be qualified before conversion to opportunity"
    rule_condition:
      condition_type: "attribute_value"
      entity: "E001"  # Lead entity
      attribute: "At005"  # Status attribute
      operator: "equals"
      value: "Qualified"
    error_message: "Only qualified leads can be converted to opportunities"

  - id: "R002"
    description: "All required opportunity fields must be populated"
    rule_condition:
      condition_type: "composite"
      operator: "and"
      conditions:
        - condition_type: "attribute_not_empty"
          entity: "E001"
          attribute: "At001"  # FirstName

        - condition_type: "attribute_not_empty"
          entity: "E001"
          attribute: "At002"  # LastName

        - condition_type: "attribute_not_empty"
          entity: "E001"
          attribute: "At003"  # Email

        - condition_type: "attribute_not_empty"
          entity: "E001"
          attribute: "At007"  # Company
    error_message: "Required fields must be populated before conversion"

# Local Objectives
local_objectives:
  - id: "LO001"
    contextual_id: "GO001.LO001"
    name: "Capture Lead Information"
    function_type: "Create"
    workflow_source: "Origin"
    execution_pathway:
      type: "Sequential"
      next_lo: "LO002"  # Set Lead Status

    # Agent Stack
    agent_stack:
      agents:
        - role: "R002"  # SalesRep
          rights: ["Execute"]
        - role: "R001"  # SalesManager
          rights: ["Transact", "Information"]

    # Input Stack
    input_stack:
      description: "Defines lead capture information requirements"
      inputs:
        - id: "I001"
          slot_id: "E001.At001"  # Lead.FirstName
          contextual_id: "GO001.LO001.I001"
          source:
            type: "User"
            description: "Lead's first name"
          required: true
          validations:
            - rule: "First name is not empty"
              rule_type: "attribute_validation"
              validation_method: "not_empty"
              error_message: "First name is required"
          access_roles: ["R001", "R002"]  # SalesManager, SalesRep

        - id: "I002"
          slot_id: "E001.At002"  # Lead.LastName
          contextual_id: "GO001.LO001.I002"
          source:
            type: "User"
            description: "Lead's last name"
          required: true
          validations:
            - rule: "Last name is not empty"
              rule_type: "attribute_validation"
              validation_method: "not_empty"
              error_message: "Last name is required"
          access_roles: ["R001", "R002"]  # SalesManager, SalesRep

        - id: "I003"
          slot_id: "E001.At003"  # Lead.Email
          contextual_id: "GO001.LO001.I003"
          source:
            type: "User"
            description: "Lead's email address"
          required: true
          validations:
            - rule: "Email is valid"
              rule_type: "attribute_validation"
              validation_method: "pattern_match"
              pattern: "^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$"
              error_message: "Valid email is required"
          access_roles: ["R001", "R002"]  # SalesManager, SalesRep

        - id: "I004"
          slot_id: "E001.At006"  # Lead.Source
          contextual_id: "GO001.LO001.I004"
          source:
            type: "User"
            description: "Lead source channel"
            data_type: "Enum"
            allowed_values: ["Website", "Referral", "Trade Show", "Cold Call", "Social Media", "Email Campaign"]
          required: true
          validations:
            - rule: "Source is valid"
              rule_type: "attribute_validation"
              validation_method: "enum_values"
              allowed_values: ["Website", "Referral", "Trade Show", "Cold Call", "Social Media", "Email Campaign"]
              error_message: "Lead source is required"
          access_roles: ["R001", "R002"]  # SalesManager, SalesRep

    # Execution Rules
    execution_rules:
     - id: "ExR001"
       contextual_id: "GO001.LO001.ExR001"
       description: "When all required inputs are provided, database create operation is triggered"
       rule_condition:
          condition_type: "function_call"
          function_name: "all_inputs_valid"
          parameters:
            objective_id: "GO001.LO001"
       action:
          action_type: "function_call"
          function_name: "create_entity"
          parameters:
            entity: "E001"
            data:
              source_type: "function_call"
              function_name: "form_inputs_to_entity_data"
              parameters:
                objective_id: "GO001.LO001"
          output_attribute: "Id"
          output_variable: "Lead.Id"

     - id: "ExR002"
       contextual_id: "GO001.LO001.ExR002"
       description: "After successful creation, proceed to next LO"
       rule_condition:
          condition_type: "attribute_not_empty"
          entity: "E001"
          attribute: "Id"
          function_name: "all_inputs_valid"
          parameters:
            objective_id: "GO001.LO001"
       action:
          action_type: "transition"
          target_lo: "GO001.LO002"
          function_name: "create_entity"
          parameters:
            entity: "E001"

    # Success Message
    success_message_template:
      message_type: "entity_formatted"
      format_string: "Lead {0} {1} has been successfully created with ID: {2}"
      format_parameters:
        - entity: "E001"
          attribute: "At001"  # FirstName
        - entity: "E001"
          attribute: "At002"  # LastName
        - entity: "E001"
          attribute: "Id"

    # Output Stack
    output_stack:
      description: "Defines lead creation outputs"
      outputs:
        - id: "O001"
          slot_id: "E001.Id"
          contextual_id: "GO001.LO001.O001"
          source:
            type: "System"
            description: "System-generated lead ID"

        - id: "O002"
          slot_id: "E001.CreatedDate"
          contextual_id: "GO001.LO001.O002"
          source:
            type: "System"
            description: "Lead creation timestamp"

        - id: "O003"
          slot_id: "E001.At005"  # Lead.Status
          contextual_id: "GO001.LO001.O003"
          source:
            type: "System"
            description: "Initial lead status"
            value: "New"

    # UI Stack
    ui_stack:
      type: "Human"
      status: "Applicable"
      description: "Defines the UI components for lead capture"
      overall_control: "Form"
      form_title: "New Lead Capture"
      submit_button_text: "Create Lead"
      cancel_button_text: "Cancel"
      elements:
        - entity_attribute: "E001.At001"  # Lead.FirstName
          ui_control: "TextBox"
          helper_text: "Enter lead's first name"
          error_message: "First name is required"

        - entity_attribute: "E001.At002"  # Lead.LastName
          ui_control: "TextBox"
          helper_text: "Enter lead's last name"
          error_message: "Last name is required"

        - entity_attribute: "E001.At003"  # Lead.Email
          ui_control: "Email"
          helper_text: "Enter lead's email address"
          error_message: "Valid email is required"

        - entity_attribute: "E001.At006"  # Lead.Source
          ui_control: "Dropdown"
          helper_text: "Select lead source"
          error_message: "Lead source is required"

# LO002: Set Lead Status
# Local Objectives
  - id: "LO002"
    contextual_id: "GO001.LO002"
    name: "Set Initial Lead Status"
    workflow_source: "Origin"
    function_type: "Update"
    execution_pathway:
      type: "Sequential"
      next_lo: "LO003"  # Assign Lead

    # Agent Stack
    agent_stack:
      agents:
        - role: "System"
          rights: ["Execute"]

    # Input Stack
    input_stack:
      description: "Automatically set the initial lead status"
      inputs:
        - id: "I001"
          slot_id: "E001.Id"
          contextual_id: "GO001.LO002.I001"
          source:
            type: "System"
            description: "Lead ID from previous step"
          required: true

        - id: "I002"
          slot_id: "E001.At005"  # Lead.Status
          contextual_id: "GO001.LO002.I002"
          source:
            type: "System"
            description: "Lead status value"
            value: "New"
          required: true

    # Execution Rules
    execution_rules:
      - id: "ExR001"
        contextual_id: "GO001.LO002.ExR001"
        description: "Automatically triggered after lead creation"
        structured_rule:
          condition: "NotEmpty(${E001.Id})"
          action: "UpdateEntity('E001', ${E001.Id}, {At005: 'New'})"

      - id: "ExR002"
        contextual_id: "GO001.LO002.ExR002"
        description: "After status update, proceed to next LO"
        structured_rule:
          condition: "EntityAttributeEquals('E001', ${E001.Id}, 'At005', 'New')"
          action: "TransitionToNextLO('GO001.LO003')"

    # Success Message
    success_message: "Lead status has been set to New"

    # Output Stack
    output_stack:
      description: "Updated lead status"
      outputs:
        - id: "O001"
          slot_id: "E001.At005"  # Lead.Status
          contextual_id: "GO001.LO002.O001"
          source:
            type: "System"
            description: "Updated lead status"
            value: "New"

    # UI Stack
    ui_stack:
      type: "System"
      status: "NotApplicable"
      description: "No UI interaction needed for this step"

# Local Objectives
# LO003: Assign Lead
  - id: "LO003"
    contextual_id: "GO001.LO003"
    name: "Assign Lead to Sales Representative"
    function_type: "Update"
    workflow: "Assign lead"
    execution_pathway:
      type: "Alternative"
      conditions:
        - condition:
            condition_type: "attribute_comparison"
            entity: "E001"
            attribute: "At009"  # Lead.Score
            operator: "greater_than_or_equal"
            value: 50
          next_lo: "LO004"  # Qualify Lead
        - condition:
            condition_type: "attribute_comparison"
            entity: "E001"
            attribute: "At009"  # Lead.Score
            operator: "less_than"
            value: 50
          next_lo: "LO006"  # Nurture Lead

    # Agent Stack
    agent_stack:
      agents:
        - role: "R001"  # SalesManager
          rights: ["Transact", "Information"]
        - role: "System"
          rights: ["Execute"]

    # Input Stack
    input_stack:
      description: "Defines lead assignment information requirements"
      inputs:
        - id: "I001"
          slot_id: "E001.Id"
          contextual_id: "GO001.LO003.I001"
          source:
            type: "System"
            description: "Lead ID from previous step"
          required: true

        - id: "I002"
          slot_id: "E001.At007"  # Lead.Company
          contextual_id: "GO001.LO003.I002"
          source:
            type: "User"
            description: "Lead's company"
          required: false

        - id: "I003"
          slot_id: "E001.At008"  # Lead.Industry
          contextual_id: "GO001.LO003.I003"
          source:
            type: "User"
            description: "Lead's industry"
          required: false

    # System Functions
    system_functions:
      - "CalculateLeadScore('E001', ${E001.Id}, ['At003', 'At004', 'At007', 'At008'], 'At009')"
      - "AutoAssignLead('E001', ${E001.Id}, 'At010', {criteria: ['At009', 'At008']})"
      - "SetCurrentTimestamp('E001', ${E001.Id}, 'At011')"  # Set AssignedDate

    # Output Stack
    output_stack:
      description: "Defines lead assignment outputs"
      outputs:
        - id: "O001"
          slot_id: "E001.At009"  # Lead.Score
          contextual_id: "GO001.LO003.O001"
          source:
            type: "System"
            description: "Calculated lead score"

        - id: "O002"
          slot_id: "E001.At010"  # Lead.AssignedTo
          contextual_id: "GO001.LO003.O002"
          source:
            type: "System"
            description: "Assigned Sales Representative"
     # UI Stack
    ui_stack:
      type: "System"
      status: "NotApplicable"
      description: "No UI interaction needed for this step"
