#!/usr/bin/env python3
"""
Test script to verify that entity additional properties and attribute additional properties
are correctly parsed and deployed to the database.
"""

import os
import logging
import json
from parsers.entity_parser import parse_entities
from deployers.entity_deployer_v2 import deploy_entities, execute_query

# Set up logging
logging.basicConfig(level=logging.INFO, 
                   format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
                   handlers=[
                       logging.FileHandler('/home/<USER>/workflow-system/chat-yaml-builder/v2/entity_additional_properties.log'),
                       logging.StreamHandler()
                   ])
logger = logging.getLogger('test_entity_additional_properties')

def test_entity_additional_properties():
    """
    Test that entity additional properties and attribute additional properties
    are correctly parsed and deployed to the database.
    """
    # Create a test entity definition with additional properties
    entity_def = """
Employee has employeeId^PK, firstName, lastName, email, phoneNumber, departmentId^FK, managerId^FK, hireDate, status(Active, Inactive, OnLeave), salary, performanceRating, probationDays, minSalary.

Entity Additional Properties:
Display Name: Company Employee
Type: Core Entity
Description: Represents an employee within the organization

Attribute Additional Properties:
Attribute name: email
Key: Unique
Display Name: Email Address
Data Type: String
Type: Mandatory
Format: "<EMAIL>"
Values: N/A
Default: "@company.com"
Validation: Regex Pattern
Error Message: "Please enter a valid email address"
Description: Employee's primary email address for communications
"""
    
    # Parse the entity definition
    entities_data, warnings = parse_entities(entity_def)
    
    # Print any warnings
    if warnings:
        logger.warning("Warnings during parsing:")
        for warning in warnings:
            logger.warning(f"  - {warning}")
    
    # Check if Employee entity was parsed
    if 'entities' in entities_data and 'Employee' in entities_data['entities']:
        employee_entity = entities_data['entities']['Employee']
        
        # Check if metadata was parsed
        if 'metadata' in employee_entity:
            logger.info("\nEmployee entity metadata:")
            for key, value in employee_entity['metadata'].items():
                logger.info(f"  - {key}: {value}")
        else:
            logger.warning("No metadata found in Employee entity")
        
        # Check if email attribute metadata was parsed
        if 'attributes' in employee_entity and 'email' in employee_entity['attributes'] and 'metadata' in employee_entity['attributes']['email']:
            logger.info("\nEmail attribute metadata:")
            for key, value in employee_entity['attributes']['email']['metadata'].items():
                logger.info(f"  - {key}: {value}")
        else:
            logger.warning("No metadata found for email attribute")
    else:
        logger.error("Employee entity not found in parsed data")
        return
    
    # Deploy the entities to the database
    schema_name = 'workflow_temp'
    success, messages = deploy_entities(entities_data, schema_name)
    
    if not success:
        logger.error("Failed to deploy entities:")
        for message in messages:
            logger.error(f"  - {message}")
        return
    
    logger.info("Successfully deployed entities")
    
    # Verify the entity metadata was deployed
    success, messages, result = execute_query(
        f"""
        SELECT metadata FROM {schema_name}.entities
        WHERE name = 'Employee'
        """,
        schema_name=schema_name
    )
    
    if success and result and result[0][0]:
        metadata = result[0][0]
        logger.info("\nEmployee entity metadata in the database:")
        logger.info(f"  - {metadata}")
    else:
        logger.warning("No metadata found for Employee entity in the database")
    
    # Verify the attribute metadata was deployed
    success, messages, result = execute_query(
        f"""
        SELECT * FROM {schema_name}.entity_attribute_metadata
        WHERE entity_id = (SELECT entity_id FROM {schema_name}.entities WHERE name = 'Employee')
        AND attribute_name = 'email'
        """,
        schema_name=schema_name
    )
    
    if success and result:
        logger.info("\nEmail attribute metadata in the database:")
        for row in result:
            logger.info(f"  - {row}")
    else:
        logger.warning("No metadata found for email attribute in the database")

if __name__ == "__main__":
    test_entity_additional_properties()
