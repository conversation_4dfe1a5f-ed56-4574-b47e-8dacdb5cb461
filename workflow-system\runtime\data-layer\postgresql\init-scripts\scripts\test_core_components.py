#!/usr/bin/env python3
"""
Test core components of the workflow engine.
"""
import sys
import os
import logging
from pathlib import Path

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Make sure we can import from the app directory
project_root = Path(__file__).parent.parent.absolute()
sys.path.append(str(project_root))

def test_db_connections():
    """Test all database connections."""
    logger.info("Testing database connections...")
    
    # Test MongoDB connection
    try:
        sys.path.append(str(project_root / "runtime" / "workflow-engine"))
        from app.db.mongo_db import get_mongodb_client
        client = get_mongodb_client()
        db_names = client.list_database_names()
        logger.info(f"MongoDB connection successful. Available databases: {db_names}")
    except Exception as e:
        logger.error(f"MongoDB connection failed: {e}")
    
    # Test PostgreSQL connection
    try:
        from app.db.postgres_db import get_postgres_connection
        conn = get_postgres_connection()
        cursor = conn.cursor()
        cursor.execute("SELECT current_database();")
        db_name = cursor.fetchone()[0]
        logger.info(f"PostgreSQL connection successful. Connected to database: {db_name}")
        cursor.close()
        conn.close()
    except Exception as e:
        logger.error(f"PostgreSQL connection failed: {e}")
    
    # Test Redis connection
    try:
        from app.db.redis_db import get_redis_client
        redis_client = get_redis_client()
        ping_result = redis_client.ping()
        logger.info(f"Redis connection successful. Ping result: {ping_result}")
    except Exception as e:
        logger.error(f"Redis connection failed: {e}")

def test_model_loading():
    """Test loading workflow models."""
    logger.info("Testing workflow model loading...")
    
    try:
        from app.models.workflow import GlobalObjective, LocalObjective, ExecutionPathway, AgentStack, Agent, InputStack, OutputStack, Input, InputSource, Output
        
        # Create a simple test model
        lo = LocalObjective(
            id="LO001",
            contextual_id="GO001.LO001",
            name="Test Local Objective",
            parent_objective_id="GO001",
            function_type="test_function",
            workflow_source="Test",
            version="1.0",
            execution_pathway=ExecutionPathway(
                type="Terminal"
            ),
            agent_stack=AgentStack(
                agents=[Agent(role="System", rights=["Execute"])]
            ),
            input_stack=InputStack(
                description="Test input stack",
                inputs=[
                    Input(
                        slot_id="test_input",
                        source=InputSource(
                            type="System",
                            value="test_value"
                        ),
                        required=True
                    )
                ]
            ),
            output_stack=OutputStack(
                description="Test output stack",
                outputs=[
                    Output(
                        slot_id="test_output",
                        source="test_output_source"
                    )
                ]
            )
        )
        
        go = GlobalObjective(
            tenant_id="T001",
            objective_id="GO001",
            contextual_id="GO001",
            name="Test Global Objective",
            version="1.0",
            local_objectives=[lo]
        )
        
        logger.info(f"Successfully created workflow models. GO: {go.name}, LO: {lo.name}")
    except Exception as e:
        logger.error(f"Model loading failed: {e}")

def main():
    """Run all tests."""
    logger.info("Starting core component tests...")
    
    test_db_connections()
    test_model_loading()
    
    logger.info("Core component tests completed.")

if __name__ == "__main__":
    main()
