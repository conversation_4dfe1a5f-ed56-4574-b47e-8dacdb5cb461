#!/usr/bin/env python3
"""
Run the ID Generator Debug script to analyze the issue with attribute ID mapping.

This script executes the id_generator_debug.py file, which contains extensive logging
to help identify where the attribute ID mapping issue is occurring.
"""

import os
import sys
import subprocess
import time

def main():
    print("Running ID Generator Debug...")
    
    # Get the path to the id_generator_debug.py file
    script_dir = os.path.dirname(os.path.abspath(__file__))
    debug_script = os.path.join(script_dir, "id_generator_debug.py")
    
    # Check if the debug script exists
    if not os.path.exists(debug_script):
        print(f"Error: Debug script not found at {debug_script}")
        return 1
    
    # Run the debug script
    try:
        start_time = time.time()
        result = subprocess.run(
            [sys.executable, debug_script],
            check=True,
            text=True,
            capture_output=True
        )
        end_time = time.time()
        
        # Print the output
        print("\n" + "="*80)
        print("DEBUG SCRIPT OUTPUT:")
        print("="*80)
        print(result.stdout)
        
        if result.stderr:
            print("\n" + "="*80)
            print("DEBUG SCRIPT ERRORS:")
            print("="*80)
            print(result.stderr)
        
        print("\n" + "="*80)
        print(f"Debug script completed in {end_time - start_time:.2f} seconds")
        print("="*80)
        
        # Find the log file
        log_files = [f for f in os.listdir(script_dir) if f.startswith("id_generator_debug_") and f.endswith(".log")]
        if log_files:
            latest_log = max(log_files, key=lambda x: os.path.getmtime(os.path.join(script_dir, x)))
            log_path = os.path.join(script_dir, latest_log)
            print(f"\nDetailed debug log saved to: {log_path}")
            print("You can examine this log file to understand the ID mapping process.")
            
            # Print the last few lines of the log file to show the final state
            print("\nLast 20 lines of the debug log:")
            with open(log_path, 'r') as f:
                lines = f.readlines()
                for line in lines[-20:]:
                    print(line.strip())
            
            # Check for generate_id functions in the log
            print("\nSearching for generate_id functions in the log...")
            generate_id_lines = []
            with open(log_path, 'r') as f:
                for i, line in enumerate(f):
                    if "generate_id" in line and "nested_function" in line:
                        generate_id_lines.append((i, line.strip()))
            
            if generate_id_lines:
                print(f"\nFound {len(generate_id_lines)} references to generate_id functions:")
                for line_num, line in generate_id_lines:
                    print(f"Line {line_num}: {line}")
            else:
                print("No generate_id functions found in the log.")
            
            # Check for attribute mappings in the log
            print("\nSearching for attribute mappings in the log...")
            attribute_mapping_lines = []
            with open(log_path, 'r') as f:
                for i, line in enumerate(f):
                    if "attribute" in line and "mapping" in line.lower():
                        attribute_mapping_lines.append((i, line.strip()))
            
            if attribute_mapping_lines:
                print(f"\nFound {len(attribute_mapping_lines)} attribute mappings:")
                for line_num, line in attribute_mapping_lines[-10:]:  # Show only the last 10 mappings
                    print(f"Line {line_num}: {line}")
            else:
                print("No attribute mappings found in the log.")
            
            # Check for the enriched YAML file
            enriched_yaml_path = os.path.join("runtime", "workflow-engine", "yamls", "enriched_debug.yaml")
            if os.path.exists(enriched_yaml_path):
                print(f"\nEnriched YAML file saved to: {enriched_yaml_path}")
                print("You can examine this file to see the final state of the YAML after processing.")
            else:
                print("\nEnriched YAML file not found.")
        else:
            print("\nNo debug log files found.")
        
        return 0
    
    except subprocess.CalledProcessError as e:
        print(f"Error running debug script: {e}")
        print(f"Output: {e.stdout}")
        print(f"Error: {e.stderr}")
        return 1
    
    except Exception as e:
        print(f"Unexpected error: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
