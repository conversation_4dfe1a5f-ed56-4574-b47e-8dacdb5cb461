=== Command output log - 2025-05-13 17:45:21.915419 ===


=== Running command: python3 reset_database_direct.py ===

2025-05-13 17:45:21,947 - reset_database_direct - INFO - Resetting database schema workflow_temp
2025-05-13 17:45:21,958 - reset_database_direct - INFO - Dropped table workflow_temp.entity_attributes
2025-05-13 17:45:21,959 - reset_database_direct - INFO - Dropped table workflow_temp.entity_attribute_metadata
2025-05-13 17:45:21,960 - reset_database_direct - INFO - Dropped table workflow_temp.entity_relationships
2025-05-13 17:45:21,961 - reset_database_direct - INFO - Dropped table workflow_temp.entity_business_rules
2025-05-13 17:45:21,961 - reset_database_direct - INFO - Dropped table workflow_temp.entity_lifecycle_management
2025-05-13 17:45:21,962 - reset_database_direct - INFO - Dropped table workflow_temp.e1_employee
2025-05-13 17:45:21,963 - reset_database_direct - INFO - Dropped table workflow_temp.e2_department
2025-05-13 17:45:21,964 - reset_database_direct - INFO - Dropped table workflow_temp.e3_leavetype
2025-05-13 17:45:21,965 - reset_database_direct - INFO - Dropped table workflow_temp.e4_leaveapplication
2025-05-13 17:45:21,965 - reset_database_direct - INFO - Dropped table workflow_temp.e5_calendarevent
2025-05-13 17:45:21,976 - reset_database_direct - INFO - Truncated table workflow_temp.books
2025-05-13 17:45:21,977 - reset_database_direct - INFO - Truncated table workflow_temp.objective_permissions
2025-05-13 17:45:21,980 - reset_database_direct - INFO - Truncated table workflow_temp.input_stack
2025-05-13 17:45:21,988 - reset_database_direct - INFO - Truncated table workflow_temp.lo_input_items
2025-05-13 17:45:21,990 - reset_database_direct - INFO - Truncated table workflow_temp.lab_report
2025-05-13 17:45:21,992 - reset_database_direct - INFO - Truncated table workflow_temp.permission_types
2025-05-13 17:45:21,994 - reset_database_direct - INFO - Truncated table workflow_temp.chapters
2025-05-13 17:45:21,997 - reset_database_direct - INFO - Truncated table workflow_temp.lo_output_stack
2025-05-13 17:45:21,998 - reset_database_direct - INFO - Truncated table workflow_temp.attribute_ui_controls
2025-05-13 17:45:21,999 - reset_database_direct - INFO - Truncated table workflow_temp.organizational_units
2025-05-13 17:45:22,001 - reset_database_direct - INFO - Truncated table workflow_temp.lo_output_items
2025-05-13 17:45:22,002 - reset_database_direct - INFO - Truncated table workflow_temp.agent_stack
2025-05-13 17:45:22,003 - reset_database_direct - INFO - Truncated table workflow_temp.conditional_success_messages
2025-05-13 17:45:22,005 - reset_database_direct - INFO - Truncated table workflow_temp.prescription
2025-05-13 17:45:22,007 - reset_database_direct - INFO - Truncated table workflow_temp.lo_data_mapping_stack
2025-05-13 17:45:22,009 - reset_database_direct - INFO - Truncated table workflow_temp.agent_rights
2025-05-13 17:45:22,017 - reset_database_direct - INFO - Truncated table workflow_temp.lo_input_stack
2025-05-13 17:45:22,018 - reset_database_direct - INFO - Truncated table workflow_temp.lo_dependencies
2025-05-13 17:45:22,020 - reset_database_direct - INFO - Truncated table workflow_temp.output_stack
2025-05-13 17:45:22,024 - reset_database_direct - INFO - Truncated table workflow_temp.roles
2025-05-13 17:45:22,027 - reset_database_direct - INFO - Truncated table workflow_temp.data_mapping_stack
2025-05-13 17:45:22,028 - reset_database_direct - INFO - Truncated table workflow_temp.lo_input_execution
2025-05-13 17:45:22,029 - reset_database_direct - INFO - Truncated table workflow_temp.role
2025-05-13 17:45:22,031 - reset_database_direct - INFO - Truncated table workflow_temp.go_performance_metrics
2025-05-13 17:45:22,044 - reset_database_direct - INFO - Truncated table workflow_temp.output_items
2025-05-13 17:45:22,045 - reset_database_direct - INFO - Truncated table workflow_temp.success_messages
2025-05-13 17:45:22,047 - reset_database_direct - INFO - Truncated table workflow_temp.attribute_enum_values
2025-05-13 17:45:22,048 - reset_database_direct - INFO - Truncated table workflow_temp.lo_nested_functions
2025-05-13 17:45:22,049 - reset_database_direct - INFO - Truncated table workflow_temp.dropdown_data_sources
2025-05-13 17:45:22,051 - reset_database_direct - INFO - Truncated table workflow_temp.attribute_validations
2025-05-13 17:45:22,052 - reset_database_direct - INFO - Truncated table workflow_temp.lo_system_functions
2025-05-13 17:45:22,052 - reset_database_direct - INFO - Truncated table workflow_temp.terminal_pathways
2025-05-13 17:45:22,053 - reset_database_direct - INFO - Truncated table workflow_temp.lo_data_mappings
2025-05-13 17:45:22,054 - reset_database_direct - INFO - Truncated table workflow_temp.medical_record
2025-05-13 17:45:22,056 - reset_database_direct - INFO - Truncated table workflow_temp.patient
2025-05-13 17:45:22,088 - reset_database_direct - INFO - Truncated table workflow_temp.tenants
2025-05-13 17:45:22,091 - reset_database_direct - INFO - Truncated table workflow_temp.ui_stack
2025-05-13 17:45:22,092 - reset_database_direct - INFO - Truncated table workflow_temp.lo_input_validations
2025-05-13 17:45:22,092 - reset_database_direct - INFO - Truncated table workflow_temp.user_organizations
2025-05-13 17:45:22,122 - reset_database_direct - INFO - Truncated table workflow_temp.global_objectives
2025-05-13 17:45:22,123 - reset_database_direct - INFO - Truncated table workflow_temp.permission_capabilities
2025-05-13 17:45:22,124 - reset_database_direct - INFO - Truncated table workflow_temp.user
2025-05-13 17:45:22,125 - reset_database_direct - INFO - Truncated table workflow_temp.user_role
2025-05-13 17:45:22,126 - reset_database_direct - INFO - Truncated table workflow_temp.role_inheritance
2025-05-13 17:45:22,129 - reset_database_direct - INFO - Truncated table workflow_temp.workflow_instances
2025-05-13 17:45:22,131 - reset_database_direct - INFO - Truncated table workflow_temp.calculated_fields
2025-05-13 17:45:22,132 - reset_database_direct - INFO - Truncated table workflow_temp.data_mappings
2025-05-13 17:45:22,133 - reset_database_direct - INFO - Truncated table workflow_temp.entities
2025-05-13 17:45:22,135 - reset_database_direct - INFO - Truncated table workflow_temp.mapping_rules
2025-05-13 17:45:22,136 - reset_database_direct - INFO - Truncated table workflow_temp.output_triggers
2025-05-13 17:45:22,136 - reset_database_direct - INFO - Truncated table workflow_temp.go_lo_mapping
2025-05-13 17:45:22,138 - reset_database_direct - INFO - Truncated table workflow_temp.lo_output_execution
2025-05-13 17:45:22,138 - reset_database_direct - INFO - Truncated table workflow_temp.metrics_aggregation
2025-05-13 17:45:22,139 - reset_database_direct - INFO - Truncated table workflow_temp.metrics_reporting
2025-05-13 17:45:22,140 - reset_database_direct - INFO - Truncated table workflow_temp.user_roles
2025-05-13 17:45:22,161 - reset_database_direct - INFO - Truncated table workflow_temp.local_objectives
2025-05-13 17:45:22,163 - reset_database_direct - INFO - Truncated table workflow_temp.input_data_sources
2025-05-13 17:45:22,164 - reset_database_direct - INFO - Truncated table workflow_temp.input_dependencies
2025-05-13 17:45:22,165 - reset_database_direct - INFO - Truncated table workflow_temp.system_functions
2025-05-13 17:45:22,167 - reset_database_direct - INFO - Truncated table workflow_temp.permission_contexts
2025-05-13 17:45:22,168 - reset_database_direct - INFO - Truncated table workflow_temp.workflow_results
2025-05-13 17:45:22,172 - reset_database_direct - INFO - Truncated table workflow_temp.users
2025-05-13 17:45:22,173 - reset_database_direct - INFO - Truncated table workflow_temp.user_oauth_tokens
2025-05-13 17:45:22,175 - reset_database_direct - INFO - Truncated table workflow_temp.runtime_metrics_stack
2025-05-13 17:45:22,178 - reset_database_direct - INFO - Truncated table workflow_temp.workflow_transaction
2025-05-13 17:45:22,182 - reset_database_direct - INFO - Truncated table workflow_temp.role_permissions
2025-05-13 17:45:22,185 - reset_database_direct - INFO - Truncated table workflow_temp.user_sessions
2025-05-13 17:45:22,186 - reset_database_direct - INFO - Truncated table workflow_temp.input_items
2025-05-13 17:45:22,191 - reset_database_direct - INFO - Truncated table workflow_temp.lo_output_triggers
2025-05-13 17:45:22,193 - reset_database_direct - INFO - Truncated table workflow_temp.ui_elements
2025-05-13 17:45:22,193 - reset_database_direct - INFO - Successfully reset database schema workflow_temp
Dropped table workflow_temp.entity_attributes
Dropped table workflow_temp.entity_attribute_metadata
Dropped table workflow_temp.entity_relationships
Dropped table workflow_temp.entity_business_rules
Dropped table workflow_temp.entity_lifecycle_management
Dropped table workflow_temp.e1_employee
Dropped table workflow_temp.e2_department
Dropped table workflow_temp.e3_leavetype
Dropped table workflow_temp.e4_leaveapplication
Dropped table workflow_temp.e5_calendarevent
Truncated table workflow_temp.books
Truncated table workflow_temp.objective_permissions
Truncated table workflow_temp.input_stack
Truncated table workflow_temp.lo_input_items
Truncated table workflow_temp.lab_report
Truncated table workflow_temp.permission_types
Truncated table workflow_temp.chapters
Truncated table workflow_temp.lo_output_stack
Truncated table workflow_temp.attribute_ui_controls
Truncated table workflow_temp.organizational_units
Truncated table workflow_temp.lo_output_items
Truncated table workflow_temp.agent_stack
Truncated table workflow_temp.conditional_success_messages
Truncated table workflow_temp.prescription
Truncated table workflow_temp.lo_data_mapping_stack
Truncated table workflow_temp.agent_rights
Truncated table workflow_temp.lo_input_stack
Truncated table workflow_temp.lo_dependencies
Truncated table workflow_temp.output_stack
Truncated table workflow_temp.roles
Truncated table workflow_temp.data_mapping_stack
Truncated table workflow_temp.lo_input_execution
Truncated table workflow_temp.role
Truncated table workflow_temp.go_performance_metrics
Truncated table workflow_temp.output_items
Truncated table workflow_temp.success_messages
Truncated table workflow_temp.attribute_enum_values
Truncated table workflow_temp.lo_nested_functions
Truncated table workflow_temp.dropdown_data_sources
Truncated table workflow_temp.attribute_validations
Truncated table workflow_temp.lo_system_functions
Truncated table workflow_temp.terminal_pathways
Truncated table workflow_temp.lo_data_mappings
Truncated table workflow_temp.medical_record
Truncated table workflow_temp.patient
Truncated table workflow_temp.tenants
Truncated table workflow_temp.ui_stack
Truncated table workflow_temp.lo_input_validations
Truncated table workflow_temp.user_organizations
Truncated table workflow_temp.global_objectives
Truncated table workflow_temp.permission_capabilities
Truncated table workflow_temp.user
Truncated table workflow_temp.user_role
Truncated table workflow_temp.role_inheritance
Truncated table workflow_temp.workflow_instances
Truncated table workflow_temp.calculated_fields
Truncated table workflow_temp.data_mappings
Truncated table workflow_temp.entities
Truncated table workflow_temp.mapping_rules
Truncated table workflow_temp.output_triggers
Truncated table workflow_temp.go_lo_mapping
Truncated table workflow_temp.lo_output_execution
Truncated table workflow_temp.metrics_aggregation
Truncated table workflow_temp.metrics_reporting
Truncated table workflow_temp.user_roles
Truncated table workflow_temp.local_objectives
Truncated table workflow_temp.input_data_sources
Truncated table workflow_temp.input_dependencies
Truncated table workflow_temp.system_functions
Truncated table workflow_temp.permission_contexts
Truncated table workflow_temp.workflow_results
Truncated table workflow_temp.users
Truncated table workflow_temp.user_oauth_tokens
Truncated table workflow_temp.runtime_metrics_stack
Truncated table workflow_temp.workflow_transaction
Truncated table workflow_temp.role_permissions
Truncated table workflow_temp.user_sessions
Truncated table workflow_temp.input_items
Truncated table workflow_temp.lo_output_triggers
Truncated table workflow_temp.ui_elements


=== Command completed with exit code: 0 ===



=== Running command: python3 init_database.py ===

2025-05-13 17:45:22,231 - init_database - INFO - Initializing database schema workflow_temp
2025-05-13 17:45:22,236 - init_database - INFO - Created schema workflow_temp
2025-05-13 17:45:22,242 - init_database - INFO - Created table workflow_temp.entities
2025-05-13 17:45:22,264 - init_database - INFO - Created table workflow_temp.entity_attributes
2025-05-13 17:45:22,266 - init_database - INFO - Created table workflow_temp.entity_attribute_metadata
2025-05-13 17:45:22,268 - init_database - INFO - Created table workflow_temp.entity_relationships
2025-05-13 17:45:22,271 - init_database - INFO - Created table workflow_temp.entity_business_rules
2025-05-13 17:45:22,271 - init_database - INFO - Created table workflow_temp.calculated_fields
2025-05-13 17:45:22,287 - init_database - INFO - Created table workflow_temp.entity_lifecycle_management
2025-05-13 17:45:22,288 - init_database - INFO - Created table workflow_temp.attribute_enum_values
2025-05-13 17:45:22,288 - init_database - INFO - Created table workflow_temp.attribute_validations
2025-05-13 17:45:22,288 - init_database - INFO - Created table workflow_temp.global_objectives
2025-05-13 17:45:22,288 - init_database - INFO - Created table workflow_temp.tenants
2025-05-13 17:45:22,288 - init_database - INFO - Created table workflow_temp.books
2025-05-13 17:45:22,288 - init_database - INFO - Created table workflow_temp.chapters
2025-05-13 17:45:22,288 - init_database - INFO - Created table workflow_temp.local_objectives
2025-05-13 17:45:22,288 - init_database - INFO - Created table workflow_temp.go_lo_mapping
2025-05-13 17:45:22,288 - init_database - INFO - Created table workflow_temp.output_stack
2025-05-13 17:45:22,289 - init_database - INFO - Created table workflow_temp.output_items
2025-05-13 17:45:22,289 - init_database - INFO - Created table workflow_temp.output_triggers
2025-05-13 17:45:22,289 - init_database - INFO - Successfully initialized database schema workflow_temp
Created schema workflow_temp
Created table workflow_temp.entities
Created table workflow_temp.entity_attributes
Created table workflow_temp.entity_attribute_metadata
Created table workflow_temp.entity_relationships
Created table workflow_temp.entity_business_rules
Created table workflow_temp.calculated_fields
Created table workflow_temp.entity_lifecycle_management
Created table workflow_temp.attribute_enum_values
Created table workflow_temp.attribute_validations
Created table workflow_temp.global_objectives
Created table workflow_temp.tenants
Created table workflow_temp.books
Created table workflow_temp.chapters
Created table workflow_temp.local_objectives
Created table workflow_temp.go_lo_mapping
Created table workflow_temp.output_stack
Created table workflow_temp.output_items
Created table workflow_temp.output_triggers


=== Command completed with exit code: 0 ===



=== Running command: python3 deploy_entities_and_go.py ===

2025-05-13 17:45:22,386 - deploy_entities_and_go - INFO - Starting entity and GO deployment
2025-05-13 17:45:22,386 - deploy_entities_and_go - INFO - Deploying entity definitions
2025-05-13 17:45:22,386 - entity_parser - INFO - Starting to parse entity definitions
2025-05-13 17:45:22,386 - entity_parser - INFO - Parsing entity: Employee
2025-05-13 17:45:22,386 - entity_parser - INFO - Found enum values for attribute 'status': ['Active', 'Inactive', 'OnLeave']
2025-05-13 17:45:22,387 - entity_parser - INFO - Successfully parsed entity 'Employee'
2025-05-13 17:45:22,387 - entity_parser - INFO - Parsing entity: Employee
2025-05-13 17:45:22,387 - entity_parser - INFO - Successfully parsed entity 'Employee'
2025-05-13 17:45:22,387 - entity_parser - INFO - Parsing entity: Employee
2025-05-13 17:45:22,387 - entity_parser - WARNING - Attribute 'email' specified in Additional Properties not found in entity 'Employee'
2025-05-13 17:45:22,387 - entity_parser - INFO - Successfully parsed entity 'Employee'
2025-05-13 17:45:22,387 - entity_parser - INFO - Parsing entity: Employee
2025-05-13 17:45:22,387 - entity_parser - INFO - Successfully parsed entity 'Employee'
2025-05-13 17:45:22,387 - entity_parser - INFO - Parsing entity: Employee
2025-05-13 17:45:22,388 - entity_parser - INFO - Successfully parsed entity 'Employee'
2025-05-13 17:45:22,388 - entity_parser - INFO - Parsing entity: Department
2025-05-13 17:45:22,388 - entity_parser - INFO - Successfully parsed entity 'Department'
2025-05-13 17:45:22,388 - entity_parser - INFO - Parsing entity: Department
2025-05-13 17:45:22,388 - entity_parser - INFO - Successfully parsed entity 'Department'
2025-05-13 17:45:22,388 - entity_parser - INFO - Parsing entity: Department
2025-05-13 17:45:22,388 - entity_parser - INFO - Successfully parsed entity 'Department'
2025-05-13 17:45:22,388 - entity_parser - INFO - Parsing entity: LeaveType
2025-05-13 17:45:22,388 - entity_parser - INFO - Successfully parsed entity 'LeaveType'
2025-05-13 17:45:22,388 - entity_parser - INFO - Parsing entity: LeaveType
2025-05-13 17:45:22,388 - entity_parser - INFO - Successfully parsed entity 'LeaveType'
2025-05-13 17:45:22,388 - entity_parser - INFO - Parsing entity: LeaveType
2025-05-13 17:45:22,388 - entity_parser - INFO - Successfully parsed entity 'LeaveType'
2025-05-13 17:45:22,388 - entity_parser - INFO - Parsing entity: LeaveType
2025-05-13 17:45:22,388 - entity_parser - INFO - Successfully parsed entity 'LeaveType'
2025-05-13 17:45:22,388 - entity_parser - INFO - Parsing entity: LeaveApplication
2025-05-13 17:45:22,388 - entity_parser - INFO - Successfully parsed entity 'LeaveApplication'
2025-05-13 17:45:22,388 - entity_parser - INFO - Parsing entity: LeaveApplication
2025-05-13 17:45:22,388 - entity_parser - INFO - Successfully parsed entity 'LeaveApplication'
2025-05-13 17:45:22,388 - entity_parser - INFO - Parsing entity: LeaveApplication
2025-05-13 17:45:22,388 - entity_parser - INFO - Successfully parsed entity 'LeaveApplication'
2025-05-13 17:45:22,388 - entity_parser - INFO - Parsing entity: CalendarEvent
2025-05-13 17:45:22,388 - entity_parser - INFO - Successfully parsed entity 'CalendarEvent'
2025-05-13 17:45:22,388 - entity_parser - INFO - Parsing entity: CalendarEvent
2025-05-13 17:45:22,388 - entity_parser - INFO - Successfully parsed entity 'CalendarEvent'
2025-05-13 17:45:22,388 - entity_parser - INFO - Parsing entity: CalendarEvent
2025-05-13 17:45:22,388 - entity_parser - INFO - Successfully parsed entity 'CalendarEvent'
2025-05-13 17:45:22,388 - entity_parser - INFO - Successfully parsed 5 entities
2025-05-13 17:45:22,388 - deploy_entities_and_go - INFO - Successfully parsed 5 entities
2025-05-13 17:45:22,388 - deploy_entities_and_go - INFO - - Employee
2025-05-13 17:45:22,388 - deploy_entities_and_go - INFO - - Department
2025-05-13 17:45:22,388 - deploy_entities_and_go - INFO - - LeaveType
2025-05-13 17:45:22,388 - deploy_entities_and_go - INFO - - LeaveApplication
2025-05-13 17:45:22,388 - deploy_entities_and_go - INFO - - CalendarEvent
2025-05-13 17:45:22,388 - entity_deployer_v2 - INFO - Deploying entities to workflow_temp
2025-05-13 17:45:22,405 - entity_deployer_v2 - INFO - Inserted entity 'Employee' into workflow_temp.entities
2025-05-13 17:45:22,438 - entity_deployer_v2 - INFO - Inserted attribute 'employeeId' for entity 'E1' into workflow_temp.entity_attributes
2025-05-13 17:45:22,443 - entity_deployer_v2 - INFO - Inserted attribute metadata for 'employeeId' into workflow_temp.entity_attribute_metadata
2025-05-13 17:45:22,477 - entity_deployer_v2 - INFO - Inserted attribute 'firstName' for entity 'E1' into workflow_temp.entity_attributes
2025-05-13 17:45:22,482 - entity_deployer_v2 - INFO - Inserted attribute metadata for 'firstName' into workflow_temp.entity_attribute_metadata
2025-05-13 17:45:22,516 - entity_deployer_v2 - INFO - Inserted attribute 'lastName' for entity 'E1' into workflow_temp.entity_attributes
2025-05-13 17:45:22,522 - entity_deployer_v2 - INFO - Inserted attribute metadata for 'lastName' into workflow_temp.entity_attribute_metadata
2025-05-13 17:45:22,557 - entity_deployer_v2 - INFO - Inserted attribute 'email' for entity 'E1' into workflow_temp.entity_attributes
2025-05-13 17:45:22,563 - entity_deployer_v2 - INFO - Inserted attribute metadata for 'email' into workflow_temp.entity_attribute_metadata
2025-05-13 17:45:22,572 - entity_deployer_v2 - INFO - Inserted archive strategy for entity 'E1' into workflow_temp.entity_lifecycle_management
2025-05-13 17:45:22,581 - entity_deployer_v2 - INFO - Inserted history tracking for entity 'E1' into workflow_temp.entity_lifecycle_management
2025-05-13 17:45:22,594 - entity_deployer_v2 - INFO - Created table workflow_temp.e1_employee
2025-05-13 17:45:22,610 - entity_deployer_v2 - INFO - Inserted entity 'Department' into workflow_temp.entities
2025-05-13 17:45:22,644 - entity_deployer_v2 - INFO - Inserted attribute 'managerId' for entity 'E2' into workflow_temp.entity_attributes
2025-05-13 17:45:22,649 - entity_deployer_v2 - INFO - Inserted attribute metadata for 'managerId' into workflow_temp.entity_attribute_metadata
2025-05-13 17:45:22,682 - entity_deployer_v2 - INFO - Inserted attribute 'locationId with Employee constrains Location' for entity 'E2' into workflow_temp.entity_attributes
2025-05-13 17:45:22,686 - entity_deployer_v2 - INFO - Inserted attribute metadata for 'locationId with Employee constrains Location' into workflow_temp.entity_attribute_metadata
2025-05-13 17:45:22,695 - entity_deployer_v2 - INFO - Inserted archive strategy for entity 'E2' into workflow_temp.entity_lifecycle_management
2025-05-13 17:45:22,705 - entity_deployer_v2 - INFO - Inserted history tracking for entity 'E2' into workflow_temp.entity_lifecycle_management
2025-05-13 17:45:22,718 - entity_deployer_v2 - INFO - Created table workflow_temp.e2_department
2025-05-13 17:45:22,733 - entity_deployer_v2 - INFO - Inserted entity 'LeaveType' into workflow_temp.entities
2025-05-13 17:45:22,770 - entity_deployer_v2 - INFO - Inserted attribute 'typeId' for entity 'E3' into workflow_temp.entity_attributes
2025-05-13 17:45:22,775 - entity_deployer_v2 - INFO - Inserted attribute metadata for 'typeId' into workflow_temp.entity_attribute_metadata
2025-05-13 17:45:22,812 - entity_deployer_v2 - INFO - Inserted attribute 'name' for entity 'E3' into workflow_temp.entity_attributes
2025-05-13 17:45:22,817 - entity_deployer_v2 - INFO - Inserted attribute metadata for 'name' into workflow_temp.entity_attribute_metadata
2025-05-13 17:45:22,855 - entity_deployer_v2 - INFO - Inserted attribute 'description' for entity 'E3' into workflow_temp.entity_attributes
2025-05-13 17:45:22,860 - entity_deployer_v2 - INFO - Inserted attribute metadata for 'description' into workflow_temp.entity_attribute_metadata
2025-05-13 17:45:22,897 - entity_deployer_v2 - INFO - Inserted attribute 'maxDuration' for entity 'E3' into workflow_temp.entity_attributes
2025-05-13 17:45:22,902 - entity_deployer_v2 - INFO - Inserted attribute metadata for 'maxDuration' into workflow_temp.entity_attribute_metadata
2025-05-13 17:45:22,939 - entity_deployer_v2 - INFO - Inserted attribute 'requiresDocumentation' for entity 'E3' into workflow_temp.entity_attributes
2025-05-13 17:45:22,945 - entity_deployer_v2 - INFO - Inserted attribute metadata for 'requiresDocumentation' into workflow_temp.entity_attribute_metadata
2025-05-13 17:45:22,956 - entity_deployer_v2 - INFO - Created table workflow_temp.e3_leavetype
2025-05-13 17:45:22,979 - entity_deployer_v2 - INFO - Inserted entity 'LeaveApplication' into workflow_temp.entities
2025-05-13 17:45:23,026 - entity_deployer_v2 - INFO - Inserted attribute 'leaveId' for entity 'E4' into workflow_temp.entity_attributes
2025-05-13 17:45:23,034 - entity_deployer_v2 - INFO - Inserted attribute metadata for 'leaveId' into workflow_temp.entity_attribute_metadata
2025-05-13 17:45:23,081 - entity_deployer_v2 - INFO - Inserted attribute 'employeeId' for entity 'E4' into workflow_temp.entity_attributes
2025-05-13 17:45:23,088 - entity_deployer_v2 - INFO - Inserted attribute metadata for 'employeeId' into workflow_temp.entity_attribute_metadata
2025-05-13 17:45:23,134 - entity_deployer_v2 - INFO - Inserted attribute 'leaveTypeId' for entity 'E4' into workflow_temp.entity_attributes
2025-05-13 17:45:23,142 - entity_deployer_v2 - INFO - Inserted attribute metadata for 'leaveTypeId' into workflow_temp.entity_attribute_metadata
2025-05-13 17:45:23,185 - entity_deployer_v2 - INFO - Inserted attribute 'startDate' for entity 'E4' into workflow_temp.entity_attributes
2025-05-13 17:45:23,193 - entity_deployer_v2 - INFO - Inserted attribute metadata for 'startDate' into workflow_temp.entity_attribute_metadata
2025-05-13 17:45:23,234 - entity_deployer_v2 - INFO - Inserted attribute 'endDate' for entity 'E4' into workflow_temp.entity_attributes
2025-05-13 17:45:23,242 - entity_deployer_v2 - INFO - Inserted attribute metadata for 'endDate' into workflow_temp.entity_attribute_metadata
2025-05-13 17:45:23,287 - entity_deployer_v2 - INFO - Inserted attribute 'numDays' for entity 'E4' into workflow_temp.entity_attributes
2025-05-13 17:45:23,294 - entity_deployer_v2 - INFO - Inserted attribute metadata for 'numDays' into workflow_temp.entity_attribute_metadata
2025-05-13 17:45:23,339 - entity_deployer_v2 - INFO - Inserted attribute 'status' for entity 'E4' into workflow_temp.entity_attributes
2025-05-13 17:45:23,346 - entity_deployer_v2 - INFO - Inserted attribute metadata for 'status' into workflow_temp.entity_attribute_metadata
2025-05-13 17:45:23,391 - entity_deployer_v2 - INFO - Inserted attribute 'approvedBy' for entity 'E4' into workflow_temp.entity_attributes
2025-05-13 17:45:23,399 - entity_deployer_v2 - INFO - Inserted attribute metadata for 'approvedBy' into workflow_temp.entity_attribute_metadata
2025-05-13 17:45:23,443 - entity_deployer_v2 - INFO - Inserted attribute 'approvalDate' for entity 'E4' into workflow_temp.entity_attributes
2025-05-13 17:45:23,451 - entity_deployer_v2 - INFO - Inserted attribute metadata for 'approvalDate' into workflow_temp.entity_attribute_metadata
2025-05-13 17:45:23,498 - entity_deployer_v2 - INFO - Inserted attribute 'comments' for entity 'E4' into workflow_temp.entity_attributes
2025-05-13 17:45:23,506 - entity_deployer_v2 - INFO - Inserted attribute metadata for 'comments' into workflow_temp.entity_attribute_metadata
2025-05-13 17:45:23,523 - entity_deployer_v2 - INFO - Created table workflow_temp.e4_leaveapplication
2025-05-13 17:45:23,545 - entity_deployer_v2 - INFO - Inserted entity 'CalendarEvent' into workflow_temp.entities
2025-05-13 17:45:23,587 - entity_deployer_v2 - INFO - Inserted attribute 'eventId' for entity 'E5' into workflow_temp.entity_attributes
2025-05-13 17:45:23,595 - entity_deployer_v2 - INFO - Inserted attribute metadata for 'eventId' into workflow_temp.entity_attribute_metadata
2025-05-13 17:45:23,638 - entity_deployer_v2 - INFO - Inserted attribute 'employeeId' for entity 'E5' into workflow_temp.entity_attributes
2025-05-13 17:45:23,646 - entity_deployer_v2 - INFO - Inserted attribute metadata for 'employeeId' into workflow_temp.entity_attribute_metadata
2025-05-13 17:45:23,689 - entity_deployer_v2 - INFO - Inserted attribute 'eventType' for entity 'E5' into workflow_temp.entity_attributes
2025-05-13 17:45:23,696 - entity_deployer_v2 - INFO - Inserted attribute metadata for 'eventType' into workflow_temp.entity_attribute_metadata
2025-05-13 17:45:23,736 - entity_deployer_v2 - INFO - Inserted attribute 'startDate' for entity 'E5' into workflow_temp.entity_attributes
2025-05-13 17:45:23,743 - entity_deployer_v2 - INFO - Inserted attribute metadata for 'startDate' into workflow_temp.entity_attribute_metadata
2025-05-13 17:45:23,785 - entity_deployer_v2 - INFO - Inserted attribute 'endDate' for entity 'E5' into workflow_temp.entity_attributes
2025-05-13 17:45:23,792 - entity_deployer_v2 - INFO - Inserted attribute metadata for 'endDate' into workflow_temp.entity_attribute_metadata
2025-05-13 17:45:23,835 - entity_deployer_v2 - INFO - Inserted attribute 'title' for entity 'E5' into workflow_temp.entity_attributes
2025-05-13 17:45:23,842 - entity_deployer_v2 - INFO - Inserted attribute metadata for 'title' into workflow_temp.entity_attribute_metadata
2025-05-13 17:45:23,886 - entity_deployer_v2 - INFO - Inserted attribute 'description' for entity 'E5' into workflow_temp.entity_attributes
2025-05-13 17:45:23,893 - entity_deployer_v2 - INFO - Inserted attribute metadata for 'description' into workflow_temp.entity_attribute_metadata
2025-05-13 17:45:23,937 - entity_deployer_v2 - INFO - Inserted attribute 'status' for entity 'E5' into workflow_temp.entity_attributes
2025-05-13 17:45:23,944 - entity_deployer_v2 - INFO - Inserted attribute metadata for 'status' into workflow_temp.entity_attribute_metadata
2025-05-13 17:45:23,991 - entity_deployer_v2 - INFO - Inserted attribute 'referenceId' for entity 'E5' into workflow_temp.entity_attributes
2025-05-13 17:45:23,998 - entity_deployer_v2 - INFO - Inserted attribute metadata for 'referenceId' into workflow_temp.entity_attribute_metadata
2025-05-13 17:45:24,017 - entity_deployer_v2 - INFO - Created table workflow_temp.e5_calendarevent
2025-05-13 17:45:24,025 - entity_deployer_v2 - INFO - Entity deployment completed successfully
2025-05-13 17:45:24,025 - deploy_entities_and_go - INFO - Successfully deployed entity definitions to workflow_temp
2025-05-13 17:45:24,025 - deploy_entities_and_go - INFO - Validating and deploying GO definitions
2025-05-13 17:45:24,025 - go_parser - INFO - Starting to parse GO definitions
2025-05-13 17:45:24,026 - go_parser - INFO - Parsing GO: Leave Approval Process (ID: go001)
2025-05-13 17:45:24,032 - go_parser - INFO - Successfully parsed GO 'Leave Approval Process'
2025-05-13 17:45:24,033 - go_parser - WARNING - Invalid GO header format: GO Relationships:
2025-05-13 17:45:24,033 - go_parser - INFO - Parsing GO: Employee Calendar Management (ID: go004)
2025-05-13 17:45:24,033 - go_parser - INFO - Successfully parsed GO 'Employee Calendar Management'
2025-05-13 17:45:24,033 - go_parser - WARNING - Invalid GO header format: GO Relationships:
2025-05-13 17:45:24,033 - go_parser - INFO - Successfully parsed 2 global objectives
2025-05-13 17:45:24,033 - deploy_entities_and_go - INFO - Successfully parsed 2 global objectives
2025-05-13 17:45:24,033 - deploy_entities_and_go - INFO - - Leave Approval Process
2025-05-13 17:45:24,033 - deploy_entities_and_go - INFO - - Employee Calendar Management
2025-05-13 17:45:24,033 - deploy_entities_and_go - WARNING - GO parsing completed with 2 warnings
2025-05-13 17:45:24,033 - deploy_entities_and_go - WARNING - - Invalid GO header format: GO Relationships:
2025-05-13 17:45:24,033 - deploy_entities_and_go - WARNING - - Invalid GO header format: GO Relationships:
2025-05-13 17:45:24,082 - deploy_entities_and_go - INFO - Registry validation passed
2025-05-13 17:45:24,082 - go_deployer - INFO - Deploying GO definitions to workflow_temp
2025-05-13 17:45:24,124 - go_deployer - WARNING - Attribute 'departmentId' of entity 'Employee' referenced in GO does not exist in the database
2025-05-13 17:45:24,129 - go_deployer - WARNING - Attribute 'managerId' of entity 'Employee' referenced in GO does not exist in the database
2025-05-13 17:45:24,155 - go_deployer - WARNING - Attribute 'false' of entity 'LeaveType' referenced in GO does not exist in the database
2025-05-13 17:45:24,190 - go_deployer - WARNING - Attribute 'Approved' of entity 'LeaveApplication' referenced in GO does not exist in the database
2025-05-13 17:45:24,194 - go_deployer - WARNING - Attribute 'Rejected' of entity 'LeaveApplication' referenced in GO does not exist in the database
2025-05-13 17:45:24,223 - go_deployer - INFO - Inserted GO 'Leave Approval Process' into workflow_temp.global_objectives
2025-05-13 17:45:24,236 - go_deployer - WARNING - Warning: LO 'Employee submits a leave request' (ID: SubmitLeaveRequest) referenced in GO 'go001' does not exist in the database. Skipping mapping.
2025-05-13 17:45:24,241 - go_deployer - WARNING - Warning: LO 'Employee uploads required documentation' (ID: UploadDocumentation) referenced in GO 'go001' does not exist in the database. Skipping mapping.
2025-05-13 17:45:24,245 - go_deployer - WARNING - Warning: LO 'Manager reviews the leave request' (ID: ReviewLeaveRequest) referenced in GO 'go001' does not exist in the database. Skipping mapping.
2025-05-13 17:45:24,249 - go_deployer - WARNING - Warning: LO 'System updates leave request status to approved' (ID: ApproveLeaveRequest) referenced in GO 'go001' does not exist in the database. Skipping mapping.
2025-05-13 17:45:24,253 - go_deployer - WARNING - Warning: LO 'System updates leave request status to rejected' (ID: RejectLeaveRequest) referenced in GO 'go001' does not exist in the database. Skipping mapping.
2025-05-13 17:45:24,257 - go_deployer - WARNING - Warning: LO 'System notifies employee of the decision' (ID: NotifyEmployee) referenced in GO 'go001' does not exist in the database. Skipping mapping.
2025-05-13 17:45:24,262 - go_deployer - WARNING - Warning: LO 'System updates employee calendar' (ID: UpdateCalendar) referenced in GO 'go001' does not exist in the database. Skipping mapping.
2025-05-13 17:45:24,267 - go_deployer - WARNING - Warning: LO 'System updates employee leave balance' (ID: UpdateLeaveBalance) referenced in GO 'go001' does not exist in the database. Skipping mapping.
2025-05-13 17:45:24,271 - go_deployer - WARNING - Warning: LO 'Employee cancels a previously submitted leave request' (ID: CancelLeaveRequest) referenced in GO 'go001' does not exist in the database. Skipping mapping.
2025-05-13 17:45:24,275 - go_deployer - WARNING - Warning: LO 'System rolls back leave approval' (ID: RollbackLeaveApproval) referenced in GO 'go001' does not exist in the database. Skipping mapping.
2025-05-13 17:45:24,279 - go_deployer - WARNING - Warning: LO 'System restores employee leave balance' (ID: RestoreLeaveBalance) referenced in GO 'go001' does not exist in the database. Skipping mapping.
2025-05-13 17:45:24,284 - go_deployer - WARNING - Warning: LO 'System notifies manager of cancellation' (ID: NotifyCancellation) referenced in GO 'go001' does not exist in the database. Skipping mapping.
2025-05-13 17:45:24,288 - go_deployer - WARNING - Warning: LO 'System logs audit trail for compliance' (ID: LogAuditTrail) referenced in GO 'go001' does not exist in the database. Skipping mapping.
2025-05-13 17:45:24,305 - db_utils - ERROR - Error executing query: column "display_name" of relation "output_items" does not exist
LINE 3: ...tput_stack_id, slot_id, contextual_id, data_type, display_na...
                                                             ^
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 83, in execute_query
    cursor.execute(query, params)
psycopg2.errors.UndefinedColumn: column "display_name" of relation "output_items" does not exist
LINE 3: ...tput_stack_id, slot_id, contextual_id, data_type, display_na...
                                                             ^

2025-05-13 17:45:24,305 - deploy_entities_and_go - ERROR - GO deployment failed with 21 messages
2025-05-13 17:45:24,305 - deploy_entities_and_go - ERROR - - Attribute 'departmentId' of entity 'Employee' referenced in GO does not exist in the database
2025-05-13 17:45:24,305 - deploy_entities_and_go - ERROR - - Attribute 'managerId' of entity 'Employee' referenced in GO does not exist in the database
2025-05-13 17:45:24,305 - deploy_entities_and_go - ERROR - - Attribute 'false' of entity 'LeaveType' referenced in GO does not exist in the database
2025-05-13 17:45:24,305 - deploy_entities_and_go - ERROR - - Attribute 'Approved' of entity 'LeaveApplication' referenced in GO does not exist in the database
2025-05-13 17:45:24,305 - deploy_entities_and_go - ERROR - - Attribute 'Rejected' of entity 'LeaveApplication' referenced in GO does not exist in the database
2025-05-13 17:45:24,305 - deploy_entities_and_go - ERROR - - Inserted GO 'Leave Approval Process' into workflow_temp.global_objectives
2025-05-13 17:45:24,305 - deploy_entities_and_go - ERROR - - Warning: LO 'Employee submits a leave request' (ID: SubmitLeaveRequest) referenced in GO 'go001' does not exist in the database. Skipping mapping.
2025-05-13 17:45:24,305 - deploy_entities_and_go - ERROR - - Warning: LO 'Employee uploads required documentation' (ID: UploadDocumentation) referenced in GO 'go001' does not exist in the database. Skipping mapping.
2025-05-13 17:45:24,305 - deploy_entities_and_go - ERROR - - Warning: LO 'Manager reviews the leave request' (ID: ReviewLeaveRequest) referenced in GO 'go001' does not exist in the database. Skipping mapping.
2025-05-13 17:45:24,305 - deploy_entities_and_go - ERROR - - Warning: LO 'System updates leave request status to approved' (ID: ApproveLeaveRequest) referenced in GO 'go001' does not exist in the database. Skipping mapping.
2025-05-13 17:45:24,305 - deploy_entities_and_go - ERROR - - Warning: LO 'System updates leave request status to rejected' (ID: RejectLeaveRequest) referenced in GO 'go001' does not exist in the database. Skipping mapping.
2025-05-13 17:45:24,305 - deploy_entities_and_go - ERROR - - Warning: LO 'System notifies employee of the decision' (ID: NotifyEmployee) referenced in GO 'go001' does not exist in the database. Skipping mapping.
2025-05-13 17:45:24,305 - deploy_entities_and_go - ERROR - - Warning: LO 'System updates employee calendar' (ID: UpdateCalendar) referenced in GO 'go001' does not exist in the database. Skipping mapping.
2025-05-13 17:45:24,305 - deploy_entities_and_go - ERROR - - Warning: LO 'System updates employee leave balance' (ID: UpdateLeaveBalance) referenced in GO 'go001' does not exist in the database. Skipping mapping.
2025-05-13 17:45:24,305 - deploy_entities_and_go - ERROR - - Warning: LO 'Employee cancels a previously submitted leave request' (ID: CancelLeaveRequest) referenced in GO 'go001' does not exist in the database. Skipping mapping.
2025-05-13 17:45:24,305 - deploy_entities_and_go - ERROR - - Warning: LO 'System rolls back leave approval' (ID: RollbackLeaveApproval) referenced in GO 'go001' does not exist in the database. Skipping mapping.
2025-05-13 17:45:24,305 - deploy_entities_and_go - ERROR - - Warning: LO 'System restores employee leave balance' (ID: RestoreLeaveBalance) referenced in GO 'go001' does not exist in the database. Skipping mapping.
2025-05-13 17:45:24,305 - deploy_entities_and_go - ERROR - - Warning: LO 'System notifies manager of cancellation' (ID: NotifyCancellation) referenced in GO 'go001' does not exist in the database. Skipping mapping.
2025-05-13 17:45:24,305 - deploy_entities_and_go - ERROR - - Warning: LO 'System logs audit trail for compliance' (ID: LogAuditTrail) referenced in GO 'go001' does not exist in the database. Skipping mapping.
2025-05-13 17:45:24,305 - deploy_entities_and_go - ERROR - - Referenced GO 'Employee Calendar Management' does not exist. Mapping will be marked as pending.
2025-05-13 17:45:24,305 - deploy_entities_and_go - ERROR - - Error executing query: column "display_name" of relation "output_items" does not exist
LINE 3: ...tput_stack_id, slot_id, contextual_id, data_type, display_na...
                                                             ^

2025-05-13 17:45:24,305 - deploy_entities_and_go - ERROR - GO validation and deployment failed


=== Command completed with exit code: 0 ===

