from fastapi import APIRouter, HTTPException, Body, Query, Depends
from typing import Dict, Any, Optional, List
import uuid
from datetime import datetime
import json
import re

from app.services.workflow.execution_engine import WorkflowExecutionEngine, WorkflowExecutionException
from app.db.mongo_db import get_collection

router = APIRouter()

@router.post("/design")
async def design_chat(
    message: str = Body(..., embed=True, description="User message"),
    tenant_id: str = Body(None, embed=True, description="Tenant ID"),
    user_id: str = Body(..., embed=True, description="User ID"),
    context: Dict[str, Any] = Body({}, embed=True, description="Conversation context")
):
    """
    Process a design-time chat message.
    """
    try:
        # Process the message to understand intent
        intent = _analyze_design_intent(message)
        
        # Handle different intents
        if intent["type"] == "create_workflow":
            # Create a new workflow definition
            result = _handle_create_workflow(message, tenant_id, user_id, context)
        elif intent["type"] == "define_entity":
            # Define a new entity
            result = _handle_define_entity(message, tenant_id, user_id, context)
        elif intent["type"] == "define_lo":
            # Define a local objective
            result = _handle_define_lo(message, tenant_id, user_id, context)
        elif intent["type"] == "help":
            # Provide help
            result = _handle_help(context)
        else:
            # Generic response
            result = _handle_generic_design(message, context)
        
        return {
            "response": result["response"],
            "context": result["context"]
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error processing design chat: {str(e)}")

@router.post("/runtime")
async def runtime_chat(
    message: str = Body(..., embed=True, description="User message"),
    instance_id: str = Body(None, embed=True, description="Workflow instance ID"),
    tenant_id: str = Body(None, embed=True, description="Tenant ID"),
    user_id: str = Body(..., embed=True, description="User ID"),
    context: Dict[str, Any] = Body({}, embed=True, description="Conversation context")
):
    """
    Process a runtime chat message.
    """
    try:
        # If no instance ID is provided, check if we need to start a new workflow
        if not instance_id:
            intent = _analyze_runtime_intent(message)
            
            if intent["type"] == "start_workflow":
                # Start a new workflow
                result = _handle_start_workflow(message, tenant_id, user_id, context)
                return {
                    "response": result["response"],
                    "context": result["context"],
                    "instance_id": result.get("instance_id")
                }
            elif intent["type"] == "list_workflows":
                # List available workflows
                result = _handle_list_workflows(tenant_id, user_id, context)
                return {
                    "response": result["response"],
                    "context": result["context"]
                }
            else:
                # No workflow instance and no intent to start one
                return {
                    "response": "Please select a workflow to start or provide an instance ID.",
                    "context": context
                }
        
        # Process message for an existing workflow instance
        intent = _analyze_runtime_intent(message, instance_id)
        
        # Handle different intents
        if intent["type"] == "provide_input":
            # Process input for current step
            result = _handle_provide_input(message, instance_id, tenant_id, user_id, context)
        elif intent["type"] == "get_status":
            # Get workflow status
            result = _handle_get_status(instance_id, tenant_id, user_id, context)
        elif intent["type"] == "pause_workflow":
            # Pause workflow
            result = _handle_pause_workflow(instance_id, tenant_id, user_id, context)
        elif intent["type"] == "resume_workflow":
            # Resume workflow
            result = _handle_resume_workflow(instance_id, tenant_id, user_id, context)
        elif intent["type"] == "cancel_workflow":
            # Cancel workflow
            result = _handle_cancel_workflow(message, instance_id, tenant_id, user_id, context)
        elif intent["type"] == "help":
            # Provide help
            result = _handle_help(context, runtime=True)
        else:
            # Generic response
            result = _handle_generic_runtime(message, instance_id, tenant_id, user_id, context)
        
        return {
            "response": result["response"],
            "context": result["context"],
            "instance_id": instance_id
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error processing runtime chat: {str(e)}")

# Intent analysis functions
def _analyze_design_intent(message: str) -> Dict[str, Any]:
    """
    Analyze the design-time message to determine intent.
    
    This is a simplified implementation. In a real-world scenario,
    you would use more sophisticated NLP techniques.
    """
    message_lower = message.lower()
    
    if re.search(r'create\s+(?:a\s+)?(?:new\s+)?workflow', message_lower) or re.search(r'define\s+(?:a\s+)?(?:new\s+)?workflow', message_lower):
        return {"type": "create_workflow"}
    
    if re.search(r'create\s+(?:a\s+)?(?:new\s+)?entity', message_lower) or re.search(r'define\s+(?:a\s+)?(?:new\s+)?entity', message_lower):
        return {"type": "define_entity"}
    
    if re.search(r'create\s+(?:a\s+)?(?:new\s+)?(?:local\s+)?objective', message_lower) or re.search(r'define\s+(?:a\s+)?(?:new\s+)?(?:local\s+)?objective', message_lower):
        return {"type": "define_lo"}
    
    if re.search(r'help', message_lower) or re.search(r'how\s+(?:do|can|to)', message_lower):
        return {"type": "help"}
    
    return {"type": "generic"}

def _analyze_runtime_intent(message: str, instance_id: str = None) -> Dict[str, Any]:
    """
    Analyze the runtime message to determine intent.
    
    This is a simplified implementation. In a real-world scenario,
    you would use more sophisticated NLP techniques.
    """
    message_lower = message.lower()
    
    if not instance_id:
        if re.search(r'start\s+(?:a\s+)?(?:new\s+)?workflow', message_lower) or re.search(r'run\s+(?:a\s+)?(?:new\s+)?workflow', message_lower):
            return {"type": "start_workflow"}
        
        if re.search(r'list\s+workflows', message_lower) or re.search(r'show\s+(?:available\s+)?workflows', message_lower):
            return {"type": "list_workflows"}
        
        return {"type": "generic"}
    
    if re.search(r'status', message_lower) or re.search(r'progress', message_lower):
        return {"type": "get_status"}
    
    if re.search(r'pause', message_lower) or re.search(r'stop', message_lower):
        return {"type": "pause_workflow"}
    
    if re.search(r'resume', message_lower) or re.search(r'continue', message_lower):
        return {"type": "resume_workflow"}
    
    if re.search(r'cancel', message_lower) or re.search(r'abort', message_lower):
        return {"type": "cancel_workflow"}
    
    if re.search(r'help', message_lower) or re.search(r'how\s+(?:do|can|to)', message_lower):
        return {"type": "help"}
    
    return {"type": "provide_input"}

# Handler functions for design-time
def _handle_create_workflow(message: str, tenant_id: str, user_id: str, context: Dict[str, Any]) -> Dict[str, Any]:
    """
    Handle request to create a new workflow.
    """
    # Extract workflow name and description from the message
    # This is a simplified implementation
    name_match = re.search(r'workflow\s+(?:called|named)?\s*["\']?([^"\']+)["\']?', message, re.IGNORECASE)
    workflow_name = name_match.group(1) if name_match else "New Workflow"
    
    # Process workflow type
    workflow_type = "Generic"
    if "for" in message.lower():
        type_match = re.search(r'for\s+([^\.]+)', message, re.IGNORECASE)
        if type_match:
            workflow_type = type_match.group(1).strip()
    
    # Generate a contextual ID
    contextual_id = f"GO_{workflow_name.replace(' ', '')}"
    
    # Create a new workflow definition
    workflow = {
        "name": workflow_name,
        "contextual_id": contextual_id,
        "type": workflow_type,
        "tenant_id": tenant_id,
        "created_by": user_id,
        "created_at": datetime.now(),
        "status": "Draft",
        "version": "1.0"
    }
    
    # In a real implementation, you would store this in MongoDB
    # For now, just add it to the context
    new_context = dict(context)
    new_context["current_workflow"] = workflow
    
    response = f"""I'll help you create a new workflow called "{workflow_name}" for {workflow_type}.

Let's start defining it:

1. **Global Objective**:
   - Name: {workflow_name}
   - ID: {contextual_id}
   - Purpose: To manage the {workflow_type} process

Next, we'll need to define:
- The entities involved (data model)
- The local objectives (steps in the workflow)
- The execution pathways

Would you like to start by defining the entities or the local objectives?"""
    
    return {
        "response": response,
        "context": new_context
    }

def _handle_define_entity(message: str, tenant_id: str, user_id: str, context: Dict[str, Any]) -> Dict[str, Any]:
    """
    Handle request to define a new entity.
    """
    # Extract entity name from the message
    name_match = re.search(r'entity\s+(?:called|named)?\s*["\']?([^"\']+)["\']?', message, re.IGNORECASE)
    entity_name = name_match.group(1) if name_match else "New Entity"
    
    # Process attributes
    attributes = []
    
    # Look for attributes in the message
    attr_match = re.search(r'with\s+(?:attributes|fields)\s+(.+)', message, re.IGNORECASE)
    if attr_match:
        attr_text = attr_match.group(1)
        # Split by commas or "and"
        attr_parts = re.split(r',\s*|\s+and\s+', attr_text)
        
        for part in attr_parts:
            # Look for attribute name and type
            attr_type_match = re.search(r'([^\(]+)\s*\(([^\)]+)\)', part)
            if attr_type_match:
                attr_name = attr_type_match.group(1).strip()
                attr_type = attr_type_match.group(2).strip()
                attributes.append({
                    "name": attr_name,
                    "type": attr_type
                })
            else:
                # Just attribute name
                attr_name = part.strip()
                attributes.append({
                    "name": attr_name,
                    "type": "String"  # Default type
                })
    
    # Create entity definition
    entity = {
        "name": entity_name,
        "attributes": attributes,
        "tenant_id": tenant_id,
        "created_by": user_id,
        "created_at": datetime.now(),
        "status": "Draft",
        "version": "1.0"
    }
    
    # Add to context
    new_context = dict(context)
    if "entities" not in new_context:
        new_context["entities"] = []
    
    new_context["entities"].append(entity)
    new_context["current_entity"] = entity
    
    # Generate response
    response = f"""I've defined a new entity called "{entity_name}" with the following attributes:

"""
    
    if attributes:
        for attr in attributes:
            response += f"- {attr['name']} ({attr['type']})\n"
    else:
        response += "(No attributes defined yet)\n"
    
    response += """
Would you like to add more attributes or define relationships with other entities?"""
    
    return {
        "response": response,
        "context": new_context
    }

def _handle_define_lo(message: str, tenant_id: str, user_id: str, context: Dict[str, Any]) -> Dict[str, Any]:
    """
    Handle request to define a new local objective.
    """
    # Extract LO name from the message
    name_match = re.search(r'objective\s+(?:called|named)?\s*["\']?([^"\']+)["\']?', message, re.IGNORECASE)
    lo_name = name_match.group(1) if name_match else "New Local Objective"
    
    # Determine function type
    function_type = "Process"  # Default
    if "create" in message.lower():
        function_type = "Create"
    elif "update" in message.lower():
        function_type = "Update"
    elif "read" in message.lower() or "get" in message.lower():
        function_type = "Read"
    elif "delete" in message.lower() or "remove" in message.lower():
        function_type = "Delete"
    elif "validate" in message.lower():
        function_type = "Validate"
    
    # Determine workflow source
    workflow_source = "Origin"
    if "after" in message.lower():
        workflow_source = "Sequential"
    
    # Create LO definition
    lo_id = f"LO_{lo_name.replace(' ', '')}"
    
    # Get parent workflow
    parent_workflow = context.get("current_workflow", {"contextual_id": "GO_Unknown"})
    
    lo = {
        "id": lo_id,
        "contextual_id": f"{parent_workflow['contextual_id']}.{lo_id}",
        "name": lo_name,
        "function_type": function_type,
        "workflow_source": workflow_source,
        "parent_objective_id": parent_workflow["contextual_id"],
        "tenant_id": tenant_id,
        "created_by": user_id,
        "created_at": datetime.now(),
        "status": "Draft",
        "version": "1.0",
        "execution_pathway": {
            "type": "Sequential"
        }
    }
    
    # Add to context
    new_context = dict(context)
    if "local_objectives" not in new_context:
        new_context["local_objectives"] = []
    
    new_context["local_objectives"].append(lo)
    new_context["current_lo"] = lo
    
    # Generate response
    response = f"""I've defined a new Local Objective:

- Name: {lo_name}
- ID: {lo_id}
- Function Type: {function_type}
- Workflow Source: {workflow_source}
- Parent: {parent_workflow['contextual_id']}

Next, we should define:
1. The input requirements
2. The execution rules
3. The output produced
4. The execution pathway (what happens next)

Would you like to start by defining the inputs?"""
    
    return {
        "response": response,
        "context": new_context
    }

def _handle_help(context: Dict[str, Any], runtime: bool = False) -> Dict[str, Any]:
    """
    Handle help request.
    """
    if runtime:
        response = """# Workflow Runtime Help

Here's how you can interact with the workflow execution:

- Start a workflow: "Start a new workflow for [purpose]"
- Check status: "What's the current status of this workflow?"
- Pause workflow: "Pause this workflow"
- Resume workflow: "Resume this workflow"
- Cancel workflow: "Cancel this workflow"

When the workflow is running, you can:
- Provide input for the current step
- Ask for guidance on what information is needed
- Request the available options

Need anything specific?"""
    else:
        response = """# Workflow Design Help

Here's how you can design workflows:

- Create workflow: "Create a new workflow for [purpose]"
- Define entity: "Define an entity called [name] with attributes [attr1, attr2]"
- Define local objective: "Create a local objective called [name]"
- Define pathways: "Create a sequential/alternative/parallel pathway"

You can also:
- Ask for YAML configuration: "Generate YAML for this workflow"
- Request templates: "Show me a template for [process type]"
- Validate design: "Validate this workflow design"

Need help with something specific?"""
    
    return {
        "response": response,
        "context": context
    }

def _handle_generic_design(message: str, context: Dict[str, Any]) -> Dict[str, Any]:
    """
    Handle generic design-time message.
    """
    # Default response
    response = """I'm your workflow design assistant. I can help you create:

1. New workflow definitions
2. Entity models
3. Local objectives (workflow steps)
4. Execution pathways

What would you like to design today?"""

    # If we're in the middle of designing something, provide contextual help
    if "current_workflow" in context:
        workflow = context["current_workflow"]
        response = f"""We're currently designing the "{workflow['name']}" workflow.

Would you like to:
1. Define entities for this workflow
2. Create local objectives (workflow steps)
3. Define execution pathways
4. Generate the YAML configuration"""
    
    return {
        "response": response,
        "context": context
    }

# Handler functions for runtime
def _handle_start_workflow(message: str, tenant_id: str, user_id: str, context: Dict[str, Any]) -> Dict[str, Any]:
    """
    Handle request to start a new workflow.
    """
    # Extract workflow information from the message
    workflow_match = re.search(r'workflow\s+(?:for|called|named)?\s*["\']?([^"\'\.]+)["\']?', message, re.IGNORECASE)
    workflow_name = workflow_match.group(1) if workflow_match else None
    
    # In a real implementation, you would query MongoDB for available workflows
    # For now, let's just create a mock workflow
    
    go_id = "GO_SampleWorkflow"  # This would come from the database
    
    # Generate a new instance ID
    instance_id = str(uuid.uuid4())
    
    # In a real implementation, you would create the workflow instance using the API
    # For now, simulate a successful response
    
    # Add to context
    new_context = dict(context)
    new_context["workflow_name"] = workflow_name or "Sample Workflow"
    new_context["go_id"] = go_id
    
    # Generate response
    response = f"""I've started a new workflow instance for you:

- Workflow: {workflow_name or "Sample Workflow"}
- Instance ID: {instance_id}
- Status: Active

The workflow starts with the "Initial Data Collection" step. Please provide:
1. Your name
2. Your email address
3. The purpose of this workflow

You can provide this information in a natural way, for example: "My name is John Doe, <NAME_EMAIL>, and I need this for project planning."
"""
    
    return {
        "response": response,
        "context": new_context,
        "instance_id": instance_id
    }

def _handle_list_workflows(tenant_id: str, user_id: str, context: Dict[str, Any]) -> Dict[str, Any]:
    """
    Handle request to list available workflows.
    """
    # In a real implementation, you would query MongoDB for available workflows
    # For now, let's just return some mock data
    
    workflows = [
        {"name": "Customer Onboarding", "id": "GO_CustomerOnboarding", "description": "Process for onboarding new customers"},
        {"name": "Invoice Processing", "id": "GO_InvoiceProcessing", "description": "Process for handling incoming invoices"},
        {"name": "Leave Request", "id": "GO_LeaveRequest", "description": "Process for employee leave requests"}
    ]
    
    # Add to context
    new_context = dict(context)
    new_context["available_workflows"] = workflows
    
    # Generate response
    response = """Here are the available workflows:

1. **Customer Onboarding**: Process for onboarding new customers
2. **Invoice Processing**: Process for handling incoming invoices
3. **Leave Request**: Process for employee leave requests

Which one would you like to start? You can say "Start the Customer Onboarding workflow" or something similar."""
    
    return {
        "response": response,
        "context": new_context
    }

def _handle_provide_input(message: str, instance_id: str, tenant_id: str, user_id: str, context: Dict[str, Any]) -> Dict[str, Any]:
    """
    Handle input for the current workflow step.
    """
    try:
        # In a real implementation, you would use a sophisticated NLP technique
        # to extract structured data from the message and pass it to the execution engine
        
        # For now, let's create a simplified extraction based on common patterns
        
        # Extract name
        name = None
        name_match = re.search(r'name\s+is\s+([^,\.]+)', message, re.IGNORECASE)
        if name_match:
            name = name_match.group(1).strip()
        
        # Extract email
        email = None
        email_match = re.search(r'([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})', message)
        if email_match:
            email = email_match.group(1)
        
        # Extract any key-value pairs
        input_data = {}
        
        if name:
            input_data["name"] = name
        
        if email:
            input_data["email"] = email
        
        # Look for other key-value patterns like "field: value" or "field = value"
        kv_matches = re.finditer(r'([a-zA-Z_]+)[\s:=]+([^,\.]+)', message)
        for match in kv_matches:
            key = match.group(1).strip().lower()
            value = match.group(2).strip()
            
            # Skip if already extracted
            if key == "name" and name:
                continue
            if key == "email" and email:
                continue
            
            input_data[key] = value
        
        # If no data was extracted, use the entire message as a generic input
        if not input_data:
            input_data["user_input"] = message
        
        # In a real implementation, you would call the execution engine to process the input
        # For now, let's simulate a response
        
        # Simulate next step information
        next_step = {
            "name": "Verification",
            "inputs": ["verification_code", "agreement"]
        }
        
        # Update context
        new_context = dict(context)
        new_context["current_step"] = next_step
        
        # Generate response
        response = f"""Thank you for the information. I've recorded:
"""
        
        for key, value in input_data.items():
            response += f"- {key.capitalize()}: {value}\n"
        
        response += f"""
The workflow has progressed to the "{next_step['name']}" step.

Please provide:
1. Your verification code (sent to your email)
2. Confirmation that you agree to the terms (yes/no)
"""
        
        return {
            "response": response,
            "context": new_context
        }
        
    except Exception as e:
        return {
            "response": f"I encountered an error processing your input: {str(e)}. Please try again with more specific information.",
            "context": context
        }

def _handle_get_status(instance_id: str, tenant_id: str, user_id: str, context: Dict[str, Any]) -> Dict[str, Any]:
    """
    Handle request to get workflow status.
    """
    # In a real implementation, you would query the database for the current status
    # For now, let's just return some mock data
    
    status = {
        "status": "Active",
        "current_step": "Verification",
        "progress": "2/5 steps completed",
        "time_elapsed": "10 minutes",
        "next_step": "Approval"
    }
    
    # Update context
    new_context = dict(context)
    new_context["status"] = status
    
    # Generate response
    response = f"""**Current Workflow Status**

- Status: {status['status']}
- Current Step: {status['current_step']}
- Progress: {status['progress']}
- Time Elapsed: {status['time_elapsed']}
- Next Step: {status['next_step']}

You need to complete the current step before proceeding. Is there anything specific you'd like to know about this workflow?"""
    
    return {
        "response": response,
        "context": new_context
    }

def _handle_pause_workflow(instance_id: str, tenant_id: str, user_id: str, context: Dict[str, Any]) -> Dict[str, Any]:
    """
    Handle request to pause workflow.
    """
    # In a real implementation, you would call the execution engine to pause the workflow
    # For now, let's just simulate a response
    
    # Update context
    new_context = dict(context)
    new_context["status"] = {"status": "Paused"}
    
    # Generate response
    response = """I've paused the workflow for you. Your progress has been saved.

You can resume at any time by saying "resume workflow" or similar. Is there anything else you need?"""
    
    return {
        "response": response,
        "context": new_context
    }

def _handle_resume_workflow(instance_id: str, tenant_id: str, user_id: str, context: Dict[str, Any]) -> Dict[str, Any]:
    """
    Handle request to resume workflow.
    """
    # In a real implementation, you would call the execution engine to resume the workflow
    # For now, let's just simulate a response
    
    # Update context
    new_context = dict(context)
    new_context["status"] = {"status": "Active"}
    
    # Generate response
    response = """I've resumed the workflow for you. Let's continue where we left off.

You were at the "Verification" step. Please provide:
1. Your verification code
2. Confirmation of agreement to terms"""
    
    return {
        "response": response,
        "context": new_context
    }

def _handle_cancel_workflow(message: str, instance_id: str, tenant_id: str, user_id: str, context: Dict[str, Any]) -> Dict[str, Any]:
    """
    Handle request to cancel workflow.
    """
    # Extract reason if provided
    reason = None
    reason_match = re.search(r'because\s+(.+)', message, re.IGNORECASE)
    if reason_match:
        reason = reason_match.group(1).strip()
    
    # In a real implementation, you would call the execution engine to cancel the workflow
    # For now, let's just simulate a response
    
    # Update context
    new_context = dict(context)
    new_context["status"] = {"status": "Cancelled"}
    if reason:
        new_context["cancellation_reason"] = reason
    
    # Generate response
    if reason:
        response = f"""I've cancelled the workflow as requested. Reason: {reason}.

Your progress has been saved for reference. Would you like to start a different workflow?"""
    else:
        response = """I've cancelled the workflow as requested.

Your progress has been saved for reference. Would you like to start a different workflow?"""
    
    return {
        "response": response,
        "context": new_context
    }

def _handle_generic_runtime(message: str, instance_id: str, tenant_id: str, user_id: str, context: Dict[str, Any]) -> Dict[str, Any]:
    """
    Handle generic runtime message.
    """
    # Default response
    response = """I'm not sure what you're asking about this workflow. You can:

1. Provide input for the current step
2. Check the status of the workflow
3. Pause or cancel the workflow
4. Ask for help

What would you like to do?"""
    
    return {
        "response": response,
        "context": context
    }
