# Enterprise Workflow System Configuration for HRMS

# Tenant Configuration
tenant:
  id: "T001"
  name: "HR001"
  roles:
    - id: "R001"
      name: "<PERSON><PERSON><PERSON><PERSON><PERSON>"
      inherits_from: null
      access:
        entities:
          - entity_id: "E001"  # Employee
            permissions: ["Read", "Create", "Update", "Delete", "Assign"]
          - entity_id: "E002"  # Leave
            permissions: ["Read", "Create", "Update", "Delete", "Assign"]
          - entity_id: "E003"  # LeaveBalance
            permissions: ["Read", "Create", "Update", "Delete"]
        objectives:
          - objective_id: "GO001"  # Leave Management
            permissions: ["Execute", "Transact"]
          - objective_id: "GO002"  # Leave Reports
            permissions: ["Information"]
    
    - id: "R002"
      name: "Manager"
      inherits_from: null
      access:
        entities:
          - entity_id: "E001"  # Employee
            permissions: ["Read"]
          - entity_id: "E002"  # Leave
            permissions: ["Read", "Update"]  # Update covers both Approve and Reject
          - entity_id: "E003"  # LeaveBalance
            permissions: ["Read"]
        objectives:
          - objective_id: "GO001"  # Leave Management
            permissions: ["Execute"]
          - objective_id: "GO001.LO003"  # Approve Leave
            permissions: ["Execute", "Update"]  # Update includes approval functions
    
    - id: "R003"
      name: "Employee"
      inherits_from: null
      access:
        entities:
          - entity_id: "E001"  # Employee
            permissions: ["Read"]
          - entity_id: "E002"  # Leave
            permissions: ["Read", "Create", "Update"]  # Update includes Cancel operation
          - entity_id: "E003"  # LeaveBalance
            permissions: ["Read"]
        objectives:
          - objective_id: "GO001"  # Leave Management
            permissions: ["Execute"]
          - objective_id: "GO001.LO001"  # Apply for Leave
            permissions: ["Execute"]

# Permission Types Definition
permission_types:
  # Execute: Limited to executing only their own transactions
  - id: "Execute" 
    description: "Can execute only their own assigned transactions"
    capabilities: ["ExecuteOwn"]
  
  # Transact: Can execute and view transactions for their team/group
  - id: "Transact"
    description: "Can view other's transactions and execute both their own and others in the same group"
    capabilities: ["ExecuteOwn", "ExecuteOthers", "ViewGroup"]
  
  # Information: Read-only access to all transactions
  - id: "Information"
    description: "Can view all transaction information but cannot execute any transactions"
    capabilities: ["ViewAll"]
  
  # CRUD Permissions
  - id: "Read"
    description: "Can view entity data"
    capabilities: ["ViewData"]
  
  - id: "Create"
    description: "Can create new entity records"
    capabilities: ["CreateData"]
  
  - id: "Update"
    description: "Can modify existing entity records including approvals and rejections"
    capabilities: ["ModifyData", "ApproveReject"]
  
  - id: "Delete"
    description: "Can remove entity records"
    capabilities: ["RemoveData"]
  
  - id: "Assign"
    description: "Can assign entity records to users"
    capabilities: ["AssignData"]
# Entities
entities:
  # Regular Entity: Employee
  - id: "E001"
    name: "Employee"
    version: "1.0"
    status: "Active"
    type: "Regular"
    attributes_metadata:
      attribute_prefix: "At"
      attribute_map:
        "At001": "EmployeeID"
        "At002": "FirstName"
        "At003": "LastName"
        "At004": "Email"
        "At005": "Department"
        "At006": "Designation"
        "At007": "JoiningDate"
        "At008": "ReportingManager"
      required_attributes: ["At001", "At002", "At003", "At004", "At005", "At006", "At007"]
    attributes:
      - id: "At001"
        name: "EmployeeID"
        display_name: "Employee ID"
        datatype: "String"
        version: "1.0"
        status: "Deployed"
        required: true
      
      - id: "At002"
        name: "FirstName"
        display_name: "First Name"
        datatype: "String"
        version: "1.0"
        status: "Deployed"
        required: true
      
      - id: "At003"
        name: "LastName"
        display_name: "Last Name"
        datatype: "String"
        version: "1.0"
        status: "Deployed"
        required: true
      
      - id: "At004"
        name: "Email"
        display_name: "Email"
        datatype: "String"
        version: "1.0"
        status: "Deployed"
        required: true
        validations:
          - rule: "Email format validation"
            expression: "^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$"
      
      - id: "At005"
        name: "Department"
        display_name: "Department"
        datatype: "String"
        version: "1.0"
        status: "Deployed"
        required: true
      
      - id: "At006"
        name: "Designation"
        display_name: "Designation"
        datatype: "String"
        version: "1.0"
        status: "Deployed"
        required: true
      
      - id: "At007"
        name: "JoiningDate"
        display_name: "Joining Date"
        datatype: "Date"
        version: "1.0"
        status: "Deployed"
        required: true
      
      - id: "At008"
        name: "ReportingManager"
        display_name: "Reporting Manager"
        datatype: "Reference"
        version: "1.0"
        status: "Deployed"
        references: "E001"  # Self-reference to Employee
        required: false
    
    relationships:
      - entity_id: "E003"  # LeaveBalance
        type: "OneToMany"
        through_attribute: "E001.At001"  # Employee.EmployeeID
        to_attribute: "E003.At001"  # LeaveBalance.EmployeeID

  # Regular Entity: Leave
  - id: "E002"
    name: "Leave"
    version: "1.0"
    status: "Active"
    type: "Regular"
    attributes_metadata:
      attribute_prefix: "At"
      attribute_map:
        "At101": "LeaveID"
        "At102": "EmployeeID"
        "At103": "LeaveType"
        "At104": "StartDate"
        "At105": "EndDate"
        "At106": "NumberOfDays"
        "At107": "Status"
        "At108": "Reason"
        "At109": "AppliedDate"
        "At110": "ApprovedBy"
        "At111": "ApprovalDate"
        "At112": "IsLossOfPay"
        "At113": "Comments"
      required_attributes: ["At101", "At102", "At103", "At104", "At105", "At106", "At107", "At108", "At109"]
    attributes:
      - id: "At101"
        name: "LeaveID"
        display_name: "Leave ID"
        datatype: "String"
        version: "1.0"
        status: "Deployed"
        required: true
      
      - id: "At102"
        name: "EmployeeID"
        display_name: "Employee ID"
        datatype: "Reference"
        version: "1.0"
        status: "Deployed"
        references: "E001" # Employee
        required: true
      
      - id: "At103"
        name: "LeaveType"
        display_name: "Leave Type"
        datatype: "Enum"
        version: "1.0"
        status: "Deployed"
        required: true
        values: ["Annual", "Sick", "Personal", "Maternity", "Paternity", "Bereavement", "Unpaid"]
      
      - id: "At104"
        name: "StartDate"
        display_name: "Start Date"
        datatype: "Date"
        version: "1.0"
        status: "Deployed"
        required: true
      
      - id: "At105"
        name: "EndDate"
        display_name: "End Date"
        datatype: "Date"
        version: "1.0"
        status: "Deployed"
        required: true
      
      - id: "At106"
        name: "NumberOfDays"
        display_name: "Number of Days"
        datatype: "Number"
        version: "1.0"
        status: "Deployed"
        required: true
      
      - id: "At107"
        name: "Status"
        display_name: "Status"
        datatype: "Enum"
        version: "1.0"
        status: "Deployed"
        required: false
        values: ["Applied", "Pending", "Approved", "Rejected", "Cancelled"]
      
      - id: "At108"
        name: "Reason"
        display_name: "Reason"
        datatype: "String"
        version: "1.0"
        status: "Deployed"
        required: false
      
      - id: "At109"
        name: "AppliedDate"
        display_name: "Applied Date"
        datatype: "DateTime"
        version: "1.0"
        status: "Deployed"
        required: false
      
      - id: "At110"
        name: "ApprovedBy"
        display_name: "Approved By"
        datatype: "Reference"
        version: "1.0"
        status: "Deployed"
        references: "E001" # Employee (Manager)
        required: false
      
      - id: "At111"
        name: "ApprovalDate"
        display_name: "Approval Date"
        datatype: "DateTime"
        version: "1.0"
        status: "Deployed"
        required: false
      
      - id: "At112"
        name: "IsLossOfPay"
        display_name: "Is Loss Of Pay"
        datatype: "Boolean"
        version: "1.0"
        status: "Deployed"
        required: false
      
      - id: "At113"
        name: "Comments"
        display_name: "Comments"
        datatype: "String"
        version: "1.0"
        status: "Deployed"
        required: false
    
    relationships:
      - entity_id: "E001"  # Employee
        type: "ManyToOne"
        through_attribute: "E002.At102"  # Leave.EmployeeID
        to_attribute: "E001.At001"  # Employee.EmployeeID    
  # Entities
entities:
  # Regular Entity: Employee
  - id: "E001"
    name: "Employee"
    version: "1.0"
    status: "Active"
    type: "Regular"
    attributes_metadata:
      attribute_prefix: "At"
      attribute_map:
        "At001": "EmployeeID"
        "At002": "FirstName"
        "At003": "LastName"
        "At004": "Email"
        "At005": "Department"
        "At006": "Designation"
        "At007": "JoiningDate"
        "At008": "ReportingManager"
      required_attributes: ["At001", "At002", "At003", "At004", "At005", "At006", "At007"]
    attributes:
      - id: "At001"
        name: "EmployeeID"
        display_name: "Employee ID"
        datatype: "String"
        version: "1.0"
        status: "Deployed"
        required: true
      
      - id: "At002"
        name: "FirstName"
        display_name: "First Name"
        datatype: "String"
        version: "1.0"
        status: "Deployed"
        required: true
      
      - id: "At003"
        name: "LastName"
        display_name: "Last Name"
        datatype: "String"
        version: "1.0"
        status: "Deployed"
        required: true
      
      - id: "At004"
        name: "Email"
        display_name: "Email"
        datatype: "String"
        version: "1.0"
        status: "Deployed"
        required: true
        validations:
          - rule: "Email format validation"
            expression: "^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$"
      
      - id: "At005"
        name: "Department"
        display_name: "Department"
        datatype: "String"
        version: "1.0"
        status: "Deployed"
        required: true
      
      - id: "At006"
        name: "Designation"
        display_name: "Designation"
        datatype: "String"
        version: "1.0"
        status: "Deployed"
        required: true
      
      - id: "At007"
        name: "JoiningDate"
        display_name: "Joining Date"
        datatype: "Date"
        version: "1.0"
        status: "Deployed"
        required: true
      
      - id: "At008"
        name: "ReportingManager"
        display_name: "Reporting Manager"
        datatype: "Reference"
        version: "1.0"
        status: "Deployed"
        references: "E001"  # Self-reference to Employee
        required: false
    
    relationships:
      - entity_id: "E003"  # LeaveBalance
        type: "OneToMany"
        through_attribute: "E001.At001"  # Employee.EmployeeID
        to_attribute: "E003.At001"  # LeaveBalance.EmployeeID

  # Regular Entity: Leave
  - id: "E002"
    name: "Leave"
    version: "1.0"
    status: "Active"
    type: "Regular"
    attributes_metadata:
      attribute_prefix: "At"
      attribute_map:
        "At101": "LeaveID"
        "At102": "EmployeeID"
        "At103": "LeaveType"
        "At104": "StartDate"
        "At105": "EndDate"
        "At106": "NumberOfDays"
        "At107": "Status"
        "At108": "Reason"
        "At109": "AppliedDate"
        "At110": "ApprovedBy"
        "At111": "ApprovalDate"
        "At112": "IsLossOfPay"
        "At113": "Comments"
      required_attributes: ["At101", "At102", "At103", "At104", "At105", "At106", "At107", "At108", "At109"]
    attributes:
      - id: "At101"
        name: "LeaveID"
        display_name: "Leave ID"
        datatype: "String"
        version: "1.0"
        status: "Deployed"
        required: true
      
      - id: "At102"
        name: "EmployeeID"
        display_name: "Employee ID"
        datatype: "Reference"
        version: "1.0"
        status: "Deployed"
        references: "E001" # Employee
        required: true
      
      - id: "At103"
        name: "LeaveType"
        display_name: "Leave Type"
        datatype: "Enum"
        version: "1.0"
        status: "Deployed"
        required: true
        values: ["Annual", "Sick", "Personal", "Maternity", "Paternity", "Bereavement", "Unpaid"]
      
      - id: "At104"
        name: "StartDate"
        display_name: "Start Date"
        datatype: "Date"
        version: "1.0"
        status: "Deployed"
        required: true
      
      - id: "At105"
        name: "EndDate"
        display_name: "End Date"
        datatype: "Date"
        version: "1.0"
        status: "Deployed"
        required: true
      
      - id: "At106"
        name: "NumberOfDays"
        display_name: "Number of Days"
        datatype: "Number"
        version: "1.0"
        status: "Deployed"
        required: true
      
      - id: "At107"
        name: "Status"
        display_name: "Status"
        datatype: "Enum"
        version: "1.0"
        status: "Deployed"
        required: false
        values: ["Applied", "Pending", "Approved", "Rejected", "Cancelled"]
      
      - id: "At108"
        name: "Reason"
        display_name: "Reason"
        datatype: "String"
        version: "1.0"
        status: "Deployed"
        required: false
      
      - id: "At109"
        name: "AppliedDate"
        display_name: "Applied Date"
        datatype: "DateTime"
        version: "1.0"
        status: "Deployed"
        required: false
      
      - id: "At110"
        name: "ApprovedBy"
        display_name: "Approved By"
        datatype: "Reference"
        version: "1.0"
        status: "Deployed"
        references: "E001" # Employee (Manager)
        required: false
      
      - id: "At111"
        name: "ApprovalDate"
        display_name: "Approval Date"
        datatype: "DateTime"
        version: "1.0"
        status: "Deployed"
        required: false
      
      - id: "At112"
        name: "IsLossOfPay"
        display_name: "Is Loss Of Pay"
        datatype: "Boolean"
        version: "1.0"
        status: "Deployed"
        required: false
      
      - id: "At113"
        name: "Comments"
        display_name: "Comments"
        datatype: "String"
        version: "1.0"
        status: "Deployed"
        required: false
      - id: "At112"
        name: "IsEligible"
        display_name: "Is Eligible"
        datatype: "Boolean"
        version: "1.0"
        status: "Deployed"
        required: false
    
    relationships:
      - entity_id: "E001"  # Employee
        type: "ManyToOne"
        through_attribute: "E002.At102"  # Leave.EmployeeID
        to_attribute: "E001.At001"  # Employee.EmployeeID
  # Regular Entity: LeaveBalance
  - id: "E003"
    name: "LeaveBalance"
    version: "1.0"
    status: "Active"
    type: "Regular"
    attributes_metadata:
      attribute_prefix: "At"
      attribute_map:
        "At201": "EmployeeID"
        "At202": "LeaveType"
        "At203": "BalanceYear"
        "At204": "TotalEntitlement"
        "At205": "Used"
        "At206": "Remaining"
        "At207": "LastUpdated"
      required_attributes: ["At201", "At202", "At203", "At204", "At205", "At206"]
    attributes:
      - id: "At201"
        name: "EmployeeID"
        display_name: "Employee ID"
        datatype: "Reference"
        version: "1.0"
        status: "Deployed"
        references: "E001" # Employee
        required: true
      
      - id: "At202"
        name: "LeaveType"
        display_name: "Leave Type"
        datatype: "Enum"
        version: "1.0"
        status: "Deployed"
        required: true
        values: ["Annual", "Sick", "Personal", "Maternity", "Paternity", "Bereavement"]
      
      - id: "At203"
        name: "BalanceYear"
        display_name: "Balance Year"
        datatype: "Number"
        version: "1.0"
        status: "Deployed"
        required: true
      
      - id: "At204"
        name: "TotalEntitlement"
        display_name: "Total Entitlement"
        datatype: "Number"
        version: "1.0"
        status: "Deployed"
        required: true
      
      - id: "At205"
        name: "Used"
        display_name: "Used"
        datatype: "Number"
        version: "1.0"
        status: "Deployed"
        required: true
      
      - id: "At206"
        name: "Remaining"
        display_name: "Remaining"
        datatype: "Number"
        version: "1.0"
        status: "Deployed"
        required: true
      
      - id: "At207"
        name: "LastUpdated"
        display_name: "Last Updated"
        datatype: "DateTime"
        version: "1.0"
        status: "Deployed"
        required: false
    
    relationships:
      - entity_id: "E001"  # Employee
        type: "ManyToOne"
        through_attribute: "E003.At201"  # LeaveBalance.EmployeeID
        to_attribute: "E001.At001"  # Employee.EmployeeID

  # System Entity: WorkflowResults
  - id: "E004"
    name: "WorkflowResults"
    version: "1.0"
    status: "Active"
    type: "System"
    description: "Contains workflow execution results and metrics"
    attributes_metadata:
      attribute_prefix: "At"
      attribute_map:
        "At301": "LeavesPendingApproval"
        "At302": "LeavesApprovedCount"
        "At303": "LeavesRejectedCount"
        "At304": "Duration"
        "At305": "StartTime"
        "At306": "EndTime"
        "At307": "Status"
      required_attributes: ["At305", "At306", "At307"]
    attributes:
      - id: "At301"
        name: "LeavesPendingApproval"
        display_name: "Leaves Pending Approval"
        datatype: "Integer"
        version: "1.0"
        status: "Deployed"
        required: false
        description: "Number of leaves pending approval"
      
      - id: "At302"
        name: "LeavesApprovedCount"
        display_name: "Leaves Approved Count"
        datatype: "Integer"
        version: "1.0"
        status: "Deployed"
        required: false
        description: "Number of leaves approved"
      
      - id: "At303"
        name: "LeavesRejectedCount"
        display_name: "Leaves Rejected Count"
        datatype: "Integer"
        version: "1.0"
        status: "Deployed"
        required: false
        description: "Number of leaves rejected"
      
      - id: "At304"
        name: "Duration"
        display_name: "Duration (ms)"
        datatype: "Integer"
        version: "1.0"
        status: "Deployed"
        required: false
        description: "Workflow execution duration in milliseconds"
      
      - id: "At305"
        name: "StartTime"
        display_name: "Start Time"
        datatype: "DateTime"
        version: "1.0"
        status: "Deployed"
        required: true
        description: "Workflow start timestamp"
      
      - id: "At306"
        name: "EndTime"
        display_name: "End Time"
        datatype: "DateTime"
        version: "1.0"
        status: "Deployed"
        required: true
        description: "Workflow end timestamp"
      
      - id: "At307"
        name: "Status"
        display_name: "Status"
        datatype: "String"
        version: "1.0"
        status: "Deployed"
        required: true
        description: "Final workflow execution status"
        values: ["Completed", "Failed", "Partial", "Cancelled"]

  # System Entity: TeamAvailability
  - id: "E005"
    name: "TeamAvailability"
    version: "1.0"
    status: "Active"
    type: "System"
    description: "Contains team availability information for leave approval decisions"
    attributes_metadata:
      attribute_prefix: "At"
      attribute_map:
        "At401": "Department"
        "At402": "Designation" 
        "At403": "Date"
        "At404": "TotalEmployees"
        "At405": "AvailableEmployees"
        "At406": "OnLeaveEmployees"
      required_attributes: ["At401", "At402", "At403", "At404", "At405", "At406"]
    attributes:
      - id: "At401"
        name: "Department"
        display_name: "Department"
        datatype: "String"
        version: "1.0"
        status: "Deployed"
        required: true
      
      - id: "At402"
        name: "Designation"
        display_name: "Designation"
        datatype: "String"
        version: "1.0"
        status: "Deployed"
        required: true
      
      - id: "At403"
        name: "Date"
        display_name: "Date"
        datatype: "Date"
        version: "1.0"
        status: "Deployed"
        required: true
      
      - id: "At404"
        name: "TotalEmployees"
        display_name: "Total Employees"
        datatype: "Integer"
        version: "1.0"
        status: "Deployed"
        required: true
      
      - id: "At405"
        name: "AvailableEmployees"
        display_name: "Available Employees"
        datatype: "Integer"
        version: "1.0"
        status: "Deployed"
        required: true
      
      - id: "At406"
        name: "OnLeaveEmployees"
        display_name: "On Leave Employees"
        datatype: "Integer"
        version: "1.0"
        status: "Deployed"
        required: true
# Global Objectives
global_objectives:
  - id: "GO001"
    name: "Leave Management Process"
    version: "1.0"
    status: "Active"
    
    # Input Stack for Global Objective
    input_stack:
      description: "Defines inputs from other global objectives for leave management process"
      inputs:
        - id: "I001"
          slot_id: "PreviousWorkflowResult"
          contextual_id: "GO001.I001"
          entity_reference: "E004"  # WorkflowResults entity
          attribute_reference: "At307"  # Status attribute
          source:
            type: "System"
            description: "Result from preceding workflow"
          required: false
        
      
      system_functions:
        - function_id: "SF001"  
          function_name: "validate_employee_exists"
          function_type: "validation"
          parameters:
            entity: "E001"
            attribute: "At001"
            value_source: "GO001.I001"
          output_to: "EmployeeValidationResult"
        
        - function_id: "SF002"
          function_name: "log_workflow_event"
          function_type: "logging"
          parameters:
            event_type: "WorkflowStart"
            workflow_id: "GO001"
            data:
              employee_id:
                source_type: "input"
                input_id: "GO001.I001"
    
    # Output Stack for Global Objective
    output_stack:
      description: "Defines outputs that other global objectives can consume"
      outputs:
        - id: "O001"
          slot_id: "LeaveProcessedCount"
          contextual_id: "GO001.O001"
          output_entity: "E004"  # WorkflowResults entity
          output_attribute: "At302"  # LeavesApprovedCount attribute
          data_type: "Integer"
          triggers:
            description: "Triggers notifications to HR dashboard workflow"
            items:
              - id: "T001"
                target_objective: "GO002"  # HR Dashboard
                target_input: "GO002.I001"  # LeaveMetrics
                mapping_type: "Direct"
                condition:
                  condition_type: "attribute_comparison"
                  entity: "E004"
                  attribute: "At307"  # Status
                  operator: "equals"
                  value: "Completed"
        
        - id: "O002"
          slot_id: "WorkflowCompletionStatus"
          contextual_id: "GO001.O002"
          output_entity: "E004"  # WorkflowResults entity
          output_attribute: "At307"  # Status attribute
          data_type: "String"
          triggers:
            description: "Provides workflow completion status to dependent workflows"
            items:
              - id: "T001"
                target_objective: "GO003"  # Payroll Processing
                target_input: "GO003.I002"  # LeaveStatus
                mapping_type: "Direct"
      
      system_functions:
        - function_id: "SF003"
          function_name: "aggregate_results"
          function_type: "data_processing"
          parameters:
            target_entity: "E004"  # WorkflowResults entity
            include_timing: true
            timing_attributes: {
              start_time: "At305",
              end_time: "At306",
              duration: "At304"
            }
        
        - function_id: "SF004"
          function_name: "log_workflow_completion"
          function_type: "logging"
          parameters:
            event_type: "WorkflowCompleted"
            workflow_id: "GO001"
            result_entity: "E004"
            include_timing: true
    
    # Data Mapping Stack for Global Objective
    data_mapping_stack:
      description: "Defines data movement between global objectives"
      mappings:
        - id: "M001"
          source: "GO002.O001"  # Employee Data Management Output
          target: "GO001.I002"  # Leave Management Input (EmployeeBulkData)
          mapping_type: "Direct"
        
        - id: "M002"
          source: "GO001.O001"  # Leave Management Output (LeaveProcessedCount)
          target: "GO003.I001"  # Payroll Processing Input
          mapping_type: "Direct"
      
      rules:
        - id: "R001"
          description: "Ensure current workflow status isn't a failure before accepting input"
          rule_condition:
            condition_type: "attribute_comparison"
            entity: "E004"  # WorkflowResults
            attribute: "At307"  # Status attribute
            operator: "not_equals"
            value: "Failed"
          error_message: "Cannot proceed with leave management when a dependent workflow failed"
# Local Objectives
# Local Objectives
local_objectives:
  # LO001: Apply for Leave
 - id: "lo001"
   contextual_id: "GO001.lo001"
   name: "Apply for Leave"
   function_type: "Create"
   workflow_source: "Origin"
   execution_pathway:
    type: "sequential"
    next_lo: "lo003"

   agent_stack:
    agents:
      - role: "R003"
        rights: ["execute"]

   input_stack:
    description: "Collect leave details from user"
    inputs:
      - id: "in001"
        slot_id: "e002.at101.in001"
        contextual_id: "go001.lo001.in001"
        source:
          type: "system"
          description: "Auto-generated Leave ID"
        required: true
        data_type: "string"
        ui_control: "oj-input-text"
        nested_function:
          id: "nf001"
          function_name: "generate_id"
          function_type: "utility"
          parameters:
            entity: "e002"
            attribute: "at101"
          output_to: "at101"

      - id: "in002"
        slot_id: "e002.at102.in002"
        contextual_id: "go001.lo001.in002"
        source:
          type: "user"
          description: "Employee ID"
        required: true
        data_type: "reference"
        ui_control: "oj-input-text"
        validations:
          - rule: "must not be empty"
            rule_type: "validate_required"

      - id: "in003"
        slot_id: "e002.at103.in003"
        contextual_id: "go001.lo001.in003"
        source:
          type: "user"
          description: "Leave Type"
        required: true
        data_type: "enum"
        ui_control: "oj-select-single"
        allowed_values:
          - "Annual"
          - "Sick"
          - "Personal"
          - "Maternity"
          - "Paternity"
          - "Bereavement"
          - "Unpaid"

      - id: "in004"
        slot_id: "e002.at104.in004"
        contextual_id: "go001.lo001.in004"
        source:
          type: "user"
          description: "Start Date"
        required: true
        data_type: "date"
        ui_control: "oj-input-date"

      - id: "in005"
        slot_id: "e002.at105.in005"
        contextual_id: "go001.lo001.in005"
        source:
          type: "user"
          description: "End Date"
        required: true
        data_type: "date"
        ui_control: "oj-input-date"

      - id: "in006"
        slot_id: "e002.at106.in006"
        contextual_id: "go001.lo001.in006"
        source:
          type: "system"
          description: "Calculated Leave Duration"
        required: true
        data_type: "number"
        ui_control: "oj-slider"
        nested_functions:
          - id: "nf002"
            function_name: "subtract_days"
            function_type: "math"
            parameters:
              start_date: "${e002.at104}"
              end_date: "${e002.at105}"
            output_to: "at106"

          - id: "nf003"
            function_name: "compare"
            function_type: "validation"
            parameters:
              a: "${e002.at106}"
              b: "10"
              operator: "less_than_or_equal"
            output_to: "iseligible"

      - id: "in007"
        slot_id: "e002.at108.in007"
        contextual_id: "go001.lo001.in007"
        source:
          type: "user"
          description: "Leave Reason"
        required: true
        data_type: "string"
        ui_control: "oj-text-area"
        validations:
          - rule: "reason is required"
            rule_type: "validate_required"

   output_stack:
    description: "Outputs from leave creation"
    outputs:
      - id: "out001"
        slot_id: "executionstatus.out001"
        contextual_id: "go001.lo001.out001"
        source:
          type: "system"
          description: "Success or failure of lo001"

      - id: "out002"
        slot_id: "e002.at101.out002"
        contextual_id: "go001.lo001.out002"
        source:
          type: "input"
          description: "Leave ID"
          value: "e002.at101"

      - id: "out003"
        slot_id: "e002.at102.out003"
        contextual_id: "go001.lo001.out003"
        source:
          type: "input"
          description: "Employee ID"
          value: "e002.at102"

      - id: "out004"
        slot_id: "e002.at103.out004"
        contextual_id: "go001.lo001.out004"
        source:
          type: "input"
          description: "Leave Type"
          value: "e002.at103"

      - id: "out005"
        slot_id: "e002.at104.out005"
        contextual_id: "go001.lo001.out005"
        source:
          type: "input"
          description: "Start Date"
          value: "e002.at104"

      - id: "out006"
        slot_id: "e002.at105.out006"
        contextual_id: "go001.lo001.out006"
        source:
          type: "input"
          description: "End Date"
          value: "e002.at105"

      - id: "out007"
        slot_id: "e002.at106.out007"
        contextual_id: "go001.lo001.out007"
        source:
          type: "input"
          description: "Leave Duration"
          value: "e002.at106"

      - id: "out008"
        slot_id: "e002.at108.out008"
        contextual_id: "go001.lo001.out008"
        source:
          type: "input"
          description: "Leave Reason"
          value: "e002.at108"
    data_mapping_stack:
      description: Resolve Leave ID from Apply step
      mappings:
        - id: lm001
          source: go001.lo001.out002
          target: go001.lo003.in008
          mapping_type: Direct

      
#local_objectives:
 - id: "lo003"
   contextual_id: "GO001.lo003"
   name: "Manager Approval"
   function_type: "Update"
   workflow_source: "Terminal"
   execution_pathway:
    type: "Alternative"
    conditions:
      - condition:
          condition_type: "attribute_comparison"
          entity: "e002"
          attribute: "at107"
          operator: "equals"
          value: "Approved"
        next_lo: "lo004"
      - condition:
          condition_type: "attribute_comparison"
          entity: "e002"
          attribute: "at107"
          operator: "equals"
          value: "Rejected"
        next_lo: "lo005"

   agent_stack:
    agents:
      - role: "R002"
        rights: ["Execute", "Update"]

   input_stack:
    description: "Receive inputs for decision"
    inputs:
      - id: "in008"
        slot_id: "e002.at101.in008"
        contextual_id: "go001.lo003.in008"
        source:
          type: "System"
          description: "Leave ID"
        required: true
        data_type: "string"
        ui_control: "oj-input-text"

      - id: "in009"
        slot_id: "e002.at102.in009"
        contextual_id: "go001.lo003.in009"
        source:
          type: "System"
          description: "Employee ID"
        required: true
        data_type: "reference"
        ui_control: "oj-input-text"

      - id: "in010"
        slot_id: "e002.at103.in010"
        contextual_id: "go001.lo003.in010"
        source:
          type: "System"
          description: "Leave Type"
        required: true
        data_type: "enum"
        ui_control: "oj-select-single"
        allowed_values:
          - "Annual"
          - "Sick"
          - "Personal"
          - "Maternity"
          - "Paternity"
          - "Bereavement"
          - "Unpaid"

      - id: "in011"
        slot_id: "e002.at106.in011"
        contextual_id: "go001.lo003.in011"
        source:
          type: "System"
          description: "Leave Duration"
        required: true
        data_type: "number"
        ui_control: "oj-slider"

      - id: "in012"
        slot_id: "e002.at107.in012"
        contextual_id: "go001.lo003.in012"
        source:
          type: "User"
          description: "Manager's Decision"
        required: true
        data_type: "enum"
        ui_control: "oj-radioset"
        allowed_values:
          - "Approved"
          - "Rejected"
        nested_function:
          id: "nf004"
          function_name: "enum_check"
          function_type: "validation"
          parameters:
            value: "${e002.at107}"
            allowed_values:
              - "Approved"
              - "Rejected"
          output_to: "isapprovalvalid"

      - id: "in013"
        slot_id: "e002.at113.in013"
        contextual_id: "go001.lo003.in013"
        source:
          type: "User"
          description: "Manager Comments"
        required: false
        data_type: "string"
        ui_control: "oj-text-area"

   output_stack:
    description: "Outputs from manager approval"
    outputs:
      - id: "out009"
        slot_id: "executionstatus.out009"
        contextual_id: "go001.lo003.out009"
        source:
          type: "System"
          description: "Success/failure of lo003"

      - id: "out010"
        slot_id: "e002.at107.out010"
        contextual_id: "go001.lo003.out010"
        source:
          type: "Input"
          description: "Manager’s Approval"
          value: "e002.at107"

   data_mapping_stack:
    description: "Pass outcome to next lo"
    mappings:
      - id: "lm005"
        source: "go001.lo003.out010"
        target: "go001.lo004.in001"
        mapping_type: "Conditional"
        condition:
          condition_type: "attribute_comparison"
          entity: "e002"
          attribute: "at107"
          operator: "equals"
          value: "Approved"

      - id: "lm006"
        source: "go001.lo003.out010"
        target: "go001.lo005.in001"
        mapping_type: "Conditional"
        condition:
          condition_type: "attribute_comparison"
          entity: "e002"
          attribute: "at107"
          operator: "equals"
          value: "Rejected"