"""
Local Objectives v2 API Module

This module provides the local objectives microservice for the v2 API,
following the standardized microservices architecture with RBAC support.
"""

from .routes import router
from .models import (
    InputFieldResponse,
    LocalObjectiveInputsResponse,
    DropdownOption,
    DependentInputRequest,
    DependentInputResponse,
    NestedFunctionRequest,
    NestedFunctionResponse,
    ValidationResult,
    InputValidationRequest,
    InputValidationResponse,
    ErrorResponse,
    SuccessResponse,
    MultiSelectValue,
    MultiSelectOption,
    NestedFunctionDefinition,
    NestedFunctionExecutionResult,
    DropdownDataSource,
    InputExecutionRecord,
    OutputExecutionRecord
)
from .service import (
    LocalObjectivesService,
    RBACService,
    NestedFunctionService,
    DropdownService
)

__all__ = [
    # Router
    "router",
    
    # Response Models
    "InputFieldResponse",
    "LocalObjectiveInputsResponse",
    "DropdownOption",
    "DependentInputRequest",
    "DependentInputResponse",
    "NestedFunctionRequest",
    "NestedFunctionResponse",
    "ValidationResult",
    "InputValidationRequest",
    "InputValidationResponse",
    "ErrorResponse",
    "SuccessResponse",
    
    # Multi-select Models
    "MultiSelectValue",
    "MultiSelectOption",
    
    # Nested Function Models
    "NestedFunctionDefinition",
    "NestedFunctionExecutionResult",
    
    # Data Source Models
    "DropdownDataSource",
    
    # Execution Models
    "InputExecutionRecord",
    "OutputExecutionRecord",
    
    # Services
    "LocalObjectivesService",
    "RBACService",
    "NestedFunctionService",
    "DropdownService"
]
