import unittest
import datetime
from typing import List
import sys
import os

# Add project root to path if needed
project_root = '/home/<USER>/workflow-system/runtime/workflow-engine'
if project_root not in sys.path:
    sys.path.insert(0, project_root)

# Import the function to test
from app.services.system_functions import count_business_days

class TestCountBusinessDays(unittest.TestCase):
    def test_same_day(self):
        """Test counting business days when start and end dates are the same."""
        # Monday (weekday)
        result = count_business_days("2023-01-02", "2023-01-02")
        self.assertEqual(result, 1)
        
        # Saturday (weekend)
        result = count_business_days("2023-01-07", "2023-01-07")
        self.assertEqual(result, 0)
    
    def test_normal_week(self):
        """Test counting business days in a normal week (no holidays)."""
        # Monday to Friday (5 business days)
        result = count_business_days("2023-01-02", "2023-01-06")
        self.assertEqual(result, 5)
        
        # Monday to Saturday (5 business days)
        result = count_business_days("2023-01-02", "2023-01-07")
        self.assertEqual(result, 5)
        
        # Monday to Sunday (5 business days)
        result = count_business_days("2023-01-02", "2023-01-08")
        self.assertEqual(result, 5)
    
    def test_weekend_span(self):
        """Test counting business days across a weekend."""
        # Friday to Monday (2 business days)
        result = count_business_days("2023-01-06", "2023-01-09")
        self.assertEqual(result, 2)
        
        # Saturday to Tuesday (2 business days)
        result = count_business_days("2023-01-07", "2023-01-10")
        self.assertEqual(result, 2)
    
    def test_multiple_weeks(self):
        """Test counting business days across multiple weeks."""
        # 2 complete weeks (10 business days)
        result = count_business_days("2023-01-02", "2023-01-15")
        self.assertEqual(result, 10)
        
        # 4 complete weeks (20 business days)
        result = count_business_days("2023-01-02", "2023-01-29")
        self.assertEqual(result, 20)
    
    def test_month_boundary(self):
        """Test counting business days across month boundaries."""
        # January 30 (Monday) to February 3 (Friday) - 5 business days
        result = count_business_days("2023-01-30", "2023-02-03")
        self.assertEqual(result, 5)
    
    def test_with_holidays(self):
        """Test counting business days with holidays excluded."""
        # Monday to Friday with one holiday
        holidays = ["2023-01-04"]  # Wednesday is a holiday
        result = count_business_days("2023-01-02", "2023-01-06", holidays)
        self.assertEqual(result, 4)  # 5 weekdays - 1 holiday
        
        # Monday to Friday with multiple holidays
        holidays = ["2023-01-03", "2023-01-05"]  # Tuesday and Thursday are holidays
        result = count_business_days("2023-01-02", "2023-01-06", holidays)
        self.assertEqual(result, 3)  # 5 weekdays - 2 holidays
    
    def test_holiday_on_weekend(self):
        """Test with holiday on weekend (should not affect the count)."""
        # Monday to Friday with weekend holiday
        holidays = ["2023-01-07"]  # Saturday is a holiday
        result = count_business_days("2023-01-02", "2023-01-06", holidays)
        self.assertEqual(result, 5)  # No effect as holiday is on weekend
    
    def test_invalid_holiday_format(self):
        """Test with invalid holiday date format (should be ignored)."""
        holidays = ["2023/01/04", "2023-01-05"]  # First date has invalid format
        result = count_business_days("2023-01-02", "2023-01-06", holidays)
        self.assertEqual(result, 4)  # Only the valid holiday is excluded
    
    def test_reversed_dates(self):
        """Test with end date before start date."""
        # Dates should be swapped automatically
        result = count_business_days("2023-01-06", "2023-01-02")
        self.assertEqual(result, 5)
    
    def test_long_period(self):
        """Test with a longer period spanning multiple months."""
        # January 2 to March 31, 2023
        # Calculate expected business days manually
        start_date = datetime.date(2023, 1, 2)
        end_date = datetime.date(2023, 3, 31)
        
        expected_business_days = 0
        current = start_date
        while current <= end_date:
            if current.weekday() < 5:  # Monday to Friday
                expected_business_days += 1
            current += datetime.timedelta(days=1)
        
        # Use the calculated value
        result = count_business_days("2023-01-02", "2023-03-31")
        self.assertEqual(result, expected_business_days)  # Should be 65
    
    def test_with_many_holidays(self):
        """Test with a longer period and multiple holidays."""
        # All Mondays in January and February 2023 as holidays
        holidays = [
            "2023-01-02", "2023-01-09", "2023-01-16", "2023-01-23", "2023-01-30",
            "2023-02-06", "2023-02-13", "2023-02-20", "2023-02-27"
        ]
        
        # January 2 to February 28, 2023
        # Calculate expected business days
        start_date = datetime.date(2023, 1, 2)
        end_date = datetime.date(2023, 2, 28)
        
        # Convert holidays to date objects
        holiday_dates = set()
        for holiday in holidays:
            try:
                holiday_date = datetime.datetime.strptime(holiday, "%Y-%m-%d").date()
                holiday_dates.add(holiday_date)
            except ValueError:
                pass
        
        expected_business_days = 0
        current = start_date
        while current <= end_date:
            if current.weekday() < 5 and current not in holiday_dates:  # Weekday and not holiday
                expected_business_days += 1
            current += datetime.timedelta(days=1)
        
        result = count_business_days("2023-01-02", "2023-02-28", holidays)
        self.assertEqual(result, expected_business_days)  # Should be 33
    
    def test_empty_holidays_list(self):
        """Test with an empty holidays list."""
        result = count_business_days("2023-01-02", "2023-01-06", [])
        self.assertEqual(result, 5)  # Same as no holidays
    
    def test_none_holidays(self):
        """Test with None as holidays list."""
        result = count_business_days("2023-01-02", "2023-01-06", None)
        self.assertEqual(result, 5)  # Same as no holidays
    
    def test_duplicate_holidays(self):
        """Test with duplicate holidays (should only count once)."""
        holidays = ["2023-01-04", "2023-01-04"]  # Same holiday twice
        result = count_business_days("2023-01-02", "2023-01-06", holidays)
        self.assertEqual(result, 4)  # Only counted once
    
    def test_invalid_date_format(self):
        """Test with invalid date format (should raise ValueError)."""
        with self.assertRaises(ValueError):
            count_business_days("2023/01/02", "2023-01-06")
        
        with self.assertRaises(ValueError):
            count_business_days("2023-01-02", "2023.01.06")

if __name__ == '__main__':
    unittest.main()