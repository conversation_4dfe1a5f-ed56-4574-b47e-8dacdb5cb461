tenant:
  id: t001
  name: DermatologyLIMS001
  roles:
  - id: r001
    name: Lab Technician
    inherits_from: null
    access:
      entities:
      - entity_id: e014
        permissions:
        - Read
        - Create
        - Update
      - entity_id: e002
        permissions:
        - Read
        - Create
      - entity_id: e003
        permissions:
        - Read
        - Create
      - entity_id: e004
        permissions:
        - Read
      - entity_id: e005
        permissions:
        - Read
      - entity_id: e006
        permissions:
        - Read
      - entity_id: e007
        permissions:
        - Read
      - entity_id: e008
        permissions:
        - Read
      objectives:
      - objective_id: go003.lo012
        permissions:
        - Execute
      - objective_id: go003.lo002
        permissions:
        - Execute
  - id: r002
    name: Lab Manager
    inherits_from: null
    access:
      entities:
      - entity_id: e014
        permissions:
        - Read
        - Create
        - Update
      - entity_id: e002
        permissions:
        - Read
        - Create
        - Update
      - entity_id: e003
        permissions:
        - Read
        - Create
        - Update
      - entity_id: e004
        permissions:
        - Read
        - Create
        - Update
      - entity_id: e005
        permissions:
        - Read
        - Create
        - Update
      - entity_id: e006
        permissions:
        - Read
        - Create
        - Update
      - entity_id: e007
        permissions:
        - Read
      - entity_id: e008
        permissions:
        - Read
      objectives:
      - objective_id: go003.lo012
        permissions:
        - Execute
      - objective_id: go003.lo002
        permissions:
        - Execute
      - objective_id: go003.lo003
        permissions:
        - Execute
  - id: r003
    name: Dermatologist
    inherits_from: null
    access:
      entities:
      - entity_id: e014
        permissions:
        - Read
      - entity_id: e002
        permissions:
        - Read
        - Create
      - entity_id: e003
        permissions:
        - Read
      - entity_id: e004
        permissions:
        - Read
        - Create
        - Update
      - entity_id: e005
        permissions:
        - Read
      - entity_id: e006
        permissions:
        - Read
      - entity_id: e007
        permissions:
        - Read
      - entity_id: e008
        permissions:
        - Read
      objectives:
      - objective_id: go003.lo004
        permissions:
        - Execute
  - id: r004
    name: Administrator
    inherits_from: null
    access:
      entities:
      - entity_id: e014
        permissions:
        - Read
        - Create
        - Update
        - Delete
      - entity_id: e002
        permissions:
        - Read
        - Create
        - Update
        - Delete
      - entity_id: e003
        permissions:
        - Read
        - Create
        - Update
        - Delete
      - entity_id: e004
        permissions:
        - Read
        - Create
        - Update
        - Delete
      - entity_id: e005
        permissions:
        - Read
        - Create
        - Update
        - Delete
      - entity_id: e006
        permissions:
        - Read
        - Create
        - Update
        - Delete
      - entity_id: e007
        permissions:
        - Read
        - Create
        - Update
        - Delete
      - entity_id: e008
        permissions:
        - Read
        - Create
        - Update
        - Delete
      objectives:
      - objective_id: go003.lo012
        permissions:
        - Execute
      - objective_id: go003.lo002
        permissions:
        - Execute
      - objective_id: go003.lo003
        permissions:
        - Execute
      - objective_id: go003.lo004
        permissions:
        - Execute
workflow_data:
  software_type: Laboratory Information Management System
  industry: Healthcare - Dermatology
  version: '1.0'
  created_by: system
  created_on: '{{timestamp}}'
permission_types:
- id: read
  description: Can read entity data
  capabilities:
  - GET
- id: create
  description: Can create new entity records
  capabilities:
  - POST
- id: update
  description: Can update existing records
  capabilities:
  - PUT
- id: delete
  description: Can delete entity records
  capabilities:
  - DELETE
- id: execute
  description: Can execute workflows
  capabilities:
  - EXECUTE
entities:
- id: e014
  name: SpecimenSample
  description: Represents a biological specimen collected for testing
  version: '1.0'
  status: Active
  type: Core
  attributes_metadata:
    attribute_prefix: at
    attribute_map:
      sample_id: at137
      patient_id: at138
      collection_date: at139
      sample_type: at140
      collection_site: at141
      status: at142
      storage_conditions: at143
      created_by: at144
      created_at: at145
    required_attributes:
    - at137
    - at138
    - at139
    - at140
    - at141
    - at142
    - at143
    - at144
    - at145
  attributes:
  - id: at137
    name: sample_id
    display_name: Sample ID
    data_type: String
    datatype: String
    required: true
    version: '1.0'
    status: Active
  - id: at138
    name: patient_id
    display_name: Patient ID
    data_type: String
    datatype: String
    required: true
    version: '1.0'
    status: Active
  - id: at139
    name: collection_date
    display_name: Collection Date
    data_type: DateTime
    datatype: DateTime
    required: true
    version: '1.0'
    status: Active
  - id: at140
    name: sample_type
    display_name: Sample Type
    data_type: String
    datatype: String
    required: true
    version: '1.0'
    status: Active
  - id: at141
    name: collection_site
    display_name: Collection Site
    data_type: String
    datatype: String
    required: true
    version: '1.0'
    status: Active
  - id: at142
    name: status
    display_name: Status
    data_type: String
    datatype: String
    required: true
    version: '1.0'
    status: Active
  - id: at143
    name: storage_conditions
    display_name: Storage Conditions
    data_type: String
    datatype: String
    required: true
    version: '1.0'
    status: Active
  - id: at144
    name: created_by
    display_name: Created By
    data_type: String
    datatype: String
    required: true
    version: '1.0'
    status: Active
  - id: at145
    name: created_at
    display_name: Created At
    data_type: DateTime
    datatype: DateTime
    required: true
    version: '1.0'
    status: Active
- id: e007
  name: User
  description: System user information
  version: '1.0'
  status: Active
  type: Core
  attributes_metadata:
    attribute_prefix: at
    attribute_map:
      at701: user_id
      at702: username
      at703: email
      at704: role_id
      at705: status
      at706: first_name
      at707: last_name
    required_attributes:
    - at089
    - at090
    - at091
    - at032
    - at094
  attributes:
  - id: at089
    name: user_id
    display_name: User ID
    data_type: String
    datatype: String
    required: true
    version: '1.0'
    status: Active
  - id: at090
    name: username
    display_name: Username
    data_type: String
    datatype: String
    required: true
    version: '1.0'
    status: Active
  - id: at091
    name: email
    display_name: Email
    data_type: String
    datatype: String
    required: true
    version: '1.0'
    status: Active
  - id: at032
    name: role_id
    display_name: Role ID
    data_type: String
    datatype: String
    required: true
    version: '1.0'
    status: Active
  - id: at094
    name: status
    display_name: Status
    data_type: String
    datatype: String
    required: true
    version: '1.0'
    status: Active
  - id: at092
    name: first_name
    display_name: First Name
    data_type: String
    datatype: String
    required: false
    version: '1.0'
    status: Active
  - id: at093
    name: last_name
    display_name: Last Name
    data_type: String
    datatype: String
    required: false
    version: '1.0'
    status: Active
- id: e008
  name: Role
  description: User role definitions
  version: '1.0'
  status: Active
  type: Core
  attributes_metadata:
    attribute_prefix: at
    attribute_map:
      at801: role_id
      at802: name
      at803: description
      at804: status
      at805: permissions
    required_attributes:
    - at095
    - at096
    - at097
    - at059
    - at036
  attributes:
  - id: at095
    name: role_id
    display_name: Role ID
    data_type: String
    datatype: String
    required: true
    version: '1.0'
    status: Active
  - id: at096
    name: name
    display_name: Name
    data_type: String
    datatype: String
    required: true
    version: '1.0'
    status: Active
  - id: at097
    name: description
    display_name: Description
    data_type: String
    datatype: String
    required: true
    version: '1.0'
    status: Active
  - id: at059
    name: status
    display_name: Status
    data_type: String
    datatype: String
    required: true
    version: '1.0'
    status: Active
  - id: at036
    name: permissions
    display_name: Permissions
    data_type: String
    datatype: String
    required: true
    version: '1.0'
    status: Active
global_objectives:
- id: go003
  name: Dermatology Lab Sample Processing Workflow
  description: End-to-end workflow for processing dermatology specimens from collection
    to result reporting
  version: '1.0'
  status: Active
  context: {}
  input_stack:
    description: Input parameters for the workflow
    inputs:
    - id: in001
      contextual_id: go003.in001
      slot_id: go003.in001.slot
      name: Patient Information
      data_type: Object
      required: true
      source:
        description: Patient data from registration
        type: system
  output_stack:
    description: Output parameters from the workflow
    outputs:
    - id: out001
      contextual_id: go003.out001
      slot_id: go003.out001.slot
      name: Test Results
      data_type: Object
      source:
        description: Final test results
        type: system
      triggers:
        type: on_completion
        action: notify
        description: Trigger notification when test results are ready
        items:
        - id: tr001
          type: email
          template: test_results_ready
          recipients:
          - patient
          - doctor
          target_objective: go003.lo002
          target_input: in001
          mapping_type: direct
  data_mapping_stack:
    description: Data mappings between workflow steps
    mappings:
    - id: map001
      mapping_type: direct
      source: lo012.out001
      target: lo002.in001
    - id: map002
      mapping_type: direct
      source: lo002.out001
      target: lo003.in001
local_objectives:
- id: lo012
  name: Register Sample and Create Test Request
  description: Register a new sample in the lab system and create associated test
    request
  contextual_id: go003.lo012
  agent_stack:
    agents:
    - agent_id: a001
      name: Lab Technician
      role: r001
      rights:
      - execute
      - read
      - write
      users: []
  function_type: Create
  workflow_source: origin
  input_stack:
    description: Input parameters for sample registration
    inputs:
    - id: in001
      contextual_id: lo012.in001
      slot_id: lo012.in001.slot
      name: Patient ID
      data_type: String
      required: true
      source:
        description: Patient identifier from registration
        type: user
        dropdown_source:
          fetch_function: fetch_records
          parameters:
            table: e004
            value_column: at401
            display_column: at402,at403
            limit: 50
  output_stack:
    description: Output parameters from sample registration
    outputs:
    - id: out001
      contextual_id: lo012.out001
      slot_id: lo012.out001.slot
      name: Sample ID
      data_type: String
      source:
        description: Generated sample identifier
        type: system
  data_mapping_stack:
    description: Data mappings for sample registration
    mappings:
    - id: map001
      mapping_type: direct
      source: lo012.out001
      target: lo002.in001
    - id: map002
      mapping_type: direct
      source: lo012.out002
      target: lo002.in002
  entity:
    id: e014
    attributes:
    - at101
    - at102
    - at103
    - at104
    - at105
    - at106
    - at107
    - at111
    - at112
  execution_pathway:
    type: sequential
    next_lo: lo002
  go_id: go003
