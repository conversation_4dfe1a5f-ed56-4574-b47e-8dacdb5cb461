{"timestamp": "2025-06-24T13:11:51.179162", "endpoint": "fetch/security-properties", "input": {}, "output": {"success": true, "postgres_security_properties": [{"id": 1, "security_property_id": "SEC5", "entity_id": "E15", "attribute_id": "E15.At1", "classification": "confidential", "pii_type": "name", "encryption_required": false, "encryption_type": "", "masking_required": true, "masking_pattern": "EMP***", "access_level": "read_restricted", "audit_trail": true, "data_residency": "global", "retention_override": "", "anonymization_required": true, "anonymization_method": "tokenize", "compliance_frameworks": "gdpr, hipaa", "data_purpose": "", "consent_required": false, "cross_border_transfer": false, "backup_encryption": false, "secure_deletion": false, "natural_language": "Entity: Employee\nAttribute: employeeId\nClassification: confidential\nPII Type: name\nMasking Required: true\nMasking Pattern: EMP***\nAccess Level: read_restricted\nAudit Trail: true\nData Residency: global\nAnonymization Required: true\nAnonymization Method: tokenize\nCompliance Frameworks: gdpr, hipaa", "version": 2, "status": "deployed_to_temp", "created_by": "system", "updated_by": "system", "created_at": "2025-06-24T11:44:47.757192", "updated_at": "2025-06-24T11:44:47.757192", "security_property_status": "new", "changes_detected": []}], "mongo_drafts": [{"_id": "6855330bcc6fc2d42ba9e4a2", "security_property_id": "SEC3", "entity_id": "E15", "attribute_id": "E15.At1", "classification": "confidential", "pii_type": "name", "encryption_required": false, "encryption_type": "", "masking_required": true, "masking_pattern": "EMP***", "access_level": "read_restricted", "audit_trail": true, "data_residency": "global", "retention_override": "", "anonymization_required": true, "anonymization_method": "tokenize", "compliance_frameworks": "gdpr, hipaa", "data_purpose": "", "consent_required": false, "cross_border_transfer": false, "backup_encryption": false, "secure_deletion": false, "natural_language": "Entity: Employee\nAttribute: employeeId\nClassification: confidential\nPII Type: name\nMasking Required: true\nMasking Pattern: EMP***\nAccess Level: read_restricted\nAudit Trail: true\nData Residency: global\nAnonymization Required: true\nAnonymization Method: tokenize\nCompliance Frameworks: gdpr, hipaa", "version": 3, "status": "deployed_to_production", "created_at": "2025-06-20T10:08:11.956898", "updated_at": "2025-06-24T11:44:47.760460", "created_by": "<PERSON><PERSON>", "updated_by": "<PERSON><PERSON>", "security_property_status": "new", "changes_detected": []}], "total_postgres": 1, "total_drafts": 1, "operation": "fetch"}, "status": "success"}