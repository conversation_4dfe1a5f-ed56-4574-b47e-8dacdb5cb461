"""
Database Connection Module for YAML Builder

This module provides functions for connecting to the database.
"""

import os
import psycopg2
import logging
from typing import Optional

# Set up logging
logger = logging.getLogger('db_connection')

def get_db_connection() -> Optional[psycopg2.extensions.connection]:
    """
    Get a connection to the PostgreSQL database.
    
    Returns:
        Connection object or None if connection fails
    """
    try:
        # Get connection parameters from environment variables or use defaults
        host = os.environ.get('DB_HOST', '**********')
        port = os.environ.get('DB_PORT', '5432')
        dbname = os.environ.get('DB_NAME', 'workflow_system')
        user = os.environ.get('DB_USER', 'postgres')
        password = os.environ.get('DB_PASSWORD', 'workflow_postgres_secure_password')
        
        # Connect to the database
        conn = psycopg2.connect(
            host=host,
            port=port,
            dbname=dbname,
            user=user,
            password=password
        )
        
        logger.info(f"Connected to database {dbname} on {host}:{port}")
        return conn
    except Exception as e:
        logger.error(f"Database connection error: {str(e)}", exc_info=True)
        return None

def get_mongo_connection_string() -> str:
    """
    Get the MongoDB connection string.
    
    Returns:
        MongoDB connection string
    """
    # Get connection string from environment variable or use default
    return os.environ.get('MONGODB_URI', 
                         "mongodb://yaml_user:StrongPassword123!@localhost:27017/workflow_chat?authSource=workflow_chat")
