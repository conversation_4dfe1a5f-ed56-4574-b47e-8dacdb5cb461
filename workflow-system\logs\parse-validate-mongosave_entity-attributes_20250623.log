{"timestamp": "2025-06-23T06:36:14.310257", "endpoint": "parse-validate-mongosave/entity-attributes", "input": {"natural_language": "Tenant: Acme Corp Test\n\nEntity: Employee\nEntity ID: E45\n\nAttributes:\nAttribute Name | Display Name | Data Type | Required | Unique | Default Type | Default Value | Description | Helper Text\n\nemployeeId | Employee ID | string | true | true | computation | EMP-{sequence} | Unique employee identifier | Auto-generated\n\nfirstName | First Name | string | true | false | static value | | Employee first name | Enter first name\n\nlastName | Last Name | string | true | false | static value | | Employee last name | Enter last name\n\nemploymentStatus | Employment Status | string | true | false | static value | Active | Current employment status | Select status", "entity_context": null, "edit_draft": false, "edit_temp": false, "edit_production": false}, "output": {"success": true, "attribute_results": [{"success": true, "saved_data": {"attribute_id": ".At1", "entity_id": "", "name": "employeeId", "display_name": "Employee ID", "datatype": "string", "is_primary_key": false, "is_foreign_key": false, "is_required": true, "is_unique": true, "default_type": "computation", "default_value": "EMP-{sequence}", "description": "Unique employee identifier", "helper_text": "Auto-generated", "is_calculated": true, "calculation_formula": "EMP-{sequence}", "version": 1, "status": "draft", "natural_language": "Display Name: Employee ID\nName: employeeId\nData Type: string\nRequired: true\nUnique: true\nCalculated: true\nDefault Type: computation\nDefault Value: EMP-{sequence}\nCalculation Formula: EMP-{sequence}\nDescription: Unique employee identifier\nHelper Text: Auto-generated", "created_at": "2025-06-23T06:36:14.276890", "updated_at": "2025-06-23T06:36:14.276897", "created_by": "Tarun", "updated_by": "Tarun", "attribute_status": "new", "changes_detected": [], "_id": "6858f5de1195bbcbf7aa7693"}, "validation_result": {"structure_errors": [], "required_field_errors": [], "data_type_errors": [], "custom_errors": []}, "dependency_errors": [], "uniqueness_result": {"is_unique": true, "status": "unique", "message": "Attribute with attribute_id .At1 is unique"}}, {"success": true, "saved_data": {"attribute_id": ".At2", "entity_id": "", "name": "firstName", "display_name": "First Name", "datatype": "string", "is_primary_key": false, "is_foreign_key": false, "is_required": true, "is_unique": false, "default_type": "static value", "default_value": "", "description": "Employee first name", "helper_text": "Enter first name", "is_calculated": false, "calculation_formula": "", "version": 1, "status": "draft", "natural_language": "Display Name: First Name\nName: firstName\nData Type: string\nRequired: true\nDefault Type: static value\nDescription: Employee first name\nHelper Text: Enter first name", "created_at": "2025-06-23T06:36:14.286765", "updated_at": "2025-06-23T06:36:14.286773", "created_by": "Tarun", "updated_by": "Tarun", "attribute_status": "new", "changes_detected": [], "_id": "6858f5de1195bbcbf7aa7694"}, "validation_result": {"structure_errors": [], "required_field_errors": [], "data_type_errors": [], "custom_errors": []}, "dependency_errors": [], "uniqueness_result": {"is_unique": true, "status": "unique", "message": "Attribute with attribute_id .At2 is unique"}}, {"success": true, "saved_data": {"attribute_id": ".At3", "entity_id": "", "name": "lastName", "display_name": "Last Name", "datatype": "string", "is_primary_key": false, "is_foreign_key": false, "is_required": true, "is_unique": false, "default_type": "static value", "default_value": "", "description": "Employee last name", "helper_text": "Enter last name", "is_calculated": false, "calculation_formula": "", "version": 1, "status": "draft", "natural_language": "Display Name: Last Name\nName: lastName\nData Type: string\nRequired: true\nDefault Type: static value\nDescription: Employee last name\nHelper Text: Enter last name", "created_at": "2025-06-23T06:36:14.298690", "updated_at": "2025-06-23T06:36:14.298698", "created_by": "Tarun", "updated_by": "Tarun", "attribute_status": "new", "changes_detected": [], "_id": "6858f5de1195bbcbf7aa7695"}, "validation_result": {"structure_errors": [], "required_field_errors": [], "data_type_errors": [], "custom_errors": []}, "dependency_errors": [], "uniqueness_result": {"is_unique": true, "status": "unique", "message": "Attribute with attribute_id .At3 is unique"}}, {"success": true, "saved_data": {"attribute_id": ".At4", "entity_id": "", "name": "employmentStatus", "display_name": "Employment Status", "datatype": "string", "is_primary_key": false, "is_foreign_key": false, "is_required": true, "is_unique": false, "default_type": "static value", "default_value": "Active", "description": "Current employment status", "helper_text": "Select status", "is_calculated": false, "calculation_formula": "", "version": 1, "status": "draft", "natural_language": "Display Name: Employment Status\nName: employmentStatus\nData Type: string\nRequired: true\nDefault Type: static value\nDefault Value: Active\nDescription: Current employment status\nHelper Text: Select status", "created_at": "2025-06-23T06:36:14.308383", "updated_at": "2025-06-23T06:36:14.308390", "created_by": "Tarun", "updated_by": "Tarun", "attribute_status": "new", "changes_detected": [], "_id": "6858f5de1195bbcbf7aa7696"}, "validation_result": {"structure_errors": [], "required_field_errors": [], "data_type_errors": [], "custom_errors": []}, "dependency_errors": [], "uniqueness_result": {"is_unique": true, "status": "unique", "message": "Attribute with attribute_id .At4 is unique"}}], "operation": "parse_validate_mongosave", "total_attributes": 4}, "status": "success"}
{"timestamp": "2025-06-23T11:29:23.094443", "endpoint": "parse-validate-mongosave/entity-attributes", "input": {"natural_language": "LeaveApplication has leaveId^PK, employeeId^FK, startDate, endDate, numDays[derived], reason, leaveTypeName, leaveSubTypeName, status, requiresDocumentation, documentationProvided, submissionDate, approvalDate, approvedBy^FK, comments, isRetroactive[derived], insufficientBalance[derived], lowTeamAvailability[derived], allowedNumberOfDays.\n\nAttributes:\nAttribute Name | Display Name | Data Type | Required | Unique | Default Type | Default Value | Description | Helper Text\n\nleaveId | Leave ID | string | true | true | computation | LV-{YYYY}-{sequence} | Unique identifier for the leave application | Auto-generated leave identifier\n\nemployeeId | Employee ID | string | true | false | static value | | References the employee requesting leave | Select employee from dropdown\n\nstartDate | Leave Start Date | date | true | false | static value | | First day of the requested leave period | Select start date for leave\n\nendDate | Leave End Date | date | true | false | static value | | Last day of the requested leave period | Select end date for leave\n\nnumDays | Number of Days | integer | true | false | computation | DATEDIFF(endDate, startDate) + 1 | Total number of leave days requested | Automatically calculated based on start and end dates", "entity_context": {"entity_id": "E7", "entity_name": "LeaveApplication", "tenant_id": "T2"}, "edit_draft": false, "edit_temp": false, "edit_production": false}, "output": {"success": true, "attribute_results": [{"success": false, "error": "Item already exists in database. Use edit_production=true to update.", "existing_in": "postgres", "parsed_data": {"attribute_id": "E7.At1", "entity_id": "E7", "name": "leaveId", "display_name": "Leave ID", "datatype": "string", "is_primary_key": true, "is_foreign_key": false, "is_required": true, "is_unique": true, "default_type": "computation", "default_value": "LV-{YYYY}-{sequence}", "description": "Unique identifier for the leave application", "helper_text": "Auto-generated leave identifier", "is_calculated": true, "calculation_formula": "LV-{YYYY}-{sequence}", "version": 1, "status": "draft", "natural_language": "Display Name: Leave ID\nName: leaveId\nData Type: string\nRequired: true\nUnique: true\nCalculated: true\nDefault Type: computation\nDefault Value: LV-{YYYY}-{sequence}\nCalculation Formula: LV-{YYYY}-{sequence}\nDescription: Unique identifier for the leave application\nHelper Text: Auto-generated leave identifier", "created_at": "2025-06-23T11:29:22.512731", "updated_at": "2025-06-23T11:29:22.512731", "created_by": "Tarun", "updated_by": "Tarun", "attribute_status": "existing", "changes_detected": []}}, {"success": false, "error": "Item already exists in database. Use edit_production=true to update.", "existing_in": "postgres", "parsed_data": {"attribute_id": "E7.At2", "entity_id": "E7", "name": "employeeId", "display_name": "Employee ID", "datatype": "string", "is_primary_key": false, "is_foreign_key": true, "is_required": true, "is_unique": false, "default_type": "static value", "default_value": "", "description": "References the employee requesting leave", "helper_text": "Select employee from dropdown", "is_calculated": false, "calculation_formula": "", "version": 1, "status": "draft", "natural_language": "Display Name: Employee ID\nName: employeeId\nData Type: string\nRequired: true\nDefault Type: static value\nDescription: References the employee requesting leave\nHelper Text: Select employee from dropdown", "created_at": "2025-06-23T11:29:22.528894", "updated_at": "2025-06-23T11:29:22.528894", "created_by": "Tarun", "updated_by": "Tarun", "attribute_status": "existing", "changes_detected": []}}, {"success": false, "error": "Item already exists in database. Use edit_production=true to update.", "existing_in": "postgres", "parsed_data": {"attribute_id": "E7.At3", "entity_id": "E7", "name": "startDate", "display_name": "Leave Start Date", "datatype": "string", "is_primary_key": false, "is_foreign_key": false, "is_required": true, "is_unique": false, "default_type": "static value", "default_value": "", "description": "First day of the requested leave period", "helper_text": "Select start date for leave", "is_calculated": false, "calculation_formula": "", "version": 1, "status": "draft", "natural_language": "Display Name: Leave Start Date\nName: startDate\nData Type: date\nRequired: true\nDefault Type: static value\nDescription: First day of the requested leave period\nHelper Text: Select start date for leave", "created_at": "2025-06-23T11:29:22.543684", "updated_at": "2025-06-23T11:29:22.543684", "created_by": "Tarun", "updated_by": "Tarun", "attribute_status": "existing", "changes_detected": []}}, {"success": false, "error": "Item already exists in database. Use edit_production=true to update.", "existing_in": "postgres", "parsed_data": {"attribute_id": "E7.At4", "entity_id": "E7", "name": "endDate", "display_name": "Leave End Date", "datatype": "string", "is_primary_key": false, "is_foreign_key": false, "is_required": true, "is_unique": false, "default_type": "static value", "default_value": "", "description": "Last day of the requested leave period", "helper_text": "Select end date for leave", "is_calculated": false, "calculation_formula": "", "version": 1, "status": "draft", "natural_language": "Display Name: Leave End Date\nName: endDate\nData Type: date\nRequired: true\nDefault Type: static value\nDescription: Last day of the requested leave period\nHelper Text: Select end date for leave", "created_at": "2025-06-23T11:29:22.558244", "updated_at": "2025-06-23T11:29:22.558244", "created_by": "Tarun", "updated_by": "Tarun", "attribute_status": "existing", "changes_detected": []}}, {"success": false, "error": "Item already exists in database. Use edit_production=true to update.", "existing_in": "postgres", "parsed_data": {"attribute_id": "E7.At5", "entity_id": "E7", "name": "numDays", "display_name": "Number of Days", "datatype": "string", "is_primary_key": false, "is_foreign_key": false, "is_required": true, "is_unique": false, "default_type": "computation", "default_value": "DATEDIFF(endDate, startDate) + 1", "description": "Total number of leave days requested", "helper_text": "Automatically calculated based on start and end dates", "is_calculated": true, "calculation_formula": "DATEDIFF(endDate, startDate) + 1", "version": 1, "status": "draft", "natural_language": "Display Name: Number of Days\nName: numDays\nData Type: integer\nRequired: true\nCalculated: true\nDefault Type: computation\nDefault Value: DATEDIFF(endDate, startDate) + 1\nCalculation Formula: DATEDIFF(endDate, startDate) + 1\nDescription: Total number of leave days requested\nHelper Text: Automatically calculated based on start and end dates", "created_at": "2025-06-23T11:29:22.571425", "updated_at": "2025-06-23T11:29:22.571425", "created_by": "Tarun", "updated_by": "Tarun", "attribute_status": "existing", "changes_detected": []}}, {"success": false, "error": "Item already exists in database. Use edit_production=true to update.", "existing_in": "postgres", "parsed_data": {"attribute_id": "E7.At6", "entity_id": "E7", "name": "reason", "display_name": "reason", "datatype": "string", "is_primary_key": false, "is_foreign_key": false, "is_required": false, "is_unique": false, "default_type": "", "default_value": "", "description": "", "helper_text": "", "is_calculated": false, "calculation_formula": "", "version": 1, "status": "draft", "natural_language": "", "created_at": "2025-06-23T11:29:22.585913", "updated_at": "2025-06-23T11:29:22.585913", "created_by": "Tarun", "updated_by": "Tarun", "attribute_status": "existing", "changes_detected": []}}, {"success": false, "error": "Item already exists in database. Use edit_production=true to update.", "existing_in": "postgres", "parsed_data": {"attribute_id": "E7.At7", "entity_id": "E7", "name": "leaveTypeName", "display_name": "leaveTypeName", "datatype": "string", "is_primary_key": false, "is_foreign_key": false, "is_required": false, "is_unique": false, "default_type": "", "default_value": "", "description": "", "helper_text": "", "is_calculated": false, "calculation_formula": "", "version": 1, "status": "draft", "natural_language": "", "created_at": "2025-06-23T11:29:22.598291", "updated_at": "2025-06-23T11:29:22.598291", "created_by": "Tarun", "updated_by": "Tarun", "attribute_status": "existing", "changes_detected": []}}, {"success": false, "error": "Item already exists in database. Use edit_production=true to update.", "existing_in": "postgres", "parsed_data": {"attribute_id": "E7.At8", "entity_id": "E7", "name": "leaveSubTypeName", "display_name": "leaveSubTypeName", "datatype": "string", "is_primary_key": false, "is_foreign_key": false, "is_required": false, "is_unique": false, "default_type": "", "default_value": "", "description": "", "helper_text": "", "is_calculated": false, "calculation_formula": "", "version": 1, "status": "draft", "natural_language": "", "created_at": "2025-06-23T11:29:22.610093", "updated_at": "2025-06-23T11:29:22.610093", "created_by": "Tarun", "updated_by": "Tarun", "attribute_status": "existing", "changes_detected": []}}, {"success": false, "error": "Item already exists in database. Use edit_production=true to update.", "existing_in": "postgres", "parsed_data": {"attribute_id": "E7.At9", "entity_id": "E7", "name": "status", "display_name": "status", "datatype": "string", "is_primary_key": false, "is_foreign_key": false, "is_required": false, "is_unique": false, "default_type": "", "default_value": "", "description": "", "helper_text": "", "is_calculated": false, "calculation_formula": "", "version": 1, "status": "draft", "natural_language": "", "created_at": "2025-06-23T11:29:22.621246", "updated_at": "2025-06-23T11:29:22.621246", "created_by": "Tarun", "updated_by": "Tarun", "attribute_status": "existing", "changes_detected": []}}, {"success": false, "error": "Item already exists in database. Use edit_production=true to update.", "existing_in": "postgres", "parsed_data": {"attribute_id": "E7.At10", "entity_id": "E7", "name": "requiresDocumentation", "display_name": "requiresDocumentation", "datatype": "string", "is_primary_key": false, "is_foreign_key": false, "is_required": false, "is_unique": false, "default_type": "", "default_value": "", "description": "", "helper_text": "", "is_calculated": false, "calculation_formula": "", "version": 1, "status": "draft", "natural_language": "", "created_at": "2025-06-23T11:29:22.634664", "updated_at": "2025-06-23T11:29:22.634664", "created_by": "Tarun", "updated_by": "Tarun", "attribute_status": "existing", "changes_detected": []}}, {"success": false, "error": "Item already exists in database. Use edit_production=true to update.", "existing_in": "postgres", "parsed_data": {"attribute_id": "E7.At11", "entity_id": "E7", "name": "documentationProvided", "display_name": "documentationProvided", "datatype": "string", "is_primary_key": false, "is_foreign_key": false, "is_required": false, "is_unique": false, "default_type": "", "default_value": "", "description": "", "helper_text": "", "is_calculated": false, "calculation_formula": "", "version": 1, "status": "draft", "natural_language": "", "created_at": "2025-06-23T11:29:22.646037", "updated_at": "2025-06-23T11:29:22.646037", "created_by": "Tarun", "updated_by": "Tarun", "attribute_status": "existing", "changes_detected": []}}, {"success": false, "error": "Item already exists in database. Use edit_production=true to update.", "existing_in": "postgres", "parsed_data": {"attribute_id": "E7.At12", "entity_id": "E7", "name": "submissionDate", "display_name": "submissionDate", "datatype": "string", "is_primary_key": false, "is_foreign_key": false, "is_required": false, "is_unique": false, "default_type": "", "default_value": "", "description": "", "helper_text": "", "is_calculated": false, "calculation_formula": "", "version": 1, "status": "draft", "natural_language": "", "created_at": "2025-06-23T11:29:22.659996", "updated_at": "2025-06-23T11:29:22.659996", "created_by": "Tarun", "updated_by": "Tarun", "attribute_status": "existing", "changes_detected": []}}, {"success": false, "error": "Item already exists in database. Use edit_production=true to update.", "existing_in": "postgres", "parsed_data": {"attribute_id": "E7.At13", "entity_id": "E7", "name": "approvalDate", "display_name": "approvalDate", "datatype": "string", "is_primary_key": false, "is_foreign_key": false, "is_required": false, "is_unique": false, "default_type": "", "default_value": "", "description": "", "helper_text": "", "is_calculated": false, "calculation_formula": "", "version": 1, "status": "draft", "natural_language": "", "created_at": "2025-06-23T11:29:22.671825", "updated_at": "2025-06-23T11:29:22.671825", "created_by": "Tarun", "updated_by": "Tarun", "attribute_status": "existing", "changes_detected": []}}, {"success": false, "error": "Item already exists in database. Use edit_production=true to update.", "existing_in": "postgres", "parsed_data": {"attribute_id": "E7.At14", "entity_id": "E7", "name": "approvedBy", "display_name": "approvedBy", "datatype": "string", "is_primary_key": false, "is_foreign_key": true, "is_required": false, "is_unique": false, "default_type": "", "default_value": "", "description": "", "helper_text": "", "is_calculated": false, "calculation_formula": "", "version": 1, "status": "draft", "natural_language": "", "created_at": "2025-06-23T11:29:22.682508", "updated_at": "2025-06-23T11:29:22.682508", "created_by": "Tarun", "updated_by": "Tarun", "attribute_status": "existing", "changes_detected": []}}, {"success": false, "error": "Item already exists in database. Use edit_production=true to update.", "existing_in": "postgres", "parsed_data": {"attribute_id": "E7.At15", "entity_id": "E7", "name": "comments", "display_name": "comments", "datatype": "string", "is_primary_key": false, "is_foreign_key": false, "is_required": false, "is_unique": false, "default_type": "", "default_value": "", "description": "", "helper_text": "", "is_calculated": false, "calculation_formula": "", "version": 1, "status": "draft", "natural_language": "", "created_at": "2025-06-23T11:29:22.694513", "updated_at": "2025-06-23T11:29:22.694513", "created_by": "Tarun", "updated_by": "Tarun", "attribute_status": "existing", "changes_detected": []}}, {"success": false, "error": "Item already exists in database. Use edit_production=true to update.", "existing_in": "postgres", "parsed_data": {"attribute_id": "E7.At16", "entity_id": "E7", "name": "isRetroactive", "display_name": "isRetroactive", "datatype": "string", "is_primary_key": false, "is_foreign_key": false, "is_required": false, "is_unique": false, "default_type": "", "default_value": "", "description": "", "helper_text": "", "is_calculated": true, "calculation_formula": "", "version": 1, "status": "draft", "natural_language": "", "created_at": "2025-06-23T11:29:22.705849", "updated_at": "2025-06-23T11:29:22.705849", "created_by": "Tarun", "updated_by": "Tarun", "attribute_status": "existing", "changes_detected": []}}, {"success": false, "error": "Item already exists in database. Use edit_production=true to update.", "existing_in": "postgres", "parsed_data": {"attribute_id": "E7.At17", "entity_id": "E7", "name": "insufficientBalance", "display_name": "insufficientBalance", "datatype": "string", "is_primary_key": false, "is_foreign_key": false, "is_required": false, "is_unique": false, "default_type": "", "default_value": "", "description": "", "helper_text": "", "is_calculated": true, "calculation_formula": "", "version": 1, "status": "draft", "natural_language": "", "created_at": "2025-06-23T11:29:22.718508", "updated_at": "2025-06-23T11:29:22.718508", "created_by": "Tarun", "updated_by": "Tarun", "attribute_status": "existing", "changes_detected": []}}, {"success": false, "error": "Item already exists in database. Use edit_production=true to update.", "existing_in": "postgres", "parsed_data": {"attribute_id": "E7.At18", "entity_id": "E7", "name": "lowTeamAvailability", "display_name": "lowTeamAvailability", "datatype": "string", "is_primary_key": false, "is_foreign_key": false, "is_required": false, "is_unique": false, "default_type": "", "default_value": "", "description": "", "helper_text": "", "is_calculated": true, "calculation_formula": "", "version": 1, "status": "draft", "natural_language": "", "created_at": "2025-06-23T11:29:22.730081", "updated_at": "2025-06-23T11:29:22.730081", "created_by": "Tarun", "updated_by": "Tarun", "attribute_status": "existing", "changes_detected": []}}, {"success": false, "error": "Item already exists in database. Use edit_production=true to update.", "existing_in": "postgres", "parsed_data": {"attribute_id": "E7.At19", "entity_id": "E7", "name": "allowedNumberOfDays", "display_name": "allowedNumberOfDays", "datatype": "string", "is_primary_key": false, "is_foreign_key": false, "is_required": false, "is_unique": false, "default_type": "", "default_value": "", "description": "", "helper_text": "", "is_calculated": false, "calculation_formula": "", "version": 1, "status": "draft", "natural_language": "", "created_at": "2025-06-23T11:29:22.739879", "updated_at": "2025-06-23T11:29:22.739879", "created_by": "Tarun", "updated_by": "Tarun", "attribute_status": "existing", "changes_detected": []}}], "operation": "parse_validate_mongosave", "total_attributes": 19}, "status": "success"}
{"timestamp": "2025-06-23T14:07:59.081274", "endpoint": "parse-validate-mongosave/entity-attributes", "input": {"natural_language": "Add category field as string and in_stock field as boolean", "entity_context": {"entity_id": "product_entity", "entity_name": "Product"}, "edit_draft": false, "edit_temp": false, "edit_production": false}, "output": {"success": true, "attribute_results": [], "operation": "parse_validate_mongosave", "total_attributes": 0}, "status": "success"}
