#!/usr/bin/env python3
"""
Verify that relationship properties are correctly parsed from the sample file.
This script does not modify the database.
"""

import json
from parsers.entity_parser import parse_entities

def verify_relationship_parsing():
    """
    Verify that relationship properties are correctly parsed from the sample file.
    """
    # Read the sample entity output file
    sample_file_path = '/home/<USER>/workflow-system/chat-yaml-builder/v2/samples/sample_entity_output2.txt'
    with open(sample_file_path, 'r') as f:
        entity_def = f.read()

    # Parse the entity definition
    entities_data, warnings = parse_entities(entity_def)
    
    # Print any warnings
    if warnings:
        print("Warnings during parsing:")
        for warning in warnings:
            print(f"  - {warning}")
    
    # Check if Employee entity was parsed
    if 'entities' in entities_data and 'Employee' in entities_data['entities']:
        employee_entity = entities_data['entities']['Employee']
        
        # Check if relationships were parsed
        if 'relationships' in employee_entity:
            print("\nEmployee relationships:")
            for rel_name, rel_def in employee_entity['relationships'].items():
                print(f"\n  - Relationship: {rel_name}")
                print(f"    - Type: {rel_def.get('type', 'N/A')}")
                print(f"    - Entity: {rel_def.get('entity', 'N/A')}")
                print(f"    - Source Attribute: {rel_def.get('source_attribute', 'N/A')}")
                print(f"    - Target Attribute: {rel_def.get('target_attribute', 'N/A')}")
                
                # Check if relationship properties were parsed
                if 'properties' in rel_def:
                    print(f"    - Properties:")
                    for prop_name, prop_value in rel_def['properties'].items():
                        print(f"      - {prop_name}: {prop_value}")
                else:
                    print(f"    - No properties found for this relationship")
        else:
            print("No relationships found in Employee entity")
    else:
        print("Employee entity not found in parsed data")
    
    # Save the parsed data to a file for inspection
    with open('parsed_relationships.json', 'w') as f:
        json.dump(entities_data, f, indent=2)
    
    print("\nParsed data saved to parsed_relationships.json")

if __name__ == "__main__":
    verify_relationship_parsing()
