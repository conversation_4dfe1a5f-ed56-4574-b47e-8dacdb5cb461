--
-- PostgreSQL database dump
--

-- Dumped from database version 15.12 (Debian 15.12-1.pgdg120+1)
-- Dumped by pg_dump version 15.12 (Debian 15.12-1.pgdg120+1)

SET statement_timeout = 0;
SET lock_timeout = 0;
SET idle_in_transaction_session_timeout = 0;
SET client_encoding = 'UTF8';
SET standard_conforming_strings = on;
SELECT pg_catalog.set_config('search_path', '', false);
SET check_function_bodies = false;
SET xmloption = content;
SET client_min_messages = warning;
SET row_security = off;

--
-- Name: function_dev; Type: SCHEMA; Schema: -; Owner: postgres
--

CREATE SCHEMA function_dev;


ALTER SCHEMA function_dev OWNER TO postgres;

--
-- Name: tenant_t001; Type: SCHEMA; Schema: -; Owner: postgres
--

CREATE SCHEMA tenant_t001;


ALTER SCHEMA tenant_t001 OWNER TO postgres;

--
-- Name: workflow_runtime; Type: SCHEMA; Schema: -; Owner: postgres
--

CREATE SCHEMA workflow_runtime;


ALTER SCHEMA workflow_runtime OWNER TO postgres;

--
-- Name: uuid-ossp; Type: EXTENSION; Schema: -; Owner: -
--

CREATE EXTENSION IF NOT EXISTS "uuid-ossp" WITH SCHEMA public;


--
-- Name: EXTENSION "uuid-ossp"; Type: COMMENT; Schema: -; Owner: 
--

COMMENT ON EXTENSION "uuid-ossp" IS 'generate universally unique identifiers (UUIDs)';


--
-- Name: input_source_type; Type: TYPE; Schema: workflow_runtime; Owner: postgres
--

CREATE TYPE workflow_runtime.input_source_type AS ENUM (
    'user',
    'information',
    'system',
    'system_dependent'
);


ALTER TYPE workflow_runtime.input_source_type OWNER TO postgres;

--
-- Name: update_modified_column(); Type: FUNCTION; Schema: workflow_runtime; Owner: postgres
--

CREATE FUNCTION workflow_runtime.update_modified_column() RETURNS trigger
    LANGUAGE plpgsql
    AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$;


ALTER FUNCTION workflow_runtime.update_modified_column() OWNER TO postgres;

SET default_tablespace = '';

SET default_table_access_method = heap;

--
-- Name: agent_rights; Type: TABLE; Schema: workflow_runtime; Owner: postgres
--

CREATE TABLE workflow_runtime.agent_rights (
    id integer NOT NULL,
    agent_stack_id integer NOT NULL,
    role_id character varying(50) NOT NULL,
    right_id character varying(50) NOT NULL,
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE workflow_runtime.agent_rights OWNER TO postgres;

--
-- Name: agent_rights_id_seq; Type: SEQUENCE; Schema: workflow_runtime; Owner: postgres
--

CREATE SEQUENCE workflow_runtime.agent_rights_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE workflow_runtime.agent_rights_id_seq OWNER TO postgres;

--
-- Name: agent_rights_id_seq; Type: SEQUENCE OWNED BY; Schema: workflow_runtime; Owner: postgres
--

ALTER SEQUENCE workflow_runtime.agent_rights_id_seq OWNED BY workflow_runtime.agent_rights.id;


--
-- Name: agent_stack; Type: TABLE; Schema: workflow_runtime; Owner: postgres
--

CREATE TABLE workflow_runtime.agent_stack (
    id integer NOT NULL,
    lo_id character varying(50) NOT NULL,
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE workflow_runtime.agent_stack OWNER TO postgres;

--
-- Name: agent_stack_id_seq; Type: SEQUENCE; Schema: workflow_runtime; Owner: postgres
--

CREATE SEQUENCE workflow_runtime.agent_stack_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE workflow_runtime.agent_stack_id_seq OWNER TO postgres;

--
-- Name: agent_stack_id_seq; Type: SEQUENCE OWNED BY; Schema: workflow_runtime; Owner: postgres
--

ALTER SEQUENCE workflow_runtime.agent_stack_id_seq OWNED BY workflow_runtime.agent_stack.id;


--
-- Name: alembic_version; Type: TABLE; Schema: workflow_runtime; Owner: postgres
--

CREATE TABLE workflow_runtime.alembic_version (
    version_num character varying(32) NOT NULL
);


ALTER TABLE workflow_runtime.alembic_version OWNER TO postgres;

--
-- Name: asset_request; Type: TABLE; Schema: workflow_runtime; Owner: postgres
--

CREATE TABLE workflow_runtime.asset_request (
    id integer NOT NULL,
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    created_by character varying(255),
    updated_by character varying(255),
    request_id character varying(255) NOT NULL,
    employee_id character varying(255) NOT NULL,
    asset_type character varying(50) NOT NULL,
    specifications character varying(255),
    request_date date NOT NULL,
    approval_status character varying(50),
    approval_date date,
    approval_remarks character varying(255),
    CONSTRAINT chk_asset_request_approval_status CHECK (((approval_status)::text = ANY ((ARRAY['Approved'::character varying, 'Rejected'::character varying, 'Pending'::character varying])::text[]))),
    CONSTRAINT chk_asset_request_asset_type CHECK (((asset_type)::text = ANY ((ARRAY['Stationary'::character varying, 'System'::character varying, 'Furniture'::character varying, 'Electronics'::character varying, 'Software'::character varying])::text[])))
);


ALTER TABLE workflow_runtime.asset_request OWNER TO postgres;

--
-- Name: asset_request_id_seq; Type: SEQUENCE; Schema: workflow_runtime; Owner: postgres
--

CREATE SEQUENCE workflow_runtime.asset_request_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE workflow_runtime.asset_request_id_seq OWNER TO postgres;

--
-- Name: asset_request_id_seq; Type: SEQUENCE OWNED BY; Schema: workflow_runtime; Owner: postgres
--

ALTER SEQUENCE workflow_runtime.asset_request_id_seq OWNED BY workflow_runtime.asset_request.id;


--
-- Name: attribute_enum_values; Type: TABLE; Schema: workflow_runtime; Owner: postgres
--

CREATE TABLE workflow_runtime.attribute_enum_values (
    id integer NOT NULL,
    attribute_id character varying(50) NOT NULL,
    value character varying(100) NOT NULL,
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE workflow_runtime.attribute_enum_values OWNER TO postgres;

--
-- Name: attribute_enum_values_id_seq; Type: SEQUENCE; Schema: workflow_runtime; Owner: postgres
--

CREATE SEQUENCE workflow_runtime.attribute_enum_values_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE workflow_runtime.attribute_enum_values_id_seq OWNER TO postgres;

--
-- Name: attribute_enum_values_id_seq; Type: SEQUENCE OWNED BY; Schema: workflow_runtime; Owner: postgres
--

ALTER SEQUENCE workflow_runtime.attribute_enum_values_id_seq OWNED BY workflow_runtime.attribute_enum_values.id;


--
-- Name: attribute_ui_controls; Type: TABLE; Schema: workflow_runtime; Owner: postgres
--

CREATE TABLE workflow_runtime.attribute_ui_controls (
    entity_id text NOT NULL,
    attribute_id text NOT NULL,
    ui_control text NOT NULL,
    created_at timestamp without time zone DEFAULT now(),
    updated_at timestamp without time zone DEFAULT now()
);


ALTER TABLE workflow_runtime.attribute_ui_controls OWNER TO postgres;

--
-- Name: attribute_validations; Type: TABLE; Schema: workflow_runtime; Owner: postgres
--

CREATE TABLE workflow_runtime.attribute_validations (
    id integer NOT NULL,
    attribute_id character varying(50) NOT NULL,
    rule character varying(100) NOT NULL,
    expression text,
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE workflow_runtime.attribute_validations OWNER TO postgres;

--
-- Name: attribute_validations_id_seq; Type: SEQUENCE; Schema: workflow_runtime; Owner: postgres
--

CREATE SEQUENCE workflow_runtime.attribute_validations_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE workflow_runtime.attribute_validations_id_seq OWNER TO postgres;

--
-- Name: attribute_validations_id_seq; Type: SEQUENCE OWNED BY; Schema: workflow_runtime; Owner: postgres
--

ALTER SEQUENCE workflow_runtime.attribute_validations_id_seq OWNED BY workflow_runtime.attribute_validations.id;


--
-- Name: attribute_validations_id_seq1; Type: SEQUENCE; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE workflow_runtime.attribute_validations ALTER COLUMN id ADD GENERATED BY DEFAULT AS IDENTITY (
    SEQUENCE NAME workflow_runtime.attribute_validations_id_seq1
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);


--
-- Name: compliance_verification; Type: TABLE; Schema: workflow_runtime; Owner: postgres
--

CREATE TABLE workflow_runtime.compliance_verification (
    id integer NOT NULL,
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    created_by character varying(255),
    updated_by character varying(255),
    verification_id character varying(255),
    enrollment_id character varying(255),
    verification_date timestamp without time zone,
    verifier_id character varying(255),
    status character varying(50),
    comments character varying(255),
    audit_trail character varying(255),
    pass_fail_flag boolean,
    remediation_required boolean,
    regulation_reference character varying(255)
);


ALTER TABLE workflow_runtime.compliance_verification OWNER TO postgres;

--
-- Name: compliance_verification_id_seq; Type: SEQUENCE; Schema: workflow_runtime; Owner: postgres
--

CREATE SEQUENCE workflow_runtime.compliance_verification_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE workflow_runtime.compliance_verification_id_seq OWNER TO postgres;

--
-- Name: compliance_verification_id_seq; Type: SEQUENCE OWNED BY; Schema: workflow_runtime; Owner: postgres
--

ALTER SEQUENCE workflow_runtime.compliance_verification_id_seq OWNED BY workflow_runtime.compliance_verification.id;


--
-- Name: conditional_success_messages; Type: TABLE; Schema: workflow_runtime; Owner: postgres
--

CREATE TABLE workflow_runtime.conditional_success_messages (
    id integer NOT NULL,
    lo_id character varying(50) NOT NULL,
    condition_expression text,
    message text NOT NULL,
    is_default boolean DEFAULT false,
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE workflow_runtime.conditional_success_messages OWNER TO postgres;

--
-- Name: conditional_success_messages_id_seq; Type: SEQUENCE; Schema: workflow_runtime; Owner: postgres
--

CREATE SEQUENCE workflow_runtime.conditional_success_messages_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE workflow_runtime.conditional_success_messages_id_seq OWNER TO postgres;

--
-- Name: conditional_success_messages_id_seq; Type: SEQUENCE OWNED BY; Schema: workflow_runtime; Owner: postgres
--

ALTER SEQUENCE workflow_runtime.conditional_success_messages_id_seq OWNED BY workflow_runtime.conditional_success_messages.id;


--
-- Name: data_mapping_stack; Type: TABLE; Schema: workflow_runtime; Owner: postgres
--

CREATE TABLE workflow_runtime.data_mapping_stack (
    id integer NOT NULL,
    go_id character varying(50) NOT NULL,
    description text,
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE workflow_runtime.data_mapping_stack OWNER TO postgres;

--
-- Name: data_mapping_stack_id_seq; Type: SEQUENCE; Schema: workflow_runtime; Owner: postgres
--

CREATE SEQUENCE workflow_runtime.data_mapping_stack_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE workflow_runtime.data_mapping_stack_id_seq OWNER TO postgres;

--
-- Name: data_mapping_stack_id_seq; Type: SEQUENCE OWNED BY; Schema: workflow_runtime; Owner: postgres
--

ALTER SEQUENCE workflow_runtime.data_mapping_stack_id_seq OWNED BY workflow_runtime.data_mapping_stack.id;


--
-- Name: data_mappings; Type: TABLE; Schema: workflow_runtime; Owner: postgres
--

CREATE TABLE workflow_runtime.data_mappings (
    id character varying(50) NOT NULL,
    mapping_stack_id integer NOT NULL,
    source character varying(50) NOT NULL,
    target character varying(50) NOT NULL,
    mapping_type character varying(50) NOT NULL,
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE workflow_runtime.data_mappings OWNER TO postgres;

--
-- Name: department; Type: TABLE; Schema: workflow_runtime; Owner: postgres
--

CREATE TABLE workflow_runtime.department (
    id integer NOT NULL,
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    created_by character varying(255),
    updated_by character varying(255),
    departmentid character varying(255),
    departmentname character varying(255),
    managerid character varying(255),
    active boolean
);


ALTER TABLE workflow_runtime.department OWNER TO postgres;

--
-- Name: department_id_seq; Type: SEQUENCE; Schema: workflow_runtime; Owner: postgres
--

CREATE SEQUENCE workflow_runtime.department_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE workflow_runtime.department_id_seq OWNER TO postgres;

--
-- Name: department_id_seq; Type: SEQUENCE OWNED BY; Schema: workflow_runtime; Owner: postgres
--

ALTER SEQUENCE workflow_runtime.department_id_seq OWNED BY workflow_runtime.department.id;


--
-- Name: dropdown_data_sources; Type: TABLE; Schema: workflow_runtime; Owner: postgres
--

CREATE TABLE workflow_runtime.dropdown_data_sources (
    id integer NOT NULL,
    input_item_id character varying(50) NOT NULL,
    input_stack_id integer NOT NULL,
    source_type character varying(50) NOT NULL,
    query_text text,
    function_name character varying(255),
    function_params jsonb,
    value_field character varying(100) NOT NULL,
    display_field character varying(100) NOT NULL,
    depends_on_fields jsonb,
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE workflow_runtime.dropdown_data_sources OWNER TO postgres;

--
-- Name: dropdown_data_sources_id_seq; Type: SEQUENCE; Schema: workflow_runtime; Owner: postgres
--

CREATE SEQUENCE workflow_runtime.dropdown_data_sources_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE workflow_runtime.dropdown_data_sources_id_seq OWNER TO postgres;

--
-- Name: dropdown_data_sources_id_seq; Type: SEQUENCE OWNED BY; Schema: workflow_runtime; Owner: postgres
--

ALTER SEQUENCE workflow_runtime.dropdown_data_sources_id_seq OWNED BY workflow_runtime.dropdown_data_sources.id;


--
-- Name: entities; Type: TABLE; Schema: workflow_runtime; Owner: postgres
--

CREATE TABLE workflow_runtime.entities (
    entity_id character varying(50) NOT NULL,
    name character varying(100) NOT NULL,
    version character varying(20) DEFAULT '1.0'::character varying,
    status character varying(50) NOT NULL,
    type character varying(50) NOT NULL,
    attribute_prefix character varying(10),
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    description text
);


ALTER TABLE workflow_runtime.entities OWNER TO postgres;

--
-- Name: entity_attribute_metadata; Type: TABLE; Schema: workflow_runtime; Owner: postgres
--

CREATE TABLE workflow_runtime.entity_attribute_metadata (
    entity_id character varying(50) NOT NULL,
    attribute_id character varying(50) NOT NULL,
    attribute_name character varying(100) NOT NULL,
    required boolean DEFAULT false
);


ALTER TABLE workflow_runtime.entity_attribute_metadata OWNER TO postgres;

--
-- Name: entity_attributes; Type: TABLE; Schema: workflow_runtime; Owner: postgres
--

CREATE TABLE workflow_runtime.entity_attributes (
    attribute_id character varying(50) NOT NULL,
    entity_id character varying(50) NOT NULL,
    name character varying(100) NOT NULL,
    display_name character varying(100) NOT NULL,
    datatype character varying(50) NOT NULL,
    version character varying(20) DEFAULT '1.0'::character varying,
    status character varying(50) NOT NULL,
    required boolean DEFAULT false,
    reference_entity_id character varying(50),
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE workflow_runtime.entity_attributes OWNER TO postgres;

--
-- Name: entity_permissions; Type: TABLE; Schema: workflow_runtime; Owner: postgres
--

CREATE TABLE workflow_runtime.entity_permissions (
    id integer NOT NULL,
    role_id character varying(50) NOT NULL,
    entity_id character varying(50) NOT NULL,
    permission_id character varying(50) NOT NULL,
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE workflow_runtime.entity_permissions OWNER TO postgres;

--
-- Name: entity_permissions_id_seq; Type: SEQUENCE; Schema: workflow_runtime; Owner: postgres
--

CREATE SEQUENCE workflow_runtime.entity_permissions_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE workflow_runtime.entity_permissions_id_seq OWNER TO postgres;

--
-- Name: entity_permissions_id_seq; Type: SEQUENCE OWNED BY; Schema: workflow_runtime; Owner: postgres
--

ALTER SEQUENCE workflow_runtime.entity_permissions_id_seq OWNED BY workflow_runtime.entity_permissions.id;


--
-- Name: entity_relationships; Type: TABLE; Schema: workflow_runtime; Owner: postgres
--

CREATE TABLE workflow_runtime.entity_relationships (
    id integer NOT NULL,
    source_entity_id character varying(50) NOT NULL,
    target_entity_id character varying(50) NOT NULL,
    relationship_type character varying(50) NOT NULL,
    source_attribute_id character varying(50) NOT NULL,
    target_attribute_id character varying(50) NOT NULL,
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE workflow_runtime.entity_relationships OWNER TO postgres;

--
-- Name: entity_relationships_id_seq; Type: SEQUENCE; Schema: workflow_runtime; Owner: postgres
--

CREATE SEQUENCE workflow_runtime.entity_relationships_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE workflow_runtime.entity_relationships_id_seq OWNER TO postgres;

--
-- Name: entity_relationships_id_seq; Type: SEQUENCE OWNED BY; Schema: workflow_runtime; Owner: postgres
--

ALTER SEQUENCE workflow_runtime.entity_relationships_id_seq OWNED BY workflow_runtime.entity_relationships.id;


--
-- Name: execution_path_tracking; Type: TABLE; Schema: workflow_runtime; Owner: postgres
--

CREATE TABLE workflow_runtime.execution_path_tracking (
    id integer NOT NULL,
    metrics_stack_id integer NOT NULL,
    enabled boolean DEFAULT true,
    store_complete_path boolean DEFAULT true,
    identify_bottlenecks boolean DEFAULT false,
    compare_to_historical boolean DEFAULT false,
    path_efficiency_function character varying(100),
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE workflow_runtime.execution_path_tracking OWNER TO postgres;

--
-- Name: execution_path_tracking_id_seq; Type: SEQUENCE; Schema: workflow_runtime; Owner: postgres
--

CREATE SEQUENCE workflow_runtime.execution_path_tracking_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE workflow_runtime.execution_path_tracking_id_seq OWNER TO postgres;

--
-- Name: execution_path_tracking_id_seq; Type: SEQUENCE OWNED BY; Schema: workflow_runtime; Owner: postgres
--

ALTER SEQUENCE workflow_runtime.execution_path_tracking_id_seq OWNED BY workflow_runtime.execution_path_tracking.id;


--
-- Name: execution_pathway_conditions; Type: TABLE; Schema: workflow_runtime; Owner: postgres
--

CREATE TABLE workflow_runtime.execution_pathway_conditions (
    id integer NOT NULL,
    lo_id character varying(50) NOT NULL,
    condition_type character varying(50) NOT NULL,
    condition_entity character varying(50),
    condition_attribute character varying(50),
    condition_operator character varying(20),
    condition_value text,
    next_lo character varying(50) NOT NULL,
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE workflow_runtime.execution_pathway_conditions OWNER TO postgres;

--
-- Name: execution_pathway_conditions_id_seq; Type: SEQUENCE; Schema: workflow_runtime; Owner: postgres
--

CREATE SEQUENCE workflow_runtime.execution_pathway_conditions_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE workflow_runtime.execution_pathway_conditions_id_seq OWNER TO postgres;

--
-- Name: execution_pathway_conditions_id_seq; Type: SEQUENCE OWNED BY; Schema: workflow_runtime; Owner: postgres
--

ALTER SEQUENCE workflow_runtime.execution_pathway_conditions_id_seq OWNED BY workflow_runtime.execution_pathway_conditions.id;


--
-- Name: execution_pathways; Type: TABLE; Schema: workflow_runtime; Owner: postgres
--

CREATE TABLE workflow_runtime.execution_pathways (
    lo_id character varying(50) NOT NULL,
    pathway_type character varying(50) NOT NULL,
    next_lo character varying(50),
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    id integer NOT NULL
);


ALTER TABLE workflow_runtime.execution_pathways OWNER TO postgres;

--
-- Name: execution_pathways_id_seq; Type: SEQUENCE; Schema: workflow_runtime; Owner: postgres
--

CREATE SEQUENCE workflow_runtime.execution_pathways_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE workflow_runtime.execution_pathways_id_seq OWNER TO postgres;

--
-- Name: execution_pathways_id_seq; Type: SEQUENCE OWNED BY; Schema: workflow_runtime; Owner: postgres
--

ALTER SEQUENCE workflow_runtime.execution_pathways_id_seq OWNED BY workflow_runtime.execution_pathways.id;


--
-- Name: execution_rules; Type: TABLE; Schema: workflow_runtime; Owner: postgres
--

CREATE TABLE workflow_runtime.execution_rules (
    id character varying(50) NOT NULL,
    lo_id character varying(50) NOT NULL,
    contextual_id character varying(100) NOT NULL,
    description text,
    structured_rule jsonb,
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE workflow_runtime.execution_rules OWNER TO postgres;

--
-- Name: global_objectives; Type: TABLE; Schema: workflow_runtime; Owner: postgres
--

CREATE TABLE workflow_runtime.global_objectives (
    go_id character varying(50) NOT NULL,
    name character varying(100) NOT NULL,
    version character varying(20) DEFAULT '1.0'::character varying,
    status character varying(50) NOT NULL,
    description text,
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    tenant_id character varying(50),
    last_used timestamp without time zone,
    deleted_mark boolean DEFAULT false
);


ALTER TABLE workflow_runtime.global_objectives OWNER TO postgres;

--
-- Name: input_data_sources; Type: TABLE; Schema: workflow_runtime; Owner: postgres
--

CREATE TABLE workflow_runtime.input_data_sources (
    id integer NOT NULL,
    input_item_id character varying(50) NOT NULL,
    input_stack_id integer NOT NULL,
    source_type character varying(50) NOT NULL,
    function_name character varying(100),
    parameters jsonb,
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE workflow_runtime.input_data_sources OWNER TO postgres;

--
-- Name: input_data_sources_id_seq; Type: SEQUENCE; Schema: workflow_runtime; Owner: postgres
--

CREATE SEQUENCE workflow_runtime.input_data_sources_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE workflow_runtime.input_data_sources_id_seq OWNER TO postgres;

--
-- Name: input_data_sources_id_seq; Type: SEQUENCE OWNED BY; Schema: workflow_runtime; Owner: postgres
--

ALTER SEQUENCE workflow_runtime.input_data_sources_id_seq OWNED BY workflow_runtime.input_data_sources.id;


--
-- Name: input_dependencies; Type: TABLE; Schema: workflow_runtime; Owner: postgres
--

CREATE TABLE workflow_runtime.input_dependencies (
    id integer NOT NULL,
    input_item_id character varying(50) NOT NULL,
    input_stack_id integer NOT NULL,
    depends_on_id character varying(50) NOT NULL,
    depends_on_stack_id integer NOT NULL,
    dependency_type character varying(50) NOT NULL,
    condition_expression text,
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE workflow_runtime.input_dependencies OWNER TO postgres;

--
-- Name: input_dependencies_id_seq; Type: SEQUENCE; Schema: workflow_runtime; Owner: postgres
--

CREATE SEQUENCE workflow_runtime.input_dependencies_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE workflow_runtime.input_dependencies_id_seq OWNER TO postgres;

--
-- Name: input_dependencies_id_seq; Type: SEQUENCE OWNED BY; Schema: workflow_runtime; Owner: postgres
--

ALTER SEQUENCE workflow_runtime.input_dependencies_id_seq OWNED BY workflow_runtime.input_dependencies.id;


--
-- Name: input_items; Type: TABLE; Schema: workflow_runtime; Owner: postgres
--

CREATE TABLE workflow_runtime.input_items (
    id character varying(50) NOT NULL,
    input_stack_id integer NOT NULL,
    slot_id character varying(50) NOT NULL,
    contextual_id character varying(50) NOT NULL,
    entity_reference character varying(50),
    attribute_reference character varying(50),
    source_type character varying(50) NOT NULL,
    source_description text,
    required boolean DEFAULT false,
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE workflow_runtime.input_items OWNER TO postgres;

--
-- Name: input_stack; Type: TABLE; Schema: workflow_runtime; Owner: postgres
--

CREATE TABLE workflow_runtime.input_stack (
    id integer NOT NULL,
    go_id character varying(50) NOT NULL,
    description text,
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE workflow_runtime.input_stack OWNER TO postgres;

--
-- Name: input_stack_id_seq; Type: SEQUENCE; Schema: workflow_runtime; Owner: postgres
--

CREATE SEQUENCE workflow_runtime.input_stack_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE workflow_runtime.input_stack_id_seq OWNER TO postgres;

--
-- Name: input_stack_id_seq; Type: SEQUENCE OWNED BY; Schema: workflow_runtime; Owner: postgres
--

ALTER SEQUENCE workflow_runtime.input_stack_id_seq OWNED BY workflow_runtime.input_stack.id;


--
-- Name: invoice; Type: TABLE; Schema: workflow_runtime; Owner: postgres
--

CREATE TABLE workflow_runtime.invoice (
    id integer NOT NULL,
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    created_by character varying(255),
    updated_by character varying(255),
    invoice_id character varying(255),
    purchase_order_id character varying(255),
    vendor_name character varying(255),
    invoice_date date,
    invoice_amount numeric,
    payment_status character varying(50),
    payment_due_date date,
    payment_method character varying(50),
    remarks character varying(255)
);


ALTER TABLE workflow_runtime.invoice OWNER TO postgres;

--
-- Name: invoice_id_seq; Type: SEQUENCE; Schema: workflow_runtime; Owner: postgres
--

CREATE SEQUENCE workflow_runtime.invoice_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE workflow_runtime.invoice_id_seq OWNER TO postgres;

--
-- Name: invoice_id_seq; Type: SEQUENCE OWNED BY; Schema: workflow_runtime; Owner: postgres
--

ALTER SEQUENCE workflow_runtime.invoice_id_seq OWNED BY workflow_runtime.invoice.id;


--
-- Name: it_support_staff; Type: TABLE; Schema: workflow_runtime; Owner: postgres
--

CREATE TABLE workflow_runtime.it_support_staff (
    id integer NOT NULL,
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    created_by character varying(255),
    updated_by character varying(255),
    staff_id character varying(255),
    staff_name character varying(255),
    specialization character varying(50),
    availability character varying(50),
    current_load integer
);


ALTER TABLE workflow_runtime.it_support_staff OWNER TO postgres;

--
-- Name: it_support_staff_id_seq; Type: SEQUENCE; Schema: workflow_runtime; Owner: postgres
--

CREATE SEQUENCE workflow_runtime.it_support_staff_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE workflow_runtime.it_support_staff_id_seq OWNER TO postgres;

--
-- Name: it_support_staff_id_seq; Type: SEQUENCE OWNED BY; Schema: workflow_runtime; Owner: postgres
--

ALTER SEQUENCE workflow_runtime.it_support_staff_id_seq OWNED BY workflow_runtime.it_support_staff.id;


--
-- Name: leave_application; Type: TABLE; Schema: workflow_runtime; Owner: postgres
--

CREATE TABLE workflow_runtime.leave_application (
    id integer NOT NULL,
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    created_by character varying(255),
    updated_by character varying(255),
    leave_id character varying(255),
    employee_id character varying(255),
    start_date date,
    end_date date,
    num_days integer,
    reason character varying(255),
    status character varying(50),
    remarks character varying(255),
    approved_by character varying(255),
    leave_type character varying(50),
    leave_sub_type character varying(255)
);


ALTER TABLE workflow_runtime.leave_application OWNER TO postgres;

--
-- Name: leave_application_id_seq; Type: SEQUENCE; Schema: workflow_runtime; Owner: postgres
--

CREATE SEQUENCE workflow_runtime.leave_application_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE workflow_runtime.leave_application_id_seq OWNER TO postgres;

--
-- Name: leave_application_id_seq; Type: SEQUENCE OWNED BY; Schema: workflow_runtime; Owner: postgres
--

ALTER SEQUENCE workflow_runtime.leave_application_id_seq OWNED BY workflow_runtime.leave_application.id;


--
-- Name: leave_sub_type; Type: TABLE; Schema: workflow_runtime; Owner: postgres
--

CREATE TABLE workflow_runtime.leave_sub_type (
    id integer NOT NULL,
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    created_by character varying(255),
    updated_by character varying(255),
    leave_type character varying(255),
    sub_type_id character varying(255),
    sub_type_name character varying(255),
    active boolean
);


ALTER TABLE workflow_runtime.leave_sub_type OWNER TO postgres;

--
-- Name: leave_sub_type_id_seq; Type: SEQUENCE; Schema: workflow_runtime; Owner: postgres
--

CREATE SEQUENCE workflow_runtime.leave_sub_type_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE workflow_runtime.leave_sub_type_id_seq OWNER TO postgres;

--
-- Name: leave_sub_type_id_seq; Type: SEQUENCE OWNED BY; Schema: workflow_runtime; Owner: postgres
--

ALTER SEQUENCE workflow_runtime.leave_sub_type_id_seq OWNED BY workflow_runtime.leave_sub_type.id;


--
-- Name: leave_sub_types; Type: TABLE; Schema: workflow_runtime; Owner: postgres
--

CREATE TABLE workflow_runtime.leave_sub_types (
    id integer NOT NULL,
    leave_type character varying(100) NOT NULL,
    sub_type_id character varying(100) NOT NULL,
    sub_type_name character varying(100) NOT NULL,
    active boolean DEFAULT true,
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE workflow_runtime.leave_sub_types OWNER TO postgres;

--
-- Name: leave_sub_types_id_seq; Type: SEQUENCE; Schema: workflow_runtime; Owner: postgres
--

CREATE SEQUENCE workflow_runtime.leave_sub_types_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE workflow_runtime.leave_sub_types_id_seq OWNER TO postgres;

--
-- Name: leave_sub_types_id_seq; Type: SEQUENCE OWNED BY; Schema: workflow_runtime; Owner: postgres
--

ALTER SEQUENCE workflow_runtime.leave_sub_types_id_seq OWNED BY workflow_runtime.leave_sub_types.id;


--
-- Name: lo_data_mapping_stack; Type: TABLE; Schema: workflow_runtime; Owner: postgres
--

CREATE TABLE workflow_runtime.lo_data_mapping_stack (
    id integer NOT NULL,
    lo_id character varying(50) NOT NULL,
    description text,
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE workflow_runtime.lo_data_mapping_stack OWNER TO postgres;

--
-- Name: lo_data_mapping_stack_id_seq; Type: SEQUENCE; Schema: workflow_runtime; Owner: postgres
--

CREATE SEQUENCE workflow_runtime.lo_data_mapping_stack_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE workflow_runtime.lo_data_mapping_stack_id_seq OWNER TO postgres;

--
-- Name: lo_data_mapping_stack_id_seq; Type: SEQUENCE OWNED BY; Schema: workflow_runtime; Owner: postgres
--

ALTER SEQUENCE workflow_runtime.lo_data_mapping_stack_id_seq OWNED BY workflow_runtime.lo_data_mapping_stack.id;


--
-- Name: lo_data_mappings; Type: TABLE; Schema: workflow_runtime; Owner: postgres
--

CREATE TABLE workflow_runtime.lo_data_mappings (
    id character varying(50) NOT NULL,
    mapping_stack_id integer NOT NULL,
    source character varying(100) NOT NULL,
    target character varying(100) NOT NULL,
    mapping_type character varying(50) NOT NULL,
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE workflow_runtime.lo_data_mappings OWNER TO postgres;

--
-- Name: lo_input_execution; Type: TABLE; Schema: workflow_runtime; Owner: postgres
--

CREATE TABLE workflow_runtime.lo_input_execution (
    id integer NOT NULL,
    instance_id uuid NOT NULL,
    lo_id text NOT NULL,
    input_contextual_id text NOT NULL,
    input_value jsonb,
    created_at timestamp without time zone DEFAULT now()
);


ALTER TABLE workflow_runtime.lo_input_execution OWNER TO postgres;

--
-- Name: lo_input_execution_id_seq; Type: SEQUENCE; Schema: workflow_runtime; Owner: postgres
--

CREATE SEQUENCE workflow_runtime.lo_input_execution_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE workflow_runtime.lo_input_execution_id_seq OWNER TO postgres;

--
-- Name: lo_input_execution_id_seq; Type: SEQUENCE OWNED BY; Schema: workflow_runtime; Owner: postgres
--

ALTER SEQUENCE workflow_runtime.lo_input_execution_id_seq OWNED BY workflow_runtime.lo_input_execution.id;


--
-- Name: lo_input_items; Type: TABLE; Schema: workflow_runtime; Owner: postgres
--

CREATE TABLE workflow_runtime.lo_input_items (
    id character varying(50) NOT NULL,
    input_stack_id integer NOT NULL,
    slot_id character varying(100) NOT NULL,
    contextual_id character varying(100) NOT NULL,
    source_type workflow_runtime.input_source_type NOT NULL,
    source_description text,
    required boolean DEFAULT false,
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    lo_id character varying(50),
    data_type character varying(50),
    ui_control character varying(50),
    nested_function jsonb,
    nested_functions jsonb,
    metadata jsonb,
    dependencies jsonb,
    dependency_type character varying(50) DEFAULT NULL::character varying,
    lookup_function jsonb,
    is_visible boolean DEFAULT true
);


ALTER TABLE workflow_runtime.lo_input_items OWNER TO postgres;

--
-- Name: lo_input_stack; Type: TABLE; Schema: workflow_runtime; Owner: postgres
--

CREATE TABLE workflow_runtime.lo_input_stack (
    id integer NOT NULL,
    lo_id character varying(50) NOT NULL,
    description text,
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE workflow_runtime.lo_input_stack OWNER TO postgres;

--
-- Name: lo_input_stack_id_seq; Type: SEQUENCE; Schema: workflow_runtime; Owner: postgres
--

CREATE SEQUENCE workflow_runtime.lo_input_stack_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE workflow_runtime.lo_input_stack_id_seq OWNER TO postgres;

--
-- Name: lo_input_stack_id_seq; Type: SEQUENCE OWNED BY; Schema: workflow_runtime; Owner: postgres
--

ALTER SEQUENCE workflow_runtime.lo_input_stack_id_seq OWNED BY workflow_runtime.lo_input_stack.id;


--
-- Name: lo_input_validations; Type: TABLE; Schema: workflow_runtime; Owner: postgres
--

CREATE TABLE workflow_runtime.lo_input_validations (
    id integer NOT NULL,
    input_item_id character varying(50) NOT NULL,
    input_stack_id integer NOT NULL,
    rule character varying(100) NOT NULL,
    rule_type character varying(50) NOT NULL,
    entity character varying(50),
    attribute character varying(50),
    validation_method character varying(50),
    reference_date_source character varying(100),
    allowed_values jsonb,
    error_message text,
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE workflow_runtime.lo_input_validations OWNER TO postgres;

--
-- Name: lo_input_validations_id_seq; Type: SEQUENCE; Schema: workflow_runtime; Owner: postgres
--

CREATE SEQUENCE workflow_runtime.lo_input_validations_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE workflow_runtime.lo_input_validations_id_seq OWNER TO postgres;

--
-- Name: lo_input_validations_id_seq; Type: SEQUENCE OWNED BY; Schema: workflow_runtime; Owner: postgres
--

ALTER SEQUENCE workflow_runtime.lo_input_validations_id_seq OWNED BY workflow_runtime.lo_input_validations.id;


--
-- Name: lo_nested_functions; Type: TABLE; Schema: workflow_runtime; Owner: postgres
--

CREATE TABLE workflow_runtime.lo_nested_functions (
    id integer NOT NULL,
    lo_id character varying(50) NOT NULL,
    nested_function_id character varying(50) NOT NULL,
    function_name character varying(100) NOT NULL,
    function_type character varying(50) NOT NULL,
    parameters jsonb,
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    input_contextual_id text,
    output_to text
);


ALTER TABLE workflow_runtime.lo_nested_functions OWNER TO postgres;

--
-- Name: lo_nested_functions_id_seq; Type: SEQUENCE; Schema: workflow_runtime; Owner: postgres
--

CREATE SEQUENCE workflow_runtime.lo_nested_functions_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE workflow_runtime.lo_nested_functions_id_seq OWNER TO postgres;

--
-- Name: lo_nested_functions_id_seq; Type: SEQUENCE OWNED BY; Schema: workflow_runtime; Owner: postgres
--

ALTER SEQUENCE workflow_runtime.lo_nested_functions_id_seq OWNED BY workflow_runtime.lo_nested_functions.id;


--
-- Name: lo_output_execution; Type: TABLE; Schema: workflow_runtime; Owner: postgres
--

CREATE TABLE workflow_runtime.lo_output_execution (
    id integer NOT NULL,
    instance_id uuid NOT NULL,
    lo_id text NOT NULL,
    output_contextual_id text NOT NULL,
    output_value jsonb,
    created_at timestamp without time zone DEFAULT now()
);


ALTER TABLE workflow_runtime.lo_output_execution OWNER TO postgres;

--
-- Name: lo_output_execution_id_seq; Type: SEQUENCE; Schema: workflow_runtime; Owner: postgres
--

CREATE SEQUENCE workflow_runtime.lo_output_execution_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE workflow_runtime.lo_output_execution_id_seq OWNER TO postgres;

--
-- Name: lo_output_execution_id_seq; Type: SEQUENCE OWNED BY; Schema: workflow_runtime; Owner: postgres
--

ALTER SEQUENCE workflow_runtime.lo_output_execution_id_seq OWNED BY workflow_runtime.lo_output_execution.id;


--
-- Name: lo_output_items; Type: TABLE; Schema: workflow_runtime; Owner: postgres
--

CREATE TABLE workflow_runtime.lo_output_items (
    id character varying(50) NOT NULL,
    output_stack_id integer NOT NULL,
    slot_id character varying(100) NOT NULL,
    contextual_id character varying(100) NOT NULL,
    source character varying(100),
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    lo_id text
);


ALTER TABLE workflow_runtime.lo_output_items OWNER TO postgres;

--
-- Name: lo_output_stack; Type: TABLE; Schema: workflow_runtime; Owner: postgres
--

CREATE TABLE workflow_runtime.lo_output_stack (
    id integer NOT NULL,
    lo_id character varying(50) NOT NULL,
    description text,
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE workflow_runtime.lo_output_stack OWNER TO postgres;

--
-- Name: lo_output_stack_id_seq; Type: SEQUENCE; Schema: workflow_runtime; Owner: postgres
--

CREATE SEQUENCE workflow_runtime.lo_output_stack_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE workflow_runtime.lo_output_stack_id_seq OWNER TO postgres;

--
-- Name: lo_output_stack_id_seq; Type: SEQUENCE OWNED BY; Schema: workflow_runtime; Owner: postgres
--

ALTER SEQUENCE workflow_runtime.lo_output_stack_id_seq OWNED BY workflow_runtime.lo_output_stack.id;


--
-- Name: lo_output_triggers; Type: TABLE; Schema: workflow_runtime; Owner: postgres
--

CREATE TABLE workflow_runtime.lo_output_triggers (
    id character varying(50) NOT NULL,
    output_item_id character varying(50) NOT NULL,
    output_stack_id integer NOT NULL,
    target_objective character varying(100) NOT NULL,
    target_input character varying(100) NOT NULL,
    mapping_type character varying(50) NOT NULL,
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE workflow_runtime.lo_output_triggers OWNER TO postgres;

--
-- Name: lo_system_functions; Type: TABLE; Schema: workflow_runtime; Owner: postgres
--

CREATE TABLE workflow_runtime.lo_system_functions (
    function_id character varying(50) NOT NULL,
    lo_id character varying(50) NOT NULL,
    function_name character varying(100) NOT NULL,
    function_type character varying(50) NOT NULL,
    parameters jsonb,
    output_to character varying(100),
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE workflow_runtime.lo_system_functions OWNER TO postgres;

--
-- Name: loanapplication; Type: TABLE; Schema: workflow_runtime; Owner: postgres
--

CREATE TABLE workflow_runtime.loanapplication (
    id integer NOT NULL,
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    created_by character varying(255),
    updated_by character varying(255),
    loanid character varying(255),
    applicantid character varying(255),
    loantype character varying(50),
    amount numeric,
    interestrate numeric,
    status character varying(50)
);


ALTER TABLE workflow_runtime.loanapplication OWNER TO postgres;

--
-- Name: loanapplication_id_seq; Type: SEQUENCE; Schema: workflow_runtime; Owner: postgres
--

CREATE SEQUENCE workflow_runtime.loanapplication_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE workflow_runtime.loanapplication_id_seq OWNER TO postgres;

--
-- Name: loanapplication_id_seq; Type: SEQUENCE OWNED BY; Schema: workflow_runtime; Owner: postgres
--

ALTER SEQUENCE workflow_runtime.loanapplication_id_seq OWNED BY workflow_runtime.loanapplication.id;


--
-- Name: loanrate; Type: TABLE; Schema: workflow_runtime; Owner: postgres
--

CREATE TABLE workflow_runtime.loanrate (
    id integer NOT NULL,
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    created_by character varying(255),
    updated_by character varying(255),
    loantype character varying(50),
    rate numeric
);


ALTER TABLE workflow_runtime.loanrate OWNER TO postgres;

--
-- Name: loanrate_id_seq; Type: SEQUENCE; Schema: workflow_runtime; Owner: postgres
--

CREATE SEQUENCE workflow_runtime.loanrate_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE workflow_runtime.loanrate_id_seq OWNER TO postgres;

--
-- Name: loanrate_id_seq; Type: SEQUENCE OWNED BY; Schema: workflow_runtime; Owner: postgres
--

ALTER SEQUENCE workflow_runtime.loanrate_id_seq OWNED BY workflow_runtime.loanrate.id;


--
-- Name: local_objectives; Type: TABLE; Schema: workflow_runtime; Owner: postgres
--

CREATE TABLE workflow_runtime.local_objectives (
    lo_id character varying(50) NOT NULL,
    contextual_id character varying(100) NOT NULL,
    name character varying(100) NOT NULL,
    function_type character varying(50) NOT NULL,
    workflow_source character varying(50) NOT NULL,
    go_id character varying(50) NOT NULL,
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    system_function character varying(100) DEFAULT NULL::character varying
);


ALTER TABLE workflow_runtime.local_objectives OWNER TO postgres;

--
-- Name: mapping_rules; Type: TABLE; Schema: workflow_runtime; Owner: postgres
--

CREATE TABLE workflow_runtime.mapping_rules (
    id character varying(50) NOT NULL,
    mapping_stack_id integer NOT NULL,
    description text,
    condition_type character varying(50),
    condition_entity character varying(50),
    condition_attribute character varying(50),
    condition_operator character varying(20),
    condition_value text,
    error_message text,
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE workflow_runtime.mapping_rules OWNER TO postgres;

--
-- Name: metrics_aggregation; Type: TABLE; Schema: workflow_runtime; Owner: postgres
--

CREATE TABLE workflow_runtime.metrics_aggregation (
    id integer NOT NULL,
    metrics_stack_id integer NOT NULL,
    attribute_name character varying(100) NOT NULL,
    aggregation_function character varying(100) NOT NULL,
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE workflow_runtime.metrics_aggregation OWNER TO postgres;

--
-- Name: metrics_aggregation_id_seq; Type: SEQUENCE; Schema: workflow_runtime; Owner: postgres
--

CREATE SEQUENCE workflow_runtime.metrics_aggregation_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE workflow_runtime.metrics_aggregation_id_seq OWNER TO postgres;

--
-- Name: metrics_aggregation_id_seq; Type: SEQUENCE OWNED BY; Schema: workflow_runtime; Owner: postgres
--

ALTER SEQUENCE workflow_runtime.metrics_aggregation_id_seq OWNED BY workflow_runtime.metrics_aggregation.id;


--
-- Name: metrics_reporting; Type: TABLE; Schema: workflow_runtime; Owner: postgres
--

CREATE TABLE workflow_runtime.metrics_reporting (
    id integer NOT NULL,
    metrics_stack_id integer NOT NULL,
    generate_execution_summary boolean DEFAULT true,
    store_summary_location character varying(255),
    error_count_threshold integer,
    duration_percentile integer,
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE workflow_runtime.metrics_reporting OWNER TO postgres;

--
-- Name: metrics_reporting_id_seq; Type: SEQUENCE; Schema: workflow_runtime; Owner: postgres
--

CREATE SEQUENCE workflow_runtime.metrics_reporting_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE workflow_runtime.metrics_reporting_id_seq OWNER TO postgres;

--
-- Name: metrics_reporting_id_seq; Type: SEQUENCE OWNED BY; Schema: workflow_runtime; Owner: postgres
--

ALTER SEQUENCE workflow_runtime.metrics_reporting_id_seq OWNED BY workflow_runtime.metrics_reporting.id;


--
-- Name: objective_permissions; Type: TABLE; Schema: workflow_runtime; Owner: postgres
--

CREATE TABLE workflow_runtime.objective_permissions (
    id integer NOT NULL,
    role_id character varying(50) NOT NULL,
    objective_id character varying(50) NOT NULL,
    permission_id character varying(50) NOT NULL,
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE workflow_runtime.objective_permissions OWNER TO postgres;

--
-- Name: objective_permissions_id_seq; Type: SEQUENCE; Schema: workflow_runtime; Owner: postgres
--

CREATE SEQUENCE workflow_runtime.objective_permissions_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE workflow_runtime.objective_permissions_id_seq OWNER TO postgres;

--
-- Name: objective_permissions_id_seq; Type: SEQUENCE OWNED BY; Schema: workflow_runtime; Owner: postgres
--

ALTER SEQUENCE workflow_runtime.objective_permissions_id_seq OWNED BY workflow_runtime.objective_permissions.id;


--
-- Name: output_items; Type: TABLE; Schema: workflow_runtime; Owner: postgres
--

CREATE TABLE workflow_runtime.output_items (
    id character varying(50) NOT NULL,
    output_stack_id integer NOT NULL,
    slot_id character varying(50) NOT NULL,
    contextual_id character varying(50) NOT NULL,
    output_entity character varying(50),
    output_attribute character varying(50),
    data_type character varying(50) NOT NULL,
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE workflow_runtime.output_items OWNER TO postgres;

--
-- Name: output_stack; Type: TABLE; Schema: workflow_runtime; Owner: postgres
--

CREATE TABLE workflow_runtime.output_stack (
    id integer NOT NULL,
    go_id character varying(50) NOT NULL,
    description text,
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE workflow_runtime.output_stack OWNER TO postgres;

--
-- Name: output_stack_id_seq; Type: SEQUENCE; Schema: workflow_runtime; Owner: postgres
--

CREATE SEQUENCE workflow_runtime.output_stack_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE workflow_runtime.output_stack_id_seq OWNER TO postgres;

--
-- Name: output_stack_id_seq; Type: SEQUENCE OWNED BY; Schema: workflow_runtime; Owner: postgres
--

ALTER SEQUENCE workflow_runtime.output_stack_id_seq OWNED BY workflow_runtime.output_stack.id;


--
-- Name: output_triggers; Type: TABLE; Schema: workflow_runtime; Owner: postgres
--

CREATE TABLE workflow_runtime.output_triggers (
    id character varying(50) NOT NULL,
    output_item_id character varying(50) NOT NULL,
    output_stack_id integer NOT NULL,
    target_objective character varying(50) NOT NULL,
    target_input character varying(50) NOT NULL,
    mapping_type character varying(50) NOT NULL,
    condition_type character varying(50),
    condition_entity character varying(50),
    condition_attribute character varying(50),
    condition_operator character varying(20),
    condition_value text,
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE workflow_runtime.output_triggers OWNER TO postgres;

--
-- Name: permission_capabilities; Type: TABLE; Schema: workflow_runtime; Owner: postgres
--

CREATE TABLE workflow_runtime.permission_capabilities (
    capability_id character varying(50) NOT NULL,
    permission_id character varying(50) NOT NULL
);


ALTER TABLE workflow_runtime.permission_capabilities OWNER TO postgres;

--
-- Name: permission_types; Type: TABLE; Schema: workflow_runtime; Owner: postgres
--

CREATE TABLE workflow_runtime.permission_types (
    permission_id character varying(50) NOT NULL,
    description text,
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE workflow_runtime.permission_types OWNER TO postgres;

--
-- Name: purchase_order; Type: TABLE; Schema: workflow_runtime; Owner: postgres
--

CREATE TABLE workflow_runtime.purchase_order (
    id integer NOT NULL,
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    created_by character varying(255),
    updated_by character varying(255),
    purchase_order_id character varying(255),
    requisition_id character varying(255),
    vendor_name character varying(255),
    vendor_id character varying(255),
    order_date date,
    delivery_date date,
    total_amount numeric,
    status character varying(50),
    payment_terms character varying(255),
    shipping_terms character varying(255),
    approved_by character varying(255)
);


ALTER TABLE workflow_runtime.purchase_order OWNER TO postgres;

--
-- Name: purchase_order_id_seq; Type: SEQUENCE; Schema: workflow_runtime; Owner: postgres
--

CREATE SEQUENCE workflow_runtime.purchase_order_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE workflow_runtime.purchase_order_id_seq OWNER TO postgres;

--
-- Name: purchase_order_id_seq; Type: SEQUENCE OWNED BY; Schema: workflow_runtime; Owner: postgres
--

ALTER SEQUENCE workflow_runtime.purchase_order_id_seq OWNED BY workflow_runtime.purchase_order.id;


--
-- Name: purchase_requisition; Type: TABLE; Schema: workflow_runtime; Owner: postgres
--

CREATE TABLE workflow_runtime.purchase_requisition (
    id integer NOT NULL,
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    created_by character varying(255),
    updated_by character varying(255),
    requisition_id character varying(255),
    requester_name character varying(255),
    department character varying(255),
    requisition_date date,
    justification character varying(255),
    status character varying(50),
    total_estimated_amount numeric,
    priority character varying(50),
    remarks character varying(255)
);


ALTER TABLE workflow_runtime.purchase_requisition OWNER TO postgres;

--
-- Name: purchase_requisition_id_seq; Type: SEQUENCE; Schema: workflow_runtime; Owner: postgres
--

CREATE SEQUENCE workflow_runtime.purchase_requisition_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE workflow_runtime.purchase_requisition_id_seq OWNER TO postgres;

--
-- Name: purchase_requisition_id_seq; Type: SEQUENCE OWNED BY; Schema: workflow_runtime; Owner: postgres
--

ALTER SEQUENCE workflow_runtime.purchase_requisition_id_seq OWNED BY workflow_runtime.purchase_requisition.id;


--
-- Name: roles; Type: TABLE; Schema: workflow_runtime; Owner: postgres
--

CREATE TABLE workflow_runtime.roles (
    role_id character varying(50) NOT NULL,
    name character varying(100) NOT NULL,
    tenant_id character varying(50) NOT NULL,
    inherits_from character varying(50),
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE workflow_runtime.roles OWNER TO postgres;

--
-- Name: runtime_metrics_stack; Type: TABLE; Schema: workflow_runtime; Owner: postgres
--

CREATE TABLE workflow_runtime.runtime_metrics_stack (
    id integer NOT NULL,
    go_id character varying(50) NOT NULL,
    description text,
    metrics_entity character varying(50) NOT NULL,
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE workflow_runtime.runtime_metrics_stack OWNER TO postgres;

--
-- Name: runtime_metrics_stack_id_seq; Type: SEQUENCE; Schema: workflow_runtime; Owner: postgres
--

CREATE SEQUENCE workflow_runtime.runtime_metrics_stack_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE workflow_runtime.runtime_metrics_stack_id_seq OWNER TO postgres;

--
-- Name: runtime_metrics_stack_id_seq; Type: SEQUENCE OWNED BY; Schema: workflow_runtime; Owner: postgres
--

ALTER SEQUENCE workflow_runtime.runtime_metrics_stack_id_seq OWNED BY workflow_runtime.runtime_metrics_stack.id;


--
-- Name: success_messages; Type: TABLE; Schema: workflow_runtime; Owner: postgres
--

CREATE TABLE workflow_runtime.success_messages (
    lo_id character varying(50) NOT NULL,
    message text NOT NULL,
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE workflow_runtime.success_messages OWNER TO postgres;

--
-- Name: system_functions; Type: TABLE; Schema: workflow_runtime; Owner: postgres
--

CREATE TABLE workflow_runtime.system_functions (
    function_id character varying(50) NOT NULL,
    function_name character varying(100) NOT NULL,
    function_type character varying(50) NOT NULL,
    stack_type character varying(50) NOT NULL,
    stack_id integer NOT NULL,
    parameters jsonb,
    output_to character varying(100),
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE workflow_runtime.system_functions OWNER TO postgres;

--
-- Name: task; Type: TABLE; Schema: workflow_runtime; Owner: postgres
--

CREATE TABLE workflow_runtime.task (
    id integer NOT NULL,
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    created_by character varying(255),
    updated_by character varying(255),
    taskid character varying(255),
    title character varying(255),
    description character varying(255),
    assigneeid character varying(255),
    priority character varying(50),
    status character varying(50),
    duedate date,
    createdby character varying(255),
    createddate date,
    category character varying(50),
    estimatedhours numeric,
    actualhours numeric,
    completiondate date,
    comments character varying(255)
);


ALTER TABLE workflow_runtime.task OWNER TO postgres;

--
-- Name: task_id_seq; Type: SEQUENCE; Schema: workflow_runtime; Owner: postgres
--

CREATE SEQUENCE workflow_runtime.task_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE workflow_runtime.task_id_seq OWNER TO postgres;

--
-- Name: task_id_seq; Type: SEQUENCE OWNED BY; Schema: workflow_runtime; Owner: postgres
--

ALTER SEQUENCE workflow_runtime.task_id_seq OWNED BY workflow_runtime.task.id;


--
-- Name: teamresource; Type: TABLE; Schema: workflow_runtime; Owner: postgres
--

CREATE TABLE workflow_runtime.teamresource (
    id integer NOT NULL,
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    created_by character varying(255),
    updated_by character varying(255),
    teamid character varying(255),
    teamname character varying(255),
    department character varying(255),
    resourceid character varying(255),
    resourcename character varying(255),
    role character varying(50),
    availability character varying(50)
);


ALTER TABLE workflow_runtime.teamresource OWNER TO postgres;

--
-- Name: teamresource_id_seq; Type: SEQUENCE; Schema: workflow_runtime; Owner: postgres
--

CREATE SEQUENCE workflow_runtime.teamresource_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE workflow_runtime.teamresource_id_seq OWNER TO postgres;

--
-- Name: teamresource_id_seq; Type: SEQUENCE OWNED BY; Schema: workflow_runtime; Owner: postgres
--

ALTER SEQUENCE workflow_runtime.teamresource_id_seq OWNED BY workflow_runtime.teamresource.id;


--
-- Name: tenants; Type: TABLE; Schema: workflow_runtime; Owner: postgres
--

CREATE TABLE workflow_runtime.tenants (
    tenant_id character varying(50) NOT NULL,
    name character varying(100) NOT NULL,
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE workflow_runtime.tenants OWNER TO postgres;

--
-- Name: terminal_pathways; Type: TABLE; Schema: workflow_runtime; Owner: postgres
--

CREATE TABLE workflow_runtime.terminal_pathways (
    lo_id character varying(50) NOT NULL,
    terminal_type character varying(50) NOT NULL,
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE workflow_runtime.terminal_pathways OWNER TO postgres;

--
-- Name: ticket; Type: TABLE; Schema: workflow_runtime; Owner: postgres
--

CREATE TABLE workflow_runtime.ticket (
    id integer NOT NULL,
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    created_by character varying(255),
    updated_by character varying(255),
    ticket_id character varying(255),
    requester_id character varying(255),
    title character varying(255),
    description character varying(255),
    category character varying(50),
    priority character varying(50),
    status character varying(50),
    assigned_to character varying(255),
    created_date date,
    due_date date,
    resolution character varying(255),
    feedback character varying(255),
    attachment_url character varying(255)
);


ALTER TABLE workflow_runtime.ticket OWNER TO postgres;

--
-- Name: ticket_id_seq; Type: SEQUENCE; Schema: workflow_runtime; Owner: postgres
--

CREATE SEQUENCE workflow_runtime.ticket_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE workflow_runtime.ticket_id_seq OWNER TO postgres;

--
-- Name: ticket_id_seq; Type: SEQUENCE OWNED BY; Schema: workflow_runtime; Owner: postgres
--

ALTER SEQUENCE workflow_runtime.ticket_id_seq OWNED BY workflow_runtime.ticket.id;


--
-- Name: ui_elements; Type: TABLE; Schema: workflow_runtime; Owner: postgres
--

CREATE TABLE workflow_runtime.ui_elements (
    id integer NOT NULL,
    ui_stack_id integer NOT NULL,
    entity_attribute character varying(100) NOT NULL,
    ui_control character varying(50) NOT NULL,
    helper_text text,
    format_as character varying(50),
    options jsonb,
    display_properties jsonb,
    enable_condition text,
    visibility_condition text,
    required_condition text,
    display_text jsonb,
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE workflow_runtime.ui_elements OWNER TO postgres;

--
-- Name: ui_elements_id_seq; Type: SEQUENCE; Schema: workflow_runtime; Owner: postgres
--

CREATE SEQUENCE workflow_runtime.ui_elements_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE workflow_runtime.ui_elements_id_seq OWNER TO postgres;

--
-- Name: ui_elements_id_seq; Type: SEQUENCE OWNED BY; Schema: workflow_runtime; Owner: postgres
--

ALTER SEQUENCE workflow_runtime.ui_elements_id_seq OWNED BY workflow_runtime.ui_elements.id;


--
-- Name: ui_stack; Type: TABLE; Schema: workflow_runtime; Owner: postgres
--

CREATE TABLE workflow_runtime.ui_stack (
    id integer NOT NULL,
    lo_id character varying(50) NOT NULL,
    type character varying(50) NOT NULL,
    status character varying(50) NOT NULL,
    description text,
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE workflow_runtime.ui_stack OWNER TO postgres;

--
-- Name: ui_stack_id_seq; Type: SEQUENCE; Schema: workflow_runtime; Owner: postgres
--

CREATE SEQUENCE workflow_runtime.ui_stack_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE workflow_runtime.ui_stack_id_seq OWNER TO postgres;

--
-- Name: ui_stack_id_seq; Type: SEQUENCE OWNED BY; Schema: workflow_runtime; Owner: postgres
--

ALTER SEQUENCE workflow_runtime.ui_stack_id_seq OWNED BY workflow_runtime.ui_stack.id;


--
-- Name: user_roles; Type: TABLE; Schema: workflow_runtime; Owner: postgres
--

CREATE TABLE workflow_runtime.user_roles (
    user_id character varying(50) NOT NULL,
    username character varying(100) NOT NULL,
    role character varying(50) NOT NULL,
    tenant_id character varying(50)
);


ALTER TABLE workflow_runtime.user_roles OWNER TO postgres;

--
-- Name: workflow_instances; Type: TABLE; Schema: workflow_runtime; Owner: postgres
--

CREATE TABLE workflow_runtime.workflow_instances (
    instance_id uuid NOT NULL,
    go_id character varying(50) NOT NULL,
    tenant_id character varying(50) NOT NULL,
    status character varying(20) NOT NULL,
    started_by character varying(50) NOT NULL,
    started_at timestamp without time zone DEFAULT now(),
    current_lo_id character varying(50),
    instance_data jsonb DEFAULT '{}'::jsonb,
    is_test boolean DEFAULT false,
    version character varying(10) DEFAULT '1.0'::character varying,
    updated_at timestamp without time zone DEFAULT now()
);


ALTER TABLE workflow_runtime.workflow_instances OWNER TO postgres;

--
-- Name: workflow_results; Type: TABLE; Schema: workflow_runtime; Owner: postgres
--

CREATE TABLE workflow_runtime.workflow_results (
    id integer NOT NULL,
    leaves_pending_approval integer,
    leaves_approved_count integer,
    leaves_rejected_count integer,
    duration integer,
    start_time character varying(255),
    end_time character varying(255),
    status character varying(255)
);


ALTER TABLE workflow_runtime.workflow_results OWNER TO postgres;

--
-- Name: workflow_results_id_seq; Type: SEQUENCE; Schema: workflow_runtime; Owner: postgres
--

CREATE SEQUENCE workflow_runtime.workflow_results_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE workflow_runtime.workflow_results_id_seq OWNER TO postgres;

--
-- Name: workflow_results_id_seq; Type: SEQUENCE OWNED BY; Schema: workflow_runtime; Owner: postgres
--

ALTER SEQUENCE workflow_runtime.workflow_results_id_seq OWNED BY workflow_runtime.workflow_results.id;


--
-- Name: workflow_transaction; Type: TABLE; Schema: workflow_runtime; Owner: postgres
--

CREATE TABLE workflow_runtime.workflow_transaction (
    id integer NOT NULL,
    workflow_instance_id character varying(100),
    go_id character varying(50),
    lo_id character varying(50),
    status character varying(50),
    input_stack json,
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    tenant_id character varying(50),
    tenant_name character varying(100),
    user_id character varying(50),
    output_stack json
);


ALTER TABLE workflow_runtime.workflow_transaction OWNER TO postgres;

--
-- Name: workflow_transaction_id_seq; Type: SEQUENCE; Schema: workflow_runtime; Owner: postgres
--

CREATE SEQUENCE workflow_runtime.workflow_transaction_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE workflow_runtime.workflow_transaction_id_seq OWNER TO postgres;

--
-- Name: workflow_transaction_id_seq; Type: SEQUENCE OWNED BY; Schema: workflow_runtime; Owner: postgres
--

ALTER SEQUENCE workflow_runtime.workflow_transaction_id_seq OWNED BY workflow_runtime.workflow_transaction.id;


--
-- Name: agent_rights id; Type: DEFAULT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.agent_rights ALTER COLUMN id SET DEFAULT nextval('workflow_runtime.agent_rights_id_seq'::regclass);


--
-- Name: agent_stack id; Type: DEFAULT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.agent_stack ALTER COLUMN id SET DEFAULT nextval('workflow_runtime.agent_stack_id_seq'::regclass);


--
-- Name: asset_request id; Type: DEFAULT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.asset_request ALTER COLUMN id SET DEFAULT nextval('workflow_runtime.asset_request_id_seq'::regclass);


--
-- Name: attribute_enum_values id; Type: DEFAULT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.attribute_enum_values ALTER COLUMN id SET DEFAULT nextval('workflow_runtime.attribute_enum_values_id_seq'::regclass);


--
-- Name: compliance_verification id; Type: DEFAULT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.compliance_verification ALTER COLUMN id SET DEFAULT nextval('workflow_runtime.compliance_verification_id_seq'::regclass);


--
-- Name: conditional_success_messages id; Type: DEFAULT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.conditional_success_messages ALTER COLUMN id SET DEFAULT nextval('workflow_runtime.conditional_success_messages_id_seq'::regclass);


--
-- Name: data_mapping_stack id; Type: DEFAULT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.data_mapping_stack ALTER COLUMN id SET DEFAULT nextval('workflow_runtime.data_mapping_stack_id_seq'::regclass);


--
-- Name: department id; Type: DEFAULT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.department ALTER COLUMN id SET DEFAULT nextval('workflow_runtime.department_id_seq'::regclass);


--
-- Name: dropdown_data_sources id; Type: DEFAULT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.dropdown_data_sources ALTER COLUMN id SET DEFAULT nextval('workflow_runtime.dropdown_data_sources_id_seq'::regclass);


--
-- Name: entity_permissions id; Type: DEFAULT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.entity_permissions ALTER COLUMN id SET DEFAULT nextval('workflow_runtime.entity_permissions_id_seq'::regclass);


--
-- Name: entity_relationships id; Type: DEFAULT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.entity_relationships ALTER COLUMN id SET DEFAULT nextval('workflow_runtime.entity_relationships_id_seq'::regclass);


--
-- Name: execution_path_tracking id; Type: DEFAULT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.execution_path_tracking ALTER COLUMN id SET DEFAULT nextval('workflow_runtime.execution_path_tracking_id_seq'::regclass);


--
-- Name: execution_pathway_conditions id; Type: DEFAULT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.execution_pathway_conditions ALTER COLUMN id SET DEFAULT nextval('workflow_runtime.execution_pathway_conditions_id_seq'::regclass);


--
-- Name: execution_pathways id; Type: DEFAULT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.execution_pathways ALTER COLUMN id SET DEFAULT nextval('workflow_runtime.execution_pathways_id_seq'::regclass);


--
-- Name: input_data_sources id; Type: DEFAULT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.input_data_sources ALTER COLUMN id SET DEFAULT nextval('workflow_runtime.input_data_sources_id_seq'::regclass);


--
-- Name: input_dependencies id; Type: DEFAULT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.input_dependencies ALTER COLUMN id SET DEFAULT nextval('workflow_runtime.input_dependencies_id_seq'::regclass);


--
-- Name: input_stack id; Type: DEFAULT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.input_stack ALTER COLUMN id SET DEFAULT nextval('workflow_runtime.input_stack_id_seq'::regclass);


--
-- Name: invoice id; Type: DEFAULT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.invoice ALTER COLUMN id SET DEFAULT nextval('workflow_runtime.invoice_id_seq'::regclass);


--
-- Name: it_support_staff id; Type: DEFAULT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.it_support_staff ALTER COLUMN id SET DEFAULT nextval('workflow_runtime.it_support_staff_id_seq'::regclass);


--
-- Name: leave_application id; Type: DEFAULT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.leave_application ALTER COLUMN id SET DEFAULT nextval('workflow_runtime.leave_application_id_seq'::regclass);


--
-- Name: leave_sub_type id; Type: DEFAULT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.leave_sub_type ALTER COLUMN id SET DEFAULT nextval('workflow_runtime.leave_sub_type_id_seq'::regclass);


--
-- Name: leave_sub_types id; Type: DEFAULT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.leave_sub_types ALTER COLUMN id SET DEFAULT nextval('workflow_runtime.leave_sub_types_id_seq'::regclass);


--
-- Name: lo_data_mapping_stack id; Type: DEFAULT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.lo_data_mapping_stack ALTER COLUMN id SET DEFAULT nextval('workflow_runtime.lo_data_mapping_stack_id_seq'::regclass);


--
-- Name: lo_input_execution id; Type: DEFAULT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.lo_input_execution ALTER COLUMN id SET DEFAULT nextval('workflow_runtime.lo_input_execution_id_seq'::regclass);


--
-- Name: lo_input_stack id; Type: DEFAULT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.lo_input_stack ALTER COLUMN id SET DEFAULT nextval('workflow_runtime.lo_input_stack_id_seq'::regclass);


--
-- Name: lo_input_validations id; Type: DEFAULT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.lo_input_validations ALTER COLUMN id SET DEFAULT nextval('workflow_runtime.lo_input_validations_id_seq'::regclass);


--
-- Name: lo_nested_functions id; Type: DEFAULT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.lo_nested_functions ALTER COLUMN id SET DEFAULT nextval('workflow_runtime.lo_nested_functions_id_seq'::regclass);


--
-- Name: lo_output_execution id; Type: DEFAULT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.lo_output_execution ALTER COLUMN id SET DEFAULT nextval('workflow_runtime.lo_output_execution_id_seq'::regclass);


--
-- Name: lo_output_stack id; Type: DEFAULT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.lo_output_stack ALTER COLUMN id SET DEFAULT nextval('workflow_runtime.lo_output_stack_id_seq'::regclass);


--
-- Name: loanapplication id; Type: DEFAULT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.loanapplication ALTER COLUMN id SET DEFAULT nextval('workflow_runtime.loanapplication_id_seq'::regclass);


--
-- Name: loanrate id; Type: DEFAULT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.loanrate ALTER COLUMN id SET DEFAULT nextval('workflow_runtime.loanrate_id_seq'::regclass);


--
-- Name: metrics_aggregation id; Type: DEFAULT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.metrics_aggregation ALTER COLUMN id SET DEFAULT nextval('workflow_runtime.metrics_aggregation_id_seq'::regclass);


--
-- Name: metrics_reporting id; Type: DEFAULT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.metrics_reporting ALTER COLUMN id SET DEFAULT nextval('workflow_runtime.metrics_reporting_id_seq'::regclass);


--
-- Name: objective_permissions id; Type: DEFAULT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.objective_permissions ALTER COLUMN id SET DEFAULT nextval('workflow_runtime.objective_permissions_id_seq'::regclass);


--
-- Name: output_stack id; Type: DEFAULT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.output_stack ALTER COLUMN id SET DEFAULT nextval('workflow_runtime.output_stack_id_seq'::regclass);


--
-- Name: purchase_order id; Type: DEFAULT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.purchase_order ALTER COLUMN id SET DEFAULT nextval('workflow_runtime.purchase_order_id_seq'::regclass);


--
-- Name: purchase_requisition id; Type: DEFAULT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.purchase_requisition ALTER COLUMN id SET DEFAULT nextval('workflow_runtime.purchase_requisition_id_seq'::regclass);


--
-- Name: runtime_metrics_stack id; Type: DEFAULT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.runtime_metrics_stack ALTER COLUMN id SET DEFAULT nextval('workflow_runtime.runtime_metrics_stack_id_seq'::regclass);


--
-- Name: task id; Type: DEFAULT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.task ALTER COLUMN id SET DEFAULT nextval('workflow_runtime.task_id_seq'::regclass);


--
-- Name: teamresource id; Type: DEFAULT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.teamresource ALTER COLUMN id SET DEFAULT nextval('workflow_runtime.teamresource_id_seq'::regclass);


--
-- Name: ticket id; Type: DEFAULT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.ticket ALTER COLUMN id SET DEFAULT nextval('workflow_runtime.ticket_id_seq'::regclass);


--
-- Name: ui_elements id; Type: DEFAULT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.ui_elements ALTER COLUMN id SET DEFAULT nextval('workflow_runtime.ui_elements_id_seq'::regclass);


--
-- Name: ui_stack id; Type: DEFAULT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.ui_stack ALTER COLUMN id SET DEFAULT nextval('workflow_runtime.ui_stack_id_seq'::regclass);


--
-- Name: workflow_results id; Type: DEFAULT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.workflow_results ALTER COLUMN id SET DEFAULT nextval('workflow_runtime.workflow_results_id_seq'::regclass);


--
-- Name: workflow_transaction id; Type: DEFAULT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.workflow_transaction ALTER COLUMN id SET DEFAULT nextval('workflow_runtime.workflow_transaction_id_seq'::regclass);


--
-- Name: agent_rights agent_rights_pkey; Type: CONSTRAINT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.agent_rights
    ADD CONSTRAINT agent_rights_pkey PRIMARY KEY (id);


--
-- Name: agent_stack agent_stack_pkey; Type: CONSTRAINT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.agent_stack
    ADD CONSTRAINT agent_stack_pkey PRIMARY KEY (id);


--
-- Name: alembic_version alembic_version_pkc; Type: CONSTRAINT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.alembic_version
    ADD CONSTRAINT alembic_version_pkc PRIMARY KEY (version_num);


--
-- Name: asset_request asset_request_pkey; Type: CONSTRAINT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.asset_request
    ADD CONSTRAINT asset_request_pkey PRIMARY KEY (id);


--
-- Name: attribute_enum_values attribute_enum_values_pkey; Type: CONSTRAINT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.attribute_enum_values
    ADD CONSTRAINT attribute_enum_values_pkey PRIMARY KEY (id);


--
-- Name: attribute_ui_controls attribute_ui_controls_pkey; Type: CONSTRAINT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.attribute_ui_controls
    ADD CONSTRAINT attribute_ui_controls_pkey PRIMARY KEY (entity_id, attribute_id);


--
-- Name: attribute_validations attribute_validations_pkey; Type: CONSTRAINT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.attribute_validations
    ADD CONSTRAINT attribute_validations_pkey PRIMARY KEY (id);


--
-- Name: compliance_verification compliance_verification_pkey; Type: CONSTRAINT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.compliance_verification
    ADD CONSTRAINT compliance_verification_pkey PRIMARY KEY (id);


--
-- Name: conditional_success_messages conditional_success_messages_pkey; Type: CONSTRAINT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.conditional_success_messages
    ADD CONSTRAINT conditional_success_messages_pkey PRIMARY KEY (id);


--
-- Name: data_mapping_stack data_mapping_stack_pkey; Type: CONSTRAINT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.data_mapping_stack
    ADD CONSTRAINT data_mapping_stack_pkey PRIMARY KEY (id);


--
-- Name: data_mappings data_mappings_pkey; Type: CONSTRAINT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.data_mappings
    ADD CONSTRAINT data_mappings_pkey PRIMARY KEY (id, mapping_stack_id);


--
-- Name: department department_pkey; Type: CONSTRAINT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.department
    ADD CONSTRAINT department_pkey PRIMARY KEY (id);


--
-- Name: dropdown_data_sources dropdown_data_sources_pkey; Type: CONSTRAINT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.dropdown_data_sources
    ADD CONSTRAINT dropdown_data_sources_pkey PRIMARY KEY (id);


--
-- Name: entities entities_pkey; Type: CONSTRAINT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.entities
    ADD CONSTRAINT entities_pkey PRIMARY KEY (entity_id);


--
-- Name: entity_attribute_metadata entity_attribute_metadata_pkey; Type: CONSTRAINT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.entity_attribute_metadata
    ADD CONSTRAINT entity_attribute_metadata_pkey PRIMARY KEY (entity_id, attribute_id);


--
-- Name: entity_attributes entity_attributes_pkey; Type: CONSTRAINT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.entity_attributes
    ADD CONSTRAINT entity_attributes_pkey PRIMARY KEY (attribute_id);


--
-- Name: entity_permissions entity_permissions_pkey; Type: CONSTRAINT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.entity_permissions
    ADD CONSTRAINT entity_permissions_pkey PRIMARY KEY (id);


--
-- Name: entity_relationships entity_relationships_pkey; Type: CONSTRAINT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.entity_relationships
    ADD CONSTRAINT entity_relationships_pkey PRIMARY KEY (id);


--
-- Name: execution_path_tracking execution_path_tracking_pkey; Type: CONSTRAINT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.execution_path_tracking
    ADD CONSTRAINT execution_path_tracking_pkey PRIMARY KEY (id);


--
-- Name: execution_pathway_conditions execution_pathway_conditions_pkey; Type: CONSTRAINT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.execution_pathway_conditions
    ADD CONSTRAINT execution_pathway_conditions_pkey PRIMARY KEY (id);


--
-- Name: execution_pathways execution_pathways_pkey; Type: CONSTRAINT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.execution_pathways
    ADD CONSTRAINT execution_pathways_pkey PRIMARY KEY (id);


--
-- Name: execution_rules execution_rules_pkey; Type: CONSTRAINT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.execution_rules
    ADD CONSTRAINT execution_rules_pkey PRIMARY KEY (id, lo_id);


--
-- Name: global_objectives global_objectives_pkey; Type: CONSTRAINT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.global_objectives
    ADD CONSTRAINT global_objectives_pkey PRIMARY KEY (go_id);


--
-- Name: input_data_sources input_data_sources_pkey; Type: CONSTRAINT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.input_data_sources
    ADD CONSTRAINT input_data_sources_pkey PRIMARY KEY (id);


--
-- Name: input_dependencies input_dependencies_pkey; Type: CONSTRAINT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.input_dependencies
    ADD CONSTRAINT input_dependencies_pkey PRIMARY KEY (id);


--
-- Name: input_items input_items_pkey; Type: CONSTRAINT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.input_items
    ADD CONSTRAINT input_items_pkey PRIMARY KEY (id, input_stack_id);


--
-- Name: input_stack input_stack_pkey; Type: CONSTRAINT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.input_stack
    ADD CONSTRAINT input_stack_pkey PRIMARY KEY (id);


--
-- Name: invoice invoice_pkey; Type: CONSTRAINT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.invoice
    ADD CONSTRAINT invoice_pkey PRIMARY KEY (id);


--
-- Name: it_support_staff it_support_staff_pkey; Type: CONSTRAINT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.it_support_staff
    ADD CONSTRAINT it_support_staff_pkey PRIMARY KEY (id);


--
-- Name: leave_application leave_application_pkey; Type: CONSTRAINT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.leave_application
    ADD CONSTRAINT leave_application_pkey PRIMARY KEY (id);


--
-- Name: leave_sub_type leave_sub_type_pkey; Type: CONSTRAINT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.leave_sub_type
    ADD CONSTRAINT leave_sub_type_pkey PRIMARY KEY (id);


--
-- Name: leave_sub_types leave_sub_types_pkey; Type: CONSTRAINT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.leave_sub_types
    ADD CONSTRAINT leave_sub_types_pkey PRIMARY KEY (id);


--
-- Name: lo_data_mapping_stack lo_data_mapping_stack_pkey; Type: CONSTRAINT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.lo_data_mapping_stack
    ADD CONSTRAINT lo_data_mapping_stack_pkey PRIMARY KEY (id);


--
-- Name: lo_data_mappings lo_data_mappings_pkey; Type: CONSTRAINT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.lo_data_mappings
    ADD CONSTRAINT lo_data_mappings_pkey PRIMARY KEY (id, mapping_stack_id);


--
-- Name: lo_input_execution lo_input_execution_pkey; Type: CONSTRAINT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.lo_input_execution
    ADD CONSTRAINT lo_input_execution_pkey PRIMARY KEY (id);


--
-- Name: lo_input_items lo_input_items_pkey; Type: CONSTRAINT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.lo_input_items
    ADD CONSTRAINT lo_input_items_pkey PRIMARY KEY (id, input_stack_id);


--
-- Name: lo_input_stack lo_input_stack_lo_id_unique; Type: CONSTRAINT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.lo_input_stack
    ADD CONSTRAINT lo_input_stack_lo_id_unique UNIQUE (lo_id);


--
-- Name: lo_input_stack lo_input_stack_pkey; Type: CONSTRAINT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.lo_input_stack
    ADD CONSTRAINT lo_input_stack_pkey PRIMARY KEY (id);


--
-- Name: lo_input_validations lo_input_validations_pkey; Type: CONSTRAINT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.lo_input_validations
    ADD CONSTRAINT lo_input_validations_pkey PRIMARY KEY (id);


--
-- Name: lo_nested_functions lo_nested_functions_pkey; Type: CONSTRAINT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.lo_nested_functions
    ADD CONSTRAINT lo_nested_functions_pkey PRIMARY KEY (id);


--
-- Name: lo_output_execution lo_output_execution_pkey; Type: CONSTRAINT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.lo_output_execution
    ADD CONSTRAINT lo_output_execution_pkey PRIMARY KEY (id);


--
-- Name: lo_output_items lo_output_items_pkey; Type: CONSTRAINT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.lo_output_items
    ADD CONSTRAINT lo_output_items_pkey PRIMARY KEY (id, output_stack_id);


--
-- Name: lo_output_stack lo_output_stack_pkey; Type: CONSTRAINT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.lo_output_stack
    ADD CONSTRAINT lo_output_stack_pkey PRIMARY KEY (id);


--
-- Name: lo_output_triggers lo_output_triggers_pkey; Type: CONSTRAINT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.lo_output_triggers
    ADD CONSTRAINT lo_output_triggers_pkey PRIMARY KEY (id, output_item_id, output_stack_id);


--
-- Name: lo_system_functions lo_system_functions_pkey; Type: CONSTRAINT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.lo_system_functions
    ADD CONSTRAINT lo_system_functions_pkey PRIMARY KEY (function_id, lo_id);


--
-- Name: loanapplication loanapplication_pkey; Type: CONSTRAINT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.loanapplication
    ADD CONSTRAINT loanapplication_pkey PRIMARY KEY (id);


--
-- Name: loanrate loanrate_pkey; Type: CONSTRAINT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.loanrate
    ADD CONSTRAINT loanrate_pkey PRIMARY KEY (id);


--
-- Name: local_objectives local_objectives_pkey; Type: CONSTRAINT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.local_objectives
    ADD CONSTRAINT local_objectives_pkey PRIMARY KEY (lo_id);


--
-- Name: mapping_rules mapping_rules_pkey; Type: CONSTRAINT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.mapping_rules
    ADD CONSTRAINT mapping_rules_pkey PRIMARY KEY (id, mapping_stack_id);


--
-- Name: metrics_aggregation metrics_aggregation_pkey; Type: CONSTRAINT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.metrics_aggregation
    ADD CONSTRAINT metrics_aggregation_pkey PRIMARY KEY (id);


--
-- Name: metrics_reporting metrics_reporting_pkey; Type: CONSTRAINT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.metrics_reporting
    ADD CONSTRAINT metrics_reporting_pkey PRIMARY KEY (id);


--
-- Name: objective_permissions objective_permissions_pkey; Type: CONSTRAINT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.objective_permissions
    ADD CONSTRAINT objective_permissions_pkey PRIMARY KEY (id);


--
-- Name: output_items output_items_pkey; Type: CONSTRAINT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.output_items
    ADD CONSTRAINT output_items_pkey PRIMARY KEY (id, output_stack_id);


--
-- Name: output_stack output_stack_pkey; Type: CONSTRAINT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.output_stack
    ADD CONSTRAINT output_stack_pkey PRIMARY KEY (id);


--
-- Name: output_triggers output_triggers_pkey; Type: CONSTRAINT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.output_triggers
    ADD CONSTRAINT output_triggers_pkey PRIMARY KEY (id, output_item_id, output_stack_id);


--
-- Name: permission_capabilities permission_capabilities_pkey; Type: CONSTRAINT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.permission_capabilities
    ADD CONSTRAINT permission_capabilities_pkey PRIMARY KEY (capability_id);


--
-- Name: permission_types permission_types_pkey; Type: CONSTRAINT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.permission_types
    ADD CONSTRAINT permission_types_pkey PRIMARY KEY (permission_id);


--
-- Name: purchase_order purchase_order_pkey; Type: CONSTRAINT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.purchase_order
    ADD CONSTRAINT purchase_order_pkey PRIMARY KEY (id);


--
-- Name: purchase_requisition purchase_requisition_pkey; Type: CONSTRAINT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.purchase_requisition
    ADD CONSTRAINT purchase_requisition_pkey PRIMARY KEY (id);


--
-- Name: roles roles_pkey; Type: CONSTRAINT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.roles
    ADD CONSTRAINT roles_pkey PRIMARY KEY (role_id);


--
-- Name: runtime_metrics_stack runtime_metrics_stack_pkey; Type: CONSTRAINT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.runtime_metrics_stack
    ADD CONSTRAINT runtime_metrics_stack_pkey PRIMARY KEY (id);


--
-- Name: success_messages success_messages_pkey; Type: CONSTRAINT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.success_messages
    ADD CONSTRAINT success_messages_pkey PRIMARY KEY (lo_id);


--
-- Name: system_functions system_functions_pkey; Type: CONSTRAINT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.system_functions
    ADD CONSTRAINT system_functions_pkey PRIMARY KEY (function_id);


--
-- Name: task task_pkey; Type: CONSTRAINT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.task
    ADD CONSTRAINT task_pkey PRIMARY KEY (id);


--
-- Name: teamresource teamresource_pkey; Type: CONSTRAINT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.teamresource
    ADD CONSTRAINT teamresource_pkey PRIMARY KEY (id);


--
-- Name: tenants tenants_pkey; Type: CONSTRAINT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.tenants
    ADD CONSTRAINT tenants_pkey PRIMARY KEY (tenant_id);


--
-- Name: terminal_pathways terminal_pathways_pkey; Type: CONSTRAINT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.terminal_pathways
    ADD CONSTRAINT terminal_pathways_pkey PRIMARY KEY (lo_id);


--
-- Name: ticket ticket_pkey; Type: CONSTRAINT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.ticket
    ADD CONSTRAINT ticket_pkey PRIMARY KEY (id);


--
-- Name: ui_elements ui_elements_pkey; Type: CONSTRAINT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.ui_elements
    ADD CONSTRAINT ui_elements_pkey PRIMARY KEY (id);


--
-- Name: ui_stack ui_stack_pkey; Type: CONSTRAINT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.ui_stack
    ADD CONSTRAINT ui_stack_pkey PRIMARY KEY (id);


--
-- Name: agent_rights unique_agent_right; Type: CONSTRAINT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.agent_rights
    ADD CONSTRAINT unique_agent_right UNIQUE (agent_stack_id, role_id, right_id);


--
-- Name: agent_stack unique_agent_stack_lo_id; Type: CONSTRAINT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.agent_stack
    ADD CONSTRAINT unique_agent_stack_lo_id UNIQUE (lo_id);


--
-- Name: lo_data_mapping_stack unique_lo_data_mapping_stack_lo_id; Type: CONSTRAINT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.lo_data_mapping_stack
    ADD CONSTRAINT unique_lo_data_mapping_stack_lo_id UNIQUE (lo_id);


--
-- Name: lo_input_stack unique_lo_input_stack; Type: CONSTRAINT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.lo_input_stack
    ADD CONSTRAINT unique_lo_input_stack UNIQUE (id);


--
-- Name: lo_input_stack unique_lo_input_stack_lo_id; Type: CONSTRAINT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.lo_input_stack
    ADD CONSTRAINT unique_lo_input_stack_lo_id UNIQUE (lo_id);


--
-- Name: lo_output_stack unique_lo_output_stack_lo_id; Type: CONSTRAINT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.lo_output_stack
    ADD CONSTRAINT unique_lo_output_stack_lo_id UNIQUE (lo_id);


--
-- Name: roles unique_role_name; Type: CONSTRAINT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.roles
    ADD CONSTRAINT unique_role_name UNIQUE (name);


--
-- Name: user_roles user_roles_pkey; Type: CONSTRAINT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.user_roles
    ADD CONSTRAINT user_roles_pkey PRIMARY KEY (user_id);


--
-- Name: workflow_instances workflow_instances_pkey; Type: CONSTRAINT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.workflow_instances
    ADD CONSTRAINT workflow_instances_pkey PRIMARY KEY (instance_id);


--
-- Name: workflow_results workflow_results_pkey; Type: CONSTRAINT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.workflow_results
    ADD CONSTRAINT workflow_results_pkey PRIMARY KEY (id);


--
-- Name: workflow_transaction workflow_transaction_pkey; Type: CONSTRAINT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.workflow_transaction
    ADD CONSTRAINT workflow_transaction_pkey PRIMARY KEY (id);


--
-- Name: idx_attribute_enum_values_attribute_id; Type: INDEX; Schema: workflow_runtime; Owner: postgres
--

CREATE INDEX idx_attribute_enum_values_attribute_id ON workflow_runtime.attribute_enum_values USING btree (attribute_id);


--
-- Name: idx_attribute_validations_attribute_id; Type: INDEX; Schema: workflow_runtime; Owner: postgres
--

CREATE INDEX idx_attribute_validations_attribute_id ON workflow_runtime.attribute_validations USING btree (attribute_id);


--
-- Name: idx_dropdown_data_sources_input_item; Type: INDEX; Schema: workflow_runtime; Owner: postgres
--

CREATE INDEX idx_dropdown_data_sources_input_item ON workflow_runtime.dropdown_data_sources USING btree (input_item_id, input_stack_id);


--
-- Name: idx_entity_attribute_metadata_attribute_id; Type: INDEX; Schema: workflow_runtime; Owner: postgres
--

CREATE INDEX idx_entity_attribute_metadata_attribute_id ON workflow_runtime.entity_attribute_metadata USING btree (attribute_id);


--
-- Name: idx_entity_attribute_metadata_entity_id; Type: INDEX; Schema: workflow_runtime; Owner: postgres
--

CREATE INDEX idx_entity_attribute_metadata_entity_id ON workflow_runtime.entity_attribute_metadata USING btree (entity_id);


--
-- Name: idx_entity_attributes_entity_id; Type: INDEX; Schema: workflow_runtime; Owner: postgres
--

CREATE INDEX idx_entity_attributes_entity_id ON workflow_runtime.entity_attributes USING btree (entity_id);


--
-- Name: idx_entity_attributes_reference_entity_id; Type: INDEX; Schema: workflow_runtime; Owner: postgres
--

CREATE INDEX idx_entity_attributes_reference_entity_id ON workflow_runtime.entity_attributes USING btree (reference_entity_id);


--
-- Name: idx_entity_permissions_entity_id; Type: INDEX; Schema: workflow_runtime; Owner: postgres
--

CREATE INDEX idx_entity_permissions_entity_id ON workflow_runtime.entity_permissions USING btree (entity_id);


--
-- Name: idx_entity_permissions_permission_id; Type: INDEX; Schema: workflow_runtime; Owner: postgres
--

CREATE INDEX idx_entity_permissions_permission_id ON workflow_runtime.entity_permissions USING btree (permission_id);


--
-- Name: idx_entity_permissions_role_id; Type: INDEX; Schema: workflow_runtime; Owner: postgres
--

CREATE INDEX idx_entity_permissions_role_id ON workflow_runtime.entity_permissions USING btree (role_id);


--
-- Name: idx_entity_relationships_source_entity_id; Type: INDEX; Schema: workflow_runtime; Owner: postgres
--

CREATE INDEX idx_entity_relationships_source_entity_id ON workflow_runtime.entity_relationships USING btree (source_entity_id);


--
-- Name: idx_entity_relationships_target_entity_id; Type: INDEX; Schema: workflow_runtime; Owner: postgres
--

CREATE INDEX idx_entity_relationships_target_entity_id ON workflow_runtime.entity_relationships USING btree (target_entity_id);


--
-- Name: idx_input_dependencies_depends_on; Type: INDEX; Schema: workflow_runtime; Owner: postgres
--

CREATE INDEX idx_input_dependencies_depends_on ON workflow_runtime.input_dependencies USING btree (depends_on_id, depends_on_stack_id);


--
-- Name: idx_input_dependencies_input_item; Type: INDEX; Schema: workflow_runtime; Owner: postgres
--

CREATE INDEX idx_input_dependencies_input_item ON workflow_runtime.input_dependencies USING btree (input_item_id, input_stack_id);


--
-- Name: idx_objective_permissions_objective_id; Type: INDEX; Schema: workflow_runtime; Owner: postgres
--

CREATE INDEX idx_objective_permissions_objective_id ON workflow_runtime.objective_permissions USING btree (objective_id);


--
-- Name: idx_objective_permissions_permission_id; Type: INDEX; Schema: workflow_runtime; Owner: postgres
--

CREATE INDEX idx_objective_permissions_permission_id ON workflow_runtime.objective_permissions USING btree (permission_id);


--
-- Name: idx_objective_permissions_role_id; Type: INDEX; Schema: workflow_runtime; Owner: postgres
--

CREATE INDEX idx_objective_permissions_role_id ON workflow_runtime.objective_permissions USING btree (role_id);


--
-- Name: idx_roles_inherits_from; Type: INDEX; Schema: workflow_runtime; Owner: postgres
--

CREATE INDEX idx_roles_inherits_from ON workflow_runtime.roles USING btree (inherits_from);


--
-- Name: idx_roles_tenant_id; Type: INDEX; Schema: workflow_runtime; Owner: postgres
--

CREATE INDEX idx_roles_tenant_id ON workflow_runtime.roles USING btree (tenant_id);


--
-- Name: asset_request update_asset_request_timestamp; Type: TRIGGER; Schema: workflow_runtime; Owner: postgres
--

CREATE TRIGGER update_asset_request_timestamp BEFORE UPDATE ON workflow_runtime.asset_request FOR EACH ROW EXECUTE FUNCTION workflow_runtime.update_modified_column();


--
-- Name: compliance_verification update_compliance_verification_timestamp; Type: TRIGGER; Schema: workflow_runtime; Owner: postgres
--

CREATE TRIGGER update_compliance_verification_timestamp BEFORE UPDATE ON workflow_runtime.compliance_verification FOR EACH ROW EXECUTE FUNCTION workflow_runtime.update_modified_column();


--
-- Name: department update_department_timestamp; Type: TRIGGER; Schema: workflow_runtime; Owner: postgres
--

CREATE TRIGGER update_department_timestamp BEFORE UPDATE ON workflow_runtime.department FOR EACH ROW EXECUTE FUNCTION workflow_runtime.update_modified_column();


--
-- Name: invoice update_invoice_timestamp; Type: TRIGGER; Schema: workflow_runtime; Owner: postgres
--

CREATE TRIGGER update_invoice_timestamp BEFORE UPDATE ON workflow_runtime.invoice FOR EACH ROW EXECUTE FUNCTION workflow_runtime.update_modified_column();


--
-- Name: it_support_staff update_it_support_staff_timestamp; Type: TRIGGER; Schema: workflow_runtime; Owner: postgres
--

CREATE TRIGGER update_it_support_staff_timestamp BEFORE UPDATE ON workflow_runtime.it_support_staff FOR EACH ROW EXECUTE FUNCTION workflow_runtime.update_modified_column();


--
-- Name: leave_application update_leave_application_timestamp; Type: TRIGGER; Schema: workflow_runtime; Owner: postgres
--

CREATE TRIGGER update_leave_application_timestamp BEFORE UPDATE ON workflow_runtime.leave_application FOR EACH ROW EXECUTE FUNCTION workflow_runtime.update_modified_column();


--
-- Name: leave_sub_type update_leave_sub_type_timestamp; Type: TRIGGER; Schema: workflow_runtime; Owner: postgres
--

CREATE TRIGGER update_leave_sub_type_timestamp BEFORE UPDATE ON workflow_runtime.leave_sub_type FOR EACH ROW EXECUTE FUNCTION workflow_runtime.update_modified_column();


--
-- Name: loanapplication update_loanapplication_timestamp; Type: TRIGGER; Schema: workflow_runtime; Owner: postgres
--

CREATE TRIGGER update_loanapplication_timestamp BEFORE UPDATE ON workflow_runtime.loanapplication FOR EACH ROW EXECUTE FUNCTION workflow_runtime.update_modified_column();


--
-- Name: loanrate update_loanrate_timestamp; Type: TRIGGER; Schema: workflow_runtime; Owner: postgres
--

CREATE TRIGGER update_loanrate_timestamp BEFORE UPDATE ON workflow_runtime.loanrate FOR EACH ROW EXECUTE FUNCTION workflow_runtime.update_modified_column();


--
-- Name: purchase_order update_purchase_order_timestamp; Type: TRIGGER; Schema: workflow_runtime; Owner: postgres
--

CREATE TRIGGER update_purchase_order_timestamp BEFORE UPDATE ON workflow_runtime.purchase_order FOR EACH ROW EXECUTE FUNCTION workflow_runtime.update_modified_column();


--
-- Name: purchase_requisition update_purchase_requisition_timestamp; Type: TRIGGER; Schema: workflow_runtime; Owner: postgres
--

CREATE TRIGGER update_purchase_requisition_timestamp BEFORE UPDATE ON workflow_runtime.purchase_requisition FOR EACH ROW EXECUTE FUNCTION workflow_runtime.update_modified_column();


--
-- Name: task update_task_timestamp; Type: TRIGGER; Schema: workflow_runtime; Owner: postgres
--

CREATE TRIGGER update_task_timestamp BEFORE UPDATE ON workflow_runtime.task FOR EACH ROW EXECUTE FUNCTION workflow_runtime.update_modified_column();


--
-- Name: teamresource update_teamresource_timestamp; Type: TRIGGER; Schema: workflow_runtime; Owner: postgres
--

CREATE TRIGGER update_teamresource_timestamp BEFORE UPDATE ON workflow_runtime.teamresource FOR EACH ROW EXECUTE FUNCTION workflow_runtime.update_modified_column();


--
-- Name: ticket update_ticket_timestamp; Type: TRIGGER; Schema: workflow_runtime; Owner: postgres
--

CREATE TRIGGER update_ticket_timestamp BEFORE UPDATE ON workflow_runtime.ticket FOR EACH ROW EXECUTE FUNCTION workflow_runtime.update_modified_column();


--
-- Name: agent_rights agent_rights_agent_stack_id_fkey; Type: FK CONSTRAINT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.agent_rights
    ADD CONSTRAINT agent_rights_agent_stack_id_fkey FOREIGN KEY (agent_stack_id) REFERENCES workflow_runtime.agent_stack(id) ON DELETE CASCADE;


--
-- Name: agent_rights agent_rights_right_id_fkey; Type: FK CONSTRAINT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.agent_rights
    ADD CONSTRAINT agent_rights_right_id_fkey FOREIGN KEY (right_id) REFERENCES workflow_runtime.permission_types(permission_id) ON DELETE CASCADE;


--
-- Name: agent_rights agent_rights_role_id_fkey; Type: FK CONSTRAINT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.agent_rights
    ADD CONSTRAINT agent_rights_role_id_fkey FOREIGN KEY (role_id) REFERENCES workflow_runtime.roles(role_id) ON DELETE CASCADE;


--
-- Name: agent_stack agent_stack_lo_id_fkey; Type: FK CONSTRAINT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.agent_stack
    ADD CONSTRAINT agent_stack_lo_id_fkey FOREIGN KEY (lo_id) REFERENCES workflow_runtime.local_objectives(lo_id) ON DELETE CASCADE;


--
-- Name: conditional_success_messages conditional_success_messages_lo_id_fkey; Type: FK CONSTRAINT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.conditional_success_messages
    ADD CONSTRAINT conditional_success_messages_lo_id_fkey FOREIGN KEY (lo_id) REFERENCES workflow_runtime.local_objectives(lo_id) ON DELETE CASCADE;


--
-- Name: data_mapping_stack data_mapping_stack_go_id_fkey; Type: FK CONSTRAINT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.data_mapping_stack
    ADD CONSTRAINT data_mapping_stack_go_id_fkey FOREIGN KEY (go_id) REFERENCES workflow_runtime.global_objectives(go_id) ON DELETE CASCADE;


--
-- Name: data_mappings data_mappings_mapping_stack_id_fkey; Type: FK CONSTRAINT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.data_mappings
    ADD CONSTRAINT data_mappings_mapping_stack_id_fkey FOREIGN KEY (mapping_stack_id) REFERENCES workflow_runtime.data_mapping_stack(id) ON DELETE CASCADE;


--
-- Name: dropdown_data_sources dropdown_data_sources_input_item_id_input_stack_id_fkey; Type: FK CONSTRAINT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.dropdown_data_sources
    ADD CONSTRAINT dropdown_data_sources_input_item_id_input_stack_id_fkey FOREIGN KEY (input_item_id, input_stack_id) REFERENCES workflow_runtime.lo_input_items(id, input_stack_id) ON DELETE CASCADE;


--
-- Name: entity_permissions entity_permissions_permission_id_fkey; Type: FK CONSTRAINT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.entity_permissions
    ADD CONSTRAINT entity_permissions_permission_id_fkey FOREIGN KEY (permission_id) REFERENCES workflow_runtime.permission_types(permission_id) ON DELETE CASCADE;


--
-- Name: entity_permissions entity_permissions_role_id_fkey; Type: FK CONSTRAINT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.entity_permissions
    ADD CONSTRAINT entity_permissions_role_id_fkey FOREIGN KEY (role_id) REFERENCES workflow_runtime.roles(role_id) ON DELETE CASCADE;


--
-- Name: entity_relationships entity_relationships_target_entity_id_fkey; Type: FK CONSTRAINT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.entity_relationships
    ADD CONSTRAINT entity_relationships_target_entity_id_fkey FOREIGN KEY (target_entity_id) REFERENCES workflow_runtime.entities(entity_id) ON DELETE CASCADE;


--
-- Name: execution_path_tracking execution_path_tracking_metrics_stack_id_fkey; Type: FK CONSTRAINT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.execution_path_tracking
    ADD CONSTRAINT execution_path_tracking_metrics_stack_id_fkey FOREIGN KEY (metrics_stack_id) REFERENCES workflow_runtime.runtime_metrics_stack(id) ON DELETE CASCADE;


--
-- Name: execution_pathway_conditions execution_pathway_conditions_lo_id_fkey; Type: FK CONSTRAINT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.execution_pathway_conditions
    ADD CONSTRAINT execution_pathway_conditions_lo_id_fkey FOREIGN KEY (lo_id) REFERENCES workflow_runtime.local_objectives(lo_id) ON DELETE CASCADE;


--
-- Name: execution_pathway_conditions execution_pathway_conditions_next_lo_fkey; Type: FK CONSTRAINT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.execution_pathway_conditions
    ADD CONSTRAINT execution_pathway_conditions_next_lo_fkey FOREIGN KEY (next_lo) REFERENCES workflow_runtime.local_objectives(lo_id) ON DELETE CASCADE;


--
-- Name: execution_pathways execution_pathways_lo_id_fkey; Type: FK CONSTRAINT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.execution_pathways
    ADD CONSTRAINT execution_pathways_lo_id_fkey FOREIGN KEY (lo_id) REFERENCES workflow_runtime.local_objectives(lo_id) ON DELETE CASCADE;


--
-- Name: execution_rules execution_rules_lo_id_fkey; Type: FK CONSTRAINT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.execution_rules
    ADD CONSTRAINT execution_rules_lo_id_fkey FOREIGN KEY (lo_id) REFERENCES workflow_runtime.local_objectives(lo_id) ON DELETE CASCADE;


--
-- Name: global_objectives global_objectives_tenant_id_fkey; Type: FK CONSTRAINT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.global_objectives
    ADD CONSTRAINT global_objectives_tenant_id_fkey FOREIGN KEY (tenant_id) REFERENCES workflow_runtime.tenants(tenant_id);


--
-- Name: input_data_sources input_data_sources_input_item_id_input_stack_id_fkey; Type: FK CONSTRAINT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.input_data_sources
    ADD CONSTRAINT input_data_sources_input_item_id_input_stack_id_fkey FOREIGN KEY (input_item_id, input_stack_id) REFERENCES workflow_runtime.lo_input_items(id, input_stack_id) ON DELETE CASCADE;


--
-- Name: input_dependencies input_dependencies_depends_on_id_depends_on_stack_id_fkey; Type: FK CONSTRAINT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.input_dependencies
    ADD CONSTRAINT input_dependencies_depends_on_id_depends_on_stack_id_fkey FOREIGN KEY (depends_on_id, depends_on_stack_id) REFERENCES workflow_runtime.lo_input_items(id, input_stack_id) ON DELETE CASCADE;


--
-- Name: input_dependencies input_dependencies_input_item_id_input_stack_id_fkey; Type: FK CONSTRAINT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.input_dependencies
    ADD CONSTRAINT input_dependencies_input_item_id_input_stack_id_fkey FOREIGN KEY (input_item_id, input_stack_id) REFERENCES workflow_runtime.lo_input_items(id, input_stack_id) ON DELETE CASCADE;


--
-- Name: input_items input_items_entity_reference_fkey; Type: FK CONSTRAINT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.input_items
    ADD CONSTRAINT input_items_entity_reference_fkey FOREIGN KEY (entity_reference) REFERENCES workflow_runtime.entities(entity_id) ON DELETE SET NULL;


--
-- Name: input_items input_items_input_stack_id_fkey; Type: FK CONSTRAINT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.input_items
    ADD CONSTRAINT input_items_input_stack_id_fkey FOREIGN KEY (input_stack_id) REFERENCES workflow_runtime.input_stack(id) ON DELETE CASCADE;


--
-- Name: input_stack input_stack_go_id_fkey; Type: FK CONSTRAINT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.input_stack
    ADD CONSTRAINT input_stack_go_id_fkey FOREIGN KEY (go_id) REFERENCES workflow_runtime.global_objectives(go_id) ON DELETE CASCADE;


--
-- Name: lo_data_mapping_stack lo_data_mapping_stack_lo_id_fkey; Type: FK CONSTRAINT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.lo_data_mapping_stack
    ADD CONSTRAINT lo_data_mapping_stack_lo_id_fkey FOREIGN KEY (lo_id) REFERENCES workflow_runtime.local_objectives(lo_id) ON DELETE CASCADE;


--
-- Name: lo_data_mappings lo_data_mappings_mapping_stack_id_fkey; Type: FK CONSTRAINT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.lo_data_mappings
    ADD CONSTRAINT lo_data_mappings_mapping_stack_id_fkey FOREIGN KEY (mapping_stack_id) REFERENCES workflow_runtime.lo_data_mapping_stack(id) ON DELETE CASCADE;


--
-- Name: lo_input_execution lo_input_execution_instance_id_fkey; Type: FK CONSTRAINT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.lo_input_execution
    ADD CONSTRAINT lo_input_execution_instance_id_fkey FOREIGN KEY (instance_id) REFERENCES workflow_runtime.workflow_instances(instance_id);


--
-- Name: lo_input_execution lo_input_execution_lo_id_fkey; Type: FK CONSTRAINT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.lo_input_execution
    ADD CONSTRAINT lo_input_execution_lo_id_fkey FOREIGN KEY (lo_id) REFERENCES workflow_runtime.local_objectives(lo_id);


--
-- Name: lo_input_items lo_input_items_input_stack_id_fkey; Type: FK CONSTRAINT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.lo_input_items
    ADD CONSTRAINT lo_input_items_input_stack_id_fkey FOREIGN KEY (input_stack_id) REFERENCES workflow_runtime.lo_input_stack(id) ON DELETE CASCADE;


--
-- Name: lo_input_stack lo_input_stack_lo_id_fkey; Type: FK CONSTRAINT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.lo_input_stack
    ADD CONSTRAINT lo_input_stack_lo_id_fkey FOREIGN KEY (lo_id) REFERENCES workflow_runtime.local_objectives(lo_id) ON DELETE CASCADE;


--
-- Name: lo_input_validations lo_input_validations_input_item_id_input_stack_id_fkey; Type: FK CONSTRAINT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.lo_input_validations
    ADD CONSTRAINT lo_input_validations_input_item_id_input_stack_id_fkey FOREIGN KEY (input_item_id, input_stack_id) REFERENCES workflow_runtime.lo_input_items(id, input_stack_id) ON DELETE CASCADE;


--
-- Name: lo_output_execution lo_output_execution_instance_id_fkey; Type: FK CONSTRAINT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.lo_output_execution
    ADD CONSTRAINT lo_output_execution_instance_id_fkey FOREIGN KEY (instance_id) REFERENCES workflow_runtime.workflow_instances(instance_id);


--
-- Name: lo_output_execution lo_output_execution_lo_id_fkey; Type: FK CONSTRAINT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.lo_output_execution
    ADD CONSTRAINT lo_output_execution_lo_id_fkey FOREIGN KEY (lo_id) REFERENCES workflow_runtime.local_objectives(lo_id);


--
-- Name: lo_output_items lo_output_items_output_stack_id_fkey; Type: FK CONSTRAINT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.lo_output_items
    ADD CONSTRAINT lo_output_items_output_stack_id_fkey FOREIGN KEY (output_stack_id) REFERENCES workflow_runtime.lo_output_stack(id) ON DELETE CASCADE;


--
-- Name: lo_output_stack lo_output_stack_lo_id_fkey; Type: FK CONSTRAINT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.lo_output_stack
    ADD CONSTRAINT lo_output_stack_lo_id_fkey FOREIGN KEY (lo_id) REFERENCES workflow_runtime.local_objectives(lo_id) ON DELETE CASCADE;


--
-- Name: lo_output_triggers lo_output_triggers_output_item_id_output_stack_id_fkey; Type: FK CONSTRAINT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.lo_output_triggers
    ADD CONSTRAINT lo_output_triggers_output_item_id_output_stack_id_fkey FOREIGN KEY (output_item_id, output_stack_id) REFERENCES workflow_runtime.lo_output_items(id, output_stack_id) ON DELETE CASCADE;


--
-- Name: lo_system_functions lo_system_functions_lo_id_fkey; Type: FK CONSTRAINT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.lo_system_functions
    ADD CONSTRAINT lo_system_functions_lo_id_fkey FOREIGN KEY (lo_id) REFERENCES workflow_runtime.local_objectives(lo_id) ON DELETE CASCADE;


--
-- Name: local_objectives local_objectives_go_id_fkey; Type: FK CONSTRAINT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.local_objectives
    ADD CONSTRAINT local_objectives_go_id_fkey FOREIGN KEY (go_id) REFERENCES workflow_runtime.global_objectives(go_id) ON DELETE CASCADE;


--
-- Name: mapping_rules mapping_rules_condition_entity_fkey; Type: FK CONSTRAINT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.mapping_rules
    ADD CONSTRAINT mapping_rules_condition_entity_fkey FOREIGN KEY (condition_entity) REFERENCES workflow_runtime.entities(entity_id) ON DELETE SET NULL;


--
-- Name: mapping_rules mapping_rules_mapping_stack_id_fkey; Type: FK CONSTRAINT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.mapping_rules
    ADD CONSTRAINT mapping_rules_mapping_stack_id_fkey FOREIGN KEY (mapping_stack_id) REFERENCES workflow_runtime.data_mapping_stack(id) ON DELETE CASCADE;


--
-- Name: metrics_aggregation metrics_aggregation_metrics_stack_id_fkey; Type: FK CONSTRAINT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.metrics_aggregation
    ADD CONSTRAINT metrics_aggregation_metrics_stack_id_fkey FOREIGN KEY (metrics_stack_id) REFERENCES workflow_runtime.runtime_metrics_stack(id) ON DELETE CASCADE;


--
-- Name: metrics_reporting metrics_reporting_metrics_stack_id_fkey; Type: FK CONSTRAINT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.metrics_reporting
    ADD CONSTRAINT metrics_reporting_metrics_stack_id_fkey FOREIGN KEY (metrics_stack_id) REFERENCES workflow_runtime.runtime_metrics_stack(id) ON DELETE CASCADE;


--
-- Name: objective_permissions objective_permissions_permission_id_fkey; Type: FK CONSTRAINT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.objective_permissions
    ADD CONSTRAINT objective_permissions_permission_id_fkey FOREIGN KEY (permission_id) REFERENCES workflow_runtime.permission_types(permission_id) ON DELETE CASCADE;


--
-- Name: objective_permissions objective_permissions_role_id_fkey; Type: FK CONSTRAINT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.objective_permissions
    ADD CONSTRAINT objective_permissions_role_id_fkey FOREIGN KEY (role_id) REFERENCES workflow_runtime.roles(role_id) ON DELETE CASCADE;


--
-- Name: output_items output_items_output_entity_fkey; Type: FK CONSTRAINT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.output_items
    ADD CONSTRAINT output_items_output_entity_fkey FOREIGN KEY (output_entity) REFERENCES workflow_runtime.entities(entity_id) ON DELETE SET NULL;


--
-- Name: output_items output_items_output_stack_id_fkey; Type: FK CONSTRAINT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.output_items
    ADD CONSTRAINT output_items_output_stack_id_fkey FOREIGN KEY (output_stack_id) REFERENCES workflow_runtime.output_stack(id) ON DELETE CASCADE;


--
-- Name: output_stack output_stack_go_id_fkey; Type: FK CONSTRAINT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.output_stack
    ADD CONSTRAINT output_stack_go_id_fkey FOREIGN KEY (go_id) REFERENCES workflow_runtime.global_objectives(go_id) ON DELETE CASCADE;


--
-- Name: output_triggers output_triggers_output_item_id_output_stack_id_fkey; Type: FK CONSTRAINT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.output_triggers
    ADD CONSTRAINT output_triggers_output_item_id_output_stack_id_fkey FOREIGN KEY (output_item_id, output_stack_id) REFERENCES workflow_runtime.output_items(id, output_stack_id) ON DELETE CASCADE;


--
-- Name: output_triggers output_triggers_target_objective_fkey; Type: FK CONSTRAINT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.output_triggers
    ADD CONSTRAINT output_triggers_target_objective_fkey FOREIGN KEY (target_objective) REFERENCES workflow_runtime.global_objectives(go_id) ON DELETE CASCADE;


--
-- Name: permission_capabilities permission_capabilities_permission_id_fkey; Type: FK CONSTRAINT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.permission_capabilities
    ADD CONSTRAINT permission_capabilities_permission_id_fkey FOREIGN KEY (permission_id) REFERENCES workflow_runtime.permission_types(permission_id) ON DELETE CASCADE;


--
-- Name: roles roles_inherits_from_fkey; Type: FK CONSTRAINT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.roles
    ADD CONSTRAINT roles_inherits_from_fkey FOREIGN KEY (inherits_from) REFERENCES workflow_runtime.roles(role_id) ON DELETE SET NULL;


--
-- Name: roles roles_tenant_id_fkey; Type: FK CONSTRAINT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.roles
    ADD CONSTRAINT roles_tenant_id_fkey FOREIGN KEY (tenant_id) REFERENCES workflow_runtime.tenants(tenant_id) ON DELETE CASCADE;


--
-- Name: runtime_metrics_stack runtime_metrics_stack_go_id_fkey; Type: FK CONSTRAINT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.runtime_metrics_stack
    ADD CONSTRAINT runtime_metrics_stack_go_id_fkey FOREIGN KEY (go_id) REFERENCES workflow_runtime.global_objectives(go_id) ON DELETE CASCADE;


--
-- Name: runtime_metrics_stack runtime_metrics_stack_metrics_entity_fkey; Type: FK CONSTRAINT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.runtime_metrics_stack
    ADD CONSTRAINT runtime_metrics_stack_metrics_entity_fkey FOREIGN KEY (metrics_entity) REFERENCES workflow_runtime.entities(entity_id) ON DELETE CASCADE;


--
-- Name: success_messages success_messages_lo_id_fkey; Type: FK CONSTRAINT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.success_messages
    ADD CONSTRAINT success_messages_lo_id_fkey FOREIGN KEY (lo_id) REFERENCES workflow_runtime.local_objectives(lo_id) ON DELETE CASCADE;


--
-- Name: terminal_pathways terminal_pathways_lo_id_fkey; Type: FK CONSTRAINT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.terminal_pathways
    ADD CONSTRAINT terminal_pathways_lo_id_fkey FOREIGN KEY (lo_id) REFERENCES workflow_runtime.local_objectives(lo_id) ON DELETE CASCADE;


--
-- Name: ui_elements ui_elements_ui_stack_id_fkey; Type: FK CONSTRAINT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.ui_elements
    ADD CONSTRAINT ui_elements_ui_stack_id_fkey FOREIGN KEY (ui_stack_id) REFERENCES workflow_runtime.ui_stack(id) ON DELETE CASCADE;


--
-- Name: ui_stack ui_stack_lo_id_fkey; Type: FK CONSTRAINT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.ui_stack
    ADD CONSTRAINT ui_stack_lo_id_fkey FOREIGN KEY (lo_id) REFERENCES workflow_runtime.local_objectives(lo_id) ON DELETE CASCADE;


--
-- Name: user_roles user_roles_role_fkey; Type: FK CONSTRAINT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.user_roles
    ADD CONSTRAINT user_roles_role_fkey FOREIGN KEY (role) REFERENCES workflow_runtime.roles(name);


--
-- Name: user_roles user_roles_tenant_id_fkey; Type: FK CONSTRAINT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.user_roles
    ADD CONSTRAINT user_roles_tenant_id_fkey FOREIGN KEY (tenant_id) REFERENCES workflow_runtime.tenants(tenant_id) ON DELETE CASCADE;


--
-- PostgreSQL database dump complete
--

