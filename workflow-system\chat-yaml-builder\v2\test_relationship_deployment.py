#!/usr/bin/env python3
"""
Test script to verify that relationship properties are correctly parsed and deployed.
This script will parse the sample entity file and deploy the relationships to the database.
"""

import os
import json
import logging
from parsers.entity_parser import parse_entities
from deployers.entity_deployer_v2 import deploy_entity_relationships, execute_query

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger('test_relationship_deployment')

def test_relationship_deployment():
    """
    Test that relationship properties are correctly parsed and deployed.
    """
    # Read the sample entity output file
    sample_file_path = '/home/<USER>/workflow-system/chat-yaml-builder/v2/samples/sample_entity_output2.txt'
    with open(sample_file_path, 'r') as f:
        entity_def = f.read()

    # Parse the entity definition
    entities_data, warnings = parse_entities(entity_def)
    
    # Print any warnings
    if warnings:
        logger.warning("Warnings during parsing:")
        for warning in warnings:
            logger.warning(f"  - {warning}")
    
    # Check if Employee entity was parsed
    if 'entities' in entities_data and 'Employee' in entities_data['entities']:
        employee_entity = entities_data['entities']['Employee']
        
        # Check if relationships were parsed
        if 'relationships' in employee_entity:
            logger.info("\nEmployee relationships:")
            for rel_name, rel_def in employee_entity['relationships'].items():
                logger.info(f"\n  - Relationship: {rel_name}")
                logger.info(f"    - Type: {rel_def.get('type', 'N/A')}")
                logger.info(f"    - Entity: {rel_def.get('entity', 'N/A')}")
                logger.info(f"    - Source Attribute: {rel_def.get('source_attribute', 'N/A')}")
                logger.info(f"    - Target Attribute: {rel_def.get('target_attribute', 'N/A')}")
                
                # Check if relationship properties were parsed
                if 'properties' in rel_def:
                    logger.info(f"    - Properties:")
                    for prop_name, prop_value in rel_def['properties'].items():
                        logger.info(f"      - {prop_name}: {prop_value}")
                else:
                    logger.info(f"    - No properties found for this relationship")
        else:
            logger.error("No relationships found in Employee entity")
    else:
        logger.error("Employee entity not found in parsed data")
    
    # Get the Employee entity ID from the database
    schema_name = 'workflow_temp'
    success, query_messages, result = execute_query(
        f"SELECT entity_id FROM {schema_name}.entities WHERE name = 'Employee'",
        schema_name=schema_name
    )
    
    if not success or not result:
        logger.error("Employee entity not found in the database")
        return
    
    employee_id = result[0][0]
    logger.info(f"Found Employee entity in database with ID: {employee_id}")
    
    # Check if Department entity exists
    success, query_messages, result = execute_query(
        f"SELECT entity_id FROM {schema_name}.entities WHERE name = 'Department'",
        schema_name=schema_name
    )
    
    if not success or not result:
        logger.error("Department entity not found in the database")
        logger.info("Creating Department entity...")
        
        # Create Department entity
        query = f"""
            INSERT INTO {schema_name}.entities (
                entity_id, name, version, status, type, created_at, updated_at, description
            ) VALUES (%s, %s, %s, %s, %s, NOW(), NOW(), %s)
        """
        
        department_id = "E000003"  # Assuming this is the next available ID
        description = "Represents a department within the organization"
        status = 'Active'
        entity_type = 'Standard'
        version = 1.0
        
        success, query_messages, _ = execute_query(
            query,
            (department_id, "Department", version, status, entity_type, description),
            schema_name
        )
        
        if not success:
            logger.error(f"Failed to create Department entity: {query_messages}")
            return
        
        logger.info("Department entity created successfully")
    else:
        department_id = result[0][0]
        logger.info(f"Found Department entity in database with ID: {department_id}")
    
    # Check the structure of the entity_attributes table
    success, query_messages, result = execute_query(
        f"""
        SELECT column_name, data_type 
        FROM information_schema.columns 
        WHERE table_schema = %s AND table_name = 'entity_attributes'
        """,
        (schema_name,),
        schema_name
    )
    
    if success and result:
        logger.info("Entity attributes table structure:")
        for row in result:
            logger.info(f"  - Column: {row[0]}, Type: {row[1]}")
    else:
        logger.error(f"Failed to get entity_attributes table structure: {query_messages}")
    
    # Check if departmentId attribute exists for Employee
    success, query_messages, result = execute_query(
        f"""
        SELECT attribute_id FROM {schema_name}.entity_attributes 
        WHERE entity_id = %s AND name = 'departmentId'
        """,
        (employee_id,),
        schema_name
    )
    
    if not success or not result:
        logger.info("Creating departmentId attribute for Employee...")
        
        # Create departmentId attribute with the correct columns
        # Based on the table structure, we'll use the correct column names
        query = f"""
            INSERT INTO {schema_name}.entity_attributes (
                attribute_id, entity_id, name, display_name, datatype, type, version, status, 
                required, created_at, updated_at, created_by, updated_by
            ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, NOW(), NOW(), %s, %s)
        """
        
        attribute_id = f"{employee_id}.At1"  # Assuming this is the next available ID
        
        success, query_messages, _ = execute_query(
            query,
            (
                attribute_id, 
                employee_id, 
                "departmentId", 
                "Department ID", 
                "string",  # datatype
                "string",  # type
                "1.0", 
                "Active", 
                False, 
                "system", 
                "system"
            ),
            schema_name
        )
        
        if not success:
            logger.error(f"Failed to create departmentId attribute: {query_messages}")
            return
        
        logger.info("departmentId attribute created successfully")
    else:
        logger.info(f"Found departmentId attribute with ID: {result[0][0]}")
    
    # Check if departmentId attribute exists for Department
    success, query_messages, result = execute_query(
        f"""
        SELECT attribute_id FROM {schema_name}.entity_attributes 
        WHERE entity_id = %s AND name = 'departmentId'
        """,
        (department_id,),
        schema_name
    )
    
    if not success or not result:
        logger.info("Creating departmentId attribute for Department...")
        
        # Create departmentId attribute with the correct columns
        query = f"""
            INSERT INTO {schema_name}.entity_attributes (
                attribute_id, entity_id, name, display_name, datatype, type, version, status, 
                required, created_at, updated_at, created_by, updated_by
            ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, NOW(), NOW(), %s, %s)
        """
        
        attribute_id = f"{department_id}.At1"  # Assuming this is the next available ID
        
        success, query_messages, _ = execute_query(
            query,
            (
                attribute_id, 
                department_id, 
                "departmentId", 
                "Department ID", 
                "string",  # datatype
                "string",  # type
                "1.0", 
                "Active", 
                True,  # Required/Primary key 
                "system", 
                "system"
            ),
            schema_name
        )
        
        if not success:
            logger.error(f"Failed to create departmentId attribute for Department: {query_messages}")
            return
        
        logger.info("departmentId attribute for Department created successfully")
    else:
        logger.info(f"Found departmentId attribute for Department with ID: {result[0][0]}")
    
    # Deploy the relationships
    if 'entities' in entities_data and 'Employee' in entities_data['entities']:
        employee_entity = entities_data['entities']['Employee']
        if 'relationships' in employee_entity:
            success, messages = deploy_entity_relationships(
                employee_id,
                employee_entity['relationships'],
                schema_name
            )
            
            logger.info("Deployment messages:")
            for message in messages:
                logger.info(f"  - {message}")
            
            if success:
                logger.info("Relationship deployment successful")
            else:
                logger.error("Relationship deployment failed")
        else:
            logger.error("No relationships found in Employee entity")
    else:
        logger.error("Employee entity not found in parsed data")
    
    # Query the database to verify the relationship properties
    query = """
    SELECT 
        er.id, 
        s.name as source_entity, 
        t.name as target_entity, 
        er.relationship_type, 
        sa.name as source_attribute, 
        ta.name as target_attribute,
        er.on_delete,
        er.on_update,
        er.foreign_key_type
    FROM 
        entity_relationships er
        JOIN entities s ON er.source_entity_id = s.entity_id
        JOIN entities t ON er.target_entity_id = t.entity_id
        JOIN entity_attributes sa ON er.source_attribute_id = sa.attribute_id
        JOIN entity_attributes ta ON er.target_attribute_id = ta.attribute_id
    WHERE 
        s.name = 'Employee'
    """
    
    success, query_messages, result = execute_query(query, schema_name=schema_name)
    
    if success and result:
        logger.info("\nEmployee relationships in the database:")
        for row in result:
            logger.info(f"\n  - ID: {row[0]}")
            logger.info(f"    - Source Entity: {row[1]}")
            logger.info(f"    - Target Entity: {row[2]}")
            logger.info(f"    - Relationship Type: {row[3]}")
            logger.info(f"    - Source Attribute: {row[4]}")
            logger.info(f"    - Target Attribute: {row[5]}")
            logger.info(f"    - ON DELETE: {row[6]}")
            logger.info(f"    - ON UPDATE: {row[7]}")
            logger.info(f"    - Foreign Key Type: {row[8]}")
    else:
        logger.error("No Employee relationships found in the database")

if __name__ == "__main__":
    test_relationship_deployment()
