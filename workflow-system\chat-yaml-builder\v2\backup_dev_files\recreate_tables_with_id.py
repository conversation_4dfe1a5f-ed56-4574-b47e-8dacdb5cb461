"""
Recreate entity tables with ID column and audit columns.
"""

import os
import sys
import logging
from typing import Dict, List, Tuple, Any, Optional

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('recreate_tables_with_id.log')
    ]
)
logger = logging.getLogger('recreate_tables_with_id')

# Add parent directory to path to import common modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from db_utils import execute_query

def drop_all_entity_tables(schema_name: str) -> Tuple[bool, List[str]]:
    """
    Drop all entity tables.
    
    Args:
        schema_name: Schema name
        
    Returns:
        Tuple containing:
            - Boolean indicating if drop was successful
            - List of messages (warnings, errors, or success messages)
    """
    messages = []
    
    try:
        # Drop tables in the correct order to avoid foreign key constraint issues
        tables = [
            "attribute_enum_values",
            "attribute_validations",
            "calculated_fields",
            "entity_lifecycle_management",
            "entity_attribute_metadata",
            "entity_business_rules",
            "entity_relationships",
            "entity_attributes",
            "entities"
        ]
        
        for table in tables:
            query = f"""
                DROP TABLE IF EXISTS {schema_name}.{table} CASCADE
            """
            
            success, query_messages, _ = execute_query(query)
            
            if not success:
                messages.extend(query_messages)
                return False, messages
            
            messages.append(f"Dropped table {schema_name}.{table}")
            logger.info(f"Dropped table {schema_name}.{table}")
        
        return True, messages
    except Exception as e:
        error_msg = f"Error dropping entity tables: {str(e)}"
        logger.error(error_msg, exc_info=True)
        messages.append(error_msg)
        return False, messages

def create_entities_table(schema_name: str) -> Tuple[bool, List[str]]:
    """
    Create entities table with ID column and audit columns.
    
    Args:
        schema_name: Schema name
        
    Returns:
        Tuple containing:
            - Boolean indicating if creation was successful
            - List of messages (warnings, errors, or success messages)
    """
    messages = []
    
    try:
        query = f"""
            CREATE TABLE {schema_name}.entities (
                id SERIAL PRIMARY KEY,
                entity_id VARCHAR(50) UNIQUE NOT NULL,
                name VARCHAR(100) NOT NULL,
                description TEXT,
                metadata JSONB,
                lifecycle_management JSONB,
                version_type VARCHAR(10) DEFAULT 'v2',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                created_by VARCHAR(100) DEFAULT 'system',
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_by VARCHAR(100) DEFAULT 'system'
            )
        """
        
        success, query_messages, _ = execute_query(query)
        
        if not success:
            messages.extend(query_messages)
            return False, messages
        
        messages.append(f"Created table {schema_name}.entities")
        logger.info(f"Created table {schema_name}.entities")
        
        return True, messages
    except Exception as e:
        error_msg = f"Error creating entities table: {str(e)}"
        logger.error(error_msg, exc_info=True)
        messages.append(error_msg)
        return False, messages

def create_entity_attributes_table(schema_name: str) -> Tuple[bool, List[str]]:
    """
    Create entity_attributes table with ID column and audit columns.
    
    Args:
        schema_name: Schema name
        
    Returns:
        Tuple containing:
            - Boolean indicating if creation was successful
            - List of messages (warnings, errors, or success messages)
    """
    messages = []
    
    try:
        query = f"""
            CREATE TABLE {schema_name}.entity_attributes (
                id SERIAL PRIMARY KEY,
                attribute_id VARCHAR(100) UNIQUE NOT NULL,
                entity_id VARCHAR(50) REFERENCES {schema_name}.entities(entity_id) ON DELETE CASCADE,
                name VARCHAR(100) NOT NULL,
                type VARCHAR(50) NOT NULL,
                required BOOLEAN DEFAULT FALSE,
                default_value TEXT,
                calculated_field BOOLEAN DEFAULT FALSE,
                calculation_formula TEXT,
                dependencies JSONB,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                created_by VARCHAR(100) DEFAULT 'system',
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_by VARCHAR(100) DEFAULT 'system'
            )
        """
        
        success, query_messages, _ = execute_query(query)
        
        if not success:
            messages.extend(query_messages)
            return False, messages
        
        messages.append(f"Created table {schema_name}.entity_attributes")
        logger.info(f"Created table {schema_name}.entity_attributes")
        
        return True, messages
    except Exception as e:
        error_msg = f"Error creating entity_attributes table: {str(e)}"
        logger.error(error_msg, exc_info=True)
        messages.append(error_msg)
        return False, messages

def create_entity_attribute_metadata_table(schema_name: str) -> Tuple[bool, List[str]]:
    """
    Create entity_attribute_metadata table with ID column and audit columns.
    
    Args:
        schema_name: Schema name
        
    Returns:
        Tuple containing:
            - Boolean indicating if creation was successful
            - List of messages (warnings, errors, or success messages)
    """
    messages = []
    
    try:
        query = f"""
            CREATE TABLE {schema_name}.entity_attribute_metadata (
                id SERIAL PRIMARY KEY,
                entity_id VARCHAR(50) REFERENCES {schema_name}.entities(entity_id) ON DELETE CASCADE,
                attribute_id VARCHAR(100) REFERENCES {schema_name}.entity_attributes(attribute_id) ON DELETE CASCADE,
                attribute_name VARCHAR(100) NOT NULL,
                required BOOLEAN DEFAULT FALSE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                created_by VARCHAR(100) DEFAULT 'system',
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_by VARCHAR(100) DEFAULT 'system',
                UNIQUE(entity_id, attribute_id)
            )
        """
        
        success, query_messages, _ = execute_query(query)
        
        if not success:
            messages.extend(query_messages)
            return False, messages
        
        messages.append(f"Created table {schema_name}.entity_attribute_metadata")
        logger.info(f"Created table {schema_name}.entity_attribute_metadata")
        
        return True, messages
    except Exception as e:
        error_msg = f"Error creating entity_attribute_metadata table: {str(e)}"
        logger.error(error_msg, exc_info=True)
        messages.append(error_msg)
        return False, messages

def create_entity_relationships_table(schema_name: str) -> Tuple[bool, List[str]]:
    """
    Create entity_relationships table with ID column and audit columns.
    
    Args:
        schema_name: Schema name
        
    Returns:
        Tuple containing:
            - Boolean indicating if creation was successful
            - List of messages (warnings, errors, or success messages)
    """
    messages = []
    
    try:
        query = f"""
            CREATE TABLE {schema_name}.entity_relationships (
                id SERIAL PRIMARY KEY,
                source_entity_id VARCHAR(50) REFERENCES {schema_name}.entities(entity_id) ON DELETE CASCADE,
                target_entity_id VARCHAR(50) REFERENCES {schema_name}.entities(entity_id) ON DELETE CASCADE,
                relationship_type VARCHAR(50) NOT NULL,
                source_attribute_id VARCHAR(100) REFERENCES {schema_name}.entity_attributes(attribute_id) ON DELETE CASCADE,
                target_attribute_id VARCHAR(100) REFERENCES {schema_name}.entity_attributes(attribute_id) ON DELETE CASCADE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                created_by VARCHAR(100) DEFAULT 'system',
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_by VARCHAR(100) DEFAULT 'system'
            )
        """
        
        success, query_messages, _ = execute_query(query)
        
        if not success:
            messages.extend(query_messages)
            return False, messages
        
        messages.append(f"Created table {schema_name}.entity_relationships")
        logger.info(f"Created table {schema_name}.entity_relationships")
        
        return True, messages
    except Exception as e:
        error_msg = f"Error creating entity_relationships table: {str(e)}"
        logger.error(error_msg, exc_info=True)
        messages.append(error_msg)
        return False, messages

def create_entity_business_rules_table(schema_name: str) -> Tuple[bool, List[str]]:
    """
    Create entity_business_rules table with ID column and audit columns.
    
    Args:
        schema_name: Schema name
        
    Returns:
        Tuple containing:
            - Boolean indicating if creation was successful
            - List of messages (warnings, errors, or success messages)
    """
    messages = []
    
    try:
        query = f"""
            CREATE TABLE {schema_name}.entity_business_rules (
                id SERIAL PRIMARY KEY,
                rule_id VARCHAR(100) UNIQUE NOT NULL,
                entity_id VARCHAR(50) REFERENCES {schema_name}.entities(entity_id) ON DELETE CASCADE,
                name VARCHAR(100) NOT NULL,
                description TEXT,
                condition TEXT,
                action TEXT,
                priority INTEGER DEFAULT 0,
                active BOOLEAN DEFAULT TRUE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                created_by VARCHAR(100) DEFAULT 'system',
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_by VARCHAR(100) DEFAULT 'system'
            )
        """
        
        success, query_messages, _ = execute_query(query)
        
        if not success:
            messages.extend(query_messages)
            return False, messages
        
        messages.append(f"Created table {schema_name}.entity_business_rules")
        logger.info(f"Created table {schema_name}.entity_business_rules")
        
        return True, messages
    except Exception as e:
        error_msg = f"Error creating entity_business_rules table: {str(e)}"
        logger.error(error_msg, exc_info=True)
        messages.append(error_msg)
        return False, messages

def create_attribute_enum_values_table(schema_name: str) -> Tuple[bool, List[str]]:
    """
    Create attribute_enum_values table with ID column and audit columns.
    
    Args:
        schema_name: Schema name
        
    Returns:
        Tuple containing:
            - Boolean indicating if creation was successful
            - List of messages (warnings, errors, or success messages)
    """
    messages = []
    
    try:
        query = f"""
            CREATE TABLE {schema_name}.attribute_enum_values (
                id SERIAL PRIMARY KEY,
                attribute_id VARCHAR(100) REFERENCES {schema_name}.entity_attributes(attribute_id) ON DELETE CASCADE,
                value VARCHAR(100) NOT NULL,
                display_name VARCHAR(100),
                description TEXT,
                sort_order INTEGER,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                created_by VARCHAR(100) DEFAULT 'system',
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_by VARCHAR(100) DEFAULT 'system',
                UNIQUE(attribute_id, value)
            )
        """
        
        success, query_messages, _ = execute_query(query)
        
        if not success:
            messages.extend(query_messages)
            return False, messages
        
        messages.append(f"Created table {schema_name}.attribute_enum_values")
        logger.info(f"Created table {schema_name}.attribute_enum_values")
        
        return True, messages
    except Exception as e:
        error_msg = f"Error creating attribute_enum_values table: {str(e)}"
        logger.error(error_msg, exc_info=True)
        messages.append(error_msg)
        return False, messages

def create_attribute_validations_table(schema_name: str) -> Tuple[bool, List[str]]:
    """
    Create attribute_validations table with ID column and audit columns.
    
    Args:
        schema_name: Schema name
        
    Returns:
        Tuple containing:
            - Boolean indicating if creation was successful
            - List of messages (warnings, errors, or success messages)
    """
    messages = []
    
    try:
        query = f"""
            CREATE TABLE {schema_name}.attribute_validations (
                id SERIAL PRIMARY KEY,
                attribute_id VARCHAR(100) REFERENCES {schema_name}.entity_attributes(attribute_id) ON DELETE CASCADE,
                validation_name VARCHAR(100) NOT NULL,
                validation_type VARCHAR(50) NOT NULL,
                validation_expression TEXT,
                error_message TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                created_by VARCHAR(100) DEFAULT 'system',
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_by VARCHAR(100) DEFAULT 'system',
                UNIQUE(attribute_id, validation_name)
            )
        """
        
        success, query_messages, _ = execute_query(query)
        
        if not success:
            messages.extend(query_messages)
            return False, messages
        
        messages.append(f"Created table {schema_name}.attribute_validations")
        logger.info(f"Created table {schema_name}.attribute_validations")
        
        return True, messages
    except Exception as e:
        error_msg = f"Error creating attribute_validations table: {str(e)}"
        logger.error(error_msg, exc_info=True)
        messages.append(error_msg)
        return False, messages

def create_calculated_fields_table(schema_name: str) -> Tuple[bool, List[str]]:
    """
    Create calculated_fields table with ID column and audit columns.
    
    Args:
        schema_name: Schema name
        
    Returns:
        Tuple containing:
            - Boolean indicating if creation was successful
            - List of messages (warnings, errors, or success messages)
    """
    messages = []
    
    try:
        query = f"""
            CREATE TABLE {schema_name}.calculated_fields (
                id SERIAL PRIMARY KEY,
                field_id VARCHAR(100) NOT NULL,
                attribute_id VARCHAR(100) REFERENCES {schema_name}.entity_attributes(attribute_id) ON DELETE CASCADE,
                formula TEXT NOT NULL,
                logic_layer VARCHAR(50),
                caching VARCHAR(50),
                dependencies JSONB,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                created_by VARCHAR(100) DEFAULT 'system',
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_by VARCHAR(100) DEFAULT 'system',
                UNIQUE(field_id)
            )
        """
        
        success, query_messages, _ = execute_query(query)
        
        if not success:
            messages.extend(query_messages)
            return False, messages
        
        messages.append(f"Created table {schema_name}.calculated_fields")
        logger.info(f"Created table {schema_name}.calculated_fields")
        
        return True, messages
    except Exception as e:
        error_msg = f"Error creating calculated_fields table: {str(e)}"
        logger.error(error_msg, exc_info=True)
        messages.append(error_msg)
        return False, messages

def create_entity_lifecycle_management_table(schema_name: str) -> Tuple[bool, List[str]]:
    """
    Create entity_lifecycle_management table with ID column and audit columns.
    
    Args:
        schema_name: Schema name
        
    Returns:
        Tuple containing:
            - Boolean indicating if creation was successful
            - List of messages (warnings, errors, or success messages)
    """
    messages = []
    
    try:
        query = f"""
            CREATE TABLE {schema_name}.entity_lifecycle_management (
                id SERIAL PRIMARY KEY,
                entity_id VARCHAR(50) REFERENCES {schema_name}.entities(entity_id) ON DELETE CASCADE,
                management_type VARCHAR(50) NOT NULL,
                trigger_type VARCHAR(50),
                criteria TEXT,
                retention VARCHAR(50),
                storage VARCHAR(50),
                access_pattern TEXT,
                restoration TEXT,
                tracked_attributes JSONB,
                tracking_method VARCHAR(50),
                granularity VARCHAR(50),
                access_control TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                created_by VARCHAR(100) DEFAULT 'system',
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_by VARCHAR(100) DEFAULT 'system',
                UNIQUE(entity_id, management_type)
            )
        """
        
        success, query_messages, _ = execute_query(query)
        
        if not success:
            messages.extend(query_messages)
            return False, messages
        
        messages.append(f"Created table {schema_name}.entity_lifecycle_management")
        logger.info(f"Created table {schema_name}.entity_lifecycle_management")
        
        return True, messages
    except Exception as e:
        error_msg = f"Error creating entity_lifecycle_management table: {str(e)}"
        logger.error(error_msg, exc_info=True)
        messages.append(error_msg)
        return False, messages

def main():
    """
    Main function.
    """
    schema_name = "workflow_temp"
    
    # Drop all entity tables
    logger.info(f"Dropping all entity tables in {schema_name} schema")
    success, messages = drop_all_entity_tables(schema_name)
    
    for message in messages:
        logger.info(message)
    
    if not success:
        logger.error(f"Failed to drop entity tables in {schema_name} schema")
        return
    
    # Create entities table
    logger.info(f"Creating {schema_name}.entities table")
    success, messages = create_entities_table(schema_name)
    
    for message in messages:
        logger.info(message)
    
    if not success:
        logger.error(f"Failed to create {schema_name}.entities table")
        return
    
    # Create entity_attributes table
    logger.info(f"Creating {schema_name}.entity_attributes table")
    success, messages = create_entity_attributes_table(schema_name)
    
    for message in messages:
        logger.info(message)
    
    if not success:
        logger.error(f"Failed to create {schema_name}.entity_attributes table")
        return
    
    # Create entity_attribute_metadata table
    logger.info(f"Creating {schema_name}.entity_attribute_metadata table")
    success, messages = create_entity_attribute_metadata_table(schema_name)
    
    for message in messages:
        logger.info(message)
    
    if not success:
        logger.error(f"Failed to create {schema_name}.entity_attribute_metadata table")
        return
    
    # Create entity_relationships table
    logger.info(f"Creating {schema_name}.entity_relationships table")
    success, messages = create_entity_relationships_table(schema_name)
    
    for message in messages:
        logger.info(message)
    
    if not success:
        logger.error(f"Failed to create {schema_name}.entity_relationships table")
        return
    
    # Create entity_business_rules table
    logger.info(f"Creating {schema_name}.entity_business_rules table")
    success, messages = create_entity_business_rules_table(schema_name)
    
    for message in messages:
        logger.info(message)
    
    if not success:
        logger.error(f"Failed to create {schema_name}.entity_business_rules table")
        return
    
    # Create attribute_enum_values table
    logger.info(f"Creating {schema_name}.attribute_enum_values table")
    success, messages = create_attribute_enum_values_table(schema_name)
    
    for message in messages:
        logger.info(message)
    
    if not success:
        logger.error(f"Failed to create {schema_name}.attribute_enum_values table")
        return
    
    # Create attribute_validations table
    logger.info(f"Creating {schema_name}.attribute_validations table")
    success, messages = create_attribute_validations_table(schema_name)
    
    for message in messages:
        logger.info(message)
    
    if not success:
        logger.error(f"Failed to create {schema_name}.attribute_validations table")
        return
    
    # Create calculated_fields table
    logger.info(f"Creating {schema_name}.calculated_fields table")
    success, messages = create_calculated_fields_table(schema_name)
    
    for message in messages:
        logger.info(message)
    
    if not success:
        logger.error(f"Failed to create {schema_name}.calculated_fields table")
        return
    
    # Create entity_lifecycle_management table
    logger.info(f"Creating {schema_name}.entity_lifecycle_management table")
    success, messages = create_entity_lifecycle_management_table(schema_name)
    
    for message in messages:
        logger.info(message)
    
    if not success:
        logger.error(f"Failed to create {schema_name}.entity_lifecycle_management table")
        return
    
    logger.info("Done")

if __name__ == "__main__":
    main()
