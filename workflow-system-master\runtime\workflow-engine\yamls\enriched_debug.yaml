tenant:
  id: t001
  name: LeaveManagement001
  roles:
  - id: r001
    name: Employee
    inherits_from: null
    access:
      entities:
      - entity_id: e001
        permissions:
        - Read
        - Create
      - entity_id: e003
        permissions:
        - Read
      - entity_id: e004
        permissions:
        - Read
      objectives:
      - objective_id: go001.lo001
        permissions:
        - Execute
  - id: r002
    name: Manager
    inherits_from: null
    access:
      entities:
      - entity_id: e001
        permissions:
        - Read
        - Update
      - entity_id: e003
        permissions:
        - Read
      - entity_id: e004
        permissions:
        - Read
      objectives:
      - objective_id: go001.lo002
        permissions:
        - Execute
  - id: r003
    name: HR Manager
    inherits_from: null
    access:
      entities:
      - entity_id: e001
        permissions:
        - Read
        - Update
      - entity_id: e003
        permissions:
        - Read
        - Update
      - entity_id: e004
        permissions:
        - Read
      objectives:
      - objective_id: go001.lo003
        permissions:
        - Execute
workflow_data:
  software_type: Leave Management
  industry: Human Resources
  version: '1.0'
  created_by: system
  created_on: '{{timestamp}}'
permission_types:
- id: read
  description: Can read entity data
  capabilities:
  - GET
- id: create
  description: Can create new entity records
  capabilities:
  - POST
- id: update
  description: Can update existing records
  capabilities:
  - PUT
- id: delete
  description: Can delete entity records
  capabilities:
  - DELETE
- id: execute
  description: Can execute workflows
  capabilities:
  - EXECUTE
entities:
- id: e001
  name: LeaveApplication
  type: Master
  version: '1.0'
  status: Active
  attributes_metadata:
    attribute_prefix: at
    attribute_map:
      at001: leaveID
      at002: employeeID
      at003: startDate
      at004: endDate
      at005: numDays
      at006: reason
      at007: status
      at008: remarks
      at009: approvedBy
      at010: leaveType
      at011: leaveSubType
    required_attributes:
    - at001
    - at002
    - at003
    - at004
    - at005
    - at006
    - at007
    - at010
  attributes:
  - id: at001
    name: leaveID
    display_name: Leave ID
    datatype: String
    required: true
    version: '1.0'
    status: Deployed
  - id: at002
    name: employeeID
    display_name: Employee ID
    datatype: String
    required: true
    version: '1.0'
    status: Deployed
  - id: at003
    name: startDate
    display_name: Start Date
    datatype: Date
    required: true
    version: '1.0'
    status: Deployed
  - id: at004
    name: endDate
    display_name: End Date
    datatype: Date
    required: true
    version: '1.0'
    status: Deployed
  - id: at005
    name: numDays
    display_name: Number of Days
    datatype: Integer
    required: true
    version: '1.0'
    status: Deployed
  - id: at006
    name: reason
    display_name: Leave Reason
    datatype: String
    required: true
    version: '1.0'
    status: Deployed
  - id: at007
    name: status
    display_name: Status
    datatype: Enum
    required: true
    values:
    - Pending
    - Approved
    - Rejected
    version: '1.0'
    status: Deployed
  - id: at008
    name: remarks
    display_name: Remarks
    datatype: String
    required: false
    version: '1.0'
    status: Deployed
  - id: at009
    name: approvedBy
    display_name: Approved By
    datatype: String
    required: false
    version: '1.0'
    status: Deployed
  - id: at010
    name: leaveType
    display_name: Leave Type
    datatype: Enum
    required: true
    values:
    - Annual Leave
    - Sick Leave
    - Parental Leave
    - Bereavement
    version: '1.0'
    status: Deployed
  - id: at011
    name: leaveSubType
    display_name: Leave Sub-Type
    datatype: String
    required: false
    version: '1.0'
    status: Deployed
- id: e002
  name: LeaveSubType
  type: Master
  version: '1.0'
  status: Active
  attributes_metadata:
    attribute_prefix: at
    attribute_map:
      at012: leaveType
      at013: subTypeId
      at014: subTypeName
      at015: active
    required_attributes:
    - at012
    - at013
    - at014
  attributes:
  - id: at012
    name: leaveType
    display_name: Leave Type
    datatype: String
    required: true
    version: '1.0'
    status: Deployed
  - id: at013
    name: subTypeId
    display_name: Sub Type ID
    datatype: String
    required: true
    version: '1.0'
    status: Deployed
  - id: at014
    name: subTypeName
    display_name: Sub Type Name
    datatype: String
    required: true
    version: '1.0'
    status: Deployed
  - id: at015
    name: active
    display_name: Active
    datatype: Boolean
    required: false
    version: '1.0'
    status: Deployed
- id: e003
  name: User
  type: Master
  version: '1.0'
  status: Active
  attributes_metadata:
    attribute_prefix: at
    attribute_map:
      at016: user_id
      at017: username
      at018: email
      at019: first_name
      at020: last_name
      at021: status
      at022: password_hash
      at023: disabled
      at024: organization
      at025: team
    required_attributes:
    - at016
    - at017
    - at018
    - at021
  attributes:
  - id: at016
    name: user_id
    display_name: User ID
    datatype: String
    required: true
    version: '1.0'
    status: Deployed
  - id: at017
    name: username
    display_name: Username
    datatype: String
    required: true
    version: '1.0'
    status: Deployed
  - id: at018
    name: email
    display_name: Email
    datatype: String
    required: true
    version: '1.0'
    status: Deployed
  - id: at019
    name: first_name
    display_name: First Name
    datatype: String
    required: false
    version: '1.0'
    status: Deployed
  - id: at020
    name: last_name
    display_name: Last Name
    datatype: String
    required: false
    version: '1.0'
    status: Deployed
  - id: at021
    name: status
    display_name: Status
    datatype: Enum
    required: true
    values:
    - active
    - inactive
    - suspended
    version: '1.0'
    status: Deployed
  - id: at022
    name: password_hash
    display_name: Password Hash
    datatype: String
    required: false
    version: '1.0'
    status: Deployed
  - id: at023
    name: disabled
    display_name: Disabled
    datatype: Boolean
    required: false
    version: '1.0'
    status: Deployed
  - id: at024
    name: organization
    display_name: Organization
    datatype: String
    required: false
    version: '1.0'
    status: Deployed
  - id: at025
    name: team
    display_name: Team
    datatype: String
    required: false
    version: '1.0'
    status: Deployed
- id: e004
  name: Role
  type: Master
  version: '1.0'
  status: Active
  attributes_metadata:
    attribute_prefix: at
    attribute_map:
      at026: role_id
      at027: name
      at028: description
      at029: inherits_from
      at030: tenant_id
    required_attributes:
    - at026
    - at027
  attributes:
  - id: at026
    name: role_id
    display_name: Role ID
    datatype: String
    required: true
    version: '1.0'
    status: Deployed
  - id: at027
    name: name
    display_name: Name
    datatype: String
    required: true
    version: '1.0'
    status: Deployed
  - id: at028
    name: description
    display_name: Description
    datatype: String
    required: false
    version: '1.0'
    status: Deployed
  - id: at029
    name: inherits_from
    display_name: Inherits From
    datatype: String
    required: false
    version: '1.0'
    status: Deployed
  - id: at030
    name: tenant_id
    display_name: Tenant ID
    datatype: String
    required: false
    version: '1.0'
    status: Deployed
global_objectives:
- id: go001
  name: Leave Application Workflow
  version: '1.0'
  status: Active
  input_stack:
    description: Global inputs
    inputs: []
  output_stack:
    description: Global outputs
    outputs: []
  data_mapping_stack:
    description: Data handover between GOs
    mappings: []
local_objectives:
- id: lo001
  contextual_id: go001.lo001
  name: Apply for Leave
  workflow_source: origin
  function_type: Create
  agent_stack:
    agents:
    - role: r001
      rights:
      - Execute
      users: []
  input_stack:
    description: Capture leave application details
    inputs:
    - id: in027
      slot_id: e001.at999.in027
      contextual_id: go001.lo001.in027
      source:
        type: information
        description: Leave Request Instructions
      required: false
      data_type: string
      ui_control: oj-text
      is_visible: true
      metadata:
        usage: ''
      validations: []
      nested_function:
        id: nf006
        function_name: to_uppercase
        function_type: transform
        parameters:
          text: Please specify your leave dates and reason. If selecting Sick Leave
            for more than 3 days, a medical certificate will be required.
        output_to: info_text
    - id: in001
      slot_id: e001.at001.in001
      contextual_id: go001.lo001.in001
      source:
        type: system
        description: Auto-generated Leave ID
      required: true
      data_type: string
      ui_control: oj-input-text
      metadata:
        usage: ''
      validations:
      - rule: Leave ID must be unique
        rule_type: validate_required
        validation_method: validate_required
        error_message: Leave ID is required
      nested_function:
        id: nf001
        function_name: generate_id
        function_type: utility
        parameters:
          entity: e001
          attribute: at001
          prefix: LV
        output_to: at001
    - id: in002
      slot_id: e001.at002.in002
      contextual_id: go001.lo001.in002
      source:
        type: user
        description: Employee ID
      required: true
      data_type: string
      ui_control: oj-input-text
      metadata:
        usage: ''
      validations:
      - rule: Employee ID is required
        rule_type: validate_required
        validation_method: validate_required
        error_message: Employee ID cannot be empty
    - id: in025
      slot_id: e001.at010.in025
      contextual_id: go001.lo001.in025
      source:
        type: user
        description: Leave Type
      required: true
      data_type: enum
      ui_control: oj-combobox-one
      has_dropdown_source: true
      metadata:
        usage: ''
      validations:
      - rule: Leave Type is required
        rule_type: validate_required
        validation_method: validate_required
        error_message: Leave Type is required
      dropdown_source:
        source_type: function
        function_name: fetch_enum_values
        function_params:
          entity_id: e001
          attribute_id: at010
        value_field: value
        display_field: display
    - id: in026
      slot_id: e001.at011.in026
      contextual_id: go001.lo001.in026
      source:
        type: system_dependent
        description: Leave Sub-Type
      required: false
      data_type: enum
      ui_control: oj-combobox-one
      dependencies:
      - in025
      dependency_type: dropdown
      has_dropdown_source: true
      metadata:
        usage: ''
      validations: []
      dropdown_source:
        source_type: function
        function_name: fetch_filtered_records
        function_params:
          table: workflow_runtime.leave_sub_types
          filter_column: leave_type
          filter_value: ${in025}
          value_column: sub_type_id
          display_column: sub_type_name
          additional_filters:
            active: true
        value_field: value
        display_field: display
        depends_on_fields:
        - in025
    - id: in003
      slot_id: e001.at003.in003
      contextual_id: go001.lo001.in003
      source:
        type: user
        description: Start Date
      required: true
      data_type: date
      ui_control: oj-input-date
      metadata:
        usage: ''
      validations:
      - rule: Start Date is required
        rule_type: validate_required
        validation_method: validate_required
        error_message: Start Date cannot be empty
    - id: in004
      slot_id: e001.at004.in004
      contextual_id: go001.lo001.in004
      source:
        type: user
        description: End Date
      required: true
      data_type: date
      ui_control: oj-input-date
      metadata:
        usage: ''
      validations:
      - rule: End Date is required
        rule_type: validate_required
        validation_method: validate_required
        error_message: End Date cannot be empty
    - id: in005
      slot_id: e001.at005.in005
      contextual_id: go001.lo001.in005
      source:
        type: system_dependent
        description: Number of Days (calculated)
      required: true
      data_type: integer
      ui_control: oj-input-number
      dependencies:
      - in003
      - in004
      dependency_type: calculation
      metadata:
        usage: ''
      validations:
      - rule: Number of Days must be positive
        rule_type: validate_required
        validation_method: validate_required
        error_message: Number of Days cannot be empty
      nested_function:
        id: nf002
        function_name: subtract_days
        function_type: math
        parameters:
          start_date: ${in003}
          end_date: ${in004}
        output_to: at005
    - id: in006
      slot_id: e001.at006.in006
      contextual_id: go001.lo001.in006
      source:
        type: user
        description: Leave Reason
      required: true
      data_type: string
      ui_control: oj-text-area
      metadata:
        usage: ''
      validations:
      - rule: Reason is required
        rule_type: validate_required
        validation_method: validate_required
        error_message: Leave reason cannot be empty
    - id: in007
      slot_id: e001.at007.in007
      contextual_id: go001.lo001.in007
      source:
        type: system
        description: Status
      required: true
      data_type: enum
      allowed_values:
      - Pending
      - Approved
      - Rejected
      ui_control: oj-select-single
      metadata:
        usage: ''
      validations:
      - rule: Status must be valid
        rule_type: enum_check
        validation_method: enum_check
        allowed_values:
        - Pending
        - Approved
        - Rejected
        error_message: Invalid status value
      nested_function:
        id: nf003
        function_name: to_uppercase
        function_type: transform
        parameters:
          text: Pending
        output_to: at007
  output_stack:
    description: Leave application created
    outputs:
    - id: out001
      slot_id: executionstatus.out001
      contextual_id: go001.lo001.out001
      source:
        type: system
        description: Success or failure
      data_type: string
    - id: out002
      slot_id: e001.at001.out002
      contextual_id: go001.lo001.out002
      source:
        type: system
        description: Leave ID
      data_type: string
    - id: out003
      slot_id: e001.at002.out003
      contextual_id: go001.lo001.out003
      source:
        type: system
        description: Employee ID
      data_type: string
    - id: out004
      slot_id: e001.at003.out004
      contextual_id: go001.lo001.out004
      source:
        type: system
        description: Start Date
      data_type: date
    - id: out005
      slot_id: e001.at004.out005
      contextual_id: go001.lo001.out005
      source:
        type: system
        description: End Date
      data_type: date
    - id: out006
      slot_id: e001.at005.out006
      contextual_id: go001.lo001.out006
      source:
        type: system
        description: Number of Days
      data_type: integer
    - id: out007
      slot_id: e001.at006.out007
      contextual_id: go001.lo001.out007
      source:
        type: system
        description: Leave Reason
      data_type: string
    - id: out008
      slot_id: e001.at007.out008
      contextual_id: go001.lo001.out008
      source:
        type: system
        description: Status
      data_type: enum
    - id: out029
      slot_id: e001.at010.out029
      contextual_id: go001.lo001.out029
      source:
        type: system
        description: Leave Type
      data_type: enum
    - id: out030
      slot_id: e001.at011.out030
      contextual_id: go001.lo001.out030
      source:
        type: system
        description: Leave Sub-Type
      data_type: string
  data_mapping_stack:
    description: Data handover between LOs
    mappings:
    - id: map001
      source: lo001.out002
      target: lo002.in008
      mapping_type: direct
    - id: map002
      source: lo001.out003
      target: lo002.in009
      mapping_type: direct
    - id: map003
      source: lo001.out004
      target: lo002.in010
      mapping_type: direct
    - id: map004
      source: lo001.out005
      target: lo002.in011
      mapping_type: direct
    - id: map005
      source: lo001.out006
      target: lo002.in012
      mapping_type: direct
    - id: map006
      source: lo001.out007
      target: lo002.in013
      mapping_type: direct
    - id: map013
      source: lo001.out029
      target: lo002.in028
      mapping_type: direct
    - id: map014
      source: lo001.out030
      target: lo002.in029
      mapping_type: direct
    - id: map007
      source: lo001.out002
      target: lo003.in015
      mapping_type: direct
    - id: map008
      source: lo001.out003
      target: lo003.in016
      mapping_type: direct
    - id: map009
      source: lo001.out004
      target: lo003.in017
      mapping_type: direct
    - id: map010
      source: lo001.out005
      target: lo003.in018
      mapping_type: direct
    - id: map011
      source: lo001.out006
      target: lo003.in019
      mapping_type: direct
    - id: map012
      source: lo001.out007
      target: lo003.in020
      mapping_type: direct
    - id: map015
      source: lo001.out029
      target: lo003.in030
      mapping_type: direct
    - id: map016
      source: lo001.out030
      target: lo003.in031
      mapping_type: direct
  execution_pathway:
    type: alternate
    conditions:
    - condition:
        condition_type: attribute_comparison
        entity: e001
        attribute: at005
        operator: greater_than
        value: 3
      next_lo: lo003
    - condition:
        condition_type: attribute_comparison
        entity: e001
        attribute: at005
        operator: less_than_or_equal
        value: 3
      next_lo: lo002
  go_id: go001
- id: lo002
  contextual_id: go001.lo002
  name: Manager Approval
  workflow_source: intermediate
  function_type: Update
  agent_stack:
    agents:
    - role: r002
      rights:
      - Execute
      - Update
      users: []
    - role: r003
      rights:
      - Read
      users:
      - <EMAIL>
  input_stack:
    description: Manager reviews leave application
    inputs:
    - id: in027
      slot_id: e001.at999.in027
      contextual_id: go001.lo002.in027
      source:
        type: information
        description: Short Leave Policy
      required: false
      data_type: string
      ui_control: oj-text
      is_visible: true
      metadata:
        usage: ''
      validations: []
      nested_function:
        id: nf007
        function_name: to_uppercase
        function_type: transform
        parameters:
          text: This leave request is for 3 days or less and requires your approval.
            Please review the details carefully.
        output_to: info_text
    - id: in008
      slot_id: e001.at001.in008
      contextual_id: go001.lo002.in008
      source:
        type: system
        description: Leave ID
      required: true
      data_type: string
      ui_control: oj-input-text
      metadata:
        usage: lookup
      validations:
      - rule: Leave ID must be present
        rule_type: validate_required
        validation_method: validate_required
        error_message: Leave ID is required
      - rule: Leave ID must exist
        rule_type: entity_exists
        validation_method: entity_exists
        entity: e001
        attribute: at001
        error_message: Leave ID not found in the system
    - id: in009
      slot_id: e001.at002.in009
      contextual_id: go001.lo002.in009
      source:
        type: system
        description: Employee ID
      required: true
      data_type: string
      ui_control: oj-input-text
      metadata:
        usage: lookup
      validations:
      - rule: Employee ID is required
        rule_type: validate_required
        validation_method: validate_required
        error_message: Employee ID is required
    - id: in010
      slot_id: e001.at003.in010
      contextual_id: go001.lo002.in010
      source:
        type: system
        description: Start Date
      required: true
      data_type: date
      ui_control: oj-input-date
      metadata:
        usage: lookup
      validations:
      - rule: Start Date is required
        rule_type: validate_required
        validation_method: validate_required
        error_message: Start Date is required
    - id: in011
      slot_id: e001.at004.in011
      contextual_id: go001.lo002.in011
      source:
        type: system
        description: End Date
      required: true
      data_type: date
      ui_control: oj-input-date
      metadata:
        usage: lookup
      validations:
      - rule: End Date is required
        rule_type: validate_required
        validation_method: validate_required
        error_message: End Date is required
    - id: in012
      slot_id: e001.at005.in012
      contextual_id: go001.lo002.in012
      source:
        type: system
        description: Number of Days
      required: true
      data_type: integer
      ui_control: oj-input-number
      metadata:
        usage: lookup
      validations:
      - rule: Number of Days is required
        rule_type: validate_required
        validation_method: validate_required
        error_message: Number of Days is required
    - id: in013
      slot_id: e001.at006.in013
      contextual_id: go001.lo002.in013
      source:
        type: system
        description: Leave Reason
      required: true
      data_type: string
      ui_control: oj-text-area
      metadata:
        usage: lookup
      validations:
      - rule: Reason is required
        rule_type: validate_required
        validation_method: validate_required
        error_message: Leave reason is required
    - id: in028
      slot_id: e001.at010.in028
      contextual_id: go001.lo002.in028
      source:
        type: system
        description: Leave Type
      required: true
      data_type: enum
      ui_control: oj-input-text
      metadata:
        usage: lookup
      validations:
      - rule: Leave Type is required
        rule_type: validate_required
        validation_method: validate_required
        error_message: Leave Type is required
    - id: in029
      slot_id: e001.at011.in029
      contextual_id: go001.lo002.in029
      source:
        type: system
        description: Leave Sub-Type
      required: false
      data_type: string
      ui_control: oj-input-text
      metadata:
        usage: lookup
      validations: []
    - id: in014
      slot_id: e001.at007.in014
      contextual_id: go001.lo002.in014
      source:
        type: user
        description: Status
      required: true
      data_type: enum
      allowed_values:
      - Pending
      - Approved
      - Rejected
      ui_control: oj-select-single
      metadata:
        usage: update
      validations:
      - rule: Status must be valid
        rule_type: enum_check
        validation_method: enum_check
        allowed_values:
        - Pending
        - Approved
        - Rejected
        error_message: Invalid status value
    - id: in015
      slot_id: e001.at008.in015
      contextual_id: go001.lo002.in015
      source:
        type: user
        description: Remarks
      required: false
      data_type: string
      ui_control: oj-text-area
      metadata:
        usage: update
      validations: []
    - id: in016
      slot_id: e001.at009.in016
      contextual_id: go001.lo002.in016
      source:
        type: system
        description: Approved By
      required: false
      data_type: string
      ui_control: oj-input-text
      metadata:
        usage: update
      validations: []
      nested_function:
        id: nf004
        function_name: current_timestamp
        function_type: utility
        parameters: {}
        output_to: at009
  output_stack:
    description: Leave application updated by manager
    outputs:
    - id: out009
      slot_id: executionstatus.out009
      contextual_id: go001.lo002.out009
      source:
        type: system
        description: Success or failure
      data_type: string
    - id: out010
      slot_id: e001.at001.out010
      contextual_id: go001.lo002.out010
      source:
        type: system
        description: Leave ID
      data_type: string
    - id: out011
      slot_id: e001.at002.out011
      contextual_id: go001.lo002.out011
      source:
        type: system
        description: Employee ID
      data_type: string
    - id: out012
      slot_id: e001.at003.out012
      contextual_id: go001.lo002.out012
      source:
        type: system
        description: Start Date
      data_type: date
    - id: out013
      slot_id: e001.at004.out013
      contextual_id: go001.lo002.out013
      source:
        type: system
        description: End Date
      data_type: date
    - id: out014
      slot_id: e001.at005.out014
      contextual_id: go001.lo002.out014
      source:
        type: system
        description: Number of Days
      data_type: integer
    - id: out015
      slot_id: e001.at006.out015
      contextual_id: go001.lo002.out015
      source:
        type: system
        description: Leave Reason
      data_type: string
    - id: out016
      slot_id: e001.at007.out016
      contextual_id: go001.lo002.out016
      source:
        type: system
        description: Status
      data_type: enum
    - id: out017
      slot_id: e001.at008.out017
      contextual_id: go001.lo002.out017
      source:
        type: system
        description: Remarks
      data_type: string
    - id: out018
      slot_id: e001.at009.out018
      contextual_id: go001.lo002.out018
      source:
        type: system
        description: Approved By
      data_type: string
    - id: out031
      slot_id: e001.at010.out031
      contextual_id: go001.lo002.out031
      source:
        type: system
        description: Leave Type
      data_type: enum
    - id: out032
      slot_id: e001.at011.out032
      contextual_id: go001.lo002.out032
      source:
        type: system
        description: Leave Sub-Type
      data_type: string
  data_mapping_stack:
    description: Data handover between LOs
    mappings: []
  execution_pathway:
    type: terminal
    next_lo: ''
  go_id: go001
- id: lo003
  contextual_id: go001.lo003
  name: HR Manager Approval
  workflow_source: intermediate
  function_type: Update
  agent_stack:
    agents:
    - role: r003
      rights:
      - Execute
      - Update
      users: []
    - role: ''
      rights:
      - Read
      users:
      - <EMAIL>
      - <EMAIL>
  input_stack:
    description: HR Manager reviews leave application
    inputs:
    - id: in032
      slot_id: e001.at999.in032
      contextual_id: go001.lo003.in032
      source:
        type: information
        description: Extended Leave Policy
      required: false
      data_type: string
      ui_control: oj-text
      is_visible: true
      metadata:
        usage: ''
      validations: []
      nested_function:
        id: nf008
        function_name: to_uppercase
        function_type: transform
        parameters:
          text: This leave request is for more than 3 days and requires HR approval.
            Please verify the employee's leave balance before approval.
        output_to: info_text
    - id: in015
      slot_id: e001.at001.in015
      contextual_id: go001.lo003.in015
      source:
        type: system
        description: Leave ID
      required: true
      data_type: string
      ui_control: oj-input-text
      metadata:
        usage: lookup
      validations:
      - rule: Leave ID must be present
        rule_type: validate_required
        validation_method: validate_required
        error_message: Leave ID is required
      - rule: Leave ID must exist
        rule_type: entity_exists
        validation_method: entity_exists
        entity: e001
        attribute: at001
        error_message: Leave ID not found in the system
    - id: in016
      slot_id: e001.at002.in016
      contextual_id: go001.lo003.in016
      source:
        type: system
        description: Employee ID
      required: true
      data_type: string
      ui_control: oj-input-text
      metadata:
        usage: lookup
      validations:
      - rule: Employee ID is required
        rule_type: validate_required
        validation_method: validate_required
        error_message: Employee ID is required
    - id: in017
      slot_id: e001.at003.in017
      contextual_id: go001.lo003.in017
      source:
        type: system
        description: Start Date
      required: true
      data_type: date
      ui_control: oj-input-date
      metadata:
        usage: lookup
      validations:
      - rule: Start Date is required
        rule_type: validate_required
        validation_method: validate_required
        error_message: Start Date is required
    - id: in018
      slot_id: e001.at004.in018
      contextual_id: go001.lo003.in018
      source:
        type: system
        description: End Date
      required: true
      data_type: date
      ui_control: oj-input-date
      metadata:
        usage: lookup
      validations:
      - rule: End Date is required
        rule_type: validate_required
        validation_method: validate_required
        error_message: End Date is required
    - id: in019
      slot_id: e001.at005.in019
      contextual_id: go001.lo003.in019
      source:
        type: system
        description: Number of Days
      required: true
      data_type: integer
      ui_control: oj-input-number
      metadata:
        usage: lookup
      validations:
      - rule: Number of Days is required
        rule_type: validate_required
        validation_method: validate_required
        error_message: Number of Days is required
    - id: in020
      slot_id: e001.at006.in020
      contextual_id: go001.lo003.in020
      source:
        type: system
        description: Leave Reason
      required: true
      data_type: string
      ui_control: oj-text-area
      metadata:
        usage: lookup
      validations:
      - rule: Reason is required
        rule_type: validate_required
        validation_method: validate_required
        error_message: Leave reason is required
    - id: in030
      slot_id: e001.at010.in030
      contextual_id: go001.lo003.in030
      source:
        type: system
        description: Leave Type
      required: true
      data_type: enum
      ui_control: oj-input-text
      metadata:
        usage: lookup
      validations:
      - rule: Leave Type is required
        rule_type: validate_required
        validation_method: validate_required
        error_message: Leave Type is required
    - id: in031
      slot_id: e001.at011.in031
      contextual_id: go001.lo003.in031
      source:
        type: system
        description: Leave Sub-Type
      required: false
      data_type: string
      ui_control: oj-input-text
      metadata:
        usage: lookup
      validations: []
    - id: in021
      slot_id: e001.at007.in021
      contextual_id: go001.lo003.in021
      source:
        type: user
        description: Status
      required: true
      data_type: enum
      allowed_values:
      - Pending
      - Approved
      - Rejected
      ui_control: oj-select-single
      metadata:
        usage: update
      validations:
      - rule: Status must be valid
        rule_type: enum_check
        validation_method: enum_check
        allowed_values:
        - Pending
        - Approved
        - Rejected
        error_message: Invalid status value
    - id: in022
      slot_id: e001.at008.in022
      contextual_id: go001.lo003.in022
      source:
        type: user
        description: Remarks
      required: false
      data_type: string
      ui_control: oj-text-area
      metadata:
        usage: update
      validations: []
    - id: in023
      slot_id: e001.at009.in023
      contextual_id: go001.lo003.in023
      source:
        type: system
        description: Approved By
      required: false
      data_type: string
      ui_control: oj-input-text
      metadata:
        usage: update
      validations: []
      nested_function:
        id: nf005
        function_name: current_timestamp
        function_type: utility
        parameters: {}
        output_to: at009
  output_stack:
    description: Leave application updated by HR manager
    outputs:
    - id: out019
      slot_id: executionstatus.out019
      contextual_id: go001.lo003.out019
      source:
        type: system
        description: Success or failure
      data_type: string
    - id: out020
      slot_id: e001.at001.out020
      contextual_id: go001.lo003.out020
      source:
        type: system
        description: Leave ID
      data_type: string
    - id: out021
      slot_id: e001.at002.out021
      contextual_id: go001.lo003.out021
      source:
        type: system
        description: Employee ID
      data_type: string
    - id: out022
      slot_id: e001.at003.out022
      contextual_id: go001.lo003.out022
      source:
        type: system
        description: Start Date
      data_type: date
    - id: out023
      slot_id: e001.at004.out023
      contextual_id: go001.lo003.out023
      source:
        type: system
        description: End Date
      data_type: date
    - id: out024
      slot_id: e001.at005.out024
      contextual_id: go001.lo003.out024
      source:
        type: system
        description: Number of Days
      data_type: integer
    - id: out025
      slot_id: e001.at006.out025
      contextual_id: go001.lo003.out025
      source:
        type: system
        description: Leave Reason
      data_type: string
    - id: out026
      slot_id: e001.at007.out026
      contextual_id: go001.lo003.out026
      source:
        type: system
        description: Status
      data_type: enum
    - id: out027
      slot_id: e001.at008.out027
      contextual_id: go001.lo003.out027
      source:
        type: system
        description: Remarks
      data_type: string
    - id: out028
      slot_id: e001.at009.out028
      contextual_id: go001.lo003.out028
      source:
        type: system
        description: Approved By
      data_type: string
    - id: out033
      slot_id: e001.at010.out033
      contextual_id: go001.lo003.out033
      source:
        type: system
        description: Leave Type
      data_type: enum
    - id: out034
      slot_id: e001.at011.out034
      contextual_id: go001.lo003.out034
      source:
        type: system
        description: Leave Sub-Type
      data_type: string
  data_mapping_stack:
    description: Data handover between LOs
    mappings: []
  execution_pathway:
    type: terminal
    next_lo: ''
  go_id: go001
