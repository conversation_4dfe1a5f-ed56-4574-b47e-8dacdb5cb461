{"timestamp": "2025-06-23T11:36:13.272363", "operation": "deploy_single_system_permission_to_workflow_temp", "input_data": {"permission_id": "PERM_ENTITY_EMPLOYEE"}, "result": {"success": false, "error": "System permission PERM_ENTITY_EMPLOYEE not found with status draft", "permission_id": "PERM_ENTITY_EMPLOYEE", "required_status": "draft"}, "status": "error"}
{"timestamp": "2025-06-23T11:36:50.494980", "operation": "deploy_single_system_permission_to_workflow_temp", "input_data": {"permission_id": "PERM_ATTR_PERSONAL_LEAVE"}, "result": {"success": false, "error": "System permission PERM_ATTR_PERSONAL_LEAVE not found with status draft", "permission_id": "PERM_ATTR_PERSONAL_LEAVE", "required_status": "draft"}, "status": "error"}
{"timestamp": "2025-06-23T14:03:49.843815", "operation": "deploy_single_system_permission_to_workflow_temp", "input_data": {"permission_id": "PERM001"}, "result": {"success": false, "error": "System permission PERM001 not found with status draft", "permission_id": "PERM001", "required_status": "draft"}, "status": "error"}
{"timestamp": "2025-06-23T14:03:50.013512", "operation": "insert_system_permission_to_workflow_temp", "input_data": {"_id": "6858ff1e909a273e7e5d8167", "permission_id": "PERM1", "permission_name": "Employee totalLeaveEntitlement Attribute", "permission_type": "attribute", "resource_identifier": "Employee.totalLeaveEntitlement", "actions": ["read", "update"], "scope": "department_records", "entity_id": "E13", "attribute_id": "E13.At11", "go_id": "", "lo_id": "", "tenant_id": "T1001", "description": "Access to Employee totalLeaveEntitlement attribute", "natural_language": "Permission to access total leave entitlement attribute", "version": 1, "status": "draft", "created_at": "2025-06-23T07:15:42.617971", "updated_at": "2025-06-23T07:15:42.617979", "created_by": "Tarun", "updated_by": "Tarun", "permission_status": "new", "changes_detected": []}, "result": {"success": false, "error": "Error inserting system permission to workflow_temp: column \"actions\" is of type jsonb but expression is of type text[]\nLINE 6: ...', 'attribute', 'Employee.totalLeaveEntitlement', ARRAY['rea...\n                                                             ^\nHINT:  You will need to rewrite or cast the expression.\n", "schema": "workflow_temp", "permission_id": "PERM1", "permission_name": "Employee totalLeaveEntitlement Attribute"}, "status": "error"}
{"timestamp": "2025-06-23T14:03:50.013874", "operation": "process_mongo_system_permissions_to_workflow_temp", "input_data": {"schema": "workflow_temp"}, "result": {"total_processed": 1, "successful_inserts": 0, "failed_inserts": 1, "details": [{"permission_id": "PERM1", "permission_name": "Employee totalLeaveEntitlement Attribute", "status": "failed", "details": {"success": false, "error": "Error inserting system permission to workflow_temp: column \"actions\" is of type jsonb but expression is of type text[]\nLINE 6: ...', 'attribute', 'Employee.totalLeaveEntitlement', ARRAY['rea...\n                                                             ^\nHINT:  You will need to rewrite or cast the expression.\n", "schema": "workflow_temp", "permission_id": "PERM1", "permission_name": "Employee totalLeaveEntitlement Attribute"}}]}, "status": "success"}
{"timestamp": "2025-06-23T14:03:51.757668", "operation": "insert_system_permission_to_workflow_temp", "input_data": {"_id": "6858ff1e909a273e7e5d8167", "permission_id": "PERM1", "permission_name": "Employee totalLeaveEntitlement Attribute", "permission_type": "attribute", "resource_identifier": "Employee.totalLeaveEntitlement", "actions": ["read", "update"], "scope": "department_records", "entity_id": "E13", "attribute_id": "E13.At11", "go_id": "", "lo_id": "", "tenant_id": "T1001", "description": "Access to Employee totalLeaveEntitlement attribute", "natural_language": "Permission to access total leave entitlement attribute", "version": 1, "status": "draft", "created_at": "2025-06-23T07:15:42.617971", "updated_at": "2025-06-23T07:15:42.617979", "created_by": "Tarun", "updated_by": "Tarun", "permission_status": "new", "changes_detected": []}, "result": {"success": false, "error": "Error inserting system permission to workflow_temp: column \"actions\" is of type jsonb but expression is of type text[]\nLINE 6: ...', 'attribute', 'Employee.totalLeaveEntitlement', ARRAY['rea...\n                                                             ^\nHINT:  You will need to rewrite or cast the expression.\n", "schema": "workflow_temp", "permission_id": "PERM1", "permission_name": "Employee totalLeaveEntitlement Attribute"}, "status": "error"}
{"timestamp": "2025-06-23T14:03:51.757853", "operation": "process_mongo_system_permissions_to_workflow_temp", "input_data": {"schema": "workflow_temp"}, "result": {"total_processed": 1, "successful_inserts": 0, "failed_inserts": 1, "details": [{"permission_id": "PERM1", "permission_name": "Employee totalLeaveEntitlement Attribute", "status": "failed", "details": {"success": false, "error": "Error inserting system permission to workflow_temp: column \"actions\" is of type jsonb but expression is of type text[]\nLINE 6: ...', 'attribute', 'Employee.totalLeaveEntitlement', ARRAY['rea...\n                                                             ^\nHINT:  You will need to rewrite or cast the expression.\n", "schema": "workflow_temp", "permission_id": "PERM1", "permission_name": "Employee totalLeaveEntitlement Attribute"}}]}, "status": "success"}
{"timestamp": "2025-06-23T14:09:00.924373", "operation": "deploy_single_system_permission_to_workflow_temp", "input_data": {"permission_id": "PERM001"}, "result": {"success": false, "error": "System permission PERM001 not found with status draft", "permission_id": "PERM001", "required_status": "draft"}, "status": "error"}
{"timestamp": "2025-06-23T14:09:01.069777", "operation": "insert_system_permission_to_workflow_temp", "input_data": {"_id": "6858ff1e909a273e7e5d8167", "permission_id": "PERM1", "permission_name": "Employee totalLeaveEntitlement Attribute", "permission_type": "attribute", "resource_identifier": "Employee.totalLeaveEntitlement", "actions": ["read", "update"], "scope": "department_records", "entity_id": "E13", "attribute_id": "E13.At11", "go_id": "", "lo_id": "", "tenant_id": "T1001", "description": "Access to Employee totalLeaveEntitlement attribute", "natural_language": "Permission to access total leave entitlement attribute", "version": 1, "status": "draft", "created_at": "2025-06-23T07:15:42.617971", "updated_at": "2025-06-23T07:15:42.617979", "created_by": "Tarun", "updated_by": "Tarun", "permission_status": "new", "changes_detected": []}, "result": {"success": false, "error": "Error inserting system permission to workflow_temp: column \"actions\" is of type jsonb but expression is of type text[]\nLINE 6: ...', 'attribute', 'Employee.totalLeaveEntitlement', ARRAY['rea...\n                                                             ^\nHINT:  You will need to rewrite or cast the expression.\n", "schema": "workflow_temp", "permission_id": "PERM1", "permission_name": "Employee totalLeaveEntitlement Attribute"}, "status": "error"}
{"timestamp": "2025-06-23T14:09:01.070136", "operation": "process_mongo_system_permissions_to_workflow_temp", "input_data": {"schema": "workflow_temp"}, "result": {"total_processed": 1, "successful_inserts": 0, "failed_inserts": 1, "details": [{"permission_id": "PERM1", "permission_name": "Employee totalLeaveEntitlement Attribute", "status": "failed", "details": {"success": false, "error": "Error inserting system permission to workflow_temp: column \"actions\" is of type jsonb but expression is of type text[]\nLINE 6: ...', 'attribute', 'Employee.totalLeaveEntitlement', ARRAY['rea...\n                                                             ^\nHINT:  You will need to rewrite or cast the expression.\n", "schema": "workflow_temp", "permission_id": "PERM1", "permission_name": "Employee totalLeaveEntitlement Attribute"}}]}, "status": "success"}
{"timestamp": "2025-06-23T14:09:02.878331", "operation": "insert_system_permission_to_workflow_temp", "input_data": {"_id": "6858ff1e909a273e7e5d8167", "permission_id": "PERM1", "permission_name": "Employee totalLeaveEntitlement Attribute", "permission_type": "attribute", "resource_identifier": "Employee.totalLeaveEntitlement", "actions": ["read", "update"], "scope": "department_records", "entity_id": "E13", "attribute_id": "E13.At11", "go_id": "", "lo_id": "", "tenant_id": "T1001", "description": "Access to Employee totalLeaveEntitlement attribute", "natural_language": "Permission to access total leave entitlement attribute", "version": 1, "status": "draft", "created_at": "2025-06-23T07:15:42.617971", "updated_at": "2025-06-23T07:15:42.617979", "created_by": "Tarun", "updated_by": "Tarun", "permission_status": "new", "changes_detected": []}, "result": {"success": false, "error": "Error inserting system permission to workflow_temp: column \"actions\" is of type jsonb but expression is of type text[]\nLINE 6: ...', 'attribute', 'Employee.totalLeaveEntitlement', ARRAY['rea...\n                                                             ^\nHINT:  You will need to rewrite or cast the expression.\n", "schema": "workflow_temp", "permission_id": "PERM1", "permission_name": "Employee totalLeaveEntitlement Attribute"}, "status": "error"}
{"timestamp": "2025-06-23T14:09:02.878551", "operation": "process_mongo_system_permissions_to_workflow_temp", "input_data": {"schema": "workflow_temp"}, "result": {"total_processed": 1, "successful_inserts": 0, "failed_inserts": 1, "details": [{"permission_id": "PERM1", "permission_name": "Employee totalLeaveEntitlement Attribute", "status": "failed", "details": {"success": false, "error": "Error inserting system permission to workflow_temp: column \"actions\" is of type jsonb but expression is of type text[]\nLINE 6: ...', 'attribute', 'Employee.totalLeaveEntitlement', ARRAY['rea...\n                                                             ^\nHINT:  You will need to rewrite or cast the expression.\n", "schema": "workflow_temp", "permission_id": "PERM1", "permission_name": "Employee totalLeaveEntitlement Attribute"}}]}, "status": "success"}
{"timestamp": "2025-06-23T14:32:33.459070", "operation": "deploy_single_system_permission_to_workflow_temp", "input_data": {"permission_id": "PERM001"}, "result": {"success": false, "error": "System permission PERM001 not found with status draft", "permission_id": "PERM001", "required_status": "draft"}, "status": "error"}
{"timestamp": "2025-06-23T14:32:33.570095", "operation": "insert_system_permission_to_workflow_temp", "input_data": {"_id": "6858ff1e909a273e7e5d8167", "permission_id": "PERM1", "permission_name": "Employee totalLeaveEntitlement Attribute", "permission_type": "attribute", "resource_identifier": "Employee.totalLeaveEntitlement", "actions": ["read", "update"], "scope": "department_records", "entity_id": "E13", "attribute_id": "E13.At11", "go_id": "", "lo_id": "", "tenant_id": "T1001", "description": "Access to Employee totalLeaveEntitlement attribute", "natural_language": "Permission to access total leave entitlement attribute", "version": 1, "status": "draft", "created_at": "2025-06-23T07:15:42.617971", "updated_at": "2025-06-23T07:15:42.617979", "created_by": "Tarun", "updated_by": "Tarun", "permission_status": "new", "changes_detected": []}, "result": {"success": false, "error": "Error inserting system permission to workflow_temp: column \"actions\" is of type jsonb but expression is of type text[]\nLINE 6: ...', 'attribute', 'Employee.totalLeaveEntitlement', ARRAY['rea...\n                                                             ^\nHINT:  You will need to rewrite or cast the expression.\n", "schema": "workflow_temp", "permission_id": "PERM1", "permission_name": "Employee totalLeaveEntitlement Attribute"}, "status": "error"}
{"timestamp": "2025-06-23T14:32:33.570456", "operation": "process_mongo_system_permissions_to_workflow_temp", "input_data": {"schema": "workflow_temp"}, "result": {"total_processed": 1, "successful_inserts": 0, "failed_inserts": 1, "details": [{"permission_id": "PERM1", "permission_name": "Employee totalLeaveEntitlement Attribute", "status": "failed", "details": {"success": false, "error": "Error inserting system permission to workflow_temp: column \"actions\" is of type jsonb but expression is of type text[]\nLINE 6: ...', 'attribute', 'Employee.totalLeaveEntitlement', ARRAY['rea...\n                                                             ^\nHINT:  You will need to rewrite or cast the expression.\n", "schema": "workflow_temp", "permission_id": "PERM1", "permission_name": "Employee totalLeaveEntitlement Attribute"}}]}, "status": "success"}
{"timestamp": "2025-06-23T14:32:35.112111", "operation": "insert_system_permission_to_workflow_temp", "input_data": {"_id": "6858ff1e909a273e7e5d8167", "permission_id": "PERM1", "permission_name": "Employee totalLeaveEntitlement Attribute", "permission_type": "attribute", "resource_identifier": "Employee.totalLeaveEntitlement", "actions": ["read", "update"], "scope": "department_records", "entity_id": "E13", "attribute_id": "E13.At11", "go_id": "", "lo_id": "", "tenant_id": "T1001", "description": "Access to Employee totalLeaveEntitlement attribute", "natural_language": "Permission to access total leave entitlement attribute", "version": 1, "status": "draft", "created_at": "2025-06-23T07:15:42.617971", "updated_at": "2025-06-23T07:15:42.617979", "created_by": "Tarun", "updated_by": "Tarun", "permission_status": "new", "changes_detected": []}, "result": {"success": false, "error": "Error inserting system permission to workflow_temp: column \"actions\" is of type jsonb but expression is of type text[]\nLINE 6: ...', 'attribute', 'Employee.totalLeaveEntitlement', ARRAY['rea...\n                                                             ^\nHINT:  You will need to rewrite or cast the expression.\n", "schema": "workflow_temp", "permission_id": "PERM1", "permission_name": "Employee totalLeaveEntitlement Attribute"}, "status": "error"}
{"timestamp": "2025-06-23T14:32:35.112302", "operation": "process_mongo_system_permissions_to_workflow_temp", "input_data": {"schema": "workflow_temp"}, "result": {"total_processed": 1, "successful_inserts": 0, "failed_inserts": 1, "details": [{"permission_id": "PERM1", "permission_name": "Employee totalLeaveEntitlement Attribute", "status": "failed", "details": {"success": false, "error": "Error inserting system permission to workflow_temp: column \"actions\" is of type jsonb but expression is of type text[]\nLINE 6: ...', 'attribute', 'Employee.totalLeaveEntitlement', ARRAY['rea...\n                                                             ^\nHINT:  You will need to rewrite or cast the expression.\n", "schema": "workflow_temp", "permission_id": "PERM1", "permission_name": "Employee totalLeaveEntitlement Attribute"}}]}, "status": "success"}
{"timestamp": "2025-06-23T14:38:52.858992", "operation": "deploy_single_system_permission_to_workflow_temp", "input_data": {"permission_id": "PERM001"}, "result": {"success": false, "error": "System permission PERM001 not found with status draft", "permission_id": "PERM001", "required_status": "draft"}, "status": "error"}
{"timestamp": "2025-06-23T14:38:52.958213", "operation": "insert_system_permission_to_workflow_temp", "input_data": {"_id": "6858ff1e909a273e7e5d8167", "permission_id": "PERM1", "permission_name": "Employee totalLeaveEntitlement Attribute", "permission_type": "attribute", "resource_identifier": "Employee.totalLeaveEntitlement", "actions": ["read", "update"], "scope": "department_records", "entity_id": "E13", "attribute_id": "E13.At11", "go_id": "", "lo_id": "", "tenant_id": "T1001", "description": "Access to Employee totalLeaveEntitlement attribute", "natural_language": "Permission to access total leave entitlement attribute", "version": 1, "status": "draft", "created_at": "2025-06-23T07:15:42.617971", "updated_at": "2025-06-23T07:15:42.617979", "created_by": "Tarun", "updated_by": "Tarun", "permission_status": "new", "changes_detected": []}, "result": {"success": false, "error": "Error inserting system permission to workflow_temp: column \"actions\" is of type jsonb but expression is of type text[]\nLINE 6: ...', 'attribute', 'Employee.totalLeaveEntitlement', ARRAY['rea...\n                                                             ^\nHINT:  You will need to rewrite or cast the expression.\n", "schema": "workflow_temp", "permission_id": "PERM1", "permission_name": "Employee totalLeaveEntitlement Attribute"}, "status": "error"}
{"timestamp": "2025-06-23T14:38:52.958595", "operation": "process_mongo_system_permissions_to_workflow_temp", "input_data": {"schema": "workflow_temp"}, "result": {"total_processed": 1, "successful_inserts": 0, "failed_inserts": 1, "details": [{"permission_id": "PERM1", "permission_name": "Employee totalLeaveEntitlement Attribute", "status": "failed", "details": {"success": false, "error": "Error inserting system permission to workflow_temp: column \"actions\" is of type jsonb but expression is of type text[]\nLINE 6: ...', 'attribute', 'Employee.totalLeaveEntitlement', ARRAY['rea...\n                                                             ^\nHINT:  You will need to rewrite or cast the expression.\n", "schema": "workflow_temp", "permission_id": "PERM1", "permission_name": "Employee totalLeaveEntitlement Attribute"}}]}, "status": "success"}
{"timestamp": "2025-06-23T14:38:54.487337", "operation": "insert_system_permission_to_workflow_temp", "input_data": {"_id": "6858ff1e909a273e7e5d8167", "permission_id": "PERM1", "permission_name": "Employee totalLeaveEntitlement Attribute", "permission_type": "attribute", "resource_identifier": "Employee.totalLeaveEntitlement", "actions": ["read", "update"], "scope": "department_records", "entity_id": "E13", "attribute_id": "E13.At11", "go_id": "", "lo_id": "", "tenant_id": "T1001", "description": "Access to Employee totalLeaveEntitlement attribute", "natural_language": "Permission to access total leave entitlement attribute", "version": 1, "status": "draft", "created_at": "2025-06-23T07:15:42.617971", "updated_at": "2025-06-23T07:15:42.617979", "created_by": "Tarun", "updated_by": "Tarun", "permission_status": "new", "changes_detected": []}, "result": {"success": false, "error": "Error inserting system permission to workflow_temp: column \"actions\" is of type jsonb but expression is of type text[]\nLINE 6: ...', 'attribute', 'Employee.totalLeaveEntitlement', ARRAY['rea...\n                                                             ^\nHINT:  You will need to rewrite or cast the expression.\n", "schema": "workflow_temp", "permission_id": "PERM1", "permission_name": "Employee totalLeaveEntitlement Attribute"}, "status": "error"}
{"timestamp": "2025-06-23T14:38:54.487703", "operation": "process_mongo_system_permissions_to_workflow_temp", "input_data": {"schema": "workflow_temp"}, "result": {"total_processed": 1, "successful_inserts": 0, "failed_inserts": 1, "details": [{"permission_id": "PERM1", "permission_name": "Employee totalLeaveEntitlement Attribute", "status": "failed", "details": {"success": false, "error": "Error inserting system permission to workflow_temp: column \"actions\" is of type jsonb but expression is of type text[]\nLINE 6: ...', 'attribute', 'Employee.totalLeaveEntitlement', ARRAY['rea...\n                                                             ^\nHINT:  You will need to rewrite or cast the expression.\n", "schema": "workflow_temp", "permission_id": "PERM1", "permission_name": "Employee totalLeaveEntitlement Attribute"}}]}, "status": "success"}
