#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to fix schema issues in workflow_temp schema.

This script addresses the issue where tables like lo_input_stack, lo_input_items,
lo_output_stack, etc. are not being properly populated in the workflow_temp schema.
"""

import os
import sys
import logging
import argparse
from typing import Dict, List, Tuple, Any, Optional

# Add parent directory to path to import common modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from common.db_connection import get_db_connection
from db_utils import execute_query

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger('fix_schema_issues')

def check_table_exists(schema_name: str, table_name: str) -> bool:
    """
    Check if a table exists in the specified schema.
    
    Args:
        schema_name: Schema name
        table_name: Table name
        
    Returns:
        Boolean indicating if the table exists
    """
    query = """
        SELECT EXISTS (
            SELECT FROM information_schema.tables
            WHERE table_schema = %s
            AND table_name = %s
        )
    """
    
    success, messages, result = execute_query(query, (schema_name, table_name))
    
    if not success or not result:
        logger.error(f"Failed to check if table {schema_name}.{table_name} exists: {messages}")
        return False
    
    return result[0][0]

def get_table_columns(schema_name: str, table_name: str) -> List[Tuple[str, str, str]]:
    """
    Get the columns of a table.
    
    Args:
        schema_name: Schema name
        table_name: Table name
        
    Returns:
        List of tuples containing column name, data type, and column default
    """
    query = """
        SELECT column_name, data_type, column_default
        FROM information_schema.columns
        WHERE table_schema = %s
        AND table_name = %s
        ORDER BY ordinal_position
    """
    
    success, messages, result = execute_query(query, (schema_name, table_name))
    
    if not success or not result:
        logger.error(f"Failed to get columns for {schema_name}.{table_name}: {messages}")
        return []
    
    return result

def create_lo_input_stack_table(schema_name: str) -> bool:
    """
    Create the lo_input_stack table in the specified schema.
    
    Args:
        schema_name: Schema name
        
    Returns:
        Boolean indicating if creation was successful
    """
    # Check if the table already exists
    if check_table_exists(schema_name, 'lo_input_stack'):
        logger.info(f"Table {schema_name}.lo_input_stack already exists")
        return True
    
    # Create the table
    query = f"""
        CREATE TABLE {schema_name}.lo_input_stack (
            id SERIAL PRIMARY KEY,
            lo_id VARCHAR(50) REFERENCES {schema_name}.local_objectives(lo_id),
            name VARCHAR(100) DEFAULT 'Input Stack',
            description TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    """
    
    success, messages, _ = execute_query(query)
    
    if not success:
        logger.error(f"Failed to create table {schema_name}.lo_input_stack: {messages}")
        return False
    
    logger.info(f"Created table {schema_name}.lo_input_stack")
    return True

def create_lo_input_items_table(schema_name: str) -> bool:
    """
    Create the lo_input_items table in the specified schema.
    
    Args:
        schema_name: Schema name
        
    Returns:
        Boolean indicating if creation was successful
    """
    # Check if the table already exists
    if check_table_exists(schema_name, 'lo_input_items'):
        logger.info(f"Table {schema_name}.lo_input_items already exists")
        return True
    
    # Create the table
    query = f"""
        CREATE TABLE {schema_name}.lo_input_items (
            id VARCHAR(100) PRIMARY KEY,
            item_id VARCHAR(100),
            input_stack_id INTEGER REFERENCES {schema_name}.lo_input_stack(id),
            slot_id VARCHAR(100),
            contextual_id VARCHAR(100),
            source_type VARCHAR(50) DEFAULT 'user',
            source_description TEXT,
            required BOOLEAN DEFAULT FALSE,
            lo_id VARCHAR(50) REFERENCES {schema_name}.local_objectives(lo_id),
            name VARCHAR(100) NOT NULL,
            type VARCHAR(50) DEFAULT 'string',
            default_value TEXT,
            ui_control VARCHAR(50) DEFAULT 'text',
            help_text TEXT,
            dependency_info JSONB,
            is_visible BOOLEAN DEFAULT TRUE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    """
    
    success, messages, _ = execute_query(query)
    
    if not success:
        logger.error(f"Failed to create table {schema_name}.lo_input_items: {messages}")
        return False
    
    logger.info(f"Created table {schema_name}.lo_input_items")
    return True

def create_lo_output_stack_table(schema_name: str) -> bool:
    """
    Create the lo_output_stack table in the specified schema.
    
    Args:
        schema_name: Schema name
        
    Returns:
        Boolean indicating if creation was successful
    """
    # Check if the table already exists
    if check_table_exists(schema_name, 'lo_output_stack'):
        logger.info(f"Table {schema_name}.lo_output_stack already exists")
        return True
    
    # Create the table
    query = f"""
        CREATE TABLE {schema_name}.lo_output_stack (
            id SERIAL PRIMARY KEY,
            lo_id VARCHAR(50) REFERENCES {schema_name}.local_objectives(lo_id),
            name VARCHAR(100) DEFAULT 'Output Stack',
            description TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    """
    
    success, messages, _ = execute_query(query)
    
    if not success:
        logger.error(f"Failed to create table {schema_name}.lo_output_stack: {messages}")
        return False
    
    logger.info(f"Created table {schema_name}.lo_output_stack")
    return True

def create_lo_output_items_table(schema_name: str) -> bool:
    """
    Create the lo_output_items table in the specified schema.
    
    Args:
        schema_name: Schema name
        
    Returns:
        Boolean indicating if creation was successful
    """
    # Check if the table already exists
    if check_table_exists(schema_name, 'lo_output_items'):
        logger.info(f"Table {schema_name}.lo_output_items already exists")
        return True
    
    # Create the table
    query = f"""
        CREATE TABLE {schema_name}.lo_output_items (
            id VARCHAR(100) PRIMARY KEY,
            output_stack_id INTEGER REFERENCES {schema_name}.lo_output_stack(id),
            lo_id VARCHAR(50) REFERENCES {schema_name}.local_objectives(lo_id),
            name VARCHAR(100) NOT NULL,
            type VARCHAR(50) DEFAULT 'string',
            slot_id VARCHAR(100),
            contextual_id VARCHAR(100),
            source VARCHAR(50) DEFAULT 'system',
            item_id VARCHAR(100),
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    """
    
    success, messages, _ = execute_query(query)
    
    if not success:
        logger.error(f"Failed to create table {schema_name}.lo_output_items: {messages}")
        return False
    
    logger.info(f"Created table {schema_name}.lo_output_items")
    return True

def create_lo_data_mapping_stack_table(schema_name: str) -> bool:
    """
    Create the lo_data_mapping_stack table in the specified schema.
    
    Args:
        schema_name: Schema name
        
    Returns:
        Boolean indicating if creation was successful
    """
    # Check if the table already exists
    if check_table_exists(schema_name, 'lo_data_mapping_stack'):
        logger.info(f"Table {schema_name}.lo_data_mapping_stack already exists")
        return True
    
    # Create the table
    query = f"""
        CREATE TABLE {schema_name}.lo_data_mapping_stack (
            id SERIAL PRIMARY KEY,
            lo_id VARCHAR(50) REFERENCES {schema_name}.local_objectives(lo_id),
            name VARCHAR(100) DEFAULT 'Data Mapping Stack',
            description TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    """
    
    success, messages, _ = execute_query(query)
    
    if not success:
        logger.error(f"Failed to create table {schema_name}.lo_data_mapping_stack: {messages}")
        return False
    
    logger.info(f"Created table {schema_name}.lo_data_mapping_stack")
    return True

def create_lo_data_mappings_table(schema_name: str) -> bool:
    """
    Create the lo_data_mappings table in the specified schema.
    
    Args:
        schema_name: Schema name
        
    Returns:
        Boolean indicating if creation was successful
    """
    # Check if the table already exists
    if check_table_exists(schema_name, 'lo_data_mappings'):
        logger.info(f"Table {schema_name}.lo_data_mappings already exists")
        return True
    
    # Create the table
    query = f"""
        CREATE TABLE {schema_name}.lo_data_mappings (
            id SERIAL PRIMARY KEY,
            mapping_stack_id INTEGER REFERENCES {schema_name}.lo_data_mapping_stack(id),
            source VARCHAR(100),
            target VARCHAR(100),
            mapping_type VARCHAR(50) DEFAULT 'direct',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    """
    
    success, messages, _ = execute_query(query)
    
    if not success:
        logger.error(f"Failed to create table {schema_name}.lo_data_mappings: {messages}")
        return False
    
    logger.info(f"Created table {schema_name}.lo_data_mappings")
    return True

def create_lo_nested_functions_table(schema_name: str) -> bool:
    """
    Create the lo_nested_functions table in the specified schema.
    
    Args:
        schema_name: Schema name
        
    Returns:
        Boolean indicating if creation was successful
    """
    # Check if the table already exists
    if check_table_exists(schema_name, 'lo_nested_functions'):
        logger.info(f"Table {schema_name}.lo_nested_functions already exists")
        return True
    
    # Create the table
    query = f"""
        CREATE TABLE {schema_name}.lo_nested_functions (
            id SERIAL PRIMARY KEY,
            lo_id VARCHAR(50) REFERENCES {schema_name}.local_objectives(lo_id),
            nested_function_id VARCHAR(100),
            function_name VARCHAR(100),
            function_type VARCHAR(50),
            parameters JSONB,
            input_contextual_id VARCHAR(100),
            output_to VARCHAR(100),
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    """
    
    success, messages, _ = execute_query(query)
    
    if not success:
        logger.error(f"Failed to create table {schema_name}.lo_nested_functions: {messages}")
        return False
    
    logger.info(f"Created table {schema_name}.lo_nested_functions")
    return True

def create_lo_dependencies_table(schema_name: str) -> bool:
    """
    Create the lo_dependencies table in the specified schema.
    
    Args:
        schema_name: Schema name
        
    Returns:
        Boolean indicating if creation was successful
    """
    # Check if the table already exists
    if check_table_exists(schema_name, 'lo_dependencies'):
        logger.info(f"Table {schema_name}.lo_dependencies already exists")
        return True
    
    # Create the table
    query = f"""
        CREATE TABLE {schema_name}.lo_dependencies (
            dependency_id VARCHAR(100) PRIMARY KEY,
            lo_id VARCHAR(50) REFERENCES {schema_name}.local_objectives(lo_id),
            depends_on_lo_id VARCHAR(50) REFERENCES {schema_name}.local_objectives(lo_id),
            type VARCHAR(50) NOT NULL,
            condition TEXT,
            mapping JSONB
        )
    """
    
    success, messages, _ = execute_query(query)
    
    if not success:
        logger.error(f"Failed to create table {schema_name}.lo_dependencies: {messages}")
        return False
    
    logger.info(f"Created table {schema_name}.lo_dependencies")
    return True

def create_lo_input_validations_table(schema_name: str) -> bool:
    """
    Create the lo_input_validations table in the specified schema.
    
    Args:
        schema_name: Schema name
        
    Returns:
        Boolean indicating if creation was successful
    """
    # Check if the table already exists
    if check_table_exists(schema_name, 'lo_input_validations'):
        logger.info(f"Table {schema_name}.lo_input_validations already exists")
        return True
    
    # Create the table
    query = f"""
        CREATE TABLE {schema_name}.lo_input_validations (
            id SERIAL PRIMARY KEY,
            input_item_id VARCHAR(100),
            input_stack_id INTEGER REFERENCES {schema_name}.lo_input_stack(id),
            rule VARCHAR(100),
            rule_type VARCHAR(50),
            validation_method VARCHAR(50),
            allowed_values JSONB,
            entity VARCHAR(50),
            attribute VARCHAR(50),
            error_message TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    """
    
    success, messages, _ = execute_query(query)
    
    if not success:
        logger.error(f"Failed to create table {schema_name}.lo_input_validations: {messages}")
        return False
    
    logger.info(f"Created table {schema_name}.lo_input_validations")
    return True

def drop_tables_if_exist(schema_name: str) -> bool:
    """
    Drop tables if they exist to ensure clean recreation.
    
    Args:
        schema_name: Schema name
        
    Returns:
        Boolean indicating if the drop was successful
    """
    # Tables to drop in reverse order of dependencies
    tables = [
        'lo_input_validations',
        'lo_dependencies',
        'lo_nested_functions',
        'lo_data_mappings',
        'lo_data_mapping_stack',
        'lo_output_items',
        'lo_output_stack',
        'lo_input_items',
        'lo_input_stack'
    ]
    
    success = True
    for table in tables:
        if check_table_exists(schema_name, table):
            query = f"DROP TABLE IF EXISTS {schema_name}.{table} CASCADE"
            drop_success, messages, _ = execute_query(query)
            if not drop_success:
                logger.error(f"Failed to drop table {schema_name}.{table}: {messages}")
                success = False
            else:
                logger.info(f"Dropped table {schema_name}.{table}")
    
    return success

def fix_schema_issues(schema_name: str) -> bool:
    """
    Fix schema issues in the specified schema.
    
    Args:
        schema_name: Schema name
        
    Returns:
        Boolean indicating if the fix was successful
    """
    # First drop existing tables to ensure clean recreation
    drop_tables_if_exist(schema_name)
    
    # Create tables in order of dependencies
    success = True
    success = success and create_lo_input_stack_table(schema_name)
    success = success and create_lo_input_items_table(schema_name)
    success = success and create_lo_output_stack_table(schema_name)
    success = success and create_lo_output_items_table(schema_name)
    success = success and create_lo_data_mapping_stack_table(schema_name)
    success = success and create_lo_data_mappings_table(schema_name)
    success = success and create_lo_nested_functions_table(schema_name)
    success = success and create_lo_dependencies_table(schema_name)
    success = success and create_lo_input_validations_table(schema_name)
    
    return success

def verify_schema(schema_name: str) -> bool:
    """
    Verify that the schema has all the necessary tables.
    
    Args:
        schema_name: Schema name
        
    Returns:
        Boolean indicating if the schema is valid
    """
    tables = [
        'lo_input_stack',
        'lo_input_items',
        'lo_output_stack',
        'lo_output_items',
        'lo_data_mapping_stack',
        'lo_data_mappings',
        'lo_nested_functions',
        'lo_dependencies',
        'lo_input_validations'
    ]
    
    for table in tables:
        if not check_table_exists(schema_name, table):
            logger.error(f"Table {schema_name}.{table} does not exist")
            return False
    
    logger.info(f"Schema {schema_name} has all necessary tables")
    return True

def main():
    """
    Main function.
    """
    parser = argparse.ArgumentParser(description='Fix schema issues in workflow_temp schema.')
    parser.add_argument('--schema', type=str, default='workflow_temp', help='Schema name to fix')
    parser.add_argument('--verify-only', action='store_true', help='Only verify the schema, do not fix issues')
    
    args = parser.parse_args()
    
    if args.verify_only:
        # Verify the schema
        success = verify_schema(args.schema)
        if success:
            logger.info(f"Schema {args.schema} is valid")
        else:
            logger.error(f"Schema {args.schema} is invalid")
    else:
        # Fix schema issues
        success = fix_schema_issues(args.schema)
        if success:
            logger.info(f"Successfully fixed schema issues in {args.schema}")
        else:
            logger.error(f"Failed to fix schema issues in {args.schema}")
        
        # Verify the schema
        success = verify_schema(args.schema)
        if success:
            logger.info(f"Schema {args.schema} is now valid")
        else:
            logger.error(f"Schema {args.schema} is still invalid")

if __name__ == '__main__':
    main()
