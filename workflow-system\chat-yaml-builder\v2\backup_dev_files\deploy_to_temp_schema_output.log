2025-05-11 09:14:35,919 - prescriptive_parser - INFO - Initializing PrescriptiveParser
2025-05-11 09:14:35,919 - component_deployer - INFO - ComponentDeployer initialized with use_temp_schema=True
2025-05-11 09:14:35,924 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:35,963 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:35,969 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:35,979 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:35,988 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:35,995 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:36,003 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:36,013 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:36,021 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:36,027 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:36,029 - component_deployer - INFO - Analyzing workflow_runtime schema to create necessary tables in temporary schema
2025-05-11 09:14:36,033 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:36,040 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:36,046 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:36,054 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:36,065 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:36,074 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:36,083 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:36,094 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:36,104 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:36,112 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:36,121 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:36,132 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:36,141 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:36,151 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:36,161 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:36,170 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:36,181 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:36,192 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:36,200 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:36,211 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:36,222 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:36,232 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:36,244 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:36,253 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:36,260 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:36,271 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:36,282 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:36,289 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:36,300 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:36,311 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:36,319 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:36,331 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:36,342 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:36,350 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:36,361 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:36,372 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:36,380 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:36,392 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:36,402 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:36,411 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:36,422 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:36,431 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:36,438 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:36,448 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:36,457 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:36,464 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:36,474 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:36,482 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:36,490 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:36,501 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:36,511 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:36,518 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:36,529 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:36,538 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:36,544 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:36,556 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:36,566 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:36,575 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:36,587 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:36,598 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:36,606 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:36,617 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:36,628 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:36,635 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:36,645 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:36,654 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:36,663 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:36,674 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:36,685 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:36,693 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:36,702 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:36,712 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:36,722 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:36,731 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:36,741 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:36,749 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:36,758 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:36,768 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:36,775 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:36,784 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:36,794 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:36,801 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:36,811 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:36,822 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:36,829 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:36,841 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:36,850 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:36,859 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:36,868 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:36,877 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:36,886 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:36,897 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:36,906 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:36,914 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:36,923 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:36,933 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:36,941 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:36,952 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:36,960 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:36,969 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:36,979 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:36,990 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:36,997 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:37,008 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:37,018 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:37,026 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:37,035 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:37,043 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:37,050 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:37,060 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:37,068 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:37,077 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:37,089 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:37,100 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:37,109 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:37,119 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:37,127 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:37,134 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:37,144 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:37,153 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:37,159 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:37,169 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:37,178 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:37,184 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:37,193 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:37,201 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:37,208 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:37,219 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:37,228 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:37,236 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:37,246 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:37,255 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:37,261 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:37,271 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:37,281 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:37,290 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:37,301 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:37,309 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:37,319 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:37,328 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:37,337 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:37,347 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:37,358 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:37,367 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:37,376 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:37,387 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:37,396 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:37,404 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:37,414 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:37,422 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:37,432 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:37,443 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:37,452 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:37,458 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:37,467 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:37,477 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:37,485 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:37,495 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:37,503 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:37,509 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:37,518 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:37,526 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:37,532 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:37,541 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:37,549 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:37,557 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:37,566 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:37,575 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:37,584 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:37,594 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:37,602 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:37,610 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:37,621 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:37,631 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:37,640 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:37,652 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:37,663 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:37,670 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:37,680 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:37,689 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:37,697 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:37,708 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:37,719 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:37,727 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:37,737 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:37,747 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:37,756 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:37,766 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:37,777 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:37,786 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:37,797 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:37,808 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:37,816 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:37,827 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:37,836 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:37,846 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:37,858 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:37,868 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:37,878 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:37,889 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:37,900 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:37,908 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:37,918 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:37,926 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:37,934 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:37,944 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:37,952 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:37,961 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:37,970 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:37,978 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:37,986 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:38,061 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:38,070 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:38,080 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:38,089 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:38,096 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:38,103 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:38,110 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:38,118 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:38,124 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:38,132 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:38,140 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:38,148 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:38,155 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:38,162 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:38,169 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:38,176 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:38,177 - db_utils - ERROR - Database error: constraint "entity_attributes_entity_id_fkey" for relation "entity_attributes" already exists
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.DuplicateObject: constraint "entity_attributes_entity_id_fkey" for relation "entity_attributes" already exists

2025-05-11 09:14:38,183 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:38,190 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:38,197 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:38,198 - db_utils - ERROR - Database error: column "input_stack_id" referenced in foreign key constraint does not exist
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.UndefinedColumn: column "input_stack_id" referenced in foreign key constraint does not exist

2025-05-11 09:14:38,204 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:38,205 - db_utils - ERROR - Database error: constraint "lo_input_items_lo_id_fkey" for relation "lo_input_items" already exists
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.DuplicateObject: constraint "lo_input_items_lo_id_fkey" for relation "lo_input_items" already exists

2025-05-11 09:14:38,210 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:38,217 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:38,218 - db_utils - ERROR - Database error: there is no unique constraint matching given keys for referenced table "roles"
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.InvalidForeignKey: there is no unique constraint matching given keys for referenced table "roles"

2025-05-11 09:14:38,224 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:38,231 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:38,237 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:38,238 - db_utils - ERROR - Database error: constraint "lo_output_items_lo_id_fkey" for relation "lo_output_items" already exists
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.DuplicateObject: constraint "lo_output_items_lo_id_fkey" for relation "lo_output_items" already exists

2025-05-11 09:14:38,244 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:38,244 - db_utils - ERROR - Database error: column "output_stack_id" referenced in foreign key constraint does not exist
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.UndefinedColumn: column "output_stack_id" referenced in foreign key constraint does not exist

2025-05-11 09:14:38,250 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:38,257 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:38,258 - db_utils - ERROR - Database error: column "id" referenced in foreign key constraint does not exist
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.UndefinedColumn: column "id" referenced in foreign key constraint does not exist

2025-05-11 09:14:38,264 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:38,264 - db_utils - ERROR - Database error: column "input_stack_id" referenced in foreign key constraint does not exist
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.UndefinedColumn: column "input_stack_id" referenced in foreign key constraint does not exist

2025-05-11 09:14:38,270 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:38,271 - db_utils - ERROR - Database error: column "id" referenced in foreign key constraint does not exist
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.UndefinedColumn: column "id" referenced in foreign key constraint does not exist

2025-05-11 09:14:38,277 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:38,278 - db_utils - ERROR - Database error: column "input_stack_id" referenced in foreign key constraint does not exist
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.UndefinedColumn: column "input_stack_id" referenced in foreign key constraint does not exist

2025-05-11 09:14:38,285 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:38,286 - db_utils - ERROR - Database error: column "id" referenced in foreign key constraint does not exist
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.UndefinedColumn: column "id" referenced in foreign key constraint does not exist

2025-05-11 09:14:38,292 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:38,293 - db_utils - ERROR - Database error: column "input_stack_id" referenced in foreign key constraint does not exist
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.UndefinedColumn: column "input_stack_id" referenced in foreign key constraint does not exist

2025-05-11 09:14:38,298 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:38,299 - db_utils - ERROR - Database error: column "id" referenced in foreign key constraint does not exist
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.UndefinedColumn: column "id" referenced in foreign key constraint does not exist

2025-05-11 09:14:38,304 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:38,305 - db_utils - ERROR - Database error: column "input_stack_id" referenced in foreign key constraint does not exist
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.UndefinedColumn: column "input_stack_id" referenced in foreign key constraint does not exist

2025-05-11 09:14:38,310 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:38,317 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:38,324 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:38,331 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:38,338 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:38,344 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:38,351 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:38,358 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:38,365 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:38,372 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:38,373 - db_utils - ERROR - Database error: column "inherits_from" referenced in foreign key constraint does not exist
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.UndefinedColumn: column "inherits_from" referenced in foreign key constraint does not exist

2025-05-11 09:14:38,379 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:38,379 - db_utils - ERROR - Database error: column "tenant_id" referenced in foreign key constraint does not exist
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.UndefinedColumn: column "tenant_id" referenced in foreign key constraint does not exist

2025-05-11 09:14:38,384 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:38,390 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:38,398 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:38,405 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:38,413 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:38,414 - db_utils - ERROR - Database error: there is no unique constraint matching given keys for referenced table "output_items"
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.InvalidForeignKey: there is no unique constraint matching given keys for referenced table "output_items"

2025-05-11 09:14:38,420 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:38,421 - db_utils - ERROR - Database error: there is no unique constraint matching given keys for referenced table "output_items"
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.InvalidForeignKey: there is no unique constraint matching given keys for referenced table "output_items"

2025-05-11 09:14:38,426 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:38,427 - db_utils - ERROR - Database error: there is no unique constraint matching given keys for referenced table "output_items"
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.InvalidForeignKey: there is no unique constraint matching given keys for referenced table "output_items"

2025-05-11 09:14:38,433 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:38,434 - db_utils - ERROR - Database error: there is no unique constraint matching given keys for referenced table "output_items"
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.InvalidForeignKey: there is no unique constraint matching given keys for referenced table "output_items"

2025-05-11 09:14:38,440 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:38,446 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:38,453 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:38,461 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:38,469 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:38,476 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:38,477 - db_utils - ERROR - Database error: column "id" referenced in foreign key constraint does not exist
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.UndefinedColumn: column "id" referenced in foreign key constraint does not exist

2025-05-11 09:14:38,482 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:38,483 - db_utils - ERROR - Database error: column "input_stack_id" referenced in foreign key constraint does not exist
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.UndefinedColumn: column "input_stack_id" referenced in foreign key constraint does not exist

2025-05-11 09:14:38,488 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:38,489 - db_utils - ERROR - Database error: column "id" referenced in foreign key constraint does not exist
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.UndefinedColumn: column "id" referenced in foreign key constraint does not exist

2025-05-11 09:14:38,494 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:38,494 - db_utils - ERROR - Database error: column "input_stack_id" referenced in foreign key constraint does not exist
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.UndefinedColumn: column "input_stack_id" referenced in foreign key constraint does not exist

2025-05-11 09:14:38,499 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:38,507 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:38,507 - db_utils - ERROR - Database error: constraint "local_objectives_go_id_fkey" for relation "local_objectives" already exists
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.DuplicateObject: constraint "local_objectives_go_id_fkey" for relation "local_objectives" already exists

2025-05-11 09:14:38,513 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:38,514 - db_utils - ERROR - Database error: column "tenant_id" referenced in foreign key constraint does not exist
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.UndefinedColumn: column "tenant_id" referenced in foreign key constraint does not exist

2025-05-11 09:14:38,519 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:38,527 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:38,533 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:38,540 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:38,547 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:38,555 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:38,561 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:38,568 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:38,575 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:38,581 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:38,588 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:38,595 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:38,602 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:38,603 - db_utils - ERROR - Database error: column "id" referenced in foreign key constraint does not exist
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.UndefinedColumn: column "id" referenced in foreign key constraint does not exist

2025-05-11 09:14:38,609 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:38,610 - db_utils - ERROR - Database error: column "output_stack_id" referenced in foreign key constraint does not exist
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.UndefinedColumn: column "output_stack_id" referenced in foreign key constraint does not exist

2025-05-11 09:14:38,615 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:38,615 - db_utils - ERROR - Database error: column "id" referenced in foreign key constraint does not exist
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.UndefinedColumn: column "id" referenced in foreign key constraint does not exist

2025-05-11 09:14:38,619 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:38,620 - db_utils - ERROR - Database error: column "output_stack_id" referenced in foreign key constraint does not exist
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.UndefinedColumn: column "output_stack_id" referenced in foreign key constraint does not exist

2025-05-11 09:14:38,624 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:38,624 - db_utils - ERROR - Database error: column "id" referenced in foreign key constraint does not exist
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.UndefinedColumn: column "id" referenced in foreign key constraint does not exist

2025-05-11 09:14:38,630 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:38,631 - db_utils - ERROR - Database error: column "input_stack_id" referenced in foreign key constraint does not exist
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.UndefinedColumn: column "input_stack_id" referenced in foreign key constraint does not exist

2025-05-11 09:14:38,635 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:38,636 - db_utils - ERROR - Database error: column "id" referenced in foreign key constraint does not exist
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.UndefinedColumn: column "id" referenced in foreign key constraint does not exist

2025-05-11 09:14:38,639 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:38,640 - db_utils - ERROR - Database error: column "input_stack_id" referenced in foreign key constraint does not exist
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.UndefinedColumn: column "input_stack_id" referenced in foreign key constraint does not exist

2025-05-11 09:14:38,644 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:38,651 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:38,659 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:38,665 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:38,671 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:38,672 - db_utils - ERROR - Database error: column "id" referenced in foreign key constraint does not exist
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.UndefinedColumn: column "id" referenced in foreign key constraint does not exist

2025-05-11 09:14:38,677 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:38,678 - db_utils - ERROR - Database error: column "input_stack_id" referenced in foreign key constraint does not exist
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.UndefinedColumn: column "input_stack_id" referenced in foreign key constraint does not exist

2025-05-11 09:14:38,681 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:38,682 - db_utils - ERROR - Database error: column "id" referenced in foreign key constraint does not exist
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.UndefinedColumn: column "id" referenced in foreign key constraint does not exist

2025-05-11 09:14:38,687 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:38,688 - db_utils - ERROR - Database error: column "input_stack_id" referenced in foreign key constraint does not exist
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.UndefinedColumn: column "input_stack_id" referenced in foreign key constraint does not exist

2025-05-11 09:14:38,692 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:38,694 - component_deployer - INFO - Successfully analyzed workflow_runtime schema and created necessary tables
2025-05-11 09:14:38,694 - deploy_to_temp_schema - INFO - Deploying sample components to schema workflow_temp
2025-05-11 09:14:38,694 - prescriptive_parser - INFO - Parsing entities prescriptive text
2025-05-11 09:14:38,694 - entity_parser - INFO - Starting to parse entity definitions
2025-05-11 09:14:38,694 - entity_parser - INFO - Parsing entity: Employee
2025-05-11 09:14:38,695 - entity_parser - INFO - Successfully parsed entity 'Employee'
2025-05-11 09:14:38,695 - entity_parser - INFO - Parsing entity: Employee
2025-05-11 09:14:38,695 - entity_parser - INFO - Successfully parsed entity 'Employee'
2025-05-11 09:14:38,695 - entity_parser - INFO - Parsing entity: Department
2025-05-11 09:14:38,695 - entity_parser - INFO - Successfully parsed entity 'Department'
2025-05-11 09:14:38,695 - entity_parser - INFO - Successfully parsed 2 entities
2025-05-11 09:14:38,695 - deploy_to_temp_schema - INFO - Deploying entities
2025-05-11 09:14:38,698 - component_deployer - INFO - Deploying component of type: entities
2025-05-11 09:14:38,702 - component_deployer - INFO - Using schema: workflow_temp
2025-05-11 09:14:38,702 - entity_deployer - INFO - Deploying entities to workflow_temp
2025-05-11 09:14:38,706 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:38,712 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:38,721 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:38,729 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:38,739 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:38,740 - entity_deployer - INFO - Inserted entity 'Department' into workflow_temp.entities
2025-05-11 09:14:38,744 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:38,748 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:38,757 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:38,766 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:38,775 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:38,776 - entity_deployer - INFO - Inserted attribute 'budget' for entity 'entity_department' into workflow_temp.entity_attributes
2025-05-11 09:14:38,780 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:38,781 - entity_deployer - INFO - Inserted attribute 'departmentId' for entity 'entity_department' into workflow_temp.entity_attributes
2025-05-11 09:14:38,786 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:38,787 - entity_deployer - INFO - Inserted attribute 'location' for entity 'entity_department' into workflow_temp.entity_attributes
2025-05-11 09:14:38,791 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:38,793 - entity_deployer - INFO - Inserted attribute 'managerId' for entity 'entity_department' into workflow_temp.entity_attributes
2025-05-11 09:14:38,799 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:38,800 - entity_deployer - INFO - Inserted attribute 'name' for entity 'entity_department' into workflow_temp.entity_attributes
2025-05-11 09:14:38,805 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:38,813 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:38,819 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:38,820 - entity_deployer - WARNING - Warning: Relationship 'one-to-many_Employee' in entity 'entity_department' references non-existent entity 'entity_employee'
2025-05-11 09:14:38,826 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:38,827 - entity_deployer - WARNING - Warning: Relationship 'one-to-one_Employee' in entity 'entity_department' references non-existent entity 'entity_employee'
2025-05-11 09:14:38,833 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:38,839 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:38,844 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:38,845 - entity_deployer - INFO - Inserted business rule 'DEPT001' for entity 'entity_department' into workflow_temp.entity_business_rules
2025-05-11 09:14:38,850 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:38,855 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:38,865 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:38,876 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:38,885 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:38,886 - entity_deployer - INFO - Inserted entity 'Employee' into workflow_temp.entities
2025-05-11 09:14:38,890 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:38,897 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:38,909 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:38,918 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:38,929 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:38,931 - entity_deployer - INFO - Inserted attribute 'employeeId' for entity 'entity_employee' into workflow_temp.entity_attributes
2025-05-11 09:14:38,935 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:38,937 - entity_deployer - INFO - Inserted attribute 'firstName' for entity 'entity_employee' into workflow_temp.entity_attributes
2025-05-11 09:14:38,941 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:38,942 - entity_deployer - INFO - Inserted attribute 'fullName[derived]' for entity 'entity_employee' into workflow_temp.entity_attributes
2025-05-11 09:14:38,948 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:38,950 - entity_deployer - INFO - Inserted attribute 'lastName' for entity 'entity_employee' into workflow_temp.entity_attributes
2025-05-11 09:14:38,975 - entity_deployer - INFO - Entity deployment completed successfully
2025-05-11 09:14:38,975 - component_deployer - INFO - Component deployment succeeded
2025-05-11 09:14:38,975 - deploy_to_temp_schema - INFO - Successfully deployed entities
2025-05-11 09:14:38,975 - prescriptive_parser - INFO - Parsing go_definitions prescriptive text
2025-05-11 09:14:38,975 - go_parser - INFO - Starting to parse GO definitions
2025-05-11 09:14:38,975 - go_parser - INFO - Parsing GO: Leave Approval Process (ID: go001)
2025-05-11 09:14:38,977 - go_parser - INFO - Successfully parsed GO 'Leave Approval Process'
2025-05-11 09:14:38,977 - go_parser - INFO - Successfully parsed 1 global objectives
2025-05-11 09:14:38,977 - deploy_to_temp_schema - INFO - Deploying go_definitions
2025-05-11 09:14:38,981 - component_deployer - INFO - Deploying component of type: go_definitions
2025-05-11 09:14:38,988 - component_deployer - INFO - Using schema: workflow_temp
2025-05-11 09:14:38,993 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:38,995 - go_deployer - INFO - Deploying GO definitions to workflow_temp
2025-05-11 09:14:39,000 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:39,006 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:39,006 - db_utils - ERROR - Database error: column "status" of relation "global_objectives" does not exist
LINE 4:                     status, tenant_id, deleted_mark
                            ^
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 54, in execute_query
    cursor.execute(query, params)
psycopg2.errors.UndefinedColumn: column "status" of relation "global_objectives" does not exist
LINE 4:                     status, tenant_id, deleted_mark
                            ^

2025-05-11 09:14:39,006 - component_deployer - INFO - Component deployment failed
2025-05-11 09:14:39,007 - deploy_to_temp_schema - ERROR - Failed to deploy go_definitions: ['Database error: column "status" of relation "global_objectives" does not exist\nLINE 4:                     status, tenant_id, deleted_mark\n                            ^\n']
2025-05-11 09:14:39,007 - deploy_to_temp_schema - ERROR - Failed to deploy GO definitions: ['Database error: column "status" of relation "global_objectives" does not exist\nLINE 4:                     status, tenant_id, deleted_mark\n                            ^\n']
2025-05-11 09:14:39,007 - prescriptive_parser - INFO - Parsing lo_definitions prescriptive text
2025-05-11 09:14:39,007 - lo_parser - INFO - Starting to parse LO definitions
2025-05-11 09:14:39,007 - lo_parser - INFO - Parsing LO: SubmitLeaveRequest
2025-05-11 09:14:39,007 - lo_parser - INFO - Successfully parsed LO 'SubmitLeaveRequest'
2025-05-11 09:14:39,007 - lo_parser - INFO - Parsing LO: ReviewLeaveRequest
2025-05-11 09:14:39,007 - lo_parser - INFO - Successfully parsed LO 'ReviewLeaveRequest'
2025-05-11 09:14:39,007 - lo_parser - INFO - Successfully parsed 2 local objectives
2025-05-11 09:14:39,008 - deploy_to_temp_schema - INFO - Deploying lo_definitions
2025-05-11 09:14:39,010 - component_deployer - INFO - Deploying component of type: lo_definitions
2025-05-11 09:14:39,014 - component_deployer - INFO - Using schema: workflow_temp
2025-05-11 09:14:39,018 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:39,023 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:39,024 - component_deployer - ERROR - Cannot deploy lo_definitions because no global objectives exist. Global objectives must be deployed first.
2025-05-11 09:14:39,024 - deploy_to_temp_schema - ERROR - Failed to deploy lo_definitions: ['Cannot deploy lo_definitions because no global objectives exist. Global objectives must be deployed first.']
2025-05-11 09:14:39,024 - deploy_to_temp_schema - ERROR - Failed to deploy LO definitions: ['Cannot deploy lo_definitions because no global objectives exist. Global objectives must be deployed first.']
2025-05-11 09:14:39,024 - prescriptive_parser - INFO - Parsing roles prescriptive text
2025-05-11 09:14:39,024 - role_parser - INFO - Starting to parse role definitions
2025-05-11 09:14:39,025 - role_parser - INFO - Parsing role: Employee (ID: emp001)
2025-05-11 09:14:39,025 - role_parser - INFO - Successfully parsed role 'Employee'
2025-05-11 09:14:39,025 - role_parser - INFO - Parsing role: Manager (ID: mgr001)
2025-05-11 09:14:39,025 - role_parser - INFO - Successfully parsed role 'Manager'
2025-05-11 09:14:39,025 - role_parser - INFO - Parsing role: HRManager (ID: hr001)
2025-05-11 09:14:39,025 - role_parser - INFO - Successfully parsed role 'HRManager'
2025-05-11 09:14:39,025 - role_parser - INFO - Parsing role: FinanceManager (ID: fin001)
2025-05-11 09:14:39,025 - role_parser - INFO - Successfully parsed role 'FinanceManager'
2025-05-11 09:14:39,025 - role_parser - INFO - Parsing role: SystemAdmin (ID: sys001)
2025-05-11 09:14:39,025 - role_parser - INFO - Successfully parsed role 'SystemAdmin'
2025-05-11 09:14:39,025 - role_parser - INFO - Successfully parsed 5 roles
2025-05-11 09:14:39,025 - deploy_to_temp_schema - INFO - Deploying roles
2025-05-11 09:14:39,026 - component_deployer - INFO - Deploying component of type: roles
2025-05-11 09:14:39,028 - component_deployer - INFO - Using schema: workflow_temp
2025-05-11 09:14:39,033 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:39,038 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:39,040 - component_deployer - ERROR - Cannot deploy roles because no global objectives exist. Global objectives must be deployed first.
2025-05-11 09:14:39,040 - deploy_to_temp_schema - ERROR - Failed to deploy roles: ['Cannot deploy roles because no global objectives exist. Global objectives must be deployed first.']
2025-05-11 09:14:39,040 - deploy_to_temp_schema - ERROR - Failed to deploy roles: ['Cannot deploy roles because no global objectives exist. Global objectives must be deployed first.']
2025-05-11 09:14:39,040 - deploy_to_temp_schema - INFO - Querying schema workflow_temp
2025-05-11 09:14:39,043 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:39,046 - deploy_to_temp_schema - INFO - Found 76 tables in schema workflow_temp
2025-05-11 09:14:39,050 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:39,051 - deploy_to_temp_schema - INFO - No data found in table workflow_temp.agent_rights
2025-05-11 09:14:39,055 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:39,056 - deploy_to_temp_schema - INFO - No data found in table workflow_temp.agent_stack
2025-05-11 09:14:39,061 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:39,062 - deploy_to_temp_schema - INFO - No data found in table workflow_temp.alembic_version
2025-05-11 09:14:39,067 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:39,068 - deploy_to_temp_schema - INFO - No data found in table workflow_temp.attribute_enum_values
2025-05-11 09:14:39,072 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:39,073 - deploy_to_temp_schema - INFO - No data found in table workflow_temp.attribute_ui_controls
2025-05-11 09:14:39,078 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:39,079 - deploy_to_temp_schema - INFO - No data found in table workflow_temp.attribute_validations
2025-05-11 09:14:39,084 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:39,084 - deploy_to_temp_schema - INFO - No data found in table workflow_temp.conditional_success_messages
2025-05-11 09:14:39,088 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:39,089 - deploy_to_temp_schema - INFO - No data found in table workflow_temp.data_mapping_stack
2025-05-11 09:14:39,094 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:39,095 - deploy_to_temp_schema - INFO - No data found in table workflow_temp.data_mappings
2025-05-11 09:14:39,100 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:39,101 - deploy_to_temp_schema - INFO - No data found in table workflow_temp.dropdown_data_sources
2025-05-11 09:14:39,105 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:39,106 - deploy_to_temp_schema - INFO - Data in table workflow_temp.entities:
2025-05-11 09:14:39,110 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:39,121 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:39,122 - deploy_to_temp_schema - INFO - No data found in table workflow_temp.entity_attribute_metadata
2025-05-11 09:14:39,127 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:39,128 - deploy_to_temp_schema - INFO - Data in table workflow_temp.entity_attributes:
2025-05-11 09:14:39,132 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:39,142 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:39,143 - deploy_to_temp_schema - INFO - Data in table workflow_temp.entity_business_rules:
2025-05-11 09:14:39,148 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:39,157 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:39,158 - deploy_to_temp_schema - INFO - No data found in table workflow_temp.entity_permissions
2025-05-11 09:14:39,163 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:39,164 - deploy_to_temp_schema - INFO - No data found in table workflow_temp.entity_relationships
2025-05-11 09:14:39,168 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:39,169 - deploy_to_temp_schema - INFO - No data found in table workflow_temp.execution_path_tracking
2025-05-11 09:14:39,173 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:39,174 - deploy_to_temp_schema - INFO - No data found in table workflow_temp.execution_pathway_conditions
2025-05-11 09:14:39,179 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:39,179 - deploy_to_temp_schema - INFO - No data found in table workflow_temp.execution_pathways
2025-05-11 09:14:39,185 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:39,185 - deploy_to_temp_schema - INFO - No data found in table workflow_temp.execution_rules
2025-05-11 09:14:39,191 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:39,192 - deploy_to_temp_schema - INFO - No data found in table workflow_temp.global_objectives
2025-05-11 09:14:39,197 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:39,198 - deploy_to_temp_schema - INFO - No data found in table workflow_temp.go_lo_mapping
2025-05-11 09:14:39,203 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:39,204 - deploy_to_temp_schema - INFO - No data found in table workflow_temp.go_performance_metrics
2025-05-11 09:14:39,209 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:39,210 - deploy_to_temp_schema - INFO - No data found in table workflow_temp.input_data_sources
2025-05-11 09:14:39,214 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:39,215 - deploy_to_temp_schema - INFO - No data found in table workflow_temp.input_dependencies
2025-05-11 09:14:39,221 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:39,222 - deploy_to_temp_schema - INFO - No data found in table workflow_temp.input_items
2025-05-11 09:14:39,228 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:39,229 - deploy_to_temp_schema - INFO - No data found in table workflow_temp.input_stack
2025-05-11 09:14:39,234 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:39,234 - deploy_to_temp_schema - INFO - No data found in table workflow_temp.lo_data_mapping_stack
2025-05-11 09:14:39,240 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:39,241 - deploy_to_temp_schema - INFO - No data found in table workflow_temp.lo_data_mappings
2025-05-11 09:14:39,247 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:39,248 - deploy_to_temp_schema - INFO - No data found in table workflow_temp.lo_input_execution
2025-05-11 09:14:39,252 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:39,253 - deploy_to_temp_schema - INFO - No data found in table workflow_temp.lo_input_items
2025-05-11 09:14:39,258 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:39,259 - deploy_to_temp_schema - INFO - No data found in table workflow_temp.lo_input_stack
2025-05-11 09:14:39,262 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:39,263 - deploy_to_temp_schema - INFO - No data found in table workflow_temp.lo_input_validations
2025-05-11 09:14:39,269 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:39,270 - deploy_to_temp_schema - INFO - No data found in table workflow_temp.lo_nested_functions
2025-05-11 09:14:39,274 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:39,274 - deploy_to_temp_schema - INFO - No data found in table workflow_temp.lo_output_execution
2025-05-11 09:14:39,280 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:39,281 - deploy_to_temp_schema - INFO - No data found in table workflow_temp.lo_output_items
2025-05-11 09:14:39,287 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:39,288 - deploy_to_temp_schema - INFO - No data found in table workflow_temp.lo_output_stack
2025-05-11 09:14:39,293 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:39,293 - deploy_to_temp_schema - INFO - No data found in table workflow_temp.lo_output_triggers
2025-05-11 09:14:39,297 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:39,298 - deploy_to_temp_schema - INFO - No data found in table workflow_temp.lo_system_functions
2025-05-11 09:14:39,303 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:39,304 - deploy_to_temp_schema - INFO - No data found in table workflow_temp.local_objectives
2025-05-11 09:14:39,309 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:39,309 - deploy_to_temp_schema - INFO - No data found in table workflow_temp.mapping_rules
2025-05-11 09:14:39,313 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:39,313 - deploy_to_temp_schema - INFO - No data found in table workflow_temp.metrics_aggregation
2025-05-11 09:14:39,318 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:39,319 - deploy_to_temp_schema - INFO - No data found in table workflow_temp.metrics_reporting
2025-05-11 09:14:39,323 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:39,324 - deploy_to_temp_schema - INFO - No data found in table workflow_temp.objective_permissions
2025-05-11 09:14:39,327 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:39,328 - deploy_to_temp_schema - INFO - No data found in table workflow_temp.organizational_units
2025-05-11 09:14:39,333 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:39,333 - deploy_to_temp_schema - INFO - No data found in table workflow_temp.output_items
2025-05-11 09:14:39,337 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:39,338 - deploy_to_temp_schema - INFO - No data found in table workflow_temp.output_stack
2025-05-11 09:14:39,341 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:39,342 - deploy_to_temp_schema - INFO - No data found in table workflow_temp.output_triggers
2025-05-11 09:14:39,347 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:39,347 - deploy_to_temp_schema - INFO - No data found in table workflow_temp.permission_capabilities
2025-05-11 09:14:39,351 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:39,352 - deploy_to_temp_schema - INFO - No data found in table workflow_temp.permission_contexts
2025-05-11 09:14:39,356 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:39,356 - deploy_to_temp_schema - INFO - No data found in table workflow_temp.permission_types
2025-05-11 09:14:39,362 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:39,363 - deploy_to_temp_schema - INFO - No data found in table workflow_temp.role
2025-05-11 09:14:39,368 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:39,368 - deploy_to_temp_schema - INFO - No data found in table workflow_temp.role_inheritance
2025-05-11 09:14:39,372 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:39,373 - deploy_to_temp_schema - INFO - No data found in table workflow_temp.role_permissions
2025-05-11 09:14:39,378 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:39,379 - deploy_to_temp_schema - INFO - No data found in table workflow_temp.roles
2025-05-11 09:14:39,383 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:39,384 - deploy_to_temp_schema - INFO - No data found in table workflow_temp.runtime_metrics_stack
2025-05-11 09:14:39,388 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:39,389 - deploy_to_temp_schema - INFO - No data found in table workflow_temp.success_messages
2025-05-11 09:14:39,393 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:39,394 - deploy_to_temp_schema - INFO - No data found in table workflow_temp.system_functions
2025-05-11 09:14:39,397 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:39,398 - deploy_to_temp_schema - INFO - No data found in table workflow_temp.tenants
2025-05-11 09:14:39,402 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:39,403 - deploy_to_temp_schema - INFO - No data found in table workflow_temp.terminal_pathways
2025-05-11 09:14:39,407 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:39,408 - deploy_to_temp_schema - INFO - No data found in table workflow_temp.ui_elements
2025-05-11 09:14:39,411 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:39,412 - deploy_to_temp_schema - INFO - No data found in table workflow_temp.ui_stack
2025-05-11 09:14:39,415 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:39,416 - deploy_to_temp_schema - INFO - No data found in table workflow_temp.user
2025-05-11 09:14:39,419 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:39,420 - deploy_to_temp_schema - INFO - No data found in table workflow_temp.user_oauth_tokens
2025-05-11 09:14:39,423 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:39,424 - deploy_to_temp_schema - INFO - No data found in table workflow_temp.user_organizations
2025-05-11 09:14:39,427 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:39,428 - deploy_to_temp_schema - INFO - No data found in table workflow_temp.user_role
2025-05-11 09:14:39,433 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:39,434 - deploy_to_temp_schema - INFO - No data found in table workflow_temp.user_roles
2025-05-11 09:14:39,438 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:39,438 - deploy_to_temp_schema - INFO - No data found in table workflow_temp.user_sessions
2025-05-11 09:14:39,444 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:39,444 - deploy_to_temp_schema - INFO - No data found in table workflow_temp.users
2025-05-11 09:14:39,448 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:39,449 - deploy_to_temp_schema - INFO - No data found in table workflow_temp.workflow_instances
2025-05-11 09:14:39,454 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:39,455 - deploy_to_temp_schema - INFO - No data found in table workflow_temp.workflow_results
2025-05-11 09:14:39,458 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:39,459 - deploy_to_temp_schema - INFO - No data found in table workflow_temp.workflow_transaction
2025-05-11 09:14:39,463 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:39,464 - deploy_to_temp_schema - INFO - No data found in table workflow_temp.z_entity_leave_application
2025-05-11 09:14:39,468 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:39,469 - deploy_to_temp_schema - INFO - No data found in table workflow_temp.z_entity_leave_sub_type
2025-05-11 09:14:39,473 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:39,474 - deploy_to_temp_schema - INFO - No data found in table workflow_temp.z_entity_role
2025-05-11 09:14:39,479 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 09:14:39,480 - deploy_to_temp_schema - INFO - No data found in table workflow_temp.z_entity_user
2025-05-11 09:14:39,480 - deploy_to_temp_schema - INFO - Components deployed to schema workflow_temp
2025-05-11 09:14:39,480 - deploy_to_temp_schema - INFO - You can now connect to the database and query the schema workflow_temp
2025-05-11 09:14:39,480 - deploy_to_temp_schema - INFO - To create a migration script, you can use the schema_analyzer.py script to compare the schemas
entity_id	name	description	metadata	lifecycle_management	version_type
entity_department	Department		{}	{}	v2
entity_employee	Employee		{}	{}	v2

attribute_id	entity_id	name	type	required	default_value	calculated_field	calculation_formula	dependencies
entity_department_attr_budget	entity_department	budget	string	False	None	False	None	[]
entity_department_attr_departmentid	entity_department	departmentId	string	True	None	False	None	[]
entity_department_attr_location	entity_department	location	string	False	None	False	None	[]
entity_department_attr_managerid	entity_department	managerId	string	False	None	False	None	[]
entity_department_attr_name	entity_department	name	string	False	None	False	None	[]
entity_employee_attr_employeeid	entity_employee	employeeId	string	False	None	False	None	[]
entity_employee_attr_firstname	entity_employee	firstName	string	False	None	False	None	[]
entity_employee_attr_fullname[derived]	entity_employee	fullName[derived]	string	False	None	False	None	[]
entity_employee_attr_lastname	entity_employee	lastName	string	False	None	False	None	[]

rule_id	entity_id	name	description	condition	action	priority	active
entity_department_rule_dept001	entity_department	DEPT001				0	True

