{"timestamp": "2025-06-24T04:45:01.874463", "operation": "deploy_single_entity_attribute_to_workflow_temp", "input_data": {"attribute_id": "ATTR001"}, "result": {"success": false, "error": "Entity attribute ATTR001 not found with status draft", "attribute_id": "ATTR001", "required_status": "draft"}, "status": "error"}
{"timestamp": "2025-06-24T04:45:02.043490", "operation": "process_mongo_entity_attributes_to_workflow_temp", "input_data": {"schema": "workflow_temp"}, "result": {"total_processed": 4, "successful_inserts": 0, "failed_inserts": 0, "details": [{"attribute_id": "E44.At2", "entity_id": "E44", "name": "firstName", "status": "skipped", "reason": "already_exists"}, {"attribute_id": "E44.At4", "entity_id": "E44", "name": "email", "status": "skipped", "reason": "already_exists"}, {"attribute_id": "E44.At8", "entity_id": "E44", "name": "annualLeaveBalance", "status": "skipped", "reason": "already_exists"}, {"attribute_id": "E44.At10", "entity_id": "E44", "name": "personalLeaveBalance", "status": "skipped", "reason": "already_exists"}]}, "status": "success"}
{"timestamp": "2025-06-24T04:57:39.487763", "operation": "deploy_single_entity_attribute_to_workflow_temp", "input_data": {"attribute_id": "E44.At2"}, "result": {"success": false, "error": "Entity attribute E44.At2 not found with status draft", "attribute_id": "E44.At2", "required_status": "draft"}, "status": "error"}
{"timestamp": "2025-06-24T04:59:39.052075", "operation": "deploy_single_entity_attribute_to_workflow_temp", "input_data": {"attribute_id": "E44.At4"}, "result": {"success": false, "error": "Entity attribute E44.At4 not found with status draft", "attribute_id": "E44.At4", "required_status": "draft"}, "status": "error"}
{"timestamp": "2025-06-24T04:59:51.876786", "operation": "deploy_single_entity_attribute_to_workflow_temp", "input_data": {"attribute_id": "E44.At4"}, "result": {"success": false, "error": "Entity attribute E44.At4 not found with status draft", "attribute_id": "E44.At4", "required_status": "draft"}, "status": "error"}
{"timestamp": "2025-06-24T11:41:08.672001", "operation": "insert_entity_attribute_to_workflow_runtime", "input_data": {"_id": "68551232806872467fb8f737", "attribute_id": "E15.At5", "entity_id": "E15", "name": "email", "display_name": "email", "datatype": "string", "is_primary_key": false, "is_foreign_key": false, "is_required": false, "is_unique": false, "default_type": "", "default_value": "", "description": "", "helper_text": "", "is_calculated": false, "calculation_formula": "", "version": 2, "status": "deployed_to_temp", "natural_language": "", "created_at": "2025-06-20T07:48:02.727028", "updated_at": "2025-06-20T14:04:00.512079", "created_by": "Tarun", "updated_by": "Tarun", "attribute_status": "new", "changes_detected": []}, "result": {"success": true, "inserted_id": "E15.At5", "schema": "workflow_runtime", "attribute_id": "E15.At5", "entity_id": "E15", "name": "email", "original_attribute_id": "E15.At4"}, "status": "success"}
{"timestamp": "2025-06-24T11:41:08.713396", "operation": "insert_entity_attribute_to_workflow_runtime", "input_data": {"_id": "68556e0b3b82120ee5643720", "attribute_id": "E44.At14", "entity_id": "E44", "name": "employeeId", "display_name": "Employee ID", "datatype": "string", "is_primary_key": true, "is_foreign_key": false, "is_required": true, "is_unique": true, "default_type": "computation", "default_value": "EMP-{YYYY}-{sequence}", "description": "Unique identifier for the employee", "helper_text": "Auto-generated employee identifier", "is_calculated": true, "calculation_formula": "EMP-{YYYY}-{sequence}", "version": 2, "status": "deployed_to_temp", "natural_language": "Display Name: Employee ID\nName: employeeId\nData Type: string\nRequired: true\nUnique: true\nCalculated: true\nDefault Type: computation\nDefault Value: EMP-{YYYY}-{sequence}\nCalculation Formula: EMP-{YYYY}-{sequence}\nDescription: Unique identifier for the employee\nHelper Text: Auto-generated employee identifier", "created_at": "2025-06-20T14:19:55.140980", "updated_at": "2025-06-20T14:20:53.143140", "created_by": "Tarun", "updated_by": "Tarun", "attribute_status": "new", "changes_detected": []}, "result": {"success": true, "inserted_id": "E44.At14", "schema": "workflow_runtime", "attribute_id": "E44.At14", "entity_id": "E44", "name": "employeeId", "original_attribute_id": "E44.At1"}, "status": "success"}
{"timestamp": "2025-06-24T11:41:08.740132", "operation": "insert_entity_attribute_to_workflow_runtime", "input_data": {"_id": "68556e0b3b82120ee5643722", "attribute_id": "E44.At15", "entity_id": "E44", "name": "lastName", "display_name": "Last Name", "datatype": "string", "is_primary_key": false, "is_foreign_key": false, "is_required": true, "is_unique": false, "default_type": "static value", "default_value": "", "description": "Employee last name", "helper_text": "Enter employee last name", "is_calculated": false, "calculation_formula": "", "version": 2, "status": "deployed_to_temp", "natural_language": "Display Name: Last Name\nName: lastName\nData Type: string\nRequired: true\nDefault Type: static value\nDescription: Employee last name\nHelper Text: Enter employee last name", "created_at": "2025-06-20T14:19:55.171349", "updated_at": "2025-06-20T14:20:53.176389", "created_by": "Tarun", "updated_by": "Tarun", "attribute_status": "new", "changes_detected": []}, "result": {"success": true, "inserted_id": "E44.At15", "schema": "workflow_runtime", "attribute_id": "E44.At15", "entity_id": "E44", "name": "lastName", "original_attribute_id": "E44.At3"}, "status": "success"}
{"timestamp": "2025-06-24T11:41:08.764781", "operation": "insert_entity_attribute_to_workflow_runtime", "input_data": {"_id": "68556e0b3b82120ee5643724", "attribute_id": "E44.At16", "entity_id": "E44", "name": "phone", "display_name": "phone", "datatype": "string", "is_primary_key": false, "is_foreign_key": false, "is_required": false, "is_unique": false, "default_type": "", "default_value": "", "description": "", "helper_text": "", "is_calculated": false, "calculation_formula": "", "version": 2, "status": "deployed_to_temp", "natural_language": "", "created_at": "2025-06-20T14:19:55.204274", "updated_at": "2025-06-20T14:20:53.210561", "created_by": "Tarun", "updated_by": "Tarun", "attribute_status": "new", "changes_detected": []}, "result": {"success": true, "inserted_id": "E44.At16", "schema": "workflow_runtime", "attribute_id": "E44.At16", "entity_id": "E44", "name": "phone", "original_attribute_id": "E44.At5"}, "status": "success"}
{"timestamp": "2025-06-24T11:41:08.788167", "operation": "insert_entity_attribute_to_workflow_runtime", "input_data": {"_id": "68556e0b3b82120ee5643725", "attribute_id": "E44.At17", "entity_id": "E44", "name": "departmentId", "display_name": "departmentId", "datatype": "string", "is_primary_key": false, "is_foreign_key": true, "is_required": false, "is_unique": false, "default_type": "", "default_value": "", "description": "", "helper_text": "", "is_calculated": false, "calculation_formula": "", "version": 2, "status": "deployed_to_temp", "natural_language": "", "created_at": "2025-06-20T14:19:55.220066", "updated_at": "2025-06-23T14:03:49.260036", "created_by": "Tarun", "updated_by": "Tarun", "attribute_status": "new", "changes_detected": []}, "result": {"success": true, "inserted_id": "E44.At17", "schema": "workflow_runtime", "attribute_id": "E44.At17", "entity_id": "E44", "name": "departmentId", "original_attribute_id": "E44.At6"}, "status": "success"}
{"timestamp": "2025-06-24T11:41:08.807933", "operation": "insert_entity_attribute_to_workflow_runtime", "input_data": {"_id": "68556e0b3b82120ee5643726", "attribute_id": "E44.At18", "entity_id": "E44", "name": "hireDate", "display_name": "hireDate", "datatype": "string", "is_primary_key": false, "is_foreign_key": false, "is_required": false, "is_unique": false, "default_type": "", "default_value": "", "description": "", "helper_text": "", "is_calculated": false, "calculation_formula": "", "version": 2, "status": "deployed_to_temp", "natural_language": "", "created_at": "2025-06-20T14:19:55.235248", "updated_at": "2025-06-20T14:20:53.247928", "created_by": "Tarun", "updated_by": "Tarun", "attribute_status": "new", "changes_detected": []}, "result": {"success": true, "inserted_id": "E44.At18", "schema": "workflow_runtime", "attribute_id": "E44.At18", "entity_id": "E44", "name": "hireDate", "original_attribute_id": "E44.At7"}, "status": "success"}
{"timestamp": "2025-06-24T11:41:08.827577", "operation": "insert_entity_attribute_to_workflow_runtime", "input_data": {"_id": "68556e0b3b82120ee5643728", "attribute_id": "E44.At19", "entity_id": "E44", "name": "sickLeaveBalance", "display_name": "sickLeaveBalance", "datatype": "string", "is_primary_key": false, "is_foreign_key": false, "is_required": false, "is_unique": false, "default_type": "", "default_value": "", "description": "", "helper_text": "", "is_calculated": false, "calculation_formula": "", "version": 2, "status": "deployed_to_temp", "natural_language": "", "created_at": "2025-06-20T14:19:55.270902", "updated_at": "2025-06-20T14:20:53.286452", "created_by": "Tarun", "updated_by": "Tarun", "attribute_status": "new", "changes_detected": []}, "result": {"success": true, "inserted_id": "E44.At19", "schema": "workflow_runtime", "attribute_id": "E44.At19", "entity_id": "E44", "name": "sickLeaveBalance", "original_attribute_id": "E44.At9"}, "status": "success"}
{"timestamp": "2025-06-24T11:41:08.849683", "operation": "insert_entity_attribute_to_workflow_runtime", "input_data": {"_id": "68556e0b3b82120ee564372a", "attribute_id": "E44.At20", "entity_id": "E44", "name": "totalLeaveEntitlement", "display_name": "totalLeaveEntitlement", "datatype": "string", "is_primary_key": false, "is_foreign_key": false, "is_required": false, "is_unique": false, "default_type": "", "default_value": "", "description": "", "helper_text": "", "is_calculated": false, "calculation_formula": "", "version": 2, "status": "deployed_to_temp", "natural_language": "", "created_at": "2025-06-20T14:19:55.303369", "updated_at": "2025-06-20T14:20:53.319983", "created_by": "Tarun", "updated_by": "Tarun", "attribute_status": "new", "changes_detected": []}, "result": {"success": true, "inserted_id": "E44.At20", "schema": "workflow_runtime", "attribute_id": "E44.At20", "entity_id": "E44", "name": "totalLeaveEntitlement", "original_attribute_id": "E44.At11"}, "status": "success"}
{"timestamp": "2025-06-24T11:41:08.865386", "operation": "insert_entity_attribute_to_workflow_runtime", "input_data": {"_id": "6858f5de1195bbcbf7aa7693", "attribute_id": ".At1", "entity_id": "", "name": "employeeId", "display_name": "Employee ID", "datatype": "string", "is_primary_key": false, "is_foreign_key": false, "is_required": true, "is_unique": true, "default_type": "computation", "default_value": "EMP-{sequence}", "description": "Unique employee identifier", "helper_text": "Auto-generated", "is_calculated": true, "calculation_formula": "EMP-{sequence}", "version": 2, "status": "deployed_to_temp", "natural_language": "Display Name: Employee ID\nName: employeeId\nData Type: string\nRequired: true\nUnique: true\nCalculated: true\nDefault Type: computation\nDefault Value: EMP-{sequence}\nCalculation Formula: EMP-{sequence}\nDescription: Unique employee identifier\nHelper Text: Auto-generated", "created_at": "2025-06-23T06:36:14.276890", "updated_at": "2025-06-23T14:03:49.290951", "created_by": "Tarun", "updated_by": "Tarun", "attribute_status": "new", "changes_detected": []}, "result": {"success": true, "inserted_id": ".At1", "schema": "workflow_runtime", "attribute_id": ".At1", "entity_id": "", "name": "employeeId", "original_attribute_id": ".At1"}, "status": "success"}
{"timestamp": "2025-06-24T11:41:08.881809", "operation": "insert_entity_attribute_to_workflow_runtime", "input_data": {"_id": "6858f5de1195bbcbf7aa7694", "attribute_id": ".At2", "entity_id": "", "name": "firstName", "display_name": "First Name", "datatype": "string", "is_primary_key": false, "is_foreign_key": false, "is_required": true, "is_unique": false, "default_type": "static value", "default_value": "", "description": "Employee first name", "helper_text": "Enter first name", "is_calculated": false, "calculation_formula": "", "version": 2, "status": "deployed_to_temp", "natural_language": "Display Name: First Name\nName: firstName\nData Type: string\nRequired: true\nDefault Type: static value\nDescription: Employee first name\nHelper Text: Enter first name", "created_at": "2025-06-23T06:36:14.286765", "updated_at": "2025-06-23T14:03:49.306821", "created_by": "Tarun", "updated_by": "Tarun", "attribute_status": "new", "changes_detected": []}, "result": {"success": true, "inserted_id": ".At2", "schema": "workflow_runtime", "attribute_id": ".At2", "entity_id": "", "name": "firstName", "original_attribute_id": ".At2"}, "status": "success"}
{"timestamp": "2025-06-24T11:41:08.897495", "operation": "insert_entity_attribute_to_workflow_runtime", "input_data": {"_id": "6858f5de1195bbcbf7aa7695", "attribute_id": ".At3", "entity_id": "", "name": "lastName", "display_name": "Last Name", "datatype": "string", "is_primary_key": false, "is_foreign_key": false, "is_required": true, "is_unique": false, "default_type": "static value", "default_value": "", "description": "Employee last name", "helper_text": "Enter last name", "is_calculated": false, "calculation_formula": "", "version": 2, "status": "deployed_to_temp", "natural_language": "Display Name: Last Name\nName: lastName\nData Type: string\nRequired: true\nDefault Type: static value\nDescription: Employee last name\nHelper Text: Enter last name", "created_at": "2025-06-23T06:36:14.298690", "updated_at": "2025-06-23T14:03:49.322301", "created_by": "Tarun", "updated_by": "Tarun", "attribute_status": "new", "changes_detected": []}, "result": {"success": true, "inserted_id": ".At3", "schema": "workflow_runtime", "attribute_id": ".At3", "entity_id": "", "name": "lastName", "original_attribute_id": ".At3"}, "status": "success"}
{"timestamp": "2025-06-24T11:41:08.919976", "operation": "insert_entity_attribute_to_workflow_runtime", "input_data": {"_id": "6858f5de1195bbcbf7aa7696", "attribute_id": "E45.At6", "entity_id": "E45", "name": "employmentStatus", "display_name": "Employment Status", "datatype": "string", "is_primary_key": false, "is_foreign_key": false, "is_required": true, "is_unique": false, "default_type": "static value", "default_value": "Active", "description": "Current employment status", "helper_text": "Select status", "is_calculated": false, "calculation_formula": "", "version": 2, "status": "deployed_to_temp", "natural_language": "Display Name: Employment Status\nName: employmentStatus\nData Type: string\nRequired: true\nDefault Type: static value\nDefault Value: Active\nDescription: Current employment status\nHelper Text: Select status", "created_at": "2025-06-23T06:36:14.308383", "updated_at": "2025-06-23T14:03:49.346904", "created_by": "Tarun", "updated_by": "Tarun", "attribute_status": "new", "changes_detected": []}, "result": {"success": true, "inserted_id": "E45.At6", "schema": "workflow_runtime", "attribute_id": "E45.At6", "entity_id": "E45", "name": "employmentStatus", "original_attribute_id": "E45.At4"}, "status": "success"}
{"timestamp": "2025-06-24T11:41:08.938408", "operation": "insert_entity_attribute_to_workflow_runtime", "input_data": {"_id": "68556e0b3b82120ee5643721", "attribute_id": "E44.At21", "entity_id": "E44", "name": "firstName", "display_name": "First Name", "datatype": "string", "is_primary_key": false, "is_foreign_key": false, "is_required": true, "is_unique": false, "default_type": "static value", "default_value": "", "description": "Employee first name", "helper_text": "Enter employee first name", "is_calculated": false, "calculation_formula": "", "version": 1, "status": "draft", "natural_language": "Display Name: First Name\nName: firstName\nData Type: string\nRequired: true\nDefault Type: static value\nDescription: Employee first name\nHelper Text: Enter employee first name", "created_at": "2025-06-20T14:19:55.155754", "updated_at": "2025-06-20T14:19:55.155760", "created_by": "Tarun", "updated_by": "Tarun", "attribute_status": "new", "changes_detected": []}, "result": {"success": true, "inserted_id": "E44.At21", "schema": "workflow_runtime", "attribute_id": "E44.At21", "entity_id": "E44", "name": "firstName", "original_attribute_id": "E44.At2"}, "status": "success"}
{"timestamp": "2025-06-24T11:41:08.961040", "operation": "insert_entity_attribute_to_workflow_runtime", "input_data": {"_id": "68556e0b3b82120ee5643723", "attribute_id": "E44.At22", "entity_id": "E44", "name": "email", "display_name": "Email Address", "datatype": "string", "is_primary_key": false, "is_foreign_key": false, "is_required": true, "is_unique": true, "default_type": "static value", "default_value": "", "description": "Employee email address", "helper_text": "Enter valid email address", "is_calculated": false, "calculation_formula": "", "version": 1, "status": "draft", "natural_language": "Display Name: Email Address\nName: email\nData Type: string\nRequired: true\nUnique: true\nDefault Type: static value\nDescription: Employee email address\nHelper Text: Enter valid email address", "created_at": "2025-06-20T14:19:55.187810", "updated_at": "2025-06-20T14:19:55.187815", "created_by": "Tarun", "updated_by": "Tarun", "attribute_status": "new", "changes_detected": []}, "result": {"success": true, "inserted_id": "E44.At22", "schema": "workflow_runtime", "attribute_id": "E44.At22", "entity_id": "E44", "name": "email", "original_attribute_id": "E44.At4"}, "status": "success"}
{"timestamp": "2025-06-24T11:41:08.979879", "operation": "insert_entity_attribute_to_workflow_runtime", "input_data": {"_id": "68556e0b3b82120ee5643727", "attribute_id": "E44.At23", "entity_id": "E44", "name": "annualLeaveBalance", "display_name": "annualLeaveBalance", "datatype": "string", "is_primary_key": false, "is_foreign_key": false, "is_required": false, "is_unique": false, "default_type": "", "default_value": "", "description": "", "helper_text": "", "is_calculated": false, "calculation_formula": "", "version": 1, "status": "draft", "natural_language": "", "created_at": "2025-06-20T14:19:55.252699", "updated_at": "2025-06-20T14:19:55.252705", "created_by": "Tarun", "updated_by": "Tarun", "attribute_status": "new", "changes_detected": []}, "result": {"success": true, "inserted_id": "E44.At23", "schema": "workflow_runtime", "attribute_id": "E44.At23", "entity_id": "E44", "name": "annualLeaveBalance", "original_attribute_id": "E44.At8"}, "status": "success"}
{"timestamp": "2025-06-24T11:41:08.998518", "operation": "insert_entity_attribute_to_workflow_runtime", "input_data": {"_id": "68556e0b3b82120ee5643729", "attribute_id": "E44.At24", "entity_id": "E44", "name": "personalLeaveBalance", "display_name": "personalLeaveBalance", "datatype": "string", "is_primary_key": false, "is_foreign_key": false, "is_required": false, "is_unique": false, "default_type": "", "default_value": "", "description": "", "helper_text": "", "is_calculated": false, "calculation_formula": "", "version": 1, "status": "draft", "natural_language": "", "created_at": "2025-06-20T14:19:55.287901", "updated_at": "2025-06-20T14:19:55.287906", "created_by": "Tarun", "updated_by": "Tarun", "attribute_status": "new", "changes_detected": []}, "result": {"success": true, "inserted_id": "E44.At24", "schema": "workflow_runtime", "attribute_id": "E44.At24", "entity_id": "E44", "name": "personalLeaveBalance", "original_attribute_id": "E44.At10"}, "status": "success"}
{"timestamp": "2025-06-24T11:41:09.000454", "operation": "process_mongo_entity_attributes_to_workflow_runtime", "input_data": {"schema": "workflow_runtime"}, "result": {"total_processed": 22, "successful_inserts": 16, "failed_inserts": 0, "details": [{"attribute_id": "E15.At1", "entity_id": "E15", "name": "employeeId", "status": "skipped", "reason": "already_exists"}, {"attribute_id": "E15.At2", "entity_id": "E15", "name": "firstName", "status": "skipped", "reason": "already_exists"}, {"attribute_id": "E15.At3", "entity_id": "E15", "name": "lastName", "status": "skipped", "reason": "already_exists"}, {"attribute_id": "E15.At5", "entity_id": "E15", "name": "email", "status": "success", "details": {"success": true, "inserted_id": "E15.At5", "schema": "workflow_runtime", "attribute_id": "E15.At5", "entity_id": "E15", "name": "email", "original_attribute_id": "E15.At4"}}, {"attribute_id": "E13.At11", "entity_id": "E13", "name": "totalLeaveEntitlement", "status": "skipped", "reason": "already_exists"}, {"attribute_id": "E13.At10", "entity_id": "E13", "name": "personalLeaveBalance", "status": "skipped", "reason": "already_exists"}, {"attribute_id": "E13.At9", "entity_id": "E13", "name": "sickLeaveBalance", "status": "skipped", "reason": "already_exists"}, {"attribute_id": "E44.At14", "entity_id": "E44", "name": "employeeId", "status": "success", "details": {"success": true, "inserted_id": "E44.At14", "schema": "workflow_runtime", "attribute_id": "E44.At14", "entity_id": "E44", "name": "employeeId", "original_attribute_id": "E44.At1"}}, {"attribute_id": "E44.At15", "entity_id": "E44", "name": "lastName", "status": "success", "details": {"success": true, "inserted_id": "E44.At15", "schema": "workflow_runtime", "attribute_id": "E44.At15", "entity_id": "E44", "name": "lastName", "original_attribute_id": "E44.At3"}}, {"attribute_id": "E44.At16", "entity_id": "E44", "name": "phone", "status": "success", "details": {"success": true, "inserted_id": "E44.At16", "schema": "workflow_runtime", "attribute_id": "E44.At16", "entity_id": "E44", "name": "phone", "original_attribute_id": "E44.At5"}}, {"attribute_id": "E44.At17", "entity_id": "E44", "name": "departmentId", "status": "success", "details": {"success": true, "inserted_id": "E44.At17", "schema": "workflow_runtime", "attribute_id": "E44.At17", "entity_id": "E44", "name": "departmentId", "original_attribute_id": "E44.At6"}}, {"attribute_id": "E44.At18", "entity_id": "E44", "name": "hireDate", "status": "success", "details": {"success": true, "inserted_id": "E44.At18", "schema": "workflow_runtime", "attribute_id": "E44.At18", "entity_id": "E44", "name": "hireDate", "original_attribute_id": "E44.At7"}}, {"attribute_id": "E44.At19", "entity_id": "E44", "name": "sickLeaveBalance", "status": "success", "details": {"success": true, "inserted_id": "E44.At19", "schema": "workflow_runtime", "attribute_id": "E44.At19", "entity_id": "E44", "name": "sickLeaveBalance", "original_attribute_id": "E44.At9"}}, {"attribute_id": "E44.At20", "entity_id": "E44", "name": "totalLeaveEntitlement", "status": "success", "details": {"success": true, "inserted_id": "E44.At20", "schema": "workflow_runtime", "attribute_id": "E44.At20", "entity_id": "E44", "name": "totalLeaveEntitlement", "original_attribute_id": "E44.At11"}}, {"attribute_id": ".At1", "entity_id": "", "name": "employeeId", "status": "success", "details": {"success": true, "inserted_id": ".At1", "schema": "workflow_runtime", "attribute_id": ".At1", "entity_id": "", "name": "employeeId", "original_attribute_id": ".At1"}}, {"attribute_id": ".At2", "entity_id": "", "name": "firstName", "status": "success", "details": {"success": true, "inserted_id": ".At2", "schema": "workflow_runtime", "attribute_id": ".At2", "entity_id": "", "name": "firstName", "original_attribute_id": ".At2"}}, {"attribute_id": ".At3", "entity_id": "", "name": "lastName", "status": "success", "details": {"success": true, "inserted_id": ".At3", "schema": "workflow_runtime", "attribute_id": ".At3", "entity_id": "", "name": "lastName", "original_attribute_id": ".At3"}}, {"attribute_id": "E45.At6", "entity_id": "E45", "name": "employmentStatus", "status": "success", "details": {"success": true, "inserted_id": "E45.At6", "schema": "workflow_runtime", "attribute_id": "E45.At6", "entity_id": "E45", "name": "employmentStatus", "original_attribute_id": "E45.At4"}}, {"attribute_id": "E44.At21", "entity_id": "E44", "name": "firstName", "status": "success", "details": {"success": true, "inserted_id": "E44.At21", "schema": "workflow_runtime", "attribute_id": "E44.At21", "entity_id": "E44", "name": "firstName", "original_attribute_id": "E44.At2"}}, {"attribute_id": "E44.At22", "entity_id": "E44", "name": "email", "status": "success", "details": {"success": true, "inserted_id": "E44.At22", "schema": "workflow_runtime", "attribute_id": "E44.At22", "entity_id": "E44", "name": "email", "original_attribute_id": "E44.At4"}}, {"attribute_id": "E44.At23", "entity_id": "E44", "name": "annualLeaveBalance", "status": "success", "details": {"success": true, "inserted_id": "E44.At23", "schema": "workflow_runtime", "attribute_id": "E44.At23", "entity_id": "E44", "name": "annualLeaveBalance", "original_attribute_id": "E44.At8"}}, {"attribute_id": "E44.At24", "entity_id": "E44", "name": "personalLeaveBalance", "status": "success", "details": {"success": true, "inserted_id": "E44.At24", "schema": "workflow_runtime", "attribute_id": "E44.At24", "entity_id": "E44", "name": "personalLeaveBalance", "original_attribute_id": "E44.At10"}}]}, "status": "success"}
