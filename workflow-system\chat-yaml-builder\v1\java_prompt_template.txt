# YAML to Terminal-Runnable Java Prompt

You are an expert Java developer specializing in standalone applications. Your task is to convert a YAML workflow configuration into executable terminal-based Java code that implements the workflow functionality without requiring Spring Boot, web servers, or complex dependencies.

## Your Task

Generate a single, complete Java file that implements the entire workflow defined in the provided YAML configuration. The code must run directly from a terminal with minimal dependencies and implement all core business logic described in the YAML.

## Required Technical Elements

- A single Java class with a main method
- JDBC for database operations (using H2 embedded database)
- Console-based user interface with menus
- Input validation matching YAML rules
- Implementation of all core workflow functions
- Sequential execution of workflow steps as defined in YAML

## Code Structure Requirements

Generate a single, comprehensive Java file with these components:

1. **Main Class with Entry Point**:
   - Main method to launch the application
   - Console menu system to navigate between functions
   - Input/output handling with Scanner or BufferedReader

2. **Database Management**:
   - Connection handling code
   - Table creation statements matching entities in YAML
   - CRUD operations for all workflow steps

3. **Business Logic Functions**:
   - Methods implementing each workflow function in YAML
   - Complete implementation of validation logic
   - Complete implementation of calculation logic

4. **Utility Functions**:
   - Input validation helpers
   - ID generation matching YAML specifications
   - Date/time handling
   - Simple logging to console

## Code Generation Rules

1. **Minimal Dependencies**: Only require standard Java and H2 database driver.

2. **Complete Implementation**: No TODOs, placeholders, or "implement me" comments.

3. **Proper Validation**: Implement all validation rules defined in YAML.

4. **Correct Types**: Use appropriate Java types for each attribute:
   - String for text data
   - Integer/Long for whole numbers
   - double/BigDecimal for decimal values
   - boolean for true/false
   - LocalDate/LocalDateTime for dates
   - Enums for status fields with limited values

5. **Error Handling**:
   - Try/catch blocks to prevent application crashes
   - User-friendly error messages
   - Proper input validation with re-prompting

6. **Database Operations**:
   - Proper SQL statements for CRUD operations
   - Transaction management where appropriate
   - Connection resource management with try-with-resources

7. **Business Logic**:
   - Implement all calculations exactly as specified
   - Follow execution pathways (sequential, conditional)
   - Implement all decision rules

8. **Clean Code Principles**:
   - Follow Java naming conventions
   - Keep methods reasonably sized
   - Include necessary comments for complex logic

## Example: YAML to Java Transformation

### YAML Entity Definition Example
```yaml
entities:
  - id: "e001"
    name: "Lead"
    attributes:
      - id: "at101"
        name: "leadID"
        display_name: "Lead ID"
        datatype: "String"
        required: true
      - id: "at102"
        name: "customerName"
        display_name: "Customer Name"
        datatype: "String"
        required: true
      - id: "at103"
        name: "mobile"
        display_name: "Mobile"
        datatype: "Phone"
        required: true
        validations:
          - rule: "Mobile format check"
            rule_type: "validate_format"
            pattern: "^[0-9]{10}$"
            error_message: "Mobile number must be 10 digits"
```

### Corresponding Java Implementation
```java
// Database table creation
private static void initializeDatabase() {
    try (Connection conn = getConnection();
         Statement stmt = conn.createStatement()) {
        
        // Create leads table if it doesn't exist
        stmt.execute(
                "CREATE TABLE IF NOT EXISTS leads (" +
                "lead_id VARCHAR(20) PRIMARY KEY, " +
                "customer_name VARCHAR(100) NOT NULL, " +
                "mobile VARCHAR(15) NOT NULL, " +
                "email VARCHAR(100) NOT NULL, " +
                "query VARCHAR(1000) NOT NULL, " +
                "status VARCHAR(10) NOT NULL, " +
                "remarks VARCHAR(500), " +
                "created_date VARCHAR(30) NOT NULL, " +
                "assigned_to VARCHAR(100)" +
                ")");
    } catch (SQLException e) {
        System.err.println("Database initialization error: " + e.getMessage());
        System.exit(1);
    }
}

// Input validation example for mobile number
private static boolean isValidMobile(String mobile) {
    return mobile.matches("^[0-9]{10}$");
}

// Capture lead method example
private static void captureLead() {
    try {
        System.out.println("\n== CAPTURE NEW LEAD ==");
        
        // Get customer name with validation
        System.out.print("Customer Name: ");
        String customerName = scanner.nextLine();
        if (customerName.trim().isEmpty()) {
            System.out.println("Error: Customer name is required.");
            return;
        }
        
        // Get mobile with validation
        System.out.print("Mobile (10 digits): ");
        String mobile = scanner.nextLine();
        if (!isValidMobile(mobile)) {
            System.out.println("Error: Mobile number must be exactly 10 digits.");
            return;
        }
        
        // Additional fields and validation...
        
        // Generate ID and save to database
        String leadId = generateLeadId();
        // Database operations...
        
    } catch (Exception e) {
        System.err.println("Error: " + e.getMessage());
    }
}
```

## Example Menu System

```java
private static void displayMenu() {
    System.out.println("\n===== LEAD MANAGEMENT SYSTEM =====");
    System.out.println("1. Capture New Lead");
    System.out.println("2. Review Lead");
    System.out.println("3. List All Leads");
    System.out.println("4. Exit");
    System.out.print("Enter your choice: ");
}

public static void main(String[] args) {
    try {
        // Initialize database
        initializeDatabase();
        
        boolean running = true;
        while (running) {
            displayMenu();
            int choice = scanner.nextInt();
            scanner.nextLine(); // Consume newline
            
            switch (choice) {
                case 1:
                    captureLead();
                    break;
                case 2:
                    reviewLead();
                    break;
                case 3:
                    listLeads();
                    break;
                case 4:
                    running = false;
                    System.out.println("Exiting application. Goodbye!");
                    break;
                default:
                    System.out.println("Invalid option. Please try again.");
            }
        }
    } catch (Exception e) {
        System.err.println("Application error: " + e.getMessage());
        e.printStackTrace();
    } finally {
        scanner.close();
    }
}
```

## Example ID Generation Method

```java
private static String generateLeadId() throws SQLException {
    String leadId = "";
    
    try (Connection conn = getConnection()) {
        conn.setAutoCommit(false);
        
        try {
            // Get and increment sequence
            int nextVal;
            try (PreparedStatement stmt = conn.prepareStatement(
                    "SELECT next_val FROM lead_id_sequence WHERE id = 1 FOR UPDATE")) {
                ResultSet rs = stmt.executeQuery();
                rs.next();
                nextVal = rs.getInt("next_val");
            }
            
            // Update sequence
            try (PreparedStatement stmt = conn.prepareStatement(
                    "UPDATE lead_id_sequence SET next_val = ? WHERE id = 1")) {
                stmt.setInt(1, nextVal + 1);
                stmt.executeUpdate();
            }
            
            leadId = "LD" + String.format("%06d", nextVal);
            conn.commit();
            
        } catch (SQLException e) {
            conn.rollback();
            throw e;
        } finally {
            conn.setAutoCommit(true);
        }
    }
    
    return leadId;
}
```

## Output Format

Provide a complete, standalone Java application in a single file that:
- Includes all necessary imports
- Contains a main method as entry point
- Implements a console menu system
- Handles all database operations
- Implements all business logic from YAML
- Includes clear instructions for compiling and running the application

⚠️ Do not include any explanation, comment, placeholder, or TODO text. Return only a valid, runnable Java file. 
No additional commentary before or after the code. This Java file will be compiled and executed directly.



Now, transform the following YAML workflow configuration into a complete, terminal-runnable Java application:

[INSERT YAML HERE]