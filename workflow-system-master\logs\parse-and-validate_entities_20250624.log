{"timestamp": "2025-06-24T10:31:13.453425", "endpoint": "parse-and-validate/entities", "input": {"natural_language": "Tenant: Acme Corp\n\nLeaveApplication has leaveId^PK, employeeId^FK, startDate, endDate, numDays[derived], reason, leaveTypeName, leaveSubTypeName, status, requiresDocumentation, documentationProvided, submissionDate, approvalDate, approvedBy^FK, comments, isRetroactive[derived], insufficientBalance[derived], lowTeamAvailability[derived], allowedNumberOfDays.\n\nEntity: LeaveApplication\n- Entity Name: LeaveApplication\n- Display Name: Leave Application\n- Type: transaction\n- Description: Represents an employee request for time off from work\n- Business Domain: Human Resources\n- Category: Leave Management\n- Tags: leave, application, request, approval\n- Archival Strategy: archive_only\n- Icon: file-text\n- Colour Theme: blue", "tenant_id": null}, "output": {"success": true, "parsed_data": {"entity_id": "E7", "name": "LeaveApplication", "display_name": "Leave Application", "tenant_id": "T2", "tenant_name": "Acme Corp", "business_domain": "Human Resources", "category": "Leave Management", "tags": ["leave", "application", "request", "approval"], "archival_strategy": "archive_only", "icon": "file-text", "colour_theme": "blue", "version": 1, "status": "draft", "type": "transaction", "description": "Represents an employee request for time off from work", "table_name": "e7_LeaveApplication", "natural_language": "Tenant: Acme Corp\n\nLeaveApplication has leaveId^PK, employeeId^FK, startDate, endDate, numDays[derived], reason, leaveTypeName, leaveSubTypeName, status, requiresDocumentation, documentationProvided, submissionDate, approvalDate, approvedBy^FK, comments, isRetroactive[derived], insufficientBalance[derived], lowTeamAvailability[derived], allowedNumberOfDays.\n\nEntity: LeaveApplication\n- Entity Name: LeaveApplication\n- Display Name: Leave Application\n- Type: transaction\n- Description: Represents an employee request for time off from work\n- Business Domain: Human Resources\n- Category: Leave Management\n- Tags: leave, application, request, approval\n- Archival Strategy: archive_only\n- Icon: file-text\n- Colour Theme: blue", "created_at": "2025-06-24T10:31:13.435183", "updated_at": "2025-06-24T10:31:13.435183", "created_by": "<PERSON><PERSON>", "updated_by": "<PERSON><PERSON>", "entity_status": "existing", "changes_detected": [], "icon_type": "text", "icon_content": ""}, "validation_result": {"structure_errors": [], "required_field_errors": [], "data_type_errors": [], "custom_errors": []}, "dependency_errors": [], "uniqueness_result": {"is_unique": false, "status": "exists_in_postgres", "message": "Entity with entity_id E7 already exists in PostgreSQL"}, "operation": "parse_and_validate", "is_valid": true}, "status": "success"}