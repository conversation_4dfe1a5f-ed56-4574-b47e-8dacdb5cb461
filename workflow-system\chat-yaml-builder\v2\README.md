# YAML Builder v2

This directory contains the implementation of YAML Builder v2, which provides functionality for parsing prescriptive text directly into structured data for database storage, eliminating the need for YAML as an intermediate format.

## Overview

YAML Builder v2 introduces a more modular and efficient approach to parsing and deploying components. The main improvements include:

1. **Direct Prescriptive Text Parsing**: Parse prescriptive text directly into structured data without YAML as an intermediate format.
2. **Modular Component Parsers**: Separate parsers for each component type (entities, global objectives, local objectives, roles).
3. **Dependency Management**: Enforced deployment order to ensure dependencies are met.
4. **Enhanced Logging and Error Handling**: Comprehensive logging and error handling for enterprise-grade reliability.
5. **Name-Based References**: Support for name-based references instead of ID-based references for better readability and maintainability.
6. **Enhanced GO Template**: Updated GO template with comprehensive metadata, trigger definition, performance metrics, and validation checklist.

## Component Types

The system supports the following component types:

1. **Entities**: Basic data structures with attributes and relationships.
2. **Global Objectives (GOs)**: High-level business objectives that define what the system aims to achieve. The updated GO template includes:
   - Core Metadata with classification structure (Global Objective, Book, Chapter, Tenant)
   - Process Ownership
   - Trigger Definition for system-initiated GOs
   - Data Management (Input Stack, Input Mapping Stack, Output Stack, Output Mapping Stack)
   - Data Constraints
   - Process Definition (Process Flow, Parallel Flows, Rollback Pathways)
   - Business Rules
   - Integration Points (GO Relationships, External Systems)
   - Performance Metadata with SLA thresholds and critical LO performance
   - Process Mining Schema with enhanced sections
   - Sample Data
   - Validation Checklist for GO Creation
3. **Local Objectives (LOs)**: Specific objectives that contribute to global objectives.
4. **Roles**: User roles with permissions to access and manipulate entities and objectives.

## Directory Structure

- `parsers/`: Contains parsers for different component types
  - `entity_parser.py`: Parser for entity definitions
  - `go_parser.py`: Parser for global objective definitions
  - `lo_parser.py`: Parser for local objective definitions
  - `role_parser.py`: Parser for role definitions
  - `__init__.py`: Package initialization file
- `deployers/`: Contains deployers for different component types
  - `entity_deployer.py`: Deployer for entities
  - `go_deployer.py`: Deployer for global objectives
  - `lo_deployer.py`: Deployer for local objectives
  - `role_deployer.py`: Deployer for roles
  - `__init__.py`: Package initialization file
- `prompts/`: Contains prompt templates for generating prescriptive text
  - `entities_prompt.txt`: Prompt template for entities
  - `go_definitions_prompt.txt`: Prompt template for global objectives
  - `lo_definitions_prompt.txt`: Prompt template for local objectives
  - `roles_prompt.txt`: Prompt template for roles
- `templates/`: Contains prescriptive templates for different component types
  - `entities_prescriptive_template.txt`: Prescriptive template for entities
  - `go_definitions_prescriptive_template.txt`: Prescriptive template for global objectives
  - `lo_definitions_prescriptive_template.txt`: Prescriptive template for local objectives
  - `roles_prescriptive_template.txt`: Prescriptive template for roles
- `prescriptive_parser.py`: Main parser interface that uses component-specific parsers
- `component_deployer.py`: Main deployer interface that uses component-specific deployers
- `component_validator.py`: Validates components before deployment
- `registry_validator.py`: Validates component registry
- `yaml_assembler.py`: Assembles YAML from structured data (legacy support)
- `check_schema.py`: Checks database schema
- `db_utils.py`: Database utility functions
- `query_db.py`: Database query functions
- `id_generator.py`: Generates IDs for components
- `test_components.py`: Tests component functionality
- `test_prescriptive_parser.py`: Tests prescriptive parser functionality

## Deployment Order

Components must be deployed in the following order due to dependencies:

1. **Entities** first (no dependencies)
2. **Global Objectives (GOs)** next (depend on entities)
3. **Local Objectives (LOs)** after that (depend on GOs)
4. **Roles** last (depend on entities, GOs, and LOs)

The `component_deployer.py` enforces this order to ensure that all dependencies are met.

## Usage

### Parsing Prescriptive Text

```python
from prescriptive_parser import PrescriptiveParser

# Initialize parser
parser = PrescriptiveParser()

# Parse prescriptive text for an entity
component_type = 'entities'
prescriptive_text = """
Entity User (user):
    description: Represents a user in the system
    - id: type: string, required
    - name: type: string, required
    - email: type: string, required
    - role: type: string, required
"""

component_data, warnings = parser.parse(component_type, prescriptive_text)

# Check for warnings
if warnings:
    print(f"Warnings: {warnings}")

# Use component data
print(component_data)

# Parse prescriptive text for a GO with the updated template
component_type = 'go_definitions'
prescriptive_text = """
## Leave Approval Process

Core Metadata:
- name: "Process Leave Requests"
- version: "1.0"
- status: "Active"
- description: "Manages employee leave requests from submission to approval or rejection"
- primary_entity: "LeaveApplication"
- classification: "HR Process"
  - Global Objective: "Leave Request Processing"
  - Book: "Employee Leave Management"
  - Chapter: "Leave Request Lifecycle"
  - Tenant: "Acme Corporation"

Process Ownership:
- Originator: Employee
- Process Owner: HR Manager
- Business Sponsor: Human Resources Department

Trigger Definition:
- Trigger Type: event-driven
- Trigger Condition: Employee submits leave request
- Trigger Frequency: on-demand
- Trigger Parameters: employeeId, startDate, endDate, leaveTypeId

Data Management:
...
"""

go_data, warnings = parser.parse(component_type, prescriptive_text)

# Check for warnings
if warnings:
    print(f"Warnings: {warnings}")

# Use GO data
print(go_data)
```

### Deploying Components

```python
from component_deployer import ComponentDeployer

# Initialize deployer
deployer = ComponentDeployer(use_temp_schema=True)

# Deploy a single component from prescriptive text
success, messages = deployer.deploy_from_prescriptive_text('entities', prescriptive_text)

# Check for messages
if messages:
    print(f"Messages: {messages}")

# Check if deployment was successful
if success:
    print("Deployment successful")
else:
    print("Deployment failed")

# Deploy a GO with the updated template
success, messages = deployer.deploy_from_prescriptive_text('go_definitions', go_prescriptive_text)

# Check for messages
if messages:
    print(f"Messages: {messages}")

# Deploy multiple components in the correct order
components = {
    'entities': entities_text,
    'go_definitions': go_text,
    'lo_definitions': lo_text,
    'roles': roles_text
}

success, messages = deployer.deploy_multiple_from_prescriptive_text(components)

# Promote from temporary schema to runtime schema
if success:
    success, messages = deployer.promote_from_temp_to_runtime()
```

### Running Tests

```bash
# Run all tests
python test_prescriptive_parser.py --test-all

# Run specific tests
python test_prescriptive_parser.py --test-individual
python test_prescriptive_parser.py --test-multiple
python test_prescriptive_parser.py --test-dependencies

# Test GO parser with the updated template
python test_prescriptive_parser.py --test-go-template

# Use temporary schema for testing
python test_prescriptive_parser.py --use-temp-schema
```

## Error Handling and Logging

The system uses Python's logging module for comprehensive logging. Log messages are written to both the console and log files. Each component has its own logger for better organization and filtering.

Error handling is implemented at multiple levels:
- Parsing errors are caught and reported with detailed messages
- Deployment errors are caught and reported with detailed messages
- Database errors are caught and reported with detailed messages

## Dependencies

- Python 3.8+
- PyYAML
- psycopg2 (for PostgreSQL database access)
- logging

## License

Proprietary - All rights reserved.
