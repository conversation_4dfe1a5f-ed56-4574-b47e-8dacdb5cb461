import unittest
import datetime
from typing import List
import sys
import os

# Add project root to path if needed
project_root = '/home/<USER>/workflow-system/runtime/workflow-engine'
if project_root not in sys.path:
    sys.path.insert(0, project_root)

# Import the function to test
from app.services.system_functions import is_business_day

class TestIsBusinessDay(unittest.TestCase):
    def test_weekday_not_holiday(self):
        """Test with a weekday that is not a holiday."""
        # Monday
        result = is_business_day("2023-01-02")
        self.assertTrue(result)
        
        # Wednesday
        result = is_business_day("2023-01-04")
        self.assertTrue(result)
        
        # Friday
        result = is_business_day("2023-01-06")
        self.assertTrue(result)
    
    def test_weekend(self):
        """Test with weekend days."""
        # Saturday
        result = is_business_day("2023-01-07")
        self.assertFalse(result)
        
        # Sunday
        result = is_business_day("2023-01-08")
        self.assertFalse(result)
    
    def test_weekday_holiday(self):
        """Test with a weekday that is a holiday."""
        # Wednesday as a holiday
        holidays = ["2023-01-04"]
        result = is_business_day("2023-01-04", holidays)
        self.assertFalse(result)
    
    def test_multiple_holidays(self):
        """Test with multiple holidays."""
        holidays = ["2023-01-02", "2023-01-03", "2023-01-04"]
        
        # Monday (holiday)
        result = is_business_day("2023-01-02", holidays)
        self.assertFalse(result)
        
        # Wednesday (holiday)
        result = is_business_day("2023-01-04", holidays)
        self.assertFalse(result)
        
        # Thursday (not holiday)
        result = is_business_day("2023-01-05", holidays)
        self.assertTrue(result)
    
    def test_holiday_on_weekend(self):
        """Test with a holiday that falls on a weekend."""
        holidays = ["2023-01-07"]  # Saturday
        
        result = is_business_day("2023-01-07", holidays)
        self.assertFalse(result)  # Weekend is already not a business day
    
    def test_empty_holidays_list(self):
        """Test with an empty holidays list."""
        # Monday with empty holidays list
        result = is_business_day("2023-01-02", [])
        self.assertTrue(result)
    
    def test_none_holidays(self):
        """Test with None as holidays list."""
        # Monday with None holidays
        result = is_business_day("2023-01-02", None)
        self.assertTrue(result)
    
    def test_invalid_holiday_format(self):
        """Test with invalid holiday date format (should be ignored)."""
        holidays = ["2023/01/04", "2023-01-05"]  # First date has invalid format
        
        # Wednesday (would be holiday if format was valid)
        result = is_business_day("2023-01-04", holidays)
        self.assertTrue(result)  # Invalid format holiday is ignored
        
        # Thursday (valid format holiday)
        result = is_business_day("2023-01-05", holidays)
        self.assertFalse(result)
    
    def test_duplicate_holidays(self):
        """Test with duplicate holidays."""
        holidays = ["2023-01-04", "2023-01-04"]  # Same holiday twice
        
        result = is_business_day("2023-01-04", holidays)
        self.assertFalse(result)
    
    def test_leap_year_day(self):
        """Test with February 29 on a leap year."""
        # February 29, 2024 (leap year, falls on a Thursday)
        result = is_business_day("2024-02-29")
        self.assertTrue(result)
    
    def test_invalid_date_format(self):
        """Test with invalid date format (should raise ValueError)."""
        with self.assertRaises(ValueError):
            is_business_day("2023/01/02")
        
        with self.assertRaises(ValueError):
            is_business_day("2023.01.06")

if __name__ == '__main__':
    unittest.main()