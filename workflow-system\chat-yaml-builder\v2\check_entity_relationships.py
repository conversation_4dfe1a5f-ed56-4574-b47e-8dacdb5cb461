#!/usr/bin/env python3
"""
Script to check the entity relationships in the database.
"""

import os
import logging
import psycopg2

# Set up logging
logging.basicConfig(level=logging.INFO, 
                   format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger('check_entity_relationships')

def get_db_connection(schema_name: str = None):
    """
    Get a database connection.
    
    Args:
        schema_name: Optional schema name to set as the search path
        
    Returns:
        Database connection object
    """
    # Get database connection parameters from environment variables or use defaults
    db_host = os.environ.get('DB_HOST', '**********')
    db_port = os.environ.get('DB_PORT', '5432')
    db_name = os.environ.get('DB_NAME', 'workflow_system')
    db_user = os.environ.get('DB_USER', 'postgres')
    db_password = os.environ.get('DB_PASSWORD', 'workflow_postgres_secure_password')
    
    # Connect to the database
    conn = psycopg2.connect(
        host=db_host,
        port=db_port,
        dbname=db_name,
        user=db_user,
        password=db_password
    )
    
    # Set schema search path if provided
    if schema_name:
        with conn.cursor() as cursor:
            cursor.execute(f"SET search_path TO {schema_name}")
            conn.commit()
    
    return conn

def check_entity_relationships():
    """
    Check the entity relationships in the database.
    """
    schema_name = 'workflow_temp'
    
    try:
        conn = get_db_connection(schema_name)
        
        with conn.cursor() as cursor:
            # Get entity relationships
            cursor.execute("""
                SELECT er.id, e1.name AS source_entity, e2.name AS target_entity, 
                       er.relationship_type, a1.name AS source_attribute, a2.name AS target_attribute,
                       er.on_delete, er.on_update, er.foreign_key_type
                FROM entity_relationships er
                JOIN entities e1 ON er.source_entity_id = e1.entity_id
                JOIN entities e2 ON er.target_entity_id = e2.entity_id
                JOIN entity_attributes a1 ON er.source_attribute_id = a1.attribute_id
                JOIN entity_attributes a2 ON er.target_attribute_id = a2.attribute_id
            """)
            
            logger.info("Entity relationships:")
            for row in cursor.fetchall():
                logger.info(f"  - {row}")
            
            # Get foreign key constraints
            cursor.execute("""
                SELECT tc.constraint_name, tc.table_name, kcu.column_name, 
                       ccu.table_name AS foreign_table_name, ccu.column_name AS foreign_column_name,
                       rc.delete_rule, rc.update_rule
                FROM information_schema.table_constraints AS tc 
                JOIN information_schema.key_column_usage AS kcu
                  ON tc.constraint_name = kcu.constraint_name
                  AND tc.table_schema = kcu.table_schema
                JOIN information_schema.constraint_column_usage AS ccu
                  ON ccu.constraint_name = tc.constraint_name
                  AND ccu.table_schema = tc.table_schema
                JOIN information_schema.referential_constraints AS rc
                  ON rc.constraint_name = tc.constraint_name
                  AND rc.constraint_schema = tc.table_schema
                WHERE tc.constraint_type = 'FOREIGN KEY'
                  AND tc.table_schema = %s
            """, (schema_name,))
            
            logger.info("\nForeign key constraints:")
            for row in cursor.fetchall():
                logger.info(f"  - {row}")
    except Exception as e:
        logger.error(f"Error checking entity relationships: {str(e)}")
    finally:
        if conn:
            conn.close()

if __name__ == "__main__":
    check_entity_relationships()
