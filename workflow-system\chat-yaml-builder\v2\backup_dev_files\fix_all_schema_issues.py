#!/usr/bin/env python3
"""
Fix all schema migration issues for YAML Builder v2 in one go.

This script:
1. Drops and recreates the global_objectives table with all required columns
2. Adds missing columns to the entities table
3. Adds missing columns to the entity_attributes table
4. Makes entity_business_rules.condition and entity_business_rules.action nullable
5. Creates a backup of the original entity_deployer.py file
6. Replaces the original entity_deployer.py file with a modified version
7. Creates a backup of the original go_deployer.py file
8. Replaces the original go_deployer.py file with a modified version

Usage:
    python fix_all_schema_issues.py --schema-name workflow_temp
    python fix_all_schema_issues.py --schema-name workflow_runtime
"""

import os
import sys
import argparse
import logging
import shutil
import psycopg2
from typing import List, Tuple, Dict, Any, Optional

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('fix_all_schema_issues')

# Database connection parameters
DB_PARAMS = {
    "dbname": "workflow_system",
    "user": "postgres",
    "password": "workflow_postgres_secure_password",
    "host": "**********",
    "port": "5432"
}

def execute_query(query: str, params: Optional[Tuple] = None, schema_name: Optional[str] = None) -> Tuple[bool, List[str], Optional[List[Tuple]]]:
    """
    Execute a SQL query.
    
    Args:
        query: SQL query to execute
        params: Query parameters
        schema_name: Schema name to use
        
    Returns:
        Tuple containing:
            - Boolean indicating if the query was successful
            - List of messages (warnings, errors, or success messages)
            - Query results (if any)
    """
    messages = []
    result = None
    
    try:
        # Connect to the database
        conn = psycopg2.connect(**DB_PARAMS)
        
        # Create a cursor
        cursor = conn.cursor()
        
        # Set search path if schema_name is provided
        if schema_name:
            cursor.execute(f"SET search_path TO {schema_name}")
        
        # Execute the query
        if params:
            cursor.execute(query, params)
        else:
            cursor.execute(query)
        
        # Commit the transaction
        conn.commit()
        
        # Fetch the results if the query returns rows
        if cursor.description:
            result = cursor.fetchall()
        
        # Close the cursor and connection
        cursor.close()
        conn.close()
        
        return True, messages, result
    except Exception as e:
        # Rollback the transaction
        if 'conn' in locals() and conn:
            conn.rollback()
        
        # Close the cursor and connection
        if 'cursor' in locals() and cursor:
            cursor.close()
        if 'conn' in locals() and conn:
            conn.close()
        
        error_msg = f"Database error: {str(e)}"
        logger.error(error_msg)
        messages.append(error_msg)
        
        return False, messages, None

def check_column_exists(schema_name: str, table_name: str, column_name: str) -> bool:
    """
    Check if a column exists in a table.
    
    Args:
        schema_name: Schema name
        table_name: Table name
        column_name: Column name
        
    Returns:
        Boolean indicating if the column exists
    """
    try:
        success, query_messages, result = execute_query(
            """
            SELECT EXISTS (
                SELECT FROM information_schema.columns 
                WHERE table_schema = %s
                AND table_name = %s
                AND column_name = %s
            )
            """,
            (schema_name, table_name, column_name)
        )
        
        if not success:
            logger.error(f"Error checking if column {column_name} exists: {query_messages}")
            return False
        
        return result and result[0][0]
    except Exception as e:
        logger.error(f"Error checking if column {column_name} exists: {str(e)}")
        return False

def fix_all_schema_issues(schema_name: str) -> Tuple[bool, List[str]]:
    """
    Fix all schema migration issues.
    
    Args:
        schema_name: Schema name
        
    Returns:
        Tuple containing:
            - Boolean indicating if the operation was successful
            - List of messages (warnings, errors, or success messages)
    """
    messages = []
    
    try:
        # 1. Drop and recreate the global_objectives table
        success, query_messages, result = execute_query(
            f"""
            DROP TABLE IF EXISTS {schema_name}.global_objectives CASCADE;
            
            CREATE TABLE {schema_name}.global_objectives (
                go_id VARCHAR(50) PRIMARY KEY,
                name VARCHAR(100) NOT NULL,
                description TEXT,
                process_mining_schema JSONB,
                performance_metadata JSONB,
                version_type VARCHAR(10),
                status VARCHAR(20) DEFAULT 'active',
                tenant_id VARCHAR(50) DEFAULT 't001',
                deleted_mark BOOLEAN DEFAULT false
            );
            """
        )
        
        if not success:
            messages.extend(query_messages)
            return False, messages
        
        messages.append(f"Recreated {schema_name}.global_objectives table with all required columns")
        logger.info(f"Recreated {schema_name}.global_objectives table with all required columns")
        
        # 2. Add missing columns to the entities table
        entities_columns_to_add = [
            ("status", "VARCHAR(20)", "active"),
            ("type", "VARCHAR(50)", "standard"),
            ("attribute_prefix", "VARCHAR(50)", "")
        ]
        
        for column_name, column_type, default_value in entities_columns_to_add:
            # Check if the column exists
            column_exists = check_column_exists(schema_name, "entities", column_name)
            
            if not column_exists:
                # Add the column
                query = f"""
                    ALTER TABLE {schema_name}.entities 
                    ADD COLUMN {column_name} {column_type} NULL DEFAULT '{default_value}'::character varying
                """
                
                success, query_messages, _ = execute_query(query)
                
                if not success:
                    messages.extend(query_messages)
                    return False, messages
                
                messages.append(f"Added column {column_name} to {schema_name}.entities")
                logger.info(f"Added column {column_name} to {schema_name}.entities")
            else:
                messages.append(f"Column {column_name} already exists in {schema_name}.entities")
                logger.info(f"Column {column_name} already exists in {schema_name}.entities")
        
        # 3. Add missing columns to the entity_attributes table
        entity_attributes_columns_to_add = [
            ("display_name", "VARCHAR(100)", None),
            ("datatype", "VARCHAR(50)", "string"),
            ("status", "VARCHAR(20)", "active")
        ]
        
        for column_name, column_type, default_value in entity_attributes_columns_to_add:
            # Check if the column exists
            column_exists = check_column_exists(schema_name, "entity_attributes", column_name)
            
            if not column_exists:
                # Add the column
                if default_value:
                    query = f"""
                        ALTER TABLE {schema_name}.entity_attributes 
                        ADD COLUMN {column_name} {column_type} NULL DEFAULT '{default_value}'::character varying
                    """
                else:
                    query = f"""
                        ALTER TABLE {schema_name}.entity_attributes 
                        ADD COLUMN {column_name} {column_type} NULL
                    """
                
                success, query_messages, _ = execute_query(query)
                
                if not success:
                    messages.extend(query_messages)
                    return False, messages
                
                messages.append(f"Added column {column_name} to {schema_name}.entity_attributes")
                logger.info(f"Added column {column_name} to {schema_name}.entity_attributes")
            else:
                messages.append(f"Column {column_name} already exists in {schema_name}.entity_attributes")
                logger.info(f"Column {column_name} already exists in {schema_name}.entity_attributes")
        
        # 4. Make entity_business_rules.condition and entity_business_rules.action nullable
        success, query_messages, result = execute_query(
            """
            SELECT EXISTS (
                SELECT FROM information_schema.tables 
                WHERE table_schema = %s
                AND table_name = 'entity_business_rules'
            )
            """,
            (schema_name,)
        )
        
        if not success:
            messages.extend(query_messages)
            return False, messages
        
        entity_business_rules_table_exists = result and result[0][0]
        
        if entity_business_rules_table_exists:
            # Check if the condition column is not nullable
            success, query_messages, result = execute_query(
                """
                SELECT is_nullable 
                FROM information_schema.columns 
                WHERE table_schema = %s
                AND table_name = 'entity_business_rules'
                AND column_name = 'condition'
                """,
                (schema_name,)
            )
            
            if not success:
                messages.extend(query_messages)
                return False, messages
            
            condition_is_not_nullable = result and result[0][0] == 'NO'
            
            if condition_is_not_nullable:
                # Make the condition column nullable
                query = f"""
                    ALTER TABLE {schema_name}.entity_business_rules 
                    ALTER COLUMN condition DROP NOT NULL
                """
                
                success, query_messages, _ = execute_query(query)
                
                if not success:
                    messages.extend(query_messages)
                    return False, messages
                
                messages.append(f"Made column condition nullable in {schema_name}.entity_business_rules")
                logger.info(f"Made column condition nullable in {schema_name}.entity_business_rules")
            
            # Check if the action column is not nullable
            success, query_messages, result = execute_query(
                """
                SELECT is_nullable 
                FROM information_schema.columns 
                WHERE table_schema = %s
                AND table_name = 'entity_business_rules'
                AND column_name = 'action'
                """,
                (schema_name,)
            )
            
            if not success:
                messages.extend(query_messages)
                return False, messages
            
            action_is_not_nullable = result and result[0][0] == 'NO'
            
            if action_is_not_nullable:
                # Make the action column nullable
                query = f"""
                    ALTER TABLE {schema_name}.entity_business_rules 
                    ALTER COLUMN action DROP NOT NULL
                """
                
                success, query_messages, _ = execute_query(query)
                
                if not success:
                    messages.extend(query_messages)
                    return False, messages
                
                messages.append(f"Made column action nullable in {schema_name}.entity_business_rules")
                logger.info(f"Made column action nullable in {schema_name}.entity_business_rules")
        
        # 5. Create a backup of the original entity_deployer.py file and replace it
        script_dir = os.path.dirname(os.path.abspath(__file__))
        entity_deployer_file = os.path.join(script_dir, 'deployers', 'entity_deployer.py')
        entity_deployer_backup = os.path.join(script_dir, 'deployers', 'entity_deployer.py.bak')
        entity_deployer_modified = os.path.join(script_dir, 'deployers', 'entity_deployer_modified.py')
        
        if os.path.exists(entity_deployer_file) and os.path.exists(entity_deployer_modified):
            # Create a backup of the original file
            shutil.copy2(entity_deployer_file, entity_deployer_backup)
            messages.append(f"Created backup of {entity_deployer_file} at {entity_deployer_backup}")
            logger.info(f"Created backup of {entity_deployer_file} at {entity_deployer_backup}")
            
            # Replace the original file with the modified file
            shutil.copy2(entity_deployer_modified, entity_deployer_file)
            messages.append(f"Replaced {entity_deployer_file} with {entity_deployer_modified}")
            logger.info(f"Replaced {entity_deployer_file} with {entity_deployer_modified}")
        
        # We don't need to modify the go_deployer.py file since we're ensuring the columns exist
        
        return True, messages
    except Exception as e:
        error_msg = f"Error fixing schema issues: {str(e)}"
        logger.error(error_msg, exc_info=True)
        messages.append(error_msg)
        return False, messages

def main():
    """Main function."""
    parser = argparse.ArgumentParser(description='Fix all schema migration issues for YAML Builder v2 in one go.')
    parser.add_argument('--schema-name', required=True, help='Schema name to fix (workflow_temp or workflow_runtime)')
    args = parser.parse_args()
    
    schema_name = args.schema_name
    
    if schema_name not in ['workflow_temp', 'workflow_runtime']:
        logger.error(f"Invalid schema name: {schema_name}. Must be workflow_temp or workflow_runtime.")
        sys.exit(1)
    
    logger.info(f"Fixing all schema migration issues for {schema_name}")
    
    # Fix all schema issues
    success, messages = fix_all_schema_issues(schema_name)
    
    for message in messages:
        logger.info(message)
    
    if not success:
        logger.error(f"Failed to fix schema issues for {schema_name}")
        sys.exit(1)
    
    logger.info(f"Successfully fixed all schema issues for {schema_name}")
    logger.info("You can now run deploy_to_temp_schema.py or deploy_to_runtime_schema.py")

if __name__ == '__main__':
    main()
