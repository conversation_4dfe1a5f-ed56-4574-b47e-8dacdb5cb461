-- Add Administrator role and ensure org_unit_id exists
-- This script adds the Administrator role to the database and ensures org_it exists

-- Set search path to workflow_runtime schema
SET search_path TO workflow_runtime;

-- Add "Administrator" role if it doesn't exist
INSERT INTO roles (role_id, name, tenant_id, created_at, updated_at)
SELECT 'r005', 'Administrator', 't001', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP
WHERE NOT EXISTS (SELECT 1 FROM roles WHERE name = 'Administrator');

-- Ensure org_it exists
INSERT INTO organizational_units (org_unit_id, name, description, tenant_id, created_at, updated_at)
VALUES ('org_it', 'IT Department', 'Information Technology Department', 't001', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
ON CONFLICT (org_unit_id) DO NOTHING;

-- Verify the changes
SELECT 'Roles:' AS table_name;
SELECT role_id, name, tenant_id FROM roles WHERE name = 'Administrator';

SELECT 'Organizational Units:' AS table_name;
SELECT org_unit_id, name, description FROM organizational_units WHERE org_unit_id = 'org_it';
