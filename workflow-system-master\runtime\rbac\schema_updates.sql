-- Part 1: Database Schema Updates

-- 1.1 Extend entities table with new columns
ALTER TABLE workflow_runtime.entities 
ADD COLUMN IF NOT EXISTS entity_class VARCHAR(50) CHECK (entity_class IN ('business', 'system', 'organizational')),
ADD COLUMN IF NOT EXISTS is_system_entity BOOLEAN DEFAULT FALSE,
ADD COLUMN IF NOT EXISTS metadata JSONB DEFAULT '{}',
ADD COLUMN IF NOT EXISTS parent_entity_id VARCHAR(50) REFERENCES workflow_runtime.entities(entity_id) ON DELETE SET NULL;

-- 1.2 Create Core RBAC Tables

-- Create users table
CREATE TABLE IF NOT EXISTS workflow_runtime.users (
    user_id VARCHAR(50) PRIMARY KEY,
    username VARCHAR(100) NOT NULL UNIQUE,
    email VARCHAR(255) NOT NULL UNIQUE,
    password_hash VARCHAR(255) NOT NULL,
    first_name <PERSON><PERSON><PERSON><PERSON>(100),
    last_name <PERSON><PERSON><PERSON><PERSON>(100),
    status VARCHAR(20) NOT NULL DEFAULT 'active' CHECK (status IN ('active', 'inactive', 'locked', 'pending')),
    last_login TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create user_oauth_tokens table
CREATE TABLE IF NOT EXISTS workflow_runtime.user_oauth_tokens (
    token_id VARCHAR(50) PRIMARY KEY,
    user_id VARCHAR(50) NOT NULL REFERENCES workflow_runtime.users(user_id) ON DELETE CASCADE,
    access_token VARCHAR(1000) NOT NULL,
    refresh_token VARCHAR(1000) NOT NULL,
    token_type VARCHAR(20) DEFAULT 'Bearer',
    expires_at TIMESTAMP NOT NULL,
    scope VARCHAR(255),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create organizational_units table
CREATE TABLE IF NOT EXISTS workflow_runtime.organizational_units (
    org_unit_id VARCHAR(50) PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    parent_org_unit_id VARCHAR(50) REFERENCES workflow_runtime.organizational_units(org_unit_id) ON DELETE SET NULL,
    tenant_id VARCHAR(50) NOT NULL REFERENCES workflow_runtime.tenants(tenant_id) ON DELETE CASCADE,
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create permission_contexts table
CREATE TABLE IF NOT EXISTS workflow_runtime.permission_contexts (
    context_id VARCHAR(50) PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    context_type VARCHAR(50) NOT NULL CHECK (context_type IN ('global', 'entity', 'objective', 'function')),
    context_rules JSONB DEFAULT '{}',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create entity_relationships table (if not already exists)
-- Note: We already have this table in the schema, but we'll include it here for completeness
-- CREATE TABLE IF NOT EXISTS workflow_runtime.entity_relationships (
--     id SERIAL PRIMARY KEY,
--     source_entity_id VARCHAR(50) NOT NULL REFERENCES workflow_runtime.entities(entity_id) ON DELETE CASCADE,
--     target_entity_id VARCHAR(50) NOT NULL REFERENCES workflow_runtime.entities(entity_id) ON DELETE CASCADE,
--     relationship_type VARCHAR(50) NOT NULL,
--     source_attribute_id VARCHAR(50) NOT NULL,
--     target_attribute_id VARCHAR(50) NOT NULL,
--     created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
--     updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
-- );

-- Create entity_hierarchy table
CREATE TABLE IF NOT EXISTS workflow_runtime.entity_hierarchy (
    id SERIAL PRIMARY KEY,
    parent_entity_id VARCHAR(50) NOT NULL REFERENCES workflow_runtime.entities(entity_id) ON DELETE CASCADE,
    child_entity_id VARCHAR(50) NOT NULL REFERENCES workflow_runtime.entities(entity_id) ON DELETE CASCADE,
    hierarchy_type VARCHAR(50) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT unique_hierarchy_entry UNIQUE (parent_entity_id, child_entity_id, hierarchy_type)
);

-- Create user_sessions table for session management
CREATE TABLE IF NOT EXISTS workflow_runtime.user_sessions (
    session_id VARCHAR(50) PRIMARY KEY,
    user_id VARCHAR(50) NOT NULL REFERENCES workflow_runtime.users(user_id) ON DELETE CASCADE,
    token VARCHAR(1000) NOT NULL,
    ip_address VARCHAR(50),
    user_agent TEXT,
    expires_at TIMESTAMP NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create user_organizations table to link users to organizational units
CREATE TABLE IF NOT EXISTS workflow_runtime.user_organizations (
    id SERIAL PRIMARY KEY,
    user_id VARCHAR(50) NOT NULL REFERENCES workflow_runtime.users(user_id) ON DELETE CASCADE,
    org_unit_id VARCHAR(50) NOT NULL REFERENCES workflow_runtime.organizational_units(org_unit_id) ON DELETE CASCADE,
    is_primary BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT unique_user_org UNIQUE (user_id, org_unit_id)
);

-- Create role_permissions table to link roles to permission contexts
CREATE TABLE IF NOT EXISTS workflow_runtime.role_permissions (
    id SERIAL PRIMARY KEY,
    role_id VARCHAR(50) NOT NULL REFERENCES workflow_runtime.roles(role_id) ON DELETE CASCADE,
    context_id VARCHAR(50) NOT NULL REFERENCES workflow_runtime.permission_contexts(context_id) ON DELETE CASCADE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT unique_role_permission UNIQUE (role_id, context_id)
);

-- Create function to update timestamps
CREATE OR REPLACE FUNCTION workflow_runtime.update_timestamp()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create triggers for timestamp updates
DO $$
DECLARE
    t text;
BEGIN
    FOR t IN 
        SELECT table_name 
        FROM information_schema.tables 
        WHERE table_schema = 'workflow_runtime' 
        AND table_name IN ('users', 'user_oauth_tokens', 'organizational_units', 
                          'permission_contexts', 'entity_hierarchy', 'user_sessions',
                          'user_organizations', 'role_permissions')
    LOOP
        EXECUTE format('
            DROP TRIGGER IF EXISTS update_%I_timestamp ON workflow_runtime.%I;
            CREATE TRIGGER update_%I_timestamp
            BEFORE UPDATE ON workflow_runtime.%I
            FOR EACH ROW
            EXECUTE FUNCTION workflow_runtime.update_timestamp();
        ', t, t, t, t);
    END LOOP;
END;
$$ LANGUAGE plpgsql;
