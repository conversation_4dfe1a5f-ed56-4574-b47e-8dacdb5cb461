2025-06-23 11:03:18,792 - validators.deployment_validator - INFO - Starting deployment validation for entity_relationships
2025-06-23 11:03:18,826 - validators.deployment_validator - INFO - Status updated from draft to deployed_to_temp
2025-06-23 11:03:18,837 - validators.deployment_validator - INFO - Validation completed: 3 errors, 1 warnings
2025-06-23 11:03:18,838 - validators.deployment_validator - INFO - Validation results logged to: /home/<USER>/workflow-system/logs/validation_results_20250623_110318.json
2025-06-23 11:20:24,265 - validators.deployment_validator - INFO - Starting deployment validation for entities
2025-06-23 11:20:24,275 - validators.deployment_validator - INFO - Validation field set for entities: version=3
2025-06-23 11:20:24,298 - validators.deployment_validator - INFO - Added is_synthetic field with default value False
2025-06-23 11:20:24,306 - validators.deployment_validator - INFO - Added supports_multivalue field with default value False
2025-06-23 11:20:24,314 - validators.deployment_validator - INFO - Validation completed: 2 errors, 2 warnings
2025-06-23 11:20:24,314 - validators.deployment_validator - INFO - Validation results logged to: /home/<USER>/workflow-system/logs/validation_results_20250623_112024.json
2025-06-23 11:23:04,901 - validators.deployment_validator - INFO - Starting deployment validation for entity_relationships
2025-06-23 11:23:04,933 - validators.deployment_validator - INFO - Status updated from draft to deployed_to_temp
2025-06-23 11:23:04,945 - validators.deployment_validator - INFO - Validation completed: 3 errors, 1 warnings
2025-06-23 11:23:04,945 - validators.deployment_validator - INFO - Validation results logged to: /home/<USER>/workflow-system/logs/validation_results_20250623_112304.json
2025-06-23 11:33:59,434 - validators.deployment_validator - INFO - Starting deployment validation for constants
2025-06-23 11:33:59,443 - validators.deployment_validator - INFO - Validation field set for constants: version=5
2025-06-23 11:33:59,455 - validators.deployment_validator - INFO - Validation completed: 0 errors, 1 warnings
2025-06-23 11:33:59,455 - validators.deployment_validator - INFO - Validation results logged to: /home/<USER>/workflow-system/logs/validation_results_20250623_113359.json
2025-06-23 11:52:06,480 - validators.deployment_validator - INFO - Starting deployment validation for constants
2025-06-23 11:52:06,488 - validators.deployment_validator - INFO - Validation field set for constants: version=5
2025-06-23 11:52:06,498 - validators.deployment_validator - INFO - Validation completed: 0 errors, 1 warnings
2025-06-23 11:52:06,499 - validators.deployment_validator - INFO - Validation results logged to: /home/<USER>/workflow-system/logs/validation_results_20250623_115206.json
2025-06-23 11:56:50,567 - validators.deployment_validator - INFO - Starting deployment validation for constants
2025-06-23 11:56:50,575 - validators.deployment_validator - INFO - Validation field set for constants: version=5
2025-06-23 11:56:50,575 - validators.deployment_validator - INFO - Status updated from draft to deployed_to_temp
2025-06-23 11:56:50,587 - validators.deployment_validator - INFO - Validation completed: 0 errors, 0 warnings
2025-06-23 11:56:50,588 - validators.deployment_validator - INFO - Validation results logged to: /home/<USER>/workflow-system/logs/validation_results_20250623_115650.json
2025-06-23 11:57:27,614 - validators.deployment_validator - INFO - Starting deployment validation for constants
2025-06-23 11:57:27,622 - validators.deployment_validator - INFO - Validation field set for constants: version=5
2025-06-23 11:57:27,623 - validators.deployment_validator - INFO - Status updated from draft to deployed_to_temp
2025-06-23 11:57:27,635 - validators.deployment_validator - INFO - Validation completed: 0 errors, 0 warnings
2025-06-23 11:57:27,635 - validators.deployment_validator - INFO - Validation results logged to: /home/<USER>/workflow-system/logs/validation_results_20250623_115727.json
2025-06-23 12:18:44,650 - validators.deployment_validator - INFO - Starting deployment validation for constants
2025-06-23 12:18:44,658 - validators.deployment_validator - INFO - Validation field set for constants: version=5
2025-06-23 12:18:44,658 - validators.deployment_validator - INFO - Status updated from draft to deployed_to_temp
2025-06-23 12:18:44,671 - validators.deployment_validator - INFO - Validation completed: 0 errors, 0 warnings
2025-06-23 12:18:44,671 - validators.deployment_validator - INFO - Validation results logged to: /home/<USER>/workflow-system/logs/validation_results_20250623_121844.json
2025-06-23 12:19:33,182 - validators.deployment_validator - INFO - Starting deployment validation for constants
2025-06-23 12:19:33,190 - validators.deployment_validator - INFO - Validation field set for constants: version=5
2025-06-23 12:19:33,190 - validators.deployment_validator - INFO - Status updated from draft to deployed_to_temp
2025-06-23 12:19:33,203 - validators.deployment_validator - INFO - Validation completed: 0 errors, 0 warnings
2025-06-23 12:19:33,203 - validators.deployment_validator - INFO - Validation results logged to: /home/<USER>/workflow-system/logs/validation_results_20250623_121933.json
2025-06-23 12:20:43,071 - validators.deployment_validator - INFO - Starting deployment validation for constants
2025-06-23 12:20:43,078 - validators.deployment_validator - INFO - Validation field set for constants: version=1
2025-06-23 12:20:43,078 - validators.deployment_validator - INFO - Status updated from draft to deployed_to_temp
2025-06-23 12:20:43,091 - validators.deployment_validator - INFO - Validation completed: 0 errors, 0 warnings
2025-06-23 12:20:43,091 - validators.deployment_validator - INFO - Validation results logged to: /home/<USER>/workflow-system/logs/validation_results_20250623_122043.json
2025-06-23 12:49:33,783 - validators.deployment_validator - INFO - Starting deployment validation for entities
2025-06-23 12:49:33,793 - validators.deployment_validator - INFO - Validation field set for entities: version=3
2025-06-23 12:49:33,815 - validators.deployment_validator - INFO - Added is_synthetic field with default value False
2025-06-23 12:49:33,823 - validators.deployment_validator - INFO - Added supports_multivalue field with default value False
2025-06-23 12:49:33,830 - validators.deployment_validator - INFO - Validation completed: 2 errors, 2 warnings
2025-06-23 12:49:33,831 - validators.deployment_validator - INFO - Validation results logged to: /home/<USER>/workflow-system/logs/validation_results_20250623_124933.json
2025-06-23 13:02:44,208 - validators.deployment_validator - INFO - Starting deployment validation for entities
2025-06-23 13:02:44,217 - validators.deployment_validator - INFO - Validation field set for entities: version=3
2025-06-23 13:02:44,238 - validators.deployment_validator - INFO - Entity type validation passed: master
2025-06-23 13:02:44,238 - validators.deployment_validator - INFO - Added is_synthetic field with default value False
2025-06-23 13:02:44,245 - validators.deployment_validator - INFO - Added supports_multivalue field with default value False
2025-06-23 13:02:44,252 - validators.deployment_validator - INFO - Validation completed: 1 errors, 2 warnings
2025-06-23 13:02:44,253 - validators.deployment_validator - INFO - Validation results logged to: /home/<USER>/workflow-system/logs/validation_results_20250623_130244.json
2025-06-23 13:03:29,511 - validators.deployment_validator - INFO - Starting deployment validation for entities
2025-06-23 13:03:29,519 - validators.deployment_validator - INFO - Validation field set for entities: version=4
2025-06-23 13:03:29,538 - validators.deployment_validator - INFO - Entity type validation passed: master
2025-06-23 13:03:29,539 - validators.deployment_validator - INFO - Added is_synthetic field with default value False
2025-06-23 13:03:29,546 - validators.deployment_validator - INFO - Added supports_multivalue field with default value False
2025-06-23 13:03:29,552 - validators.deployment_validator - INFO - Validation completed: 1 errors, 2 warnings
2025-06-23 13:03:29,552 - validators.deployment_validator - INFO - Validation results logged to: /home/<USER>/workflow-system/logs/validation_results_20250623_130329.json
2025-06-23 13:17:56,698 - validators.deployment_validator - INFO - Starting deployment validation for entities
2025-06-23 13:17:56,707 - validators.deployment_validator - INFO - Validation field set for entities: version=4
2025-06-23 13:17:56,726 - validators.deployment_validator - INFO - Entity type validation passed: master
2025-06-23 13:17:56,726 - validators.deployment_validator - INFO - Added is_synthetic field with default value False
2025-06-23 13:17:56,734 - validators.deployment_validator - INFO - Added supports_multivalue field with default value False
2025-06-23 13:17:56,742 - validators.deployment_validator - INFO - Validation completed: 1 errors, 2 warnings
2025-06-23 13:17:56,742 - validators.deployment_validator - INFO - Validation results logged to: /home/<USER>/workflow-system/logs/validation_results_20250623_131756.json
2025-06-23 13:33:13,445 - validators.deployment_validator - INFO - Starting deployment validation for entities
2025-06-23 13:33:13,454 - validators.deployment_validator - INFO - Validation field set for entities: version=3
2025-06-23 13:33:13,454 - validators.deployment_validator - INFO - Status updated from draft to deployed_to_temp
2025-06-23 13:33:13,474 - validators.deployment_validator - INFO - Entity type validation passed: master
2025-06-23 13:33:13,474 - validators.deployment_validator - INFO - Added is_synthetic field with default value False
2025-06-23 13:33:13,482 - validators.deployment_validator - INFO - Added supports_multivalue field with default value False
2025-06-23 13:33:13,488 - validators.deployment_validator - INFO - Validation completed: 1 errors, 1 warnings
2025-06-23 13:33:13,489 - validators.deployment_validator - INFO - Validation results logged to: /home/<USER>/workflow-system/logs/validation_results_20250623_133313.json
2025-06-23 13:34:08,040 - validators.deployment_validator - INFO - Starting deployment validation for entities
2025-06-23 13:34:08,048 - validators.deployment_validator - INFO - Validation field set for entities: version=1
2025-06-23 13:34:08,048 - validators.deployment_validator - INFO - Status updated from draft to deployed_to_temp
2025-06-23 13:34:08,068 - validators.deployment_validator - INFO - Entity type validation passed: master
2025-06-23 13:34:08,069 - validators.deployment_validator - INFO - Added is_synthetic field with default value False
2025-06-23 13:34:08,076 - validators.deployment_validator - INFO - Added supports_multivalue field with default value False
2025-06-23 13:34:08,083 - validators.deployment_validator - INFO - Validation completed: 1 errors, 0 warnings
2025-06-23 13:34:08,084 - validators.deployment_validator - INFO - Validation results logged to: /home/<USER>/workflow-system/logs/validation_results_20250623_133408.json
2025-06-23 14:03:49,531 - validators.deployment_validator - INFO - Starting deployment validation for entity_relationships
2025-06-23 14:03:49,562 - validators.deployment_validator - INFO - Status updated from draft to deployed_to_temp
2025-06-23 14:03:49,572 - validators.deployment_validator - INFO - Validation completed: 3 errors, 1 warnings
2025-06-23 14:03:49,573 - validators.deployment_validator - INFO - Validation results logged to: /home/<USER>/workflow-system/logs/validation_results_20250623_140349.json
2025-06-23 14:03:51,489 - validators.deployment_validator - INFO - Starting deployment validation for attributes
2025-06-23 14:03:51,497 - validators.deployment_validator - INFO - Status updated from draft to deployed_to_temp
2025-06-23 14:03:51,508 - validators.deployment_validator - INFO - Validation completed: 1 errors, 1 warnings
2025-06-23 14:03:51,508 - validators.deployment_validator - INFO - Starting deployment validation for attributes
2025-06-23 14:03:51,514 - validators.deployment_validator - INFO - Status updated from draft to deployed_to_temp
2025-06-23 14:03:51,527 - validators.deployment_validator - INFO - Validation completed: 1 errors, 1 warnings
2025-06-23 14:03:51,527 - validators.deployment_validator - INFO - Starting deployment validation for attributes
2025-06-23 14:03:51,533 - validators.deployment_validator - INFO - Status updated from draft to deployed_to_temp
2025-06-23 14:03:51,545 - validators.deployment_validator - INFO - Validation completed: 1 errors, 1 warnings
2025-06-23 14:03:51,545 - validators.deployment_validator - INFO - Starting deployment validation for attributes
2025-06-23 14:03:51,553 - validators.deployment_validator - INFO - Status updated from draft to deployed_to_temp
2025-06-23 14:03:51,564 - validators.deployment_validator - INFO - Validation completed: 1 errors, 1 warnings
2025-06-23 14:03:51,564 - validators.deployment_validator - INFO - Starting deployment validation for attributes
2025-06-23 14:03:51,570 - validators.deployment_validator - INFO - Status updated from draft to deployed_to_temp
2025-06-23 14:03:51,581 - validators.deployment_validator - INFO - Validation completed: 1 errors, 1 warnings
2025-06-23 14:03:51,581 - validators.deployment_validator - INFO - Starting deployment validation for attributes
2025-06-23 14:03:51,588 - validators.deployment_validator - INFO - Status updated from draft to deployed_to_temp
2025-06-23 14:03:51,599 - validators.deployment_validator - INFO - Validation completed: 1 errors, 1 warnings
2025-06-23 14:03:51,599 - validators.deployment_validator - INFO - Starting deployment validation for attributes
2025-06-23 14:03:51,606 - validators.deployment_validator - INFO - Status updated from draft to deployed_to_temp
2025-06-23 14:03:51,618 - validators.deployment_validator - INFO - Validation completed: 1 errors, 1 warnings
2025-06-23 14:03:51,618 - validators.deployment_validator - INFO - Starting deployment validation for attributes
2025-06-23 14:03:51,625 - validators.deployment_validator - INFO - Status updated from draft to deployed_to_temp
2025-06-23 14:03:51,636 - validators.deployment_validator - INFO - Validation completed: 1 errors, 1 warnings
2025-06-23 14:03:51,639 - validators.deployment_validator - INFO - Starting deployment validation for entity_attributes
2025-06-23 14:03:51,645 - validators.deployment_validator - INFO - Status updated from draft to deployed_to_temp
2025-06-23 14:03:51,662 - validators.deployment_validator - INFO - Validation completed: 2 errors, 0 warnings
2025-06-23 14:03:51,662 - validators.deployment_validator - INFO - Starting deployment validation for entity_attributes
2025-06-23 14:03:51,669 - validators.deployment_validator - INFO - Status updated from draft to deployed_to_temp
2025-06-23 14:03:51,684 - validators.deployment_validator - INFO - Validation completed: 2 errors, 0 warnings
2025-06-23 14:03:51,684 - validators.deployment_validator - INFO - Starting deployment validation for entity_attributes
2025-06-23 14:03:51,689 - validators.deployment_validator - INFO - Status updated from draft to deployed_to_temp
2025-06-23 14:03:51,707 - validators.deployment_validator - INFO - Validation completed: 2 errors, 0 warnings
2025-06-23 14:03:51,707 - validators.deployment_validator - INFO - Starting deployment validation for entity_attributes
2025-06-23 14:03:51,714 - validators.deployment_validator - INFO - Status updated from draft to deployed_to_temp
2025-06-23 14:03:51,732 - validators.deployment_validator - INFO - Validation completed: 2 errors, 0 warnings
2025-06-23 14:03:51,734 - validators.deployment_validator - INFO - Starting deployment validation for system_permissions
2025-06-23 14:03:51,734 - validators.deployment_validator - INFO - Status updated from draft to deployed_to_temp
2025-06-23 14:03:51,734 - validators.deployment_validator - INFO - Added is_synthetic field with default value False
2025-06-23 14:03:51,734 - validators.deployment_validator - INFO - Validation completed: 0 errors, 0 warnings
2025-06-23 14:03:51,761 - validators.deployment_validator - INFO - Starting deployment validation for entity_relationships
2025-06-23 14:03:51,788 - validators.deployment_validator - INFO - Status updated from draft to deployed_to_temp
2025-06-23 14:03:51,798 - validators.deployment_validator - INFO - Validation completed: 3 errors, 1 warnings
2025-06-23 14:09:00,708 - validators.deployment_validator - INFO - Starting deployment validation for entity_relationships
2025-06-23 14:09:00,733 - validators.deployment_validator - INFO - Status updated from draft to deployed_to_temp
2025-06-23 14:09:00,743 - validators.deployment_validator - INFO - Validation completed: 3 errors, 1 warnings
2025-06-23 14:09:00,744 - validators.deployment_validator - INFO - Validation results logged to: /home/<USER>/workflow-system/logs/validation_results_20250623_140900.json
2025-06-23 14:09:02,604 - validators.deployment_validator - INFO - Starting deployment validation for attributes
2025-06-23 14:09:02,612 - validators.deployment_validator - INFO - Status updated from draft to deployed_to_temp
2025-06-23 14:09:02,625 - validators.deployment_validator - INFO - Validation completed: 1 errors, 1 warnings
2025-06-23 14:09:02,625 - validators.deployment_validator - INFO - Starting deployment validation for attributes
2025-06-23 14:09:02,632 - validators.deployment_validator - INFO - Status updated from draft to deployed_to_temp
2025-06-23 14:09:02,643 - validators.deployment_validator - INFO - Validation completed: 1 errors, 1 warnings
2025-06-23 14:09:02,643 - validators.deployment_validator - INFO - Starting deployment validation for attributes
2025-06-23 14:09:02,650 - validators.deployment_validator - INFO - Status updated from draft to deployed_to_temp
2025-06-23 14:09:02,662 - validators.deployment_validator - INFO - Validation completed: 1 errors, 1 warnings
2025-06-23 14:09:02,662 - validators.deployment_validator - INFO - Starting deployment validation for attributes
2025-06-23 14:09:02,669 - validators.deployment_validator - INFO - Status updated from draft to deployed_to_temp
2025-06-23 14:09:02,681 - validators.deployment_validator - INFO - Validation completed: 1 errors, 1 warnings
2025-06-23 14:09:02,681 - validators.deployment_validator - INFO - Starting deployment validation for attributes
2025-06-23 14:09:02,688 - validators.deployment_validator - INFO - Status updated from draft to deployed_to_temp
2025-06-23 14:09:02,699 - validators.deployment_validator - INFO - Validation completed: 1 errors, 1 warnings
2025-06-23 14:09:02,699 - validators.deployment_validator - INFO - Starting deployment validation for attributes
2025-06-23 14:09:02,706 - validators.deployment_validator - INFO - Status updated from draft to deployed_to_temp
2025-06-23 14:09:02,718 - validators.deployment_validator - INFO - Validation completed: 1 errors, 1 warnings
2025-06-23 14:09:02,718 - validators.deployment_validator - INFO - Starting deployment validation for attributes
2025-06-23 14:09:02,723 - validators.deployment_validator - INFO - Status updated from draft to deployed_to_temp
2025-06-23 14:09:02,735 - validators.deployment_validator - INFO - Validation completed: 1 errors, 1 warnings
2025-06-23 14:09:02,735 - validators.deployment_validator - INFO - Starting deployment validation for attributes
2025-06-23 14:09:02,743 - validators.deployment_validator - INFO - Status updated from draft to deployed_to_temp
2025-06-23 14:09:02,752 - validators.deployment_validator - INFO - Validation completed: 1 errors, 1 warnings
2025-06-23 14:09:02,754 - validators.deployment_validator - INFO - Starting deployment validation for entity_attributes
2025-06-23 14:09:02,761 - validators.deployment_validator - INFO - Status updated from draft to deployed_to_temp
2025-06-23 14:09:02,779 - validators.deployment_validator - INFO - Validation completed: 2 errors, 0 warnings
2025-06-23 14:09:02,779 - validators.deployment_validator - INFO - Starting deployment validation for entity_attributes
2025-06-23 14:09:02,786 - validators.deployment_validator - INFO - Status updated from draft to deployed_to_temp
2025-06-23 14:09:02,802 - validators.deployment_validator - INFO - Validation completed: 2 errors, 0 warnings
2025-06-23 14:09:02,802 - validators.deployment_validator - INFO - Starting deployment validation for entity_attributes
2025-06-23 14:09:02,809 - validators.deployment_validator - INFO - Status updated from draft to deployed_to_temp
2025-06-23 14:09:02,826 - validators.deployment_validator - INFO - Validation completed: 2 errors, 0 warnings
2025-06-23 14:09:02,826 - validators.deployment_validator - INFO - Starting deployment validation for entity_attributes
2025-06-23 14:09:02,833 - validators.deployment_validator - INFO - Status updated from draft to deployed_to_temp
2025-06-23 14:09:02,851 - validators.deployment_validator - INFO - Validation completed: 2 errors, 0 warnings
2025-06-23 14:09:02,854 - validators.deployment_validator - INFO - Starting deployment validation for system_permissions
2025-06-23 14:09:02,854 - validators.deployment_validator - INFO - Status updated from draft to deployed_to_temp
2025-06-23 14:09:02,854 - validators.deployment_validator - INFO - Added is_synthetic field with default value False
2025-06-23 14:09:02,854 - validators.deployment_validator - INFO - Validation completed: 0 errors, 0 warnings
2025-06-23 14:09:02,885 - validators.deployment_validator - INFO - Starting deployment validation for entity_relationships
2025-06-23 14:09:02,911 - validators.deployment_validator - INFO - Status updated from draft to deployed_to_temp
2025-06-23 14:09:02,922 - validators.deployment_validator - INFO - Validation completed: 3 errors, 1 warnings
2025-06-23 14:27:25,721 - validators.deployment_validator - INFO - Starting deployment validation for attribute_enum_values
2025-06-23 14:27:25,721 - validators.deployment_validator - INFO - Status updated from draft to deployed_to_temp
2025-06-23 14:27:25,721 - validators.deployment_validator - INFO - Added is_synthetic field with default value False
2025-06-23 14:27:25,721 - validators.deployment_validator - INFO - Validation completed: 0 errors, 0 warnings
2025-06-23 14:27:25,722 - validators.deployment_validator - INFO - Validation results logged to: /home/<USER>/workflow-system/logs/validation_results_20250623_142725.json
2025-06-23 14:28:25,663 - validators.deployment_validator - INFO - Starting deployment validation for attribute_enum_values
2025-06-23 14:28:25,663 - validators.deployment_validator - INFO - Status updated from draft to deployed_to_temp
2025-06-23 14:28:25,663 - validators.deployment_validator - INFO - Added is_synthetic field with default value False
2025-06-23 14:28:25,663 - validators.deployment_validator - INFO - Validation completed: 0 errors, 0 warnings
2025-06-23 14:28:25,664 - validators.deployment_validator - INFO - Validation results logged to: /home/<USER>/workflow-system/logs/validation_results_20250623_142825.json
2025-06-23 14:29:00,131 - validators.deployment_validator - INFO - Starting deployment validation for attribute_enum_values
2025-06-23 14:29:00,131 - validators.deployment_validator - INFO - Status updated from draft to deployed_to_temp
2025-06-23 14:29:00,131 - validators.deployment_validator - INFO - Added is_synthetic field with default value False
2025-06-23 14:29:00,131 - validators.deployment_validator - INFO - Validation completed: 0 errors, 0 warnings
2025-06-23 14:29:00,131 - validators.deployment_validator - INFO - Validation results logged to: /home/<USER>/workflow-system/logs/validation_results_20250623_142900.json
2025-06-23 14:29:24,309 - validators.deployment_validator - INFO - Starting deployment validation for attribute_enum_values
2025-06-23 14:29:24,309 - validators.deployment_validator - INFO - Status updated from draft to deployed_to_temp
2025-06-23 14:29:24,309 - validators.deployment_validator - INFO - Added is_synthetic field with default value False
2025-06-23 14:29:24,309 - validators.deployment_validator - INFO - Validation completed: 0 errors, 0 warnings
2025-06-23 14:29:24,309 - validators.deployment_validator - INFO - Validation results logged to: /home/<USER>/workflow-system/logs/validation_results_20250623_142924.json
2025-06-23 14:32:33,155 - validators.deployment_validator - INFO - Starting deployment validation for entity_relationships
2025-06-23 14:32:33,185 - validators.deployment_validator - INFO - Status updated from draft to deployed_to_temp
2025-06-23 14:32:33,196 - validators.deployment_validator - INFO - Validation completed: 3 errors, 1 warnings
2025-06-23 14:32:33,196 - validators.deployment_validator - INFO - Validation results logged to: /home/<USER>/workflow-system/logs/validation_results_20250623_143233.json
2025-06-23 14:32:34,852 - validators.deployment_validator - INFO - Starting deployment validation for attributes
2025-06-23 14:32:34,860 - validators.deployment_validator - INFO - Status updated from draft to deployed_to_temp
2025-06-23 14:32:34,872 - validators.deployment_validator - INFO - Validation completed: 1 errors, 1 warnings
2025-06-23 14:32:34,872 - validators.deployment_validator - INFO - Starting deployment validation for attributes
2025-06-23 14:32:34,878 - validators.deployment_validator - INFO - Status updated from draft to deployed_to_temp
2025-06-23 14:32:34,888 - validators.deployment_validator - INFO - Validation completed: 1 errors, 1 warnings
2025-06-23 14:32:34,888 - validators.deployment_validator - INFO - Starting deployment validation for attributes
2025-06-23 14:32:34,895 - validators.deployment_validator - INFO - Status updated from draft to deployed_to_temp
2025-06-23 14:32:34,906 - validators.deployment_validator - INFO - Validation completed: 1 errors, 1 warnings
2025-06-23 14:32:34,906 - validators.deployment_validator - INFO - Starting deployment validation for attributes
2025-06-23 14:32:34,913 - validators.deployment_validator - INFO - Status updated from draft to deployed_to_temp
2025-06-23 14:32:34,924 - validators.deployment_validator - INFO - Validation completed: 1 errors, 1 warnings
2025-06-23 14:32:34,924 - validators.deployment_validator - INFO - Starting deployment validation for attributes
2025-06-23 14:32:34,931 - validators.deployment_validator - INFO - Status updated from draft to deployed_to_temp
2025-06-23 14:32:34,942 - validators.deployment_validator - INFO - Validation completed: 1 errors, 1 warnings
2025-06-23 14:32:34,942 - validators.deployment_validator - INFO - Starting deployment validation for attributes
2025-06-23 14:32:34,949 - validators.deployment_validator - INFO - Status updated from draft to deployed_to_temp
2025-06-23 14:32:34,959 - validators.deployment_validator - INFO - Validation completed: 1 errors, 1 warnings
2025-06-23 14:32:34,959 - validators.deployment_validator - INFO - Starting deployment validation for attributes
2025-06-23 14:32:34,965 - validators.deployment_validator - INFO - Status updated from draft to deployed_to_temp
2025-06-23 14:32:34,976 - validators.deployment_validator - INFO - Validation completed: 1 errors, 1 warnings
2025-06-23 14:32:34,977 - validators.deployment_validator - INFO - Starting deployment validation for attributes
2025-06-23 14:32:34,982 - validators.deployment_validator - INFO - Status updated from draft to deployed_to_temp
2025-06-23 14:32:34,992 - validators.deployment_validator - INFO - Validation completed: 1 errors, 1 warnings
2025-06-23 14:32:34,995 - validators.deployment_validator - INFO - Starting deployment validation for entity_attributes
2025-06-23 14:32:35,002 - validators.deployment_validator - INFO - Status updated from draft to deployed_to_temp
2025-06-23 14:32:35,022 - validators.deployment_validator - INFO - Validation completed: 2 errors, 0 warnings
2025-06-23 14:32:35,022 - validators.deployment_validator - INFO - Starting deployment validation for entity_attributes
2025-06-23 14:32:35,029 - validators.deployment_validator - INFO - Status updated from draft to deployed_to_temp
2025-06-23 14:32:35,044 - validators.deployment_validator - INFO - Validation completed: 2 errors, 0 warnings
2025-06-23 14:32:35,044 - validators.deployment_validator - INFO - Starting deployment validation for entity_attributes
2025-06-23 14:32:35,051 - validators.deployment_validator - INFO - Status updated from draft to deployed_to_temp
2025-06-23 14:32:35,069 - validators.deployment_validator - INFO - Validation completed: 2 errors, 0 warnings
2025-06-23 14:32:35,069 - validators.deployment_validator - INFO - Starting deployment validation for entity_attributes
2025-06-23 14:32:35,076 - validators.deployment_validator - INFO - Status updated from draft to deployed_to_temp
2025-06-23 14:32:35,092 - validators.deployment_validator - INFO - Validation completed: 2 errors, 0 warnings
2025-06-23 14:32:35,093 - validators.deployment_validator - INFO - Starting deployment validation for system_permissions
2025-06-23 14:32:35,093 - validators.deployment_validator - INFO - Status updated from draft to deployed_to_temp
2025-06-23 14:32:35,093 - validators.deployment_validator - INFO - Added is_synthetic field with default value False
2025-06-23 14:32:35,093 - validators.deployment_validator - INFO - Validation completed: 0 errors, 0 warnings
2025-06-23 14:32:35,116 - validators.deployment_validator - INFO - Starting deployment validation for entity_relationships
2025-06-23 14:32:35,138 - validators.deployment_validator - INFO - Status updated from draft to deployed_to_temp
2025-06-23 14:32:35,148 - validators.deployment_validator - INFO - Validation completed: 3 errors, 1 warnings
2025-06-23 14:35:16,710 - validators.deployment_validator - INFO - Starting deployment validation for attribute_enum_values
2025-06-23 14:35:16,710 - validators.deployment_validator - INFO - Status updated from draft to deployed_to_temp
2025-06-23 14:35:16,710 - validators.deployment_validator - INFO - Added is_synthetic field with default value False
2025-06-23 14:35:16,710 - validators.deployment_validator - INFO - Validation completed: 0 errors, 0 warnings
2025-06-23 14:35:16,710 - validators.deployment_validator - INFO - Validation results logged to: /home/<USER>/workflow-system/logs/validation_results_20250623_143516.json
2025-06-23 14:35:40,371 - validators.deployment_validator - INFO - Starting deployment validation for attribute_enum_values
2025-06-23 14:35:40,371 - validators.deployment_validator - INFO - Status updated from draft to deployed_to_temp
2025-06-23 14:35:40,371 - validators.deployment_validator - INFO - Added is_synthetic field with default value False
2025-06-23 14:35:40,371 - validators.deployment_validator - INFO - Validation completed: 0 errors, 0 warnings
2025-06-23 14:35:40,372 - validators.deployment_validator - INFO - Validation results logged to: /home/<USER>/workflow-system/logs/validation_results_20250623_143540.json
2025-06-23 14:38:52,580 - validators.deployment_validator - INFO - Starting deployment validation for entity_relationships
2025-06-23 14:38:52,611 - validators.deployment_validator - INFO - Status updated from draft to deployed_to_temp
2025-06-23 14:38:52,621 - validators.deployment_validator - INFO - Validation completed: 3 errors, 1 warnings
2025-06-23 14:38:52,622 - validators.deployment_validator - INFO - Validation results logged to: /home/<USER>/workflow-system/logs/validation_results_20250623_143852.json
2025-06-23 14:38:54,233 - validators.deployment_validator - INFO - Starting deployment validation for attributes
2025-06-23 14:38:54,241 - validators.deployment_validator - INFO - Status updated from draft to deployed_to_temp
2025-06-23 14:38:54,252 - validators.deployment_validator - INFO - Validation completed: 1 errors, 1 warnings
2025-06-23 14:38:54,252 - validators.deployment_validator - INFO - Starting deployment validation for attributes
2025-06-23 14:38:54,259 - validators.deployment_validator - INFO - Status updated from draft to deployed_to_temp
2025-06-23 14:38:54,271 - validators.deployment_validator - INFO - Validation completed: 1 errors, 1 warnings
2025-06-23 14:38:54,271 - validators.deployment_validator - INFO - Starting deployment validation for attributes
2025-06-23 14:38:54,277 - validators.deployment_validator - INFO - Status updated from draft to deployed_to_temp
2025-06-23 14:38:54,289 - validators.deployment_validator - INFO - Validation completed: 1 errors, 1 warnings
2025-06-23 14:38:54,289 - validators.deployment_validator - INFO - Starting deployment validation for attributes
2025-06-23 14:38:54,294 - validators.deployment_validator - INFO - Status updated from draft to deployed_to_temp
2025-06-23 14:38:54,306 - validators.deployment_validator - INFO - Validation completed: 1 errors, 1 warnings
2025-06-23 14:38:54,306 - validators.deployment_validator - INFO - Starting deployment validation for attributes
2025-06-23 14:38:54,313 - validators.deployment_validator - INFO - Status updated from draft to deployed_to_temp
2025-06-23 14:38:54,323 - validators.deployment_validator - INFO - Validation completed: 1 errors, 1 warnings
2025-06-23 14:38:54,323 - validators.deployment_validator - INFO - Starting deployment validation for attributes
2025-06-23 14:38:54,328 - validators.deployment_validator - INFO - Status updated from draft to deployed_to_temp
2025-06-23 14:38:54,339 - validators.deployment_validator - INFO - Validation completed: 1 errors, 1 warnings
2025-06-23 14:38:54,339 - validators.deployment_validator - INFO - Starting deployment validation for attributes
2025-06-23 14:38:54,344 - validators.deployment_validator - INFO - Status updated from draft to deployed_to_temp
2025-06-23 14:38:54,355 - validators.deployment_validator - INFO - Validation completed: 1 errors, 1 warnings
2025-06-23 14:38:54,355 - validators.deployment_validator - INFO - Starting deployment validation for attributes
2025-06-23 14:38:54,360 - validators.deployment_validator - INFO - Status updated from draft to deployed_to_temp
2025-06-23 14:38:54,372 - validators.deployment_validator - INFO - Validation completed: 1 errors, 1 warnings
2025-06-23 14:38:54,375 - validators.deployment_validator - INFO - Starting deployment validation for entity_attributes
2025-06-23 14:38:54,382 - validators.deployment_validator - INFO - Status updated from draft to deployed_to_temp
2025-06-23 14:38:54,399 - validators.deployment_validator - INFO - Validation completed: 2 errors, 0 warnings
2025-06-23 14:38:54,399 - validators.deployment_validator - INFO - Starting deployment validation for entity_attributes
2025-06-23 14:38:54,404 - validators.deployment_validator - INFO - Status updated from draft to deployed_to_temp
2025-06-23 14:38:54,418 - validators.deployment_validator - INFO - Validation completed: 2 errors, 0 warnings
2025-06-23 14:38:54,418 - validators.deployment_validator - INFO - Starting deployment validation for entity_attributes
2025-06-23 14:38:54,425 - validators.deployment_validator - INFO - Status updated from draft to deployed_to_temp
2025-06-23 14:38:54,440 - validators.deployment_validator - INFO - Validation completed: 2 errors, 0 warnings
2025-06-23 14:38:54,440 - validators.deployment_validator - INFO - Starting deployment validation for entity_attributes
2025-06-23 14:38:54,445 - validators.deployment_validator - INFO - Status updated from draft to deployed_to_temp
2025-06-23 14:38:54,462 - validators.deployment_validator - INFO - Validation completed: 2 errors, 0 warnings
2025-06-23 14:38:54,465 - validators.deployment_validator - INFO - Starting deployment validation for system_permissions
2025-06-23 14:38:54,465 - validators.deployment_validator - INFO - Status updated from draft to deployed_to_temp
2025-06-23 14:38:54,465 - validators.deployment_validator - INFO - Added is_synthetic field with default value False
2025-06-23 14:38:54,465 - validators.deployment_validator - INFO - Validation completed: 0 errors, 0 warnings
2025-06-23 14:38:54,491 - validators.deployment_validator - INFO - Starting deployment validation for entity_relationships
2025-06-23 14:38:54,515 - validators.deployment_validator - INFO - Status updated from draft to deployed_to_temp
2025-06-23 14:38:54,524 - validators.deployment_validator - INFO - Validation completed: 3 errors, 1 warnings
2025-06-24 04:45:02,177 - validators.deployment_validator - INFO - Starting deployment validation for entity_relationships
2025-06-24 04:45:02,204 - validators.deployment_validator - INFO - Status updated from draft to deployed_to_temp
2025-06-24 04:45:02,213 - validators.deployment_validator - INFO - Validation completed: 3 errors, 1 warnings
2025-06-24 04:45:02,214 - validators.deployment_validator - INFO - Validation results logged to: /home/<USER>/workflow-system/logs/validation_results_20250624_044502.json
2025-06-24 04:45:03,875 - validators.deployment_validator - INFO - Starting deployment validation for attributes
2025-06-24 04:45:03,882 - validators.deployment_validator - INFO - Status updated from draft to deployed_to_temp
2025-06-24 04:45:03,895 - validators.deployment_validator - INFO - Validation completed: 1 errors, 1 warnings
2025-06-24 04:45:03,895 - validators.deployment_validator - INFO - Starting deployment validation for attributes
2025-06-24 04:45:03,900 - validators.deployment_validator - INFO - Status updated from draft to deployed_to_temp
2025-06-24 04:45:03,911 - validators.deployment_validator - INFO - Validation completed: 1 errors, 1 warnings
2025-06-24 04:45:03,911 - validators.deployment_validator - INFO - Starting deployment validation for attributes
2025-06-24 04:45:03,919 - validators.deployment_validator - INFO - Status updated from draft to deployed_to_temp
2025-06-24 04:45:03,930 - validators.deployment_validator - INFO - Validation completed: 1 errors, 1 warnings
2025-06-24 04:45:03,931 - validators.deployment_validator - INFO - Starting deployment validation for attributes
2025-06-24 04:45:03,938 - validators.deployment_validator - INFO - Status updated from draft to deployed_to_temp
2025-06-24 04:45:03,948 - validators.deployment_validator - INFO - Validation completed: 1 errors, 1 warnings
2025-06-24 04:45:03,948 - validators.deployment_validator - INFO - Starting deployment validation for attributes
2025-06-24 04:45:03,956 - validators.deployment_validator - INFO - Status updated from draft to deployed_to_temp
2025-06-24 04:45:03,966 - validators.deployment_validator - INFO - Validation completed: 1 errors, 1 warnings
2025-06-24 04:45:03,966 - validators.deployment_validator - INFO - Starting deployment validation for attributes
2025-06-24 04:45:03,974 - validators.deployment_validator - INFO - Status updated from draft to deployed_to_temp
2025-06-24 04:45:03,985 - validators.deployment_validator - INFO - Validation completed: 1 errors, 1 warnings
2025-06-24 04:45:03,985 - validators.deployment_validator - INFO - Starting deployment validation for attributes
2025-06-24 04:45:03,992 - validators.deployment_validator - INFO - Status updated from draft to deployed_to_temp
2025-06-24 04:45:04,003 - validators.deployment_validator - INFO - Validation completed: 1 errors, 1 warnings
2025-06-24 04:45:04,003 - validators.deployment_validator - INFO - Starting deployment validation for attributes
2025-06-24 04:45:04,010 - validators.deployment_validator - INFO - Status updated from draft to deployed_to_temp
2025-06-24 04:45:04,021 - validators.deployment_validator - INFO - Validation completed: 1 errors, 1 warnings
2025-06-24 04:45:04,023 - validators.deployment_validator - INFO - Starting deployment validation for entity_attributes
2025-06-24 04:45:04,031 - validators.deployment_validator - INFO - Status updated from draft to deployed_to_temp
2025-06-24 04:45:04,048 - validators.deployment_validator - INFO - Validation completed: 2 errors, 0 warnings
2025-06-24 04:45:04,048 - validators.deployment_validator - INFO - Starting deployment validation for entity_attributes
2025-06-24 04:45:04,054 - validators.deployment_validator - INFO - Status updated from draft to deployed_to_temp
2025-06-24 04:45:04,072 - validators.deployment_validator - INFO - Validation completed: 2 errors, 0 warnings
2025-06-24 04:45:04,072 - validators.deployment_validator - INFO - Starting deployment validation for entity_attributes
2025-06-24 04:45:04,080 - validators.deployment_validator - INFO - Status updated from draft to deployed_to_temp
2025-06-24 04:45:04,098 - validators.deployment_validator - INFO - Validation completed: 2 errors, 0 warnings
2025-06-24 04:45:04,098 - validators.deployment_validator - INFO - Starting deployment validation for entity_attributes
2025-06-24 04:45:04,105 - validators.deployment_validator - INFO - Status updated from draft to deployed_to_temp
2025-06-24 04:45:04,123 - validators.deployment_validator - INFO - Validation completed: 2 errors, 0 warnings
2025-06-24 04:45:04,126 - validators.deployment_validator - INFO - Starting deployment validation for system_permissions
2025-06-24 04:45:04,126 - validators.deployment_validator - INFO - Status updated from draft to deployed_to_temp
2025-06-24 04:45:04,126 - validators.deployment_validator - INFO - Added is_synthetic field with default value False
2025-06-24 04:45:04,126 - validators.deployment_validator - INFO - Validation completed: 0 errors, 0 warnings
2025-06-24 04:45:04,155 - validators.deployment_validator - INFO - Starting deployment validation for entity_relationships
2025-06-24 04:45:04,180 - validators.deployment_validator - INFO - Status updated from draft to deployed_to_temp
2025-06-24 04:45:04,189 - validators.deployment_validator - INFO - Validation completed: 3 errors, 1 warnings
2025-06-24 04:53:53,211 - validators.deployment_validator - INFO - Starting deployment validation for entities
2025-06-24 04:53:53,220 - validators.deployment_validator - INFO - Validation field set for entities: version=2
2025-06-24 04:53:53,239 - validators.deployment_validator - INFO - Entity type validation passed: master
2025-06-24 04:53:53,239 - validators.deployment_validator - INFO - Added is_synthetic field with default value False
2025-06-24 04:53:53,247 - validators.deployment_validator - INFO - Added supports_multivalue field with default value False
2025-06-24 04:53:53,254 - validators.deployment_validator - INFO - Validation completed: 1 errors, 2 warnings
2025-06-24 04:53:53,254 - validators.deployment_validator - INFO - Validation results logged to: /home/<USER>/workflow-system/logs/validation_results_20250624_045353.json
2025-06-24 04:54:07,775 - validators.deployment_validator - INFO - Starting deployment validation for entities
2025-06-24 04:54:07,782 - validators.deployment_validator - INFO - Validation field set for entities: version=4
2025-06-24 04:54:07,802 - validators.deployment_validator - INFO - Entity type validation passed: master
2025-06-24 04:54:07,802 - validators.deployment_validator - INFO - Added is_synthetic field with default value False
2025-06-24 04:54:07,809 - validators.deployment_validator - INFO - Added supports_multivalue field with default value False
2025-06-24 04:54:07,816 - validators.deployment_validator - INFO - Validation completed: 1 errors, 2 warnings
2025-06-24 04:54:07,817 - validators.deployment_validator - INFO - Validation results logged to: /home/<USER>/workflow-system/logs/validation_results_20250624_045407.json
2025-06-24 09:34:46,064 - validators.deployment_validator - INFO - Starting deployment validation for entities
2025-06-24 09:34:46,073 - validators.deployment_validator - INFO - Validation field set for entities: version=4
2025-06-24 09:34:46,094 - validators.deployment_validator - INFO - Entity type validation passed: master
2025-06-24 09:34:46,094 - validators.deployment_validator - INFO - Added is_synthetic field with default value False
2025-06-24 09:34:46,102 - validators.deployment_validator - INFO - Added supports_multivalue field with default value False
2025-06-24 09:34:46,110 - validators.deployment_validator - INFO - Validation completed: 1 errors, 2 warnings
2025-06-24 09:34:46,110 - validators.deployment_validator - INFO - Validation results logged to: /home/<USER>/workflow-system/logs/validation_results_20250624_093446.json
