import os
import sys
import logging
import json
from parsers.entity_parser import parse_entities
from deployers.entity_deployer_v2 import deploy_entities

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger('test_relationship_properties')

def test_relationship_properties():
    """
    Test that relationship properties are correctly parsed and deployed to the database.
    """
    # Read the sample entity output file
    sample_file_path = 'samples/sample_entity_output2.txt'
    with open(sample_file_path, 'r') as f:
        entity_def = f.read()

    # Print the entity definition
    logger.info("Entity definition:")
    logger.info(entity_def[:500] + "..." if len(entity_def) > 500 else entity_def)
    
    # Parse the entity definition
    entities_data, warnings = parse_entities(entity_def)
    
    # Print the parsed entities
    logger.info("Parsed entities:")
    logger.info(json.dumps(entities_data, indent=2))
    
    # Check if relationships were parsed correctly
    if 'entities' in entities_data and 'Employee' in entities_data['entities']:
        employee_entity = entities_data['entities']['Employee']
        if 'relationships' in employee_entity:
            logger.info("Employee relationships:")
            for rel_name, rel_def in employee_entity['relationships'].items():
                logger.info(f"  - Relationship: {rel_name}")
                logger.info(f"    - Type: {rel_def.get('type', 'N/A')}")
                logger.info(f"    - Entity: {rel_def.get('entity', 'N/A')}")
                logger.info(f"    - Source Attribute: {rel_def.get('source_attribute', 'N/A')}")
                logger.info(f"    - Target Attribute: {rel_def.get('target_attribute', 'N/A')}")
                
                if 'properties' in rel_def:
                    logger.info(f"    - Properties:")
                    for prop_name, prop_value in rel_def['properties'].items():
                        logger.info(f"      - {prop_name}: {prop_value}")
                else:
                    logger.info(f"    - No properties found")
        else:
            logger.error("No relationships found in Employee entity")
    else:
        logger.error("Employee entity not found in parsed data")
    
    # Print any warnings
    if warnings:
        logger.warning("Warnings:")
        for warning in warnings:
            logger.warning(f"  - {warning}")
    
    # Deploy the entities to the database
    schema_name = 'workflow_temp'
    success, messages = deploy_entities(entities_data, schema_name)
    
    # Print the deployment messages
    logger.info("Deployment messages:")
    for message in messages:
        logger.info(f"  - {message}")
    
    # Check if deployment was successful
    if success:
        logger.info("Deployment successful")
    else:
        logger.error("Deployment failed")
    
    # Check if the relationship properties were correctly stored in the database
    import psycopg2
    
    # Database connection parameters
    db_host = os.environ.get('DB_HOST', '**********')
    db_port = os.environ.get('DB_PORT', '5432')
    db_name = os.environ.get('DB_NAME', 'workflow_system')
    db_user = os.environ.get('DB_USER', 'postgres')
    db_password = os.environ.get('DB_PASSWORD', 'workflow_postgres_secure_password')
    
    # Connect to the database
    conn = psycopg2.connect(
        host=db_host,
        port=db_port,
        dbname=db_name,
        user=db_user,
        password=db_password
    )
    
    # Set schema search path
    with conn.cursor() as cursor:
        cursor.execute(f"SET search_path TO {schema_name}")
        conn.commit()
    
    # Query to check the entity_relationships table
    query = """
    SELECT 
        er.id, 
        s.name as source_entity, 
        t.name as target_entity, 
        er.relationship_type, 
        sa.name as source_attribute, 
        ta.name as target_attribute,
        er.on_delete,
        er.on_update,
        er.foreign_key_type
    FROM 
        entity_relationships er
        JOIN entities s ON er.source_entity_id = s.entity_id
        JOIN entities t ON er.target_entity_id = t.entity_id
        JOIN entity_attributes sa ON er.source_attribute_id = sa.attribute_id
        JOIN entity_attributes ta ON er.target_attribute_id = ta.attribute_id
    WHERE 
        s.name = 'Employee' AND t.name = 'Department'
    """
    
    # Execute the query
    with conn.cursor() as cursor:
        cursor.execute(query)
        result = cursor.fetchall()
    
    # Print the result
    if result:
        logger.info("Employee to Department relationship in the database:")
        for row in result:
            logger.info(f"  - ID: {row[0]}")
            logger.info(f"  - Source Entity: {row[1]}")
            logger.info(f"  - Target Entity: {row[2]}")
            logger.info(f"  - Relationship Type: {row[3]}")
            logger.info(f"  - Source Attribute: {row[4]}")
            logger.info(f"  - Target Attribute: {row[5]}")
            logger.info(f"  - ON DELETE: {row[6]}")
            logger.info(f"  - ON UPDATE: {row[7]}")
            logger.info(f"  - Foreign Key Type: {row[8]}")
    else:
        logger.error("No Employee to Department relationship found in the database.")
    
    # Close the connection
    conn.close()

if __name__ == "__main__":
    test_relationship_properties()
