#!/usr/bin/env python3
"""
Test script for the GO parser.

This script reads a sample GO file, parses it using the GO parser, and outputs the resulting JSON.
It can also compare the output with an expected JSON structure.
"""

import os
import sys
import json
import logging
from typing import Dict, Any

# Add the project root to the Python path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Import the GO parser
from nl_prescriptive_parser.src.parsers.go.parser import GOParser

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def parse_go_file(input_file: str, output_file: str) -> bool:
    """
    Parse a GO file and output the resulting JSON.
    
    Args:
        input_file: Path to the input GO file
        output_file: Path to the output JSON file
        
    Returns:
        True if parsing was successful, False otherwise
    """
    try:
        logger.info(f"Parsing GO file: {input_file}")
        
        # Create a GO parser
        parser = GOParser()
        
        # Parse the GO file
        success, result, messages = parser.parse_file(input_file)
        
        # Log messages
        for message in messages:
            logger.info(f"Parser message: {message}")
        
        if not success:
            logger.error("Parsing failed")
            return False
        
        # Write the result to the output file
        with open(output_file, 'w') as f:
            json.dump(result, f, indent=2)
        
        logger.info(f"Parsing successful, output written to: {output_file}")
        return True
    
    except Exception as e:
        logger.error(f"Error parsing GO file: {str(e)}", exc_info=True)
        return False

def compare_json_files(file1: str, file2: str) -> bool:
    """
    Compare two JSON files.
    
    Args:
        file1: Path to the first JSON file
        file2: Path to the second JSON file
        
    Returns:
        True if the files are equivalent, False otherwise
    """
    try:
        logger.info(f"Comparing JSON files: {file1} and {file2}")
        
        # Read the first file
        with open(file1, 'r') as f:
            json1 = json.load(f)
        
        # Read the second file
        with open(file2, 'r') as f:
            json2 = json.load(f)
        
        # Compare the JSON structures
        if json1 == json2:
            logger.info("JSON files are equivalent")
            return True
        else:
            logger.info("JSON files are different")
            
            # Find differences
            find_differences(json1, json2)
            
            return False
    
    except Exception as e:
        logger.error(f"Error comparing JSON files: {str(e)}", exc_info=True)
        return False

def find_differences(json1: Dict[str, Any], json2: Dict[str, Any], path: str = "") -> None:
    """
    Find differences between two JSON structures.
    
    Args:
        json1: First JSON structure
        json2: Second JSON structure
        path: Current path in the JSON structure
    """
    if isinstance(json1, dict) and isinstance(json2, dict):
        # Check for keys in json1 that are not in json2
        for key in json1:
            if key not in json2:
                logger.info(f"Key {path}.{key} is in file1 but not in file2")
            else:
                find_differences(json1[key], json2[key], f"{path}.{key}")
        
        # Check for keys in json2 that are not in json1
        for key in json2:
            if key not in json1:
                logger.info(f"Key {path}.{key} is in file2 but not in file1")
    
    elif isinstance(json1, list) and isinstance(json2, list):
        # Check if the lists have the same length
        if len(json1) != len(json2):
            logger.info(f"List {path} has different lengths: {len(json1)} vs {len(json2)}")
        
        # Compare list items
        for i in range(min(len(json1), len(json2))):
            find_differences(json1[i], json2[i], f"{path}[{i}]")
    
    elif json1 != json2:
        logger.info(f"Value at {path} is different: {json1} vs {json2}")

def main():
    """Main function."""
    # Define input and output files
    input_file = "nl-prescriptive-parser/samples/sample_go_output.txt"
    output_file = "nl-prescriptive-parser/samples/parsed_go_output.json"
    expected_file = "nl-prescriptive-parser/samples/go_json_structure.json"
    
    # Parse the GO file
    if not parse_go_file(input_file, output_file):
        logger.error("Parsing failed")
        return 1
    
    # Compare with expected output
    if not compare_json_files(output_file, expected_file):
        logger.warning("Output differs from expected structure")
        # This is not necessarily an error, as the parser might produce a valid but different structure
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
