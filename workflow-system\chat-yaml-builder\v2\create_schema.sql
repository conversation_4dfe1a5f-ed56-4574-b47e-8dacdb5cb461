-- Schema creation script for YAML Builder v2
-- This script creates the necessary tables for entity deployment

-- Create schema if it doesn't exist
CREATE SCHEMA IF NOT EXISTS workflow_temp;

-- Entities table
CREATE TABLE IF NOT EXISTS workflow_temp.entities (
    entity_id VARCHAR(50) PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    metadata JSONB,
    lifecycle_management JSONB,
    version_type VARCHAR(10) DEFAULT 'v2',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by VARCHAR(100) DEFAULT 'system',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_by VARCHAR(100) DEFAULT 'system'
);

-- Entity attributes table
CREATE TABLE IF NOT EXISTS workflow_temp.entity_attributes (
    attribute_id VARCHAR(100) PRIMARY KEY,
    entity_id VARCHAR(50) REFERENCES workflow_temp.entities(entity_id),
    name <PERSON><PERSON><PERSON><PERSON>(100) NOT NULL,
    type VARCHAR(50) NOT NULL,
    required B<PERSON>OLEA<PERSON> DEFAULT FALSE,
    default_value TEXT,
    calculated_field BOOLEAN DEFAULT FALSE,
    calculation_formula TEXT,
    dependencies JSONB,
    display_name VARCHAR(100),
    datatype VARCHAR(50) DEFAULT 'string',
    status VARCHAR(20) DEFAULT 'active',
    metadata JSONB,
    format VARCHAR(255),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by VARCHAR(100) DEFAULT 'system',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_by VARCHAR(100) DEFAULT 'system'
);

-- Entity relationships table
CREATE TABLE IF NOT EXISTS workflow_temp.entity_relationships (
    id SERIAL PRIMARY KEY,
    source_entity_id VARCHAR(50) REFERENCES workflow_temp.entities(entity_id),
    target_entity_id VARCHAR(50) REFERENCES workflow_temp.entities(entity_id),
    relationship_type VARCHAR(50) NOT NULL,
    source_attribute_id VARCHAR(100) REFERENCES workflow_temp.entity_attributes(attribute_id),
    target_attribute_id VARCHAR(100) REFERENCES workflow_temp.entity_attributes(attribute_id),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by VARCHAR(100) DEFAULT 'system',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_by VARCHAR(100) DEFAULT 'system'
);

-- Entity attribute metadata table
CREATE TABLE IF NOT EXISTS workflow_temp.entity_attribute_metadata (
    id SERIAL PRIMARY KEY,
    entity_id VARCHAR(50) REFERENCES workflow_temp.entities(entity_id),
    attribute_id VARCHAR(100) REFERENCES workflow_temp.entity_attributes(attribute_id),
    attribute_name VARCHAR(100) NOT NULL,
    required BOOLEAN DEFAULT FALSE,
    display_name VARCHAR(100),
    format VARCHAR(255),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by VARCHAR(100) DEFAULT 'system',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_by VARCHAR(100) DEFAULT 'system',
    UNIQUE(entity_id, attribute_id)
);

-- Attribute enum values table
CREATE TABLE IF NOT EXISTS workflow_temp.attribute_enum_values (
    id SERIAL PRIMARY KEY,
    attribute_id VARCHAR(100) REFERENCES workflow_temp.entity_attributes(attribute_id),
    value VARCHAR(100) NOT NULL,
    display_name VARCHAR(100),
    sort_order INTEGER DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by VARCHAR(100) DEFAULT 'system',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_by VARCHAR(100) DEFAULT 'system',
    UNIQUE(attribute_id, value)
);

-- Attribute validations table
CREATE TABLE IF NOT EXISTS workflow_temp.attribute_validations (
    id SERIAL PRIMARY KEY,
    attribute_id VARCHAR(100) REFERENCES workflow_temp.entity_attributes(attribute_id),
    validation_name VARCHAR(100) NOT NULL,
    validation_type VARCHAR(50) NOT NULL,
    validation_expression TEXT,
    error_message TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by VARCHAR(100) DEFAULT 'system',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_by VARCHAR(100) DEFAULT 'system',
    UNIQUE(attribute_id, validation_name)
);

-- Calculated fields table
CREATE TABLE IF NOT EXISTS workflow_temp.calculated_fields (
    field_id VARCHAR(100) PRIMARY KEY,
    attribute_id VARCHAR(100) REFERENCES workflow_temp.entity_attributes(attribute_id),
    formula TEXT NOT NULL,
    logic_layer VARCHAR(50) DEFAULT 'Application',
    caching VARCHAR(50) DEFAULT 'None',
    dependencies JSONB,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by VARCHAR(100) DEFAULT 'system',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_by VARCHAR(100) DEFAULT 'system'
);

-- Entity business rules table
CREATE TABLE IF NOT EXISTS workflow_temp.entity_business_rules (
    rule_id VARCHAR(100) PRIMARY KEY,
    entity_id VARCHAR(50) REFERENCES workflow_temp.entities(entity_id),
    name VARCHAR(100) NOT NULL,
    description TEXT,
    condition TEXT,
    action TEXT,
    priority INTEGER DEFAULT 0,
    active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by VARCHAR(100) DEFAULT 'system',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_by VARCHAR(100) DEFAULT 'system'
);

-- Entity lifecycle management table
CREATE TABLE IF NOT EXISTS workflow_temp.entity_lifecycle_management (
    id SERIAL PRIMARY KEY,
    entity_id VARCHAR(50) REFERENCES workflow_temp.entities(entity_id),
    management_type VARCHAR(50) NOT NULL,
    trigger_type VARCHAR(50),
    criteria TEXT,
    retention VARCHAR(100),
    storage VARCHAR(100),
    access_pattern TEXT,
    restoration TEXT,
    tracked_attributes JSONB,
    tracking_method VARCHAR(50),
    granularity VARCHAR(50),
    access_control TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by VARCHAR(100) DEFAULT 'system',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_by VARCHAR(100) DEFAULT 'system',
    UNIQUE(entity_id, management_type)
);
