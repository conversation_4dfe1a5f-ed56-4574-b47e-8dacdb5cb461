import yaml

def parse_yaml(data, level=0):
    """
    Recursively parses a YAML structure and prints each node.
    :param data: YAML dictionary or list
    :param level: Indentation level for hierarchy display
    """
    indent = "  " * level  # Indentation for readability
    if isinstance(data, dict):
        for key, value in data.items():
            print(f"{indent}- {key}:")
            parse_yaml(value, level + 1)  # Recursively parse child nodes
    elif isinstance(data, list):
        for index, item in enumerate(data):
            print(f"{indent}- Item {index + 1}:")
            parse_yaml(item, level + 1)
    else:
        print(f"{indent}- Value: {data}")  # Base case for primitive values

def validate_yaml_structure(yaml_data):
    """
    Validates essential fields in YAML structure.
    :param yaml_data: Parsed YAML dictionary
    """
    required_fields = ["tenant", "permission_types", "entities", "global_objectives"]
    for field in required_fields:
        if field not in yaml_data:
            print(f"⚠️ WARNING: Missing key '{field}' in YAML structure.")

def load_yaml(file_path):
    """
    Loads YAML from a file and parses it.
    :param file_path: Path to the YAML file
    :return: Parsed YAML dictionary
    """
    try:
        with open(file_path, "r", encoding="utf-8") as file:
            yaml_data = yaml.safe_load(file)
        print("\n✅ YAML successfully loaded!\n")
        validate_yaml_structure(yaml_data)
        print("\n🔍 Parsed YAML Nodes:\n")
        parse_yaml(yaml_data)  # Start recursive parsing
        return yaml_data
    except yaml.YAMLError as e:
        print(f"❌ YAML Error: {e}")
    except Exception as e:
        print(f"❌ Error loading YAML: {e}")

# Run the parser
yaml_file = "workflow.yaml"  # Replace with your YAML file path
yaml_data = load_yaml(yaml_file)
