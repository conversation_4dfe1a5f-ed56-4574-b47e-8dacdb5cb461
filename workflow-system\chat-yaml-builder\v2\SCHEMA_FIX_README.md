# Schema Fix for YAML Builder v2

This document explains how to fix the schema issues in YAML Builder v2 where tables like `lo_input_stack`, `lo_input_items`, `lo_output_stack`, etc. are not being properly populated.

## Problem Description

When parsing prescriptive sentences in YAML Builder v2, the following issues were identified:

1. The `lo_input_items` table is missing in the `workflow_temp` schema while it exists in the `workflow_runtime` schema.
2. The data mapping stacks are not getting populated.
3. Other related tables like `lo_input_stack`, `lo_output_stack`, `lo_output_items`, etc. may also be missing or not properly populated.

These issues prevent the runtime from working correctly since it relies on these tables to store and retrieve data for Local Objectives (LOs).

## Root Cause

The root cause of the issue is that the schema synchronization between `workflow_runtime` and `workflow_temp` is incomplete. The `create_temp_schema` function in `db_utils.py` creates a basic set of tables, but it doesn't include all the tables needed for the v2 implementation, particularly the stack-related tables.

## Solution

Two scripts have been created to fix the issue:

1. `fix_schema_issues.py`: This script creates any missing tables in the specified schema with the correct structure.
2. `test_schema_fix.py`: This script tests that the fix resolves the issue by deploying a sample LO and verifying that the tables are populated correctly.

## How to Use

### Using the Combined Script

The easiest way to fix the schema issues is to use the `run_schema_fix.py` script, which runs both the fix and test scripts in sequence:

```bash
cd chat-yaml-builder/v2
python run_schema_fix.py --schema workflow_temp
```

This will:
1. Run the `fix_schema_issues.py` script to create any missing tables
2. Run the `test_schema_fix.py` script to deploy a sample LO and verify that the tables are populated correctly

You can also run only the fix or only the test:

```bash
# Run only the fix
python run_schema_fix.py --schema workflow_temp --fix-only

# Run only the test
python run_schema_fix.py --schema workflow_temp --test-only

# Only verify the schema without fixing issues
python run_schema_fix.py --schema workflow_temp --verify-only
```

### Fix Schema Issues Manually

If you prefer to run the scripts individually, you can run the `fix_schema_issues.py` script:

```bash
cd chat-yaml-builder/v2
python fix_schema_issues.py --schema workflow_temp
```

This will create any missing tables in the `workflow_temp` schema. You can also specify a different schema if needed:

```bash
python fix_schema_issues.py --schema workflow_runtime
```

If you just want to verify the schema without fixing issues:

```bash
python fix_schema_issues.py --schema workflow_temp --verify-only
```

### Test the Fix Manually

To test that the fix resolves the issue, run the `test_schema_fix.py` script:

```bash
cd chat-yaml-builder/v2
python test_schema_fix.py --schema workflow_temp
```

This will:
1. Run the `fix_schema_issues.py` script to create any missing tables
2. Deploy a sample LO definition to the specified schema
3. Verify that the tables are populated correctly

If you've already run the `fix_schema_issues.py` script and just want to test the deployment:

```bash
python test_schema_fix.py --schema workflow_temp --skip-fix
```

## Tables Created

The following tables are created by the `fix_schema_issues.py` script:

1. `lo_input_stack`: Stores input stacks for Local Objectives
2. `lo_input_items`: Stores input items for Local Objectives
3. `lo_output_stack`: Stores output stacks for Local Objectives
4. `lo_output_items`: Stores output items for Local Objectives
5. `lo_data_mapping_stack`: Stores data mapping stacks for Local Objectives
6. `lo_data_mappings`: Stores data mappings for Local Objectives
7. `lo_nested_functions`: Stores nested functions for input items
8. `lo_dependencies`: Stores dependencies between Local Objectives
9. `lo_input_validations`: Stores validations for input items

## ID Generation Logic

The ID generation in the system follows a hierarchical pattern:
- Global Objectives (GO) IDs: GO1, GO2, etc.
- Local Objectives (LO) IDs: GO1.LO1, GO1.LO2, etc.
- LO Input Items: GO1.LO1.IN1, GO1.LO1.IN2, etc.
- LO Output Items: GO1.LO1.OP1, GO1.LO1.OP2, etc.

There are three important IDs in the system that must be auto-generated and maintained:
1. Item ID (e.g., IN1, OP1)
2. Contextual ID - correlates to the respective Entity attribute
3. Slot ID - correlates to the LO Stack ID

The `lo_deployer.py` file generates these IDs correctly, but the tables need to exist in the schema for the IDs to be stored properly.

## Conclusion

By running the `fix_schema_issues.py` script, you can ensure that all the necessary tables exist in the schema with the correct structure. This will allow the YAML Builder v2 to properly populate the tables when parsing prescriptive sentences, which in turn will enable the runtime to work correctly.

If you encounter any issues or have questions, please refer to the `SCHEMA_MIGRATION_README.md` file for more information on the schema migration process.
