"""
V2 System Functions Executor

Centralized function executor that routes to appropriate V2 system functions
with uniform calling mechanism and enhanced logging.
"""

import logging
import importlib
from typing import Dict, Any, Optional, Type
from sqlalchemy.orm import Session

from .models import SystemFunctionInput, SystemFunctionOutput, ExecutionStatus
from .base_function import BaseSystemFunction

logger = logging.getLogger(__name__)

class V2FunctionExecutor:
    """
    Centralized executor for all V2 system functions.
    Provides uniform calling mechanism and function routing.
    """
    
    def __init__(self, db_session: Session):
        self.db = db_session
        self.logger = logger
        self._function_registry: Dict[str, Type[BaseSystemFunction]] = {}
        self._function_instances: Dict[str, BaseSystemFunction] = {}
        
        # Initialize function registry
        self._initialize_function_registry()
    
    def _initialize_function_registry(self):
        """
        Initialize the function registry with all available V2 functions.
        This method dynamically discovers and registers all function classes.
        """
        self.logger.info("🔧 Initializing V2 Function Registry...")
        
        # Define function mappings (function_name -> module_path)
        function_mappings = {
            # Database functions
            "fetch_records": "app.services.v2.functions.database.fetch_records",
            "fetch_by_id": "app.services.v2.functions.database.fetch_records",  # Use fetch_records for fetch_by_id
            "create_record": "app.services.v2.functions.database.create_record",
            "create": "app.services.v2.functions.database.create_record",  # Alias for create_record
            "update_record": "app.services.v2.functions.database.update_record",
            "delete_record": "app.services.v2.functions.database.delete_record",
            
            # Utility functions
            "generate_id": "app.services.v2.functions.utility.generate_id",
            "format_date": "app.services.v2.functions.utility.format_date",
            
            # Spatio-temporal functions
            "current_timestamp": "app.services.v2.functions.spatio_temporal.current_timestamp",
            
            # Validation functions
            "validate_email": "app.services.v2.functions.validation.validate_email",
            "validate_phone": "app.services.v2.functions.validation.validate_phone",
            "validate_required": "app.services.v2.functions.validation.validate_required",
            
            # Transform functions
            "transform_data": "app.services.v2.functions.transform.transform_data",
            "format_string": "app.services.v2.functions.transform.format_string",
            "format_enum_value": "app.services.v2.functions.transform.format_enum_value",
            
            # Math functions
            "calculate": "app.services.v2.functions.math.calculate",
            "sum_values": "app.services.v2.functions.math.sum_values",
            "subtract_days": "app.services.v2.functions.math.subtract_days",
            "conditional_assignment": "app.services.v2.functions.math.conditional_assignment",
            
            # Additional math functions
            "add_days": "app.services.v2.functions.math.subtract_days",  # Can reuse subtract_days with negative values
            "date_diff": "app.services.v2.functions.math.subtract_days",  # Alias for subtract_days
            
            # Adapter functions
            "send_email": "app.services.v2.functions.adapter.send_email",
        }
        
        # Register each function
        for function_name, module_path in function_mappings.items():
            try:
                self._register_function(function_name, module_path)
            except Exception as e:
                self.logger.warning(f"⚠️ Failed to register function {function_name}: {str(e)}")
        
        self.logger.info(f"✅ Function registry initialized with {len(self._function_registry)} functions")
        self.logger.info(f"📋 Available functions: {list(self._function_registry.keys())}")
    
    def _register_function(self, function_name: str, module_path: str):
        """Register a single function in the registry"""
        try:
            # Import the module
            module = importlib.import_module(module_path)
            
            # Get the function class (assumes class name follows pattern: FunctionNameFunction)
            class_name = self._get_class_name_from_function_name(function_name)
            function_class = getattr(module, class_name)
            
            # Validate that it's a BaseSystemFunction
            if not issubclass(function_class, BaseSystemFunction):
                raise ValueError(f"Function class {class_name} must inherit from BaseSystemFunction")
            
            # Register the function
            self._function_registry[function_name] = function_class
            self.logger.debug(f"✅ Registered function: {function_name} -> {class_name}")
            
        except Exception as e:
            self.logger.error(f"❌ Failed to register function {function_name}: {str(e)}")
            raise
    
    def _get_class_name_from_function_name(self, function_name: str) -> str:
        """Convert function name to class name (e.g., fetch_records -> FetchRecordsFunction)"""
        # Handle special cases
        if function_name == "fetch_by_id":
            return "FetchRecordsFunction"  # fetch_by_id uses the same class as fetch_records
        elif function_name == "create":
            return "CreateRecordFunction"  # create uses the same class as create_record
        
        # Split by underscore and capitalize each part
        parts = function_name.split('_')
        class_name = ''.join(word.capitalize() for word in parts) + 'Function'
        return class_name
    
    def _get_function_instance(self, function_name: str) -> BaseSystemFunction:
        """Get or create function instance"""
        if function_name not in self._function_instances:
            if function_name not in self._function_registry:
                raise ValueError(f"Function '{function_name}' not found in registry")
            
            function_class = self._function_registry[function_name]
            self._function_instances[function_name] = function_class(self.db)
        
        return self._function_instances[function_name]
    
    def execute_function(
        self, 
        function_name: str,
        input_values: Dict[str, Any],
        go_id: Optional[str] = None,
        lo_id: Optional[str] = None,
        primary_entity_id: Optional[str] = None,
        primary_attribute_id: Optional[str] = None,
        user_id: Optional[str] = None,
        tenant_id: Optional[str] = None,
        instance_id: Optional[str] = None,
        function_params: Optional[Dict[str, Any]] = None,
        execution_context: Optional[Dict[str, Any]] = None
    ) -> SystemFunctionOutput:
        """
        Execute a V2 system function with standardized input format.
        
        This is the main entry point for all function executions from LOs and nested functions.
        """
        self.logger.info(f"🚀 V2 FUNCTION EXECUTOR: Executing {function_name}")
        self.logger.info(f"📋 Context: GO={go_id}, LO={lo_id}, Entity={primary_entity_id}")
        self.logger.info(f"📋 Input Values: {input_values}")
        
        try:
            # Create standardized input
            system_input = SystemFunctionInput(
                function_name=function_name,
                go_id=go_id,
                lo_id=lo_id,
                primary_entity_id=primary_entity_id,
                primary_attribute_id=primary_attribute_id,
                input_values=input_values or {},
                user_id=user_id,
                tenant_id=tenant_id,
                instance_id=instance_id,
                function_params=function_params or {},
                execution_context=execution_context or {}
            )
            
            # Get function instance
            function_instance = self._get_function_instance(function_name)
            
            # Execute the function
            result = function_instance.execute(system_input)
            
            self.logger.info(f"✅ Function {function_name} executed successfully")
            self.logger.info(f"📤 Status: {result.status}, Result: {result.result}")
            
            return result
            
        except Exception as e:
            error_message = f"Function execution failed: {str(e)}"
            self.logger.error(f"💥 {error_message}")
            
            return SystemFunctionOutput(
                status=ExecutionStatus.ERROR,
                function_name=function_name,
                error_message=error_message,
                error_code="EXECUTOR_ERROR"
            )
    
    def get_available_functions(self) -> list[str]:
        """Get list of all available functions"""
        return list(self._function_registry.keys())
    
    def get_function_info(self, function_name: str) -> Dict[str, Any]:
        """Get information about a specific function"""
        if function_name not in self._function_registry:
            raise ValueError(f"Function '{function_name}' not found")
        
        function_instance = self._get_function_instance(function_name)
        
        return {
            "function_name": function_name,
            "category": function_instance.category.value,
            "required_inputs": function_instance.get_required_inputs(),
            "optional_inputs": function_instance.get_optional_inputs(),
            "class_name": function_instance.__class__.__name__
        }
    
    def validate_function_inputs(self, function_name: str, input_values: Dict[str, Any]) -> tuple[bool, Optional[str]]:
        """Validate inputs for a specific function without executing it"""
        try:
            function_instance = self._get_function_instance(function_name)
            
            system_input = SystemFunctionInput(
                function_name=function_name,
                input_values=input_values
            )
            
            return function_instance.validate_inputs(system_input)
            
        except Exception as e:
            return False, f"Validation error: {str(e)}"

# Global executor instance (will be initialized when needed)
_executor_instance: Optional[V2FunctionExecutor] = None

def get_v2_executor(db_session: Session) -> V2FunctionExecutor:
    """Get or create the global V2 function executor instance"""
    global _executor_instance
    if _executor_instance is None:
        _executor_instance = V2FunctionExecutor(db_session)
    else:
        # Update database session if different
        _executor_instance.db = db_session
    return _executor_instance

def execute_v2_function(
    function_name: str,
    db_session: Session,
    input_values: Dict[str, Any],
    **kwargs
) -> SystemFunctionOutput:
    """
    Convenience function for executing V2 system functions.
    This is the main entry point that should be used by LOs and nested functions.
    """
    executor = get_v2_executor(db_session)
    return executor.execute_function(function_name, input_values, **kwargs)
