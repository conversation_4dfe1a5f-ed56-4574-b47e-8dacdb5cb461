# Project Configuration
PROJECT_NAME=Workflow Engine
PROJECT_DESCRIPTION=Enterprise Workflow System Engine
VERSION=0.1.0

# CORS Configuration
CORS_ORIGINS=*

# MongoDB Configuration
MONGODB_URI=**************************************************************************************

# PostgreSQL Configuration
POSTGRES_SERVER=localhost
POSTGRES_USER=postgres
POSTGRES_PASSWORD=workflow_postgres_secure_password
POSTGRES_DB=workflow_system
POSTGRES_PORT=5433

# Redis Configuration
REDIS_HOST=workflow_redis
REDIS_PORT=6379
REDIS_PASSWORD=

# Security Configuration
SECRET_KEY=your_very_long_and_complex_secret_key_here
ACCESS_TOKEN_EXPIRE_MINUTES=10080  # 7 days

# Logging Configuration
LOG_LEVEL=INFO
