tenant:
  id: "t001"
  name: "ITTicketManagement001"
  roles:
    - id: "r001"
      name: "Employee"
      inherits_from: null
      access:
        entities:
          - entity_id: "e001"
            permissions: ["Read", "Create"]
        objectives:
          - objective_id: "go001.lo001"
            permissions: ["Execute"]
    - id: "r002"
      name: "IT Support"
      inherits_from: null
      access:
        entities:
          - entity_id: "e001"
            permissions: ["Read", "Update"]
        objectives:
          - objective_id: "go001.lo002"
            permissions: ["Execute"]
    - id: "r003"
      name: "IT Manager"
      inherits_from: null
      access:
        entities:
          - entity_id: "e001"
            permissions: ["Read", "Update"]
          - entity_id: "e002"
            permissions: ["Read", "Update"]
        objectives:
          - objective_id: "go001.lo003"
            permissions: ["Execute"]

workflow_data:
  software_type: "Ticket Management"
  industry: "Information Technology"
  version: "1.0"
  created_by: "system"
  created_on: "{{timestamp}}"

permission_types:
  - id: "read"
    description: "Can read entity data"
    capabilities: ["GET"]
  - id: "create"
    description: "Can create new entity records"
    capabilities: ["POST"]
  - id: "update"
    description: "Can update existing records"
    capabilities: ["PUT"]
  - id: "delete"
    description: "Can delete entity records"
    capabilities: ["DELETE"]
  - id: "execute"
    description: "Can execute workflows"
    capabilities: ["EXECUTE"]

entities:
  - id: "e001"
    name: "Ticket"
    type: "Master"
    version: "1.0"
    status: "Active"
    attributes_metadata:
      attribute_prefix: "at"
      attribute_map:
        "at101": "ticketID"
        "at102": "requesterID"
        "at103": "title"
        "at104": "description"
        "at105": "category"
        "at106": "priority"
        "at107": "status"
        "at108": "assignedTo"
        "at109": "createdDate"
        "at110": "dueDate"
        "at111": "resolution"
        "at112": "feedback"
        "at113": "attachmentURL"
      required_attributes:
        - "at101"
        - "at102"
        - "at103"
        - "at104"
        - "at105"
        - "at106"
        - "at107"
    attributes:
      - id: "at101"
        name: "ticketID"
        display_name: "Ticket ID"
        datatype: "String"
        required: true
        version: "1.0"
        status: "Deployed"
      - id: "at102"
        name: "requesterID"
        display_name: "Requester ID"
        datatype: "String"
        required: true
        version: "1.0"
        status: "Deployed"
      - id: "at103"
        name: "title"
        display_name: "Title"
        datatype: "String"
        required: true
        version: "1.0"
        status: "Deployed"
      - id: "at104"
        name: "description"
        display_name: "Description"
        datatype: "String"
        required: true
        version: "1.0"
        status: "Deployed"
      - id: "at105"
        name: "category"
        display_name: "Category"
        datatype: "Enum"
        required: true
        values: ["Hardware", "Software", "Network", "Access", "Email", "Other"]
        version: "1.0"
        status: "Deployed"
      - id: "at106"
        name: "priority"
        display_name: "Priority"
        datatype: "Enum"
        required: true
        values: ["Low", "Medium", "High", "Critical"]
        version: "1.0"
        status: "Deployed"
      - id: "at107"
        name: "status"
        display_name: "Status"
        datatype: "Enum"
        required: true
        values: ["New", "Assigned", "In Progress", "On Hold", "Resolved", "Closed"]
        version: "1.0"
        status: "Deployed"
      - id: "at108"
        name: "assignedTo"
        display_name: "Assigned To"
        datatype: "String"
        required: false
        version: "1.0"
        status: "Deployed"
      - id: "at109"
        name: "createdDate"
        display_name: "Created Date"
        datatype: "Date"
        required: false
        version: "1.0"
        status: "Deployed"
      - id: "at110"
        name: "dueDate"
        display_name: "Due Date"
        datatype: "Date"
        required: false
        version: "1.0"
        status: "Deployed"
      - id: "at111"
        name: "resolution"
        display_name: "Resolution"
        datatype: "String"
        required: false
        version: "1.0"
        status: "Deployed"
      - id: "at112"
        name: "feedback"
        display_name: "Feedback"
        datatype: "String"
        required: false
        version: "1.0"
        status: "Deployed"
      - id: "at113"
        name: "attachmentURL"
        display_name: "Attachment URL"
        datatype: "String"
        required: false
        version: "1.0"
        status: "Deployed"
  - id: "e002"
    name: "ITSupportStaff"
    type: "Master"
    version: "1.0"
    status: "Active"
    attributes_metadata:
      attribute_prefix: "at"
      attribute_map:
        "at201": "staffID"
        "at202": "staffName"
        "at203": "specialization"
        "at204": "availability"
        "at205": "currentLoad"
      required_attributes:
        - "at201"
        - "at202"
        - "at203"
        - "at204"
    attributes:
      - id: "at201"
        name: "staffID"
        display_name: "Staff ID"
        datatype: "String"
        required: true
        version: "1.0"
        status: "Deployed"
      - id: "at202"
        name: "staffName"
        display_name: "Staff Name"
        datatype: "String"
        required: true
        version: "1.0"
        status: "Deployed"
      - id: "at203"
        name: "specialization"
        display_name: "Specialization"
        datatype: "Enum"
        required: true
        values: ["Hardware", "Software", "Network", "Security", "General"]
        version: "1.0"
        status: "Deployed"
      - id: "at204"
        name: "availability"
        display_name: "Availability"
        datatype: "Enum"
        required: true
        values: ["Available", "Busy", "On Leave", "Out of Office"]
        version: "1.0"
        status: "Deployed"
      - id: "at205"
        name: "currentLoad"
        display_name: "Current Load"
        datatype: "Integer"
        required: false
        version: "1.0"
        status: "Deployed"

global_objectives:
  - id: "go001"
    name: "IT Ticket Management Workflow"
    version: "1.0"
    status: "Active"
    input_stack:
      description: "Global inputs"
      inputs: []
    output_stack:
      description: "Global outputs"
      outputs: []
    data_mapping_stack:
      description: "Data handover between GOs"
      mappings: []

local_objectives:
  - id: "lo001"
    contextual_id: "go001.lo001"
    name: "Submit IT Support Ticket"
    workflow_source: "origin"
    function_type: "Create"
    agent_stack:
      agents:
        - role: "r001"
          rights: ["Execute"]
    input_stack:
      description: "Capture ticket details"
      inputs:
        # Information-only help text
        - id: "in001"
          slot_id: "e001.at999.in001"
          contextual_id: "go001.lo001.in001"
          source:
            type: "information"
            description: "Ticket Submission Instructions"
          required: false
          data_type: "string"
          ui_control: "oj-text"
          is_visible: true
          metadata:
            usage: ""
          validations: []
          nested_function:
            id: "nf001"
            function_name: "to_uppercase"
            function_type: "transform"
            parameters:
              text: "Please provide detailed information about your IT issue. The more details you provide, the faster we can assist you."
            output_to: "info_text"

        # Auto-generated Ticket ID
        - id: "in002"
          slot_id: "e001.at101.in002"
          contextual_id: "go001.lo001.in002"
          source:
            type: "system"
            description: "Auto-generated Ticket ID"
          required: true
          data_type: "string"
          ui_control: "oj-input-text"
          metadata:
            usage: ""
          validations:
            - rule: "Ticket ID must be unique"
              rule_type: "validate_required"
              validation_method: "validate_required"
              error_message: "Ticket ID is required"
          nested_function:
            id: "nf002"
            function_name: "generate_id"
            function_type: "utility"
            parameters:
              entity: "e001"
              attribute: "at101"
              prefix: "TKT"
            output_to: "at101"

        # Requester ID
        - id: "in003"
          slot_id: "e001.at102.in003"
          contextual_id: "go001.lo001.in003"
          source:
            type: "user"
            description: "Requester ID"
          required: true
          data_type: "string"
          ui_control: "oj-input-text"
          metadata:
            usage: ""
          validations:
            - rule: "Requester ID is required"
              rule_type: "validate_required"
              validation_method: "validate_required"
              error_message: "Requester ID cannot be empty"

        # Ticket Title
        - id: "in004"
          slot_id: "e001.at103.in004"
          contextual_id: "go001.lo001.in004"
          source:
            type: "user"
            description: "Ticket Title"
          required: true
          data_type: "string"
          ui_control: "oj-input-text"
          metadata:
            usage: ""
          validations:
            - rule: "Title is required"
              rule_type: "validate_required"
              validation_method: "validate_required"
              error_message: "Ticket title cannot be empty"

        # Description
        - id: "in005"
          slot_id: "e001.at104.in005"
          contextual_id: "go001.lo001.in005"
          source:
            type: "user"
            description: "Description"
          required: true
          data_type: "string"
          ui_control: "oj-text-area"
          metadata:
            usage: ""
          validations:
            - rule: "Description is required"
              rule_type: "validate_required"
              validation_method: "validate_required"
              error_message: "Description cannot be empty"

        # Category dropdown
        - id: "in006"
          slot_id: "e001.at105.in006"
          contextual_id: "go001.lo001.in006"
          source:
            type: "user"
            description: "Category"
          required: true
          data_type: "enum"
          ui_control: "oj-combobox-one"
          has_dropdown_source: true
          metadata:
            usage: ""
          validations:
            - rule: "Category is required"
              rule_type: "validate_required"
              validation_method: "validate_required"
              error_message: "Category is required"
          dropdown_source:
            source_type: "function"
            function_name: "fetch_enum_values"
            function_params:
              entity_id: "e001"
              attribute_id: "at105"
            value_field: "value"
            display_field: "display"

        # Priority dropdown
        - id: "in007"
          slot_id: "e001.at106.in007"
          contextual_id: "go001.lo001.in007"
          source:
            type: "user"
            description: "Priority"
          required: true
          data_type: "enum"
          ui_control: "oj-combobox-one"
          has_dropdown_source: true
          metadata:
            usage: ""
          validations:
            - rule: "Priority is required"
              rule_type: "validate_required"
              validation_method: "validate_required"
              error_message: "Priority is required"
          dropdown_source:
            source_type: "function"
            function_name: "fetch_enum_values"
            function_params:
              entity_id: "e001"
              attribute_id: "at106"
            value_field: "value"
            display_field: "display"

        # Status - auto-set to New
        - id: "in008"
          slot_id: "e001.at107.in008"
          contextual_id: "go001.lo001.in008"
          source:
            type: "system"
            description: "Status"
          required: true
          data_type: "enum"
          allowed_values: ["New", "Assigned", "In Progress", "On Hold", "Resolved", "Closed"]
          ui_control: "oj-select-single"
          metadata:
            usage: ""
          validations:
            - rule: "Status must be valid"
              rule_type: "enum_check"
              validation_method: "enum_check"
              allowed_values: ["New", "Assigned", "In Progress", "On Hold", "Resolved", "Closed"]
              error_message: "Invalid status value"
          nested_function:
            id: "nf003"
            function_name: "to_uppercase"
            function_type: "transform"
            parameters:
              text: "New"
            output_to: "at107"
            
        # Created Date - system generated
        - id: "in009"
          slot_id: "e001.at109.in009"
          contextual_id: "go001.lo001.in009"
          source:
            type: "system"
            description: "Created Date"
          required: true
          data_type: "date"
          ui_control: "oj-input-date"
          metadata:
            usage: ""
          validations:
            - rule: "Created Date is required"
              rule_type: "validate_required"
              validation_method: "validate_required"
              error_message: "Created Date is required"
          nested_function:
            id: "nf004"
            function_name: "current_timestamp"
            function_type: "utility"
            parameters: {}
            output_to: "at109"

        # Attachment URL (optional)
        - id: "in010"
          slot_id: "e001.at113.in010"
          contextual_id: "go001.lo001.in010"
          source:
            type: "user"
            description: "Attachment URL"
          required: false
          data_type: "string"
          ui_control: "oj-input-text"
          metadata:
            usage: ""
          validations: []

    output_stack:
      description: "Ticket created"
      outputs:
        - id: "out001"
          slot_id: "executionstatus.out001"
          contextual_id: "go001.lo001.out001"
          source:
            type: "system"
            description: "Success or failure"
          data_type: "string"

        - id: "out002"
          slot_id: "e001.at101.out002"
          contextual_id: "go001.lo001.out002"
          source:
            type: "system"
            description: "Ticket ID"
          data_type: "string"

        - id: "out003"
          slot_id: "e001.at102.out003"
          contextual_id: "go001.lo001.out003"
          source:
            type: "system"
            description: "Requester ID"
          data_type: "string"

        - id: "out004"
          slot_id: "e001.at103.out004"
          contextual_id: "go001.lo001.out004"
          source:
            type: "system"
            description: "Title"
          data_type: "string"

        - id: "out005"
          slot_id: "e001.at104.out005"
          contextual_id: "go001.lo001.out005"
          source:
            type: "system"
            description: "Description"
          data_type: "string"

        - id: "out006"
          slot_id: "e001.at105.out006"
          contextual_id: "go001.lo001.out006"
          source:
            type: "system"
            description: "Category"
          data_type: "enum"

        - id: "out007"
          slot_id: "e001.at106.out007"
          contextual_id: "go001.lo001.out007"
          source:
            type: "system"
            description: "Priority"
          data_type: "enum"

        - id: "out008"
          slot_id: "e001.at107.out008"
          contextual_id: "go001.lo001.out008"
          source:
            type: "system"
            description: "Status"
          data_type: "enum"
          
        - id: "out009"
          slot_id: "e001.at109.out009"
          contextual_id: "go001.lo001.out009"
          source:
            type: "system"
            description: "Created Date"
          data_type: "date"
          
        - id: "out010"
          slot_id: "e001.at113.out010"
          contextual_id: "go001.lo001.out010"
          source:
            type: "system"
            description: "Attachment URL"
          data_type: "string"

    data_mapping_stack:
      description: "Data handover between LOs"
      mappings:
        - id: "map001"
          source: "lo001.out002"
          target: "lo002.in011"
          mapping_type: "direct"
        - id: "map002"
          source: "lo001.out003"
          target: "lo002.in012"
          mapping_type: "direct"
        - id: "map003"
          source: "lo001.out004"
          target: "lo002.in013"
          mapping_type: "direct"
        - id: "map004"
          source: "lo001.out005"
          target: "lo002.in014"
          mapping_type: "direct"
        - id: "map005"
          source: "lo001.out006"
          target: "lo002.in015"
          mapping_type: "direct"
        - id: "map006"
          source: "lo001.out007"
          target: "lo002.in016"
          mapping_type: "direct"
        - id: "map007"
          source: "lo001.out009"
          target: "lo002.in018"
          mapping_type: "direct"
        - id: "map008"
          source: "lo001.out010"
          target: "lo002.in020"
          mapping_type: "direct"
        - id: "map009"
          source: "lo001.out002"
          target: "lo003.in024"
          mapping_type: "direct"
        - id: "map010"
          source: "lo001.out003"
          target: "lo003.in025"
          mapping_type: "direct"
        - id: "map011"
          source: "lo001.out004"
          target: "lo003.in026"
          mapping_type: "direct"
        - id: "map012"
          source: "lo001.out005"
          target: "lo003.in027"
          mapping_type: "direct"
        - id: "map013"
          source: "lo001.out006"
          target: "lo003.in028"
          mapping_type: "direct"
        - id: "map014"
          source: "lo001.out007"
          target: "lo003.in029"
          mapping_type: "direct"
        - id: "map015"
          source: "lo001.out009"
          target: "lo003.in031"
          mapping_type: "direct"
        - id: "map016"
          source: "lo001.out010"
          target: "lo003.in033"
          mapping_type: "direct"

    execution_pathway:
      type: "alternate"
      conditions:
        - condition:
            condition_type: "attribute_comparison"
            entity: "e001"
            attribute: "at106"
            operator: "equals"
            value: "Critical"
          next_lo: "lo003"
        - condition:
            condition_type: "attribute_comparison"
            entity: "e001"
            attribute: "at106"
            operator: "not_equals"
            value: "Critical"
          next_lo: "lo002"

  - id: "lo002"
    contextual_id: "go001.lo002"
    name: "Assign and Process Ticket"
    workflow_source: "intermediate"
    function_type: "Update"
    agent_stack:
      agents:
        - role: "r002"
          rights: ["Execute", "Update"]
    input_stack:
      description: "IT Support reviews and processes ticket"
      inputs:
        # Information message
        - id: "in010"
          slot_id: "e001.at999.in010"
          contextual_id: "go001.lo002.in010"
          source:
            type: "information"
            description: "Ticket Processing Instructions"
          required: false
          data_type: "string"
          ui_control: "oj-text"
          is_visible: true
          metadata:
            usage: ""
          validations: []
          nested_function:
            id: "nf005"
            function_name: "to_uppercase"
            function_type: "transform"
            parameters:
              text: "Review the ticket details and assign it to an appropriate IT support staff. Update the status to reflect current progress."
            output_to: "info_text"

        # Ticket ID for lookup
        - id: "in011"
          slot_id: "e001.at101.in011"
          contextual_id: "go001.lo002.in011"
          source:
            type: "system"
            description: "Ticket ID"
          required: true
          data_type: "string"
          ui_control: "oj-input-text"
          metadata:
            usage: "lookup"
          validations:
            - rule: "Ticket ID must be present"
              rule_type: "validate_required"
              validation_method: "validate_required"
              error_message: "Ticket ID is required"
            - rule: "Ticket ID must exist"
              rule_type: "entity_exists"
              validation_method: "entity_exists"
              entity: "e001"
              attribute: "at101"
              error_message: "Ticket ID not found in the system"
              
        # Requester ID
        - id: "in012"
          slot_id: "e001.at102.in012"
          contextual_id: "go001.lo002.in012"
          source:
            type: "system"
            description: "Requester ID"
          required: true
          data_type: "string"
          ui_control: "oj-input-text"
          metadata:
            usage: "lookup"
          validations:
            - rule: "Requester ID is required"
              rule_type: "validate_required"
              validation_method: "validate_required"
              error_message: "Requester ID is required"
              
        # Title
        - id: "in013"
          slot_id: "e001.at103.in013"
          contextual_id: "go001.lo002.in013"
          source:
            type: "system"
            description: "Title"
          required: true
          data_type: "string"
          ui_control: "oj-input-text"
          metadata:
            usage: "lookup"
          validations:
            - rule: "Title is required"
              rule_type: "validate_required"
              validation_method: "validate_required"
              error_message: "Title is required"
              
        # Description
        - id: "in014"
          slot_id: "e001.at104.in014"
          contextual_id: "go001.lo002.in014"
          source:
            type: "system"
            description: "Description"
          required: true
          data_type: "string"
          ui_control: "oj-text-area"
          metadata:
            usage: "lookup"
          validations:
            - rule: "Description is required"
              rule_type: "validate_required"
              validation_method: "validate_required"
              error_message: "Description is required"
              
        # Category
        - id: "in015"
          slot_id: "e001.at105.in015"
          contextual_id: "go001.lo002.in015"
          source:
            type: "system"
            description: "Category"
          required: true
          data_type: "enum"
          ui_control: "oj-combobox-one"
          metadata:
            usage: "lookup"
          validations:
            - rule: "Category is required"
              rule_type: "validate_required"
              validation_method: "validate_required"
              error_message: "Category is required"
              
        # Priority
        - id: "in016"
          slot_id: "e001.at106.in016"
          contextual_id: "go001.lo002.in016"
          source:
            type: "system"
            description: "Priority"
          required: true
          data_type: "enum"
          ui_control: "oj-combobox-one"
          metadata:
            usage: "lookup"
          validations:
            - rule: "Priority is required"
              rule_type: "validate_required"
              validation_method: "validate_required"
              error_message: "Priority is required"
              
        # Status to be updated
        - id: "in017"
          slot_id: "e001.at107.in017"
          contextual_id: "go001.lo002.in017"
          source:
            type: "user"
            description: "Status"
          required: true
          data_type: "enum"
          allowed_values: ["New", "Assigned", "In Progress", "On Hold", "Resolved", "Closed"]
          ui_control: "oj-select-single"
          metadata:
            usage: "update"
          validations:
            - rule: "Status must be valid"
              rule_type: "enum_check"
              validation_method: "enum_check"
              allowed_values: ["New", "Assigned", "In Progress", "On Hold", "Resolved", "Closed"]
              error_message: "Invalid status value"
              
        # Created Date
        - id: "in018"
          slot_id: "e001.at109.in018"
          contextual_id: "go001.lo002.in018"
          source:
            type: "system"
            description: "Created Date"
          required: true
          data_type: "date"
          ui_control: "oj-input-date"
          metadata:
            usage: "lookup"
          validations:
            - rule: "Created Date is required"
              rule_type: "validate_required"
              validation_method: "validate_required"
              error_message: "Created Date is required"
        
        # Assigned To
        - id: "in019"
          slot_id: "e001.at108.in019"
          contextual_id: "go001.lo002.in019"
          source:
            type: "user"
            description: "Assigned To"
          required: true
          data_type: "string"
          ui_control: "oj-combobox-one"
          has_dropdown_source: true
          metadata:
            usage: "update"
          validations:
            - rule: "Assignment is required"
              rule_type: "validate_required"
              validation_method: "validate_required"
              error_message: "Ticket must be assigned to an IT Support staff"
          dropdown_source:
            source_type: "function"
            function_name: "fetch_filtered_records"
            function_params:
              table: "workflow_runtime.it_support_staff"
              filter_column: "availability"
              filter_value: "Available"
              value_column: "staff_id"
              display_column: "staff_name"
              additional_filters: {}
            value_field: "value"
            display_field: "display"
            
        # Attachment URL
        - id: "in020"
          slot_id: "e001.at113.in020"
          contextual_id: "go001.lo002.in020"
          source:
            type: "system"
            description: "Attachment URL"
          required: false
          data_type: "string"
          ui_control: "oj-input-text"
          metadata:
            usage: "lookup"
          validations: []
          
        # Due Date
        - id: "in021"
          slot_id: "e001.at110.in021"
          contextual_id: "go001.lo002.in021"
          source:
            type: "user"
            description: "Due Date"
          required: true
          data_type: "date"
          ui_control: "oj-input-date"
          metadata:
            usage: "update"
          validations:
            - rule: "Due Date is required"
              rule_type: "validate_required"
              validation_method: "validate_required"
              error_message: "Due Date is required"
              
        # Resolution (optional)
        - id: "in022"
          slot_id: "e001.at111.in022"
          contextual_id: "go001.lo002.in022"
          source:
            type: "user"
            description: "Resolution"
          required: false
          data_type: "string"
          ui_control: "oj-text-area"
          metadata:
            usage: "update"
          validations: []

    output_stack:
      description: "Ticket updated by IT Support"
      outputs:
        - id: "out011"
          slot_id: "executionstatus.out011"
          contextual_id: "go001.lo002.out011"
          source:
            type: "system"
            description: "Success or failure"
          data_type: "string"
          
        - id: "out012"
          slot_id: "e001.at101.out012"
          contextual_id: "go001.lo002.out012"
          source:
            type: "system"
            description: "Ticket ID"
          data_type: "string"
          
        - id: "out013"
          slot_id: "e001.at102.out013"
          contextual_id: "go001.lo002.out013"
          source:
            type: "system"
            description: "Requester ID"
          data_type: "string"
          
        - id: "out014"
          slot_id: "e001.at103.out014"
          contextual_id: "go001.lo002.out014"
          source:
            type: "system"
            description: "Title"
          data_type: "string"
          
        - id: "out015"
          slot_id: "e001.at104.out015"
          contextual_id: "go001.lo002.out015"
          source:
            type: "system"
            description: "Description"
          data_type: "string"
          
        - id: "out016"
          slot_id: "e001.at105.out016"
          contextual_id: "go001.lo002.out016"
          source:
            type: "system"
            description: "Category"
          data_type: "enum"
          
        - id: "out017"
          slot_id: "e001.at106.out017"
          contextual_id: "go001.lo002.out017"
          source:
            type: "system"
            description: "Priority"
          data_type: "enum"
          
        - id: "out018"
          slot_id: "e001.at107.out018"
          contextual_id: "go001.lo002.out018"
          source:
            type: "system"
            description: "Status"
          data_type: "enum"
          
        - id: "out019"
          slot_id: "e001.at108.out019"
          contextual_id: "go001.lo002.out019"
          source:
            type: "system"
            description: "Assigned To"
          data_type: "string"
          
        - id: "out020"
          slot_id: "e001.at109.out020"
          contextual_id: "go001.lo002.out020"
          source:
            type: "system"
            description: "Created Date"
          data_type: "date"
          
        - id: "out021"
          slot_id: "e001.at110.out021"
          contextual_id: "go001.lo002.out021"
          source:
            type: "system"
            description: "Due Date"
          data_type: "date"
          
        - id: "out022"
          slot_id: "e001.at111.out022"
          contextual_id: "go001.lo002.out022"
          source:
            type: "system"
            description: "Resolution"
          data_type: "string"
          
        - id: "out023"
          slot_id: "e001.at113.out023"
          contextual_id: "go001.lo002.out023"
          source:
            type: "system"
            description: "Attachment URL"
          data_type: "string"

    data_mapping_stack:
      description: "Data handover between LOs"
      mappings: []

    execution_pathway:
      type: "terminal"
      next_lo: ""

  - id: "lo003"
    contextual_id: "go001.lo003"
    name: "Critical Ticket Escalation"
    workflow_source: "intermediate"
    function_type: "Update"
    agent_stack:
      agents:
        - role: "r003"
          rights: ["Execute", "Update"]
    input_stack:
      description: "IT Manager reviews critical ticket"
      inputs:
        # Information policy message
        - id: "in023"
          slot_id: "e001.at999.in023"
          contextual_id: "go001.lo003.in023"
          source:
            type: "information"
            description: "Critical Ticket Handling"
          required: false
          data_type: "string"
          ui_control: "oj-text"
          is_visible: true
          metadata:
            usage: ""
          validations: []
          nested_function:
            id: "nf006"
            function_name: "to_uppercase"
            function_type: "transform"
            parameters:
              text: "This ticket has been marked as CRITICAL and requires immediate attention. Please assign to the most suitable specialist and set appropriate priority."
            output_to: "info_text"
            
        # Ticket ID for lookup
        - id: "in024"
          slot_id: "e001.at101.in024"
          contextual_id: "go001.lo003.in024"
          source:
            type: "system"
            description: "Ticket ID"
          required: true
          data_type: "string"
          ui_control: "oj-input-text"
          metadata:
            usage: "lookup"
          validations:
            - rule: "Ticket ID must be present"
              rule_type: "validate_required"
              validation_method: "validate_required"
              error_message: "Ticket ID is required"
            - rule: "Ticket ID must exist"
              rule_type: "entity_exists"
              validation_method: "entity_exists"
              entity: "e001"
              attribute: "at101"
              error_message: "Ticket ID not found in the system"
              
        # Requester ID
        - id: "in025"
          slot_id: "e001.at102.in025"
          contextual_id: "go001.lo003.in025"
          source:
            type: "system"
            description: "Requester ID"
          required: true
          data_type: "string"
          ui_control: "oj-input-text"
          metadata:
            usage: "lookup"
          validations:
            - rule: "Requester ID is required"
              rule_type: "validate_required"
              validation_method: "validate_required"
              error_message: "Requester ID is required"
              
        # Title
        - id: "in026"
          slot_id: "e001.at103.in026"
          contextual_id: "go001.lo003.in026"
          source:
            type: "system"
            description: "Title"
          required: true
          data_type: "string"
          ui_control: "oj-input-text"
          metadata:
            usage: "lookup"
          validations:
            - rule: "Title is required"
              rule_type: "validate_required"
              validation_method: "validate_required"
              error_message: "Title is required"
              
        # Description
        - id: "in027"
          slot_id: "e001.at104.in027"
          contextual_id: "go001.lo003.in027"
          source:
            type: "system"
            description: "Description"
          required: true
          data_type: "string"
          ui_control: "oj-text-area"
          metadata:
            usage: "lookup"
          validations:
            - rule: "Description is required"
              rule_type: "validate_required"
              validation_method: "validate_required"
              error_message: "Description is required"
              
        # Category
        - id: "in028"
          slot_id: "e001.at105.in028"
          contextual_id: "go001.lo003.in028"
          source:
            type: "system"
            description: "Category"
          required: true
          data_type: "enum"
          ui_control: "oj-combobox-one"
          metadata:
            usage: "lookup"
          validations:
            - rule: "Category is required"
              rule_type: "validate_required"
              validation_method: "validate_required"
              error_message: "Category is required"
              
        # Priority
        - id: "in029"
          slot_id: "e001.at106.in029"
          contextual_id: "go001.lo003.in029"
          source:
            type: "system"
            description: "Priority"
          required: true
          data_type: "enum"
          ui_control: "oj-combobox-one"
          metadata:
            usage: "lookup"
          validations:
            - rule: "Priority is required"
              rule_type: "validate_required"
              validation_method: "validate_required"
              error_message: "Priority is required"
              
        # Status to be updated
        - id: "in030"
          slot_id: "e001.at107.in030"
          contextual_id: "go001.lo003.in030"
          source:
            type: "user"
            description: "Status"
          required: true
          data_type: "enum"
          allowed_values: ["New", "Assigned", "In Progress", "On Hold", "Resolved", "Closed"]
          ui_control: "oj-select-single"
          metadata:
            usage: "update"
          validations:
            - rule: "Status must be valid"
              rule_type: "enum_check"
              validation_method: "enum_check"
              allowed_values: ["New", "Assigned", "In Progress", "On Hold", "Resolved", "Closed"]
              error_message: "Invalid status value"
              
        # Created Date
        - id: "in031"
          slot_id: "e001.at109.in031"
          contextual_id: "go001.lo003.in031"
          source:
            type: "system"
            description: "Created Date"
          required: true
          data_type: "date"
          ui_control: "oj-input-date"
          metadata:
            usage: "lookup"
          validations:
            - rule: "Created Date is required"
              rule_type: "validate_required"
              validation_method: "validate_required"
              error_message: "Created Date is required"
        
        # Assigned To - dropdown with specialist staff only
        - id: "in032"
          slot_id: "e001.at108.in032"
          contextual_id: "go001.lo003.in032"
          source:
            type: "user"
            description: "Assigned To"
          required: true
          data_type: "string"
          ui_control: "oj-combobox-one"
          has_dropdown_source: true
          metadata:
            usage: "update"
          validations:
            - rule: "Assignment is required"
              rule_type: "validate_required"
              validation_method: "validate_required"
              error_message: "Critical ticket must be assigned to a specialist"
          dropdown_source:
            source_type: "function"
            function_name: "fetch_filtered_records"
            function_params:
              table: "workflow_runtime.it_support_staff"
              filter_column: "specialization"
              filter_value: "${in028}"
              value_column: "staff_id"
              display_column: "staff_name"
              additional_filters:
                availability: "Available"
            value_field: "value"
            display_field: "display"
            depends_on_fields: ["in028"]
            
        # Attachment URL
        - id: "in033"
          slot_id: "e001.at113.in033"
          contextual_id: "go001.lo003.in033"
          source:
            type: "system"
            description: "Attachment URL"
          required: false
          data_type: "string"
          ui_control: "oj-input-text"
          metadata:
            usage: "lookup"
          validations: []
          
        # Due Date - system calculated for critical tickets (24 hours from creation)
        - id: "in034"
          slot_id: "e001.at110.in034"
          contextual_id: "go001.lo003.in034"
          source:
            type: "system_dependent"
            description: "Due Date (24 hours)"
          required: true
          data_type: "date"
          ui_control: "oj-input-date"
          dependencies: ["in031"]
          dependency_type: "calculation"
          metadata:
            usage: "update"
          validations:
            - rule: "Due Date is required"
              rule_type: "validate_required"
              validation_method: "validate_required"
              error_message: "Due Date is required"
          nested_function:
            id: "nf007"
            function_name: "add_days"
            function_type: "math"
            parameters:
              base_date: "${in031}"
              days: 1
            output_to: "at110"
              
        # Resolution Notes
        - id: "in035"
          slot_id: "e001.at111.in035"
          contextual_id: "go001.lo003.in035"
          source:
            type: "user"
            description: "Resolution Notes"
          required: false
          data_type: "string"
          ui_control: "oj-text-area"
          metadata:
            usage: "update"
          validations: []

    output_stack:
      description: "Critical ticket updated by IT Manager"
      outputs:
        - id: "out024"
          slot_id: "executionstatus.out024"
          contextual_id: "go001.lo003.out024"
          source:
            type: "system"
            description: "Success or failure"
          data_type: "string"
          
        - id: "out025"
          slot_id: "e001.at101.out025"
          contextual_id: "go001.lo003.out025"
          source:
            type: "system"
            description: "Ticket ID"
          data_type: "string"
          
        - id: "out026"
          slot_id: "e001.at102.out026"
          contextual_id: "go001.lo003.out026"
          source:
            type: "system"
            description: "Requester ID"
          data_type: "string"
          
        - id: "out027"
          slot_id: "e001.at103.out027"
          contextual_id: "go001.lo003.out027"
          source:
            type: "system"
            description: "Title"
          data_type: "string"
          
        - id: "out028"
          slot_id: "e001.at104.out028"
          contextual_id: "go001.lo003.out028"
          source:
            type: "system"
            description: "Description"
          data_type: "string"
          
        - id: "out029"
          slot_id: "e001.at105.out029"
          contextual_id: "go001.lo003.out029"
          source:
            type: "system"
            description: "Category"
          data_type: "enum"
          
        - id: "out030"
          slot_id: "e001.at106.out030"
          contextual_id: "go001.lo003.out030"
          source:
            type: "system"
            description: "Priority"
          data_type: "enum"
          
        - id: "out031"
          slot_id: "e001.at107.out031"
          contextual_id: "go001.lo003.out031"
          source:
            type: "system"
            description: "Status"
          data_type: "enum"
          
        - id: "out032"
          slot_id: "e001.at108.out032"
          contextual_id: "go001.lo003.out032"
          source:
            type: "system"
            description: "Assigned To"
          data_type: "string"
          
        - id: "out033"
          slot_id: "e001.at109.out033"
          contextual_id: "go001.lo003.out033"
          source:
            type: "system"
            description: "Created Date"
          data_type: "date"
          
        - id: "out034"
          slot_id: "e001.at110.out034"
          contextual_id: "go001.lo003.out034"
          source:
            type: "system"
            description: "Due Date"
          data_type: "date"
          
        - id: "out035"
          slot_id: "e001.at111.out035"
          contextual_id: "go001.lo003.out035"
          source:
            type: "system"
            description: "Resolution Notes"
          data_type: "string"
          
        - id: "out036"
          slot_id: "e001.at113.out036"
          contextual_id: "go001.lo003.out036"
          source:
            type: "system"
            description: "Attachment URL"
          data_type: "string"

    data_mapping_stack:
      description: "Data handover between LOs"
      mappings: []

    execution_pathway:
      type: "terminal"
      next_lo: ""