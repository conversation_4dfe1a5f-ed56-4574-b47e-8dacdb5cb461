{"timestamp": "2025-06-24T04:45:02.319282", "operation": "process_mongo_entity_relationships_to_workflow_temp", "input_data": {"schema": "workflow_temp"}, "result": {"total_processed": 1, "successful_inserts": 0, "failed_inserts": 0, "details": [{"relationship_id": 1, "source_entity_id": "E7", "target_entity_id": "E15", "status": "skipped", "reason": "already_exists"}]}, "status": "success"}
{"timestamp": "2025-06-24T11:42:23.287388", "operation": "insert_entity_relationship_to_workflow_runtime", "input_data": {"_id": "68551396458daab7c4ae1272", "relationship_id": 13, "source_entity_id": "E7", "target_entity_id": "E15", "relationship_type": "many-to-one", "source_attribute_id": "E7.At2", "target_attribute_id": "E15.At1", "on_delete": "restrict", "on_update": "cascade", "foreign_key_type": "Non-Nullable", "description": "Each leave application belongs to one employee", "version": 1, "status": "draft", "created_at": "2025-06-20T07:53:58.261487", "updated_at": "2025-06-20T07:53:58.261492", "created_by": "Tarun", "updated_by": "Tarun"}, "result": {"success": true, "inserted_id": 13, "schema": "workflow_runtime", "relationship_id": 13, "source_entity_id": "E7", "target_entity_id": "E15", "original_relationship_id": 1}, "status": "success"}
{"timestamp": "2025-06-24T11:42:23.290138", "operation": "process_mongo_entity_relationships_to_workflow_runtime", "input_data": {"schema": "workflow_runtime"}, "result": {"total_processed": 1, "successful_inserts": 1, "failed_inserts": 0, "details": [{"relationship_id": 13, "source_entity_id": "E7", "target_entity_id": "E15", "status": "success", "details": {"success": true, "inserted_id": 13, "schema": "workflow_runtime", "relationship_id": 13, "source_entity_id": "E7", "target_entity_id": "E15", "original_relationship_id": 1}}]}, "status": "success"}
