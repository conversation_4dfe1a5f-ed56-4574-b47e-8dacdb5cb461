```yaml
tenant:
  id: "t001"
  name: "SalesCRM"
  roles:
    - id: "r001"
      name: "Sales Executive"
      inherits_from: null
      access:
        entities:
          - entity_id: "e001"
            permissions: ["Read", "Create", "Update"]
        objectives:
          - objective_id: "go001.lo001"
            permissions: ["Execute"]
          - objective_id: "go001.lo002"
            permissions: ["Execute"]

permission_types:
  - Read
  - Create
  - Update
  - Delete
  - Execute

entities:
  - id: "e001"
    name: "Lead"
    attributes:
      - id: "at101"
        name: "Lead ID"
        datatype: "String"
        required: true
      - id: "at102"
        name: "Customer Name"
        datatype: "String"
        required: true
      - id: "at103"
        name: "Email"
        datatype: "String"
        required: true
      - id: "at104"
        name: "Mobile"
        datatype: "String"
        required: true
      - id: "at105"
        name: "Address"
        datatype: "String"
        required: true
      - id: "at106"
        name: "Status"
        datatype: "Enum"
        required: false
        values: ["Warm", "Cold", "Hot"]
      - id: "at107"
        name: "Conversation Details"
        datatype: "String"
        required: false

global_objectives:
  - id: "go001"
    name: "Lead Management Workflow"
    version: "1.0"
    status: "Active"
    input_stack:
      description: "Global inputs"
      inputs: []
    output_stack:
      description: "Global outputs"
      outputs: []
    local_objectives:
      - id: "lo001"
        contextual_id: "go001.lo001"
        name: "Capture Lead"
        workflow_source: "Origin"
        function_type: "Create"
        agent_stack:
          agents:
            - role: "r001"
              rights: ["Execute"]
        input_stack:
          description: "Collect lead details from customer"
          inputs:
            - id: in001
              slot_id: e001.at101.in001
              contextual_id: go001.lo001.in001
              source:
                type: system
                description: Auto-generated Lead ID
              required: true
              nested_function:
                id: nf001
                function_name: generate_id
                function_type: utility
                parameters:
                  entity: e001
                  attribute: at101
                output_to: at101
            - id: in002
              slot_id: e001.at102.in002
              contextual_id: go001.lo001.in002
              source:
                type: user
                description: Customer Name
              required: true
            - id: in003
              slot_id: e001.at103.in003
              contextual_id: go001.lo001.in003
              source:
                type: user
                description: Email
              required: true
              validations:
                - rule: Email format is valid
                  rule_type: validate_email
                  error_message: Invalid email format
            - id: in004
              slot_id: e001.at104.in004
              contextual_id: go001.lo001.in004
              source:
                type: user
                description: Mobile
              required: true
            - id: in005
              slot_id: e001.at105.in005
              contextual_id: go001.lo001.in005
              source:
                type: user
                description: Address
              required: true
        output_stack:
          description: "Outputs from lead capture"
          outputs:
            - id: out001
              slot_id: executionstatus.out001
              contextual_id: go001.lo001.out001
              source:
                type: system
                description: Success or failure of lo001
        execution_pathway:
          type: sequential
          next_lo: lo002
      - id: "lo002"
        contextual_id: "go001.lo002"
        name: "Update Lead Status"
        workflow_source: "Terminal"
        function_type: "Update"
        agent_stack:
          agents:
            - role: "r001"
              rights: ["Execute"]
        input_stack:
          description: "Sales executive updates lead status"
          inputs:
            - id: in006
              slot_id: e001.at101.in006
              contextual_id: go001.lo002.in006
              source:
                type: user
                description: Lead ID
              required: true
            - id: in007
              slot_id: e001.at106.in007
              contextual_id: go001.lo002.in007
              source:
                type: user
                description: Status of lead
              required: true
              data_type: enum
              ui_control: oj-select-single
              allowed_values:
                - Warm
                - Cold
                - Hot
            - id: in008
              slot_id: e001.at107.in008
              contextual_id: go001.lo002.in008
              source:
                type: user
                description: Conversation Details
              required: false
        output_stack:
          description: "Outputs from lead status update"
          outputs:
            - id: out002
              slot_id: executionstatus.out002
              contextual_id: go001.lo002.out002
              source:
                type: system
                description: Success or failure of lo002
```