"""
Database session for auth tests.

This module provides a database session for auth tests that can be used
when running tests outside of Docker.
"""

import os
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker, Session

# Database configuration for tests
# Connect to the database in the Docker container
# Using localhost since we're running outside of the Docker network
DATABASE_URL = os.getenv("DATABASE_URL", "postgresql://postgres:workflow_postgres_secure_password@localhost:5433/workflow_system")

# Create database engine and session
engine = create_engine(DATABASE_URL)
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

def get_db():
    """Get database session for tests."""
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()
