{"timestamp": "2025-06-23T14:07:59.259900", "endpoint": "parse-validate-mongosave/entity-relationships", "input": {"natural_language": "Product belongs to Category, Category has many Products", "tenant_id": "tenant_123", "edit_draft": false, "edit_temp": false, "edit_production": false}, "output": {"success": true, "relationship_results": [], "operation": "parse_validate_mongosave", "total_relationships": 0}, "status": "success"}