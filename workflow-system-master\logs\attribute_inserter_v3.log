2025-06-23 11:26:31,802 - inserters.v3.roles.attribute_inserter - INFO - Starting deploy_single_attribute_validation_to_workflow_temp for validation ID: 4
2025-06-23 11:28:18,037 - inserters.v3.roles.attribute_inserter - INFO - Starting deploy_single_attribute_validation_to_workflow_temp for validation ID: 4
2025-06-23 14:03:48,456 - inserters.v3.roles.attribute_inserter - INFO - Starting deploy_single_attribute_validation_to_workflow_temp for validation ID: 1
2025-06-23 14:03:48,465 - inserters.v3.roles.attribute_inserter - INFO - Starting insert_attribute_validation_to_workflow_temp for attribute: E8.At1
2025-06-23 14:03:48,465 - inserters.v3.roles.attribute_inserter - INFO - Field validation passed: 2/2 required fields, 13/13 optional fields
2025-06-23 14:03:48,479 - inserters.v3.roles.attribute_inserter - INFO - Incremented validation ID from 1 to 186
2025-06-23 14:03:48,489 - inserters.v3.roles.attribute_inserter - INFO - Generated next attribute ID: E8.At1 -> E8.At22
2025-06-23 14:03:48,490 - inserters.v3.roles.attribute_inserter - INFO - Incremented attribute ID from E8.At1 to E8.At22
2025-06-23 14:03:48,490 - inserters.v3.roles.attribute_inserter - INFO - Additional MongoDB fields preserved: {'original_created_at': '2025-06-18T16:49:30.448481', 'original_updated_at': '2025-06-18T16:49:30.448484', 'original_created_by': 'system', 'original_updated_by': 'system', 'original_validation_id': 1}
2025-06-23 14:03:48,491 - inserters.v3.roles.attribute_inserter - INFO - Successfully inserted attribute validation to workflow_temp with ID: 42
2025-06-23 14:03:48,494 - inserters.v3.roles.attribute_inserter - INFO - Updated MongoDB status to deployed_to_temp for validation ID: 1
2025-06-23 14:03:48,618 - inserters.v3.roles.attribute_inserter - INFO - Starting process_mongo_attribute_validations_to_workflow_temp
2025-06-23 14:03:48,620 - inserters.v3.roles.attribute_inserter - INFO - Found 8 attribute validations with status 'draft' in MongoDB
2025-06-23 14:03:48,627 - inserters.v3.roles.attribute_inserter - INFO - Starting insert_attribute_validation_to_workflow_temp for attribute: E8.At4
2025-06-23 14:03:48,627 - inserters.v3.roles.attribute_inserter - INFO - Field validation passed: 2/2 required fields, 13/13 optional fields
2025-06-23 14:03:48,644 - inserters.v3.roles.attribute_inserter - INFO - Incremented validation ID from 2 to 196
2025-06-23 14:03:48,654 - inserters.v3.roles.attribute_inserter - INFO - Generated next attribute ID: E8.At4 -> E8.At23
2025-06-23 14:03:48,654 - inserters.v3.roles.attribute_inserter - INFO - Incremented attribute ID from E8.At4 to E8.At23
2025-06-23 14:03:48,655 - inserters.v3.roles.attribute_inserter - INFO - Additional MongoDB fields preserved: {'original_created_at': '2025-06-19T17:27:38.404018', 'original_updated_at': '2025-06-19T17:27:38.404025', 'original_created_by': 'system', 'original_updated_by': 'system', 'original_validation_id': 2}
2025-06-23 14:03:48,656 - inserters.v3.roles.attribute_inserter - INFO - Successfully inserted attribute validation to workflow_temp with ID: 43
2025-06-23 14:03:48,659 - inserters.v3.roles.attribute_inserter - INFO - Updated MongoDB status to deployed_to_temp for validation ID: 196
2025-06-23 14:03:48,665 - inserters.v3.roles.attribute_inserter - INFO - Starting insert_attribute_validation_to_workflow_temp for attribute: E8.At4
2025-06-23 14:03:48,665 - inserters.v3.roles.attribute_inserter - INFO - Field validation passed: 2/2 required fields, 13/13 optional fields
2025-06-23 14:03:48,680 - inserters.v3.roles.attribute_inserter - INFO - Incremented validation ID from 6 to 206
2025-06-23 14:03:48,690 - inserters.v3.roles.attribute_inserter - INFO - Generated next attribute ID: E8.At4 -> E8.At24
2025-06-23 14:03:48,691 - inserters.v3.roles.attribute_inserter - INFO - Incremented attribute ID from E8.At4 to E8.At24
2025-06-23 14:03:48,691 - inserters.v3.roles.attribute_inserter - INFO - Additional MongoDB fields preserved: {'original_created_at': '2025-06-20T05:15:31.135351', 'original_updated_at': '2025-06-20T05:15:31.135357', 'original_created_by': 'system', 'original_updated_by': 'system', 'original_validation_id': 6}
2025-06-23 14:03:48,692 - inserters.v3.roles.attribute_inserter - INFO - Successfully inserted attribute validation to workflow_temp with ID: 44
2025-06-23 14:03:48,694 - inserters.v3.roles.attribute_inserter - INFO - Updated MongoDB status to deployed_to_temp for validation ID: 206
2025-06-23 14:03:48,701 - inserters.v3.roles.attribute_inserter - INFO - Starting insert_attribute_validation_to_workflow_temp for attribute: E8.At1
2025-06-23 14:03:48,701 - inserters.v3.roles.attribute_inserter - INFO - Field validation passed: 2/2 required fields, 13/13 optional fields
2025-06-23 14:03:48,713 - inserters.v3.roles.attribute_inserter - INFO - Incremented validation ID from 5 to 216
2025-06-23 14:03:48,723 - inserters.v3.roles.attribute_inserter - INFO - Generated next attribute ID: E8.At1 -> E8.At25
2025-06-23 14:03:48,723 - inserters.v3.roles.attribute_inserter - INFO - Incremented attribute ID from E8.At1 to E8.At25
2025-06-23 14:03:48,723 - inserters.v3.roles.attribute_inserter - INFO - Additional MongoDB fields preserved: {'original_created_at': '2025-06-20T05:15:31.135903', 'original_updated_at': '2025-06-20T05:15:31.135906', 'original_created_by': 'system', 'original_updated_by': 'system', 'original_validation_id': 5}
2025-06-23 14:03:48,724 - inserters.v3.roles.attribute_inserter - INFO - Successfully inserted attribute validation to workflow_temp with ID: 45
2025-06-23 14:03:48,726 - inserters.v3.roles.attribute_inserter - INFO - Updated MongoDB status to deployed_to_temp for validation ID: 216
2025-06-23 14:03:48,732 - inserters.v3.roles.attribute_inserter - INFO - Starting insert_attribute_validation_to_workflow_temp for attribute: E8.At4
2025-06-23 14:03:48,732 - inserters.v3.roles.attribute_inserter - INFO - Field validation passed: 2/2 required fields, 13/13 optional fields
2025-06-23 14:03:48,747 - inserters.v3.roles.attribute_inserter - INFO - Incremented validation ID from 7 to 226
2025-06-23 14:03:48,755 - inserters.v3.roles.attribute_inserter - INFO - Generated next attribute ID: E8.At4 -> E8.At26
2025-06-23 14:03:48,755 - inserters.v3.roles.attribute_inserter - INFO - Incremented attribute ID from E8.At4 to E8.At26
2025-06-23 14:03:48,756 - inserters.v3.roles.attribute_inserter - INFO - Additional MongoDB fields preserved: {'original_created_at': '2025-06-20T05:37:48.522400', 'original_updated_at': '2025-06-20T05:37:48.522404', 'original_created_by': 'system', 'original_updated_by': 'system', 'original_validation_id': 7}
2025-06-23 14:03:48,757 - inserters.v3.roles.attribute_inserter - INFO - Successfully inserted attribute validation to workflow_temp with ID: 46
2025-06-23 14:03:48,759 - inserters.v3.roles.attribute_inserter - INFO - Updated MongoDB status to deployed_to_temp for validation ID: 226
2025-06-23 14:03:48,766 - inserters.v3.roles.attribute_inserter - INFO - Starting insert_attribute_validation_to_workflow_temp for attribute: E7.At1
2025-06-23 14:03:48,766 - inserters.v3.roles.attribute_inserter - INFO - Field validation passed: 2/2 required fields, 13/13 optional fields
2025-06-23 14:03:48,780 - inserters.v3.roles.attribute_inserter - INFO - Incremented validation ID from 1 to 236
2025-06-23 14:03:48,789 - inserters.v3.roles.attribute_inserter - INFO - Generated next attribute ID: E7.At1 -> E7.At20
2025-06-23 14:03:48,789 - inserters.v3.roles.attribute_inserter - INFO - Incremented attribute ID from E7.At1 to E7.At20
2025-06-23 14:03:48,790 - inserters.v3.roles.attribute_inserter - INFO - Additional MongoDB fields preserved: {'original_created_at': '2025-06-20T07:05:40.927340', 'original_updated_at': '2025-06-20T07:05:40.927345', 'original_created_by': 'system', 'original_updated_by': 'system', 'original_validation_id': 1}
2025-06-23 14:03:48,791 - inserters.v3.roles.attribute_inserter - INFO - Successfully inserted attribute validation to workflow_temp with ID: 47
2025-06-23 14:03:48,794 - inserters.v3.roles.attribute_inserter - INFO - Updated MongoDB status to deployed_to_temp for validation ID: 236
2025-06-23 14:03:48,799 - inserters.v3.roles.attribute_inserter - INFO - Starting insert_attribute_validation_to_workflow_temp for attribute: E7.At2
2025-06-23 14:03:48,801 - inserters.v3.roles.attribute_inserter - INFO - Field validation passed: 2/2 required fields, 13/13 optional fields
2025-06-23 14:03:48,814 - inserters.v3.roles.attribute_inserter - INFO - Incremented validation ID from 2 to 246
2025-06-23 14:03:48,822 - inserters.v3.roles.attribute_inserter - INFO - Generated next attribute ID: E7.At2 -> E7.At21
2025-06-23 14:03:48,822 - inserters.v3.roles.attribute_inserter - INFO - Incremented attribute ID from E7.At2 to E7.At21
2025-06-23 14:03:48,822 - inserters.v3.roles.attribute_inserter - INFO - Additional MongoDB fields preserved: {'original_created_at': '2025-06-20T07:05:40.932582', 'original_updated_at': '2025-06-20T07:05:40.932597', 'original_created_by': 'system', 'original_updated_by': 'system', 'original_validation_id': 2}
2025-06-23 14:03:48,823 - inserters.v3.roles.attribute_inserter - INFO - Successfully inserted attribute validation to workflow_temp with ID: 48
2025-06-23 14:03:48,825 - inserters.v3.roles.attribute_inserter - INFO - Updated MongoDB status to deployed_to_temp for validation ID: 246
2025-06-23 14:03:48,831 - inserters.v3.roles.attribute_inserter - INFO - Starting insert_attribute_validation_to_workflow_temp for attribute: E7.At3
2025-06-23 14:03:48,831 - inserters.v3.roles.attribute_inserter - INFO - Field validation passed: 2/2 required fields, 13/13 optional fields
2025-06-23 14:03:48,844 - inserters.v3.roles.attribute_inserter - INFO - Incremented validation ID from 3 to 256
2025-06-23 14:03:48,850 - inserters.v3.roles.attribute_inserter - INFO - Generated next attribute ID: E7.At3 -> E7.At22
2025-06-23 14:03:48,850 - inserters.v3.roles.attribute_inserter - INFO - Incremented attribute ID from E7.At3 to E7.At22
2025-06-23 14:03:48,850 - inserters.v3.roles.attribute_inserter - INFO - Additional MongoDB fields preserved: {'original_created_at': '2025-06-20T07:05:40.934871', 'original_updated_at': '2025-06-20T07:05:40.934878', 'original_created_by': 'system', 'original_updated_by': 'system', 'original_validation_id': 3}
2025-06-23 14:03:48,851 - inserters.v3.roles.attribute_inserter - INFO - Successfully inserted attribute validation to workflow_temp with ID: 49
2025-06-23 14:03:48,852 - inserters.v3.roles.attribute_inserter - INFO - Updated MongoDB status to deployed_to_temp for validation ID: 256
2025-06-23 14:03:48,856 - inserters.v3.roles.attribute_inserter - INFO - Starting insert_attribute_validation_to_workflow_temp for attribute: E7.At4
2025-06-23 14:03:48,856 - inserters.v3.roles.attribute_inserter - INFO - Field validation passed: 2/2 required fields, 13/13 optional fields
2025-06-23 14:03:48,867 - inserters.v3.roles.attribute_inserter - INFO - Incremented validation ID from 4 to 266
2025-06-23 14:03:48,874 - inserters.v3.roles.attribute_inserter - INFO - Generated next attribute ID: E7.At4 -> E7.At23
2025-06-23 14:03:48,874 - inserters.v3.roles.attribute_inserter - INFO - Incremented attribute ID from E7.At4 to E7.At23
2025-06-23 14:03:48,874 - inserters.v3.roles.attribute_inserter - INFO - Additional MongoDB fields preserved: {'original_created_at': '2025-06-20T07:05:40.936694', 'original_updated_at': '2025-06-20T07:05:40.936701', 'original_created_by': 'system', 'original_updated_by': 'system', 'original_validation_id': 4}
2025-06-23 14:03:48,875 - inserters.v3.roles.attribute_inserter - INFO - Successfully inserted attribute validation to workflow_temp with ID: 50
2025-06-23 14:03:48,877 - inserters.v3.roles.attribute_inserter - INFO - Updated MongoDB status to deployed_to_temp for validation ID: 266
2025-06-23 14:03:48,877 - inserters.v3.roles.attribute_inserter - INFO - Completed process_mongo_attribute_validations_to_workflow_temp: 8 successful, 0 failed
2025-06-23 14:08:59,909 - inserters.v3.roles.attribute_inserter - INFO - Starting deploy_single_attribute_validation_to_workflow_temp for validation ID: 1
2025-06-23 14:08:59,918 - inserters.v3.roles.attribute_inserter - INFO - Starting insert_attribute_validation_to_workflow_temp for attribute: E8.At1
2025-06-23 14:08:59,919 - inserters.v3.roles.attribute_inserter - INFO - Field validation passed: 2/2 required fields, 13/13 optional fields
2025-06-23 14:08:59,935 - inserters.v3.roles.attribute_inserter - INFO - Incremented validation ID from 1 to 276
2025-06-23 14:08:59,945 - inserters.v3.roles.attribute_inserter - INFO - Generated next attribute ID: E8.At1 -> E8.At27
2025-06-23 14:08:59,945 - inserters.v3.roles.attribute_inserter - INFO - Incremented attribute ID from E8.At1 to E8.At27
2025-06-23 14:08:59,945 - inserters.v3.roles.attribute_inserter - INFO - Additional MongoDB fields preserved: {'original_created_at': '2025-06-18T16:49:30.448481', 'original_updated_at': '2025-06-18T16:49:30.448484', 'original_created_by': 'system', 'original_updated_by': 'system', 'original_validation_id': 1}
2025-06-23 14:08:59,946 - inserters.v3.roles.attribute_inserter - INFO - Successfully inserted attribute validation to workflow_temp with ID: 51
2025-06-23 14:08:59,949 - inserters.v3.roles.attribute_inserter - INFO - Updated MongoDB status to deployed_to_temp for validation ID: 1
2025-06-23 14:09:00,080 - inserters.v3.roles.attribute_inserter - INFO - Starting process_mongo_attribute_validations_to_workflow_temp
2025-06-23 14:09:00,083 - inserters.v3.roles.attribute_inserter - INFO - Found 8 attribute validations with status 'draft' in MongoDB
2025-06-23 14:09:00,089 - inserters.v3.roles.attribute_inserter - INFO - Starting insert_attribute_validation_to_workflow_temp for attribute: E8.At4
2025-06-23 14:09:00,089 - inserters.v3.roles.attribute_inserter - INFO - Field validation passed: 2/2 required fields, 13/13 optional fields
2025-06-23 14:09:00,105 - inserters.v3.roles.attribute_inserter - INFO - Incremented validation ID from 2 to 286
2025-06-23 14:09:00,115 - inserters.v3.roles.attribute_inserter - INFO - Generated next attribute ID: E8.At4 -> E8.At28
2025-06-23 14:09:00,115 - inserters.v3.roles.attribute_inserter - INFO - Incremented attribute ID from E8.At4 to E8.At28
2025-06-23 14:09:00,115 - inserters.v3.roles.attribute_inserter - INFO - Additional MongoDB fields preserved: {'original_created_at': '2025-06-19T17:27:38.404018', 'original_updated_at': '2025-06-19T17:27:38.404025', 'original_created_by': 'system', 'original_updated_by': 'system', 'original_validation_id': 2}
2025-06-23 14:09:00,117 - inserters.v3.roles.attribute_inserter - INFO - Successfully inserted attribute validation to workflow_temp with ID: 52
2025-06-23 14:09:00,119 - inserters.v3.roles.attribute_inserter - INFO - Updated MongoDB status to deployed_to_temp for validation ID: 286
2025-06-23 14:09:00,124 - inserters.v3.roles.attribute_inserter - INFO - Starting insert_attribute_validation_to_workflow_temp for attribute: E8.At4
2025-06-23 14:09:00,124 - inserters.v3.roles.attribute_inserter - INFO - Field validation passed: 2/2 required fields, 13/13 optional fields
2025-06-23 14:09:00,140 - inserters.v3.roles.attribute_inserter - INFO - Incremented validation ID from 6 to 296
2025-06-23 14:09:00,146 - inserters.v3.roles.attribute_inserter - INFO - Generated next attribute ID: E8.At4 -> E8.At29
2025-06-23 14:09:00,146 - inserters.v3.roles.attribute_inserter - INFO - Incremented attribute ID from E8.At4 to E8.At29
2025-06-23 14:09:00,146 - inserters.v3.roles.attribute_inserter - INFO - Additional MongoDB fields preserved: {'original_created_at': '2025-06-20T05:15:31.135351', 'original_updated_at': '2025-06-20T05:15:31.135357', 'original_created_by': 'system', 'original_updated_by': 'system', 'original_validation_id': 6}
2025-06-23 14:09:00,147 - inserters.v3.roles.attribute_inserter - INFO - Successfully inserted attribute validation to workflow_temp with ID: 53
2025-06-23 14:09:00,150 - inserters.v3.roles.attribute_inserter - INFO - Updated MongoDB status to deployed_to_temp for validation ID: 296
2025-06-23 14:09:00,156 - inserters.v3.roles.attribute_inserter - INFO - Starting insert_attribute_validation_to_workflow_temp for attribute: E8.At1
2025-06-23 14:09:00,156 - inserters.v3.roles.attribute_inserter - INFO - Field validation passed: 2/2 required fields, 13/13 optional fields
2025-06-23 14:09:00,168 - inserters.v3.roles.attribute_inserter - INFO - Incremented validation ID from 5 to 306
2025-06-23 14:09:00,177 - inserters.v3.roles.attribute_inserter - INFO - Generated next attribute ID: E8.At1 -> E8.At30
2025-06-23 14:09:00,177 - inserters.v3.roles.attribute_inserter - INFO - Incremented attribute ID from E8.At1 to E8.At30
2025-06-23 14:09:00,177 - inserters.v3.roles.attribute_inserter - INFO - Additional MongoDB fields preserved: {'original_created_at': '2025-06-20T05:15:31.135903', 'original_updated_at': '2025-06-20T05:15:31.135906', 'original_created_by': 'system', 'original_updated_by': 'system', 'original_validation_id': 5}
2025-06-23 14:09:00,178 - inserters.v3.roles.attribute_inserter - INFO - Successfully inserted attribute validation to workflow_temp with ID: 54
2025-06-23 14:09:00,180 - inserters.v3.roles.attribute_inserter - INFO - Updated MongoDB status to deployed_to_temp for validation ID: 306
2025-06-23 14:09:00,185 - inserters.v3.roles.attribute_inserter - INFO - Starting insert_attribute_validation_to_workflow_temp for attribute: E8.At4
2025-06-23 14:09:00,185 - inserters.v3.roles.attribute_inserter - INFO - Field validation passed: 2/2 required fields, 13/13 optional fields
2025-06-23 14:09:00,197 - inserters.v3.roles.attribute_inserter - INFO - Incremented validation ID from 7 to 316
2025-06-23 14:09:00,205 - inserters.v3.roles.attribute_inserter - INFO - Generated next attribute ID: E8.At4 -> E8.At31
2025-06-23 14:09:00,205 - inserters.v3.roles.attribute_inserter - INFO - Incremented attribute ID from E8.At4 to E8.At31
2025-06-23 14:09:00,205 - inserters.v3.roles.attribute_inserter - INFO - Additional MongoDB fields preserved: {'original_created_at': '2025-06-20T05:37:48.522400', 'original_updated_at': '2025-06-20T05:37:48.522404', 'original_created_by': 'system', 'original_updated_by': 'system', 'original_validation_id': 7}
2025-06-23 14:09:00,207 - inserters.v3.roles.attribute_inserter - INFO - Successfully inserted attribute validation to workflow_temp with ID: 55
2025-06-23 14:09:00,209 - inserters.v3.roles.attribute_inserter - INFO - Updated MongoDB status to deployed_to_temp for validation ID: 316
2025-06-23 14:09:00,215 - inserters.v3.roles.attribute_inserter - INFO - Starting insert_attribute_validation_to_workflow_temp for attribute: E7.At1
2025-06-23 14:09:00,215 - inserters.v3.roles.attribute_inserter - INFO - Field validation passed: 2/2 required fields, 13/13 optional fields
2025-06-23 14:09:00,228 - inserters.v3.roles.attribute_inserter - INFO - Incremented validation ID from 1 to 326
2025-06-23 14:09:00,236 - inserters.v3.roles.attribute_inserter - INFO - Generated next attribute ID: E7.At1 -> E7.At24
2025-06-23 14:09:00,236 - inserters.v3.roles.attribute_inserter - INFO - Incremented attribute ID from E7.At1 to E7.At24
2025-06-23 14:09:00,236 - inserters.v3.roles.attribute_inserter - INFO - Additional MongoDB fields preserved: {'original_created_at': '2025-06-20T07:05:40.927340', 'original_updated_at': '2025-06-20T07:05:40.927345', 'original_created_by': 'system', 'original_updated_by': 'system', 'original_validation_id': 1}
2025-06-23 14:09:00,237 - inserters.v3.roles.attribute_inserter - INFO - Successfully inserted attribute validation to workflow_temp with ID: 56
2025-06-23 14:09:00,239 - inserters.v3.roles.attribute_inserter - INFO - Updated MongoDB status to deployed_to_temp for validation ID: 326
2025-06-23 14:09:00,245 - inserters.v3.roles.attribute_inserter - INFO - Starting insert_attribute_validation_to_workflow_temp for attribute: E7.At2
2025-06-23 14:09:00,245 - inserters.v3.roles.attribute_inserter - INFO - Field validation passed: 2/2 required fields, 13/13 optional fields
2025-06-23 14:09:00,258 - inserters.v3.roles.attribute_inserter - INFO - Incremented validation ID from 2 to 336
2025-06-23 14:09:00,266 - inserters.v3.roles.attribute_inserter - INFO - Generated next attribute ID: E7.At2 -> E7.At25
2025-06-23 14:09:00,266 - inserters.v3.roles.attribute_inserter - INFO - Incremented attribute ID from E7.At2 to E7.At25
2025-06-23 14:09:00,266 - inserters.v3.roles.attribute_inserter - INFO - Additional MongoDB fields preserved: {'original_created_at': '2025-06-20T07:05:40.932582', 'original_updated_at': '2025-06-20T07:05:40.932597', 'original_created_by': 'system', 'original_updated_by': 'system', 'original_validation_id': 2}
2025-06-23 14:09:00,267 - inserters.v3.roles.attribute_inserter - INFO - Successfully inserted attribute validation to workflow_temp with ID: 57
2025-06-23 14:09:00,268 - inserters.v3.roles.attribute_inserter - INFO - Updated MongoDB status to deployed_to_temp for validation ID: 336
2025-06-23 14:09:00,274 - inserters.v3.roles.attribute_inserter - INFO - Starting insert_attribute_validation_to_workflow_temp for attribute: E7.At3
2025-06-23 14:09:00,274 - inserters.v3.roles.attribute_inserter - INFO - Field validation passed: 2/2 required fields, 13/13 optional fields
2025-06-23 14:09:00,284 - inserters.v3.roles.attribute_inserter - INFO - Incremented validation ID from 3 to 346
2025-06-23 14:09:00,291 - inserters.v3.roles.attribute_inserter - INFO - Generated next attribute ID: E7.At3 -> E7.At26
2025-06-23 14:09:00,291 - inserters.v3.roles.attribute_inserter - INFO - Incremented attribute ID from E7.At3 to E7.At26
2025-06-23 14:09:00,291 - inserters.v3.roles.attribute_inserter - INFO - Additional MongoDB fields preserved: {'original_created_at': '2025-06-20T07:05:40.934871', 'original_updated_at': '2025-06-20T07:05:40.934878', 'original_created_by': 'system', 'original_updated_by': 'system', 'original_validation_id': 3}
2025-06-23 14:09:00,293 - inserters.v3.roles.attribute_inserter - INFO - Successfully inserted attribute validation to workflow_temp with ID: 58
2025-06-23 14:09:00,294 - inserters.v3.roles.attribute_inserter - INFO - Updated MongoDB status to deployed_to_temp for validation ID: 346
2025-06-23 14:09:00,299 - inserters.v3.roles.attribute_inserter - INFO - Starting insert_attribute_validation_to_workflow_temp for attribute: E7.At4
2025-06-23 14:09:00,299 - inserters.v3.roles.attribute_inserter - INFO - Field validation passed: 2/2 required fields, 13/13 optional fields
2025-06-23 14:09:00,311 - inserters.v3.roles.attribute_inserter - INFO - Incremented validation ID from 4 to 356
2025-06-23 14:09:00,319 - inserters.v3.roles.attribute_inserter - INFO - Generated next attribute ID: E7.At4 -> E7.At27
2025-06-23 14:09:00,319 - inserters.v3.roles.attribute_inserter - INFO - Incremented attribute ID from E7.At4 to E7.At27
2025-06-23 14:09:00,319 - inserters.v3.roles.attribute_inserter - INFO - Additional MongoDB fields preserved: {'original_created_at': '2025-06-20T07:05:40.936694', 'original_updated_at': '2025-06-20T07:05:40.936701', 'original_created_by': 'system', 'original_updated_by': 'system', 'original_validation_id': 4}
2025-06-23 14:09:00,321 - inserters.v3.roles.attribute_inserter - INFO - Successfully inserted attribute validation to workflow_temp with ID: 59
2025-06-23 14:09:00,322 - inserters.v3.roles.attribute_inserter - INFO - Updated MongoDB status to deployed_to_temp for validation ID: 356
2025-06-23 14:09:00,322 - inserters.v3.roles.attribute_inserter - INFO - Completed process_mongo_attribute_validations_to_workflow_temp: 8 successful, 0 failed
2025-06-23 14:32:32,120 - inserters.v3.roles.attribute_inserter - INFO - Starting deploy_single_attribute_validation_to_workflow_temp for validation ID: 1
2025-06-23 14:32:32,141 - inserters.v3.roles.attribute_inserter - INFO - Starting insert_attribute_validation_to_workflow_temp for attribute: E8.At1
2025-06-23 14:32:32,141 - inserters.v3.roles.attribute_inserter - INFO - Field validation passed: 2/2 required fields, 13/13 optional fields
2025-06-23 14:32:32,156 - inserters.v3.roles.attribute_inserter - INFO - Incremented validation ID from 1 to 366
2025-06-23 14:32:32,165 - inserters.v3.roles.attribute_inserter - INFO - Generated next attribute ID: E8.At1 -> E8.At32
2025-06-23 14:32:32,165 - inserters.v3.roles.attribute_inserter - INFO - Incremented attribute ID from E8.At1 to E8.At32
2025-06-23 14:32:32,165 - inserters.v3.roles.attribute_inserter - INFO - Additional MongoDB fields preserved: {'original_created_at': '2025-06-18T16:49:30.448481', 'original_updated_at': '2025-06-18T16:49:30.448484', 'original_created_by': 'system', 'original_updated_by': 'system', 'original_validation_id': 1}
2025-06-23 14:32:32,167 - inserters.v3.roles.attribute_inserter - INFO - Successfully inserted attribute validation to workflow_temp with ID: 60
2025-06-23 14:32:32,170 - inserters.v3.roles.attribute_inserter - INFO - Updated MongoDB status to deployed_to_temp for validation ID: 1
2025-06-23 14:32:32,327 - inserters.v3.roles.attribute_inserter - INFO - Starting process_mongo_attribute_validations_to_workflow_temp
2025-06-23 14:32:32,330 - inserters.v3.roles.attribute_inserter - INFO - Found 8 attribute validations with status 'draft' in MongoDB
2025-06-23 14:32:32,335 - inserters.v3.roles.attribute_inserter - INFO - Starting insert_attribute_validation_to_workflow_temp for attribute: E8.At4
2025-06-23 14:32:32,335 - inserters.v3.roles.attribute_inserter - INFO - Field validation passed: 2/2 required fields, 13/13 optional fields
2025-06-23 14:32:32,349 - inserters.v3.roles.attribute_inserter - INFO - Incremented validation ID from 2 to 376
2025-06-23 14:32:32,359 - inserters.v3.roles.attribute_inserter - INFO - Generated next attribute ID: E8.At4 -> E8.At33
2025-06-23 14:32:32,359 - inserters.v3.roles.attribute_inserter - INFO - Incremented attribute ID from E8.At4 to E8.At33
2025-06-23 14:32:32,359 - inserters.v3.roles.attribute_inserter - INFO - Additional MongoDB fields preserved: {'original_created_at': '2025-06-19T17:27:38.404018', 'original_updated_at': '2025-06-19T17:27:38.404025', 'original_created_by': 'system', 'original_updated_by': 'system', 'original_validation_id': 2}
2025-06-23 14:32:32,361 - inserters.v3.roles.attribute_inserter - INFO - Successfully inserted attribute validation to workflow_temp with ID: 61
2025-06-23 14:32:32,363 - inserters.v3.roles.attribute_inserter - INFO - Updated MongoDB status to deployed_to_temp for validation ID: 376
2025-06-23 14:32:32,368 - inserters.v3.roles.attribute_inserter - INFO - Starting insert_attribute_validation_to_workflow_temp for attribute: E8.At4
2025-06-23 14:32:32,368 - inserters.v3.roles.attribute_inserter - INFO - Field validation passed: 2/2 required fields, 13/13 optional fields
2025-06-23 14:32:32,382 - inserters.v3.roles.attribute_inserter - INFO - Incremented validation ID from 6 to 386
2025-06-23 14:32:32,391 - inserters.v3.roles.attribute_inserter - INFO - Generated next attribute ID: E8.At4 -> E8.At34
2025-06-23 14:32:32,391 - inserters.v3.roles.attribute_inserter - INFO - Incremented attribute ID from E8.At4 to E8.At34
2025-06-23 14:32:32,391 - inserters.v3.roles.attribute_inserter - INFO - Additional MongoDB fields preserved: {'original_created_at': '2025-06-20T05:15:31.135351', 'original_updated_at': '2025-06-20T05:15:31.135357', 'original_created_by': 'system', 'original_updated_by': 'system', 'original_validation_id': 6}
2025-06-23 14:32:32,392 - inserters.v3.roles.attribute_inserter - INFO - Successfully inserted attribute validation to workflow_temp with ID: 62
2025-06-23 14:32:32,394 - inserters.v3.roles.attribute_inserter - INFO - Updated MongoDB status to deployed_to_temp for validation ID: 386
2025-06-23 14:32:32,398 - inserters.v3.roles.attribute_inserter - INFO - Starting insert_attribute_validation_to_workflow_temp for attribute: E8.At1
2025-06-23 14:32:32,398 - inserters.v3.roles.attribute_inserter - INFO - Field validation passed: 2/2 required fields, 13/13 optional fields
2025-06-23 14:32:32,408 - inserters.v3.roles.attribute_inserter - INFO - Incremented validation ID from 5 to 396
2025-06-23 14:32:32,416 - inserters.v3.roles.attribute_inserter - INFO - Generated next attribute ID: E8.At1 -> E8.At35
2025-06-23 14:32:32,416 - inserters.v3.roles.attribute_inserter - INFO - Incremented attribute ID from E8.At1 to E8.At35
2025-06-23 14:32:32,416 - inserters.v3.roles.attribute_inserter - INFO - Additional MongoDB fields preserved: {'original_created_at': '2025-06-20T05:15:31.135903', 'original_updated_at': '2025-06-20T05:15:31.135906', 'original_created_by': 'system', 'original_updated_by': 'system', 'original_validation_id': 5}
2025-06-23 14:32:32,418 - inserters.v3.roles.attribute_inserter - INFO - Successfully inserted attribute validation to workflow_temp with ID: 63
2025-06-23 14:32:32,420 - inserters.v3.roles.attribute_inserter - INFO - Updated MongoDB status to deployed_to_temp for validation ID: 396
2025-06-23 14:32:32,424 - inserters.v3.roles.attribute_inserter - INFO - Starting insert_attribute_validation_to_workflow_temp for attribute: E8.At4
2025-06-23 14:32:32,424 - inserters.v3.roles.attribute_inserter - INFO - Field validation passed: 2/2 required fields, 13/13 optional fields
2025-06-23 14:32:32,434 - inserters.v3.roles.attribute_inserter - INFO - Incremented validation ID from 7 to 406
2025-06-23 14:32:32,443 - inserters.v3.roles.attribute_inserter - INFO - Generated next attribute ID: E8.At4 -> E8.At36
2025-06-23 14:32:32,443 - inserters.v3.roles.attribute_inserter - INFO - Incremented attribute ID from E8.At4 to E8.At36
2025-06-23 14:32:32,443 - inserters.v3.roles.attribute_inserter - INFO - Additional MongoDB fields preserved: {'original_created_at': '2025-06-20T05:37:48.522400', 'original_updated_at': '2025-06-20T05:37:48.522404', 'original_created_by': 'system', 'original_updated_by': 'system', 'original_validation_id': 7}
2025-06-23 14:32:32,445 - inserters.v3.roles.attribute_inserter - INFO - Successfully inserted attribute validation to workflow_temp with ID: 64
2025-06-23 14:32:32,447 - inserters.v3.roles.attribute_inserter - INFO - Updated MongoDB status to deployed_to_temp for validation ID: 406
2025-06-23 14:32:32,451 - inserters.v3.roles.attribute_inserter - INFO - Starting insert_attribute_validation_to_workflow_temp for attribute: E7.At1
2025-06-23 14:32:32,451 - inserters.v3.roles.attribute_inserter - INFO - Field validation passed: 2/2 required fields, 13/13 optional fields
2025-06-23 14:32:32,462 - inserters.v3.roles.attribute_inserter - INFO - Incremented validation ID from 1 to 416
2025-06-23 14:32:32,470 - inserters.v3.roles.attribute_inserter - INFO - Generated next attribute ID: E7.At1 -> E7.At28
2025-06-23 14:32:32,470 - inserters.v3.roles.attribute_inserter - INFO - Incremented attribute ID from E7.At1 to E7.At28
2025-06-23 14:32:32,470 - inserters.v3.roles.attribute_inserter - INFO - Additional MongoDB fields preserved: {'original_created_at': '2025-06-20T07:05:40.927340', 'original_updated_at': '2025-06-20T07:05:40.927345', 'original_created_by': 'system', 'original_updated_by': 'system', 'original_validation_id': 1}
2025-06-23 14:32:32,471 - inserters.v3.roles.attribute_inserter - INFO - Successfully inserted attribute validation to workflow_temp with ID: 65
2025-06-23 14:32:32,473 - inserters.v3.roles.attribute_inserter - INFO - Updated MongoDB status to deployed_to_temp for validation ID: 416
2025-06-23 14:32:32,477 - inserters.v3.roles.attribute_inserter - INFO - Starting insert_attribute_validation_to_workflow_temp for attribute: E7.At2
2025-06-23 14:32:32,477 - inserters.v3.roles.attribute_inserter - INFO - Field validation passed: 2/2 required fields, 13/13 optional fields
2025-06-23 14:32:32,488 - inserters.v3.roles.attribute_inserter - INFO - Incremented validation ID from 2 to 426
2025-06-23 14:32:32,495 - inserters.v3.roles.attribute_inserter - INFO - Generated next attribute ID: E7.At2 -> E7.At29
2025-06-23 14:32:32,495 - inserters.v3.roles.attribute_inserter - INFO - Incremented attribute ID from E7.At2 to E7.At29
2025-06-23 14:32:32,495 - inserters.v3.roles.attribute_inserter - INFO - Additional MongoDB fields preserved: {'original_created_at': '2025-06-20T07:05:40.932582', 'original_updated_at': '2025-06-20T07:05:40.932597', 'original_created_by': 'system', 'original_updated_by': 'system', 'original_validation_id': 2}
2025-06-23 14:32:32,496 - inserters.v3.roles.attribute_inserter - INFO - Successfully inserted attribute validation to workflow_temp with ID: 66
2025-06-23 14:32:32,497 - inserters.v3.roles.attribute_inserter - INFO - Updated MongoDB status to deployed_to_temp for validation ID: 426
2025-06-23 14:32:32,501 - inserters.v3.roles.attribute_inserter - INFO - Starting insert_attribute_validation_to_workflow_temp for attribute: E7.At3
2025-06-23 14:32:32,501 - inserters.v3.roles.attribute_inserter - INFO - Field validation passed: 2/2 required fields, 13/13 optional fields
2025-06-23 14:32:32,511 - inserters.v3.roles.attribute_inserter - INFO - Incremented validation ID from 3 to 436
2025-06-23 14:32:32,518 - inserters.v3.roles.attribute_inserter - INFO - Generated next attribute ID: E7.At3 -> E7.At30
2025-06-23 14:32:32,518 - inserters.v3.roles.attribute_inserter - INFO - Incremented attribute ID from E7.At3 to E7.At30
2025-06-23 14:32:32,518 - inserters.v3.roles.attribute_inserter - INFO - Additional MongoDB fields preserved: {'original_created_at': '2025-06-20T07:05:40.934871', 'original_updated_at': '2025-06-20T07:05:40.934878', 'original_created_by': 'system', 'original_updated_by': 'system', 'original_validation_id': 3}
2025-06-23 14:32:32,519 - inserters.v3.roles.attribute_inserter - INFO - Successfully inserted attribute validation to workflow_temp with ID: 67
2025-06-23 14:32:32,521 - inserters.v3.roles.attribute_inserter - INFO - Updated MongoDB status to deployed_to_temp for validation ID: 436
2025-06-23 14:32:32,525 - inserters.v3.roles.attribute_inserter - INFO - Starting insert_attribute_validation_to_workflow_temp for attribute: E7.At4
2025-06-23 14:32:32,525 - inserters.v3.roles.attribute_inserter - INFO - Field validation passed: 2/2 required fields, 13/13 optional fields
2025-06-23 14:32:32,535 - inserters.v3.roles.attribute_inserter - INFO - Incremented validation ID from 4 to 446
2025-06-23 14:32:32,542 - inserters.v3.roles.attribute_inserter - INFO - Generated next attribute ID: E7.At4 -> E7.At31
2025-06-23 14:32:32,542 - inserters.v3.roles.attribute_inserter - INFO - Incremented attribute ID from E7.At4 to E7.At31
2025-06-23 14:32:32,542 - inserters.v3.roles.attribute_inserter - INFO - Additional MongoDB fields preserved: {'original_created_at': '2025-06-20T07:05:40.936694', 'original_updated_at': '2025-06-20T07:05:40.936701', 'original_created_by': 'system', 'original_updated_by': 'system', 'original_validation_id': 4}
2025-06-23 14:32:32,543 - inserters.v3.roles.attribute_inserter - INFO - Successfully inserted attribute validation to workflow_temp with ID: 68
2025-06-23 14:32:32,544 - inserters.v3.roles.attribute_inserter - INFO - Updated MongoDB status to deployed_to_temp for validation ID: 446
2025-06-23 14:32:32,544 - inserters.v3.roles.attribute_inserter - INFO - Completed process_mongo_attribute_validations_to_workflow_temp: 8 successful, 0 failed
2025-06-23 14:38:51,840 - inserters.v3.roles.attribute_inserter - INFO - Starting deploy_single_attribute_validation_to_workflow_temp for validation ID: 1
2025-06-23 14:38:51,854 - inserters.v3.roles.attribute_inserter - INFO - Starting insert_attribute_validation_to_workflow_temp for attribute: E8.At1
2025-06-23 14:38:51,854 - inserters.v3.roles.attribute_inserter - INFO - Field validation passed: 2/2 required fields, 13/13 optional fields
2025-06-23 14:38:51,871 - inserters.v3.roles.attribute_inserter - INFO - Incremented validation ID from 1 to 456
2025-06-23 14:38:51,879 - inserters.v3.roles.attribute_inserter - INFO - Generated next attribute ID: E8.At1 -> E8.At37
2025-06-23 14:38:51,879 - inserters.v3.roles.attribute_inserter - INFO - Incremented attribute ID from E8.At1 to E8.At37
2025-06-23 14:38:51,879 - inserters.v3.roles.attribute_inserter - INFO - Additional MongoDB fields preserved: {'original_created_at': '2025-06-18T16:49:30.448481', 'original_updated_at': '2025-06-18T16:49:30.448484', 'original_created_by': 'system', 'original_updated_by': 'system', 'original_validation_id': 1}
2025-06-23 14:38:51,881 - inserters.v3.roles.attribute_inserter - INFO - Successfully inserted attribute validation to workflow_temp with ID: 69
2025-06-23 14:38:51,884 - inserters.v3.roles.attribute_inserter - INFO - Updated MongoDB status to deployed_to_temp for validation ID: 1
2025-06-23 14:38:51,942 - inserters.v3.roles.attribute_inserter - INFO - Starting process_mongo_attribute_validations_to_workflow_temp
2025-06-23 14:38:51,944 - inserters.v3.roles.attribute_inserter - INFO - Found 8 attribute validations with status 'draft' in MongoDB
2025-06-23 14:38:51,951 - inserters.v3.roles.attribute_inserter - INFO - Starting insert_attribute_validation_to_workflow_temp for attribute: E8.At4
2025-06-23 14:38:51,952 - inserters.v3.roles.attribute_inserter - INFO - Field validation passed: 2/2 required fields, 13/13 optional fields
2025-06-23 14:38:51,966 - inserters.v3.roles.attribute_inserter - INFO - Incremented validation ID from 2 to 466
2025-06-23 14:38:51,975 - inserters.v3.roles.attribute_inserter - INFO - Generated next attribute ID: E8.At4 -> E8.At38
2025-06-23 14:38:51,975 - inserters.v3.roles.attribute_inserter - INFO - Incremented attribute ID from E8.At4 to E8.At38
2025-06-23 14:38:51,975 - inserters.v3.roles.attribute_inserter - INFO - Additional MongoDB fields preserved: {'original_created_at': '2025-06-19T17:27:38.404018', 'original_updated_at': '2025-06-19T17:27:38.404025', 'original_created_by': 'system', 'original_updated_by': 'system', 'original_validation_id': 2}
2025-06-23 14:38:51,977 - inserters.v3.roles.attribute_inserter - INFO - Successfully inserted attribute validation to workflow_temp with ID: 70
2025-06-23 14:38:51,980 - inserters.v3.roles.attribute_inserter - INFO - Updated MongoDB status to deployed_to_temp for validation ID: 466
2025-06-23 14:38:51,985 - inserters.v3.roles.attribute_inserter - INFO - Starting insert_attribute_validation_to_workflow_temp for attribute: E8.At4
2025-06-23 14:38:51,985 - inserters.v3.roles.attribute_inserter - INFO - Field validation passed: 2/2 required fields, 13/13 optional fields
2025-06-23 14:38:52,000 - inserters.v3.roles.attribute_inserter - INFO - Incremented validation ID from 6 to 476
2025-06-23 14:38:52,008 - inserters.v3.roles.attribute_inserter - INFO - Generated next attribute ID: E8.At4 -> E8.At39
2025-06-23 14:38:52,009 - inserters.v3.roles.attribute_inserter - INFO - Incremented attribute ID from E8.At4 to E8.At39
2025-06-23 14:38:52,009 - inserters.v3.roles.attribute_inserter - INFO - Additional MongoDB fields preserved: {'original_created_at': '2025-06-20T05:15:31.135351', 'original_updated_at': '2025-06-20T05:15:31.135357', 'original_created_by': 'system', 'original_updated_by': 'system', 'original_validation_id': 6}
2025-06-23 14:38:52,010 - inserters.v3.roles.attribute_inserter - INFO - Successfully inserted attribute validation to workflow_temp with ID: 71
2025-06-23 14:38:52,013 - inserters.v3.roles.attribute_inserter - INFO - Updated MongoDB status to deployed_to_temp for validation ID: 476
2025-06-23 14:38:52,018 - inserters.v3.roles.attribute_inserter - INFO - Starting insert_attribute_validation_to_workflow_temp for attribute: E8.At1
2025-06-23 14:38:52,018 - inserters.v3.roles.attribute_inserter - INFO - Field validation passed: 2/2 required fields, 13/13 optional fields
2025-06-23 14:38:52,032 - inserters.v3.roles.attribute_inserter - INFO - Incremented validation ID from 5 to 486
2025-06-23 14:38:52,041 - inserters.v3.roles.attribute_inserter - INFO - Generated next attribute ID: E8.At1 -> E8.At40
2025-06-23 14:38:52,041 - inserters.v3.roles.attribute_inserter - INFO - Incremented attribute ID from E8.At1 to E8.At40
2025-06-23 14:38:52,041 - inserters.v3.roles.attribute_inserter - INFO - Additional MongoDB fields preserved: {'original_created_at': '2025-06-20T05:15:31.135903', 'original_updated_at': '2025-06-20T05:15:31.135906', 'original_created_by': 'system', 'original_updated_by': 'system', 'original_validation_id': 5}
2025-06-23 14:38:52,042 - inserters.v3.roles.attribute_inserter - INFO - Successfully inserted attribute validation to workflow_temp with ID: 72
2025-06-23 14:38:52,045 - inserters.v3.roles.attribute_inserter - INFO - Updated MongoDB status to deployed_to_temp for validation ID: 486
2025-06-23 14:38:52,051 - inserters.v3.roles.attribute_inserter - INFO - Starting insert_attribute_validation_to_workflow_temp for attribute: E8.At4
2025-06-23 14:38:52,051 - inserters.v3.roles.attribute_inserter - INFO - Field validation passed: 2/2 required fields, 13/13 optional fields
2025-06-23 14:38:52,063 - inserters.v3.roles.attribute_inserter - INFO - Incremented validation ID from 7 to 496
2025-06-23 14:38:52,073 - inserters.v3.roles.attribute_inserter - INFO - Generated next attribute ID: E8.At4 -> E8.At41
2025-06-23 14:38:52,073 - inserters.v3.roles.attribute_inserter - INFO - Incremented attribute ID from E8.At4 to E8.At41
2025-06-23 14:38:52,073 - inserters.v3.roles.attribute_inserter - INFO - Additional MongoDB fields preserved: {'original_created_at': '2025-06-20T05:37:48.522400', 'original_updated_at': '2025-06-20T05:37:48.522404', 'original_created_by': 'system', 'original_updated_by': 'system', 'original_validation_id': 7}
2025-06-23 14:38:52,074 - inserters.v3.roles.attribute_inserter - INFO - Successfully inserted attribute validation to workflow_temp with ID: 73
2025-06-23 14:38:52,076 - inserters.v3.roles.attribute_inserter - INFO - Updated MongoDB status to deployed_to_temp for validation ID: 496
2025-06-23 14:38:52,082 - inserters.v3.roles.attribute_inserter - INFO - Starting insert_attribute_validation_to_workflow_temp for attribute: E7.At1
2025-06-23 14:38:52,082 - inserters.v3.roles.attribute_inserter - INFO - Field validation passed: 2/2 required fields, 13/13 optional fields
2025-06-23 14:38:52,094 - inserters.v3.roles.attribute_inserter - INFO - Incremented validation ID from 1 to 506
2025-06-23 14:38:52,101 - inserters.v3.roles.attribute_inserter - INFO - Generated next attribute ID: E7.At1 -> E7.At32
2025-06-23 14:38:52,101 - inserters.v3.roles.attribute_inserter - INFO - Incremented attribute ID from E7.At1 to E7.At32
2025-06-23 14:38:52,101 - inserters.v3.roles.attribute_inserter - INFO - Additional MongoDB fields preserved: {'original_created_at': '2025-06-20T07:05:40.927340', 'original_updated_at': '2025-06-20T07:05:40.927345', 'original_created_by': 'system', 'original_updated_by': 'system', 'original_validation_id': 1}
2025-06-23 14:38:52,103 - inserters.v3.roles.attribute_inserter - INFO - Successfully inserted attribute validation to workflow_temp with ID: 74
2025-06-23 14:38:52,105 - inserters.v3.roles.attribute_inserter - INFO - Updated MongoDB status to deployed_to_temp for validation ID: 506
2025-06-23 14:38:52,110 - inserters.v3.roles.attribute_inserter - INFO - Starting insert_attribute_validation_to_workflow_temp for attribute: E7.At2
2025-06-23 14:38:52,110 - inserters.v3.roles.attribute_inserter - INFO - Field validation passed: 2/2 required fields, 13/13 optional fields
2025-06-23 14:38:52,122 - inserters.v3.roles.attribute_inserter - INFO - Incremented validation ID from 2 to 516
2025-06-23 14:38:52,130 - inserters.v3.roles.attribute_inserter - INFO - Generated next attribute ID: E7.At2 -> E7.At33
2025-06-23 14:38:52,130 - inserters.v3.roles.attribute_inserter - INFO - Incremented attribute ID from E7.At2 to E7.At33
2025-06-23 14:38:52,130 - inserters.v3.roles.attribute_inserter - INFO - Additional MongoDB fields preserved: {'original_created_at': '2025-06-20T07:05:40.932582', 'original_updated_at': '2025-06-20T07:05:40.932597', 'original_created_by': 'system', 'original_updated_by': 'system', 'original_validation_id': 2}
2025-06-23 14:38:52,132 - inserters.v3.roles.attribute_inserter - INFO - Successfully inserted attribute validation to workflow_temp with ID: 75
2025-06-23 14:38:52,134 - inserters.v3.roles.attribute_inserter - INFO - Updated MongoDB status to deployed_to_temp for validation ID: 516
2025-06-23 14:38:52,139 - inserters.v3.roles.attribute_inserter - INFO - Starting insert_attribute_validation_to_workflow_temp for attribute: E7.At3
2025-06-23 14:38:52,139 - inserters.v3.roles.attribute_inserter - INFO - Field validation passed: 2/2 required fields, 13/13 optional fields
2025-06-23 14:38:52,151 - inserters.v3.roles.attribute_inserter - INFO - Incremented validation ID from 3 to 526
2025-06-23 14:38:52,158 - inserters.v3.roles.attribute_inserter - INFO - Generated next attribute ID: E7.At3 -> E7.At34
2025-06-23 14:38:52,158 - inserters.v3.roles.attribute_inserter - INFO - Incremented attribute ID from E7.At3 to E7.At34
2025-06-23 14:38:52,158 - inserters.v3.roles.attribute_inserter - INFO - Additional MongoDB fields preserved: {'original_created_at': '2025-06-20T07:05:40.934871', 'original_updated_at': '2025-06-20T07:05:40.934878', 'original_created_by': 'system', 'original_updated_by': 'system', 'original_validation_id': 3}
2025-06-23 14:38:52,159 - inserters.v3.roles.attribute_inserter - INFO - Successfully inserted attribute validation to workflow_temp with ID: 76
2025-06-23 14:38:52,161 - inserters.v3.roles.attribute_inserter - INFO - Updated MongoDB status to deployed_to_temp for validation ID: 526
2025-06-23 14:38:52,166 - inserters.v3.roles.attribute_inserter - INFO - Starting insert_attribute_validation_to_workflow_temp for attribute: E7.At4
2025-06-23 14:38:52,166 - inserters.v3.roles.attribute_inserter - INFO - Field validation passed: 2/2 required fields, 13/13 optional fields
2025-06-23 14:38:52,179 - inserters.v3.roles.attribute_inserter - INFO - Incremented validation ID from 4 to 536
2025-06-23 14:38:52,185 - inserters.v3.roles.attribute_inserter - INFO - Generated next attribute ID: E7.At4 -> E7.At35
2025-06-23 14:38:52,185 - inserters.v3.roles.attribute_inserter - INFO - Incremented attribute ID from E7.At4 to E7.At35
2025-06-23 14:38:52,185 - inserters.v3.roles.attribute_inserter - INFO - Additional MongoDB fields preserved: {'original_created_at': '2025-06-20T07:05:40.936694', 'original_updated_at': '2025-06-20T07:05:40.936701', 'original_created_by': 'system', 'original_updated_by': 'system', 'original_validation_id': 4}
2025-06-23 14:38:52,188 - inserters.v3.roles.attribute_inserter - INFO - Successfully inserted attribute validation to workflow_temp with ID: 77
2025-06-23 14:38:52,190 - inserters.v3.roles.attribute_inserter - INFO - Updated MongoDB status to deployed_to_temp for validation ID: 536
2025-06-23 14:38:52,190 - inserters.v3.roles.attribute_inserter - INFO - Completed process_mongo_attribute_validations_to_workflow_temp: 8 successful, 0 failed
2025-06-24 04:45:01,343 - inserters.v3.roles.attribute_inserter - INFO - Starting deploy_single_attribute_validation_to_workflow_temp for validation ID: 1
2025-06-24 04:45:01,353 - inserters.v3.roles.attribute_inserter - INFO - Starting insert_attribute_validation_to_workflow_temp for attribute: E8.At1
2025-06-24 04:45:01,353 - inserters.v3.roles.attribute_inserter - INFO - Field validation passed: 2/2 required fields, 13/13 optional fields
2025-06-24 04:45:01,367 - inserters.v3.roles.attribute_inserter - INFO - Incremented validation ID from 1 to 546
2025-06-24 04:45:01,377 - inserters.v3.roles.attribute_inserter - INFO - Generated next attribute ID: E8.At1 -> E8.At42
2025-06-24 04:45:01,377 - inserters.v3.roles.attribute_inserter - INFO - Incremented attribute ID from E8.At1 to E8.At42
2025-06-24 04:45:01,377 - inserters.v3.roles.attribute_inserter - INFO - Additional MongoDB fields preserved: {'original_created_at': '2025-06-18T16:49:30.448481', 'original_updated_at': '2025-06-18T16:49:30.448484', 'original_created_by': 'system', 'original_updated_by': 'system', 'original_validation_id': 1}
2025-06-24 04:45:01,379 - inserters.v3.roles.attribute_inserter - INFO - Successfully inserted attribute validation to workflow_temp with ID: 78
2025-06-24 04:45:01,381 - inserters.v3.roles.attribute_inserter - INFO - Updated MongoDB status to deployed_to_temp for validation ID: 1
2025-06-24 04:45:01,491 - inserters.v3.roles.attribute_inserter - INFO - Starting process_mongo_attribute_validations_to_workflow_temp
2025-06-24 04:45:01,493 - inserters.v3.roles.attribute_inserter - INFO - Found 8 attribute validations with status 'draft' in MongoDB
2025-06-24 04:45:01,501 - inserters.v3.roles.attribute_inserter - INFO - Starting insert_attribute_validation_to_workflow_temp for attribute: E8.At4
2025-06-24 04:45:01,501 - inserters.v3.roles.attribute_inserter - INFO - Field validation passed: 2/2 required fields, 13/13 optional fields
2025-06-24 04:45:01,516 - inserters.v3.roles.attribute_inserter - INFO - Incremented validation ID from 2 to 556
2025-06-24 04:45:01,525 - inserters.v3.roles.attribute_inserter - INFO - Generated next attribute ID: E8.At4 -> E8.At43
2025-06-24 04:45:01,525 - inserters.v3.roles.attribute_inserter - INFO - Incremented attribute ID from E8.At4 to E8.At43
2025-06-24 04:45:01,525 - inserters.v3.roles.attribute_inserter - INFO - Additional MongoDB fields preserved: {'original_created_at': '2025-06-19T17:27:38.404018', 'original_updated_at': '2025-06-19T17:27:38.404025', 'original_created_by': 'system', 'original_updated_by': 'system', 'original_validation_id': 2}
2025-06-24 04:45:01,527 - inserters.v3.roles.attribute_inserter - INFO - Successfully inserted attribute validation to workflow_temp with ID: 79
2025-06-24 04:45:01,529 - inserters.v3.roles.attribute_inserter - INFO - Updated MongoDB status to deployed_to_temp for validation ID: 556
2025-06-24 04:45:01,534 - inserters.v3.roles.attribute_inserter - INFO - Starting insert_attribute_validation_to_workflow_temp for attribute: E8.At4
2025-06-24 04:45:01,534 - inserters.v3.roles.attribute_inserter - INFO - Field validation passed: 2/2 required fields, 13/13 optional fields
2025-06-24 04:45:01,549 - inserters.v3.roles.attribute_inserter - INFO - Incremented validation ID from 6 to 566
2025-06-24 04:45:01,555 - inserters.v3.roles.attribute_inserter - INFO - Generated next attribute ID: E8.At4 -> E8.At44
2025-06-24 04:45:01,555 - inserters.v3.roles.attribute_inserter - INFO - Incremented attribute ID from E8.At4 to E8.At44
2025-06-24 04:45:01,555 - inserters.v3.roles.attribute_inserter - INFO - Additional MongoDB fields preserved: {'original_created_at': '2025-06-20T05:15:31.135351', 'original_updated_at': '2025-06-20T05:15:31.135357', 'original_created_by': 'system', 'original_updated_by': 'system', 'original_validation_id': 6}
2025-06-24 04:45:01,556 - inserters.v3.roles.attribute_inserter - INFO - Successfully inserted attribute validation to workflow_temp with ID: 80
2025-06-24 04:45:01,559 - inserters.v3.roles.attribute_inserter - INFO - Updated MongoDB status to deployed_to_temp for validation ID: 566
2025-06-24 04:45:01,563 - inserters.v3.roles.attribute_inserter - INFO - Starting insert_attribute_validation_to_workflow_temp for attribute: E8.At1
2025-06-24 04:45:01,563 - inserters.v3.roles.attribute_inserter - INFO - Field validation passed: 2/2 required fields, 13/13 optional fields
2025-06-24 04:45:01,577 - inserters.v3.roles.attribute_inserter - INFO - Incremented validation ID from 5 to 576
2025-06-24 04:45:01,583 - inserters.v3.roles.attribute_inserter - INFO - Generated next attribute ID: E8.At1 -> E8.At45
2025-06-24 04:45:01,583 - inserters.v3.roles.attribute_inserter - INFO - Incremented attribute ID from E8.At1 to E8.At45
2025-06-24 04:45:01,583 - inserters.v3.roles.attribute_inserter - INFO - Additional MongoDB fields preserved: {'original_created_at': '2025-06-20T05:15:31.135903', 'original_updated_at': '2025-06-20T05:15:31.135906', 'original_created_by': 'system', 'original_updated_by': 'system', 'original_validation_id': 5}
2025-06-24 04:45:01,584 - inserters.v3.roles.attribute_inserter - INFO - Successfully inserted attribute validation to workflow_temp with ID: 81
2025-06-24 04:45:01,587 - inserters.v3.roles.attribute_inserter - INFO - Updated MongoDB status to deployed_to_temp for validation ID: 576
2025-06-24 04:45:01,591 - inserters.v3.roles.attribute_inserter - INFO - Starting insert_attribute_validation_to_workflow_temp for attribute: E8.At4
2025-06-24 04:45:01,591 - inserters.v3.roles.attribute_inserter - INFO - Field validation passed: 2/2 required fields, 13/13 optional fields
2025-06-24 04:45:01,605 - inserters.v3.roles.attribute_inserter - INFO - Incremented validation ID from 7 to 586
2025-06-24 04:45:01,611 - inserters.v3.roles.attribute_inserter - INFO - Generated next attribute ID: E8.At4 -> E8.At46
2025-06-24 04:45:01,611 - inserters.v3.roles.attribute_inserter - INFO - Incremented attribute ID from E8.At4 to E8.At46
2025-06-24 04:45:01,611 - inserters.v3.roles.attribute_inserter - INFO - Additional MongoDB fields preserved: {'original_created_at': '2025-06-20T05:37:48.522400', 'original_updated_at': '2025-06-20T05:37:48.522404', 'original_created_by': 'system', 'original_updated_by': 'system', 'original_validation_id': 7}
2025-06-24 04:45:01,612 - inserters.v3.roles.attribute_inserter - INFO - Successfully inserted attribute validation to workflow_temp with ID: 82
2025-06-24 04:45:01,614 - inserters.v3.roles.attribute_inserter - INFO - Updated MongoDB status to deployed_to_temp for validation ID: 586
2025-06-24 04:45:01,619 - inserters.v3.roles.attribute_inserter - INFO - Starting insert_attribute_validation_to_workflow_temp for attribute: E7.At1
2025-06-24 04:45:01,619 - inserters.v3.roles.attribute_inserter - INFO - Field validation passed: 2/2 required fields, 13/13 optional fields
2025-06-24 04:45:01,629 - inserters.v3.roles.attribute_inserter - INFO - Incremented validation ID from 1 to 596
2025-06-24 04:45:01,638 - inserters.v3.roles.attribute_inserter - INFO - Generated next attribute ID: E7.At1 -> E7.At36
2025-06-24 04:45:01,638 - inserters.v3.roles.attribute_inserter - INFO - Incremented attribute ID from E7.At1 to E7.At36
2025-06-24 04:45:01,639 - inserters.v3.roles.attribute_inserter - INFO - Additional MongoDB fields preserved: {'original_created_at': '2025-06-20T07:05:40.927340', 'original_updated_at': '2025-06-20T07:05:40.927345', 'original_created_by': 'system', 'original_updated_by': 'system', 'original_validation_id': 1}
2025-06-24 04:45:01,640 - inserters.v3.roles.attribute_inserter - INFO - Successfully inserted attribute validation to workflow_temp with ID: 83
2025-06-24 04:45:01,642 - inserters.v3.roles.attribute_inserter - INFO - Updated MongoDB status to deployed_to_temp for validation ID: 596
2025-06-24 04:45:01,647 - inserters.v3.roles.attribute_inserter - INFO - Starting insert_attribute_validation_to_workflow_temp for attribute: E7.At2
2025-06-24 04:45:01,647 - inserters.v3.roles.attribute_inserter - INFO - Field validation passed: 2/2 required fields, 13/13 optional fields
2025-06-24 04:45:01,657 - inserters.v3.roles.attribute_inserter - INFO - Incremented validation ID from 2 to 606
2025-06-24 04:45:01,666 - inserters.v3.roles.attribute_inserter - INFO - Generated next attribute ID: E7.At2 -> E7.At37
2025-06-24 04:45:01,666 - inserters.v3.roles.attribute_inserter - INFO - Incremented attribute ID from E7.At2 to E7.At37
2025-06-24 04:45:01,666 - inserters.v3.roles.attribute_inserter - INFO - Additional MongoDB fields preserved: {'original_created_at': '2025-06-20T07:05:40.932582', 'original_updated_at': '2025-06-20T07:05:40.932597', 'original_created_by': 'system', 'original_updated_by': 'system', 'original_validation_id': 2}
2025-06-24 04:45:01,668 - inserters.v3.roles.attribute_inserter - INFO - Successfully inserted attribute validation to workflow_temp with ID: 84
2025-06-24 04:45:01,670 - inserters.v3.roles.attribute_inserter - INFO - Updated MongoDB status to deployed_to_temp for validation ID: 606
2025-06-24 04:45:01,675 - inserters.v3.roles.attribute_inserter - INFO - Starting insert_attribute_validation_to_workflow_temp for attribute: E7.At3
2025-06-24 04:45:01,675 - inserters.v3.roles.attribute_inserter - INFO - Field validation passed: 2/2 required fields, 13/13 optional fields
2025-06-24 04:45:01,685 - inserters.v3.roles.attribute_inserter - INFO - Incremented validation ID from 3 to 616
2025-06-24 04:45:01,693 - inserters.v3.roles.attribute_inserter - INFO - Generated next attribute ID: E7.At3 -> E7.At38
2025-06-24 04:45:01,693 - inserters.v3.roles.attribute_inserter - INFO - Incremented attribute ID from E7.At3 to E7.At38
2025-06-24 04:45:01,693 - inserters.v3.roles.attribute_inserter - INFO - Additional MongoDB fields preserved: {'original_created_at': '2025-06-20T07:05:40.934871', 'original_updated_at': '2025-06-20T07:05:40.934878', 'original_created_by': 'system', 'original_updated_by': 'system', 'original_validation_id': 3}
2025-06-24 04:45:01,694 - inserters.v3.roles.attribute_inserter - INFO - Successfully inserted attribute validation to workflow_temp with ID: 85
2025-06-24 04:45:01,696 - inserters.v3.roles.attribute_inserter - INFO - Updated MongoDB status to deployed_to_temp for validation ID: 616
2025-06-24 04:45:01,700 - inserters.v3.roles.attribute_inserter - INFO - Starting insert_attribute_validation_to_workflow_temp for attribute: E7.At4
2025-06-24 04:45:01,700 - inserters.v3.roles.attribute_inserter - INFO - Field validation passed: 2/2 required fields, 13/13 optional fields
2025-06-24 04:45:01,713 - inserters.v3.roles.attribute_inserter - INFO - Incremented validation ID from 4 to 626
2025-06-24 04:45:01,719 - inserters.v3.roles.attribute_inserter - INFO - Generated next attribute ID: E7.At4 -> E7.At39
2025-06-24 04:45:01,719 - inserters.v3.roles.attribute_inserter - INFO - Incremented attribute ID from E7.At4 to E7.At39
2025-06-24 04:45:01,719 - inserters.v3.roles.attribute_inserter - INFO - Additional MongoDB fields preserved: {'original_created_at': '2025-06-20T07:05:40.936694', 'original_updated_at': '2025-06-20T07:05:40.936701', 'original_created_by': 'system', 'original_updated_by': 'system', 'original_validation_id': 4}
2025-06-24 04:45:01,720 - inserters.v3.roles.attribute_inserter - INFO - Successfully inserted attribute validation to workflow_temp with ID: 86
2025-06-24 04:45:01,722 - inserters.v3.roles.attribute_inserter - INFO - Updated MongoDB status to deployed_to_temp for validation ID: 626
2025-06-24 04:45:01,722 - inserters.v3.roles.attribute_inserter - INFO - Completed process_mongo_attribute_validations_to_workflow_temp: 8 successful, 0 failed
2025-06-24 11:47:14,573 - inserters.v3.roles.attribute_inserter - INFO - Starting process_mongo_attribute_validations_to_workflow_runtime
2025-06-24 11:47:14,578 - inserters.v3.roles.attribute_inserter - INFO - Found 0 deployed_to_temp + 8 draft = 8 total attribute validations in MongoDB
2025-06-24 11:47:14,586 - inserters.v3.roles.attribute_inserter - INFO - Starting insert_attribute_validation_to_workflow_runtime for attribute: E8.At4
2025-06-24 11:47:14,586 - inserters.v3.roles.attribute_inserter - INFO - Field validation passed: 2/2 required fields, 13/13 optional fields
2025-06-24 11:47:14,602 - inserters.v3.roles.attribute_inserter - INFO - Incremented validation ID from 2 to 636
2025-06-24 11:47:14,613 - inserters.v3.roles.attribute_inserter - INFO - Generated next attribute ID: E8.At4 -> E8.At47
2025-06-24 11:47:14,613 - inserters.v3.roles.attribute_inserter - INFO - Incremented attribute ID from E8.At4 to E8.At47
2025-06-24 11:47:14,613 - inserters.v3.roles.attribute_inserter - INFO - Additional MongoDB fields preserved: {'original_created_at': '2025-06-19T17:27:38.404018', 'original_updated_at': '2025-06-19T17:27:38.404025', 'original_created_by': 'system', 'original_updated_by': 'system', 'original_validation_id': 2}
2025-06-24 11:47:14,616 - inserters.v3.roles.attribute_inserter - INFO - Successfully inserted attribute validation to workflow_runtime with ID: 29
2025-06-24 11:47:14,617 - inserters.v3.roles.attribute_inserter - INFO - Updated MongoDB status to deployed_to_production for validation ID: 636
2025-06-24 11:47:14,624 - inserters.v3.roles.attribute_inserter - INFO - Starting insert_attribute_validation_to_workflow_runtime for attribute: E8.At4
2025-06-24 11:47:14,624 - inserters.v3.roles.attribute_inserter - INFO - Field validation passed: 2/2 required fields, 13/13 optional fields
2025-06-24 11:47:14,638 - inserters.v3.roles.attribute_inserter - INFO - Incremented validation ID from 6 to 646
2025-06-24 11:47:14,646 - inserters.v3.roles.attribute_inserter - INFO - Generated next attribute ID: E8.At4 -> E8.At48
2025-06-24 11:47:14,646 - inserters.v3.roles.attribute_inserter - INFO - Incremented attribute ID from E8.At4 to E8.At48
2025-06-24 11:47:14,646 - inserters.v3.roles.attribute_inserter - INFO - Additional MongoDB fields preserved: {'original_created_at': '2025-06-20T05:15:31.135351', 'original_updated_at': '2025-06-20T05:15:31.135357', 'original_created_by': 'system', 'original_updated_by': 'system', 'original_validation_id': 6}
2025-06-24 11:47:14,649 - inserters.v3.roles.attribute_inserter - INFO - Successfully inserted attribute validation to workflow_runtime with ID: 30
2025-06-24 11:47:14,652 - inserters.v3.roles.attribute_inserter - INFO - Updated MongoDB status to deployed_to_production for validation ID: 646
2025-06-24 11:47:14,657 - inserters.v3.roles.attribute_inserter - INFO - Starting insert_attribute_validation_to_workflow_runtime for attribute: E8.At1
2025-06-24 11:47:14,658 - inserters.v3.roles.attribute_inserter - INFO - Field validation passed: 2/2 required fields, 13/13 optional fields
2025-06-24 11:47:14,672 - inserters.v3.roles.attribute_inserter - INFO - Incremented validation ID from 5 to 656
2025-06-24 11:47:14,681 - inserters.v3.roles.attribute_inserter - INFO - Generated next attribute ID: E8.At1 -> E8.At49
2025-06-24 11:47:14,682 - inserters.v3.roles.attribute_inserter - INFO - Incremented attribute ID from E8.At1 to E8.At49
2025-06-24 11:47:14,682 - inserters.v3.roles.attribute_inserter - INFO - Additional MongoDB fields preserved: {'original_created_at': '2025-06-20T05:15:31.135903', 'original_updated_at': '2025-06-20T05:15:31.135906', 'original_created_by': 'system', 'original_updated_by': 'system', 'original_validation_id': 5}
2025-06-24 11:47:14,684 - inserters.v3.roles.attribute_inserter - INFO - Successfully inserted attribute validation to workflow_runtime with ID: 31
2025-06-24 11:47:14,686 - inserters.v3.roles.attribute_inserter - INFO - Updated MongoDB status to deployed_to_production for validation ID: 656
2025-06-24 11:47:14,690 - inserters.v3.roles.attribute_inserter - INFO - Starting insert_attribute_validation_to_workflow_runtime for attribute: E8.At4
2025-06-24 11:47:14,690 - inserters.v3.roles.attribute_inserter - INFO - Field validation passed: 2/2 required fields, 13/13 optional fields
2025-06-24 11:47:14,705 - inserters.v3.roles.attribute_inserter - INFO - Incremented validation ID from 7 to 666
2025-06-24 11:47:14,711 - inserters.v3.roles.attribute_inserter - INFO - Generated next attribute ID: E8.At4 -> E8.At50
2025-06-24 11:47:14,711 - inserters.v3.roles.attribute_inserter - INFO - Incremented attribute ID from E8.At4 to E8.At50
2025-06-24 11:47:14,711 - inserters.v3.roles.attribute_inserter - INFO - Additional MongoDB fields preserved: {'original_created_at': '2025-06-20T05:37:48.522400', 'original_updated_at': '2025-06-20T05:37:48.522404', 'original_created_by': 'system', 'original_updated_by': 'system', 'original_validation_id': 7}
2025-06-24 11:47:14,714 - inserters.v3.roles.attribute_inserter - INFO - Successfully inserted attribute validation to workflow_runtime with ID: 32
2025-06-24 11:47:14,716 - inserters.v3.roles.attribute_inserter - INFO - Updated MongoDB status to deployed_to_production for validation ID: 666
2025-06-24 11:47:14,720 - inserters.v3.roles.attribute_inserter - INFO - Starting insert_attribute_validation_to_workflow_runtime for attribute: E7.At1
2025-06-24 11:47:14,720 - inserters.v3.roles.attribute_inserter - INFO - Field validation passed: 2/2 required fields, 13/13 optional fields
2025-06-24 11:47:14,731 - inserters.v3.roles.attribute_inserter - INFO - Incremented validation ID from 1 to 676
2025-06-24 11:47:14,740 - inserters.v3.roles.attribute_inserter - INFO - Generated next attribute ID: E7.At1 -> E7.At40
2025-06-24 11:47:14,740 - inserters.v3.roles.attribute_inserter - INFO - Incremented attribute ID from E7.At1 to E7.At40
2025-06-24 11:47:14,740 - inserters.v3.roles.attribute_inserter - INFO - Additional MongoDB fields preserved: {'original_created_at': '2025-06-20T07:05:40.927340', 'original_updated_at': '2025-06-20T07:05:40.927345', 'original_created_by': 'system', 'original_updated_by': 'system', 'original_validation_id': 1}
2025-06-24 11:47:14,742 - inserters.v3.roles.attribute_inserter - INFO - Successfully inserted attribute validation to workflow_runtime with ID: 33
2025-06-24 11:47:14,745 - inserters.v3.roles.attribute_inserter - INFO - Updated MongoDB status to deployed_to_production for validation ID: 676
2025-06-24 11:47:14,749 - inserters.v3.roles.attribute_inserter - INFO - Starting insert_attribute_validation_to_workflow_runtime for attribute: E7.At2
2025-06-24 11:47:14,750 - inserters.v3.roles.attribute_inserter - INFO - Field validation passed: 2/2 required fields, 13/13 optional fields
2025-06-24 11:47:14,761 - inserters.v3.roles.attribute_inserter - INFO - Incremented validation ID from 2 to 686
2025-06-24 11:47:14,770 - inserters.v3.roles.attribute_inserter - INFO - Generated next attribute ID: E7.At2 -> E7.At41
2025-06-24 11:47:14,770 - inserters.v3.roles.attribute_inserter - INFO - Incremented attribute ID from E7.At2 to E7.At41
2025-06-24 11:47:14,770 - inserters.v3.roles.attribute_inserter - INFO - Additional MongoDB fields preserved: {'original_created_at': '2025-06-20T07:05:40.932582', 'original_updated_at': '2025-06-20T07:05:40.932597', 'original_created_by': 'system', 'original_updated_by': 'system', 'original_validation_id': 2}
2025-06-24 11:47:14,773 - inserters.v3.roles.attribute_inserter - INFO - Successfully inserted attribute validation to workflow_runtime with ID: 34
2025-06-24 11:47:14,775 - inserters.v3.roles.attribute_inserter - INFO - Updated MongoDB status to deployed_to_production for validation ID: 686
2025-06-24 11:47:14,781 - inserters.v3.roles.attribute_inserter - INFO - Starting insert_attribute_validation_to_workflow_runtime for attribute: E7.At3
2025-06-24 11:47:14,781 - inserters.v3.roles.attribute_inserter - INFO - Field validation passed: 2/2 required fields, 13/13 optional fields
2025-06-24 11:47:14,796 - inserters.v3.roles.attribute_inserter - INFO - Incremented validation ID from 3 to 696
2025-06-24 11:47:14,804 - inserters.v3.roles.attribute_inserter - INFO - Generated next attribute ID: E7.At3 -> E7.At42
2025-06-24 11:47:14,804 - inserters.v3.roles.attribute_inserter - INFO - Incremented attribute ID from E7.At3 to E7.At42
2025-06-24 11:47:14,804 - inserters.v3.roles.attribute_inserter - INFO - Additional MongoDB fields preserved: {'original_created_at': '2025-06-20T07:05:40.934871', 'original_updated_at': '2025-06-20T07:05:40.934878', 'original_created_by': 'system', 'original_updated_by': 'system', 'original_validation_id': 3}
2025-06-24 11:47:14,807 - inserters.v3.roles.attribute_inserter - INFO - Successfully inserted attribute validation to workflow_runtime with ID: 35
2025-06-24 11:47:14,809 - inserters.v3.roles.attribute_inserter - INFO - Updated MongoDB status to deployed_to_production for validation ID: 696
2025-06-24 11:47:14,815 - inserters.v3.roles.attribute_inserter - INFO - Starting insert_attribute_validation_to_workflow_runtime for attribute: E7.At4
2025-06-24 11:47:14,815 - inserters.v3.roles.attribute_inserter - INFO - Field validation passed: 2/2 required fields, 13/13 optional fields
2025-06-24 11:47:14,828 - inserters.v3.roles.attribute_inserter - INFO - Incremented validation ID from 4 to 706
2025-06-24 11:47:14,836 - inserters.v3.roles.attribute_inserter - INFO - Generated next attribute ID: E7.At4 -> E7.At43
2025-06-24 11:47:14,836 - inserters.v3.roles.attribute_inserter - INFO - Incremented attribute ID from E7.At4 to E7.At43
2025-06-24 11:47:14,836 - inserters.v3.roles.attribute_inserter - INFO - Additional MongoDB fields preserved: {'original_created_at': '2025-06-20T07:05:40.936694', 'original_updated_at': '2025-06-20T07:05:40.936701', 'original_created_by': 'system', 'original_updated_by': 'system', 'original_validation_id': 4}
2025-06-24 11:47:14,838 - inserters.v3.roles.attribute_inserter - INFO - Successfully inserted attribute validation to workflow_runtime with ID: 36
2025-06-24 11:47:14,839 - inserters.v3.roles.attribute_inserter - INFO - Updated MongoDB status to deployed_to_production for validation ID: 706
2025-06-24 11:47:14,839 - inserters.v3.roles.attribute_inserter - INFO - Completed process_mongo_attribute_validations_to_workflow_runtime: 8 successful, 0 failed
