"""
Test script for the prescriptive parser and component deployer.

This script tests the functionality of the prescriptive parser and component deployer
by parsing and deploying components from prescriptive text.
"""

import os
import sys
import logging
import argparse
from typing import Dict, List, Tuple, Any, Optional

# Add parent directory to path to import common modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('test_prescriptive_parser.log')
    ]
)
logger = logging.getLogger('test_prescriptive_parser')

def read_prescriptive_text(component_type: str) -> str:
    """
    Read prescriptive text from a file.
    
    Args:
        component_type: Type of the component ('roles', 'entities', 'go_definitions', 'lo_definitions')
        
    Returns:
        Prescriptive text as a string
    """
    file_path = os.path.join('prompts', f'{component_type}_prompt.txt')
    logger.info(f"Reading prescriptive text from {file_path}")
    
    try:
        with open(file_path, 'r') as f:
            return f.read()
    except Exception as e:
        logger.error(f"Error reading prescriptive text: {str(e)}")
        return ""

def test_parse_and_deploy_individual(use_temp_schema: bool = True) -> bool:
    """
    Test parsing and deploying components individually.
    
    Args:
        use_temp_schema: Whether to deploy to a temporary schema for validation
        
    Returns:
        Boolean indicating if all tests passed
    """
    from prescriptive_parser import PrescriptiveParser
    from component_deployer import ComponentDeployer
    
    logger.info("Testing parsing and deploying components individually")
    
    # Initialize parser and deployer
    parser = PrescriptiveParser()
    deployer = ComponentDeployer(use_temp_schema=use_temp_schema)
    
    # Define component types in the correct order
    component_types = ['entities', 'go_definitions', 'lo_definitions', 'roles']
    
    # Parse and deploy each component type
    for component_type in component_types:
        logger.info(f"Testing {component_type}")
        
        # Read prescriptive text
        prescriptive_text = read_prescriptive_text(component_type)
        if not prescriptive_text:
            logger.error(f"Failed to read prescriptive text for {component_type}")
            return False
        
        # Parse prescriptive text
        component_data, parse_warnings = parser.parse(component_type, prescriptive_text)
        
        if parse_warnings:
            logger.warning(f"Parse warnings for {component_type}: {parse_warnings}")
        
        if not component_data:
            logger.error(f"Failed to parse {component_type}")
            return False
        
        logger.info(f"Successfully parsed {component_type}")
        
        # Deploy component
        success, deploy_messages = deployer.deploy_from_prescriptive_text(component_type, prescriptive_text)
        
        if deploy_messages:
            logger.info(f"Deploy messages for {component_type}: {deploy_messages}")
        
        if not success:
            logger.error(f"Failed to deploy {component_type}")
            return False
        
        logger.info(f"Successfully deployed {component_type}")
    
    # Promote from temp to runtime if using temp schema
    if use_temp_schema:
        success, promote_messages = deployer.promote_from_temp_to_runtime()
        
        if promote_messages:
            logger.info(f"Promote messages: {promote_messages}")
        
        if not success:
            logger.error("Failed to promote from temp to runtime")
            return False
        
        logger.info("Successfully promoted from temp to runtime")
    
    logger.info("All individual tests passed")
    return True

def test_parse_and_deploy_multiple(use_temp_schema: bool = True) -> bool:
    """
    Test parsing and deploying multiple components at once.
    
    Args:
        use_temp_schema: Whether to deploy to a temporary schema for validation
        
    Returns:
        Boolean indicating if all tests passed
    """
    from component_deployer import ComponentDeployer
    
    logger.info("Testing parsing and deploying multiple components at once")
    
    # Initialize deployer
    deployer = ComponentDeployer(use_temp_schema=use_temp_schema)
    
    # Define component types
    component_types = ['entities', 'go_definitions', 'lo_definitions', 'roles']
    
    # Read prescriptive text for each component type
    components = {}
    for component_type in component_types:
        prescriptive_text = read_prescriptive_text(component_type)
        if not prescriptive_text:
            logger.error(f"Failed to read prescriptive text for {component_type}")
            return False
        
        components[component_type] = prescriptive_text
    
    # Deploy all components at once
    success, deploy_messages = deployer.deploy_multiple_from_prescriptive_text(components)
    
    if deploy_messages:
        logger.info(f"Deploy messages: {deploy_messages}")
    
    if not success:
        logger.error("Failed to deploy multiple components")
        return False
    
    logger.info("Successfully deployed multiple components")
    
    # Promote from temp to runtime if using temp schema
    if use_temp_schema:
        success, promote_messages = deployer.promote_from_temp_to_runtime()
        
        if promote_messages:
            logger.info(f"Promote messages: {promote_messages}")
        
        if not success:
            logger.error("Failed to promote from temp to runtime")
            return False
        
        logger.info("Successfully promoted from temp to runtime")
    
    logger.info("Multiple component test passed")
    return True

def test_dependency_checking(use_temp_schema: bool = True) -> bool:
    """
    Test dependency checking in the component deployer.
    
    Args:
        use_temp_schema: Whether to deploy to a temporary schema for validation
        
    Returns:
        Boolean indicating if all tests passed
    """
    from component_deployer import ComponentDeployer
    
    logger.info("Testing dependency checking")
    
    # Initialize deployer
    deployer = ComponentDeployer(use_temp_schema=use_temp_schema)
    
    # Try to deploy roles first (should fail due to missing dependencies)
    prescriptive_text = read_prescriptive_text('roles')
    if not prescriptive_text:
        logger.error("Failed to read prescriptive text for roles")
        return False
    
    success, deploy_messages = deployer.deploy_from_prescriptive_text('roles', prescriptive_text)
    
    if deploy_messages:
        logger.info(f"Deploy messages: {deploy_messages}")
    
    if success:
        logger.error("Dependency checking failed: roles were deployed without dependencies")
        return False
    
    logger.info("Dependency checking passed: roles were not deployed without dependencies")
    
    # Try to deploy in the wrong order
    components = {}
    for component_type in ['roles', 'lo_definitions', 'go_definitions', 'entities']:
        prescriptive_text = read_prescriptive_text(component_type)
        if not prescriptive_text:
            logger.error(f"Failed to read prescriptive text for {component_type}")
            return False
        
        components[component_type] = prescriptive_text
    
    # This should still work because the deployer enforces the correct order
    success, deploy_messages = deployer.deploy_multiple_from_prescriptive_text(components)
    
    if deploy_messages:
        logger.info(f"Deploy messages: {deploy_messages}")
    
    if not success:
        logger.error("Failed to deploy components in the wrong order (should have been reordered)")
        return False
    
    logger.info("Successfully deployed components in the wrong order (reordered automatically)")
    
    # Promote from temp to runtime if using temp schema
    if use_temp_schema:
        success, promote_messages = deployer.promote_from_temp_to_runtime()
        
        if promote_messages:
            logger.info(f"Promote messages: {promote_messages}")
        
        if not success:
            logger.error("Failed to promote from temp to runtime")
            return False
        
        logger.info("Successfully promoted from temp to runtime")
    
    logger.info("Dependency checking test passed")
    return True

def main():
    """Main function."""
    parser = argparse.ArgumentParser(description='Test prescriptive parser and component deployer')
    parser.add_argument('--use-runtime-schema', action='store_true', help='Deploy to the runtime schema instead of a temporary schema')
    parser.add_argument('--test-individual', action='store_true', help='Test parsing and deploying components individually')
    parser.add_argument('--test-multiple', action='store_true', help='Test parsing and deploying multiple components at once')
    parser.add_argument('--test-dependencies', action='store_true', help='Test dependency checking')
    parser.add_argument('--test-all', action='store_true', help='Run all tests')
    
    args = parser.parse_args()
    
    # Default to running all tests if no specific test is specified
    if not (args.test_individual or args.test_multiple or args.test_dependencies):
        args.test_all = True
    
    # By default, use a temporary schema for testing
    use_temp_schema = not args.use_runtime_schema
    
    # Run tests
    if args.test_all or args.test_individual:
        if not test_parse_and_deploy_individual(use_temp_schema):
            logger.error("Individual test failed")
            return 1
    
    if args.test_all or args.test_multiple:
        if not test_parse_and_deploy_multiple(use_temp_schema):
            logger.error("Multiple test failed")
            return 1
    
    if args.test_all or args.test_dependencies:
        if not test_dependency_checking(use_temp_schema):
            logger.error("Dependency checking test failed")
            return 1
    
    logger.info("All tests passed")
    return 0

if __name__ == '__main__':
    sys.exit(main())
