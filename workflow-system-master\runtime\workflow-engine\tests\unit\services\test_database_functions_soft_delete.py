import unittest
from unittest.mock import Mock, patch, MagicMock
from sqlalchemy.orm import Session
from sqlalchemy import text
from typing import Dict, Any
import datetime
import sys
import os

# Add project root to path if needed
project_root = '/home/<USER>/workflow-system/runtime/workflow-engine'
if project_root not in sys.path:
    sys.path.insert(0, project_root)

# Import the function to test
from app.services.system_functions import soft_delete

class TestSoftDelete(unittest.TestCase):
    def setUp(self):
        # Mock database session
        self.db = Mock(spec=Session)
        
        # Sample data
        self.entity_id = "e001"
        self.record_data = {
            "id": 123,
            "name": "Test Record"
        }
        
    def test_entity_not_found(self):
        """Test when entity_id doesn't exist"""
        # Setup: Make DB return None for entity lookup
        execute_mock = Mock()
        execute_mock.scalar.return_value = None
        self.db.execute.return_value = execute_mock
        
        # Execute and verify exception
        with self.assertRaises(ValueError) as context:
            soft_delete(self.db, self.entity_id, self.record_data)
        
        # Verify error message
        self.assertIn(f"Entity '{self.entity_id}' not found", str(context.exception))
    
    @patch('app.services.system_functions.function_repository')
    @patch('app.services.database_utils.CaseConverter')
    @patch('app.services.system_functions.datetime')
    def test_successful_soft_delete(self, mock_datetime, mock_converter, mock_repo):
        """Test successful soft deletion of a record"""
        # Setup mocks
        execute_mock = Mock()
        execute_mock.scalar.return_value = "TestTable"
        self.db.execute.return_value = execute_mock
        
        # Setup CaseConverter mock
        mock_converter.to_snake_case.return_value = "test_table"
        
        # Setup datetime mock
        mock_now = datetime.datetime(2023, 5, 1, 12, 0, 0)
        mock_datetime.datetime.now.return_value = mock_now
        
        # Configure function_repository mock
        mock_repo.auto_execute.return_value = {
            "status": "updated",
            "id": 123
        }
        
        # Execute function
        result = soft_delete(self.db, self.entity_id, self.record_data)
        
        # Verify function_repository was called with correct parameters
        expected_data = self.record_data.copy()
        expected_data["deleted_mark"] = True
        
        mock_repo.auto_execute.assert_called_once_with(
            "update", self.db, self.entity_id, expected_data
        )
        
        # Verify results
        self.assertEqual(result["status"], "deleted")
        self.assertEqual(result["id"], 123)
        self.assertEqual(result["message"], "Record marked as deleted using deleted_mark field")
    
    @patch('app.services.system_functions.function_repository')
    @patch('app.services.database_utils.CaseConverter')
    @patch('app.services.system_functions.datetime')
    def test_custom_delete_field(self, mock_datetime, mock_converter, mock_repo):
        """Test using a custom field for soft deletion"""
        # Setup mocks
        execute_mock = Mock()
        execute_mock.scalar.return_value = "TestTable"
        self.db.execute.return_value = execute_mock
        
        # Setup CaseConverter mock
        mock_converter.to_snake_case.return_value = "test_table"
        
        # Setup datetime mock
        mock_now = datetime.datetime(2023, 5, 1, 12, 0, 0)
        mock_datetime.datetime.now.return_value = mock_now
        
        # Configure function_repository mock
        mock_repo.auto_execute.return_value = {
            "status": "updated",
            "id": 123
        }
        
        # Custom delete field
        custom_field = "is_inactive"
        
        # Execute function with custom delete field
        result = soft_delete(self.db, self.entity_id, self.record_data, custom_field)
        
        # Verify function_repository was called with correct parameters
        expected_data = self.record_data.copy()
        expected_data[custom_field] = True
        
        mock_repo.auto_execute.assert_called_once_with(
            "update", self.db, self.entity_id, expected_data
        )
        
        # Verify results
        self.assertEqual(result["status"], "deleted")
        self.assertEqual(result["id"], 123)
        self.assertEqual(result["message"], f"Record marked as deleted using {custom_field} field")
    
    @patch('app.services.system_functions.function_repository')
    @patch('app.services.database_utils.CaseConverter')
    @patch('app.services.system_functions.datetime')
    def test_with_updated_at_field(self, mock_datetime, mock_converter, mock_repo):
        """Test that updated_at field is properly handled"""
        # Setup mocks
        execute_mock = Mock()
        execute_mock.scalar.return_value = "TestTable"
        self.db.execute.return_value = execute_mock
        
        # Setup CaseConverter mock
        mock_converter.to_snake_case.return_value = "test_table"
        
        # Setup datetime mock
        mock_now = datetime.datetime(2023, 5, 1, 12, 0, 0)
        mock_datetime.datetime.now.return_value = mock_now
        
        # Configure function_repository mock
        mock_repo.auto_execute.return_value = {
            "status": "updated",
            "id": 123
        }
        
        # Data with updated_at field
        data_with_updated_at = {
            "id": 123,
            "name": "Test Record",
            "updated_at": "2023-01-01T00:00:00"
        }
        
        # Execute function
        result = soft_delete(self.db, self.entity_id, data_with_updated_at)
        
        # Verify function_repository was called with correct parameters
        expected_data = data_with_updated_at.copy()
        expected_data["deleted_mark"] = True
        expected_data["updated_at"] = mock_now
        
        mock_repo.auto_execute.assert_called_once_with(
            "update", self.db, self.entity_id, expected_data
        )
        
        # Verify results
        self.assertEqual(result["status"], "deleted")
        self.assertEqual(result["id"], 123)
    
    @patch('app.services.system_functions.function_repository')
    @patch('app.services.database_utils.CaseConverter')
    @patch('app.services.system_functions.datetime')
    def test_update_failure(self, mock_datetime, mock_converter, mock_repo):
        """Test when the update operation fails"""
        # Setup mocks
        execute_mock = Mock()
        execute_mock.scalar.return_value = "TestTable"
        self.db.execute.return_value = execute_mock
        
        # Setup CaseConverter mock
        mock_converter.to_snake_case.return_value = "test_table"
        
        # Setup datetime mock
        mock_now = datetime.datetime(2023, 5, 1, 12, 0, 0)
        mock_datetime.datetime.now.return_value = mock_now
        
        # Configure function_repository mock to return an error
        mock_repo.auto_execute.return_value = {
            "status": "error",
            "message": "Record not found"
        }
        
        # Execute function
        result = soft_delete(self.db, self.entity_id, self.record_data)
        
        # Verify results indicate failure
        self.assertEqual(result["status"], "error")
        self.assertEqual(result["message"], "Failed to mark record as deleted")

if __name__ == '__main__':
    unittest.main()