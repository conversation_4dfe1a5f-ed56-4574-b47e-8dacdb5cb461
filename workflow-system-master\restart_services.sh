#!/bin/bash

# <PERSON><PERSON>t to check and restart required services using nohup

echo "Checking and restarting services..."

# Check and start chat-yaml-builder API backend
if ! pgrep -f "api_chat_yaml_backend.py" > /dev/null; then
    echo "Starting chat-yaml-builder API backend..."
    cd /home/<USER>/workflow-system
    cd chat-yaml-builder
    nohup python3 api_chat_yaml_backend.py > api_chat_yaml.log 2>&1 &
    CHAT_YAML_PID=$!
    echo "Started chat-yaml-builder API backend with PID: $CHAT_YAML_PID"
    cd ..
else
    echo "chat-yaml-builder API backend is already running."
fi

# Check and start backend FastAPI service
if ! pgrep -f "uvicorn backend.app.main:app" > /dev/null; then
    echo "Starting backend FastAPI service..."
    cd /home/<USER>/workflow-system
    nohup python3 -m uvicorn backend.app.main:app --host 0.0.0.0 --port 8020 --reload > backend/logs/backend_api.log 2>&1 &
    BACKEND_PID=$!
    echo "Started backend FastAPI service with PID: $BACKEND_PID"
else
    echo "backend FastAPI service is already running."
fi

# Check and start enterprise-design-time app
if ! pgrep -f "enterprise-design-time/app.py" > /dev/null; then
    echo "Starting enterprise-design-time app..."
    cd /home/<USER>/workflow-system
    nohup python3 enterprise-design-time/app.py > enterprise-design-time/logs/enterprise_design_time.log 2>&1 &
    DESIGN_TIME_PID=$!
    echo "Started enterprise-design-time app with PID: $DESIGN_TIME_PID"
else
    echo "enterprise-design-time app is already running."
fi

# Check if streamlit chat-yaml-builder is running
if ! pgrep -f "streamlit run streamlit_chat_yaml_builder.py" > /dev/null; then
    echo "Starting streamlit chat-yaml-builder..."
    cd /home/<USER>/workflow-system/chat-yaml-builder
    nohup /home/<USER>/workflow-system/backend/venv/bin/python3 /home/<USER>/workflow-system/backend/venv/bin/streamlit run streamlit_chat_yaml_builder.py > streamlit_chat_yaml.log 2>&1 &
    STREAMLIT_PID=$!
    echo "Started streamlit chat-yaml-builder with PID: $STREAMLIT_PID"
else
    echo "streamlit chat-yaml-builder is already running."
fi

# Check Docker containers
echo "Checking Docker containers..."
if ! docker ps | grep -q "workflow_mongodb"; then
    echo "Starting MongoDB container..."
    docker start workflow_mongodb
fi

if ! docker ps | grep -q "workflow_mongo_express"; then
    echo "Starting Mongo Express container..."
    docker start workflow_mongo_express
fi

if ! docker ps | grep -q "workflow_postgres"; then
    echo "Starting PostgreSQL container..."
    docker start workflow_postgres
fi

if ! docker ps | grep -q "workflow_pgadmin"; then
    echo "Starting PGAdmin container..."
    docker start workflow_pgadmin
fi

if ! docker ps | grep -q "workflow_redis"; then
    echo "Starting Redis container..."
    docker start workflow_redis
fi

if ! docker ps | grep -q "workflow_redis_commander"; then
    echo "Starting Redis Commander container..."
    docker start workflow_redis_commander
fi

if ! docker ps | grep -q "workflow_engine"; then
    echo "Starting Workflow Engine container..."
    docker start workflow_engine
fi

echo "All services checked and started if needed!"
echo "You can check the logs in the respective log files."
