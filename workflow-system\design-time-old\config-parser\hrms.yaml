# Hotel Reservation System

# Tenant Configuration
tenant:
  id: "T002"
  name: "Hotel001"
  roles:
    - id: "R001"
      name: "Hotel<PERSON>ana<PERSON>"
      inherits_from: null
      access:
        entities:
          - entity_id: "E001"  # Reservation
            permissions: ["Read", "Write", "Delete", "Assign"]
          - entity_id: "E002"  # Guest
            permissions: ["Read", "Write", "Delete"]
          - entity_id: "E003"  # Room
            permissions: ["Read", "Write", "Delete"]
        objectives:
          - objective_id: "GO001"  # Hotel Reservation Management
            permissions: ["Execute", "Transact"]
          - objective_id: "GO001.LO003"  # Payment Processing
            permissions: ["Execute"]

    - id: "R002"
      name: "Receptionist"
      inherits_from: null
      access:
        entities:
          - entity_id: "E001"  # Reservation
            permissions: ["Read", "Write"]
          - entity_id: "E002"  # Guest
            permissions: ["Read", "Write"]
          - entity_id: "E003"  # Room
            permissions: ["Read"]
        objectives:
          - objective_id: "GO001"  # Hotel Reservation Management
            permissions: ["Execute"]
          - objective_id: "GO001.LO002"  # Reservation Confirmation
            permissions: ["Execute"]

    - id: "R003"
      name: "InventoryManager"
      inherits_from: null
      access:
        entities:
          - entity_id: "E001"  # Reservation
            permissions: ["Read"]
          - entity_id: "E003"  # Room
            permissions: ["Read", "Write", "Delete"]
          - entity_id: "E004"  # RoomType
            permissions: ["Read", "Write", "Delete"]
        objectives:
          - objective_id: "GO001.LO001"  # Room Availability Check
            permissions: ["Execute", "Transact"]

# Permission Types Definition
permission_types:
  - id: "Execute"
    description: "Can execute only their own assigned transactions"
    capabilities: ["ExecuteOwn"]

  - id: "Transact"
    description: "Can view other's transactions and execute both their own and others in the same group"
    capabilities: ["ExecuteOwn", "ExecuteOthers", "ViewGroup"]

  - id: "Information"
    description: "Can view all transaction information but cannot execute any transactions"
    capabilities: ["ViewAll"]

# Entities Definition
entities:
  # Regular Entity: Reservation
  - id: "E001"
    name: "Reservation"
    version: "1.0"
    status: "Active"
    type: "Regular"
    attributes_metadata:
      attribute_prefix: "At"
      attribute_map:
        "At101": "ReservationID"
        "At102": "GuestID"
        "At103": "RoomID"
        "At104": "CheckInDate"
        "At105": "CheckOutDate"
        "At106": "RoomType"
        "At107": "StayDuration"
        "At108": "TotalAmount"
        "At109": "Status"
        "At110": "SpecialRequests"
        "At111": "CreatedDate"
      required_attributes: ["At101", "At102", "At103", "At104", "At105", "At106", "At109"]
    attributes:
      - id: "At101"
        name: "ReservationID"
        display_name: "Reservation ID"
        datatype: "String"
        version: "1.0"
        status: "Deployed"
        required: true
        validations:
          - rule: "Must be a unique identifier"
            expression: "^RES-[0-9]{8}$"

      - id: "At102"
        name: "GuestID"
        display_name: "Guest ID"
        datatype: "Reference"
        version: "1.0"
        status: "Deployed"
        references: "E002"  # Guest
        required: true

      - id: "At103"
        name: "RoomID"
        display_name: "Room ID"
        datatype: "Reference"
        version: "1.0"
        status: "Deployed"
        references: "E003"  # Room
        required: true

      - id: "At104"
        name: "CheckInDate"
        display_name: "Check-In Date"
        datatype: "DateTime"
        version: "1.0"
        status: "Deployed"
        required: true
        validations:
          - rule: "Must be a valid date"
            expression: "^\\d{4}-\\d{2}-\\d{2}$"

      - id: "At105"
        name: "CheckOutDate"
        display_name: "Check-Out Date"
        datatype: "DateTime"
        version: "1.0"
        status: "Deployed"
        required: true
        validations:
          - rule: "Must be a valid date and after Check-In Date"
            expression: "^\\d{4}-\\d{2}-\\d{2}$"
            condition: "CheckOutDate > CheckInDate"

      - id: "At106"
        name: "RoomType"
        display_name: "Room Type"
        datatype: "Enum"
        version: "1.0"
        status: "Deployed"
        required: true
        values: ["Standard", "Deluxe", "Suite", "Family"]

      - id: "At107"
        name: "StayDuration"
        display_name: "Stay Duration (Nights)"
        datatype: "Integer"
        version: "1.0"
        status: "Deployed"
        required: false

      - id: "At108"
        name: "TotalAmount"
        display_name: "Total Amount"
        datatype: "Number"
        version: "1.0"
        status: "Deployed"
        required: false

      - id: "At109"
        name: "Status"
        display_name: "Reservation Status"
        datatype: "Enum"
        version: "1.0"
        status: "Deployed"
        required: true
        values: ["Pending", "Confirmed", "Cancelled", "CheckedIn", "CheckedOut"]

      - id: "At110"
        name: "SpecialRequests"
        display_name: "Special Requests"
        datatype: "String"
        version: "1.0"
        status: "Deployed"
        required: false

      - id: "At111"
        name: "CreatedDate"
        display_name: "Created Date"
        datatype: "DateTime"
        version: "1.0"
        status: "Deployed"
        required: false

    nested_entities:
      - entity_id: "E005"  # PaymentDetails
        relationship: "OneToOne"
        constraints:
          - "Payment must be completed for confirmed reservations"

    relationships:
      - entity_id: "E002"  # Guest
        type: "ManyToOne"
        through_attribute: "E001.At102"  # Reservation.GuestID
        to_attribute: "E002.At101"  # Guest.GuestID

      - entity_id: "E003"  # Room
        type: "ManyToOne"
        through_attribute: "E001.At103"  # Reservation.RoomID
        to_attribute: "E003.At101"  # Room.RoomID

  # Regular Entity: Guest
  - id: "E002"
    name: "Guest"
    version: "1.0"
    status: "Active"
    type: "Regular"
    attributes_metadata:
      attribute_prefix: "At"
      attribute_map:
        "At101": "GuestID"
        "At102": "FirstName"
        "At103": "LastName"
        "At104": "Email"
        "At105": "Phone"
        "At106": "Nationality"
      required_attributes: ["At101", "At102", "At103", "At104"]
    attributes:
      - id: "At101"
        name: "GuestID"
        display_name: "Guest ID"
        datatype: "String"
        version: "1.0"
        status: "Deployed"
        required: true
        validations:
          - rule: "Must be a unique identifier"
            expression: "^GST-[0-9]{8}$"

      - id: "At102"
        name: "FirstName"
        display_name: "First Name"
        datatype: "String"
        version: "1.0"
        status: "Deployed"
        required: true

      - id: "At103"
        name: "LastName"
        display_name: "Last Name"
        datatype: "String"
        version: "1.0"
        status: "Deployed"
        required: true

      - id: "At104"
        name: "Email"
        display_name: "Email"
        datatype: "String"
        version: "1.0"
        status: "Deployed"
        required: true
        validations:
          - rule: "Email format validation"
            expression: "^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$"

      - id: "At105"
        name: "Phone"
        display_name: "Phone Number"
        datatype: "String"
        version: "1.0"
        status: "Deployed"
        required: false
        validations:
          - rule: "Phone format validation"
            expression: "^\\+?[0-9]{10,15}$"

      - id: "At106"
        name: "Nationality"
        display_name: "Nationality"
        datatype: "String"
        version: "1.0"
        status: "Deployed"
        required: false

    nested_entities:
      - entity_id: "E006"  # GuestPreferences
        relationship: "OneToMany"
        constraints:
          - "Preferences must be unique per guest"

  # Regular Entity: Room
  - id: "E003"
    name: "Room"
    version: "1.0"
    status: "Active"
    type: "Regular"
    attributes_metadata:
      attribute_prefix: "At"
      attribute_map:
        "At101": "RoomID"
        "At102": "RoomNumber"
        "At103": "RoomType"
        "At104": "Status"
        "At105": "RatePerNight"
      required_attributes: ["At101", "At102", "At103", "At104"]
    attributes:
      - id: "At101"
        name: "RoomID"
        display_name: "Room ID"
        datatype: "String"
        version: "1.0"
        status: "Deployed"
        required: true
        validations:
          - rule: "Must be a unique identifier"
            expression: "^RM-[0-9]{4}$"

      - id: "At102"
        name: "RoomNumber"
        display_name: "Room Number"
        datatype: "String"
        version: "1.0"
        status: "Deployed"
        required: true

      - id: "At103"
        name: "RoomType"
        display_name: "Room Type"
        datatype: "Enum"
        version: "1.0"
        status: "Deployed"
        required: true
        values: ["Standard", "Deluxe", "Suite", "Family"]

      - id: "At104"
        name: "Status"
        display_name: "Room Status"
        datatype: "Enum"
        version: "1.0"
        status: "Deployed"
        required: true
        values: ["Available", "Occupied", "Maintenance"]

      - id: "At105"
        name: "RatePerNight"
        display_name: "Rate Per Night"
        datatype: "Number"
        version: "1.0"
        status: "Deployed"
        required: false

    relationships:
      - entity_id: "E004"  # RoomType
        type: "ManyToOne"
        through_attribute: "E003.At103"  # Room.RoomType
        to_attribute: "E004.At101"  # RoomType.TypeName

  # Regular Entity: RoomType
  - id: "E004"
    name: "RoomType"
    version: "1.0"
    status: "Active"
    type: "Regular"
    attributes_metadata:
      attribute_prefix: "At"
      attribute_map:
        "At101": "TypeName"
        "At102": "BaseRate"
        "At103": "Capacity"
      required_attributes: ["At101", "At102", "At103"]
    attributes:
      - id: "At101"
        name: "TypeName"
        display_name: "Type Name"
        datatype: "Enum"
        version: "1.0"
        status: "Deployed"
        required: true
        values: ["Standard", "Deluxe", "Suite", "Family"]

      - id: "At102"
        name: "BaseRate"
        display_name: "Base Rate"
        datatype: "Number"
        version: "1.0"
        status: "Deployed"
        required: true

      - id: "At103"
        name: "Capacity"
        display_name: "Capacity"
        datatype: "Integer"
        version: "1.0"
        status: "Deployed"
        required: true

  # Nested Entity: PaymentDetails
  - id: "E005"
    name: "PaymentDetails"
    version: "1.0"
    status: "Active"
    type: "Nested"
    parent_entities: ["E001"]  # Reservation
    attributes_metadata:
      attribute_prefix: "At"
      attribute_map:
        "At201": "PaymentID"
        "At202": "Amount"
        "At203": "Method"
        "At204": "Status"
        "At205": "TransactionDate"
      required_attributes: ["At201", "At202", "At203", "At204", "At205"]
    attributes:
      - id: "At201"
        name: "PaymentID"
        display_name: "Payment ID"
        datatype: "String"
        version: "1.0"
        status: "Deployed"
        required: true
        validations:
          - rule: "Must be a unique identifier"
            expression: "^PAY-[0-9]{8}$"

      - id: "At202"
        name: "Amount"
        display_name: "Amount"
        datatype: "Number"
        version: "1.0"
        status: "Deployed"
        required: true

      - id: "At203"
        name: "Method"
        display_name: "Payment Method"
        datatype: "Enum"
        version: "1.0"
        status: "Deployed"
        required: true
        values: ["Credit Card", "Debit Card", "UPI", "Cash"]

      - id: "At204"
        name: "Status"
        display_name: "Payment Status"
        datatype: "Enum"
        version: "1.0"
        status: "Deployed"
        required: true
        values: ["Success", "Failed", "Pending"]

      - id: "At205"
        name: "TransactionDate"
        display_name: "Transaction Date"
        datatype: "DateTime"
        version: "1.0"
        status: "Deployed"
        required: true
        validations:
          - rule: "Must be a valid date"
            expression: "^\\d{4}-\\d{2}-\\d{2}$"

  # Nested Entity: GuestPreferences
  - id: "E006"
    name: "GuestPreferences"
    version: "1.0"
    status: "Active"
    type: "Nested"
    parent_entities: ["E002"]  # Guest
    attributes_metadata:
      attribute_prefix: "At"
      attribute_map:
        "At301": "PreferenceType"
        "At302": "Value"
      required_attributes: ["At301", "At302"]
    attributes:
      - id: "At301"
        name: "PreferenceType"
        display_name: "Preference Type"
        datatype: "Enum"
        version: "1.0"
        status: "Deployed"
        required: true
        values: ["Room Location", "Bed Type", "Meal Plan", "Other"]

      - id: "At302"
        name: "Value"
        display_name: "Preference Value"
        datatype: "String"
        version: "1.0"
        status: "Deployed"
        required: true

  # System Entity: WorkflowResults
  - id: "E007"
    name: "WorkflowResults"
    version: "1.0"
    status: "Active"
    type: "System"
    description: "Contains workflow execution results and metrics for hotel reservations"
    attributes_metadata:
      attribute_prefix: "At"
      attribute_map:
        "At401": "ProcessedCount"
        "At402": "ConfirmedCount"
        "At403": "CancelledCount"
        "At404": "Duration"
        "At405": "StartTime"
        "At406": "EndTime"
        "At407": "Status"
      required_attributes: ["At401", "At405", "At406", "At407"]
    attributes:
      - id: "At401"
        name: "ProcessedCount"
        display_name: "Processed Count"
        datatype: "Integer"
        version: "1.0"
        status: "Deployed"
        required: true
        description: "Number of reservations processed in the workflow"
        validations:
          - rule: "Must be a non-negative integer"
            expression: ">= 0"

      - id: "At402"
        name: "ConfirmedCount"
        display_name: "Confirmed Count"
        datatype: "Integer"
        version: "1.0"
        status: "Deployed"
        required: false
        description: "Number of reservations confirmed in the workflow"
        validations:
          - rule: "Must be a non-negative integer"
            expression: ">= 0"

      - id: "At403"
        name: "CancelledCount"
        display_name: "Cancelled Count"
        datatype: "Integer"
        version: "1.0"
        status: "Deployed"
        required: false
        description: "Number of reservations cancelled in the workflow"
        validations:
          - rule: "Must be a non-negative integer"
            expression: ">= 0"

      - id: "At404"
        name: "Duration"
        display_name: "Duration (ms)"
        datatype: "Integer"
        version: "1.0"
        status: "Deployed"
        required: false
        description: "Workflow execution duration in milliseconds"
        validations:
          - rule: "Must be a non-negative integer"
            expression: ">= 0"

      - id: "At405"
        name: "StartTime"
        display_name: "Start Time"
        datatype: "DateTime"
        version: "1.0"
        status: "Deployed"
        required: true
        description: "Workflow start timestamp"
        validations:
          - rule: "Must be a valid date"
            expression: "^\\d{4}-\\d{2}-\\d{2}T\\d{2}:\\d{2}:\\d{2}Z$"

      - id: "At406"
        name: "EndTime"
        display_name: "End Time"
        datatype: "DateTime"
        version: "1.0"
        status: "Deployed"
        required: true
        description: "Workflow end timestamp"
        validations:
          - rule: "Must be a valid date and after StartTime"
            expression: "^\\d{4}-\\d{2}-\\d{2}T\\d{2}:\\d{2}:\\d{2}Z$"
            condition: "EndTime > StartTime"

      - id: "At407"
        name: "Status"
        display_name: "Status"
        datatype: "String"
        version: "1.0"
        status: "Deployed"
        required: true
        description: "Final workflow execution status"
        values: ["Completed", "Failed", "Partial", "Cancelled"]

# Global Objectives
global_objectives:
  - id: "GO001"
    name: "Hotel Reservation System"
    version: "1.0"
    status: "Active"
    
    local_objectives:
      # LO001: Room Availability Check
      - id: "LO001"
        contextual_id: "GO001.LO001"
        name: "Room Availability Check"
        function_type: "Search"
        execution_pathway:
          type: "Alternative"
          conditions:
            - condition:
                condition_type: "attribute_comparison"
                entity: "E001"
                attribute: "At301"
                operator: "equals"
                value: "Available"
              next_lo: "LO002"
            - condition:
                condition_type: "attribute_comparison"
                entity: "E001"
                attribute: "At301"
                operator: "equals"
                value: "Unavailable"
              next_lo: "LO004"

        agent_stack:
          agents:
            - role: "R003"
              rights: ["Execute"]

        input_stack:
          description: "Receive inputs for room availability check"
          inputs:
            - id: "IN201"
              slot_id: "E001.At301.IN201"
              contextual_id: "GO001.LO001.IN201"
              source:
                type: "System"
                description: "Check-in Date"
              required: true

            - id: "IN202"
              slot_id: "E001.At302.IN202"
              contextual_id: "GO001.LO001.IN202"
              source:
                type: "System"
                description: "Check-out Date"
              required: true

            - id: "IN203"
              slot_id: "E001.At303.IN203"
              contextual_id: "GO001.LO001.IN203"
              source:
                type: "System"
                description: "Room Type"
              required: true

        output_stack:
          description: "Outputs from room availability check"
          outputs:
            - id: "OUT201"
              slot_id: "ExecutionStatus.OUT201"
              contextual_id: "GO001.LO001.OUT201"
              source:
                type: "System"
                description: "Success/failure of LO001"

            - id: "OUT202"
              slot_id: "E001.At301.OUT202"
              contextual_id: "GO001.LO001.OUT202"
              source:
                type: "System"
                description: "Availability Status"
                value: "E001.At301"

        data_mapping_stack:
          description: "Pass availability status to next LO"
          mappings:
            - id: "LM001"
              source: "GO001.LO001.OUT202"
              target: "GO001.LO002.IN301"
              mapping_type: "Conditional"
              condition:
                condition_type: "attribute_comparison"
                entity: "E001"
                attribute: "At301"
                operator: "equals"
                value: "Available"

            - id: "LM002"
              source: "GO001.LO001.OUT202"
              target: "GO001.LO004.IN301"
              mapping_type: "Conditional"
              condition:
                condition_type: "attribute_comparison"
                entity: "E001"
                attribute: "At301"
                operator: "equals"
                value: "Unavailable"

      # LO002: Reservation Confirmation
      - id: "LO002"
        contextual_id: "GO001.LO002"
        name: "Reservation Confirmation"
        function_type: "Update"
        execution_pathway:
          type: "Sequential"
          next_lo: "LO003"

        agent_stack:
          agents:
            - role: "R002"
              rights: ["Execute", "Update"]

        input_stack:
          description: "Receive inputs for reservation confirmation"
          inputs:
            - id: "IN301"
              slot_id: "E001.At301.IN301"
              contextual_id: "GO001.LO002.IN301"
              source:
                type: "Input"
                description: "Availability Status"
              required: true

            - id: "IN302"
              slot_id: "E001.At304.IN302"
              contextual_id: "GO001.LO002.IN302"
              source:
                type: "System"
                description: "Guest ID"
              required: true

            - id: "IN303"
              slot_id: "E001.At305.IN303"
              contextual_id: "GO001.LO002.IN303"
              source:
                type: "System"
                description: "Reservation ID"
              required: true

            - id: "IN304"
              slot_id: "E001.At306.IN304"
              contextual_id: "GO001.LO002.IN304"
              source:
                type: "User"
                description: "Special Requests"
              required: false

        output_stack:
          description: "Outputs from reservation confirmation"
          outputs:
            - id: "OUT301"
              slot_id: "ExecutionStatus.OUT301"
              contextual_id: "GO001.LO002.OUT301"
              source:
                type: "System"
                description: "Success/failure of LO002"

            - id: "OUT302"
              slot_id: "E001.At305.OUT302"
              contextual_id: "GO001.LO002.OUT302"
              source:
                type: "Input"
                description: "Reservation ID"
                value: "E001.At305"

        data_mapping_stack:
          description: "Pass reservation details to payment processing"
          mappings:
            - id: "LM003"
              source: "GO001.LO002.OUT302"
              target: "GO001.LO003.IN401"
              mapping_type: "Direct"

      # LO003: Payment Processing
      - id: "LO003"
        contextual_id: "GO001.LO003"
        name: "Payment Processing"
        function_type: "Update"
        execution_pathway:
          type: "Alternative"
          conditions:
            - condition:
                condition_type: "attribute_comparison"
                entity: "E001"
                attribute: "At401"
                operator: "equals"
                value: "Success"
              next_lo: "LO005"
            - condition:
                condition_type: "attribute_comparison"
                entity: "E001"
                attribute: "At401"
                operator: "equals"
                value: "Failed"
              next_lo: "LO004"

        agent_stack:
          agents:
            - role: "R001"
              rights: ["Execute"]

        input_stack:
          description: "Receive inputs for payment processing"
          inputs:
            - id: "IN401"
              slot_id: "E001.At401.IN401"
              contextual_id: "GO001.LO003.IN401"
              source:
                type: "Input"
                description: "Reservation ID"
              required: true

            - id: "IN402"
              slot_id: "E001.At402.IN402"
              contextual_id: "GO001.LO003.IN402"
              source:
                type: "System"
                description: "Total Amount"
              required: true

            - id: "IN403"
              slot_id: "E001.At403.IN403"
              contextual_id: "GO001.LO003.IN403"
              source:
                type: "User"
                description: "Payment Method"
              required: true
              validations:
                - rule: "Must be a valid payment method"
                  rule_type: "enum_check"
                  allowed_values: ["Credit Card", "Debit Card", "UPI", "Cash"]

        output_stack:
          description: "Outputs from payment processing"
          outputs:
            - id: "OUT401"
              slot_id: "ExecutionStatus.OUT401"
              contextual_id: "GO001.LO003.OUT401"
              source:
                type: "System"
                description: "Success/failure of LO003"

            - id: "OUT402"
              slot_id: "E001.At401.OUT402"
              contextual_id: "GO001.LO003.OUT402"
              source:
                type: "System"
                description: "Payment Status"
                value: "E001.At401"

        data_mapping_stack:
          description: "Pass payment status to next LO"
          mappings:
            - id: "LM004"
              source: "GO001.LO003.OUT402"
              target: "GO001.LO005.IN501"
              mapping_type: "Conditional"
              condition:
                condition_type: "attribute_comparison"
                entity: "E001"
                attribute: "At401"
                operator: "equals"
                value: "Success"

            - id: "LM005"
              source: "GO001.LO003.OUT402"
              target: "GO001.LO004.IN501"
              mapping_type: "Conditional"
              condition:
                condition_type: "attribute_comparison"
                entity: "E001"
                attribute: "At401"
                operator: "equals"
                value: "Failed"

      # LO004: Reservation Cancellation
      - id: "LO004"
        contextual_id: "GO001.LO004"
        name: "Reservation Cancellation"
        function_type: "Update"
        execution_pathway:
          type: "Terminal"

        agent_stack:
          agents:
            - role: "R002"
              rights: ["Execute", "Update"]

        input_stack:
          description: "Receive inputs for reservation cancellation"
          inputs:
            - id: "IN501"
              slot_id: "E001.At501.IN501"
              contextual_id: "GO001.LO004.IN501"
              source:
                type: "Input"
                description: "Reservation ID"
              required: true

            - id: "IN502"
              slot_id: "E001.At502.IN502"
              contextual_id: "GO001.LO004.IN502"
              source:
                type: "System"
                description: "Cancellation Reason"
              required: true
              validations:
                - rule: "Must be a valid reason"
                  rule_type: "enum_check"
                  allowed_values: ["Room Unavailable", "Payment Failed", "Guest Request"]

        output_stack:
          description: "Outputs from reservation cancellation"
          outputs:
            - id: "OUT501"
              slot_id: "ExecutionStatus.OUT501"
              contextual_id: "GO001.LO004.OUT501"
              source:
                type: "System"
                description: "Success/failure of LO004"

            - id: "OUT502"
              slot_id: "E001.At503.OUT502"
              contextual_id: "GO001.LO004.OUT502"
              source:
                type: "System"
                description: "Cancellation Status"
                value: "Cancelled"

        data_mapping_stack:
          description: "No further mapping as this is a terminal process"
          mappings: []

      # LO005: Final Reservation Confirmation
      - id: "LO005"
        contextual_id: "GO001.LO005"
        name: "Final Reservation Confirmation"
        function_type: "Update"
        execution_pathway:
          type: "Terminal"

        agent_stack:
          agents:
            - role: "R001"
              rights: ["Execute"]

        input_stack:
          description: "Receive inputs for final reservation confirmation"
          inputs:
            - id: "IN601"
              slot_id: "E001.At601.IN601"
              contextual_id: "GO001.LO005.IN601"
              source:
                type: "Input"
                description: "Reservation ID"
              required: true

            - id: "IN602"
              slot_id: "E001.At602.IN602"
              contextual_id: "GO001.LO005.IN602"
              source:
                type: "System"
                description: "Confirmation Details"
              required: true

        output_stack:
          description: "Outputs from final reservation confirmation"
          outputs:
            - id: "OUT601"
              slot_id: "ExecutionStatus.OUT601"
              contextual_id: "GO001.LO005.OUT601"
              source:
                type: "System"
                description: "Success/failure of LO005"

            - id: "OUT602"
              slot_id: "E001.At603.OUT602"
              contextual_id: "GO001.LO005.OUT602"
              source:
                type: "System"
                description: "Final Confirmation Status"
                value: "Confirmed"

        data_mapping_stack:
          description: "No further mapping as this is a terminal process"
          mappings: []
