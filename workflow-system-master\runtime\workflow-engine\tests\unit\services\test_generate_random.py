import unittest
from unittest.mock import patch
import re
import uuid
import string
import random
import datetime
from typing import Dict, Any
import sys
import os

# Add project root to path if needed
project_root = '/home/<USER>/workflow-system/runtime/workflow-engine'
if project_root not in sys.path:
    sys.path.insert(0, project_root)

# Import the function to test
from app.services.system_functions import generate_random

class TestGenerateRandom(unittest.TestCase):
    def test_random_string_default(self):
        """Test generating a random string with default parameters."""
        # Set seed for reproducibility
        random.seed(42)
        
        result = generate_random()
        
        self.assertIsInstance(result, str)
        self.assertEqual(len(result), 8)  # Default length is 8
        
        # Default includes lowercase, uppercase, and digits
        allowed_chars = set(string.ascii_lowercase + string.ascii_uppercase + string.digits)
        self.assertTrue(all(c in allowed_chars for c in result))
    
    def test_random_string_custom_length(self):
        """Test generating a random string with custom length."""
        result = generate_random("string", 12)
        
        self.assertIsInstance(result, str)
        self.assertEqual(len(result), 12)
    
    def test_random_string_custom_chars(self):
        """Test generating a random string with custom character set."""
        # Set seed for reproducibility
        random.seed(42)
        
        custom_chars = "abc123"
        result = generate_random("string", 10, options={"chars": custom_chars})
        
        self.assertIsInstance(result, str)
        self.assertEqual(len(result), 10)
        
        # Only allowed chars should be used
        allowed_chars = set(custom_chars)
        self.assertTrue(all(c in allowed_chars for c in result))
    
    def test_random_string_lowercase_only(self):
        """Test generating a random string with lowercase letters only."""
        result = generate_random("string", options={"lowercase": True, "uppercase": False, "digits": False})
        
        self.assertIsInstance(result, str)
        self.assertEqual(len(result), 8)  # Default length
        
        # Only lowercase should be used
        allowed_chars = set(string.ascii_lowercase)
        self.assertTrue(all(c in allowed_chars for c in result))
    
    def test_random_string_uppercase_only(self):
        """Test generating a random string with uppercase letters only."""
        result = generate_random("string", options={"lowercase": False, "uppercase": True, "digits": False})
        
        self.assertIsInstance(result, str)
        self.assertEqual(len(result), 8)  # Default length
        
        # Only uppercase should be used
        allowed_chars = set(string.ascii_uppercase)
        self.assertTrue(all(c in allowed_chars for c in result))
    
    def test_random_string_digits_only(self):
        """Test generating a random string with digits only."""
        result = generate_random("string", options={"lowercase": False, "uppercase": False, "digits": True})
        
        self.assertIsInstance(result, str)
        self.assertEqual(len(result), 8)  # Default length
        
        # Only digits should be used
        allowed_chars = set(string.digits)
        self.assertTrue(all(c in allowed_chars for c in result))
    
    def test_random_string_with_special_chars(self):
        """Test generating a random string with special characters included."""
        result = generate_random("string", options={"special": True})
        
        self.assertIsInstance(result, str)
        self.assertEqual(len(result), 8)  # Default length
        
        # Special characters should be included in the allowed set
        allowed_chars = set(string.ascii_lowercase + string.ascii_uppercase + string.digits + string.punctuation)
        self.assertTrue(all(c in allowed_chars for c in result))
    
    def test_random_integer(self):
        """Test generating a random integer."""
        # Set seed for reproducibility
        random.seed(42)
        
        result = generate_random("integer")
        
        self.assertIsInstance(result, int)
        self.assertTrue(1 <= result <= 100)  # Default range is 1-100
    
    def test_random_number(self):
        """Test generating a random number (alias for integer)."""
        # Set seed for reproducibility
        random.seed(42)
        
        result = generate_random("number")
        
        self.assertIsInstance(result, int)
        self.assertTrue(1 <= result <= 100)
    
    def test_random_integer_custom_range(self):
        """Test generating a random integer with custom range."""
        result = generate_random("integer", min_value=500, max_value=1000)
        
        self.assertIsInstance(result, int)
        self.assertTrue(500 <= result <= 1000)
    
    def test_random_decimal(self):
        """Test generating a random decimal number."""
        # Set seed for reproducibility
        random.seed(42)
        
        result = generate_random("decimal")
        
        self.assertIsInstance(result, float)
        self.assertTrue(1 <= result <= 100)  # Default range is 1-100
        self.assertEqual(round(result, 2), result)  # Default 2 decimal places
    
    def test_random_float(self):
        """Test generating a random float (alias for decimal)."""
        # Set seed for reproducibility
        random.seed(42)
        
        result = generate_random("float")
        
        self.assertIsInstance(result, float)
        self.assertTrue(1 <= result <= 100)
        self.assertEqual(round(result, 2), result)  # Default 2 decimal places
    
    def test_random_decimal_custom_places(self):
        """Test generating a random decimal with custom decimal places."""
        result = generate_random("decimal", options={"decimal_places": 4})
        
        self.assertIsInstance(result, float)
        self.assertTrue(1 <= result <= 100)
        self.assertEqual(round(result, 4), result)  # 4 decimal places
    
    def test_random_decimal_custom_range(self):
        """Test generating a random decimal with custom range."""
        result = generate_random("decimal", min_value=0.5, max_value=0.9)
        
        self.assertIsInstance(result, float)
        self.assertTrue(0.5 <= result <= 0.9)
    
    def test_random_uuid(self):
        """Test generating a random UUID."""
        result = generate_random("uuid")
        
        self.assertIsInstance(result, str)
        # UUID should match the expected format
        pattern = r'^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$'
        self.assertTrue(re.match(pattern, result))
        
        # Verify it's a valid UUID
        try:
            uuid_obj = uuid.UUID(result)
            self.assertEqual(str(uuid_obj), result)
        except ValueError:
            self.fail("Generated UUID is not valid")
    
    def test_random_boolean(self):
        """Test generating a random boolean."""
        result = generate_random("boolean")
        
        self.assertIsInstance(result, bool)
        self.assertTrue(result in [True, False])
    
    def test_random_boolean_values(self):
        """Test both possible boolean values are returned."""
        # Instead of trying to mock, we'll just verify that we can get both values
        # by repeatedly calling the function
        seen_true = False
        seen_false = False
        
        # Retry up to 100 times to get both values
        for _ in range(100):
            result = generate_random("boolean")
            if result is True:
                seen_true = True
            else:
                seen_false = True
            
            if seen_true and seen_false:
                break
        
        self.assertTrue(seen_true, "True value was never generated")
        self.assertTrue(seen_false, "False value was never generated")
    
    def test_random_date_default(self):
        """Test generating a random date with default range."""
        result = generate_random("date")
        
        self.assertIsInstance(result, str)
        
        # Date should match the expected format
        pattern = r'^\d{4}-\d{2}-\d{2}$'
        self.assertTrue(re.match(pattern, result))
        
        # Parse the date to verify it's valid
        try:
            date_obj = datetime.datetime.strptime(result, "%Y-%m-%d").date()
            
            # Default start date is 2000-01-01
            start_date = datetime.date(2000, 1, 1)
            # Default end date is today
            end_date = datetime.date.today()
            
            self.assertTrue(start_date <= date_obj <= end_date)
        except ValueError:
            self.fail("Generated date is not valid")
    
    def test_random_date_custom_range(self):
        """Test generating a random date with custom range."""
        options = {
            "start_date": "2020-01-01",
            "end_date": "2020-12-31"
        }
        
        result = generate_random("date", options=options)
        
        self.assertIsInstance(result, str)
        
        # Parse the date to verify it's within range
        date_obj = datetime.datetime.strptime(result, "%Y-%m-%d").date()
        start_date = datetime.datetime.strptime(options["start_date"], "%Y-%m-%d").date()
        end_date = datetime.datetime.strptime(options["end_date"], "%Y-%m-%d").date()
        
        self.assertTrue(start_date <= date_obj <= end_date)
    
    def test_random_date_reversed_range(self):
        """Test generating a random date with reversed range (end before start)."""
        options = {
            "start_date": "2022-01-01",
            "end_date": "2020-01-01"
        }
        
        result = generate_random("date", options=options)
        
        self.assertIsInstance(result, str)
        
        # Parse the date to verify it's within range
        date_obj = datetime.datetime.strptime(result, "%Y-%m-%d").date()
        start_date = datetime.datetime.strptime(options["end_date"], "%Y-%m-%d").date()  # Reversed
        end_date = datetime.datetime.strptime(options["start_date"], "%Y-%m-%d").date()  # Reversed
        
        self.assertTrue(start_date <= date_obj <= end_date)
    
    def test_random_date_with_date_objects(self):
        """Test generating a random date with date objects instead of strings."""
        options = {
            "start_date": datetime.date(2020, 1, 1),
            "end_date": datetime.date(2020, 12, 31)
        }
        
        result = generate_random("date", options=options)
        
        self.assertIsInstance(result, str)
        
        # Parse the date to verify it's within range
        date_obj = datetime.datetime.strptime(result, "%Y-%m-%d").date()
        
        self.assertTrue(options["start_date"] <= date_obj <= options["end_date"])
    
    def test_invalid_type(self):
        """Test with an invalid random type."""
        with self.assertRaises(ValueError) as context:
            generate_random("invalid_type")
        
        self.assertTrue("Unsupported random type" in str(context.exception))
    
    def test_case_insensitive_type(self):
        """Test that type string is case-insensitive."""
        # Since random values are, well, random, we can't directly compare outputs
        # Instead, we'll verify that both calls succeed with the same type
        result_lower = generate_random("string", length=8)
        result_upper = generate_random("STRING", length=8)
        
        self.assertIsInstance(result_lower, str)
        self.assertIsInstance(result_upper, str)
        self.assertEqual(len(result_lower), 8)
        self.assertEqual(len(result_upper), 8)
    
    def test_none_options(self):
        """Test with None options parameter."""
        # Should work the same as empty dict
        result = generate_random("string", options=None)
        
        self.assertIsInstance(result, str)
        self.assertEqual(len(result), 8)

if __name__ == '__main__':
    unittest.main()