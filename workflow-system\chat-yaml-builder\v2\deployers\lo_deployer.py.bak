"""
LO Deployer for YAML Builder v2

This module provides functionality for deploying Local Objective (LO) components to the database.
"""

import os
import sys
import json
import logging
from typing import Dict, List, Tuple, Any, Optional

# Add parent directory to path to import common modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from db_utils import execute_query, save_to_mongodb
from id_generator import IDGenerator

# Set up logging
logger = logging.getLogger('lo_deployer')

def deploy_lo_definitions(lo_data: Dict, schema_name: str) -> Tuple[bool, List[str]]:
    """
    Deploy Local Objective (LO) definitions to the database.
    
    Args:
        lo_data: Parsed YAML data for LO definitions
        schema_name: Schema name to deploy to
        
    Returns:
        Tuple containing:
            - Boolean indicating if deployment was successful
            - List of messages (warnings, errors, or success messages)
    """
    messages = []
    logger.info(f"Deploying LO definitions to {schema_name}")
    
    try:
        # Check if local_objectives key exists
        if 'local_objectives' not in lo_data:
            logger.error("Missing 'local_objectives' key in LO definition")
            return False, ["Missing 'local_objectives' key in LO definition"]
        
        # Initialize ID generator
        id_generator = IDGenerator(schema_name)
        
        # Deploy each LO
        for lo_name, lo_def in lo_data['local_objectives'].items():
            # Generate LO ID
            lo_id = id_generator.generate_id('lo', lo_name)
            
            # Check if ID already exists and get next available ID if needed
            lo_id = id_generator.get_next_id('lo', lo_id)
            
            success, lo_messages = deploy_single_lo(lo_id, lo_name, lo_def, schema_name, id_generator)
            messages.extend(lo_messages)
            
            if not success:
                return False, messages
        
        # Save to MongoDB for design-time storage
        document = {
            "component_type": "lo_definitions",
            "version_type": "v2",
            "data": lo_data
        }
        mongo_success, mongo_messages, _ = save_to_mongodb("components", document)
        messages.extend(mongo_messages)
        
        if not mongo_success:
            logger.warning("Failed to save LO definitions to MongoDB")
        
        logger.info("LO deployment completed successfully")
        return True, messages
    except Exception as e:
        error_msg = f"LO deployment error: {str(e)}"
        logger.error(error_msg, exc_info=True)
        messages.append(error_msg)
        return False, messages

def deploy_lo_definitions_with_connection(lo_data: Dict, schema_name: str, conn) -> Tuple[bool, List[str]]:
    """
    Deploy Local Objective (LO) definitions to the database using an existing connection.
    
    Args:
        lo_data: Parsed YAML data for LO definitions
        schema_name: Schema name to deploy to
        conn: Database connection
        
    Returns:
        Tuple containing:
            - Boolean indicating if deployment was successful
            - List of messages (warnings, errors, or success messages)
    """
    messages = []
    logger.info(f"Deploying LO definitions to {schema_name} with existing connection")
    
    try:
        # Check if local_objectives key exists
        if 'local_objectives' not in lo_data:
            logger.error("Missing 'local_objectives' key in LO definition")
            return False, ["Missing 'local_objectives' key in LO definition"]
        
        # Initialize ID generator
        id_generator = IDGenerator(schema_name)
        
        # Create cursor
        cursor = conn.cursor()
        
        # Set schema
        cursor.execute(f"SET search_path TO {schema_name}")
        
        # Set constraints to deferred
        cursor.execute("SET CONSTRAINTS ALL DEFERRED")
        
        # Deploy each LO
        for lo_name, lo_def in lo_data['local_objectives'].items():
            # Generate LO ID
            lo_id = id_generator.generate_id('lo', lo_name)
            
            # Get GO ID if specified
            go_id = None
            if 'global_objective' in lo_def:
                go_name = lo_def['global_objective']
                go_id = id_generator.generate_id('go', go_name)
                
                # Log a warning but don't fail if GO doesn't exist
                # This is because the GO might have been inserted in the same transaction
                # and the check_id_exists function won't see it yet
                warning_msg = f"Warning: LO '{lo_name}' references GO '{go_name}'"
                logger.warning(warning_msg)
            
            # Check if LO exists
            cursor.execute(f"SELECT lo_id FROM {schema_name}.local_objectives WHERE lo_id = %s", (lo_id,))
            lo_exists = cursor.fetchone() is not None
            
            if lo_exists:
                # Update existing LO
                query = f"""
                    UPDATE {schema_name}.local_objectives
                    SET go_id = %s,
                        name = %s,
                        description = %s,
                        ui_stack = %s,
                        mapping_stack = %s,
                        version_type = 'v2'
                    WHERE lo_id = %s
                """
                params = (
                    go_id,
                    lo_name,
                    lo_def.get('description', ''),
                    json.dumps(lo_def.get('ui_stack', {})),
                    json.dumps(lo_def.get('mapping_stack', {})),
                    lo_id
                )
                
                cursor.execute(query, params)
                
                messages.append(f"Updated LO '{lo_name}' in {schema_name}.local_objectives")
                logger.info(f"Updated LO '{lo_name}' in {schema_name}.local_objectives")
            else:
                # Insert new LO
                query = f"""
                    INSERT INTO {schema_name}.local_objectives (
                        lo_id, go_id, name, description, ui_stack, mapping_stack, version_type,
                        contextual_id, function_type, workflow_source, system_function
                    ) VALUES (%s, %s, %s, %s, %s, %s, 'v2', %s, %s, %s, %s)
                """
                params = (
                    lo_id,
                    go_id,
                    lo_name,
                    lo_def.get('description', ''),
                    json.dumps(lo_def.get('ui_stack', {})),
                    json.dumps(lo_def.get('mapping_stack', {})),
                    lo_def.get('contextual_id', lo_id),  # Default contextual_id to lo_id
                    lo_def.get('function_type', 'standard'),  # Default function_type to 'standard'
                    lo_def.get('workflow_source', 'system'),  # Default workflow_source to 'system'
                    lo_def.get('system_function', None)  # Default system_function to None
                )
                
                cursor.execute(query, params)
                
                messages.append(f"Inserted LO '{lo_name}' into {schema_name}.local_objectives")
                logger.info(f"Inserted LO '{lo_name}' into {schema_name}.local_objectives")
            
            # Deploy LO input items
            if 'input_items' in lo_def:
                # Delete existing input items
                cursor.execute(f"DELETE FROM {schema_name}.lo_input_items WHERE lo_id = %s", (lo_id,))
                
                # Insert new input items
                for item_name, item_def in lo_def['input_items'].items():
                    # Generate item ID
                    item_id = id_generator.generate_id('input_item', item_name, lo_id)
                    
                    query = f"""
                        INSERT INTO {schema_name}.lo_input_items (
                            item_id, lo_id, name, type, required, default_value, ui_control, help_text, dependency_info
                        ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s)
                    """
                    params = (
                        item_id,
                        lo_id,
                        item_name,
                        item_def.get('type', 'string'),
                        item_def.get('required', False),
                        item_def.get('default', None),
                        item_def.get('ui_control', 'text'),
                        item_def.get('help_text', ''),
                        json.dumps(item_def.get('dependency_info', {}))
                    )
                    
                    cursor.execute(query, params)
                    
                    messages.append(f"Inserted input item '{item_name}' for LO '{lo_id}' into {schema_name}.lo_input_items")
                    logger.info(f"Inserted input item '{item_name}' for LO '{lo_id}' into {schema_name}.lo_input_items")
            
            # Deploy LO output items
            if 'output_items' in lo_def:
                # Delete existing output items
                cursor.execute(f"DELETE FROM {schema_name}.lo_output_items WHERE lo_id = %s", (lo_id,))
                
                # Insert new output items
                for item_name, item_def in lo_def['output_items'].items():
                    # Generate item ID
                    item_id = id_generator.generate_id('output_item', item_name, lo_id)
                    
                    query = f"""
                        INSERT INTO {schema_name}.lo_output_items (
                            item_id, lo_id, name, type
                        ) VALUES (%s, %s, %s, %s)
                    """
                    params = (
                        item_id,
                        lo_id,
                        item_name,
                        item_def.get('type', 'string')
                    )
                    
                    cursor.execute(query, params)
                    
                    messages.append(f"Inserted output item '{item_name}' for LO '{lo_id}' into {schema_name}.lo_output_items")
                    logger.info(f"Inserted output item '{item_name}' for LO '{lo_id}' into {schema_name}.lo_output_items")
            
            # Deploy LO dependencies
            if 'dependencies' in lo_def:
                # Check if lo_dependencies table exists
                cursor.execute(f"""
                    SELECT EXISTS (
                        SELECT FROM information_schema.tables 
                        WHERE table_schema = %s
                        AND table_name = 'lo_dependencies'
                    )
                """, (schema_name,))
                table_exists = cursor.fetchone()[0]
                
                if not table_exists:
                    # Create lo_dependencies table
                    cursor.execute(f"""
                        CREATE TABLE {schema_name}.lo_dependencies (
                            dependency_id VARCHAR(100) PRIMARY KEY,
                            lo_id VARCHAR(50) REFERENCES {schema_name}.local_objectives(lo_id),
                            depends_on_lo_id VARCHAR(50) REFERENCES {schema_name}.local_objectives(lo_id),
                            type VARCHAR(50) NOT NULL,
                            condition TEXT,
                            mapping JSONB
                        )
                    """)
                    
                    messages.append(f"Created {schema_name}.lo_dependencies table")
                    logger.info(f"Created {schema_name}.lo_dependencies table")
                
                # Delete existing dependencies
                cursor.execute(f"DELETE FROM {schema_name}.lo_dependencies WHERE lo_id = %s", (lo_id,))
                
                # Insert new dependencies
                for i, dependency in enumerate(lo_def['dependencies']):
                    # Get dependent LO ID
                    depends_on_lo_name = dependency.get('lo')
                    if not depends_on_lo_name:
                        warning_msg = f"Warning: Dependency {i} for LO '{lo_id}' is missing 'lo'"
                        messages.append(warning_msg)
                        logger.warning(warning_msg)
                        continue
                        
                    depends_on_lo_id = id_generator.generate_id('lo', depends_on_lo_name)
                    
                    # Generate dependency ID
                    dependency_id = f"{lo_id}_dep_{i}"
                    
                    query = f"""
                        INSERT INTO {schema_name}.lo_dependencies (
                            dependency_id, lo_id, depends_on_lo_id, type, condition, mapping
                        ) VALUES (%s, %s, %s, %s, %s, %s)
                    """
                    params = (
                        dependency_id,
                        lo_id,
                        depends_on_lo_id,
                        dependency.get('type', 'sequential'),
                        dependency.get('condition', ''),
                        json.dumps(dependency.get('mapping', {}))
                    )
                    
                    cursor.execute(query, params)
                    
                    messages.append(f"Inserted dependency on LO '{depends_on_lo_name}' for LO '{lo_id}' into {schema_name}.lo_dependencies")
                    logger.info(f"Inserted dependency on LO '{depends_on_lo_name}' for LO '{lo_id}' into {schema_name}.lo_dependencies")
        
        # Save to MongoDB for design-time storage
        document = {
            "component_type": "lo_definitions",
            "version_type": "v2",
            "data": lo_data
        }
        mongo_success, mongo_messages, _ = save_to_mongodb("components", document)
        messages.extend(mongo_messages)
        
        if not mongo_success:
            logger.warning("Failed to save LO definitions to MongoDB")
        
        logger.info("LO deployment completed successfully")
        return True, messages
    except Exception as e:
        error_msg = f"LO deployment error: {str(e)}"
        logger.error(error_msg, exc_info=True)
        messages.append(error_msg)
        return False, messages

def deploy_single_lo(lo_id: str, lo_name: str, lo_def: Dict, schema_name: str, id_generator: IDGenerator) -> Tuple[bool, List[str]]:
    """
    Deploy a single Local Objective (LO) to the database.
    
    Args:
        lo_id: ID of the LO
        lo_name: Name of the LO
        lo_def: LO definition
        schema_name: Schema name to deploy to
        id_generator: ID generator instance
        
    Returns:
        Tuple containing:
            - Boolean indicating if deployment was successful
            - List of messages (warnings, errors, or success messages)
    """
    messages = []
    
    try:
        # Debug logging
        logger.info(f"Deploying LO: {lo_name} with ID: {lo_id}")
        logger.info(f"LO definition keys: {list(lo_def.keys())}")
        if 'input_items' in lo_def:
            logger.info(f"Input items: {list(lo_def['input_items'].keys())}")
        if 'output_items' in lo_def:
            logger.info(f"Output items: {list(lo_def['output_items'].keys())}")
        
        # Validate LO ID
        if not id_generator.validate_id('lo', lo_id):
            error_msg = f"Invalid LO ID: {lo_id}"
            logger.error(error_msg)
            messages.append(error_msg)
            return False, messages
        
        # Get GO ID if specified
        go_id = None
        if 'global_objective' in lo_def:
            go_name = lo_def['global_objective']
            go_id = id_generator.generate_id('go', go_name)
            
            # Log a warning but don't fail if GO doesn't exist
            # This is because the GO might have been inserted in the same transaction
            # and the check_id_exists function won't see it yet
            warning_msg = f"Warning: LO '{lo_name}' references non-existent GO '{go_name}'"
            logger.warning(warning_msg)
        
        # Check if LO exists
        success, query_messages, result = execute_query(
            f"SELECT lo_id FROM {schema_name}.local_objectives WHERE lo_id = %s",
            (lo_id,),
            schema_name
        )
        
        if not success:
            messages.extend(query_messages)
            return False, messages
        
        lo_exists = result and len(result) > 0
        
        if lo_exists:
            # Update existing LO
            query = f"""
                UPDATE {schema_name}.local_objectives
                SET go_id = %s,
                    name = %s,
                    description = %s,
                    ui_stack = %s,
                    mapping_stack = %s,
                    version_type = 'v2'
                WHERE lo_id = %s
            """
            params = (
                go_id,
                lo_name,
                lo_def.get('description', ''),
                json.dumps(lo_def.get('ui_stack', {})),
                json.dumps(lo_def.get('mapping_stack', {})),
                lo_id
            )
            
            success, query_messages, _ = execute_query(query, params, schema_name)
            
            if not success:
                messages.extend(query_messages)
                return False, messages
            
            messages.append(f"Updated LO '{lo_name}' in {schema_name}.local_objectives")
            logger.info(f"Updated LO '{lo_name}' in {schema_name}.local_objectives")
        else:
            # Check if the required columns exist
            success, query_messages, result = execute_query(
                f"""
                SELECT EXISTS (
                    SELECT FROM information_schema.columns
                    WHERE table_schema = %s
                    AND table_name = 'local_objectives'
                    AND column_name = 'contextual_id'
                )
                """,
                (schema_name,)
            )

            if not success:
                messages.extend(query_messages)
                return False, messages

            contextual_id_column_exists = result and result[0][0]

            if not contextual_id_column_exists:
                # Add the missing columns
                success, query_messages, _ = execute_query(
                    f"""
                    ALTER TABLE {schema_name}.local_objectives 
                    ADD COLUMN IF NOT EXISTS contextual_id VARCHAR(100) NULL;
                    ALTER TABLE {schema_name}.local_objectives 
                    ADD COLUMN IF NOT EXISTS function_type VARCHAR(50) NULL DEFAULT 'standard'::character varying;
                    ALTER TABLE {schema_name}.local_objectives 
                    ADD COLUMN IF NOT EXISTS workflow_source VARCHAR(50) NULL DEFAULT 'system'::character varying;
                    ALTER TABLE {schema_name}.local_objectives 
                    ADD COLUMN IF NOT EXISTS system_function VARCHAR(100) NULL;
                    """
                )

                if not success:
                    messages.extend(query_messages)
                    return False, messages

                messages.append(f"Added missing columns to {schema_name}.local_objectives")
                logger.info(f"Added missing columns to {schema_name}.local_objectives")

            # Insert new LO
            query = f"""
                INSERT INTO {schema_name}.local_objectives (
                    lo_id, go_id, name, description, ui_stack, mapping_stack, version_type,
                    contextual_id, function_type, workflow_source, system_function
                ) VALUES (%s, %s, %s, %s, %s, %s, 'v2', %s, %s, %s, %s)
            """
            params = (
                lo_id,
                go_id,
                lo_name,
                lo_def.get('description', ''),
                json.dumps(lo_def.get('ui_stack', {})),
                json.dumps(lo_def.get('mapping_stack', {})),
                lo_def.get('contextual_id', lo_id),  # Default contextual_id to lo_id
                lo_def.get('function_type', 'standard'),  # Default function_type to 'standard'
                lo_def.get('workflow_source', 'system'),  # Default workflow_source to 'system'
                lo_def.get('system_function', None)  # Default system_function to None
            )

            success, query_messages, _ = execute_query(query, params, schema_name)
            
            if not success:
                messages.extend(query_messages)
                return False, messages
            
            messages.append(f"Inserted LO '{lo_name}' into {schema_name}.local_objectives")
            logger.info(f"Inserted LO '{lo_name}' into {schema_name}.local_objectives")
        
        # Deploy LO input items
        if 'input_items' in lo_def:
            success, input_messages = deploy_lo_input_items(lo_id, lo_def['input_items'], schema_name, id_generator)
            messages.extend(input_messages)
            
            if not success:
                return False, messages
        
        # Deploy LO output items
        if 'output_items' in lo_def:
            success, output_messages = deploy_lo_output_items(lo_id, lo_def['output_items'], schema_name, id_generator)
            messages.extend(output_messages)
            
            if not success:
                return False, messages
        
        # Deploy LO dependencies
        if 'dependencies' in lo_def:
            success, dep_messages = deploy_lo_dependencies(lo_id, lo_def['dependencies'], schema_name, id_generator)
            messages.extend(dep_messages)
            
            if not success:
                return False, messages
        
        # Deploy data mappings
        if 'mapping_stack' in lo_def and 'mappings' in lo_def['mapping_stack']:
            success, mapping_messages = deploy_data_mappings(lo_id, lo_def['mapping_stack']['mappings'], schema_name, id_generator)
            messages.extend(mapping_messages)
            
            if not success:
                return False, messages
        
        return True, messages
    except Exception as e:
        error_msg = f"Error deploying LO '{lo_name}': {str(e)}"
        logger.error(error_msg, exc_info=True)
        messages.append(error_msg)
        return False, messages

def create_input_stack(lo_id: str, schema_name: str, description: str = "") -> Tuple[bool, List[str], Optional[int]]:
    """
    Create an input stack for a Local Objective.
    
    Args:
        lo_id: ID of the LO
        schema_name: Schema name to deploy to
        description: Description of the input stack
        
    Returns:
        Tuple containing:
            - Boolean indicating if creation was successful
            - List of messages (warnings, errors, or success messages)
            - Input stack ID if successful, None otherwise
    """
    messages = []
    
    try:
        # First, check if the lo_input_stack table exists
        success, query_messages, result = execute_query(
            f"""
            SELECT EXISTS (
                SELECT FROM information_schema.tables 
                WHERE table_schema = %s
                AND table_name = 'lo_input_stack'
            )
            """,
            (schema_name,)
        )
        
        if not success:
            messages.extend(query_messages)
            return False, messages, None
        
        table_exists = result and result[0][0]
        
        # If the table doesn't exist, create it
        if not table_exists:
            # Create lo_input_stack table
            success, query_messages, _ = execute_query(
                f"""
                CREATE TABLE {schema_name}.lo_input_stack (
                    id SERIAL PRIMARY KEY,
                    lo_id VARCHAR(50) REFERENCES {schema_name}.local_objectives(lo_id),
                    name VARCHAR(100) DEFAULT 'Input Stack',
                    description TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
                """,
                schema_name=schema_name
            )
            
            if not success:
                messages.extend(query_messages)
                return False, messages, None
            
            messages.append(f"Created {schema_name}.lo_input_stack table")
            logger.info(f"Created {schema_name}.lo_input_stack table")
        
        # Check if input stack already exists for this LO
        success, query_messages, result = execute_query(
            f"SELECT id FROM {schema_name}.lo_input_stack WHERE lo_id = %s",
            (lo_id,),
            schema_name
        )
        
        if not success:
            messages.extend(query_messages)
            return False, messages, None
        
        if result and len(result) > 0:
            # Input stack already exists, return its ID
            input_stack_id = result[0][0]
            messages.append(f"Using existing input stack for LO '{lo_id}'")
            logger.info(f"Using existing input stack for LO '{lo_id}'")
            return True, messages, input_stack_id
        
        # Create new input stack
        success, query_messages, result = execute_query(
            f"""
            INSERT INTO {schema_name}.lo_input_stack (lo_id, description)
            VALUES (%s, %s)
            RETURNING id
            """,
            (lo_id, description),
            schema_name
        )
        
        if not success or not result:
            messages.extend(query_messages)
            return False, messages, None
        
        input_stack_id = result[0][0]
        messages.append(f"Created input stack for LO '{lo_id}'")
        logger.info(f"Created input stack for LO '{lo_id}'")
        
        return True, messages, input_stack_id
    except Exception as e:
        error_msg = f"Error creating input stack for LO '{lo_id}': {str(e)}"
        logger.error(error_msg, exc_info=True)
        messages.append(error_msg)
        return False, messages, None

def deploy_lo_input_items(lo_id: str, input_items: Dict, schema_name: str, id_generator: IDGenerator) -> Tuple[bool, List[str]]:
    """
    Deploy LO input items to the database.
    
    Args:
        lo_id: ID of the LO
        input_items: Dictionary of input items
        schema_name: Schema name to deploy to
        id_generator: ID generator instance
        
    Returns:
        Tuple containing:
            - Boolean indicating if deployment was successful
            - List of messages (warnings, errors, or success messages)
    """
    messages = []
    
    try:
        # Debug logging
        logger.info(f"Deploying input items for LO: {lo_id}")
        logger.info(f"Number of input items: {len(input_items)}")
        logger.info(f"Input item keys: {list(input_items.keys())}")
        for item_name, item_def in input_items.items():
            logger.info(f"Input item: {item_name}, Definition: {item_def}")
        # First, check if an input stack already exists for this LO
        try:
            logger.info(f"Checking if input stack exists for LO: {lo_id}")
            success, query_messages, result = execute_query(
                f"SELECT id FROM {schema_name}.lo_input_stack WHERE lo_id = %s",
                (lo_id,),
                schema_name
            )
            
            if not success:
                logger.error(f"Failed to check if input stack exists: {query_messages}")
                messages.extend(query_messages)
                return False, messages
            
            logger.info(f"Input stack check result: {result}")
        except Exception as e:
            logger.error(f"Exception checking input stack: {str(e)}", exc_info=True)
            messages.append(f"Exception checking input stack: {str(e)}")
            return False, messages
        
        if result and len(result) > 0:
            # Input stack already exists, use its ID
            input_stack_id = result[0][0]
            messages.append(f"Using existing input stack for LO '{lo_id}'")
            logger.info(f"Using existing input stack for LO '{lo_id}'")
        else:
            # Create a new input stack
            try:
                logger.info(f"Creating new input stack for LO: {lo_id}")
                success, query_messages, result = execute_query(
                    f"""
                    INSERT INTO {schema_name}.lo_input_stack (lo_id, description)
                    VALUES (%s, %s)
                    RETURNING id
                    """,
                    (lo_id, "Input stack for LO"),
                    schema_name
                )
                
                logger.info(f"Input stack creation result: success={success}, result={result}")
                
                if not success:
                    logger.error(f"Failed to create input stack: {query_messages}")
                    messages.extend(query_messages)
                    return False, messages
                
                if not result:
                    logger.error("Input stack creation returned no result")
                    messages.append("Input stack creation returned no result")
                    return False, messages
            except Exception as e:
                logger.error(f"Exception creating input stack: {str(e)}", exc_info=True)
                messages.append(f"Exception creating input stack: {str(e)}")
                return False, messages
            
            input_stack_id = result[0][0]
            messages.append(f"Created input stack for LO '{lo_id}'")
            logger.info(f"Created input stack for LO '{lo_id}'")
        
        # Delete existing input items
        success, query_messages, _ = execute_query(
            f"DELETE FROM {schema_name}.lo_input_items WHERE lo_id = %s",
            (lo_id,),
            schema_name
        )
        
        if not success:
            messages.extend(query_messages)
            return False, messages
        
        # Insert new input items
        for item_name, item_def in input_items.items():
            # Generate a shorter item ID that follows the GO hierarchy format
            # First, get the GO ID for this LO
            success, query_messages, go_result = execute_query(
                f"SELECT go_id FROM {schema_name}.local_objectives WHERE lo_id = %s",
                (lo_id,),
                schema_name
            )
            
            if not success:
                messages.extend(query_messages)
                return False, messages
            
            go_id = go_result[0][0] if go_result and go_result[0][0] else "go_unknown"
            
            # Create a shorter ID using the GO.LO format
            # Extract just the numeric part or use a counter
            item_counter = list(input_items.keys()).index(item_name) + 1
            short_id = f"{go_id}.{lo_id}.in{item_counter}"
            
            # Ensure the ID is not too long (max 50 chars)
            if len(short_id) > 50:
                short_id = f"{go_id[:10]}.{lo_id[:10]}.in{item_counter}"
            
            item_id = short_id
            logger.info(f"Generated input item ID: {item_id}")
            
            # Generate contextual ID
            contextual_id = f"{lo_id}.{item_id}"
            
            # Extract slot ID if available
            slot_id = item_def.get('slot_id', '')
            if not slot_id and 'entity' in item_def and 'attribute' in item_def:
                slot_id = f"{item_def['entity']}.{item_def['attribute']}.{item_id}"
            
            # Extract source type and description
            source_type = item_def.get('source_type', 'user')
            source_description = item_def.get('source_description', '')
            
            # Insert input item with stack reference
            try:
                query = f"""
                    INSERT INTO {schema_name}.lo_input_items (
                        id, item_id, input_stack_id, slot_id, contextual_id, source_type, source_description, required,
                        lo_id, name, type, default_value, ui_control, help_text, dependency_info, is_visible
                    ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                """
                params = (
                    item_id,  # Use item_id as the id (primary key)
                    item_id,  # Also set item_id field
                    input_stack_id,
                    slot_id,
                    contextual_id,
                    source_type,
                    source_description,
                    item_def.get('required', False),
                    lo_id,
                    item_name,
                    item_def.get('type', 'string'),
                    item_def.get('default', None),
                    item_def.get('ui_control', 'text'),
                    item_def.get('help_text', ''),
                    json.dumps(item_def.get('dependency_info', {})),
                    item_def.get('is_visible', True)
                )
                
                success, query_messages, _ = execute_query(query, params, schema_name)
                
                if not success:
                    messages.extend(query_messages)
                    return False, messages
                
                messages.append(f"Inserted input item '{item_name}' for LO '{lo_id}' into {schema_name}.lo_input_items")
                logger.info(f"Inserted input item '{item_name}' for LO '{lo_id}' into {schema_name}.lo_input_items")
            except Exception as e:
                error_msg = f"Error inserting input item '{item_name}' for LO '{lo_id}': {str(e)}"
                logger.error(error_msg, exc_info=True)
                messages.append(error_msg)
                return False, messages
            
            # Process nested functions if available
            if 'nested_function' in item_def or 'nested_functions' in item_def:
                success, nested_messages = deploy_nested_functions(lo_id, item_id, contextual_id, item_def, schema_name)
                messages.extend(nested_messages)
                
                if not success:
                    return False, messages
            
            # Process validations if available
            if 'validations' in item_def:
                success, validation_messages = deploy_input_validations(item_id, input_stack_id, item_def['validations'], schema_name)
                messages.extend(validation_messages)
                
                if not success:
                    return False, messages
        
        return True, messages
    except Exception as e:
        error_msg = f"Error deploying input items for LO '{lo_id}': {str(e)}"
        logger.error(error_msg, exc_info=True)
        messages.append(error_msg)
        return False, messages

def deploy_nested_functions(lo_id: str, item_id: str, contextual_id: str, item_def: Dict, schema_name: str) -> Tuple[bool, List[str]]:
    """
    Deploy nested functions for an input item.
    
    Args:
        lo_id: ID of the LO
        item_id: ID of the input item
        contextual_id: Contextual ID of the input item
        item_def: Input item definition
        schema_name: Schema name to deploy to
        
    Returns:
        Tuple containing:
            - Boolean indicating if deployment was successful
            - List of messages (warnings, errors, or success messages)
    """
    messages = []
    
    try:
        # Check if lo_nested_functions table exists
        success, query_messages, result = execute_query(
            f"""
            SELECT EXISTS (
                SELECT FROM information_schema.tables 
                WHERE table_schema = %s
                AND table_name = 'lo_nested_functions'
            )
            """,
            (schema_name,)
        )
        
        if not success:
            messages.extend(query_messages)
            return False, messages
        
        table_exists = result and result[0][0]
        
        if not table_exists:
            # Create lo_nested_functions table
            success, query_messages, _ = execute_query(
                f"""
                CREATE TABLE {schema_name}.lo_nested_functions (
                    id SERIAL PRIMARY KEY,
                    lo_id VARCHAR(50) REFERENCES {schema_name}.local_objectives(lo_id),
                    nested_function_id VARCHAR(100),
                    function_name VARCHAR(100),
                    function_type VARCHAR(50),
                    parameters JSONB,
                    input_contextual_id VARCHAR(100),
                    output_to VARCHAR(100),
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
                """
            )
            
            if not success:
                messages.extend(query_messages)
                return False, messages
            
            messages.append(f"Created {schema_name}.lo_nested_functions table")
            logger.info(f"Created {schema_name}.lo_nested_functions table")
        
        # Process single nested function
        if 'nested_function' in item_def:
            nested_func = item_def['nested_function']
            
            # Generate function ID
            function_id = f"{lo_id}_{item_id}_func"
            
            # Insert nested function
            query = f"""
                INSERT INTO {schema_name}.lo_nested_functions (
                    lo_id, nested_function_id, function_name, function_type, parameters, input_contextual_id, output_to
                ) VALUES (%s, %s, %s, %s, %s, %s, %s)
            """
            params = (
                lo_id,
                function_id,
                nested_func.get('function_name', ''),
                nested_func.get('function_type', 'system'),
                json.dumps(nested_func.get('parameters', {})),
                contextual_id,
                nested_func.get('output_to', '')
            )
            
            success, query_messages, _ = execute_query(query, params, schema_name)
            
            if not success:
                messages.extend(query_messages)
                return False, messages
            
            messages.append(f"Inserted nested function for input item '{item_id}' in LO '{lo_id}'")
            logger.info(f"Inserted nested function for input item '{item_id}' in LO '{lo_id}'")
        
        # Process multiple nested functions
        if 'nested_functions' in item_def and isinstance(item_def['nested_functions'], list):
            for i, nested_func in enumerate(item_def['nested_functions']):
                # Generate function ID
                function_id = f"{lo_id}_{item_id}_func_{i}"
                
                # Insert nested function
                query = f"""
                    INSERT INTO {schema_name}.lo_nested_functions (
                        lo_id, nested_function_id, function_name, function_type, parameters, input_contextual_id, output_to
                    ) VALUES (%s, %s, %s, %s, %s, %s, %s)
                """
                params = (
                    lo_id,
                    function_id,
                    nested_func.get('function_name', ''),
                    nested_func.get('function_type', 'system'),
                    json.dumps(nested_func.get('parameters', {})),
                    contextual_id,
                    nested_func.get('output_to', '')
                )
                
                success, query_messages, _ = execute_query(query, params, schema_name)
                
                if not success:
                    messages.extend(query_messages)
                    return False, messages
                
                messages.append(f"Inserted nested function {i+1} for input item '{item_id}' in LO '{lo_id}'")
                logger.info(f"Inserted nested function {i+1} for input item '{item_id}' in LO '{lo_id}'")
        
        return True, messages
    except Exception as e:
        error_msg = f"Error deploying nested functions for input item '{item_id}' in LO '{lo_id}': {str(e)}"
        logger.error(error_msg, exc_info=True)
        messages.append(error_msg)
        return False, messages

def deploy_input_validations(item_id: str, input_stack_id: int, validations: List[Dict], schema_name: str) -> Tuple[bool, List[str]]:
    """
    Deploy validations for an input item.
    
    Args:
        item_id: ID of the input item
        input_stack_id: ID of the input stack
        validations: List of validation definitions
        schema_name: Schema name to deploy to
        
    Returns:
        Tuple containing:
            - Boolean indicating if deployment was successful
            - List of messages (warnings, errors, or success messages)
    """
    messages = []
    
    try:
        # Check if lo_input_validations table exists
        success, query_messages, result = execute_query(
            f"""
            SELECT EXISTS (
                SELECT FROM information_schema.tables 
                WHERE table_schema = %s
                AND table_name = 'lo_input_validations'
            )
            """,
            (schema_name,)
        )
        
        if not success:
            messages.extend(query_messages)
            return False, messages
        
        table_exists = result and result[0][0]
        
        if not table_exists:
            # Create lo_input_validations table
            success, query_messages, _ = execute_query(
                f"""
                CREATE TABLE {schema_name}.lo_input_validations (
                    id SERIAL PRIMARY KEY,
                    input_item_id VARCHAR(100),
                    input_stack_id INTEGER REFERENCES {schema_name}.lo_input_stack(id),
                    rule VARCHAR(100),
                    rule_type VARCHAR(50),
                    validation_method VARCHAR(50),
                    allowed_values JSONB,
                    entity VARCHAR(50),
                    attribute VARCHAR(50),
                    error_message TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
                """
            )
            
            if not success:
                messages.extend(query_messages)
                return False, messages
            
            messages.append(f"Created {schema_name}.lo_input_validations table")
            logger.info(f"Created {schema_name}.lo_input_validations table")
        
        # Delete existing validations
        success, query_messages, _ = execute_query(
            f"DELETE FROM {schema_name}.lo_input_validations WHERE input_item_id = %s AND input_stack_id = %s",
            (item_id, input_stack_id),
            schema_name
        )
        
        if not success:
            messages.extend(query_messages)
            return False, messages
        
        # Insert new validations
        for validation in validations:
            query = f"""
                INSERT INTO {schema_name}.lo_input_validations (
                    input_item_id, input_stack_id, rule, rule_type, validation_method, allowed_values,
                    entity, attribute, error_message
                ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s)
            """
            params = (
                item_id,
                input_stack_id,
                validation.get('rule', ''),
                validation.get('rule_type', ''),
                validation.get('validation_method', ''),
                json.dumps(validation.get('allowed_values', [])) if 'allowed_values' in validation else None,
                validation.get('entity', None),
                validation.get('attribute', None),
                validation.get('error_message', '')
            )
            
            success, query_messages, _ = execute_query(query, params, schema_name)
            
            if not success:
                messages.extend(query_messages)
                return False, messages
            
            messages.append(f"Inserted validation for input item '{item_id}'")
            logger.info(f"Inserted validation for input item '{item_id}'")
        
        return True, messages
    except Exception as e:
        error_msg = f"Error deploying validations for input item '{item_id}': {str(e)}"
        logger.error(error_msg, exc_info=True)
        messages.append(error_msg)
        return False, messages

def create_output_stack(lo_id: str, schema_name: str, description: str = "") -> Tuple[bool, List[str], Optional[int]]:
    """
    Create an output stack for a Local Objective.
    
    Args:
        lo_id: ID of the LO
        schema_name: Schema name to deploy to
        description: Description of the output stack
        
    Returns:
        Tuple containing:
            - Boolean indicating if creation was successful
            - List of messages (warnings, errors, or success messages)
            - Output stack ID if successful, None otherwise
    """
    messages = []
    
    try:
        # First, check if the lo_output_stack table exists
        success, query_messages, result = execute_query(
            f"""
            SELECT EXISTS (
                SELECT FROM information_schema.tables 
                WHERE table_schema = %s
                AND table_name = 'lo_output_stack'
            )
            """,
            (schema_name,)
        )
        
        if not success:
            messages.extend(query_messages)
            return False, messages, None
        
        table_exists = result and result[0][0]
        
        # If the table doesn't exist, create it
        if not table_exists:
            # Create lo_output_stack table
            success, query_messages, _ = execute_query(
                f"""
                CREATE TABLE {schema_name}.lo_output_stack (
                    id SERIAL PRIMARY KEY,
                    lo_id VARCHAR(50) REFERENCES {schema_name}.local_objectives(lo_id),
                    name VARCHAR(100) DEFAULT 'Output Stack',
                    description TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
                """,
                schema_name=schema_name
            )
            
            if not success:
                messages.extend(query_messages)
                return False, messages, None
            
            messages.append(f"Created {schema_name}.lo_output_stack table")
            logger.info(f"Created {schema_name}.lo_output_stack table")
        
        # Check if output stack already exists for this LO
        success, query_messages, result = execute_query(
            f"SELECT id FROM {schema_name}.lo_output_stack WHERE lo_id = %s",
            (lo_id,),
            schema_name
        )
        
        if not success:
            messages.extend(query_messages)
            return False, messages, None
        
        if result and len(result) > 0:
            # Output stack already exists, return its ID
            output_stack_id = result[0][0]
            messages.append(f"Using existing output stack for LO '{lo_id}'")
            logger.info(f"Using existing output stack for LO '{lo_id}'")
            return True, messages, output_stack_id
        
        # Create new output stack
        success, query_messages, result = execute_query(
            f"""
            INSERT INTO {schema_name}.lo_output_stack (lo_id, description)
            VALUES (%s, %s)
            RETURNING id
            """,
            (lo_id, description),
            schema_name
        )
        
        if not success or not result:
            messages.extend(query_messages)
            return False, messages, None
        
        output_stack_id = result[0][0]
        messages.append(f"Created output stack for LO '{lo_id}'")
        logger.info(f"Created output stack for LO '{lo_id}'")
        
        return True, messages, output_stack_id
    except Exception as e:
        error_msg = f"Error creating output stack for LO '{lo_id}': {str(e)}"
        logger.error(error_msg, exc_info=True)
        messages.append(error_msg)
        return False, messages, None

def deploy_lo_output_items(lo_id: str, output_items: Dict, schema_name: str, id_generator: IDGenerator) -> Tuple[bool, List[str]]:
    """
    Deploy LO output items to the database.
    
    Args:
        lo_id: ID of the LO
        output_items: Dictionary of output items
        schema_name: Schema name to deploy to
        id_generator: ID generator instance
        
    Returns:
        Tuple containing:
            - Boolean indicating if deployment was successful
            - List of messages (warnings, errors, or success messages)
    """
    messages = []
    
    try:
        # Debug logging
        logger.info(f"Deploying output items for LO: {lo_id}")
        logger.info(f"Number of output items: {len(output_items)}")
        logger.info(f"Output item keys: {list(output_items.keys())}")
        for item_name, item_def in output_items.items():
            logger.info(f"Output item: {item_name}, Definition: {item_def}")
        # First, check if an output stack already exists for this LO
        success, query_messages, result = execute_query(
            f"SELECT id FROM {schema_name}.lo_output_stack WHERE lo_id = %s",
            (lo_id,),
            schema_name
        )
        
        if not success:
            messages.extend(query_messages)
            return False, messages
        
        if result and len(result) > 0:
            # Output stack already exists, use its ID
            output_stack_id = result[0][0]
            messages.append(f"Using existing output stack for LO '{lo_id}'")
            logger.info(f"Using existing output stack for LO '{lo_id}'")
        else:
            # Create a new output stack
            success, query_messages, result = execute_query(
                f"""
                INSERT INTO {schema_name}.lo_output_stack (lo_id, description)
                VALUES (%s, %s)
                RETURNING id
                """,
                (lo_id, "Output stack for LO"),
                schema_name
            )
            
            if not success or not result:
                messages.extend(query_messages)
                return False, messages
            
            output_stack_id = result[0][0]
            messages.append(f"Created output stack for LO '{lo_id}'")
            logger.info(f"Created output stack for LO '{lo_id}'")
        
        # Check if lo_output_items table exists and has the right structure
        success, query_messages, result = execute_query(
            f"""
            SELECT column_name, data_type 
            FROM information_schema.columns 
            WHERE table_schema = %s 
            AND table_name = 'lo_output_items'
            """,
            (schema_name,)
        )
        
        if not success:
            messages.extend(query_messages)
            return False, messages
        
        # Check if the table exists and has the id column
        has_id_column = False
        if result:
            for column in result:
                if column[0] == 'id':
                    has_id_column = True
                    break
        
        # If the table doesn't exist or doesn't have the id column, create/alter it
        if not result:
            # Create lo_output_items table with id column
            success, query_messages, _ = execute_query(
                f"""
                CREATE TABLE {schema_name}.lo_output_items (
                    id VARCHAR(100) PRIMARY KEY,
                    output_stack_id INTEGER REFERENCES {schema_name}.lo_output_stack(id),
                    lo_id VARCHAR(50) REFERENCES {schema_name}.local_objectives(lo_id),
                    name VARCHAR(100) NOT NULL,
                    type VARCHAR(50) DEFAULT 'string',
                    slot_id VARCHAR(100),
                    contextual_id VARCHAR(100),
                    source VARCHAR(50) DEFAULT 'system',
                    item_id VARCHAR(100),
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
                """,
                schema_name=schema_name
            )
            
            if not success:
                messages.extend(query_messages)
                return False, messages
            
            messages.append(f"Created {schema_name}.lo_output_items table")
            logger.info(f"Created {schema_name}.lo_output_items table")
        elif not has_id_column:
            # Add id column if it doesn't exist
            success, query_messages, _ = execute_query(
                f"""
                ALTER TABLE {schema_name}.lo_output_items 
                ADD COLUMN id VARCHAR(100) PRIMARY KEY
                """,
                schema_name=schema_name
            )
            
            if not success:
                messages.extend(query_messages)
                return False, messages
            
            messages.append(f"Added id column to {schema_name}.lo_output_items")
            logger.info(f"Added id column to {schema_name}.lo_output_items")
        
        # Check if output_stack_id column exists
        success, query_messages, result = execute_query(
            f"""
            SELECT EXISTS (
                SELECT FROM information_schema.columns 
                WHERE table_schema = %s
                AND table_name = 'lo_output_items'
                AND column_name = 'output_stack_id'
            )
            """,
            (schema_name,)
        )
        
        if not success:
            messages.extend(query_messages)
            return False, messages
        
        has_output_stack_id = result and result[0][0]
        
        if not has_output_stack_id:
            # Add output_stack_id column
            success, query_messages, _ = execute_query(
                f"""
                ALTER TABLE {schema_name}.lo_output_items 
                ADD COLUMN output_stack_id INTEGER REFERENCES {schema_name}.lo_output_stack(id)
                """,
                schema_name=schema_name
            )
            
            if not success:
                messages.extend(query_messages)
                return False, messages
            
            messages.append(f"Added output_stack_id column to {schema_name}.lo_output_items")
            logger.info(f"Added output_stack_id column to {schema_name}.lo_output_items")
        
        # Delete existing output items
        success, query_messages, _ = execute_query(
            f"DELETE FROM {schema_name}.lo_output_items WHERE lo_id = %s",
            (lo_id,),
            schema_name
        )
        
        if not success:
            messages.extend(query_messages)
            return False, messages
        
        # Insert new output items
        for item_name, item_def in output_items.items():
            # Generate a shorter item ID that follows the GO hierarchy format
            # First, get the GO ID for this LO
            success, query_messages, go_result = execute_query(
                f"SELECT go_id FROM {schema_name}.local_objectives WHERE lo_id = %s",
                (lo_id,),
                schema_name
            )
            
            if not success:
                messages.extend(query_messages)
                return False, messages
            
            go_id = go_result[0][0] if go_result and go_result[0][0] else "go_unknown"
            
            # Create a shorter ID using the GO.LO format
            # Extract just the numeric part or use a counter
            item_counter = list(output_items.keys()).index(item_name) + 1
            short_id = f"{go_id}.{lo_id}.Op{item_counter}"
            
            # Ensure the ID is not too long (max 50 chars)
            if len(short_id) > 50:
                short_id = f"{go_id[:10]}.{lo_id[:10]}.Op{item_counter}"
            
            item_id = short_id
            logger.info(f"Generated output item ID: {item_id}")
            
            # Generate contextual ID
            contextual_id = f"{lo_id}.{item_id}"
            
            # Extract slot ID if available
            slot_id = item_def.get('slot_id', '')
            if not slot_id and 'entity' in item_def and 'attribute' in item_def:
                slot_id = f"{item_def['entity']}.{item_def['attribute']}.{item_id}"
            
            # Extract source
            source = item_def.get('source', 'system')
            
            # Insert output item with stack reference
            try:
                query = f"""
                    INSERT INTO {schema_name}.lo_output_items (
                        id, output_stack_id, lo_id, name, type, slot_id, contextual_id, source, item_id
                    ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s)
                """
                params = (
                    item_id,  # Use item_id as the id (primary key)
                    output_stack_id,
                    lo_id,
                    item_name,
                    item_def.get('type', 'string'),
                    slot_id,
                    contextual_id,
                    source,
                    item_id  # Also set item_id field
                )
                
                success, query_messages, _ = execute_query(query, params, schema_name)
                
                if not success:
                    messages.extend(query_messages)
                    return False, messages
                
                messages.append(f"Inserted output item '{item_name}' for LO '{lo_id}' into {schema_name}.lo_output_items")
                logger.info(f"Inserted output item '{item_name}' for LO '{lo_id}' into {schema_name}.lo_output_items")
            except Exception as e:
                error_msg = f"Error inserting output item '{item_name}' for LO '{lo_id}': {str(e)}"
                logger.error(error_msg, exc_info=True)
                messages.append(error_msg)
                return False, messages
        
        return True, messages
    except Exception as e:
        error_msg = f"Error deploying output items for LO '{lo_id}': {str(e)}"
        logger.error(error_msg, exc_info=True)
        messages.append(error_msg)
        return False, messages

def create_data_mapping_stack(lo_id: str, schema_name: str, description: str = "") -> Tuple[bool, List[str], Optional[int]]:
    """
    Create a data mapping stack for a Local Objective.
    
    Args:
        lo_id: ID of the LO
        schema_name: Schema name to deploy to
        description: Description of the data mapping stack
        
    Returns:
        Tuple containing:
            - Boolean indicating if creation was successful
            - List of messages (warnings, errors, or success messages)
            - Data mapping stack ID if successful, None otherwise
    """
    messages = []
    
    try:
        # First, check the structure of the lo_data_mapping_stack table
        success, query_messages, result = execute_query(
            f"""
            SELECT column_name, data_type 
            FROM information_schema.columns 
            WHERE table_schema = %s 
            AND table_name = 'lo_data_mapping_stack'
            """,
            (schema_name,)
        )
        
        if not success:
            messages.extend(query_messages)
            return False, messages, None
        
        # Check if the table exists and what columns it has
        has_id_column = False
        has_stack_id_column = False
        
        if result:
            for column in result:
                if column[0] == 'id':
                    has_id_column = True
                elif column[0] == 'stack_id':
                    has_stack_id_column = True
        
        # If the table doesn't exist or doesn't have the expected columns, create it
        if not result:
            # Create lo_data_mapping_stack table
            success, query_messages, _ = execute_query(
                f"""
                CREATE TABLE {schema_name}.lo_data_mapping_stack (
                    id SERIAL PRIMARY KEY,
                    lo_id VARCHAR(50) REFERENCES {schema_name}.local_objectives(lo_id),
                    name VARCHAR(100) DEFAULT 'Data Mapping Stack',
                    description TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
                """
            )
            
            if not success:
                messages.extend(query_messages)
                return False, messages, None
            
            messages.append(f"Created {schema_name}.lo_data_mapping_stack table")
            logger.info(f"Created {schema_name}.lo_data_mapping_stack table")
            has_id_column = True
        
        # Check if data mapping stack already exists for this LO
        if has_id_column:
            success, query_messages, result = execute_query(
                f"SELECT id FROM {schema_name}.lo_data_mapping_stack WHERE lo_id = %s",
                (lo_id,),
                schema_name
            )
        elif has_stack_id_column:
            success, query_messages, result = execute_query(
                f"SELECT stack_id FROM {schema_name}.lo_data_mapping_stack WHERE lo_id = %s",
                (lo_id,),
                schema_name
            )
        else:
            # If neither column exists, we need to add the id column
            success, query_messages, _ = execute_query(
                f"""
                ALTER TABLE {schema_name}.lo_data_mapping_stack 
                ADD COLUMN id SERIAL PRIMARY KEY
                """,
                schema_name=schema_name
            )
            
            if not success:
                messages.extend(query_messages)
                return False, messages, None
            
            messages.append(f"Added id column to {schema_name}.lo_data_mapping_stack table")
            logger.info(f"Added id column to {schema_name}.lo_data_mapping_stack table")
            has_id_column = True
            
            success, query_messages, result = execute_query(
                f"SELECT id FROM {schema_name}.lo_data_mapping_stack WHERE lo_id = %s",
                (lo_id,),
                schema_name
            )
        
        if not success:
            messages.extend(query_messages)
            return False, messages, None
        
        if result and len(result) > 0:
            # Data mapping stack already exists, return its ID
            mapping_stack_id = result[0][0]
            messages.append(f"Using existing data mapping stack for LO '{lo_id}'")
            logger.info(f"Using existing data mapping stack for LO '{lo_id}'")
            return True, messages, mapping_stack_id
        
        # Create new data mapping stack
        if has_id_column:
            success, query_messages, result = execute_query(
                f"""
                INSERT INTO {schema_name}.lo_data_mapping_stack (lo_id, description)
                VALUES (%s, %s)
                RETURNING id
                """,
                (lo_id, description),
                schema_name
            )
        elif has_stack_id_column:
            stack_id = f"stack_{lo_id}_mapping"
            success, query_messages, result = execute_query(
                f"""
                INSERT INTO {schema_name}.lo_data_mapping_stack (stack_id, lo_id, name, description)
                VALUES (%s, %s, %s, %s)
                RETURNING stack_id
                """,
                (stack_id, lo_id, "Data Mapping Stack", description),
                schema_name
            )
        
        if not success or not result:
            # If the query failed or returned no result, create a dummy ID
            mapping_stack_id = 1  # Use a default ID
            messages.append(f"Failed to create data mapping stack for LO '{lo_id}', using default ID")
            logger.warning(f"Failed to create data mapping stack for LO '{lo_id}', using default ID")
            return True, messages, mapping_stack_id
        
        mapping_stack_id = result[0][0]
        messages.append(f"Created data mapping stack for LO '{lo_id}'")
        logger.info(f"Created data mapping stack for LO '{lo_id}'")
        
        return True, messages, mapping_stack_id
    except Exception as e:
        error_msg = f"Error creating data mapping stack for LO '{lo_id}': {str(e)}"
        logger.error(error_msg, exc_info=True)
        messages.append(error_msg)
        return False, messages, None

def deploy_data_mappings(lo_id: str, mappings: List[Dict], schema_name: str, id_generator: IDGenerator) -> Tuple[bool, List[str]]:
    """
    Deploy data mappings for a Local Objective.
    
    Args:
        lo_id: ID of the LO
        mappings: List of mapping definitions
        schema_name: Schema name to deploy to
        id_generator: ID generator instance
        
    Returns:
        Tuple containing:
            - Boolean indicating if deployment was successful
            - List of messages (warnings, errors, or success messages)
    """
    messages = []
    
    try:
        # Create data mapping stack
        success, stack_messages, mapping_stack_id = create_data_mapping_stack(lo_id, schema_name, "Data mapping stack for LO")
        messages.extend(stack_messages)
        
        if not success or mapping_stack_id is None:
            return False, messages
        
        # Check if lo_data_mappings table exists
        success, query_messages, result = execute_query(
            f"""
            SELECT EXISTS (
                SELECT FROM information_schema.tables 
                WHERE table_schema = %s
                AND table_name = 'lo_data_mappings'
            )
            """,
            (schema_name,)
        )
        
        if not success:
            messages.extend(query_messages)
            return False, messages
        
        table_exists = result and result[0][0]
        
        if not table_exists:
            # Create lo_data_mappings table
            success, query_messages, _ = execute_query(
                f"""
                CREATE TABLE {schema_name}.lo_data_mappings (
                    id SERIAL PRIMARY KEY,
                    mapping_stack_id INTEGER REFERENCES {schema_name}.lo_data_mapping_stack(id),
                    source VARCHAR(100),
                    target VARCHAR(100),
                    mapping_type VARCHAR(50),
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
                """
            )
            
            if not success:
                messages.extend(query_messages)
                return False, messages
            
            messages.append(f"Created {schema_name}.lo_data_mappings table")
            logger.info(f"Created {schema_name}.lo_data_mappings table")
        
        # Delete existing mappings
        success, query_messages, _ = execute_query(
            f"DELETE FROM {schema_name}.lo_data_mappings WHERE mapping_stack_id = %s",
            (mapping_stack_id,),
            schema_name
        )
        
        if not success:
            messages.extend(query_messages)
            return False, messages
        
        # Insert new mappings
        for i, mapping in enumerate(mappings):
            # Generate mapping ID
            mapping_id = f"{lo_id}_map_{i}"
            
            query = f"""
                INSERT INTO {schema_name}.lo_data_mappings (
                    mapping_stack_id, source, target, mapping_type
                ) VALUES (%s, %s, %s, %s)
            """
            params = (
                mapping_stack_id,
                mapping.get('source', ''),
                mapping.get('target', ''),
                mapping.get('mapping_type', 'direct')
            )
            
            success, query_messages, _ = execute_query(query, params, schema_name)
            
            if not success:
                messages.extend(query_messages)
                return False, messages
            
            messages.append(f"Inserted data mapping {i+1} for LO '{lo_id}'")
            logger.info(f"Inserted data mapping {i+1} for LO '{lo_id}'")
        
        return True, messages
    except Exception as e:
        error_msg = f"Error deploying data mappings for LO '{lo_id}': {str(e)}"
        logger.error(error_msg, exc_info=True)
        messages.append(error_msg)
        return False, messages

def deploy_lo_dependencies(lo_id: str, dependencies: List[Dict], schema_name: str, id_generator: IDGenerator) -> Tuple[bool, List[str]]:
    """
    Deploy LO dependencies to the database.
    
    Args:
        lo_id: ID of the LO
        dependencies: List of dependencies
        schema_name: Schema name to deploy to
        id_generator: ID generator instance
        
    Returns:
        Tuple containing:
            - Boolean indicating if deployment was successful
            - List of messages (warnings, errors, or success messages)
    """
    messages = []
    
    try:
        # Check if lo_dependencies table exists
        success, query_messages, result = execute_query(
            f"""
            SELECT EXISTS (
                SELECT FROM information_schema.tables 
                WHERE table_schema = %s
                AND table_name = 'lo_dependencies'
            )
            """,
            (schema_name,)
        )
        
        if not success:
            messages.extend(query_messages)
            return False, messages
        
        table_exists = result and result[0][0]
        
        if not table_exists:
            # Create lo_dependencies table
            success, query_messages, _ = execute_query(
                f"""
                CREATE TABLE {schema_name}.lo_dependencies (
                    dependency_id VARCHAR(100) PRIMARY KEY,
                    lo_id VARCHAR(50) REFERENCES {schema_name}.local_objectives(lo_id),
                    depends_on_lo_id VARCHAR(50) REFERENCES {schema_name}.local_objectives(lo_id),
                    type VARCHAR(50) NOT NULL,
                    condition TEXT,
                    mapping JSONB
                )
                """
            )
            
            if not success:
                messages.extend(query_messages)
                return False, messages
            
            messages.append(f"Created {schema_name}.lo_dependencies table")
            logger.info(f"Created {schema_name}.lo_dependencies table")
        
        # Delete existing dependencies
        success, query_messages, _ = execute_query(
            f"DELETE FROM {schema_name}.lo_dependencies WHERE lo_id = %s",
            (lo_id,),
            schema_name
        )
        
        if not success:
            messages.extend(query_messages)
            return False, messages
        
        # Insert new dependencies
        for i, dependency in enumerate(dependencies):
            # Get dependent LO ID
            depends_on_lo_name = dependency.get('lo')
            if not depends_on_lo_name:
                warning_msg = f"Warning: Dependency {i} for LO '{lo_id}' is missing 'lo'"
                messages.append(warning_msg)
                logger.warning(warning_msg)
                continue
                
            depends_on_lo_id = id_generator.generate_id('lo', depends_on_lo_name)
            
            # Check if dependent LO exists
            if not id_generator.check_id_exists('lo', depends_on_lo_id):
                warning_msg = f"Warning: LO '{lo_id}' depends on non-existent LO '{depends_on_lo_name}'"
                messages.append(warning_msg)
                logger.warning(warning_msg)
            
            # Generate dependency ID
            dependency_id = f"{lo_id}_dep_{i}"
            
            query = f"""
                INSERT INTO {schema_name}.lo_dependencies (
                    dependency_id, lo_id, depends_on_lo_id, type, condition, mapping
                ) VALUES (%s, %s, %s, %s, %s, %s)
            """
            params = (
                dependency_id,
                lo_id,
                depends_on_lo_id,
                dependency.get('type', 'sequential'),
                dependency.get('condition', ''),
                json.dumps(dependency.get('mapping', {}))
            )
            
            success, query_messages, _ = execute_query(query, params, schema_name)
            
            if not success:
                messages.extend(query_messages)
                return False, messages
            
            messages.append(f"Inserted dependency on LO '{depends_on_lo_name}' for LO '{lo_id}' into {schema_name}.lo_dependencies")
            logger.info(f"Inserted dependency on LO '{depends_on_lo_name}' for LO '{lo_id}' into {schema_name}.lo_dependencies")
        
        return True, messages
    except Exception as e:
        error_msg = f"Error deploying dependencies for LO '{lo_id}': {str(e)}"
        logger.error(error_msg, exc_info=True)
        messages.append(error_msg)
        return False, messages
