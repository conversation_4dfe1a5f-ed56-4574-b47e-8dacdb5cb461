# Company Email Service Implementation Guide

## Overview
This guide provides step-by-step instructions for implementing a comprehensive email service system for your company using Python, FastAPI, and the unified adapter pattern that matches your specified request format.

## Request Format Structure
Your email service will accept requests in this unified format:
```json
{
  "adapter_config": {
    "service": "service_name",
    "operation": "operation_name",
    "version": "v1"
  },
  "parameters": {
    // Service-specific parameters
  },
  "files": {
    // File attachments (for multipart requests)
  }
}
```

## Architecture Overview

```
┌─────────────────────────────────────────────────────────────────┐
│                    Email Service Architecture                    │
├─────────────────────────────────────────────────────────────────┤
│  API Gateway (FastAPI)                                          │
│  ├── Unified Request Handler                                    │
│  ├── Authentication & Authorization                             │
│  ├── Rate Limiting                                              │
│  └── Request Validation                                         │
├─────────────────────────────────────────────────────────────────┤
│  Core Email Service                                             │
│  ├── Email Processing Logic                                     │
│  ├── Template Engine (Jinja2)                                  │
│  ├── Bulk Processing & Queue Management                        │
│  └── File & Attachment Management                              │
├─────────────────────────────────────────────────────────────────┤
│  Email Providers (Pluggable)                                   │
│  ├── Outlook/Office365 Provider                                │
│  ├── SMTP Provider (Gmail, Custom)                             │
│  ├── AWS SES Provider                                           │
│  └── SendGrid Provider                                          │
├─────────────────────────────────────────────────────────────────┤
│  Storage & Database                                             │
│  ├── PostgreSQL (Jobs, Templates, Logs)                        │
│  ├── Redis (Caching, Queue)                                    │
│  └── File Storage (Attachments, Templates)                     │
└─────────────────────────────────────────────────────────────────┘
```

## Step 1: Environment Setup and Dependencies

### 1.1 Create Project Structure
```bash
mkdir company-email-service
cd company-email-service

# Create directory structure
mkdir -p {app/{api,config,database,models,services/providers,utils},storage/{attachments,templates,bulk_emails},logs,database,tests}
```

### 1.2 Install Dependencies
Create `requirements.txt`:
```txt
fastapi==0.104.1
uvicorn[standard]==0.24.0
pydantic==2.4.2
sqlalchemy==2.0.23
psycopg2-binary==2.9.7
redis==5.0.1
jinja2==3.1.2
loguru==0.7.2
python-multipart==0.0.6
aiofiles==23.2.1
python-dotenv==1.0.0
exchangelib==5.0.3
aiosmtplib==3.0.1
cryptography==41.0.7
pytest==7.4.3
pytest-asyncio==0.21.1
httpx==0.25.2
celery==5.3.4
kombu==5.3.4
slowapi==0.1.9
python-jose[cryptography]==3.3.0
passlib[bcrypt]==1.7.4
```

### 1.3 Environment Configuration
Create `.env`:
```env
# Service Configuration
SERVICE_NAME=email-service
SERVICE_VERSION=v1
SERVICE_HOST=0.0.0.0
SERVICE_PORT=8000
LOG_LEVEL=INFO

# Database Configuration
DATABASE_URL=postgresql://user:password@localhost:5432/email_service
REDIS_URL=redis://localhost:6379/0

# Security
SECRET_KEY=your-secret-key-here
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

# Email Provider Configurations
# Outlook/Office365
OUTLOOK_CLIENT_ID=your_outlook_client_id
OUTLOOK_CLIENT_SECRET=your_outlook_client_secret
OUTLOOK_TENANT_ID=your_tenant_id

# SMTP Configuration
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USE_TLS=true
SMTP_USERNAME=your_smtp_username
SMTP_PASSWORD=your_smtp_password

# AWS SES
AWS_ACCESS_KEY_ID=your_aws_access_key
AWS_SECRET_ACCESS_KEY=your_aws_secret_key
AWS_REGION=us-east-1

# Storage Configuration
STORAGE_PATH=./storage
MAX_ATTACHMENT_SIZE_MB=25
MAX_BULK_RECIPIENTS=10000

# Rate Limiting
RATE_LIMIT_PER_MINUTE=100
BULK_EMAIL_RATE_LIMIT=10
```

## Step 2: Database Schema Setup

### 2.1 Database Models (`app/database/models.py`)
```python
from sqlalchemy import Column, Integer, String, Text, Boolean, DateTime, JSON, ForeignKey
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import relationship
from datetime import datetime

Base = declarative_base()

class EmailJob(Base):
    __tablename__ = "email_jobs"
    
    id = Column(Integer, primary_key=True, index=True)
    job_id = Column(String(255), unique=True, index=True, nullable=False)
    campaign_id = Column(String(255), index=True)
    service_operation = Column(String(100), nullable=False)
    status = Column(String(50), nullable=False, default="pending")
    provider = Column(String(50), nullable=False)
    total_recipients = Column(Integer, nullable=False, default=0)
    sent_count = Column(Integer, default=0)
    failed_count = Column(Integer, default=0)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    scheduled_at = Column(DateTime)
    completed_at = Column(DateTime)
    metadata = Column(JSON)
    
    # Relationship to email logs
    email_logs = relationship("EmailLog", back_populates="job")

class EmailTemplate(Base):
    __tablename__ = "email_templates"
    
    id = Column(Integer, primary_key=True, index=True)
    template_id = Column(String(255), unique=True, index=True, nullable=False)
    template_name = Column(String(255), nullable=False)
    subject = Column(String(500), nullable=False)
    body = Column(Text, nullable=False)
    body_type = Column(String(20), default="html")
    variables = Column(JSON, default=[])
    category = Column(String(100))
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

class EmailLog(Base):
    __tablename__ = "email_logs"
    
    id = Column(Integer, primary_key=True, index=True)
    job_id = Column(String(255), ForeignKey("email_jobs.job_id"), index=True)
    recipient_email = Column(String(255), nullable=False, index=True)
    status = Column(String(50), nullable=False)
    provider = Column(String(50), nullable=False)
    message_id = Column(String(255))
    error_message = Column(Text)
    sent_at = Column(DateTime, default=datetime.utcnow)
    metadata = Column(JSON)
    
    # Relationship to job
    job = relationship("EmailJob", back_populates="email_logs")

class EmailConfiguration(Base):
    __tablename__ = "email_configurations"
    
    id = Column(Integer, primary_key=True, index=True)
    provider = Column(String(50), nullable=False)
    config_name = Column(String(100), nullable=False)
    config_data = Column(JSON, nullable=False)
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
```

### 2.2 Database Connection (`app/database/connection.py`)
```python
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession
import os
from loguru import logger

DATABASE_URL = os.getenv("DATABASE_URL")
ASYNC_DATABASE_URL = DATABASE_URL.replace("postgresql://", "postgresql+asyncpg://")

# Async engine for FastAPI
async_engine = create_async_engine(ASYNC_DATABASE_URL, echo=True)
AsyncSessionLocal = sessionmaker(
    async_engine, class_=AsyncSession, expire_on_commit=False
)

# Sync engine for migrations
sync_engine = create_engine(DATABASE_URL)
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=sync_engine)

async def get_async_db():
    async with AsyncSessionLocal() as session:
        try:
            yield session
        except Exception as e:
            logger.error(f"Database session error: {e}")
            await session.rollback()
            raise
        finally:
            await session.close()

def get_sync_db():
    db = SessionLocal()
    try:
        yield db
    except Exception as e:
        logger.error(f"Database session error: {e}")
        db.rollback()
        raise
    finally:
        db.close()
```

## Step 3: Unified Request Models

### 3.1 Email Models (`app/models/email_models.py`)
```python
from pydantic import BaseModel, EmailStr, Field, validator
from typing import List, Optional, Dict, Any, Union
from datetime import datetime
from enum import Enum

class EmailService(str, Enum):
    EMAIL = "email"

class EmailOperation(str, Enum):
    SEND_SINGLE = "send_single"
    SEND_BULK = "send_bulk"
    CREATE_TEMPLATE = "create_template"
    GET_TEMPLATE = "get_template"
    UPDATE_TEMPLATE = "update_template"
    DELETE_TEMPLATE = "delete_template"
    CHECK_STATUS = "check_status"
    SCHEDULE_SEND = "schedule_send"
    GET_ANALYTICS = "get_analytics"
    AUTHENTICATE = "authenticate"
    VALIDATE_EMAILS = "validate_emails"
    UNSUBSCRIBE = "unsubscribe"

class EmailProvider(str, Enum):
    OUTLOOK = "outlook"
    SMTP = "smtp"
    AWS_SES = "aws_ses"
    SENDGRID = "sendgrid"

class EmailPriority(str, Enum):
    LOW = "low"
    NORMAL = "normal"
    HIGH = "high"

class EmailBodyType(str, Enum):
    TEXT = "text"
    HTML = "html"

# Base adapter configuration
class AdapterConfig(BaseModel):
    service: EmailService
    operation: EmailOperation
    version: str = "v1"

# Email recipient model
class EmailRecipient(BaseModel):
    email: EmailStr
    name: Optional[str] = None
    personalization: Optional[Dict[str, Any]] = {}

# File attachment model
class FileAttachment(BaseModel):
    filename: str
    content_base64: str
    content_type: str
    size: Optional[int] = None

# Unified request models
class UnifiedEmailRequest(BaseModel):
    adapter_config: AdapterConfig
    parameters: Dict[str, Any]
    files: Optional[Dict[str, Any]] = {}

# Specific parameter models
class SendSingleParameters(BaseModel):
    provider: EmailProvider
    to: List[EmailRecipient]
    cc: Optional[List[EmailRecipient]] = []
    bcc: Optional[List[EmailRecipient]] = []
    subject: str
    body: str
    body_type: EmailBodyType = EmailBodyType.HTML
    priority: EmailPriority = EmailPriority.NORMAL
    reply_to: Optional[str] = None
    delivery_receipt: bool = False
    read_receipt: bool = False

class SendBulkParameters(BaseModel):
    provider: EmailProvider
    campaign_name: str
    template_id: Optional[str] = None
    recipients: List[EmailRecipient]
    subject: str
    body: str
    body_type: EmailBodyType = EmailBodyType.HTML
    batch_size: int = Field(default=50, ge=1, le=1000)
    delay_between_batches: int = Field(default=60, ge=0)
    priority: EmailPriority = EmailPriority.NORMAL
    schedule_send: Optional[datetime] = None

class CreateTemplateParameters(BaseModel):
    provider: EmailProvider
    template_id: str
    template_name: str
    subject: str
    body: str
    body_type: EmailBodyType = EmailBodyType.HTML
    variables: List[str] = []
    category: Optional[str] = None
    is_active: bool = True

# Response models
class EmailResponse(BaseModel):
    success: bool
    message: str
    data: Optional[Dict[str, Any]] = None
    metadata: "ResponseMetadata"
    errors: List["ErrorDetail"] = []
    warnings: List[str] = []

class ResponseMetadata(BaseModel):
    timestamp: datetime = Field(default_factory=datetime.utcnow)
    processing_time_ms: int
    adapter_version: str = "v1.0.0"
    service: str = "email"
    operation: str
    request_id: str

class ErrorDetail(BaseModel):
    code: str
    message: str
    field: Optional[str] = None
```

## Step 4: Core Email Service Implementation

### 4.1 Email Service (`app/services/email_service.py`)
```python
import asyncio
import uuid
import base64
from datetime import datetime
from typing import Dict, Any, List, Optional
from loguru import logger

from app.models.email_models import (
    EmailProvider, EmailOperation, SendSingleParameters, 
    SendBulkParameters, CreateTemplateParameters
)
from app.services.providers.outlook_provider import OutlookProvider
from app.services.providers.smtp_provider import SMTPProvider
from app.database.models import EmailJob, EmailTemplate, EmailLog
from app.database.connection import get_async_db
from sqlalchemy.ext.asyncio import AsyncSession

class EmailService:
    """Core email service implementing unified adapter pattern"""
    
    def __init__(self):
        self.providers = {
            EmailProvider.OUTLOOK: OutlookProvider(),
            EmailProvider.SMTP: SMTPProvider(),
        }
    
    async def process_email_request(self, request_data: Dict[str, Any]) -> Dict[str, Any]:
        """Process unified email request"""
        operation = request_data["adapter_config"]["operation"]
        parameters = request_data["parameters"]
        files = request_data.get("files", {})
        
        # Add file attachments to parameters if present
        if files.get("attachments"):
            parameters["attachments"] = files["attachments"]
        
        # Route to appropriate handler
        handlers = {
            EmailOperation.SEND_SINGLE: self._handle_send_single,
            EmailOperation.SEND_BULK: self._handle_send_bulk,
            EmailOperation.CREATE_TEMPLATE: self._handle_create_template,
            EmailOperation.CHECK_STATUS: self._handle_check_status,
        }
        
        handler = handlers.get(operation)
        if not handler:
            raise ValueError(f"Unsupported operation: {operation}")
        
        return await handler(parameters)
    
    async def _handle_send_single(self, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """Handle single email send"""
        params = SendSingleParameters(**parameters)
        
        provider = self.providers.get(params.provider)
        if not provider:
            raise ValueError(f"Provider not supported: {params.provider}")
        
        # Process attachments
        attachments = await self._process_attachments(
            parameters.get("attachments", [])
        )
        
        # Send email
        result = await provider.send_email(
            to=params.to,
            cc=params.cc,
            bcc=params.bcc,
            subject=params.subject,
            body=params.body,
            body_type=params.body_type,
            attachments=attachments,
            reply_to=params.reply_to,
            priority=params.priority
        )
        
        # Log the email
        await self._log_email_send(
            job_id=str(uuid.uuid4()),
            recipients=params.to,
            provider=params.provider,
            status="sent",
            message_id=result.get("message_id")
        )
        
        return {
            "job_id": str(uuid.uuid4()),
            "message_id": result.get("message_id"),
            "status": "sent",
            "recipients_count": len(params.to)
        }
    
    async def _handle_send_bulk(self, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """Handle bulk email send"""
        params = SendBulkParameters(**parameters)
        
        # Create job record
        job_id = str(uuid.uuid4())
        campaign_id = f"campaign_{int(datetime.utcnow().timestamp())}"
        
        async with AsyncSessionLocal() as db:
            job = EmailJob(
                job_id=job_id,
                campaign_id=campaign_id,
                service_operation=EmailOperation.SEND_BULK,
                status="queued",
                provider=params.provider,
                total_recipients=len(params.recipients),
                metadata={
                    "campaign_name": params.campaign_name,
                    "batch_size": params.batch_size,
                    "delay_between_batches": params.delay_between_batches
                }
            )
            db.add(job)
            await db.commit()
        
        # Start bulk processing in background
        asyncio.create_task(self._process_bulk_email(job_id, params))
        
        return {
            "job_id": job_id,
            "campaign_id": campaign_id,
            "status": "queued",
            "total_recipients": len(params.recipients)
        }
    
    async def _handle_create_template(self, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """Handle template creation"""
        params = CreateTemplateParameters(**parameters)
        
        async with AsyncSessionLocal() as db:
            template = EmailTemplate(
                template_id=params.template_id,
                template_name=params.template_name,
                subject=params.subject,
                body=params.body,
                body_type=params.body_type,
                variables=params.variables,
                category=params.category,
                is_active=params.is_active
            )
            db.add(template)
            await db.commit()
        
        return {
            "template_id": params.template_id,
            "status": "created",
            "variables": params.variables
        }
    
    async def _handle_check_status(self, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """Handle status check"""
        job_id = parameters.get("job_id")
        if not job_id:
            raise ValueError("job_id is required")
        
        async with AsyncSessionLocal() as db:
            result = await db.execute(
                select(EmailJob).where(EmailJob.job_id == job_id)
            )
            job = result.scalar_one_or_none()
            
            if not job:
                raise ValueError(f"Job not found: {job_id}")
            
            return {
                "job_id": job.job_id,
                "campaign_id": job.campaign_id,
                "status": job.status,
                "total_recipients": job.total_recipients,
                "sent_count": job.sent_count,
                "failed_count": job.failed_count,
                "progress_percentage": (
                    (job.sent_count + job.failed_count) / job.total_recipients * 100
                    if job.total_recipients > 0 else 0
                )
            }
    
    async def _process_attachments(self, attachments: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Process file attachments"""
        processed = []
        for attachment in attachments:
            try:
                content = base64.b64decode(attachment["content_base64"])
                processed.append({
                    "filename": attachment["filename"],
                    "content": content,
                    "content_type": attachment["content_type"]
                })
            except Exception as e:
                logger.error(f"Failed to process attachment: {e}")
                continue
        return processed
    
    async def _log_email_send(self, job_id: str, recipients: List, provider: str, 
                             status: str, message_id: Optional[str] = None):
        """Log email send operation"""
        async with AsyncSessionLocal() as db:
            for recipient in recipients:
                log_entry = EmailLog(
                    job_id=job_id,
                    recipient_email=recipient.email,
                    status=status,
                    provider=provider,
                    message_id=message_id
                )
                db.add(log_entry)
            await db.commit()
```

## Step 5: API Routes Implementation

### 5.1 Main Routes (`app/api/routes.py`)
```python
from fastapi import APIRouter, HTTPException, Request
from fastapi.responses import JSONResponse
from typing import Dict, Any
import uuid
from datetime import datetime
from loguru import logger

from app.models.email_models import UnifiedEmailRequest, EmailResponse, ResponseMetadata, ErrorDetail
from app.services.email_service import EmailService

router = APIRouter()
email_service = EmailService()

@router.post("/email", response_model=EmailResponse)
async def handle_email_request(request: UnifiedEmailRequest) -> EmailResponse:
    """Unified email service endpoint"""
    start_time = datetime.utcnow()
    request_id = str(uuid.uuid4())
    
    try:
        # Process the email request
        result = await email_service.process_email_request(request.dict())
        
        # Calculate processing time
        processing_time = int((datetime.utcnow() - start_time).total_seconds() * 1000)
        
        # Create response metadata
        metadata = ResponseMetadata(
            processing_time_ms=processing_time,
            service=request.adapter_config.service,
            operation=request.adapter_config.operation,
            request_id=request_id
        )
        
        return EmailResponse(
            success=True,
            message="Email operation completed successfully",
            data=result,
            metadata=metadata
        )
        
    except Exception as e:
        logger.error(f"Email request failed: {str(e)}")
        processing_time = int((datetime.utcnow() - start_time).total_seconds() * 1000)
        
        metadata = ResponseMetadata(
            processing_time_ms=processing_time,
            service=request.adapter_config.service,
            operation=request.adapter_config.operation,
            request_id=request_id
        )
        
        return EmailResponse(
            success=False,
            message="Email operation failed",
            data=None,
            metadata=metadata,
            errors=[ErrorDetail(code="OPERATION_FAILED", message=str(e))]
        )

@router.get("/health")
async def health_check():
    """Health check endpoint"""
    return {
        "status": "healthy",
        "service": "email-service",
        "version": "v1",
        "timestamp": datetime.utcnow().isoformat()
    }
```

## Step 6: Email Provider Implementation

### 6.1 SMTP Provider (`app/services/providers/smtp_provider.py`)
```python
import aiosmtplib
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from email.mime.base import MIMEBase
from email import encoders
from typing import List, Dict, Any, Optional
import os
from loguru import logger

from app.services.providers.base_provider import BaseEmailProvider
from app.models.email_models import EmailRecipient, EmailBodyType, EmailPriority

class SMTPProvider(BaseEmailProvider):
    """SMTP email provider implementation"""
    
    def __init__(self):
        self.host = os.getenv("SMTP_HOST")
        self.port = int(os.getenv("SMTP_PORT", 587))
        self.username = os.getenv("SMTP_USERNAME")
        self.password = os.getenv("SMTP_PASSWORD")
        self.use_tls = os.getenv("SMTP_USE_TLS", "true").lower() == "true"
    
    async def send_email(
        self,
        to: List[EmailRecipient],
        subject: str,
        body: str,
        body_type: EmailBodyType = EmailBodyType.HTML,
        cc: Optional[List[EmailRecipient]] = None,
        bcc: Optional[List[EmailRecipient]] = None,
        attachments: Optional[List[Dict[str, Any]]] = None,
        reply_to: Optional[str] = None,
        priority: EmailPriority = EmailPriority.NORMAL
    ) -> Dict[str, Any]:
        """Send email via SMTP"""
        
        try:
            # Create message
            msg = MIMEMultipart()
            msg['From'] = self.username
            msg['To'] = ", ".join([r.email for r in to])
            msg['Subject'] = subject
            
            if cc:
                msg['Cc'] = ", ".join([r.email for r in cc])
            if reply_to:
                msg['Reply-To'] = reply_to
            
            # Add body
            if body_type == EmailBodyType.HTML:
                msg.attach(MIMEText(body, 'html'))
            else:
                msg.attach(MIMEText(body, 'plain'))
            
            # Add attachments
            if attachments:
                for attachment in attachments:
                    part = MIMEBase('application', 'octet-stream')
                    part.set_payload(attachment['content'])
                    encoders.encode_base64(part)
                    part.add_header(
                        'Content-Disposition',
                        f'attachment; filename= {attachment["filename"]}'
                    )
                    msg.attach(part)
            
            # Get all recipients
            all_recipients = [r.email for r in to]
            if cc:
                all_recipients.extend([r.email for r in cc])
            if bcc:
                all_recipients.extend([r.email for r in bcc])
            
            # Send email
            async with aiosmtplib.SMTP(
                hostname=self.host,
                port=self.port,
                use_tls=self.use_tls
            ) as server:
                await server.login(self.username, self.password)
                await server.send_message(msg, recipients=all_recipients)
            
            logger.info(f"Email sent successfully via SMTP to {len(all_recipients)} recipients")
            
            return {
                "message_id": f"smtp_{hash(msg.as_string())}",
                "status": "sent",
                "recipients_count": len(all_recipients)
            }
            
        except Exception as e:
            logger.error(f"SMTP send failed: {str(e)}")
            raise
    
    async def authenticate(self, credentials: Dict[str, Any]) -> bool:
        """Test SMTP authentication"""
        try:
            async with aiosmtplib.SMTP(
                hostname=self.host,
                port=self.port,
                use_tls=self.use_tls
            ) as server:
                await server.login(self.username, self.password)
                return True
        except Exception as e:
            logger.error(f"SMTP authentication failed: {str(e)}")
            return False
    
    async def validate_email(self, email: str) -> bool:
        """Basic email validation"""
        import re
        pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        return re.match(pattern, email) is not None
```

## Step 7: Main Application Setup

### 7.1 Main Application (`app/main.py`)
```python
from fastapi import FastAPI, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from contextlib import asynccontextmanager
import uvicorn
import os
from loguru import logger

from app.api.routes import router as email_router
from app.database.connection import async_engine
from app.database.models import Base

@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan manager"""
    logger.info("Starting Email Service...")
    
    # Create database tables
    async with async_engine.begin() as conn:
        await conn.run_sync(Base.metadata.create_all)
    
    # Create storage directories
    os.makedirs("storage/attachments", exist_ok=True)
    os.makedirs("storage/templates", exist_ok=True)
    os.makedirs("storage/bulk_emails", exist_ok=True)
    os.makedirs("logs", exist_ok=True)
    
    logger.info("Email Service started successfully")
    yield
    
    logger.info("Shutting down Email Service...")

# Create FastAPI application
app = FastAPI(
    title="Company Email Service",
    description="Unified email service with multi-provider support",
    version="1.0.0",
    lifespan=lifespan
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Include email router
app.include_router(email_router, prefix="/api/v1", tags=["email"])

@app.get("/")
async def root():
    """Root endpoint"""
    return {
        "service": "Company Email Service",
        "version": "1.0.0",
        "status": "running",
        "unified_adapter": True,
        "supported_operations": [
            "send_single",
            "send_bulk", 
            "create_template",
            "check_status",
            "schedule_send",
            "get_analytics"
        ],
        "supported_providers": [
            "outlook",
            "smtp",
            "aws_ses",
            "sendgrid"
        ]
    }

if __name__ == "__main__":
    uvicorn.run(
        "app.main:app",
        host=os.getenv("SERVICE_HOST", "0.0.0.0"),
        port=int(os.getenv("SERVICE_PORT", 8000)),
        reload=True
    )
```

## Step 8: Usage Examples

### 8.1 Send Single Email
```bash
curl -X POST "http://localhost:8000/api/v1/email" \
  -H "Content-Type: application/json" \
