{"timestamp": "2025-06-23T05:03:59.180000", "endpoint": "fetch/security-properties", "input": {}, "output": {"success": true, "postgres_security_properties": [], "mongo_drafts": [{"_id": "6855330bcc6fc2d42ba9e4a2", "security_property_id": "SEC3", "entity_id": "E15", "attribute_id": "E15.At1", "classification": "confidential", "pii_type": "name", "encryption_required": false, "encryption_type": "", "masking_required": true, "masking_pattern": "EMP***", "access_level": "read_restricted", "audit_trail": true, "data_residency": "global", "retention_override": "", "anonymization_required": true, "anonymization_method": "tokenize", "compliance_frameworks": "gdpr, hipaa", "data_purpose": "", "consent_required": false, "cross_border_transfer": false, "backup_encryption": false, "secure_deletion": false, "natural_language": "Entity: Employee\nAttribute: employeeId\nClassification: confidential\nPII Type: name\nMasking Required: true\nMasking Pattern: EMP***\nAccess Level: read_restricted\nAudit Trail: true\nData Residency: global\nAnonymization Required: true\nAnonymization Method: tokenize\nCompliance Frameworks: gdpr, hipaa", "version": 2, "status": "deployed_to_temp", "created_at": "2025-06-20T10:08:11.956898", "updated_at": "2025-06-20T12:23:39.064941", "created_by": "Tarun", "updated_by": "Tarun", "security_property_status": "new", "changes_detected": []}], "total_postgres": 0, "total_drafts": 1, "operation": "fetch"}, "status": "success"}
{"timestamp": "2025-06-23T05:21:45.001915", "endpoint": "fetch/security-properties", "input": {}, "output": {"success": true, "postgres_security_properties": [], "mongo_drafts": [{"_id": "6855330bcc6fc2d42ba9e4a2", "security_property_id": "SEC3", "entity_id": "E15", "attribute_id": "E15.At1", "classification": "confidential", "pii_type": "name", "encryption_required": false, "encryption_type": "", "masking_required": true, "masking_pattern": "EMP***", "access_level": "read_restricted", "audit_trail": true, "data_residency": "global", "retention_override": "", "anonymization_required": true, "anonymization_method": "tokenize", "compliance_frameworks": "gdpr, hipaa", "data_purpose": "", "consent_required": false, "cross_border_transfer": false, "backup_encryption": false, "secure_deletion": false, "natural_language": "Entity: Employee\nAttribute: employeeId\nClassification: confidential\nPII Type: name\nMasking Required: true\nMasking Pattern: EMP***\nAccess Level: read_restricted\nAudit Trail: true\nData Residency: global\nAnonymization Required: true\nAnonymization Method: tokenize\nCompliance Frameworks: gdpr, hipaa", "version": 2, "status": "deployed_to_temp", "created_at": "2025-06-20T10:08:11.956898", "updated_at": "2025-06-20T12:23:39.064941", "created_by": "Tarun", "updated_by": "Tarun", "security_property_status": "new", "changes_detected": []}], "total_postgres": 0, "total_drafts": 1, "operation": "fetch"}, "status": "success"}
{"timestamp": "2025-06-23T08:01:32.908645", "endpoint": "fetch/security-properties", "input": {}, "output": {"success": true, "postgres_security_properties": [], "mongo_drafts": [{"_id": "6855330bcc6fc2d42ba9e4a2", "security_property_id": "SEC3", "entity_id": "E15", "attribute_id": "E15.At1", "classification": "confidential", "pii_type": "name", "encryption_required": false, "encryption_type": "", "masking_required": true, "masking_pattern": "EMP***", "access_level": "read_restricted", "audit_trail": true, "data_residency": "global", "retention_override": "", "anonymization_required": true, "anonymization_method": "tokenize", "compliance_frameworks": "gdpr, hipaa", "data_purpose": "", "consent_required": false, "cross_border_transfer": false, "backup_encryption": false, "secure_deletion": false, "natural_language": "Entity: Employee\nAttribute: employeeId\nClassification: confidential\nPII Type: name\nMasking Required: true\nMasking Pattern: EMP***\nAccess Level: read_restricted\nAudit Trail: true\nData Residency: global\nAnonymization Required: true\nAnonymization Method: tokenize\nCompliance Frameworks: gdpr, hipaa", "version": 2, "status": "deployed_to_temp", "created_at": "2025-06-20T10:08:11.956898", "updated_at": "2025-06-20T12:23:39.064941", "created_by": "Tarun", "updated_by": "Tarun", "security_property_status": "new", "changes_detected": []}], "total_postgres": 0, "total_drafts": 1, "operation": "fetch"}, "status": "success"}
{"timestamp": "2025-06-23T14:08:01.089042", "endpoint": "fetch/security-properties", "input": {}, "output": {"success": true, "postgres_security_properties": [], "mongo_drafts": [{"_id": "6855330bcc6fc2d42ba9e4a2", "security_property_id": "SEC3", "entity_id": "E15", "attribute_id": "E15.At1", "classification": "confidential", "pii_type": "name", "encryption_required": false, "encryption_type": "", "masking_required": true, "masking_pattern": "EMP***", "access_level": "read_restricted", "audit_trail": true, "data_residency": "global", "retention_override": "", "anonymization_required": true, "anonymization_method": "tokenize", "compliance_frameworks": "gdpr, hipaa", "data_purpose": "", "consent_required": false, "cross_border_transfer": false, "backup_encryption": false, "secure_deletion": false, "natural_language": "Entity: Employee\nAttribute: employeeId\nClassification: confidential\nPII Type: name\nMasking Required: true\nMasking Pattern: EMP***\nAccess Level: read_restricted\nAudit Trail: true\nData Residency: global\nAnonymization Required: true\nAnonymization Method: tokenize\nCompliance Frameworks: gdpr, hipaa", "version": 2, "status": "deployed_to_temp", "created_at": "2025-06-20T10:08:11.956898", "updated_at": "2025-06-20T12:23:39.064941", "created_by": "Tarun", "updated_by": "Tarun", "security_property_status": "new", "changes_detected": []}], "total_postgres": 0, "total_drafts": 1, "operation": "fetch"}, "status": "success"}
