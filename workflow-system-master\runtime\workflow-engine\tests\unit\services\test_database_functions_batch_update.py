import unittest
from unittest.mock import Mock, patch, MagicMock
from sqlalchemy.orm import Session
from sqlalchemy import text
from typing import Dict, Any, List, Union
import sys
import os

# Add project root to path
project_root = '/home/<USER>/workflow-system/runtime/workflow-engine'
if project_root not in sys.path:
    sys.path.insert(0, project_root)

# Import the function to test
from app.services.system_functions import batch_update, fetch_max_value

class TestBatchUpdate(unittest.TestCase):
    def setUp(self):
        # Mock database session
        self.db = Mock(spec=Session)
        
        # Sample data
        self.entity_id = "e001"
        self.lo_id = "lo123"
        self.table_name = "TestTable"
        
        # Create proper mock objects for attribute rows
        attr_row1 = Mock()
        attr_row1.attribute_id = "ATTR1"
        attr_row1.name = "FirstName"
        attr_row1.datatype = "String"
        
        attr_row2 = Mock()
        attr_row2.attribute_id = "ATTR2"
        attr_row2.name = "Age"
        attr_row2.datatype = "Integer"
        
        self.attr_rows = [attr_row1, attr_row2]
        
        # Create test records with lookup fields
        self.records = [
            {
                "ATTR1": "John", 
                "ATTR2": 30,
                "metadata": {"usage": "lookup"}
            },
            {
                "ATTR1": "Jane", 
                "ATTR2": 25,
                "metadata": {"usage": "lookup"}
            }
        ]
        
    def test_entity_not_found(self):
        """Test when the entity_id doesn't exist"""
        # Mock the database to return None for table_name
        self.db.execute.return_value.scalar.return_value = None
        
        # Assert that ValueError is raised
        with self.assertRaises(ValueError) as context:
            batch_update(self.db, self.entity_id, self.records, self.lo_id)
            
        # Check the error message
        self.assertIn(f"Entity '{self.entity_id}' not found", str(context.exception))
    
    def test_empty_records_list(self):
        """Test with an empty list of records"""
        # Let's mock at the lowest level possible to avoid complex interactions
        with patch('app.services.function_repository.FunctionRepository') as mock_repo:
            # Mock the essential DB calls
            mock_scalar = Mock()
            mock_scalar.scalar.return_value = self.table_name
            
            mock_fetchall = Mock()
            mock_fetchall.fetchall.return_value = self.attr_rows
            
            # Set up the side effects
            self.db.execute.side_effect = [mock_scalar, mock_fetchall]
            
            # Call with empty records list
            result = batch_update(self.db, self.entity_id, [], self.lo_id)
            
            # Basic assertions
            self.assertEqual(result["status"], "completed")
            self.assertEqual(result["total"], 0)
            
            # Verify function_repository was not called
            mock_repo.auto_execute.assert_not_called()

class TestFetchMaxValue(unittest.TestCase):
    def setUp(self):
        # Mock database session
        self.db = Mock(spec=Session)
        
        # Sample data
        self.entity_id = "e001"
        self.attribute_id = "attr1"
        
    def test_successful_fetch(self):
        """Test successful retrieval of max value"""
        # Mock database responses
        table_result_mock = Mock()
        table_result_mock.scalar.return_value = "TestTable"
        
        column_result_mock = Mock()
        column_result_mock.scalar.return_value = "age_column"
        
        max_result_mock = Mock()
        max_result_mock.scalar.return_value = 42
        
        # Set up the mock to return different mocks on successive calls
        self.db.execute.side_effect = [table_result_mock, column_result_mock, max_result_mock]
        
        # Call the function
        result = fetch_max_value(self.db, self.entity_id, self.attribute_id)
        
        # Assertions
        self.assertEqual(result, 42)
        self.assertEqual(self.db.execute.call_count, 3)
        
    def test_entity_or_attribute_not_found(self):
        """Test when entity or attribute doesn't exist"""
        # Mock for table name query (returns None)
        table_result_mock = Mock()
        table_result_mock.scalar.return_value = None
        
        self.db.execute.return_value = table_result_mock
        
        # Assert that ValueError is raised
        with self.assertRaises(ValueError) as context:
            fetch_max_value(self.db, self.entity_id, self.attribute_id)
            
        self.assertIn("Entity or attribute not found", str(context.exception))
        
    def test_no_max_value(self):
        """Test when there are no values to get max from"""
        # Mock database responses
        table_result_mock = Mock()
        table_result_mock.scalar.return_value = "TestTable"
        
        column_result_mock = Mock()
        column_result_mock.scalar.return_value = "age_column"
        
        max_result_mock = Mock()
        max_result_mock.scalar.return_value = None  # No max value found
        
        # Set up the mock to return different mocks on successive calls
        self.db.execute.side_effect = [table_result_mock, column_result_mock, max_result_mock]
        
        # Call the function
        result = fetch_max_value(self.db, self.entity_id, self.attribute_id)
        
        # Should return default value 0
        self.assertEqual(result, 0)