"""
Run Entity Example with Detailed Logging

This script parses and deploys an entity component from prescriptive text,
showing detailed information about what gets parsed and which tables get inserted.
It also compares the process with the insert_generator.py script to ensure nothing is missed.
"""

import os
import sys
import logging
import json
import traceback
from typing import Dict, List, Tuple, Any, Optional
from datetime import datetime

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('entity_example_detailed.log')
    ]
)
logger = logging.getLogger('entity_example_detailed')

# Add parent directory to path to import common modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from prescriptive_parser import PrescriptiveParser
from component_deployer import ComponentDeployer
from db_utils import execute_query

def log_comparison(message: str, level: str = "INFO"):
    """
    Log a message with comparison information.
    
    Args:
        message: Message to log
        level: Log level (INFO, WARNING, ERROR)
    """
    if level == "INFO":
        logger.info(f"[COMPARISON] {message}")
    elif level == "WARNING":
        logger.warning(f"[COMPARISON] {message}")
    elif level == "ERROR":
        logger.error(f"[COMPARISON] {message}")

def parse_entity(prescriptive_text: str) -> Tuple[Dict, List[str]]:
    """
    Parse entity from prescriptive text with detailed logging.
    
    Args:
        prescriptive_text: Prescriptive text to parse
        
    Returns:
        Tuple containing:
            - Parsed entity data
            - List of warnings
    """
    logger.info("=== Parsing Entity ===")
    
    # Initialize parser
    parser = PrescriptiveParser()
    
    # Parse prescriptive text
    logger.info("Parsing prescriptive text:")
    logger.info(prescriptive_text)
    
    entity_data, warnings = parser.parse('entities', prescriptive_text)
    
    # Log parsed data
    logger.info("Parsed entity data:")
    for entity_name, entity_info in entity_data.get('entities', {}).items():
        logger.info(f"Entity: {entity_name}")
        logger.info(f"  Description: {entity_info.get('description', 'N/A')}")
        
        # Log attributes
        logger.info("  Attributes:")
        for attr_name, attr_info in entity_info.get('attributes', {}).items():
            logger.info(f"    {attr_name}: {attr_info}")
        
        # Log relationships
        logger.info("  Relationships:")
        for rel_name, rel_info in entity_info.get('relationships', {}).items():
            logger.info(f"    {rel_name}: {rel_info}")
        
        # Log business rules
        logger.info("  Business Rules:")
        for rule_id, rule_info in entity_info.get('business_rules', {}).items():
            logger.info(f"    {rule_id}: {rule_info}")
        
        # Log calculated fields
        logger.info("  Calculated Fields:")
        for field_id, field_info in entity_info.get('calculated_fields', {}).items():
            logger.info(f"    {field_id}: {field_info}")
        
        # Log lifecycle management
        logger.info("  Lifecycle Management:")
        if 'lifecycle_management' in entity_info:
            for lm_type, lm_info in entity_info.get('lifecycle_management', {}).items():
                logger.info(f"    {lm_type}: {lm_info}")
    
    # Log warnings
    if warnings:
        logger.warning("Parse warnings:")
        for warning in warnings:
            logger.warning(f"  {warning}")
    
    return entity_data, warnings

def deploy_entity(entity_data: Dict, schema_name: str = "workflow_temp") -> Tuple[bool, List[str]]:
    """
    Deploy entity to database with detailed logging.
    
    Args:
        entity_data: Parsed entity data
        schema_name: Schema name to deploy to
        
    Returns:
        Tuple containing:
            - Boolean indicating if deployment was successful
            - List of messages
    """
    logger.info("=== Deploying Entity ===")
    
    # Initialize deployer
    deployer = ComponentDeployer(use_temp_schema=True)
    
    # Deploy entity directly from prescriptive text
    logger.info(f"Deploying entity to schema {schema_name}")
    with open('samples/sample_entity_output.txt', 'r') as f:
        prescriptive_text = f.read()
    success, messages = deployer.deploy_from_prescriptive_text('entities', prescriptive_text)
    
    # Log messages
    if messages:
        logger.info("Deploy messages:")
        for message in messages:
            logger.info(f"  {message}")
    
    return success, messages

def query_deployed_entity(schema_name: str = "workflow_temp") -> None:
    """
    Query deployed entity in database.
    
    Args:
        schema_name: Schema name to query
    """
    logger.info("=== Querying Deployed Entity ===")
    
    # Query entities table
    logger.info(f"Querying {schema_name}.entities table")
    success, messages, entities = execute_query(
        f"SELECT entity_id, name, description, metadata, lifecycle_management FROM {schema_name}.entities",
        schema_name=schema_name
    )
    
    if success and entities:
        logger.info("Entities:")
        for entity in entities:
            logger.info(f"  {entity}")
    else:
        logger.warning("No entities found")
    
    # Query entity_attributes table
    logger.info(f"Querying {schema_name}.entity_attributes table")
    success, messages, attributes = execute_query(
        f"SELECT attribute_id, entity_id, name, type, required, default_value, calculated_field, calculation_formula, dependencies FROM {schema_name}.entity_attributes",
        schema_name=schema_name
    )
    
    if success and attributes:
        logger.info("Entity Attributes:")
        for attribute in attributes:
            logger.info(f"  {attribute}")
    else:
        logger.warning("No entity attributes found")

def compare_with_insert_generator(entity_data: Dict) -> None:
    """
    Compare the entity data with how it would be processed by insert_generator.py.
    
    Args:
        entity_data: Parsed entity data
    """
    logger.info("=== Comparing with insert_generator.py ===")
    
    # Create a mock workflow data structure similar to what insert_generator.py expects
    workflow_data = {
        "entities": []
    }
    
    # Convert our entity data to the format expected by insert_generator.py
    for entity_name, entity_info in entity_data.get('entities', {}).items():
        entity_obj = {
            "id": f"en_{entity_name.lower()}",
            "name": entity_name,
            "type": "standard",
            "version": "1.0",
            "status": "active",
            "attributes": []
        }
        
        # Add attributes
        for attr_name, attr_info in entity_info.get('attributes', {}).items():
            attribute_obj = {
                "id": f"at_{attr_name.lower()}",
                "name": attr_name,
                "display_name": attr_name,
                "datatype": attr_info.get('type', 'string'),
                "required": attr_info.get('required', False),
                "version": "1.0",
                "status": "active"
            }
            
            # Add validations
            if 'validations' in attr_info:
                attribute_obj["validations"] = []
                for validation in attr_info.get('validations', []):
                    validation_obj = {
                        "rule": validation.get('rule', ''),
                        "expression": validation.get('expression', '')
                    }
                    attribute_obj["validations"].append(validation_obj)
            
            entity_obj["attributes"].append(attribute_obj)
        
        # Add relationships
        if 'relationships' in entity_info:
            entity_obj["relationships"] = []
            for rel_name, rel_info in entity_info.get('relationships', {}).items():
                relationship_obj = {
                    "entity_id": rel_info.get('entity', ''),
                    "type": rel_info.get('type', ''),
                    "through_attribute": rel_info.get('through_attribute', ''),
                    "to_attribute": rel_info.get('to_attribute', '')
                }
                entity_obj["relationships"].append(relationship_obj)
        
        workflow_data["entities"].append(entity_obj)
    
    # Log the workflow data structure
    logger.info("Converted entity data to insert_generator.py format:")
    logger.info(json.dumps(workflow_data, indent=2))
    
    # Simulate what insert_generator.py would do
    log_comparison("Simulating insert_generator.py processing...")
    
    # Check for entity table inserts
    log_comparison("Entity table inserts:")
    for entity in workflow_data.get("entities", []):
        entity_id = entity.get("id", "")
        name = entity.get("name", "")
        entity_type = entity.get("type", "")
        version = entity.get("version", "")
        status = entity.get("status", "")
        
        sql = f"""
        INSERT INTO entities (entity_id, name, type, version, status, created_at, updated_at)
        VALUES ('{entity_id}', '{name}', '{entity_type}', '{version}', '{status}', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);
        """
        log_comparison(f"SQL: {sql}")
    
    # Check for entity attribute inserts
    log_comparison("Entity attribute inserts:")
    for entity in workflow_data.get("entities", []):
        entity_id = entity.get("id", "")
        
        for attr in entity.get("attributes", []):
            attribute_id = attr.get("id", "")
            name = attr.get("name", "")
            display_name = attr.get("display_name", "")
            datatype = attr.get("datatype", "")
            required = attr.get("required", False)
            version = attr.get("version", "")
            status = attr.get("status", "")
            
            sql = f"""
            INSERT INTO entity_attributes (attribute_id, entity_id, name, display_name, datatype, version, status, required, created_at, updated_at)
            VALUES ('{attribute_id}', '{entity_id}', '{name}', '{display_name}', '{datatype}', '{version}', '{status}', {required}, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);
            """
            log_comparison(f"SQL: {sql}")
            
            # Check for attribute validations
            if "validations" in attr:
                for validation in attr.get("validations", []):
                    rule = validation.get("rule", "")
                    expression = validation.get("expression", "")
                    
                    sql = f"""
                    INSERT INTO attribute_validations (attribute_id, rule, expression, created_at, updated_at)
                    VALUES ('{attribute_id}', '{rule}', '{expression}', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);
                    """
                    log_comparison(f"SQL: {sql}")
    
    # Check for entity relationships
    log_comparison("Entity relationship inserts:")
    for entity in workflow_data.get("entities", []):
        entity_id = entity.get("id", "")
        
        if "relationships" in entity:
            for relationship in entity.get("relationships", []):
                target_entity_id = relationship.get("entity_id", "")
                relationship_type = relationship.get("type", "")
                source_attribute_id = relationship.get("through_attribute", "")
                target_attribute_id = relationship.get("to_attribute", "")
                
                sql = f"""
                INSERT INTO entity_relationships (source_entity_id, target_entity_id, relationship_type, source_attribute_id, target_attribute_id, created_at, updated_at)
                VALUES ('{entity_id}', '{target_entity_id}', '{relationship_type}', '{source_attribute_id}', '{target_attribute_id}', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);
                """
                log_comparison(f"SQL: {sql}")
    
    # Compare with v2 deployer
    log_comparison("Comparing with v2 deployer:")
    log_comparison("1. v2 deployer uses prescriptive_parser.py to parse prescriptive text into structured data")
    log_comparison("2. v2 deployer uses component_deployer.py to deploy the structured data to the database")
    log_comparison("3. v2 deployer uses entity_deployer.py to deploy entities to the database")
    
    # Check for differences
    log_comparison("Key differences:")
    log_comparison("1. insert_generator.py uses a JSON file as input, while v2 uses prescriptive text")
    log_comparison("2. insert_generator.py generates SQL statements, while v2 executes them directly")
    log_comparison("3. insert_generator.py handles multiple components at once, while v2 can handle them separately")
    log_comparison("4. v2 has more validation and error handling")
    log_comparison("5. v2 uses a temporary schema for validation before promoting to runtime schema")

def main():
    """
    Main function.
    """
    # Read prescriptive text
    with open('samples/sample_entity_output.txt', 'r') as f:
        prescriptive_text = f.read()
    
    # Parse entity
    entity_data, warnings = parse_entity(prescriptive_text)
    
    # Compare with insert_generator.py
    compare_with_insert_generator(entity_data)
    
    # Deploy entity
    success, messages = deploy_entity(entity_data)
    
    # Query deployed entity
    if success:
        query_deployed_entity()
    
    logger.info("=== Summary ===")
    logger.info(f"Deployment successful: {success}")
    
    # Print summary to console
    print(f"Deployment successful: {success}")
    print("See entity_example_detailed.log for detailed information")

if __name__ == '__main__':
    main()
