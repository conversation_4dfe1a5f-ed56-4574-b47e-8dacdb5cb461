"""
Check the structure of entity tables.
"""

import sys
from db_utils import execute_query

def main():
    # Get schema name from command line arguments
    schema_name = "workflow_temp"
    if len(sys.argv) > 1:
        schema_name = sys.argv[1]
    
    # Get table name from command line arguments
    table_name = "e1.employee"
    if len(sys.argv) > 2:
        table_name = sys.argv[2]
    
    # Get table columns
    print(f"Columns in {schema_name}.{table_name}:")
    success, messages, columns = execute_query(
        """
        SELECT column_name, data_type, is_nullable
        FROM information_schema.columns 
        WHERE table_schema = %s
        AND table_name = %s
        ORDER BY ordinal_position
        """,
        (schema_name, table_name)
    )
    
    if success and columns:
        for column in columns:
            nullable = "NULL" if column[2] == "YES" else "NOT NULL"
            print(f"  {column[0]} - {column[1]} {nullable}")
    else:
        print(f"Failed to get columns for {schema_name}.{table_name}")
        if messages:
            print("Messages:")
            for message in messages:
                print(f"  {message}")

if __name__ == "__main__":
    main()
