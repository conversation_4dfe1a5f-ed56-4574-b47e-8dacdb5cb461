-- Initialize workflow_runtime database schema

-- Create workflow schema if it doesn't exist
CREATE SCHEMA IF NOT EXISTS workflow_runtime;

-- Tenant table for multi-tenancy
CREATE TABLE IF NOT EXISTS workflow_runtime.tenants (
    tenant_id VARCHAR(50) PRIMARY KEY,
    name VA<PERSON><PERSON><PERSON>(255) NOT NULL,
    status VARCHAR(50) NOT NULL DEFAULT 'Active',
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Global Objective table
CREATE TABLE IF NOT EXISTS workflow_runtime.global_objectives (
    go_id VARCHAR(50) PRIMARY KEY,
    tenant_id VARCHAR(50) NOT NULL REFERENCES workflow_runtime.tenants(tenant_id),
    name VARCHAR(255) NOT NULL,
    version VARCHAR(20) NOT NULL,
    status VARCHAR(50) NOT NULL DEFAULT 'Active',
    config <PERSON><PERSON><PERSON><PERSON> NOT NULL,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Workflow instances table
CREATE TABLE IF NOT EXISTS workflow_runtime.workflow_instances (
    instance_id VARCHAR(50) PRIMARY KEY,
    go_id VARCHAR(50) NOT NULL REFERENCES workflow_runtime.global_objectives(go_id),
    tenant_id VARCHAR(50) NOT NULL REFERENCES workflow_runtime.tenants(tenant_id),
    status VARCHAR(50) NOT NULL DEFAULT 'Draft',
    started_by VARCHAR(50) NOT NULL,
    started_at TIMESTAMP NOT NULL,
    completed_at TIMESTAMP NULL,
    current_lo_id VARCHAR(100) NULL,
    instance_data JSONB NOT NULL DEFAULT '{}',
    is_test BOOLEAN NOT NULL DEFAULT FALSE,
    version VARCHAR(20) NOT NULL,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Workflow execution log table
CREATE TABLE IF NOT EXISTS workflow_runtime.workflow_execution_log (
    log_id SERIAL PRIMARY KEY,
    instance_id VARCHAR(50) NOT NULL REFERENCES workflow_runtime.workflow_instances(instance_id),
    lo_id VARCHAR(100) NOT NULL,
    status VARCHAR(50) NOT NULL,
    executed_by VARCHAR(50) NOT NULL,
    input_data JSONB NULL,
    output_data JSONB NULL,
    execution_time_ms INTEGER NULL,
    sequence_number INTEGER NOT NULL,
    started_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    completed_at TIMESTAMP NULL
);

-- Entity table for generic entities
CREATE TABLE IF NOT EXISTS workflow_runtime.entities (
    entity_id VARCHAR(50) NOT NULL,
    entity_type VARCHAR(50) NOT NULL,
    tenant_id VARCHAR(50) NOT NULL REFERENCES workflow_runtime.tenants(tenant_id),
    data JSONB NOT NULL,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (entity_id, entity_type, tenant_id)
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_workflow_instances_tenant_id ON workflow_runtime.workflow_instances(tenant_id);
CREATE INDEX IF NOT EXISTS idx_workflow_instances_status ON workflow_runtime.workflow_instances(status);
CREATE INDEX IF NOT EXISTS idx_workflow_execution_log_instance_id ON workflow_runtime.workflow_execution_log(instance_id);
CREATE INDEX IF NOT EXISTS idx_entities_tenant_id ON workflow_runtime.entities(tenant_id);
CREATE INDEX IF NOT EXISTS idx_entities_entity_type ON workflow_runtime.entities(entity_type);

-- Initialize with a test tenant
INSERT INTO workflow_runtime.tenants (tenant_id, name, status)
VALUES ('T001', 'Test Tenant', 'Active')
ON CONFLICT (tenant_id) DO NOTHING;
