=== Command output log - 2025-05-13 17:57:33.714605 ===


=== Running command: python3 reset_database_direct.py ===

2025-05-13 17:57:33,748 - reset_database_direct - INFO - Resetting database schema workflow_temp
2025-05-13 17:57:33,759 - reset_database_direct - INFO - Dropped table workflow_temp.entity_attributes
2025-05-13 17:57:33,760 - reset_database_direct - INFO - Dropped table workflow_temp.entity_attribute_metadata
2025-05-13 17:57:33,761 - reset_database_direct - INFO - Dropped table workflow_temp.entity_relationships
2025-05-13 17:57:33,762 - reset_database_direct - INFO - Dropped table workflow_temp.entity_business_rules
2025-05-13 17:57:33,762 - reset_database_direct - INFO - Dropped table workflow_temp.entity_lifecycle_management
2025-05-13 17:57:33,763 - reset_database_direct - INFO - Dropped table workflow_temp.e1_employee
2025-05-13 17:57:33,764 - reset_database_direct - INFO - Dropped table workflow_temp.e2_department
2025-05-13 17:57:33,765 - reset_database_direct - INFO - Dropped table workflow_temp.e3_leavetype
2025-05-13 17:57:33,765 - reset_database_direct - INFO - Dropped table workflow_temp.e4_leaveapplication
2025-05-13 17:57:33,766 - reset_database_direct - INFO - Dropped table workflow_temp.e5_calendarevent
2025-05-13 17:57:33,777 - reset_database_direct - INFO - Truncated table workflow_temp.books
2025-05-13 17:57:33,779 - reset_database_direct - INFO - Truncated table workflow_temp.objective_permissions
2025-05-13 17:57:33,781 - reset_database_direct - INFO - Truncated table workflow_temp.input_stack
2025-05-13 17:57:33,785 - reset_database_direct - INFO - Truncated table workflow_temp.lo_input_items
2025-05-13 17:57:33,788 - reset_database_direct - INFO - Truncated table workflow_temp.permission_types
2025-05-13 17:57:33,789 - reset_database_direct - INFO - Truncated table workflow_temp.chapters
2025-05-13 17:57:33,791 - reset_database_direct - INFO - Truncated table workflow_temp.lo_output_stack
2025-05-13 17:57:33,792 - reset_database_direct - INFO - Truncated table workflow_temp.attribute_ui_controls
2025-05-13 17:57:33,793 - reset_database_direct - INFO - Truncated table workflow_temp.organizational_units
2025-05-13 17:57:33,795 - reset_database_direct - INFO - Truncated table workflow_temp.lo_output_items
2025-05-13 17:57:33,796 - reset_database_direct - INFO - Truncated table workflow_temp.agent_stack
2025-05-13 17:57:33,797 - reset_database_direct - INFO - Truncated table workflow_temp.conditional_success_messages
2025-05-13 17:57:33,798 - reset_database_direct - INFO - Truncated table workflow_temp.prescription
2025-05-13 17:57:33,799 - reset_database_direct - INFO - Truncated table workflow_temp.lo_data_mapping_stack
2025-05-13 17:57:33,800 - reset_database_direct - INFO - Truncated table workflow_temp.agent_rights
2025-05-13 17:57:33,805 - reset_database_direct - INFO - Truncated table workflow_temp.lo_input_stack
2025-05-13 17:57:33,806 - reset_database_direct - INFO - Truncated table workflow_temp.lo_dependencies
2025-05-13 17:57:33,808 - reset_database_direct - INFO - Truncated table workflow_temp.output_stack
2025-05-13 17:57:33,811 - reset_database_direct - INFO - Truncated table workflow_temp.roles
2025-05-13 17:57:33,813 - reset_database_direct - INFO - Truncated table workflow_temp.data_mapping_stack
2025-05-13 17:57:33,814 - reset_database_direct - INFO - Truncated table workflow_temp.lo_input_execution
2025-05-13 17:57:33,816 - reset_database_direct - INFO - Truncated table workflow_temp.output_items
2025-05-13 17:57:33,817 - reset_database_direct - INFO - Truncated table workflow_temp.role
2025-05-13 17:57:33,818 - reset_database_direct - INFO - Truncated table workflow_temp.go_performance_metrics
2025-05-13 17:57:33,819 - reset_database_direct - INFO - Truncated table workflow_temp.success_messages
2025-05-13 17:57:33,820 - reset_database_direct - INFO - Truncated table workflow_temp.attribute_enum_values
2025-05-13 17:57:33,821 - reset_database_direct - INFO - Truncated table workflow_temp.lo_nested_functions
2025-05-13 17:57:33,823 - reset_database_direct - INFO - Truncated table workflow_temp.dropdown_data_sources
2025-05-13 17:57:33,824 - reset_database_direct - INFO - Truncated table workflow_temp.attribute_validations
2025-05-13 17:57:33,825 - reset_database_direct - INFO - Truncated table workflow_temp.lo_system_functions
2025-05-13 17:57:33,826 - reset_database_direct - INFO - Truncated table workflow_temp.terminal_pathways
2025-05-13 17:57:33,827 - reset_database_direct - INFO - Truncated table workflow_temp.lo_data_mappings
2025-05-13 17:57:33,857 - reset_database_direct - INFO - Truncated table workflow_temp.tenants
2025-05-13 17:57:33,859 - reset_database_direct - INFO - Truncated table workflow_temp.ui_stack
2025-05-13 17:57:33,860 - reset_database_direct - INFO - Truncated table workflow_temp.lo_input_validations
2025-05-13 17:57:33,861 - reset_database_direct - INFO - Truncated table workflow_temp.user_organizations
2025-05-13 17:57:33,888 - reset_database_direct - INFO - Truncated table workflow_temp.global_objectives
2025-05-13 17:57:33,889 - reset_database_direct - INFO - Truncated table workflow_temp.permission_capabilities
2025-05-13 17:57:33,890 - reset_database_direct - INFO - Truncated table workflow_temp.user
2025-05-13 17:57:33,892 - reset_database_direct - INFO - Truncated table workflow_temp.user_role
2025-05-13 17:57:33,892 - reset_database_direct - INFO - Truncated table workflow_temp.role_inheritance
2025-05-13 17:57:33,895 - reset_database_direct - INFO - Truncated table workflow_temp.workflow_instances
2025-05-13 17:57:33,897 - reset_database_direct - INFO - Truncated table workflow_temp.calculated_fields
2025-05-13 17:57:33,898 - reset_database_direct - INFO - Truncated table workflow_temp.data_mappings
2025-05-13 17:57:33,899 - reset_database_direct - INFO - Truncated table workflow_temp.entities
2025-05-13 17:57:33,900 - reset_database_direct - INFO - Truncated table workflow_temp.mapping_rules
2025-05-13 17:57:33,901 - reset_database_direct - INFO - Truncated table workflow_temp.output_triggers
2025-05-13 17:57:33,902 - reset_database_direct - INFO - Truncated table workflow_temp.go_lo_mapping
2025-05-13 17:57:33,903 - reset_database_direct - INFO - Truncated table workflow_temp.lo_output_execution
2025-05-13 17:57:33,904 - reset_database_direct - INFO - Truncated table workflow_temp.metrics_aggregation
2025-05-13 17:57:33,905 - reset_database_direct - INFO - Truncated table workflow_temp.metrics_reporting
2025-05-13 17:57:33,905 - reset_database_direct - INFO - Truncated table workflow_temp.user_roles
2025-05-13 17:57:33,924 - reset_database_direct - INFO - Truncated table workflow_temp.local_objectives
2025-05-13 17:57:33,925 - reset_database_direct - INFO - Truncated table workflow_temp.input_data_sources
2025-05-13 17:57:33,926 - reset_database_direct - INFO - Truncated table workflow_temp.input_dependencies
2025-05-13 17:57:33,927 - reset_database_direct - INFO - Truncated table workflow_temp.system_functions
2025-05-13 17:57:33,929 - reset_database_direct - INFO - Truncated table workflow_temp.permission_contexts
2025-05-13 17:57:33,930 - reset_database_direct - INFO - Truncated table workflow_temp.workflow_results
2025-05-13 17:57:33,934 - reset_database_direct - INFO - Truncated table workflow_temp.users
2025-05-13 17:57:33,935 - reset_database_direct - INFO - Truncated table workflow_temp.user_oauth_tokens
2025-05-13 17:57:33,937 - reset_database_direct - INFO - Truncated table workflow_temp.runtime_metrics_stack
2025-05-13 17:57:33,938 - reset_database_direct - INFO - Truncated table workflow_temp.workflow_transaction
2025-05-13 17:57:33,939 - reset_database_direct - INFO - Truncated table workflow_temp.role_permissions
2025-05-13 17:57:33,940 - reset_database_direct - INFO - Truncated table workflow_temp.user_sessions
2025-05-13 17:57:33,941 - reset_database_direct - INFO - Truncated table workflow_temp.input_items
2025-05-13 17:57:33,942 - reset_database_direct - INFO - Truncated table workflow_temp.lo_output_triggers
2025-05-13 17:57:33,943 - reset_database_direct - INFO - Truncated table workflow_temp.ui_elements
2025-05-13 17:57:33,943 - reset_database_direct - INFO - Successfully reset database schema workflow_temp
Dropped table workflow_temp.entity_attributes
Dropped table workflow_temp.entity_attribute_metadata
Dropped table workflow_temp.entity_relationships
Dropped table workflow_temp.entity_business_rules
Dropped table workflow_temp.entity_lifecycle_management
Dropped table workflow_temp.e1_employee
Dropped table workflow_temp.e2_department
Dropped table workflow_temp.e3_leavetype
Dropped table workflow_temp.e4_leaveapplication
Dropped table workflow_temp.e5_calendarevent
Truncated table workflow_temp.books
Truncated table workflow_temp.objective_permissions
Truncated table workflow_temp.input_stack
Truncated table workflow_temp.lo_input_items
Truncated table workflow_temp.permission_types
Truncated table workflow_temp.chapters
Truncated table workflow_temp.lo_output_stack
Truncated table workflow_temp.attribute_ui_controls
Truncated table workflow_temp.organizational_units
Truncated table workflow_temp.lo_output_items
Truncated table workflow_temp.agent_stack
Truncated table workflow_temp.conditional_success_messages
Truncated table workflow_temp.prescription
Truncated table workflow_temp.lo_data_mapping_stack
Truncated table workflow_temp.agent_rights
Truncated table workflow_temp.lo_input_stack
Truncated table workflow_temp.lo_dependencies
Truncated table workflow_temp.output_stack
Truncated table workflow_temp.roles
Truncated table workflow_temp.data_mapping_stack
Truncated table workflow_temp.lo_input_execution
Truncated table workflow_temp.output_items
Truncated table workflow_temp.role
Truncated table workflow_temp.go_performance_metrics
Truncated table workflow_temp.success_messages
Truncated table workflow_temp.attribute_enum_values
Truncated table workflow_temp.lo_nested_functions
Truncated table workflow_temp.dropdown_data_sources
Truncated table workflow_temp.attribute_validations
Truncated table workflow_temp.lo_system_functions
Truncated table workflow_temp.terminal_pathways
Truncated table workflow_temp.lo_data_mappings
Truncated table workflow_temp.tenants
Truncated table workflow_temp.ui_stack
Truncated table workflow_temp.lo_input_validations
Truncated table workflow_temp.user_organizations
Truncated table workflow_temp.global_objectives
Truncated table workflow_temp.permission_capabilities
Truncated table workflow_temp.user
Truncated table workflow_temp.user_role
Truncated table workflow_temp.role_inheritance
Truncated table workflow_temp.workflow_instances
Truncated table workflow_temp.calculated_fields
Truncated table workflow_temp.data_mappings
Truncated table workflow_temp.entities
Truncated table workflow_temp.mapping_rules
Truncated table workflow_temp.output_triggers
Truncated table workflow_temp.go_lo_mapping
Truncated table workflow_temp.lo_output_execution
Truncated table workflow_temp.metrics_aggregation
Truncated table workflow_temp.metrics_reporting
Truncated table workflow_temp.user_roles
Truncated table workflow_temp.local_objectives
Truncated table workflow_temp.input_data_sources
Truncated table workflow_temp.input_dependencies
Truncated table workflow_temp.system_functions
Truncated table workflow_temp.permission_contexts
Truncated table workflow_temp.workflow_results
Truncated table workflow_temp.users
Truncated table workflow_temp.user_oauth_tokens
Truncated table workflow_temp.runtime_metrics_stack
Truncated table workflow_temp.workflow_transaction
Truncated table workflow_temp.role_permissions
Truncated table workflow_temp.user_sessions
Truncated table workflow_temp.input_items
Truncated table workflow_temp.lo_output_triggers
Truncated table workflow_temp.ui_elements


=== Command completed with exit code: 0 ===



=== Running command: python3 init_database.py ===

2025-05-13 17:57:33,989 - init_database - INFO - Initializing database schema workflow_temp
2025-05-13 17:57:33,995 - init_database - INFO - Created schema workflow_temp
2025-05-13 17:57:34,001 - init_database - INFO - Created table workflow_temp.entities
2025-05-13 17:57:34,020 - init_database - INFO - Created table workflow_temp.entity_attributes
2025-05-13 17:57:34,022 - init_database - INFO - Created table workflow_temp.entity_attribute_metadata
2025-05-13 17:57:34,024 - init_database - INFO - Created table workflow_temp.entity_relationships
2025-05-13 17:57:34,025 - init_database - INFO - Created table workflow_temp.entity_business_rules
2025-05-13 17:57:34,026 - init_database - INFO - Created table workflow_temp.calculated_fields
2025-05-13 17:57:34,027 - init_database - INFO - Created table workflow_temp.entity_lifecycle_management
2025-05-13 17:57:34,028 - init_database - INFO - Created table workflow_temp.attribute_enum_values
2025-05-13 17:57:34,028 - init_database - INFO - Created table workflow_temp.attribute_validations
2025-05-13 17:57:34,028 - init_database - INFO - Created table workflow_temp.global_objectives
2025-05-13 17:57:34,028 - init_database - INFO - Created table workflow_temp.tenants
2025-05-13 17:57:34,028 - init_database - INFO - Created table workflow_temp.books
2025-05-13 17:57:34,028 - init_database - INFO - Created table workflow_temp.chapters
2025-05-13 17:57:34,028 - init_database - INFO - Created table workflow_temp.local_objectives
2025-05-13 17:57:34,028 - init_database - INFO - Created table workflow_temp.go_lo_mapping
2025-05-13 17:57:34,028 - init_database - INFO - Created table workflow_temp.output_stack
2025-05-13 17:57:34,029 - init_database - INFO - Created table workflow_temp.output_items
2025-05-13 17:57:34,029 - init_database - INFO - Created table workflow_temp.output_triggers
2025-05-13 17:57:34,029 - init_database - INFO - Successfully initialized database schema workflow_temp
Created schema workflow_temp
Created table workflow_temp.entities
Created table workflow_temp.entity_attributes
Created table workflow_temp.entity_attribute_metadata
Created table workflow_temp.entity_relationships
Created table workflow_temp.entity_business_rules
Created table workflow_temp.calculated_fields
Created table workflow_temp.entity_lifecycle_management
Created table workflow_temp.attribute_enum_values
Created table workflow_temp.attribute_validations
Created table workflow_temp.global_objectives
Created table workflow_temp.tenants
Created table workflow_temp.books
Created table workflow_temp.chapters
Created table workflow_temp.local_objectives
Created table workflow_temp.go_lo_mapping
Created table workflow_temp.output_stack
Created table workflow_temp.output_items
Created table workflow_temp.output_triggers


=== Command completed with exit code: 0 ===



=== Running command: python3 deploy_entities_and_go.py ===

2025-05-13 17:57:34,132 - deploy_entities_and_go - INFO - Starting entity and GO deployment
2025-05-13 17:57:34,132 - deploy_entities_and_go - INFO - Deploying entity definitions
2025-05-13 17:57:34,132 - entity_parser - INFO - Starting to parse entity definitions
2025-05-13 17:57:34,132 - entity_parser - INFO - Parsing entity: Employee
2025-05-13 17:57:34,132 - entity_parser - INFO - Found enum values for attribute 'status': ['Active', 'Inactive', 'OnLeave']
2025-05-13 17:57:34,133 - entity_parser - INFO - Successfully parsed entity 'Employee'
2025-05-13 17:57:34,133 - entity_parser - INFO - Parsing entity: Employee
2025-05-13 17:57:34,133 - entity_parser - INFO - Successfully parsed entity 'Employee'
2025-05-13 17:57:34,133 - entity_parser - INFO - Parsing entity: Employee
2025-05-13 17:57:34,133 - entity_parser - WARNING - Attribute 'email' specified in Additional Properties not found in entity 'Employee'
2025-05-13 17:57:34,133 - entity_parser - INFO - Successfully parsed entity 'Employee'
2025-05-13 17:57:34,133 - entity_parser - INFO - Parsing entity: Employee
2025-05-13 17:57:34,133 - entity_parser - INFO - Successfully parsed entity 'Employee'
2025-05-13 17:57:34,133 - entity_parser - INFO - Parsing entity: Employee
2025-05-13 17:57:34,133 - entity_parser - INFO - Successfully parsed entity 'Employee'
2025-05-13 17:57:34,133 - entity_parser - INFO - Parsing entity: Department
2025-05-13 17:57:34,133 - entity_parser - INFO - Successfully parsed entity 'Department'
2025-05-13 17:57:34,133 - entity_parser - INFO - Parsing entity: Department
2025-05-13 17:57:34,133 - entity_parser - INFO - Successfully parsed entity 'Department'
2025-05-13 17:57:34,133 - entity_parser - INFO - Parsing entity: Department
2025-05-13 17:57:34,133 - entity_parser - INFO - Successfully parsed entity 'Department'
2025-05-13 17:57:34,133 - entity_parser - INFO - Parsing entity: LeaveType
2025-05-13 17:57:34,133 - entity_parser - INFO - Successfully parsed entity 'LeaveType'
2025-05-13 17:57:34,133 - entity_parser - INFO - Parsing entity: LeaveType
2025-05-13 17:57:34,133 - entity_parser - INFO - Successfully parsed entity 'LeaveType'
2025-05-13 17:57:34,133 - entity_parser - INFO - Parsing entity: LeaveType
2025-05-13 17:57:34,134 - entity_parser - INFO - Successfully parsed entity 'LeaveType'
2025-05-13 17:57:34,134 - entity_parser - INFO - Parsing entity: LeaveType
2025-05-13 17:57:34,134 - entity_parser - INFO - Successfully parsed entity 'LeaveType'
2025-05-13 17:57:34,134 - entity_parser - INFO - Parsing entity: LeaveApplication
2025-05-13 17:57:34,134 - entity_parser - INFO - Successfully parsed entity 'LeaveApplication'
2025-05-13 17:57:34,134 - entity_parser - INFO - Parsing entity: LeaveApplication
2025-05-13 17:57:34,134 - entity_parser - INFO - Successfully parsed entity 'LeaveApplication'
2025-05-13 17:57:34,134 - entity_parser - INFO - Parsing entity: LeaveApplication
2025-05-13 17:57:34,134 - entity_parser - INFO - Successfully parsed entity 'LeaveApplication'
2025-05-13 17:57:34,134 - entity_parser - INFO - Parsing entity: CalendarEvent
2025-05-13 17:57:34,134 - entity_parser - INFO - Successfully parsed entity 'CalendarEvent'
2025-05-13 17:57:34,134 - entity_parser - INFO - Parsing entity: CalendarEvent
2025-05-13 17:57:34,134 - entity_parser - INFO - Successfully parsed entity 'CalendarEvent'
2025-05-13 17:57:34,134 - entity_parser - INFO - Parsing entity: CalendarEvent
2025-05-13 17:57:34,134 - entity_parser - INFO - Successfully parsed entity 'CalendarEvent'
2025-05-13 17:57:34,134 - entity_parser - INFO - Successfully parsed 5 entities
2025-05-13 17:57:34,134 - deploy_entities_and_go - INFO - Successfully parsed 5 entities
2025-05-13 17:57:34,134 - deploy_entities_and_go - INFO - - Employee
2025-05-13 17:57:34,134 - deploy_entities_and_go - INFO - - Department
2025-05-13 17:57:34,134 - deploy_entities_and_go - INFO - - LeaveType
2025-05-13 17:57:34,134 - deploy_entities_and_go - INFO - - LeaveApplication
2025-05-13 17:57:34,134 - deploy_entities_and_go - INFO - - CalendarEvent
2025-05-13 17:57:34,134 - entity_deployer_v2 - INFO - Deploying entities to workflow_temp
2025-05-13 17:57:34,153 - entity_deployer_v2 - INFO - Inserted entity 'Employee' into workflow_temp.entities
2025-05-13 17:57:34,192 - entity_deployer_v2 - INFO - Inserted attribute 'employeeId' for entity 'E1' into workflow_temp.entity_attributes
2025-05-13 17:57:34,198 - entity_deployer_v2 - INFO - Inserted attribute metadata for 'employeeId' into workflow_temp.entity_attribute_metadata
2025-05-13 17:57:34,238 - entity_deployer_v2 - INFO - Inserted attribute 'firstName' for entity 'E1' into workflow_temp.entity_attributes
2025-05-13 17:57:34,245 - entity_deployer_v2 - INFO - Inserted attribute metadata for 'firstName' into workflow_temp.entity_attribute_metadata
2025-05-13 17:57:34,288 - entity_deployer_v2 - INFO - Inserted attribute 'lastName' for entity 'E1' into workflow_temp.entity_attributes
2025-05-13 17:57:34,293 - entity_deployer_v2 - INFO - Inserted attribute metadata for 'lastName' into workflow_temp.entity_attribute_metadata
2025-05-13 17:57:34,334 - entity_deployer_v2 - INFO - Inserted attribute 'email' for entity 'E1' into workflow_temp.entity_attributes
2025-05-13 17:57:34,340 - entity_deployer_v2 - INFO - Inserted attribute metadata for 'email' into workflow_temp.entity_attribute_metadata
2025-05-13 17:57:34,352 - entity_deployer_v2 - INFO - Inserted archive strategy for entity 'E1' into workflow_temp.entity_lifecycle_management
2025-05-13 17:57:34,365 - entity_deployer_v2 - INFO - Inserted history tracking for entity 'E1' into workflow_temp.entity_lifecycle_management
2025-05-13 17:57:34,378 - entity_deployer_v2 - INFO - Created table workflow_temp.e1_employee
2025-05-13 17:57:34,396 - entity_deployer_v2 - INFO - Inserted entity 'Department' into workflow_temp.entities
2025-05-13 17:57:34,440 - entity_deployer_v2 - INFO - Inserted attribute 'managerId' for entity 'E2' into workflow_temp.entity_attributes
2025-05-13 17:57:34,447 - entity_deployer_v2 - INFO - Inserted attribute metadata for 'managerId' into workflow_temp.entity_attribute_metadata
2025-05-13 17:57:34,492 - entity_deployer_v2 - INFO - Inserted attribute 'locationId with Employee constrains Location' for entity 'E2' into workflow_temp.entity_attributes
2025-05-13 17:57:34,498 - entity_deployer_v2 - INFO - Inserted attribute metadata for 'locationId with Employee constrains Location' into workflow_temp.entity_attribute_metadata
2025-05-13 17:57:34,511 - entity_deployer_v2 - INFO - Inserted archive strategy for entity 'E2' into workflow_temp.entity_lifecycle_management
2025-05-13 17:57:34,523 - entity_deployer_v2 - INFO - Inserted history tracking for entity 'E2' into workflow_temp.entity_lifecycle_management
2025-05-13 17:57:34,537 - entity_deployer_v2 - INFO - Created table workflow_temp.e2_department
2025-05-13 17:57:34,554 - entity_deployer_v2 - INFO - Inserted entity 'LeaveType' into workflow_temp.entities
2025-05-13 17:57:34,597 - entity_deployer_v2 - INFO - Inserted attribute 'typeId' for entity 'E3' into workflow_temp.entity_attributes
2025-05-13 17:57:34,606 - entity_deployer_v2 - INFO - Inserted attribute metadata for 'typeId' into workflow_temp.entity_attribute_metadata
2025-05-13 17:57:34,651 - entity_deployer_v2 - INFO - Inserted attribute 'name' for entity 'E3' into workflow_temp.entity_attributes
2025-05-13 17:57:34,658 - entity_deployer_v2 - INFO - Inserted attribute metadata for 'name' into workflow_temp.entity_attribute_metadata
2025-05-13 17:57:34,703 - entity_deployer_v2 - INFO - Inserted attribute 'description' for entity 'E3' into workflow_temp.entity_attributes
2025-05-13 17:57:34,709 - entity_deployer_v2 - INFO - Inserted attribute metadata for 'description' into workflow_temp.entity_attribute_metadata
2025-05-13 17:57:34,752 - entity_deployer_v2 - INFO - Inserted attribute 'maxDuration' for entity 'E3' into workflow_temp.entity_attributes
2025-05-13 17:57:34,760 - entity_deployer_v2 - INFO - Inserted attribute metadata for 'maxDuration' into workflow_temp.entity_attribute_metadata
2025-05-13 17:57:34,803 - entity_deployer_v2 - INFO - Inserted attribute 'requiresDocumentation' for entity 'E3' into workflow_temp.entity_attributes
2025-05-13 17:57:34,809 - entity_deployer_v2 - INFO - Inserted attribute metadata for 'requiresDocumentation' into workflow_temp.entity_attribute_metadata
2025-05-13 17:57:34,826 - entity_deployer_v2 - INFO - Created table workflow_temp.e3_leavetype
2025-05-13 17:57:34,845 - entity_deployer_v2 - INFO - Inserted entity 'LeaveApplication' into workflow_temp.entities
2025-05-13 17:57:34,888 - entity_deployer_v2 - INFO - Inserted attribute 'leaveId' for entity 'E4' into workflow_temp.entity_attributes
2025-05-13 17:57:34,895 - entity_deployer_v2 - INFO - Inserted attribute metadata for 'leaveId' into workflow_temp.entity_attribute_metadata
2025-05-13 17:57:34,937 - entity_deployer_v2 - INFO - Inserted attribute 'employeeId' for entity 'E4' into workflow_temp.entity_attributes
2025-05-13 17:57:34,944 - entity_deployer_v2 - INFO - Inserted attribute metadata for 'employeeId' into workflow_temp.entity_attribute_metadata
2025-05-13 17:57:34,985 - entity_deployer_v2 - INFO - Inserted attribute 'leaveTypeId' for entity 'E4' into workflow_temp.entity_attributes
2025-05-13 17:57:34,993 - entity_deployer_v2 - INFO - Inserted attribute metadata for 'leaveTypeId' into workflow_temp.entity_attribute_metadata
2025-05-13 17:57:35,036 - entity_deployer_v2 - INFO - Inserted attribute 'startDate' for entity 'E4' into workflow_temp.entity_attributes
2025-05-13 17:57:35,045 - entity_deployer_v2 - INFO - Inserted attribute metadata for 'startDate' into workflow_temp.entity_attribute_metadata
2025-05-13 17:57:35,087 - entity_deployer_v2 - INFO - Inserted attribute 'endDate' for entity 'E4' into workflow_temp.entity_attributes
2025-05-13 17:57:35,094 - entity_deployer_v2 - INFO - Inserted attribute metadata for 'endDate' into workflow_temp.entity_attribute_metadata
2025-05-13 17:57:35,138 - entity_deployer_v2 - INFO - Inserted attribute 'numDays' for entity 'E4' into workflow_temp.entity_attributes
2025-05-13 17:57:35,145 - entity_deployer_v2 - INFO - Inserted attribute metadata for 'numDays' into workflow_temp.entity_attribute_metadata
2025-05-13 17:57:35,187 - entity_deployer_v2 - INFO - Inserted attribute 'status' for entity 'E4' into workflow_temp.entity_attributes
2025-05-13 17:57:35,194 - entity_deployer_v2 - INFO - Inserted attribute metadata for 'status' into workflow_temp.entity_attribute_metadata
2025-05-13 17:57:35,237 - entity_deployer_v2 - INFO - Inserted attribute 'approvedBy' for entity 'E4' into workflow_temp.entity_attributes
2025-05-13 17:57:35,244 - entity_deployer_v2 - INFO - Inserted attribute metadata for 'approvedBy' into workflow_temp.entity_attribute_metadata
2025-05-13 17:57:35,287 - entity_deployer_v2 - INFO - Inserted attribute 'approvalDate' for entity 'E4' into workflow_temp.entity_attributes
2025-05-13 17:57:35,294 - entity_deployer_v2 - INFO - Inserted attribute metadata for 'approvalDate' into workflow_temp.entity_attribute_metadata
2025-05-13 17:57:35,336 - entity_deployer_v2 - INFO - Inserted attribute 'comments' for entity 'E4' into workflow_temp.entity_attributes
2025-05-13 17:57:35,344 - entity_deployer_v2 - INFO - Inserted attribute metadata for 'comments' into workflow_temp.entity_attribute_metadata
2025-05-13 17:57:35,359 - entity_deployer_v2 - INFO - Created table workflow_temp.e4_leaveapplication
2025-05-13 17:57:35,378 - entity_deployer_v2 - INFO - Inserted entity 'CalendarEvent' into workflow_temp.entities
2025-05-13 17:57:35,419 - entity_deployer_v2 - INFO - Inserted attribute 'eventId' for entity 'E5' into workflow_temp.entity_attributes
2025-05-13 17:57:35,427 - entity_deployer_v2 - INFO - Inserted attribute metadata for 'eventId' into workflow_temp.entity_attribute_metadata
2025-05-13 17:57:35,471 - entity_deployer_v2 - INFO - Inserted attribute 'employeeId' for entity 'E5' into workflow_temp.entity_attributes
2025-05-13 17:57:35,479 - entity_deployer_v2 - INFO - Inserted attribute metadata for 'employeeId' into workflow_temp.entity_attribute_metadata
2025-05-13 17:57:35,520 - entity_deployer_v2 - INFO - Inserted attribute 'eventType' for entity 'E5' into workflow_temp.entity_attributes
2025-05-13 17:57:35,526 - entity_deployer_v2 - INFO - Inserted attribute metadata for 'eventType' into workflow_temp.entity_attribute_metadata
2025-05-13 17:57:35,568 - entity_deployer_v2 - INFO - Inserted attribute 'startDate' for entity 'E5' into workflow_temp.entity_attributes
2025-05-13 17:57:35,576 - entity_deployer_v2 - INFO - Inserted attribute metadata for 'startDate' into workflow_temp.entity_attribute_metadata
2025-05-13 17:57:35,617 - entity_deployer_v2 - INFO - Inserted attribute 'endDate' for entity 'E5' into workflow_temp.entity_attributes
2025-05-13 17:57:35,624 - entity_deployer_v2 - INFO - Inserted attribute metadata for 'endDate' into workflow_temp.entity_attribute_metadata
2025-05-13 17:57:35,665 - entity_deployer_v2 - INFO - Inserted attribute 'title' for entity 'E5' into workflow_temp.entity_attributes
2025-05-13 17:57:35,672 - entity_deployer_v2 - INFO - Inserted attribute metadata for 'title' into workflow_temp.entity_attribute_metadata
2025-05-13 17:57:35,715 - entity_deployer_v2 - INFO - Inserted attribute 'description' for entity 'E5' into workflow_temp.entity_attributes
2025-05-13 17:57:35,722 - entity_deployer_v2 - INFO - Inserted attribute metadata for 'description' into workflow_temp.entity_attribute_metadata
2025-05-13 17:57:35,767 - entity_deployer_v2 - INFO - Inserted attribute 'status' for entity 'E5' into workflow_temp.entity_attributes
2025-05-13 17:57:35,775 - entity_deployer_v2 - INFO - Inserted attribute metadata for 'status' into workflow_temp.entity_attribute_metadata
2025-05-13 17:57:35,820 - entity_deployer_v2 - INFO - Inserted attribute 'referenceId' for entity 'E5' into workflow_temp.entity_attributes
2025-05-13 17:57:35,827 - entity_deployer_v2 - INFO - Inserted attribute metadata for 'referenceId' into workflow_temp.entity_attribute_metadata
2025-05-13 17:57:35,844 - entity_deployer_v2 - INFO - Created table workflow_temp.e5_calendarevent
2025-05-13 17:57:35,852 - entity_deployer_v2 - INFO - Entity deployment completed successfully
2025-05-13 17:57:35,852 - deploy_entities_and_go - INFO - Successfully deployed entity definitions to workflow_temp
2025-05-13 17:57:35,852 - deploy_entities_and_go - INFO - Validating and deploying GO definitions
2025-05-13 17:57:35,853 - go_parser - INFO - Starting to parse GO definitions
2025-05-13 17:57:35,853 - go_parser - INFO - Parsing GO: Leave Approval Process (ID: go001)
2025-05-13 17:57:35,859 - go_parser - INFO - Successfully parsed GO 'Leave Approval Process'
2025-05-13 17:57:35,860 - go_parser - WARNING - Invalid GO header format: GO Relationships:
2025-05-13 17:57:35,860 - go_parser - INFO - Parsing GO: Employee Calendar Management (ID: go004)
2025-05-13 17:57:35,860 - go_parser - INFO - Successfully parsed GO 'Employee Calendar Management'
2025-05-13 17:57:35,861 - go_parser - WARNING - Invalid GO header format: GO Relationships:
2025-05-13 17:57:35,861 - go_parser - INFO - Successfully parsed 2 global objectives
2025-05-13 17:57:35,861 - deploy_entities_and_go - INFO - Successfully parsed 2 global objectives
2025-05-13 17:57:35,861 - deploy_entities_and_go - INFO - - Leave Approval Process
2025-05-13 17:57:35,861 - deploy_entities_and_go - INFO - - Employee Calendar Management
2025-05-13 17:57:35,861 - deploy_entities_and_go - WARNING - GO parsing completed with 2 warnings
2025-05-13 17:57:35,861 - deploy_entities_and_go - WARNING - - Invalid GO header format: GO Relationships:
2025-05-13 17:57:35,861 - deploy_entities_and_go - WARNING - - Invalid GO header format: GO Relationships:
2025-05-13 17:57:35,914 - deploy_entities_and_go - INFO - Registry validation passed
2025-05-13 17:57:35,914 - go_deployer - INFO - Deploying GO definitions to workflow_temp
2025-05-13 17:57:35,966 - go_deployer - WARNING - Attribute 'departmentId' of entity 'Employee' referenced in GO does not exist in the database
2025-05-13 17:57:35,971 - go_deployer - WARNING - Attribute 'managerId' of entity 'Employee' referenced in GO does not exist in the database
2025-05-13 17:57:36,002 - go_deployer - WARNING - Attribute 'false' of entity 'LeaveType' referenced in GO does not exist in the database
2025-05-13 17:57:36,050 - go_deployer - WARNING - Attribute 'Approved' of entity 'LeaveApplication' referenced in GO does not exist in the database
2025-05-13 17:57:36,055 - go_deployer - WARNING - Attribute 'Rejected' of entity 'LeaveApplication' referenced in GO does not exist in the database
2025-05-13 17:57:36,124 - go_deployer - INFO - Inserted GO 'Leave Approval Process' into workflow_temp.global_objectives
2025-05-13 17:57:36,140 - go_deployer - WARNING - Warning: LO 'Employee submits a leave request' (ID: SubmitLeaveRequest) referenced in GO 'go001' does not exist in the database. Skipping mapping.
2025-05-13 17:57:36,146 - go_deployer - WARNING - Warning: LO 'Employee uploads required documentation' (ID: UploadDocumentation) referenced in GO 'go001' does not exist in the database. Skipping mapping.
2025-05-13 17:57:36,151 - go_deployer - WARNING - Warning: LO 'Manager reviews the leave request' (ID: ReviewLeaveRequest) referenced in GO 'go001' does not exist in the database. Skipping mapping.
2025-05-13 17:57:36,155 - go_deployer - WARNING - Warning: LO 'System updates leave request status to approved' (ID: ApproveLeaveRequest) referenced in GO 'go001' does not exist in the database. Skipping mapping.
2025-05-13 17:57:36,161 - go_deployer - WARNING - Warning: LO 'System updates leave request status to rejected' (ID: RejectLeaveRequest) referenced in GO 'go001' does not exist in the database. Skipping mapping.
2025-05-13 17:57:36,166 - go_deployer - WARNING - Warning: LO 'System notifies employee of the decision' (ID: NotifyEmployee) referenced in GO 'go001' does not exist in the database. Skipping mapping.
2025-05-13 17:57:36,170 - go_deployer - WARNING - Warning: LO 'System updates employee calendar' (ID: UpdateCalendar) referenced in GO 'go001' does not exist in the database. Skipping mapping.
2025-05-13 17:57:36,176 - go_deployer - WARNING - Warning: LO 'System updates employee leave balance' (ID: UpdateLeaveBalance) referenced in GO 'go001' does not exist in the database. Skipping mapping.
2025-05-13 17:57:36,181 - go_deployer - WARNING - Warning: LO 'Employee cancels a previously submitted leave request' (ID: CancelLeaveRequest) referenced in GO 'go001' does not exist in the database. Skipping mapping.
2025-05-13 17:57:36,186 - go_deployer - WARNING - Warning: LO 'System rolls back leave approval' (ID: RollbackLeaveApproval) referenced in GO 'go001' does not exist in the database. Skipping mapping.
2025-05-13 17:57:36,191 - go_deployer - WARNING - Warning: LO 'System restores employee leave balance' (ID: RestoreLeaveBalance) referenced in GO 'go001' does not exist in the database. Skipping mapping.
2025-05-13 17:57:36,196 - go_deployer - WARNING - Warning: LO 'System notifies manager of cancellation' (ID: NotifyCancellation) referenced in GO 'go001' does not exist in the database. Skipping mapping.
2025-05-13 17:57:36,201 - go_deployer - WARNING - Warning: LO 'System logs audit trail for compliance' (ID: LogAuditTrail) referenced in GO 'go001' does not exist in the database. Skipping mapping.
2025-05-13 17:57:36,219 - go_deployer - WARNING - Referenced GO 'Employee Calendar Management' does not exist. Skipping mapping creation.
2025-05-13 17:57:36,224 - go_deployer - WARNING - Referenced GO 'Employee Calendar Management' does not exist. Skipping mapping creation.
2025-05-13 17:57:36,230 - go_deployer - WARNING - Referenced GO 'Employee Calendar Management' does not exist. Skipping mapping creation.
2025-05-13 17:57:36,236 - go_deployer - WARNING - Referenced GO 'Employee Calendar Management' does not exist. Skipping mapping creation.
2025-05-13 17:57:36,280 - go_deployer - WARNING - Attribute 'Rejected' of entity 'LeaveApplication' referenced in GO does not exist in the database
2025-05-13 17:57:36,343 - go_deployer - WARNING - Attribute 'Cancelled' of entity 'CalendarEvent' referenced in GO does not exist in the database
2025-05-13 17:57:36,388 - go_deployer - INFO - Inserted GO 'Employee Calendar Management' into workflow_temp.global_objectives
2025-05-13 17:57:36,409 - go_deployer - WARNING - Warning: LO 'System creates a calendar event' (ID: CreateCalendarEvent) referenced in GO 'go004' does not exist in the database. Skipping mapping.
2025-05-13 17:57:36,417 - go_deployer - WARNING - Warning: LO 'System notifies employee of the calendar event' (ID: NotifyEmployee) referenced in GO 'go004' does not exist in the database. Skipping mapping.
2025-05-13 17:57:36,424 - go_deployer - WARNING - Warning: LO 'System cancels a calendar event' (ID: CancelCalendarEvent) referenced in GO 'go004' does not exist in the database. Skipping mapping.
2025-05-13 17:57:36,429 - go_deployer - WARNING - Warning: LO 'System notifies employee of the cancellation' (ID: NotifyCancellation) referenced in GO 'go004' does not exist in the database. Skipping mapping.
2025-05-13 17:57:36,486 - db_utils - ERROR - Error executing query: duplicate key value violates unique constraint "output_items_pkey"
DETAIL:  Key (id, output_stack_id)=(go004.O1, 1185) already exists.
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 83, in execute_query
    cursor.execute(query, params)
psycopg2.errors.UniqueViolation: duplicate key value violates unique constraint "output_items_pkey"
DETAIL:  Key (id, output_stack_id)=(go004.O1, 1185) already exists.

2025-05-13 17:57:36,487 - deploy_entities_and_go - ERROR - GO deployment failed with 36 messages
2025-05-13 17:57:36,487 - deploy_entities_and_go - ERROR - - Attribute 'departmentId' of entity 'Employee' referenced in GO does not exist in the database
2025-05-13 17:57:36,487 - deploy_entities_and_go - ERROR - - Attribute 'managerId' of entity 'Employee' referenced in GO does not exist in the database
2025-05-13 17:57:36,487 - deploy_entities_and_go - ERROR - - Attribute 'false' of entity 'LeaveType' referenced in GO does not exist in the database
2025-05-13 17:57:36,487 - deploy_entities_and_go - ERROR - - Attribute 'Approved' of entity 'LeaveApplication' referenced in GO does not exist in the database
2025-05-13 17:57:36,487 - deploy_entities_and_go - ERROR - - Attribute 'Rejected' of entity 'LeaveApplication' referenced in GO does not exist in the database
2025-05-13 17:57:36,487 - deploy_entities_and_go - ERROR - - Created tenant 'Acme Corporation' with ID 't_c19ea536'
2025-05-13 17:57:36,487 - deploy_entities_and_go - ERROR - - Created book 'Employee Leave Management' with ID 'b_0da724a4'
2025-05-13 17:57:36,487 - deploy_entities_and_go - ERROR - - Created chapter 'Leave Request Lifecycle' with ID 'c_99ab2a74'
2025-05-13 17:57:36,487 - deploy_entities_and_go - ERROR - - Inserted GO 'Leave Approval Process' into workflow_temp.global_objectives
2025-05-13 17:57:36,487 - deploy_entities_and_go - ERROR - - Warning: LO 'Employee submits a leave request' (ID: SubmitLeaveRequest) referenced in GO 'go001' does not exist in the database. Skipping mapping.
2025-05-13 17:57:36,487 - deploy_entities_and_go - ERROR - - Warning: LO 'Employee uploads required documentation' (ID: UploadDocumentation) referenced in GO 'go001' does not exist in the database. Skipping mapping.
2025-05-13 17:57:36,487 - deploy_entities_and_go - ERROR - - Warning: LO 'Manager reviews the leave request' (ID: ReviewLeaveRequest) referenced in GO 'go001' does not exist in the database. Skipping mapping.
2025-05-13 17:57:36,487 - deploy_entities_and_go - ERROR - - Warning: LO 'System updates leave request status to approved' (ID: ApproveLeaveRequest) referenced in GO 'go001' does not exist in the database. Skipping mapping.
2025-05-13 17:57:36,487 - deploy_entities_and_go - ERROR - - Warning: LO 'System updates leave request status to rejected' (ID: RejectLeaveRequest) referenced in GO 'go001' does not exist in the database. Skipping mapping.
2025-05-13 17:57:36,487 - deploy_entities_and_go - ERROR - - Warning: LO 'System notifies employee of the decision' (ID: NotifyEmployee) referenced in GO 'go001' does not exist in the database. Skipping mapping.
2025-05-13 17:57:36,487 - deploy_entities_and_go - ERROR - - Warning: LO 'System updates employee calendar' (ID: UpdateCalendar) referenced in GO 'go001' does not exist in the database. Skipping mapping.
2025-05-13 17:57:36,487 - deploy_entities_and_go - ERROR - - Warning: LO 'System updates employee leave balance' (ID: UpdateLeaveBalance) referenced in GO 'go001' does not exist in the database. Skipping mapping.
2025-05-13 17:57:36,487 - deploy_entities_and_go - ERROR - - Warning: LO 'Employee cancels a previously submitted leave request' (ID: CancelLeaveRequest) referenced in GO 'go001' does not exist in the database. Skipping mapping.
2025-05-13 17:57:36,487 - deploy_entities_and_go - ERROR - - Warning: LO 'System rolls back leave approval' (ID: RollbackLeaveApproval) referenced in GO 'go001' does not exist in the database. Skipping mapping.
2025-05-13 17:57:36,487 - deploy_entities_and_go - ERROR - - Warning: LO 'System restores employee leave balance' (ID: RestoreLeaveBalance) referenced in GO 'go001' does not exist in the database. Skipping mapping.
2025-05-13 17:57:36,487 - deploy_entities_and_go - ERROR - - Warning: LO 'System notifies manager of cancellation' (ID: NotifyCancellation) referenced in GO 'go001' does not exist in the database. Skipping mapping.
2025-05-13 17:57:36,487 - deploy_entities_and_go - ERROR - - Warning: LO 'System logs audit trail for compliance' (ID: LogAuditTrail) referenced in GO 'go001' does not exist in the database. Skipping mapping.
2025-05-13 17:57:36,487 - deploy_entities_and_go - ERROR - - Referenced GO 'Employee Calendar Management' does not exist. Skipping mapping creation.
2025-05-13 17:57:36,487 - deploy_entities_and_go - ERROR - - Referenced GO 'Employee Calendar Management' does not exist. Skipping mapping creation.
2025-05-13 17:57:36,487 - deploy_entities_and_go - ERROR - - Referenced GO 'Employee Calendar Management' does not exist. Skipping mapping creation.
2025-05-13 17:57:36,487 - deploy_entities_and_go - ERROR - - Referenced GO 'Employee Calendar Management' does not exist. Skipping mapping creation.
2025-05-13 17:57:36,487 - deploy_entities_and_go - ERROR - - Attribute 'Rejected' of entity 'LeaveApplication' referenced in GO does not exist in the database
2025-05-13 17:57:36,487 - deploy_entities_and_go - ERROR - - Attribute 'Cancelled' of entity 'CalendarEvent' referenced in GO does not exist in the database
2025-05-13 17:57:36,487 - deploy_entities_and_go - ERROR - - Created chapter 'Calendar Management' with ID 'c_28f4b534'
2025-05-13 17:57:36,487 - deploy_entities_and_go - ERROR - - Inserted GO 'Employee Calendar Management' into workflow_temp.global_objectives
2025-05-13 17:57:36,487 - deploy_entities_and_go - ERROR - - Warning: LO 'System creates a calendar event' (ID: CreateCalendarEvent) referenced in GO 'go004' does not exist in the database. Skipping mapping.
2025-05-13 17:57:36,487 - deploy_entities_and_go - ERROR - - Warning: LO 'System notifies employee of the calendar event' (ID: NotifyEmployee) referenced in GO 'go004' does not exist in the database. Skipping mapping.
2025-05-13 17:57:36,487 - deploy_entities_and_go - ERROR - - Warning: LO 'System cancels a calendar event' (ID: CancelCalendarEvent) referenced in GO 'go004' does not exist in the database. Skipping mapping.
2025-05-13 17:57:36,487 - deploy_entities_and_go - ERROR - - Warning: LO 'System notifies employee of the cancellation' (ID: NotifyCancellation) referenced in GO 'go004' does not exist in the database. Skipping mapping.
2025-05-13 17:57:36,487 - deploy_entities_and_go - ERROR - - Created GO-to-GO mapping from 'go004' to 'go001'
2025-05-13 17:57:36,487 - deploy_entities_and_go - ERROR - - Error executing query: duplicate key value violates unique constraint "output_items_pkey"
DETAIL:  Key (id, output_stack_id)=(go004.O1, 1185) already exists.

2025-05-13 17:57:36,487 - deploy_entities_and_go - ERROR - GO validation and deployment failed


=== Command completed with exit code: 0 ===

