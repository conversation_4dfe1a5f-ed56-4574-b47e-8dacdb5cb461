"""
Query entity relationships from the database.
"""

from db_utils import execute_query

def main():
    # Query entity_relationships table
    print("Querying workflow_temp.entity_relationships table")
    success, messages, relationships = execute_query(
        "SELECT source_entity_id, target_entity_id, relationship_type, source_attribute_id, target_attribute_id FROM workflow_temp.entity_relationships",
        schema_name="workflow_temp"
    )
    
    if success and relationships:
        print("Entity Relationships:")
        for relationship in relationships:
            print(f"  {relationship}")
    else:
        print("No entity relationships found or query failed")
        if messages:
            print("Messages:")
            for message in messages:
                print(f"  {message}")

if __name__ == "__main__":
    main()
