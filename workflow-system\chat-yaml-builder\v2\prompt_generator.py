"""
Prompt Generator for YAML Builder v2

This module provides functionality for generating prompts with system-specific information
such as available system functions, entities, attributes, and more.
"""

import os
import sys
import logging
from typing import Dict, List, Tuple, Any, Optional

# Add parent directory to path to import common modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from system_function_validator import SystemFunctionValidator
from db_utils import execute_query

# Set up logging
logger = logging.getLogger('prompt_generator')

class PromptGenerator:
    """
    Generates prompts with system-specific information such as available system functions,
    entities, attributes, and more.
    """
    
    def __init__(self, system_functions_path: str = None, schema_name: str = "workflow_runtime"):
        """
        Initialize the prompt generator.
        
        Args:
            system_functions_path: Path to the system_functions.py file
            schema_name: The database schema name to query for existing components
        """
        self.system_function_validator = SystemFunctionValidator(system_functions_path)
        self.schema_name = schema_name
        
    def generate_prompt(self, component_type: str) -> str:
        """
        Generate a prompt for a specific component type.
        
        Args:
            component_type: The type of component ('roles', 'entities', 'go_definitions', 'lo_definitions')
            
        Returns:
            Generated prompt text
        """
        # Read the base prompt template
        prompt_template = self._read_prompt_template(component_type)
        
        # Add system functions information
        prompt = self._add_system_functions_info(prompt_template)
        
        # Add existing components information based on component type
        if component_type == 'entities':
            entities_info = self._get_existing_entities_info()
            prompt = self._add_existing_entities_info(prompt, entities_info)
        elif component_type == 'go_definitions':
            go_info = self._get_existing_go_info()
            entities_info = self._get_existing_entities_info()
            prompt = self._add_existing_go_info(prompt, go_info, entities_info)
        elif component_type == 'lo_definitions':
            lo_info = self._get_existing_lo_info()
            go_info = self._get_existing_go_info()
            entities_info = self._get_existing_entities_info()
            prompt = self._add_existing_lo_info(prompt, lo_info, go_info, entities_info)
        elif component_type == 'roles':
            roles_info = self._get_existing_roles_info()
            go_info = self._get_existing_go_info()
            lo_info = self._get_existing_lo_info()
            entities_info = self._get_existing_entities_info()
            prompt = self._add_existing_roles_info(prompt, roles_info, go_info, lo_info, entities_info)
        
        # Add validation instructions
        prompt = self._add_validation_instructions(prompt, component_type)
        
        return prompt
    
    def _read_prompt_template(self, component_type: str) -> str:
        """
        Read the prompt template for a specific component type.
        
        Args:
            component_type: The type of component ('roles', 'entities', 'go_definitions', 'lo_definitions')
            
        Returns:
            Prompt template text
        """
        # Use the correct path to the prompts directory
        prompts_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'prompts')
        template_path = os.path.join(prompts_dir, f'{component_type}_prompt.txt')
        
        try:
            with open(template_path, 'r') as f:
                return f.read()
        except Exception as e:
            logger.error(f"Error reading prompt template: {str(e)}", exc_info=True)
            return ""
    
    def _add_system_functions_info(self, prompt_text: str) -> str:
        """
        Add system functions information to the prompt.
        
        Args:
            prompt_text: The original prompt text
            
        Returns:
            Enhanced prompt text
        """
        # Check if system functions section already exists
        if "## System Functions Validation" in prompt_text:
            return prompt_text
        
        # Get system functions information
        system_functions_info = self._get_system_functions_info()
        
        # Create a section for system functions
        system_functions_section = "\n## System Functions Validation\n\n"
        system_functions_section += "## IMPORTANT: Only use the system functions listed below in your definitions. Using undefined functions will cause runtime errors.\n\n"
        
        for category in system_functions_info["categories"]:
            system_functions_section += f"### {category.capitalize()} Functions\n"
            
            for function_name in system_functions_info["functions_by_category"][category]:
                system_functions_section += f"- **{function_name}**: "
                
                # Add parameter information if available
                params = self.system_function_validator.get_function_parameters(function_name)
                if params:
                    system_functions_section += f"Parameters: {', '.join(params)}"
                
                system_functions_section += "\n"
            
            system_functions_section += "\n"
        
        # Find the best place to insert the system functions section
        # Look for a section like "## Common System Functions" or "## Common UI Controls"
        import re
        common_system_functions_match = re.search(r'(## Common System Functions)', prompt_text)
        common_ui_controls_match = re.search(r'(## Common UI Controls)', prompt_text)
        
        if common_system_functions_match:
            # Replace the Common System Functions section
            start = common_system_functions_match.start()
            end = common_ui_controls_match.start() if common_ui_controls_match else len(prompt_text)
            
            enhanced_prompt = prompt_text[:start] + system_functions_section + prompt_text[end:]
        elif common_ui_controls_match:
            # Insert before the Common UI Controls section
            enhanced_prompt = prompt_text[:common_ui_controls_match.start()] + system_functions_section + prompt_text[common_ui_controls_match.start():]
        else:
            # Append to the end of the prompt
            enhanced_prompt = prompt_text + "\n\n" + system_functions_section
        
        return enhanced_prompt
    
    def _get_system_functions_info(self) -> Dict:
        """
        Get information about available system functions.
        
        Returns:
            Dictionary containing system functions information
        """
        system_functions_info = {
            "categories": self.system_function_validator.get_all_categories(),
            "functions_by_category": {}
        }
        
        for category in system_functions_info["categories"]:
            system_functions_info["functions_by_category"][category] = self.system_function_validator.get_functions_by_category(category)
        
        return system_functions_info
    
    def _get_existing_entities_info(self) -> Dict:
        """
        Get information about existing entities in the database.
        
        Returns:
            Dictionary containing entities information
        """
        entities_info = {
            "entities": {},
            "attributes": {}
        }
        
        try:
            # Query entities
            success, query_messages, result = execute_query(
                f"SELECT entity_id, name, description FROM {self.schema_name}.entities",
                schema_name=self.schema_name
            )
            
            if success and result:
                for row in result:
                    entity_id, name, description = row
                    entities_info["entities"][entity_id] = {
                        "name": name,
                        "description": description,
                        "attributes": []
                    }
            
            # Query attributes
            success, query_messages, result = execute_query(
                f"""
                SELECT a.attribute_id, a.entity_id, a.name, a.type, a.required, a.calculated_field
                FROM {self.schema_name}.entity_attributes a
                JOIN {self.schema_name}.entities e ON a.entity_id = e.entity_id
                """,
                schema_name=self.schema_name
            )
            
            if success and result:
                for row in result:
                    attribute_id, entity_id, name, attr_type, required, calculated = row
                    
                    if entity_id in entities_info["entities"]:
                        entities_info["entities"][entity_id]["attributes"].append(attribute_id)
                    
                    entities_info["attributes"][attribute_id] = {
                        "entity_id": entity_id,
                        "name": name,
                        "type": attr_type,
                        "required": required,
                        "calculated": calculated
                    }
        except Exception as e:
            logger.error(f"Error getting entities information: {str(e)}", exc_info=True)
        
        return entities_info
    
    def _add_existing_entities_info(self, prompt_text: str, entities_info: Dict) -> str:
        """
        Add existing entities information to the prompt.
        
        Args:
            prompt_text: The original prompt text
            entities_info: Dictionary containing entities information
            
        Returns:
            Enhanced prompt text
        """
        # Check if entities section already exists
        if "## Existing Entities" in prompt_text:
            return prompt_text
        
        # Create a section for existing entities
        entities_section = "\n## Existing Entities\n\n"
        entities_section += "The following entities already exist in the system. When possible, reference these entities and their attributes:\n\n"
        
        for entity_id, entity_data in entities_info["entities"].items():
            entities_section += f"### {entity_data['name']} ({entity_id})\n"
            
            if entity_data["description"]:
                entities_section += f"Description: {entity_data['description']}\n\n"
            
            entities_section += "Attributes:\n"
            
            for attribute_id in entity_data["attributes"]:
                if attribute_id in entities_info["attributes"]:
                    attr_data = entities_info["attributes"][attribute_id]
                    required_str = "required" if attr_data["required"] else "optional"
                    calculated_str = ", calculated" if attr_data["calculated"] else ""
                    
                    entities_section += f"- **{attr_data['name']}** ({attribute_id}): {attr_data['type']}, {required_str}{calculated_str}\n"
            
            entities_section += "\n"
        
        # Add a note about using existing entities
        entities_section += "**IMPORTANT**: When defining new components, reference existing entities and attributes whenever possible. If you need to create a new entity or modify an existing one, clearly indicate this in your definition.\n\n"
        
        # Find the best place to insert the entities section
        # Look for the system functions section or a section like "## Common UI Controls"
        import re
        system_functions_match = re.search(r'(## System Functions Validation)', prompt_text)
        common_ui_controls_match = re.search(r'(## Common UI Controls)', prompt_text)
        
        if system_functions_match:
            # Find the end of the system functions section
            system_functions_end = common_ui_controls_match.start() if common_ui_controls_match else len(prompt_text)
            
            # Insert after the system functions section
            enhanced_prompt = prompt_text[:system_functions_end] + "\n\n" + entities_section + prompt_text[system_functions_end:]
        elif common_ui_controls_match:
            # Insert before the Common UI Controls section
            enhanced_prompt = prompt_text[:common_ui_controls_match.start()] + entities_section + prompt_text[common_ui_controls_match.start():]
        else:
            # Append to the end of the prompt
            enhanced_prompt = prompt_text + "\n\n" + entities_section
        
        return enhanced_prompt
    
    def _get_existing_go_info(self) -> Dict:
        """
        Get information about existing Global Objectives (GOs) in the database.
        
        Returns:
            Dictionary containing GO information
        """
        go_info = {
            "global_objectives": {}
        }
        
        try:
            # Query global objectives
            success, query_messages, result = execute_query(
                f"SELECT go_id, name, description FROM {self.schema_name}.global_objectives",
                schema_name=self.schema_name
            )
            
            if success and result:
                for row in result:
                    go_id, name, description = row
                    go_info["global_objectives"][go_id] = {
                        "name": name,
                        "description": description,
                        "local_objectives": []
                    }
            
            # Query GO-LO mappings
            success, query_messages, result = execute_query(
                f"""
                SELECT m.go_id, m.lo_id
                FROM {self.schema_name}.go_lo_mapping m
                JOIN {self.schema_name}.global_objectives g ON m.go_id = g.go_id
                JOIN {self.schema_name}.local_objectives l ON m.lo_id = l.lo_id
                """,
                schema_name=self.schema_name
            )
            
            if success and result:
                for row in result:
                    go_id, lo_id = row
                    
                    if go_id in go_info["global_objectives"]:
                        go_info["global_objectives"][go_id]["local_objectives"].append(lo_id)
        except Exception as e:
            logger.error(f"Error getting GO information: {str(e)}", exc_info=True)
        
        return go_info
    
    def _add_existing_go_info(self, prompt_text: str, go_info: Dict, entities_info: Dict) -> str:
        """
        Add existing Global Objectives (GOs) information to the prompt.
        
        Args:
            prompt_text: The original prompt text
            go_info: Dictionary containing GO information
            entities_info: Dictionary containing entities information
            
        Returns:
            Enhanced prompt text
        """
        # Check if GO section already exists
        if "## Existing Global Objectives" in prompt_text:
            return prompt_text
        
        # Create a section for existing GOs
        go_section = "\n## Existing Global Objectives\n\n"
        go_section += "The following Global Objectives (GOs) already exist in the system. When possible, reference these GOs:\n\n"
        
        for go_id, go_data in go_info["global_objectives"].items():
            go_section += f"### {go_data['name']} ({go_id})\n"
            
            if go_data["description"]:
                go_section += f"Description: {go_data['description']}\n\n"
            
            if go_data["local_objectives"]:
                go_section += "Local Objectives:\n"
                
                for lo_id in go_data["local_objectives"]:
                    go_section += f"- {lo_id}\n"
                
                go_section += "\n"
        
        # Add a note about using existing GOs
        go_section += "**IMPORTANT**: When defining new Global Objectives, consider how they relate to existing GOs and entities. If you need to modify an existing GO, clearly indicate this in your definition.\n\n"
        
        # Find the best place to insert the GO section
        # Look for the entities section or system functions section
        import re
        entities_match = re.search(r'(## Existing Entities)', prompt_text)
        system_functions_match = re.search(r'(## System Functions Validation)', prompt_text)
        common_ui_controls_match = re.search(r'(## Common UI Controls)', prompt_text)
        
        if entities_match:
            # Find the end of the entities section
            entities_end = common_ui_controls_match.start() if common_ui_controls_match else len(prompt_text)
            
            # Insert after the entities section
            enhanced_prompt = prompt_text[:entities_end] + "\n\n" + go_section + prompt_text[entities_end:]
        elif system_functions_match:
            # Find the end of the system functions section
            system_functions_end = common_ui_controls_match.start() if common_ui_controls_match else len(prompt_text)
            
            # Insert after the system functions section
            enhanced_prompt = prompt_text[:system_functions_end] + "\n\n" + go_section + prompt_text[system_functions_end:]
        else:
            # Append to the end of the prompt
            enhanced_prompt = prompt_text + "\n\n" + go_section
        
        return enhanced_prompt
    
    def _get_existing_lo_info(self) -> Dict:
        """
        Get information about existing Local Objectives (LOs) in the database.
        
        Returns:
            Dictionary containing LO information
        """
        lo_info = {
            "local_objectives": {}
        }
        
        try:
            # Query local objectives
            success, query_messages, result = execute_query(
                f"SELECT lo_id, go_id, name, description FROM {self.schema_name}.local_objectives",
                schema_name=self.schema_name
            )
            
            if success and result:
                for row in result:
                    lo_id, go_id, name, description = row
                    lo_info["local_objectives"][lo_id] = {
                        "name": name,
                        "description": description,
                        "go_id": go_id,
                        "input_items": [],
                        "output_items": []
                    }
            
            # Query LO input items
            success, query_messages, result = execute_query(
                f"""
                SELECT i.item_id, i.lo_id, i.name, i.type, i.required
                FROM {self.schema_name}.lo_input_items i
                JOIN {self.schema_name}.local_objectives l ON i.lo_id = l.lo_id
                """,
                schema_name=self.schema_name
            )
            
            if success and result:
                for row in result:
                    item_id, lo_id, name, item_type, required = row
                    
                    if lo_id in lo_info["local_objectives"]:
                        lo_info["local_objectives"][lo_id]["input_items"].append({
                            "item_id": item_id,
                            "name": name,
                            "type": item_type,
                            "required": required
                        })
            
            # Query LO output items
            success, query_messages, result = execute_query(
                f"""
                SELECT o.item_id, o.lo_id, o.name, o.type
                FROM {self.schema_name}.lo_output_items o
                JOIN {self.schema_name}.local_objectives l ON o.lo_id = l.lo_id
                """,
                schema_name=self.schema_name
            )
            
            if success and result:
                for row in result:
                    item_id, lo_id, name, item_type = row
                    
                    if lo_id in lo_info["local_objectives"]:
                        lo_info["local_objectives"][lo_id]["output_items"].append({
                            "item_id": item_id,
                            "name": name,
                            "type": item_type
                        })
        except Exception as e:
            logger.error(f"Error getting LO information: {str(e)}", exc_info=True)
        
        return lo_info
    
    def _add_existing_lo_info(self, prompt_text: str, lo_info: Dict, go_info: Dict, entities_info: Dict) -> str:
        """
        Add existing Local Objectives (LOs) information to the prompt.
        
        Args:
            prompt_text: The original prompt text
            lo_info: Dictionary containing LO information
            go_info: Dictionary containing GO information
            entities_info: Dictionary containing entities information
            
        Returns:
            Enhanced prompt text
        """
        # Check if LO section already exists
        if "## Existing Local Objectives" in prompt_text:
            return prompt_text
        
        # Create a section for existing LOs
        lo_section = "\n## Existing Local Objectives\n\n"
        lo_section += "The following Local Objectives (LOs) already exist in the system. When possible, reference these LOs:\n\n"
        
        for lo_id, lo_data in lo_info["local_objectives"].items():
            lo_section += f"### {lo_data['name']} ({lo_id})\n"
            
            if lo_data["description"]:
                lo_section += f"Description: {lo_data['description']}\n\n"
            
            if lo_data["go_id"]:
                go_name = go_info["global_objectives"].get(lo_data["go_id"], {}).get("name", lo_data["go_id"])
                lo_section += f"Global Objective: {go_name} ({lo_data['go_id']})\n\n"
            
            if lo_data["input_items"]:
                lo_section += "Input Items:\n"
                
                for item in lo_data["input_items"]:
                    required_str = "required" if item["required"] else "optional"
                    lo_section += f"- **{item['name']}** ({item['item_id']}): {item['type']}, {required_str}\n"
                
                lo_section += "\n"
            
            if lo_data["output_items"]:
                lo_section += "Output Items:\n"
                
                for item in lo_data["output_items"]:
                    lo_section += f"- **{item['name']}** ({item['item_id']}): {item['type']}\n"
                
                lo_section += "\n"
        
        # Add a note about using existing LOs
        lo_section += "**IMPORTANT**: When defining new Local Objectives, consider how they relate to existing LOs, GOs, and entities. If you need to modify an existing LO, clearly indicate this in your definition.\n\n"
        lo_section += "**IMPORTANT**: Even small changes to an LO require creating a new LO, not modifying an existing one.\n\n"
        
        # Find the best place to insert the LO section
        # Look for the GO section or entities section
        import re
        go_match = re.search(r'(## Existing Global Objectives)', prompt_text)
        entities_match = re.search(r'(## Existing Entities)', prompt_text)
        common_ui_controls_match = re.search(r'(## Common UI Controls)', prompt_text)
        
        if go_match:
            # Find the end of the GO section
            go_end = common_ui_controls_match.start() if common_ui_controls_match else len(prompt_text)
            
            # Insert after the GO section
            enhanced_prompt = prompt_text[:go_end] + "\n\n" + lo_section + prompt_text[go_end:]
        elif entities_match:
            # Find the end of the entities section
            entities_end = common_ui_controls_match.start() if common_ui_controls_match else len(prompt_text)
            
            # Insert after the entities section
            enhanced_prompt = prompt_text[:entities_end] + "\n\n" + lo_section + prompt_text[entities_end:]
        else:
            # Append to the end of the prompt
            enhanced_prompt = prompt_text + "\n\n" + lo_section
        
        return enhanced_prompt
    
    def _get_existing_roles_info(self) -> Dict:
        """
        Get information about existing roles in the database.
        
        Returns:
            Dictionary containing roles information
        """
        roles_info = {
            "roles": {}
        }
        
        try:
            # Query roles
            success, query_messages, result = execute_query(
                f"SELECT role_id, name, description FROM {self.schema_name}.roles",
                schema_name=self.schema_name
            )
            
            if success and result:
                for row in result:
                    role_id, name, description = row
                    roles_info["roles"][role_id] = {
                        "name": name,
                        "description": description,
                        "permissions": []
                    }
            
            # Query role permissions
            success, query_messages, result = execute_query(
                f"""
                SELECT p.role_id, p.permission_id, p.permission_name, p.resource_id
                FROM {self.schema_name}.role_permissions p
                JOIN {self.schema_name}.roles r ON p.role_id = r.role_id
                """,
                schema_name=self.schema_name
            )
            
            if success and result:
                for row in result:
                    role_id, permission_id, permission_name, resource_id = row
                    
                    if role_id in roles_info["roles"]:
                        roles_info["roles"][role_id]["permissions"].append({
                            "permission_id": permission_id,
                            "name": permission_name,
                            "resource_id": resource_id
                        })
        except Exception as e:
            logger.error(f"Error getting roles information: {str(e)}", exc_info=True)
        
        return roles_info
    
    def _add_existing_roles_info(self, prompt_text: str, roles_info: Dict, go_info: Dict, lo_info: Dict, entities_info: Dict) -> str:
        """
        Add existing roles information to the prompt.
        
        Args:
            prompt_text: The original prompt text
            roles_info: Dictionary containing roles information
            go_info: Dictionary containing GO information
            lo_info: Dictionary containing LO information
            entities_info: Dictionary containing entities information
            
        Returns:
            Enhanced prompt text
        """
        # Check if roles section already exists
        if "## Existing Roles" in prompt_text:
            return prompt_text
        
        # Create a section for existing roles
        roles_section = "\n## Existing Roles\n\n"
        roles_section += "The following roles already exist in the system. When possible, reference these roles:\n\n"
        
        for role_id, role_data in roles_info["roles"].items():
            roles_section += f"### {role_data['name']} ({role_id})\n"
            
            if role_data["description"]:
                roles_section += f"Description: {role_data['description']}\n\n"
            
            if role_data["permissions"]:
                roles_section += "Permissions:\n"
                
                for permission in role_data["permissions"]:
                    resource_type = "Unknown"
                    resource_name = permission["resource_id"]
                    
                    # Try to determine resource type and name
                    if permission["resource_id"].startswith("go"):
                        resource_type = "Global Objective"
                        resource_name = go_info["global_objectives"].get(permission["resource_id"], {}).get("name", permission["resource_id"])
                    elif permission["resource_id"].startswith("lo"):
                        resource_type = "Local Objective"
                        resource_name = lo_info["local_objectives"].get(permission["resource_id"], {}).get("name", permission["resource_id"])
                    elif permission["resource_id"].startswith("entity"):
                        resource_type = "Entity"
                        resource_name = entities_info["entities"].get(permission["resource_id"], {}).get("name", permission["resource_id"])
                    
                    roles_section += f"- **{permission['name']}** on {resource_type} {resource_name} ({permission['resource_id']})\n"
                
                roles_section += "\n"
        
        # Add a note about using existing roles
        roles_section += "**IMPORTANT**: When defining new roles, consider how they relate to existing roles, GOs, LOs, and entities. If you need to modify an existing role, clearly indicate this in your definition.\n\n"
        
        # Find the best place to insert the roles section
        # Look for the LO section or GO section
        import re
        lo_match = re.search(r'(## Existing Local Objectives)', prompt_text)
        go_match = re.search(r'(## Existing Global Objectives)', prompt_text)
        common_ui_controls_match = re.search(r'(## Common UI Controls)', prompt_text)
        
        if lo_match:
            # Find the end of the LO section
            lo_end = common_ui_controls_match.start() if common_ui_controls_match else len(prompt_text)
            
            # Insert after the LO section
            enhanced_prompt = prompt_text[:lo_end] + "\n\n" + roles_section + prompt_text[lo_end:]
        elif go_match:
            # Find the end of the GO section
            go_end = common_ui_controls_match.start() if common_ui_controls_match else len(prompt_text)
            
            # Insert after the GO section
            enhanced_prompt = prompt_text[:go_end] + "\n\n" + roles_section + prompt_text[go_end:]
        else:
            # Append to the end of the prompt
            enhanced_prompt = prompt_text + "\n\n" + roles_section
        
        return enhanced_prompt
    
    def _add_validation_instructions(self, prompt_text: str, component_type: str) -> str:
        """
        Add validation instructions to the prompt.
        
        Args:
            prompt_text: The original prompt text
            component_type: The type of component ('roles', 'entities', 'go_definitions', 'lo_definitions')
            
        Returns:
            Enhanced prompt text
        """
        # Check if validation section already exists
        if "## Validation Requirements" in prompt_text:
            return prompt_text
        
        # Create a section for validation instructions
        validation_section = "\n## Validation Requirements\n\n"
        validation_section += "Your output will be validated against the following requirements:\n\n"
        
        # Add general validation requirements
        validation_section += "### General Requirements\n"
        validation_section += "- All system functions used must be from the list of available system functions provided above.\n"
        validation_section += "- All references to entities, attributes, GOs, LOs, and roles must be to existing components or clearly marked as new.\n"
        validation_section += "- All IDs must follow the correct format for their component type.\n\n"
        
        # Add component-specific validation requirements
        if component_type == 'entities':
            validation_section += "### Entity-Specific Requirements\n"
            validation_section += "- Entity IDs must be in the format 'entity_[name]' (e.g., 'entity_employee').\n"
            validation_section += "- Attribute IDs must be in the format '[entity_id]_attr_[name]' (e.g., 'entity_employee_attr_name').\n"
            validation_section += "- If modifying an existing entity, clearly indicate which attributes are new or modified.\n\n"
        elif component_type == 'go_definitions':
            validation_section += "### GO-Specific Requirements\n"
            validation_section += "- GO IDs must be in the format 'goXXX' where XXX is a number (e.g., 'go001').\n"
            validation_section += "- All entity references must be to existing entities.\n"
            validation_section += "- If modifying an existing GO, clearly indicate which parts are new or modified.\n\n"
        elif component_type == 'lo_definitions':
            validation_section += "### LO-Specific Requirements\n"
            validation_section += "- LO IDs must be in the format 'loXXX' where XXX is a number (e.g., 'lo001').\n"
            validation_section += "- All GO references must be to existing GOs.\n"
            validation_section += "- All entity and attribute references must be to existing entities and attributes.\n"
            validation_section += "- All system functions used must be from the list of available system functions.\n"
            validation_section += "- If referencing another LO, it must be an existing LO.\n"
            validation_section += "- Even small changes to an LO require creating a new LO, not modifying an existing one.\n\n"
        elif component_type == 'roles':
            validation_section += "### Role-Specific Requirements\n"
            validation_section += "- Role IDs must be in the format 'roleXXX' where XXX is a number (e.g., 'role001').\n"
            validation_section += "- All GO, LO, and entity references must be to existing components.\n"
            validation_section += "- If modifying an existing role, clearly indicate which parts are new or modified.\n\n"
        
        # Find the best place to insert the validation section
        # Look for the Common UI Controls section or the end of the prompt
        import re
        common_ui_controls_match = re.search(r'(## Common UI Controls)', prompt_text)
        
        if common_ui_controls_match:
            # Find the end of the Common UI Controls section
            common_ui_controls_end = prompt_text.find("\n\n## ", common_ui_controls_match.end())
            if common_ui_controls_end == -1:
                common_ui_controls_end = len(prompt_text)
            
            # Insert after the Common UI Controls section
            enhanced_prompt = prompt_text[:common_ui_controls_end] + "\n\n" + validation_section + prompt_text[common_ui_controls_end:]
        else:
            # Append to the end of the prompt
            enhanced_prompt = prompt_text + "\n\n" + validation_section
        
        return enhanced_prompt
