"""
Output Validator for YAML Builder v2

This module provides functionality for validating the output of the ChatGPT API
to ensure it uses only valid system functions and references existing entities,
attributes, LOs, and GOs.
"""

import os
import sys
import re
import logging
from typing import Dict, List, Tuple, Any, Optional, Set

# Add parent directory to path to import common modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from system_function_validator import SystemFunctionValidator
from db_utils import execute_query

# Set up logging
logger = logging.getLogger('output_validator')

class OutputValidator:
    """
    Validates the output of the ChatGPT API to ensure it uses only valid system functions
    and references existing entities, attributes, LOs, and GOs.
    """
    
    def __init__(self, system_functions_path: str = None, schema_name: str = "workflow_runtime"):
        """
        Initialize the output validator.
        
        Args:
            system_functions_path: Path to the system_functions.py file
            schema_name: The database schema name to query for existing components
        """
        self.system_function_validator = SystemFunctionValidator(system_functions_path)
        self.schema_name = schema_name
        self.entities_info = self._get_existing_entities_info()
        self.go_info = self._get_existing_go_info()
        self.lo_info = self._get_existing_lo_info()
        self.roles_info = self._get_existing_roles_info()
        
    def validate_output(self, output_text: str, component_type: str) -> Tuple[bool, List[str]]:
        """
        Validate the output of the ChatGPT API.
        
        Args:
            output_text: The output text to validate
            component_type: The type of component ('roles', 'entities', 'go_definitions', 'lo_definitions')
            
        Returns:
            Tuple containing:
                - Boolean indicating if validation passed
                - List of error messages (empty if validation passed)
        """
        errors = []
        
        # Validate system functions
        system_function_errors = self._validate_system_functions(output_text)
        errors.extend(system_function_errors)
        
        # Validate component-specific references
        if component_type == 'entities':
            entity_errors = self._validate_entity_references(output_text)
            errors.extend(entity_errors)
        elif component_type == 'go_definitions':
            go_errors = self._validate_go_references(output_text)
            errors.extend(go_errors)
        elif component_type == 'lo_definitions':
            lo_errors = self._validate_lo_references(output_text)
            errors.extend(lo_errors)
        elif component_type == 'roles':
            role_errors = self._validate_role_references(output_text)
            errors.extend(role_errors)
        
        return len(errors) == 0, errors
    
    def _validate_system_functions(self, output_text: str) -> List[str]:
        """
        Validate system functions used in the output.
        
        Args:
            output_text: The output text to validate
            
        Returns:
            List of error messages (empty if validation passed)
        """
        errors = []
        
        # Extract system function calls
        # Look for patterns like "System generates X using function_name with parameters"
        # or "using function_name from Y"
        function_patterns = [
            r'using\s+([a-zA-Z0-9_]+)\s+with',
            r'using\s+([a-zA-Z0-9_]+)\s+from',
            r'using\s+([a-zA-Z0-9_]+)\s*\.',
            r'using\s+([a-zA-Z0-9_]+)\s*$'
        ]
        
        for pattern in function_patterns:
            function_matches = re.findall(pattern, output_text)
            
            for function_name in function_matches:
                is_valid, message = self.system_function_validator.validate_function_name(function_name)
                
                if not is_valid:
                    errors.append(f"Invalid system function: {function_name}. {message}")
        
        return errors
    
    def _validate_entity_references(self, output_text: str) -> List[str]:
        """
        Validate entity references in the output.
        
        Args:
            output_text: The output text to validate
            
        Returns:
            List of error messages (empty if validation passed)
        """
        errors = []
        
        # Extract entity references
        # Look for patterns like "Entity1 with attribute1"
        entity_pattern = r'([A-Za-z0-9_]+)\s+with\s+'
        entity_matches = re.findall(entity_pattern, output_text)
        
        for entity_name in entity_matches:
            # Check if entity exists
            entity_exists = False
            
            for entity_id, entity_data in self.entities_info["entities"].items():
                if entity_data["name"].lower() == entity_name.lower():
                    entity_exists = True
                    break
            
            if not entity_exists and not entity_name.endswith("_new"):
                errors.append(f"Entity '{entity_name}' does not exist and is not marked as new.")
        
        # Extract attribute references
        # Look for patterns like "Entity.attribute"
        attribute_pattern = r'([A-Za-z0-9_]+)\.([A-Za-z0-9_]+)'
        attribute_matches = re.findall(attribute_pattern, output_text)
        
        for entity_name, attribute_name in attribute_matches:
            # Skip if entity is new
            if entity_name.endswith("_new"):
                continue
            
            # Check if entity exists
            entity_exists = False
            entity_id = None
            
            for eid, entity_data in self.entities_info["entities"].items():
                if entity_data["name"].lower() == entity_name.lower():
                    entity_exists = True
                    entity_id = eid
                    break
            
            if not entity_exists:
                errors.append(f"Entity '{entity_name}' referenced by attribute '{attribute_name}' does not exist.")
                continue
            
            # Check if attribute exists
            attribute_exists = False
            
            for attribute_id, attribute_data in self.entities_info["attributes"].items():
                if attribute_data["entity_id"] == entity_id and attribute_data["name"].lower() == attribute_name.lower():
                    attribute_exists = True
                    break
            
            if not attribute_exists and not attribute_name.endswith("_new"):
                errors.append(f"Attribute '{attribute_name}' of entity '{entity_name}' does not exist and is not marked as new.")
        
        return errors
    
    def _validate_go_references(self, output_text: str) -> List[str]:
        """
        Validate GO references in the output.
        
        Args:
            output_text: The output text to validate
            
        Returns:
            List of error messages (empty if validation passed)
        """
        errors = []
        
        # Extract GO references
        # Look for patterns like "go_id: "goXXX""
        go_pattern = r'go_id:\s*"([^"]+)"'
        go_matches = re.findall(go_pattern, output_text)
        
        for go_id in go_matches:
            # Check if GO exists
            if go_id not in self.go_info["global_objectives"] and not go_id.endswith("_new"):
                errors.append(f"GO '{go_id}' does not exist and is not marked as new.")
        
        # Validate entity references
        entity_errors = self._validate_entity_references(output_text)
        errors.extend(entity_errors)
        
        return errors
    
    def _validate_lo_references(self, output_text: str) -> List[str]:
        """
        Validate LO references in the output.
        
        Args:
            output_text: The output text to validate
            
        Returns:
            List of error messages (empty if validation passed)
        """
        errors = []
        
        # Extract LO references
        # Look for patterns like "id: "loXXX""
        lo_pattern = r'id:\s*"([^"]+)"'
        lo_matches = re.findall(lo_pattern, output_text)
        
        for lo_id in lo_matches:
            # Check if LO exists
            if lo_id in self.lo_info["local_objectives"]:
                errors.append(f"LO '{lo_id}' already exists. Even small changes to an LO require creating a new LO, not modifying an existing one.")
        
        # Extract GO references
        # Look for patterns like "contextual_id: "goXXX.loXXX""
        go_pattern = r'contextual_id:\s*"([^.]+)\.[^"]+"'
        go_matches = re.findall(go_pattern, output_text)
        
        for go_id in go_matches:
            # Check if GO exists
            if go_id not in self.go_info["global_objectives"] and not go_id.endswith("_new"):
                errors.append(f"GO '{go_id}' referenced in contextual_id does not exist and is not marked as new.")
        
        # Extract route references
        # Look for patterns like "route to LoName"
        route_pattern = r'route\s+to\s+([A-Za-z0-9_]+)'
        route_matches = re.findall(route_pattern, output_text)
        
        for lo_name in route_matches:
            # Check if LO exists
            lo_exists = False
            
            for lo_id, lo_data in self.lo_info["local_objectives"].items():
                if lo_data["name"].lower() == lo_name.lower():
                    lo_exists = True
                    break
            
            if not lo_exists and not lo_name.endswith("_new"):
                errors.append(f"LO '{lo_name}' referenced in route does not exist and is not marked as new.")
        
        # Validate entity references
        entity_errors = self._validate_entity_references(output_text)
        errors.extend(entity_errors)
        
        return errors
    
    def _validate_role_references(self, output_text: str) -> List[str]:
        """
        Validate role references in the output.
        
        Args:
            output_text: The output text to validate
            
        Returns:
            List of error messages (empty if validation passed)
        """
        errors = []
        
        # Extract role references
        # Look for patterns like "id: "roleXXX""
        role_pattern = r'id:\s*"([^"]+)"'
        role_matches = re.findall(role_pattern, output_text)
        
        for role_id in role_matches:
            # Check if role exists
            if role_id in self.roles_info["roles"] and not role_id.endswith("_new"):
                # It's okay to modify existing roles, so we don't add an error here
                pass
        
        # Extract GO references
        # Look for patterns like "go_id: "goXXX""
        go_pattern = r'go_id:\s*"([^"]+)"'
        go_matches = re.findall(go_pattern, output_text)
        
        for go_id in go_matches:
            # Check if GO exists
            if go_id not in self.go_info["global_objectives"] and not go_id.endswith("_new"):
                errors.append(f"GO '{go_id}' does not exist and is not marked as new.")
        
        # Extract LO references
        # Look for patterns like "lo_id: "loXXX""
        lo_pattern = r'lo_id:\s*"([^"]+)"'
        lo_matches = re.findall(lo_pattern, output_text)
        
        for lo_id in lo_matches:
            # Check if LO exists
            if lo_id not in self.lo_info["local_objectives"] and not lo_id.endswith("_new"):
                errors.append(f"LO '{lo_id}' does not exist and is not marked as new.")
        
        # Validate entity references
        entity_errors = self._validate_entity_references(output_text)
        errors.extend(entity_errors)
        
        return errors
    
    def _get_existing_entities_info(self) -> Dict:
        """
        Get information about existing entities in the database.
        
        Returns:
            Dictionary containing entities information
        """
        entities_info = {
            "entities": {},
            "attributes": {}
        }
        
        try:
            # Query entities
            success, query_messages, result = execute_query(
                f"SELECT entity_id, name, description FROM {self.schema_name}.entities",
                schema_name=self.schema_name
            )
            
            if success and result:
                for row in result:
                    entity_id, name, description = row
                    entities_info["entities"][entity_id] = {
                        "name": name,
                        "description": description,
                        "attributes": []
                    }
            
            # Query attributes
            success, query_messages, result = execute_query(
                f"""
                SELECT a.attribute_id, a.entity_id, a.name, a.type, a.required, a.calculated_field
                FROM {self.schema_name}.entity_attributes a
                JOIN {self.schema_name}.entities e ON a.entity_id = e.entity_id
                """,
                schema_name=self.schema_name
            )
            
            if success and result:
                for row in result:
                    attribute_id, entity_id, name, attr_type, required, calculated = row
                    
                    if entity_id in entities_info["entities"]:
                        entities_info["entities"][entity_id]["attributes"].append(attribute_id)
                    
                    entities_info["attributes"][attribute_id] = {
                        "entity_id": entity_id,
                        "name": name,
                        "type": attr_type,
                        "required": required,
                        "calculated": calculated
                    }
        except Exception as e:
            logger.error(f"Error getting entities information: {str(e)}", exc_info=True)
        
        return entities_info
    
    def _get_existing_go_info(self) -> Dict:
        """
        Get information about existing Global Objectives (GOs) in the database.
        
        Returns:
            Dictionary containing GO information
        """
        go_info = {
            "global_objectives": {}
        }
        
        try:
            # Query global objectives
            success, query_messages, result = execute_query(
                f"SELECT go_id, name, description FROM {self.schema_name}.global_objectives",
                schema_name=self.schema_name
            )
            
            if success and result:
                for row in result:
                    go_id, name, description = row
                    go_info["global_objectives"][go_id] = {
                        "name": name,
                        "description": description,
                        "local_objectives": []
                    }
            
            # Query GO-LO mappings
            success, query_messages, result = execute_query(
                f"""
                SELECT m.go_id, m.lo_id
                FROM {self.schema_name}.go_lo_mapping m
                JOIN {self.schema_name}.global_objectives g ON m.go_id = g.go_id
                JOIN {self.schema_name}.local_objectives l ON m.lo_id = l.lo_id
                """,
                schema_name=self.schema_name
            )
            
            if success and result:
                for row in result:
                    go_id, lo_id = row
                    
                    if go_id in go_info["global_objectives"]:
                        go_info["global_objectives"][go_id]["local_objectives"].append(lo_id)
        except Exception as e:
            logger.error(f"Error getting GO information: {str(e)}", exc_info=True)
        
        return go_info
    
    def _get_existing_lo_info(self) -> Dict:
        """
        Get information about existing Local Objectives (LOs) in the database.
        
        Returns:
            Dictionary containing LO information
        """
        lo_info = {
            "local_objectives": {}
        }
        
        try:
            # Query local objectives
            success, query_messages, result = execute_query(
                f"SELECT lo_id, go_id, name, description FROM {self.schema_name}.local_objectives",
                schema_name=self.schema_name
            )
            
            if success and result:
                for row in result:
                    lo_id, go_id, name, description = row
                    lo_info["local_objectives"][lo_id] = {
                        "name": name,
                        "description": description,
                        "go_id": go_id,
                        "input_items": [],
                        "output_items": []
                    }
            
            # Query LO input items
            success, query_messages, result = execute_query(
                f"""
                SELECT i.item_id, i.lo_id, i.name, i.type, i.required
                FROM {self.schema_name}.lo_input_items i
                JOIN {self.schema_name}.local_objectives l ON i.lo_id = l.lo_id
                """,
                schema_name=self.schema_name
            )
            
            if success and result:
                for row in result:
                    item_id, lo_id, name, item_type, required = row
                    
                    if lo_id in lo_info["local_objectives"]:
                        lo_info["local_objectives"][lo_id]["input_items"].append({
                            "item_id": item_id,
                            "name": name,
                            "type": item_type,
                            "required": required
                        })
            
            # Query LO output items
            success, query_messages, result = execute_query(
                f"""
                SELECT o.item_id, o.lo_id, o.name, o.type
                FROM {self.schema_name}.lo_output_items o
                JOIN {self.schema_name}.local_objectives l ON o.lo_id = l.lo_id
                """,
                schema_name=self.schema_name
            )
            
            if success and result:
                for row in result:
                    item_id, lo_id, name, item_type = row
                    
                    if lo_id in lo_info["local_objectives"]:
                        lo_info["local_objectives"][lo_id]["output_items"].append({
                            "item_id": item_id,
                            "name": name,
                            "type": item_type
                        })
        except Exception as e:
            logger.error(f"Error getting LO information: {str(e)}", exc_info=True)
        
        return lo_info
    
    def _get_existing_roles_info(self) -> Dict:
        """
        Get information about existing roles in the database.
        
        Returns:
            Dictionary containing roles information
        """
        roles_info = {
            "roles": {}
        }
        
        try:
            # Query roles
            success, query_messages, result = execute_query(
                f"SELECT role_id, name, description FROM {self.schema_name}.roles",
                schema_name=self.schema_name
            )
            
            if success and result:
                for row in result:
                    role_id, name, description = row
                    roles_info["roles"][role_id] = {
                        "name": name,
                        "description": description,
                        "permissions": []
                    }
            
            # Query role permissions
            success, query_messages, result = execute_query(
                f"""
                SELECT p.role_id, p.permission_id, p.permission_name, p.resource_id
                FROM {self.schema_name}.role_permissions p
                JOIN {self.schema_name}.roles r ON p.role_id = r.role_id
                """,
                schema_name=self.schema_name
            )
            
            if success and result:
                for row in result:
                    role_id, permission_id, permission_name, resource_id = row
                    
                    if role_id in roles_info["roles"]:
                        roles_info["roles"][role_id]["permissions"].append({
                            "permission_id": permission_id,
                            "name": permission_name,
                            "resource_id": resource_id
                        })
        except Exception as e:
            logger.error(f"Error getting roles information: {str(e)}", exc_info=True)
        
        return roles_info


# Example usage
if __name__ == "__main__":
    validator = OutputValidator()
    
    # Example validation of LO output
    lo_output = """
    ## ApplyForLeave

    id: "lo001"
    contextual_id: "go001.lo001"
    name: "Apply for Leave"
    version: "1.0"
    status: "Active"
    workflow_source: "origin"
    function_type: "Create"

    *Employee has execution rights*

    *Inputs: LeaveApplication with leaveID*, employeeID*, startDate*, endDate*, numDays*, reason*, leaveType* (Sick Leave, Annual Leave, Parental Leave, Bereavement), leaveSubType [depends on: leaveType], status* (Pending, Approved, Rejected), instructions [info]*
    * System generates LeaveApplication.leaveID using generate_id with prefix "LV".
    * System calculates LeaveApplication.numDays using subtract_days with startDate and endDate.
    * System defaults LeaveApplication.status to "Pending".
    * System populates LeaveApplication.leaveSubType [depends on: leaveType] using fetch_filtered_records from LeaveSubType where LeaveSubType.leaveType = LeaveApplication.leaveType and LeaveSubType.active = true.
    * System loads LeaveApplication.instructions [info] with constant "leave_application_instructions".

    *Outputs: LeaveApplication with leaveID, employeeID, startDate, endDate, numDays, reason, leaveType, leaveSubType, status*
    * System returns LeaveApplication.leaveID for reference in notifications.
    * System captures LeaveApplication.submissionDate using current_timestamp for audit trails.
    * System transforms LeaveApplication.leaveType for display using format_enum_value.
    """
    
    is_valid, errors = validator.validate_output(lo_output, 'lo_definitions')
    print(f"LO validation {'passed' if is_valid else 'failed'}")
    if not is_valid:
        for error in errors:
            print(f"- {error}")
