"""
Check the schema of the tables in the workflow_temp schema.
"""

import os
import sys
import logging

# Add parent directory to path to import common modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from db_utils import execute_query

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('check_schema')

def check_schema():
    """
    Check the schema of the tables in the workflow_temp schema.
    """
    schema_name = "workflow_temp"
    
    # Check if global_objectives table exists
    success, _, result = execute_query(
        f"""
        SELECT EXISTS (
            SELECT FROM information_schema.tables 
            WHERE table_schema = %s
            AND table_name = 'global_objectives'
        )
        """,
        (schema_name,)
    )
    
    if success and result and result[0][0]:
        logger.info("global_objectives table exists")
    else:
        logger.warning("global_objectives table does not exist")
    
    # Check if local_objectives table exists
    success, _, result = execute_query(
        f"""
        SELECT EXISTS (
            SELECT FROM information_schema.tables 
            WHERE table_schema = %s
            AND table_name = 'local_objectives'
        )
        """,
        (schema_name,)
    )
    
    if success and result and result[0][0]:
        logger.info("local_objectives table exists")
    else:
        logger.warning("local_objectives table does not exist")
    
    # Check the schema of the global_objectives table
    success, _, result = execute_query(
        f"""
        SELECT column_name, data_type, is_nullable
        FROM information_schema.columns
        WHERE table_schema = %s
        AND table_name = 'global_objectives'
        ORDER BY ordinal_position
        """,
        (schema_name,)
    )
    
    if success and result:
        logger.info("global_objectives table schema:")
        for column in result:
            logger.info(f"  {column[0]}: {column[1]} (nullable: {column[2]})")
    
    # Check the schema of the local_objectives table
    success, _, result = execute_query(
        f"""
        SELECT column_name, data_type, is_nullable
        FROM information_schema.columns
        WHERE table_schema = %s
        AND table_name = 'local_objectives'
        ORDER BY ordinal_position
        """,
        (schema_name,)
    )
    
    if success and result:
        logger.info("local_objectives table schema:")
        for column in result:
            logger.info(f"  {column[0]}: {column[1]} (nullable: {column[2]})")
    
    # Check the foreign key constraint on local_objectives
    success, _, result = execute_query(
        f"""
        SELECT
            tc.constraint_name,
            tc.table_name,
            kcu.column_name,
            ccu.table_name AS foreign_table_name,
            ccu.column_name AS foreign_column_name
        FROM
            information_schema.table_constraints AS tc
            JOIN information_schema.key_column_usage AS kcu
              ON tc.constraint_name = kcu.constraint_name
              AND tc.table_schema = kcu.table_schema
            JOIN information_schema.constraint_column_usage AS ccu
              ON ccu.constraint_name = tc.constraint_name
              AND ccu.table_schema = tc.table_schema
        WHERE tc.constraint_type = 'FOREIGN KEY'
        AND tc.table_schema = %s
        AND tc.table_name = 'local_objectives'
        """,
        (schema_name,)
    )
    
    if success and result:
        logger.info("local_objectives foreign key constraints:")
        for constraint in result:
            logger.info(f"  {constraint[0]}: {constraint[1]}.{constraint[2]} -> {constraint[3]}.{constraint[4]}")
    
    # Check if the GO exists in the global_objectives table
    success, _, result = execute_query(
        f"""
        SELECT go_id, name
        FROM {schema_name}.global_objectives
        WHERE go_id = 'go_employeeonboarding'
        """,
        schema_name=schema_name
    )
    
    if success and result:
        logger.info(f"GO 'go_employeeonboarding' exists in the database: {result}")
    else:
        logger.warning(f"GO 'go_employeeonboarding' does not exist in the database")
        
        # Try to find the GO with a different ID
        success, _, result = execute_query(
            f"""
            SELECT go_id, name
            FROM {schema_name}.global_objectives
            """,
            schema_name=schema_name
        )
        
        if success and result:
            logger.info(f"GOs in the database: {result}")
        else:
            logger.warning(f"No GOs found in the database")
    
    # Check if the GO exists in the public schema
    success, _, result = execute_query(
        """
        SELECT go_id, name
        FROM public.global_objectives
        WHERE go_id = 'go_employeeonboarding'
        """
    )
    
    if success and result:
        logger.info(f"GO 'go_employeeonboarding' exists in the public schema: {result}")
    else:
        logger.warning(f"GO 'go_employeeonboarding' does not exist in the public schema")
        
        # Try to find the GO with a different ID
        success, _, result = execute_query(
            """
            SELECT go_id, name
            FROM public.global_objectives
            """
        )
        
        if success and result:
            logger.info(f"GOs in the public schema: {result}")
        else:
            logger.warning(f"No GOs found in the public schema")
    
    # Check all schemas in the database
    success, _, result = execute_query(
        """
        SELECT schema_name
        FROM information_schema.schemata
        WHERE schema_name NOT IN ('information_schema', 'pg_catalog', 'pg_toast')
        """
    )
    
    if success and result:
        logger.info(f"Schemas in the database: {result}")
        
        # Check if the GO exists in any schema
        for schema in result:
            schema_name = schema[0]
            success, _, result = execute_query(
                f"""
                SELECT EXISTS (
                    SELECT FROM information_schema.tables 
                    WHERE table_schema = %s
                    AND table_name = 'global_objectives'
                )
                """,
                (schema_name,)
            )
            
            if success and result and result[0][0]:
                logger.info(f"global_objectives table exists in schema {schema_name}")
                
                # Check if the GO exists in this schema
                success, _, result = execute_query(
                    f"""
                    SELECT go_id, name
                    FROM {schema_name}.global_objectives
                    WHERE go_id = 'go_employeeonboarding'
                    """,
                    schema_name=schema_name
                )
                
                if success and result:
                    logger.info(f"GO 'go_employeeonboarding' exists in schema {schema_name}: {result}")
                else:
                    logger.warning(f"GO 'go_employeeonboarding' does not exist in schema {schema_name}")

if __name__ == "__main__":
    check_schema()
