import os
import sys
import logging
from pathlib import Path

# Add parent directory to path
current_dir = Path(__file__).parent
parent_dir = current_dir.parent
sys.path.append(str(parent_dir))

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("remove_columns.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger("remove_columns")

from db_utils import execute_query

def remove_unnecessary_columns():
    """
    Remove unnecessary columns from the entities table.
    """
    logger.info("Removing unnecessary columns from entities table")
    
    # Read SQL file
    sql_file_path = os.path.join(current_dir, "sql", "remove_unnecessary_columns.sql")
    with open(sql_file_path, "r") as f:
        sql = f.read()
    
    # Execute SQL
    success, messages, _ = execute_query(sql)
    
    if success:
        logger.info("Successfully removed unnecessary columns")
        return True, messages
    else:
        logger.error(f"Failed to remove unnecessary columns: {messages}")
        return False, messages

if __name__ == "__main__":
    success, messages = remove_unnecessary_columns()
    
    if success:
        print("Successfully removed unnecessary columns")
    else:
        print(f"Failed to remove unnecessary columns: {messages}")