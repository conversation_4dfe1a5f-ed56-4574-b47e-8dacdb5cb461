-- Part 1.3: Data Migration

-- Populate users as entities
-- First, create a user entity type if it doesn't exist
INSERT INTO workflow_runtime.entities (entity_id, name, version, status, type, entity_class, is_system_entity)
VALUES ('user_entity', 'User', '1.0', 'ACTIVE', 'REGULAR', 'system', TRUE)
ON CONFLICT (entity_id) DO NOTHING;

-- Update existing entities with new columns
UPDATE workflow_runtime.entities
SET 
    entity_class = CASE 
        WHEN type = 'REGULAR' THEN 'business'
        WHEN type = 'SHARED' THEN 'system'
        ELSE 'business'
    END,
    is_system_entity = CASE 
        WHEN entity_id IN ('user_entity', 'role_entity', 'permission_entity', 'org_unit_entity') THEN TRUE
        ELSE FALSE
    END
WHERE entity_class IS NULL;

-- Create default organizational structure
INSERT INTO workflow_runtime.organizational_units (org_unit_id, name, tenant_id, description)
VALUES 
    ('org_root', 'Organization Root', 
     (SELECT tenant_id FROM workflow_runtime.tenants LIMIT 1), 
     'Root organizational unit')
ON CONFLICT (org_unit_id) DO NOTHING;

-- Create default departments
INSERT INTO workflow_runtime.organizational_units (org_unit_id, name, parent_org_unit_id, tenant_id, description)
VALUES 
    ('org_it', 'IT Department', 'org_root', 
     (SELECT tenant_id FROM workflow_runtime.tenants LIMIT 1), 
     'IT Department'),
    ('org_hr', 'HR Department', 'org_root', 
     (SELECT tenant_id FROM workflow_runtime.tenants LIMIT 1), 
     'Human Resources Department'),
    ('org_finance', 'Finance Department', 'org_root', 
     (SELECT tenant_id FROM workflow_runtime.tenants LIMIT 1), 
     'Finance Department')
ON CONFLICT (org_unit_id) DO NOTHING;

-- Create default permission contexts
INSERT INTO workflow_runtime.permission_contexts (context_id, name, context_type, description)
VALUES
    ('ctx_global_admin', 'Global Administrator', 'global', 'Full system access'),
    ('ctx_entity_read', 'Entity Read Access', 'entity', 'Read access to entities'),
    ('ctx_entity_write', 'Entity Write Access', 'entity', 'Write access to entities'),
    ('ctx_entity_delete', 'Entity Delete Access', 'entity', 'Delete access to entities'),
    ('ctx_workflow_execute', 'Workflow Execution', 'objective', 'Permission to execute workflows'),
    ('ctx_workflow_manage', 'Workflow Management', 'objective', 'Permission to manage workflows'),
    ('ctx_function_execute', 'Function Execution', 'function', 'Permission to execute functions')
ON CONFLICT (context_id) DO NOTHING;

-- Create default permission types if they don't exist
INSERT INTO workflow_runtime.permission_types (permission_id, description)
VALUES
    ('read', 'Read access'),
    ('write', 'Write access'),
    ('delete', 'Delete access'),
    ('execute', 'Execute access'),
    ('manage', 'Management access'),
    ('admin', 'Administrative access')
ON CONFLICT (permission_id) DO NOTHING;

-- Create default roles if they don't exist
INSERT INTO workflow_runtime.roles (role_id, name, tenant_id)
VALUES
    ('role_admin', 'Administrator', 
     (SELECT tenant_id FROM workflow_runtime.tenants LIMIT 1)),
    ('role_manager', 'Manager', 
     (SELECT tenant_id FROM workflow_runtime.tenants LIMIT 1)),
    ('role_user', 'User', 
     (SELECT tenant_id FROM workflow_runtime.tenants LIMIT 1)),
    ('role_guest', 'Guest', 
     (SELECT tenant_id FROM workflow_runtime.tenants LIMIT 1))
ON CONFLICT (role_id) DO NOTHING;

-- Link roles to permission contexts
INSERT INTO workflow_runtime.role_permissions (role_id, context_id)
VALUES
    ('role_admin', 'ctx_global_admin'),
    ('role_admin', 'ctx_entity_read'),
    ('role_admin', 'ctx_entity_write'),
    ('role_admin', 'ctx_entity_delete'),
    ('role_admin', 'ctx_workflow_execute'),
    ('role_admin', 'ctx_workflow_manage'),
    ('role_admin', 'ctx_function_execute'),
    
    ('role_manager', 'ctx_entity_read'),
    ('role_manager', 'ctx_entity_write'),
    ('role_manager', 'ctx_workflow_execute'),
    ('role_manager', 'ctx_workflow_manage'),
    
    ('role_user', 'ctx_entity_read'),
    ('role_user', 'ctx_workflow_execute'),
    
    ('role_guest', 'ctx_entity_read')
ON CONFLICT DO NOTHING;

-- Create a default admin user
INSERT INTO workflow_runtime.users (
    user_id, username, email, password_hash, 
    first_name, last_name, status
)
VALUES (
    'admin_user',
    'admin',
    '<EMAIL>',
    -- This is a bcrypt hash for 'admin123' - in production, generate this properly
    '$2b$12$tPzABVWPgHCYwlB7zQDxNO6BYtARUXcTjWRlIHHPsOjCXUGvNNPcW',
    'System',
    'Administrator',
    'active'
)
ON CONFLICT (user_id) DO NOTHING;

-- Assign admin role to admin user
INSERT INTO workflow_runtime.user_roles (user_id, username, role, tenant_id)
VALUES (
    'admin_user',
    'admin',
    'Administrator',
    (SELECT tenant_id FROM workflow_runtime.tenants LIMIT 1)
)
ON CONFLICT (user_id) DO NOTHING;

-- Assign admin user to root organizational unit
INSERT INTO workflow_runtime.user_organizations (user_id, org_unit_id, is_primary)
VALUES (
    'admin_user',
    'org_root',
    TRUE
)
ON CONFLICT DO NOTHING;

-- Map existing roles to new permission contexts
-- This is a placeholder - in a real migration, you would map existing roles to new contexts
-- based on your specific requirements
