from fastapi import APIRouter, Depends, HTTPException, Path, Query, Body
from typing import List, Dict, Any, Optional
from app.services.function_repository import function_repository  # ✅ Use function_repository
from app.services import core_functions as base

router = APIRouter()

@router.get("/{entity}/{record_id}")
async def get_entity_record(
    entity: str = Path(..., description="Entity name"),
    record_id: str = Path(..., description="Record ID"),
    attributes: List[str] = Query(None, description="Specific attributes to return")
):
    """
    Get a record from a specific entity.
    """
    try:
        result = function_repository.execute("database", "fetch", entity, {"id": record_id})
        if attributes:
            result = {key: result[key] for key in attributes if key in result}
        return result
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error retrieving entity record: {str(e)}")

@router.post("/{entity}")
async def create_entity_record(
    entity: str = Path(..., description="Entity name"),
    attributes: Dict[str, Any] = Body(..., description="Record attributes"),
    user_id: str = Body(..., description="User ID creating the record")
):
    """
    Create a new record in a specific entity.
    """
    try:
        record_id = function_repository.execute("database", "create", entity, attributes)
        return {"id": record_id, "message": f"Record created successfully in {entity}"}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error creating entity record: {str(e)}")

@router.put("/{entity}/{record_id}")
async def update_entity_record(
    entity: str = Path(..., description="Entity name"),
    record_id: str = Path(..., description="Record ID"),
    attributes: Dict[str, Any] = Body(..., description="Record attributes to update"),
    user_id: str = Body(..., description="User ID updating the record")
):
    """
    Update a record in a specific entity.
    """
    try:
        updated_data = function_repository.execute("database", "update", entity, {"id": record_id}, attributes)
        return {"message": f"Record {record_id} updated successfully in {entity}", "data": updated_data}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error updating entity record: {str(e)}")

@router.delete("/{entity}/{record_id}")
async def delete_entity_record(
    entity: str = Path(..., description="Entity name"),
    record_id: str = Path(..., description="Record ID"),
    user_id: str = Body(..., description="User ID deleting the record")
):
    """
    Delete a record from a specific entity.
    """
    try:
        function_repository.execute("database", "delete", entity, {"id": record_id})
        return {"message": f"Record {record_id} deleted successfully from {entity}"}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error deleting entity record: {str(e)}")

@router.get("/{entity}")
async def query_entity_records(
    entity: str = Path(..., description="Entity name"),
    limit: int = Query(10, description="Maximum number of records to return"),
    skip: int = Query(0, description="Number of records to skip"),
    sort_by: str = Query(None, description="Field to sort by"),
    sort_order: int = Query(1, description="Sort order (1 for ascending, -1 for descending)")
):
    """
    Query records from a specific entity.
    """
    try:
        sort = {sort_by: sort_order} if sort_by else None
        results = function_repository.execute("database", "fetch_records", entity, {}, limit, skip, sort_by)
        return results
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error querying entity records: {str(e)}")
