#!/usr/bin/env python3
"""
Script to synchronize the workflow_temp schema with the workflow_runtime schema.
"""

import os
import sys
import logging
import argparse
from typing import Dict, List, Tuple, Any

# Add parent directory to path to import common modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from common.db_connection import get_db_connection
from db_utils import execute_query

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger('sync_temp_schema')

def get_table_schema(schema_name: str, table_name: str) -> List[Tuple[str, str, str, str]]:
    """
    Get the schema definition for a table.
    
    Args:
        schema_name: Schema name
        table_name: Table name
        
    Returns:
        List of tuples containing column name, data type, column default, and udt_name
    """
    query = """
        SELECT column_name, data_type, column_default, udt_name
        FROM information_schema.columns
        WHERE table_schema = %s
        AND table_name = %s
        ORDER BY ordinal_position
    """
    
    success, messages, result = execute_query(query, (schema_name, table_name))
    
    if not success or not result:
        logger.error(f"Failed to get schema for {schema_name}.{table_name}: {messages}")
        return []
    
    return result

def get_primary_key(schema_name: str, table_name: str) -> str:
    """
    Get the primary key column for a table.
    
    Args:
        schema_name: Schema name
        table_name: Table name
        
    Returns:
        Primary key column name
    """
    query = """
        SELECT a.attname
        FROM pg_index i
        JOIN pg_attribute a ON a.attrelid = i.indrelid AND a.attnum = ANY(i.indkey)
        WHERE i.indrelid = %s::regclass
        AND i.indisprimary
    """
    
    success, messages, result = execute_query(query, (f"{schema_name}.{table_name}",))
    
    if not success or not result:
        logger.error(f"Failed to get primary key for {schema_name}.{table_name}: {messages}")
        return ""
    
    return result[0][0]

def get_foreign_keys(schema_name: str, table_name: str) -> List[Tuple[str, str, str]]:
    """
    Get the foreign key constraints for a table.
    
    Args:
        schema_name: Schema name
        table_name: Table name
        
    Returns:
        List of tuples containing column name, referenced table, and referenced column
    """
    query = """
        SELECT
            kcu.column_name,
            ccu.table_schema || '.' || ccu.table_name AS referenced_table,
            ccu.column_name AS referenced_column
        FROM
            information_schema.table_constraints AS tc
            JOIN information_schema.key_column_usage AS kcu
              ON tc.constraint_name = kcu.constraint_name
              AND tc.table_schema = kcu.table_schema
            JOIN information_schema.constraint_column_usage AS ccu
              ON ccu.constraint_name = tc.constraint_name
              AND ccu.table_schema = tc.table_schema
        WHERE tc.constraint_type = 'FOREIGN KEY'
        AND tc.table_schema = %s
        AND tc.table_name = %s
    """
    
    success, messages, result = execute_query(query, (schema_name, table_name))
    
    if not success:
        logger.error(f"Failed to get foreign keys for {schema_name}.{table_name}: {messages}")
        return []
    
    return result

def get_tables(schema_name: str) -> List[str]:
    """
    Get all tables in a schema.
    
    Args:
        schema_name: Schema name
        
    Returns:
        List of table names
    """
    query = """
        SELECT table_name
        FROM information_schema.tables
        WHERE table_schema = %s
        AND table_type = 'BASE TABLE'
    """
    
    success, messages, result = execute_query(query, (schema_name,))
    
    if not success or not result:
        logger.error(f"Failed to get tables for {schema_name}: {messages}")
        return []
    
    return [row[0] for row in result]

def create_table_if_not_exists(schema_name: str, table_name: str, columns: List[Tuple[str, str, str, str]], primary_key: str, foreign_keys: List[Tuple[str, str, str]]) -> bool:
    """
    Create a table if it doesn't exist.
    
    Args:
        schema_name: Schema name
        table_name: Table name
        columns: List of tuples containing column name, data type, default value, and udt_name
        primary_key: Primary key column name
        foreign_keys: List of tuples containing column name, referenced table, and referenced column
        
    Returns:
        Boolean indicating if creation was successful
    """
    # Check if table exists
    query = """
        SELECT EXISTS (
            SELECT FROM information_schema.tables
            WHERE table_schema = %s
            AND table_name = %s
        )
    """
    
    success, messages, result = execute_query(query, (schema_name, table_name))
    
    if not success:
        logger.error(f"Failed to check if table {schema_name}.{table_name} exists: {messages}")
        return False
    
    table_exists = result and result[0][0]
    
    if table_exists:
        logger.info(f"Table {schema_name}.{table_name} already exists")
        return True
    
    # Create table
    column_defs = []
    for column_name, data_type, default_value, udt_name in columns:
        # Use udt_name for USER-DEFINED types
        if data_type == 'USER-DEFINED':
            column_def = f"{column_name} {udt_name}"
        else:
            column_def = f"{column_name} {data_type}"
            
        if default_value:
            column_def += f" DEFAULT {default_value}"
        column_defs.append(column_def)
    
    if primary_key:
        column_defs.append(f"PRIMARY KEY ({primary_key})")
    
    for column_name, referenced_table, referenced_column in foreign_keys:
        column_defs.append(f"FOREIGN KEY ({column_name}) REFERENCES {referenced_table}({referenced_column})")
    
    query = f"""
        CREATE TABLE {schema_name}.{table_name} (
            {', '.join(column_defs)}
        )
    """
    
    success, messages, _ = execute_query(query)
    
    if not success:
        logger.error(f"Failed to create table {schema_name}.{table_name}: {messages}")
        return False
    
    logger.info(f"Created table {schema_name}.{table_name}")
    return True

def sync_table_schema(source_schema: str, target_schema: str, table_name: str) -> bool:
    """
    Synchronize the schema of a table from source to target.
    
    Args:
        source_schema: Source schema name
        target_schema: Target schema name
        table_name: Table name
        
    Returns:
        Boolean indicating if synchronization was successful
    """
    # Get source table schema
    source_columns = get_table_schema(source_schema, table_name)
    if not source_columns:
        logger.error(f"Failed to get schema for {source_schema}.{table_name}")
        return False
    
    # Get source table primary key
    primary_key = get_primary_key(source_schema, table_name)
    
    # Get source table foreign keys
    foreign_keys = get_foreign_keys(source_schema, table_name)
    
    # Create target table if it doesn't exist
    success = create_table_if_not_exists(target_schema, table_name, source_columns, primary_key, foreign_keys)
    if not success:
        return False
    
    # Get target table schema
    target_columns = get_table_schema(target_schema, table_name)
    if not target_columns:
        logger.error(f"Failed to get schema for {target_schema}.{table_name}")
        return False
    
    # Compare schemas and add missing columns
    source_column_names = [col[0] for col in source_columns]
    target_column_names = [col[0] for col in target_columns]
    
    for column_name, data_type, default_value, udt_name in source_columns:
        if column_name not in target_column_names:
            # Add column to target table
            if data_type == 'USER-DEFINED':
                query = f"""
                    ALTER TABLE {target_schema}.{table_name}
                    ADD COLUMN {column_name} {udt_name}
                """
            else:
                query = f"""
                    ALTER TABLE {target_schema}.{table_name}
                    ADD COLUMN {column_name} {data_type}
                """
            
            if default_value:
                query += f" DEFAULT {default_value}"
            
            success, messages, _ = execute_query(query)
            
            if not success:
                logger.error(f"Failed to add column {column_name} to {target_schema}.{table_name}: {messages}")
                return False
            
            logger.info(f"Added column {column_name} to {target_schema}.{table_name}")
    
    # Remove extra columns from target table
    for column_name in target_column_names:
        if column_name not in source_column_names:
            # Drop column from target table
            query = f"""
                ALTER TABLE {target_schema}.{table_name}
                DROP COLUMN {column_name}
            """
            
            success, messages, _ = execute_query(query)
            
            if not success:
                logger.error(f"Failed to drop column {column_name} from {target_schema}.{table_name}: {messages}")
                return False
            
            logger.info(f"Dropped column {column_name} from {target_schema}.{table_name}")
    
    return True

def sync_schema(source_schema: str, target_schema: str) -> bool:
    """
    Synchronize the schema from source to target.
    
    Args:
        source_schema: Source schema name
        target_schema: Target schema name
        
    Returns:
        Boolean indicating if synchronization was successful
    """
    # Get all tables in source schema
    source_tables = get_tables(source_schema)
    if not source_tables:
        logger.error(f"Failed to get tables for {source_schema}")
        return False
    
    # Synchronize each table
    for table_name in source_tables:
        success = sync_table_schema(source_schema, target_schema, table_name)
        if not success:
            logger.error(f"Failed to synchronize table {table_name}")
            return False
    
    return True

def main():
    """
    Main function.
    """
    parser = argparse.ArgumentParser(description='Synchronize the workflow_temp schema with the workflow_runtime schema.')
    parser.add_argument('--source-schema', type=str, default='workflow_runtime', help='Source schema name')
    parser.add_argument('--target-schema', type=str, default='workflow_temp', help='Target schema name')
    parser.add_argument('--table', type=str, help='Specific table to synchronize')
    
    args = parser.parse_args()
    
    if args.table:
        # Synchronize specific table
        success = sync_table_schema(args.source_schema, args.target_schema, args.table)
        if success:
            logger.info(f"Successfully synchronized table {args.table}")
        else:
            logger.error(f"Failed to synchronize table {args.table}")
    else:
        # Synchronize all tables
        success = sync_schema(args.source_schema, args.target_schema)
        if success:
            logger.info(f"Successfully synchronized schema from {args.source_schema} to {args.target_schema}")
        else:
            logger.error(f"Failed to synchronize schema from {args.source_schema} to {args.target_schema}")

if __name__ == '__main__':
    main()
