"""
Entity Deployer for YAML Builder v2

This module provides functionality for deploying parsed entity data to the database.
"""

import os
import sys
import logging
from typing import Dict, List, Tuple, Any, Optional

# Import database utilities
from db_utils import execute_query, create_table, add_column, add_constraint

# Set up logging
logger = logging.getLogger('entity_deployer')

def deploy_entities(entity_data: Dict, schema_name: str) -> Tuple[bool, List[str]]:
    """
    Deploy parsed entity data to the database.
    
    Args:
        entity_data: Parsed entity data
        schema_name: Schema name to deploy to
        
    Returns:
        Tuple containing:
            - Boolean indicating if deployment was successful
            - List of messages (warnings, errors, or success messages)
    """
    messages = []
    
    try:
        logger.info(f"Deploying entities to schema {schema_name}")
        
        # Check if entity data is valid
        if not entity_data or 'entities' not in entity_data:
            logger.error("Invalid entity data: 'entities' key not found")
            return False, ["Invalid entity data: 'entities' key not found"]
        
        entities = entity_data['entities']
        
        # Create entities table if it doesn't exist
        success, create_messages = create_entities_table(schema_name)
        messages.extend(create_messages)
        
        if not success:
            return False, messages
        
        # Create attributes table if it doesn't exist
        success, create_messages = create_attributes_table(schema_name)
        messages.extend(create_messages)
        
        if not success:
            return False, messages
        
        # Create relationships table if it doesn't exist
        success, create_messages = create_relationships_table(schema_name)
        messages.extend(create_messages)
        
        if not success:
            return False, messages
        
        # Create business rules table if it doesn't exist
        success, create_messages = create_business_rules_table(schema_name)
        messages.extend(create_messages)
        
        if not success:
            return False, messages
        
        # Create calculated fields table if it doesn't exist
        success, create_messages = create_calculated_fields_table(schema_name)
        messages.extend(create_messages)
        
        if not success:
            return False, messages
        
        # Create validations table if it doesn't exist
        success, create_messages = create_validations_table(schema_name)
        messages.extend(create_messages)
        
        if not success:
            return False, messages
        
        # Create constraints table if it doesn't exist
        success, create_messages = create_constraints_table(schema_name)
        messages.extend(create_messages)
        
        if not success:
            return False, messages
        
        # Deploy each entity
        for entity_name, entity in entities.items():
            success, deploy_messages = deploy_entity(entity, schema_name)
            messages.extend(deploy_messages)
            
            if not success:
                return False, messages
        
        messages.append(f"Successfully deployed {len(entities)} entities to schema {schema_name}")
        return True, messages
    except Exception as e:
        logger.error(f"Error deploying entities to schema {schema_name}: {str(e)}", exc_info=True)
        messages.append(f"Error deploying entities to schema {schema_name}: {str(e)}")
        return False, messages

def create_entities_table(schema_name: str) -> Tuple[bool, List[str]]:
    """
    Create entities table if it doesn't exist.
    
    Args:
        schema_name: Schema name to deploy to
        
    Returns:
        Tuple containing:
            - Boolean indicating if creation was successful
            - List of messages (warnings, errors, or success messages)
    """
    return create_table(
        schema_name,
        'entities',
        """
        entity_id SERIAL PRIMARY KEY,
        name VARCHAR(255) NOT NULL,
        display_name VARCHAR(255),
        type VARCHAR(50),
        description TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        CONSTRAINT entities_name_unique UNIQUE (name)
        """
    )

def create_attributes_table(schema_name: str) -> Tuple[bool, List[str]]:
    """
    Create attributes table if it doesn't exist.
    
    Args:
        schema_name: Schema name to deploy to
        
    Returns:
        Tuple containing:
            - Boolean indicating if creation was successful
            - List of messages (warnings, errors, or success messages)
    """
    return create_table(
        schema_name,
        'attributes',
        """
        attribute_id SERIAL PRIMARY KEY,
        entity_id INTEGER NOT NULL,
        name VARCHAR(255) NOT NULL,
        type VARCHAR(50) NOT NULL,
        primary_key BOOLEAN DEFAULT FALSE,
        foreign_key BOOLEAN DEFAULT FALSE,
        required BOOLEAN DEFAULT FALSE,
        unique BOOLEAN DEFAULT FALSE,
        calculated BOOLEAN DEFAULT FALSE,
        default_value TEXT,
        description TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        CONSTRAINT attributes_entity_id_fkey FOREIGN KEY (entity_id) REFERENCES {schema_name}.entities (entity_id) ON DELETE CASCADE,
        CONSTRAINT attributes_entity_id_name_unique UNIQUE (entity_id, name)
        """.format(schema_name=schema_name)
    )

def create_relationships_table(schema_name: str) -> Tuple[bool, List[str]]:
    """
    Create relationships table if it doesn't exist.
    
    Args:
        schema_name: Schema name to deploy to
        
    Returns:
        Tuple containing:
            - Boolean indicating if creation was successful
            - List of messages (warnings, errors, or success messages)
    """
    return create_table(
        schema_name,
        'relationships',
        """
        relationship_id SERIAL PRIMARY KEY,
        source_entity_id INTEGER NOT NULL,
        target_entity_id INTEGER NOT NULL,
        type VARCHAR(50) NOT NULL,
        source_attribute VARCHAR(255) NOT NULL,
        target_attribute VARCHAR(255) NOT NULL,
        on_delete VARCHAR(50),
        on_update VARCHAR(50),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        CONSTRAINT relationships_source_entity_id_fkey FOREIGN KEY (source_entity_id) REFERENCES {schema_name}.entities (entity_id) ON DELETE CASCADE,
        CONSTRAINT relationships_target_entity_id_fkey FOREIGN KEY (target_entity_id) REFERENCES {schema_name}.entities (entity_id) ON DELETE CASCADE
        """.format(schema_name=schema_name)
    )

def create_business_rules_table(schema_name: str) -> Tuple[bool, List[str]]:
    """
    Create business rules table if it doesn't exist.
    
    Args:
        schema_name: Schema name to deploy to
        
    Returns:
        Tuple containing:
            - Boolean indicating if creation was successful
            - List of messages (warnings, errors, or success messages)
    """
    return create_table(
        schema_name,
        'business_rules',
        """
        rule_id SERIAL PRIMARY KEY,
        entity_id INTEGER NOT NULL,
        name VARCHAR(255) NOT NULL,
        description TEXT,
        operation TEXT,
        inputs TEXT,
        outputs TEXT,
        error_message TEXT,
        validation_type VARCHAR(50),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        CONSTRAINT business_rules_entity_id_fkey FOREIGN KEY (entity_id) REFERENCES {schema_name}.entities (entity_id) ON DELETE CASCADE,
        CONSTRAINT business_rules_entity_id_name_unique UNIQUE (entity_id, name)
        """.format(schema_name=schema_name)
    )

def create_calculated_fields_table(schema_name: str) -> Tuple[bool, List[str]]:
    """
    Create calculated fields table if it doesn't exist.
    
    Args:
        schema_name: Schema name to deploy to
        
    Returns:
        Tuple containing:
            - Boolean indicating if creation was successful
            - List of messages (warnings, errors, or success messages)
    """
    return create_table(
        schema_name,
        'calculated_fields',
        """
        field_id SERIAL PRIMARY KEY,
        entity_id INTEGER NOT NULL,
        attribute_id INTEGER NOT NULL,
        formula TEXT NOT NULL,
        logic_layer VARCHAR(50),
        caching VARCHAR(50),
        dependencies TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        CONSTRAINT calculated_fields_entity_id_fkey FOREIGN KEY (entity_id) REFERENCES {schema_name}.entities (entity_id) ON DELETE CASCADE,
        CONSTRAINT calculated_fields_attribute_id_fkey FOREIGN KEY (attribute_id) REFERENCES {schema_name}.attributes (attribute_id) ON DELETE CASCADE
        """.format(schema_name=schema_name)
    )

def create_validations_table(schema_name: str) -> Tuple[bool, List[str]]:
    """
    Create validations table if it doesn't exist.
    
    Args:
        schema_name: Schema name to deploy to
        
    Returns:
        Tuple containing:
            - Boolean indicating if creation was successful
            - List of messages (warnings, errors, or success messages)
    """
    return create_table(
        schema_name,
        'validations',
        """
        validation_id SERIAL PRIMARY KEY,
        entity_id INTEGER NOT NULL,
        attribute_id INTEGER NOT NULL,
        type VARCHAR(50) NOT NULL,
        constraint_rule TEXT NOT NULL,
        error_message TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        CONSTRAINT validations_entity_id_fkey FOREIGN KEY (entity_id) REFERENCES {schema_name}.entities (entity_id) ON DELETE CASCADE,
        CONSTRAINT validations_attribute_id_fkey FOREIGN KEY (attribute_id) REFERENCES {schema_name}.attributes (attribute_id) ON DELETE CASCADE
        """.format(schema_name=schema_name)
    )

def create_constraints_table(schema_name: str) -> Tuple[bool, List[str]]:
    """
    Create constraints table if it doesn't exist.
    
    Args:
        schema_name: Schema name to deploy to
        
    Returns:
        Tuple containing:
            - Boolean indicating if creation was successful
            - List of messages (warnings, errors, or success messages)
    """
    return create_table(
        schema_name,
        'constraints',
        """
        constraint_id SERIAL PRIMARY KEY,
        source_entity_id INTEGER NOT NULL,
        target_entity_id INTEGER NOT NULL,
        constrained_entity_id INTEGER NOT NULL,
        foreign_key_attributes TEXT NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        CONSTRAINT constraints_source_entity_id_fkey FOREIGN KEY (source_entity_id) REFERENCES {schema_name}.entities (entity_id) ON DELETE CASCADE,
        CONSTRAINT constraints_target_entity_id_fkey FOREIGN KEY (target_entity_id) REFERENCES {schema_name}.entities (entity_id) ON DELETE CASCADE,
        CONSTRAINT constraints_constrained_entity_id_fkey FOREIGN KEY (constrained_entity_id) REFERENCES {schema_name}.entities (entity_id) ON DELETE CASCADE
        """.format(schema_name=schema_name)
    )

def deploy_entity(entity: Dict, schema_name: str) -> Tuple[bool, List[str]]:
    """
    Deploy an entity to the database.
    
    Args:
        entity: Entity data
        schema_name: Schema name to deploy to
        
    Returns:
        Tuple containing:
            - Boolean indicating if deployment was successful
            - List of messages (warnings, errors, or success messages)
    """
    messages = []
    
    try:
        entity_name = entity.get('name')
        logger.info(f"Deploying entity {entity_name} to schema {schema_name}")
        
        # Insert entity
        success, query_messages, entity_id = insert_entity(entity, schema_name)
        messages.extend(query_messages)
        
        if not success:
            return False, messages
        
        # Insert attributes
        if 'attributes' in entity:
            for attr_name, attr in entity['attributes'].items():
                success, query_messages, attribute_id = insert_attribute(entity_id, attr_name, attr, schema_name)
                messages.extend(query_messages)
                
                if not success:
                    return False, messages
        
        # Insert relationships
        if 'relationships' in entity:
            for rel_id, rel in entity['relationships'].items():
                success, query_messages = insert_relationship(entity_id, rel, schema_name)
                messages.extend(query_messages)
                
                if not success:
                    return False, messages
        
        # Insert business rules
        if 'business_rules' in entity:
            for rule_name, rule in entity['business_rules'].items():
                success, query_messages = insert_business_rule(entity_id, rule_name, rule, schema_name)
                messages.extend(query_messages)
                
                if not success:
                    return False, messages
        
        # Insert calculated fields
        if 'calculated_fields' in entity:
            for field_id, field in entity['calculated_fields'].items():
                success, query_messages = insert_calculated_field(entity_id, field, schema_name)
                messages.extend(query_messages)
                
                if not success:
                    return False, messages
        
        # Insert validations
        if 'validations' in entity:
            for validation_id, validation in entity['validations'].items():
                success, query_messages = insert_validation(entity_id, validation, schema_name)
                messages.extend(query_messages)
                
                if not success:
                    return False, messages
        
        # Insert constraints
        if 'constraints' in entity:
            for constraint_id, constraint in entity['constraints'].items():
                success, query_messages = insert_constraint(entity_id, constraint, schema_name)
                messages.extend(query_messages)
                
                if not success:
                    return False, messages
        
        messages.append(f"Successfully deployed entity {entity_name} to schema {schema_name}")
        return True, messages
    except Exception as e:
        logger.error(f"Error deploying entity {entity.get('name')} to schema {schema_name}: {str(e)}", exc_info=True)
        messages.append(f"Error deploying entity {entity.get('name')} to schema {schema_name}: {str(e)}")
        return False, messages

def insert_entity(entity: Dict, schema_name: str) -> Tuple[bool, List[str], int]:
    """
    Insert an entity into the database.
    
    Args:
        entity: Entity data
        schema_name: Schema name to deploy to
        
    Returns:
        Tuple containing:
            - Boolean indicating if insertion was successful
            - List of messages (warnings, errors, or success messages)
            - Entity ID (0 if insertion failed)
    """
    messages = []
    
    try:
        entity_name = entity.get('name')
        display_name = entity.get('display_name', entity_name)
        entity_type = entity.get('type', 'standard')
        description = entity.get('description', '')
        
        # Check if entity already exists
        success, query_messages, result = execute_query(
            f"""
            SELECT entity_id FROM {schema_name}.entities
            WHERE name = %s
            """,
            (entity_name,)
        )
        
        if not success:
            messages.extend(query_messages)
            return False, messages, 0
        
        if result:
            # Entity already exists, update it
            entity_id = result[0][0]
            
            success, query_messages, _ = execute_query(
                f"""
                UPDATE {schema_name}.entities
                SET display_name = %s, type = %s, description = %s, updated_at = CURRENT_TIMESTAMP
                WHERE entity_id = %s
                """,
                (display_name, entity_type, description, entity_id)
            )
            
            if not success:
                messages.extend(query_messages)
                return False, messages, 0
            
            messages.append(f"Updated entity {entity_name} in schema {schema_name}")
        else:
            # Entity doesn't exist, insert it
            success, query_messages, result = execute_query(
                f"""
                INSERT INTO {schema_name}.entities (name, display_name, type, description)
                VALUES (%s, %s, %s, %s)
                RETURNING entity_id
                """,
                (entity_name, display_name, entity_type, description)
            )
            
            if not success:
                messages.extend(query_messages)
                return False, messages, 0
            
            entity_id = result[0][0]
            messages.append(f"Inserted entity {entity_name} into schema {schema_name}")
        
        return True, messages, entity_id
    except Exception as e:
        logger.error(f"Error inserting entity {entity.get('name')} into schema {schema_name}: {str(e)}", exc_info=True)
        messages.append(f"Error inserting entity {entity.get('name')} into schema {schema_name}: {str(e)}")
        return False, messages, 0

def insert_attribute(entity_id: int, attr_name: str, attr: Dict, schema_name: str) -> Tuple[bool, List[str], int]:
    """
    Insert an attribute into the database.
    
    Args:
        entity_id: Entity ID
        attr_name: Attribute name
        attr: Attribute data
        schema_name: Schema name to deploy to
        
    Returns:
        Tuple containing:
            - Boolean indicating if insertion was successful
            - List of messages (warnings, errors, or success messages)
            - Attribute ID (0 if insertion failed)
    """
    messages = []
    
    try:
        attr_type = attr.get('type', 'string')
        primary_key = attr.get('primary_key', False)
        foreign_key = attr.get('foreign_key', False)
        required = attr.get('required', False)
        unique = attr.get('unique', False)
        calculated = attr.get('calculated', False)
        default_value = attr.get('default', None)
        description = attr.get('description', '')
        
        # Check if attribute already exists
        success, query_messages, result = execute_query(
            f"""
            SELECT attribute_id FROM {schema_name}.attributes
            WHERE entity_id = %s AND name = %s
            """,
            (entity_id, attr_name)
        )
        
        if not success:
            messages.extend(query_messages)
            return False, messages, 0
        
        if result:
            # Attribute already exists, update it
            attribute_id = result[0][0]
            
            success, query_messages, _ = execute_query(
                f"""
                UPDATE {schema_name}.attributes
                SET type = %s, primary_key = %s, foreign_key = %s, required = %s, unique = %s,
                    calculated = %s, default_value = %s, description = %s, updated_at = CURRENT_TIMESTAMP
                WHERE attribute_id = %s
                """,
                (attr_type, primary_key, foreign_key, required, unique, calculated, default_value, description, attribute_id)
            )
            
            if not success:
                messages.extend(query_messages)
                return False, messages, 0
            
            messages.append(f"Updated attribute {attr_name} in schema {schema_name}")
        else:
            # Attribute doesn't exist, insert it
            success, query_messages, result = execute_query(
                f"""
                INSERT INTO {schema_name}.attributes
                (entity_id, name, type, primary_key, foreign_key, required, unique, calculated, default_value, description)
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                RETURNING attribute_id
                """,
                (entity_id, attr_name, attr_type, primary_key, foreign_key, required, unique, calculated, default_value, description)
            )
            
            if not success:
                messages.extend(query_messages)
                return False, messages, 0
            
            attribute_id = result[0][0]
            messages.append(f"Inserted attribute {attr_name} into schema {schema_name}")
        
        return True, messages, attribute_id
    except Exception as e:
        logger.error(f"Error inserting attribute {attr_name} into schema {schema_name}: {str(e)}", exc_info=True)
        messages.append(f"Error inserting attribute {attr_name} into schema {schema_name}: {str(e)}")
        return False, messages, 0

def insert_relationship(source_entity_id: int, relationship: Dict, schema_name: str) -> Tuple[bool, List[str]]:
    """
    Insert a relationship into the database.
    
    Args:
        source_entity_id: Source entity ID
        relationship: Relationship data
        schema_name: Schema name to deploy to
        
    Returns:
        Tuple containing:
            - Boolean indicating if insertion was successful
            - List of messages (warnings, errors, or success messages)
    """
    messages = []
    
    try:
        target_entity_name = relationship.get('entity')
        relationship_type = relationship.get('type', 'one-to-many')
        source_attribute = relationship.get('source_attribute')
        target_attribute = relationship.get('target_attribute')
        on_delete = relationship.get('on_delete', 'CASCADE')
        on_update = relationship.get('on_update', 'CASCADE')
        
        # Get target entity ID
        success, query_messages, result = execute_query(
            f"""
            SELECT entity_id FROM {schema_name}.entities
            WHERE name = %s
            """,
            (target_entity_name,)
        )
        
        if not success:
            messages.extend(query_messages)
            return False, messages
        
        if not result:
            messages.append(f"Target entity {target_entity_name} not found in schema {schema_name}")
            return False, messages
        
        target_entity_id = result[0][0]
        
        # Check if relationship already exists
        success, query_messages, result = execute_query(
            f"""
            SELECT relationship_id FROM {schema_name}.relationships
            WHERE source_entity_id = %s AND target_entity_id = %s AND source_attribute = %s AND target_attribute = %s
            """,
            (source_entity_id, target_entity_id, source_attribute, target_attribute)
        )
        
        if not success:
            messages.extend(query_messages)
            return False, messages
        
        if result:
            # Relationship already exists, update it
            relationship_id = result[0][0]
            
            success, query_messages, _ = execute_query(
                f"""
                UPDATE {schema_name}.relationships
                SET type = %s, on_delete = %s, on_update = %s, updated_at = CURRENT_TIMESTAMP
                WHERE relationship_id = %s
                """,
                (relationship_type, on_delete, on_update, relationship_id)
            )
            
            if not success:
                messages.extend(query_messages)
                return False, messages
            
            messages.append(f"Updated relationship in schema {schema_name}")
        else:
            # Relationship doesn't exist, insert it
            success, query_messages, _ = execute_query(
                f"""
                INSERT INTO {schema_name}.relationships
                (source_entity_id, target_entity_id, type, source_attribute, target_attribute, on_delete, on_update)
                VALUES (%s, %s, %s, %s, %s, %s, %s)
                """,
                (source_entity_id, target_entity_id, relationship_type, source_attribute, target_attribute, on_delete, on_update)
            )
            
            if not success:
                messages.extend(query_messages)
                return False, messages
            
            messages.append(f"Inserted relationship into schema {schema_name}")
        
        return True, messages
    except Exception as e:
        logger.error(f"Error inserting relationship into schema {schema_name}: {str(e)}", exc_info=True)
        messages.append(f"Error inserting relationship into schema {schema_name}: {str(e)}")
        return False, messages

def insert_business_rule(entity_id: int, rule_name: str, rule: Dict, schema_name: str) -> Tuple[bool, List[str]]:
    """
    Insert a business rule into the database.
    
    Args:
        entity_id: Entity ID
        rule_name: Rule name
        rule: Business rule data
        schema_name: Schema name to deploy to
        
    Returns:
        Tuple containing:
            - Boolean indicating if insertion was successful
            - List of messages (warnings, errors, or success messages)
    """
    messages = []
    
    try:
        description = rule.get('description', '')
        operation = rule.get('operation', '')
        inputs = ','.join(rule.get('inputs', []))
        outputs = ','.join(rule.get('outputs', []))
        error_message = rule.get('error_message', '')
        validation_type = rule.get('validation_type', '')
        
        # Check if business rule already exists
        success, query_messages, result = execute_query(
            f"""
            SELECT rule_id FROM {schema_name}.business_rules
            WHERE entity_id = %s AND name = %s
            """,
            (entity_id, rule_name)
        )
        
        if not success:
            messages.extend(query_messages)
            return False, messages
        
        if result:
            # Business rule already exists, update it
            rule_id = result[0][0]
            
            success, query_messages, _ = execute_query(
                f"""
                UPDATE {schema_name}.business_rules
                SET description = %s, operation = %s, inputs = %s, outputs = %s,
                    error_message = %s, validation_type = %s, updated_at = CURRENT_TIMESTAMP
                WHERE rule_id = %s
                """,
                (description, operation, inputs, outputs, error_message, validation_type, rule_id)
            )
            
            if not success:
                messages.extend(query_messages)
                return False, messages
            
            messages.append(f"Updated business rule {rule_name} in schema {schema_name}")
        else:
            # Business rule doesn't exist, insert it
            success, query_messages, _ = execute_query(
                f"""
                INSERT INTO {schema_name}.business_rules
                (entity_id, name, description, operation, inputs, outputs, error_message, validation_type)
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
                """,
                (entity_id, rule_name, description, operation, inputs, outputs, error_message, validation_type)
            )
            
            if not success:
                messages.extend(query_messages)
                return False, messages
            
            messages.append(f"Inserted business rule {rule_name} into schema {schema_name}")
        
        return True, messages
    except Exception as e:
        logger.error(f"Error inserting business rule {rule_name} into schema {schema_name}: {str(e)}", exc_info=True)
        messages.append(f"Error inserting business rule {rule_name} into schema {schema_name}: {str(e)}")
        return False, messages

def insert_calculated_field(entity_id: int, field: Dict, schema_name: str) -> Tuple[bool, List[str]]:
    """
    Insert a calculated field into the database.
    
    Args:
        entity_id: Entity ID
        field: Calculated field data
        schema_name: Schema name to deploy to
        
    Returns:
        Tuple containing:
            - Boolean indicating if insertion was successful
            - List of messages (warnings, errors, or success messages)
    """
    messages = []
    
    try:
        attribute_name = field.get('attribute')
        formula = field.get('formula', '')
        logic_layer = field.get('logic_layer', 'Application')
        caching = field.get('caching', 'None')
        dependencies = ','.join(field.get('dependencies', []))
        
        # Get attribute ID
        success, query_messages, result = execute_query(
            f"""
            SELECT attribute_id FROM {schema_name}.attributes
            WHERE entity_id = %s AND name = %s
            """,
            (entity_id, attribute_name)
        )
        
        if not success:
            messages.extend(query_messages)
            return False, messages
        
        if not result:
            messages.append(f"Attribute {attribute_name} not found in schema {schema_name}")
            return False, messages
        
        attribute_id = result[0][0]
        
        # Check if calculated field already exists
        success, query_messages, result = execute_query(
            f"""
            SELECT field_id FROM {schema_name}.calculated_fields
            WHERE entity_id = %s AND attribute_id = %s
            """,
            (entity_id, attribute_id)
        )
        
        if not success:
            messages.extend(query_messages)
            return False, messages
        
        if result:
            # Calculated field already exists, update it
            field_id = result[0][0]
            
            success, query_messages, _ = execute_query(
                f"""
                UPDATE {schema_name}.calculated_fields
                SET formula = %s, logic_layer = %s, caching = %s, dependencies = %s, updated_at = CURRENT_TIMESTAMP
                WHERE field_id = %s
                """,
                (formula, logic_layer, caching, dependencies, field_id)
            )
            
            if not success:
                messages.extend(query_messages)
                return False, messages
            
            messages.append(f"Updated calculated field for attribute {attribute_name} in schema {schema_name}")
        else:
            # Calculated field doesn't exist, insert it
            success, query_messages, _ = execute_query(
                f"""
                INSERT INTO {schema_name}.calculated_fields
                (entity_id, attribute_id, formula, logic_layer, caching, dependencies)
                VALUES (%s, %s, %s, %s, %s, %s)
                """,
                (entity_id, attribute_id, formula, logic_layer, caching, dependencies)
            )
            
            if not success:
                messages.extend(query_messages)
                return False, messages
            
            messages.append(f"Inserted calculated field for attribute {attribute_name} into schema {schema_name}")
        
        return True, messages
    except Exception as e:
        logger.error(f"Error inserting calculated field into schema {schema_name}: {str(e)}", exc_info=True)
        messages.append(f"Error inserting calculated field into schema {schema_name}: {str(e)}")
        return False, messages

def insert_validation(entity_id: int, validation: Dict, schema_name: str) -> Tuple[bool, List[str]]:
    """
    Insert a validation into the database.
    
    Args:
        entity_id: Entity ID
        validation: Validation data
        schema_name: Schema name to deploy to
        
    Returns:
        Tuple containing:
            - Boolean indicating if insertion was successful
            - List of messages (warnings, errors, or success messages)
    """
    messages = []
    
    try:
        attribute_name = validation.get('attribute')
        validation_type = validation.get('type', 'expression')
        constraint_rule = validation.get('constraint', '')
        error_message = validation.get('error_message', '')
        
        # Get attribute ID
        success, query_messages, result = execute_query(
            f"""
            SELECT attribute_id FROM {schema_name}.attributes
            WHERE entity_id = %s AND name = %s
            """,
            (entity_id, attribute_name)
        )
        
        if not success:
            messages.extend(query_messages)
            return False, messages
        
        if not result:
            messages.append(f"Attribute {attribute_name} not found in schema {schema_name}")
            return False, messages
        
        attribute_id = result[0][0]
        
        # Check if validation already exists
        success, query_messages, result = execute_query(
            f"""
            SELECT validation_id FROM {schema_name}.validations
            WHERE entity_id = %s AND attribute_id = %s AND type = %s
            """,
            (entity_id, attribute_id, validation_type)
        )
        
        if not success:
            messages.extend(query_messages)
            return False, messages
        
        if result:
            # Validation already exists, update it
            validation_id = result[0][0]
            
            success, query_messages, _ = execute_query(
                f"""
                UPDATE {schema_name}.validations
                SET constraint_rule = %s, error_message = %s, updated_at = CURRENT_TIMESTAMP
                WHERE validation_id = %s
                """,
                (constraint_rule, error_message, validation_id)
            )
            
            if not success:
                messages.extend(query_messages)
                return False, messages
            
            messages.append(f"Updated validation for attribute {attribute_name} in schema {schema_name}")
        else:
            # Validation doesn't exist, insert it
            success, query_messages, _ = execute_query(
                f"""
                INSERT INTO {schema_name}.validations
                (entity_id, attribute_id, type, constraint_rule, error_message)
                VALUES (%s, %s, %s, %s, %s)
                """,
                (entity_id, attribute_id, validation_type, constraint_rule, error_message)
            )
            
            if not success:
                messages.extend(query_messages)
                return False, messages
            
            messages.append(f"Inserted validation for attribute {attribute_name} into schema {schema_name}")
        
        return True, messages
    except Exception as e:
        logger.error(f"Error inserting validation into schema {schema_name}: {str(e)}", exc_info=True)
        messages.append(f"Error inserting validation into schema {schema_name}: {str(e)}")
        return False, messages

def insert_constraint(entity_id: int, constraint: Dict, schema_name: str) -> Tuple[bool, List[str]]:
    """
    Insert a constraint into the database.
    
    Args:
        entity_id: Entity ID
        constraint: Constraint data
        schema_name: Schema name to deploy to
        
    Returns:
        Tuple containing:
            - Boolean indicating if insertion was successful
            - List of messages (warnings, errors, or success messages)
    """
    messages = []
    
    try:
        target_entity_name = constraint.get('target_entity')
        constrained_entity_name = constraint.get('constrained_entity')
        foreign_key_attributes = ','.join(constraint.get('foreign_key_attributes', []))
        
        # Get target entity ID
        success, query_messages, result = execute_query(
            f"""
            SELECT entity_id FROM {schema_name}.entities
            WHERE name = %s
            """,
            (target_entity_name,)
        )
        
        if not success:
            messages.extend(query_messages)
            return False, messages
        
        if not result:
            messages.append(f"Target entity {target_entity_name} not found in schema {schema_name}")
            return False, messages
        
        target_entity_id = result[0][0]
        
        # Get constrained entity ID
        success, query_messages, result = execute_query(
            f"""
            SELECT entity_id FROM {schema_name}.entities
            WHERE name = %s
            """,
            (constrained_entity_name,)
        )
        
        if not success:
            messages.extend(query_messages)
            return False, messages
        
        if not result:
            messages.append(f"Constrained entity {constrained_entity_name} not found in schema {schema_name}")
            return False, messages
        
        constrained_entity_id = result[0][0]
        
        # Check if constraint already exists
        success, query_messages, result = execute_query(
            f"""
            SELECT constraint_id FROM {schema_name}.constraints
            WHERE source_entity_id = %s AND target_entity_id = %s AND constrained_entity_id = %s
            """,
            (entity_id, target_entity_id, constrained_entity_id)
        )
        
        if not success:
            messages.extend(query_messages)
            return False, messages
        
        if result:
            # Constraint already exists, update it
            constraint_id = result[0][0]
            
            success, query_messages, _ = execute_query(
                f"""
                UPDATE {schema_name}.constraints
                SET foreign_key_attributes = %s, updated_at = CURRENT_TIMESTAMP
                WHERE constraint_id = %s
                """,
                (foreign_key_attributes, constraint_id)
            )
            
            if not success:
                messages.extend(query_messages)
                return False, messages
            
            messages.append(f"Updated constraint in schema {schema_name}")
        else:
            # Constraint doesn't exist, insert it
            success, query_messages, _ = execute_query(
                f"""
                INSERT INTO {schema_name}.constraints
                (source_entity_id, target_entity_id, constrained_entity_id, foreign_key_attributes)
                VALUES (%s, %s, %s, %s)
                """,
                (entity_id, target_entity_id, constrained_entity_id, foreign_key_attributes)
            )
            
            if not success:
                messages.extend(query_messages)
                return False, messages
            
            messages.append(f"Inserted constraint into schema {schema_name}")
        
        return True, messages
    except Exception as e:
        logger.error(f"Error inserting constraint into schema {schema_name}: {str(e)}", exc_info=True)
        messages.append(f"Error inserting constraint into schema {schema_name}: {str(e)}")
        return False, messages
