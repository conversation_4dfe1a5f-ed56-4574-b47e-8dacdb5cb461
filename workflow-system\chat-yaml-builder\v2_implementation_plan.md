# V2 Implementation Plan for YAML Builder

This document outlines the implementation plan for creating a v2 version of the YAML Builder that doesn't disrupt the existing working software. The plan includes creating a v2 folder structure and making database updates with backward compatibility.

## 1. Overview

The current YAML Builder system uses a monolithic approach where the entire YAML is generated at once. The new v2 implementation will break this down into components (roles, entities, GO definitions, LO definitions) to address context window limitations and enforce rigorous validations.

### Key Principles

1. **Non-disruptive**: The v2 implementation should not disrupt the existing working software.
2. **Backward Compatible**: The v2 system should be able to read and process existing YAML files.
3. **Gradual Migration**: Users should be able to migrate to the v2 system at their own pace.
4. **Feature Parity**: The v2 system should provide all the features of the v1 system, plus the new component-based approach.

## 2. Folder Structure

We will create a v2 folder structure to separate the new implementation from the existing one:

```
chat-yaml-builder/
├── v1/                           # Existing implementation (moved)
│   ├── streamlit_chat_yaml_builder.py
│   ├── validate_yaml.py
│   ├── pipeline_runner.py
│   └── ...
├── v2/                           # New implementation
│   ├── streamlit_chat_yaml_builder_v2.py
│   ├── component_validator.py
│   ├── registry_validator.py
│   ├── yaml_assembler.py
│   ├── prescriptive_parser.py    # New file for parsing prescriptive text
│   ├── prompts/                  # Component-specific prompts
│   │   ├── roles_prompt.txt
│   │   ├── entities_prompt.txt
│   │   ├── go_definitions_prompt.txt
│   │   └── lo_definitions_prompt.txt
│   ├── templates/                # Prescriptive templates
│   │   ├── roles_prescriptive_template.txt
│   │   ├── entities_prescriptive_template.txt
│   │   ├── go_definitions_prescriptive_template.txt
│   │   └── lo_definitions_prescriptive_template.txt
│   └── ...
├── common/                       # Shared code between v1 and v2
│   ├── db_connection.py
│   ├── save_to_mongo.py
│   ├── id_generator.py
│   └── ...
└── ...                           # Other files
```

## 3. Database Updates

### MongoDB (Design-Time)

We'll add a new field to the MongoDB schema to support v2 documents without disrupting existing ones:

```javascript
// Add a version_type field to distinguish between v1 and v2 documents
{
    // Existing fields remain unchanged
    "version_type": "v2",  // New field to indicate v2 document
    "components": {
        // Component-specific data for v2
        "roles": { /* ... */ },
        "entities": { /* ... */ },
        "go_definitions": { /* ... */ },
        "lo_definitions": { /* ... */ }
    },
    // Chat history for conversational UI
    "chat_history": [
        {
            "role": "user",
            "content": "...",
            "timestamp": "..."
        },
        {
            "role": "system",
            "content": "...",
            "timestamp": "..."
        }
    ]
}
```

### PostgreSQL (Runtime)

For PostgreSQL, we'll add a new column to track the version type:

```sql
-- Add version_type column to relevant tables
ALTER TABLE workflow_runtime.workflow_metadata ADD COLUMN IF NOT EXISTS version_type VARCHAR(10) DEFAULT 'v1';
```

## 4. Component Definitions

### 4.1 Role Definitions

For the enhanced role definitions, we'll follow the template defined in:
`/home/<USER>/workflow-system/chat-yaml-builder/guides/role-template-markdown.md`

This template provides a comprehensive structure for defining roles, including:
- Standard Role Definition Template
- Role Metadata Template (for reference)
- Example Role Definitions (basic and with inheritance)

When implementing the roles component, the system will read from this file to ensure consistency with the defined templates.

### 4.2 Entity Definitions

For the enhanced entity definitions, we'll follow the template defined in:
`/home/<USER>/workflow-system/chat-yaml-builder/guides/ED-Template 1.md`

This template provides a comprehensive structure for defining entities, including:
- Basic Entity Declaration
- Relationships
- Constants & Configuration
- Validations
- Business Rules
- Calculated/Derived Fields
- Hierarchical Dependencies
- Metadata Components
- Circuit Components
- Data Lifecycle Management
- Workflow Definition
- Organizational Placement

When implementing the entities component, the system will read from this file to ensure consistency and completeness in the entity definitions.

### 4.3 GO Definitions

For the enhanced GO definitions, we'll follow the updated template structure that uses name-based references instead of ID-based references and follows the hierarchy: Tenant -> Book -> Chapter -> Global Objectives -> Local Objectives.

The updated GO template includes:
- Core Metadata with classification structure (Global Objective, Book, Chapter, Tenant)
- Process Ownership
- Trigger Definition for system-initiated GOs
- Data Management (Input Stack, Input Mapping Stack, Output Stack, Output Mapping Stack)
- Data Constraints
- Process Definition (Process Flow, Parallel Flows, Rollback Pathways)
- Business Rules
- Integration Points (GO Relationships, External Systems)
- Performance Metadata with SLA thresholds and critical LO performance
- Process Mining Schema with enhanced sections (Event Log Specification, Performance Discovery Metrics, Resource Patterns, Conformance Analytics, Advanced Process Intelligence, Rollback Analytics)
- Sample Data
- Validation Checklist for GO Creation

This updated template provides a more comprehensive and structured approach to defining Global Objectives, with better support for name-based references and enhanced performance monitoring.

### 4.4 LO Definitions

For the enhanced LO definitions, we'll follow the comprehensive guide defined in:
`/home/<USER>/workflow-system/chat-yaml-builder/guides/lo-definition-guide.md`

This guide provides a structured approach to generating high-quality Local Objective (LO) definitions, including:
- Core LO Definition Structure
- Section-by-Section Guidelines
- Common Patterns and Reusable Components
- Best Practices for LO Generation
- Complete Examples

When implementing the LO definitions component, the system will read from this file to ensure consistency and completeness in the LO definitions.

### 4.5 Database Schema Updates for Enhanced Definitions

To support the enhanced definitions, we'll need to update the database schema:

#### MongoDB Updates

```javascript
// Add fields to support enhanced role, GO, and LO definitions
"components": {
    "roles": {
        "prescriptive": "...",  // Store prescriptive text directly
        "structured_data": { /* Parsed data structure */ },  // Parsed from prescriptive text
        "validated": true,
        "errors": [],
        "metadata": {
            "scope": "...",
            "classification": "...",
            "special_conditions": "..."
        }
    },
    "entities": {
        "prescriptive": "...",  // Store prescriptive text directly
        "structured_data": { /* Parsed data structure */ },  // Parsed from prescriptive text
        "validated": true,
        "errors": [],
        "relationships": { /* ... */ },
        "business_rules": { /* ... */ },
        "calculated_fields": { /* ... */ },
        "lifecycle_management": {
            "archive_strategy": { /* ... */ },
            "purge_rules": { /* ... */ },
            "history_tracking": { /* ... */ }
        }
    },
    "go_definitions": {
        "prescriptive": "...",  // Store prescriptive text directly
        "structured_data": { /* Parsed data structure */ },  // Parsed from prescriptive text
        "validated": true,
        "errors": [],
        "process_mining_schema": {
            "event_log_specification": { /* ... */ },
            "performance_discovery_metrics": { /* ... */ },
            "conformance_analytics": { /* ... */ }
        }
    },
    "lo_definitions": {
        "prescriptive": "...",  // Store prescriptive text directly
        "structured_data": { /* Parsed data structure */ },  // Parsed from prescriptive text
        "validated": true,
        "errors": [],
        "ui_stack": {
            "controls": { /* ... */ },
            "dependencies": { /* ... */ },
            "help_text": { /* ... */ }
        },
        "mapping_stack": {
            "mappings": { /* ... */ }
        }
    },
    // For backward compatibility with v1
    "yaml": "..."  // Only for v1 documents
}
```

#### PostgreSQL Updates

```sql
-- Add columns for enhanced role definitions
ALTER TABLE workflow_runtime.roles ADD COLUMN IF NOT EXISTS scope VARCHAR(50);
ALTER TABLE workflow_runtime.roles ADD COLUMN IF NOT EXISTS classification VARCHAR(50);
ALTER TABLE workflow_runtime.roles ADD COLUMN IF NOT EXISTS special_conditions TEXT;

-- Add columns for enhanced entity definitions
ALTER TABLE workflow_runtime.entities ADD COLUMN IF NOT EXISTS metadata JSONB;
ALTER TABLE workflow_runtime.entities ADD COLUMN IF NOT EXISTS lifecycle_management JSONB;
ALTER TABLE workflow_runtime.entity_attributes ADD COLUMN IF NOT EXISTS calculated_field BOOLEAN DEFAULT FALSE;
ALTER TABLE workflow_runtime.entity_attributes ADD COLUMN IF NOT EXISTS calculation_formula TEXT;
ALTER TABLE workflow_runtime.entity_attributes ADD COLUMN IF NOT EXISTS dependencies TEXT[];

-- Add columns for enhanced GO definitions
ALTER TABLE workflow_runtime.global_objectives ADD COLUMN IF NOT EXISTS tenant_name VARCHAR(100);
ALTER TABLE workflow_runtime.global_objectives ADD COLUMN IF NOT EXISTS book_id VARCHAR(50);
ALTER TABLE workflow_runtime.global_objectives ADD COLUMN IF NOT EXISTS book_name VARCHAR(100);
ALTER TABLE workflow_runtime.global_objectives ADD COLUMN IF NOT EXISTS chapter_id VARCHAR(50);
ALTER TABLE workflow_runtime.global_objectives ADD COLUMN IF NOT EXISTS chapter_name VARCHAR(100);
ALTER TABLE workflow_runtime.global_objectives ADD COLUMN IF NOT EXISTS trigger_definition JSONB;
ALTER TABLE workflow_runtime.global_objectives ADD COLUMN IF NOT EXISTS process_mining_schema JSONB;
ALTER TABLE workflow_runtime.global_objectives ADD COLUMN IF NOT EXISTS performance_metadata JSONB;
ALTER TABLE workflow_runtime.global_objectives ADD COLUMN IF NOT EXISTS validation_checklist JSONB;

-- Add columns for enhanced LO definitions
ALTER TABLE workflow_runtime.local_objectives ADD COLUMN IF NOT EXISTS ui_stack JSONB;
ALTER TABLE workflow_runtime.local_objectives ADD COLUMN IF NOT EXISTS mapping_stack JSONB;
ALTER TABLE workflow_runtime.lo_input_items ADD COLUMN IF NOT EXISTS ui_control VARCHAR(50);
ALTER TABLE workflow_runtime.lo_input_items ADD COLUMN IF NOT EXISTS help_text TEXT;
ALTER TABLE workflow_runtime.lo_input_items ADD COLUMN IF NOT EXISTS dependency_info JSONB;

-- Create tables for Books and Chapters
CREATE TABLE IF NOT EXISTS workflow_runtime.books (
    book_id VARCHAR(50) PRIMARY KEY,
    tenant_id VARCHAR(50) REFERENCES workflow_runtime.tenants(tenant_id),
    name VARCHAR(100) NOT NULL,
    description TEXT,
    created_at TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE IF NOT EXISTS workflow_runtime.chapters (
    chapter_id VARCHAR(50) PRIMARY KEY,
    book_id VARCHAR(50) REFERENCES workflow_runtime.books(book_id),
    name VARCHAR(100) NOT NULL,
    description TEXT,
    created_at TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Add foreign key constraints
ALTER TABLE workflow_runtime.global_objectives 
ADD CONSTRAINT global_objectives_book_id_fkey FOREIGN KEY (book_id) REFERENCES workflow_runtime.books(book_id),
ADD CONSTRAINT global_objectives_chapter_id_fkey FOREIGN KEY (chapter_id) REFERENCES workflow_runtime.chapters(chapter_id);
```

## 5. Process Flow and Implementation Sequence

### 5.1 Conversational UI Process Flow

The v2 implementation will use a conversational UI that follows this process:

1. **Entity Generation Phase**
   - User provides input about entities and attributes
   - System generates entities and attributes
   - User iterates and refines
   - System checks for existing entities and uses them
   - System validates entity definitions

2. **GO/LO Generation Phase**
   - System first checks for existing entities to reference in the GOs
   - User provides input about workflows
   - System generates GOs and LOs, strictly using only existing entities
   - User iterates and refines
   - System checks for existing GOs/LOs and updates them if they exist
   - System validates GO/LO definitions
   - System checks for loopholes or issues in the workflow

3. **Role Generation Phase**
   - System first checks for existing entities and GOs to reference in the roles
   - User provides input about roles
   - System generates roles, strictly using only existing entities and GOs
   - User iterates and refines
   - System checks for existing roles and updates them if they exist
   - System validates role definitions
   - System checks for security issues or missing permissions

4. **Deployment Phase**
   - System deploys all components in the correct sequence
   - System provides feedback on deployment success/failure

### 5.2 Implementation Sequence and Current Status

#### Phase 1: Preparation - COMPLETED

1. ✅ Create the new folder structure
   - Created v1, v2, and common directories
   - Created v2/deployers directory for component-specific deployers
   - Added __init__.py files to make proper Python packages

2. ✅ Create common utility functions for both v1 and v2
   - Created db_utils.py for database operations
   - Created id_generator.py for generating and validating IDs
   - Moved shared code to common directory

#### Phase 2: Core Implementation - IN PROGRESS

1. ✅ Implement component-specific validators
   - Created component_validator.py for validating individual components
   - Created registry_validator.py for validating relationships between components

2. ✅ Implement YAML assembler (for backward compatibility only)
   - Created yaml_assembler.py for assembling components into a complete YAML
   - Added support for disassembling a complete YAML into components
   - Added support for converting v1 YAML to v2 components

3. ✅ Implement component-specific deployers
   - Created deployers/role_deployer.py for deploying roles
   - Created deployers/entity_deployer.py for deploying entities
   - Created deployers/go_deployer.py for deploying GO definitions
   - Created deployers/lo_deployer.py for deploying LO definitions
   - Updated go_deployer.py to support name-based references and the new GO template structure
   - Added support for the Trigger Definition section, enhanced Performance Metadata, and Process Mining Schema
   - Added validation for the Validation Checklist

4. ✅ Create component-specific prompts and templates
   - Created prompts/roles_prompt.txt
   - Created prompts/entities_prompt.txt
   - Created prompts/go_definitions_prompt.txt
   - Created prompts/lo_definitions_prompt.txt
   - Created templates/roles_prescriptive_template.txt
   - Created templates/entities_prescriptive_template.txt
   - Created templates/go_definitions_prescriptive_template.txt (updated with new structure)
   - Created templates/lo_definitions_prescriptive_template.txt

5. ✅ Create prescriptive text parser
   - Created prescriptive_parser.py for parsing prescriptive text directly into structured data for database storage
   - This replaces the YAML process for new components, eliminating the double work
   - Updated go_parser.py to support the new GO template structure with name-based references
   - Added support for parsing the Trigger Definition section, enhanced Performance Metadata, and Process Mining Schema
   - Added parsing for the Validation Checklist

6. ✅ Update component_deployer.py to work with prescriptive text and enforce correct sequencing
   - Updated component_deployer.py to work directly with prescriptive text instead of YAML
   - Enforced the sequence: Entities → GOs/LOs → Roles
   - Ensured component deployers check for existing components and update them

7. ✅ Implement system function validation
   - Created system_function_validator.py to validate system functions against available functions
   - Added validation to ensure only valid system functions are used in component definitions
   - Added support for suggesting similar functions when an invalid function is used

8. ✅ Implement output validation
   - Created output_validator.py to validate the output of the ChatGPT API
   - Added validation for system functions, entity references, GO references, and LO references
   - Ensured all references to existing components are valid

9. ✅ Implement prompt generation with database context
   - Created prompt_generator.py to generate prompts with system-specific information
   - Added support for including existing entities, attributes, GOs, LOs, and roles in prompts
   - Ensured prompts include validation requirements to guide the ChatGPT API

#### Phase 3: Database Schema Updates - IN PROGRESS

1. 🔄 Update MongoDB schema to support v2 documents and chat history
   - Need to add version_type field
   - Need to add components field with nested structure
   - Need to add chat_history field for conversational UI

2. ✅ Create tools for PostgreSQL schema migration
   - Created deploy_to_temp_schema.py for deploying sample components to a temporary schema
   - Created generate_migration_script.py for generating SQL migration scripts
   - Created SCHEMA_MIGRATION_README.md with step-by-step instructions
   - Created run_deploy_and_test.py for testing the schema migration
   - Created query_db.py for querying the database and displaying results

3. ✅ Generate migration script for PostgreSQL schema
   - Generated migration_script.sql with all necessary ALTER TABLE statements
   - Script adds version_type column to relevant tables
   - Script adds enhanced fields for roles, entities, GO definitions, and LO definitions
   - Script adds new tables for entity_business_rules, go_lo_mapping, and role_inheritance
   - Script adds new tables for go_performance_metrics, execution_pathways, agent_stack, and lo_nested_functions
   - Script adds new tables for lo_data_mapping_stack and lo_data_mappings
   - Script adds foreign key constraints to ensure data integrity

4. ✅ Fix entity deployer issues with enum values and validations
   - Fixed entity_parser.py to correctly parse enum values from prescriptive text
   - Modified entity_deployer_v2.py to properly handle validations at the entity level
   - Changed validation_name column type in attribute_validations table from VARCHAR(100) to TEXT to support longer validation names
   - Verified that enum values and validations are now correctly saved to the database
   - Tested with complex entity definitions containing multiple enum values and validations

5. 🔄 Apply migration script to workflow_runtime schema
   - Created test_db_completeness.py to verify schema completeness
   - Need to test the migration script in a staging environment
   - Need to apply the migration script to the production environment
   - Need to verify the migration was successful by running test_db_completeness.py
   - Need to populate the schema with sample data using test_sample_files.py

#### Phase 4: UI Implementation - PENDING

1. 🔄 Implement v2 Streamlit UI with conversational interface
   - Need to create streamlit_chat_yaml_builder_v2.py
   - Need to implement the conversational UI process flow
   - Need to add support for component-based approach
   - Need to add validation and feedback for each component

2. 🔄 Implement smart validation and suggestion system
   - Need to create smart_validator.py for detecting potential issues
   - Need to add suggestions for improvements

3. 🔄 Create a launcher script that allows users to choose between v1 and v2
   - Need to create launcher.py

#### Phase 5: Pipeline Integration - PENDING

1. 🔄 Update pipeline runner to support v2 documents
   - Need to create pipeline_runner_v2.py
   - Need to add support for component-based approach

2. 🔄 Implement backward compatibility layer for v1 documents
   - Need to add support for converting v1 YAML to v2 components
   - Need to add support for deploying v1 YAML as v2 components

#### Phase 6: Testing and Deployment - PENDING

1. 🔄 Test v2 implementation with existing YAML files
   - Need to create test cases for v1 YAML files
   - Need to verify backward compatibility

2. 🔄 Test v2 implementation with new component-based approach
   - Need to create test cases for v2 components
   - Need to verify component validation and assembly

3. 🔄 Deploy v2 implementation alongside v1
   - Need to update deployment scripts
   - Need to ensure both v1 and v2 can run side by side

4. 🔄 Create documentation for migrating from v1 to v2
   - Need to create migration guide
   - Need to document new features and improvements

## 6. Transaction Handling for GO and LO Deployments

One important aspect of the implementation is handling the foreign key constraints between Global Objectives (GOs) and Local Objectives (LOs). Since LOs reference GOs through a foreign key constraint, we need to ensure that GOs are deployed before LOs, or that both are deployed in the same transaction.

### Issue

When deploying GOs and LOs separately, the foreign key constraint check can fail if the GO doesn't exist in the database when the LO is being deployed. This can happen even if the GO was just inserted in a previous transaction.

### Solution

We've implemented a solution that uses a single database connection for both GO and LO deployments, with deferred constraint checking:

1. **Deferred Constraint Checking**: We've set the foreign key constraint in the `local_objectives` table to be deferrable and initially deferred. This means the constraint is checked at the end of the transaction, not when the row is inserted.

```sql
CREATE TABLE workflow_temp.local_objectives (
    lo_id VARCHAR(50) PRIMARY KEY,
    go_id VARCHAR(50) REFERENCES workflow_temp.global_objectives(go_id) DEFERRABLE INITIALLY DEFERRED,
    -- other columns
);
```

2. **Connection Sharing**: We've added a new function `deploy_lo_definitions_with_connection` to the `lo_deployer.py` file that accepts an existing database connection. This allows us to use the same connection for both GO and LO deployments.

```python
def deploy_lo_definitions_with_connection(lo_data: Dict, schema_name: str, conn, is_prescriptive: bool = False) -> Tuple[bool, List[str]]:
    """
    Deploy Local Objective (LO) definitions to the database using an existing connection.
    
    Args:
        lo_data: Parsed data for LO definitions (from YAML or prescriptive text)
        schema_name: Schema name to deploy to
        conn: Database connection
        is_prescriptive: Whether the data is from prescriptive text (True) or YAML (False)
        
    Returns:
        Tuple containing:
            - Boolean indicating if deployment was successful
            - List of messages (warnings, errors, or success messages)
    """
    # Implementation details...
```

3. **Transaction Management**: In the `test_components.py` file, we create a single connection for both GO and LO deployments, set the constraints to deferred, and commit the transaction after both deployments are complete.

```python
# Create a single connection for both GO and LO deployments
conn = None
try:
    # Get database connection
    from common.db_connection import get_db_connection
    conn = get_db_connection()
    
    # Set the connection to use deferred constraints
    cursor = conn.cursor()
    cursor.execute("SET CONSTRAINTS ALL DEFERRED")
    
    # Now deploy the LO definitions using the same connection
    lo_deploy_success, lo_deploy_messages = deploy_lo_definitions_with_connection(
        SAMPLE_LO_DEFINITIONS, schema_name, conn
    )
    
    # Commit the transaction
    conn.commit()
    logger.info("Committed transaction for LO deployment")
except Exception as e:
    # Rollback in case of error
    if conn:
        conn.rollback()
    logger.error(f"Transaction error: {str(e)}", exc_info=True)
finally:
    # Close connection
    if conn and not conn.closed:
        conn.close()
```

This solution ensures that both GO and LO deployments are part of the same transaction, and the foreign key constraint is checked only at the end of the transaction, after both the GO and LO have been inserted.

## 7. Testing Strategy

### Component Testing

We've created a comprehensive test script (`test_components.py`) that tests each component of the v2 implementation:

1. **Component Validators**
   - Test validation of roles, entities, GO definitions, and LO definitions
   - Verify that validation rules are correctly applied
   - Check error messages for clarity and helpfulness

2. **Registry Validator**
   - Test validation of relationships between components
   - Verify that cross-component references are correctly validated
   - Check for circular dependencies and other potential issues

3. **Prescriptive Text Parser**
   - Test parsing of prescriptive text into structured data
   - Verify that the parsed data is valid and contains all required fields
   - Test error handling for invalid prescriptive text

4. **YAML Assembler** (for backward compatibility)
   - Test assembly of components into a complete YAML
   - Verify that the assembled YAML is valid and contains all components
   - Test disassembly of a complete YAML into components

4. **ID Generator**
   - Test generation of IDs for different component types
   - Verify that IDs follow the correct pattern
   - Test validation of IDs
   - Test incrementation of IDs when duplicates exist

5. **Component Deployers**
   - Test deployment of roles, entities, GO definitions, and LO definitions
   - Verify that components are correctly stored in the database
   - Test updating existing components
   - Test validation before deployment

6. **Component Deployer (Integration)**
   - Test the central component deployer that uses individual deployers
   - Verify that components are correctly deployed to the temporary schema
   - Test promotion from temporary schema to runtime schema
   - Test rollback of changes in temporary schema

### Running Tests

To run the tests, use the following command:

```bash
python chat-yaml-builder/v2/test_components.py
```

This will run all tests and generate a log file (`test_components.log`) with detailed results.

To test a specific component, use the `--component` flag:

```bash
python chat-yaml-builder/v2/test_components.py --component roles
```

To test a specific test type, use the `--test-type` flag:

```bash
python chat-yaml-builder/v2/test_components.py --test-type validator
```

To use a temporary schema for deployment tests, use the `--use-temp-schema` flag:

```bash
python chat-yaml-builder/v2/test_components.py --use-temp-schema
```

### Unit Tests

1. Test component validators
2. Test prescriptive text parser
3. Test YAML assembler (for backward compatibility)
4. Test backward compatibility with v1 documents

### Integration Tests

1. Test end-to-end workflow with v2 implementation using prescriptive text
2. Test end-to-end workflow with v2 implementation using YAML (for backward compatibility)
3. Test migration from v1 to v2
4. Test backward compatibility with v1 documents

### User Acceptance Testing

1. Test v2 UI with real users using prescriptive text input
2. Test v2 UI with real users using YAML input (for backward compatibility)
3. Gather feedback on component-based approach
4. Gather feedback on prescriptive text vs. YAML approach
5. Validate that v2 implementation meets all requirements

## 8. Rollback Plan

In case of issues with the v2 implementation, we can roll back to the v1 implementation:

1. Revert database schema changes
2. Remove v2 folder
3. Restore v1 files to their original locations

Since we're maintaining backward compatibility, this should be a straightforward process.
