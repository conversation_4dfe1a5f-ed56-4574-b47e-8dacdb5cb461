-- COMPLETE Purchase Furniture Use Case Implementation
-- This script includes ALL required components following exact database patterns
-- GO2: Purchase Furniture with 3 Local Objectives

SET search_path TO workflow_runtime;

-- =====================================================
-- 1. GLOBAL OBJECTIVE (GO2)
-- =====================================================
INSERT INTO global_objectives (
    go_id, name, version, status, description, created_at, updated_at, 
    tenant_id, last_used, deleted_mark, version_type, metadata, auto_id, 
    primary_entity, classification, tenant_name, book_id, book_name, 
    chapter_id, chapter_name, created_by, updated_by, natural_language
) VALUES (
    'GO2', 
    'Purchase Furniture', 
    '1.0', 
    'Active', 
    'Manages furniture purchase process from selection to payment and inventory update', 
    NOW(), 
    NOW(), 
    't001', 
    NOW(), 
    false, 
    'v2', 
    '{}', 
    1834593450, 
    'FurnitureOrder', 
    'workflow', 
    'Acme Corporation', 
    'b002', 
    'Furniture Purchase Management', 
    'c002', 
    'Furniture Purchase Lifecycle', 
    'system', 
    'system', 
    'Purchase Furniture workflow for managing furniture orders'
);

-- =====================================================
-- 2. LOCAL OBJECTIVES (GO2.LO1, GO2.LO2, GO2.LO3)
-- =====================================================
INSERT INTO local_objectives (
    lo_id, go_id, name, description, version, status, created_at, updated_at, 
    tenant_id, created_by, updated_by, natural_language
) VALUES 
('GO2.LO1', 'GO2', 'SelectFurnitureType', 'Local Objective: SelectFurnitureType', '1.0', 'Active', NOW(), NOW(), 't001', 'system', 'system', 'User selects furniture type and product from available inventory'),
('GO2.LO2', 'GO2', 'ProcessCartPayment', 'Local Objective: ProcessCartPayment', '1.0', 'Active', NOW(), NOW(), 't001', 'system', 'system', 'Process cart with quantity calculations and payment method selection'),
('GO2.LO3', 'GO2', 'CompleteOrderInventory', 'Local Objective: CompleteOrderInventory', '1.0', 'Active', NOW(), NOW(), 't001', 'system', 'system', 'Complete order placement and update inventory levels');

-- =====================================================
-- 3. ENTITIES (E13, E14, E15, E16)
-- =====================================================
INSERT INTO entities (
    entity_id, name, display_name, version, status, type, description, 
    created_at, updated_at, created_by, updated_by, table_name, tenant_id, 
    tenant_name, business_domain, category, archival_strategy, icon, 
    colour_theme, natural_language
) VALUES 
('E13', 'FurnitureOrder', 'Furniture Order', 1, 'Active', 'transactional', 'Represents a furniture purchase order', NOW(), NOW(), 'system', 'system', 'e13_furnitureorder', 't001', 'Acme Corporation', 'Sales', 'Order Management', 'archive_after_completion', 'shopping-cart', 'blue', 'Primary entity for furniture purchase orders'),
('E14', 'FurnitureProduct', 'Furniture Product', 1, 'Active', 'master', 'Represents furniture products available for purchase', NOW(), NOW(), 'system', 'system', 'e14_furnitureproduct', 't001', 'Acme Corporation', 'Inventory', 'Product Catalog', 'retain_permanently', 'package', 'green', 'Master data for furniture products'),
('E15', 'FurnitureType', 'Furniture Type', 1, 'Active', 'master', 'Represents different types of furniture categories', NOW(), NOW(), 'system', 'system', 'e15_furnituretype', 't001', 'Acme Corporation', 'Inventory', 'Product Classification', 'retain_permanently', 'grid', 'purple', 'Master data for furniture categories'),
('E16', 'PaymentDetails', 'Payment Details', 1, 'Active', 'transactional', 'Stores payment information for furniture orders', NOW(), NOW(), 'system', 'system', 'e16_paymentdetails', 't001', 'Acme Corporation', 'Finance', 'Payment Processing', 'archive_after_audit', 'credit-card', 'orange', 'Payment details for furniture orders');

-- =====================================================
-- 4. ENTITY ATTRIBUTES (Following exact column structure)
-- =====================================================

-- E13: FurnitureOrder Attributes
INSERT INTO entity_attributes (
    attribute_id, entity_id, name, display_name, datatype, is_required, 
    is_primary_key, is_foreign_key, is_unique, default_value, description, 
    is_calculated, calculation_formula, version, status, required, 
    calculated_field, created_at, updated_at, created_by, updated_by, 
    default_type, helper_text, natural_language
) VALUES 
('E13.At1', 'E13', 'orderid', 'Order ID', 'VARCHAR(255)', true, true, false, true, NULL, 'Unique order identifier', false, NULL, 1, 'Active', true, false, NOW(), NOW(), 'system', 'system', NULL, 'Unique identifier for the furniture order', 'Primary key for furniture orders'),
('E13.At2', 'E13', 'userid', 'User ID', 'VARCHAR(255)', true, false, true, false, NULL, 'Reference to user who placed the order', false, NULL, 1, 'Active', true, false, NOW(), NOW(), 'system', 'system', NULL, 'Links to the user table', 'Foreign key reference to users'),
('E13.At3', 'E13', 'furnituretypeid', 'Furniture Type ID', 'VARCHAR(255)', true, false, true, false, NULL, 'Reference to furniture type', false, NULL, 1, 'Active', true, false, NOW(), NOW(), 'system', 'system', NULL, 'Links to furniture type', 'Foreign key to furniture type'),
('E13.At4', 'E13', 'productid', 'Product ID', 'VARCHAR(255)', true, false, true, false, NULL, 'Reference to selected product', false, NULL, 1, 'Active', true, false, NOW(), NOW(), 'system', 'system', NULL, 'Links to product catalog', 'Foreign key to furniture product'),
('E13.At5', 'E13', 'quantity', 'Quantity', 'INTEGER', true, false, false, false, '1', 'Number of items ordered', false, NULL, 1, 'Active', true, false, NOW(), NOW(), 'system', 'system', 'default', 'Enter quantity to order', 'Quantity of items in the order'),
('E13.At6', 'E13', 'unitprice', 'Unit Price', 'DECIMAL(10,2)', true, false, false, false, NULL, 'Price per unit', false, NULL, 1, 'Active', true, false, NOW(), NOW(), 'system', 'system', NULL, 'Price per individual item', 'Unit price from product catalog'),
('E13.At7', 'E13', 'subtotal', 'Subtotal', 'DECIMAL(10,2)', true, false, false, false, NULL, 'Quantity × Unit Price', true, 'quantity * unitprice', 1, 'Active', true, true, NOW(), NOW(), 'system', 'system', 'calculated', 'Automatically calculated', 'Calculated field: quantity × unit price'),
('E13.At8', 'E13', 'gstamount', 'GST Amount', 'DECIMAL(10,2)', true, false, false, false, NULL, 'GST calculated at 18%', true, 'subtotal * 0.18', 1, 'Active', true, true, NOW(), NOW(), 'system', 'system', 'calculated', 'GST at 18%', 'Calculated GST amount'),
('E13.At9', 'E13', 'totalamount', 'Total Amount', 'DECIMAL(10,2)', true, false, false, false, NULL, 'Subtotal + GST Amount', true, 'subtotal + gstamount', 1, 'Active', true, true, NOW(), NOW(), 'system', 'system', 'calculated', 'Final total amount', 'Total including GST'),
('E13.At10', 'E13', 'paymentmethod', 'Payment Method', 'VARCHAR(20)', true, false, false, false, NULL, 'UPI or Credit Card', false, NULL, 1, 'Active', true, false, NOW(), NOW(), 'system', 'system', NULL, 'Select payment method', 'Payment method selection'),
('E13.At11', 'E13', 'orderstatus', 'Order Status', 'VARCHAR(20)', true, false, false, false, 'Pending', 'Current status of the order', false, NULL, 1, 'Active', true, false, NOW(), NOW(), 'system', 'system', 'default', 'Order processing status', 'Current order status'),
('E13.At12', 'E13', 'orderdate', 'Order Date', 'TIMESTAMP', true, false, false, false, 'NOW()', 'Date when order was placed', false, NULL, 1, 'Active', true, false, NOW(), NOW(), 'system', 'system', 'system', 'Automatically set', 'Order placement timestamp');

-- E14: FurnitureProduct Attributes
INSERT INTO entity_attributes (
    attribute_id, entity_id, name, display_name, datatype, is_required, 
    is_primary_key, is_foreign_key, is_unique, default_value, description, 
    is_calculated, calculation_formula, version, status, required, 
    calculated_field, created_at, updated_at, created_by, updated_by, 
    default_type, helper_text, natural_language
) VALUES 
('E14.At1', 'E14', 'productid', 'Product ID', 'VARCHAR(255)', true, true, false, true, NULL, 'Unique product identifier', false, NULL, 1, 'Active', true, false, NOW(), NOW(), 'system', 'system', NULL, 'Unique product code', 'Primary key for products'),
('E14.At2', 'E14', 'productname', 'Product Name', 'VARCHAR(255)', true, false, false, false, NULL, 'Name of the furniture product', false, NULL, 1, 'Active', true, false, NOW(), NOW(), 'system', 'system', NULL, 'Display name of product', 'Product display name'),
('E14.At3', 'E14', 'furnituretypeid', 'Furniture Type ID', 'VARCHAR(255)', true, false, true, false, NULL, 'Reference to furniture type', false, NULL, 1, 'Active', true, false, NOW(), NOW(), 'system', 'system', NULL, 'Product category', 'Links to furniture type'),
('E14.At4', 'E14', 'price', 'Price', 'DECIMAL(10,2)', true, false, false, false, NULL, 'Product price', false, NULL, 1, 'Active', true, false, NOW(), NOW(), 'system', 'system', NULL, 'Product selling price', 'Unit price of the product'),
('E14.At5', 'E14', 'availableinventory', 'Available Inventory', 'INTEGER', true, false, false, false, '0', 'Current stock available', false, NULL, 1, 'Active', true, false, NOW(), NOW(), 'system', 'system', 'default', 'Stock quantity', 'Current inventory count'),
('E14.At6', 'E14', 'description', 'Description', 'TEXT', false, false, false, false, NULL, 'Product description', false, NULL, 1, 'Active', false, false, NOW(), NOW(), 'system', 'system', NULL, 'Product details', 'Detailed product description');

-- E15: FurnitureType Attributes
INSERT INTO entity_attributes (
    attribute_id, entity_id, name, display_name, datatype, is_required, 
    is_primary_key, is_foreign_key, is_unique, default_value, description, 
    is_calculated, calculation_formula, version, status, required, 
    calculated_field, created_at, updated_at, created_by, updated_by, 
    default_type, helper_text, natural_language
) VALUES 
('E15.At1', 'E15', 'furnituretypeid', 'Furniture Type ID', 'VARCHAR(255)', true, true, false, true, NULL, 'Unique furniture type identifier', false, NULL, 1, 'Active', true, false, NOW(), NOW(), 'system', 'system', NULL, 'Unique type code', 'Primary key for furniture types'),
('E15.At2', 'E15', 'typename', 'Type Name', 'VARCHAR(100)', true, false, false, false, NULL, 'Name of furniture type', false, NULL, 1, 'Active', true, false, NOW(), NOW(), 'system', 'system', NULL, 'Category name', 'Display name for furniture category'),
('E15.At3', 'E15', 'description', 'Description', 'TEXT', false, false, false, false, NULL, 'Furniture type description', false, NULL, 1, 'Active', false, false, NOW(), NOW(), 'system', 'system', NULL, 'Category description', 'Detailed type description');

-- E16: PaymentDetails Attributes
INSERT INTO entity_attributes (
    attribute_id, entity_id, name, display_name, datatype, is_required, 
    is_primary_key, is_foreign_key, is_unique, default_value, description, 
    is_calculated, calculation_formula, version, status, required, 
    calculated_field, created_at, updated_at, created_by, updated_by, 
    default_type, helper_text, natural_language
) VALUES 
('E16.At1', 'E16', 'paymentid', 'Payment ID', 'VARCHAR(255)', true, true, false, true, NULL, 'Unique payment identifier', false, NULL, 1, 'Active', true, false, NOW(), NOW(), 'system', 'system', NULL, 'Unique payment reference', 'Primary key for payments'),
('E16.At2', 'E16', 'orderid', 'Order ID', 'VARCHAR(255)', true, false, true, false, NULL, 'Reference to furniture order', false, NULL, 1, 'Active', true, false, NOW(), NOW(), 'system', 'system', NULL, 'Links to order', 'Foreign key to furniture order'),
('E16.At3', 'E16', 'paymentmethod', 'Payment Method', 'VARCHAR(20)', true, false, false, false, NULL, 'UPI or Credit Card', false, NULL, 1, 'Active', true, false, NOW(), NOW(), 'system', 'system', NULL, 'Payment type', 'Method of payment'),
('E16.At4', 'E16', 'upiid', 'UPI ID', 'VARCHAR(100)', false, false, false, false, NULL, 'UPI ID for UPI payments', false, NULL, 1, 'Active', false, false, NOW(), NOW(), 'system', 'system', NULL, 'UPI identifier', 'UPI payment ID'),
('E16.At5', 'E16', 'cardname', 'Card Name', 'VARCHAR(100)', false, false, false, false, NULL, 'Name on credit card', false, NULL, 1, 'Active', false, false, NOW(), NOW(), 'system', 'system', NULL, 'Cardholder name', 'Name on the credit card'),
('E16.At6', 'E16', 'cardnumber', 'Card Number', 'VARCHAR(20)', false, false, false, false, NULL, 'Credit card number', false, NULL, 1, 'Active', false, false, NOW(), NOW(), 'system', 'system', NULL, 'Card number', 'Credit card number'),
('E16.At7', 'E16', 'cvv', 'CVV', 'VARCHAR(4)', false, false, false, false, NULL, 'Credit card CVV', false, NULL, 1, 'Active', false, false, NOW(), NOW(), 'system', 'system', NULL, 'Security code', 'Card verification value'),
('E16.At8', 'E16', 'expirydate', 'Expiry Date', 'VARCHAR(7)', false, false, false, false, NULL, 'Credit card expiry (MM/YYYY)', false, NULL, 1, 'Active', false, false, NOW(), NOW(), 'system', 'system', NULL, 'Card expiry', 'Card expiration date'),
('E16.At9', 'E16', 'paymentstatus', 'Payment Status', 'VARCHAR(20)', true, false, false, false, 'Pending', 'Status of payment', false, NULL, 1, 'Active', true, false, NOW(), NOW(), 'system', 'system', 'default', 'Payment processing status', 'Current payment status');

-- =====================================================
-- 5. EXECUTION PATHWAYS
-- =====================================================
INSERT INTO execution_pathways (
    id, execution_pathway_id, lo_id, pathway_type, next_lo, created_at, 
    created_by, updated_at, updated_by, version, natural_language
) VALUES 
('GO2.LO1.EP1_ID', 'GO2.LO1.EP1', 'GO2.LO1', 'SEQUENTIAL', 'GO2.LO2', NOW(), 'system', NOW(), 'system', '1.0', 'After furniture selection, proceed to cart and payment processing'),
('GO2.LO2.EP2_ID', 'GO2.LO2.EP2', 'GO2.LO2', 'SEQUENTIAL', 'GO2.LO3', NOW(), 'system', NOW(), 'system', '1.0', 'After payment processing, complete the order and update inventory'),
('GO2.LO3.EP3_ID', 'GO2.LO3.EP3', 'GO2.LO3', 'terminal', NULL, NOW(), 'system', NOW(), 'system', '1.0', 'Order completion is terminal - workflow ends');

-- =====================================================
-- 6. CONSTANTS
-- =====================================================
INSERT INTO constants (
    constant_id, attribute, value, description, tenant_id, allow_override, 
    override_permissions, created_at, updated_at, created_by, updated_by, 
    status, entity_name, attribute_name, auto_id
) VALUES 
('GST_RATE', 'gst_percentage', '18', 'GST rate for furniture purchases', 't001', false, NULL, NOW(), NOW(), 'system', 'system', 'Active', 'FurnitureOrder', 'gstamount', 1001),
('PAYMENT_METHODS', 'allowed_methods', '["UPI", "Credit Card"]', 'Allowed payment methods for furniture orders', 't001', false, NULL, NOW(), NOW(), 'system', 'system', 'Active', 'FurnitureOrder', 'paymentmethod', 1002);

-- =====================================================
-- 7. LO INPUT STACKS
-- =====================================================
INSERT INTO lo_input_stack (
    lo_id, lo_input_stack_id, description, created_at, updated_at, 
    created_by, updated_by, version, natural_language
) VALUES 
('GO2.LO1', 'GO2.LO1.IP1', 'Furniture selection input stack', NOW(), NOW(), 'system', 'system', '1.0', 'Input stack for furniture type and product selection'),
('GO2.LO2', 'GO2.LO2.IP1', 'Cart and payment input stack', NOW(), NOW(), 'system', 'system', '1.0', 'Input stack for quantity and payment processing'),
('GO2.LO3', 'GO2.LO3.IP1', 'Order completion input stack', NOW(), NOW(), 'system', 'system', '1.0', 'Input stack for order finalization');

-- =====================================================
-- 8. LO INPUT ITEMS
-- =====================================================

-- GO2.LO1 Input Items (Furniture Selection)
INSERT INTO lo_input_items (
    item_id, input_stack_id, slot_id, source_type, source_description, required, 
    lo_id, data_type, ui_control, nested_function_id, is_visible, name, type, 
    created_at, updated_at, created_by, updated_by, read_only, agent_type, 
    dependent_attribute, dependent_attribute_value, enum_values, default_value, 
    information_field, constant_field, entity_id, attribute_id, entity_name, 
    attribute_name, version, natural_language
) VALUES 
('GO2.LO1.IP1.IT1', 'GO2.LO1.IP1', 'GO2.LO1.SL1', 'user', 'User selects furniture type', true, 'GO2.LO1', 'string', 'dropdown', NULL, true, 'furnituretypeid', 'input', NOW(), NOW(), 'system', 'system', false, 'user', false, NULL, NULL, NULL, false, false, 'E15', 'E15.At1', 'FurnitureType', 'furnituretypeid', '1.0', 'Dropdown for furniture type selection'),
('GO2.LO1.IP1.IT2', 'GO2.LO1.IP1', 'GO2.LO1.SL2', 'user', 'User selects specific product', true, 'GO2.LO1', 'string', 'dropdown', NULL, true, 'productid', 'input', NOW(), NOW(), 'system', 'system', false, 'user', true, 'furnituretypeid', NULL, NULL, false, false, 'E14', 'E14.At1', 'FurnitureProduct', 'productid', '1.0', 'Dependent dropdown for product selection based on furniture type'),
('GO2.LO1.IP1.IT3', 'GO2.LO1.IP1', 'GO2.LO1.SL3', 'information', 'Display selected product information', false, 'GO2.LO1', 'string', 'label', NULL, true, 'productname', 'information', NOW(), NOW(), 'system', 'system', true, 'system', false, NULL, NULL, NULL, true, false, 'E14', 'E14.At2', 'FurnitureProduct', 'productname', '1.0', 'Information field showing selected product name'),
('GO2.LO1.IP1.IT4', 'GO2.LO1.IP1', 'GO2.LO1.SL4', 'information', 'Display product price', false, 'GO2.LO1', 'decimal', 'label', NULL, true, 'unitprice', 'information', NOW(), NOW(), 'system', 'system', true, 'system', false, NULL, NULL, NULL, true, false, 'E14', 'E14.At4', 'FurnitureProduct', 'price', '1.0', 'Information field showing product price'),
('GO2.LO1.IP1.IT5', 'GO2.LO1.IP1', 'GO2.LO1.SL5', 'information', 'Display available inventory', false, 'GO2.LO1', 'integer', 'label', NULL, true, 'availableinventory', 'information', NOW(), NOW(), 'system', 'system', true, 'system', false, NULL, NULL, NULL, true, false, 'E14', 'E14.At5', 'FurnitureProduct', 'availableinventory', '1.0', 'Information field showing available stock');

-- GO2.LO2 Input Items (Cart and Payment)
INSERT INTO lo_input_items (
    item_id, input_stack_id, slot_id, source_type, source_description, required, 
    lo_id, data_type, ui_control, nested_function_id, is_visible, name, type, 
    created_at, updated_at, created_by, updated_by, read_only, agent_type, 
    dependent_attribute, dependent_attribute_value, enum_values, default_value, 
    information_field, constant_field, entity_id, attribute_id, entity_name, 
    attribute_name, version, natural_language
) VALUES 
('GO2.LO2.IP1.IT1', 'GO2.LO2.IP1', 'GO2.LO2.SL1', 'mapping', 'Mapped from GO2.LO1 selection', false, 'GO2.LO2', 'string', 'label', NULL, true, 'productname', 'mapping', NOW(), NOW(), 'system', 'system', true, 'system', false, NULL, NULL, NULL, true, false, 'E14', 'E14.At2', 'FurnitureProduct', 'productname', '1.0', 'Product name mapped from previous step'),
('GO2.LO2.IP1.IT2', 'GO2.LO2.IP1', 'GO2.LO2.SL2', 'mapping', 'Mapped unit price from GO2.LO1', false, 'GO2.LO2', 'decimal', 'label', NULL, true, 'unitprice', 'mapping', NOW(), NOW(), 'system', 'system', true, 'system', false, NULL, NULL, NULL, true, false, 'E14', 'E14.At4', 'FurnitureProduct', 'price', '1.0', 'Unit price mapped from product selection'),
('GO2.LO2.IP1.IT3', 'GO2.LO2.IP1', 'GO2.LO2.SL3', 'user', 'User enters quantity', true, 'GO2.LO2', 'integer', 'integer', NULL, true, 'quantity', 'input', NOW(), NOW(), 'system', 'system', false, 'user', false, NULL, NULL, '1', false, false, 'E13', 'E13.At5', 'FurnitureOrder', 'quantity', '1.0', 'User input for order quantity'),
('GO2.LO2.IP1.IT4', 'GO2.LO2.IP1', 'GO2.LO2.SL4', 'nested_function', 'Calculate subtotal', false, 'GO2.LO2', 'decimal', 'label', 'NF_CALCULATE_SUBTOTAL', true, 'subtotal', 'calculated', NOW(), NOW(), 'system', 'system', true, 'system', false, NULL, NULL, NULL, false, false, 'E13', 'E13.At7', 'FurnitureOrder', 'subtotal', '1.0', 'Calculated subtotal using nested function'),
('GO2.LO2.IP1.IT5', 'GO2.LO2.IP1', 'GO2.LO2.SL5', 'nested_function', 'Calculate GST amount', false, 'GO2.LO2', 'decimal', 'label', 'NF_CALCULATE_GST', true, 'gstamount', 'calculated', NOW(), NOW(), 'system', 'system', true, 'system', false, NULL, NULL, NULL, false, false, 'E13', 'E13.At8', 'FurnitureOrder', 'gstamount', '1.0', 'Calculated GST using nested function'),
('GO2.LO2.IP1.IT6', 'GO2.LO2.IP1', 'GO2.LO2.SL6', 'nested_function', 'Calculate total amount', false, 'GO2.LO2', 'decimal', 'label', 'NF_CALCULATE_TOTAL', true, 'totalamount', 'calculated', NOW(), NOW(), 'system', 'system', true, 'system', false, NULL, NULL, NULL, false, false, 'E13', 'E13.At9', 'FurnitureOrder', 'totalamount', '1.0', 'Calculated total using nested function'),
('GO2.LO2.IP1.IT7', 'GO2.LO2.IP1', 'GO2.LO2.SL7', 'user', 'User selects payment method', true, 'GO2.LO2', 'string', 'dropdown', NULL, true, 'paymentmethod', 'input', NOW(), NOW(), 'system', 'system', false, 'user', false, NULL, '["UPI", "Credit Card"]', NULL, false, false, 'E13', 'E13.At10', 'FurnitureOrder', 'paymentmethod', '1.0', 'Payment method selection dropdown'),
('GO2.LO2.IP1.IT8', 'GO2.LO2.IP1', 'GO2.LO2.SL8', 'user', 'UPI ID for UPI payments', false, 'GO2.LO2', 'string', 'text', NULL, true, 'upiid', 'input', NOW(), NOW(), 'system', 'system', false, 'user', true, 'UPI', NULL, NULL, false, false, 'E16', 'E16.At4', 'PaymentDetails', 'upiid', '1.0', 'UPI ID input when UPI is selected'),
('GO2.LO2.IP1.IT9', 'GO2.LO2.IP1', 'GO2.LO2.SL9', 'user', 'Card name for credit card', false, 'GO2.LO2', 'string', 'text', NULL, true, 'cardname', 'input', NOW(), NOW(), 'system', 'system', false, 'user', true, 'Credit Card', NULL, NULL, false, false, 'E16', 'E16.At5', 'PaymentDetails', 'cardname', '1.0', 'Card name when credit card is selected'),
('GO2.LO2.IP1.IT10', 'GO2.LO2.IP1', 'GO2.LO2.SL10', 'user', 'Card number for credit card', false, 'GO2.LO2', 'string', 'text', NULL, true, 'cardnumber', 'input', NOW(), NOW(), 'system', 'system', false, 'user', true, 'Credit Card', NULL, NULL, false, false, 'E16', 'E16.At6', 'PaymentDetails', 'cardnumber', '1.0', 'Card number when credit card is selected'),
('GO2.LO2.IP1.IT11', 'GO2.LO2.IP1', 'GO2.LO2.SL11', 'user', 'CVV for credit card', false, 'GO2.LO2', 'string', 'text', NULL, true, 'cvv', 'input', NOW(), NOW(), 'system', 'system', false, 'user', true, 'Credit Card', NULL, NULL, false, false, 'E16', 'E16.At7', 'PaymentDetails', 'cvv', '1.0', 'CVV when credit card is selected'),
('GO2.LO2.IP1.IT12', 'GO2.LO2.IP1', 'GO2.LO2.SL12', 'user', 'Expiry date for credit card', false, 'GO2.LO2', 'string', 'text', NULL, true, 'expirydate', 'input', NOW(), NOW(), 'system', 'system', false, 'user', true, 'Credit Card', NULL, NULL, false, false, 'E16', 'E16.At8', 'PaymentDetails', 'expirydate', '1.0', 'Expiry date when credit card is selected');

-- GO2.LO3 Input Items (Order Completion)
INSERT INTO lo_input_items (
    item_id, input_stack_id, slot_id, source_type, source_description, required, 
    lo_id, data_type, ui_control, nested_function_id, is_visible, name, type, 
    created_at, updated_at, created_by, updated_by, read_only, agent_type, 
    dependent_attribute, dependent_attribute_value, enum_values, default_value, 
    information_field, constant_field, entity_id, attribute_id, entity_name, 
    attribute_name, version, natural_language
) VALUES 
('GO2.LO3.IP1.IT1', 'GO2.LO3.IP1', 'GO2.LO3.SL1', 'mapping', 'Order summary from previous steps', false, 'GO2.LO3', 'string', 'label', NULL, true, 'ordersummary', 'mapping', NOW(), NOW(), 'system', 'system', true, 'system', false, NULL, NULL, NULL, true, false, 'E13', 'E13.At9', 'FurnitureOrder', 'totalamount', '1.0', 'Order summary mapped from previous steps'),
('GO2.LO3.IP1.IT2', 'GO2.LO3.IP1', 'GO2.LO3.SL2', 'system', 'Generate order ID', false, 'GO2.LO3', 'string', 'label', 'NF_GENERATE_ORDER_ID', true, 'orderid', 'system', NOW(), NOW(), 'system', 'system', true, 'system', false, NULL, NULL, NULL, false, false, 'E13', 'E13.At1', 'FurnitureOrder', 'orderid', '1.0', 'System generated order ID'),
('GO2.LO3.IP1.IT3', 'GO2.LO3.IP1', 'GO2.LO3.SL3', 'system', 'Update inventory', false, 'GO2.LO3', 'boolean', 'checkbox', 'NF_UPDATE_INVENTORY', true, 'inventoryupdated', 'system', NOW(), NOW(), 'system', 'system', true, 'system', false, NULL, NULL, 'false', false, false, 'E14', 'E14.At5', 'FurnitureProduct', 'availableinventory', '1.0', 'System updates inventory levels');

-- =====================================================
-- 9. LO OUTPUT STACKS
-- =====================================================
INSERT INTO lo_output_stack (
    lo_id, lo_output_stack_id, description, created_at, updated_at, 
    created_by, updated_by, version, natural_language
) VALUES 
('GO2.LO1', 'GO2.LO1.OP1', 'Furniture selection output stack', NOW(), NOW(), 'system', 'system', '1.0', 'Output stack for selected furniture details'),
('GO2.LO2', 'GO2.LO2.OP1', 'Cart and payment output stack', NOW(), NOW(), 'system', 'system', '1.0', 'Output stack for calculated amounts and payment details'),
('GO2.LO3', 'GO2.LO3.OP1', 'Order completion output stack', NOW(), NOW(), 'system', 'system', '1.0', 'Output stack for order confirmation');

-- =====================================================
-- 10. LO OUTPUT ITEMS
-- =====================================================

-- GO2.LO1 Output Items
INSERT INTO lo_output_items (
    item_id, output_stack_id, slot_id, source_type, source_description, 
    lo_id, data_type, ui_control, is_visible, name, type, 
    created_at, updated_at, created_by, updated_by, entity_id, attribute_id, 
    entity_name, attribute_name, version, natural_language
) VALUES 
('GO2.LO1.OP1.IT1', 'GO2.LO1.OP1', 'GO2.LO1.OSL1', 'output', 'Selected furniture type ID', 'GO2.LO1', 'string', 'label', true, 'furnituretypeid', 'output', NOW(), NOW(), 'system', 'system', 'E15', 'E15.At1', 'FurnitureType', 'furnituretypeid', '1.0', 'Output furniture type selection'),
('GO2.LO1.OP1.IT2', 'GO2.LO1.OP1', 'GO2.LO1.OSL2', 'output', 'Selected product ID', 'GO2.LO1', 'string', 'label', true, 'productid', 'output', NOW(), NOW(), 'system', 'system', 'E14', 'E14.At1', 'FurnitureProduct', 'productid', '1.0', 'Output product selection'),
('GO2.LO1.OP1.IT3', 'GO2.LO1.OP1', 'GO2.LO1.OSL3', 'output', 'Product name', 'GO2.LO1', 'string', 'label', true, 'productname', 'output', NOW(), NOW(), 'system', 'system', 'E14', 'E14.At2', 'FurnitureProduct', 'productname', '1.0', 'Output product name'),
('GO2.LO1.OP1.IT4', 'GO2.LO1.OP1', 'GO2.LO1.OSL4', 'output', 'Unit price', 'GO2.LO1', 'decimal', 'label', true, 'unitprice', 'output', NOW(), NOW(), 'system', 'system', 'E14', 'E14.At4', 'FurnitureProduct', 'price', '1.0', 'Output unit price');

-- GO2.LO2 Output Items
INSERT INTO lo_output_items (
    item_id, output_stack_id, slot_id, source_type, source_description, 
    lo_id, data_type, ui_control, is_visible, name, type, 
    created_at, updated_at, created_by, updated_by, entity_id, attribute_id, 
    entity_name, attribute_name, version, natural_language
) VALUES 
('GO2.LO2.OP1.IT1', 'GO2.LO2.OP1', 'GO2.LO2.OSL1', 'output', 'Order quantity', 'GO2.LO2', 'integer', 'label', true, 'quantity', 'output', NOW(), NOW(), 'system', 'system', 'E13', 'E13.At5', 'FurnitureOrder', 'quantity', '1.0', 'Output order quantity'),
('GO2.LO2.OP1.IT2', 'GO2.LO2.OP1', 'GO2.LO2.OSL2', 'output', 'Calculated subtotal', 'GO2.LO2', 'decimal', 'label', true, 'subtotal', 'output', NOW(), NOW(), 'system', 'system', 'E13', 'E13.At7', 'FurnitureOrder', 'subtotal', '1.0', 'Output calculated subtotal'),
('GO2.LO2.OP1.IT3', 'GO2.LO2.OP1', 'GO2.LO2.OSL3', 'output', 'GST amount', 'GO2.LO2', 'decimal', 'label', true, 'gstamount', 'output', NOW(), NOW(), 'system', 'system', 'E13', 'E13.At8', 'FurnitureOrder', 'gstamount', '1.0', 'Output GST amount'),
('GO2.LO2.OP1.IT4', 'GO2.LO2.OP1', 'GO2.LO2.OSL4', 'output', 'Total amount', 'GO2.LO2', 'decimal', 'label', true, 'totalamount', 'output', NOW(), NOW(), 'system', 'system', 'E13', 'E13.At9', 'FurnitureOrder', 'totalamount', '1.0', 'Output total amount'),
('GO2.LO2.OP1.IT5', 'GO2.LO2.OP1', 'GO2.LO2.OSL5', 'output', 'Payment method', 'GO2.LO2', 'string', 'label', true, 'paymentmethod', 'output', NOW(), NOW(), 'system', 'system', 'E13', 'E13.At10', 'FurnitureOrder', 'paymentmethod', '1.0', 'Output payment method'),
('GO2.LO2.OP1.IT6', 'GO2.LO2.OP1', 'GO2.LO2.OSL6', 'output', 'Payment details', 'GO2.LO2', 'string', 'label', true, 'paymentdetails', 'output', NOW(), NOW(), 'system', 'system', 'E16', 'E16.At1', 'PaymentDetails', 'paymentid', '1.0', 'Output payment details');

-- GO2.LO3 Output Items
INSERT INTO lo_output_items (
    item_id, output_stack_id, slot_id, source_type, source_description, 
    lo_id, data_type, ui_control, is_visible, name, type, 
    created_at, updated_at, created_by, updated_by, entity_id, attribute_id, 
    entity_name, attribute_name, version, natural_language
) VALUES 
('GO2.LO3.OP1.IT1', 'GO2.LO3.OP1', 'GO2.LO3.OSL1', 'output', 'Order ID', 'GO2.LO3', 'string', 'label', true, 'orderid', 'output', NOW(), NOW(), 'system', 'system', 'E13', 'E13.At1', 'FurnitureOrder', 'orderid', '1.0', 'Output order ID'),
('GO2.LO3.OP1.IT2', 'GO2.LO3.OP1', 'GO2.LO3.OSL2', 'output', 'Order status', 'GO2.LO3', 'string', 'label', true, 'orderstatus', 'output', NOW(), NOW(), 'system', 'system', 'E13', 'E13.At11', 'FurnitureOrder', 'orderstatus', '1.0', 'Output order status'),
('GO2.LO3.OP1.IT3', 'GO2.LO3.OP1', 'GO2.LO3.OSL3', 'output', 'Inventory updated', 'GO2.LO3', 'boolean', 'label', true, 'inventoryupdated', 'output', NOW(), NOW(), 'system', 'system', 'E14', 'E14.At5', 'FurnitureProduct', 'availableinventory', '1.0', 'Output inventory update status');

-- =====================================================
-- 11. NESTED FUNCTIONS
-- =====================================================
INSERT INTO lo_nested_functions (
    nested_function_id, lo_id, function_name, function_type, description, 
    input_parameters, output_parameters, calculation_logic, created_at, 
    updated_at, created_by, updated_by, version, natural_language
) VALUES 
('NF_CALCULATE_SUBTOTAL', 'GO2.LO2', 'Calculate Subtotal', 'CALCULATE', 'Multiply quantity by unit price', 
 '{"quantity": "integer", "unitprice": "decimal"}', 
 '{"subtotal": "decimal"}', 
 'quantity * unitprice', NOW(), NOW(), 'system', 'system', '1.0', 
 'Nested function to calculate subtotal by multiplying quantity and unit price'),
('NF_CALCULATE_GST', 'GO2.LO2', 'Calculate GST', 'CALCULATE', 'Calculate 18% GST on subtotal', 
 '{"subtotal": "decimal"}', 
 '{"gstamount": "decimal"}', 
 'subtotal * 0.18', NOW(), NOW(), 'system', 'system', '1.0', 
 'Nested function to calculate GST at 18% rate'),
('NF_CALCULATE_TOTAL', 'GO2.LO2', 'Calculate Total', 'CALCULATE', 'Add subtotal and GST amount', 
 '{"subtotal": "decimal", "gstamount": "decimal"}', 
 '{"totalamount": "decimal"}', 
 'subtotal + gstamount', NOW(), NOW(), 'system', 'system', '1.0', 
 'Nested function to calculate total amount including GST'),
('NF_GENERATE_ORDER_ID', 'GO2.LO3', 'Generate Order ID', 'GENERATE', 'Generate unique order identifier', 
 '{}', 
 '{"orderid": "string"}', 
 'CONCAT("ORD", YEAR(NOW()), MONTH(NOW()), DAY(NOW()), "_", UNIX_TIMESTAMP())', NOW(), NOW(), 'system', 'system', '1.0', 
 'Nested function to generate unique order ID'),
('NF_UPDATE_INVENTORY', 'GO2.LO3', 'Update Inventory', 'UPDATE', 'Reduce inventory by ordered quantity', 
 '{"productid": "string", "quantity": "integer"}', 
 '{"inventoryupdated": "boolean"}', 
 'UPDATE e14_furnitureproduct SET availableinventory = availableinventory - quantity WHERE productid = productid', NOW(), NOW(), 'system', 'system', '1.0', 
 'Nested function to update product inventory levels');

-- =====================================================
-- 12. NESTED FUNCTION INPUT/OUTPUT ITEMS
-- =====================================================

-- Nested Function Input Items
INSERT INTO lo_nested_function_input_items (
    item_id, nested_function_id, input_stack_id, slot_id, source_type, 
    source_description, required, lo_id, data_type, name, 
    created_at, updated_at, created_by, updated_by, version, natural_language
) VALUES 
-- NF_CALCULATE_SUBTOTAL inputs
('NF_CALC_SUB_IN1', 'NF_CALCULATE_SUBTOTAL', 'NF_CALC_SUB_IN_STACK', 'NF_CALC_SUB_SL1', 'input', 'Quantity from user input', true, 'GO2.LO2', 'integer', 'quantity', NOW(), NOW(), 'system', 'system', '1.0', 'Quantity input for subtotal calculation'),
('NF_CALC_SUB_IN2', 'NF_CALCULATE_SUBTOTAL', 'NF_CALC_SUB_IN_STACK', 'NF_CALC_SUB_SL2', 'mapping', 'Unit price from product selection', true, 'GO2.LO2', 'decimal', 'unitprice', NOW(), NOW(), 'system', 'system', '1.0', 'Unit price input for subtotal calculation'),

-- NF_CALCULATE_GST inputs
('NF_CALC_GST_IN1', 'NF_CALCULATE_GST', 'NF_CALC_GST_IN_STACK', 'NF_CALC_GST_SL1', 'nested_output', 'Subtotal from previous calculation', true, 'GO2.LO2', 'decimal', 'subtotal', NOW(), NOW(), 'system', 'system', '1.0', 'Subtotal input for GST calculation'),

-- NF_CALCULATE_TOTAL inputs
('NF_CALC_TOT_IN1', 'NF_CALCULATE_TOTAL', 'NF_CALC_TOT_IN_STACK', 'NF_CALC_TOT_SL1', 'nested_output', 'Subtotal from calculation', true, 'GO2.LO2', 'decimal', 'subtotal', NOW(), NOW(), 'system', 'system', '1.0', 'Subtotal input for total calculation'),
('NF_CALC_TOT_IN2', 'NF_CALCULATE_TOTAL', 'NF_CALC_TOT_IN_STACK', 'NF_CALC_TOT_SL2', 'nested_output', 'GST amount from calculation', true, 'GO2.LO2', 'decimal', 'gstamount', NOW(), NOW(), 'system', 'system', '1.0', 'GST amount input for total calculation'),

-- NF_UPDATE_INVENTORY inputs
('NF_UPD_INV_IN1', 'NF_UPDATE_INVENTORY', 'NF_UPD_INV_IN_STACK', 'NF_UPD_INV_SL1', 'mapping', 'Product ID from selection', true, 'GO2.LO3', 'string', 'productid', NOW(), NOW(), 'system', 'system', '1.0', 'Product ID for inventory update'),
('NF_UPD_INV_IN2', 'NF_UPDATE_INVENTORY', 'NF_UPD_INV_IN_STACK', 'NF_UPD_INV_SL2', 'mapping', 'Quantity from order', true, 'GO2.LO3', 'integer', 'quantity', NOW(), NOW(), 'system', 'system', '1.0', 'Quantity for inventory reduction');

-- Nested Function Output Items
INSERT INTO lo_nested_function_output_items (
    item_id, nested_function_id, output_stack_id, slot_id, source_type, 
    source_description, lo_id, data_type, name, 
    created_at, updated_at, created_by, updated_by, version, natural_language
) VALUES 
-- NF_CALCULATE_SUBTOTAL outputs
('NF_CALC_SUB_OUT1', 'NF_CALCULATE_SUBTOTAL', 'NF_CALC_SUB_OUT_STACK', 'NF_CALC_SUB_OSL1', 'calculated', 'Calculated subtotal', 'GO2.LO2', 'decimal', 'subtotal', NOW(), NOW(), 'system', 'system', '1.0', 'Subtotal calculation result'),

-- NF_CALCULATE_GST outputs
('NF_CALC_GST_OUT1', 'NF_CALCULATE_GST', 'NF_CALC_GST_OUT_STACK', 'NF_CALC_GST_OSL1', 'calculated', 'Calculated GST amount', 'GO2.LO2', 'decimal', 'gstamount', NOW(), NOW(), 'system', 'system', '1.0', 'GST calculation result'),

-- NF_CALCULATE_TOTAL outputs
('NF_CALC_TOT_OUT1', 'NF_CALCULATE_TOTAL', 'NF_CALC_TOT_OUT_STACK', 'NF_CALC_TOT_OSL1', 'calculated', 'Calculated total amount', 'GO2.LO2', 'decimal', 'totalamount', NOW(), NOW(), 'system', 'system', '1.0', 'Total amount calculation result'),

-- NF_GENERATE_ORDER_ID outputs
('NF_GEN_ORD_OUT1', 'NF_GENERATE_ORDER_ID', 'NF_GEN_ORD_OUT_STACK', 'NF_GEN_ORD_OSL1', 'generated', 'Generated order ID', 'GO2.LO3', 'string', 'orderid', NOW(), NOW(), 'system', 'system', '1.0', 'Generated order identifier'),

-- NF_UPDATE_INVENTORY outputs
('NF_UPD_INV_OUT1', 'NF_UPDATE_INVENTORY', 'NF_UPD_INV_OUT_STACK', 'NF_UPD_INV_OSL1', 'updated', 'Inventory update status', 'GO2.LO3', 'boolean', 'inventoryupdated', NOW(), NOW(), 'system', 'system', '1.0', 'Inventory update confirmation');

-- =====================================================
-- 13. DATA MAPPINGS
-- =====================================================
INSERT INTO lo_data_mappings (
    mapping_id, source_lo_id, target_lo_id, source_output_item_id, 
    target_input_item_id, mapping_type, transformation_rule, 
    created_at, updated_at, created_by, updated_by, version, natural_language
) VALUES 
-- GO2.LO1 to GO2.LO2 mappings
('MAP_GO2_LO1_TO_LO2_1', 'GO2.LO1', 'GO2.LO2', 'GO2.LO1.OP1.IT2', 'GO2.LO2.IP1.IT1', 'direct', 'productid -> productname lookup', NOW(), NOW(), 'system', 'system', '1.0', 'Map product ID to product name for display'),
('MAP_GO2_LO1_TO_LO2_2', 'GO2.LO1', 'GO2.LO2', 'GO2.LO1.OP1.IT4', 'GO2.LO2.IP1.IT2', 'direct', 'unitprice -> unitprice', NOW(), NOW(), 'system', 'system', '1.0', 'Map unit price from product selection'),

-- GO2.LO2 to GO2.LO3 mappings
('MAP_GO2_LO2_TO_LO3_1', 'GO2.LO2', 'GO2.LO3', 'GO2.LO2.OP1.IT4', 'GO2.LO3.IP1.IT1', 'direct', 'totalamount -> ordersummary', NOW(), NOW(), 'system', 'system', '1.0', 'Map total amount to order summary'),
('MAP_GO2_LO2_TO_LO3_2', 'GO2.LO2', 'GO2.LO3', 'GO2.LO2.OP1.IT1', 'GO2.LO3.IP1.IT3', 'transformation', 'quantity for inventory update', NOW(), NOW(), 'system', 'system', '1.0', 'Map quantity for inventory reduction');

-- =====================================================
-- 14. CREATE ENTITY TABLES
-- =====================================================

-- E13: FurnitureOrder Table
CREATE TABLE IF NOT EXISTS e13_furnitureorder (
    id SERIAL,
    orderid VARCHAR(255) NOT NULL,
    userid VARCHAR(255),
    furnituretypeid VARCHAR(255),
    productid VARCHAR(255),
    quantity INTEGER,
    unitprice DECIMAL(10,2),
    subtotal DECIMAL(10,2),
    gstamount DECIMAL(10,2),
    totalamount DECIMAL(10,2),
    paymentmethod VARCHAR(20),
    orderstatus VARCHAR(20),
    orderdate TIMESTAMP WITHOUT TIME ZONE,
    created_at TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    created_by VARCHAR(255) DEFAULT 'system',
    updated_by VARCHAR(255) DEFAULT 'system',
    CONSTRAINT e13_furnitureorder_pkey PRIMARY KEY (orderid)
);

-- E14: FurnitureProduct Table
CREATE TABLE IF NOT EXISTS e14_furnitureproduct (
    id SERIAL,
    productid VARCHAR(255) NOT NULL,
    productname VARCHAR(255),
    furnituretypeid VARCHAR(255),
    price DECIMAL(10,2),
    availableinventory INTEGER,
    description TEXT,
    created_at TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    created_by VARCHAR(255) DEFAULT 'system',
    updated_by VARCHAR(255) DEFAULT 'system',
    CONSTRAINT e14_furnitureproduct_pkey PRIMARY KEY (productid)
);

-- E15: FurnitureType Table
CREATE TABLE IF NOT EXISTS e15_furnituretype (
    id SERIAL,
    furnituretypeid VARCHAR(255) NOT NULL,
    typename VARCHAR(100),
    description TEXT,
    created_at TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    created_by VARCHAR(255) DEFAULT 'system',
    updated_by VARCHAR(255) DEFAULT 'system',
    CONSTRAINT e15_furnituretype_pkey PRIMARY KEY (furnituretypeid)
);

-- E16: PaymentDetails Table
CREATE TABLE IF NOT EXISTS e16_paymentdetails (
    id SERIAL,
    paymentid VARCHAR(255) NOT NULL,
    orderid VARCHAR(255),
    paymentmethod VARCHAR(20),
    upiid VARCHAR(100),
    cardname VARCHAR(100),
    cardnumber VARCHAR(20),
    cvv VARCHAR(4),
    expirydate VARCHAR(7),
    paymentstatus VARCHAR(20),
    created_at TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    created_by VARCHAR(255) DEFAULT 'system',
    updated_by VARCHAR(255) DEFAULT 'system',
    CONSTRAINT e16_paymentdetails_pkey PRIMARY KEY (paymentid)
);

-- Add update triggers
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_e13_furnitureorder_updated_at 
    BEFORE UPDATE ON e13_furnitureorder 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_e14_furnitureproduct_updated_at 
    BEFORE UPDATE ON e14_furnitureproduct 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_e15_furnituretype_updated_at 
    BEFORE UPDATE ON e15_furnituretype 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_e16_paymentdetails_updated_at 
    BEFORE UPDATE ON e16_paymentdetails 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- =====================================================
-- 15. INSERT SAMPLE DATA
-- =====================================================

-- Insert sample furniture types
INSERT INTO e15_furnituretype (furnituretypeid, typename, description) VALUES 
('FT001', 'Cupboard', 'Storage furniture for clothes and personal items'),
('FT002', 'Bar', 'Furniture for serving drinks and entertainment'),
('FT003', 'Study Table', 'Desk furniture for studying and working'),
('FT004', 'Dining Table', 'Table furniture for dining and meals');

-- Insert sample furniture products
INSERT INTO e14_furnitureproduct (productid, productname, furnituretypeid, price, availableinventory, description) VALUES 
-- Cupboards
('P001', 'Classic Wooden Cupboard', 'FT001', 15000.00, 10, 'Traditional wooden cupboard with 3 compartments'),
('P002', 'Modern Steel Cupboard', 'FT001', 12000.00, 15, 'Contemporary steel cupboard with mirror'),
('P003', 'Sliding Door Cupboard', 'FT001', 18000.00, 8, 'Space-saving cupboard with sliding doors'),

-- Bars
('P004', 'Home Bar Counter', 'FT002', 25000.00, 5, 'Elegant home bar with storage and counter space'),
('P005', 'Mini Bar Cabinet', 'FT002', 8000.00, 12, 'Compact bar cabinet for small spaces'),
('P006', 'Premium Wine Bar', 'FT002', 35000.00, 3, 'Luxury wine bar with temperature control'),

-- Study Tables
('P007', 'Executive Study Table', 'FT003', 10000.00, 20, 'Professional study table with drawers'),
('P008', 'Student Desk', 'FT003', 5000.00, 25, 'Simple and functional study desk for students'),
('P009', 'L-Shaped Study Table', 'FT003', 15000.00, 10, 'Spacious L-shaped desk for home office'),

-- Dining Tables
('P010', 'Family Dining Table', 'FT004', 20000.00, 8, '6-seater dining table for family meals'),
('P011', 'Round Dining Table', 'FT004', 18000.00, 6, '4-seater round dining table'),
('P012', 'Extendable Dining Table', 'FT004', 30000.00, 4, 'Dining table that extends for larger gatherings');

-- =====================================================
-- 16. CREATE LO EXECUTION TABLES
-- =====================================================
CREATE TABLE IF NOT EXISTS go2_lo1_selectfurnituretype (
    id SERIAL PRIMARY KEY,
    instance_id VARCHAR(255) NOT NULL,
    execution_id VARCHAR(255) NOT NULL,
    furnituretypeid VARCHAR(255),
    productid VARCHAR(255
