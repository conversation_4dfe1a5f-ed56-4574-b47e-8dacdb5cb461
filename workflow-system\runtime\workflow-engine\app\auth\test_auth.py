"""
Test script for the authentication and permission services.

This script tests:
- Database schema updates
- User creation and authentication
- Token generation and validation
- Permission checks
"""

import os
import sys
import json
import logging
import unittest
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional

import jwt
import bcrypt
import requests
import httpx
from fastapi import FastAPI, Depends, HTTPException, status, Request
from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker, Session

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("test_auth")

# Add parent directory to path to import app modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

# Import app modules
from app.auth.auth_service import AuthService, User, UserCreate, Token
from app.auth.auth_middleware import SecurityContext, AuthMiddleware, require_auth
from app.auth.permission.permission_service import (
    PermissionService, 
    PermissionContext, 
    PermissionType, 
    ResourceType
)

# Database configuration
# Connect to the database in the Docker container
# Using localhost since we're running outside of the Docker network
DATABASE_URL = os.getenv("DATABASE_URL", "postgresql://postgres:workflow_postgres_secure_password@localhost:5433/workflow_system")

# Create database engine and session
engine = create_engine(DATABASE_URL)
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

def get_db():
    """Get database session."""
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()

# Create FastAPI app for testing
app = FastAPI()

# Add authentication middleware
app.add_middleware(
    AuthMiddleware,
    exclude_paths=["/docs", "/redoc", "/openapi.json", "/token", "/refresh"]
)

# Add test routes
@app.post("/token")
async def login_for_access_token(
    request: Request,
    username: str,
    password: str,
    db: Session = Depends(get_db)
):
    """Test login endpoint."""
    auth_service = AuthService(db)
    user = auth_service.authenticate_user(username, password)
    
    if not user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Incorrect username or password",
            headers={"WWW-Authenticate": "Bearer"},
        )
        
    # Create token
    token = auth_service.create_token(user)
    
    # Create session
    auth_service.create_session(user.user_id, token.access_token, request)
    
    return token

@app.get("/me")
async def get_current_user(
    security_context: SecurityContext = Depends(require_auth),
    db: Session = Depends(get_db)
):
    """Test authenticated endpoint."""
    return {
        "user_id": security_context.user_id,
        "username": security_context.username,
        "roles": security_context.roles,
        "permissions": security_context.permissions
    }

@app.post("/check-permission")
async def check_permission(
    resource_type: ResourceType,
    resource_id: str,
    permission_type: PermissionType,
    security_context: SecurityContext = Depends(require_auth),
    db: Session = Depends(get_db)
):
    """Test permission check endpoint."""
    permission_service = PermissionService(db)
    
    # Create permission context
    permission_context = PermissionContext(
        resource_type=resource_type,
        resource_id=resource_id,
        permission_type=permission_type
    )
    
    # Check permission
    granted = permission_service.check_permission(security_context, permission_context)
    
    return {
        "resource_type": resource_type,
        "resource_id": resource_id,
        "permission_type": permission_type,
        "granted": granted
    }

# Create test client
from fastapi.testclient import TestClient
client = TestClient(app)

class TestAuth(unittest.TestCase):
    """Test authentication and permission services."""
    
    def setUp(self):
        """Set up test database."""
        self.db = SessionLocal()
        self.auth_service = AuthService(self.db)
        self.permission_service = PermissionService(self.db)
        
        # Verify the test user exists and can be authenticated
        user = self.auth_service.get_user("testuser")
        logger.info(f"Test user: {user}")
        
        if user:
            # Manually check password verification
            password_verified = self.auth_service.verify_password("testpassword", user.password_hash)
            logger.info(f"Password verification result: {password_verified}")
            logger.info(f"Password hash: {user.password_hash}")
            
    def tearDown(self):
        """Clean up test database."""
        self.db.close()
        
    def test_01_database_schema(self):
        """Test database schema."""
        logger.info("Testing database schema...")
        
        # Check if users table exists
        try:
            query = """
            SELECT EXISTS (
                SELECT FROM information_schema.tables 
                WHERE table_schema = 'workflow_runtime' 
                AND table_name = 'users'
            )
            """
            result = self.db.execute(text(query)).fetchone()
            self.assertTrue(result[0], "Users table does not exist")
            logger.info("Users table exists")
            
            # Check if user_roles table exists
            query = """
            SELECT EXISTS (
                SELECT FROM information_schema.tables 
                WHERE table_schema = 'workflow_runtime' 
                AND table_name = 'user_roles'
            )
            """
            result = self.db.execute(text(query)).fetchone()
            self.assertTrue(result[0], "User roles table does not exist")
            logger.info("User roles table exists")
            
            # Check if user_oauth_tokens table exists
            query = """
            SELECT EXISTS (
                SELECT FROM information_schema.tables 
                WHERE table_schema = 'workflow_runtime' 
                AND table_name = 'user_oauth_tokens'
            )
            """
            result = self.db.execute(text(query)).fetchone()
            self.assertTrue(result[0], "User OAuth tokens table does not exist")
            logger.info("User OAuth tokens table exists")
            
            # Check if user_sessions table exists
            query = """
            SELECT EXISTS (
                SELECT FROM information_schema.tables 
                WHERE table_schema = 'workflow_runtime' 
                AND table_name = 'user_sessions'
            )
            """
            result = self.db.execute(text(query)).fetchone()
            self.assertTrue(result[0], "User sessions table does not exist")
            logger.info("User sessions table exists")
            
            # Check if roles table exists
            query = """
            SELECT EXISTS (
                SELECT FROM information_schema.tables 
                WHERE table_schema = 'workflow_runtime' 
                AND table_name = 'roles'
            )
            """
            result = self.db.execute(text(query)).fetchone()
            self.assertTrue(result[0], "Roles table does not exist")
            logger.info("Roles table exists")
            
            # Check if permission_contexts table exists
            query = """
            SELECT EXISTS (
                SELECT FROM information_schema.tables 
                WHERE table_schema = 'workflow_runtime' 
                AND table_name = 'permission_contexts'
            )
            """
            result = self.db.execute(text(query)).fetchone()
            self.assertTrue(result[0], "Permission contexts table does not exist")
            logger.info("Permission contexts table exists")
            
            # Check if permission_types table exists
            query = """
            SELECT EXISTS (
                SELECT FROM information_schema.tables 
                WHERE table_schema = 'workflow_runtime' 
                AND table_name = 'permission_types'
            )
            """
            result = self.db.execute(text(query)).fetchone()
            self.assertTrue(result[0], "Permission types table does not exist")
            logger.info("Permission types table exists")
            
            # Check if role_permissions table exists
            query = """
            SELECT EXISTS (
                SELECT FROM information_schema.tables 
                WHERE table_schema = 'workflow_runtime' 
                AND table_name = 'role_permissions'
            )
            """
            result = self.db.execute(text(query)).fetchone()
            self.assertTrue(result[0], "Role permissions table does not exist")
            logger.info("Role permissions table exists")
            
            # Check if organizational_units table exists
            query = """
            SELECT EXISTS (
                SELECT FROM information_schema.tables 
                WHERE table_schema = 'workflow_runtime' 
                AND table_name = 'organizational_units'
            )
            """
            result = self.db.execute(text(query)).fetchone()
            self.assertTrue(result[0], "Organizational units table does not exist")
            logger.info("Organizational units table exists")
            
            # Check if user_organizations table exists
            query = """
            SELECT EXISTS (
                SELECT FROM information_schema.tables 
                WHERE table_schema = 'workflow_runtime' 
                AND table_name = 'user_organizations'
            )
            """
            result = self.db.execute(text(query)).fetchone()
            self.assertTrue(result[0], "User organizations table does not exist")
            logger.info("User organizations table exists")
            
        except Exception as e:
            logger.error(f"Error checking database schema: {str(e)}")
            raise
            
    def test_02_user_authentication(self):
        """Test user authentication."""
        logger.info("Testing user authentication...")
        
        # Test user authentication
        user = self.auth_service.authenticate_user("testuser", "testpassword")
        self.assertIsNotNone(user, "User authentication failed")
        self.assertEqual(user.username, "testuser", "Username does not match")
        logger.info("User authentication successful")
        
        # Test invalid password
        user = self.auth_service.authenticate_user("testuser", "wrongpassword")
        self.assertIsNone(user, "User authentication with wrong password should fail")
        logger.info("Invalid password test successful")
        
        # Test invalid username
        user = self.auth_service.authenticate_user("nonexistentuser", "testpassword")
        self.assertIsNone(user, "User authentication with wrong username should fail")
        logger.info("Invalid username test successful")
        
    def test_03_token_generation(self):
        """Test token generation and validation."""
        logger.info("Testing token generation and validation...")
        
        # Test token generation
        user = self.auth_service.authenticate_user("testuser", "testpassword")
        self.assertIsNotNone(user, "User authentication failed")
        
        token = self.auth_service.create_token(user)
        self.assertIsNotNone(token, "Token generation failed")
        self.assertIsNotNone(token.access_token, "Access token is None")
        self.assertIsNotNone(token.refresh_token, "Refresh token is None")
        logger.info("Token generation successful")
        
        # Test token validation
        payload = self.auth_service.decode_token(token.access_token)
        self.assertIsNotNone(payload, "Token validation failed")
        self.assertEqual(payload.get("sub"), user.user_id, "User ID in token does not match")
        self.assertEqual(payload.get("username"), user.username, "Username in token does not match")
        logger.info("Token validation successful")
        
        # Test token refresh
        new_token = self.auth_service.refresh_token(token.refresh_token)
        self.assertIsNotNone(new_token, "Token refresh failed")
        self.assertIsNotNone(new_token.access_token, "New access token is None")
        self.assertIsNotNone(new_token.refresh_token, "New refresh token is None")
        logger.info("Token refresh successful")
        
    def test_04_permission_checks(self):
        """Test permission checks."""
        logger.info("Testing permission checks...")
        
        # Create test security context
        user = self.auth_service.authenticate_user("testuser", "testpassword")
        self.assertIsNotNone(user, "User authentication failed")
        
        # Get user permissions
        permissions = self.auth_service.get_user_permissions(user.user_id)
        
        security_context = SecurityContext()
        security_context.user_id = user.user_id
        security_context.username = user.username
        security_context.roles = user.roles
        security_context.permissions = permissions
        security_context.org_units = user.org_units
        security_context.tenant_id = user.tenant_id
        security_context.authenticated = True
        
        # Test permission check
        permission_context = PermissionContext(
            resource_type=ResourceType.ENTITY,
            resource_id="test_entity",
            permission_type=PermissionType.READ
        )
        
        # This may pass or fail depending on the permissions assigned to the test user
        result = self.permission_service.check_permission(security_context, permission_context)
        logger.info(f"Permission check result: {result}")
        
        # Test bulk permission check
        permission_contexts = [
            PermissionContext(
                resource_type=ResourceType.ENTITY,
                resource_id="test_entity",
                permission_type=PermissionType.READ
            ),
            PermissionContext(
                resource_type=ResourceType.ENTITY,
                resource_id="test_entity",
                permission_type=PermissionType.WRITE
            )
        ]
        
        results = self.permission_service.check_permissions(security_context, permission_contexts)
        logger.info(f"Bulk permission check results: {results}")
        
    def test_05_api_endpoints(self):
        """Test API endpoints."""
        logger.info("Testing API endpoints...")
        
        # Test login endpoint
        response = client.post("/token", params={"username": "testuser", "password": "testpassword"})
        self.assertEqual(response.status_code, 200, f"Login failed: {response.text}")
        token_data = response.json()
        self.assertIn("access_token", token_data, "Access token not in response")
        self.assertIn("refresh_token", token_data, "Refresh token not in response")
        logger.info("Login endpoint test successful")
        
        # Test authenticated endpoint
        response = client.get(
            "/me", 
            headers={"Authorization": f"Bearer {token_data['access_token']}"}
        )
        self.assertEqual(response.status_code, 200, f"Authenticated endpoint failed: {response.text}")
        user_data = response.json()
        self.assertEqual(user_data["username"], "testuser", "Username in response does not match")
        logger.info("Authenticated endpoint test successful")
        
        # Test permission check endpoint
        response = client.post(
            "/check-permission",
            params={
                "resource_type": "entity",  # Use the string value instead of the enum
                "resource_id": "test_entity",
                "permission_type": "read"   # Use the string value instead of the enum
            },
            headers={"Authorization": f"Bearer {token_data['access_token']}"}
        )
        self.assertEqual(response.status_code, 200, f"Permission check endpoint failed: {response.text}")
        permission_data = response.json()
        self.assertIn("granted", permission_data, "Granted flag not in response")
        logger.info(f"Permission check endpoint test successful: {permission_data}")
        
    def test_06_time_based_permission(self):
        """Test time-based permission conditions."""
        logger.info("Testing time-based permission conditions...")
        
        # Create test security context
        user = self.auth_service.authenticate_user("testuser", "testpassword")
        self.assertIsNotNone(user, "User authentication failed")
        
        # Get user permissions
        permissions = self.auth_service.get_user_permissions(user.user_id)
        
        security_context = SecurityContext()
        security_context.user_id = user.user_id
        security_context.username = user.username
        security_context.roles = user.roles
        security_context.permissions = permissions
        security_context.org_units = user.org_units
        security_context.tenant_id = user.tenant_id
        security_context.authenticated = True
        
        # Create permission context with time-based condition
        permission_context = PermissionContext(
            resource_type=ResourceType.ENTITY,
            resource_id="test_entity",
            permission_type=PermissionType.READ,
            additional_context={
                "current_time": datetime.now().isoformat()
            }
        )
        
        # Insert test permission context with time-based condition
        try:
            # Create a permission context with time-based condition
            query = """
            INSERT INTO workflow_runtime.permission_contexts (context_id, name, context_type, context_rules)
            VALUES (
                'time_based_context', 
                'Time Based Context', 
                'entity', 
                '{"resource_id": "test_entity", "permissions": ["read"], "conditions": {"time_range": {"start": "09:00:00", "end": "17:00:00", "days": ["Monday", "Tuesday", "Wednesday", "Thursday", "Friday"]}}}'::jsonb
            )
            ON CONFLICT (context_id) DO UPDATE
            SET context_rules = '{"resource_id": "test_entity", "permissions": ["read"], "conditions": {"time_range": {"start": "09:00:00", "end": "17:00:00", "days": ["Monday", "Tuesday", "Wednesday", "Thursday", "Friday"]}}}'::jsonb
            """
            self.db.execute(text(query))
            
            # Assign permission context to test role
            query = """
            INSERT INTO workflow_runtime.role_permissions (role_id, context_id)
            VALUES ('test_role', 'time_based_context')
            ON CONFLICT (role_id, context_id) DO NOTHING
            """
            self.db.execute(text(query))
            self.db.commit()
            
            # Test time-based permission check
            # Note: This test will be skipped until time-based condition evaluation is implemented
            # The test is included here as a placeholder for future implementation
            logger.info("Time-based permission check test skipped (not yet implemented)")
            
        except Exception as e:
            logger.error(f"Error setting up time-based permission test: {str(e)}")
            self.db.rollback()
            raise
            
    def test_07_attribute_based_permission(self):
        """Test attribute-based permission conditions."""
        logger.info("Testing attribute-based permission conditions...")
        
        # Create test security context
        user = self.auth_service.authenticate_user("testuser", "testpassword")
        self.assertIsNotNone(user, "User authentication failed")
        
        # Get user permissions
        permissions = self.auth_service.get_user_permissions(user.user_id)
        
        security_context = SecurityContext()
        security_context.user_id = user.user_id
        security_context.username = user.username
        security_context.roles = user.roles
        security_context.permissions = permissions
        security_context.org_units = user.org_units
        security_context.tenant_id = user.tenant_id
        security_context.authenticated = True
        
        # Create permission context with attribute-based condition
        permission_context = PermissionContext(
            resource_type=ResourceType.ENTITY,
            resource_id="test_entity",
            permission_type=PermissionType.READ,
            additional_context={
                "entity_type": "test_type",
                "entity_status": "active"
            }
        )
        
        # Insert test permission context with attribute-based condition
        try:
            # Create a permission context with attribute-based condition
            query = """
            INSERT INTO workflow_runtime.permission_contexts (context_id, name, context_type, context_rules)
            VALUES (
                'attribute_based_context', 
                'Attribute Based Context', 
                'entity', 
                '{"resource_id": "test_entity", "permissions": ["read"], "conditions": {"attributes": {"entity_type": "test_type", "entity_status": "active"}}}'::jsonb
            )
            ON CONFLICT (context_id) DO UPDATE
            SET context_rules = '{"resource_id": "test_entity", "permissions": ["read"], "conditions": {"attributes": {"entity_type": "test_type", "entity_status": "active"}}}'::jsonb
            """
            self.db.execute(text(query))
            
            # Assign permission context to test role
            query = """
            INSERT INTO workflow_runtime.role_permissions (role_id, context_id)
            VALUES ('test_role', 'attribute_based_context')
            ON CONFLICT (role_id, context_id) DO NOTHING
            """
            self.db.execute(text(query))
            self.db.commit()
            
            # Test attribute-based permission check
            # Note: This test will be skipped until attribute-based condition evaluation is implemented
            # The test is included here as a placeholder for future implementation
            logger.info("Attribute-based permission check test skipped (not yet implemented)")
            
        except Exception as e:
            logger.error(f"Error setting up attribute-based permission test: {str(e)}")
            self.db.rollback()
            raise
            
    def test_08_ownership_based_permission(self):
        """Test ownership-based permission conditions."""
        logger.info("Testing ownership-based permission conditions...")
        
        # Create test security context
        user = self.auth_service.authenticate_user("testuser", "testpassword")
        self.assertIsNotNone(user, "User authentication failed")
        
        # Get user permissions
        permissions = self.auth_service.get_user_permissions(user.user_id)
        
        security_context = SecurityContext()
        security_context.user_id = user.user_id
        security_context.username = user.username
        security_context.roles = user.roles
        security_context.permissions = permissions
        security_context.org_units = user.org_units
        security_context.tenant_id = user.tenant_id
        security_context.authenticated = True
        
        # Create permission context with ownership condition
        permission_context = PermissionContext(
            resource_type=ResourceType.ENTITY,
            resource_id="test_entity",
            permission_type=PermissionType.WRITE,
            additional_context={
                "owner_id": user.user_id  # User is the owner
            }
        )
        
        # Insert test permission context with ownership condition
        try:
            # Create a permission context with ownership condition
            query = """
            INSERT INTO workflow_runtime.permission_contexts (context_id, name, context_type, context_rules)
            VALUES (
                'ownership_based_context', 
                'Ownership Based Context', 
                'entity', 
                '{"resource_id": "test_entity", "permissions": ["write"], "conditions": {"is_owner": true}}'::jsonb
            )
            ON CONFLICT (context_id) DO UPDATE
            SET context_rules = '{"resource_id": "test_entity", "permissions": ["write"], "conditions": {"is_owner": true}}'::jsonb
            """
            self.db.execute(text(query))
            
            # Assign permission context to test role
            query = """
            INSERT INTO workflow_runtime.role_permissions (role_id, context_id)
            VALUES ('test_role', 'ownership_based_context')
            ON CONFLICT (role_id, context_id) DO NOTHING
            """
            self.db.execute(text(query))
            self.db.commit()
            
            # Test ownership-based permission check
            result = self.permission_service.check_permission(security_context, permission_context)
            logger.info(f"Ownership-based permission check result: {result}")
            
            # Test with non-owner
            non_owner_permission_context = PermissionContext(
                resource_type=ResourceType.ENTITY,
                resource_id="test_entity",
                permission_type=PermissionType.WRITE,
                additional_context={
                    "owner_id": "different_user_id"  # User is not the owner
                }
            )
            
            result = self.permission_service.check_permission(security_context, non_owner_permission_context)
            logger.info(f"Non-owner permission check result: {result}")
            
        except Exception as e:
            logger.error(f"Error setting up ownership-based permission test: {str(e)}")
            self.db.rollback()
            raise
            
    def test_09_custom_condition_permission(self):
        """Test custom condition permission."""
        logger.info("Testing custom condition permission...")
        
        # Create test security context
        user = self.auth_service.authenticate_user("testuser", "testpassword")
        self.assertIsNotNone(user, "User authentication failed")
        
        # Get user permissions
        permissions = self.auth_service.get_user_permissions(user.user_id)
        
        security_context = SecurityContext()
        security_context.user_id = user.user_id
        security_context.username = user.username
        security_context.roles = user.roles
        security_context.permissions = permissions
        security_context.org_units = user.org_units
        security_context.tenant_id = user.tenant_id
        security_context.authenticated = True
        
        # Create permission context with custom condition
        permission_context = PermissionContext(
            resource_type=ResourceType.ENTITY,
            resource_id="test_entity",
            permission_type=PermissionType.EXECUTE,
            additional_context={
                "custom_value": "test_value"
            }
        )
        
        # Insert test permission context with custom condition
        try:
            # Create a permission context with custom condition
            query = """
            INSERT INTO workflow_runtime.permission_contexts (context_id, name, context_type, context_rules)
            VALUES (
                'custom_condition_context', 
                'Custom Condition Context', 
                'entity', 
                '{"resource_id": "test_entity", "permissions": ["execute"], "conditions": {"custom_condition": {"type": "equals", "field": "custom_value", "value": "test_value"}}}'::jsonb
            )
            ON CONFLICT (context_id) DO UPDATE
            SET context_rules = '{"resource_id": "test_entity", "permissions": ["execute"], "conditions": {"custom_condition": {"type": "equals", "field": "custom_value", "value": "test_value"}}}'::jsonb
            """
            self.db.execute(text(query))
            
            # Assign permission context to test role
            query = """
            INSERT INTO workflow_runtime.role_permissions (role_id, context_id)
            VALUES ('test_role', 'custom_condition_context')
            ON CONFLICT (role_id, context_id) DO NOTHING
            """
            self.db.execute(text(query))
            self.db.commit()
            
            # Test custom condition permission check
            # Note: This test will be skipped until custom condition evaluation is implemented
            # The test is included here as a placeholder for future implementation
            logger.info("Custom condition permission check test skipped (not yet implemented)")
            
        except Exception as e:
            logger.error(f"Error setting up custom condition permission test: {str(e)}")
            self.db.rollback()
            raise

if __name__ == "__main__":
    unittest.main()
