#!/usr/bin/env python3
"""
Test script for verifying the schema fix.

This script tests that the fix_schema_issues.py script resolves the issue
with tables not being populated correctly.
"""

import os
import sys
import logging
import argparse
import json
from typing import Dict, List, Tuple, Any, Optional

# Add parent directory to path to import common modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from common.db_connection import get_db_connection
from db_utils import execute_query

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger('test_schema_fix')

def run_fix_schema_issues(schema_name: str) -> bool:
    """
    Run the fix_schema_issues.py script.
    
    Args:
        schema_name: Schema name to fix
        
    Returns:
        <PERSON>olean indicating if the fix was successful
    """
    logger.info(f"Running fix_schema_issues.py for schema {schema_name}")
    
    # Import the fix_schema_issues module
    from fix_schema_issues import fix_schema_issues as fix_schema
    
    # Run the fix
    success = fix_schema(schema_name)
    
    if success:
        logger.info(f"Successfully fixed schema issues in {schema_name}")
    else:
        logger.error(f"Failed to fix schema issues in {schema_name}")
    
    return success

def deploy_sample_lo(schema_name: str) -> Tuple[bool, str]:
    """
    Deploy a sample LO definition to the specified schema.
    
    Args:
        schema_name: Schema name to deploy to
        
    Returns:
        Tuple containing:
            - Boolean indicating if deployment was successful
            - LO ID of the deployed LO
    """
    logger.info(f"Deploying sample LO to {schema_name}")
    
    # First, make sure we have a GO to reference
    go_id = "GO1"
    go_name = "Sample GO"
    
    # Check if GO exists
    query = f"""
        SELECT go_id FROM {schema_name}.global_objectives WHERE go_id = %s
    """
    
    success, messages, result = execute_query(query, (go_id,))
    
    if not success:
        logger.error(f"Failed to check if GO exists: {messages}")
        return False, ""
    
    if not result:
        # Create GO
        query = f"""
            INSERT INTO {schema_name}.global_objectives (go_id, name, description, status, version)
            VALUES (%s, %s, %s, %s, %s)
        """
        
        success, messages, _ = execute_query(query, (go_id, go_name, "Sample GO description", "active", "1.0"))
        
        if not success:
            logger.error(f"Failed to create GO: {messages}")
            return False, ""
        
        logger.info(f"Created GO {go_id}")
    
    # Create LO
    lo_id = "GO1.LO1"
    lo_name = "Sample LO"
    
    # Check if LO exists
    query = f"""
        SELECT lo_id FROM {schema_name}.local_objectives WHERE lo_id = %s
    """
    
    success, messages, result = execute_query(query, (lo_id,))
    
    if not success:
        logger.error(f"Failed to check if LO exists: {messages}")
        return False, ""
    
    if result:
        # Delete existing LO
        query = f"""
            DELETE FROM {schema_name}.local_objectives WHERE lo_id = %s
        """
        
        success, messages, _ = execute_query(query, (lo_id,))
        
        if not success:
            logger.error(f"Failed to delete existing LO: {messages}")
            return False, ""
        
        logger.info(f"Deleted existing LO {lo_id}")
    
    # Create LO
    query = f"""
        INSERT INTO {schema_name}.local_objectives (
            lo_id, go_id, name, description, ui_stack, mapping_stack, version_type,
            contextual_id, function_type, workflow_source, system_function
        ) VALUES (%s, %s, %s, %s, %s, %s, 'v2', %s, %s, %s, %s)
    """
    
    ui_stack = {
        "components": [
            {"type": "form", "id": "sample_form"}
        ]
    }
    
    mapping_stack = {
        "mappings": [
            {"source": "input1", "target": "output1", "mapping_type": "direct"}
        ]
    }
    
    params = (
        lo_id,
        go_id,
        lo_name,
        "Sample LO description",
        json.dumps(ui_stack),
        json.dumps(mapping_stack),
        lo_id,  # contextual_id
        "standard",  # function_type
        "system",  # workflow_source
        None  # system_function
    )
    
    success, messages, _ = execute_query(query, params)
    
    if not success:
        logger.error(f"Failed to create LO: {messages}")
        return False, ""
    
    logger.info(f"Created LO {lo_id}")
    
    # Create input stack
    query = f"""
        INSERT INTO {schema_name}.lo_input_stack (lo_id, name, description)
        VALUES (%s, %s, %s)
        RETURNING id
    """
    
    params = (
        lo_id,
        "Input Stack",
        "Sample input stack"
    )
    
    success, messages, result = execute_query(query, params)
    
    if not success or not result:
        logger.error(f"Failed to create input stack: {messages}")
        return False, ""
    
    input_stack_id = result[0][0]
    logger.info(f"Created input stack with ID {input_stack_id}")
    
    # Create input items
    input_items = [
        {
            "id": "GO1.LO1.IN1",
            "name": "input1",
            "type": "string",
            "required": True,
            "ui_control": "text",
            "help_text": "Sample input 1",
            "slot_id": "entity1.attr1.GO1.LO1.IN1",
            "contextual_id": "GO1.LO1.GO1.LO1.IN1"
        },
        {
            "id": "GO1.LO1.IN2",
            "name": "input2",
            "type": "number",
            "required": False,
            "ui_control": "number",
            "help_text": "Sample input 2",
            "slot_id": "entity1.attr2.GO1.LO1.IN2",
            "contextual_id": "GO1.LO1.GO1.LO1.IN2"
        }
    ]
    
    for item in input_items:
        query = f"""
            INSERT INTO {schema_name}.lo_input_items (
                id, item_id, input_stack_id, slot_id, contextual_id, source_type, required,
                lo_id, name, type, ui_control, help_text
            ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
        """
        
        params = (
            item["id"],
            item["id"],
            input_stack_id,
            item["slot_id"],
            item["contextual_id"],
            "user",
            item["required"],
            lo_id,
            item["name"],
            item["type"],
            item["ui_control"],
            item["help_text"]
        )
        
        success, messages, _ = execute_query(query, params)
        
        if not success:
            logger.error(f"Failed to create input item {item['id']}: {messages}")
            return False, ""
        
        logger.info(f"Created input item {item['id']}")
    
    # Create output stack
    query = f"""
        INSERT INTO {schema_name}.lo_output_stack (lo_id, name, description)
        VALUES (%s, %s, %s)
        RETURNING id
    """
    
    params = (
        lo_id,
        "Output Stack",
        "Sample output stack"
    )
    
    success, messages, result = execute_query(query, params)
    
    if not success or not result:
        logger.error(f"Failed to create output stack: {messages}")
        return False, ""
    
    output_stack_id = result[0][0]
    logger.info(f"Created output stack with ID {output_stack_id}")
    
    # Create output items
    output_items = [
        {
            "id": "GO1.LO1.OP1",
            "name": "output1",
            "type": "string",
            "slot_id": "entity2.attr1.GO1.LO1.OP1",
            "contextual_id": "GO1.LO1.GO1.LO1.OP1"
        },
        {
            "id": "GO1.LO1.OP2",
            "name": "output2",
            "type": "number",
            "slot_id": "entity2.attr2.GO1.LO1.OP2",
            "contextual_id": "GO1.LO1.GO1.LO1.OP2"
        }
    ]
    
    for item in output_items:
        query = f"""
            INSERT INTO {schema_name}.lo_output_items (
                id, item_id, output_stack_id, slot_id, contextual_id, source,
                lo_id, name, type
            ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s)
        """
        
        params = (
            item["id"],
            item["id"],
            output_stack_id,
            item["slot_id"],
            item["contextual_id"],
            "system",
            lo_id,
            item["name"],
            item["type"]
        )
        
        success, messages, _ = execute_query(query, params)
        
        if not success:
            logger.error(f"Failed to create output item {item['id']}: {messages}")
            return False, ""
        
        logger.info(f"Created output item {item['id']}")
    
    # Create data mapping stack
    query = f"""
        INSERT INTO {schema_name}.lo_data_mapping_stack (lo_id, name, description)
        VALUES (%s, %s, %s)
        RETURNING id
    """
    
    params = (
        lo_id,
        "Data Mapping Stack",
        "Sample data mapping stack"
    )
    
    success, messages, result = execute_query(query, params)
    
    if not success or not result:
        logger.error(f"Failed to create data mapping stack: {messages}")
        return False, ""
    
    mapping_stack_id = result[0][0]
    logger.info(f"Created data mapping stack with ID {mapping_stack_id}")
    
    # Create data mappings
    mappings = [
        {
            "source": "input1",
            "target": "output1",
            "mapping_type": "direct"
        },
        {
            "source": "input2",
            "target": "output2",
            "mapping_type": "direct"
        }
    ]
    
    for i, mapping in enumerate(mappings):
        query = f"""
            INSERT INTO {schema_name}.lo_data_mappings (
                mapping_stack_id, source, target, mapping_type
            ) VALUES (%s, %s, %s, %s)
        """
        
        params = (
            mapping_stack_id,
            mapping["source"],
            mapping["target"],
            mapping["mapping_type"]
        )
        
        success, messages, _ = execute_query(query, params)
        
        if not success:
            logger.error(f"Failed to create data mapping {i+1}: {messages}")
            return False, ""
        
        logger.info(f"Created data mapping {i+1}")
    
    return True, lo_id

def verify_tables_populated(schema_name: str, lo_id: str) -> bool:
    """
    Verify that the tables are populated correctly.
    
    Args:
        schema_name: Schema name to verify
        lo_id: LO ID to verify
        
    Returns:
        Boolean indicating if the tables are populated correctly
    """
    logger.info(f"Verifying tables are populated correctly for LO {lo_id} in schema {schema_name}")
    
    # Check lo_input_stack
    query = f"""
        SELECT COUNT(*) FROM {schema_name}.lo_input_stack WHERE lo_id = %s
    """
    
    success, messages, result = execute_query(query, (lo_id,))
    
    if not success or not result:
        logger.error(f"Failed to check lo_input_stack: {messages}")
        return False
    
    if result[0][0] == 0:
        logger.error(f"lo_input_stack is empty for LO {lo_id}")
        return False
    
    logger.info(f"lo_input_stack has {result[0][0]} rows for LO {lo_id}")
    
    # Check lo_input_items
    query = f"""
        SELECT COUNT(*) FROM {schema_name}.lo_input_items WHERE lo_id = %s
    """
    
    success, messages, result = execute_query(query, (lo_id,))
    
    if not success or not result:
        logger.error(f"Failed to check lo_input_items: {messages}")
        return False
    
    if result[0][0] == 0:
        logger.error(f"lo_input_items is empty for LO {lo_id}")
        return False
    
    logger.info(f"lo_input_items has {result[0][0]} rows for LO {lo_id}")
    
    # Check lo_output_stack
    query = f"""
        SELECT COUNT(*) FROM {schema_name}.lo_output_stack WHERE lo_id = %s
    """
    
    success, messages, result = execute_query(query, (lo_id,))
    
    if not success or not result:
        logger.error(f"Failed to check lo_output_stack: {messages}")
        return False
    
    if result[0][0] == 0:
        logger.error(f"lo_output_stack is empty for LO {lo_id}")
        return False
    
    logger.info(f"lo_output_stack has {result[0][0]} rows for LO {lo_id}")
    
    # Check lo_output_items
    query = f"""
        SELECT COUNT(*) FROM {schema_name}.lo_output_items WHERE lo_id = %s
    """
    
    success, messages, result = execute_query(query, (lo_id,))
    
    if not success or not result:
        logger.error(f"Failed to check lo_output_items: {messages}")
        return False
    
    if result[0][0] == 0:
        logger.error(f"lo_output_items is empty for LO {lo_id}")
        return False
    
    logger.info(f"lo_output_items has {result[0][0]} rows for LO {lo_id}")
    
    # Check lo_data_mapping_stack
    query = f"""
        SELECT COUNT(*) FROM {schema_name}.lo_data_mapping_stack WHERE lo_id = %s
    """
    
    success, messages, result = execute_query(query, (lo_id,))
    
    if not success or not result:
        logger.error(f"Failed to check lo_data_mapping_stack: {messages}")
        return False
    
    if result[0][0] == 0:
        logger.error(f"lo_data_mapping_stack is empty for LO {lo_id}")
        return False
    
    logger.info(f"lo_data_mapping_stack has {result[0][0]} rows for LO {lo_id}")
    
    # Check lo_data_mappings
    query = f"""
        SELECT COUNT(*) FROM {schema_name}.lo_data_mappings
        WHERE mapping_stack_id IN (
            SELECT id FROM {schema_name}.lo_data_mapping_stack WHERE lo_id = %s
        )
    """
    
    success, messages, result = execute_query(query, (lo_id,))
    
    if not success or not result:
        logger.error(f"Failed to check lo_data_mappings: {messages}")
        return False
    
    if result[0][0] == 0:
        logger.error(f"lo_data_mappings is empty for LO {lo_id}")
        return False
    
    logger.info(f"lo_data_mappings has {result[0][0]} rows for LO {lo_id}")
    
    logger.info(f"All tables are populated correctly for LO {lo_id} in schema {schema_name}")
    return True

def main():
    """
    Main function.
    """
    parser = argparse.ArgumentParser(description='Test the schema fix.')
    parser.add_argument('--schema', type=str, default='workflow_temp', help='Schema name to test')
    parser.add_argument('--skip-fix', action='store_true', help='Skip running the fix_schema_issues.py script')
    
    args = parser.parse_args()
    
    # Run the fix_schema_issues.py script
    if not args.skip_fix:
        success = run_fix_schema_issues(args.schema)
        if not success:
            logger.error("Failed to fix schema issues")
            return
    
    # Deploy a sample LO
    success, lo_id = deploy_sample_lo(args.schema)
    if not success:
        logger.error("Failed to deploy sample LO")
        return
    
    # Verify that the tables are populated correctly
    success = verify_tables_populated(args.schema, lo_id)
    if success:
        logger.info("Schema fix test passed")
    else:
        logger.error("Schema fix test failed")

if __name__ == '__main__':
    main()
