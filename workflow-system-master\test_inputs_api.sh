#!/bin/bash

# Test Inputs API and Dependent Values API
# This script tests the workflow inputs API and dependent dropdown functionality

echo "Inputs API Testing"
echo "=================="
echo ""

# Configuration
BASE_URL="http://localhost:8000"
TENANT_ID="t001"
GO_ID="GO1"

# Employee user data (from working test script)
EMPLOYEE_USERNAME="employee_test"
EMPLOYEE_PASSWORD="secure123"
EMPLOYEE_USER_ID="U2"

echo "📋 STEP 1: Employee Login and Get Access Token"
echo "============================================================"
echo "Logging in as Employee user..."

# Employee login (using v2 API)
EMPLOYEE_LOGIN_RESPONSE=$(curl -s -X POST "$BASE_URL/api/v2/auth/login" \
  -H "Content-Type: application/json" \
  -d "{\"username\": \"$EMPLOYEE_USERNAME\", \"password\": \"$EMPLOYEE_PASSWORD\"}")

if echo "$EMPLOYEE_LOGIN_RESPONSE" | grep -q "access_token"; then
    echo "✅ Employee login successful!"
    EMPLOYEE_TOKEN=$(echo "$EMPLOYEE_LOGIN_RESPONSE" | jq -r '.access_token')
    EMPLOYEE_ROLES=$(echo "$EMPLOYEE_LOGIN_RESPONSE" | jq -r '.user.roles[]' | tr '\n' ' ')
    echo "Employee roles: $EMPLOYEE_ROLES"
    echo ""
else
    echo "❌ Employee login failed!"
    echo "Response: $EMPLOYEE_LOGIN_RESPONSE"
    exit 1
fi

echo "Employee Access token: ${EMPLOYEE_TOKEN:0:50}..."
echo ""

echo "📋 STEP 2: Create Workflow Instance"
echo "============================================================"
echo "Creating workflow instance for GO: $GO_ID..."

# Create workflow instance (using v2 API)
CREATE_RESPONSE=$(curl -s -X POST "$BASE_URL/api/v2/workflow_instances/?tenant_id=$TENANT_ID" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $EMPLOYEE_TOKEN" \
  -d "{
    \"go_id\": \"$GO_ID\",
    \"tenant_id\": \"$TENANT_ID\",
    \"user_id\": \"$EMPLOYEE_USER_ID\",
    \"test_mode\": false
  }")

if echo "$CREATE_RESPONSE" | grep -q "instance_id"; then
    echo "✅ Workflow instance created!"
    INSTANCE_ID=$(echo "$CREATE_RESPONSE" | jq -r '.instance_id')
    INSTANCE_STATUS=$(echo "$CREATE_RESPONSE" | jq -r '.status')
    echo ""
    echo "Instance ID: $INSTANCE_ID"
    echo "Status: $INSTANCE_STATUS"
    echo ""
else
    echo "❌ Workflow instance creation failed!"
    echo "Response: $CREATE_RESPONSE"
    exit 1
fi

echo "📋 STEP 3: Start Workflow Instance"
echo "============================================================"
echo "Starting workflow instance..."

# Start workflow instance (using v2 API)
START_RESPONSE=$(curl -s -X POST "$BASE_URL/api/v2/workflow_instances/$INSTANCE_ID/start?tenant_id=$TENANT_ID" \
  -H "Authorization: Bearer $EMPLOYEE_TOKEN" \
  -H "Content-Type: application/json" \
  -d "{\"user_id\": \"$EMPLOYEE_USER_ID\"}")

if echo "$START_RESPONSE" | grep -q "current_lo_id"; then
    echo "✅ Workflow instance started!"
    CURRENT_LO=$(echo "$START_RESPONSE" | jq -r '.current_lo_id')
    WORKFLOW_STATUS=$(echo "$START_RESPONSE" | jq -r '.status')
    echo ""
    echo "Current LO: $CURRENT_LO"
    echo "Status: $WORKFLOW_STATUS"
    echo ""
else
    echo "❌ Workflow instance start failed!"
    echo "Response: $START_RESPONSE"
    exit 1
fi

echo "📋 STEP 4: Fetch Workflow Instance Inputs"
echo "============================================================"
echo "Fetching input fields for current LO: $CURRENT_LO..."

# Fetch inputs (using v2 API)
INPUTS_RESPONSE=$(curl -s -X GET "$BASE_URL/api/v2/local_objectives/instances/$INSTANCE_ID/inputs?tenant_id=$TENANT_ID" \
  -H "Authorization: Bearer $EMPLOYEE_TOKEN")

if echo "$INPUTS_RESPONSE" | grep -q "local_objective"; then
    echo "✅ Inputs fetched successfully!"
    echo ""
    
    # Parse and display input categories
    USER_INPUTS_COUNT=$(echo "$INPUTS_RESPONSE" | jq '.user_inputs | length')
    SYSTEM_INPUTS_COUNT=$(echo "$INPUTS_RESPONSE" | jq '.system_inputs | length')
    DEPENDENT_INPUTS_COUNT=$(echo "$INPUTS_RESPONSE" | jq '.dependent_inputs | length')
    
    echo "📊 Input Processing Categories:"
    echo "   👤 User Inputs: $USER_INPUTS_COUNT fields"
    echo "   🤖 System Inputs: $SYSTEM_INPUTS_COUNT fields"
    echo "   🔗 Dependent Inputs: $DEPENDENT_INPUTS_COUNT fields"
    echo ""
    
    # Show detailed input structure
    echo "📋 Detailed Input Structure:"
    echo "============================"
    echo "$INPUTS_RESPONSE" | jq '.'
    echo ""
    
    # Check for dependent inputs
    if [ "$DEPENDENT_INPUTS_COUNT" -gt 0 ]; then
        echo "📋 STEP 5: Testing Dependent Dropdown API"
        echo "============================================================"
        echo "Found $DEPENDENT_INPUTS_COUNT dependent inputs. Testing dependent dropdown functionality..."
        echo ""
        
        # Get dependent input details
        DEPENDENT_INPUTS=$(echo "$INPUTS_RESPONSE" | jq -r '.dependent_inputs[0]')
        DEPENDENT_INPUT_ID=$(echo "$DEPENDENT_INPUTS" | jq -r '.item_id')
        DEPENDENT_ATTRIBUTE=$(echo "$DEPENDENT_INPUTS" | jq -r '.dependent_attribute_value')
        
        echo "Dependent Input ID: $DEPENDENT_INPUT_ID"
        echo "Depends on: $DEPENDENT_ATTRIBUTE"
        echo ""
        
        # Test dependent dropdown with sample values
        echo "Testing dependent dropdown with sample parent value..."
        
        # Example: Test with leave type = "annual leave"
        DEPENDENT_RESPONSE=$(curl -s -X GET "$BASE_URL/api/v2/local_objectives/instances/$INSTANCE_ID/inputs?tenant_id=$TENANT_ID&dependent_input_id=$DEPENDENT_INPUT_ID&leaveTypeName=Annual%20Leave" \
          -H "Authorization: Bearer $EMPLOYEE_TOKEN")
        
        echo "📊 Dependent Dropdown Response:"
        echo "$DEPENDENT_RESPONSE" | jq '.'
        echo ""
        
    else
        echo "📋 No dependent inputs found in this workflow."
        echo ""
    fi
    
    # Show user inputs that need to be filled
    echo "📋 User Inputs Required:"
    echo "======================="
    echo "$INPUTS_RESPONSE" | jq -r '.user_inputs[] | "• \(.name // .attribute_name // .item_id) (\(.data_type // "unknown")) - Required: \(.required)"'
    echo ""
    
    # Show system inputs that are auto-populated
    echo "📋 System Inputs (Auto-populated):"
    echo "=================================="
    echo "$INPUTS_RESPONSE" | jq -r '.system_inputs[] | "• \(.name // .attribute_name // .item_id): \(.input_value // "null")"'
    echo ""
    
else
    echo "❌ Failed to fetch inputs!"
    echo "Response: $INPUTS_RESPONSE"
    exit 1
fi

echo "🎉 Inputs API Testing Completed Successfully!"
echo "============================================="
echo ""
echo "📊 Test Results Summary:"
echo "   Instance ID: $INSTANCE_ID"
echo "   Current LO: $CURRENT_LO"
echo "   User Inputs: $USER_INPUTS_COUNT"
echo "   System Inputs: $SYSTEM_INPUTS_COUNT"
echo "   Dependent Inputs: $DEPENDENT_INPUTS_COUNT"
echo ""
echo "📋 Next Steps:"
echo "• Review the input structure above"
echo "• Test dependent dropdown APIs if applicable"
echo "• Proceed with local_objective/execute API testing"
echo ""
