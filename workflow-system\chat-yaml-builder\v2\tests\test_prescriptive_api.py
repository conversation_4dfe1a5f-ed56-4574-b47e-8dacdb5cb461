import unittest
import json
from fastapi.testclient import TestClient
from v2.api.prescriptive_api import app

class TestPrescriptiveAPI(unittest.TestCase):
    """
    Unit tests for the prescriptive API.
    """
    
    def setUp(self):
        self.client = TestClient(app)
    
    def test_validate_entity(self):
        """
        Test validating an entity.
        """
        # Valid entity
        request_data = {
            "component_type": "entities",
            "prescriptive_text": "Employee has id^PK, name, email, department, status (Active, Inactive, OnLeave).",
            "validate_only": True
        }
        
        response = self.client.post("/validate", json=request_data)
        self.assertEqual(response.status_code, 200)
        result = response.json()
        self.assertTrue(result["is_valid"])
        
        # Invalid entity (no primary key)
        request_data = {
            "component_type": "entities",
            "prescriptive_text": "Employee has id, name, email, department.",
            "validate_only": True
        }
        
        response = self.client.post("/validate", json=request_data)
        self.assertEqual(response.status_code, 200)
        result = response.json()
        self.assertFalse(result["is_valid"])
    
    def test_get_entities(self):
        """
        Test getting entities.
        """
        response = self.client.get("/entities")
        self.assertEqual(response.status_code, 200)