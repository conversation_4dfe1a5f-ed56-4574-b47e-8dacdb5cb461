#!/usr/bin/env python3
"""
Script to show entity details from the database, including validations and relationships.
"""

import sys
import os
import json
import argparse
import psycopg2
from psycopg2.extras import RealDictCursor

# Add parent directory to path to import modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def get_db_connection(schema='workflow_temp'):
    """Get a database connection with the specified schema."""
    try:
        conn = psycopg2.connect(
            host="localhost",
            database="workflow",
            user="postgres",
            password="postgres",
            options=f"-c search_path={schema}"
        )
        return conn
    except Exception as e:
        print(f"Error connecting to database: {str(e)}")
        sys.exit(1)

def show_entities(schema):
    """Show all entities in the database."""
    conn = get_db_connection(schema)
    cursor = conn.cursor(cursor_factory=RealDictCursor)
    
    try:
        print(f"\n=== Entities in schema '{schema}' ===")
        cursor.execute("SELECT entity_id, entity_name, description FROM entities ORDER BY entity_name")
        entities = cursor.fetchall()
        
        if not entities:
            print("No entities found.")
            return
        
        for entity in entities:
            print(f"Entity: {entity['entity_name']} (ID: {entity['entity_id']})")
            print(f"  Description: {entity['description'] or 'N/A'}")
            
            # Get attributes
            cursor.execute("""
                SELECT attribute_id, attribute_name, data_type, is_required, is_primary_key, is_foreign_key, default_value
                FROM entity_attributes
                WHERE entity_id = %s
                ORDER BY attribute_name
            """, (entity['entity_id'],))
            attributes = cursor.fetchall()
            
            print(f"  Attributes ({len(attributes)}):")
            for attr in attributes:
                attr_flags = []
                if attr['is_primary_key']:
                    attr_flags.append("PK")
                if attr['is_foreign_key']:
                    attr_flags.append("FK")
                if attr['is_required']:
                    attr_flags.append("Required")
                
                flags_str = f" [{', '.join(attr_flags)}]" if attr_flags else ""
                default_str = f", Default: {attr['default_value']}" if attr['default_value'] else ""
                
                print(f"    {attr['attribute_name']} ({attr['data_type'] or 'string'}){flags_str}{default_str}")
            
            # Get validations
            cursor.execute("""
                SELECT v.validation_id, v.validation_name, v.validation_type, v.attribute_id, a.attribute_name, 
                       v.constraint_expression, v.error_message
                FROM entity_validations v
                JOIN entity_attributes a ON v.attribute_id = a.attribute_id
                WHERE a.entity_id = %s
                ORDER BY a.attribute_name, v.validation_name
            """, (entity['entity_id'],))
            validations = cursor.fetchall()
            
            if validations:
                print(f"  Validations ({len(validations)}):")
                for val in validations:
                    error_msg = f", Error: {val['error_message']}" if val['error_message'] else ""
                    print(f"    {val['attribute_name']}: {val['constraint_expression']}{error_msg}")
            
            # Get relationships
            cursor.execute("""
                SELECT r.relationship_id, r.relationship_type, r.source_entity_id, se.entity_name as source_entity_name,
                       r.target_entity_id, te.entity_name as target_entity_name,
                       r.source_attribute_id, sa.attribute_name as source_attribute_name,
                       r.target_attribute_id, ta.attribute_name as target_attribute_name
                FROM entity_relationships r
                JOIN entities se ON r.source_entity_id = se.entity_id
                JOIN entities te ON r.target_entity_id = te.entity_id
                JOIN entity_attributes sa ON r.source_attribute_id = sa.attribute_id
                JOIN entity_attributes ta ON r.target_attribute_id = ta.attribute_id
                WHERE r.source_entity_id = %s
                ORDER BY te.entity_name
            """, (entity['entity_id'],))
            relationships = cursor.fetchall()
            
            if relationships:
                print(f"  Relationships ({len(relationships)}):")
                for rel in relationships:
                    print(f"    {rel['relationship_type']} to {rel['target_entity_name']} "
                          f"({rel['source_attribute_name']} -> {rel['target_attribute_name']})")
            
            print()
    
    except Exception as e:
        print(f"Error querying database: {str(e)}")
    finally:
        cursor.close()
        conn.close()

def main():
    parser = argparse.ArgumentParser(description='Show entity details from the database.')
    parser.add_argument('--schema', default='workflow_temp', help='Database schema to use')
    args = parser.parse_args()
    
    show_entities(args.schema)

if __name__ == "__main__":
    main()
