# Authentication Flow API Documentation

This document provides a comprehensive guide to the authentication flow in the Workflow System, from user registration to making authenticated API calls.

## Overview

The Workflow System uses JWT (JSON Web Tokens) for authentication. The authentication flow consists of the following steps:

1. User Registration
2. Login to obtain access and refresh tokens
3. Using the access token to make authenticated API calls
4. Refreshing the access token when it expires
5. Logging out to revoke the tokens

## Base URL

All API endpoints are relative to the base URL: `http://localhost:8000/api/v1`

## Authentication Endpoints

### 1. User Registration

Register a new user in the system.

**Endpoint:** `POST /auth/auth/register`

**Request Headers:**
```
Content-Type: application/json
```

**Request Body:**
```json
{
  "username": "testuser",
  "email": "<EMAIL>",
  "password": "password123",
  "first_name": "Test",
  "last_name": "User",
  "roles": ["User"],
  "org_unit_id": null,
  "tenant_id": null
}
```

**Response (201 Created):**
```json
{
  "user_id": "d6dc308f-db9e-4e2e-a0b5-3dae7c4bae1c",
  "username": "testuser",
  "email": "<EMAIL>",
  "first_name": "Test",
  "last_name": "User",
  "status": "active",
  "roles": ["User"],
  "org_units": [],
  "tenant_id": "t001",
  "disabled": false
}
```

**Error Response (400 Bad Request):**
```json
{
  "detail": "User with this username or email already exists"
}
```

### 2. Login

Authenticate a user and obtain access and refresh tokens.

**Endpoint:** `POST /auth/auth/token`

**Request Headers:**
```
Content-Type: application/x-www-form-urlencoded
```

**Request Body:**
```
username=testuser&password=password123
```

**Response (200 OK):**
```json
{
  "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "refresh_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "token_type": "bearer",
  "expires_at": 1745736936
}
```

**Error Response (401 Unauthorized):**
```json
{
  "detail": "Incorrect username or password"
}
```

### 3. Get Current User

Get information about the currently authenticated user.

**Endpoint:** `GET /auth/auth/me`

**Request Headers:**
```
Authorization: Bearer {access_token}
```

**Response (200 OK):**
```json
{
  "user_id": "d6dc308f-db9e-4e2e-a0b5-3dae7c4bae1c",
  "username": "testuser",
  "email": "<EMAIL>",
  "first_name": "Test",
  "last_name": "User",
  "status": "active",
  "roles": ["User"],
  "org_units": [],
  "tenant_id": "t001",
  "disabled": false
}
```

**Error Response (401 Unauthorized):**
```json
{
  "detail": "Not authenticated"
}
```

### 4. Refresh Token

Refresh an expired access token using a refresh token.

**Endpoint:** `POST /auth/auth/refresh`

**Request Headers:**
```
Content-Type: application/json
```

**Request Body:**
```json
{
  "refresh_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
}
```

**Response (200 OK):**
```json
{
  "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "refresh_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "token_type": "bearer",
  "expires_at": 1745736965
}
```

**Error Response (401 Unauthorized):**
```json
{
  "detail": "Invalid refresh token"
}
```

### 5. Logout

Logout the current user by revoking their tokens.

**Endpoint:** `POST /auth/auth/logout`

**Request Headers:**
```
Authorization: Bearer {access_token}
```

**Response (204 No Content):**
```
(No content in response body)
```

**Error Response (401 Unauthorized):**
```json
{
  "detail": "Not authenticated"
}
```

### 6. Logout All Sessions

Logout the current user from all active sessions.

**Endpoint:** `POST /auth/auth/logout-all`

**Request Headers:**
```
Authorization: Bearer {access_token}
```

**Response (204 No Content):**
```
(No content in response body)
```

**Error Response (401 Unauthorized):**
```json
{
  "detail": "Not authenticated"
}
```

## Authentication Flow Example

Here's a complete example of the authentication flow using curl commands:

### 1. Register a new user

```bash
curl -X POST "http://localhost:8000/api/v1/auth/auth/register" \
  -H "Content-Type: application/json" \
  -d '{
    "username": "testuser",
    "email": "<EMAIL>",
    "password": "password123",
    "first_name": "Test",
    "last_name": "User"
  }'
```

### 2. Login to get access and refresh tokens

```bash
curl -X POST "http://localhost:8000/api/v1/auth/auth/token" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "username=testuser&password=password123"
```

### 3. Access a protected endpoint

```bash
curl -X GET "http://localhost:8000/api/v1/auth/auth/me" \
  -H "Authorization: Bearer {access_token}"
```

### 4. Refresh the access token

```bash
curl -X POST "http://localhost:8000/api/v1/auth/auth/refresh" \
  -H "Content-Type: application/json" \
  -d '{
    "refresh_token": "{refresh_token}"
  }'
```

### 5. Logout

```bash
curl -X POST "http://localhost:8000/api/v1/auth/auth/logout" \
  -H "Authorization: Bearer {access_token}"
```

## JWT Token Structure

The access token is a JWT token with the following structure:

**Header:**
```json
{
  "alg": "HS256",
  "typ": "JWT"
}
```

**Payload:**
```json
{
  "sub": "d6dc308f-db9e-4e2e-a0b5-3dae7c4bae1c",  // User ID
  "username": "testuser",                         // Username
  "roles": ["User"],                              // User roles
  "permissions": [],                              // User permissions
  "org_units": [],                                // User organizational units
  "tenant_id": "t001",                            // Tenant ID
  "exp": 1745736965                               // Expiration timestamp
}
```

## Security Considerations

1. Always use HTTPS in production to protect tokens in transit
2. Store tokens securely on the client side
3. Implement token expiration and refresh mechanisms
4. Revoke tokens when they are no longer needed
5. Use proper CORS settings to prevent cross-origin attacks

## Error Handling

The API returns standard HTTP status codes and JSON error responses:

- 200 OK: Request successful
- 201 Created: Resource created successfully
- 204 No Content: Request successful, no content to return
- 400 Bad Request: Invalid request parameters
- 401 Unauthorized: Authentication required or failed
- 403 Forbidden: Permission denied
- 404 Not Found: Resource not found
- 422 Unprocessable Entity: Validation error
- 500 Internal Server Error: Server error

## Conclusion

This document provides a comprehensive guide to the authentication flow in the Workflow System. By following this guide, you can implement a secure authentication flow in your application.
