"""
Test script for GO Parser and Deployer

This script tests the GO parser and deployer with the new features:
- GO-to-GO mappings
- Entity validation
- Hierarchy support
- Process Mining Schema
- Validation Checklist
"""

import os
import sys
import yaml
import json
import logging
import importlib.util
from typing import Dict, List, Tuple, Any

# Add parent directory to path to import modules
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
from parsers.go_parser import parse_go_definitions
from registry_validator import RegistryValidator

# Mock the db_utils module for testing
sys.modules['db_utils'] = importlib.import_module('mock_db_utils')

# Import the deployer after mocking db_utils
from deployers.go_deployer import deploy_go_definitions

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('test_go_parser_deployer')

def load_sample_go_definition() -> str:
    """
    Load a sample GO definition from a file.
    
    Returns:
        Sample GO definition as a string
    """
    try:
        sample_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'samples', 'sample_go_output.txt')
        with open(sample_path, 'r') as f:
            return f.read()
    except Exception as e:
        logger.error(f"Error loading sample GO definition: {str(e)}")
        return ""

def test_go_parser() -> Tuple[bool, Dict, List[str]]:
    """
    Test the GO parser with a sample GO definition.
    
    Returns:
        Tuple containing:
            - Boolean indicating if parsing was successful
            - Parsed GO data
            - List of warning messages
    """
    logger.info("Testing GO parser")
    
    # Load sample GO definition
    sample_go_def = load_sample_go_definition()
    if not sample_go_def:
        logger.error("Failed to load sample GO definition")
        return False, {}, ["Failed to load sample GO definition"]
    
    # Parse GO definition
    try:
        go_data, warnings = parse_go_definitions(sample_go_def)
        
        # Check if parsing was successful
        if not go_data or "global_objectives" not in go_data or not go_data["global_objectives"]:
            logger.error("GO parsing failed: No global objectives found")
            return False, go_data, warnings + ["No global objectives found"]
        
        # Log parsing results
        logger.info(f"Successfully parsed {len(go_data['global_objectives'])} global objectives")
        for go_name in go_data['global_objectives']:
            logger.info(f"- {go_name}")
            
            # Print detailed GO data
            go_def = go_data['global_objectives'][go_name]
            logger.info(f"  ID: {go_def.get('id', 'N/A')}")
            logger.info(f"  Description: {go_def.get('description', 'N/A')}")
            
            # Print local objectives
            if 'local_objectives' in go_def:
                logger.info(f"  Local Objectives:")
                for lo in go_def['local_objectives']:
                    if isinstance(lo, dict):
                        logger.info(f"    - {lo.get('id', 'N/A')}: {lo.get('name', 'N/A')} ({lo.get('type', 'N/A')})")
                    else:
                        logger.info(f"    - {lo}")
            
            # Print entity references
            if 'entity_references' in go_def:
                logger.info(f"  Entity References:")
                for entity_ref in go_def['entity_references']:
                    entity_name = entity_ref.get('name', 'N/A')
                    logger.info(f"    - {entity_name}")
                    if 'attributes' in entity_ref:
                        for attr in entity_ref['attributes']:
                            required = attr.get('required', False)
                            logger.info(f"      - {attr.get('name', 'N/A')}{' (required)' if required else ''}")
            
            # Print GO references
            if 'go_references' in go_def:
                logger.info(f"  GO References:")
                for go_ref in go_def['go_references']:
                    go_name = go_ref.get('name', 'N/A')
                    go_type = go_ref.get('type', 'N/A')
                    entity = go_ref.get('entity', 'N/A')
                    attribute = go_ref.get('attribute', 'N/A')
                    logger.info(f"    - {go_name} ({go_type}) -> {entity}.{attribute}")
            
            # Print business rules
            if 'business_rules' in go_def:
                logger.info(f"  Business Rules:")
                for rule in go_def['business_rules']:
                    rule_number = rule.get('number', 'N/A')
                    rule_description = rule.get('description', 'N/A')
                    enforced_by = rule.get('enforced_by', 'N/A')
                    logger.info(f"    - Rule {rule_number}: {rule_description} (Enforced by: {enforced_by})")
            
            # Print data constraints
            if 'data_constraints' in go_def:
                logger.info(f"  Data Constraints:")
                for constraint in go_def['data_constraints']:
                    entity = constraint.get('entity', 'N/A')
                    attribute = constraint.get('attribute', 'N/A')
                    data_type = constraint.get('data_type', 'N/A')
                    constraint_text = constraint.get('constraint', 'N/A')
                    error_message = constraint.get('error_message', 'N/A')
                    logger.info(f"    - {entity}.{attribute} ({data_type}) is {constraint_text}. Error: \"{error_message}\"")
            
            # Print process ownership
            if 'process_ownership' in go_def:
                logger.info(f"  Process Ownership:")
                for role, name in go_def['process_ownership'].items():
                    logger.info(f"    - {role.capitalize()}: {name}")
            
            # Print sample data
            if 'sample_data' in go_def:
                logger.info(f"  Sample Data:")
                for entity, attributes in go_def['sample_data'].items():
                    logger.info(f"    - {entity}:")
                    for attr, values in attributes.items():
                        logger.info(f"      - {attr}: {values}")
            
            # Print performance metadata
            if 'performance_metadata' in go_def:
                logger.info(f"  Performance Metadata:")
                if 'cycle_time' in go_def['performance_metadata']:
                    logger.info(f"    - Cycle Time: {go_def['performance_metadata']['cycle_time']}")
                if 'number_of_pathways' in go_def['performance_metadata']:
                    logger.info(f"    - Number of Pathways: {go_def['performance_metadata']['number_of_pathways']}")
                if 'sla_thresholds' in go_def['performance_metadata']:
                    logger.info(f"    - SLA Thresholds:")
                    for sla_name, sla_value in go_def['performance_metadata']['sla_thresholds'].items():
                        logger.info(f"      - {sla_name}: {sla_value}")
        
        if warnings:
            logger.warning(f"Parsing completed with {len(warnings)} warnings")
            for warning in warnings:
                logger.warning(f"- {warning}")
        
        return True, go_data, warnings
    except Exception as e:
        logger.error(f"Error parsing GO definition: {str(e)}", exc_info=True)
        return False, {}, [f"Error parsing GO definition: {str(e)}"]

def test_registry_validator(go_data: Dict) -> Tuple[bool, List[str]]:
    """
    Test the registry validator with the parsed GO data.
    
    Args:
        go_data: Parsed GO data
        
    Returns:
        Tuple containing:
            - Boolean indicating if validation passed
            - List of error messages
    """
    logger.info("Testing registry validator")
    
    try:
        # Create a validator
        validator = RegistryValidator()
        
        # Convert GO data to YAML string
        go_yaml = yaml.dump(go_data)
        
        # Create a sample entity definition that matches the entities referenced in the GO definitions
        entities_yaml = """
        entities:
          LeaveRequest:
            attributes:
              id:
                type: string
              employeeId:
                type: string
              startDate:
                type: date
              endDate:
                type: date
              status:
                type: string
              type:
                type: string
          Employee:
            attributes:
              id:
                type: string
              name:
                type: string
              department:
                type: string
              manager:
                type: string
              employeeId:
                type: string
              firstName:
                type: string
              lastName:
                type: string
              email:
                type: string
              departmentId:
                type: string
              managerId:
                type: string
          LeaveType:
            attributes:
              id:
                type: string
              name:
                type: string
              description:
                type: string
          LeaveApplication:
            attributes:
              id:
                type: string
              employeeId:
                type: string
              startDate:
                type: date
              endDate:
                type: date
              status:
                type: string
          CalendarEvent:
            attributes:
              id:
                type: string
              title:
                type: string
              startDate:
                type: date
              endDate:
                type: date
              employeeId:
                type: string
        """
        
        # Create components dictionary
        components = {
            'go_definitions': go_yaml,
            'entities': entities_yaml
        }
        
        # Validate registry
        is_valid, errors = validator.validate_registry(components)
        
        # Log validation results
        if is_valid:
            logger.info("Registry validation passed")
        else:
            logger.error(f"Registry validation failed with {len(errors)} errors")
            for error in errors:
                logger.error(f"- {error}")
        
        return is_valid, errors
    except Exception as e:
        logger.error(f"Error validating registry: {str(e)}", exc_info=True)
        return False, [f"Error validating registry: {str(e)}"]

def test_go_deployer(go_data: Dict) -> Tuple[bool, List[str]]:
    """
    Test the GO deployer with the parsed GO data.
    
    Args:
        go_data: Parsed GO data
        
    Returns:
        Tuple containing:
            - Boolean indicating if deployment was successful
            - List of messages
    """
    logger.info("Testing GO deployer")
    
    try:
        # Deploy GO definitions to test schema
        schema_name = "test_schema"
        success, messages = deploy_go_definitions(go_data, schema_name)
        
        # Log deployment results
        if success:
            logger.info(f"Successfully deployed GO definitions to {schema_name}")
        else:
            logger.error(f"GO deployment failed with {len(messages)} messages")
            for message in messages:
                logger.error(f"- {message}")
        
        return success, messages
    except Exception as e:
        logger.error(f"Error deploying GO definitions: {str(e)}", exc_info=True)
        return False, [f"Error deploying GO definitions: {str(e)}"]

def main():
    """Main test function."""
    logger.info("Starting GO parser and deployer test")
    
    # Test GO parser
    parser_success, go_data, parser_warnings = test_go_parser()
    if not parser_success:
        logger.error("GO parser test failed")
        return
    
    # Test registry validator
    validator_success, validator_errors = test_registry_validator(go_data)
    if not validator_success:
        logger.warning("Registry validation failed, but continuing with deployment test")
    
    # Test GO deployer
    deployer_success, deployer_messages = test_go_deployer(go_data)
    if not deployer_success:
        logger.error("GO deployer test failed")
        return
    
    logger.info("GO parser and deployer test completed successfully")

if __name__ == "__main__":
    main()
