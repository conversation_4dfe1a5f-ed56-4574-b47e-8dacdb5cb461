{"success": false, "component_type": "entities", "target_schema": "workflow_temp", "validation_timestamp": "2025-06-23T13:33:13.488801", "errors": [{"rule_id": "RULE_11", "message": "Entity 'E13' must have at least one attribute", "severity": "error", "entity_id": "E13", "attribute_id": null, "timestamp": "2025-06-23T13:33:13.488771"}], "warnings": [{"rule_id": "RULE_07", "message": "Version mismatch between schemas for entity E13: 3 vs 1", "severity": "warning", "entity_id": "E13", "attribute_id": null, "timestamp": "2025-06-23T13:33:13.482288"}], "error_count": 1, "warning_count": 1}