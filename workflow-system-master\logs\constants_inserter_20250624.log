{"timestamp": "2025-06-24T04:45:03.485608", "operation": "process_mongo_constants_to_workflow_temp", "input_data": {"schema": "workflow_temp"}, "result": {"total_processed": 0, "successful_inserts": 0, "failed_inserts": 0, "details": []}, "status": "success"}
{"timestamp": "2025-06-24T11:42:53.278225", "operation": "insert_constant_to_workflow_runtime", "input_data": {"_id": "6858f30f1195bbcbf7aa7691", "constant_id": 1004, "attribute": "allow_override", "value": "false", "description": "Whether this constant can be overridden", "tenant_id": "T101", "allow_override": false, "override_permissions": "", "status": "deployed_to_temp", "entity_name": "", "attribute_name": "", "created_at": "2025-06-23T09:02:49.471376", "updated_at": "2025-06-23T14:03:51.090866", "created_by": "Tarun", "updated_by": "Tarun", "constant_status": "new", "changes_detected": [], "version": 2}, "result": {"success": true, "inserted_id": 1004, "schema": "workflow_runtime", "constant_id": 1004, "attribute": "allow_override", "original_constant_id": 1004, "entity_reference_validation": {"entity_exists": true, "attribute_exists": true, "entity_name": "", "attribute_name": "", "warnings": []}}, "status": "success"}
{"timestamp": "2025-06-24T11:42:53.281191", "operation": "process_mongo_constants_to_workflow_runtime", "input_data": {"schema": "workflow_runtime"}, "result": {"total_processed": 1, "successful_inserts": 1, "failed_inserts": 0, "details": [{"constant_id": 1004, "status": "success", "details": {"success": true, "inserted_id": 1004, "schema": "workflow_runtime", "constant_id": 1004, "attribute": "allow_override", "original_constant_id": 1004, "entity_reference_validation": {"entity_exists": true, "attribute_exists": true, "entity_name": "", "attribute_name": "", "warnings": []}}}]}, "status": "success"}
{"timestamp": "2025-06-24T13:14:31.356139", "operation": "insert_constant_to_workflow_temp", "input_data": {"_id": "685a9df2a30d43107e83f576", "constant_id": 1005, "attribute": "MAX_TEST_ITEMS", "value": "100", "description": "Maximum number of test items allowed", "tenant_id": "t999", "allow_override": false, "override_permissions": "", "status": "draft", "entity_name": "", "attribute_name": "", "created_at": "2025-06-24T12:45:38.607968", "updated_at": "2025-06-24T12:45:38.607976", "created_by": "Tarun", "updated_by": "Tarun", "constant_status": "new", "changes_detected": [], "version": 1}, "result": {"success": true, "inserted_id": 1005, "schema": "workflow_temp", "constant_id": 1005, "attribute": "MAX_TEST_ITEMS", "original_constant_id": 1005, "entity_reference_validation": {"entity_exists": true, "attribute_exists": true, "entity_name": "", "attribute_name": "", "warnings": []}}, "status": "success"}
{"timestamp": "2025-06-24T13:14:31.359452", "operation": "process_mongo_constants_to_workflow_temp", "input_data": {"schema": "workflow_temp"}, "result": {"total_processed": 1, "successful_inserts": 1, "failed_inserts": 0, "details": [{"constant_id": 1005, "status": "success", "details": {"success": true, "inserted_id": 1005, "schema": "workflow_temp", "constant_id": 1005, "attribute": "MAX_TEST_ITEMS", "original_constant_id": 1005, "entity_reference_validation": {"entity_exists": true, "attribute_exists": true, "entity_name": "", "attribute_name": "", "warnings": []}}}]}, "status": "success"}
