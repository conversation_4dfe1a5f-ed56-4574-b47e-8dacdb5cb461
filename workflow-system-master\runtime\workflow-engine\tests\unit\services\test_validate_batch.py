import unittest
from unittest.mock import patch, MagicMock
from typing import Dict, Any
import sys
import os

# Add project root to path if needed
project_root = '/home/<USER>/workflow-system/runtime/workflow-engine'
if project_root not in sys.path:
    sys.path.insert(0, project_root)

# Import the function to test
from app.services.system_functions import validate_batch, validate_pattern

class TestValidateBatch(unittest.TestCase):
    @patch('app.services.system_functions.function_repository')
    @patch('app.services.system_functions.validate_pattern')
    def test_required_rule(self, mock_validate_pattern, mock_repo):
        """Test validation with required rule."""
        # Setup mock
        mock_repo.auto_execute.return_value = {"is_valid": True, "message": "Field is valid"}
        
        # Test data
        values = {"name": "John"}
        rules = {"name": "required"}
        
        # Execute
        result = validate_batch(values, rules)
        
        # Verify
        self.assertTrue(result["is_valid"])
        self.assertEqual(result["message"], "All validations passed")
        self.assertTrue(result["fields"]["name"]["is_valid"])
        
        # Verify correct function was called
        mock_repo.auto_execute.assert_called_once_with("validate_required", "John")

    @patch('app.services.system_functions.function_repository')
    @patch('app.services.system_functions.validate_pattern')
    def test_email_rule(self, mock_validate_pattern, mock_repo):
        """Test validation with email rule."""
        # Setup mock
        mock_repo.auto_execute.return_value = {"is_valid": True, "message": "Valid email"}
        
        # Test data
        values = {"email": "<EMAIL>"}
        rules = {"email": "email"}
        
        # Execute
        result = validate_batch(values, rules)
        
        # Verify
        self.assertTrue(result["is_valid"])
        self.assertTrue(result["fields"]["email"]["is_valid"])
        
        # Verify correct function was called
        mock_repo.auto_execute.assert_called_once_with("validate_email", "<EMAIL>")

    @patch('app.services.system_functions.function_repository')
    @patch('app.services.system_functions.validate_pattern')
    def test_pattern_rule(self, mock_validate_pattern, mock_repo):
        """Test validation with pattern rule."""
        # Setup mock
        mock_validate_pattern.return_value = {"is_valid": True, "message": "Pattern matched"}
        
        # Test data
        values = {"code": "ABC-123"}
        rules = {"code": "pattern:^[A-Z]+-\\d+$"}
        
        # Execute
        result = validate_batch(values, rules)
        
        # Verify
        self.assertTrue(result["is_valid"])
        self.assertTrue(result["fields"]["code"]["is_valid"])
        
        # Verify correct function was called
        mock_validate_pattern.assert_called_once_with("^[A-Z]+-\\d+$", "ABC-123")

    @patch('app.services.system_functions.function_repository')
    @patch('app.services.system_functions.validate_pattern')
    def test_min_rule(self, mock_validate_pattern, mock_repo):
        """Test validation with min rule."""
        # Setup mock
        mock_repo.auto_execute.return_value = {"is_valid": True, "message": "Value is valid"}
        
        # Test data
        values = {"age": 25}
        rules = {"age": "min:18"}
        
        # Execute
        result = validate_batch(values, rules)
        
        # Verify
        self.assertTrue(result["is_valid"])
        self.assertTrue(result["fields"]["age"]["is_valid"])
        
        # Verify correct function was called
        mock_repo.auto_execute.assert_called_once_with("compare", a=25, b=18, operator=">=")

    @patch('app.services.system_functions.function_repository')
    @patch('app.services.system_functions.validate_pattern')
    def test_max_rule(self, mock_validate_pattern, mock_repo):
        """Test validation with max rule."""
        # Setup mock
        mock_repo.auto_execute.return_value = {"is_valid": True, "message": "Value is valid"}
        
        # Test data
        values = {"quantity": 5}
        rules = {"quantity": "max:10"}
        
        # Execute
        result = validate_batch(values, rules)
        
        # Verify
        self.assertTrue(result["is_valid"])
        self.assertTrue(result["fields"]["quantity"]["is_valid"])
        
        # Verify correct function was called
        mock_repo.auto_execute.assert_called_once_with("compare", a=5, b=10, operator="<=")

    @patch('app.services.system_functions.function_repository')
    @patch('app.services.system_functions.validate_pattern')
    def test_enum_rule(self, mock_validate_pattern, mock_repo):
        """Test validation with enum rule."""
        # Setup mock
        mock_repo.auto_execute.return_value = {"is_valid": True, "message": "Value is valid"}
        
        # Test data
        values = {"status": "active"}
        rules = {"status": "enum:active,pending,inactive"}
        
        # Execute
        result = validate_batch(values, rules)
        
        # Verify
        self.assertTrue(result["is_valid"])
        self.assertTrue(result["fields"]["status"]["is_valid"])
        
        # Verify correct function was called
        mock_repo.auto_execute.assert_called_once_with(
            "enum_check", 
            value="active", 
            allowed_values=["active", "pending", "inactive"]
        )

    @patch('app.services.system_functions.function_repository')
    @patch('app.services.system_functions.validate_pattern')
    def test_multiple_fields(self, mock_validate_pattern, mock_repo):
        """Test validation with multiple fields."""
        # Setup mock
        mock_repo.auto_execute.side_effect = [
            {"is_valid": True, "message": "Name is valid"},    # name required
            {"is_valid": True, "message": "Email is valid"}    # email
        ]
        
        # Test data
        values = {"name": "John", "email": "<EMAIL>"}
        rules = {
            "name": "required",
            "email": "email"
        }
        
        # Execute
        result = validate_batch(values, rules)
        
        # Verify
        self.assertTrue(result["is_valid"])
        self.assertEqual(len(result["fields"]), 2)
        self.assertTrue(result["fields"]["name"]["is_valid"])
        self.assertTrue(result["fields"]["email"]["is_valid"])
        
        # Verify function calls
        self.assertEqual(mock_repo.auto_execute.call_count, 2)

    @patch('app.services.system_functions.function_repository')
    @patch('app.services.system_functions.validate_pattern')
    def test_failed_validation(self, mock_validate_pattern, mock_repo):
        """Test validation with a failing rule."""
        # Setup mock
        mock_repo.auto_execute.return_value = {"is_valid": False, "message": "Value too small"}
        
        # Test data
        values = {"age": 15}
        rules = {"age": "min:18"}
        
        # Execute
        result = validate_batch(values, rules)
        
        # Verify
        self.assertFalse(result["is_valid"])
        self.assertEqual(result["message"], "One or more validations failed")
        self.assertFalse(result["fields"]["age"]["is_valid"])
        self.assertEqual(result["fields"]["age"]["message"], "Value too small")

    @patch('app.services.system_functions.function_repository')
    @patch('app.services.system_functions.validate_pattern')
    def test_missing_field(self, mock_validate_pattern, mock_repo):
        """Test validation with a field that doesn't exist in values."""
        # Setup mock
        mock_repo.auto_execute.return_value = {"is_valid": False, "message": "Field is required"}
        
        # Test data
        values = {}  # Missing field
        rules = {"name": "required"}
        
        # Execute
        result = validate_batch(values, rules)
        
        # Verify
        self.assertFalse(result["is_valid"])
        mock_repo.auto_execute.assert_called_once_with("validate_required", None)

    @patch('app.services.system_functions.function_repository')
    @patch('app.services.system_functions.validate_pattern')
    def test_unknown_rule(self, mock_validate_pattern, mock_repo):
        """Test validation with an unknown rule."""
        # Test data
        values = {"name": "John"}
        rules = {"name": "unknown_rule"}
        
        # Execute
        result = validate_batch(values, rules)
        
        # Verify
        self.assertFalse(result["is_valid"])
        self.assertFalse(result["fields"]["name"]["is_valid"])
        self.assertEqual(result["fields"]["name"]["message"], "Unknown validation rule: unknown_rule")

    @patch('app.services.system_functions.function_repository')
    @patch('app.services.system_functions.validate_pattern')
    def test_exception_during_validation(self, mock_validate_pattern, mock_repo):
        """Test handling of exceptions during validation."""
        # Setup mock to raise an exception
        mock_repo.auto_execute.side_effect = Exception("Test exception")
        
        # Test data
        values = {"name": "John"}
        rules = {"name": "required"}
        
        # Execute
        result = validate_batch(values, rules)
        
        # Verify
        self.assertFalse(result["is_valid"])
        self.assertFalse(result["fields"]["name"]["is_valid"])
        self.assertTrue("Error executing validation: Test exception" in result["fields"]["name"]["message"])

    @patch('app.services.system_functions.function_repository')
    @patch('app.services.system_functions.validate_pattern')
    def test_invalid_min_value(self, mock_validate_pattern, mock_repo):
        """Test with invalid min value (not a number)."""
        # Test data
        values = {"age": 25}
        rules = {"age": "min:abc"}  # Invalid numeric value
        
        # Execute
        result = validate_batch(values, rules)
        
        # Verify
        self.assertFalse(result["is_valid"])
        self.assertFalse(result["fields"]["age"]["is_valid"])
        self.assertTrue("Error executing validation:" in result["fields"]["age"]["message"])

    @patch('app.services.system_functions.function_repository')
    @patch('app.services.system_functions.validate_pattern')
    def test_empty_enum_values(self, mock_validate_pattern, mock_repo):
        """Test with empty enum values."""
        # Setup mock
        mock_repo.auto_execute.return_value = {"is_valid": False, "message": "Invalid value"}
        
        # Test data
        values = {"status": "active"}
        rules = {"status": "enum:"}  # Empty enum values
        
        # Execute
        result = validate_batch(values, rules)
        
        # Verify
        self.assertFalse(result["is_valid"])
        mock_repo.auto_execute.assert_called_once_with(
            "enum_check", 
            value="active", 
            allowed_values=[""]  # The split would result in [""]
        )

    @patch('app.services.system_functions.function_repository')
    @patch('app.services.system_functions.validate_pattern')
    def test_mixed_validation_results(self, mock_validate_pattern, mock_repo):
        """Test with mixed validation results (one pass, one fail)."""
        # Setup mock
        mock_repo.auto_execute.side_effect = [
            {"is_valid": True, "message": "Name is valid"},
            {"is_valid": False, "message": "Invalid email"}
        ]
        
        # Test data
        values = {"name": "John", "email": "invalid-email"}
        rules = {
            "name": "required",
            "email": "email"
        }
        
        # Execute
        result = validate_batch(values, rules)
        
        # Verify
        self.assertFalse(result["is_valid"])
        self.assertEqual(result["message"], "One or more validations failed")
        self.assertTrue(result["fields"]["name"]["is_valid"])
        self.assertFalse(result["fields"]["email"]["is_valid"])
        
        # Verify function calls
        self.assertEqual(mock_repo.auto_execute.call_count, 2)

if __name__ == '__main__':
    unittest.main()