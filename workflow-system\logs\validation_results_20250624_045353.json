{"success": false, "component_type": "entities", "target_schema": "workflow_temp", "validation_timestamp": "2025-06-24T04:53:53.254018", "errors": [{"rule_id": "RULE_11", "message": "Entity 'E13' must have at least one attribute", "severity": "error", "entity_id": "E13", "attribute_id": null, "timestamp": "2025-06-24T04:53:53.253998"}], "warnings": [{"rule_id": "RULE_02", "message": "Invalid status transition from deployed_to_temp to deployed_to_temp", "severity": "warning", "entity_id": "E13", "attribute_id": null, "timestamp": "2025-06-24T04:53:53.220270"}, {"rule_id": "RULE_07", "message": "Version mismatch between schemas for entity E13: 2 vs 1", "severity": "warning", "entity_id": "E13", "attribute_id": null, "timestamp": "2025-06-24T04:53:53.246962"}], "error_count": 1, "warning_count": 2}