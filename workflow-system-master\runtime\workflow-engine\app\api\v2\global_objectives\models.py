"""
Global Objectives Models for v2 API

This module contains Pydantic models for global objectives endpoints.
"""

from typing import List, Optional, Dict, Any
from pydantic import BaseModel, Field
from datetime import datetime


class PathwayDefinitionModel(BaseModel):
    """Pathway definition model"""
    id: Optional[int] = Field(None, description="Internal ID")
    pathway_definitions_id: Optional[str] = Field(None, description="Pathway definition ID")
    go_id: Optional[str] = Field(None, description="Global objective ID")
    pathway_number: Optional[int] = Field(None, description="Pathway number")
    pathway_name: Optional[str] = Field(None, description="Pathway name")
    steps: Optional[str] = Field(None, description="Pathway steps")
    created_at: Optional[datetime] = Field(None, description="Creation timestamp")
    created_by: Optional[str] = Field(None, description="Created by user")
    updated_at: Optional[datetime] = Field(None, description="Update timestamp")
    updated_by: Optional[str] = Field(None, description="Updated by user")

    class Config:
        from_attributes = True


class PathwayFrequencyModel(BaseModel):
    """Pathway frequency model"""
    id: Optional[int] = Field(None, description="Internal ID")
    performance_discovery_id: Optional[str] = Field(None, description="Performance discovery ID")
    go_id: Optional[str] = Field(None, description="Global objective ID")
    pathway_name: Optional[str] = Field(None, description="Pathway name")
    frequency: Optional[int] = Field(None, description="Frequency count")
    percentage: Optional[int] = Field(None, description="Percentage")
    average_duration: Optional[str] = Field(None, description="Average duration")
    success_rate: Optional[int] = Field(None, description="Success rate")
    created_at: Optional[datetime] = Field(None, description="Creation timestamp")
    created_by: Optional[str] = Field(None, description="Created by user")
    updated_at: Optional[datetime] = Field(None, description="Update timestamp")
    updated_by: Optional[str] = Field(None, description="Updated by user")

    class Config:
        from_attributes = True


class LocalObjectiveModel(BaseModel):
    """Local objective model"""
    id: Optional[int] = Field(None, description="Internal ID")
    lo_id: Optional[str] = Field(None, description="Local objective ID")
    go_id: Optional[str] = Field(None, description="Global objective ID")
    lo_number: Optional[int] = Field(None, description="Local objective number")
    lo_name: Optional[str] = Field(None, description="Local objective name")
    actor_type: Optional[str] = Field(None, description="Actor type")
    created_at: Optional[datetime] = Field(None, description="Creation timestamp")
    created_by: Optional[str] = Field(None, description="Created by user")
    updated_at: Optional[datetime] = Field(None, description="Update timestamp")
    updated_by: Optional[str] = Field(None, description="Updated by user")

    class Config:
        from_attributes = True


class ValidationRuleDetailModel(BaseModel):
    """Validation rule detail model"""
    id: Optional[int] = Field(None, description="Internal ID")
    rule_id: Optional[str] = Field(None, description="Rule ID")
    go_validation_rule_id: Optional[str] = Field(None, description="GO validation rule ID")
    go_id: Optional[str] = Field(None, description="Global objective ID")
    rule_name: Optional[str] = Field(None, description="Rule name")
    rule_inputs: Optional[str] = Field(None, description="Rule inputs")
    rule_operation: Optional[str] = Field(None, description="Rule operation")
    rule_description: Optional[str] = Field(None, description="Rule description")
    rule_output: Optional[str] = Field(None, description="Rule output")
    rule_error: Optional[str] = Field(None, description="Rule error")
    rule_validation: Optional[str] = Field(None, description="Rule validation")
    created_at: Optional[datetime] = Field(None, description="Creation timestamp")
    created_by: Optional[str] = Field(None, description="Created by user")
    updated_at: Optional[datetime] = Field(None, description="Update timestamp")
    updated_by: Optional[str] = Field(None, description="Updated by user")

    class Config:
        from_attributes = True


class GlobalObjectiveResponse(BaseModel):
    """Global objective response model with all related data"""
    # Main global objective fields
    go_id: str = Field(..., description="Global objective ID")
    name: str = Field(..., description="Global objective name")
    version: Optional[str] = Field(None, description="Version")
    status: Optional[str] = Field(None, description="Status")
    description: Optional[str] = Field(None, description="Description")
    created_at: Optional[datetime] = Field(None, description="Creation timestamp")
    updated_at: Optional[datetime] = Field(None, description="Update timestamp")
    tenant_id: Optional[str] = Field(None, description="Tenant ID")
    last_used: Optional[datetime] = Field(None, description="Last used timestamp")
    deleted_mark: Optional[bool] = Field(None, description="Deleted mark")
    version_type: Optional[str] = Field(None, description="Version type")
    metadata: Optional[Dict[str, Any]] = Field(None, description="Metadata")
    auto_id: Optional[int] = Field(None, description="Auto ID")
    primary_entity: Optional[str] = Field(None, description="Primary entity")
    classification: Optional[str] = Field(None, description="Classification")
    tenant_name: Optional[str] = Field(None, description="Tenant name")
    book_id: Optional[str] = Field(None, description="Book ID")
    book_name: Optional[str] = Field(None, description="Book name")
    chapter_id: Optional[str] = Field(None, description="Chapter ID")
    chapter_name: Optional[str] = Field(None, description="Chapter name")
    created_by: Optional[str] = Field(None, description="Created by user")
    updated_by: Optional[str] = Field(None, description="Updated by user")
    
    # Related data
    pathway_definitions: List[PathwayDefinitionModel] = Field(default=[], description="Pathway definitions")
    pathway_frequency: List[PathwayFrequencyModel] = Field(default=[], description="Pathway frequency data")
    local_objectives: List[LocalObjectiveModel] = Field(default=[], description="Local objectives")
    validation_rule_details: List[ValidationRuleDetailModel] = Field(default=[], description="Validation rule details")

    class Config:
        from_attributes = True
        schema_extra = {
            "example": {
                "go_id": "GO001",
                "name": "Sample Global Objective",
                "version": "1.0",
                "status": "active",
                "description": "Sample description",
                "tenant_id": "t001",
                "book_id": "BOOK001",
                "book_name": "Sample Book",
                "chapter_id": "CHAP001",
                "chapter_name": "Sample Chapter",
                "pathway_definitions": [],
                "pathway_frequency": [],
                "local_objectives": [],
                "validation_rule_details": []
            }
        }


class GlobalObjectiveListResponse(BaseModel):
    """Global objective list response model"""
    objectives: List[GlobalObjectiveResponse] = Field(..., description="List of global objectives")
    total_count: int = Field(..., description="Total count of objectives")
    filtered_count: int = Field(..., description="Count after filtering")

    class Config:
        schema_extra = {
            "example": {
                "objectives": [],
                "total_count": 0,
                "filtered_count": 0
            }
        }


class BookFilterRequest(BaseModel):
    """Book filter request model"""
    book_id: Optional[str] = Field(None, description="Book ID to filter by")
    book_name: Optional[str] = Field(None, description="Book name to filter by")

    class Config:
        schema_extra = {
            "example": {
                "book_id": "BOOK001",
                "book_name": "Sample Book"
            }
        }


class ChapterFilterRequest(BaseModel):
    """Chapter filter request model"""
    chapter_id: Optional[str] = Field(None, description="Chapter ID to filter by")
    chapter_name: Optional[str] = Field(None, description="Chapter name to filter by")
    book_id: Optional[str] = Field(None, description="Book ID to filter by")

    class Config:
        schema_extra = {
            "example": {
                "chapter_id": "CHAP001",
                "chapter_name": "Sample Chapter",
                "book_id": "BOOK001"
            }
        }


class CreateGlobalObjectiveRequest(BaseModel):
    """Create global objective request model"""
    name: str = Field(..., description="Global objective name")
    description: Optional[str] = Field(None, description="Description")
    version: Optional[str] = Field("1.0", description="Version")
    status: Optional[str] = Field("active", description="Status")
    version_type: Optional[str] = Field(None, description="Version type")
    metadata: Optional[Dict[str, Any]] = Field(None, description="Metadata")
    primary_entity: Optional[str] = Field(None, description="Primary entity")
    classification: Optional[str] = Field(None, description="Classification")
    book_id: Optional[str] = Field(None, description="Book ID")
    book_name: Optional[str] = Field(None, description="Book name")
    chapter_id: Optional[str] = Field(None, description="Chapter ID")
    chapter_name: Optional[str] = Field(None, description="Chapter name")

    class Config:
        schema_extra = {
            "example": {
                "name": "New Global Objective",
                "description": "Description of the new objective",
                "version": "1.0",
                "status": "active",
                "book_id": "BOOK001",
                "book_name": "Sample Book",
                "chapter_id": "CHAP001",
                "chapter_name": "Sample Chapter"
            }
        }


class UpdateGlobalObjectiveRequest(BaseModel):
    """Update global objective request model"""
    name: Optional[str] = Field(None, description="Global objective name")
    description: Optional[str] = Field(None, description="Description")
    version: Optional[str] = Field(None, description="Version")
    status: Optional[str] = Field(None, description="Status")
    version_type: Optional[str] = Field(None, description="Version type")
    metadata: Optional[Dict[str, Any]] = Field(None, description="Metadata")
    primary_entity: Optional[str] = Field(None, description="Primary entity")
    classification: Optional[str] = Field(None, description="Classification")
    book_id: Optional[str] = Field(None, description="Book ID")
    book_name: Optional[str] = Field(None, description="Book name")
    chapter_id: Optional[str] = Field(None, description="Chapter ID")
    chapter_name: Optional[str] = Field(None, description="Chapter name")

    class Config:
        schema_extra = {
            "example": {
                "name": "Updated Global Objective",
                "description": "Updated description",
                "status": "active"
            }
        }


class ErrorResponse(BaseModel):
    """Error response model"""
    error: str = Field(..., description="Error type")
    message: str = Field(..., description="Error message")
    details: Optional[Dict[str, Any]] = Field(None, description="Additional error details")

    class Config:
        schema_extra = {
            "example": {
                "error": "VALIDATION_ERROR",
                "message": "Invalid input data",
                "details": {
                    "field": "name",
                    "code": "FIELD_REQUIRED"
                }
            }
        }


class SuccessResponse(BaseModel):
    """Generic success response model"""
    success: bool = Field(True, description="Success indicator")
    message: str = Field(..., description="Success message")
    data: Optional[Dict[str, Any]] = Field(None, description="Response data")

    class Config:
        schema_extra = {
            "example": {
                "success": True,
                "message": "Operation completed successfully",
                "data": {}
            }
        }
