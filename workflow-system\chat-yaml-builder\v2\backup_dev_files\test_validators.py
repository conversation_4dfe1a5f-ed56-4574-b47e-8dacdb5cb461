"""
Test script for system function validator and output validator.

This script tests the functionality of the system function validator and output validator
to ensure they correctly validate system functions and component references.
"""

import os
import sys
import logging
import argparse
from typing import Dict, List, Tuple, Any, Optional

# Add parent directory to path to import common modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from system_function_validator import SystemFunctionValidator
from output_validator import OutputValidator
from prompt_generator import PromptGenerator

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('test_validators.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger('test_validators')

def test_system_function_validator():
    """
    Test the system function validator.
    """
    logger.info("Testing system function validator...")
    
    # Initialize the system function validator
    validator = SystemFunctionValidator()
    
    # Test valid system functions
    valid_functions = [
        "generate_id",
        "current_timestamp",
        "fetch_filtered_records",
        "validate_reference"
    ]
    
    for function_name in valid_functions:
        is_valid, message = validator.validate_function_name(function_name)
        logger.info(f"Function '{function_name}': {is_valid} - {message}")
        assert is_valid, f"Function '{function_name}' should be valid"
    
    # Test invalid system functions
    invalid_functions = [
        "generate_uuid",  # Similar to generate_id
        "get_current_time",  # Similar to current_timestamp
        "fetch_records_filtered",  # Similar to fetch_filtered_records
        "completely_invalid_function"  # No similar function
    ]
    
    for function_name in invalid_functions:
        is_valid, message = validator.validate_function_name(function_name)
        logger.info(f"Function '{function_name}': {is_valid} - {message}")
        assert not is_valid, f"Function '{function_name}' should be invalid"
    
    # Test getting functions by category
    categories = validator.get_all_categories()
    logger.info(f"Categories: {categories}")
    
    for category in categories:
        functions = validator.get_functions_by_category(category)
        logger.info(f"Category '{category}': {functions}")
        assert len(functions) > 0, f"Category '{category}' should have at least one function"
    
    logger.info("System function validator tests passed!")

def test_output_validator():
    """
    Test the output validator.
    """
    logger.info("Testing output validator...")
    
    # Initialize the output validator with a temporary schema
    validator = OutputValidator(schema_name="workflow_temp")
    
    # Test LO output with valid system functions
    valid_lo_output = """
    ## ApplyForLeave

    id: "lo001"
    contextual_id: "go001.lo001"
    name: "Apply for Leave"
    version: "1.0"
    status: "Active"
    workflow_source: "origin"
    function_type: "Create"

    *Employee has execution rights*

    *Inputs: LeaveApplication with leaveID*, employeeID*, startDate*, endDate*, numDays*, reason*, leaveType* (Sick Leave, Annual Leave, Parental Leave, Bereavement), leaveSubType [depends on: leaveType], status* (Pending, Approved, Rejected), instructions [info]*
    * System generates LeaveApplication.leaveID using generate_id with prefix "LV".
    * System calculates LeaveApplication.numDays using subtract_days with startDate and endDate.
    * System defaults LeaveApplication.status to "Pending".
    * System populates LeaveApplication.leaveSubType [depends on: leaveType] using fetch_filtered_records from LeaveSubType where LeaveSubType.leaveType = LeaveApplication.leaveType and LeaveSubType.active = true.
    * System loads LeaveApplication.instructions [info] with constant "leave_application_instructions".

    *Outputs: LeaveApplication with leaveID, employeeID, startDate, endDate, numDays, reason, leaveType, leaveSubType, status*
    * System returns LeaveApplication.leaveID for reference in notifications.
    * System captures LeaveApplication.submissionDate using current_timestamp for audit trails.
    * System transforms LeaveApplication.leaveType for display using format_enum_value.
    """
    
    is_valid, errors = validator.validate_output(valid_lo_output, 'lo_definitions')
    logger.info(f"Valid LO output: {is_valid}")
    if not is_valid:
        for error in errors:
            logger.info(f"- {error}")
    
    # Test LO output with invalid system functions
    invalid_lo_output = """
    ## ApplyForLeave

    id: "lo001"
    contextual_id: "go001.lo001"
    name: "Apply for Leave"
    version: "1.0"
    status: "Active"
    workflow_source: "origin"
    function_type: "Create"

    *Employee has execution rights*

    *Inputs: LeaveApplication with leaveID*, employeeID*, startDate*, endDate*, numDays*, reason*, leaveType* (Sick Leave, Annual Leave, Parental Leave, Bereavement), leaveSubType [depends on: leaveType], status* (Pending, Approved, Rejected), instructions [info]*
    * System generates LeaveApplication.leaveID using generate_uuid with prefix "LV".
    * System calculates LeaveApplication.numDays using calculate_days with startDate and endDate.
    * System defaults LeaveApplication.status to "Pending".
    * System populates LeaveApplication.leaveSubType [depends on: leaveType] using get_filtered_records from LeaveSubType where LeaveSubType.leaveType = LeaveApplication.leaveType and LeaveSubType.active = true.
    * System loads LeaveApplication.instructions [info] with constant "leave_application_instructions".

    *Outputs: LeaveApplication with leaveID, employeeID, startDate, endDate, numDays, reason, leaveType, leaveSubType, status*
    * System returns LeaveApplication.leaveID for reference in notifications.
    * System captures LeaveApplication.submissionDate using get_current_time for audit trails.
    * System transforms LeaveApplication.leaveType for display using format_enum.
    """
    
    is_valid, errors = validator.validate_output(invalid_lo_output, 'lo_definitions')
    logger.info(f"Invalid LO output: {is_valid}")
    if not is_valid:
        for error in errors:
            logger.info(f"- {error}")
    
    assert not is_valid, "Invalid LO output should fail validation"
    
    logger.info("Output validator tests passed!")

def test_prompt_generator():
    """
    Test the prompt generator.
    """
    logger.info("Testing prompt generator...")
    
    # Initialize the prompt generator with a temporary schema
    generator = PromptGenerator(schema_name="workflow_temp")
    
    # Test generating prompts for each component type
    component_types = ['roles', 'entities', 'go_definitions', 'lo_definitions']
    
    for component_type in component_types:
        logger.info(f"Generating prompt for {component_type}...")
        prompt = generator.generate_prompt(component_type)
        
        # Check that the prompt contains the system functions section
        assert "## System Functions Validation" in prompt, f"Prompt for {component_type} should contain system functions section"
        
        # Check that the prompt contains the validation requirements section
        assert "## Validation Requirements" in prompt, f"Prompt for {component_type} should contain validation requirements section"
        
        logger.info(f"Prompt for {component_type} generated successfully")
    
    logger.info("Prompt generator tests passed!")

def main():
    """
    Main function to run the tests.
    """
    parser = argparse.ArgumentParser(description='Test system function validator and output validator')
    parser.add_argument('--test-type', choices=['system_function', 'output', 'prompt', 'all'], default='all',
                        help='Type of test to run')
    
    args = parser.parse_args()
    
    if args.test_type == 'system_function' or args.test_type == 'all':
        test_system_function_validator()
    
    if args.test_type == 'output' or args.test_type == 'all':
        test_output_validator()
    
    if args.test_type == 'prompt' or args.test_type == 'all':
        test_prompt_generator()
    
    logger.info("All tests completed successfully!")

if __name__ == "__main__":
    main()
