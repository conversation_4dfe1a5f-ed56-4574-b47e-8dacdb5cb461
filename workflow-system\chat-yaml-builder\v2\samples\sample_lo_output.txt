## SubmitLeaveRequest

id: "lo001"
contextual_id: "go001.lo001"
name: "Submit Leave Request"
version: "1.0"
status: "Active"
workflow_source: "origin"
function_type: "Create"

*Employee has execution rights*

*Inputs: LeaveApplication with leaveId*, employeeId*, startDate*, endDate*, numDays*, reason*, leaveTypeId* (LT001, LT002, LT003), leaveTypeName* (Annual Leave, Sick Leave, Personal Leave), requiresDocumentation* (true, false), status* (Pending, Approved, Rejected), instructions [info]*
* System generates LeaveApplication.leaveId using generate_id with prefix "LV".
* System calculates LeaveApplication.numDays using subtract_days with startDate and endDate.
* System defaults LeaveApplication.status to "Pending".
* System populates LeaveApplication.leaveTypeName [depends on: leaveTypeId] using fetch_filtered_records from LeaveType where LeaveType.typeId = LeaveApplication.leaveTypeId.
* System loads LeaveApplication.instructions [info] with constant "leave_application_instructions".

*Outputs: LeaveApplication with leaveId, employeeId, startDate, endDate, numDays, reason, leaveTypeId, leaveTypeName, requiresDocumentation, status*
* System returns LeaveApplication.leaveId for reference in notifications.
* System captures LeaveApplication.submissionDate using current_timestamp for audit trails.
* System transforms LeaveApplication.leaveTypeName for display using format_enum_value.

*DB Stack:*
* LeaveApplication.leaveId (string) is mandatory and unique. Error message: "Leave ID is required and must be unique"
* LeaveApplication.employeeId (string) is mandatory and must exist in Employee table. Error message: "Employee ID is required"
* LeaveApplication.startDate (date) is mandatory and must be a valid date. Error message: "Start date is required"
* LeaveApplication.endDate (date) is mandatory and must be after startDate. Error message: "End date must be after start date"
* LeaveApplication.numDays (number) is mandatory and must be greater than 0. Error message: "Number of days must be greater than 0"
* LeaveApplication.reason (string) is mandatory with minimum 10 characters. Error message: "Please provide a detailed reason for your leave request"
* LeaveApplication.leaveTypeId (string) is mandatory and must exist in LeaveType table. Error message: "Please select a valid leave type"
* LeaveApplication.status (enum) must be one of "Pending", "Approved", "Rejected". Error message: "Invalid status value"

*UI Stack:*
* LeaveApplication.leaveId displays as oj-input-text with readonly property.
* LeaveApplication.employeeId displays as oj-input-text with readonly property and value bound to current_user.
* LeaveApplication.startDate displays as oj-input-date with min-value set to current date.
* LeaveApplication.endDate displays as oj-input-date with min-value bound to startDate.
* LeaveApplication.numDays displays as oj-input-number with readonly property.
* LeaveApplication.reason displays as oj-text-area with rows=3 and maxlength=500.
* LeaveApplication.leaveTypeId displays as oj-combobox-one with source from LeaveType entity.
* LeaveApplication.leaveTypeName displays as oj-text with readonly property.
* LeaveApplication.requiresDocumentation displays as oj-switch with readonly property.
* LeaveApplication.instructions [info] displays as oj-text with formatting-class "policy-highlight".
* System provides contextual help for LeaveApplication.leaveTypeId explaining "Select the type of leave you are requesting. Different leave types may have different approval requirements."

*Mapping Stack:*
* SubmitLeaveRequest.output.leaveId maps to UploadDocumentation.input.leaveId using direct mapping.
* SubmitLeaveRequest.output.employeeId maps to UploadDocumentation.input.employeeId using direct mapping.
* SubmitLeaveRequest.output.leaveTypeId maps to UploadDocumentation.input.leaveTypeId using direct mapping.
* SubmitLeaveRequest.output.requiresDocumentation maps to UploadDocumentation.input.requiresDocumentation using direct mapping.
* SubmitLeaveRequest.output.leaveId maps to ReviewLeaveRequest.input.leaveId using direct mapping.
* SubmitLeaveRequest.output.employeeId maps to ReviewLeaveRequest.input.employeeId using direct mapping.
* SubmitLeaveRequest.output.startDate maps to ReviewLeaveRequest.input.startDate using direct mapping.
* SubmitLeaveRequest.output.endDate maps to ReviewLeaveRequest.input.endDate using direct mapping.
* SubmitLeaveRequest.output.numDays maps to ReviewLeaveRequest.input.numDays using direct mapping.
* SubmitLeaveRequest.output.reason maps to ReviewLeaveRequest.input.reason using direct mapping.
* SubmitLeaveRequest.output.leaveTypeId maps to ReviewLeaveRequest.input.leaveTypeId using direct mapping.
* SubmitLeaveRequest.output.leaveTypeName maps to ReviewLeaveRequest.input.leaveTypeName using direct mapping.

Execution pathway:
* When LeaveApplication.requiresDocumentation = true, route to UploadDocumentation.
* When LeaveApplication.requiresDocumentation = false, route to ReviewLeaveRequest.
* When LeaveApplication.leaveTypeId = "LT002" and LeaveApplication.numDays > 3, system flags LeaveApplication.requiresDocumentation to true, route to UploadDocumentation.
* When LeaveApplication.startDate < current_date, system flags LeaveApplication.isRetroactive to true, route to ReviewLeaveRequest.

Synthetic values:
* LeaveApplication.leaveId: "LV-2023-0001", "LV-2023-0002", "LV-2023-0003"
* LeaveApplication.employeeId: "EMP001", "EMP002", "EMP003"
* LeaveApplication.startDate: "2023-06-15", "2023-07-01", "2023-08-10"
* LeaveApplication.endDate: "2023-06-16", "2023-07-05", "2023-08-17"
* LeaveApplication.numDays: 2, 5, 8
* LeaveApplication.reason: "Personal matters", "Family emergency", "Medical procedure"
* LeaveApplication.leaveTypeId: "LT001", "LT002", "LT003"
* LeaveApplication.leaveTypeName: "Annual Leave", "Sick Leave", "Personal Leave"
* LeaveApplication.requiresDocumentation: true, false
* LeaveApplication.status: "Pending"
* LeaveApplication.submissionDate: "2023-06-01T09:15:30", "2023-06-15T14:22:45", "2023-07-05T10:05:12"

## ReviewLeaveRequest

id: "lo003"
contextual_id: "go001.lo003"
name: "Review Leave Request"
version: "1.0"
status: "Active"
workflow_source: "intermediate"
function_type: "Update"

*Manager has execution rights*

*Inputs: LeaveApplication with leaveId*, employeeId*, startDate*, endDate*, numDays*, reason*, leaveTypeId*, leaveTypeName*, status* (Pending, Approved, Rejected), isRetroactive (true, false), requiresDocumentation (true, false), documentationUploaded (true, false), comments [optional], employeeInfo [info]*
* System loads LeaveApplication.employeeInfo [info] using fetch_by_id from Employee where Employee.employeeId = LeaveApplication.employeeId.
* System calculates LeaveApplication.remainingBalance using fetch_leave_balance with employeeId and leaveTypeId.
* System checks LeaveApplication.teamAvailability using check_team_availability with departmentId, startDate, and endDate.
* System defaults LeaveApplication.status to "Pending".

*Outputs: LeaveApplication with leaveId, employeeId, startDate, endDate, numDays, reason, leaveTypeId, leaveTypeName, status, comments, approvedBy, approvalDate*
* System returns LeaveApplication.status for downstream processing.
* System captures LeaveApplication.approvalDate using current_timestamp when status changes to "Approved" or "Rejected".
* System captures LeaveApplication.approvedBy using current_user when status changes to "Approved" or "Rejected".

*DB Stack:*
* LeaveApplication.leaveId (string) is mandatory and unique. Error message: "Leave ID is required and must be unique"
* LeaveApplication.status (enum) must be one of "Pending", "Approved", "Rejected". Error message: "Please select a valid status"
* LeaveApplication.comments (string) is required when status = "Rejected". Error message: "Please provide a reason for rejection"

*UI Stack:*
* LeaveApplication.leaveId displays as oj-input-text with readonly property.
* LeaveApplication.employeeId displays as oj-input-text with readonly property.
* LeaveApplication.employeeInfo [info] displays as oj-form-layout with readonly property.
* LeaveApplication.startDate displays as oj-input-date with readonly property.
* LeaveApplication.endDate displays as oj-input-date with readonly property.
* LeaveApplication.numDays displays as oj-input-number with readonly property.
* LeaveApplication.reason displays as oj-text-area with readonly property.
* LeaveApplication.leaveTypeName displays as oj-text with readonly property.
* LeaveApplication.remainingBalance displays as oj-progress-bar with thresholds.
* LeaveApplication.teamAvailability displays as oj-chart with team availability visualization.
* LeaveApplication.status displays as oj-radioset with options "Approved", "Rejected".
* LeaveApplication.comments displays as oj-text-area with rows=3 and maxlength=500.
* LeaveApplication.comments visibility depends on LeaveApplication.status = "Rejected".
* System provides contextual help for LeaveApplication.status explaining "Approve or reject the leave request based on team availability and business needs."

*Mapping Stack:*
* ReviewLeaveRequest.output.leaveId maps to ApproveLeaveRequest.input.leaveId using direct mapping.
* ReviewLeaveRequest.output.employeeId maps to ApproveLeaveRequest.input.employeeId using direct mapping.
* ReviewLeaveRequest.output.status maps to ApproveLeaveRequest.input.status using direct mapping.
* ReviewLeaveRequest.output.leaveId maps to RejectLeaveRequest.input.leaveId using direct mapping.
* ReviewLeaveRequest.output.employeeId maps to RejectLeaveRequest.input.employeeId using direct mapping.
* ReviewLeaveRequest.output.status maps to RejectLeaveRequest.input.status using direct mapping.
* ReviewLeaveRequest.output.comments maps to RejectLeaveRequest.input.comments using direct mapping.

Execution pathway:
* When LeaveApplication.status = "Approved", route to ApproveLeaveRequest.
* When LeaveApplication.status = "Rejected", route to RejectLeaveRequest.
* When LeaveApplication.numDays > LeaveApplication.remainingBalance, system flags LeaveApplication.insufficientBalance to true.
* When LeaveApplication.teamAvailability < 50%, system flags LeaveApplication.lowTeamAvailability to true.

Synthetic values:
* LeaveApplication.leaveId: "LV-2023-0001", "LV-2023-0002", "LV-2023-0003"
* LeaveApplication.employeeId: "EMP001", "EMP002", "EMP003"
* LeaveApplication.startDate: "2023-06-15", "2023-07-01", "2023-08-10"
* LeaveApplication.endDate: "2023-06-16", "2023-07-05", "2023-08-17"
* LeaveApplication.numDays: 2, 5, 8
* LeaveApplication.reason: "Personal matters", "Family emergency", "Medical procedure"
* LeaveApplication.leaveTypeId: "LT001", "LT002", "LT003"
* LeaveApplication.leaveTypeName: "Annual Leave", "Sick Leave", "Personal Leave"
* LeaveApplication.status: "Approved", "Rejected"
* LeaveApplication.comments: "Approved as requested", "Team is understaffed during this period", "Please reschedule for next month"
* LeaveApplication.approvedBy: "MGR001", "MGR002", "MGR003"
* LeaveApplication.approvalDate: "2023-06-02T10:30:45", "2023-06-16T11:15:20", "2023-07-06T09:45:30"
* LeaveApplication.remainingBalance: 10, 15, 20
* LeaveApplication.teamAvailability: 75, 60, 45
