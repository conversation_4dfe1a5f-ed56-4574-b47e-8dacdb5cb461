"""
LO Parser for YAML Builder v2

This module provides functionality for parsing natural language prescriptives
into structured Local Objective (LO) data for database storage.
"""

import os
import sys
import re
import logging
from typing import Dict, List, Tuple, Any, Optional

# Set up logging
logger = logging.getLogger('lo_parser')

def parse_lo_definitions(prescriptive_text: str) -> Tuple[Dict, List[str]]:
    """
    Parse natural language prescriptives into structured LO data.
    
    Args:
        prescriptive_text: Natural language prescriptive text
        
    Returns:
        Tuple containing:
            - Structured LO data dictionary
            - List of warning messages (empty if no warnings)
    """
    warnings = []
    local_objectives = {}
    
    try:
        # Split the text into LO definitions
        lo_blocks = re.split(r'\n\s*\n', prescriptive_text)
        
        for block in lo_blocks:
            if not block.strip():
                continue
            
            # Parse LO definition
            lo_data, lo_warnings = parse_lo_block(block)
            warnings.extend(lo_warnings)
            
            if lo_data:
                lo_name = lo_data.get('lo_name')
                if lo_name:
                    local_objectives[lo_name] = lo_data
                else:
                    warnings.append(f"LO without a name found in block: {block[:100]}...")
        
        if not local_objectives:
            warnings.append("No local objectives found in the prescriptive text")
            return {"local_objectives": {}}, warnings
        
        return {"local_objectives": local_objectives}, warnings
    except Exception as e:
        logger.error(f"Error parsing local objectives: {str(e)}", exc_info=True)
        warnings.append(f"Error parsing local objectives: {str(e)}")
        return {"local_objectives": {}}, warnings

def parse_lo_block(block: str) -> Tuple[Dict, List[str]]:
    """
    Parse a single LO block.
    
    Args:
        block: LO block text
        
    Returns:
        Tuple containing:
            - LO data dictionary
            - List of warning messages (empty if no warnings)
    """
    warnings = []
    lo_data = {
        "input_stack": [],
        "output_stack": [],
        "execution_pathways": [],
        "execution_pathway_conditions": [],
        "nested_functions": [],
        "system_functions": [],
        "input_validations": []
    }
    
    try:
        # Extract LO name and function type
        first_line_match = re.match(r'Local Objective: (\w+) - (\w+)', block)
        if first_line_match:
            lo_name = first_line_match.group(1)
            function_type = first_line_match.group(2)
            
            lo_data['lo_name'] = lo_name
            lo_data['function_type'] = function_type
        else:
            warnings.append(f"Could not extract LO name and function type from block: {block[:100]}...")
            return {}, warnings
        
        # Extract description
        description_match = re.search(r'Description:(.*?)(?:\n\s*\n|\n\w|\Z)', block, re.DOTALL)
        if description_match:
            description = description_match.group(1).strip()
            lo_data['description'] = description
        
        # Extract version
        version_match = re.search(r'Version: ([\d\.]+)', block)
        if version_match:
            version = version_match.group(1)
            lo_data['version'] = version
        else:
            lo_data['version'] = '1.0'  # Default version
        
        # Extract status
        status_match = re.search(r'Status: (\w+)', block)
        if status_match:
            status = status_match.group(1)
            lo_data['status'] = status
        else:
            lo_data['status'] = 'Active'  # Default status
        
        # Extract GO reference
        go_ref_match = re.search(r'GO Reference: ([\w-]+)', block)
        if go_ref_match:
            go_id = go_ref_match.group(1)
            lo_data['go_id'] = go_id
        
        # Extract workflow source
        workflow_source_match = re.search(r'Workflow Source: (\w+)', block)
        if workflow_source_match:
            workflow_source = workflow_source_match.group(1)
            lo_data['workflow_source'] = workflow_source
        
        # Extract agent type
        agent_type_match = re.search(r'Agent Type: (\w+)', block)
        if agent_type_match:
            agent_type = agent_type_match.group(1)
            lo_data['agent_type'] = agent_type
        
        # Extract tenant, book, and chapter
        tenant_match = re.search(r'Tenant: (\w+)', block)
        if tenant_match:
            tenant = tenant_match.group(1)
            lo_data['tenant_name'] = tenant
        
        book_match = re.search(r'Book: (\w+)', block)
        if book_match:
            book = book_match.group(1)
            lo_data['book_name'] = book
        
        chapter_match = re.search(r'Chapter: (\w+)', block)
        if chapter_match:
            chapter = chapter_match.group(1)
            lo_data['chapter_name'] = chapter
        
        # Extract input stack
        input_stack_match = re.search(r'Input Stack:(.*?)(?:\n\s*\n|\nOutput Stack|\Z)', block, re.DOTALL)
        if input_stack_match:
            input_stack_text = input_stack_match.group(1)
            input_stack, input_warnings = parse_input_stack(input_stack_text)
            lo_data['input_stack'] = input_stack
            warnings.extend(input_warnings)
        
        # Extract output stack
        output_stack_match = re.search(r'Output Stack:(.*?)(?:\n\s*\n|\nExecution Pathways|\Z)', block, re.DOTALL)
        if output_stack_match:
            output_stack_text = output_stack_match.group(1)
            output_stack, output_warnings = parse_output_stack(output_stack_text)
            lo_data['output_stack'] = output_stack
            warnings.extend(output_warnings)
        
        # Extract execution pathways
        execution_pathways_match = re.search(r'Execution Pathways:(.*?)(?:\n\s*\n|\nExecution Pathway Conditions|\Z)', block, re.DOTALL)
        if execution_pathways_match:
            execution_pathways_text = execution_pathways_match.group(1)
            execution_pathways, pathway_warnings = parse_execution_pathways(execution_pathways_text)
            lo_data['execution_pathways'] = execution_pathways
            warnings.extend(pathway_warnings)
        
        # Extract execution pathway conditions
        execution_pathway_conditions_match = re.search(r'Execution Pathway Conditions:(.*?)(?:\n\s*\n|\nNested Functions|\Z)', block, re.DOTALL)
        if execution_pathway_conditions_match:
            execution_pathway_conditions_text = execution_pathway_conditions_match.group(1)
            execution_pathway_conditions, condition_warnings = parse_execution_pathway_conditions(execution_pathway_conditions_text)
            lo_data['execution_pathway_conditions'] = execution_pathway_conditions
            warnings.extend(condition_warnings)
        
        # Extract nested functions
        nested_functions_match = re.search(r'Nested Functions:(.*?)(?:\n\s*\n|\nSystem Functions|\Z)', block, re.DOTALL)
        if nested_functions_match:
            nested_functions_text = nested_functions_match.group(1)
            nested_functions, function_warnings = parse_nested_functions(nested_functions_text)
            lo_data['nested_functions'] = nested_functions
            warnings.extend(function_warnings)
        
        # Extract system functions
        system_functions_match = re.search(r'System Functions:(.*?)(?:\n\s*\n|\nInput Validations|\Z)', block, re.DOTALL)
        if system_functions_match:
            system_functions_text = system_functions_match.group(1)
            system_functions, function_warnings = parse_system_functions(system_functions_text)
            lo_data['system_functions'] = system_functions
            warnings.extend(function_warnings)
        
        # Extract input validations
        input_validations_match = re.search(r'Input Validations:(.*?)(?:\n\s*\n|\Z)', block, re.DOTALL)
        if input_validations_match:
            input_validations_text = input_validations_match.group(1)
            input_validations, validation_warnings = parse_input_validations(input_validations_text)
            lo_data['input_validations'] = input_validations
            warnings.extend(validation_warnings)
        
        # Clean up empty lists
        for key in list(lo_data.keys()):
            if isinstance(lo_data[key], list) and not lo_data[key]:
                del lo_data[key]
        
        return lo_data, warnings
    except Exception as e:
        logger.error(f"Error parsing LO block: {str(e)}", exc_info=True)
        warnings.append(f"Error parsing LO block: {str(e)}")
        return {}, warnings

def parse_input_stack(input_stack_text: str) -> Tuple[List[Dict], List[str]]:
    """
    Parse input stack from text.
    
    Args:
        input_stack_text: Input stack text
        
    Returns:
        Tuple containing:
            - List of input items
            - List of warning messages (empty if no warnings)
    """
    warnings = []
    input_items = []
    
    try:
        # Split input stack by newline
        input_lines = input_stack_text.strip().split('\n')
        
        for line in input_lines:
            line = line.strip()
            if not line:
                continue
            
            # Parse input line
            input_match = re.match(r'(\d+)\. (\w+) from (.*?)(?:\s+\((\w+)\))?(?:\s+\[UI: ([\w-]+)\])?(?:\s+\{(.*?)\})?(?:\s+\(required\)|\s*$)', line)
            if input_match:
                slot_id = input_match.group(1)
                attribute = input_match.group(2)
                source = input_match.group(3)
                data_type = input_match.group(4) if len(input_match.groups()) > 3 and input_match.group(4) else None
                ui_control = input_match.group(5) if len(input_match.groups()) > 4 and input_match.group(5) else None
                system_function = input_match.group(6) if len(input_match.groups()) > 5 and input_match.group(6) else None
                required = 'required' in line
                
                # Parse entity reference
                entity_ref = None
                attr_ref = None
                
                if '.' in source:
                    entity_ref, attr_ref = source.split('.', 1)
                else:
                    entity_ref = source
                
                input_item = {
                    "slot_id": slot_id,
                    "entity_reference": entity_ref,
                    "required": required
                }
                
                if attr_ref:
                    input_item["attribute_reference"] = attr_ref
                
                if data_type:
                    input_item["data_type"] = data_type
                
                if ui_control:
                    input_item["ui_control"] = ui_control
                
                if system_function:
                    input_item["system_function"] = system_function
                
                input_items.append(input_item)
            else:
                warnings.append(f"Could not parse input line: {line}")
        
        return input_items, warnings
    except Exception as e:
        logger.error(f"Error parsing input stack: {str(e)}", exc_info=True)
        warnings.append(f"Error parsing input stack: {str(e)}")
        return [], warnings

def parse_output_stack(output_stack_text: str) -> Tuple[List[Dict], List[str]]:
    """
    Parse output stack from text.
    
    Args:
        output_stack_text: Output stack text
        
    Returns:
        Tuple containing:
            - List of output items
            - List of warning messages (empty if no warnings)
    """
    warnings = []
    output_items = []
    
    try:
        # Split output stack by newline
        output_lines = output_stack_text.strip().split('\n')
        
        for line in output_lines:
            line = line.strip()
            if not line:
                continue
            
            # Parse output line
            output_match = re.match(r'(\d+)\. (\w+) to (.*?)(?:\s+\((\w+)\)|\s*$)', line)
            if output_match:
                slot_id = output_match.group(1)
                attribute = output_match.group(2)
                target = output_match.group(3)
                data_type = output_match.group(4) if len(output_match.groups()) > 3 else None
                
                # Parse entity reference
                entity_ref = None
                attr_ref = None
                
                if '.' in target:
                    entity_ref, attr_ref = target.split('.', 1)
                else:
                    entity_ref = target
                
                output_item = {
                    "slot_id": slot_id,
                    "output_entity": entity_ref
                }
                
                if attr_ref:
                    output_item["output_attribute"] = attr_ref
                
                if data_type:
                    output_item["data_type"] = data_type
                
                output_items.append(output_item)
            else:
                warnings.append(f"Could not parse output line: {line}")
        
        return output_items, warnings
    except Exception as e:
        logger.error(f"Error parsing output stack: {str(e)}", exc_info=True)
        warnings.append(f"Error parsing output stack: {str(e)}")
        return [], warnings

def parse_execution_pathways(execution_pathways_text: str) -> Tuple[List[Dict], List[str]]:
    """
    Parse execution pathways from text.
    
    Args:
        execution_pathways_text: Execution pathways text
        
    Returns:
        Tuple containing:
            - List of execution pathways
            - List of warning messages (empty if no warnings)
    """
    warnings = []
    execution_pathways = []
    
    try:
        # Split execution pathways by newline
        pathway_lines = execution_pathways_text.strip().split('\n')
        
        for line in pathway_lines:
            line = line.strip()
            if not line:
                continue
            
            # Parse pathway line
            pathway_match = re.match(r'(\w+) -> (\w+)(?:\s+\((\w+)\))?', line)
            if pathway_match:
                current_lo = pathway_match.group(1)
                next_lo = pathway_match.group(2)
                pathway_type = pathway_match.group(3) if len(pathway_match.groups()) > 2 else 'unconditional'
                
                pathway = {
                    "current_lo": current_lo,
                    "next_lo": next_lo,
                    "pathway_type": pathway_type
                }
                
                execution_pathways.append(pathway)
            else:
                warnings.append(f"Could not parse execution pathway line: {line}")
        
        return execution_pathways, warnings
    except Exception as e:
        logger.error(f"Error parsing execution pathways: {str(e)}", exc_info=True)
        warnings.append(f"Error parsing execution pathways: {str(e)}")
        return [], warnings

def parse_execution_pathway_conditions(execution_pathway_conditions_text: str) -> Tuple[List[Dict], List[str]]:
    """
    Parse execution pathway conditions from text.
    
    Args:
        execution_pathway_conditions_text: Execution pathway conditions text
        
    Returns:
        Tuple containing:
            - List of execution pathway conditions
            - List of warning messages (empty if no warnings)
    """
    warnings = []
    execution_pathway_conditions = []
    
    try:
        # Split execution pathway conditions by newline
        condition_lines = execution_pathway_conditions_text.strip().split('\n')
        
        for line in condition_lines:
            line = line.strip()
            if not line:
                continue
            
            # Parse condition line
            condition_match = re.match(r'If (.*?) then (\w+)', line)
            if condition_match:
                condition = condition_match.group(1)
                next_lo = condition_match.group(2)
                
                # Parse condition
                entity_attr_match = re.match(r'(\w+)\.(\w+)\s+([=!<>]+)\s+(.*)', condition)
                if entity_attr_match:
                    condition_entity = entity_attr_match.group(1)
                    condition_attribute = entity_attr_match.group(2)
                    condition_operator = entity_attr_match.group(3)
                    condition_value = entity_attr_match.group(4)
                    
                    condition_data = {
                        "condition_entity": condition_entity,
                        "condition_attribute": condition_attribute,
                        "condition_operator": condition_operator,
                        "condition_value": condition_value,
                        "next_lo": next_lo
                    }
                    
                    execution_pathway_conditions.append(condition_data)
                else:
                    # Simple condition
                    condition_data = {
                        "condition": condition,
                        "next_lo": next_lo
                    }
                    
                    execution_pathway_conditions.append(condition_data)
            else:
                warnings.append(f"Could not parse execution pathway condition line: {line}")
        
        return execution_pathway_conditions, warnings
    except Exception as e:
        logger.error(f"Error parsing execution pathway conditions: {str(e)}", exc_info=True)
        warnings.append(f"Error parsing execution pathway conditions: {str(e)}")
        return [], warnings

def parse_nested_functions(nested_functions_text: str) -> Tuple[List[Dict], List[str]]:
    """
    Parse nested functions from text.
    
    Args:
        nested_functions_text: Nested functions text
        
    Returns:
        Tuple containing:
            - List of nested functions
            - List of warning messages (empty if no warnings)
    """
    warnings = []
    nested_functions = []
    
    try:
        # Split nested functions by newline
        function_lines = nested_functions_text.strip().split('\n')
        
        for line in function_lines:
            line = line.strip()
            if not line:
                continue
            
            # Parse function line
            function_match = re.match(r'(\w+)\((.*?)\) -> (.*)', line)
            if function_match:
                function_name = function_match.group(1)
                parameters = function_match.group(2)
                writes_to = function_match.group(3)
                
                # Parse parameters
                param_list = [param.strip() for param in parameters.split(',') if param.strip()]
                
                function = {
                    "function_name": function_name,
                    "parameters": param_list,
                    "writes_to": writes_to
                }
                
                nested_functions.append(function)
            else:
                warnings.append(f"Could not parse nested function line: {line}")
        
        return nested_functions, warnings
    except Exception as e:
        logger.error(f"Error parsing nested functions: {str(e)}", exc_info=True)
        warnings.append(f"Error parsing nested functions: {str(e)}")
        return [], warnings

def parse_system_functions(system_functions_text: str) -> Tuple[List[Dict], List[str]]:
    """
    Parse system functions from text.
    
    Args:
        system_functions_text: System functions text
        
    Returns:
        Tuple containing:
            - List of system functions
            - List of warning messages (empty if no warnings)
    """
    warnings = []
    system_functions = []
    
    try:
        # Split system functions by newline
        function_lines = system_functions_text.strip().split('\n')
        
        for line in function_lines:
            line = line.strip()
            if not line:
                continue
            
            # Parse function line
            function_match = re.match(r'(\w+)\((.*?)\) -> (.*?)(?:\s+\((\w+)\))?', line)
            if function_match:
                function_name = function_match.group(1)
                parameters = function_match.group(2)
                output_to = function_match.group(3)
                function_type = function_match.group(4) if len(function_match.groups()) > 3 else 'standard'
                
                # Parse parameters
                param_list = [param.strip() for param in parameters.split(',') if param.strip()]
                
                function = {
                    "function_id": f"{function_name}_{len(system_functions) + 1}",
                    "function_name": function_name,
                    "parameters": param_list,
                    "output_to": output_to,
                    "function_type": function_type
                }
                
                system_functions.append(function)
            else:
                warnings.append(f"Could not parse system function line: {line}")
        
        return system_functions, warnings
    except Exception as e:
        logger.error(f"Error parsing system functions: {str(e)}", exc_info=True)
        warnings.append(f"Error parsing system functions: {str(e)}")
        return [], warnings

def parse_input_validations(input_validations_text: str) -> Tuple[List[Dict], List[str]]:
    """
    Parse input validations from text.
    
    Args:
        input_validations_text: Input validations text
        
    Returns:
        Tuple containing:
            - List of input validations
            - List of warning messages (empty if no warnings)
    """
    warnings = []
    input_validations = []
    
    try:
        # Split input validations by newline
        validation_lines = input_validations_text.strip().split('\n')
        
        for line in validation_lines:
            line = line.strip()
            if not line:
                continue
            
            # Parse validation line
            validation_match = re.match(r'(\w+)\.(\w+) must be (.*?)(?:\s+\((\w+)\))?(?:\s+\{(.*?)\})?', line)
            if validation_match:
                entity = validation_match.group(1)
                attribute = validation_match.group(2)
                rule = validation_match.group(3)
                rule_type = validation_match.group(4) if len(validation_match.groups()) > 3 and validation_match.group(4) else None
                error_message = validation_match.group(5) if len(validation_match.groups()) > 4 and validation_match.group(5) else None
                
                validation = {
                    "entity": entity,
                    "attribute": attribute,
                    "rule": rule
                }
                
                if rule_type:
                    validation["rule_type"] = rule_type
                else:
                    # Infer rule type
                    if 'unique' in rule.lower():
                        validation["rule_type"] = 'unique'
                    elif any(op in rule for op in ['>', '<', '>=', '<=']):
                        validation["rule_type"] = 'range'
                    elif 'pattern' in rule.lower() or 'match' in rule.lower():
                        validation["rule_type"] = 'pattern'
                    elif 'date' in rule.lower():
                        validation["rule_type"] = 'date'
                    elif 'in' in rule.lower():
                        validation["rule_type"] = 'enum'
                    else:
                        validation["rule_type"] = 'custom'
                
                if error_message:
                    validation["error_message"] = error_message
                
                input_validations.append(validation)
            else:
                warnings.append(f"Could not parse input validation line: {line}")
        
        return input_validations, warnings
    except Exception as e:
        logger.error(f"Error parsing input validations: {str(e)}", exc_info=True)
        warnings.append(f"Error parsing input validations: {str(e)}")
        return [], warnings
