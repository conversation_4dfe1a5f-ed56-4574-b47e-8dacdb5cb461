import unittest
from unittest.mock import patch
import datetime
from typing import Union
import sys
import os

# Add project root to path if needed
project_root = '/home/<USER>/workflow-system/runtime/workflow-engine'
if project_root not in sys.path:
    sys.path.insert(0, project_root)

# Import the function to test
from app.services.system_functions import date_functions

class TestDateFunctions(unittest.TestCase):
    def test_format_operation(self):
        """Test date formatting operation."""
        date_value = "2023-01-15"
        
        # Test default format
        result = date_functions(date_value, "format")
        self.assertEqual(result, "2023-01-15")
        
        # Test custom format
        result = date_functions(date_value, "format", format_str="%d/%m/%Y")
        self.assertEqual(result, "15/01/2023")
        
        # Test format with month name
        result = date_functions(date_value, "format", format_str="%B %d, %Y")
        self.assertEqual(result, "January 15, 2023")
    
    def test_add_days(self):
        """Test adding days to a date."""
        date_value = "2023-01-15"
        
        # Add 5 days
        result = date_functions(date_value, "add", "days", 5)
        self.assertEqual(result, "2023-01-20")
        
        # Add 20 days (crossing month boundary)
        result = date_functions(date_value, "add", "days", 20)
        self.assertEqual(result, "2023-02-04")
        
        # Add days crossing year boundary
        result = date_functions("2023-12-25", "add", "days", 10)
        self.assertEqual(result, "2024-01-04")
    
    def test_add_months(self):
        """Test adding months to a date."""
        date_value = "2023-01-15"
        
        # Add 3 months
        result = date_functions(date_value, "add", "months", 3)
        self.assertEqual(result, "2023-04-15")
        
        # Add 12 months (one year)
        result = date_functions(date_value, "add", "months", 12)
        self.assertEqual(result, "2024-01-15")
        
        # Add months crossing multiple years
        result = date_functions(date_value, "add", "months", 25)
        self.assertEqual(result, "2025-02-15")
    
    def test_add_months_edge_cases(self):
        """Test adding months with edge cases like month end dates."""
        # January 31 + 1 month = February 28/29
        result = date_functions("2023-01-31", "add", "months", 1)
        self.assertEqual(result, "2023-02-28")  # Not leap year
        
        result = date_functions("2024-01-31", "add", "months", 1)
        self.assertEqual(result, "2024-02-29")  # Leap year
        
        # Test other month transitions with different days
        result = date_functions("2023-01-30", "add", "months", 1)
        self.assertEqual(result, "2023-02-28")
        
        result = date_functions("2023-03-31", "add", "months", 1)
        self.assertEqual(result, "2023-04-30")
    
    def test_add_years(self):
        """Test adding years to a date."""
        date_value = "2023-01-15"
        
        # Add 5 years
        result = date_functions(date_value, "add", "years", 5)
        self.assertEqual(result, "2028-01-15")
        
        # Add negative years (subtract)
        result = date_functions(date_value, "add", "years", -3)
        self.assertEqual(result, "2020-01-15")
    
    def test_add_years_leap_year(self):
        """Test adding years with leap year considerations."""
        # February 29 on leap year
        result = date_functions("2020-02-29", "add", "years", 1)
        self.assertEqual(result, "2021-02-28")  # Next year is not leap
        
        result = date_functions("2020-02-29", "add", "years", 4)
        self.assertEqual(result, "2024-02-29")  # 4 years later is leap again
    
    def test_subtract_days(self):
        """Test subtracting days from a date."""
        date_value = "2023-01-15"
        
        # Subtract 5 days
        result = date_functions(date_value, "subtract", "days", 5)
        self.assertEqual(result, "2023-01-10")
        
        # Subtract 20 days (crossing month boundary)
        result = date_functions(date_value, "subtract", "days", 20)
        self.assertEqual(result, "2022-12-26")
    
    def test_subtract_months(self):
        """Test subtracting months from a date."""
        date_value = "2023-01-15"
        
        # Subtract 3 months
        result = date_functions(date_value, "subtract", "months", 3)
        self.assertEqual(result, "2022-10-15")
        
        # Handle month end dates when subtracting
        result = date_functions("2023-03-31", "subtract", "months", 1)
        self.assertEqual(result, "2023-02-28")  # Not leap year
    
    def test_subtract_years(self):
        """Test subtracting years from a date."""
        date_value = "2023-01-15"
        
        # Subtract 5 years
        result = date_functions(date_value, "subtract", "years", 5)
        self.assertEqual(result, "2018-01-15")
        
        # February 29 handling
        result = date_functions("2020-02-29", "subtract", "years", 1)
        self.assertEqual(result, "2019-02-28")
    
    def test_diff_days(self):
        """Test calculating day difference between dates."""
        date_value = "2023-01-15"
        other_date = "2023-01-10"
        
        # Positive difference
        result = date_functions(date_value, "diff", "days", other_date)
        self.assertEqual(result, 5)
        
        # Negative difference (reversed dates)
        result = date_functions(other_date, "diff", "days", date_value)
        self.assertEqual(result, -5)
        
        # Same date
        result = date_functions(date_value, "diff", "days", date_value)
        self.assertEqual(result, 0)
    
    def test_diff_months(self):
        """Test calculating month difference between dates."""
        date_value = "2023-01-15"
        
        # Exactly 3 months difference
        result = date_functions(date_value, "diff", "months", "2022-10-15")
        self.assertEqual(result, 3)
        
        # Different days shouldn't affect month diff
        result = date_functions(date_value, "diff", "months", "2022-10-01")
        self.assertEqual(result, 3)
        
        # Crossing multiple years
        result = date_functions("2025-03-15", "diff", "months", "2023-01-15")
        self.assertEqual(result, 26)
    
    def test_diff_years(self):
        """Test calculating year difference between dates."""
        date_value = "2023-01-15"
        
        # Exactly 5 years difference
        result = date_functions(date_value, "diff", "years", "2018-01-15")
        self.assertEqual(result, 5)
        
        # Different months shouldn't affect year diff
        result = date_functions(date_value, "diff", "years", "2018-06-30")
        self.assertEqual(result, 5)
    
    def test_extract_parts(self):
        """Test extracting parts of a date."""
        date_value = "2023-01-15"
        
        # Extract day
        result = date_functions(date_value, "extract", "day")
        self.assertEqual(result, 15)
        
        # Extract month
        result = date_functions(date_value, "extract", "month")
        self.assertEqual(result, 1)
        
        # Extract year
        result = date_functions(date_value, "extract", "year")
        self.assertEqual(result, 2023)
        
        # Extract weekday (January 15, 2023 was a Sunday = 6)
        result = date_functions(date_value, "extract", "weekday")
        self.assertEqual(result, 6)
        
        # Extract week number
        result = date_functions(date_value, "extract", "week")
        self.assertEqual(result, 2)  # Second week of 2023
    
    def test_case_insensitive_operation(self):
        """Test that operation parameter is case-insensitive."""
        date_value = "2023-01-15"
        
        result_lower = date_functions(date_value, "format", format_str="%Y-%m-%d")
        result_upper = date_functions(date_value, "FORMAT", format_str="%Y-%m-%d")
        
        self.assertEqual(result_lower, result_upper)
    
    def test_case_insensitive_unit(self):
        """Test that unit parameter is case-insensitive."""
        date_value = "2023-01-15"
        
        result_lower = date_functions(date_value, "add", "days", 5)
        result_upper = date_functions(date_value, "add", "DAYS", 5)
        
        self.assertEqual(result_lower, result_upper)
    
    def test_datetime_object_input(self):
        """Test with datetime object input instead of string."""
        date_obj = datetime.datetime(2023, 1, 15)
        
        result = date_functions(date_obj, "format", format_str="%Y-%m-%d")
        self.assertEqual(result, "2023-01-15")
    
    def test_invalid_date_format(self):
        """Test with invalid date format."""
        invalid_date = "2023/01/15"  # Not in YYYY-MM-DD format
        
        with self.assertRaises(ValueError) as context:
            date_functions(invalid_date, "format")
        
        self.assertTrue("Invalid date format" in str(context.exception))
    
    def test_invalid_date_value_type(self):
        """Test with invalid date value type."""
        invalid_date = 20230115  # Not a string or datetime
        
        with self.assertRaises(ValueError) as context:
            date_functions(invalid_date, "format")
        
        self.assertTrue("date_value must be a string or datetime object" in str(context.exception))
    
    def test_unsupported_operation(self):
        """Test with unsupported operation."""
        date_value = "2023-01-15"
        
        with self.assertRaises(ValueError) as context:
            date_functions(date_value, "invalid_operation")
        
        self.assertTrue("Unsupported date operation" in str(context.exception))
    
    def test_unsupported_unit(self):
        """Test with unsupported time unit."""
        date_value = "2023-01-15"
        
        with self.assertRaises(ValueError) as context:
            date_functions(date_value, "add", "hours", 5)
        
        self.assertTrue("Unsupported time unit" in str(context.exception))
    
    def test_unsupported_extract_unit(self):
        """Test with unsupported extract unit."""
        date_value = "2023-01-15"
        
        with self.assertRaises(ValueError) as context:
            date_functions(date_value, "extract", "hour")
        
        self.assertTrue("Unsupported extract unit" in str(context.exception))
    
    def test_diff_invalid_amount_type(self):
        """Test diff operation with invalid amount type."""
        date_value = "2023-01-15"
        
        with self.assertRaises(ValueError) as context:
            date_functions(date_value, "diff", "days", 5)  # Amount should be a date string
        
        self.assertTrue("amount should be a date string" in str(context.exception))
    
    def test_diff_invalid_date_format(self):
        """Test diff operation with invalid date format in amount."""
        date_value = "2023-01-15"
        invalid_date = "2023/01/10"  # Not in YYYY-MM-DD format
        
        with self.assertRaises(ValueError) as context:
            date_functions(date_value, "diff", "days", invalid_date)
        
        self.assertTrue("Invalid date format" in str(context.exception))

if __name__ == '__main__':
    unittest.main()