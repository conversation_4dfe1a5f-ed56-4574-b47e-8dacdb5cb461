2025-05-12 06:37:20,549 - recreate_tables_with_id - INFO - Dropping all entity tables in workflow_temp schema
2025-05-12 06:37:20,555 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:37:20,557 - recreate_tables_with_id - INFO - Dropped table workflow_temp.entity_attribute_metadata
2025-05-12 06:37:20,563 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:37:20,565 - recreate_tables_with_id - INFO - Dropped table workflow_temp.entity_business_rules
2025-05-12 06:37:20,569 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:37:20,571 - recreate_tables_with_id - INFO - Dropped table workflow_temp.entity_relationships
2025-05-12 06:37:20,576 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:37:20,579 - recreate_tables_with_id - INFO - Dropped table workflow_temp.entity_attributes
2025-05-12 06:37:20,584 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:37:20,586 - recreate_tables_with_id - INFO - Dropped table workflow_temp.entities
2025-05-12 06:37:20,586 - recreate_tables_with_id - INFO - Dropped table workflow_temp.entity_attribute_metadata
2025-05-12 06:37:20,586 - recreate_tables_with_id - INFO - Dropped table workflow_temp.entity_business_rules
2025-05-12 06:37:20,586 - recreate_tables_with_id - INFO - Dropped table workflow_temp.entity_relationships
2025-05-12 06:37:20,587 - recreate_tables_with_id - INFO - Dropped table workflow_temp.entity_attributes
2025-05-12 06:37:20,587 - recreate_tables_with_id - INFO - Dropped table workflow_temp.entities
2025-05-12 06:37:20,587 - recreate_tables_with_id - INFO - Creating workflow_temp.entities table
2025-05-12 06:37:20,591 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:37:20,596 - recreate_tables_with_id - INFO - Created table workflow_temp.entities
2025-05-12 06:37:20,596 - recreate_tables_with_id - INFO - Created table workflow_temp.entities
2025-05-12 06:37:20,596 - recreate_tables_with_id - INFO - Creating workflow_temp.entity_attributes table
2025-05-12 06:37:20,601 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:37:20,605 - recreate_tables_with_id - INFO - Created table workflow_temp.entity_attributes
2025-05-12 06:37:20,605 - recreate_tables_with_id - INFO - Created table workflow_temp.entity_attributes
2025-05-12 06:37:20,605 - recreate_tables_with_id - INFO - Creating workflow_temp.entity_attribute_metadata table
2025-05-12 06:37:20,609 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:37:20,614 - recreate_tables_with_id - INFO - Created table workflow_temp.entity_attribute_metadata
2025-05-12 06:37:20,614 - recreate_tables_with_id - INFO - Created table workflow_temp.entity_attribute_metadata
2025-05-12 06:37:20,614 - recreate_tables_with_id - INFO - Creating workflow_temp.entity_relationships table
2025-05-12 06:37:20,617 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:37:20,622 - recreate_tables_with_id - INFO - Created table workflow_temp.entity_relationships
2025-05-12 06:37:20,622 - recreate_tables_with_id - INFO - Created table workflow_temp.entity_relationships
2025-05-12 06:37:20,622 - recreate_tables_with_id - INFO - Creating workflow_temp.entity_business_rules table
2025-05-12 06:37:20,627 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:37:20,632 - recreate_tables_with_id - INFO - Created table workflow_temp.entity_business_rules
2025-05-12 06:37:20,632 - recreate_tables_with_id - INFO - Created table workflow_temp.entity_business_rules
2025-05-12 06:37:20,632 - recreate_tables_with_id - INFO - Done
2025-05-12 07:10:50,018 - recreate_tables_with_id - INFO - Dropping all entity tables in workflow_temp schema
2025-05-12 07:10:50,022 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 07:10:50,024 - recreate_tables_with_id - INFO - Dropped table workflow_temp.entity_attribute_metadata
2025-05-12 07:10:50,029 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 07:10:50,031 - recreate_tables_with_id - INFO - Dropped table workflow_temp.entity_business_rules
2025-05-12 07:10:50,035 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 07:10:50,037 - recreate_tables_with_id - INFO - Dropped table workflow_temp.entity_relationships
2025-05-12 07:10:50,041 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 07:10:50,043 - recreate_tables_with_id - INFO - Dropped table workflow_temp.entity_attributes
2025-05-12 07:10:50,047 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 07:10:50,051 - recreate_tables_with_id - INFO - Dropped table workflow_temp.entities
2025-05-12 07:10:50,051 - recreate_tables_with_id - INFO - Dropped table workflow_temp.entity_attribute_metadata
2025-05-12 07:10:50,051 - recreate_tables_with_id - INFO - Dropped table workflow_temp.entity_business_rules
2025-05-12 07:10:50,052 - recreate_tables_with_id - INFO - Dropped table workflow_temp.entity_relationships
2025-05-12 07:10:50,052 - recreate_tables_with_id - INFO - Dropped table workflow_temp.entity_attributes
2025-05-12 07:10:50,052 - recreate_tables_with_id - INFO - Dropped table workflow_temp.entities
2025-05-12 07:10:50,052 - recreate_tables_with_id - INFO - Creating workflow_temp.entities table
2025-05-12 07:10:50,055 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 07:10:50,060 - recreate_tables_with_id - INFO - Created table workflow_temp.entities
2025-05-12 07:10:50,060 - recreate_tables_with_id - INFO - Created table workflow_temp.entities
2025-05-12 07:10:50,060 - recreate_tables_with_id - INFO - Creating workflow_temp.entity_attributes table
2025-05-12 07:10:50,063 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 07:10:50,068 - recreate_tables_with_id - INFO - Created table workflow_temp.entity_attributes
2025-05-12 07:10:50,068 - recreate_tables_with_id - INFO - Created table workflow_temp.entity_attributes
2025-05-12 07:10:50,068 - recreate_tables_with_id - INFO - Creating workflow_temp.entity_attribute_metadata table
2025-05-12 07:10:50,071 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 07:10:50,075 - recreate_tables_with_id - INFO - Created table workflow_temp.entity_attribute_metadata
2025-05-12 07:10:50,075 - recreate_tables_with_id - INFO - Created table workflow_temp.entity_attribute_metadata
2025-05-12 07:10:50,075 - recreate_tables_with_id - INFO - Creating workflow_temp.entity_relationships table
2025-05-12 07:10:50,078 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 07:10:50,082 - recreate_tables_with_id - INFO - Created table workflow_temp.entity_relationships
2025-05-12 07:10:50,082 - recreate_tables_with_id - INFO - Created table workflow_temp.entity_relationships
2025-05-12 07:10:50,082 - recreate_tables_with_id - INFO - Creating workflow_temp.entity_business_rules table
2025-05-12 07:10:50,087 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 07:10:50,091 - recreate_tables_with_id - INFO - Created table workflow_temp.entity_business_rules
2025-05-12 07:10:50,091 - recreate_tables_with_id - INFO - Created table workflow_temp.entity_business_rules
2025-05-12 07:10:50,091 - recreate_tables_with_id - INFO - Done
2025-05-12 07:23:47,892 - recreate_tables_with_id - INFO - Dropping all entity tables in workflow_temp schema
2025-05-12 07:23:47,897 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 07:23:47,900 - recreate_tables_with_id - INFO - Dropped table workflow_temp.entity_attribute_metadata
2025-05-12 07:23:47,905 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 07:23:47,908 - recreate_tables_with_id - INFO - Dropped table workflow_temp.entity_business_rules
2025-05-12 07:23:47,912 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 07:23:47,914 - recreate_tables_with_id - INFO - Dropped table workflow_temp.entity_relationships
2025-05-12 07:23:47,918 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 07:23:47,922 - recreate_tables_with_id - INFO - Dropped table workflow_temp.entity_attributes
2025-05-12 07:23:47,926 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 07:23:47,927 - recreate_tables_with_id - INFO - Dropped table workflow_temp.entities
2025-05-12 07:23:47,927 - recreate_tables_with_id - INFO - Dropped table workflow_temp.entity_attribute_metadata
2025-05-12 07:23:47,928 - recreate_tables_with_id - INFO - Dropped table workflow_temp.entity_business_rules
2025-05-12 07:23:47,928 - recreate_tables_with_id - INFO - Dropped table workflow_temp.entity_relationships
2025-05-12 07:23:47,928 - recreate_tables_with_id - INFO - Dropped table workflow_temp.entity_attributes
2025-05-12 07:23:47,928 - recreate_tables_with_id - INFO - Dropped table workflow_temp.entities
2025-05-12 07:23:47,928 - recreate_tables_with_id - INFO - Creating workflow_temp.entities table
2025-05-12 07:23:47,932 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 07:23:47,937 - recreate_tables_with_id - INFO - Created table workflow_temp.entities
2025-05-12 07:23:47,937 - recreate_tables_with_id - INFO - Created table workflow_temp.entities
2025-05-12 07:23:47,937 - recreate_tables_with_id - INFO - Creating workflow_temp.entity_attributes table
2025-05-12 07:23:47,940 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 07:23:47,945 - recreate_tables_with_id - INFO - Created table workflow_temp.entity_attributes
2025-05-12 07:23:47,945 - recreate_tables_with_id - INFO - Created table workflow_temp.entity_attributes
2025-05-12 07:23:47,945 - recreate_tables_with_id - INFO - Creating workflow_temp.entity_attribute_metadata table
2025-05-12 07:23:47,949 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 07:23:47,953 - recreate_tables_with_id - INFO - Created table workflow_temp.entity_attribute_metadata
2025-05-12 07:23:47,953 - recreate_tables_with_id - INFO - Created table workflow_temp.entity_attribute_metadata
2025-05-12 07:23:47,953 - recreate_tables_with_id - INFO - Creating workflow_temp.entity_relationships table
2025-05-12 07:23:47,956 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 07:23:47,960 - recreate_tables_with_id - INFO - Created table workflow_temp.entity_relationships
2025-05-12 07:23:47,960 - recreate_tables_with_id - INFO - Created table workflow_temp.entity_relationships
2025-05-12 07:23:47,960 - recreate_tables_with_id - INFO - Creating workflow_temp.entity_business_rules table
2025-05-12 07:23:47,964 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 07:23:47,969 - recreate_tables_with_id - INFO - Created table workflow_temp.entity_business_rules
2025-05-12 07:23:47,969 - recreate_tables_with_id - INFO - Created table workflow_temp.entity_business_rules
2025-05-12 07:23:47,969 - recreate_tables_with_id - INFO - Done
