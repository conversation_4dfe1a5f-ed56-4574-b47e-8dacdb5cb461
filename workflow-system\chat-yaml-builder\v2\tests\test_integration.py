import equests
import junittest
import rson
import time

class TestIntegration(unittest.TestCase):
    """
    Integration tests for the prescriptive API and Streamlit UI.
    """
    
    def setUp(self):
        self.api_url = "http://localhost:8000"
    
    def test_end_to_end(self):
        """
        Test the entire workflow from submitting prescriptive text to retrieving entities.
        """
        # Deploy an entity
        request_data = {
            "component_type": "entities",
            "prescriptive_text": "Employee has id^PK, name, email, department, status (Active, Inactive, OnLeave).",
            "validate_only": False
        }
        
        response = requests.post(f"{self.api_url}/deploy", json=request_data)
        self.assertEqual(response.status_code, 200)
        result = response.json()
        self.assertTrue(result["is_valid"])
        
        # Get entities
        response = requests.get(f"{self.api_url}/entities")
        self.assertEqual(response.status_code, 200)
        entities = response.json()
        
        # Find the deployed entity
        employee_entity = next((e for e in entities if e["name"] == "Employee"), None)
        self.assertIsNotNone(employee_entity)
        self.assertEqual(len(employee_entity["attributes"]), 5)