
# Add to insert_generator.py

def generate_user_inserts(yaml_data):
    '''Generate SQL inserts for User entities.'''
    inserts = []
    
    # Extract User entities
    entities = yaml_data.get("entities", [])
    user_entities = [e for e in entities if e.get("name") == "User"]
    
    for user in user_entities:
        # Generate SQL for User entity
        user_id = user.get("id")
        attributes = user.get("attributes", [])
        
        # Find required attributes
        user_id_attr = next((a for a in attributes if a.get("name") == "user_id"), None)
        username_attr = next((a for a in attributes if a.get("name") == "username"), None)
        email_attr = next((a for a in attributes if a.get("name") == "email"), None)
        status_attr = next((a for a in attributes if a.get("name") == "status"), None)
        
        if user_id_attr and username_attr and email_attr and status_attr:
            sql = f'''
            INSERT INTO users (user_id, username, email, status)
            VALUES ('{user_id_attr.get("default_value", "")}', 
                    '{username_attr.get("default_value", "")}', 
                    '{email_attr.get("default_value", "")}', 
                    '{status_attr.get("default_value", "active")}');
            '''
            inserts.append(sql)
        
        # Generate SQL for entity table
        sql = f'''
        INSERT INTO entities (id, name, type, version, status, entity_class)
        VALUES ('{user_id}', '{user.get("name")}', '{user.get("type")}', 
                '{user.get("version")}', '{user.get("status")}', 'organizational');
        '''
        inserts.append(sql)
    
    return inserts

def generate_role_inserts(yaml_data):
    '''Generate SQL inserts for Role entities.'''
    inserts = []
    
    # Extract Role entities
    entities = yaml_data.get("entities", [])
    role_entities = [e for e in entities if e.get("name") == "Role"]
    
    for role in role_entities:
        # Generate SQL for Role entity
        role_id = role.get("id")
        attributes = role.get("attributes", [])
        
        # Find required attributes
        role_id_attr = next((a for a in attributes if a.get("name") == "role_id"), None)
        name_attr = next((a for a in attributes if a.get("name") == "name"), None)
        
        if role_id_attr and name_attr:
            sql = f'''
            INSERT INTO roles (role_id, name, description)
            VALUES ('{role_id_attr.get("default_value", "")}', 
                    '{name_attr.get("default_value", "")}', 
                    '');
            '''
            inserts.append(sql)
        
        # Generate SQL for entity table
        sql = f'''
        INSERT INTO entities (id, name, type, version, status, entity_class)
        VALUES ('{role_id}', '{role.get("name")}', '{role.get("type")}', 
                '{role.get("version")}', '{role.get("status")}', 'organizational');
        '''
        inserts.append(sql)
    
    return inserts

def generate_user_role_relationship_inserts(yaml_data):
    '''Generate SQL inserts for User-Role relationships.'''
    inserts = []
    
    # Extract tenant roles
    tenant_roles = []
    if "tenant" in yaml_data and "roles" in yaml_data["tenant"]:
        tenant_roles = yaml_data["tenant"]["roles"]
    
    # Extract agent permissions
    agent_permissions = []
    local_objectives = yaml_data.get("local_objectives", [])
    for lo in local_objectives:
        if "agent_stack" not in lo or "agents" not in lo["agent_stack"]:
            continue
        
        for agent in lo["agent_stack"]["agents"]:
            role = agent.get("role", "")
            users = agent.get("users", [])
            
            # Process user-specific permissions
            for user in users:
                if isinstance(user, str):
                    # Simple email format
                    agent_permissions.append({
                        "role": role,
                        "user": user
                    })
                elif isinstance(user, dict) and "email" in user:
                    # Extended format with user-specific rights
                    agent_permissions.append({
                        "role": role,
                        "user": user["email"]
                    })
    
    # Generate relationships
    for role in tenant_roles:
        role_id = role.get("id")
        
        for perm in agent_permissions:
            if perm.get("role") == role_id and "user" in perm:
                user_email = perm["user"]
                
                # SQL to find user_id by email
                sql = f'''
                -- Find user_id for {user_email}
                SET @user_id = (SELECT user_id FROM users WHERE email = '{user_email}');
                
                -- Insert into user_roles table
                INSERT INTO user_roles (user_id, role_id)
                VALUES (@user_id, '{role_id}');
                
                -- Insert into entity_relationships table
                INSERT INTO entity_relationships (source_entity, source_id, target_entity, target_id, relationship_type)
                VALUES ('User', @user_id, 'Role', '{role_id}', 'has_role');
                '''
                inserts.append(sql)
    
    return inserts
