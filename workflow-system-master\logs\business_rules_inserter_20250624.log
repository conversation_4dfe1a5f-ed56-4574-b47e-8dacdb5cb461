{"timestamp": "2025-06-24T04:45:02.606307", "operation": "deploy_single_business_rule_to_workflow_temp", "input_data": {"business_rules_id": "BR001"}, "result": {"success": false, "error": "Business rule BR001 not found with status draft", "business_rules_id": "BR001", "required_status": "draft"}, "status": "error"}
{"timestamp": "2025-06-24T04:45:02.745977", "operation": "process_mongo_business_rules_to_workflow_temp", "input_data": {"schema": "workflow_temp"}, "result": {"total_processed": 0, "successful_inserts": 0, "failed_inserts": 0, "details": []}, "status": "success"}
