"""
Query entity attributes from the database.
"""

from db_utils import execute_query

def main():
    # Query entity_attributes table
    print("Querying workflow_temp.entity_attributes table")
    success, messages, attributes = execute_query(
        "SELECT attribute_id, entity_id, name, type, required FROM workflow_temp.entity_attributes",
        schema_name="workflow_temp"
    )
    
    if success and attributes:
        print("Entity Attributes:")
        for attribute in attributes:
            print(f"  {attribute}")
    else:
        print("No entity attributes found or query failed")
        if messages:
            print("Messages:")
            for message in messages:
                print(f"  {message}")

if __name__ == "__main__":
    main()
