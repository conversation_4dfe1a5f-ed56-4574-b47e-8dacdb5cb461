import psycopg2
from pymongo import MongoClient
import json

# PostgreSQL Database Connection Configuration
PG_CONFIG = {
    "dbname": "workflow_solution_temp",
    "user": "postgres",
    "password": "postgres",
    "host": "localhost",
    "port": "5432"
}

# MongoDB Connection Configuration
MONGO_URI = "mongodb://localhost:27017/"
MONGO_DB = "workflow_system"
MONGO_COLLECTION = "workflow"

# ✅ Connect to MongoDB
mongo_client = MongoClient(MONGO_URI)
mongo_db = mongo_client[MONGO_DB]
mongo_collection = mongo_db[MONGO_COLLECTION]

# ✅ Connect to PostgreSQL
pg_conn = psycopg2.connect(**PG_CONFIG)
pg_conn.autocommit = True
pg_cursor = pg_conn.cursor()

# ✅ Ensure `workflow_source` is NULLABLE
try:
    pg_cursor.execute("""
        ALTER TABLE workflow_solution_temp.local_objectives
        ALTER COLUMN workflow_source DROP NOT NULL;
    """)
    print("✅ Altered `workflow_source` to allow NULL values.")
except Exception as e:
    print(f"⚠️ Could not alter column: {e}")

# ✅ Fetch MongoDB Data
workflow_data = mongo_collection.find_one()

if not workflow_data:
    print("❌ No data found in MongoDB.")
    exit()

workflow_data = workflow_data.get("workflow_data", {})

# ✅ Insert Permission Types
print("\n➡️ Inserting Permission Types...")
for permission in workflow_data.get("permission_types", []):
    try:
        pg_cursor.execute(
            "INSERT INTO workflow_solution_temp.permission_types (permission_id, description) VALUES (%s, %s) ON CONFLICT DO NOTHING",
            (permission["id"], permission["description"])
        )
    except Exception as e:
        print(f"❌ Error inserting permission types: {e}")

# ✅ Insert Tenant
tenant = workflow_data.get("tenant", {})
tenant_id = tenant.get("id", "T000")
tenant_name = tenant.get("name", "DefaultTenant")

print(f"\n➡️ Inserting Tenant: {tenant_name}...")
try:
    pg_cursor.execute(
        "INSERT INTO workflow_solution_temp.tenants (tenant_id, name) VALUES (%s, %s) ON CONFLICT DO NOTHING",
        (tenant_id, tenant_name)
    )
except Exception as e:
    print(f"❌ Error inserting tenant: {e}")

# ✅ Insert Roles
print("\n➡️ Inserting Roles...")
for role in tenant.get("roles", []):
    try:
        pg_cursor.execute(
            "INSERT INTO workflow_solution_temp.roles (role_id, name, tenant_id, inherits_from) VALUES (%s, %s, %s, %s) ON CONFLICT DO NOTHING",
            (role["id"], role["name"], tenant_id, role.get("inherits_from"))
        )
    except Exception as e:
        print(f"❌ Error inserting role {role['name']}: {e}")

# ✅ Insert Global Objectives
print("\n➡️ Inserting Global Objectives...")
global_objective_ids = set()
for go in workflow_data.get("global_objectives", []):
    try:
        pg_cursor.execute(
            "INSERT INTO workflow_solution_temp.global_objectives (go_id, name, status, description) VALUES (%s, %s, %s, %s) ON CONFLICT DO NOTHING",
            (go["id"], go["name"], go["status"], go.get("description", ""))
        )
        global_objective_ids.add(go["id"])
		
    except Exception as e:
        print(f"❌ Error inserting global objective {go['id']}: {e}")

print(f"✅ Inserted Global Objectives: {global_objective_ids}")

# ✅ Insert Local Objectives
print("\n➡️ Inserting Local Objectives...")
for lo in workflow_data.get("local_objectives", []):
    try:
        workflow_source = lo.get("workflow_source", None)  # Allow NULL
        pg_cursor.execute(
            "INSERT INTO workflow_solution_temp.local_objectives (lo_id, contextual_id, name, function_type, workflow_source, go_id) VALUES (%s, %s, %s, %s, %s, %s) ON CONFLICT DO NOTHING",
            (lo["id"], lo["contextual_id"], lo["name"], lo["function_type"], workflow_source, global_objective_ids)
        )
    except Exception as e:
        print(f"❌ Error inserting local objective {lo['id']}: {e}")

# ✅ Insert Entities
print("\n➡️ Inserting Entities...")
for entity in workflow_data.get("entities", []):
    try:
        pg_cursor.execute(
            "INSERT INTO workflow_solution_temp.entities (entity_id, name, version, status, type, attribute_prefix, description) VALUES (%s, %s, %s, %s, %s, %s, %s) ON CONFLICT DO NOTHING",
            (entity["id"], entity["name"], entity.get("version", "1.0"), entity["status"], entity["type"], entity.get("attributes_metadata", {}).get("attribute_prefix", ""), entity.get("description", ""))
        )
    except Exception as e:
        print(f"❌ Error inserting entity {entity['id']}: {e}")

# ✅ Fetch and Display Inserted Data
print("\n🔎 Verifying Data in PostgreSQL...")

print("\n📌 Tenants:")
pg_cursor.execute("SELECT * FROM workflow_solution_temp.tenants;")
print(pg_cursor.fetchall())

print("\n📌 Roles:")
pg_cursor.execute("SELECT * FROM workflow_solution_temp.roles;")
print(pg_cursor.fetchall())

print("\n📌 Global Objectives:")
pg_cursor.execute("SELECT * FROM workflow_solution_temp.global_objectives;")
print(pg_cursor.fetchall())

print("\n📌 Local Objectives:")
pg_cursor.execute("SELECT * FROM workflow_solution_temp.local_objectives;")
print(pg_cursor.fetchall())

print("\n📌 Entities:")
pg_cursor.execute("SELECT * FROM workflow_solution_temp.entities;")
print(pg_cursor.fetchall())

# ✅ Close Connections
pg_cursor.close()
pg_conn.close()
mongo_client.close()

print("\n✅ Migration Completed Successfully!")
