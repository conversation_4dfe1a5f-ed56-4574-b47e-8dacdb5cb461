#!/usr/bin/env python3
"""
Script to update entity table names from old format (e000001_department, e000002_employee, etc.)
to new format (e1_department, e2_employee, etc.).
"""

import logging
import sys
from deployers.entity_deployer_v2 import update_entity_table_names

# Set up logging
logging.basicConfig(level=logging.INFO,
                   format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger('run_update_entity_table_names')

def main():
    """
    Main function to update entity table names.
    """
    schema_name = 'workflow_temp'
    
    logger.info(f"Starting update of entity table names in schema {schema_name}")
    
    success, messages = update_entity_table_names(schema_name)
    
    for message in messages:
        logger.info(message)
    
    if success:
        logger.info(f"Successfully updated entity table names in schema {schema_name}")
    else:
        logger.error(f"Failed to update entity table names in schema {schema_name}")

if __name__ == "__main__":
    main()
