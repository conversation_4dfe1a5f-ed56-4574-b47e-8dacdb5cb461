#!/usr/bin/env python3
"""
Test script to verify that "belongs to" constraints are correctly parsed and deployed to the database.
"""

import os
import logging
from parsers.entity_parser import parse_entities
from deployers.entity_deployer_v2 import deploy_entities, execute_query

# Set up logging
logging.basicConfig(level=logging.INFO, 
                   format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
                   handlers=[
                       logging.FileHandler('/home/<USER>/workflow-system/chat-yaml-builder/v2/belongs_to_constraint.log'),
                       logging.StreamHandler()
                   ])
logger = logging.getLogger('test_belongs_to_constraint')

def test_belongs_to_constraint():
    """
    Test that "belongs to" constraints are correctly parsed and deployed to the database.
    """
    # Create a test entity definition with a "belongs to" constraint
    entity_def = """
Employee has employeeId^PK, firstName, lastName, email, phoneNumber, departmentId^FK, managerId^FK, hireDate, status(Active, Inactive, OnLeave), salary, performanceRating, probationDays, minSalary.

* Employee has many-to-one relationship with Department using Employee.departmentId to Department.departmentId^PK
* Employee has many-to-one relationship with Employee using Employee.managerId to Employee.employeeId^PK

* Employee.managerId must belong to selected Employee.departmentId

Department has departmentId^PK, name, managerId^FK, location, budget.

* Department has one-to-one relationship with Employee using Department.managerId to Employee.employeeId^PK
* Department has one-to-many relationship with Employee using Department.departmentId to Employee.departmentId^FK
"""
    
    # Parse the entity definition
    entities_data, warnings = parse_entities(entity_def)
    
    # Print any warnings
    if warnings:
        logger.warning("Warnings during parsing:")
        for warning in warnings:
            logger.warning(f"  - {warning}")
    
    # Check if Employee entity was parsed
    if 'entities' in entities_data and 'Employee' in entities_data['entities']:
        employee_entity = entities_data['entities']['Employee']
        
        # Check if constraints were parsed
        if 'constraints' in employee_entity:
            logger.info("\nEmployee constraints:")
            for constraint_name, constraint_def in employee_entity['constraints'].items():
                logger.info(f"\n  - Constraint: {constraint_name}")
                for key, value in constraint_def.items():
                    logger.info(f"    - {key}: {value}")
        else:
            logger.warning("No constraints found in Employee entity")
            return
    else:
        logger.error("Employee entity not found in parsed data")
        return
    
    # Deploy the entities to the database
    schema_name = 'workflow_temp'
    success, messages = deploy_entities(entities_data, schema_name)
    
    if not success:
        logger.error("Failed to deploy entities:")
        for message in messages:
            logger.error(f"  - {message}")
        return
    
    logger.info("Successfully deployed entities")
    
    # Verify the constraints were deployed
    # Check if the trigger function exists
    success, messages, result = execute_query(
        f"""
        SELECT EXISTS (
            SELECT FROM pg_proc
            WHERE proname = 'e000002_employee_manager_department_check'
            AND pronamespace = (SELECT oid FROM pg_namespace WHERE nspname = '{schema_name}')
        )
        """,
        schema_name=schema_name
    )
    
    if success and result and result[0][0]:
        logger.info("Trigger function 'e000002_employee_manager_department_check' exists")
    else:
        logger.warning("Trigger function 'e000002_employee_manager_department_check' does not exist")
    
    # Check if the trigger exists
    success, messages, result = execute_query(
        f"""
        SELECT EXISTS (
            SELECT FROM pg_trigger
            WHERE tgname = 'e000002_employee_manager_department_trigger'
            AND tgrelid = (
                SELECT oid FROM pg_class 
                WHERE relname = 'e000002_employee' 
                AND relnamespace = (SELECT oid FROM pg_namespace WHERE nspname = '{schema_name}')
            )
        )
        """,
        schema_name=schema_name
    )
    
    if success and result and result[0][0]:
        logger.info("Trigger 'e000002_employee_manager_department_trigger' exists")
    else:
        logger.warning("Trigger 'e000002_employee_manager_department_trigger' does not exist")
    
    # Test the constraint by inserting data
    # First, create a department
    success, messages, _ = execute_query(
        f"""
        INSERT INTO {schema_name}.e000001_department (departmentid, name, location, budget)
        VALUES ('DEPT001', 'Engineering', 'Building A', 1000000)
        ON CONFLICT (departmentid) DO UPDATE
        SET name = EXCLUDED.name, location = EXCLUDED.location, budget = EXCLUDED.budget
        """,
        schema_name=schema_name
    )
    
    if not success:
        logger.warning(f"Failed to insert department: {messages}")
    else:
        logger.info("Inserted department 'Engineering' with ID 'DEPT001'")
    
    # Create a manager in the Engineering department
    success, messages, _ = execute_query(
        f"""
        INSERT INTO {schema_name}.e000002_employee (employeeid, firstname, lastname, email, departmentid, status, salary)
        VALUES ('EMP001', 'John', 'Doe', '<EMAIL>', 'DEPT001', 'Active', 100000)
        ON CONFLICT (employeeid) DO UPDATE
        SET firstname = EXCLUDED.firstname, lastname = EXCLUDED.lastname, email = EXCLUDED.email,
            departmentid = EXCLUDED.departmentid, status = EXCLUDED.status, salary = EXCLUDED.salary
        """,
        schema_name=schema_name
    )
    
    if not success:
        logger.warning(f"Failed to insert manager: {messages}")
    else:
        logger.info("Inserted manager 'John Doe' with ID 'EMP001' in department 'DEPT001'")
    
    # Create another department
    success, messages, _ = execute_query(
        f"""
        INSERT INTO {schema_name}.e000001_department (departmentid, name, location, budget)
        VALUES ('DEPT002', 'Marketing', 'Building B', 500000)
        ON CONFLICT (departmentid) DO UPDATE
        SET name = EXCLUDED.name, location = EXCLUDED.location, budget = EXCLUDED.budget
        """,
        schema_name=schema_name
    )
    
    if not success:
        logger.warning(f"Failed to insert department: {messages}")
    else:
        logger.info("Inserted department 'Marketing' with ID 'DEPT002'")
    
    # Try to create an employee in the Engineering department with a manager from a different department
    # This should fail due to the constraint
    success, messages, _ = execute_query(
        f"""
        INSERT INTO {schema_name}.e000002_employee (employeeid, firstname, lastname, email, departmentid, managerid, status, salary)
        VALUES ('EMP002', 'Jane', 'Smith', '<EMAIL>', 'DEPT002', 'EMP001', 'Active', 80000)
        """,
        schema_name=schema_name
    )
    
    if not success:
        logger.info("Constraint test passed: Failed to insert employee with manager from a different department")
        logger.info(f"Error message: {messages}")
    else:
        logger.warning("Constraint test failed: Successfully inserted employee with manager from a different department")
    
    # Create a manager in the Marketing department
    success, messages, _ = execute_query(
        f"""
        INSERT INTO {schema_name}.e000002_employee (employeeid, firstname, lastname, email, departmentid, status, salary)
        VALUES ('EMP003', 'Bob', 'Johnson', '<EMAIL>', 'DEPT002', 'Active', 90000)
        ON CONFLICT (employeeid) DO UPDATE
        SET firstname = EXCLUDED.firstname, lastname = EXCLUDED.lastname, email = EXCLUDED.email,
            departmentid = EXCLUDED.departmentid, status = EXCLUDED.status, salary = EXCLUDED.salary
        """,
        schema_name=schema_name
    )
    
    if not success:
        logger.warning(f"Failed to insert manager: {messages}")
    else:
        logger.info("Inserted manager 'Bob Johnson' with ID 'EMP003' in department 'DEPT002'")
    
    # Try to create an employee in the Marketing department with a manager from the same department
    # This should succeed
    success, messages, _ = execute_query(
        f"""
        INSERT INTO {schema_name}.e000002_employee (employeeid, firstname, lastname, email, departmentid, managerid, status, salary)
        VALUES ('EMP004', 'Alice', 'Williams', '<EMAIL>', 'DEPT002', 'EMP003', 'Active', 75000)
        """,
        schema_name=schema_name
    )
    
    if success:
        logger.info("Constraint test passed: Successfully inserted employee with manager from the same department")
    else:
        logger.warning("Constraint test failed: Failed to insert employee with manager from the same department")
        logger.warning(f"Error message: {messages}")

if __name__ == "__main__":
    test_belongs_to_constraint()
