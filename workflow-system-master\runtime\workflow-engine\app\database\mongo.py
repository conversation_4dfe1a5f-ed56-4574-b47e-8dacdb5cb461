from pymongo import MongoClient
import logging
from app.core.config import settings

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class MongoDBConnection:
    def __init__(self, uri: str = settings.MONGODB_URI):
        """
        Initialize MongoDB connection
        
        Args:
            uri (str): MongoDB connection URI
        """
        self.client = None
        self.uri = uri
        self.logger = logging.getLogger(self.__class__.__name__)

    def connect(self):
        """
        Establish a connection to MongoDB
        
        Returns:
            MongoClient: Connected MongoDB client
        """
        if not self.client:
            try:
                # Create client with the provided URI
                self.client = MongoClient(
                    self.uri,
                    serverSelectionTimeoutMS=5000,  # 5 second timeout for server selection
                    connectTimeoutMS=5000  # 5 second timeout for connection
                )

                # Verify the connection
                self.client.admin.command('ismaster')
                self.logger.info("Successfully connected to MongoDB")
            except Exception as e:
                self.logger.error(f"Error connecting to MongoDB: {e}")
                raise
        return self.client

    def get_database(self, db_name: str = 'workflow_system'):
        """
        Get a specific database
        
        Args:
            db_name (str): Name of the database to retrieve
        
        Returns:
            Database: MongoDB database instance
        """
        return self.connect()[db_name]

    def close(self):
        """
        Close the MongoDB connection
        """
        if self.client:
            self.client.close()
            self.client = None
            self.logger.info("MongoDB connection closed")

# Create a singleton MongoDB connection
mongodb_conn = MongoDBConnection()
