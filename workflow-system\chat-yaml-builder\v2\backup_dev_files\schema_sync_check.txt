Found 76 tables in workflow_runtime
Found 75 tables in workflow_temp
Missing tables in workflow_temp: ['lo_input_items']
All columns in workflow_runtime tables exist in workflow_temp tables

Schema Sync Summary:
====================
Tables in workflow_runtime: 76
Tables in workflow_temp: 75

Missing tables in workflow_temp: ['lo_input_items']

All columns in workflow_runtime tables exist in workflow_temp tables

Detailed Column Comparison for Tables of Interest:
==================================================

Table: lo_input_stack
Column       Runtime Type                 Temp Type                    Match
-----------  ---------------------------  ---------------------------  -------
created_at   timestamp without time zone  timestamp without time zone  ✓
description  text                         text                         ✓
id           integer                      integer                      ✓
lo_id        character varying            character varying            ✓
updated_at   timestamp without time zone  timestamp without time zone  ✓

Table lo_input_items does not exist in workflow_temp

Table: lo_output_stack
Column       Runtime Type                 Temp Type                    Match
-----------  ---------------------------  ---------------------------  -------
created_at   timestamp without time zone  timestamp without time zone  ✓
description  text                         text                         ✓
id           integer                      integer                      ✓
lo_id        character varying            character varying            ✓
updated_at   timestamp without time zone  timestamp without time zone  ✓

Table: lo_output_items
Column           Runtime Type                 Temp Type                    Match
---------------  ---------------------------  ---------------------------  -------
contextual_id    character varying            character varying            ✓
created_at       timestamp without time zone  timestamp without time zone  ✓
id               character varying            character varying            ✓
item_id          character varying            character varying            ✓
lo_id            character varying            character varying            ✓
name             character varying            character varying            ✓
output_stack_id  integer                      integer                      ✓
slot_id          character varying            character varying            ✓
source           character varying            character varying            ✓
type             character varying            character varying            ✓
updated_at       timestamp without time zone  timestamp without time zone  ✓
