"""
Fix Entity Deployer for Chat YAML Builder v2

This script fixes the entity deployer by:
1. Replacing entity_deployer.py with entity_deployer_v2.py
2. Running the implement_missing_features.py script to fix existing data
3. Verifying that all issues have been fixed

Usage:
    python fix_entity_deployer.py
"""

import os
import sys
import logging
import shutil
import subprocess
from typing import Dict, List, Tuple, Any, Optional

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('fix_entity_deployer.log')
    ]
)
logger = logging.getLogger('fix_entity_deployer')

# Add parent directory to path to import common modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from db_utils import execute_query

def backup_entity_deployer() -> Tuple[bool, List[str]]:
    """
    Backup the current entity_deployer.py file.
    
    Returns:
        <PERSON>ple containing:
            - <PERSON><PERSON>an indicating if the backup was successful
            - List of messages (warnings, errors, or success messages)
    """
    messages = []
    
    try:
        logger.info("Backing up entity_deployer.py")
        
        # Get the path to the entity_deployer.py file
        entity_deployer_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "deployers", "entity_deployer.py")
        
        # Check if the file exists
        if not os.path.exists(entity_deployer_path):
            logger.warning(f"entity_deployer.py not found at {entity_deployer_path}")
            messages.append(f"Warning: entity_deployer.py not found at {entity_deployer_path}")
            return False, messages
        
        # Create a backup
        backup_path = entity_deployer_path + ".bak"
        shutil.copy2(entity_deployer_path, backup_path)
        
        logger.info(f"Backed up entity_deployer.py to {backup_path}")
        messages.append(f"Backed up entity_deployer.py to {backup_path}")
        
        return True, messages
    except Exception as e:
        error_msg = f"Error backing up entity_deployer.py: {str(e)}"
        logger.error(error_msg, exc_info=True)
        messages.append(error_msg)
        return False, messages

def replace_entity_deployer() -> Tuple[bool, List[str]]:
    """
    Replace entity_deployer.py with entity_deployer_v2.py.
    
    Returns:
        Tuple containing:
            - Boolean indicating if the replacement was successful
            - List of messages (warnings, errors, or success messages)
    """
    messages = []
    
    try:
        logger.info("Replacing entity_deployer.py with entity_deployer_v2.py")
        
        # Get the path to the entity_deployer.py and entity_deployer_v2.py files
        deployers_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "deployers")
        entity_deployer_path = os.path.join(deployers_dir, "entity_deployer.py")
        entity_deployer_v2_path = os.path.join(deployers_dir, "entity_deployer_v2.py")
        
        # Check if the files exist
        if not os.path.exists(entity_deployer_v2_path):
            logger.warning(f"entity_deployer_v2.py not found at {entity_deployer_v2_path}")
            messages.append(f"Warning: entity_deployer_v2.py not found at {entity_deployer_v2_path}")
            return False, messages
        
        # Read the content of entity_deployer_v2.py
        with open(entity_deployer_v2_path, 'r') as f:
            content = f.read()
        
        # Write the content to entity_deployer.py
        with open(entity_deployer_path, 'w') as f:
            f.write(content)
        
        logger.info(f"Replaced entity_deployer.py with entity_deployer_v2.py")
        messages.append(f"Replaced entity_deployer.py with entity_deployer_v2.py")
        
        return True, messages
    except Exception as e:
        error_msg = f"Error replacing entity_deployer.py: {str(e)}"
        logger.error(error_msg, exc_info=True)
        messages.append(error_msg)
        return False, messages

def run_implement_missing_features() -> Tuple[bool, List[str]]:
    """
    Run the implement_missing_features.py script to fix existing data.
    
    Returns:
        Tuple containing:
            - Boolean indicating if the script ran successfully
            - List of messages (warnings, errors, or success messages)
    """
    messages = []
    
    try:
        logger.info("Running implement_missing_features.py")
        
        # Get the path to the implement_missing_features.py file
        implement_missing_features_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "implement_missing_features.py")
        
        # Check if the file exists
        if not os.path.exists(implement_missing_features_path):
            logger.warning(f"implement_missing_features.py not found at {implement_missing_features_path}")
            messages.append(f"Warning: implement_missing_features.py not found at {implement_missing_features_path}")
            return False, messages
        
        # Run the script
        result = subprocess.run([sys.executable, implement_missing_features_path], capture_output=True, text=True)
        
        # Check if the script ran successfully
        if result.returncode == 0:
            logger.info("implement_missing_features.py ran successfully")
            messages.append("implement_missing_features.py ran successfully")
            
            # Log the output
            for line in result.stdout.splitlines():
                logger.info(f"  {line}")
                messages.append(f"  {line}")
            
            return True, messages
        else:
            logger.error(f"implement_missing_features.py failed with return code {result.returncode}")
            messages.append(f"Error: implement_missing_features.py failed with return code {result.returncode}")
            
            # Log the error output
            for line in result.stderr.splitlines():
                logger.error(f"  {line}")
                messages.append(f"  {line}")
            
            return False, messages
    except Exception as e:
        error_msg = f"Error running implement_missing_features.py: {str(e)}"
        logger.error(error_msg, exc_info=True)
        messages.append(error_msg)
        return False, messages

def verify_fixes() -> Tuple[bool, List[str]]:
    """
    Verify that all issues have been fixed.
    
    Returns:
        Tuple containing:
            - Boolean indicating if all issues have been fixed
            - List of messages (warnings, errors, or success messages)
    """
    messages = []
    schema_name = "workflow_temp"
    
    try:
        logger.info("Verifying fixes")
        
        # Check if entity IDs use sequential IDs (E1, E2)
        logger.info("Checking entity IDs")
        success, query_messages, entities = execute_query(
            f"SELECT entity_id FROM {schema_name}.entities",
            schema_name=schema_name
        )
        
        if not success:
            messages.extend(query_messages)
            return False, messages
        
        if not entities:
            logger.warning("No entities found")
            messages.append("Warning: No entities found")
        else:
            # Check if entity IDs follow the E1, E2 pattern
            all_sequential = True
            for i, (entity_id,) in enumerate(entities):
                expected_id = f"E{i+1}"
                if entity_id != expected_id:
                    all_sequential = False
                    logger.warning(f"Entity ID {entity_id} does not match expected ID {expected_id}")
                    messages.append(f"Warning: Entity ID {entity_id} does not match expected ID {expected_id}")
            
            if all_sequential:
                logger.info("All entity IDs use sequential IDs (E1, E2)")
                messages.append("All entity IDs use sequential IDs (E1, E2)")
            else:
                logger.warning("Not all entity IDs use sequential IDs (E1, E2)")
                messages.append("Warning: Not all entity IDs use sequential IDs (E1, E2)")
        
        # Check if enum values are stored in the attribute_enum_values table
        logger.info("Checking enum values")
        success, query_messages, enum_attributes = execute_query(
            f"""
            SELECT a.attribute_id, a.name, a.type
            FROM {schema_name}.entity_attributes a
            WHERE a.type = 'enum'
            """,
            schema_name=schema_name
        )
        
        if not success:
            messages.extend(query_messages)
            return False, messages
        
        if not enum_attributes:
            logger.info("No enum attributes found")
            messages.append("No enum attributes found")
        else:
            # Check if enum values are stored for each enum attribute
            for attr_id, attr_name, _ in enum_attributes:
                success, query_messages, enum_values = execute_query(
                    f"""
                    SELECT value
                    FROM {schema_name}.attribute_enum_values
                    WHERE attribute_id = %s
                    """,
                    (attr_id,),
                    schema_name
                )
                
                if not success:
                    messages.extend(query_messages)
                    return False, messages
                
                if not enum_values:
                    logger.warning(f"No enum values found for attribute {attr_id} ({attr_name})")
                    messages.append(f"Warning: No enum values found for attribute {attr_id} ({attr_name})")
                else:
                    logger.info(f"Found {len(enum_values)} enum values for attribute {attr_id} ({attr_name})")
                    messages.append(f"Found {len(enum_values)} enum values for attribute {attr_id} ({attr_name})")
        
        # Check if validations are stored in the attribute_validations table
        logger.info("Checking validations")
        success, query_messages, validations = execute_query(
            f"SELECT attribute_id, validation_name FROM {schema_name}.attribute_validations",
            schema_name=schema_name
        )
        
        if not success:
            messages.extend(query_messages)
            return False, messages
        
        if not validations:
            logger.warning("No validations found")
            messages.append("Warning: No validations found")
        else:
            logger.info(f"Found {len(validations)} validations")
            messages.append(f"Found {len(validations)} validations")
        
        # Check if calculated fields are stored in the calculated_fields table
        logger.info("Checking calculated fields")
        success, query_messages, calculated_fields = execute_query(
            f"SELECT field_id, attribute_id FROM {schema_name}.calculated_fields",
            schema_name=schema_name
        )
        
        if not success:
            messages.extend(query_messages)
            return False, messages
        
        if not calculated_fields:
            logger.warning("No calculated fields found")
            messages.append("Warning: No calculated fields found")
        else:
            logger.info(f"Found {len(calculated_fields)} calculated fields")
            messages.append(f"Found {len(calculated_fields)} calculated fields")
        
        # Check if lifecycle management is stored in the entity_lifecycle_management table
        logger.info("Checking lifecycle management")
        success, query_messages, lifecycle_management = execute_query(
            f"SELECT entity_id, management_type FROM {schema_name}.entity_lifecycle_management",
            schema_name=schema_name
        )
        
        if not success:
            messages.extend(query_messages)
            return False, messages
        
        if not lifecycle_management:
            logger.warning("No lifecycle management found")
            messages.append("Warning: No lifecycle management found")
        else:
            logger.info(f"Found {len(lifecycle_management)} lifecycle management entries")
            messages.append(f"Found {len(lifecycle_management)} lifecycle management entries")
        
        # Check for duplicate foreign key constraints
        logger.info("Checking for duplicate foreign key constraints")
        success, query_messages, constraints = execute_query(
            f"""
            SELECT
                tc.constraint_name,
                tc.table_name,
                kcu.column_name,
                ccu.table_name AS foreign_table_name,
                ccu.column_name AS foreign_column_name,
                COUNT(*) OVER (
                    PARTITION BY tc.table_name, kcu.column_name, ccu.table_name, ccu.column_name
                ) AS constraint_count
            FROM
                information_schema.table_constraints AS tc
                JOIN information_schema.key_column_usage AS kcu
                  ON tc.constraint_name = kcu.constraint_name
                  AND tc.table_schema = kcu.table_schema
                JOIN information_schema.constraint_column_usage AS ccu
                  ON ccu.constraint_name = tc.constraint_name
                  AND ccu.table_schema = tc.table_schema
            WHERE
                tc.constraint_type = 'FOREIGN KEY'
                AND tc.table_schema = %s
            """,
            (schema_name,)
        )
        
        if not success:
            messages.extend(query_messages)
            return False, messages
        
        duplicate_constraints = [c for c in constraints if c[5] > 1]
        
        if duplicate_constraints:
            logger.warning(f"Found {len(duplicate_constraints)} duplicate foreign key constraints")
            messages.append(f"Warning: Found {len(duplicate_constraints)} duplicate foreign key constraints")
            
            for constraint in duplicate_constraints:
                logger.warning(f"  {constraint[0]} on {constraint[1]}.{constraint[2]} references {constraint[3]}.{constraint[4]}")
                messages.append(f"  {constraint[0]} on {constraint[1]}.{constraint[2]} references {constraint[3]}.{constraint[4]}")
        else:
            logger.info("No duplicate foreign key constraints found")
            messages.append("No duplicate foreign key constraints found")
        
        logger.info("Verification completed")
        messages.append("Verification completed")
        
        return True, messages
    except Exception as e:
        error_msg = f"Error verifying fixes: {str(e)}"
        logger.error(error_msg, exc_info=True)
        messages.append(error_msg)
        return False, messages

def run_test_entity_deployer_v2() -> Tuple[bool, List[str]]:
    """
    Run the test_entity_deployer_v2.py script to test the fixed entity deployer.
    
    Returns:
        Tuple containing:
            - Boolean indicating if the test ran successfully
            - List of messages (warnings, errors, or success messages)
    """
    messages = []
    
    try:
        logger.info("Running test_entity_deployer_v2.py")
        
        # Get the path to the test_entity_deployer_v2.py file
        test_entity_deployer_v2_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "test_entity_deployer_v2.py")
        
        # Check if the file exists
        if not os.path.exists(test_entity_deployer_v2_path):
            logger.warning(f"test_entity_deployer_v2.py not found at {test_entity_deployer_v2_path}")
            messages.append(f"Warning: test_entity_deployer_v2.py not found at {test_entity_deployer_v2_path}")
            return False, messages
        
        # Run the script
        result = subprocess.run([sys.executable, test_entity_deployer_v2_path], capture_output=True, text=True)
        
        # Check if the script ran successfully
        if result.returncode == 0:
            logger.info("test_entity_deployer_v2.py ran successfully")
            messages.append("test_entity_deployer_v2.py ran successfully")
            
            # Log the output
            for line in result.stdout.splitlines():
                logger.info(f"  {line}")
                messages.append(f"  {line}")
            
            return True, messages
        else:
            logger.error(f"test_entity_deployer_v2.py failed with return code {result.returncode}")
            messages.append(f"Error: test_entity_deployer_v2.py failed with return code {result.returncode}")
            
            # Log the error output
            for line in result.stderr.splitlines():
                logger.error(f"  {line}")
                messages.append(f"  {line}")
            
            return False, messages
    except Exception as e:
        error_msg = f"Error running test_entity_deployer_v2.py: {str(e)}"
        logger.error(error_msg, exc_info=True)
        messages.append(error_msg)
        return False, messages

def main():
    """
    Main function to run all fixes.
    """
    logger.info("Starting to fix entity deployer")
    
    # Backup entity_deployer.py
    logger.info("=== Backing up entity_deployer.py ===")
    success, messages = backup_entity_deployer()
    for message in messages:
        logger.info(message)
    
    if not success:
        logger.error("Failed to backup entity_deployer.py")
        return
    
    # Replace entity_deployer.py with entity_deployer_v2.py
    logger.info("=== Replacing entity_deployer.py with entity_deployer_v2.py ===")
    success, messages = replace_entity_deployer()
    for message in messages:
        logger.info(message)
    
    if not success:
        logger.error("Failed to replace entity_deployer.py")
        return
    
    # Run implement_missing_features.py
    logger.info("=== Running implement_missing_features.py ===")
    success, messages = run_implement_missing_features()
    for message in messages:
        logger.info(message)
    
    if not success:
        logger.error("Failed to run implement_missing_features.py")
        return
    
    # Verify fixes
    logger.info("=== Verifying fixes ===")
    success, messages = verify_fixes()
    for message in messages:
        logger.info(message)
    
    # Run test_entity_deployer_v2.py
    logger.info("=== Running test_entity_deployer_v2.py ===")
    success, messages = run_test_entity_deployer_v2()
    for message in messages:
        logger.info(message)
    
    logger.info("All fixes completed successfully")
    print("All fixes completed successfully. See fix_entity_deployer.log for details.")

if __name__ == "__main__":
    main()
