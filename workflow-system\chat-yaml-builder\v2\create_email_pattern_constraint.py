#!/usr/bin/env python3
"""
Scrip<PERSON> to create the email pattern constraint for the Employee entity.
"""

import logging
from deployers.entity_deployer_v2 import execute_query

# Set up logging
logging.basicConfig(level=logging.INFO, 
                   format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
                   handlers=[
                       logging.FileHandler('/home/<USER>/workflow-system/chat-yaml-builder/v2/email_pattern_constraint.log'),
                       logging.StreamHandler()
                   ])
logger = logging.getLogger('create_email_pattern_constraint')

def create_email_pattern_constraint():
    """
    Create the email pattern constraint for the Employee entity.
    """
    schema_name = 'workflow_temp'
    
    # Get the Employee entity ID from the database
    success, messages, result = execute_query(
        f"SELECT entity_id FROM {schema_name}.entities WHERE name = 'Employee'",
        schema_name=schema_name
    )
    
    if not success or not result:
        logger.error("Employee entity not found in the database")
        return
    
    employee_id = result[0][0]
    logger.info(f"Found Employee entity in database with ID: {employee_id}")
    
    # Get the entity table name
    entity_num = employee_id[1:]  # Remove 'E' prefix
    table_name = f"e{entity_num}_employee"
    
    # Check if the table exists
    success, messages, result = execute_query(
        f"""
        SELECT EXISTS (
            SELECT FROM information_schema.tables 
            WHERE table_schema = %s
            AND table_name = %s
        )
        """,
        (schema_name, table_name),
        schema_name
    )
    
    if not success or not result or not result[0][0]:
        logger.error(f"Employee table '{table_name}' not found in the database")
        return
    
    logger.info(f"Found Employee table '{table_name}' in the database")
    
    # Check if the email column exists
    success, messages, result = execute_query(
        f"""
        SELECT EXISTS (
            SELECT FROM information_schema.columns 
            WHERE table_schema = %s
            AND table_name = %s
            AND column_name = 'email'
        )
        """,
        (schema_name, table_name),
        schema_name
    )
    
    if not success or not result or not result[0][0]:
        logger.error(f"Email column not found in table '{table_name}'")
        return
    
    logger.info(f"Found email column in table '{table_name}'")
    
    # Check if the check constraint already exists
    success, messages, result = execute_query(
        f"""
        SELECT EXISTS (
            SELECT FROM information_schema.table_constraints
            WHERE table_schema = %s
            AND table_name = %s
            AND constraint_name = 'check_email_pattern'
        )
        """,
        (schema_name, table_name),
        schema_name
    )
    
    if success and result and result[0][0]:
        logger.info("Check constraint for email pattern already exists")
        
        # Drop the constraint to recreate it
        success, messages, result = execute_query(
            f"""
            ALTER TABLE {schema_name}."{table_name}"
            DROP CONSTRAINT IF EXISTS check_email_pattern
            """,
            schema_name=schema_name
        )
        
        if success:
            logger.info("Dropped existing check constraint for email pattern")
        else:
            logger.error(f"Failed to drop check constraint for email pattern: {messages}")
            return
    
    # Create the check constraint with a simple pattern
    success, messages, result = execute_query(
        f"""
        ALTER TABLE {schema_name}."{table_name}"
        ADD CONSTRAINT check_email_pattern CHECK (email ~ '.*@.*\\..*')
        """,
        schema_name=schema_name
    )
    
    if success:
        logger.info("Created check constraint for email pattern")
    else:
        logger.error(f"Failed to create check constraint for email pattern: {messages}")
        return
    
    # Verify the constraint was created
    success, messages, result = execute_query(
        f"""
        SELECT constraint_name, constraint_type
        FROM information_schema.table_constraints
        WHERE table_schema = %s
        AND table_name = %s
        AND constraint_name = 'check_email_pattern'
        """,
        (schema_name, table_name),
        schema_name
    )
    
    if success and result:
        logger.info("\nEmail pattern constraint in the database:")
        for row in result:
            constraint_name = row[0]
            constraint_type = row[1]
            logger.info(f"  - {constraint_name}: {constraint_type}")
    else:
        logger.warning("Email pattern constraint not found in the database after creation")

if __name__ == "__main__":
    create_email_pattern_constraint()
