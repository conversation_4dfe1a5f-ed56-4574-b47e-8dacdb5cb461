"""
Database Utilities for YAML Builder v2

This module provides utility functions for database operations.
"""

import os
import sys
import logging
import psycopg2
from typing import Dict, List, Tuple, Any, Optional

# Set up logging
logger = logging.getLogger('db_utils')

def get_connection():
    """
    Get a database connection.
    
    Returns:
        Database connection
    """
    try:
        connection = psycopg2.connect(
            dbname="workflow_system",
            user="postgres",
            password="workflow_postgres_secure_password",
            host="**********",
            port="5432"
        )
        return connection
    except Exception as e:
        logger.error(f"Error connecting to database: {str(e)}", exc_info=True)
        raise

def execute_query(query: str, params: Tuple = None) -> Tuple[bool, List[str], List[Tuple]]:
    """
    Execute a database query.
    
    Args:
        query: SQL query to execute
        params: Query parameters
        
    Returns:
        Tuple containing:
            - Boolean indicating if execution was successful
            - List of messages (warnings, errors, or success messages)
            - List of result rows (empty if no results)
    """
    messages = []
    result = []
    
    try:
        connection = get_connection()
        cursor = connection.cursor()
        
        cursor.execute(query, params)
        
        if query.strip().upper().startswith(('SELECT', 'SHOW', 'DESCRIBE')):
            result = cursor.fetchall()
        
        connection.commit()
        cursor.close()
        connection.close()
        
        return True, messages, result
    except Exception as e:
        logger.error(f"Error executing query: {str(e)}", exc_info=True)
        messages.append(f"Error executing query: {str(e)}")
        
        try:
            if connection:
                connection.rollback()
                cursor.close()
                connection.close()
        except:
            pass
        
        return False, messages, result

def create_schema(schema_name: str) -> Tuple[bool, List[str]]:
    """
    Create a schema if it doesn't exist.
    
    Args:
        schema_name: Schema name to create
        
    Returns:
        Tuple containing:
            - Boolean indicating if creation was successful
            - List of messages (warnings, errors, or success messages)
    """
    messages = []
    
    try:
        # Check if schema exists
        success, query_messages, result = execute_query(
            """
            SELECT EXISTS (
                SELECT FROM information_schema.schemata
                WHERE schema_name = %s
            )
            """,
            (schema_name,)
        )
        
        if not success:
            messages.extend(query_messages)
            return False, messages
        
        if result and result[0][0]:
            messages.append(f"Schema {schema_name} already exists")
            return True, messages
        
        # Create schema
        success, query_messages, _ = execute_query(
            f"CREATE SCHEMA {schema_name}"
        )
        
        if not success:
            messages.extend(query_messages)
            return False, messages
        
        messages.append(f"Created schema {schema_name}")
        return True, messages
    except Exception as e:
        logger.error(f"Error creating schema {schema_name}: {str(e)}", exc_info=True)
        messages.append(f"Error creating schema {schema_name}: {str(e)}")
        return False, messages

def create_temp_schema(schema_name: str) -> Tuple[bool, List[str]]:
    """
    Create a temporary schema for validation and testing.
    
    Args:
        schema_name: Schema name to create
        
    Returns:
        Tuple containing:
            - Boolean indicating if creation was successful
            - List of messages (warnings, errors, or success messages)
    """
    messages = []
    
    try:
        # Check if schema exists
        success, query_messages, result = execute_query(
            """
            SELECT EXISTS (
                SELECT FROM information_schema.schemata
                WHERE schema_name = %s
            )
            """,
            (schema_name,)
        )
        
        if not success:
            messages.extend(query_messages)
            return False, messages
        
        if result and result[0][0]:
            # Drop existing schema
            success, query_messages, _ = execute_query(
                f"DROP SCHEMA {schema_name} CASCADE"
            )
            
            if not success:
                messages.extend(query_messages)
                return False, messages
            
            messages.append(f"Dropped existing schema {schema_name}")
        
        # Create schema
        success, query_messages, _ = execute_query(
            f"CREATE SCHEMA {schema_name}"
        )
        
        if not success:
            messages.extend(query_messages)
            return False, messages
        
        messages.append(f"Created temporary schema {schema_name}")
        return True, messages
    except Exception as e:
        logger.error(f"Error creating temporary schema {schema_name}: {str(e)}", exc_info=True)
        messages.append(f"Error creating temporary schema {schema_name}: {str(e)}")
        return False, messages

def create_table(schema_name: str, table_name: str, columns: str) -> Tuple[bool, List[str]]:
    """
    Create a table if it doesn't exist.
    
    Args:
        schema_name: Schema name
        table_name: Table name
        columns: Column definitions
        
    Returns:
        Tuple containing:
            - Boolean indicating if creation was successful
            - List of messages (warnings, errors, or success messages)
    """
    messages = []
    
    try:
        # Check if table exists
        success, query_messages, result = execute_query(
            """
            SELECT EXISTS (
                SELECT FROM information_schema.tables
                WHERE table_schema = %s
                AND table_name = %s
            )
            """,
            (schema_name, table_name)
        )
        
        if not success:
            messages.extend(query_messages)
            return False, messages
        
        if result and result[0][0]:
            messages.append(f"Table {schema_name}.{table_name} already exists")
            return True, messages
        
        # Create table
        success, query_messages, _ = execute_query(
            f"""
            CREATE TABLE {schema_name}.{table_name} (
                {columns}
            )
            """
        )
        
        if not success:
            messages.extend(query_messages)
            return False, messages
        
        messages.append(f"Created table {schema_name}.{table_name}")
        return True, messages
    except Exception as e:
        logger.error(f"Error creating table {schema_name}.{table_name}: {str(e)}", exc_info=True)
        messages.append(f"Error creating table {schema_name}.{table_name}: {str(e)}")
        return False, messages

def add_column(schema_name: str, table_name: str, column_name: str, column_type: str) -> Tuple[bool, List[str]]:
    """
    Add a column to a table if it doesn't exist.
    
    Args:
        schema_name: Schema name
        table_name: Table name
        column_name: Column name
        column_type: Column type
        
    Returns:
        Tuple containing:
            - Boolean indicating if addition was successful
            - List of messages (warnings, errors, or success messages)
    """
    messages = []
    
    try:
        # Check if column exists
        success, query_messages, result = execute_query(
            """
            SELECT EXISTS (
                SELECT FROM information_schema.columns
                WHERE table_schema = %s
                AND table_name = %s
                AND column_name = %s
            )
            """,
            (schema_name, table_name, column_name)
        )
        
        if not success:
            messages.extend(query_messages)
            return False, messages
        
        if result and result[0][0]:
            messages.append(f"Column {column_name} already exists in table {schema_name}.{table_name}")
            return True, messages
        
        # Add column
        success, query_messages, _ = execute_query(
            f"""
            ALTER TABLE {schema_name}.{table_name}
            ADD COLUMN {column_name} {column_type}
            """
        )
        
        if not success:
            messages.extend(query_messages)
            return False, messages
        
        messages.append(f"Added column {column_name} to table {schema_name}.{table_name}")
        return True, messages
    except Exception as e:
        logger.error(f"Error adding column {column_name} to table {schema_name}.{table_name}: {str(e)}", exc_info=True)
        messages.append(f"Error adding column {column_name} to table {schema_name}.{table_name}: {str(e)}")
        return False, messages

def add_constraint(schema_name: str, table_name: str, constraint_name: str, constraint: str) -> Tuple[bool, List[str]]:
    """
    Add a constraint to a table if it doesn't exist.
    
    Args:
        schema_name: Schema name
        table_name: Table name
        constraint_name: Constraint name
        constraint: Constraint definition
        
    Returns:
        Tuple containing:
            - Boolean indicating if addition was successful
            - List of messages (warnings, errors, or success messages)
    """
    messages = []
    
    try:
        # Check if constraint exists
        success, query_messages, result = execute_query(
            """
            SELECT EXISTS (
                SELECT FROM information_schema.table_constraints
                WHERE constraint_schema = %s
                AND table_name = %s
                AND constraint_name = %s
            )
            """,
            (schema_name, table_name, constraint_name)
        )
        
        if not success:
            messages.extend(query_messages)
            return False, messages
        
        if result and result[0][0]:
            messages.append(f"Constraint {constraint_name} already exists in table {schema_name}.{table_name}")
            return True, messages
        
        # Add constraint
        success, query_messages, _ = execute_query(
            f"""
            ALTER TABLE {schema_name}.{table_name}
            ADD CONSTRAINT {constraint_name} {constraint}
            """
        )
        
        if not success:
            messages.extend(query_messages)
            return False, messages
        
        messages.append(f"Added constraint {constraint_name} to table {schema_name}.{table_name}")
        return True, messages
    except Exception as e:
        logger.error(f"Error adding constraint {constraint_name} to table {schema_name}.{table_name}: {str(e)}", exc_info=True)
        messages.append(f"Error adding constraint {constraint_name} to table {schema_name}.{table_name}: {str(e)}")
        return False, messages
