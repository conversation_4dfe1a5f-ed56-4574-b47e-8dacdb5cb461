"""
Organizational Service for v2 API

This module contains the business logic for organizational hierarchy and user profiles.
"""

import logging
from typing import Optional, List, Dict, Any
from sqlalchemy.orm import Session
from sqlalchemy.sql import text

from .organizational_models import (
    UserProfileResponse, UserProfileUpdateRequest, DepartmentResponse, TeamResponse,
    SystemPermissionResponse, RoleKPIResponse, OrganizationalHierarchyResponse,
    OrganizationalTreeNode, OrganizationalTreeResponse, OrganizationalLevel,
    EnhancedLoginResponse, DepartmentListResponse, TeamListResponse,
    UserTeamMembershipResponse, UserTeamMembershipRequest, PermissionType,
    PermissionAction, PermissionCheckRequest, PermissionCheckResponse
)
from .models import UserRegistrationResponse, TokenResponse
from .service import UserRegistrationService, UserAuthenticationService

# Configure logging
logger = logging.getLogger(__name__)


class OrganizationalService:
    """Service class for organizational operations"""
    
    def __init__(self, db_session: Session):
        """
        Initialize the organizational service.
        
        Args:
            db_session: SQLAlchemy database session
        """
        self.db = db_session
        self.logger = logger
        self.user_service = UserRegistrationService(db_session)
    
    def get_department_by_id(self, department_id: str) -> Optional[DepartmentResponse]:
        """
        Get department by ID.
        
        Args:
            department_id: Department ID
            
        Returns:
            Optional[DepartmentResponse]: Department data if found, None otherwise
        """
        try:
            query = """
            SELECT department_id, name, description, head_user_id, parent_department_id,
                   created_at, updated_at
            FROM workflow_runtime.departments
            WHERE department_id = :department_id
            """
            
            result = self.db.execute(text(query), {"department_id": department_id}).fetchone()
            
            if not result:
                return None
            
            return DepartmentResponse(
                department_id=result.department_id,
                name=result.name,
                description=result.description,
                head_user_id=result.head_user_id,
                parent_department_id=result.parent_department_id,
                created_at=result.created_at,
                updated_at=result.updated_at
            )
            
        except Exception as e:
            self.logger.error(f"Error getting department by ID: {str(e)}")
            return None
    
    def get_team_by_id(self, team_id: str) -> Optional[TeamResponse]:
        """
        Get team by ID.
        
        Args:
            team_id: Team ID
            
        Returns:
            Optional[TeamResponse]: Team data if found, None otherwise
        """
        try:
            query = """
            SELECT team_id, name, description, department_id, team_lead_user_id,
                   created_at, updated_at
            FROM workflow_runtime.teams
            WHERE team_id = :team_id
            """
            
            result = self.db.execute(text(query), {"team_id": team_id}).fetchone()
            
            if not result:
                return None
            
            return TeamResponse(
                team_id=result.team_id,
                name=result.name,
                description=result.description,
                department_id=result.department_id,
                team_lead_user_id=result.team_lead_user_id,
                created_at=result.created_at,
                updated_at=result.updated_at
            )
            
        except Exception as e:
            self.logger.error(f"Error getting team by ID: {str(e)}")
            return None
    
    def get_user_team_memberships(self, user_id: str) -> List[UserTeamMembershipResponse]:
        """
        Get user's team memberships.
        
        Args:
            user_id: User ID
            
        Returns:
            List[UserTeamMembershipResponse]: List of team memberships
        """
        try:
            query = """
            SELECT utm.team_id, t.name as team_name, utm.is_primary_team, utm.joined_at
            FROM workflow_runtime.user_team_memberships utm
            JOIN workflow_runtime.teams t ON utm.team_id = t.team_id
            WHERE utm.user_id = :user_id
            ORDER BY utm.is_primary_team DESC, utm.joined_at ASC
            """
            
            results = self.db.execute(text(query), {"user_id": user_id}).fetchall()
            
            memberships = []
            for result in results:
                memberships.append(UserTeamMembershipResponse(
                    team_id=result.team_id,
                    team_name=result.team_name,
                    is_primary_team=result.is_primary_team,
                    joined_at=result.joined_at
                ))
            
            return memberships
            
        except Exception as e:
            self.logger.error(f"Error getting user team memberships: {str(e)}")
            return []
    
    def get_user_permissions_by_type(self, user_id: str, permission_type: PermissionType) -> List[SystemPermissionResponse]:
        """
        Get user permissions by type.
        
        Args:
            user_id: User ID
            permission_type: Type of permissions to retrieve
            
        Returns:
            List[SystemPermissionResponse]: List of permissions
        """
        try:
            # Get permissions from roles
            query = """
            SELECT DISTINCT sp.permission_id, sp.permission_name, sp.permission_type, 
                   sp.resource_identifier, sp.actions, rsp.granted_actions, sp.description
            FROM workflow_runtime.system_permissions sp
            JOIN workflow_runtime.role_system_permissions rsp ON sp.permission_id = rsp.permission_id
            JOIN workflow_runtime.user_roles ur ON rsp.role_id = ur.role
            WHERE ur.user_id = :user_id AND sp.permission_type = :permission_type
            
            UNION
            
            SELECT DISTINCT sp.permission_id, sp.permission_name, sp.permission_type,
                   sp.resource_identifier, sp.actions, upo.granted_actions, sp.description
            FROM workflow_runtime.system_permissions sp
            JOIN workflow_runtime.user_permission_overrides upo ON sp.permission_id = upo.permission_id
            WHERE upo.user_id = :user_id AND sp.permission_type = :permission_type AND upo.is_grant = true
            
            ORDER BY permission_name
            """
            
            results = self.db.execute(text(query), {
                "user_id": user_id, 
                "permission_type": permission_type.value
            }).fetchall()
            
            permissions = []
            for result in results:
                permissions.append(SystemPermissionResponse(
                    permission_id=result.permission_id,
                    permission_name=result.permission_name,
                    permission_type=PermissionType(result.permission_type),
                    resource_identifier=result.resource_identifier,
                    available_actions=[PermissionAction(action) for action in result.actions],
                    granted_actions=[PermissionAction(action) for action in result.granted_actions],
                    description=result.description
                ))
            
            return permissions
            
        except Exception as e:
            self.logger.error(f"Error getting user permissions by type: {str(e)}")
            return []
    
    def get_user_role_kpis(self, user_id: str) -> List[RoleKPIResponse]:
        """
        Get KPIs for user's roles.
        
        Args:
            user_id: User ID
            
        Returns:
            List[RoleKPIResponse]: List of role KPIs
        """
        try:
            query = """
            SELECT DISTINCT rk.id, rk.role_id, rk.name, rk.description, rk.formula, 
                   rk.target, rk.measurement_frequency, rk.created_at
            FROM workflow_runtime.role_kpis rk
            JOIN workflow_runtime.user_roles ur ON rk.role_id = ur.role
            WHERE ur.user_id = :user_id
            ORDER BY rk.name
            """
            
            results = self.db.execute(text(query), {"user_id": user_id}).fetchall()
            
            kpis = []
            for result in results:
                kpis.append(RoleKPIResponse(
                    kpi_id=str(result.id),
                    role_id=result.role_id,
                    name=result.name,
                    description=result.description,
                    formula=result.formula,
                    target=result.target,
                    measurement_frequency=result.measurement_frequency,
                    created_at=result.created_at
                ))
            
            return kpis
            
        except Exception as e:
            self.logger.error(f"Error getting user role KPIs: {str(e)}")
            return []
    
    def get_organizational_hierarchy(self, user_id: str) -> Optional[OrganizationalHierarchyResponse]:
        """
        Get organizational hierarchy for a user.
        
        Args:
            user_id: User ID
            
        Returns:
            Optional[OrganizationalHierarchyResponse]: Organizational hierarchy data
        """
        try:
            # Get user's hierarchy info
            query = """
            SELECT user_id, reports_to_user_id, organizational_level
            FROM workflow_runtime.users
            WHERE user_id = :user_id
            """
            
            result = self.db.execute(text(query), {"user_id": user_id}).fetchone()
            
            if not result:
                return None
            
            # Get direct reports
            direct_reports_query = """
            SELECT user_id FROM workflow_runtime.users
            WHERE reports_to_user_id = :user_id
            ORDER BY first_name, last_name
            """
            
            direct_reports_results = self.db.execute(text(direct_reports_query), {"user_id": user_id}).fetchall()
            direct_reports = [row.user_id for row in direct_reports_results]
            
            # Build hierarchy path
            hierarchy_path = []
            current_user_id = user_id
            
            while current_user_id:
                hierarchy_path.insert(0, current_user_id)
                
                # Get manager
                manager_query = """
                SELECT reports_to_user_id FROM workflow_runtime.users
                WHERE user_id = :user_id
                """
                
                manager_result = self.db.execute(text(manager_query), {"user_id": current_user_id}).fetchone()
                current_user_id = manager_result.reports_to_user_id if manager_result and manager_result.reports_to_user_id else None
            
            return OrganizationalHierarchyResponse(
                user_id=result.user_id,
                reports_to_user_id=result.reports_to_user_id,
                organizational_level=OrganizationalLevel(result.organizational_level) if result.organizational_level else None,
                direct_reports=direct_reports,
                hierarchy_path=hierarchy_path
            )
            
        except Exception as e:
            self.logger.error(f"Error getting organizational hierarchy: {str(e)}")
            return None
    
    def get_team_colleagues(self, user_id: str) -> List[str]:
        """
        Get team colleagues for a user across all their teams.
        
        Args:
            user_id: User ID
            
        Returns:
            List[str]: List of colleague user IDs
        """
        try:
            query = """
            SELECT DISTINCT utm2.user_id
            FROM workflow_runtime.user_team_memberships utm1
            JOIN workflow_runtime.user_team_memberships utm2 ON utm1.team_id = utm2.team_id
            WHERE utm1.user_id = :user_id AND utm2.user_id != :user_id
            ORDER BY utm2.user_id
            """
            
            results = self.db.execute(text(query), {"user_id": user_id}).fetchall()
            return [row.user_id for row in results]
            
        except Exception as e:
            self.logger.error(f"Error getting team colleagues: {str(e)}")
            return []
    
    def get_department_colleagues(self, user_id: str, department_id: str) -> List[str]:
        """
        Get department colleagues for a user.
        
        Args:
            user_id: User ID
            department_id: Department ID
            
        Returns:
            List[str]: List of colleague user IDs
        """
        try:
            query = """
            SELECT user_id FROM workflow_runtime.users
            WHERE department_id = :department_id AND user_id != :user_id
            ORDER BY user_id
            """
            
            results = self.db.execute(text(query), {
                "department_id": department_id,
                "user_id": user_id
            }).fetchall()
            
            return [row.user_id for row in results]
            
        except Exception as e:
            self.logger.error(f"Error getting department colleagues: {str(e)}")
            return []
    
    def get_user_profile(self, user_id: str) -> Optional[UserProfileResponse]:
        """
        Get comprehensive user profile with organizational context.
        
        Args:
            user_id: User ID
            
        Returns:
            Optional[UserProfileResponse]: User profile data if found, None otherwise
        """

        try:
            # Get user data with organizational fields directly
            user_query = """
            SELECT u.user_id, u.username, u.email, u.first_name, u.last_name, 
                   u.status, u.created_at, u.department_id, u.reports_to_user_id, u.organizational_level,
                   array_agg(DISTINCT ur.role) as roles, ur.tenant_id
            FROM workflow_runtime.users u
            LEFT JOIN workflow_runtime.user_roles ur ON u.user_id = ur.user_id
            WHERE u.user_id = :user_id AND u.status = 'active'
            GROUP BY u.user_id, u.username, u.email, u.first_name, u.last_name, 
                    u.status, u.created_at, u.department_id, u.reports_to_user_id, u.organizational_level, ur.tenant_id
            """

            
            user_result = self.db.execute(text(user_query), {"user_id": user_id}).fetchone()
            if not user_result:
                return None
            
            # Create user object from result
            user = type('User', (), {
                'user_id': user_result.user_id,
                'username': user_result.username,
                'email': user_result.email,
                'first_name': user_result.first_name,
                'last_name': user_result.last_name,
                'status': user_result.status,
                'roles': user_result.roles or [],
                'tenant_id': user_result.tenant_id,
                'disabled': False,
                'created_at': user_result.created_at,
                'department_id': user_result.department_id
            })()
            
            # Get department information
            department = None
            if user.department_id:
                department = self.get_department_by_id(user.department_id)
        
                   
            # Get team memberships
            team_memberships = self.get_user_team_memberships(user_id)
            
            # Get organizational hierarchy
            hierarchy = self.get_organizational_hierarchy(user_id)
            
            # Get permissions by type
            entity_permissions = self.get_user_permissions_by_type(user_id, PermissionType.entity)
            attribute_permissions = self.get_user_permissions_by_type(user_id, PermissionType.attribute)
            global_objective_permissions = self.get_user_permissions_by_type(user_id, PermissionType.global_objective)
            local_objective_permissions = self.get_user_permissions_by_type(user_id, PermissionType.local_objective)
            book_permissions = self.get_user_permissions_by_type(user_id, PermissionType.book)
            chapter_permissions = self.get_user_permissions_by_type(user_id, PermissionType.chapter)
            
            # Get role KPIs
            role_kpis = self.get_user_role_kpis(user_id)
            
            # Get colleagues
            team_colleagues = self.get_team_colleagues(user_id)
            department_colleagues = []
            if hasattr(user, 'department_id') and user.department_id:
                department_colleagues = self.get_department_colleagues(user_id, user.department_id)
            
            return UserProfileResponse(
                user_id=user.user_id,
                username=user.username,
                email=user.email,
                first_name=user.first_name,
                last_name=user.last_name,
                status=user.status,
                roles=user.roles,
                tenant_id=user.tenant_id,
                disabled=user.disabled,
                created_at=user.created_at,
                department=department,
                team_memberships=team_memberships,
                organizational_hierarchy=hierarchy,
                entity_permissions=entity_permissions,
                attribute_permissions=attribute_permissions,
                global_objective_permissions=global_objective_permissions,
                local_objective_permissions=local_objective_permissions,
                book_permissions=book_permissions,
                chapter_permissions=chapter_permissions,
                role_kpis=role_kpis,
                team_colleagues=team_colleagues,
                department_colleagues=department_colleagues
            )
            
        except Exception as e:
            self.logger.error(f"Error getting user profile: {str(e)}")
            return None
    
    def update_user_profile(self, user_id: str, update_data: UserProfileUpdateRequest) -> Optional[UserRegistrationResponse]:
        """
        Update user profile information.
        
        Args:
            user_id: User ID
            update_data: Profile update data
            
        Returns:
            Optional[UserRegistrationResponse]: Updated user data if successful, None otherwise
        """
        try:
            # Check if user exists
            if not self.user_service.get_user_by_id(user_id):
                return None
            
            # Validate organizational references
            if update_data.department_id:
                if not self.get_department_by_id(update_data.department_id):
                    raise ValueError("Invalid department_id")
            
            if update_data.reports_to_user_id:
                if not self.user_service.get_user_by_id(update_data.reports_to_user_id):
                    raise ValueError("Invalid reports_to_user_id")
            
            # Build update query dynamically
            update_fields = []
            update_values = []
            
            if update_data.first_name is not None:
                update_fields.append("first_name = :first_name")
                update_values.append(("first_name", update_data.first_name))
            
            if update_data.last_name is not None:
                update_fields.append("last_name = :last_name")
                update_values.append(("last_name", update_data.last_name))
            
            if update_data.department_id is not None:
                update_fields.append("department_id = :department_id")
                update_values.append(("department_id", update_data.department_id))
            
            if update_data.reports_to_user_id is not None:
                update_fields.append("reports_to_user_id = :reports_to_user_id")
                update_values.append(("reports_to_user_id", update_data.reports_to_user_id))
            
            if update_data.organizational_level is not None:
                update_fields.append("organizational_level = :organizational_level")
                update_values.append(("organizational_level", update_data.organizational_level.value))
            
            if update_fields:
                update_fields.append("updated_at = CURRENT_TIMESTAMP")
                query = f"UPDATE workflow_runtime.users SET {', '.join(update_fields)} WHERE user_id = :user_id"
                
                # Build parameters dict
                params = {"user_id": user_id}
                for key, value in update_values:
                    params[key] = value
                
                self.db.execute(text(query), params)
                self.db.commit()
            
            # Return updated user
            return self.user_service.get_user_by_id(user_id)
            
        except Exception as e:
            self.db.rollback()
            self.logger.error(f"Error updating user profile: {str(e)}")
            raise e
    
    def add_user_to_team(self, user_id: str, membership_request: UserTeamMembershipRequest) -> bool:
        """
        Add user to a team.
        
        Args:
            user_id: User ID
            membership_request: Team membership request
            
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            # Validate user and team exist
            if not self.user_service.get_user_by_id(user_id):
                raise ValueError("Invalid user_id")
            
            if not self.get_team_by_id(membership_request.team_id):
                raise ValueError("Invalid team_id")
            
            # If this should be primary team, unset other primary teams
            if membership_request.is_primary_team:
                update_query = """
                UPDATE workflow_runtime.user_team_memberships 
                SET is_primary_team = false 
                WHERE user_id = :user_id
                """
                self.db.execute(text(update_query), {"user_id": user_id})
            
            # Insert team membership
            insert_query = """
            INSERT INTO workflow_runtime.user_team_memberships 
            (user_id, team_id, is_primary_team, joined_at)
            VALUES (:user_id, :team_id, :is_primary_team, CURRENT_TIMESTAMP)
            ON CONFLICT (user_id, team_id) 
            DO UPDATE SET is_primary_team = :is_primary_team, updated_at = CURRENT_TIMESTAMP
            """
            
            self.db.execute(text(insert_query), {
                "user_id": user_id,
                "team_id": membership_request.team_id,
                "is_primary_team": membership_request.is_primary_team
            })
            
            self.db.commit()
            return True
            
        except Exception as e:
            self.db.rollback()
            self.logger.error(f"Error adding user to team: {str(e)}")
            return False
    
    def remove_user_from_team(self, user_id: str, team_id: str) -> bool:
        """
        Remove user from a team.
        
        Args:
            user_id: User ID
            team_id: Team ID
            
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            query = """
            DELETE FROM workflow_runtime.user_team_memberships
            WHERE user_id = :user_id AND team_id = :team_id
            """
            
            result = self.db.execute(text(query), {
                "user_id": user_id,
                "team_id": team_id
            })
            
            self.db.commit()
            return result.rowcount > 0
            
        except Exception as e:
            self.db.rollback()
            self.logger.error(f"Error removing user from team: {str(e)}")
            return False
    
    def check_user_permission(self, check_request: PermissionCheckRequest) -> PermissionCheckResponse:
        """
        Check if user has a specific permission.
        
        Args:
            check_request: Permission check request
            
        Returns:
            PermissionCheckResponse: Permission check result
        """
        try:
            # Check role-based permissions
            role_query = """
            SELECT rsp.granted_actions, rsp.row_level_conditions, ur.role
            FROM workflow_runtime.role_system_permissions rsp
            JOIN workflow_runtime.system_permissions sp ON rsp.permission_id = sp.permission_id
            JOIN workflow_runtime.user_roles ur ON rsp.role_id = ur.role
            WHERE ur.user_id = :user_id 
            AND sp.permission_type = :permission_type 
            AND sp.resource_identifier = :resource_identifier
            """
            
            role_results = self.db.execute(text(role_query), {
                "user_id": check_request.user_id,
                "permission_type": check_request.permission_type.value,
                "resource_identifier": check_request.resource_identifier
            }).fetchall()
            
            # Check user-specific overrides
            override_query = """
            SELECT upo.granted_actions, upo.is_grant, upo.row_level_conditions
            FROM workflow_runtime.user_permission_overrides upo
            JOIN workflow_runtime.system_permissions sp ON upo.permission_id = sp.permission_id
            WHERE upo.user_id = :user_id 
            AND sp.permission_type = :permission_type 
            AND sp.resource_identifier = :resource_identifier
            """
            
            override_results = self.db.execute(text(override_query), {
                "user_id": check_request.user_id,
                "permission_type": check_request.permission_type.value,
                "resource_identifier": check_request.resource_identifier
            }).fetchall()
            
            # Determine if user has permission
            has_permission = False
            granted_via = None
            conditions = None
            
            # Check role permissions
            for result in role_results:
                if check_request.action.value in result.granted_actions:
                    has_permission = True
                    granted_via = f"role:{result.role}"
                    conditions = result.row_level_conditions
                    break
            
            # Check user overrides (these take precedence)
            for result in override_results:
                if check_request.action.value in result.granted_actions:
                    has_permission = result.is_grant
                    granted_via = "user_override"
                    conditions = result.row_level_conditions
                    break
            
            return PermissionCheckResponse(
                user_id=check_request.user_id,
                permission_type=check_request.permission_type,
                resource_identifier=check_request.resource_identifier,
                action=check_request.action,
                has_permission=has_permission,
                granted_via=granted_via,
                conditions=conditions
            )
            
        except Exception as e:
            self.logger.error(f"Error checking user permission: {str(e)}")
            return PermissionCheckResponse(
                user_id=check_request.user_id,
                permission_type=check_request.permission_type,
                resource_identifier=check_request.resource_identifier,
                action=check_request.action,
                has_permission=False,
                granted_via=None,
                conditions=None
            )
    
    def get_all_departments(self) -> DepartmentListResponse:
        """
        Get all departments.
        
        Returns:
            DepartmentListResponse: List of all departments
        """
        try:
            query = """
            SELECT department_id, name, description, head_user_id, parent_department_id,
                   created_at, updated_at
            FROM workflow_runtime.departments
            ORDER BY name
            """
            
            results = self.db.execute(text(query)).fetchall()
            
            departments = []
            for result in results:
                departments.append(DepartmentResponse(
                    department_id=result.department_id,
                    name=result.name,
                    description=result.description,
                    head_user_id=result.head_user_id,
                    parent_department_id=result.parent_department_id,
                    created_at=result.created_at,
                    updated_at=result.updated_at
                ))
            
            return DepartmentListResponse(
                departments=departments,
                total_count=len(departments)
            )
            
        except Exception as e:
            self.logger.error(f"Error getting all departments: {str(e)}")
            return DepartmentListResponse(departments=[], total_count=0)
    
    def get_all_teams(self, department_id: Optional[str] = None) -> TeamListResponse:
        """
        Get all teams, optionally filtered by department.
        
        Args:
            department_id: Optional department ID to filter by
            
        Returns:
            TeamListResponse: List of teams
        """
        try:
            query = """
            SELECT team_id, name, description, department_id, team_lead_user_id,
                   created_at, updated_at
            FROM workflow_runtime.teams
            """
            
            params = {}
            if department_id:
                query += " WHERE department_id = :department_id"
                params["department_id"] = department_id
            
            query += " ORDER BY name"
            
            results = self.db.execute(text(query), params).fetchall()
            
            teams = []
            for result in results:
                teams.append(TeamResponse(
                    team_id=result.team_id,
                    name=result.name,
                    description=result.description,
                    department_id=result.department_id,
                    team_lead_user_id=result.team_lead_user_id,
                    created_at=result.created_at,
                    updated_at=result.updated_at
                ))
            
            return TeamListResponse(
                teams=teams,
                total_count=len(teams)
            )
            
        except Exception as e:
            self.logger.error(f"Error getting all teams: {str(e)}")
            return TeamListResponse(teams=[], total_count=0)
