"""
Entity Parser for YAML Builder v2

This module provides functionality for parsing entity definitions from natural language text.
"""

import os
import sys
import logging
import re
from typing import Dict, List, Tuple, Any, Optional

# Import functions from entity_parser_loading_strategies.py
from .entity_parser_loading_strategies import (
    parse_loading_strategies,
    parse_archive_strategy,
    parse_history_tracking,
    parse_workflow,
    parse_business_rule_placement,
    parse_workflow_placement,
    parse_entity_placement
)

# Set up logging
logger = logging.getLogger('entity_parser')

def parse_entity_text(text: str) -> Tuple[bool, Dict, List[str]]:
    """
    Parse entity definitions from natural language text.
    
    Args:
        text: Natural language text containing entity definitions
        
    Returns:
        Tuple containing:
            - Boolean indicating if parsing was successful
            - Dictionary containing parsed entity data
            - List of messages (warnings, errors, or success messages)
    """
    messages = []
    
    try:
        logger.info("Parsing entity definitions from natural language text")
        
        # Extract entity data from text
        success, entity_data, extract_messages = extract_entity_data(text)
        messages.extend(extract_messages)
        
        if not success:
            return False, {}, messages
        
        logger.info(f"Successfully parsed {len(entity_data)} entity definitions")
        return True, {'entities': entity_data}, messages
    except Exception as e:
        logger.error(f"Error parsing entity definitions: {str(e)}", exc_info=True)
        messages.append(f"Error parsing entity definitions: {str(e)}")
        return False, {}, messages

def extract_entity_data(text: str) -> Tuple[bool, Dict, List[str]]:
    """
    Extract entity data from natural language text.
    
    Args:
        text: Natural language text containing entity definitions
        
    Returns:
        Tuple containing:
            - Boolean indicating if extraction was successful
            - Dictionary containing extracted entity data
            - List of messages (warnings, errors, or success messages)
    """
    messages = []
    entities = {}
    
    try:
        # Split text into entity definitions
        entity_blocks = re.split(r'\n\s*\n', text)
        
        for block in entity_blocks:
            if not block.strip():
                continue
            
            # Extract entity name and attributes
            entity_match = re.match(r'(\w+)\s+has\s+(.*?)\.', block, re.DOTALL)
            if not entity_match:
                continue
            
            entity_name = entity_match.group(1)
            attributes_text = entity_match.group(2)
            
            # Create entity dictionary
            entity = create_entity_dict(entity_name)
            
            # Parse attributes
            parse_attributes(entity, attributes_text)
            
            # Parse relationships
            parse_relationships(entity, entity_name, block)
            
            # Parse defaults
            parse_defaults(entity, entity_name, block)
            
            # Parse business rules
            parse_business_rules(entity, entity_name, block)
            
            # Parse calculated fields
            parse_calculated_fields(entity, entity_name, block)
            
            # Parse validations
            parse_validations(entity, entity_name, block)
            
            # Parse constraints
            parse_constraints(entity, entity_name, block)
            
            # Parse belongs_to constraints
            parse_belongs_to_constraints(entity, entity_name, block)
            
            # Parse entity additional properties
            parse_entity_properties(entity, block)
            
            # Parse attribute additional properties
            parse_attribute_properties(entity, block)
            
            # Parse relationship properties
            parse_relationship_properties(entity, entity_name, block)
            
            # Parse synthetic data
            parse_synthetic_data(entity, entity_name, block)
            
            # Parse confidential attributes
            parse_confidential_attributes(entity, entity_name, block)
            
            # Parse internal attributes
            parse_internal_attributes(entity, entity_name, block)
            
            # Parse public attributes
            parse_public_attributes(entity, entity_name, block)
            
            # Parse loading strategies
            parse_loading_strategies(entity, entity_name, block)
            
            # Parse archive strategy
            parse_archive_strategy(entity, entity_name, block)
            
            # Parse history tracking
            parse_history_tracking(entity, entity_name, block)
            
            # Parse workflow
            parse_workflow(entity, entity_name, block)
            
            # Parse business rule placement
            parse_business_rule_placement(entity, entity_name, block)
            
            # Parse workflow placement
            parse_workflow_placement(entity, entity_name, block)
            
            # Parse entity placement
            parse_entity_placement(entity, entity_name, block)
            
            # Add entity to entities dictionary
            entities[entity_name] = entity
        
        if not entities:
            logger.warning("No entity definitions found in the text")
            messages.append("Warning: No entity definitions found in the text")
        
        return True, entities, messages
    except Exception as e:
        logger.error(f"Error extracting entity data: {str(e)}", exc_info=True)
        messages.append(f"Error extracting entity data: {str(e)}")
        return False, {}, messages

def create_entity_dict(entity_name: str) -> Dict:
    """
    Create entity dictionary with default values.
    
    Args:
        entity_name: Entity name
        
    Returns:
        Entity dictionary
    """
    return {
        'name': entity_name,
        'attributes': {},
        'relationships': {},
        'business_rules': {},
        'calculated_fields': {},
        'validations': {},
        'constraints': {},
        'attribute_metadata': {},
        'relationship_properties': {},
        'synthetic_data': [],
        'confidential_attributes': [],
        'internal_attributes': [],
        'public_attributes': [],
        'loading_strategies': {},
        'archive_strategy': {},
        'history_tracking': {},
        'workflow': {},
        'business_rule_placement': {},
        'workflow_placement': {},
        'entity_placement': {},
        'display_name': '',
        'type': '',
        'description': ''
    }

def parse_attributes(entity: Dict, attributes_text: str) -> None:
    """
    Parse attributes from attributes text.
    
    Args:
        entity: Entity dictionary
        attributes_text: Attributes text
    """
    attributes = attributes_text.split(',')
    for i, attr_text in enumerate(attributes):
        attr_text = attr_text.strip()
        
        # Extract attribute name and properties
        attr_match = re.match(r'(\w+)(\^PK|\^FK|\[derived\])?(\s+\(.*\))?', attr_text)
        if not attr_match:
            continue
        
        attr_name = attr_match.group(1)
        attr_type = attr_match.group(2) if attr_match.group(2) else ''
        attr_enum = attr_match.group(3) if attr_match.group(3) else ''
        
        # Create attribute dictionary
        attribute = {
            'name': attr_name,
            'primary_key': '^PK' in attr_type,
            'foreign_key': '^FK' in attr_type,
            'calculated': '[derived]' in attr_type,
            'data_type': 'string'  # Default data type
        }
        
        # Extract enum values if present
        if attr_enum:
            enum_values = re.findall(r'\w+', attr_enum)
            attribute['enum_values'] = enum_values
        
        # Add attribute to entity
        entity['attributes'][attr_name] = attribute

def parse_relationships(entity: Dict, entity_name: str, block: str) -> None:
    """
    Parse relationships from block.
    
    Args:
        entity: Entity dictionary
        entity_name: Entity name
        block: Entity block
    """
    relationships_match = re.search(r'Relationships for (\w+):(.*?)(?:\n\n|\Z)', block, re.DOTALL)
    if relationships_match and relationships_match.group(1) == entity_name:
        relationships_text = relationships_match.group(2)
        relationship_lines = relationships_text.strip().split('\n')
        
        for i, rel_line in enumerate(relationship_lines):
            rel_line = rel_line.strip()
            if not rel_line:
                continue
            
            # Extract relationship details
            rel_match = re.match(r'(\w+)\s+has\s+([\w-]+)\s+relationship\s+with\s+(\w+)\s+using\s+(\w+)\.(\w+)\s+to\s+(\w+)\.(\w+)', rel_line)
            if not rel_match:
                continue
            
            source_entity = rel_match.group(1)
            rel_type = rel_match.group(2)
            target_entity = rel_match.group(3)
            source_entity_attr = rel_match.group(4)
            source_attr = rel_match.group(5)
            target_entity_attr = rel_match.group(6)
            target_attr = rel_match.group(7)
            
            # Create relationship dictionary
            relationship = {
                'entity': target_entity,
                'type': rel_type,
                'source_attribute': source_attr,
                'target_attribute': target_attr
            }
            
            # Add relationship to entity
            rel_id = f"rel_{i+1}"
            entity['relationships'][rel_id] = relationship

def parse_defaults(entity: Dict, entity_name: str, block: str) -> None:
    """
    Parse defaults from block.
    
    Args:
        entity: Entity dictionary
        entity_name: Entity name
        block: Entity block
    """
    defaults_match = re.search(r'Defaults for (\w+):(.*?)(?:\n\n|\Z)', block, re.DOTALL)
    if defaults_match and defaults_match.group(1) == entity_name:
        defaults_text = defaults_match.group(2)
        default_lines = defaults_text.strip().split('\n')
        
        for default_line in default_lines:
            default_line = default_line.strip()
            if not default_line:
                continue
            
            # Extract default details
            default_match = re.match(r'(\w+)\.(\w+)\s+(\w+)\s+=\s+(.*?)(?:\s*$)', default_line)
            if not default_match:
                continue
            
            default_entity = default_match.group(1)
            default_attr = default_match.group(2)
            property_name = default_match.group(3)
            property_value = default_match.group(4)
            
            # Add default to attribute if it exists
            if default_attr in entity['attributes']:
                if property_name == 'DEFAULT_VALUE':
                    entity['attributes'][default_attr]['default_value'] = property_value.strip('"')
                else:
                    if 'properties' not in entity['attributes'][default_attr]:
                        entity['attributes'][default_attr]['properties'] = {}
                    entity['attributes'][default_attr]['properties'][property_name] = property_value.strip('"')

def parse_business_rules(entity: Dict, entity_name: str, block: str) -> None:
    """
    Parse business rules from block.
    
    Args:
        entity: Entity dictionary
        entity_name: Entity name
        block: Entity block
    """
    business_rules_match = re.search(r'BusinessRule for (\w+):(.*?)(?:\n\n|\Z)', block, re.DOTALL)
    if business_rules_match and business_rules_match.group(1) == entity_name:
        business_rules_text = business_rules_match.group(2)
        rule_blocks = re.split(r'\n\s*\n', business_rules_text)
        
        for rule_block in rule_blocks:
            rule_block = rule_block.strip()
            if not rule_block:
                continue
            
            # Extract rule name
            rule_name_match = re.match(r'(\w+)', rule_block)
            if not rule_name_match:
                continue
            
            rule_name = rule_name_match.group(1)
            
            # Create rule dictionary
            rule = {
                'name': rule_name,
                'inputs': [],
                'operation': '',
                'description': '',
                'outputs': [],
                'error': '',
                'validation': '',
                'trigger': ''
            }
            
            # Extract rule details
            inputs_match = re.search(r'Inputs:\s+(.*?)(?:\n|$)', rule_block)
            if inputs_match:
                inputs_text = inputs_match.group(1)
                rule['inputs'] = [input.strip() for input in inputs_text.split(';')]
            
            operation_match = re.search(r'Operation:\s+(.*?)(?:\n|$)', rule_block)
            if operation_match:
                rule['operation'] = operation_match.group(1)
            
            description_match = re.search(r'Description:\s+(.*?)(?:\n|$)', rule_block)
            if description_match:
                rule['description'] = description_match.group(1)
            
            output_match = re.search(r'Output:\s+(.*?)(?:\n|$)', rule_block)
            if output_match:
                outputs_text = output_match.group(1)
                rule['outputs'] = [output.strip() for output in outputs_text.split(';')]
            
            error_match = re.search(r'Error:\s+(.*?)(?:\n|$)', rule_block)
            if error_match:
                rule['error'] = error_match.group(1)
            
            validation_match = re.search(r'Validation:\s+(.*?)(?:\n|$)', rule_block)
            if validation_match:
                rule['validation'] = validation_match.group(1)
            
            trigger_match = re.search(r'Trigger:\s+(.*?)(?:\n|$)', rule_block)
            if trigger_match:
                rule['trigger'] = trigger_match.group(1)
            
            # Add rule to entity
            entity['business_rules'][rule_name] = rule

def parse_calculated_fields(entity: Dict, entity_name: str, block: str) -> None:
    """
    Parse calculated fields from block.
    
    Args:
        entity: Entity dictionary
        entity_name: Entity name
        block: Entity block
    """
    calculated_fields_match = re.findall(r'CalculatedField for (\w+)\.(\w+):(.*?)(?=CalculatedField|\n\n|\Z)', block, re.DOTALL)
    for calc_match in calculated_fields_match:
        calc_entity = calc_match[0]
        calc_attr = calc_match[1]
        calc_text = calc_match[2]
        
        if calc_entity != entity_name:
            continue
        
        # Create calculated field dictionary
        calc_field = {
            'attribute': calc_attr,
            'formula': '',
            'logic_layer': '',
            'caching': '',
            'dependencies': []
        }
        
        # Extract calculated field details
        formula_match = re.search(r'\*\s+Formula:\s+(.*?)(?:\n|$)', calc_text)
        if formula_match:
            calc_field['formula'] = formula_match.group(1)
        
        logic_layer_match = re.search(r'\*\s+Logic Layer:\s+(.*?)(?:\n|$)', calc_text)
        if logic_layer_match:
            calc_field['logic_layer'] = logic_layer_match.group(1)
        
        caching_match = re.search(r'\*\s+Caching:\s+(.*?)(?:\n|$)', calc_text)
        if caching_match:
            calc_field['caching'] = caching_match.group(1)
        
        dependencies_match = re.search(r'\*\s+Dependencies:\s+(.*?)(?:\n|$)', calc_text)
        if dependencies_match:
            dependencies_text = dependencies_match.group(1)
            calc_field['dependencies'] = [dep.strip() for dep in dependencies_text.split(',')]
        
        # Add calculated field to entity
        calc_id = f"calc_{calc_attr}"
        entity['calculated_fields'][calc_id] = calc_field

def parse_validations(entity: Dict, entity_name: str, block: str) -> None:
    """
    Parse validations from block.
    
    Args:
        entity: Entity dictionary
        entity_name: Entity name
        block: Entity block
    """
    validations_match = re.search(r'Validations for (\w+):(.*?)(?:\n\n|\Z)', block, re.DOTALL)
    if validations_match and validations_match.group(1) == entity_name:
        validations_text = validations_match.group(2)
        validation_lines = validations_text.strip().split('\n')
        
        for i, val_line in enumerate(validation_lines):
            val_line = val_line.strip()
            if not val_line:
                continue
            
            # Extract validation details
            val_match = re.match(r'(\w+)\.(\w+)\s+must\s+be\s+(.*?)(?:\.|$)', val_line)
            if not val_match:
                continue
            
            val_entity = val_match.group(1)
            val_attr = val_match.group(2)
            val_constraint = val_match.group(3)
            
            # Create validation dictionary
            validation = {
                'attribute': val_attr,
                'constraint': val_constraint
            }
            
            # Add validation to entity
            val_id = f"val_{i+1}"
            entity['validations'][val_id] = validation

def parse_constraints(entity: Dict, entity_name: str, block: str) -> None:
    """
    Parse constraints from block.
    
    Args:
        entity: Entity dictionary
        entity_name: Entity name
        block: Entity block
    """
    constraints_match = re.search(r'(\w+)\s+has\s+(.*?)\s+with\s+(\w+)\s+constrains\s+(\w+)', block)
    if constraints_match and constraints_match.group(1) == entity_name:
        constraint_entity = constraints_match.group(1)
        constraint_attrs = constraints_match.group(2)
        target_entity = constraints_match.group(3)
        constrained_entity = constraints_match.group(4)
        
        # Extract constraint attributes
        constraint_attr_list = re.findall(r'(\w+)(?:\^FK)?', constraint_attrs)
        
        # Create constraint dictionary
        constraint = {
            'target_entity': target_entity,
            'constrained_entity': constrained_entity,
            'foreign_key_attributes': constraint_attr_list
        }
        
        # Add constraint to entity
        constraint_id = f"constraint_{target_entity}_{constrained_entity}"
        entity['constraints'][constraint_id] = constraint

def parse_belongs_to_constraints(entity: Dict, entity_name: str, block: str) -> None:
    """
    Parse belongs_to constraints from block.
    
    Args:
        entity: Entity dictionary
        entity_name: Entity name
        block: Entity block
    """
    belongs_to_match = re.search(r'(\w+)\.(\w+)\s+must\s+belong\s+to\s+selected\s+(\w+)\.(\w+)', block)
    if belongs_to_match and belongs_to_match.group(1) == entity_name:
        belongs_entity = belongs_to_match.group(1)
        belongs_attr = belongs_to_match.group(2)
        selected_entity = belongs_to_match.group(3)
        selected_attr = belongs_to_match.group(4)
        
        # Create belongs_to constraint dictionary
        belongs_to = {
            'attribute': belongs_attr,
            'selected_entity': selected_entity,
            'selected_attribute': selected_attr
        }
        
        # Add belongs_to constraint to entity
        belongs_id = f"belongs_to_{belongs_attr}_{selected_attr}"
        entity['constraints'][belongs_id] = belongs_to

def parse_entity_properties(entity: Dict, block: str) -> None:
    """
    Parse entity additional properties from block.
    
    Args:
        entity: Entity dictionary
        block: Entity block
    """
    entity_props_match = re.search(r'Entity Additional Properties:(.*?)(?:\n\n|\Z)', block, re.DOTALL)
    if entity_props_match:
        props_text = entity_props_match.group(1)
        
        display_name_match = re.search(r'Display Name:\s+(.*?)(?:\n|$)', props_text)
        if display_name_match:
            entity['display_name'] = display_name_match.group(1)
        
        type_match = re.search(r'Type:\s+(.*?)(?:\n|$)', props_text)
        if type_match:
            entity['type'] = type_match.group(1)
        
        description_match = re.search(r'Description:\s+(.*?)(?:\n|$)', props_text)
        if description_match:
            entity['description'] = description_match.group(1)

def parse_attribute_properties(entity: Dict, block: str) -> None:
    """
    Parse attribute additional properties from block.
    
    Args:
        entity: Entity dictionary
        block: Entity block
    """
    attr_props_matches = re.findall(r'Attribute Additional Properties:(.*?)(?=Attribute Additional Properties:|\n\n|\Z)', block, re.DOTALL)
    for attr_props_text in attr_props_matches:
        attr_name_match = re.search(r'Attribute name:\s+(\w+)', attr_props_text)
        if not attr_name_match:
            continue
        
        attr_name = attr_name_match.group(1)
        
        # Create attribute metadata dictionary if it doesn't exist
        if attr_name not in entity['attribute_metadata']:
            entity['attribute_metadata'][attr_name] = {}
        
        # Extract attribute properties
        key_match = re.search(r'Key:\s+(.*?)(?:\n|$)', attr_props_text)
        if key_match:
            entity['attribute_metadata'][attr_name]['key_type'] = key_match.group(1)
        
        display_name_match = re.search(r'Display Name:\s+(.*?)(?:\n|$)', attr_props_text)
        if display_name_match:
            entity['attribute_metadata'][attr_name]['display_name'] = display_name_match.group(1)
        
        data_type_match = re.search(r'Data Type:\s+(.*?)(?:\n|$)', attr_props_text)
        if data_type_match:
            entity['attribute_metadata'][attr_name]['data_type'] = data_type_match.group(1)
            # Update the attribute's data_type as well
            if attr_name in entity['attributes']:
                entity['attributes'][attr_name]['data_type'] = data_type_match.group(1)
        
        type_match = re.search(r'Type:\s+(.*?)(?:\n|$)', attr_props_text)
        if type_match:
            entity['attribute_metadata'][attr_name]['type'] = type_match.group(1)
        
        format_match = re.search(r'Format:\s+(.*?)(?:\n|$)', attr_props_text)
        if format_match:
            entity['attribute_metadata'][attr_name]['format'] = format_match.group(1)
        
        values_match = re.search(r'Values:\s+(.*?)(?:\n|$)', attr_props_text)
        if values_match:
            entity['attribute_metadata'][attr_name]['values'] = values_match.group(1)
        
        default_match = re.search(r'Default:\s+(.*?)(?:\n|$)', attr_props_text)
        if default_match:
            entity['attribute_metadata'][attr_name]['default'] = default_match.group(1)
            # Update the attribute's default_value as well
            if attr_name in entity['attributes']:
                entity['attributes'][attr_name]['default_value'] = default_match.group(1).strip('"')
        
        validation_match = re.search(r'Validation:\s+(.*?)(?:\n|$)', attr_props_text)
        if validation_match:
            entity['attribute_metadata'][attr_name]['validation'] = validation_match.group(1)
        
        error_message_match = re.search(r'Error Message:\s+"(.*?)"(?:\n|$)', attr_props_text)
        if error_message_match:
            entity['attribute_metadata'][attr_name]['error_message'] = error_message_match.group(1)
        
        description_match = re.search(r'Description:\s+(.*?)(?:\n|$)', attr_props_text)
        if description_match:
            entity['attribute_metadata'][attr_name]['description'] = description_match.group(1)

def parse_relationship_properties(entity: Dict, entity_name: str, block: str) -> None:
    """
    Parse relationship properties from block.
    
    Args:
        entity: Entity dictionary
        entity_name: Entity name
        block: Entity block
    """
    rel_props_match = re.search(r'Relationship: (\w+) to (\w+)(.*?)(?:\n\n|\Z)', block, re.DOTALL)
    if rel_props_match and rel_props_match.group(1) == entity_name:
        source_entity = rel_props_match.group(1)
        target_entity = rel_props_match.group(2)
        props_text = rel_props_match.group(3)
        
        # Create relationship properties dictionary
        rel_props = {
            'source_entity': source_entity,
            'target_entity': target_entity,
            'on_delete': '',
            'on_update': '',
            'foreign_key_type': ''
        }
        
        # Extract relationship properties
        on_delete_match = re.search(r'On Delete:\s+(.*?)(?:\n|$)', props_text)
        if on_delete_match:
            rel_props['on_delete'] = on_delete_match.group(1)
        
        on_update_match = re.search(r'On Update:\s+(.*?)(?:\n|$)', props_text)
        if on_update_match:
            rel_props['on_update'] = on_update_match.group(1)
        
        fk_type_match = re.search(r'Foreign Key Type:\s+(.*?)(?:\n|$)', props_text)
        if fk_type_match:
            rel_props['foreign_key_type'] = fk_type_match.group(1)
        
        # Add relationship properties to entity
        rel_id = f"rel_props_{source_entity}_{target_entity}"
        entity['relationship_properties'][rel_id] = rel_props

def parse_synthetic_data(entity: Dict, entity_name: str, block: str) -> None:
    """
    Parse synthetic data from block.
    
    Args:
        entity: Entity dictionary
        entity_name: Entity name
        block: Entity block
    """
    synthetic_match = re.search(r'Synthetic:\s+(.*?)(?:\n\n|\Z)', block, re.DOTALL)
    if synthetic_match:
        synthetic_text = synthetic_match.group(1)
        synthetic_lines = synthetic_text.strip().split('.')
        
        for synthetic_line in synthetic_lines:
            synthetic_line = synthetic_line.strip()
            if not synthetic_line:
                continue
            
            # Extract synthetic data details
            synthetic_match = re.match(r'(\w+)\s+has\s+(.*?)(?:\s*$)', synthetic_line)
            if not synthetic_match:
                continue
            
            synthetic_entity = synthetic_match.group(1)
            if synthetic_entity != entity_name:
                continue
            
            synthetic_attrs = synthetic_match.group(2)
            
            # Extract attribute values
            attr_values = {}
            for attr_value in synthetic_attrs.split(','):
                attr_value = attr_value.strip()
                if not attr_value:
                    continue
                
                # Extract attribute name and value
                attr_value_match = re.match(r'(\w+)\s+=\s+(.*?)(?:\s*$)', attr_value)
                if not attr_value_match:
                    continue
                
                attr_name = attr_value_match.group(1)
                attr_value = attr_value_match.group(2)
                
                attr_values[attr_name] = attr_value
            
            # Add synthetic data to entity
            entity['synthetic_data'].append(attr_values)

def parse_confidential_attributes(entity: Dict, entity_name: str, block: str) -> None:
    """
    Parse confidential attributes from block.
    
    Args:
        entity: Entity dictionary
        entity_name: Entity name
        block: Entity block
    """
    confidential_match = re.search(r'Confidential:\s+(.*?)(?:\n|$)', block)
    if confidential_match:
        confidential_text = confidential_match.group(1)
        confidential_attrs = confidential_text.split(',')
        
        for attr in confidential_attrs:
            attr = attr.strip()
            if not attr:
                continue
            
            # Extract attribute name
            attr_match = re.match(r'(\w+)\.(\w+)', attr)
            if not attr_match:
                continue
            
            attr_entity = attr_match.group(1)
            attr_name = attr_match.group(2)
            
            if attr_entity == entity_name:
                entity['confidential_attributes'].append(attr_name)

def parse_internal_attributes(entity: Dict, entity_name: str, block: str) -> None:
    """
    Parse internal attributes from block.
    
    Args:
        entity: Entity dictionary
        entity_name: Entity name
        block: Entity block
    """
    internal_match = re.search(r'Internal:\s+(.*?)(?:\n|$)', block)
    if internal_match:
        internal_text = internal_match.group(1)
        internal_attrs = internal_text.split(',')
        
        for attr in internal_attrs:
            attr = attr.strip()
            if not attr:
                continue
            
            # Extract attribute name
            attr_match = re.match(r'(\w+)\.(\w+)', attr)
            if not attr_match:
                continue
            
            attr_entity = attr_match.group(1)
            attr_name = attr_match.group(2)
            
            if attr_entity == entity_name:
                entity['internal_attributes'].append(attr_name)

def parse_public_attributes(entity: Dict, entity_name: str, block: str) -> None:
    """
    Parse public attributes from block.
    
    Args:
        entity: Entity dictionary
        entity_name: Entity name
        block: Entity block
    """
    public_match = re.search(r'Public:\s+(.*?)(?:\n|$)', block)
    if public_match:
        public_text = public_match.group(1)
        public_attrs = public_text.split(',')
        
        for attr in public_attrs:
            attr = attr.strip()
            if not attr:
                continue
            
            # Extract attribute name
            attr_match = re.match(r'(\w+)\.(\w+)', attr)
            if not attr_match:
                continue
            
            attr_entity = attr_match.group(1)
            attr_name = attr_match.group(2)
            
            if attr_entity == entity_name:
                entity['public_attributes'].append(attr_name)
