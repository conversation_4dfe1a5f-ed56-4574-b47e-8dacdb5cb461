"""
LO Deployer for YAML Builder v2

This module provides functionality for deploying parsed Local Objective (LO) data to the database.
"""

import os
import sys
import logging
from typing import Dict, List, Tuple, Any, Optional

# Import database utilities
from db_utils import execute_query, create_table, add_column, add_constraint

# Set up logging
logger = logging.getLogger('lo_deployer')

def deploy_lo_definitions(lo_data: Dict, schema_name: str) -> Tuple[bool, List[str]]:
    """
    Deploy parsed LO data to the database.
    
    Args:
        lo_data: Parsed LO data
        schema_name: Schema name to deploy to
        
    Returns:
        Tuple containing:
            - Boolean indicating if deployment was successful
            - List of messages (warnings, errors, or success messages)
    """
    messages = []
    
    try:
        logger.info(f"Deploying local objectives to schema {schema_name}")
        
        # Check if LO data is valid
        if not lo_data or 'local_objectives' not in lo_data:
            logger.error("Invalid LO data: 'local_objectives' key not found")
            return False, ["Invalid LO data: 'local_objectives' key not found"]
        
        local_objectives = lo_data['local_objectives']
        
        # Create local_objectives table if it doesn't exist
        success, create_messages = create_local_objectives_table(schema_name)
        messages.extend(create_messages)
        
        if not success:
            return False, messages
        
        # Create lo_input_stack table if it doesn't exist
        success, create_messages = create_lo_input_stack_table(schema_name)
        messages.extend(create_messages)
        
        if not success:
            return False, messages
        
        # Create lo_output_stack table if it doesn't exist
        success, create_messages = create_lo_output_stack_table(schema_name)
        messages.extend(create_messages)
        
        if not success:
            return False, messages
        
        # Create execution_pathways table if it doesn't exist
        success, create_messages = create_execution_pathways_table(schema_name)
        messages.extend(create_messages)
        
        if not success:
            return False, messages
        
        # Create execution_pathway_conditions table if it doesn't exist
        success, create_messages = create_execution_pathway_conditions_table(schema_name)
        messages.extend(create_messages)
        
        if not success:
            return False, messages
        
        # Create nested_functions table if it doesn't exist
        success, create_messages = create_nested_functions_table(schema_name)
        messages.extend(create_messages)
        
        if not success:
            return False, messages
        
        # Create system_functions table if it doesn't exist
        success, create_messages = create_system_functions_table(schema_name)
        messages.extend(create_messages)
        
        if not success:
            return False, messages
        
        # Create input_validations table if it doesn't exist
        success, create_messages = create_input_validations_table(schema_name)
        messages.extend(create_messages)
        
        if not success:
            return False, messages
        
        # Deploy each local objective
        for lo_name, lo in local_objectives.items():
            success, deploy_messages = deploy_local_objective(lo, schema_name)
            messages.extend(deploy_messages)
            
            if not success:
                return False, messages
        
        messages.append(f"Successfully deployed {len(local_objectives)} local objectives to schema {schema_name}")
        return True, messages
    except Exception as e:
        logger.error(f"Error deploying local objectives to schema {schema_name}: {str(e)}", exc_info=True)
        messages.append(f"Error deploying local objectives to schema {schema_name}: {str(e)}")
        return False, messages

def create_local_objectives_table(schema_name: str) -> Tuple[bool, List[str]]:
    """
    Create local_objectives table if it doesn't exist.
    
    Args:
        schema_name: Schema name to deploy to
        
    Returns:
        Tuple containing:
            - Boolean indicating if creation was successful
            - List of messages (warnings, errors, or success messages)
    """
    return create_table(
        schema_name,
        'local_objectives',
        """
        lo_name VARCHAR(255) PRIMARY KEY,
        function_type VARCHAR(50) NOT NULL,
        description TEXT,
        version VARCHAR(50) NOT NULL,
        status VARCHAR(50) NOT NULL,
        go_id VARCHAR(255),
        workflow_source VARCHAR(255),
        agent_type VARCHAR(50),
        tenant_name VARCHAR(255),
        book_name VARCHAR(255),
        chapter_name VARCHAR(255),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        CONSTRAINT local_objectives_go_id_fkey FOREIGN KEY (go_id) REFERENCES {schema_name}.global_objectives (go_id) ON DELETE SET NULL
        """.format(schema_name=schema_name)
    )

def create_lo_input_stack_table(schema_name: str) -> Tuple[bool, List[str]]:
    """
    Create lo_input_stack table if it doesn't exist.
    
    Args:
        schema_name: Schema name to deploy to
        
    Returns:
        Tuple containing:
            - Boolean indicating if creation was successful
            - List of messages (warnings, errors, or success messages)
    """
    return create_table(
        schema_name,
        'lo_input_stack',
        """
        input_id SERIAL PRIMARY KEY,
        lo_name VARCHAR(255) NOT NULL,
        slot_id VARCHAR(50) NOT NULL,
        entity_reference VARCHAR(255) NOT NULL,
        attribute_reference VARCHAR(255),
        data_type VARCHAR(50),
        ui_control VARCHAR(50),
        system_function VARCHAR(255),
        required BOOLEAN DEFAULT FALSE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        CONSTRAINT lo_input_stack_lo_name_fkey FOREIGN KEY (lo_name) REFERENCES {schema_name}.local_objectives (lo_name) ON DELETE CASCADE,
        CONSTRAINT lo_input_stack_lo_name_slot_id_unique UNIQUE (lo_name, slot_id)
        """.format(schema_name=schema_name)
    )

def create_lo_output_stack_table(schema_name: str) -> Tuple[bool, List[str]]:
    """
    Create lo_output_stack table if it doesn't exist.
    
    Args:
        schema_name: Schema name to deploy to
        
    Returns:
        Tuple containing:
            - Boolean indicating if creation was successful
            - List of messages (warnings, errors, or success messages)
    """
    return create_table(
        schema_name,
        'lo_output_stack',
        """
        output_id SERIAL PRIMARY KEY,
        lo_name VARCHAR(255) NOT NULL,
        slot_id VARCHAR(50) NOT NULL,
        output_entity VARCHAR(255) NOT NULL,
        output_attribute VARCHAR(255),
        data_type VARCHAR(50),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        CONSTRAINT lo_output_stack_lo_name_fkey FOREIGN KEY (lo_name) REFERENCES {schema_name}.local_objectives (lo_name) ON DELETE CASCADE,
        CONSTRAINT lo_output_stack_lo_name_slot_id_unique UNIQUE (lo_name, slot_id)
        """.format(schema_name=schema_name)
    )

def create_execution_pathways_table(schema_name: str) -> Tuple[bool, List[str]]:
    """
    Create execution_pathways table if it doesn't exist.
    
    Args:
        schema_name: Schema name to deploy to
        
    Returns:
        Tuple containing:
            - Boolean indicating if creation was successful
            - List of messages (warnings, errors, or success messages)
    """
    return create_table(
        schema_name,
        'execution_pathways',
        """
        pathway_id SERIAL PRIMARY KEY,
        lo_name VARCHAR(255) NOT NULL,
        current_lo VARCHAR(255) NOT NULL,
        next_lo VARCHAR(255) NOT NULL,
        pathway_type VARCHAR(50) NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        CONSTRAINT execution_pathways_lo_name_fkey FOREIGN KEY (lo_name) REFERENCES {schema_name}.local_objectives (lo_name) ON DELETE CASCADE
        """.format(schema_name=schema_name)
    )

def create_execution_pathway_conditions_table(schema_name: str) -> Tuple[bool, List[str]]:
    """
    Create execution_pathway_conditions table if it doesn't exist.
    
    Args:
        schema_name: Schema name to deploy to
        
    Returns:
        Tuple containing:
            - Boolean indicating if creation was successful
            - List of messages (warnings, errors, or success messages)
    """
    return create_table(
        schema_name,
        'execution_pathway_conditions',
        """
        condition_id SERIAL PRIMARY KEY,
        lo_name VARCHAR(255) NOT NULL,
        condition TEXT NOT NULL,
        condition_entity VARCHAR(255),
        condition_attribute VARCHAR(255),
        condition_operator VARCHAR(50),
        condition_value TEXT,
        next_lo VARCHAR(255) NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        CONSTRAINT execution_pathway_conditions_lo_name_fkey FOREIGN KEY (lo_name) REFERENCES {schema_name}.local_objectives (lo_name) ON DELETE CASCADE
        """.format(schema_name=schema_name)
    )

def create_nested_functions_table(schema_name: str) -> Tuple[bool, List[str]]:
    """
    Create nested_functions table if it doesn't exist.
    
    Args:
        schema_name: Schema name to deploy to
        
    Returns:
        Tuple containing:
            - Boolean indicating if creation was successful
            - List of messages (warnings, errors, or success messages)
    """
    return create_table(
        schema_name,
        'nested_functions',
        """
        function_id SERIAL PRIMARY KEY,
        lo_name VARCHAR(255) NOT NULL,
        function_name VARCHAR(255) NOT NULL,
        parameters TEXT,
        writes_to VARCHAR(255) NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        CONSTRAINT nested_functions_lo_name_fkey FOREIGN KEY (lo_name) REFERENCES {schema_name}.local_objectives (lo_name) ON DELETE CASCADE
        """.format(schema_name=schema_name)
    )

def create_system_functions_table(schema_name: str) -> Tuple[bool, List[str]]:
    """
    Create system_functions table if it doesn't exist.
    
    Args:
        schema_name: Schema name to deploy to
        
    Returns:
        Tuple containing:
            - Boolean indicating if creation was successful
            - List of messages (warnings, errors, or success messages)
    """
    return create_table(
        schema_name,
        'system_functions',
        """
        function_id VARCHAR(255) PRIMARY KEY,
        lo_name VARCHAR(255) NOT NULL,
        function_name VARCHAR(255) NOT NULL,
        parameters TEXT,
        output_to VARCHAR(255) NOT NULL,
        function_type VARCHAR(50) NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        CONSTRAINT system_functions_lo_name_fkey FOREIGN KEY (lo_name) REFERENCES {schema_name}.local_objectives (lo_name) ON DELETE CASCADE
        """.format(schema_name=schema_name)
    )

def create_input_validations_table(schema_name: str) -> Tuple[bool, List[str]]:
    """
    Create input_validations table if it doesn't exist.
    
    Args:
        schema_name: Schema name to deploy to
        
    Returns:
        Tuple containing:
            - Boolean indicating if creation was successful
            - List of messages (warnings, errors, or success messages)
    """
    return create_table(
        schema_name,
        'input_validations',
        """
        validation_id SERIAL PRIMARY KEY,
        lo_name VARCHAR(255) NOT NULL,
        entity VARCHAR(255) NOT NULL,
        attribute VARCHAR(255) NOT NULL,
        rule TEXT NOT NULL,
        rule_type VARCHAR(50) NOT NULL,
        error_message TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        CONSTRAINT input_validations_lo_name_fkey FOREIGN KEY (lo_name) REFERENCES {schema_name}.local_objectives (lo_name) ON DELETE CASCADE
        """.format(schema_name=schema_name)
    )

def deploy_local_objective(lo: Dict, schema_name: str) -> Tuple[bool, List[str]]:
    """
    Deploy a local objective to the database.
    
    Args:
        lo: Local objective data
        schema_name: Schema name to deploy to
        
    Returns:
        Tuple containing:
            - Boolean indicating if deployment was successful
            - List of messages (warnings, errors, or success messages)
    """
    messages = []
    
    try:
        lo_name = lo.get('lo_name')
        logger.info(f"Deploying local objective {lo_name} to schema {schema_name}")
        
        # Insert local objective
        success, query_messages = insert_local_objective(lo, schema_name)
        messages.extend(query_messages)
        
        if not success:
            return False, messages
        
        # Insert input stack
        if 'input_stack' in lo:
            for input_item in lo['input_stack']:
                success, query_messages = insert_lo_input_stack_item(lo_name, input_item, schema_name)
                messages.extend(query_messages)
                
                if not success:
                    return False, messages
        
        # Insert output stack
        if 'output_stack' in lo:
            for output_item in lo['output_stack']:
                success, query_messages = insert_lo_output_stack_item(lo_name, output_item, schema_name)
                messages.extend(query_messages)
                
                if not success:
                    return False, messages
        
        # Insert execution pathways
        if 'execution_pathways' in lo:
            for pathway in lo['execution_pathways']:
                success, query_messages = insert_execution_pathway(lo_name, pathway, schema_name)
                messages.extend(query_messages)
                
                if not success:
                    return False, messages
        
        # Insert execution pathway conditions
        if 'execution_pathway_conditions' in lo:
            for condition in lo['execution_pathway_conditions']:
                success, query_messages = insert_execution_pathway_condition(lo_name, condition, schema_name)
                messages.extend(query_messages)
                
                if not success:
                    return False, messages
        
        # Insert nested functions
        if 'nested_functions' in lo:
            for function in lo['nested_functions']:
                success, query_messages = insert_nested_function(lo_name, function, schema_name)
                messages.extend(query_messages)
                
                if not success:
                    return False, messages
        
        # Insert system functions
        if 'system_functions' in lo:
            for function in lo['system_functions']:
                success, query_messages = insert_system_function(lo_name, function, schema_name)
                messages.extend(query_messages)
                
                if not success:
                    return False, messages
        
        # Insert input validations
        if 'input_validations' in lo:
            for validation in lo['input_validations']:
                success, query_messages = insert_input_validation(lo_name, validation, schema_name)
                messages.extend(query_messages)
                
                if not success:
                    return False, messages
        
        messages.append(f"Successfully deployed local objective {lo_name} to schema {schema_name}")
        return True, messages
    except Exception as e:
        logger.error(f"Error deploying local objective {lo.get('lo_name')} to schema {schema_name}: {str(e)}", exc_info=True)
        messages.append(f"Error deploying local objective {lo.get('lo_name')} to schema {schema_name}: {str(e)}")
        return False, messages

def insert_local_objective(lo: Dict, schema_name: str) -> Tuple[bool, List[str]]:
    """
    Insert a local objective into the database.
    
    Args:
        lo: Local objective data
        schema_name: Schema name to deploy to
        
    Returns:
        Tuple containing:
            - Boolean indicating if insertion was successful
            - List of messages (warnings, errors, or success messages)
    """
    messages = []
    
    try:
        lo_name = lo.get('lo_name')
        function_type = lo.get('function_type', 'standard')
        description = lo.get('description', '')
        version = lo.get('version', '1.0')
        status = lo.get('status', 'Active')
        go_id = lo.get('go_id', None)
        workflow_source = lo.get('workflow_source', '')
        agent_type = lo.get('agent_type', '')
        tenant_name = lo.get('tenant_name', '')
        book_name = lo.get('book_name', '')
        chapter_name = lo.get('chapter_name', '')
        
        # Check if local objective already exists
        success, query_messages, result = execute_query(
            f"""
            SELECT lo_name FROM {schema_name}.local_objectives
            WHERE lo_name = %s
            """,
            (lo_name,)
        )
        
        if not success:
            messages.extend(query_messages)
            return False, messages
        
        if result:
            # Local objective already exists, update it
            success, query_messages, _ = execute_query(
                f"""
                UPDATE {schema_name}.local_objectives
                SET function_type = %s, description = %s, version = %s, status = %s,
                    go_id = %s, workflow_source = %s, agent_type = %s,
                    tenant_name = %s, book_name = %s, chapter_name = %s,
                    updated_at = CURRENT_TIMESTAMP
                WHERE lo_name = %s
                """,
                (function_type, description, version, status, go_id, workflow_source, agent_type,
                 tenant_name, book_name, chapter_name, lo_name)
            )
            
            if not success:
                messages.extend(query_messages)
                return False, messages
            
            messages.append(f"Updated local objective {lo_name} in schema {schema_name}")
        else:
            # Local objective doesn't exist, insert it
            success, query_messages, _ = execute_query(
                f"""
                INSERT INTO {schema_name}.local_objectives
                (lo_name, function_type, description, version, status, go_id, workflow_source, agent_type,
                 tenant_name, book_name, chapter_name)
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                """,
                (lo_name, function_type, description, version, status, go_id, workflow_source, agent_type,
                 tenant_name, book_name, chapter_name)
            )
            
            if not success:
                messages.extend(query_messages)
                return False, messages
            
            messages.append(f"Inserted local objective {lo_name} into schema {schema_name}")
        
        return True, messages
    except Exception as e:
        logger.error(f"Error inserting local objective {lo.get('lo_name')} into schema {schema_name}: {str(e)}", exc_info=True)
        messages.append(f"Error inserting local objective {lo.get('lo_name')} into schema {schema_name}: {str(e)}")
        return False, messages

def insert_lo_input_stack_item(lo_name: str, input_item: Dict, schema_name: str) -> Tuple[bool, List[str]]:
    """
    Insert an input stack item into the database.
    
    Args:
        lo_name: Local objective name
        input_item: Input stack item data
        schema_name: Schema name to deploy to
        
    Returns:
        Tuple containing:
            - Boolean indicating if insertion was successful
            - List of messages (warnings, errors, or success messages)
    """
    messages = []
    
    try:
        slot_id = input_item.get('slot_id')
        entity_reference = input_item.get('entity_reference')
        attribute_reference = input_item.get('attribute_reference', None)
        data_type = input_item.get('data_type', None)
        ui_control = input_item.get('ui_control', None)
        system_function = input_item.get('system_function', None)
        required = input_item.get('required', False)
        
        # Check if input stack item already exists
        success, query_messages, result = execute_query(
            f"""
            SELECT input_id FROM {schema_name}.lo_input_stack
            WHERE lo_name = %s AND slot_id = %s
            """,
            (lo_name, slot_id)
        )
        
        if not success:
            messages.extend(query_messages)
            return False, messages
        
        if result:
            # Input stack item already exists, update it
            success, query_messages, _ = execute_query(
                f"""
                UPDATE {schema_name}.lo_input_stack
                SET entity_reference = %s, attribute_reference = %s, data_type = %s,
                    ui_control = %s, system_function = %s, required = %s,
                    updated_at = CURRENT_TIMESTAMP
                WHERE lo_name = %s AND slot_id = %s
                """,
                (entity_reference, attribute_reference, data_type, ui_control, system_function, required, lo_name, slot_id)
            )
            
            if not success:
                messages.extend(query_messages)
                return False, messages
            
            messages.append(f"Updated input stack item {slot_id} for local objective {lo_name} in schema {schema_name}")
        else:
            # Input stack item doesn't exist, insert it
            success, query_messages, _ = execute_query(
                f"""
                INSERT INTO {schema_name}.lo_input_stack
                (lo_name, slot_id, entity_reference, attribute_reference, data_type, ui_control, system_function, required)
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
                """,
                (lo_name, slot_id, entity_reference, attribute_reference, data_type, ui_control, system_function, required)
            )
            
            if not success:
                messages.extend(query_messages)
                return False, messages
            
            messages.append(f"Inserted input stack item {slot_id} for local objective {lo_name} into schema {schema_name}")
        
        return True, messages
    except Exception as e:
        logger.error(f"Error inserting input stack item for local objective {lo_name} into schema {schema_name}: {str(e)}", exc_info=True)
        messages.append(f"Error inserting input stack item for local objective {lo_name} into schema {schema_name}: {str(e)}")
        return False, messages

def insert_lo_output_stack_item(lo_name: str, output_item: Dict, schema_name: str) -> Tuple[bool, List[str]]:
    """
    Insert an output stack item into the database.
    
    Args:
        lo_name: Local objective name
        output_item: Output stack item data
        schema_name: Schema name to deploy to
        
    Returns:
        Tuple containing:
            - Boolean indicating if insertion was successful
            - List of messages (warnings, errors, or success messages)
    """
    messages = []
    
    try:
        slot_id = output_item.get('slot_id')
        output_entity = output_item.get('output_entity')
        output_attribute = output_item.get('output_attribute', None)
        data_type = output_item.get('data_type', None)
        
        # Check if output stack item already exists
        success, query_messages, result = execute_query(
            f"""
            SELECT output_id FROM {schema_name}.lo_output_stack
            WHERE lo_name = %s AND slot_id = %s
            """,
            (lo_name, slot_id)
        )
        
        if not success:
            messages.extend(query_messages)
            return False, messages
        
        if result:
            # Output stack item already exists, update it
            success, query_messages, _ = execute_query(
                f"""
                UPDATE {schema_name}.lo_output_stack
                SET output_entity = %s, output_attribute = %s, data_type = %s, updated_at = CURRENT_TIMESTAMP
                WHERE lo_name = %s AND slot_id = %s
                """,
                (output_entity, output_attribute, data_type, lo_name, slot_id)
            )
            
            if not success:
                messages.extend(query_messages)
                return False, messages
            
            messages.append(f"Updated output stack item {slot_id} for local objective {lo_name} in schema {schema_name}")
        else:
            # Output stack item doesn't exist, insert it
            success, query_messages, _ = execute_query(
                f"""
                INSERT INTO {schema_name}.lo_output_stack
                (lo_name, slot_id, output_entity, output_attribute, data_type)
                VALUES (%s, %s, %s, %s, %s)
                """,
                (lo_name, slot_id, output_entity, output_attribute, data_type)
            )
            
            if not success:
                messages.extend(query_messages)
                return False, messages
            
            messages.append(f"Inserted output stack item {slot_id} for local objective {lo_name} into schema {schema_name}")
        
        return True, messages
    except Exception as e:
        logger.error(f"Error inserting output stack item for local objective {lo_name} into schema {schema_name}: {str(e)}", exc_info=True)
        messages.append(f"Error inserting output stack item for local objective {lo_name} into schema {schema_name}: {str(e)}")
        return False, messages

def insert_execution_pathway(lo_name: str, pathway: Dict, schema_name: str) -> Tuple[bool, List[str]]:
    """
    Insert an execution pathway into the database.
    
    Args:
        lo_name: Local objective name
        pathway: Execution pathway data
        schema_name: Schema name to deploy to
        
    Returns:
        Tuple containing:
            - Boolean indicating if insertion was successful
            - List of messages (warnings, errors, or success messages)
    """
    messages = []
    
    try:
        current_lo = pathway.get('current_lo')
        next_lo = pathway.get('next_lo')
        pathway_type = pathway.get('pathway_type', 'unconditional')
        
        # Check if execution pathway already exists
        success, query_messages, result = execute_query(
            f"""
            SELECT pathway_id FROM {schema_name}.execution_pathways
            WHERE lo_name = %s AND current_lo = %s AND next_lo = %s
            """,
            (lo_name, current_lo, next_lo)
        )
        
        if not success:
            messages.extend(query_messages)
            return False, messages
        
        if result:
            # Execution pathway already exists, update it
            pathway_id = result[0][0]
            
            success, query_messages, _ = execute_query(
                f"""
                UPDATE {schema_name}.execution_pathways
                SET pathway_type = %s, updated_at = CURRENT_TIMESTAMP
                WHERE pathway_id = %s
                """,
                (pathway_type, pathway_id)
            )
            
            if not success:
                messages.extend(query_messages)
                return False, messages
            
            messages.append(f"Updated execution pathway for local objective {lo_name} in schema {schema_name}")
        else:
            # Execution pathway doesn't exist, insert it
            success, query_messages, _ = execute_query(
                f"""
                INSERT INTO {schema_name}.execution_pathways
                (lo_name, current_lo, next_lo, pathway_type)
                VALUES (%s, %s, %s, %s)
                """,
                (lo_name, current_lo, next_lo, pathway_type)
            )
            
            if not success:
                messages.extend(query_messages)
                return False, messages
            
            messages.append(f"Inserted execution pathway for local objective {lo_name} into schema {schema_name}")
        
        return True, messages
    except Exception as e:
        logger.error(f"Error inserting execution pathway for local objective {lo_name} into schema {schema_name}: {str(e)}", exc_info=True)
        messages.append(f"Error inserting execution pathway for local objective {lo_name} into schema {schema_name}: {str(e)}")
        return False, messages

def insert_execution_pathway_condition(lo_name: str, condition: Dict, schema_name: str) -> Tuple[bool, List[str]]:
    """
    Insert an execution pathway condition into the database.
    
    Args:
        lo_name: Local objective name
        condition: Execution pathway condition data
        schema_name: Schema name to deploy to
        
    Returns:
        Tuple containing:
            - Boolean indicating if insertion was successful
            - List of messages (warnings, errors, or success messages)
    """
    messages = []
    
    try:
        condition_text = condition.get('condition', '')
        condition_entity = condition.get('condition_entity', None)
        condition_attribute = condition.get('condition_attribute', None)
        condition_operator = condition.get('condition_operator', None)
        condition_value = condition.get('condition_value', None)
        next_lo = condition.get('next_lo')
        
        # Check if execution pathway condition already exists
        success, query_messages, result = execute_query(
            f"""
            SELECT condition_id FROM {schema_name}.execution_pathway_conditions
            WHERE lo_name = %s AND condition = %s AND next_lo = %s
            """,
            (lo_name, condition_text, next_lo)
        )
        
        if not success:
            messages.extend(query_messages)
            return False, messages
        
        if result:
            # Execution pathway condition already exists, update it
            condition_id = result[0][0]
            
            success, query_messages, _ = execute_query(
                f"""
                UPDATE {schema_name}.execution_pathway_conditions
                SET condition_entity = %s, condition_attribute = %s, condition_operator = %s, condition_value = %s,
                    updated_at = CURRENT_TIMESTAMP
                WHERE condition_id = %s
                """,
                (condition_entity, condition_attribute, condition_operator, condition_value, condition_id)
            )
            
            if not success:
                messages.extend(query_messages)
                return False, messages
            
            messages.append(f"Updated execution pathway condition for local objective {lo_name} in schema {schema_name}")
        else:
            # Execution pathway condition doesn't exist, insert it
            success, query_messages, _ = execute_query(
                f"""
                INSERT INTO {schema_name}.execution_pathway_conditions
                (lo_name, condition, condition_entity, condition_attribute, condition_operator, condition_value, next_lo)
                VALUES (%s, %s, %s, %s, %s, %s, %s)
                """,
                (lo_name, condition_text, condition_entity, condition_attribute, condition_operator, condition_value, next_lo)
            )
            
            if not success:
                messages.extend(query_messages)
                return False, messages
            
            messages.append(f"Inserted execution pathway condition for local objective {lo_name} into schema {schema_name}")
        
        return True, messages
    except Exception as e:
        logger.error(f"Error inserting execution pathway condition for local objective {lo_name} into schema {schema_name}: {str(e)}", exc_info=True)
        messages.append(f"Error inserting execution pathway condition for local objective {lo_name} into schema {schema_name}: {str(e)}")
        return False, messages

def insert_nested_function(lo_name: str, function: Dict, schema_name: str) -> Tuple[bool, List[str]]:
    """
    Insert a nested function into the database.
    
    Args:
        lo_name: Local objective name
        function: Nested function data
        schema_name: Schema name to deploy to
        
    Returns:
        Tuple containing:
            - Boolean indicating if insertion was successful
            - List of messages (warnings, errors, or success messages)
    """
    messages = []
    
    try:
        function_name = function.get('function_name')
        parameters = ','.join(function.get('parameters', []))
        writes_to = function.get('writes_to')
        
        # Check if nested function already exists
        success, query_messages, result = execute_query(
            f"""
            SELECT function_id FROM {schema_name}.nested_functions
            WHERE lo_name = %s AND function_name = %s
            """,
            (lo_name, function_name)
        )
        
        if not success:
            messages.extend(query_messages)
            return False, messages
        
        if result:
            # Nested function already exists, update it
            function_id = result[0][0]
            
            success, query_messages, _ = execute_query(
                f"""
                UPDATE {schema_name}.nested_functions
                SET parameters = %s, writes_to = %s, updated_at = CURRENT_TIMESTAMP
                WHERE function_id = %s
                """,
                (parameters, writes_to, function_id)
            )
            
            if not success:
                messages.extend(query_messages)
                return False, messages
            
            messages.append(f"Updated nested function {function_name} for local objective {lo_name} in schema {schema_name}")
        else:
            # Nested function doesn't exist, insert it
            success, query_messages, _ = execute_query(
                f"""
                INSERT INTO {schema_name}.nested_functions
                (lo_name, function_name, parameters, writes_to)
                VALUES (%s, %s, %s, %s)
                """,
                (lo_name, function_name, parameters, writes_to)
            )
            
            if not success:
                messages.extend(query_messages)
                return False, messages
            
            messages.append(f"Inserted nested function {function_name} for local objective {lo_name} into schema {schema_name}")
        
        return True, messages
    except Exception as e:
        logger.error(f"Error inserting nested function for local objective {lo_name} into schema {schema_name}: {str(e)}", exc_info=True)
        messages.append(f"Error inserting nested function for local objective {lo_name} into schema {schema_name}: {str(e)}")
        return False, messages

def insert_system_function(lo_name: str, function: Dict, schema_name: str) -> Tuple[bool, List[str]]:
    """
    Insert a system function into the database.
    
    Args:
        lo_name: Local objective name
        function: System function data
        schema_name: Schema name to deploy to
        
    Returns:
        Tuple containing:
            - Boolean indicating if insertion was successful
            - List of messages (warnings, errors, or success messages)
    """
    messages = []
    
    try:
        function_id = function.get('function_id')
        function_name = function.get('function_name')
        parameters = ','.join(function.get('parameters', []))
        output_to = function.get('output_to')
        function_type = function.get('function_type', 'standard')
        
        # Check if system function already exists
        success, query_messages, result = execute_query(
            f"""
            SELECT function_id FROM {schema_name}.system_functions
            WHERE function_id = %s
            """,
            (function_id,)
        )
        
        if not success:
            messages.extend(query_messages)
            return False, messages
        
        if result:
            # System function already exists, update it
            success, query_messages, _ = execute_query(
                f"""
                UPDATE {schema_name}.system_functions
                SET function_name = %s, parameters = %s, output_to = %s, function_type = %s, updated_at = CURRENT_TIMESTAMP
                WHERE function_id = %s
                """,
                (function_name, parameters, output_to, function_type, function_id)
            )
            
            if not success:
                messages.extend(query_messages)
                return False, messages
            
            messages.append(f"Updated system function {function_name} for local objective {lo_name} in schema {schema_name}")
        else:
            # System function doesn't exist, insert it
            success, query_messages, _ = execute_query(
                f"""
                INSERT INTO {schema_name}.system_functions
                (function_id, lo_name, function_name, parameters, output_to, function_type)
                VALUES (%s, %s, %s, %s, %s, %s)
                """,
                (function_id, lo_name, function_name, parameters, output_to, function_type)
            )
            
            if not success:
                messages.extend(query_messages)
                return False, messages
            
            messages.append(f"Inserted system function {function_name} for local objective {lo_name} into schema {schema_name}")
        
        return True, messages
    except Exception as e:
        logger.error(f"Error inserting system function for local objective {lo_name} into schema {schema_name}: {str(e)}", exc_info=True)
        messages.append(f"Error inserting system function for local objective {lo_name} into schema {schema_name}: {str(e)}")
        return False, messages

def insert_input_validation(lo_name: str, validation: Dict, schema_name: str) -> Tuple[bool, List[str]]:
    """
    Insert an input validation into the database.
    
    Args:
        lo_name: Local objective name
        validation: Input validation data
        schema_name: Schema name to deploy to
        
    Returns:
        Tuple containing:
            - Boolean indicating if insertion was successful
            - List of messages (warnings, errors, or success messages)
    """
    messages = []
    
    try:
        entity = validation.get('entity')
        attribute = validation.get('attribute')
        rule = validation.get('rule')
        rule_type = validation.get('rule_type', 'custom')
        error_message = validation.get('error_message', '')
        
        # Check if input validation already exists
        success, query_messages, result = execute_query(
            f"""
            SELECT validation_id FROM {schema_name}.input_validations
            WHERE lo_name = %s AND entity = %s AND attribute = %s AND rule_type = %s
            """,
            (lo_name, entity, attribute, rule_type)
        )
        
        if not success:
            messages.extend(query_messages)
            return False, messages
        
        if result:
            # Input validation already exists, update it
            validation_id = result[0][0]
            
            success, query_messages, _ = execute_query(
                f"""
                UPDATE {schema_name}.input_validations
                SET rule = %s, error_message = %s, updated_at = CURRENT_TIMESTAMP
                WHERE validation_id = %s
                """,
                (rule, error_message, validation_id)
            )
            
            if not success:
                messages.extend(query_messages)
                return False, messages
            
            messages.append(f"Updated input validation for {entity}.{attribute} for local objective {lo_name} in schema {schema_name}")
        else:
            # Input validation doesn't exist, insert it
            success, query_messages, _ = execute_query(
                f"""
                INSERT INTO {schema_name}.input_validations
                (lo_name, entity, attribute, rule, rule_type, error_message)
                VALUES (%s, %s, %s, %s, %s, %s)
                """,
                (lo_name, entity, attribute, rule, rule_type, error_message)
            )
            
            if not success:
                messages.extend(query_messages)
                return False, messages
            
            messages.append(f"Inserted input validation for {entity}.{attribute} for local objective {lo_name} into schema {schema_name}")
        
        return True, messages
    except Exception as e:
        logger.error(f"Error inserting input validation for local objective {lo_name} into schema {schema_name}: {str(e)}", exc_info=True)
        messages.append(f"Error inserting input validation for local objective {lo_name} into schema {schema_name}: {str(e)}")
        return False, messages
