#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> Analyzer for YAML Builder v2

This script analyzes the workflow_runtime schema and creates the necessary tables
in a temporary schema for testing. It helps identify and address any missing tables
required for the execution engine.
"""

import os
import sys
import json
import logging
import argparse
from typing import Dict, List, Tuple, Any, Optional

# Add parent directory to path to import common modules
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
from db_utils import execute_query

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('schema_analyzer')

def analyze_schema(source_schema: str, target_schema: str) -> Tuple[bool, List[str]]:
    """
    Analyze the source schema and create the necessary tables in the target schema.
    
    Args:
        source_schema: Source schema to analyze
        target_schema: Target schema to create tables in
        
    Returns:
        Tuple containing:
            - Boolean indicating if the operation was successful
            - List of messages (warnings, errors, or success messages)
    """
    messages = []
    
    try:
        # Get list of tables in source schema
        success, query_messages, result = execute_query(
            f"""
            SELECT table_name
            FROM information_schema.tables
            WHERE table_schema = %s
            AND table_type = 'BASE TABLE'
            ORDER BY table_name
            """,
            (source_schema,)
        )
        
        if not success:
            messages.extend(query_messages)
            return False, messages
        
        if not result:
            messages.append(f"No tables found in schema '{source_schema}'")
            return False, messages
        
        tables = [row[0] for row in result]
        messages.append(f"Found {len(tables)} tables in schema '{source_schema}'")
        
        # Create target schema if it doesn't exist
        success, query_messages, _ = execute_query(
            f"""
            CREATE SCHEMA IF NOT EXISTS {target_schema}
            """
        )
        
        if not success:
            messages.extend(query_messages)
            return False, messages
        
        messages.append(f"Created schema '{target_schema}' if it didn't exist")
        
        # Get list of tables in target schema
        success, query_messages, result = execute_query(
            f"""
            SELECT table_name
            FROM information_schema.tables
            WHERE table_schema = %s
            AND table_type = 'BASE TABLE'
            """,
            (target_schema,)
        )
        
        if not success:
            messages.extend(query_messages)
            return False, messages
        
        existing_tables = [row[0] for row in result] if result else []
        messages.append(f"Found {len(existing_tables)} existing tables in schema '{target_schema}'")
        
        # Create missing tables in target schema
        for table in tables:
            if table in existing_tables:
                messages.append(f"Table '{table}' already exists in schema '{target_schema}'")
                continue
            
            # Get table definition
            success, query_messages, result = execute_query(
                f"""
                SELECT column_name, data_type, character_maximum_length, 
                       is_nullable, column_default
                FROM information_schema.columns
                WHERE table_schema = %s
                AND table_name = %s
                ORDER BY ordinal_position
                """,
                (source_schema, table)
            )
            
            if not success:
                messages.extend(query_messages)
                continue
            
            if not result:
                messages.append(f"No columns found for table '{table}' in schema '{source_schema}'")
                continue
            
            # Build CREATE TABLE statement
            columns = []
            for row in result:
                column_name, data_type, max_length, is_nullable, default = row
                
                # Format data type
                if data_type == 'character varying' and max_length:
                    data_type = f"VARCHAR({max_length})"
                elif data_type == 'character' and max_length:
                    data_type = f"CHAR({max_length})"
                
                # Format nullable
                nullable = "NULL" if is_nullable == "YES" else "NOT NULL"
                
                # Format default
                default_clause = f"DEFAULT {default}" if default else ""
                
                # Combine
                column_def = f"{column_name} {data_type} {nullable} {default_clause}".strip()
                columns.append(column_def)
            
            # Get primary key
            success, query_messages, result = execute_query(
                f"""
                SELECT kcu.column_name
                FROM information_schema.table_constraints tc
                JOIN information_schema.key_column_usage kcu
                ON tc.constraint_name = kcu.constraint_name
                AND tc.table_schema = kcu.table_schema
                WHERE tc.constraint_type = 'PRIMARY KEY'
                AND tc.table_schema = %s
                AND tc.table_name = %s
                ORDER BY kcu.ordinal_position
                """,
                (source_schema, table)
            )
            
            if success and result:
                pk_columns = [row[0] for row in result]
                if pk_columns:
                    columns.append(f"PRIMARY KEY ({', '.join(pk_columns)})")
            
            # Create table
            column_sql = ",\n                ".join(columns)
            create_table_sql = f"""
            CREATE TABLE {target_schema}.{table} (
                {column_sql}
            )
            """
            
            success, query_messages, _ = execute_query(create_table_sql)
            
            if not success:
                messages.extend(query_messages)
                messages.append(f"Failed to create table '{table}' in schema '{target_schema}'")
                continue
            
            messages.append(f"Created table '{table}' in schema '{target_schema}'")
            
            # Add missing columns needed by entity_deployer.py
            if table == 'entities':
                # Add status, type, and attribute_prefix columns if they don't exist
                success, query_messages, _ = execute_query(
                    f"""
                    ALTER TABLE {target_schema}.entities 
                    ADD COLUMN IF NOT EXISTS status VARCHAR(20) NULL DEFAULT 'active'::character varying,
                    ADD COLUMN IF NOT EXISTS type VARCHAR(50) NULL DEFAULT 'standard'::character varying,
                    ADD COLUMN IF NOT EXISTS attribute_prefix VARCHAR(50) NULL DEFAULT ''::character varying
                    """
                )
                
                if not success:
                    messages.extend(query_messages)
                    messages.append(f"Failed to add missing columns to '{table}' in schema '{target_schema}'")
                else:
                    messages.append(f"Added missing columns to '{table}' in schema '{target_schema}'")
            
            # Add missing columns needed by entity_deployer.py for entity_attributes
            if table == 'entity_attributes':
                # Add display_name, datatype, and status columns if they don't exist
                success, query_messages, _ = execute_query(
                    f"""
                    ALTER TABLE {target_schema}.entity_attributes
                    ADD COLUMN IF NOT EXISTS display_name VARCHAR(100) NULL,
                    ADD COLUMN IF NOT EXISTS datatype VARCHAR(50) NULL DEFAULT 'string'::character varying,
                    ADD COLUMN IF NOT EXISTS status VARCHAR(20) NULL DEFAULT 'active'::character varying
                    """
                )

                if not success:
                    messages.extend(query_messages)
                    messages.append(f"Failed to add missing columns to '{table}' in schema '{target_schema}'")
                else:
                    messages.append(f"Added missing columns to '{table}' in schema '{target_schema}'")
                    
            # Add missing columns needed by go_deployer.py for global_objectives
            if table == 'global_objectives':
                # Add status, tenant_id, and deleted_mark columns if they don't exist
                success, query_messages, _ = execute_query(
                    f"""
                    ALTER TABLE {target_schema}.global_objectives
                    ADD COLUMN IF NOT EXISTS status VARCHAR(20) NULL DEFAULT 'active'::character varying,
                    ADD COLUMN IF NOT EXISTS tenant_id VARCHAR(50) NULL DEFAULT 't001'::character varying,
                    ADD COLUMN IF NOT EXISTS deleted_mark BOOLEAN NULL DEFAULT false
                    """
                )

                if not success:
                    messages.extend(query_messages)
                    messages.append(f"Failed to add missing columns to '{table}' in schema '{target_schema}'")
                else:
                    messages.append(f"Added missing columns to '{table}' in schema '{target_schema}'")
        
        # Get list of foreign keys in source schema
        success, query_messages, result = execute_query(
            f"""
            SELECT
                tc.table_name,
                kcu.column_name,
                ccu.table_name AS foreign_table_name,
                ccu.column_name AS foreign_column_name,
                tc.constraint_name
            FROM information_schema.table_constraints tc
            JOIN information_schema.key_column_usage kcu
            ON tc.constraint_name = kcu.constraint_name
            AND tc.table_schema = kcu.table_schema
            JOIN information_schema.constraint_column_usage ccu
            ON ccu.constraint_name = tc.constraint_name
            AND ccu.table_schema = tc.table_schema
            WHERE tc.constraint_type = 'FOREIGN KEY'
            AND tc.table_schema = %s
            """,
            (source_schema,)
        )
        
        if success and result:
            # Add foreign keys to target schema
            for row in result:
                table_name, column_name, foreign_table_name, foreign_column_name, constraint_name = row
                
                # Skip if table doesn't exist in target schema
                if table_name not in existing_tables and table_name not in tables:
                    continue
                
                # Skip if foreign table doesn't exist in target schema
                if foreign_table_name not in existing_tables and foreign_table_name not in tables:
                    continue
                
                # Create foreign key
                alter_table_sql = f"""
                ALTER TABLE {target_schema}.{table_name}
                ADD CONSTRAINT {constraint_name}
                FOREIGN KEY ({column_name})
                REFERENCES {target_schema}.{foreign_table_name} ({foreign_column_name})
                """
                
                success, query_messages, _ = execute_query(alter_table_sql)
                
                if not success:
                    messages.extend(query_messages)
                    messages.append(f"Failed to add foreign key '{constraint_name}' to table '{table_name}' in schema '{target_schema}'")
                    continue
                
                messages.append(f"Added foreign key '{constraint_name}' to table '{table_name}' in schema '{target_schema}'")
        
        messages.append(f"Schema analysis and creation completed successfully")
        return True, messages
    except Exception as e:
        error_msg = f"Error analyzing schema: {str(e)}"
        logger.error(error_msg, exc_info=True)
        messages.append(error_msg)
        return False, messages

def main():
    """Main function."""
    parser = argparse.ArgumentParser(description='Analyze schema and create necessary tables')
    parser.add_argument('--source-schema', default='workflow_runtime', help='Source schema to analyze')
    parser.add_argument('--target-schema', default='workflow_temp', help='Target schema to create tables in')
    args = parser.parse_args()
    
    success, messages = analyze_schema(args.source_schema, args.target_schema)
    
    for message in messages:
        print(message)
    
    if success:
        print("Schema analysis and creation completed successfully")
    else:
        print("Schema analysis and creation failed")
        sys.exit(1)

if __name__ == '__main__':
    main()
