import anthropic

# Set your API key
client = anthropic.Anthropic(api_key="************************************************************************************************************")

# Start a looped conversation
def run_chat():
    print("Welcome to Claude Coding Assistant. Type 'exit' to quit.")
    
    conversation = []  # stores message history

    while True:
        user_input = input("\nYou: ")
        if user_input.lower() in ['exit', 'quit']:
            break

        conversation.append({"role": "user", "content": user_input})

        response = client.messages.create(
            #model="claude-3-sonnet-20240229",  # use opus if you have access
            model="claude-3-haiku-20240307",
            max_tokens=1000,
            messages=conversation
        )

        assistant_reply = response.content[0].text

        print("\nClaude:", assistant_reply)

        conversation.append({"role": "assistant", "content": assistant_reply})


if __name__ == "__main__":
    run_chat()
