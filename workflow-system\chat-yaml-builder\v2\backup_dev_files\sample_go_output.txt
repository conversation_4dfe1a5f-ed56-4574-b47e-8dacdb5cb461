## Leave Approval Process

**Core Metadata:**
- id: "go001"
- name: "Process Leave Requests"
- version: "1.0"
- status: "Active"
- description: "Manages employee leave requests from submission to approval or rejection"
- primary_entity: "LeaveApplication"
- classification: "HR Process"

**Process Ownership:**
- *Originator:* Employee
- *Process Owner:* HR Manager
- *Business Sponsor:* Human Resources Department

**Data Management:**

*Input Stack:*
- Employee with employeeId*, firstName, lastName, email, departmentId, managerId; LeaveType with typeId*, name, maxDuration, requiresDocumentation (true, false).

*Input Mapping Stack:*
- Employee.employeeId maps to lo001 → LeaveApplication.employeeId
- Employee.firstName maps to lo001 → LeaveApplication.employeeName
- Employee.departmentId maps to lo001 → LeaveApplication.departmentId
- Employee.managerId maps to lo001 → LeaveApplication.managerId
- Employee.email maps to lo001 → LeaveApplication.notificationEmail
- LeaveType.typeId maps to lo001 → LeaveApplication.leaveTypeId
- LeaveType.name maps to lo001 → LeaveApplication.leaveTypeName
- LeaveType.maxDuration maps to lo001 → LeaveApplication.maxAllowedDays
- LeaveType.requiresDocumentation maps to lo001 → LeaveApplication.requiresDocumentation

*Output Stack:*
- LeaveApplication with leaveId*, employeeId*, startDate*, endDate*, numDays*, status* (Pending, Approved, Rejected), approvedBy, approvalDate, comments.

*Output Mapping Stack:*
- LeaveApplication.leaveId maps to go002 "Employee Calendar Management" → CalendarEvent.referenceId
- LeaveApplication.employeeId maps to go002 "Employee Calendar Management" → CalendarEvent.employeeId
- LeaveApplication.startDate maps to go002 "Employee Calendar Management" → CalendarEvent.startDate
- LeaveApplication.endDate maps to go002 "Employee Calendar Management" → CalendarEvent.endDate
- LeaveApplication.status maps to go003 "Leave Balance Tracking" → LeaveTransaction.status
- LeaveApplication.numDays maps to go003 "Leave Balance Tracking" → LeaveTransaction.daysUsed

**Data Constraints:**

*DB Stack:*
- LeaveApplication.leaveId (string) is mandatory and unique. Error message: "Leave ID is required and must be unique"
- LeaveApplication.employeeId (string) is mandatory and must exist in Employee table. Error message: "Employee ID is required"
- LeaveApplication.startDate (date) is mandatory and must be a valid date. Error message: "Start date is required"
- LeaveApplication.endDate (date) is mandatory and must be after startDate. Error message: "End date must be after start date"
- LeaveApplication.numDays (number) is mandatory and must be greater than 0. Error message: "Number of days must be greater than 0"
- LeaveApplication.status (enum) must be one of "Pending", "Approved", "Rejected". Error message: "Invalid status value"

**Process Definition:**

*Process Flow:*
1. lo001: SubmitLeaveRequest [HUMAN] - Employee submits a leave request
   a. If LeaveApplication.requiresDocumentation = true, route to lo002
   b. If LeaveApplication.requiresDocumentation = false, route to lo003
2. lo002: UploadDocumentation [HUMAN] - Employee uploads required documentation
   a. Route to lo003
3. lo003: ReviewLeaveRequest [HUMAN] - Manager reviews the leave request
   a. If LeaveApplication.status = "Approved", route to lo004
   b. If LeaveApplication.status = "Rejected", route to lo005
4. lo004: ApproveLeaveRequest [SYSTEM] - System updates leave request status to approved
   a. Route to lo006
5. lo005: RejectLeaveRequest [SYSTEM] - System updates leave request status to rejected
   a. Route to lo006
6. lo006: NotifyEmployee [SYSTEM] - System notifies employee of the decision
   a. Route to lo007
7. lo007: UpdateCalendar [SYSTEM] - System updates employee calendar
   a. If LeaveApplication.status = "Approved", route to lo008
   b. If LeaveApplication.status = "Rejected", route to Terminal
8. lo008: UpdateLeaveBalance [SYSTEM] - System updates employee leave balance
   a. Route to Terminal
9. lo009: CancelLeaveRequest [HUMAN] - Employee cancels a previously submitted leave request
   a. Route to lo010
10. lo010: RollbackLeaveApproval [SYSTEM] - System rolls back leave approval
    a. Route to lo011
11. lo011: RestoreLeaveBalance [SYSTEM] - System restores employee leave balance
    a. Route to lo012
12. lo012: NotifyCancellation [SYSTEM] - System notifies manager of cancellation
    a. Route to Terminal

*Parallel Flows:*
- After lo006 (NotifyEmployee):
  * lo007 (UpdateCalendar) - System updates employee calendar
  * lo013 (LogAuditTrail) - System logs audit trail for compliance
- Join at: Terminal

*Rollback Pathways:*
- lo001 (SubmitLeaveRequest) ↔ lo009 (CancelLeaveRequest)
- lo004 (ApproveLeaveRequest) ↔ lo010 (RollbackLeaveApproval)
- lo008 (UpdateLeaveBalance) ↔ lo011 (RestoreLeaveBalance)
- Full rollback pathway: lo009 → lo010 → lo011 → lo012

**Business Rules:**
1. Leave requests must be submitted at least 7 days in advance for planned leave - Enforced by lo001
2. Sick leave can be submitted retroactively within 3 days - Enforced by lo001
3. Leave requests exceeding 5 consecutive days require manager approval - Enforced by lo003
4. Documentation is required for sick leave exceeding 3 days - Enforced by lo001 and lo002
5. Leave balance must be sufficient for the requested leave duration - Validated by lo001
6. Overlapping leave requests are flagged for manager review - Implemented by lo003
7. Cancellations must be made at least 24 hours in advance - Enforced by lo009

**Integration Points:**

*GO Relationships:*
- GO sends output to go002 "Employee Calendar Management" for CalendarEvent processing.
- GO sends output to go003 "Leave Balance Tracking" for LeaveTransaction processing.
- GO sends output to go004 "Team Availability Dashboard" for TeamAvailability processing.

*External Systems:*
- HR System: Two-way integration for employee data and leave balance verification
- Calendar System: Outbound integration for employee calendar updates
- Notification System: Outbound integration for email and mobile notifications

**Performance Metadata:**
- cycle_time: "2 business days"
- number_of_pathways: 4
- volume_metrics: {
  average_volume: 120,
  peak_volume: 250,
  unit: "requests/month"
}

**Process Mining Schema:**

*Event Log Specification:*
- case_id: "LeaveApplication.leaveId"
- activity: "lo_id.LO_name"
- event_type: "start/complete/abort/rollback"
- timestamp: "ISO-8601 datetime"
- resource: "role/system_executing"
- duration: "milliseconds"
- attributes: {
  entity_state: "json_snapshot",
  input_values: "input_parameters",
  output_values: "output_results",
  execution_status: "success/failure/pending",
  error_details: "error_message_if_any"
}

*Performance Discovery Metrics:*
- pathway_frequency: {
  "Standard Approval": {
    frequency: 95,
    percentage: 79,
    average_duration: "1.5 days",
    success_rate: 98
  },
  "Documentation Required": {
    frequency: 25,
    percentage: 21,
    average_duration: "2.8 days",
    success_rate: 92
  }
}
- bottleneck_analysis: {
  "lo003": {
    average_wait_time: "1.2 days",
    queue_length: 8,
    resource_utilization: 75,
    failure_rate: 5
  }
}

*Rollback Analytics:*
- rollback_frequency: 4
- rollback_success_rate: 99
- rollback_triggers: {
  "user_initiated": {
    frequency: 18,
    average_impact_scope: 2,
    average_recovery_time: "2 hours"
  },
  "system_error": {
    frequency: 2,
    average_impact_scope: 1,
    average_recovery_time: "1 hour"
  }
}

**Sample Data:**
- LeaveApplication.leaveId: "LV-2023-0001", "LV-2023-0002", "LV-2023-0003"
- LeaveApplication.employeeId: "EMP001", "EMP002", "EMP003"
- LeaveApplication.startDate: "2023-06-15", "2023-07-01", "2023-08-10"
- LeaveApplication.endDate: "2023-06-16", "2023-07-05", "2023-08-17"
- LeaveApplication.numDays: 2, 5, 8
- LeaveApplication.status: "Pending", "Approved", "Rejected"
- LeaveApplication.leaveTypeId: "LT001", "LT002", "LT003"
- LeaveApplication.leaveTypeName: "Annual Leave", "Sick Leave", "Personal Leave"
