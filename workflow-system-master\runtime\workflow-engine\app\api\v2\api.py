"""
API v2 Main Router

This module contains the main FastAPI router for v2 API endpoints.
Implements microservices architecture with separate service modules.
"""

from fastapi import APIRouter

from .auth.routes import router as auth_router
from .auth.organizational_routes import router as org_router
from .global_objectives.routes import router as global_objectives_router
from .workflow_instances.routes import router as workflow_instances_router
from .local_objectives.routes import router as local_objectives_router


# Create main v2 API router
api_router = APIRouter(prefix="/v2")

# Include microservice routers
api_router.include_router(
    auth_router,
    tags=["Authentication v2"]
)

api_router.include_router(
    org_router,
    tags=["Organizational v2"]
)

api_router.include_router(
    global_objectives_router,
    tags=["Global Objectives v2"]
)

api_router.include_router(
    workflow_instances_router,
    tags=["Workflow Instances v2"]
)

api_router.include_router(
    local_objectives_router,
    tags=["Local Objectives v2"]
)

# Health check endpoint for v2 API
@api_router.get("/health", tags=["Health"])
async def health_check():
    """
    Health check endpoint for v2 API.
    
    Returns:
        dict: API health status
    """
    return {
        "status": "healthy",
        "version": "2.0",
        "services": {
            "authentication": "active"
        }
    }


# API information endpoint
@api_router.get("/info", tags=["Information"])
async def api_info():
    """
    API information endpoint.
    
    Returns:
        dict: API version and available services
    """
    return {
        "api_version": "2.0",
        "description": "Workflow System API v2 with microservices architecture",
        "available_services": [
            {
                "name": "authentication",
                "endpoints": [
                    "POST /v2/auth/register - Register a new user",
                    "POST /v2/auth/login - User login",
                    "POST /v2/auth/refresh - Refresh access token",
                    "POST /v2/auth/logout - User logout",
                    "GET /v2/auth/user/{user_id} - Get user by ID"
                ]
            },
            {
                "name": "global_objectives",
                "endpoints": [
                    "GET /v2/global_objectives/ - Get all global objectives",
                    "GET /v2/global_objectives/{go_id} - Get global objective by ID",
                    "GET /v2/global_objectives/books/all - Get all books",
                    "GET /v2/global_objectives/books/{book_id}/chapters - Get chapters by book",
                    "GET /v2/global_objectives/books/{book_id}/objectives - Get objectives by book",
                    "GET /v2/global_objectives/chapters/{chapter_id}/objectives - Get objectives by chapter"
                ]
            },
            {
                "name": "workflow_instances",
                "endpoints": [
                    "POST /v2/workflow_instances/ - Create a new workflow instance",
                    "POST /v2/workflow_instances/{instance_id}/start - Start a workflow instance",
                    "GET /v2/workflow_instances/{instance_id} - Get workflow instance by ID",
                    "GET /v2/workflow_instances/ - List workflow instances with filtering"
                ]
            },
            {
                "name": "local_objectives",
                "endpoints": [
                    "GET /v2/local_objectives/instances/{instance_id}/inputs - Get all input fields for workflow instance",
                    "GET /v2/local_objectives/instances/{instance_id}/inputs/user - Get user input fields only",
                    "GET /v2/local_objectives/instances/{instance_id}/inputs/system - Get system input fields only",
                    "GET /v2/local_objectives/instances/{instance_id}/inputs/dependencies - Get input field dependencies",
                    "GET /v2/local_objectives/health - Health check for local objectives service"
                ]
            }
        ],
        "documentation": "/docs",
        "openapi_schema": "/openapi.json"
    }
