Employee has employeeId^PK, firstName, lastName, email, phoneNumber, departmentId^FK, managerId^FK, hireDate, status (Active, Inactive, OnLeave), salary, performanceRating.

* Employee has many-to-one relationship with Department using Employee.departmentId to Department.departmentId^PK
* Employee has many-to-one relationship with Employee using Employee.managerId to Employee.employeeId^PK

* Employee.email must be unique
* Employee.email must match pattern "^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$"
* Employee.hireDate must be before current date
* Employee.salary must be greater than 0

BusinessRule EMP001 for Employee:
* Employee.status must be Active to receive performance reviews
* Employee.hireDate must be at least 90 days before Employee.performanceRating can be set

Employee has employeeId, firstName, lastName, fullName[derived].

CalculatedField CF001 for Employee.fullName:
* Formula: CONCAT(firstName, ' ', lastName)
* Logic Layer: Application
* Caching: Session
* Dependencies: Employee.firstName, Employee.lastName

* Archive Strategy for Employee:
  - Trigger: Event-based
  - Criteria: When Employee.status changes to 'Inactive' and remains so for 1 year
  - Retention: 7 years
  - Storage: Cold storage
  - Access Pattern: Read-only through HR archive portal
  - Restoration: Manual process requiring HR Director approval

* History Tracking for Employee:
  - Tracked Attributes: Employee.salary, Employee.departmentId, Employee.managerId, Employee.status
  - Tracking Method: Audit table
  - Granularity: Change level
  - Retention: 7 years
  - Access Control: HR Managers and Compliance Officers only

Department has departmentId^PK, name, managerId^FK, location, budget.

* Department has one-to-one relationship with Employee using Department.managerId to Employee.employeeId^PK
* Department has one-to-many relationship with Employee using Department.departmentId to Employee.departmentId^FK

* Department.name must be unique
* Department.budget must be greater than 0

BusinessRule DEPT001 for Department:
* Department.budget must be approved by Finance before changes
* Department.managerId must reference an Employee with status = 'Active'

* Archive Strategy for Department:
  - Trigger: Event-based
  - Criteria: When Department is marked as 'Dissolved'
  - Retention: 10 years
  - Storage: Cold storage
  - Access Pattern: Read-only through Admin portal
  - Restoration: Manual process requiring CEO approval
