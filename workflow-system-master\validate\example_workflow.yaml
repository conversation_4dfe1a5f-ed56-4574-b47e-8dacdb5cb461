---
# Enterprise Workflow YAML Configuration - Negative Example (Intentional Errors for Validation)
tenant:
  # Missing 'id' field which is required
  name: Error Company
  description: Enterprise workflow system with errors
  version: "1.0.0"
  status: active
  roles:
    - id: ADMIN  # Error: ID not lowercase
      name: Administrator
      # Missing 'access' field which is required
    
    - id: user
      # Missing 'name' field which is required
      description: Regular user
      access:
        entities:
          - entity_id: e999  # Error: References non-existent entity
            permissions: [read, invalid_permission]  # Error: Invalid permission type
        objectives:
          - # Missing 'objective_id' field which is required
            permissions: [execute]

workflow_data:
  id: wd001
  name: Error Workflow
  description: Process with validation errors
  version: "1.0.0"
  status: active

# Missing 'permission_types' section which is required

entities:
  - id: e001
    name: Bad Request  # Error: Name contains spaces (should be camelCase)
    display_name: Request
    type: data
    version: "1.0.0"
    status: active
    attributes_metadata:
      attribute_prefix: req
      attribute_map:
        at101: requestId
        at102: title
      # Missing 'required_attributes' field which is required
    attributes:
      - id: at101
        name: requestId
        display_name: Request ID
        # Missing 'datatype' field which is required
        required: true
        version: "1.0.0"
        status: active
      
      - id: at102
        name: title
        display_name: Title
        description: Request title
        datatype: string
        required: "yes"  # Error: Should be boolean, not string
        default_value: null
        version: "1.0.0"
        status: active
      
      - id: at103
        name: status
        display_name: Status
        description: Request status
        datatype: enum  # Error: Enum type without 'values' field
        required: true
        version: "1.0.0"
        status: active

global_objectives:
  - id: go001
    name: errorProcess
    display_name: Error Process
    description: Process with validation errors
    version: "1.0.0"
    status: active

local_objectives:
  # First LO should have workflow_source: origin
  - id: lo001
    contextual_id: go001.lo001
    name: badObjective
    display_name: Bad Objective
    description: Objective with validation errors
    workflow_source: intermediate  # Error: First LO should be 'origin'
    function_type: invalid_type  # Error: Invalid function_type
    version: "1.0.0"
    status: active
    
    agent_stack:
      agents:
        - role: nonexistent_role  # Error: References non-existent role
          rights: [execute]
    
    input_stack:
      # Missing 'inputs' array which is required
    
    output_stack:
      outputs:
        - id: out001
          # Missing 'slot_id' field which is required
          contextual_id: go001.lo001.out001
          display_name: Title
          data_type: string
          ui_control: oj-bind-text
        
        - id: out002
          slot_id: invalid.format  # Error: Invalid slot_id format
          contextual_id: go001.lo001.out002
          display_name: Description
          data_type: string
          ui_control: invalid-control  # Error: Invalid UI control
    
    data_mapping_stack:
      mappings:
        - id: map001
          source: go001.lo001.in001  # Error: References non-existent input
          target: go001.lo001.out001
    
    execution_pathway:
      type: sequential
      # Missing 'next_lo' field for sequential pathway
  
  - id: lo002
    contextual_id: go001.lo002
    name: missingFields
    # Missing several required fields
    workflow_source: terminal
    function_type: update
    
    # Missing 'agent_stack' which is required
    
    input_stack:
      inputs:
        - id: in001
          slot_id: e001.at101.in001
          contextual_id: go001.lo002.in001
          display_name: Request ID
          source:
            type: unknown  # Error: Invalid source type
          # Missing 'required' field which is required
          data_type: string
          ui_control: oj-input-text
          metadata:
            usage: lookup
    
    # Missing 'output_stack' which is required
    
    data_mapping_stack:
      # Empty mappings list (should have at least input-to-output mappings)
      mappings: []
    
    # Missing 'execution_pathway' which is required

# Missing the terminal objective which is required