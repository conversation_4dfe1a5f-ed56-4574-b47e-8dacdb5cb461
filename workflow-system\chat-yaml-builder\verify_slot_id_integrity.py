#!/usr/bin/env python3

"""
This script verifies the integrity of slot ID mappings in the database.
It checks that each slot ID references an attribute that exists in the specified entity.
"""

import psycopg2
from datetime import datetime
import os

# Set log path to current working directory
LOG_PATH = os.path.join(
    os.path.dirname(os.path.abspath(__file__)),
    f"verify_slot_id_integrity_log_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
)

# Logging function
def log(msg):
    with open(LOG_PATH, "a") as f:
        f.write(f"{datetime.now().strftime('%Y-%m-%d %H:%M:%S')} | {msg}\n")
    print(msg)

# Database configuration
PG_CONFIG = {
    "dbname": "workflow_system",
    "user": "postgres",
    "password": "workflow_postgres_secure_password",
    "host": "**********",
    "port": "5432"
}

def connect_to_db():
    """Connect to the PostgreSQL database."""
    try:
        conn = psycopg2.connect(**PG_CONFIG)
        log("✅ Connected to PostgreSQL database")
        return conn
    except Exception as e:
        log(f"❌ Error connecting to database: {e}")
        raise

def verify_slot_id_integrity():
    """Verify the integrity of slot ID mappings in the database."""
    conn = connect_to_db()
    cursor = conn.cursor()
    
    try:
        # Set search path to the workflow_runtime schema
        cursor.execute("SET search_path TO workflow_runtime")
        
        # Get all input items
        cursor.execute("""
            SELECT i.id, i.slot_id, i.source_description, lo.lo_id
            FROM lo_input_items i
            JOIN lo_input_stack s ON i.input_stack_id = s.id
            JOIN local_objectives lo ON i.lo_id = lo.lo_id
        """)
        
        items = cursor.fetchall()
        
        log(f"🔍 Checking {len(items)} input items for slot ID integrity")
        
        integrity_issues = []
        
        for item_id, slot_id, description, lo_id in items:
            parts = slot_id.split('.')
            if len(parts) >= 2:
                entity_id = parts[0]
                attribute_id = parts[1]
                
                # Verify that the entity exists
                cursor.execute(
                    """
                    SELECT 1 FROM entities WHERE entity_id = %s
                    """,
                    (entity_id,)
                )
                
                if not cursor.fetchone():
                    integrity_issues.append(f"Entity {entity_id} not found for input {item_id} in {lo_id}")
                    continue
                
                # Verify that the attribute exists for this entity
                cursor.execute(
                    """
                    SELECT display_name FROM entity_attributes 
                    WHERE entity_id = %s AND attribute_id = %s
                    """,
                    (entity_id, attribute_id)
                )
                
                result = cursor.fetchone()
                if not result:
                    integrity_issues.append(f"Attribute {attribute_id} not found for entity {entity_id} in input {item_id}")
                elif result[0] != description:
                    integrity_issues.append(f"Attribute {attribute_id} has display name '{result[0]}' but input {item_id} has source description '{description}'")
        
        if integrity_issues:
            log(f"⚠️ Found {len(integrity_issues)} integrity issues:")
            for issue in integrity_issues:
                log(f"  - {issue}")
        else:
            log("✅ All slot IDs have integrity")
        
    except Exception as e:
        log(f"❌ Error verifying slot ID integrity: {e}")
    finally:
        cursor.close()
        conn.close()

if __name__ == "__main__":
    verify_slot_id_integrity()
