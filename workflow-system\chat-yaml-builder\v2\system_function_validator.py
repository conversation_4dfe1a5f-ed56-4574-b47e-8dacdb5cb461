"""
System Function Validator for YAML Builder v2

This module provides functionality for validating system function names and formats
against the available functions in the workflow-engine system_functions.py file.
"""

import os
import sys
import re
import logging
import importlib.util
from typing import Dict, List, Tuple, Any, Optional, Set

# Set up logging
logger = logging.getLogger('system_function_validator')

class SystemFunctionValidator:
    """
    Validates system function names and formats against the available functions
    in the workflow-engine system_functions.py file.
    """
    
    def __init__(self, system_functions_path: str = None):
        """
        Initialize the system function validator.
        
        Args:
            system_functions_path: Path to the system_functions.py file
        """
        self.system_functions_path = system_functions_path or "/home/<USER>/workflow-system/runtime/workflow-engine/app/services/system_functions.py"
        self.available_functions = set()
        self.function_categories = {}
        self.function_parameters = {}
        self.load_system_functions()
        
    def load_system_functions(self) -> None:
        """
        Load available system functions from the system_functions.py file.
        """
        try:
            # Read the system_functions.py file
            with open(self.system_functions_path, 'r') as f:
                content = f.read()
            
            # Extract function names using regex
            # Look for @function_repository.register patterns
            register_pattern = r'@function_repository\.register\(["\']([^"\']+)["\'],\s*["\']([^"\']+)["\']\)'
            register_matches = re.findall(register_pattern, content)
            
            # Look for function definitions
            function_pattern = r'def\s+([a-zA-Z0-9_]+)\s*\(([^)]*)\)'
            function_matches = re.findall(function_pattern, content)
            
            # Process register matches
            for category, function_name in register_matches:
                self.available_functions.add(function_name)
                
                if category not in self.function_categories:
                    self.function_categories[category] = []
                
                self.function_categories[category].append(function_name)
            
            # Process function definitions to get parameters
            for function_name, params_str in function_matches:
                if function_name in self.available_functions:
                    # Parse parameters
                    params = []
                    for param in params_str.split(','):
                        param = param.strip()
                        if param and not param.startswith('*'):
                            # Extract parameter name (before any colon or equals sign)
                            param_name = param.split(':')[0].split('=')[0].strip()
                            params.append(param_name)
                    
                    self.function_parameters[function_name] = params
            
            logger.info(f"Loaded {len(self.available_functions)} system functions from {self.system_functions_path}")
            logger.info(f"Function categories: {list(self.function_categories.keys())}")
        except Exception as e:
            logger.error(f"Error loading system functions: {str(e)}", exc_info=True)
            # Initialize with some common functions as fallback
            self.available_functions = {
                "generate_id", "current_timestamp", "current_user", "current_date",
                "subtract_days", "add_days", "calculate_amount", "calculate_tax", "calculate_total",
                "to_uppercase", "to_lowercase", "format_date", "format_number", "format_enum_value",
                "fetch_filtered_records", "fetch_by_id", "fetch_related", "fetch_lookup", "fetch_user_profile",
                "validate_reference", "validate_pattern", "validate_range", "validate_uniqueness", "validate_business_rule"
            }
            
            self.function_categories = {
                "utility": ["generate_id", "current_timestamp", "current_user", "current_date"],
                "math": ["subtract_days", "add_days", "calculate_amount", "calculate_tax", "calculate_total"],
                "transform": ["to_uppercase", "to_lowercase", "format_date", "format_number", "format_enum_value"],
                "database": ["fetch_filtered_records", "fetch_by_id", "fetch_related", "fetch_lookup", "fetch_user_profile"],
                "validation": ["validate_reference", "validate_pattern", "validate_range", "validate_uniqueness", "validate_business_rule"]
            }
            
            logger.warning("Using fallback system functions due to error")
    
    def validate_function_name(self, function_name: str) -> Tuple[bool, str]:
        """
        Validate a function name against the available system functions.
        
        Args:
            function_name: The function name to validate
            
        Returns:
            Tuple containing:
                - Boolean indicating if the function name is valid
                - Message with validation result or suggestion
        """
        if function_name in self.available_functions:
            return True, f"Valid system function: {function_name}"
        
        # If not found, try to find similar functions
        similar_functions = []
        for func in self.available_functions:
            # Simple similarity check (could be improved with more sophisticated algorithms)
            if function_name.lower() in func.lower() or func.lower() in function_name.lower():
                similar_functions.append(func)
        
        if similar_functions:
            return False, f"Invalid system function: {function_name}. Did you mean one of these? {', '.join(similar_functions)}"
        
        # If no similar functions found, suggest by category
        category_suggestions = {}
        for category, functions in self.function_categories.items():
            if len(functions) > 0:
                category_suggestions[category] = functions[0]
        
        suggestion_msg = "Available function categories: " + ", ".join([f"{cat}: {func}" for cat, func in category_suggestions.items()])
        return False, f"Invalid system function: {function_name}. {suggestion_msg}"
    
    def get_functions_by_category(self, category: str) -> List[str]:
        """
        Get all functions in a specific category.
        
        Args:
            category: The category to get functions for
            
        Returns:
            List of function names in the category
        """
        return self.function_categories.get(category, [])
    
    def get_function_parameters(self, function_name: str) -> List[str]:
        """
        Get the parameters for a specific function.
        
        Args:
            function_name: The function name to get parameters for
            
        Returns:
            List of parameter names
        """
        return self.function_parameters.get(function_name, [])
    
    def get_all_functions(self) -> Set[str]:
        """
        Get all available system functions.
        
        Returns:
            Set of all function names
        """
        return self.available_functions
    
    def get_all_categories(self) -> List[str]:
        """
        Get all function categories.
        
        Returns:
            List of category names
        """
        return list(self.function_categories.keys())


# Example usage
if __name__ == "__main__":
    validator = SystemFunctionValidator()
    
    # Test some function names
    test_functions = ["generate_id", "current_timestamp", "format_date", "invalid_function"]
    
    for func in test_functions:
        is_valid, message = validator.validate_function_name(func)
        print(f"{func}: {is_valid} - {message}")
    
    # Print all functions by category
    print("\nFunctions by category:")
    for category in validator.get_all_categories():
        functions = validator.get_functions_by_category(category)
        print(f"{category}: {', '.join(functions)}")
