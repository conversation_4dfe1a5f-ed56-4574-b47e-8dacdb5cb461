#!/usr/bin/env python3
"""
Fix schema migration issues for YAML Builder v2.

This script:
1. Adds missing columns to the entities table
2. Adds missing columns to the entity_attributes table
3. Creates a backup of the original entity_deployer.py file
4. Replaces the original entity_deployer.py file with a modified version that checks for column existence

Usage:
    python fix_schema_migration.py --schema-name workflow_temp
    python fix_schema_migration.py --schema-name workflow_runtime
"""

import os
import sys
import argparse
import logging
import shutil
import psycopg2
from typing import List, Tuple, Dict, Any, Optional

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('fix_schema_migration')

# Database connection parameters
DB_PARAMS = {
    "dbname": "workflow_system",
    "user": "postgres",
    "password": "workflow_postgres_secure_password",
    "host": "**********",
    "port": "5432"
}

def execute_query(query: str, params: Optional[Tuple] = None, schema_name: Optional[str] = None) -> Tuple[bool, List[str], Optional[List[Tuple]]]:
    """
    Execute a SQL query.
    
    Args:
        query: SQL query to execute
        params: Query parameters
        schema_name: Schema name to use
        
    Returns:
        Tuple containing:
            - Boolean indicating if the query was successful
            - List of messages (warnings, errors, or success messages)
            - Query results (if any)
    """
    messages = []
    result = None
    
    try:
        # Connect to the database
        conn = psycopg2.connect(**DB_PARAMS)
        
        # Create a cursor
        cursor = conn.cursor()
        
        # Set search path if schema_name is provided
        if schema_name:
            cursor.execute(f"SET search_path TO {schema_name}")
        
        # Execute the query
        if params:
            cursor.execute(query, params)
        else:
            cursor.execute(query)
        
        # Commit the transaction
        conn.commit()
        
        # Fetch the results if the query returns rows
        if cursor.description:
            result = cursor.fetchall()
        
        # Close the cursor and connection
        cursor.close()
        conn.close()
        
        return True, messages, result
    except Exception as e:
        # Rollback the transaction
        if 'conn' in locals() and conn:
            conn.rollback()
        
        # Close the cursor and connection
        if 'cursor' in locals() and cursor:
            cursor.close()
        if 'conn' in locals() and conn:
            conn.close()
        
        error_msg = f"Database error: {str(e)}"
        logger.error(error_msg)
        messages.append(error_msg)
        
        return False, messages, None

def check_column_exists(schema_name: str, table_name: str, column_name: str) -> bool:
    """
    Check if a column exists in a table.
    
    Args:
        schema_name: Schema name
        table_name: Table name
        column_name: Column name
        
    Returns:
        Boolean indicating if the column exists
    """
    try:
        success, query_messages, result = execute_query(
            """
            SELECT EXISTS (
                SELECT FROM information_schema.columns 
                WHERE table_schema = %s
                AND table_name = %s
                AND column_name = %s
            )
            """,
            (schema_name, table_name, column_name)
        )
        
        if not success:
            logger.error(f"Error checking if column {column_name} exists: {query_messages}")
            return False
        
        return result and result[0][0]
    except Exception as e:
        logger.error(f"Error checking if column {column_name} exists: {str(e)}")
        return False

def add_missing_columns(schema_name: str) -> Tuple[bool, List[str]]:
    """
    Add missing columns to the entities and entity_attributes tables.
    
    Args:
        schema_name: Schema name
        
    Returns:
        Tuple containing:
            - Boolean indicating if the operation was successful
            - List of messages (warnings, errors, or success messages)
    """
    messages = []
    
    try:
        # Check if the entities table exists
        success, query_messages, result = execute_query(
            """
            SELECT EXISTS (
                SELECT FROM information_schema.tables 
                WHERE table_schema = %s
                AND table_name = 'entities'
            )
            """,
            (schema_name,)
        )
        
        if not success:
            messages.extend(query_messages)
            return False, messages
        
        entities_table_exists = result and result[0][0]
        
        if not entities_table_exists:
            error_msg = f"Table {schema_name}.entities does not exist"
            logger.error(error_msg)
            messages.append(error_msg)
            return False, messages
        
        # Add missing columns to the entities table
        entities_columns_to_add = [
            ("status", "VARCHAR(20)", "active"),
            ("type", "VARCHAR(50)", "standard"),
            ("attribute_prefix", "VARCHAR(50)", "")
        ]
        
        for column_name, column_type, default_value in entities_columns_to_add:
            # Check if the column exists
            column_exists = check_column_exists(schema_name, "entities", column_name)
            
            if not column_exists:
                # Add the column
                query = f"""
                    ALTER TABLE {schema_name}.entities 
                    ADD COLUMN {column_name} {column_type} NULL DEFAULT '{default_value}'::character varying
                """
                
                success, query_messages, _ = execute_query(query)
                
                if not success:
                    messages.extend(query_messages)
                    return False, messages
                
                messages.append(f"Added column {column_name} to {schema_name}.entities")
                logger.info(f"Added column {column_name} to {schema_name}.entities")
            else:
                messages.append(f"Column {column_name} already exists in {schema_name}.entities")
                logger.info(f"Column {column_name} already exists in {schema_name}.entities")
                
        # Check if the global_objectives table exists
        success, query_messages, result = execute_query(
            """
            SELECT EXISTS (
                SELECT FROM information_schema.tables 
                WHERE table_schema = %s
                AND table_name = 'global_objectives'
            )
            """,
            (schema_name,)
        )
        
        if not success:
            messages.extend(query_messages)
            return False, messages
        
        global_objectives_table_exists = result and result[0][0]
        
        if global_objectives_table_exists:
            # Drop and recreate the global_objectives table
            query = f"""
                DROP TABLE IF EXISTS {schema_name}.global_objectives CASCADE;
                
                CREATE TABLE {schema_name}.global_objectives (
                    go_id VARCHAR(50) PRIMARY KEY,
                    name VARCHAR(100) NOT NULL,
                    description TEXT,
                    process_mining_schema JSONB,
                    performance_metadata JSONB,
                    version_type VARCHAR(10),
                    status VARCHAR(20) DEFAULT 'active',
                    tenant_id VARCHAR(50) DEFAULT 't001',
                    deleted_mark BOOLEAN DEFAULT false
                );
            """
            
            success, query_messages, _ = execute_query(query)
            
            if not success:
                messages.extend(query_messages)
                return False, messages
            
            messages.append(f"Recreated {schema_name}.global_objectives table with all required columns")
            logger.info(f"Recreated {schema_name}.global_objectives table with all required columns")
        else:
            # Create the global_objectives table
            query = f"""
                CREATE TABLE {schema_name}.global_objectives (
                    go_id VARCHAR(50) PRIMARY KEY,
                    name VARCHAR(100) NOT NULL,
                    description TEXT,
                    process_mining_schema JSONB,
                    performance_metadata JSONB,
                    version_type VARCHAR(10),
                    status VARCHAR(20) DEFAULT 'active',
                    tenant_id VARCHAR(50) DEFAULT 't001',
                    deleted_mark BOOLEAN DEFAULT false
                );
            """
            
            success, query_messages, _ = execute_query(query)
            
            if not success:
                messages.extend(query_messages)
                return False, messages
            
            messages.append(f"Created {schema_name}.global_objectives table with all required columns")
            logger.info(f"Created {schema_name}.global_objectives table with all required columns")
        
        # Check if the entity_attributes table exists
        success, query_messages, result = execute_query(
            """
            SELECT EXISTS (
                SELECT FROM information_schema.tables 
                WHERE table_schema = %s
                AND table_name = 'entity_attributes'
            )
            """,
            (schema_name,)
        )
        
        if not success:
            messages.extend(query_messages)
            return False, messages
        
        entity_attributes_table_exists = result and result[0][0]
        
        if not entity_attributes_table_exists:
            error_msg = f"Table {schema_name}.entity_attributes does not exist"
            logger.error(error_msg)
            messages.append(error_msg)
            return False, messages
        
        # Add missing columns to the entity_attributes table
        columns_to_add = [
            ("display_name", "VARCHAR(100)", None),
            ("datatype", "VARCHAR(50)", "string"),
            ("status", "VARCHAR(20)", "active")
        ]
        
        for column_name, column_type, default_value in columns_to_add:
            # Check if the column exists
            column_exists = check_column_exists(schema_name, "entity_attributes", column_name)
            
            if not column_exists:
                # Add the column
                if default_value:
                    query = f"""
                        ALTER TABLE {schema_name}.entity_attributes 
                        ADD COLUMN {column_name} {column_type} NULL DEFAULT '{default_value}'::character varying
                    """
                else:
                    query = f"""
                        ALTER TABLE {schema_name}.entity_attributes 
                        ADD COLUMN {column_name} {column_type} NULL
                    """
                
                success, query_messages, _ = execute_query(query)
                
                if not success:
                    messages.extend(query_messages)
                    return False, messages
                
                messages.append(f"Added column {column_name} to {schema_name}.entity_attributes")
                logger.info(f"Added column {column_name} to {schema_name}.entity_attributes")
            else:
                messages.append(f"Column {column_name} already exists in {schema_name}.entity_attributes")
                logger.info(f"Column {column_name} already exists in {schema_name}.entity_attributes")
        
        # Make entity_business_rules.condition and entity_business_rules.action nullable
        success, query_messages, result = execute_query(
            """
            SELECT EXISTS (
                SELECT FROM information_schema.tables 
                WHERE table_schema = %s
                AND table_name = 'entity_business_rules'
            )
            """,
            (schema_name,)
        )
        
        if not success:
            messages.extend(query_messages)
            return False, messages
        
        entity_business_rules_table_exists = result and result[0][0]
        
        if entity_business_rules_table_exists:
            # Check if the condition column is not nullable
            success, query_messages, result = execute_query(
                """
                SELECT is_nullable 
                FROM information_schema.columns 
                WHERE table_schema = %s
                AND table_name = 'entity_business_rules'
                AND column_name = 'condition'
                """,
                (schema_name,)
            )
            
            if not success:
                messages.extend(query_messages)
                return False, messages
            
            condition_is_not_nullable = result and result[0][0] == 'NO'
            
            if condition_is_not_nullable:
                # Make the condition column nullable
                query = f"""
                    ALTER TABLE {schema_name}.entity_business_rules 
                    ALTER COLUMN condition DROP NOT NULL
                """
                
                success, query_messages, _ = execute_query(query)
                
                if not success:
                    messages.extend(query_messages)
                    return False, messages
                
                messages.append(f"Made column condition nullable in {schema_name}.entity_business_rules")
                logger.info(f"Made column condition nullable in {schema_name}.entity_business_rules")
            
            # Check if the action column is not nullable
            success, query_messages, result = execute_query(
                """
                SELECT is_nullable 
                FROM information_schema.columns 
                WHERE table_schema = %s
                AND table_name = 'entity_business_rules'
                AND column_name = 'action'
                """,
                (schema_name,)
            )
            
            if not success:
                messages.extend(query_messages)
                return False, messages
            
            action_is_not_nullable = result and result[0][0] == 'NO'
            
            if action_is_not_nullable:
                # Make the action column nullable
                query = f"""
                    ALTER TABLE {schema_name}.entity_business_rules 
                    ALTER COLUMN action DROP NOT NULL
                """
                
                success, query_messages, _ = execute_query(query)
                
                if not success:
                    messages.extend(query_messages)
                    return False, messages
                
                messages.append(f"Made column action nullable in {schema_name}.entity_business_rules")
                logger.info(f"Made column action nullable in {schema_name}.entity_business_rules")
        
        return True, messages
    except Exception as e:
        error_msg = f"Error adding missing columns: {str(e)}"
        logger.error(error_msg, exc_info=True)
        messages.append(error_msg)
        return False, messages

def update_entity_deployer() -> Tuple[bool, List[str]]:
    """
    Update the entity_deployer.py file to check for column existence.
    
    Returns:
        Tuple containing:
            - Boolean indicating if the operation was successful
            - List of messages (warnings, errors, or success messages)
    """
    messages = []
    
    try:
        # Get the directory of this script
        script_dir = os.path.dirname(os.path.abspath(__file__))
        
        # Path to the original entity_deployer.py file
        original_file = os.path.join(script_dir, 'deployers', 'entity_deployer.py')
        
        # Path to the backup file
        backup_file = os.path.join(script_dir, 'deployers', 'entity_deployer.py.bak')
        
        # Path to the modified file
        modified_file = os.path.join(script_dir, 'deployers', 'entity_deployer_modified.py')
        
        # Check if the original file exists
        if not os.path.exists(original_file):
            error_msg = f"Original file {original_file} does not exist"
            logger.error(error_msg)
            messages.append(error_msg)
            return False, messages
        
        # Check if the modified file exists
        if not os.path.exists(modified_file):
            error_msg = f"Modified file {modified_file} does not exist"
            logger.error(error_msg)
            messages.append(error_msg)
            return False, messages
        
        # Create a backup of the original file
        shutil.copy2(original_file, backup_file)
        messages.append(f"Created backup of {original_file} at {backup_file}")
        logger.info(f"Created backup of {original_file} at {backup_file}")
        
        # Replace the original file with the modified file
        shutil.copy2(modified_file, original_file)
        messages.append(f"Replaced {original_file} with {modified_file}")
        logger.info(f"Replaced {original_file} with {modified_file}")
        
        return True, messages
    except Exception as e:
        error_msg = f"Error updating entity_deployer.py: {str(e)}"
        logger.error(error_msg, exc_info=True)
        messages.append(error_msg)
        return False, messages

def main():
    """Main function."""
    parser = argparse.ArgumentParser(description='Fix schema migration issues for YAML Builder v2.')
    parser.add_argument('--schema-name', required=True, help='Schema name to fix (workflow_temp or workflow_runtime)')
    args = parser.parse_args()
    
    schema_name = args.schema_name
    
    if schema_name not in ['workflow_temp', 'workflow_runtime']:
        logger.error(f"Invalid schema name: {schema_name}. Must be workflow_temp or workflow_runtime.")
        sys.exit(1)
    
    logger.info(f"Fixing schema migration issues for {schema_name}")
    
    # Add missing columns
    success, messages = add_missing_columns(schema_name)
    
    for message in messages:
        logger.info(message)
    
    if not success:
        logger.error(f"Failed to add missing columns to {schema_name}")
        sys.exit(1)
    
    # Update entity_deployer.py
    success, messages = update_entity_deployer()
    
    for message in messages:
        logger.info(message)
    
    if not success:
        logger.error("Failed to update entity_deployer.py")
        sys.exit(1)
    
    logger.info(f"Successfully fixed schema migration issues for {schema_name}")
    logger.info("You can now run deploy_to_temp_schema.py or deploy_to_runtime_schema.py")

if __name__ == '__main__':
    main()
