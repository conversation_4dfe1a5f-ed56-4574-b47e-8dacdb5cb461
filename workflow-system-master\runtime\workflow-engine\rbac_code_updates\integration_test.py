
#!/usr/bin/env python3
'''
RBAC Integration Test

This script tests the integration of the RBAC implementation with the existing authentication system.
'''

import sys
import os
import yaml
import json
import requests
from typing import Dict, Any

def test_authentication():
    '''Test the authentication system.'''
    print("Testing authentication...")
    
    # Test login
    response = requests.post(
        "http://localhost:8000/api/v1/auth/login",
        json={
            "username": "johndo<PERSON>",
            "password": "password123"
        }
    )
    
    if response.status_code != 200:
        print(f"❌ Login failed: {response.status_code} {response.text}")
        return None
    
    print("✅ Login successful")
    token = response.json().get("access_token")
    
    # Test token validation
    response = requests.get(
        "http://localhost:8000/api/v1/auth/validate",
        headers={
            "Authorization": f"Bearer {token}"
        }
    )
    
    if response.status_code != 200:
        print(f"❌ Token validation failed: {response.status_code} {response.text}")
        return None
    
    print("✅ Token validation successful")
    
    # Test refresh token
    response = requests.post(
        "http://localhost:8000/api/v1/auth/refresh",
        headers={
            "Authorization": f"Bearer {token}"
        }
    )
    
    if response.status_code != 200:
        print(f"❌ Token refresh failed: {response.status_code} {response.text}")
        return None
    
    print("✅ Token refresh successful")
    token = response.json().get("access_token")
    
    return token

def test_permission_checks(token):
    '''Test permission checks.'''
    print("Testing permission checks...")
    
    # Test permission check for a specific objective
    response = requests.get(
        "http://localhost:8000/api/v1/permissions/check",
        headers={
            "Authorization": f"Bearer {token}"
        },
        params={
            "objective_id": "go001.lo001",
            "permission": "Execute"
        }
    )
    
    if response.status_code != 200:
        print(f"❌ Permission check failed: {response.status_code} {response.text}")
        return False
    
    result = response.json()
    if not result.get("has_permission"):
        print(f"❌ Permission check failed: User does not have permission")
        return False
    
    print("✅ Permission check successful")
    
    # Test bulk permission check
    response = requests.post(
        "http://localhost:8000/api/v1/permissions/check_bulk",
        headers={
            "Authorization": f"Bearer {token}"
        },
        json={
            "checks": [
                {
                    "objective_id": "go001.lo001",
                    "permission": "Execute"
                },
                {
                    "objective_id": "go001.lo002",
                    "permission": "Execute"
                }
            ]
        }
    )
    
    if response.status_code != 200:
        print(f"❌ Bulk permission check failed: {response.status_code} {response.text}")
        return False
    
    results = response.json().get("results", [])
    if not all(r.get("has_permission") for r in results):
        print(f"❌ Bulk permission check failed: User does not have all permissions")
        return False
    
    print("✅ Bulk permission check successful")
    
    return True

def test_workflow_execution(token):
    '''Test workflow execution with RBAC.'''
    print("Testing workflow execution with RBAC...")
    
    # Test workflow execution
    response = requests.post(
        "http://localhost:8000/api/v1/workflows/execute",
        headers={
            "Authorization": f"Bearer {token}"
        },
        json={
            "objective_id": "go001.lo001",
            "inputs": {
                "in002": "E001",
                "in025": "Annual Leave",
                "in003": "2025-05-01",
                "in004": "2025-05-05",
                "in006": "Vacation"
            }
        }
    )
    
    if response.status_code != 200:
        print(f"❌ Workflow execution failed: {response.status_code} {response.text}")
        return False
    
    print("✅ Workflow execution successful")
    
    return True

def main():
    '''Main function.'''
    print("Running RBAC integration test...")
    
    # Test authentication
    token = test_authentication()
    if not token:
        print("❌ Authentication test failed")
        return False
    
    # Test permission checks
    if not test_permission_checks(token):
        print("❌ Permission check test failed")
        return False
    
    # Test workflow execution
    if not test_workflow_execution(token):
        print("❌ Workflow execution test failed")
        return False
    
    print("✅ All tests passed!")
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
