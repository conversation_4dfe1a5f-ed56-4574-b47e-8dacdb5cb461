#!/usr/bin/env python3
"""
Script to recreate the lo_input_items table in the workflow_temp schema.
"""

import os
import sys
import logging

# Add parent directory to path to import common modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from common.db_connection import get_db_connection
from db_utils import execute_query

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger('recreate_lo_input_items')

def main():
    """
    Main function.
    """
    # Get the structure of the lo_input_items table from workflow_runtime
    query = """
        SELECT column_name, data_type, column_default, is_nullable
        FROM information_schema.columns
        WHERE table_schema = 'workflow_runtime'
        AND table_name = 'lo_input_items'
        ORDER BY ordinal_position
    """
    
    success, messages, columns = execute_query(query)
    
    if not success:
        logger.error(f"Failed to get columns for workflow_runtime.lo_input_items: {messages}")
        return False
    
    # Drop the lo_input_items table in the workflow_temp schema if it exists
    drop_query = """
        DROP TABLE IF EXISTS workflow_temp.lo_input_items CASCADE
    """
    
    success, messages, _ = execute_query(drop_query)
    
    if not success:
        logger.error(f"Failed to drop table workflow_temp.lo_input_items: {messages}")
        return False
    
    logger.info("Dropped table workflow_temp.lo_input_items")
    
    # Create the lo_input_items table in the workflow_temp schema
    column_defs = []
    for column in columns:
        column_name = column[0]
        data_type = column[1]
        default_value = column[2]
        is_nullable = column[3]
        
        column_def = f"{column_name} {data_type}"
        
        if default_value:
            column_def += f" DEFAULT {default_value}"
        
        if is_nullable == 'NO':
            column_def += " NOT NULL"
        
        column_defs.append(column_def)
    
    create_table_query = f"""
        CREATE TABLE workflow_temp.lo_input_items (
            {', '.join(column_defs)}
        )
    """
    
    success, messages, _ = execute_query(create_table_query)
    
    if not success:
        logger.error(f"Failed to create table workflow_temp.lo_input_items: {messages}")
        return False
    
    logger.info("Created table workflow_temp.lo_input_items with the correct structure")
    
    # Add foreign key constraints
    add_fk_query = """
        ALTER TABLE workflow_temp.lo_input_items
        ADD CONSTRAINT lo_input_items_lo_id_fkey
        FOREIGN KEY (lo_id)
        REFERENCES workflow_temp.local_objectives(lo_id);
        
        ALTER TABLE workflow_temp.lo_input_items
        ADD CONSTRAINT lo_input_items_input_stack_id_fkey
        FOREIGN KEY (input_stack_id)
        REFERENCES workflow_temp.lo_input_stack(id);
    """
    
    success, messages, _ = execute_query(add_fk_query)
    
    if not success:
        logger.error(f"Failed to add foreign key constraints: {messages}")
        return False
    
    logger.info("Added foreign key constraints to workflow_temp.lo_input_items")
    
    # Verify the table was created correctly
    verify_query = """
        SELECT column_name, data_type
        FROM information_schema.columns
        WHERE table_schema = 'workflow_temp'
        AND table_name = 'lo_input_items'
        ORDER BY ordinal_position
    """
    
    success, messages, result = execute_query(verify_query)
    
    if not success:
        logger.error(f"Failed to verify table workflow_temp.lo_input_items: {messages}")
        return False
    
    logger.info("Table workflow_temp.lo_input_items created successfully with the following columns:")
    for column in result:
        logger.info(f"  {column[0]}: {column[1]}")
    
    return True

if __name__ == '__main__':
    main()
