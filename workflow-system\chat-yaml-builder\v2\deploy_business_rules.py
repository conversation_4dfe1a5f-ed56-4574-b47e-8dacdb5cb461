#!/usr/bin/env python3
"""
Script to deploy business rules to the database.
"""

import os
import logging
from parsers.entity_parser import parse_entities
from deployers.entity_deployer_v2 import execute_query

# Set up logging
logging.basicConfig(level=logging.INFO, 
                   format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
                   handlers=[
                       logging.FileHandler('/home/<USER>/workflow-system/chat-yaml-builder/v2/business_rule_deployment.log'),
                       logging.StreamHandler()
                   ])
logger = logging.getLogger('deploy_business_rules')

def deploy_business_rules():
    """
    Deploy business rules to the database.
    """
    # Read the sample entity output file
    sample_file_path = '/home/<USER>/workflow-system/chat-yaml-builder/v2/samples/sample_entity_output2.txt'
    with open(sample_file_path, 'r') as f:
        entity_def = f.read()
    
    # Add the specific business rule we want to deploy
    business_rule = """
BusinessRule EMP001 for Employee:
* Employee.status must be Active to receive performance reviews
* Employee.hireDate must be at least 90 days before Employee.performanceRating can be set
"""
    
    # Append the business rule to the entity definition
    entity_def_with_rule = entity_def + business_rule
    
    # Parse the entity definition
    entities_data, warnings = parse_entities(entity_def_with_rule)
    
    # Print any warnings
    if warnings:
        logger.warning("Warnings during parsing:")
        for warning in warnings:
            logger.warning(f"  - {warning}")
    
    # Check if Employee entity was parsed
    if 'entities' in entities_data and 'Employee' in entities_data['entities']:
        employee_entity = entities_data['entities']['Employee']
        
        # Check if business rules were parsed
        if 'business_rules' in employee_entity:
            logger.info("\nEmployee business rules:")
            for rule_id, rule_def in employee_entity['business_rules'].items():
                logger.info(f"\n  - Business Rule: {rule_id}")
                if 'conditions' in rule_def:
                    for i, condition in enumerate(rule_def['conditions']):
                        logger.info(f"    - Condition {i+1}: {condition}")
                else:
                    logger.warning(f"No conditions found in business rule '{rule_id}'")
        else:
            logger.warning("No business rules found in Employee entity")
            return
    else:
        logger.error("Employee entity not found in parsed data")
        return
    
    # Create the entity_business_rules table if it doesn't exist
    schema_name = 'workflow_temp'
    success, messages, result = execute_query(
        f"""
        CREATE TABLE IF NOT EXISTS {schema_name}.entity_business_rules (
            rule_id VARCHAR(255) PRIMARY KEY,
            entity_id VARCHAR(255) NOT NULL,
            name VARCHAR(255) NOT NULL,
            description TEXT,
            created_at TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (entity_id) REFERENCES {schema_name}.entities(entity_id)
        )
        """,
        schema_name=schema_name
    )
    
    if not success:
        logger.error(f"Failed to create entity_business_rules table: {messages}")
        return
    
    # Create the business_rule_conditions table if it doesn't exist
    success, messages, result = execute_query(
        f"""
        CREATE TABLE IF NOT EXISTS {schema_name}.business_rule_conditions (
            condition_id SERIAL PRIMARY KEY,
            rule_id VARCHAR(255) NOT NULL,
            condition_text TEXT NOT NULL,
            attribute_name VARCHAR(255),
            operator VARCHAR(255),
            value TEXT,
            created_at TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (rule_id) REFERENCES {schema_name}.entity_business_rules(rule_id)
        )
        """,
        schema_name=schema_name
    )
    
    if not success:
        logger.error(f"Failed to create business_rule_conditions table: {messages}")
        return
    
    # Get the Employee entity ID from the database
    success, messages, result = execute_query(
        f"SELECT entity_id FROM {schema_name}.entities WHERE name = 'Employee'",
        schema_name=schema_name
    )
    
    if not success or not result:
        logger.error("Employee entity not found in the database")
        return
    
    employee_id = result[0][0]
    logger.info(f"Found Employee entity in database with ID: {employee_id}")
    
    # Deploy each business rule
    for rule_id, rule_def in employee_entity['business_rules'].items():
        # Check if the rule already exists
        success, messages, result = execute_query(
            f"SELECT rule_id FROM {schema_name}.entity_business_rules WHERE rule_id = %s",
            (rule_id,),
            schema_name
        )
        
        if success and result:
            # Rule already exists, update it
            logger.info(f"Business rule '{rule_id}' already exists, updating it")
            
            success, messages, result = execute_query(
                f"""
                UPDATE {schema_name}.entity_business_rules
                SET updated_at = CURRENT_TIMESTAMP
                WHERE rule_id = %s
                """,
                (rule_id,),
                schema_name
            )
            
            if not success:
                logger.error(f"Failed to update business rule '{rule_id}': {messages}")
                continue
            
            # Delete existing conditions
            success, messages, result = execute_query(
                f"DELETE FROM {schema_name}.business_rule_conditions WHERE rule_id = %s",
                (rule_id,),
                schema_name
            )
            
            if not success:
                logger.error(f"Failed to delete conditions for business rule '{rule_id}': {messages}")
                continue
        else:
            # Rule doesn't exist, create it
            logger.info(f"Creating new business rule '{rule_id}'")
            
            # Use the explicit ID if available, otherwise use the rule_id
            rule_name = rule_def.get('id', rule_id)
            
            success, messages, result = execute_query(
                f"""
                INSERT INTO {schema_name}.entity_business_rules (
                    rule_id, entity_id, name, description, created_at, updated_at
                ) VALUES (%s, %s, %s, %s, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
                """,
                (rule_id, employee_id, rule_name, ""),
                schema_name
            )
            
            if not success:
                logger.error(f"Failed to create business rule '{rule_id}': {messages}")
                continue
        
        # Add conditions
        if 'conditions' in rule_def:
            for condition in rule_def['conditions']:
                # Parse the condition to extract attribute, operator, and value
                attribute_name = None
                operator = None
                value = None
                
                # Try to parse conditions like "Employee.status must be Active to receive performance reviews"
                if '.' in condition and ' must ' in condition:
                    parts = condition.split(' must ')
                    if len(parts) == 2:
                        entity_attr = parts[0].strip()
                        constraint = parts[1].strip()
                        
                        if '.' in entity_attr:
                            entity_name, attribute_name = entity_attr.split('.')
                            
                            # Extract operator and value
                            if ' be ' in constraint:
                                operator = 'be'
                                value = constraint.split(' be ')[1].strip()
                                
                                # If value contains "to", extract the part before "to"
                                if ' to ' in value:
                                    value = value.split(' to ')[0].strip()
                            elif ' match ' in constraint:
                                operator = 'match'
                                value = constraint.split(' match ')[1].strip()
                            elif ' belong ' in constraint:
                                operator = 'belong'
                                value = constraint.split(' belong ')[1].strip()
                
                success, messages, result = execute_query(
                    f"""
                    INSERT INTO {schema_name}.business_rule_conditions (
                        rule_id, condition_text, attribute_name, operator, value,
                        created_at, updated_at
                    ) VALUES (%s, %s, %s, %s, %s, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
                    """,
                    (rule_id, condition, attribute_name, operator, value),
                    schema_name
                )
                
                if not success:
                    logger.error(f"Failed to create condition for business rule '{rule_id}': {messages}")
                    continue
                
                logger.info(f"Added condition to business rule '{rule_id}': {condition}")
        
        logger.info(f"Successfully deployed business rule '{rule_id}'")
    
    # Verify the business rules were deployed
    success, messages, result = execute_query(
        f"""
        SELECT er.rule_id, er.name, er.entity_id, e.name as entity_name
        FROM {schema_name}.entity_business_rules er
        JOIN {schema_name}.entities e ON er.entity_id = e.entity_id
        WHERE e.name = 'Employee'
        """,
        schema_name=schema_name
    )
    
    if success and result:
        logger.info("\nEmployee business rules in the database:")
        logger.info("=" * 80)
        logger.info(f"{'Rule ID':<20} {'Name':<20} {'Entity ID':<20} {'Entity Name':<20}")
        logger.info("-" * 80)
        for row in result:
            rule_id = row[0]
            name = row[1]
            entity_id = row[2]
            entity_name = row[3]
            logger.info(f"{rule_id:<20} {name:<20} {entity_id:<20} {entity_name:<20}")
        logger.info("=" * 80)
        
        # Get conditions for each rule
        for row in result:
            rule_id = row[0]
            
            success, messages, result = execute_query(
                f"""
                SELECT condition_text, attribute_name, operator, value
                FROM {schema_name}.business_rule_conditions
                WHERE rule_id = %s
                """,
                (rule_id,),
                schema_name
            )
            
            if success and result:
                logger.info(f"\nConditions for business rule '{rule_id}':")
                logger.info("=" * 80)
                logger.info(f"{'Condition':<60} {'Attribute':<15} {'Operator':<15} {'Value':<20}")
                logger.info("-" * 80)
                for row in result:
                    condition = row[0]
                    attribute = row[1] if row[1] else "N/A"
                    operator = row[2] if row[2] else "N/A"
                    value = row[3] if row[3] else "N/A"
                    logger.info(f"{condition:<60} {attribute:<15} {operator:<15} {value:<20}")
                logger.info("=" * 80)
    else:
        logger.warning("No Employee business rules found in the database")

if __name__ == "__main__":
    deploy_business_rules()
