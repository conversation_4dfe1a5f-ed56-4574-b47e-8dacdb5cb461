{"lo_input_items": {"id": 1, "item_id": "string", "input_stack_id": "string", "slot_id": "string", "source_type": "string", "source_description": "string", "required": true, "lo_id": "string", "data_type": "string", "ui_control": "string", "nested_function_id": "string", "is_visible": true, "name": "string", "type": "string", "created_at": "2025-06-10T14:20:00Z", "updated_at": "2025-06-10T14:20:00Z", "created_by": "string", "updated_by": "string", "read_only": true, "agent_type": "string", "dependent_attribute": true, "dependent_attribute_value": "string", "enum_values": {}, "default_value": "string", "information_field": true, "constant_field": true, "entity_id": "string", "attribute_id": "string", "entity_name": "string", "attribute_name": "string", "version": "string", "natural_language": "string"}, "lo_output_items": {"id": 1, "output_stack_id": "string", "slot_id": "string", "source": "string", "created_at": "2025-06-10T14:20:00Z", "updated_at": "2025-06-10T14:20:00Z", "lo_id": "string", "item_id": "string", "name": "string", "type": "string", "nested_function_id": "string", "created_by": "string", "updated_by": "string", "entity_id": "string", "attribute_id": "string", "entity_name": "string", "attribute_name": "string", "version": "string", "natural_language": "string"}, "lo_input_stacks": {"id": 1, "stack_id": "string", "lo_id": "string", "description": "string", "created_at": "2025-06-10T14:20:00Z", "updated_at": "2025-06-10T14:20:00Z", "created_by": "string", "updated_by": "string", "version": "string", "natural_language": "string"}, "lo_output_stacks": {"id": 1, "stack_id": "string", "lo_id": "string", "description": "string", "created_at": "2025-06-10T14:20:00Z", "updated_at": "2025-06-10T14:20:00Z", "created_by": "string", "updated_by": "string", "version": "string", "natural_language": "string"}, "lo_output_mappings": {"id": 1, "mapping_id": "string", "lo_id": "string", "source_output_item_id": "string", "target_input_item_id": "string", "created_at": "2025-06-10T14:20:00Z", "updated_at": "2025-06-10T14:20:00Z", "created_by": "string", "updated_by": "string", "version": "string", "natural_language": "string"}, "lo_nested_function_input_items": {"id": 1, "item_id": "string", "input_stack_id": "string", "nested_function_id": "string", "slot_id": "string", "contextual_id": "string", "source_type": "string", "source_description": "string", "required": true, "data_type": "string", "value": "string", "reference_input_item_id": "string", "entity_id": "string", "attribute_id": "string", "created_at": "2025-06-10T14:20:00Z", "updated_at": "2025-06-10T14:20:00Z", "created_by": "string", "updated_by": "string", "version": "string", "natural_language": "string"}, "lo_nested_function_input_stacks": {"id": 1, "stack_id": "string", "nested_function_id": "string", "description": "string", "created_at": "2025-06-10T14:20:00Z", "updated_at": "2025-06-10T14:20:00Z", "created_by": "string", "updated_by": "string", "version": "string", "natural_language": "string"}, "lo_nested_function_output_stacks": {"id": 1, "stack_id": "string", "nested_function_id": "string", "description": "string", "created_at": "2025-06-10T14:20:00Z", "updated_at": "2025-06-10T14:20:00Z", "created_by": "string", "updated_by": "string", "version": "string", "natural_language": "string"}, "lo_nested_function_output_items": {"id": 1, "item_id": "string", "output_stack_id": "string", "nested_function_id": "string", "slot_id": "string", "contextual_id": "string", "data_type": "string", "entity_id": "string", "attribute_id": "string", "created_at": "2025-06-10T14:20:00Z", "updated_at": "2025-06-10T14:20:00Z", "created_by": "string", "updated_by": "string", "version": "string", "natural_language": "string"}, "lo_nested_function_mappings": {"id": 1, "mapping_id": "string", "nested_function_id": "string", "lo_id": "string", "source_output_item_id": "string", "target_input_item_id": "string", "created_at": "2025-06-10T14:20:00Z", "updated_at": "2025-06-10T14:20:00Z", "created_by": "string", "updated_by": "string", "version": "string", "natural_language": "string"}, "lo_nested_functions": {"id": 1, "lo_id": "string", "nested_function_id": "string", "function_name": "string", "function_type": "string", "parameters": {}, "output_to": "string", "description": "string", "returns": "string", "success_message": "string", "error_message": "string", "created_at": "2025-06-10T14:20:00Z", "updated_at": "2025-06-10T14:20:00Z", "created_by": "string", "updated_by": "string", "version": "string", "natural_language": "string"}, "global_objectives": {"go_id": "string", "name": "string", "version": "string", "status": "string", "description": "string", "created_at": "2025-06-10T14:20:00Z", "updated_at": "2025-06-10T14:20:00Z", "tenant_id": "string", "last_used": "2025-06-10T14:20:00Z", "deleted_mark": false, "version_type": "string", "metadata": {}, "auto_id": 1, "primary_entity": "string", "classification": "string", "tenant_name": "string", "book_id": "string", "book_name": "string", "chapter_id": "string", "chapter_name": "string", "created_by": "string", "updated_by": "string", "natural_language": "string"}, "execution_pathways": {"id": "string", "execution_pathway_id": "string", "lo_id": "string", "pathway_type": "string", "next_lo": "string", "created_at": "2025-06-10T14:20:00Z", "created_by": "string", "updated_at": "2025-06-10T14:20:00Z", "updated_by": "string", "version": "string", "natural_language": "string"}, "execution_pathway_conditions": {"id": 1, "execution_pathway_condition_id": "string", "lo_id": "string", "condition_type": "string", "condition_entity": "string", "condition_attribute": "string", "condition_operator": "string", "condition_value": "string", "multiple_conditions": "string", "multiple_conditions_operator": "string", "next_lo": "string", "created_at": "2025-06-10T14:20:00Z", "created_by": "string", "updated_at": "2025-06-10T14:20:00Z", "updated_by": "string", "version": "string", "natural_language": "string"}}