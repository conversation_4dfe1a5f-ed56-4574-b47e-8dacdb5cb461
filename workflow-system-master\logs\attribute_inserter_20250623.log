{"timestamp": "2025-06-23T11:26:31.805189", "operation": "deploy_single_attribute_validation_to_workflow_temp", "input_data": {"validation_id": 4}, "result": {"success": false, "error": "Attribute validation 4 not found with status draft", "validation_id": 4, "required_status": "draft"}, "status": "error"}
{"timestamp": "2025-06-23T11:28:18.040091", "operation": "deploy_single_attribute_validation_to_workflow_temp", "input_data": {"validation_id": 4}, "result": {"success": false, "error": "Attribute validation 4 not found with status draft", "validation_id": 4, "required_status": "draft"}, "status": "error"}
{"timestamp": "2025-06-23T14:03:48.492017", "operation": "insert_attribute_validation_to_workflow_temp", "input_data": {"_id": "6852ee1a727e97ac18d0839e", "validation_id": 186, "attribute_id": "E8.At22", "entity_name": "Employee", "attribute_name": "employeeId", "left_operand": "employeeId", "operator": "IS_UNIQUE", "right_operand": "", "success_value": "unique", "warning_value": "", "failure_value": "duplicate", "multi_condition_operator": "FALSE", "warning_message": "", "success_message": "Employee ID is valid", "error_message": "Employee ID must be unique", "natural_language": "Employee.employeeId: employeeId IS_UNIQUE ", "version": 1, "status": "draft", "created_at": "2025-06-18T16:49:30.448481", "updated_at": "2025-06-18T16:49:30.448484", "created_by": "system", "updated_by": "system"}, "result": {"success": true, "inserted_id": 42, "schema": "workflow_temp", "validation_id": 186, "attribute_id": "E8.At22", "original_validation_id": 1, "original_attribute_id": "E8.At1"}, "status": "success"}
{"timestamp": "2025-06-23T14:03:48.494648", "operation": "deploy_single_attribute_validation_to_workflow_temp", "input_data": {"_id": "6852ee1a727e97ac18d0839e", "validation_id": 186, "attribute_id": "E8.At22", "entity_name": "Employee", "attribute_name": "employeeId", "left_operand": "employeeId", "operator": "IS_UNIQUE", "right_operand": "", "success_value": "unique", "warning_value": "", "failure_value": "duplicate", "multi_condition_operator": "FALSE", "warning_message": "", "success_message": "Employee ID is valid", "error_message": "Employee ID must be unique", "natural_language": "Employee.employeeId: employeeId IS_UNIQUE ", "version": 1, "status": "draft", "created_at": "2025-06-18T16:49:30.448481", "updated_at": "2025-06-18T16:49:30.448484", "created_by": "system", "updated_by": "system"}, "result": {"success": true, "inserted_id": 42, "schema": "workflow_temp", "validation_id": 186, "attribute_id": "E8.At22", "original_validation_id": 1, "original_attribute_id": "E8.At1"}, "status": "success"}
{"timestamp": "2025-06-23T14:03:48.656710", "operation": "insert_attribute_validation_to_workflow_temp", "input_data": {"_id": "6854488a754fbf4d729d3a7a", "validation_id": 196, "attribute_id": "E8.At23", "entity_name": "Employee", "attribute_name": "email", "left_operand": "email", "operator": "IS_UNIQUE", "right_operand": "", "success_value": "unique", "warning_value": "", "failure_value": "duplicate", "multi_condition_operator": "FALSE", "warning_message": "", "success_message": "Email is unique", "error_message": "Email must be unique", "natural_language": "Employee.email: email IS_UNIQUE ", "version": 1, "status": "draft", "created_at": "2025-06-19T17:27:38.404018", "updated_at": "2025-06-19T17:27:38.404025", "created_by": "system", "updated_by": "system"}, "result": {"success": true, "inserted_id": 43, "schema": "workflow_temp", "validation_id": 196, "attribute_id": "E8.At23", "original_validation_id": 2, "original_attribute_id": "E8.At4"}, "status": "success"}
{"timestamp": "2025-06-23T14:03:48.692755", "operation": "insert_attribute_validation_to_workflow_temp", "input_data": {"_id": "6854ee73fa0d6bfc16038bef", "validation_id": 206, "attribute_id": "E8.At24", "entity_name": "Employee", "attribute_name": "email", "left_operand": "email", "operator": "IS_UNIQUE", "right_operand": "", "success_value": "unique", "warning_value": "", "failure_value": "duplicate", "multi_condition_operator": "FALSE", "warning_message": "", "success_message": "Email is unique", "error_message": "Email must be unique", "natural_language": "Employee.email: email IS_UNIQUE ", "version": 1, "status": "draft", "created_at": "2025-06-20T05:15:31.135351", "updated_at": "2025-06-20T05:15:31.135357", "created_by": "system", "updated_by": "system"}, "result": {"success": true, "inserted_id": 44, "schema": "workflow_temp", "validation_id": 206, "attribute_id": "E8.At24", "original_validation_id": 6, "original_attribute_id": "E8.At4"}, "status": "success"}
{"timestamp": "2025-06-23T14:03:48.724713", "operation": "insert_attribute_validation_to_workflow_temp", "input_data": {"_id": "6854ee73fa0d6bfc16038bf0", "validation_id": 216, "attribute_id": "E8.At25", "entity_name": "Employee", "attribute_name": "employeeId", "left_operand": "employeeId", "operator": "IS_UNIQUE", "right_operand": "", "success_value": "unique", "warning_value": "", "failure_value": "duplicate", "multi_condition_operator": "FALSE", "warning_message": "", "success_message": "Employee ID is valid", "error_message": "Employee ID must be unique", "natural_language": "Employee.employeeId: employeeId IS_UNIQUE ", "version": 1, "status": "draft", "created_at": "2025-06-20T05:15:31.135903", "updated_at": "2025-06-20T05:15:31.135906", "created_by": "system", "updated_by": "system"}, "result": {"success": true, "inserted_id": 45, "schema": "workflow_temp", "validation_id": 216, "attribute_id": "E8.At25", "original_validation_id": 5, "original_attribute_id": "E8.At1"}, "status": "success"}
{"timestamp": "2025-06-23T14:03:48.757557", "operation": "insert_attribute_validation_to_workflow_temp", "input_data": {"_id": "6854f3ac9031164ff452a0f8", "validation_id": 226, "attribute_id": "E8.At26", "entity_name": "Employee", "attribute_name": "phone", "left_operand": "phone", "operator": "IS_UNIQUE", "right_operand": "", "success_value": "unique", "warning_value": "", "failure_value": "duplicate", "multi_condition_operator": "FALSE", "warning_message": "", "success_message": "Phone number is unique", "error_message": "Phone number must be unique", "natural_language": "Employee.phone: phone IS_UNIQUE ", "version": 1, "status": "draft", "created_at": "2025-06-20T05:37:48.522400", "updated_at": "2025-06-20T05:37:48.522404", "created_by": "system", "updated_by": "system"}, "result": {"success": true, "inserted_id": 46, "schema": "workflow_temp", "validation_id": 226, "attribute_id": "E8.At26", "original_validation_id": 7, "original_attribute_id": "E8.At4"}, "status": "success"}
{"timestamp": "2025-06-23T14:03:48.791721", "operation": "insert_attribute_validation_to_workflow_temp", "input_data": {"_id": "685508447147def4f91d1022", "validation_id": 236, "attribute_id": "E7.At20", "entity_name": "LeaveApplication", "attribute_name": "leaveId", "left_operand": "leaveId", "operator": "IS_UNIQUE", "right_operand": "", "success_value": "unique", "warning_value": "", "failure_value": "duplicate", "multi_condition_operator": "FALSE", "warning_message": "", "success_message": "Leave ID is valid", "error_message": "Leave ID must be unique", "natural_language": "LeaveApplication.leaveId: leaveId IS_UNIQUE ", "version": 1, "status": "draft", "created_at": "2025-06-20T07:05:40.927340", "updated_at": "2025-06-20T07:05:40.927345", "created_by": "system", "updated_by": "system"}, "result": {"success": true, "inserted_id": 47, "schema": "workflow_temp", "validation_id": 236, "attribute_id": "E7.At20", "original_validation_id": 1, "original_attribute_id": "E7.At1"}, "status": "success"}
{"timestamp": "2025-06-23T14:03:48.823682", "operation": "insert_attribute_validation_to_workflow_temp", "input_data": {"_id": "685508447147def4f91d1023", "validation_id": 246, "attribute_id": "E7.At21", "entity_name": "LeaveApplication", "attribute_name": "employeeId", "left_operand": "employeeId", "operator": "EXISTS_IN", "right_operand": "Employee.employeeId", "success_value": "exists", "warning_value": "", "failure_value": "not_exists", "multi_condition_operator": "FALSE", "warning_message": "", "success_message": "Employee ID is valid", "error_message": "Employee ID must exist in Employee table", "natural_language": "LeaveApplication.employeeId: employeeId EXISTS_IN Employee.employeeId", "version": 1, "status": "draft", "created_at": "2025-06-20T07:05:40.932582", "updated_at": "2025-06-20T07:05:40.932597", "created_by": "system", "updated_by": "system"}, "result": {"success": true, "inserted_id": 48, "schema": "workflow_temp", "validation_id": 246, "attribute_id": "E7.At21", "original_validation_id": 2, "original_attribute_id": "E7.At2"}, "status": "success"}
{"timestamp": "2025-06-23T14:03:48.851360", "operation": "insert_attribute_validation_to_workflow_temp", "input_data": {"_id": "685508447147def4f91d1024", "validation_id": 256, "attribute_id": "E7.At22", "entity_name": "LeaveApplication", "attribute_name": "startDate", "left_operand": "startDate", "operator": "IS_VALID_DATE", "right_operand": "", "success_value": "valid_date", "warning_value": "", "failure_value": "invalid_date", "multi_condition_operator": "FALSE", "warning_message": "", "success_message": "Start date is valid", "error_message": "Start date must be a valid date", "natural_language": "LeaveApplication.startDate: startDate IS_VALID_DATE ", "version": 1, "status": "draft", "created_at": "2025-06-20T07:05:40.934871", "updated_at": "2025-06-20T07:05:40.934878", "created_by": "system", "updated_by": "system"}, "result": {"success": true, "inserted_id": 49, "schema": "workflow_temp", "validation_id": 256, "attribute_id": "E7.At22", "original_validation_id": 3, "original_attribute_id": "E7.At3"}, "status": "success"}
{"timestamp": "2025-06-23T14:03:48.875955", "operation": "insert_attribute_validation_to_workflow_temp", "input_data": {"_id": "685508447147def4f91d1025", "validation_id": 266, "attribute_id": "E7.At23", "entity_name": "LeaveApplication", "attribute_name": "endDate", "left_operand": "endDate", "operator": "GREATER_THAN", "right_operand": "startDate", "success_value": "after_start", "warning_value": "", "failure_value": "before_or_equal_start", "multi_condition_operator": "FALSE", "warning_message": "", "success_message": "End date is valid", "error_message": "End date must be after start date", "natural_language": "LeaveApplication.endDate: endDate GREATER_THAN startDate", "version": 1, "status": "draft", "created_at": "2025-06-20T07:05:40.936694", "updated_at": "2025-06-20T07:05:40.936701", "created_by": "system", "updated_by": "system"}, "result": {"success": true, "inserted_id": 50, "schema": "workflow_temp", "validation_id": 266, "attribute_id": "E7.At23", "original_validation_id": 4, "original_attribute_id": "E7.At4"}, "status": "success"}
{"timestamp": "2025-06-23T14:03:48.877924", "operation": "process_mongo_attribute_validations_to_workflow_temp", "input_data": {"schema": "workflow_temp"}, "result": {"total_processed": 8, "successful_inserts": 8, "failed_inserts": 0, "details": [{"validation_id": 196, "attribute_id": "E8.At23", "status": "success", "details": {"success": true, "inserted_id": 43, "schema": "workflow_temp", "validation_id": 196, "attribute_id": "E8.At23", "original_validation_id": 2, "original_attribute_id": "E8.At4"}}, {"validation_id": 206, "attribute_id": "E8.At24", "status": "success", "details": {"success": true, "inserted_id": 44, "schema": "workflow_temp", "validation_id": 206, "attribute_id": "E8.At24", "original_validation_id": 6, "original_attribute_id": "E8.At4"}}, {"validation_id": 216, "attribute_id": "E8.At25", "status": "success", "details": {"success": true, "inserted_id": 45, "schema": "workflow_temp", "validation_id": 216, "attribute_id": "E8.At25", "original_validation_id": 5, "original_attribute_id": "E8.At1"}}, {"validation_id": 226, "attribute_id": "E8.At26", "status": "success", "details": {"success": true, "inserted_id": 46, "schema": "workflow_temp", "validation_id": 226, "attribute_id": "E8.At26", "original_validation_id": 7, "original_attribute_id": "E8.At4"}}, {"validation_id": 236, "attribute_id": "E7.At20", "status": "success", "details": {"success": true, "inserted_id": 47, "schema": "workflow_temp", "validation_id": 236, "attribute_id": "E7.At20", "original_validation_id": 1, "original_attribute_id": "E7.At1"}}, {"validation_id": 246, "attribute_id": "E7.At21", "status": "success", "details": {"success": true, "inserted_id": 48, "schema": "workflow_temp", "validation_id": 246, "attribute_id": "E7.At21", "original_validation_id": 2, "original_attribute_id": "E7.At2"}}, {"validation_id": 256, "attribute_id": "E7.At22", "status": "success", "details": {"success": true, "inserted_id": 49, "schema": "workflow_temp", "validation_id": 256, "attribute_id": "E7.At22", "original_validation_id": 3, "original_attribute_id": "E7.At3"}}, {"validation_id": 266, "attribute_id": "E7.At23", "status": "success", "details": {"success": true, "inserted_id": 50, "schema": "workflow_temp", "validation_id": 266, "attribute_id": "E7.At23", "original_validation_id": 4, "original_attribute_id": "E7.At4"}}]}, "status": "success"}
{"timestamp": "2025-06-23T14:08:59.947160", "operation": "insert_attribute_validation_to_workflow_temp", "input_data": {"_id": "6852ee1a727e97ac18d0839e", "validation_id": 276, "attribute_id": "E8.At27", "entity_name": "Employee", "attribute_name": "employeeId", "left_operand": "employeeId", "operator": "IS_UNIQUE", "right_operand": "", "success_value": "unique", "warning_value": "", "failure_value": "duplicate", "multi_condition_operator": "FALSE", "warning_message": "", "success_message": "Employee ID is valid", "error_message": "Employee ID must be unique", "natural_language": "Employee.employeeId: employeeId IS_UNIQUE ", "version": 1, "status": "draft", "created_at": "2025-06-18T16:49:30.448481", "updated_at": "2025-06-18T16:49:30.448484", "created_by": "system", "updated_by": "system"}, "result": {"success": true, "inserted_id": 51, "schema": "workflow_temp", "validation_id": 276, "attribute_id": "E8.At27", "original_validation_id": 1, "original_attribute_id": "E8.At1"}, "status": "success"}
{"timestamp": "2025-06-23T14:08:59.949622", "operation": "deploy_single_attribute_validation_to_workflow_temp", "input_data": {"_id": "6852ee1a727e97ac18d0839e", "validation_id": 276, "attribute_id": "E8.At27", "entity_name": "Employee", "attribute_name": "employeeId", "left_operand": "employeeId", "operator": "IS_UNIQUE", "right_operand": "", "success_value": "unique", "warning_value": "", "failure_value": "duplicate", "multi_condition_operator": "FALSE", "warning_message": "", "success_message": "Employee ID is valid", "error_message": "Employee ID must be unique", "natural_language": "Employee.employeeId: employeeId IS_UNIQUE ", "version": 1, "status": "draft", "created_at": "2025-06-18T16:49:30.448481", "updated_at": "2025-06-18T16:49:30.448484", "created_by": "system", "updated_by": "system"}, "result": {"success": true, "inserted_id": 51, "schema": "workflow_temp", "validation_id": 276, "attribute_id": "E8.At27", "original_validation_id": 1, "original_attribute_id": "E8.At1"}, "status": "success"}
{"timestamp": "2025-06-23T14:09:00.117338", "operation": "insert_attribute_validation_to_workflow_temp", "input_data": {"_id": "6854488a754fbf4d729d3a7a", "validation_id": 286, "attribute_id": "E8.At28", "entity_name": "Employee", "attribute_name": "email", "left_operand": "email", "operator": "IS_UNIQUE", "right_operand": "", "success_value": "unique", "warning_value": "", "failure_value": "duplicate", "multi_condition_operator": "FALSE", "warning_message": "", "success_message": "Email is unique", "error_message": "Email must be unique", "natural_language": "Employee.email: email IS_UNIQUE ", "version": 1, "status": "draft", "created_at": "2025-06-19T17:27:38.404018", "updated_at": "2025-06-19T17:27:38.404025", "created_by": "system", "updated_by": "system"}, "result": {"success": true, "inserted_id": 52, "schema": "workflow_temp", "validation_id": 286, "attribute_id": "E8.At28", "original_validation_id": 2, "original_attribute_id": "E8.At4"}, "status": "success"}
{"timestamp": "2025-06-23T14:09:00.147861", "operation": "insert_attribute_validation_to_workflow_temp", "input_data": {"_id": "6854ee73fa0d6bfc16038bef", "validation_id": 296, "attribute_id": "E8.At29", "entity_name": "Employee", "attribute_name": "email", "left_operand": "email", "operator": "IS_UNIQUE", "right_operand": "", "success_value": "unique", "warning_value": "", "failure_value": "duplicate", "multi_condition_operator": "FALSE", "warning_message": "", "success_message": "Email is unique", "error_message": "Email must be unique", "natural_language": "Employee.email: email IS_UNIQUE ", "version": 1, "status": "draft", "created_at": "2025-06-20T05:15:31.135351", "updated_at": "2025-06-20T05:15:31.135357", "created_by": "system", "updated_by": "system"}, "result": {"success": true, "inserted_id": 53, "schema": "workflow_temp", "validation_id": 296, "attribute_id": "E8.At29", "original_validation_id": 6, "original_attribute_id": "E8.At4"}, "status": "success"}
{"timestamp": "2025-06-23T14:09:00.178951", "operation": "insert_attribute_validation_to_workflow_temp", "input_data": {"_id": "6854ee73fa0d6bfc16038bf0", "validation_id": 306, "attribute_id": "E8.At30", "entity_name": "Employee", "attribute_name": "employeeId", "left_operand": "employeeId", "operator": "IS_UNIQUE", "right_operand": "", "success_value": "unique", "warning_value": "", "failure_value": "duplicate", "multi_condition_operator": "FALSE", "warning_message": "", "success_message": "Employee ID is valid", "error_message": "Employee ID must be unique", "natural_language": "Employee.employeeId: employeeId IS_UNIQUE ", "version": 1, "status": "draft", "created_at": "2025-06-20T05:15:31.135903", "updated_at": "2025-06-20T05:15:31.135906", "created_by": "system", "updated_by": "system"}, "result": {"success": true, "inserted_id": 54, "schema": "workflow_temp", "validation_id": 306, "attribute_id": "E8.At30", "original_validation_id": 5, "original_attribute_id": "E8.At1"}, "status": "success"}
{"timestamp": "2025-06-23T14:09:00.207411", "operation": "insert_attribute_validation_to_workflow_temp", "input_data": {"_id": "6854f3ac9031164ff452a0f8", "validation_id": 316, "attribute_id": "E8.At31", "entity_name": "Employee", "attribute_name": "phone", "left_operand": "phone", "operator": "IS_UNIQUE", "right_operand": "", "success_value": "unique", "warning_value": "", "failure_value": "duplicate", "multi_condition_operator": "FALSE", "warning_message": "", "success_message": "Phone number is unique", "error_message": "Phone number must be unique", "natural_language": "Employee.phone: phone IS_UNIQUE ", "version": 1, "status": "draft", "created_at": "2025-06-20T05:37:48.522400", "updated_at": "2025-06-20T05:37:48.522404", "created_by": "system", "updated_by": "system"}, "result": {"success": true, "inserted_id": 55, "schema": "workflow_temp", "validation_id": 316, "attribute_id": "E8.At31", "original_validation_id": 7, "original_attribute_id": "E8.At4"}, "status": "success"}
{"timestamp": "2025-06-23T14:09:00.237866", "operation": "insert_attribute_validation_to_workflow_temp", "input_data": {"_id": "685508447147def4f91d1022", "validation_id": 326, "attribute_id": "E7.At24", "entity_name": "LeaveApplication", "attribute_name": "leaveId", "left_operand": "leaveId", "operator": "IS_UNIQUE", "right_operand": "", "success_value": "unique", "warning_value": "", "failure_value": "duplicate", "multi_condition_operator": "FALSE", "warning_message": "", "success_message": "Leave ID is valid", "error_message": "Leave ID must be unique", "natural_language": "LeaveApplication.leaveId: leaveId IS_UNIQUE ", "version": 1, "status": "draft", "created_at": "2025-06-20T07:05:40.927340", "updated_at": "2025-06-20T07:05:40.927345", "created_by": "system", "updated_by": "system"}, "result": {"success": true, "inserted_id": 56, "schema": "workflow_temp", "validation_id": 326, "attribute_id": "E7.At24", "original_validation_id": 1, "original_attribute_id": "E7.At1"}, "status": "success"}
{"timestamp": "2025-06-23T14:09:00.267647", "operation": "insert_attribute_validation_to_workflow_temp", "input_data": {"_id": "685508447147def4f91d1023", "validation_id": 336, "attribute_id": "E7.At25", "entity_name": "LeaveApplication", "attribute_name": "employeeId", "left_operand": "employeeId", "operator": "EXISTS_IN", "right_operand": "Employee.employeeId", "success_value": "exists", "warning_value": "", "failure_value": "not_exists", "multi_condition_operator": "FALSE", "warning_message": "", "success_message": "Employee ID is valid", "error_message": "Employee ID must exist in Employee table", "natural_language": "LeaveApplication.employeeId: employeeId EXISTS_IN Employee.employeeId", "version": 1, "status": "draft", "created_at": "2025-06-20T07:05:40.932582", "updated_at": "2025-06-20T07:05:40.932597", "created_by": "system", "updated_by": "system"}, "result": {"success": true, "inserted_id": 57, "schema": "workflow_temp", "validation_id": 336, "attribute_id": "E7.At25", "original_validation_id": 2, "original_attribute_id": "E7.At2"}, "status": "success"}
{"timestamp": "2025-06-23T14:09:00.293246", "operation": "insert_attribute_validation_to_workflow_temp", "input_data": {"_id": "685508447147def4f91d1024", "validation_id": 346, "attribute_id": "E7.At26", "entity_name": "LeaveApplication", "attribute_name": "startDate", "left_operand": "startDate", "operator": "IS_VALID_DATE", "right_operand": "", "success_value": "valid_date", "warning_value": "", "failure_value": "invalid_date", "multi_condition_operator": "FALSE", "warning_message": "", "success_message": "Start date is valid", "error_message": "Start date must be a valid date", "natural_language": "LeaveApplication.startDate: startDate IS_VALID_DATE ", "version": 1, "status": "draft", "created_at": "2025-06-20T07:05:40.934871", "updated_at": "2025-06-20T07:05:40.934878", "created_by": "system", "updated_by": "system"}, "result": {"success": true, "inserted_id": 58, "schema": "workflow_temp", "validation_id": 346, "attribute_id": "E7.At26", "original_validation_id": 3, "original_attribute_id": "E7.At3"}, "status": "success"}
{"timestamp": "2025-06-23T14:09:00.321100", "operation": "insert_attribute_validation_to_workflow_temp", "input_data": {"_id": "685508447147def4f91d1025", "validation_id": 356, "attribute_id": "E7.At27", "entity_name": "LeaveApplication", "attribute_name": "endDate", "left_operand": "endDate", "operator": "GREATER_THAN", "right_operand": "startDate", "success_value": "after_start", "warning_value": "", "failure_value": "before_or_equal_start", "multi_condition_operator": "FALSE", "warning_message": "", "success_message": "End date is valid", "error_message": "End date must be after start date", "natural_language": "LeaveApplication.endDate: endDate GREATER_THAN startDate", "version": 1, "status": "draft", "created_at": "2025-06-20T07:05:40.936694", "updated_at": "2025-06-20T07:05:40.936701", "created_by": "system", "updated_by": "system"}, "result": {"success": true, "inserted_id": 59, "schema": "workflow_temp", "validation_id": 356, "attribute_id": "E7.At27", "original_validation_id": 4, "original_attribute_id": "E7.At4"}, "status": "success"}
{"timestamp": "2025-06-23T14:09:00.322378", "operation": "process_mongo_attribute_validations_to_workflow_temp", "input_data": {"schema": "workflow_temp"}, "result": {"total_processed": 8, "successful_inserts": 8, "failed_inserts": 0, "details": [{"validation_id": 286, "attribute_id": "E8.At28", "status": "success", "details": {"success": true, "inserted_id": 52, "schema": "workflow_temp", "validation_id": 286, "attribute_id": "E8.At28", "original_validation_id": 2, "original_attribute_id": "E8.At4"}}, {"validation_id": 296, "attribute_id": "E8.At29", "status": "success", "details": {"success": true, "inserted_id": 53, "schema": "workflow_temp", "validation_id": 296, "attribute_id": "E8.At29", "original_validation_id": 6, "original_attribute_id": "E8.At4"}}, {"validation_id": 306, "attribute_id": "E8.At30", "status": "success", "details": {"success": true, "inserted_id": 54, "schema": "workflow_temp", "validation_id": 306, "attribute_id": "E8.At30", "original_validation_id": 5, "original_attribute_id": "E8.At1"}}, {"validation_id": 316, "attribute_id": "E8.At31", "status": "success", "details": {"success": true, "inserted_id": 55, "schema": "workflow_temp", "validation_id": 316, "attribute_id": "E8.At31", "original_validation_id": 7, "original_attribute_id": "E8.At4"}}, {"validation_id": 326, "attribute_id": "E7.At24", "status": "success", "details": {"success": true, "inserted_id": 56, "schema": "workflow_temp", "validation_id": 326, "attribute_id": "E7.At24", "original_validation_id": 1, "original_attribute_id": "E7.At1"}}, {"validation_id": 336, "attribute_id": "E7.At25", "status": "success", "details": {"success": true, "inserted_id": 57, "schema": "workflow_temp", "validation_id": 336, "attribute_id": "E7.At25", "original_validation_id": 2, "original_attribute_id": "E7.At2"}}, {"validation_id": 346, "attribute_id": "E7.At26", "status": "success", "details": {"success": true, "inserted_id": 58, "schema": "workflow_temp", "validation_id": 346, "attribute_id": "E7.At26", "original_validation_id": 3, "original_attribute_id": "E7.At3"}}, {"validation_id": 356, "attribute_id": "E7.At27", "status": "success", "details": {"success": true, "inserted_id": 59, "schema": "workflow_temp", "validation_id": 356, "attribute_id": "E7.At27", "original_validation_id": 4, "original_attribute_id": "E7.At4"}}]}, "status": "success"}
{"timestamp": "2025-06-23T14:32:32.167368", "operation": "insert_attribute_validation_to_workflow_temp", "input_data": {"_id": "6852ee1a727e97ac18d0839e", "validation_id": 366, "attribute_id": "E8.At32", "entity_name": "Employee", "attribute_name": "employeeId", "left_operand": "employeeId", "operator": "IS_UNIQUE", "right_operand": "", "success_value": "unique", "warning_value": "", "failure_value": "duplicate", "multi_condition_operator": "FALSE", "warning_message": "", "success_message": "Employee ID is valid", "error_message": "Employee ID must be unique", "natural_language": "Employee.employeeId: employeeId IS_UNIQUE ", "version": 1, "status": "draft", "created_at": "2025-06-18T16:49:30.448481", "updated_at": "2025-06-18T16:49:30.448484", "created_by": "system", "updated_by": "system"}, "result": {"success": true, "inserted_id": 60, "schema": "workflow_temp", "validation_id": 366, "attribute_id": "E8.At32", "original_validation_id": 1, "original_attribute_id": "E8.At1"}, "status": "success"}
{"timestamp": "2025-06-23T14:32:32.170100", "operation": "deploy_single_attribute_validation_to_workflow_temp", "input_data": {"_id": "6852ee1a727e97ac18d0839e", "validation_id": 366, "attribute_id": "E8.At32", "entity_name": "Employee", "attribute_name": "employeeId", "left_operand": "employeeId", "operator": "IS_UNIQUE", "right_operand": "", "success_value": "unique", "warning_value": "", "failure_value": "duplicate", "multi_condition_operator": "FALSE", "warning_message": "", "success_message": "Employee ID is valid", "error_message": "Employee ID must be unique", "natural_language": "Employee.employeeId: employeeId IS_UNIQUE ", "version": 1, "status": "draft", "created_at": "2025-06-18T16:49:30.448481", "updated_at": "2025-06-18T16:49:30.448484", "created_by": "system", "updated_by": "system"}, "result": {"success": true, "inserted_id": 60, "schema": "workflow_temp", "validation_id": 366, "attribute_id": "E8.At32", "original_validation_id": 1, "original_attribute_id": "E8.At1"}, "status": "success"}
{"timestamp": "2025-06-23T14:32:32.361303", "operation": "insert_attribute_validation_to_workflow_temp", "input_data": {"_id": "6854488a754fbf4d729d3a7a", "validation_id": 376, "attribute_id": "E8.At33", "entity_name": "Employee", "attribute_name": "email", "left_operand": "email", "operator": "IS_UNIQUE", "right_operand": "", "success_value": "unique", "warning_value": "", "failure_value": "duplicate", "multi_condition_operator": "FALSE", "warning_message": "", "success_message": "Email is unique", "error_message": "Email must be unique", "natural_language": "Employee.email: email IS_UNIQUE ", "version": 1, "status": "draft", "created_at": "2025-06-19T17:27:38.404018", "updated_at": "2025-06-19T17:27:38.404025", "created_by": "system", "updated_by": "system"}, "result": {"success": true, "inserted_id": 61, "schema": "workflow_temp", "validation_id": 376, "attribute_id": "E8.At33", "original_validation_id": 2, "original_attribute_id": "E8.At4"}, "status": "success"}
{"timestamp": "2025-06-23T14:32:32.392550", "operation": "insert_attribute_validation_to_workflow_temp", "input_data": {"_id": "6854ee73fa0d6bfc16038bef", "validation_id": 386, "attribute_id": "E8.At34", "entity_name": "Employee", "attribute_name": "email", "left_operand": "email", "operator": "IS_UNIQUE", "right_operand": "", "success_value": "unique", "warning_value": "", "failure_value": "duplicate", "multi_condition_operator": "FALSE", "warning_message": "", "success_message": "Email is unique", "error_message": "Email must be unique", "natural_language": "Employee.email: email IS_UNIQUE ", "version": 1, "status": "draft", "created_at": "2025-06-20T05:15:31.135351", "updated_at": "2025-06-20T05:15:31.135357", "created_by": "system", "updated_by": "system"}, "result": {"success": true, "inserted_id": 62, "schema": "workflow_temp", "validation_id": 386, "attribute_id": "E8.At34", "original_validation_id": 6, "original_attribute_id": "E8.At4"}, "status": "success"}
{"timestamp": "2025-06-23T14:32:32.418242", "operation": "insert_attribute_validation_to_workflow_temp", "input_data": {"_id": "6854ee73fa0d6bfc16038bf0", "validation_id": 396, "attribute_id": "E8.At35", "entity_name": "Employee", "attribute_name": "employeeId", "left_operand": "employeeId", "operator": "IS_UNIQUE", "right_operand": "", "success_value": "unique", "warning_value": "", "failure_value": "duplicate", "multi_condition_operator": "FALSE", "warning_message": "", "success_message": "Employee ID is valid", "error_message": "Employee ID must be unique", "natural_language": "Employee.employeeId: employeeId IS_UNIQUE ", "version": 1, "status": "draft", "created_at": "2025-06-20T05:15:31.135903", "updated_at": "2025-06-20T05:15:31.135906", "created_by": "system", "updated_by": "system"}, "result": {"success": true, "inserted_id": 63, "schema": "workflow_temp", "validation_id": 396, "attribute_id": "E8.At35", "original_validation_id": 5, "original_attribute_id": "E8.At1"}, "status": "success"}
{"timestamp": "2025-06-23T14:32:32.445193", "operation": "insert_attribute_validation_to_workflow_temp", "input_data": {"_id": "6854f3ac9031164ff452a0f8", "validation_id": 406, "attribute_id": "E8.At36", "entity_name": "Employee", "attribute_name": "phone", "left_operand": "phone", "operator": "IS_UNIQUE", "right_operand": "", "success_value": "unique", "warning_value": "", "failure_value": "duplicate", "multi_condition_operator": "FALSE", "warning_message": "", "success_message": "Phone number is unique", "error_message": "Phone number must be unique", "natural_language": "Employee.phone: phone IS_UNIQUE ", "version": 1, "status": "draft", "created_at": "2025-06-20T05:37:48.522400", "updated_at": "2025-06-20T05:37:48.522404", "created_by": "system", "updated_by": "system"}, "result": {"success": true, "inserted_id": 64, "schema": "workflow_temp", "validation_id": 406, "attribute_id": "E8.At36", "original_validation_id": 7, "original_attribute_id": "E8.At4"}, "status": "success"}
{"timestamp": "2025-06-23T14:32:32.471959", "operation": "insert_attribute_validation_to_workflow_temp", "input_data": {"_id": "685508447147def4f91d1022", "validation_id": 416, "attribute_id": "E7.At28", "entity_name": "LeaveApplication", "attribute_name": "leaveId", "left_operand": "leaveId", "operator": "IS_UNIQUE", "right_operand": "", "success_value": "unique", "warning_value": "", "failure_value": "duplicate", "multi_condition_operator": "FALSE", "warning_message": "", "success_message": "Leave ID is valid", "error_message": "Leave ID must be unique", "natural_language": "LeaveApplication.leaveId: leaveId IS_UNIQUE ", "version": 1, "status": "draft", "created_at": "2025-06-20T07:05:40.927340", "updated_at": "2025-06-20T07:05:40.927345", "created_by": "system", "updated_by": "system"}, "result": {"success": true, "inserted_id": 65, "schema": "workflow_temp", "validation_id": 416, "attribute_id": "E7.At28", "original_validation_id": 1, "original_attribute_id": "E7.At1"}, "status": "success"}
{"timestamp": "2025-06-23T14:32:32.496321", "operation": "insert_attribute_validation_to_workflow_temp", "input_data": {"_id": "685508447147def4f91d1023", "validation_id": 426, "attribute_id": "E7.At29", "entity_name": "LeaveApplication", "attribute_name": "employeeId", "left_operand": "employeeId", "operator": "EXISTS_IN", "right_operand": "Employee.employeeId", "success_value": "exists", "warning_value": "", "failure_value": "not_exists", "multi_condition_operator": "FALSE", "warning_message": "", "success_message": "Employee ID is valid", "error_message": "Employee ID must exist in Employee table", "natural_language": "LeaveApplication.employeeId: employeeId EXISTS_IN Employee.employeeId", "version": 1, "status": "draft", "created_at": "2025-06-20T07:05:40.932582", "updated_at": "2025-06-20T07:05:40.932597", "created_by": "system", "updated_by": "system"}, "result": {"success": true, "inserted_id": 66, "schema": "workflow_temp", "validation_id": 426, "attribute_id": "E7.At29", "original_validation_id": 2, "original_attribute_id": "E7.At2"}, "status": "success"}
{"timestamp": "2025-06-23T14:32:32.519853", "operation": "insert_attribute_validation_to_workflow_temp", "input_data": {"_id": "685508447147def4f91d1024", "validation_id": 436, "attribute_id": "E7.At30", "entity_name": "LeaveApplication", "attribute_name": "startDate", "left_operand": "startDate", "operator": "IS_VALID_DATE", "right_operand": "", "success_value": "valid_date", "warning_value": "", "failure_value": "invalid_date", "multi_condition_operator": "FALSE", "warning_message": "", "success_message": "Start date is valid", "error_message": "Start date must be a valid date", "natural_language": "LeaveApplication.startDate: startDate IS_VALID_DATE ", "version": 1, "status": "draft", "created_at": "2025-06-20T07:05:40.934871", "updated_at": "2025-06-20T07:05:40.934878", "created_by": "system", "updated_by": "system"}, "result": {"success": true, "inserted_id": 67, "schema": "workflow_temp", "validation_id": 436, "attribute_id": "E7.At30", "original_validation_id": 3, "original_attribute_id": "E7.At3"}, "status": "success"}
{"timestamp": "2025-06-23T14:32:32.543275", "operation": "insert_attribute_validation_to_workflow_temp", "input_data": {"_id": "685508447147def4f91d1025", "validation_id": 446, "attribute_id": "E7.At31", "entity_name": "LeaveApplication", "attribute_name": "endDate", "left_operand": "endDate", "operator": "GREATER_THAN", "right_operand": "startDate", "success_value": "after_start", "warning_value": "", "failure_value": "before_or_equal_start", "multi_condition_operator": "FALSE", "warning_message": "", "success_message": "End date is valid", "error_message": "End date must be after start date", "natural_language": "LeaveApplication.endDate: endDate GREATER_THAN startDate", "version": 1, "status": "draft", "created_at": "2025-06-20T07:05:40.936694", "updated_at": "2025-06-20T07:05:40.936701", "created_by": "system", "updated_by": "system"}, "result": {"success": true, "inserted_id": 68, "schema": "workflow_temp", "validation_id": 446, "attribute_id": "E7.At31", "original_validation_id": 4, "original_attribute_id": "E7.At4"}, "status": "success"}
{"timestamp": "2025-06-23T14:32:32.544667", "operation": "process_mongo_attribute_validations_to_workflow_temp", "input_data": {"schema": "workflow_temp"}, "result": {"total_processed": 8, "successful_inserts": 8, "failed_inserts": 0, "details": [{"validation_id": 376, "attribute_id": "E8.At33", "status": "success", "details": {"success": true, "inserted_id": 61, "schema": "workflow_temp", "validation_id": 376, "attribute_id": "E8.At33", "original_validation_id": 2, "original_attribute_id": "E8.At4"}}, {"validation_id": 386, "attribute_id": "E8.At34", "status": "success", "details": {"success": true, "inserted_id": 62, "schema": "workflow_temp", "validation_id": 386, "attribute_id": "E8.At34", "original_validation_id": 6, "original_attribute_id": "E8.At4"}}, {"validation_id": 396, "attribute_id": "E8.At35", "status": "success", "details": {"success": true, "inserted_id": 63, "schema": "workflow_temp", "validation_id": 396, "attribute_id": "E8.At35", "original_validation_id": 5, "original_attribute_id": "E8.At1"}}, {"validation_id": 406, "attribute_id": "E8.At36", "status": "success", "details": {"success": true, "inserted_id": 64, "schema": "workflow_temp", "validation_id": 406, "attribute_id": "E8.At36", "original_validation_id": 7, "original_attribute_id": "E8.At4"}}, {"validation_id": 416, "attribute_id": "E7.At28", "status": "success", "details": {"success": true, "inserted_id": 65, "schema": "workflow_temp", "validation_id": 416, "attribute_id": "E7.At28", "original_validation_id": 1, "original_attribute_id": "E7.At1"}}, {"validation_id": 426, "attribute_id": "E7.At29", "status": "success", "details": {"success": true, "inserted_id": 66, "schema": "workflow_temp", "validation_id": 426, "attribute_id": "E7.At29", "original_validation_id": 2, "original_attribute_id": "E7.At2"}}, {"validation_id": 436, "attribute_id": "E7.At30", "status": "success", "details": {"success": true, "inserted_id": 67, "schema": "workflow_temp", "validation_id": 436, "attribute_id": "E7.At30", "original_validation_id": 3, "original_attribute_id": "E7.At3"}}, {"validation_id": 446, "attribute_id": "E7.At31", "status": "success", "details": {"success": true, "inserted_id": 68, "schema": "workflow_temp", "validation_id": 446, "attribute_id": "E7.At31", "original_validation_id": 4, "original_attribute_id": "E7.At4"}}]}, "status": "success"}
{"timestamp": "2025-06-23T14:38:51.881246", "operation": "insert_attribute_validation_to_workflow_temp", "input_data": {"_id": "6852ee1a727e97ac18d0839e", "validation_id": 456, "attribute_id": "E8.At37", "entity_name": "Employee", "attribute_name": "employeeId", "left_operand": "employeeId", "operator": "IS_UNIQUE", "right_operand": "", "success_value": "unique", "warning_value": "", "failure_value": "duplicate", "multi_condition_operator": "FALSE", "warning_message": "", "success_message": "Employee ID is valid", "error_message": "Employee ID must be unique", "natural_language": "Employee.employeeId: employeeId IS_UNIQUE ", "version": 1, "status": "draft", "created_at": "2025-06-18T16:49:30.448481", "updated_at": "2025-06-18T16:49:30.448484", "created_by": "system", "updated_by": "system"}, "result": {"success": true, "inserted_id": 69, "schema": "workflow_temp", "validation_id": 456, "attribute_id": "E8.At37", "original_validation_id": 1, "original_attribute_id": "E8.At1"}, "status": "success"}
{"timestamp": "2025-06-23T14:38:51.884328", "operation": "deploy_single_attribute_validation_to_workflow_temp", "input_data": {"_id": "6852ee1a727e97ac18d0839e", "validation_id": 456, "attribute_id": "E8.At37", "entity_name": "Employee", "attribute_name": "employeeId", "left_operand": "employeeId", "operator": "IS_UNIQUE", "right_operand": "", "success_value": "unique", "warning_value": "", "failure_value": "duplicate", "multi_condition_operator": "FALSE", "warning_message": "", "success_message": "Employee ID is valid", "error_message": "Employee ID must be unique", "natural_language": "Employee.employeeId: employeeId IS_UNIQUE ", "version": 1, "status": "draft", "created_at": "2025-06-18T16:49:30.448481", "updated_at": "2025-06-18T16:49:30.448484", "created_by": "system", "updated_by": "system"}, "result": {"success": true, "inserted_id": 69, "schema": "workflow_temp", "validation_id": 456, "attribute_id": "E8.At37", "original_validation_id": 1, "original_attribute_id": "E8.At1"}, "status": "success"}
{"timestamp": "2025-06-23T14:38:51.977577", "operation": "insert_attribute_validation_to_workflow_temp", "input_data": {"_id": "6854488a754fbf4d729d3a7a", "validation_id": 466, "attribute_id": "E8.At38", "entity_name": "Employee", "attribute_name": "email", "left_operand": "email", "operator": "IS_UNIQUE", "right_operand": "", "success_value": "unique", "warning_value": "", "failure_value": "duplicate", "multi_condition_operator": "FALSE", "warning_message": "", "success_message": "Email is unique", "error_message": "Email must be unique", "natural_language": "Employee.email: email IS_UNIQUE ", "version": 1, "status": "draft", "created_at": "2025-06-19T17:27:38.404018", "updated_at": "2025-06-19T17:27:38.404025", "created_by": "system", "updated_by": "system"}, "result": {"success": true, "inserted_id": 70, "schema": "workflow_temp", "validation_id": 466, "attribute_id": "E8.At38", "original_validation_id": 2, "original_attribute_id": "E8.At4"}, "status": "success"}
{"timestamp": "2025-06-23T14:38:52.010598", "operation": "insert_attribute_validation_to_workflow_temp", "input_data": {"_id": "6854ee73fa0d6bfc16038bef", "validation_id": 476, "attribute_id": "E8.At39", "entity_name": "Employee", "attribute_name": "email", "left_operand": "email", "operator": "IS_UNIQUE", "right_operand": "", "success_value": "unique", "warning_value": "", "failure_value": "duplicate", "multi_condition_operator": "FALSE", "warning_message": "", "success_message": "Email is unique", "error_message": "Email must be unique", "natural_language": "Employee.email: email IS_UNIQUE ", "version": 1, "status": "draft", "created_at": "2025-06-20T05:15:31.135351", "updated_at": "2025-06-20T05:15:31.135357", "created_by": "system", "updated_by": "system"}, "result": {"success": true, "inserted_id": 71, "schema": "workflow_temp", "validation_id": 476, "attribute_id": "E8.At39", "original_validation_id": 6, "original_attribute_id": "E8.At4"}, "status": "success"}
{"timestamp": "2025-06-23T14:38:52.042797", "operation": "insert_attribute_validation_to_workflow_temp", "input_data": {"_id": "6854ee73fa0d6bfc16038bf0", "validation_id": 486, "attribute_id": "E8.At40", "entity_name": "Employee", "attribute_name": "employeeId", "left_operand": "employeeId", "operator": "IS_UNIQUE", "right_operand": "", "success_value": "unique", "warning_value": "", "failure_value": "duplicate", "multi_condition_operator": "FALSE", "warning_message": "", "success_message": "Employee ID is valid", "error_message": "Employee ID must be unique", "natural_language": "Employee.employeeId: employeeId IS_UNIQUE ", "version": 1, "status": "draft", "created_at": "2025-06-20T05:15:31.135903", "updated_at": "2025-06-20T05:15:31.135906", "created_by": "system", "updated_by": "system"}, "result": {"success": true, "inserted_id": 72, "schema": "workflow_temp", "validation_id": 486, "attribute_id": "E8.At40", "original_validation_id": 5, "original_attribute_id": "E8.At1"}, "status": "success"}
{"timestamp": "2025-06-23T14:38:52.074706", "operation": "insert_attribute_validation_to_workflow_temp", "input_data": {"_id": "6854f3ac9031164ff452a0f8", "validation_id": 496, "attribute_id": "E8.At41", "entity_name": "Employee", "attribute_name": "phone", "left_operand": "phone", "operator": "IS_UNIQUE", "right_operand": "", "success_value": "unique", "warning_value": "", "failure_value": "duplicate", "multi_condition_operator": "FALSE", "warning_message": "", "success_message": "Phone number is unique", "error_message": "Phone number must be unique", "natural_language": "Employee.phone: phone IS_UNIQUE ", "version": 1, "status": "draft", "created_at": "2025-06-20T05:37:48.522400", "updated_at": "2025-06-20T05:37:48.522404", "created_by": "system", "updated_by": "system"}, "result": {"success": true, "inserted_id": 73, "schema": "workflow_temp", "validation_id": 496, "attribute_id": "E8.At41", "original_validation_id": 7, "original_attribute_id": "E8.At4"}, "status": "success"}
{"timestamp": "2025-06-23T14:38:52.103314", "operation": "insert_attribute_validation_to_workflow_temp", "input_data": {"_id": "685508447147def4f91d1022", "validation_id": 506, "attribute_id": "E7.At32", "entity_name": "LeaveApplication", "attribute_name": "leaveId", "left_operand": "leaveId", "operator": "IS_UNIQUE", "right_operand": "", "success_value": "unique", "warning_value": "", "failure_value": "duplicate", "multi_condition_operator": "FALSE", "warning_message": "", "success_message": "Leave ID is valid", "error_message": "Leave ID must be unique", "natural_language": "LeaveApplication.leaveId: leaveId IS_UNIQUE ", "version": 1, "status": "draft", "created_at": "2025-06-20T07:05:40.927340", "updated_at": "2025-06-20T07:05:40.927345", "created_by": "system", "updated_by": "system"}, "result": {"success": true, "inserted_id": 74, "schema": "workflow_temp", "validation_id": 506, "attribute_id": "E7.At32", "original_validation_id": 1, "original_attribute_id": "E7.At1"}, "status": "success"}
{"timestamp": "2025-06-23T14:38:52.132315", "operation": "insert_attribute_validation_to_workflow_temp", "input_data": {"_id": "685508447147def4f91d1023", "validation_id": 516, "attribute_id": "E7.At33", "entity_name": "LeaveApplication", "attribute_name": "employeeId", "left_operand": "employeeId", "operator": "EXISTS_IN", "right_operand": "Employee.employeeId", "success_value": "exists", "warning_value": "", "failure_value": "not_exists", "multi_condition_operator": "FALSE", "warning_message": "", "success_message": "Employee ID is valid", "error_message": "Employee ID must exist in Employee table", "natural_language": "LeaveApplication.employeeId: employeeId EXISTS_IN Employee.employeeId", "version": 1, "status": "draft", "created_at": "2025-06-20T07:05:40.932582", "updated_at": "2025-06-20T07:05:40.932597", "created_by": "system", "updated_by": "system"}, "result": {"success": true, "inserted_id": 75, "schema": "workflow_temp", "validation_id": 516, "attribute_id": "E7.At33", "original_validation_id": 2, "original_attribute_id": "E7.At2"}, "status": "success"}
{"timestamp": "2025-06-23T14:38:52.159528", "operation": "insert_attribute_validation_to_workflow_temp", "input_data": {"_id": "685508447147def4f91d1024", "validation_id": 526, "attribute_id": "E7.At34", "entity_name": "LeaveApplication", "attribute_name": "startDate", "left_operand": "startDate", "operator": "IS_VALID_DATE", "right_operand": "", "success_value": "valid_date", "warning_value": "", "failure_value": "invalid_date", "multi_condition_operator": "FALSE", "warning_message": "", "success_message": "Start date is valid", "error_message": "Start date must be a valid date", "natural_language": "LeaveApplication.startDate: startDate IS_VALID_DATE ", "version": 1, "status": "draft", "created_at": "2025-06-20T07:05:40.934871", "updated_at": "2025-06-20T07:05:40.934878", "created_by": "system", "updated_by": "system"}, "result": {"success": true, "inserted_id": 76, "schema": "workflow_temp", "validation_id": 526, "attribute_id": "E7.At34", "original_validation_id": 3, "original_attribute_id": "E7.At3"}, "status": "success"}
{"timestamp": "2025-06-23T14:38:52.188720", "operation": "insert_attribute_validation_to_workflow_temp", "input_data": {"_id": "685508447147def4f91d1025", "validation_id": 536, "attribute_id": "E7.At35", "entity_name": "LeaveApplication", "attribute_name": "endDate", "left_operand": "endDate", "operator": "GREATER_THAN", "right_operand": "startDate", "success_value": "after_start", "warning_value": "", "failure_value": "before_or_equal_start", "multi_condition_operator": "FALSE", "warning_message": "", "success_message": "End date is valid", "error_message": "End date must be after start date", "natural_language": "LeaveApplication.endDate: endDate GREATER_THAN startDate", "version": 1, "status": "draft", "created_at": "2025-06-20T07:05:40.936694", "updated_at": "2025-06-20T07:05:40.936701", "created_by": "system", "updated_by": "system"}, "result": {"success": true, "inserted_id": 77, "schema": "workflow_temp", "validation_id": 536, "attribute_id": "E7.At35", "original_validation_id": 4, "original_attribute_id": "E7.At4"}, "status": "success"}
{"timestamp": "2025-06-23T14:38:52.190810", "operation": "process_mongo_attribute_validations_to_workflow_temp", "input_data": {"schema": "workflow_temp"}, "result": {"total_processed": 8, "successful_inserts": 8, "failed_inserts": 0, "details": [{"validation_id": 466, "attribute_id": "E8.At38", "status": "success", "details": {"success": true, "inserted_id": 70, "schema": "workflow_temp", "validation_id": 466, "attribute_id": "E8.At38", "original_validation_id": 2, "original_attribute_id": "E8.At4"}}, {"validation_id": 476, "attribute_id": "E8.At39", "status": "success", "details": {"success": true, "inserted_id": 71, "schema": "workflow_temp", "validation_id": 476, "attribute_id": "E8.At39", "original_validation_id": 6, "original_attribute_id": "E8.At4"}}, {"validation_id": 486, "attribute_id": "E8.At40", "status": "success", "details": {"success": true, "inserted_id": 72, "schema": "workflow_temp", "validation_id": 486, "attribute_id": "E8.At40", "original_validation_id": 5, "original_attribute_id": "E8.At1"}}, {"validation_id": 496, "attribute_id": "E8.At41", "status": "success", "details": {"success": true, "inserted_id": 73, "schema": "workflow_temp", "validation_id": 496, "attribute_id": "E8.At41", "original_validation_id": 7, "original_attribute_id": "E8.At4"}}, {"validation_id": 506, "attribute_id": "E7.At32", "status": "success", "details": {"success": true, "inserted_id": 74, "schema": "workflow_temp", "validation_id": 506, "attribute_id": "E7.At32", "original_validation_id": 1, "original_attribute_id": "E7.At1"}}, {"validation_id": 516, "attribute_id": "E7.At33", "status": "success", "details": {"success": true, "inserted_id": 75, "schema": "workflow_temp", "validation_id": 516, "attribute_id": "E7.At33", "original_validation_id": 2, "original_attribute_id": "E7.At2"}}, {"validation_id": 526, "attribute_id": "E7.At34", "status": "success", "details": {"success": true, "inserted_id": 76, "schema": "workflow_temp", "validation_id": 526, "attribute_id": "E7.At34", "original_validation_id": 3, "original_attribute_id": "E7.At3"}}, {"validation_id": 536, "attribute_id": "E7.At35", "status": "success", "details": {"success": true, "inserted_id": 77, "schema": "workflow_temp", "validation_id": 536, "attribute_id": "E7.At35", "original_validation_id": 4, "original_attribute_id": "E7.At4"}}]}, "status": "success"}
