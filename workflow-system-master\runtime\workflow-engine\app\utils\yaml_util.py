"""
YAML Utility Functions.

This module provides utility functions for working with YAML configurations.
"""

import yaml
from typing import Dict, Any, List

def parse_yaml(yaml_string: str) -> Dict[str, Any]:
    """
    Parse a YAML string into a Python object.
    
    Args:
        yaml_string: YAML content as a string
        
    Returns:
        Parsed YAML as a Python object
    """
    try:
        return yaml.safe_load(yaml_string)
    except Exception as e:
        raise ValueError(f"Error parsing YAML: {str(e)}")

def generate_yaml(data: Dict[str, Any]) -> str:
    """
    Generate a YAML string from a Python object.
    
    Args:
        data: Python object to convert to YAML
        
    Returns:
        YAML string
    """
    try:
        return yaml.dump(data, default_flow_style=False, sort_keys=False)
    except Exception as e:
        raise ValueError(f"Error generating YAML: {str(e)}")

def validate_workflow_yaml(yaml_string: str) -> List[str]:
    """
    Validate a workflow YAML configuration.
    
    Args:
        yaml_string: YAML content as a string
        
    Returns:
        List of validation errors, if any
    """
    errors = []
    
    try:
        data = parse_yaml(yaml_string)
        
        # Check for required top-level sections
        if "global_objective" not in data:
            errors.append("Missing required section: global_objective")
        
        # Check global objective
        if "global_objective" in data:
            go = data["global_objective"]
            
            if "name" not in go:
                errors.append("Missing required field: global_objective.name")
            
            if "contextual_id" not in go:
                errors.append("Missing required field: global_objective.contextual_id")
            
            # Check local objectives
            if "local_objectives" not in go:
                errors.append("Missing required section: global_objective.local_objectives")
            elif not isinstance(go["local_objectives"], list):
                errors.append("global_objective.local_objectives must be a list")
            else:
                for i, lo in enumerate(go["local_objectives"]):
                    if "id" not in lo:
                        errors.append(f"Missing required field: global_objective.local_objectives[{i}].id")
                    
                    if "name" not in lo:
                        errors.append(f"Missing required field: global_objective.local_objectives[{i}].name")
                    
                    if "function_type" not in lo:
                        errors.append(f"Missing required field: global_objective.local_objectives[{i}].function_type")
                    
                    if "execution_pathway" not in lo:
                        errors.append(f"Missing required field: global_objective.local_objectives[{i}].execution_pathway")
                    elif "type" not in lo["execution_pathway"]:
                        errors.append(f"Missing required field: global_objective.local_objectives[{i}].execution_pathway.type")
        
        # Check entities
        if "entities" in data:
            if not isinstance(data["entities"], list):
                errors.append("entities must be a list")
            else:
                for i, entity in enumerate(data["entities"]):
                    if "id" not in entity:
                        errors.append(f"Missing required field: entities[{i}].id")
                    
                    if "name" not in entity:
                        errors.append(f"Missing required field: entities[{i}].name")
        
    except Exception as e:
        errors.append(f"Error validating YAML: {str(e)}")
    
    return errors

def generate_workflow_yaml(workflow_data: Dict[str, Any]) -> str:
    """
    Generate a workflow YAML configuration.
    
    Args:
        workflow_data: Workflow data
        
    Returns:
        YAML string
    """
    yaml_data = {}
    
    # Add tenant information if available
    if "tenant" in workflow_data:
        yaml_data["tenant"] = workflow_data["tenant"]
    
    # Add entities if available
    if "entities" in workflow_data:
        yaml_data["entities"] = workflow_data["entities"]
    
    # Add global objective
    if "global_objective" in workflow_data:
        yaml_data["global_objective"] = workflow_data["global_objective"]
    
    # Generate YAML
    return generate_yaml(yaml_data)
