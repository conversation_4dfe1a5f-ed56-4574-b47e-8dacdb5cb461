"""
Test Script for YAML Builder v2 Components

This script tests the component validators, individual deployers, and the component deployer.
"""

import os
import sys
import yaml
import logging
import argparse
from typing import Dict, List, Tuple, Any, Optional

# Add parent directory to path to import common modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from component_validator import ComponentValidator
from registry_validator import RegistryValidator
from yaml_assembler import YAMLAssembler
from component_deployer import ComponentDeployer
from db_utils import create_temp_schema
from id_generator import IDGenerator

# Set up logging
log_file = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'test_components.log')
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(log_file),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger('test_components')

# Create a function to write test results to a file
def write_results_to_file(results):
    """
    Write test results to a file.
    
    Args:
        results: Dictionary containing test results
    """
    output_file = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'test_results.txt')
    with open(output_file, 'w') as f:
        f.write("=== YAML Builder v2 Test Results ===\n\n")
        
        # Write component validator results
        f.write("Component Validators:\n")
        for component, result in results.get('validators', {}).items():
            f.write(f"- {component}: {'PASS' if result['success'] else 'FAIL'}\n")
            for message in result.get('messages', []):
                f.write(f"  - {message}\n")
        f.write("\n")
        
        # Write registry validator results
        registry_result = results.get('registry', {})
        f.write(f"Registry Validator: {'PASS' if registry_result.get('success', False) else 'FAIL'}\n")
        for message in registry_result.get('messages', []):
            f.write(f"- {message}\n")
        f.write("\n")
        
        # Write YAML assembler results
        assembler_result = results.get('assembler', {})
        f.write(f"YAML Assembler: {'PASS' if assembler_result.get('success', False) else 'FAIL'}\n")
        for message in assembler_result.get('messages', []):
            f.write(f"- {message}\n")
        f.write("\n")
        
        # Write ID generator results
        id_gen_result = results.get('id_generator', {})
        f.write(f"ID Generator: {'PASS' if id_gen_result.get('success', False) else 'FAIL'}\n")
        for message in id_gen_result.get('messages', []):
            f.write(f"- {message}\n")
        f.write("\n")
        
        # Write component deployer results
        f.write("Component Deployers:\n")
        for component, result in results.get('deployers', {}).items():
            f.write(f"- {component}: {'PASS' if result['success'] else 'FAIL'}\n")
            for message in result.get('messages', []):
                f.write(f"  - {message}\n")
        f.write("\n")
        
        # Write summary
        f.write("=== Summary ===\n")
        total_tests = 0
        passed_tests = 0
        
        # Count validator tests
        for result in results.get('validators', {}).values():
            total_tests += 1
            if result.get('success', False):
                passed_tests += 1
        
        # Count registry validator test
        total_tests += 1
        if results.get('registry', {}).get('success', False):
            passed_tests += 1
        
        # Count YAML assembler test
        total_tests += 1
        if results.get('assembler', {}).get('success', False):
            passed_tests += 1
        
        # Count ID generator test
        total_tests += 1
        if results.get('id_generator', {}).get('success', False):
            passed_tests += 1
        
        # Count deployer tests
        for result in results.get('deployers', {}).values():
            total_tests += 1
            if result.get('success', False):
                passed_tests += 1
        
        f.write(f"Total tests: {total_tests}\n")
        f.write(f"Passed tests: {passed_tests}\n")
        f.write(f"Failed tests: {total_tests - passed_tests}\n")
        f.write(f"Success rate: {passed_tests / total_tests * 100:.2f}%\n")
    
    logger.info(f"Test results written to {output_file}")
    return output_file

# Sample component data for testing
SAMPLE_ROLES = """
roles:
  Admin:
    description: Administrator role with full access
    permissions:
      - create_workflow
      - edit_workflow
      - delete_workflow
      - view_workflow
      - execute_workflow
    scope: global
    classification: system
    special_conditions: None
  
  Manager:
    description: Manager role with limited access
    permissions:
      - view_workflow
      - execute_workflow
    inherits:
      - User
    scope: department
    classification: business
    special_conditions: Requires department head approval
  
  User:
    description: Basic user role
    permissions:
      - view_workflow
    scope: personal
    classification: general
    special_conditions: None
"""

SAMPLE_ENTITIES = """
entities:
  Department:
    description: Department entity definition
    attributes:
      id:
        type: string
        required: true
      name:
        type: string
        required: true
      budget:
        type: number
        required: true
      manager_id:
        type: string
        required: true
    relationships:
      employees:
        entity: Employee
        type: one-to-many
        cardinality: one-to-many
    metadata:
      owner: HR
      version: 1.0
      tags:
        - department
        - hr
  
  Employee:
    description: Employee entity definition
    attributes:
      id:
        type: string
        required: true
      name:
        type: string
        required: true
      email:
        type: string
        required: true
      department:
        type: string
        required: true
      manager:
        type: string
        required: false
      salary:
        type: number
        required: true
        calculated: true
        formula: "base_salary + bonus"
        dependencies:
          - base_salary
          - bonus
    relationships:
      manages:
        entity: Employee
        type: one-to-many
        cardinality: one-to-many
      belongsTo:
        entity: Department
        type: many-to-one
        cardinality: many-to-one
    business_rules:
      salary_cap:
        description: Salary cannot exceed department budget
        condition: "salary > department.budget"
        action: "reject"
        priority: 1
        active: true
    metadata:
      owner: HR
      version: 1.0
      tags:
        - employee
        - hr
    lifecycle_management:
      archive_strategy:
        type: time_based
        retention_period: 7 years
      purge_rules:
        type: conditional
        condition: "status == 'terminated' && termination_date < (now() - 7 years)"
      history_tracking:
        enabled: true
        fields:
          - salary
          - department
          - manager
"""

SAMPLE_GO_DEFINITIONS = """
global_objectives:
  EmployeeOnboarding:
    description: Employee onboarding process
    local_objectives:
      - CollectPersonalInfo
      - AssignDepartment
      - SetupAccounts
      - ScheduleTraining
    process_mining_schema:
      event_log_specification:
        case_id: employee_id
        activity: activity_name
        timestamp: timestamp
        resource: resource_id
      performance_discovery_metrics:
        - throughput_time
        - waiting_time
        - processing_time
      conformance_analytics:
        - deviation_analysis
        - bottleneck_analysis
    performance_metrics:
      onboarding_time:
        description: Time to complete onboarding
        formula: "end_time - start_time"
        target_value: 5
        unit: days
        threshold_warning: 7
        threshold_critical: 10
      completion_rate:
        description: Percentage of completed onboarding steps
        formula: "completed_steps / total_steps * 100"
        target_value: 100
        unit: percentage
        threshold_warning: 90
        threshold_critical: 80
    performance_metadata:
      owner: HR
      priority: high
      review_frequency: monthly
"""

SAMPLE_LO_DEFINITIONS = """
local_objectives:
  CollectPersonalInfo:
    description: Collect personal information from new employee
    global_objective: EmployeeOnboarding
    input_items:
      name:
        type: string
        required: true
        ui_control: text
        help_text: Full name of the employee
      email:
        type: string
        required: true
        ui_control: email
        help_text: Work email address
      phone:
        type: string
        required: true
        ui_control: phone
        help_text: Work phone number
      address:
        type: string
        required: true
        ui_control: textarea
        help_text: Home address
      emergency_contact:
        type: object
        required: true
        ui_control: nested_form
        help_text: Emergency contact information
        dependency_info:
          fields:
            - name
            - phone
            - relationship
    output_items:
      employee_record:
        type: object
      employee_id:
        type: string
    ui_stack:
      form_layout: tabbed
      validation_rules:
        email: "^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\\\.[a-zA-Z]{2,}$"
        phone: "^\\\\+?[0-9]{10,15}$"
      conditional_display:
        emergency_contact:
          condition: "has_emergency_contact === true"
    mapping_stack:
      entity_mappings:
        employee_record: Employee
      field_mappings:
        name: Employee.name
        email: Employee.email
        phone: Employee.phone
        address: Employee.address
  
  AssignDepartment:
    description: Assign department to new employee
    global_objective: EmployeeOnboarding
    dependencies:
      - lo: CollectPersonalInfo
        type: sequential
        condition: "employee_id != null"
        mapping:
          employee_id: employee_id
    input_items:
      employee_id:
        type: string
        required: true
        ui_control: hidden
      department:
        type: string
        required: true
        ui_control: dropdown
        help_text: Department to assign
      manager:
        type: string
        required: true
        ui_control: dropdown
        help_text: Manager to assign
        dependency_info:
          depends_on: department
          update_url: "/api/managers?department={department}"
    output_items:
      department_assignment:
        type: object
    ui_stack:
      form_layout: standard
      validation_rules: {}
    mapping_stack:
      entity_mappings:
        department_assignment: Employee
      field_mappings:
        department: Employee.department
        manager: Employee.manager
        
  SetupAccounts:
    description: Set up IT accounts for new employee
    global_objective: EmployeeOnboarding
    dependencies:
      - lo: AssignDepartment
        type: sequential
        condition: "department_assignment != null"
        mapping:
          employee_id: employee_id
          department: department
    input_items:
      employee_id:
        type: string
        required: true
        ui_control: hidden
      department:
        type: string
        required: true
        ui_control: hidden
      email_account:
        type: boolean
        required: true
        ui_control: checkbox
        help_text: Create email account
        default: true
      vpn_access:
        type: boolean
        required: true
        ui_control: checkbox
        help_text: Provide VPN access
        default: false
      system_access:
        type: array
        required: true
        ui_control: multi_select
        help_text: Select systems to provide access to
        options:
          - CRM
          - ERP
          - HRIS
          - Project Management
    output_items:
      account_setup:
        type: object
      email_address:
        type: string
    ui_stack:
      form_layout: standard
      validation_rules: {}
    mapping_stack:
      entity_mappings:
        account_setup: Employee
      field_mappings:
        email_address: Employee.email
        
  ScheduleTraining:
    description: Schedule training sessions for new employee
    global_objective: EmployeeOnboarding
    dependencies:
      - lo: SetupAccounts
        type: sequential
        condition: "account_setup != null"
        mapping:
          employee_id: employee_id
          department: department
    input_items:
      employee_id:
        type: string
        required: true
        ui_control: hidden
      department:
        type: string
        required: true
        ui_control: hidden
      orientation_date:
        type: date
        required: true
        ui_control: date_picker
        help_text: Company orientation date
      department_training_date:
        type: date
        required: true
        ui_control: date_picker
        help_text: Department-specific training date
      system_training:
        type: array
        required: true
        ui_control: multi_select
        help_text: Select systems for training
        options:
          - CRM
          - ERP
          - HRIS
          - Project Management
    output_items:
      training_schedule:
        type: object
    ui_stack:
      form_layout: standard
      validation_rules: {}
    mapping_stack:
      entity_mappings:
        training_schedule: Employee
      field_mappings:
        orientation_date: Employee.orientation_date
        department_training_date: Employee.department_training_date
"""

def test_component_validator(component_type: str, component_yaml: str) -> Tuple[bool, List[str]]:
    """
    Test the component validator.
    
    Args:
        component_type: Type of the component ('roles', 'entities', 'go_definitions', 'lo_definitions')
        component_yaml: YAML string of the component
        
    Returns:
        Tuple containing:
            - Boolean indicating if validation was successful
            - List of messages (warnings, errors, or success messages)
    """
    logger.info(f"Testing component validator for {component_type}")
    
    try:
        # Create validator
        validator = ComponentValidator()
        
        # Validate component
        is_valid, messages = validator.validate_component(component_type, component_yaml)
        
        logger.info(f"Component validation {'succeeded' if is_valid else 'failed'}")
        for message in messages:
            logger.info(f"- {message}")
        
        return is_valid, messages
    except yaml.YAMLError as e:
        logger.error(f"YAML parsing error: {str(e)}")
        return False, [f"YAML parsing error: {str(e)}"]
    except Exception as e:
        logger.error(f"Validation error: {str(e)}", exc_info=True)
        return False, [f"Validation error: {str(e)}"]

def test_registry_validator(components: Dict[str, str]) -> Tuple[bool, List[str]]:
    """
    Test the registry validator.
    
    Args:
        components: Dictionary mapping component types to YAML strings
        
    Returns:
        Tuple containing:
            - Boolean indicating if validation was successful
            - List of messages (warnings, errors, or success messages)
    """
    logger.info("Testing registry validator")
    
    try:
        # Create validator
        validator = RegistryValidator()
        
        # Validate registry
        is_valid, messages = validator.validate_registry(components)
        
        logger.info(f"Registry validation {'succeeded' if is_valid else 'failed'}")
        for message in messages:
            logger.info(f"- {message}")
        
        return is_valid, messages
    except yaml.YAMLError as e:
        logger.error(f"YAML parsing error: {str(e)}")
        return False, [f"YAML parsing error: {str(e)}"]
    except Exception as e:
        logger.error(f"Validation error: {str(e)}", exc_info=True)
        return False, [f"Validation error: {str(e)}"]

def test_yaml_assembler(components: Dict[str, str]) -> Tuple[bool, str, List[str]]:
    """
    Test the YAML assembler.
    
    Args:
        components: Dictionary mapping component types to YAML strings
        
    Returns:
        Tuple containing:
            - Boolean indicating if assembly was successful
            - Assembled YAML string
            - List of messages (warnings, errors, or success messages)
    """
    logger.info("Testing YAML assembler")
    
    try:
        # Create assembler
        assembler = YAMLAssembler()
        
        # Assemble YAML
        assembled_yaml, messages = assembler.assemble_yaml(components)
        
        logger.info("YAML assembly completed")
        for message in messages:
            logger.info(f"- {message}")
        
        return True, assembled_yaml, messages
    except yaml.YAMLError as e:
        logger.error(f"YAML parsing error: {str(e)}")
        return False, "", [f"YAML parsing error: {str(e)}"]
    except Exception as e:
        logger.error(f"Assembly error: {str(e)}", exc_info=True)
        return False, "", [f"Assembly error: {str(e)}"]

def test_component_deployer(component_type: str, component_yaml: str, use_temp_schema: bool = True) -> Tuple[bool, List[str]]:
    """
    Test the component deployer.
    
    Args:
        component_type: Type of the component ('roles', 'entities', 'go_definitions', 'lo_definitions')
        component_yaml: YAML string of the component
        use_temp_schema: Whether to deploy to a temporary schema for validation
        
    Returns:
        Tuple containing:
            - Boolean indicating if deployment was successful
            - List of messages (warnings, errors, or success messages)
    """
    logger.info(f"Testing component deployer for {component_type}")
    
    try:
        # Create deployer
        deployer = ComponentDeployer(use_temp_schema=use_temp_schema)
        
        # Deploy component
        success, messages = deployer.deploy_component(component_type, component_yaml)
        
        logger.info(f"Component deployment {'succeeded' if success else 'failed'}")
        for message in messages:
            logger.info(f"- {message}")
        
        return success, messages
    except Exception as e:
        logger.error(f"Deployment error: {str(e)}", exc_info=True)
        return False, [f"Deployment error: {str(e)}"]

def test_id_generator(schema_name: str = "workflow_temp") -> Tuple[bool, List[str]]:
    """
    Test the ID generator.
    
    Args:
        schema_name: Schema name to use for ID validation
        
    Returns:
        Tuple containing:
            - Boolean indicating if ID generation was successful
            - List of messages (warnings, errors, or success messages)
    """
    logger.info("Testing ID generator")
    
    try:
        # Create ID generator
        id_generator = IDGenerator(schema_name)
        
        # Test ID generation
        role_id = id_generator.generate_id('role', 'Admin')
        entity_id = id_generator.generate_id('entity', 'Employee')
        go_id = id_generator.generate_id('go', 'EmployeeOnboarding')
        lo_id = id_generator.generate_id('lo', 'CollectPersonalInfo')
        
        # Test ID validation
        role_valid = id_generator.validate_id('role', role_id)
        entity_valid = id_generator.validate_id('entity', entity_id)
        go_valid = id_generator.validate_id('go', go_id)
        lo_valid = id_generator.validate_id('lo', lo_id)
        
        # Log results
        logger.info(f"Generated role ID: {role_id}, valid: {role_valid}")
        logger.info(f"Generated entity ID: {entity_id}, valid: {entity_valid}")
        logger.info(f"Generated GO ID: {go_id}, valid: {go_valid}")
        logger.info(f"Generated LO ID: {lo_id}, valid: {lo_valid}")
        
        return all([role_valid, entity_valid, go_valid, lo_valid]), []
    except Exception as e:
        logger.error(f"ID generation error: {str(e)}", exc_info=True)
        return False, [f"ID generation error: {str(e)}"]

def run_all_tests() -> str:
    """
    Run all tests.
    
    Returns:
        Path to the test results file
    """
    logger.info("Running all tests")
    
    # Initialize results dictionary
    results = {
        'validators': {},
        'registry': {},
        'assembler': {},
        'id_generator': {},
        'deployers': {}
    }
    
    # Test component validators
    logger.info("=== Testing Component Validators ===")
    
    # Test roles validator
    roles_valid, roles_messages = test_component_validator('roles', SAMPLE_ROLES)
    results['validators']['roles'] = {
        'success': roles_valid,
        'messages': roles_messages
    }
    
    # Test entities validator
    entities_valid, entities_messages = test_component_validator('entities', SAMPLE_ENTITIES)
    results['validators']['entities'] = {
        'success': entities_valid,
        'messages': entities_messages
    }
    
    # Test GO definitions validator
    go_valid, go_messages = test_component_validator('go_definitions', SAMPLE_GO_DEFINITIONS)
    results['validators']['go_definitions'] = {
        'success': go_valid,
        'messages': go_messages
    }
    
    # Test LO definitions validator
    lo_valid, lo_messages = test_component_validator('lo_definitions', SAMPLE_LO_DEFINITIONS)
    results['validators']['lo_definitions'] = {
        'success': lo_valid,
        'messages': lo_messages
    }
    
    # Test registry validator
    logger.info("=== Testing Registry Validator ===")
    components = {
        'roles': SAMPLE_ROLES,
        'entities': SAMPLE_ENTITIES,
        'go_definitions': SAMPLE_GO_DEFINITIONS,
        'lo_definitions': SAMPLE_LO_DEFINITIONS
    }
    registry_valid, registry_messages = test_registry_validator(components)
    results['registry'] = {
        'success': registry_valid,
        'messages': registry_messages
    }
    
    # Test YAML assembler
    logger.info("=== Testing YAML Assembler ===")
    assembly_success, assembled_yaml, assembly_messages = test_yaml_assembler(components)
    results['assembler'] = {
        'success': assembly_success,
        'messages': assembly_messages
    }
    
    # Test ID generator
    logger.info("=== Testing ID Generator ===")
    id_gen_success, id_gen_messages = test_id_generator()
    results['id_generator'] = {
        'success': id_gen_success,
        'messages': id_gen_messages
    }
    
    # Test component deployers
    logger.info("=== Testing Component Deployers ===")
    
    # Create temporary schema
    schema_name = "workflow_temp"
    success, messages = create_temp_schema(schema_name)
    if not success:
        logger.error(f"Failed to create temporary schema: {messages}")
        results['deployers'] = {
            'roles': {'success': False, 'messages': ["Failed to create temporary schema"]},
            'entities': {'success': False, 'messages': ["Failed to create temporary schema"]},
            'go_definitions': {'success': False, 'messages': ["Failed to create temporary schema"]},
            'lo_definitions': {'success': False, 'messages': ["Failed to create temporary schema"]}
        }
    else:
        # Test deployers in the correct order: roles, entities, GO definitions, LO definitions
        # This ensures that dependencies are created before they are referenced
        
        # 1. Deploy roles first
        roles_deploy_success, roles_deploy_messages = test_component_deployer('roles', SAMPLE_ROLES)
        results['deployers']['roles'] = {
            'success': roles_deploy_success,
            'messages': roles_deploy_messages
        }
        
        # 2. Deploy entities next
        entities_deploy_success, entities_deploy_messages = test_component_deployer('entities', SAMPLE_ENTITIES)
        results['deployers']['entities'] = {
            'success': entities_deploy_success,
            'messages': entities_deploy_messages
        }
        
        # 3. Deploy GO definitions
        go_deploy_success, go_deploy_messages = test_component_deployer('go_definitions', SAMPLE_GO_DEFINITIONS)
        results['deployers']['go_definitions'] = {
            'success': go_deploy_success,
            'messages': go_deploy_messages
        }
        
        # 4. Deploy LO definitions last, after their dependencies (GOs) are created
        if go_deploy_success:
            # Debug: Check if the GO exists in the database
            from db_utils import execute_query
            success, _, result = execute_query(
                f"SELECT go_id, name FROM {schema_name}.global_objectives WHERE go_id = 'go_employeeonboarding'",
                schema_name=schema_name
            )
            if success and result:
                logger.info(f"GO 'go_employeeonboarding' exists in the database: {result}")
                
                # Create a single connection for both GO and LO deployments
                conn = None
                try:
                    # Get database connection
                    from common.db_connection import get_db_connection
                    conn = get_db_connection()
                    
                    # Set the connection to use deferred constraints
                    cursor = conn.cursor()
                    cursor.execute("SET CONSTRAINTS ALL DEFERRED")
                    
                    # Now deploy the LO definitions using the same connection
                    # We'll need to modify the test_component_deployer function to accept a connection
                    lo_deploy_success, lo_deploy_messages = deploy_lo_definitions_with_connection(
                        SAMPLE_LO_DEFINITIONS, schema_name, conn
                    )
                    results['deployers']['lo_definitions'] = {
                        'success': lo_deploy_success,
                        'messages': lo_deploy_messages
                    }
                    
                    # Commit the transaction
                    conn.commit()
                    logger.info("Committed transaction for LO deployment")
                except Exception as e:
                    # Rollback in case of error
                    if conn:
                        conn.rollback()
                    logger.error(f"Transaction error: {str(e)}", exc_info=True)
                    results['deployers']['lo_definitions'] = {
                        'success': False,
                        'messages': [f"LO deployment error: {str(e)}"]
                    }
                finally:
                    # Close connection
                    if conn and not conn.closed:
                        conn.close()
            else:
                logger.warning(f"GO 'go_employeeonboarding' does not exist in the database")
                
                # Try to find the GO with a different ID
                success, _, result = execute_query(
                    f"SELECT go_id, name FROM {schema_name}.global_objectives",
                    schema_name=schema_name
                )
                if success and result:
                    logger.info(f"GOs in the database: {result}")
                else:
                    logger.warning(f"No GOs found in the database")
                
                # Skip LO deployment if GO doesn't exist
                results['deployers']['lo_definitions'] = {
                    'success': False,
                    'messages': ["Skipped LO deployment because GO 'go_employeeonboarding' does not exist in the database"]
                }
        else:
            # Skip LO deployment if GO deployment failed
            results['deployers']['lo_definitions'] = {
                'success': False,
                'messages': ["Skipped LO deployment because GO deployment failed"]
            }

def deploy_lo_definitions_with_connection(lo_yaml: str, schema_name: str, conn) -> Tuple[bool, List[str]]:
    """
    Deploy LO definitions using an existing database connection.
    
    Args:
        lo_yaml: YAML string of LO definitions
        schema_name: Schema name to deploy to
        conn: Database connection
        
    Returns:
        Tuple containing:
            - Boolean indicating if deployment was successful
            - List of messages (warnings, errors, or success messages)
    """
    try:
        # Parse YAML
        import yaml
        lo_data = yaml.safe_load(lo_yaml)
        
        # Deploy LO definitions
        from deployers.lo_deployer import deploy_lo_definitions_with_connection
        success, messages = deploy_lo_definitions_with_connection(lo_data, schema_name, conn)
        
        return success, messages
    except Exception as e:
        error_msg = f"LO deployment error: {str(e)}"
        logger.error(error_msg, exc_info=True)
        return False, [error_msg]

def run_all_tests() -> str:
    """
    Run all tests.
    
    Returns:
        Path to the test results file
    """
    logger.info("Running all tests")
    
    # Initialize results dictionary
    results = {
        'validators': {},
        'registry': {},
        'assembler': {},
        'id_generator': {},
        'deployers': {}
    }
    
    # Test component validators
    logger.info("=== Testing Component Validators ===")
    
    # Test roles validator
    roles_valid, roles_messages = test_component_validator('roles', SAMPLE_ROLES)
    results['validators']['roles'] = {
        'success': roles_valid,
        'messages': roles_messages
    }
    
    # Test entities validator
    entities_valid, entities_messages = test_component_validator('entities', SAMPLE_ENTITIES)
    results['validators']['entities'] = {
        'success': entities_valid,
        'messages': entities_messages
    }
    
    # Test GO definitions validator
    go_valid, go_messages = test_component_validator('go_definitions', SAMPLE_GO_DEFINITIONS)
    results['validators']['go_definitions'] = {
        'success': go_valid,
        'messages': go_messages
    }
    
    # Test LO definitions validator
    lo_valid, lo_messages = test_component_validator('lo_definitions', SAMPLE_LO_DEFINITIONS)
    results['validators']['lo_definitions'] = {
        'success': lo_valid,
        'messages': lo_messages
    }
    
    # Test registry validator
    logger.info("=== Testing Registry Validator ===")
    components = {
        'roles': SAMPLE_ROLES,
        'entities': SAMPLE_ENTITIES,
        'go_definitions': SAMPLE_GO_DEFINITIONS,
        'lo_definitions': SAMPLE_LO_DEFINITIONS
    }
    registry_valid, registry_messages = test_registry_validator(components)
    results['registry'] = {
        'success': registry_valid,
        'messages': registry_messages
    }
    
    # Test YAML assembler
    logger.info("=== Testing YAML Assembler ===")
    assembly_success, assembled_yaml, assembly_messages = test_yaml_assembler(components)
    results['assembler'] = {
        'success': assembly_success,
        'messages': assembly_messages
    }
    
    # Test ID generator
    logger.info("=== Testing ID Generator ===")
    id_gen_success, id_gen_messages = test_id_generator()
    results['id_generator'] = {
        'success': id_gen_success,
        'messages': id_gen_messages
    }
    
    # Test component deployers
    logger.info("=== Testing Component Deployers ===")
    
    # Create temporary schema
    schema_name = "workflow_temp"
    success, messages = create_temp_schema(schema_name)
    if not success:
        logger.error(f"Failed to create temporary schema: {messages}")
        results['deployers'] = {
            'roles': {'success': False, 'messages': ["Failed to create temporary schema"]},
            'entities': {'success': False, 'messages': ["Failed to create temporary schema"]},
            'go_definitions': {'success': False, 'messages': ["Failed to create temporary schema"]},
            'lo_definitions': {'success': False, 'messages': ["Failed to create temporary schema"]}
        }
    else:
        # Test deployers in the correct order: roles, entities, GO definitions, LO definitions
        # This ensures that dependencies are created before they are referenced
        
        # 1. Deploy roles first
        roles_deploy_success, roles_deploy_messages = test_component_deployer('roles', SAMPLE_ROLES)
        results['deployers']['roles'] = {
            'success': roles_deploy_success,
            'messages': roles_deploy_messages
        }
        
        # 2. Deploy entities next
        entities_deploy_success, entities_deploy_messages = test_component_deployer('entities', SAMPLE_ENTITIES)
        results['deployers']['entities'] = {
            'success': entities_deploy_success,
            'messages': entities_deploy_messages
        }
        
        # 3. Deploy GO definitions
        go_deploy_success, go_deploy_messages = test_component_deployer('go_definitions', SAMPLE_GO_DEFINITIONS)
        results['deployers']['go_definitions'] = {
            'success': go_deploy_success,
            'messages': go_deploy_messages
        }
        
        # 4. Deploy LO definitions last, after their dependencies (GOs) are created
        if go_deploy_success:
            # Debug: Check if the GO exists in the database
            from db_utils import execute_query
            success, _, result = execute_query(
                f"SELECT go_id, name FROM {schema_name}.global_objectives WHERE go_id = 'go_employeeonboarding'",
                schema_name=schema_name
            )
            if success and result:
                logger.info(f"GO 'go_employeeonboarding' exists in the database: {result}")
                
                # Create a single connection for both GO and LO deployments
                conn = None
                try:
                    # Get database connection
                    from common.db_connection import get_db_connection
                    conn = get_db_connection()
                    
                    # Set the connection to use deferred constraints
                    cursor = conn.cursor()
                    cursor.execute("SET CONSTRAINTS ALL DEFERRED")
                    
                    # Now deploy the LO definitions using the same connection
                    # We'll need to modify the test_component_deployer function to accept a connection
                    lo_deploy_success, lo_deploy_messages = deploy_lo_definitions_with_connection(
                        SAMPLE_LO_DEFINITIONS, schema_name, conn
                    )
                    results['deployers']['lo_definitions'] = {
                        'success': lo_deploy_success,
                        'messages': lo_deploy_messages
                    }
                    
                    # Commit the transaction
                    conn.commit()
                    logger.info("Committed transaction for LO deployment")
                except Exception as e:
                    # Rollback in case of error
                    if conn:
                        conn.rollback()
                    logger.error(f"Transaction error: {str(e)}", exc_info=True)
                    results['deployers']['lo_definitions'] = {
                        'success': False,
                        'messages': [f"LO deployment error: {str(e)}"]
                    }
                finally:
                    # Close connection
                    if conn and not conn.closed:
                        conn.close()
            else:
                logger.warning(f"GO 'go_employeeonboarding' does not exist in the database")
                
                # Try to find the GO with a different ID
                success, _, result = execute_query(
                    f"SELECT go_id, name FROM {schema_name}.global_objectives",
                    schema_name=schema_name
                )
                if success and result:
                    logger.info(f"GOs in the database: {result}")
                else:
                    logger.warning(f"No GOs found in the database")
                
                # Skip LO deployment if GO doesn't exist
                results['deployers']['lo_definitions'] = {
                    'success': False,
                    'messages': ["Skipped LO deployment because GO 'go_employeeonboarding' does not exist in the database"]
                }
        else:
            # Skip LO deployment if GO deployment failed
            results['deployers']['lo_definitions'] = {
                'success': False,
                'messages': ["Skipped LO deployment because GO deployment failed"]
            }
    
    # Print summary
    logger.info("=== Test Summary ===")
    logger.info(f"Component Validators:")
    logger.info(f"- Roles: {'PASS' if results['validators'].get('roles', {}).get('success', False) else 'FAIL'}")
    logger.info(f"- Entities: {'PASS' if results['validators'].get('entities', {}).get('success', False) else 'FAIL'}")
    logger.info(f"- GO Definitions: {'PASS' if results['validators'].get('go_definitions', {}).get('success', False) else 'FAIL'}")
    logger.info(f"- LO Definitions: {'PASS' if results['validators'].get('lo_definitions', {}).get('success', False) else 'FAIL'}")
    logger.info(f"Registry Validator: {'PASS' if results['registry'].get('success', False) else 'FAIL'}")
    logger.info(f"YAML Assembler: {'PASS' if results['assembler'].get('success', False) else 'FAIL'}")
    logger.info(f"ID Generator: {'PASS' if results['id_generator'].get('success', False) else 'FAIL'}")
    logger.info(f"Component Deployers:")
    logger.info(f"- Roles: {'PASS' if results['deployers'].get('roles', {}).get('success', False) else 'FAIL'}")
    logger.info(f"- Entities: {'PASS' if results['deployers'].get('entities', {}).get('success', False) else 'FAIL'}")
    logger.info(f"- GO Definitions: {'PASS' if results['deployers'].get('go_definitions', {}).get('success', False) else 'FAIL'}")
    logger.info(f"- LO Definitions: {'PASS' if results['deployers'].get('lo_definitions', {}).get('success', False) else 'FAIL'}")
    
    # Write results to file
    return write_results_to_file(results)

def main() -> None:
    """
    Main function.
    """
    parser = argparse.ArgumentParser(description='Test YAML Builder v2 Components')
    parser.add_argument('--component', choices=['roles', 'entities', 'go_definitions', 'lo_definitions', 'all'],
                        default='all', help='Component to test')
    parser.add_argument('--test-type', choices=['validator', 'deployer', 'all'],
                        default='all', help='Type of test to run')
    parser.add_argument('--use-temp-schema', action='store_true',
                        help='Use temporary schema for deployment tests')
    
    args = parser.parse_args()
    
    if args.component == 'all' and args.test_type == 'all':
        output_file = run_all_tests()
        logger.info(f"All tests completed. Results written to {output_file}")
        return
    
    # Initialize results dictionary
    results = {
        'validators': {},
        'registry': {},
        'assembler': {},
        'id_generator': {},
        'deployers': {}
    }
    
    # Get component YAML
    component_yaml = None
    if args.component == 'roles':
        component_yaml = SAMPLE_ROLES
    elif args.component == 'entities':
        component_yaml = SAMPLE_ENTITIES
    elif args.component == 'go_definitions':
        component_yaml = SAMPLE_GO_DEFINITIONS
    elif args.component == 'lo_definitions':
        component_yaml = SAMPLE_LO_DEFINITIONS
    
    # Run tests
    if args.test_type == 'validator' or args.test_type == 'all':
        success, messages = test_component_validator(args.component, component_yaml)
        results['validators'][args.component] = {
            'success': success,
            'messages': messages
        }
    
    if args.test_type == 'deployer' or args.test_type == 'all':
        success, messages = test_component_deployer(args.component, component_yaml, args.use_temp_schema)
        results['deployers'][args.component] = {
            'success': success,
            'messages': messages
        }
    
    # Write results to file
    output_file = write_results_to_file(results)
    logger.info(f"Tests completed. Results written to {output_file}")

if __name__ == '__main__':
    main()
