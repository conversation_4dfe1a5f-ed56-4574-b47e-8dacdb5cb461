#!/usr/bin/env python3

import sys
import os
from pipeline_runner import run_pipeline_from_text

def main():
    if len(sys.argv) != 2:
        print("Usage: python pipeline_runner_from_terminal.py <yaml_file_path>")
        sys.exit(1)
    
    yaml_file_path = sys.argv[1]
    
    if not os.path.exists(yaml_file_path):
        print(f"Error: File '{yaml_file_path}' does not exist.")
        sys.exit(1)
    
    try:
        with open(yaml_file_path, 'r') as f:
            yaml_text = f.read()
        
        print(f"Running pipeline for YAML file: {yaml_file_path}")
        success, message = run_pipeline_from_text(yaml_text)
        
        if success:
            print("✅ Pipeline execution successful!")
            print(f"Message: {message}")
        else:
            print("❌ Pipeline execution failed!")
            print(f"Error: {message}")
    
    except Exception as e:
        print(f"❌ An error occurred: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    main()
