"""
Permission Service for the Workflow System.

This module provides permission evaluation and management services:
- Permission checking
- Role-based access control
- Organizational hierarchy-based permissions
- Contextual permissions
"""

import logging
from typing import Dict, Any, List, Optional, Set, Union
from enum import Enum
from datetime import datetime, time

from sqlalchemy.orm import Session
from sqlalchemy.sql import text
from fastapi import HTT<PERSON>Ex<PERSON>, status

from app.auth.auth_middleware import SecurityContext

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("permission_service")

class PermissionType(str, Enum):
    """Permission types supported by the system."""
    READ = "read"
    WRITE = "write"
    DELETE = "delete"
    EXECUTE = "execute"
    MANAGE = "manage"
    ADMIN = "admin"

class ResourceType(str, Enum):
    """Resource types that can be protected by permissions."""
    ENTITY = "entity"
    OBJECTIVE = "objective"
    FUNCTION = "function"
    SYSTEM = "system"
    ORGANIZATION = "organization"

class PermissionContext:
    """
    Context for permission evaluation.
    
    Contains information about the resource being accessed, the action being performed,
    and any additional context needed for permission evaluation.
    """
    
    def __init__(
        self,
        resource_type: ResourceType,
        resource_id: str,
        permission_type: PermissionType,
        tenant_id: Optional[str] = None,
        org_unit_id: Optional[str] = None,
        additional_context: Optional[Dict[str, Any]] = None
    ):
        """
        Initialize the permission context.
        
        Args:
            resource_type: Type of resource being accessed
            resource_id: ID of resource being accessed
            permission_type: Type of permission being checked
            tenant_id: Tenant ID
            org_unit_id: Organizational unit ID
            additional_context: Additional context for permission evaluation
        """
        self.resource_type = resource_type
        self.resource_id = resource_id
        self.permission_type = permission_type
        self.tenant_id = tenant_id
        self.org_unit_id = org_unit_id
        self.additional_context = additional_context or {}
        
    def to_dict(self) -> Dict[str, Any]:
        """
        Convert the permission context to a dictionary.
        
        Returns:
            Dict[str, Any]: Dictionary representation of the permission context
        """
        return {
            "resource_type": self.resource_type,
            "resource_id": self.resource_id,
            "permission_type": self.permission_type,
            "tenant_id": self.tenant_id,
            "org_unit_id": self.org_unit_id,
            "additional_context": self.additional_context
        }

class PermissionService:
    """
    Permission Service for the Workflow System.
    
    Provides permission evaluation and management services.
    """
    
    def __init__(self, db_session: Session):
        """
        Initialize the permission service.
        
        Args:
            db_session: SQLAlchemy database session
        """
        self.db = db_session
        self.logger = logging.getLogger("permission_service")
        self._cache = {}  # Cache for expensive lookups
        
    def check_permission(
        self,
        security_context: SecurityContext,
        permission_context: PermissionContext
    ) -> bool:
        """
        Check if a user has permission to perform an action on a resource.
        
        Args:
            security_context: Security context with user information
            permission_context: Permission context with resource information
            
        Returns:
            bool: True if the user has permission, False otherwise
        """
        # If user is not authenticated, deny access
        if not security_context.authenticated:
            return False
            
        # Check if user has admin role, which grants all permissions
        if "Administrator" in security_context.roles:
            return True
            
        # Check direct permissions
        if self._check_direct_permission(security_context, permission_context):
            return True
            
        # Check role-based permissions
        if self._check_role_permission(security_context, permission_context):
            return True
            
        # Check organizational hierarchy-based permissions
        if self._check_org_hierarchy_permission(security_context, permission_context):
            return True
            
        # Check contextual permissions
        if self._check_contextual_permission(security_context, permission_context):
            return True
            
        # No permission found
        return False
        
    def check_permissions(
        self,
        security_context: SecurityContext,
        permission_contexts: List[PermissionContext]
    ) -> Dict[str, bool]:
        """
        Check multiple permissions at once.
        
        Args:
            security_context: Security context with user information
            permission_contexts: List of permission contexts
            
        Returns:
            Dict[str, bool]: Dictionary of permission results
        """
        results = {}
        
        for context in permission_contexts:
            key = f"{context.resource_type}:{context.resource_id}:{context.permission_type}"
            results[key] = self.check_permission(security_context, context)
            
        return results
        
    def require_permission(
        self,
        security_context: SecurityContext,
        permission_context: PermissionContext
    ) -> None:
        """
        Require a permission, raising an exception if not granted.
        
        Args:
            security_context: Security context with user information
            permission_context: Permission context with resource information
            
        Raises:
            HTTPException: If the user does not have the required permission
        """
        if not self.check_permission(security_context, permission_context):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=f"Permission denied: {permission_context.permission_type} on {permission_context.resource_type} {permission_context.resource_id}"
            )
            
    def get_user_permissions(
        self,
        security_context: SecurityContext,
        resource_type: Optional[ResourceType] = None,
        resource_id: Optional[str] = None
    ) -> List[Dict[str, Any]]:
        """
        Get all permissions for a user.
        
        Args:
            security_context: Security context with user information
            resource_type: Optional resource type to filter by
            resource_id: Optional resource ID to filter by
            
        Returns:
            List[Dict[str, Any]]: List of permissions
        """
        try:
            # Build query based on filters
            query = """
            SELECT DISTINCT
                pc.context_id,
                pc.name,
                pc.context_type,
                pc.context_rules,
                pt.permission_id
            FROM workflow_runtime.user_roles ur
            JOIN workflow_runtime.roles r ON ur.role = r.name
            JOIN workflow_runtime.role_permissions rp ON r.role_id = rp.role_id
            JOIN workflow_runtime.permission_contexts pc ON rp.context_id = pc.context_id
            JOIN workflow_runtime.permission_types pt ON pt.permission_id IN (
                SELECT jsonb_array_elements_text(pc.context_rules->'permissions')
            )
            WHERE ur.user_id = :user_id
            """
            
            params = {"user_id": security_context.user_id}
            
            if resource_type:
                query += " AND pc.context_type = :resource_type"
                params["resource_type"] = resource_type
                
            if resource_id:
                query += " AND pc.context_rules->>'resource_id' = :resource_id"
                params["resource_id"] = resource_id
                
            result = self.db.execute(text(query), params).fetchall()
            
            permissions = []
            for row in result:
                context_rules = row.context_rules if isinstance(row.context_rules, dict) else {}
                
                permissions.append({
                    "context_id": row.context_id,
                    "name": row.name,
                    "context_type": row.context_type,
                    "permission_id": row.permission_id,
                    "resource_id": context_rules.get("resource_id"),
                    "conditions": context_rules.get("conditions", {})
                })
                
            return permissions
            
        except Exception as e:
            self.logger.error(f"Error getting user permissions: {str(e)}")
            return []
            
    def _check_direct_permission(
        self,
        security_context: SecurityContext,
        permission_context: PermissionContext
    ) -> bool:
        """
        Check if a user has direct permission to perform an action on a resource.
        
        Args:
            security_context: Security context with user information
            permission_context: Permission context with resource information
            
        Returns:
            bool: True if the user has direct permission, False otherwise
        """
        # Check if the permission is in the security context
        return permission_context.permission_type in security_context.permissions
        
    def _check_role_permission(
        self,
        security_context: SecurityContext,
        permission_context: PermissionContext
    ) -> bool:
        """
        Check if a user has permission through their roles.
        
        Args:
            security_context: Security context with user information
            permission_context: Permission context with resource information
            
        Returns:
            bool: True if the user has permission through roles, False otherwise
        """
        try:
            # Check if any of the user's roles have the required permission
            query = """
            SELECT COUNT(*) > 0 as has_permission
            FROM workflow_runtime.roles r
            JOIN workflow_runtime.role_permissions rp ON r.role_id = rp.role_id
            JOIN workflow_runtime.permission_contexts pc ON rp.context_id = pc.context_id
            WHERE r.name = ANY(:roles)
            AND pc.context_type = :resource_type
            AND (
                pc.context_rules->>'resource_id' = :resource_id
                OR pc.context_rules->>'resource_id' = '*'
            )
            AND :permission_type IN (
                SELECT jsonb_array_elements_text(pc.context_rules->'permissions')
            )
            """
            
            result = self.db.execute(text(query), {
                "roles": security_context.roles,
                "resource_type": permission_context.resource_type,
                "resource_id": permission_context.resource_id,
                "permission_type": permission_context.permission_type
            }).fetchone()
            
            return result and result.has_permission
            
        except Exception as e:
            self.logger.error(f"Error checking role permission: {str(e)}")
            return False
            
    def _check_org_hierarchy_permission(
        self,
        security_context: SecurityContext,
        permission_context: PermissionContext
    ) -> bool:
        """
        Check if a user has permission through organizational hierarchy.
        
        Args:
            security_context: Security context with user information
            permission_context: Permission context with resource information
            
        Returns:
            bool: True if the user has permission through org hierarchy, False otherwise
        """
        # If no org unit is specified, skip this check
        if not permission_context.org_unit_id or not security_context.org_units:
            return False
            
        try:
            # Check if the user is in the same org unit or a parent org unit
            query = """
            WITH RECURSIVE org_hierarchy AS (
                -- Base case: start with the user's org units
                SELECT ou.org_unit_id, ou.parent_org_unit_id
                FROM workflow_runtime.organizational_units ou
                WHERE ou.org_unit_id = ANY(:user_org_units)
                
                UNION ALL
                
                -- Recursive case: add parent org units
                SELECT ou.org_unit_id, ou.parent_org_unit_id
                FROM workflow_runtime.organizational_units ou
                JOIN org_hierarchy oh ON ou.org_unit_id = oh.parent_org_unit_id
            )
            SELECT COUNT(*) > 0 as has_permission
            FROM org_hierarchy
            WHERE org_unit_id = :resource_org_unit
            """
            
            result = self.db.execute(text(query), {
                "user_org_units": security_context.org_units,
                "resource_org_unit": permission_context.org_unit_id
            }).fetchone()
            
            return result and result.has_permission
            
        except Exception as e:
            self.logger.error(f"Error checking org hierarchy permission: {str(e)}")
            return False
            
    def _check_contextual_permission(
        self,
        security_context: SecurityContext,
        permission_context: PermissionContext
    ) -> bool:
        """
        Check if a user has permission based on contextual rules.
        
        Args:
            security_context: Security context with user information
            permission_context: Permission context with resource information
            
        Returns:
            bool: True if the user has contextual permission, False otherwise
        """
        try:
            # Get contextual permissions for the user
            query = """
            SELECT pc.context_rules
            FROM workflow_runtime.user_roles ur
            JOIN workflow_runtime.roles r ON ur.role = r.name
            JOIN workflow_runtime.role_permissions rp ON r.role_id = rp.role_id
            JOIN workflow_runtime.permission_contexts pc ON rp.context_id = pc.context_id
            WHERE ur.user_id = :user_id
            AND pc.context_type = :resource_type
            AND (
                pc.context_rules->>'resource_id' = :resource_id
                OR pc.context_rules->>'resource_id' = '*'
            )
            AND pc.context_rules ? 'conditions'
            """
            
            result = self.db.execute(text(query), {
                "user_id": security_context.user_id,
                "resource_type": permission_context.resource_type,
                "resource_id": permission_context.resource_id
            }).fetchall()
            
            # Evaluate each contextual rule
            for row in result:
                context_rules = row.context_rules if isinstance(row.context_rules, dict) else {}
                conditions = context_rules.get("conditions", {})
                
                # Check if the permission is in the allowed permissions
                permissions = context_rules.get("permissions", [])
                if permission_context.permission_type not in permissions:
                    continue
                    
                # Evaluate conditions
                if self._evaluate_conditions(conditions, security_context, permission_context):
                    return True
                    
            return False
            
        except Exception as e:
            self.logger.error(f"Error checking contextual permission: {str(e)}")
            return False
            
    def _evaluate_conditions(
        self,
        conditions: Dict[str, Any],
        security_context: SecurityContext,
        permission_context: PermissionContext
    ) -> bool:
        """
        Evaluate conditions for contextual permissions.
        
        Args:
            conditions: Conditions to evaluate
            security_context: Security context with user information
            permission_context: Permission context with resource information
            
        Returns:
            bool: True if conditions are met, False otherwise
        """
        # If no conditions, return True
        if not conditions:
            return True
            
        # Track condition results
        condition_results = []
            
        # Check time-based conditions
        if "time_range" in conditions:
            time_range = conditions["time_range"]
            time_result = self._evaluate_time_condition(time_range, permission_context)
            condition_results.append(time_result)
            
        # Check attribute-based conditions
        if "attributes" in conditions:
            attributes = conditions["attributes"]
            attr_result = self._evaluate_attribute_condition(attributes, permission_context)
            condition_results.append(attr_result)
            
        # Check ownership condition
        if "is_owner" in conditions and conditions["is_owner"]:
            # Check if the user is the owner of the resource
            if "owner_id" in permission_context.additional_context:
                owner_result = permission_context.additional_context["owner_id"] == security_context.user_id
                condition_results.append(owner_result)
                
        # Check custom condition
        if "custom_condition" in conditions:
            custom_condition = conditions["custom_condition"]
            custom_result = self._evaluate_custom_condition(custom_condition, permission_context)
            condition_results.append(custom_result)
            
        # If no conditions were evaluated, return False
        if not condition_results:
            return False
            
        # All conditions must be met
        return all(condition_results)
        
    def _evaluate_time_condition(
        self,
        time_range: Dict[str, Any],
        permission_context: PermissionContext
    ) -> bool:
        """
        Evaluate time-based condition.
        
        Args:
            time_range: Time range condition
            permission_context: Permission context with resource information
            
        Returns:
            bool: True if the condition is met, False otherwise
        """
        try:
            # Get current time from context or use current time
            current_time_str = permission_context.additional_context.get("current_time")
            
            if current_time_str:
                # Parse ISO format datetime string
                current_time = datetime.fromisoformat(current_time_str)
            else:
                # Use current time
                current_time = datetime.now()
                
            # Get current day of week (0 = Monday, 6 = Sunday)
            current_day = current_time.strftime("%A")
            
            # Check if current day is in allowed days
            allowed_days = time_range.get("days", ["Monday", "Tuesday", "Wednesday", "Thursday", "Friday"])
            if current_day not in allowed_days:
                return False
                
            # Get start and end times
            start_time_str = time_range.get("start", "00:00:00")
            end_time_str = time_range.get("end", "23:59:59")
            
            # Parse time strings
            start_time = datetime.strptime(start_time_str, "%H:%M:%S").time()
            end_time = datetime.strptime(end_time_str, "%H:%M:%S").time()
            
            # Check if current time is within range
            current_time_only = current_time.time()
            
            return start_time <= current_time_only <= end_time
            
        except Exception as e:
            self.logger.error(f"Error evaluating time condition: {str(e)}")
            return False
            
    def _evaluate_attribute_condition(
        self,
        attributes: Dict[str, Any],
        permission_context: PermissionContext
    ) -> bool:
        """
        Evaluate attribute-based condition.
        
        Args:
            attributes: Attribute conditions
            permission_context: Permission context with resource information
            
        Returns:
            bool: True if the condition is met, False otherwise
        """
        try:
            # Check if all required attributes are present and match
            for attr_name, attr_value in attributes.items():
                # Get attribute from context
                context_value = permission_context.additional_context.get(attr_name)
                
                # If attribute is missing or doesn't match, return False
                if context_value is None or context_value != attr_value:
                    return False
                    
            # All attributes matched
            return True
            
        except Exception as e:
            self.logger.error(f"Error evaluating attribute condition: {str(e)}")
            return False
            
    def _evaluate_custom_condition(
        self,
        custom_condition: Dict[str, Any],
        permission_context: PermissionContext
    ) -> bool:
        """
        Evaluate custom condition.
        
        Args:
            custom_condition: Custom condition
            permission_context: Permission context with resource information
            
        Returns:
            bool: True if the condition is met, False otherwise
        """
        try:
            # Get condition type
            condition_type = custom_condition.get("type")
            
            if not condition_type:
                return False
                
            # Get field and value
            field = custom_condition.get("field")
            expected_value = custom_condition.get("value")
            
            if not field:
                return False
                
            # Get actual value from context
            actual_value = permission_context.additional_context.get(field)
            
            # Evaluate based on condition type
            if condition_type == "equals":
                return actual_value == expected_value
                
            elif condition_type == "not_equals":
                return actual_value != expected_value
                
            elif condition_type == "contains":
                if isinstance(actual_value, list):
                    return expected_value in actual_value
                elif isinstance(actual_value, str):
                    return expected_value in actual_value
                else:
                    return False
                    
            elif condition_type == "starts_with":
                if isinstance(actual_value, str):
                    return actual_value.startswith(expected_value)
                else:
                    return False
                    
            elif condition_type == "ends_with":
                if isinstance(actual_value, str):
                    return actual_value.endswith(expected_value)
                else:
                    return False
                    
            elif condition_type == "greater_than":
                try:
                    return float(actual_value) > float(expected_value)
                except (ValueError, TypeError):
                    return False
                    
            elif condition_type == "less_than":
                try:
                    return float(actual_value) < float(expected_value)
                except (ValueError, TypeError):
                    return False
                    
            elif condition_type == "in_range":
                try:
                    min_value = float(custom_condition.get("min", float("-inf")))
                    max_value = float(custom_condition.get("max", float("inf")))
                    actual_float = float(actual_value)
                    return min_value <= actual_float <= max_value
                except (ValueError, TypeError):
                    return False
                    
            # Unknown condition type
            return False
            
        except Exception as e:
            self.logger.error(f"Error evaluating custom condition: {str(e)}")
            return False
