#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to create the necessary tables for business rules.
"""

import os
import logging
import psycopg2

# Set up logging
logging.basicConfig(level=logging.INFO, 
                   format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
                   handlers=[
                       logging.FileHandler('/home/<USER>/workflow-system/chat-yaml-builder/v2/business_rule_tables.log'),
                       logging.StreamHandler()
                   ])
logger = logging.getLogger('create_business_rule_tables')

def get_db_connection(schema_name=None):
    """
    Get a database connection.
    
    Args:
        schema_name: Optional schema name to set as the search path
        
    Returns:
        Database connection object
    """
    # Get database connection parameters from environment variables or use defaults
    db_host = os.environ.get('DB_HOST', '**********')
    db_port = os.environ.get('DB_PORT', '5432')
    db_name = os.environ.get('DB_NAME', 'workflow_system')
    db_user = os.environ.get('DB_USER', 'postgres')
    db_password = os.environ.get('DB_PASSWORD', 'workflow_postgres_secure_password')
    
    # Connect to the database
    conn = psycopg2.connect(
        host=db_host,
        port=db_port,
        dbname=db_name,
        user=db_user,
        password=db_password
    )
    
    # Set schema search path if provided
    if schema_name:
        with conn.cursor() as cursor:
            cursor.execute(f"SET search_path TO {schema_name}")
            conn.commit()
    
    return conn

def execute_query(query, params=None, schema_name=None):
    """
    Execute a database query.
    
    Args:
        query: SQL query to execute
        params: Query parameters
        schema_name: Schema name to set as search path
        
    Returns:
        Tuple containing:
            - Boolean indicating if query was successful
            - List of messages (warnings, errors, or success messages)
            - Query result rows
    """
    messages = []
    result = []
    conn = None
    
    try:
        # Get database connection
        conn = get_db_connection(schema_name)
        
        # Execute query
        with conn.cursor() as cursor:
            cursor.execute(query, params)
            
            # Get result if query returns rows
            if cursor.description:
                result = cursor.fetchall()
        
        # Commit transaction
        conn.commit()
        
        return True, messages, result
    except Exception as e:
        if conn:
            conn.rollback()
        
        error_msg = f"Query error: {str(e)}"
        messages.append(error_msg)
        logger.error(error_msg)
        return False, messages, result
    finally:
        if conn:
            conn.close()

def check_entities_table(schema_name):
    """
    Check if the entities table has a unique constraint on entity_id.
    
    Args:
        schema_name: Schema name
        
    Returns:
        Boolean indicating if the entities table has a unique constraint on entity_id
    """
    # Check if the entities table exists
    success, messages, result = execute_query(
        f"""
        SELECT EXISTS (
            SELECT FROM information_schema.tables 
            WHERE table_schema = %s
            AND table_name = 'entities'
        )
        """,
        (schema_name,),
        schema_name
    )
    
    if not success or not result or not result[0][0]:
        logger.error("entities table does not exist")
        return False
    
    # Check if the entity_id column has a unique constraint
    success, messages, result = execute_query(
        f"""
        SELECT EXISTS (
            SELECT FROM information_schema.table_constraints
            WHERE table_schema = %s
            AND table_name = 'entities'
            AND constraint_type IN ('PRIMARY KEY', 'UNIQUE')
            AND EXISTS (
                SELECT FROM information_schema.constraint_column_usage
                WHERE table_schema = %s
                AND table_name = 'entities'
                AND column_name = 'entity_id'
                AND constraint_name = information_schema.table_constraints.constraint_name
            )
        )
        """,
        (schema_name, schema_name),
        schema_name
    )
    
    if success and result and result[0][0]:
        logger.info("entities table already has a unique constraint on entity_id")
        return True
    
    logger.warning("entities table exists but doesn't have a unique constraint on entity_id")
    
    # Add a unique constraint to the entities table
    success, messages, _ = execute_query(
        f"""
        ALTER TABLE {schema_name}.entities
        ADD CONSTRAINT entities_entity_id_key UNIQUE (entity_id)
        """,
        schema_name=schema_name
    )
    
    if not success:
        logger.error(f"Failed to add unique constraint to entities table: {messages}")
        return False
    
    logger.info("Added unique constraint to entities table")
    return True

def create_business_rule_tables():
    """
    Create the necessary tables for business rules.
    """
    schema_name = 'workflow_temp'
    
    # First, check if the entities table has a primary key constraint on entity_id
    if not check_entities_table(schema_name):
        return
    
    # Check if the entity_business_rules table exists
    success, messages, result = execute_query(
        f"""
        SELECT EXISTS (
            SELECT FROM information_schema.tables 
            WHERE table_schema = %s
            AND table_name = 'entity_business_rules'
        )
        """,
        (schema_name,),
        schema_name
    )
    
    if success and result and result[0][0]:
        logger.info("entity_business_rules table already exists")
        
        # Check if the rule_id column is the primary key
        success, messages, result = execute_query(
            f"""
            SELECT a.attname
            FROM pg_index i
            JOIN pg_attribute a ON a.attrelid = i.indrelid AND a.attnum = ANY(i.indkey)
            WHERE i.indrelid = '{schema_name}.entity_business_rules'::regclass
            AND i.indisprimary
            """,
            schema_name=schema_name
        )
        
        if success and result and result[0][0] == 'rule_id':
            logger.info("entity_business_rules table already has rule_id as the primary key")
        else:
            logger.warning("entity_business_rules table exists but doesn't have a primary key constraint")
            
            # Drop the table and recreate it with rule_id as the primary key
            logger.info("Dropping entity_business_rules table to recreate it with rule_id as the primary key")
            
            # First, check if business_rule_conditions table exists and drop it if it does
            success, messages, result = execute_query(
                f"""
                SELECT EXISTS (
                    SELECT FROM information_schema.tables 
                    WHERE table_schema = %s
                    AND table_name = 'business_rule_conditions'
                )
                """,
                (schema_name,),
                schema_name
            )
            
            if success and result and result[0][0]:
                logger.info("Dropping business_rule_conditions table first")
                success, messages, _ = execute_query(
                    f"DROP TABLE IF EXISTS {schema_name}.business_rule_conditions",
                    schema_name=schema_name
                )
                
                if not success:
                    logger.error(f"Failed to drop business_rule_conditions table: {messages}")
                    return
                
                logger.info("Dropped business_rule_conditions table")
            
            # Now drop the entity_business_rules table
            success, messages, _ = execute_query(
                f"DROP TABLE IF EXISTS {schema_name}.entity_business_rules CASCADE",
                schema_name=schema_name
            )
            
            if not success:
                logger.error(f"Failed to drop entity_business_rules table: {messages}")
                return
            
            logger.info("Dropped entity_business_rules table")
            
            # Create the entity_business_rules table with a primary key constraint
            success, messages, _ = execute_query(
                f"""
                CREATE TABLE {schema_name}.entity_business_rules (
                    rule_id VARCHAR(255) PRIMARY KEY,
                    entity_id VARCHAR(255) NOT NULL,
                    name VARCHAR(255) NOT NULL,
                    description TEXT,
                    created_at TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (entity_id) REFERENCES {schema_name}.entities(entity_id)
                )
                """,
                schema_name=schema_name
            )
            
            if not success:
                logger.error(f"Failed to create entity_business_rules table: {messages}")
                return
            
            logger.info("Created entity_business_rules table with primary key constraint")
    else:
        # Create the entity_business_rules table with a primary key constraint
        success, messages, _ = execute_query(
            f"""
            CREATE TABLE {schema_name}.entity_business_rules (
                rule_id VARCHAR(255) PRIMARY KEY,
                entity_id VARCHAR(255) NOT NULL,
                name VARCHAR(255) NOT NULL,
                description TEXT,
                created_at TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (entity_id) REFERENCES {schema_name}.entities(entity_id)
            )
            """,
            schema_name=schema_name
        )
        
        if not success:
            logger.error(f"Failed to create entity_business_rules table: {messages}")
            return
        
        logger.info("Created entity_business_rules table with primary key constraint")
    
    # Check if the business_rule_conditions table exists
    success, messages, result = execute_query(
        f"""
        SELECT EXISTS (
            SELECT FROM information_schema.tables 
            WHERE table_schema = %s
            AND table_name = 'business_rule_conditions'
        )
        """,
        (schema_name,),
        schema_name
    )
    
    if success and result and result[0][0]:
        logger.info("business_rule_conditions table already exists")
        
        # Check if the rule_id column has a foreign key constraint
        success, messages, result = execute_query(
            f"""
            SELECT EXISTS (
                SELECT FROM information_schema.table_constraints
                WHERE table_schema = %s
                AND table_name = 'business_rule_conditions'
                AND constraint_type = 'FOREIGN KEY'
            )
            """,
            (schema_name,),
            schema_name
        )
        
        if success and result and result[0][0]:
            logger.info("business_rule_conditions table already has a foreign key constraint")
        else:
            logger.warning("business_rule_conditions table exists but doesn't have a foreign key constraint")
            
            # Drop the table and recreate it with a foreign key constraint
            success, messages, _ = execute_query(
                f"DROP TABLE IF EXISTS {schema_name}.business_rule_conditions",
                schema_name=schema_name
            )
            
            if not success:
                logger.error(f"Failed to drop business_rule_conditions table: {messages}")
                return
            
            logger.info("Dropped business_rule_conditions table")
            
            # Create the business_rule_conditions table with a foreign key constraint
            success, messages, _ = execute_query(
                f"""
                CREATE TABLE {schema_name}.business_rule_conditions (
                    condition_id SERIAL PRIMARY KEY,
                    rule_id VARCHAR(255) NOT NULL,
                    condition_text TEXT NOT NULL,
                    attribute_name VARCHAR(255),
                    operator VARCHAR(255),
                    value TEXT,
                    created_at TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (rule_id) REFERENCES {schema_name}.entity_business_rules(rule_id)
                )
                """,
                schema_name=schema_name
            )
            
            if not success:
                logger.error(f"Failed to create business_rule_conditions table: {messages}")
                return
            
            logger.info("Created business_rule_conditions table with foreign key constraint")
    else:
        # Create the business_rule_conditions table with a foreign key constraint
        success, messages, _ = execute_query(
            f"""
            CREATE TABLE {schema_name}.business_rule_conditions (
                condition_id SERIAL PRIMARY KEY,
                rule_id VARCHAR(255) NOT NULL,
                condition_text TEXT NOT NULL,
                attribute_name VARCHAR(255),
                operator VARCHAR(255),
                value TEXT,
                created_at TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (rule_id) REFERENCES {schema_name}.entity_business_rules(rule_id)
            )
            """,
            schema_name=schema_name
        )
        
        if not success:
            logger.error(f"Failed to create business_rule_conditions table: {messages}")
            return
        
        logger.info("Created business_rule_conditions table with foreign key constraint")
    
    # Check the structure of the entity_attributes table
    success, messages, result = execute_query(
        f"""
        SELECT column_name, is_nullable
        FROM information_schema.columns
        WHERE table_schema = %s
        AND table_name = 'entity_attributes'
        AND column_name = 'display_name'
        """,
        (schema_name,),
        schema_name
    )
    
    if success and result:
        logger.info(f"entity_attributes table has a display_name column with is_nullable={result[0][1]}")
        
        # If display_name is not nullable, alter the table to make it nullable
        if result[0][1] == 'NO':
            success, messages, _ = execute_query(
                f"""
                ALTER TABLE {schema_name}.entity_attributes
                ALTER COLUMN display_name DROP NOT NULL
                """,
                schema_name=schema_name
            )
            
            if not success:
                logger.error(f"Failed to make display_name column nullable: {messages}")
                return
            
            logger.info("Made display_name column nullable")
    else:
        logger.warning("entity_attributes table doesn't have a display_name column")
        
        # Add a display_name column to the entity_attributes table
        success, messages, _ = execute_query(
            f"""
            ALTER TABLE {schema_name}.entity_attributes
            ADD COLUMN display_name VARCHAR(255)
            """,
            schema_name=schema_name
        )
        
        if not success:
            logger.error(f"Failed to add display_name column: {messages}")
            return
        
        logger.info("Added display_name column to entity_attributes table")
    
    logger.info("Successfully created business rule tables")

if __name__ == "__main__":
    create_business_rule_tables()
