#!/usr/bin/env python3
"""
Query the database and display the results.

This script can be used to query the database and display the results in a tabular format.
It's useful for debugging and checking the contents of tables.
"""

import os
import sys
import logging
import argparse
from typing import Dict, List, Tuple, Any, Optional
import json
from tabulate import tabulate

# Add parent directory to path to import common modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from db_utils import execute_query

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('query_db')

def format_value(value: Any) -> str:
    """
    Format a value for display.
    
    Args:
        value: The value to format
        
    Returns:
        The formatted value as a string
    """
    if value is None:
        return "NULL"
    elif isinstance(value, dict) or isinstance(value, list):
        return json.dumps(value, indent=2)
    else:
        return str(value)

def query_database(query: str, params: Optional[Tuple] = None, schema_name: str = "workflow_runtime") -> None:
    """
    Query the database and display the results.
    
    Args:
        query: The SQL query to execute
        params: Optional parameters for the query
        schema_name: The schema name to use
    """
    logger.info(f"Executing query: {query}")
    
    if params:
        logger.info(f"Parameters: {params}")
    
    success, query_messages, result = execute_query(query, params, schema_name=schema_name)
    
    if not success:
        logger.error(f"Query failed: {query_messages}")
        return
    
    if not result:
        logger.info("Query returned no results")
        return
    
    # Get column names
    column_names = []
    
    if hasattr(result, 'description'):
        # If result is a cursor
        column_names = [desc[0] for desc in result.description]
    else:
        # If result is a list of tuples, try to get column names from the query
        # This is a simple approach and might not work for complex queries
        try:
            # Extract column names from SELECT clause
            select_clause = query.lower().split("from")[0].strip()
            if select_clause.startswith("select"):
                select_clause = select_clause[6:].strip()
                
                if select_clause == "*":
                    # If SELECT *, we can't determine column names from the query
                    column_names = [f"Column {i+1}" for i in range(len(result[0]))]
                else:
                    # Extract column names from SELECT clause
                    columns = []
                    current_column = ""
                    in_parentheses = 0
                    
                    for char in select_clause:
                        if char == "," and in_parentheses == 0:
                            columns.append(current_column.strip())
                            current_column = ""
                        else:
                            if char == "(":
                                in_parentheses += 1
                            elif char == ")":
                                in_parentheses -= 1
                            
                            current_column += char
                    
                    if current_column:
                        columns.append(current_column.strip())
                    
                    # Extract column aliases or names
                    for column in columns:
                        if " as " in column.lower():
                            alias = column.lower().split(" as ")[1].strip()
                            column_names.append(alias)
                        else:
                            # Use the last part of the column name (after the last dot)
                            parts = column.split(".")
                            column_names.append(parts[-1].strip())
            else:
                # If query doesn't start with SELECT, use default column names
                column_names = [f"Column {i+1}" for i in range(len(result[0]))]
        except Exception as e:
            # If we can't extract column names from the query, use default column names
            logger.warning(f"Failed to extract column names from query: {e}")
            column_names = [f"Column {i+1}" for i in range(len(result[0]))]
    
    # Format the results
    formatted_result = []
    
    for row in result:
        formatted_row = [format_value(value) for value in row]
        formatted_result.append(formatted_row)
    
    # Display the results
    print(tabulate(formatted_result, headers=column_names, tablefmt="grid"))
    
    logger.info(f"Query returned {len(formatted_result)} rows")

def main():
    """Main function."""
    parser = argparse.ArgumentParser(description='Query the database and display the results')
    parser.add_argument('--query', required=True, help='The SQL query to execute')
    parser.add_argument('--schema', default='workflow_runtime', help='The schema name to use')
    args = parser.parse_args()
    
    query_database(args.query, schema_name=args.schema)

if __name__ == '__main__':
    main()
