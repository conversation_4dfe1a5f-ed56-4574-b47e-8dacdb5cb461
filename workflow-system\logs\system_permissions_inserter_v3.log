2025-06-23 11:36:13,269 - inserters.v3.roles.system_permissions_inserter - INFO - Starting deploy_single_system_permission_to_workflow_temp for permission ID: PERM_ENTITY_EMPLOYEE
2025-06-23 11:36:50,492 - inserters.v3.roles.system_permissions_inserter - INFO - Starting deploy_single_system_permission_to_workflow_temp for permission ID: PERM_ATTR_PERSONAL_LEAVE
2025-06-23 14:03:49,841 - inserters.v3.roles.system_permissions_inserter - INFO - Starting deploy_single_system_permission_to_workflow_temp for permission ID: PERM001
2025-06-23 14:03:49,983 - inserters.v3.roles.system_permissions_inserter - INFO - Starting process_mongo_system_permissions_to_workflow_temp
2025-06-23 14:03:49,986 - inserters.v3.roles.system_permissions_inserter - INFO - Found 1 system permissions with status 'draft' in MongoDB
2025-06-23 14:03:49,995 - inserters.v3.roles.system_permissions_inserter - INFO - Starting insert_system_permission_to_workflow_temp for permission: Employee totalLeaveEntitlement Attribute
2025-06-23 14:03:49,995 - inserters.v3.roles.system_permissions_inserter - INFO - Field validation passed: 5/5 required fields, 10/10 optional fields
2025-06-23 14:03:50,012 - inserters.v3.roles.system_permissions_inserter - INFO - Incremented permission ID from PERM_ATTR_EMP_LEAVE to PERM1
2025-06-23 14:03:50,012 - inserters.v3.roles.system_permissions_inserter - INFO - Additional MongoDB fields preserved: {'original_created_at': '2025-06-23T07:15:42.617971', 'original_updated_at': '2025-06-23T07:15:42.617979', 'original_created_by': 'Tarun', 'original_updated_by': 'Tarun', 'original_permission_id': 'PERM_ATTR_EMP_LEAVE'}
2025-06-23 14:03:50,013 - inserters.v3.roles.system_permissions_inserter - ERROR - Error inserting system permission to workflow_temp: column "actions" is of type jsonb but expression is of type text[]
LINE 6: ...', 'attribute', 'Employee.totalLeaveEntitlement', ARRAY['rea...
                                                             ^
HINT:  You will need to rewrite or cast the expression.

2025-06-23 14:03:50,013 - inserters.v3.roles.system_permissions_inserter - INFO - Completed process_mongo_system_permissions_to_workflow_temp: 0 successful, 1 failed
2025-06-23 14:03:51,734 - inserters.v3.roles.system_permissions_inserter - INFO - Starting process_mongo_system_permissions_to_workflow_temp
2025-06-23 14:03:51,735 - inserters.v3.roles.system_permissions_inserter - INFO - Found 1 system permissions with status 'draft' in MongoDB
2025-06-23 14:03:51,742 - inserters.v3.roles.system_permissions_inserter - INFO - Starting insert_system_permission_to_workflow_temp for permission: Employee totalLeaveEntitlement Attribute
2025-06-23 14:03:51,742 - inserters.v3.roles.system_permissions_inserter - INFO - Field validation passed: 5/5 required fields, 10/10 optional fields
2025-06-23 14:03:51,756 - inserters.v3.roles.system_permissions_inserter - INFO - Incremented permission ID from PERM_ATTR_EMP_LEAVE to PERM1
2025-06-23 14:03:51,756 - inserters.v3.roles.system_permissions_inserter - INFO - Additional MongoDB fields preserved: {'original_created_at': '2025-06-23T07:15:42.617971', 'original_updated_at': '2025-06-23T07:15:42.617979', 'original_created_by': 'Tarun', 'original_updated_by': 'Tarun', 'original_permission_id': 'PERM_ATTR_EMP_LEAVE'}
2025-06-23 14:03:51,757 - inserters.v3.roles.system_permissions_inserter - ERROR - Error inserting system permission to workflow_temp: column "actions" is of type jsonb but expression is of type text[]
LINE 6: ...', 'attribute', 'Employee.totalLeaveEntitlement', ARRAY['rea...
                                                             ^
HINT:  You will need to rewrite or cast the expression.

2025-06-23 14:03:51,757 - inserters.v3.roles.system_permissions_inserter - INFO - Completed process_mongo_system_permissions_to_workflow_temp: 0 successful, 1 failed
2025-06-23 14:09:00,921 - inserters.v3.roles.system_permissions_inserter - INFO - Starting deploy_single_system_permission_to_workflow_temp for permission ID: PERM001
2025-06-23 14:09:01,041 - inserters.v3.roles.system_permissions_inserter - INFO - Starting process_mongo_system_permissions_to_workflow_temp
2025-06-23 14:09:01,043 - inserters.v3.roles.system_permissions_inserter - INFO - Found 1 system permissions with status 'draft' in MongoDB
2025-06-23 14:09:01,052 - inserters.v3.roles.system_permissions_inserter - INFO - Starting insert_system_permission_to_workflow_temp for permission: Employee totalLeaveEntitlement Attribute
2025-06-23 14:09:01,052 - inserters.v3.roles.system_permissions_inserter - INFO - Field validation passed: 5/5 required fields, 10/10 optional fields
2025-06-23 14:09:01,068 - inserters.v3.roles.system_permissions_inserter - INFO - Incremented permission ID from PERM_ATTR_EMP_LEAVE to PERM1
2025-06-23 14:09:01,068 - inserters.v3.roles.system_permissions_inserter - INFO - Additional MongoDB fields preserved: {'original_created_at': '2025-06-23T07:15:42.617971', 'original_updated_at': '2025-06-23T07:15:42.617979', 'original_created_by': 'Tarun', 'original_updated_by': 'Tarun', 'original_permission_id': 'PERM_ATTR_EMP_LEAVE'}
2025-06-23 14:09:01,069 - inserters.v3.roles.system_permissions_inserter - ERROR - Error inserting system permission to workflow_temp: column "actions" is of type jsonb but expression is of type text[]
LINE 6: ...', 'attribute', 'Employee.totalLeaveEntitlement', ARRAY['rea...
                                                             ^
HINT:  You will need to rewrite or cast the expression.

2025-06-23 14:09:01,070 - inserters.v3.roles.system_permissions_inserter - INFO - Completed process_mongo_system_permissions_to_workflow_temp: 0 successful, 1 failed
2025-06-23 14:09:02,854 - inserters.v3.roles.system_permissions_inserter - INFO - Starting process_mongo_system_permissions_to_workflow_temp
2025-06-23 14:09:02,856 - inserters.v3.roles.system_permissions_inserter - INFO - Found 1 system permissions with status 'draft' in MongoDB
2025-06-23 14:09:02,863 - inserters.v3.roles.system_permissions_inserter - INFO - Starting insert_system_permission_to_workflow_temp for permission: Employee totalLeaveEntitlement Attribute
2025-06-23 14:09:02,863 - inserters.v3.roles.system_permissions_inserter - INFO - Field validation passed: 5/5 required fields, 10/10 optional fields
2025-06-23 14:09:02,877 - inserters.v3.roles.system_permissions_inserter - INFO - Incremented permission ID from PERM_ATTR_EMP_LEAVE to PERM1
2025-06-23 14:09:02,877 - inserters.v3.roles.system_permissions_inserter - INFO - Additional MongoDB fields preserved: {'original_created_at': '2025-06-23T07:15:42.617971', 'original_updated_at': '2025-06-23T07:15:42.617979', 'original_created_by': 'Tarun', 'original_updated_by': 'Tarun', 'original_permission_id': 'PERM_ATTR_EMP_LEAVE'}
2025-06-23 14:09:02,878 - inserters.v3.roles.system_permissions_inserter - ERROR - Error inserting system permission to workflow_temp: column "actions" is of type jsonb but expression is of type text[]
LINE 6: ...', 'attribute', 'Employee.totalLeaveEntitlement', ARRAY['rea...
                                                             ^
HINT:  You will need to rewrite or cast the expression.

2025-06-23 14:09:02,878 - inserters.v3.roles.system_permissions_inserter - INFO - Completed process_mongo_system_permissions_to_workflow_temp: 0 successful, 1 failed
2025-06-23 14:32:33,456 - inserters.v3.roles.system_permissions_inserter - INFO - Starting deploy_single_system_permission_to_workflow_temp for permission ID: PERM001
2025-06-23 14:32:33,541 - inserters.v3.roles.system_permissions_inserter - INFO - Starting process_mongo_system_permissions_to_workflow_temp
2025-06-23 14:32:33,544 - inserters.v3.roles.system_permissions_inserter - INFO - Found 1 system permissions with status 'draft' in MongoDB
2025-06-23 14:32:33,552 - inserters.v3.roles.system_permissions_inserter - INFO - Starting insert_system_permission_to_workflow_temp for permission: Employee totalLeaveEntitlement Attribute
2025-06-23 14:32:33,552 - inserters.v3.roles.system_permissions_inserter - INFO - Field validation passed: 5/5 required fields, 10/10 optional fields
2025-06-23 14:32:33,568 - inserters.v3.roles.system_permissions_inserter - INFO - Incremented permission ID from PERM_ATTR_EMP_LEAVE to PERM1
2025-06-23 14:32:33,568 - inserters.v3.roles.system_permissions_inserter - INFO - Additional MongoDB fields preserved: {'original_created_at': '2025-06-23T07:15:42.617971', 'original_updated_at': '2025-06-23T07:15:42.617979', 'original_created_by': 'Tarun', 'original_updated_by': 'Tarun', 'original_permission_id': 'PERM_ATTR_EMP_LEAVE'}
2025-06-23 14:32:33,569 - inserters.v3.roles.system_permissions_inserter - ERROR - Error inserting system permission to workflow_temp: column "actions" is of type jsonb but expression is of type text[]
LINE 6: ...', 'attribute', 'Employee.totalLeaveEntitlement', ARRAY['rea...
                                                             ^
HINT:  You will need to rewrite or cast the expression.

2025-06-23 14:32:33,570 - inserters.v3.roles.system_permissions_inserter - INFO - Completed process_mongo_system_permissions_to_workflow_temp: 0 successful, 1 failed
2025-06-23 14:32:35,093 - inserters.v3.roles.system_permissions_inserter - INFO - Starting process_mongo_system_permissions_to_workflow_temp
2025-06-23 14:32:35,094 - inserters.v3.roles.system_permissions_inserter - INFO - Found 1 system permissions with status 'draft' in MongoDB
2025-06-23 14:32:35,100 - inserters.v3.roles.system_permissions_inserter - INFO - Starting insert_system_permission_to_workflow_temp for permission: Employee totalLeaveEntitlement Attribute
2025-06-23 14:32:35,100 - inserters.v3.roles.system_permissions_inserter - INFO - Field validation passed: 5/5 required fields, 10/10 optional fields
2025-06-23 14:32:35,111 - inserters.v3.roles.system_permissions_inserter - INFO - Incremented permission ID from PERM_ATTR_EMP_LEAVE to PERM1
2025-06-23 14:32:35,111 - inserters.v3.roles.system_permissions_inserter - INFO - Additional MongoDB fields preserved: {'original_created_at': '2025-06-23T07:15:42.617971', 'original_updated_at': '2025-06-23T07:15:42.617979', 'original_created_by': 'Tarun', 'original_updated_by': 'Tarun', 'original_permission_id': 'PERM_ATTR_EMP_LEAVE'}
2025-06-23 14:32:35,112 - inserters.v3.roles.system_permissions_inserter - ERROR - Error inserting system permission to workflow_temp: column "actions" is of type jsonb but expression is of type text[]
LINE 6: ...', 'attribute', 'Employee.totalLeaveEntitlement', ARRAY['rea...
                                                             ^
HINT:  You will need to rewrite or cast the expression.

2025-06-23 14:32:35,112 - inserters.v3.roles.system_permissions_inserter - INFO - Completed process_mongo_system_permissions_to_workflow_temp: 0 successful, 1 failed
2025-06-23 14:38:52,856 - inserters.v3.roles.system_permissions_inserter - INFO - Starting deploy_single_system_permission_to_workflow_temp for permission ID: PERM001
2025-06-23 14:38:52,930 - inserters.v3.roles.system_permissions_inserter - INFO - Starting process_mongo_system_permissions_to_workflow_temp
2025-06-23 14:38:52,933 - inserters.v3.roles.system_permissions_inserter - INFO - Found 1 system permissions with status 'draft' in MongoDB
2025-06-23 14:38:52,941 - inserters.v3.roles.system_permissions_inserter - INFO - Starting insert_system_permission_to_workflow_temp for permission: Employee totalLeaveEntitlement Attribute
2025-06-23 14:38:52,941 - inserters.v3.roles.system_permissions_inserter - INFO - Field validation passed: 5/5 required fields, 10/10 optional fields
2025-06-23 14:38:52,956 - inserters.v3.roles.system_permissions_inserter - INFO - Incremented permission ID from PERM_ATTR_EMP_LEAVE to PERM1
2025-06-23 14:38:52,956 - inserters.v3.roles.system_permissions_inserter - INFO - Additional MongoDB fields preserved: {'original_created_at': '2025-06-23T07:15:42.617971', 'original_updated_at': '2025-06-23T07:15:42.617979', 'original_created_by': 'Tarun', 'original_updated_by': 'Tarun', 'original_permission_id': 'PERM_ATTR_EMP_LEAVE'}
2025-06-23 14:38:52,957 - inserters.v3.roles.system_permissions_inserter - ERROR - Error inserting system permission to workflow_temp: column "actions" is of type jsonb but expression is of type text[]
LINE 6: ...', 'attribute', 'Employee.totalLeaveEntitlement', ARRAY['rea...
                                                             ^
HINT:  You will need to rewrite or cast the expression.

2025-06-23 14:38:52,958 - inserters.v3.roles.system_permissions_inserter - INFO - Completed process_mongo_system_permissions_to_workflow_temp: 0 successful, 1 failed
2025-06-23 14:38:54,465 - inserters.v3.roles.system_permissions_inserter - INFO - Starting process_mongo_system_permissions_to_workflow_temp
2025-06-23 14:38:54,467 - inserters.v3.roles.system_permissions_inserter - INFO - Found 1 system permissions with status 'draft' in MongoDB
2025-06-23 14:38:54,473 - inserters.v3.roles.system_permissions_inserter - INFO - Starting insert_system_permission_to_workflow_temp for permission: Employee totalLeaveEntitlement Attribute
2025-06-23 14:38:54,473 - inserters.v3.roles.system_permissions_inserter - INFO - Field validation passed: 5/5 required fields, 10/10 optional fields
2025-06-23 14:38:54,486 - inserters.v3.roles.system_permissions_inserter - INFO - Incremented permission ID from PERM_ATTR_EMP_LEAVE to PERM1
2025-06-23 14:38:54,486 - inserters.v3.roles.system_permissions_inserter - INFO - Additional MongoDB fields preserved: {'original_created_at': '2025-06-23T07:15:42.617971', 'original_updated_at': '2025-06-23T07:15:42.617979', 'original_created_by': 'Tarun', 'original_updated_by': 'Tarun', 'original_permission_id': 'PERM_ATTR_EMP_LEAVE'}
2025-06-23 14:38:54,487 - inserters.v3.roles.system_permissions_inserter - ERROR - Error inserting system permission to workflow_temp: column "actions" is of type jsonb but expression is of type text[]
LINE 6: ...', 'attribute', 'Employee.totalLeaveEntitlement', ARRAY['rea...
                                                             ^
HINT:  You will need to rewrite or cast the expression.

2025-06-23 14:38:54,487 - inserters.v3.roles.system_permissions_inserter - INFO - Completed process_mongo_system_permissions_to_workflow_temp: 0 successful, 1 failed
2025-06-24 04:45:02,384 - inserters.v3.roles.system_permissions_inserter - INFO - Starting deploy_single_system_permission_to_workflow_temp for permission ID: PERM001
2025-06-24 04:45:02,485 - inserters.v3.roles.system_permissions_inserter - INFO - Starting process_mongo_system_permissions_to_workflow_temp
2025-06-24 04:45:02,488 - inserters.v3.roles.system_permissions_inserter - INFO - Found 1 system permissions with status 'draft' in MongoDB
2025-06-24 04:45:02,495 - inserters.v3.roles.system_permissions_inserter - INFO - Starting insert_system_permission_to_workflow_temp for permission: Employee totalLeaveEntitlement Attribute
2025-06-24 04:45:02,496 - inserters.v3.roles.system_permissions_inserter - INFO - Field validation passed: 5/5 required fields, 10/10 optional fields
2025-06-24 04:45:02,511 - inserters.v3.roles.system_permissions_inserter - INFO - Incremented permission ID from PERM_ATTR_EMP_LEAVE to PERM1
2025-06-24 04:45:02,511 - inserters.v3.roles.system_permissions_inserter - INFO - Additional MongoDB fields preserved: {'original_created_at': '2025-06-23T07:15:42.617971', 'original_updated_at': '2025-06-23T07:15:42.617979', 'original_created_by': 'Tarun', 'original_updated_by': 'Tarun', 'original_permission_id': 'PERM_ATTR_EMP_LEAVE'}
2025-06-24 04:45:02,513 - inserters.v3.roles.system_permissions_inserter - ERROR - Error inserting system permission to workflow_temp: column "actions" is of type jsonb but expression is of type text[]
LINE 6: ...', 'attribute', 'Employee.totalLeaveEntitlement', ARRAY['rea...
                                                             ^
HINT:  You will need to rewrite or cast the expression.

2025-06-24 04:45:02,513 - inserters.v3.roles.system_permissions_inserter - INFO - Completed process_mongo_system_permissions_to_workflow_temp: 0 successful, 1 failed
2025-06-24 04:45:04,126 - inserters.v3.roles.system_permissions_inserter - INFO - Starting process_mongo_system_permissions_to_workflow_temp
2025-06-24 04:45:04,127 - inserters.v3.roles.system_permissions_inserter - INFO - Found 1 system permissions with status 'draft' in MongoDB
2025-06-24 04:45:04,134 - inserters.v3.roles.system_permissions_inserter - INFO - Starting insert_system_permission_to_workflow_temp for permission: Employee totalLeaveEntitlement Attribute
2025-06-24 04:45:04,134 - inserters.v3.roles.system_permissions_inserter - INFO - Field validation passed: 5/5 required fields, 10/10 optional fields
2025-06-24 04:45:04,149 - inserters.v3.roles.system_permissions_inserter - INFO - Incremented permission ID from PERM_ATTR_EMP_LEAVE to PERM1
2025-06-24 04:45:04,149 - inserters.v3.roles.system_permissions_inserter - INFO - Additional MongoDB fields preserved: {'original_created_at': '2025-06-23T07:15:42.617971', 'original_updated_at': '2025-06-23T07:15:42.617979', 'original_created_by': 'Tarun', 'original_updated_by': 'Tarun', 'original_permission_id': 'PERM_ATTR_EMP_LEAVE'}
2025-06-24 04:45:04,150 - inserters.v3.roles.system_permissions_inserter - ERROR - Error inserting system permission to workflow_temp: column "actions" is of type jsonb but expression is of type text[]
LINE 6: ...', 'attribute', 'Employee.totalLeaveEntitlement', ARRAY['rea...
                                                             ^
HINT:  You will need to rewrite or cast the expression.

2025-06-24 04:45:04,150 - inserters.v3.roles.system_permissions_inserter - INFO - Completed process_mongo_system_permissions_to_workflow_temp: 0 successful, 1 failed
2025-06-24 11:45:42,303 - inserters.v3.roles.system_permissions_inserter - INFO - Starting process_mongo_system_permissions_to_workflow_runtime
2025-06-24 11:45:42,308 - inserters.v3.roles.system_permissions_inserter - INFO - Found 0 deployed_to_temp + 1 draft = 1 total system permissions in MongoDB
2025-06-24 11:45:42,316 - inserters.v3.roles.system_permissions_inserter - INFO - Starting insert_system_permission_to_workflow_runtime for permission: Employee totalLeaveEntitlement Attribute
2025-06-24 11:45:42,316 - inserters.v3.roles.system_permissions_inserter - INFO - Field validation passed: 5/5 required fields, 10/10 optional fields
2025-06-24 11:45:42,333 - inserters.v3.roles.system_permissions_inserter - INFO - Incremented permission ID from PERM_ATTR_EMP_LEAVE to PERM1
2025-06-24 11:45:42,333 - inserters.v3.roles.system_permissions_inserter - INFO - Additional MongoDB fields preserved: {'original_created_at': '2025-06-23T07:15:42.617971', 'original_updated_at': '2025-06-23T07:15:42.617979', 'original_created_by': 'Tarun', 'original_updated_by': 'Tarun', 'original_permission_id': 'PERM_ATTR_EMP_LEAVE'}
2025-06-24 11:45:42,334 - inserters.v3.roles.system_permissions_inserter - ERROR - Error inserting system permission to workflow_runtime: column "actions" is of type jsonb but expression is of type text[]
LINE 6: ...', 'attribute', 'Employee.totalLeaveEntitlement', ARRAY['rea...
                                                             ^
HINT:  You will need to rewrite or cast the expression.

2025-06-24 11:45:42,334 - inserters.v3.roles.system_permissions_inserter - INFO - Completed process_mongo_system_permissions_to_workflow_runtime: 0 successful, 1 failed
2025-06-24 11:50:39,026 - inserters.v3.roles.system_permissions_inserter - INFO - Starting process_mongo_system_permissions_to_workflow_runtime
2025-06-24 11:50:39,031 - inserters.v3.roles.system_permissions_inserter - INFO - Found 0 deployed_to_temp + 1 draft = 1 total system permissions in MongoDB
2025-06-24 11:50:39,038 - inserters.v3.roles.system_permissions_inserter - INFO - Starting insert_system_permission_to_workflow_runtime for permission: Employee totalLeaveEntitlement Attribute
2025-06-24 11:50:39,039 - inserters.v3.roles.system_permissions_inserter - INFO - Field validation passed: 5/5 required fields, 10/10 optional fields
2025-06-24 11:50:39,055 - inserters.v3.roles.system_permissions_inserter - INFO - Incremented permission ID from PERM_ATTR_EMP_LEAVE to PERM1
2025-06-24 11:50:39,055 - inserters.v3.roles.system_permissions_inserter - INFO - Additional MongoDB fields preserved: {'original_created_at': '2025-06-23T07:15:42.617971', 'original_updated_at': '2025-06-23T07:15:42.617979', 'original_created_by': 'Tarun', 'original_updated_by': 'Tarun', 'original_permission_id': 'PERM_ATTR_EMP_LEAVE'}
2025-06-24 11:50:39,056 - inserters.v3.roles.system_permissions_inserter - ERROR - Error inserting system permission to workflow_runtime: column "actions" is of type jsonb but expression is of type text[]
LINE 6: ...', 'attribute', 'Employee.totalLeaveEntitlement', ARRAY['rea...
                                                             ^
HINT:  You will need to rewrite or cast the expression.

2025-06-24 11:50:39,057 - inserters.v3.roles.system_permissions_inserter - INFO - Completed process_mongo_system_permissions_to_workflow_runtime: 0 successful, 1 failed
2025-06-24 12:04:39,171 - inserters.v3.roles.system_permissions_inserter - INFO - Starting process_mongo_system_permissions_to_workflow_runtime
2025-06-24 12:04:39,176 - inserters.v3.roles.system_permissions_inserter - INFO - Found 0 deployed_to_temp + 1 draft = 1 total system permissions in MongoDB
2025-06-24 12:04:39,185 - inserters.v3.roles.system_permissions_inserter - INFO - Starting insert_system_permission_to_workflow_runtime for permission: Employee totalLeaveEntitlement Attribute
2025-06-24 12:04:39,185 - inserters.v3.roles.system_permissions_inserter - INFO - Field validation passed: 5/5 required fields, 10/10 optional fields
2025-06-24 12:04:39,211 - inserters.v3.roles.system_permissions_inserter - INFO - Incremented permission ID from PERM_ATTR_EMP_LEAVE to PERM1
2025-06-24 12:04:39,212 - inserters.v3.roles.system_permissions_inserter - INFO - Additional MongoDB fields preserved: {'original_created_at': '2025-06-23T07:15:42.617971', 'original_updated_at': '2025-06-23T07:15:42.617979', 'original_created_by': 'Tarun', 'original_updated_by': 'Tarun', 'original_permission_id': 'PERM_ATTR_EMP_LEAVE'}
2025-06-24 12:04:39,213 - inserters.v3.roles.system_permissions_inserter - INFO - Successfully inserted system permission to workflow_runtime with ID: 80
2025-06-24 12:04:39,216 - inserters.v3.roles.system_permissions_inserter - INFO - Updated MongoDB status to deployed_to_production for permission ID: PERM1
2025-06-24 12:04:39,217 - inserters.v3.roles.system_permissions_inserter - INFO - Completed process_mongo_system_permissions_to_workflow_runtime: 1 successful, 0 failed
