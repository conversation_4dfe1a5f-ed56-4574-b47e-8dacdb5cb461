Loan has loanId^PK, customerId^FK, loanAmount, interestRate, term, startDate, endDate, status (Active, Closed, Default, Restructured), paymentFrequency, totalPaymentsMade, remainingBalance[derived], loanType (Personal, Mortgage, Auto, Education, Business), collateralId^FK, originationFee, lateFeePercentage, earlyPaymentPenalty.

Relationships for Loan:
Loan has many-to-one relationship with Customer using Loan.customerId to Customer.customerId^PK
Loan has many-to-one relationship with Collateral using Loan.collateralId to Collateral.collateralId^PK
Loan has one-to-many relationship with Payment using Loan.loanId to Payment.loanId^FK

Defaults for Loan:
Loan.minInterestRate PROPERTY_NAME = 1.0
Loan.minTerm PROPERTY_NAME = 3
Loan.status DEFAULT_VALUE = "Active"
Loan.startDate DEFAULT_VALUE = CURRENT_DATE


Validations for Loan:
Loan.loanId must be unique
Loan.loanAmount must be greater than 0
Loan.interestRate must be greater than or equal to 1.0
Loan.term must be greater than or equal to 3
Loan.startDate must not be in the future
Loan.endDate must be after startDate

BusinessRule for Loan:

validate_loan_update_status_1
Inputs: Loan with status, loanId, paymentFrequency, term, interestRate, lateFeePercentage, earlyPaymentPenalty

Operation: conditional_logic({"condition": "AND", "rules": [{"field": "FUNCTION_TYPE", "operator": "equals", "value": "update"}, {"field": "Loan.status", "operator": "notEquals", "value": "Active"}]}, {"result": "error", "message": "Loan must be Active for any modifications"}, {"result": "success"}, Loan.validationResult)

Description: Validates that a Loan's status is Active before allowing any update operations on loan terms such as payment frequency, term length, interest rate, or fee structures.

Output: Loan with validationResult

Error: When Loan.status is not "Active" and an update is attempted, returns "Loan must be Active for any modifications"

Validation: PRE_UPDATE on Loan entity

calculate_remaining_balance_on_payment_1
Inputs: Loan with loanId, loanAmount; Payment with loanId, status, amount

Operation: conditional_logic({"condition": "AND", "rules": [{"field": "FUNCTION_TYPE", "operator": "equals", "value": "update"}, {"field": "ENTITY_NAME", "operator": "equals", "value": "Payment"}, {"field": "Payment.status", "operator": "changedTo", "value": "Completed"}]}, {"execute": true}, {"execute": false}, Payment.shouldCalculateBalance) | fetch_sum("payments", "amount", {"loanId": Loan.loanId, "status": "Completed"}, Payment.totalPaid) | subtract(Loan.loanAmount, Payment.totalPaid, Loan.remainingBalance) | update("loans", {"loanId": Loan.loanId}, {"remainingBalance": Loan.remainingBalance}, Loan.updateResult)

Description: Automatically recalculates and updates the loan's remaining balance whenever a payment is marked as completed.

Output: Loan with remainingBalance, updateResult

Error: When calculation fails, returns "Failed to update remaining balance"

Trigger: POST_UPDATE on Payment entity where status changed to "Completed"

increment_payments_made_1
Inputs: Loan with loanId, totalPaymentsMade*; Payment with loanId, status

Operation: conditional_logic({"condition": "AND", "rules": [{"field": "FUNCTION_TYPE", "operator": "equals", "value": "update"}, {"field": "ENTITY_NAME", "operator": "equals", "value": "Payment"}, {"field": "Payment.status", "operator": "changedTo", "value": "Completed"}]}, {"execute": true}, {"execute": false}, Payment.shouldIncrementPayments) | add(Loan.totalPaymentsMade, 1, Loan.newTotalPayments) | update("loans", {"loanId": Loan.loanId}, {"totalPaymentsMade": Loan.newTotalPayments}, Loan.updateResult)

Description: Automatically increments the count of total payments made on a loan whenever a payment is marked as completed.

Output: Loan with newTotalPayments, updateResult

Error: When update fails, returns "Failed to update payment count"

Trigger: POST_UPDATE on Payment entity where status changed to "Completed"

CalculatedField for Loan.remainingBalance: * Formula: Loan.loanAmount - SUM(Payment.amount WHERE Payment.loanId = Loan.loanId AND Payment.status = 'Completed') * Logic Layer: Database * Caching: Request * Dependencies: Loan.loanAmount, Payment.amount, Payment.status

CalculatedField for Loan.monthlyPayment: * Formula: (Loan.loanAmount * (Loan.interestRate/1200) * POW(1 + Loan.interestRate/1200, Loan.term)) / (POW(1 + Loan.interestRate/1200, Loan.term) - 1) * Logic Layer: Application * Caching: Session * Dependencies: Loan.loanAmount, Loan.interestRate, Loan.term

Loan has customerId^FK, collateralId^FK with Customer constrains Collateral.

Loan.collateralId must belong to selected Loan.customerId
Entity Additional Properties: Display Name: Loan Agreement Type: Core Entity Description: Represents a loan agreement between the financial institution and a customer

Attribute Additional Properties: Attribute name: loanId Key: Primary Display Name: Loan ID Data Type: Integer Type: Mandatory Format: "LN-######" Values: N/A Default: N/A Validation: Auto-increment Error Message: "Invalid Loan ID format" Description: Unique identifier for the loan

Attribute Additional Properties: Attribute name: customerId Key: Foreign Display Name: Customer ID Data Type: Integer Type: Mandatory Format: "CUS-######" Values: N/A Default: N/A Validation: Foreign Key Check Error Message: "Customer ID must reference an existing customer" Description: References the customer who took the loan

Attribute Additional Properties: Attribute name: loanAmount Key: Non-unique Display Name: Principal Loan Amount Data Type: Decimal Type: Mandatory Format: "$#,###.00" Values: 1000.00-10000000.00 Default: N/A Validation: Range Check Error Message: "Loan amount must be between $1,000 and $10,000,000" Description: The principal amount borrowed by the customer

Attribute Additional Properties: Attribute name: interestRate Key: Non-unique Display Name: Annual Interest Rate (%) Data Type: Decimal Type: Mandatory Format: "0.00%" Values: 1.00-30.00 Default: "5.99" Validation: Range Check Error Message: "Interest rate must be between 1.00% and 30.00%" Description: Annual interest rate applied to the loan

Attribute Additional Properties: Attribute name: term Key: Non-unique Display Name: Loan Term (Months) Data Type: Integer Type: Mandatory Format: "### months" Values: 3-480 Default: "60" Validation: Range Check Error Message: "Term must be between 3 and 480 months" Description: Duration of the loan in months

Attribute Additional Properties: Attribute name: startDate Key: Non-unique Display Name: Loan Start Date Data Type: Date Type: Mandatory Format: "YYYY-MM-DD" Values: N/A Default: "CURRENT_DATE" Validation: Date Check Error Message: "Start date cannot be in the future" Description: Date when the loan becomes active

Attribute Additional Properties: Attribute name: endDate Key: Non-unique Display Name: Loan End Date Data Type: Date Type: Mandatory Format: "YYYY-MM-DD" Values: N/A Default: N/A Validation: Date Check Error Message: "End date must be after start date" Description: Scheduled date for loan completion

Attribute Additional Properties: Attribute name: status Key: Non-unique Display Name: Loan Status Data Type: Enum Type: Mandatory Format: N/A Values: "Active", "Closed", "Default", "Restructured" Default: "Active" Validation: List Check Error Message: "Invalid loan status" Description: Current status of the loan

Attribute Additional Properties: Attribute name: paymentFrequency Key: Non-unique Display Name: Payment Frequency Data Type: Enum Type: Mandatory Format: N/A Values: "Monthly", "Biweekly", "Weekly", "Quarterly", "Annually" Default: "Monthly" Validation: List Check Error Message: "Invalid payment frequency" Description: How often payments are made on the loan

Attribute Additional Properties: Attribute name: totalPaymentsMade Key: Non-unique Display Name: Total Payments Made Data Type: Integer Type: Mandatory Format: "###" Values: 0-N Default: "0" Validation: Range Check Error Message: "Total payments cannot be negative" Description: Number of payments completed for this loan

Attribute Additional Properties: Attribute name: remainingBalance Key: Non-unique Display Name: Remaining Balance Data Type: Decimal Type: Calculated Format: "$#,###.00" Values: 0.00-N Default: N/A Validation: Calculation Error Message: N/A Description: Current outstanding balance on the loan

Attribute Additional Properties: Attribute name: loanType Key: Non-unique Display Name: Loan Type Data Type: Enum Type: Mandatory Format: N/A Values: "Personal", "Mortgage", "Auto", "Education", "Business" Default: "Personal" Validation: List Check Error Message: "Invalid loan type" Description: Category of loan based on purpose

Attribute Additional Properties: Attribute name: collateralId Key: Foreign Display Name: Collateral ID Data Type: Integer Type: Optional Format: "COL-######" Values: N/A Default: N/A Validation: Foreign Key Check Error Message: "Collateral ID must reference existing collateral" Description: References the asset used to secure the loan

Attribute Additional Properties: Attribute name: originationFee Key: Non-unique Display Name: Origination Fee Data Type: Decimal Type: Optional Format: "$#,###.00" Values: 0.00-N Default: "0.00" Validation: Range Check Error Message: "Origination fee cannot be negative" Description: Fee charged for processing a new loan application

Attribute Additional Properties: Attribute name: lateFeePercentage Key: Non-unique Display Name: Late Fee Percentage Data Type: Decimal Type: Optional Format: "0.00%" Values: 0.00-20.00 Default: "5.00" Validation: Range Check Error Message: "Late fee percentage must be between 0% and 20%" Description: Percentage charged on late payments

Attribute Additional Properties: Attribute name: earlyPaymentPenalty Key: Non-unique Display Name: Early Payment Penalty Data Type: Decimal Type: Optional Format: "0.00%" Values: 0.00-10.00 Default: "1.00" Validation: Range Check Error Message: "Early payment penalty must be between 0% and 10%" Description: Penalty charged for early loan payoff

Relationship: Loan to Customer

Relationship Properties: On Delete: Restrict (Prevent deletion of customer with active loans) On Update: Cascade (Update loan records when customer details change) Foreign Key Type: Non-Nullable

Synthetic: Loan has loanId = 1001, customerId = 5001, loanAmount = 250000, interestRate = 4.5, term = 360, startDate = "2024-01-10", endDate = "2054-01-10", status = "Active", paymentFrequency = "Monthly", totalPaymentsMade = 4, remainingBalance = 247856.32, loanType = "Mortgage", collateralId = 3001, originationFee = 2500, lateFeePercentage = 5.0, earlyPaymentPenalty = 1.0. Loan has loanId = 1002, customerId = 5002, loanAmount = 35000, interestRate = 6.9, term = 60, startDate = "2024-02-15", endDate = "2029-02-15", status = "Active", paymentFrequency = "Monthly", totalPaymentsMade = 3, remainingBalance = 33245.65, loanType = "Auto", collateralId = 3002, originationFee = 350, lateFeePercentage = 7.5, earlyPaymentPenalty = 2.0.

Confidential: Loan.interestRate, Loan.originationFee, Loan.earlyPaymentPenalty

Internal: Loan.remainingBalance, Loan.collateralId, Loan.lateFeePercentage
Public: Loan.loanType, Loan.term, Loan.status

Loading for Loan.Customer: Eager Loading

Loading for Loan.Collateral: Lazy Loading
Loading for Loan.Payments: Lazy Loading

Archive Strategy for Loan:

Trigger: Event-based
Criteria: When Loan.status changes to 'Closed' and remains so for 2 years
Retention: 10 years
Storage: Cold storage
Access Pattern: Read-only through Financial archive portal
Restoration: Manual process requiring Financial Director approval
Purge Rule for Loan:

Trigger: Time-based (annual evaluation)
Criteria: archivedDate < (CURRENT_DATE - (10 * 365)) AND Regulatory.retentionExpired = true
Approvals: ComplianceOfficer AND FinancialDirector
Audit: Full metadata retention with deletion certificate
Dependencies: Payment (cascade), LoanDocument (cascade)
History Tracking for Loan:

Tracked Attributes: Loan.status, Loan.interestRate, Loan.remainingBalance, Loan.term
Tracking Method: Audit table
Granularity: Change level
Retention: 10 years
Access Control: Financial Managers and Compliance Officers only
Workflow: LoanApproval for Loan

States: Applied, UnderReview, CreditChecked, Approved, Funded, Active
Transitions:
Applied → UnderReview [Loan_Officer, Branch_Manager]
UnderReview → CreditChecked [Credit_Analyst, Risk_Manager]
CreditChecked → Approved [Loan_Committee, Senior_Manager]
Approved → Funded [Finance_Officer, Treasury_Manager]
Funded → Active [Loan_Officer, Branch_Manager]
Actions: ReviewApplication, PerformCreditCheck, ApproveLoan, ReleaseFunds, ActivateLoan
BusinessRule Placement: * Local Objective: LoanManagement * Global Objective: LendingLifecycle * Chapter: Lending * Book: FinancialOperations * Tenant: BankHQ

Workflow LoanApproval Placement:

Global Objective: LendingLifecycle
Book: FinancialOperations
Chapter: Lending
Tenant: BankHQ
Entity Placement for Loan:

Tenant: BankHQ
Book: FinancialOperations
Chapter: Lending
Global Objectives: LendingLifecycle, RiskManagement
Local Objectives: LoanManagement, CollectionManagement