from flask import Flask, request, jsonify
from flask_cors import CORS
import yaml
import tempfile
import os
import sys
from typing import Dict, List, Any, Optional

app = Flask(__name__)
CORS(app)  # Enable CORS for all routes

# Define expected YAML structure - using the complete structure from your original code
EXPECTED_STRUCTURE = {
    # Tenant Configuration
    "tenant": {
        "required": True,
        "type": "dict",
        "fields": {
            "id": {"type": "str", "required": True},
            "name": {"type": "str", "required": True},
            "roles": {
                "type": "list",
                "required": True,
                "schema": {
                    "type": "dict",
                    "fields": {
                        "id": {"type": "str", "required": True},
                        "name": {"type": "str", "required": True},
                        "inherits_from": {"type": "str", "required": False, "nullable": True},
                        "access": {
                            "type": "dict",
                            "required": True,
                            "fields": {
                                "entities": {
                                    "type": "list",
                                    "required": True,
                                    "schema": {
                                        "type": "dict",
                                        "fields": {
                                            "entity_id": {"type": "str", "required": True},
                                            "permissions": {"type": "list", "required": True}
                                        }
                                    }
                                },
                                "objectives": {
                                    "type": "list",
                                    "required": True,
                                    "schema": {
                                        "type": "dict",
                                        "fields": {
                                            "objective_id": {"type": "str", "required": True},
                                            "permissions": {"type": "list", "required": True}
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    },

    # Permission Types
    "permission_types": {
        "required": True,
        "type": "list",
        "schema": {
            "type": "dict",
            "fields": {
                "id": {"type": "str", "required": True},
                "description": {"type": "str", "required": True},
                "capabilities": {"type": "list", "required": True}
            }
        }
    },

    # Entities
    "entities": {
        "required": True,
        "type": "list",
        "schema": {
            "type": "dict",
            "fields": {
                "id": {"type": "str", "required": True},
                "name": {"type": "str", "required": True},
                "version": {"type": "str", "required": True},
                "status": {"type": "str", "required": True},
                "type": {"type": "str", "required": True},
                "attributes_metadata": {
                    "type": "dict",
                    "required": True,
                    "fields": {
                        "attribute_prefix": {"type": "str", "required": True},
                        "attribute_map": {"type": "dict", "required": True},
                        "required_attributes": {"type": "list", "required": True}
                    }
                },
                "attributes": {
                    "type": "list",
                    "required": True,
                    "schema": {
                        "type": "dict",
                        "fields": {
                            "id": {"type": "str", "required": True},
                            "name": {"type": "str", "required": True},
                            "display_name": {"type": "str", "required": True},
                            "datatype": {"type": "str", "required": True},
                            "version": {"type": "str", "required": True},
                            "status": {"type": "str", "required": True},
                            "required": {"type": "bool", "required": True}
                        }
                    }
                },
                "relationships": {"type": "list", "required": False},
                "nested_entities": {"type": "list", "required": False}
            }
        }
    },

    # Global Objectives
    "global_objectives": {
        "required": False,
        "type": "list",
        "schema": {
            "type": "dict",
            "fields": {
                "id": {"type": "str", "required": True},
                "name": {"type": "str", "required": True},
                "version": {"type": "str", "required": True},
                "status": {"type": "str", "required": True},

                # Input Stack for Global Objective
                "input_stack": {
                    "type": "dict",
                    "required": True,
                    "fields": {
                        "description": {"type": "str", "required": True},
                        "inputs": {
                            "type": "list",
                            "required": True,
                            "schema": {
                                "type": "dict",
                                "fields": {
                                    "id": {"type": "str", "required": True},
                                    "slot_id": {"type": "str", "required": True},
                                    "contextual_id": {"type": "str", "required": True},
                                    "entity_reference": {"type": "str", "required": False},
                                    "attribute_reference": {"type": "str", "required": False},
                                    "source": {
                                        "type": "dict",
                                        "required": True,
                                        "fields": {
                                            "type": {"type": "str", "required": True},
                                            "description": {"type": "str", "required": True}
                                        }
                                    },
                                    "required": {"type": "bool", "required": True}
                                }
                            }
                        },
                        "system_functions": {
                            "type": "list",
                            "required": False,
                            "schema": {
                                "type": "dict",
                                "fields": {
                                    "function_id": {"type": "str", "required": True},
                                    "function_name": {"type": "str", "required": True},
                                    "function_type": {"type": "str", "required": True},
                                    "parameters": {"type": "dict", "required": True},
                                    "output_to": {"type": "str", "required": False}
                                }
                            }
                        }
                    }
                },

                # Output Stack for Global Objective
                "output_stack": {
                    "type": "dict",
                    "required": True,
                    "fields": {
                        "description": {"type": "str", "required": True},
                        "outputs": {
                            "type": "list",
                            "required": True,
                            "schema": {
                                "type": "dict",
                                "fields": {
                                    "id": {"type": "str", "required": True},
                                    "slot_id": {"type": "str", "required": True},
                                    "contextual_id": {"type": "str", "required": True},
                                    "output_entity": {"type": "str", "required": False},
                                    "output_attribute": {"type": "str", "required": False},
                                    "data_type": {"type": "str", "required": True},
                                    "triggers": {
                                        "type": "dict",
                                        "required": True,
                                        "fields": {
                                            "description": {"type": "str", "required": True},
                                            "items": {
                                                "type": "list",
                                                "required": True,
                                                "schema": {
                                                    "type": "dict",
                                                    "fields": {
                                                        "id": {"type": "str", "required": True},
                                                        "target_objective": {"type": "str", "required": True},
                                                        "target_input": {"type": "str", "required": True},
                                                        "mapping_type": {"type": "str", "required": True},
                                                        "condition": {"type": "dict", "required": False}
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        },
                        "system_functions": {
                            "type": "list",
                            "required": False,
                            "schema": {
                                "type": "dict",
                                "fields": {
                                    "function_id": {"type": "str", "required": True},
                                    "function_name": {"type": "str", "required": True},
                                    "function_type": {"type": "str", "required": True},
                                    "parameters": {"type": "dict", "required": True}
                                }
                            }
                        }
                    }
                },

                # Data Mapping Stack for Global Objective
                "data_mapping_stack": {
                    "type": "dict",
                    "required": True,
                    "fields": {
                        "description": {"type": "str", "required": True},
                        "mappings": {
                            "type": "list",
                            "required": True,
                            "schema": {
                                "type": "dict",
                                "fields": {
                                    "id": {"type": "str", "required": True},
                                    "source": {"type": "str", "required": True},
                                    "target": {"type": "str", "required": True},
                                    "mapping_type": {"type": "str", "required": True}
                                }
                            }
                        },
                        "rules": {
                            "type": "list",
                            "required": False,
                            "schema": {
                                "type": "dict",
                                "fields": {
                                    "id": {"type": "str", "required": True},
                                    "description": {"type": "str", "required": True},
                                    "rule_condition": {
                                        "type": "dict",
                                        "required": True,
                                        "fields": {
                                            "condition_type": {"type": "str", "required": True},
                                            "entity": {"type": "str", "required": True},
                                            "attribute": {"type": "str", "required": True},
                                            "operator": {"type": "str", "required": True},
                                            "value": {"type": "any", "required": True}
                                        }
                                    },
                                    "error_message": {"type": "str", "required": True}
                                }
                            }
                        }
                    }
                }
            }
        }
    },

    # Local Objectives
    "local_objectives": {
        "required": True,
        "type": "list",
        "schema": {
            "type": "dict",
            "fields": {
                "id": {"type": "str", "required": True},
                "contextual_id": {"type": "str", "required": True},
                "name": {"type": "str", "required": True},
                "function_type": {"type": "str", "required": True},
                "workflow_source": {"type": "str", "required": False},

                # Execution Pathway
                "execution_pathway": {
                    "type": "dict",
                    "required": True,
                    "fields": {
                        "type": {"type": "str", "required": True},
                        "next_lo": {"type": "str", "required": False},
                        "conditions": {"type": "list", "required": False}
                    }
                },

                # Agent Stack
                "agent_stack": {
                    "type": "dict",
                    "required": True,
                    "fields": {
                        "agents": {
                            "type": "list",
                            "required": True,
                            "schema": {
                                "type": "dict",
                                "fields": {
                                    "role": {"type": "str", "required": True},
                                    "rights": {"type": "list", "required": True}
                                }
                            }
                        }
                    }
                },

                # Input Stack
                "input_stack": {
                    "type": "dict",
                    "required": True,
                    "fields": {
                        "description": {"type": "str", "required": True},
                        "inputs": {
                            "type": "list",
                            "required": True,
                            "schema": {
                                "type": "dict",
                                "fields": {
                                    "id": {"type": "str", "required": True},
                                    "slot_id": {"type": "str", "required": True},
                                    "contextual_id": {"type": "str", "required": True},
                                    "source": {
                                        "type": "dict",
                                        "required": True,
                                        "fields": {
                                            "type": {"type": "str", "required": True},
                                            "description": {"type": "str", "required": True}
                                        }
                                    },
                                    "required": {"type": "bool", "required": True},
                                    "data_type": {"type": "str", "required": False},
                                    "ui_control": {"type": "str", "required": False},
                                    "nested_function": {"type": "dict", "required": False},
                                    "nested_functions": {"type": "list", "required": False},
                                    "validations": {"type": "list", "required": False},
                                    "allowed_values": {"type": "list", "required": False}
                                }
                            }
                        }
                    }
                },

                # Execution Rules (Required)
                "execution_rules": {
                    "type": "list",
                    "required": True,
                    "schema": {
                        "type": "dict",
                        "fields": {
                            "id": {"type": "str", "required": True},
                            "contextual_id": {"type": "str", "required": True},
                            "description": {"type": "str", "required": True},
                            "rule_condition": {
                                "type": "dict",
                                "required": True,
                                "fields": {
                                    "condition_type": {"type": "str", "required": True},
                                    "function_name": {"type": "str", "required": True},
                                    "parameters": {"type": "dict", "required": True}
                                }
                            },
                            "action": {
                                "type": "dict",
                                "required": True,
                                "fields": {
                                    "action_type": {"type": "str", "required": True},
                                    "function_name": {"type": "str", "required": True},
                                    "parameters": {"type": "dict", "required": True},
                                    "output_attribute": {"type": "str", "required": False},
                                    "output_variable": {"type": "str", "required": False}
                                }
                            }
                        }
                    }
                },

                # Success Message Template (Required)
                "success_message_template": {
                    "type": "dict",
                    "required": True,
                    "fields": {
                        "message_type": {"type": "str", "required": True},
                        "format_string": {"type": "str", "required": True},
                        "format_parameters": {
                            "type": "list",
                            "required": True,
                            "schema": {
                                "type": "dict",
                                "fields": {
                                    "entity": {"type": "str", "required": True},
                                    "attribute": {"type": "str", "required": True}
                                }
                            }
                        }
                    }
                },

                # Output Stack
                "output_stack": {
                    "type": "dict",
                    "required": True,
                    "fields": {
                        "description": {"type": "str", "required": True},
                        "outputs": {
                            "type": "list",
                            "required": True,
                            "schema": {
                                "type": "dict",
                                "fields": {
                                    "id": {"type": "str", "required": True},
                                    "slot_id": {"type": "str", "required": True},
                                    "contextual_id": {"type": "str", "required": True},
                                    "source": {
                                        "type": "dict",
                                        "required": True,
                                        "fields": {
                                            "type": {"type": "str", "required": True},
                                            "description": {"type": "str", "required": True},
                                            "value": {"type": "str", "required": False}
                                        }
                                    },
                                    "output_entity": {"type": "str", "required": False},
                                    "output_attribute": {"type": "str", "required": False},
                                    "required": {"type": "bool", "required": False},
                                    "data_type": {"type": "str", "required": False}
                                }
                            }
                        }
                    }
                },

                # UI Stack (Required)
                "ui_stack": {
                    "type": "dict",
                    "required": True,
                    "fields": {
                        "type": {"type": "str", "required": True},
                        "status": {"type": "str", "required": True},
                        "description": {"type": "str", "required": True},
                        "overall_control": {"type": "str", "required": True},
                        "form_title": {"type": "str", "required": True},
                        "submit_button_text": {"type": "str", "required": True},
                        "cancel_button_text": {"type": "str", "required": False},
                        "elements": {
                            "type": "list",
                            "required": True,
                            "schema": {
                                "type": "dict",
                                "fields": {
                                    "entity_attribute": {"type": "str", "required": True},
                                    "ui_control": {"type": "str", "required": True},
                                    "helper_text": {"type": "str", "required": False},
                                    "error_message": {"type": "str", "required": False}
                                }
                            }
                        }
                    }
                },

                # Data Mapping Stack (Optional)
                "data_mapping_stack": {
                    "type": "dict",
                    "required": False,
                    "fields": {
                        "description": {"type": "str", "required": True},
                        "mappings": {
                            "type": "list",
                            "required": True,
                            "schema": {
                                "type": "dict",
                                "fields": {
                                    "id": {"type": "str", "required": True},
                                    "source": {"type": "str", "required": True},
                                    "target": {"type": "str", "required": True},
                                    "mapping_type": {"type": "str", "required": True},
                                    "condition": {"type": "dict", "required": False}
                                }
                            }
                        },
                        "rules": {"type": "list", "required": False}
                    }
                }
            }
        }
    }
}


def check_type(value: Any, expected_type: str) -> bool:
    """Check if value matches expected type string."""
    if expected_type == "str":
        return isinstance(value, str)
    elif expected_type == "int":
        return isinstance(value, int)
    elif expected_type == "bool":
        return isinstance(value, bool)
    elif expected_type == "list":
        return isinstance(value, list)
    elif expected_type == "dict":
        return isinstance(value, dict)
    elif expected_type == "any":
        return True  # Any type is acceptable
    else:
        return False  # Unknown type


def validate_field(value: Any, field_spec: Dict, path: str) -> List[str]:
    """Validate a field against its specification."""
    errors = []

    # Check if required field is missing
    if field_spec.get("required", False) and value is None:
        errors.append(f"❌ {path} is required but missing")
        return errors

    # Skip validation if value is None and field is not required
    if value is None:
        return errors

    # Check type
    field_type = field_spec.get("type", "any")
    if not check_type(value, field_type):
        errors.append(f"❌ {path} should be a {field_type} but found {type(value).__name__}")
        return errors  # Skip further validation if type is incorrect

    # For lists, validate each item
    if field_type == "list" and "schema" in field_spec and value:
        item_schema = field_spec["schema"]
        for i, item in enumerate(value):
            item_path = f"{path}[{i}]"
            errors.extend(validate_value(item, item_schema, item_path))

    # For dicts, validate each field
    elif field_type == "dict" and "fields" in field_spec:
        for key, key_spec in field_spec["fields"].items():
            field_path = f"{path}.{key}"
            field_value = value.get(key)
            errors.extend(validate_field(field_value, key_spec, field_path))

    return errors


def validate_value(value: Any, spec: Dict, path: str) -> List[str]:
    """Validate a value against its specification."""
    errors = []

    # Check value type
    value_type = spec.get("type")
    if not value_type:
        errors.append(f"❌ Missing type specification for {path}")
        return errors

    if not check_type(value, value_type):
        errors.append(f"❌ {path} should be a {value_type} but found {type(value).__name__}")
        return errors  # Skip further validation if type is incorrect

    # For dictionaries, validate fields
    if value_type == "dict" and "fields" in spec:
        for key, field_spec in spec["fields"].items():
            field_path = f"{path}.{key}"
            field_value = value.get(key)
            errors.extend(validate_field(field_value, field_spec, field_path))

    # For lists, validate each item
    elif value_type == "list" and "schema" in spec and value:
        item_schema = spec["schema"]
        for i, item in enumerate(value):
            item_path = f"{path}[{i}]"
            errors.extend(validate_value(item, item_schema, item_path))

    return errors


def validate_yaml_content(yaml_content: str) -> dict:
    """Validates YAML content and returns a result dictionary."""
    try:
        data = yaml.safe_load(yaml_content)
        if data is None:
            return {
                "success": False,
                "errors": ["❌ Error: Empty or invalid YAML content"],
                "message": "YAML validation failed: Empty or invalid content"
            }
        
        all_errors = []

        # Validate against expected structure
        for key, spec in EXPECTED_STRUCTURE.items():
            if spec.get("required", False) and key not in data:
                all_errors.append(f"❌ Missing required top-level section: {key}")
                continue

            if key in data:
                value = data[key]
                errors = validate_value(value, spec, key)
                all_errors.extend(errors)

        # Special validation for execution_pathway.type
        if "local_objectives" in data and isinstance(data["local_objectives"], list):
            for i, lo in enumerate(data["local_objectives"]):
                lo_path = f"local_objectives[{i}]"

                if "execution_pathway" in lo and "type" in lo["execution_pathway"]:
                    pathway_type = lo["execution_pathway"]["type"]

                    # Check Alternative type requirements
                    if pathway_type == "Alternative":
                        if "conditions" not in lo["execution_pathway"] or not lo["execution_pathway"]["conditions"]:
                            all_errors.append(
                                f"❌ {lo_path}.execution_pathway with type 'Alternative' must have a non-empty 'conditions' list"
                            )

                    # Check Sequential type requirements
                    elif pathway_type == "sequential":
                        if "next_lo" not in lo["execution_pathway"]:
                            all_errors.append(
                                f"❌ {lo_path}.execution_pathway with type 'sequential' should have 'next_lo' defined"
                            )

        if all_errors:
            return {
                "success": False,
                "errors": all_errors,
                "message": f"YAML validation failed with {len(all_errors)} errors"
            }
        else:
            return {
                "success": True,
                "errors": [],
                "message": "✅ YAML structure is valid!"
            }
            
    except yaml.YAMLError as e:
        return {
            "success": False,
            "errors": [f"❌ Error parsing YAML: {str(e)}"],
            "message": "YAML syntax error"
        }
    except Exception as e:
        return {
            "success": False,
            "errors": [f"❌ Unexpected error: {str(e)}"],
            "message": "Validation error"
        }


@app.route('/api/validate', methods=['POST'])
def validate():
    """API endpoint to validate YAML content."""
    try:
        data = request.json
        if not data or 'yaml_content' not in data:
            return jsonify({
                "success": False,
                "errors": ["Missing 'yaml_content' in request"],
                "message": "Invalid request"
            }), 400
        
        yaml_content = data['yaml_content']
        result = validate_yaml_content(yaml_content)
        return jsonify(result)
        
    except Exception as e:
        return jsonify({
            "success": False,
            "errors": [f"Server error: {str(e)}"],
            "message": "Server error occurred"
        }), 500


def generate_sample_fixes(yaml_content):
    """Generates sample fixes for common validation errors."""
    sample_fixes = []
    
    try:
        data = yaml.safe_load(yaml_content)
        if not data:
            return ["❌ Cannot generate fixes for empty YAML"]
        
        # Check for missing sections in local objectives
        if "local_objectives" in data and isinstance(data["local_objectives"], list):
            for i, lo in enumerate(data["local_objectives"]):
                lo_id = lo.get("id", f"#{i}")
                missing_sections = []

                # Check for required sections
                if "execution_rules" not in lo:
                    missing_sections.append("execution_rules")
                if "success_message_template" not in lo:
                    missing_sections.append("success_message_template")
                if "ui_stack" not in lo:
                    missing_sections.append("ui_stack")

                if missing_sections:
                    fix = f"For Local Objective {lo_id}, add these missing sections: {', '.join(missing_sections)}"
                    sample_fixes.append(fix)
        
        # Check for required top-level sections
        required_sections = ["tenant", "permission_types", "entities", "local_objectives"]
        missing_top_level = [section for section in required_sections if section not in data]
        
        if missing_top_level:
            fix = f"Add these missing top-level sections: {', '.join(missing_top_level)}"
            sample_fixes.append(fix)
            
        return sample_fixes
        
    except Exception as e:
        return [f"❌ Error generating fixes: {str(e)}"]


@app.route('/api/suggest-fixes', methods=['POST'])
def suggest_fixes():
    """API endpoint to suggest fixes for invalid YAML."""
    try:
        data = request.json
        if not data or 'yaml_content' not in data:
            return jsonify({
                "success": False,
                "suggestions": ["Missing 'yaml_content' in request"],
                "message": "Invalid request"
            }), 400
        
        yaml_content = data['yaml_content']
        fixes = generate_sample_fixes(yaml_content)
        
        return jsonify({
            "success": True,
            "suggestions": fixes,
            "message": f"Generated {len(fixes)} suggestion(s)"
        })
        
    except Exception as e:
        return jsonify({
            "success": False,
            "suggestions": [f"Error: {str(e)}"],
            "message": "Error generating suggestions"
        }), 500


if __name__ == '__main__':
    print("Starting YAML Validator API on http://localhost:5000")
    print("API endpoints:")
    print("  POST /api/validate - Validate YAML content")
    print("  POST /api/suggest-fixes - Get suggestions for fixing YAML")
    app.run(debug=True)
