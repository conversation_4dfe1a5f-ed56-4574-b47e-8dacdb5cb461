# Local Objective (LO) Prescriptive Template

This template defines the structure for Local Objective (LO) definitions in the system. The LO definitions follow a specific format that can be parsed and deployed to the database.

## LO Definition Format

```
## [LO_Name]

id: "[lo_id]"
contextual_id: "[go_id].[lo_id]"
name: "[Human-readable LO name]"
version: "[version_number]"
status: "[Active/Draft/Deprecated]"
workflow_source: "[origin/intermediate/terminal]"
function_type: "[Create/Update/Read/Delete]"

*[Role] has [rights] rights*

*Inputs: [Entity1] with attribute1*, attribute2* [depends on: attribute1], attribute3* (value1, value2), attribute4 [optional], instructions [info]*
* System loads [Entity1].instructions [info] with constant "[constant_key]".
* System generates [Entity].[attribute] using [function_name] with [parameters].
* System calculates [Entity].[attribute] using [function_name] with [parameters].
* System defaults [Entity].[attribute] to "[value]".
* System populates [Entity].[attribute2] [depends on: attribute1] using [function_name] from [ReferenceEntity].
* System displays instructions [info] with key "[instruction_key]".
* [Entity].[attribute] depends on [Entity].[other_attribute] using [function_name].

*Outputs: [Entity1] with attribute1, attribute2; [Entity2] with attribute1*
* System returns [Entity].[attribute] for downstream operations.
* System captures [Entity].[attribute] for audit purposes.
* System transforms [Entity].[attribute] for display using [function_name].

*DB Stack:*
* [Entity].[attribute] (datatype) is mandatory and unique. Error message: "[error_message]"
* [Entity].[attribute] (datatype) is optional. Error message: "[error_message]"
* [Entity].[attribute] must be one of [allowed_values]. Error message: "[error_message]"
* [Entity].[attribute] must follow [pattern/rule]. Error message: "[error_message]"
* [Entity].[attribute] must reference [ReferenceEntity].[reference_attribute]. Error message: "[error_message]"

*UI Stack:*
* [Entity].[attribute] displays as [ui_control] with [properties].
* [Entity].[attribute] is [editable/read-only] depending on [condition].
* [Entity].[attribute] visibility depends on [condition].
* [Entity].[attribute] [depends on: other_attribute] populates from [ReferenceEntity] filtered by [filter_condition].
* System provides contextual help for [Entity].[attribute] explaining "[help_text]".

*Mapping Stack:*
* [this_lo].output.[attribute] maps to [next_lo].input.[attribute] using [mapping_type].
* [Entity1].[attribute] transforms to [Entity2].[attribute] using [function].
* System preserves [Entity].[attribute] across multiple LOs for workflow continuity.

Execution pathway:
* When [Entity].[attribute] [operator] [value], route to [specific_LO_name].
* When [Entity].[attribute] [operator] [value], route to [specific_LO_name].

Synthetic values:
* [Entity].[attribute]: "[sample_value1]", "[sample_value2]", "[sample_value3]"
* [Entity].[attribute]: [number_value1], [number_value2], [number_value3]
```

## Field Definitions

### LO Metadata
- **LO_Name**: The name of the LO (e.g., ApplyForLeave, ManagerApproval)
- **lo_id**: Unique identifier for the LO in the format "loXXX" (e.g., lo001, lo002)
- **go_id**: Identifier of the parent Global Objective (GO)
- **contextual_id**: Combined identifier of GO and LO (e.g., go001.lo001)
- **name**: Human-readable name of the LO
- **version_number**: The version of the LO definition (e.g., "1.0", "2.1")
- **status**: The current status of the LO (Active, Draft, Deprecated)
- **workflow_source**: Position in the workflow (origin, intermediate, terminal)
- **function_type**: Primary database operation (Create, Update, Read, Delete)

### Role Definition
- **Role**: The role that can perform this LO (e.g., Employee, Manager)
- **rights**: The rights the role has (execution, read, update)

### Inputs
- **Entity**: The entity that provides input data
- **attribute**: The attribute of the entity
- **\***: Marker for mandatory attributes
- **[depends on: attribute]**: Marker for dependent attributes
- **[optional]**: Marker for optional attributes
- **[info]**: Marker for information-only attributes
- **enum values**: Possible values for enumeration attributes, listed in parentheses

### System Actions
- **function_name**: The name of a system function (e.g., generate_id, calculate_days)
- **parameters**: Parameters for the function
- **constant_key**: Key for loading constant values
- **value**: Default value for an attribute

### Outputs
- **Entity**: The entity that receives output data
- **attribute**: The attribute of the entity

### DB Stack
- **datatype**: The data type of an attribute (e.g., string, date, decimal)
- **constraint**: A validation constraint (e.g., mandatory, unique, pattern)
- **allowed_values**: Possible values for an attribute
- **pattern/rule**: A validation pattern or rule
- **error_message**: Error message to display when validation fails

### UI Stack
- **ui_control**: The UI control to use for an attribute (e.g., oj-input-text, oj-combobox-one)
- **properties**: Properties of the UI control
- **condition**: Condition for editable/read-only or visibility
- **filter_condition**: Condition for filtering reference data
- **help_text**: Help text to display for an attribute

### Mapping Stack
- **this_lo**: The current LO
- **next_lo**: The next LO in the workflow
- **mapping_type**: Type of mapping (direct, transform, conditional)
- **function**: Function to use for transformation

### Execution Pathway
- **operator**: Comparison operator (=, !=, >, <, >=, <=)
- **value**: Value to compare against
- **specific_LO_name**: Name of the next LO in the workflow

### Synthetic Values
- **sample_value**: Sample values for testing

## Validation Rules

1. **LO ID Format**: Must be in the format "loXXX" where XXX is a number
2. **GO ID Format**: Must be in the format "goXXX" where XXX is a number
3. **Entity References**: All entities referenced must exist in the system
4. **Attribute References**: All attributes referenced must exist in the referenced entity
5. **Role References**: All roles referenced must exist in the system
6. **LO References**: All LOs referenced in execution pathways must exist in the system
7. **Function References**: All functions referenced must exist in the system
8. **UI Control References**: All UI controls referenced must exist in the system
9. **Mandatory Inputs**: All mandatory inputs must have corresponding DB constraints
10. **Execution Pathways**: All possible conditions must be covered in execution pathways

## Example LO Definition

```
## ApplyForLeave

id: "lo001"
contextual_id: "go001.lo001"
name: "Apply for Leave"
version: "1.0"
status: "Active"
workflow_source: "origin"
function_type: "Create"

*Employee has execution rights*

*Inputs: LeaveApplication with leaveID*, employeeID*, startDate*, endDate*, numDays*, reason*, leaveType* (Sick Leave, Annual Leave, Parental Leave, Bereavement), leaveSubType [depends on: leaveType], status* (Pending, Approved, Rejected), instructions [info]*
* System generates LeaveApplication.leaveID using generate_id with prefix "LV".
* System calculates LeaveApplication.numDays using subtract_days with startDate and endDate.
* System defaults LeaveApplication.status to "Pending".
* System populates LeaveApplication.leaveSubType [depends on: leaveType] using fetch_filtered_records from LeaveSubType where LeaveSubType.leaveType = LeaveApplication.leaveType and LeaveSubType.active = true.
* System loads LeaveApplication.instructions [info] with constant "leave_application_instructions".

*Outputs: LeaveApplication with leaveID, employeeID, startDate, endDate, numDays, reason, leaveType, leaveSubType, status*
* System returns LeaveApplication.leaveID for reference in notifications.
* System captures LeaveApplication.submissionDate using current_timestamp for audit trails.
* System transforms LeaveApplication.leaveType for display using format_enum_value.

*DB Stack:*
* LeaveApplication.leaveID (string) is mandatory and unique. Error message: "Leave ID is required and must be unique"
* LeaveApplication.employeeID (string) is mandatory and must exist in Employee table. Error message: "Employee ID is required"
* LeaveApplication.startDate (date) is mandatory and must be a valid date. Error message: "Start date is required"
* LeaveApplication.endDate (date) is mandatory and must be after startDate. Error message: "End date must be after start date"
* LeaveApplication.reason (string) is mandatory with minimum 10 characters. Error message: "Please provide a detailed reason for your leave request"
* LeaveApplication.leaveType (enum) must be one of "Sick Leave", "Annual Leave", "Parental Leave", "Bereavement". Error message: "Please select a valid leave type"

*UI Stack:*
* LeaveApplication.leaveID displays as oj-input-text with readonly property.
* LeaveApplication.startDate displays as oj-input-date with min-value set to current date.
* LeaveApplication.endDate displays as oj-input-date with min-value bound to startDate.
* LeaveApplication.numDays displays as oj-input-number with readonly property.
* LeaveApplication.leaveType displays as oj-combobox-one with source from entity enumeration.
* LeaveApplication.leaveSubType [depends on: leaveType] displays as oj-combobox-one with source from LeaveSubType filtered by LeaveSubType.leaveType = LeaveApplication.leaveType and LeaveSubType.active = true.
* LeaveApplication.instructions [info] displays as oj-text with formatting-class "policy-highlight".
* System provides contextual help for LeaveApplication.leaveType explaining "Select the type of leave you are requesting. Different leave types may have different approval requirements."

*Mapping Stack:*
* ApplyForLeave.output.leaveID maps to ManagerApproval.input.leaveID using direct mapping.
* ApplyForLeave.output.employeeID maps to ManagerApproval.input.employeeID using direct mapping.
* ApplyForLeave.output.startDate maps to ManagerApproval.input.startDate using direct mapping.
* ApplyForLeave.output.endDate maps to ManagerApproval.input.endDate using direct mapping.
* ApplyForLeave.output.numDays maps to ManagerApproval.input.numDays using direct mapping.
* ApplyForLeave.output.reason maps to ManagerApproval.input.reason using direct mapping.
* ApplyForLeave.output.leaveType maps to ManagerApproval.input.leaveType using direct mapping.
* ApplyForLeave.output.leaveSubType maps to ManagerApproval.input.leaveSubType using direct mapping.

Execution pathway:
* When LeaveApplication.numDays > 3, system flags LeaveApplication.requiresHRApproval to true, route to HRManagerApproval.
* When LeaveApplication.numDays <= 3, route to ManagerApproval.
* When LeaveApplication.leaveType = "Sick Leave" and LeaveApplication.numDays > 3, system flags LeaveApplication.requiresMedicalCertificate to true, route to MedicalCertificateUpload.

Synthetic values:
* LeaveApplication.leaveID: "LV-2023-0001", "LV-2023-0002", "LV-2023-0003"
* LeaveApplication.employeeID: "EMP001", "EMP002", "EMP003"
* LeaveApplication.startDate: "2023-06-15", "2023-07-01", "2023-08-10"
* LeaveApplication.endDate: "2023-06-16", "2023-07-05", "2023-08-17"
* LeaveApplication.numDays: 2, 5, 8
* LeaveApplication.reason: "Personal matters", "Family emergency", "Medical procedure"
* LeaveApplication.leaveType: "Annual Leave", "Sick Leave", "Parental Leave"
* LeaveApplication.leaveSubType: "Standard Annual Leave", "Short-term Illness", "Maternity Leave"
* LeaveApplication.status: "Pending"
* LeaveApplication.submissionDate: "2023-06-01T09:15:30", "2023-06-15T14:22:45", "2023-07-05T10:05:12"
```

## Common System Functions

### Data Generation Functions
- **generate_id**: Generates a unique ID with a prefix
- **current_timestamp**: Gets the current date and time
- **current_user**: Gets the current user
- **current_date**: Gets the current date

### Calculation Functions
- **subtract_days**: Calculates the difference between two dates
- **add_days**: Adds days to a date
- **calculate_amount**: Calculates an amount based on parameters
- **calculate_tax**: Calculates tax based on parameters
- **calculate_total**: Calculates a total based on parameters

### Transformation Functions
- **to_uppercase**: Converts text to uppercase
- **to_lowercase**: Converts text to lowercase
- **format_date**: Formats a date according to a pattern
- **format_number**: Formats a number according to a pattern
- **format_enum_value**: Formats an enumeration value for display

### Data Retrieval Functions
- **fetch_filtered_records**: Retrieves records based on a filter
- **fetch_by_id**: Retrieves a record by ID
- **fetch_related**: Retrieves related records
- **fetch_lookup**: Retrieves lookup values
- **fetch_user_profile**: Retrieves the current user's profile

### Validation Functions
- **validate_reference**: Validates a reference to another entity
- **validate_pattern**: Validates a pattern
- **validate_range**: Validates a range
- **validate_uniqueness**: Validates uniqueness
- **validate_business_rule**: Validates a business rule

## Common UI Controls

### Text Controls
- **oj-input-text**: Single-line text input
- **oj-text-area**: Multi-line text input
- **oj-password-input**: Password input
- **oj-text**: Display-only text

### Number Controls
- **oj-input-number**: Number input
- **oj-slider**: Slider for selecting a number
- **oj-progress-bar**: Progress bar

### Date Controls
- **oj-input-date**: Date input
- **oj-input-time**: Time input
- **oj-input-date-time**: Date and time input
- **oj-date-picker**: Date picker

### Selection Controls
- **oj-combobox-one**: Single-selection dropdown
- **oj-combobox-many**: Multi-selection dropdown
- **oj-select-single**: Single-selection list
- **oj-select-multiple**: Multi-selection list
- **oj-checkboxset**: Set of checkboxes
- **oj-radioset**: Set of radio buttons
- **oj-switch**: Toggle switch

### Container Controls
- **oj-form-layout**: Form layout
- **oj-accordion**: Accordion
- **oj-tab-bar**: Tab bar
- **oj-collapsible**: Collapsible section
- **oj-dialog**: Dialog
- **oj-popup**: Popup

### Button Controls
- **oj-button**: Button
- **oj-menu-button**: Menu button
- **oj-toolbar**: Toolbar

### Table Controls
- **oj-table**: Table
- **oj-list-view**: List view
- **oj-tree-view**: Tree view
- **oj-data-grid**: Data grid

## Parsing Guidelines

When parsing LO definitions:

1. Extract the LO name and metadata
2. Parse role definitions
3. Parse inputs and system actions
4. Parse outputs
5. Parse DB constraints
6. Parse UI controls
7. Parse mappings
8. Parse execution pathways
9. Parse synthetic values
10. Validate all references against the system database
