# Workflow APIs Authentication Documentation

This document provides information about the authentication requirements for the Workflow System APIs.

## Overview

The Workflow System uses JWT (JSON Web Tokens) for authentication. All workflow APIs require authentication, and the token must be included in the `Authorization` header of the request.

## Authentication Requirements

The following workflow APIs require authentication:

- **GET /api/v1/global-objectives/**
  - Description: Get all global objectives for a specific tenant
  - Authentication: Required
  - Example: `GET /api/v1/global-objectives/?tenant_id=t001`

- **POST /api/v1/workflows/instances**
  - Description: Create a new workflow instance
  - Authentication: Required
  - Example: `POST /api/v1/workflows/instances`

- **GET /api/v1/workflows/instances/{instance_id}**
  - Description: Get details of a workflow instance
  - Authentication: Required
  - Example: `GET /api/v1/workflows/instances/1340c4c4-9ac6-495e-8de6-938dd6a4d7b2`

- **POST /api/v1/workflows/instances/{instance_id}/start**
  - Description: Start a workflow instance
  - Authentication: Required
  - Example: `POST /api/v1/workflows/instances/1340c4c4-9ac6-495e-8de6-938dd6a4d7b2/start`

- **GET /api/v1/workflows/instances/{instance_id}/inputs**
  - Description: Get input fields for the current local objective in a workflow instance
  - Authentication: Required
  - Example: `GET /api/v1/workflows/instances/1340c4c4-9ac6-495e-8de6-938dd6a4d7b2/inputs`

- **POST /api/v1/workflows/instances/{instance_id}/execute**
  - Description: Execute the current local objective in a workflow instance
  - Authentication: Required
  - Example: `POST /api/v1/workflows/instances/1340c4c4-9ac6-495e-8de6-938dd6a4d7b2/execute`

- **POST /api/v1/workflows/instances/{instance_id}/pause**
  - Description: Pause a workflow instance
  - Authentication: Required
  - Example: `POST /api/v1/workflows/instances/1340c4c4-9ac6-495e-8de6-938dd6a4d7b2/pause`

- **POST /api/v1/workflows/instances/{instance_id}/resume**
  - Description: Resume a paused workflow instance
  - Authentication: Required
  - Example: `POST /api/v1/workflows/instances/1340c4c4-9ac6-495e-8de6-938dd6a4d7b2/resume`

- **POST /api/v1/workflows/instances/{instance_id}/cancel**
  - Description: Cancel a workflow instance
  - Authentication: Required
  - Example: `POST /api/v1/workflows/instances/1340c4c4-9ac6-495e-8de6-938dd6a4d7b2/cancel`

## Authentication Flow

To authenticate with the Workflow System APIs, follow these steps:

1. Register a user (if not already registered)
2. Login to get access and refresh tokens
3. Include the access token in the `Authorization` header of your requests

### 1. Register a User

```bash
curl -X POST "http://localhost:8000/api/v1/auth/auth/register" \
  -H "Content-Type: application/json" \
  -d '{
    "username": "testuser",
    "email": "<EMAIL>",
    "password": "password123",
    "first_name": "Test",
    "last_name": "User"
  }'
```

### 2. Login to Get Access Token

```bash
curl -X POST "http://localhost:8000/api/v1/auth/auth/token" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "username=testuser&password=password123"
```

### 3. Use Access Token in Requests

```bash
curl -X GET "http://localhost:8000/api/v1/global-objectives/?tenant_id=t001" \
  -H "Authorization: Bearer {access_token}"
```

## Error Handling

If a request is made to an authenticated endpoint without a valid token, the API will return a 401 Unauthorized error:

```json
{
  "detail": "Not authenticated"
}
```

## Testing Authentication

You can test the authentication flow and workflow APIs with authentication using the provided test scripts:

- `scripts/test_auth_flow.py`: Tests the complete authentication flow
- `scripts/test_workflow_apis_with_auth.py`: Tests workflow APIs with authentication

To run the tests:

```bash
python scripts/test_auth_flow.py
python scripts/test_workflow_apis_with_auth.py
```

## Known Issues

- There is a database transaction issue with the "Fetch Workflow Inputs" endpoint that needs to be fixed.
