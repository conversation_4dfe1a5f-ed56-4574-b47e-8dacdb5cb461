#!/bin/bash

# Create a backup directory
mkdir -p backup_dev_files

# Move log files to backup
echo "Moving log files to backup..."
find . -name "*.log" -type f -exec mv {} backup_dev_files/ \;

# Move test output files to backup
echo "Moving test output files to backup..."
find . -name "*.txt" -type f -exec mv {} backup_dev_files/ \;

# Move test scripts to backup
echo "Moving test scripts to backup..."
find . -name "test_*.py" -type f -exec mv {} backup_dev_files/ \;

# Move check scripts to backup
echo "Moving check scripts to backup..."
find . -name "check_*.py" -type f -exec mv {} backup_dev_files/ \;

# Move query scripts to backup
echo "Moving query scripts to backup..."
find . -name "query_*.py" -type f -exec mv {} backup_dev_files/ \;

# Move list scripts to backup
echo "Moving list scripts to backup..."
find . -name "list_*.py" -type f -exec mv {} backup_dev_files/ \;

# Move fix scripts to backup
echo "Moving fix scripts to backup..."
find . -name "fix_*.py" -type f -exec mv {} backup_dev_files/ \;

# Move add scripts to backup
echo "Moving add scripts to backup..."
find . -name "add_*.py" -type f -exec mv {} backup_dev_files/ \;

# Move apply scripts to backup
echo "Moving apply scripts to backup..."
find . -name "apply_*.py" -type f -exec mv {} backup_dev_files/ \;

# Move recreate scripts to backup
echo "Moving recreate scripts to backup..."
find . -name "recreate_*.py" -type f -exec mv {} backup_dev_files/ \;

# Move run scripts to backup
echo "Moving run scripts to backup..."
find . -name "run_*.py" -type f -exec mv {} backup_dev_files/ \;

# Move SQL files to backup
echo "Moving SQL files to backup..."
find . -name "*.sql" -type f -exec mv {} backup_dev_files/ \;

# Move specific files to backup
echo "Moving specific development files to backup..."
specific_files=(
  "truncate_tables.py"
  "verify_fixes.py"
  "insert_test_data.py"
  "implement_missing_features.py"
  "deploy_to_temp_schema.py"
  "generate_migration_script.py"
  "sync_temp_schema.py"
  "schema_analyzer.py"
  "remove_redundant_columns.py"
)

for file in "${specific_files[@]}"; do
  if [ -f "$file" ]; then
    mv "$file" backup_dev_files/
  fi
done

echo "Cleanup complete. All development files have been moved to the backup_dev_files directory."
echo "To restore files, use: mv backup_dev_files/* ."
echo "To permanently delete the backup, use: rm -rf backup_dev_files"
