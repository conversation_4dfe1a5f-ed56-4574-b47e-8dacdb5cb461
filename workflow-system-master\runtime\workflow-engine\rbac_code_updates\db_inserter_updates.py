
# Add to db_inserter.py

def insert_user_entity(conn, user_entity):
    '''Insert a User entity into the database.'''
    cursor = conn.cursor()
    
    # Extract user attributes
    user_id = user_entity.get("id")
    attributes = user_entity.get("attributes", [])
    
    # Find required attributes
    user_id_attr = None
    username_attr = None
    email_attr = None
    status_attr = None
    
    for attr in attributes:
        if attr.get("name") == "user_id":
            user_id_attr = attr
        elif attr.get("name") == "username":
            username_attr = attr
        elif attr.get("name") == "email":
            email_attr = attr
        elif attr.get("name") == "status":
            status_attr = attr
    
    # Insert into users table
    if user_id_attr and username_attr and email_attr and status_attr:
        sql = '''
        INSERT INTO users (user_id, username, email, status)
        VALUES (%s, %s, %s, %s)
        '''
        cursor.execute(sql, (
            user_id_attr.get("default_value", ""),
            username_attr.get("default_value", ""),
            email_attr.get("default_value", ""),
            status_attr.get("default_value", "active")
        ))
    
    # Insert into entities table
    sql = '''
    INSERT INTO entities (id, name, type, version, status, entity_class)
    VALUES (%s, %s, %s, %s, %s, %s)
    '''
    cursor.execute(sql, (
        user_id,
        user_entity.get("name"),
        user_entity.get("type"),
        user_entity.get("version"),
        user_entity.get("status"),
        "organizational"
    ))
    
    conn.commit()

def insert_role_entity(conn, role_entity):
    '''Insert a Role entity into the database.'''
    cursor = conn.cursor()
    
    # Extract role attributes
    role_id = role_entity.get("id")
    attributes = role_entity.get("attributes", [])
    
    # Find required attributes
    role_id_attr = None
    name_attr = None
    
    for attr in attributes:
        if attr.get("name") == "role_id":
            role_id_attr = attr
        elif attr.get("name") == "name":
            name_attr = attr
    
    # Insert into roles table
    if role_id_attr and name_attr:
        sql = '''
        INSERT INTO roles (role_id, name, description)
        VALUES (%s, %s, %s)
        '''
        cursor.execute(sql, (
            role_id_attr.get("default_value", ""),
            name_attr.get("default_value", ""),
            ""  # Default empty description
        ))
    
    # Insert into entities table
    sql = '''
    INSERT INTO entities (id, name, type, version, status, entity_class)
    VALUES (%s, %s, %s, %s, %s, %s)
    '''
    cursor.execute(sql, (
        role_id,
        role_entity.get("name"),
        role_entity.get("type"),
        role_entity.get("version"),
        role_entity.get("status"),
        "organizational"
    ))
    
    conn.commit()

def insert_user_role_relationship(conn, user_email, role_id):
    '''Insert a relationship between a User and a Role.'''
    cursor = conn.cursor()
    
    # Find user_id by email
    sql = "SELECT user_id FROM users WHERE email = %s"
    cursor.execute(sql, (user_email,))
    result = cursor.fetchone()
    
    if not result:
        print(f"Warning: User with email {user_email} not found")
        return
    
    user_id = result[0]
    
    # Insert into user_roles table
    sql = '''
    INSERT INTO user_roles (user_id, role_id)
    VALUES (%s, %s)
    '''
    cursor.execute(sql, (user_id, role_id))
    
    # Insert into entity_relationships table
    sql = '''
    INSERT INTO entity_relationships (source_entity, source_id, target_entity, target_id, relationship_type)
    VALUES (%s, %s, %s, %s, %s)
    '''
    cursor.execute(sql, (
        "User",
        user_id,
        "Role",
        role_id,
        "has_role"
    ))
    
    conn.commit()
