import anthropic
import os
from app.core.config import Settings
from sqlalchemy.orm import Session
from typing import Optional, Dict, Any

class ClaudeService:
    def __init__(self, api_key: Optional[str] = None):
        # Get API key from settings or environment
        settings = Settings()
        self.api_key = api_key or os.environ.get("ANTHROPIC_API_KEY") or settings.anthropic_api_key
        self.client = anthropic.Anthropic(api_key=self.api_key)
        self.model = "claude-3-7-sonnet-20250219"

    async def query_claude(
        self, 
        user_message: str, 
        system_prompt: Optional[str] = None,
        workflow_context: Optional[Dict[str, Any]] = None,
        max_tokens: int = 2000
    ) -> str:
        """
        Send a query to <PERSON> with optional context from your workflow system.
        """
        messages = []

        if system_prompt:
            messages.append({"role": "system", "content": system_prompt})

        if workflow_context:
            context_str = self._format_workflow_context(workflow_context)
            messages.append({"role": "user", "content": f"Here is the workflow context information:\n{context_str}"})

        messages.append({"role": "user", "content": user_message})

        try:
            response = self.client.messages.create(
                model=self.model,
                max_tokens=max_tokens,
                messages=messages
            )
            return response.content[0].text
        except Exception as e:
            print(f"Error querying Claude API: {e}")
            return f"Error: Unable to get response from Claude API. {str(e)}"

    def _format_workflow_context(self, context: Dict[str, Any]) -> str:
        """
        Format workflow context into a string Claude can understand.
        """
        formatted = "WORKFLOW CONTEXT:\n"

        if "workflow_id" in context:
            formatted += f"Workflow ID: {context['workflow_id']}\n"

        if "current_step" in context:
            formatted += f"Current Step: {context['current_step']}\n"

        if "variables" in context and isinstance(context["variables"], dict):
            formatted += "Variables:\n"
            for k, v in context["variables"].items():
                formatted += f"  - {k}: {v}\n"

        if "execution_history" in context and isinstance(context["execution_history"], list):
            formatted += "Execution History:\n"
            for entry in context["execution_history"]:
                formatted += f"  - {entry}\n"

        return formatted

    async def enhance_workflow_step(self, db: Session, step_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Enhance a workflow step with Claude's assistance.
        """
        workflow_id = step_data.get("workflow_id")
        step_id = step_data.get("step_id")

        context = self._get_workflow_context(db, workflow_id, step_id)

        system_prompt = """
        You are an AI assistant integrated with a workflow engine system.
        Your task is to analyze the current workflow step and provide suggestions or enhancements.
        Focus on improving efficiency, error handling, and clarity of the workflow step.
        Provide your response in a structured format that can be easily parsed by the workflow engine.
        """

        user_message = f"""
        Please analyze and enhance this workflow step:

        Step ID: {step_id}
        Step Type: {step_data.get('type', 'Unknown')}
        Step Name: {step_data.get('name', 'Unnamed Step')}

        Configuration:
        {step_data.get('configuration', {})}

        Please provide suggestions to improve this step.
        """

        claude_response = await self.query_claude(
            user_message=user_message,
            system_prompt=system_prompt,
            workflow_context=context
        )

        step_data["ai_suggestions"] = claude_response
        return step_data

    def _get_workflow_context(self, db: Session, workflow_id: str, step_id: str) -> Dict[str, Any]:
        """
        Get relevant context for a specific workflow step from the database.
        """
        # Placeholder — extend with actual queries as needed
        context = {
            "workflow_id": workflow_id,
            "current_step": step_id,
            "variables": {},
            "execution_history": []
        }

        return context
