{"timestamp": "2025-06-23T14:08:00.480923", "endpoint": "fetch/entity-relationships", "input": {}, "output": {"success": true, "postgres_relationships": [], "mongo_drafts": [{"_id": "68551396458daab7c4ae1272", "relationship_id": 1, "source_entity_id": "E7", "target_entity_id": "E15", "relationship_type": "many-to-one", "source_attribute_id": "E7.At2", "target_attribute_id": "E15.At1", "on_delete": "restrict", "on_update": "cascade", "foreign_key_type": "Non-Nullable", "description": "Each leave application belongs to one employee", "version": 1, "status": "draft", "created_at": "2025-06-20T07:53:58.261487", "updated_at": "2025-06-20T07:53:58.261492", "created_by": "<PERSON><PERSON>", "updated_by": "<PERSON><PERSON>"}], "total_postgres": 0, "total_drafts": 1, "operation": "fetch"}, "status": "success"}