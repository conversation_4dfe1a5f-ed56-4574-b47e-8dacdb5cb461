{"timestamp": "2025-06-23T11:43:03.181039", "endpoint": "fetch/entities/E13", "input": {}, "output": {"success": true, "entity_id": "E13", "postgres_record": {"entity_id": "E13", "name": "FurnitureOrder", "display_name": "Furniture Order", "version": 1, "status": "Active", "type": "transactional", "description": "Represents a furniture purchase order", "created_at": "2025-06-22T10:29:01.017241", "updated_at": "2025-06-22T11:40:40.898347", "created_by": "system", "updated_by": "system", "table_name": "e13_furnitureorder", "tenant_id": "t001", "tenant_name": "Acme Corporation", "business_domain": "Sales", "category": "Order Management", "tags": null, "archival_strategy": "archive_after_completion", "icon": "shopping-cart", "colour_theme": "blue", "natural_language": "Primary entity for furniture purchase orders"}, "mongo_draft": {"_id": "685442ebf7fe510cb8237bf5", "entity_id": "E13", "name": "LeaveApplication", "display_name": "Leave Application", "tenant_id": "T1001", "tenant_name": "Alpha Corp", "business_domain": "Human Resources", "category": "Leave Management", "tags": ["leave", "application", "request", "approval"], "archival_strategy": "archive_only", "icon": "file-text", "colour_theme": "blue", "version": 1, "status": "draft", "type": "transaction", "description": "Represents an employee's request for time off from work", "table_name": "e13_LeaveApplication", "natural_language": "Tenant: Alpha Corp\n\nLeaveApplication has leaveId^PK, employeeId^FK, startDate, endDate, numDays[derived], reason, leaveTypeName, leaveSubTypeName, status, requiresDocumentation, documentationProvided, submissionDate, approvalDate, approvedBy^FK, comments, isRetroactive[derived], insufficientBalance[derived], lowTeamAvailability[derived], allowedNumberOfDays.\n\nEntity: LeaveApplication\n- Entity Name: LeaveApplication\n- Display Name: Leave Application\n- Type: transaction\n- Description: Represents an employee's request for time off from work\n- Business Domain: Human Resources\n- Category: Leave Management\n- Tags: leave, application, request, approval\n- Archival Strategy: archive_only\n- Icon: file-text\n- Colour Theme: blue", "created_at": "2025-06-19T17:03:39.677413", "updated_at": "2025-06-19T17:03:39.677423", "created_by": "<PERSON><PERSON>", "updated_by": "<PERSON><PERSON>", "entity_status": "new", "changes_detected": [], "icon_type": "text", "icon_content": ""}, "found_in_postgres": true, "found_in_mongo": true, "operation": "fetch"}, "status": "success"}