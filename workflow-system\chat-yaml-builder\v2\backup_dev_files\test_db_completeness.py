"""
Test Database Completeness for YAML Builder v2

This script tests the completeness of the database schema and data after migration.
It checks if all required tables and columns are present and if data is properly populated,
especially for GO data mapping, LO data mapping, input stack, and output stacks.
"""

import os
import sys
import logging
import argparse
from typing import Dict, List, Tuple, Any, Optional

# Add parent directory to path to import common modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from common.db_connection import get_db_connection
from db_utils import execute_query

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('test_db_completeness.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger('test_db_completeness')

def check_table_exists(schema_name: str, table_name: str) -> bool:
    """
    Check if a table exists in the specified schema.
    
    Args:
        schema_name: The schema name to check
        table_name: The table name to check
        
    Returns:
        Boolean indicating if the table exists
    """
    success, query_messages, result = execute_query(
        """
        SELECT EXISTS (
            SELECT FROM information_schema.tables 
            WHERE table_schema = %s
            AND table_name = %s
        )
        """,
        (schema_name, table_name)
    )
    
    if success and result and result[0][0]:
        logger.info(f"✅ Table {schema_name}.{table_name} exists")
        return True
    else:
        logger.error(f"❌ Table {schema_name}.{table_name} does not exist")
        return False

def check_column_exists(schema_name: str, table_name: str, column_name: str) -> bool:
    """
    Check if a column exists in the specified table.
    
    Args:
        schema_name: The schema name to check
        table_name: The table name to check
        column_name: The column name to check
        
    Returns:
        Boolean indicating if the column exists
    """
    success, query_messages, result = execute_query(
        """
        SELECT EXISTS (
            SELECT FROM information_schema.columns 
            WHERE table_schema = %s
            AND table_name = %s
            AND column_name = %s
        )
        """,
        (schema_name, table_name, column_name)
    )
    
    if success and result and result[0][0]:
        logger.info(f"✅ Column {schema_name}.{table_name}.{column_name} exists")
        return True
    else:
        logger.error(f"❌ Column {schema_name}.{table_name}.{column_name} does not exist")
        return False

def check_table_has_data(schema_name: str, table_name: str) -> bool:
    """
    Check if a table has data.
    
    Args:
        schema_name: The schema name to check
        table_name: The table name to check
        
    Returns:
        Boolean indicating if the table has data
    """
    success, query_messages, result = execute_query(
        f"SELECT COUNT(*) FROM {schema_name}.{table_name}",
        schema_name=schema_name
    )
    
    if success and result:
        count = result[0][0]
        if count > 0:
            logger.info(f"✅ Table {schema_name}.{table_name} has {count} rows")
            return True
        else:
            logger.warning(f"⚠️ Table {schema_name}.{table_name} has no data")
            return False
    else:
        logger.error(f"❌ Failed to check if table {schema_name}.{table_name} has data")
        return False

def check_go_data_mapping(schema_name: str) -> bool:
    """
    Check if GO data mapping is complete.
    
    Args:
        schema_name: The schema name to check
        
    Returns:
        Boolean indicating if GO data mapping is complete
    """
    logger.info("Checking GO data mapping...")
    
    # Check if go_lo_mapping table exists and has data
    if not check_table_exists(schema_name, "go_lo_mapping"):
        return False
    
    # Check if go_lo_mapping has data
    has_data = check_table_has_data(schema_name, "go_lo_mapping")
    
    # Check if global_objectives table has process_mining_schema and performance_metadata columns
    has_process_mining_schema = check_column_exists(schema_name, "global_objectives", "process_mining_schema")
    has_performance_metadata = check_column_exists(schema_name, "global_objectives", "performance_metadata")
    
    return has_data and has_process_mining_schema and has_performance_metadata

def check_lo_data_mapping(schema_name: str) -> bool:
    """
    Check if LO data mapping is complete.
    
    Args:
        schema_name: The schema name to check
        
    Returns:
        Boolean indicating if LO data mapping is complete
    """
    logger.info("Checking LO data mapping...")
    
    # Check if lo_data_mapping_stack table exists and has data
    if not check_table_exists(schema_name, "lo_data_mapping_stack"):
        return False
    
    # Check if lo_data_mappings table exists and has data
    if not check_table_exists(schema_name, "lo_data_mappings"):
        return False
    
    # Check if lo_data_mapping_stack has data
    has_stack_data = check_table_has_data(schema_name, "lo_data_mapping_stack")
    
    # Check if lo_data_mappings has data
    has_mappings_data = check_table_has_data(schema_name, "lo_data_mappings")
    
    # Check if local_objectives table has ui_stack and mapping_stack columns
    has_ui_stack = check_column_exists(schema_name, "local_objectives", "ui_stack")
    has_mapping_stack = check_column_exists(schema_name, "local_objectives", "mapping_stack")
    
    return has_stack_data and has_mappings_data and has_ui_stack and has_mapping_stack

def check_input_stack(schema_name: str) -> bool:
    """
    Check if input stack is complete.
    
    Args:
        schema_name: The schema name to check
        
    Returns:
        Boolean indicating if input stack is complete
    """
    logger.info("Checking input stack...")
    
    # Check if lo_input_stack table exists and has data
    if not check_table_exists(schema_name, "lo_input_stack"):
        return False
    
    # Check if lo_input_items table exists and has data
    if not check_table_exists(schema_name, "lo_input_items"):
        return False
    
    # Check if lo_input_validations table exists and has data
    if not check_table_exists(schema_name, "lo_input_validations"):
        return False
    
    # Check if lo_nested_functions table exists and has data
    if not check_table_exists(schema_name, "lo_nested_functions"):
        return False
    
    # Check if dropdown_data_sources table exists and has data
    if not check_table_exists(schema_name, "dropdown_data_sources"):
        return False
    
    # Check if input_dependencies table exists and has data
    if not check_table_exists(schema_name, "input_dependencies"):
        return False
    
    # Check if lo_input_stack has data
    has_stack_data = check_table_has_data(schema_name, "lo_input_stack")
    
    # Check if lo_input_items has data
    has_items_data = check_table_has_data(schema_name, "lo_input_items")
    
    # Check if lo_input_items has ui_control, help_text, and dependency_info columns
    has_ui_control = check_column_exists(schema_name, "lo_input_items", "ui_control")
    has_help_text = check_column_exists(schema_name, "lo_input_items", "help_text")
    has_dependency_info = check_column_exists(schema_name, "lo_input_items", "dependency_info")
    
    return has_stack_data and has_items_data and has_ui_control and has_help_text and has_dependency_info

def check_output_stack(schema_name: str) -> bool:
    """
    Check if output stack is complete.
    
    Args:
        schema_name: The schema name to check
        
    Returns:
        Boolean indicating if output stack is complete
    """
    logger.info("Checking output stack...")
    
    # Check if lo_output_stack table exists and has data
    if not check_table_exists(schema_name, "lo_output_stack"):
        return False
    
    # Check if lo_output_items table exists and has data
    if not check_table_exists(schema_name, "lo_output_items"):
        return False
    
    # Check if lo_output_stack has data
    has_stack_data = check_table_has_data(schema_name, "lo_output_stack")
    
    # Check if lo_output_items has data
    has_items_data = check_table_has_data(schema_name, "lo_output_items")
    
    return has_stack_data and has_items_data

def check_entity_business_rules(schema_name: str) -> bool:
    """
    Check if entity business rules are complete.
    
    Args:
        schema_name: The schema name to check
        
    Returns:
        Boolean indicating if entity business rules are complete
    """
    logger.info("Checking entity business rules...")
    
    # Check if entity_business_rules table exists
    if not check_table_exists(schema_name, "entity_business_rules"):
        return False
    
    # Check if entity_business_rules has data
    has_data = check_table_has_data(schema_name, "entity_business_rules")
    
    # Check if entities table has metadata and lifecycle_management columns
    has_metadata = check_column_exists(schema_name, "entities", "metadata")
    has_lifecycle_management = check_column_exists(schema_name, "entities", "lifecycle_management")
    
    # Check if entity_attributes table has calculated_field and calculation_formula columns
    has_calculated_field = check_column_exists(schema_name, "entity_attributes", "calculated_field")
    has_calculation_formula = check_column_exists(schema_name, "entity_attributes", "calculation_formula")
    
    return has_metadata and has_lifecycle_management and has_calculated_field and has_calculation_formula

def check_role_inheritance(schema_name: str) -> bool:
    """
    Check if role inheritance is complete.
    
    Args:
        schema_name: The schema name to check
        
    Returns:
        Boolean indicating if role inheritance is complete
    """
    logger.info("Checking role inheritance...")
    
    # Check if role_inheritance table exists
    if not check_table_exists(schema_name, "role_inheritance"):
        return False
    
    # Check if role_permissions table has permission_name column
    has_permission_name = check_column_exists(schema_name, "role_permissions", "permission_name")
    
    return has_permission_name

def check_execution_pathways(schema_name: str) -> bool:
    """
    Check if execution pathways are complete.
    
    Args:
        schema_name: The schema name to check
        
    Returns:
        Boolean indicating if execution pathways are complete
    """
    logger.info("Checking execution pathways...")
    
    # Check if execution_pathways table exists and has data
    if not check_table_exists(schema_name, "execution_pathways"):
        return False
    
    # Check if execution_pathway_conditions table exists and has data
    if not check_table_exists(schema_name, "execution_pathway_conditions"):
        return False
    
    # Check if execution_pathways has data
    has_pathways_data = check_table_has_data(schema_name, "execution_pathways")
    
    # Check if execution_pathway_conditions has data
    has_conditions_data = check_table_has_data(schema_name, "execution_pathway_conditions")
    
    return has_pathways_data and has_conditions_data

def check_agent_stack(schema_name: str) -> bool:
    """
    Check if agent stack is complete.
    
    Args:
        schema_name: The schema name to check
        
    Returns:
        Boolean indicating if agent stack is complete
    """
    logger.info("Checking agent stack...")
    
    # Check if agent_stack table exists and has data
    if not check_table_exists(schema_name, "agent_stack"):
        return False
    
    # Check if agent_rights table exists and has data
    if not check_table_exists(schema_name, "agent_rights"):
        return False
    
    # Check if agent_stack has data
    has_stack_data = check_table_has_data(schema_name, "agent_stack")
    
    # Check if agent_rights has data
    has_rights_data = check_table_has_data(schema_name, "agent_rights")
    
    return has_stack_data and has_rights_data

def check_nested_functions(schema_name: str) -> bool:
    """
    Check if nested functions are complete.
    
    Args:
        schema_name: The schema name to check
        
    Returns:
        Boolean indicating if nested functions are complete
    """
    logger.info("Checking nested functions...")
    
    # Check if lo_nested_functions table exists and has data
    if not check_table_exists(schema_name, "lo_nested_functions"):
        return False
    
    # Check if lo_nested_functions has data
    has_data = check_table_has_data(schema_name, "lo_nested_functions")
    
    # Check if lo_nested_functions has function_name, function_type, parameters, and output_to columns
    has_function_name = check_column_exists(schema_name, "lo_nested_functions", "function_name")
    has_function_type = check_column_exists(schema_name, "lo_nested_functions", "function_type")
    has_parameters = check_column_exists(schema_name, "lo_nested_functions", "parameters")
    has_output_to = check_column_exists(schema_name, "lo_nested_functions", "output_to")
    
    return has_data and has_function_name and has_function_type and has_parameters and has_output_to

def check_db_completeness(schema_name: str) -> bool:
    """
    Check if the database schema and data are complete.
    
    Args:
        schema_name: The schema name to check
        
    Returns:
        Boolean indicating if the database is complete
    """
    logger.info(f"Checking database completeness for schema {schema_name}...")
    
    # Check if schema exists
    success, query_messages, result = execute_query(
        """
        SELECT EXISTS (
            SELECT FROM information_schema.schemata
            WHERE schema_name = %s
        )
        """,
        (schema_name,)
    )
    
    if not (success and result and result[0][0]):
        logger.error(f"❌ Schema {schema_name} does not exist")
        return False
    
    # Check if all required tables exist
    required_tables = [
        "entities", "entity_attributes", "entity_relationships", "entity_business_rules",
        "global_objectives", "go_lo_mapping", "go_performance_metrics",
        "local_objectives", "lo_input_stack", "lo_input_items", "lo_output_stack", "lo_output_items",
        "lo_data_mapping_stack", "lo_data_mappings", "lo_nested_functions",
        "execution_pathways", "execution_pathway_conditions",
        "agent_stack", "agent_rights",
        "roles", "role_permissions", "role_inheritance"
    ]
    
    for table in required_tables:
        if not check_table_exists(schema_name, table):
            logger.error(f"❌ Required table {schema_name}.{table} does not exist")
            return False
    
    # Check if all components are complete
    go_data_mapping_complete = check_go_data_mapping(schema_name)
    lo_data_mapping_complete = check_lo_data_mapping(schema_name)
    input_stack_complete = check_input_stack(schema_name)
    output_stack_complete = check_output_stack(schema_name)
    entity_business_rules_complete = check_entity_business_rules(schema_name)
    role_inheritance_complete = check_role_inheritance(schema_name)
    execution_pathways_complete = check_execution_pathways(schema_name)
    agent_stack_complete = check_agent_stack(schema_name)
    nested_functions_complete = check_nested_functions(schema_name)
    
    # Check if all components are complete
    all_complete = (
        go_data_mapping_complete and
        lo_data_mapping_complete and
        input_stack_complete and
        output_stack_complete and
        entity_business_rules_complete and
        role_inheritance_complete and
        execution_pathways_complete and
        agent_stack_complete and
        nested_functions_complete
    )
    
    if all_complete:
        logger.info(f"✅ Database schema {schema_name} is complete")
    else:
        logger.error(f"❌ Database schema {schema_name} is incomplete")
        
        # Log which components are incomplete
        if not go_data_mapping_complete:
            logger.error("❌ GO data mapping is incomplete")
        if not lo_data_mapping_complete:
            logger.error("❌ LO data mapping is incomplete")
        if not input_stack_complete:
            logger.error("❌ Input stack is incomplete")
        if not output_stack_complete:
            logger.error("❌ Output stack is incomplete")
        if not entity_business_rules_complete:
            logger.error("❌ Entity business rules are incomplete")
        if not role_inheritance_complete:
            logger.error("❌ Role inheritance is incomplete")
        if not execution_pathways_complete:
            logger.error("❌ Execution pathways are incomplete")
        if not agent_stack_complete:
            logger.error("❌ Agent stack is incomplete")
        if not nested_functions_complete:
            logger.error("❌ Nested functions are incomplete")
    
    return all_complete

def main():
    """
    Main function to run the tests.
    """
    parser = argparse.ArgumentParser(description='Test database completeness')
    parser.add_argument('--schema', default='workflow_temp', help='Schema name to check')
    
    args = parser.parse_args()
    
    # Check database completeness
    is_complete = check_db_completeness(args.schema)
    
    if is_complete:
        logger.info(f"✅ Database schema {args.schema} is complete")
        return 0
    else:
        logger.error(f"❌ Database schema {args.schema} is incomplete")
        return 1

if __name__ == "__main__":
    sys.exit(main())
