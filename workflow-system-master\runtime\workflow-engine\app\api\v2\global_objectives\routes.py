"""
Global Objectives Routes for v2 API

This module contains the FastAPI routes for global objectives endpoints with RBAC checks.
"""

from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session
from typing import Optional, List, Dict, Any

from app.db.session import get_db
from ..auth.middleware import get_security_context, require_auth, SecurityContext
from .models import (
    GlobalObjectiveResponse, GlobalObjectiveListResponse,
    ErrorResponse, SuccessResponse
)
from .service import GlobalObjectivesService


# Create router for global objectives endpoints
router = APIRouter(
    prefix="/global_objectives",
    tags=["global-objectives-v2"],
    responses={
        401: {"model": ErrorResponse, "description": "Unauthorized"},
        403: {"model": ErrorResponse, "description": "Forbidden"},
        404: {"model": ErrorResponse, "description": "Not Found"},
        500: {"model": ErrorResponse, "description": "Internal Server Error"}
    }
)


@router.get(
    "/",
    response_model=GlobalObjectiveListResponse,
    summary="Get all global objectives",
    description="Retrieve all global objectives for the authenticated user's tenant with RBAC checks",
    responses={
        200: {
            "description": "Global objectives retrieved successfully",
            "model": GlobalObjectiveListResponse
        },
        401: {
            "description": "Authentication required",
            "model": ErrorResponse,
            "content": {
                "application/json": {
                    "example": {
                        "error": "AUTHENTICATION_REQUIRED",
                        "message": "Authentication required to access this resource",
                        "details": {}
                    }
                }
            }
        },
        403: {
            "description": "Access denied",
            "model": ErrorResponse,
            "content": {
                "application/json": {
                    "example": {
                        "error": "ACCESS_DENIED",
                        "message": "User does not have permission to access global objectives",
                        "details": {}
                    }
                }
            }
        }
    }
)
async def get_all_global_objectives(
    tenant_id: str = Query(..., description="Tenant ID to filter objectives"),
    limit: Optional[int] = Query(None, description="Limit number of results", ge=1, le=1000),
    offset: Optional[int] = Query(None, description="Offset for pagination", ge=0),
    db: Session = Depends(get_db),
    security_context: SecurityContext = Depends(require_auth)
) -> GlobalObjectiveListResponse:
    """
    Get all global objectives for the authenticated user's tenant.
    
    This endpoint retrieves all global objectives that the user has access to
    based on their tenant membership and role permissions.
    
    Args:
        tenant_id: Tenant ID to filter objectives
        limit: Optional limit for pagination
        offset: Optional offset for pagination
        db: Database session dependency
        security_context: Security context from authentication
        
    Returns:
        GlobalObjectiveListResponse: List of global objectives with related data
        
    Raises:
        HTTPException: 
            - 401 if authentication fails
            - 403 if user lacks permissions
            - 500 if internal error occurs
    """
    try:
        service = GlobalObjectivesService(db)
        
        result = service.get_all_global_objectives(
            user_id=security_context.user_id,
            tenant_id=tenant_id,
            limit=limit,
            offset=offset
        )
        
        if result is None:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail={
                    "error": "ACCESS_DENIED",
                    "message": "User does not have permission to access global objectives",
                    "details": {
                        "user_id": security_context.user_id,
                        "tenant_id": tenant_id
                    }
                }
            )
        
        return result
        
    except HTTPException:
        raise
    except Exception as e:
        import logging
        logger = logging.getLogger(__name__)
        logger.error(f"Unexpected error getting global objectives: {str(e)}")
        
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail={
                "error": "INTERNAL_ERROR",
                "message": "An unexpected error occurred while retrieving global objectives",
                "details": {}
            }
        )


@router.get(
    "/{go_id}",
    response_model=GlobalObjectiveResponse,
    summary="Get global objective by ID",
    description="Retrieve a specific global objective by ID with RBAC checks",
    responses={
        200: {
            "description": "Global objective found",
            "model": GlobalObjectiveResponse
        },
        404: {
            "description": "Global objective not found",
            "model": ErrorResponse,
            "content": {
                "application/json": {
                    "example": {
                        "error": "NOT_FOUND",
                        "message": "Global objective not found or access denied",
                        "details": {
                            "go_id": "GO001"
                        }
                    }
                }
            }
        }
    }
)
async def get_global_objective_by_id(
    go_id: str,
    tenant_id: str = Query(..., description="Tenant ID"),
    db: Session = Depends(get_db),
    security_context: SecurityContext = Depends(require_auth)
) -> GlobalObjectiveResponse:
    """
    Get a specific global objective by ID.
    
    Args:
        go_id: Global objective ID
        tenant_id: Tenant ID
        db: Database session dependency
        security_context: Security context from authentication
        
    Returns:
        GlobalObjectiveResponse: Global objective with related data
        
    Raises:
        HTTPException: 404 if objective not found or access denied
    """
    try:
        service = GlobalObjectivesService(db)
        
        result = service.get_global_objective_by_id(
            user_id=security_context.user_id,
            tenant_id=tenant_id,
            go_id=go_id
        )
        
        if result is None:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail={
                    "error": "NOT_FOUND",
                    "message": "Global objective not found or access denied",
                    "details": {
                        "go_id": go_id,
                        "tenant_id": tenant_id
                    }
                }
            )
        
        return result
        
    except HTTPException:
        raise
    except Exception as e:
        import logging
        logger = logging.getLogger(__name__)
        logger.error(f"Error retrieving global objective {go_id}: {str(e)}")
        
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail={
                "error": "INTERNAL_ERROR",
                "message": "An error occurred while retrieving the global objective",
                "details": {}
            }
        )


@router.get(
    "/books/all",
    response_model=List[Dict[str, Any]],
    summary="Get all books",
    description="Retrieve all unique books that the user has access to with RBAC checks",
    responses={
        200: {
            "description": "Books retrieved successfully",
            "content": {
                "application/json": {
                    "example": [
                        {
                            "book_id": "BOOK001",
                            "book_name": "Sample Book",
                            "objective_count": 5
                        }
                    ]
                }
            }
        }
    }
)
async def get_all_books(
    tenant_id: str = Query(..., description="Tenant ID"),
    db: Session = Depends(get_db),
    security_context: SecurityContext = Depends(require_auth)
) -> List[Dict[str, Any]]:
    """
    Get all unique books that the user has access to.
    
    Args:
        tenant_id: Tenant ID
        db: Database session dependency
        security_context: Security context from authentication
        
    Returns:
        List[Dict[str, Any]]: List of books with objective counts
    """
    try:
        service = GlobalObjectivesService(db)
        
        result = service.get_all_books(
            user_id=security_context.user_id,
            tenant_id=tenant_id
        )
        
        if result is None:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail={
                    "error": "ACCESS_DENIED",
                    "message": "User does not have permission to access books",
                    "details": {}
                }
            )
        
        return result
        
    except HTTPException:
        raise
    except Exception as e:
        import logging
        logger = logging.getLogger(__name__)
        logger.error(f"Error retrieving books: {str(e)}")
        
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail={
                "error": "INTERNAL_ERROR",
                "message": "An error occurred while retrieving books",
                "details": {}
            }
        )


@router.get(
    "/books/{book_id}/chapters",
    response_model=List[Dict[str, Any]],
    summary="Get chapters by book",
    description="Retrieve all chapters for a specific book with RBAC checks",
    responses={
        200: {
            "description": "Chapters retrieved successfully",
            "content": {
                "application/json": {
                    "example": [
                        {
                            "chapter_id": "CHAP001",
                            "chapter_name": "Sample Chapter",
                            "book_id": "BOOK001",
                            "book_name": "Sample Book",
                            "objective_count": 3
                        }
                    ]
                }
            }
        }
    }
)
async def get_chapters_by_book(
    book_id: str,
    tenant_id: str = Query(..., description="Tenant ID"),
    db: Session = Depends(get_db),
    security_context: SecurityContext = Depends(require_auth)
) -> List[Dict[str, Any]]:
    """
    Get all chapters for a specific book.
    
    Args:
        book_id: Book ID
        tenant_id: Tenant ID
        db: Database session dependency
        security_context: Security context from authentication
        
    Returns:
        List[Dict[str, Any]]: List of chapters with objective counts
    """
    try:
        service = GlobalObjectivesService(db)
        
        result = service.get_chapters_by_book(
            user_id=security_context.user_id,
            tenant_id=tenant_id,
            book_id=book_id
        )
        
        if result is None:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail={
                    "error": "ACCESS_DENIED",
                    "message": "User does not have permission to access chapters",
                    "details": {}
                }
            )
        
        return result
        
    except HTTPException:
        raise
    except Exception as e:
        import logging
        logger = logging.getLogger(__name__)
        logger.error(f"Error retrieving chapters for book {book_id}: {str(e)}")
        
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail={
                "error": "INTERNAL_ERROR",
                "message": "An error occurred while retrieving chapters",
                "details": {}
            }
        )


@router.get(
    "/books/{book_id}/objectives",
    response_model=GlobalObjectiveListResponse,
    summary="Get objectives by book",
    description="Retrieve all global objectives for a specific book with RBAC checks",
    responses={
        200: {
            "description": "Objectives retrieved successfully",
            "model": GlobalObjectiveListResponse
        }
    }
)
async def get_objectives_by_book(
    book_id: str,
    tenant_id: str = Query(..., description="Tenant ID"),
    limit: Optional[int] = Query(None, description="Limit number of results", ge=1, le=1000),
    offset: Optional[int] = Query(None, description="Offset for pagination", ge=0),
    db: Session = Depends(get_db),
    security_context: SecurityContext = Depends(require_auth)
) -> GlobalObjectiveListResponse:
    """
    Get all global objectives for a specific book.
    
    Args:
        book_id: Book ID
        tenant_id: Tenant ID
        limit: Optional limit for pagination
        offset: Optional offset for pagination
        db: Database session dependency
        security_context: Security context from authentication
        
    Returns:
        GlobalObjectiveListResponse: List of global objectives for the book
    """
    try:
        service = GlobalObjectivesService(db)
        
        result = service.get_objectives_by_book(
            user_id=security_context.user_id,
            tenant_id=tenant_id,
            book_id=book_id,
            limit=limit,
            offset=offset
        )
        
        if result is None:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail={
                    "error": "ACCESS_DENIED",
                    "message": "User does not have permission to access objectives for this book",
                    "details": {}
                }
            )
        
        return result
        
    except HTTPException:
        raise
    except Exception as e:
        import logging
        logger = logging.getLogger(__name__)
        logger.error(f"Error retrieving objectives for book {book_id}: {str(e)}")
        
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail={
                "error": "INTERNAL_ERROR",
                "message": "An error occurred while retrieving objectives for the book",
                "details": {}
            }
        )


@router.get(
    "/chapters/{chapter_id}/objectives",
    response_model=GlobalObjectiveListResponse,
    summary="Get objectives by chapter",
    description="Retrieve all global objectives for a specific chapter with RBAC checks",
    responses={
        200: {
            "description": "Objectives retrieved successfully",
            "model": GlobalObjectiveListResponse
        }
    }
)
async def get_objectives_by_chapter(
    chapter_id: str,
    tenant_id: str = Query(..., description="Tenant ID"),
    limit: Optional[int] = Query(None, description="Limit number of results", ge=1, le=1000),
    offset: Optional[int] = Query(None, description="Offset for pagination", ge=0),
    db: Session = Depends(get_db),
    security_context: SecurityContext = Depends(require_auth)
) -> GlobalObjectiveListResponse:
    """
    Get all global objectives for a specific chapter.
    
    Args:
        chapter_id: Chapter ID
        tenant_id: Tenant ID
        limit: Optional limit for pagination
        offset: Optional offset for pagination
        db: Database session dependency
        security_context: Security context from authentication
        
    Returns:
        GlobalObjectiveListResponse: List of global objectives for the chapter
    """
    try:
        service = GlobalObjectivesService(db)
        
        result = service.get_objectives_by_chapter(
            user_id=security_context.user_id,
            tenant_id=tenant_id,
            chapter_id=chapter_id,
            limit=limit,
            offset=offset
        )
        
        if result is None:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail={
                    "error": "ACCESS_DENIED",
                    "message": "User does not have permission to access objectives for this chapter",
                    "details": {}
                }
            )
        
        return result
        
    except HTTPException:
        raise
    except Exception as e:
        import logging
        logger = logging.getLogger(__name__)
        logger.error(f"Error retrieving objectives for chapter {chapter_id}: {str(e)}")
        
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail={
                "error": "INTERNAL_ERROR",
                "message": "An error occurred while retrieving objectives for the chapter",
                "details": {}
            }
        )
