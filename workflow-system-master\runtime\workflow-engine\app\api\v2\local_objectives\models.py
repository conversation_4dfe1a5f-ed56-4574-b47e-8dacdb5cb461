"""
Local Objectives Models for v2 API

This module contains Pydantic models for local objectives operations based on the actual database schema.
"""

from typing import List, Optional, Dict, Any, Union
from pydantic import BaseModel, Field
from datetime import datetime

class InputFieldResponse(BaseModel):
    """Input field response model based on actual lo_input_items table schema"""
    # Core fields from lo_input_items table
    id: int = Field(..., description="Primary key from lo_input_items.id")
    item_id: Optional[str] = Field(None, description="Item ID from lo_input_items.item_id")
    input_stack_id: Optional[str] = Field(None, description="Input stack ID from lo_input_items.input_stack_id")
    slot_id: Optional[str] = Field(None, description="Slot ID from lo_input_items.slot_id")
    source_type: Optional[str] = Field(None, description="Source type from lo_input_items.source_type")
    source_description: Optional[str] = Field(None, description="Source description from lo_input_items.source_description")
    required: Optional[bool] = Field(False, description="Whether field is required from lo_input_items.required")
    lo_id: Optional[str] = Field(None, description="Local objective ID from lo_input_items.lo_id")
    data_type: Optional[str] = Field(None, description="Data type from lo_input_items.data_type")
    ui_control: Optional[str] = Field("oj-input-text", description="UI control from lo_input_items.ui_control")
    nested_function_id: Optional[str] = Field(None, description="Nested function ID from lo_input_items.nested_function_id")
    is_visible: Optional[bool] = Field(True, description="Whether field is visible from lo_input_items.is_visible")
    name: Optional[str] = Field(None, description="Name from lo_input_items.name")
    type: Optional[str] = Field(None, description="Type from lo_input_items.type")
    read_only: Optional[bool] = Field(False, description="Whether field is readonly from lo_input_items.read_only")
    agent_type: Optional[str] = Field(None, description="Agent type from lo_input_items.agent_type")
    dependent_attribute: Optional[bool] = Field(False, description="Dependent attribute from lo_input_items.dependent_attribute")
    dependent_attribute_value: Optional[str] = Field(None, description="Dependent attribute value from lo_input_items.dependent_attribute_value")
    dependent_attribute_ids: Optional[Dict[str, str]] = Field(None, description="Mapping of dependent attribute names to their item_ids for UI binding")
    enum_values: Optional[List[Any]] = Field(None, description="Enum values from lo_input_items.enum_values (jsonb)")
    default_value: Optional[str] = Field(None, description="Default value from lo_input_items.default_value")
    information_field: Optional[bool] = Field(False, description="Information field from lo_input_items.information_field")
    constant_field: Optional[bool] = Field(False, description="Constant field from lo_input_items.constant_field")
    entity_id: Optional[str] = Field(None, description="Entity ID from lo_input_items.entity_id")
    attribute_id: Optional[str] = Field(None, description="Attribute ID from lo_input_items.attribute_id")
    entity_name: Optional[str] = Field(None, description="Entity name from lo_input_items.entity_name")
    attribute_name: Optional[str] = Field(None, description="Attribute name from lo_input_items.attribute_name")
    
    # Computed/joined fields
    display_name: Optional[str] = Field(None, description="Display name from entity_attributes")
    input_value: Optional[Any] = Field(None, description="Current input value from lo_input_execution")
    allowed_values: Optional[List[Any]] = Field(None, description="Allowed enum values from attribute_enum_values")
    validations: Optional[List[Dict[str, Any]]] = Field(None, description="Validation rules from attribute_validations")
    has_dropdown_source: Optional[bool] = Field(False, description="Whether field has dropdown source")
    dropdown_options: Optional[List[Dict[str, Any]]] = Field(None, description="Dropdown options")
    dependencies: Optional[List[str]] = Field(None, description="Field dependencies")
    dependency_type: Optional[str] = Field(None, description="Type of dependency")
    needs_parent_value: Optional[bool] = Field(False, description="Whether field needs parent values")
    parent_ids: Optional[List[str]] = Field(None, description="Parent field IDs")
    metadata: Optional[Dict[str, Any]] = Field(None, description="Additional metadata")
    
    # Computed contextual ID (for backward compatibility with v1)
    contextual_id: Optional[str] = Field(None, description="Computed contextual ID for compatibility")
    
    class Config:
        from_attributes = True

class DropdownOption(BaseModel):
    """Dropdown option model"""
    value: Any = Field(..., description="Option value")
    label: str = Field(..., description="Option label")
    
    class Config:
        from_attributes = True

class DependentInputRequest(BaseModel):
    """Request model for dependent input resolution"""
    dependent_input_id: str = Field(..., description="Dependent input ID to resolve")
    parent_values: Dict[str, Any] = Field(..., description="Parent field values")
    
    class Config:
        from_attributes = True

class DependentInputResponse(BaseModel):
    """Response model for dependent input resolution"""
    local_objective: str = Field(..., description="Local objective ID")
    dependent_input: InputFieldResponse = Field(..., description="Resolved dependent input")
    
    class Config:
        from_attributes = True

class LocalObjectiveInputsResponse(BaseModel):
    """Local objective inputs response model"""
    local_objective: str = Field(..., description="Local objective ID")
    user_inputs: List[InputFieldResponse] = Field(..., description="User input fields")
    system_inputs: List[InputFieldResponse] = Field(..., description="System input fields")
    info_inputs: List[InputFieldResponse] = Field(..., description="Information input fields")
    dependent_inputs: List[InputFieldResponse] = Field(..., description="Dependent input fields")
    dependencies: Dict[str, List[str]] = Field(..., description="Field dependencies map")
    lo_ui_stack: Optional[Dict[str, Any]] = Field(None, description="LO UI stack information from lo_ui_stacks table")
    
    class Config:
        from_attributes = True

class NestedFunctionRequest(BaseModel):
    """Request model for nested function execution"""
    function_name: str = Field(..., description="Function name to execute")
    parameters: Dict[str, Any] = Field(..., description="Function parameters")
    context_data: Optional[Dict[str, Any]] = Field(None, description="Context data for parameter resolution")
    
    class Config:
        from_attributes = True

class NestedFunctionResponse(BaseModel):
    """Response model for nested function execution"""
    function_name: str = Field(..., description="Executed function name")
    result: Any = Field(..., description="Function execution result")
    execution_time_ms: Optional[float] = Field(None, description="Execution time in milliseconds")
    
    class Config:
        from_attributes = True

class ValidationResult(BaseModel):
    """Validation result model"""
    is_valid: bool = Field(..., description="Whether validation passed")
    message: Optional[str] = Field(None, description="Validation message")
    field: Optional[str] = Field(None, description="Field that failed validation")
    details: Optional[Dict[str, Any]] = Field(None, description="Additional validation details")
    
    class Config:
        from_attributes = True

class InputValidationRequest(BaseModel):
    """Request model for input validation"""
    input_data: Dict[str, Any] = Field(..., description="Input data to validate")
    validation_rules: Optional[List[Dict[str, Any]]] = Field(None, description="Custom validation rules")
    
    class Config:
        from_attributes = True

class InputValidationResponse(BaseModel):
    """Response model for input validation"""
    is_valid: bool = Field(..., description="Whether all inputs are valid")
    validation_results: List[ValidationResult] = Field(..., description="Individual validation results")
    validated_data: Optional[Dict[str, Any]] = Field(None, description="Validated and processed data")
    
    class Config:
        from_attributes = True

class ErrorResponse(BaseModel):
    """Error response model"""
    error: str = Field(..., description="Error type")
    message: str = Field(..., description="Error message")
    details: Optional[Dict[str, Any]] = Field(None, description="Additional error details")
    
    class Config:
        from_attributes = True

class SuccessResponse(BaseModel):
    """Generic success response model"""
    success: bool = Field(True, description="Success indicator")
    message: str = Field(..., description="Success message")
    data: Optional[Dict[str, Any]] = Field(None, description="Additional response data")
    
    class Config:
        from_attributes = True

# Multi-select support models
class MultiSelectValue(BaseModel):
    """Multi-select value model"""
    values: List[Any] = Field(..., description="List of selected values")
    display_values: Optional[List[str]] = Field(None, description="List of display values")
    
    class Config:
        from_attributes = True

class MultiSelectOption(BaseModel):
    """Multi-select option model"""
    value: Any = Field(..., description="Option value")
    label: str = Field(..., description="Option label")
    selected: bool = Field(False, description="Whether option is selected")
    
    class Config:
        from_attributes = True

# Nested function execution models (based on lo_nested_functions table)
class NestedFunctionDefinition(BaseModel):
    """Nested function definition model based on lo_nested_functions table"""
    id: Optional[int] = Field(None, description="ID from lo_nested_functions.id")
    lo_id: Optional[str] = Field(None, description="LO ID from lo_nested_functions.lo_id")
    nested_function_id: Optional[str] = Field(None, description="Nested function ID from lo_nested_functions.nested_function_id")
    function_name: str = Field(..., description="Function name from lo_nested_functions.function_name")
    function_type: Optional[str] = Field(None, description="Function type from lo_nested_functions.function_type")
    parameters: Optional[Dict[str, Any]] = Field(None, description="Function parameters from lo_nested_functions.parameters (jsonb)")
    output_to: Optional[str] = Field(None, description="Output variable from lo_nested_functions.output_to")
    description: Optional[str] = Field(None, description="Description from lo_nested_functions.description")
    returns: Optional[str] = Field(None, description="Returns from lo_nested_functions.returns")
    
    class Config:
        from_attributes = True

class NestedFunctionExecutionResult(BaseModel):
    """Nested function execution result model"""
    function_name: str = Field(..., description="Function name")
    result: Any = Field(..., description="Execution result")
    output_variable: Optional[str] = Field(None, description="Output variable name")
    execution_status: str = Field(..., description="Execution status (success, error)")
    error_message: Optional[str] = Field(None, description="Error message if failed")
    
    class Config:
        from_attributes = True

# Dropdown data source models (based on dropdown_data_sources table)
class DropdownDataSource(BaseModel):
    """Dropdown data source model based on dropdown_data_sources table"""
    id: Optional[int] = Field(None, description="ID from dropdown_data_sources.id")
    input_item_id: Optional[str] = Field(None, description="Input item ID from dropdown_data_sources.input_item_id")
    input_stack_id: Optional[int] = Field(None, description="Input stack ID from dropdown_data_sources.input_stack_id")
    source_type: Optional[str] = Field(None, description="Source type from dropdown_data_sources.source_type")
    query_text: Optional[str] = Field(None, description="Query text from dropdown_data_sources.query_text")
    function_name: Optional[str] = Field(None, description="Function name from dropdown_data_sources.function_name")
    function_params: Optional[Dict[str, Any]] = Field(None, description="Function params from dropdown_data_sources.function_params (jsonb)")
    value_field: Optional[str] = Field(None, description="Value field from dropdown_data_sources.value_field")
    display_field: Optional[str] = Field(None, description="Display field from dropdown_data_sources.display_field")
    depends_on_fields: Optional[List[str]] = Field(None, description="Depends on fields from dropdown_data_sources.depends_on_fields (jsonb)")
    
    class Config:
        from_attributes = True

# Input execution models (based on lo_input_execution table)
class InputExecutionRecord(BaseModel):
    """Input execution record model based on lo_input_execution table"""
    id: Optional[int] = Field(None, description="ID from lo_input_execution.id")
    instance_id: Optional[str] = Field(None, description="Instance ID from lo_input_execution.instance_id (uuid)")
    lo_id: Optional[str] = Field(None, description="LO ID from lo_input_execution.lo_id")
    input_contextual_id: Optional[str] = Field(None, description="Input contextual ID from lo_input_execution.input_contextual_id")
    input_value: Optional[Any] = Field(None, description="Input value from lo_input_execution.input_value (jsonb)")
    created_at: Optional[datetime] = Field(None, description="Created at from lo_input_execution.created_at")
    
    class Config:
        from_attributes = True

# Output execution models (based on lo_output_execution table)
class OutputExecutionRecord(BaseModel):
    """Output execution record model based on lo_output_execution table"""
    id: Optional[int] = Field(None, description="ID from lo_output_execution.id")
    instance_id: Optional[str] = Field(None, description="Instance ID from lo_output_execution.instance_id (uuid)")
    lo_id: Optional[str] = Field(None, description="LO ID from lo_output_execution.lo_id")
    output_contextual_id: Optional[str] = Field(None, description="Output contextual ID from lo_output_execution.output_contextual_id")
    output_value: Optional[Any] = Field(None, description="Output value from lo_output_execution.output_value (jsonb)")
    created_at: Optional[datetime] = Field(None, description="Created at from lo_output_execution.created_at")
    
    class Config:
        from_attributes = True

# Execute API Models
class ExecuteLocalObjectiveRequest(BaseModel):
    """Request model for executing local objective"""
    input_data: Dict[str, Any] = Field(..., description="User-provided input data")
    user_id: str = Field(..., description="User ID executing the LO")
    
    class Config:
        from_attributes = True

class ExecuteLocalObjectiveResponse(BaseModel):
    """Response model for local objective execution"""
    status: str = Field(..., description="Execution status")
    message: str = Field(..., description="Execution message")
    output: Dict[str, Any] = Field(..., description="Execution output")
    next_lo_id: Optional[str] = Field(None, description="Next local objective ID")
    execution_id: Optional[str] = Field(None, description="Execution transaction ID")
    lo_execution_table: Optional[str] = Field(None, description="Individual LO execution table name")
    
    class Config:
        from_attributes = True

class LOExecutionRecord(BaseModel):
    """Model for individual LO execution table records"""
    workflow_instance_id: str = Field(..., description="Workflow instance ID")
    user_id: str = Field(..., description="User ID who executed the LO")
    execution_timestamp: datetime = Field(..., description="When the LO was executed")
    input_data: Dict[str, Any] = Field(..., description="Input data used for execution")
    output_data: Dict[str, Any] = Field(..., description="Output data generated")
    status: str = Field(..., description="Execution status")
    created_at: datetime = Field(..., description="Record creation timestamp")
    
    class Config:
        from_attributes = True

class ConstantValue(BaseModel):
    """Model for constant values from constants table"""
    value: Any = Field(..., description="Constant value")
    allow_override: Optional[bool] = Field(False, description="Whether value can be overridden")
    override_permissions: Optional[str] = Field(None, description="Permissions required to override")
    
    class Config:
        from_attributes = True
