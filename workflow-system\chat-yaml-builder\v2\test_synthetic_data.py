#!/usr/bin/env python3
"""
Test script to verify that synthetic data is correctly parsed and deployed to the database.
"""

import os
import logging
import json
from parsers.entity_parser import parse_entities
from deployers.entity_deployer_v2 import deploy_entities, execute_query

# Set up logging
logging.basicConfig(level=logging.INFO, 
                   format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
                   handlers=[
                       logging.FileHandler('/home/<USER>/workflow-system/chat-yaml-builder/v2/synthetic_data.log'),
                       logging.StreamHandler()
                   ])
logger = logging.getLogger('test_synthetic_data')

def test_synthetic_data():
    """
    Test that synthetic data is correctly parsed and deployed to the database.
    """
    # Create a test entity definition with synthetic data
    entity_def = """
Employee has employeeId^PK, firstName, lastName, email, phoneNumber, departmentId^FK, managerId^FK, hireDate, status(Active, Inactive, OnLeave), salary, performanceRating, probationDays, minSalary.

* Synthetic: 
Employee has employeeId = 1, firstName = Tarun, lastName = Singh, email = "<EMAIL>", phoneNumber = "************", departmentId = 101, managerId = 1001, hireDate = "2024-01-15", status = "Active", salary = 60000, performanceRating = 4.5.
Employee has employeeId = 2, firstName = Priya, lastName = Sharma, email = "<EMAIL>", phoneNumber = "************", departmentId = 102, managerId = 1002, hireDate = "2024-02-20", status = "Inactive", salary = 55000, performanceRating = 4.0.
"""
    
    # Parse the entity definition
    entities_data, warnings = parse_entities(entity_def)
    
    # Print any warnings
    if warnings:
        logger.warning("Warnings during parsing:")
        for warning in warnings:
            logger.warning(f"  - {warning}")
    
    # Check if Employee entity was parsed
    if 'entities' in entities_data and 'Employee' in entities_data['entities']:
        employee_entity = entities_data['entities']['Employee']
        
        # Check if synthetic data was parsed
        if 'synthetic_data' in employee_entity:
            logger.info("\nEmployee entity synthetic data:")
            for data in employee_entity['synthetic_data']:
                logger.info(f"  - {data}")
        else:
            logger.warning("No synthetic data found in Employee entity")
    else:
        logger.error("Employee entity not found in parsed data")
        return
    
    # Deploy the entities to the database
    schema_name = 'workflow_temp'
    success, messages = deploy_entities(entities_data, schema_name)
    
    if not success:
        logger.error("Failed to deploy entities:")
        for message in messages:
            logger.error(f"  - {message}")
        return
    
    logger.info("Successfully deployed entities")
    
    # Verify the synthetic data was deployed
    success, messages, result = execute_query(
        f"""
        SELECT *
        FROM {schema_name}.e000002_employee
        WHERE employeeid IN ('1', '2')
        """,
        schema_name=schema_name
    )
    
    if success and result:
        logger.info("\nEmployee synthetic data in the database:")
        for row in result:
            logger.info(f"  - {row}")
    else:
        logger.warning("No synthetic data found for Employee entity in the database")

if __name__ == "__main__":
    test_synthetic_data()
