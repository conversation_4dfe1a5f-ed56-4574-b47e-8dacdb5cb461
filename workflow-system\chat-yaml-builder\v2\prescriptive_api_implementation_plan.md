# Prescriptive API Implementation Plan

This document outlines the implementation plan for creating an API where users can add prescriptives, parse them, save them in the database, and retrieve the values. It also includes the implementation of a Streamlit UI for this functionality and thorough validation mechanisms.

## 1. Overview

The current YAML Builder v2 system has successfully implemented the entity side of things. Now we need to extend it to support prescriptives through an API and UI interface. This will allow users to:

1. Submit prescriptive text for entities, attributes, relationships, etc.
2. Parse the prescriptive text into structured data
3. Save the structured data in the database
4. Retrieve the values of the parsed data
5. Validate the data and receive proper error messages

### Key Principles

1. **User-Friendly**: The API and UI should be intuitive and easy to use
2. **Robust Validation**: Thorough validation of prescriptive text with clear error messages
3. **Efficient Storage**: Proper database storage with optimized queries
4. **Comprehensive Feedback**: Detailed feedback on parsing and validation results

## 2. API Implementation

### 2.1 API Endpoints

We will create the following API endpoints:

```python
# File: chat-yaml-builder/v2/api/prescriptive_api.py

from fastapi import FastAPI, HTTPException, Depends, status
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
from typing import Dict, List, Optional, Any
import logging

# Set up logging
logger = logging.getLogger("prescriptive_api")

app = FastAPI(title="Prescriptive API", description="API for managing prescriptive text")

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Adjust in production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Models
class PrescriptiveRequest(BaseModel):
    component_type: str  # 'entities', 'go_definitions', 'lo_definitions', 'roles'
    prescriptive_text: str
    validate_only: bool = False

class ValidationResponse(BaseModel):
    is_valid: bool
    messages: List[str]
    parsed_data: Optional[Dict[str, Any]] = None

class EntityResponse(BaseModel):
    entity_id: str
    name: str
    attributes: Dict[str, Any]
    relationships: Optional[Dict[str, Any]] = None
    business_rules: Optional[Dict[str, Any]] = None
    validations: Optional[Dict[str, Any]] = None
    calculated_fields: Optional[Dict[str, Any]] = None
    lifecycle_management: Optional[Dict[str, Any]] = None

# Routes
@app.post("/validate", response_model=ValidationResponse)
async def validate_prescriptive(request: PrescriptiveRequest):
    """
    Validate prescriptive text without saving to database
    """
    try:
        # Import parser based on component type
        if request.component_type == 'entities':
            from v2.parsers.entity_parser import parse_entities
            parsed_data, warnings = parse_entities(request.prescriptive_text)
        elif request.component_type == 'go_definitions':
            from v2.parsers.go_parser import parse_go_definitions
            parsed_data, warnings = parse_go_definitions(request.prescriptive_text)
        elif request.component_type == 'lo_definitions':
            from v2.parsers.lo_parser import parse_lo_definitions
            parsed_data, warnings = parse_lo_definitions(request.prescriptive_text)
        elif request.component_type == 'roles':
            from v2.parsers.role_parser import parse_roles
            parsed_data, warnings = parse_roles(request.prescriptive_text)
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Unknown component type: {request.component_type}"
            )
        
        # Validate parsed data
        is_valid = len(warnings) == 0
        
        return {
            "is_valid": is_valid,
            "messages": warnings,
            "parsed_data": parsed_data
        }
    except Exception as e:
        logger.error(f"Error validating prescriptive text: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error validating prescriptive text: {str(e)}"
        )

@app.post("/deploy", response_model=ValidationResponse)
async def deploy_prescriptive(request: PrescriptiveRequest):
    """
    Parse prescriptive text and deploy to database
    """
    try:
        # First validate
        validation_response = await validate_prescriptive(request)
        
        if not validation_response["is_valid"]:
            return validation_response
        
        # If validate_only is True, return validation results without deploying
        if request.validate_only:
            return validation_response
        
        # Deploy to database
        from v2.component_deployer import ComponentDeployer
        
        deployer = ComponentDeployer(use_temp_schema=False)
        success, messages = deployer.deploy_from_prescriptive_text(
            request.component_type, 
            request.prescriptive_text
        )
        
        return {
            "is_valid": success,
            "messages": messages,
            "parsed_data": validation_response["parsed_data"]
        }
    except Exception as e:
        logger.error(f"Error deploying prescriptive text: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error deploying prescriptive text: {str(e)}"
        )

@app.get("/entities", response_model=List[EntityResponse])
async def get_entities():
    """
    Get all entities from the database
    """
    try:
        from v2.db_utils import execute_query
        
        success, messages, result = execute_query(
            """
            SELECT e.entity_id, e.name, e.description, e.metadata, e.lifecycle_management
            FROM workflow_runtime.entities e
            ORDER BY e.name
            """
        )
        
        if not success:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Error querying entities: {messages}"
            )
        
        entities = []
        for row in result:
            entity_id, name, description, metadata, lifecycle_management = row
            
            # Get attributes
            success, attr_messages, attr_result = execute_query(
                """
                SELECT a.attribute_id, a.name, a.type, a.required, a.calculated_field, 
                       a.calculation_formula, a.dependencies
                FROM workflow_runtime.entity_attributes a
                WHERE a.entity_id = %s
                ORDER BY a.name
                """,
                (entity_id,)
            )
            
            if not success:
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail=f"Error querying attributes: {attr_messages}"
                )
            
            attributes = {}
            for attr_row in attr_result:
                attr_id, attr_name, attr_type, attr_required, attr_calculated, attr_formula, attr_deps = attr_row
                
                attributes[attr_name] = {
                    "attribute_id": attr_id,
                    "type": attr_type,
                    "required": attr_required,
                    "calculated_field": attr_calculated,
                    "calculation_formula": attr_formula,
                    "dependencies": attr_deps
                }
                
                # Get enum values if applicable
                if attr_type == 'enum':
                    success, enum_messages, enum_result = execute_query(
                        """
                        SELECT value, display_name, sort_order
                        FROM workflow_runtime.attribute_enum_values
                        WHERE attribute_id = %s
                        ORDER BY sort_order
                        """,
                        (attr_id,)
                    )
                    
                    if success and enum_result:
                        attributes[attr_name]["enum_values"] = [row[0] for row in enum_result]
                
                # Get validations
                success, val_messages, val_result = execute_query(
                    """
                    SELECT validation_name, validation_type, validation_expression, error_message
                    FROM workflow_runtime.attribute_validations
                    WHERE attribute_id = %s
                    """,
                    (attr_id,)
                )
                
                if success and val_result:
                    validations = {}
                    for val_row in val_result:
                        val_name, val_type, val_expr, val_error = val_row
                        validations[val_name] = {
                            "type": val_type,
                            "constraint": val_expr,
                            "error_message": val_error
                        }
                    
                    attributes[attr_name]["validations"] = validations
            
            # Get relationships
            success, rel_messages, rel_result = execute_query(
                """
                SELECT r.relationship_type, e2.name as target_entity, 
                       a1.name as source_attribute, a2.name as target_attribute
                FROM workflow_runtime.entity_relationships r
                JOIN workflow_runtime.entities e2 ON r.target_entity_id = e2.entity_id
                JOIN workflow_runtime.entity_attributes a1 ON r.source_attribute_id = a1.attribute_id
                JOIN workflow_runtime.entity_attributes a2 ON r.target_attribute_id = a2.attribute_id
                WHERE r.source_entity_id = %s
                """,
                (entity_id,)
            )
            
            relationships = {}
            if success and rel_result:
                for rel_row in rel_result:
                    rel_type, target_entity, source_attr, target_attr = rel_row
                    rel_name = f"{rel_type}_{target_entity}"
                    relationships[rel_name] = {
                        "type": rel_type,
                        "entity": target_entity,
                        "source_attribute": source_attr,
                        "target_attribute": target_attr
                    }
            
            # Get business rules
            success, rule_messages, rule_result = execute_query(
                """
                SELECT rule_id, name, description, condition
                FROM workflow_runtime.entity_business_rules
                WHERE entity_id = %s
                """,
                (entity_id,)
            )
            
            business_rules = {}
            if success and rule_result:
                for rule_row in rule_result:
                    rule_id, rule_name, rule_desc, rule_condition = rule_row
                    conditions = rule_condition.split('\n') if rule_condition else []
                    business_rules[rule_name] = {
                        "rule_id": rule_id,
                        "description": rule_desc,
                        "conditions": conditions
                    }
            
            entity = {
                "entity_id": entity_id,
                "name": name,
                "attributes": attributes,
                "relationships": relationships,
                "business_rules": business_rules,
                "lifecycle_management": lifecycle_management
            }
            
            entities.append(entity)
        
        return entities
    except Exception as e:
        logger.error(f"Error retrieving entities: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error retrieving entities: {str(e)}"
        )

@app.get("/entities/{entity_id}", response_model=EntityResponse)
async def get_entity(entity_id: str):
    """
    Get a specific entity by ID
    """
    try:
        from v2.db_utils import execute_query
        
        success, messages, result = execute_query(
            """
            SELECT e.entity_id, e.name, e.description, e.metadata, e.lifecycle_management
            FROM workflow_runtime.entities e
            WHERE e.entity_id = %s
            """,
            (entity_id,)
        )
        
        if not success:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Error querying entity: {messages}"
            )
        
        if not result:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Entity with ID {entity_id} not found"
            )
        
        entity_id, name, description, metadata, lifecycle_management = result[0]
        
        # Get attributes (same as in get_entities)
        # ... (code omitted for brevity, same as in get_entities)
        
        # Return entity
        # ... (code omitted for brevity, same as in get_entities)
        
    except Exception as e:
        logger.error(f"Error retrieving entity: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error retrieving entity: {str(e)}"
        )

# Main entry point
if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
```

### 2.2 API Server Setup

Create a script to run the API server:

```python
# File: chat-yaml-builder/v2/run_prescriptive_api.py

import os
import sys
import logging
from pathlib import Path

# Add parent directory to path
current_dir = Path(__file__).parent
parent_dir = current_dir.parent
sys.path.append(str(parent_dir))

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("prescriptive_api.log"),
        logging.StreamHandler()
    ]
)

# Import and run the API
from api.prescriptive_api import app
import uvicorn

if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=8000)
```

## 3. Prescriptive Parser Implementation

### 3.1 Base Parser Class

Create a base parser class that all component parsers will inherit from:

```python
# File: chat-yaml-builder/v2/parsers/base_parser.py

from typing import Dict, List, Tuple, Any
import logging

class BaseParser:
    """
    Base class for all prescriptive text parsers.
    """
    
    def __init__(self):
        self.logger = logging.getLogger(self.__class__.__name__)
    
    def parse(self, prescriptive_text: str) -> Tuple[Dict[str, Any], List[str]]:
        """
        Parse prescriptive text into structured data.
        
        Args:
            prescriptive_text: Prescriptive text to parse
            
        Returns:
            Tuple containing:
                - Structured data dictionary
                - List of warning messages (empty if no warnings)
        """
        raise NotImplementedError("Subclasses must implement parse method")
    
    def validate(self, parsed_data: Dict[str, Any]) -> List[str]:
        """
        Validate parsed data.
        
        Args:
            parsed_data: Parsed data to validate
            
        Returns:
            List of validation error messages (empty if no errors)
        """
        raise NotImplementedError("Subclasses must implement validate method")
```

### 3.2 Entity Parser Enhancements

Enhance the existing entity parser with validation capabilities:

```python
# File: chat-yaml-builder/v2/parsers/entity_parser.py

# Add to existing entity_parser.py

def validate_entities(entities_data: Dict) -> List[str]:
    """
    Validate entity definitions.
    
    Args:
        entities_data: Parsed entity data
        
    Returns:
        List of validation error messages (empty if no errors)
    """
    validation_errors = []
    
    # Check if entities key exists
    if 'entities' not in entities_data:
        validation_errors.append("Missing 'entities' key in entities definition")
        return validation_errors
    
    # Validate each entity
    for entity_name, entity_def in entities_data['entities'].items():
        # Validate entity name
        if not entity_name:
            validation_errors.append("Entity name cannot be empty")
            continue
        
        # Validate attributes
        if 'attributes' not in entity_def:
            validation_errors.append(f"Entity '{entity_name}' has no attributes")
            continue
        
        if not entity_def['attributes']:
            validation_errors.append(f"Entity '{entity_name}' has empty attributes")
            continue
        
        # Check for primary key
        has_pk = False
        for attr_name, attr_def in entity_def['attributes'].items():
            if attr_def.get('primary_key', False):
                has_pk = True
                break
        
        if not has_pk:
            validation_errors.append(f"Entity '{entity_name}' has no primary key attribute")
        
        # Validate relationships
        if 'relationships' in entity_def:
            for rel_name, rel_def in entity_def['relationships'].items():
                if 'entity' not in rel_def:
                    validation_errors.append(f"Relationship '{rel_name}' in entity '{entity_name}' is missing 'entity'")
                
                if 'type' not in rel_def:
                    validation_errors.append(f"Relationship '{rel_name}' in entity '{entity_name}' is missing 'type'")
                
                if 'source_attribute' not in rel_def:
                    validation_errors.append(f"Relationship '{rel_name}' in entity '{entity_name}' is missing 'source_attribute'")
                
                if 'target_attribute' not in rel_def:
                    validation_errors.append(f"Relationship '{rel_name}' in entity '{entity_name}' is missing 'target_attribute'")
        
        # Validate business rules
        if 'business_rules' in entity_def:
            for rule_name, rule_def in entity_def['business_rules'].items():
                if 'conditions' not in rule_def or not rule_def['conditions']:
                    validation_errors.append(f"Business rule '{rule_name}' in entity '{entity_name}' has no conditions")
    
    return validation_errors
```

### 3.3 Prescriptive Parser Class

Create a central prescriptive parser class that uses the appropriate component parser:

```python
# File: chat-yaml-builder/v2/prescriptive_parser.py

from typing import Dict, List, Tuple, Any
import logging

# Set up logging
logger = logging.getLogger('prescriptive_parser')

class PrescriptiveParser:
    """
    Central parser for prescriptive text.
    """
    
    def __init__(self):
        pass
    
    def parse(self, component_type: str, prescriptive_text: str) -> Tuple[Dict[str, Any], List[str]]:
        """
        Parse prescriptive text for a specific component type.
        
        Args:
            component_type: Type of the component ('roles', 'entities', 'go_definitions', 'lo_definitions')
            prescriptive_text: Prescriptive text to parse
            
        Returns:
            Tuple containing:
                - Structured data dictionary
                - List of warning messages (empty if no warnings)
        """
        logger.info(f"Parsing prescriptive text for component type: {component_type}")
        
        try:
            # Use the appropriate parser based on component type
            if component_type == 'entities':
                from parsers.entity_parser import parse_entities, validate_entities
                parsed_data, warnings = parse_entities(prescriptive_text)
                validation_errors = validate_entities(parsed_data)
                warnings.extend(validation_errors)
            elif component_type == 'go_definitions':
                from parsers.go_parser import parse_go_definitions, validate_go_definitions
                parsed_data, warnings = parse_go_definitions(prescriptive_text)
                validation_errors = validate_go_definitions(parsed_data)
                warnings.extend(validation_errors)
            elif component_type == 'lo_definitions':
                from parsers.lo_parser import parse_lo_definitions, validate_lo_definitions
                parsed_data, warnings = parse_lo_definitions(prescriptive_text)
                validation_errors = validate_lo_definitions(parsed_data)
                warnings.extend(validation_errors)
            elif component_type == 'roles':
                from parsers.role_parser import parse_roles, validate_roles
                parsed_data, warnings = parse_roles(prescriptive_text)
                validation_errors = validate_roles(parsed_data)
                warnings.extend(validation_errors)
            else:
                logger.error(f"Unknown component type: {component_type}")
                return {}, [f"Unknown component type: {component_type}"]
            
            logger.info(f"Parsed {component_type} with {len(warnings)} warnings")
            return parsed_data, warnings
        except Exception as e:
            logger.error(f"Error parsing prescriptive text: {str(e)}", exc_info=True)
            return {}, [f"Error parsing prescriptive text: {str(e)}"]
    
    def validate_existing_entity(self, entity_id: str, entity_name: str) -> List[str]:
        """
        Validate if an entity with the given ID or name already exists.
        
        Args:
            entity_id: Entity ID to check
            entity_name: Entity name to check
            
        Returns:
            List of validation error messages (empty if no errors)
        """
        from db_utils import execute_query
        
        validation_errors = []
        
        # Check if entity ID exists
        success, messages, result = execute_query(
            """
            SELECT entity_id, name FROM workflow_runtime.entities 
            WHERE entity_id = %s OR name = %s
            """,
            (entity_id, entity_name)
        )
        
        if not success:
            validation_errors.append(f"Error checking if entity exists: {messages}")
            return validation_errors
        
        if result:
            for row in result:
                existing_id, existing_name = row
                if existing_id == entity_id:
                    validation_errors.append(f"Entity with ID '{entity_id}' already exists")
                if existing_name == entity_name:
                    validation_errors.append(f"Entity with name '{entity_name}' already exists")
        
        return validation_errors
```

## 4. Streamlit UI Implementation

### 4.1 Streamlit App

Create a Streamlit app for the prescriptive UI:

```python
# File: chat-yaml-builder/v2/streamlit_prescriptive_ui.py

import streamlit as st
import requests
import json
import pandas as pd
import logging
from typing import Dict, List, Any

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("prescriptive_ui.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger("prescriptive_ui")

# API URL
API_URL = "http://localhost:8000"

def main():
    st.set_page_config(
        page_title="Prescriptive YAML Builder",
        page_icon="📝",
        layout="wide"
    )
    
    st.title("Prescriptive YAML Builder v2")
    
    # Sidebar
    st.sidebar.title("Navigation")
    page = st.sidebar.radio(
        "Select a page",
        ["Add Prescriptive", "View Entities", "View GOs", "View LOs", "View Roles"]
    )
    
    if page == "Add Prescriptive":
        show_add_prescriptive_page()
    elif page == "View Entities":
        show_view_entities_page()
    elif page == "View GOs":
        show_view_gos_page()
    elif page == "View LOs":
        show_view_los_page()
    elif page == "View Roles":
        show_view_roles_page()

def show_add_prescriptive_page():
    st.header("Add Prescriptive Text")
    
    # Component type selection
    component_type = st.selectbox(
        "Select Component Type",
        ["entities", "go_definitions", "lo_definitions", "roles"]
    )
    
    # Prescriptive text input
    prescriptive_text = st.text_area(
        "Enter Prescriptive Text",
        height=300,
        help="Enter the prescriptive text for the selected component type"
    )
    
    # Validation only checkbox
    validate_only = st.checkbox("Validate Only (Don't Deploy)")
    
    # Submit button
    if st.button("Submit"):
        if not prescriptive_text:
            st.error("Please enter prescriptive text")
            return
        
        try:
            # Prepare request data
            request_data = {
                "component_type": component_type,
                "prescriptive_text": prescriptive_text,
                "validate_only": validate_only
            }
            
            # Call API
            endpoint = f"{API_URL}/validate" if validate_only else f"{API_URL}/deploy"
            response = requests.post(endpoint, json=request_data)
            
            if response.status_code == 200:
                result = response.json()
                
                if result["is_valid"]:
                    st.success("Validation successful!")
                    
                    if not validate_only:
                        st.success("Deployment successful!")
                    
                    # Display parsed data
                    st.subheader("Parsed Data")
                    st.json(result["parsed_data"])
                else:
                    st.error("Validation failed!")
                    
                    # Display validation messages
                    st.subheader("Validation Messages")
                    for message in result["messages"]:
                        st.error(message)
            else:
                st.error(f"API Error: {response.status_code} - {response.text}")
        except Exception as e:
            logger.error(f"Error submitting prescriptive text: {str(e)}", exc_info=True)
            st.error(f"Error: {str(e)}")

def show_view_entities_page():
    st.header("View Entities")
    
    try:
        # Call API to get entities
        response = requests.get(f"{API_URL}/entities")
        
        if response.status_code == 200:
            entities = response.json()
            
            if not entities:
                st.info("No entities found")
                return
            
            # Display entities in a table
            entity_data = []
            for entity in entities:
                entity_data.append({
                    "Entity ID": entity["entity_id"],
                    "Name": entity["name"],
                    "Attributes": len(entity["attributes"]),
                    "Relationships": len(entity.get("relationships", {})),
                    "Business Rules": len(entity.get("business_rules", {}))
                })
            
            st.dataframe(pd.DataFrame(entity_data))
            
            # Entity details
            st.subheader("Entity Details")
            selected_entity = st.selectbox(
                "Select an entity to view details",
                [entity["name"] for entity in entities]
            )
            
            # Find selected entity
            entity = next((e for e in entities if e["name"] == selected_entity), None)
            
            if entity:
                # Display entity details
                st.write(f"**Entity ID:** {entity['entity_id']}")
                st.write(f"**Name:** {entity['name']}")
                
                # Attributes
                st.subheader("Attributes")
                attr_data = []
                for attr_name, attr in entity["attributes"].items():
                    attr_data.append({
                        "Name": attr_name,
                        "Type": attr["type"],
                        "Required": "Yes" if attr["required"] else "No",
                        "Primary Key": "Yes" if attr.get("primary_key", False) else "No",
                        "Calculated": "Yes" if attr.get("calculated_field", False) else "No"
                    })
                
                st.dataframe(pd.DataFrame(attr_data))
                
                # Relationships
                if entity.get("relationships"):
                    st.subheader("Relationships")
                    rel_data = []
                    for rel_name, rel in entity["relationships"].items():
                        rel_data.append({
                            "Name": rel_name,
                            "Type": rel["type"],
                            "Target Entity": rel["entity"],
                            "Source Attribute": rel["source_attribute"],
                            "Target Attribute": rel["target_attribute"]
                        })
                    
                    st.dataframe(pd.DataFrame(rel_data))
                
                # Business Rules
                if entity.get("business_rules"):
                    st.subheader("Business Rules")
                    for rule_name, rule in entity["business_rules"].items():
                        st.write(f"**{rule_name}**")
                        st.write(f"Description: {rule.get('description', '')}")
                        st.write("Conditions:")
                        for condition in rule.get("conditions", []):
                            st.write(f"- {condition}")
                        st.write("---")
        else:
            st.error(f"API Error: {response.status_code} - {response.text}")
    except Exception as e:
        logger.error(f"Error retrieving entities: {str(e)}", exc_info=True)
        st.error(f"Error: {str(e)}")

def show_view_gos_page():
    st.header("View Global Objectives")
    st.info("Implementation pending")
    # Similar to show_view_entities_page but for GOs

def show_view_los_page():
    st.header("View Local Objectives")
    st.info("Implementation pending")
    # Similar to show_view_entities_page but for LOs

def show_view_roles_page():
    st.header("View Roles")
    st.info("Implementation pending")
    # Similar to show_view_entities_page but for Roles

if __name__ == "__main__":
    main()
```

### 4.2 Streamlit App Runner

Create a script to run the Streamlit app:

```python
# File: chat-yaml-builder/v2/run_streamlit_ui.py

import os
import sys
import subprocess
from pathlib import Path

# Add parent directory to path
current_dir = Path(__file__).parent
parent_dir = current_dir.parent
sys.path.append(str(parent_dir))

if __name__ == "__main__":
    # Run the Streamlit app
    subprocess.run(["streamlit", "run", "streamlit_prescriptive_ui.py"])
```

## 5. Database Schema Updates

### 5.1 Remove Unnecessary Columns

As mentioned in the requirements, there are extra columns in the entity table like description, lifecycle management, and metadata which were moved to new tables. We need to remove these columns:

```sql
-- File: chat-yaml-builder/v2/sql/remove_unnecessary_columns.sql

-- Remove unnecessary columns from entities table
ALTER TABLE workflow_runtime.entities 
DROP COLUMN IF EXISTS description,
DROP COLUMN IF EXISTS lifecycle_management,
DROP COLUMN IF EXISTS metadata;

-- Add a comment to explain the change
COMMENT ON TABLE workflow_runtime.entities IS 'Entity table with unnecessary columns removed';
```

Create a script to execute this SQL:

```python
# File: chat-yaml-builder/v2/remove_unnecessary_columns.py

import os
import sys
import logging
from pathlib import Path

# Add parent directory to path
current_dir = Path(__file__).parent
parent_dir = current_dir.parent
sys.path.append(str(parent_dir))

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("remove_columns.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger("remove_columns")

from db_utils import execute_query

def remove_unnecessary_columns():
    """
    Remove unnecessary columns from the entities table.
    """
    logger.info("Removing unnecessary columns from entities table")
    
    # Read SQL file
    sql_file_path = os.path.join(current_dir, "sql", "remove_unnecessary_columns.sql")
    with open(sql_file_path, "r") as f:
        sql = f.read()
    
    # Execute SQL
    success, messages, _ = execute_query(sql)
    
    if success:
        logger.info("Successfully removed unnecessary columns")
        return True, messages
    else:
        logger.error(f"Failed to remove unnecessary columns: {messages}")
        return False, messages

if __name__ == "__main__":
    success, messages = remove_unnecessary_columns()
    
    if success:
        print("Successfully removed unnecessary columns")
    else:
        print(f"Failed to remove unnecessary columns: {messages}")
```

### 5.2 Use Existing Metadata Tables

The metadata tables for entities already exist in the database. We'll use these existing tables to store entity metadata and lifecycle management information:

1. **entity_metadata**: Stores general metadata for entities
2. **entity_lifecycle_management**: Stores lifecycle management information for entities

When deploying entities, we'll ensure that the metadata and lifecycle management information is properly stored in these tables.

## 6. Validation Mechanisms

### 6.1 Entity Validation

Implement thorough validation for entities:

```python
# File: chat-yaml-builder/v2/validators/entity_validator.py

import re
import logging
from typing import Dict, List, Any
from db_utils import execute_query

# Set up logging
logger = logging.getLogger('entity_validator')

class EntityValidator:
    """
    Validator for entity definitions.
    """
    
    def __init__(self):
        pass
    
    def validate_entity(self, entity_name: str, entity_def: Dict[str, Any]) -> List[str]:
        """
        Validate an entity definition.
        
        Args:
            entity_name: Name of the entity
            entity_def: Entity definition
            
        Returns:
            List of validation error messages (empty if no errors)
        """
        validation_errors = []
        
        # Validate entity name
        if not entity_name:
            validation_errors.append("Entity name cannot be empty")
            return validation_errors
        
        if not re.match(r'^[A-Za-z][A-Za-z0-9_]*$', entity_name):
            validation_errors.append(f"Entity name '{entity_name}' must start with a letter and contain only letters, numbers, and underscores")
        
        # Validate attributes
        if 'attributes' not in entity_def:
            validation_errors.append(f"Entity '{entity_name}' has no attributes")
            return validation_errors
        
        if not entity_def['attributes']:
            validation_errors.append(f"Entity '{entity_name}' has empty attributes")
            return validation_errors
        
        # Check for primary key
        has_pk = False
        for attr_name, attr_def in entity_def['attributes'].items():
            if attr_def.get('primary_key', False):
                has_pk = True
                break
        
        if not has_pk:
            validation_errors.append(f"Entity '{entity_name}' has no primary key attribute")
        
        # Validate each attribute
        for attr_name, attr_def in entity_def['attributes'].items():
            attr_errors = self.validate_attribute(entity_name, attr_name, attr_def)
            validation_errors.extend(attr_errors)
        
        # Validate relationships
        if 'relationships' in entity_def:
            for rel_name, rel_def in entity_def['relationships'].items():
                rel_errors = self.validate_relationship(entity_name, rel_name, rel_def)
                validation_errors.extend(rel_errors)
        
        # Validate business rules
        if 'business_rules' in entity_def:
            for rule_name, rule_def in entity_def['business_rules'].items():
                rule_errors = self.validate_business_rule(entity_name, rule_name, rule_def)
                validation_errors.extend(rule_errors)
        
        # Validate calculated fields
        if 'calculated_fields' in entity_def:
            for field_name, field_def in entity_def['calculated_fields'].items():
                field_errors = self.validate_calculated_field(entity_name, field_name, field_def)
                validation_errors.extend(field_errors)
        
        return validation_errors
    
    def validate_attribute(self, entity_name: str, attr_name: str, attr_def: Dict[str, Any]) -> List[str]:
        """
        Validate an attribute definition.
        
        Args:
            entity_name: Name of the entity
            attr_name: Name of the attribute
            attr_def: Attribute definition
            
        Returns:
            List of validation error messages (empty if no errors)
        """
        validation_errors = []
        
        # Validate attribute name
        if not attr_name:
            validation_errors.append(f"Attribute name in entity '{entity_name}' cannot be empty")
            return validation_errors
        
        if not re.match(r'^[A-Za-z][A-Za-z0-9_]*$', attr_name):
            validation_errors.append(f"Attribute name '{attr_name}' in entity '{entity_name}' must start with a letter and contain only letters, numbers, and underscores")
        
        # Validate attribute type
        if 'type' not in attr_def:
            validation_errors.append(f"Attribute '{attr_name}' in entity '{entity_name}' has no type")
        else:
            valid_types = ['string', 'text', 'integer', 'float', 'boolean', 'date', 'datetime', 'time', 'json', 'enum']
            if attr_def['type'].lower() not in valid_types:
                validation_errors.append(f"Attribute '{attr_name}' in entity '{entity_name}' has invalid type '{attr_def['type']}'. Valid types are: {', '.join(valid_types)}")
        
        # Validate enum values
        if attr_def.get('type', '').lower() == 'enum' and 'enum_values' not in attr_def:
            validation_errors.append(f"Enum attribute '{attr_name}' in entity '{entity_name}' has no enum values")
        
        return validation_errors
    
    def validate_relationship(self, entity_name: str, rel_name: str, rel_def: Dict[str, Any]) -> List[str]:
        """
        Validate a relationship definition.
        
        Args:
            entity_name: Name of the entity
            rel_name: Name of the relationship
            rel_def: Relationship definition
            
        Returns:
            List of validation error messages (empty if no errors)
        """
        validation_errors = []
        
        # Validate relationship name
        if not rel_name:
            validation_errors.append(f"Relationship name in entity '{entity_name}' cannot be empty")
            return validation_errors
        
        # Validate required fields
        required_fields = ['entity', 'type', 'source_attribute', 'target_attribute']
        for field in required_fields:
            if field not in rel_def:
                validation_errors.append(f"Relationship '{rel_name}' in entity '{entity_name}' is missing required field '{field}'")
        
        # Validate relationship type
        if 'type' in rel_def:
            valid_types = ['one-to-one', 'one-to-many', 'many-to-one', 'many-to-many']
            if rel_def['type'].lower() not in valid_types:
                validation_errors.append(f"Relationship '{rel_name}' in entity '{entity_name}' has invalid type '{rel_def['type']}'. Valid types are: {', '.join(valid_types)}")
        
        # Check if target entity exists
        if 'entity' in rel_def:
            target_entity = rel_def['entity']
            success, messages, result = execute_query(
                """
                SELECT COUNT(*) FROM workflow_runtime.entities 
                WHERE name = %s
                """,
                (target_entity,)
            )
            
            if success and result and result[0][0] == 0:
                validation_errors.append(f"Relationship '{rel_name}' in entity '{entity_name}' references non-existent entity '{target_entity}'")
        
        return validation_errors
    
    def validate_business_rule(self, entity_name: str, rule_name: str, rule_def: Dict[str, Any]) -> List[str]:
        """
        Validate a business rule definition.
        
        Args:
            entity_name: Name of the entity
            rule_name: Name of the business rule
            rule_def: Business rule definition
            
        Returns:
            List of validation error messages (empty if no errors)
        """
        validation_errors = []
        
        # Validate business rule name
        if not rule_name:
            validation_errors.append(f"Business rule name in entity '{entity_name}' cannot be empty")
            return validation_errors
        
        # Validate conditions
        if 'conditions' not in rule_def or not rule_def['conditions']:
            validation_errors.append(f"Business rule '{rule_name}' in entity '{entity_name}' has no conditions")
        
        return validation_errors
    
    def validate_calculated_field(self, entity_name: str, field_name: str, field_def: Dict[str, Any]) -> List[str]:
        """
        Validate a calculated field definition.
        
        Args:
            entity_name: Name of the entity
            field_name: Name of the calculated field
            field_def: Calculated field definition
            
        Returns:
            List of validation error messages (empty if no errors)
        """
        validation_errors = []
        
        # Validate calculated field name
        if not field_name:
            validation_errors.append(f"Calculated field name in entity '{entity_name}' cannot be empty")
            return validation_errors
        
        # Validate required fields
        required_fields = ['attribute', 'formula']
        for field in required_fields:
            if field not in field_def:
                validation_errors.append(f"Calculated field '{field_name}' in entity '{entity_name}' is missing required field '{field}'")
        
        return validation_errors
    
    def validate_existing_entity(self, entity_id: str, entity_name: str) -> List[str]:
        """
        Validate if an entity with the given ID or name already exists.
        
        Args:
            entity_id: Entity ID to check
            entity_name: Entity name to check
            
        Returns:
            List of validation error messages (empty if no errors)
        """
        validation_errors = []
        
        # Check if entity ID exists
        success, messages, result = execute_query(
            """
            SELECT entity_id, name FROM workflow_runtime.entities 
            WHERE entity_id = %s OR name = %s
            """,
            (entity_id, entity_name)
        )
        
        if not success:
            validation_errors.append(f"Error checking if entity exists: {messages}")
            return validation_errors
        
        if result:
            for row in result:
                existing_id, existing_name = row
                if existing_id == entity_id:
                    validation_errors.append(f"Entity with ID '{entity_id}' already exists")
                if existing_name == entity_name:
                    validation_errors.append(f"Entity with name '{entity_name}' already exists")
        
        return validation_errors
```

### 6.2 Integration with Entity Deployer

Modify the entity deployer to use the validator:

```python
# File: chat-yaml-builder/v2/deployers/entity_deployer_v2.py

# Add to existing entity_deployer_v2.py

from validators.entity_validator import EntityValidator

def deploy_single_entity(entity_name: str, entity_def: Dict, schema_name: str) -> Tuple[bool, List[str]]:
    """
    Deploy a single entity to the database.
    
    Args:
        entity_name: Name of the entity
        entity_def: Entity definition
        schema_name: Schema name to deploy to
        
    Returns:
        Tuple containing:
            - Boolean indicating if deployment was successful
            - List of messages (warnings, errors, or success messages)
    """
    messages = []
    
    try:
        # Validate entity definition
        validator = EntityValidator()
        validation_errors = validator.validate_entity(entity_name, entity_def)
        
        if validation_errors:
            logger.error(f"Validation errors for entity '{entity_name}': {validation_errors}")
            return False, validation_errors
        
        # Generate entity ID
        entity_id = get_next_entity_id(schema_name)
        
        # Check if entity exists
        success, query_messages, result = execute_query(
            f"SELECT entity_id FROM {schema_name}.entities WHERE name = %s",
            (entity_name,),
            schema_name
        )
        
        if not success:
            messages.extend(query_messages)
            return False, messages
        
        entity_exists = result and len(result) > 0
        
        if entity_exists:
            # Use existing entity ID
            entity_id = result[0][0]
            logger.info(f"Using existing entity ID {entity_id} for {entity_name}")
            
            # Validate that we can update this entity
            validation_errors = validator.validate_existing_entity(entity_id, entity_name)
            
            if validation_errors:
                logger.error(f"Validation errors for existing entity '{entity_name}': {validation_errors}")
                return False, validation_errors
            
            # Update existing entity
            # ... (rest of the function remains the same)
```

## 7. Testing Strategy

### 7.1 Unit Tests

Create unit tests for the prescriptive API and parsers:

```python
# File: chat-yaml-builder/v2/tests/test_prescriptive_api.py

import unittest
import json
from fastapi.testclient import TestClient
from v2.api.prescriptive_api import app

class TestPrescriptiveAPI(unittest.TestCase):
    """
    Unit tests for the prescriptive API.
    """
    
    def setUp(self):
        self.client = TestClient(app)
    
    def test_validate_entity(self):
        """
        Test validating an entity.
        """
        # Valid entity
        request_data = {
            "component_type": "entities",
            "prescriptive_text": "Employee has id^PK, name, email, department, status (Active, Inactive, OnLeave).",
            "validate_only": True
        }
        
        response = self.client.post("/validate", json=request_data)
        self.assertEqual(response.status_code, 200)
        result = response.json()
        self.assertTrue(result["is_valid"])
        
        # Invalid entity (no primary key)
        request_data = {
            "component_type": "entities",
            "prescriptive_text": "Employee has id, name, email, department.",
            "validate_only": True
        }
        
        response = self.client.post("/validate", json=request_data)
        self.assertEqual(response.status_code, 200)
        result = response.json()
        self.assertFalse(result["is_valid"])
    
    def test_get_entities(self):
        """
        Test getting entities.
        """
        response = self.client.get("/entities")
        self.assertEqual(response.status_code, 200)
        
        # More tests...

# More test classes...
```

### 7.2 Integration Tests

Create integration tests for the entire system:

```python
# File: chat-yaml-builder/v2/tests/test_integration.py

import unittest
import requests
import json
import time

class TestIntegration(unittest.TestCase):
    """
    Integration tests for the prescriptive API and Streamlit UI.
    """
    
    def setUp(self):
        self.api_url = "http://localhost:8000"
    
    def test_end_to_end(self):
        """
        Test the entire workflow from submitting prescriptive text to retrieving entities.
        """
        # Deploy an entity
        request_data = {
            "component_type": "entities",
            "prescriptive_text": "Employee has id^PK, name, email, department, status (Active, Inactive, OnLeave).",
            "validate_only": False
        }
        
        response = requests.post(f"{self.api_url}/deploy", json=request_data)
        self.assertEqual(response.status_code, 200)
        result = response.json()
        self.assertTrue(result["is_valid"])
        
        # Get entities
        response = requests.get(f"{self.api_url}/entities")
        self.assertEqual(response.status_code, 200)
        entities = response.json()
        
        # Find the deployed entity
        employee_entity = next((e for e in entities if e["name"] == "Employee"), None)
        self.assertIsNotNone(employee_entity)
        self.assertEqual(len(employee_entity["attributes"]), 5)
        
        # More assertions...
```

### 7.3 Test Runner

Create a script to run all tests:

```python
# File: chat-yaml-builder/v2/run_tests.py

import unittest
import sys
import os
from pathlib import Path

# Add parent directory to path
current_dir = Path(__file__).parent
parent_dir = current_dir.parent
sys.path.append(str(parent_dir))

if __name__ == "__main__":
    # Discover and run tests
    test_loader = unittest.TestLoader()
    test_suite = test_loader.discover("tests", pattern="test_*.py")
    
    # Run tests
    test_runner = unittest.TextTestRunner(verbosity=2)
    result = test_runner.run(test_suite)
    
    # Exit with non-zero code if tests failed
    sys.exit(not result.wasSuccessful())
```

## 8. Deployment Steps

### 8.1 Prerequisites

Before deploying the prescriptive API and UI, ensure the following prerequisites are met:

1. Python 3.8 or higher is installed
2. PostgreSQL database is running
3. Required Python packages are installed:
   - fastapi
   - uvicorn
   - streamlit
   - psycopg2-binary
   - pydantic
   - requests
   - pandas

### 8.2 Installation

1. Clone the repository:
   ```bash
   git clone <repository-url>
   cd workflow-system
   ```

2. Install dependencies:
   ```bash
   pip install -r requirements.txt
   ```

3. Update database schema:
   ```bash
   python chat-yaml-builder/v2/remove_unnecessary_columns.py
   python chat-yaml-builder/v2/create_metadata_tables.py
   ```

### 8.3 Running the API

Start the prescriptive API:

```bash
python chat-yaml-builder/v2/run_prescriptive_api.py
```

The API will be available at http://localhost:8000.

### 8.4 Running the Streamlit UI

Start the Streamlit UI:

```bash
python chat-yaml-builder/v2/run_streamlit_ui.py
```

The UI will be available at http://localhost:8501.

## 9. Future Enhancements

### 9.1 Performance Optimization

1. Implement caching for frequently accessed data
2. Optimize database queries
3. Add pagination for large result sets

### 9.2 Security Enhancements

1. Add authentication and authorization
2. Implement rate limiting
3. Add input validation and sanitization

### 9.3 UI Improvements

1. Add more interactive visualizations
2. Implement drag-and-drop functionality
3. Add dark mode support

### 9.4 Additional Features

1. Export/import functionality
2. Version control for prescriptive text
3. Collaboration features
4. Audit logging
