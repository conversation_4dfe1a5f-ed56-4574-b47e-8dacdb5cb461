#!/usr/bin/env python3
"""
Test script to verify that attribute properties are correctly parsed.
"""

import os
import json
import logging
from parsers.entity_parser import parse_entities

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger('test_attribute_properties')

def test_attribute_properties():
    """
    Test that attribute properties are correctly parsed.
    """
    # Define a simple entity definition with attribute properties
    entity_def = """
Employee has employeeId^PK, firstName, lastName, email, phoneNumber, departmentId^FK, managerId^FK, hireDate, status (Active, Inactive, OnLeave), salary, performanceRating, probationDays, minSalary.

* Employee.probationDays PROPERTY_NAME = 90
* Employee.minSalary PROPERTY_NAME = 30000
* Employee.status DEFAULT_VALUE = "Active"
* Employee.hireDate DEFAULT_VALUE = CURRENT_DATE
    """
    
    # Parse the entity definition
    entities_data, warnings = parse_entities(entity_def)
    
    # Print any warnings
    if warnings:
        logger.warning("Warnings during parsing:")
        for warning in warnings:
            logger.warning(f"  - {warning}")
    
    # Check if Employee entity was parsed
    if 'entities' in entities_data and 'Employee' in entities_data['entities']:
        employee_entity = entities_data['entities']['Employee']
        
        # Check if attributes were parsed
        if 'attributes' in employee_entity:
            logger.info("\nEmployee attributes:")
            for attr_name, attr_def in employee_entity['attributes'].items():
                logger.info(f"\n  - Attribute: {attr_name}")
                
                # Check for default value
                if 'default' in attr_def:
                    logger.info(f"    - Default Value: {attr_def['default']}")
                
                # Check for property value
                if 'property_value' in attr_def:
                    logger.info(f"    - Property Value: {attr_def['property_value']}")
                
                # Check for other properties
                for prop_name, prop_value in attr_def.items():
                    if prop_name not in ['default', 'property_value']:
                        logger.info(f"    - {prop_name}: {prop_value}")
        else:
            logger.error("No attributes found in Employee entity")
    else:
        logger.error("Employee entity not found in parsed data")
    
    # Print the full parsed data as JSON
    logger.info("\nFull parsed data:")
    logger.info(json.dumps(entities_data, indent=2))

if __name__ == "__main__":
    test_attribute_properties()
