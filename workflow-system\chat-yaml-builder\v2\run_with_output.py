"""
Run commands and save output to a file.
"""

import os
import sys
import subprocess
import datetime

def run_command(command, output_file):
    """
    Run a command and save the output to a file.
    
    Args:
        command: Command to run
        output_file: File to save output to
    """
    with open(output_file, 'a') as f:
        f.write(f"\n\n=== Running command: {command} ===\n\n")
        
        # Run the command and capture output
        process = subprocess.Popen(
            command,
            shell=True,
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            universal_newlines=True
        )
        
        # Write output to file as it becomes available
        for line in process.stdout:
            f.write(line)
            f.flush()
        
        # Wait for the process to complete
        process.wait()
        
        f.write(f"\n\n=== Command completed with exit code: {process.returncode} ===\n\n")

def main():
    """Main function."""
    # Create output file with timestamp
    timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
    output_file = f"command_output_{timestamp}.txt"
    
    # Run commands
    commands = [
        "python3 reset_database_direct.py",
        "python3 init_database.py",
        "python3 deploy_entities_and_go.py"
    ]
    
    with open(output_file, 'w') as f:
        f.write(f"=== Command output log - {datetime.datetime.now()} ===\n")
    
    for command in commands:
        run_command(command, output_file)
    
    print(f"All commands completed. Output saved to {output_file}")

if __name__ == "__main__":
    main()
