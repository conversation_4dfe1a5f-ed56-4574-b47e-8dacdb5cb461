#!/usr/bin/env python3
"""
Test script to parse entity definitions and deploy relationships to the database.
"""

import os
import logging
import sys
from parsers.entity_parser import parse_entities
from deployers.entity_deployer_v2 import deploy_entities, execute_query

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger('test_parse_deploy')

def test_parse_and_deploy_relationships():
    """
    Parse entity definitions from sample file and deploy relationships to the database.
    """
    # Read the sample entity output file
    sample_file_path = '/home/<USER>/workflow-system/chat-yaml-builder/v2/samples/sample_entity_output2.txt'
    with open(sample_file_path, 'r') as f:
        entity_def = f.read()
    
    # Parse the entity definition
    entities_data, warnings = parse_entities(entity_def)
    
    # Print any warnings
    if warnings:
        logger.warning("Warnings during parsing:")
        for warning in warnings:
            logger.warning(f"  - {warning}")
    
    # Check if entities were parsed
    if 'entities' not in entities_data:
        logger.error("No entities found in parsed data")
        return
    
    # Print parsed entities
    logger.info(f"Parsed {len(entities_data['entities'])} entities:")
    for entity_name in entities_data['entities']:
        logger.info(f"  - {entity_name}")
    
    # Check if Employee entity was parsed
    if 'Employee' in entities_data['entities']:
        employee_entity = entities_data['entities']['Employee']
        
        # Check if relationships were parsed
        if 'relationships' in employee_entity:
            logger.info("\nEmployee relationships:")
            for rel_name, rel_def in employee_entity['relationships'].items():
                logger.info(f"\n  - Relationship: {rel_name}")
                logger.info(f"    - Type: {rel_def.get('type', 'N/A')}")
                logger.info(f"    - Entity: {rel_def.get('entity', 'N/A')}")
                logger.info(f"    - Source Attribute: {rel_def.get('source_attribute', 'N/A')}")
                logger.info(f"    - Target Attribute: {rel_def.get('target_attribute', 'N/A')}")
                
                # Check if relationship properties were parsed
                if 'properties' in rel_def:
                    logger.info(f"    - Properties:")
                    for prop_name, prop_value in rel_def['properties'].items():
                        logger.info(f"      - {prop_name}: {prop_value}")
                else:
                    logger.info(f"    - No properties found for this relationship")
        else:
            logger.error("No relationships found in Employee entity")
    else:
        logger.error("Employee entity not found in parsed data")
    
    # Deploy entities to the database
    schema_name = 'workflow_temp'
    
    # Check if the entity_relationships table has the necessary columns
    success, messages, result = execute_query(
        """
        SELECT column_name
        FROM information_schema.columns 
        WHERE table_schema = %s
        AND table_name = 'entity_relationships'
        AND column_name IN ('on_delete', 'on_update', 'foreign_key_type')
        """,
        (schema_name,),
        schema_name
    )
    
    if success and result:
        logger.info("Relationship property columns found in entity_relationships table:")
        for row in result:
            logger.info(f"  - Column: {row[0]}")
    else:
        logger.warning("Relationship property columns not found in entity_relationships table")
        logger.info("Adding relationship property columns to entity_relationships table...")
        
        # Add on_delete column if it doesn't exist
        success, messages, result = execute_query(
            """
            SELECT column_name
            FROM information_schema.columns 
            WHERE table_schema = %s
            AND table_name = 'entity_relationships'
            AND column_name = 'on_delete'
            """,
            (schema_name,),
            schema_name
        )
        
        if not success or not result:
            logger.info("Adding on_delete column...")
            success, messages, result = execute_query(
                f"ALTER TABLE {schema_name}.entity_relationships ADD COLUMN on_delete VARCHAR(50)",
                schema_name=schema_name
            )
            
            if not success:
                logger.error(f"Failed to add on_delete column: {messages}")
            else:
                logger.info("Added on_delete column successfully")
        
        # Add on_update column if it doesn't exist
        success, messages, result = execute_query(
            """
            SELECT column_name
            FROM information_schema.columns 
            WHERE table_schema = %s
            AND table_name = 'entity_relationships'
            AND column_name = 'on_update'
            """,
            (schema_name,),
            schema_name
        )
        
        if not success or not result:
            logger.info("Adding on_update column...")
            success, messages, result = execute_query(
                f"ALTER TABLE {schema_name}.entity_relationships ADD COLUMN on_update VARCHAR(50)",
                schema_name=schema_name
            )
            
            if not success:
                logger.error(f"Failed to add on_update column: {messages}")
            else:
                logger.info("Added on_update column successfully")
        
        # Add foreign_key_type column if it doesn't exist
        success, messages, result = execute_query(
            """
            SELECT column_name
            FROM information_schema.columns 
            WHERE table_schema = %s
            AND table_name = 'entity_relationships'
            AND column_name = 'foreign_key_type'
            """,
            (schema_name,),
            schema_name
        )
        
        if not success or not result:
            logger.info("Adding foreign_key_type column...")
            success, messages, result = execute_query(
                f"ALTER TABLE {schema_name}.entity_relationships ADD COLUMN foreign_key_type VARCHAR(50)",
                schema_name=schema_name
            )
            
            if not success:
                logger.error(f"Failed to add foreign_key_type column: {messages}")
            else:
                logger.info("Added foreign_key_type column successfully")
    
    # Deploy entities to the database
    success, messages = deploy_entities(entities_data, schema_name)
    
    logger.info("Deployment messages:")
    for message in messages:
        logger.info(f"  - {message}")
    
    if success:
        logger.info("Entity deployment successful")
    else:
        logger.error("Entity deployment failed")
    
    # Verify the relationships were created
    success, messages, result = execute_query(
        f"""
        SELECT 
            er.id, 
            s.name as source_entity, 
            t.name as target_entity, 
            er.relationship_type, 
            sa.name as source_attribute, 
            ta.name as target_attribute,
            er.on_delete,
            er.on_update,
            er.foreign_key_type
        FROM 
            {schema_name}.entity_relationships er
            JOIN {schema_name}.entities s ON er.source_entity_id = s.entity_id
            JOIN {schema_name}.entities t ON er.target_entity_id = t.entity_id
            JOIN {schema_name}.entity_attributes sa ON er.source_attribute_id = sa.attribute_id
            JOIN {schema_name}.entity_attributes ta ON er.target_attribute_id = ta.attribute_id
        WHERE 
            (s.name = 'Employee' AND t.name = 'Department') OR
            (s.name = 'Employee' AND t.name = 'Employee')
        """,
        schema_name=schema_name
    )
    
    if success and result:
        logger.info("\nRelationships in the database:")
        for row in result:
            logger.info(f"\n  - ID: {row[0]}")
            logger.info(f"    - Source Entity: {row[1]}")
            logger.info(f"    - Target Entity: {row[2]}")
            logger.info(f"    - Relationship Type: {row[3]}")
            logger.info(f"    - Source Attribute: {row[4]}")
            logger.info(f"    - Target Attribute: {row[5]}")
            logger.info(f"    - ON DELETE: {row[6]}")
            logger.info(f"    - ON UPDATE: {row[7]}")
            logger.info(f"    - Foreign Key Type: {row[8]}")
    else:
        logger.error("No relationships found in the database")

if __name__ == "__main__":
    test_parse_and_deploy_relationships()
