# pipeline_runner.py

import yaml
from pymongo import MongoClient
import psycopg2
import logging
from datetime import datetime
import os

from validate_yaml import validate_yaml
from id_generator import enrich_ids
from save_to_mongo import save_enriched_yaml
from insert_generator import generate_inserts_from_drafts
from db_connection import get_connection
from db_inserter import db_inserts_from_drafts

MONGO_URI = "mongodb://localhost:27017/"
MONGO_DB = "workflow_system"
MONGO_COLLECTION = "workflow"

log_dir = "logs"
os.makedirs(log_dir, exist_ok=True)

def run_pipeline_from_text(yaml_text: str) -> tuple[bool, str]:
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    log_file = f"pipeline_run_log_{timestamp}.log"
    log_path = os.path.join(log_dir, log_file)

    logging.basicConfig(
        filename=log_path,
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )

    try:
        logging.info("✅ Starting pipeline execution.")

        # Step 1: Validate YAML
        validation_result = validate_yaml(yaml_text)
        if not validation_result["valid"]:
            logging.error(f"❌ YAML validation failed: {validation_result['error']}")
            return False, validation_result["error"]
        logging.info("✅ YAML validation passed.")

        # Step 2: Enrich IDs
        conn = get_connection()
        enriched_yaml = enrich_ids(yaml_text, conn)
        logging.info("✅ ID enrichment completed.")
        
        # Save enriched YAML to file for inspection
        enriched_file_path = "/home/<USER>/workflow-system/runtime/workflow-engine/yamls/enriched_output.yaml"
        with open(enriched_file_path, "w") as f:
            yaml.dump(enriched_yaml, f, sort_keys=False)
        logging.info(f"✅ Enriched YAML saved to file: {enriched_file_path}")

        # Step 3: Delete existing drafts
        client = MongoClient(MONGO_URI)
        db = client[MONGO_DB]
        collection = db[MONGO_COLLECTION]
        deleted = collection.delete_many({"status": "draft"})
        logging.info(f"🧹 Deleted {deleted.deleted_count} old draft(s) from MongoDB.")

        # Step 4: Save enriched YAML
        save_enriched_yaml(enriched_yaml)
        logging.info("✅ Enriched YAML saved to MongoDB.")

        # Step 5: Generate design-time inserts
        success = generate_inserts_from_drafts()
        if not success:
            logging.error("❌ Failed to generate inserts.")
            return False, "Insert generation failed"
        logging.info("✅ Design-time inserts generated.")

        # Step 6: Runtime data inserts
        db_inserts_from_drafts()
        logging.info("✅ Runtime data inserted.")

        # Step 7: Update current doc as published
        tenant_id = enriched_yaml.get("workflow_data", {}).get("tenant", {}).get("id")
        if tenant_id:
            updated = collection.update_many(
                {"workflow_data.tenant.id": tenant_id},
                {"$set": {"status": "published"}}
            )
            logging.info(f"✅ MongoDB status updated: {updated.modified_count} doc(s) marked as published.")
        else:
            logging.warning("⚠️ No tenant ID found in workflow_data. Skipping MongoDB status update.")

        logging.info("🏁 Pipeline completed successfully.")
        return True, f"Pipeline executed successfully. Log: {log_path}"

    except Exception as e:
        logging.exception(f"❌ Pipeline crashed: {e}")
        return False, str(e)


# def run_pipeline_from_text(yaml_text: str) -> tuple[bool, str]:
#     timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
#     log_file = f"pipeline_run_log_{timestamp}.log"
#     log_path = os.path.join(log_dir, log_file)

#     logging.basicConfig(
#         filename=log_path,
#         level=logging.INFO,
#         format='%(asctime)s - %(levelname)s - %(message)s'
#     )

#     try:
#         logging.info("✅ Starting pipeline execution.")

#         # Step 1: Validate YAML
#         validation_result = validate_yaml(yaml_text)
#         if not validation_result["valid"]:
#             logging.error(f"❌ YAML validation failed: {validation_result['error']}")
#             return False, validation_result["error"]

#         logging.info("✅ YAML validation passed.")

#         # Step 2: Enrich IDs
#         conn = get_connection()
#         enriched_yaml = enrich_ids(yaml_text, conn)
#         logging.info("✅ ID enrichment completed.")

#         # Step 3: Save to MongoDB
#         save_enriched_yaml(enriched_yaml)
#         logging.info("✅ Enriched YAML saved to MongoDB.")

#         # Step 4: Generate Inserts
#         success = generate_inserts_from_drafts()
#         if not success:
#             logging.error("❌ Failed to generate design-time inserts.")
#             return False, "Insert generation failed"

#         logging.info("✅ Design-time inserts generated.")

#         # Step 5: Runtime Execution
#         workflow_data = enriched_yaml.get("workflow_data", {})
#         if not workflow_data:
#             logging.error("❌ No workflow_data found in YAML.")
#             return False, "Missing workflow_data block"

#        # process_workflow_data_for_runtime(workflow_data)
#         logging.info("✅ Runtime data inserted.")
#         db_inserts_from_drafts()
#         # Step 6: Update MongoDB status
#         try:
#             client = MongoClient(MONGO_URI)
#             db = client[MONGO_DB]
#             collection = db[MONGO_COLLECTION]
#             tenant_id = workflow_data.get("tenant", {}).get("id")

#             if tenant_id:
#                 result = collection.update_many(
#                     {"workflow_data.tenant.id": tenant_id, "status": "draft"},
#                     {"$set": {"status": "published"}}
#                 )
#                 logging.info(f"✅ MongoDB status updated: {result.modified_count} doc(s)")
#             else:
#                 logging.warning("⚠️ Tenant ID not found. Mongo status not updated.")
#         except Exception as e:
#             logging.error(f"❌ MongoDB update failed: {e}")

#         logging.info("✅ Pipeline completed successfully.")
#         return True, f"Pipeline executed successfully. Log: {log_path}"

#     except Exception as e:
#         logging.exception(f"❌ Pipeline crashed: {e}")
#         return False, str(e)
