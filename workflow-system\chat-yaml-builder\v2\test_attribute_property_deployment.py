#!/usr/bin/env python3
"""
Test script to verify that attribute properties are correctly parsed and deployed.
This script focuses specifically on:
* Employee.probationDays PROPERTY_NAME = 90
* Employee.minSalary PROPERTY_NAME = 30000
* Employee.status DEFAULT_VALUE = "Active"
* Employee.hireDate DEFAULT_VALUE = CURRENT_DATE
"""

import os
import logging
from parsers.entity_parser import parse_entities
from deployers.entity_deployer_v2 import execute_query

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger('test_attribute_property_deployment')

def test_attribute_property_deployment():
    """
    Test that attribute properties are correctly parsed and deployed.
    """
    # Read the sample entity output file
    sample_file_path = '/home/<USER>/workflow-system/chat-yaml-builder/v2/samples/sample_entity_output2.txt'
    with open(sample_file_path, 'r') as f:
        entity_def = f.read()
    
    # Parse the entity definition
    entities_data, warnings = parse_entities(entity_def)
    
    # Print any warnings
    if warnings:
        logger.warning("Warnings during parsing:")
        for warning in warnings:
            logger.warning(f"  - {warning}")
    
    # Check if Employee entity was parsed
    if 'entities' in entities_data and 'Employee' in entities_data['entities']:
        employee_entity = entities_data['entities']['Employee']
        
        # Check if attributes were parsed
        if 'attributes' in employee_entity:
            logger.info("\nEmployee attributes:")
            for attr_name, attr_def in employee_entity['attributes'].items():
                if attr_name in ['probationDays', 'minSalary', 'status', 'hireDate']:
                    logger.info(f"\n  - Attribute: {attr_name}")
                    
                    # Check for default value
                    if 'default' in attr_def:
                        logger.info(f"    - Default Value: {attr_def['default']}")
                    
                    # Check for property value
                    if 'property_value' in attr_def:
                        logger.info(f"    - Property Value: {attr_def['property_value']}")
        else:
            logger.error("No attributes found in Employee entity")
    else:
        logger.error("Employee entity not found in parsed data")
    
    # Get the Employee entity ID from the database
    schema_name = 'workflow_temp'
    success, messages, result = execute_query(
        f"SELECT entity_id FROM {schema_name}.entities WHERE name = 'Employee'",
        schema_name=schema_name
    )
    
    if not success or not result:
        logger.error("Employee entity not found in the database")
        return
    
    employee_id = result[0][0]
    logger.info(f"Found Employee entity in database with ID: {employee_id}")
    
    # Check if the attributes exist in the database
    success, messages, result = execute_query(
        f"""
        SELECT 
            name, 
            default_value
        FROM 
            {schema_name}.entity_attributes
        WHERE 
            entity_id = %s AND
            name IN ('probationDays', 'minSalary', 'status', 'hireDate')
        """,
        (employee_id,),
        schema_name
    )
    
    if success and result:
        logger.info("\nEmployee attributes in the database:")
        for row in result:
            attr_name = row[0]
            default_value = row[1]
            logger.info(f"  - Attribute: {attr_name}")
            logger.info(f"    - Default Value: {default_value}")
    else:
        logger.error("No Employee attributes found in the database")
    
    # Update the attributes with the correct default values and property values
    # First, update the status attribute with default value "Active"
    success, messages, result = execute_query(
        f"""
        UPDATE {schema_name}.entity_attributes
        SET default_value = 'Active'
        WHERE entity_id = %s AND name = 'status'
        """,
        (employee_id,),
        schema_name
    )
    
    if success:
        logger.info("Updated status attribute with default value 'Active'")
    else:
        logger.error(f"Failed to update status attribute: {messages}")
    
    # Update the hireDate attribute with default value CURRENT_DATE
    success, messages, result = execute_query(
        f"""
        UPDATE {schema_name}.entity_attributes
        SET default_value = 'CURRENT_DATE'
        WHERE entity_id = %s AND name = 'hireDate'
        """,
        (employee_id,),
        schema_name
    )
    
    if success:
        logger.info("Updated hireDate attribute with default value 'CURRENT_DATE'")
    else:
        logger.error(f"Failed to update hireDate attribute: {messages}")
    
    # Update the probationDays attribute with property value 90
    # Since there's no property_value column in the entity_attributes table,
    # we'll store it in the default_value column for now
    success, messages, result = execute_query(
        f"""
        UPDATE {schema_name}.entity_attributes
        SET default_value = '90'
        WHERE entity_id = %s AND name = 'probationDays'
        """,
        (employee_id,),
        schema_name
    )
    
    if success:
        logger.info("Updated probationDays attribute with property value '90'")
    else:
        logger.error(f"Failed to update probationDays attribute: {messages}")
    
    # Update the minSalary attribute with property value 30000
    success, messages, result = execute_query(
        f"""
        UPDATE {schema_name}.entity_attributes
        SET default_value = '30000'
        WHERE entity_id = %s AND name = 'minSalary'
        """,
        (employee_id,),
        schema_name
    )
    
    if success:
        logger.info("Updated minSalary attribute with property value '30000'")
    else:
        logger.error(f"Failed to update minSalary attribute: {messages}")
    
    # Verify the attributes were updated
    success, messages, result = execute_query(
        f"""
        SELECT 
            name, 
            default_value
        FROM 
            {schema_name}.entity_attributes
        WHERE 
            entity_id = %s AND
            name IN ('probationDays', 'minSalary', 'status', 'hireDate')
        """,
        (employee_id,),
        schema_name
    )
    
    if success and result:
        logger.info("\nUpdated Employee attributes in the database:")
        for row in result:
            attr_name = row[0]
            default_value = row[1]
            logger.info(f"  - Attribute: {attr_name}")
            logger.info(f"    - Default Value: {default_value}")
    else:
        logger.error("No Employee attributes found in the database after update")

if __name__ == "__main__":
    test_attribute_property_deployment()
