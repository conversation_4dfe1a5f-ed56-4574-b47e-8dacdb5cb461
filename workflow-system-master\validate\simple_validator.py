#!/usr/bin/env python3
"""
Simple YAML Validator for Enterprise Workflow Configurations

A simplified validator for enterprise workflow YAML configurations
that focuses on basic structure and entity validation.
"""

import yaml
from enum import Enum
from typing import Dict, List, Any, Optional, Set
from dataclasses import dataclass
from collections import defaultdict


class ValidationLevel(Enum):
    """Validation issue severity levels."""
    ERROR = "ERROR"  # Critical issues that must be fixed
    WARNING = "WARNING"  # Potential issues that should be reviewed
    INFO = "INFO"  # Informational findings


@dataclass
class ValidationIssue:
    """Represents a validation issue found during YAML validation."""
    level: ValidationLevel
    message: str
    path: str
    context: Optional[Any] = None

    def __str__(self):
        return f"{self.level.value}: {self.path} - {self.message}"


class SimpleWorkflowValidator:
    """
    Simple validator for enterprise workflow YAML configurations.
    """
    
    def __init__(self):
        self.issues = []
        self.yaml_data = None
        
        # Initialize caches for cross-referencing validation
        self.entity_cache = {}
        self.attribute_cache = {}
        self.required_attributes_cache = {}
        
    def add_issue(self, level: ValidationLevel, message: str, path: str, context=None):
        """Add a validation issue to the issues list."""
        self.issues.append(ValidationIssue(level, message, path, context))

    def validate_file(self, file_path: str) -> List[ValidationIssue]:
        """Validate a YAML file by path."""
        try:
            with open(file_path, 'r') as f:
                yaml_content = f.read()
            
            try:
                return self.validate_yaml(yaml_content)
            except Exception as e:
                self.add_issue(ValidationLevel.ERROR, f"Error during validation: {str(e)}", "validation")
                return self.issues
        except Exception as e:
            self.add_issue(ValidationLevel.ERROR, f"Failed to read file: {str(e)}", "file")
            return self.issues

    def validate_yaml(self, yaml_content: str) -> List[ValidationIssue]:
        """Validate a YAML string."""
        self.issues = []
        
        try:
            self.yaml_data = yaml.safe_load(yaml_content)
        except yaml.YAMLError as e:
            self.add_issue(ValidationLevel.ERROR, f"Invalid YAML syntax: {str(e)}", "yaml_syntax")
            return self.issues
        
        # Start validation
        self._validate_basic_structure()
        if len([i for i in self.issues if i.level == ValidationLevel.ERROR]) > 0:
            # Don't continue if there are critical structure issues
            return self.issues
        
        # Build caches for cross-referencing
        self._build_caches()
        
        # Validate entities and attributes
        self._validate_entities_and_attributes()
        
        return self.issues
    
    def _build_caches(self):
        """Build internal caches for cross-referencing validation."""
        # Cache entities and attributes
        if "entities" in self.yaml_data:
            for entity in self.yaml_data["entities"]:
                if "id" in entity:
                    self.entity_cache[entity["id"]] = entity
                    
                    # Cache required attributes for this entity
                    if "attributes_metadata" in entity and "required_attributes" in entity["attributes_metadata"]:
                        self.required_attributes_cache[entity["id"]] = set(entity["attributes_metadata"]["required_attributes"])
                    
                    # Process attributes
                    if "attributes" in entity:
                        for attr in entity["attributes"]:
                            if "id" in attr:
                                attr_key = f"{entity['id']}.{attr['id']}"
                                self.attribute_cache[attr_key] = attr

    def _validate_basic_structure(self):
        """Validate the basic structure of the YAML."""
        # Check required top-level sections
        required_sections = ["tenant", "workflow_data", "permission_types", "entities", "global_objectives", "local_objectives"]
        for section in required_sections:
            if section not in self.yaml_data:
                self.add_issue(ValidationLevel.ERROR, f"Missing required top-level section: {section}", f"root.{section}")
        
        # Validate tenant section
        if "tenant" in self.yaml_data:
            tenant = self.yaml_data["tenant"]
            if "id" not in tenant:
                self.add_issue(ValidationLevel.ERROR, "Missing tenant ID", "tenant.id")
            elif not tenant["id"].lower() == tenant["id"]:
                self.add_issue(ValidationLevel.ERROR, "Tenant ID must be lowercase", "tenant.id")
            
            if "name" not in tenant:
                self.add_issue(ValidationLevel.ERROR, "Missing tenant name", "tenant.name")
            
            if "roles" not in tenant or not isinstance(tenant["roles"], list):
                self.add_issue(ValidationLevel.ERROR, "Missing or invalid tenant roles section", "tenant.roles")
        
        # Validate permission_types
        if "permission_types" in self.yaml_data:
            permission_types = self.yaml_data["permission_types"]
            if not isinstance(permission_types, list):
                self.add_issue(ValidationLevel.ERROR, "Permission types must be a list", "permission_types")

    def _validate_entities_and_attributes(self):
        """Validate entities and their attributes."""
        if "entities" not in self.yaml_data:
            return
        
        # Track entity and attribute usage for comprehensive validation
        entity_usage = {entity["id"]: {"referenced": False, "attributes_used": set()} 
                        for entity in self.yaml_data["entities"] if "id" in entity}
        
        # First pass: validate entity definitions
        for i, entity in enumerate(self.yaml_data["entities"]):
            entity_path = f"entities[{i}]"
            
            # Validate entity basic fields
            required_entity_fields = ["id", "name", "type", "version", "status", "attributes_metadata", "attributes"]
            for field in required_entity_fields:
                if field not in entity:
                    self.add_issue(ValidationLevel.ERROR, f"Missing required entity field: {field}", f"{entity_path}.{field}")
            
            # Validate entity ID format
            if "id" in entity and not entity["id"].lower() == entity["id"]:
                self.add_issue(ValidationLevel.ERROR, "Entity ID must be lowercase", f"{entity_path}.id")
            
            # Validate entity name (no spaces for internal name)
            if "name" in entity and " " in entity["name"]:
                self.add_issue(ValidationLevel.ERROR, "Entity name cannot contain spaces (use camelCase)", f"{entity_path}.name")
            
            # Validate attributes_metadata
            if "attributes_metadata" in entity:
                metadata = entity["attributes_metadata"]
                metadata_path = f"{entity_path}.attributes_metadata"
                
                required_metadata_fields = ["attribute_prefix", "attribute_map", "required_attributes"]
                for field in required_metadata_fields:
                    if field not in metadata:
                        self.add_issue(ValidationLevel.ERROR, f"Missing required attributes_metadata field: {field}", f"{metadata_path}.{field}")
                
                # Validate attribute_map
                if "attribute_map" in metadata and isinstance(metadata["attribute_map"], dict):
                    for attr_id, attr_name in metadata["attribute_map"].items():
                        if " " in attr_name:
                            self.add_issue(ValidationLevel.ERROR, f"Attribute internal name '{attr_name}' contains spaces (use camelCase)", f"{metadata_path}.attribute_map.{attr_id}")
                
                # Validate required_attributes
                if "required_attributes" in metadata and "attribute_map" in metadata:
                    for req_attr in metadata["required_attributes"]:
                        if req_attr not in metadata["attribute_map"]:
                            self.add_issue(ValidationLevel.ERROR, f"Required attribute {req_attr} not defined in attribute_map", f"{metadata_path}.required_attributes")
            
            # Validate attributes
            if "attributes" in entity:
                # Check for attribute definition completeness and consistency
                attribute_ids = set()
                attribute_names = set()
                
                for j, attr in enumerate(entity["attributes"]):
                    attr_path = f"{entity_path}.attributes[{j}]"
                    
                    # Required attribute fields
                    required_attr_fields = ["id", "name", "display_name", "datatype", "required", "version", "status"]
                    for field in required_attr_fields:
                        if field not in attr:
                            self.add_issue(ValidationLevel.ERROR, f"Missing required attribute field: {field}", f"{attr_path}.{field}")
                    
                    # Validate attribute ID format
                    if "id" in attr:
                        if not attr["id"].lower() == attr["id"]:
                            self.add_issue(ValidationLevel.ERROR, "Attribute ID must be lowercase", f"{attr_path}.id")
                        
                        # Check for duplicate IDs
                        if attr["id"] in attribute_ids:
                            self.add_issue(ValidationLevel.ERROR, f"Duplicate attribute ID: {attr['id']}", f"{attr_path}.id")
                        attribute_ids.add(attr["id"])
                    
                    # Validate attribute name (no spaces for internal name)
                    if "name" in attr:
                        if " " in attr["name"]:
                            self.add_issue(ValidationLevel.ERROR, "Attribute name cannot contain spaces (use camelCase)", f"{attr_path}.name")
                        
                        # Check for duplicate names
                        if attr["name"] in attribute_names:
                            self.add_issue(ValidationLevel.ERROR, f"Duplicate attribute name: {attr['name']}", f"{attr_path}.name")
                        attribute_names.add(attr["name"])
                    
                    # Validate enum attributes
                    if "datatype" in attr and attr["datatype"].lower() == "enum" and "values" not in attr:
                        self.add_issue(ValidationLevel.ERROR, "Enum attribute must define values array", f"{attr_path}.values")
                    
                    # Check if required is a boolean
                    if "required" in attr and not isinstance(attr["required"], bool):
                        self.add_issue(ValidationLevel.ERROR, "Required field must be a boolean", f"{attr_path}.required")
                
                # Verify all attributes in metadata.attribute_map have definitions
                if "attributes_metadata" in entity and "attribute_map" in entity["attributes_metadata"]:
                    attribute_map = entity["attributes_metadata"]["attribute_map"]
                    for attr_id in attribute_map.keys():
                        if attr_id not in attribute_ids:
                            self.add_issue(ValidationLevel.ERROR, 
                                          f"Attribute ID {attr_id} in attribute_map doesn't have a definition", 
                                          f"{entity_path}.attributes_metadata.attribute_map")
        
        # Second pass: check entity usage in Local Objectives
        if "local_objectives" in self.yaml_data:
            for lo in self.yaml_data["local_objectives"]:
                # Check input_stack for entity references
                if "input_stack" in lo and "inputs" in lo["input_stack"]:
                    for inp in lo["input_stack"]["inputs"]:
                        if "slot_id" in inp:
                            parts = inp["slot_id"].split(".")
                            if len(parts) >= 2 and parts[0].startswith("e") and parts[1].startswith("at"):
                                entity_id = parts[0]
                                attr_id = parts[1]
                                
                                # Skip information fields
                                if attr_id == "at999" or inp.get("source", {}).get("type") == "information":
                                    continue
                                    
                                if entity_id in entity_usage:
                                    entity_usage[entity_id]["referenced"] = True
                                    entity_usage[entity_id]["attributes_used"].add(attr_id)
                
                # Check output_stack for entity references
                if "output_stack" in lo and "outputs" in lo["output_stack"]:
                    for out in lo["output_stack"]["outputs"]:
                        if "slot_id" in out and not out["slot_id"].startswith("executionstatus"):
                            parts = out["slot_id"].split(".")
                            if len(parts) >= 2 and parts[0].startswith("e") and parts[1].startswith("at"):
                                entity_id = parts[0]
                                attr_id = parts[1]
                                
                                if entity_id in entity_usage:
                                    entity_usage[entity_id]["referenced"] = True
                                    entity_usage[entity_id]["attributes_used"].add(attr_id)
        
        # Report unused entities and attributes
        for entity_id, usage in entity_usage.items():
            if not usage["referenced"]:
                self.add_issue(ValidationLevel.WARNING, f"Entity {entity_id} is defined but never used in any Local Objective", f"entities.{entity_id}")
            else:
                # Check for unused attributes
                all_attrs = set()
                if entity_id in self.entity_cache and "attributes" in self.entity_cache[entity_id]:
                    all_attrs = {attr["id"] for attr in self.entity_cache[entity_id]["attributes"] if "id" in attr}
                
                unused_attrs = all_attrs - usage["attributes_used"]
                if unused_attrs:
                    attrs_str = ", ".join(unused_attrs)
                    self.add_issue(ValidationLevel.INFO, f"Entity {entity_id} has unused attributes: {attrs_str}", f"entities.{entity_id}")
                
                # Check if all required attributes are used
                if entity_id in self.required_attributes_cache:
                    required_attrs = self.required_attributes_cache[entity_id]
                    unused_required = required_attrs - usage["attributes_used"]
                    
                    if unused_required:
                        attrs_str = ", ".join(unused_required)
                        self.add_issue(ValidationLevel.WARNING, 
                                      f"Entity {entity_id} has required attributes that are never used: {attrs_str}", 
                                      f"entities.{entity_id}")


# Add a main function to test the validator
if __name__ == "__main__":
    import sys
    
    # Create validator instance
    validator = SimpleWorkflowValidator()
    
    # If a file path is provided as an argument, use it; otherwise use the default example
    file_path = sys.argv[1] if len(sys.argv) > 1 else "validate/example_workflow.yaml"
    
    print(f"Validating file: {file_path}")
    issues = validator.validate_file(file_path)
    
    # Print validation results
    print(f"Validation complete. Found {len(issues)} issues:")
    for issue in issues:
        print(f"{issue.level.value}: {issue.path} - {issue.message}")
