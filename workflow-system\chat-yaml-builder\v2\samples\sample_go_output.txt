## Leave Approval Process

Core Metadata:
- name: "Process Leave Requests"
- version: "1.0"
- status: "Active"
- description: "Manages employee leave requests from submission to approval or rejection"
- primary_entity: "LeaveApplication"
- classification: "HR Process"
  - Global Objective: "Leave Request Processing"
  - Book: "Employee Leave Management"
  - Chapter: "Leave Request Lifecycle"
  - Tenant: "Acme Corporation"

Process Ownership:
- Originator: Employee
- Process Owner: HR Manager
- Business Sponsor: Human Resources Department

Trigger Definition:
- Trigger Type: event-driven
- Trigger Condition: Employee submits leave request
- Trigger Frequency: on-demand
- Trigger Parameters: employeeId, startDate, endDate, leaveTypeId

Data Management:

Input Stack:
- Employee with employeeId*, firstName, lastName, email, departmentId, managerId; LeaveType with typeId*, name, maxDuration, requiresDocumentation (true, false).

Input Mapping Stack:
- Employee.employeeId maps to SubmitLeaveRequest → LeaveApplication.employeeId
- Employee.firstName maps to SubmitLeaveRequest → LeaveApplication.employeeName
- Employee.departmentId maps to SubmitLeaveRequest → LeaveApplication.departmentId
- Employee.managerId maps to SubmitLeaveRequest → LeaveApplication.managerId
- Employee.email maps to SubmitLeaveRequest → LeaveApplication.notificationEmail
- LeaveType.typeId maps to SubmitLeaveRequest → LeaveApplication.leaveTypeId
- LeaveType.name maps to SubmitLeaveRequest → LeaveApplication.leaveTypeName
- LeaveType.maxDuration maps to SubmitLeaveRequest → LeaveApplication.maxAllowedDays
- LeaveType.requiresDocumentation maps to SubmitLeaveRequest → LeaveApplication.requiresDocumentation

Output Stack:
- LeaveApplication with leaveId*, employeeId*, startDate*, endDate*, numDays*, status* (Pending, Approved, Rejected), approvedBy, approvalDate, comments.

Output Mapping Stack:
- LeaveApplication.leaveId maps to "Employee Calendar Management" → CalendarEvent.referenceId
- LeaveApplication.employeeId maps to "Employee Calendar Management" → CalendarEvent.employeeId
- LeaveApplication.startDate maps to "Employee Calendar Management" → CalendarEvent.startDate
- LeaveApplication.endDate maps to "Employee Calendar Management" → CalendarEvent.endDate

Data Constraints:

DB Stack:
- LeaveApplication.leaveId (string) is mandatory and unique. Error message: "Leave ID is required and must be unique"
- LeaveApplication.employeeId (string) is mandatory and must exist in Employee table. Error message: "Employee ID is required"
- LeaveApplication.startDate (date) is mandatory and must be a valid date. Error message: "Start date is required"
- LeaveApplication.endDate (date) is mandatory and must be after startDate. Error message: "End date must be after start date"
- LeaveApplication.numDays (number) is mandatory and must be greater than 0. Error message: "Number of days must be greater than 0"
- LeaveApplication.status (enum) must be one of "Pending", "Approved", "Rejected". Error message: "Invalid status value"

Process Definition:

Process Flow:
1. SubmitLeaveRequest [HUMAN] - Employee submits a leave request
   a. If LeaveApplication.requiresDocumentation = true, route to UploadDocumentation
   b. If LeaveApplication.requiresDocumentation = false, route to ReviewLeaveRequest
2. UploadDocumentation [HUMAN] - Employee uploads required documentation
   a. Route to ReviewLeaveRequest
3. ReviewLeaveRequest [HUMAN] - Manager reviews the leave request
   a. If LeaveApplication.status = "Approved", route to ApproveLeaveRequest
   b. If LeaveApplication.status = "Rejected", route to RejectLeaveRequest
4. ApproveLeaveRequest [SYSTEM] - System updates leave request status to approved
   a. Route to NotifyEmployee
5. RejectLeaveRequest [SYSTEM] - System updates leave request status to rejected
   a. Route to NotifyEmployee
6. NotifyEmployee [SYSTEM] - System notifies employee of the decision
   a. Route to UpdateCalendar
7. UpdateCalendar [SYSTEM] - System updates employee calendar
   a. If LeaveApplication.status = "Approved", route to UpdateLeaveBalance
   b. If LeaveApplication.status = "Rejected", route to Terminal
8. UpdateLeaveBalance [SYSTEM] - System updates employee leave balance
   a. Route to Terminal
9. CancelLeaveRequest [HUMAN] - Employee cancels a previously submitted leave request
   a. Route to RollbackLeaveApproval
10. RollbackLeaveApproval [SYSTEM] - System rolls back leave approval
    a. Route to RestoreLeaveBalance
11. RestoreLeaveBalance [SYSTEM] - System restores employee leave balance
    a. Route to NotifyCancellation
12. NotifyCancellation [SYSTEM] - System notifies manager of cancellation
    a. Route to Terminal
13. LogAuditTrail [SYSTEM] - System logs audit trail for compliance
    a. Route to Terminal

Parallel Flows:
- After NotifyEmployee:
  * UpdateCalendar - System updates employee calendar
  * LogAuditTrail - System logs audit trail for compliance
- Join at: Terminal

Rollback Pathways:
- SubmitLeaveRequest ↔ CancelLeaveRequest
- ApproveLeaveRequest ↔ RollbackLeaveApproval
- UpdateLeaveBalance ↔ RestoreLeaveBalance
- Full rollback pathway: CancelLeaveRequest → RollbackLeaveApproval → RestoreLeaveBalance → NotifyCancellation

Business Rules:
1. Leave requests must be submitted at least 7 days in advance for planned leave - Enforced by SubmitLeaveRequest
2. Sick leave can be submitted retroactively within 3 days - Enforced by SubmitLeaveRequest
3. Leave requests exceeding 5 consecutive days require manager approval - Enforced by ReviewLeaveRequest
4. Documentation is required for sick leave exceeding 3 days - Enforced by SubmitLeaveRequest and UploadDocumentation
5. Leave balance must be sufficient for the requested leave duration - Validated by SubmitLeaveRequest
6. Overlapping leave requests are flagged for manager review - Implemented by ReviewLeaveRequest
7. Cancellations must be made at least 24 hours in advance - Enforced by CancelLeaveRequest

Integration Points:

GO Relationships:
- GO sends output to "Employee Calendar Management" for CalendarEvent processing via UpdateCalendar.

External Systems:
- HR System: Two-way integration for employee data and leave balance verification
- Calendar System: Outbound integration for employee calendar updates
- Notification System: Outbound integration for email and mobile notifications

Performance Metadata:
- cycle_time: "2 business days"
- number_of_pathways: 4
- volume_metrics:
  * average_volume: 120
  * peak_volume: 250
  * unit: "requests/month"
- sla_thresholds:
  * manager_review: "1 business day"
  * notification: "1 hour"
  * system_processing: "5 minutes"
- critical_lo_performance:
  * "ReviewLeaveRequest": "24 hours maximum"
  * "NotifyEmployee": "1 hour maximum"
  * "UpdateCalendar": "5 minutes maximum"
  * "UpdateLeaveBalance": "5 minutes maximum"

Process Mining Schema:

Event Log Specification:
- case_id: "LeaveApplication.leaveId"
- activity: "LO_name"
- event_type: "start/complete/abort/rollback"
- timestamp: "ISO-8601 datetime"
- resource: "role/system_executing"
- duration: "milliseconds"
- attributes:
  * entity_state: "json_snapshot"
  * input_values: "input_parameters"
  * output_values: "output_results"
  * execution_status: "success/failure/pending"
  * error_details: "error_message_if_any"

Performance Discovery Metrics:
- pathway_frequency:
  * "Standard Approval":
    - frequency: 95
    - percentage: 79
    - average_duration: "1.5 days"
    - success_rate: 98
  * "Documentation Required":
    - frequency: 25
    - percentage: 21
    - average_duration: "2.8 days"
    - success_rate: 92
- bottleneck_analysis:
  * "ReviewLeaveRequest":
    - average_wait_time: "1.2 days"
    - queue_length: 8
    - resource_utilization: 75
    - failure_rate: 5
- resource_patterns:
  * "HR Manager":
    - active_hours: "9:00-17:00 business days"
    - peak_load_periods: "Monday mornings, month-end"
    - concurrent_executions: 12
  * "Notification System":
    - active_hours: "24/7"
    - peak_load_periods: "8:00-10:00 workdays"
    - concurrent_executions: 50

Conformance Analytics:
- compliance_rate: 96
- execution_variance:
  * "ReviewLeaveRequest":
    - expected_duration: "4 hours"
    - actual_duration_range: "2-24 hours"
    - variance_causes: ["manager availability", "high request volume", "incomplete documentation"]
  * "NotifyEmployee":
    - expected_duration: "10 minutes"
    - actual_duration_range: "5-25 minutes"
    - variance_causes: ["notification system latency", "email delivery issues"]
- exception_patterns:
  * "input_timeout":
    - frequency: 6
    - affected_pathways: ["Documentation Required"]
    - recovery_success_rate: 85
  * "validation_failure":
    - frequency: 12
    - most_common_failures: ["insufficient leave balance", "invalid date range"]
    - resolution_time: "1.5 days"
  * "system_error":
    - frequency: 2
    - error_categories: ["calendar integration", "notification delivery"]
    - automatic_recovery_rate: 92

Advanced Process Intelligence:
- process_health_score:
  * performance_score: 88
  * compliance_score: 96
  * efficiency_score: 84
  * overall_health: 89
- prediction_models:
  * completion_time_forecast:
    - algorithm: "Random Forest Regression"
    - accuracy: 87
    - confidence_interval: "±0.4 days"
  * failure_prediction:
    - algorithm: "Gradient Boosting Classification"
    - precision: 82
    - recall: 79
- optimization_insights:
  * bottleneck_elimination: ["Implement auto-approval for standard requests under 2 days", "Expand manager delegation options"]
  * resource_reallocation: ["Add review capacity during Monday mornings", "Implement notification batching"]
  * pathway_optimization: ["Streamline documentation review process", "Implement one-click approvals for recurring requests"]

Rollback Analytics:
- rollback_frequency: 4
- rollback_success_rate: 99
- rollback_triggers:
  * "user_initiated":
    - frequency: 18
    - average_impact_scope: 2
    - average_recovery_time: "2 hours"
  * "system_error":
    - frequency: 2
    - average_impact_scope: 1
    - average_recovery_time: "1 hour"
- rollback_pathways:
  * "leave_cancellation":
    - frequency: 16
    - success_rate: 99
    - average_completion_time: "2.5 hours"
  * "system_initiated_rollback":
    - frequency: 4
    - success_rate: 95
    - average_completion_time: "1.2 hours"

Sample Data:
- LeaveApplication.leaveId: "LV-2023-0001", "LV-2023-0002", "LV-2023-0003"
- LeaveApplication.employeeId: "EMP001", "EMP002", "EMP003"
- LeaveApplication.startDate: "2023-06-15", "2023-07-01", "2023-08-10"
- LeaveApplication.endDate: "2023-06-16", "2023-07-05", "2023-08-17"
- LeaveApplication.numDays: 2, 5, 8
- LeaveApplication.status: "Pending", "Approved", "Rejected"
- LeaveApplication.leaveTypeId: "LT001", "LT002", "LT003"
- LeaveApplication.leaveTypeName: "Annual Leave", "Sick Leave", "Personal Leave"

Validation Checklist for GO Creation:

- [x] All LOs are defined with natural language names
- [x] Every referenced LO is defined in Process Flow section
- [x] All routing references appropriate LO names
- [x] First LO is human-initiated
- [x] All input mappings use natural language LO names
- [x] All output mappings use natural language GO names
- [x] Business rules reference specific LO names
- [x] Rollback LOs are included in Process Flow
- [x] Parallel flows reference defined LOs
- [x] All pathways terminate in completion or cancellation
- [x] Input Stack contains only aggregate/post-process data
- [x] Input Mapping shows where data enters LOs
- [x] Output Stack contains only final GO outputs
- [x] Output Mapping shows where data goes to other GOs
- [x] No function calls in Input/Output stacks
- [x] DB Stack has proper constraints and error messages
- [x] GO relationships specify exact entities
- [x] External systems specify integration direction
- [x] All integration points map to specific LOs
- [x] Process mining schema captures all events
- [x] Rollback analytics are included
- [x] Cycle time is specified
- [x] Number of pathways is counted correctly
- [x] Volume metrics are realistic
- [x] All critical LOs have performance targets
- [x] SLA thresholds are defined

## Employee Calendar Management

Core Metadata:
- name: "Employee Calendar Management"
- version: "1.0"
- status: "Active"
- description: "Manages employee calendar events including leave, meetings, and other appointments"
- primary_entity: "CalendarEvent"
- classification: "HR Process"
  - Global Objective: "Employee Calendar Management"
  - Book: "Employee Leave Management"
  - Chapter: "Calendar Management"
  - Tenant: "Acme Corporation"

Process Ownership:
- Originator: System
- Process Owner: HR Manager
- Business Sponsor: Human Resources Department

Trigger Definition:
- Trigger Type: event-driven
- Trigger Condition: Leave request approved or cancelled
- Trigger Frequency: on-demand
- Trigger Parameters: leaveId, employeeId, startDate, endDate, status

Data Management:

Input Stack:
- LeaveApplication with leaveId*, employeeId*, startDate*, endDate*, status* (Approved, Rejected).

Input Mapping Stack:
- LeaveApplication.leaveId maps to CreateCalendarEvent → CalendarEvent.referenceId
- LeaveApplication.employeeId maps to CreateCalendarEvent → CalendarEvent.employeeId
- LeaveApplication.startDate maps to CreateCalendarEvent → CalendarEvent.startDate
- LeaveApplication.endDate maps to CreateCalendarEvent → CalendarEvent.endDate
- LeaveApplication.status maps to CreateCalendarEvent → CalendarEvent.status

Output Stack:
- CalendarEvent with eventId*, employeeId*, eventType*, startDate*, endDate*, title*, description, status* (Scheduled, Cancelled).

Output Mapping Stack:
- CalendarEvent.eventId maps to "Leave Approval Process" → LeaveApplication.calendarEventId
- CalendarEvent.status maps to "Leave Approval Process" → LeaveApplication.calendarStatus

Data Constraints:

DB Stack:
- CalendarEvent.eventId (string) is mandatory and unique. Error message: "Event ID is required and must be unique"
- CalendarEvent.employeeId (string) is mandatory and must exist in Employee table. Error message: "Employee ID is required"
- CalendarEvent.startDate (date) is mandatory and must be a valid date. Error message: "Start date is required"
- CalendarEvent.endDate (date) is mandatory and must be after startDate. Error message: "End date must be after start date"
- CalendarEvent.title (string) is mandatory. Error message: "Title is required"
- CalendarEvent.status (enum) must be one of "Scheduled", "Cancelled". Error message: "Invalid status value"

Process Definition:

Process Flow:
1. CreateCalendarEvent [SYSTEM] - System creates a calendar event
   a. Route to NotifyEmployee
2. NotifyEmployee [SYSTEM] - System notifies employee of the calendar event
   a. Route to Terminal
3. CancelCalendarEvent [SYSTEM] - System cancels a calendar event
   a. Route to NotifyCancellation
4. NotifyCancellation [SYSTEM] - System notifies employee of the cancellation
   a. Route to Terminal

Rollback Pathways:
- CreateCalendarEvent ↔ CancelCalendarEvent
- Full rollback pathway: CancelCalendarEvent → NotifyCancellation

Business Rules:
1. Calendar events must not overlap with existing events - Enforced by CreateCalendarEvent
2. Calendar events must be created at least 1 hour in advance - Enforced by CreateCalendarEvent
3. Cancellations must be made at least 1 hour in advance - Enforced by CancelCalendarEvent

Integration Points:

GO Relationships:
- GO receives input from "Leave Approval Process" for LeaveApplication processing.

External Systems:
- Calendar System: Two-way integration for calendar event synchronization
- Notification System: Outbound integration for email and mobile notifications

Performance Metadata:
- cycle_time: "1 minute"
- number_of_pathways: 2
- volume_metrics:
  * average_volume: 200
  * peak_volume: 400
  * unit: "events/month"
- sla_thresholds:
  * notification: "5 minutes"
  * calendar_update: "1 minute"
- critical_lo_performance:
  * "CreateCalendarEvent": "1 minute maximum"
  * "NotifyEmployee": "5 minutes maximum"

Process Mining Schema:

Event Log Specification:
- case_id: "CalendarEvent.eventId"
- activity: "LO_name"
- event_type: "start/complete/abort/rollback"
- timestamp: "ISO-8601 datetime"
- resource: "role/system_executing"
- duration: "milliseconds"
- attributes:
  * entity_state: "json_snapshot"
  * input_values: "input_parameters"
  * output_values: "output_results"
  * execution_status: "success/failure/pending"
  * error_details: "error_message_if_any"

Performance Discovery Metrics:
- pathway_frequency:
  * "Standard Calendar Creation":
    - frequency: 180
    - percentage: 90
    - average_duration: "45 seconds"
    - success_rate: 99
  * "Calendar Cancellation":
    - frequency: 20
    - percentage: 10
    - average_duration: "30 seconds"
    - success_rate: 98
- bottleneck_analysis:
  * "CreateCalendarEvent":
    - average_wait_time: "10 seconds"
    - queue_length: 3
    - resource_utilization: 60
    - failure_rate: 1
- resource_patterns:
  * "Calendar System":
    - active_hours: "24/7"
    - peak_load_periods: "8:00-10:00 workdays"
    - concurrent_executions: 25

Conformance Analytics:
- compliance_rate: 99
- execution_variance:
  * "CreateCalendarEvent":
    - expected_duration: "30 seconds"
    - actual_duration_range: "20-60 seconds"
    - variance_causes: ["system load", "calendar service latency"]
  * "NotifyEmployee":
    - expected_duration: "1 minute"
    - actual_duration_range: "45-120 seconds"
    - variance_causes: ["notification system latency", "email delivery issues"]
- exception_patterns:
  * "calendar_conflict":
    - frequency: 5
    - affected_pathways: ["Standard Calendar Creation"]
    - recovery_success_rate: 95
  * "system_error":
    - frequency: 1
    - error_categories: ["calendar service unavailable", "notification delivery failure"]
    - automatic_recovery_rate: 90

Advanced Process Intelligence:
- process_health_score:
  * performance_score: 95
  * compliance_score: 98
  * efficiency_score: 92
  * overall_health: 95
- prediction_models:
  * completion_time_forecast:
    - algorithm: "Linear Regression"
    - accuracy: 95
    - confidence_interval: "±10 seconds"
  * failure_prediction:
    - algorithm: "Decision Tree"
    - precision: 90
    - recall: 85
- optimization_insights:
  * bottleneck_elimination: ["Implement calendar service caching", "Optimize notification delivery"]
  * resource_reallocation: ["Increase calendar service capacity during peak hours"]
  * pathway_optimization: ["Batch calendar updates for efficiency"]

Rollback Analytics:
- rollback_frequency: 2
- rollback_success_rate: 99
- rollback_triggers:
  * "user_initiated":
    - frequency: 18
    - average_impact_scope: 1
    - average_recovery_time: "30 seconds"
  * "system_error":
    - frequency: 2
    - average_impact_scope: 1
    - average_recovery_time: "45 seconds"
- rollback_pathways:
  * "calendar_cancellation":
    - frequency: 20
    - success_rate: 99
    - average_completion_time: "35 seconds"

Sample Data:
- CalendarEvent.eventId: "CE-2023-0001", "CE-2023-0002", "CE-2023-0003"
- CalendarEvent.employeeId: "EMP001", "EMP002", "EMP003"
- CalendarEvent.startDate: "2023-06-15", "2023-07-01", "2023-08-10"
- CalendarEvent.endDate: "2023-06-16", "2023-07-05", "2023-08-17"
- CalendarEvent.title: "Annual Leave", "Sick Leave", "Personal Leave"
- CalendarEvent.status: "Scheduled", "Cancelled"

Validation Checklist for GO Creation:

- [x] All LOs are defined with natural language names
- [x] Every referenced LO is defined in Process Flow section
- [x] All routing references appropriate LO names
- [x] First LO is system-initiated
- [x] All input mappings use natural language LO names
- [x] All output mappings use natural language GO names
- [x] Business rules reference specific LO names
- [x] Rollback LOs are included in Process Flow
- [x] All pathways terminate in completion or cancellation
- [x] Input Stack contains only aggregate/post-process data
- [x] Input Mapping shows where data enters LOs
- [x] Output Stack contains only final GO outputs
- [x] Output Mapping shows where data goes to other GOs
- [x] No function calls in Input/Output stacks
- [x] DB Stack has proper constraints and error messages
- [x] GO relationships specify exact entities
- [x] External systems specify integration direction
- [x] All integration points map to specific LOs
- [x] Process mining schema captures all events
- [x] Rollback analytics are included
- [x] Cycle time is specified
- [x] Number of pathways is counted correctly
- [x] Volume metrics are realistic
- [x] All critical LOs have performance targets
- [x] SLA thresholds are defined
