{"timestamp": "2025-06-23T14:07:58.070569", "endpoint": "parse-and-validate/attribute-validations", "input": {"natural_language": "Email field should be required and must be a valid email format", "tenant_id": "tenant_123"}, "output": {"success": true, "parsed_data": {"tenant_id": "tenant_123", "tenant_name": null, "validation_rules": [], "total_rules": 0, "parsing_metadata": {"parser_version": "1.0.0", "parsed_at": "2025-06-23T14:07:58.070414", "parser_type": "attribute_validation"}}, "validation_result": {"structure_errors": [], "required_field_errors": [], "data_type_errors": [], "custom_errors": []}, "uniqueness_result": {"is_unique": true, "status": "unique", "message": "No existing data to compare against", "conflicts": []}, "operation": "parse_and_validate", "is_valid": true}, "status": "success"}