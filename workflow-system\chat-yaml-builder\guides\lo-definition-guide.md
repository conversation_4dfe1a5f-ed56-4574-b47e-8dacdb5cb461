# Comprehensive Guide to Creating LO Definitions with LLMs

## Introduction

This guide provides a structured approach to generating high-quality Local Objective (LO) definitions using Large Language Models (LLMs). The framework ensures consistent, well-structured LO definitions that capture all the necessary components for your workflow system.

## Core LO Definition Structure

Each LO definition should follow this comprehensive format:

```
## [LO_Name]

id: "[lo_id]"
contextual_id: "[go_id].[lo_id]"
name: "[Human-readable LO name]"
version: "[version_number]"
status: "[Active/Draft/Deprecated]"
workflow_source: "[origin/intermediate/terminal]"
function_type: "[Create/Update/Read/Delete]"

*[Role] has [rights] rights*

*Inputs: [Entity1] with attribute1*, attribute2* [depends on: attribute1], attribute3* (value1, value2), attribute4 [optional], instructions [info]*
* System loads [Entity1].instructions [info] with constant "[constant_key]".
* System generates [Entity].[attribute] using [function_name] with [parameters].
* System calculates [Entity].[attribute] using [function_name] with [parameters].
* System defaults [Entity].[attribute] to "[value]".
* System populates [Entity].[attribute2] [depends on: attribute1] using [function_name] from [ReferenceEntity].
* System displays instructions [info] with key "[instruction_key]".
* [Entity].[attribute] depends on [Entity].[other_attribute] using [function_name].

*Outputs: [Entity1] with attribute1, attribute2; [Entity2] with attribute1*
* System returns [Entity].[attribute] for downstream operations.
* System captures [Entity].[attribute] for audit purposes.
* System transforms [Entity].[attribute] for display using [function_name].

*DB Stack:*
* [Entity].[attribute] (datatype) is mandatory and unique. Error message: "[error_message]"
* [Entity].[attribute] (datatype) is optional. Error message: "[error_message]"
* [Entity].[attribute] must be one of [allowed_values]. Error message: "[error_message]"
* [Entity].[attribute] must follow [pattern/rule]. Error message: "[error_message]"
* [Entity].[attribute] must reference [ReferenceEntity].[reference_attribute]. Error message: "[error_message]"

*UI Stack:*
* [Entity].[attribute] displays as [ui_control] with [properties].
* [Entity].[attribute] is [editable/read-only] depending on [condition].
* [Entity].[attribute] visibility depends on [condition].
* [Entity].[attribute] [depends on: other_attribute] populates from [ReferenceEntity] filtered by [filter_condition].
* System provides contextual help for [Entity].[attribute] explaining "[help_text]".

*Mapping Stack:*
* [this_lo].output.[attribute] maps to [next_lo].input.[attribute] using [mapping_type].
* [Entity1].[attribute] transforms to [Entity2].[attribute] using [function].
* System preserves [Entity].[attribute] across multiple LOs for workflow continuity.

Execution pathway:
* When [Entity].[attribute] [operator] [value], route to [specific_LO_name].
* When [Entity].[attribute] [operator] [value], route to [specific_LO_name].

Synthetic values:
* [Entity].[attribute]: "[sample_value1]", "[sample_value2]", "[sample_value3]"
* [Entity].[attribute]: [number_value1], [number_value2], [number_value3]
```

## Section-by-Section Guidelines

### 1. LO Metadata and Role Definition

```
## ApplyForLeave

id: "lo001"
contextual_id: "go001.lo001"
name: "Apply for Leave"
version: "1.0"
status: "Active"
workflow_source: "origin"
function_type: "Create"

*Employee has execution rights*
```

**Guidelines:**
- Use CamelCase for LO names without spaces
- Assign a unique ID to each LO, typically using a sequential pattern (lo001, lo002)
- Include contextual ID that references the parent Global Objective (GO)
- Specify version using semantic versioning (major.minor)
- Define current status (Active, Draft, Deprecated, Archived)
- Indicate workflow source position (origin, intermediate, terminal)
- Specify the primary database operation (Create, Update, Read, Delete)
- Be specific about role names (matching your system's role definitions)
- Specify exact rights: "execution", "read", "update"
- When multiple roles are involved, list each on a separate line with their specific rights

### 2. Input Definition

```
*Inputs: LeaveApplication with leaveID*, employeeID*, startDate*, endDate*, numDays*, reason*, leaveType* (Sick Leave, Annual Leave, Parental Leave, Bereavement), leaveSubType, status* (Pending, Approved, Rejected)*
```

**Guidelines:**
- Start with the entity name
- List all attributes that are part of the input
- Mark mandatory fields with an asterisk (*) after the attribute name
- Alternatively, you can mark optional fields with [optional] after the attribute name
- Mark information-only fields with [info] - these are attributes that display text but aren't stored
- Include enum values in parentheses immediately after the attribute
- When multiple entities are involved, separate them with semicolons
- For dependent dropdowns, use the [depends on: attribute_name] notation after the attribute
- Ensure consistency in naming across the entire workflow

### 3. System Actions

```
* System generates LeaveApplication.leaveID using generate_id with prefix "LV".
* System calculates LeaveApplication.numDays using subtract_days with startDate and endDate.
* System defaults LeaveApplication.status to "Pending".
* When filtering subtypes, LeaveSubType records should filter where leaveType equals selected LeaveApplication.leaveType and active equals true.
* System populates LeaveApplication.leaveSubType [depends on: leaveType] using fetch_filtered_records from LeaveSubType where LeaveSubType.leaveType = LeaveApplication.leaveType.
* System displays Instructions.policyText [info] using load_info_text with "leave_policy".
```

**Guidelines:**
- Use the exact format: "System [action] [Entity].[attribute] using [function] with [parameters]"
- Use standard action verbs: "generates", "calculates", "validates", "transforms", "defaults", "populates", "displays"
- Be specific about function names and parameters
- For filtering and dependencies, clearly state the conditions and relationships
- For dependent dropdowns, indicate the parent field with [depends on: attribute_name]
- For information-only fields, mark with [info] after the attribute name

### 4. Outputs Definition

```
*Outputs: LeaveApplication with leaveID, employeeID, startDate, endDate, numDays, reason, leaveType, leaveSubType, status*
```

**Guidelines:**
- Follow the same entity-attribute format as inputs
- Include only the attributes that are actually output from this LO
- For complex transformations, explain how outputs are derived
- Use semicolons to separate multiple entities

### 5. DB Stack

```
*DB Stack:*
* LeaveApplication.leaveID (string) is mandatory and unique. Error message: "Leave ID is required and must be unique"
* LeaveApplication.employeeID (string) is mandatory and must exist in Employee table. Error message: "Employee ID is required"
```

**Guidelines:**
- Specify data type in parentheses
- Clearly indicate constraints: mandatory/optional, unique/non-unique
- Include validation rules, such as references to other entities
- Always provide error messages in quotes
- Include patterns or formats if applicable

### 6. UI Stack

```
*UI Stack:*
* LeaveApplication.leaveID displays as oj-input-text with readonly property.
* LeaveApplication.startDate displays as oj-input-date with min-value set to current date.
* LeaveApplication.endDate displays as oj-input-date with min-value bound to startDate.
* LeaveApplication.leaveType displays as oj-combobox-one with source from entity enumeration.
* LeaveApplication.leaveSubType [depends on: leaveType] displays as oj-combobox-one with source from LeaveSubType filtered by LeaveSubType.leaveType = LeaveApplication.leaveType and LeaveSubType.active = true.
* Instructions.policyText [info] displays as oj-text with formatting-class "policy-highlight".
```

**Guidelines:**
- Specify the exact UI control name from your library
- Include all relevant properties and constraints
- For conditional display or properties, clearly state the conditions
- Include help text for complex fields
- Specify dependencies between fields using [depends on: attribute_name]
- For dependent dropdowns, clearly specify the reference entity and filter conditions
- For information-only fields, mark with [info]
- Describe how reference data is loaded and filtered for dropdown controls

### 7. Mapping Stack

```
*Mapping Stack:*
* ApplyForLeave.output.leaveID maps to ManagerApproval.input.leaveID using direct mapping.
* ApplyForLeave.output.employeeID maps to ManagerApproval.input.employeeID using direct mapping.
```

**Guidelines:**
- Use the format: "[source_lo].output.[attribute] maps to [target_lo].input.[attribute]"
- Specify the mapping type: "direct", "transform", "conditional"
- For transformations, specify the function used
- Ensure all critical fields are mapped between LOs
- For conditional mappings, clearly state the conditions

### 8. Execution Pathway

```
Execution pathway:
* When LeaveApplication.numDays > 3, system flags LeaveApplication.requiresHRApproval to true, route to HRManagerApproval.
* When LeaveApplication.numDays <= 3, route to ManagerApproval.
```

**Guidelines:**
- Use the format: "When [condition], route to [specific_LO_name]"
- Use standard comparison operators: =, !=, >, <, >=, <=
- For setting flags, include that in the same statement
- Cover all possible pathways to ensure the workflow is complete
- For terminal LOs, state "Terminal workflow stage" if there's no next step

### 9. Synthetic Values

```
Synthetic values:
* LeaveApplication.leaveID: "LV-2023-0001", "LV-2023-0002", "LV-2023-0003"
* LeaveApplication.startDate: "2023-06-15", "2023-07-01", "2023-08-10"
* LeaveApplication.numDays: 2, 5, 8
```

**Guidelines:**
- Provide 3-5 representative synthetic values for each attribute
- Use realistic data formats appropriate for each data type
- For dates, use consistent ISO format: "YYYY-MM-DD"
- For timestamps, use ISO format: "YYYY-MM-DDTHH:MM:SS"
- Include boundary values and typical values to cover the range
- For enums, include all possible values

## Common Patterns and Reusable Components

### 1. Standard System Functions

- **generate_id**: Creates unique identifiers with prefixes
  ```
  System generates [Entity].[attribute] using generate_id with prefix "[prefix]".
  ```

- **subtract_days**: Calculates the difference between dates
  ```
  System calculates [Entity].[attribute] using subtract_days with startDate, endDate.
  ```

- **current_timestamp**: Captures the current date and time
  ```
  System captures [Entity].[attribute] using current_timestamp.
  ```

- **to_uppercase**: Transforms text to uppercase
  ```
  System transforms [Entity].[attribute] using to_uppercase with [text].
  ```

- **fetch_filtered_records**: Retrieves records based on conditions
  ```
  System filters [Entity] records using fetch_filtered_records with [filter_entity].[filter_attribute] equals [source_entity].[source_attribute].
  ```

- **populate_dependent_dropdown**: Populates dependent dropdown options
  ```
  System populates [Entity].[attribute] [depends on: parent_attribute] using populate_dependent_dropdown from [ReferenceEntity] where [ReferenceEntity].[filter_attribute] = [Entity].[parent_attribute].
  ```

- **load_info_text**: Loads informational text for display only
  ```
  System loads [Entity].[attribute] [info] with constant "[constant_key]".
  ```

- **format_enum_value**: Formats enumeration values for display
  ```
  System transforms [Entity].[attribute] for display using format_enum_value.
  ```

- **validate_reference**: Validates reference to another entity
  ```
  System validates [Entity].[attribute] using validate_reference with [ReferenceEntity].[reference_attribute].
  ```

### 2. Common Validation Patterns

- **Required Field**
  ```
  [Entity].[attribute] (datatype) is mandatory. Error message: "[attribute] is required"
  ```

- **Unique Constraint**
  ```
  [Entity].[attribute] (datatype) is mandatory and unique. Error message: "[attribute] must be unique"
  ```

- **Enumeration Constraint**
  ```
  [Entity].[attribute] (enum) must be one of "[value1]", "[value2]", "[value3]". Error message: "Please select a valid [attribute]"
  ```

- **Cross-Field Validation**
  ```
  [Entity].[attribute1] must be after [Entity].[attribute2]. Error message: "[attribute1] must be after [attribute2]"
  ```

### 3. Common UI Controls

- **Text Input**
  ```
  [Entity].[attribute] displays as oj-input-text with [properties].
  ```

- **Date Input**
  ```
  [Entity].[attribute] displays as oj-input-date with min-value set to [value].
  ```

- **Number Input**
  ```
  [Entity].[attribute] displays as oj-input-number with min-value set to [value].
  ```

- **Dropdown/Select from Enumeration**
  ```
  [Entity].[attribute] displays as oj-combobox-one with source from entity enumeration.
  ```

- **Dependent Dropdown**
  ```
  [Entity].[attribute] [depends on: parent_attribute] displays as oj-combobox-one with source from [ReferenceEntity] filtered by [ReferenceEntity].[filter_attribute] = [Entity].[parent_attribute].
  ```

- **Text Area**
  ```
  [Entity].[attribute] displays as oj-text-area with rows set to [value].
  ```

- **Information Text**
  ```
  [Entity].[attribute] [info] displays as oj-text with formatting-class "[class_name]".
  ```

- **Hidden Field**
  ```
  [Entity].[attribute] is hidden and populated by system.
  ```

### 4. Common Mapping Patterns

- **Direct Mapping**
  ```
  [source_lo].output.[attribute] maps to [target_lo].input.[attribute] using direct mapping.
  ```

- **Transformation Mapping**
  ```
  [source_lo].output.[attribute] maps to [target_lo].input.[attribute] using [transform_function].
  ```

- **Conditional Mapping**
  ```
  When [condition], [source_lo].output.[attribute] maps to [target_lo].input.[attribute].
  ```

### 5. Common Pathway Patterns

- **Conditional Routing**
  ```
  When [Entity].[attribute] [operator] [value], route to [specific_LO_name].
  ```

- **Flag Setting and Routing**
  ```
  When [condition], system flags [Entity].[attribute] to [value], route to [specific_LO_name].
  ```

- **Terminal LO**
  ```
  Terminal workflow stage.
  ```

## Best Practices for LO Generation with LLMs

1. **Provide Complete Entity Context**
   - Give the LLM complete entity definitions before requesting LO generation
   - Include attributes, data types, and relationships between entities

2. **Use Few-Shot Learning**
   - Provide 2-3 examples of well-formed LOs with different patterns
   - Include examples covering different workflow patterns (creation, approval, notification)

3. **Validate LO Connectivity**
   - Ensure input/output mappings between LOs are consistent
   - Verify that all pathways lead to valid LO destinations

4. **Check for Completeness**
   - Ensure all required sections are present in each LO
   - Verify that all inputs have corresponding validations and UI controls

5. **Maintain Naming Consistency**
   - Use consistent naming conventions across all LOs in a workflow
   - Ensure entity and attribute names match your data model exactly

6. **Review Synthetic Data**
   - Ensure synthetic values are realistic and cover the range of possibilities
   - Include edge cases and typical values

7. **Chunk Complex Workflows**
   - For large workflows, generate LOs in smaller related groups
   - Ensure proper connections between chunks

## Implementation Strategy for LLM Integration

1. **Preparation Phase**
   - Define your entity model completely
   - Create reference examples for each LO type
   - Develop prompt templates with placeholders

2. **Generation Phase**
   - Generate high-level workflow structure first
   - Then generate detailed LOs for each step
   - Include context from previous LOs when generating subsequent ones

3. **Validation Phase**
   - Check for syntactic correctness
   - Verify entity-attribute consistency
   - Ensure complete pathway coverage
   - Validate mappings between LOs

4. **Refinement Phase**
   - Address any issues identified in validation
   - Enhance with additional constraints or rules
   - Improve synthetic data coverage

## Complete Example: Leave Management Workflow

### ApplyForLeave

```
## ApplyForLeave

id: "lo001"
contextual_id: "go001.lo001"
name: "Apply for Leave"
version: "1.0"
status: "Active"
workflow_source: "origin"
function_type: "Create"

*Employee has execution rights*

*Inputs: LeaveApplication with leaveID*, employeeID*, startDate*, endDate*, numDays*, reason*, leaveType* (Sick Leave, Annual Leave, Parental Leave, Bereavement), leaveSubType [depends on: leaveType], status* (Pending, Approved, Rejected), instructions [info]*
* System generates LeaveApplication.leaveID using generate_id with prefix "LV".
* System calculates LeaveApplication.numDays using subtract_days with startDate and endDate.
* System defaults LeaveApplication.status to "Pending".
* System populates LeaveApplication.leaveSubType [depends on: leaveType] using fetch_filtered_records from LeaveSubType where LeaveSubType.leaveType = LeaveApplication.leaveType and LeaveSubType.active = true.
* System loads LeaveApplication.instructions [info] with constant "leave_application_instructions".

*Outputs: LeaveApplication with leaveID, employeeID, startDate, endDate, numDays, reason, leaveType, leaveSubType, status*
* System returns LeaveApplication.leaveID for reference in notifications.
* System captures LeaveApplication.submissionDate using current_timestamp for audit trails.
* System transforms LeaveApplication.leaveType for display using format_enum_value.

*DB Stack:*
* LeaveApplication.leaveID (string) is mandatory and unique. Error message: "Leave ID is required and must be unique"
* LeaveApplication.employeeID (string) is mandatory and must exist in Employee table. Error message: "Employee ID is required"
* LeaveApplication.startDate (date) is mandatory and must be a valid date. Error message: "Start date is required"
* LeaveApplication.endDate (date) is mandatory and must be after startDate. Error message: "End date must be after start date"
* LeaveApplication.reason (string) is mandatory with minimum 10 characters. Error message: "Please provide a detailed reason for your leave request"
* LeaveApplication.leaveType (enum) must be one of "Sick Leave", "Annual Leave", "Parental Leave", "Bereavement". Error message: "Please select a valid leave type"

*UI Stack:*
* LeaveApplication.leaveID displays as oj-input-text with readonly property.
* LeaveApplication.startDate displays as oj-input-date with min-value set to current date.
* LeaveApplication.endDate displays as oj-input-date with min-value bound to startDate.
* LeaveApplication.numDays displays as oj-input-number with readonly property.
* LeaveApplication.leaveType displays as oj-combobox-one with source from entity enumeration.
* LeaveApplication.leaveSubType [depends on: leaveType] displays as oj-combobox-one with source from LeaveSubType filtered by LeaveSubType.leaveType = LeaveApplication.leaveType and LeaveSubType.active = true.
* LeaveApplication.instructions [info] displays as oj-text with formatting-class "policy-highlight".
* System provides contextual help for LeaveApplication.leaveType explaining "Select the type of leave you are requesting. Different leave types may have different approval requirements."

*Mapping Stack:*
* ApplyForLeave.output.leaveID maps to ManagerApproval.input.leaveID using direct mapping.
* ApplyForLeave.output.employeeID maps to ManagerApproval.input.employeeID using direct mapping.
* ApplyForLeave.output.startDate maps to ManagerApproval.input.startDate using direct mapping.
* ApplyForLeave.output.endDate maps to ManagerApproval.input.endDate using direct mapping.
* ApplyForLeave.output.numDays maps to ManagerApproval.input.numDays using direct mapping.
* ApplyForLeave.output.reason maps to ManagerApproval.input.reason using direct mapping.
* ApplyForLeave.output.leaveType maps to ManagerApproval.input.leaveType using direct mapping.
* ApplyForLeave.output.leaveSubType maps to ManagerApproval.input.leaveSubType using direct mapping.

Execution pathway:
* When LeaveApplication.numDays > 3, system flags LeaveApplication.requiresHRApproval to true, route to HRManagerApproval.
* When LeaveApplication.numDays <= 3, route to ManagerApproval.
* When LeaveApplication.leaveType = "Sick Leave" and LeaveApplication.numDays > 3, system flags LeaveApplication.requiresMedicalCertificate to true, route to MedicalCertificateUpload.

Synthetic values:
* LeaveApplication.leaveID: "LV-2023-0001", "LV-2023-0002", "LV-2023-0003"
* LeaveApplication.employeeID: "EMP001", "EMP002", "EMP003"
* LeaveApplication.startDate: "2023-06-15", "2023-07-01", "2023-08-10"
* LeaveApplication.endDate: "2023-06-16", "2023-07-05", "2023-08-17"
* LeaveApplication.numDays: 2, 5, 8
* LeaveApplication.reason: "Personal matters", "Family emergency", "Medical procedure"
* LeaveApplication.leaveType: "Annual Leave", "Sick Leave", "Parental Leave"
* LeaveApplication.leaveSubType: "Standard Annual Leave", "Short-term Illness", "Maternity Leave"
* LeaveApplication.status: "Pending"
* LeaveApplication.submissionDate: "2023-06-01T09:15:30", "2023-06-15T14:22:45", "2023-07-05T10:05:12"
```

### ManagerApproval

```
## ManagerApproval

id: "lo002"
contextual_id: "go001.lo002"
name: "Manager Approval"
version: "1.0"
status: "Active"
workflow_source: "intermediate"
function_type: "Update"

*Manager has execution and update rights*
*HR Manager has read rights*

*Inputs: LeaveApplication with leaveID*, employeeID*, startDate*, endDate*, numDays*, reason*, leaveType*, leaveSubType, status* (Pending, Approved, Rejected), instructions [info]*
* System loads LeaveApplication.instructions [info] with constant "short_leave_policy_guidelines".
* Manager updates LeaveApplication.status to "Approved" or "Rejected".
* Manager provides LeaveApplication.remarks [optional, required when status="Rejected"].
* System captures LeaveApplication.approvedBy using current_timestamp.

*Outputs: LeaveApplication with leaveID, status, remarks, approvedBy*
* System returns LeaveApplication.status for notification generation.
* System captures LeaveApplication.approvalDate using current_timestamp for audit purposes.
* System transforms LeaveApplication.status for display using format_status_with_icon.

*DB Stack:*
* LeaveApplication.leaveID (string) is mandatory and must exist. Error message: "Invalid leave application reference"
* LeaveApplication.status (enum) is mandatory and must be one of "Pending", "Approved", "Rejected". Error message: "Please select a valid approval status"
* LeaveApplication.remarks (string) is optional but required when status is "Rejected". Error message: "Please provide a reason for rejection"
* LeaveApplication.approvedBy (string) is system-generated and mandatory. Error message: "Approver information missing"

*UI Stack:*
* LeaveApplication.leaveID displays as oj-input-text with readonly property.
* LeaveApplication.employeeID displays as oj-input-text with readonly property.
* LeaveApplication.startDate displays as oj-input-date with readonly property.
* LeaveApplication.endDate displays as oj-input-date with readonly property.
* LeaveApplication.numDays displays as oj-input-number with readonly property.
* LeaveApplication.reason displays as oj-text-area with readonly property.
* LeaveApplication.instructions [info] displays as oj-text with formatting-class "policy-highlight".
* LeaveApplication.status displays as oj-select-single with options "Approved" and "Rejected".
* LeaveApplication.remarks displays as oj-text-area.
* LeaveApplication.remarks is mandatory when LeaveApplication.status is "Rejected".
* System provides contextual help for LeaveApplication.status explaining "Approve or reject this leave request. Rejection requires providing a reason in the remarks field."

*Mapping Stack:*
* ManagerApproval.output.leaveID maps to LeaveConfirmation.input.leaveID using direct mapping when status is "Approved".
* ManagerApproval.output.leaveID maps to LeaveRejectionNotification.input.leaveID using direct mapping when status is "Rejected".
* ManagerApproval.output.status maps to LeaveConfirmation.input.status using direct mapping when status is "Approved".
* ManagerApproval.output.status maps to LeaveRejectionNotification.input.status using direct mapping when status is "Rejected".
* ManagerApproval.output.remarks maps to LeaveRejectionNotification.input.remarks using direct mapping when status is "Rejected".

Execution pathway:
* When LeaveApplication.status = "Approved", route to LeaveConfirmation.
* When LeaveApplication.status = "Rejected", system requires Manager to provide LeaveApplication.remarks, route to LeaveRejectionNotification.

Synthetic values:
* LeaveApplication.leaveID: "LV-2023-0001", "LV-2023-0002", "LV-2023-0003"
* LeaveApplication.status: "Approved", "Rejected", "Pending" 
* LeaveApplication.remarks: "Approved as requested", "Team is short-staffed during this period", "Please reschedule"
* LeaveApplication.approvedBy: "MGR001", "MGR002", "MGR003"
* LeaveApplication.approvalDate: "2023-06-02T10:30:45", "2023-06-16T15:45:22", "2023-07-06T11:20:15"
```

By following this guide, you can create comprehensive, consistent LO definitions that fully leverage your workflow system's capabilities.
