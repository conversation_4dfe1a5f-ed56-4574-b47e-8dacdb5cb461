2025-05-12 11:12:05,050 - verify_fixes - INFO - Starting verification checks
2025-05-12 11:12:05,050 - verify_fixes - INFO - === Checking Entity IDs ===
2025-05-12 11:12:05,050 - verify_fixes - INFO - Checking entity IDs
2025-05-12 11:12:05,055 - db_connection - INFO - Connected to database workflow_system on 172.18.0.5:5432
2025-05-12 11:12:05,056 - verify_fixes - INFO - All entity IDs use sequential IDs (E1, E2)
2025-05-12 11:12:05,056 - verify_fixes - INFO - All entity IDs use sequential IDs (E1, E2)
2025-05-12 11:12:05,056 - verify_fixes - INFO - === Checking Enum Values ===
2025-05-12 11:12:05,056 - verify_fixes - INFO - Checking enum values
2025-05-12 11:12:05,061 - db_connection - INFO - Connected to database workflow_system on 172.18.0.5:5432
2025-05-12 11:12:05,063 - verify_fixes - INFO - No enum attributes found
2025-05-12 11:12:05,063 - verify_fixes - INFO - No enum attributes found
2025-05-12 11:12:05,063 - verify_fixes - INFO - === Checking Validations ===
2025-05-12 11:12:05,063 - verify_fixes - INFO - Checking validations
2025-05-12 11:12:05,067 - db_connection - INFO - Connected to database workflow_system on 172.18.0.5:5432
2025-05-12 11:12:05,068 - verify_fixes - WARNING - No validations found
2025-05-12 11:12:05,069 - verify_fixes - INFO - Warning: No validations found
2025-05-12 11:12:05,069 - verify_fixes - INFO - === Checking Calculated Fields ===
2025-05-12 11:12:05,069 - verify_fixes - INFO - Checking calculated fields
2025-05-12 11:12:05,073 - db_connection - INFO - Connected to database workflow_system on 172.18.0.5:5432
2025-05-12 11:12:05,074 - verify_fixes - INFO - Found 1 calculated fields
2025-05-12 11:12:05,074 - verify_fixes - INFO - Calculated field 'CF001' for Employee.fullName:
2025-05-12 11:12:05,074 - verify_fixes - INFO -   - Formula: CONCAT(firstName, ' ', lastName)
2025-05-12 11:12:05,074 - verify_fixes - INFO -   - Logic Layer: Application
2025-05-12 11:12:05,074 - verify_fixes - INFO -   - Caching: Session
2025-05-12 11:12:05,074 - verify_fixes - INFO -   - Dependencies: ['Employee.firstName', 'Employee.lastName']
2025-05-12 11:12:05,078 - db_connection - INFO - Connected to database workflow_system on 172.18.0.5:5432
2025-05-12 11:12:05,079 - verify_fixes - INFO - Found 1 attributes marked as calculated fields
2025-05-12 11:12:05,080 - verify_fixes - INFO - Attribute Employee.fullName is marked as a calculated field
2025-05-12 11:12:05,080 - verify_fixes - INFO - Found 1 calculated fields
2025-05-12 11:12:05,080 - verify_fixes - INFO - Calculated field 'CF001' for Employee.fullName: CONCAT(firstName, ' ', lastName)
2025-05-12 11:12:05,080 - verify_fixes - INFO - Found 1 attributes marked as calculated fields
2025-05-12 11:12:05,080 - verify_fixes - INFO - Attribute Employee.fullName is marked as a calculated field
2025-05-12 11:12:05,080 - verify_fixes - INFO - === Checking Lifecycle Management ===
2025-05-12 11:12:05,080 - verify_fixes - INFO - Checking lifecycle management
2025-05-12 11:12:05,083 - db_connection - INFO - Connected to database workflow_system on 172.18.0.5:5432
2025-05-12 11:12:05,085 - verify_fixes - INFO - Found 3 lifecycle management entries
2025-05-12 11:12:05,085 - verify_fixes - INFO - Lifecycle management for Employee (archive):
2025-05-12 11:12:05,085 - verify_fixes - INFO -   - Trigger Type: Event-based
2025-05-12 11:12:05,085 - verify_fixes - INFO -   - Criteria: When Employee.status changes to 'Inactive' and remains so for 1 year
2025-05-12 11:12:05,085 - verify_fixes - INFO -   - Retention: 7 years
2025-05-12 11:12:05,085 - verify_fixes - INFO -   - Storage: Cold storage
2025-05-12 11:12:05,085 - verify_fixes - INFO -   - Access Pattern: Read-only through HR archive portal
2025-05-12 11:12:05,085 - verify_fixes - INFO -   - Restoration: Manual process requiring HR Director approval
2025-05-12 11:12:05,085 - verify_fixes - INFO - Lifecycle management for Employee (history):
2025-05-12 11:12:05,085 - verify_fixes - INFO -   - Tracked Attributes: Employee.salary, Employee.departmentId, Employee.managerId, Employee.status
2025-05-12 11:12:05,085 - verify_fixes - INFO -   - Tracking Method: Audit table
2025-05-12 11:12:05,085 - verify_fixes - INFO -   - Granularity: Change level
2025-05-12 11:12:05,085 - verify_fixes - INFO -   - Retention: 7 years
2025-05-12 11:12:05,085 - verify_fixes - INFO -   - Access Control: HR Managers and Compliance Officers only
2025-05-12 11:12:05,085 - verify_fixes - INFO - Lifecycle management for Department (archive):
2025-05-12 11:12:05,085 - verify_fixes - INFO -   - Trigger Type: Event-based
2025-05-12 11:12:05,085 - verify_fixes - INFO -   - Criteria: When Department is marked as 'Dissolved'
2025-05-12 11:12:05,085 - verify_fixes - INFO -   - Retention: 10 years
2025-05-12 11:12:05,085 - verify_fixes - INFO -   - Storage: Cold storage
2025-05-12 11:12:05,085 - verify_fixes - INFO -   - Access Pattern: Read-only through Admin portal
2025-05-12 11:12:05,085 - verify_fixes - INFO -   - Restoration: Manual process requiring CEO approval
2025-05-12 11:12:05,085 - verify_fixes - INFO - Found 3 lifecycle management entries
2025-05-12 11:12:05,085 - verify_fixes - INFO - Archive strategy for Employee: Event-based - When Employee.status changes to 'Inactive' and remains so for 1 year
2025-05-12 11:12:05,085 - verify_fixes - INFO - History tracking for Employee: Audit table - Change level
2025-05-12 11:12:05,085 - verify_fixes - INFO - Archive strategy for Department: Event-based - When Department is marked as 'Dissolved'
2025-05-12 11:12:05,085 - verify_fixes - INFO - === Checking Foreign Key Constraints ===
2025-05-12 11:12:05,085 - verify_fixes - INFO - Checking for duplicate foreign key constraints
2025-05-12 11:12:05,089 - db_connection - INFO - Connected to database workflow_system on 172.18.0.5:5432
2025-05-12 11:12:05,158 - verify_fixes - INFO - No duplicate foreign key constraints found
2025-05-12 11:12:05,158 - verify_fixes - INFO - No duplicate foreign key constraints found
2025-05-12 11:12:05,158 - verify_fixes - INFO - === Checking Database Connections ===
2025-05-12 11:12:05,158 - verify_fixes - INFO - Checking database connections
2025-05-12 11:12:05,158 - verify_fixes - WARNING - component_deployer.py does not have connection pooling
2025-05-12 11:12:05,158 - verify_fixes - INFO - Warning: component_deployer.py does not have connection pooling
2025-05-12 11:12:05,158 - verify_fixes - INFO - === Verification Summary ===
2025-05-12 11:12:05,158 - verify_fixes - INFO - Entity IDs: OK
2025-05-12 11:12:05,158 - verify_fixes - INFO - Enum Values: OK
2025-05-12 11:12:05,158 - verify_fixes - INFO - Validations: FAILED
2025-05-12 11:12:05,158 - verify_fixes - INFO - Calculated Fields: OK
2025-05-12 11:12:05,158 - verify_fixes - INFO - Lifecycle Management: OK
2025-05-12 11:12:05,158 - verify_fixes - INFO - Foreign Key Constraints: OK
2025-05-12 11:12:05,158 - verify_fixes - INFO - Database Connections: FAILED
2025-05-12 11:12:05,158 - verify_fixes - WARNING - Some checks failed. See verify_fixes.log for details.
2025-05-12 11:16:35,045 - verify_fixes - INFO - Starting verification checks
2025-05-12 11:16:35,045 - verify_fixes - INFO - === Checking Entity IDs ===
2025-05-12 11:16:35,045 - verify_fixes - INFO - Checking entity IDs
2025-05-12 11:16:35,050 - db_connection - INFO - Connected to database workflow_system on 172.18.0.5:5432
2025-05-12 11:16:35,051 - verify_fixes - INFO - All entity IDs use sequential IDs (E1, E2)
2025-05-12 11:16:35,051 - verify_fixes - INFO - All entity IDs use sequential IDs (E1, E2)
2025-05-12 11:16:35,051 - verify_fixes - INFO - === Checking Enum Values ===
2025-05-12 11:16:35,051 - verify_fixes - INFO - Checking enum values
2025-05-12 11:16:35,055 - db_connection - INFO - Connected to database workflow_system on 172.18.0.5:5432
2025-05-12 11:16:35,057 - verify_fixes - INFO - No enum attributes found
2025-05-12 11:16:35,057 - verify_fixes - INFO - No enum attributes found
2025-05-12 11:16:35,057 - verify_fixes - INFO - === Checking Validations ===
2025-05-12 11:16:35,057 - verify_fixes - INFO - Checking validations
2025-05-12 11:16:35,061 - db_connection - INFO - Connected to database workflow_system on 172.18.0.5:5432
2025-05-12 11:16:35,067 - db_connection - INFO - Connected to database workflow_system on 172.18.0.5:5432
2025-05-12 11:16:35,069 - verify_fixes - WARNING - No validations found
2025-05-12 11:16:35,069 - verify_fixes - INFO - Warning: No validations found
2025-05-12 11:16:35,069 - verify_fixes - INFO - === Checking Calculated Fields ===
2025-05-12 11:16:35,069 - verify_fixes - INFO - Checking calculated fields
2025-05-12 11:16:35,073 - db_connection - INFO - Connected to database workflow_system on 172.18.0.5:5432
2025-05-12 11:16:35,074 - verify_fixes - INFO - Found 1 calculated fields
2025-05-12 11:16:35,074 - verify_fixes - INFO - Calculated field 'CF001' for Employee.fullName:
2025-05-12 11:16:35,074 - verify_fixes - INFO -   - Formula: CONCAT(firstName, ' ', lastName)
2025-05-12 11:16:35,074 - verify_fixes - INFO -   - Logic Layer: Application
2025-05-12 11:16:35,074 - verify_fixes - INFO -   - Caching: Session
2025-05-12 11:16:35,075 - verify_fixes - INFO -   - Dependencies: ['Employee.firstName', 'Employee.lastName']
2025-05-12 11:16:35,078 - db_connection - INFO - Connected to database workflow_system on 172.18.0.5:5432
2025-05-12 11:16:35,080 - verify_fixes - INFO - Found 1 attributes marked as calculated fields
2025-05-12 11:16:35,080 - verify_fixes - INFO - Attribute Employee.fullName is marked as a calculated field
2025-05-12 11:16:35,080 - verify_fixes - INFO - Found 1 calculated fields
2025-05-12 11:16:35,080 - verify_fixes - INFO - Calculated field 'CF001' for Employee.fullName: CONCAT(firstName, ' ', lastName)
2025-05-12 11:16:35,080 - verify_fixes - INFO - Found 1 attributes marked as calculated fields
2025-05-12 11:16:35,080 - verify_fixes - INFO - Attribute Employee.fullName is marked as a calculated field
2025-05-12 11:16:35,080 - verify_fixes - INFO - === Checking Lifecycle Management ===
2025-05-12 11:16:35,080 - verify_fixes - INFO - Checking lifecycle management
2025-05-12 11:16:35,083 - db_connection - INFO - Connected to database workflow_system on 172.18.0.5:5432
2025-05-12 11:16:35,085 - verify_fixes - INFO - Found 3 lifecycle management entries
2025-05-12 11:16:35,085 - verify_fixes - INFO - Lifecycle management for Employee (archive):
2025-05-12 11:16:35,085 - verify_fixes - INFO -   - Trigger Type: Event-based
2025-05-12 11:16:35,085 - verify_fixes - INFO -   - Criteria: When Employee.status changes to 'Inactive' and remains so for 1 year
2025-05-12 11:16:35,085 - verify_fixes - INFO -   - Retention: 7 years
2025-05-12 11:16:35,085 - verify_fixes - INFO -   - Storage: Cold storage
2025-05-12 11:16:35,085 - verify_fixes - INFO -   - Access Pattern: Read-only through HR archive portal
2025-05-12 11:16:35,085 - verify_fixes - INFO -   - Restoration: Manual process requiring HR Director approval
2025-05-12 11:16:35,085 - verify_fixes - INFO - Lifecycle management for Employee (history):
2025-05-12 11:16:35,085 - verify_fixes - INFO -   - Tracked Attributes: Employee.salary, Employee.departmentId, Employee.managerId, Employee.status
2025-05-12 11:16:35,085 - verify_fixes - INFO -   - Tracking Method: Audit table
2025-05-12 11:16:35,085 - verify_fixes - INFO -   - Granularity: Change level
2025-05-12 11:16:35,085 - verify_fixes - INFO -   - Retention: 7 years
2025-05-12 11:16:35,085 - verify_fixes - INFO -   - Access Control: HR Managers and Compliance Officers only
2025-05-12 11:16:35,085 - verify_fixes - INFO - Lifecycle management for Department (archive):
2025-05-12 11:16:35,085 - verify_fixes - INFO -   - Trigger Type: Event-based
2025-05-12 11:16:35,085 - verify_fixes - INFO -   - Criteria: When Department is marked as 'Dissolved'
2025-05-12 11:16:35,085 - verify_fixes - INFO -   - Retention: 10 years
2025-05-12 11:16:35,085 - verify_fixes - INFO -   - Storage: Cold storage
2025-05-12 11:16:35,085 - verify_fixes - INFO -   - Access Pattern: Read-only through Admin portal
2025-05-12 11:16:35,085 - verify_fixes - INFO -   - Restoration: Manual process requiring CEO approval
2025-05-12 11:16:35,085 - verify_fixes - INFO - Found 3 lifecycle management entries
2025-05-12 11:16:35,085 - verify_fixes - INFO - Archive strategy for Employee: Event-based - When Employee.status changes to 'Inactive' and remains so for 1 year
2025-05-12 11:16:35,085 - verify_fixes - INFO - History tracking for Employee: Audit table - Change level
2025-05-12 11:16:35,085 - verify_fixes - INFO - Archive strategy for Department: Event-based - When Department is marked as 'Dissolved'
2025-05-12 11:16:35,085 - verify_fixes - INFO - === Checking Foreign Key Constraints ===
2025-05-12 11:16:35,085 - verify_fixes - INFO - Checking for duplicate foreign key constraints
2025-05-12 11:16:35,089 - db_connection - INFO - Connected to database workflow_system on 172.18.0.5:5432
2025-05-12 11:16:35,157 - verify_fixes - INFO - No duplicate foreign key constraints found
2025-05-12 11:16:35,158 - verify_fixes - INFO - No duplicate foreign key constraints found
2025-05-12 11:16:35,158 - verify_fixes - INFO - === Checking Database Connections ===
2025-05-12 11:16:35,158 - verify_fixes - INFO - Checking database connections
2025-05-12 11:16:35,158 - verify_fixes - INFO - component_deployer.py has connection pooling
2025-05-12 11:16:35,158 - verify_fixes - INFO - component_deployer.py has connection pooling
2025-05-12 11:16:35,158 - verify_fixes - INFO - === Verification Summary ===
2025-05-12 11:16:35,158 - verify_fixes - INFO - Entity IDs: OK
2025-05-12 11:16:35,158 - verify_fixes - INFO - Enum Values: OK
2025-05-12 11:16:35,158 - verify_fixes - INFO - Validations: FAILED
2025-05-12 11:16:35,158 - verify_fixes - INFO - Calculated Fields: OK
2025-05-12 11:16:35,158 - verify_fixes - INFO - Lifecycle Management: OK
2025-05-12 11:16:35,158 - verify_fixes - INFO - Foreign Key Constraints: OK
2025-05-12 11:16:35,158 - verify_fixes - INFO - Database Connections: OK
2025-05-12 11:16:35,158 - verify_fixes - WARNING - Some checks failed. See verify_fixes.log for details.
2025-05-12 11:20:00,930 - verify_fixes - INFO - Starting verification checks
2025-05-12 11:20:00,930 - verify_fixes - INFO - === Checking Entity IDs ===
2025-05-12 11:20:00,930 - verify_fixes - INFO - Checking entity IDs
2025-05-12 11:20:00,935 - db_connection - INFO - Connected to database workflow_system on 172.18.0.5:5432
2025-05-12 11:20:00,937 - verify_fixes - INFO - All entity IDs use sequential IDs (E1, E2)
2025-05-12 11:20:00,937 - verify_fixes - INFO - All entity IDs use sequential IDs (E1, E2)
2025-05-12 11:20:00,937 - verify_fixes - INFO - === Checking Enum Values ===
2025-05-12 11:20:00,937 - verify_fixes - INFO - Checking enum values
2025-05-12 11:20:00,941 - db_connection - INFO - Connected to database workflow_system on 172.18.0.5:5432
2025-05-12 11:20:00,943 - verify_fixes - INFO - No enum attributes found
2025-05-12 11:20:00,943 - verify_fixes - INFO - No enum attributes found
2025-05-12 11:20:00,943 - verify_fixes - INFO - === Checking Validations ===
2025-05-12 11:20:00,943 - verify_fixes - INFO - Checking validations
2025-05-12 11:20:00,947 - db_connection - INFO - Connected to database workflow_system on 172.18.0.5:5432
2025-05-12 11:20:00,954 - db_connection - INFO - Connected to database workflow_system on 172.18.0.5:5432
2025-05-12 11:20:00,955 - verify_fixes - WARNING - No validations found
2025-05-12 11:20:00,955 - verify_fixes - INFO - Warning: No validations found
2025-05-12 11:20:00,955 - verify_fixes - INFO - === Checking Calculated Fields ===
2025-05-12 11:20:00,955 - verify_fixes - INFO - Checking calculated fields
2025-05-12 11:20:00,959 - db_connection - INFO - Connected to database workflow_system on 172.18.0.5:5432
2025-05-12 11:20:00,961 - verify_fixes - INFO - Found 1 calculated fields
2025-05-12 11:20:00,961 - verify_fixes - INFO - Calculated field 'CF001' for Employee.fullName:
2025-05-12 11:20:00,961 - verify_fixes - INFO -   - Formula: CONCAT(firstName, ' ', lastName)
2025-05-12 11:20:00,961 - verify_fixes - INFO -   - Logic Layer: Application
2025-05-12 11:20:00,961 - verify_fixes - INFO -   - Caching: Session
2025-05-12 11:20:00,961 - verify_fixes - INFO -   - Dependencies: ['Employee.firstName', 'Employee.lastName']
2025-05-12 11:20:00,965 - db_connection - INFO - Connected to database workflow_system on 172.18.0.5:5432
2025-05-12 11:20:00,966 - verify_fixes - INFO - Found 1 attributes marked as calculated fields
2025-05-12 11:20:00,966 - verify_fixes - INFO - Attribute Employee.fullName is marked as a calculated field
2025-05-12 11:20:00,966 - verify_fixes - INFO - Found 1 calculated fields
2025-05-12 11:20:00,966 - verify_fixes - INFO - Calculated field 'CF001' for Employee.fullName: CONCAT(firstName, ' ', lastName)
2025-05-12 11:20:00,966 - verify_fixes - INFO - Found 1 attributes marked as calculated fields
2025-05-12 11:20:00,966 - verify_fixes - INFO - Attribute Employee.fullName is marked as a calculated field
2025-05-12 11:20:00,966 - verify_fixes - INFO - === Checking Lifecycle Management ===
2025-05-12 11:20:00,966 - verify_fixes - INFO - Checking lifecycle management
2025-05-12 11:20:00,970 - db_connection - INFO - Connected to database workflow_system on 172.18.0.5:5432
2025-05-12 11:20:00,971 - verify_fixes - INFO - Found 3 lifecycle management entries
2025-05-12 11:20:00,971 - verify_fixes - INFO - Lifecycle management for Employee (archive):
2025-05-12 11:20:00,971 - verify_fixes - INFO -   - Trigger Type: Event-based
2025-05-12 11:20:00,971 - verify_fixes - INFO -   - Criteria: When Employee.status changes to 'Inactive' and remains so for 1 year
2025-05-12 11:20:00,971 - verify_fixes - INFO -   - Retention: 7 years
2025-05-12 11:20:00,971 - verify_fixes - INFO -   - Storage: Cold storage
2025-05-12 11:20:00,971 - verify_fixes - INFO -   - Access Pattern: Read-only through HR archive portal
2025-05-12 11:20:00,971 - verify_fixes - INFO -   - Restoration: Manual process requiring HR Director approval
2025-05-12 11:20:00,971 - verify_fixes - INFO - Lifecycle management for Employee (history):
2025-05-12 11:20:00,971 - verify_fixes - INFO -   - Tracked Attributes: Employee.salary, Employee.departmentId, Employee.managerId, Employee.status
2025-05-12 11:20:00,971 - verify_fixes - INFO -   - Tracking Method: Audit table
2025-05-12 11:20:00,972 - verify_fixes - INFO -   - Granularity: Change level
2025-05-12 11:20:00,972 - verify_fixes - INFO -   - Retention: 7 years
2025-05-12 11:20:00,972 - verify_fixes - INFO -   - Access Control: HR Managers and Compliance Officers only
2025-05-12 11:20:00,972 - verify_fixes - INFO - Lifecycle management for Department (archive):
2025-05-12 11:20:00,972 - verify_fixes - INFO -   - Trigger Type: Event-based
2025-05-12 11:20:00,972 - verify_fixes - INFO -   - Criteria: When Department is marked as 'Dissolved'
2025-05-12 11:20:00,972 - verify_fixes - INFO -   - Retention: 10 years
2025-05-12 11:20:00,972 - verify_fixes - INFO -   - Storage: Cold storage
2025-05-12 11:20:00,972 - verify_fixes - INFO -   - Access Pattern: Read-only through Admin portal
2025-05-12 11:20:00,972 - verify_fixes - INFO -   - Restoration: Manual process requiring CEO approval
2025-05-12 11:20:00,972 - verify_fixes - INFO - Found 3 lifecycle management entries
2025-05-12 11:20:00,972 - verify_fixes - INFO - Archive strategy for Employee: Event-based - When Employee.status changes to 'Inactive' and remains so for 1 year
2025-05-12 11:20:00,972 - verify_fixes - INFO - History tracking for Employee: Audit table - Change level
2025-05-12 11:20:00,972 - verify_fixes - INFO - Archive strategy for Department: Event-based - When Department is marked as 'Dissolved'
2025-05-12 11:20:00,972 - verify_fixes - INFO - === Checking Foreign Key Constraints ===
2025-05-12 11:20:00,972 - verify_fixes - INFO - Checking for duplicate foreign key constraints
2025-05-12 11:20:00,976 - db_connection - INFO - Connected to database workflow_system on 172.18.0.5:5432
2025-05-12 11:20:01,044 - verify_fixes - INFO - No duplicate foreign key constraints found
2025-05-12 11:20:01,045 - verify_fixes - INFO - No duplicate foreign key constraints found
2025-05-12 11:20:01,045 - verify_fixes - INFO - === Checking Database Connections ===
2025-05-12 11:20:01,045 - verify_fixes - INFO - Checking database connections
2025-05-12 11:20:01,045 - verify_fixes - INFO - component_deployer.py has connection pooling
2025-05-12 11:20:01,045 - verify_fixes - INFO - component_deployer.py has connection pooling
2025-05-12 11:20:01,045 - verify_fixes - INFO - === Verification Summary ===
2025-05-12 11:20:01,045 - verify_fixes - INFO - Entity IDs: OK
2025-05-12 11:20:01,045 - verify_fixes - INFO - Enum Values: OK
2025-05-12 11:20:01,045 - verify_fixes - INFO - Validations: FAILED
2025-05-12 11:20:01,045 - verify_fixes - INFO - Calculated Fields: OK
2025-05-12 11:20:01,045 - verify_fixes - INFO - Lifecycle Management: OK
2025-05-12 11:20:01,045 - verify_fixes - INFO - Foreign Key Constraints: OK
2025-05-12 11:20:01,045 - verify_fixes - INFO - Database Connections: OK
2025-05-12 11:20:01,045 - verify_fixes - WARNING - Some checks failed. See verify_fixes.log for details.
2025-05-12 11:22:07,882 - verify_fixes - INFO - Starting verification checks
2025-05-12 11:22:07,882 - verify_fixes - INFO - === Checking Entity IDs ===
2025-05-12 11:22:07,882 - verify_fixes - INFO - Checking entity IDs
2025-05-12 11:22:07,888 - db_connection - INFO - Connected to database workflow_system on 172.18.0.5:5432
2025-05-12 11:22:07,889 - verify_fixes - INFO - All entity IDs use sequential IDs (E1, E2)
2025-05-12 11:22:07,889 - verify_fixes - INFO - All entity IDs use sequential IDs (E1, E2)
2025-05-12 11:22:07,889 - verify_fixes - INFO - === Checking Enum Values ===
2025-05-12 11:22:07,889 - verify_fixes - INFO - Checking enum values
2025-05-12 11:22:07,894 - db_connection - INFO - Connected to database workflow_system on 172.18.0.5:5432
2025-05-12 11:22:07,896 - verify_fixes - INFO - No enum attributes found
2025-05-12 11:22:07,896 - verify_fixes - INFO - No enum attributes found
2025-05-12 11:22:07,896 - verify_fixes - INFO - === Checking Validations ===
2025-05-12 11:22:07,896 - verify_fixes - INFO - Checking validations
2025-05-12 11:22:07,899 - db_connection - INFO - Connected to database workflow_system on 172.18.0.5:5432
2025-05-12 11:22:07,905 - db_connection - INFO - Connected to database workflow_system on 172.18.0.5:5432
2025-05-12 11:22:07,906 - verify_fixes - WARNING - No validations found
2025-05-12 11:22:07,906 - verify_fixes - INFO - Warning: No validations found
2025-05-12 11:22:07,906 - verify_fixes - INFO - === Checking Calculated Fields ===
2025-05-12 11:22:07,906 - verify_fixes - INFO - Checking calculated fields
2025-05-12 11:22:07,910 - db_connection - INFO - Connected to database workflow_system on 172.18.0.5:5432
2025-05-12 11:22:07,911 - verify_fixes - INFO - Found 1 calculated fields
2025-05-12 11:22:07,911 - verify_fixes - INFO - Calculated field 'CF001' for Employee.fullName:
2025-05-12 11:22:07,911 - verify_fixes - INFO -   - Formula: CONCAT(firstName, ' ', lastName)
2025-05-12 11:22:07,911 - verify_fixes - INFO -   - Logic Layer: Application
2025-05-12 11:22:07,911 - verify_fixes - INFO -   - Caching: Session
2025-05-12 11:22:07,911 - verify_fixes - INFO -   - Dependencies: ['Employee.firstName', 'Employee.lastName']
2025-05-12 11:22:07,915 - db_connection - INFO - Connected to database workflow_system on 172.18.0.5:5432
2025-05-12 11:22:07,917 - verify_fixes - INFO - Found 1 attributes marked as calculated fields
2025-05-12 11:22:07,917 - verify_fixes - INFO - Attribute Employee.fullName is marked as a calculated field
2025-05-12 11:22:07,917 - verify_fixes - INFO - Found 1 calculated fields
2025-05-12 11:22:07,917 - verify_fixes - INFO - Calculated field 'CF001' for Employee.fullName: CONCAT(firstName, ' ', lastName)
2025-05-12 11:22:07,917 - verify_fixes - INFO - Found 1 attributes marked as calculated fields
2025-05-12 11:22:07,917 - verify_fixes - INFO - Attribute Employee.fullName is marked as a calculated field
2025-05-12 11:22:07,917 - verify_fixes - INFO - === Checking Lifecycle Management ===
2025-05-12 11:22:07,917 - verify_fixes - INFO - Checking lifecycle management
2025-05-12 11:22:07,921 - db_connection - INFO - Connected to database workflow_system on 172.18.0.5:5432
2025-05-12 11:22:07,923 - verify_fixes - INFO - Found 3 lifecycle management entries
2025-05-12 11:22:07,923 - verify_fixes - INFO - Lifecycle management for Employee (archive):
2025-05-12 11:22:07,923 - verify_fixes - INFO -   - Trigger Type: Event-based
2025-05-12 11:22:07,923 - verify_fixes - INFO -   - Criteria: When Employee.status changes to 'Inactive' and remains so for 1 year
2025-05-12 11:22:07,923 - verify_fixes - INFO -   - Retention: 7 years
2025-05-12 11:22:07,923 - verify_fixes - INFO -   - Storage: Cold storage
2025-05-12 11:22:07,923 - verify_fixes - INFO -   - Access Pattern: Read-only through HR archive portal
2025-05-12 11:22:07,923 - verify_fixes - INFO -   - Restoration: Manual process requiring HR Director approval
2025-05-12 11:22:07,923 - verify_fixes - INFO - Lifecycle management for Employee (history):
2025-05-12 11:22:07,923 - verify_fixes - INFO -   - Tracked Attributes: Employee.salary, Employee.departmentId, Employee.managerId, Employee.status
2025-05-12 11:22:07,923 - verify_fixes - INFO -   - Tracking Method: Audit table
2025-05-12 11:22:07,923 - verify_fixes - INFO -   - Granularity: Change level
2025-05-12 11:22:07,923 - verify_fixes - INFO -   - Retention: 7 years
2025-05-12 11:22:07,923 - verify_fixes - INFO -   - Access Control: HR Managers and Compliance Officers only
2025-05-12 11:22:07,923 - verify_fixes - INFO - Lifecycle management for Department (archive):
2025-05-12 11:22:07,923 - verify_fixes - INFO -   - Trigger Type: Event-based
2025-05-12 11:22:07,923 - verify_fixes - INFO -   - Criteria: When Department is marked as 'Dissolved'
2025-05-12 11:22:07,923 - verify_fixes - INFO -   - Retention: 10 years
2025-05-12 11:22:07,923 - verify_fixes - INFO -   - Storage: Cold storage
2025-05-12 11:22:07,923 - verify_fixes - INFO -   - Access Pattern: Read-only through Admin portal
2025-05-12 11:22:07,923 - verify_fixes - INFO -   - Restoration: Manual process requiring CEO approval
2025-05-12 11:22:07,923 - verify_fixes - INFO - Found 3 lifecycle management entries
2025-05-12 11:22:07,923 - verify_fixes - INFO - Archive strategy for Employee: Event-based - When Employee.status changes to 'Inactive' and remains so for 1 year
2025-05-12 11:22:07,923 - verify_fixes - INFO - History tracking for Employee: Audit table - Change level
2025-05-12 11:22:07,923 - verify_fixes - INFO - Archive strategy for Department: Event-based - When Department is marked as 'Dissolved'
2025-05-12 11:22:07,923 - verify_fixes - INFO - === Checking Foreign Key Constraints ===
2025-05-12 11:22:07,923 - verify_fixes - INFO - Checking for duplicate foreign key constraints
2025-05-12 11:22:07,927 - db_connection - INFO - Connected to database workflow_system on 172.18.0.5:5432
2025-05-12 11:22:07,995 - verify_fixes - INFO - No duplicate foreign key constraints found
2025-05-12 11:22:07,996 - verify_fixes - INFO - No duplicate foreign key constraints found
2025-05-12 11:22:07,996 - verify_fixes - INFO - === Checking Database Connections ===
2025-05-12 11:22:07,996 - verify_fixes - INFO - Checking database connections
2025-05-12 11:22:07,996 - verify_fixes - INFO - component_deployer.py has connection pooling
2025-05-12 11:22:07,996 - verify_fixes - INFO - component_deployer.py has connection pooling
2025-05-12 11:22:07,996 - verify_fixes - INFO - === Verification Summary ===
2025-05-12 11:22:07,996 - verify_fixes - INFO - Entity IDs: OK
2025-05-12 11:22:07,996 - verify_fixes - INFO - Enum Values: OK
2025-05-12 11:22:07,996 - verify_fixes - INFO - Validations: FAILED
2025-05-12 11:22:07,996 - verify_fixes - INFO - Calculated Fields: OK
2025-05-12 11:22:07,996 - verify_fixes - INFO - Lifecycle Management: OK
2025-05-12 11:22:07,996 - verify_fixes - INFO - Foreign Key Constraints: OK
2025-05-12 11:22:07,996 - verify_fixes - INFO - Database Connections: OK
2025-05-12 11:22:07,996 - verify_fixes - WARNING - Some checks failed. See verify_fixes.log for details.
2025-05-12 11:57:33,672 - verify_fixes - INFO - Starting verification checks
2025-05-12 11:57:33,672 - verify_fixes - INFO - === Checking Entity IDs ===
2025-05-12 11:57:33,672 - verify_fixes - INFO - Checking entity IDs
2025-05-12 11:57:33,678 - db_connection - INFO - Connected to database workflow_system on 172.18.0.5:5432
2025-05-12 11:57:33,679 - verify_fixes - INFO - All entity IDs use sequential IDs (E1, E2)
2025-05-12 11:57:33,679 - verify_fixes - INFO - All entity IDs use sequential IDs (E1, E2)
2025-05-12 11:57:33,679 - verify_fixes - INFO - === Checking Enum Values ===
2025-05-12 11:57:33,679 - verify_fixes - INFO - Checking enum values
2025-05-12 11:57:33,684 - db_connection - INFO - Connected to database workflow_system on 172.18.0.5:5432
2025-05-12 11:57:33,686 - verify_fixes - INFO - No enum attributes found
2025-05-12 11:57:33,686 - verify_fixes - INFO - No enum attributes found
2025-05-12 11:57:33,686 - verify_fixes - INFO - === Checking Validations ===
2025-05-12 11:57:33,686 - verify_fixes - INFO - Checking validations
2025-05-12 11:57:33,692 - db_connection - INFO - Connected to database workflow_system on 172.18.0.5:5432
2025-05-12 11:57:33,698 - db_connection - INFO - Connected to database workflow_system on 172.18.0.5:5432
2025-05-12 11:57:33,699 - verify_fixes - WARNING - No validations found
2025-05-12 11:57:33,700 - verify_fixes - INFO - Warning: No validations found
2025-05-12 11:57:33,700 - verify_fixes - INFO - === Checking Calculated Fields ===
2025-05-12 11:57:33,700 - verify_fixes - INFO - Checking calculated fields
2025-05-12 11:57:33,704 - db_connection - INFO - Connected to database workflow_system on 172.18.0.5:5432
2025-05-12 11:57:33,706 - verify_fixes - INFO - Found 1 calculated fields
2025-05-12 11:57:33,706 - verify_fixes - INFO - Calculated field 'CF001' for Employee.fullName:
2025-05-12 11:57:33,706 - verify_fixes - INFO -   - Formula: CONCAT(firstName, ' ', lastName)
2025-05-12 11:57:33,706 - verify_fixes - INFO -   - Logic Layer: Application
2025-05-12 11:57:33,706 - verify_fixes - INFO -   - Caching: Session
2025-05-12 11:57:33,706 - verify_fixes - INFO -   - Dependencies: ['Employee.firstName', 'Employee.lastName']
2025-05-12 11:57:33,710 - db_connection - INFO - Connected to database workflow_system on 172.18.0.5:5432
2025-05-12 11:57:33,712 - verify_fixes - INFO - Found 1 attributes marked as calculated fields
2025-05-12 11:57:33,712 - verify_fixes - INFO - Attribute Employee.fullName is marked as a calculated field
2025-05-12 11:57:33,712 - verify_fixes - INFO - Found 1 calculated fields
2025-05-12 11:57:33,712 - verify_fixes - INFO - Calculated field 'CF001' for Employee.fullName: CONCAT(firstName, ' ', lastName)
2025-05-12 11:57:33,712 - verify_fixes - INFO - Found 1 attributes marked as calculated fields
2025-05-12 11:57:33,712 - verify_fixes - INFO - Attribute Employee.fullName is marked as a calculated field
2025-05-12 11:57:33,712 - verify_fixes - INFO - === Checking Lifecycle Management ===
2025-05-12 11:57:33,712 - verify_fixes - INFO - Checking lifecycle management
2025-05-12 11:57:33,717 - db_connection - INFO - Connected to database workflow_system on 172.18.0.5:5432
2025-05-12 11:57:33,718 - verify_fixes - INFO - Found 3 lifecycle management entries
2025-05-12 11:57:33,719 - verify_fixes - INFO - Lifecycle management for Employee (history):
2025-05-12 11:57:33,719 - verify_fixes - INFO -   - Tracked Attributes: Employee.salary, Employee.departmentId, Employee.managerId, Employee.status
2025-05-12 11:57:33,719 - verify_fixes - INFO -   - Tracking Method: Audit table
2025-05-12 11:57:33,719 - verify_fixes - INFO -   - Granularity: Change level
2025-05-12 11:57:33,719 - verify_fixes - INFO -   - Retention: 7 years
2025-05-12 11:57:33,719 - verify_fixes - INFO -   - Access Control: HR Managers and Compliance Officers only
2025-05-12 11:57:33,719 - verify_fixes - INFO - Lifecycle management for Employee (archive):
2025-05-12 11:57:33,719 - verify_fixes - INFO -   - Trigger Type: Event-based
2025-05-12 11:57:33,719 - verify_fixes - INFO -   - Criteria: When Employee.status changes to 'Inactive' and remains so for 1 year
2025-05-12 11:57:33,719 - verify_fixes - INFO -   - Retention: 7 years
2025-05-12 11:57:33,719 - verify_fixes - INFO -   - Storage: Cold storage
2025-05-12 11:57:33,719 - verify_fixes - INFO -   - Access Pattern: Read-only through HR archive portal
2025-05-12 11:57:33,719 - verify_fixes - INFO -   - Restoration: Manual process requiring HR Director approval
2025-05-12 11:57:33,719 - verify_fixes - INFO - Lifecycle management for Department (archive):
2025-05-12 11:57:33,719 - verify_fixes - INFO -   - Trigger Type: Event-based
2025-05-12 11:57:33,719 - verify_fixes - INFO -   - Criteria: When Department is marked as 'Dissolved'
2025-05-12 11:57:33,719 - verify_fixes - INFO -   - Retention: 10 years
2025-05-12 11:57:33,719 - verify_fixes - INFO -   - Storage: Cold storage
2025-05-12 11:57:33,719 - verify_fixes - INFO -   - Access Pattern: Read-only through Admin portal
2025-05-12 11:57:33,719 - verify_fixes - INFO -   - Restoration: Manual process requiring CEO approval
2025-05-12 11:57:33,719 - verify_fixes - INFO - Found 3 lifecycle management entries
2025-05-12 11:57:33,719 - verify_fixes - INFO - History tracking for Employee: Audit table - Change level
2025-05-12 11:57:33,719 - verify_fixes - INFO - Archive strategy for Employee: Event-based - When Employee.status changes to 'Inactive' and remains so for 1 year
2025-05-12 11:57:33,719 - verify_fixes - INFO - Archive strategy for Department: Event-based - When Department is marked as 'Dissolved'
2025-05-12 11:57:33,719 - verify_fixes - INFO - === Checking Foreign Key Constraints ===
2025-05-12 11:57:33,719 - verify_fixes - INFO - Checking for duplicate foreign key constraints
2025-05-12 11:57:33,723 - db_connection - INFO - Connected to database workflow_system on 172.18.0.5:5432
2025-05-12 11:57:33,792 - verify_fixes - INFO - No duplicate foreign key constraints found
2025-05-12 11:57:33,792 - verify_fixes - INFO - No duplicate foreign key constraints found
2025-05-12 11:57:33,792 - verify_fixes - INFO - === Checking Database Connections ===
2025-05-12 11:57:33,792 - verify_fixes - INFO - Checking database connections
2025-05-12 11:57:33,792 - verify_fixes - INFO - component_deployer.py has connection pooling
2025-05-12 11:57:33,792 - verify_fixes - INFO - component_deployer.py has connection pooling
2025-05-12 11:57:33,792 - verify_fixes - INFO - === Verification Summary ===
2025-05-12 11:57:33,792 - verify_fixes - INFO - Entity IDs: OK
2025-05-12 11:57:33,792 - verify_fixes - INFO - Enum Values: OK
2025-05-12 11:57:33,792 - verify_fixes - INFO - Validations: FAILED
2025-05-12 11:57:33,792 - verify_fixes - INFO - Calculated Fields: OK
2025-05-12 11:57:33,792 - verify_fixes - INFO - Lifecycle Management: OK
2025-05-12 11:57:33,792 - verify_fixes - INFO - Foreign Key Constraints: OK
2025-05-12 11:57:33,792 - verify_fixes - INFO - Database Connections: OK
2025-05-12 11:57:33,792 - verify_fixes - WARNING - Some checks failed. See verify_fixes.log for details.
2025-05-12 12:11:59,708 - verify_fixes - INFO - Starting verification checks
2025-05-12 12:11:59,709 - verify_fixes - INFO - === Checking Entity IDs ===
2025-05-12 12:11:59,709 - verify_fixes - INFO - Checking entity IDs
2025-05-12 12:11:59,714 - db_connection - INFO - Connected to database workflow_system on 172.18.0.5:5432
2025-05-12 12:11:59,715 - verify_fixes - INFO - All entity IDs use sequential IDs (E1, E2)
2025-05-12 12:11:59,715 - verify_fixes - INFO - All entity IDs use sequential IDs (E1, E2)
2025-05-12 12:11:59,715 - verify_fixes - INFO - === Checking Enum Values ===
2025-05-12 12:11:59,715 - verify_fixes - INFO - Checking enum values
2025-05-12 12:11:59,720 - db_connection - INFO - Connected to database workflow_system on 172.18.0.5:5432
2025-05-12 12:11:59,722 - verify_fixes - INFO - No enum attributes found
2025-05-12 12:11:59,722 - verify_fixes - INFO - No enum attributes found
2025-05-12 12:11:59,722 - verify_fixes - INFO - === Checking Validations ===
2025-05-12 12:11:59,722 - verify_fixes - INFO - Checking validations
2025-05-12 12:11:59,725 - db_connection - INFO - Connected to database workflow_system on 172.18.0.5:5432
2025-05-12 12:11:59,731 - db_connection - INFO - Connected to database workflow_system on 172.18.0.5:5432
2025-05-12 12:11:59,732 - verify_fixes - WARNING - No validations found
2025-05-12 12:11:59,732 - verify_fixes - INFO - Warning: No validations found
2025-05-12 12:11:59,732 - verify_fixes - INFO - === Checking Calculated Fields ===
2025-05-12 12:11:59,732 - verify_fixes - INFO - Checking calculated fields
2025-05-12 12:11:59,735 - db_connection - INFO - Connected to database workflow_system on 172.18.0.5:5432
2025-05-12 12:11:59,737 - verify_fixes - INFO - Found 1 calculated fields
2025-05-12 12:11:59,737 - verify_fixes - INFO - Calculated field 'CF001' for Employee.fullName:
2025-05-12 12:11:59,737 - verify_fixes - INFO -   - Formula: CONCAT(firstName, ' ', lastName)
2025-05-12 12:11:59,737 - verify_fixes - INFO -   - Logic Layer: Application
2025-05-12 12:11:59,737 - verify_fixes - INFO -   - Caching: Session
2025-05-12 12:11:59,737 - verify_fixes - INFO -   - Dependencies: ['Employee.firstName', 'Employee.lastName']
2025-05-12 12:11:59,741 - db_connection - INFO - Connected to database workflow_system on 172.18.0.5:5432
2025-05-12 12:11:59,742 - verify_fixes - INFO - Found 1 attributes marked as calculated fields
2025-05-12 12:11:59,742 - verify_fixes - INFO - Attribute Employee.fullName is marked as a calculated field
2025-05-12 12:11:59,742 - verify_fixes - INFO - Found 1 calculated fields
2025-05-12 12:11:59,742 - verify_fixes - INFO - Calculated field 'CF001' for Employee.fullName: CONCAT(firstName, ' ', lastName)
2025-05-12 12:11:59,742 - verify_fixes - INFO - Found 1 attributes marked as calculated fields
2025-05-12 12:11:59,742 - verify_fixes - INFO - Attribute Employee.fullName is marked as a calculated field
2025-05-12 12:11:59,742 - verify_fixes - INFO - === Checking Lifecycle Management ===
2025-05-12 12:11:59,742 - verify_fixes - INFO - Checking lifecycle management
2025-05-12 12:11:59,745 - db_connection - INFO - Connected to database workflow_system on 172.18.0.5:5432
2025-05-12 12:11:59,747 - verify_fixes - INFO - Found 3 lifecycle management entries
2025-05-12 12:11:59,747 - verify_fixes - INFO - Lifecycle management for Employee (history):
2025-05-12 12:11:59,747 - verify_fixes - INFO -   - Tracked Attributes: Employee.salary, Employee.departmentId, Employee.managerId, Employee.status
2025-05-12 12:11:59,747 - verify_fixes - INFO -   - Tracking Method: Audit table
2025-05-12 12:11:59,747 - verify_fixes - INFO -   - Granularity: Change level
2025-05-12 12:11:59,747 - verify_fixes - INFO -   - Retention: 7 years
2025-05-12 12:11:59,747 - verify_fixes - INFO -   - Access Control: HR Managers and Compliance Officers only
2025-05-12 12:11:59,747 - verify_fixes - INFO - Lifecycle management for Employee (archive):
2025-05-12 12:11:59,747 - verify_fixes - INFO -   - Trigger Type: Event-based
2025-05-12 12:11:59,747 - verify_fixes - INFO -   - Criteria: When Employee.status changes to 'Inactive' and remains so for 1 year
2025-05-12 12:11:59,747 - verify_fixes - INFO -   - Retention: 7 years
2025-05-12 12:11:59,747 - verify_fixes - INFO -   - Storage: Cold storage
2025-05-12 12:11:59,747 - verify_fixes - INFO -   - Access Pattern: Read-only through HR archive portal
2025-05-12 12:11:59,747 - verify_fixes - INFO -   - Restoration: Manual process requiring HR Director approval
2025-05-12 12:11:59,747 - verify_fixes - INFO - Lifecycle management for Department (archive):
2025-05-12 12:11:59,747 - verify_fixes - INFO -   - Trigger Type: Event-based
2025-05-12 12:11:59,747 - verify_fixes - INFO -   - Criteria: When Department is marked as 'Dissolved'
2025-05-12 12:11:59,747 - verify_fixes - INFO -   - Retention: 10 years
2025-05-12 12:11:59,747 - verify_fixes - INFO -   - Storage: Cold storage
2025-05-12 12:11:59,747 - verify_fixes - INFO -   - Access Pattern: Read-only through Admin portal
2025-05-12 12:11:59,747 - verify_fixes - INFO -   - Restoration: Manual process requiring CEO approval
2025-05-12 12:11:59,747 - verify_fixes - INFO - Found 3 lifecycle management entries
2025-05-12 12:11:59,747 - verify_fixes - INFO - History tracking for Employee: Audit table - Change level
2025-05-12 12:11:59,747 - verify_fixes - INFO - Archive strategy for Employee: Event-based - When Employee.status changes to 'Inactive' and remains so for 1 year
2025-05-12 12:11:59,748 - verify_fixes - INFO - Archive strategy for Department: Event-based - When Department is marked as 'Dissolved'
2025-05-12 12:11:59,748 - verify_fixes - INFO - === Checking Foreign Key Constraints ===
2025-05-12 12:11:59,748 - verify_fixes - INFO - Checking for duplicate foreign key constraints
2025-05-12 12:11:59,751 - db_connection - INFO - Connected to database workflow_system on 172.18.0.5:5432
2025-05-12 12:11:59,820 - verify_fixes - INFO - No duplicate foreign key constraints found
2025-05-12 12:11:59,820 - verify_fixes - INFO - No duplicate foreign key constraints found
2025-05-12 12:11:59,820 - verify_fixes - INFO - === Checking Database Connections ===
2025-05-12 12:11:59,820 - verify_fixes - INFO - Checking database connections
2025-05-12 12:11:59,820 - verify_fixes - INFO - component_deployer.py has connection pooling
2025-05-12 12:11:59,820 - verify_fixes - INFO - component_deployer.py has connection pooling
2025-05-12 12:11:59,820 - verify_fixes - INFO - === Verification Summary ===
2025-05-12 12:11:59,820 - verify_fixes - INFO - Entity IDs: OK
2025-05-12 12:11:59,820 - verify_fixes - INFO - Enum Values: OK
2025-05-12 12:11:59,820 - verify_fixes - INFO - Validations: FAILED
2025-05-12 12:11:59,820 - verify_fixes - INFO - Calculated Fields: OK
2025-05-12 12:11:59,820 - verify_fixes - INFO - Lifecycle Management: OK
2025-05-12 12:11:59,820 - verify_fixes - INFO - Foreign Key Constraints: OK
2025-05-12 12:11:59,820 - verify_fixes - INFO - Database Connections: OK
2025-05-12 12:11:59,820 - verify_fixes - WARNING - Some checks failed. See verify_fixes.log for details.
2025-05-12 12:14:47,867 - verify_fixes - INFO - Starting verification checks
2025-05-12 12:14:47,867 - verify_fixes - INFO - === Checking Entity IDs ===
2025-05-12 12:14:47,868 - verify_fixes - INFO - Checking entity IDs
2025-05-12 12:14:47,873 - db_connection - INFO - Connected to database workflow_system on 172.18.0.5:5432
2025-05-12 12:14:47,875 - verify_fixes - INFO - All entity IDs use sequential IDs (E1, E2)
2025-05-12 12:14:47,875 - verify_fixes - INFO - All entity IDs use sequential IDs (E1, E2)
2025-05-12 12:14:47,875 - verify_fixes - INFO - === Checking Enum Values ===
2025-05-12 12:14:47,875 - verify_fixes - INFO - Checking enum values
2025-05-12 12:14:47,880 - db_connection - INFO - Connected to database workflow_system on 172.18.0.5:5432
2025-05-12 12:14:47,881 - verify_fixes - INFO - No enum attributes found
2025-05-12 12:14:47,881 - verify_fixes - INFO - No enum attributes found
2025-05-12 12:14:47,881 - verify_fixes - INFO - === Checking Validations ===
2025-05-12 12:14:47,881 - verify_fixes - INFO - Checking validations
2025-05-12 12:14:47,885 - db_connection - INFO - Connected to database workflow_system on 172.18.0.5:5432
2025-05-12 12:14:47,891 - db_connection - INFO - Connected to database workflow_system on 172.18.0.5:5432
2025-05-12 12:14:47,896 - db_connection - INFO - Connected to database workflow_system on 172.18.0.5:5432
2025-05-12 12:14:47,897 - verify_fixes - INFO - Found 1 validations
2025-05-12 12:14:47,897 - verify_fixes - INFO - Validation 'test_validation' for Employee.status (Active:
2025-05-12 12:14:47,897 - verify_fixes - INFO -   - Type: expression
2025-05-12 12:14:47,897 - verify_fixes - INFO -   - Expression: Test expression
2025-05-12 12:14:47,897 - verify_fixes - INFO - Found 1 validations
2025-05-12 12:14:47,897 - verify_fixes - INFO - Validation 'test_validation' for Employee.status (Active: expression - Test expression
2025-05-12 12:14:47,898 - verify_fixes - INFO - === Checking Calculated Fields ===
2025-05-12 12:14:47,898 - verify_fixes - INFO - Checking calculated fields
2025-05-12 12:14:47,901 - db_connection - INFO - Connected to database workflow_system on 172.18.0.5:5432
2025-05-12 12:14:47,903 - verify_fixes - INFO - Found 1 calculated fields
2025-05-12 12:14:47,903 - verify_fixes - INFO - Calculated field 'CF001' for Employee.fullName:
2025-05-12 12:14:47,903 - verify_fixes - INFO -   - Formula: CONCAT(firstName, ' ', lastName)
2025-05-12 12:14:47,903 - verify_fixes - INFO -   - Logic Layer: Application
2025-05-12 12:14:47,903 - verify_fixes - INFO -   - Caching: Session
2025-05-12 12:14:47,903 - verify_fixes - INFO -   - Dependencies: ['Employee.firstName', 'Employee.lastName']
2025-05-12 12:14:47,907 - db_connection - INFO - Connected to database workflow_system on 172.18.0.5:5432
2025-05-12 12:14:47,909 - verify_fixes - INFO - Found 1 attributes marked as calculated fields
2025-05-12 12:14:47,909 - verify_fixes - INFO - Attribute Employee.fullName is marked as a calculated field
2025-05-12 12:14:47,909 - verify_fixes - INFO - Found 1 calculated fields
2025-05-12 12:14:47,909 - verify_fixes - INFO - Calculated field 'CF001' for Employee.fullName: CONCAT(firstName, ' ', lastName)
2025-05-12 12:14:47,909 - verify_fixes - INFO - Found 1 attributes marked as calculated fields
2025-05-12 12:14:47,909 - verify_fixes - INFO - Attribute Employee.fullName is marked as a calculated field
2025-05-12 12:14:47,909 - verify_fixes - INFO - === Checking Lifecycle Management ===
2025-05-12 12:14:47,909 - verify_fixes - INFO - Checking lifecycle management
2025-05-12 12:14:47,913 - db_connection - INFO - Connected to database workflow_system on 172.18.0.5:5432
2025-05-12 12:14:47,914 - verify_fixes - INFO - Found 3 lifecycle management entries
2025-05-12 12:14:47,914 - verify_fixes - INFO - Lifecycle management for Employee (history):
2025-05-12 12:14:47,914 - verify_fixes - INFO -   - Tracked Attributes: Employee.salary, Employee.departmentId, Employee.managerId, Employee.status
2025-05-12 12:14:47,914 - verify_fixes - INFO -   - Tracking Method: Audit table
2025-05-12 12:14:47,914 - verify_fixes - INFO -   - Granularity: Change level
2025-05-12 12:14:47,914 - verify_fixes - INFO -   - Retention: 7 years
2025-05-12 12:14:47,914 - verify_fixes - INFO -   - Access Control: HR Managers and Compliance Officers only
2025-05-12 12:14:47,914 - verify_fixes - INFO - Lifecycle management for Employee (archive):
2025-05-12 12:14:47,914 - verify_fixes - INFO -   - Trigger Type: Event-based
2025-05-12 12:14:47,914 - verify_fixes - INFO -   - Criteria: When Employee.status changes to 'Inactive' and remains so for 1 year
2025-05-12 12:14:47,914 - verify_fixes - INFO -   - Retention: 7 years
2025-05-12 12:14:47,914 - verify_fixes - INFO -   - Storage: Cold storage
2025-05-12 12:14:47,914 - verify_fixes - INFO -   - Access Pattern: Read-only through HR archive portal
2025-05-12 12:14:47,914 - verify_fixes - INFO -   - Restoration: Manual process requiring HR Director approval
2025-05-12 12:14:47,915 - verify_fixes - INFO - Lifecycle management for Department (archive):
2025-05-12 12:14:47,915 - verify_fixes - INFO -   - Trigger Type: Event-based
2025-05-12 12:14:47,915 - verify_fixes - INFO -   - Criteria: When Department is marked as 'Dissolved'
2025-05-12 12:14:47,915 - verify_fixes - INFO -   - Retention: 10 years
2025-05-12 12:14:47,915 - verify_fixes - INFO -   - Storage: Cold storage
2025-05-12 12:14:47,915 - verify_fixes - INFO -   - Access Pattern: Read-only through Admin portal
2025-05-12 12:14:47,915 - verify_fixes - INFO -   - Restoration: Manual process requiring CEO approval
2025-05-12 12:14:47,915 - verify_fixes - INFO - Found 3 lifecycle management entries
2025-05-12 12:14:47,915 - verify_fixes - INFO - History tracking for Employee: Audit table - Change level
2025-05-12 12:14:47,915 - verify_fixes - INFO - Archive strategy for Employee: Event-based - When Employee.status changes to 'Inactive' and remains so for 1 year
2025-05-12 12:14:47,915 - verify_fixes - INFO - Archive strategy for Department: Event-based - When Department is marked as 'Dissolved'
2025-05-12 12:14:47,915 - verify_fixes - INFO - === Checking Foreign Key Constraints ===
2025-05-12 12:14:47,915 - verify_fixes - INFO - Checking for duplicate foreign key constraints
2025-05-12 12:14:47,919 - db_connection - INFO - Connected to database workflow_system on 172.18.0.5:5432
2025-05-12 12:14:47,987 - verify_fixes - INFO - No duplicate foreign key constraints found
2025-05-12 12:14:47,987 - verify_fixes - INFO - No duplicate foreign key constraints found
2025-05-12 12:14:47,987 - verify_fixes - INFO - === Checking Database Connections ===
2025-05-12 12:14:47,987 - verify_fixes - INFO - Checking database connections
2025-05-12 12:14:47,987 - verify_fixes - INFO - component_deployer.py has connection pooling
2025-05-12 12:14:47,987 - verify_fixes - INFO - component_deployer.py has connection pooling
2025-05-12 12:14:47,987 - verify_fixes - INFO - === Verification Summary ===
2025-05-12 12:14:47,987 - verify_fixes - INFO - Entity IDs: OK
2025-05-12 12:14:47,987 - verify_fixes - INFO - Enum Values: OK
2025-05-12 12:14:47,987 - verify_fixes - INFO - Validations: OK
2025-05-12 12:14:47,987 - verify_fixes - INFO - Calculated Fields: OK
2025-05-12 12:14:47,987 - verify_fixes - INFO - Lifecycle Management: OK
2025-05-12 12:14:47,987 - verify_fixes - INFO - Foreign Key Constraints: OK
2025-05-12 12:14:47,987 - verify_fixes - INFO - Database Connections: OK
2025-05-12 12:14:47,987 - verify_fixes - INFO - All checks passed!
