# 🏢 Organizational Hierarchy & User Profile API - V2 Implementation

## 🎯 Overview

This document describes the complete implementation of organizational hierarchy and enhanced user profile features for the V2 Authentication API. The implementation provides granular permission management, multi-team membership support, and comprehensive organizational context.

## 🚀 Key Features Implemented

### 1. **Multi-Team Membership Support**
- Users can belong to multiple teams simultaneously
- Primary team designation for main team affiliation
- Team-based colleague discovery

### 2. **Granular Permission System**
- **Entity-level permissions**: Access to specific entities (E1, E2, E3, etc.)
- **Attribute-level permissions**: Access to specific attributes (e.g., `e1_leaveapplication.status`)
- **Global Objective permissions**: Access to specific GOs (GO001, GO002, etc.)
- **Local Objective permissions**: Access to specific LOs (LO001, LO002, etc.)
- **Book-level permissions**: Access to specific books (B001, B002, etc.)
- **Chapter-level permissions**: Access to specific chapters (C001, C002, etc.)

### 3. **CRUD Permission Actions**
- **Create**: Permission to create new records
- **Read**: Permission to view/read records
- **Update**: Permission to modify existing records
- **Delete**: Permission to remove records

### 4. **Organizational Hierarchy**
- Department-based organization structure
- Manager-subordinate relationships (`reports_to_user_id`)
- Organizational levels (executive, senior_management, etc.)
- Hierarchy path tracking

### 5. **Role-Based KPIs**
- KPI assignments based on user roles
- Target tracking and measurement frequency
- Performance formula definitions

## 📊 Database Schema

### Core Tables Added

```sql
-- Organizational structure
workflow_runtime.departments
workflow_runtime.teams
workflow_runtime.user_team_memberships (many-to-many)

-- Enhanced users table with organizational fields
ALTER TABLE workflow_runtime.users ADD COLUMN department_id VARCHAR(255);
ALTER TABLE workflow_runtime.users ADD COLUMN reports_to_user_id VARCHAR(255);
ALTER TABLE workflow_runtime.users ADD COLUMN organizational_level VARCHAR(50);

-- Granular permission system
workflow_runtime.system_permissions
workflow_runtime.role_system_permissions
workflow_runtime.user_permission_overrides
```

### Permission Examples

```sql
-- Entity-level permission
INSERT INTO system_permissions VALUES (
    'PERM_ENTITY_E1', 
    'E1 LeaveApplication Entity', 
    'entity', 
    'e1_leaveapplication', 
    '["create", "read", "update", "delete"]'
);

-- Attribute-level permission
INSERT INTO system_permissions VALUES (
    'PERM_ATTR_E1_STATUS', 
    'E1 Status Attribute', 
    'attribute', 
    'e1_leaveapplication.status', 
    '["read", "update"]'
);

-- Global Objective permission
INSERT INTO system_permissions VALUES (
    'PERM_GO_GO001', 
    'Global Objective GO001', 
    'global_objective', 
    'GO001', 
    '["create", "read", "update", "delete"]'
);
```

## 🔧 API Endpoints

### Enhanced User Profile Management

#### **GET /api/v2/auth/profile/{user_id}**
Returns comprehensive user profile with:
- Basic user information
- Department and team memberships
- Organizational hierarchy
- All permissions categorized by type
- Role KPIs
- Team and department colleagues

```json
{
  "user_id": "U5",
  "username": "john.doe",
  "email": "<EMAIL>",
  "department": {
    "department_id": "DEPT001",
    "name": "Engineering"
  },
  "team_memberships": [
    {
      "team_id": "TEAM001",
      "team_name": "Backend Development",
      "is_primary_team": true
    }
  ],
  "entity_permissions": [
    {
      "permission_id": "PERM_ENTITY_E1",
      "resource_identifier": "e1_leaveapplication",
      "granted_actions": ["create", "read"]
    }
  ],
  "attribute_permissions": [...],
  "global_objective_permissions": [...],
  "role_kpis": [...]
}
```

#### **PUT /api/v2/auth/profile/{user_id}**
Update user profile including organizational details:
```json
{
  "first_name": "John",
  "last_name": "Doe",
  "department_id": "DEPT001",
  "reports_to_user_id": "U2",
  "organizational_level": "mid_level"
}
```

### Team Management

#### **POST /api/v2/auth/users/{user_id}/teams**
Add user to team with primary designation:
```json
{
  "team_id": "TEAM001",
  "is_primary_team": true
}
```

#### **DELETE /api/v2/auth/users/{user_id}/teams/{team_id}**
Remove user from team

### Permission Management

#### **POST /api/v2/auth/permissions/check**
Check specific user permissions:
```json
{
  "user_id": "U5",
  "permission_type": "entity",
  "resource_identifier": "e1_leaveapplication",
  "action": "create"
}
```

Response:
```json
{
  "user_id": "U5",
  "permission_type": "entity",
  "resource_identifier": "e1_leaveapplication",
  "action": "create",
  "has_permission": true,
  "granted_via": "role:R003",
  "conditions": {"own_records_only": true}
}
```

### Organizational Structure

#### **GET /api/v2/auth/departments**
List all departments

#### **GET /api/v2/auth/departments/{department_id}**
Get specific department details

#### **GET /api/v2/auth/teams**
List all teams (optionally filtered by department)

#### **GET /api/v2/auth/teams/{team_id}**
Get specific team details

## 🔐 Permission System Architecture

### Permission Hierarchy
1. **User-specific overrides** (highest priority)
2. **Role-based permissions** (standard)
3. **Default deny** (lowest priority)

### Permission Types & Examples

| Type | Resource Identifier | Description |
|------|-------------------|-------------|
| `entity` | `e1_leaveapplication` | Access to entire E1 entity |
| `attribute` | `e1_leaveapplication.status` | Access to specific attribute |
| `global_objective` | `GO001` | Access to specific Global Objective |
| `local_objective` | `LO001` | Access to specific Local Objective |
| `book` | `B001` | Access to specific book |
| `chapter` | `C001` | Access to specific chapter |

### Role-Based Permission Mapping

```sql
-- Administrator: Full access to everything
INSERT INTO role_system_permissions VALUES 
('R001', 'PERM_ENTITY_E1', '["create", "read", "update", "delete"]');

-- Manager: Limited access
INSERT INTO role_system_permissions VALUES 
('R002', 'PERM_ENTITY_E1', '["create", "read", "update"]');

-- User: Basic access
INSERT INTO role_system_permissions VALUES 
('R003', 'PERM_ENTITY_E1', '["create", "read"]');
```

## 🏗️ Implementation Architecture

### Service Layer Structure
```
organizational_service.py
├── OrganizationalService
    ├── get_user_profile()
    ├── update_user_profile()
    ├── get_user_permissions_by_type()
    ├── check_user_permission()
    ├── add_user_to_team()
    ├── remove_user_from_team()
    ├── get_organizational_hierarchy()
    └── get_team_colleagues()
```

### Model Structure
```
organizational_models.py
├── UserProfileResponse (comprehensive profile)
├── SystemPermissionResponse (granular permissions)
├── UserTeamMembershipResponse (multi-team support)
├── OrganizationalHierarchyResponse (hierarchy data)
├── PermissionCheckRequest/Response (permission validation)
└── Department/Team models
```

## 🎯 Usage Examples

### 1. Check Entity Permission
```bash
curl -X POST http://localhost:8000/api/v2/auth/permissions/check \
  -H "Content-Type: application/json" \
  -d '{
    "user_id": "U5",
    "permission_type": "entity",
    "resource_identifier": "e1_leaveapplication",
    "action": "create"
  }'
```

### 2. Get User Profile with Organizational Context
```bash
curl -X GET http://localhost:8000/api/v2/auth/profile/U5
```

### 3. Add User to Multiple Teams
```bash
# Add to primary team
curl -X POST http://localhost:8000/api/v2/auth/users/U5/teams \
  -H "Content-Type: application/json" \
  -d '{"team_id": "TEAM001", "is_primary_team": true}'

# Add to secondary team
curl -X POST http://localhost:8000/api/v2/auth/users/U5/teams \
  -H "Content-Type: application/json" \
  -d '{"team_id": "TEAM002", "is_primary_team": false}'
```

### 4. Update User Organizational Info
```bash
curl -X PUT http://localhost:8000/api/v2/auth/profile/U5 \
  -H "Content-Type: application/json" \
  -d '{
    "department_id": "DEPT001",
    "reports_to_user_id": "U2",
    "organizational_level": "mid_level"
  }'
```

## 🔄 Migration Instructions

1. **Run the organizational migration**:
   ```bash
   psql -d workflow_system -f organizational_migration.sql
   ```

2. **Verify tables created**:
   ```sql
   \dt workflow_runtime.*departments
   \dt workflow_runtime.*teams
   \dt workflow_runtime.*user_team_memberships
   \dt workflow_runtime.*system_permissions
   ```

3. **Test sample permissions**:
   ```sql
   SELECT * FROM workflow_runtime.system_permissions 
   WHERE permission_type = 'entity';
   ```

## 🚀 Next Steps

1. **Enhanced Login Response**: Modify login endpoint to return `UserProfileResponse` instead of basic user data
2. **Permission Middleware**: Create middleware to automatically check permissions on API endpoints
3. **Organizational Tree View**: Implement hierarchical organization chart endpoint
4. **Bulk Permission Management**: Add endpoints for bulk permission assignments
5. **Audit Logging**: Track permission changes and organizational updates

## 📈 Benefits

1. **Granular Security**: Fine-grained access control at entity and attribute levels
2. **Scalable Organization**: Support for complex organizational structures
3. **Multi-Team Flexibility**: Users can work across multiple teams
4. **Role-Based Management**: Simplified permission management through roles
5. **Comprehensive Context**: Rich user profiles with full organizational context
6. **Future-Proof**: Extensible permission system for new resource types

This implementation provides a solid foundation for enterprise-level organizational management and security within the workflow system.
