#!/usr/bin/env python3
"""
Authentication Flow Test Script

This script demonstrates the complete authentication flow for the Workflow System API:
1. User Registration
2. Login to get access and refresh tokens
3. Accessing a protected endpoint
4. Refreshing the access token
5. Logging out

Usage:
    python test_auth_flow.py
"""

import requests
import json
import time
import uuid
import sys

# Configuration
BASE_URL = "http://localhost:8000/api/v1"
USERNAME = f"testuser_{uuid.uuid4().hex[:8]}"  # Generate a unique username
PASSWORD = "password123"
EMAIL = f"{USERNAME}@example.com"

def print_separator(title):
    """Print a separator with a title."""
    print("\n" + "=" * 80)
    print(f" {title} ".center(80, "="))
    print("=" * 80 + "\n")

def print_response(response):
    """Print the response details."""
    print(f"Status Code: {response.status_code}")
    print(f"Headers: {json.dumps(dict(response.headers), indent=2)}")
    
    try:
        print(f"Response: {json.dumps(response.json(), indent=2)}")
    except json.JSONDecodeError:
        print(f"Response: {response.text}")
    
    print()

def register_user():
    """Register a new user."""
    print_separator("1. User Registration")
    
    url = f"{BASE_URL}/auth/auth/register"
    headers = {"Content-Type": "application/json"}
    data = {
        "username": USERNAME,
        "email": EMAIL,
        "password": PASSWORD,
        "first_name": "Test",
        "last_name": "User"
    }
    
    print(f"Request URL: {url}")
    print(f"Request Headers: {json.dumps(headers, indent=2)}")
    print(f"Request Body: {json.dumps(data, indent=2)}")
    
    response = requests.post(url, headers=headers, json=data)
    print_response(response)
    
    if response.status_code != 201:
        print(f"Error registering user: {response.text}")
        return None
    
    return response.json()

def login():
    """Login to get access and refresh tokens."""
    print_separator("2. Login")
    
    url = f"{BASE_URL}/auth/auth/token"
    headers = {"Content-Type": "application/x-www-form-urlencoded"}
    data = {
        "username": USERNAME,
        "password": PASSWORD
    }
    
    print(f"Request URL: {url}")
    print(f"Request Headers: {json.dumps(headers, indent=2)}")
    print(f"Request Body: {data}")
    
    response = requests.post(url, headers=headers, data=data)
    print_response(response)
    
    if response.status_code != 200:
        print(f"Error logging in: {response.text}")
        return None
    
    return response.json()

def get_current_user(access_token):
    """Get the current user information."""
    print_separator("3. Get Current User")
    
    url = f"{BASE_URL}/auth/auth/me"
    headers = {"Authorization": f"Bearer {access_token}"}
    
    print(f"Request URL: {url}")
    print(f"Request Headers: {json.dumps(headers, indent=2)}")
    
    response = requests.get(url, headers=headers)
    print_response(response)
    
    if response.status_code != 200:
        print(f"Error getting current user: {response.text}")
        return None
    
    return response.json()

def refresh_access_token(refresh_token_str):
    """Refresh the access token."""
    print_separator("4. Refresh Token")
    
    url = f"{BASE_URL}/auth/auth/refresh"
    headers = {"Content-Type": "application/json"}
    data = {"refresh_token": refresh_token_str}
    
    print(f"Request URL: {url}")
    print(f"Request Headers: {json.dumps(headers, indent=2)}")
    print(f"Request Body: {json.dumps(data, indent=2)}")
    
    response = requests.post(url, headers=headers, json=data)
    print_response(response)
    
    if response.status_code != 200:
        print(f"Error refreshing token: {response.text}")
        return None
    
    return response.json()

def logout(access_token):
    """Logout the current user."""
    print_separator("5. Logout")
    
    url = f"{BASE_URL}/auth/auth/logout"
    headers = {"Authorization": f"Bearer {access_token}"}
    
    print(f"Request URL: {url}")
    print(f"Request Headers: {json.dumps(headers, indent=2)}")
    
    response = requests.post(url, headers=headers)
    print_response(response)
    
    if response.status_code != 204:
        print(f"Error logging out: {response.text}")
        return False
    
    return True

def verify_token_revoked(access_token):
    """Verify that the token has been revoked."""
    print_separator("6. Verify Token Revoked")
    
    url = f"{BASE_URL}/auth/auth/me"
    headers = {"Authorization": f"Bearer {access_token}"}
    
    print(f"Request URL: {url}")
    print(f"Request Headers: {json.dumps(headers, indent=2)}")
    
    response = requests.get(url, headers=headers)
    print_response(response)
    
    if response.status_code == 401:
        print("Token has been successfully revoked!")
        return True
    else:
        print("Token is still valid!")
        return False

def main():
    """Main function."""
    print_separator("Authentication Flow Test")
    print(f"Testing with username: {USERNAME}")
    print(f"Testing with email: {EMAIL}")
    
    # 1. Register a new user
    user = register_user()
    if not user:
        sys.exit(1)
    
    # 2. Login to get access and refresh tokens
    tokens = login()
    if not tokens:
        sys.exit(1)
    
    access_token = tokens["access_token"]
    refresh_token = tokens["refresh_token"]
    
    # 3. Get current user information
    current_user = get_current_user(access_token)
    if not current_user:
        sys.exit(1)
    
    # 4. Refresh the access token
    new_tokens = refresh_access_token(refresh_token)
    if not new_tokens:
        sys.exit(1)
    
    new_access_token = new_tokens["access_token"]
    
    # 5. Logout
    if not logout(new_access_token):
        sys.exit(1)
    
    # 6. Verify that the token has been revoked
    if not verify_token_revoked(new_access_token):
        sys.exit(1)
    
    print_separator("Authentication Flow Test Completed Successfully")

if __name__ == "__main__":
    main()
