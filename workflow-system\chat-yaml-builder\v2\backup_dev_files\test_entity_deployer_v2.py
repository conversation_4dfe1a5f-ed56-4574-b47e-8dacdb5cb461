"""
Test script for the enhanced entity deployer v2.

This script demonstrates the enhanced entity deployer v2 by deploying an entity from prescriptive text.
"""

import os
import sys
import logging
from typing import Dict, List, Tuple, Any, Optional

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('test_entity_deployer_v2.log')
    ]
)
logger = logging.getLogger('test_entity_deployer_v2')

# Add parent directory to path to import common modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from prescriptive_parser import PrescriptiveParser
from deployers.entity_deployer_v2 import deploy_entities
from db_utils import execute_query

def parse_entity(prescriptive_text: str) -> Tuple[Dict, List[str]]:
    """
    Parse entity from prescriptive text.
    
    Args:
        prescriptive_text: Prescriptive text to parse
        
    Returns:
        Tuple containing:
            - Parsed entity data
            - List of warnings
    """
    logger.info("=== Parsing Entity ===")
    
    # Initialize parser
    parser = PrescriptiveParser()
    
    # Parse prescriptive text
    logger.info("Parsing prescriptive text:")
    logger.info(prescriptive_text)
    
    entity_data, warnings = parser.parse('entities', prescriptive_text)
    
    # Log parsed data
    logger.info("Parsed entity data:")
    for entity_name, entity_info in entity_data.get('entities', {}).items():
        logger.info(f"Entity: {entity_name}")
        logger.info(f"  Description: {entity_info.get('description', 'N/A')}")
        
        # Log attributes
        logger.info("  Attributes:")
        for attr_name, attr_info in entity_info.get('attributes', {}).items():
            logger.info(f"    {attr_name}: {attr_info}")
        
        # Log relationships
        logger.info("  Relationships:")
        for rel_name, rel_info in entity_info.get('relationships', {}).items():
            logger.info(f"    {rel_name}: {rel_info}")
        
        # Log business rules
        logger.info("  Business Rules:")
        for rule_id, rule_info in entity_info.get('business_rules', {}).items():
            logger.info(f"    {rule_id}: {rule_info}")
        
        # Log calculated fields
        logger.info("  Calculated Fields:")
        for field_id, field_info in entity_info.get('calculated_fields', {}).items():
            logger.info(f"    {field_id}: {field_info}")
        
        # Log lifecycle management
        logger.info("  Lifecycle Management:")
        if 'lifecycle_management' in entity_info:
            for lm_type, lm_info in entity_info.get('lifecycle_management', {}).items():
                logger.info(f"    {lm_type}: {lm_info}")
    
    # Log warnings
    if warnings:
        logger.warning("Parse warnings:")
        for warning in warnings:
            logger.warning(f"  {warning}")
    
    return entity_data, warnings

def deploy_entity(entity_data: Dict, schema_name: str = "workflow_temp") -> Tuple[bool, List[str]]:
    """
    Deploy entity to database.
    
    Args:
        entity_data: Parsed entity data
        schema_name: Schema name to deploy to
        
    Returns:
        Tuple containing:
            - Boolean indicating if deployment was successful
            - List of messages
    """
    logger.info("=== Deploying Entity ===")
    
    # Deploy entity
    logger.info(f"Deploying entity to schema {schema_name}")
    success, messages = deploy_entities(entity_data, schema_name)
    
    # Log messages
    if messages:
        logger.info("Deploy messages:")
        for message in messages:
            logger.info(f"  {message}")
    
    return success, messages

def query_deployed_entity(schema_name: str = "workflow_temp") -> None:
    """
    Query deployed entity in database.
    
    Args:
        schema_name: Schema name to query
    """
    logger.info("=== Querying Deployed Entity ===")
    
    # Query entities table
    logger.info(f"Querying {schema_name}.entities table")
    success, messages, entities = execute_query(
        f"SELECT entity_id, name, description, metadata, lifecycle_management FROM {schema_name}.entities",
        schema_name=schema_name
    )
    
    if success and entities:
        logger.info("Entities:")
        for entity in entities:
            logger.info(f"  {entity}")
    else:
        logger.warning("No entities found")
    
    # Query entity_attributes table
    logger.info(f"Querying {schema_name}.entity_attributes table")
    success, messages, attributes = execute_query(
        f"SELECT attribute_id, entity_id, name, type, required, default_value, calculated_field, calculation_formula, dependencies FROM {schema_name}.entity_attributes",
        schema_name=schema_name
    )
    
    if success and attributes:
        logger.info("Entity Attributes:")
        for attribute in attributes:
            logger.info(f"  {attribute}")
    else:
        logger.warning("No entity attributes found")
    
    # Query entity_attribute_metadata table
    logger.info(f"Querying {schema_name}.entity_attribute_metadata table")
    success, messages, metadata = execute_query(
        f"SELECT entity_id, attribute_id, attribute_name, required FROM {schema_name}.entity_attribute_metadata",
        schema_name=schema_name
    )
    
    if success and metadata:
        logger.info("Entity Attribute Metadata:")
        for item in metadata:
            logger.info(f"  {item}")
    else:
        logger.warning("No entity attribute metadata found")
    
    # Query entity_relationships table
    logger.info(f"Querying {schema_name}.entity_relationships table")
    success, messages, relationships = execute_query(
        f"SELECT source_entity_id, target_entity_id, relationship_type, source_attribute_id, target_attribute_id FROM {schema_name}.entity_relationships",
        schema_name=schema_name
    )
    
    if success and relationships:
        logger.info("Entity Relationships:")
        for relationship in relationships:
            logger.info(f"  {relationship}")
    else:
        logger.warning("No entity relationships found")
    
    # Query entity_business_rules table
    logger.info(f"Querying {schema_name}.entity_business_rules table")
    success, messages, rules = execute_query(
        f"SELECT rule_id, entity_id, name, description, condition FROM {schema_name}.entity_business_rules",
        schema_name=schema_name
    )
    
    if success and rules:
        logger.info("Entity Business Rules:")
        for rule in rules:
            logger.info(f"  {rule}")
    else:
        logger.warning("No entity business rules found")
    
    # List tables in schema
    logger.info(f"Listing tables in {schema_name} schema")
    success, messages, tables = execute_query(
        f"""
        SELECT table_name 
        FROM information_schema.tables 
        WHERE table_schema = '{schema_name}'
        AND table_name LIKE 'e%'
        ORDER BY table_name
        """
    )
    
    if success and tables:
        logger.info("Entity Tables:")
        for table in tables:
            table_name = table[0]
            logger.info(f"  {table_name}")
            
            # Get table columns
            success, messages, columns = execute_query(
                f"""
                SELECT column_name, data_type 
                FROM information_schema.columns 
                WHERE table_schema = '{schema_name}'
                AND table_name = '{table_name}'
                ORDER BY ordinal_position
                """
            )
            
            if success and columns:
                logger.info(f"    Columns:")
                for column in columns:
                    logger.info(f"      {column[0]} - {column[1]}")
    else:
        logger.warning("No entity tables found")

def main():
    """
    Main function.
    """
    # Sample prescriptive text
    prescriptive_text = """
Employee has employeeId^PK, firstName, lastName, email, phoneNumber, departmentId^FK, managerId^FK, hireDate, status (Active, Inactive, OnLeave), salary, performanceRating.

* Employee has many-to-one relationship with Department using Employee.departmentId to Department.departmentId^PK
* Employee has many-to-one relationship with Employee using Employee.managerId to Employee.employeeId^PK

* Employee.probationDays PROPERTY_NAME = 90
* Employee.minSalary PROPERTY_NAME = 30000
* Employee.status DEFAULT_VALUE = "Active"
* Employee.hireDate DEFAULT_VALUE = CURRENT_DATE

* Employee.email must be unique
* Employee.email must match pattern "^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$"
* Employee.hireDate must be before current date
* Employee.salary must be greater than 0

BusinessRule EMP001 for Employee:
* Employee.status must be Active to receive performance reviews
* Employee.hireDate must be at least 90 days before Employee.performanceRating can be set

Employee has employeeId, firstName, lastName, fullName[derived].

CalculatedField CF001 for Employee.fullName:
* Formula: CONCAT(firstName, ' ', lastName)
* Logic Layer: Application
* Caching: Session
* Dependencies: Employee.firstName, Employee.lastName

Employee has departmentId^FK, managerId^FK with Department constrains Manager.

* Employee.managerId must belong to selected Employee.departmentId


Entity Additional Properties:
Display Name: Company Employee
Type: Core Entity
Description: Represents an employee within the organization

Attribute Additional Properties:
Attribute name: email
Key: Unique
Display Name: Email Address
Data Type: String
Type: Mandatory
Format: "<EMAIL>"
Values: N/A
Default: "@company.com"
Validation: Regex Pattern
Error Message: "Please enter a valid email address"
Description: Employee's primary email address for communications

Relationship: Employee to Department

Relationship Properties:
On Delete: Restrict (Prevent deletion of department with active employees)
On Update: Cascade (Update employee records when department details change)
Foreign Key Type: Non-Nullable

* Synthetic: 
Employee has employeeId = 1, firstName = Tarun, lastName = Singh, email = "<EMAIL>", phoneNumber = "************", departmentId = 101, managerId = 1001, hireDate = "2024-01-15", status = "Active", salary = 60000, performanceRating = 4.5.
Employee has employeeId = 2, firstName = Priya, lastName = Sharma, email = "<EMAIL>", phoneNumber = "************", departmentId = 102, managerId = 1002, hireDate = "2024-02-20", status = "Inactive", salary = 55000, performanceRating = 4.0.


* Confidential: Employee.salary, Employee.performanceRating
* Internal: Employee.hireDate, Employee.departmentId, Employee.managerId
* Public: Employee.firstName, Employee.lastName, Employee.status

* Loading for Employee.Department: Eager Loading
* Loading for Employee.Manager: Lazy Loading

* Archive Strategy for Employee:
  - Trigger: Event-based
  - Criteria: When Employee.status changes to 'Inactive' and remains so for 1 year
  - Retention: 7 years
  - Storage: Cold storage
  - Access Pattern: Read-only through HR archive portal
  - Restoration: Manual process requiring HR Director approval

* History Tracking for Employee:
  - Tracked Attributes: Employee.salary, Employee.departmentId, Employee.managerId, Employee.status
  - Tracking Method: Audit table
  - Granularity: Change level
  - Retention: 7 years
  - Access Control: HR Managers and Compliance Officers only


BusinessRule EMP001 Placement:
* Local Objective: EmployeeManagement
* Global Objective: EmployeeLifecycle
* Chapter: HumanResources
* Book: CompanyOperations
* Tenant: CorporateHQ

* Workflow EmployeeOnboarding Placement:
  - Global Objective: EmployeeLifecycle
  - Book: CompanyOperations
  - Chapter: HumanResources
  - Tenant: CorporateHQ

* Entity Placement for Employee:
  - Tenant: CorporateHQ
  - Book: CompanyOperations
  - Chapter: HumanResources
  - Global Objectives: EmployeeLifecycle, PayrollProcessing
  - Local Objectives: EmployeeManagement, TimeTracking

Department has departmentId^PK, name, managerId^FK, location, budget.

* Department has one-to-one relationship with Employee using Department.managerId to Employee.employeeId^PK
* Department has one-to-many relationship with Employee using Department.departmentId to Employee.departmentId^FK

* Department.minBudget PROPERTY_NAME = 10000
* Department.location DEFAULT_VALUE = "Headquarters"

* Department.name must be unique
* Department.budget must be greater than 0

BusinessRule DEPT001 for Department:
* Department.budget must be approved by Finance before changes
* Department.managerId must reference an Employee with status = 'Active'

Department has name, location, fullAddress[derived].

CalculatedField CF002 for Department.fullAddress:
* Formula: CONCAT(name, ' - ', location)
* Logic Layer: Application
* Caching: Session
* Dependencies: Department.name, Department.location

Department has managerId^FK, locationId^FK with Employee constrains Location.

* Department.locationId must be compatible with selected Department.managerId's office location

Entity Hover: Department

Editable Properties:
Display Name: Business Department
Type: Core Entity
Description: Represents a department within the organization

Relationship Hover: Department to Employee

Relationship Properties:
On Delete: Restrict (Prevent deletion of department with active employees)
On Update: Cascade (Update employee records when department details change)
Foreign Key Type: Non-Nullable

* Synthetic: Department.location

* Confidential: Department.budget
* Internal: Department.managerId
* Public: Department.name, Department.location

* Loading for Department.Employees: Lazy Loading
* Loading for Department.Manager: Eager Loading

* Archive Strategy for Department:
  - Trigger: Event-based
  - Criteria: When Department is marked as 'Dissolved'
  - Retention: 10 years
  - Storage: Cold storage
  - Access Pattern: Read-only through Admin portal
  - Restoration: Manual process requiring CEO approval

* Purge Rule for Department:
  - Trigger: Time-based (biennial evaluation)
  - Criteria: archivedDate < (CURRENT_DATE - (10 * 365)) AND noActiveReferences = true
  - Approvals: ExecutiveTeam AND ComplianceOfficer
  - Audit: Full historical record with executive approval documentation
  - Dependencies: Employee (verify no references), Budget (archive), DepartmentDocument (cascade)

* History Tracking for Department:
  - Tracked Attributes: Department.name, Department.managerId, Department.budget
  - Tracking Method: Temporal tables
  - Granularity: Snapshot level
  - Retention: 10 years
  - Access Control: Finance Managers and Executive Team only

BusinessRule DEPT001 Placement:
* Local Objective: DepartmentManagement
* Global Objective: OrganizationalStructure
* Chapter: Administration
* Book: CompanyOperations
* Tenant: CorporateHQ

* Entity Placement for Department:
  - Tenant: CorporateHQ
  - Book: CompanyOperations
  - Chapter: Administration
  - Global Objectives: OrganizationalStructure, BudgetPlanning
  - Local Objectives: DepartmentManagement, ResourceAllocation
"""
    
    # Parse entity
    entity_data, warnings = parse_entity(prescriptive_text)
    
    # Deploy entity
    success, messages = deploy_entity(entity_data)
    
    # Query deployed entity
    if success:
        query_deployed_entity()
    
    logger.info("=== Summary ===")
    logger.info(f"Deployment successful: {success}")
    
    # Print summary to console
    print(f"Deployment successful: {success}")
    print("See test_entity_deployer_v2.log for detailed information")

if __name__ == '__main__':
    main()
