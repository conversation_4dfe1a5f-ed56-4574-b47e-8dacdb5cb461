#!/usr/bin/env python3
"""
RBAC Validation Module for Workflow System

This module provides validation functions for RBAC (Role-Based Access Control)
in workflow YAML files. It validates:
- User and Role entities with mandatory attributes
- Agent stack with proper role and user-specific permissions
- Email-based user identification
- Workflow source constraints

This module is designed to be used by the validate_engine.py module.
"""

import re
from typing import Dict, List, Any, Optional, Set, Tuple

# Mandatory attribute names for User entity
USER_MANDATORY_ATTRIBUTES = ["user_id", "username", "email", "status"]

# Mandatory attribute names for Role entity
ROLE_MANDATORY_ATTRIBUTES = ["role_id", "name"]

def validate_entity_attributes(entities: List[Dict[str, Any]], entity_name: str, 
                              mandatory_attribute_names: List[str]) -> Tuple[bool, List[str]]:
    """
    Validate that an entity has all mandatory attributes.
    
    Args:
        entities: List of entity definitions
        entity_name: Name of the entity to validate
        mandatory_attribute_names: List of mandatory attribute names
        
    Returns:
        Tuple of (is_valid, missing_attributes)
    """
    # Find the entity
    entity = None
    for e in entities:
        if e.get("name") == entity_name:
            entity = e
            break
    
    if not entity:
        return False, [f"{entity_name} entity not found"]
    
    # Get attribute metadata
    attributes = entity.get("attributes", [])
    required_attrs = entity.get("attributes_metadata", {}).get("required_attributes", [])
    attribute_map = entity.get("attributes_metadata", {}).get("attribute_map", {})
    
    # Create mappings for easier lookup
    name_to_id = {}
    id_to_name = {}
    id_to_attr = {}
    
    # Build mappings from the attribute definitions
    for attr in attributes:
        attr_id = attr.get("id")
        attr_name = attr.get("name")
        if attr_id and attr_name:
            name_to_id[attr_name] = attr_id
            id_to_name[attr_id] = attr_name
            id_to_attr[attr_id] = attr
    
    # Also check the attribute_map for additional mappings
    for attr_id, attr_name in attribute_map.items():
        if attr_name not in name_to_id:
            name_to_id[attr_name] = attr_id
        if attr_id not in id_to_name:
            id_to_name[attr_id] = attr_name
    
    # Check if all mandatory attributes are present
    missing_attrs = []
    for attr_name in mandatory_attribute_names:
        # Find the attribute ID for this name
        attr_id = name_to_id.get(attr_name)
        
        if not attr_id:
            missing_attrs.append(f"{attr_name} not found in entity attributes")
            continue
        
        # Check if attribute ID is in required_attributes
        if attr_id not in required_attrs:
            missing_attrs.append(f"{attr_name} ({attr_id}) not in required_attributes")
        
        # Check if attribute ID is in attribute_map
        if attr_id not in attribute_map:
            missing_attrs.append(f"{attr_name} ({attr_id}) not in attribute_map")
        
        # Check if attribute exists in attributes list
        if attr_id not in id_to_attr:
            missing_attrs.append(f"{attr_name} ({attr_id}) not found in attributes list")
        else:
            # Check if attribute is marked as required
            attr = id_to_attr[attr_id]
            if not attr.get("required", False):
                missing_attrs.append(f"{attr_name} ({attr_id}) is not marked as required")
    
    return len(missing_attrs) == 0, missing_attrs

def validate_agent_stack(local_objectives: List[Dict[str, Any]]) -> Tuple[bool, List[str]]:
    """
    Validate agent stack in local objectives.
    
    Args:
        local_objectives: List of local objective definitions
        
    Returns:
        Tuple of (is_valid, errors)
    """
    errors = []
    
    for lo in local_objectives:
        lo_id = lo.get("id", "unknown")
        contextual_id = lo.get("contextual_id", "unknown")
        
        # Check if agent_stack exists
        if "agent_stack" not in lo:
            errors.append(f"Local objective {contextual_id} ({lo_id}) has no agent_stack")
            continue
        
        # Check if agents exist in agent_stack
        if "agents" not in lo["agent_stack"]:
            errors.append(f"Local objective {contextual_id} ({lo_id}) has no agents in agent_stack")
            continue
        
        # Check each agent
        for i, agent in enumerate(lo["agent_stack"]["agents"]):
            # Check if role is defined
            if "role" not in agent:
                errors.append(f"Agent {i+1} in {contextual_id} ({lo_id}) has no role defined")
            
            # Check if rights are defined
            if "rights" not in agent:
                errors.append(f"Agent {i+1} in {contextual_id} ({lo_id}) has no rights defined")
            elif not isinstance(agent["rights"], list):
                errors.append(f"Agent {i+1} in {contextual_id} ({lo_id}) has invalid rights format (should be a list)")
            elif not agent["rights"]:
                errors.append(f"Agent {i+1} in {contextual_id} ({lo_id}) has empty rights list")
            
            # Check if users field exists (can be empty list)
            if "users" not in agent:
                errors.append(f"Agent {i+1} in {contextual_id} ({lo_id}) has no users field (should be a list, can be empty)")
            elif not isinstance(agent["users"], list):
                errors.append(f"Agent {i+1} in {contextual_id} ({lo_id}) has invalid users format (should be a list)")
            else:
                # Validate email format for users
                for j, user in enumerate(agent["users"]):
                    if isinstance(user, str):
                        # Simple email validation
                        if not re.match(r"[^@]+@[^@]+\.[^@]+", user):
                            errors.append(f"User {j+1} in agent {i+1} in {contextual_id} ({lo_id}) has invalid email format: {user}")
                    elif isinstance(user, dict):
                        # Check if email is defined
                        if "email" not in user:
                            errors.append(f"User {j+1} in agent {i+1} in {contextual_id} ({lo_id}) has no email defined")
                        elif not re.match(r"[^@]+@[^@]+\.[^@]+", user["email"]):
                            errors.append(f"User {j+1} in agent {i+1} in {contextual_id} ({lo_id}) has invalid email format: {user['email']}")
                        
                        # Check if rights are defined
                        if "rights" not in user:
                            errors.append(f"User {j+1} in agent {i+1} in {contextual_id} ({lo_id}) has no rights defined")
                        elif not isinstance(user["rights"], list):
                            errors.append(f"User {j+1} in agent {i+1} in {contextual_id} ({lo_id}) has invalid rights format (should be a list)")
                        elif not user["rights"]:
                            errors.append(f"User {j+1} in agent {i+1} in {contextual_id} ({lo_id}) has empty rights list")
                    else:
                        errors.append(f"User {j+1} in agent {i+1} in {contextual_id} ({lo_id}) has invalid format (should be string or dict)")
    
    return len(errors) == 0, errors

def validate_workflow_sources(local_objectives: List[Dict[str, Any]]) -> Tuple[bool, List[str]]:
    """
    Validate that only one local objective has workflow_source: "origin".
    
    Args:
        local_objectives: List of local objective definitions
        
    Returns:
        Tuple of (is_valid, errors)
    """
    errors = []
    origin_count = 0
    origin_los = []
    
    for lo in local_objectives:
        lo_id = lo.get("id", "unknown")
        contextual_id = lo.get("contextual_id", "unknown")
        
        if lo.get("workflow_source") == "origin":
            origin_count += 1
            origin_los.append(f"{contextual_id} ({lo_id})")
    
    if origin_count == 0:
        errors.append("No local objective has workflow_source: 'origin'")
    elif origin_count > 1:
        errors.append(f"Multiple local objectives have workflow_source: 'origin': {', '.join(origin_los)}")
    
    return len(errors) == 0, errors

def validate_user_specific_permissions(local_objectives: List[Dict[str, Any]]) -> Tuple[bool, List[str]]:
    """
    Test that user-specific permissions are correctly defined in the agent stack.
    
    Args:
        local_objectives: List of local objective definitions
        
    Returns:
        Tuple of (is_valid, errors)
    """
    errors = []
    
    for lo in local_objectives:
        lo_id = lo.get("id", "unknown")
        contextual_id = lo.get("contextual_id", "unknown")
        
        # Check if agent_stack exists
        if "agent_stack" not in lo:
            errors.append(f"Local objective {contextual_id} ({lo_id}) has no agent_stack")
            continue
        
        # Check if agents exist in agent_stack
        if "agents" not in lo["agent_stack"]:
            errors.append(f"Local objective {contextual_id} ({lo_id}) has no agents in agent_stack")
            continue
        
        # Check for user-specific permissions
        has_user_specific = False
        for i, agent in enumerate(lo["agent_stack"]["agents"]):
            # Check if users field exists
            if "users" not in agent:
                continue
                
            # Check if users field is a list
            if not isinstance(agent["users"], list):
                continue
                
            # Check if users field is not empty
            if agent["users"]:
                has_user_specific = True
                break
    
    return len(errors) == 0, errors

def validate_role_references(workflow_data: Dict[str, Any]) -> Tuple[bool, List[str]]:
    """
    Test that all role references in the agent stack are valid.
    
    Args:
        workflow_data: The workflow data loaded from a YAML file
        
    Returns:
        Tuple of (is_valid, errors)
    """
    errors = []
    
    # Get all defined roles
    defined_roles = set()
    if "tenant" in workflow_data and "roles" in workflow_data["tenant"]:
        for role in workflow_data["tenant"]["roles"]:
            if "id" in role:
                defined_roles.add(role["id"])
            if "name" in role:
                defined_roles.add(role["name"])
    
    # Check if local_objectives exist
    if "local_objectives" not in workflow_data:
        return False, ["No local_objectives found in workflow data"]
    
    # Check each local objective
    for lo in workflow_data.get("local_objectives", []):
        lo_id = lo.get("id", "unknown")
        contextual_id = lo.get("contextual_id", "unknown")
        
        # Check if agent_stack exists
        if "agent_stack" not in lo:
            errors.append(f"Local objective {contextual_id} ({lo_id}) has no agent_stack")
            continue
        
        # Check if agents exist in agent_stack
        if "agents" not in lo["agent_stack"]:
            errors.append(f"Local objective {contextual_id} ({lo_id}) has no agents in agent_stack")
            continue
        
        # Check each agent
        for i, agent in enumerate(lo["agent_stack"]["agents"]):
            # Check if role is defined
            if "role" not in agent:
                errors.append(f"Agent {i+1} in {contextual_id} ({lo_id}) has no role defined")
                continue
                
            # Check if role is empty (allowed for user-specific permissions)
            if agent["role"] == "":
                # If role is empty, users must be specified
                if "users" not in agent or not agent["users"]:
                    errors.append(f"Agent {i+1} in {contextual_id} ({lo_id}) has empty role but no users specified")
                continue
                
            # Check if role is valid
            if agent["role"] not in defined_roles:
                errors.append(f"Agent {i+1} in {contextual_id} ({lo_id}) has invalid role: {agent['role']}")
    
    return len(errors) == 0, errors

def validate_rbac(workflow_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Validate RBAC implementation in a workflow YAML file.
    
    Args:
        workflow_data: The workflow data loaded from a YAML file
        
    Returns:
        Dict with validation results
    """
    results = {
        "valid": True,
        "errors": [],
        "sections": {}
    }
    
    # Validate User entity
    user_valid, user_errors = validate_entity_attributes(
        workflow_data.get("entities", []),
        "User",
        USER_MANDATORY_ATTRIBUTES
    )
    results["sections"]["user_entity"] = {
        "valid": user_valid,
        "errors": user_errors
    }
    if not user_valid:
        results["valid"] = False
        results["errors"].extend(user_errors)
    
    # Validate Role entity
    role_valid, role_errors = validate_entity_attributes(
        workflow_data.get("entities", []),
        "Role",
        ROLE_MANDATORY_ATTRIBUTES
    )
    results["sections"]["role_entity"] = {
        "valid": role_valid,
        "errors": role_errors
    }
    if not role_valid:
        results["valid"] = False
        results["errors"].extend(role_errors)
    
    # Validate agent stack
    agent_valid, agent_errors = validate_agent_stack(
        workflow_data.get("local_objectives", [])
    )
    results["sections"]["agent_stack"] = {
        "valid": agent_valid,
        "errors": agent_errors
    }
    if not agent_valid:
        results["valid"] = False
        results["errors"].extend(agent_errors)
    
    # Validate workflow sources
    source_valid, source_errors = validate_workflow_sources(
        workflow_data.get("local_objectives", [])
    )
    results["sections"]["workflow_sources"] = {
        "valid": source_valid,
        "errors": source_errors
    }
    if not source_valid:
        results["valid"] = False
        results["errors"].extend(source_errors)
    
    # Validate user-specific permissions
    user_specific_valid, user_specific_errors = validate_user_specific_permissions(
        workflow_data.get("local_objectives", [])
    )
    results["sections"]["user_specific_permissions"] = {
        "valid": user_specific_valid,
        "errors": user_specific_errors
    }
    if not user_specific_valid:
        results["valid"] = False
        results["errors"].extend(user_specific_errors)
    
    # Validate role references
    role_refs_valid, role_refs_errors = validate_role_references(workflow_data)
    results["sections"]["role_references"] = {
        "valid": role_refs_valid,
        "errors": role_refs_errors
    }
    if not role_refs_valid:
        results["valid"] = False
        results["errors"].extend(role_refs_errors)
    
    return results
