-- Purchase Furniture Data Mappings Insert Script
-- Inserts data mappings between LOs and nested functions

SET search_path TO workflow_runtime;

-- =====================================================
-- DATA MAPPINGS
-- =====================================================
INSERT INTO lo_data_mappings (
    mapping_id, source_lo_id, target_lo_id, source_output_item_id, 
    target_input_item_id, mapping_type, transformation_rule, 
    created_at, updated_at, created_by, updated_by, version, natural_language
) VALUES 
-- GO2.LO1 to GO2.LO2 mappings
('MAP_GO2_LO1_TO_LO2_1', 'GO2.LO1', 'GO2.LO2', 'GO2.LO1.OP1.IT2', 'GO2.LO2.IP1.IT1', 'direct', 'productid -> productname lookup', NOW(), NOW(), 'system', 'system', '1.0', 'Map product ID to product name for display'),
('MAP_GO2_LO1_TO_LO2_2', 'GO2.LO1', 'GO2.LO2', 'GO2.LO1.OP1.IT4', 'GO2.LO2.IP1.IT2', 'direct', 'unitprice -> unitprice', NOW(), NOW(), 'system', 'system', '1.0', 'Map unit price from product selection'),

-- GO2.LO2 to GO2.LO3 mappings
('MAP_GO2_LO2_TO_LO3_1', 'GO2.LO2', 'GO2.LO3', 'GO2.LO2.OP1.IT4', 'GO2.LO3.IP1.IT1', 'direct', 'totalamount -> ordersummary', NOW(), NOW(), 'system', 'system', '1.0', 'Map total amount to order summary'),
('MAP_GO2_LO2_TO_LO3_2', 'GO2.LO2', 'GO2.LO3', 'GO2.LO2.OP1.IT1', 'GO2.LO3.IP1.IT3', 'transformation', 'quantity for inventory update', NOW(), NOW(), 'system', 'system', '1.0', 'Map quantity for inventory reduction');

COMMIT;
