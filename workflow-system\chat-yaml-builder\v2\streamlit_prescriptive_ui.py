import streamlit as st
import requests
import json
import pandas as pd
import logging
from typing import Dict, List, Any

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("prescriptive_ui.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger("prescriptive_ui")

# API URL
API_URL = "http://localhost:8000"

def main():
    st.set_page_config(
        page_title="Prescriptive YAML Builder",
        page_icon="📝",
        layout="wide"
    )
    
    st.title("Prescriptive YAML Builder v2")
    
    # Sidebar
    st.sidebar.title("Navigation")
    page = st.sidebar.radio(
        "Select a page",
        ["Add Prescriptive", "View Entities", "View GOs", "View LOs", "View Roles"]
    )
    
    if page == "Add Prescriptive":
        show_add_prescriptive_page()
    elif page == "View Entities":
        show_view_entities_page()
    elif page == "View GOs":
        show_view_gos_page()
    elif page == "View LOs":
        show_view_los_page()
    elif page == "View Roles":
        show_view_roles_page()

def show_add_prescriptive_page():
    st.header("Add Prescriptive Text")
    
    # Component type selection
    component_type = st.selectbox(
        "Select Component Type",
        ["entities", "go_definitions", "lo_definitions", "roles"]
    )
    
    # Prescriptive text input
    prescriptive_text = st.text_area(
        "Enter Prescriptive Text",
        height=300,
        help="Enter the prescriptive text for the selected component type"
    )
    
    # Validation only checkbox
    validate_only = st.checkbox("Validate Only (Don't Deploy)")
    
    # Submit button
    if st.button("Submit"):
        if not prescriptive_text:
            st.error("Please enter prescriptive text")
            return
        
        try:
            # Prepare request data
            request_data = {
                "component_type": component_type,
                "prescriptive_text": prescriptive_text,
                "validate_only": validate_only
            }
            
            # Call API
            endpoint = f"{API_URL}/validate" if validate_only else f"{API_URL}/deploy"
            response = requests.post(endpoint, json=request_data)
            
            if response.status_code == 200:
                result = response.json()
                
                if result["is_valid"]:
                    st.success("Validation successful!")
                    
                    if not validate_only:
                        st.success("Deployment successful!")
                    
                    # Display parsed data
                    st.subheader("Parsed Data")
                    st.json(result["parsed_data"])
                else:
                    st.error("Validation failed!")
                    
                    # Display validation messages
                    st.subheader("Validation Messages")
                    for message in result["messages"]:
                        st.error(message)
            else:
                st.error(f"API Error: {response.status_code} - {response.text}")
        except Exception as e:
            logger.error(f"Error submitting prescriptive text: {str(e)}", exc_info=True)
            st.error(f"Error: {str(e)}")

def show_view_entities_page():
    st.header("View Entities")
    
    try:
        # Call API to get entities
        response = requests.get(f"{API_URL}/entities")
        
        if response.status_code == 200:
            entities = response.json()
            
            if not entities:
                st.info("No entities found")
                return
            
            # Display entities in a table
            entity_data = []
            for entity in entities:
                entity_data.append({
                    "Entity ID": entity["entity_id"],
                    "Name": entity["name"],
                    "Attributes": len(entity["attributes"]),
                    "Relationships": len(entity.get("relationships", {})),
                    "Business Rules": len(entity.get("business_rules", {}))
                })
            
            st.dataframe(pd.DataFrame(entity_data))
            
            # Entity details
            st.subheader("Entity Details")
            selected_entity = st.selectbox(
                "Select an entity to view details",
                [entity["name"] for entity in entities]
            )
            
            # Find selected entity
            entity = next((e for e in entities if e["name"] == selected_entity), None)
            
            if entity:
                # Display entity details
                st.write(f"**Entity ID:** {entity['entity_id']}")
                st.write(f"**Name:** {entity['name']}")
                
                # Attributes
                st.subheader("Attributes")
                attr_data = []
                for attr_name, attr in entity["attributes"].items():
                    attr_data.append({
                        "Name": attr_name,
                        "Type": attr["type"],
                        "Required": "Yes" if attr["required"] else "No",
                        "Primary Key": "Yes" if attr.get("primary_key", False) else "No",
                        "Calculated": "Yes" if attr.get("calculated_field", False) else "No"
                    })
                
                st.dataframe(pd.DataFrame(attr_data))
                
                # Relationships
                if entity.get("relationships"):
                    st.subheader("Relationships")
                    rel_data = []
                    for rel_name, rel in entity["relationships"].items():
                        rel_data.append({
                            "Name": rel_name,
                            "Type": rel["type"],
                            "Target Entity": rel["entity"],
                            "Source Attribute": rel["source_attribute"],
                            "Target Attribute": rel["target_attribute"]
                        })
                    
                    st.dataframe(pd.DataFrame(rel_data))
                
                # Business Rules
                if entity.get("business_rules"):
                    st.subheader("Business Rules")
                    for rule_name, rule in entity["business_rules"].items():
                        st.write(f"**{rule_name}**")
                        st.write(f"Description: {rule.get('description', '')}")
                        st.write("Conditions:")
                        for condition in rule.get("conditions", []):
                            st.write(f"- {condition}")
                        st.write("---")
        else:
            st.error(f"API Error: {response.status_code} - {response.text}")
    except Exception as e:
        logger.error(f"Error retrieving entities: {str(e)}", exc_info=True)
        st.error(f"Error: {str(e)}")

def show_view_gos_page():
    st.header("View Global Objectives")
    st.info("Implementation pending")
    # Similar to show_view_entities_page but for GOs

def show_view_los_page():
    st.header("View Local Objectives")
    st.info("Implementation pending")
    # Similar to show_view_entities_page but for LOs

def show_view_roles_page():
    st.header("View Roles")
    st.info("Implementation pending")
    # Similar to show_view_entities_page but for Roles

if __name__ == "__main__":
    main()