// Create workflow_system database and user
db = db.getSiblingDB('workflow_system');

db.createUser({
  user: 'workflow_user',
  pwd: 'workflow_password',  // In production, use environment variables
  roles: [
    { role: 'readWrite', db: 'workflow_system' }
  ]
});

// Create collections
db.createCollection('configuration_versions');
db.createCollection('tenants');
db.createCollection('roles');
db.createCollection('entities');
db.createCollection('entity_attributes');
db.createCollection('global_objectives');
db.createCollection('local_objectives');
db.createCollection('nested_functions');
db.createCollection('system_functions');

// Create indexes
db.tenants.createIndex({ "tenant_id": 1 }, { unique: true });
db.roles.createIndex({ "tenant_id": 1, "role_id": 1 }, { unique: true });
db.entities.createIndex({ "tenant_id": 1, "entity_id": 1 }, { unique: true });
db.entity_attributes.createIndex({ "tenant_id": 1, "entity_id": 1, "attribute_id": 1 }, { unique: true });
db.global_objectives.createIndex({ "tenant_id": 1, "objective_id": 1 }, { unique: true });
db.local_objectives.createIndex({ "tenant_id": 1, "objective_id": 1 }, { unique: true });
db.nested_functions.createIndex({ "tenant_id": 1, "function_id": 1 }, { unique: true });
db.system_functions.createIndex({ "function_id": 1 }, { unique: true });
db.configuration_versions.createIndex({ "tenant_id": 1, "version_id": 1 }, { unique: true });
