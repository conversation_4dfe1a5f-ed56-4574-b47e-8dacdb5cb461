-- Master Execution Script for Purchase Furniture Implementation
-- Executes all scripts in the correct order
-- Tenant: T2

SET search_path TO workflow_runtime;

-- =====================================================
-- EXECUTION ORDER
-- =====================================================

-- 1. ENTITIES
\i /home/<USER>/workflow-system/scripts/entity/01_insert_entities.sql
\i /home/<USER>/workflow-system/scripts/entity/02_create_entity_tables.sql
\i /home/<USER>/workflow-system/scripts/entity/03_insert_sample_data.sql

-- 2. ATTRIBUTES
\i /home/<USER>/workflow-system/scripts/attribute/01_insert_entity_attributes.sql

-- 3. GLOBAL OBJECTIVE
\i /home/<USER>/workflow-system/scripts/go/01_insert_global_objective.sql

-- 4. LOCAL OBJECTIVES
\i /home/<USER>/workflow-system/scripts/lo/01_insert_local_objectives.sql
\i /home/<USER>/workflow-system/scripts/lo/02_insert_lo_input_stacks.sql
\i /home/<USER>/workflow-system/scripts/lo/03_insert_lo_input_items.sql
\i /home/<USER>/workflow-system/scripts/lo/04_insert_lo_output_stacks.sql
\i /home/<USER>/workflow-system/scripts/lo/05_insert_lo_output_items.sql
\i /home/<USER>/workflow-system/scripts/lo/06_insert_nested_functions.sql
\i /home/<USER>/workflow-system/scripts/lo/07_insert_nested_function_input_output.sql
\i /home/<USER>/workflow-system/scripts/lo/08_insert_data_mappings.sql

-- Final commit
COMMIT;

-- Display completion message
SELECT 'Purchase Furniture Implementation Completed Successfully for Tenant T2' AS status;
SELECT 'All components inserted: Entities, Attributes, Global Objective, Local Objectives, Input/Output Stacks, Input/Output Items, Nested Functions, and Data Mappings' AS details;
