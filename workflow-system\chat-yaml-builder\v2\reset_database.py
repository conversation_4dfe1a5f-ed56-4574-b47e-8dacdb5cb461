"""
Reset Database Script

This script drops all entity tables and truncates all data in the workflow_temp schema.
"""

import os
import sys
import logging
from typing import List, Tuple

# Add parent directory to path to import common modules
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
from db_utils import execute_query

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('reset_database')

def drop_entity_tables(schema_name: str) -> Tuple[bool, List[str]]:
    """
    Drop all entity tables in the schema.
    
    Args:
        schema_name: Schema name
        
    Returns:
        Tuple containing:
            - Boolean indicating if operation was successful
            - List of messages
    """
    messages = []
    
    try:
        # Get all entity tables
        success, query_messages, result = execute_query(
            f"""
            SELECT table_name 
            FROM information_schema.tables 
            WHERE table_schema = %s
            AND (table_name LIKE 'e%_%%' OR table_name LIKE 'e%.%%')
            """,
            (schema_name,),
            schema_name
        )
        
        if not success:
            messages.extend(query_messages)
            return False, messages
        
        if not result:
            messages.append("No entity tables found")
            return True, messages
        
        # Drop each table
        for row in result:
            table_name = row[0]
            
            drop_query = f'DROP TABLE IF EXISTS {schema_name}."{table_name}" CASCADE'
            success, query_messages, _ = execute_query(drop_query, None, schema_name)
            
            if not success:
                messages.extend(query_messages)
                logger.warning(f"Failed to drop table {schema_name}.{table_name}: {query_messages}")
                continue
            
            messages.append(f"Dropped table {schema_name}.{table_name}")
            logger.info(f"Dropped table {schema_name}.{table_name}")
        
        return True, messages
    except Exception as e:
        error_msg = f"Error dropping entity tables: {str(e)}"
        logger.error(error_msg, exc_info=True)
        messages.append(error_msg)
        return False, messages

def truncate_tables(schema_name: str) -> Tuple[bool, List[str]]:
    """
    Truncate all tables in the schema.
    
    Args:
        schema_name: Schema name
        
    Returns:
        Tuple containing:
            - Boolean indicating if operation was successful
            - List of messages
    """
    messages = []
    
    try:
        # Get all tables
        success, query_messages, result = execute_query(
            f"""
            SELECT table_name 
            FROM information_schema.tables 
            WHERE table_schema = %s
            AND table_type = 'BASE TABLE'
            """,
            (schema_name,),
            schema_name
        )
        
        if not success:
            messages.extend(query_messages)
            return False, messages
        
        if not result:
            messages.append("No tables found")
            return True, messages
        
        # Disable foreign key checks
        set_replica_query = "SET session_replication_role = 'replica'"
        success, query_messages, _ = execute_query(set_replica_query, None, schema_name)
        
        if not success:
            messages.extend(query_messages)
            return False, messages
        
        # Truncate each table
        for row in result:
            table_name = row[0]
            
            # Skip alembic_version table
            if table_name == 'alembic_version':
                continue
            
            truncate_query = f'TRUNCATE TABLE {schema_name}."{table_name}" CASCADE'
            success, query_messages, _ = execute_query(truncate_query, None, schema_name)
            
            if not success:
                messages.extend(query_messages)
                logger.warning(f"Failed to truncate table {schema_name}.{table_name}: {query_messages}")
                continue
            
            messages.append(f"Truncated table {schema_name}.{table_name}")
            logger.info(f"Truncated table {schema_name}.{table_name}")
        
        # Re-enable foreign key checks
        set_origin_query = "SET session_replication_role = 'origin'"
        success, query_messages, _ = execute_query(set_origin_query, None, schema_name)
        
        if not success:
            messages.extend(query_messages)
            return False, messages
        
        return True, messages
    except Exception as e:
        error_msg = f"Error truncating tables: {str(e)}"
        logger.error(error_msg, exc_info=True)
        messages.append(error_msg)
        return False, messages

def main():
    """Main function."""
    schema_name = "workflow_temp"
    logger.info(f"Resetting database schema {schema_name}")
    
    # Drop entity tables
    success, messages = drop_entity_tables(schema_name)
    for message in messages:
        print(message)
    
    if not success:
        logger.error("Failed to drop entity tables")
        return
    
    # Truncate tables
    success, messages = truncate_tables(schema_name)
    for message in messages:
        print(message)
    
    if not success:
        logger.error("Failed to truncate tables")
        return
    
    logger.info(f"Successfully reset database schema {schema_name}")

if __name__ == "__main__":
    main()
