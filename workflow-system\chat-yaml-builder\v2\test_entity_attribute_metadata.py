"""
Test script for entity attribute metadata

This script:
1. Parses the sample entity output file
2. Deploys the entities to the database
3. Checks if the entity_attribute_metadata table has the additional properties stored correctly
"""

import os
import sys
import logging
from typing import Dict, List, Tuple, Any, Optional

# Set up logging
logging.basicConfig(level=logging.INFO, 
                    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger('test_entity_attribute_metadata')

# Import the entity parser and deployer
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from parsers.entity_parser import parse_entities
from deployers.entity_deployer_v2 import deploy_entities, execute_query

def main():
    """
    Main function to test entity attribute metadata.
    """
    logger.info("Testing entity attribute metadata")
    
    # Define schema name
    schema_name = 'workflow_temp'
    
    # Read the sample entity output file
    sample_file_path = os.path.join(os.path.dirname(__file__), 'samples', 'sample_entity_output2.txt')
    with open(sample_file_path, 'r') as f:
        prescriptive_text = f.read()
    
    # Parse the entities
    entities_data, warnings = parse_entities(prescriptive_text)
    
    if warnings:
        logger.warning("Warnings during entity parsing:")
        for warning in warnings:
            logger.warning(f"  - {warning}")
    
    # Deploy the entities
    success, messages = deploy_entities(entities_data, schema_name)
    
    if not success:
        logger.error("Failed to deploy entities")
        for message in messages:
            logger.error(f"  - {message}")
        return
    
    logger.info("Entities deployed successfully")
    
    # Check if the entity_attribute_metadata table has the additional properties stored correctly
    # First, get the entity_id for Employee
    success, query_messages, result = execute_query(
        f"SELECT entity_id FROM {schema_name}.entities WHERE name = %s",
        ('Employee',),
        schema_name
    )
    
    if not success or not result:
        logger.error("Failed to get entity_id for Employee")
        return
    
    entity_id = result[0][0]
    
    # Get the attribute_id for email
    success, query_messages, result = execute_query(
        f"SELECT attribute_id FROM {schema_name}.entity_attributes WHERE entity_id = %s AND name = %s",
        (entity_id, 'email'),
        schema_name
    )
    
    if not success or not result:
        logger.error("Failed to get attribute_id for email")
        return
    
    attribute_id = result[0][0]
    
    # Check if the entity_attribute_metadata table has the additional properties stored correctly
    success, query_messages, result = execute_query(
        f"""
        SELECT 
            attribute_name, 
            required, 
            display_name, 
            key_type, 
            data_type, 
            type, 
            format, 
            values, 
            default_value, 
            validation, 
            error_message, 
            description
        FROM {schema_name}.entity_attribute_metadata 
        WHERE entity_id = %s AND attribute_id = %s
        """,
        (entity_id, attribute_id),
        schema_name
    )
    
    if not success or not result:
        logger.error("Failed to get metadata for email attribute")
        return
    
    # Print the metadata
    metadata = result[0]
    logger.info("Metadata for email attribute:")
    logger.info(f"  - attribute_name: {metadata[0]}")
    logger.info(f"  - required: {metadata[1]}")
    logger.info(f"  - display_name: {metadata[2]}")
    logger.info(f"  - key_type: {metadata[3]}")
    logger.info(f"  - data_type: {metadata[4]}")
    logger.info(f"  - type: {metadata[5]}")
    logger.info(f"  - format: {metadata[6]}")
    logger.info(f"  - values: {metadata[7]}")
    logger.info(f"  - default_value: {metadata[8]}")
    logger.info(f"  - validation: {metadata[9]}")
    logger.info(f"  - error_message: {metadata[10]}")
    logger.info(f"  - description: {metadata[11]}")
    
    # Check if the entity_relationships table has the relationships stored correctly
    success, query_messages, result = execute_query(
        f"""
        SELECT 
            r.id,
            e1.name as source_entity,
            e2.name as target_entity,
            r.relationship_type,
            a1.name as source_attribute,
            a2.name as target_attribute
        FROM {schema_name}.entity_relationships r
        JOIN {schema_name}.entities e1 ON r.source_entity_id = e1.entity_id
        JOIN {schema_name}.entities e2 ON r.target_entity_id = e2.entity_id
        JOIN {schema_name}.entity_attributes a1 ON r.source_attribute_id = a1.attribute_id
        JOIN {schema_name}.entity_attributes a2 ON r.target_attribute_id = a2.attribute_id
        WHERE e1.name = 'Employee'
        """,
        schema_name=schema_name
    )
    
    if not success:
        logger.error("Failed to get relationships for Employee")
        return
    
    # Print the relationships
    logger.info("Relationships for Employee:")
    for rel in result:
        logger.info(f"  - {rel[0]}: {rel[1]} has {rel[3]} relationship with {rel[2]} using {rel[4]} to {rel[5]}")
    
    logger.info("Test completed successfully")

if __name__ == "__main__":
    main()
