# Role Prescriptive Template

This template defines the structure for role definitions in the system. The role definitions follow a specific format that can be parsed and deployed to the database.

## Role Definition Format

```
Role [RoleName] ([role_id]) inherits [ParentRole]:
- Create: [Entity1], [Entity2] (attr1^deny, attr2^deny)
- Read: [Entity1], [Entity2] (attr3^deny, attr4^deny)
- Update: [Entity1] (attr5^deny), [Entity2]
- GO: [go001] as [RoleType], [go002] as [RoleType]
- Scope: [ScopeLevel]
- Classification: [RoleClassification]
- Special: [SpecialConditions]
```

## Field Definitions

- **RoleName**: The name of the role (e.g., Em<PERSON>loyee, Manager, Administrator)
- **role_id**: Unique identifier for the role in the format "roleXXX" (e.g., role001, role002)
- **ParentRole**: Optional. The name of the parent role if this role inherits permissions
- **Create**: List of entities that this role can create, with optional denied attributes
- **Read**: List of entities that this role can read, with optional denied attributes
- **Update**: List of entities that this role can update, with optional denied attributes
- **GO**: List of Global Objectives (GOs) that this role can participate in, with the role type
- **Scope**: The scope level of the role (Own, Team, Department, Organization)
- **Classification**: The classification of the role (Standard, Administrative, Temporary, Emergency)
- **Special**: Optional. Any special conditions or privileges for this role

## Validation Rules

1. **Role ID Format**: Must be in the format "roleXXX" where XXX is a number
2. **Entity References**: All entities referenced must exist in the system
3. **GO References**: All GOs referenced must exist in the system
4. **Role Types**: Must be one of: Originator, ProcessOwner, Sponsor
5. **Scope Levels**: Must be one of: Own, Team, Department, Organization
6. **Classifications**: Must be one of: Standard, Administrative, Temporary, Emergency
7. **Inheritance**: If a role inherits from another role, the parent role must exist

## Examples

### Basic Role

```
Role Employee (role001):
- Create: LeaveApplication
- Read: LeaveApplication, Employee (salary^deny, performanceRating^deny)
- Update: Employee (own only - salary^deny, performanceRating^deny)
- GO: go001 as Originator
- Scope: Own
- Classification: Standard
```

### Role with Inheritance

```
Role Manager (role002) inherits Employee:
- Create: PerformanceReview, TeamBudget
- Read: Employee (salary^deny, ssn^deny), TeamMetrics
- Update: PerformanceReview, TeamBudget (within limits)
- GO: go001 as ProcessOwner, go002 as Originator
- Scope: Team
- Classification: Standard
- Special: budget approval up to $10K
```

## Parsing Guidelines

When parsing role definitions:

1. Extract the role name and ID from the first line
2. Check for inheritance by looking for "inherits" keyword
3. Parse each permission section (Create, Read, Update) to extract entities and denied attributes
4. Parse the GO section to extract GO IDs and role types
5. Extract scope, classification, and special conditions
6. Validate all references against the system database
7. Apply inheritance by combining permissions from the parent role
