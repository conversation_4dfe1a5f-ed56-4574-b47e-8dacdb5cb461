{"timestamp": "2025-06-23T05:05:16.626704", "endpoint": "parse-validate-mongosave/constants", "input": {"natural_language": "Tenant: Acme Corp\n\nAttributes:\nAttribute Name | Display Name | Data Type | Required | Unique | Default Type | Default Value | Description | Helper Text\n\nconstantId | Constant ID | string | true | true | computation | CONST-{sequence} | Unique identifier for the constant | Auto-generated constant identifier\n\nattribute | Attribute Name | string | true | true | static value | | Name of the configuration attribute | Enter attribute name\n\nvalue | Value | string | true | false | static value | | Value of the configuration attribute | Enter attribute value\n\ndataType | Data Type | string | true | false | static value | string | Data type of the constant value | Select data type\n\ndescription | Description | string | false | false | static value | | Description of the constant | Enter description\n\nstatus | Status | string | true | false | static value | active | Status of the constant | Select status", "tenant_id": "T_ACME_CORP", "edit_draft": false, "edit_temp": false, "edit_production": false}, "output": {"success": true, "constant_results": [{"success": false, "errors": ["Tenant with tenant_id T_ACME_CORP does not exist"], "parsed_data": {"constant_id": 1003, "attribute": "constantId", "value": "CONST-{sequence}", "description": "Unique identifier for the constant", "tenant_id": "T_ACME_CORP", "allow_override": false, "override_permissions": "", "status": "active", "entity_name": "", "attribute_name": "", "created_at": "2025-06-23T05:05:16.388064", "updated_at": "2025-06-23T05:05:16.388064", "created_by": "Tarun", "updated_by": "Tarun", "constant_status": "new", "changes_detected": []}}, {"success": false, "errors": ["value is required", "Tenant with tenant_id T_ACME_CORP does not exist"], "parsed_data": {"constant_id": 1003, "attribute": "attribute", "value": "", "description": "Name of the configuration attribute", "tenant_id": "T_ACME_CORP", "allow_override": false, "override_permissions": "", "status": "active", "entity_name": "", "attribute_name": "", "created_at": "2025-06-23T05:05:16.408523", "updated_at": "2025-06-23T05:05:16.408523", "created_by": "Tarun", "updated_by": "Tarun", "constant_status": "new", "changes_detected": []}}, {"success": false, "errors": ["value is required", "Tenant with tenant_id T_ACME_CORP does not exist"], "parsed_data": {"constant_id": 1003, "attribute": "value", "value": "", "description": "Value of the configuration attribute", "tenant_id": "T_ACME_CORP", "allow_override": false, "override_permissions": "", "status": "active", "entity_name": "", "attribute_name": "", "created_at": "2025-06-23T05:05:16.426322", "updated_at": "2025-06-23T05:05:16.426322", "created_by": "Tarun", "updated_by": "Tarun", "constant_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Tenant with tenant_id T_ACME_CORP does not exist"], "parsed_data": {"constant_id": 1003, "attribute": "dataType", "value": "string", "description": "Data type of the constant value", "tenant_id": "T_ACME_CORP", "allow_override": false, "override_permissions": "", "status": "active", "entity_name": "", "attribute_name": "", "created_at": "2025-06-23T05:05:16.444954", "updated_at": "2025-06-23T05:05:16.444954", "created_by": "Tarun", "updated_by": "Tarun", "constant_status": "new", "changes_detected": []}}, {"success": false, "errors": ["value is required", "Tenant with tenant_id T_ACME_CORP does not exist"], "parsed_data": {"constant_id": 1003, "attribute": "description", "value": "", "description": "Description of the constant", "tenant_id": "T_ACME_CORP", "allow_override": false, "override_permissions": "", "status": "active", "entity_name": "", "attribute_name": "", "created_at": "2025-06-23T05:05:16.462068", "updated_at": "2025-06-23T05:05:16.462068", "created_by": "Tarun", "updated_by": "Tarun", "constant_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Tenant with tenant_id T_ACME_CORP does not exist"], "parsed_data": {"constant_id": 1003, "attribute": "status", "value": "active", "description": "Status of the constant", "tenant_id": "T_ACME_CORP", "allow_override": false, "override_permissions": "", "status": "active", "entity_name": "", "attribute_name": "", "created_at": "2025-06-23T05:05:16.478389", "updated_at": "2025-06-23T05:05:16.478389", "created_by": "Tarun", "updated_by": "Tarun", "constant_status": "new", "changes_detected": []}}], "operation": "parse_validate_mongosave", "total_constants": 6}, "status": "success"}
{"timestamp": "2025-06-23T05:24:27.851089", "endpoint": "parse-validate-mongosave/constants", "input": {"natural_language": "Tenant: Acme Corp\n\nAttributes:\nAttribute Name | Display Name | Data Type | Required | Unique | Default Type | Default Value | Description | Helper Text\n\nconstantId | Constant ID | string | true | true | computation | CONST-{sequence} | Unique identifier for the constant | Auto-generated constant identifier\n\nattribute | Attribute Name | string | true | true | static value | | Name of the configuration attribute | Enter attribute name\n\nvalue | Value | string | true | false | static value | | Value of the configuration attribute | Enter attribute value\n\ndataType | Data Type | string | true | false | static value | string | Data type of the constant value | Select data type\n\ndescription | Description | string | false | false | static value | | Description of the constant | Enter description\n\nstatus | Status | string | true | false | static value | active | Status of the constant | Select status", "tenant_id": "T_ACME_CORP", "edit_draft": false, "edit_temp": false, "edit_production": false}, "output": {"success": true, "constant_results": [{"success": false, "errors": ["Tenant with tenant_id T_ACME_CORP does not exist"], "parsed_data": {"constant_id": 1003, "attribute": "constantId", "value": "CONST-{sequence}", "description": "Unique identifier for the constant", "tenant_id": "T_ACME_CORP", "allow_override": false, "override_permissions": "", "status": "active", "entity_name": "", "attribute_name": "", "created_at": "2025-06-23T05:24:27.654715", "updated_at": "2025-06-23T05:24:27.654715", "created_by": "Tarun", "updated_by": "Tarun", "constant_status": "new", "changes_detected": []}}, {"success": false, "errors": ["value is required", "Tenant with tenant_id T_ACME_CORP does not exist"], "parsed_data": {"constant_id": 1003, "attribute": "attribute", "value": "", "description": "Name of the configuration attribute", "tenant_id": "T_ACME_CORP", "allow_override": false, "override_permissions": "", "status": "active", "entity_name": "", "attribute_name": "", "created_at": "2025-06-23T05:24:27.668718", "updated_at": "2025-06-23T05:24:27.668718", "created_by": "Tarun", "updated_by": "Tarun", "constant_status": "new", "changes_detected": []}}, {"success": false, "errors": ["value is required", "Tenant with tenant_id T_ACME_CORP does not exist"], "parsed_data": {"constant_id": 1003, "attribute": "value", "value": "", "description": "Value of the configuration attribute", "tenant_id": "T_ACME_CORP", "allow_override": false, "override_permissions": "", "status": "active", "entity_name": "", "attribute_name": "", "created_at": "2025-06-23T05:24:27.684259", "updated_at": "2025-06-23T05:24:27.684259", "created_by": "Tarun", "updated_by": "Tarun", "constant_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Tenant with tenant_id T_ACME_CORP does not exist"], "parsed_data": {"constant_id": 1003, "attribute": "dataType", "value": "string", "description": "Data type of the constant value", "tenant_id": "T_ACME_CORP", "allow_override": false, "override_permissions": "", "status": "active", "entity_name": "", "attribute_name": "", "created_at": "2025-06-23T05:24:27.698282", "updated_at": "2025-06-23T05:24:27.698282", "created_by": "Tarun", "updated_by": "Tarun", "constant_status": "new", "changes_detected": []}}, {"success": false, "errors": ["value is required", "Tenant with tenant_id T_ACME_CORP does not exist"], "parsed_data": {"constant_id": 1003, "attribute": "description", "value": "", "description": "Description of the constant", "tenant_id": "T_ACME_CORP", "allow_override": false, "override_permissions": "", "status": "active", "entity_name": "", "attribute_name": "", "created_at": "2025-06-23T05:24:27.714691", "updated_at": "2025-06-23T05:24:27.714691", "created_by": "Tarun", "updated_by": "Tarun", "constant_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Tenant with tenant_id T_ACME_CORP does not exist"], "parsed_data": {"constant_id": 1003, "attribute": "status", "value": "active", "description": "Status of the constant", "tenant_id": "T_ACME_CORP", "allow_override": false, "override_permissions": "", "status": "active", "entity_name": "", "attribute_name": "", "created_at": "2025-06-23T05:24:27.730455", "updated_at": "2025-06-23T05:24:27.730455", "created_by": "Tarun", "updated_by": "Tarun", "constant_status": "new", "changes_detected": []}}], "operation": "parse_validate_mongosave", "total_constants": 6}, "status": "success"}
{"timestamp": "2025-06-23T05:43:10.180013", "endpoint": "parse-validate-mongosave/constants", "input": {"natural_language": "Tenant: Acme Corp Test\n\nAttributes:\nAttribute Name | Display Name | Data Type | Required | Unique | Default Type | Default Value | Description | Helper Text\n\nMAX_LEAVE_DAYS | Maximum Leave Days | integer | true | true | static value | 30 | Maximum number of leave days per year | Enter maximum days\n\nDEFAULT_LEAVE_TYPE | Default Leave Type | string | true | false | static value | Annual | Default leave type for new requests | Select default type\n\nAPPROVAL_REQUIRED | Approval Required | boolean | true | false | static value | true | Whether approval is required for leave requests | Check if approval needed\n\nNOTIFICATION_EMAIL | Notification Email | string | true | false | static value | <EMAIL> | Email for leave notifications | Enter notification email", "tenant_id": "T1007", "edit_draft": false, "edit_temp": false, "edit_production": false}, "output": {"success": true, "constant_results": [{"success": false, "errors": ["Tenant with tenant_id T1007 does not exist"], "parsed_data": {"constant_id": 1003, "attribute": "MAX_LEAVE_DAYS", "value": "30", "description": "Maximum number of leave days per year", "tenant_id": "T1007", "allow_override": false, "override_permissions": "", "status": "active", "entity_name": "", "attribute_name": "", "created_at": "2025-06-23T05:43:10.033800", "updated_at": "2025-06-23T05:43:10.033800", "created_by": "Tarun", "updated_by": "Tarun", "constant_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Tenant with tenant_id T1007 does not exist"], "parsed_data": {"constant_id": 1003, "attribute": "DEFAULT_LEAVE_TYPE", "value": "Annual", "description": "Default leave type for new requests", "tenant_id": "T1007", "allow_override": false, "override_permissions": "", "status": "active", "entity_name": "", "attribute_name": "", "created_at": "2025-06-23T05:43:10.060348", "updated_at": "2025-06-23T05:43:10.060348", "created_by": "Tarun", "updated_by": "Tarun", "constant_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Tenant with tenant_id T1007 does not exist"], "parsed_data": {"constant_id": 1003, "attribute": "APPROVAL_REQUIRED", "value": "true", "description": "Whether approval is required for leave requests", "tenant_id": "T1007", "allow_override": false, "override_permissions": "", "status": "active", "entity_name": "", "attribute_name": "", "created_at": "2025-06-23T05:43:10.077121", "updated_at": "2025-06-23T05:43:10.077121", "created_by": "Tarun", "updated_by": "Tarun", "constant_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Tenant with tenant_id T1007 does not exist"], "parsed_data": {"constant_id": 1003, "attribute": "NOTIFICATION_EMAIL", "value": "<EMAIL>", "description": "Email for leave notifications", "tenant_id": "T1007", "allow_override": false, "override_permissions": "", "status": "active", "entity_name": "", "attribute_name": "", "created_at": "2025-06-23T05:43:10.091747", "updated_at": "2025-06-23T05:43:10.091747", "created_by": "Tarun", "updated_by": "Tarun", "constant_status": "new", "changes_detected": []}}], "operation": "parse_validate_mongosave", "total_constants": 4}, "status": "success"}
{"timestamp": "2025-06-23T05:46:23.950162", "endpoint": "parse-validate-mongosave/constants", "input": {"natural_language": "Tenant: Test\n\nAttributes:\nAttribute Name | Display Name | Data Type | Required | Unique | Default Type | Default Value | Description | Helper Text\n\nMAX_LEAVE_DAYS | Maximum Leave Days | integer | true | true | static value | 30 | Maximum number of leave days per year | Enter maximum days\n\nDEFAULT_LEAVE_TYPE | Default Leave Type | string | true | false | static value | Annual | Default leave type for new requests | Select default type", "tenant_id": null, "edit_draft": false, "edit_temp": false, "edit_production": false}, "output": {"success": true, "constant_results": [{"success": true, "saved_data": {"constant_id": 1003, "attribute": "MAX_LEAVE_DAYS", "value": "30", "description": "Maximum number of leave days per year", "tenant_id": null, "allow_override": false, "override_permissions": "", "status": "draft", "entity_name": "", "attribute_name": "", "created_at": "2025-06-23T05:46:23.922795", "updated_at": "2025-06-23T05:46:23.922805", "created_by": "Tarun", "updated_by": "Tarun", "constant_status": "new", "changes_detected": [], "version": 1, "_id": "6858ea2f1195bbcbf7aa768f"}, "validation_result": {"structure_errors": [], "required_field_errors": [], "data_type_errors": [], "custom_errors": []}, "dependency_errors": [], "uniqueness_result": {"is_unique": true, "status": "unique", "message": "Constant with constant_id 1003 and attribute MAX_LEAVE_DAYS is unique"}}, {"success": false, "error": "Item already exists in draft. Use edit_draft=true to update.", "existing_in": "mongo", "existing_document": {"_id": "6858ea2f1195bbcbf7aa768f", "constant_id": 1003, "attribute": "MAX_LEAVE_DAYS", "value": "30", "description": "Maximum number of leave days per year", "tenant_id": null, "allow_override": false, "override_permissions": "", "status": "draft", "entity_name": "", "attribute_name": "", "created_at": "2025-06-23T05:46:23.922795", "updated_at": "2025-06-23T05:46:23.922805", "created_by": "Tarun", "updated_by": "Tarun", "constant_status": "new", "changes_detected": [], "version": 1}, "parsed_data": {"constant_id": 1003, "attribute": "DEFAULT_LEAVE_TYPE", "value": "Annual", "description": "Default leave type for new requests", "tenant_id": null, "allow_override": false, "override_permissions": "", "status": "active", "entity_name": "", "attribute_name": "", "created_at": "2025-06-23T05:46:23.903254", "updated_at": "2025-06-23T05:46:23.903254", "created_by": "Tarun", "updated_by": "Tarun", "constant_status": "new", "changes_detected": []}}], "operation": "parse_validate_mongosave", "total_constants": 2}, "status": "success"}
{"timestamp": "2025-06-23T06:18:56.381294", "endpoint": "parse-validate-mongosave/constants", "input": {"natural_language": "Tenant: Acme Corp Test\n\nAttributes:\nAttribute Name | Display Name | Data Type | Required | Unique | Default Type | Default Value | Description | Helper Text\n\nMAX_LEAVE_DAYS_ANNUAL | Maximum Annual Leave Days | integer | true | true | static value | 25 | Maximum number of annual leave days per year | Enter maximum annual days\n\nDEFAULT_LEAVE_TYPE | Default Leave Type | string | true | false | static value | Annual | Default leave type for new requests | Select default type\n\nAPPROVAL_REQUIRED | Approval Required | boolean | true | false | static value | true | Whether approval is required for leave requests | Check if approval needed", "tenant_id": "T1007", "edit_draft": false, "edit_temp": false, "edit_production": false}, "output": {"success": true, "constant_results": [{"success": false, "errors": ["Tenant with tenant_id T1007 does not exist"], "parsed_data": {"constant_id": 1003, "attribute": "MAX_LEAVE_DAYS_ANNUAL", "value": "25", "description": "Maximum number of annual leave days per year", "tenant_id": "T1007", "allow_override": false, "override_permissions": "", "status": "active", "entity_name": "", "attribute_name": "", "created_at": "2025-06-23T06:18:56.280162", "updated_at": "2025-06-23T06:18:56.280162", "created_by": "Tarun", "updated_by": "Tarun", "constant_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Tenant with tenant_id T1007 does not exist"], "parsed_data": {"constant_id": 1003, "attribute": "DEFAULT_LEAVE_TYPE", "value": "Annual", "description": "Default leave type for new requests", "tenant_id": "T1007", "allow_override": false, "override_permissions": "", "status": "active", "entity_name": "", "attribute_name": "", "created_at": "2025-06-23T06:18:56.296486", "updated_at": "2025-06-23T06:18:56.296486", "created_by": "Tarun", "updated_by": "Tarun", "constant_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Tenant with tenant_id T1007 does not exist"], "parsed_data": {"constant_id": 1003, "attribute": "APPROVAL_REQUIRED", "value": "true", "description": "Whether approval is required for leave requests", "tenant_id": "T1007", "allow_override": false, "override_permissions": "", "status": "active", "entity_name": "", "attribute_name": "", "created_at": "2025-06-23T06:18:56.310492", "updated_at": "2025-06-23T06:18:56.310492", "created_by": "Tarun", "updated_by": "Tarun", "constant_status": "new", "changes_detected": []}}], "operation": "parse_validate_mongosave", "total_constants": 3}, "status": "success"}
{"timestamp": "2025-06-23T06:21:50.852503", "endpoint": "parse-validate-mongosave/constants", "input": {"natural_language": "Tenant: Acme Corp Test\n\nAttributes:\nAttribute Name | Display Name | Data Type | Required | Unique | Default Type | Default Value | Description | Helper Text\n\nMAX_LEAVE_DAYS_ANNUAL | Maximum Annual Leave Days | integer | true | true | static value | 25 | Maximum number of annual leave days per year | Enter maximum annual days\n\nDEFAULT_LEAVE_TYPE | Default Leave Type | string | true | false | static value | Annual | Default leave type for new requests | Select default type\n\nAPPROVAL_REQUIRED | Approval Required | boolean | true | false | static value | true | Whether approval is required for leave requests | Check if approval needed", "tenant_id": "T1007", "edit_draft": false, "edit_temp": false, "edit_production": false}, "output": {"success": true, "constant_results": [{"success": false, "error": "Item already exists in draft. Use edit_draft=true to update.", "existing_in": "mongo", "existing_document": {"_id": "6858ea2f1195bbcbf7aa768f", "constant_id": 1003, "attribute": "MAX_LEAVE_DAYS", "value": "30", "description": "Maximum number of leave days per year", "tenant_id": null, "allow_override": false, "override_permissions": "", "status": "draft", "entity_name": "", "attribute_name": "", "created_at": "2025-06-23T05:46:23.922795", "updated_at": "2025-06-23T05:46:23.922805", "created_by": "Tarun", "updated_by": "Tarun", "constant_status": "new", "changes_detected": [], "version": 1}, "parsed_data": {"constant_id": 1003, "attribute": "MAX_LEAVE_DAYS_ANNUAL", "value": "25", "description": "Maximum number of annual leave days per year", "tenant_id": "T1007", "allow_override": false, "override_permissions": "", "status": "active", "entity_name": "", "attribute_name": "", "created_at": "2025-06-23T06:21:50.751869", "updated_at": "2025-06-23T06:21:50.751869", "created_by": "Tarun", "updated_by": "Tarun", "constant_status": "new", "changes_detected": []}}, {"success": false, "error": "Item already exists in draft. Use edit_draft=true to update.", "existing_in": "mongo", "existing_document": {"_id": "6858ea2f1195bbcbf7aa768f", "constant_id": 1003, "attribute": "MAX_LEAVE_DAYS", "value": "30", "description": "Maximum number of leave days per year", "tenant_id": null, "allow_override": false, "override_permissions": "", "status": "draft", "entity_name": "", "attribute_name": "", "created_at": "2025-06-23T05:46:23.922795", "updated_at": "2025-06-23T05:46:23.922805", "created_by": "Tarun", "updated_by": "Tarun", "constant_status": "new", "changes_detected": [], "version": 1}, "parsed_data": {"constant_id": 1003, "attribute": "DEFAULT_LEAVE_TYPE", "value": "Annual", "description": "Default leave type for new requests", "tenant_id": "T1007", "allow_override": false, "override_permissions": "", "status": "active", "entity_name": "", "attribute_name": "", "created_at": "2025-06-23T06:21:50.766792", "updated_at": "2025-06-23T06:21:50.766792", "created_by": "Tarun", "updated_by": "Tarun", "constant_status": "new", "changes_detected": []}}, {"success": false, "error": "Item already exists in draft. Use edit_draft=true to update.", "existing_in": "mongo", "existing_document": {"_id": "6858ea2f1195bbcbf7aa768f", "constant_id": 1003, "attribute": "MAX_LEAVE_DAYS", "value": "30", "description": "Maximum number of leave days per year", "tenant_id": null, "allow_override": false, "override_permissions": "", "status": "draft", "entity_name": "", "attribute_name": "", "created_at": "2025-06-23T05:46:23.922795", "updated_at": "2025-06-23T05:46:23.922805", "created_by": "Tarun", "updated_by": "Tarun", "constant_status": "new", "changes_detected": [], "version": 1}, "parsed_data": {"constant_id": 1003, "attribute": "APPROVAL_REQUIRED", "value": "true", "description": "Whether approval is required for leave requests", "tenant_id": "T1007", "allow_override": false, "override_permissions": "", "status": "active", "entity_name": "", "attribute_name": "", "created_at": "2025-06-23T06:21:50.782214", "updated_at": "2025-06-23T06:21:50.782214", "created_by": "Tarun", "updated_by": "Tarun", "constant_status": "new", "changes_detected": []}}], "operation": "parse_validate_mongosave", "total_constants": 3}, "status": "success"}
{"timestamp": "2025-06-23T06:23:23.394793", "endpoint": "parse-validate-mongosave/constants", "input": {"natural_language": "Tenant: Acme Corp Test\n\nAttributes:\nAttribute Name | Display Name | Data Type | Required | Unique | Default Type | Default Value | Description | Helper Text\n\nNOTIFICATION_EMAIL | Notification Email | string | true | false | static value | <EMAIL> | Email for leave notifications | Enter notification email\n\nWORKING_HOURS_PER_DAY | Working Hours Per Day | integer | true | false | static value | 8 | Standard working hours per day | Enter working hours", "tenant_id": "T1007", "edit_draft": false, "edit_temp": false, "edit_production": false}, "output": {"success": true, "constant_results": [{"success": false, "error": "Item already exists in draft. Use edit_draft=true to update.", "existing_in": "mongo", "existing_document": {"_id": "6858ea2f1195bbcbf7aa768f", "constant_id": 1003, "attribute": "MAX_LEAVE_DAYS", "value": "30", "description": "Maximum number of leave days per year", "tenant_id": null, "allow_override": false, "override_permissions": "", "status": "draft", "entity_name": "", "attribute_name": "", "created_at": "2025-06-23T05:46:23.922795", "updated_at": "2025-06-23T05:46:23.922805", "created_by": "Tarun", "updated_by": "Tarun", "constant_status": "new", "changes_detected": [], "version": 1}, "parsed_data": {"constant_id": 1003, "attribute": "NOTIFICATION_EMAIL", "value": "<EMAIL>", "description": "Email for leave notifications", "tenant_id": "T1007", "allow_override": false, "override_permissions": "", "status": "active", "entity_name": "", "attribute_name": "", "created_at": "2025-06-23T06:23:23.339571", "updated_at": "2025-06-23T06:23:23.339571", "created_by": "Tarun", "updated_by": "Tarun", "constant_status": "new", "changes_detected": []}}, {"success": false, "error": "Item already exists in draft. Use edit_draft=true to update.", "existing_in": "mongo", "existing_document": {"_id": "6858ea2f1195bbcbf7aa768f", "constant_id": 1003, "attribute": "MAX_LEAVE_DAYS", "value": "30", "description": "Maximum number of leave days per year", "tenant_id": null, "allow_override": false, "override_permissions": "", "status": "draft", "entity_name": "", "attribute_name": "", "created_at": "2025-06-23T05:46:23.922795", "updated_at": "2025-06-23T05:46:23.922805", "created_by": "Tarun", "updated_by": "Tarun", "constant_status": "new", "changes_detected": [], "version": 1}, "parsed_data": {"constant_id": 1003, "attribute": "WORKING_HOURS_PER_DAY", "value": "8", "description": "Standard working hours per day", "tenant_id": "T1007", "allow_override": false, "override_permissions": "", "status": "active", "entity_name": "", "attribute_name": "", "created_at": "2025-06-23T06:23:23.354112", "updated_at": "2025-06-23T06:23:23.354112", "created_by": "Tarun", "updated_by": "Tarun", "constant_status": "new", "changes_detected": []}}], "operation": "parse_validate_mongosave", "total_constants": 2}, "status": "success"}
{"timestamp": "2025-06-23T06:24:15.954151", "endpoint": "parse-validate-mongosave/constants", "input": {"natural_language": "Tenant: Acme Corp Test\n\nAttributes:\nAttribute Name | Display Name | Data Type | Required | Unique | Default Type | Default Value | Description | Helper Text\n\nMAX_LEAVE_DAYS_ANNUAL | Maximum Annual Leave Days | integer | true | true | static value | 25 | Maximum number of annual leave days per year | Enter maximum annual days\n\nDEFAULT_LEAVE_TYPE | Default Leave Type | string | true | false | static value | Annual | Default leave type for new requests | Select default type", "tenant_id": "T1007", "edit_draft": false, "edit_temp": false, "edit_production": false}, "output": {"success": true, "constant_results": [{"success": true, "saved_data": {"constant_id": 1003, "attribute": "MAX_LEAVE_DAYS_ANNUAL", "value": "25", "description": "Maximum number of annual leave days per year", "tenant_id": "T1007", "allow_override": false, "override_permissions": "", "status": "draft", "entity_name": "", "attribute_name": "", "created_at": "2025-06-23T06:24:15.926637", "updated_at": "2025-06-23T06:24:15.926647", "created_by": "Tarun", "updated_by": "Tarun", "constant_status": "new", "changes_detected": [], "version": 1, "_id": "6858f30f1195bbcbf7aa7691"}, "validation_result": {"structure_errors": [], "required_field_errors": [], "data_type_errors": [], "custom_errors": []}, "dependency_errors": [], "uniqueness_result": {"is_unique": true, "status": "unique", "message": "Constant with constant_id 1003 and attribute MAX_LEAVE_DAYS_ANNUAL is unique"}}, {"success": false, "error": "Item already exists in draft. Use edit_draft=true to update.", "existing_in": "mongo", "existing_document": {"_id": "6858f30f1195bbcbf7aa7691", "constant_id": 1003, "attribute": "MAX_LEAVE_DAYS_ANNUAL", "value": "25", "description": "Maximum number of annual leave days per year", "tenant_id": "T1007", "allow_override": false, "override_permissions": "", "status": "draft", "entity_name": "", "attribute_name": "", "created_at": "2025-06-23T06:24:15.926637", "updated_at": "2025-06-23T06:24:15.926647", "created_by": "Tarun", "updated_by": "Tarun", "constant_status": "new", "changes_detected": [], "version": 1}, "parsed_data": {"constant_id": 1003, "attribute": "DEFAULT_LEAVE_TYPE", "value": "Annual", "description": "Default leave type for new requests", "tenant_id": "T1007", "allow_override": false, "override_permissions": "", "status": "active", "entity_name": "", "attribute_name": "", "created_at": "2025-06-23T06:24:15.899877", "updated_at": "2025-06-23T06:24:15.899877", "created_by": "Tarun", "updated_by": "Tarun", "constant_status": "new", "changes_detected": []}}], "operation": "parse_validate_mongosave", "total_constants": 2}, "status": "success"}
{"timestamp": "2025-06-23T08:22:09.224187", "endpoint": "parse-validate-mongosave/constants", "input": {"natural_language": "Tenant: Acme Corp\n\nAttributes:\nAttribute Name | Display Name | Data Type | Required | Unique | Default Type | Default Value | Description | Helper Text\n\nconstantId | Constant ID | string | true | true | computation | CONST-{sequence} | Unique identifier for the constant | Auto-generated constant identifier\n\nattribute | Attribute Name | string | true | true | static value | | Name of the configuration attribute | Enter attribute name\n\nvalue | Value | string | true | false | static value | | Value of the configuration attribute | Enter attribute value\n\ndataType | Data Type | string | true | false | static value | string | Data type of the constant value | Select data type\n\ndescription | Description | string | false | false | static value | | Description of the constant | Enter description\n\nstatus | Status | string | true | false | static value | active | Status of the constant | Select status", "tenant_id": "T_ACME_CORP", "edit_draft": false, "edit_temp": false, "edit_production": false}, "output": {"success": true, "constant_results": [{"success": false, "errors": ["Tenant with tenant_id T_ACME_CORP does not exist"], "parsed_data": {"constant_id": 1003, "attribute": "constantId", "value": "CONST-{sequence}", "description": "Unique identifier for the constant", "tenant_id": "T_ACME_CORP", "allow_override": false, "override_permissions": "", "status": "active", "entity_name": "", "attribute_name": "", "created_at": "2025-06-23T08:22:09.016138", "updated_at": "2025-06-23T08:22:09.016138", "created_by": "Tarun", "updated_by": "Tarun", "constant_status": "new", "changes_detected": []}}, {"success": false, "errors": ["value is required", "Tenant with tenant_id T_ACME_CORP does not exist"], "parsed_data": {"constant_id": 1003, "attribute": "attribute", "value": "", "description": "Name of the configuration attribute", "tenant_id": "T_ACME_CORP", "allow_override": false, "override_permissions": "", "status": "active", "entity_name": "", "attribute_name": "", "created_at": "2025-06-23T08:22:09.034130", "updated_at": "2025-06-23T08:22:09.034130", "created_by": "Tarun", "updated_by": "Tarun", "constant_status": "new", "changes_detected": []}}, {"success": false, "errors": ["value is required", "Tenant with tenant_id T_ACME_CORP does not exist"], "parsed_data": {"constant_id": 1003, "attribute": "value", "value": "", "description": "Value of the configuration attribute", "tenant_id": "T_ACME_CORP", "allow_override": false, "override_permissions": "", "status": "active", "entity_name": "", "attribute_name": "", "created_at": "2025-06-23T08:22:09.049565", "updated_at": "2025-06-23T08:22:09.049565", "created_by": "Tarun", "updated_by": "Tarun", "constant_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Tenant with tenant_id T_ACME_CORP does not exist"], "parsed_data": {"constant_id": 1003, "attribute": "dataType", "value": "string", "description": "Data type of the constant value", "tenant_id": "T_ACME_CORP", "allow_override": false, "override_permissions": "", "status": "active", "entity_name": "", "attribute_name": "", "created_at": "2025-06-23T08:22:09.063919", "updated_at": "2025-06-23T08:22:09.063919", "created_by": "Tarun", "updated_by": "Tarun", "constant_status": "new", "changes_detected": []}}, {"success": false, "errors": ["value is required", "Tenant with tenant_id T_ACME_CORP does not exist"], "parsed_data": {"constant_id": 1003, "attribute": "description", "value": "", "description": "Description of the constant", "tenant_id": "T_ACME_CORP", "allow_override": false, "override_permissions": "", "status": "active", "entity_name": "", "attribute_name": "", "created_at": "2025-06-23T08:22:09.079152", "updated_at": "2025-06-23T08:22:09.079152", "created_by": "Tarun", "updated_by": "Tarun", "constant_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Tenant with tenant_id T_ACME_CORP does not exist"], "parsed_data": {"constant_id": 1003, "attribute": "status", "value": "active", "description": "Status of the constant", "tenant_id": "T_ACME_CORP", "allow_override": false, "override_permissions": "", "status": "active", "entity_name": "", "attribute_name": "", "created_at": "2025-06-23T08:22:09.093376", "updated_at": "2025-06-23T08:22:09.093376", "created_by": "Tarun", "updated_by": "Tarun", "constant_status": "new", "changes_detected": []}}], "operation": "parse_validate_mongosave", "total_constants": 6}, "status": "success"}
{"timestamp": "2025-06-23T09:02:49.762721", "endpoint": "parse-validate-mongosave/constants", "input": {"natural_language": "Tenant: Acme Corp\n\nConstants has constantId^PK, attribute, value, description, dataType, status, allow_override, override_permissions, entity_name, attribute_name.\n\nEntity: Constants\n- Entity Name: Constants\n- Display Name: System Constants\n- Type: configuration\n- Description: Stores system-wide configuration values and constants\n- Business Domain: System Configuration\n- Category: Configuration Management\n- Tags: constants, configuration, system, settings\n- Archival Strategy: archive_delete\n- Icon: settings\n- Colour Theme: gray\n\nAttributes:\nAttribute Name | Display Name | Data Type | Required | Unique | Default Type | Default Value | Description | Helper Text\n\nconstantId | Constant ID | string | true | true | computation | CONST-{sequence} | Unique identifier for the constant | Auto-generated constant identifier\n\nattribute | Attribute Name | string | true | true | static value | | Name of the configuration attribute | Enter attribute name\n\nvalue | Value | string | true | false | static value | | Value of the configuration attribute | Enter attribute value\n\ndescription | Description | text | false | false | static value | | Description of the configuration attribute | Enter detailed description\n\ndataType | Data Type | string | true | false | static value | String | Data type of the configuration value | Select data type from dropdown\n\nstatus | Status | string | true | false | static value | Active | Current status of the constant | Select status from dropdown\n\nallow_override | Allow Override | boolean | false | false | static value | false | Whether this constant can be overridden | Check to allow override\n\noverride_permissions | Override Permissions | string | false | false | static value | | Permissions required to override this constant | Enter required permissions\n\nentity_name | Entity Name | string | false | false | static value | | Entity this constant is associated with | Enter entity name\n\nattribute_name | Attribute Name | string | false | false | static value | | Specific attribute this constant relates to | Enter attribute name\n", "tenant_id": "T1001", "edit_draft": true, "edit_temp": false, "edit_production": false}, "output": {"success": true, "constant_results": [{"success": true, "saved_data": {"constant_id": 1003, "attribute": "constantId", "value": "CONST-{sequence}", "description": "Unique identifier for the constant", "tenant_id": "T1001", "allow_override": false, "override_permissions": "", "status": "active", "entity_name": "", "attribute_name": "", "created_at": "2025-06-23T09:02:49.367582", "updated_at": "2025-06-23T09:02:49.543345", "created_by": "Tarun", "updated_by": "Tarun", "constant_status": "new", "changes_detected": [], "_id": "6858f30f1195bbcbf7aa7691", "version": 2}, "validation_result": {"structure_errors": [], "required_field_errors": [], "data_type_errors": [], "custom_errors": []}, "dependency_errors": [], "uniqueness_result": {"is_unique": false, "status": "exists_in_mongo", "message": "Constant with constant_id 1003 or attribute constantId already exists in MongoDB drafts", "existing_document": {"_id": "6858f30f1195bbcbf7aa7691", "constant_id": 1003, "attribute": "MAX_LEAVE_DAYS_ANNUAL", "value": "25", "description": "Maximum number of annual leave days per year", "tenant_id": "T1007", "allow_override": false, "override_permissions": "", "status": "draft", "entity_name": "", "attribute_name": "", "created_at": "2025-06-23T06:24:15.926637", "updated_at": "2025-06-23T06:24:15.926647", "created_by": "Tarun", "updated_by": "Tarun", "constant_status": "new", "changes_detected": [], "version": 1}}}, {"success": false, "errors": ["value is required"], "parsed_data": {"constant_id": 1003, "attribute": "attribute", "value": "", "description": "Name of the configuration attribute", "tenant_id": "T1001", "allow_override": false, "override_permissions": "", "status": "active", "entity_name": "", "attribute_name": "", "created_at": "2025-06-23T09:02:49.386755", "updated_at": "2025-06-23T09:02:49.386755", "created_by": "Tarun", "updated_by": "Tarun", "constant_status": "new", "changes_detected": []}}, {"success": false, "errors": ["value is required"], "parsed_data": {"constant_id": 1003, "attribute": "value", "value": "", "description": "Value of the configuration attribute", "tenant_id": "T1001", "allow_override": false, "override_permissions": "", "status": "active", "entity_name": "", "attribute_name": "", "created_at": "2025-06-23T09:02:49.403724", "updated_at": "2025-06-23T09:02:49.403724", "created_by": "Tarun", "updated_by": "Tarun", "constant_status": "new", "changes_detected": []}}, {"success": false, "errors": ["value is required"], "parsed_data": {"constant_id": 1003, "attribute": "description", "value": "", "description": "Description of the configuration attribute", "tenant_id": "T1001", "allow_override": false, "override_permissions": "", "status": "active", "entity_name": "", "attribute_name": "", "created_at": "2025-06-23T09:02:49.418652", "updated_at": "2025-06-23T09:02:49.418652", "created_by": "Tarun", "updated_by": "Tarun", "constant_status": "new", "changes_detected": []}}, {"success": true, "saved_data": {"constant_id": 1003, "attribute": "dataType", "value": "String", "description": "Data type of the configuration value", "tenant_id": "T1001", "allow_override": false, "override_permissions": "", "status": "active", "entity_name": "", "attribute_name": "", "created_at": "2025-06-23T09:02:49.436245", "updated_at": "2025-06-23T09:02:49.645781", "created_by": "Tarun", "updated_by": "Tarun", "constant_status": "new", "changes_detected": [], "_id": "6858f30f1195bbcbf7aa7691", "version": 3}, "validation_result": {"structure_errors": [], "required_field_errors": [], "data_type_errors": [], "custom_errors": []}, "dependency_errors": [], "uniqueness_result": {"is_unique": false, "status": "exists_in_mongo", "message": "Constant with constant_id 1003 or attribute dataType already exists in MongoDB drafts", "existing_document": {"_id": "6858f30f1195bbcbf7aa7691", "constant_id": 1003, "attribute": "constantId", "value": "CONST-{sequence}", "description": "Unique identifier for the constant", "tenant_id": "T1001", "allow_override": false, "override_permissions": "", "status": "active", "entity_name": "", "attribute_name": "", "created_at": "2025-06-23T09:02:49.367582", "updated_at": "2025-06-23T09:02:49.543345", "created_by": "Tarun", "updated_by": "Tarun", "constant_status": "new", "changes_detected": [], "version": 2}}}, {"success": true, "saved_data": {"constant_id": 1003, "attribute": "status", "value": "Active", "description": "Current status of the constant", "tenant_id": "T1001", "allow_override": false, "override_permissions": "", "status": "active", "entity_name": "", "attribute_name": "", "created_at": "2025-06-23T09:02:49.455200", "updated_at": "2025-06-23T09:02:49.670914", "created_by": "Tarun", "updated_by": "Tarun", "constant_status": "new", "changes_detected": [], "_id": "6858f30f1195bbcbf7aa7691", "version": 4}, "validation_result": {"structure_errors": [], "required_field_errors": [], "data_type_errors": [], "custom_errors": []}, "dependency_errors": [], "uniqueness_result": {"is_unique": false, "status": "exists_in_mongo", "message": "Constant with constant_id 1003 or attribute status already exists in MongoDB drafts", "existing_document": {"_id": "6858f30f1195bbcbf7aa7691", "constant_id": 1003, "attribute": "dataType", "value": "String", "description": "Data type of the configuration value", "tenant_id": "T1001", "allow_override": false, "override_permissions": "", "status": "active", "entity_name": "", "attribute_name": "", "created_at": "2025-06-23T09:02:49.436245", "updated_at": "2025-06-23T09:02:49.645781", "created_by": "Tarun", "updated_by": "Tarun", "constant_status": "new", "changes_detected": [], "version": 3}}}, {"success": true, "saved_data": {"constant_id": 1003, "attribute": "allow_override", "value": "false", "description": "Whether this constant can be overridden", "tenant_id": "T1001", "allow_override": false, "override_permissions": "", "status": "active", "entity_name": "", "attribute_name": "", "created_at": "2025-06-23T09:02:49.471376", "updated_at": "2025-06-23T09:02:49.695971", "created_by": "Tarun", "updated_by": "Tarun", "constant_status": "new", "changes_detected": [], "_id": "6858f30f1195bbcbf7aa7691", "version": 5}, "validation_result": {"structure_errors": [], "required_field_errors": [], "data_type_errors": [], "custom_errors": []}, "dependency_errors": [], "uniqueness_result": {"is_unique": false, "status": "exists_in_mongo", "message": "Constant with constant_id 1003 or attribute allow_override already exists in MongoDB drafts", "existing_document": {"_id": "6858f30f1195bbcbf7aa7691", "constant_id": 1003, "attribute": "status", "value": "Active", "description": "Current status of the constant", "tenant_id": "T1001", "allow_override": false, "override_permissions": "", "status": "active", "entity_name": "", "attribute_name": "", "created_at": "2025-06-23T09:02:49.455200", "updated_at": "2025-06-23T09:02:49.670914", "created_by": "Tarun", "updated_by": "Tarun", "constant_status": "new", "changes_detected": [], "version": 4}}}, {"success": false, "errors": ["value is required"], "parsed_data": {"constant_id": 1003, "attribute": "override_permissions", "value": "", "description": "Permissions required to override this constant", "tenant_id": "T1001", "allow_override": false, "override_permissions": "", "status": "active", "entity_name": "", "attribute_name": "", "created_at": "2025-06-23T09:02:49.486636", "updated_at": "2025-06-23T09:02:49.486636", "created_by": "Tarun", "updated_by": "Tarun", "constant_status": "new", "changes_detected": []}}, {"success": false, "errors": ["value is required"], "parsed_data": {"constant_id": 1003, "attribute": "entity_name", "value": "", "description": "Entity this constant is associated with", "tenant_id": "T1001", "allow_override": false, "override_permissions": "", "status": "active", "entity_name": "", "attribute_name": "", "created_at": "2025-06-23T09:02:49.502728", "updated_at": "2025-06-23T09:02:49.502728", "created_by": "Tarun", "updated_by": "Tarun", "constant_status": "new", "changes_detected": []}}, {"success": false, "errors": ["value is required"], "parsed_data": {"constant_id": 1003, "attribute": "attribute_name", "value": "", "description": "Specific attribute this constant relates to", "tenant_id": "T1001", "allow_override": false, "override_permissions": "", "status": "active", "entity_name": "", "attribute_name": "", "created_at": "2025-06-23T09:02:49.518675", "updated_at": "2025-06-23T09:02:49.518675", "created_by": "Tarun", "updated_by": "Tarun", "constant_status": "new", "changes_detected": []}}], "operation": "parse_validate_mongosave", "total_constants": 10}, "status": "success"}
{"timestamp": "2025-06-23T11:52:30.696632", "endpoint": "parse-validate-mongosave/constants", "input": {"natural_language": "Tenant: Acme Corp\n\nAttributes:\nAttribute Name | Display Name | Data Type | Required | Unique | Default Type | Default Value | Description | Helper Text\n\nconstantId | Constant ID | string | true | true | computation | CONST-{sequence} | Unique identifier for the constant | Auto-generated constant identifier\n\nattribute | Attribute Name | string | true | true | static value | | Name of the configuration attribute | Enter attribute name\n\nvalue | Value | string | true | false | static value | | Value of the configuration attribute | Enter attribute value\n\ndataType | Data Type | string | true | false | static value | string | Data type of the constant value | Select data type\n\ndescription | Description | string | false | false | static value | | Description of the constant | Enter description\n\nstatus | Status | string | true | false | static value | active | Status of the constant | Select status", "tenant_id": "T_ACME_CORP", "edit_draft": false, "edit_temp": false, "edit_production": false}, "output": {"success": true, "constant_results": [{"success": false, "errors": ["Tenant with tenant_id T_ACME_CORP does not exist"], "parsed_data": {"constant_id": 1003, "attribute": "constantId", "value": "CONST-{sequence}", "description": "Unique identifier for the constant", "tenant_id": "T_ACME_CORP", "allow_override": false, "override_permissions": "", "status": "active", "entity_name": "", "attribute_name": "", "created_at": "2025-06-23T11:52:30.494616", "updated_at": "2025-06-23T11:52:30.494616", "created_by": "Tarun", "updated_by": "Tarun", "constant_status": "new", "changes_detected": []}}, {"success": false, "errors": ["value is required", "Tenant with tenant_id T_ACME_CORP does not exist"], "parsed_data": {"constant_id": 1003, "attribute": "attribute", "value": "", "description": "Name of the configuration attribute", "tenant_id": "T_ACME_CORP", "allow_override": false, "override_permissions": "", "status": "active", "entity_name": "", "attribute_name": "", "created_at": "2025-06-23T11:52:30.509442", "updated_at": "2025-06-23T11:52:30.509442", "created_by": "Tarun", "updated_by": "Tarun", "constant_status": "new", "changes_detected": []}}, {"success": false, "errors": ["value is required", "Tenant with tenant_id T_ACME_CORP does not exist"], "parsed_data": {"constant_id": 1003, "attribute": "value", "value": "", "description": "Value of the configuration attribute", "tenant_id": "T_ACME_CORP", "allow_override": false, "override_permissions": "", "status": "active", "entity_name": "", "attribute_name": "", "created_at": "2025-06-23T11:52:30.523773", "updated_at": "2025-06-23T11:52:30.523773", "created_by": "Tarun", "updated_by": "Tarun", "constant_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Tenant with tenant_id T_ACME_CORP does not exist"], "parsed_data": {"constant_id": 1003, "attribute": "dataType", "value": "string", "description": "Data type of the constant value", "tenant_id": "T_ACME_CORP", "allow_override": false, "override_permissions": "", "status": "active", "entity_name": "", "attribute_name": "", "created_at": "2025-06-23T11:52:30.538788", "updated_at": "2025-06-23T11:52:30.538788", "created_by": "Tarun", "updated_by": "Tarun", "constant_status": "new", "changes_detected": []}}, {"success": false, "errors": ["value is required", "Tenant with tenant_id T_ACME_CORP does not exist"], "parsed_data": {"constant_id": 1003, "attribute": "description", "value": "", "description": "Description of the constant", "tenant_id": "T_ACME_CORP", "allow_override": false, "override_permissions": "", "status": "active", "entity_name": "", "attribute_name": "", "created_at": "2025-06-23T11:52:30.552616", "updated_at": "2025-06-23T11:52:30.552616", "created_by": "Tarun", "updated_by": "Tarun", "constant_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Tenant with tenant_id T_ACME_CORP does not exist"], "parsed_data": {"constant_id": 1003, "attribute": "status", "value": "active", "description": "Status of the constant", "tenant_id": "T_ACME_CORP", "allow_override": false, "override_permissions": "", "status": "active", "entity_name": "", "attribute_name": "", "created_at": "2025-06-23T11:52:30.565654", "updated_at": "2025-06-23T11:52:30.565654", "created_by": "Tarun", "updated_by": "Tarun", "constant_status": "new", "changes_detected": []}}], "operation": "parse_validate_mongosave", "total_constants": 6}, "status": "success"}
{"timestamp": "2025-06-23T14:07:59.644296", "endpoint": "parse-validate-mongosave/constants", "input": {"natural_language": "Define product categories: ELECTRONICS, CLOTHING, BOOKS, HOME", "tenant_id": "tenant_123", "edit_draft": false, "edit_temp": false, "edit_production": false}, "output": {"success": true, "constant_results": [], "operation": "parse_validate_mongosave", "total_constants": 0}, "status": "success"}
