"""
Authentication Service for v2 API

This module contains the business logic for user registration and authentication.
Simplified version without user_organizations dependency.
"""

import os
import uuid
import logging
import time
import jwt
from datetime import datetime, timed<PERSON><PERSON>
from typing import Optional

import bcrypt
from sqlalchemy.orm import Session
from sqlalchemy.sql import text

from .models import UserRegistrationRequest, UserRegistrationResponse, UserLoginRequest, TokenResponse, RefreshTokenRequest, LogoutRequest, SuccessResponse

# JWT Configuration
JWT_SECRET_KEY = os.getenv("JWT_SECRET_KEY", "your-secret-key-change-in-production")
JWT_ALGORITHM = "HS256"
ACCESS_TOKEN_EXPIRE_MINUTES = 30
REFRESH_TOKEN_EXPIRE_DAYS = 7


# Configure logging
logger = logging.getLogger(__name__)


class UserRegistrationService:
    """Service class for user registration operations"""
    
    def __init__(self, db_session: Session):
        """
        Initialize the user registration service.
        
        Args:
            db_session: SQLAlchemy database session
        """
        self.db = db_session
        self.logger = logger
    
    def _get_password_hash(self, password: str) -> str:
        """
        Generate a password hash.
        
        Args:
            password: Plain text password
            
        Returns:
            str: Hashed password
        """
        salt = bcrypt.gensalt()
        return bcrypt.hashpw(password.encode('utf-8'), salt).decode('utf-8')
    
    def _check_user_exists(self, username: str, email: str) -> bool:
        """
        Check if a user with the given username or email already exists.
        
        Args:
            username: Username to check
            email: Email to check
            
        Returns:
            bool: True if user exists, False otherwise
        """
        try:
            query = """
            SELECT user_id FROM workflow_runtime.users
            WHERE username = :username OR email = :email
            """
            
            result = self.db.execute(text(query), {
                "username": username,
                "email": email
            }).fetchone()
            
            return result is not None
            
        except Exception as e:
            self.logger.error(f"Error checking user existence: {str(e)}")
            return True  # Assume exists to be safe
    
    def _get_default_tenant(self) -> Optional[str]:
        """
        Get the default tenant ID.
        
        Returns:
            Optional[str]: Default tenant ID or None
        """
        try:
            query = "SELECT tenant_id FROM workflow_runtime.tenants LIMIT 1"
            result = self.db.execute(text(query)).fetchone()
            return result[0] if result else None
        except Exception as e:
            self.logger.error(f"Error getting default tenant: {str(e)}")
            return None
    
    def _generate_next_user_id(self) -> str:
        """
        Generate the next sequential user ID following the pattern U1, U2, U3, etc.
        
        Returns:
            str: Next user ID in sequence
        """
        try:
            query = """
            SELECT user_id FROM workflow_runtime.users 
            WHERE user_id ~ '^U[0-9]+$' 
            ORDER BY CAST(SUBSTRING(user_id FROM 2) AS INTEGER) DESC 
            LIMIT 1
            """
            
            result = self.db.execute(text(query)).fetchone()
            
            if result:
                # Extract the number from the last user ID (e.g., "U4" -> 4)
                last_number = int(result[0][1:])
                next_number = last_number + 1
            else:
                # No existing users with this pattern, start with 1
                next_number = 1
            
            return f"U{next_number}"
            
        except Exception as e:
            self.logger.error(f"Error generating next user ID: {str(e)}")
            # Fallback to UUID if there's an error
            return str(uuid.uuid4())
    
    def _create_user_record(self, user_data: UserRegistrationRequest, user_id: str, tenant_id: str) -> bool:
        """
        Create the main user record.
        
        Args:
            user_data: User registration data
            user_id: Generated user ID
            tenant_id: Tenant ID
            
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            password_hash = self._get_password_hash(user_data.password)
            
            query = """
            INSERT INTO workflow_runtime.users
            (user_id, username, email, password_hash, first_name, last_name, status, created_at, updated_at)
            VALUES (:user_id, :username, :email, :password_hash, :first_name, :last_name, 'active', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
            """
            
            self.db.execute(text(query), {
                "user_id": user_id,
                "username": user_data.username,
                "email": user_data.email,
                "password_hash": password_hash,
                "first_name": user_data.first_name,
                "last_name": user_data.last_name
            })
            
            return True
            
        except Exception as e:
            self.logger.error(f"Error creating user record: {str(e)}")
            return False
    
    def _assign_user_roles(self, user_id: str, username: str, roles: list, tenant_id: str) -> bool:
        """
        Assign roles to the user.
        
        Args:
            user_id: User ID
            username: Username
            roles: List of roles to assign
            tenant_id: Tenant ID
            
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            for role in roles:
                query = """
                INSERT INTO workflow_runtime.user_roles
                (user_id, username, role, tenant_id)
                VALUES (:user_id, :username, :role, :tenant_id)
                """
                
                self.db.execute(text(query), {
                    "user_id": user_id,
                    "username": username,
                    "role": role,
                    "tenant_id": tenant_id
                })
            
            return True
            
        except Exception as e:
            self.logger.error(f"Error assigning user roles: {str(e)}")
            return False
    
    def register_user(self, user_data: UserRegistrationRequest) -> Optional[UserRegistrationResponse]:
        """
        Register a new user.
        
        Args:
            user_data: User registration data
            
        Returns:
            Optional[UserRegistrationResponse]: Created user data if successful, None otherwise
        """
        try:
            # Check if user already exists
            if self._check_user_exists(user_data.username, user_data.email):
                self.logger.warning(f"User registration failed: username '{user_data.username}' or email '{user_data.email}' already exists")
                return None
            
            # Get tenant ID
            tenant_id = user_data.tenant_id
            if not tenant_id:
                tenant_id = self._get_default_tenant()
                if not tenant_id:
                    self.logger.error("No tenant available for user registration")
                    return None
            
            # Generate user ID
            user_id = self._generate_next_user_id()
            
            # Create user record
            if not self._create_user_record(user_data, user_id, tenant_id):
                self.db.rollback()
                return None
            
            # Assign roles
            if not self._assign_user_roles(user_id, user_data.username, user_data.roles, tenant_id):
                self.db.rollback()
                return None
            
            # Commit transaction
            self.db.commit()
            
            # Return created user data
            return UserRegistrationResponse(
                user_id=user_id,
                username=user_data.username,
                email=user_data.email,
                first_name=user_data.first_name,
                last_name=user_data.last_name,
                status="active",
                roles=user_data.roles,
                tenant_id=tenant_id,
                disabled=False,
                created_at=datetime.utcnow()
            )
            
        except Exception as e:
            self.logger.error(f"Error during user registration: {str(e)}")
            self.db.rollback()
            return None
    
    def get_user_by_id(self, user_id: str) -> Optional[UserRegistrationResponse]:
        """
        Get user by ID.
        
        Args:
            user_id: User ID
            
        Returns:
            Optional[UserRegistrationResponse]: User data if found, None otherwise
        """
        try:
            query = """
            SELECT 
                u.user_id, u.username, u.email, u.first_name, u.last_name, 
                u.status, u.created_at,
                array_agg(DISTINCT ur.role) as roles,
                ur.tenant_id
            FROM workflow_runtime.users u
            LEFT JOIN workflow_runtime.user_roles ur ON u.user_id = ur.user_id
            WHERE u.user_id = :user_id AND u.status = 'active'
            GROUP BY u.user_id, u.username, u.email, u.first_name, u.last_name, 
                    u.status, u.created_at, ur.tenant_id
            """
            
            result = self.db.execute(text(query), {"user_id": user_id}).fetchone()
            
            if not result:
                return None
            
            return UserRegistrationResponse(
                user_id=result.user_id,
                username=result.username,
                email=result.email,
                first_name=result.first_name,
                last_name=result.last_name,
                status=result.status,
                roles=result.roles if result.roles and result.roles[0] is not None else [],
                tenant_id=result.tenant_id,
                disabled=result.status != "active",
                created_at=result.created_at
            )
            
        except Exception as e:
            self.logger.error(f"Error getting user by ID: {str(e)}")
            return None


class UserAuthenticationService:
    """Service class for user authentication operations"""
    
    def __init__(self, db_session: Session):
        """
        Initialize the authentication service.
        
        Args:
            db_session: SQLAlchemy database session
        """
        self.db = db_session
        self.logger = logger
    
    def _verify_password(self, plain_password: str, hashed_password: str) -> bool:
        """
        Verify a password against a hash.
        
        Args:
            plain_password: Plain text password
            hashed_password: Hashed password
            
        Returns:
            bool: True if password matches hash
        """
        return bcrypt.checkpw(
            plain_password.encode('utf-8'), 
            hashed_password.encode('utf-8')
        )
    
    def _get_user_by_username_or_email(self, username: str) -> Optional[dict]:
        """
        Get user by username or email.
        
        Args:
            username: Username or email to look up
            
        Returns:
            Optional[dict]: User data if found, None otherwise
        """
        try:
            query = """
            SELECT 
                u.user_id, u.username, u.email, u.password_hash, 
                u.first_name, u.last_name, u.status, u.created_at,
                array_agg(DISTINCT ur.role) as roles,
                ur.tenant_id
            FROM workflow_runtime.users u
            LEFT JOIN workflow_runtime.user_roles ur ON u.user_id = ur.user_id
            WHERE (u.username = :username OR u.email = :username) AND u.status = 'active'
            GROUP BY u.user_id, u.username, u.email, u.password_hash, 
                    u.first_name, u.last_name, u.status, u.created_at, ur.tenant_id
            """
            
            result = self.db.execute(text(query), {"username": username}).fetchone()
            
            if not result:
                return None
                
            return {
                "user_id": result.user_id,
                "username": result.username,
                "email": result.email,
                "password_hash": result.password_hash,
                "first_name": result.first_name,
                "last_name": result.last_name,
                "status": result.status,
                "roles": result.roles if result.roles and result.roles[0] is not None else [],
                "tenant_id": result.tenant_id,
                "created_at": result.created_at
            }
            
        except Exception as e:
            self.logger.error(f"Error getting user by username/email: {str(e)}")
            return None
    
    def _create_access_token(self, data: dict, expires_delta: Optional[timedelta] = None) -> str:
        """
        Create a JWT access token.
        
        Args:
            data: Data to encode in the token
            expires_delta: Token expiration time
            
        Returns:
            str: JWT token
        """
        to_encode = data.copy()
        
        if expires_delta:
            expire = datetime.utcnow() + expires_delta
        else:
            expire = datetime.utcnow() + timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
            
        to_encode.update({"exp": expire})
        encoded_jwt = jwt.encode(to_encode, JWT_SECRET_KEY, algorithm=JWT_ALGORITHM)
        
        return encoded_jwt
    
    def _create_refresh_token(self, data: dict) -> str:
        """
        Create a JWT refresh token.
        
        Args:
            data: Data to encode in the token
            
        Returns:
            str: JWT refresh token
        """
        to_encode = data.copy()
        expire = datetime.utcnow() + timedelta(days=REFRESH_TOKEN_EXPIRE_DAYS)
        to_encode.update({"exp": expire})
        encoded_jwt = jwt.encode(to_encode, JWT_SECRET_KEY, algorithm=JWT_ALGORITHM)
        
        return encoded_jwt
    
    def _store_tokens(self, user_id: str, access_token: str, refresh_token: str) -> bool:
        """
        Store tokens in database.
        
        Args:
            user_id: User ID
            access_token: Access token
            refresh_token: Refresh token
            
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            token_id = str(uuid.uuid4())
            expires_at = datetime.utcnow() + timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
            
            query = """
            INSERT INTO workflow_runtime.user_oauth_tokens
            (token_id, user_id, access_token, refresh_token, expires_at)
            VALUES (:token_id, :user_id, :access_token, :refresh_token, :expires_at)
            """
            
            self.db.execute(text(query), {
                "token_id": token_id,
                "user_id": user_id,
                "access_token": access_token,
                "refresh_token": refresh_token,
                "expires_at": expires_at
            })
            
            self.db.commit()
            return True
            
        except Exception as e:
            self.logger.error(f"Error storing tokens: {str(e)}")
            return False
    
    def _update_last_login(self, user_id: str) -> None:
        """
        Update user's last login timestamp.
        
        Args:
            user_id: User ID
        """
        try:
            query = """
            UPDATE workflow_runtime.users
            SET last_login = CURRENT_TIMESTAMP
            WHERE user_id = :user_id
            """
            self.db.execute(text(query), {"user_id": user_id})
            self.db.commit()
        except Exception as e:
            self.logger.error(f"Error updating last login: {str(e)}")
    
    def authenticate_user(self, login_data: UserLoginRequest) -> Optional[TokenResponse]:
        """
        Authenticate user and return tokens.
        
        Args:
            login_data: Login credentials
            
        Returns:
            Optional[TokenResponse]: Token response if successful, None otherwise
        """
        try:
            # Get user by username or email
            user_data = self._get_user_by_username_or_email(login_data.username)
            
            if not user_data:
                self.logger.warning(f"Authentication failed: user '{login_data.username}' not found")
                return None
            
            # Verify password
            if not self._verify_password(login_data.password, user_data["password_hash"]):
                self.logger.warning(f"Authentication failed: invalid password for user '{login_data.username}'")
                return None
            
            # Create token data
            token_data = {
                "sub": user_data["user_id"],
                "username": user_data["username"],
                "roles": user_data["roles"],
                "tenant_id": user_data["tenant_id"]
            }
            
            # Create tokens
            access_token_expires = timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
            access_token = self._create_access_token(
                data=token_data, 
                expires_delta=access_token_expires
            )
            
            refresh_token = self._create_refresh_token(data={"sub": user_data["user_id"]})
            
            # Store tokens in database
            if not self._store_tokens(user_data["user_id"], access_token, refresh_token):
                self.logger.error("Failed to store tokens")
                return None
            
            # Update last login
            self._update_last_login(user_data["user_id"])
            
            # Create user response
            user_response = UserRegistrationResponse(
                user_id=user_data["user_id"],
                username=user_data["username"],
                email=user_data["email"],
                first_name=user_data["first_name"],
                last_name=user_data["last_name"],
                status=user_data["status"],
                roles=user_data["roles"],
                tenant_id=user_data["tenant_id"],
                disabled=user_data["status"] != "active",
                created_at=user_data["created_at"]
            )
            
            # Return token response
            return TokenResponse(
                access_token=access_token,
                refresh_token=refresh_token,
                token_type="bearer",
                expires_at=int(time.time() + ACCESS_TOKEN_EXPIRE_MINUTES * 60),
                user=user_response
            )
            
        except Exception as e:
            self.logger.error(f"Error during authentication: {str(e)}")
            return None
    
    def _decode_token(self, token: str) -> Optional[dict]:
        """
        Decode a JWT token.
        
        Args:
            token: JWT token
            
        Returns:
            Optional[dict]: Decoded token data if valid, None otherwise
        """
        try:
            payload = jwt.decode(token, JWT_SECRET_KEY, algorithms=[JWT_ALGORITHM])
            return payload
        except jwt.ExpiredSignatureError:
            self.logger.warning("Token has expired")
            return None
        except jwt.InvalidTokenError:
            self.logger.warning("Invalid token")
            return None
    
    def _revoke_token(self, token: str) -> bool:
        """
        Revoke a token by removing it from the database.
        
        Args:
            token: Token to revoke
            
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            query = """
            DELETE FROM workflow_runtime.user_oauth_tokens
            WHERE access_token = :token OR refresh_token = :token
            """
            
            result = self.db.execute(text(query), {"token": token})
            self.db.commit()
            
            return result.rowcount > 0
            
        except Exception as e:
            self.logger.error(f"Error revoking token: {str(e)}")
            return False
    
    def _revoke_all_user_tokens(self, user_id: str) -> bool:
        """
        Revoke all tokens for a user.
        
        Args:
            user_id: User ID
            
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            query = """
            DELETE FROM workflow_runtime.user_oauth_tokens
            WHERE user_id = :user_id
            """
            
            result = self.db.execute(text(query), {"user_id": user_id})
            self.db.commit()
            
            return result.rowcount >= 0  # Even 0 is success (no tokens to revoke)
            
        except Exception as e:
            self.logger.error(f"Error revoking all user tokens: {str(e)}")
            return False
    
    def refresh_access_token(self, refresh_request: RefreshTokenRequest) -> Optional[TokenResponse]:
        """
        Refresh access token using refresh token.
        
        Args:
            refresh_request: Refresh token request
            
        Returns:
            Optional[TokenResponse]: New token response if successful, None otherwise
        """
        try:
            # Decode refresh token
            payload = self._decode_token(refresh_request.refresh_token)
            
            if not payload:
                return None
                
            user_id = payload.get("sub")
            if not user_id:
                return None
            
            # Check if refresh token exists in database
            query = """
            SELECT user_id FROM workflow_runtime.user_oauth_tokens
            WHERE refresh_token = :refresh_token AND user_id = :user_id
            """
            
            result = self.db.execute(text(query), {
                "refresh_token": refresh_request.refresh_token,
                "user_id": user_id
            }).fetchone()
            
            if not result:
                self.logger.warning(f"Refresh token not found for user {user_id}")
                return None
            
            # Get user data
            user_data = self._get_user_by_username_or_email(user_id)  # This won't work, need to get by ID
            
            # Let's get user by ID instead
            query = """
            SELECT 
                u.user_id, u.username, u.email, u.first_name, u.last_name, 
                u.status, u.created_at,
                array_agg(DISTINCT ur.role) as roles,
                ur.tenant_id
            FROM workflow_runtime.users u
            LEFT JOIN workflow_runtime.user_roles ur ON u.user_id = ur.user_id
            WHERE u.user_id = :user_id AND u.status = 'active'
            GROUP BY u.user_id, u.username, u.email, u.first_name, u.last_name, 
                    u.status, u.created_at, ur.tenant_id
            """
            
            result = self.db.execute(text(query), {"user_id": user_id}).fetchone()
            
            if not result:
                self.logger.warning(f"User {user_id} not found or inactive")
                return None
            
            user_data = {
                "user_id": result.user_id,
                "username": result.username,
                "email": result.email,
                "first_name": result.first_name,
                "last_name": result.last_name,
                "status": result.status,
                "roles": result.roles if result.roles and result.roles[0] is not None else [],
                "tenant_id": result.tenant_id,
                "created_at": result.created_at
            }
            
            # Create new token data
            token_data = {
                "sub": user_data["user_id"],
                "username": user_data["username"],
                "roles": user_data["roles"],
                "tenant_id": user_data["tenant_id"]
            }
            
            # Create new tokens
            access_token_expires = timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
            access_token = self._create_access_token(
                data=token_data, 
                expires_delta=access_token_expires
            )
            
            new_refresh_token = self._create_refresh_token(data={"sub": user_data["user_id"]})
            
            # Revoke old tokens
            self._revoke_token(refresh_request.refresh_token)
            
            # Store new tokens
            if not self._store_tokens(user_data["user_id"], access_token, new_refresh_token):
                self.logger.error("Failed to store new tokens")
                return None
            
            # Create user response
            user_response = UserRegistrationResponse(
                user_id=user_data["user_id"],
                username=user_data["username"],
                email=user_data["email"],
                first_name=user_data["first_name"],
                last_name=user_data["last_name"],
                status=user_data["status"],
                roles=user_data["roles"],
                tenant_id=user_data["tenant_id"],
                disabled=user_data["status"] != "active",
                created_at=user_data["created_at"]
            )
            
            # Return new token response
            return TokenResponse(
                access_token=access_token,
                refresh_token=new_refresh_token,
                token_type="bearer",
                expires_at=int(time.time() + ACCESS_TOKEN_EXPIRE_MINUTES * 60),
                user=user_response
            )
            
        except Exception as e:
            self.logger.error(f"Error refreshing token: {str(e)}")
            return None
    
    def logout_user(self, logout_request: LogoutRequest) -> SuccessResponse:
        """
        Logout user by revoking tokens.
        
        Args:
            logout_request: Logout request with optional refresh token
            
        Returns:
            SuccessResponse: Success response
        """
        try:
            if logout_request.refresh_token:
                # Revoke specific token
                self._revoke_token(logout_request.refresh_token)
                message = "Successfully logged out"
            else:
                # This would require getting user ID from access token
                # For now, just return success
                message = "Successfully logged out"
            
            return SuccessResponse(
                success=True,
                message=message,
                data={}
            )
            
        except Exception as e:
            self.logger.error(f"Error during logout: {str(e)}")
            return SuccessResponse(
                success=True,  # Still return success to avoid exposing errors
                message="Successfully logged out",
                data={}
            )
    
    def logout_all_sessions(self, user_id: str) -> SuccessResponse:
        """
        Logout user from all sessions by revoking all tokens.
        
        Args:
            user_id: User ID
            
        Returns:
            SuccessResponse: Success response
        """
        try:
            self._revoke_all_user_tokens(user_id)
            
            return SuccessResponse(
                success=True,
                message="Successfully logged out from all sessions",
                data={}
            )
            
        except Exception as e:
            self.logger.error(f"Error during logout all: {str(e)}")
            return SuccessResponse(
                success=True,  # Still return success to avoid exposing errors
                message="Successfully logged out from all sessions",
                data={}
            )
