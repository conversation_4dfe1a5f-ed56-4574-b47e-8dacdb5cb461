import unittest
from typing import List, Union
import sys
import os

# Add project root to path if needed
project_root = '/home/<USER>/workflow-system/runtime/workflow-engine'
if project_root not in sys.path:
    sys.path.insert(0, project_root)

# Import the function to test
from app.services.system_functions import min_max

class TestMinMax(unittest.TestCase):
    def test_min_operation_with_integers(self):
        """Test min operation with a list of integers."""
        values = [5, 3, 7, 1, 9]
        result = min_max(values, "min")
        self.assertEqual(result, 1)
    
    def test_max_operation_with_integers(self):
        """Test max operation with a list of integers."""
        values = [5, 3, 7, 1, 9]
        result = min_max(values, "max")
        self.assertEqual(result, 9)
    
    def test_default_operation(self):
        """Test with default operation (should be 'min')."""
        values = [5, 3, 7, 1, 9]
        result = min_max(values)
        self.assertEqual(result, 1)
    
    def test_with_floats(self):
        """Test with a list of floating point numbers."""
        values = [5.5, 3.3, 7.7, 1.1, 9.9]
        
        # Test min
        result_min = min_max(values, "min")
        self.assertEqual(result_min, 1.1)
        
        # Test max
        result_max = min_max(values, "max")
        self.assertEqual(result_max, 9.9)
    
    def test_with_mixed_types(self):
        """Test with a list containing both integers and floats."""
        values = [5, 3.3, 7, 1.1, 9]
        
        # Test min
        result_min = min_max(values, "min")
        self.assertEqual(result_min, 1.1)
        
        # Test max
        result_max = min_max(values, "max")
        self.assertEqual(result_max, 9)
    
    def test_with_string_numbers(self):
        """Test with a list containing string representations of numbers."""
        values = ["5", "3.3", "7", "1.1", "9"]
        
        # Test min
        result_min = min_max(values, "min")
        self.assertEqual(result_min, 1.1)
        
        # Test max
        result_max = min_max(values, "max")
        self.assertEqual(result_max, 9.0)  # Note: All values convert to float when strings are involved
    
    def test_with_mixed_strings_and_numbers(self):
        """Test with a list containing both actual numbers and string representations."""
        values = [5, "3.3", 7, "1.1", 9]
        
        # Test min
        result_min = min_max(values, "min")
        self.assertEqual(result_min, 1.1)
        
        # Test max
        result_max = min_max(values, "max")
        self.assertEqual(result_max, 9)
    
    def test_case_insensitive_operation(self):
        """Test that operation parameter is case-insensitive."""
        values = [5, 3, 7, 1, 9]
        
        # Test uppercase MIN
        result_min = min_max(values, "MIN")
        self.assertEqual(result_min, 1)
        
        # Test mixed case MaX
        result_max = min_max(values, "MaX")
        self.assertEqual(result_max, 9)
    
    def test_negative_numbers(self):
        """Test with negative numbers."""
        values = [-5, -3, -7, -1, -9]
        
        # Test min
        result_min = min_max(values, "min")
        self.assertEqual(result_min, -9)
        
        # Test max
        result_max = min_max(values, "max")
        self.assertEqual(result_max, -1)
    
    def test_single_value_list(self):
        """Test with a list containing only one value."""
        values = [42]
        
        # Test min
        result_min = min_max(values, "min")
        self.assertEqual(result_min, 42)
        
        # Test max
        result_max = min_max(values, "max")
        self.assertEqual(result_max, 42)
    
    def test_empty_list(self):
        """Test with an empty list, should raise ValueError."""
        values = []
        
        with self.assertRaises(ValueError) as context:
            min_max(values)
        
        self.assertEqual(str(context.exception), "Cannot find min/max of empty list")
    
    def test_invalid_operation(self):
        """Test with an invalid operation, should raise ValueError."""
        values = [5, 3, 7, 1, 9]
        
        with self.assertRaises(ValueError) as context:
            min_max(values, "average")  # 'average' is not a valid operation
        
        self.assertEqual(str(context.exception), "Invalid operation: average. Use 'min' or 'max'.")
    
    def test_non_numeric_string(self):
        """Test with a non-numeric string, should raise ValueError."""
        values = [5, 3, "seven", 1, 9]
        
        with self.assertRaises(ValueError) as context:
            min_max(values)
        
        self.assertEqual(str(context.exception), "Non-numeric value in list: seven")
    
    def test_with_very_large_numbers(self):
        """Test with very large numbers."""
        values = [1e10, 2e10, 3e10]
        
        # Test min
        result_min = min_max(values, "min")
        self.assertEqual(result_min, 1e10)
        
        # Test max
        result_max = min_max(values, "max")
        self.assertEqual(result_max, 3e10)
    
    def test_with_very_small_numbers(self):
        """Test with very small numbers."""
        values = [1e-10, 2e-10, 3e-10]
        
        # Test min
        result_min = min_max(values, "min")
        self.assertEqual(result_min, 1e-10)
        
        # Test max
        result_max = min_max(values, "max")
        self.assertEqual(result_max, 3e-10)

if __name__ == '__main__':
    unittest.main()