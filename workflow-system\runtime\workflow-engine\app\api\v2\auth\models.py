"""
Authentication Models for v2 API

This module contains Pydantic models for authentication endpoints.
"""

from typing import List, Optional
from pydantic import BaseModel, EmailStr, Field
from datetime import datetime


class UserRegistrationRequest(BaseModel):
    """User registration request model"""
    username: str = Field(..., min_length=3, max_length=50, description="Username")
    email: EmailStr = Field(..., description="Email address")
    password: str = Field(..., min_length=8, description="Password")
    first_name: Optional[str] = Field(None, max_length=100, description="First name")
    last_name: Optional[str] = Field(None, max_length=100, description="Last name")
    roles: List[str] = Field(default=["User"], description="User roles")
    tenant_id: Optional[str] = Field(None, description="Tenant ID")

    class Config:
        schema_extra = {
            "example": {
                "username": "johndo<PERSON>",
                "email": "<EMAIL>",
                "password": "secure123",
                "first_name": "<PERSON>",
                "last_name": "<PERSON><PERSON>",
                "roles": ["Administrator"],
                "tenant_id": "t001"
            }
        }


class UserRegistrationResponse(BaseModel):
    """User registration response model"""
    user_id: str = Field(..., description="Unique user identifier")
    username: str = Field(..., description="Username")
    email: EmailStr = Field(..., description="Email address")
    first_name: Optional[str] = Field(None, description="First name")
    last_name: Optional[str] = Field(None, description="Last name")
    status: str = Field(default="active", description="User status")
    roles: List[str] = Field(default=[], description="User roles")
    tenant_id: Optional[str] = Field(None, description="Tenant ID")
    disabled: bool = Field(default=False, description="Whether user is disabled")
    created_at: Optional[datetime] = Field(None, description="Creation timestamp")

    class Config:
        schema_extra = {
            "example": {
                "user_id": "0be32e4c-a2d6-40f5-8958-f312ddf7dd45",
                "username": "johndoe",
                "email": "<EMAIL>",
                "first_name": "John",
                "last_name": "Doe",
                "status": "active",
                "roles": ["User"],
                "tenant_id": "t001",
                "disabled": False
            }
        }


class ErrorResponse(BaseModel):
    """Error response model"""
    error: str = Field(..., description="Error type")
    message: str = Field(..., description="Error message")
    details: Optional[dict] = Field(None, description="Additional error details")

    class Config:
        schema_extra = {
            "example": {
                "error": "VALIDATION_ERROR",
                "message": "User with this username or email already exists",
                "details": {
                    "field": "username",
                    "code": "DUPLICATE_VALUE"
                }
            }
        }


class UserLoginRequest(BaseModel):
    """User login request model"""
    username: str = Field(..., description="Username or email")
    password: str = Field(..., description="Password")

    class Config:
        schema_extra = {
            "example": {
                "username": "testuser_v2_fixed",
                "password": "secure789"
            }
        }


class TokenResponse(BaseModel):
    """Token response model"""
    access_token: str = Field(..., description="JWT access token")
    refresh_token: str = Field(..., description="JWT refresh token")
    token_type: str = Field(default="bearer", description="Token type")
    expires_at: int = Field(..., description="Token expiration timestamp")
    user: UserRegistrationResponse = Field(..., description="User information")

    class Config:
        schema_extra = {
            "example": {
                "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
                "refresh_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
                "token_type": "bearer",
                "expires_at": 1716563118,
                "user": {
                    "user_id": "U5",
                    "username": "testuser_v2_fixed",
                    "email": "<EMAIL>",
                    "first_name": "Test",
                    "last_name": "User",
                    "status": "active",
                    "roles": ["User"],
                    "tenant_id": "t001",
                    "disabled": False
                }
            }
        }


class RefreshTokenRequest(BaseModel):
    """Refresh token request model"""
    refresh_token: str = Field(..., description="Refresh token")

    class Config:
        schema_extra = {
            "example": {
                "refresh_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
            }
        }


class LogoutRequest(BaseModel):
    """Logout request model"""
    refresh_token: Optional[str] = Field(None, description="Refresh token to revoke")

    class Config:
        schema_extra = {
            "example": {
                "refresh_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
            }
        }


class SuccessResponse(BaseModel):
    """Generic success response model"""
    success: bool = Field(True, description="Success indicator")
    message: str = Field(..., description="Success message")
    data: Optional[dict] = Field(None, description="Response data")

    class Config:
        schema_extra = {
            "example": {
                "success": True,
                "message": "User registered successfully",
                "data": {}
            }
        }
