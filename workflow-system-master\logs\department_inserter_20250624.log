{"timestamp": "2025-06-24T09:54:52.551378", "operation": "insert_department_to_workflow_runtime", "input_data": {"_id": "685438b777ff47ee3c5f402a", "id": 2, "tenant_id": "T1001", "name": "Marketing", "description": "Marketing and communications department", "department_head_role_id": null, "parent_department_id": null, "natural_language": "Department Name: Marketing\nDescription: Marketing and communications department\nTenant: T1001", "created_at": "2025-06-19T16:20:07.342096", "created_by": null, "updated_at": "2025-06-19T16:20:07.342106", "updated_by": null, "version": 1, "status": "draft"}, "result": {"success": true, "inserted_id": 2, "schema": "workflow_runtime", "tenant_id": "T1001", "name": "Marketing"}, "status": "success"}
{"timestamp": "2025-06-24T09:54:52.569066", "operation": "insert_department_to_workflow_runtime", "input_data": {"_id": "685a753d0ab469c89675f241", "id": 2, "tenant_id": "t999", "name": "Sales", "description": "Revenue generation through customer acquisition and relationship management", "department_head_role_id": null, "parent_department_id": null, "natural_language": "Department Name: Sales\nDescription: Revenue generation through customer acquisition and relationship management\nDepartment Head Role ID: 1\nParent Department: [None]", "created_at": "2025-06-24T09:51:57.351955", "created_by": null, "updated_at": "2025-06-24T09:51:57.351965", "updated_by": null, "version": 1, "status": "draft"}, "result": {"success": true, "inserted_id": 3, "schema": "workflow_runtime", "tenant_id": "t999", "name": "Sales"}, "status": "success"}
{"timestamp": "2025-06-24T09:54:52.571276", "operation": "process_mongo_departments_to_workflow_runtime", "input_data": {"schema": "workflow_runtime"}, "result": {"total_processed": 2, "successful_inserts": 2, "failed_inserts": 0, "details": [{"department_name": "Marketing", "tenant_id": "T1001", "status": "success", "details": {"success": true, "inserted_id": 2, "schema": "workflow_runtime", "tenant_id": "T1001", "name": "Marketing"}}, {"department_name": "Sales", "tenant_id": "t999", "status": "success", "details": {"success": true, "inserted_id": 3, "schema": "workflow_runtime", "tenant_id": "t999", "name": "Sales"}}]}, "status": "success"}
