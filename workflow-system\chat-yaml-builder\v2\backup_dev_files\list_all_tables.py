"""
List all tables in all schemas.
"""

import sys
from db_utils import execute_query

def main():
    # Get schema name from command line arguments
    schema_name = "workflow_temp"
    if len(sys.argv) > 1:
        schema_name = sys.argv[1]
    
    # List all schemas
    print(f"Listing all schemas related to {schema_name}")
    success, messages, schemas = execute_query(
        """
        SELECT schema_name 
        FROM information_schema.schemata 
        WHERE schema_name LIKE %s OR schema_name LIKE 'e%%'
        ORDER BY schema_name
        """,
        (f"{schema_name}%",)
    )
    
    if not success or not schemas:
        print("Failed to list schemas")
        if messages:
            print("Messages:")
            for message in messages:
                print(f"  {message}")
        return
    
    print("Schemas:")
    for schema in schemas:
        schema_name = schema[0]
        print(f"  {schema_name}")
        
        # List tables in schema
        success, messages, tables = execute_query(
            """
            SELECT table_name 
            FROM information_schema.tables 
            WHERE table_schema = %s
            ORDER BY table_name
            """,
            (schema_name,)
        )
        
        if success and tables:
            print(f"    Tables in {schema_name} schema:")
            for table in tables:
                print(f"      {table[0]}")
                
                # List columns in table
                success, messages, columns = execute_query(
                    """
                    SELECT column_name, data_type, is_nullable
                    FROM information_schema.columns 
                    WHERE table_schema = %s AND table_name = %s
                    ORDER BY ordinal_position
                    """,
                    (schema_name, table[0])
                )
                
                if success and columns:
                    print(f"        Columns:")
                    for column in columns:
                        nullable = "NULL" if column[2] == "YES" else "NOT NULL"
                        print(f"          {column[0]} - {column[1]} {nullable}")
        else:
            print(f"    Failed to list tables in {schema_name}")
            if messages:
                print("    Messages:")
                for message in messages:
                    print(f"      {message}")

if __name__ == "__main__":
    main()
