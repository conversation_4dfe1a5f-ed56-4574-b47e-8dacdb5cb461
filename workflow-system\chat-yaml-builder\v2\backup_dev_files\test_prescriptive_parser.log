2025-05-11 03:53:11,782 - test_prescriptive_parser - INFO - Testing parsing and deploying components individually
2025-05-11 03:53:11,782 - prescriptive_parser - INFO - Initializing PrescriptiveParser
2025-05-11 03:53:11,782 - component_deployer - INFO - ComponentDeployer initialized with use_temp_schema=False
2025-05-11 03:53:11,782 - test_prescriptive_parser - INFO - Testing entities
2025-05-11 03:53:11,782 - test_prescriptive_parser - INFO - Reading prescriptive text from prompts/entities_prompt.txt
2025-05-11 03:53:11,782 - prescriptive_parser - INFO - Parsing entities prescriptive text
2025-05-11 03:53:11,782 - entity_parser - INFO - Starting to parse entity definitions
2025-05-11 03:53:11,782 - entity_parser - WARNING - Invalid entity header format: # Entity Definition Prompt

You are a specialized AI assistant tasked with creating entity definitions for a workflow system. Your goal is to generate well-structured, comprehensive entity definitions that follow the standard template and best practices.

## Instructions

1. Create entity definitions based on the provided information and requirements.
2. Follow the standard entity definition template exactly.
3. Define attributes clearly, marking primary keys (^PK) and foreign keys (^FK).
4. Specify relationships between entities.
5. Include constants, validations, and business rules where applicable.
6. Define calculated/derived fields with clear formulas and dependencies.
7. Specify hierarchical dependencies if they exist.
8. Include data lifecycle management details where relevant.
9. Ensure all entity and attribute references are consistent with the system's data model.

## 1. Basic Entity Declaration
```
[EntityName] has [attribute1]^PK, [attribute2], [attribute3] (enum1, enum2), [attribute4]^FK, [attribute5].
```

## 2. Relationships
```
* [EntityName] has [relationship-type] relationship with [RelatedEntity] using [EntityName].[attributeX]^PK to [RelatedEntity].[attributeY]^FK
```

## 3. Constants & Configuration
```
* [EntityName].[attribute] [PROPERTY_NAME] = [value]
* [EntityName].[attribute] DEFAULT_VALUE = [value]
```

## 4. Validations
```
* [EntityName].[attribute] must be [constraint]
```

## 5. Business Rules
```
BusinessRule [ruleID] for [EntityName]
2025-05-11 03:53:11,782 - entity_parser - WARNING - Invalid entity header format: Entity Metadata
2025-05-11 03:53:11,782 - entity_parser - INFO - Successfully parsed 0 entities
2025-05-11 03:53:11,782 - test_prescriptive_parser - WARNING - Parse warnings for entities: ["Invalid entity header format: # Entity Definition Prompt\n\nYou are a specialized AI assistant tasked with creating entity definitions for a workflow system. Your goal is to generate well-structured, comprehensive entity definitions that follow the standard template and best practices.\n\n## Instructions\n\n1. Create entity definitions based on the provided information and requirements.\n2. Follow the standard entity definition template exactly.\n3. Define attributes clearly, marking primary keys (^PK) and foreign keys (^FK).\n4. Specify relationships between entities.\n5. Include constants, validations, and business rules where applicable.\n6. Define calculated/derived fields with clear formulas and dependencies.\n7. Specify hierarchical dependencies if they exist.\n8. Include data lifecycle management details where relevant.\n9. Ensure all entity and attribute references are consistent with the system's data model.\n\n## 1. Basic Entity Declaration\n```\n[EntityName] has [attribute1]^PK, [attribute2], [attribute3] (enum1, enum2), [attribute4]^FK, [attribute5].\n```\n\n## 2. Relationships\n```\n* [EntityName] has [relationship-type] relationship with [RelatedEntity] using [EntityName].[attributeX]^PK to [RelatedEntity].[attributeY]^FK\n```\n\n## 3. Constants & Configuration\n```\n* [EntityName].[attribute] [PROPERTY_NAME] = [value]\n* [EntityName].[attribute] DEFAULT_VALUE = [value]\n```\n\n## 4. Validations\n```\n* [EntityName].[attribute] must be [constraint]\n```\n\n## 5. Business Rules\n```\nBusinessRule [ruleID] for [EntityName]", 'Invalid entity header format: Entity Metadata']
2025-05-11 03:53:11,782 - test_prescriptive_parser - INFO - Successfully parsed entities
2025-05-11 03:53:11,782 - component_deployer - INFO - Deploying entities from prescriptive text
2025-05-11 03:53:11,782 - prescriptive_parser - INFO - Initializing PrescriptiveParser
2025-05-11 03:53:11,782 - prescriptive_parser - INFO - Parsing entities prescriptive text
2025-05-11 03:53:11,782 - entity_parser - INFO - Starting to parse entity definitions
2025-05-11 03:53:11,782 - entity_parser - WARNING - Invalid entity header format: # Entity Definition Prompt

You are a specialized AI assistant tasked with creating entity definitions for a workflow system. Your goal is to generate well-structured, comprehensive entity definitions that follow the standard template and best practices.

## Instructions

1. Create entity definitions based on the provided information and requirements.
2. Follow the standard entity definition template exactly.
3. Define attributes clearly, marking primary keys (^PK) and foreign keys (^FK).
4. Specify relationships between entities.
5. Include constants, validations, and business rules where applicable.
6. Define calculated/derived fields with clear formulas and dependencies.
7. Specify hierarchical dependencies if they exist.
8. Include data lifecycle management details where relevant.
9. Ensure all entity and attribute references are consistent with the system's data model.

## 1. Basic Entity Declaration
```
[EntityName] has [attribute1]^PK, [attribute2], [attribute3] (enum1, enum2), [attribute4]^FK, [attribute5].
```

## 2. Relationships
```
* [EntityName] has [relationship-type] relationship with [RelatedEntity] using [EntityName].[attributeX]^PK to [RelatedEntity].[attributeY]^FK
```

## 3. Constants & Configuration
```
* [EntityName].[attribute] [PROPERTY_NAME] = [value]
* [EntityName].[attribute] DEFAULT_VALUE = [value]
```

## 4. Validations
```
* [EntityName].[attribute] must be [constraint]
```

## 5. Business Rules
```
BusinessRule [ruleID] for [EntityName]
2025-05-11 03:53:11,783 - entity_parser - WARNING - Invalid entity header format: Entity Metadata
2025-05-11 03:53:11,783 - entity_parser - INFO - Successfully parsed 0 entities
2025-05-11 03:53:11,783 - component_deployer - INFO - Deploying component of type: entities
2025-05-11 03:53:11,783 - component_deployer - INFO - Using schema: workflow_runtime
2025-05-11 03:53:11,783 - entity_deployer - INFO - Deploying entities to workflow_runtime
2025-05-11 03:53:11,797 - entity_deployer - INFO - Entity deployment completed successfully
2025-05-11 03:53:11,797 - component_deployer - INFO - Component deployment succeeded
2025-05-11 03:53:11,797 - test_prescriptive_parser - INFO - Deploy messages for entities: ["Invalid entity header format: # Entity Definition Prompt\n\nYou are a specialized AI assistant tasked with creating entity definitions for a workflow system. Your goal is to generate well-structured, comprehensive entity definitions that follow the standard template and best practices.\n\n## Instructions\n\n1. Create entity definitions based on the provided information and requirements.\n2. Follow the standard entity definition template exactly.\n3. Define attributes clearly, marking primary keys (^PK) and foreign keys (^FK).\n4. Specify relationships between entities.\n5. Include constants, validations, and business rules where applicable.\n6. Define calculated/derived fields with clear formulas and dependencies.\n7. Specify hierarchical dependencies if they exist.\n8. Include data lifecycle management details where relevant.\n9. Ensure all entity and attribute references are consistent with the system's data model.\n\n## 1. Basic Entity Declaration\n```\n[EntityName] has [attribute1]^PK, [attribute2], [attribute3] (enum1, enum2), [attribute4]^FK, [attribute5].\n```\n\n## 2. Relationships\n```\n* [EntityName] has [relationship-type] relationship with [RelatedEntity] using [EntityName].[attributeX]^PK to [RelatedEntity].[attributeY]^FK\n```\n\n## 3. Constants & Configuration\n```\n* [EntityName].[attribute] [PROPERTY_NAME] = [value]\n* [EntityName].[attribute] DEFAULT_VALUE = [value]\n```\n\n## 4. Validations\n```\n* [EntityName].[attribute] must be [constraint]\n```\n\n## 5. Business Rules\n```\nBusinessRule [ruleID] for [EntityName]", 'Invalid entity header format: Entity Metadata', 'Inserted document into components']
2025-05-11 03:53:11,797 - test_prescriptive_parser - INFO - Successfully deployed entities
2025-05-11 03:53:11,797 - test_prescriptive_parser - INFO - Testing go_definitions
2025-05-11 03:53:11,797 - test_prescriptive_parser - INFO - Reading prescriptive text from prompts/go_definitions_prompt.txt
2025-05-11 03:53:11,797 - prescriptive_parser - INFO - Parsing go_definitions prescriptive text
2025-05-11 03:53:11,797 - go_parser - INFO - Starting to parse GO definitions
2025-05-11 03:53:11,797 - go_parser - WARNING - Invalid GO header format: # Global Objective (GO) Definition Prompt

You are a specialized AI assistant tasked with creating Global Objective (GO) definitions for a workflow system. Your goal is to generate well-structured, comprehensive GO definitions that follow the standard template and best practices.

## Instructions

1. Create GO definitions based on the provided information and requirements.
2. Follow the standard GO definition template exactly.
3. Ensure each GO has a unique ID in the format "goXXX" (e.g., go001, go002).
4. Define clear input and output stacks with explicit mappings.
5. Include a comprehensive process flow with all Local Objectives (LOs).
6. Specify business rules, integration points, and performance metadata.
7. Include process mining schema details for analytics.
8. Ensure all entity, attribute, and LO references are consistent with the system's data model.

## Critical Design Rules

### 1. LO Enumeration Requirements
- **EVERY LO must have an explicit ID** in the format "lo001", "lo002", etc.
- **EVERY LO referenced anywhere must be defined** in the Process Flow section
- **ALL routing references must use LO IDs**, not just names
- Maintain sequential numbering for all LOs
- Include ALL LOs, even system-triggered or rollback LOs

### 2. First LO Requirements
- The first LO in any workflow **MUST be human-triggered** unless explicitly marked as system-triggered
- System-triggered workflows are permitted ONLY when initiated by
2025-05-11 03:53:11,797 - go_parser - INFO - Successfully parsed 0 global objectives
2025-05-11 03:53:11,797 - test_prescriptive_parser - WARNING - Parse warnings for go_definitions: ['Invalid GO header format: # Global Objective (GO) Definition Prompt\n\nYou are a specialized AI assistant tasked with creating Global Objective (GO) definitions for a workflow system. Your goal is to generate well-structured, comprehensive GO definitions that follow the standard template and best practices.\n\n## Instructions\n\n1. Create GO definitions based on the provided information and requirements.\n2. Follow the standard GO definition template exactly.\n3. Ensure each GO has a unique ID in the format "goXXX" (e.g., go001, go002).\n4. Define clear input and output stacks with explicit mappings.\n5. Include a comprehensive process flow with all Local Objectives (LOs).\n6. Specify business rules, integration points, and performance metadata.\n7. Include process mining schema details for analytics.\n8. Ensure all entity, attribute, and LO references are consistent with the system\'s data model.\n\n## Critical Design Rules\n\n### 1. LO Enumeration Requirements\n- **EVERY LO must have an explicit ID** in the format "lo001", "lo002", etc.\n- **EVERY LO referenced anywhere must be defined** in the Process Flow section\n- **ALL routing references must use LO IDs**, not just names\n- Maintain sequential numbering for all LOs\n- Include ALL LOs, even system-triggered or rollback LOs\n\n### 2. First LO Requirements\n- The first LO in any workflow **MUST be human-triggered** unless explicitly marked as system-triggered\n- System-triggered workflows are permitted ONLY when initiated by']
2025-05-11 03:53:11,798 - test_prescriptive_parser - INFO - Successfully parsed go_definitions
2025-05-11 03:53:11,798 - component_deployer - INFO - Deploying go_definitions from prescriptive text
2025-05-11 03:53:11,798 - prescriptive_parser - INFO - Initializing PrescriptiveParser
2025-05-11 03:53:11,798 - prescriptive_parser - INFO - Parsing go_definitions prescriptive text
2025-05-11 03:53:11,798 - go_parser - INFO - Starting to parse GO definitions
2025-05-11 03:53:11,798 - go_parser - WARNING - Invalid GO header format: # Global Objective (GO) Definition Prompt

You are a specialized AI assistant tasked with creating Global Objective (GO) definitions for a workflow system. Your goal is to generate well-structured, comprehensive GO definitions that follow the standard template and best practices.

## Instructions

1. Create GO definitions based on the provided information and requirements.
2. Follow the standard GO definition template exactly.
3. Ensure each GO has a unique ID in the format "goXXX" (e.g., go001, go002).
4. Define clear input and output stacks with explicit mappings.
5. Include a comprehensive process flow with all Local Objectives (LOs).
6. Specify business rules, integration points, and performance metadata.
7. Include process mining schema details for analytics.
8. Ensure all entity, attribute, and LO references are consistent with the system's data model.

## Critical Design Rules

### 1. LO Enumeration Requirements
- **EVERY LO must have an explicit ID** in the format "lo001", "lo002", etc.
- **EVERY LO referenced anywhere must be defined** in the Process Flow section
- **ALL routing references must use LO IDs**, not just names
- Maintain sequential numbering for all LOs
- Include ALL LOs, even system-triggered or rollback LOs

### 2. First LO Requirements
- The first LO in any workflow **MUST be human-triggered** unless explicitly marked as system-triggered
- System-triggered workflows are permitted ONLY when initiated by
2025-05-11 03:53:11,798 - go_parser - INFO - Successfully parsed 0 global objectives
2025-05-11 03:53:11,798 - component_deployer - INFO - Deploying component of type: go_definitions
2025-05-11 03:53:11,798 - component_deployer - INFO - Using schema: workflow_runtime
2025-05-11 03:53:11,803 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 03:53:11,804 - go_deployer - INFO - Deploying GO definitions to workflow_runtime
2025-05-11 03:53:11,812 - go_deployer - INFO - GO deployment completed successfully
2025-05-11 03:53:11,812 - component_deployer - INFO - Component deployment succeeded
2025-05-11 03:53:11,812 - test_prescriptive_parser - INFO - Deploy messages for go_definitions: ['Invalid GO header format: # Global Objective (GO) Definition Prompt\n\nYou are a specialized AI assistant tasked with creating Global Objective (GO) definitions for a workflow system. Your goal is to generate well-structured, comprehensive GO definitions that follow the standard template and best practices.\n\n## Instructions\n\n1. Create GO definitions based on the provided information and requirements.\n2. Follow the standard GO definition template exactly.\n3. Ensure each GO has a unique ID in the format "goXXX" (e.g., go001, go002).\n4. Define clear input and output stacks with explicit mappings.\n5. Include a comprehensive process flow with all Local Objectives (LOs).\n6. Specify business rules, integration points, and performance metadata.\n7. Include process mining schema details for analytics.\n8. Ensure all entity, attribute, and LO references are consistent with the system\'s data model.\n\n## Critical Design Rules\n\n### 1. LO Enumeration Requirements\n- **EVERY LO must have an explicit ID** in the format "lo001", "lo002", etc.\n- **EVERY LO referenced anywhere must be defined** in the Process Flow section\n- **ALL routing references must use LO IDs**, not just names\n- Maintain sequential numbering for all LOs\n- Include ALL LOs, even system-triggered or rollback LOs\n\n### 2. First LO Requirements\n- The first LO in any workflow **MUST be human-triggered** unless explicitly marked as system-triggered\n- System-triggered workflows are permitted ONLY when initiated by', 'Inserted document into components']
2025-05-11 03:53:11,813 - test_prescriptive_parser - INFO - Successfully deployed go_definitions
2025-05-11 03:53:11,813 - test_prescriptive_parser - INFO - Testing lo_definitions
2025-05-11 03:53:11,813 - test_prescriptive_parser - INFO - Reading prescriptive text from prompts/lo_definitions_prompt.txt
2025-05-11 03:53:11,813 - prescriptive_parser - INFO - Parsing lo_definitions prescriptive text
2025-05-11 03:53:11,813 - lo_parser - INFO - Starting to parse LO definitions
2025-05-11 03:53:11,813 - lo_parser - WARNING - Invalid LO header format: # Local Objective (LO) Definition Prompt

You are a specialized AI assistant tasked with creating Local Objective (LO) definitions for a workflow system. Your goal is to generate well-structured, comprehensive LO definitions that follow the standard template and best practices.

## Instructions

1. Create LO definitions based on the provided information and requirements.
2. Follow the standard LO definition template exactly.
3. Ensure each LO has a unique ID in the format "loXXX" (e.g., lo001, lo002).
4. Define clear inputs and outputs with all required attributes.
5. Specify system actions, DB constraints, UI controls, and mapping details.
6. Include execution pathways and synthetic values for testing.
7. Ensure all entity, attribute, and role references are consistent with the system's data model.
8. Make sure LO definitions align with their parent Global Objective (GO) definitions.

## Core LO Definition Structure

Each LO definition should follow this comprehensive format
2025-05-11 03:53:11,813 - lo_parser - INFO - Successfully parsed 0 local objectives
2025-05-11 03:53:11,813 - test_prescriptive_parser - WARNING - Parse warnings for lo_definitions: ['Invalid LO header format: # Local Objective (LO) Definition Prompt\n\nYou are a specialized AI assistant tasked with creating Local Objective (LO) definitions for a workflow system. Your goal is to generate well-structured, comprehensive LO definitions that follow the standard template and best practices.\n\n## Instructions\n\n1. Create LO definitions based on the provided information and requirements.\n2. Follow the standard LO definition template exactly.\n3. Ensure each LO has a unique ID in the format "loXXX" (e.g., lo001, lo002).\n4. Define clear inputs and outputs with all required attributes.\n5. Specify system actions, DB constraints, UI controls, and mapping details.\n6. Include execution pathways and synthetic values for testing.\n7. Ensure all entity, attribute, and role references are consistent with the system\'s data model.\n8. Make sure LO definitions align with their parent Global Objective (GO) definitions.\n\n## Core LO Definition Structure\n\nEach LO definition should follow this comprehensive format']
2025-05-11 03:53:11,813 - test_prescriptive_parser - INFO - Successfully parsed lo_definitions
2025-05-11 03:53:11,813 - component_deployer - INFO - Deploying lo_definitions from prescriptive text
2025-05-11 03:53:11,813 - prescriptive_parser - INFO - Initializing PrescriptiveParser
2025-05-11 03:53:11,813 - prescriptive_parser - INFO - Parsing lo_definitions prescriptive text
2025-05-11 03:53:11,813 - lo_parser - INFO - Starting to parse LO definitions
2025-05-11 03:53:11,813 - lo_parser - WARNING - Invalid LO header format: # Local Objective (LO) Definition Prompt

You are a specialized AI assistant tasked with creating Local Objective (LO) definitions for a workflow system. Your goal is to generate well-structured, comprehensive LO definitions that follow the standard template and best practices.

## Instructions

1. Create LO definitions based on the provided information and requirements.
2. Follow the standard LO definition template exactly.
3. Ensure each LO has a unique ID in the format "loXXX" (e.g., lo001, lo002).
4. Define clear inputs and outputs with all required attributes.
5. Specify system actions, DB constraints, UI controls, and mapping details.
6. Include execution pathways and synthetic values for testing.
7. Ensure all entity, attribute, and role references are consistent with the system's data model.
8. Make sure LO definitions align with their parent Global Objective (GO) definitions.

## Core LO Definition Structure

Each LO definition should follow this comprehensive format
2025-05-11 03:53:11,813 - lo_parser - INFO - Successfully parsed 0 local objectives
2025-05-11 03:53:11,813 - component_deployer - INFO - Deploying component of type: lo_definitions
2025-05-11 03:53:11,814 - component_deployer - INFO - Using schema: workflow_runtime
2025-05-11 03:53:11,819 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 03:53:11,824 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 03:53:11,825 - lo_deployer - INFO - Deploying LO definitions to workflow_runtime
2025-05-11 03:53:11,833 - lo_deployer - INFO - LO deployment completed successfully
2025-05-11 03:53:11,834 - component_deployer - INFO - Component deployment succeeded
2025-05-11 03:53:11,834 - test_prescriptive_parser - INFO - Deploy messages for lo_definitions: ['Invalid LO header format: # Local Objective (LO) Definition Prompt\n\nYou are a specialized AI assistant tasked with creating Local Objective (LO) definitions for a workflow system. Your goal is to generate well-structured, comprehensive LO definitions that follow the standard template and best practices.\n\n## Instructions\n\n1. Create LO definitions based on the provided information and requirements.\n2. Follow the standard LO definition template exactly.\n3. Ensure each LO has a unique ID in the format "loXXX" (e.g., lo001, lo002).\n4. Define clear inputs and outputs with all required attributes.\n5. Specify system actions, DB constraints, UI controls, and mapping details.\n6. Include execution pathways and synthetic values for testing.\n7. Ensure all entity, attribute, and role references are consistent with the system\'s data model.\n8. Make sure LO definitions align with their parent Global Objective (GO) definitions.\n\n## Core LO Definition Structure\n\nEach LO definition should follow this comprehensive format', 'Inserted document into components']
2025-05-11 03:53:11,834 - test_prescriptive_parser - INFO - Successfully deployed lo_definitions
2025-05-11 03:53:11,834 - test_prescriptive_parser - INFO - Testing roles
2025-05-11 03:53:11,834 - test_prescriptive_parser - INFO - Reading prescriptive text from prompts/roles_prompt.txt
2025-05-11 03:53:11,834 - prescriptive_parser - INFO - Parsing roles prescriptive text
2025-05-11 03:53:11,834 - role_parser - INFO - Starting to parse role definitions
2025-05-11 03:53:11,834 - role_parser - WARNING - Invalid role header format: # Role Definition Prompt

You are a specialized AI assistant tasked with creating role definitions for a workflow system. Your goal is to generate well-structured, comprehensive role definitions that follow the standard template and best practices.

## Instructions

1. Create role definitions based on the provided information and requirements.
2. Follow the standard role definition template exactly.
3. Ensure each role has a unique ID in the format "roleXXX" (e.g., role001, role002).
4. Define clear permissions for Create, Read, Update operations on entities.
5. Specify Global Objective (GO) associations with appropriate role types.
6. Include scope, classification, and special conditions where applicable.
7. For inherited roles, clearly specify the parent role and only add the additional permissions.
8. Ensure all entity and attribute references are consistent with the system's data model.

## Standard Role Definition Template

```
2025-05-11 03:53:11,834 - role_parser - INFO - Parsing role: [RoleName] (ID: [role_id])
2025-05-11 03:53:11,834 - role_parser - INFO - Successfully parsed role '[RoleName]'
2025-05-11 03:53:11,834 - role_parser - WARNING - Invalid role header format: Role Details
2025-05-11 03:53:11,834 - role_parser - INFO - Parsing role: Employee (ID: emp001)
2025-05-11 03:53:11,834 - role_parser - INFO - Successfully parsed role 'Employee'
2025-05-11 03:53:11,834 - role_parser - INFO - Parsing role: Manager (ID: mgr001)
2025-05-11 03:53:11,834 - role_parser - INFO - Successfully parsed role 'Manager'
2025-05-11 03:53:11,834 - role_parser - INFO - Successfully parsed 3 roles
2025-05-11 03:53:11,834 - test_prescriptive_parser - WARNING - Parse warnings for roles: ['Invalid role header format: # Role Definition Prompt\n\nYou are a specialized AI assistant tasked with creating role definitions for a workflow system. Your goal is to generate well-structured, comprehensive role definitions that follow the standard template and best practices.\n\n## Instructions\n\n1. Create role definitions based on the provided information and requirements.\n2. Follow the standard role definition template exactly.\n3. Ensure each role has a unique ID in the format "roleXXX" (e.g., role001, role002).\n4. Define clear permissions for Create, Read, Update operations on entities.\n5. Specify Global Objective (GO) associations with appropriate role types.\n6. Include scope, classification, and special conditions where applicable.\n7. For inherited roles, clearly specify the parent role and only add the additional permissions.\n8. Ensure all entity and attribute references are consistent with the system\'s data model.\n\n## Standard Role Definition Template\n\n```', 'Invalid role header format: Role Details']
2025-05-11 03:53:11,834 - test_prescriptive_parser - INFO - Successfully parsed roles
2025-05-11 03:53:11,834 - component_deployer - INFO - Deploying roles from prescriptive text
2025-05-11 03:53:11,834 - prescriptive_parser - INFO - Initializing PrescriptiveParser
2025-05-11 03:53:11,834 - prescriptive_parser - INFO - Parsing roles prescriptive text
2025-05-11 03:53:11,834 - role_parser - INFO - Starting to parse role definitions
2025-05-11 03:53:11,834 - role_parser - WARNING - Invalid role header format: # Role Definition Prompt

You are a specialized AI assistant tasked with creating role definitions for a workflow system. Your goal is to generate well-structured, comprehensive role definitions that follow the standard template and best practices.

## Instructions

1. Create role definitions based on the provided information and requirements.
2. Follow the standard role definition template exactly.
3. Ensure each role has a unique ID in the format "roleXXX" (e.g., role001, role002).
4. Define clear permissions for Create, Read, Update operations on entities.
5. Specify Global Objective (GO) associations with appropriate role types.
6. Include scope, classification, and special conditions where applicable.
7. For inherited roles, clearly specify the parent role and only add the additional permissions.
8. Ensure all entity and attribute references are consistent with the system's data model.

## Standard Role Definition Template

```
2025-05-11 03:53:11,835 - role_parser - INFO - Parsing role: [RoleName] (ID: [role_id])
2025-05-11 03:53:11,835 - role_parser - INFO - Successfully parsed role '[RoleName]'
2025-05-11 03:53:11,835 - role_parser - WARNING - Invalid role header format: Role Details
2025-05-11 03:53:11,835 - role_parser - INFO - Parsing role: Employee (ID: emp001)
2025-05-11 03:53:11,835 - role_parser - INFO - Successfully parsed role 'Employee'
2025-05-11 03:53:11,835 - role_parser - INFO - Parsing role: Manager (ID: mgr001)
2025-05-11 03:53:11,835 - role_parser - INFO - Successfully parsed role 'Manager'
2025-05-11 03:53:11,835 - role_parser - INFO - Successfully parsed 3 roles
2025-05-11 03:53:11,836 - component_deployer - INFO - Deploying component of type: roles
2025-05-11 03:53:11,837 - component_deployer - INFO - Using schema: workflow_runtime
2025-05-11 03:53:11,841 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 03:53:11,846 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 03:53:11,851 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 03:53:11,852 - role_deployer - INFO - Deploying roles to workflow_runtime
2025-05-11 03:53:11,855 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 03:53:11,859 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 03:53:11,860 - db_utils - ERROR - Database error: column "description" of relation "roles" does not exist
LINE 3:                     role_id, name, description, permissions,...
                                           ^
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 54, in execute_query
    cursor.execute(query, params)
psycopg2.errors.UndefinedColumn: column "description" of relation "roles" does not exist
LINE 3:                     role_id, name, description, permissions,...
                                           ^

2025-05-11 03:53:11,860 - component_deployer - INFO - Component deployment failed
2025-05-11 03:53:11,860 - test_prescriptive_parser - INFO - Deploy messages for roles: ['Invalid role header format: # Role Definition Prompt\n\nYou are a specialized AI assistant tasked with creating role definitions for a workflow system. Your goal is to generate well-structured, comprehensive role definitions that follow the standard template and best practices.\n\n## Instructions\n\n1. Create role definitions based on the provided information and requirements.\n2. Follow the standard role definition template exactly.\n3. Ensure each role has a unique ID in the format "roleXXX" (e.g., role001, role002).\n4. Define clear permissions for Create, Read, Update operations on entities.\n5. Specify Global Objective (GO) associations with appropriate role types.\n6. Include scope, classification, and special conditions where applicable.\n7. For inherited roles, clearly specify the parent role and only add the additional permissions.\n8. Ensure all entity and attribute references are consistent with the system\'s data model.\n\n## Standard Role Definition Template\n\n```', 'Invalid role header format: Role Details', 'Database error: column "description" of relation "roles" does not exist\nLINE 3:                     role_id, name, description, permissions,...\n                                           ^\n']
2025-05-11 03:53:11,860 - test_prescriptive_parser - ERROR - Failed to deploy roles
2025-05-11 03:53:11,860 - test_prescriptive_parser - ERROR - Individual test failed
2025-05-11 03:56:13,122 - test_prescriptive_parser - INFO - Testing parsing and deploying components individually
2025-05-11 03:56:13,122 - prescriptive_parser - INFO - Initializing PrescriptiveParser
2025-05-11 03:56:13,122 - component_deployer - INFO - ComponentDeployer initialized with use_temp_schema=False
2025-05-11 03:56:13,122 - test_prescriptive_parser - INFO - Testing entities
2025-05-11 03:56:13,122 - test_prescriptive_parser - INFO - Reading prescriptive text from prompts/entities_prompt.txt
2025-05-11 03:56:13,122 - prescriptive_parser - INFO - Parsing entities prescriptive text
2025-05-11 03:56:13,123 - entity_parser - INFO - Starting to parse entity definitions
2025-05-11 03:56:13,123 - entity_parser - WARNING - Invalid entity header format: # Entity Definition Prompt

You are a specialized AI assistant tasked with creating entity definitions for a workflow system. Your goal is to generate well-structured, comprehensive entity definitions that follow the standard template and best practices.

## Instructions

1. Create entity definitions based on the provided information and requirements.
2. Follow the standard entity definition template exactly.
3. Define attributes clearly, marking primary keys (^PK) and foreign keys (^FK).
4. Specify relationships between entities.
5. Include constants, validations, and business rules where applicable.
6. Define calculated/derived fields with clear formulas and dependencies.
7. Specify hierarchical dependencies if they exist.
8. Include data lifecycle management details where relevant.
9. Ensure all entity and attribute references are consistent with the system's data model.

## 1. Basic Entity Declaration
```
[EntityName] has [attribute1]^PK, [attribute2], [attribute3] (enum1, enum2), [attribute4]^FK, [attribute5].
```

## 2. Relationships
```
* [EntityName] has [relationship-type] relationship with [RelatedEntity] using [EntityName].[attributeX]^PK to [RelatedEntity].[attributeY]^FK
```

## 3. Constants & Configuration
```
* [EntityName].[attribute] [PROPERTY_NAME] = [value]
* [EntityName].[attribute] DEFAULT_VALUE = [value]
```

## 4. Validations
```
* [EntityName].[attribute] must be [constraint]
```

## 5. Business Rules
```
BusinessRule [ruleID] for [EntityName]
2025-05-11 03:56:13,123 - entity_parser - WARNING - Invalid entity header format: Entity Metadata
2025-05-11 03:56:13,123 - entity_parser - INFO - Successfully parsed 0 entities
2025-05-11 03:56:13,123 - test_prescriptive_parser - WARNING - Parse warnings for entities: ["Invalid entity header format: # Entity Definition Prompt\n\nYou are a specialized AI assistant tasked with creating entity definitions for a workflow system. Your goal is to generate well-structured, comprehensive entity definitions that follow the standard template and best practices.\n\n## Instructions\n\n1. Create entity definitions based on the provided information and requirements.\n2. Follow the standard entity definition template exactly.\n3. Define attributes clearly, marking primary keys (^PK) and foreign keys (^FK).\n4. Specify relationships between entities.\n5. Include constants, validations, and business rules where applicable.\n6. Define calculated/derived fields with clear formulas and dependencies.\n7. Specify hierarchical dependencies if they exist.\n8. Include data lifecycle management details where relevant.\n9. Ensure all entity and attribute references are consistent with the system's data model.\n\n## 1. Basic Entity Declaration\n```\n[EntityName] has [attribute1]^PK, [attribute2], [attribute3] (enum1, enum2), [attribute4]^FK, [attribute5].\n```\n\n## 2. Relationships\n```\n* [EntityName] has [relationship-type] relationship with [RelatedEntity] using [EntityName].[attributeX]^PK to [RelatedEntity].[attributeY]^FK\n```\n\n## 3. Constants & Configuration\n```\n* [EntityName].[attribute] [PROPERTY_NAME] = [value]\n* [EntityName].[attribute] DEFAULT_VALUE = [value]\n```\n\n## 4. Validations\n```\n* [EntityName].[attribute] must be [constraint]\n```\n\n## 5. Business Rules\n```\nBusinessRule [ruleID] for [EntityName]", 'Invalid entity header format: Entity Metadata']
2025-05-11 03:56:13,123 - test_prescriptive_parser - INFO - Successfully parsed entities
2025-05-11 03:56:13,123 - component_deployer - INFO - Deploying entities from prescriptive text
2025-05-11 03:56:13,123 - prescriptive_parser - INFO - Initializing PrescriptiveParser
2025-05-11 03:56:13,123 - prescriptive_parser - INFO - Parsing entities prescriptive text
2025-05-11 03:56:13,123 - entity_parser - INFO - Starting to parse entity definitions
2025-05-11 03:56:13,123 - entity_parser - WARNING - Invalid entity header format: # Entity Definition Prompt

You are a specialized AI assistant tasked with creating entity definitions for a workflow system. Your goal is to generate well-structured, comprehensive entity definitions that follow the standard template and best practices.

## Instructions

1. Create entity definitions based on the provided information and requirements.
2. Follow the standard entity definition template exactly.
3. Define attributes clearly, marking primary keys (^PK) and foreign keys (^FK).
4. Specify relationships between entities.
5. Include constants, validations, and business rules where applicable.
6. Define calculated/derived fields with clear formulas and dependencies.
7. Specify hierarchical dependencies if they exist.
8. Include data lifecycle management details where relevant.
9. Ensure all entity and attribute references are consistent with the system's data model.

## 1. Basic Entity Declaration
```
[EntityName] has [attribute1]^PK, [attribute2], [attribute3] (enum1, enum2), [attribute4]^FK, [attribute5].
```

## 2. Relationships
```
* [EntityName] has [relationship-type] relationship with [RelatedEntity] using [EntityName].[attributeX]^PK to [RelatedEntity].[attributeY]^FK
```

## 3. Constants & Configuration
```
* [EntityName].[attribute] [PROPERTY_NAME] = [value]
* [EntityName].[attribute] DEFAULT_VALUE = [value]
```

## 4. Validations
```
* [EntityName].[attribute] must be [constraint]
```

## 5. Business Rules
```
BusinessRule [ruleID] for [EntityName]
2025-05-11 03:56:13,123 - entity_parser - WARNING - Invalid entity header format: Entity Metadata
2025-05-11 03:56:13,123 - entity_parser - INFO - Successfully parsed 0 entities
2025-05-11 03:56:13,123 - component_deployer - INFO - Deploying component of type: entities
2025-05-11 03:56:13,123 - component_deployer - INFO - Using schema: workflow_runtime
2025-05-11 03:56:13,124 - entity_deployer - INFO - Deploying entities to workflow_runtime
2025-05-11 03:56:13,137 - entity_deployer - INFO - Entity deployment completed successfully
2025-05-11 03:56:13,137 - component_deployer - INFO - Component deployment succeeded
2025-05-11 03:56:13,137 - test_prescriptive_parser - INFO - Deploy messages for entities: ["Invalid entity header format: # Entity Definition Prompt\n\nYou are a specialized AI assistant tasked with creating entity definitions for a workflow system. Your goal is to generate well-structured, comprehensive entity definitions that follow the standard template and best practices.\n\n## Instructions\n\n1. Create entity definitions based on the provided information and requirements.\n2. Follow the standard entity definition template exactly.\n3. Define attributes clearly, marking primary keys (^PK) and foreign keys (^FK).\n4. Specify relationships between entities.\n5. Include constants, validations, and business rules where applicable.\n6. Define calculated/derived fields with clear formulas and dependencies.\n7. Specify hierarchical dependencies if they exist.\n8. Include data lifecycle management details where relevant.\n9. Ensure all entity and attribute references are consistent with the system's data model.\n\n## 1. Basic Entity Declaration\n```\n[EntityName] has [attribute1]^PK, [attribute2], [attribute3] (enum1, enum2), [attribute4]^FK, [attribute5].\n```\n\n## 2. Relationships\n```\n* [EntityName] has [relationship-type] relationship with [RelatedEntity] using [EntityName].[attributeX]^PK to [RelatedEntity].[attributeY]^FK\n```\n\n## 3. Constants & Configuration\n```\n* [EntityName].[attribute] [PROPERTY_NAME] = [value]\n* [EntityName].[attribute] DEFAULT_VALUE = [value]\n```\n\n## 4. Validations\n```\n* [EntityName].[attribute] must be [constraint]\n```\n\n## 5. Business Rules\n```\nBusinessRule [ruleID] for [EntityName]", 'Invalid entity header format: Entity Metadata', 'Inserted document into components']
2025-05-11 03:56:13,137 - test_prescriptive_parser - INFO - Successfully deployed entities
2025-05-11 03:56:13,137 - test_prescriptive_parser - INFO - Testing go_definitions
2025-05-11 03:56:13,137 - test_prescriptive_parser - INFO - Reading prescriptive text from prompts/go_definitions_prompt.txt
2025-05-11 03:56:13,137 - prescriptive_parser - INFO - Parsing go_definitions prescriptive text
2025-05-11 03:56:13,137 - go_parser - INFO - Starting to parse GO definitions
2025-05-11 03:56:13,138 - go_parser - WARNING - Invalid GO header format: # Global Objective (GO) Definition Prompt

You are a specialized AI assistant tasked with creating Global Objective (GO) definitions for a workflow system. Your goal is to generate well-structured, comprehensive GO definitions that follow the standard template and best practices.

## Instructions

1. Create GO definitions based on the provided information and requirements.
2. Follow the standard GO definition template exactly.
3. Ensure each GO has a unique ID in the format "goXXX" (e.g., go001, go002).
4. Define clear input and output stacks with explicit mappings.
5. Include a comprehensive process flow with all Local Objectives (LOs).
6. Specify business rules, integration points, and performance metadata.
7. Include process mining schema details for analytics.
8. Ensure all entity, attribute, and LO references are consistent with the system's data model.

## Critical Design Rules

### 1. LO Enumeration Requirements
- **EVERY LO must have an explicit ID** in the format "lo001", "lo002", etc.
- **EVERY LO referenced anywhere must be defined** in the Process Flow section
- **ALL routing references must use LO IDs**, not just names
- Maintain sequential numbering for all LOs
- Include ALL LOs, even system-triggered or rollback LOs

### 2. First LO Requirements
- The first LO in any workflow **MUST be human-triggered** unless explicitly marked as system-triggered
- System-triggered workflows are permitted ONLY when initiated by
2025-05-11 03:56:13,138 - go_parser - INFO - Successfully parsed 0 global objectives
2025-05-11 03:56:13,138 - test_prescriptive_parser - WARNING - Parse warnings for go_definitions: ['Invalid GO header format: # Global Objective (GO) Definition Prompt\n\nYou are a specialized AI assistant tasked with creating Global Objective (GO) definitions for a workflow system. Your goal is to generate well-structured, comprehensive GO definitions that follow the standard template and best practices.\n\n## Instructions\n\n1. Create GO definitions based on the provided information and requirements.\n2. Follow the standard GO definition template exactly.\n3. Ensure each GO has a unique ID in the format "goXXX" (e.g., go001, go002).\n4. Define clear input and output stacks with explicit mappings.\n5. Include a comprehensive process flow with all Local Objectives (LOs).\n6. Specify business rules, integration points, and performance metadata.\n7. Include process mining schema details for analytics.\n8. Ensure all entity, attribute, and LO references are consistent with the system\'s data model.\n\n## Critical Design Rules\n\n### 1. LO Enumeration Requirements\n- **EVERY LO must have an explicit ID** in the format "lo001", "lo002", etc.\n- **EVERY LO referenced anywhere must be defined** in the Process Flow section\n- **ALL routing references must use LO IDs**, not just names\n- Maintain sequential numbering for all LOs\n- Include ALL LOs, even system-triggered or rollback LOs\n\n### 2. First LO Requirements\n- The first LO in any workflow **MUST be human-triggered** unless explicitly marked as system-triggered\n- System-triggered workflows are permitted ONLY when initiated by']
2025-05-11 03:56:13,138 - test_prescriptive_parser - INFO - Successfully parsed go_definitions
2025-05-11 03:56:13,138 - component_deployer - INFO - Deploying go_definitions from prescriptive text
2025-05-11 03:56:13,138 - prescriptive_parser - INFO - Initializing PrescriptiveParser
2025-05-11 03:56:13,138 - prescriptive_parser - INFO - Parsing go_definitions prescriptive text
2025-05-11 03:56:13,138 - go_parser - INFO - Starting to parse GO definitions
2025-05-11 03:56:13,138 - go_parser - WARNING - Invalid GO header format: # Global Objective (GO) Definition Prompt

You are a specialized AI assistant tasked with creating Global Objective (GO) definitions for a workflow system. Your goal is to generate well-structured, comprehensive GO definitions that follow the standard template and best practices.

## Instructions

1. Create GO definitions based on the provided information and requirements.
2. Follow the standard GO definition template exactly.
3. Ensure each GO has a unique ID in the format "goXXX" (e.g., go001, go002).
4. Define clear input and output stacks with explicit mappings.
5. Include a comprehensive process flow with all Local Objectives (LOs).
6. Specify business rules, integration points, and performance metadata.
7. Include process mining schema details for analytics.
8. Ensure all entity, attribute, and LO references are consistent with the system's data model.

## Critical Design Rules

### 1. LO Enumeration Requirements
- **EVERY LO must have an explicit ID** in the format "lo001", "lo002", etc.
- **EVERY LO referenced anywhere must be defined** in the Process Flow section
- **ALL routing references must use LO IDs**, not just names
- Maintain sequential numbering for all LOs
- Include ALL LOs, even system-triggered or rollback LOs

### 2. First LO Requirements
- The first LO in any workflow **MUST be human-triggered** unless explicitly marked as system-triggered
- System-triggered workflows are permitted ONLY when initiated by
2025-05-11 03:56:13,138 - go_parser - INFO - Successfully parsed 0 global objectives
2025-05-11 03:56:13,138 - component_deployer - INFO - Deploying component of type: go_definitions
2025-05-11 03:56:13,138 - component_deployer - INFO - Using schema: workflow_runtime
2025-05-11 03:56:13,144 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 03:56:13,145 - go_deployer - INFO - Deploying GO definitions to workflow_runtime
2025-05-11 03:56:13,154 - go_deployer - INFO - GO deployment completed successfully
2025-05-11 03:56:13,154 - component_deployer - INFO - Component deployment succeeded
2025-05-11 03:56:13,154 - test_prescriptive_parser - INFO - Deploy messages for go_definitions: ['Invalid GO header format: # Global Objective (GO) Definition Prompt\n\nYou are a specialized AI assistant tasked with creating Global Objective (GO) definitions for a workflow system. Your goal is to generate well-structured, comprehensive GO definitions that follow the standard template and best practices.\n\n## Instructions\n\n1. Create GO definitions based on the provided information and requirements.\n2. Follow the standard GO definition template exactly.\n3. Ensure each GO has a unique ID in the format "goXXX" (e.g., go001, go002).\n4. Define clear input and output stacks with explicit mappings.\n5. Include a comprehensive process flow with all Local Objectives (LOs).\n6. Specify business rules, integration points, and performance metadata.\n7. Include process mining schema details for analytics.\n8. Ensure all entity, attribute, and LO references are consistent with the system\'s data model.\n\n## Critical Design Rules\n\n### 1. LO Enumeration Requirements\n- **EVERY LO must have an explicit ID** in the format "lo001", "lo002", etc.\n- **EVERY LO referenced anywhere must be defined** in the Process Flow section\n- **ALL routing references must use LO IDs**, not just names\n- Maintain sequential numbering for all LOs\n- Include ALL LOs, even system-triggered or rollback LOs\n\n### 2. First LO Requirements\n- The first LO in any workflow **MUST be human-triggered** unless explicitly marked as system-triggered\n- System-triggered workflows are permitted ONLY when initiated by', 'Inserted document into components']
2025-05-11 03:56:13,154 - test_prescriptive_parser - INFO - Successfully deployed go_definitions
2025-05-11 03:56:13,154 - test_prescriptive_parser - INFO - Testing lo_definitions
2025-05-11 03:56:13,154 - test_prescriptive_parser - INFO - Reading prescriptive text from prompts/lo_definitions_prompt.txt
2025-05-11 03:56:13,154 - prescriptive_parser - INFO - Parsing lo_definitions prescriptive text
2025-05-11 03:56:13,154 - lo_parser - INFO - Starting to parse LO definitions
2025-05-11 03:56:13,154 - lo_parser - WARNING - Invalid LO header format: # Local Objective (LO) Definition Prompt

You are a specialized AI assistant tasked with creating Local Objective (LO) definitions for a workflow system. Your goal is to generate well-structured, comprehensive LO definitions that follow the standard template and best practices.

## Instructions

1. Create LO definitions based on the provided information and requirements.
2. Follow the standard LO definition template exactly.
3. Ensure each LO has a unique ID in the format "loXXX" (e.g., lo001, lo002).
4. Define clear inputs and outputs with all required attributes.
5. Specify system actions, DB constraints, UI controls, and mapping details.
6. Include execution pathways and synthetic values for testing.
7. Ensure all entity, attribute, and role references are consistent with the system's data model.
8. Make sure LO definitions align with their parent Global Objective (GO) definitions.

## Core LO Definition Structure

Each LO definition should follow this comprehensive format
2025-05-11 03:56:13,154 - lo_parser - INFO - Successfully parsed 0 local objectives
2025-05-11 03:56:13,154 - test_prescriptive_parser - WARNING - Parse warnings for lo_definitions: ['Invalid LO header format: # Local Objective (LO) Definition Prompt\n\nYou are a specialized AI assistant tasked with creating Local Objective (LO) definitions for a workflow system. Your goal is to generate well-structured, comprehensive LO definitions that follow the standard template and best practices.\n\n## Instructions\n\n1. Create LO definitions based on the provided information and requirements.\n2. Follow the standard LO definition template exactly.\n3. Ensure each LO has a unique ID in the format "loXXX" (e.g., lo001, lo002).\n4. Define clear inputs and outputs with all required attributes.\n5. Specify system actions, DB constraints, UI controls, and mapping details.\n6. Include execution pathways and synthetic values for testing.\n7. Ensure all entity, attribute, and role references are consistent with the system\'s data model.\n8. Make sure LO definitions align with their parent Global Objective (GO) definitions.\n\n## Core LO Definition Structure\n\nEach LO definition should follow this comprehensive format']
2025-05-11 03:56:13,154 - test_prescriptive_parser - INFO - Successfully parsed lo_definitions
2025-05-11 03:56:13,154 - component_deployer - INFO - Deploying lo_definitions from prescriptive text
2025-05-11 03:56:13,154 - prescriptive_parser - INFO - Initializing PrescriptiveParser
2025-05-11 03:56:13,154 - prescriptive_parser - INFO - Parsing lo_definitions prescriptive text
2025-05-11 03:56:13,154 - lo_parser - INFO - Starting to parse LO definitions
2025-05-11 03:56:13,154 - lo_parser - WARNING - Invalid LO header format: # Local Objective (LO) Definition Prompt

You are a specialized AI assistant tasked with creating Local Objective (LO) definitions for a workflow system. Your goal is to generate well-structured, comprehensive LO definitions that follow the standard template and best practices.

## Instructions

1. Create LO definitions based on the provided information and requirements.
2. Follow the standard LO definition template exactly.
3. Ensure each LO has a unique ID in the format "loXXX" (e.g., lo001, lo002).
4. Define clear inputs and outputs with all required attributes.
5. Specify system actions, DB constraints, UI controls, and mapping details.
6. Include execution pathways and synthetic values for testing.
7. Ensure all entity, attribute, and role references are consistent with the system's data model.
8. Make sure LO definitions align with their parent Global Objective (GO) definitions.

## Core LO Definition Structure

Each LO definition should follow this comprehensive format
2025-05-11 03:56:13,154 - lo_parser - INFO - Successfully parsed 0 local objectives
2025-05-11 03:56:13,154 - component_deployer - INFO - Deploying component of type: lo_definitions
2025-05-11 03:56:13,155 - component_deployer - INFO - Using schema: workflow_runtime
2025-05-11 03:56:13,160 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 03:56:13,166 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 03:56:13,167 - lo_deployer - INFO - Deploying LO definitions to workflow_runtime
2025-05-11 03:56:13,174 - lo_deployer - INFO - LO deployment completed successfully
2025-05-11 03:56:13,175 - component_deployer - INFO - Component deployment succeeded
2025-05-11 03:56:13,175 - test_prescriptive_parser - INFO - Deploy messages for lo_definitions: ['Invalid LO header format: # Local Objective (LO) Definition Prompt\n\nYou are a specialized AI assistant tasked with creating Local Objective (LO) definitions for a workflow system. Your goal is to generate well-structured, comprehensive LO definitions that follow the standard template and best practices.\n\n## Instructions\n\n1. Create LO definitions based on the provided information and requirements.\n2. Follow the standard LO definition template exactly.\n3. Ensure each LO has a unique ID in the format "loXXX" (e.g., lo001, lo002).\n4. Define clear inputs and outputs with all required attributes.\n5. Specify system actions, DB constraints, UI controls, and mapping details.\n6. Include execution pathways and synthetic values for testing.\n7. Ensure all entity, attribute, and role references are consistent with the system\'s data model.\n8. Make sure LO definitions align with their parent Global Objective (GO) definitions.\n\n## Core LO Definition Structure\n\nEach LO definition should follow this comprehensive format', 'Inserted document into components']
2025-05-11 03:56:13,175 - test_prescriptive_parser - INFO - Successfully deployed lo_definitions
2025-05-11 03:56:13,175 - test_prescriptive_parser - INFO - Testing roles
2025-05-11 03:56:13,175 - test_prescriptive_parser - INFO - Reading prescriptive text from prompts/roles_prompt.txt
2025-05-11 03:56:13,175 - prescriptive_parser - INFO - Parsing roles prescriptive text
2025-05-11 03:56:13,175 - role_parser - INFO - Starting to parse role definitions
2025-05-11 03:56:13,175 - role_parser - WARNING - Invalid role header format: # Role Definition Prompt

You are a specialized AI assistant tasked with creating role definitions for a workflow system. Your goal is to generate well-structured, comprehensive role definitions that follow the standard template and best practices.

## Instructions

1. Create role definitions based on the provided information and requirements.
2. Follow the standard role definition template exactly.
3. Ensure each role has a unique ID in the format "roleXXX" (e.g., role001, role002).
4. Define clear permissions for Create, Read, Update operations on entities.
5. Specify Global Objective (GO) associations with appropriate role types.
6. Include scope, classification, and special conditions where applicable.
7. For inherited roles, clearly specify the parent role and only add the additional permissions.
8. Ensure all entity and attribute references are consistent with the system's data model.

## Standard Role Definition Template

```
2025-05-11 03:56:13,175 - role_parser - INFO - Parsing role: [RoleName] (ID: [role_id])
2025-05-11 03:56:13,175 - role_parser - INFO - Successfully parsed role '[RoleName]'
2025-05-11 03:56:13,175 - role_parser - WARNING - Invalid role header format: Role Details
2025-05-11 03:56:13,175 - role_parser - INFO - Parsing role: Employee (ID: emp001)
2025-05-11 03:56:13,175 - role_parser - INFO - Successfully parsed role 'Employee'
2025-05-11 03:56:13,175 - role_parser - INFO - Parsing role: Manager (ID: mgr001)
2025-05-11 03:56:13,175 - role_parser - INFO - Successfully parsed role 'Manager'
2025-05-11 03:56:13,175 - role_parser - INFO - Successfully parsed 3 roles
2025-05-11 03:56:13,175 - test_prescriptive_parser - WARNING - Parse warnings for roles: ['Invalid role header format: # Role Definition Prompt\n\nYou are a specialized AI assistant tasked with creating role definitions for a workflow system. Your goal is to generate well-structured, comprehensive role definitions that follow the standard template and best practices.\n\n## Instructions\n\n1. Create role definitions based on the provided information and requirements.\n2. Follow the standard role definition template exactly.\n3. Ensure each role has a unique ID in the format "roleXXX" (e.g., role001, role002).\n4. Define clear permissions for Create, Read, Update operations on entities.\n5. Specify Global Objective (GO) associations with appropriate role types.\n6. Include scope, classification, and special conditions where applicable.\n7. For inherited roles, clearly specify the parent role and only add the additional permissions.\n8. Ensure all entity and attribute references are consistent with the system\'s data model.\n\n## Standard Role Definition Template\n\n```', 'Invalid role header format: Role Details']
2025-05-11 03:56:13,175 - test_prescriptive_parser - INFO - Successfully parsed roles
2025-05-11 03:56:13,175 - component_deployer - INFO - Deploying roles from prescriptive text
2025-05-11 03:56:13,175 - prescriptive_parser - INFO - Initializing PrescriptiveParser
2025-05-11 03:56:13,175 - prescriptive_parser - INFO - Parsing roles prescriptive text
2025-05-11 03:56:13,176 - role_parser - INFO - Starting to parse role definitions
2025-05-11 03:56:13,176 - role_parser - WARNING - Invalid role header format: # Role Definition Prompt

You are a specialized AI assistant tasked with creating role definitions for a workflow system. Your goal is to generate well-structured, comprehensive role definitions that follow the standard template and best practices.

## Instructions

1. Create role definitions based on the provided information and requirements.
2. Follow the standard role definition template exactly.
3. Ensure each role has a unique ID in the format "roleXXX" (e.g., role001, role002).
4. Define clear permissions for Create, Read, Update operations on entities.
5. Specify Global Objective (GO) associations with appropriate role types.
6. Include scope, classification, and special conditions where applicable.
7. For inherited roles, clearly specify the parent role and only add the additional permissions.
8. Ensure all entity and attribute references are consistent with the system's data model.

## Standard Role Definition Template

```
2025-05-11 03:56:13,176 - role_parser - INFO - Parsing role: [RoleName] (ID: [role_id])
2025-05-11 03:56:13,176 - role_parser - INFO - Successfully parsed role '[RoleName]'
2025-05-11 03:56:13,176 - role_parser - WARNING - Invalid role header format: Role Details
2025-05-11 03:56:13,176 - role_parser - INFO - Parsing role: Employee (ID: emp001)
2025-05-11 03:56:13,176 - role_parser - INFO - Successfully parsed role 'Employee'
2025-05-11 03:56:13,176 - role_parser - INFO - Parsing role: Manager (ID: mgr001)
2025-05-11 03:56:13,176 - role_parser - INFO - Successfully parsed role 'Manager'
2025-05-11 03:56:13,176 - role_parser - INFO - Successfully parsed 3 roles
2025-05-11 03:56:13,177 - component_deployer - INFO - Deploying component of type: roles
2025-05-11 03:56:13,178 - component_deployer - INFO - Using schema: workflow_runtime
2025-05-11 03:56:13,183 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 03:56:13,189 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 03:56:13,193 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 03:56:13,194 - role_deployer - INFO - Deploying roles to workflow_runtime
2025-05-11 03:56:13,199 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 03:56:13,203 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 03:56:13,204 - db_utils - ERROR - Database error: column "permissions" of relation "roles" does not exist
LINE 3:                     role_id, name, description, permissions,...
                                                        ^
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 54, in execute_query
    cursor.execute(query, params)
psycopg2.errors.UndefinedColumn: column "permissions" of relation "roles" does not exist
LINE 3:                     role_id, name, description, permissions,...
                                                        ^

2025-05-11 03:56:13,204 - component_deployer - INFO - Component deployment failed
2025-05-11 03:56:13,204 - test_prescriptive_parser - INFO - Deploy messages for roles: ['Invalid role header format: # Role Definition Prompt\n\nYou are a specialized AI assistant tasked with creating role definitions for a workflow system. Your goal is to generate well-structured, comprehensive role definitions that follow the standard template and best practices.\n\n## Instructions\n\n1. Create role definitions based on the provided information and requirements.\n2. Follow the standard role definition template exactly.\n3. Ensure each role has a unique ID in the format "roleXXX" (e.g., role001, role002).\n4. Define clear permissions for Create, Read, Update operations on entities.\n5. Specify Global Objective (GO) associations with appropriate role types.\n6. Include scope, classification, and special conditions where applicable.\n7. For inherited roles, clearly specify the parent role and only add the additional permissions.\n8. Ensure all entity and attribute references are consistent with the system\'s data model.\n\n## Standard Role Definition Template\n\n```', 'Invalid role header format: Role Details', 'Database error: column "permissions" of relation "roles" does not exist\nLINE 3:                     role_id, name, description, permissions,...\n                                                        ^\n']
2025-05-11 03:56:13,204 - test_prescriptive_parser - ERROR - Failed to deploy roles
2025-05-11 03:56:13,204 - test_prescriptive_parser - ERROR - Individual test failed
2025-05-11 03:56:57,547 - test_prescriptive_parser - INFO - Testing parsing and deploying components individually
2025-05-11 03:56:57,547 - prescriptive_parser - INFO - Initializing PrescriptiveParser
2025-05-11 03:56:57,547 - component_deployer - INFO - ComponentDeployer initialized with use_temp_schema=False
2025-05-11 03:56:57,547 - test_prescriptive_parser - INFO - Testing entities
2025-05-11 03:56:57,547 - test_prescriptive_parser - INFO - Reading prescriptive text from prompts/entities_prompt.txt
2025-05-11 03:56:57,547 - prescriptive_parser - INFO - Parsing entities prescriptive text
2025-05-11 03:56:57,547 - entity_parser - INFO - Starting to parse entity definitions
2025-05-11 03:56:57,548 - entity_parser - WARNING - Invalid entity header format: # Entity Definition Prompt

You are a specialized AI assistant tasked with creating entity definitions for a workflow system. Your goal is to generate well-structured, comprehensive entity definitions that follow the standard template and best practices.

## Instructions

1. Create entity definitions based on the provided information and requirements.
2. Follow the standard entity definition template exactly.
3. Define attributes clearly, marking primary keys (^PK) and foreign keys (^FK).
4. Specify relationships between entities.
5. Include constants, validations, and business rules where applicable.
6. Define calculated/derived fields with clear formulas and dependencies.
7. Specify hierarchical dependencies if they exist.
8. Include data lifecycle management details where relevant.
9. Ensure all entity and attribute references are consistent with the system's data model.

## 1. Basic Entity Declaration
```
[EntityName] has [attribute1]^PK, [attribute2], [attribute3] (enum1, enum2), [attribute4]^FK, [attribute5].
```

## 2. Relationships
```
* [EntityName] has [relationship-type] relationship with [RelatedEntity] using [EntityName].[attributeX]^PK to [RelatedEntity].[attributeY]^FK
```

## 3. Constants & Configuration
```
* [EntityName].[attribute] [PROPERTY_NAME] = [value]
* [EntityName].[attribute] DEFAULT_VALUE = [value]
```

## 4. Validations
```
* [EntityName].[attribute] must be [constraint]
```

## 5. Business Rules
```
BusinessRule [ruleID] for [EntityName]
2025-05-11 03:56:57,548 - entity_parser - WARNING - Invalid entity header format: Entity Metadata
2025-05-11 03:56:57,548 - entity_parser - INFO - Successfully parsed 0 entities
2025-05-11 03:56:57,548 - test_prescriptive_parser - WARNING - Parse warnings for entities: ["Invalid entity header format: # Entity Definition Prompt\n\nYou are a specialized AI assistant tasked with creating entity definitions for a workflow system. Your goal is to generate well-structured, comprehensive entity definitions that follow the standard template and best practices.\n\n## Instructions\n\n1. Create entity definitions based on the provided information and requirements.\n2. Follow the standard entity definition template exactly.\n3. Define attributes clearly, marking primary keys (^PK) and foreign keys (^FK).\n4. Specify relationships between entities.\n5. Include constants, validations, and business rules where applicable.\n6. Define calculated/derived fields with clear formulas and dependencies.\n7. Specify hierarchical dependencies if they exist.\n8. Include data lifecycle management details where relevant.\n9. Ensure all entity and attribute references are consistent with the system's data model.\n\n## 1. Basic Entity Declaration\n```\n[EntityName] has [attribute1]^PK, [attribute2], [attribute3] (enum1, enum2), [attribute4]^FK, [attribute5].\n```\n\n## 2. Relationships\n```\n* [EntityName] has [relationship-type] relationship with [RelatedEntity] using [EntityName].[attributeX]^PK to [RelatedEntity].[attributeY]^FK\n```\n\n## 3. Constants & Configuration\n```\n* [EntityName].[attribute] [PROPERTY_NAME] = [value]\n* [EntityName].[attribute] DEFAULT_VALUE = [value]\n```\n\n## 4. Validations\n```\n* [EntityName].[attribute] must be [constraint]\n```\n\n## 5. Business Rules\n```\nBusinessRule [ruleID] for [EntityName]", 'Invalid entity header format: Entity Metadata']
2025-05-11 03:56:57,548 - test_prescriptive_parser - INFO - Successfully parsed entities
2025-05-11 03:56:57,548 - component_deployer - INFO - Deploying entities from prescriptive text
2025-05-11 03:56:57,548 - prescriptive_parser - INFO - Initializing PrescriptiveParser
2025-05-11 03:56:57,548 - prescriptive_parser - INFO - Parsing entities prescriptive text
2025-05-11 03:56:57,548 - entity_parser - INFO - Starting to parse entity definitions
2025-05-11 03:56:57,548 - entity_parser - WARNING - Invalid entity header format: # Entity Definition Prompt

You are a specialized AI assistant tasked with creating entity definitions for a workflow system. Your goal is to generate well-structured, comprehensive entity definitions that follow the standard template and best practices.

## Instructions

1. Create entity definitions based on the provided information and requirements.
2. Follow the standard entity definition template exactly.
3. Define attributes clearly, marking primary keys (^PK) and foreign keys (^FK).
4. Specify relationships between entities.
5. Include constants, validations, and business rules where applicable.
6. Define calculated/derived fields with clear formulas and dependencies.
7. Specify hierarchical dependencies if they exist.
8. Include data lifecycle management details where relevant.
9. Ensure all entity and attribute references are consistent with the system's data model.

## 1. Basic Entity Declaration
```
[EntityName] has [attribute1]^PK, [attribute2], [attribute3] (enum1, enum2), [attribute4]^FK, [attribute5].
```

## 2. Relationships
```
* [EntityName] has [relationship-type] relationship with [RelatedEntity] using [EntityName].[attributeX]^PK to [RelatedEntity].[attributeY]^FK
```

## 3. Constants & Configuration
```
* [EntityName].[attribute] [PROPERTY_NAME] = [value]
* [EntityName].[attribute] DEFAULT_VALUE = [value]
```

## 4. Validations
```
* [EntityName].[attribute] must be [constraint]
```

## 5. Business Rules
```
BusinessRule [ruleID] for [EntityName]
2025-05-11 03:56:57,548 - entity_parser - WARNING - Invalid entity header format: Entity Metadata
2025-05-11 03:56:57,548 - entity_parser - INFO - Successfully parsed 0 entities
2025-05-11 03:56:57,548 - component_deployer - INFO - Deploying component of type: entities
2025-05-11 03:56:57,548 - component_deployer - INFO - Using schema: workflow_runtime
2025-05-11 03:56:57,548 - entity_deployer - INFO - Deploying entities to workflow_runtime
2025-05-11 03:56:57,562 - entity_deployer - INFO - Entity deployment completed successfully
2025-05-11 03:56:57,562 - component_deployer - INFO - Component deployment succeeded
2025-05-11 03:56:57,562 - test_prescriptive_parser - INFO - Deploy messages for entities: ["Invalid entity header format: # Entity Definition Prompt\n\nYou are a specialized AI assistant tasked with creating entity definitions for a workflow system. Your goal is to generate well-structured, comprehensive entity definitions that follow the standard template and best practices.\n\n## Instructions\n\n1. Create entity definitions based on the provided information and requirements.\n2. Follow the standard entity definition template exactly.\n3. Define attributes clearly, marking primary keys (^PK) and foreign keys (^FK).\n4. Specify relationships between entities.\n5. Include constants, validations, and business rules where applicable.\n6. Define calculated/derived fields with clear formulas and dependencies.\n7. Specify hierarchical dependencies if they exist.\n8. Include data lifecycle management details where relevant.\n9. Ensure all entity and attribute references are consistent with the system's data model.\n\n## 1. Basic Entity Declaration\n```\n[EntityName] has [attribute1]^PK, [attribute2], [attribute3] (enum1, enum2), [attribute4]^FK, [attribute5].\n```\n\n## 2. Relationships\n```\n* [EntityName] has [relationship-type] relationship with [RelatedEntity] using [EntityName].[attributeX]^PK to [RelatedEntity].[attributeY]^FK\n```\n\n## 3. Constants & Configuration\n```\n* [EntityName].[attribute] [PROPERTY_NAME] = [value]\n* [EntityName].[attribute] DEFAULT_VALUE = [value]\n```\n\n## 4. Validations\n```\n* [EntityName].[attribute] must be [constraint]\n```\n\n## 5. Business Rules\n```\nBusinessRule [ruleID] for [EntityName]", 'Invalid entity header format: Entity Metadata', 'Inserted document into components']
2025-05-11 03:56:57,562 - test_prescriptive_parser - INFO - Successfully deployed entities
2025-05-11 03:56:57,562 - test_prescriptive_parser - INFO - Testing go_definitions
2025-05-11 03:56:57,562 - test_prescriptive_parser - INFO - Reading prescriptive text from prompts/go_definitions_prompt.txt
2025-05-11 03:56:57,563 - prescriptive_parser - INFO - Parsing go_definitions prescriptive text
2025-05-11 03:56:57,563 - go_parser - INFO - Starting to parse GO definitions
2025-05-11 03:56:57,563 - go_parser - WARNING - Invalid GO header format: # Global Objective (GO) Definition Prompt

You are a specialized AI assistant tasked with creating Global Objective (GO) definitions for a workflow system. Your goal is to generate well-structured, comprehensive GO definitions that follow the standard template and best practices.

## Instructions

1. Create GO definitions based on the provided information and requirements.
2. Follow the standard GO definition template exactly.
3. Ensure each GO has a unique ID in the format "goXXX" (e.g., go001, go002).
4. Define clear input and output stacks with explicit mappings.
5. Include a comprehensive process flow with all Local Objectives (LOs).
6. Specify business rules, integration points, and performance metadata.
7. Include process mining schema details for analytics.
8. Ensure all entity, attribute, and LO references are consistent with the system's data model.

## Critical Design Rules

### 1. LO Enumeration Requirements
- **EVERY LO must have an explicit ID** in the format "lo001", "lo002", etc.
- **EVERY LO referenced anywhere must be defined** in the Process Flow section
- **ALL routing references must use LO IDs**, not just names
- Maintain sequential numbering for all LOs
- Include ALL LOs, even system-triggered or rollback LOs

### 2. First LO Requirements
- The first LO in any workflow **MUST be human-triggered** unless explicitly marked as system-triggered
- System-triggered workflows are permitted ONLY when initiated by
2025-05-11 03:56:57,563 - go_parser - INFO - Successfully parsed 0 global objectives
2025-05-11 03:56:57,563 - test_prescriptive_parser - WARNING - Parse warnings for go_definitions: ['Invalid GO header format: # Global Objective (GO) Definition Prompt\n\nYou are a specialized AI assistant tasked with creating Global Objective (GO) definitions for a workflow system. Your goal is to generate well-structured, comprehensive GO definitions that follow the standard template and best practices.\n\n## Instructions\n\n1. Create GO definitions based on the provided information and requirements.\n2. Follow the standard GO definition template exactly.\n3. Ensure each GO has a unique ID in the format "goXXX" (e.g., go001, go002).\n4. Define clear input and output stacks with explicit mappings.\n5. Include a comprehensive process flow with all Local Objectives (LOs).\n6. Specify business rules, integration points, and performance metadata.\n7. Include process mining schema details for analytics.\n8. Ensure all entity, attribute, and LO references are consistent with the system\'s data model.\n\n## Critical Design Rules\n\n### 1. LO Enumeration Requirements\n- **EVERY LO must have an explicit ID** in the format "lo001", "lo002", etc.\n- **EVERY LO referenced anywhere must be defined** in the Process Flow section\n- **ALL routing references must use LO IDs**, not just names\n- Maintain sequential numbering for all LOs\n- Include ALL LOs, even system-triggered or rollback LOs\n\n### 2. First LO Requirements\n- The first LO in any workflow **MUST be human-triggered** unless explicitly marked as system-triggered\n- System-triggered workflows are permitted ONLY when initiated by']
2025-05-11 03:56:57,563 - test_prescriptive_parser - INFO - Successfully parsed go_definitions
2025-05-11 03:56:57,563 - component_deployer - INFO - Deploying go_definitions from prescriptive text
2025-05-11 03:56:57,563 - prescriptive_parser - INFO - Initializing PrescriptiveParser
2025-05-11 03:56:57,563 - prescriptive_parser - INFO - Parsing go_definitions prescriptive text
2025-05-11 03:56:57,563 - go_parser - INFO - Starting to parse GO definitions
2025-05-11 03:56:57,563 - go_parser - WARNING - Invalid GO header format: # Global Objective (GO) Definition Prompt

You are a specialized AI assistant tasked with creating Global Objective (GO) definitions for a workflow system. Your goal is to generate well-structured, comprehensive GO definitions that follow the standard template and best practices.

## Instructions

1. Create GO definitions based on the provided information and requirements.
2. Follow the standard GO definition template exactly.
3. Ensure each GO has a unique ID in the format "goXXX" (e.g., go001, go002).
4. Define clear input and output stacks with explicit mappings.
5. Include a comprehensive process flow with all Local Objectives (LOs).
6. Specify business rules, integration points, and performance metadata.
7. Include process mining schema details for analytics.
8. Ensure all entity, attribute, and LO references are consistent with the system's data model.

## Critical Design Rules

### 1. LO Enumeration Requirements
- **EVERY LO must have an explicit ID** in the format "lo001", "lo002", etc.
- **EVERY LO referenced anywhere must be defined** in the Process Flow section
- **ALL routing references must use LO IDs**, not just names
- Maintain sequential numbering for all LOs
- Include ALL LOs, even system-triggered or rollback LOs

### 2. First LO Requirements
- The first LO in any workflow **MUST be human-triggered** unless explicitly marked as system-triggered
- System-triggered workflows are permitted ONLY when initiated by
2025-05-11 03:56:57,563 - go_parser - INFO - Successfully parsed 0 global objectives
2025-05-11 03:56:57,563 - component_deployer - INFO - Deploying component of type: go_definitions
2025-05-11 03:56:57,563 - component_deployer - INFO - Using schema: workflow_runtime
2025-05-11 03:56:57,569 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 03:56:57,570 - go_deployer - INFO - Deploying GO definitions to workflow_runtime
2025-05-11 03:56:57,578 - go_deployer - INFO - GO deployment completed successfully
2025-05-11 03:56:57,578 - component_deployer - INFO - Component deployment succeeded
2025-05-11 03:56:57,578 - test_prescriptive_parser - INFO - Deploy messages for go_definitions: ['Invalid GO header format: # Global Objective (GO) Definition Prompt\n\nYou are a specialized AI assistant tasked with creating Global Objective (GO) definitions for a workflow system. Your goal is to generate well-structured, comprehensive GO definitions that follow the standard template and best practices.\n\n## Instructions\n\n1. Create GO definitions based on the provided information and requirements.\n2. Follow the standard GO definition template exactly.\n3. Ensure each GO has a unique ID in the format "goXXX" (e.g., go001, go002).\n4. Define clear input and output stacks with explicit mappings.\n5. Include a comprehensive process flow with all Local Objectives (LOs).\n6. Specify business rules, integration points, and performance metadata.\n7. Include process mining schema details for analytics.\n8. Ensure all entity, attribute, and LO references are consistent with the system\'s data model.\n\n## Critical Design Rules\n\n### 1. LO Enumeration Requirements\n- **EVERY LO must have an explicit ID** in the format "lo001", "lo002", etc.\n- **EVERY LO referenced anywhere must be defined** in the Process Flow section\n- **ALL routing references must use LO IDs**, not just names\n- Maintain sequential numbering for all LOs\n- Include ALL LOs, even system-triggered or rollback LOs\n\n### 2. First LO Requirements\n- The first LO in any workflow **MUST be human-triggered** unless explicitly marked as system-triggered\n- System-triggered workflows are permitted ONLY when initiated by', 'Inserted document into components']
2025-05-11 03:56:57,578 - test_prescriptive_parser - INFO - Successfully deployed go_definitions
2025-05-11 03:56:57,578 - test_prescriptive_parser - INFO - Testing lo_definitions
2025-05-11 03:56:57,578 - test_prescriptive_parser - INFO - Reading prescriptive text from prompts/lo_definitions_prompt.txt
2025-05-11 03:56:57,578 - prescriptive_parser - INFO - Parsing lo_definitions prescriptive text
2025-05-11 03:56:57,578 - lo_parser - INFO - Starting to parse LO definitions
2025-05-11 03:56:57,579 - lo_parser - WARNING - Invalid LO header format: # Local Objective (LO) Definition Prompt

You are a specialized AI assistant tasked with creating Local Objective (LO) definitions for a workflow system. Your goal is to generate well-structured, comprehensive LO definitions that follow the standard template and best practices.

## Instructions

1. Create LO definitions based on the provided information and requirements.
2. Follow the standard LO definition template exactly.
3. Ensure each LO has a unique ID in the format "loXXX" (e.g., lo001, lo002).
4. Define clear inputs and outputs with all required attributes.
5. Specify system actions, DB constraints, UI controls, and mapping details.
6. Include execution pathways and synthetic values for testing.
7. Ensure all entity, attribute, and role references are consistent with the system's data model.
8. Make sure LO definitions align with their parent Global Objective (GO) definitions.

## Core LO Definition Structure

Each LO definition should follow this comprehensive format
2025-05-11 03:56:57,579 - lo_parser - INFO - Successfully parsed 0 local objectives
2025-05-11 03:56:57,579 - test_prescriptive_parser - WARNING - Parse warnings for lo_definitions: ['Invalid LO header format: # Local Objective (LO) Definition Prompt\n\nYou are a specialized AI assistant tasked with creating Local Objective (LO) definitions for a workflow system. Your goal is to generate well-structured, comprehensive LO definitions that follow the standard template and best practices.\n\n## Instructions\n\n1. Create LO definitions based on the provided information and requirements.\n2. Follow the standard LO definition template exactly.\n3. Ensure each LO has a unique ID in the format "loXXX" (e.g., lo001, lo002).\n4. Define clear inputs and outputs with all required attributes.\n5. Specify system actions, DB constraints, UI controls, and mapping details.\n6. Include execution pathways and synthetic values for testing.\n7. Ensure all entity, attribute, and role references are consistent with the system\'s data model.\n8. Make sure LO definitions align with their parent Global Objective (GO) definitions.\n\n## Core LO Definition Structure\n\nEach LO definition should follow this comprehensive format']
2025-05-11 03:56:57,579 - test_prescriptive_parser - INFO - Successfully parsed lo_definitions
2025-05-11 03:56:57,579 - component_deployer - INFO - Deploying lo_definitions from prescriptive text
2025-05-11 03:56:57,579 - prescriptive_parser - INFO - Initializing PrescriptiveParser
2025-05-11 03:56:57,579 - prescriptive_parser - INFO - Parsing lo_definitions prescriptive text
2025-05-11 03:56:57,579 - lo_parser - INFO - Starting to parse LO definitions
2025-05-11 03:56:57,579 - lo_parser - WARNING - Invalid LO header format: # Local Objective (LO) Definition Prompt

You are a specialized AI assistant tasked with creating Local Objective (LO) definitions for a workflow system. Your goal is to generate well-structured, comprehensive LO definitions that follow the standard template and best practices.

## Instructions

1. Create LO definitions based on the provided information and requirements.
2. Follow the standard LO definition template exactly.
3. Ensure each LO has a unique ID in the format "loXXX" (e.g., lo001, lo002).
4. Define clear inputs and outputs with all required attributes.
5. Specify system actions, DB constraints, UI controls, and mapping details.
6. Include execution pathways and synthetic values for testing.
7. Ensure all entity, attribute, and role references are consistent with the system's data model.
8. Make sure LO definitions align with their parent Global Objective (GO) definitions.

## Core LO Definition Structure

Each LO definition should follow this comprehensive format
2025-05-11 03:56:57,579 - lo_parser - INFO - Successfully parsed 0 local objectives
2025-05-11 03:56:57,579 - component_deployer - INFO - Deploying component of type: lo_definitions
2025-05-11 03:56:57,579 - component_deployer - INFO - Using schema: workflow_runtime
2025-05-11 03:56:57,585 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 03:56:57,590 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 03:56:57,592 - lo_deployer - INFO - Deploying LO definitions to workflow_runtime
2025-05-11 03:56:57,599 - lo_deployer - INFO - LO deployment completed successfully
2025-05-11 03:56:57,599 - component_deployer - INFO - Component deployment succeeded
2025-05-11 03:56:57,599 - test_prescriptive_parser - INFO - Deploy messages for lo_definitions: ['Invalid LO header format: # Local Objective (LO) Definition Prompt\n\nYou are a specialized AI assistant tasked with creating Local Objective (LO) definitions for a workflow system. Your goal is to generate well-structured, comprehensive LO definitions that follow the standard template and best practices.\n\n## Instructions\n\n1. Create LO definitions based on the provided information and requirements.\n2. Follow the standard LO definition template exactly.\n3. Ensure each LO has a unique ID in the format "loXXX" (e.g., lo001, lo002).\n4. Define clear inputs and outputs with all required attributes.\n5. Specify system actions, DB constraints, UI controls, and mapping details.\n6. Include execution pathways and synthetic values for testing.\n7. Ensure all entity, attribute, and role references are consistent with the system\'s data model.\n8. Make sure LO definitions align with their parent Global Objective (GO) definitions.\n\n## Core LO Definition Structure\n\nEach LO definition should follow this comprehensive format', 'Inserted document into components']
2025-05-11 03:56:57,599 - test_prescriptive_parser - INFO - Successfully deployed lo_definitions
2025-05-11 03:56:57,599 - test_prescriptive_parser - INFO - Testing roles
2025-05-11 03:56:57,599 - test_prescriptive_parser - INFO - Reading prescriptive text from prompts/roles_prompt.txt
2025-05-11 03:56:57,599 - prescriptive_parser - INFO - Parsing roles prescriptive text
2025-05-11 03:56:57,599 - role_parser - INFO - Starting to parse role definitions
2025-05-11 03:56:57,599 - role_parser - WARNING - Invalid role header format: # Role Definition Prompt

You are a specialized AI assistant tasked with creating role definitions for a workflow system. Your goal is to generate well-structured, comprehensive role definitions that follow the standard template and best practices.

## Instructions

1. Create role definitions based on the provided information and requirements.
2. Follow the standard role definition template exactly.
3. Ensure each role has a unique ID in the format "roleXXX" (e.g., role001, role002).
4. Define clear permissions for Create, Read, Update operations on entities.
5. Specify Global Objective (GO) associations with appropriate role types.
6. Include scope, classification, and special conditions where applicable.
7. For inherited roles, clearly specify the parent role and only add the additional permissions.
8. Ensure all entity and attribute references are consistent with the system's data model.

## Standard Role Definition Template

```
2025-05-11 03:56:57,599 - role_parser - INFO - Parsing role: [RoleName] (ID: [role_id])
2025-05-11 03:56:57,600 - role_parser - INFO - Successfully parsed role '[RoleName]'
2025-05-11 03:56:57,600 - role_parser - WARNING - Invalid role header format: Role Details
2025-05-11 03:56:57,600 - role_parser - INFO - Parsing role: Employee (ID: emp001)
2025-05-11 03:56:57,600 - role_parser - INFO - Successfully parsed role 'Employee'
2025-05-11 03:56:57,600 - role_parser - INFO - Parsing role: Manager (ID: mgr001)
2025-05-11 03:56:57,600 - role_parser - INFO - Successfully parsed role 'Manager'
2025-05-11 03:56:57,600 - role_parser - INFO - Successfully parsed 3 roles
2025-05-11 03:56:57,600 - test_prescriptive_parser - WARNING - Parse warnings for roles: ['Invalid role header format: # Role Definition Prompt\n\nYou are a specialized AI assistant tasked with creating role definitions for a workflow system. Your goal is to generate well-structured, comprehensive role definitions that follow the standard template and best practices.\n\n## Instructions\n\n1. Create role definitions based on the provided information and requirements.\n2. Follow the standard role definition template exactly.\n3. Ensure each role has a unique ID in the format "roleXXX" (e.g., role001, role002).\n4. Define clear permissions for Create, Read, Update operations on entities.\n5. Specify Global Objective (GO) associations with appropriate role types.\n6. Include scope, classification, and special conditions where applicable.\n7. For inherited roles, clearly specify the parent role and only add the additional permissions.\n8. Ensure all entity and attribute references are consistent with the system\'s data model.\n\n## Standard Role Definition Template\n\n```', 'Invalid role header format: Role Details']
2025-05-11 03:56:57,600 - test_prescriptive_parser - INFO - Successfully parsed roles
2025-05-11 03:56:57,600 - component_deployer - INFO - Deploying roles from prescriptive text
2025-05-11 03:56:57,600 - prescriptive_parser - INFO - Initializing PrescriptiveParser
2025-05-11 03:56:57,600 - prescriptive_parser - INFO - Parsing roles prescriptive text
2025-05-11 03:56:57,600 - role_parser - INFO - Starting to parse role definitions
2025-05-11 03:56:57,600 - role_parser - WARNING - Invalid role header format: # Role Definition Prompt

You are a specialized AI assistant tasked with creating role definitions for a workflow system. Your goal is to generate well-structured, comprehensive role definitions that follow the standard template and best practices.

## Instructions

1. Create role definitions based on the provided information and requirements.
2. Follow the standard role definition template exactly.
3. Ensure each role has a unique ID in the format "roleXXX" (e.g., role001, role002).
4. Define clear permissions for Create, Read, Update operations on entities.
5. Specify Global Objective (GO) associations with appropriate role types.
6. Include scope, classification, and special conditions where applicable.
7. For inherited roles, clearly specify the parent role and only add the additional permissions.
8. Ensure all entity and attribute references are consistent with the system's data model.

## Standard Role Definition Template

```
2025-05-11 03:56:57,600 - role_parser - INFO - Parsing role: [RoleName] (ID: [role_id])
2025-05-11 03:56:57,600 - role_parser - INFO - Successfully parsed role '[RoleName]'
2025-05-11 03:56:57,600 - role_parser - WARNING - Invalid role header format: Role Details
2025-05-11 03:56:57,600 - role_parser - INFO - Parsing role: Employee (ID: emp001)
2025-05-11 03:56:57,600 - role_parser - INFO - Successfully parsed role 'Employee'
2025-05-11 03:56:57,600 - role_parser - INFO - Parsing role: Manager (ID: mgr001)
2025-05-11 03:56:57,600 - role_parser - INFO - Successfully parsed role 'Manager'
2025-05-11 03:56:57,600 - role_parser - INFO - Successfully parsed 3 roles
2025-05-11 03:56:57,601 - component_deployer - INFO - Deploying component of type: roles
2025-05-11 03:56:57,603 - component_deployer - INFO - Using schema: workflow_runtime
2025-05-11 03:56:57,606 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 03:56:57,612 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 03:56:57,616 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 03:56:57,617 - role_deployer - INFO - Deploying roles to workflow_runtime
2025-05-11 03:56:57,621 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 03:56:57,625 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 03:56:57,626 - db_utils - ERROR - Database error: column "version_type" of relation "roles" does not exist
LINE 3: ...sions, scope, classification, special_conditions, version_ty...
                                                             ^
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 54, in execute_query
    cursor.execute(query, params)
psycopg2.errors.UndefinedColumn: column "version_type" of relation "roles" does not exist
LINE 3: ...sions, scope, classification, special_conditions, version_ty...
                                                             ^

2025-05-11 03:56:57,626 - component_deployer - INFO - Component deployment failed
2025-05-11 03:56:57,626 - test_prescriptive_parser - INFO - Deploy messages for roles: ['Invalid role header format: # Role Definition Prompt\n\nYou are a specialized AI assistant tasked with creating role definitions for a workflow system. Your goal is to generate well-structured, comprehensive role definitions that follow the standard template and best practices.\n\n## Instructions\n\n1. Create role definitions based on the provided information and requirements.\n2. Follow the standard role definition template exactly.\n3. Ensure each role has a unique ID in the format "roleXXX" (e.g., role001, role002).\n4. Define clear permissions for Create, Read, Update operations on entities.\n5. Specify Global Objective (GO) associations with appropriate role types.\n6. Include scope, classification, and special conditions where applicable.\n7. For inherited roles, clearly specify the parent role and only add the additional permissions.\n8. Ensure all entity and attribute references are consistent with the system\'s data model.\n\n## Standard Role Definition Template\n\n```', 'Invalid role header format: Role Details', 'Database error: column "version_type" of relation "roles" does not exist\nLINE 3: ...sions, scope, classification, special_conditions, version_ty...\n                                                             ^\n']
2025-05-11 03:56:57,627 - test_prescriptive_parser - ERROR - Failed to deploy roles
2025-05-11 03:56:57,627 - test_prescriptive_parser - ERROR - Individual test failed
2025-05-11 03:57:26,557 - test_prescriptive_parser - INFO - Testing parsing and deploying components individually
2025-05-11 03:57:26,558 - prescriptive_parser - INFO - Initializing PrescriptiveParser
2025-05-11 03:57:26,558 - component_deployer - INFO - ComponentDeployer initialized with use_temp_schema=False
2025-05-11 03:57:26,558 - test_prescriptive_parser - INFO - Testing entities
2025-05-11 03:57:26,558 - test_prescriptive_parser - INFO - Reading prescriptive text from prompts/entities_prompt.txt
2025-05-11 03:57:26,558 - prescriptive_parser - INFO - Parsing entities prescriptive text
2025-05-11 03:57:26,558 - entity_parser - INFO - Starting to parse entity definitions
2025-05-11 03:57:26,558 - entity_parser - WARNING - Invalid entity header format: # Entity Definition Prompt

You are a specialized AI assistant tasked with creating entity definitions for a workflow system. Your goal is to generate well-structured, comprehensive entity definitions that follow the standard template and best practices.

## Instructions

1. Create entity definitions based on the provided information and requirements.
2. Follow the standard entity definition template exactly.
3. Define attributes clearly, marking primary keys (^PK) and foreign keys (^FK).
4. Specify relationships between entities.
5. Include constants, validations, and business rules where applicable.
6. Define calculated/derived fields with clear formulas and dependencies.
7. Specify hierarchical dependencies if they exist.
8. Include data lifecycle management details where relevant.
9. Ensure all entity and attribute references are consistent with the system's data model.

## 1. Basic Entity Declaration
```
[EntityName] has [attribute1]^PK, [attribute2], [attribute3] (enum1, enum2), [attribute4]^FK, [attribute5].
```

## 2. Relationships
```
* [EntityName] has [relationship-type] relationship with [RelatedEntity] using [EntityName].[attributeX]^PK to [RelatedEntity].[attributeY]^FK
```

## 3. Constants & Configuration
```
* [EntityName].[attribute] [PROPERTY_NAME] = [value]
* [EntityName].[attribute] DEFAULT_VALUE = [value]
```

## 4. Validations
```
* [EntityName].[attribute] must be [constraint]
```

## 5. Business Rules
```
BusinessRule [ruleID] for [EntityName]
2025-05-11 03:57:26,558 - entity_parser - WARNING - Invalid entity header format: Entity Metadata
2025-05-11 03:57:26,558 - entity_parser - INFO - Successfully parsed 0 entities
2025-05-11 03:57:26,558 - test_prescriptive_parser - WARNING - Parse warnings for entities: ["Invalid entity header format: # Entity Definition Prompt\n\nYou are a specialized AI assistant tasked with creating entity definitions for a workflow system. Your goal is to generate well-structured, comprehensive entity definitions that follow the standard template and best practices.\n\n## Instructions\n\n1. Create entity definitions based on the provided information and requirements.\n2. Follow the standard entity definition template exactly.\n3. Define attributes clearly, marking primary keys (^PK) and foreign keys (^FK).\n4. Specify relationships between entities.\n5. Include constants, validations, and business rules where applicable.\n6. Define calculated/derived fields with clear formulas and dependencies.\n7. Specify hierarchical dependencies if they exist.\n8. Include data lifecycle management details where relevant.\n9. Ensure all entity and attribute references are consistent with the system's data model.\n\n## 1. Basic Entity Declaration\n```\n[EntityName] has [attribute1]^PK, [attribute2], [attribute3] (enum1, enum2), [attribute4]^FK, [attribute5].\n```\n\n## 2. Relationships\n```\n* [EntityName] has [relationship-type] relationship with [RelatedEntity] using [EntityName].[attributeX]^PK to [RelatedEntity].[attributeY]^FK\n```\n\n## 3. Constants & Configuration\n```\n* [EntityName].[attribute] [PROPERTY_NAME] = [value]\n* [EntityName].[attribute] DEFAULT_VALUE = [value]\n```\n\n## 4. Validations\n```\n* [EntityName].[attribute] must be [constraint]\n```\n\n## 5. Business Rules\n```\nBusinessRule [ruleID] for [EntityName]", 'Invalid entity header format: Entity Metadata']
2025-05-11 03:57:26,558 - test_prescriptive_parser - INFO - Successfully parsed entities
2025-05-11 03:57:26,558 - component_deployer - INFO - Deploying entities from prescriptive text
2025-05-11 03:57:26,558 - prescriptive_parser - INFO - Initializing PrescriptiveParser
2025-05-11 03:57:26,558 - prescriptive_parser - INFO - Parsing entities prescriptive text
2025-05-11 03:57:26,558 - entity_parser - INFO - Starting to parse entity definitions
2025-05-11 03:57:26,558 - entity_parser - WARNING - Invalid entity header format: # Entity Definition Prompt

You are a specialized AI assistant tasked with creating entity definitions for a workflow system. Your goal is to generate well-structured, comprehensive entity definitions that follow the standard template and best practices.

## Instructions

1. Create entity definitions based on the provided information and requirements.
2. Follow the standard entity definition template exactly.
3. Define attributes clearly, marking primary keys (^PK) and foreign keys (^FK).
4. Specify relationships between entities.
5. Include constants, validations, and business rules where applicable.
6. Define calculated/derived fields with clear formulas and dependencies.
7. Specify hierarchical dependencies if they exist.
8. Include data lifecycle management details where relevant.
9. Ensure all entity and attribute references are consistent with the system's data model.

## 1. Basic Entity Declaration
```
[EntityName] has [attribute1]^PK, [attribute2], [attribute3] (enum1, enum2), [attribute4]^FK, [attribute5].
```

## 2. Relationships
```
* [EntityName] has [relationship-type] relationship with [RelatedEntity] using [EntityName].[attributeX]^PK to [RelatedEntity].[attributeY]^FK
```

## 3. Constants & Configuration
```
* [EntityName].[attribute] [PROPERTY_NAME] = [value]
* [EntityName].[attribute] DEFAULT_VALUE = [value]
```

## 4. Validations
```
* [EntityName].[attribute] must be [constraint]
```

## 5. Business Rules
```
BusinessRule [ruleID] for [EntityName]
2025-05-11 03:57:26,558 - entity_parser - WARNING - Invalid entity header format: Entity Metadata
2025-05-11 03:57:26,558 - entity_parser - INFO - Successfully parsed 0 entities
2025-05-11 03:57:26,558 - component_deployer - INFO - Deploying component of type: entities
2025-05-11 03:57:26,559 - component_deployer - INFO - Using schema: workflow_runtime
2025-05-11 03:57:26,559 - entity_deployer - INFO - Deploying entities to workflow_runtime
2025-05-11 03:57:26,573 - entity_deployer - INFO - Entity deployment completed successfully
2025-05-11 03:57:26,573 - component_deployer - INFO - Component deployment succeeded
2025-05-11 03:57:26,573 - test_prescriptive_parser - INFO - Deploy messages for entities: ["Invalid entity header format: # Entity Definition Prompt\n\nYou are a specialized AI assistant tasked with creating entity definitions for a workflow system. Your goal is to generate well-structured, comprehensive entity definitions that follow the standard template and best practices.\n\n## Instructions\n\n1. Create entity definitions based on the provided information and requirements.\n2. Follow the standard entity definition template exactly.\n3. Define attributes clearly, marking primary keys (^PK) and foreign keys (^FK).\n4. Specify relationships between entities.\n5. Include constants, validations, and business rules where applicable.\n6. Define calculated/derived fields with clear formulas and dependencies.\n7. Specify hierarchical dependencies if they exist.\n8. Include data lifecycle management details where relevant.\n9. Ensure all entity and attribute references are consistent with the system's data model.\n\n## 1. Basic Entity Declaration\n```\n[EntityName] has [attribute1]^PK, [attribute2], [attribute3] (enum1, enum2), [attribute4]^FK, [attribute5].\n```\n\n## 2. Relationships\n```\n* [EntityName] has [relationship-type] relationship with [RelatedEntity] using [EntityName].[attributeX]^PK to [RelatedEntity].[attributeY]^FK\n```\n\n## 3. Constants & Configuration\n```\n* [EntityName].[attribute] [PROPERTY_NAME] = [value]\n* [EntityName].[attribute] DEFAULT_VALUE = [value]\n```\n\n## 4. Validations\n```\n* [EntityName].[attribute] must be [constraint]\n```\n\n## 5. Business Rules\n```\nBusinessRule [ruleID] for [EntityName]", 'Invalid entity header format: Entity Metadata', 'Inserted document into components']
2025-05-11 03:57:26,573 - test_prescriptive_parser - INFO - Successfully deployed entities
2025-05-11 03:57:26,573 - test_prescriptive_parser - INFO - Testing go_definitions
2025-05-11 03:57:26,573 - test_prescriptive_parser - INFO - Reading prescriptive text from prompts/go_definitions_prompt.txt
2025-05-11 03:57:26,573 - prescriptive_parser - INFO - Parsing go_definitions prescriptive text
2025-05-11 03:57:26,573 - go_parser - INFO - Starting to parse GO definitions
2025-05-11 03:57:26,573 - go_parser - WARNING - Invalid GO header format: # Global Objective (GO) Definition Prompt

You are a specialized AI assistant tasked with creating Global Objective (GO) definitions for a workflow system. Your goal is to generate well-structured, comprehensive GO definitions that follow the standard template and best practices.

## Instructions

1. Create GO definitions based on the provided information and requirements.
2. Follow the standard GO definition template exactly.
3. Ensure each GO has a unique ID in the format "goXXX" (e.g., go001, go002).
4. Define clear input and output stacks with explicit mappings.
5. Include a comprehensive process flow with all Local Objectives (LOs).
6. Specify business rules, integration points, and performance metadata.
7. Include process mining schema details for analytics.
8. Ensure all entity, attribute, and LO references are consistent with the system's data model.

## Critical Design Rules

### 1. LO Enumeration Requirements
- **EVERY LO must have an explicit ID** in the format "lo001", "lo002", etc.
- **EVERY LO referenced anywhere must be defined** in the Process Flow section
- **ALL routing references must use LO IDs**, not just names
- Maintain sequential numbering for all LOs
- Include ALL LOs, even system-triggered or rollback LOs

### 2. First LO Requirements
- The first LO in any workflow **MUST be human-triggered** unless explicitly marked as system-triggered
- System-triggered workflows are permitted ONLY when initiated by
2025-05-11 03:57:26,573 - go_parser - INFO - Successfully parsed 0 global objectives
2025-05-11 03:57:26,573 - test_prescriptive_parser - WARNING - Parse warnings for go_definitions: ['Invalid GO header format: # Global Objective (GO) Definition Prompt\n\nYou are a specialized AI assistant tasked with creating Global Objective (GO) definitions for a workflow system. Your goal is to generate well-structured, comprehensive GO definitions that follow the standard template and best practices.\n\n## Instructions\n\n1. Create GO definitions based on the provided information and requirements.\n2. Follow the standard GO definition template exactly.\n3. Ensure each GO has a unique ID in the format "goXXX" (e.g., go001, go002).\n4. Define clear input and output stacks with explicit mappings.\n5. Include a comprehensive process flow with all Local Objectives (LOs).\n6. Specify business rules, integration points, and performance metadata.\n7. Include process mining schema details for analytics.\n8. Ensure all entity, attribute, and LO references are consistent with the system\'s data model.\n\n## Critical Design Rules\n\n### 1. LO Enumeration Requirements\n- **EVERY LO must have an explicit ID** in the format "lo001", "lo002", etc.\n- **EVERY LO referenced anywhere must be defined** in the Process Flow section\n- **ALL routing references must use LO IDs**, not just names\n- Maintain sequential numbering for all LOs\n- Include ALL LOs, even system-triggered or rollback LOs\n\n### 2. First LO Requirements\n- The first LO in any workflow **MUST be human-triggered** unless explicitly marked as system-triggered\n- System-triggered workflows are permitted ONLY when initiated by']
2025-05-11 03:57:26,573 - test_prescriptive_parser - INFO - Successfully parsed go_definitions
2025-05-11 03:57:26,573 - component_deployer - INFO - Deploying go_definitions from prescriptive text
2025-05-11 03:57:26,573 - prescriptive_parser - INFO - Initializing PrescriptiveParser
2025-05-11 03:57:26,573 - prescriptive_parser - INFO - Parsing go_definitions prescriptive text
2025-05-11 03:57:26,573 - go_parser - INFO - Starting to parse GO definitions
2025-05-11 03:57:26,573 - go_parser - WARNING - Invalid GO header format: # Global Objective (GO) Definition Prompt

You are a specialized AI assistant tasked with creating Global Objective (GO) definitions for a workflow system. Your goal is to generate well-structured, comprehensive GO definitions that follow the standard template and best practices.

## Instructions

1. Create GO definitions based on the provided information and requirements.
2. Follow the standard GO definition template exactly.
3. Ensure each GO has a unique ID in the format "goXXX" (e.g., go001, go002).
4. Define clear input and output stacks with explicit mappings.
5. Include a comprehensive process flow with all Local Objectives (LOs).
6. Specify business rules, integration points, and performance metadata.
7. Include process mining schema details for analytics.
8. Ensure all entity, attribute, and LO references are consistent with the system's data model.

## Critical Design Rules

### 1. LO Enumeration Requirements
- **EVERY LO must have an explicit ID** in the format "lo001", "lo002", etc.
- **EVERY LO referenced anywhere must be defined** in the Process Flow section
- **ALL routing references must use LO IDs**, not just names
- Maintain sequential numbering for all LOs
- Include ALL LOs, even system-triggered or rollback LOs

### 2. First LO Requirements
- The first LO in any workflow **MUST be human-triggered** unless explicitly marked as system-triggered
- System-triggered workflows are permitted ONLY when initiated by
2025-05-11 03:57:26,573 - go_parser - INFO - Successfully parsed 0 global objectives
2025-05-11 03:57:26,573 - component_deployer - INFO - Deploying component of type: go_definitions
2025-05-11 03:57:26,574 - component_deployer - INFO - Using schema: workflow_runtime
2025-05-11 03:57:26,579 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 03:57:26,580 - go_deployer - INFO - Deploying GO definitions to workflow_runtime
2025-05-11 03:57:26,589 - go_deployer - INFO - GO deployment completed successfully
2025-05-11 03:57:26,589 - component_deployer - INFO - Component deployment succeeded
2025-05-11 03:57:26,589 - test_prescriptive_parser - INFO - Deploy messages for go_definitions: ['Invalid GO header format: # Global Objective (GO) Definition Prompt\n\nYou are a specialized AI assistant tasked with creating Global Objective (GO) definitions for a workflow system. Your goal is to generate well-structured, comprehensive GO definitions that follow the standard template and best practices.\n\n## Instructions\n\n1. Create GO definitions based on the provided information and requirements.\n2. Follow the standard GO definition template exactly.\n3. Ensure each GO has a unique ID in the format "goXXX" (e.g., go001, go002).\n4. Define clear input and output stacks with explicit mappings.\n5. Include a comprehensive process flow with all Local Objectives (LOs).\n6. Specify business rules, integration points, and performance metadata.\n7. Include process mining schema details for analytics.\n8. Ensure all entity, attribute, and LO references are consistent with the system\'s data model.\n\n## Critical Design Rules\n\n### 1. LO Enumeration Requirements\n- **EVERY LO must have an explicit ID** in the format "lo001", "lo002", etc.\n- **EVERY LO referenced anywhere must be defined** in the Process Flow section\n- **ALL routing references must use LO IDs**, not just names\n- Maintain sequential numbering for all LOs\n- Include ALL LOs, even system-triggered or rollback LOs\n\n### 2. First LO Requirements\n- The first LO in any workflow **MUST be human-triggered** unless explicitly marked as system-triggered\n- System-triggered workflows are permitted ONLY when initiated by', 'Inserted document into components']
2025-05-11 03:57:26,589 - test_prescriptive_parser - INFO - Successfully deployed go_definitions
2025-05-11 03:57:26,589 - test_prescriptive_parser - INFO - Testing lo_definitions
2025-05-11 03:57:26,589 - test_prescriptive_parser - INFO - Reading prescriptive text from prompts/lo_definitions_prompt.txt
2025-05-11 03:57:26,589 - prescriptive_parser - INFO - Parsing lo_definitions prescriptive text
2025-05-11 03:57:26,589 - lo_parser - INFO - Starting to parse LO definitions
2025-05-11 03:57:26,590 - lo_parser - WARNING - Invalid LO header format: # Local Objective (LO) Definition Prompt

You are a specialized AI assistant tasked with creating Local Objective (LO) definitions for a workflow system. Your goal is to generate well-structured, comprehensive LO definitions that follow the standard template and best practices.

## Instructions

1. Create LO definitions based on the provided information and requirements.
2. Follow the standard LO definition template exactly.
3. Ensure each LO has a unique ID in the format "loXXX" (e.g., lo001, lo002).
4. Define clear inputs and outputs with all required attributes.
5. Specify system actions, DB constraints, UI controls, and mapping details.
6. Include execution pathways and synthetic values for testing.
7. Ensure all entity, attribute, and role references are consistent with the system's data model.
8. Make sure LO definitions align with their parent Global Objective (GO) definitions.

## Core LO Definition Structure

Each LO definition should follow this comprehensive format
2025-05-11 03:57:26,590 - lo_parser - INFO - Successfully parsed 0 local objectives
2025-05-11 03:57:26,590 - test_prescriptive_parser - WARNING - Parse warnings for lo_definitions: ['Invalid LO header format: # Local Objective (LO) Definition Prompt\n\nYou are a specialized AI assistant tasked with creating Local Objective (LO) definitions for a workflow system. Your goal is to generate well-structured, comprehensive LO definitions that follow the standard template and best practices.\n\n## Instructions\n\n1. Create LO definitions based on the provided information and requirements.\n2. Follow the standard LO definition template exactly.\n3. Ensure each LO has a unique ID in the format "loXXX" (e.g., lo001, lo002).\n4. Define clear inputs and outputs with all required attributes.\n5. Specify system actions, DB constraints, UI controls, and mapping details.\n6. Include execution pathways and synthetic values for testing.\n7. Ensure all entity, attribute, and role references are consistent with the system\'s data model.\n8. Make sure LO definitions align with their parent Global Objective (GO) definitions.\n\n## Core LO Definition Structure\n\nEach LO definition should follow this comprehensive format']
2025-05-11 03:57:26,590 - test_prescriptive_parser - INFO - Successfully parsed lo_definitions
2025-05-11 03:57:26,590 - component_deployer - INFO - Deploying lo_definitions from prescriptive text
2025-05-11 03:57:26,590 - prescriptive_parser - INFO - Initializing PrescriptiveParser
2025-05-11 03:57:26,590 - prescriptive_parser - INFO - Parsing lo_definitions prescriptive text
2025-05-11 03:57:26,590 - lo_parser - INFO - Starting to parse LO definitions
2025-05-11 03:57:26,590 - lo_parser - WARNING - Invalid LO header format: # Local Objective (LO) Definition Prompt

You are a specialized AI assistant tasked with creating Local Objective (LO) definitions for a workflow system. Your goal is to generate well-structured, comprehensive LO definitions that follow the standard template and best practices.

## Instructions

1. Create LO definitions based on the provided information and requirements.
2. Follow the standard LO definition template exactly.
3. Ensure each LO has a unique ID in the format "loXXX" (e.g., lo001, lo002).
4. Define clear inputs and outputs with all required attributes.
5. Specify system actions, DB constraints, UI controls, and mapping details.
6. Include execution pathways and synthetic values for testing.
7. Ensure all entity, attribute, and role references are consistent with the system's data model.
8. Make sure LO definitions align with their parent Global Objective (GO) definitions.

## Core LO Definition Structure

Each LO definition should follow this comprehensive format
2025-05-11 03:57:26,590 - lo_parser - INFO - Successfully parsed 0 local objectives
2025-05-11 03:57:26,590 - component_deployer - INFO - Deploying component of type: lo_definitions
2025-05-11 03:57:26,590 - component_deployer - INFO - Using schema: workflow_runtime
2025-05-11 03:57:26,596 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 03:57:26,602 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 03:57:26,603 - lo_deployer - INFO - Deploying LO definitions to workflow_runtime
2025-05-11 03:57:26,611 - lo_deployer - INFO - LO deployment completed successfully
2025-05-11 03:57:26,611 - component_deployer - INFO - Component deployment succeeded
2025-05-11 03:57:26,611 - test_prescriptive_parser - INFO - Deploy messages for lo_definitions: ['Invalid LO header format: # Local Objective (LO) Definition Prompt\n\nYou are a specialized AI assistant tasked with creating Local Objective (LO) definitions for a workflow system. Your goal is to generate well-structured, comprehensive LO definitions that follow the standard template and best practices.\n\n## Instructions\n\n1. Create LO definitions based on the provided information and requirements.\n2. Follow the standard LO definition template exactly.\n3. Ensure each LO has a unique ID in the format "loXXX" (e.g., lo001, lo002).\n4. Define clear inputs and outputs with all required attributes.\n5. Specify system actions, DB constraints, UI controls, and mapping details.\n6. Include execution pathways and synthetic values for testing.\n7. Ensure all entity, attribute, and role references are consistent with the system\'s data model.\n8. Make sure LO definitions align with their parent Global Objective (GO) definitions.\n\n## Core LO Definition Structure\n\nEach LO definition should follow this comprehensive format', 'Inserted document into components']
2025-05-11 03:57:26,611 - test_prescriptive_parser - INFO - Successfully deployed lo_definitions
2025-05-11 03:57:26,611 - test_prescriptive_parser - INFO - Testing roles
2025-05-11 03:57:26,611 - test_prescriptive_parser - INFO - Reading prescriptive text from prompts/roles_prompt.txt
2025-05-11 03:57:26,611 - prescriptive_parser - INFO - Parsing roles prescriptive text
2025-05-11 03:57:26,611 - role_parser - INFO - Starting to parse role definitions
2025-05-11 03:57:26,611 - role_parser - WARNING - Invalid role header format: # Role Definition Prompt

You are a specialized AI assistant tasked with creating role definitions for a workflow system. Your goal is to generate well-structured, comprehensive role definitions that follow the standard template and best practices.

## Instructions

1. Create role definitions based on the provided information and requirements.
2. Follow the standard role definition template exactly.
3. Ensure each role has a unique ID in the format "roleXXX" (e.g., role001, role002).
4. Define clear permissions for Create, Read, Update operations on entities.
5. Specify Global Objective (GO) associations with appropriate role types.
6. Include scope, classification, and special conditions where applicable.
7. For inherited roles, clearly specify the parent role and only add the additional permissions.
8. Ensure all entity and attribute references are consistent with the system's data model.

## Standard Role Definition Template

```
2025-05-11 03:57:26,611 - role_parser - INFO - Parsing role: [RoleName] (ID: [role_id])
2025-05-11 03:57:26,611 - role_parser - INFO - Successfully parsed role '[RoleName]'
2025-05-11 03:57:26,612 - role_parser - WARNING - Invalid role header format: Role Details
2025-05-11 03:57:26,612 - role_parser - INFO - Parsing role: Employee (ID: emp001)
2025-05-11 03:57:26,612 - role_parser - INFO - Successfully parsed role 'Employee'
2025-05-11 03:57:26,612 - role_parser - INFO - Parsing role: Manager (ID: mgr001)
2025-05-11 03:57:26,612 - role_parser - INFO - Successfully parsed role 'Manager'
2025-05-11 03:57:26,612 - role_parser - INFO - Successfully parsed 3 roles
2025-05-11 03:57:26,612 - test_prescriptive_parser - WARNING - Parse warnings for roles: ['Invalid role header format: # Role Definition Prompt\n\nYou are a specialized AI assistant tasked with creating role definitions for a workflow system. Your goal is to generate well-structured, comprehensive role definitions that follow the standard template and best practices.\n\n## Instructions\n\n1. Create role definitions based on the provided information and requirements.\n2. Follow the standard role definition template exactly.\n3. Ensure each role has a unique ID in the format "roleXXX" (e.g., role001, role002).\n4. Define clear permissions for Create, Read, Update operations on entities.\n5. Specify Global Objective (GO) associations with appropriate role types.\n6. Include scope, classification, and special conditions where applicable.\n7. For inherited roles, clearly specify the parent role and only add the additional permissions.\n8. Ensure all entity and attribute references are consistent with the system\'s data model.\n\n## Standard Role Definition Template\n\n```', 'Invalid role header format: Role Details']
2025-05-11 03:57:26,612 - test_prescriptive_parser - INFO - Successfully parsed roles
2025-05-11 03:57:26,612 - component_deployer - INFO - Deploying roles from prescriptive text
2025-05-11 03:57:26,612 - prescriptive_parser - INFO - Initializing PrescriptiveParser
2025-05-11 03:57:26,612 - prescriptive_parser - INFO - Parsing roles prescriptive text
2025-05-11 03:57:26,612 - role_parser - INFO - Starting to parse role definitions
2025-05-11 03:57:26,612 - role_parser - WARNING - Invalid role header format: # Role Definition Prompt

You are a specialized AI assistant tasked with creating role definitions for a workflow system. Your goal is to generate well-structured, comprehensive role definitions that follow the standard template and best practices.

## Instructions

1. Create role definitions based on the provided information and requirements.
2. Follow the standard role definition template exactly.
3. Ensure each role has a unique ID in the format "roleXXX" (e.g., role001, role002).
4. Define clear permissions for Create, Read, Update operations on entities.
5. Specify Global Objective (GO) associations with appropriate role types.
6. Include scope, classification, and special conditions where applicable.
7. For inherited roles, clearly specify the parent role and only add the additional permissions.
8. Ensure all entity and attribute references are consistent with the system's data model.

## Standard Role Definition Template

```
2025-05-11 03:57:26,612 - role_parser - INFO - Parsing role: [RoleName] (ID: [role_id])
2025-05-11 03:57:26,612 - role_parser - INFO - Successfully parsed role '[RoleName]'
2025-05-11 03:57:26,612 - role_parser - WARNING - Invalid role header format: Role Details
2025-05-11 03:57:26,612 - role_parser - INFO - Parsing role: Employee (ID: emp001)
2025-05-11 03:57:26,612 - role_parser - INFO - Successfully parsed role 'Employee'
2025-05-11 03:57:26,612 - role_parser - INFO - Parsing role: Manager (ID: mgr001)
2025-05-11 03:57:26,612 - role_parser - INFO - Successfully parsed role 'Manager'
2025-05-11 03:57:26,612 - role_parser - INFO - Successfully parsed 3 roles
2025-05-11 03:57:26,613 - component_deployer - INFO - Deploying component of type: roles
2025-05-11 03:57:26,614 - component_deployer - INFO - Using schema: workflow_runtime
2025-05-11 03:57:26,618 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 03:57:26,623 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 03:57:26,628 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 03:57:26,629 - role_deployer - INFO - Deploying roles to workflow_runtime
2025-05-11 03:57:26,633 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 03:57:26,637 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 03:57:26,638 - db_utils - ERROR - Database error: null value in column "tenant_id" of relation "roles" violates not-null constraint
DETAIL:  Failing row contains (role_employee, Employee, null, null, 2025-05-11 03:57:26.638085, 2025-05-11 03:57:26.638085, , ["create:LeaveApplication", "read:LeaveApplication", "read:Emplo..., Own, Standard, , v2).
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 54, in execute_query
    cursor.execute(query, params)
psycopg2.errors.NotNullViolation: null value in column "tenant_id" of relation "roles" violates not-null constraint
DETAIL:  Failing row contains (role_employee, Employee, null, null, 2025-05-11 03:57:26.638085, 2025-05-11 03:57:26.638085, , ["create:LeaveApplication", "read:LeaveApplication", "read:Emplo..., Own, Standard, , v2).

2025-05-11 03:57:26,639 - component_deployer - INFO - Component deployment failed
2025-05-11 03:57:26,639 - test_prescriptive_parser - INFO - Deploy messages for roles: ['Invalid role header format: # Role Definition Prompt\n\nYou are a specialized AI assistant tasked with creating role definitions for a workflow system. Your goal is to generate well-structured, comprehensive role definitions that follow the standard template and best practices.\n\n## Instructions\n\n1. Create role definitions based on the provided information and requirements.\n2. Follow the standard role definition template exactly.\n3. Ensure each role has a unique ID in the format "roleXXX" (e.g., role001, role002).\n4. Define clear permissions for Create, Read, Update operations on entities.\n5. Specify Global Objective (GO) associations with appropriate role types.\n6. Include scope, classification, and special conditions where applicable.\n7. For inherited roles, clearly specify the parent role and only add the additional permissions.\n8. Ensure all entity and attribute references are consistent with the system\'s data model.\n\n## Standard Role Definition Template\n\n```', 'Invalid role header format: Role Details', 'Database error: null value in column "tenant_id" of relation "roles" violates not-null constraint\nDETAIL:  Failing row contains (role_employee, Employee, null, null, 2025-05-11 03:57:26.638085, 2025-05-11 03:57:26.638085, , ["create:LeaveApplication", "read:LeaveApplication", "read:Emplo..., Own, Standard, , v2).\n']
2025-05-11 03:57:26,639 - test_prescriptive_parser - ERROR - Failed to deploy roles
2025-05-11 03:57:26,639 - test_prescriptive_parser - ERROR - Individual test failed
2025-05-11 03:58:14,818 - test_prescriptive_parser - INFO - Testing parsing and deploying components individually
2025-05-11 03:58:14,818 - prescriptive_parser - INFO - Initializing PrescriptiveParser
2025-05-11 03:58:14,818 - component_deployer - INFO - ComponentDeployer initialized with use_temp_schema=False
2025-05-11 03:58:14,818 - test_prescriptive_parser - INFO - Testing entities
2025-05-11 03:58:14,818 - test_prescriptive_parser - INFO - Reading prescriptive text from prompts/entities_prompt.txt
2025-05-11 03:58:14,818 - prescriptive_parser - INFO - Parsing entities prescriptive text
2025-05-11 03:58:14,818 - entity_parser - INFO - Starting to parse entity definitions
2025-05-11 03:58:14,818 - entity_parser - WARNING - Invalid entity header format: # Entity Definition Prompt

You are a specialized AI assistant tasked with creating entity definitions for a workflow system. Your goal is to generate well-structured, comprehensive entity definitions that follow the standard template and best practices.

## Instructions

1. Create entity definitions based on the provided information and requirements.
2. Follow the standard entity definition template exactly.
3. Define attributes clearly, marking primary keys (^PK) and foreign keys (^FK).
4. Specify relationships between entities.
5. Include constants, validations, and business rules where applicable.
6. Define calculated/derived fields with clear formulas and dependencies.
7. Specify hierarchical dependencies if they exist.
8. Include data lifecycle management details where relevant.
9. Ensure all entity and attribute references are consistent with the system's data model.

## 1. Basic Entity Declaration
```
[EntityName] has [attribute1]^PK, [attribute2], [attribute3] (enum1, enum2), [attribute4]^FK, [attribute5].
```

## 2. Relationships
```
* [EntityName] has [relationship-type] relationship with [RelatedEntity] using [EntityName].[attributeX]^PK to [RelatedEntity].[attributeY]^FK
```

## 3. Constants & Configuration
```
* [EntityName].[attribute] [PROPERTY_NAME] = [value]
* [EntityName].[attribute] DEFAULT_VALUE = [value]
```

## 4. Validations
```
* [EntityName].[attribute] must be [constraint]
```

## 5. Business Rules
```
BusinessRule [ruleID] for [EntityName]
2025-05-11 03:58:14,818 - entity_parser - WARNING - Invalid entity header format: Entity Metadata
2025-05-11 03:58:14,819 - entity_parser - INFO - Successfully parsed 0 entities
2025-05-11 03:58:14,819 - test_prescriptive_parser - WARNING - Parse warnings for entities: ["Invalid entity header format: # Entity Definition Prompt\n\nYou are a specialized AI assistant tasked with creating entity definitions for a workflow system. Your goal is to generate well-structured, comprehensive entity definitions that follow the standard template and best practices.\n\n## Instructions\n\n1. Create entity definitions based on the provided information and requirements.\n2. Follow the standard entity definition template exactly.\n3. Define attributes clearly, marking primary keys (^PK) and foreign keys (^FK).\n4. Specify relationships between entities.\n5. Include constants, validations, and business rules where applicable.\n6. Define calculated/derived fields with clear formulas and dependencies.\n7. Specify hierarchical dependencies if they exist.\n8. Include data lifecycle management details where relevant.\n9. Ensure all entity and attribute references are consistent with the system's data model.\n\n## 1. Basic Entity Declaration\n```\n[EntityName] has [attribute1]^PK, [attribute2], [attribute3] (enum1, enum2), [attribute4]^FK, [attribute5].\n```\n\n## 2. Relationships\n```\n* [EntityName] has [relationship-type] relationship with [RelatedEntity] using [EntityName].[attributeX]^PK to [RelatedEntity].[attributeY]^FK\n```\n\n## 3. Constants & Configuration\n```\n* [EntityName].[attribute] [PROPERTY_NAME] = [value]\n* [EntityName].[attribute] DEFAULT_VALUE = [value]\n```\n\n## 4. Validations\n```\n* [EntityName].[attribute] must be [constraint]\n```\n\n## 5. Business Rules\n```\nBusinessRule [ruleID] for [EntityName]", 'Invalid entity header format: Entity Metadata']
2025-05-11 03:58:14,819 - test_prescriptive_parser - INFO - Successfully parsed entities
2025-05-11 03:58:14,819 - component_deployer - INFO - Deploying entities from prescriptive text
2025-05-11 03:58:14,819 - prescriptive_parser - INFO - Initializing PrescriptiveParser
2025-05-11 03:58:14,819 - prescriptive_parser - INFO - Parsing entities prescriptive text
2025-05-11 03:58:14,819 - entity_parser - INFO - Starting to parse entity definitions
2025-05-11 03:58:14,819 - entity_parser - WARNING - Invalid entity header format: # Entity Definition Prompt

You are a specialized AI assistant tasked with creating entity definitions for a workflow system. Your goal is to generate well-structured, comprehensive entity definitions that follow the standard template and best practices.

## Instructions

1. Create entity definitions based on the provided information and requirements.
2. Follow the standard entity definition template exactly.
3. Define attributes clearly, marking primary keys (^PK) and foreign keys (^FK).
4. Specify relationships between entities.
5. Include constants, validations, and business rules where applicable.
6. Define calculated/derived fields with clear formulas and dependencies.
7. Specify hierarchical dependencies if they exist.
8. Include data lifecycle management details where relevant.
9. Ensure all entity and attribute references are consistent with the system's data model.

## 1. Basic Entity Declaration
```
[EntityName] has [attribute1]^PK, [attribute2], [attribute3] (enum1, enum2), [attribute4]^FK, [attribute5].
```

## 2. Relationships
```
* [EntityName] has [relationship-type] relationship with [RelatedEntity] using [EntityName].[attributeX]^PK to [RelatedEntity].[attributeY]^FK
```

## 3. Constants & Configuration
```
* [EntityName].[attribute] [PROPERTY_NAME] = [value]
* [EntityName].[attribute] DEFAULT_VALUE = [value]
```

## 4. Validations
```
* [EntityName].[attribute] must be [constraint]
```

## 5. Business Rules
```
BusinessRule [ruleID] for [EntityName]
2025-05-11 03:58:14,819 - entity_parser - WARNING - Invalid entity header format: Entity Metadata
2025-05-11 03:58:14,819 - entity_parser - INFO - Successfully parsed 0 entities
2025-05-11 03:58:14,819 - component_deployer - INFO - Deploying component of type: entities
2025-05-11 03:58:14,819 - component_deployer - INFO - Using schema: workflow_runtime
2025-05-11 03:58:14,819 - entity_deployer - INFO - Deploying entities to workflow_runtime
2025-05-11 03:58:14,833 - entity_deployer - INFO - Entity deployment completed successfully
2025-05-11 03:58:14,833 - component_deployer - INFO - Component deployment succeeded
2025-05-11 03:58:14,833 - test_prescriptive_parser - INFO - Deploy messages for entities: ["Invalid entity header format: # Entity Definition Prompt\n\nYou are a specialized AI assistant tasked with creating entity definitions for a workflow system. Your goal is to generate well-structured, comprehensive entity definitions that follow the standard template and best practices.\n\n## Instructions\n\n1. Create entity definitions based on the provided information and requirements.\n2. Follow the standard entity definition template exactly.\n3. Define attributes clearly, marking primary keys (^PK) and foreign keys (^FK).\n4. Specify relationships between entities.\n5. Include constants, validations, and business rules where applicable.\n6. Define calculated/derived fields with clear formulas and dependencies.\n7. Specify hierarchical dependencies if they exist.\n8. Include data lifecycle management details where relevant.\n9. Ensure all entity and attribute references are consistent with the system's data model.\n\n## 1. Basic Entity Declaration\n```\n[EntityName] has [attribute1]^PK, [attribute2], [attribute3] (enum1, enum2), [attribute4]^FK, [attribute5].\n```\n\n## 2. Relationships\n```\n* [EntityName] has [relationship-type] relationship with [RelatedEntity] using [EntityName].[attributeX]^PK to [RelatedEntity].[attributeY]^FK\n```\n\n## 3. Constants & Configuration\n```\n* [EntityName].[attribute] [PROPERTY_NAME] = [value]\n* [EntityName].[attribute] DEFAULT_VALUE = [value]\n```\n\n## 4. Validations\n```\n* [EntityName].[attribute] must be [constraint]\n```\n\n## 5. Business Rules\n```\nBusinessRule [ruleID] for [EntityName]", 'Invalid entity header format: Entity Metadata', 'Inserted document into components']
2025-05-11 03:58:14,833 - test_prescriptive_parser - INFO - Successfully deployed entities
2025-05-11 03:58:14,833 - test_prescriptive_parser - INFO - Testing go_definitions
2025-05-11 03:58:14,833 - test_prescriptive_parser - INFO - Reading prescriptive text from prompts/go_definitions_prompt.txt
2025-05-11 03:58:14,834 - prescriptive_parser - INFO - Parsing go_definitions prescriptive text
2025-05-11 03:58:14,834 - go_parser - INFO - Starting to parse GO definitions
2025-05-11 03:58:14,834 - go_parser - WARNING - Invalid GO header format: # Global Objective (GO) Definition Prompt

You are a specialized AI assistant tasked with creating Global Objective (GO) definitions for a workflow system. Your goal is to generate well-structured, comprehensive GO definitions that follow the standard template and best practices.

## Instructions

1. Create GO definitions based on the provided information and requirements.
2. Follow the standard GO definition template exactly.
3. Ensure each GO has a unique ID in the format "goXXX" (e.g., go001, go002).
4. Define clear input and output stacks with explicit mappings.
5. Include a comprehensive process flow with all Local Objectives (LOs).
6. Specify business rules, integration points, and performance metadata.
7. Include process mining schema details for analytics.
8. Ensure all entity, attribute, and LO references are consistent with the system's data model.

## Critical Design Rules

### 1. LO Enumeration Requirements
- **EVERY LO must have an explicit ID** in the format "lo001", "lo002", etc.
- **EVERY LO referenced anywhere must be defined** in the Process Flow section
- **ALL routing references must use LO IDs**, not just names
- Maintain sequential numbering for all LOs
- Include ALL LOs, even system-triggered or rollback LOs

### 2. First LO Requirements
- The first LO in any workflow **MUST be human-triggered** unless explicitly marked as system-triggered
- System-triggered workflows are permitted ONLY when initiated by
2025-05-11 03:58:14,834 - go_parser - INFO - Successfully parsed 0 global objectives
2025-05-11 03:58:14,834 - test_prescriptive_parser - WARNING - Parse warnings for go_definitions: ['Invalid GO header format: # Global Objective (GO) Definition Prompt\n\nYou are a specialized AI assistant tasked with creating Global Objective (GO) definitions for a workflow system. Your goal is to generate well-structured, comprehensive GO definitions that follow the standard template and best practices.\n\n## Instructions\n\n1. Create GO definitions based on the provided information and requirements.\n2. Follow the standard GO definition template exactly.\n3. Ensure each GO has a unique ID in the format "goXXX" (e.g., go001, go002).\n4. Define clear input and output stacks with explicit mappings.\n5. Include a comprehensive process flow with all Local Objectives (LOs).\n6. Specify business rules, integration points, and performance metadata.\n7. Include process mining schema details for analytics.\n8. Ensure all entity, attribute, and LO references are consistent with the system\'s data model.\n\n## Critical Design Rules\n\n### 1. LO Enumeration Requirements\n- **EVERY LO must have an explicit ID** in the format "lo001", "lo002", etc.\n- **EVERY LO referenced anywhere must be defined** in the Process Flow section\n- **ALL routing references must use LO IDs**, not just names\n- Maintain sequential numbering for all LOs\n- Include ALL LOs, even system-triggered or rollback LOs\n\n### 2. First LO Requirements\n- The first LO in any workflow **MUST be human-triggered** unless explicitly marked as system-triggered\n- System-triggered workflows are permitted ONLY when initiated by']
2025-05-11 03:58:14,834 - test_prescriptive_parser - INFO - Successfully parsed go_definitions
2025-05-11 03:58:14,834 - component_deployer - INFO - Deploying go_definitions from prescriptive text
2025-05-11 03:58:14,834 - prescriptive_parser - INFO - Initializing PrescriptiveParser
2025-05-11 03:58:14,834 - prescriptive_parser - INFO - Parsing go_definitions prescriptive text
2025-05-11 03:58:14,834 - go_parser - INFO - Starting to parse GO definitions
2025-05-11 03:58:14,834 - go_parser - WARNING - Invalid GO header format: # Global Objective (GO) Definition Prompt

You are a specialized AI assistant tasked with creating Global Objective (GO) definitions for a workflow system. Your goal is to generate well-structured, comprehensive GO definitions that follow the standard template and best practices.

## Instructions

1. Create GO definitions based on the provided information and requirements.
2. Follow the standard GO definition template exactly.
3. Ensure each GO has a unique ID in the format "goXXX" (e.g., go001, go002).
4. Define clear input and output stacks with explicit mappings.
5. Include a comprehensive process flow with all Local Objectives (LOs).
6. Specify business rules, integration points, and performance metadata.
7. Include process mining schema details for analytics.
8. Ensure all entity, attribute, and LO references are consistent with the system's data model.

## Critical Design Rules

### 1. LO Enumeration Requirements
- **EVERY LO must have an explicit ID** in the format "lo001", "lo002", etc.
- **EVERY LO referenced anywhere must be defined** in the Process Flow section
- **ALL routing references must use LO IDs**, not just names
- Maintain sequential numbering for all LOs
- Include ALL LOs, even system-triggered or rollback LOs

### 2. First LO Requirements
- The first LO in any workflow **MUST be human-triggered** unless explicitly marked as system-triggered
- System-triggered workflows are permitted ONLY when initiated by
2025-05-11 03:58:14,834 - go_parser - INFO - Successfully parsed 0 global objectives
2025-05-11 03:58:14,834 - component_deployer - INFO - Deploying component of type: go_definitions
2025-05-11 03:58:14,834 - component_deployer - INFO - Using schema: workflow_runtime
2025-05-11 03:58:14,840 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 03:58:14,841 - go_deployer - INFO - Deploying GO definitions to workflow_runtime
2025-05-11 03:58:14,850 - go_deployer - INFO - GO deployment completed successfully
2025-05-11 03:58:14,850 - component_deployer - INFO - Component deployment succeeded
2025-05-11 03:58:14,850 - test_prescriptive_parser - INFO - Deploy messages for go_definitions: ['Invalid GO header format: # Global Objective (GO) Definition Prompt\n\nYou are a specialized AI assistant tasked with creating Global Objective (GO) definitions for a workflow system. Your goal is to generate well-structured, comprehensive GO definitions that follow the standard template and best practices.\n\n## Instructions\n\n1. Create GO definitions based on the provided information and requirements.\n2. Follow the standard GO definition template exactly.\n3. Ensure each GO has a unique ID in the format "goXXX" (e.g., go001, go002).\n4. Define clear input and output stacks with explicit mappings.\n5. Include a comprehensive process flow with all Local Objectives (LOs).\n6. Specify business rules, integration points, and performance metadata.\n7. Include process mining schema details for analytics.\n8. Ensure all entity, attribute, and LO references are consistent with the system\'s data model.\n\n## Critical Design Rules\n\n### 1. LO Enumeration Requirements\n- **EVERY LO must have an explicit ID** in the format "lo001", "lo002", etc.\n- **EVERY LO referenced anywhere must be defined** in the Process Flow section\n- **ALL routing references must use LO IDs**, not just names\n- Maintain sequential numbering for all LOs\n- Include ALL LOs, even system-triggered or rollback LOs\n\n### 2. First LO Requirements\n- The first LO in any workflow **MUST be human-triggered** unless explicitly marked as system-triggered\n- System-triggered workflows are permitted ONLY when initiated by', 'Inserted document into components']
2025-05-11 03:58:14,850 - test_prescriptive_parser - INFO - Successfully deployed go_definitions
2025-05-11 03:58:14,850 - test_prescriptive_parser - INFO - Testing lo_definitions
2025-05-11 03:58:14,850 - test_prescriptive_parser - INFO - Reading prescriptive text from prompts/lo_definitions_prompt.txt
2025-05-11 03:58:14,850 - prescriptive_parser - INFO - Parsing lo_definitions prescriptive text
2025-05-11 03:58:14,850 - lo_parser - INFO - Starting to parse LO definitions
2025-05-11 03:58:14,850 - lo_parser - WARNING - Invalid LO header format: # Local Objective (LO) Definition Prompt

You are a specialized AI assistant tasked with creating Local Objective (LO) definitions for a workflow system. Your goal is to generate well-structured, comprehensive LO definitions that follow the standard template and best practices.

## Instructions

1. Create LO definitions based on the provided information and requirements.
2. Follow the standard LO definition template exactly.
3. Ensure each LO has a unique ID in the format "loXXX" (e.g., lo001, lo002).
4. Define clear inputs and outputs with all required attributes.
5. Specify system actions, DB constraints, UI controls, and mapping details.
6. Include execution pathways and synthetic values for testing.
7. Ensure all entity, attribute, and role references are consistent with the system's data model.
8. Make sure LO definitions align with their parent Global Objective (GO) definitions.

## Core LO Definition Structure

Each LO definition should follow this comprehensive format
2025-05-11 03:58:14,850 - lo_parser - INFO - Successfully parsed 0 local objectives
2025-05-11 03:58:14,850 - test_prescriptive_parser - WARNING - Parse warnings for lo_definitions: ['Invalid LO header format: # Local Objective (LO) Definition Prompt\n\nYou are a specialized AI assistant tasked with creating Local Objective (LO) definitions for a workflow system. Your goal is to generate well-structured, comprehensive LO definitions that follow the standard template and best practices.\n\n## Instructions\n\n1. Create LO definitions based on the provided information and requirements.\n2. Follow the standard LO definition template exactly.\n3. Ensure each LO has a unique ID in the format "loXXX" (e.g., lo001, lo002).\n4. Define clear inputs and outputs with all required attributes.\n5. Specify system actions, DB constraints, UI controls, and mapping details.\n6. Include execution pathways and synthetic values for testing.\n7. Ensure all entity, attribute, and role references are consistent with the system\'s data model.\n8. Make sure LO definitions align with their parent Global Objective (GO) definitions.\n\n## Core LO Definition Structure\n\nEach LO definition should follow this comprehensive format']
2025-05-11 03:58:14,851 - test_prescriptive_parser - INFO - Successfully parsed lo_definitions
2025-05-11 03:58:14,851 - component_deployer - INFO - Deploying lo_definitions from prescriptive text
2025-05-11 03:58:14,851 - prescriptive_parser - INFO - Initializing PrescriptiveParser
2025-05-11 03:58:14,851 - prescriptive_parser - INFO - Parsing lo_definitions prescriptive text
2025-05-11 03:58:14,851 - lo_parser - INFO - Starting to parse LO definitions
2025-05-11 03:58:14,851 - lo_parser - WARNING - Invalid LO header format: # Local Objective (LO) Definition Prompt

You are a specialized AI assistant tasked with creating Local Objective (LO) definitions for a workflow system. Your goal is to generate well-structured, comprehensive LO definitions that follow the standard template and best practices.

## Instructions

1. Create LO definitions based on the provided information and requirements.
2. Follow the standard LO definition template exactly.
3. Ensure each LO has a unique ID in the format "loXXX" (e.g., lo001, lo002).
4. Define clear inputs and outputs with all required attributes.
5. Specify system actions, DB constraints, UI controls, and mapping details.
6. Include execution pathways and synthetic values for testing.
7. Ensure all entity, attribute, and role references are consistent with the system's data model.
8. Make sure LO definitions align with their parent Global Objective (GO) definitions.

## Core LO Definition Structure

Each LO definition should follow this comprehensive format
2025-05-11 03:58:14,851 - lo_parser - INFO - Successfully parsed 0 local objectives
2025-05-11 03:58:14,851 - component_deployer - INFO - Deploying component of type: lo_definitions
2025-05-11 03:58:14,851 - component_deployer - INFO - Using schema: workflow_runtime
2025-05-11 03:58:14,856 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 03:58:14,862 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 03:58:14,863 - lo_deployer - INFO - Deploying LO definitions to workflow_runtime
2025-05-11 03:58:14,871 - lo_deployer - INFO - LO deployment completed successfully
2025-05-11 03:58:14,872 - component_deployer - INFO - Component deployment succeeded
2025-05-11 03:58:14,872 - test_prescriptive_parser - INFO - Deploy messages for lo_definitions: ['Invalid LO header format: # Local Objective (LO) Definition Prompt\n\nYou are a specialized AI assistant tasked with creating Local Objective (LO) definitions for a workflow system. Your goal is to generate well-structured, comprehensive LO definitions that follow the standard template and best practices.\n\n## Instructions\n\n1. Create LO definitions based on the provided information and requirements.\n2. Follow the standard LO definition template exactly.\n3. Ensure each LO has a unique ID in the format "loXXX" (e.g., lo001, lo002).\n4. Define clear inputs and outputs with all required attributes.\n5. Specify system actions, DB constraints, UI controls, and mapping details.\n6. Include execution pathways and synthetic values for testing.\n7. Ensure all entity, attribute, and role references are consistent with the system\'s data model.\n8. Make sure LO definitions align with their parent Global Objective (GO) definitions.\n\n## Core LO Definition Structure\n\nEach LO definition should follow this comprehensive format', 'Inserted document into components']
2025-05-11 03:58:14,872 - test_prescriptive_parser - INFO - Successfully deployed lo_definitions
2025-05-11 03:58:14,872 - test_prescriptive_parser - INFO - Testing roles
2025-05-11 03:58:14,872 - test_prescriptive_parser - INFO - Reading prescriptive text from prompts/roles_prompt.txt
2025-05-11 03:58:14,872 - prescriptive_parser - INFO - Parsing roles prescriptive text
2025-05-11 03:58:14,872 - role_parser - INFO - Starting to parse role definitions
2025-05-11 03:58:14,872 - role_parser - WARNING - Invalid role header format: # Role Definition Prompt

You are a specialized AI assistant tasked with creating role definitions for a workflow system. Your goal is to generate well-structured, comprehensive role definitions that follow the standard template and best practices.

## Instructions

1. Create role definitions based on the provided information and requirements.
2. Follow the standard role definition template exactly.
3. Ensure each role has a unique ID in the format "roleXXX" (e.g., role001, role002).
4. Define clear permissions for Create, Read, Update operations on entities.
5. Specify Global Objective (GO) associations with appropriate role types.
6. Include scope, classification, and special conditions where applicable.
7. For inherited roles, clearly specify the parent role and only add the additional permissions.
8. Ensure all entity and attribute references are consistent with the system's data model.

## Standard Role Definition Template

```
2025-05-11 03:58:14,872 - role_parser - INFO - Parsing role: [RoleName] (ID: [role_id])
2025-05-11 03:58:14,872 - role_parser - INFO - Successfully parsed role '[RoleName]'
2025-05-11 03:58:14,872 - role_parser - WARNING - Invalid role header format: Role Details
2025-05-11 03:58:14,872 - role_parser - INFO - Parsing role: Employee (ID: emp001)
2025-05-11 03:58:14,872 - role_parser - INFO - Successfully parsed role 'Employee'
2025-05-11 03:58:14,872 - role_parser - INFO - Parsing role: Manager (ID: mgr001)
2025-05-11 03:58:14,872 - role_parser - INFO - Successfully parsed role 'Manager'
2025-05-11 03:58:14,872 - role_parser - INFO - Successfully parsed 3 roles
2025-05-11 03:58:14,872 - test_prescriptive_parser - WARNING - Parse warnings for roles: ['Invalid role header format: # Role Definition Prompt\n\nYou are a specialized AI assistant tasked with creating role definitions for a workflow system. Your goal is to generate well-structured, comprehensive role definitions that follow the standard template and best practices.\n\n## Instructions\n\n1. Create role definitions based on the provided information and requirements.\n2. Follow the standard role definition template exactly.\n3. Ensure each role has a unique ID in the format "roleXXX" (e.g., role001, role002).\n4. Define clear permissions for Create, Read, Update operations on entities.\n5. Specify Global Objective (GO) associations with appropriate role types.\n6. Include scope, classification, and special conditions where applicable.\n7. For inherited roles, clearly specify the parent role and only add the additional permissions.\n8. Ensure all entity and attribute references are consistent with the system\'s data model.\n\n## Standard Role Definition Template\n\n```', 'Invalid role header format: Role Details']
2025-05-11 03:58:14,872 - test_prescriptive_parser - INFO - Successfully parsed roles
2025-05-11 03:58:14,872 - component_deployer - INFO - Deploying roles from prescriptive text
2025-05-11 03:58:14,872 - prescriptive_parser - INFO - Initializing PrescriptiveParser
2025-05-11 03:58:14,872 - prescriptive_parser - INFO - Parsing roles prescriptive text
2025-05-11 03:58:14,872 - role_parser - INFO - Starting to parse role definitions
2025-05-11 03:58:14,873 - role_parser - WARNING - Invalid role header format: # Role Definition Prompt

You are a specialized AI assistant tasked with creating role definitions for a workflow system. Your goal is to generate well-structured, comprehensive role definitions that follow the standard template and best practices.

## Instructions

1. Create role definitions based on the provided information and requirements.
2. Follow the standard role definition template exactly.
3. Ensure each role has a unique ID in the format "roleXXX" (e.g., role001, role002).
4. Define clear permissions for Create, Read, Update operations on entities.
5. Specify Global Objective (GO) associations with appropriate role types.
6. Include scope, classification, and special conditions where applicable.
7. For inherited roles, clearly specify the parent role and only add the additional permissions.
8. Ensure all entity and attribute references are consistent with the system's data model.

## Standard Role Definition Template

```
2025-05-11 03:58:14,873 - role_parser - INFO - Parsing role: [RoleName] (ID: [role_id])
2025-05-11 03:58:14,873 - role_parser - INFO - Successfully parsed role '[RoleName]'
2025-05-11 03:58:14,873 - role_parser - WARNING - Invalid role header format: Role Details
2025-05-11 03:58:14,873 - role_parser - INFO - Parsing role: Employee (ID: emp001)
2025-05-11 03:58:14,873 - role_parser - INFO - Successfully parsed role 'Employee'
2025-05-11 03:58:14,873 - role_parser - INFO - Parsing role: Manager (ID: mgr001)
2025-05-11 03:58:14,873 - role_parser - INFO - Successfully parsed role 'Manager'
2025-05-11 03:58:14,873 - role_parser - INFO - Successfully parsed 3 roles
2025-05-11 03:58:14,874 - component_deployer - INFO - Deploying component of type: roles
2025-05-11 03:58:14,875 - component_deployer - INFO - Using schema: workflow_runtime
2025-05-11 03:58:14,879 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 03:58:14,884 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 03:58:14,888 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 03:58:14,889 - role_deployer - INFO - Deploying roles to workflow_runtime
2025-05-11 03:58:14,893 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 03:58:14,898 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 03:58:14,899 - db_utils - ERROR - Database error: duplicate key value violates unique constraint "unique_role_name"
DETAIL:  Key (name)=(Employee) already exists.
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 54, in execute_query
    cursor.execute(query, params)
psycopg2.errors.UniqueViolation: duplicate key value violates unique constraint "unique_role_name"
DETAIL:  Key (name)=(Employee) already exists.

2025-05-11 03:58:14,899 - component_deployer - INFO - Component deployment failed
2025-05-11 03:58:14,899 - test_prescriptive_parser - INFO - Deploy messages for roles: ['Invalid role header format: # Role Definition Prompt\n\nYou are a specialized AI assistant tasked with creating role definitions for a workflow system. Your goal is to generate well-structured, comprehensive role definitions that follow the standard template and best practices.\n\n## Instructions\n\n1. Create role definitions based on the provided information and requirements.\n2. Follow the standard role definition template exactly.\n3. Ensure each role has a unique ID in the format "roleXXX" (e.g., role001, role002).\n4. Define clear permissions for Create, Read, Update operations on entities.\n5. Specify Global Objective (GO) associations with appropriate role types.\n6. Include scope, classification, and special conditions where applicable.\n7. For inherited roles, clearly specify the parent role and only add the additional permissions.\n8. Ensure all entity and attribute references are consistent with the system\'s data model.\n\n## Standard Role Definition Template\n\n```', 'Invalid role header format: Role Details', 'Database error: duplicate key value violates unique constraint "unique_role_name"\nDETAIL:  Key (name)=(Employee) already exists.\n']
2025-05-11 03:58:14,899 - test_prescriptive_parser - ERROR - Failed to deploy roles
2025-05-11 03:58:14,899 - test_prescriptive_parser - ERROR - Individual test failed
2025-05-11 03:58:58,807 - test_prescriptive_parser - INFO - Testing parsing and deploying components individually
2025-05-11 03:58:58,807 - prescriptive_parser - INFO - Initializing PrescriptiveParser
2025-05-11 03:58:58,807 - component_deployer - INFO - ComponentDeployer initialized with use_temp_schema=True
2025-05-11 03:58:58,813 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 03:58:58,825 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 03:58:58,831 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 03:58:58,840 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 03:58:58,848 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 03:58:58,856 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 03:58:58,864 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 03:58:58,871 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 03:58:58,879 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 03:58:58,882 - test_prescriptive_parser - INFO - Testing entities
2025-05-11 03:58:58,882 - test_prescriptive_parser - INFO - Reading prescriptive text from prompts/entities_prompt.txt
2025-05-11 03:58:58,882 - prescriptive_parser - INFO - Parsing entities prescriptive text
2025-05-11 03:58:58,882 - entity_parser - INFO - Starting to parse entity definitions
2025-05-11 03:58:58,883 - entity_parser - WARNING - Invalid entity header format: # Entity Definition Prompt

You are a specialized AI assistant tasked with creating entity definitions for a workflow system. Your goal is to generate well-structured, comprehensive entity definitions that follow the standard template and best practices.

## Instructions

1. Create entity definitions based on the provided information and requirements.
2. Follow the standard entity definition template exactly.
3. Define attributes clearly, marking primary keys (^PK) and foreign keys (^FK).
4. Specify relationships between entities.
5. Include constants, validations, and business rules where applicable.
6. Define calculated/derived fields with clear formulas and dependencies.
7. Specify hierarchical dependencies if they exist.
8. Include data lifecycle management details where relevant.
9. Ensure all entity and attribute references are consistent with the system's data model.

## 1. Basic Entity Declaration
```
[EntityName] has [attribute1]^PK, [attribute2], [attribute3] (enum1, enum2), [attribute4]^FK, [attribute5].
```

## 2. Relationships
```
* [EntityName] has [relationship-type] relationship with [RelatedEntity] using [EntityName].[attributeX]^PK to [RelatedEntity].[attributeY]^FK
```

## 3. Constants & Configuration
```
* [EntityName].[attribute] [PROPERTY_NAME] = [value]
* [EntityName].[attribute] DEFAULT_VALUE = [value]
```

## 4. Validations
```
* [EntityName].[attribute] must be [constraint]
```

## 5. Business Rules
```
BusinessRule [ruleID] for [EntityName]
2025-05-11 03:58:58,883 - entity_parser - WARNING - Invalid entity header format: Entity Metadata
2025-05-11 03:58:58,883 - entity_parser - INFO - Successfully parsed 0 entities
2025-05-11 03:58:58,883 - test_prescriptive_parser - WARNING - Parse warnings for entities: ["Invalid entity header format: # Entity Definition Prompt\n\nYou are a specialized AI assistant tasked with creating entity definitions for a workflow system. Your goal is to generate well-structured, comprehensive entity definitions that follow the standard template and best practices.\n\n## Instructions\n\n1. Create entity definitions based on the provided information and requirements.\n2. Follow the standard entity definition template exactly.\n3. Define attributes clearly, marking primary keys (^PK) and foreign keys (^FK).\n4. Specify relationships between entities.\n5. Include constants, validations, and business rules where applicable.\n6. Define calculated/derived fields with clear formulas and dependencies.\n7. Specify hierarchical dependencies if they exist.\n8. Include data lifecycle management details where relevant.\n9. Ensure all entity and attribute references are consistent with the system's data model.\n\n## 1. Basic Entity Declaration\n```\n[EntityName] has [attribute1]^PK, [attribute2], [attribute3] (enum1, enum2), [attribute4]^FK, [attribute5].\n```\n\n## 2. Relationships\n```\n* [EntityName] has [relationship-type] relationship with [RelatedEntity] using [EntityName].[attributeX]^PK to [RelatedEntity].[attributeY]^FK\n```\n\n## 3. Constants & Configuration\n```\n* [EntityName].[attribute] [PROPERTY_NAME] = [value]\n* [EntityName].[attribute] DEFAULT_VALUE = [value]\n```\n\n## 4. Validations\n```\n* [EntityName].[attribute] must be [constraint]\n```\n\n## 5. Business Rules\n```\nBusinessRule [ruleID] for [EntityName]", 'Invalid entity header format: Entity Metadata']
2025-05-11 03:58:58,883 - test_prescriptive_parser - INFO - Successfully parsed entities
2025-05-11 03:58:58,883 - component_deployer - INFO - Deploying entities from prescriptive text
2025-05-11 03:58:58,883 - prescriptive_parser - INFO - Initializing PrescriptiveParser
2025-05-11 03:58:58,883 - prescriptive_parser - INFO - Parsing entities prescriptive text
2025-05-11 03:58:58,883 - entity_parser - INFO - Starting to parse entity definitions
2025-05-11 03:58:58,883 - entity_parser - WARNING - Invalid entity header format: # Entity Definition Prompt

You are a specialized AI assistant tasked with creating entity definitions for a workflow system. Your goal is to generate well-structured, comprehensive entity definitions that follow the standard template and best practices.

## Instructions

1. Create entity definitions based on the provided information and requirements.
2. Follow the standard entity definition template exactly.
3. Define attributes clearly, marking primary keys (^PK) and foreign keys (^FK).
4. Specify relationships between entities.
5. Include constants, validations, and business rules where applicable.
6. Define calculated/derived fields with clear formulas and dependencies.
7. Specify hierarchical dependencies if they exist.
8. Include data lifecycle management details where relevant.
9. Ensure all entity and attribute references are consistent with the system's data model.

## 1. Basic Entity Declaration
```
[EntityName] has [attribute1]^PK, [attribute2], [attribute3] (enum1, enum2), [attribute4]^FK, [attribute5].
```

## 2. Relationships
```
* [EntityName] has [relationship-type] relationship with [RelatedEntity] using [EntityName].[attributeX]^PK to [RelatedEntity].[attributeY]^FK
```

## 3. Constants & Configuration
```
* [EntityName].[attribute] [PROPERTY_NAME] = [value]
* [EntityName].[attribute] DEFAULT_VALUE = [value]
```

## 4. Validations
```
* [EntityName].[attribute] must be [constraint]
```

## 5. Business Rules
```
BusinessRule [ruleID] for [EntityName]
2025-05-11 03:58:58,883 - entity_parser - WARNING - Invalid entity header format: Entity Metadata
2025-05-11 03:58:58,883 - entity_parser - INFO - Successfully parsed 0 entities
2025-05-11 03:58:58,884 - component_deployer - INFO - Deploying component of type: entities
2025-05-11 03:58:58,884 - component_deployer - INFO - Using schema: workflow_temp
2025-05-11 03:58:58,884 - entity_deployer - INFO - Deploying entities to workflow_temp
2025-05-11 03:58:58,900 - entity_deployer - INFO - Entity deployment completed successfully
2025-05-11 03:58:58,900 - component_deployer - INFO - Component deployment succeeded
2025-05-11 03:58:58,900 - test_prescriptive_parser - INFO - Deploy messages for entities: ["Invalid entity header format: # Entity Definition Prompt\n\nYou are a specialized AI assistant tasked with creating entity definitions for a workflow system. Your goal is to generate well-structured, comprehensive entity definitions that follow the standard template and best practices.\n\n## Instructions\n\n1. Create entity definitions based on the provided information and requirements.\n2. Follow the standard entity definition template exactly.\n3. Define attributes clearly, marking primary keys (^PK) and foreign keys (^FK).\n4. Specify relationships between entities.\n5. Include constants, validations, and business rules where applicable.\n6. Define calculated/derived fields with clear formulas and dependencies.\n7. Specify hierarchical dependencies if they exist.\n8. Include data lifecycle management details where relevant.\n9. Ensure all entity and attribute references are consistent with the system's data model.\n\n## 1. Basic Entity Declaration\n```\n[EntityName] has [attribute1]^PK, [attribute2], [attribute3] (enum1, enum2), [attribute4]^FK, [attribute5].\n```\n\n## 2. Relationships\n```\n* [EntityName] has [relationship-type] relationship with [RelatedEntity] using [EntityName].[attributeX]^PK to [RelatedEntity].[attributeY]^FK\n```\n\n## 3. Constants & Configuration\n```\n* [EntityName].[attribute] [PROPERTY_NAME] = [value]\n* [EntityName].[attribute] DEFAULT_VALUE = [value]\n```\n\n## 4. Validations\n```\n* [EntityName].[attribute] must be [constraint]\n```\n\n## 5. Business Rules\n```\nBusinessRule [ruleID] for [EntityName]", 'Invalid entity header format: Entity Metadata', 'Inserted document into components']
2025-05-11 03:58:58,900 - test_prescriptive_parser - INFO - Successfully deployed entities
2025-05-11 03:58:58,900 - test_prescriptive_parser - INFO - Testing go_definitions
2025-05-11 03:58:58,900 - test_prescriptive_parser - INFO - Reading prescriptive text from prompts/go_definitions_prompt.txt
2025-05-11 03:58:58,901 - prescriptive_parser - INFO - Parsing go_definitions prescriptive text
2025-05-11 03:58:58,901 - go_parser - INFO - Starting to parse GO definitions
2025-05-11 03:58:58,901 - go_parser - WARNING - Invalid GO header format: # Global Objective (GO) Definition Prompt

You are a specialized AI assistant tasked with creating Global Objective (GO) definitions for a workflow system. Your goal is to generate well-structured, comprehensive GO definitions that follow the standard template and best practices.

## Instructions

1. Create GO definitions based on the provided information and requirements.
2. Follow the standard GO definition template exactly.
3. Ensure each GO has a unique ID in the format "goXXX" (e.g., go001, go002).
4. Define clear input and output stacks with explicit mappings.
5. Include a comprehensive process flow with all Local Objectives (LOs).
6. Specify business rules, integration points, and performance metadata.
7. Include process mining schema details for analytics.
8. Ensure all entity, attribute, and LO references are consistent with the system's data model.

## Critical Design Rules

### 1. LO Enumeration Requirements
- **EVERY LO must have an explicit ID** in the format "lo001", "lo002", etc.
- **EVERY LO referenced anywhere must be defined** in the Process Flow section
- **ALL routing references must use LO IDs**, not just names
- Maintain sequential numbering for all LOs
- Include ALL LOs, even system-triggered or rollback LOs

### 2. First LO Requirements
- The first LO in any workflow **MUST be human-triggered** unless explicitly marked as system-triggered
- System-triggered workflows are permitted ONLY when initiated by
2025-05-11 03:58:58,901 - go_parser - INFO - Successfully parsed 0 global objectives
2025-05-11 03:58:58,901 - test_prescriptive_parser - WARNING - Parse warnings for go_definitions: ['Invalid GO header format: # Global Objective (GO) Definition Prompt\n\nYou are a specialized AI assistant tasked with creating Global Objective (GO) definitions for a workflow system. Your goal is to generate well-structured, comprehensive GO definitions that follow the standard template and best practices.\n\n## Instructions\n\n1. Create GO definitions based on the provided information and requirements.\n2. Follow the standard GO definition template exactly.\n3. Ensure each GO has a unique ID in the format "goXXX" (e.g., go001, go002).\n4. Define clear input and output stacks with explicit mappings.\n5. Include a comprehensive process flow with all Local Objectives (LOs).\n6. Specify business rules, integration points, and performance metadata.\n7. Include process mining schema details for analytics.\n8. Ensure all entity, attribute, and LO references are consistent with the system\'s data model.\n\n## Critical Design Rules\n\n### 1. LO Enumeration Requirements\n- **EVERY LO must have an explicit ID** in the format "lo001", "lo002", etc.\n- **EVERY LO referenced anywhere must be defined** in the Process Flow section\n- **ALL routing references must use LO IDs**, not just names\n- Maintain sequential numbering for all LOs\n- Include ALL LOs, even system-triggered or rollback LOs\n\n### 2. First LO Requirements\n- The first LO in any workflow **MUST be human-triggered** unless explicitly marked as system-triggered\n- System-triggered workflows are permitted ONLY when initiated by']
2025-05-11 03:58:58,901 - test_prescriptive_parser - INFO - Successfully parsed go_definitions
2025-05-11 03:58:58,901 - component_deployer - INFO - Deploying go_definitions from prescriptive text
2025-05-11 03:58:58,901 - prescriptive_parser - INFO - Initializing PrescriptiveParser
2025-05-11 03:58:58,901 - prescriptive_parser - INFO - Parsing go_definitions prescriptive text
2025-05-11 03:58:58,901 - go_parser - INFO - Starting to parse GO definitions
2025-05-11 03:58:58,901 - go_parser - WARNING - Invalid GO header format: # Global Objective (GO) Definition Prompt

You are a specialized AI assistant tasked with creating Global Objective (GO) definitions for a workflow system. Your goal is to generate well-structured, comprehensive GO definitions that follow the standard template and best practices.

## Instructions

1. Create GO definitions based on the provided information and requirements.
2. Follow the standard GO definition template exactly.
3. Ensure each GO has a unique ID in the format "goXXX" (e.g., go001, go002).
4. Define clear input and output stacks with explicit mappings.
5. Include a comprehensive process flow with all Local Objectives (LOs).
6. Specify business rules, integration points, and performance metadata.
7. Include process mining schema details for analytics.
8. Ensure all entity, attribute, and LO references are consistent with the system's data model.

## Critical Design Rules

### 1. LO Enumeration Requirements
- **EVERY LO must have an explicit ID** in the format "lo001", "lo002", etc.
- **EVERY LO referenced anywhere must be defined** in the Process Flow section
- **ALL routing references must use LO IDs**, not just names
- Maintain sequential numbering for all LOs
- Include ALL LOs, even system-triggered or rollback LOs

### 2. First LO Requirements
- The first LO in any workflow **MUST be human-triggered** unless explicitly marked as system-triggered
- System-triggered workflows are permitted ONLY when initiated by
2025-05-11 03:58:58,901 - go_parser - INFO - Successfully parsed 0 global objectives
2025-05-11 03:58:58,901 - component_deployer - INFO - Deploying component of type: go_definitions
2025-05-11 03:58:58,901 - component_deployer - INFO - Using schema: workflow_temp
2025-05-11 03:58:58,906 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-11 03:58:58,907 - component_deployer - ERROR - Cannot deploy go_definitions because no entities exist. Entities must be deployed first.
2025-05-11 03:58:58,907 - test_prescriptive_parser - INFO - Deploy messages for go_definitions: ['Invalid GO header format: # Global Objective (GO) Definition Prompt\n\nYou are a specialized AI assistant tasked with creating Global Objective (GO) definitions for a workflow system. Your goal is to generate well-structured, comprehensive GO definitions that follow the standard template and best practices.\n\n## Instructions\n\n1. Create GO definitions based on the provided information and requirements.\n2. Follow the standard GO definition template exactly.\n3. Ensure each GO has a unique ID in the format "goXXX" (e.g., go001, go002).\n4. Define clear input and output stacks with explicit mappings.\n5. Include a comprehensive process flow with all Local Objectives (LOs).\n6. Specify business rules, integration points, and performance metadata.\n7. Include process mining schema details for analytics.\n8. Ensure all entity, attribute, and LO references are consistent with the system\'s data model.\n\n## Critical Design Rules\n\n### 1. LO Enumeration Requirements\n- **EVERY LO must have an explicit ID** in the format "lo001", "lo002", etc.\n- **EVERY LO referenced anywhere must be defined** in the Process Flow section\n- **ALL routing references must use LO IDs**, not just names\n- Maintain sequential numbering for all LOs\n- Include ALL LOs, even system-triggered or rollback LOs\n\n### 2. First LO Requirements\n- The first LO in any workflow **MUST be human-triggered** unless explicitly marked as system-triggered\n- System-triggered workflows are permitted ONLY when initiated by', 'Cannot deploy go_definitions because no entities exist. Entities must be deployed first.']
2025-05-11 03:58:58,907 - test_prescriptive_parser - ERROR - Failed to deploy go_definitions
2025-05-11 03:58:58,907 - test_prescriptive_parser - ERROR - Individual test failed
