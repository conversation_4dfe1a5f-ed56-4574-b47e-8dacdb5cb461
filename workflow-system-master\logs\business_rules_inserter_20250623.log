{"timestamp": "2025-06-23T14:03:50.141703", "operation": "deploy_single_business_rule_to_workflow_temp", "input_data": {"business_rules_id": "BR001"}, "result": {"success": false, "error": "Business rule BR001 not found with status draft", "business_rules_id": "BR001", "required_status": "draft"}, "status": "error"}
{"timestamp": "2025-06-23T14:03:50.287126", "operation": "process_mongo_business_rules_to_workflow_temp", "input_data": {"schema": "workflow_temp"}, "result": {"total_processed": 0, "successful_inserts": 0, "failed_inserts": 0, "details": []}, "status": "success"}
{"timestamp": "2025-06-23T14:09:01.207714", "operation": "deploy_single_business_rule_to_workflow_temp", "input_data": {"business_rules_id": "BR001"}, "result": {"success": false, "error": "Business rule BR001 not found with status draft", "business_rules_id": "BR001", "required_status": "draft"}, "status": "error"}
{"timestamp": "2025-06-23T14:09:01.366543", "operation": "process_mongo_business_rules_to_workflow_temp", "input_data": {"schema": "workflow_temp"}, "result": {"total_processed": 0, "successful_inserts": 0, "failed_inserts": 0, "details": []}, "status": "success"}
{"timestamp": "2025-06-23T14:32:33.658377", "operation": "deploy_single_business_rule_to_workflow_temp", "input_data": {"business_rules_id": "BR001"}, "result": {"success": false, "error": "Business rule BR001 not found with status draft", "business_rules_id": "BR001", "required_status": "draft"}, "status": "error"}
{"timestamp": "2025-06-23T14:32:33.715968", "operation": "process_mongo_business_rules_to_workflow_temp", "input_data": {"schema": "workflow_temp"}, "result": {"total_processed": 0, "successful_inserts": 0, "failed_inserts": 0, "details": []}, "status": "success"}
{"timestamp": "2025-06-23T14:38:53.041694", "operation": "deploy_single_business_rule_to_workflow_temp", "input_data": {"business_rules_id": "BR001"}, "result": {"success": false, "error": "Business rule BR001 not found with status draft", "business_rules_id": "BR001", "required_status": "draft"}, "status": "error"}
{"timestamp": "2025-06-23T14:38:53.169964", "operation": "process_mongo_business_rules_to_workflow_temp", "input_data": {"schema": "workflow_temp"}, "result": {"total_processed": 0, "successful_inserts": 0, "failed_inserts": 0, "details": []}, "status": "success"}
