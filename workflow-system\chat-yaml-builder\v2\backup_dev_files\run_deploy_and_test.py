#!/usr/bin/env python3
"""
Run deploy_to_temp_schema.py and then test_db_completeness.py to check if all required
tables and columns are populated.
"""

import os
import sys
import logging
import subprocess
import argparse
import time

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('run_deploy_and_test.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger('run_deploy_and_test')

def run_command(command: str) -> bool:
    """
    Run a command and return whether it was successful.
    
    Args:
        command: The command to run
        
    Returns:
        <PERSON><PERSON>an indicating if the command was successful
    """
    logger.info(f"Running command: {command}")
    
    try:
        result = subprocess.run(
            command,
            shell=True,
            check=True,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True
        )
        
        logger.info(f"Command output:\n{result.stdout}")
        
        if result.stderr:
            logger.warning(f"Command stderr:\n{result.stderr}")
        
        return True
    except subprocess.CalledProcessError as e:
        logger.error(f"Command failed with exit code {e.returncode}")
        logger.error(f"Command output:\n{e.stdout}")
        logger.error(f"Command stderr:\n{e.stderr}")
        return False

def main():
    """Main function."""
    parser = argparse.ArgumentParser(description='Run deploy_to_temp_schema.py and then test_db_completeness.py')
    parser.add_argument('--schema-name', default='workflow_temp', help='Name of the temporary schema')
    args = parser.parse_args()
    
    # Step 1: Run deploy_to_temp_schema.py
    logger.info("Step 1: Running deploy_to_temp_schema.py")
    deploy_success = run_command(f"python3 deploy_to_temp_schema.py --schema-name {args.schema_name}")
    
    if not deploy_success:
        logger.error("Failed to deploy sample components to temporary schema")
        return 1
    
    # Wait a moment for the database to settle
    logger.info("Waiting for database to settle...")
    time.sleep(2)
    
    # Step 2: Run test_db_completeness.py
    logger.info("Step 2: Running test_db_completeness.py")
    test_success = run_command(f"python3 test_db_completeness.py --schema {args.schema_name}")
    
    if not test_success:
        logger.error("Database completeness test failed")
        
        # Step 3: Check specific tables that might be empty
        logger.info("Step 3: Checking specific tables that might be empty")
        
        # Check GO data mapping
        run_command(f"python3 query_db.py --schema {args.schema_name} --query \"SELECT * FROM {args.schema_name}.go_lo_mapping\"")
        run_command(f"python3 query_db.py --schema {args.schema_name} --query \"SELECT go_id, process_mining_schema, performance_metadata FROM {args.schema_name}.global_objectives\"")
        
        # Check LO data mapping
        run_command(f"python3 query_db.py --schema {args.schema_name} --query \"SELECT * FROM {args.schema_name}.lo_data_mapping_stack\"")
        run_command(f"python3 query_db.py --schema {args.schema_name} --query \"SELECT * FROM {args.schema_name}.lo_data_mappings\"")
        run_command(f"python3 query_db.py --schema {args.schema_name} --query \"SELECT lo_id, ui_stack, mapping_stack FROM {args.schema_name}.local_objectives\"")
        
        # Check input stack
        run_command(f"python3 query_db.py --schema {args.schema_name} --query \"SELECT * FROM {args.schema_name}.lo_input_stack\"")
        run_command(f"python3 query_db.py --schema {args.schema_name} --query \"SELECT * FROM {args.schema_name}.lo_input_items\"")
        
        # Check output stack
        run_command(f"python3 query_db.py --schema {args.schema_name} --query \"SELECT * FROM {args.schema_name}.lo_output_stack\"")
        run_command(f"python3 query_db.py --schema {args.schema_name} --query \"SELECT * FROM {args.schema_name}.lo_output_items\"")
        
        # Check entity business rules
        run_command(f"python3 query_db.py --schema {args.schema_name} --query \"SELECT * FROM {args.schema_name}.entity_business_rules\"")
        run_command(f"python3 query_db.py --schema {args.schema_name} --query \"SELECT entity_id, metadata, lifecycle_management FROM {args.schema_name}.entities\"")
        run_command(f"python3 query_db.py --schema {args.schema_name} --query \"SELECT attribute_id, calculated_field, calculation_formula FROM {args.schema_name}.entity_attributes\"")
        
        # Check role inheritance
        run_command(f"python3 query_db.py --schema {args.schema_name} --query \"SELECT * FROM {args.schema_name}.role_inheritance\"")
        run_command(f"python3 query_db.py --schema {args.schema_name} --query \"SELECT role_id, permission_id, permission_name FROM {args.schema_name}.role_permissions\"")
        
        return 1
    
    logger.info("All tests passed!")
    return 0

if __name__ == '__main__':
    sys.exit(main())
