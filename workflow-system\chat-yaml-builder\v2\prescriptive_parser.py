"""
Prescriptive Parser for YAML Builder v2

This module provides functionality for parsing prescriptive YAML files.
"""

import os
import sys
import logging
import yaml
from typing import Dict, List, Tuple, Any, Optional

# Import parsers
from parsers.entity_parser import extract_entity_data
from parsers.go_parser import extract_go_data
from parsers.lo_parser import extract_lo_data
from parsers.role_parser import extract_role_data

# Set up logging
logger = logging.getLogger('prescriptive_parser')

def parse_prescriptive_yaml(yaml_file: str) -> Tuple[bool, Dict, List[str]]:
    """
    Parse a prescriptive YAML file.
    
    Args:
        yaml_file: Path to the YAML file
        
    Returns:
        Tuple containing:
            - Boolean indicating if parsing was successful
            - Dictionary containing parsed data
            - List of messages (warnings, errors, or success messages)
    """
    messages = []
    
    try:
        logger.info(f"Parsing prescriptive YAML file: {yaml_file}")
        
        # Read YAML file
        with open(yaml_file, 'r') as f:
            yaml_data = yaml.safe_load(f)
        
        # Check if YAML data is valid
        if not yaml_data or not isinstance(yaml_data, dict):
            logger.error("Invalid YAML data: not a dictionary")
            return False, {}, ["Invalid YAML data: not a dictionary"]
        
        # Extract entity data
        entity_data = {}
        if 'entities' in yaml_data:
            success, entity_data, entity_messages = extract_entity_data(yaml_data['entities'])
            messages.extend(entity_messages)
            
            if not success:
                return False, {}, messages
        
        # Extract GO data
        go_data = {}
        if 'global_objectives' in yaml_data:
            success, go_data, go_messages = extract_go_data(yaml_data['global_objectives'])
            messages.extend(go_messages)
            
            if not success:
                return False, {}, messages
        
        # Extract LO data
        lo_data = {}
        if 'local_objectives' in yaml_data:
            success, lo_data, lo_messages = extract_lo_data(yaml_data['local_objectives'])
            messages.extend(lo_messages)
            
            if not success:
                return False, {}, messages
        
        # Extract role data
        role_data = {}
        if 'roles' in yaml_data:
            success, role_data, role_messages = extract_role_data(yaml_data['roles'])
            messages.extend(role_messages)
            
            if not success:
                return False, {}, messages
        
        # Create result dictionary
        result = {}
        
        if entity_data:
            result['entities'] = entity_data
        
        if go_data:
            result['go_definitions'] = go_data
        
        if lo_data:
            result['lo_definitions'] = lo_data
        
        if role_data:
            result['roles'] = role_data
        
        logger.info(f"Successfully parsed prescriptive YAML file: {yaml_file}")
        return True, result, messages
    except yaml.YAMLError as e:
        logger.error(f"Error parsing YAML file: {str(e)}", exc_info=True)
        messages.append(f"Error parsing YAML file: {str(e)}")
        return False, {}, messages
    except Exception as e:
        logger.error(f"Error parsing prescriptive YAML file: {str(e)}", exc_info=True)
        messages.append(f"Error parsing prescriptive YAML file: {str(e)}")
        return False, {}, messages
