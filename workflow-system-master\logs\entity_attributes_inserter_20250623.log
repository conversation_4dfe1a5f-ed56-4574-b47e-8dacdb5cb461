{"timestamp": "2025-06-23T10:56:38.421444", "operation": "deploy_single_entity_attribute_to_workflow_temp", "input_data": {"attribute_id": "E45.At4"}, "result": {"success": false, "error": "Entity attribute E45.At4 not found with status draft", "attribute_id": "E45.At4", "required_status": "draft"}, "status": "error"}
{"timestamp": "2025-06-23T10:59:40.876832", "operation": "deploy_single_entity_attribute_to_workflow_runtime", "input_data": {"attribute_id": "E45.At4"}, "result": {"success": false, "error": "Entity attribute E45.At4 not found with status deployed_to_temp or draft", "attribute_id": "E45.At4", "required_status": "deployed_to_temp or draft"}, "status": "error"}
{"timestamp": "2025-06-23T11:21:56.168494", "operation": "deploy_single_entity_attribute_to_workflow_temp", "input_data": {"attribute_id": "E44.At11"}, "result": {"success": false, "error": "Entity attribute E44.At11 not found with status draft", "attribute_id": "E44.At11", "required_status": "draft"}, "status": "error"}
{"timestamp": "2025-06-23T14:03:49.129842", "operation": "deploy_single_entity_attribute_to_workflow_temp", "input_data": {"attribute_id": "ATTR001"}, "result": {"success": false, "error": "Entity attribute ATTR001 not found with status draft", "attribute_id": "ATTR001", "required_status": "draft"}, "status": "error"}
{"timestamp": "2025-06-23T14:03:49.259806", "operation": "insert_entity_attribute_to_workflow_temp", "input_data": {"_id": "68556e0b3b82120ee5643725", "attribute_id": "E44.At13", "entity_id": "E44", "name": "departmentId", "display_name": "departmentId", "datatype": "string", "is_primary_key": false, "is_foreign_key": true, "is_required": false, "is_unique": false, "default_type": "", "default_value": "", "description": "", "helper_text": "", "is_calculated": false, "calculation_formula": "", "version": 1, "status": "draft", "natural_language": "", "created_at": "2025-06-20T14:19:55.220066", "updated_at": "2025-06-20T14:19:55.220073", "created_by": "Tarun", "updated_by": "Tarun", "attribute_status": "new", "changes_detected": []}, "result": {"success": true, "inserted_id": "E44.At13", "schema": "workflow_temp", "attribute_id": "E44.At13", "entity_id": "E44", "name": "departmentId", "original_attribute_id": "E44.At6"}, "status": "success"}
{"timestamp": "2025-06-23T14:03:49.290787", "operation": "insert_entity_attribute_to_workflow_temp", "input_data": {"_id": "6858f5de1195bbcbf7aa7693", "attribute_id": ".At1", "entity_id": "", "name": "employeeId", "display_name": "Employee ID", "datatype": "string", "is_primary_key": false, "is_foreign_key": false, "is_required": true, "is_unique": true, "default_type": "computation", "default_value": "EMP-{sequence}", "description": "Unique employee identifier", "helper_text": "Auto-generated", "is_calculated": true, "calculation_formula": "EMP-{sequence}", "version": 1, "status": "draft", "natural_language": "Display Name: Employee ID\nName: employeeId\nData Type: string\nRequired: true\nUnique: true\nCalculated: true\nDefault Type: computation\nDefault Value: EMP-{sequence}\nCalculation Formula: EMP-{sequence}\nDescription: Unique employee identifier\nHelper Text: Auto-generated", "created_at": "2025-06-23T06:36:14.276890", "updated_at": "2025-06-23T06:36:14.276897", "created_by": "Tarun", "updated_by": "Tarun", "attribute_status": "new", "changes_detected": []}, "result": {"success": true, "inserted_id": ".At1", "schema": "workflow_temp", "attribute_id": ".At1", "entity_id": "", "name": "employeeId", "original_attribute_id": ".At1"}, "status": "success"}
{"timestamp": "2025-06-23T14:03:49.306587", "operation": "insert_entity_attribute_to_workflow_temp", "input_data": {"_id": "6858f5de1195bbcbf7aa7694", "attribute_id": ".At2", "entity_id": "", "name": "firstName", "display_name": "First Name", "datatype": "string", "is_primary_key": false, "is_foreign_key": false, "is_required": true, "is_unique": false, "default_type": "static value", "default_value": "", "description": "Employee first name", "helper_text": "Enter first name", "is_calculated": false, "calculation_formula": "", "version": 1, "status": "draft", "natural_language": "Display Name: First Name\nName: firstName\nData Type: string\nRequired: true\nDefault Type: static value\nDescription: Employee first name\nHelper Text: Enter first name", "created_at": "2025-06-23T06:36:14.286765", "updated_at": "2025-06-23T06:36:14.286773", "created_by": "Tarun", "updated_by": "Tarun", "attribute_status": "new", "changes_detected": []}, "result": {"success": true, "inserted_id": ".At2", "schema": "workflow_temp", "attribute_id": ".At2", "entity_id": "", "name": "firstName", "original_attribute_id": ".At2"}, "status": "success"}
{"timestamp": "2025-06-23T14:03:49.322090", "operation": "insert_entity_attribute_to_workflow_temp", "input_data": {"_id": "6858f5de1195bbcbf7aa7695", "attribute_id": ".At3", "entity_id": "", "name": "lastName", "display_name": "Last Name", "datatype": "string", "is_primary_key": false, "is_foreign_key": false, "is_required": true, "is_unique": false, "default_type": "static value", "default_value": "", "description": "Employee last name", "helper_text": "Enter last name", "is_calculated": false, "calculation_formula": "", "version": 1, "status": "draft", "natural_language": "Display Name: Last Name\nName: lastName\nData Type: string\nRequired: true\nDefault Type: static value\nDescription: Employee last name\nHelper Text: Enter last name", "created_at": "2025-06-23T06:36:14.298690", "updated_at": "2025-06-23T06:36:14.298698", "created_by": "Tarun", "updated_by": "Tarun", "attribute_status": "new", "changes_detected": []}, "result": {"success": true, "inserted_id": ".At3", "schema": "workflow_temp", "attribute_id": ".At3", "entity_id": "", "name": "lastName", "original_attribute_id": ".At3"}, "status": "success"}
{"timestamp": "2025-06-23T14:03:49.346680", "operation": "insert_entity_attribute_to_workflow_temp", "input_data": {"_id": "6858f5de1195bbcbf7aa7696", "attribute_id": "E45.At5", "entity_id": "E45", "name": "employmentStatus", "display_name": "Employment Status", "datatype": "string", "is_primary_key": false, "is_foreign_key": false, "is_required": true, "is_unique": false, "default_type": "static value", "default_value": "Active", "description": "Current employment status", "helper_text": "Select status", "is_calculated": false, "calculation_formula": "", "version": 1, "status": "draft", "natural_language": "Display Name: Employment Status\nName: employmentStatus\nData Type: string\nRequired: true\nDefault Type: static value\nDefault Value: Active\nDescription: Current employment status\nHelper Text: Select status", "created_at": "2025-06-23T06:36:14.308383", "updated_at": "2025-06-23T06:36:14.308390", "created_by": "Tarun", "updated_by": "Tarun", "attribute_status": "new", "changes_detected": []}, "result": {"success": true, "inserted_id": "E45.At5", "schema": "workflow_temp", "attribute_id": "E45.At5", "entity_id": "E45", "name": "employmentStatus", "original_attribute_id": "E45.At4"}, "status": "success"}
{"timestamp": "2025-06-23T14:03:49.349061", "operation": "process_mongo_entity_attributes_to_workflow_temp", "input_data": {"schema": "workflow_temp"}, "result": {"total_processed": 9, "successful_inserts": 5, "failed_inserts": 0, "details": [{"attribute_id": "E44.At2", "entity_id": "E44", "name": "firstName", "status": "skipped", "reason": "already_exists"}, {"attribute_id": "E44.At4", "entity_id": "E44", "name": "email", "status": "skipped", "reason": "already_exists"}, {"attribute_id": "E44.At13", "entity_id": "E44", "name": "departmentId", "status": "success", "details": {"success": true, "inserted_id": "E44.At13", "schema": "workflow_temp", "attribute_id": "E44.At13", "entity_id": "E44", "name": "departmentId", "original_attribute_id": "E44.At6"}}, {"attribute_id": "E44.At8", "entity_id": "E44", "name": "annualLeaveBalance", "status": "skipped", "reason": "already_exists"}, {"attribute_id": "E44.At10", "entity_id": "E44", "name": "personalLeaveBalance", "status": "skipped", "reason": "already_exists"}, {"attribute_id": ".At1", "entity_id": "", "name": "employeeId", "status": "success", "details": {"success": true, "inserted_id": ".At1", "schema": "workflow_temp", "attribute_id": ".At1", "entity_id": "", "name": "employeeId", "original_attribute_id": ".At1"}}, {"attribute_id": ".At2", "entity_id": "", "name": "firstName", "status": "success", "details": {"success": true, "inserted_id": ".At2", "schema": "workflow_temp", "attribute_id": ".At2", "entity_id": "", "name": "firstName", "original_attribute_id": ".At2"}}, {"attribute_id": ".At3", "entity_id": "", "name": "lastName", "status": "success", "details": {"success": true, "inserted_id": ".At3", "schema": "workflow_temp", "attribute_id": ".At3", "entity_id": "", "name": "lastName", "original_attribute_id": ".At3"}}, {"attribute_id": "E45.At5", "entity_id": "E45", "name": "employmentStatus", "status": "success", "details": {"success": true, "inserted_id": "E45.At5", "schema": "workflow_temp", "attribute_id": "E45.At5", "entity_id": "E45", "name": "employmentStatus", "original_attribute_id": "E45.At4"}}]}, "status": "success"}
{"timestamp": "2025-06-23T14:09:00.478471", "operation": "deploy_single_entity_attribute_to_workflow_temp", "input_data": {"attribute_id": "ATTR001"}, "result": {"success": false, "error": "Entity attribute ATTR001 not found with status draft", "attribute_id": "ATTR001", "required_status": "draft"}, "status": "error"}
{"timestamp": "2025-06-23T14:09:00.573063", "operation": "process_mongo_entity_attributes_to_workflow_temp", "input_data": {"schema": "workflow_temp"}, "result": {"total_processed": 4, "successful_inserts": 0, "failed_inserts": 0, "details": [{"attribute_id": "E44.At2", "entity_id": "E44", "name": "firstName", "status": "skipped", "reason": "already_exists"}, {"attribute_id": "E44.At4", "entity_id": "E44", "name": "email", "status": "skipped", "reason": "already_exists"}, {"attribute_id": "E44.At8", "entity_id": "E44", "name": "annualLeaveBalance", "status": "skipped", "reason": "already_exists"}, {"attribute_id": "E44.At10", "entity_id": "E44", "name": "personalLeaveBalance", "status": "skipped", "reason": "already_exists"}]}, "status": "success"}
{"timestamp": "2025-06-23T14:32:32.829040", "operation": "deploy_single_entity_attribute_to_workflow_temp", "input_data": {"attribute_id": "ATTR001"}, "result": {"success": false, "error": "Entity attribute ATTR001 not found with status draft", "attribute_id": "ATTR001", "required_status": "draft"}, "status": "error"}
{"timestamp": "2025-06-23T14:32:32.991851", "operation": "process_mongo_entity_attributes_to_workflow_temp", "input_data": {"schema": "workflow_temp"}, "result": {"total_processed": 4, "successful_inserts": 0, "failed_inserts": 0, "details": [{"attribute_id": "E44.At2", "entity_id": "E44", "name": "firstName", "status": "skipped", "reason": "already_exists"}, {"attribute_id": "E44.At4", "entity_id": "E44", "name": "email", "status": "skipped", "reason": "already_exists"}, {"attribute_id": "E44.At8", "entity_id": "E44", "name": "annualLeaveBalance", "status": "skipped", "reason": "already_exists"}, {"attribute_id": "E44.At10", "entity_id": "E44", "name": "personalLeaveBalance", "status": "skipped", "reason": "already_exists"}]}, "status": "success"}
{"timestamp": "2025-06-23T14:38:52.331838", "operation": "deploy_single_entity_attribute_to_workflow_temp", "input_data": {"attribute_id": "ATTR001"}, "result": {"success": false, "error": "Entity attribute ATTR001 not found with status draft", "attribute_id": "ATTR001", "required_status": "draft"}, "status": "error"}
{"timestamp": "2025-06-23T14:38:52.506910", "operation": "process_mongo_entity_attributes_to_workflow_temp", "input_data": {"schema": "workflow_temp"}, "result": {"total_processed": 4, "successful_inserts": 0, "failed_inserts": 0, "details": [{"attribute_id": "E44.At2", "entity_id": "E44", "name": "firstName", "status": "skipped", "reason": "already_exists"}, {"attribute_id": "E44.At4", "entity_id": "E44", "name": "email", "status": "skipped", "reason": "already_exists"}, {"attribute_id": "E44.At8", "entity_id": "E44", "name": "annualLeaveBalance", "status": "skipped", "reason": "already_exists"}, {"attribute_id": "E44.At10", "entity_id": "E44", "name": "personalLeaveBalance", "status": "skipped", "reason": "already_exists"}]}, "status": "success"}
