-- Purchase Furniture Sample Data Insert Script
-- Inserts sample data for furniture types and products

SET search_path TO workflow_runtime;

-- =====================================================
-- INSERT SAMPLE DATA
-- =====================================================

-- Insert sample furniture types
INSERT INTO e15_furnituretype (furnituretypeid, typename, description) VALUES 
('FT001', 'Cupboard', 'Storage furniture for clothes and personal items'),
('FT002', 'Bar', 'Furniture for serving drinks and entertainment'),
('FT003', 'Study Table', 'Desk furniture for studying and working'),
('FT004', 'Dining Table', 'Table furniture for dining and meals');

-- Insert sample furniture products
INSERT INTO e14_furnitureproduct (productid, productname, furnituretypeid, price, availableinventory, description) VALUES 
-- Cupboards
('P001', 'Classic Wooden Cupboard', 'FT001', 15000.00, 10, 'Traditional wooden cupboard with 3 compartments'),
('P002', 'Modern Steel Cupboard', 'FT001', 12000.00, 15, 'Contemporary steel cupboard with mirror'),
('P003', 'Sliding Door Cupboard', 'FT001', 18000.00, 8, 'Space-saving cupboard with sliding doors'),

-- Bars
('P004', 'Home Bar Counter', 'FT002', 25000.00, 5, 'Elegant home bar with storage and counter space'),
('P005', 'Mini Bar Cabinet', 'FT002', 8000.00, 12, 'Compact bar cabinet for small spaces'),
('P006', 'Premium Wine Bar', 'FT002', 35000.00, 3, 'Luxury wine bar with temperature control'),

-- Study Tables
('P007', 'Executive Study Table', 'FT003', 10000.00, 20, 'Professional study table with drawers'),
('P008', 'Student Desk', 'FT003', 5000.00, 25, 'Simple and functional study desk for students'),
('P009', 'L-Shaped Study Table', 'FT003', 15000.00, 10, 'Spacious L-shaped desk for home office'),

-- Dining Tables
('P010', 'Family Dining Table', 'FT004', 20000.00, 8, '6-seater dining table for family meals'),
('P011', 'Round Dining Table', 'FT004', 18000.00, 6, '4-seater round dining table'),
('P012', 'Extendable Dining Table', 'FT004', 30000.00, 4, 'Dining table that extends for larger gatherings');

COMMIT;
