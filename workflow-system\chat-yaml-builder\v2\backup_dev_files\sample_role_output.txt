Role Employee (emp001):
- Create: LeaveApplication
- Read: LeaveApplication, Employee
- Update: Employee
- GO: go001 as Originator
- Scope: Own
- Classification: Standard

Role Manager (mgr001) inherits Employee:
- Create: PerformanceReview, TeamBudget
- Read: Employee, TeamMetrics
- Update: PerformanceReview, TeamBudget
- GO: go001 as ProcessOwner, go002 as Originator
- Scope: Team
- Classification: Standard
- Special: budget approval up to $10K

Role HRManager (hr001) inherits Manager:
- Create: EmployeeRecord, CompensationPlan
- Read: Employee, Department, PerformanceReview
- Update: Employee, CompensationPlan
- GO: go001 as BusinessSponsor, go003 as ProcessOwner
- Scope: Organization
- Classification: Administrative
- Special: salary approval, termination processing

Role FinanceManager (fin001):
- Create: Budget, Expense, Revenue
- Read: Department, TeamBudget, PerformanceReview
- Update: Budget, Expense, Revenue
- GO: go002 as ProcessOwner, go004 as BusinessSponsor
- Scope: Organization
- Classification: Administrative
- Special: budget approval up to $100K

Role SystemAdmin (sys001):
- Create: User, Role, Permission
- Read: User, Role, Permission, SystemLog
- Update: User, Role, Permission, SystemConfiguration
- GO: go005 as ProcessOwner
- Scope: System
- Classification: Administrative
- Special: system configuration, user management
