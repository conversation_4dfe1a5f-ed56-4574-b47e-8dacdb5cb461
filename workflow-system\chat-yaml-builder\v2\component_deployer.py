"""
Component Deployer for YAML Builder v2

This module provides functionality for deploying parsed components to the database.
It also handles temporary schema management for validation and testing.
"""

import os
import sys
import logging
from typing import Dict, List, Tuple, Any, Optional

# Import deployers
from deployers.entity_deployer import deploy_entities
from deployers.go_deployer import deploy_go_definitions
from deployers.lo_deployer import deploy_lo_definitions
from deployers.role_deployer import deploy_roles

# Import schema analyzer
from schema_analyzer import analyze_schema

# Import database utilities
from db_utils import execute_query, create_temp_schema

# Set up logging
logger = logging.getLogger('component_deployer')

class ComponentDeployer:
    """
    Deployer for parsed components.
    """
    
    def __init__(self, use_temp_schema: bool = False):
        """
        Initialize the deployer.
        
        Args:
            use_temp_schema: Whether to use a temporary schema for deployment
        """
        self.use_temp_schema = use_temp_schema
        self.temp_schema_name = 'workflow_temp'
        self.runtime_schema_name = 'workflow_runtime'
        
        self.deployers = {
            'entities': deploy_entities,
            'go_definitions': deploy_go_definitions,
            'lo_definitions': deploy_lo_definitions,
            'roles': deploy_roles
        }
    
    def deploy(self, component_type: str, component_data: Dict, schema_name: str = None) -> Tuple[bool, List[str]]:
        """
        Deploy a component to the database.
        
        Args:
            component_type: Type of the component ('roles', 'entities', 'go_definitions', 'lo_definitions')
            component_data: Parsed component data
            schema_name: Schema name to deploy to (overrides use_temp_schema)
            
        Returns:
            Tuple containing:
                - Boolean indicating if deployment was successful
                - List of messages (warnings, errors, or success messages)
        """
        if component_type not in self.deployers:
            logger.error(f"Unknown component type: {component_type}")
            return False, [f"Unknown component type: {component_type}"]
        
        # Determine schema name
        if schema_name is None:
            schema_name = self.temp_schema_name if self.use_temp_schema else self.runtime_schema_name
        
        try:
            logger.info(f"Deploying {component_type} to schema {schema_name}")
            return self.deployers[component_type](component_data, schema_name)
        except Exception as e:
            logger.error(f"Error deploying {component_type} to schema {schema_name}: {str(e)}", exc_info=True)
            return False, [f"Error deploying {component_type} to schema {schema_name}: {str(e)}"]
    
    def create_temp_schema(self) -> Tuple[bool, List[str]]:
        """
        Create a temporary schema for validation and testing.
        
        Returns:
            Tuple containing:
                - Boolean indicating if creation was successful
                - List of messages (warnings, errors, or success messages)
        """
        try:
            logger.info(f"Creating temporary schema {self.temp_schema_name}")
            return create_temp_schema(self.temp_schema_name)
        except Exception as e:
            logger.error(f"Error creating temporary schema {self.temp_schema_name}: {str(e)}", exc_info=True)
            return False, [f"Error creating temporary schema {self.temp_schema_name}: {str(e)}"]
    
    def promote_from_temp_to_runtime(self) -> Tuple[bool, List[str]]:
        """
        Promote from temporary schema to runtime schema.
        
        Returns:
            Tuple containing:
                - Boolean indicating if promotion was successful
                - List of messages (warnings, errors, or success messages)
        """
        messages = []
        
        try:
            logger.info(f"Promoting from temporary schema {self.temp_schema_name} to runtime schema {self.runtime_schema_name}")
            
            # Check if temporary schema exists
            success, query_messages, result = execute_query(
                """
                SELECT EXISTS (
                    SELECT FROM information_schema.schemata
                    WHERE schema_name = %s
                )
                """,
                (self.temp_schema_name,)
            )
            
            if not success:
                messages.extend(query_messages)
                return False, messages
            
            if not result or not result[0][0]:
                messages.append(f"Temporary schema {self.temp_schema_name} does not exist")
                return False, messages
            
            # Check if runtime schema exists
            success, query_messages, result = execute_query(
                """
                SELECT EXISTS (
                    SELECT FROM information_schema.schemata
                    WHERE schema_name = %s
                )
                """,
                (self.runtime_schema_name,)
            )
            
            if not success:
                messages.extend(query_messages)
                return False, messages
            
            # Create runtime schema if it doesn't exist
            if not result or not result[0][0]:
                success, query_messages, _ = execute_query(
                    f"CREATE SCHEMA {self.runtime_schema_name}"
                )
                
                if not success:
                    messages.extend(query_messages)
                    return False, messages
                
                messages.append(f"Created runtime schema {self.runtime_schema_name}")
            else:
                # Drop existing runtime schema
                success, query_messages, _ = execute_query(
                    f"DROP SCHEMA {self.runtime_schema_name} CASCADE"
                )
                
                if not success:
                    messages.extend(query_messages)
                    return False, messages
                
                # Create runtime schema
                success, query_messages, _ = execute_query(
                    f"CREATE SCHEMA {self.runtime_schema_name}"
                )
                
                if not success:
                    messages.extend(query_messages)
                    return False, messages
                
                messages.append(f"Dropped and recreated runtime schema {self.runtime_schema_name}")
            
            # Copy schema structure from temporary to runtime
            success, analyze_messages = analyze_schema(self.temp_schema_name, self.runtime_schema_name)
            messages.extend(analyze_messages)
            
            if not success:
                return False, messages
            
            # Get all tables in the temporary schema
            success, query_messages, tables = execute_query(
                """
                SELECT table_name
                FROM information_schema.tables
                WHERE table_schema = %s
                AND table_type = 'BASE TABLE'
                """,
                (self.temp_schema_name,)
            )
            
            if not success:
                messages.extend(query_messages)
                return False, messages
            
            # Copy data from temporary to runtime for each table
            for table in tables:
                table_name = table[0]
                
                success, query_messages, _ = execute_query(
                    f"""
                    INSERT INTO {self.runtime_schema_name}."{table_name}"
                    SELECT * FROM {self.temp_schema_name}."{table_name}"
                    """
                )
                
                if not success:
                    messages.extend(query_messages)
                    return False, messages
                
                messages.append(f"Copied data from {self.temp_schema_name}.{table_name} to {self.runtime_schema_name}.{table_name}")
            
            messages.append(f"Successfully promoted from temporary schema {self.temp_schema_name} to runtime schema {self.runtime_schema_name}")
            return True, messages
        except Exception as e:
            logger.error(f"Error promoting from temporary schema {self.temp_schema_name} to runtime schema {self.runtime_schema_name}: {str(e)}", exc_info=True)
            messages.append(f"Error promoting from temporary schema {self.temp_schema_name} to runtime schema {self.runtime_schema_name}: {str(e)}")
            return False, messages
    
    def rollback_temp_schema(self) -> Tuple[bool, List[str]]:
        """
        Rollback temporary schema.
        
        Returns:
            Tuple containing:
                - Boolean indicating if rollback was successful
                - List of messages (warnings, errors, or success messages)
        """
        messages = []
        
        try:
            logger.info(f"Rolling back temporary schema {self.temp_schema_name}")
            
            # Check if temporary schema exists
            success, query_messages, result = execute_query(
                """
                SELECT EXISTS (
                    SELECT FROM information_schema.schemata
                    WHERE schema_name = %s
                )
                """,
                (self.temp_schema_name,)
            )
            
            if not success:
                messages.extend(query_messages)
                return False, messages
            
            if not result or not result[0][0]:
                messages.append(f"Temporary schema {self.temp_schema_name} does not exist")
                return True, messages
            
            # Drop temporary schema
            success, query_messages, _ = execute_query(
                f"DROP SCHEMA {self.temp_schema_name} CASCADE"
            )
            
            if not success:
                messages.extend(query_messages)
                return False, messages
            
            # Create empty temporary schema
            success, query_messages, _ = execute_query(
                f"CREATE SCHEMA {self.temp_schema_name}"
            )
            
            if not success:
                messages.extend(query_messages)
                return False, messages
            
            messages.append(f"Successfully rolled back temporary schema {self.temp_schema_name}")
            return True, messages
        except Exception as e:
            logger.error(f"Error rolling back temporary schema {self.temp_schema_name}: {str(e)}", exc_info=True)
            messages.append(f"Error rolling back temporary schema {self.temp_schema_name}: {str(e)}")
            return False, messages
