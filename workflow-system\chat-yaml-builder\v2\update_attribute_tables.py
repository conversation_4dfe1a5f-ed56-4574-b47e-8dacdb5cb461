#!/usr/bin/env python3
"""
Script to update entity IDs in entity_attributes, entity_attribute_metadata, and entity_relationships tables
to match the entity IDs in the entities table.
"""

import logging
import sys
import os
import psycopg2

# Set up logging
logging.basicConfig(level=logging.INFO,
                   format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
                   handlers=[logging.StreamHandler()])
logger = logging.getLogger('update_attribute_tables')

def get_db_connection(schema_name=None):
    """
    Get a database connection.
    
    Args:
        schema_name: Optional schema name to set as the search path
        
    Returns:
        Database connection object
    """
    # Get database connection parameters from environment variables or use defaults
    db_host = os.environ.get('DB_HOST', '**********')
    db_port = os.environ.get('DB_PORT', '5432')
    db_name = os.environ.get('DB_NAME', 'workflow_system')
    db_user = os.environ.get('DB_USER', 'postgres')
    db_password = os.environ.get('DB_PASSWORD', 'workflow_postgres_secure_password')
    
    # Connect to the database
    conn = psycopg2.connect(
        host=db_host,
        port=db_port,
        dbname=db_name,
        user=db_user,
        password=db_password
    )
    
    # Set schema search path if provided
    if schema_name:
        with conn.cursor() as cursor:
            cursor.execute(f"SET search_path TO {schema_name}")
            conn.commit()
    
    return conn

def update_attribute_tables(schema_name):
    """
    Update entity IDs in entity_attributes, entity_attribute_metadata, and entity_relationships tables
    to match the entity IDs in the entities table.
    
    Args:
        schema_name: Schema name
    """
    conn = None
    
    try:
        logger.info(f"Updating entity IDs in attribute tables in schema {schema_name}")
        
        # Get database connection
        conn = get_db_connection(schema_name)
        
        with conn.cursor() as cursor:
            # Check if entity_attributes, entity_attribute_metadata, and entity_relationships tables exist
            cursor.execute(f"""
                SELECT table_name
                FROM information_schema.tables
                WHERE table_schema = '{schema_name}'
                AND table_name IN ('entity_attributes', 'entity_attribute_metadata', 'entity_relationships')
            """)
            
            attribute_tables = cursor.fetchall()
            attribute_tables_list = [table[0] for table in attribute_tables]
            
            logger.info(f"Found tables: {', '.join(attribute_tables_list)}")
            
            # Get entity IDs from entities table
            cursor.execute(f'SELECT entity_id FROM {schema_name}.entities')
            entity_ids = cursor.fetchall()
            
            logger.info(f"Found {len(entity_ids)} entity IDs in entities table")
            
            # For each entity ID, update the corresponding records in entity_attributes, entity_attribute_metadata, and entity_relationships tables
            for entity_id in entity_ids:
                entity_id = entity_id[0]
                
                # Extract numeric part and create old entity ID format
                if entity_id.startswith('E'):
                    num = int(entity_id[1:])
                    old_entity_id = f'E{num:06d}'
                    
                    logger.info(f'Updating entity ID {old_entity_id} to {entity_id}')
                    
                    # Update entity_id in entity_attributes table
                    if 'entity_attributes' in attribute_tables_list:
                        cursor.execute(f"""
                            UPDATE {schema_name}.entity_attributes
                            SET entity_id = %s
                            WHERE entity_id = %s
                        """, (entity_id, old_entity_id))
                        
                        rows_updated = cursor.rowcount
                        logger.info(f'Updated {rows_updated} rows in entity_attributes.entity_id')
                        
                        # Update attribute_id in entity_attributes table
                        cursor.execute(f"""
                            UPDATE {schema_name}.entity_attributes
                            SET attribute_id = REPLACE(attribute_id, %s, %s)
                            WHERE attribute_id LIKE %s
                        """, (old_entity_id, entity_id, f'{old_entity_id}.%'))
                        
                        rows_updated = cursor.rowcount
                        logger.info(f'Updated {rows_updated} rows in entity_attributes.attribute_id')
                    
                    # Update entity_id in entity_attribute_metadata table
                    if 'entity_attribute_metadata' in attribute_tables_list:
                        cursor.execute(f"""
                            UPDATE {schema_name}.entity_attribute_metadata
                            SET entity_id = %s
                            WHERE entity_id = %s
                        """, (entity_id, old_entity_id))
                        
                        rows_updated = cursor.rowcount
                        logger.info(f'Updated {rows_updated} rows in entity_attribute_metadata.entity_id')
                        
                        # Update attribute_id in entity_attribute_metadata table
                        cursor.execute(f"""
                            UPDATE {schema_name}.entity_attribute_metadata
                            SET attribute_id = REPLACE(attribute_id, %s, %s)
                            WHERE attribute_id LIKE %s
                        """, (old_entity_id, entity_id, f'{old_entity_id}.%'))
                        
                        rows_updated = cursor.rowcount
                        logger.info(f'Updated {rows_updated} rows in entity_attribute_metadata.attribute_id')
                    
                    # Update source_entity_id and target_entity_id in entity_relationships table
                    if 'entity_relationships' in attribute_tables_list:
                        # Update source_entity_id
                        cursor.execute(f"""
                            UPDATE {schema_name}.entity_relationships
                            SET source_entity_id = %s
                            WHERE source_entity_id = %s
                        """, (entity_id, old_entity_id))
                        
                        rows_updated = cursor.rowcount
                        logger.info(f'Updated {rows_updated} rows in entity_relationships.source_entity_id')
                        
                        # Update target_entity_id
                        cursor.execute(f"""
                            UPDATE {schema_name}.entity_relationships
                            SET target_entity_id = %s
                            WHERE target_entity_id = %s
                        """, (entity_id, old_entity_id))
                        
                        rows_updated = cursor.rowcount
                        logger.info(f'Updated {rows_updated} rows in entity_relationships.target_entity_id')
            
            # Commit the changes
            conn.commit()
            logger.info('Changes committed successfully')
    except Exception as e:
        logger.error(f'Error updating attribute tables: {str(e)}')
        if conn:
            conn.rollback()
    finally:
        if conn:
            conn.close()

def main():
    """
    Main function to update attribute tables.
    """
    schema_name = 'workflow_temp'
    
    logger.info(f"Starting update of attribute tables in schema {schema_name}")
    
    update_attribute_tables(schema_name)
    
    logger.info(f"Finished update of attribute tables in schema {schema_name}")

if __name__ == "__main__":
    main()
