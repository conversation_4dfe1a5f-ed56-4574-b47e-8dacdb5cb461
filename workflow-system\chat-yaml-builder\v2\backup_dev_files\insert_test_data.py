"""
Insert test data into the entity tables.
"""

import os
import sys
import logging
from typing import Dict, List, Tuple, Any, Optional

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('insert_test_data.log')
    ]
)
logger = logging.getLogger('insert_test_data')

# Add parent directory to path to import common modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from db_utils import execute_query

def insert_department(schema_name: str, department_data: Dict[str, Any]) -> Tuple[bool, List[str], Any]:
    """
    Insert a department record into the database.
    
    Args:
        schema_name: Schema name
        department_data: Department data
        
    Returns:
        Tuple containing:
            - Boolean indicating if insertion was successful
            - List of messages (warnings, errors, or success messages)
            - Department ID
    """
    messages = []
    
    try:
        # Get table name
        table_name = "entity_department.department"
        
        # Build INSERT query
        columns_str = ", ".join(department_data.keys())
        placeholders = ", ".join(["%s"] * len(department_data))
        
        query = f"""
            INSERT INTO {schema_name}."{table_name}" (
                {columns_str}
            ) VALUES (
                {placeholders}
            ) RETURNING departmentid
        """
        
        success, query_messages, result = execute_query(
            query,
            tuple(department_data.values())
        )
        
        if not success:
            messages.extend(query_messages)
            return False, messages, None
        
        department_id = result[0][0] if result else None
        
        messages.append(f"Inserted department with ID {department_id}")
        logger.info(f"Inserted department with ID {department_id}")
        
        return True, messages, department_id
    except Exception as e:
        error_msg = f"Error inserting department: {str(e)}"
        logger.error(error_msg, exc_info=True)
        messages.append(error_msg)
        return False, messages, None

def insert_employee(schema_name: str, employee_data: Dict[str, Any]) -> Tuple[bool, List[str], Any]:
    """
    Insert an employee record into the database.
    
    Args:
        schema_name: Schema name
        employee_data: Employee data
        
    Returns:
        Tuple containing:
            - Boolean indicating if insertion was successful
            - List of messages (warnings, errors, or success messages)
            - Employee ID
    """
    messages = []
    
    try:
        # Get table name
        table_name = "entity_employee.employee"
        
        # Build INSERT query
        columns_str = ", ".join(employee_data.keys())
        placeholders = ", ".join(["%s"] * len(employee_data))
        
        query = f"""
            INSERT INTO {schema_name}."{table_name}" (
                {columns_str}
            ) VALUES (
                {placeholders}
            ) RETURNING employee_id
        """
        
        success, query_messages, result = execute_query(
            query,
            tuple(employee_data.values())
        )
        
        if not success:
            messages.extend(query_messages)
            return False, messages, None
        
        employee_id = result[0][0] if result else None
        
        messages.append(f"Inserted employee with ID {employee_id}")
        logger.info(f"Inserted employee with ID {employee_id}")
        
        return True, messages, employee_id
    except Exception as e:
        error_msg = f"Error inserting employee: {str(e)}"
        logger.error(error_msg, exc_info=True)
        messages.append(error_msg)
        return False, messages, None

def query_departments(schema_name: str) -> Tuple[bool, List[str], Any]:
    """
    Query departments from the database.
    
    Args:
        schema_name: Schema name
        
    Returns:
        Tuple containing:
            - Boolean indicating if query was successful
            - List of messages (warnings, errors, or success messages)
            - Query results
    """
    messages = []
    
    try:
        # Get table name
        table_name = "entity_department.department"
        
        # Build SELECT query
        query = f"""
            SELECT * FROM {schema_name}."{table_name}"
        """
        
        success, query_messages, result = execute_query(query)
        
        if not success:
            messages.extend(query_messages)
            return False, messages, None
        
        messages.append(f"Retrieved {len(result)} departments")
        logger.info(f"Retrieved {len(result)} departments")
        
        return True, messages, result
    except Exception as e:
        error_msg = f"Error querying departments: {str(e)}"
        logger.error(error_msg, exc_info=True)
        messages.append(error_msg)
        return False, messages, None

def query_employees(schema_name: str) -> Tuple[bool, List[str], Any]:
    """
    Query employees from the database.
    
    Args:
        schema_name: Schema name
        
    Returns:
        Tuple containing:
            - Boolean indicating if query was successful
            - List of messages (warnings, errors, or success messages)
            - Query results
    """
    messages = []
    
    try:
        # Get table name
        table_name = "entity_employee.employee"
        
        # Build SELECT query
        query = f"""
            SELECT * FROM {schema_name}."{table_name}"
        """
        
        success, query_messages, result = execute_query(query)
        
        if not success:
            messages.extend(query_messages)
            return False, messages, None
        
        messages.append(f"Retrieved {len(result)} employees")
        logger.info(f"Retrieved {len(result)} employees")
        
        return True, messages, result
    except Exception as e:
        error_msg = f"Error querying employees: {str(e)}"
        logger.error(error_msg, exc_info=True)
        messages.append(error_msg)
        return False, messages, None

def main():
    """
    Main function.
    """
    schema_name = "workflow_temp"
    
    # Department data
    department_data = {
        "departmentid": "DEPT001",
        "name": "Engineering",
        "location": "Building A",
        "budget": "1000000",
        "created_by": "system",
        "updated_by": "system"
    }
    
    # Insert department
    success, messages, department_id = insert_department(schema_name, department_data)
    
    for message in messages:
        logger.info(message)
    
    if not success:
        logger.error("Failed to insert department")
        return
    
    # Employee data
    employee_data = {
        "employeeid": "EMP001",
        "firstname": "John",
        "lastname": "Doe",
        "fullname_derived_": "John Doe",
        "created_by": "system",
        "updated_by": "system"
    }
    
    # Insert employee
    success, messages, employee_id = insert_employee(schema_name, employee_data)
    
    for message in messages:
        logger.info(message)
    
    if not success:
        logger.error("Failed to insert employee")
        return
    
    # Query departments
    success, messages, departments = query_departments(schema_name)
    
    for message in messages:
        logger.info(message)
    
    if success and departments:
        logger.info("Departments:")
        for department in departments:
            logger.info(f"  {department}")
    
    # Query employees
    success, messages, employees = query_employees(schema_name)
    
    for message in messages:
        logger.info(message)
    
    if success and employees:
        logger.info("Employees:")
        for employee in employees:
            logger.info(f"  {employee}")
    
    logger.info("Done")

if __name__ == "__main__":
    main()
