import unittest
from typing import List, Any, Union
import sys
import os

# Add project root to path if needed
project_root = '/home/<USER>/workflow-system/runtime/workflow-engine'
if project_root not in sys.path:
    sys.path.insert(0, project_root)

# Import the function to test
from app.services.system_functions import array_functions

class TestArrayFunctions(unittest.TestCase):
    def test_empty_array(self):
        """Test with empty array for all operations."""
        empty_array = []
        
        # Test each operation with empty array
        self.assertEqual(array_functions(empty_array, "filter"), [])
        self.assertEqual(array_functions(empty_array, "sort"), [])
        self.assertEqual(array_functions(empty_array, "unique"), [])
        self.assertEqual(array_functions(empty_array, "count"), 0)
        
        # Find operation on empty array - the implementation returns [] not None
        self.assertEqual(array_functions(empty_array, "find", value=5), [])
    
    def test_filter_by_key_value(self):
        """Test filtering array of dictionaries by key and value."""
        array = [
            {"id": 1, "name": "<PERSON>", "active": True},
            {"id": 2, "name": "<PERSON>", "active": False},
            {"id": 3, "name": "<PERSON>", "active": True}
        ]
        
        # Filter active users
        result = array_functions(array, "filter", key="active", value=True)
        expected = [
            {"id": 1, "name": "Alice", "active": True},
            {"id": 3, "name": "Charlie", "active": True}
        ]
        self.assertEqual(result, expected)
        
        # Filter by name
        result = array_functions(array, "filter", key="name", value="Bob")
        expected = [{"id": 2, "name": "Bob", "active": False}]
        self.assertEqual(result, expected)
        
        # Filter with no matches
        result = array_functions(array, "filter", key="name", value="David")
        self.assertEqual(result, [])
    
    def test_filter_without_key(self):
        """Test filtering array without key (removes None and empty values)."""
        array = [1, 0, None, "hello", "", False, True, []]
        
        result = array_functions(array, "filter")
        expected = [1, "hello", True]  # Only truthy values remain
        self.assertEqual(result, expected)
    
    def test_filter_non_dict_with_key(self):
        """Test filtering non-dictionary array with key (should raise error)."""
        array = [1, 2, 3, 4, 5]
        
        with self.assertRaises(ValueError):
            array_functions(array, "filter", key="id", value=3)
    
    def test_sort_basic(self):
        """Test basic sorting of array."""
        array = [5, 3, 1, 4, 2]
        
        result = array_functions(array, "sort")
        expected = [1, 2, 3, 4, 5]
        self.assertEqual(result, expected)
    
    def test_sort_by_key(self):
        """Test sorting array of dictionaries by key."""
        array = [
            {"id": 3, "name": "Charlie"},
            {"id": 1, "name": "Alice"},
            {"id": 2, "name": "Bob"}
        ]
        
        # Sort by id
        result = array_functions(array, "sort", key="id")
        expected = [
            {"id": 1, "name": "Alice"},
            {"id": 2, "name": "Bob"},
            {"id": 3, "name": "Charlie"}
        ]
        self.assertEqual(result, expected)
        
        # Sort by name
        result = array_functions(array, "sort", key="name")
        expected = [
            {"id": 1, "name": "Alice"},
            {"id": 2, "name": "Bob"},
            {"id": 3, "name": "Charlie"}
        ]
        self.assertEqual(result, expected)
    
    def test_sort_non_sortable(self):
        """Test sorting array of non-sortable items (should return original array)."""
        # Array with complex objects that aren't comparable
        array = [
            {"a": 1},
            set([1, 2, 3]),
            [1, 2, 3]
        ]
        
        result = array_functions(array, "sort")
        self.assertEqual(result, array)  # Should return unsorted
    
    def test_unique_basic(self):
        """Test getting unique values from array."""
        array = [1, 2, 2, 3, 3, 3, 4, 4, 4, 4]
        
        result = array_functions(array, "unique")
        expected = [1, 2, 3, 4]
        self.assertEqual(result, expected)
    
    def test_unique_by_key(self):
        """Test getting unique values by key from array of dictionaries."""
        array = [
            {"id": 1, "category": "A"},
            {"id": 2, "category": "B"},
            {"id": 3, "category": "A"},
            {"id": 4, "category": "C"},
            {"id": 5, "category": "B"}
        ]
        
        result = array_functions(array, "unique", key="category")
        expected = ["A", "B", "C"]
        self.assertEqual(result, expected)
    
    def test_unique_unhashable(self):
        """Test getting unique values with unhashable types."""
        array = [
            {"x": 1, "y": 2},
            {"x": 1, "y": 2},  # Duplicate
            {"x": 3, "y": 4},
            {"a": 1, "b": 2},  # Different structure
        ]
        
        result = array_functions(array, "unique")
        # Result should contain one of each unique dictionary based on string representation
        self.assertEqual(len(result), 3)
        
        # Check that each expected item appears in result
        expected_items = [
            {"x": 1, "y": 2},
            {"x": 3, "y": 4},
            {"a": 1, "b": 2}
        ]
        for item in expected_items:
            self.assertTrue(any(item == res for res in result), f"{item} not found in result")
    
    def test_map_by_key(self):
        """Test mapping array of dictionaries by key."""
        array = [
            {"id": 1, "name": "Alice", "age": 30},
            {"id": 2, "name": "Bob", "age": 25},
            {"id": 3, "name": "Charlie", "age": 35}
        ]
        
        # Map to get names
        result = array_functions(array, "map", key="name")
        expected = ["Alice", "Bob", "Charlie"]
        self.assertEqual(result, expected)
        
        # Map to get ages
        result = array_functions(array, "map", key="age")
        expected = [30, 25, 35]
        self.assertEqual(result, expected)
    
    def test_map_non_dict(self):
        """Test mapping non-dictionary array by key (should raise error)."""
        array = [1, 2, 3, 4, 5]
        
        with self.assertRaises(ValueError):
            array_functions(array, "map", key="id")
    
    def test_count_total(self):
        """Test counting total items in array."""
        array = [1, 2, 3, 4, 5]
        
        result = array_functions(array, "count")
        self.assertEqual(result, 5)
    
    def test_count_by_key_value(self):
        """Test counting items by key and value."""
        array = [
            {"id": 1, "status": "active"},
            {"id": 2, "status": "inactive"},
            {"id": 3, "status": "active"},
            {"id": 4, "status": "active"},
            {"id": 5, "status": "inactive"}
        ]
        
        # Count active items
        result = array_functions(array, "count", key="status", value="active")
        self.assertEqual(result, 3)
        
        # Count inactive items
        result = array_functions(array, "count", key="status", value="inactive")
        self.assertEqual(result, 2)
        
        # Count non-existent status
        result = array_functions(array, "count", key="status", value="pending")
        self.assertEqual(result, 0)
    
    def test_find_by_value(self):
        """Test finding item by value."""
        array = [1, 2, 3, 4, 5]
        
        # Find existing value
        result = array_functions(array, "find", value=3)
        self.assertEqual(result, 3)
        
        # Find non-existent value
        result = array_functions(array, "find", value=10)
        self.assertEqual(result, None)
    
    def test_find_by_key_value(self):
        """Test finding dictionary by key and value."""
        array = [
            {"id": 1, "name": "Alice"},
            {"id": 2, "name": "Bob"},
            {"id": 3, "name": "Charlie"}
        ]
        
        # Find by id
        result = array_functions(array, "find", key="id", value=2)
        expected = {"id": 2, "name": "Bob"}
        self.assertEqual(result, expected)
        
        # Find by name
        result = array_functions(array, "find", key="name", value="Charlie")
        expected = {"id": 3, "name": "Charlie"}
        self.assertEqual(result, expected)
        
        # Find non-existent
        result = array_functions(array, "find", key="name", value="David")
        self.assertEqual(result, None)
    
    def test_invalid_operation(self):
        """Test with invalid operation (should raise error)."""
        array = [1, 2, 3]
        
        with self.assertRaises(ValueError):
            array_functions(array, "invalid_op")
    
    def test_case_insensitive_operation(self):
        """Test that operation is case-insensitive."""
        array = [5, 3, 1, 4, 2]
        
        # Test with uppercase operation
        result = array_functions(array, "SORT")
        expected = [1, 2, 3, 4, 5]
        self.assertEqual(result, expected)
        
        # Test with mixed case operation
        result = array_functions(array, "UniQue")
        expected = [5, 3, 1, 4, 2]  # All values are already unique
        self.assertEqual(result, expected)
    
    def test_map_empty_array(self):
        """Test mapping an empty array."""
        empty_array = []
        
        # The function actually returns [] for map operation on empty array,
        # not raising a ValueError
        result = array_functions(empty_array, "map", key="name")
        self.assertEqual(result, [])

if __name__ == '__main__':
    unittest.main()