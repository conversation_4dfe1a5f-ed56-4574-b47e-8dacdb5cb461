from fastapi import APIRouter, Depends, HTTPException
from typing import List, Dict, Optional
from app.services.workflow import workflow_service
from app.models.workflow import (
    GlobalObjective, 
    LocalObjective, 
    WorkflowExecutionCreate, 
    WorkflowExecutionResponse
)

router = APIRouter(prefix="/workflow", tags=["Workflow"])

@router.post("/global-objective", response_model=GlobalObjective)
def create_global_objective(
    tenant_id: str, 
    name: str, 
    description: Optional[str] = "", 
    configuration: Optional[Dict] = None
):
    """
    Create a new Global Objective
    
    Args:
        tenant_id (str): Tenant identifier
        name (str): Name of the global objective
        description (str, optional): Description of the global objective
        configuration (dict, optional): Additional configuration details
    
    Returns:
        GlobalObjective: Created global objective
    """
    try:
        global_objective = workflow_service.create_global_objective(
            tenant_id=tenant_id,
            name=name,
            description=description,
            configuration=configuration or {}
        )
        return global_objective
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))

@router.post("/local-objective", response_model=LocalObjective)
def create_local_objective(
    tenant_id: str, 
    global_objective_id: str, 
    name: str, 
    description: Optional[str] = "", 
    order: int = 0,
    configuration: Optional[Dict] = None
):
    """
    Create a new Local Objective within a Global Objective
    
    Args:
        tenant_id (str): Tenant identifier
        global_objective_id (str): Parent Global Objective ID
        name (str): Name of the local objective
        description (str, optional): Description of the local objective
        order (int, optional): Execution order within the global objective
        configuration (dict, optional): Additional configuration details
    
    Returns:
        LocalObjective: Created local objective
    """
    try:
        local_objective = workflow_service.create_local_objective(
            tenant_id=tenant_id,
            global_objective_id=global_objective_id,
            name=name,
            description=description,
            order=order,
            configuration=configuration or {}
        )
        return local_objective
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))

@router.get("/global-objectives", response_model=List[GlobalObjective])
def get_global_objectives(
    tenant_id: str, 
    is_active: Optional[bool] = True
):
    """
    Retrieve Global Objectives for a tenant
    
    Args:
        tenant_id (str): Tenant identifier
        is_active (bool, optional): Filter by active status
    
    Returns:
        List[GlobalObjective]: List of global objectives
    """
    try:
        global_objectives = workflow_service.get_global_objectives(
            tenant_id=tenant_id,
            is_active=is_active
        )
        return global_objectives
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))

@router.post("/execution-log", response_model=WorkflowExecutionResponse)
def log_workflow_execution(
    global_objective_id: str,
    local_objective_id: str,
    tenant_id: str,
    status: str,
    execution_metadata: Optional[Dict] = None
):
    """
    Log workflow execution details
    
    Args:
        global_objective_id (str): Global Objective ID
        local_objective_id (str): Local Objective ID
        tenant_id (str): Tenant identifier
        status (str): Execution status
        execution_metadata (dict, optional): Additional execution metadata
    
    Returns:
        WorkflowExecutionResponse: Created workflow execution log
    """
    try:
        execution_log = workflow_service.log_workflow_execution(
            global_objective_id=global_objective_id,
            local_objective_id=local_objective_id,
            tenant_id=tenant_id,
            status=status,
            execution_metadata=execution_metadata or {}
        )
        return execution_log
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))
def get_global_objectives(
    tenant_id: str, 
    is_active: bool = True
):
    """
    Retrieve Global Objectives for a tenant
    
    Args:
        tenant_id (str): Tenant identifier
        is_active (bool, optional): Filter by active status
    
    Returns:
        List[GlobalObjective]: List of global objectives
    """
    try:
        global_objectives = workflow_service.get_global_objectives(
            tenant_id=tenant_id,
            is_active=is_active
        )
        return global_objectives
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))

@router.post("/execution-log", response_model=WorkflowExecutionResponse)
def log_workflow_execution(
    global_objective_id: str,
    local_objective_id: str,
    tenant_id: str,
    status: str,
    execution_metadata: Dict = None
):
    """
    Log workflow execution details
    
    Args:
        global_objective_id (str): Global Objective ID
        local_objective_id (str): Local Objective ID
        tenant_id (str): Tenant identifier
        status (str): Execution status
        execution_metadata (dict, optional): Additional execution metadata
    
    Returns:
        WorkflowExecutionResponse: Created workflow execution log
    """
    try:
        execution_log = workflow_service.log_workflow_execution(
            global_objective_id=global_objective_id,
            local_objective_id=local_objective_id,
            tenant_id=tenant_id,
            status=status,
            execution_metadata=execution_metadata or {}
        )
        return execution_log
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))
