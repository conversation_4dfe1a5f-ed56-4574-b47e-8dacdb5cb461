#!/usr/bin/env python3
"""
Script to run the LO parser and deployer.
"""

import os
import sys
import logging
import argparse
from datetime import datetime

# Configure logging
log_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'logs')
os.makedirs(log_dir, exist_ok=True)
log_file = os.path.join(log_dir, f'lo_parser_deployer_run_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log')

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(log_file),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

# Add parent directory to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Import parser and deployer
from parsers.lo_parser import parse_lo_definitions
from deployers.lo_deployer import deploy_lo_definitions
from db_utils import get_db_connection

def run_lo_parser_deployer(input_file: str, schema_name: str = "workflow_temp", skip_validation: bool = False):
    """
    Run the LO parser and deployer.
    
    Args:
        input_file: Path to the input file containing LO definitions
        schema_name: Name of the database schema to use
        skip_validation: Whether to skip validation
    """
    logger.info(f"Running LO parser and deployer with input file: {input_file}")
    logger.info(f"Using schema: {schema_name}")
    logger.info(f"Skip validation: {skip_validation}")
    
    # Read input file
    try:
        with open(input_file, 'r') as f:
            input_text = f.read()
    except Exception as e:
        logger.error(f"Error reading input file: {str(e)}")
        return
    
    # Parse LO definitions
    logger.info("Parsing LO definitions...")
    try:
        lo_definitions, warnings = parse_lo_definitions(input_text)
    except Exception as e:
        logger.error(f"Error parsing LO definitions: {str(e)}")
        return
    
    # Print warnings
    if warnings:
        logger.warning("Warnings during parsing:")
        for warning in warnings:
            logger.warning(f"  - {warning}")
    
    # Print parsed LO definitions
    logger.info(f"Parsed {len(lo_definitions)} LO definitions:")
    for lo_name, lo_definition in lo_definitions.items():
        logger.info(f"  - {lo_name}")
    
    # Validate LO definitions
    if not skip_validation:
        logger.info("Validating LO definitions...")
        # TODO: Implement validation
    
    # Deploy LO definitions
    logger.info("Deploying LO definitions...")
    try:
        success, messages = deploy_lo_definitions(lo_definitions, schema_name=schema_name)
    except Exception as e:
        logger.error(f"Error deploying LO definitions: {str(e)}")
        return
    
    # Print deployment messages
    for message in messages:
        if "Error" in message or "Failed" in message:
            logger.error(f"  - {message}")
        else:
            logger.info(f"  - {message}")
    
    # Print deployment status
    if success:
        logger.info("Deployment successful!")
    else:
        logger.error("Deployment failed!")

def main():
    """
    Main function.
    """
    # Parse command line arguments
    parser = argparse.ArgumentParser(description='Run the LO parser and deployer.')
    parser.add_argument('input_file', help='Path to the input file containing LO definitions')
    parser.add_argument('--schema', default='workflow_temp', help='Name of the database schema to use')
    parser.add_argument('--skip-validation', action='store_true', help='Skip validation')
    args = parser.parse_args()
    
    # Run LO parser and deployer
    run_lo_parser_deployer(args.input_file, args.schema, args.skip_validation)

if __name__ == "__main__":
    main()
