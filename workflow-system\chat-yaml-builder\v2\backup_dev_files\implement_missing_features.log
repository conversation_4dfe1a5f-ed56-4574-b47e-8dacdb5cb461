2025-05-12 11:11:53,397 - implement_missing_features - INFO - Starting to implement missing features
2025-05-12 11:11:53,397 - implement_missing_features - INFO - === Fixing Enum Values ===
2025-05-12 11:11:53,397 - implement_missing_features - INFO - Fixing enum values table
2025-05-12 11:11:53,403 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 11:11:53,404 - implement_missing_features - INFO - No enum attributes found
2025-05-12 11:11:53,404 - implement_missing_features - INFO - No enum attributes found
2025-05-12 11:11:53,404 - implement_missing_features - INFO - === Fixing Validations ===
2025-05-12 11:11:53,404 - implement_missing_features - INFO - Fixing validations table
2025-05-12 11:11:53,409 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 11:11:53,410 - implement_missing_features - INFO - Processing entity E1 (Employee)
2025-05-12 11:11:53,414 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 11:11:53,420 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 11:11:53,421 - implement_missing_features - WARNING - Attribute 'status' not found for entity E1
2025-05-12 11:11:53,425 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 11:11:53,426 - db_utils - ERROR - Database error: duplicate key value violates unique constraint "attribute_validations_attribute_id_validation_name_key"
DETAIL:  Key (attribute_id, validation_name)=(E1.At9, hireDate_at_least_90_days_before_Employ) already exists.
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 54, in execute_query
    cursor.execute(query, params)
psycopg2.errors.UniqueViolation: duplicate key value violates unique constraint "attribute_validations_attribute_id_validation_name_key"
DETAIL:  Key (attribute_id, validation_name)=(E1.At9, hireDate_at_least_90_days_before_Employ) already exists.

2025-05-12 11:11:53,427 - implement_missing_features - WARNING - Failed to insert validation for attribute E1.At9: ['Database error: duplicate key value violates unique constraint "attribute_validations_attribute_id_validation_name_key"\nDETAIL:  Key (attribute_id, validation_name)=(E1.At9, hireDate_at_least_90_days_before_Employ) already exists.\n']
2025-05-12 11:11:53,427 - implement_missing_features - INFO - Processing entity E2 (Department)
2025-05-12 11:11:53,430 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 11:11:53,435 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 11:11:53,439 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 11:11:53,440 - db_utils - ERROR - Database error: duplicate key value violates unique constraint "attribute_validations_attribute_id_validation_name_key"
DETAIL:  Key (attribute_id, validation_name)=(E2.At5, budget_approved_by_Finance_before_cha) already exists.
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 54, in execute_query
    cursor.execute(query, params)
psycopg2.errors.UniqueViolation: duplicate key value violates unique constraint "attribute_validations_attribute_id_validation_name_key"
DETAIL:  Key (attribute_id, validation_name)=(E2.At5, budget_approved_by_Finance_before_cha) already exists.

2025-05-12 11:11:53,440 - implement_missing_features - WARNING - Failed to insert validation for attribute E2.At5: ['Database error: duplicate key value violates unique constraint "attribute_validations_attribute_id_validation_name_key"\nDETAIL:  Key (attribute_id, validation_name)=(E2.At5, budget_approved_by_Finance_before_cha) already exists.\n']
2025-05-12 11:11:53,440 - implement_missing_features - INFO - Validations table fixed successfully
2025-05-12 11:11:53,440 - implement_missing_features - INFO - Warning: Attribute 'status' not found for entity E1
2025-05-12 11:11:53,440 - implement_missing_features - INFO - Database error: duplicate key value violates unique constraint "attribute_validations_attribute_id_validation_name_key"
DETAIL:  Key (attribute_id, validation_name)=(E1.At9, hireDate_at_least_90_days_before_Employ) already exists.

2025-05-12 11:11:53,440 - implement_missing_features - INFO - Database error: duplicate key value violates unique constraint "attribute_validations_attribute_id_validation_name_key"
DETAIL:  Key (attribute_id, validation_name)=(E2.At5, budget_approved_by_Finance_before_cha) already exists.

2025-05-12 11:11:53,440 - implement_missing_features - INFO - Validations table fixed successfully
2025-05-12 11:11:53,441 - implement_missing_features - INFO - === Fixing Entity IDs ===
2025-05-12 11:11:53,441 - implement_missing_features - INFO - Fixing entity IDs
2025-05-12 11:11:53,444 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 11:11:53,446 - implement_missing_features - INFO - Entity IDs already use sequential IDs
2025-05-12 11:11:53,446 - implement_missing_features - INFO - Entity IDs already use sequential IDs
2025-05-12 11:11:53,446 - implement_missing_features - INFO - === Fixing Relationship References ===
2025-05-12 11:11:53,446 - implement_missing_features - INFO - Fixing relationship references
2025-05-12 11:11:53,450 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 11:11:53,455 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 11:11:53,459 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 11:11:53,464 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 11:11:53,469 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 11:11:53,473 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 11:11:53,478 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 11:11:53,482 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 11:11:53,483 - implement_missing_features - INFO - Relationship references fixed successfully
2025-05-12 11:11:53,484 - implement_missing_features - INFO - Relationship references fixed successfully
2025-05-12 11:11:53,484 - implement_missing_features - INFO - === Fixing Foreign Key Constraints ===
2025-05-12 11:11:53,484 - implement_missing_features - INFO - Fixing foreign key constraints
2025-05-12 11:11:53,488 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 11:11:53,493 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 11:11:53,507 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 11:11:53,519 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 11:11:53,531 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 11:11:53,544 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 11:11:53,556 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 11:11:53,567 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 11:11:53,579 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 11:11:53,590 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 11:11:53,602 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 11:11:53,611 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 11:11:53,623 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 11:11:53,639 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 11:11:53,645 - implement_missing_features - INFO - Foreign key constraints fixed successfully
2025-05-12 11:11:53,645 - implement_missing_features - INFO - Foreign key constraints fixed successfully
2025-05-12 11:11:53,646 - implement_missing_features - INFO - === Optimizing Database Connections ===
2025-05-12 11:11:53,646 - implement_missing_features - INFO - Optimizing database connections
2025-05-12 11:11:53,646 - implement_missing_features - INFO - Database connections optimized successfully
2025-05-12 11:11:53,646 - implement_missing_features - INFO - Database connections optimized successfully
2025-05-12 11:11:53,646 - implement_missing_features - INFO - All fixes completed successfully
2025-05-12 11:19:47,539 - implement_missing_features - INFO - Starting to implement missing features
2025-05-12 11:19:47,539 - implement_missing_features - INFO - === Fixing Enum Values ===
2025-05-12 11:19:47,539 - implement_missing_features - INFO - Fixing enum values table
2025-05-12 11:19:47,545 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 11:19:47,546 - implement_missing_features - INFO - No enum attributes found
2025-05-12 11:19:47,546 - implement_missing_features - INFO - No enum attributes found
2025-05-12 11:19:47,546 - implement_missing_features - INFO - === Fixing Validations ===
2025-05-12 11:19:47,546 - implement_missing_features - INFO - Fixing validations table
2025-05-12 11:19:47,551 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 11:19:47,556 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 11:19:47,561 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 11:19:47,565 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 11:19:47,566 - implement_missing_features - INFO - Processing entity E1 (Employee)
2025-05-12 11:19:47,570 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 11:19:47,571 - implement_missing_features - WARNING - Attribute 'status' not found for entity E1
2025-05-12 11:19:47,574 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 11:19:47,576 - implement_missing_features - INFO - Inserted validation 'hireDate_be_at_least_90_days_before_Emp' for attribute E1.At9
2025-05-12 11:19:47,577 - implement_missing_features - INFO - Processing entity E2 (Department)
2025-05-12 11:19:47,580 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 11:19:47,585 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 11:19:47,586 - implement_missing_features - INFO - Inserted validation 'budget_be_approved_by_Finance_before_' for attribute E2.At5
2025-05-12 11:19:47,590 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 11:19:47,592 - implement_missing_features - INFO - Inserted validation 'managerId_reference_an_Employee_with_sta' for attribute E2.At3
2025-05-12 11:19:47,596 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 11:19:47,597 - implement_missing_features - INFO - Inserted validation 'status_must_be_active_for_performance_reviews' for attribute E1.At10
2025-05-12 11:19:47,601 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 11:19:47,602 - implement_missing_features - INFO - Inserted validation 'hireDate_at_least_90_days_before_performance_rating' for attribute E1.At9
2025-05-12 11:19:47,606 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 11:19:47,607 - implement_missing_features - INFO - Inserted validation 'budget_approved_by_finance_before_changes' for attribute E2.At5
2025-05-12 11:19:47,610 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 11:19:47,612 - implement_missing_features - INFO - Inserted validation 'manager_must_be_active' for attribute E2.At3
2025-05-12 11:19:47,612 - implement_missing_features - INFO - Validations table fixed successfully
2025-05-12 11:19:47,612 - implement_missing_features - INFO - Warning: Attribute 'status' not found for entity E1
2025-05-12 11:19:47,612 - implement_missing_features - INFO - Inserted validation 'hireDate_be_at_least_90_days_before_Emp' for attribute E1.At9
2025-05-12 11:19:47,612 - implement_missing_features - INFO - Inserted validation 'budget_be_approved_by_Finance_before_' for attribute E2.At5
2025-05-12 11:19:47,612 - implement_missing_features - INFO - Inserted validation 'managerId_reference_an_Employee_with_sta' for attribute E2.At3
2025-05-12 11:19:47,612 - implement_missing_features - INFO - Inserted validation 'status_must_be_active_for_performance_reviews' for attribute E1.At10
2025-05-12 11:19:47,612 - implement_missing_features - INFO - Inserted validation 'hireDate_at_least_90_days_before_performance_rating' for attribute E1.At9
2025-05-12 11:19:47,612 - implement_missing_features - INFO - Inserted validation 'budget_approved_by_finance_before_changes' for attribute E2.At5
2025-05-12 11:19:47,612 - implement_missing_features - INFO - Inserted validation 'manager_must_be_active' for attribute E2.At3
2025-05-12 11:19:47,612 - implement_missing_features - INFO - Validations table fixed successfully
2025-05-12 11:19:47,612 - implement_missing_features - INFO - === Fixing Entity IDs ===
2025-05-12 11:19:47,612 - implement_missing_features - INFO - Fixing entity IDs
2025-05-12 11:19:47,615 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 11:19:47,616 - implement_missing_features - INFO - Entity IDs already use sequential IDs
2025-05-12 11:19:47,616 - implement_missing_features - INFO - Entity IDs already use sequential IDs
2025-05-12 11:19:47,616 - implement_missing_features - INFO - === Fixing Relationship References ===
2025-05-12 11:19:47,616 - implement_missing_features - INFO - Fixing relationship references
2025-05-12 11:19:47,620 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 11:19:47,624 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 11:19:47,629 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 11:19:47,633 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 11:19:47,637 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 11:19:47,641 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 11:19:47,646 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 11:19:47,650 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 11:19:47,651 - implement_missing_features - INFO - Relationship references fixed successfully
2025-05-12 11:19:47,651 - implement_missing_features - INFO - Relationship references fixed successfully
2025-05-12 11:19:47,651 - implement_missing_features - INFO - === Fixing Foreign Key Constraints ===
2025-05-12 11:19:47,651 - implement_missing_features - INFO - Fixing foreign key constraints
2025-05-12 11:19:47,654 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 11:19:47,659 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 11:19:47,671 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 11:19:47,683 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 11:19:47,695 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 11:19:47,707 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 11:19:47,719 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 11:19:47,730 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 11:19:47,740 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 11:19:47,753 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 11:19:47,764 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 11:19:47,776 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 11:19:47,789 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 11:19:47,803 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 11:19:47,809 - implement_missing_features - INFO - Foreign key constraints fixed successfully
2025-05-12 11:19:47,809 - implement_missing_features - INFO - Foreign key constraints fixed successfully
2025-05-12 11:19:47,809 - implement_missing_features - INFO - === Optimizing Database Connections ===
2025-05-12 11:19:47,809 - implement_missing_features - INFO - Optimizing database connections
2025-05-12 11:19:47,809 - implement_missing_features - INFO - component_deployer.py already has connection pooling
2025-05-12 11:19:47,809 - implement_missing_features - INFO - component_deployer.py already has connection pooling
2025-05-12 11:19:47,809 - implement_missing_features - INFO - All fixes completed successfully
2025-05-12 11:21:52,809 - implement_missing_features - INFO - Starting to implement missing features
2025-05-12 11:21:52,809 - implement_missing_features - INFO - === Fixing Enum Values ===
2025-05-12 11:21:52,809 - implement_missing_features - INFO - Fixing enum values table
2025-05-12 11:21:52,814 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 11:21:52,815 - implement_missing_features - INFO - No enum attributes found
2025-05-12 11:21:52,815 - implement_missing_features - INFO - No enum attributes found
2025-05-12 11:21:52,815 - implement_missing_features - INFO - === Fixing Validations ===
2025-05-12 11:21:52,815 - implement_missing_features - INFO - Fixing validations table
2025-05-12 11:21:52,820 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 11:21:52,825 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 11:21:52,830 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 11:21:52,834 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 11:21:52,835 - implement_missing_features - INFO - Processing entity E1 (Employee)
2025-05-12 11:21:52,839 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 11:21:52,840 - implement_missing_features - WARNING - Attribute 'status' not found for entity E1
2025-05-12 11:21:52,844 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 11:21:52,845 - implement_missing_features - INFO - Inserted validation 'hireDate_be_at_least_90_days_before_Emp' for attribute E1.At9
2025-05-12 11:21:52,845 - implement_missing_features - INFO - Processing entity E2 (Department)
2025-05-12 11:21:52,849 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 11:21:52,853 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 11:21:52,855 - implement_missing_features - INFO - Inserted validation 'budget_be_approved_by_Finance_before_' for attribute E2.At5
2025-05-12 11:21:52,858 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 11:21:52,860 - implement_missing_features - INFO - Inserted validation 'managerId_reference_an_Employee_with_sta' for attribute E2.At3
2025-05-12 11:21:52,864 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 11:21:52,866 - implement_missing_features - INFO - Inserted validation 'status_must_be_active_for_performance_reviews' for attribute E1.At10
2025-05-12 11:21:52,870 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 11:21:52,871 - implement_missing_features - INFO - Inserted validation 'hireDate_at_least_90_days_before_performance_rating' for attribute E1.At9
2025-05-12 11:21:52,875 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 11:21:52,877 - implement_missing_features - INFO - Inserted validation 'budget_approved_by_finance_before_changes' for attribute E2.At5
2025-05-12 11:21:52,881 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 11:21:52,882 - implement_missing_features - INFO - Inserted validation 'manager_must_be_active' for attribute E2.At3
2025-05-12 11:21:52,882 - implement_missing_features - INFO - Validations table fixed successfully
2025-05-12 11:21:52,882 - implement_missing_features - INFO - Warning: Attribute 'status' not found for entity E1
2025-05-12 11:21:52,882 - implement_missing_features - INFO - Inserted validation 'hireDate_be_at_least_90_days_before_Emp' for attribute E1.At9
2025-05-12 11:21:52,882 - implement_missing_features - INFO - Inserted validation 'budget_be_approved_by_Finance_before_' for attribute E2.At5
2025-05-12 11:21:52,882 - implement_missing_features - INFO - Inserted validation 'managerId_reference_an_Employee_with_sta' for attribute E2.At3
2025-05-12 11:21:52,882 - implement_missing_features - INFO - Inserted validation 'status_must_be_active_for_performance_reviews' for attribute E1.At10
2025-05-12 11:21:52,882 - implement_missing_features - INFO - Inserted validation 'hireDate_at_least_90_days_before_performance_rating' for attribute E1.At9
2025-05-12 11:21:52,882 - implement_missing_features - INFO - Inserted validation 'budget_approved_by_finance_before_changes' for attribute E2.At5
2025-05-12 11:21:52,882 - implement_missing_features - INFO - Inserted validation 'manager_must_be_active' for attribute E2.At3
2025-05-12 11:21:52,882 - implement_missing_features - INFO - Validations table fixed successfully
2025-05-12 11:21:52,882 - implement_missing_features - INFO - === Fixing Entity IDs ===
2025-05-12 11:21:52,882 - implement_missing_features - INFO - Fixing entity IDs
2025-05-12 11:21:52,886 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 11:21:52,887 - implement_missing_features - INFO - Entity IDs already use sequential IDs
2025-05-12 11:21:52,887 - implement_missing_features - INFO - Entity IDs already use sequential IDs
2025-05-12 11:21:52,887 - implement_missing_features - INFO - === Fixing Relationship References ===
2025-05-12 11:21:52,887 - implement_missing_features - INFO - Fixing relationship references
2025-05-12 11:21:52,891 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 11:21:52,895 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 11:21:52,900 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 11:21:52,905 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 11:21:52,910 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 11:21:52,915 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 11:21:52,919 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 11:21:52,924 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 11:21:52,925 - implement_missing_features - INFO - Relationship references fixed successfully
2025-05-12 11:21:52,925 - implement_missing_features - INFO - Relationship references fixed successfully
2025-05-12 11:21:52,925 - implement_missing_features - INFO - === Fixing Foreign Key Constraints ===
2025-05-12 11:21:52,925 - implement_missing_features - INFO - Fixing foreign key constraints
2025-05-12 11:21:52,929 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 11:21:52,935 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 11:21:52,947 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 11:21:52,959 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 11:21:52,972 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 11:21:52,984 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 11:21:52,995 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 11:21:53,007 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 11:21:53,016 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 11:21:53,027 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 11:21:53,038 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 11:21:53,048 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 11:21:53,060 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 11:21:53,073 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 11:21:53,078 - implement_missing_features - INFO - Foreign key constraints fixed successfully
2025-05-12 11:21:53,079 - implement_missing_features - INFO - Foreign key constraints fixed successfully
2025-05-12 11:21:53,079 - implement_missing_features - INFO - === Optimizing Database Connections ===
2025-05-12 11:21:53,079 - implement_missing_features - INFO - Optimizing database connections
2025-05-12 11:21:53,079 - implement_missing_features - INFO - component_deployer.py already has connection pooling
2025-05-12 11:21:53,079 - implement_missing_features - INFO - component_deployer.py already has connection pooling
2025-05-12 11:21:53,079 - implement_missing_features - INFO - All fixes completed successfully
2025-05-12 11:57:22,550 - implement_missing_features - INFO - Starting to implement missing features
2025-05-12 11:57:22,550 - implement_missing_features - INFO - === Fixing Enum Values ===
2025-05-12 11:57:22,550 - implement_missing_features - INFO - Fixing enum values table
2025-05-12 11:57:22,555 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 11:57:22,557 - implement_missing_features - INFO - No enum attributes found
2025-05-12 11:57:22,557 - implement_missing_features - INFO - No enum attributes found
2025-05-12 11:57:22,557 - implement_missing_features - INFO - === Fixing Validations ===
2025-05-12 11:57:22,557 - implement_missing_features - INFO - Fixing validations table
2025-05-12 11:57:22,561 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 11:57:22,565 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 11:57:22,568 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 11:57:22,572 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 11:57:22,573 - implement_missing_features - INFO - Processing entity E1 (Employee)
2025-05-12 11:57:22,576 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 11:57:22,577 - implement_missing_features - WARNING - Attribute 'status' not found for entity E1
2025-05-12 11:57:22,581 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 11:57:22,582 - implement_missing_features - INFO - Inserted validation 'hireDate_be_at_least_90_days_before_Emp' for attribute E1.At9
2025-05-12 11:57:22,582 - implement_missing_features - INFO - Processing entity E2 (Department)
2025-05-12 11:57:22,587 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 11:57:22,593 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 11:57:22,595 - implement_missing_features - INFO - Inserted validation 'budget_be_approved_by_Finance_before_' for attribute E2.At5
2025-05-12 11:57:22,599 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 11:57:22,600 - implement_missing_features - INFO - Inserted validation 'managerId_reference_an_Employee_with_sta' for attribute E2.At3
2025-05-12 11:57:22,603 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 11:57:22,604 - implement_missing_features - INFO - Inserted validation 'status_must_be_active_for_performance_reviews' for attribute E1.At10
2025-05-12 11:57:22,609 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 11:57:22,611 - implement_missing_features - INFO - Inserted validation 'hireDate_at_least_90_days_before_performance_rating' for attribute E1.At9
2025-05-12 11:57:22,615 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 11:57:22,616 - implement_missing_features - INFO - Inserted validation 'budget_approved_by_finance_before_changes' for attribute E2.At5
2025-05-12 11:57:22,620 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 11:57:22,622 - implement_missing_features - INFO - Inserted validation 'manager_must_be_active' for attribute E2.At3
2025-05-12 11:57:22,623 - implement_missing_features - INFO - Validations table fixed successfully
2025-05-12 11:57:22,623 - implement_missing_features - INFO - Warning: Attribute 'status' not found for entity E1
2025-05-12 11:57:22,623 - implement_missing_features - INFO - Inserted validation 'hireDate_be_at_least_90_days_before_Emp' for attribute E1.At9
2025-05-12 11:57:22,623 - implement_missing_features - INFO - Inserted validation 'budget_be_approved_by_Finance_before_' for attribute E2.At5
2025-05-12 11:57:22,623 - implement_missing_features - INFO - Inserted validation 'managerId_reference_an_Employee_with_sta' for attribute E2.At3
2025-05-12 11:57:22,623 - implement_missing_features - INFO - Inserted validation 'status_must_be_active_for_performance_reviews' for attribute E1.At10
2025-05-12 11:57:22,623 - implement_missing_features - INFO - Inserted validation 'hireDate_at_least_90_days_before_performance_rating' for attribute E1.At9
2025-05-12 11:57:22,623 - implement_missing_features - INFO - Inserted validation 'budget_approved_by_finance_before_changes' for attribute E2.At5
2025-05-12 11:57:22,623 - implement_missing_features - INFO - Inserted validation 'manager_must_be_active' for attribute E2.At3
2025-05-12 11:57:22,623 - implement_missing_features - INFO - Validations table fixed successfully
2025-05-12 11:57:22,623 - implement_missing_features - INFO - === Fixing Entity IDs ===
2025-05-12 11:57:22,623 - implement_missing_features - INFO - Fixing entity IDs
2025-05-12 11:57:22,627 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 11:57:22,628 - implement_missing_features - INFO - Entity IDs already use sequential IDs
2025-05-12 11:57:22,628 - implement_missing_features - INFO - Entity IDs already use sequential IDs
2025-05-12 11:57:22,628 - implement_missing_features - INFO - === Fixing Relationship References ===
2025-05-12 11:57:22,628 - implement_missing_features - INFO - Fixing relationship references
2025-05-12 11:57:22,631 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 11:57:22,635 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 11:57:22,640 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 11:57:22,645 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 11:57:22,650 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 11:57:22,654 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 11:57:22,658 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 11:57:22,663 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 11:57:22,664 - implement_missing_features - INFO - Relationship references fixed successfully
2025-05-12 11:57:22,664 - implement_missing_features - INFO - Relationship references fixed successfully
2025-05-12 11:57:22,664 - implement_missing_features - INFO - === Fixing Foreign Key Constraints ===
2025-05-12 11:57:22,664 - implement_missing_features - INFO - Fixing foreign key constraints
2025-05-12 11:57:22,668 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 11:57:22,673 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 11:57:22,686 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 11:57:22,699 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 11:57:22,713 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 11:57:22,727 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 11:57:22,738 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 11:57:22,752 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 11:57:22,762 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 11:57:22,774 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 11:57:22,786 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 11:57:22,796 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 11:57:22,811 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 11:57:22,826 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 11:57:22,832 - implement_missing_features - INFO - Foreign key constraints fixed successfully
2025-05-12 11:57:22,832 - implement_missing_features - INFO - Foreign key constraints fixed successfully
2025-05-12 11:57:22,832 - implement_missing_features - INFO - === Optimizing Database Connections ===
2025-05-12 11:57:22,832 - implement_missing_features - INFO - Optimizing database connections
2025-05-12 11:57:22,832 - implement_missing_features - INFO - component_deployer.py already has connection pooling
2025-05-12 11:57:22,833 - implement_missing_features - INFO - component_deployer.py already has connection pooling
2025-05-12 11:57:22,833 - implement_missing_features - INFO - All fixes completed successfully
2025-05-12 12:11:45,210 - implement_missing_features - INFO - Starting to implement missing features
2025-05-12 12:11:45,210 - implement_missing_features - INFO - === Fixing Enum Values ===
2025-05-12 12:11:45,210 - implement_missing_features - INFO - Fixing enum values table
2025-05-12 12:11:45,216 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 12:11:45,217 - implement_missing_features - INFO - No enum attributes found
2025-05-12 12:11:45,217 - implement_missing_features - INFO - No enum attributes found
2025-05-12 12:11:45,217 - implement_missing_features - INFO - === Fixing Validations ===
2025-05-12 12:11:45,217 - implement_missing_features - INFO - Fixing validations table
2025-05-12 12:11:45,222 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 12:11:45,228 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 12:11:45,233 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 12:11:45,238 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 12:11:45,239 - implement_missing_features - INFO - Processing entity E1 (Employee)
2025-05-12 12:11:45,243 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 12:11:45,244 - implement_missing_features - WARNING - Attribute 'status' not found for entity E1
2025-05-12 12:11:45,248 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 12:11:45,250 - implement_missing_features - INFO - Inserted validation 'hireDate_be_at_least_90_days_before_Emp' for attribute E1.At9
2025-05-12 12:11:45,250 - implement_missing_features - INFO - Processing entity E2 (Department)
2025-05-12 12:11:45,253 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 12:11:45,258 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 12:11:45,259 - implement_missing_features - INFO - Inserted validation 'budget_be_approved_by_Finance_before_' for attribute E2.At5
2025-05-12 12:11:45,263 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 12:11:45,264 - implement_missing_features - INFO - Inserted validation 'managerId_reference_an_Employee_with_sta' for attribute E2.At3
2025-05-12 12:11:45,268 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 12:11:45,269 - implement_missing_features - INFO - Inserted validation 'status_must_be_active_for_performance_reviews' for attribute E1.At10
2025-05-12 12:11:45,273 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 12:11:45,274 - implement_missing_features - INFO - Inserted validation 'hireDate_at_least_90_days_before_performance_rating' for attribute E1.At9
2025-05-12 12:11:45,278 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 12:11:45,279 - implement_missing_features - INFO - Inserted validation 'budget_approved_by_finance_before_changes' for attribute E2.At5
2025-05-12 12:11:45,283 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 12:11:45,284 - implement_missing_features - INFO - Inserted validation 'manager_must_be_active' for attribute E2.At3
2025-05-12 12:11:45,284 - implement_missing_features - INFO - Validations table fixed successfully
2025-05-12 12:11:45,284 - implement_missing_features - INFO - Warning: Attribute 'status' not found for entity E1
2025-05-12 12:11:45,285 - implement_missing_features - INFO - Inserted validation 'hireDate_be_at_least_90_days_before_Emp' for attribute E1.At9
2025-05-12 12:11:45,285 - implement_missing_features - INFO - Inserted validation 'budget_be_approved_by_Finance_before_' for attribute E2.At5
2025-05-12 12:11:45,285 - implement_missing_features - INFO - Inserted validation 'managerId_reference_an_Employee_with_sta' for attribute E2.At3
2025-05-12 12:11:45,285 - implement_missing_features - INFO - Inserted validation 'status_must_be_active_for_performance_reviews' for attribute E1.At10
2025-05-12 12:11:45,285 - implement_missing_features - INFO - Inserted validation 'hireDate_at_least_90_days_before_performance_rating' for attribute E1.At9
2025-05-12 12:11:45,285 - implement_missing_features - INFO - Inserted validation 'budget_approved_by_finance_before_changes' for attribute E2.At5
2025-05-12 12:11:45,285 - implement_missing_features - INFO - Inserted validation 'manager_must_be_active' for attribute E2.At3
2025-05-12 12:11:45,285 - implement_missing_features - INFO - Validations table fixed successfully
2025-05-12 12:11:45,285 - implement_missing_features - INFO - === Fixing Entity IDs ===
2025-05-12 12:11:45,285 - implement_missing_features - INFO - Fixing entity IDs
2025-05-12 12:11:45,288 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 12:11:45,289 - implement_missing_features - INFO - Entity IDs already use sequential IDs
2025-05-12 12:11:45,289 - implement_missing_features - INFO - Entity IDs already use sequential IDs
2025-05-12 12:11:45,289 - implement_missing_features - INFO - === Fixing Relationship References ===
2025-05-12 12:11:45,289 - implement_missing_features - INFO - Fixing relationship references
2025-05-12 12:11:45,293 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 12:11:45,298 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 12:11:45,303 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 12:11:45,308 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 12:11:45,314 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 12:11:45,319 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 12:11:45,323 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 12:11:45,328 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 12:11:45,329 - implement_missing_features - INFO - Relationship references fixed successfully
2025-05-12 12:11:45,329 - implement_missing_features - INFO - Relationship references fixed successfully
2025-05-12 12:11:45,329 - implement_missing_features - INFO - === Fixing Foreign Key Constraints ===
2025-05-12 12:11:45,329 - implement_missing_features - INFO - Fixing foreign key constraints
2025-05-12 12:11:45,333 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 12:11:45,338 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 12:11:45,351 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 12:11:45,362 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 12:11:45,374 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 12:11:45,388 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 12:11:45,400 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 12:11:45,411 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 12:11:45,420 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 12:11:45,431 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 12:11:45,443 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 12:11:45,454 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 12:11:45,466 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 12:11:45,482 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 12:11:45,487 - implement_missing_features - INFO - Foreign key constraints fixed successfully
2025-05-12 12:11:45,488 - implement_missing_features - INFO - Foreign key constraints fixed successfully
2025-05-12 12:11:45,488 - implement_missing_features - INFO - === Optimizing Database Connections ===
2025-05-12 12:11:45,488 - implement_missing_features - INFO - Optimizing database connections
2025-05-12 12:11:45,488 - implement_missing_features - INFO - component_deployer.py already has connection pooling
2025-05-12 12:11:45,488 - implement_missing_features - INFO - component_deployer.py already has connection pooling
2025-05-12 12:11:45,488 - implement_missing_features - INFO - All fixes completed successfully
2025-05-12 12:15:04,055 - implement_missing_features - INFO - Starting to implement missing features
2025-05-12 12:15:04,055 - implement_missing_features - INFO - === Fixing Enum Values ===
2025-05-12 12:15:04,055 - implement_missing_features - INFO - Fixing enum values table
2025-05-12 12:15:04,060 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 12:15:04,062 - implement_missing_features - INFO - No enum attributes found
2025-05-12 12:15:04,062 - implement_missing_features - INFO - No enum attributes found
2025-05-12 12:15:04,062 - implement_missing_features - INFO - === Fixing Validations ===
2025-05-12 12:15:04,062 - implement_missing_features - INFO - Fixing validations table
2025-05-12 12:15:04,067 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 12:15:04,072 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 12:15:04,076 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 12:15:04,081 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 12:15:04,082 - implement_missing_features - INFO - Processing entity E1 (Employee)
2025-05-12 12:15:04,087 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 12:15:04,088 - implement_missing_features - WARNING - Attribute 'status' not found for entity E1
2025-05-12 12:15:04,091 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 12:15:04,093 - implement_missing_features - INFO - Inserted validation 'hireDate_be_at_least_90_days_before_Emp' for attribute E1.At9
2025-05-12 12:15:04,093 - implement_missing_features - INFO - Processing entity E2 (Department)
2025-05-12 12:15:04,097 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 12:15:04,101 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 12:15:04,102 - implement_missing_features - INFO - Inserted validation 'budget_be_approved_by_Finance_before_' for attribute E2.At5
2025-05-12 12:15:04,106 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 12:15:04,107 - implement_missing_features - INFO - Inserted validation 'managerId_reference_an_Employee_with_sta' for attribute E2.At3
2025-05-12 12:15:04,111 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 12:15:04,112 - implement_missing_features - INFO - Inserted validation 'status_must_be_active_for_performance_reviews' for attribute E1.At10
2025-05-12 12:15:04,115 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 12:15:04,117 - implement_missing_features - INFO - Inserted validation 'hireDate_at_least_90_days_before_performance_rating' for attribute E1.At9
2025-05-12 12:15:04,120 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 12:15:04,122 - implement_missing_features - INFO - Inserted validation 'budget_approved_by_finance_before_changes' for attribute E2.At5
2025-05-12 12:15:04,125 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 12:15:04,126 - implement_missing_features - INFO - Inserted validation 'manager_must_be_active' for attribute E2.At3
2025-05-12 12:15:04,126 - implement_missing_features - INFO - Validations table fixed successfully
2025-05-12 12:15:04,126 - implement_missing_features - INFO - Warning: Attribute 'status' not found for entity E1
2025-05-12 12:15:04,126 - implement_missing_features - INFO - Inserted validation 'hireDate_be_at_least_90_days_before_Emp' for attribute E1.At9
2025-05-12 12:15:04,126 - implement_missing_features - INFO - Inserted validation 'budget_be_approved_by_Finance_before_' for attribute E2.At5
2025-05-12 12:15:04,126 - implement_missing_features - INFO - Inserted validation 'managerId_reference_an_Employee_with_sta' for attribute E2.At3
2025-05-12 12:15:04,126 - implement_missing_features - INFO - Inserted validation 'status_must_be_active_for_performance_reviews' for attribute E1.At10
2025-05-12 12:15:04,126 - implement_missing_features - INFO - Inserted validation 'hireDate_at_least_90_days_before_performance_rating' for attribute E1.At9
2025-05-12 12:15:04,126 - implement_missing_features - INFO - Inserted validation 'budget_approved_by_finance_before_changes' for attribute E2.At5
2025-05-12 12:15:04,126 - implement_missing_features - INFO - Inserted validation 'manager_must_be_active' for attribute E2.At3
2025-05-12 12:15:04,126 - implement_missing_features - INFO - Validations table fixed successfully
2025-05-12 12:15:04,126 - implement_missing_features - INFO - === Fixing Entity IDs ===
2025-05-12 12:15:04,127 - implement_missing_features - INFO - Fixing entity IDs
2025-05-12 12:15:04,130 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 12:15:04,131 - implement_missing_features - INFO - Entity IDs already use sequential IDs
2025-05-12 12:15:04,131 - implement_missing_features - INFO - Entity IDs already use sequential IDs
2025-05-12 12:15:04,131 - implement_missing_features - INFO - === Fixing Relationship References ===
2025-05-12 12:15:04,131 - implement_missing_features - INFO - Fixing relationship references
2025-05-12 12:15:04,134 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 12:15:04,138 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 12:15:04,142 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 12:15:04,146 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 12:15:04,150 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 12:15:04,155 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 12:15:04,159 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 12:15:04,163 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 12:15:04,164 - implement_missing_features - INFO - Relationship references fixed successfully
2025-05-12 12:15:04,164 - implement_missing_features - INFO - Relationship references fixed successfully
2025-05-12 12:15:04,164 - implement_missing_features - INFO - === Fixing Foreign Key Constraints ===
2025-05-12 12:15:04,164 - implement_missing_features - INFO - Fixing foreign key constraints
2025-05-12 12:15:04,168 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 12:15:04,173 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 12:15:04,185 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 12:15:04,197 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 12:15:04,209 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 12:15:04,221 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 12:15:04,233 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 12:15:04,245 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 12:15:04,254 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 12:15:04,265 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 12:15:04,277 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 12:15:04,286 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 12:15:04,298 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 12:15:04,311 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 12:15:04,317 - implement_missing_features - INFO - Foreign key constraints fixed successfully
2025-05-12 12:15:04,317 - implement_missing_features - INFO - Foreign key constraints fixed successfully
2025-05-12 12:15:04,317 - implement_missing_features - INFO - === Optimizing Database Connections ===
2025-05-12 12:15:04,317 - implement_missing_features - INFO - Optimizing database connections
2025-05-12 12:15:04,317 - implement_missing_features - INFO - component_deployer.py already has connection pooling
2025-05-12 12:15:04,317 - implement_missing_features - INFO - component_deployer.py already has connection pooling
2025-05-12 12:15:04,317 - implement_missing_features - INFO - All fixes completed successfully
2025-05-12 12:19:53,135 - implement_missing_features - INFO - Starting to implement missing features
2025-05-12 12:19:53,135 - implement_missing_features - INFO - === Fixing Enum Values ===
2025-05-12 12:19:53,135 - implement_missing_features - INFO - Fixing enum values table
2025-05-12 12:19:53,141 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 12:19:53,142 - implement_missing_features - INFO - No enum attributes found
2025-05-12 12:19:53,142 - implement_missing_features - INFO - No enum attributes found
2025-05-12 12:19:53,142 - implement_missing_features - INFO - === Fixing Validations ===
2025-05-12 12:19:53,142 - implement_missing_features - INFO - Fixing validations table
2025-05-12 12:19:53,147 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 12:19:53,151 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 12:19:53,156 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 12:19:53,161 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 12:19:53,162 - implement_missing_features - INFO - Processing entity E1 (Employee)
2025-05-12 12:19:53,166 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 12:19:53,167 - implement_missing_features - WARNING - Attribute 'status' not found for entity E1
2025-05-12 12:19:53,171 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 12:19:53,173 - implement_missing_features - INFO - Inserted validation 'hireDate_be_at_least_90_days_before_Emp' for attribute E1.At9
2025-05-12 12:19:53,173 - implement_missing_features - INFO - Processing entity E2 (Department)
2025-05-12 12:19:53,176 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 12:19:53,181 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 12:19:53,182 - implement_missing_features - INFO - Inserted validation 'budget_be_approved_by_Finance_before_' for attribute E2.At5
2025-05-12 12:19:53,186 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 12:19:53,188 - implement_missing_features - INFO - Inserted validation 'managerId_reference_an_Employee_with_sta' for attribute E2.At3
2025-05-12 12:19:53,191 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 12:19:53,193 - implement_missing_features - INFO - Inserted validation 'status_must_be_active_for_performance_reviews' for attribute E1.At10
2025-05-12 12:19:53,196 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 12:19:53,197 - implement_missing_features - INFO - Inserted validation 'hireDate_at_least_90_days_before_performance_rating' for attribute E1.At9
2025-05-12 12:19:53,201 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 12:19:53,202 - implement_missing_features - INFO - Inserted validation 'budget_approved_by_finance_before_changes' for attribute E2.At5
2025-05-12 12:19:53,205 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 12:19:53,207 - implement_missing_features - INFO - Inserted validation 'manager_must_be_active' for attribute E2.At3
2025-05-12 12:19:53,207 - implement_missing_features - INFO - Validations table fixed successfully
2025-05-12 12:19:53,207 - implement_missing_features - INFO - Warning: Attribute 'status' not found for entity E1
2025-05-12 12:19:53,207 - implement_missing_features - INFO - Inserted validation 'hireDate_be_at_least_90_days_before_Emp' for attribute E1.At9
2025-05-12 12:19:53,207 - implement_missing_features - INFO - Inserted validation 'budget_be_approved_by_Finance_before_' for attribute E2.At5
2025-05-12 12:19:53,207 - implement_missing_features - INFO - Inserted validation 'managerId_reference_an_Employee_with_sta' for attribute E2.At3
2025-05-12 12:19:53,207 - implement_missing_features - INFO - Inserted validation 'status_must_be_active_for_performance_reviews' for attribute E1.At10
2025-05-12 12:19:53,207 - implement_missing_features - INFO - Inserted validation 'hireDate_at_least_90_days_before_performance_rating' for attribute E1.At9
2025-05-12 12:19:53,207 - implement_missing_features - INFO - Inserted validation 'budget_approved_by_finance_before_changes' for attribute E2.At5
2025-05-12 12:19:53,207 - implement_missing_features - INFO - Inserted validation 'manager_must_be_active' for attribute E2.At3
2025-05-12 12:19:53,207 - implement_missing_features - INFO - Validations table fixed successfully
2025-05-12 12:19:53,207 - implement_missing_features - INFO - === Fixing Entity IDs ===
2025-05-12 12:19:53,207 - implement_missing_features - INFO - Fixing entity IDs
2025-05-12 12:19:53,210 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 12:19:53,211 - implement_missing_features - INFO - Entity IDs already use sequential IDs
2025-05-12 12:19:53,211 - implement_missing_features - INFO - Entity IDs already use sequential IDs
2025-05-12 12:19:53,211 - implement_missing_features - INFO - === Fixing Relationship References ===
2025-05-12 12:19:53,211 - implement_missing_features - INFO - Fixing relationship references
2025-05-12 12:19:53,215 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 12:19:53,219 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 12:19:53,223 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 12:19:53,227 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 12:19:53,231 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 12:19:53,235 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 12:19:53,239 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 12:19:53,244 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 12:19:53,245 - implement_missing_features - INFO - Relationship references fixed successfully
2025-05-12 12:19:53,245 - implement_missing_features - INFO - Relationship references fixed successfully
2025-05-12 12:19:53,245 - implement_missing_features - INFO - === Fixing Foreign Key Constraints ===
2025-05-12 12:19:53,245 - implement_missing_features - INFO - Fixing foreign key constraints
2025-05-12 12:19:53,248 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 12:19:53,253 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 12:19:53,266 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 12:19:53,277 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 12:19:53,290 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 12:19:53,302 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 12:19:53,313 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 12:19:53,324 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 12:19:53,333 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 12:19:53,344 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 12:19:53,356 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 12:19:53,365 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 12:19:53,377 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 12:19:53,391 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 12:19:53,397 - implement_missing_features - INFO - Foreign key constraints fixed successfully
2025-05-12 12:19:53,397 - implement_missing_features - INFO - Foreign key constraints fixed successfully
2025-05-12 12:19:53,397 - implement_missing_features - INFO - === Optimizing Database Connections ===
2025-05-12 12:19:53,397 - implement_missing_features - INFO - Optimizing database connections
2025-05-12 12:19:53,397 - implement_missing_features - INFO - component_deployer.py already has connection pooling
2025-05-12 12:19:53,397 - implement_missing_features - INFO - component_deployer.py already has connection pooling
2025-05-12 12:19:53,397 - implement_missing_features - INFO - All fixes completed successfully
2025-05-12 14:21:55,001 - implement_missing_features - INFO - Starting to implement missing features
2025-05-12 14:21:55,001 - implement_missing_features - INFO - === Fixing Enum Values ===
2025-05-12 14:21:55,001 - implement_missing_features - INFO - Fixing enum values table
2025-05-12 14:21:55,007 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 14:21:55,009 - implement_missing_features - INFO - No enum attributes found
2025-05-12 14:21:55,009 - implement_missing_features - INFO - No enum attributes found
2025-05-12 14:21:55,009 - implement_missing_features - INFO - === Fixing Validations ===
2025-05-12 14:21:55,009 - implement_missing_features - INFO - Fixing validations table
2025-05-12 14:21:55,014 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 14:21:55,020 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 14:21:55,025 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 14:21:55,029 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 14:21:55,030 - implement_missing_features - INFO - Processing entity E1 (Employee)
2025-05-12 14:21:55,034 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 14:21:55,035 - implement_missing_features - WARNING - Attribute 'status' not found for entity E1
2025-05-12 14:21:55,038 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 14:21:55,040 - implement_missing_features - INFO - Inserted validation 'hireDate_be_at_least_90_days_before_Emp' for attribute E1.At9
2025-05-12 14:21:55,040 - implement_missing_features - INFO - Processing entity E2 (Department)
2025-05-12 14:21:55,044 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 14:21:55,048 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 14:21:55,050 - implement_missing_features - INFO - Inserted validation 'budget_be_approved_by_Finance_before_' for attribute E2.At5
2025-05-12 14:21:55,053 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 14:21:55,054 - implement_missing_features - INFO - Inserted validation 'managerId_reference_an_Employee_with_sta' for attribute E2.At3
2025-05-12 14:21:55,058 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 14:21:55,060 - implement_missing_features - INFO - Inserted validation 'status_must_be_active_for_performance_reviews' for attribute E1.At10
2025-05-12 14:21:55,063 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 14:21:55,065 - implement_missing_features - INFO - Inserted validation 'hireDate_at_least_90_days_before_performance_rating' for attribute E1.At9
2025-05-12 14:21:55,069 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 14:21:55,070 - implement_missing_features - INFO - Inserted validation 'budget_approved_by_finance_before_changes' for attribute E2.At5
2025-05-12 14:21:55,074 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 14:21:55,076 - implement_missing_features - INFO - Inserted validation 'manager_must_be_active' for attribute E2.At3
2025-05-12 14:21:55,076 - implement_missing_features - INFO - Validations table fixed successfully
2025-05-12 14:21:55,076 - implement_missing_features - INFO - Warning: Attribute 'status' not found for entity E1
2025-05-12 14:21:55,076 - implement_missing_features - INFO - Inserted validation 'hireDate_be_at_least_90_days_before_Emp' for attribute E1.At9
2025-05-12 14:21:55,076 - implement_missing_features - INFO - Inserted validation 'budget_be_approved_by_Finance_before_' for attribute E2.At5
2025-05-12 14:21:55,076 - implement_missing_features - INFO - Inserted validation 'managerId_reference_an_Employee_with_sta' for attribute E2.At3
2025-05-12 14:21:55,076 - implement_missing_features - INFO - Inserted validation 'status_must_be_active_for_performance_reviews' for attribute E1.At10
2025-05-12 14:21:55,076 - implement_missing_features - INFO - Inserted validation 'hireDate_at_least_90_days_before_performance_rating' for attribute E1.At9
2025-05-12 14:21:55,076 - implement_missing_features - INFO - Inserted validation 'budget_approved_by_finance_before_changes' for attribute E2.At5
2025-05-12 14:21:55,076 - implement_missing_features - INFO - Inserted validation 'manager_must_be_active' for attribute E2.At3
2025-05-12 14:21:55,076 - implement_missing_features - INFO - Validations table fixed successfully
2025-05-12 14:21:55,076 - implement_missing_features - INFO - === Fixing Entity IDs ===
2025-05-12 14:21:55,076 - implement_missing_features - INFO - Fixing entity IDs
2025-05-12 14:21:55,080 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 14:21:55,081 - implement_missing_features - INFO - Entity IDs already use sequential IDs
2025-05-12 14:21:55,081 - implement_missing_features - INFO - Entity IDs already use sequential IDs
2025-05-12 14:21:55,081 - implement_missing_features - INFO - === Fixing Relationship References ===
2025-05-12 14:21:55,081 - implement_missing_features - INFO - Fixing relationship references
2025-05-12 14:21:55,084 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 14:21:55,088 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 14:21:55,092 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 14:21:55,097 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 14:21:55,102 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 14:21:55,106 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 14:21:55,111 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 14:21:55,116 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 14:21:55,118 - implement_missing_features - INFO - Relationship references fixed successfully
2025-05-12 14:21:55,118 - implement_missing_features - INFO - Relationship references fixed successfully
2025-05-12 14:21:55,118 - implement_missing_features - INFO - === Fixing Foreign Key Constraints ===
2025-05-12 14:21:55,118 - implement_missing_features - INFO - Fixing foreign key constraints
2025-05-12 14:21:55,121 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 14:21:55,128 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 14:21:55,142 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 14:21:55,154 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 14:21:55,167 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 14:21:55,179 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 14:21:55,190 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 14:21:55,203 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 14:21:55,212 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 14:21:55,224 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 14:21:55,236 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 14:21:55,245 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 14:21:55,259 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 14:21:55,273 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 14:21:55,279 - implement_missing_features - INFO - Foreign key constraints fixed successfully
2025-05-12 14:21:55,279 - implement_missing_features - INFO - Foreign key constraints fixed successfully
2025-05-12 14:21:55,279 - implement_missing_features - INFO - === Optimizing Database Connections ===
2025-05-12 14:21:55,279 - implement_missing_features - INFO - Optimizing database connections
2025-05-12 14:21:55,279 - implement_missing_features - INFO - component_deployer.py already has connection pooling
2025-05-12 14:21:55,279 - implement_missing_features - INFO - component_deployer.py already has connection pooling
2025-05-12 14:21:55,279 - implement_missing_features - INFO - All fixes completed successfully
2025-05-12 15:06:20,912 - implement_missing_features - INFO - Starting to implement missing features
2025-05-12 15:06:20,912 - implement_missing_features - INFO - === Fixing Enum Values ===
2025-05-12 15:06:20,912 - implement_missing_features - INFO - Fixing enum values table
2025-05-12 15:06:20,917 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:06:20,919 - implement_missing_features - INFO - No enum attributes found
2025-05-12 15:06:20,919 - implement_missing_features - INFO - No enum attributes found
2025-05-12 15:06:20,919 - implement_missing_features - INFO - === Fixing Validations ===
2025-05-12 15:06:20,919 - implement_missing_features - INFO - Fixing validations table
2025-05-12 15:06:20,924 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:06:20,929 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:06:20,934 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:06:20,939 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:06:20,940 - implement_missing_features - INFO - Processing entity E1 (Employee)
2025-05-12 15:06:20,943 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:06:20,944 - implement_missing_features - WARNING - Attribute 'status' not found for entity E1
2025-05-12 15:06:20,948 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:06:20,950 - implement_missing_features - INFO - Inserted validation 'hireDate_be_at_least_90_days_before_Emp' for attribute E1.At9
2025-05-12 15:06:20,950 - implement_missing_features - INFO - Processing entity E2 (Department)
2025-05-12 15:06:20,953 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:06:20,957 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:06:20,959 - implement_missing_features - INFO - Inserted validation 'budget_be_approved_by_Finance_before_' for attribute E2.At5
2025-05-12 15:06:20,962 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:06:20,964 - implement_missing_features - INFO - Inserted validation 'managerId_reference_an_Employee_with_sta' for attribute E2.At3
2025-05-12 15:06:20,967 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:06:20,969 - implement_missing_features - INFO - Inserted validation 'status_must_be_active_for_performance_reviews' for attribute E1.At10
2025-05-12 15:06:20,972 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:06:20,974 - implement_missing_features - INFO - Inserted validation 'hireDate_at_least_90_days_before_performance_rating' for attribute E1.At9
2025-05-12 15:06:20,978 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:06:20,979 - implement_missing_features - INFO - Inserted validation 'budget_approved_by_finance_before_changes' for attribute E2.At5
2025-05-12 15:06:20,983 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:06:20,984 - implement_missing_features - INFO - Inserted validation 'manager_must_be_active' for attribute E2.At3
2025-05-12 15:06:20,984 - implement_missing_features - INFO - Validations table fixed successfully
2025-05-12 15:06:20,984 - implement_missing_features - INFO - Warning: Attribute 'status' not found for entity E1
2025-05-12 15:06:20,985 - implement_missing_features - INFO - Inserted validation 'hireDate_be_at_least_90_days_before_Emp' for attribute E1.At9
2025-05-12 15:06:20,985 - implement_missing_features - INFO - Inserted validation 'budget_be_approved_by_Finance_before_' for attribute E2.At5
2025-05-12 15:06:20,985 - implement_missing_features - INFO - Inserted validation 'managerId_reference_an_Employee_with_sta' for attribute E2.At3
2025-05-12 15:06:20,985 - implement_missing_features - INFO - Inserted validation 'status_must_be_active_for_performance_reviews' for attribute E1.At10
2025-05-12 15:06:20,985 - implement_missing_features - INFO - Inserted validation 'hireDate_at_least_90_days_before_performance_rating' for attribute E1.At9
2025-05-12 15:06:20,985 - implement_missing_features - INFO - Inserted validation 'budget_approved_by_finance_before_changes' for attribute E2.At5
2025-05-12 15:06:20,985 - implement_missing_features - INFO - Inserted validation 'manager_must_be_active' for attribute E2.At3
2025-05-12 15:06:20,985 - implement_missing_features - INFO - Validations table fixed successfully
2025-05-12 15:06:20,985 - implement_missing_features - INFO - === Fixing Entity IDs ===
2025-05-12 15:06:20,985 - implement_missing_features - INFO - Fixing entity IDs
2025-05-12 15:06:20,988 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:06:20,989 - implement_missing_features - INFO - Entity IDs already use sequential IDs
2025-05-12 15:06:20,989 - implement_missing_features - INFO - Entity IDs already use sequential IDs
2025-05-12 15:06:20,989 - implement_missing_features - INFO - === Fixing Relationship References ===
2025-05-12 15:06:20,989 - implement_missing_features - INFO - Fixing relationship references
2025-05-12 15:06:20,993 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:06:20,997 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:06:21,002 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:06:21,006 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:06:21,012 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:06:21,017 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:06:21,023 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:06:21,030 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:06:21,031 - implement_missing_features - INFO - Relationship references fixed successfully
2025-05-12 15:06:21,031 - implement_missing_features - INFO - Relationship references fixed successfully
2025-05-12 15:06:21,031 - implement_missing_features - INFO - === Fixing Foreign Key Constraints ===
2025-05-12 15:06:21,031 - implement_missing_features - INFO - Fixing foreign key constraints
2025-05-12 15:06:21,035 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:06:21,042 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:06:21,054 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:06:21,066 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:06:21,079 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:06:21,092 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:06:21,105 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:06:21,118 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:06:21,129 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:06:21,142 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:06:21,155 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:06:21,166 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:06:21,179 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:06:21,193 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 15:06:21,199 - implement_missing_features - INFO - Foreign key constraints fixed successfully
2025-05-12 15:06:21,199 - implement_missing_features - INFO - Foreign key constraints fixed successfully
2025-05-12 15:06:21,199 - implement_missing_features - INFO - === Optimizing Database Connections ===
2025-05-12 15:06:21,199 - implement_missing_features - INFO - Optimizing database connections
2025-05-12 15:06:21,199 - implement_missing_features - INFO - component_deployer.py already has connection pooling
2025-05-12 15:06:21,199 - implement_missing_features - INFO - component_deployer.py already has connection pooling
2025-05-12 15:06:21,199 - implement_missing_features - INFO - All fixes completed successfully
