{"timestamp": "2025-06-23T07:28:00.966674", "endpoint": "fetch/attribute-validations", "input": {}, "output": {"success": true, "postgres_validations": [{"attribute_id": "E7.At1", "created_at": "2025-06-14T07:31:21.846597", "updated_at": "2025-06-14T07:31:21.846597", "error_message": "Valida<PERSON> failed for leaveId", "validation_id": 29, "id": 1, "created_by": null, "updated_by": null, "left_operand": null, "operator": null, "right_operand": null, "success_value": null, "warning_value": null, "failure_value": null, "multi_condition_operator": null, "warning_message": null, "success_message": null, "natural_language": null, "version": 1, "status": null}, {"attribute_id": "E7.At2", "created_at": "2025-06-14T07:31:21.846597", "updated_at": "2025-06-14T07:31:21.846597", "error_message": "Validation failed for employeeId", "validation_id": 30, "id": 2, "created_by": null, "updated_by": null, "left_operand": null, "operator": null, "right_operand": null, "success_value": null, "warning_value": null, "failure_value": null, "multi_condition_operator": null, "warning_message": null, "success_message": null, "natural_language": null, "version": 1, "status": null}, {"attribute_id": "E7.At3", "created_at": "2025-06-14T07:31:21.846597", "updated_at": "2025-06-14T07:31:21.846597", "error_message": "Validation failed for startDate", "validation_id": 31, "id": 3, "created_by": null, "updated_by": null, "left_operand": null, "operator": null, "right_operand": null, "success_value": null, "warning_value": null, "failure_value": null, "multi_condition_operator": null, "warning_message": null, "success_message": null, "natural_language": null, "version": 1, "status": null}, {"attribute_id": "E7.At4", "created_at": "2025-06-14T07:31:21.846597", "updated_at": "2025-06-14T07:31:21.846597", "error_message": "Validation failed for endDate", "validation_id": 32, "id": 4, "created_by": null, "updated_by": null, "left_operand": null, "operator": null, "right_operand": null, "success_value": null, "warning_value": null, "failure_value": null, "multi_condition_operator": null, "warning_message": null, "success_message": null, "natural_language": null, "version": 1, "status": null}, {"attribute_id": "E7.At5", "created_at": "2025-06-14T07:31:21.846597", "updated_at": "2025-06-14T07:31:21.846597", "error_message": "Validation failed for numDays", "validation_id": 33, "id": 5, "created_by": null, "updated_by": null, "left_operand": null, "operator": null, "right_operand": null, "success_value": null, "warning_value": null, "failure_value": null, "multi_condition_operator": null, "warning_message": null, "success_message": null, "natural_language": null, "version": 1, "status": null}, {"attribute_id": "E7.At6", "created_at": "2025-06-14T07:31:21.846597", "updated_at": "2025-06-14T07:31:21.846597", "error_message": "Validation failed for reason", "validation_id": 34, "id": 6, "created_by": null, "updated_by": null, "left_operand": null, "operator": null, "right_operand": null, "success_value": null, "warning_value": null, "failure_value": null, "multi_condition_operator": null, "warning_message": null, "success_message": null, "natural_language": null, "version": 1, "status": null}, {"attribute_id": "E7.At15", "created_at": "2025-06-14T07:31:21.846597", "updated_at": "2025-06-14T07:31:21.846597", "error_message": "Validation failed for comments", "validation_id": 35, "id": 7, "created_by": null, "updated_by": null, "left_operand": null, "operator": null, "right_operand": null, "success_value": null, "warning_value": null, "failure_value": null, "multi_condition_operator": null, "warning_message": null, "success_message": null, "natural_language": null, "version": 1, "status": null}, {"attribute_id": "E8.At1", "created_at": "2025-06-14T07:31:21.846597", "updated_at": "2025-06-14T07:31:21.846597", "error_message": "Validation failed for employeeId", "validation_id": 36, "id": 8, "created_by": null, "updated_by": null, "left_operand": null, "operator": null, "right_operand": null, "success_value": null, "warning_value": null, "failure_value": null, "multi_condition_operator": null, "warning_message": null, "success_message": null, "natural_language": null, "version": 1, "status": null}, {"attribute_id": "E8.At4", "created_at": "2025-06-14T07:31:21.846597", "updated_at": "2025-06-14T07:31:21.846597", "error_message": "Validation failed for email", "validation_id": 37, "id": 9, "created_by": null, "updated_by": null, "left_operand": null, "operator": null, "right_operand": null, "success_value": null, "warning_value": null, "failure_value": null, "multi_condition_operator": null, "warning_message": null, "success_message": null, "natural_language": null, "version": 1, "status": null}, {"attribute_id": "E8.At6", "created_at": "2025-06-14T07:31:21.846597", "updated_at": "2025-06-14T07:31:21.846597", "error_message": "Validation failed for departmentId", "validation_id": 38, "id": 10, "created_by": null, "updated_by": null, "left_operand": null, "operator": null, "right_operand": null, "success_value": null, "warning_value": null, "failure_value": null, "multi_condition_operator": null, "warning_message": null, "success_message": null, "natural_language": null, "version": 1, "status": null}, {"attribute_id": "E8.At9", "created_at": "2025-06-14T07:31:21.846597", "updated_at": "2025-06-14T07:31:21.846597", "error_message": "Validation failed for hireDate", "validation_id": 39, "id": 11, "created_by": null, "updated_by": null, "left_operand": null, "operator": null, "right_operand": null, "success_value": null, "warning_value": null, "failure_value": null, "multi_condition_operator": null, "warning_message": null, "success_message": null, "natural_language": null, "version": 1, "status": null}, {"attribute_id": "E8.At11", "created_at": "2025-06-14T07:31:21.846597", "updated_at": "2025-06-14T07:31:21.846597", "error_message": "Validation failed for annualLeaveBalance", "validation_id": 40, "id": 12, "created_by": null, "updated_by": null, "left_operand": null, "operator": null, "right_operand": null, "success_value": null, "warning_value": null, "failure_value": null, "multi_condition_operator": null, "warning_message": null, "success_message": null, "natural_language": null, "version": 1, "status": null}, {"attribute_id": "E8.At12", "created_at": "2025-06-14T07:31:21.846597", "updated_at": "2025-06-14T07:31:21.846597", "error_message": "Validation failed for sickLeaveBalance", "validation_id": 41, "id": 13, "created_by": null, "updated_by": null, "left_operand": null, "operator": null, "right_operand": null, "success_value": null, "warning_value": null, "failure_value": null, "multi_condition_operator": null, "warning_message": null, "success_message": null, "natural_language": null, "version": 1, "status": null}, {"attribute_id": "E8.At13", "created_at": "2025-06-14T07:31:21.846597", "updated_at": "2025-06-14T07:31:21.846597", "error_message": "Validation failed for personalLeaveBalance", "validation_id": 42, "id": 14, "created_by": null, "updated_by": null, "left_operand": null, "operator": null, "right_operand": null, "success_value": null, "warning_value": null, "failure_value": null, "multi_condition_operator": null, "warning_message": null, "success_message": null, "natural_language": null, "version": 1, "status": null}, {"attribute_id": "E9.At1", "created_at": "2025-06-14T07:31:21.846597", "updated_at": "2025-06-14T07:31:21.846597", "error_message": "Validation failed for leaveTypeId", "validation_id": 43, "id": 15, "created_by": null, "updated_by": null, "left_operand": null, "operator": null, "right_operand": null, "success_value": null, "warning_value": null, "failure_value": null, "multi_condition_operator": null, "warning_message": null, "success_message": null, "natural_language": null, "version": 1, "status": null}, {"attribute_id": "E9.At2", "created_at": "2025-06-14T07:31:21.846597", "updated_at": "2025-06-14T07:31:21.846597", "error_message": "Validation failed for leaveTypeName", "validation_id": 44, "id": 16, "created_by": null, "updated_by": null, "left_operand": null, "operator": null, "right_operand": null, "success_value": null, "warning_value": null, "failure_value": null, "multi_condition_operator": null, "warning_message": null, "success_message": null, "natural_language": null, "version": 1, "status": null}, {"attribute_id": "E9.At4", "created_at": "2025-06-14T07:31:21.846597", "updated_at": "2025-06-14T07:31:21.846597", "error_message": "Validation failed for maxDaysPerYear", "validation_id": 45, "id": 17, "created_by": null, "updated_by": null, "left_operand": null, "operator": null, "right_operand": null, "success_value": null, "warning_value": null, "failure_value": null, "multi_condition_operator": null, "warning_message": null, "success_message": null, "natural_language": null, "version": 1, "status": null}, {"attribute_id": "E9.At6", "created_at": "2025-06-14T07:31:21.846597", "updated_at": "2025-06-14T07:31:21.846597", "error_message": "Validation failed for advanceNoticeDays", "validation_id": 46, "id": 18, "created_by": null, "updated_by": null, "left_operand": null, "operator": null, "right_operand": null, "success_value": null, "warning_value": null, "failure_value": null, "multi_condition_operator": null, "warning_message": null, "success_message": null, "natural_language": null, "version": 1, "status": null}, {"attribute_id": "E10.At1", "created_at": "2025-06-14T07:31:21.846597", "updated_at": "2025-06-14T07:31:21.846597", "error_message": "Validation failed for leaveSubTypeId", "validation_id": 47, "id": 19, "created_by": null, "updated_by": null, "left_operand": null, "operator": null, "right_operand": null, "success_value": null, "warning_value": null, "failure_value": null, "multi_condition_operator": null, "warning_message": null, "success_message": null, "natural_language": null, "version": 1, "status": null}, {"attribute_id": "E10.At3", "created_at": "2025-06-14T07:31:21.846597", "updated_at": "2025-06-14T07:31:21.846597", "error_message": "Validation failed for leaveSubTypeName", "validation_id": 48, "id": 20, "created_by": null, "updated_by": null, "left_operand": null, "operator": null, "right_operand": null, "success_value": null, "warning_value": null, "failure_value": null, "multi_condition_operator": null, "warning_message": null, "success_message": null, "natural_language": null, "version": 1, "status": null}, {"attribute_id": "E10.At5", "created_at": "2025-06-14T07:31:21.846597", "updated_at": "2025-06-14T07:31:21.846597", "error_message": "Validation failed for maxDaysPerRequest", "validation_id": 49, "id": 21, "created_by": null, "updated_by": null, "left_operand": null, "operator": null, "right_operand": null, "success_value": null, "warning_value": null, "failure_value": null, "multi_condition_operator": null, "warning_message": null, "success_message": null, "natural_language": null, "version": 1, "status": null}, {"attribute_id": "E11.At1", "created_at": "2025-06-14T07:31:21.846597", "updated_at": "2025-06-14T07:31:21.846597", "error_message": "Validation failed for departmentId", "validation_id": 50, "id": 22, "created_by": null, "updated_by": null, "left_operand": null, "operator": null, "right_operand": null, "success_value": null, "warning_value": null, "failure_value": null, "multi_condition_operator": null, "warning_message": null, "success_message": null, "natural_language": null, "version": 1, "status": null}, {"attribute_id": "E11.At2", "created_at": "2025-06-14T07:31:21.846597", "updated_at": "2025-06-14T07:31:21.846597", "error_message": "Validation failed for departmentName", "validation_id": 51, "id": 23, "created_by": null, "updated_by": null, "left_operand": null, "operator": null, "right_operand": null, "success_value": null, "warning_value": null, "failure_value": null, "multi_condition_operator": null, "warning_message": null, "success_message": null, "natural_language": null, "version": 1, "status": null}, {"attribute_id": "E11.At4", "created_at": "2025-06-14T07:31:21.846597", "updated_at": "2025-06-14T07:31:21.846597", "error_message": "Validation failed for managerId", "validation_id": 52, "id": 24, "created_by": null, "updated_by": null, "left_operand": null, "operator": null, "right_operand": null, "success_value": null, "warning_value": null, "failure_value": null, "multi_condition_operator": null, "warning_message": null, "success_message": null, "natural_language": null, "version": 1, "status": null}, {"attribute_id": "E12.At1", "created_at": "2025-06-14T07:31:21.846597", "updated_at": "2025-06-14T07:31:21.846597", "error_message": "Validation failed for constantId", "validation_id": 53, "id": 25, "created_by": null, "updated_by": null, "left_operand": null, "operator": null, "right_operand": null, "success_value": null, "warning_value": null, "failure_value": null, "multi_condition_operator": null, "warning_message": null, "success_message": null, "natural_language": null, "version": 1, "status": null}, {"attribute_id": "E12.At2", "created_at": "2025-06-14T07:31:21.846597", "updated_at": "2025-06-14T07:31:21.846597", "error_message": "Validation failed for attribute", "validation_id": 54, "id": 26, "created_by": null, "updated_by": null, "left_operand": null, "operator": null, "right_operand": null, "success_value": null, "warning_value": null, "failure_value": null, "multi_condition_operator": null, "warning_message": null, "success_message": null, "natural_language": null, "version": 1, "status": null}, {"attribute_id": "E12.At3", "created_at": "2025-06-14T07:31:21.846597", "updated_at": "2025-06-14T07:31:21.846597", "error_message": "Validation failed for value", "validation_id": 55, "id": 27, "created_by": null, "updated_by": null, "left_operand": null, "operator": null, "right_operand": null, "success_value": null, "warning_value": null, "failure_value": null, "multi_condition_operator": null, "warning_message": null, "success_message": null, "natural_language": null, "version": 1, "status": null}, {"attribute_id": "E12.At5", "created_at": "2025-06-14T07:31:21.846597", "updated_at": "2025-06-14T07:31:21.846597", "error_message": "Validation failed for dataType", "validation_id": 56, "id": 28, "created_by": null, "updated_by": null, "left_operand": null, "operator": null, "right_operand": null, "success_value": null, "warning_value": null, "failure_value": null, "multi_condition_operator": null, "warning_message": null, "success_message": null, "natural_language": null, "version": 1, "status": null}], "mongo_drafts": [{"_id": "6854488a754fbf4d729d3a7a", "validation_id": 2, "attribute_id": "E8.At4", "entity_name": "Employee", "attribute_name": "email", "left_operand": "email", "operator": "IS_UNIQUE", "right_operand": "", "success_value": "unique", "warning_value": "", "failure_value": "duplicate", "multi_condition_operator": "FALSE", "warning_message": "", "success_message": "Email is unique", "error_message": "Email must be unique", "natural_language": "Employee.email: email IS_UNIQUE ", "version": 1, "status": "draft", "created_at": "2025-06-19T17:27:38.404018", "updated_at": "2025-06-19T17:27:38.404025", "created_by": "system", "updated_by": "system"}, {"_id": "6854ee73fa0d6bfc16038bef", "validation_id": 6, "attribute_id": "E8.At4", "entity_name": "Employee", "attribute_name": "email", "left_operand": "email", "operator": "IS_UNIQUE", "right_operand": "", "success_value": "unique", "warning_value": "", "failure_value": "duplicate", "multi_condition_operator": "FALSE", "warning_message": "", "success_message": "Email is unique", "error_message": "Email must be unique", "natural_language": "Employee.email: email IS_UNIQUE ", "version": 1, "status": "draft", "created_at": "2025-06-20T05:15:31.135351", "updated_at": "2025-06-20T05:15:31.135357", "created_by": "system", "updated_by": "system"}, {"_id": "6854ee73fa0d6bfc16038bf0", "validation_id": 5, "attribute_id": "E8.At1", "entity_name": "Employee", "attribute_name": "employeeId", "left_operand": "employeeId", "operator": "IS_UNIQUE", "right_operand": "", "success_value": "unique", "warning_value": "", "failure_value": "duplicate", "multi_condition_operator": "FALSE", "warning_message": "", "success_message": "Employee ID is valid", "error_message": "Employee ID must be unique", "natural_language": "Employee.employeeId: employeeId IS_UNIQUE ", "version": 1, "status": "draft", "created_at": "2025-06-20T05:15:31.135903", "updated_at": "2025-06-20T05:15:31.135906", "created_by": "system", "updated_by": "system"}, {"_id": "6854f3ac9031164ff452a0f8", "validation_id": 7, "attribute_id": "E8.At4", "entity_name": "Employee", "attribute_name": "phone", "left_operand": "phone", "operator": "IS_UNIQUE", "right_operand": "", "success_value": "unique", "warning_value": "", "failure_value": "duplicate", "multi_condition_operator": "FALSE", "warning_message": "", "success_message": "Phone number is unique", "error_message": "Phone number must be unique", "natural_language": "Employee.phone: phone IS_UNIQUE ", "version": 1, "status": "draft", "created_at": "2025-06-20T05:37:48.522400", "updated_at": "2025-06-20T05:37:48.522404", "created_by": "system", "updated_by": "system"}, {"_id": "685508447147def4f91d1022", "validation_id": 1, "attribute_id": "E7.At1", "entity_name": "LeaveApplication", "attribute_name": "leaveId", "left_operand": "leaveId", "operator": "IS_UNIQUE", "right_operand": "", "success_value": "unique", "warning_value": "", "failure_value": "duplicate", "multi_condition_operator": "FALSE", "warning_message": "", "success_message": "Leave ID is valid", "error_message": "Leave ID must be unique", "natural_language": "LeaveApplication.leaveId: leaveId IS_UNIQUE ", "version": 1, "status": "draft", "created_at": "2025-06-20T07:05:40.927340", "updated_at": "2025-06-20T07:05:40.927345", "created_by": "system", "updated_by": "system"}, {"_id": "685508447147def4f91d1023", "validation_id": 2, "attribute_id": "E7.At2", "entity_name": "LeaveApplication", "attribute_name": "employeeId", "left_operand": "employeeId", "operator": "EXISTS_IN", "right_operand": "Employee.employeeId", "success_value": "exists", "warning_value": "", "failure_value": "not_exists", "multi_condition_operator": "FALSE", "warning_message": "", "success_message": "Employee ID is valid", "error_message": "Employee ID must exist in Employee table", "natural_language": "LeaveApplication.employeeId: employeeId EXISTS_IN Employee.employeeId", "version": 1, "status": "draft", "created_at": "2025-06-20T07:05:40.932582", "updated_at": "2025-06-20T07:05:40.932597", "created_by": "system", "updated_by": "system"}, {"_id": "685508447147def4f91d1024", "validation_id": 3, "attribute_id": "E7.At3", "entity_name": "LeaveApplication", "attribute_name": "startDate", "left_operand": "startDate", "operator": "IS_VALID_DATE", "right_operand": "", "success_value": "valid_date", "warning_value": "", "failure_value": "invalid_date", "multi_condition_operator": "FALSE", "warning_message": "", "success_message": "Start date is valid", "error_message": "Start date must be a valid date", "natural_language": "LeaveApplication.startDate: startDate IS_VALID_DATE ", "version": 1, "status": "draft", "created_at": "2025-06-20T07:05:40.934871", "updated_at": "2025-06-20T07:05:40.934878", "created_by": "system", "updated_by": "system"}, {"_id": "685508447147def4f91d1025", "validation_id": 4, "attribute_id": "E7.At4", "entity_name": "LeaveApplication", "attribute_name": "endDate", "left_operand": "endDate", "operator": "GREATER_THAN", "right_operand": "startDate", "success_value": "after_start", "warning_value": "", "failure_value": "before_or_equal_start", "multi_condition_operator": "FALSE", "warning_message": "", "success_message": "End date is valid", "error_message": "End date must be after start date", "natural_language": "LeaveApplication.endDate: endDate GREATER_THAN startDate", "version": 1, "status": "draft", "created_at": "2025-06-20T07:05:40.936694", "updated_at": "2025-06-20T07:05:40.936701", "created_by": "system", "updated_by": "system"}], "total_postgres": 28, "total_drafts": 8, "operation": "fetch"}, "status": "success"}
{"timestamp": "2025-06-23T14:08:01.581932", "endpoint": "fetch/attribute-validations", "input": {}, "output": {"success": true, "postgres_validations": [{"attribute_id": "E7.At1", "created_at": "2025-06-14T07:31:21.846597", "updated_at": "2025-06-14T07:31:21.846597", "error_message": "Validation failed for leaveId", "validation_id": 29, "id": 1, "created_by": null, "updated_by": null, "left_operand": null, "operator": null, "right_operand": null, "success_value": null, "warning_value": null, "failure_value": null, "multi_condition_operator": null, "warning_message": null, "success_message": null, "natural_language": null, "version": 1, "status": null}, {"attribute_id": "E7.At2", "created_at": "2025-06-14T07:31:21.846597", "updated_at": "2025-06-14T07:31:21.846597", "error_message": "Validation failed for employeeId", "validation_id": 30, "id": 2, "created_by": null, "updated_by": null, "left_operand": null, "operator": null, "right_operand": null, "success_value": null, "warning_value": null, "failure_value": null, "multi_condition_operator": null, "warning_message": null, "success_message": null, "natural_language": null, "version": 1, "status": null}, {"attribute_id": "E7.At3", "created_at": "2025-06-14T07:31:21.846597", "updated_at": "2025-06-14T07:31:21.846597", "error_message": "Validation failed for startDate", "validation_id": 31, "id": 3, "created_by": null, "updated_by": null, "left_operand": null, "operator": null, "right_operand": null, "success_value": null, "warning_value": null, "failure_value": null, "multi_condition_operator": null, "warning_message": null, "success_message": null, "natural_language": null, "version": 1, "status": null}, {"attribute_id": "E7.At4", "created_at": "2025-06-14T07:31:21.846597", "updated_at": "2025-06-14T07:31:21.846597", "error_message": "Validation failed for endDate", "validation_id": 32, "id": 4, "created_by": null, "updated_by": null, "left_operand": null, "operator": null, "right_operand": null, "success_value": null, "warning_value": null, "failure_value": null, "multi_condition_operator": null, "warning_message": null, "success_message": null, "natural_language": null, "version": 1, "status": null}, {"attribute_id": "E7.At5", "created_at": "2025-06-14T07:31:21.846597", "updated_at": "2025-06-14T07:31:21.846597", "error_message": "Validation failed for numDays", "validation_id": 33, "id": 5, "created_by": null, "updated_by": null, "left_operand": null, "operator": null, "right_operand": null, "success_value": null, "warning_value": null, "failure_value": null, "multi_condition_operator": null, "warning_message": null, "success_message": null, "natural_language": null, "version": 1, "status": null}, {"attribute_id": "E7.At6", "created_at": "2025-06-14T07:31:21.846597", "updated_at": "2025-06-14T07:31:21.846597", "error_message": "Validation failed for reason", "validation_id": 34, "id": 6, "created_by": null, "updated_by": null, "left_operand": null, "operator": null, "right_operand": null, "success_value": null, "warning_value": null, "failure_value": null, "multi_condition_operator": null, "warning_message": null, "success_message": null, "natural_language": null, "version": 1, "status": null}, {"attribute_id": "E7.At15", "created_at": "2025-06-14T07:31:21.846597", "updated_at": "2025-06-14T07:31:21.846597", "error_message": "Validation failed for comments", "validation_id": 35, "id": 7, "created_by": null, "updated_by": null, "left_operand": null, "operator": null, "right_operand": null, "success_value": null, "warning_value": null, "failure_value": null, "multi_condition_operator": null, "warning_message": null, "success_message": null, "natural_language": null, "version": 1, "status": null}, {"attribute_id": "E8.At1", "created_at": "2025-06-14T07:31:21.846597", "updated_at": "2025-06-14T07:31:21.846597", "error_message": "Validation failed for employeeId", "validation_id": 36, "id": 8, "created_by": null, "updated_by": null, "left_operand": null, "operator": null, "right_operand": null, "success_value": null, "warning_value": null, "failure_value": null, "multi_condition_operator": null, "warning_message": null, "success_message": null, "natural_language": null, "version": 1, "status": null}, {"attribute_id": "E8.At4", "created_at": "2025-06-14T07:31:21.846597", "updated_at": "2025-06-14T07:31:21.846597", "error_message": "Validation failed for email", "validation_id": 37, "id": 9, "created_by": null, "updated_by": null, "left_operand": null, "operator": null, "right_operand": null, "success_value": null, "warning_value": null, "failure_value": null, "multi_condition_operator": null, "warning_message": null, "success_message": null, "natural_language": null, "version": 1, "status": null}, {"attribute_id": "E8.At6", "created_at": "2025-06-14T07:31:21.846597", "updated_at": "2025-06-14T07:31:21.846597", "error_message": "Validation failed for departmentId", "validation_id": 38, "id": 10, "created_by": null, "updated_by": null, "left_operand": null, "operator": null, "right_operand": null, "success_value": null, "warning_value": null, "failure_value": null, "multi_condition_operator": null, "warning_message": null, "success_message": null, "natural_language": null, "version": 1, "status": null}, {"attribute_id": "E8.At9", "created_at": "2025-06-14T07:31:21.846597", "updated_at": "2025-06-14T07:31:21.846597", "error_message": "Validation failed for hireDate", "validation_id": 39, "id": 11, "created_by": null, "updated_by": null, "left_operand": null, "operator": null, "right_operand": null, "success_value": null, "warning_value": null, "failure_value": null, "multi_condition_operator": null, "warning_message": null, "success_message": null, "natural_language": null, "version": 1, "status": null}, {"attribute_id": "E8.At11", "created_at": "2025-06-14T07:31:21.846597", "updated_at": "2025-06-14T07:31:21.846597", "error_message": "Validation failed for annualLeaveBalance", "validation_id": 40, "id": 12, "created_by": null, "updated_by": null, "left_operand": null, "operator": null, "right_operand": null, "success_value": null, "warning_value": null, "failure_value": null, "multi_condition_operator": null, "warning_message": null, "success_message": null, "natural_language": null, "version": 1, "status": null}, {"attribute_id": "E8.At12", "created_at": "2025-06-14T07:31:21.846597", "updated_at": "2025-06-14T07:31:21.846597", "error_message": "Validation failed for sickLeaveBalance", "validation_id": 41, "id": 13, "created_by": null, "updated_by": null, "left_operand": null, "operator": null, "right_operand": null, "success_value": null, "warning_value": null, "failure_value": null, "multi_condition_operator": null, "warning_message": null, "success_message": null, "natural_language": null, "version": 1, "status": null}, {"attribute_id": "E8.At13", "created_at": "2025-06-14T07:31:21.846597", "updated_at": "2025-06-14T07:31:21.846597", "error_message": "Validation failed for personalLeaveBalance", "validation_id": 42, "id": 14, "created_by": null, "updated_by": null, "left_operand": null, "operator": null, "right_operand": null, "success_value": null, "warning_value": null, "failure_value": null, "multi_condition_operator": null, "warning_message": null, "success_message": null, "natural_language": null, "version": 1, "status": null}, {"attribute_id": "E9.At1", "created_at": "2025-06-14T07:31:21.846597", "updated_at": "2025-06-14T07:31:21.846597", "error_message": "Validation failed for leaveTypeId", "validation_id": 43, "id": 15, "created_by": null, "updated_by": null, "left_operand": null, "operator": null, "right_operand": null, "success_value": null, "warning_value": null, "failure_value": null, "multi_condition_operator": null, "warning_message": null, "success_message": null, "natural_language": null, "version": 1, "status": null}, {"attribute_id": "E9.At2", "created_at": "2025-06-14T07:31:21.846597", "updated_at": "2025-06-14T07:31:21.846597", "error_message": "Validation failed for leaveTypeName", "validation_id": 44, "id": 16, "created_by": null, "updated_by": null, "left_operand": null, "operator": null, "right_operand": null, "success_value": null, "warning_value": null, "failure_value": null, "multi_condition_operator": null, "warning_message": null, "success_message": null, "natural_language": null, "version": 1, "status": null}, {"attribute_id": "E9.At4", "created_at": "2025-06-14T07:31:21.846597", "updated_at": "2025-06-14T07:31:21.846597", "error_message": "Validation failed for maxDaysPerYear", "validation_id": 45, "id": 17, "created_by": null, "updated_by": null, "left_operand": null, "operator": null, "right_operand": null, "success_value": null, "warning_value": null, "failure_value": null, "multi_condition_operator": null, "warning_message": null, "success_message": null, "natural_language": null, "version": 1, "status": null}, {"attribute_id": "E9.At6", "created_at": "2025-06-14T07:31:21.846597", "updated_at": "2025-06-14T07:31:21.846597", "error_message": "Validation failed for advanceNoticeDays", "validation_id": 46, "id": 18, "created_by": null, "updated_by": null, "left_operand": null, "operator": null, "right_operand": null, "success_value": null, "warning_value": null, "failure_value": null, "multi_condition_operator": null, "warning_message": null, "success_message": null, "natural_language": null, "version": 1, "status": null}, {"attribute_id": "E10.At1", "created_at": "2025-06-14T07:31:21.846597", "updated_at": "2025-06-14T07:31:21.846597", "error_message": "Validation failed for leaveSubTypeId", "validation_id": 47, "id": 19, "created_by": null, "updated_by": null, "left_operand": null, "operator": null, "right_operand": null, "success_value": null, "warning_value": null, "failure_value": null, "multi_condition_operator": null, "warning_message": null, "success_message": null, "natural_language": null, "version": 1, "status": null}, {"attribute_id": "E10.At3", "created_at": "2025-06-14T07:31:21.846597", "updated_at": "2025-06-14T07:31:21.846597", "error_message": "Validation failed for leaveSubTypeName", "validation_id": 48, "id": 20, "created_by": null, "updated_by": null, "left_operand": null, "operator": null, "right_operand": null, "success_value": null, "warning_value": null, "failure_value": null, "multi_condition_operator": null, "warning_message": null, "success_message": null, "natural_language": null, "version": 1, "status": null}, {"attribute_id": "E10.At5", "created_at": "2025-06-14T07:31:21.846597", "updated_at": "2025-06-14T07:31:21.846597", "error_message": "Validation failed for maxDaysPerRequest", "validation_id": 49, "id": 21, "created_by": null, "updated_by": null, "left_operand": null, "operator": null, "right_operand": null, "success_value": null, "warning_value": null, "failure_value": null, "multi_condition_operator": null, "warning_message": null, "success_message": null, "natural_language": null, "version": 1, "status": null}, {"attribute_id": "E11.At1", "created_at": "2025-06-14T07:31:21.846597", "updated_at": "2025-06-14T07:31:21.846597", "error_message": "Validation failed for departmentId", "validation_id": 50, "id": 22, "created_by": null, "updated_by": null, "left_operand": null, "operator": null, "right_operand": null, "success_value": null, "warning_value": null, "failure_value": null, "multi_condition_operator": null, "warning_message": null, "success_message": null, "natural_language": null, "version": 1, "status": null}, {"attribute_id": "E11.At2", "created_at": "2025-06-14T07:31:21.846597", "updated_at": "2025-06-14T07:31:21.846597", "error_message": "Validation failed for departmentName", "validation_id": 51, "id": 23, "created_by": null, "updated_by": null, "left_operand": null, "operator": null, "right_operand": null, "success_value": null, "warning_value": null, "failure_value": null, "multi_condition_operator": null, "warning_message": null, "success_message": null, "natural_language": null, "version": 1, "status": null}, {"attribute_id": "E11.At4", "created_at": "2025-06-14T07:31:21.846597", "updated_at": "2025-06-14T07:31:21.846597", "error_message": "Validation failed for managerId", "validation_id": 52, "id": 24, "created_by": null, "updated_by": null, "left_operand": null, "operator": null, "right_operand": null, "success_value": null, "warning_value": null, "failure_value": null, "multi_condition_operator": null, "warning_message": null, "success_message": null, "natural_language": null, "version": 1, "status": null}, {"attribute_id": "E12.At1", "created_at": "2025-06-14T07:31:21.846597", "updated_at": "2025-06-14T07:31:21.846597", "error_message": "Validation failed for constantId", "validation_id": 53, "id": 25, "created_by": null, "updated_by": null, "left_operand": null, "operator": null, "right_operand": null, "success_value": null, "warning_value": null, "failure_value": null, "multi_condition_operator": null, "warning_message": null, "success_message": null, "natural_language": null, "version": 1, "status": null}, {"attribute_id": "E12.At2", "created_at": "2025-06-14T07:31:21.846597", "updated_at": "2025-06-14T07:31:21.846597", "error_message": "Validation failed for attribute", "validation_id": 54, "id": 26, "created_by": null, "updated_by": null, "left_operand": null, "operator": null, "right_operand": null, "success_value": null, "warning_value": null, "failure_value": null, "multi_condition_operator": null, "warning_message": null, "success_message": null, "natural_language": null, "version": 1, "status": null}, {"attribute_id": "E12.At3", "created_at": "2025-06-14T07:31:21.846597", "updated_at": "2025-06-14T07:31:21.846597", "error_message": "Validation failed for value", "validation_id": 55, "id": 27, "created_by": null, "updated_by": null, "left_operand": null, "operator": null, "right_operand": null, "success_value": null, "warning_value": null, "failure_value": null, "multi_condition_operator": null, "warning_message": null, "success_message": null, "natural_language": null, "version": 1, "status": null}, {"attribute_id": "E12.At5", "created_at": "2025-06-14T07:31:21.846597", "updated_at": "2025-06-14T07:31:21.846597", "error_message": "Validation failed for dataType", "validation_id": 56, "id": 28, "created_by": null, "updated_by": null, "left_operand": null, "operator": null, "right_operand": null, "success_value": null, "warning_value": null, "failure_value": null, "multi_condition_operator": null, "warning_message": null, "success_message": null, "natural_language": null, "version": 1, "status": null}], "mongo_drafts": [{"_id": "6854488a754fbf4d729d3a7a", "validation_id": 2, "attribute_id": "E8.At4", "entity_name": "Employee", "attribute_name": "email", "left_operand": "email", "operator": "IS_UNIQUE", "right_operand": "", "success_value": "unique", "warning_value": "", "failure_value": "duplicate", "multi_condition_operator": "FALSE", "warning_message": "", "success_message": "Email is unique", "error_message": "Email must be unique", "natural_language": "Employee.email: email IS_UNIQUE ", "version": 1, "status": "draft", "created_at": "2025-06-19T17:27:38.404018", "updated_at": "2025-06-19T17:27:38.404025", "created_by": "system", "updated_by": "system"}, {"_id": "6854ee73fa0d6bfc16038bef", "validation_id": 6, "attribute_id": "E8.At4", "entity_name": "Employee", "attribute_name": "email", "left_operand": "email", "operator": "IS_UNIQUE", "right_operand": "", "success_value": "unique", "warning_value": "", "failure_value": "duplicate", "multi_condition_operator": "FALSE", "warning_message": "", "success_message": "Email is unique", "error_message": "Email must be unique", "natural_language": "Employee.email: email IS_UNIQUE ", "version": 1, "status": "draft", "created_at": "2025-06-20T05:15:31.135351", "updated_at": "2025-06-20T05:15:31.135357", "created_by": "system", "updated_by": "system"}, {"_id": "6854ee73fa0d6bfc16038bf0", "validation_id": 5, "attribute_id": "E8.At1", "entity_name": "Employee", "attribute_name": "employeeId", "left_operand": "employeeId", "operator": "IS_UNIQUE", "right_operand": "", "success_value": "unique", "warning_value": "", "failure_value": "duplicate", "multi_condition_operator": "FALSE", "warning_message": "", "success_message": "Employee ID is valid", "error_message": "Employee ID must be unique", "natural_language": "Employee.employeeId: employeeId IS_UNIQUE ", "version": 1, "status": "draft", "created_at": "2025-06-20T05:15:31.135903", "updated_at": "2025-06-20T05:15:31.135906", "created_by": "system", "updated_by": "system"}, {"_id": "6854f3ac9031164ff452a0f8", "validation_id": 7, "attribute_id": "E8.At4", "entity_name": "Employee", "attribute_name": "phone", "left_operand": "phone", "operator": "IS_UNIQUE", "right_operand": "", "success_value": "unique", "warning_value": "", "failure_value": "duplicate", "multi_condition_operator": "FALSE", "warning_message": "", "success_message": "Phone number is unique", "error_message": "Phone number must be unique", "natural_language": "Employee.phone: phone IS_UNIQUE ", "version": 1, "status": "draft", "created_at": "2025-06-20T05:37:48.522400", "updated_at": "2025-06-20T05:37:48.522404", "created_by": "system", "updated_by": "system"}, {"_id": "685508447147def4f91d1022", "validation_id": 1, "attribute_id": "E7.At1", "entity_name": "LeaveApplication", "attribute_name": "leaveId", "left_operand": "leaveId", "operator": "IS_UNIQUE", "right_operand": "", "success_value": "unique", "warning_value": "", "failure_value": "duplicate", "multi_condition_operator": "FALSE", "warning_message": "", "success_message": "Leave ID is valid", "error_message": "Leave ID must be unique", "natural_language": "LeaveApplication.leaveId: leaveId IS_UNIQUE ", "version": 1, "status": "draft", "created_at": "2025-06-20T07:05:40.927340", "updated_at": "2025-06-20T07:05:40.927345", "created_by": "system", "updated_by": "system"}, {"_id": "685508447147def4f91d1023", "validation_id": 2, "attribute_id": "E7.At2", "entity_name": "LeaveApplication", "attribute_name": "employeeId", "left_operand": "employeeId", "operator": "EXISTS_IN", "right_operand": "Employee.employeeId", "success_value": "exists", "warning_value": "", "failure_value": "not_exists", "multi_condition_operator": "FALSE", "warning_message": "", "success_message": "Employee ID is valid", "error_message": "Employee ID must exist in Employee table", "natural_language": "LeaveApplication.employeeId: employeeId EXISTS_IN Employee.employeeId", "version": 1, "status": "draft", "created_at": "2025-06-20T07:05:40.932582", "updated_at": "2025-06-20T07:05:40.932597", "created_by": "system", "updated_by": "system"}, {"_id": "685508447147def4f91d1024", "validation_id": 3, "attribute_id": "E7.At3", "entity_name": "LeaveApplication", "attribute_name": "startDate", "left_operand": "startDate", "operator": "IS_VALID_DATE", "right_operand": "", "success_value": "valid_date", "warning_value": "", "failure_value": "invalid_date", "multi_condition_operator": "FALSE", "warning_message": "", "success_message": "Start date is valid", "error_message": "Start date must be a valid date", "natural_language": "LeaveApplication.startDate: startDate IS_VALID_DATE ", "version": 1, "status": "draft", "created_at": "2025-06-20T07:05:40.934871", "updated_at": "2025-06-20T07:05:40.934878", "created_by": "system", "updated_by": "system"}, {"_id": "685508447147def4f91d1025", "validation_id": 4, "attribute_id": "E7.At4", "entity_name": "LeaveApplication", "attribute_name": "endDate", "left_operand": "endDate", "operator": "GREATER_THAN", "right_operand": "startDate", "success_value": "after_start", "warning_value": "", "failure_value": "before_or_equal_start", "multi_condition_operator": "FALSE", "warning_message": "", "success_message": "End date is valid", "error_message": "End date must be after start date", "natural_language": "LeaveApplication.endDate: endDate GREATER_THAN startDate", "version": 1, "status": "draft", "created_at": "2025-06-20T07:05:40.936694", "updated_at": "2025-06-20T07:05:40.936701", "created_by": "system", "updated_by": "system"}], "total_postgres": 28, "total_drafts": 8, "operation": "fetch"}, "status": "success"}
