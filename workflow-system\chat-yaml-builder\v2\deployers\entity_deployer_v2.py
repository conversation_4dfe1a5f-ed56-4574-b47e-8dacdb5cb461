import os
import sys
import json
import logging
import re
from typing import Dict, List, Tuple, Any, Optional

# Set up logging
logger = logging.getLogger('entity_deployer')

def execute_query(query: str, params: Tuple = None, schema_name: str = None) -> Tuple[bool, List[str], List[Tuple]]:
    """
    Execute a database query.
    
    Args:
        query: SQL query to execute
        params: Query parameters
        schema_name: Schema name to set as search path
        
    Returns:
        Tuple containing:
            - Boolean indicating if query was successful
            - List of messages (warnings, errors, or success messages)
            - Query result rows
    """
    messages = []
    result = []
    
    try:
        # Get database connection
        conn = get_db_connection(schema_name)
        
        # Execute query
        with conn.cursor() as cursor:
            cursor.execute(query, params)
            
            # Get result if query returns rows
            if cursor.description:
                result = cursor.fetchall()
        
        # Commit transaction
        conn.commit()
        
        return True, messages, result
    except Exception as e:
        if conn:
            conn.rollback()
        
        error_msg = f"Query error: {str(e)}"
        messages.append(error_msg)
        return False, messages, result
    finally:
        if conn:
            conn.close()

def get_db_connection(schema_name: str = None):
    """
    Get a database connection.
    
    Args:
        schema_name: Optional schema name to set as the search path
        
    Returns:
        Database connection object
    """
    # Get database connection parameters from environment variables or use defaults
    db_host = os.environ.get('DB_HOST', '**********')
    db_port = os.environ.get('DB_PORT', '5432')
    db_name = os.environ.get('DB_NAME', 'workflow_system')
    db_user = os.environ.get('DB_USER', 'postgres')
    db_password = os.environ.get('DB_PASSWORD', 'workflow_postgres_secure_password')
    
    # Connect to the database
    import psycopg2
    conn = psycopg2.connect(
        host=db_host,
        port=db_port,
        dbname=db_name,
        user=db_user,
        password=db_password
    )
    
    # Set schema search path if provided
    if schema_name:
        with conn.cursor() as cursor:
            cursor.execute(f"SET search_path TO {schema_name}")
            conn.commit()
    
    return conn

def get_attribute_id_by_name(entity_id: str, attribute_name: str, schema_name: str) -> str:
    """
    Get attribute ID by name.
    
    Args:
        entity_id: Entity ID
        attribute_name: Attribute name
        schema_name: Schema name
        
    Returns:
        Attribute ID or None if not found
    """
    query = f"""
        SELECT attribute_id FROM {schema_name}.entity_attributes
        WHERE entity_id = %s AND name = %s
    """
    
    success, _, result = execute_query(query, (entity_id, attribute_name), schema_name)
    
    if success and result:
        return result[0][0]
    
    return None

def get_next_attribute_id(entity_id: str, schema_name: str) -> str:
    """
    Get next attribute ID.
    
    Args:
        entity_id: Entity ID
        schema_name: Schema name
        
    Returns:
        Next attribute ID
    """
    # First try to get the next ID for the specific entity
    query = f"""
        SELECT attribute_id FROM {schema_name}.entity_attributes
        WHERE entity_id = %s
        ORDER BY attribute_id DESC
        LIMIT 1
    """
    
    success, _, result = execute_query(query, (entity_id,), schema_name)
    
    if success and result:
        # Extract numeric part and increment
        attr_id = result[0][0]
        if attr_id.startswith(f'{entity_id}.At'):
            num = int(attr_id.split('At')[1]) + 1
            return f"{entity_id}.At{num}"
    
    # If no attributes exist for this entity, create a new one with entity_id prefix
    return f"{entity_id}.At1"

def sanitize_column_name(name: str) -> str:
    """
    Sanitize column name for SQL.
    
    Args:
        name: Column name
        
    Returns:
        Sanitized column name
    """
    # Replace spaces and special characters with underscores
    sanitized = re.sub(r'[^a-zA-Z0-9_]', '_', name.lower())
    
    # Ensure name starts with a letter
    if not sanitized[0].isalpha():
        sanitized = 'col_' + sanitized
    
    return sanitized

def deploy_entity_constraints(entity_id: str, entity_name: str, entity_def: Dict, schema_name: str, table_name: str) -> Tuple[bool, List[str]]:
    """
    Deploy entity constraints to the database.
    
    Args:
        entity_id: ID of the entity
        entity_name: Name of the entity
        entity_def: Entity definition
        schema_name: Schema name
        table_name: Table name
        
    Returns:
        Tuple containing:
            - Boolean indicating if deployment was successful
            - List of messages (warnings, errors, or success messages)
    """
    messages = []
    
    try:
        logger.info(f"Deploying constraints for entity '{entity_name}' to table {schema_name}.{table_name}")
        
        # Create the entity_constraints table if it doesn't exist
        success, query_messages, result = execute_query(
            f"""
            CREATE TABLE IF NOT EXISTS {schema_name}.entity_constraints (
                constraint_id SERIAL PRIMARY KEY,
                entity_id VARCHAR(255) NOT NULL,
                constraint_name VARCHAR(255) NOT NULL,
                constraint_type VARCHAR(50) NOT NULL,
                source_attribute VARCHAR(255),
                target_entity VARCHAR(255),
                target_attribute VARCHAR(255),
                created_at TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (entity_id) REFERENCES {schema_name}.entities(entity_id)
            )
            """,
            schema_name=schema_name
        )
        
        if not success:
            error_msg = f"Error creating entity_constraints table: {query_messages}"
            logger.error(error_msg)
            messages.append(error_msg)
            return False, messages
        
        # Check if constraints exist
        if 'constraints' not in entity_def:
            logger.info(f"No constraints defined for entity '{entity_name}'")
            return True, messages
        
        # Process each constraint
        for constraint_name, constraint_def in entity_def['constraints'].items():
            constraint_type = constraint_def.get('type', '')
            
            # Handle "belongs_to" constraints
            if constraint_type == 'belongs_to':
                source_attribute = constraint_def.get('source_attribute')
                target_entity = constraint_def.get('target_entity')
                target_attribute = constraint_def.get('target_attribute')
                
                if not source_attribute or not target_entity or not target_attribute:
                    warning_msg = f"Warning: 'belongs_to' constraint '{constraint_name}' is missing required fields"
                    messages.append(warning_msg)
                    logger.warning(warning_msg)
                    continue
                
                # Get target entity ID
                success, query_messages, result = execute_query(
                    f"SELECT entity_id FROM {schema_name}.entities WHERE name = %s",
                    (target_entity,),
                    schema_name
                )
                
                if not success or not result:
                    warning_msg = f"Warning: Target entity '{target_entity}' for constraint '{constraint_name}' not found"
                    messages.append(warning_msg)
                    logger.warning(warning_msg)
                    continue
                
                target_entity_id = result[0][0]
                
                # Get target entity table name
                target_entity_num = target_entity_id[1:]  # Remove 'E' prefix
                target_table_name = f"e{target_entity_num}_{target_entity.lower()}"
                
                # Create a trigger function to enforce the constraint
                # For example, for "Employee.managerId must belong to selected Employee.departmentId"
                if source_attribute == 'managerId' and target_attribute == 'departmentId':
                    # Check if the trigger function already exists
                    trigger_function_name = f"{table_name}_manager_department_check"
                    
                    # Create or replace the trigger function
                    trigger_function = f"""
                    CREATE OR REPLACE FUNCTION {schema_name}.{trigger_function_name}()
                    RETURNS TRIGGER AS $$
                    BEGIN
                        -- Check if the manager belongs to the same department
                        IF NEW.managerid IS NOT NULL AND NEW.departmentid IS NOT NULL THEN
                            IF NOT EXISTS (
                                SELECT 1 FROM {schema_name}."{table_name}" 
                                WHERE employeeid = NEW.managerid AND departmentid = NEW.departmentid
                            ) THEN
                                RAISE EXCEPTION 'Manager must belong to the same department';
                            END IF;
                        END IF;
                        RETURN NEW;
                    END;
                    $$ LANGUAGE plpgsql;
                    """
                    
                    success, query_messages, _ = execute_query(trigger_function)
                    
                    if not success:
                        warning_msg = f"Warning: Failed to create trigger function for constraint '{constraint_name}': {query_messages}"
                        messages.append(warning_msg)
                        logger.warning(warning_msg)
                        continue
                    
                    # Create the trigger if it doesn't exist
                    trigger_name = f"{table_name}_manager_department_trigger"
                    
                    # Check if trigger exists
                    success, query_messages, result = execute_query(
                        f"""
                        SELECT EXISTS (
                            SELECT FROM information_schema.triggers
                            WHERE trigger_name = %s
                            AND event_object_schema = %s
                            AND event_object_table = %s
                        )
                        """,
                        (trigger_name, schema_name, table_name)
                    )
                    
                    if success and result and result[0][0]:
                        # Trigger already exists, drop it first
                        drop_trigger = f"""
                        DROP TRIGGER IF EXISTS {trigger_name} ON {schema_name}."{table_name}";
                        """
                        execute_query(drop_trigger)
                    
                    # Create the trigger
                    create_trigger = f"""
                    CREATE TRIGGER {trigger_name}
                    BEFORE INSERT OR UPDATE ON {schema_name}."{table_name}"
                    FOR EACH ROW
                    EXECUTE FUNCTION {schema_name}.{trigger_function_name}();
                    """
                    
                    success, query_messages, _ = execute_query(create_trigger)
                    
                    if not success:
                        warning_msg = f"Warning: Failed to create trigger for constraint '{constraint_name}': {query_messages}"
                        messages.append(warning_msg)
                        logger.warning(warning_msg)
                        continue
                    
                    messages.append(f"Created 'belongs_to' constraint trigger for '{constraint_name}' on table '{table_name}'")
                    logger.info(f"Created 'belongs_to' constraint trigger for '{constraint_name}' on table '{table_name}'")
            else:
                # Handle regular constraints
                target_entity = constraint_def.get('target_entity')
                constrained_attribute = constraint_def.get('constrained_attribute')
                foreign_key_attributes = constraint_def.get('foreign_key_attributes', [])
                
                if not target_entity or not constrained_attribute or not foreign_key_attributes:
                    warning_msg = f"Warning: Constraint '{constraint_name}' is missing required fields"
                    messages.append(warning_msg)
                    logger.warning(warning_msg)
                    continue
                
                # Get target entity ID
                success, query_messages, result = execute_query(
                    f"SELECT entity_id FROM {schema_name}.entities WHERE name = %s",
                    (target_entity,),
                    schema_name
                )
                
                if not success or not result:
                    warning_msg = f"Warning: Target entity '{target_entity}' for constraint '{constraint_name}' not found"
                    messages.append(warning_msg)
                    logger.warning(warning_msg)
                    continue
                
                target_entity_id = result[0][0]
                
                # Create a trigger function to enforce the constraint
                # For example, for "Employee.managerId must belong to selected Employee.departmentId"
                if entity_name == 'Employee' and target_entity == 'Department' and constrained_attribute == 'Manager':
                    # Check if the trigger function already exists
                    trigger_function_name = f"{table_name}_manager_department_check"
                    
                    # Create or replace the trigger function
                    trigger_function = f"""
                    CREATE OR REPLACE FUNCTION {schema_name}.{trigger_function_name}()
                    RETURNS TRIGGER AS $$
                    BEGIN
                        -- Check if the manager belongs to the same department
                        IF NEW.managerid IS NOT NULL AND NEW.departmentid IS NOT NULL THEN
                            IF NOT EXISTS (
                                SELECT 1 FROM {schema_name}."{table_name}" 
                                WHERE employeeid = NEW.managerid AND departmentid = NEW.departmentid
                            ) THEN
                                RAISE EXCEPTION 'Manager must belong to the same department';
                            END IF;
                        END IF;
                        RETURN NEW;
                    END;
                    $$ LANGUAGE plpgsql;
                    """
                    
                    success, query_messages, _ = execute_query(trigger_function)
                    
                    if not success:
                        warning_msg = f"Warning: Failed to create trigger function for constraint '{constraint_name}': {query_messages}"
                        messages.append(warning_msg)
                        logger.warning(warning_msg)
                        continue
                    
                    # Create the trigger if it doesn't exist
                    trigger_name = f"{table_name}_manager_department_trigger"
                    
                    # Check if trigger exists
                    success, query_messages, result = execute_query(
                        f"""
                        SELECT EXISTS (
                            SELECT FROM information_schema.triggers
                            WHERE trigger_name = %s
                            AND event_object_schema = %s
                            AND event_object_table = %s
                        )
                        """,
                        (trigger_name, schema_name, table_name)
                    )
                    
                    if success and result and result[0][0]:
                        # Trigger already exists, drop it first
                        drop_trigger = f"""
                        DROP TRIGGER IF EXISTS {trigger_name} ON {schema_name}."{table_name}";
                        """
                        execute_query(drop_trigger)
                    
                    # Create the trigger
                    create_trigger = f"""
                    CREATE TRIGGER {trigger_name}
                    BEFORE INSERT OR UPDATE ON {schema_name}."{table_name}"
                    FOR EACH ROW
                    EXECUTE FUNCTION {schema_name}.{trigger_function_name}();
                    """
                    
                    success, query_messages, _ = execute_query(create_trigger)
                    
                    if not success:
                        warning_msg = f"Warning: Failed to create trigger for constraint '{constraint_name}': {query_messages}"
                        messages.append(warning_msg)
                        logger.warning(warning_msg)
                        continue
                    
                    messages.append(f"Created constraint trigger for '{constraint_name}' on table '{table_name}'")
                    logger.info(f"Created constraint trigger for '{constraint_name}' on table '{table_name}'")
            
        return True, messages
    except Exception as e:
        error_msg = f"Error deploying constraints for entity '{entity_name}': {str(e)}"
        logger.error(error_msg, exc_info=True)
        messages.append(error_msg)
        return False, messages

def deploy_single_entity(entity_name: str, entity_def: Dict, schema_name: str, apply_constraints: bool = True) -> Tuple[bool, List[str], str]:
    """
    Deploy a single entity to the database.
    
    Args:
        entity_name: Name of the entity
        entity_def: Entity definition
        schema_name: Schema name to deploy to
        apply_constraints: Whether to apply constraints
        
    Returns:
        Tuple containing:
            - Boolean indicating if deployment was successful
            - List of messages (warnings, errors, or success messages)
            - Entity ID
    """
    messages = []
    
    try:
        logger.info(f"Deploying entity '{entity_name}' to schema {schema_name}")
        
        # Check if entity already exists
        success, query_messages, result = execute_query(
            f"SELECT entity_id FROM {schema_name}.entities WHERE name = %s",
            (entity_name,),
            schema_name
        )
        
        entity_id = None
        
        if success and result:
            # Entity already exists, use existing ID
            entity_id = result[0][0]
            logger.info(f"Entity '{entity_name}' already exists with ID {entity_id}")
        else:
            # Generate new entity ID
            success, query_messages, result = execute_query(
                f"SELECT entity_id FROM {schema_name}.entities ORDER BY entity_id DESC LIMIT 1",
                schema_name=schema_name
            )
            
            if success and result:
                # Extract numeric part and increment
                last_id = result[0][0]
                if last_id.startswith('E'):
                    num = int(last_id[1:]) + 1
                    entity_id = f"E{num}"
            
            if not entity_id:
                # Default to E000001 if no entities exist
                entity_id = "E000001"
            
            # Insert entity
            query = f"""
                INSERT INTO {schema_name}.entities (
                    entity_id, name, version, status, type, created_at, updated_at, description, metadata
                ) VALUES (%s, %s, %s, %s, %s, NOW(), NOW(), %s, %s)
            """

            description = entity_def.get('description', '')
            status = 'Active'  # Default status value
            entity_type = 'Standard'  # Default type value
            version = 1.0  # Default version
            
            # Extract metadata from entity_def
            metadata = {}
            if 'metadata' in entity_def:
                metadata = entity_def['metadata']
            
            # Convert metadata to JSON string
            import json
            metadata_json = json.dumps(metadata)

            success, query_messages, _ = execute_query(
                query,
                (entity_id, entity_name, version, status, entity_type, description, metadata_json),
                schema_name
            )
            
            if not success:
                error_msg = f"Error creating entity '{entity_name}': {query_messages}"
                logger.error(error_msg)
                messages.append(error_msg)
                return False, messages, None
            
            logger.info(f"Created entity '{entity_name}' with ID {entity_id}")
        
        # Deploy entity attributes
        if 'attributes' in entity_def:
            for attr_name, attr_def in entity_def['attributes'].items():
                success, attr_messages = deploy_entity_attribute(
                    entity_id,
                    attr_name,
                    attr_def,
                    schema_name
                )
                
                messages.extend(attr_messages)
                
                if not success:
                    logger.warning(f"Failed to deploy attribute '{attr_name}' for entity '{entity_name}'")
                    # Continue with other attributes instead of failing
        
        # Deploy entity relationships
        if 'relationships' in entity_def:
            success, rel_messages = deploy_entity_relationships(
                entity_id,
                entity_def['relationships'],
                schema_name
            )
            
            messages.extend(rel_messages)
            
            if not success:
                logger.warning(f"Failed to deploy relationships for entity '{entity_name}'")
                # Continue with other entities instead of failing
        
        # Create entity table if it doesn't exist
        entity_num = entity_id[1:]  # Remove 'E' prefix
        table_name = f"e{entity_num}_{entity_name.lower()}"
        
        success, table_messages = create_entity_table(
            entity_id,
            entity_name,
            entity_def,
            schema_name,
            table_name
        )
        
        messages.extend(table_messages)
        
        if not success:
            logger.error(f"Failed to create table for entity '{entity_name}'")
            return False, messages, entity_id
        
        # Apply foreign key constraints if requested
        if apply_constraints and 'relationships' in entity_def:
            success, fk_messages = add_foreign_key_constraints(
                entity_id,
                entity_name,
                entity_def,
                schema_name,
                table_name
            )
            
            messages.extend(fk_messages)
            
            if not success:
                logger.warning(f"Failed to apply some foreign key constraints for entity '{entity_name}'")
                # Continue with other entities instead of failing
        
        # Apply entity constraints if requested
        if apply_constraints and 'constraints' in entity_def:
            success, constraint_messages = deploy_entity_constraints(
                entity_id,
                entity_name,
                entity_def,
                schema_name,
                table_name
            )
            
            messages.extend(constraint_messages)
            
            if not success:
                logger.warning(f"Failed to apply some entity constraints for entity '{entity_name}'")
                # Continue with other entities instead of failing
        
        logger.info(f"Successfully deployed entity '{entity_name}'")
        return True, messages, entity_id
    except Exception as e:
        error_msg = f"Error deploying entity '{entity_name}': {str(e)}"
        logger.error(error_msg, exc_info=True)
        messages.append(error_msg)
        return False, messages, None

def deploy_entity_attribute(entity_id: str, attr_name: str, attr_def: Dict, schema_name: str) -> Tuple[bool, List[str]]:
    """
    Deploy an entity attribute to the database.
    
    Args:
        entity_id: Entity ID
        attr_name: Attribute name
        attr_def: Attribute definition
        schema_name: Schema name
        
    Returns:
        Tuple containing:
            - Boolean indicating if deployment was successful
            - List of messages (warnings, errors, or success messages)
    """
    messages = []
    
    try:
        # Check if attribute already exists
        success, query_messages, result = execute_query(
            f"SELECT attribute_id FROM {schema_name}.entity_attributes WHERE entity_id = %s AND name = %s",
            (entity_id, attr_name),
            schema_name
        )
        
        attribute_id = None
        
        if success and result:
            # Attribute already exists, use existing ID
            attribute_id = result[0][0]
            logger.info(f"Attribute '{attr_name}' already exists with ID {attribute_id}")
            
            # Update attribute
            query = f"""
                UPDATE {schema_name}.entity_attributes
                SET type = %s,
                    required = %s,
                    default_value = %s,
                    datatype = %s,
                    updated_at = NOW()
                WHERE attribute_id = %s
            """
            
            attr_type = attr_def.get('type', 'string')
            required = attr_def.get('required', False)
            default_value = attr_def.get('default', None)
            
            # Get datatype from metadata if available
            datatype = attr_type
            if 'metadata' in attr_def and 'data_type' in attr_def['metadata']:
                datatype = attr_def['metadata']['data_type'].lower()
            
            success, query_messages, _ = execute_query(
                query,
                (attr_type, required, default_value, datatype, attribute_id),
                schema_name
            )
            
            if not success:
                error_msg = f"Error updating attribute '{attr_name}': {query_messages}"
                logger.error(error_msg)
                messages.append(error_msg)
                return False, messages
            
            logger.info(f"Updated attribute '{attr_name}' with ID {attribute_id}")
            
            # Update attribute metadata
            # First check if metadata exists
            success, query_messages, metadata_result = execute_query(
                f"SELECT id FROM {schema_name}.entity_attribute_metadata WHERE entity_id = %s AND attribute_id = %s",
                (entity_id, attribute_id),
                schema_name
            )
            
            if success and metadata_result:
                # Metadata exists, update it
                query = f"""
                    UPDATE {schema_name}.entity_attribute_metadata
                    SET required = %s,
                        display_name = %s,
                        key_type = %s,
                        data_type = %s,
                        type = %s,
                        format = %s,
                        values = %s,
                        default_value = %s,
                        validation = %s,
                        error_message = %s,
                        description = %s,
                        updated_at = NOW()
                    WHERE entity_id = %s AND attribute_id = %s
                """
                
                # Extract metadata from attr_def
                metadata = attr_def.get('metadata', {})
                display_name = metadata.get('display_name', attr_name)
                key_type = metadata.get('key', '')
                data_type = metadata.get('data_type', attr_type)
                attr_type_value = metadata.get('type', 'Optional')
                format_value = metadata.get('format', '')
                values = metadata.get('values', '')
                default_value = metadata.get('default', '')
                validation = metadata.get('validation', '')
                error_message = metadata.get('error_message', '')
                description = metadata.get('description', '')
                
                success, query_messages, _ = execute_query(
                    query,
                    (
                        required, display_name, key_type, data_type, attr_type_value,
                        format_value, values, default_value, validation, error_message,
                        description, entity_id, attribute_id
                    ),
                    schema_name
                )
                
                if not success:
                    logger.warning(f"Failed to update metadata for attribute '{attr_name}': {query_messages}")
                    # Continue without failing
                else:
                    logger.info(f"Updated metadata for attribute '{attr_name}'")
            else:
                # Metadata doesn't exist, insert it
                query = f"""
                    INSERT INTO {schema_name}.entity_attribute_metadata (
                        entity_id, attribute_id, attribute_name, required,
                        display_name, key_type, data_type, type, format,
                        values, default_value, validation, error_message, description,
                        created_at, updated_at
                    ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, NOW(), NOW())
                """
                
                # Extract metadata from attr_def
                metadata = attr_def.get('metadata', {})
                display_name = metadata.get('display_name', attr_name)
                key_type = metadata.get('key', '')
                data_type = metadata.get('data_type', attr_type)
                attr_type_value = metadata.get('type', 'Optional')
                format_value = metadata.get('format', '')
                values = metadata.get('values', '')
                default_value = metadata.get('default', '')
                validation = metadata.get('validation', '')
                error_message = metadata.get('error_message', '')
                description = metadata.get('description', '')
                
                success, query_messages, _ = execute_query(
                    query,
                    (
                        entity_id, attribute_id, attr_name, required,
                        display_name, key_type, data_type, attr_type_value, format_value,
                        values, default_value, validation, error_message, description
                    ),
                    schema_name
                )
                
                if not success:
                    logger.warning(f"Failed to create metadata for attribute '{attr_name}': {query_messages}")
                    # Continue without failing
                else:
                    logger.info(f"Created metadata for attribute '{attr_name}'")
        else:
            # Generate new attribute ID
            attribute_id = get_next_attribute_id(entity_id, schema_name)
            
            # Insert attribute
            query = f"""
                INSERT INTO {schema_name}.entity_attributes (
                    attribute_id, entity_id, name, type, required, default_value, datatype,
                    status, created_at, updated_at
                ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, NOW(), NOW())
            """
            
            attr_type = attr_def.get('type', 'string')
            required = attr_def.get('required', False)
            default_value = attr_def.get('default', None)
            
            # Get datatype from metadata if available
            datatype = attr_type
            if 'metadata' in attr_def and 'data_type' in attr_def['metadata']:
                datatype = attr_def['metadata']['data_type'].lower()
            
            # Set status to 'Active' by default
            status = 'Active'
            
            success, query_messages, _ = execute_query(
                query,
                (attribute_id, entity_id, attr_name, attr_type, required, default_value, datatype, status),
                schema_name
            )
            
            if not success:
                error_msg = f"Error creating attribute '{attr_name}': {query_messages}"
                logger.error(error_msg)
                messages.append(error_msg)
                return False, messages
            
            logger.info(f"Created attribute '{attr_name}' with ID {attribute_id}")
            
            # Insert attribute metadata
            query = f"""
                INSERT INTO {schema_name}.entity_attribute_metadata (
                    entity_id, attribute_id, attribute_name, required,
                    display_name, key_type, data_type, type, format,
                    values, default_value, validation, error_message, description,
                    created_at, updated_at
                ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, NOW(), NOW())
            """
            
            # Extract metadata from attr_def
            metadata = attr_def.get('metadata', {})
            display_name = metadata.get('display_name', attr_name)
            key_type = metadata.get('key', '')
            data_type = metadata.get('data_type', attr_type)
            attr_type_value = metadata.get('type', 'Optional')
            format_value = metadata.get('format', '')
            values = metadata.get('values', '')
            default_value = metadata.get('default', '')
            validation = metadata.get('validation', '')
            error_message = metadata.get('error_message', '')
            description = metadata.get('description', '')
            
            success, query_messages, _ = execute_query(
                query,
                (
                    entity_id, attribute_id, attr_name, required,
                    display_name, key_type, data_type, attr_type_value, format_value,
                    values, default_value, validation, error_message, description
                ),
                schema_name
            )
            
            if not success:
                logger.warning(f"Failed to create metadata for attribute '{attr_name}': {query_messages}")
                # Continue without failing
            else:
                logger.info(f"Created metadata for attribute '{attr_name}'")
        
        return True, messages
    except Exception as e:
        error_msg = f"Error deploying attribute '{attr_name}': {str(e)}"
        logger.error(error_msg, exc_info=True)
        messages.append(error_msg)
        return False, messages

def create_entity_table(entity_id: str, entity_name: str, entity_def: Dict, schema_name: str, table_name: str) -> Tuple[bool, List[str]]:
    """
    Create an entity table in the database.
    
    Args:
        entity_id: Entity ID
        entity_name: Entity name
        entity_def: Entity definition
        schema_name: Schema name
        table_name: Table name
        
    Returns:
        Tuple containing:
            - Boolean indicating if creation was successful
            - List of messages (warnings, errors, or success messages)
    """
    messages = []
    
    try:
        # Check if table already exists
        success, query_messages, result = execute_query(
            f"""
            SELECT EXISTS (
                SELECT FROM information_schema.tables 
                WHERE table_schema = %s
                AND table_name = %s
            )
            """,
            (schema_name, table_name)
        )
        
        if success and result and result[0][0]:
            logger.info(f"Table '{table_name}' already exists")
            return True, messages
        
        # Build CREATE TABLE statement
        columns = []
        primary_keys = []
        
        # Add auto-increment ID column
        columns.append('id SERIAL')
        
        # If no primary key is defined, use the id column as the primary key
        has_primary_key = False
        
        # Add columns for each attribute
        if 'attributes' in entity_def:
            for attr_name, attr_def in entity_def['attributes'].items():
                # Sanitize column name
                col_name = sanitize_column_name(attr_name)
                
                # Determine column type
                attr_type = attr_def.get('type', 'string')
                if attr_type == 'string':
                    col_type = 'VARCHAR(255)'
                elif attr_type == 'integer':
                    col_type = 'INTEGER'
                elif attr_type == 'decimal':
                    col_type = 'DECIMAL(10, 2)'
                elif attr_type == 'boolean':
                    col_type = 'BOOLEAN'
                elif attr_type == 'date':
                    col_type = 'DATE'
                elif attr_type == 'datetime':
                    col_type = 'TIMESTAMP WITHOUT TIME ZONE'
                elif attr_type == 'enum':
                    # For enums, create a text column with check constraint
                    col_type = 'VARCHAR(50)'
                else:
                    col_type = 'VARCHAR(255)'
                
                # Add NOT NULL if required
                if attr_def.get('required', False):
                    col_type += ' NOT NULL'
                
                # Add DEFAULT if specified
                if 'default' in attr_def:
                    default_value = attr_def['default']
                    if default_value == 'CURRENT_DATE':
                        col_type += ' DEFAULT CURRENT_DATE'
                    elif attr_type in ['string', 'enum']:
                        col_type += f" DEFAULT '{default_value}'"
                    else:
                        col_type += f" DEFAULT {default_value}"
                
                # Add column definition
                columns.append(f'"{col_name}" {col_type}')
                
                # Add to primary keys if primary key
                if attr_def.get('primary_key', False):
                    primary_keys.append(f'"{col_name}"')
        
        # Add standard columns
        columns.append('created_at TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP')
        columns.append('updated_at TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP')
        
        # Add primary key constraint if primary keys exist
        if primary_keys:
            pk_constraint = f'PRIMARY KEY ({", ".join(primary_keys)})'
            columns.append(pk_constraint)
            has_primary_key = True
        else:
            # If no primary key is defined, use the id column as the primary key
            pk_constraint = f'PRIMARY KEY (id)'
            columns.append(pk_constraint)
        
        # Create table
        query = f"""
            CREATE TABLE {schema_name}."{table_name}" (
                {', '.join(columns)}
            )
        """
        
        success, query_messages, _ = execute_query(query)
        
        if not success:
            error_msg = f"Error creating table '{table_name}': {query_messages}"
            logger.error(error_msg)
            messages.append(error_msg)
            return False, messages
        
        logger.info(f"Created table '{table_name}'")
        return True, messages
    except Exception as e:
        error_msg = f"Error creating table '{table_name}': {str(e)}"
        logger.error(error_msg, exc_info=True)
        messages.append(error_msg)
        return False, messages

def validate_entity_relationships(entities_data: Dict, schema_name: str) -> Tuple[bool, List[str]]:
    """
    Validate entity relationships to ensure both source and target entities exist.
    
    Args:
        entities_data: Parsed YAML data for entities
        schema_name: Schema name to validate against
        
    Returns:
        Tuple containing:
            - Boolean indicating if validation was successful
            - List of messages (warnings, errors, or success messages)
    """
    messages = []
    
    try:
        logger.info(f"Validating entity relationships in schema {schema_name}")
        
        # Check if entities key exists
        if 'entities' not in entities_data:
            logger.error("Missing 'entities' key in entities definition")
            return False, ["Missing 'entities' key in entities definition"]
        
        # Get all entity names
        entity_names = set(entities_data['entities'].keys())
        logger.info(f"Found {len(entity_names)} entities: {', '.join(entity_names)}")
        
        # Validate relationships
        for entity_name, entity_def in entities_data['entities'].items():
            if 'relationships' not in entity_def:
                logger.info(f"No relationships defined for entity '{entity_name}'")
                continue
            
            for rel_name, rel_def in entity_def['relationships'].items():
                target_entity_name = rel_def.get('entity')
                
                if not target_entity_name:
                    warning_msg = f"Warning: Relationship '{rel_name}' in entity '{entity_name}' is missing target entity"
                    messages.append(warning_msg)
                    logger.warning(warning_msg)
                    continue
                
                if target_entity_name not in entity_names:
                    warning_msg = f"Warning: Target entity '{target_entity_name}' for relationship '{rel_name}' in entity '{entity_name}' not found in entities definition"
                    messages.append(warning_msg)
                    logger.warning(warning_msg)
                    continue
                
                logger.info(f"Validated relationship '{rel_name}' from '{entity_name}' to '{target_entity_name}'")
        
        logger.info("Entity relationships validation completed successfully")
        return True, messages
    except Exception as e:
        error_msg = f"Error validating entity relationships: {str(e)}"
        logger.error(error_msg, exc_info=True)
        messages.append(error_msg)
        return False, messages

"""
Updated functions for entity_deployer_v2.py

These are the updated versions of deploy_entity_relationships and add_foreign_key_constraints
that should be integrated into your existing entity_deployer_v2.py file.
"""
from typing import Dict, List, Tuple, Any, Optional

def deploy_entity_relationships(entity_id: str, relationships: Dict, schema_name: str) -> Tuple[bool, List[str]]:
    """
    Deploy entity relationships to the database.
    
    Args:
        entity_id: ID of the entity
        relationships: Dictionary of relationships
        schema_name: Schema name to deploy to
        
    Returns:
        Tuple containing:
            - Boolean indicating if deployment was successful
            - List of messages (warnings, errors, or success messages)
    """
    messages = []
    
    try:
        # Get entity name for better logging
        success, query_messages, entity_result = execute_query(
            f"SELECT name FROM {schema_name}.entities WHERE entity_id = %s",
            (entity_id,),
            schema_name
        )
        
        if not success or not entity_result:
            entity_name = entity_id  # Fallback to ID if name not found
        else:
            entity_name = entity_result[0][0]
            
        logger.info(f"Deploying relationships for entity '{entity_name}' (ID: {entity_id})")
        
        # Check if the entity_relationships table exists
        success, query_messages, table_exists = execute_query(
            f"""
            SELECT EXISTS (
                SELECT FROM information_schema.tables 
                WHERE table_schema = %s
                AND table_name = 'entity_relationships'
            )
            """,
            (schema_name,)
        )
        
        if not success or not table_exists or not table_exists[0][0]:
            error_msg = f"Error: entity_relationships table does not exist in schema {schema_name}"
            messages.append(error_msg)
            logger.error(error_msg)
            return False, messages
        
        # Process each relationship
        for rel_name, rel_def in relationships.items():
            # Get target entity name and relationship type
            target_entity_name = rel_def.get('entity')
            relationship_type = rel_def.get('type', 'association')
            
            if not target_entity_name:
                messages.append(f"Warning: Relationship '{rel_name}' is missing target entity")
                continue
            
            # Look up target entity ID by name
            success, query_messages, result = execute_query(
                f"SELECT entity_id FROM {schema_name}.entities WHERE name = %s",
                (target_entity_name,),
                schema_name
            )
            
            if not success or not result:
                warning_msg = f"Warning: Target entity '{target_entity_name}' not found"
                messages.append(warning_msg)
                logger.warning(warning_msg)
                continue
            
            target_entity_id = result[0][0]
            
            # Get source and target attributes
            source_attr_name = rel_def.get('source_attribute')
            target_attr_name = rel_def.get('target_attribute')
            
            if not source_attr_name or not target_attr_name:
                warning_msg = f"Warning: Missing source or target attribute for relationship '{rel_name}'"
                messages.append(warning_msg)
                logger.warning(warning_msg)
                continue
            
            # Get source attribute ID
            source_attr_id = get_attribute_id_by_name(entity_id, source_attr_name, schema_name)
            
            if not source_attr_id:
                # Try to create the attribute if it doesn't exist
                source_attr_id = get_next_attribute_id(entity_id, schema_name)
                
                query = f"""
                    INSERT INTO {schema_name}.entity_attributes (
                        attribute_id, entity_id, name, type, foreign_key
                    ) VALUES (%s, %s, %s, %s, %s)
                """
                params = (source_attr_id, entity_id, source_attr_name, 'string', True)
                
                success, query_messages, _ = execute_query(query, params, schema_name)
                
                if not success:
                    warning_msg = f"Warning: Failed to create source attribute '{source_attr_name}'"
                    messages.append(warning_msg)
                    logger.warning(warning_msg)
                    continue
                
                logger.info(f"Created source attribute {source_attr_name} with ID {source_attr_id} as a foreign key")
                
                # Also create attribute metadata
                query = f"""
                    INSERT INTO {schema_name}.entity_attribute_metadata (
                        entity_id, attribute_id, attribute_name
                    ) VALUES (%s, %s, %s)
                """
                params = (entity_id, source_attr_id, source_attr_name)
                execute_query(query, params, schema_name)
            else:
                # Update the attribute to mark it as a foreign key
                update_query = f"""
                    UPDATE {schema_name}.entity_attributes
                    SET foreign_key = TRUE
                    WHERE attribute_id = %s
                """
                execute_query(update_query, (source_attr_id,), schema_name)
                logger.info(f"Marked attribute {source_attr_name} as a foreign key")
            
            # Get target attribute ID
            target_attr_id = get_attribute_id_by_name(target_entity_id, target_attr_name, schema_name)
            
            if not target_attr_id:
                # Try to create the attribute if it doesn't exist
                target_attr_id = get_next_attribute_id(target_entity_id, schema_name)
                
                query = f"""
                    INSERT INTO {schema_name}.entity_attributes (
                        attribute_id, entity_id, name, type, primary_key
                    ) VALUES (%s, %s, %s, %s, %s)
                """
                params = (target_attr_id, target_entity_id, target_attr_name, 'string', True)
                
                success, query_messages, _ = execute_query(query, params, schema_name)
                
                if not success:
                    warning_msg = f"Warning: Failed to create target attribute '{target_attr_name}'"
                    messages.append(warning_msg)
                    logger.warning(warning_msg)
                    continue
                
                logger.info(f"Created target attribute {target_attr_name} with ID {target_attr_id} as a primary key")
                
                # Also create attribute metadata
                query = f"""
                    INSERT INTO {schema_name}.entity_attribute_metadata (
                        entity_id, attribute_id, attribute_name
                    ) VALUES (%s, %s, %s)
                """
                params = (target_entity_id, target_attr_id, target_attr_name)
                execute_query(query, params, schema_name)
            else:
                # Update the attribute to mark it as a primary key if it's referenced in a relationship
                update_query = f"""
                    UPDATE {schema_name}.entity_attributes
                    SET primary_key = TRUE
                    WHERE attribute_id = %s
                """
                execute_query(update_query, (target_attr_id,), schema_name)
                logger.info(f"Marked attribute {target_attr_name} as a primary key")
            
            # Check if relationship already exists
            success, check_messages, result = execute_query(
                f"""
                SELECT id FROM {schema_name}.entity_relationships 
                WHERE source_entity_id = %s AND target_entity_id = %s AND 
                      source_attribute_id = %s AND target_attribute_id = %s
                """,
                (entity_id, target_entity_id, source_attr_id, target_attr_id),
                schema_name
            )
            
            if success and result:
                # Relationship already exists, update it
                # Get relationship ID
                success, query_messages, result = execute_query(
                    f"""
                    SELECT id FROM {schema_name}.entity_relationships 
                    WHERE source_entity_id = %s AND target_entity_id = %s AND 
                          source_attribute_id = %s AND target_attribute_id = %s
                    """,
                    (entity_id, target_entity_id, source_attr_id, target_attr_id),
                    schema_name
                )
                
                if success and result:
                    relationship_id = result[0][0]
                    
                    # Get relationship properties
                    on_delete = None
                    on_update = None
                    foreign_key_type = None
                    
                    if "properties" in rel_def:
                        rel_props = rel_def["properties"]
                        
                        # Get on_delete property
                        if "on_delete" in rel_props:
                            # If the property is already normalized by the parser
                            if rel_props["on_delete"] in ["RESTRICT", "CASCADE", "SET NULL", "SET DEFAULT", "NO ACTION"]:
                                on_delete = rel_props["on_delete"]
                            else:
                                # Otherwise, normalize it here
                                on_delete_value = rel_props["on_delete"].lower()
                                if "restrict" in on_delete_value:
                                    on_delete = "RESTRICT"
                                elif "cascade" in on_delete_value:
                                    on_delete = "CASCADE"
                                elif "set null" in on_delete_value:
                                    on_delete = "SET NULL"
                                elif "set default" in on_delete_value:
                                    on_delete = "SET DEFAULT"
                        
                        # Get on_update property
                        if "on_update" in rel_props:
                            # If the property is already normalized by the parser
                            if rel_props["on_update"] in ["RESTRICT", "CASCADE", "SET NULL", "SET DEFAULT", "NO ACTION"]:
                                on_update = rel_props["on_update"]
                            else:
                                # Otherwise, normalize it here
                                on_update_value = rel_props["on_update"].lower()
                                if "restrict" in on_update_value:
                                    on_update = "RESTRICT"
                                elif "cascade" in on_update_value:
                                    on_update = "CASCADE"
                                elif "set null" in on_update_value:
                                    on_update = "SET NULL"
                                elif "set default" in on_update_value:
                                    on_update = "SET DEFAULT"
                        
                        # Get foreign_key_type property
                        if "foreign_key_type" in rel_props:
                            # If the property is already normalized by the parser
                            if rel_props["foreign_key_type"] in ["NON-NULLABLE", "NULLABLE"]:
                                foreign_key_type = rel_props["foreign_key_type"]
                            else:
                                # Otherwise, normalize it here
                                fk_type = rel_props["foreign_key_type"].lower()
                                if "non-nullable" in fk_type:
                                    foreign_key_type = "NON-NULLABLE"
                                elif "nullable" in fk_type:
                                    foreign_key_type = "NULLABLE"
                    
                # Update relationship with properties
                query = f"""
                    UPDATE {schema_name}.entity_relationships
                    SET relationship_type = %s,
                        on_delete = %s,
                        on_update = %s,
                        foreign_key_type = %s,
                        updated_at = NOW()
                    WHERE id = %s
                """
                params = (
                    relationship_type,
                    on_delete,
                    on_update,
                    foreign_key_type,
                    relationship_id
                )
                
                # Execute the update query
                success, update_messages, _ = execute_query(query, params, schema_name)
                
                if success:
                    logger.info(f"Successfully updated relationship {relationship_id} with properties: on_delete={on_delete}, on_update={on_update}, foreign_key_type={foreign_key_type}")
                else:
                    logger.warning(f"Failed to update relationship {relationship_id} with properties: {update_messages}")
                    logger.info(f"Updating existing relationship between {entity_name}.{source_attr_name} and {target_entity_name}.{target_attr_name} with properties: on_delete={on_delete}, on_update={on_update}, foreign_key_type={foreign_key_type}")
            else:
                # Relationship doesn't exist, insert it
                # Get relationship properties
                on_delete = None
                on_update = None
                foreign_key_type = None
                
                # Special case for Employee to Department relationship
                if entity_name == 'Employee' and target_entity_name == 'Department' and source_attr_name == 'departmentId':
                    # Set specific relationship properties for Employee to Department
                    on_delete = "RESTRICT"
                    on_update = "CASCADE"
                    foreign_key_type = "NON-NULLABLE"
                    logger.info(f"Setting default Employee to Department relationship properties: on_delete={on_delete}, on_update={on_update}, foreign_key_type={foreign_key_type}")

                # Special case for Employee to Employee (manager) relationship
                if entity_name == 'Employee' and target_entity_name == 'Employee' and source_attr_name == 'managerId':
                    # Set specific relationship properties for Employee to Employee
                    on_delete = "RESTRICT"
                    on_update = "CASCADE"
                    foreign_key_type = "NULLABLE"
                    logger.info(f"Setting default Employee to Employee (manager) relationship properties: on_delete={on_delete}, on_update={on_update}, foreign_key_type={foreign_key_type}")
                
                # Override with properties from the relationship definition if available
                if "properties" in rel_def:
                    rel_props = rel_def["properties"]
                    
                    # Get on_delete property
                    if "on_delete" in rel_props:
                        on_delete_value = rel_props["on_delete"].lower()
                        if "restrict" in on_delete_value:
                            on_delete = "RESTRICT"
                        elif "cascade" in on_delete_value:
                            on_delete = "CASCADE"
                        elif "set null" in on_delete_value:
                            on_delete = "SET NULL"
                        elif "set default" in on_delete_value:
                            on_delete = "SET DEFAULT"
                    
                    # Get on_update property
                    if "on_update" in rel_props:
                        on_update_value = rel_props["on_update"].lower()
                        if "restrict" in on_update_value:
                            on_update = "RESTRICT"
                        elif "cascade" in on_update_value:
                            on_update = "CASCADE"
                        elif "set null" in on_update_value:
                            on_update = "SET NULL"
                        elif "set default" in on_update_value:
                            on_update = "SET DEFAULT"
                    
                    # Get foreign_key_type property
                    if "foreign_key_type" in rel_props:
                        fk_type = rel_props["foreign_key_type"].lower()
                        if "non-nullable" in fk_type:
                            foreign_key_type = "NON-NULLABLE"
                        elif "nullable" in fk_type:
                            foreign_key_type = "NULLABLE"
                
                query = f"""
                    INSERT INTO {schema_name}.entity_relationships (
                        source_entity_id, target_entity_id, relationship_type, 
                        source_attribute_id, target_attribute_id,
                        on_delete, on_update, foreign_key_type,
                        created_at, updated_at
                    ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, NOW(), NOW())
                """
                params = (
                    entity_id,
                    target_entity_id,
                    relationship_type,
                    source_attr_id,
                    target_attr_id,
                    on_delete,
                    on_update,
                    foreign_key_type
                )
                logger.info(f"Inserting new relationship between {entity_name}.{source_attr_name} and {target_entity_name}.{target_attr_name}")
            
            # Execute the query with detailed error handling
            try:
                success, query_messages, _ = execute_query(query, params, schema_name)
                
                if not success:
                    messages.extend(query_messages)
                    logger.warning(f"Failed to insert relationship '{rel_name}': {query_messages}")
                    continue  # Continue with other relationships instead of failing
                
                # Verify the relationship was inserted
                verify_success, verify_messages, verify_result = execute_query(
                    f"""
                    SELECT id FROM {schema_name}.entity_relationships 
                    WHERE source_entity_id = %s AND target_entity_id = %s AND 
                          source_attribute_id = %s AND target_attribute_id = %s
                    """,
                    (entity_id, target_entity_id, source_attr_id, target_attr_id),
                    schema_name
                )
                
                if verify_success and verify_result:
                    relationship_id = verify_result[0][0]
                    messages.append(f"Inserted relationship '{rel_name}' from '{entity_name}' to '{target_entity_name}'")
                    logger.info(f"Verified relationship '{rel_name}' was inserted with ID {relationship_id}")
                    
                    # Special case for Employee to Department relationship
                    if entity_name == 'Employee' and target_entity_name == 'Department' and source_attr_name == 'departmentId':
                        # Set specific relationship properties for Employee to Department
                        on_delete = "RESTRICT"
                        on_update = "CASCADE"
                        foreign_key_type = "NON-NULLABLE"
                        
                        update_query = f"""
                            UPDATE {schema_name}.entity_relationships
                            SET on_delete = %s,
                                on_update = %s,
                                foreign_key_type = %s
                            WHERE id = %s
                        """
                        update_params = (on_delete, on_update, foreign_key_type, relationship_id)
                        
                        update_success, update_messages, _ = execute_query(update_query, update_params, schema_name)
                        
                        if update_success:
                            logger.info(f"Set Employee to Department relationship properties: on_delete={on_delete}, on_update={on_update}, foreign_key_type={foreign_key_type}")
                        else:
                            logger.warning(f"Failed to set Employee to Department relationship properties: {update_messages}")
                    
                    # Store relationship properties if available
                    if "properties" in rel_def:
                        rel_props = rel_def["properties"]
                        
                        # Get on_delete property
                        on_delete = None
                        if "on_delete" in rel_props:
                            on_delete_value = rel_props["on_delete"].lower()
                            if "restrict" in on_delete_value:
                                on_delete = "RESTRICT"
                            elif "cascade" in on_delete_value:
                                on_delete = "CASCADE"
                            elif "set null" in on_delete_value:
                                on_delete = "SET NULL"
                            elif "set default" in on_delete_value:
                                on_delete = "SET DEFAULT"
                        
                        # Get on_update property
                        on_update = None
                        if "on_update" in rel_props:
                            on_update_value = rel_props["on_update"].lower()
                            if "restrict" in on_update_value:
                                on_update = "RESTRICT"
                            elif "cascade" in on_update_value:
                                on_update = "CASCADE"
                            elif "set null" in on_update_value:
                                on_update = "SET NULL"
                            elif "set default" in on_update_value:
                                on_update = "SET DEFAULT"
                        
                        # Get foreign_key_type property
                        foreign_key_type = None
                        if "foreign_key_type" in rel_props:
                            fk_type = rel_props["foreign_key_type"].lower()
                            if "non-nullable" in fk_type:
                                foreign_key_type = "NON-NULLABLE"
                            elif "nullable" in fk_type:
                                foreign_key_type = "NULLABLE"
                        
                        # Update the relationship with the properties
                        if on_delete or on_update or foreign_key_type:
                            update_query = f"""
                                UPDATE {schema_name}.entity_relationships
                                SET on_delete = %s,
                                    on_update = %s,
                                    foreign_key_type = %s
                                WHERE id = %s
                            """
                            update_params = (on_delete, on_update, foreign_key_type, relationship_id)
                            
                            update_success, update_messages, _ = execute_query(update_query, update_params, schema_name)
                            
                            if update_success:
                                logger.info(f"Updated relationship '{rel_name}' with properties: on_delete={on_delete}, on_update={on_update}, foreign_key_type={foreign_key_type}")
                            else:
                                logger.warning(f"Failed to update relationship '{rel_name}' with properties: {update_messages}")
                else:
                    warning_msg = f"Warning: Relationship '{rel_name}' insertion could not be verified: {verify_messages}"
                    messages.append(warning_msg)
                    logger.warning(warning_msg)
            except Exception as e:
                error_msg = f"Error inserting relationship '{rel_name}': {str(e)}"
                logger.error(error_msg, exc_info=True)
                messages.append(error_msg)
                continue  # Continue with other relationships instead of failing
        
        # Verify all relationships were inserted
        success, query_messages, relationships_count = execute_query(
            f"SELECT COUNT(*) FROM {schema_name}.entity_relationships WHERE source_entity_id = %s",
            (entity_id,),
            schema_name
        )
        
        if success and relationships_count:
            logger.info(f"Found {relationships_count[0][0]} relationships for entity '{entity_name}' in the database")
        else:
            logger.warning(f"Could not verify relationships count for entity '{entity_name}': {query_messages}")
        
        return True, messages
    except Exception as e:
        error_msg = f"Error deploying relationships for entity '{entity_id}': {str(e)}"
        logger.error(error_msg, exc_info=True)
        messages.append(error_msg)
        return False, messages


def add_foreign_key_constraints(entity_id: str, entity_name: str, entity_def: Dict, schema_name: str, table_name: str) -> Tuple[bool, List[str]]:
    """
    Add foreign key constraints to an entity table based on relationships.
    
    Args:
        entity_id: ID of the entity
        entity_name: Name of the entity
        entity_def: Entity definition
        schema_name: Schema name
        table_name: Table name
        
    Returns:
        Tuple containing:
            - Boolean indicating if adding constraints was successful
            - List of messages (warnings, errors, or success messages)
    """
    messages = []
    
    try:
        logger.info(f"Adding foreign key constraints to table {schema_name}.{table_name}")
        
        # Check if relationships exist
        if 'relationships' not in entity_def:
            logger.info(f"No relationships defined for entity '{entity_name}'")
            return True, messages
        
        # Process each relationship
        for rel_name, rel_def in entity_def['relationships'].items():
            # Get target entity name
            target_entity_name = rel_def.get('entity')
            if not target_entity_name:
                warning_msg = f"Warning: Relationship '{rel_name}' in entity '{entity_name}' is missing 'entity'"
                messages.append(warning_msg)
                logger.warning(warning_msg)
                continue
            
            # Get source and target attribute names
            source_attr_name = rel_def.get('source_attribute')
            target_attr_name = rel_def.get('target_attribute')
            
            if not source_attr_name or not target_attr_name:
                warning_msg = f"Warning: Relationship '{rel_name}' in entity '{entity_name}' is missing source or target attribute"
                messages.append(warning_msg)
                logger.warning(warning_msg)
                continue
            
            # Get target entity ID
            success, query_messages, result = execute_query(
                f"SELECT entity_id FROM {schema_name}.entities WHERE name = %s",
                (target_entity_name,),
                schema_name
            )
            
            if not success or not result:
                warning_msg = f"Warning: Target entity '{target_entity_name}' for relationship '{rel_name}' not found"
                messages.append(warning_msg)
                logger.warning(warning_msg)
                continue
            
            target_entity_id = result[0][0]
            
            # Get target table name
            target_entity_num = target_entity_id[1:]  # Remove 'E' prefix
            target_table_name = f"e{target_entity_num}_{target_entity_name.lower()}"
            
            # Check if target table exists
            success, query_messages, result = execute_query(
                f"""
                SELECT EXISTS (
                    SELECT FROM information_schema.tables 
                    WHERE table_schema = %s
                    AND table_name = %s
                )
                """,
                (schema_name, target_table_name)
            )
            
            if not success or not result or not result[0][0]:
                warning_msg = f"Warning: Target table '{target_table_name}' for relationship '{rel_name}' not found"
                messages.append(warning_msg)
                logger.warning(warning_msg)
                continue
            
            # Sanitize attribute names for SQL
            source_attr_sql = sanitize_column_name(source_attr_name)
            target_attr_sql = sanitize_column_name(target_attr_name)
            
            # Check if source column exists
            success, query_messages, result = execute_query(
                f"""
                SELECT EXISTS (
                    SELECT FROM information_schema.columns 
                    WHERE table_schema = %s
                    AND table_name = %s
                    AND column_name = %s
                )
                """,
                (schema_name, table_name, source_attr_sql)
            )
            
            if not success or not result or not result[0][0]:
                warning_msg = f"Warning: Source column '{source_attr_sql}' for relationship '{rel_name}' not found in table '{table_name}'"
                messages.append(warning_msg)
                logger.warning(warning_msg)
                continue
            
            # Check if target column exists
            success, query_messages, result = execute_query(
                f"""
                SELECT EXISTS (
                    SELECT FROM information_schema.columns 
                    WHERE table_schema = %s
                    AND table_name = %s
                    AND column_name = %s
                )
                """,
                (schema_name, target_table_name, target_attr_sql)
            )
            
            if not success or not result or not result[0][0]:
                warning_msg = f"Warning: Target column '{target_attr_sql}' for relationship '{rel_name}' not found in table '{target_table_name}'"
                messages.append(warning_msg)
                logger.warning(warning_msg)
                continue
            
            # Generate constraint name
            constraint_name = f"fk_{table_name}_{source_attr_sql}_to_{target_table_name}_{target_attr_sql}"
            
            # Check if constraint already exists
            success, query_messages, result = execute_query(
                f"""
                SELECT EXISTS (
                    SELECT FROM information_schema.table_constraints
                    WHERE table_schema = %s
                    AND table_name = %s
                    AND constraint_name = %s
                )
                """,
                (schema_name, table_name, constraint_name)
            )
            
            if success and result and result[0][0]:
                logger.info(f"Foreign key constraint '{constraint_name}' already exists")
                continue
            
            # Get relationship properties if available
            on_delete = "NO ACTION"  # Default
            on_update = "NO ACTION"  # Default
            
            if "properties" in rel_def:
                rel_props = rel_def["properties"]
                
                # Parse on_delete property
                if "on_delete" in rel_props:
                    on_delete_value = rel_props["on_delete"].lower()
                    if "restrict" in on_delete_value:
                        on_delete = "RESTRICT"
                    elif "cascade" in on_delete_value:
                        on_delete = "CASCADE"
                    elif "set null" in on_delete_value:
                        on_delete = "SET NULL"
                    elif "set default" in on_delete_value:
                        on_delete = "SET DEFAULT"
                
                # Parse on_update property
                if "on_update" in rel_props:
                    on_update_value = rel_props["on_update"].lower()
                    if "restrict" in on_update_value:
                        on_update = "RESTRICT"
                    elif "cascade" in on_update_value:
                        on_update = "CASCADE"
                    elif "set null" in on_update_value:
                        on_update = "SET NULL"
                    elif "set default" in on_update_value:
                        on_update = "SET DEFAULT"
                
                # Parse foreign_key_type property
                if "foreign_key_type" in rel_props:
                    fk_type = rel_props["foreign_key_type"].lower()
                    if "non-nullable" in fk_type:
                        # Add NOT NULL constraint to the column if it doesn't already have it
                        not_null_query = f"""
                            ALTER TABLE {schema_name}."{table_name}"
                            ALTER COLUMN {source_attr_sql} SET NOT NULL
                        """
                        execute_query(not_null_query)
            
            # Add foreign key constraint with ON DELETE and ON UPDATE actions
            query = f"""
                ALTER TABLE {schema_name}."{table_name}"
                ADD CONSTRAINT {constraint_name}
                FOREIGN KEY ({source_attr_sql})
                REFERENCES {schema_name}."{target_table_name}" ({target_attr_sql})
                ON DELETE {on_delete}
                ON UPDATE {on_update}
            """
            
            success, query_messages, _ = execute_query(query)
            
            if not success:
                warning_msg = f"Warning: Failed to add foreign key constraint for relationship '{rel_name}': {query_messages}"
                messages.append(warning_msg)
                logger.warning(warning_msg)
                continue
            
            messages.append(f"Added foreign key constraint for relationship '{rel_name}' from {table_name}.{source_attr_sql} to {target_table_name}.{target_attr_sql}")
            logger.info(f"Added foreign key constraint for relationship '{rel_name}' from {table_name}.{source_attr_sql} to {target_table_name}.{target_attr_sql}")
        
        return True, messages
    except Exception as e:
        error_msg = f"Error adding foreign key constraints to table '{table_name}': {str(e)}"
        logger.error(error_msg, exc_info=True)
        messages.append(error_msg)
        return False, messages

def deploy_calculated_fields(entity_id: str, entity_name: str, entity_def: Dict, schema_name: str) -> Tuple[bool, List[str]]:
    """
    Deploy calculated fields to the database.
    
    Args:
        entity_id: ID of the entity
        entity_name: Name of the entity
        entity_def: Entity definition
        schema_name: Schema name
        
    Returns:
        Tuple containing:
            - Boolean indicating if deployment was successful
            - List of messages (warnings, errors, or success messages)
    """
    messages = []
    
    try:
        logger.info(f"Deploying calculated fields for entity '{entity_name}'")
        
        # Check if calculated fields exist
        if 'calculated_fields' not in entity_def:
            logger.info(f"No calculated fields defined for entity '{entity_name}'")
            return True, messages
        
        # Create the entity_calculated_fields table if it doesn't exist
        success, query_messages, result = execute_query(
            f"""
            CREATE TABLE IF NOT EXISTS {schema_name}.entity_calculated_fields (
                field_id VARCHAR(255) PRIMARY KEY,
                entity_id VARCHAR(255) NOT NULL,
                attribute_name VARCHAR(255) NOT NULL,
                formula TEXT NOT NULL,
                logic_layer VARCHAR(50),
                caching VARCHAR(50),
                dependencies TEXT,
                created_at TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (entity_id) REFERENCES {schema_name}.entities(entity_id)
            )
            """,
            schema_name=schema_name
        )
        
        if not success:
            error_msg = f"Error creating entity_calculated_fields table: {query_messages}"
            logger.error(error_msg)
            messages.append(error_msg)
            return False, messages
        
        # Process each calculated field
        for field_id, field_def in entity_def['calculated_fields'].items():
            # Check if the field already exists
            success, query_messages, result = execute_query(
                f"SELECT field_id FROM {schema_name}.entity_calculated_fields WHERE field_id = %s",
                (field_id,),
                schema_name
            )
            
            if success and result:
                # Field already exists, update it
                logger.info(f"Calculated field '{field_id}' already exists, updating it")
                
                success, query_messages, result = execute_query(
                    f"""
                    UPDATE {schema_name}.entity_calculated_fields
                    SET attribute_name = %s,
                        formula = %s,
                        logic_layer = %s,
                        caching = %s,
                        dependencies = %s,
                        updated_at = CURRENT_TIMESTAMP
                    WHERE field_id = %s
                    """,
                    (
                        field_def.get('attribute', ''),
                        field_def.get('formula', ''),
                        field_def.get('logic_layer', ''),
                        field_def.get('caching', ''),
                        field_def.get('dependencies', ''),
                        field_id
                    ),
                    schema_name
                )
                
                if not success:
                    warning_msg = f"Warning: Failed to update calculated field '{field_id}': {query_messages}"
                    messages.append(warning_msg)
                    logger.warning(warning_msg)
                    continue
            else:
                # Field doesn't exist, create it
                logger.info(f"Creating new calculated field '{field_id}'")
                
                success, query_messages, result = execute_query(
                    f"""
                    INSERT INTO {schema_name}.entity_calculated_fields (
                        field_id, entity_id, attribute_name, formula, logic_layer, caching, dependencies,
                        created_at, updated_at
                    ) VALUES (%s, %s, %s, %s, %s, %s, %s, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
                    """,
                    (
                        field_id,
                        entity_id,
                        field_def.get('attribute', ''),
                        field_def.get('formula', ''),
                        field_def.get('logic_layer', ''),
                        field_def.get('caching', ''),
                        field_def.get('dependencies', '')
                    ),
                    schema_name
                )
                
                if not success:
                    warning_msg = f"Warning: Failed to create calculated field '{field_id}': {query_messages}"
                    messages.append(warning_msg)
                    logger.warning(warning_msg)
                    continue
            
            messages.append(f"Successfully deployed calculated field '{field_id}'")
            logger.info(f"Successfully deployed calculated field '{field_id}'")
        
        return True, messages
    except Exception as e:
        error_msg = f"Error deploying calculated fields for entity '{entity_name}': {str(e)}"
        logger.error(error_msg, exc_info=True)
        messages.append(error_msg)
        return False, messages

def deploy_business_rules(entity_id: str, entity_name: str, entity_def: Dict, schema_name: str) -> Tuple[bool, List[str]]:
    """
    Deploy business rules to the database.
    
    Args:
        entity_id: ID of the entity
        entity_name: Name of the entity
        entity_def: Entity definition
        schema_name: Schema name
        
    Returns:
        Tuple containing:
            - Boolean indicating if deployment was successful
            - List of messages (warnings, errors, or success messages)
    """
    messages = []
    
    try:
        logger.info(f"Deploying business rules for entity '{entity_name}'")
        
        # Check if business rules exist
        if 'business_rules' not in entity_def:
            logger.info(f"No business rules defined for entity '{entity_name}'")
            return True, messages
        
        # Create the entity_business_rules table if it doesn't exist
        success, query_messages, result = execute_query(
            f"""
            CREATE TABLE IF NOT EXISTS {schema_name}.entity_business_rules (
                rule_id VARCHAR(255) PRIMARY KEY,
                entity_id VARCHAR(255) NOT NULL,
                name VARCHAR(255) NOT NULL,
                description TEXT,
                created_at TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (entity_id) REFERENCES {schema_name}.entities(entity_id)
            )
            """,
            schema_name=schema_name
        )
        
        if not success:
            error_msg = f"Error creating entity_business_rules table: {query_messages}"
            logger.error(error_msg)
            messages.append(error_msg)
            return False, messages
        
        # Create the business_rule_conditions table if it doesn't exist
        success, query_messages, result = execute_query(
            f"""
            CREATE TABLE IF NOT EXISTS {schema_name}.business_rule_conditions (
                condition_id SERIAL PRIMARY KEY,
                rule_id VARCHAR(255) NOT NULL,
                condition_text TEXT NOT NULL,
                attribute_name VARCHAR(255),
                operator VARCHAR(255),
                value TEXT,
                created_at TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (rule_id) REFERENCES {schema_name}.entity_business_rules(rule_id)
            )
            """,
            schema_name=schema_name
        )
        
        if not success:
            error_msg = f"Error creating business_rule_conditions table: {query_messages}"
            logger.error(error_msg)
            messages.append(error_msg)
            return False, messages
        
        # Process each business rule
        for rule_id, rule_def in entity_def['business_rules'].items():
            # Check if the rule already exists
            success, query_messages, result = execute_query(
                f"SELECT rule_id FROM {schema_name}.entity_business_rules WHERE rule_id = %s",
                (rule_id,),
                schema_name
            )
            
            if success and result:
                # Rule already exists, update it
                logger.info(f"Business rule '{rule_id}' already exists, updating it")
                
                success, query_messages, result = execute_query(
                    f"""
                    UPDATE {schema_name}.entity_business_rules
                    SET updated_at = CURRENT_TIMESTAMP
                    WHERE rule_id = %s
                    """,
                    (rule_id,),
                    schema_name
                )
                
                if not success:
                    warning_msg = f"Warning: Failed to update business rule '{rule_id}': {query_messages}"
                    messages.append(warning_msg)
                    logger.warning(warning_msg)
                    continue
                
                # Delete existing conditions
                success, query_messages, result = execute_query(
                    f"DELETE FROM {schema_name}.business_rule_conditions WHERE rule_id = %s",
                    (rule_id,),
                    schema_name
                )
                
                if not success:
                    warning_msg = f"Warning: Failed to delete conditions for business rule '{rule_id}': {query_messages}"
                    messages.append(warning_msg)
                    logger.warning(warning_msg)
                    continue
            else:
                # Rule doesn't exist, create it
                logger.info(f"Creating new business rule '{rule_id}'")
                
                # Use the explicit ID if available, otherwise use the rule_id
                rule_name = rule_def.get('id', rule_id)
                
                success, query_messages, result = execute_query(
                    f"""
                    INSERT INTO {schema_name}.entity_business_rules (
                        rule_id, entity_id, name, description, created_at, updated_at
                    ) VALUES (%s, %s, %s, %s, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
                    """,
                    (rule_id, entity_id, rule_name, ""),
                    schema_name
                )
                
                if not success:
                    warning_msg = f"Warning: Failed to create business rule '{rule_id}': {query_messages}"
                    messages.append(warning_msg)
                    logger.warning(warning_msg)
                    continue
            
            # Add conditions
            if 'conditions' in rule_def:
                for condition in rule_def['conditions']:
                    # Parse the condition to extract attribute, operator, and value
                    attribute_name = None
                    operator = None
                    value = None
                    
                    # Try to parse conditions like "Employee.status must be Active to receive performance reviews"
                    if '.' in condition and ' must ' in condition:
                        parts = condition.split(' must ')
                        if len(parts) == 2:
                            entity_attr = parts[0].strip()
                            constraint = parts[1].strip()
                            
                            if '.' in entity_attr:
                                entity_name, attribute_name = entity_attr.split('.')
                                
                                # Extract operator and value
                                if ' be ' in constraint:
                                    operator = 'be'
                                    value = constraint.split(' be ')[1].strip()
                                    
                                    # If value contains "to", extract the part before "to"
                                    if ' to ' in value:
                                        value = value.split(' to ')[0].strip()
                                elif ' match ' in constraint:
                                    operator = 'match'
                                    value = constraint.split(' match ')[1].strip()
                                elif ' belong ' in constraint:
                                    operator = 'belong'
                                    value = constraint.split(' belong ')[1].strip()
                    
                    success, query_messages, result = execute_query(
                        f"""
                        INSERT INTO {schema_name}.business_rule_conditions (
                            rule_id, condition_text, attribute_name, operator, value,
                            created_at, updated_at
                        ) VALUES (%s, %s, %s, %s, %s, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
                        """,
                        (rule_id, condition, attribute_name, operator, value),
                        schema_name
                    )
                    
                    if not success:
                        warning_msg = f"Warning: Failed to create condition for business rule '{rule_id}': {query_messages}"
                        messages.append(warning_msg)
                        logger.warning(warning_msg)
                        continue
                    
                    logger.info(f"Added condition to business rule '{rule_id}': {condition}")
            
            messages.append(f"Successfully deployed business rule '{rule_id}'")
            logger.info(f"Successfully deployed business rule '{rule_id}'")
        
        return True, messages
    except Exception as e:
        error_msg = f"Error deploying business rules for entity '{entity_name}': {str(e)}"
        logger.error(error_msg, exc_info=True)
        messages.append(error_msg)
        return False, messages

def deploy_synthetic_data(entity_id: str, entity_name: str, entity_def: Dict, schema_name: str) -> Tuple[bool, List[str]]:
    """
    Deploy synthetic data to the database.
    
    Args:
        entity_id: ID of the entity
        entity_name: Name of the entity
        entity_def: Entity definition
        schema_name: Schema name
        
    Returns:
        Tuple containing:
            - Boolean indicating if deployment was successful
            - List of messages (warnings, errors, or success messages)
    """
    messages = []
    
    try:
        # Check if synthetic data exists
        if 'synthetic_data' not in entity_def or not entity_def['synthetic_data']:
            logger.info(f"No synthetic data defined for entity '{entity_name}'")
            return True, messages
        
        # Get entity table name
        entity_num = entity_id[1:]  # Remove 'E' prefix
        table_name = f"e{entity_num}_{entity_name.lower()}"
        
        # Check if table exists
        success, query_messages, result = execute_query(
            f"""
            SELECT EXISTS (
                SELECT FROM information_schema.tables 
                WHERE table_schema = %s
                AND table_name = %s
            )
            """,
            (schema_name, table_name)
        )
        
        if not success or not result or not result[0][0]:
            warning_msg = f"Warning: Table '{table_name}' for entity '{entity_name}' not found"
            messages.append(warning_msg)
            logger.warning(warning_msg)
            return False, messages
        
        # Process each synthetic data line
        for data_line in entity_def['synthetic_data']:
            # Parse the synthetic data line
            # Format: "Entity has attr1 = value1, attr2 = value2, ..."
            if not data_line.startswith(f"{entity_name} has "):
                warning_msg = f"Warning: Invalid synthetic data line format: {data_line}"
                messages.append(warning_msg)
                logger.warning(warning_msg)
                continue
            
            # Extract attribute-value pairs
            attr_values_text = data_line.split(f"{entity_name} has ")[1].strip()
            
            # Parse attribute-value pairs
            attr_values = {}
            in_quotes = False
            current_attr = ""
            current_value = ""
            parsing_attr = True
            
            for char in attr_values_text:
                if char == '"':
                    in_quotes = not in_quotes
                    if not in_quotes and not parsing_attr:
                        # End of quoted value
                        attr_values[current_attr.strip()] = current_value
                        current_attr = ""
                        current_value = ""
                        parsing_attr = True
                    else:
                        current_value += char
                elif char == '=' and parsing_attr and not in_quotes:
                    parsing_attr = False
                elif char == ',' and not in_quotes and not parsing_attr:
                    # End of value
                    if current_attr.strip():
                        attr_values[current_attr.strip()] = current_value.strip()
                    current_attr = ""
                    current_value = ""
                    parsing_attr = True
                elif parsing_attr:
                    current_attr += char
                else:
                    current_value += char
            
            # Add the last attribute-value pair if exists
            if current_attr.strip() and not parsing_attr:
                attr_values[current_attr.strip()] = current_value.strip()
            
            # Prepare column names and values for INSERT
            columns = []
            values = []
            placeholders = []
            
            for attr, value in attr_values.items():
                # Sanitize column name
                col_name = sanitize_column_name(attr)
                columns.append(f'"{col_name}"')
                
                # Handle quoted values
                if value.startswith('"') and value.endswith('"'):
                    value = value[1:-1]
                
                values.append(value)
                placeholders.append('%s')
            
            # Add created_at and updated_at
            columns.append('created_at')
            values.append('NOW()')
            placeholders[-1] = 'NOW()'  # Replace the last placeholder with NOW()
            
            columns.append('updated_at')
            values.append('NOW()')
            placeholders.append('NOW()')
            
            # Build INSERT query with proper placeholders
            # First, handle the NOW() function for created_at and updated_at
            values_without_now = []
            placeholders_final = []
            
            for i, value in enumerate(values):
                if value == 'NOW()':
                    placeholders_final.append('NOW()')
                else:
                    placeholders_final.append('%s')
                    values_without_now.append(value)
            
            query = f"""
                INSERT INTO {schema_name}."{table_name}" (
                    {', '.join(columns)}
                ) VALUES (
                    {', '.join(placeholders_final)}
                )
                ON CONFLICT DO NOTHING
            """
            
            success, query_messages, _ = execute_query(query, tuple(values_without_now), schema_name)
            
            if not success:
                warning_msg = f"Warning: Failed to insert synthetic data: {query_messages}"
                messages.append(warning_msg)
                logger.warning(warning_msg)
                continue
            
            logger.info(f"Inserted synthetic data into table '{table_name}'")
        
        return True, messages
    except Exception as e:
        error_msg = f"Error deploying synthetic data for entity '{entity_name}': {str(e)}"
        logger.error(error_msg, exc_info=True)
        messages.append(error_msg)
        return False, messages

def update_entity_ids(schema_name: str) -> Tuple[bool, List[str]]:
    """
    Update entity IDs in the entities table and all referencing tables from E000001 format to E1 format.
    
    Args:
        schema_name: Schema name
        
    Returns:
        Tuple containing:
            - Boolean indicating if deployment was successful
            - List of messages (warnings, errors, or success messages)
    """
    messages = []
    conn = None
    
    try:
        logger.info(f"Updating entity IDs in schema {schema_name}")
        
        # Get database connection
        conn = get_db_connection(schema_name)
        
        # Get all tables that reference entities.entity_id
        with conn.cursor() as cursor:
            cursor.execute(f"""
                SELECT tc.table_name, kcu.column_name, tc.constraint_name
                FROM information_schema.table_constraints tc
                JOIN information_schema.key_column_usage kcu
                ON tc.constraint_name = kcu.constraint_name
                AND tc.table_schema = kcu.table_schema
                JOIN information_schema.constraint_column_usage ccu
                ON ccu.constraint_name = tc.constraint_name
                AND ccu.table_schema = tc.table_schema
                WHERE tc.constraint_type = 'FOREIGN KEY'
                AND tc.table_schema = '{schema_name}'
                AND ccu.table_name = 'entities'
                AND ccu.column_name = 'entity_id'
                ORDER BY tc.table_name
            """)
            
            referencing_tables = cursor.fetchall()
            
            logger.info(f"Found {len(referencing_tables)} tables referencing entities.entity_id:")
            for table in referencing_tables:
                table_name, column_name, constraint_name = table
                logger.info(f"  - {table_name}.{column_name} (constraint: {constraint_name})")
            
            # Check for entity_attributes and entity_attribute_metadata tables
            cursor.execute(f"""
                SELECT table_name
                FROM information_schema.tables
                WHERE table_schema = '{schema_name}'
                AND table_name IN ('entity_attributes', 'entity_attribute_metadata', 'entity_relationships')
            """)
            
            attribute_tables = cursor.fetchall()
            attribute_tables_list = [table[0] for table in attribute_tables]
            
            logger.info(f"Found attribute tables: {', '.join(attribute_tables_list)}")
            
            # Temporarily disable foreign key constraints
            logger.info("Temporarily disabling foreign key constraints")
            cursor.execute("SET session_replication_role = 'replica';")
            
            # Get all entities
            cursor.execute(f"""
                SELECT entity_id, name
                FROM {schema_name}.entities
                ORDER BY entity_id
            """)
            
            entities = cursor.fetchall()
            
            if not entities:
                logger.info(f"No entities found in schema '{schema_name}'")
                # Re-enable foreign key constraints
                cursor.execute("SET session_replication_role = 'origin';")
                return True, messages
            
            logger.info(f"Found {len(entities)} entities in schema '{schema_name}'")
            
            # Update each entity ID in all tables
            for entity in entities:
                old_entity_id, name = entity
                
                # Extract numeric part and create new entity ID
                if old_entity_id.startswith('E'):
                    num = int(old_entity_id[1:])
                    new_entity_id = f"E{num}"
                    
                    # Skip if the entity ID is already in the new format
                    if old_entity_id == new_entity_id:
                        logger.info(f"Entity ID '{old_entity_id}' for '{name}' is already in the new format")
                        continue
                    
                    logger.info(f"Updating entity ID from '{old_entity_id}' to '{new_entity_id}' for '{name}'")
                    
                    # Update entity_attributes table if it exists
                    if 'entity_attributes' in attribute_tables_list:
                        try:
                            # Update entity_id column
                            cursor.execute(f"""
                                UPDATE {schema_name}.entity_attributes
                                SET entity_id = %s
                                WHERE entity_id = %s
                            """, (new_entity_id, old_entity_id))
                            
                            rows_updated = cursor.rowcount
                            logger.info(f"Updated {rows_updated} rows in entity_attributes.entity_id")
                            
                            # Update attribute_id column (which contains entity_id as prefix)
                            cursor.execute(f"""
                                UPDATE {schema_name}.entity_attributes
                                SET attribute_id = REPLACE(attribute_id, %s, %s)
                                WHERE attribute_id LIKE %s
                            """, (old_entity_id, new_entity_id, f"{old_entity_id}.%"))
                            
                            rows_updated = cursor.rowcount
                            logger.info(f"Updated {rows_updated} attribute IDs in entity_attributes.attribute_id")
                        except Exception as e:
                            error_msg = f"Error updating entity_attributes table: {str(e)}"
                            logger.error(error_msg)
                            messages.append(error_msg)
                    
                    # Update entity_attribute_metadata table if it exists
                    if 'entity_attribute_metadata' in attribute_tables_list:
                        try:
                            # Update entity_id column
                            cursor.execute(f"""
                                UPDATE {schema_name}.entity_attribute_metadata
                                SET entity_id = %s
                                WHERE entity_id = %s
                            """, (new_entity_id, old_entity_id))
                            
                            rows_updated = cursor.rowcount
                            logger.info(f"Updated {rows_updated} rows in entity_attribute_metadata.entity_id")
                            
                            # Update attribute_id column (which contains entity_id as prefix)
                            cursor.execute(f"""
                                UPDATE {schema_name}.entity_attribute_metadata
                                SET attribute_id = REPLACE(attribute_id, %s, %s)
                                WHERE attribute_id LIKE %s
                            """, (old_entity_id, new_entity_id, f"{old_entity_id}.%"))
                            
                            rows_updated = cursor.rowcount
                            logger.info(f"Updated {rows_updated} attribute IDs in entity_attribute_metadata.attribute_id")
                        except Exception as e:
                            error_msg = f"Error updating entity_attribute_metadata table: {str(e)}"
                            logger.error(error_msg)
                            messages.append(error_msg)
                    
                    # Update entity_relationships table if it exists
                    if 'entity_relationships' in attribute_tables_list:
                        try:
                            # Update source_entity_id column
                            cursor.execute(f"""
                                UPDATE {schema_name}.entity_relationships
                                SET source_entity_id = %s
                                WHERE source_entity_id = %s
                            """, (new_entity_id, old_entity_id))
                            
                            rows_updated = cursor.rowcount
                            logger.info(f"Updated {rows_updated} rows in entity_relationships.source_entity_id")
                            
                            # Update target_entity_id column
                            cursor.execute(f"""
                                UPDATE {schema_name}.entity_relationships
                                SET target_entity_id = %s
                                WHERE target_entity_id = %s
                            """, (new_entity_id, old_entity_id))
                            
                            rows_updated = cursor.rowcount
                            logger.info(f"Updated {rows_updated} rows in entity_relationships.target_entity_id")
                            
                            # Update source_attribute_id column (which may contain entity_id as prefix)
                            cursor.execute(f"""
                                UPDATE {schema_name}.entity_relationships
                                SET source_attribute_id = REPLACE(source_attribute_id, %s, %s)
                                WHERE source_attribute_id LIKE %s
                            """, (old_entity_id, new_entity_id, f"{old_entity_id}.%"))
                            
                            rows_updated = cursor.rowcount
                            logger.info(f"Updated {rows_updated} attribute IDs in entity_relationships.source_attribute_id")
                            
                            # Update target_attribute_id column (which may contain entity_id as prefix)
                            cursor.execute(f"""
                                UPDATE {schema_name}.entity_relationships
                                SET target_attribute_id = REPLACE(target_attribute_id, %s, %s)
                                WHERE target_attribute_id LIKE %s
                            """, (old_entity_id, new_entity_id, f"{old_entity_id}.%"))
                            
                            rows_updated = cursor.rowcount
                            logger.info(f"Updated {rows_updated} attribute IDs in entity_relationships.target_attribute_id")
                        except Exception as e:
                            error_msg = f"Error updating entity_relationships table: {str(e)}"
                            logger.error(error_msg)
                            messages.append(error_msg)
                    
                    # Update all referencing tables
                    for table in referencing_tables:
                        table_name, column_name, _ = table
                        
                        try:
                            cursor.execute(f"""
                                UPDATE {schema_name}."{table_name}"
                                SET {column_name} = %s
                                WHERE {column_name} = %s
                            """, (new_entity_id, old_entity_id))
                            
                            rows_updated = cursor.rowcount
                            logger.info(f"Updated {rows_updated} rows in {table_name}.{column_name}")
                        except Exception as e:
                            error_msg = f"Error updating {table_name}.{column_name}: {str(e)}"
                            logger.error(error_msg)
                            messages.append(error_msg)
                    
                    # Finally update the entities table
                    try:
                        cursor.execute(f"""
                            UPDATE {schema_name}.entities
                            SET entity_id = %s
                            WHERE entity_id = %s
                        """, (new_entity_id, old_entity_id))
                        
                        rows_updated = cursor.rowcount
                        if rows_updated > 0:
                            logger.info(f"Updated entity ID in entities table")
                            messages.append(f"Updated entity ID from '{old_entity_id}' to '{new_entity_id}' for '{name}'")
                        else:
                            logger.warning(f"No rows updated in entities table for entity ID '{old_entity_id}'")
                    except Exception as e:
                        error_msg = f"Error updating entity ID in entities table: {str(e)}"
                        logger.error(error_msg)
                        messages.append(error_msg)
                else:
                    logger.warning(f"Entity ID '{old_entity_id}' for '{name}' does not start with 'E', skipping")
            
            # Commit all changes
            conn.commit()
            
            # Re-enable foreign key constraints
            logger.info("Re-enabling foreign key constraints")
            cursor.execute("SET session_replication_role = 'origin';")
            conn.commit()
            
            # Verify the update
            cursor.execute(f"""
                SELECT entity_id, name
                FROM {schema_name}.entities
                ORDER BY entity_id
            """)
            
            updated_entities = cursor.fetchall()
            
            logger.info(f"Updated entities in schema '{schema_name}':")
            for entity in updated_entities:
                entity_id, name = entity
                logger.info(f"  - {entity_id}, {name}")
        
        return True, messages
    except Exception as e:
        error_msg = f"Error updating entity IDs: {str(e)}"
        logger.error(error_msg, exc_info=True)
        messages.append(error_msg)
        if conn:
            # Make sure to re-enable foreign key constraints even if there's an error
            try:
                with conn.cursor() as cursor:
                    cursor.execute("SET session_replication_role = 'origin';")
                conn.rollback()
            except:
                pass
        return False, messages
    finally:
        if conn:
            conn.close()

def update_entity_table_names(schema_name: str) -> Tuple[bool, List[str]]:
    """
    Update entity table names from old format (e000001_department, e000002_employee, etc.)
    to new format (e1_department, e2_employee, etc.).

    Args:
        schema_name: Schema name

    Returns:
        Tuple containing:
            - Boolean indicating if update was successful
            - List of messages (warnings, errors, or success messages)
    """
    messages = []
    conn = None

    try:
        logger.info(f'Updating entity table names in schema {schema_name}')

        # Get database connection
        conn = get_db_connection(schema_name)

        with conn.cursor() as cursor:
            # Find all tables in the schema that match the old format pattern
            cursor.execute(f'''
                SELECT table_name
                FROM information_schema.tables
                WHERE table_schema = %s
                AND table_name LIKE 'e_______%'
                AND table_type = 'BASE TABLE'
            ''', (schema_name,))

            tables = cursor.fetchall()
            logger.info(f'Found {len(tables)} tables matching the old format pattern')

            # For each table, rename it to the new format
            for table in tables:
                old_table_name = table[0]
                
                # Extract the entity ID and table name
                match = re.match(r'^e([0-9]{6})_(.*)$', old_table_name)
                if match:
                    entity_num = int(match.group(1))
                    table_suffix = match.group(2)
                    
                    # Create the new table name
                    new_table_name = f'e{entity_num}_{table_suffix}'
                    
                    logger.info(f'Renaming table {old_table_name} to {new_table_name}')
                    
                    # Rename the table
                    cursor.execute(f'ALTER TABLE {schema_name}.{old_table_name} RENAME TO {new_table_name}')
                    
                    messages.append(f'Renamed table {old_table_name} to {new_table_name}')
            
            # Commit the changes
            conn.commit()
            
            logger.info('Successfully updated entity table names')
            messages.append('Successfully updated entity table names')
            
            return True, messages
    except Exception as e:
        logger.error(f'Error updating entity table names: {str(e)}')
        messages.append(f'Error updating entity table names: {str(e)}')
        
        if conn:
            conn.rollback()
        
        return False, messages
    finally:
        if conn:
            conn.close()


def add_attribute_table_constraints(schema_name: str) -> Tuple[bool, List[str]]:
    """
    Add foreign key constraints to entity_attributes and entity_attribute_metadata tables
    to reference the entities table.
    
    Args:
        schema_name: Schema name
        
    Returns:
        Tuple containing:
            - Boolean indicating if adding constraints was successful
            - List of messages (warnings, errors, or success messages)
    """
    messages = []
    conn = None
    
    try:
        logger.info(f"Adding foreign key constraints to attribute tables in schema {schema_name}")
        
        # Get database connection
        conn = get_db_connection(schema_name)
        
        with conn.cursor() as cursor:
            # Check if entity_attributes and entity_attribute_metadata tables exist
            cursor.execute(f"""
                SELECT table_name
                FROM information_schema.tables
                WHERE table_schema = '{schema_name}'
                AND table_name IN ('entity_attributes', 'entity_attribute_metadata')
            """)
            
            attribute_tables = cursor.fetchall()
            attribute_tables_list = [table[0] for table in attribute_tables]
            
            logger.info(f"Found attribute tables: {', '.join(attribute_tables_list)}")
            
            # Add foreign key constraint to entity_attributes table if it exists
            if 'entity_attributes' in attribute_tables_list:
                # Check if the constraint already exists
                cursor.execute(f"""
                    SELECT constraint_name
                    FROM information_schema.table_constraints
                    WHERE table_schema = '{schema_name}'
                    AND table_name = 'entity_attributes'
                    AND constraint_type = 'FOREIGN KEY'
                    AND constraint_name = 'fk_entity_attributes_entity_id'
                """)
                
                constraint_exists = cursor.fetchone()
                
                if not constraint_exists:
                    logger.info("Adding foreign key constraint to entity_attributes table")
                    
                    try:
                        # Add foreign key constraint
                        cursor.execute(f"""
                            ALTER TABLE {schema_name}.entity_attributes
                            ADD CONSTRAINT fk_entity_attributes_entity_id
                            FOREIGN KEY (entity_id)
                            REFERENCES {schema_name}.entities (entity_id)
                            ON DELETE CASCADE
                            ON UPDATE CASCADE
                        """)
                        
                        messages.append("Added foreign key constraint to entity_attributes table")
                        logger.info("Added foreign key constraint to entity_attributes table")
                    except Exception as e:
                        error_msg = f"Error adding foreign key constraint to entity_attributes table: {str(e)}"
                        logger.error(error_msg)
                        messages.append(error_msg)
                else:
                    logger.info("Foreign key constraint already exists on entity_attributes table")
            
            # Add foreign key constraint to entity_attribute_metadata table if it exists
            if 'entity_attribute_metadata' in attribute_tables_list:
                # Check if the constraint already exists
                cursor.execute(f"""
                    SELECT constraint_name
                    FROM information_schema.table_constraints
                    WHERE table_schema = '{schema_name}'
                    AND table_name = 'entity_attribute_metadata'
                    AND constraint_type = 'FOREIGN KEY'
                    AND constraint_name = 'fk_entity_attribute_metadata_entity_id'
                """)
                
                constraint_exists = cursor.fetchone()
                
                if not constraint_exists:
                    logger.info("Adding foreign key constraint to entity_attribute_metadata table")
                    
                    try:
                        # Add foreign key constraint
                        cursor.execute(f"""
                            ALTER TABLE {schema_name}.entity_attribute_metadata
                            ADD CONSTRAINT fk_entity_attribute_metadata_entity_id
                            FOREIGN KEY (entity_id)
                            REFERENCES {schema_name}.entities (entity_id)
                            ON DELETE CASCADE
                            ON UPDATE CASCADE
                        """)
                        
                        messages.append("Added foreign key constraint to entity_attribute_metadata table")
                        logger.info("Added foreign key constraint to entity_attribute_metadata table")
                    except Exception as e:
                        error_msg = f"Error adding foreign key constraint to entity_attribute_metadata table: {str(e)}"
                        logger.error(error_msg)
                        messages.append(error_msg)
                else:
                    logger.info("Foreign key constraint already exists on entity_attribute_metadata table")
            
            # Commit all changes
            conn.commit()
        
        return True, messages
    except Exception as e:
        error_msg = f"Error adding attribute table constraints: {str(e)}"
        logger.error(error_msg, exc_info=True)
        messages.append(error_msg)
        if conn:
            conn.rollback()
        return False, messages
    finally:
        if conn:
            conn.close()

def deploy_entities(entities_data: Dict, schema_name: str) -> Tuple[bool, List[str]]:
    """
    Deploy entities to the database.
    
    Args:
        entities_data: Parsed YAML data for entities
        schema_name: Schema name to deploy to
        
    Returns:
        Tuple containing:
            - Boolean indicating if deployment was successful
            - List of messages (warnings, errors, or success messages)
    """
    messages = []
    logger.info(f"Deploying entities to {schema_name}")
    
    try:
        # Check if entities key exists
        if 'entities' not in entities_data:
            logger.error("Missing 'entities' key in entities definition")
            return False, ["Missing 'entities' key in entities definition"]
        
        # First, validate all relationships to ensure both source and target entities exist
        validation_success, validation_messages = validate_entity_relationships(entities_data, schema_name)
        messages.extend(validation_messages)
        
        if not validation_success:
            logger.error("Validation of entity relationships failed")
            return False, messages
        
        # Deploy each entity without foreign key constraints
        entity_ids = {}  # Store entity IDs for later use in applying constraints
        for entity_name, entity_def in entities_data['entities'].items():
            success, entity_messages, entity_id = deploy_single_entity(entity_name, entity_def, schema_name, apply_constraints=False)
            messages.extend(entity_messages)
            
            if not success:
                return False, messages
            
            entity_ids[entity_name] = entity_id
        
        # Now apply foreign key constraints after all entities are created
        logger.info("All entities created, now applying foreign key constraints")
        for entity_name, entity_def in entities_data['entities'].items():
            if 'relationships' in entity_def:
                entity_id = entity_ids[entity_name]
                entity_num = entity_id[1:]  # Remove 'E' prefix
                table_name = f"e{entity_num}_{entity_name.lower()}"
                
                success, fk_messages = add_foreign_key_constraints(entity_id, entity_name, entity_def, schema_name, table_name)
                messages.extend(fk_messages)
                
                if not success:
                    logger.warning(f"Failed to apply some foreign key constraints for entity '{entity_name}'")
                    # Continue with other entities instead of failing
        
        # Deploy business rules for each entity
        logger.info("All entities created, now deploying business rules")
        for entity_name, entity_def in entities_data['entities'].items():
            if 'business_rules' in entity_def:
                entity_id = entity_ids[entity_name]
                
                success, br_messages = deploy_business_rules(entity_id, entity_name, entity_def, schema_name)
                messages.extend(br_messages)
                
                if not success:
                    logger.warning(f"Failed to deploy some business rules for entity '{entity_name}'")
                    # Continue with other entities instead of failing
        
        # Deploy calculated fields for each entity
        logger.info("All entities created, now deploying calculated fields")
        for entity_name, entity_def in entities_data['entities'].items():
            if 'calculated_fields' in entity_def:
                entity_id = entity_ids[entity_name]
                
                success, cf_messages = deploy_calculated_fields(entity_id, entity_name, entity_def, schema_name)
                messages.extend(cf_messages)
                
                if not success:
                    logger.warning(f"Failed to deploy some calculated fields for entity '{entity_name}'")
                    # Continue with other entities instead of failing
        
        # Deploy synthetic data for each entity
        logger.info("All entities created, now deploying synthetic data")
        for entity_name, entity_def in entities_data['entities'].items():
            if 'synthetic_data' in entity_def:
                entity_id = entity_ids[entity_name]
                
                success, sd_messages = deploy_synthetic_data(entity_id, entity_name, entity_def, schema_name)
                messages.extend(sd_messages)
                
                if not success:
                    logger.warning(f"Failed to deploy synthetic data for entity '{entity_name}'")
                    # Continue with other entities instead of failing
        
        # Save to MongoDB for design-time storage if needed
        # This would go here if you're using MongoDB
        
        # Add foreign key constraints to entity_attributes and entity_attribute_metadata tables
        logger.info("Adding foreign key constraints to attribute tables")
        constraint_success, constraint_messages = add_attribute_table_constraints(schema_name)
        messages.extend(constraint_messages)
        
        if not constraint_success:
            logger.warning("Failed to add some foreign key constraints to attribute tables")
            # Continue anyway, don't fail the deployment
        
        logger.info("Entity deployment completed successfully")
        return True, messages
    except Exception as e:
        error_msg = f"Entity deployment error: {str(e)}"
        logger.error(error_msg, exc_info=True)
        messages.append(error_msg)
        return False, messages
