"""
Global Objectives v2 API Module

This module provides global objectives management with RBAC checks.
"""

from .routes import router
from .models import (
    GlobalObjectiveResponse,
    GlobalObjectiveListResponse,
    PathwayDefinitionModel,
    PathwayFrequencyModel,
    LocalObjectiveModel,
    ValidationRuleDetailModel,
    ErrorResponse,
    SuccessResponse
)
from .service import GlobalObjectivesService, RBACService

__all__ = [
    "router",
    "GlobalObjectiveResponse",
    "GlobalObjectiveListResponse",
    "PathwayDefinitionModel",
    "PathwayFrequencyModel",
    "LocalObjectiveModel",
    "ValidationRuleDetailModel",
    "ErrorResponse",
    "SuccessResponse",
    "GlobalObjectivesService",
    "RBACService"
]
