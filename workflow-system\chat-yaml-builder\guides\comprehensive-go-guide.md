# Comprehensive GO Template and Implementation Guide

## Critical Design Rules

### 1. LO Enumeration Requirements
- **EVERY LO must have an explicit ID** in the format "lo001", "lo002", etc.
- **EVERY LO referenced anywhere must be defined** in the Process Flow section
- **ALL routing references must use LO IDs**, not just names
- Maintain sequential numbering for all LOs
- Include ALL LOs, even system-triggered or rollback LOs

### 2. First LO Requirements
- The first LO in any workflow **MUST be human-triggered** unless explicitly marked as system-triggered
- System-triggered workflows are permitted ONLY when initiated by:
  - Scheduled events (e.g., monthly report generation)
  - External system events (e.g., API calls)
  - Data-change events (e.g., record insertion)
  - Threshold crossing (e.g., inventory minimum)
- **System-triggered workflows MUST have a Trigger Definition section**

### 3. Input Stack to LO Mapping Rules
- GO input stack contains aggregate or post-process data needed by the GO
- **EXPLICIT mapping required for EVERY use of input data in ANY LO**
- Each input stack element must be mapped to specific LOs where it's used
- Format: `[Entity].[attribute] maps to [lo_id] → [lo_internal_entity].[attribute]`
- **Same input can map to multiple LOs** - each mapping must be explicit

### 4. Output Stack from GO Rules
- GO output stack contains final outputs produced by the GO
- Outputs flow to INPUT stacks of OTHER GOs
- **EXPLICIT mapping required for EVERY output to target GO**
- Format: `[Entity].[attribute] maps to [go_id] "[GO Name]" → [target_entity].[attribute]`
- **Same output can map to multiple target GOs** - each mapping must be explicit

### 5. Data Flow Architecture
- GO Input Stack → (through Input Mapping) → LO execution → LO outputs
- LO outputs aggregate to → GO Output Stack → (through Output Mapping) → Other GO Input Stacks
- Input mappings define when data enters the process (GO→LO)
- Output mappings define where data exits the process (GO→GO)

### 6. Rollback Requirements
- Every state-changing LO must have a corresponding rollback LO
- Rollback LOs must be included in the Process Flow section
- Rollback pathways must be explicitly defined
- Format: `[forward_lo_id] ([LO_Name]) ↔ [rollback_lo_id] ([Rollback_LO_Name])`

### 7. Business Rules Mapping
- Each business rule must reference the specific LO ID that implements it
- Rules must be categorized as: Validation, Routing, Data, or SLA
- Each rule must specify whether it's Enforced, Implemented, Validated, or Applied
- Format: `[Rule description] - [Action] by [lo_id]`

### 8. Parallel Flow Requirements
- All LOs in parallel flows must be defined in the Process Flow section
- Parallel flows must specify starting point and join point
- Format shows which LOs execute in parallel but full definition is in Process Flow

### 9. Integration Mapping Rules
- All GO relationships must specify exact entities being exchanged
- External system integrations must specify direction (inbound/outbound/two-way)
- Each integration point must map to specific LOs

### 10. Process Mining Data Structure
- Event log must capture every LO execution with case_id, activity, timestamp, resource
- All exceptions must be categorized (input_timeout, validation_failure, system_error)
- Rollback events must be tracked separately with their own analytics

---

# Final GO Structure Template

## [ProcessName]

**Core Metadata:**
- id: "[go_id]"
- name: "[Verb Phrase describing the process]"
- version: "[version_number]"
- status: "[Active/Draft/Deprecated]"
- description: "[Brief description of the business process]"
- primary_entity: "[Main entity this process operates on]"
- classification: "[Business process classification]"

**Process Ownership:**
- *Originator:* [Role who can initiate this process OR "System" for automated initiation]
- *Process Owner:* [Role responsible for process]
- *Business Sponsor:* [Department/role providing business justification]

**Trigger Definition:** *(MANDATORY for system-initiated GOs)*
- *Trigger Type:* [scheduled/event-driven/data-change/threshold]
- *Trigger Condition:* [Specific condition that initiates the process]
- *Trigger Frequency:* [daily/hourly/on-demand/etc.]
- *Trigger Parameters:* [Any specific parameters for the trigger]

**Data Management:**

*Input Stack:*
- [Entity1] with attribute1*, attribute2, attribute3 (value1, value2, value3); [Entity2] with attribute1*, attribute2.

*Input Mapping Stack:* *(EXPLICIT mapping for EVERY usage)*
- [Entity1].[attribute1] maps to [lo_id] → [lo_internal_entity].[attribute]
- [Entity1].[attribute1] maps to [lo_id] → [lo_internal_entity].[attribute]  # Same input, different LO
- [Entity1].[attribute2] maps to [lo_id] → [lo_internal_entity].[attribute]
- [Entity2].[attribute1] maps to [lo_id] → [lo_internal_entity].[attribute]
- [Entity2].[attribute2] maps to [lo_id] → [lo_internal_entity].[attribute]

*Output Stack:*
- [Entity1] with attribute1*, attribute2, attribute3 (value1, value2, value3); [Entity2] with attribute1*, attribute2.

*Output Mapping Stack:* *(EXPLICIT mapping for EVERY target GO)*
- [Entity1].[attribute1] maps to [go_id] "[GO Name]" → [target_entity].[attribute]
- [Entity1].[attribute1] maps to [go_id] "[GO Name]" → [target_entity].[attribute]  # Same output, different GO
- [Entity1].[attribute2] maps to [go_id] "[GO Name]" → [target_entity].[attribute]
- [Entity2].[attribute1] maps to [go_id] "[GO Name]" → [target_entity].[attribute]

**Data Constraints:**

*DB Stack:*
- [Entity1].[attribute1] (datatype) is mandatory and unique. Error message: "[error_message]"
- [Entity1].[attribute2] (datatype) is optional. Error message: "[error_message]"
- [Entity2].[attribute1] (datatype) must be one of [allowed_values]. Error message: "[error_message]"

**Process Definition:**

*Process Flow:* *(ALL LOs must be defined here)*
1. [lo_id]: [LO_Name] [HUMAN/SYSTEM] - [Brief description]
   a. If [Entity].[attribute] [operator] [value], route to [lo_id]
   b. If [Entity].[attribute] [operator] [value], route to [lo_id]
2. [lo_id]: [LO_Name] [HUMAN/SYSTEM] - [Brief description]
   a. If [Entity].[attribute] [operator] [value], route to [lo_id]
3. [lo_id]: [LO_Name] [HUMAN/SYSTEM] - [Brief description]
   ...
   [Continue with ALL LOs sequentially numbered]

*Parallel Flows:*
- After [lo_id] ([LO_Name]):  # Starting point
  * [lo_id] ([LO_Name]) - [Brief description]  # These must be defined above
  * [lo_id] ([LO_Name]) - [Brief description]  # These must be defined above
- Join at: [lo_id] ([LO_Name])  # Join point (must be defined above)

*Rollback Pathways:*
- [lo_id] ([LO_Name]) ↔ [lo_id] ([Rollback_LO_Name])  # Both must be defined in Process Flow
- [lo_id] ([LO_Name]) ↔ [lo_id] ([Rollback_LO_Name])  # Both must be defined in Process Flow
- Full rollback pathway: [lo_id] → [lo_id] → ... → [lo_id]  # All LOs in pathway

**Business Rules:** *(Each rule mapped to specific LO)*
1. [Rule description] - Enforced by [lo_id]
2. [Rule description] - Implemented by [lo_id]
3. [Rule description] - Validated by [lo_id]
4. [Rule description] - Applied at [lo_id]
...

**Integration Points:**

*GO Relationships:*
- GO receives input from [go_id] "[GO Name]" for [Entity] processing.
- GO sends output to [go_id] "[GO Name]" for [Entity] processing.

*External Systems:*
- [System Name]: [Integration type] integration for [purpose]
- [API Name]: [Integration direction] for [data exchange]

**Performance Metadata:**
- cycle_time: "[average_time]"
- number_of_pathways: [count]
- volume_metrics: {
  average_volume: [number],
  peak_volume: [number],
  unit: "[per_month/day/hour]"
}

**Process Mining Schema:**

*Event Log Specification:*
- case_id: "[primary_entity].[unique_identifier]"
- activity: "[lo_id].[LO_name]"
- event_type: "[start/complete/abort/rollback]"
- timestamp: "[ISO-8601 datetime]"
- resource: "[role/system_executing]"
- duration: "[milliseconds]"
- attributes: {
  entity_state: "[json_snapshot]",
  input_values: "[input_parameters]",
  output_values: "[output_results]",
  execution_status: "[success/failure/pending]",
  error_details: "[error_message_if_any]"
}

*Performance Discovery Metrics:*
- pathway_frequency: {
  "[pathway_description]": {
    frequency: [count],
    percentage: [percentage],
    average_duration: "[time]",
    success_rate: [percentage]
  }
}
- bottleneck_analysis: {
  "[lo_id]": {
    average_wait_time: "[duration]",
    queue_length: [count],
    resource_utilization: [percentage],
    failure_rate: [percentage]
  }
}
- resource_patterns: {
  "[role/system]": {
    active_hours: "[time_ranges]",
    peak_load_periods: "[time_ranges]",
    concurrent_executions: [max_count]
  }
}

*Conformance Analytics:*
- compliance_rate: [percentage]
- execution_variance: {
  "[lo_id]": {
    expected_duration: "[time]",
    actual_duration_range: "[min-max]",
    variance_causes: ["list_of_identified_causes"]
  }
}
- exception_patterns: {
  "input_timeout": {
    frequency: [count],
    affected_pathways: ["list_of_pathways"],
    recovery_success_rate: [percentage]
  },
  "validation_failure": {
    frequency: [count],
    most_common_failures: ["list_of_validation_types"],
    resolution_time: "[average_duration]"
  },
  "system_error": {
    frequency: [count],
    error_categories: ["list_of_error_types"],
    automatic_recovery_rate: [percentage]
  }
}

*Advanced Process Intelligence:*
- process_health_score: {
  performance_score: [0-100],
  compliance_score: [0-100],
  efficiency_score: [0-100],
  overall_health: [0-100]
}
- prediction_models: {
  completion_time_forecast: {
    algorithm: "[ML_algorithm_used]",
    accuracy: [percentage],
    confidence_interval: [range]
  },
  failure_prediction: {
    algorithm: "[ML_algorithm_used]",
    precision: [percentage],
    recall: [percentage]
  }
}
- optimization_insights: {
  bottleneck_elimination: ["list_of_recommendations"],
  resource_reallocation: ["list_of_suggestions"],
  pathway_optimization: ["list_of_improvements"]
}

*Rollback Analytics:*
- rollback_frequency: [percentage]
- rollback_success_rate: [percentage]
- rollback_triggers: {
  "[trigger_type]": {
    frequency: [count],
    average_impact_scope: [count],
    average_recovery_time: "[duration]"
  }
}
- rollback_pathways: {
  "[rollback_pathway]": {
    frequency: [count],
    success_rate: [percentage],
    average_completion_time: "[duration]"
  }
}

**Sample Data:**
- [Entity1].[attribute1]: "[sample_value1]", "[sample_value2]", "[sample_value3]"
- [Entity1].[attribute2]: [number_value1], [number_value2], [number_value3]
- [Entity2].[attribute1]: "[sample_value1]", "[sample_value2]", "[sample_value3]"

---

# Validation Checklist for GO Creation

## Essential Checks
- [ ] **All LOs have unique IDs** (lo001, lo002, etc.)
- [ ] **Every referenced LO is defined** in Process Flow section
- [ ] **All routing uses LO IDs**, not names
- [ ] **First LO is human-initiated** OR Trigger Definition exists
- [ ] **All input mappings are explicit** (Entity.attribute → lo_id)
- [ ] **All output mappings are explicit** (Entity.attribute → go_id)
- [ ] **Business rules reference specific LO IDs**
- [ ] **Rollback LOs are included** in Process Flow
- [ ] **Parallel flows reference defined LOs**
- [ ] **All pathways terminate** in completion or cancellation

## Data Flow Validation
- [ ] Input Stack contains only aggregate/post-process data
- [ ] Input Mapping shows where data enters LOs
- [ ] Output Stack contains only final GO outputs
- [ ] Output Mapping shows where data goes to other GOs
- [ ] No function calls in Input/Output stacks
- [ ] DB Stack has proper constraints and error messages

## Integration Validation
- [ ] GO relationships specify exact entities
- [ ] External systems specify integration direction
- [ ] All integration points map to specific LOs
- [ ] Process mining schema captures all events
- [ ] Rollback analytics are included

## Performance Validation
- [ ] Cycle time is specified
- [ ] Number of pathways is counted correctly
- [ ] Volume metrics are realistic
- [ ] All critical LOs have performance targets
- [ ] SLA thresholds are defined

---

# Example: Complete Purchase Requisition Process

## Purchase Requisition Process

**Core Metadata:**
- id: "go001"
- name: "Process Purchase Requisitions"
- version: "1.0"
- status: "Active"
- description: "Manages purchase requisitions from creation to purchase order generation"
- primary_entity: "Requisition"
- classification: "Procurement Process"

**Process Ownership:**
- *Originator:* Employee with Purchasing Authority
- *Process Owner:* Procurement Manager
- *Business Sponsor:* Finance Department

**Data Management:**

*Input Stack:*
- VendorCatalog with preferredVendors*, contractPricing, deliveryTimes; BudgetSystem with fiscalYearBudget*, departmentAllocations, spendingThresholds (standard, expedited, emergency).

*Input Mapping Stack:*
- VendorCatalog.preferredVendors maps to lo001 → Requisition.allowedVendors
- VendorCatalog.preferredVendors maps to lo005 → PurchaseOrder.vendorOptions
- VendorCatalog.contractPricing maps to lo001 → Requisition.estimatedPricing
- VendorCatalog.contractPricing maps to lo005 → PurchaseOrder.finalPricing
- BudgetSystem.departmentAllocations maps to lo001 → Requisition.availableBudget
- BudgetSystem.spendingThresholds maps to lo001 → Requisition.approvalThresholds
- BudgetSystem.fiscalYearBudget maps to lo008 → BudgetAllocation.yearlyBudget

*Output Stack:*
- Requisition with status* (Approved, Rejected, Cancelled), approvedBy*, approvalDate; PurchaseOrder with orderNumber*, vendorId*, totalAmount*, deliveryDate.

*Output Mapping Stack:*
- PurchaseOrder.orderNumber maps to go002 "Process Accounts Payable" → ApInvoice.poReference
- PurchaseOrder.vendorId maps to go002 "Process Accounts Payable" → ApInvoice.vendorId
- PurchaseOrder.totalAmount maps to go002 "Process Accounts Payable" → ApInvoice.totalAmount
- PurchaseOrder.totalAmount maps to go008 "Budget Tracking" → BudgetTransaction.amount
- PurchaseOrder.deliveryDate maps to go004 "Track Inventory" → Shipment.expectedArrival
- Requisition.approvedBy maps to go006 "Performance Analytics" → ApprovalMetrics.approver
- Requisition.status maps to go006 "Performance Analytics" → RequisitionMetrics.outcomeStatus

**Data Constraints:**

*DB Stack:*
- Requisition.id (string) is mandatory and unique. Error message: "Requisition ID is required and must be unique"
- Requisition.requestor (string) is mandatory and must exist in Employee table. Error message: "Requestor must be a valid employee"
- Requisition.estimatedTotal (decimal) is mandatory and must be greater than zero. Error message: "Estimated total must be greater than zero"
- Requisition.status (enum) must be one of "Draft", "Submitted", "Approved", "Rejected". Error message: "Invalid status value"
- PurchaseOrder.orderNumber (string) is mandatory and unique. Error message: "Order number is required and must be unique"

**Process Definition:**

*Process Flow:*
1. lo001: CreateRequisition [HUMAN] - Employee creates requisition with budget verification
   a. If Requisition.estimatedTotal > $10000, route to lo003
   b. If Requisition.estimatedTotal ≤ $10000, route to lo002
2. lo002: ReviewRequisition [HUMAN] - Manager reviews requisition
   a. If Requisition.status = "Approved", route to lo005
   b. If Requisition.status = "Rejected", route to lo006
3. lo003: ReviewRequisition [HUMAN] - Director reviews high-value requisition
   a. If Requisition.status = "Approved", route to lo005
   b. If Requisition.status = "Rejected", route to lo006
4. lo005: CreatePurchaseOrder [HUMAN] - Procurement officer creates purchase order
5. lo006: ProcessRejection [SYSTEM] - System logs rejection and notifies requestor
6. lo007: CheckInventory [SYSTEM] - System verifies current inventory levels
7. lo008: AllocateBudget [SYSTEM] - System allocates budget and updates records
8. lo009: CompletePurchaseOrder [HUMAN] - Procurement officer finalizes purchase order
9. lo010: NotifyOutcome [SYSTEM] - System notifies all stakeholders of decision
10. lo011: RollbackRequisition [HUMAN] - Cancel requisition and restore state
11. lo012: RollbackPurchaseOrder [HUMAN] - Cancel purchase order and notify vendor
12. lo013: RollbackBudgetAllocation [SYSTEM] - Release allocated budget
13. lo014: RollbackNotification [SYSTEM] - Notify all parties of rollback completion

*Parallel Flows:*
- After lo005 (CreatePurchaseOrder):
  * lo007 (CheckInventory) - System verifies inventory availability
  * lo008 (AllocateBudget) - System allocates and locks budget
- Join at: lo009 (CompletePurchaseOrder)

*Rollback Pathways:*
- lo001 (CreateRequisition) ↔ lo011 (RollbackRequisition)
- lo005 (CreatePurchaseOrder) ↔ lo012 (RollbackPurchaseOrder)
- lo008 (AllocateBudget) ↔ lo013 (RollbackBudgetAllocation)
- Full rollback pathway: lo011 → lo012 → lo013 → lo014

**Business Rules:**
1. Requisitions exceeding $10000 require Director approval - Enforced by lo001
2. All rejections require a rejection reason - Implemented by lo002, lo003, and lo006
3. Budget must be verified before purchase order creation - Validated by lo008
4. Purchase orders must be completed within 3 business days - Monitored at lo009
5. Only preferred vendors can be selected - Enforced by lo001 and lo005
6. Department budget allocation cannot be exceeded - Validated by lo001
7. Rollback requires same approval level as original action - Enforced by lo011, lo012

**Integration Points:**

*GO Relationships:*
- GO receives input from go003 "Manage Vendor Catalog" for VendorCatalog processing.
- GO receives input from go008 "Manage Budget System" for BudgetSystem processing.
- GO sends output to go002 "Process Accounts Payable" for PurchaseOrder processing.
- GO sends output to go004 "Track Inventory" for expected delivery tracking.

*External Systems:*
- ERP System: Two-way integration for budget verification and purchase order creation
- Vendor Portal: Outbound integration for purchase order delivery
- Inventory Management System: Read-only access for stock verification

**Performance Metadata:**
- cycle_time: "3.5 business days"
- number_of_pathways: 5
- volume_metrics: {
  average_volume: 450,
  peak_volume: 800,
  unit: "requisitions/month"
}

[Process Mining Schema sections follow as defined in template above]

**Sample Data:**
- Requisition.id: "REQ-2025-0001", "REQ-2025-0002", "REQ-2025-0003"
- Requisition.estimatedTotal: 1500.00, 8000.00, 12000.00
- Requisition.status: "Draft", "Submitted", "Approved", "Rejected"
- PurchaseOrder.orderNumber: "PO-2025-0001", "PO-2025-0002", "PO-2025-0003"
- PurchaseOrder.totalAmount: 1450.00, 7950.00, 11950.00

---

This comprehensive guide now captures all the critical rules and requirements for creating properly structured GOs that can be used for training datasets and LLM generation.