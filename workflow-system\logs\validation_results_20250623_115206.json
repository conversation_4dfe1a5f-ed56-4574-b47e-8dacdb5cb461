{"success": true, "component_type": "constants", "target_schema": "workflow_temp", "validation_timestamp": "2025-06-23T11:52:06.498524", "errors": [], "warnings": [{"rule_id": "RULE_02", "message": "Invalid status transition from active to deployed_to_temp", "severity": "warning", "entity_id": null, "attribute_id": null, "timestamp": "2025-06-23T11:52:06.488313"}], "error_count": 0, "warning_count": 1}