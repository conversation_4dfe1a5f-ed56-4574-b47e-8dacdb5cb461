-- Migration script to update the workflow_runtime schema
-- Generated by generate_migration_script.py
-- Source schema: workflow_temp
-- Target schema: workflow_runtime


-- Entity-related changes
ALTER TABLE workflow_runtime.entities ADD COLUMN IF NOT EXISTS metadata jsonb NULL;
ALTER TABLE workflow_runtime.entities ADD COLUMN IF NOT EXISTS lifecycle_management jsonb NULL;
ALTER TABLE workflow_runtime.entities ADD COLUMN IF NOT EXISTS version_type VARCHAR(10) NULL DEFAULT 'v2'::character varying;
-- Add missing columns needed by entity_deployer.py
ALTER TABLE workflow_runtime.entities ADD COLUMN IF NOT EXISTS status VARCHAR(20) NULL DEFAULT 'active'::character varying;
ALTER TABLE workflow_runtime.entities ADD COLUMN IF NOT EXISTS type VARCHAR(50) NULL DEFAULT 'standard'::character varying;
ALTER TABLE workflow_runtime.entities ADD COLUMN IF NOT EXISTS attribute_prefix VARCHAR(50) NULL DEFAULT ''::character varying;

ALTER TABLE workflow_runtime.entity_attributes ALTER COLUMN attribute_id TYPE VARCHAR(100);
ALTER TABLE workflow_runtime.entity_attributes ALTER COLUMN entity_id DROP NOT NULL;
ALTER TABLE workflow_runtime.entity_attributes ADD COLUMN IF NOT EXISTS type VARCHAR(50) NOT NULL;
ALTER TABLE workflow_runtime.entity_attributes ADD COLUMN IF NOT EXISTS default_value text NULL;
ALTER TABLE workflow_runtime.entity_attributes ADD COLUMN IF NOT EXISTS calculated_field boolean NULL DEFAULT false;
ALTER TABLE workflow_runtime.entity_attributes ADD COLUMN IF NOT EXISTS calculation_formula text NULL;
ALTER TABLE workflow_runtime.entity_attributes ADD COLUMN IF NOT EXISTS dependencies jsonb NULL;
-- Add missing columns needed by entity_deployer.py
ALTER TABLE workflow_runtime.entity_attributes ADD COLUMN IF NOT EXISTS display_name VARCHAR(100) NULL;
ALTER TABLE workflow_runtime.entity_attributes ADD COLUMN IF NOT EXISTS datatype VARCHAR(50) NULL DEFAULT 'string'::character varying;
ALTER TABLE workflow_runtime.entity_attributes ADD COLUMN IF NOT EXISTS status VARCHAR(20) NULL DEFAULT 'active'::character varying;

CREATE TABLE IF NOT EXISTS workflow_runtime.entity_business_rules (
    rule_id VARCHAR(100) NOT NULL,
    entity_id VARCHAR(50) NULL,
    name VARCHAR(100) NOT NULL,
    description text NULL,
    condition text NULL,
    action text NULL,
    priority integer NULL DEFAULT 0,
    active boolean NULL DEFAULT true,
    PRIMARY KEY (rule_id)
);

-- Global Objective-related changes
ALTER TABLE workflow_runtime.global_objectives ADD COLUMN IF NOT EXISTS process_mining_schema jsonb NULL;
ALTER TABLE workflow_runtime.global_objectives ADD COLUMN IF NOT EXISTS performance_metadata jsonb NULL;
ALTER TABLE workflow_runtime.global_objectives ADD COLUMN IF NOT EXISTS version_type VARCHAR(10) NULL DEFAULT 'v2'::character varying;
ALTER TABLE workflow_runtime.global_objectives ADD COLUMN IF NOT EXISTS status VARCHAR(20) NULL DEFAULT 'active'::character varying;
ALTER TABLE workflow_runtime.global_objectives ADD COLUMN IF NOT EXISTS tenant_id VARCHAR(50) NULL DEFAULT 't001'::character varying;
ALTER TABLE workflow_runtime.global_objectives ADD COLUMN IF NOT EXISTS deleted_mark BOOLEAN NULL DEFAULT false;

CREATE TABLE IF NOT EXISTS workflow_runtime.go_lo_mapping (
    mapping_id VARCHAR(100) NOT NULL,
    go_id VARCHAR(50) NULL,
    lo_id VARCHAR(50) NOT NULL,
    sequence_number integer NULL DEFAULT 0,
    PRIMARY KEY (mapping_id)
);

CREATE TABLE IF NOT EXISTS workflow_runtime.go_performance_metrics (
    metric_id VARCHAR(100) NOT NULL,
    go_id VARCHAR(50) NOT NULL,
    name VARCHAR(100) NOT NULL,
    description text NULL,
    value text NULL,
    unit VARCHAR(50) NULL,
    PRIMARY KEY (metric_id)
);

-- Local Objective-related changes
ALTER TABLE workflow_runtime.lo_input_items ADD COLUMN IF NOT EXISTS item_id VARCHAR(100) NOT NULL;
ALTER TABLE workflow_runtime.lo_input_items ADD COLUMN IF NOT EXISTS name VARCHAR(100) NOT NULL;
ALTER TABLE workflow_runtime.lo_input_items ADD COLUMN IF NOT EXISTS type VARCHAR(50) NOT NULL;
ALTER TABLE workflow_runtime.lo_input_items ADD COLUMN IF NOT EXISTS default_value text NULL;
ALTER TABLE workflow_runtime.lo_input_items ADD COLUMN IF NOT EXISTS help_text text NULL;
ALTER TABLE workflow_runtime.lo_input_items ADD COLUMN IF NOT EXISTS dependency_info jsonb NULL;
ALTER TABLE workflow_runtime.lo_input_items ADD COLUMN IF NOT EXISTS ui_control VARCHAR(50) NULL;

ALTER TABLE workflow_runtime.lo_output_items ADD COLUMN IF NOT EXISTS item_id VARCHAR(100) NOT NULL;
ALTER TABLE workflow_runtime.lo_output_items ALTER COLUMN lo_id TYPE VARCHAR(50);
ALTER TABLE workflow_runtime.lo_output_items ADD COLUMN IF NOT EXISTS name VARCHAR(100) NOT NULL;
ALTER TABLE workflow_runtime.lo_output_items ADD COLUMN IF NOT EXISTS type VARCHAR(50) NOT NULL;

ALTER TABLE workflow_runtime.local_objectives ALTER COLUMN go_id DROP NOT NULL;
ALTER TABLE workflow_runtime.local_objectives ADD COLUMN IF NOT EXISTS description text NULL;
ALTER TABLE workflow_runtime.local_objectives ADD COLUMN IF NOT EXISTS ui_stack jsonb NULL;
ALTER TABLE workflow_runtime.local_objectives ADD COLUMN IF NOT EXISTS mapping_stack jsonb NULL;
ALTER TABLE workflow_runtime.local_objectives ADD COLUMN IF NOT EXISTS version_type VARCHAR(10) NULL DEFAULT 'v2'::character varying;

-- Role-related changes
CREATE TABLE IF NOT EXISTS workflow_runtime.role_inheritance (
    role_id VARCHAR(50) NOT NULL,
    parent_role_id VARCHAR(50) NOT NULL,
    PRIMARY KEY (role_id, parent_role_id)
);

ALTER TABLE workflow_runtime.role_permissions ADD COLUMN IF NOT EXISTS permission_name VARCHAR(100) NOT NULL;
ALTER TABLE workflow_runtime.roles ALTER COLUMN version_type TYPE VARCHAR(10);
ALTER TABLE workflow_runtime.roles ALTER COLUMN version_type SET DEFAULT 'v2'::character varying;
ALTER TABLE workflow_runtime.roles ALTER COLUMN tenant_id SET DEFAULT 't001'::character varying;

-- Execution pathway-related changes
CREATE TABLE IF NOT EXISTS workflow_runtime.execution_pathways (
    pathway_id VARCHAR(100) NOT NULL,
    go_id VARCHAR(50) NOT NULL,
    source_lo_id VARCHAR(50) NOT NULL,
    target_lo_id VARCHAR(50) NOT NULL,
    pathway_type VARCHAR(50) NOT NULL,
    description text NULL,
    PRIMARY KEY (pathway_id)
);

CREATE TABLE IF NOT EXISTS workflow_runtime.execution_pathway_conditions (
    condition_id VARCHAR(100) NOT NULL,
    pathway_id VARCHAR(100) NOT NULL,
    condition_type VARCHAR(50) NOT NULL,
    condition_value text NOT NULL,
    description text NULL,
    PRIMARY KEY (condition_id)
);

-- Agent-related changes
CREATE TABLE IF NOT EXISTS workflow_runtime.agent_stack (
    agent_id VARCHAR(100) NOT NULL,
    go_id VARCHAR(50) NOT NULL,
    name VARCHAR(100) NOT NULL,
    description text NULL,
    agent_type VARCHAR(50) NOT NULL,
    configuration jsonb NULL,
    PRIMARY KEY (agent_id)
);

CREATE TABLE IF NOT EXISTS workflow_runtime.agent_rights (
    right_id VARCHAR(100) NOT NULL,
    agent_id VARCHAR(100) NOT NULL,
    entity_id VARCHAR(50) NULL,
    permission VARCHAR(50) NOT NULL,
    scope VARCHAR(50) NOT NULL,
    description text NULL,
    PRIMARY KEY (right_id)
);

-- Nested functions-related changes
CREATE TABLE IF NOT EXISTS workflow_runtime.lo_nested_functions (
    function_id VARCHAR(100) NOT NULL,
    lo_id VARCHAR(50) NOT NULL,
    function_name VARCHAR(100) NOT NULL,
    function_type VARCHAR(50) NOT NULL,
    parameters jsonb NULL,
    output_to VARCHAR(100) NULL,
    description text NULL,
    PRIMARY KEY (function_id)
);

-- Data mapping-related changes
CREATE TABLE IF NOT EXISTS workflow_runtime.lo_data_mapping_stack (
    stack_id VARCHAR(100) NOT NULL,
    lo_id VARCHAR(50) NOT NULL,
    name VARCHAR(100) NOT NULL,
    description text NULL,
    PRIMARY KEY (stack_id)
);

CREATE TABLE IF NOT EXISTS workflow_runtime.lo_data_mappings (
    mapping_id VARCHAR(100) NOT NULL,
    stack_id VARCHAR(100) NOT NULL,
    source_entity VARCHAR(50) NOT NULL,
    source_attribute VARCHAR(50) NOT NULL,
    target_entity VARCHAR(50) NOT NULL,
    target_attribute VARCHAR(50) NOT NULL,
    mapping_type VARCHAR(50) NOT NULL,
    transformation text NULL,
    PRIMARY KEY (mapping_id)
);

-- Foreign key constraints
-- These will be added manually after all tables are created
