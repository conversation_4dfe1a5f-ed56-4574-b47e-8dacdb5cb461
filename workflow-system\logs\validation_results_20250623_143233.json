{"success": false, "component_type": "entity_relationships", "target_schema": "workflow_temp", "validation_timestamp": "2025-06-23T14:32:33.195981", "errors": [{"rule_id": "RULE_08", "message": "Source entity 'E7' does not exist in workflow_temp", "severity": "error", "entity_id": "E7", "attribute_id": null, "timestamp": "2025-06-23T14:32:33.163005"}, {"rule_id": "RULE_08", "message": "Target entity 'E15' does not exist in workflow_temp", "severity": "error", "entity_id": "E15", "attribute_id": null, "timestamp": "2025-06-23T14:32:33.170941"}, {"rule_id": "RULE_01", "message": "Record with relationship_id='1' already exists in workflow_temp.entity_relationships", "severity": "error", "entity_id": null, "attribute_id": null, "timestamp": "2025-06-23T14:32:33.184975"}], "warnings": [{"rule_id": "RULE_03", "message": "Potentially incompatible fields: ['version']", "severity": "warning", "entity_id": null, "attribute_id": null, "timestamp": "2025-06-23T14:32:33.195963"}], "error_count": 3, "warning_count": 1}