#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to clean up the database before redeploying YAML files.
Note: This script will truncate all tables in the workflow_runtime schema.
      A backup of the leave_sub_type table is available in leave_subtype_insert.sql
"""

import psycopg2
import os
import sys
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Database connection parameters from environment variables or defaults
DB_CONFIG = {
    "dbname": os.getenv("DB_NAME", "workflow_system"),
    "user": os.getenv("DB_USER", "postgres"),
    "password": os.getenv("DB_PASSWORD", "workflow_postgres_secure_password"),
    "host": os.getenv("DB_HOST", "**********"),
    "port": os.getenv("DB_PORT", "5432")
}

def connect_to_db():
    """Connect to the PostgreSQL database."""
    try:
        conn = psycopg2.connect(**DB_CONFIG)
        print("✅ Connected to PostgreSQL database")
        return conn
    except Exception as e:
        print(f"❌ Error connecting to PostgreSQL database: {e}")
        return None

def cleanup_mongodb():
    """Clean up MongoDB collections."""
    try:
        from pymongo import MongoClient
        
        # MongoDB connection parameters from environment variables or defaults
        mongo_host = os.getenv("MONGO_HOST", "localhost")
        mongo_port = os.getenv("MONGO_PORT", "27017")
        mongo_db = os.getenv("MONGO_DB", "workflow_system")
        
        # Connect to MongoDB
        mongo_uri = f"mongodb://{mongo_host}:{mongo_port}/"
        client = MongoClient(mongo_uri)
        db = client[mongo_db]
        
        # Collections to clean up
        collections_to_clean = ["workflow"]
        
        # Add more collections here if needed
        # collections_to_clean.extend(["collection1", "collection2"])
        
        total_deleted = 0
        for collection_name in collections_to_clean:
            try:
                collection = db[collection_name]
                result = collection.delete_many({})
                total_deleted += result.deleted_count
                print(f"✅ Deleted {result.deleted_count} documents from {collection_name} collection")
            except Exception as e:
                print(f"❌ Error cleaning up collection {collection_name}: {e}")
        
        client.close()
        print(f"✅ MongoDB cleanup completed. Total documents deleted: {total_deleted}")
        return True
    except Exception as e:
        print(f"❌ Error cleaning up MongoDB: {e}")
        return False

def cleanup_postgres_locks(conn):
    """Clean up any locks in the PostgreSQL database."""
    try:
        cursor = conn.cursor()
        
        # Get database name from connection
        cursor.execute("SELECT current_database()")
        db_name = cursor.fetchone()[0]
        
        print(f"Checking for locks in database: {db_name}")
        
        # Get active locks
        cursor.execute("""
            SELECT pid, 
                   usename, 
                   application_name,
                   client_addr,
                   state,
                   query_start,
                   wait_event_type,
                   wait_event,
                   query
            FROM pg_stat_activity
            WHERE datname = current_database()
            AND pid <> pg_backend_pid()
            AND state = 'active'
        """)
        
        active_connections = cursor.fetchall()
        
        if not active_connections:
            print("✅ No active connections found that might be causing locks")
            return True
        
        print(f"Found {len(active_connections)} active connections that might be causing locks")
        
        # Terminate connections
        for conn_info in active_connections:
            pid = conn_info[0]
            username = conn_info[1]
            app_name = conn_info[2]
            query = conn_info[8]
            
            print(f"Terminating connection: PID={pid}, User={username}, App={app_name}")
            print(f"Query: {query[:100]}..." if len(query) > 100 else f"Query: {query}")
            
            try:
                # First try to cancel the query
                cursor.execute(f"SELECT pg_cancel_backend({pid})")
                
                # Then terminate the connection
                cursor.execute(f"SELECT pg_terminate_backend({pid})")
                conn.commit()
                print(f"✅ Successfully terminated connection with PID {pid}")
            except Exception as e:
                conn.rollback()
                print(f"❌ Error terminating connection with PID {pid}: {e}")
        
        print("✅ Finished cleaning up database locks")
        return True
    except Exception as e:
        if conn:
            conn.rollback()
        print(f"❌ Error cleaning up database locks: {e}")
        return False

def cleanup_postgres(conn):
    """Clean up PostgreSQL tables."""
    try:
        # First clean up any locks
        cleanup_postgres_locks(conn)
        
        cursor = conn.cursor()
        
        # Schema name from environment variable or default
        schema_name = os.getenv("DB_SCHEMA", "workflow_runtime")
        
        # Set search path to the schema
        cursor.execute(f"SET search_path TO {schema_name}")
        
        # Get all tables in the schema
        cursor.execute(f"""
            SELECT table_name 
            FROM information_schema.tables 
            WHERE table_schema = '{schema_name}'
            AND table_type = 'BASE TABLE'
            ORDER BY table_name
        """)
        
        tables = cursor.fetchall()
        
        if not tables:
            print(f"⚠️ No tables found in schema '{schema_name}'")
            return True
        
        print(f"Found {len(tables)} tables in schema '{schema_name}'")
        
        # Disable foreign key checks to allow truncating tables with foreign key constraints
        cursor.execute("SET session_replication_role = 'replica'")
        
        # Define problematic tables that need special handling
        problematic_tables = ['user_oauth_tokens', 'user_sessions', 'users']
        
        # First handle non-problematic tables
        truncated_tables = 0
        for table in tables:
            table_name = table[0]
            
            # Skip problematic tables for now
            if table_name in problematic_tables:
                print(f"⚠️ Skipping problematic table for now: {table_name}")
                continue
                
            try:
                print(f"Truncating table: {table_name}")
                cursor.execute(f"TRUNCATE TABLE {schema_name}.{table_name} CASCADE")
                conn.commit()
                truncated_tables += 1
            except Exception as e:
                conn.rollback()
                print(f"❌ Error truncating table {table_name}: {e}")
                
                # If we encounter an error, try to clean up locks again and retry
                print(f"Attempting to clean up locks and retry truncating {table_name}...")
                cleanup_postgres_locks(conn)
                
                try:
                    print(f"Retrying truncate for table: {table_name}")
                    cursor.execute(f"TRUNCATE TABLE {schema_name}.{table_name} CASCADE")
                    conn.commit()
                    truncated_tables += 1
                    print(f"✅ Successfully truncated table {table_name} on retry")
                except Exception as retry_error:
                    conn.rollback()
                    print(f"❌ Error truncating table {table_name} on retry: {retry_error}")
        
        # Now handle problematic tables with special care
        for table_name in problematic_tables:
            # Check if the table exists in our schema
            cursor.execute(f"""
                SELECT 1 FROM information_schema.tables 
                WHERE table_schema = '{schema_name}'
                AND table_name = '{table_name}'
            """)
            
            if not cursor.fetchone():
                print(f"⚠️ Problematic table {table_name} not found in schema, skipping")
                continue
                
            print(f"🔄 Handling problematic table: {table_name}")
            
            # First, try to kill any processes that might be locking this table
            try:
                cursor.execute(f"""
                    SELECT pid FROM pg_locks l
                    JOIN pg_class c ON l.relation = c.oid
                    JOIN pg_namespace n ON c.relnamespace = n.oid
                    WHERE c.relname = '{table_name}'
                    AND n.nspname = '{schema_name}'
                """)
                
                lock_pids = cursor.fetchall()
                for pid in lock_pids:
                    print(f"Terminating process {pid[0]} that's locking {table_name}")
                    cursor.execute(f"SELECT pg_terminate_backend({pid[0]})")
                
                # Wait a moment for locks to clear
                print(f"Waiting for locks to clear on {table_name}...")
                conn.commit()
                
                # Try a more aggressive approach - delete instead of truncate
                print(f"Attempting to DELETE FROM {table_name} instead of TRUNCATE...")
                cursor.execute(f"DELETE FROM {schema_name}.{table_name}")
                conn.commit()
                truncated_tables += 1
                print(f"✅ Successfully cleared table {table_name} using DELETE")
                
            except Exception as e:
                conn.rollback()
                print(f"❌ Error handling problematic table {table_name}: {e}")
                print(f"⚠️ You may need to manually clean up {table_name} table")
        
        # Re-enable foreign key checks
        cursor.execute("SET session_replication_role = 'origin'")
        
        # We're not dropping entity tables anymore, just truncating them
        print(f"✅ {truncated_tables} of {len(tables)} tables have been truncated, keeping table structure intact")
        
        print("✅ PostgreSQL cleanup completed")
        return True
    except Exception as e:
        if conn:
            conn.rollback()
        print(f"❌ Error cleaning up PostgreSQL: {e}")
        return False

def main():
    """Main function."""
    print("=" * 80)
    print("Starting database cleanup process...")
    print("Note: This will truncate all tables in the workflow_runtime schema.")
    print("      A backup of the leave_sub_type table is available in leave_subtype_insert.sql")
    print("=" * 80)
    
    # Ask for confirmation
    if len(sys.argv) <= 1 or sys.argv[1] != "--force":
        confirmation = input("Are you sure you want to proceed? (y/n): ")
        if confirmation.lower() != 'y':
            print("Operation cancelled by user.")
            return False
    
    # # Clean up MongoDB
    # print("\n[1/2] Cleaning up MongoDB...")
    # mongodb_success = cleanup_mongodb()
    
    # Connect to PostgreSQL
    print("\n[2/2] Cleaning up PostgreSQL...")
    conn = connect_to_db()
    if not conn:
        print("❌ Failed to connect to PostgreSQL database")
        return False
    
    # Clean up PostgreSQL
    postgres_success = cleanup_postgres(conn)
    
    # Close connection
    conn.close()
    
    print("\n" + "=" * 80)
    if postgres_success:
        print("✅ Database cleanup completed successfully")
        print("Note: To restore leave_sub_type data, run: psql -f leave_subtype_insert.sql")
        return True
    else:
        print("❌ Database cleanup failed")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
