"""
Check entity attributes in the database.
"""

import os
import sys
import psycopg2
import datetime

def get_db_connection(schema_name):
    """
    Get a database connection.
    
    Args:
        schema_name: Schema name
        
    Returns:
        Database connection
    """
    try:
        conn = psycopg2.connect(
            host="workflow_postgres",
            database="workflow_system",
            user="postgres",
            password="postgres"
        )
        conn.autocommit = True
        return conn
    except Exception as e:
        print(f"Error connecting to database: {str(e)}")
        return None

def check_entity_attributes():
    """Check entity attributes in the database."""
    conn = get_db_connection("workflow_temp")
    if not conn:
        print("Failed to connect to database")
        return
    
    try:
        with conn.cursor() as cursor:
            # Get all entities
            cursor.execute("SELECT entity_id, name FROM workflow_temp.entities")
            entities = cursor.fetchall()
            
            # Create output file with timestamp
            timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
            output_file = f"entity_attributes_{timestamp}.txt"
            
            with open(output_file, 'w') as f:
                f.write(f"=== Entity Attributes - {datetime.datetime.now()} ===\n\n")
                
                for entity_id, entity_name in entities:
                    f.write(f"Entity: {entity_name} (ID: {entity_id})\n")
                    f.write("-" * 50 + "\n")
                    
                    # Get attributes for entity
                    cursor.execute(
                        "SELECT name FROM workflow_temp.entity_attributes WHERE entity_id = %s ORDER BY name",
                        (entity_id,)
                    )
                    attributes = cursor.fetchall()
                    
                    for attr in attributes:
                        f.write(f"- {attr[0]}\n")
                    
                    f.write("\n\n")
            
            print(f"Entity attributes saved to {output_file}")
    except Exception as e:
        print(f"Error checking entity attributes: {str(e)}")
    finally:
        conn.close()

if __name__ == "__main__":
    check_entity_attributes()
