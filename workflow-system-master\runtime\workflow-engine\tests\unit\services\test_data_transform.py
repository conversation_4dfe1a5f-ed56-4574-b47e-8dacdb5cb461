import unittest
from typing import Dict, Any, List
import sys
import os

# Add project root to path if needed
project_root = '/home/<USER>/workflow-system/runtime/workflow-engine'
if project_root not in sys.path:
    sys.path.insert(0, project_root)

# Import the function to test
from app.services.system_functions import data_transform

class TestDataTransform(unittest.TestCase):
    def test_array_to_object(self):
        """Test array_to_object transformation."""
        data = [
            {"id": "1", "name": "<PERSON>", "age": 30},
            {"id": "2", "name": "<PERSON>", "age": 25},
            {"id": "3", "name": "<PERSON>", "age": 35}
        ]
        
        result = data_transform(data, "array_to_object", {"key_field": "id"})
        
        expected = {
            "1": {"id": "1", "name": "<PERSON>", "age": 30},
            "2": {"id": "2", "name": "<PERSON>", "age": 25},
            "3": {"id": "3", "name": "<PERSON>", "age": 35}
        }
        
        self.assertEqual(result, expected)
    
    def test_array_to_object_invalid_data(self):
        """Test array_to_object with invalid data type."""
        data = {"not": "an array"}
        
        with self.assertRaises(ValueError) as context:
            data_transform(data, "array_to_object", {"key_field": "id"})
        
        self.assertEqual(str(context.exception), "Data must be an array for array_to_object transformation")
    
    def test_array_to_object_missing_key_field(self):
        """Test array_to_object without key_field option."""
        data = [{"id": "1", "name": "Alice"}]
        
        with self.assertRaises(ValueError) as context:
            data_transform(data, "array_to_object", {})
        
        self.assertEqual(str(context.exception), "key_field option is required for array_to_object transformation")
    
    def test_array_to_object_missing_keys(self):
        """Test array_to_object with items missing the key field."""
        data = [
            {"id": "1", "name": "Alice"},
            {"name": "Bob"},  # Missing id
            {"id": "3", "name": "Charlie"}
        ]
        
        result = data_transform(data, "array_to_object", {"key_field": "id"})
        
        expected = {
            "1": {"id": "1", "name": "Alice"},
            "3": {"id": "3", "name": "Charlie"}
        }
        
        self.assertEqual(result, expected)
    
    def test_object_to_array_default(self):
        """Test object_to_array transformation with default options."""
        data = {
            "user1": {"name": "Alice", "age": 30},
            "user2": {"name": "Bob", "age": 25},
            "user3": {"name": "Charlie", "age": 35}
        }
        
        result = data_transform(data, "object_to_array")
        
        expected = [
            {"key": "user1", "name": "Alice", "age": 30},
            {"key": "user2", "name": "Bob", "age": 25},
            {"key": "user3", "name": "Charlie", "age": 35}
        ]
        
        # Sort by key for consistent comparison
        result.sort(key=lambda x: x["key"])
        expected.sort(key=lambda x: x["key"])
        
        self.assertEqual(result, expected)
    
    def test_object_to_array_custom_key_field(self):
        """Test object_to_array with custom key_field."""
        data = {
            "user1": {"name": "Alice", "age": 30},
            "user2": {"name": "Bob", "age": 25},
            "user3": {"name": "Charlie", "age": 35}
        }
        
        result = data_transform(data, "object_to_array", {"key_field": "user_id"})
        
        expected = [
            {"user_id": "user1", "name": "Alice", "age": 30},
            {"user_id": "user2", "name": "Bob", "age": 25},
            {"user_id": "user3", "name": "Charlie", "age": 35}
        ]
        
        # Sort by key for consistent comparison
        result.sort(key=lambda x: x["user_id"])
        expected.sort(key=lambda x: x["user_id"])
        
        self.assertEqual(result, expected)
    
    def test_object_to_array_primitive_values(self):
        """Test object_to_array with primitive values."""
        data = {
            "item1": 100,
            "item2": "string value",
            "item3": True
        }
        
        result = data_transform(data, "object_to_array")
        
        expected = [
            {"key": "item1", "value": 100},
            {"key": "item2", "value": "string value"},
            {"key": "item3", "value": True}
        ]
        
        # Sort by key for consistent comparison
        result.sort(key=lambda x: x["key"])
        expected.sort(key=lambda x: x["key"])
        
        self.assertEqual(result, expected)
    
    def test_object_to_array_mixed_values(self):
        """Test object_to_array with mixed value types."""
        data = {
            "item1": 100,
            "item2": {"name": "Object value"},
            "item3": True
        }
        
        result = data_transform(data, "object_to_array")
        
        expected = [
            {"key": "item1", "value": 100},
            {"key": "item2", "name": "Object value"},
            {"key": "item3", "value": True}
        ]
        
        # Sort by key for consistent comparison
        result.sort(key=lambda x: x["key"])
        expected.sort(key=lambda x: x["key"])
        
        self.assertEqual(result, expected)
    
    def test_object_to_array_self_format(self):
        """Test object_to_array with __self__ value_field option."""
        data = {
            "item1": 100,
            "item2": {"name": "Object value"},
            "item3": True
        }
        
        result = data_transform(data, "object_to_array", {"value_field": "__self__"})
        
        expected = [
            {"key": "item1", "value": 100},
            {"key": "item2", "value": {"name": "Object value"}},
            {"key": "item3", "value": True}
        ]
        
        # Sort by key for consistent comparison
        result.sort(key=lambda x: x["key"])
        expected.sort(key=lambda x: x["key"])
        
        self.assertEqual(result, expected)
    
    def test_object_to_array_invalid_data(self):
        """Test object_to_array with invalid data type."""
        data = ["not", "an", "object"]
        
        with self.assertRaises(ValueError) as context:
            data_transform(data, "object_to_array")
        
        self.assertEqual(str(context.exception), "Data must be an object for object_to_array transformation")
    
    def test_flatten_basic(self):
        """Test flatten transformation with basic nested object."""
        data = {
            "user": {
                "name": "Alice",
                "address": {
                    "street": "123 Main St",
                    "city": "New York",
                    "zip": "10001"
                }
            },
            "active": True
        }
        
        result = data_transform(data, "flatten")
        
        expected = {
            "user.name": "Alice",
            "user.address.street": "123 Main St",
            "user.address.city": "New York",
            "user.address.zip": "10001",
            "active": True
        }
        
        self.assertEqual(result, expected)
    
    def test_flatten_custom_delimiter(self):
        """Test flatten transformation with custom delimiter."""
        data = {
            "user": {
                "name": "Alice",
                "address": {
                    "street": "123 Main St"
                }
            }
        }
        
        result = data_transform(data, "flatten", {"delimiter": "/"})
        
        expected = {
            "user/name": "Alice",
            "user/address/street": "123 Main St"
        }
        
        self.assertEqual(result, expected)
    
    def test_flatten_invalid_data(self):
        """Test flatten with invalid data type."""
        data = ["not", "an", "object"]
        
        with self.assertRaises(ValueError) as context:
            data_transform(data, "flatten")
        
        self.assertEqual(str(context.exception), "Data must be an object for flatten transformation")
    
    def test_pivot_basic(self):
        """Test pivot transformation with basic data."""
        data = [
            {"product": "Apple", "quarter": "Q1", "sales": 100},
            {"product": "Apple", "quarter": "Q2", "sales": 150},
            {"product": "Banana", "quarter": "Q1", "sales": 80},
            {"product": "Banana", "quarter": "Q2", "sales": 120}
        ]
        
        result = data_transform(data, "pivot", {
            "row_field": "product",
            "column_field": "quarter",
            "value_field": "sales"
        })
        
        expected = {
            "Apple": {"Q1": 100, "Q2": 150},
            "Banana": {"Q1": 80, "Q2": 120}
        }
        
        self.assertEqual(result, expected)
    
    def test_pivot_missing_fields(self):
        """Test pivot with items missing fields."""
        data = [
            {"product": "Apple", "quarter": "Q1", "sales": 100},
            {"product": "Apple", "sales": 150},  # Missing quarter
            {"quarter": "Q1", "sales": 80},  # Missing product
            {"product": "Banana", "quarter": "Q2", "sales": 120}
        ]
        
        result = data_transform(data, "pivot", {
            "row_field": "product",
            "column_field": "quarter",
            "value_field": "sales"
        })
        
        expected = {
            "Apple": {"Q1": 100},
            "Banana": {"Q2": 120}
        }
        
        self.assertEqual(result, expected)
    
    def test_pivot_missing_options(self):
        """Test pivot without required options."""
        data = [{"product": "Apple", "quarter": "Q1", "sales": 100}]
        
        with self.assertRaises(ValueError) as context:
            data_transform(data, "pivot", {"row_field": "product"})
        
        self.assertEqual(str(context.exception), "row_field, column_field, and value_field options are required for pivot transformation")
    
    def test_pivot_invalid_data(self):
        """Test pivot with invalid data type."""
        data = {"not": "an array"}
        
        with self.assertRaises(ValueError) as context:
            data_transform(data, "pivot", {
                "row_field": "product",
                "column_field": "quarter",
                "value_field": "sales"
            })
        
        self.assertEqual(str(context.exception), "Data must be an array for pivot transformation")
    
    def test_to_table_basic(self):
        """Test to_table transformation with basic data."""
        data = [
            {"name": "Alice", "age": 30, "active": True},
            {"name": "Bob", "age": 25, "active": False},
            {"name": "Charlie", "age": 35, "active": True}
        ]
        
        result = data_transform(data, "to_table")
        
        # The order of columns might vary, so we need to check more carefully
        self.assertEqual(len(result), 4)  # Header + 3 rows
        self.assertEqual(set(result[0]), {"name", "age", "active"})  # Header has all columns
        
        # Check data rows
        data_rows = result[1:]
        self.assertEqual(len(data_rows), 3)
        
        # Create a function to find rows by name
        def find_row(name):
            for i, row in enumerate(data_rows):
                name_index = result[0].index("name")
                if row[name_index] == name:
                    return i, row
            return None, None
        
        # Check Alice's row
        alice_idx, alice_row = find_row("Alice")
        self.assertIsNotNone(alice_row)
        age_index = result[0].index("age")
        active_index = result[0].index("active")
        self.assertEqual(alice_row[age_index], 30)
        self.assertEqual(alice_row[active_index], True)
    
    def test_to_table_custom_columns(self):
        """Test to_table with custom columns specified."""
        data = [
            {"name": "Alice", "age": 30, "active": True, "email": "<EMAIL>"},
            {"name": "Bob", "age": 25, "active": False, "email": "<EMAIL>"}
        ]
        
        result = data_transform(data, "to_table", {"columns": ["name", "email"]})
        
        expected = [
            ["name", "email"],
            ["Alice", "<EMAIL>"],
            ["Bob", "<EMAIL>"]
        ]
        
        self.assertEqual(result, expected)
    
    def test_to_table_without_header(self):
        """Test to_table without including header."""
        data = [
            {"name": "Alice", "age": 30},
            {"name": "Bob", "age": 25}
        ]
        
        result = data_transform(data, "to_table", {"include_header": False, "columns": ["name", "age"]})
        
        expected = [
            ["Alice", 30],
            ["Bob", 25]
        ]
        
        self.assertEqual(result, expected)
    
    def test_to_table_invalid_data(self):
        """Test to_table with invalid data type."""
        data = {"not": "an array"}
        
        with self.assertRaises(ValueError) as context:
            data_transform(data, "to_table")
        
        self.assertEqual(str(context.exception), "Data must be an array of objects for to_table transformation")
    
    def test_to_table_non_dict_items(self):
        """Test to_table with array containing non-dict items."""
        data = ["not", "dict", "items"]
        
        with self.assertRaises(ValueError) as context:
            data_transform(data, "to_table")
        
        self.assertEqual(str(context.exception), "Data must be an array of objects for to_table transformation")
    
    def test_invalid_transform_type(self):
        """Test with invalid transformation type."""
        data = [{"name": "Alice"}]
        
        with self.assertRaises(ValueError) as context:
            data_transform(data, "invalid_type")
        
        self.assertEqual(str(context.exception), "Unsupported transformation type: invalid_type")
    
    def test_case_insensitive_transform_type(self):
        """Test that transform_type is case-insensitive."""
        data = [{"id": "1", "name": "Alice"}]
        
        result = data_transform(data, "ARRAY_TO_OBJECT", {"key_field": "id"})
        
        expected = {"1": {"id": "1", "name": "Alice"}}
        
        self.assertEqual(result, expected)
    
    def test_with_none_options(self):
        """Test with None options parameter."""
        data = [{"id": "1", "name": "Alice"}]
        
        with self.assertRaises(ValueError) as context:
            data_transform(data, "array_to_object", None)
        
        self.assertEqual(str(context.exception), "key_field option is required for array_to_object transformation")

if __name__ == '__main__':
    unittest.main()