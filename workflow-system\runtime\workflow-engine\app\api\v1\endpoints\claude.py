from fastapi import API<PERSON><PERSON><PERSON>, Depends, HTTPException
from sqlalchemy.orm import Session
from typing import Dict, Any, Optional

# ✅ Corrected import for SQLAlchemy session
from app.database.postgres import get_db

from app.services.claude_service import ClaudeService

router = APIRouter()
claude_service = ClaudeService()

@router.post("/query")
async def query_claude(
    message: str,
    system_prompt: Optional[str] = None,
    workflow_id: Optional[str] = None,
    step_id: Optional[str] = None,
    db: Session = Depends(get_db)
):
    """
    Send a query to Claude API
    """
    try:
        # Get workflow context if workflow_id and step_id are provided
        context = None
        if workflow_id and step_id:
            context = claude_service._get_workflow_context(db, workflow_id, step_id)
        
        # Query Claude
        response = await claude_service.query_claude(
            user_message=message,
            system_prompt=system_prompt,
            workflow_context=context
        )
        
        return {"response": response}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error querying Claude API: {str(e)}")

@router.post("/enhance-workflow-step")
async def enhance_workflow_step(
    step_data: Dict[str, Any],
    db: Session = Depends(get_db)
):
    """
    Enhance a workflow step with Claude's assistance
    """
    try:
        enhanced_step = await claude_service.enhance_workflow_step(db, step_data)
        return enhanced_step
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error enhancing workflow step: {str(e)}")
