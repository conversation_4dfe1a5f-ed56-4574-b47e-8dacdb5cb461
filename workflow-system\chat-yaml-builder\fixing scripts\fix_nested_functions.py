#!/usr/bin/env python3

"""
This script fixes the parameters in the lo_nested_functions table.
It updates the parameter references to use the new IDs after fixing duplicate IDs.
"""

import psycopg2
from datetime import datetime
import os
import json
import traceback

# Set log path to current working directory
LOG_PATH = os.path.join(
    os.path.dirname(os.path.abspath(__file__)),
    f"fix_nested_functions_log_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
)

# Logging function
def log(msg):
    with open(LOG_PATH, "a") as f:
        f.write(f"{datetime.now().strftime('%Y-%m-%d %H:%M:%S')} | {msg}\n")
    print(msg)

# Database configuration
PG_CONFIG = {
    "dbname": "workflow_system",
    "user": "postgres",
    "password": "workflow_postgres_secure_password",
    "host": "**********",
    "port": "5432"
}

def connect_to_db():
    """Connect to the PostgreSQL database."""
    try:
        conn = psycopg2.connect(**PG_CONFIG)
        log("✅ Connected to PostgreSQL database")
        return conn
    except Exception as e:
        log(f"❌ Error connecting to database: {e}")
        raise

def fix_nested_functions():
    """Fix parameters in the lo_nested_functions table."""
    conn = connect_to_db()
    cursor = conn.cursor()
    
    try:
        # Set search path to the workflow_runtime schema
        cursor.execute("SET search_path TO workflow_runtime")
        
        # Get a mapping of old IDs to new IDs
        cursor.execute("""
            SELECT id
            FROM lo_input_items
            WHERE id LIKE '%\\_%'
        """)
        
        new_ids = cursor.fetchall()
        id_mapping = {}
        
        for new_id in new_ids:
            base_id = new_id[0].split('_')[0]
            id_mapping[base_id] = new_id[0]
        
        log(f"📊 ID mapping: {id_mapping}")
        
        # Find all nested functions with parameters
        log("🔍 Finding nested functions with parameters...")
        cursor.execute("""
            SELECT id, nested_function_id, function_name, parameters
            FROM lo_nested_functions
            WHERE parameters IS NOT NULL AND parameters::text != 'null'
        """)
        
        functions = cursor.fetchall()
        
        if not functions:
            log("✅ No nested functions with parameters found")
            return
        
        log(f"⚠️ Found {len(functions)} nested functions with parameters")
        
        # Process each function
        for func_id, nested_function_id, function_name, parameters in functions:
            log(f"🔧 Processing function {function_name} (ID: {nested_function_id})")
            
            # Parse the parameters JSON
            if isinstance(parameters, str):
                params = json.loads(parameters)
            else:
                params = parameters
            
            # Check if any parameters need to be updated
            needs_update = False
            updated_params = {}
            
            for param_name, param_value in params.items():
                if isinstance(param_value, str):
                    # Check for template strings like "${in003}"
                    for old_id, new_id in id_mapping.items():
                        if f"${{{old_id}}}" in param_value:
                            new_value = param_value.replace(f"${{{old_id}}}", f"${{{new_id}}}")
                            updated_params[param_name] = new_value
                            needs_update = True
                            log(f"🔄 Updating parameter {param_name}: {param_value} -> {new_value}")
                            break
                    else:
                        updated_params[param_name] = param_value
                else:
                    updated_params[param_name] = param_value
            
            if needs_update:
                # Update the parameters
                cursor.execute("""
                    UPDATE lo_nested_functions
                    SET parameters = %s
                    WHERE id = %s
                """, (json.dumps(updated_params), func_id))
                
                conn.commit()
                log(f"✅ Updated parameters for function {function_name}: {params} -> {updated_params}")
        
        log("✅ All nested functions have been fixed")
        
    except Exception as e:
        if conn:
            conn.rollback()
        log(f"❌ Error fixing nested functions: {e}")
        log(traceback.format_exc())
    finally:
        if cursor:
            cursor.close()
        if conn:
            conn.close()

if __name__ == "__main__":
    fix_nested_functions()
