-- Purchase Furniture LO Input Items Insert Script
-- Inserts input items for GO2.LO1, GO2.LO2, GO2.LO3

SET search_path TO workflow_runtime;

-- =====================================================
-- LO INPUT ITEMS
-- =====================================================

-- GO2.LO1 Input Items (Furniture Selection)
INSERT INTO lo_input_items (
    item_id, input_stack_id, slot_id, source_type, source_description, required, 
    lo_id, data_type, ui_control, nested_function_id, is_visible, name, type, 
    created_at, updated_at, created_by, updated_by, read_only, agent_type, 
    dependent_attribute, dependent_attribute_value, enum_values, default_value, 
    information_field, constant_field, entity_id, attribute_id, entity_name, 
    attribute_name, version, natural_language
) VALUES 
('GO2.LO1.IP1.IT1', 'GO2.LO1.IP1', 'GO2.LO1.SL1', 'user', 'User selects furniture type', true, 'GO2.LO1', 'string', 'dropdown', NULL, true, 'furnituretypeid', 'input', NOW(), NOW(), 'system', 'system', false, 'user', false, NULL, NULL, NULL, false, false, 'E15', 'E15.At1', 'FurnitureType', 'furnituretypeid', '1.0', 'Dropdown for furniture type selection'),
('GO2.LO1.IP1.IT2', 'GO2.LO1.IP1', 'GO2.LO1.SL2', 'user', 'User selects specific product', true, 'GO2.LO1', 'string', 'dropdown', NULL, true, 'productid', 'input', NOW(), NOW(), 'system', 'system', false, 'user', true, 'furnituretypeid', NULL, NULL, false, false, 'E14', 'E14.At1', 'FurnitureProduct', 'productid', '1.0', 'Dependent dropdown for product selection based on furniture type'),
('GO2.LO1.IP1.IT3', 'GO2.LO1.IP1', 'GO2.LO1.SL3', 'information', 'Display selected product information', false, 'GO2.LO1', 'string', 'label', NULL, true, 'productname', 'information', NOW(), NOW(), 'system', 'system', true, 'system', false, NULL, NULL, NULL, true, false, 'E14', 'E14.At2', 'FurnitureProduct', 'productname', '1.0', 'Information field showing selected product name'),
('GO2.LO1.IP1.IT4', 'GO2.LO1.IP1', 'GO2.LO1.SL4', 'information', 'Display product price', false, 'GO2.LO1', 'decimal', 'label', NULL, true, 'unitprice', 'information', NOW(), NOW(), 'system', 'system', true, 'system', false, NULL, NULL, NULL, true, false, 'E14', 'E14.At4', 'FurnitureProduct', 'price', '1.0', 'Information field showing product price'),
('GO2.LO1.IP1.IT5', 'GO2.LO1.IP1', 'GO2.LO1.SL5', 'information', 'Display available inventory', false, 'GO2.LO1', 'integer', 'label', NULL, true, 'availableinventory', 'information', NOW(), NOW(), 'system', 'system', true, 'system', false, NULL, NULL, NULL, true, false, 'E14', 'E14.At5', 'FurnitureProduct', 'availableinventory', '1.0', 'Information field showing available stock');

-- GO2.LO2 Input Items (Cart and Payment)
INSERT INTO lo_input_items (
    item_id, input_stack_id, slot_id, source_type, source_description, required, 
    lo_id, data_type, ui_control, nested_function_id, is_visible, name, type, 
    created_at, updated_at, created_by, updated_by, read_only, agent_type, 
    dependent_attribute, dependent_attribute_value, enum_values, default_value, 
    information_field, constant_field, entity_id, attribute_id, entity_name, 
    attribute_name, version, natural_language
) VALUES 
('GO2.LO2.IP1.IT1', 'GO2.LO2.IP1', 'GO2.LO2.SL1', 'mapping', 'Mapped from GO2.LO1 selection', false, 'GO2.LO2', 'string', 'label', NULL, true, 'productname', 'mapping', NOW(), NOW(), 'system', 'system', true, 'system', false, NULL, NULL, NULL, true, false, 'E14', 'E14.At2', 'FurnitureProduct', 'productname', '1.0', 'Product name mapped from previous step'),
('GO2.LO2.IP1.IT2', 'GO2.LO2.IP1', 'GO2.LO2.SL2', 'mapping', 'Mapped unit price from GO2.LO1', false, 'GO2.LO2', 'decimal', 'label', NULL, true, 'unitprice', 'mapping', NOW(), NOW(), 'system', 'system', true, 'system', false, NULL, NULL, NULL, true, false, 'E14', 'E14.At4', 'FurnitureProduct', 'price', '1.0', 'Unit price mapped from product selection'),
('GO2.LO2.IP1.IT3', 'GO2.LO2.IP1', 'GO2.LO2.SL3', 'user', 'User enters quantity', true, 'GO2.LO2', 'integer', 'integer', NULL, true, 'quantity', 'input', NOW(), NOW(), 'system', 'system', false, 'user', false, NULL, NULL, '1', false, false, 'E13', 'E13.At5', 'FurnitureOrder', 'quantity', '1.0', 'User input for order quantity'),
('GO2.LO2.IP1.IT4', 'GO2.LO2.IP1', 'GO2.LO2.SL4', 'nested_function', 'Calculate subtotal', false, 'GO2.LO2', 'decimal', 'label', 'NF_CALCULATE_SUBTOTAL', true, 'subtotal', 'calculated', NOW(), NOW(), 'system', 'system', true, 'system', false, NULL, NULL, NULL, false, false, 'E13', 'E13.At7', 'FurnitureOrder', 'subtotal', '1.0', 'Calculated subtotal using nested function'),
('GO2.LO2.IP1.IT5', 'GO2.LO2.IP1', 'GO2.LO2.SL5', 'nested_function', 'Calculate GST amount', false, 'GO2.LO2', 'decimal', 'label', 'NF_CALCULATE_GST', true, 'gstamount', 'calculated', NOW(), NOW(), 'system', 'system', true, 'system', false, NULL, NULL, NULL, false, false, 'E13', 'E13.At8', 'FurnitureOrder', 'gstamount', '1.0', 'Calculated GST using nested function'),
('GO2.LO2.IP1.IT6', 'GO2.LO2.IP1', 'GO2.LO2.SL6', 'nested_function', 'Calculate total amount', false, 'GO2.LO2', 'decimal', 'label', 'NF_CALCULATE_TOTAL', true, 'totalamount', 'calculated', NOW(), NOW(), 'system', 'system', true, 'system', false, NULL, NULL, NULL, false, false, 'E13', 'E13.At9', 'FurnitureOrder', 'totalamount', '1.0', 'Calculated total using nested function'),
('GO2.LO2.IP1.IT7', 'GO2.LO2.IP1', 'GO2.LO2.SL7', 'user', 'User selects payment method', true, 'GO2.LO2', 'string', 'dropdown', NULL, true, 'paymentmethod', 'input', NOW(), NOW(), 'system', 'system', false, 'user', false, NULL, '["UPI", "Credit Card"]', NULL, false, false, 'E13', 'E13.At10', 'FurnitureOrder', 'paymentmethod', '1.0', 'Payment method selection dropdown'),
('GO2.LO2.IP1.IT8', 'GO2.LO2.IP1', 'GO2.LO2.SL8', 'user', 'UPI ID for UPI payments', false, 'GO2.LO2', 'string', 'text', NULL, true, 'upiid', 'input', NOW(), NOW(), 'system', 'system', false, 'user', true, 'UPI', NULL, NULL, false, false, 'E16', 'E16.At4', 'PaymentDetails', 'upiid', '1.0', 'UPI ID input when UPI is selected'),
('GO2.LO2.IP1.IT9', 'GO2.LO2.IP1', 'GO2.LO2.SL9', 'user', 'Card name for credit card', false, 'GO2.LO2', 'string', 'text', NULL, true, 'cardname', 'input', NOW(), NOW(), 'system', 'system', false, 'user', true, 'Credit Card', NULL, NULL, false, false, 'E16', 'E16.At5', 'PaymentDetails', 'cardname', '1.0', 'Card name when credit card is selected'),
('GO2.LO2.IP1.IT10', 'GO2.LO2.IP1', 'GO2.LO2.SL10', 'user', 'Card number for credit card', false, 'GO2.LO2', 'string', 'text', NULL, true, 'cardnumber', 'input', NOW(), NOW(), 'system', 'system', false, 'user', true, 'Credit Card', NULL, NULL, false, false, 'E16', 'E16.At6', 'PaymentDetails', 'cardnumber', '1.0', 'Card number when credit card is selected'),
('GO2.LO2.IP1.IT11', 'GO2.LO2.IP1', 'GO2.LO2.SL11', 'user', 'CVV for credit card', false, 'GO2.LO2', 'string', 'text', NULL, true, 'cvv', 'input', NOW(), NOW(), 'system', 'system', false, 'user', true, 'Credit Card', NULL, NULL, false, false, 'E16', 'E16.At7', 'PaymentDetails', 'cvv', '1.0', 'CVV when credit card is selected'),
('GO2.LO2.IP1.IT12', 'GO2.LO2.IP1', 'GO2.LO2.SL12', 'user', 'Expiry date for credit card', false, 'GO2.LO2', 'string', 'text', NULL, true, 'expirydate', 'input', NOW(), NOW(), 'system', 'system', false, 'user', true, 'Credit Card', NULL, NULL, false, false, 'E16', 'E16.At8', 'PaymentDetails', 'expirydate', '1.0', 'Expiry date when credit card is selected');

-- GO2.LO3 Input Items (Order Completion)
INSERT INTO lo_input_items (
    item_id, input_stack_id, slot_id, source_type, source_description, required, 
    lo_id, data_type, ui_control, nested_function_id, is_visible, name, type, 
    created_at, updated_at, created_by, updated_by, read_only, agent_type, 
    dependent_attribute, dependent_attribute_value, enum_values, default_value, 
    information_field, constant_field, entity_id, attribute_id, entity_name, 
    attribute_name, version, natural_language
) VALUES 
('GO2.LO3.IP1.IT1', 'GO2.LO3.IP1', 'GO2.LO3.SL1', 'mapping', 'Order summary from previous steps', false, 'GO2.LO3', 'string', 'label', NULL, true, 'ordersummary', 'mapping', NOW(), NOW(), 'system', 'system', true, 'system', false, NULL, NULL, NULL, true, false, 'E13', 'E13.At9', 'FurnitureOrder', 'totalamount', '1.0', 'Order summary mapped from previous steps'),
('GO2.LO3.IP1.IT2', 'GO2.LO3.IP1', 'GO2.LO3.SL2', 'system', 'Generate order ID', false, 'GO2.LO3', 'string', 'label', 'NF_GENERATE_ORDER_ID', true, 'orderid', 'system', NOW(), NOW(), 'system', 'system', true, 'system', false, NULL, NULL, NULL, false, false, 'E13', 'E13.At1', 'FurnitureOrder', 'orderid', '1.0', 'System generated order ID'),
('GO2.LO3.IP1.IT3', 'GO2.LO3.IP1', 'GO2.LO3.SL3', 'system', 'Update inventory', false, 'GO2.LO3', 'boolean', 'checkbox', 'NF_UPDATE_INVENTORY', true, 'inventoryupdated', 'system', NOW(), NOW(), 'system', 'system', true, 'system', false, NULL, NULL, 'false', false, false, 'E14', 'E14.At5', 'FurnitureProduct', 'availableinventory', '1.0', 'System updates inventory levels');

COMMIT;
