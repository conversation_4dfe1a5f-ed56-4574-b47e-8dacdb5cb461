import streamlit as st
from openai import OpenAI
import os
import uuid
import datetime
from pathlib import Path
from pymongo import MongoClient
from validate_yaml import validate_yaml
from pipeline_runner import run_pipeline_from_text


# --- Setup ---
client = OpenAI(api_key=st.secrets.get("OPENAI_API_KEY") or os.getenv("OPENAI_API_KEY"))
mongo = MongoClient(st.secrets["MONGODB_URI"])
db = mongo["workflow_chat"]
convo_col = db["conversations"]
yaml_dir = Path("yamls")
yaml_dir.mkdir(exist_ok=True)

with open("final_chatgpt_system_prompt.yaml.txt", "r") as f:
    system_prompt = f.read()

with open("prescriptive_prompt_template.txt", "r") as f:
    prescriptive_template = f.read()

with open("java_prompt_template.txt", "r") as f:
    java_template = f.read()


#---Prompt for Generating Prescriptive Sentences
def get_prescriptive_prompt_from_yaml(yaml_text: str) -> str:
    """
    Injects the generated YAML into the prompt template for prescriptive sentence generation.
    """
    return prescriptive_template.replace("[INSERT YAML HERE]", f"\n```yaml\n{yaml_text.strip()}\n```")

#---Prompt for Generating Java Code & Injecting YAML
def get_java_prompt_from_yaml(yaml_text: str) -> str:
    """
    Injects the generated YAML into the Java code generation prompt template.
    """
    return java_template.replace("[INSERT YAML HERE]", f"\n```yaml\n{yaml_text.strip()}\n```")


st.markdown("""
    <style>
        * {
            font-family: Cambria, serif !important;
        }
        html, body, div, p, span, input, textarea, label, button {
            font-family: Cambria, serif !important;
        }
    </style>
""", unsafe_allow_html=True)


# --- Session State ---
if "chat_history" not in st.session_state:
    st.session_state.chat_history = []
if "final_yaml" not in st.session_state:
    st.session_state.final_yaml = None
if "prescriptive_text" not in st.session_state:
    st.session_state.prescriptive_text = ""

st.markdown("""
    <style>
        html, body, [class*="css"]  {
            
            font-family: 'Cambria', serif;
        }
        .block-container {
            padding: 2rem 3rem 2rem 3rem;
        }
        .section-header {
            font-size: 1.5rem;
            font-weight: 600;
            margin-bottom: 1rem;
        }
        .chatbox {
            border: 1px solid #ddd;
            border-radius: 10px;
            padding: 1rem;
            background-color: #fff;
            margin-bottom: 1rem;
        }
        .solution-button {
            margin-bottom: 0.5rem;
            text-align: left;
            width: 100%;
        }
        .gpt-msg {
            background-color: #f3f3f3;
            padding: 0.7rem;
            border-radius: 8px;
            margin-bottom: 0.5rem;
        }
    </style>
            
""", unsafe_allow_html=True)

st.markdown("""
    <style>
        .block-container {
            max-width: 100% !important;
            padding: 2rem 3rem 2rem 3rem;
        }
    </style>
""", unsafe_allow_html=True)


col1, col2, col3 = st.columns([1.5, 4, 4])

def strip_yaml(text: str) -> str:
    if "```yaml" in text:
        return text.split("```yaml")[0].strip()
    return text


# ✅ GPT Chat Handler — Must Be Above Layout
def chat_with_gpt(user_message):
    messages = [{"role": "system", "content": system_prompt}] + st.session_state.chat_history + [{"role": "user", "content": user_message}]
    response = client.chat.completions.create(
        model="gpt-4-1106-preview",
        messages=messages,
        temperature=0.2,
    )
    reply = response.choices[0].message.content.strip()

    # Split YAML from prescriptive message if present
    if "```yaml" in reply:
        parts = reply.split("```yaml")
        st.session_state.prescriptive_text = parts[0].strip()
        yaml_part = parts[1].replace("```", "").strip()

        # 🎯 Generate prescriptive sentences using the YAML
        prescriptive_prompt = get_prescriptive_prompt_from_yaml(yaml_part)
        prescriptive_response = client.chat.completions.create(
            model="gpt-4-1106-preview",
            messages=[
                {"role": "system", "content": "You are a workflow documentation expert."},
                {"role": "user", "content": prescriptive_prompt}
            ],
            temperature=0.2,
        )
        st.session_state.prescriptive_text = prescriptive_response.choices[0].message.content.strip()

        if "java_code" not in st.session_state:
            st.session_state.java_code = ""


        # 🎯 Generate Java Code using the YAML
        java_prompt = get_java_prompt_from_yaml(yaml_part)
        java_response = client.chat.completions.create(
            model="gpt-4-1106-preview",
            messages=[
                {"role": "system", "content": "You are a Java developer. Only output valid Java code without any comments or explanations."},
                {"role": "user", "content": java_prompt}
            ],
            temperature=0.2,
        )
        st.session_state.java_code = java_response.choices[0].message.content.strip()

        
    
    else:
        st.session_state.prescriptive_text = reply
        yaml_part = ""

    st.session_state.chat_history.append({"role": "user", "content": user_message})
    #st.session_state.chat_history.append({"role": "assistant", "content": reply})
    
    
    if "```yaml" in reply:
        st.session_state.chat_history.append({
            "role": "assistant",
            "content": st.session_state.prescriptive_text  # Only conversational part
        })
    else:
            assistant_msg = strip_yaml(reply)
            st.session_state.chat_history.append({"role": "assistant", "content": assistant_msg})

            #st.session_state.chat_history.append({"role": "assistant", "content": reply})

   

    return yaml_part


# LEFT PANE: Saved Solutions
with col1:
    st.markdown('<div class="section-header">📁 Solutions</div>', unsafe_allow_html=True)
    stored_yaml_docs = list(convo_col.find().sort("timestamp", -1))
    if not stored_yaml_docs:
        st.info("No saved versions found.")
    else:
        with st.expander("🧾 Saved Versions", expanded=True):
            for doc in stored_yaml_docs:
                version = doc.get("version", 0)
                created_on = doc.get("created_on", datetime.datetime.utcnow())
                label = doc.get("yaml_version") or f"{created_on.strftime('%Y%m%d-%H%M%S')}"  # Fallback label

                unique_key = f"{label}_{str(doc.get('_id'))}"

                if st.button(f"📄 {label}", key=unique_key):
                    st.session_state.chat_history = doc.get("chat_history", [])
                    st.session_state.final_yaml = doc.get("yaml_output", "") or doc.get("yaml_content", "")
                    st.session_state.prescriptive_text = doc.get("prescriptive_text", "Loaded from history.")


# CENTER PANE: Chat Interface
with col2:
    st.markdown('<div class="section-header">💬 Workflow Chat</div>', unsafe_allow_html=True)
    user_input = st.text_area("📝 Describe your requirement", height=120)
    st.markdown("Press 'Ask NSL' to generate Prescriptive Sentences.")

    if st.button("🧠 Ask NSL") and user_input.strip():
        yaml_output = chat_with_gpt(user_input.strip())

            # 💡 If GPT gives suggestions like "You may also want to...", offer follow-up
        if "you might want" in st.session_state.prescriptive_text.lower():
            st.info("💡 NSL has a recommendation for improvement.")
            if st.button("✅ Apply Suggested Change"):
                yaml_output = chat_with_gpt("Yes, please apply your suggestion")
                validation_result = validate_yaml(yaml_output)
                if validation_result["valid"]:
                    st.session_state.final_yaml = yaml_output
                    st.success("✅ Updated with GPT's recommendation")
                else:
                    st.error("❌ Validation failed after suggestion")
                    st.code(validation_result["error"])


        validation_result = validate_yaml(yaml_output)
        if validation_result["valid"]:
            st.session_state.final_yaml = yaml_output
            st.success("✅ Solution Validated")
        else:
            st.error("❌ Solution Validation Failed")
            st.code(validation_result["error"])

    st.markdown("---")

    # for msg in st.session_state.chat_history:
    #     if msg["role"] == "user":
    #         st.markdown(f"<div class='gpt-msg'>🧑 <b>You</b>:<br>{msg['content']}</div>", unsafe_allow_html=True)
    #     elif msg["role"] == "assistant":
    #         st.markdown(f"<div class='gpt-msg'>🤖 <b>NSL</b>:<br>{msg['content']}</div>", unsafe_allow_html=True)


    # for msg in st.session_state.chat_history:
    #     who = "🧑 **You**" if msg["role"] == "user" else "🤖 **NSL**"
    #     st.markdown(f"<div class='gpt-msg'>{who}:<br>{msg['content']}</div>", unsafe_allow_html=True)

    for msg in st.session_state.chat_history:
        who = "🧑 <b>You</b>" if msg["role"] == "user" else "🤖 <b>NSL</b>"
        st.markdown(f"<div class='gpt-msg'>{who}:<br>{msg['content']}</div>", unsafe_allow_html=True)

    # 🔁 Follow-up input to modify or continue conversation
    st.markdown("### ✏️ Refine or Follow-up")
    follow_up = st.text_input("Ask NSL to make changes or continue...", key="follow_up_input")

    if st.button("🔁 Continue Chat with NSL"):
        if follow_up.strip():
            yaml_output = chat_with_gpt(follow_up.strip())
            validation_result = validate_yaml(yaml_output)
            if validation_result["valid"]:
                st.session_state.final_yaml = yaml_output
                st.success("✅ YAML updated and validated")
            else:
                st.error("❌ Updated YAML failed validation")
                st.code(validation_result["error"])


# RIGHT PANE: Output Viewer
with col3:
    st.markdown('<div class="section-header">📄 Output Viewer</div>', unsafe_allow_html=True)
    if not st.session_state.final_yaml:
        st.info("Prescriptive Sentences will appear here once generated.")
    else:
        #view = st.radio("View Mode", ["YAML", "Prescriptive Sentences"], horizontal=True)

        # if view == "YAML":
        #     st.code(st.session_state.final_yaml, language="yaml")
        #     st.download_button("⬇️ Download YAML", st.session_state.final_yaml, file_name="workflow.yaml")
        # else:
        #     st.markdown(st.session_state.prescriptive_text or "_No prescriptive text available._")

        view = st.radio("View Mode", ["Prescriptive Sentences", "Java Code", "YAML"], horizontal=True)

        if view == "Prescriptive Sentences":
            st.markdown(st.session_state.prescriptive_text or "_No prescriptive text available._")

        elif view == "Java Code":
            if st.session_state.java_code:
                st.code(st.session_state.java_code, language="java")
                st.download_button("⬇️ Download Java", st.session_state.java_code, file_name="WorkflowApp.java")
            else:
                st.info("Java code not available. Generate YAML first.")


        elif view == "YAML":
            st.code(st.session_state.final_yaml, language="yaml")
            st.download_button("⬇️ Download YAML", st.session_state.final_yaml, file_name="workflow.yaml")


        st.markdown("---")
        if st.button("🚀 Deploy to Runtime"):
            with st.spinner("🚀 Deploying workflow to runtime... Please wait."):
                success, logs = run_pipeline_from_text(st.session_state.final_yaml)
            
            if success:
                st.success("✅ Workflow Deployed Successfully!")
            else:
                st.error("❌ Deployment Failed")
                st.code(logs)

