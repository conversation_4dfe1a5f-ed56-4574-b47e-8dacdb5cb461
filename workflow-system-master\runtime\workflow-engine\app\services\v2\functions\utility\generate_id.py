"""
V2 Generate ID Function

Utility function to generate unique IDs for entities with proper formatting and sequencing.
This replaces the problematic v1 generate_id function with standardized parameter handling.
"""

from typing import Dict, Any, Optional
import uuid
import datetime
from sqlalchemy.sql import text

from ...base_function import BaseSystemFunction
from ...models import SystemFunctionInput, FunctionExecutionContext, FunctionCategory

class GenerateIdFunction(BaseSystemFunction):
    """
    V2 Generate ID function with standardized input handling.
    
    This function generates unique IDs for entities using various strategies:
    - UUID generation
    - Sequential numbering
    - Custom format with prefixes
    - Timestamp-based IDs
    """
    
    def get_category(self) -> FunctionCategory:
        return FunctionCategory.UTILITY
    
    def get_required_inputs(self) -> list[str]:
        """Required inputs for generate_id function"""
        return ["entity"]  # Entity is required to determine ID format
    
    def get_optional_inputs(self) -> list[str]:
        """Optional inputs for generate_id function"""
        return [
            "attribute",         # Specific attribute for ID generation
            "id_type",           # Type of ID: uuid, sequential, custom, timestamp
            "prefix",            # Prefix for the ID
            "suffix",            # Suffix for the ID
            "format",            # Custom format string
            "length",            # Length of generated ID
            "sequence_table",    # Table to track sequences
            "sequence_column",   # Column for sequence tracking
            "tenant_id",         # For tenant-specific sequences
            "reset_frequency",   # daily, monthly, yearly, never
        ]
    
    def execute_function(self, input_data: SystemFunctionInput, context: FunctionExecutionContext) -> str:
        """
        Execute generate_id with flexible parameter handling.
        
        This function intelligently generates IDs based on entity and configuration
        without hardcoded entity-specific logic.
        """
        self.log_info("🔧 Starting generate_id execution", context)
        
        # Get entity (required)
        entity = self.get_input_value(input_data, "entity")
        if not entity:
            raise ValueError("Entity is required for ID generation")
        
        # Get ID generation parameters
        attribute = self.get_input_value(input_data, "attribute", "id")
        id_type = self.get_input_value(input_data, "id_type", "uuid")
        prefix = self.get_input_value(input_data, "prefix", "")
        suffix = self.get_input_value(input_data, "suffix", "")
        format_string = self.get_input_value(input_data, "format", "")
        length = self.get_input_value(input_data, "length", 8)
        tenant_id = self.get_input_value(input_data, "tenant_id", "")
        
        self.log_info(f"📋 Generating ID for entity: {entity}, attribute: {attribute}, type: {id_type}", context)
        
        # Generate ID based on type
        if id_type.lower() == "uuid":
            generated_id = self._generate_uuid_id(prefix, suffix, context)
        elif id_type.lower() == "sequential":
            generated_id = self._generate_sequential_id(entity, attribute, prefix, suffix, tenant_id, context)
        elif id_type.lower() == "timestamp":
            generated_id = self._generate_timestamp_id(prefix, suffix, context)
        elif id_type.lower() == "custom":
            generated_id = self._generate_custom_id(format_string, entity, attribute, context)
        elif id_type.lower() == "random":
            generated_id = self._generate_random_id(length, prefix, suffix, context)
        else:
            # Default to UUID if unknown type
            self.log_warning(f"Unknown ID type '{id_type}', defaulting to UUID", context)
            generated_id = self._generate_uuid_id(prefix, suffix, context)
        
        self.log_info(f"✅ Generated ID: {generated_id}", context)
        return generated_id
    
    def _generate_uuid_id(self, prefix: str, suffix: str, context: FunctionExecutionContext) -> str:
        """Generate UUID-based ID"""
        self.log_debug("Generating UUID-based ID", context)
        
        # Generate UUID and take first 8 characters for shorter IDs
        uuid_str = str(uuid.uuid4()).replace('-', '').upper()[:8]
        
        return f"{prefix}{uuid_str}{suffix}"
    
    def _generate_sequential_id(self, entity: str, attribute: str, prefix: str, suffix: str, tenant_id: str, context: FunctionExecutionContext) -> str:
        """Generate sequential ID by getting MAX value from entity table + 1"""
        self.log_debug(f"Generating sequential ID for {entity}.{attribute}", context)
        
        try:
            # Step 1: Get table name from entities table
            entity_query = """
            SELECT table_name 
            FROM workflow_runtime.entities 
            WHERE name = :entity_name
            """
            
            self.log_debug(f"Getting table name for entity: {entity}", context)
            entity_result = self.db.execute(text(entity_query), {"entity_name": entity}).fetchone()
            
            if not entity_result:
                raise ValueError(f"Entity '{entity}' not found in entities table")
            
            table_name = entity_result.table_name
            self.log_debug(f"Found table name: {table_name}", context)
            
            # Step 2: Get primary key column from entity_attributes table
            # First get entity_id
            entity_id_query = """
            SELECT entity_id 
            FROM workflow_runtime.entities 
            WHERE name = :entity_name
            """
            
            entity_id_result = self.db.execute(text(entity_id_query), {"entity_name": entity}).fetchone()
            if not entity_id_result:
                raise ValueError(f"Entity ID not found for entity '{entity}'")
            
            entity_id = entity_id_result.entity_id
            
            # Get primary key column
            pk_query = """
            SELECT name 
            FROM workflow_runtime.entity_attributes 
            WHERE entity_id = :entity_id AND is_primary_key = true
            """
            
            pk_result = self.db.execute(text(pk_query), {"entity_id": entity_id}).fetchone()
            if not pk_result:
                raise ValueError(f"Primary key not found for entity '{entity}'")
            
            pk_column = pk_result.name.lower()  # Convert to lowercase for database column
            self.log_debug(f"Found primary key column: {pk_column}", context)
            
            # Step 3: Get the highest existing ID from the actual entity table
            max_query = f"""
            SELECT {pk_column}
            FROM workflow_runtime.{table_name}
            WHERE {pk_column} LIKE :prefix_pattern
            ORDER BY {pk_column} DESC
            LIMIT 1
            """
            
            prefix_pattern = f"{prefix}%" if prefix else "%"
            self.log_debug(f"Executing max query: {max_query} with pattern: {prefix_pattern}", context)
            result = self.db.execute(text(max_query), {"prefix_pattern": prefix_pattern}).fetchone()
            
            if result and result[0]:
                # Extract numeric part from existing ID
                existing_id = result[0]
                self.log_debug(f"Found existing ID: {existing_id}", context)
                
                # Remove prefix and suffix to get numeric part
                numeric_part = existing_id
                if prefix:
                    numeric_part = numeric_part.replace(prefix, '', 1)
                if suffix:
                    numeric_part = numeric_part.replace(suffix, '')
                
                try:
                    next_num = int(numeric_part) + 1
                except ValueError:
                    # If we can't parse the number, start from 1
                    self.log_warning(f"Could not parse numeric part '{numeric_part}' from ID '{existing_id}', starting from 1", context)
                    next_num = 1
            else:
                # No existing records, start from 1
                self.log_debug("No existing records found, starting from 1", context)
                next_num = 1
            
            # Format with leading zeros (default 4 digits)
            formatted_num = str(next_num).zfill(4)
            
            generated_id = f"{prefix}{formatted_num}{suffix}"
            self.log_info(f"Generated sequential ID: {generated_id} (next number: {next_num})", context)
            
            return generated_id
            
        except Exception as e:
            self.log_error(f"Failed to generate sequential ID: {str(e)}", context)
            # Fallback: try to get a safe next number
            try:
                # Simple count + 1 as fallback
                fallback_query = "SELECT COUNT(*) FROM workflow_runtime.e1_leaveapplication"
                result = self.db.execute(text(fallback_query)).fetchone()
                next_num = (result[0] if result else 0) + 1
                formatted_num = str(next_num).zfill(4)
                self.log_warning(f"Using fallback ID generation: {prefix}{formatted_num}{suffix}", context)
            except:
                formatted_num = "0001"
                self.log_error("All ID generation methods failed, using default 0001", context)
            
            return f"{prefix}{formatted_num}{suffix}"
    
    def _generate_timestamp_id(self, prefix: str, suffix: str, context: FunctionExecutionContext) -> str:
        """Generate timestamp-based ID"""
        self.log_debug("Generating timestamp-based ID", context)
        
        # Use current timestamp in format YYYYMMDDHHMMSS
        timestamp = datetime.datetime.now().strftime("%Y%m%d%H%M%S")
        
        # Add microseconds for uniqueness
        microseconds = str(datetime.datetime.now().microsecond)[:3]
        
        return f"{prefix}{timestamp}{microseconds}{suffix}"
    
    def _generate_custom_id(self, format_string: str, entity: str, attribute: str, context: FunctionExecutionContext) -> str:
        """Generate ID using custom format string"""
        self.log_debug(f"Generating custom ID with format: {format_string}", context)
        
        if not format_string:
            # Default format if none provided
            format_string = "{entity}_{timestamp}_{random}"
        
        # Available placeholders
        placeholders = {
            "entity": entity.upper(),
            "attribute": attribute.upper(),
            "timestamp": datetime.datetime.now().strftime("%Y%m%d"),
            "time": datetime.datetime.now().strftime("%H%M%S"),
            "random": str(uuid.uuid4()).replace('-', '').upper()[:6],
            "uuid": str(uuid.uuid4()).replace('-', '').upper()[:8],
            "year": datetime.datetime.now().strftime("%Y"),
            "month": datetime.datetime.now().strftime("%m"),
            "day": datetime.datetime.now().strftime("%d"),
        }
        
        # Replace placeholders
        generated_id = format_string
        for placeholder, value in placeholders.items():
            generated_id = generated_id.replace(f"{{{placeholder}}}", value)
        
        return generated_id
    
    def _generate_random_id(self, length: int, prefix: str, suffix: str, context: FunctionExecutionContext) -> str:
        """Generate random alphanumeric ID"""
        self.log_debug(f"Generating random ID of length {length}", context)
        
        import random
        import string
        
        # Generate random alphanumeric string
        chars = string.ascii_uppercase + string.digits
        random_str = ''.join(random.choice(chars) for _ in range(length))
        
        return f"{prefix}{random_str}{suffix}"
