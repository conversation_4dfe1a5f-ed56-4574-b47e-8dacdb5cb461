{"timestamp": "2025-06-24T09:10:45.916640", "operation": "insert_tenant_to_workflow_runtime", "input_data": {"_id": "684e90ff74b35199a6b370c9", "id": 999, "tenant_id": "t999", "name": "Test Tenant", "natural_language": "Test tenant for debugging", "version": 1, "status": "draft", "created_at": "2025-06-15T09:23:11.004443", "updated_at": "2025-06-15T09:23:11.004451"}, "result": {"success": true, "inserted_id": 6, "schema": "workflow_runtime", "tenant_id": "t999", "name": "Test Tenant"}, "status": "success"}
{"timestamp": "2025-06-24T09:10:45.918820", "operation": "deploy_single_tenant_to_workflow_runtime", "input_data": {"_id": "684e90ff74b35199a6b370c9", "id": 999, "tenant_id": "t999", "name": "Test Tenant", "natural_language": "Test tenant for debugging", "version": 1, "status": "draft", "created_at": "2025-06-15T09:23:11.004443", "updated_at": "2025-06-15T09:23:11.004451"}, "result": {"success": true, "inserted_id": 6, "schema": "workflow_runtime", "tenant_id": "t999", "name": "Test Tenant"}, "status": "success"}
{"timestamp": "2025-06-24T09:18:47.334214", "operation": "deploy_single_tenant_to_workflow_temp", "input_data": {"tenant_id": "t888"}, "result": {"success": false, "error": "Tenant t888 not found with status draft", "tenant_id": "t888", "required_status": "draft"}, "status": "error"}
{"timestamp": "2025-06-24T09:19:14.850173", "operation": "deploy_single_tenant_to_workflow_temp", "input_data": {"tenant_id": "t888"}, "result": {"success": false, "error": "Tenant t888 not found with status draft", "tenant_id": "t888", "required_status": "draft"}, "status": "error"}
{"timestamp": "2025-06-24T09:20:09.853990", "operation": "deploy_single_tenant_to_workflow_temp", "input_data": {"tenant_id": "t777"}, "result": {"success": false, "error": "Tenant t777 not found with status draft", "tenant_id": "t777", "required_status": "draft"}, "status": "error"}
{"timestamp": "2025-06-24T09:20:43.630474", "operation": "deploy_single_tenant_to_workflow_temp", "input_data": {"tenant_id": "t777"}, "result": {"success": false, "error": "Tenant t777 not found with status draft", "tenant_id": "t777", "required_status": "draft"}, "status": "error"}
{"timestamp": "2025-06-24T09:22:00.859234", "operation": "insert_tenant_to_workflow_temp", "input_data": {"_id": "685a6d580ab469c89675f23f", "id": 7, "tenant_id": "t888", "name": "Temp Test Corp", "natural_language": "Tenant ID: t888\nTenant Name: Temp Test Corp", "created_at": "2025-06-24T09:18:16.954962", "created_by": null, "updated_at": "2025-06-24T09:18:16.954972", "updated_by": null, "version": 1, "status": "draft"}, "result": {"success": true, "inserted_id": 15, "schema": "workflow_temp", "tenant_id": "t888", "name": "Temp Test Corp"}, "status": "success"}
{"timestamp": "2025-06-24T09:22:00.875250", "operation": "insert_tenant_to_workflow_temp", "input_data": {"_id": "685a6db20ab469c89675f240", "id": 7, "tenant_id": "t777", "name": "Workflow Temp Corp", "natural_language": "Tenant ID: t777\nTenant Name: Workflow Temp Corp", "created_at": "2025-06-24T09:19:46.718045", "created_by": null, "updated_at": "2025-06-24T09:19:46.718058", "updated_by": null, "version": 1, "status": "draft"}, "result": {"success": true, "inserted_id": 16, "schema": "workflow_temp", "tenant_id": "t777", "name": "Workflow Temp Corp"}, "status": "success"}
{"timestamp": "2025-06-24T09:22:00.877397", "operation": "process_mongo_tenants_to_workflow_temp", "input_data": {"schema": "workflow_temp"}, "result": {"total_processed": 4, "successful_inserts": 2, "failed_inserts": 0, "details": [{"tenant_id": "T1007", "status": "skipped", "reason": "already_exists"}, {"tenant_id": "t999", "status": "skipped", "reason": "already_exists"}, {"tenant_id": "t888", "status": "success", "details": {"success": true, "inserted_id": 15, "schema": "workflow_temp", "tenant_id": "t888", "name": "Temp Test Corp"}}, {"tenant_id": "t777", "status": "success", "details": {"success": true, "inserted_id": 16, "schema": "workflow_temp", "tenant_id": "t777", "name": "Workflow Temp Corp"}}]}, "status": "success"}
