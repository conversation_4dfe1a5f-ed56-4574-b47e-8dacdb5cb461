import unittest
from unittest.mock import patch
from typing import Union
import sys
import os

# Add project root to path if needed
project_root = '/home/<USER>/workflow-system/runtime/workflow-engine'
if project_root not in sys.path:
    sys.path.insert(0, project_root)

# Import the function to test
from app.services.system_functions import format_currency

class TestFormatCurrency(unittest.TestCase):
    @patch('app.services.system_functions.number_format')
    def test_default_usd_formatting(self, mock_number_format):
        """Test default USD currency formatting."""
        # Setup mock
        mock_number_format.return_value = "1,234.56"
        
        # Execute
        result = format_currency(1234.56)
        
        # Verify
        self.assertEqual(result, "$1,234.56")
        mock_number_format.assert_called_once_with(1234.56, 2)
    
    @patch('app.services.system_functions.number_format')
    def test_eur_formatting(self, mock_number_format):
        """Test EUR currency formatting."""
        # Setup mock
        mock_number_format.return_value = "1,234.56"
        
        # Execute
        result = format_currency(1234.56, currency="EUR")
        
        # Verify
        self.assertEqual(result, "€1,234.56")
        mock_number_format.assert_called_once_with(1234.56, 2)
    
    @patch('app.services.system_functions.number_format')
    def test_gbp_formatting(self, mock_number_format):
        """Test GBP currency formatting."""
        # Setup mock
        mock_number_format.return_value = "1,234.56"
        
        # Execute
        result = format_currency(1234.56, currency="GBP")
        
        # Verify
        self.assertEqual(result, "£1,234.56")
        mock_number_format.assert_called_once_with(1234.56, 2)
    
    @patch('app.services.system_functions.number_format')
    def test_jpy_formatting(self, mock_number_format):
        """Test JPY currency formatting."""
        # Setup mock
        mock_number_format.return_value = "1,235"  # JPY typically has 0 decimal places
        
        # Execute
        result = format_currency(1234.56, currency="JPY", decimal_places=0)
        
        # Verify
        self.assertEqual(result, "¥1,235")
        mock_number_format.assert_called_once_with(1234.56, 0)
    
    @patch('app.services.system_functions.number_format')
    def test_cny_formatting(self, mock_number_format):
        """Test CNY currency formatting."""
        # Setup mock
        mock_number_format.return_value = "1,234.56"
        
        # Execute
        result = format_currency(1234.56, currency="CNY")
        
        # Verify
        self.assertEqual(result, "¥1,234.56")
        mock_number_format.assert_called_once_with(1234.56, 2)
    
    @patch('app.services.system_functions.number_format')
    def test_inr_formatting(self, mock_number_format):
        """Test INR currency formatting."""
        # Setup mock
        mock_number_format.return_value = "1,234.56"
        
        # Execute
        result = format_currency(1234.56, currency="INR")
        
        # Verify
        self.assertEqual(result, "₹1,234.56")
        mock_number_format.assert_called_once_with(1234.56, 2)
    
    @patch('app.services.system_functions.number_format')
    def test_cad_formatting(self, mock_number_format):
        """Test CAD currency formatting."""
        # Setup mock
        mock_number_format.return_value = "1,234.56"
        
        # Execute
        result = format_currency(1234.56, currency="CAD")
        
        # Verify
        self.assertEqual(result, "CA$1,234.56")
        mock_number_format.assert_called_once_with(1234.56, 2)
    
    @patch('app.services.system_functions.number_format')
    def test_aud_formatting(self, mock_number_format):
        """Test AUD currency formatting."""
        # Setup mock
        mock_number_format.return_value = "1,234.56"
        
        # Execute
        result = format_currency(1234.56, currency="AUD")
        
        # Verify
        self.assertEqual(result, "A$1,234.56")
        mock_number_format.assert_called_once_with(1234.56, 2)
    
    @patch('app.services.system_functions.number_format')
    def test_brl_formatting(self, mock_number_format):
        """Test BRL currency formatting."""
        # Setup mock
        mock_number_format.return_value = "1,234.56"
        
        # Execute
        result = format_currency(1234.56, currency="BRL")
        
        # Verify
        self.assertEqual(result, "R$1,234.56")
        mock_number_format.assert_called_once_with(1234.56, 2)
    
    @patch('app.services.system_functions.number_format')
    def test_unknown_currency_formatting(self, mock_number_format):
        """Test formatting with unknown currency code."""
        # Setup mock
        mock_number_format.return_value = "1,234.56"
        
        # Execute
        result = format_currency(1234.56, currency="XYZ")
        
        # Verify - unknown currencies should be suffixed
        self.assertEqual(result, "1,234.56 XYZ")
        mock_number_format.assert_called_once_with(1234.56, 2)
    
    @patch('app.services.system_functions.number_format')
    def test_custom_decimal_places(self, mock_number_format):
        """Test formatting with custom decimal places."""
        # Setup mock
        mock_number_format.return_value = "1,234.567"
        
        # Execute
        result = format_currency(1234.567, decimal_places=3)
        
        # Verify
        self.assertEqual(result, "$1,234.567")
        mock_number_format.assert_called_once_with(1234.567, 3)
    
    @patch('app.services.system_functions.number_format')
    def test_zero_decimal_places(self, mock_number_format):
        """Test formatting with zero decimal places."""
        # Setup mock
        mock_number_format.return_value = "1,235"
        
        # Execute
        result = format_currency(1234.56, decimal_places=0)
        
        # Verify
        self.assertEqual(result, "$1,235")
        mock_number_format.assert_called_once_with(1234.56, 0)
    
    @patch('app.services.system_functions.number_format')
    def test_negative_amount(self, mock_number_format):
        """Test formatting with negative amount."""
        # Setup mock
        mock_number_format.return_value = "-1,234.56"
        
        # Execute
        result = format_currency(-1234.56)
        
        # Verify
        self.assertEqual(result, "$-1,234.56")
        mock_number_format.assert_called_once_with(-1234.56, 2)
    
    @patch('app.services.system_functions.number_format')
    def test_zero_amount(self, mock_number_format):
        """Test formatting with zero amount."""
        # Setup mock
        mock_number_format.return_value = "0.00"
        
        # Execute
        result = format_currency(0)
        
        # Verify
        self.assertEqual(result, "$0.00")
        mock_number_format.assert_called_once_with(0, 2)
    
    def test_integration_without_mocking(self):
        """Test actual integration without mocking."""
        # Test USD formatting
        result = format_currency(1234.56)
        self.assertEqual(result, "$1,234.56")
        
        # Test EUR formatting
        result = format_currency(1234.56, currency="EUR")
        self.assertEqual(result, "€1,234.56")
        
        # Test unknown currency
        result = format_currency(1234.56, currency="XYZ")
        self.assertEqual(result, "1,234.56 XYZ")
        
        # Test zero decimal places
        result = format_currency(1234.56, decimal_places=0)
        self.assertEqual(result, "$1,235")  # Rounded to nearest integer

if __name__ == '__main__':
    unittest.main()