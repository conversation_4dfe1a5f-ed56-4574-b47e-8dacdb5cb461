from typing import Dict, List, Tuple, Any
import logging

class BaseParser:
    """
    Base class for all prescriptive text parsers.
    """
    
    def __init__(self):
        self.logger = logging.getLogger(self.__class__.__name__)
    
    def parse(self, prescriptive_text: str) -> Tuple[Dict[str, Any], List[str]]:
        """
        Parse prescriptive text into structured data.
        
        Args:
            prescriptive_text: Prescriptive text to parse
            
        Returns:
            Tuple containing:
                - Structured data dictionary
                - List of warning messages (empty if no warnings)
        """
        raise NotImplementedError("Subclasses must implement parse method")
    
    def validate(self, parsed_data: Dict[str, Any]) -> List[str]:
        """
        Validate parsed data.
        
        Args:
            parsed_data: Parsed data to validate
            
        Returns:
            List of validation error messages (empty if no errors)
        """
        raise NotImplementedError("Subclasses must implement validate method")