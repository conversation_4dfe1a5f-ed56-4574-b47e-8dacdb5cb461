---
# Enterprise Workflow YAML Configuration - Validated Positive Example
tenant:
  id: company1
  name: Example Company
  description: Enterprise workflow system
  version: "1.0.0"
  status: active
  roles:
    - id: admin
      name: Administrator
      description: System administrator with full access
      access:
        entities:
          - entity_id: e001
            permissions: [create, read, update, delete]
        objectives:
          - objective_id: go001.lo001
            permissions: [execute, view, edit]
          - objective_id: go001.lo002
            permissions: [execute, view, edit]
    
    - id: user
      name: Regular User
      description: Standard user with limited access
      access:
        entities:
          - entity_id: e001
            permissions: [read]
        objectives:
          - objective_id: go001.lo001
            permissions: [execute]
          - objective_id: go001.lo002
            permissions: [execute]

workflow_data:
  id: wd001
  name: Approval Workflow
  description: Process for approving requests
  version: "1.0.0"
  status: active

permission_types:
  - id: create
    name: Create
    description: Permission to create new records
    capabilities: [add]
  
  - id: read
    name: Read
    description: Permission to read records
    capabilities: [view]
  
  - id: update
    name: Update
    description: Permission to update existing records
    capabilities: [edit, modify]
  
  - id: delete
    name: Delete
    description: Permission to delete records
    capabilities: [remove]
  
  - id: execute
    name: Execute
    description: Permission to execute workflows
    capabilities: [run, invoke]
  
  - id: view
    name: View
    description: Permission to view workflow details
    capabilities: [display]
  
  - id: edit
    name: Edit
    description: Permission to edit workflow configuration
    capabilities: [modify]

entities:
  - id: e001
    name: request
    display_name: Request
    type: data
    version: "1.0.0"
    status: active
    description: Request data
    attributes_metadata:
      attribute_prefix: req
      attribute_map:
        at101: requestId
        at102: title
        at103: description
        at104: status
        at105: userId
      required_attributes: [at101, at102, at104, at105]
    attributes:
      - id: at101
        name: requestId
        display_name: Request ID
        description: Unique identifier for requests
        datatype: string
        required: true
        default_value: null
        version: "1.0.0"
        status: active
      
      - id: at102
        name: title
        display_name: Title
        description: Request title
        datatype: string
        required: true
        default_value: null
        version: "1.0.0"
        status: active
      
      - id: at103
        name: description
        display_name: Description
        description: Request description
        datatype: string
        required: false
        default_value: null
        version: "1.0.0"
        status: active
      
      - id: at104
        name: status
        display_name: Status
        description: Request status
        datatype: enum
        required: true
        values: [draft, submitted, approved, rejected]
        default_value: "draft"
        version: "1.0.0"
        status: active
      
      - id: at105
        name: userId
        display_name: User ID
        description: ID of the person who created the request
        datatype: string
        required: true
        default_value: null
        version: "1.0.0"
        status: active
  
  - id: e002
    name: user
    display_name: User
    type: data
    version: "1.0.0"
    status: active
    description: User data
    attributes_metadata:
      attribute_prefix: usr
      attribute_map:
        at101: userId
        at102: username
        at103: email
      required_attributes: [at101, at102, at103]
    attributes:
      - id: at101
        name: userId
        display_name: User ID
        description: Unique identifier for users
        datatype: string
        required: true
        default_value: null
        version: "1.0.0"
        status: active
      
      - id: at102
        name: username
        display_name: Username
        description: User's username
        datatype: string
        required: true
        default_value: null
        version: "1.0.0"
        status: active
      
      - id: at103
        name: email
        display_name: Email
        description: User's email address
        datatype: string
        required: true
        default_value: null
        version: "1.0.0"
        status: active

global_objectives:
  - id: go001
    name: requestApproval
    display_name: Request Approval
    description: Process to approve requests
    version: "1.0.0"
    status: active

local_objectives:
  - id: lo001
    contextual_id: go001.lo001
    name: createRequest
    display_name: Create Request
    description: Create a new request in the system
    workflow_source: origin
    function_type: create
    version: "1.0.0"
    status: active
    
    agent_stack:
      agents:
        - role: admin
          rights: [execute]
        - role: user
          rights: [execute]
    
    input_stack:
      inputs:
        - id: in001
          slot_id: e001.at102.in001
          contextual_id: go001.lo001.in001
          display_name: Title
          description: Title of the request
          source:
            type: user
            description: Provided by the user
          required: true
          data_type: string
          ui_control: oj-input-text
          metadata:
            usage: update
        
        - id: in002
          slot_id: e001.at103.in002
          contextual_id: go001.lo001.in002
          display_name: Description
          description: Description of the request
          source:
            type: user
            description: Provided by the user
          required: false
          data_type: string
          ui_control: oj-text-area
          metadata:
            usage: update
        
        - id: in003
          slot_id: e001.at101.in003
          contextual_id: go001.lo001.in003
          display_name: Request ID
          description: Auto-generated request ID
          source:
            type: system
            description: Generated by the system
          required: true
          data_type: string
          ui_control: oj-input-text
          nested_function:
            function_name: generate_id
            parameters:
              entity: e001
              attribute: at101
              prefix: REQ
            output_to: at101
          metadata:
            usage: update
        
        - id: in004
          slot_id: e001.at104.in004
          contextual_id: go001.lo001.in004
          display_name: Status
          description: Initial request status
          source:
            type: system
            description: Default value from system
          required: true
          data_type: enum
          ui_control: oj-select-single
          default_value: "draft"
          nested_function:
            function_name: fetch_enum_values
            parameters:
              entity_id: e001
              attribute_id: at104
            output_to: at104
          metadata:
            usage: update
        
        - id: in005
          slot_id: e001.at105.in005
          contextual_id: go001.lo001.in005
          display_name: User ID
          description: ID of the current user
          source:
            type: system
            description: Current user ID
          required: true
          data_type: string
          ui_control: oj-input-text
          nested_function:
            function_name: current_timestamp
            parameters: {}
            output_to: at105
          metadata:
            usage: update
        
        - id: in999
          slot_id: executionstatus.in999
          contextual_id: go001.lo001.in999
          display_name: Execution Status
          description: Status of the current execution
          source:
            type: system
            description: System execution status
          required: true
          data_type: string
          ui_control: oj-bind-text
          nested_function:
            function_name: notify
            parameters:
              message: "Creating new request"
            output_to: at999
          metadata:
            usage: ""
    
    output_stack:
      outputs:
        - id: out001
          slot_id: e001.at102.out001
          contextual_id: go001.lo001.out001
          display_name: Title
          description: Title of the request
          data_type: string
          ui_control: oj-bind-text
        
        - id: out002
          slot_id: e001.at103.out002
          contextual_id: go001.lo001.out002
          display_name: Description
          description: Description of the request
          data_type: string
          ui_control: oj-bind-text
        
        - id: out003
          slot_id: e001.at101.out003
          contextual_id: go001.lo001.out003
          display_name: Request ID
          description: Auto-generated request ID
          data_type: string
          ui_control: oj-bind-text
        
        - id: out004
          slot_id: e001.at104.out004
          contextual_id: go001.lo001.out004
          display_name: Status
          description: Initial request status
          data_type: enum
          ui_control: oj-bind-text
        
        - id: out005
          slot_id: e001.at105.out005
          contextual_id: go001.lo001.out005
          display_name: User ID
          description: ID of the user making the request
          data_type: string
          ui_control: oj-bind-text
        
        - id: out999
          slot_id: executionstatus.out999
          contextual_id: go001.lo001.out999
          display_name: Execution Status
          description: Status of the current execution
          data_type: string
          ui_control: oj-bind-text
    
    data_mapping_stack:
      mappings:
        - id: map001
          source: go001.lo001.in001
          target: go001.lo001.out001
        
        - id: map002
          source: go001.lo001.in002
          target: go001.lo001.out002
        
        - id: map003
          source: go001.lo001.in003
          target: go001.lo001.out003
        
        - id: map004
          source: go001.lo001.in004
          target: go001.lo001.out004
        
        - id: map005
          source: go001.lo001.in005
          target: go001.lo001.out005
        
        - id: map999
          source: go001.lo001.in999
          target: go001.lo001.out999
    
    execution_pathway:
      type: sequential
      next_lo: lo002
  
  - id: lo002
    contextual_id: go001.lo002
    name: approveRequest
    display_name: Approve Request
    description: Approve or reject the request
    workflow_source: terminal
    function_type: update
    version: "1.0.0"
    status: active
    
    agent_stack:
      agents:
        - role: admin
          rights: [execute]
    
    input_stack:
      inputs:
        - id: in001
          slot_id: e001.at101.in001
          contextual_id: go001.lo002.in001
          display_name: Request ID
          description: Request ID for reference
          source:
            type: system
            description: From previous step
          required: true
          data_type: string
          ui_control: oj-input-text
          nested_function:
            function_name: fetch
            parameters:
              table: e001
              filters:
                id: "{{go001.lo001.out003}}"
            output_to: at101
          metadata:
            usage: lookup
        
        - id: in002
          slot_id: e001.at102.in002
          contextual_id: go001.lo002.in002
          display_name: Title
          description: Title of the request
          source:
            type: system
            description: From previous step
          required: true
          data_type: string
          ui_control: oj-input-text
          nested_function:
            function_name: fetch
            parameters:
              table: e001
              filters:
                id: "{{go001.lo001.out003}}"
            output_to: at102
          metadata:
            usage: lookup
        
        - id: in003
          slot_id: e001.at104.in003
          contextual_id: go001.lo002.in003
          display_name: Status
          description: Request status to update
          source:
            type: user
            description: Selected by the user
          required: true
          data_type: enum
          ui_control: oj-select-single
          allowed_values: ["approved", "rejected"]
          metadata:
            usage: update
        
        - id: in999
          slot_id: executionstatus.in999
          contextual_id: go001.lo002.in999
          display_name: Execution Status
          description: Status of the current execution
          source:
            type: system
            description: System execution status
          required: true
          data_type: string
          ui_control: oj-bind-text
          nested_function:
            function_name: notify
            parameters:
              message: "Updating request status"
            output_to: at999
          metadata:
            usage: ""
    
    output_stack:
      outputs:
        - id: out001
          slot_id: e001.at101.out001
          contextual_id: go001.lo002.out001
          display_name: Request ID
          description: Request ID for reference
          data_type: string
          ui_control: oj-bind-text
        
        - id: out002
          slot_id: e001.at102.out002
          contextual_id: go001.lo002.out002
          display_name: Title
          description: Title of the request
          data_type: string
          ui_control: oj-bind-text
        
        - id: out003
          slot_id: e001.at104.out003
          contextual_id: go001.lo002.out003
          display_name: Status
          description: Updated request status
          data_type: enum
          ui_control: oj-bind-text
        
        - id: out999
          slot_id: executionstatus.out999
          contextual_id: go001.lo002.out999
          display_name: Execution Status
          description: Status of the current execution
          data_type: string
          ui_control: oj-bind-text
    
    data_mapping_stack:
      mappings:
        - id: map001
          source: go001.lo001.out003  # Request ID from previous step
          target: go001.lo002.in001
        
        - id: map002
          source: go001.lo001.out001  # Title from previous step
          target: go001.lo002.in002
        
        - id: map003
          source: go001.lo002.in001
          target: go001.lo002.out001
        
        - id: map004
          source: go001.lo002.in002
          target: go001.lo002.out002
        
        - id: map005
          source: go001.lo002.in003
          target: go001.lo002.out003
        
        - id: map999
          source: go001.lo002.in999
          target: go001.lo002.out999
    
    execution_pathway:
      type: terminal