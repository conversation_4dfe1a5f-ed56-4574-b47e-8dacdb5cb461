#!/usr/bin/env python3

"""
This script fixes the issue with duplicate input IDs in the database.
It identifies duplicate input IDs and updates them to be unique.
"""

import psycopg2
from datetime import datetime
import os
import traceback

# Set log path to current working directory
LOG_PATH = os.path.join(
    os.path.dirname(os.path.abspath(__file__)),
    f"fix_duplicate_ids_log_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
)

# Logging function
def log(msg):
    with open(LOG_PATH, "a") as f:
        f.write(f"{datetime.now().strftime('%Y-%m-%d %H:%M:%S')} | {msg}\n")
    print(msg)

# Database configuration
PG_CONFIG = {
    "dbname": "workflow_system",
    "user": "postgres",
    "password": "workflow_postgres_secure_password",
    "host": "**********",
    "port": "5432"
}

def connect_to_db():
    """Connect to the PostgreSQL database."""
    try:
        conn = psycopg2.connect(**PG_CONFIG)
        log("✅ Connected to PostgreSQL database")
        return conn
    except Exception as e:
        log(f"❌ Error connecting to database: {e}")
        raise

def fix_duplicate_input_ids():
    """Fix duplicate input IDs in the lo_input_items table."""
    conn = connect_to_db()
    cursor = conn.cursor()
    
    try:
        # Set search path to the workflow_runtime schema
        cursor.execute("SET search_path TO workflow_runtime")
        
        # Find duplicate input IDs
        log("🔍 Finding duplicate input IDs...")
        cursor.execute("""
            SELECT id, COUNT(*) as count
            FROM lo_input_items
            GROUP BY id
            HAVING COUNT(*) > 1
            ORDER BY count DESC
        """)
        
        duplicates = cursor.fetchall()
        
        if not duplicates:
            log("✅ No duplicate input IDs found")
            return
        
        log(f"⚠️ Found {len(duplicates)} duplicate input IDs")
        
        # Process each duplicate ID
        for dup_id, count in duplicates:
            log(f"🔧 Processing duplicate ID: {dup_id} (appears {count} times)")
            
            # Get all instances of this ID
            cursor.execute("""
                SELECT id, input_stack_id, lo_id, contextual_id
                FROM lo_input_items
                WHERE id = %s
                ORDER BY input_stack_id
            """, (dup_id,))
            
            instances = cursor.fetchall()
            
            # Keep the first instance as is, update the rest
            for i, (id, input_stack_id, lo_id, contextual_id) in enumerate(instances[1:], 1):
                # Generate a new unique ID
                new_id = f"{dup_id}_{i}"
                
                log(f"🔄 Updating ID {dup_id} to {new_id} for input_stack_id={input_stack_id}, lo_id={lo_id}")
                
                try:
                    # Start a transaction for this update
                    cursor.execute("BEGIN")
                    
                    # First, create a new row with the new ID
                    cursor.execute("""
                        INSERT INTO lo_input_items
                        (id, input_stack_id, slot_id, contextual_id, source_type, source_description,
                         required, ui_control, metadata, dependencies, dependency_type, is_visible,
                         created_at, updated_at, lo_id, data_type, nested_function, nested_functions, lookup_function)
                        SELECT %s, input_stack_id, slot_id, contextual_id, source_type, source_description,
                               required, ui_control, metadata, dependencies, dependency_type, is_visible,
                               created_at, updated_at, lo_id, data_type, nested_function, nested_functions, lookup_function
                        FROM lo_input_items
                        WHERE id = %s AND input_stack_id = %s
                    """, (new_id, dup_id, input_stack_id))
                    
                    # Update contextual_id to reflect the new ID if needed
                    if contextual_id and contextual_id.endswith(dup_id):
                        new_contextual_id = contextual_id.replace(dup_id, new_id)
                        cursor.execute("""
                            UPDATE lo_input_items
                            SET contextual_id = %s
                            WHERE id = %s AND input_stack_id = %s
                        """, (new_contextual_id, new_id, input_stack_id))
                        
                        # Update any references in lo_nested_functions
                        cursor.execute("""
                            UPDATE lo_nested_functions
                            SET input_contextual_id = %s
                            WHERE input_contextual_id = %s
                        """, (new_contextual_id, contextual_id))
                    
                    # Update any references in input_dependencies where this is the depends_on_id
                    cursor.execute("""
                        UPDATE input_dependencies
                        SET depends_on_id = %s
                        WHERE depends_on_id = %s AND depends_on_stack_id = %s
                    """, (new_id, dup_id, input_stack_id))
                    
                    # Update any references in input_dependencies where this is the input_item_id
                    cursor.execute("""
                        UPDATE input_dependencies
                        SET input_item_id = %s
                        WHERE input_item_id = %s AND input_stack_id = %s
                    """, (new_id, dup_id, input_stack_id))
                    
                    # Update any references in dropdown_data_sources
                    cursor.execute("""
                        UPDATE dropdown_data_sources
                        SET input_item_id = %s
                        WHERE input_item_id = %s AND input_stack_id = %s
                    """, (new_id, dup_id, input_stack_id))
                    
                    # Copy any validations to the new ID
                    cursor.execute("""
                        INSERT INTO lo_input_validations
                        (input_item_id, input_stack_id, rule, rule_type, validation_method, allowed_values,
                         entity, attribute, error_message, created_at, updated_at)
                        SELECT %s, input_stack_id, rule, rule_type, validation_method, allowed_values,
                               entity, attribute, error_message, created_at, updated_at
                        FROM lo_input_validations
                        WHERE input_item_id = %s AND input_stack_id = %s
                    """, (new_id, dup_id, input_stack_id))
                    
                    # Now delete the old row
                    cursor.execute("""
                        DELETE FROM lo_input_validations
                        WHERE input_item_id = %s AND input_stack_id = %s
                    """, (dup_id, input_stack_id))
                    
                    cursor.execute("""
                        DELETE FROM lo_input_items
                        WHERE id = %s AND input_stack_id = %s
                    """, (dup_id, input_stack_id))
                    
                    # Commit the transaction
                    cursor.execute("COMMIT")
                    log(f"✅ Successfully updated ID {dup_id} to {new_id}")
                    
                except Exception as e:
                    # Rollback in case of error
                    cursor.execute("ROLLBACK")
                    log(f"❌ Error updating ID {dup_id} to {new_id}: {e}")
                    log(traceback.format_exc())
            
        log("✅ All duplicate input IDs have been fixed")
        
    except Exception as e:
        if conn:
            conn.rollback()
        log(f"❌ Error fixing duplicate input IDs: {e}")
        log(traceback.format_exc())
    finally:
        if cursor:
            cursor.close()
        if conn:
            conn.close()

if __name__ == "__main__":
    fix_duplicate_input_ids()
