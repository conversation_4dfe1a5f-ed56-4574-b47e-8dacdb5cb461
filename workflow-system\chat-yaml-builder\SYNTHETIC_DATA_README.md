# Synthetic Data Generation for YAML Deployments

This document explains how to use the synthetic data generation feature in YAML deployments. This feature allows you to define synthetic data directly in your YAML files, which will be automatically generated and inserted into the database during entity deployment.

## Overview

The synthetic data generation feature is particularly useful for:

1. Pre-populating lookup tables and reference data
2. Setting up dependent dropdown options
3. Creating test data for development and testing
4. Ensuring that required data is available when the application starts

## How It Works

The synthetic data generation is integrated into the deployment pipeline. When a YAML file is processed:

1. The YAML is validated and enriched with IDs
2. Entity tables are created in the database
3. Synthetic data defined in the YAML is extracted and inserted into the appropriate tables
4. The deployment continues with the rest of the pipeline

## Defining Synthetic Data in YAML

To define synthetic data for an entity, add a `synthetic_records` array to the entity definition in your YAML file. Each item in the array represents a record to be inserted into the database.

### Example

```yaml
- id: "e002"
  name: "LeaveSubType"
  type: "Master"
  version: "1.0"
  status: "Active"
  attributes_metadata:
    attribute_prefix: "at"
    attribute_map:
      "at201": "leaveType"
      "at202": "subTypeId"
      "at203": "subTypeName"
      "at204": "active"
  attributes:
    - id: "at201"
      name: "leaveType"
      display_name: "Leave Type"
      datatype: "String"
      required: true
    - id: "at202"
      name: "subTypeId"
      display_name: "Sub Type ID"
      datatype: "String"
      required: true
    - id: "at203"
      name: "subTypeName"
      display_name: "Sub Type Name"
      datatype: "String"
      required: true
    - id: "at204"
      name: "active"
      display_name: "Active"
      datatype: "Boolean"
      required: false
  # Synthetic data definition
  synthetic_records:
    - at201: "Annual Leave"
      at202: "AL001"
      at203: "Standard Annual Leave"
      at204: true
    - at201: "Annual Leave"
      at202: "AL002"
      at203: "Carry-over Leave"
      at204: true
    - at201: "Sick Leave"
      at202: "SL001"
      at203: "Short-term Illness"
      at204: true
```

## Field Mapping

When defining synthetic records, use the attribute IDs as field names. The system will automatically map these to the correct column names in the database.

For example, if you have an attribute with:
- `id: "at201"`
- `name: "leaveType"`

Then in your synthetic record, use `at201` as the field name:

```yaml
synthetic_records:
  - at201: "Annual Leave"
    at202: "AL001"
    at203: "Standard Annual Leave"
```

This will be inserted into the database with the correct column names based on the attribute mapping.

## Data Types

The system supports various data types for synthetic records:

- **String**: Use quotes for string values
- **Number/Integer**: Use numeric values without quotes
- **Boolean**: Use `true` or `false` without quotes
- **Date**: Use ISO format strings (YYYY-MM-DD)
- **Enum**: Use one of the allowed values defined in the attribute

## Use Cases

### Dependent Dropdowns

One common use case is to populate data for dependent dropdowns. For example, in a leave management system, you might have a "Leave Type" dropdown and a "Leave Sub-Type" dropdown that depends on the selected Leave Type.

By defining synthetic data for the LeaveSubType entity, you ensure that when a user selects a Leave Type, the appropriate Sub-Types are available in the dependent dropdown.

### Reference Data

Another common use case is to populate reference data that is required by the application, such as:

- Status codes
- Category lists
- Configuration settings
- Default values

## Best Practices

1. **Keep synthetic data minimal**: Only include the data that is necessary for the application to function correctly.
2. **Use meaningful IDs**: Make sure your synthetic data has meaningful IDs that are easy to reference.
3. **Maintain data integrity**: Ensure that your synthetic data maintains referential integrity with other entities.
4. **Document your synthetic data**: Add comments to explain the purpose of each synthetic record.
5. **Test your synthetic data**: Verify that your synthetic data is correctly inserted and can be used by the application.

## Troubleshooting

If you encounter issues with synthetic data generation:

1. Check the logs for error messages
2. Verify that your attribute IDs match the ones in your synthetic records
3. Ensure that your data types match the attribute definitions
4. Check for duplicate records or unique constraint violations
