"""
Authentication Routes for the Workflow System.

This module provides API endpoints for authentication:
- Login endpoint
- Token refresh endpoint
- Logout endpoint
- User registration endpoint
"""

from fastapi import APIRouter, Depends, HTTPException, status, Request, Response, Body
from fastapi.security import OAuth2Pass<PERSON>RequestForm
from sqlalchemy.orm import Session

from app.db.session import get_db
from app.auth.auth_service import AuthService, User, UserCreate, Token
from app.auth.auth_middleware import get_security_context, require_auth, SecurityContext

# Create router
router = APIRouter(
    prefix="/auth",
    tags=["authentication"],
    responses={401: {"description": "Unauthorized"}},
)

@router.post("/token", response_model=Token)
async def login_for_access_token(
    request: Request,
    form_data: OAuth2PasswordRequestForm = Depends(),
    db: Session = Depends(get_db)
):
    """
    OAuth2 compatible token login, get an access token for future requests.
    
    Args:
        request: FastAPI request
        form_data: OAuth2 form data with username and password
        db: Database session
        
    Returns:
        Token: Access and refresh tokens
        
    Raises:
        HTTPException: If authentication fails
    """
    auth_service = AuthService(db)
    user = auth_service.authenticate_user(form_data.username, form_data.password)
    
    if not user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Incorrect username or password",
            headers={"WWW-Authenticate": "Bearer"},
        )
        
    # Create token
    token = auth_service.create_token(user)
    
    # Create session
    auth_service.create_session(user.user_id, token.access_token, request)
    
    return token

@router.post("/refresh", response_model=Token)
async def refresh_access_token(
    request: Request,
    refresh_token: str = Body(..., embed=True),
    db: Session = Depends(get_db)
):
    """
    Refresh access token using refresh token.
    
    Args:
        request: FastAPI request
        refresh_token: Refresh token
        db: Database session
        
    Returns:
        Token: New access and refresh tokens
        
    Raises:
        HTTPException: If refresh token is invalid
    """
    auth_service = AuthService(db)
    token = auth_service.refresh_token(refresh_token)
    
    if not token:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid refresh token",
            headers={"WWW-Authenticate": "Bearer"},
        )
        
    # Get user ID from token
    payload = auth_service.decode_token(token.access_token)
    user_id = payload.get("sub")
    
    # Create session
    auth_service.create_session(user_id, token.access_token, request)
    
    return token

@router.post("/logout", status_code=status.HTTP_204_NO_CONTENT)
async def logout(
    request: Request,
    response: Response,
    security_context: SecurityContext = Depends(require_auth),
    db: Session = Depends(get_db)
):
    """
    Logout user by revoking token and ending session.
    
    Args:
        request: FastAPI request
        response: FastAPI response
        security_context: Security context
        db: Database session
        
    Returns:
        None
    """
    auth_service = AuthService(db)
    
    # Revoke token
    if security_context.token:
        auth_service.revoke_token(security_context.token)
    
    # End session
    if security_context.session_id:
        auth_service.end_session(security_context.session_id)
    
    # Clear cookie
    response.delete_cookie("access_token")
    
    return None

@router.post("/register", response_model=User, status_code=status.HTTP_201_CREATED)
async def register_user(
    user: UserCreate,
    db: Session = Depends(get_db)
):
    """
    Register a new user.
    
    Args:
        user: User data
        db: Database session
        
    Returns:
        User: Created user
        
    Raises:
        HTTPException: If user already exists
    """
    auth_service = AuthService(db)
    created_user = auth_service.create_user(user)
    
    if not created_user:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="User with this username or email already exists",
        )
        
    return created_user

@router.get("/me", response_model=User)
async def get_current_user(
    security_context: SecurityContext = Depends(require_auth),
    db: Session = Depends(get_db)
):
    """
    Get current user information.
    
    Args:
        security_context: Security context
        db: Database session
        
    Returns:
        User: Current user
    """
    auth_service = AuthService(db)
    user = auth_service.get_user(security_context.username)
    
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="User not found",
        )
        
    return User(
        user_id=user.user_id,
        username=user.username,
        email=user.email,
        first_name=user.first_name,
        last_name=user.last_name,
        status=user.status,
        roles=user.roles,
        org_units=user.org_units,
        tenant_id=user.tenant_id,
        disabled=user.disabled
    )

@router.post("/logout-all", status_code=status.HTTP_204_NO_CONTENT)
async def logout_all_sessions(
    security_context: SecurityContext = Depends(require_auth),
    db: Session = Depends(get_db)
):
    """
    Logout user from all sessions.
    
    Args:
        security_context: Security context
        db: Database session
        
    Returns:
        None
    """
    auth_service = AuthService(db)
    
    # End all user sessions
    auth_service.end_all_user_sessions(security_context.user_id)
    
    return None
