{"timestamp": "2025-06-24T09:14:47.981909", "operation": "deploy_single_role_to_workflow_runtime", "input_data": {"role_id": "R12"}, "result": {"success": false, "error": "Role R12 not found with status deployed_to_temp or draft", "role_id": "R12", "required_status": "deployed_to_temp or draft"}, "status": "error"}
{"timestamp": "2025-06-24T09:15:12.421505", "operation": "deploy_single_role_to_workflow_runtime", "input_data": {"role_id": "R12"}, "result": {"success": false, "error": "Role R12 not found with status deployed_to_temp or draft", "role_id": "R12", "required_status": "deployed_to_temp or draft"}, "status": "error"}
{"timestamp": "2025-06-24T09:24:02.521777", "operation": "insert_role_to_workflow_temp", "input_data": {"_id": "6854391977ff47ee3c5f402b", "id": 8, "role_id": "R_MarketingManager_20250619162145", "name": "Marketing Manager", "description": "Manages marketing campaigns and team", "tenant_id": "T1001", "reports_to_role_id": null, "organizational_level": null, "department_id": 2, "natural_language": "Role Name: Marketing Manager\nDescription: Manages marketing campaigns and team\nTenant: T1001\nDepartment: Marketing", "created_at": "2025-06-19T16:21:45.442150", "created_by": null, "updated_at": "2025-06-19T16:21:45.442161", "updated_by": null, "version": 1, "status": "draft"}, "result": {"success": false, "error": "Error inserting role to workflow_temp: insert or update on table \"roles\" violates foreign key constraint \"fk_roles_department\"\nDETAIL:  Key (department_id)=(2) is not present in table \"departments\".\n", "schema": "workflow_temp", "role_id": "R_MarketingManager_20250619162145", "name": "Marketing Manager", "tenant_id": "T1001"}, "status": "error"}
{"timestamp": "2025-06-24T09:24:02.537631", "operation": "insert_role_to_workflow_temp", "input_data": {"_id": "68543be4fc1c96b5e83de3d2", "id": 8, "role_id": "R11", "name": "Marketing Manager", "description": "Manages marketing campaigns and team", "tenant_id": "T1001", "reports_to_role_id": null, "organizational_level": null, "department_id": 2, "natural_language": "Role Name: Marketing Manager\nDescription: Manages marketing campaigns and team\nTenant: T1001\nDepartment: Marketing", "created_at": "2025-06-19T16:33:40.578166", "created_by": null, "updated_at": "2025-06-19T16:33:40.578171", "updated_by": null, "version": 1, "status": "draft"}, "result": {"success": false, "error": "Error inserting role to workflow_temp: insert or update on table \"roles\" violates foreign key constraint \"fk_roles_department\"\nDETAIL:  Key (department_id)=(2) is not present in table \"departments\".\n", "schema": "workflow_temp", "role_id": "R11", "name": "Marketing Manager", "tenant_id": "T1001"}, "status": "error"}
{"timestamp": "2025-06-24T09:24:02.551071", "operation": "insert_role_to_workflow_temp", "input_data": {"_id": "685a6bdc0ab469c89675f23e", "id": 8, "role_id": "R12", "name": "Test Manager", "description": "Test management role", "tenant_id": "t999", "reports_to_role_id": null, "organizational_level": "Management", "department_id": 1, "natural_language": "Role Name: Test Manager\nDescription: Test management role\nReports To: CEO\nOrganization Level: Management\nDepartment: Engineering", "created_at": "2025-06-24T09:11:56.638801", "created_by": null, "updated_at": "2025-06-24T09:11:56.638809", "updated_by": null, "version": 1, "status": "draft", "reports_to_role_name": "CEO"}, "result": {"success": true, "inserted_id": 10, "schema": "workflow_temp", "role_id": "R12", "name": "Test Manager", "tenant_id": "t999"}, "status": "success"}
{"timestamp": "2025-06-24T09:24:02.554709", "operation": "process_mongo_roles_to_workflow_temp", "input_data": {"schema": "workflow_temp"}, "result": {"total_processed": 3, "successful_inserts": 1, "failed_inserts": 2, "inheritance_processed": 0, "details": [{"role_id": "R_MarketingManager_20250619162145", "status": "failed", "role_result": {"success": false, "error": "Error inserting role to workflow_temp: insert or update on table \"roles\" violates foreign key constraint \"fk_roles_department\"\nDETAIL:  Key (department_id)=(2) is not present in table \"departments\".\n", "schema": "workflow_temp", "role_id": "R_MarketingManager_20250619162145", "name": "Marketing Manager", "tenant_id": "T1001"}, "inheritance_results": []}, {"role_id": "R11", "status": "failed", "role_result": {"success": false, "error": "Error inserting role to workflow_temp: insert or update on table \"roles\" violates foreign key constraint \"fk_roles_department\"\nDETAIL:  Key (department_id)=(2) is not present in table \"departments\".\n", "schema": "workflow_temp", "role_id": "R11", "name": "Marketing Manager", "tenant_id": "T1001"}, "inheritance_results": []}, {"role_id": "R12", "status": "success", "role_result": {"success": true, "inserted_id": 10, "schema": "workflow_temp", "role_id": "R12", "name": "Test Manager", "tenant_id": "t999"}, "inheritance_results": []}]}, "status": "success"}
{"timestamp": "2025-06-24T10:20:17.364135", "operation": "deploy_single_role_to_workflow_runtime", "input_data": {"role_id": "R12"}, "result": {"success": false, "error": "Role R12 not found with status deployed_to_temp or draft", "role_id": "R12", "required_status": "deployed_to_temp or draft"}, "status": "error"}
{"timestamp": "2025-06-24T10:20:44.267036", "operation": "insert_role_to_workflow_runtime", "input_data": {"_id": "6854391977ff47ee3c5f402b", "id": 8, "role_id": "R_MarketingManager_20250619162145", "name": "Marketing Manager", "description": "Manages marketing campaigns and team", "tenant_id": "T1001", "reports_to_role_id": null, "organizational_level": null, "department_id": 2, "natural_language": "Role Name: Marketing Manager\nDescription: Manages marketing campaigns and team\nTenant: T1001\nDepartment: Marketing", "created_at": "2025-06-19T16:21:45.442150", "created_by": null, "updated_at": "2025-06-19T16:21:45.442161", "updated_by": null, "version": 1, "status": "draft"}, "result": {"success": true, "inserted_id": 8, "schema": "workflow_runtime", "role_id": "R_MarketingManager_20250619162145", "name": "Marketing Manager", "tenant_id": "T1001"}, "status": "success"}
{"timestamp": "2025-06-24T10:20:44.287335", "operation": "insert_role_to_workflow_runtime", "input_data": {"_id": "68543be4fc1c96b5e83de3d2", "id": 8, "role_id": "R11", "name": "Marketing Manager", "description": "Manages marketing campaigns and team", "tenant_id": "T1001", "reports_to_role_id": null, "organizational_level": null, "department_id": 2, "natural_language": "Role Name: Marketing Manager\nDescription: Manages marketing campaigns and team\nTenant: T1001\nDepartment: Marketing", "created_at": "2025-06-19T16:33:40.578166", "created_by": null, "updated_at": "2025-06-19T16:33:40.578171", "updated_by": null, "version": 1, "status": "draft"}, "result": {"success": true, "inserted_id": 9, "schema": "workflow_runtime", "role_id": "R11", "name": "Marketing Manager", "tenant_id": "T1001"}, "status": "success"}
{"timestamp": "2025-06-24T10:20:44.306945", "operation": "insert_role_to_workflow_runtime", "input_data": {"_id": "685a75790ab469c89675f242", "id": 8, "role_id": "R12", "name": "Sales Representative", "description": "Customer sales and relationship management", "tenant_id": "t999", "reports_to_role_id": null, "organizational_level": "Individual", "department_id": 2, "natural_language": "Role Name: Sales Representative\nDescription: Customer sales and relationship management\nReports To: Manager\nOrganization Level: Individual\nDepartment: Sales", "created_at": "2025-06-24T09:52:57.645462", "created_by": null, "updated_at": "2025-06-24T09:52:57.645466", "updated_by": null, "version": 1, "status": "draft", "reports_to_role_name": "Manager"}, "result": {"success": true, "inserted_id": 10, "schema": "workflow_runtime", "role_id": "R12", "name": "Sales Representative", "tenant_id": "t999"}, "status": "success"}
{"timestamp": "2025-06-24T10:20:44.324815", "operation": "insert_role_to_workflow_runtime", "input_data": {"_id": "685a75920ab469c89675f243", "id": 8, "role_id": "R13", "name": "Marketing Specialist", "description": "Marketing campaigns and brand management", "tenant_id": "t999", "reports_to_role_id": null, "organizational_level": "Individual", "department_id": 2, "natural_language": "Role Name: Marketing Specialist\nDescription: Marketing campaigns and brand management\nReports To: Manager\nOrganization Level: Individual\nDepartment: Sales", "created_at": "2025-06-24T09:53:22.429097", "created_by": null, "updated_at": "2025-06-24T09:53:22.429106", "updated_by": null, "version": 1, "status": "draft", "reports_to_role_name": "Manager"}, "result": {"success": true, "inserted_id": 11, "schema": "workflow_runtime", "role_id": "R13", "name": "Marketing Specialist", "tenant_id": "t999"}, "status": "success"}
{"timestamp": "2025-06-24T10:20:44.327418", "operation": "process_mongo_roles_to_workflow_runtime", "input_data": {"schema": "workflow_runtime"}, "result": {"total_processed": 4, "successful_inserts": 4, "failed_inserts": 0, "inheritance_processed": 0, "details": [{"role_id": "R_MarketingManager_20250619162145", "status": "success", "role_result": {"success": true, "inserted_id": 8, "schema": "workflow_runtime", "role_id": "R_MarketingManager_20250619162145", "name": "Marketing Manager", "tenant_id": "T1001"}, "inheritance_results": []}, {"role_id": "R11", "status": "success", "role_result": {"success": true, "inserted_id": 9, "schema": "workflow_runtime", "role_id": "R11", "name": "Marketing Manager", "tenant_id": "T1001"}, "inheritance_results": []}, {"role_id": "R12", "status": "success", "role_result": {"success": true, "inserted_id": 10, "schema": "workflow_runtime", "role_id": "R12", "name": "Sales Representative", "tenant_id": "t999"}, "inheritance_results": []}, {"role_id": "R13", "status": "success", "role_result": {"success": true, "inserted_id": 11, "schema": "workflow_runtime", "role_id": "R13", "name": "Marketing Specialist", "tenant_id": "t999"}, "inheritance_results": []}]}, "status": "success"}
