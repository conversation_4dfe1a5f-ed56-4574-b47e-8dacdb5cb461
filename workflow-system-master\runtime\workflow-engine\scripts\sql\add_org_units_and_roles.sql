-- Add Organizational Units and Roles
-- This script adds organizational units and the "User" role to the database.

-- Set search path to workflow_runtime schema
SET search_path TO workflow_runtime;

-- Add "User" role if it doesn't exist
INSERT INTO roles (role_id, name, tenant_id, created_at, updated_at)
SELECT 'r004', 'User', 't001', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP
WHERE NOT EXISTS (SELECT 1 FROM roles WHERE name = 'User');

-- Create permission contexts if they don't exist
INSERT INTO permission_contexts (context_id, name, description, context_type, context_rules)
VALUES 
    ('workflow_execute', 'Workflow Execute', 'Permission to execute workflows', 'function', '{"resource_id": "*", "permissions": ["execute"]}'::jsonb),
    ('entity_read', 'Entity Read', 'Permission to read entities', 'entity', '{"resource_id": "*", "permissions": ["read"]}'::jsonb),
    ('entity_write', 'Entity Write', 'Permission to write entities', 'entity', '{"resource_id": "*", "permissions": ["write"]}'::jsonb)
ON CONFLICT (context_id) DO NOTHING;

-- Assign permissions to the User role
INSERT INTO role_permissions (role_id, context_id)
VALUES 
    ('r004', 'workflow_execute'),
    ('r004', 'entity_read'),
    ('r004', 'entity_write')
ON CONFLICT (role_id, context_id) DO NOTHING;

-- Get tenant ID
DO $$
DECLARE
    tenant_id_var VARCHAR(36);
BEGIN
    SELECT tenant_id INTO tenant_id_var FROM tenants LIMIT 1;

    -- Add organizational units
    INSERT INTO organizational_units (org_unit_id, name, description, tenant_id, created_at, updated_at)
    VALUES 
        ('org_it', 'IT Department', 'Information Technology Department', tenant_id_var, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
        ('org_hr', 'HR Department', 'Human Resources Department', tenant_id_var, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
        ('org_finance', 'Finance Department', 'Finance and Accounting Department', tenant_id_var, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
        ('org_sales', 'Sales Department', 'Sales and Marketing Department', tenant_id_var, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
        ('org_ops', 'Operations', 'Operations Department', tenant_id_var, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
    ON CONFLICT (org_unit_id) DO NOTHING;
END $$;

-- Verify the changes
SELECT 'Roles:' AS table_name;
SELECT role_id, name, tenant_id FROM roles WHERE name = 'User';

SELECT 'Organizational Units:' AS table_name;
SELECT org_unit_id, name, description FROM organizational_units;
