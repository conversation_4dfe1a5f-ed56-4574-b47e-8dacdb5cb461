#!/usr/bin/env python3
"""
Enterprise Workflow YAML Validator

A comprehensive validator for enterprise workflow YAML configurations
used in dynamic execution engines.

This validator performs thorough validation of:
- YAML structure and syntax
- Entity and attribute definitions
- Local objective configurations
- Input/output stack integrity
- Data mapping consistency
- Database operation integrity
- Role and permission consistency
- Workflow continuity
- System function usage
"""

import yaml
import re
import json
from typing import Dict, List, Any, Set, Tuple, Optional, Union
from dataclasses import dataclass, field
from enum import Enum
import copy
import sys
from collections import defaultdict


class ValidationLevel(Enum):
    """Validation issue severity levels."""
    ERROR = "ERROR"  # Critical issues that must be fixed
    WARNING = "WARNING"  # Potential issues that should be reviewed
    INFO = "INFO"  # Informational findings


@dataclass
class ValidationIssue:
    """Represents a validation issue found during YAML validation."""
    level: ValidationLevel
    message: str
    path: str
    context: Optional[Any] = None

    def __str__(self):
        return f"{self.level.value}: {self.path} - {self.message}"


class YAMLWorkflowValidator:
    """
    Comprehensive validator for enterprise workflow YAML configurations.
    """

    def __init__(self):
        self.issues = []
        self.yaml_data = None

        # System function registry with detailed parameter information
        self.system_functions = self._build_system_function_registry()

        # Valid UI controls
        self.valid_ui_controls = self._build_ui_control_list()

        # Data type to UI control mapping
        self.data_type_ui_control_map = self._build_data_type_ui_control_map()

        # Initialize caches for cross-referencing validation
        self.lo_cache = {}
        self.entity_cache = {}
        self.attribute_cache = {}
        self.input_output_cache = {}
        self.mapping_cache = {}
        self.role_cache = {}
        self.permission_cache = {}
        self.entity_relationships = defaultdict(list)
        self.db_operations = defaultdict(list)
        self.required_attributes_cache = {}
        self.enum_values_cache = {}
        self.entity_attribute_map = {}
        self.role_entity_permissions = defaultdict(dict)
        self.role_objective_permissions = defaultdict(dict)

    def _build_system_function_registry(self) -> Dict:
        """Build the system function registry with detailed information."""
        return {
            # Database functions
            "create": {
                "type": "database",
                "params": ["entity_id", "data"],
                "description": "Insert record into entity table using mapped columns",
                "required_params": ["entity_id", "data"],
                "return_type": "boolean",  # Success/failure
                "affects_database": True,
                "operation_type": "write"
            },
            "fetch": {
                "type": "database",
                "params": ["table", "filters"],
                "description": "Fetch a single record from table",
                "required_params": ["table", "filters"],
                "return_type": "object",
                "affects_database": False,
                "operation_type": "read"
            },
            "fetch_records": {
                "type": "database",
                "params": ["table", "filters", "limit", "offset", "order_by"],
                "description": "Fetch multiple records from table",
                "required_params": ["table"],
                "return_type": "array",
                "affects_database": False,
                "operation_type": "read"
            },
            "update": {
                "type": "database",
                "params": ["entity_id", "data"],
                "description": "Update records in entity table",
                "required_params": ["entity_id", "data"],
                "return_type": "boolean",
                "affects_database": True,
                "operation_type": "write"
            },
            "fetch_max_value": {
                "type": "database",
                "params": ["entity", "attribute"],
                "description": "Fetch max value from an attribute",
                "required_params": ["entity", "attribute"],
                "return_type": "any",
                "affects_database": False,
                "operation_type": "read"
            },

            # Validation functions
            "validate_email": {
                "type": "validation",
                "params": ["email"],
                "description": "Validate format of an email",
                "required_params": ["email"],
                "return_type": "boolean",
                "affects_database": False
            },
            "validate_required": {
                "type": "validation",
                "params": ["value"],
                "description": "Ensure non-empty value",
                "required_params": ["value"],
                "return_type": "boolean",
                "affects_database": False
            },
            "enum_check": {
                "type": "validation",
                "params": ["value", "allowed_values"],
                "description": "Validate value exists in a given list",
                "required_params": ["value", "allowed_values"],
                "return_type": "boolean",
                "affects_database": False
            },
            "entity_exists": {
                "type": "validation",
                "params": ["entity", "attribute", "value"],
                "description": "Check if record exists in an entity",
                "required_params": ["entity", "attribute", "value"],
                "return_type": "boolean",
                "affects_database": False,
                "operation_type": "read"
            },
            "compare": {
                "type": "validation",
                "params": ["a", "b", "operator"],
                "description": "Compare two values using =, >, <, etc.",
                "required_params": ["a", "b", "operator"],
                "return_type": "boolean",
                "affects_database": False
            },
            "validate_audit_fields": {
                "type": "validation",
                "params": ["user_id", "timestamp", "action", "reason_for_change"],
                "description": "Validate audit-specific fields",
                "required_params": ["user_id", "timestamp", "action"],
                "return_type": "boolean",
                "affects_database": False
            },

            # Transform functions
            "format_date": {
                "type": "transform",
                "params": ["date_value", "format_string"],
                "description": "Format date to YYYY-MM-DD",
                "required_params": ["date_value"],
                "return_type": "string",
                "affects_database": False
            },
            "to_uppercase": {
                "type": "transform",
                "params": ["text"],
                "description": "Convert text to uppercase",
                "required_params": ["text"],
                "return_type": "string",
                "affects_database": False
            },
            "to_lowercase": {
                "type": "transform",
                "params": ["text"],
                "description": "Convert text to lowercase",
                "required_params": ["text"],
                "return_type": "string",
                "affects_database": False
            },

            # Math functions
            "add": {
                "type": "math",
                "params": ["a", "b"],
                "description": "Add two numbers",
                "required_params": ["a", "b"],
                "return_type": "number",
                "affects_database": False
            },
            "subtract": {
                "type": "math",
                "params": ["a", "b"],
                "description": "Subtract b from a",
                "required_params": ["a", "b"],
                "return_type": "number",
                "affects_database": False
            },
            "multiply": {
                "type": "math",
                "params": ["a", "b"],
                "description": "Multiply numbers",
                "required_params": ["a", "b"],
                "return_type": "number",
                "affects_database": False
            },
            "divide": {
                "type": "math",
                "params": ["a", "b"],
                "description": "Divide a by b",
                "required_params": ["a", "b"],
                "return_type": "number",
                "affects_database": False
            },
            "subtract_days": {
                "type": "math",
                "params": ["start_date", "end_date"],
                "description": "Get number of days between two dates",
                "required_params": ["start_date", "end_date"],
                "return_type": "integer",
                "affects_database": False
            },
            "add_days": {
                "type": "math",
                "params": ["base_date", "days"],
                "description": "Add N days to base date",
                "required_params": ["base_date", "days"],
                "return_type": "date",
                "affects_database": False
            },

            # Data functions
            "merge_dicts": {
                "type": "data",
                "params": ["dict1", "dict2"],
                "description": "Merge two dictionaries",
                "required_params": ["dict1", "dict2"],
                "return_type": "object",
                "affects_database": False
            },
            "filter_dict": {
                "type": "data",
                "params": ["data", "keys"],
                "description": "Keep only selected keys",
                "required_params": ["data", "keys"],
                "return_type": "object",
                "affects_database": False
            },

            # Utility functions
            "generate_id": {
                "type": "utility",
                "params": ["entity", "attribute", "prefix"],
                "description": "Generate incremental ID or fallback UUID",
                "required_params": ["entity", "attribute"],
                "return_type": "string",
                "affects_database": True,
                "operation_type": "read"  # Reads max value before generating new one
            },
            "current_timestamp": {
                "type": "utility",
                "params": [],
                "description": "Return current time in ISO format",
                "required_params": [],
                "return_type": "date",
                "affects_database": False
            },
            "notify": {
                "type": "utility",
                "params": ["message"],
                "description": "Send a mock notification/logging message",
                "required_params": ["message"],
                "return_type": "boolean",
                "affects_database": False
            },

            # Dropdown fetchers
            "fetch_enum_values": {
                "type": "dropdown",
                "params": ["entity_id", "attribute_id"],
                "description": "Fetch enum values for an attribute",
                "required_params": ["entity_id", "attribute_id"],
                "return_type": "array",
                "affects_database": False,
                "operation_type": "read"
            },
            "fetch_filtered_records": {
                "type": "dropdown",
                "params": ["table", "filter_column", "filter_value", "value_column", "display_column", "additional_filters"],
                "description": "Fetch filtered records for dropdown",
                "required_params": ["table", "filter_column", "value_column", "display_column"],
                "return_type": "array",
                "affects_database": False,
                "operation_type": "read"
            }
        }

    def _build_ui_control_list(self) -> List[str]:
        """Build the list of valid UI controls."""
        return [
            "oj-accordion", "oj-c-input-number", "oj-checkboxset", "oj-chip",
            "oj-select-single", "oj-input-text", "oj-menu-button", "oj-table",
            "oj-bind-text", "oj-combobox-one", "oj-input-date", "oj-button",
            "oj-toolbar", "oj-paging-control", "oj-date-picker", "oj-c-form-layout",
            "oj-c-input-date-picker", "oj-input-time", "oj-input-date-time",
            "oj-c-popup", "oj-file-picker", "oj-c-button", "oj-form-layout",
            "oj-input-number", "oj-c-select-multiple", "oj-input-password",
            "oj-c-rating-gauge", "oj-label", "oj-slider", "oj-switch", "oj-radioset",
            "oj-text-area", "oj-input-search", "oj-avatar", "oj-buttonset-one",
            "oj-bind-for-each", "oj-list-view", "oj-menu", "oj-list-item-layout",
            "oj-dialog", "oj-c-text-area", "oj-progress-bar", "oj-range-slider",
            "oj-train", "oj-c-input-month-mask", "oj-text"
        ]

    def _build_data_type_ui_control_map(self) -> Dict:
        """Build mapping between data types and appropriate UI controls."""
        return {
            "string": ["oj-input-text", "oj-text-area", "oj-c-text-area", "oj-bind-text", "oj-text"],
            "enum": ["oj-select-single", "oj-combobox-one", "oj-radioset", "oj-buttonset-one"],
            "number": ["oj-input-number", "oj-c-input-number", "oj-slider", "oj-range-slider"],
            "integer": ["oj-input-number", "oj-c-input-number", "oj-slider", "oj-range-slider"],
            "date": ["oj-input-date", "oj-date-picker", "oj-c-input-date-picker"],
            "boolean": ["oj-switch", "oj-checkboxset"]
        }

    def add_issue(self, level: ValidationLevel, message: str, path: str, context=None):
        """Add a validation issue to the issues list."""
        self.issues.append(ValidationIssue(level, message, path, context))

    def validate_file(self, file_path: str) -> List[ValidationIssue]:
        """Validate a YAML file by path."""
        try:
            with open(file_path, 'r') as f:
                yaml_content = f.read()

            try:
                return self.validate_yaml(yaml_content)
            except Exception as e:
                self.add_issue(ValidationLevel.ERROR, f"Error during validation: {str(e)}", "validation")
                return self.issues
        except Exception as e:
            self.add_issue(ValidationLevel.ERROR, f"Failed to read file: {str(e)}", "file")
            return self.issues

    def validate_yaml(self, yaml_content: str) -> List[ValidationIssue]:
        """Validate a YAML string."""
        self.issues = []

        try:
            self.yaml_data = yaml.safe_load(yaml_content)
        except yaml.YAMLError as e:
            self.add_issue(ValidationLevel.ERROR, f"Invalid YAML syntax: {str(e)}", "yaml_syntax")
            return self.issues

        # Start validation
        self._validate_basic_structure()
        if len([i for i in self.issues if i.level == ValidationLevel.ERROR]) > 0:
            # Don't continue if there are critical structure issues
            return self.issues

        # Build caches for cross-referencing
        self._build_caches()

        # Validate entities and attributes
        self._validate_entities_and_attributes()

        # Validate roles and permissions
        self._validate_roles_and_permissions()

        # Validate local objectives
        self._validate_local_objectives()

        # Database-specific validations
        self._validate_entity_relationships()
        self._validate_db_operations_consistency()

        # Role-Operation cross-validation
        self._validate_role_operation_consistency()

        # Validate workflow continuity
        self._validate_workflow_continuity()

        return self.issues

    def _build_caches(self):
        """Build internal caches for cross-referencing validation."""
        # Cache roles and permissions
        if "tenant" in self.yaml_data and "roles" in self.yaml_data["tenant"]:
            for role in self.yaml_data["tenant"]["roles"]:
                if "id" in role:
                    self.role_cache[role["id"]] = role

                    # Cache entity permissions for this role
                    if "access" in role and "entities" in role["access"]:
                        for entity_access in role["access"]["entities"]:
                            if "entity_id" in entity_access and "permissions" in entity_access:
                                self.role_entity_permissions[role["id"]][entity_access["entity_id"]] = entity_access["permissions"]

                    # Cache objective permissions for this role
                    if "access" in role and "objectives" in role["access"]:
                        for obj_access in role["access"]["objectives"]:
                            if "objective_id" in obj_access and "permissions" in obj_access:
                                self.role_objective_permissions[role["id"]][obj_access["objective_id"]] = obj_access["permissions"]

        # Cache permission types
        if "permission_types" in self.yaml_data:
            for perm in self.yaml_data["permission_types"]:
                if "id" in perm:
                    self.permission_cache[perm["id"]] = perm

        # Cache entities and attributes
        if "entities" in self.yaml_data:
            for entity in self.yaml_data["entities"]:
                if "id" in entity:
                    self.entity_cache[entity["id"]] = entity

                    # Cache required attributes for this entity
                    if "attributes_metadata" in entity and "required_attributes" in entity["attributes_metadata"]:
                        self.required_attributes_cache[entity["id"]] = set(entity["attributes_metadata"]["required_attributes"])

                    # Cache attribute map for this entity
                    if "attributes_metadata" in entity and "attribute_map" in entity["attributes_metadata"]:
                        attr_map = entity["attributes_metadata"]["attribute_map"]
                        for attr_id, attr_name in attr_map.items():
                            self.entity_attribute_map[f"{entity['id']}.{attr_id}"] = attr_name

                    # Process attributes
                    if "attributes" in entity:
                        for attr in entity["attributes"]:
                            if "id" in attr:
                                attr_key = f"{entity['id']}.{attr['id']}"
                                self.attribute_cache[attr_key] = attr

                                # Cache enum values
                                if attr.get("datatype", "").lower() == "enum" and "values" in attr:
                                    self.enum_values_cache[attr_key] = attr["values"]

                                # Detect potential foreign key relationships (simplified approach)
                                if attr.get("name", "").lower().endswith("id") and attr.get("name", "").lower() != entity.get("name", "").lower() + "id":
                                    # This might be a foreign key to another entity
                                    potential_entity = attr.get("name", "").lower().replace("id", "")
                                    self.entity_relationships[entity["id"]].append({
                                        "attribute": attr["id"],
                                        "potential_entity": potential_entity,
                                        "attribute_name": attr.get("name", "")
                                    })

        # Cache local objectives
        if "local_objectives" in self.yaml_data:
            for lo in self.yaml_data["local_objectives"]:
                if "id" in lo:
                    self.lo_cache[lo["id"]] = lo

                    # Track database operations based on function_type
                    if "function_type" in lo and lo["function_type"].lower() in ["create", "update", "delete"]:
                        # Determine target entity (usually from input_stack)
                        target_entity = self._extract_target_entity(lo)
                        if target_entity:
                            self.db_operations[target_entity].append({
                                "lo_id": lo["id"],
                                "operation": lo["function_type"].lower(),
                                "path": f"local_objectives.{lo['id']}"
                            })

                    # Cache input/output stacks
                    if "input_stack" in lo and "inputs" in lo["input_stack"]:
                        for inp in lo["input_stack"]["inputs"]:
                            if "id" in inp and "contextual_id" in inp:
                                self.input_output_cache[inp["contextual_id"]] = {"type": "input", "data": inp, "lo_id": lo["id"]}

                    if "output_stack" in lo and "outputs" in lo["output_stack"]:
                        for out in lo["output_stack"]["outputs"]:
                            if "id" in out and "contextual_id" in out:
                                self.input_output_cache[out["contextual_id"]] = {"type": "output", "data": out, "lo_id": lo["id"]}

                    # Cache mappings
                    if "data_mapping_stack" in lo and "mappings" in lo["data_mapping_stack"]:
                        for mapping in lo["data_mapping_stack"]["mappings"]:
                            if "id" in mapping and "source" in mapping and "target" in mapping:
                                self.mapping_cache[mapping["id"]] = mapping

    def _extract_target_entity(self, lo):
        """Extract the target entity for a local objective with database operation."""
        # Try to find entity reference in input_stack
        if "input_stack" in lo and "inputs" in lo["input_stack"]:
            for inp in lo["input_stack"]["inputs"]:
                if "slot_id" in inp and inp["slot_id"].startswith("e"):
                    # Extract entity ID from slot_id (e.g., "e001.at101.in001")
                    parts = inp["slot_id"].split(".")
                    if len(parts) > 0:
                        return parts[0]
        return None

    def _validate_basic_structure(self):
        """Validate the basic structure of the YAML."""
        # Check required top-level sections
        required_sections = ["tenant", "workflow_data", "permission_types", "entities", "global_objectives", "local_objectives"]
        for section in required_sections:
            if section not in self.yaml_data:
                self.add_issue(ValidationLevel.ERROR, f"Missing required top-level section: {section}", f"root.{section}")

        # Validate tenant section
        if "tenant" in self.yaml_data:
            tenant = self.yaml_data["tenant"]
            if "id" not in tenant:
                self.add_issue(ValidationLevel.ERROR, "Missing tenant ID", "tenant.id")
            elif not tenant["id"].lower() == tenant["id"]:
                self.add_issue(ValidationLevel.ERROR, "Tenant ID must be lowercase", "tenant.id")

            if "name" not in tenant:
                self.add_issue(ValidationLevel.ERROR, "Missing tenant name", "tenant.name")

            if "roles" not in tenant or not isinstance(tenant["roles"], list):
                self.add_issue(ValidationLevel.ERROR, "Missing or invalid tenant roles section", "tenant.roles")
            else:
                for i, role in enumerate(tenant["roles"]):
                    role_path = f"tenant.roles[{i}]"
                    if "id" not in role:
                        self.add_issue(ValidationLevel.ERROR, "Missing role ID", role_path)
                    elif not role["id"].lower() == role["id"]:
                        self.add_issue(ValidationLevel.ERROR, "Role ID must be lowercase", f"{role_path}.id")

                    if "name" not in role:
                        self.add_issue(ValidationLevel.ERROR, "Missing role name", f"{role_path}.name")

                    if "access" not in role:
                        self.add_issue(ValidationLevel.ERROR, "Missing role access section", f"{role_path}.access")

        # Validate permission_types
        if "permission_types" in self.yaml_data:
            permission_types = self.yaml_data["permission_types"]
            if not isinstance(permission_types, list):
                self.add_issue(ValidationLevel.ERROR, "Permission types must be a list", "permission_types")
            else:
                for i, perm in enumerate(permission_types):
                    perm_path = f"permission_types[{i}]"
                    if "id" not in perm:
                        self.add_issue(ValidationLevel.ERROR, "Missing permission type ID", perm_path)
                    elif not perm["id"].lower() == perm["id"]:
                        self.add_issue(ValidationLevel.ERROR, "Permission type ID must be lowercase", f"{perm_path}.id")

                    if "description" not in perm:
                        self.add_issue(ValidationLevel.ERROR, "Missing permission type description", f"{perm_path}.description")

                    if "capabilities" not in perm or not isinstance(perm["capabilities"], list):
                        self.add_issue(ValidationLevel.ERROR, "Missing or invalid capabilities", f"{perm_path}.capabilities")

    def _validate_entities_and_attributes(self):
        """Validate entities and their attributes."""
        if "entities" not in self.yaml_data:
            return

        # Track entity and attribute usage for comprehensive validation
        entity_usage = {entity["id"]: {"referenced": False, "attributes_used": set()}
                        for entity in self.yaml_data["entities"] if "id" in entity}

        # First pass: validate entity definitions
        for i, entity in enumerate(self.yaml_data["entities"]):
            entity_path = f"entities[{i}]"

            # Validate entity basic fields
            required_entity_fields = ["id", "name", "type", "version", "status", "attributes_metadata", "attributes"]
            for field in required_entity_fields:
                if field not in entity:
                    self.add_issue(ValidationLevel.ERROR, f"Missing required entity field: {field}", f"{entity_path}.{field}")

            # Validate entity ID format
            if "id" in entity and not entity["id"].lower() == entity["id"]:
                self.add_issue(ValidationLevel.ERROR, "Entity ID must be lowercase", f"{entity_path}.id")

            # Validate entity name (no spaces for internal name)
            if "name" in entity and " " in entity["name"]:
                self.add_issue(ValidationLevel.ERROR, "Entity name cannot contain spaces (use camelCase)", f"{entity_path}.name")

            # Validate attributes_metadata
            if "attributes_metadata" in entity:
                metadata = entity["attributes_metadata"]
                metadata_path = f"{entity_path}.attributes_metadata"

                required_metadata_fields = ["attribute_prefix", "attribute_map", "required_attributes"]
                for field in required_metadata_fields:
                    if field not in metadata:
                        self.add_issue(ValidationLevel.ERROR, f"Missing required attributes_metadata field: {field}", f"{metadata_path}.{field}")

                # Validate attribute_map
                if "attribute_map" in metadata and isinstance(metadata["attribute_map"], dict):
                    for attr_id, attr_name in metadata["attribute_map"].items():
                        if " " in attr_name:
                            self.add_issue(ValidationLevel.ERROR, f"Attribute internal name '{attr_name}' contains spaces (use camelCase)", f"{metadata_path}.attribute_map.{attr_id}")

                # Validate required_attributes
                if "required_attributes" in metadata and "attribute_map" in metadata:
                    for req_attr in metadata["required_attributes"]:
                        if req_attr not in metadata["attribute_map"]:
                            self.add_issue(ValidationLevel.ERROR, f"Required attribute {req_attr} not defined in attribute_map", f"{metadata_path}.required_attributes")

            # Validate attributes
            if "attributes" in entity:
                # Check for attribute definition completeness and consistency
                attribute_ids = set()
                attribute_names = set()

                for j, attr in enumerate(entity["attributes"]):
                    attr_path = f"{entity_path}.attributes[{j}]"

                    # Required attribute fields
                    required_attr_fields = ["id", "name", "display_name", "datatype", "required", "version", "status"]
                    for field in required_attr_fields:
                        if field not in attr:
                            self.add_issue(ValidationLevel.ERROR, f"Missing required attribute field: {field}", f"{attr_path}.{field}")

                    # Validate attribute ID format
                    if "id" in attr:
                        if not attr["id"].lower() == attr["id"]:
                            self.add_issue(ValidationLevel.ERROR, "Attribute ID must be lowercase", f"{attr_path}.id")

                        # Check for duplicate IDs
                        if attr["id"] in attribute_ids:
                            self.add_issue(ValidationLevel.ERROR, f"Duplicate attribute ID: {attr['id']}", f"{attr_path}.id")
                        attribute_ids.add(attr["id"])

                    # Validate attribute name (no spaces for internal name)
                    if "name" in attr:
                        if " " in attr["name"]:
                            self.add_issue(ValidationLevel.ERROR, "Attribute name cannot contain spaces (use camelCase)", f"{attr_path}.name")

                        # Check for duplicate names
                        if attr["name"] in attribute_names:
                            self.add_issue(ValidationLevel.ERROR, f"Duplicate attribute name: {attr['name']}", f"{attr_path}.name")
                        attribute_names.add(attr["name"])

                    # Validate enum attributes
                    if "datatype" in attr and attr["datatype"].lower() == "enum" and "values" not in attr:
                        self.add_issue(ValidationLevel.ERROR, "Enum attribute must define values array", f"{attr_path}.values")

                    # Check if required is a boolean
                    if "required" in attr and not isinstance(attr["required"], bool):
                        self.add_issue(ValidationLevel.ERROR, "Required field must be a boolean", f"{attr_path}.required")

                # Verify all attributes in metadata.attribute_map have definitions
                if "attributes_metadata" in entity and "attribute_map" in entity["attributes_metadata"]:
                    attribute_map = entity["attributes_metadata"]["attribute_map"]
                    for attr_id in attribute_map.keys():
                        if attr_id not in attribute_ids:
                            self.add_issue(ValidationLevel.ERROR,
                                          f"Attribute ID {attr_id} in attribute_map doesn't have a definition",
                                          f"{entity_path}.attributes_metadata.attribute_map")

        # Second pass: check entity usage in Local Objectives
        if "local_objectives" in self.yaml_data:
            for lo in self.yaml_data["local_objectives"]:
                # Check input_stack for entity references
                if "input_stack" in lo and "inputs" in lo["input_stack"]:
                    for inp in lo["input_stack"]["inputs"]:
                        if "slot_id" in inp:
                            parts = inp["slot_id"].split(".")
                            if len(parts) >= 2 and parts[0].startswith("e") and parts[1].startswith("at"):
                                entity_id = parts[0]
                                attr_id = parts[1]

                                # Skip information fields
                                if attr_id == "at999" or inp.get("source", {}).get("type") == "information":
                                    continue

                                if entity_id in entity_usage:
                                    entity_usage[entity_id]["referenced"] = True
                                    entity_usage[entity_id]["attributes_used"].add(attr_id)

                # Check output_stack for entity references
                if "output_stack" in lo and "outputs" in lo["output_stack"]:
                    for out in lo["output_stack"]["outputs"]:
                        if "slot_id" in out and not out["slot_id"].startswith("executionstatus"):
                            parts = out["slot_id"].split(".")
                            if len(parts) >= 2 and parts[0].startswith("e") and parts[1].startswith("at"):
                                entity_id = parts[0]
                                attr_id = parts[1]

                                if entity_id in entity_usage:
                                    entity_usage[entity_id]["referenced"] = True
                                    entity_usage[entity_id]["attributes_used"].add(attr_id)

        # Report unused entities and attributes
        for entity_id, usage in entity_usage.items():
            if not usage["referenced"]:
                self.add_issue(ValidationLevel.WARNING, f"Entity {entity_id} is defined but never used in any Local Objective", f"entities.{entity_id}")
            else:
                # Check for unused attributes
                all_attrs = set()
                if entity_id in self.entity_cache and "attributes" in self.entity_cache[entity_id]:
                    all_attrs = {attr["id"] for attr in self.entity_cache[entity_id]["attributes"] if "id" in attr}

                unused_attrs = all_attrs - usage["attributes_used"]
                if unused_attrs:
                    attrs_str = ", ".join(unused_attrs)
                    self.add_issue(ValidationLevel.INFO, f"Entity {entity_id} has unused attributes: {attrs_str}", f"entities.{entity_id}")

                # Check if all required attributes are used
                if entity_id in self.required_attributes_cache:
                    required_attrs = self.required_attributes_cache[entity_id]
                    unused_required = required_attrs - usage["attributes_used"]

                    if unused_required:
                        attrs_str = ", ".join(unused_required)
                        self.add_issue(ValidationLevel.WARNING,
                                      f"Entity {entity_id} has required attributes that are never used: {attrs_str}",
                                      f"entities.{entity_id}")

    def _validate_entity_relationships_integrity(self):
        """Validate entity relationship integrity."""
        # Identify all potential foreign key relationships
        foreign_key_map = {}  # Maps entity_id.attr_id to target_entity_id

        # First pass: identify all ID attributes and potential foreign keys
        for entity_id, entity in self.entity_cache.items():
            if "attributes" not in entity:
                continue

            # Find the primary ID field for this entity
            primary_key = None
            for attr in entity["attributes"]:
                if "id" in attr and "name" in attr:
                    if attr["name"].lower() == entity["name"].lower() + "id" or attr["name"].lower() == "id":
                        primary_key = attr["id"]
                        break

            # Find potential foreign keys
            for attr in entity["attributes"]:
                if "id" not in attr or "name" not in attr:
                    continue

                # Skip the primary key
                if attr["id"] == primary_key:
                    continue

                # Look for foreign key pattern (ends with "id" or "Id")
                if attr["name"].lower().endswith("id"):
                    # Extract the entity name from the attribute name
                    potential_entity_name = attr["name"].lower().replace("id", "")

                    # Find target entity by name
                    target_entity_id = None
                    for target_id, target in self.entity_cache.items():
                        if "name" in target and target["name"].lower() == potential_entity_name:
                            target_entity_id = target_id
                            break

                    if target_entity_id:
                        foreign_key_map[f"{entity_id}.{attr['id']}"] = target_entity_id
                        
        # Second pass: check for validation rules on foreign keys
        for fk_attr, target_entity_id in foreign_key_map.items():
            entity_id, attr_id = fk_attr.split('.')
            
            # Check if there are proper validations in LOs for this foreign key
            validation_found = False
            
            # Look through all LOs that use this foreign key
            for lo_id, lo in self.lo_cache.items():
                if "input_stack" in lo and "inputs" in lo["input_stack"]:
                    for inp in lo["input_stack"]["inputs"]:
                        if "slot_id" in inp and inp["slot_id"].startswith(f"{entity_id}.{attr_id}"):
                            # Check for validations
                            if "validations" in inp and isinstance(inp["validations"], list):
                                for validation in inp["validations"]:
                                    if validation.get("validation_method") == "entity_exists" and validation.get("entity") == target_entity_id:
                                        validation_found = True
                                        break
            
            if not validation_found:
                self.add_issue(ValidationLevel.WARNING, 
                              f"Foreign key {attr_id} in entity {entity_id} references {target_entity_id} but lacks entity_exists validation",
                              f"entities.{entity_id}.attributes.{attr_id}")

    def _validate_roles_and_permissions(self):
        """Validate roles, permissions, and their relationships."""
        if "tenant" not in self.yaml_data or "roles" not in self.yaml_data["tenant"]:
            return

        # Build set of valid permission types
        valid_permission_types = set()
        if "permission_types" in self.yaml_data:
            valid_permission_types = {perm["id"].lower() for perm in self.yaml_data["permission_types"] if "id" in perm}

        # Track role inheritance to detect circular references
        role_inheritance_graph = {}

        # First pass: validate role definitions and build inheritance graph
        for i, role in enumerate(self.yaml_data["tenant"]["roles"]):
            role_path = f"tenant.roles[{i}]"
            role_id = role.get("id")

            if not role_id:
                continue  # Already reported in basic structure validation

            # Track inheritance
            if "inherits_from" in role and role["inherits_from"]:
                parent_role = role["inherits_from"]
                role_inheritance_graph[role_id] = parent_role

                # Check if parent role exists
                if parent_role not in self.role_cache:
                    self.add_issue(ValidationLevel.ERROR,
                                  f"Role {role_id} inherits from non-existent role: {parent_role}",
                                  f"{role_path}.inherits_from")

            # Validate entity permissions
            if "access" in role and "entities" in role["access"]:
                for j, entity_access in enumerate(role["access"]["entities"]):
                    entity_path = f"{role_path}.access.entities[{j}]"

                    if "entity_id" not in entity_access:
                        self.add_issue(ValidationLevel.ERROR, "Missing entity_id in role entity access", entity_path)
                    elif entity_access["entity_id"] not in self.entity_cache:
                        self.add_issue(ValidationLevel.ERROR,
                                      f"Role references non-existent entity: {entity_access['entity_id']}",
                                      f"{entity_path}.entity_id")

                    if "permissions" not in entity_access or not isinstance(entity_access["permissions"], list):
                        self.add_issue(ValidationLevel.ERROR, "Missing or invalid permissions list in role entity access", f"{entity_path}.permissions")
                    else:
                        # Validate each permission
                        for perm in entity_access["permissions"]:
                            perm_lower = perm.lower()
                            if perm_lower not in valid_permission_types:
                                self.add_issue(ValidationLevel.ERROR,
                                             f"Invalid permission type '{perm}' for entity {entity_access.get('entity_id', '')}",
                                             f"{entity_path}.permissions")

            # Validate objective permissions
            if "access" in role and "objectives" in role["access"]:
                for j, obj_access in enumerate(role["access"]["objectives"]):
                    obj_path = f"{role_path}.access.objectives[{j}]"

                    if "objective_id" not in obj_access:
                        self.add_issue(ValidationLevel.ERROR, "Missing objective_id in role objective access", obj_path)
                    # We can't validate objective existence yet as they might be local or global objectives

                    if "permissions" not in obj_access or not isinstance(obj_access["permissions"], list):
                        self.add_issue(ValidationLevel.ERROR, "Missing or invalid permissions list in role objective access", f"{obj_path}.permissions")
                    else:
                        # Validate each permission
                        for perm in obj_access["permissions"]:
                            perm_lower = perm.lower()
                            if perm_lower not in valid_permission_types and perm_lower != "execute":
                                self.add_issue(ValidationLevel.ERROR,
                                             f"Invalid permission type '{perm}' for objective {obj_access.get('objective_id', '')}",
                                             f"{obj_path}.permissions")

        # Second pass: detect circular role inheritance
        def detect_cycle(role_id, visited=None, path=None):
            if visited is None:
                visited = set()
            if path is None:
                path = []

            if role_id in visited:
                return path + [role_id]

            visited.add(role_id)
            path = path + [role_id]

            if role_id in role_inheritance_graph:
                parent = role_inheritance_graph[role_id]
                if parent in path:
                    return path + [parent]

                cycle = detect_cycle(parent, visited, path)
                if cycle:
                    return cycle

            return None

        for role_id in role_inheritance_graph:
            cycle = detect_cycle(role_id)
            if cycle and cycle[0] == cycle[-1]:
                cycle_str = " -> ".join(cycle)
                self.add_issue(ValidationLevel.ERROR,
                              f"Circular role inheritance detected: {cycle_str}",
                              f"tenant.roles")

    def _validate_local_objectives(self):
        """Validate local objectives and their components."""
        if "local_objectives" not in self.yaml_data:
            return

        los = self.yaml_data["local_objectives"]

        # Validate first LO is origin and last is terminal
        if len(los) > 0:
            first_lo = los[0]
            if "workflow_source" not in first_lo or first_lo["workflow_source"] != "origin":
                self.add_issue(ValidationLevel.ERROR, "First Local Objective must have workflow_source: origin", f"local_objectives[0].workflow_source")

            last_lo = los[-1]
            if "workflow_source" not in last_lo or last_lo["workflow_source"] != "terminal":
                self.add_issue(ValidationLevel.ERROR, "Last Local Objective must have workflow_source: terminal", f"local_objectives[{len(los)-1}].workflow_source")

        for i, lo in enumerate(los):
            lo_path = f"local_objectives[{i}]"

            # Validate required fields
            required_lo_fields = ["id", "contextual_id", "name", "workflow_source", "function_type", "agent_stack", "input_stack", "output_stack", "data_mapping_stack", "execution_pathway"]
            for field in required_lo_fields:
                if field not in lo:
                    self.add_issue(ValidationLevel.ERROR, f"Missing required local objective field: {field}", f"{lo_path}.{field}")

            # Validate ID format
            if "id" in lo and not lo["id"].lower() == lo["id"]:
                self.add_issue(ValidationLevel.ERROR, "Local Objective ID must be lowercase", f"{lo_path}.id")

            # Validate workflow_source
            if "workflow_source" in lo and lo["workflow_source"] not in ["origin", "intermediate", "terminal"]:
                self.add_issue(ValidationLevel.ERROR, "Invalid workflow_source value - must be 'origin', 'intermediate', or 'terminal'", f"{lo_path}.workflow_source")

            # Validate function_type
            if "function_type" in lo and lo["function_type"].lower() not in ["create", "update", "read", "delete"]:
                self.add_issue(ValidationLevel.ERROR, "Invalid function_type value", f"{lo_path}.function_type")

            # Validate agent_stack
            if "agent_stack" in lo:
                agent_stack = lo["agent_stack"]
                agent_path = f"{lo_path}.agent_stack"

                if "agents" not in agent_stack or not isinstance(agent_stack["agents"], list) or len(agent_stack["agents"]) == 0:
                    self.add_issue(ValidationLevel.ERROR, "Missing or empty agents list in agent_stack", f"{agent_path}.agents")
                else:
                    for j, agent in enumerate(agent_stack["agents"]):
                        agent_item_path = f"{agent_path}.agents[{j}]"

                        if "role" not in agent:
                            self.add_issue(ValidationLevel.ERROR, "Missing role in agent", f"{agent_item_path}.role")
                        elif agent["role"] not in self.role_cache:
                            self.add_issue(ValidationLevel.ERROR, f"Agent references non-existent role: {agent['role']}", f"{agent_item_path}.role")

                        if "rights" not in agent or not isinstance(agent["rights"], list) or len(agent["rights"]) == 0:
                            self.add_issue(ValidationLevel.ERROR, "Missing or empty rights in agent", f"{agent_item_path}.rights")
                        else:
                            # Check that rights match role permissions
                            role_id = agent["role"]
                            if "contextual_id" in lo:
                                obj_id = lo["contextual_id"]
                                if role_id in self.role_objective_permissions and obj_id in self.role_objective_permissions[role_id]:
                                    role_perms = set(p.lower() for p in self.role_objective_permissions[role_id][obj_id])
                                    agent_rights = set(r.lower() for r in agent["rights"])

                                    missing_rights = agent_rights - role_perms
                                    if missing_rights:
                                        self.add_issue(ValidationLevel.ERROR,
                                                     f"Agent has rights {', '.join(missing_rights)} not granted to role {role_id} for objective {obj_id}",
                                                     f"{agent_item_path}.rights")

            # Validate input_stack
            if "input_stack" in lo:
                input_stack = lo["input_stack"]
                input_path = f"{lo_path}.input_stack"

                if "inputs" not in input_stack or not isinstance(input_stack["inputs"], list):
                    self.add_issue(ValidationLevel.ERROR, "Missing or invalid inputs list in input_stack", f"{input_path}.inputs")
                else:
                    self._validate_input_stack(input_stack["inputs"], input_path, lo)

            # Validate output_stack
            if "output_stack" in lo:
                output_stack = lo["output_stack"]
                output_path = f"{lo_path}.output_stack"

                if "outputs" not in output_stack or not isinstance(output_stack["outputs"], list):
                    self.add_issue(ValidationLevel.ERROR, "Missing or invalid outputs list in output_stack", f"{output_path}.outputs")
                else:
                    self._validate_output_stack(output_stack["outputs"], output_path, lo)

            # Validate execution_pathway
            if "execution_pathway" in lo:
                self._validate_execution_pathway(lo["execution_pathway"], f"{lo_path}.execution_pathway", lo)

    def _validate_input_stack(self, inputs, base_path, lo):
        """Validate the input stack of a local objective."""
        input_ids = set()
        slot_ids = set()
        contextual_ids = set()

        for i, inp in enumerate(inputs):
            input_path = f"{base_path}.inputs[{i}]"

            # Check for required fields
            required_input_fields = ["id", "slot_id", "contextual_id", "source", "required", "data_type", "ui_control", "metadata"]
            for field in required_input_fields:
                if field not in inp:
                    self.add_issue(ValidationLevel.ERROR, f"Missing required input field: {field}", f"{input_path}.{field}")

            # Check for duplicate IDs
            if "id" in inp:
                if inp["id"] in input_ids:
                    self.add_issue(ValidationLevel.ERROR, f"Duplicate input ID: {inp['id']}", f"{input_path}.id")
                input_ids.add(inp["id"])

                # Check ID format
                if not inp["id"].startswith("in"):
                    self.add_issue(ValidationLevel.ERROR, f"Input ID should start with 'in': {inp['id']}", f"{input_path}.id")

            # Check for duplicate slot_ids
            if "slot_id" in inp:
                if inp["slot_id"] in slot_ids:
                    self.add_issue(ValidationLevel.ERROR, f"Duplicate slot_id: {inp['slot_id']}", f"{input_path}.slot_id")
                slot_ids.add(inp["slot_id"])

                # Validate slot_id format (e.g., "e001.at101.in001")
                if not self._validate_slot_id_format(inp["slot_id"], input_path, "input"):
                    pass  # Issue already added in _validate_slot_id_format

                # Check if the entity and attribute in slot_id exist
                parts = inp["slot_id"].split(".")
                if len(parts) >= 2 and parts[0].startswith("e") and parts[1].startswith("at"):
                    entity_id = parts[0]
                    attr_id = parts[1]

                    # Skip information fields which might have special slot_id format
                    if "source" in inp and isinstance(inp["source"], dict) and inp["source"].get("type") == "information":
                        pass
                    # For other inputs, validate entity and attribute references
                    elif entity_id not in self.entity_cache:
                        self.add_issue(ValidationLevel.ERROR, f"Slot ID references non-existent entity: {entity_id}", f"{input_path}.slot_id")
                    elif f"{entity_id}.{attr_id}" not in self.attribute_cache:
                        self.add_issue(ValidationLevel.ERROR, f"Slot ID references non-existent attribute: {attr_id} in entity {entity_id}", f"{input_path}.slot_id")

            # Check for duplicate contextual_ids
            if "contextual_id" in inp:
                if inp["contextual_id"] in contextual_ids:
                    self.add_issue(ValidationLevel.ERROR, f"Duplicate contextual_id: {inp['contextual_id']}", f"{input_path}.contextual_id")
                contextual_ids.add(inp["contextual_id"])

                # Validate contextual_id format (e.g., "go001.lo001.in001")
                if not inp["contextual_id"].startswith("go") or ".lo" not in inp["contextual_id"] or ".in" not in inp["contextual_id"]:
                    self.add_issue(ValidationLevel.ERROR, f"Invalid contextual_id format: {inp['contextual_id']}", f"{input_path}.contextual_id")

            # Validate source
            if "source" in inp and isinstance(inp["source"], dict):
                source = inp["source"]
                source_path = f"{input_path}.source"

                if "type" not in source:
                    self.add_issue(ValidationLevel.ERROR, "Missing source type", f"{source_path}.type")
                elif source["type"] not in ["user", "system", "system_dependent", "information"]:
                    self.add_issue(ValidationLevel.ERROR, f"Invalid source type: {source['type']}", f"{source_path}.type")

                # For system_dependent inputs, validate dependencies
                if source.get("type") == "system_dependent":
                    if "dependencies" not in inp or not isinstance(inp["dependencies"], list) or len(inp["dependencies"]) == 0:
                        self.add_issue(ValidationLevel.ERROR, "system_dependent input must specify dependencies", f"{input_path}.dependencies")
                    else:
                        # Check all dependencies exist as input IDs
                        for dep_id in inp["dependencies"]:
                            if dep_id not in input_ids:
                                self.add_issue(ValidationLevel.ERROR, f"Dependency '{dep_id}' not found in input stack", f"{input_path}.dependencies")

                    if "dependency_type" not in inp:
                        self.add_issue(ValidationLevel.ERROR, "system_dependent input must specify dependency_type", f"{input_path}.dependency_type")
                    elif inp["dependency_type"] not in ["calculation", "dropdown"]:
                        self.add_issue(ValidationLevel.ERROR, f"Invalid dependency_type: {inp['dependency_type']}", f"{input_path}.dependency_type")

                    if inp.get("dependency_type") == "dropdown" and ("dropdown_source" not in inp or not isinstance(inp["dropdown_source"], dict)):
                        self.add_issue(ValidationLevel.ERROR, "dropdown dependency_type must have dropdown_source", f"{input_path}.dropdown_source")

                    if inp.get("dependency_type") == "calculation" and "nested_function" not in inp:
                        self.add_issue(ValidationLevel.ERROR, "calculation dependency_type must have nested_function", f"{input_path}.nested_function")

                # For system inputs, check if they need a nested_function
                if source.get("type") == "system" and inp.get("id") != "executionstatus":
                    # Check for database or calculated values
                    source_description = source.get("description", "").lower()
                    needs_function = any(term in source_description for term in
                                       ["auto", "generated", "calculated", "computed", "database",
                                        "fetch", "default", "system", "current", "session"])

                    if needs_function and "nested_function" not in inp:
                        self.add_issue(ValidationLevel.ERROR,
                                      f"System input with description '{source.get('description')}' should have nested_function",
                                      f"{input_path}.nested_function")

            # Validate UI control
            if "ui_control" in inp and inp["ui_control"] not in self.valid_ui_controls:
                self.add_issue(ValidationLevel.ERROR, f"Invalid UI control: {inp['ui_control']}", f"{input_path}.ui_control")

            # Validate data_type and ui_control compatibility
            if "data_type" in inp and "ui_control" in inp:
                data_type = inp["data_type"].lower()
                ui_control = inp["ui_control"]

                if data_type in self.data_type_ui_control_map and ui_control not in self.data_type_ui_control_map[data_type]:
                    self.add_issue(ValidationLevel.WARNING, f"UI control {ui_control} may not be appropriate for data type {data_type}", f"{input_path}.ui_control")

            # Validate required field is boolean
            if "required" in inp and not isinstance(inp["required"], bool):
                self.add_issue(ValidationLevel.ERROR, "Required field must be a boolean", f"{input_path}.required")

            # Validate enum values are provided for enum types
            if "data_type" in inp and inp["data_type"].lower() == "enum":
                enum_values_provided = False

                # Check for allowed_values directly on the input
                if "allowed_values" in inp and isinstance(inp["allowed_values"], list):
                    enum_values_provided = True

                # Check for dropdown source
                if "dropdown_source" in inp:
                    enum_values_provided = True

                # Check for system source that might fetch from entity attribute
                if "source" in inp and inp["source"].get("type") == "system" and "slot_id" in inp:
                    # Parse entity and attribute from slot_id
                    parts = inp["slot_id"].split(".")
                    if len(parts) >= 2:
                        entity_id = parts[0]
                        attr_id = parts[1]
                        attr_key = f"{entity_id}.{attr_id}"

                        # Check if the attribute has enum values defined
                        if attr_key in self.enum_values_cache:
                            enum_values_provided = True

                if not enum_values_provided:
                    self.add_issue(ValidationLevel.ERROR, "Enum data type requires allowed_values or dropdown_source", f"{input_path}.allowed_values")

            # Validate nested function if present
            if "nested_function" in inp:
                self._validate_nested_function(inp["nested_function"], f"{input_path}.nested_function")

            # Function type "Update" specific validation
            if lo.get("function_type", "").lower() == "update" and "metadata" in inp and isinstance(inp["metadata"], dict):
                if "usage" not in inp["metadata"]:
                    self.add_issue(ValidationLevel.ERROR, "Update function_type requires metadata.usage", f"{input_path}.metadata.usage")
                elif inp["metadata"]["usage"] not in ["lookup", "update", "both", ""]:
                    self.add_issue(ValidationLevel.ERROR, f"Invalid metadata.usage value: {inp['metadata']['usage']}", f"{input_path}.metadata.usage")

                # For user inputs in update functions, check they're properly marked for update
                if "source" in inp and inp["source"].get("type") == "user" and inp["metadata"]["usage"] not in ["update", "both"]:
                    self.add_issue(ValidationLevel.ERROR,
                                  f"User input in update function should have metadata.usage='update' or 'both', found '{inp['metadata']['usage']}'",
                                  f"{input_path}.metadata.usage")

                # For system inputs that provide lookup values, verify they're marked correctly
                if "source" in inp and inp["source"].get("type") == "system" and inp["metadata"]["usage"] not in ["lookup", "both"]:
                    # Warn only if this seems like a lookup field (ID or key field)
                    if "slot_id" in inp and (".at101." in inp["slot_id"] or "id" in inp["slot_id"].lower()):
                        self.add_issue(ValidationLevel.WARNING,
                                      f"System input that appears to be ID field should have metadata.usage='lookup' or 'both'",
                                      f"{input_path}.metadata.usage")

    def _validate_nested_function(self, nested_function, path):
        """Validate nested function configuration."""
        # Check for required fields
        required_fields = ["function_name", "parameters", "output_to"]
        for field in required_fields:
            if field not in nested_function:
                self.add_issue(ValidationLevel.ERROR, f"Missing required nested function field: {field}", f"{path}.{field}")
                
        # Check if function exists in the system function registry
        if "function_name" in nested_function:
            function_name = nested_function["function_name"]
            if function_name not in self.system_functions:
                self.add_issue(ValidationLevel.ERROR, f"Unknown function: {function_name}", f"{path}.function_name")
            else:
                # Validate parameters against function definition
                function_def = self.system_functions[function_name]
                if "parameters" in nested_function and "required_params" in function_def:
                    params = nested_function["parameters"]
                    for required_param in function_def["required_params"]:
                        if required_param not in params:
                            self.add_issue(ValidationLevel.ERROR, 
                                          f"Missing required parameter '{required_param}' for function {function_name}", 
                                          f"{path}.parameters.{required_param}")

    def _validate_dropdown_source(self, dropdown_source, path):
        """Validate dropdown source configuration."""
        # Check for required fields
        required_fields = ["function_name", "function_params"]
        for field in required_fields:
            if field not in dropdown_source:
                self.add_issue(ValidationLevel.ERROR, f"Missing required dropdown source field: {field}", f"{path}.{field}")
                
        # Check if function exists and is a dropdown type
        if "function_name" in dropdown_source:
            function_name = dropdown_source["function_name"]
            if function_name not in self.system_functions:
                self.add_issue(ValidationLevel.ERROR, f"Unknown function: {function_name}", f"{path}.function_name")
            else:
                function_def = self.system_functions[function_name]
                if function_def.get("type") != "dropdown":
                    self.add_issue(ValidationLevel.ERROR, 
                                  f"Function {function_name} is not a dropdown function type", 
                                  f"{path}.function_name")
                
                # Validate parameters against function definition
                if "function_params" in dropdown_source and "required_params" in function_def:
                    params = dropdown_source["function_params"]
                    for required_param in function_def["required_params"]:
                        if required_param not in params:
                            self.add_issue(ValidationLevel.ERROR, 
                                          f"Missing required parameter '{required_param}' for function {function_name}", 
                                          f"{path}.function_params.{required_param}")

    def _validate_slot_id_format(self, slot_id, path, field_type):
        """Validate the format of slot_id fields."""
        # Check basic structure (entity.attribute.id)
        parts = slot_id.split(".")

        # Special case for executionstatus
        if slot_id.startswith("executionstatus."):
            return True

        # Special case for information fields
        if slot_id.startswith("information.") or "at999" in slot_id:
            return True

        if len(parts) != 3:
            self.add_issue(ValidationLevel.ERROR, f"Invalid slot_id format: {slot_id} (should be entity.attribute.id)", f"{path}")
            return False

        entity, attribute, id_part = parts

        # Check entity format (e.g., "e001")
        if not entity.startswith("e") or not entity[1:].isdigit():
            self.add_issue(ValidationLevel.ERROR, f"Invalid entity format in slot_id: {entity}", f"{path}")
            return False

        # Check attribute format (e.g., "at101")
        if not attribute.startswith("at") or not attribute[2:].isdigit():
            self.add_issue(ValidationLevel.ERROR, f"Invalid attribute format in slot_id: {attribute}", f"{path}")
            return False

        # Check ID part format based on field type
        if field_type == "input" and not id_part.startswith("in"):
            self.add_issue(ValidationLevel.ERROR, f"Input slot_id should have ID starting with 'in': {id_part}", f"{path}")
            return False

        if field_type == "output" and not id_part.startswith("out"):
            self.add_issue(ValidationLevel.ERROR, f"Output slot_id should have ID starting with 'out': {id_part}", f"{path}")
            return False

        return True

    def _validate_output_stack(self, outputs, base_path, lo):
        """Validate the output stack of a local objective."""
        output_ids = set()
        slot_ids = set()
        contextual_ids = set()
        found_execution_status = False

        # First, check if all input fields are also in output
        if "input_stack" in lo and "inputs" in lo["input_stack"]:
            # Map inputs by contextual_id
            input_map = {}
            for inp in lo["input_stack"]["inputs"]:
                if "contextual_id" in inp:
                    input_map[inp["contextual_id"]] = inp

            # Create a map of outputs for cross-referencing
            output_map = {}
            for out in outputs:
                if "contextual_id" in out:
                    output_map[out["contextual_id"]] = out

            # Check that each input has a corresponding output
            for input_id, inp in input_map.items():
                # Replace "in" with "out" in the contextual_id to find corresponding output
                expected_output_id = input_id.replace(".in", ".out")

                if expected_output_id not in output_map:
                    # This is critical - all inputs must be in outputs
                    self.add_issue(ValidationLevel.ERROR,
                                  f"Input {input_id} not found in output_stack (expected {expected_output_id})",
                                  f"{base_path}.outputs")
                else:
                    # Check data types match between input and output
                    out = output_map[expected_output_id]
                    if "data_type" in inp and "data_type" in out and inp["data_type"] != out["data_type"]:
                        self.add_issue(ValidationLevel.ERROR,
                                      f"Data type mismatch between input {input_id} ({inp['data_type']}) and output {expected_output_id} ({out['data_type']})",
                                      f"{base_path}.outputs")

        for i, out in enumerate(outputs):
            output_path = f"{base_path}.outputs[{i}]"

            # Check for required fields
            required_output_fields = ["id", "slot_id", "contextual_id", "data_type", "ui_control"]
            for field in required_output_fields:
                if field not in out:
                    self.add_issue(ValidationLevel.ERROR, f"Missing required output field: {field}", f"{output_path}.{field}")

            # Check for execution status output
            if "slot_id" in out and out["slot_id"].startswith("executionstatus"):
                found_execution_status = True

            # Validate slot_id format
            if "slot_id" in out:
                self._validate_slot_id_format(out["slot_id"], output_path, "output")