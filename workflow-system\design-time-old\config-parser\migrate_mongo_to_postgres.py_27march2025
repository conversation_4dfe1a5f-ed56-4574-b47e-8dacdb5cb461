import psycopg2
from psycopg2.extras import <PERSON><PERSON>
from pymongo import MongoClient
import json
import traceback

# ✅ PostgreSQL Connection Details
PG_CONFIG = {
    "dbname": "workflow_solution_temp",
    "user": "postgres",
    "password": "postgres",
    "host": "localhost",
    "port": "5432"
}

# ✅ MongoDB Connection Details
MONGO_URI = "mongodb://localhost:27017/"
MONGO_DB = "workflow_system"
MONGO_COLLECTION = "workflow"

try:
    # ✅ Connect to MongoDB
    print("\n➡️ Connecting to MongoDB...")
    mongo_client = MongoClient(MONGO_URI)
    mongo_db = mongo_client[MONGO_DB]
    mongo_collection = mongo_db[MONGO_COLLECTION]
    print("✅ MongoDB connection established")

    # ✅ Connect to PostgreSQL
    print("\n➡️ Connecting to PostgreSQL...")
    pg_conn = psycopg2.connect(**PG_CONFIG)
    pg_conn.autocommit = True
    pg_cursor = pg_conn.cursor()
    print("✅ PostgreSQL connection established")

    # ✅ Step 1: Truncate all tables before insertion
    print("\n⚠️ Truncating all tables before insertion...")

    tables = [
        "output_triggers", "output_items", "output_stack",
        "system_functions", "input_items", "input_stack",
        "data_mappings", "mapping_rules", "data_mapping_stack",
        "global_objectives", "agent_rights", "agent_stack", "execution_pathway_conditions",
        "terminal_pathways", "execution_pathways", "local_objectives", "tenants", "permission_types",
        "roles", "entity_permissions", "objective_permissions", "entities", "entity_attribute_metadata",
        "entity_attributes", "attribute_validations", "attribute_enum_values", "entity_relationships",
        "execution_pathway_conditions"
    ]

    for table in tables:
        try:
            pg_cursor.execute(f"DELETE FROM workflow_solution_temp.{table};")  # DELETE instead of TRUNCATE
            print(f"✅ Deleted records from {table}")
        except Exception as e:
            print(f"❌ Error deleting records from {table}: {e}")

    # ✅ Fetch MongoDB Data
    workflow_data = mongo_collection.find_one()
    if not workflow_data:
        print("❌ No data found in MongoDB.")
        exit()
    workflow_data = workflow_data.get("workflow_data", {})

    # ✅ Insert Tenant
    tenant = workflow_data.get("tenant", {})
    tenant_id = tenant.get("id", "T000")
    tenant_name = tenant.get("name", "DefaultTenant")

    print(f"\n➡️ Inserting Tenant: {tenant_name}...")
    try:
        pg_cursor.execute(
            "INSERT INTO workflow_solution_temp.tenants (tenant_id, name) VALUES (%s, %s) ON CONFLICT DO NOTHING",
            (tenant_id, tenant_name)
        )
        print(f"✅ Inserted Tenant: {tenant_name}")
    except Exception as e:
        print(f"❌ Error inserting tenant: {e}")

    print("\n➡️ Inserting Roles...")
    for role in tenant.get("roles", []):
        try:
            pg_cursor.execute(
                """
                INSERT INTO workflow_solution_temp.roles (role_id, name, tenant_id, inherits_from)
                VALUES (%s, %s, %s, %s)
                ON CONFLICT (role_id) DO NOTHING;
                """,
                (role["id"], role["name"], tenant["id"], role.get("inherits_from"))
            )
            print(f"✅ Inserted Role: {role['id']} - {role['name']}")
        except Exception as e:
            print(f"❌ Error inserting role {role['id']}: {e}")

    # Insert entity permissions
    print("\n➡️ Inserting Entity Permissions...")
    for role in tenant.get("roles", []):
        for entity in role.get("access", {}).get("entities", []):
            for permission in entity.get("permissions", []):
                try:
                    pg_cursor.execute(
                        """
                        INSERT INTO workflow_solution_temp.entity_permissions (role_id, entity_id, permission_id)
                        VALUES (%s, %s, %s)
                        """,
                        (role["id"], entity["entity_id"], permission)
                    )
                    print(f"✅ Inserted Entity Permission: Role {role['id']} - Entity {entity['entity_id']} - Permission {permission}")
                except Exception as e:
                    print(f"❌ Error inserting entity permission {permission} for role {role['id']}: {e}")

    # Insert objective permissions
    print("\n➡️ Inserting Objective Permissions...")
    for role in tenant.get("roles", []):
        for objective in role.get("access", {}).get("objectives", []):
            for permission in objective.get("permissions", []):
                try:
                    pg_cursor.execute(
                        """
                        INSERT INTO workflow_solution_temp.objective_permissions (role_id, objective_id, permission_id)
                        VALUES (%s, %s, %s)
                        """,
                        (role["id"], objective["objective_id"], permission)
                    )
                    print(f"✅ Inserted Objective Permission: Role {role['id']} - Objective {objective['objective_id']} - Permission {permission}")
                except Exception as e:
                    print(f"❌ Error inserting objective permission {permission} for role {role['id']}: {e}")
    
    # ✅ Insert Entities
    print("\n➡️ Inserting Entities...")
    for entity in workflow_data.get("entities", []):
        try:
            pg_cursor.execute(
                "INSERT INTO workflow_solution_temp.entities (entity_id, name, version, status, type, attribute_prefix, description) VALUES (%s, %s, %s, %s, %s, %s, %s) ON CONFLICT DO NOTHING",
                (entity["id"], entity["name"], entity.get("version", "1.0"), entity["status"], entity["type"], entity.get("attributes_metadata", {}).get("attribute_prefix", ""), entity.get("description", ""))
            )
            print(f"✅ Inserted Entity: {entity['id']}")
        except Exception as e:
            print(f"❌ Error inserting entity {entity['id']}: {e}")

    # ✅ Insert Entity Attribute Metadata
    print("\n➡️ Inserting Entity Attribute Metadata...")
    for entity in workflow_data.get("entities", []):
        entity_id = entity["id"]
        for attr_id, attr_name in entity.get("attributes_metadata", {}).get("attribute_map", {}).items():
            required = attr_id in entity.get("attributes_metadata", {}).get("required_attributes", [])
            try:
                pg_cursor.execute(
                    "INSERT INTO workflow_solution_temp.entity_attribute_metadata (entity_id, attribute_id, attribute_name, required) VALUES (%s, %s, %s, %s) ON CONFLICT DO NOTHING",
                    (entity_id, attr_id, attr_name, required)
                )
                print(f"✅ Inserted Entity Attribute Metadata: {entity_id}.{attr_id}")
            except Exception as e:
                print(f"❌ Error inserting entity attribute metadata {attr_id}: {e}")

    # ✅ Insert Entity Attributes
    print("\n➡️ Inserting Entity Attributes...")
    for entity in workflow_data.get("entities", []):
        entity_id = entity["id"]
        for attr in entity.get("attributes", []):
            try:
                pg_cursor.execute(
                    """
                    INSERT INTO workflow_solution_temp.entity_attributes (
                        attribute_id, entity_id, name, display_name, datatype, version, status, required, reference_entity_id
                    ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s) ON CONFLICT DO NOTHING
                    """,
                    (
                        attr["id"], entity_id, attr["name"], attr["display_name"], attr["datatype"],
                        attr.get("version", "1.0"), attr["status"], attr.get("required", False),
                        attr.get("references")
                    )
                )
                print(f"✅ Inserted Entity Attribute: {entity_id}.{attr['id']}")
            except Exception as e:
                print(f"❌ Error inserting entity attribute {attr['id']}: {e}")

    # ✅ Insert Attribute Validations
    print("\n➡️ Inserting Attribute Validations...")
    for entity in workflow_data.get("entities", []):
        for attr in entity.get("attributes", []):
            attribute_id = attr["id"]
            for validation in attr.get("validations", []):
                try:
                    pg_cursor.execute(
                        """
                        INSERT INTO workflow_solution_temp.attribute_validations
                        (attribute_id, rule, expression) VALUES (%s, %s, %s) ON CONFLICT DO NOTHING
                        """,
                        (attribute_id, validation["rule"], validation.get("expression", None))
                    )
                    print(f"✅ Inserted Validation for {attribute_id}: {validation['rule']}")
                except Exception as e:
                    print(f"❌ Error inserting validation for {attribute_id}: {e}")

    # ✅ Insert Enum Values for Attributes
    print("\n➡️ Inserting Enum Values...")
    for entity in workflow_data.get("entities", []):
        for attr in entity.get("attributes", []):
            if attr["datatype"].lower() == "enum":
                attribute_id = attr["id"]
                for value in attr.get("values", []):
                    try:
                        pg_cursor.execute(
                            """
                            INSERT INTO workflow_solution_temp.attribute_enum_values
                            (attribute_id, value) VALUES (%s, %s) ON CONFLICT DO NOTHING
                            """,
                            (attribute_id, value)
                        )
                        print(f"✅ Inserted Enum Value for {attribute_id}: {value}")
                    except Exception as e:
                        print(f"❌ Error inserting enum value {value} for {attribute_id}: {e}")

    # ✅ Insert Entity Relationships
    print("\n➡️ Inserting Entity Relationships...")
    for entity in workflow_data.get("entities", []):
        for relationship in entity.get("relationships", []):
            try:
                pg_cursor.execute(
                    """
                    INSERT INTO workflow_solution_temp.entity_relationships
                    (source_entity_id, target_entity_id, relationship_type, source_attribute_id, target_attribute_id)
                    VALUES (%s, %s, %s, %s, %s) ON CONFLICT DO NOTHING
                    """,
                    (entity["id"], relationship["entity_id"], relationship["type"],
                    relationship["through_attribute"], relationship["to_attribute"])
                )
                print(f"✅ Inserted Relationship: {entity['id']} → {relationship['entity_id']}")
            except Exception as e:
                print(f"❌ Error inserting relationship from {entity['id']} to {relationship['entity_id']}: {e}")
    
    # ✅ Insert Permission Type & Capabilities
    print("\n➡️ Inserting Permission Capabilities...")

    for permission in workflow_data.get("permission_types", []):
        try:
            pg_cursor.execute(
                """
                INSERT INTO workflow_solution_temp.permission_types
                (description, permission, capabilities)
                VALUES (%s, %s, %s)
                RETURNING permission_id;
                """,
                (
                    permission.get("description", ""),  # Description
                    permission.get("id"),  # Permission name
                    json.dumps(permission.get("capabilities", []))  # Convert capabilities to JSON
                )
            )

            # ✅ Fetch inserted permission_id safely
            result = pg_cursor.fetchone()
            if result:
                inserted_permission_id = result[0]  # Extract the first value
                print(f"✅ Inserted Permission ID: {inserted_permission_id}")
            else:
                print(f"⚠️ No permission ID returned for {permission.get('id')}")

        except Exception as e:
            print(f"❌ Error inserting permission type {permission.get('id', 'unknown')}: {e}")

    # ✅ Step 2: Insert Global Objectives and Child Data Sequentially
    print("\n➡️ Inserting Global Objectives and Child Data Sequentially...")

    for go in workflow_data.get("global_objectives", []):
        try:
            # 1️⃣ Insert Global Objective
            pg_cursor.execute(
                """
                INSERT INTO workflow_solution_temp.global_objectives 
                (go_id, name, version, status, description) 
                VALUES (%s, %s, %s, %s, %s) 
                ON CONFLICT (go_id) DO NOTHING
                RETURNING go_id
                """,
                (go["id"], go["name"], go["version"], go["status"], go.get("description", ""))
            )
            result = pg_cursor.fetchone()
            global_objective_id = result[0] if result else go["id"]
            print(f"✅ Inserted Global Objective: {go['id']}")
# Process runtime metrics stack if it exists
            if "runtime_metrics_stack" in go:
             try:
                # Insert the main metrics stack record
                pg_cursor.execute(
                    """
                    INSERT INTO workflow_solution_temp.runtime_metrics_stack
                    (go_id, description, metrics_entity)
                    VALUES (%s, %s, %s)
                    RETURNING id
                    """,
                    (
                        global_objective_id,
                        go["runtime_metrics_stack"].get("description", ""),
                        go["runtime_metrics_stack"].get("metrics_entity", "")
                    )
                )
                metrics_stack_id = pg_cursor.fetchone()[0]
                print(f"✅ Inserted Runtime Metrics Stack for {go['id']}")
                
                # Insert metrics aggregation data
                metrics_record = go["runtime_metrics_stack"].get("metrics_record", {})
                if "aggregation_attributes" in metrics_record:
                    for attr_name, aggr_function in metrics_record["aggregation_attributes"].items():
                        try:
                            pg_cursor.execute(
                                """
                                INSERT INTO workflow_solution_temp.metrics_aggregation
                                (metrics_stack_id, attribute_name, aggregation_function)
                                VALUES (%s, %s, %s)
                                """,
                                (metrics_stack_id, attr_name, aggr_function)
                            )
                            print(f"✅ Inserted Metrics Aggregation: {attr_name}")
                        except Exception as e:
                            print(f"❌ Error inserting metrics aggregation {attr_name}: {e}")
                
                # Insert execution path tracking
                path_tracking = go["runtime_metrics_stack"].get("execution_path_tracking", {})
                path_analysis = path_tracking.get("path_analysis", {})
                
                try:
                    pg_cursor.execute(
                        """
                        INSERT INTO workflow_solution_temp.execution_path_tracking
                        (metrics_stack_id, enabled, store_complete_path, identify_bottlenecks, 
                         compare_to_historical, path_efficiency_function)
                        VALUES (%s, %s, %s, %s, %s, %s)
                        """,
                        (
                            metrics_stack_id,
                            path_tracking.get("enabled", True),
                            path_tracking.get("store_complete_path", True),
                            path_analysis.get("identify_bottlenecks", False),
                            path_analysis.get("compare_to_historical", False),
                            path_analysis.get("path_efficiency_score")
                        )
                    )
                    print(f"✅ Inserted Execution Path Tracking configuration")
                except Exception as e:
                    print(f"❌ Error inserting execution path tracking: {e}")
                
                # Insert metrics reporting
                reporting = go["runtime_metrics_stack"].get("reporting", {})
                notification_threshold = reporting.get("notification_threshold", {})
                
                try:
                    pg_cursor.execute(
                        """
                        INSERT INTO workflow_solution_temp.metrics_reporting
                        (metrics_stack_id, generate_execution_summary, store_summary_location,
                         error_count_threshold, duration_percentile)
                        VALUES (%s, %s, %s, %s, %s)
                        """,
                        (
                            metrics_stack_id,
                            reporting.get("generate_execution_summary", True),
                            reporting.get("store_summary_location"),
                            notification_threshold.get("error_count"),
                            notification_threshold.get("duration_percentile")
                        )
                    )
                    print(f"✅ Inserted Metrics Reporting configuration")
                except Exception as e:
                    print(f"❌ Error inserting metrics reporting: {e}")
                
             except Exception as e:
                print(f"❌ Error inserting runtime metrics stack for {go['id']}: {e}")
  

            # 2️⃣ Insert Input Stack
            if "input_stack" in go:
                try:
                    pg_cursor.execute(
                        """
                        INSERT INTO workflow_solution_temp.input_stack (go_id, description) 
                        VALUES (%s, %s) RETURNING id
                        """,
                        (global_objective_id, go["input_stack"]["description"])
                    )
                    input_stack_id = pg_cursor.fetchone()[0]
                    print(f"✅ Inserted Input Stack for {go['id']}")

                    # 3️⃣ Insert Input Items
                    for input_item in go["input_stack"]["inputs"]:
                        try:
                            pg_cursor.execute(
                                """
                                INSERT INTO workflow_solution_temp.input_items 
                                (id, input_stack_id, slot_id, contextual_id, entity_reference, attribute_reference, 
                                source_type, source_description, required) 
                                VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s)
                                """,
                                (input_item["id"], input_stack_id, input_item["slot_id"], input_item["contextual_id"],
                                input_item.get("entity_reference"), input_item.get("attribute_reference"),
                                input_item["source"]["type"], input_item["source"]["description"], input_item["required"])
                            )
                            print(f"✅ Inserted Input Item: {input_item['id']}")
                        except Exception as e:
                            print(f"❌ Error inserting input item {input_item['id']} for {go['id']}: {e}")

                    # 4️⃣ Insert System Functions for Input Stack
                    for function in go["input_stack"].get("system_functions", []):
                        try:
                            pg_cursor.execute(
                                """
                                INSERT INTO workflow_solution_temp.system_functions 
                                (function_id, function_name, function_type, stack_type, stack_id, parameters, output_to) 
                                VALUES (%s, %s, %s, %s, %s, %s, %s)
                                """,
                                (function["function_id"], function["function_name"], function["function_type"],
                                "input", input_stack_id, json.dumps(function.get("parameters", {})), function.get("output_to"))
                            )
                            print(f"✅ Inserted System Function: {function['function_id']}")
                        except Exception as e:
                            print(f"❌ Error inserting system function {function['function_id']} for {go['id']}: {e}")

                except Exception as e:
                    print(f"❌ Error inserting input stack for {go['id']}: {e}")

            # 5️⃣ Insert Output Stack
            if "output_stack" in go:
                try:
                    pg_cursor.execute(
                        """
                        INSERT INTO workflow_solution_temp.output_stack (go_id, description) 
                        VALUES (%s, %s) RETURNING id
                        """,
                        (global_objective_id, go["output_stack"]["description"])
                    )
                    output_stack_id = pg_cursor.fetchone()[0]
                    print(f"✅ Inserted Output Stack for {go['id']}")

                    # 6️⃣ Insert Output Items
                    for output_item in go["output_stack"]["outputs"]:
                        try:
                            pg_cursor.execute(
                                """
                                INSERT INTO workflow_solution_temp.output_items 
                                (id, output_stack_id, slot_id, contextual_id, output_entity, output_attribute, data_type) 
                                VALUES (%s, %s, %s, %s, %s, %s, %s)
                                """,
                                (output_item["id"], output_stack_id, output_item["slot_id"], output_item["contextual_id"],
                                output_item.get("output_entity"), output_item.get("output_attribute"), output_item["data_type"])
                            )
                            print(f"✅ Inserted Output Item: {output_item['id']}")
                        except Exception as e:
                            print(f"❌ Error inserting output item {output_item['id']} for {go['id']}: {e}")

                    # 7️⃣ Insert Output Triggers
                    for output_item in go["output_stack"]["outputs"]:
                        if "triggers" in output_item:
                            for trigger in output_item["triggers"]["items"]:
                                try:
                                    pg_cursor.execute(
                                        """
                                        INSERT INTO workflow_solution_temp.output_triggers 
                                        (id, output_item_id, output_stack_id, target_objective, target_input, mapping_type, 
                                        condition_type, condition_entity, condition_attribute, condition_operator, condition_value) 
                                        VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                                        """,
                                        (trigger["id"], output_item["id"], output_stack_id, go['id'],
                                        trigger["target_input"], trigger["mapping_type"],
                                        trigger.get("condition", {}).get("condition_type"),
                                        trigger.get("condition", {}).get("entity"),
                                        trigger.get("condition", {}).get("attribute"),
                                        trigger.get("condition", {}).get("operator"),
                                        trigger.get("condition", {}).get("value"))
                                    )
                                    print(f"✅ Inserted Output Trigger: {trigger['id']}")
                                except Exception as e:
                                    print(f"❌ Error inserting output trigger {trigger['id']} for {go['id']}: {e}")

                except Exception as e:
                    print(f"❌ Error inserting output stack for {go['id']}: {e}")
            
            # 8️⃣ Insert Data Mapping Stack
            if "data_mapping_stack" in go:
                try:
                    pg_cursor.execute(
                        """
                        INSERT INTO workflow_solution_temp.data_mapping_stack (go_id, description) 
                        VALUES (%s, %s) RETURNING id
                        """,
                        (go["id"], go["data_mapping_stack"]["description"])
                    )
                    mapping_stack_id = pg_cursor.fetchone()[0]
                    print(f"✅ Inserted Data Mapping Stack for {go['id']}")

                    # 9️⃣ Insert Data Mappings
                    for mapping in go["data_mapping_stack"].get("mappings", []):
                        try:
                            pg_cursor.execute(
                                """
                                INSERT INTO workflow_solution_temp.data_mappings 
                                (id, mapping_stack_id, source, target, mapping_type) 
                                VALUES (%s, %s, %s, %s, %s)
                                """,
                                (mapping["id"], mapping_stack_id, mapping["source"], mapping["target"], mapping["mapping_type"])
                            )
                            print(f"✅ Inserted Data Mapping: {mapping['id']}")
                        except Exception as e:
                            print(f"❌ Error inserting data mapping {mapping['id']} for {go['id']}: {e}")

                    # 🔟 Insert Mapping Rules
                    for rule in go["data_mapping_stack"].get("rules", []):
                        try:
                            pg_cursor.execute(
                                """
                                INSERT INTO workflow_solution_temp.mapping_rules 
                                (id, mapping_stack_id, description, condition_type, condition_entity, 
                                condition_attribute, condition_operator, condition_value, error_message) 
                                VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s)
                                """,
                                (rule["id"], mapping_stack_id, rule["description"],
                                rule["rule_condition"]["condition_type"], rule["rule_condition"]["entity"],
                                rule["rule_condition"]["attribute"], rule["rule_condition"]["operator"],
                                rule["rule_condition"]["value"], rule["error_message"])
                            )
                            print(f"✅ Inserted Mapping Rule: {rule['id']}")
                        except Exception as e:
                            print(f"❌ Error inserting mapping rule {rule['id']} for {go['id']}: {e}")

                except Exception as e:
                    print(f"❌ Error inserting data mapping stack for {go['id']}: {e}")

        except Exception as e:
            print(f"❌ Error processing Global Objective {go['id']}: {e}")

    # Insert Local Objectives
    print("\n➡️ Inserting Local Objectives and Associated Data...")
    
    # First pass: Insert all local objectives
    for lo in workflow_data.get("local_objectives", []):
        try:
            # Extract Global Objective ID from Contextual ID
            go_id = lo["contextual_id"].split('.')[0]
            workflow_source = lo.get("workflow_source", None)

            pg_cursor.execute(
                """
                INSERT INTO workflow_solution_temp.local_objectives 
                (lo_id, contextual_id, name, function_type, workflow_source, go_id) 
                VALUES (%s, %s, %s, %s, %s, %s) 
                RETURNING lo_id
                """,
                (lo["id"], lo["contextual_id"], lo["name"], lo["function_type"], workflow_source, go_id)
            )
            lo_id = pg_cursor.fetchone()[0]
            print(f"✅ Inserted Local Objective: {lo['id']}")
        except Exception as e:
            print(f"❌ Error inserting Local Objective {lo['id']}: {e}")

    # Second pass: Insert associated data (now that all LOs exist)
    for lo in workflow_data.get("local_objectives", []):
        try:
            lo_id = lo['id']

            # Insert Execution Pathway
            if "execution_pathway" in lo:
                try:
                    pg_cursor.execute(
                        """
                        INSERT INTO workflow_solution_temp.execution_pathways 
                        (lo_id, pathway_type, next_lo) 
                        VALUES (%s, %s, %s) 
                        ON CONFLICT (lo_id) DO NOTHING
                        """,
                        (lo_id, lo["execution_pathway"]["type"], lo["execution_pathway"].get("next_lo"))
                    )
                    print(f"✅ Inserted Execution Pathway for {lo_id}")
                    
                    # Insert Execution Pathway Conditions
                    execution_pathway = lo.get("execution_pathway", {})
                    if "conditions" in execution_pathway:
                        for condition in execution_pathway["conditions"]:
                            condition_data = condition.get("condition", {})
                            next_lo = condition.get("next_lo")
                            
                            try:
                                pg_cursor.execute(
                                    """
                                    INSERT INTO workflow_solution_temp.execution_pathway_conditions 
                                    (lo_id, condition_type, condition_entity, condition_attribute, 
                                    condition_operator, condition_value, next_lo) 
                                    VALUES (%s, %s, %s, %s, %s, %s, %s)
                                    """,
                                    (
                                        lo_id,
                                        condition_data.get("condition_type"),
                                        condition_data.get("entity"),
                                        condition_data.get("attribute"),
                                        condition_data.get("operator"),
                                        condition_data.get("value"),
                                        next_lo,
                                    )
                                )
                                print(f"✅ Inserted Execution Pathway Condition for {lo_id} → Next: {next_lo}")
                            except Exception as e:
                                print(f"❌ Error inserting execution pathway condition for {lo_id}: {e}")
                except Exception as e:
                    print(f"❌ Error inserting execution pathway for {lo_id}: {e}")

            # Insert Terminal Pathway if exists
            if "terminal_type" in lo:
                try:
                    pg_cursor.execute(
                        """
                        INSERT INTO workflow_solution_temp.terminal_pathways 
                        (lo_id, terminal_type) 
                        VALUES (%s, %s) 
                        ON CONFLICT (lo_id) DO NOTHING
                        """,
                        (lo_id, lo["terminal_type"])
                    )
                    print(f"✅ Inserted Terminal Pathway for {lo_id}")
                except Exception as e:
                    print(f"❌ Error inserting terminal pathway for {lo_id}: {e}")

            # Insert Agent Stack
            if "agent_stack" in lo:
                try:
                    pg_cursor.execute(
                        "INSERT INTO workflow_solution_temp.agent_stack (lo_id) VALUES (%s) RETURNING id",
                        (lo_id,)
                    )
                    agent_stack_id = pg_cursor.fetchone()[0]
                    print(f"✅ Inserted Agent Stack for {lo_id}")

                    # Insert Agent Rights
                    for agent in lo["agent_stack"].get("agents", []):
                        for right in agent.get("rights", []):
                            try:
                                pg_cursor.execute(
                                    """
                                    INSERT INTO workflow_solution_temp.agent_rights 
                                    (agent_stack_id, role_id, right_id) 
                                    VALUES (%s, %s, %s)
                                    """,
                                    (agent_stack_id, agent["role"], right)
                                )
                                print(f"✅ Inserted Agent Right: {agent['role']} → {right}")
                            except Exception as e:
                                print(f"❌ Error inserting agent right {right} for role {agent['role']}: {e}")
                except Exception as e:
                    print(f"❌ Error inserting agent stack for {lo_id}: {e}")

            # Insert Input Stack
            if "input_stack" in lo:
                try:
                    pg_cursor.execute(
                        """
                        INSERT INTO workflow_solution_temp.lo_input_stack
                        (lo_id, description)
                        VALUES (%s, %s)
                        RETURNING id
                        """,
                        (lo_id, lo["input_stack"].get("description", ""))
                    )
                    input_stack_id = pg_cursor.fetchone()[0]
                    print(f"✅ Inserted Input Stack for {lo_id}")

                    # Process inputs
                    if "inputs" in lo["input_stack"]:
                        for input_item in lo["input_stack"]["inputs"]:
                            try:
                                input_id = input_item["id"]
                                slot_id = input_item["slot_id"]
                                contextual_id = input_item["contextual_id"]
                                
                                # Handle source which can be string or dictionary
                                if isinstance(input_item["source"], dict):
                                    source_type = input_item["source"].get("type", "User")
                                    source_description = input_item["source"].get("description", "")
                                else:
                                    source_type = input_item["source"]
                                    source_description = ""
                                
                                required = input_item.get("required", False)
                                
                                # Insert input item
                                pg_cursor.execute(
                                    """
                                    INSERT INTO workflow_solution_temp.lo_input_items
                                    (id, input_stack_id, slot_id, contextual_id, source_type, source_description, required)
                                    VALUES (%s, %s, %s, %s, %s, %s, %s)
                                    """,
                                    (input_id, input_stack_id, slot_id, contextual_id, source_type, source_description, required)
                                )
                                print(f"✅ Inserted Input Item {input_id}")
                                
                                # Process validations if present
                                if "validations" in input_item:
                                    for validation in input_item["validations"]:
                                        try:
                                            rule = validation["rule"]
                                            rule_type = validation["rule_type"]
                                            entity = validation.get("entity")
                                            attribute = validation.get("attribute")
                                            validation_method = validation.get("validation_method")
                                            reference_date_source = validation.get("reference_date_source")
                                            allowed_values = Json(validation["allowed_values"]) if "allowed_values" in validation else None
                                            error_message = validation.get("error_message", "")
                                            
                                            # Insert validation
                                            pg_cursor.execute(
                                                """
                                                INSERT INTO workflow_solution_temp.lo_input_validations
                                                (input_item_id, input_stack_id, rule, rule_type, entity, attribute, 
                                                validation_method, reference_date_source, allowed_values, error_message)
                                                VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                                                """,
                                                (input_id, input_stack_id, rule, rule_type, entity, attribute, 
                                                validation_method, reference_date_source, allowed_values, error_message)
                                            )
                                            print(f"✅ Inserted Input Validation: {rule}")
                                        except Exception as e:
                                            print(f"❌ Error inserting validation for input {input_id}: {e}")
                            except Exception as e:
                                print(f"❌ Error inserting input item {input_id}: {e}")
                except Exception as e:
                    print(f"❌ Error inserting input stack for {lo_id}: {e}")

            # Insert Execution Rules
            if "execution_rules" in lo:
                for rule in lo["execution_rules"]:
                    try:
                        pg_cursor.execute(
                            """
                            INSERT INTO workflow_solution_temp.execution_rules 
                            (id, lo_id, contextual_id, description, structured_rule) 
                            VALUES (%s, %s, %s, %s, %s)
                            """,
                            (rule["id"], lo_id, rule["contextual_id"], rule.get("description", ""), 
                             json.dumps(rule.get("structured_rule", {})))
                        )
                        print(f"✅ Inserted Execution Rule: {rule['id']}")
                    except Exception as e:
                        print(f"❌ Error inserting execution rule {rule['id']} for {lo_id}: {e}")

            # Insert system functions
            if "system_functions" in lo:
                for func in lo["system_functions"]:
                    try:
                        pg_cursor.execute(
                            """
                            INSERT INTO workflow_solution_temp.lo_system_functions 
                            (function_id, lo_id, function_name, function_type, parameters, output_to) 
                            VALUES (%s, %s, %s, %s, %s, %s)
                            """,
                            (func["function_id"], lo_id, func["function_name"], func["function_type"],
                             json.dumps(func.get("parameters", {})), func.get("output_to"))
                        )
                        print(f"✅ Inserted System Function: {func['function_id']}")
                    except Exception as e:
                        print(f"❌ Error inserting system function {func['function_id']} for {lo_id}: {e}")

            # Insert Output Stack
            if "output_stack" in lo:
                try:
                    pg_cursor.execute(
                        """
                        INSERT INTO workflow_solution_temp.lo_output_stack (lo_id, description) 
                        VALUES (%s, %s) RETURNING id
                        """,
                        (lo_id, lo["output_stack"].get("description", ""))
                    )
                    output_stack_id = pg_cursor.fetchone()[0]
                    print(f"✅ Inserted Output Stack for {lo_id}")

                    # Insert Output Items
                    for output_item in lo["output_stack"]["outputs"]:
                        try:
                            # Handle different source formats
                            source = output_item.get("source", "System")
                            if isinstance(source, dict):
                                source = json.dumps(source)
                            
                            pg_cursor.execute(
                                """
                                INSERT INTO workflow_solution_temp.lo_output_items 
                                (id, output_stack_id, slot_id, contextual_id, source) 
                                VALUES (%s, %s, %s, %s, %s)
                                """,
                                (output_item["id"], output_stack_id, output_item["slot_id"], 
                                 output_item["contextual_id"], source)
                            )
                            output_item_id = output_item["id"]
                            print(f"✅ Inserted Output Item: {output_item_id}")
                            
                            # Insert triggers if present
                            if "triggers" in output_item and "items" in output_item["triggers"]:
                                for trigger in output_item["triggers"]["items"]:
                                    try:
                                        trigger_id = trigger["id"]
                                        target_objective = trigger["target_objective"]
                                        target_input = trigger["target_input"]
                                        mapping_type = trigger["mapping_type"]
                                        
                                        pg_cursor.execute(
                                            """
                                            INSERT INTO workflow_solution_temp.lo_output_triggers
                                            (id, output_item_id, output_stack_id, target_objective, target_input, mapping_type)
                                            VALUES (%s, %s, %s, %s, %s, %s)
                                            """,
                                            (trigger_id, output_item_id, output_stack_id, target_objective, target_input, mapping_type)
                                        )
                                        print(f"✅ Inserted Output Trigger: {trigger_id}")
                                    except Exception as e:
                                        print(f"❌ Error inserting output trigger {trigger_id}: {e}")
                        except Exception as e:
                            print(f"❌ Error inserting output item {output_item['id']} for {lo_id}: {e}")
                except Exception as e:
                    print(f"❌ Error inserting output stack for {lo_id}: {e}")

            # Insert data mapping stack
            if "data_mapping_stack" in lo:
                try:
                    pg_cursor.execute(
                        """
                        INSERT INTO workflow_solution_temp.lo_data_mapping_stack (lo_id, description) 
                        VALUES (%s, %s) RETURNING id
                        """,
                        (lo_id, lo["data_mapping_stack"].get("description", ""))
                    )
                    mapping_stack_id = pg_cursor.fetchone()[0]
                    print(f"✅ Inserted Data Mapping Stack for {lo_id}")

                    # Insert mappings
                    if "mappings" in lo["data_mapping_stack"]:
                        for mapping in lo["data_mapping_stack"]["mappings"]:
                            try:
                                pg_cursor.execute(
                                    """
                                    INSERT INTO workflow_solution_temp.lo_data_mappings 
                                    (id, mapping_stack_id, source, target, mapping_type) 
                                    VALUES (%s, %s, %s, %s, %s)
                                    """,
                                    (mapping["id"], mapping_stack_id, mapping["source"], 
                                     mapping["target"], mapping["mapping_type"])
                                )
                                print(f"✅ Inserted Data Mapping: {mapping['id']}")
                            except Exception as e:
                                print(f"❌ Error inserting data mapping {mapping['id']}: {e}")
                except Exception as e:
                    print(f"❌ Error inserting data mapping stack for {lo_id}: {e}")

            # Insert success message if present
            if "success_message" in lo:
                try:
                    pg_cursor.execute(
                        """
                        INSERT INTO workflow_solution_temp.success_messages (lo_id, message) 
                        VALUES (%s, %s)
                        """,
                        (lo_id, lo["success_message"])
                    )
                    print(f"✅ Inserted Success Message for {lo_id}")
                except Exception as e:
                    print(f"❌ Error inserting success message for {lo_id}: {e}")

            # Insert UI stack if present
            if "ui_stack" in lo:
                try:
                    pg_cursor.execute(
                        """
                        INSERT INTO workflow_solution_temp.ui_stack 
                        (lo_id, type, status, description) 
                        VALUES (%s, %s, %s, %s)
                        """,
                        (lo_id, lo["ui_stack"]["type"], lo["ui_stack"]["status"], 
                         lo["ui_stack"].get("description", ""))
                    )
                    print(f"✅ Inserted UI Stack for {lo_id}")
                except Exception as e:
                    print(f"❌ Error inserting UI stack for {lo_id}: {e}")

        except Exception as e:
            print(f"❌ Error processing Local Objective {lo_id}: {e}")
            traceback.print_exc()

    # ✅ Verify Data in PostgreSQL (optional)
    print("\n🔎 Verifying Data in PostgreSQL...")

    def count_records(table_name):
        pg_cursor.execute(f"SELECT COUNT(*) FROM workflow_solution_temp.{table_name};")
        count = pg_cursor.fetchone()[0]
        return count

    for table in tables:
        try:
            count = count_records(table)
            print(f"📊 {table}: {count} records")
        except Exception as e:
            print(f"❌ Error counting records in {table}: {e}")

    # ✅ Close Connections
    pg_cursor.close()
    pg_conn.close()
    mongo_client.close()

    print("\n✅ Migration Completed Successfully!")

except Exception as e:
    print(f"❌ Fatal Error: {e}")
    traceback.print_exc()
    
    # Close connections if they exist
    try:
        if 'pg_cursor' in locals() and pg_cursor:
            pg_cursor.close()
        if 'pg_conn' in locals() and pg_conn:
            pg_conn.close()
        if 'mongo_client' in locals() and mongo_client:
            mongo_client.close()
    except Exception as cleanup_error:
        print(f"❌ Error during cleanup: {cleanup_error}")
