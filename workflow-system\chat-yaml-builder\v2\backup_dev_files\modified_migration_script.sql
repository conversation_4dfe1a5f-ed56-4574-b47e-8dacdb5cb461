-- Migration script for YAML Builder v2
-- This script adds the necessary tables and columns to the workflow_runtime schema
-- to support the YAML Builder v2 implementation.

-- Global Objective (GO) related changes
ALTER TABLE workflow_runtime.global_objectives ADD COLUMN IF NOT EXISTS process_mining_schema jsonb NULL;
ALTER TABLE workflow_runtime.global_objectives ADD COLUMN IF NOT EXISTS performance_metadata jsonb NULL;
ALTER TABLE workflow_runtime.global_objectives ADD COLUMN IF NOT EXISTS version_type VARCHAR(10) NULL DEFAULT 'v2'::character varying;
ALTER TABLE workflow_runtime.global_objectives ADD COLUMN IF NOT EXISTS status VARCHAR(20) NULL DEFAULT 'active'::character varying;
ALTER TABLE workflow_runtime.global_objectives ADD COLUMN IF NOT EXISTS tenant_id VARCHAR(50) NULL DEFAULT 't001'::character varying;
ALTER TABLE workflow_runtime.global_objectives ADD COLUMN IF NOT EXISTS deleted_mark BOOLEAN NULL DEFAULT false;

CREATE TABLE IF NOT EXISTS workflow_runtime.go_lo_mapping (
    mapping_id VARCHAR(100) NOT NULL,
    go_id VARCHAR(50) NULL,
    lo_id VARCHAR(50) NOT NULL,
    sequence_number integer NULL DEFAULT 0,
    PRIMARY KEY (mapping_id)
);

CREATE TABLE IF NOT EXISTS workflow_runtime.go_performance_metrics (
    metric_id VARCHAR(100) NOT NULL,
    go_id VARCHAR(50) NULL,
    name VARCHAR(100) NOT NULL,
    description text NULL,
    formula text NOT NULL,
    target_value float8 NULL,
    unit VARCHAR(50) NULL,
    threshold_warning float8 NULL,
    threshold_critical float8 NULL,
    PRIMARY KEY (metric_id)
);

-- Local Objective (LO) related changes
ALTER TABLE workflow_runtime.local_objectives ALTER COLUMN go_id DROP NOT NULL;
ALTER TABLE workflow_runtime.local_objectives ADD COLUMN IF NOT EXISTS description text NULL;
ALTER TABLE workflow_runtime.local_objectives ADD COLUMN IF NOT EXISTS ui_stack jsonb NULL;
ALTER TABLE workflow_runtime.local_objectives ADD COLUMN IF NOT EXISTS mapping_stack jsonb NULL;
ALTER TABLE workflow_runtime.local_objectives ADD COLUMN IF NOT EXISTS version_type VARCHAR(10) NULL DEFAULT 'v2'::character varying;
ALTER TABLE workflow_runtime.local_objectives ADD COLUMN IF NOT EXISTS contextual_id VARCHAR(100) NULL;
ALTER TABLE workflow_runtime.local_objectives ADD COLUMN IF NOT EXISTS function_type VARCHAR(50) NULL DEFAULT 'standard'::character varying;
ALTER TABLE workflow_runtime.local_objectives ADD COLUMN IF NOT EXISTS workflow_source VARCHAR(50) NULL DEFAULT 'system'::character varying;
ALTER TABLE workflow_runtime.local_objectives ADD COLUMN IF NOT EXISTS system_function VARCHAR(100) NULL;

CREATE TABLE IF NOT EXISTS workflow_runtime.lo_data_mapping_stack (
    stack_id VARCHAR(100) NOT NULL,
    lo_id VARCHAR(50) NOT NULL,
    name VARCHAR(100) NOT NULL,
    description text NULL,
    PRIMARY KEY (stack_id)
);

CREATE TABLE IF NOT EXISTS workflow_runtime.lo_data_mappings (
    mapping_id VARCHAR(100) NOT NULL,
    stack_id VARCHAR(100) NOT NULL,
    source_type VARCHAR(50) NOT NULL,
    source_id VARCHAR(100) NOT NULL,
    target_type VARCHAR(50) NOT NULL,
    target_id VARCHAR(100) NOT NULL,
    mapping_type VARCHAR(50) NOT NULL,
    mapping_config jsonb NULL,
    PRIMARY KEY (mapping_id)
);

CREATE TABLE IF NOT EXISTS workflow_runtime.lo_input_stack (
    stack_id VARCHAR(100) NOT NULL,
    lo_id VARCHAR(50) NOT NULL,
    name VARCHAR(100) NOT NULL,
    description text NULL,
    PRIMARY KEY (stack_id)
);

CREATE TABLE IF NOT EXISTS workflow_runtime.lo_input_items (
    item_id VARCHAR(100) NOT NULL,
    lo_id VARCHAR(50) NOT NULL,
    name VARCHAR(100) NOT NULL,
    type VARCHAR(50) NOT NULL,
    required boolean NULL DEFAULT false,
    default_value text NULL,
    ui_control VARCHAR(50) NULL DEFAULT 'text'::character varying,
    help_text text NULL,
    dependency_info jsonb NULL,
    PRIMARY KEY (item_id)
);

CREATE TABLE IF NOT EXISTS workflow_runtime.lo_output_stack (
    stack_id VARCHAR(100) NOT NULL,
    lo_id VARCHAR(50) NOT NULL,
    name VARCHAR(100) NOT NULL,
    description text NULL,
    PRIMARY KEY (stack_id)
);

CREATE TABLE IF NOT EXISTS workflow_runtime.lo_output_items (
    item_id VARCHAR(100) NOT NULL,
    lo_id VARCHAR(50) NOT NULL,
    name VARCHAR(100) NOT NULL,
    type VARCHAR(50) NOT NULL,
    PRIMARY KEY (item_id)
);

CREATE TABLE IF NOT EXISTS workflow_runtime.lo_nested_functions (
    function_id VARCHAR(100) NOT NULL,
    lo_id VARCHAR(50) NOT NULL,
    function_name VARCHAR(100) NOT NULL,
    function_type VARCHAR(50) NOT NULL,
    parameters jsonb NULL,
    output_to VARCHAR(100) NULL,
    PRIMARY KEY (function_id)
);

CREATE TABLE IF NOT EXISTS workflow_runtime.execution_pathways (
    pathway_id VARCHAR(100) NOT NULL,
    lo_id VARCHAR(50) NOT NULL,
    name VARCHAR(100) NOT NULL,
    description text NULL,
    sequence_number integer NULL DEFAULT 0,
    PRIMARY KEY (pathway_id)
);

CREATE TABLE IF NOT EXISTS workflow_runtime.execution_pathway_conditions (
    condition_id VARCHAR(100) NOT NULL,
    pathway_id VARCHAR(100) NOT NULL,
    condition_type VARCHAR(50) NOT NULL,
    condition_value text NOT NULL,
    sequence_number integer NULL DEFAULT 0,
    PRIMARY KEY (condition_id)
);

CREATE TABLE IF NOT EXISTS workflow_runtime.agent_stack (
    agent_id VARCHAR(100) NOT NULL,
    lo_id VARCHAR(50) NOT NULL,
    name VARCHAR(100) NOT NULL,
    description text NULL,
    agent_type VARCHAR(50) NOT NULL,
    agent_config jsonb NULL,
    PRIMARY KEY (agent_id)
);

CREATE TABLE IF NOT EXISTS workflow_runtime.agent_rights (
    right_id VARCHAR(100) NOT NULL,
    agent_id VARCHAR(100) NOT NULL,
    right_type VARCHAR(50) NOT NULL,
    right_value text NOT NULL,
    PRIMARY KEY (right_id)
);

-- Role-related changes
CREATE TABLE IF NOT EXISTS workflow_runtime.role_inheritance (
    role_id VARCHAR(50) NOT NULL,
    parent_role_id VARCHAR(50) NOT NULL,
    PRIMARY KEY (role_id, parent_role_id)
);

ALTER TABLE workflow_runtime.role_permissions ADD COLUMN IF NOT EXISTS permission_name VARCHAR(100) NOT NULL;
ALTER TABLE workflow_runtime.roles ALTER COLUMN version_type TYPE VARCHAR(10);

-- Entity-related changes
ALTER TABLE workflow_runtime.entities ADD COLUMN IF NOT EXISTS metadata jsonb NULL;
ALTER TABLE workflow_runtime.entities ADD COLUMN IF NOT EXISTS lifecycle_management jsonb NULL;
ALTER TABLE workflow_runtime.entities ADD COLUMN IF NOT EXISTS version_type VARCHAR(10) NULL DEFAULT 'v2'::character varying;

ALTER TABLE workflow_runtime.entity_attributes ADD COLUMN IF NOT EXISTS calculated_field boolean NULL DEFAULT false;
ALTER TABLE workflow_runtime.entity_attributes ADD COLUMN IF NOT EXISTS calculation_formula text NULL;
ALTER TABLE workflow_runtime.entity_attributes ADD COLUMN IF NOT EXISTS dependencies jsonb NULL;

ALTER TABLE workflow_runtime.entity_business_rules ALTER COLUMN condition DROP NOT NULL;
ALTER TABLE workflow_runtime.entity_business_rules ALTER COLUMN action DROP NOT NULL;
