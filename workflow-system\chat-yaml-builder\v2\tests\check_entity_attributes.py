#!/usr/bin/env python3
"""
Script to check if default values and property names are being correctly parsed.
"""

import sys
import os
import json
import importlib.util

# Add parent directory to path to import modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Import entity_parser module
def import_module_from_file(module_name, file_path):
    spec = importlib.util.spec_from_file_location(module_name, file_path)
    module = importlib.util.module_from_spec(spec)
    spec.loader.exec_module(module)
    return module

# Find entity_parser.py
parser_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'parsers', 'entity_parser.py')
entity_parser = import_module_from_file('entity_parser', parser_path)

# Find sample file
sample_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'samples', 'sample_entity_output2.txt')
with open(sample_path, 'r') as f:
    sample_text = f.read()

# Parse entities
entities_data, warnings = entity_parser.parse_entities(sample_text)

# Check Employee entity attributes
employee_entity = entities_data['entities'].get('Employee', {})
employee_attrs = employee_entity.get('attributes', {})

print("=== Employee Entity Attributes ===")
for attr_name, attr_data in employee_attrs.items():
    if attr_name in ['probationDays', 'minSalary', 'status', 'hireDate']:
        print(f"Attribute: {attr_name}")
        print(f"  Data: {json.dumps(attr_data, indent=2)}")

# Check specific attributes mentioned in the requirements
print("\n=== Specific Attributes ===")
if 'probationDays' in employee_attrs and 'property_value' in employee_attrs['probationDays']:
    print(f"Employee.probationDays PROPERTY_NAME = {employee_attrs['probationDays']['property_value']}")
else:
    print("Employee.probationDays PROPERTY_NAME not found")

if 'minSalary' in employee_attrs and 'property_value' in employee_attrs['minSalary']:
    print(f"Employee.minSalary PROPERTY_NAME = {employee_attrs['minSalary']['property_value']}")
else:
    print("Employee.minSalary PROPERTY_NAME not found")

if 'status' in employee_attrs and 'default' in employee_attrs['status']:
    print(f"Employee.status DEFAULT_VALUE = {employee_attrs['status']['default']}")
else:
    print("Employee.status DEFAULT_VALUE not found")

if 'hireDate' in employee_attrs and 'default' in employee_attrs['hireDate']:
    print(f"Employee.hireDate DEFAULT_VALUE = {employee_attrs['hireDate']['default']}")
else:
    print("Employee.hireDate DEFAULT_VALUE not found")

# Check validations
print("\n=== Employee Validations ===")
employee_validations = employee_entity.get('validations', {})
print(f"Found {len(employee_validations)} validations for Employee entity")
for val_name, val_data in employee_validations.items():
    print(f"Validation: {val_name}")
    print(f"  Data: {json.dumps(val_data, indent=2)}")

# Check specific validations mentioned in the requirements
print("\n=== Specific Validations ===")
email_unique_found = False
email_pattern_found = False
hiredate_before_found = False
salary_greater_found = False

for val_name, val_data in employee_validations.items():
    if val_data.get('attribute') == 'email' and 'unique' in val_data.get('constraint', ''):
        email_unique_found = True
        print(f"Found validation: Employee.email must be unique")
    
    if val_data.get('attribute') == 'email' and ('match pattern' in val_data.get('constraint', '') or val_data.get('constraint') == 'match'):
        email_pattern_found = True
        print(f"Found validation: Employee.email must match pattern")
    
    if val_data.get('attribute') == 'hireDate' and 'before current date' in val_data.get('constraint', ''):
        hiredate_before_found = True
        print(f"Found validation: Employee.hireDate must be before current date")
    
    if val_data.get('attribute') == 'salary' and 'greater than 0' in val_data.get('constraint', ''):
        salary_greater_found = True
        print(f"Found validation: Employee.salary must be greater than 0")

if not email_unique_found:
    print("Validation 'Employee.email must be unique' not found")
if not email_pattern_found:
    print("Validation 'Employee.email must match pattern' not found")
if not hiredate_before_found:
    print("Validation 'Employee.hireDate must be before current date' not found")
if not salary_greater_found:
    print("Validation 'Employee.salary must be greater than 0' not found")
