"""
Schema Analyzer for YAML Builder v2

This module provides functionality for analyzing database schemas and copying structures.
"""

import os
import sys
import logging
from typing import Dict, List, Tuple, Any, Optional

# Import database utilities
from db_utils import execute_query

# Set up logging
logger = logging.getLogger('schema_analyzer')

def analyze_schema(source_schema: str, target_schema: str) -> <PERSON>ple[bool, List[str]]:
    """
    Analyze a source schema and copy its structure to a target schema.
    
    Args:
        source_schema: Source schema name
        target_schema: Target schema name
        
    Returns:
        Tuple containing:
            - Boolean indicating if analysis was successful
            - List of messages (warnings, errors, or success messages)
    """
    messages = []
    
    try:
        logger.info(f"Analyzing schema {source_schema} and copying to {target_schema}")
        
        # Get all tables in the source schema
        success, query_messages, tables = execute_query(
            """
            SELECT table_name
            FROM information_schema.tables
            WHERE table_schema = %s
            AND table_type = 'BASE TABLE'
            ORDER BY table_name
            """,
            (source_schema,)
        )
        
        if not success:
            messages.extend(query_messages)
            return False, messages
        
        if not tables:
            messages.append(f"No tables found in schema {source_schema}")
            return True, messages
        
        # Process each table
        for table in tables:
            table_name = table[0]
            
            # Get table definition
            success, query_messages, columns = execute_query(
                """
                SELECT column_name, data_type, character_maximum_length, 
                       is_nullable, column_default, numeric_precision, numeric_scale
                FROM information_schema.columns
                WHERE table_schema = %s
                AND table_name = %s
                ORDER BY ordinal_position
                """,
                (source_schema, table_name)
            )
            
            if not success:
                messages.extend(query_messages)
                return False, messages
            
            # Create table in target schema
            column_defs = []
            for column in columns:
                column_name = column[0]
                data_type = column[1]
                max_length = column[2]
                is_nullable = column[3]
                default_value = column[4]
                numeric_precision = column[5]
                numeric_scale = column[6]
                
                # Build column definition
                column_def = f'"{column_name}" {data_type}'
                
                # Add length for character types
                if max_length is not None:
                    column_def += f'({max_length})'
                
                # Add precision and scale for numeric types
                if data_type.upper() in ('NUMERIC', 'DECIMAL') and numeric_precision is not None:
                    if numeric_scale is not None:
                        column_def += f'({numeric_precision},{numeric_scale})'
                    else:
                        column_def += f'({numeric_precision})'
                
                # Add nullable constraint
                if is_nullable == 'NO':
                    column_def += ' NOT NULL'
                
                # Add default value
                if default_value is not None:
                    column_def += f' DEFAULT {default_value}'
                
                column_defs.append(column_def)
            
            # Create table
            success, query_messages, _ = execute_query(
                f"""
                CREATE TABLE {target_schema}."{table_name}" (
                    {', '.join(column_defs)}
                )
                """
            )
            
            if not success:
                messages.extend(query_messages)
                return False, messages
            
            messages.append(f"Created table {target_schema}.{table_name}")
            
            # Get primary key
            success, query_messages, pk_constraints = execute_query(
                """
                SELECT kcu.column_name, tc.constraint_name
                FROM information_schema.table_constraints tc
                JOIN information_schema.key_column_usage kcu
                ON tc.constraint_name = kcu.constraint_name
                AND tc.table_schema = kcu.table_schema
                WHERE tc.constraint_type = 'PRIMARY KEY'
                AND tc.table_schema = %s
                AND tc.table_name = %s
                ORDER BY kcu.ordinal_position
                """,
                (source_schema, table_name)
            )
            
            if not success:
                messages.extend(query_messages)
                return False, messages
            
            # Add primary key constraint
            if pk_constraints:
                pk_columns = [f'"{constraint[0]}"' for constraint in pk_constraints]
                pk_name = pk_constraints[0][1]
                
                success, query_messages, _ = execute_query(
                    f"""
                    ALTER TABLE {target_schema}."{table_name}"
                    ADD CONSTRAINT "{pk_name}" PRIMARY KEY ({', '.join(pk_columns)})
                    """
                )
                
                if not success:
                    messages.extend(query_messages)
                    return False, messages
                
                messages.append(f"Added primary key constraint to {target_schema}.{table_name}")
            
            # Get unique constraints
            success, query_messages, unique_constraints = execute_query(
                """
                SELECT tc.constraint_name, kcu.column_name
                FROM information_schema.table_constraints tc
                JOIN information_schema.key_column_usage kcu
                ON tc.constraint_name = kcu.constraint_name
                AND tc.table_schema = kcu.table_schema
                WHERE tc.constraint_type = 'UNIQUE'
                AND tc.table_schema = %s
                AND tc.table_name = %s
                ORDER BY tc.constraint_name, kcu.ordinal_position
                """,
                (source_schema, table_name)
            )
            
            if not success:
                messages.extend(query_messages)
                return False, messages
            
            # Add unique constraints
            if unique_constraints:
                # Group by constraint name
                unique_dict = {}
                for constraint in unique_constraints:
                    constraint_name = constraint[0]
                    column_name = constraint[1]
                    
                    if constraint_name not in unique_dict:
                        unique_dict[constraint_name] = []
                    
                    unique_dict[constraint_name].append(f'"{column_name}"')
                
                # Add each unique constraint
                for constraint_name, columns in unique_dict.items():
                    success, query_messages, _ = execute_query(
                        f"""
                        ALTER TABLE {target_schema}."{table_name}"
                        ADD CONSTRAINT "{constraint_name}" UNIQUE ({', '.join(columns)})
                        """
                    )
                    
                    if not success:
                        messages.extend(query_messages)
                        return False, messages
                    
                    messages.append(f"Added unique constraint {constraint_name} to {target_schema}.{table_name}")
            
            # Get foreign key constraints
            success, query_messages, fk_constraints = execute_query(
                """
                SELECT
                    tc.constraint_name,
                    kcu.column_name,
                    ccu.table_name AS foreign_table_name,
                    ccu.column_name AS foreign_column_name,
                    rc.update_rule,
                    rc.delete_rule
                FROM information_schema.table_constraints tc
                JOIN information_schema.key_column_usage kcu
                ON tc.constraint_name = kcu.constraint_name
                AND tc.table_schema = kcu.table_schema
                JOIN information_schema.constraint_column_usage ccu
                ON ccu.constraint_name = tc.constraint_name
                AND ccu.table_schema = tc.table_schema
                JOIN information_schema.referential_constraints rc
                ON tc.constraint_name = rc.constraint_name
                AND tc.table_schema = rc.constraint_schema
                WHERE tc.constraint_type = 'FOREIGN KEY'
                AND tc.table_schema = %s
                AND tc.table_name = %s
                ORDER BY tc.constraint_name, kcu.ordinal_position
                """,
                (source_schema, table_name)
            )
            
            if not success:
                messages.extend(query_messages)
                return False, messages
            
            # Add foreign key constraints
            if fk_constraints:
                # Group by constraint name
                fk_dict = {}
                for constraint in fk_constraints:
                    constraint_name = constraint[0]
                    column_name = constraint[1]
                    foreign_table_name = constraint[2]
                    foreign_column_name = constraint[3]
                    update_rule = constraint[4]
                    delete_rule = constraint[5]
                    
                    if constraint_name not in fk_dict:
                        fk_dict[constraint_name] = {
                            'columns': [],
                            'foreign_table': foreign_table_name,
                            'foreign_columns': [],
                            'update_rule': update_rule,
                            'delete_rule': delete_rule
                        }
                    
                    fk_dict[constraint_name]['columns'].append(f'"{column_name}"')
                    fk_dict[constraint_name]['foreign_columns'].append(f'"{foreign_column_name}"')
                
                # Add each foreign key constraint
                for constraint_name, fk_info in fk_dict.items():
                    success, query_messages, _ = execute_query(
                        f"""
                        ALTER TABLE {target_schema}."{table_name}"
                        ADD CONSTRAINT "{constraint_name}" FOREIGN KEY ({', '.join(fk_info['columns'])})
                        REFERENCES {target_schema}."{fk_info['foreign_table']}" ({', '.join(fk_info['foreign_columns'])})
                        ON UPDATE {fk_info['update_rule']} ON DELETE {fk_info['delete_rule']}
                        """
                    )
                    
                    if not success:
                        messages.extend(query_messages)
                        return False, messages
                    
                    messages.append(f"Added foreign key constraint {constraint_name} to {target_schema}.{table_name}")
            
            # Get check constraints
            success, query_messages, check_constraints = execute_query(
                """
                SELECT constraint_name, check_clause
                FROM information_schema.check_constraints cc
                JOIN information_schema.table_constraints tc
                ON cc.constraint_name = tc.constraint_name
                AND cc.constraint_schema = tc.constraint_schema
                WHERE tc.table_schema = %s
                AND tc.table_name = %s
                """,
                (source_schema, table_name)
            )
            
            if not success:
                messages.extend(query_messages)
                return False, messages
            
            # Add check constraints
            for constraint in check_constraints:
                constraint_name = constraint[0]
                check_clause = constraint[1]
                
                success, query_messages, _ = execute_query(
                    f"""
                    ALTER TABLE {target_schema}."{table_name}"
                    ADD CONSTRAINT "{constraint_name}" CHECK ({check_clause})
                    """
                )
                
                if not success:
                    messages.extend(query_messages)
                    return False, messages
                
                messages.append(f"Added check constraint {constraint_name} to {target_schema}.{table_name}")
            
            # Get indexes
            success, query_messages, indexes = execute_query(
                f"""
                SELECT indexname, indexdef
                FROM pg_indexes
                WHERE schemaname = %s
                AND tablename = %s
                AND indexname NOT IN (
                    SELECT constraint_name
                    FROM information_schema.table_constraints
                    WHERE table_schema = %s
                    AND table_name = %s
                )
                """,
                (source_schema, table_name, source_schema, table_name)
            )
            
            if not success:
                messages.extend(query_messages)
                return False, messages
            
            # Add indexes
            for index in indexes:
                index_name = index[0]
                index_def = index[1]
                
                # Replace schema name in index definition
                index_def = index_def.replace(f'"{source_schema}"', f'"{target_schema}"')
                
                success, query_messages, _ = execute_query(index_def)
                
                if not success:
                    messages.extend(query_messages)
                    return False, messages
                
                messages.append(f"Added index {index_name} to {target_schema}.{table_name}")
        
        messages.append(f"Successfully analyzed schema {source_schema} and copied to {target_schema}")
        return True, messages
    except Exception as e:
        logger.error(f"Error analyzing schema {source_schema}: {str(e)}", exc_info=True)
        messages.append(f"Error analyzing schema {source_schema}: {str(e)}")
        return False, messages
