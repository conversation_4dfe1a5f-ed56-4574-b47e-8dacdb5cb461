"""
Query entities from the database.
"""

from db_utils import execute_query

def main():
    # Query entities table
    print("Querying workflow_temp.entities table")
    success, messages, entities = execute_query(
        "SELECT entity_id, name, description FROM workflow_temp.entities",
        schema_name="workflow_temp"
    )
    
    if success and entities:
        print("Entities:")
        for entity in entities:
            print(f"  {entity}")
    else:
        print("No entities found or query failed")
        if messages:
            print("Messages:")
            for message in messages:
                print(f"  {message}")

if __name__ == "__main__":
    main()
