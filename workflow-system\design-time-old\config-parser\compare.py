import yaml

def load_yaml(file_path):
    """Loads YAML from a file."""
    try:
        with open(file_path, "r", encoding="utf-8") as file:
            return yaml.safe_load(file)
    except yaml.YAMLError as e:
        print(f"❌ YAML Error in {file_path}: {e}")
        return None
    except Exception as e:
        print(f"❌ Error loading {file_path}: {e}")
        return None

def extract_keys(yaml_data):
    """Extracts relevant keys from YAML (tenants, GOs, LOs)."""
    extracted = {
        "tenants": set(),
        "global_objectives": set(),
        "local_objectives": set()
    }

    if "tenant" in yaml_data:
        extracted["tenants"].add(yaml_data["tenant"].get("id", "Unknown"))

    if "global_objectives" in yaml_data:
        for go in yaml_data["global_objectives"]:
            extracted["global_objectives"].add(go["id"])
            if "local_objectives" in go:
                for lo in go["local_objectives"]:
                    extracted["local_objectives"].add(lo["id"])

    return extracted

def compare_yaml_sets(base_yaml, compare_yaml):
    """Compares extracted sets from two YAMLs and finds missing/excess objects."""
    base_keys = extract_keys(base_yaml)
    compare_keys = extract_keys(compare_yaml)

    differences = {}

    for key in base_keys.keys():
        missing = base_keys[key] - compare_keys[key]
        extra = compare_keys[key] - base_keys[key]
        differences[key] = {"missing": missing, "extra": extra}

    return differences

def display_differences(differences):
    """Displays missing and extra entries clearly."""
    for key, diff in differences.items():
        if diff["missing"]:
            print(f"⚠️ Missing {key}: {', '.join(diff['missing'])}")
        if diff["extra"]:
            print(f"➕ Extra {key}: {', '.join(diff['extra'])}")

# Load YAML files
yaml_file_1 = "workflow.yaml"  # Replace with actual file path
yaml_file_2 = "workflow1.yaml"  # Replace with actual file path

yaml_data_1 = load_yaml(yaml_file_1)
yaml_data_2 = load_yaml(yaml_file_2)

if yaml_data_1 and yaml_data_2:
    differences = compare_yaml_sets(yaml_data_1, yaml_data_2)
    display_differences(differences)
