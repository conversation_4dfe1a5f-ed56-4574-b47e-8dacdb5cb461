2025-06-23 11:33:59,455 - inserters.v3.roles.constants_inserter - INFO - Starting deploy_single_constant_to_workflow_temp for constant: 1003
2025-06-23 11:52:06,499 - inserters.v3.roles.constants_inserter - INFO - Starting deploy_single_constant_to_workflow_temp for constant: 1003
2025-06-23 11:56:50,588 - inserters.v3.roles.constants_inserter - INFO - Starting deploy_single_constant_to_workflow_temp for constant: 1003
2025-06-23 11:57:27,635 - inserters.v3.roles.constants_inserter - INFO - Starting deploy_single_constant_to_workflow_temp for constant: 1003
2025-06-23 12:18:44,672 - inserters.v3.roles.constants_inserter - INFO - Starting deploy_single_constant_to_workflow_temp for constant: 1003
2025-06-23 12:19:33,203 - inserters.v3.roles.constants_inserter - INFO - Starting deploy_single_constant_to_workflow_temp for constant: 1004
2025-06-23 12:20:43,091 - inserters.v3.roles.constants_inserter - INFO - Starting deploy_single_constant_to_workflow_temp for constant: 1004
2025-06-23 14:03:51,055 - inserters.v3.roles.constants_inserter - INFO - Starting process_mongo_constants_to_workflow_temp
2025-06-23 14:03:51,058 - inserters.v3.roles.constants_inserter - INFO - Found 1 constants with status 'draft' in MongoDB
2025-06-23 14:03:51,066 - inserters.v3.roles.constants_inserter - INFO - Starting insert_constant_to_workflow_temp for constant: allow_override
2025-06-23 14:03:51,066 - inserters.v3.roles.constants_inserter - INFO - Field validation passed: 3/3 required fields, 11/11 optional fields
2025-06-23 14:03:51,088 - inserters.v3.roles.constants_inserter - INFO - Incremented constant ID from 1004 to 1003
2025-06-23 14:03:51,089 - inserters.v3.roles.constants_inserter - INFO - Additional MongoDB fields preserved: {'constant_status': 'new', 'changes_detected': [], 'version': 1, 'original_created_at': '2025-06-23T09:02:49.471376', 'original_updated_at': '2025-06-23T09:02:49.695971', 'original_created_by': 'Tarun', 'original_updated_by': 'Tarun', 'entity_reference_validation': {'entity_exists': True, 'attribute_exists': True, 'entity_name': '', 'attribute_name': '', 'warnings': []}}
2025-06-23 14:03:51,090 - inserters.v3.roles.constants_inserter - INFO - Successfully inserted constant to workflow_temp with ID: 1003
2025-06-23 14:03:51,093 - inserters.v3.roles.constants_inserter - INFO - Updated MongoDB status to deployed_to_temp for constant: 1003
2025-06-23 14:03:51,093 - inserters.v3.roles.constants_inserter - INFO - Completed process_mongo_constants_to_workflow_temp: 1 successful, 0 failed
2025-06-23 14:09:02,094 - inserters.v3.roles.constants_inserter - INFO - Starting process_mongo_constants_to_workflow_temp
2025-06-23 14:09:02,097 - inserters.v3.roles.constants_inserter - INFO - Found 0 constants with status 'draft' in MongoDB
2025-06-23 14:09:02,097 - inserters.v3.roles.constants_inserter - INFO - Completed process_mongo_constants_to_workflow_temp: 0 successful, 0 failed
2025-06-23 14:32:34,399 - inserters.v3.roles.constants_inserter - INFO - Starting process_mongo_constants_to_workflow_temp
2025-06-23 14:32:34,402 - inserters.v3.roles.constants_inserter - INFO - Found 0 constants with status 'draft' in MongoDB
2025-06-23 14:32:34,402 - inserters.v3.roles.constants_inserter - INFO - Completed process_mongo_constants_to_workflow_temp: 0 successful, 0 failed
2025-06-23 14:38:53,910 - inserters.v3.roles.constants_inserter - INFO - Starting process_mongo_constants_to_workflow_temp
2025-06-23 14:38:53,913 - inserters.v3.roles.constants_inserter - INFO - Found 0 constants with status 'draft' in MongoDB
2025-06-23 14:38:53,913 - inserters.v3.roles.constants_inserter - INFO - Completed process_mongo_constants_to_workflow_temp: 0 successful, 0 failed
2025-06-24 04:45:03,482 - inserters.v3.roles.constants_inserter - INFO - Starting process_mongo_constants_to_workflow_temp
2025-06-24 04:45:03,485 - inserters.v3.roles.constants_inserter - INFO - Found 0 constants with status 'draft' in MongoDB
2025-06-24 04:45:03,485 - inserters.v3.roles.constants_inserter - INFO - Completed process_mongo_constants_to_workflow_temp: 0 successful, 0 failed
2025-06-24 11:42:53,239 - inserters.v3.roles.constants_inserter - INFO - Starting process_mongo_constants_to_workflow_runtime
2025-06-24 11:42:53,245 - inserters.v3.roles.constants_inserter - INFO - Found 1 deployed_to_temp + 0 draft = 1 total constants in MongoDB
2025-06-24 11:42:53,252 - inserters.v3.roles.constants_inserter - INFO - Starting insert_constant_to_workflow_runtime for constant: allow_override
2025-06-24 11:42:53,252 - inserters.v3.roles.constants_inserter - INFO - Field validation passed: 3/3 required fields, 11/11 optional fields
2025-06-24 11:42:53,276 - inserters.v3.roles.constants_inserter - INFO - Incremented constant ID from 1004 to 1004
2025-06-24 11:42:53,276 - inserters.v3.roles.constants_inserter - INFO - Additional MongoDB fields preserved: {'constant_status': 'new', 'changes_detected': [], 'version': 2, 'original_created_at': '2025-06-23T09:02:49.471376', 'original_updated_at': '2025-06-23T14:03:51.090866', 'original_created_by': 'Tarun', 'original_updated_by': 'Tarun', 'entity_reference_validation': {'entity_exists': True, 'attribute_exists': True, 'entity_name': '', 'attribute_name': '', 'warnings': []}}
2025-06-24 11:42:53,278 - inserters.v3.roles.constants_inserter - INFO - Successfully inserted constant to workflow_runtime with ID: 1004
2025-06-24 11:42:53,280 - inserters.v3.roles.constants_inserter - INFO - Updated MongoDB status to deployed_to_production for constant: 1004
2025-06-24 11:42:53,281 - inserters.v3.roles.constants_inserter - INFO - Completed process_mongo_constants_to_workflow_runtime: 1 successful, 0 failed
2025-06-24 13:14:31,318 - inserters.v3.roles.constants_inserter - INFO - Starting process_mongo_constants_to_workflow_temp
2025-06-24 13:14:31,321 - inserters.v3.roles.constants_inserter - INFO - Found 1 constants with status 'draft' in MongoDB
2025-06-24 13:14:31,329 - inserters.v3.roles.constants_inserter - INFO - Starting insert_constant_to_workflow_temp for constant: MAX_TEST_ITEMS
2025-06-24 13:14:31,329 - inserters.v3.roles.constants_inserter - INFO - Field validation passed: 3/3 required fields, 11/11 optional fields
2025-06-24 13:14:31,354 - inserters.v3.roles.constants_inserter - INFO - Incremented constant ID from 1005 to 1005
2025-06-24 13:14:31,354 - inserters.v3.roles.constants_inserter - INFO - Additional MongoDB fields preserved: {'constant_status': 'new', 'changes_detected': [], 'version': 1, 'original_created_at': '2025-06-24T12:45:38.607968', 'original_updated_at': '2025-06-24T12:45:38.607976', 'original_created_by': 'Tarun', 'original_updated_by': 'Tarun', 'entity_reference_validation': {'entity_exists': True, 'attribute_exists': True, 'entity_name': '', 'attribute_name': '', 'warnings': []}}
2025-06-24 13:14:31,355 - inserters.v3.roles.constants_inserter - INFO - Successfully inserted constant to workflow_temp with ID: 1005
2025-06-24 13:14:31,359 - inserters.v3.roles.constants_inserter - INFO - Updated MongoDB status to deployed_to_temp for constant: 1005
2025-06-24 13:14:31,359 - inserters.v3.roles.constants_inserter - INFO - Completed process_mongo_constants_to_workflow_temp: 1 successful, 0 failed
