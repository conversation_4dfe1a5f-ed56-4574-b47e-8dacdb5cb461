{"timestamp": "2025-06-24T04:45:02.868609", "operation": "deploy_single_ui_property_to_workflow_temp", "input_data": {"ui_property_id": "UI001"}, "result": {"success": false, "error": "UI property UI001 not found with status draft", "ui_property_id": "UI001", "required_status": "draft"}, "status": "error"}
{"timestamp": "2025-06-24T04:45:03.010730", "operation": "process_mongo_ui_properties_to_workflow_temp", "input_data": {"schema": "workflow_temp"}, "result": {"total_processed": 0, "successful_inserts": 0, "failed_inserts": 0, "details": []}, "status": "success"}
{"timestamp": "2025-06-24T11:43:35.727776", "operation": "insert_ui_property_to_workflow_runtime", "input_data": {"_id": "68552fce229f6505fc03bb6b", "ui_property_id": "UI8", "entity_id": "E15", "attribute_id": "E15.At1", "control_type": "text_input", "display_format": "EMP###", "input_mask": "EMP###", "placeholder_text": "Auto-generated", "auto_complete": false, "read_only": true, "validation_display": "inline", "help_text_position": "below", "label": "Employee ID", "required_indicator": false, "natural_language": "Entity: Employee\nAttribute: employeeId\nControl Type: text_input\nLabel: Employee ID\nDisplay Format: EMP###\nInput Mask: EMP###\nPlaceholder Text: Auto-generated\nRead Only: true\nValidation Display: inline\nHelp Text Position: below", "version": 2, "status": "deployed_to_temp", "created_at": "2025-06-20T09:54:22.597847", "updated_at": "2025-06-20T12:19:39.187272", "created_by": "Tarun", "updated_by": "Tarun", "ui_property_status": "new", "changes_detected": []}, "result": {"success": true, "inserted_id": "UI8", "schema": "workflow_runtime", "ui_property_id": "UI8", "entity_id": "E15", "attribute_id": "E15.At1", "label": "Employee ID", "original_ui_property_id": "UI5"}, "status": "success"}
{"timestamp": "2025-06-24T11:43:35.755215", "operation": "insert_ui_property_to_workflow_runtime", "input_data": {"_id": "685903f05e423f11979d4093", "ui_property_id": "UI9", "entity_id": "E13", "attribute_id": "E13.At11", "control_type": "number_input", "display_format": "##.# days", "input_mask": "##.#", "placeholder_text": "Enter entitlement", "auto_complete": false, "read_only": false, "validation_display": "inline", "help_text_position": "below", "label": "Total Leave Entitlement", "required_indicator": false, "natural_language": "Entity: Employee\nAttribute: totalLeaveEntitlement\nControl Type: number_input\nLabel: Total Leave Entitlement\nDisplay Format: ##.# days\nInput Mask: ##.#\nPlaceholder Text: Enter entitlement\nValidation Display: inline\nHelp Text Position: below", "version": 5, "status": "deployed_to_temp", "created_at": "2025-06-23T09:00:43.903023", "updated_at": "2025-06-23T14:03:50.593286", "created_by": "Tarun", "updated_by": "Tarun", "ui_property_status": "existing", "changes_detected": []}, "result": {"success": true, "inserted_id": "UI9", "schema": "workflow_runtime", "ui_property_id": "UI9", "entity_id": "E13", "attribute_id": "E13.At11", "label": "Total Leave Entitlement", "original_ui_property_id": "UI1"}, "status": "success"}
{"timestamp": "2025-06-24T11:43:35.757789", "operation": "process_mongo_ui_properties_to_workflow_runtime", "input_data": {"schema": "workflow_runtime"}, "result": {"total_processed": 2, "successful_inserts": 2, "failed_inserts": 0, "details": [{"ui_property_id": "UI8", "entity_id": "E15", "attribute_id": "E15.At1", "label": "Employee ID", "status": "success", "details": {"success": true, "inserted_id": "UI8", "schema": "workflow_runtime", "ui_property_id": "UI8", "entity_id": "E15", "attribute_id": "E15.At1", "label": "Employee ID", "original_ui_property_id": "UI5"}}, {"ui_property_id": "UI9", "entity_id": "E13", "attribute_id": "E13.At11", "label": "Total Leave Entitlement", "status": "success", "details": {"success": true, "inserted_id": "UI9", "schema": "workflow_runtime", "ui_property_id": "UI9", "entity_id": "E13", "attribute_id": "E13.At11", "label": "Total Leave Entitlement", "original_ui_property_id": "UI1"}}]}, "status": "success"}
