{"timestamp": "2025-06-23T14:03:48.296838", "operation": "insert_entity_to_workflow_temp", "input_data": {"_id": "6854208d644ab97edf294a5b", "entity_id": "E26", "name": "Employee", "display_name": "Employee", "tenant_id": "T1001", "tenant_name": "Alpha Corp", "business_domain": "Human Resources", "category": "Employee Management", "tags": ["employee", "staff", "personnel"], "archival_strategy": "archive_only", "icon": "user", "colour_theme": "green", "version": 1, "status": "draft", "type": "master", "description": "Represents an employee in the organization", "table_name": "e13_Employee", "natural_language": "Tenant: Alpha Corp\n\nEmployee has employeeId^PK, firstName, lastName, email, phone, departmentId^FK, hireDate, annualLeaveBalance, sickLeaveBalance, personalLeaveBalance, totalLeaveEntitlement.\n\nEntity: Employee\n- Entity Name: Employee\n- Display Name: Employee\n- Type: master\n- Description: Represents an employee in the organization\n- Business Domain: Human Resources\n- Category: Employee Management\n- Tags: employee, staff, personnel\n- Archival Strategy: archive_only\n- Icon: user\n- Colour Theme: green", "created_at": "2025-06-19T14:37:01.860351", "updated_at": "2025-06-20T11:34:42.214059", "created_by": "Tarun", "updated_by": "Tarun", "entity_status": "new", "changes_detected": [], "icon_type": "text", "icon_content": ""}, "result": {"success": true, "inserted_id": "E26", "schema": "workflow_temp", "entity_id": "E26", "name": "Employee", "attributes_inserted": 0, "original_entity_id": "E13"}, "status": "success"}
{"timestamp": "2025-06-23T14:03:48.299726", "operation": "process_mongo_entities_to_workflow_temp", "input_data": {"schema": "workflow_temp"}, "result": {"total_processed": 1, "successful_inserts": 1, "failed_inserts": 0, "details": [{"entity_id": "E26", "status": "success", "details": {"success": true, "inserted_id": "E26", "schema": "workflow_temp", "entity_id": "E26", "name": "Employee", "attributes_inserted": 0, "original_entity_id": "E13"}}]}, "status": "success"}
{"timestamp": "2025-06-23T14:08:59.789277", "operation": "process_mongo_entities_to_workflow_temp", "input_data": {"schema": "workflow_temp"}, "result": {"total_processed": 0, "successful_inserts": 0, "failed_inserts": 0, "details": []}, "status": "success"}
{"timestamp": "2025-06-23T14:32:31.999994", "operation": "process_mongo_entities_to_workflow_temp", "input_data": {"schema": "workflow_temp"}, "result": {"total_processed": 0, "successful_inserts": 0, "failed_inserts": 0, "details": []}, "status": "success"}
{"timestamp": "2025-06-23T14:38:51.701736", "operation": "process_mongo_entities_to_workflow_temp", "input_data": {"schema": "workflow_temp"}, "result": {"total_processed": 0, "successful_inserts": 0, "failed_inserts": 0, "details": []}, "status": "success"}
