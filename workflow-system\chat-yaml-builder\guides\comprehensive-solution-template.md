# Comprehensive Solution Definition Template

## 1. ROLE DEFINITIONS

### Standard Role Definition Template

```
Role [RoleName] ([role_id]) inherits [ParentRole]:
- Create: [Entity1], [Entity2] (attr1^deny, attr2^deny)
- Read: [Entity1], [Entity2] (attr3^deny, attr4^deny)
- Update: [Entity1] (attr5^deny), [Entity2]
- GO: [go001] as [RoleType], [go002] as [RoleType]
- Scope: [ScopeLevel]
- Classification: [RoleClassification]
- Special: [SpecialConditions]
```

### Role Hover/Click Details

```
Role Details: [RoleName]

Core Information:
ID: [role_id]
Version: [version]
Display Name: [DisplayName]
Category: [category]
Classification: [classification]
Status: [Active/Draft/Deprecated]
Risk Level: [High/Medium/Low]

Lifecycle:
Created: [Date] by [Name]
Last Modified: [Date] by [Name]
Approval Chain: [approver_list]

Permission Summary:
Total Permissions: [count]
Entity Access: [create_count/read_count/update_count]
GO Roles: [originator_count/owner_count/sponsor_count]

Usage Statistics:
Active Users: [count]
Last Access: [Date]
Success Rate: [percentage]
```

## 2. ENTITY DEFINITIONS

### Entity Definition Template

```
[EntityName] has [attribute1]^PK, [attribute2], [attribute3] (enum1, enum2), [attribute4]^FK, [attribute5][derived].
```

### Relationship Definition Template

```
* [EntityName] has [relationship-type] relationship with [RelatedEntity] using [EntityName].[attributeX]^PK to [RelatedEntity].[attributeY]^FK
```

### Entity Hover/Click Details

```
Entity Hover: [EntityName]

System Properties:
ID: [entity_id]
Created: [Date] by [Name]
Last Modified: [Date] by [Name]

Editable Properties:
Display Name: [DisplayName]
Type: [EntityType]
Description: [Description]
```

### Attribute Hover/Click Details

```
Attribute Hover: [attributeName]

System Properties:
ID: [attribute_id]
Version: [version]
Key: [PK/FK/Normal]
Created: [Date] by [Name]
Last Modified: [Date] by [Name]

Editable Properties:
Display Name: [DisplayName]
Data Type: [DataType]
Type: [Mandatory/Optional]
Format: [FormatPattern]
Values: [AllowedValues]
Default: [DefaultValue]
Validation: [ValidationType]
Error Message: [ErrorMessage]
Description: [Description]
```

### Relationship Hover/Click Details

```
Relationship Hover: [RelationshipType]

Relationship Properties:
On Delete: [DeleteBehavior]
On Update: [UpdateBehavior]
Foreign Key Type: [KeyType]
```

### Constants & Configuration Template

```
* [EntityName].[attribute] [PROPERTY_NAME] = [value]
* [EntityName].[attribute] DEFAULT_VALUE = [value]
```

### Validation Template

```
* [EntityName].[attribute] must be [constraint]
```

### Business Rules Template

```
BusinessRule [ruleID] for [EntityName]:
* [EntityName].[condition] must be [state] to [action]
* [EntityName].[attribute1] must be [relation] [EntityName].[attribute2]
```

### Calculated Fields Template

```
[EntityName] has [attribute1], [attribute2], [calculatedAttribute][derived].

CalculatedField [fieldID] for [EntityName].[calculatedAttribute]:
* Formula: [calculation logic]
* Logic Layer: [implementation layer]
* Caching: [caching strategy]
* Dependencies: [EntityName].[attribute1], [EntityName].[attribute2]
```

### Hierarchical Dependencies Template

```
[EntityName] has [parent1Id]^FK, [parent2Id]^FK with [parent1] constrains [parent2].

* [EntityName].[parent2Id] must belong to selected [EntityName].[parent1Id]
```

### Data Lifecycle Management Template

```
### Archive Strategy
* Archive Strategy for [EntityName]:
  - Trigger: [Time-based/Event-based/Manual]
  - Criteria: [Conditions for archiving]
  - Retention: [Duration/Policy]
  - Storage: [Storage solution]
  - Access Pattern: [How archived data is accessed]
  - Restoration: [Process for restoring if needed]

### Purge Rules
* Purge Rule for [EntityName]:
  - Trigger: [Time-based/Event-based/Manual]
  - Criteria: [Conditions for permanent deletion]
  - Approvals: [Required approval workflow]
  - Audit: [Audit trail requirements]
  - Dependencies: [Related entities affected]

### History Tracking
* History Tracking for [EntityName]:
  - Tracked Attributes: [list of attributes with entity prefix]
  - Tracking Method: [Audit table/Temporal tables/Event sourcing]
  - Granularity: [Change level/Snapshot level]
  - Retention: [Duration policy]
  - Access Control: [Who can access history]
```

### Circuit Components Template

```
### Synthetic Data:
* Synthetic: [EntityName].[attribute1], [EntityName].[attribute2]

Synthetic Hover:
System Properties:
Description: "[Generator description]"

### Classification:

#### Public Data:
* Public: [EntityName].[field1], [EntityName].[field2], [EntityName].[field3]

Public Data Hover:
System Properties:
Description: "[Visibility description]"

#### Internal Data:
* Internal: [EntityName].[field1], [EntityName].[field2], [EntityName].[field3]

Internal Data Hover:
System Properties:
Description: "[Visibility description]"

#### Confidential Data:
* Confidential: [EntityName].[field1], [EntityName].[field2], [EntityName].[field3]

Confidential Data Hover:
System Properties:
Description: "[Visibility description]"

### Distribution Strategy:

#### [StrategyName]:
* Distribution: [Distribution details]

Distribution Hover:
System Properties:
Description: "[Strategy description]"

### Loading Strategy:

#### [LoadingType] for [EntityName].[relationships/attributes]:
* Loading: [Loading details]

Loading Hover:
System Properties:
Description: "[Loading description]"
```

### Organizational Placement Template (Post-Solution Development)

```
### Business Rule Placement
BusinessRule [ruleID] Placement:
* Local Objective: [Function]
* Global Objective: [Workflow]
* Chapter: [Sub-module]
* Book: [Module]
* Tenant: [Business]

### Workflow Placement
* Workflow [WorkflowName] Placement:
  - Global Objective: [Workflow name in system]
  - Book: [Module]
  - Chapter: [Sub-module]
  - Tenant: [Business]

### Entity Placement
* Entity Placement for [EntityName]:
  - Tenant: [Business]
  - Book: [Module]
  - Chapter: [Sub-module]
  - Global Objectives: [Workflow1, Workflow2]
  - Local Objectives: [Function1, Function2]
```

## 3. GLOBAL OBJECTIVE (GO) DEFINITIONS

### GO Core Template

```
## [ProcessName]

Core Metadata:
- id: "[go_id]"
- name: "[Verb Phrase describing the process]"
- version: "[version_number]"
- status: "[Active/Draft/Deprecated]"
- description: "[Brief description of the business process]"
- primary_entity: "[Main entity this process operates on]"
- classification: "[Business process classification]"

Process Ownership:
- Originator: [Role who can initiate this process OR "System" for automated initiation]
- Process Owner: [Role responsible for process]
- Business Sponsor: [Department/role providing business justification]

Trigger Definition: (MANDATORY for system-initiated GOs)
- Trigger Type: [scheduled/event-driven/data-change/threshold]
- Trigger Condition: [Specific condition that initiates the process]
- Trigger Frequency: [daily/hourly/on-demand/etc.]
- Trigger Parameters: [Any specific parameters for the trigger]

Data Management:

Input Stack:
- [Entity1] with attribute1*, attribute2, attribute3 (value1, value2, value3); [Entity2] with attribute1*, attribute2.

Input Mapping Stack: (EXPLICIT mapping for EVERY usage)
- [Entity1].[attribute1] maps to [lo_id] → [lo_internal_entity].[attribute]
- [Entity1].[attribute1] maps to [lo_id] → [lo_internal_entity].[attribute]  # Same input, different LO
- [Entity1].[attribute2] maps to [lo_id] → [lo_internal_entity].[attribute]
- [Entity2].[attribute1] maps to [lo_id] → [lo_internal_entity].[attribute]
- [Entity2].[attribute2] maps to [lo_id] → [lo_internal_entity].[attribute]

Output Stack:
- [Entity1] with attribute1*, attribute2, attribute3 (value1, value2, value3); [Entity2] with attribute1*, attribute2.

Output Mapping Stack: (EXPLICIT mapping for EVERY target GO)
- [Entity1].[attribute1] maps to [go_id] "[GO Name]" → [target_entity].[attribute]
- [Entity1].[attribute1] maps to [go_id] "[GO Name]" → [target_entity].[attribute]  # Same output, different GO
- [Entity1].[attribute2] maps to [go_id] "[GO Name]" → [target_entity].[attribute]
- [Entity2].[attribute1] maps to [go_id] "[GO Name]" → [target_entity].[attribute]

Data Constraints:

DB Stack:
- [Entity1].[attribute1] (datatype) is mandatory and unique. Error message: "[error_message]"
- [Entity1].[attribute2] (datatype) is optional. Error message: "[error_message]"
- [Entity2].[attribute1] (datatype) must be one of [allowed_values]. Error message: "[error_message]"

Process Definition:

Process Flow: (ALL LOs must be defined here)
1. [lo_id]: [LO_Name] [HUMAN/SYSTEM] - [Brief description]
   a. If [Entity].[attribute] [operator] [value], route to [lo_id]
   b. If [Entity].[attribute] [operator] [value], route to [lo_id]
2. [lo_id]: [LO_Name] [HUMAN/SYSTEM] - [Brief description]
   a. If [Entity].[attribute] [operator] [value], route to [lo_id]
3. [lo_id]: [LO_Name] [HUMAN/SYSTEM] - [Brief description]
   ...

Parallel Flows:
- After [lo_id] ([LO_Name]):  # Starting point
  * [lo_id] ([LO_Name]) - [Brief description]  # These must be defined above
  * [lo_id] ([LO_Name]) - [Brief description]  # These must be defined above
- Join at: [lo_id] ([LO_Name])  # Join point (must be defined above)

Rollback Pathways:
- [lo_id] ([LO_Name]) ↔ [lo_id] ([Rollback_LO_Name])  # Both must be defined in Process Flow
- [lo_id] ([LO_Name]) ↔ [lo_id] ([Rollback_LO_Name])  # Both must be defined in Process Flow
- Full rollback pathway: [lo_id] → [lo_id] → ... → [lo_id]  # All LOs in pathway

Business Rules: (Each rule mapped to specific LO)
1. [Rule description] - Enforced by [lo_id]
2. [Rule description] - Implemented by [lo_id]
3. [Rule description] - Validated by [lo_id]
4. [Rule description] - Applied at [lo_id]
...

Integration Points:

GO Relationships:
- GO receives input from [go_id] "[GO Name]" for [Entity] processing.
- GO sends output to [go_id] "[GO Name]" for [Entity] processing.

External Systems:
- [System Name]: [Integration type] integration for [purpose]
- [API Name]: [Integration direction] for [data exchange]

Performance Metadata:
- cycle_time: "[average_time]"
- number_of_pathways: [count]
- volume_metrics:
  * average_volume: [number]
  * peak_volume: [number]
  * unit: "[per_month/day/hour]"

Process Mining Schema:

Event Log Specification:
- case_id: "[primary_entity].[unique_identifier]"
- activity: "[lo_id].[LO_name]"
- event_type: "[start/complete/abort/rollback]"
- timestamp: "[ISO-8601 datetime]"
- resource: "[role/system_executing]"
- duration: "[milliseconds]"
- attributes:
  * entity_state: "[json_snapshot]"
  * input_values: "[input_parameters]"
  * output_values: "[output_results]"
  * execution_status: "[success/failure/pending]"
  * error_details: "[error_message_if_any]"

Performance Discovery Metrics:
- pathway_frequency:
  * "[pathway_description]":
    - frequency: [count]
    - percentage: [percentage]
    - average_duration: "[time]"
    - success_rate: [percentage]
- bottleneck_analysis:
  * "[lo_id]":
    - average_wait_time: "[duration]"
    - queue_length: [count]
    - resource_utilization: [percentage]
    - failure_rate: [percentage]
- resource_patterns:
  * "[role/system]":
    - active_hours: "[time_ranges]"
    - peak_load_periods: "[time_ranges]"
    - concurrent_executions: [max_count]

Conformance Analytics:
- compliance_rate: [percentage]
- execution_variance:
  * "[lo_id]":
    - expected_duration: "[time]"
    - actual_duration_range: "[min-max]"
    - variance_causes: ["list_of_identified_causes"]
- exception_patterns:
  * "input_timeout":
    - frequency: [count]
    - affected_pathways: ["list_of_pathways"]
    - recovery_success_rate: [percentage]
  * "validation_failure":
    - frequency: [count]
    - most_common_failures: ["list_of_validation_types"]
    - resolution_time: "[average_duration]"
  * "system_error":
    - frequency: [count]
    - error_categories: ["list_of_error_types"]
    - automatic_recovery_rate: [percentage]

Advanced Process Intelligence:
- process_health_score:
  * performance_score: [0-100]
  * compliance_score: [0-100]
  * efficiency_score: [0-100]
  * overall_health: [0-100]
- prediction_models:
  * completion_time_forecast:
    - algorithm: "[ML_algorithm_used]"
    - accuracy: [percentage]
    - confidence_interval: [range]
  * failure_prediction:
    - algorithm: "[ML_algorithm_used]"
    - precision: [percentage]
    - recall: [percentage]
- optimization_insights:
  * bottleneck_elimination: ["list_of_recommendations"]
  * resource_reallocation: ["list_of_suggestions"]
  * pathway_optimization: ["list_of_improvements"]

Rollback Analytics:
- rollback_frequency: [percentage]
- rollback_success_rate: [percentage]
- rollback_triggers:
  * "[trigger_type]":
    - frequency: [count]
    - average_impact_scope: [count]
    - average_recovery_time: "[duration]"
- rollback_pathways:
  * "[rollback_pathway]":
    - frequency: [count]
    - success_rate: [percentage]
    - average_completion_time: "[duration]"

Sample Data:
- [Entity1].[attribute1]: "[sample_value1]", "[sample_value2]", "[sample_value3]"
- [Entity1].[attribute2]: [number_value1], [number_value2], [number_value3]
- [Entity2].[attribute1]: "[sample_value1]", "[sample_value2]", "[sample_value3]"
```

## 4. LOCAL OBJECTIVE (LO) DEFINITIONS

### LO Core Template

```
## [LO_Name]

id: "[lo_id]"
contextual_id: "[go_id].[lo_id]"
name: "[Human-readable LO name]"
version: "[version_number]"
status: "[Active/Draft/Deprecated]"
workflow_source: "[origin/intermediate/terminal]"
function_type: "[Create/Update/Read/Delete]"

*[Role] has [rights] rights*

*Inputs: [Entity1] with attribute1*, attribute2* [depends on: attribute1], attribute3* (value1, value2), attribute4 [optional], instructions [info]*
* System loads [Entity1].instructions [info] with constant "[constant_key]".
* System generates [Entity].[attribute] using [function_name] with [parameters].
* System calculates [Entity].[attribute] using [function_name] with [parameters].
* System defaults [Entity].[attribute] to "[value]".
* System populates [Entity].[attribute2] [depends on: attribute1] using [function_name] from [ReferenceEntity].
* System displays instructions [info] with key "[instruction_key]".
* [Entity].[attribute] depends on [Entity].[other_attribute] using [function_name].

*Outputs: [Entity1] with attribute1, attribute2; [Entity2] with attribute1*
* System returns [Entity].[attribute] for downstream operations.
* System captures [Entity].[attribute] for audit purposes.
* System transforms [Entity].[attribute] for display using [function_name].

*DB Stack:*
* [Entity].[attribute] (datatype) is mandatory and unique. Error message: "[error_message]"
* [Entity].[attribute] (datatype) is optional. Error message: "[error_message]"
* [Entity].[attribute] must be one of [allowed_values]. Error message: "[error_message]"
* [Entity].[attribute] must follow [pattern/rule]. Error message: "[error_message]"
* [Entity].[attribute] must reference [ReferenceEntity].[reference_attribute]. Error message: "[error_message]"

*UI Stack:*
* [Entity].[attribute] displays as [ui_control] with [properties].
* [Entity].[attribute] is [editable/read-only] depending on [condition].
* [Entity].[attribute] visibility depends on [condition].
* [Entity].[attribute] [depends on: other_attribute] populates from [ReferenceEntity] filtered by [filter_condition].
* System provides contextual help for [Entity].[attribute] explaining "[help_text]".

*Mapping Stack:*
* [this_lo].output.[attribute] maps to [next_lo].input.[attribute] using [mapping_type].
* [Entity1].[attribute] transforms to [Entity2].[attribute] using [function].
* System preserves [Entity].[attribute] across multiple LOs for workflow continuity.

Execution pathway:
* When [Entity].[attribute] [operator] [value], route to [specific_LO_name].
* When [Entity].[attribute] [operator] [value], route to [specific_LO_name].

Synthetic values:
* [Entity].[attribute]: "[sample_value1]", "[sample_value2]", "[sample_value3]"
* [Entity].[attribute]: [number_value1], [number_value2], [number_value3]
```

### Common LO Patterns and Reusable Components

```
### Standard System Functions

generate_id: Creates unique identifiers with prefixes
* System generates [Entity].[attribute] using generate_id with prefix "[prefix]".

subtract_days: Calculates the difference between dates
* System calculates [Entity].[attribute] using subtract_days with startDate, endDate.

current_timestamp: Captures the current date and time
* System captures [Entity].[attribute] using current_timestamp.

to_uppercase: Transforms text to uppercase
* System transforms [Entity].[attribute] using to_uppercase with [text].

fetch_filtered_records: Retrieves records based on conditions
* System filters [Entity] records using fetch_filtered_records with [filter_entity].[filter_attribute] equals [source_entity].[source_attribute].

populate_dependent_dropdown: Populates dependent dropdown options
* System populates [Entity].[attribute] [depends on: parent_attribute] using populate_dependent_dropdown from [ReferenceEntity] where [ReferenceEntity].[filter_attribute] = [Entity].[parent_attribute].

load_info_text: Loads informational text for display only
* System loads [Entity].[attribute] [info] with constant "[constant_key]".

format_enum_value: Formats enumeration values for display
* System transforms [Entity].[attribute] for display using format_enum_value.

validate_reference: Validates reference to another entity
* System validates [Entity].[attribute] using validate_reference with [ReferenceEntity].[reference_attribute].

### Common Validation Patterns

Required Field:
* [Entity].[attribute] (datatype) is mandatory. Error message: "[attribute] is required"

Unique Constraint:
* [Entity].[attribute] (datatype) is mandatory and unique. Error message: "[attribute] must be unique"

Enumeration Constraint:
* [Entity].[attribute] (enum) must be one of "[value1]", "[value2]", "[value3]". Error message: "Please select a valid [attribute]"

Cross-Field Validation:
* [Entity].[attribute1] must be after [Entity].[attribute2]. Error message: "[attribute1] must be after [attribute2]"

### Common UI Controls

Text Input:
* [Entity].[attribute] displays as oj-input-text with [properties].

Date Input:
* [Entity].[attribute] displays as oj-input-date with min-value set to [value].

Number Input:
* [Entity].[attribute] displays as oj-input-number with min-value set to [value].

Dropdown/Select from Enumeration:
* [Entity].[attribute] displays as oj-combobox-one with source from entity enumeration.

Dependent Dropdown:
* [Entity].[attribute] [depends on: parent_attribute] displays as oj-combobox-one with source from [ReferenceEntity] filtered by [ReferenceEntity].[filter_attribute] = [Entity].[parent_attribute].

Text Area:
* [Entity].[attribute] displays as oj-text-area with rows set to [value].

Information Text:
* [Entity].[attribute] [info] displays as oj-text with formatting-class "[class_name]".

Hidden Field:
* [Entity].[attribute] is hidden and populated by system.

### Common Mapping Patterns

Direct Mapping:
* [source_lo].output.[attribute] maps to [target_lo].input.[attribute] using direct mapping.

Transformation Mapping:
* [source_lo].output.[attribute] maps to [target_lo].input.[attribute] using [transform_function].

Conditional Mapping:
* When [condition], [source_lo].output.[attribute] maps to [target_lo].input.[attribute].

### Common Pathway Patterns

Conditional Routing:
* When [Entity].[attribute] [operator] [value], route to [specific_LO_name].

Flag Setting and Routing:
* When [condition], system flags [Entity].[attribute] to [value], route to [specific_LO_name].

Terminal LO:
* Terminal workflow stage.
```

## 5. VALIDATION CHECKLIST

### Role Validation Checklist
- [ ] All role IDs are unique and follow naming convention
- [ ] Inheritance relationships are properly defined
- [ ] Entity permissions include create, read, and update specifications
- [ ] Denied attributes are properly marked with ^deny notation
- [ ] GO role assignments are complete and valid
- [ ] Scope levels are appropriate for each role
- [ ] Special permissions are clearly documented
- [ ] External/contractor roles have proper limitations
- [ ] Temporary roles have duration limits

### Entity Validation Checklist
- [ ] All entity names follow PascalCase convention
- [ ] Primary keys are marked with ^PK
- [ ] Foreign keys are marked with ^FK and properly referenced
- [ ] Enumerated values are listed in parentheses
- [ ] Relationships are properly defined with cardinality
- [ ] Business rules reference specific attributes
- [ ] Calculated fields are marked with [derived]
- [ ] Hierarchy dependencies are properly declared
- [ ] Data lifecycle management is defined
- [ ] Circuit components are complete

### GO Validation Checklist
- [ ] All LOs have unique IDs (lo001, lo002, etc.)
- [ ] Every referenced LO is defined in Process Flow
- [ ] All routing uses LO IDs, not names
- [ ] First LO is human-triggered OR Trigger Definition exists
- [ ] Input and Output mappings are explicit
- [ ] Business rules reference specific LO IDs
- [ ] Rollback LOs are included in Process Flow
- [ ] Parallel flows reference defined LOs
- [ ] All pathways terminate properly
- [ ] Integration points are fully defined
- [ ] Process mining schema is comprehensive

### LO Validation Checklist
- [ ] LO ID follows convention (lo###)
- [ ] Contextual ID includes GO reference (go###.lo###)
- [ ] All inputs and outputs are properly defined
- [ ] System actions use standard function names
- [ ] DB Stack includes all necessary validations
- [ ] UI Stack defines all display controls
- [ ] Mapping Stack connects to next LOs
- [ ] Execution pathways cover all scenarios
- [ ] Dependent attributes use [depends on:] notation
- [ ] Information-only fields are marked with [info]
- [ ] Synthetic values provide comprehensive examples