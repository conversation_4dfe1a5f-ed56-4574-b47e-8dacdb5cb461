import psycopg2

# PostgreSQL Database Connection Configuration
PG_CONFIG = {
    "dbname": "workflow_solution_temp",  # Replace with your DB name
    "user": "postgres",  # Replace with your DB username
    "password": "postgres",  # Replace with your DB password
    "host": "localhost",  # Replace with your DB host (e.g., localhost)
    "port": "5432"  # Replace with your DB port (default is 5432)
}

# Complete SQL Schema
SQL_SCHEMA = """
CREATE SCHEMA IF NOT EXISTS workflow_solution_temp;


-- Tenants table
CREATE TABLE workflow_solution_temp.tenants (
    tenant_id VARCHAR(50) PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Permission types table
CREATE TABLE workflow_solution_temp.permission_types (
    permission_id VARCHAR(50) PRIMARY KEY,
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Permission capabilities table
CREATE TABLE workflow_solution_temp.permission_capabilities (
    capability_id VARCHAR(50) PRIMARY KEY,
    permission_id VARCHAR(50) NOT NULL,
    FOREIGN KEY (permission_id) REFERENCES workflow_solution_temp.permission_types(permission_id) ON DELETE CASCADE
);

-- Roles table
CREATE TABLE workflow_solution_temp.roles (
    role_id VARCHAR(50) PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    tenant_id VARCHAR(50) NOT NULL,
    inherits_from VARCHAR(50) NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (tenant_id) REFERENCES workflow_solution_temp.tenants(tenant_id) ON DELETE CASCADE,
    FOREIGN KEY (inherits_from) REFERENCES workflow_solution_temp.roles(role_id) ON DELETE SET NULL
);

-- Entity permissions by role
CREATE TABLE workflow_solution_temp.entity_permissions (
    id SERIAL PRIMARY KEY,
    role_id VARCHAR(50) NOT NULL,
    entity_id VARCHAR(50) NOT NULL,
    permission_id VARCHAR(50) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (role_id) REFERENCES workflow_solution_temp.roles(role_id) ON DELETE CASCADE,
    FOREIGN KEY (permission_id) REFERENCES workflow_solution_temp.permission_types(permission_id) ON DELETE CASCADE
);

-- Objective permissions by role
CREATE TABLE workflow_solution_temp.objective_permissions (
    id SERIAL PRIMARY KEY,
    role_id VARCHAR(50) NOT NULL,
    objective_id VARCHAR(50) NOT NULL,
    permission_id VARCHAR(50) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (role_id) REFERENCES workflow_solution_temp.roles(role_id) ON DELETE CASCADE,
    FOREIGN KEY (permission_id) REFERENCES workflow_solution_temp.permission_types(permission_id) ON DELETE CASCADE
);

-- ENTITY DEFINITION TABLES
-----------------------------------------------------------

-- Entities table
CREATE TABLE workflow_solution_temp.entities (
    entity_id VARCHAR(50) PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    version VARCHAR(20) DEFAULT '1.0',
    status VARCHAR(50) NOT NULL,
    type VARCHAR(50) NOT NULL,
    attribute_prefix VARCHAR(10),
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Entity attribute metadata
CREATE TABLE workflow_solution_temp.entity_attribute_metadata (
    entity_id VARCHAR(50) NOT NULL,
    attribute_id VARCHAR(50) NOT NULL,
    attribute_name VARCHAR(100) NOT NULL,
    required BOOLEAN DEFAULT FALSE,
    PRIMARY KEY (entity_id, attribute_id),
    FOREIGN KEY (entity_id) REFERENCES workflow_solution_temp.entities(entity_id) ON DELETE CASCADE
);

-- Entity attributes
CREATE TABLE workflow_solution_temp.entity_attributes (
    attribute_id VARCHAR(50) PRIMARY KEY,
    entity_id VARCHAR(50) NOT NULL,
    name VARCHAR(100) NOT NULL,
    display_name VARCHAR(100) NOT NULL,
    datatype VARCHAR(50) NOT NULL,
    version VARCHAR(20) DEFAULT '1.0',
    status VARCHAR(50) NOT NULL,
    required BOOLEAN DEFAULT FALSE,
    reference_entity_id VARCHAR(50),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (entity_id) REFERENCES workflow_solution_temp.entities(entity_id) ON DELETE CASCADE,
    FOREIGN KEY (reference_entity_id) REFERENCES workflow_solution_temp.entities(entity_id) ON DELETE SET NULL
);

-- Attribute validations
CREATE TABLE workflow_solution_temp.attribute_validations (
    id SERIAL PRIMARY KEY,
    attribute_id VARCHAR(50) NOT NULL,
    rule VARCHAR(100) NOT NULL,
    expression TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (attribute_id) REFERENCES workflow_solution_temp.entity_attributes(attribute_id) ON DELETE CASCADE
);

-- Enum values for attributes
CREATE TABLE workflow_solution_temp.attribute_enum_values (
    id SERIAL PRIMARY KEY,
    attribute_id VARCHAR(50) NOT NULL,
    value VARCHAR(100) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (attribute_id) REFERENCES workflow_solution_temp.entity_attributes(attribute_id) ON DELETE CASCADE
);

-- Entity relationships
CREATE TABLE workflow_solution_temp.entity_relationships (
    id SERIAL PRIMARY KEY,
    source_entity_id VARCHAR(50) NOT NULL,
    target_entity_id VARCHAR(50) NOT NULL,
    relationship_type VARCHAR(50) NOT NULL,
    source_attribute_id VARCHAR(50) NOT NULL,
    target_attribute_id VARCHAR(50) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (source_entity_id) REFERENCES workflow_solution_temp.entities(entity_id) ON DELETE CASCADE,
    FOREIGN KEY (target_entity_id) REFERENCES workflow_solution_temp.entities(entity_id) ON DELETE CASCADE
);

-- GLOBAL OBJECTIVE TABLES
-----------------------------------------------------------

-- Global Objectives
CREATE TABLE workflow_solution_temp.global_objectives (
    go_id VARCHAR(50) PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    version VARCHAR(20) DEFAULT '1.0',
    status VARCHAR(50) NOT NULL,
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Input Stack
CREATE TABLE workflow_solution_temp.input_stack (
    id SERIAL PRIMARY KEY,
    go_id VARCHAR(50) NOT NULL,
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (go_id) REFERENCES workflow_solution_temp.global_objectives(go_id) ON DELETE CASCADE
);

-- Input Items
CREATE TABLE workflow_solution_temp.input_items (
    id VARCHAR(50) NOT NULL,
    input_stack_id INTEGER NOT NULL,
    slot_id VARCHAR(50) NOT NULL,
    contextual_id VARCHAR(50) NOT NULL,
    entity_reference VARCHAR(50),
    attribute_reference VARCHAR(50),
    source_type VARCHAR(50) NOT NULL,
    source_description TEXT,
    required BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (id, input_stack_id),
    FOREIGN KEY (input_stack_id) REFERENCES workflow_solution_temp.input_stack(id) ON DELETE CASCADE,
    FOREIGN KEY (entity_reference) REFERENCES workflow_solution_temp.entities(entity_id) ON DELETE SET NULL
);

-- System Functions
CREATE TABLE workflow_solution_temp.system_functions (
    function_id VARCHAR(50) PRIMARY KEY,
    function_name VARCHAR(100) NOT NULL,
    function_type VARCHAR(50) NOT NULL,
    stack_type VARCHAR(50) NOT NULL, -- 'input' or 'output'
    stack_id INTEGER NOT NULL,
    parameters JSONB,
    output_to VARCHAR(100),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Output Stack
CREATE TABLE workflow_solution_temp.output_stack (
    id SERIAL PRIMARY KEY,
    go_id VARCHAR(50) NOT NULL,
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (go_id) REFERENCES workflow_solution_temp.global_objectives(go_id) ON DELETE CASCADE
);

-- Output Items
CREATE TABLE workflow_solution_temp.output_items (
    id VARCHAR(50) NOT NULL,
    output_stack_id INTEGER NOT NULL,
    slot_id VARCHAR(50) NOT NULL,
    contextual_id VARCHAR(50) NOT NULL,
    output_entity VARCHAR(50),
    output_attribute VARCHAR(50),
    data_type VARCHAR(50) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (id, output_stack_id),
    FOREIGN KEY (output_stack_id) REFERENCES workflow_solution_temp.output_stack(id) ON DELETE CASCADE,
    FOREIGN KEY (output_entity) REFERENCES workflow_solution_temp.entities(entity_id) ON DELETE SET NULL
);

-- Triggers
CREATE TABLE workflow_solution_temp.output_triggers (
    id VARCHAR(50) NOT NULL,
    output_item_id VARCHAR(50) NOT NULL,
    output_stack_id INTEGER NOT NULL,
    target_objective VARCHAR(50) NOT NULL,
    target_input VARCHAR(50) NOT NULL,
    mapping_type VARCHAR(50) NOT NULL,
    condition_type VARCHAR(50),
    condition_entity VARCHAR(50),
    condition_attribute VARCHAR(50),
    condition_operator VARCHAR(20),
    condition_value TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (id, output_item_id, output_stack_id),
    FOREIGN KEY (output_item_id, output_stack_id) REFERENCES workflow_solution_temp.output_items(id, output_stack_id) ON DELETE CASCADE,
    FOREIGN KEY (target_objective) REFERENCES workflow_solution_temp.global_objectives(go_id) ON DELETE CASCADE
);

-- Data Mapping Stack
CREATE TABLE workflow_solution_temp.data_mapping_stack (
    id SERIAL PRIMARY KEY,
    go_id VARCHAR(50) NOT NULL,
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (go_id) REFERENCES workflow_solution_temp.global_objectives(go_id) ON DELETE CASCADE
);

-- Data Mappings
CREATE TABLE workflow_solution_temp.data_mappings (
    id VARCHAR(50) NOT NULL,
    mapping_stack_id INTEGER NOT NULL,
    source VARCHAR(50) NOT NULL,
    target VARCHAR(50) NOT NULL,
    mapping_type VARCHAR(50) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (id, mapping_stack_id),
    FOREIGN KEY (mapping_stack_id) REFERENCES workflow_solution_temp.data_mapping_stack(id) ON DELETE CASCADE
);

-- Mapping Rules
CREATE TABLE workflow_solution_temp.mapping_rules (
    id VARCHAR(50) NOT NULL,
    mapping_stack_id INTEGER NOT NULL,
    description TEXT,
    condition_type VARCHAR(50),
    condition_entity VARCHAR(50),
    condition_attribute VARCHAR(50),
    condition_operator VARCHAR(20),
    condition_value TEXT,
    error_message TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (id, mapping_stack_id),
    FOREIGN KEY (mapping_stack_id) REFERENCES workflow_solution_temp.data_mapping_stack(id) ON DELETE CASCADE,
    FOREIGN KEY (condition_entity) REFERENCES workflow_solution_temp.entities(entity_id) ON DELETE SET NULL
);

-- LOCAL OBJECTIVE TABLES
-----------------------------------------------------------

-- Local Objectives
CREATE TABLE workflow_solution_temp.local_objectives (
    lo_id VARCHAR(50) PRIMARY KEY,
    contextual_id VARCHAR(100) NOT NULL,
    name VARCHAR(100) NOT NULL,
    function_type VARCHAR(50) NOT NULL,
    workflow_source VARCHAR(50) NOT NULL,
    go_id VARCHAR(50) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (go_id) REFERENCES workflow_solution_temp.global_objectives(go_id) ON DELETE CASCADE
);

-- Execution Pathway
CREATE TABLE workflow_solution_temp.execution_pathways (
    lo_id VARCHAR(50) PRIMARY KEY,
    pathway_type VARCHAR(50) NOT NULL,
    next_lo VARCHAR(50),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (lo_id) REFERENCES workflow_solution_temp.local_objectives(lo_id) ON DELETE CASCADE,
    FOREIGN KEY (next_lo) REFERENCES workflow_solution_temp.local_objectives(lo_id) ON DELETE SET NULL
);

-- Terminal pathways
CREATE TABLE workflow_solution_temp.terminal_pathways (
    lo_id VARCHAR(50) PRIMARY KEY,
    terminal_type VARCHAR(50) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (lo_id) REFERENCES workflow_solution_temp.local_objectives(lo_id) ON DELETE CASCADE
);

-- Alternative Execution Pathway Conditions
CREATE TABLE workflow_solution_temp.execution_pathway_conditions (
    id SERIAL PRIMARY KEY,
    lo_id VARCHAR(50) NOT NULL,
    condition_type VARCHAR(50) NOT NULL,
    condition_entity VARCHAR(50),
    condition_attribute VARCHAR(50),
    condition_operator VARCHAR(20),
    condition_value TEXT,
    next_lo VARCHAR(50) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (lo_id) REFERENCES workflow_solution_temp.local_objectives(lo_id) ON DELETE CASCADE,
    FOREIGN KEY (next_lo) REFERENCES workflow_solution_temp.local_objectives(lo_id) ON DELETE CASCADE
);

-- Agent Stack
CREATE TABLE workflow_solution_temp.agent_stack (
    id SERIAL PRIMARY KEY,
    lo_id VARCHAR(50) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (lo_id) REFERENCES workflow_solution_temp.local_objectives(lo_id) ON DELETE CASCADE
);

-- Agent Rights
CREATE TABLE workflow_solution_temp.agent_rights (
    id SERIAL PRIMARY KEY,
    agent_stack_id INTEGER NOT NULL,
    role_id VARCHAR(50) NOT NULL,
    right_id VARCHAR(50) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (agent_stack_id) REFERENCES workflow_solution_temp.agent_stack(id) ON DELETE CASCADE,
    FOREIGN KEY (role_id) REFERENCES workflow_solution_temp.roles(role_id) ON DELETE CASCADE,
    FOREIGN KEY (right_id) REFERENCES workflow_solution_temp.permission_types(permission_id) ON DELETE CASCADE
);

-- LO Input Stack
CREATE TABLE workflow_solution_temp.lo_input_stack (
    id SERIAL PRIMARY KEY,
    lo_id VARCHAR(50) NOT NULL,
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (lo_id) REFERENCES workflow_solution_temp.local_objectives(lo_id) ON DELETE CASCADE
);

-- LO Input Items
CREATE TABLE workflow_solution_temp.lo_input_items (
    id VARCHAR(50) NOT NULL,
    input_stack_id INTEGER NOT NULL,
    slot_id VARCHAR(100) NOT NULL,
    contextual_id VARCHAR(100) NOT NULL,
    source_type VARCHAR(50) NOT NULL,
    source_description TEXT,
    required BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (id, input_stack_id),
    FOREIGN KEY (input_stack_id) REFERENCES workflow_solution_temp.lo_input_stack(id) ON DELETE CASCADE
);

-- Input Data Sources
CREATE TABLE workflow_solution_temp.input_data_sources (
    id SERIAL PRIMARY KEY,
    input_item_id VARCHAR(50) NOT NULL,
    input_stack_id INTEGER NOT NULL,
    source_type VARCHAR(50) NOT NULL,
    function_name VARCHAR(100),
    parameters JSONB,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (input_item_id, input_stack_id) REFERENCES workflow_solution_temp.lo_input_items(id, input_stack_id) ON DELETE CASCADE
);

-- LO Input Validations
CREATE TABLE workflow_solution_temp.lo_input_validations (
    id SERIAL PRIMARY KEY,
    input_item_id VARCHAR(50) NOT NULL,
    input_stack_id INTEGER NOT NULL,
    rule VARCHAR(100) NOT NULL,
    rule_type VARCHAR(50) NOT NULL,
    entity VARCHAR(50),
    attribute VARCHAR(50),
    validation_method VARCHAR(50),
    reference_date_source VARCHAR(100),
    allowed_values JSONB,
    error_message TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (input_item_id, input_stack_id) REFERENCES workflow_solution_temp.lo_input_items(id, input_stack_id) ON DELETE CASCADE
);

-- Execution Rules
CREATE TABLE workflow_solution_temp.execution_rules (
    id VARCHAR(50) NOT NULL,
    lo_id VARCHAR(50) NOT NULL,
    contextual_id VARCHAR(100) NOT NULL,
    description TEXT,
    structured_rule JSONB,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (id, lo_id),
    FOREIGN KEY (lo_id) REFERENCES workflow_solution_temp.local_objectives(lo_id) ON DELETE CASCADE
);

-- LO System Functions
CREATE TABLE workflow_solution_temp.lo_system_functions (
    function_id VARCHAR(50) NOT NULL,
    lo_id VARCHAR(50) NOT NULL,
    function_name VARCHAR(100) NOT NULL,
    function_type VARCHAR(50) NOT NULL,
    parameters JSONB,
    output_to VARCHAR(100),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (function_id, lo_id),
    FOREIGN KEY (lo_id) REFERENCES workflow_solution_temp.local_objectives(lo_id) ON DELETE CASCADE
);

-- LO Output Stack
CREATE TABLE workflow_solution_temp.lo_output_stack (
    id SERIAL PRIMARY KEY,
    lo_id VARCHAR(50) NOT NULL,
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (lo_id) REFERENCES workflow_solution_temp.local_objectives(lo_id) ON DELETE CASCADE
);

-- LO Output Items
CREATE TABLE workflow_solution_temp.lo_output_items (
    id VARCHAR(50) NOT NULL,
    output_stack_id INTEGER NOT NULL,
    slot_id VARCHAR(100) NOT NULL,
    contextual_id VARCHAR(100) NOT NULL,
    source VARCHAR(100),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (id, output_stack_id),
    FOREIGN KEY (output_stack_id) REFERENCES workflow_solution_temp.lo_output_stack(id) ON DELETE CASCADE
);

-- LO Output Triggers
CREATE TABLE workflow_solution_temp.lo_output_triggers (
    id VARCHAR(50) NOT NULL,
    output_item_id VARCHAR(50) NOT NULL,
    output_stack_id INTEGER NOT NULL,
    target_objective VARCHAR(100) NOT NULL,
    target_input VARCHAR(100) NOT NULL,
    mapping_type VARCHAR(50) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (id, output_item_id, output_stack_id),
    FOREIGN KEY (output_item_id, output_stack_id) REFERENCES workflow_solution_temp.lo_output_items(id, output_stack_id) ON DELETE CASCADE
);

-- LO Data Mapping Stack
CREATE TABLE workflow_solution_temp.lo_data_mapping_stack (
    id SERIAL PRIMARY KEY,
    lo_id VARCHAR(50) NOT NULL,
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (lo_id) REFERENCES workflow_solution_temp.local_objectives(lo_id) ON DELETE CASCADE
);

-- LO Data Mappings
CREATE TABLE workflow_solution_temp.lo_data_mappings (
    id VARCHAR(50) NOT NULL,
    mapping_stack_id INTEGER NOT NULL,
    source VARCHAR(100) NOT NULL,
    target VARCHAR(100) NOT NULL,
    mapping_type VARCHAR(50) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (id, mapping_stack_id),
    FOREIGN KEY (mapping_stack_id) REFERENCES workflow_solution_temp.lo_data_mapping_stack(id) ON DELETE CASCADE
);

-- Success Messages
CREATE TABLE workflow_solution_temp.success_messages (
    lo_id VARCHAR(50) PRIMARY KEY,
    message TEXT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (lo_id) REFERENCES workflow_solution_temp.local_objectives(lo_id) ON DELETE CASCADE
);

-- Conditional Success Messages
CREATE TABLE workflow_solution_temp.conditional_success_messages (
    id SERIAL PRIMARY KEY,
    lo_id VARCHAR(50) NOT NULL,
    condition_expression TEXT,
    message TEXT NOT NULL,
    is_default BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (lo_id) REFERENCES workflow_solution_temp.local_objectives(lo_id) ON DELETE CASCADE
);

-- UI Stack
CREATE TABLE workflow_solution_temp.ui_stack (
    id SERIAL PRIMARY KEY,
    lo_id VARCHAR(50) NOT NULL,
    type VARCHAR(50) NOT NULL,
    status VARCHAR(50) NOT NULL,
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (lo_id) REFERENCES workflow_solution_temp.local_objectives(lo_id) ON DELETE CASCADE
);

-- UI Elements
CREATE TABLE workflow_solution_temp.ui_elements (
    id SERIAL PRIMARY KEY,
    ui_stack_id INTEGER NOT NULL,
    entity_attribute VARCHAR(100) NOT NULL,
    ui_control VARCHAR(50) NOT NULL,
    helper_text TEXT,
    format_as VARCHAR(50),
    options JSONB,
    display_properties JSONB,
    enable_condition TEXT,
    visibility_condition TEXT,
    required_condition TEXT,
    display_text JSONB,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (ui_stack_id) REFERENCES workflow_solution_temp.ui_stack(id) ON DELETE CASCADE
);

-- RUNTIME METRICS TABLES
-----------------------------------------------------------

-- Runtime Metrics Stack
CREATE TABLE workflow_solution_temp.runtime_metrics_stack (
    id SERIAL PRIMARY KEY,
    go_id VARCHAR(50) NOT NULL,
    description TEXT,
    metrics_entity VARCHAR(50) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (go_id) REFERENCES workflow_solution_temp.global_objectives(go_id) ON DELETE CASCADE,
    FOREIGN KEY (metrics_entity) REFERENCES workflow_solution_temp.entities(entity_id) ON DELETE CASCADE
);

-- Metrics Aggregation
CREATE TABLE workflow_solution_temp.metrics_aggregation (
    id SERIAL PRIMARY KEY,
    metrics_stack_id INTEGER NOT NULL,
    attribute_name VARCHAR(100) NOT NULL,
    aggregation_function VARCHAR(100) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (metrics_stack_id) REFERENCES workflow_solution_temp.runtime_metrics_stack(id) ON DELETE CASCADE
);

-- Execution Path Tracking
CREATE TABLE workflow_solution_temp.execution_path_tracking (
    id SERIAL PRIMARY KEY,
    metrics_stack_id INTEGER NOT NULL,
    enabled BOOLEAN DEFAULT TRUE,
    store_complete_path BOOLEAN DEFAULT TRUE,
    identify_bottlenecks BOOLEAN DEFAULT FALSE,
    compare_to_historical BOOLEAN DEFAULT FALSE,
    path_efficiency_function VARCHAR(100),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (metrics_stack_id) REFERENCES workflow_solution_temp.runtime_metrics_stack(id) ON DELETE CASCADE
);

-- Metrics Reporting
CREATE TABLE workflow_solution_temp.metrics_reporting (
    id SERIAL PRIMARY KEY,
    metrics_stack_id INTEGER NOT NULL,
    generate_execution_summary BOOLEAN DEFAULT TRUE,
    store_summary_location VARCHAR(255),
    error_count_threshold INTEGER,
    duration_percentile INTEGER,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (metrics_stack_id) REFERENCES workflow_solution_temp.runtime_metrics_stack(id) ON DELETE CASCADE
);

-- WORKFLOW RUNTIME EXECUTION TABLES
-----------------------------------------------------------

-- Workflow Instances
CREATE TABLE workflow_solution_temp.workflow_instances (
    instance_id VARCHAR(50) PRIMARY KEY,
    go_id VARCHAR(50) NOT NULL,
    tenant_id VARCHAR(50) NOT NULL,
    user_id VARCHAR(50) NOT NULL,
    status VARCHAR(50) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (go_id) REFERENCES workflow_solution_temp.global_objectives(go_id),
    FOREIGN KEY (tenant_id) REFERENCES workflow_solution_temp.tenants(tenant_id)
);

-- Workflow Execution Log
CREATE TABLE workflow_solution_temp.workflow_execution_log (
    id SERIAL PRIMARY KEY,
    instance_id VARCHAR(50) NOT NULL,
    lo_id VARCHAR(50) NOT NULL,
    executed_by VARCHAR(50) NOT NULL,
    status VARCHAR(50) NOT NULL,
    execution_start TIMESTAMP,
    execution_end TIMESTAMP,
    duration_ms INTEGER,
    input_data JSONB,
    output_data JSONB,
    error_message TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (instance_id) REFERENCES workflow_solution_temp.workflow_instances(instance_id),
    FOREIGN KEY (lo_id) REFERENCES workflow_solution_temp.local_objectives(lo_id)
);
"""
# Function to create the database and tables
def create_database():
    try:
        # Connect to PostgreSQL
        conn = psycopg2.connect(**PG_CONFIG)
        conn.autocommit = True  # Enable auto-commit
        cursor = conn.cursor()

        # Execute schema creation
#        cursor.execute(SQL_SCHEMA)
  # Split the SQL statements and execute them one by one
        for statement in SQL_SCHEMA.strip().split(";"):
            if statement.strip():  # Ignore empty statements
                cursor.execute(statement)


       print("Database schema created successfully.")

    except Exception as e:
        print("Error creating database schema:", e)
    finally:
        cursor.close()
        conn.close()

# Run the script
if __name__ == "__main__":
    create_database()
