#!/usr/bin/env python3

"""
This script fixes the insert_generator.py file to properly map entity attributes
based on source descriptions rather than relying on attribute IDs from YAML.

The issue is that the insert_generator.py script is not correctly mapping entity attributes
when inserting input items, causing a mismatch between the YAML definition and the database.
"""

import os
import re
import sys
import traceback
from datetime import datetime

# Set log path to current working directory
LOG_PATH = os.path.join(
    os.path.dirname(os.path.abspath(__file__)),
    f"fix_insert_generator_slot_ids_log_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
)

# Logging function
def log(msg):
    with open(LOG_PATH, "a") as f:
        f.write(f"{datetime.now().strftime('%Y-%m-%d %H:%M:%S')} | {msg}\n")
    print(msg)

def fix_insert_generator():
    """Fix the insert_generator.py file to properly map entity attributes."""
    file_path = "insert_generator.py"
    backup_path = f"insert_generator.py.bak.{datetime.now().strftime('%Y%m%d%H%M%S')}"
    
    # Create a backup of the original file
    try:
        with open(file_path, 'r') as src, open(backup_path, 'w') as dst:
            dst.write(src.read())
        log(f"✅ Created backup at {backup_path}")
    except Exception as e:
        log(f"❌ Error creating backup: {e}")
        return False
    
    # Read the file content
    try:
        with open(file_path, 'r') as f:
            content = f.read()
    except Exception as e:
        log(f"❌ Error reading file: {e}")
        return False
    
    # Add the map_attribute_by_description function
    map_attribute_function = """
def map_attribute_by_description(cursor, entity_id, source_description):
    \"\"\"Map an attribute by its description to the correct attribute ID in the database.\"\"\"
    try:
        # Find the attribute by display name
        cursor.execute(
            \"\"\"
            SELECT attribute_id FROM entity_attributes 
            WHERE entity_id = %s AND display_name = %s
            \"\"\",
            (entity_id, source_description)
        )
        
        result = cursor.fetchone()
        if result:
            return result[0]
        
        # Try case-insensitive match
        cursor.execute(
            \"\"\"
            SELECT attribute_id FROM entity_attributes 
            WHERE entity_id = %s AND LOWER(display_name) = LOWER(%s)
            \"\"\",
            (entity_id, source_description)
        )
        
        result = cursor.fetchone()
        if result:
            return result[0]
        
        # If still not found, log a warning
        log(f"⚠️ Could not find attribute for '{source_description}' in entity {entity_id}")
        return None
    except Exception as e:
        log(f"❌ Error mapping attribute by description: {e}")
        return None
"""
    
    # Find a good place to insert the function (before insert_input_item_complete)
    insert_input_item_pattern = r"def insert_input_item_complete\(cursor, input_item, input_stack_id, lo_id, current_time\):"
    match = re.search(insert_input_item_pattern, content)
    
    if match:
        insert_position = match.start()
        content = content[:insert_position] + map_attribute_function + content[insert_position:]
        log("✅ Added map_attribute_by_description function")
    else:
        log("❌ Could not find insert_input_item_complete function")
        return False
    
    # Modify the insert_input_item_complete function to use the new mapping function
    insert_input_item_complete_pattern = r"def insert_input_item_complete\(cursor, input_item, input_stack_id, lo_id, current_time\):(.*?)try:(.*?)item_id = input_item\.get\(\"id\", \"\"\)\.lower\(\)(.*?)slot_id = input_item\.get\(\"slot_id\", \"\"\)\.lower\(\)(.*?)contextual_id = input_item\.get\(\"contextual_id\", \"\"\)\.lower\(\)(.*?)source_type = input_item\[\"source\"\]\[\"type\"\]\.lower\(\)(.*?)source_description = input_item\[\"source\"\]\.get\(\"description\", \"\"\)(.*?)(required = input_item\.get\(\"required\", False\))"
    
    replacement = r"""def insert_input_item_complete(cursor, input_item, input_stack_id, lo_id, current_time):
    \"\"\"Insert an input item and all its components (validations, nested functions, dropdown sources) in one operation.\"\"\"
    try:
        item_id = input_item.get("id", "").lower()
        slot_id = input_item.get("slot_id", "").lower()
        contextual_id = input_item.get("contextual_id", "").lower()
        source_type = input_item["source"]["type"].lower()
        source_description = input_item["source"].get("description", "")
        
        # Extract entity_id from slot_id and map to correct attribute ID
        parts = slot_id.split('.')
        if len(parts) >= 2:
            entity_id = parts[0]
            
            # Find the correct attribute ID based on source_description
            attribute_id = map_attribute_by_description(cursor, entity_id, source_description)
            
            if attribute_id:
                # Reconstruct the slot_id with the correct attribute ID
                slot_id = f"{entity_id}.{attribute_id}.{item_id}"
                log(f"🔄 Assigned correct slot_id for {item_id}: {slot_id}")
        
        required = input_item.get("required", False)"""
    
    content = re.sub(insert_input_item_complete_pattern, replacement, content, flags=re.DOTALL)
    
    # Write the modified content back to the file
    try:
        with open(file_path, 'w') as f:
            f.write(content)
        log(f"✅ Updated {file_path} with fixes")
        return True
    except Exception as e:
        log(f"❌ Error writing file: {e}")
        return False

def create_verify_slot_id_integrity_script():
    """Create a script to verify slot ID integrity."""
    file_path = "verify_slot_id_integrity.py"
    
    # Check if the file already exists
    if os.path.exists(file_path):
        log(f"⚠️ {file_path} already exists, skipping creation")
        return False
    
    script_content = """#!/usr/bin/env python3

\"\"\"
This script verifies the integrity of slot ID mappings in the database.
It checks that each slot ID references an attribute that exists in the specified entity.
\"\"\"

import psycopg2
from datetime import datetime
import os

# Set log path to current working directory
LOG_PATH = os.path.join(
    os.path.dirname(os.path.abspath(__file__)),
    f"verify_slot_id_integrity_log_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
)

# Logging function
def log(msg):
    with open(LOG_PATH, "a") as f:
        f.write(f"{datetime.now().strftime('%Y-%m-%d %H:%M:%S')} | {msg}\\n")
    print(msg)

# Database configuration
PG_CONFIG = {
    "dbname": "workflow_system",
    "user": "postgres",
    "password": "workflow_postgres_secure_password",
    "host": "**********",
    "port": "5432"
}

def connect_to_db():
    \"\"\"Connect to the PostgreSQL database.\"\"\"
    try:
        conn = psycopg2.connect(**PG_CONFIG)
        log("✅ Connected to PostgreSQL database")
        return conn
    except Exception as e:
        log(f"❌ Error connecting to database: {e}")
        raise

def verify_slot_id_integrity():
    \"\"\"Verify the integrity of slot ID mappings in the database.\"\"\"
    conn = connect_to_db()
    cursor = conn.cursor()
    
    try:
        # Set search path to the workflow_runtime schema
        cursor.execute("SET search_path TO workflow_runtime")
        
        # Get all input items
        cursor.execute(\"\"\"
            SELECT i.id, i.slot_id, i.source_description, lo.lo_id
            FROM lo_input_items i
            JOIN lo_input_stack s ON i.input_stack_id = s.id
            JOIN local_objectives lo ON i.lo_id = lo.lo_id
        \"\"\")
        
        items = cursor.fetchall()
        
        log(f"🔍 Checking {len(items)} input items for slot ID integrity")
        
        integrity_issues = []
        
        for item_id, slot_id, description, lo_id in items:
            parts = slot_id.split('.')
            if len(parts) >= 2:
                entity_id = parts[0]
                attribute_id = parts[1]
                
                # Verify that the entity exists
                cursor.execute(
                    \"\"\"
                    SELECT 1 FROM entities WHERE entity_id = %s
                    \"\"\",
                    (entity_id,)
                )
                
                if not cursor.fetchone():
                    integrity_issues.append(f"Entity {entity_id} not found for input {item_id} in {lo_id}")
                    continue
                
                # Verify that the attribute exists for this entity
                cursor.execute(
                    \"\"\"
                    SELECT display_name FROM entity_attributes 
                    WHERE entity_id = %s AND attribute_id = %s
                    \"\"\",
                    (entity_id, attribute_id)
                )
                
                result = cursor.fetchone()
                if not result:
                    integrity_issues.append(f"Attribute {attribute_id} not found for entity {entity_id} in input {item_id}")
                elif result[0] != description:
                    integrity_issues.append(f"Attribute {attribute_id} has display name '{result[0]}' but input {item_id} has source description '{description}'")
        
        if integrity_issues:
            log(f"⚠️ Found {len(integrity_issues)} integrity issues:")
            for issue in integrity_issues:
                log(f"  - {issue}")
        else:
            log("✅ All slot IDs have integrity")
        
    except Exception as e:
        log(f"❌ Error verifying slot ID integrity: {e}")
    finally:
        cursor.close()
        conn.close()

if __name__ == "__main__":
    verify_slot_id_integrity()
"""
    
    try:
        with open(file_path, 'w') as f:
            f.write(script_content)
        os.chmod(file_path, 0o755)  # Make the script executable
        log(f"✅ Created {file_path}")
        return True
    except Exception as e:
        log(f"❌ Error creating {file_path}: {e}")
        return False

def create_reset_database_script():
    """Create a script to reset the database."""
    file_path = "reset_database.py"
    
    # Check if the file already exists
    if os.path.exists(file_path):
        log(f"⚠️ {file_path} already exists, skipping creation")
        return False
    
    script_content = """#!/usr/bin/env python3

\"\"\"
This script truncates the workflow_runtime schema tables and redeploys the YAML files.
\"\"\"

import psycopg2
from datetime import datetime
import os
import traceback
import subprocess

# Set log path to current working directory
LOG_PATH = os.path.join(
    os.path.dirname(os.path.abspath(__file__)),
    f"reset_database_log_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
)

# Logging function
def log(msg):
    with open(LOG_PATH, "a") as f:
        f.write(f"{datetime.now().strftime('%Y-%m-%d %H:%M:%S')} | {msg}\\n")
    print(msg)

# Database configuration
PG_CONFIG = {
    "dbname": "workflow_system",
    "user": "postgres",
    "password": "workflow_postgres_secure_password",
    "host": "**********",
    "port": "5432"
}

def connect_to_db():
    \"\"\"Connect to the PostgreSQL database.\"\"\"
    try:
        conn = psycopg2.connect(**PG_CONFIG)
        log("✅ Connected to PostgreSQL database")
        return conn
    except Exception as e:
        log(f"❌ Error connecting to database: {e}")
        raise

def reset_database():
    \"\"\"Truncate the workflow_runtime schema tables.\"\"\"
    conn = connect_to_db()
    cursor = conn.cursor()
    
    try:
        # Set search path to the workflow_runtime schema
        cursor.execute("SET search_path TO workflow_runtime")
        
        # Get all tables in the schema
        cursor.execute(\"\"\"
            SELECT table_name
            FROM information_schema.tables
            WHERE table_schema = 'workflow_runtime'
            AND table_type = 'BASE TABLE'
            ORDER BY table_name
        \"\"\")
        
        tables = cursor.fetchall()
        
        log(f"🔍 Found {len(tables)} tables to truncate")
        
        # Disable foreign key constraints
        cursor.execute("SET session_replication_role = 'replica'")
        
        # Truncate each table
        for table in tables:
            table_name = table[0]
            cursor.execute(f"TRUNCATE TABLE {table_name} CASCADE")
            log(f"✅ Truncated table: {table_name}")
        
        # Re-enable foreign key constraints
        cursor.execute("SET session_replication_role = 'origin'")
        
        conn.commit()
        log("✅ All tables truncated successfully")
        
    except Exception as e:
        if conn:
            conn.rollback()
        log(f"❌ Error truncating tables: {e}")
        log(traceback.format_exc())
    finally:
        if cursor:
            cursor.close()
        if conn:
            conn.close()

def redeploy_yaml_files():
    \"\"\"Redeploy the YAML files.\"\"\"
    try:
        # Run the deploy_rbac_yamls.py script
        log("🔄 Redeploying YAML files...")
        result = subprocess.run(["python", "deploy_rbac_yamls.py"], capture_output=True, text=True)
        
        if result.returncode == 0:
            log("✅ YAML files redeployed successfully")
            log(result.stdout)
        else:
            log(f"❌ Error redeploying YAML files: {result.stderr}")
    except Exception as e:
        log(f"❌ Error redeploying YAML files: {e}")
        log(traceback.format_exc())

if __name__ == "__main__":
    reset_database()
    redeploy_yaml_files()
"""
    
    try:
        with open(file_path, 'w') as f:
            f.write(script_content)
        os.chmod(file_path, 0o755)  # Make the script executable
        log(f"✅ Created {file_path}")
        return True
    except Exception as e:
        log(f"❌ Error creating {file_path}: {e}")
        return False

def main():
    """Main function."""
    log("🚀 Starting fix_insert_generator_slot_ids.py")
    
    # Fix the insert_generator.py file
    if fix_insert_generator():
        log("✅ Successfully fixed insert_generator.py")
    else:
        log("❌ Failed to fix insert_generator.py")
        return False
    
    # Create the verify_slot_id_integrity.py script
    if create_verify_slot_id_integrity_script():
        log("✅ Successfully created verify_slot_id_integrity.py")
    else:
        log("⚠️ Could not create verify_slot_id_integrity.py")
    
    # Create the reset_database.py script
    if create_reset_database_script():
        log("✅ Successfully created reset_database.py")
    else:
        log("⚠️ Could not create reset_database.py")
    
    log("✅ All tasks completed")
    return True

if __name__ == "__main__":
    try:
        main()
    except Exception as e:
        log(f"❌ Unhandled exception: {e}")
        traceback.print_exc()
        sys.exit(1)
