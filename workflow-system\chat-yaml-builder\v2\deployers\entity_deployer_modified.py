"""
Entity Deployer for YAML Builder v2

This module provides functionality for deploying entity components to the database.
"""

import os
import sys
import json
import logging
from typing import Dict, List, Tuple, Any, Optional

# Add parent directory to path to import common modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from db_utils import execute_query, save_to_mongodb

# Set up logging
logger = logging.getLogger('entity_deployer')

def check_column_exists(schema_name: str, table_name: str, column_name: str) -> bool:
    """
    Check if a column exists in a table.
    
    Args:
        schema_name: Schema name
        table_name: Table name
        column_name: Column name
        
    Returns:
        Boolean indicating if the column exists
    """
    try:
        success, query_messages, result = execute_query(
            """
            SELECT EXISTS (
                SELECT FROM information_schema.columns 
                WHERE table_schema = %s
                AND table_name = %s
                AND column_name = %s
            )
            """,
            (schema_name, table_name, column_name)
        )
        
        if not success:
            logger.error(f"Error checking if column {column_name} exists: {query_messages}")
            return False
        
        return result and result[0][0]
    except Exception as e:
        logger.error(f"Error checking if column {column_name} exists: {str(e)}")
        return False

def deploy_entities(entities_data: Dict, schema_name: str) -> Tuple[bool, List[str]]:
    """
    Deploy entities to the database.
    
    Args:
        entities_data: Parsed YAML data for entities
        schema_name: Schema name to deploy to
        
    Returns:
        Tuple containing:
            - Boolean indicating if deployment was successful
            - List of messages (warnings, errors, or success messages)
    """
    messages = []
    logger.info(f"Deploying entities to {schema_name}")
    
    try:
        # Check if entities key exists
        if 'entities' not in entities_data:
            logger.error("Missing 'entities' key in entities definition")
            return False, ["Missing 'entities' key in entities definition"]
        
        # Deploy each entity
        for entity_name, entity_def in entities_data['entities'].items():
            success, entity_messages = deploy_single_entity(entity_name, entity_def, schema_name)
            messages.extend(entity_messages)
            
            if not success:
                return False, messages
        
        # Save to MongoDB for design-time storage
        document = {
            "component_type": "entities",
            "version_type": "v2",
            "data": entities_data
        }
        mongo_success, mongo_messages, _ = save_to_mongodb("components", document)
        messages.extend(mongo_messages)
        
        if not mongo_success:
            logger.warning("Failed to save entities to MongoDB")
        
        logger.info("Entity deployment completed successfully")
        return True, messages
    except Exception as e:
        error_msg = f"Entity deployment error: {str(e)}"
        logger.error(error_msg, exc_info=True)
        messages.append(error_msg)
        return False, messages

def deploy_single_entity(entity_name: str, entity_def: Dict, schema_name: str) -> Tuple[bool, List[str]]:
    """
    Deploy a single entity to the database.
    
    Args:
        entity_name: Name of the entity
        entity_def: Entity definition
        schema_name: Schema name to deploy to
        
    Returns:
        Tuple containing:
            - Boolean indicating if deployment was successful
            - List of messages (warnings, errors, or success messages)
    """
    messages = []
    
    try:
        # Generate entity ID
        entity_id = f"entity_{entity_name.lower().replace(' ', '_')}"
        
        # Check if entity exists
        success, query_messages, result = execute_query(
            f"SELECT entity_id FROM {schema_name}.entities WHERE entity_id = %s",
            (entity_id,),
            schema_name
        )
        
        if not success:
            messages.extend(query_messages)
            return False, messages
        
        entity_exists = result and len(result) > 0
        
        # Check if the additional columns exist
        has_status_column = check_column_exists(schema_name, "entities", "status")
        has_type_column = check_column_exists(schema_name, "entities", "type")
        has_attribute_prefix_column = check_column_exists(schema_name, "entities", "attribute_prefix")
        
        if entity_exists:
            # Update existing entity
            query = f"""
                UPDATE {schema_name}.entities
                SET name = %s,
                    description = %s,
                    metadata = %s,
                    lifecycle_management = %s,
                    version_type = 'v2'
                WHERE entity_id = %s
            """
            params = (
                entity_name,
                entity_def.get('description', ''),
                json.dumps(entity_def.get('metadata', {})),
                json.dumps(entity_def.get('lifecycle_management', {})),
                entity_id
            )
            
            success, query_messages, _ = execute_query(query, params, schema_name)
            
            if not success:
                messages.extend(query_messages)
                return False, messages
            
            messages.append(f"Updated entity '{entity_name}' in {schema_name}.entities")
            logger.info(f"Updated entity '{entity_name}' in {schema_name}.entities")
        else:
            # Insert new entity
            if has_status_column and has_type_column and has_attribute_prefix_column:
                # All additional columns exist
                query = f"""
                    INSERT INTO {schema_name}.entities (
                        entity_id, name, description, metadata, lifecycle_management, version_type,
                        status, type, attribute_prefix
                    ) VALUES (%s, %s, %s, %s, %s, 'v2', %s, %s, %s)
                """
                params = (
                    entity_id,
                    entity_name,
                    entity_def.get('description', ''),
                    json.dumps(entity_def.get('metadata', {})),
                    json.dumps(entity_def.get('lifecycle_management', {})),
                    entity_def.get('status', 'active'),  # Default status to 'active'
                    entity_def.get('type', 'standard'),  # Default type to 'standard'
                    entity_def.get('attribute_prefix', '')  # Default attribute_prefix to empty string
                )
            else:
                # Some or all additional columns don't exist
                query = f"""
                    INSERT INTO {schema_name}.entities (
                        entity_id, name, description, metadata, lifecycle_management, version_type
                    ) VALUES (%s, %s, %s, %s, %s, 'v2')
                """
                params = (
                    entity_id,
                    entity_name,
                    entity_def.get('description', ''),
                    json.dumps(entity_def.get('metadata', {})),
                    json.dumps(entity_def.get('lifecycle_management', {}))
                )
            
            success, query_messages, _ = execute_query(query, params, schema_name)
            
            if not success:
                messages.extend(query_messages)
                return False, messages
            
            messages.append(f"Inserted entity '{entity_name}' into {schema_name}.entities")
            logger.info(f"Inserted entity '{entity_name}' into {schema_name}.entities")
        
        # Deploy entity attributes
        if 'attributes' in entity_def:
            success, attr_messages = deploy_entity_attributes(entity_id, entity_def['attributes'], schema_name)
            messages.extend(attr_messages)
            
            if not success:
                return False, messages
        
        # Deploy entity relationships
        if 'relationships' in entity_def:
            success, rel_messages = deploy_entity_relationships(entity_id, entity_def['relationships'], schema_name)
            messages.extend(rel_messages)
            
            if not success:
                return False, messages
        
        # Deploy entity business rules
        if 'business_rules' in entity_def:
            success, rule_messages = deploy_entity_business_rules(entity_id, entity_def['business_rules'], schema_name)
            messages.extend(rule_messages)
            
            if not success:
                return False, messages
        
        return True, messages
    except Exception as e:
        error_msg = f"Error deploying entity '{entity_name}': {str(e)}"
        logger.error(error_msg, exc_info=True)
        messages.append(error_msg)
        return False, messages

def deploy_entity_attributes(entity_id: str, attributes: Dict, schema_name: str) -> Tuple[bool, List[str]]:
    """
    Deploy entity attributes to the database.
    
    Args:
        entity_id: ID of the entity
        attributes: Dictionary of attributes
        schema_name: Schema name to deploy to
        
    Returns:
        Tuple containing:
            - Boolean indicating if deployment was successful
            - List of messages (warnings, errors, or success messages)
    """
    messages = []
    
    try:
        # Delete existing attributes
        success, query_messages, _ = execute_query(
            f"DELETE FROM {schema_name}.entity_attributes WHERE entity_id = %s",
            (entity_id,),
            schema_name
        )
        
        if not success:
            messages.extend(query_messages)
            return False, messages
        
        # Check if the additional columns exist
        has_display_name_column = check_column_exists(schema_name, "entity_attributes", "display_name")
        has_datatype_column = check_column_exists(schema_name, "entity_attributes", "datatype")
        has_status_column = check_column_exists(schema_name, "entity_attributes", "status")
        
        # Insert new attributes
        for attr_name, attr_def in attributes.items():
            # Generate attribute ID
            attr_id = f"{entity_id}_attr_{attr_name.lower().replace(' ', '_')}"
            
            if has_display_name_column and has_datatype_column and has_status_column:
                # All additional columns exist
                query = f"""
                    INSERT INTO {schema_name}.entity_attributes (
                        attribute_id, entity_id, name, type, required, default_value, 
                        calculated_field, calculation_formula, dependencies,
                        display_name, datatype, status
                    ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                """
                params = (
                    attr_id,
                    entity_id,
                    attr_name,
                    attr_def.get('type', 'string'),
                    attr_def.get('required', False),
                    attr_def.get('default', None),
                    attr_def.get('calculated', False),
                    attr_def.get('formula', None),
                    json.dumps(attr_def.get('dependencies', [])),
                    attr_def.get('display_name', attr_name),  # Default display_name to attribute name
                    attr_def.get('datatype', 'string'),  # Default datatype to 'string'
                    attr_def.get('status', 'active')  # Default status to 'active'
                )
            else:
                # Some or all additional columns don't exist
                query = f"""
                    INSERT INTO {schema_name}.entity_attributes (
                        attribute_id, entity_id, name, type, required, default_value, 
                        calculated_field, calculation_formula, dependencies
                    ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s)
                """
                params = (
                    attr_id,
                    entity_id,
                    attr_name,
                    attr_def.get('type', 'string'),
                    attr_def.get('required', False),
                    attr_def.get('default', None),
                    attr_def.get('calculated', False),
                    attr_def.get('formula', None),
                    json.dumps(attr_def.get('dependencies', []))
                )
            
            success, query_messages, _ = execute_query(query, params, schema_name)
            
            if not success:
                messages.extend(query_messages)
                return False, messages
            
            messages.append(f"Inserted attribute '{attr_name}' for entity '{entity_id}' into {schema_name}.entity_attributes")
            logger.info(f"Inserted attribute '{attr_name}' for entity '{entity_id}' into {schema_name}.entity_attributes")
        
        return True, messages
    except Exception as e:
        error_msg = f"Error deploying attributes for entity '{entity_id}': {str(e)}"
        logger.error(error_msg, exc_info=True)
        messages.append(error_msg)
        return False, messages

def deploy_entity_relationships(entity_id: str, relationships: Dict, schema_name: str) -> Tuple[bool, List[str]]:
    """
    Deploy entity relationships to the database.
    
    Args:
        entity_id: ID of the entity
        relationships: Dictionary of relationships
        schema_name: Schema name to deploy to
        
    Returns:
        Tuple containing:
            - Boolean indicating if deployment was successful
            - List of messages (warnings, errors, or success messages)
    """
    messages = []
    
    try:
        # Check if entity_relationships table exists
        success, query_messages, result = execute_query(
            f"""
            SELECT EXISTS (
                SELECT FROM information_schema.tables 
                WHERE table_schema = %s
                AND table_name = 'entity_relationships'
            )
            """,
            (schema_name,)
        )
        
        if not success:
            messages.extend(query_messages)
            return False, messages
        
        table_exists = result and result[0][0]
        
        if not table_exists:
            # Create entity_relationships table
            success, query_messages, _ = execute_query(
                f"""
                CREATE TABLE {schema_name}.entity_relationships (
                    relationship_id VARCHAR(100) PRIMARY KEY,
                    source_entity_id VARCHAR(50) REFERENCES {schema_name}.entities(entity_id),
                    target_entity_id VARCHAR(50) REFERENCES {schema_name}.entities(entity_id),
                    name VARCHAR(100) NOT NULL,
                    type VARCHAR(50) NOT NULL,
                    cardinality VARCHAR(20),
                    constraints JSONB
                )
                """
            )
            
            if not success:
                messages.extend(query_messages)
                return False, messages
            
            messages.append(f"Created {schema_name}.entity_relationships table")
            logger.info(f"Created {schema_name}.entity_relationships table")
        
        # Delete existing relationships
        success, query_messages, _ = execute_query(
            f"DELETE FROM {schema_name}.entity_relationships WHERE source_entity_id = %s",
            (entity_id,),
            schema_name
        )
        
        if not success:
            messages.extend(query_messages)
            return False, messages
        
        # Insert new relationships
        for rel_name, rel_def in relationships.items():
            # Generate relationship ID
            rel_id = f"{entity_id}_rel_{rel_name.lower().replace(' ', '_')}"
            
            # Generate target entity ID
            target_entity = rel_def.get('entity')
            if not target_entity:
                messages.append(f"Warning: Relationship '{rel_name}' in entity '{entity_id}' is missing 'entity'")
                continue
                
            target_entity_id = f"entity_{target_entity.lower().replace(' ', '_')}"
            
            # Check if target entity exists
            success, query_messages, result = execute_query(
                f"SELECT entity_id FROM {schema_name}.entities WHERE entity_id = %s",
                (target_entity_id,),
                schema_name
            )
            
            if not success:
                messages.extend(query_messages)
                return False, messages
            
            entity_exists = result and len(result) > 0
            
            if not entity_exists:
                warning_msg = f"Warning: Relationship '{rel_name}' in entity '{entity_id}' references non-existent entity '{target_entity_id}'"
                messages.append(warning_msg)
                logger.warning(warning_msg)
                continue
            
            query = f"""
                INSERT INTO {schema_name}.entity_relationships (
                    relationship_id, source_entity_id, target_entity_id, name, type, cardinality, constraints
                ) VALUES (%s, %s, %s, %s, %s, %s, %s)
            """
            params = (
                rel_id,
                entity_id,
                target_entity_id,
                rel_name,
                rel_def.get('type', 'association'),
                rel_def.get('cardinality', 'many-to-many'),
                json.dumps(rel_def.get('constraints', {}))
            )
            
            success, query_messages, _ = execute_query(query, params, schema_name)
            
            if not success:
                messages.extend(query_messages)
                return False, messages
            
            messages.append(f"Inserted relationship '{rel_name}' for entity '{entity_id}' into {schema_name}.entity_relationships")
            logger.info(f"Inserted relationship '{rel_name}' for entity '{entity_id}' into {schema_name}.entity_relationships")
        
        return True, messages
    except Exception as e:
        error_msg = f"Error deploying relationships for entity '{entity_id}': {str(e)}"
        logger.error(error_msg, exc_info=True)
        messages.append(error_msg)
        return False, messages

def deploy_entity_business_rules(entity_id: str, business_rules: Dict, schema_name: str) -> Tuple[bool, List[str]]:
    """
    Deploy entity business rules to the database.
    
    Args:
        entity_id: ID of the entity
        business_rules: Dictionary of business rules
        schema_name: Schema name to deploy to
        
    Returns:
        Tuple containing:
            - Boolean indicating if deployment was successful
            - List of messages (warnings, errors, or success messages)
    """
    messages = []
    
    try:
        # Check if entity_business_rules table exists
        success, query_messages, result = execute_query(
            f"""
            SELECT EXISTS (
                SELECT FROM information_schema.tables 
                WHERE table_schema = %s
                AND table_name = 'entity_business_rules'
            )
            """,
            (schema_name,)
        )
        
        if not success:
            messages.extend(query_messages)
            return False, messages
        
        table_exists = result and result[0][0]
        
        if not table_exists:
            # Create entity_business_rules table
            success, query_messages, _ = execute_query(
                f"""
                CREATE TABLE {schema_name}.entity_business_rules (
                    rule_id VARCHAR(100) PRIMARY KEY,
                    entity_id VARCHAR(50) REFERENCES {schema_name}.entities(entity_id),
                    name VARCHAR(100) NOT NULL,
                    description TEXT,
                    condition TEXT,
                    action TEXT,
                    priority INTEGER DEFAULT 0,
                    active BOOLEAN DEFAULT TRUE
                )
                """
            )
            
            if not success:
                messages.extend(query_messages)
                return False, messages
            
            messages.append(f"Created {schema_name}.entity_business_rules table")
            logger.info(f"Created {schema_name}.entity_business_rules table")
        
        # Delete existing business rules
        success, query_messages, _ = execute_query(
            f"DELETE FROM {schema_name}.entity_business_rules WHERE entity_id = %s",
            (entity_id,),
            schema_name
        )
        
        if not success:
            messages.extend(query_messages)
            return False, messages
        
        # Insert new business rules
        for rule_name, rule_def in business_rules.items():
            # Generate rule ID
            rule_id = f"{entity_id}_rule_{rule_name.lower().replace(' ', '_')}"
            
            query = f"""
                INSERT INTO {schema_name}.entity_business_rules (
                    rule_id, entity_id, name, description, condition, action, priority, active
                ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
            """
            params = (
                rule_id,
                entity_id,
                rule_name,
                rule_def.get('description', ''),
                rule_def.get('condition', ''),
                rule_def.get('action', ''),
                rule_def.get('priority', 0),
                rule_def.get('active', True)
            )
            
            success, query_messages, _ = execute_query(query, params, schema_name)
            
            if not success:
                messages.extend(query_messages)
                return False, messages
            
            messages.append(f"Inserted business rule '{rule_name}' for entity '{entity_id}' into {schema_name}.entity_business_rules")
            logger.info(f"Inserted business rule '{rule_name}' for entity '{entity_id}' into {schema_name}.entity_business_rules")
        
        return True, messages
    except Exception as e:
        error_msg = f"Error deploying business rules for entity '{entity_id}': {str(e)}"
        logger.error(error_msg, exc_info=True)
        messages.append(error_msg)
        return False, messages
