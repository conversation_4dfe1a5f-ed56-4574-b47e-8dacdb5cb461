{"success": false, "component_type": "entities", "target_schema": "workflow_temp", "validation_timestamp": "2025-06-23T13:02:44.252697", "errors": [{"rule_id": "RULE_11", "message": "Entity 'E13' must have at least one attribute", "severity": "error", "entity_id": "E13", "attribute_id": null, "timestamp": "2025-06-23T13:02:44.252670"}], "warnings": [{"rule_id": "RULE_02", "message": "Invalid status transition from deployed_to_production to deployed_to_temp", "severity": "warning", "entity_id": "E13", "attribute_id": null, "timestamp": "2025-06-23T13:02:44.217717"}, {"rule_id": "RULE_07", "message": "Version mismatch between schemas for entity E13: 3 vs 1", "severity": "warning", "entity_id": "E13", "attribute_id": null, "timestamp": "2025-06-23T13:02:44.245685"}], "error_count": 1, "warning_count": 2}