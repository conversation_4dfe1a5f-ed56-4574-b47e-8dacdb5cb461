"""
Fix Entity ID Generation

This script fixes the entity ID generation issue by ensuring all entities use sequential IDs (E1, E2, etc.)
instead of entity IDs like "entity_employee".

It also updates any references to those entities in other tables.
"""

import os
import sys
import logging
import re
from typing import Dict, List, Tuple, Any, Optional

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('fix_entity_id_generation.log')
    ]
)
logger = logging.getLogger('fix_entity_id_generation')

# Add parent directory to path to import common modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from db_utils import execute_query

def get_entities_with_non_sequential_ids(schema_name: str) -> List[Tuple[str, str]]:
    """
    Get entities with non-sequential IDs.
    
    Args:
        schema_name: Schema name
        
    Returns:
        List of tuples containing (entity_id, entity_name)
    """
    try:
        # Get entities with non-sequential IDs
        success, query_messages, result = execute_query(
            f"""
            SELECT entity_id, name 
            FROM {schema_name}.entities 
            WHERE entity_id NOT LIKE 'E%'
            """,
            schema_name=schema_name
        )
        
        if not success:
            logger.error(f"Error getting entities with non-sequential IDs: {query_messages}")
            return []
        
        return result if result else []
    except Exception as e:
        logger.error(f"Error getting entities with non-sequential IDs: {str(e)}")
        return []

def get_next_entity_id(schema_name: str) -> str:
    """
    Get the next sequential entity ID (E1, E2, etc.).
    
    Args:
        schema_name: Schema name
        
    Returns:
        Next entity ID
    """
    try:
        # Check if any entities exist
        success, query_messages, result = execute_query(
            f"""
            SELECT entity_id FROM {schema_name}.entities 
            WHERE entity_id LIKE 'E%' 
            ORDER BY CAST(SUBSTRING(entity_id FROM 2) AS INTEGER) DESC 
            LIMIT 1
            """,
            schema_name=schema_name
        )
        
        if not success:
            logger.error(f"Error getting next entity ID: {query_messages}")
            return "E1"
        
        if not result:
            return "E1"
        
        # Extract the number from the entity ID
        last_id = result[0][0]
        match = re.search(r'E(\d+)', last_id)
        if not match:
            return "E1"
        
        last_num = int(match.group(1))
        return f"E{last_num + 1}"
    except Exception as e:
        logger.error(f"Error getting next entity ID: {str(e)}")
        return "E1"

def update_entity_id(old_entity_id: str, new_entity_id: str, entity_name: str, schema_name: str) -> Tuple[bool, List[str]]:
    """
    Update entity ID and all references to it.
    
    Args:
        old_entity_id: Old entity ID
        new_entity_id: New entity ID
        entity_name: Entity name
        schema_name: Schema name
        
    Returns:
        Tuple containing:
            - Boolean indicating if update was successful
            - List of messages (warnings, errors, or success messages)
    """
    messages = []
    
    try:
        # Start a transaction
        success, query_messages, _ = execute_query("BEGIN")
        
        if not success:
            messages.extend(query_messages)
            return False, messages
        
        # Update entity ID in entities table
        success, query_messages, _ = execute_query(
            f"""
            UPDATE {schema_name}.entities
            SET entity_id = %s
            WHERE entity_id = %s
            """,
            (new_entity_id, old_entity_id),
            schema_name
        )
        
        if not success:
            messages.extend(query_messages)
            execute_query("ROLLBACK")
            return False, messages
        
        messages.append(f"Updated entity ID from '{old_entity_id}' to '{new_entity_id}' in {schema_name}.entities")
        logger.info(f"Updated entity ID from '{old_entity_id}' to '{new_entity_id}' in {schema_name}.entities")
        
        # Update entity ID in entity_attributes table
        success, query_messages, _ = execute_query(
            f"""
            UPDATE {schema_name}.entity_attributes
            SET entity_id = %s,
                attribute_id = REPLACE(attribute_id, %s, %s)
            WHERE entity_id = %s
            """,
            (new_entity_id, old_entity_id, new_entity_id, old_entity_id),
            schema_name
        )
        
        if not success:
            messages.extend(query_messages)
            execute_query("ROLLBACK")
            return False, messages
        
        messages.append(f"Updated entity ID from '{old_entity_id}' to '{new_entity_id}' in {schema_name}.entity_attributes")
        logger.info(f"Updated entity ID from '{old_entity_id}' to '{new_entity_id}' in {schema_name}.entity_attributes")
        
        # Update entity ID in entity_attribute_metadata table
        success, query_messages, _ = execute_query(
            f"""
            UPDATE {schema_name}.entity_attribute_metadata
            SET entity_id = %s,
                attribute_id = REPLACE(attribute_id, %s, %s)
            WHERE entity_id = %s
            """,
            (new_entity_id, old_entity_id, new_entity_id, old_entity_id),
            schema_name
        )
        
        if not success:
            messages.extend(query_messages)
            execute_query("ROLLBACK")
            return False, messages
        
        messages.append(f"Updated entity ID from '{old_entity_id}' to '{new_entity_id}' in {schema_name}.entity_attribute_metadata")
        logger.info(f"Updated entity ID from '{old_entity_id}' to '{new_entity_id}' in {schema_name}.entity_attribute_metadata")
        
        # Update entity ID in entity_relationships table (source_entity_id)
        success, query_messages, _ = execute_query(
            f"""
            UPDATE {schema_name}.entity_relationships
            SET source_entity_id = %s,
                source_attribute_id = REPLACE(source_attribute_id, %s, %s)
            WHERE source_entity_id = %s
            """,
            (new_entity_id, old_entity_id, new_entity_id, old_entity_id),
            schema_name
        )
        
        if not success:
            messages.extend(query_messages)
            execute_query("ROLLBACK")
            return False, messages
        
        messages.append(f"Updated source_entity_id from '{old_entity_id}' to '{new_entity_id}' in {schema_name}.entity_relationships")
        logger.info(f"Updated source_entity_id from '{old_entity_id}' to '{new_entity_id}' in {schema_name}.entity_relationships")
        
        # Update entity ID in entity_relationships table (target_entity_id)
        success, query_messages, _ = execute_query(
            f"""
            UPDATE {schema_name}.entity_relationships
            SET target_entity_id = %s,
                target_attribute_id = REPLACE(target_attribute_id, %s, %s)
            WHERE target_entity_id = %s
            """,
            (new_entity_id, old_entity_id, new_entity_id, old_entity_id),
            schema_name
        )
        
        if not success:
            messages.extend(query_messages)
            execute_query("ROLLBACK")
            return False, messages
        
        messages.append(f"Updated target_entity_id from '{old_entity_id}' to '{new_entity_id}' in {schema_name}.entity_relationships")
        logger.info(f"Updated target_entity_id from '{old_entity_id}' to '{new_entity_id}' in {schema_name}.entity_relationships")
        
        # Update entity ID in entity_business_rules table
        success, query_messages, _ = execute_query(
            f"""
            UPDATE {schema_name}.entity_business_rules
            SET entity_id = %s,
                rule_id = REPLACE(rule_id, %s, %s)
            WHERE entity_id = %s
            """,
            (new_entity_id, old_entity_id, new_entity_id, old_entity_id),
            schema_name
        )
        
        if not success:
            messages.extend(query_messages)
            execute_query("ROLLBACK")
            return False, messages
        
        messages.append(f"Updated entity ID from '{old_entity_id}' to '{new_entity_id}' in {schema_name}.entity_business_rules")
        logger.info(f"Updated entity ID from '{old_entity_id}' to '{new_entity_id}' in {schema_name}.entity_business_rules")
        
        # Check if entity table exists with old format
        old_table_name = f"entity_{entity_name.lower()}"
        success, query_messages, result = execute_query(
            f"""
            SELECT EXISTS (
                SELECT FROM information_schema.tables 
                WHERE table_schema = %s
                AND table_name = %s
            )
            """,
            (schema_name, old_table_name)
        )
        
        if not success:
            messages.extend(query_messages)
            execute_query("ROLLBACK")
            return False, messages
        
        old_table_exists = result and result[0][0]
        
        if old_table_exists:
            # Generate new table name
            # Format: e1.entityname
            entity_num = new_entity_id[1:]  # Remove 'E' prefix
            new_table_name = f"e{entity_num}.{entity_name.lower()}"
            
            # Rename table
            success, query_messages, _ = execute_query(
                f"""
                ALTER TABLE {schema_name}."{old_table_name}"
                RENAME TO "{new_table_name}"
                """,
                schema_name=schema_name
            )
            
            if not success:
                messages.extend(query_messages)
                execute_query("ROLLBACK")
                return False, messages
            
            messages.append(f"Renamed table from {schema_name}.{old_table_name} to {schema_name}.{new_table_name}")
            logger.info(f"Renamed table from {schema_name}.{old_table_name} to {schema_name}.{new_table_name}")
        
        # Commit transaction
        success, query_messages, _ = execute_query("COMMIT")
        
        if not success:
            messages.extend(query_messages)
            execute_query("ROLLBACK")
            return False, messages
        
        messages.append(f"Successfully updated entity ID from '{old_entity_id}' to '{new_entity_id}'")
        logger.info(f"Successfully updated entity ID from '{old_entity_id}' to '{new_entity_id}'")
        
        return True, messages
    except Exception as e:
        error_msg = f"Error updating entity ID from '{old_entity_id}' to '{new_entity_id}': {str(e)}"
        logger.error(error_msg, exc_info=True)
        messages.append(error_msg)
        execute_query("ROLLBACK")
        return False, messages

def main():
    """
    Main function.
    """
    schema_name = "workflow_temp"
    
    # Get entities with non-sequential IDs
    logger.info(f"Getting entities with non-sequential IDs in {schema_name} schema")
    entities = get_entities_with_non_sequential_ids(schema_name)
    
    if not entities:
        logger.info(f"No entities with non-sequential IDs found in {schema_name} schema")
        return
    
    logger.info(f"Found {len(entities)} entities with non-sequential IDs in {schema_name} schema")
    
    # Update entity IDs
    for old_entity_id, entity_name in entities:
        logger.info(f"Updating entity ID for '{entity_name}' from '{old_entity_id}' to sequential ID")
        
        # Get next entity ID
        new_entity_id = get_next_entity_id(schema_name)
        
        # Update entity ID
        success, messages = update_entity_id(old_entity_id, new_entity_id, entity_name, schema_name)
        
        for message in messages:
            logger.info(message)
        
        if not success:
            logger.error(f"Failed to update entity ID for '{entity_name}' from '{old_entity_id}' to '{new_entity_id}'")
    
    logger.info("Done")

if __name__ == "__main__":
    main()
