"""
Script to check entity relationships in the database.
"""

import os
import sys
import logging
from typing import Dict, List, Tuple

# Add parent directory to path to import modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from db_utils import execute_query

# Set up logging
logging.basicConfig(level=logging.INFO, 
                    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger('check_relationships')

def check_entity_relationships(schema_name: str = 'workflow_temp') -> None:
    """
    Check entity relationships in the database.
    
    Args:
        schema_name: Database schema name
    """
    logger.info(f"Checking entity relationships in schema: {schema_name}")
    
    # Query the database to show the relationships
    logger.info("Querying entity_relationships table:")
    success, query_messages, relationships = execute_query(
        f"""
        SELECT er.id, s.name as source_entity, t.name as target_entity, 
               er.relationship_type, sa.name as source_attribute, ta.name as target_attribute
        FROM {schema_name}.entity_relationships er
        JOIN {schema_name}.entities s ON er.source_entity_id = s.entity_id
        JOIN {schema_name}.entities t ON er.target_entity_id = t.entity_id
        JOIN {schema_name}.entity_attributes sa ON er.source_attribute_id = sa.attribute_id
        JOIN {schema_name}.entity_attributes ta ON er.target_attribute_id = ta.attribute_id
        ORDER BY s.name, t.name
        """
    )
    
    if not success:
        logger.error("Failed to query entity_relationships table")
        for message in query_messages:
            logger.error(f"  - {message}")
        return
    
    if not relationships:
        logger.warning("No relationships found in the database")
        
        # Check if there are any rows in the entity_relationships table
        success, query_messages, raw_relationships = execute_query(
            f"SELECT * FROM {schema_name}.entity_relationships"
        )
        
        if success and raw_relationships:
            logger.info(f"Found {len(raw_relationships)} raw relationships in the table")
            for rel in raw_relationships:
                logger.info(f"  - {rel}")
                
            # Check if the source and target entities exist
            for rel in raw_relationships:
                source_entity_id = rel[1]  # Assuming source_entity_id is the second column
                target_entity_id = rel[2]  # Assuming target_entity_id is the third column
                
                success, query_messages, source_entity = execute_query(
                    f"SELECT name FROM {schema_name}.entities WHERE entity_id = %s",
                    (source_entity_id,)
                )
                
                if success and source_entity:
                    logger.info(f"Source entity {source_entity_id} exists: {source_entity[0][0]}")
                else:
                    logger.warning(f"Source entity {source_entity_id} does not exist")
                
                success, query_messages, target_entity = execute_query(
                    f"SELECT name FROM {schema_name}.entities WHERE entity_id = %s",
                    (target_entity_id,)
                )
                
                if success and target_entity:
                    logger.info(f"Target entity {target_entity_id} exists: {target_entity[0][0]}")
                else:
                    logger.warning(f"Target entity {target_entity_id} does not exist")
        else:
            logger.warning("No raw relationships found in the table")
            
        # Check if the entities exist
        success, query_messages, entities = execute_query(
            f"SELECT entity_id, name FROM {schema_name}.entities"
        )
        
        if success and entities:
            logger.info(f"Found {len(entities)} entities in the database")
            for entity in entities:
                logger.info(f"  - {entity}")
        else:
            logger.warning("No entities found in the database")
    else:
        logger.info("Relationships in the database:")
        for rel in relationships:
            logger.info(f"  - {rel[1]} -> {rel[2]} ({rel[3]}) using {rel[4]} -> {rel[5]}")

if __name__ == "__main__":
    schema_name = sys.argv[1] if len(sys.argv) > 1 else "workflow_temp"
    check_entity_relationships(schema_name)
