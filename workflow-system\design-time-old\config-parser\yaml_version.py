import yaml
import pymongo

# MongoDB Connection Details
MONGO_URI = "mongodb://localhost:27017/"
DATABASE_NAME = "workflow_system"
COLLECTION_NAME = "workflow"
YAML_FILE = "hrms.yaml"  # Update the path if needed

def connect_mongo():
    """Connect to MongoDB and return the database object."""
    client = pymongo.MongoClient(MONGO_URI)
    return client[DATABASE_NAME]

def delete_all_collections(db):
    """Delete all collections in the database."""
    collections = db.list_collection_names()
    for collection in collections:
        db[collection].drop()
        print(f"🗑️ Dropped collection: {collection}")
    print("✅ All collections deleted in workflow_system.")

def load_yaml(file_path):
    """Load YAML file and return the parsed data."""
    with open(file_path, "r") as file:
        return yaml.safe_load(file)

def save_to_single_collection(db, data):
    """Save YAML data into a single MongoDB collection."""
    db[COLLECTION_NAME].insert_one({"workflow_data": data})
    print(f"✅ YAML data saved in `{COLLECTION_NAME}` collection.")

def main():
    db = connect_mongo()  # Connect to MongoDB
    delete_all_collections(db)  # Delete all collections
    data = load_yaml(YAML_FILE)  # Load YAML file
    save_to_single_collection(db, data)  # Save to MongoDB

if __name__ == "__main__":
    main()
