# Natural Language Prescriptives to Database Mapping

This document provides a comprehensive mapping of natural language prescriptives to database tables in the Postgres database. It includes detailed information about how each component of the prescriptive language is parsed, validated, and stored in the database.

## Table of Contents

1. [Entity Mapping](#entity-mapping)
2. [Global Objective (GO) Mapping](#global-objective-mapping)
3. [Learning Objective (LO) Mapping](#learning-objective-mapping)
4. [Role Mapping](#role-mapping)
5. [Validation Rules](#validation-rules)
6. [Implementation Plan](#implementation-plan)

## Entity Mapping

Entities are defined in natural language prescriptives and mapped to multiple database tables.

### Entity Definition

Natural language format:
```
EntityName has attribute1^PK, attribute2, attribute3 (enum1, enum2), attribute4^FK, attribute5[derived].

* EntityName has relationship-type relationship with TargetEntity using EntityName.attribute to TargetEntity.attribute

* EntityName.attribute PROPERTY_NAME = value
* EntityName.attribute DEFAULT_VALUE = value

* EntityName.attribute must be unique
* EntityName.attribute must match pattern "regex"

BusinessRule for EntityName:
* Business rule description

CalculatedField for EntityName.attribute:
* Formula: formula_expression
* Logic Layer: Application
* Caching: Session
* Dependencies: EntityName.attribute1, EntityName.attribute2

Entity Additional Properties:
Display Name: Friendly Name
Type: Core Entity
Description: Entity description
```

### Database Tables

#### 1. `entities` Table

Stores basic entity information.

| Column | Description | Example Value |
|--------|-------------|--------------|
| entity_id | Unique identifier for the entity (E1, E2, etc.) | E1 |
| name | Entity name | Employee |
| version | Entity version | 1.0 |
| status | Entity status | Active |
| type | Entity type | Core Entity |
| attribute_prefix | Prefix for attribute IDs | |
| description | Entity description | Represents an employee within the organization |
| metadata | Additional entity metadata (JSON) | {"display_name": "Company Employee"} |
| lifecycle_management | Lifecycle management information (JSON) | {"archive_strategy": {...}} |
| version_type | Version type | v2 |

#### 2. `entity_attributes` Table

Stores entity attribute information.

| Column | Description | Example Value |
|--------|-------------|--------------|
| attribute_id | Unique identifier for the attribute (E1.At1, E1.At2, etc.) | E1.At1 |
| entity_id | Reference to the entity | E1 |
| name | Attribute name | employeeId |
| display_name | Display name for the attribute | Employee ID |
| datatype | Data type of the attribute | string |
| version | Attribute version | 1.0 |
| status | Attribute status | Active |
| required | Whether the attribute is required | true |
| reference_entity_id | Reference to another entity (for foreign keys) | E2 |
| default_value | Default value for the attribute | "Active" |
| calculated_field | Whether the attribute is calculated | false |
| calculation_formula | Formula for calculated fields | CONCAT(firstName, ' ', lastName) |
| dependencies | Dependencies for calculated fields (JSON) | ["firstName", "lastName"] |
| type | Attribute type | string |

#### 3. `entity_attribute_metadata` Table

Stores additional metadata for entity attributes.

| Column | Description | Example Value |
|--------|-------------|--------------|
| entity_id | Reference to the entity | E1 |
| attribute_id | Reference to the attribute | E1.At1 |
| attribute_name | Attribute name | employeeId |
| required | Whether the attribute is required | true |

#### 4. `attribute_enum_values` Table

Stores enum values for attributes.

| Column | Description | Example Value |
|--------|-------------|--------------|
| attribute_id | Reference to the attribute | E1.At5 |
| value | Enum value | Active |
| display_name | Display name for the enum value | Active |
| sort_order | Sort order for the enum value | 1 |

#### 5. `entity_relationships` Table

Stores relationships between entities.

| Column | Description | Example Value |
|--------|-------------|--------------|
| id | Unique identifier for the relationship | 1 |
| source_entity_id | Source entity ID | E1 |
| target_entity_id | Target entity ID | E2 |
| relationship_type | Type of relationship | many-to-one |
| source_attribute_id | Source attribute ID | E1.At4 |
| target_attribute_id | Target attribute ID | E2.At1 |

#### 6. `entity_business_rules` Table

Stores business rules for entities.

| Column | Description | Example Value |
|--------|-------------|--------------|
| rule_id | Unique identifier for the rule | E1_rule_1 |
| entity_id | Reference to the entity | E1 |
| name | Rule name | status_active_rule |
| description | Rule description | Employee status must be Active for performance reviews |
| condition | Rule condition | Employee.status = 'Active' |
| action | Rule action | Allow performance review |
| priority | Rule priority | 1 |
| active | Whether the rule is active | true |

#### 7. `attribute_validations` Table

Stores validations for attributes.

| Column | Description | Example Value |
|--------|-------------|--------------|
| attribute_id | Reference to the attribute | E1.At3 |
| validation_name | Validation name | email_format |
| validation_type | Validation type | regex |
| validation_expression | Validation expression | ^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$ |
| error_message | Error message | Invalid email format |

#### 8. Entity Tables (e.g., `e1_employee`)

Each entity gets its own table with columns for each attribute.

| Column | Description | Example Value |
|--------|-------------|--------------|
| id | Auto-incrementing ID | 1 |
| employeeId | Employee ID | EMP001 |
| firstName | First name | John |
| lastName | Last name | Doe |
| email | Email address | <EMAIL> |
| departmentId | Department ID | DEPT001 |
| status | Status | Active |
| created_at | Creation timestamp | 2023-01-01 00:00:00 |
| created_by | Creator | system |
| updated_at | Update timestamp | 2023-01-01 00:00:00 |
| updated_by | Updater | system |

### Mapping Process

1. The `entity_parser.py` parses the natural language prescriptive text and extracts entity information.
2. The `entity_deployer.py` deploys the parsed entity information to the database tables.
3. The entity ID is generated as E1, E2, etc.
4. Attribute IDs are generated as E1.At1, E1.At2, etc.
5. Entity tables are created with the naming convention e1_entityname, e2_entityname, etc.
6. Relationships, business rules, and validations are stored in their respective tables.

## Global Objective Mapping

Global Objectives (GOs) are defined in natural language prescriptives and mapped to multiple database tables.

### GO Definition

Natural language format:
```
## Process Name

Core Metadata:
- name: "Process Name"
- version: "1.0"
- status: "Active"
- description: "Process description"
- primary_entity: "EntityName"
- classification: "Process Type"
  - Global Objective: "Global Objective Name"
  - Book: "Book Name"
  - Chapter: "Chapter Name"
  - Tenant: "Tenant Name"

Process Ownership:
- Originator: Role1
- Process Owner: Role2
- Business Sponsor: Role3

Trigger Definition:
- Trigger Type: event-driven
- Trigger Condition: Condition description
- Trigger Frequency: on-demand
- Trigger Parameters: param1, param2

Data Management:

Input Stack:
- Entity1 with attr1*, attr2, attr3; Entity2 with attr1*, attr2.

Input Mapping Stack:
- Entity1.attr1 maps to LO1 → Entity3.attr1
- Entity1.attr2 maps to LO1 → Entity3.attr2

Output Stack:
- Entity3 with attr1*, attr2*, attr3*.

Output Mapping Stack:
- Entity3.attr1 maps to "GO Name" → Entity4.attr1
- Entity3.attr2 maps to "GO Name" → Entity4.attr2

Data Constraints:

DB Stack:
- Entity3.attr1 (string) is mandatory and unique. Error message: "Error message"
- Entity3.attr2 (date) is mandatory and must be a valid date. Error message: "Error message"

Process Definition:

Process Flow:
1. LO1 [HUMAN] - LO description
   a. If condition1, route to LO2
   b. If condition2, route to LO3
2. LO2 [SYSTEM] - LO description
   a. Route to LO4
3. LO3 [HUMAN] - LO description
   a. Route to LO4
4. LO4 [SYSTEM] - LO description
   a. Route to Terminal

Parallel Flows:
- After LO1:
  * LO5 - LO description
  * LO6 - LO description
- Join at: LO4

Rollback Pathways:
- LO1 ↔ LO7
- Full rollback pathway: LO7 → LO8 → LO9

Business Rules:
1. Business rule description - Enforced by LO1
2. Business rule description - Enforced by LO2

Integration Points:

GO Relationships:
- GO sends output to "GO Name" for Entity4 processing via LO4.
- GO receives input from "GO Name" for Entity1 processing.

External Systems:
- System1: Two-way integration for data exchange
- System2: Outbound integration for notifications

Performance Metadata:
- cycle_time: "2 business days"
- number_of_pathways: 4
- volume_metrics:
  * average_volume: 120
  * peak_volume: 250
  * unit: "requests/month"
- sla_thresholds:
  * threshold1: "1 business day"
  * threshold2: "1 hour"
- critical_lo_performance:
  * "LO1": "24 hours maximum"
  * "LO2": "1 hour maximum"

Process Mining Schema:
...

Sample Data:
- Entity3.attr1: "value1", "value2", "value3"
- Entity3.attr2: "value1", "value2", "value3"
```

### Database Tables

#### 1. `global_objectives` Table

Stores basic GO information.

| Column | Description | Example Value |
|--------|-------------|--------------|
| go_id | Unique identifier for the GO (GO1, GO2, etc.) | GO1 |
| name | GO name | Process Leave Requests |
| version | GO version | 1.0 |
| status | GO status | Active |
| description | GO description | Manages employee leave requests from submission to approval or rejection |
| created_at | Creation timestamp | 2023-01-01 00:00:00 |
| updated_at | Update timestamp | 2023-01-01 00:00:00 |
| tenant_id | Reference to the tenant | t001 |
| tenant_name | Tenant name | Acme Corporation |
| book_id | Reference to the book | b001 |
| book_name | Book name | Employee Leave Management |
| chapter_id | Reference to the chapter | c001 |
| chapter_name | Chapter name | Leave Request Lifecycle |
| process_mining_schema | Process mining schema (JSON) | {...} |
| performance_metadata | Performance metadata (JSON) | {...} |
| trigger_definition | Trigger definition (JSON) | {...} |
| validation_checklist | Validation checklist (JSON) | {...} |
| sample_data | Sample data (JSON) | {...} |
| business_rules | Business rules (JSON) | [...] |
| data_constraints | Data constraints (JSON) | [...] |
| rollback_pathways | Rollback pathways (JSON) | [...] |
| version_type | Version type | v2 |

#### 2. `tenants` Table

Stores tenant information.

| Column | Description | Example Value |
|--------|-------------|--------------|
| tenant_id | Unique identifier for the tenant | t001 |
| name | Tenant name | Acme Corporation |
| description | Tenant description | |
| created_at | Creation timestamp | 2023-01-01 00:00:00 |
| updated_at | Update timestamp | 2023-01-01 00:00:00 |

#### 3. `books` Table

Stores book information.

| Column | Description | Example Value |
|--------|-------------|--------------|
| book_id | Unique identifier for the book | b001 |
| tenant_id | Reference to the tenant | t001 |
| name | Book name | Employee Leave Management |
| description | Book description | |
| created_at | Creation timestamp | 2023-01-01 00:00:00 |
| updated_at | Update timestamp | 2023-01-01 00:00:00 |

#### 4. `chapters` Table

Stores chapter information.

| Column | Description | Example Value |
|--------|-------------|--------------|
| chapter_id | Unique identifier for the chapter | c001 |
| book_id | Reference to the book | b001 |
| name | Chapter name | Leave Request Lifecycle |
| description | Chapter description | |
| created_at | Creation timestamp | 2023-01-01 00:00:00 |
| updated_at | Update timestamp | 2023-01-01 00:00:00 |

#### 5. `input_stack` Table

Stores GO input stack information.

| Column | Description | Example Value |
|--------|-------------|--------------|
| id | Unique identifier for the input stack | 1 |
| go_id | Reference to the GO | GO1 |
| description | Input stack description | Input stack for GO1 |
| created_at | Creation timestamp | 2023-01-01 00:00:00 |
| updated_at | Update timestamp | 2023-01-01 00:00:00 |

#### 6. `input_items` Table

Stores GO input items.

| Column | Description | Example Value |
|--------|-------------|--------------|
| id | Unique identifier for the input item | item1 |
| input_stack_id | Reference to the input stack | 1 |
| slot_id | Slot ID | Employee.employeeId |
| contextual_id | Contextual ID | Employee.employeeId |
| entity_reference | Reference to the entity | E1 |
| attribute_reference | Reference to the attribute | E1.At1 |
| source_type | Source type | user |
| source_description | Source description | |
| required | Whether the input is required | true |
| created_at | Creation timestamp | 2023-01-01 00:00:00 |
| updated_at | Update timestamp | 2023-01-01 00:00:00 |

#### 7. `output_stack` Table

Stores GO output stack information.

| Column | Description | Example Value |
|--------|-------------|--------------|
| id | Unique identifier for the output stack | 1 |
| go_id | Reference to the GO | GO1 |
| description | Output stack description | Output stack for GO1 |
| created_at | Creation timestamp | 2023-01-01 00:00:00 |
| updated_at | Update timestamp | 2023-01-01 00:00:00 |

#### 8. `output_items` Table

Stores GO output items.

| Column | Description | Example Value |
|--------|-------------|--------------|
| id | Unique identifier for the output item | item1 |
| output_stack_id | Reference to the output stack | 1 |
| slot_id | Slot ID | LeaveApplication.leaveId |
| contextual_id | Contextual ID | LeaveApplication.leaveId |
| output_entity | Reference to the entity | E3 |
| output_attribute | Reference to the attribute | E3.At1 |
| data_type | Data type | string |
| created_at | Creation timestamp | 2023-01-01 00:00:00 |
| updated_at | Update timestamp | 2023-01-01 00:00:00 |

#### 9. `output_triggers` Table

Stores GO output triggers.

| Column | Description | Example Value |
|--------|-------------|--------------|
| id | Unique identifier for the output trigger | trigger1 |
| output_item_id | Reference to the output item | item1 |
| output_stack_id | Reference to the output stack | 1 |
| target_objective | Reference to the target GO | GO2 |
| target_input | Reference to the target input | input1 |
| mapping_type | Mapping type | direct |

#### 10. `go_lo_mapping` Table

Stores mappings between GOs and LOs.

| Column | Description | Example Value |
|--------|-------------|--------------|
| mapping_id | Unique identifier for the mapping | GO1_LO1 |
| go_id | Reference to the GO | GO1 |
| lo_id | Reference to the LO | LO1 |
| sequence_number | Sequence number | 1 |

### Mapping Process

1. The `go_parser.py` parses the natural language prescriptive text and extracts GO information.
2. The `go_deployer.py` deploys the parsed GO information to the database tables.
3. The GO ID is generated as GO1, GO2, etc.
4. Tenant, book, and chapter information is stored in their respective tables.
5. Input stack, output stack, and mappings are stored in their respective tables.
6. Process mining schema, performance metadata, and other JSON data are stored in the global_objectives table.

## Learning Objective Mapping

Learning Objectives (LOs) are defined in natural language prescriptives and mapped to multiple database tables.

### LO Definition

Natural language format:
```
## LO Name

id: "lo001"
contextual_id: "go001.lo001"
name: "LO Name"
version: "1.0"
status: "Active"
workflow_source: "origin"
function_type: "Create"

*Role has execution rights*

*Inputs: Entity with attr1*, attr2*, attr3 [info]*
* System generates Entity.attr1 using generate_id with prefix "prefix".
* System calculates Entity.attr2 using calculate_formula with attr3 and attr4.
* System defaults Entity.attr3 to "default_value".

*Outputs: Entity with attr1, attr2, attr3*
* System returns Entity.attr1 for reference in notifications.
* System captures Entity.attr2 using current_timestamp for audit trails.
* System transforms Entity.attr3 for display using format_enum_value.

*DB Stack:*
* Entity.attr1 (string) is mandatory and unique. Error message: "Error message"
* Entity.attr2 (date) is mandatory and must be a valid date. Error message: "Error message"

*UI Stack:*
* Entity.attr1 displays as oj-input-text with readonly property.
* Entity.attr2 displays as oj-input-date with min-value set to current date.
* Entity.attr3 displays as oj-text-area with rows=3 and maxlength=500.
* System provides contextual help for Entity.attr1 explaining "Help text".

*Mapping Stack:*
* LO1.output.attr1 maps to LO2.input.attr1 using direct mapping.
* LO1.output.attr2 maps to LO2.input.attr2 using direct mapping.

Execution pathway:
* When Entity.attr1 = value1, route to LO2.
* When Entity.attr2 = value2, route to LO3.
* When Entity.attr3 = value3, system flags Entity.attr4 to true, route to LO4.

Synthetic values:
* Entity.attr1: "value1", "value2", "value3"
* Entity.attr2: "value1", "value2", "value3"
```

### Database Tables

#### 1. `local_objectives` Table

Stores basic LO information.

| Column | Description | Example Value |
|--------|-------------|--------------|
| lo_id | Unique identifier for the LO | lo001 |
| contextual_id | Contextual ID (GO.LO) | go001.lo001 |
| name | LO name | Submit Leave Request |
| function_type | Function type | Create |
| workflow_source | Workflow source | origin |
| go_id | Reference to the GO | go001 |
| created_at | Creation timestamp | 2023-01-01 00:00:00 |
| updated_at | Update timestamp | 2023-01-01 00:00:00 |
| system_function | System function | |
| description | LO description | |
| ui_stack | UI stack (JSON) | {...} |
| mapping_stack | Mapping stack (JSON) | {...} |
| version_type | Version type | v2 |
| tenant_id | Reference to the tenant | t001 |
| tenant_name | Tenant name | Acme Corporation |
| book_id | Reference to the book | b001 |
| book_name | Book name | Employee Leave Management |
| chapter_id | Reference to the chapter | c001 |
| chapter_name | Chapter name | Leave Request Lifecycle |

#### 2. `lo_input_stack` Table

Stores LO input stack information.

| Column | Description | Example Value |
|--------|-------------|--------------|
| id | Unique identifier for the input stack | 1 |
| lo_id | Reference to the LO | lo001 |
| description | Input stack description | Input stack for LO1 |
| created_at | Creation timestamp | 2023-01-01 00:00:00 |
| updated_at | Update timestamp | 2023-01-01 00:00:00 |

#### 3. `lo_input_items` Table

Stores LO input items.

| Column | Description | Example Value |
|--------|-------------|--------------|
| id | Unique identifier for the input item | item1 |
| input_stack_id | Reference to the input stack | 1 |
| slot_id | Slot ID | LeaveApplication.leaveId |
| contextual_id | Contextual ID | LeaveApplication.leaveId |
| source_type | Source type | user |
| source_description | Source description | |
| required | Whether the input is required | true |
| lo_id | Reference to the LO | lo001 |
| data_type | Data type | string |
| ui_control | UI control | oj-input-text |
| nested_function | Nested function (JSON) | {...} |
| nested_functions | Nested functions (JSON) | [...] |
| metadata | Metadata (JSON) | {...} |
| dependencies | Dependencies (JSON) | [...] |
| dependency_type | Dependency type | |
| lookup_function | Lookup function (JSON) | {...} |
| is_visible | Whether the input is visible | true |
| default_value | Default value | |
| help_text | Help text | |
| dependency_info | Dependency information (JSON) | {...} |
| item_id | Item ID | default_item_id |
| name | Item name | leaveId |
| type | Item type | string |

#### 4. `lo_input_validations` Table

Stores LO input validations.

| Column | Description | Example Value |
|--------|-------------|--------------|
| input_item_id | Reference to the input item | item1 |
| input_stack_id | Reference to the input stack | 1 |
| rule | Validation rule | |
| rule_type | Rule type | |
| entity | Entity | |
| attribute | Attribute | |
| validation_method | Validation method | |
| reference_date_source | Reference date source | |
| allowed_values | Allowed values (JSON) | [...] |
| error_message | Error message | |

#### 5. `lo_output_stack` Table

Stores LO output stack information.

| Column | Description | Example Value |
|--------|-------------|--------------|
| id | Unique identifier for the output stack | 1 |
| lo_id | Reference to the LO | lo001 |
| description | Output stack description | Output stack for LO1 |
| created_at | Creation timestamp | 2023-01-01 00:00:00 |
| updated_at | Update timestamp | 2023-01-01 00:00:00 |

#### 6. `lo_output_items` Table

Stores LO output items.

| Column | Description | Example Value |
|--------|-------------|--------------|
| id | Unique identifier for the output item | item1 |
| output_stack_id | Reference to the output stack | 1 |
| slot_id | Slot ID | LeaveApplication.leaveId |
| contextual_id | Contextual ID | LeaveApplication.leaveId |
| source | Source | system |
| lo_id | Reference to the LO | lo001 |
| name | Item name | leaveId |
| type | Item type | string |
| item_id | Item ID | item1 |

#### 7. `lo_output_triggers` Table

Stores LO output triggers.

| Column | Description | Example Value |
|--------|-------------|--------------|
| id | Unique identifier for the output trigger | trigger1 |
| output_item_id | Reference to the output item | item1 |
| output_stack_id | Reference to the output stack | 1 |
| target_objective | Reference to the target LO | lo002 |
| target_input | Reference to the target input | input1 |
| mapping_type | Mapping type | direct |

#### 8. `execution_pathways` Table

Stores LO execution pathways.

| Column | Description | Example Value |
|--------|-------------|--------------|
| lo_id | Reference to the LO | lo001 |
| pathway_type | Pathway type | conditional |
| next_lo | Reference to the next LO | lo002 |
| created_at | Creation timestamp | 2023-01-01 00:00:00 |
| updated_at | Update timestamp | 2023-01-01 00:00:00 |
| id | Unique identifier for the pathway | 1 |

#### 9. `execution_pathway_conditions` Table

Stores LO execution pathway conditions.

| Column | Description | Example Value |
|--------|-------------|--------------|
| id | Unique identifier for the condition | 1 |
| lo_id | Reference to the LO | lo001 |
| condition_type | Condition type | attribute |
| condition_entity | Condition entity | LeaveApplication |
| condition_attribute | Condition attribute | status |
| condition_operator | Condition operator | = |
| condition_value | Condition value | Approved |
| next_lo | Reference to the next LO | lo002 |

#### 10. `lo_system_functions` Table

Stores LO system functions.

| Column | Description | Example Value |
|--------|-------------|--------------|
| function_id | Unique identifier for the function | func1 |
| lo_id | Reference to the LO | lo001 |
| function_name | Function name | generate_id |
| function_type | Function type | standard |
| parameters | Parameters (JSON) | {...} |
| output_to | Output to | |

### Mapping Process

1. The `lo_parser.py` parses the natural language prescriptive text and extracts LO information.
2. The `lo_deployer.py` deploys the parsed LO information to the database tables.
3. The LO ID is generated as lo001, lo002, etc.
4. Input stack, output stack, and mappings are stored in their respective tables.
5. Execution pathways and conditions are stored in their respective tables.
6. System functions are stored in the lo_system_functions table.

## Role Mapping

Roles are defined in natural language prescriptives and mapped to multiple database tables.

### Role Definition

Natural language format:
```
Role RoleName (role_id):
- Create: Entity1, Entity2
- Read: Entity1, Entity2, Entity3
- Update: Entity1, Entity2
- GO: go001 as Originator, go002 as ProcessOwner
- Scope: Own
- Classification: Standard
- Special: special conditions

Role RoleName2 (role_id2) inherits RoleName:
- Create: Entity3, Entity4
- Read: Entity3, Entity4, Entity5
- Update: Entity3, Entity4
- GO: go003 as Originator, go004 as ProcessOwner
- Scope: Team
- Classification: Standard
- Special: special conditions
```

### Database Tables

#### 1. `roles` Table

Stores basic role information.

| Column | Description | Example Value |
|--------|-------------|--------------|
| role_id | Unique identifier for the role | role_employee |
| name | Role name | Employee |
| tenant_id | Reference to the tenant | t001 |
| inherits_from | Reference to the parent role | |
| created_at | Creation timestamp | 2023-01-01 00:00:00 |
| updated_at | Update timestamp | 2023-01-01 00:00:00 |
| description | Role description | |
| permissions | Permissions (JSON) | [...] |
| scope | Role scope | Own |
| classification | Role classification | Standard |
| special_conditions | Special conditions | budget approval up to $10K |
| version_type | Version type | v2 |

#### 2. `role_permissions` Table

Stores role permissions.

| Column | Description | Example Value |
|--------|-------------|--------------|
| id | Unique identifier for the permission | 1 |
| role_id | Reference to the role | role_employee |
| context_id | Reference to the permission context | default |
| created_at | Creation timestamp | 2023-01-01 00:00:00 |
| updated_at | Update timestamp | 2023-01-01 00:00:00 |
| permission_name | Permission name | create:Entity1 |

#### 3. `permission_contexts` Table

Stores permission contexts.

| Column | Description | Example Value |
|--------|-------------|--------------|
| context_id | Unique identifier for the context | default |
| name | Context name | Default Context |
| description | Context description | Default permission context |
| context_type | Context type | global |
| context_rules | Context rules (JSON) | {...} |
| created_at | Creation timestamp | 2023-01-01 00:00:00 |
| updated_at | Update timestamp | 2023-01-01 00:00:00 |

#### 4. `role_inheritance` Table

Stores role inheritance.

| Column | Description | Example Value |
|--------|-------------|--------------|
| role_id | Reference to the role | role_manager |
| parent_role_id | Reference to the parent role | role_employee |

### Mapping Process

1. The `role_parser.py` parses the natural language prescriptive text and extracts role information.
2. The `role_deployer.py` deploys the parsed role information to the database tables.
3. The role ID is generated as role_rolename, role_rolename2, etc.
4. Permissions are stored in the role_permissions table.
5. Inheritance is stored in the role_inheritance table.

## Validation Rules

The following validation rules are applied during the parsing and deployment process:

### Entity Validation

1. Entity names must be unique.
2. Attribute names must be unique within an entity.
3. Foreign key attributes must reference existing entities.
4. Relationship source and target entities must exist.
5. Relationship source and target attributes must exist.
6. Business rule entities must exist.
7. Calculated field attributes must exist.

### GO Validation

1. GO names must be unique.
2. Entity references in input and output stacks must reference existing entities.
3. Attribute references in input and output stacks must reference existing attributes.
4. LO references in process flow must reference existing LOs.
5. GO references in output mapping stack must reference existing GOs.
6. Business rules must reference existing LOs.
7. Rollback pathways must reference existing LOs.

### LO Validation

1. LO names must be unique.
2. LO IDs must be unique.
3. Entity references in input and output stacks must reference existing entities.
4. Attribute references in input and output stacks must reference existing attributes.
5. LO references in execution pathways must reference existing LOs.
6. System functions must be valid.
7. UI controls must be valid.

### Role Validation

1. Role names must be unique.
2. Role IDs must be unique.
3. Entity references in permissions must reference existing entities.
4. GO references in permissions must reference existing GOs.
5. Inherited roles must exist.

## Implementation Plan

To implement the parser and deployer logic from Natural Language Prescriptives to Postgres Database, the following steps should be taken:

### 1. Entity Implementation

1. Parse entity definitions using `entity_parser.py`.
2. Validate entity references and relationships.
3. Deploy entities to the database using `entity_deployer.py`.
4. Create entity tables with the naming convention e1_entityname, e2_entityname, etc.
5. Populate entity attributes, relationships, business rules, and validations.

### 2. GO Implementation

1. Parse GO definitions using `go_parser.py`.
2. Validate entity references, LO references, and GO references.
3. Deploy GOs to the database using `go_deployer.py`.
4. Create tenant, book, and chapter records if needed.
5. Populate input stack, output stack, and mappings.
6. Store process mining schema, performance metadata, and other JSON data.

### 3. LO Implementation

1. Parse LO definitions using `lo_parser.py`.
2. Validate entity references, LO references, and system functions.
3. Deploy LOs to the database using `lo_deployer.py`.
4. Populate input stack, output stack, and mappings.
5. Create execution pathways and conditions.
6. Store system functions and UI controls.

### 4. Role Implementation

1. Parse role definitions using `role_parser.py`.
2. Validate entity references, GO references, and inherited roles.
3. Deploy roles to the database using `role_deployer.py`.
4. Create permission contexts if needed.
5. Populate role permissions and inheritance.

### 5. Validation Implementation

1. Implement validation checks in each parser to ensure data integrity.
2. Validate references between components (e.g., GO references to LOs, LO references to entities).
3. Ensure that all required fields are present and valid.
4. Check for duplicate names and IDs.
5. Validate that referenced entities, attributes, LOs, and GOs exist.

### 6. Deployment Sequence

The deployment sequence is important to ensure that dependencies are satisfied:

1. Deploy entities first, as they are referenced by GOs, LOs, and roles.
2. Deploy GOs next, as they are referenced by LOs and roles.
3. Deploy LOs next, as they are referenced by roles.
4. Deploy roles last, as they reference entities, GOs, and LOs.

### 7. Error Handling

1. Implement comprehensive error handling in each parser and deployer.
2. Log errors and warnings for debugging purposes.
3. Provide clear error messages to the user.
4. Roll back database changes if an error occurs during deployment.

### 8. Testing

1. Create unit tests for each parser and deployer.
2. Create integration tests to ensure that the components work together correctly.
3. Test with sample data to ensure that the system works as expected.
4. Test error handling to ensure that the system gracefully handles invalid input.

### 9. Documentation

1. Document the mapping between natural language prescriptives and database tables.
2. Document the validation rules and error messages.
3. Document the deployment sequence and dependencies.
4. Document the testing process and results.

### 10. Maintenance

1. Implement a versioning system to track changes to the parsers and deployers.
2. Implement a migration system to handle changes to the database schema.
3. Implement a backup system to ensure that data is not lost during deployment.
4. Implement a monitoring system to track the performance of the parsers and deployers.
