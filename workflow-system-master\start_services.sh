#!/bin/bash

# <PERSON>ript to start all required services using nohup

echo "Starting services..."

# Start chat-yaml-builder API backend
cd /home/<USER>/workflow-system
echo "Starting chat-yaml-builder API backend..."
nohup python3 chat-yaml-builder/api_chat_yaml_backend.py > chat-yaml-builder/api_chat_yaml.log 2>&1 &
CHAT_YAML_PID=$!
echo "Started chat-yaml-builder API backend with PID: $CHAT_YAML_PID"

# Start backend FastAPI service
cd /home/<USER>/workflow-system
echo "Starting backend FastAPI service..."
nohup python3 -m uvicorn backend.app.main:app --host 0.0.0.0 --port 8020 --reload > backend/logs/backend_api.log 2>&1 &
BACKEND_PID=$!
echo "Started backend FastAPI service with PID: $BACKEND_PID"

# Start enterprise-design-time app
cd /home/<USER>/workflow-system
echo "Starting enterprise-design-time app..."
nohup python3 enterprise-design-time/app.py > enterprise-design-time/logs/enterprise_design_time.log 2>&1 &
DESIGN_TIME_PID=$!
echo "Started enterprise-design-time app with PID: $DESIGN_TIME_PID"

echo "All services started successfully!"
echo "You can check the logs in the respective log files."
echo ""
echo "Service PIDs:"
echo "chat-yaml-builder: $CHAT_YAML_PID"
echo "backend: $BACKEND_PID"
echo "enterprise-design-time: $DESIGN_TIME_PID"
