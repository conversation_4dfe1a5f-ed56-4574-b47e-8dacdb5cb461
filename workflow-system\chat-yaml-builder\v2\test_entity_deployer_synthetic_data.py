#!/usr/bin/env python3
"""
Test script to verify that synthetic data is correctly deployed using the entity_deployer_v2.py file.
"""

import os
import logging
from parsers.entity_parser import parse_entities
from deployers.entity_deployer_v2 import deploy_entities

# Set up logging
logging.basicConfig(level=logging.INFO, 
                   format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger('test_entity_deployer_synthetic_data')

def test_entity_deployer_synthetic_data():
    """
    Test that synthetic data is correctly deployed using the entity_deployer_v2.py file.
    """
    # Define the entity with synthetic data
    entity_def = """
Department has departmentId^PK, name.

Employee has employeeId^PK, firstName, lastName, email, phoneNumber, departmentId^FK, managerId^FK, hireDate, status(Active, Inactive, OnLeave), salary, performanceRating.

* Employee has many-to-one relationship with Department using Employee.departmentId to Department.departmentId^PK
* Employee has many-to-one relationship with <PERSON>ployee using Employee.managerId to Employee.employeeId^PK

* Synthetic: 
Department has departmentId = 101, name = "Engineering".
Department has departmentId = 102, name = "Marketing".
Employee has employeeId = 1, firstName = Tarun, lastName = Singh, email = "<EMAIL>", phoneNumber = "************", departmentId = 101, managerId = null, hireDate = "2024-01-15", status = "Active", salary = 60000, performanceRating = 4.5.
Employee has employeeId = 2, firstName = Priya, lastName = Sharma, email = "<EMAIL>", phoneNumber = "************", departmentId = 102, managerId = 1, hireDate = "2024-02-20", status = "Inactive", salary = 55000, performanceRating = 4.0.
"""
    
    # Parse the entity definition
    logger.info("Parsing entity definition")
    entities_data, warnings = parse_entities(entity_def)
    
    # Print any warnings
    if warnings:
        logger.warning("Warnings during parsing:")
        for warning in warnings:
            logger.warning(f"  - {warning}")
    
    # Check if entities were parsed
    if 'entities' in entities_data:
        for entity_name, entity_def in entities_data['entities'].items():
            logger.info(f"\nEntity: {entity_name}")
            
            # Check if synthetic data was parsed
            if 'synthetic_data' in entity_def:
                logger.info(f"{entity_name} entity synthetic data:")
                for data in entity_def['synthetic_data']:
                    logger.info(f"  - {data}")
            else:
                logger.warning(f"No synthetic data found in {entity_name} entity")
    else:
        logger.error("No entities found in parsed data")
        return
    
    # Deploy the entities to the database
    schema_name = 'workflow_temp'
    logger.info(f"Deploying entities to {schema_name}")
    success, messages = deploy_entities(entities_data, schema_name)
    
    if not success:
        logger.error("Failed to deploy entities:")
        for message in messages:
            logger.error(f"  - {message}")
        return
    
    logger.info("Successfully deployed entities with synthetic data")
    for message in messages:
        logger.info(f"  - {message}")

if __name__ == "__main__":
    test_entity_deployer_synthetic_data()
