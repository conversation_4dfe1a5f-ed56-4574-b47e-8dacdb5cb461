# Purchase Furniture Use Case Implementation Plan

## Current Database Analysis

### Existing Global Objective
- **GO1**: Process Leave Requests (Employee Leave Management)
  - 12 Local Objectives (GO1.LO1 to GO1.LO12)
  - Primary Entity: LeaveApplication (E7)

### Current Entities (E7-E12)
- **E7**: LeaveApplication - Leave Application
- **E8**: Employee - Employee  
- **E9**: LeaveType - Leave Type
- **E10**: LeaveSubType - Leave Sub-Type
- **E11**: Department - Department
- **E12**: Constants - System Constants

### Current Execution Pathways Pattern
- GO1.LO1 → GO1.LO2 (CONDITIONAL) or GO1.LO3 (CONDITIONAL)
- GO1.LO2 → GO1.LO3 (SEQUENTIAL)
- GO1.LO3 → terminal pathways
- Complex branching with PARALLEL, SEQUENTIAL, CONDITIONAL types

## New Implementation: Purchase Furniture (GO2)

### Global Objective
- **GO2**: Purchase Furniture
- **Description**: Manages furniture purchase process from selection to payment and inventory update
- **Primary Entity**: FurnitureOrder (E13)

### Local Objectives
1. **GO2.LO1**: User Selects Furniture Type
   - Function: SELECT with dependent dropdowns
   - User selects furniture type → shows products → adds to cart

2. **GO2.LO2**: Process Cart and Payment
   - Function: CALCULATE (qty × price, GST calculation)
   - User enters quantity, selects payment method
   - Nested functions for calculations

3. **GO2.LO3**: Complete Order and Update Inventory
   - Function: UPDATE inventory
   - Process payment details, show order confirmation
   - Reduce inventory by purchased quantity

### New Entities Required

#### E13: FurnitureOrder (Primary Entity)
- id (auto increment)
- order_id (Primary Key)
- customer_id
- furniture_type
- product_id
- quantity
- unit_price
- subtotal
- gst_amount
- total_amount
- payment_method
- payment_details
- order_status
- order_date
- created_at, updated_at, created_by, updated_by

#### E14: FurnitureProduct
- id (auto increment)
- product_id (Primary Key)
- product_name
- furniture_type_id
- price
- available_inventory
- description
- created_at, updated_at

#### E15: FurnitureType
- id (auto increment)
- furniture_type_id (Primary Key)
- type_name (Cupboard, Bar, Study Table, Dining Table)
- description
- created_at, updated_at

#### E16: Use the existing Users Table


#### E17: PaymentDetails
- payment_id (Primary Key)
- order_id (Foreign Key)
- payment_method (UPI, Credit Card)
- upi_id (for UPI)
- card_name (for Credit Card)
- card_number (for Credit Card)
- cvv (for Credit Card)
- expiry_date (for Credit Card)
- payment_status
- created_at, updated_at

### Constants Required
- GST_RATE: 18%
- PAYMENT_METHODS: ["UPI", "Credit Card"]

### Execution Pathways
- **GO2.LO1.EP1**: GO2.LO1 → GO2.LO2 (SEQUENTIAL)
- **GO2.LO2.EP2**: GO2.LO2 → GO2.LO3 (CONDITIONAL - based on payment method)
- **GO2.LO3.EP3**: GO2.LO3 → terminal (TERMINAL)

### System Functions Needed
Based on existing system, we'll use:
- **SELECT**: For furniture type and product selection
- **CALCULATE**: For price calculations and GST
- **UPDATE**: For inventory reduction
- **CREATE**: For order creation

### LO Input/Output Stacks

#### GO2.LO1 - User Selects Furniture Type
**Input Stack:**
- furniture_type (dropdown: Cupboard, Bar, Study Table, Dining Table)
- product_selection (dependent dropdown based on furniture_type)

**Output Stack:**
- selected_product_id
- selected_furniture_type
- unit_price

#### GO2.LO2 - Process Cart and Payment  
**Input Stack:**
- quantity (user input)
- payment_method (dropdown: UPI, Credit Card)
- payment_details (conditional based on payment_method)

**Nested Functions:**
- calculate_subtotal(quantity × unit_price)
- calculate_gst(subtotal × 0.18)
- calculate_total(subtotal + gst_amount)

**Output Stack:**
- subtotal
- gst_amount
- total_amount
- payment_confirmation

#### GO2.LO3 - Complete Order and Update Inventory
**Input Stack:**
- order_confirmation
- inventory_update_flag

**Output Stack:**
- order_placed_message
- updated_inventory_count
- order_id

### Database Tables to Create
1. **e13_furnitureorder** (GO2.LO1, GO2.LO2, GO2.LO3 execution tables)
2. **e14_furnitureproduct**
3. **e15_furnituretype** 
4. **e16_customer**
5. **e17_paymentdetails**

### ID System Pattern
Following existing pattern:
- Entity IDs: E13, E14, E15, E16, E17
- GO ID: GO2
- LO IDs: GO2.LO1, GO2.LO2, GO2.LO3
- Execution Pathway IDs: GO2.LO1.EP1, GO2.LO2.EP2, GO2.LO3.EP3

### Next Steps
1. Insert Global Objective (GO2)
2. Insert Local Objectives (GO2.LO1, GO2.LO2, GO2.LO3)
3. Insert Entities (E13-E17)
4. Insert Entity Attributes for all entities
5. Insert Execution Pathways
6. Insert Constants (GST_RATE)
7. Insert LO Input/Output Stacks
8. Insert System Functions mappings
9. Create corresponding database tables
10. Insert sample data for testing

### Sample Data Requirements
- 4 Furniture Types (Cupboard, Bar, Study Table, Dining Table)
- 3-4 products per furniture type
- 1 test customer
- GST constant at 18%
