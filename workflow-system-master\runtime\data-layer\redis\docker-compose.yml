version: '3.8'

services:
  redis:
    image: redis:7.0
    container_name: workflow_redis
    restart: always
    ports:
      - "6379:6379"
    command: redis-server --requirepass ${REDIS_PASSWORD}
    volumes:
      - redis_data:/data
    networks:
      - workflow_network

  redis-commander:
    image: rediscommander/redis-commander:latest
    container_name: workflow_redis_commander
    restart: always
    ports:
      - "8082:8081"
    environment:
      REDIS_HOSTS: local:redis:6379:0:${REDIS_PASSWORD}
    networks:
      - workflow_network
    depends_on:
      - redis

volumes:
  redis_data:

networks:
  workflow_network:
    driver: bridge
