# Entity Definition Template

## 1. Basic Entity Declaration
```
[EntityName] has [attribute1]^PK, [attribute2], [attribute3] (enum1, enum2), [attribute4]^FK, [attribute5].
```

## 2. Relationships
```
* [EntityName] has [relationship-type] relationship with [RelatedEntity] using [EntityName].[attributeX]^PK to [RelatedEntity].[attributeY]^FK
```

## 3. Constants & Configuration
```
* [EntityName].[attribute] [PROPERTY_NAME] = [value]
* [EntityName].[attribute] DEFAULT_VALUE = [value]
```

## 4. Validations
```
* [EntityName].[attribute] must be [constraint]
```

## 5. Business Rules
```
BusinessRule [ruleID] for [EntityName]:
* [EntityName].[condition] must be [state] to [action]
* [EntityName].[attribute1] must be [relation] [EntityName].[attribute2]
```

## 6. Calculated/Derived Fields
```
[EntityName] has [attribute1], [attribute2], [calculatedAttribute][derived].

CalculatedField [fieldID] for [EntityName].[calculatedAttribute]:
* Formula: [calculation logic]
* Logic Layer: [implementation layer]
* Caching: [caching strategy]
* Dependencies: [EntityName].[attribute1], [EntityName].[attribute2]
```

## 7. Hierarchical Dependencies
```
[EntityName] has [parent1Id]^FK, [parent2Id]^FK with [parent1] constrains [parent2].

* [EntityName].[parent2Id] must belong to selected [EntityName].[parent1Id]
```

## 8. Metadata Components

### 8.1 Entity Hover
```
Entity Hover: [EntityName]

System Properties:
ID: [ID]
Created: [Date] by [Name]
Last Modified: [Date] by [Name]

Editable Properties:
Display Name: [Display Name]
Type: [Entity Type]
Description: [Description]
```

### 8.2 Attribute Hover
```
Attribute Hover: [attributeName]

System Properties:
ID: [ID]
Version: [Version]
Key: [Key Type]
Created: [Date] by [Name]
Last Modified: [Date] by [Name]

Editable Properties:
Display Name: [Display Name]
Data Type: [Data Type]
Type: [Mandatory/Optional]
Format: [Format Pattern]
Values: [Allowed Values]
Default: [Default Value]
Validation: [Validation Type]
Error Message: [Error Message]
Description: [Description]
```

### 8.3 Relationship Hover
```
Relationship Hover: [Relationship Type]

Relationship Properties:
On Delete: [Delete Behavior]
On Update: [Update Behavior]
Foreign Key Type: [Key Type]
```

## 9. Circuit Components

### 9.1 Synthetic Data
```
* Synthetic: [EntityName].[attribute1], [EntityName].[attribute2]
```

### 9.2 Data Classification
```
* [Classification Level]: [EntityName].[attribute1], [EntityName].[attribute2]
```

### 9.3 Loading Behavior
```
* Loading for [EntityName].[relationships/attributes]: [Loading Type]
```

## 10. Data Lifecycle Management

### 10.1 Archive Strategy
```
* Archive Strategy for [EntityName]:
  - Trigger: [Time-based/Event-based/Manual]
  - Criteria: [Conditions for archiving]
  - Retention: [Duration/Policy]
  - Storage: [Storage solution]
  - Access Pattern: [How archived data is accessed]
  - Restoration: [Process for restoring if needed]
```

### 10.2 Purge Rules
```
* Purge Rule for [EntityName]:
  - Trigger: [Time-based/Event-based/Manual]
  - Criteria: [Conditions for permanent deletion]
  - Approvals: [Required approval workflow]
  - Audit: [Audit trail requirements]
  - Dependencies: [Related entities affected]
```

### 10.3 History Tracking
```
* History Tracking for [EntityName]:
  - Tracked Attributes: [list of attributes with entity prefix]
  - Tracking Method: [Audit table/Temporal tables/Event sourcing]
  - Granularity: [Change level/Snapshot level]
  - Retention: [Duration policy]
  - Access Control: [Who can access history]
```

## 11. Workflow Definition

### 11.1 Workflow (Global Objective)
```
* Workflow: [WorkflowName] for [EntityName]
  - States: [state1, state2, state3]
  - Transitions:
    - [state1] → [state2] [role1, role2]
    - [state2] → [state3] [role1]
  - Actions: [action1, action2]
```

## 12. Organizational Placement (Post-Solution Development)

### 12.1 Business Rule Placement
```
BusinessRule [ruleID] Placement:
* Local Objective: [Function]
* Global Objective: [Workflow]
* Chapter: [Sub-module]
* Book: [Module]
* Tenant: [Business]
```

### 12.2 Workflow Placement
```
* Workflow [WorkflowName] Placement:
  - Global Objective: [Workflow name in system]
  - Book: [Module]
  - Chapter: [Sub-module]
  - Tenant: [Business]
```

### 12.3 Entity Placement
```
* Entity Placement for [EntityName]:
  - Tenant: [Business]
  - Book: [Module]
  - Chapter: [Sub-module]
  - Global Objectives: [Workflow1, Workflow2]
  - Local Objectives: [Function1, Function2]
```
