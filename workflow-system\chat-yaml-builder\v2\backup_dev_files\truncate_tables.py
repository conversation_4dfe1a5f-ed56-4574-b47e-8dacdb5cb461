"""
Truncate Tables Script for Chat YAML Builder v2

This script truncates all tables in the workflow_temp schema to allow for clean testing.
It provides options to truncate all tables or specific tables.

Usage:
    python truncate_tables.py [--all] [--entities] [--attributes] [--relationships] [--business_rules] [--validations] [--enums] [--calculated_fields] [--lifecycle]

Options:
    --all: Truncate all tables
    --entities: Truncate entities table
    --attributes: Truncate entity_attributes and entity_attribute_metadata tables
    --relationships: Truncate entity_relationships table
    --business_rules: Truncate entity_business_rules table
    --validations: Truncate attribute_validations table
    --enums: Truncate attribute_enum_values table
    --calculated_fields: Truncate calculated_fields table
    --lifecycle: Truncate entity_lifecycle_management table

Example:
    python truncate_tables.py --all
    python truncate_tables.py --entities --attributes
"""

import os
import sys
import logging
import argparse
from typing import Dict, List, Tuple, Any, Optional

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('truncate_tables.log')
    ]
)
logger = logging.getLogger('truncate_tables')

# Add parent directory to path to import common modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from db_utils import execute_query

def truncate_table(table_name: str, schema_name: str = "workflow_temp") -> Tuple[bool, List[str]]:
    """
    Truncate a table in the specified schema.
    
    Args:
        table_name: Name of the table to truncate
        schema_name: Schema name
        
    Returns:
        Tuple containing:
            - Boolean indicating if truncation was successful
            - List of messages (warnings, errors, or success messages)
    """
    messages = []
    
    try:
        logger.info(f"Truncating table {schema_name}.{table_name}")
        
        # Check if table exists
        success, query_messages, result = execute_query(
            f"""
            SELECT EXISTS (
                SELECT FROM information_schema.tables 
                WHERE table_schema = %s
                AND table_name = %s
            )
            """,
            (schema_name, table_name)
        )
        
        if not success:
            messages.extend(query_messages)
            return False, messages
        
        table_exists = result and result[0][0]
        
        if not table_exists:
            warning_msg = f"Table {schema_name}.{table_name} does not exist"
            logger.warning(warning_msg)
            messages.append(warning_msg)
            return True, messages
        
        # Truncate table
        success, query_messages, _ = execute_query(
            f'TRUNCATE TABLE {schema_name}.{table_name} CASCADE',
            schema_name=schema_name
        )
        
        if not success:
            messages.extend(query_messages)
            return False, messages
        
        success_msg = f"Successfully truncated table {schema_name}.{table_name}"
        logger.info(success_msg)
        messages.append(success_msg)
        
        return True, messages
    except Exception as e:
        error_msg = f"Error truncating table {schema_name}.{table_name}: {str(e)}"
        logger.error(error_msg, exc_info=True)
        messages.append(error_msg)
        return False, messages

def truncate_all_tables(schema_name: str = "workflow_temp") -> Tuple[bool, List[str]]:
    """
    Truncate all tables in the specified schema.
    
    Args:
        schema_name: Schema name
        
    Returns:
        Tuple containing:
            - Boolean indicating if truncation was successful
            - List of messages (warnings, errors, or success messages)
    """
    messages = []
    
    try:
        logger.info(f"Truncating all tables in schema {schema_name}")
        
        # Get all tables in the schema
        success, query_messages, tables = execute_query(
            f"""
            SELECT table_name 
            FROM information_schema.tables 
            WHERE table_schema = %s
            AND table_type = 'BASE TABLE'
            """,
            (schema_name,)
        )
        
        if not success:
            messages.extend(query_messages)
            return False, messages
        
        if not tables:
            warning_msg = f"No tables found in schema {schema_name}"
            logger.warning(warning_msg)
            messages.append(warning_msg)
            return True, messages
        
        # Truncate each table
        for table in tables:
            table_name = table[0]
            
            # Skip tables with dot notation (e.g., e1.employee)
            if '.' in table_name:
                warning_msg = f"Skipping table {schema_name}.{table_name} (contains dot notation)"
                logger.warning(warning_msg)
                messages.append(warning_msg)
                continue
            
            success, table_messages = truncate_table(table_name, schema_name)
            messages.extend(table_messages)
            
            if not success:
                return False, messages
        
        success_msg = f"Successfully truncated all tables in schema {schema_name}"
        logger.info(success_msg)
        messages.append(success_msg)
        
        return True, messages
    except Exception as e:
        error_msg = f"Error truncating all tables in schema {schema_name}: {str(e)}"
        logger.error(error_msg, exc_info=True)
        messages.append(error_msg)
        return False, messages

def main():
    """
    Main function to run the script.
    """
    parser = argparse.ArgumentParser(description='Truncate tables in the workflow_temp schema')
    parser.add_argument('--all', action='store_true', help='Truncate all tables')
    parser.add_argument('--entities', action='store_true', help='Truncate entities table')
    parser.add_argument('--attributes', action='store_true', help='Truncate entity_attributes and entity_attribute_metadata tables')
    parser.add_argument('--relationships', action='store_true', help='Truncate entity_relationships table')
    parser.add_argument('--business_rules', action='store_true', help='Truncate entity_business_rules table')
    parser.add_argument('--validations', action='store_true', help='Truncate attribute_validations table')
    parser.add_argument('--enums', action='store_true', help='Truncate attribute_enum_values table')
    parser.add_argument('--calculated_fields', action='store_true', help='Truncate calculated_fields table')
    parser.add_argument('--lifecycle', action='store_true', help='Truncate entity_lifecycle_management table')
    
    args = parser.parse_args()
    
    # If no arguments are provided, show help
    if not any(vars(args).values()):
        parser.print_help()
        return
    
    schema_name = "workflow_temp"
    
    # Truncate all tables
    if args.all:
        success, messages = truncate_all_tables(schema_name)
        for message in messages:
            print(message)
        return
    
    # Truncate specific tables
    if args.entities:
        success, messages = truncate_table("entities", schema_name)
        for message in messages:
            print(message)
    
    if args.attributes:
        success, messages = truncate_table("entity_attributes", schema_name)
        for message in messages:
            print(message)
        
        success, messages = truncate_table("entity_attribute_metadata", schema_name)
        for message in messages:
            print(message)
    
    if args.relationships:
        success, messages = truncate_table("entity_relationships", schema_name)
        for message in messages:
            print(message)
    
    if args.business_rules:
        success, messages = truncate_table("entity_business_rules", schema_name)
        for message in messages:
            print(message)
    
    if args.validations:
        success, messages = truncate_table("attribute_validations", schema_name)
        for message in messages:
            print(message)
    
    if args.enums:
        success, messages = truncate_table("attribute_enum_values", schema_name)
        for message in messages:
            print(message)
    
    if args.calculated_fields:
        success, messages = truncate_table("calculated_fields", schema_name)
        for message in messages:
            print(message)
    
    if args.lifecycle:
        success, messages = truncate_table("entity_lifecycle_management", schema_name)
        for message in messages:
            print(message)

if __name__ == "__main__":
    main()
