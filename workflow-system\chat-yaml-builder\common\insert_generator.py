#!/usr/bin/env python3
"""
MongoDB to PostgreSQL Migration Script (Schema-Based)

This script migrates workflow data from MongoDB to a PostgreSQL database.
It creates a separate schema for each tenant within the same PostgreSQL database.
All tables include created_at and updated_at timestamps.

Usage:
    python migrate_mongodb_to_postgres.py [json_file_path]

Configuration:
    Configure the database connection details and file paths in the script constants.
"""

import os
import sys
import signal
import time
import json
import traceback
import psycopg2
from psycopg2.extras import <PERSON><PERSON>
from datetime import datetime

# Set log path to current working directory
LOG_PATH = os.path.join(
    os.path.dirname(os.path.abspath(__file__)),
    f"insert_log_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
)

# Logging function
def log(msg):
    with open(LOG_PATH, "a") as f:
        f.write(f"{datetime.now().strftime('%Y-%m-%d %H:%M:%S')} | {msg}\n")
    print(msg)

# === Configuration ===
PG_CONFIG = {
    "dbname": "workflow_system",  # Use a single database for all tenants
    "user": "postgres",
    "password": "workflow_postgres_secure_password",
    "host": "**********",
    "port": "5432"
}

# === Fix traceback errors ===
def safe_traceback():
    log(traceback.format_exc())
    traceback.print_exc()

MONGO_JSON_FILE = "workflow_data.json"  # Path to MongoDB JSON export file
SQL_SCHEMA_FILE = "db_schema.sql"  # Path to SQL schema file

# === Database Connection Functions ===

def connect_with_retry(db_config, retries=5, delay=1):
    """Attempt to connect to PostgreSQL with retries."""
    for attempt in range(retries):
        try:
            return psycopg2.connect(**db_config)
        except psycopg2.OperationalError:
            log(f"⏳ Waiting for DB to be ready... retry {attempt + 1}/{retries}")
            time.sleep(delay)
    raise Exception("❌ Could not connect to database after multiple retries.")

def check_and_create_schema(pg_conn, tenant_id):
    """Check if schema exists and create if needed."""
    schema_name = "workflow_runtime"

    try:
        with pg_conn.cursor() as cursor:
            # Check if schema exists
            cursor.execute("SELECT schema_name FROM information_schema.schemata WHERE schema_name = %s", (schema_name,))
            exists = cursor.fetchone()

            # Set the search path to our schema for this connection
            cursor.execute(f"SET search_path TO {schema_name}")
            pg_conn.commit()

        return schema_name

    except Exception as e:
        pg_conn.rollback()
        log(f"❌ Error checking/creating schema: {e}")
        traceback.print_exc()
        log(traceback.format_exc())
        sys.exit(1)

def create_tables_in_schema(pg_conn, sql_path, schema_name):
    """Create database tables from SQL schema file in the specified schema."""
    try:
        # First, ensure we're using the right schema
        with pg_conn.cursor() as cursor:
            cursor.execute(f"SET search_path TO {schema_name}")
            pg_conn.commit()

        # Read the SQL file
        with open(sql_path, "r") as file:
            sql_content = file.read()

        # Add created_at and updated_at columns to every CREATE TABLE statement
        sql_lines = sql_content.split('\n')
        modified_sql = []
        inside_create_table = False

        for line in sql_lines:
            if line.strip().startswith('CREATE TABLE IF NOT EXISTS'):
                inside_create_table = True
                modified_sql.append(line)
            elif inside_create_table and line.strip().endswith(');'):
                # Add timestamp columns before closing parenthesis
                indentation = line.split(');')[0]
                modified_sql.append(f"{indentation},")
                modified_sql.append(f"{indentation}    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,")
                modified_sql.append(f"{indentation}    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP);")
                inside_create_table = False
            else:
                modified_sql.append(line)

        # Join the modified lines back into a single string
        modified_sql_content = '\n'.join(modified_sql)

        # Split SQL by semicolon to execute each statement separately
        # This allows us to handle errors more gracefully
        statements = modified_sql_content.split(';')

        with pg_conn.cursor() as cursor:
            for statement in statements:
                if statement.strip():  # Skip empty statements
                    try:
                        cursor.execute(statement)
                        pg_conn.commit()
                    except Exception as e:
                        pg_conn.rollback()
                        log(f"⚠️ Error executing statement: {e}")
                        log(f"Statement: {statement[:100]}...")  # log first 100 chars of statement

        log(f"✅ Tables created successfully in schema {schema_name} with timestamp columns.")
    except Exception as e:
        pg_conn.rollback()
        log(f"❌ Error creating tables: {e}")
        traceback.print_exc()
        log(traceback.format_exc())

# === Helper Functions ===

def insert_and_get_stack_id(cursor, table_name, lo_id, description, current_time):
    """Create a stack entry and return its ID, handling conflicts gracefully."""
    log(f"🔧 insert_and_get_stack_id called for {table_name}, lo_id={lo_id}")

    insert_sql = f"""
        INSERT INTO {table_name} (lo_id, description, created_at, updated_at)
        VALUES (%s, %s, %s, %s)
        ON CONFLICT (lo_id) DO NOTHING
        RETURNING id
    """

    log(f"🔧 Executing SQL: {insert_sql}")
    log(f"🔧 Parameters: ({lo_id}, '{description}', {current_time}, {current_time})")
    
    try:
        cursor.execute(insert_sql, (lo_id, description, current_time, current_time))
        result = cursor.fetchone()
        
        if not result:
            log(f"🔧 No ID returned (possible conflict), selecting existing ID")
            cursor.execute(f"SELECT id FROM {table_name} WHERE lo_id = %s", (lo_id,))
            result = cursor.fetchone()

        if not result:
            log(f"🚨 Failed to get ID from {table_name} for lo_id={lo_id}")
            raise Exception(f"Insert + select failed for {table_name} with lo_id = {lo_id}")

        log(f"🔧 Successfully got ID: {result[0]}")
        return result[0]
    except Exception as e:
        log(f"🚨 Exception in insert_and_get_stack_id: {str(e)}")
        log(f"🚨 Table: {table_name}, lo_id: {lo_id}")
        log(f"🚨 Stack trace: {traceback.format_exc()}")
        raise

def to_capitalize(word):
    """Format capitalization of source types consistently."""
    if word and word.lower() in ['information', 'system', 'user', 'system_dependent']:
        return word.lower()  # Keep source types lowercase
    return word[0].upper() + word[1:].lower() if word else word

# === Entity Attribute Mapping Function ===
def map_attribute_by_description(cursor, entity_id, source_description):
    """Map an attribute by its description to the correct attribute ID in the database."""
    try:
        # Find the attribute by display name
        cursor.execute(
            """
            SELECT attribute_id FROM entity_attributes 
            WHERE entity_id = %s AND display_name = %s
            """,
            (entity_id, source_description)
        )
        
        result = cursor.fetchone()
        if result:
            return result[0]
        
        # Try case-insensitive match
        cursor.execute(
            """
            SELECT attribute_id FROM entity_attributes 
            WHERE entity_id = %s AND LOWER(display_name) = LOWER(%s)
            """,
            (entity_id, source_description)
        )
        
        result = cursor.fetchone()
        if result:
            return result[0]
        
        # If still not found, log a warning
        log(f"⚠️ Could not find attribute for '{source_description}' in entity {entity_id}")
        return None
    except Exception as e:
        log(f"❌ Error mapping attribute by description: {e}")
        return None

# === Process Functions ===

def process_execution_pathway(cursor, lo, lo_id, current_time):
    """Process execution pathway for a Local Objective."""
    pathway = lo["execution_pathway"]
    pathway_type = pathway.get("type", "")
    # Normalize pathway_type to "alternate"
    normalized_pathway_type = "alternate" if pathway_type.lower() in ["conditional", "alternative", "alternate"] else pathway_type.lower()
    
    # Ensure consistent casing and handle empty strings
    next_lo_raw = pathway.get("next_lo", "")
    next_lo = next_lo_raw.lower() if next_lo_raw else None
    
    # Handle terminal pathways with empty next_lo
    if normalized_pathway_type.lower() == "terminal" and not next_lo:
        # For terminal pathways, we don't need a next_lo value
        # Insert into terminal_pathways table instead
        cursor.execute(
            """
            SELECT 1 FROM terminal_pathways WHERE lo_id = %s
            """,
            (lo_id,)
        )
        
        if not cursor.fetchone():
            cursor.execute(
                """
                INSERT INTO terminal_pathways
                (lo_id, terminal_type, created_at, updated_at)
                VALUES (%s, %s, %s, %s)
                """,
                (lo_id, "end", current_time, current_time)
            )
            cursor.connection.commit()
            log(f"✅ Inserted Terminal Pathway for {lo_id}")
        else:
            log(f"⚠️ Terminal pathway already exists for {lo_id}, skipping")
        
        # For terminal pathways, we still need to insert into execution_pathways
        # but with NULL for next_lo
        cursor.execute(
            """
            SELECT 1 FROM execution_pathways WHERE lo_id = %s
            """,
            (lo_id,)
        )
        
        if not cursor.fetchone():
            cursor.execute(
                """
                INSERT INTO execution_pathways
                (lo_id, pathway_type, next_lo, created_at, updated_at)
                VALUES (%s, %s, NULL, %s, %s)
                """,
                (lo_id, normalized_pathway_type, current_time, current_time)
            )
            cursor.connection.commit()
            log(f"✅ Inserted Execution Pathway (Terminal): {lo_id}")
        else:
            log(f"⚠️ Execution pathway already exists for {lo_id}, skipping")
        
        return
    
    # Check if pathway already exists
    cursor.execute(
        "SELECT 1 FROM execution_pathways WHERE lo_id = %s", 
        (lo_id,)
    )
    
    if not cursor.fetchone():
        cursor.execute(
            """
            INSERT INTO execution_pathways
            (lo_id, pathway_type, next_lo, created_at, updated_at)
            VALUES (%s, %s, %s, %s, %s)
            """,
            (lo_id, normalized_pathway_type, next_lo, current_time, current_time)
        )
        cursor.connection.commit()
        log(f"✅ Inserted Execution Pathway: {lo_id} -> {next_lo}")
    else:
        log(f"⚠️ Execution pathway already exists for {lo_id}, skipping")
    
    # Process conditions for alternate pathways
    if normalized_pathway_type.lower() in ["alternative", "alternate", "conditional"] and "conditions" in pathway:
        for condition in pathway["conditions"]:
            cond = condition["condition"]
            next_lo = condition["next_lo"].lower()
            
            cursor.execute(
                """
                SELECT 1 FROM execution_pathway_conditions
                WHERE lo_id = %s AND next_lo = %s
                """,
                (lo_id, next_lo)
            )
            
            if not cursor.fetchone():
                cursor.execute(
                    """
                    INSERT INTO execution_pathway_conditions
                    (lo_id, condition_type, condition_entity, condition_attribute,
                    condition_operator, condition_value, next_lo, created_at, updated_at)
                    VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s)
                    """,
                    (
                        lo_id,
                        cond["condition_type"],
                        cond["entity"].lower(),
                        cond["attribute"],
                        cond["operator"],
                        cond["value"],
                        next_lo,
                        current_time,
                        current_time
                    )
                )
                cursor.connection.commit()
                log(f"✅ Inserted Execution Pathway Condition: {lo_id} -> {next_lo}")
            else:
                log(f"⚠️ Skipped duplicate condition: {lo_id} -> {next_lo}")

def process_agent_stack(cursor, lo, lo_id, current_time, tenant_id):
    """Process agent stack for a Local Objective."""
    try:
        # Check if agent stack already exists
        cursor.execute(
            "SELECT id FROM agent_stack WHERE lo_id = %s",
            (lo_id,)
        )
        result = cursor.fetchone()
        
        if not result:
            # Create new agent stack
            cursor.execute(
                """
                INSERT INTO agent_stack (lo_id, created_at, updated_at)
                VALUES (%s, %s, %s)
                RETURNING id
                """,
                (lo_id, current_time, current_time)
            )
            result = cursor.fetchone()
            cursor.connection.commit()
            
            if result:
                agent_stack_id = result[0]
                log(f"✅ Created Agent Stack for {lo_id}")
            else:
                raise Exception(f"Failed to create agent stack for {lo_id}")
        else:
            agent_stack_id = result[0]
            log(f"⚠️ Using existing Agent Stack for {lo_id}")
        
        # Process agents
        for agent in lo["agent_stack"]["agents"]:
            process_agent_rights(cursor, agent, agent_stack_id, lo_id, tenant_id, current_time)
            
        return agent_stack_id
    except Exception as e:
        cursor.connection.rollback()
        log(f"❌ Error processing agent stack for {lo_id}: {e}")
        traceback.print_exc()
        raise

def process_agent_rights(cursor, agent, agent_stack_id, lo_id, tenant_id, current_time):
    """Process rights for an agent."""
    role_id = agent["role"].lower()
    log(f"DEBUG: Role ID from YAML: '{role_id}'")
    
    # Check if the role exists
    cursor.execute("SELECT 1 FROM roles WHERE role_id = %s", (role_id,))
    role_exists = cursor.fetchone()
    
    if not role_exists:
        log(f"⚠️ Role {role_id} not found, trying case-insensitive search")
        cursor.execute("SELECT role_id FROM roles WHERE LOWER(role_id) = LOWER(%s)", (role_id,))
        role_result = cursor.fetchone()
        
        if role_result:
            log(f"⚠️ Found role with different case: {role_result[0]}")
            role_id = role_result[0]
        else:
            log(f"⚠️ Role {role_id} not found, creating it")
            cursor.execute(
                """
                INSERT INTO roles (role_id, name, tenant_id, created_at, updated_at)
                VALUES (%s, %s, %s, %s, %s)
                ON CONFLICT DO NOTHING
                """,
                (role_id, f"Auto-generated {role_id}", tenant_id, current_time, current_time)
            )
            cursor.connection.commit()
            log(f"✅ Created missing role: {role_id}")
    
    # Insert agent rights
    rights_added = 0
    for right in agent.get("rights", []):
        right_id = right.lower()
        
        # Check if right already exists
        cursor.execute(
            """
            SELECT 1 FROM agent_rights 
            WHERE agent_stack_id = %s AND role_id = %s AND right_id = %s
            """,
            (agent_stack_id, role_id, right_id)
        )
        
        if not cursor.fetchone():
            cursor.execute(
                """
                INSERT INTO agent_rights
                (agent_stack_id, role_id, right_id, created_at, updated_at)
                VALUES (%s, %s, %s, %s, %s)
                ON CONFLICT DO NOTHING
                """,
                (agent_stack_id, role_id, right_id, current_time, current_time)
            )
            cursor.connection.commit()
            rights_added += 1
    
    log(f"✅ Inserted {rights_added} Agent Rights for role {role_id} in stack for {lo_id}")

def process_input_stack(cursor, lo, lo_id, current_time):
    """Process input stack and all its items for a Local Objective."""
    try:
        # Step 1: Create or retrieve input stack
        log(f"🔍 Creating input stack for {lo_id}...")
        
        input_stack_id = insert_and_get_stack_id(
            cursor, "lo_input_stack", lo_id,
            lo["input_stack"].get("description", ""), current_time
        )
        
        log(f"✅ Created or retrieved Input Stack for {lo_id} with ID: {input_stack_id}")
        
        # Step 2: Process each input item - ONE TIME ONLY
        if "inputs" in lo["input_stack"]:
            for input_item in lo["input_stack"]["inputs"]:
                # Insert the input item and all its components in a single operation
                insert_input_item_complete(cursor, input_item, input_stack_id, lo_id, current_time)
        
        return input_stack_id
    except Exception as e:
        cursor.connection.rollback()
        log(f"❌ Error processing input stack for {lo_id}: {e}")
        traceback.print_exc()
        raise

def insert_input_item_complete(cursor, input_item, input_stack_id, lo_id, current_time):
    """Insert an input item and all its components (validations, nested functions, dropdown sources) in one operation."""
    try:
        item_id = input_item.get("id", "").lower()
        slot_id = input_item.get("slot_id", "").lower()
        contextual_id = input_item.get("contextual_id", "").lower()
        source_type = input_item["source"]["type"].lower()
        source_description = input_item["source"].get("description", "")
        
        # Extract entity_id from slot_id and map to correct attribute ID
        parts = slot_id.split('.')
        if len(parts) >= 2:
            entity_id = parts[0]
            
            # Find the correct attribute ID based on source_description
            attribute_id = map_attribute_by_description(cursor, entity_id, source_description)
            
            if attribute_id:
                # Reconstruct the slot_id with the correct attribute ID
                slot_id = f"{entity_id}.{attribute_id}.{item_id}"
                log(f"🔄 Assigned correct slot_id for {item_id}: {slot_id}")
        
        required = input_item.get("required", False)
        ui_control = input_item.get("ui_control", "")
        is_visible = input_item.get("is_visible", True)
        data_type = input_item.get("data_type", "string")
        
        # Process dependencies for system_dependent fields
        dependencies = None
        dependency_type = None
        if source_type == "system_dependent" and "dependencies" in input_item:
            dependencies = Json(input_item.get("dependencies", []))
            dependency_type = input_item.get("dependency_type", "")
        
        # Prepare enhanced metadata
        metadata_obj = input_item.get("metadata", {}) or {}
        metadata = {
            "usage": metadata_obj.get("usage", ""),
            "is_informational": source_type == "information",
            "has_dropdown_source": input_item.get("has_dropdown_source", False)
        }
        
        # Check if input item already exists
        cursor.execute(
            """
            SELECT 1 FROM lo_input_items 
            WHERE id = %s AND input_stack_id = %s
            """,
            (item_id, input_stack_id)
        )
        
        if not cursor.fetchone():
            # Insert the input item
            cursor.execute(
                """
                INSERT INTO lo_input_items
                (id, input_stack_id, lo_id, slot_id, contextual_id, source_type, source_description,
                required, ui_control, metadata, dependencies, dependency_type, is_visible, created_at, updated_at)
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                """,
                (
                    item_id,
                    input_stack_id,
                    lo_id,
                    slot_id,
                    contextual_id,
                    source_type,
                    source_description,
                    required,
                    ui_control,
                    Json(metadata),
                    dependencies,
                    dependency_type,
                    is_visible,
                    current_time,
                    current_time
                )
            )
            cursor.connection.commit()
            log(f"✅ Inserted Input Item {item_id} for {lo_id}")
            
            # Process dropdown source
            if input_item.get("has_dropdown_source", False) and "dropdown_source" in input_item:
                insert_dropdown_source(cursor, input_item["dropdown_source"], item_id, input_stack_id, current_time)
            
            # Process dependencies
            if source_type == "system_dependent" and input_item.get("dependencies"):
                insert_input_dependencies(
                    cursor, item_id, input_stack_id, lo_id, 
                    input_item.get("dependencies", []), 
                    input_item.get("dependency_type", ""), 
                    current_time
                )
            
            # Process validations
            if "validations" in input_item:
                for validation in input_item["validations"]:
                    insert_input_validation(cursor, validation, item_id, input_stack_id, current_time)
            
            # Process nested function
            if "nested_function" in input_item:
                insert_nested_function(
                    cursor, input_item["nested_function"], lo_id, 
                    item_id, contextual_id, current_time
                )
            
            # Process multiple nested functions
            if "nested_functions" in input_item and isinstance(input_item["nested_functions"], list):
                for nested_func in input_item["nested_functions"]:
                    insert_nested_function(
                        cursor, nested_func, lo_id, 
                        item_id, contextual_id, current_time
                    )
        else:
            log(f"⚠️ Skipped duplicate input item: {lo_id}.{item_id}")
            
            # Even if input item exists, process validations if required
            if "validations" in input_item:
                for validation in input_item["validations"]:
                    insert_input_validation(cursor, validation, item_id, input_stack_id, current_time)
            
        return item_id
    
    except Exception as e:
        cursor.connection.rollback()
        log(f"❌ Error inserting input item {input_item.get('id', 'unknown')}: {e}")
        traceback.print_exc()
        return None

def insert_input_validation(cursor, validation, item_id, input_stack_id, current_time):
    """Insert a validation for an input item."""
    try:
        rule = validation.get("rule", "")
        rule_type = validation.get("rule_type", "")
        validation_method = validation.get("validation_method", "")
        allowed_values = Json(validation.get("allowed_values")) if "allowed_values" in validation else None
        entity = validation.get("entity", None)
        attribute = validation.get("attribute", None)
        error_message = validation.get("error_message", "")
        
        # Check if validation already exists
        cursor.execute(
            """
            SELECT 1 FROM lo_input_validations 
            WHERE input_item_id = %s AND input_stack_id = %s AND rule = %s
            """,
            (item_id, input_stack_id, rule)
        )
        
        if not cursor.fetchone():
            cursor.execute(
                """
                INSERT INTO lo_input_validations
                (input_item_id, input_stack_id, rule, rule_type, validation_method, allowed_values,
                entity, attribute, error_message, created_at, updated_at)
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                ON CONFLICT DO NOTHING
                """,
                (
                    item_id,
                    input_stack_id,
                    rule,
                    rule_type,
                    validation_method,
                    allowed_values,
                    entity,
                    attribute,
                    error_message,
                    current_time,
                    current_time
                )
            )
            cursor.connection.commit()
            log(f"✅ Inserted Validation for {item_id}: {rule_type}")
        else:
            log(f"⚠️ Validation already exists for {item_id}: {rule_type}")
    
    except Exception as e:
        cursor.connection.rollback()
        log(f"❌ Error inserting validation for {item_id}: {e}")
        traceback.print_exc()

def insert_nested_function(cursor, nested_func, lo_id, item_id, contextual_id, current_time):
    """Insert a nested function with proper duplicate detection."""
    try:
        # DEBUG: Print the nested function details
        log(f"DEBUG: insert_nested_function called with:")
        log(f"DEBUG: nested_func = {nested_func}")
        log(f"DEBUG: lo_id = {lo_id}")
        log(f"DEBUG: item_id = {item_id}")
        log(f"DEBUG: contextual_id = {contextual_id}")
        
        # Ensure nested_func is a dictionary
        if not isinstance(nested_func, dict):
            log(f"⚠️ Nested function for input {item_id} is not a valid object: {nested_func}")
            return
        
        function_id = nested_func.get("id", "").lower()
        function_name = nested_func.get("function_name", "").lower()
        function_type = nested_func.get("function_type", "").lower()
        parameters = nested_func.get("parameters", {}) or {}  # Ensure parameters is a dict even if None
        output_to = nested_func.get("output_to", "")
        
        # DEBUG: Print the extracted values
        log(f"DEBUG: function_id = {function_id}")
        log(f"DEBUG: function_name = {function_name}")
        log(f"DEBUG: function_type = {function_type}")
        log(f"DEBUG: parameters = {parameters}")
        log(f"DEBUG: output_to = {output_to}")
        
        # Normalize attribute in parameters
        # if "attribute" in parameters and not parameters["attribute"].startswith("at"):
        #     try:
        #         cursor.execute("""
        #             SELECT SPLIT_PART(slot_id, '.', 2)
        #             FROM lo_input_items
        #             WHERE contextual_id = %s AND lo_id = %s
        #             LIMIT 1
        #         """, (contextual_id, lo_id))
        #         res = cursor.fetchone()
        #         if res:
        #             corrected_attr = res[0].lower()
        #             parameters["attribute"] = corrected_attr
        #             log(f"🔁 Corrected nested_function param 'attribute': {contextual_id} → {corrected_attr}")
        #     except Exception as e:
        #         log(f"⚠️ Failed to normalize nested attribute param for {contextual_id}: {e}")


        # Improve attribute resolution - directly query the entity_attributes table
        if "attribute" in parameters and "entity" in parameters:
            try:
                entity_id = parameters["entity"].lower()
                attr_ref = parameters["attribute"]
                
                # DEBUG: Print the entity and attribute reference
                log(f"DEBUG: entity_id = {entity_id}")
                log(f"DEBUG: attr_ref = {attr_ref}")
                
                # Always validate the attribute ID exists in the database, even if it's already in atXXX format
                cursor.execute("""
                    SELECT attribute_id FROM entity_attributes 
                    WHERE entity_id = %s AND attribute_id = %s
                """, (entity_id, attr_ref))
                
                result = cursor.fetchone()
                
                # DEBUG: Print the result of the first query
                log(f"DEBUG: First query result = {result}")
                
                if result:
                    # Attribute ID exists, use it as is
                    parameters["attribute"] = result[0].lower()
                    log(f"✅ Validated attribute ID '{attr_ref}' exists for entity '{entity_id}'")
                else:
                    # Attribute ID doesn't exist, try to resolve by name if it's not in atXXX format
                    if not attr_ref.startswith("at"):
                        cursor.execute("""
                            SELECT attribute_id FROM entity_attributes 
                            WHERE entity_id = %s AND (
                                LOWER(name) = LOWER(%s) OR 
                                LOWER(display_name) = LOWER(%s)
                            )
                        """, (entity_id, attr_ref, attr_ref))
                        
                        result = cursor.fetchone()
                        
                        # DEBUG: Print the result of the name resolution query
                        log(f"DEBUG: Name resolution query result = {result}")
                        
                        if result:
                            parameters["attribute"] = result[0].lower()
                            log(f"✅ Resolved attribute name '{attr_ref}' to ID '{result[0]}' for entity '{entity_id}'")
                        else:
                            log(f"⚠️ Cannot resolve attribute '{attr_ref}' for entity '{entity_id}'")
                    else:
                        # It's in atXXX format but doesn't exist, try to find the correct attribute ID
                        # First, check if we can find the attribute in the entity_attributes table
                        if function_name == 'generate_id':
                            # For generate_id, we need to find the primary key or ID field
                            # Try to find an attribute with 'id' in its name
                            cursor.execute("""
                                SELECT attribute_id FROM entity_attributes 
                                WHERE entity_id = %s AND (
                                    LOWER(name) LIKE '%id%' OR 
                                    LOWER(display_name) LIKE '%id%'
                                )
                                ORDER BY attribute_id
                                LIMIT 1
                            """, (entity_id,))
                            
                            result = cursor.fetchone()
                            
                            # DEBUG: Print the result of the ID field query
                            log(f"DEBUG: ID field query result = {result}")
                            
                            if result:
                                parameters["attribute"] = result[0].lower()
                                log(f"✅ Fixed generate_id attribute from '{attr_ref}' to '{result[0]}' for entity '{entity_id}'")
                            else:
                                # If no ID field found, try to get the first attribute as a fallback
                                cursor.execute("""
                                    SELECT attribute_id FROM entity_attributes 
                                    WHERE entity_id = %s
                                    ORDER BY attribute_id
                                    LIMIT 1
                                """, (entity_id,))
                                
                                result = cursor.fetchone()
                                
                                # DEBUG: Print the result of the fallback query
                                log(f"DEBUG: Fallback query result = {result}")
                                
                                if result:
                                    parameters["attribute"] = result[0].lower()
                                    log(f"✅ Fixed generate_id attribute from '{attr_ref}' to '{result[0]}' for entity '{entity_id}' (fallback)")
                                else:
                                    log(f"⚠️ Cannot find any attribute for entity '{entity_id}'")
            except Exception as e:
                log(f"⚠️ Error resolving attribute for entity: {e}")
        
        
        # # Normalize output_to
        # if output_to and not output_to.startswith("at"):
        #     try:
        #         cursor.execute("""
        #             SELECT SPLIT_PART(slot_id, '.', 2)
        #             FROM lo_input_items
        #             WHERE contextual_id = %s AND lo_id = %s
        #             LIMIT 1
        #         """, (contextual_id, lo_id))
        #         result = cursor.fetchone()
        #         if result:
        #             output_to = result[0].lower()
        #             log(f"🔁 Rewrote output_to for nested function {function_id} → {output_to}")
        #         else:
        #             log(f"⚠️ Could not resolve output_to for {function_id}, keeping: {output_to}")
        #     except Exception as e:
        #         log(f"⚠️ Failed to normalize output_to for {function_id}: {e}")

        # DEBUG: Print the parameters after attribute resolution
        log(f"DEBUG: parameters after attribute resolution = {parameters}")
        
        # Improve output_to resolution
        if output_to and "entity" in parameters:
            try:
                entity_id = parameters["entity"].lower()
                
                # DEBUG: Print the entity for output_to resolution
                log(f"DEBUG: entity_id for output_to resolution = {entity_id}")
                log(f"DEBUG: output_to before resolution = {output_to}")
                
                # First check if output_to is already a valid attribute ID
                if output_to.startswith("at"):
                    cursor.execute("""
                        SELECT attribute_id FROM entity_attributes 
                        WHERE entity_id = %s AND attribute_id = %s
                    """, (entity_id, output_to))
                    
                    result = cursor.fetchone()
                    
                    # DEBUG: Print the result of the output_to validation query
                    log(f"DEBUG: output_to validation query result = {result}")
                    
                    if result:
                        # Valid attribute ID, use it as is
                        output_to = result[0].lower()
                        log(f"✅ Validated output_to attribute ID '{output_to}' exists for entity '{entity_id}'")
                    else:
                        # Invalid attribute ID, try to find the correct one for generate_id function
                        if function_name == 'generate_id':
                            # For generate_id, we need to find the primary key or ID field
                            # Try to find an attribute with 'id' in its name
                            cursor.execute("""
                                SELECT attribute_id FROM entity_attributes 
                                WHERE entity_id = %s AND (
                                    LOWER(name) LIKE '%id%' OR 
                                    LOWER(display_name) LIKE '%id%'
                                )
                                ORDER BY attribute_id
                                LIMIT 1
                            """, (entity_id,))
                            
                            result = cursor.fetchone()
                            
                            # DEBUG: Print the result of the ID field query for output_to
                            log(f"DEBUG: ID field query result for output_to = {result}")
                            
                            if result:
                                output_to = result[0].lower()
                                log(f"✅ Fixed generate_id output_to from '{nested_func.get('output_to')}' to '{output_to}'")
                            else:
                                # If no ID field found, try to get the first attribute as a fallback
                                cursor.execute("""
                                    SELECT attribute_id FROM entity_attributes 
                                    WHERE entity_id = %s
                                    ORDER BY attribute_id
                                    LIMIT 1
                                """, (entity_id,))
                                
                                result = cursor.fetchone()
                                
                                # DEBUG: Print the result of the fallback query for output_to
                                log(f"DEBUG: Fallback query result for output_to = {result}")
                                
                                if result:
                                    output_to = result[0].lower()
                                    log(f"✅ Fixed generate_id output_to from '{nested_func.get('output_to')}' to '{output_to}' (fallback)")
                                else:
                                    log(f"⚠️ Cannot find any attribute for entity '{entity_id}'")
                else:
                    # Not an attribute ID, try to resolve by name
                    cursor.execute("""
                        SELECT attribute_id FROM entity_attributes 
                        WHERE entity_id = %s AND (
                            LOWER(name) = LOWER(%s) OR 
                            LOWER(display_name) = LOWER(%s)
                        )
                    """, (entity_id, output_to, output_to))
                    
                    result = cursor.fetchone()
                    
                    # DEBUG: Print the result of the name resolution query for output_to
                    log(f"DEBUG: Name resolution query result for output_to = {result}")
                    
                    if result:
                        output_to = result[0].lower()
                        log(f"✅ Resolved output_to name '{nested_func.get('output_to')}' to ID '{output_to}'")
                    else:
                        log(f"⚠️ Cannot resolve output_to '{output_to}' for entity '{entity_id}'")
            except Exception as e:
                log(f"⚠️ Error resolving output_to for entity: {e}")
        
        
        # DEBUG: Print the final output_to value
        log(f"DEBUG: output_to after resolution = {output_to}")
        
        # IMPROVED CHECK: Look for any function with the same name for this input context
        cursor.execute(
            """
            SELECT 1 FROM lo_nested_functions 
            WHERE lo_id = %s 
            AND input_contextual_id = %s
            AND function_name = %s
            """,
            (lo_id, contextual_id, function_name)
        )
        
        result = cursor.fetchone()
        
        # DEBUG: Print the result of the duplicate check
        log(f"DEBUG: Duplicate check result = {result}")
        
        if not result:
            # DEBUG: Print the final values before insertion
            log(f"DEBUG: Final values for insertion:")
            log(f"DEBUG: lo_id = {lo_id}")
            log(f"DEBUG: function_id = {function_id}")
            log(f"DEBUG: function_name = {function_name}")
            log(f"DEBUG: function_type = {function_type}")
            log(f"DEBUG: parameters = {parameters}")
            log(f"DEBUG: contextual_id = {contextual_id}")
            log(f"DEBUG: output_to = {output_to}")
            
            cursor.execute(
                """
                INSERT INTO lo_nested_functions
                (lo_id, nested_function_id, function_name, function_type, parameters,
                input_contextual_id, output_to, created_at, updated_at)
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s)
                ON CONFLICT DO NOTHING
                """,
                (
                    lo_id,
                    function_id,
                    function_name,
                    function_type,
                    Json(parameters) if parameters else None,
                    contextual_id,
                    output_to,
                    current_time,
                    current_time
                )
            )
            cursor.connection.commit()
            log(f"✅ Inserted Nested Function: {function_id} for input {item_id}")
        else:
            log(f"⚠️ Nested function {function_name} already exists for {contextual_id} in {lo_id}, skipping")
    
    except Exception as e:
        cursor.connection.rollback()
        log(f"❌ Error inserting nested function for {item_id}: {e}")
        log(f"DEBUG: Exception traceback: {traceback.format_exc()}")

def validate_nested_functions(cursor, schema_name):
    """Verify that all nested function attributes reference valid database columns."""
    log("\n==== VALIDATING NESTED FUNCTIONS ====")
    
    cursor.execute(f"SET search_path TO {schema_name}")
    
    # Get all nested functions that use generate_id
    cursor.execute("""
        SELECT id, lo_id, parameters, output_to 
        FROM lo_nested_functions 
        WHERE function_name = 'generate_id'
    """)
    
    functions = cursor.fetchall()
    errors = []
    
    for func_id, lo_id, params, output_to in functions:
        if isinstance(params, dict) and "entity" in params and "attribute" in params:
            entity_id = params["entity"]
            attribute_id = params["attribute"]
            
            # Verify entity exists
            cursor.execute("""
                SELECT 1 FROM entities WHERE entity_id = %s
            """, (entity_id,))
            
            if not cursor.fetchone():
                errors.append(f"Function {func_id}: Entity {entity_id} not found")
                continue
            
            # Verify attribute exists for entity
            cursor.execute("""
                SELECT 1 FROM entity_attributes 
                WHERE entity_id = %s AND attribute_id = %s
            """, (entity_id, attribute_id))
            
            if not cursor.fetchone():
                errors.append(f"Function {func_id}: Attribute {attribute_id} not found for entity {entity_id}")
                
                # Try to auto-fix by finding the correct attribute
                cursor.execute("""
                    SELECT attribute_id FROM entity_attributes 
                    WHERE entity_id = %s AND (
                        LOWER(name) LIKE '%id%' OR 
                        LOWER(display_name) LIKE '%id%'
                    )
                    ORDER BY attribute_id
                    LIMIT 1
                """, (entity_id,))
                
                correct_attr = cursor.fetchone()
                if correct_attr:
                    log(f"🛠️ Auto-fixing function {func_id}: Updating attribute from {attribute_id} to {correct_attr[0]}")
                    cursor.execute("""
                        UPDATE lo_nested_functions 
                        SET parameters = jsonb_set(
                            parameters::jsonb, 
                            '{attribute}', 
                            %s::jsonb
                        ),
                        output_to = %s
                        WHERE id = %s
                    """, (f'"{correct_attr[0]}"', correct_attr[0], func_id))
                    cursor.connection.commit()
                    log(f"✅ Fixed function {func_id}")
    
    if errors:
        log(f"⚠️ Found {len(errors)} validation errors:")
        for error in errors:
            log(f"  - {error}")
        return False
    
    log("✅ All nested functions validated successfully")
    return True

def insert_dropdown_source(cursor, dropdown_data, input_item_id, input_stack_id, current_time):
    """Insert a dropdown data source configuration for an input item."""
    try:
        # Check if dropdown source already exists
        cursor.execute(
            """
            SELECT 1 FROM dropdown_data_sources 
            WHERE input_item_id = %s AND input_stack_id = %s
            """,
            (input_item_id, input_stack_id)
        )
        
        if cursor.fetchone():
            log(f"⚠️ Dropdown source already exists for {input_item_id}")
            return
            
        source_type = dropdown_data.get("source_type", "function")
        query_text = dropdown_data.get("query_text")
        function_name = dropdown_data.get("function_name")
        function_params = Json(dropdown_data.get("function_params", {})) if dropdown_data.get("function_params") else None
        value_field = dropdown_data.get("value_field", "value")
        display_field = dropdown_data.get("display_field", "display")
        depends_on_fields = Json(dropdown_data.get("depends_on_fields", [])) if dropdown_data.get("depends_on_fields") else None
        
        cursor.execute(
            """
            INSERT INTO dropdown_data_sources
            (input_item_id, input_stack_id, source_type, query_text, function_name, function_params,
            value_field, display_field, depends_on_fields, created_at, updated_at)
            VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
            ON CONFLICT DO NOTHING
            """,
            (
                input_item_id, 
                input_stack_id, 
                source_type, 
                query_text, 
                function_name, 
                function_params,
                value_field, 
                display_field, 
                depends_on_fields, 
                current_time, 
                current_time
            )
        )
        cursor.connection.commit()
        log(f"✅ Inserted Dropdown Source for {input_item_id}")
    except Exception as e:
        cursor.connection.rollback()
        log(f"❌ Error inserting dropdown source for {input_item_id}: {str(e)}")
        traceback.print_exc()

def insert_input_dependencies(cursor, input_item_id, input_stack_id, lo_id, dependencies, dependency_type, current_time):
    """Insert dependencies between input fields with duplicate detection."""
    for dependency_id in dependencies:
        try:
            # Find the dependency item in the same LO
            cursor.execute(
                """
                SELECT input_stack_id FROM lo_input_items 
                WHERE id = %s AND lo_id = %s
                """,
                (dependency_id, lo_id)
            )
            result = cursor.fetchone()
            
            if not result:
                log(f"⚠️ Dependency {dependency_id} not found for {input_item_id}")
                continue
                
            depends_on_stack_id = result[0]
            
            # Default condition expression based on dependency type
            condition_expression = None
            if dependency_type == "dropdown":
                condition_expression = "value_depends_on_parent"
            elif dependency_type == "calculation":
                condition_expression = "calculate_from_parent"
            
            # Check if dependency already exists
            cursor.execute(
                """
                SELECT 1 FROM input_dependencies
                WHERE input_item_id = %s AND depends_on_id = %s
                """,
                (input_item_id, dependency_id)
            )
            
            if not cursor.fetchone():
                cursor.execute(
                    """
                    INSERT INTO input_dependencies
                    (input_item_id, input_stack_id, depends_on_id, depends_on_stack_id,
                    dependency_type, condition_expression, created_at, updated_at)
                    VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
                    ON CONFLICT DO NOTHING
                    """,
                    (
                        input_item_id,
                        input_stack_id,
                        dependency_id,
                        depends_on_stack_id,
                        dependency_type,
                        condition_expression,
                        current_time,
                        current_time
                    )
                )
                cursor.connection.commit()
                log(f"✅ Inserted Dependency: {input_item_id} depends on {dependency_id}")
            else:
                log(f"⚠️ Dependency already exists: {input_item_id} → {dependency_id}")
                
        except Exception as e:
            cursor.connection.rollback()
            log(f"❌ Error inserting dependency for {input_item_id} → {dependency_id}: {str(e)}")
            traceback.print_exc()

def process_output_stack(cursor, lo, lo_id, current_time):
    """Process output stack and its items for a Local Objective."""
    try:
        output_stack_id = insert_and_get_stack_id(
            cursor, "lo_output_stack", lo_id,
            lo["output_stack"].get("description", ""),
            current_time
        )
        log(f"✅ Created or retrieved Output Stack for {lo_id}")
        
        # Insert output items
        if "outputs" in lo["output_stack"]:
            for output_item in lo["output_stack"]["outputs"]:
                insert_output_item(cursor, output_item, output_stack_id, lo_id, current_time)
        
        return output_stack_id
    except Exception as e:
        cursor.connection.rollback()
        log(f"❌ Error processing output stack for {lo_id}: {e}")
        traceback.print_exc()
        raise

def insert_output_item(cursor, output_item, output_stack_id, lo_id, current_time):
    """Insert an output item with duplicate detection."""
    try:
        item_id = output_item["id"].lower()
        slot_id = output_item["slot_id"].lower()
        contextual_id = output_item["contextual_id"].lower()
        source_type = to_capitalize(output_item["source"]["type"])
        data_type = output_item.get("data_type", "string")
        
        # Check if output item already exists
        cursor.execute(
            """
            SELECT 1 FROM lo_output_items 
            WHERE id = %s AND output_stack_id = %s
            """,
            (item_id, output_stack_id)
        )
        
        if not cursor.fetchone():
            cursor.execute(
                """
                INSERT INTO lo_output_items
                (id, output_stack_id, lo_id, slot_id, contextual_id, source, created_at, updated_at)
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
                ON CONFLICT DO NOTHING
                """,
                (
                    item_id,
                    output_stack_id,
                    lo_id,
                    slot_id,
                    contextual_id,
                    source_type,
                    current_time,
                    current_time
                )
            )
            cursor.connection.commit()
            log(f"✅ Inserted Output Item {item_id} for {lo_id}")
        else:
            log(f"⚠️ Skipped duplicate output item: {lo_id}.{item_id}")
    
    except Exception as e:
        cursor.connection.rollback()
        log(f"❌ Error inserting output item {output_item.get('id', 'unknown')}: {e}")
        traceback.print_exc()

def process_data_mapping_stack(cursor, lo, lo_id, current_time):
    """Process data mapping stack and mappings for a Local Objective."""
    try:
        mapping_stack_id = insert_and_get_stack_id(
            cursor, "lo_data_mapping_stack", lo_id,
            lo["data_mapping_stack"].get("description", ""),
            current_time
        )
        log(f"✅ Created or retrieved Data Mapping Stack for {lo_id}")
        
        # Insert data mappings
        if "mappings" in lo["data_mapping_stack"]:
            for mapping in lo["data_mapping_stack"]["mappings"]:
                insert_data_mapping(cursor, mapping, mapping_stack_id, current_time)
        
        return mapping_stack_id
    except Exception as e:
        cursor.connection.rollback()
        log(f"❌ Error processing data mapping stack for {lo_id}: {e}")
        traceback.print_exc()
        raise

def insert_data_mapping(cursor, mapping, mapping_stack_id, current_time):
    """Insert a data mapping with duplicate detection."""
    try:
        mapping_id = mapping["id"].lower()
        source = mapping["source"].lower()
        target = mapping["target"].lower()
        mapping_type = mapping["mapping_type"].lower()
        
        # Check if mapping already exists
        cursor.execute(
            """
            SELECT 1 FROM lo_data_mappings 
            WHERE id = %s AND mapping_stack_id = %s
            """,
            (mapping_id, mapping_stack_id)
        )
        
        if not cursor.fetchone():
            cursor.execute(
                """
                INSERT INTO lo_data_mappings
                (id, mapping_stack_id, source, target, mapping_type, created_at, updated_at)
                VALUES (%s, %s, %s, %s, %s, %s, %s)
                ON CONFLICT DO NOTHING
                """,
                (
                    mapping_id,
                    mapping_stack_id,
                    source,
                    target,
                    mapping_type,
                    current_time,
                    current_time
                )
            )
            cursor.connection.commit()
            log(f"✅ Inserted Data Mapping {mapping_id}")
        else:
            log(f"⚠️ Skipped duplicate data mapping: {mapping_id}")
    
    except Exception as e:
        cursor.connection.rollback()
        log(f"❌ Error inserting data mapping {mapping.get('id', 'unknown')}: {e}")
        traceback.print_exc()

# === Main migration function ===
def migrate_workflow_data_to_schema(pg_conn, workflow_data, schema_name):
    """
    Migrate workflow data to PostgreSQL schema.
    This function handles the actual data migration to the specified schema.
    """
    log("🚀 STARTING WORKFLOW DATA MIGRATION")

    cursor = None
    try:
        cursor = pg_conn.cursor()

        # Set search path to the tenant's schema
        cursor.execute(f"SET search_path TO {schema_name}")
        pg_conn.commit()

        # Current timestamp for created_at and updated_at fields
        current_time = datetime.now()

        # ===== STEP 1: BASE ENTITIES =====
        log("\n==== STEP 1: INSERTING BASE ENTITIES ====")

        # Insert Tenant
        tenant = workflow_data.get("tenant", {})
        tenant_id = tenant.get("id", "T001").lower()
        tenant_name = tenant.get("name", "DefaultTenant").lower()
        log(f"➡️ Inserting Tenant: {tenant_name}...")
        try:
            cursor.execute(
                """
                INSERT INTO tenants
                (tenant_id, name, created_at, updated_at)
                VALUES (%s, %s, %s, %s)
                ON CONFLICT (tenant_id) DO NOTHING
                """,
                (tenant_id, tenant_name, current_time, current_time)
            )
            pg_conn.commit()
            log(f"✅ Inserted Tenant: {tenant_name}")
        except Exception as e:
            pg_conn.rollback()
            log(f"❌ Error inserting tenant: {e}")

        # Insert Permission Types
        # === Ensure Mandatory Permission Types Exist (fix for "execute" not found) ===
        log("\n➡️ Ensuring Standard Permission Types...")
        required_permission_types = [
            ("read", "Can read entity data"),
            ("create", "Can create new records"),
            ("update", "Can update records"),
            ("delete", "Can delete records"),
            ("execute", "Can execute workflows"),
        ]

        for pid, desc in required_permission_types:
            try:
                cursor.execute(
                    """
                    INSERT INTO permission_types (permission_id, description)
                    VALUES (%s, %s)
                    ON CONFLICT (permission_id) DO NOTHING
                    """,
                    (pid, desc)
                )
                pg_conn.commit()
                log(f"✅ Ensured Permission Type: {pid}")
            except Exception as e:
                pg_conn.rollback()
                log(f"❌ Failed to ensure permission_type {pid}: {e}")

        # === Insert any additional permission types from YAML (if present) ===
        log("\n➡️ Inserting Additional Permission Types from YAML...")
        for permission in workflow_data.get("permission_types", []):
            try:
                permission_id = permission.get("id", "").lower()
                description = permission.get("description", "").capitalize()

                # Skip if already covered in default list
                if permission_id in [p[0] for p in required_permission_types]:
                    continue

                cursor.execute(
                    """
                    INSERT INTO permission_types (permission_id, description)
                    VALUES (%s, %s)
                    ON CONFLICT (permission_id) DO NOTHING
                    """,
                    (permission_id, description)
                )
                pg_conn.commit()
                log(f"✅ Inserted Custom Permission Type: {permission_id}")
            except Exception as e:
                pg_conn.rollback()
                log(f"❌ Error inserting custom permission type {permission.get('id', 'unknown')}: {e}")

        # Insert Roles
        log("\n➡️ Inserting Roles...")
        for role in tenant.get("roles", []):
            try:
                role_id = role.get("id", "").lower()
                role_name = role.get("name", "")
                inherits_from = role.get("inherits_from")

                cursor.execute(
                    """
                    INSERT INTO roles
                    (role_id, name, tenant_id, inherits_from, created_at, updated_at)
                    VALUES (%s, %s, %s, %s, %s, %s)
                    ON CONFLICT (role_id) DO NOTHING
                    """,
                    (role_id, role_name, tenant_id, inherits_from, current_time, current_time)
                )
                pg_conn.commit()
                log(f"✅ Inserted Role: {role_id} - {role_name}")
            except Exception as e:
                pg_conn.rollback()
                log(f"❌ Error inserting role {role.get('id', 'unknown')}: {e}")

        # Insert Entities (handle duplicates that might exist in MongoDB JSON)
        log("\n➡️ Inserting Entities...")
        processed_entities = set()  # Keep track of already processed entities
        for entity in workflow_data.get("entities", []):
            try:
                entity_id = entity.get("id", "").lower()
                # Skip if we've already processed this entity (due to duplication in JSON)
                if entity_id in processed_entities:
                    log(f"⚠️ Skipping duplicate entity: {entity_id}")
                    continue

                name = entity.get("name", "")
                version = entity.get("version", "1.0")
                status = entity.get("status", "")
                entity_type = entity.get("type", "")
                attribute_prefix = entity.get("attributes_metadata", {}).get("attribute_prefix", "")
                description = entity.get("description", "").lower()

                cursor.execute(
                    """
                    INSERT INTO entities
                    (entity_id, name, version, status, type, attribute_prefix, description, created_at, updated_at)
                    VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s)
                    ON CONFLICT (entity_id) DO NOTHING
                    """,
                    (entity_id, name, version, status, entity_type, attribute_prefix, description,
                     current_time, current_time)
                )
                pg_conn.commit()
                processed_entities.add(entity_id)
                log(f"✅ Inserted Entity: {entity_id} - {name}")
            except Exception as e:
                pg_conn.rollback()
                log(f"❌ Error inserting entity {entity.get('id', 'unknown')}: {e}")

        # ===== STEP 2: ENTITY ATTRIBUTES AND METADATA =====
        log("\n==== STEP 2: INSERTING ENTITY ATTRIBUTES AND METADATA ====")

        # Insert Entity Attributes
        log("\n➡️ Inserting Entity Attributes...")
        for entity in workflow_data.get("entities", []):
            entity_id = entity.get("id", "").lower()
            if entity_id not in processed_entities:
                log(f"⚠️ Skipping attributes for unprocessed entity: {entity_id}")
                continue

            for attr in entity.get("attributes", []):
                try:
                    attribute_id = attr.get("id", "").lower()
                    name = attr.get("name", "")
                    display_name = attr.get("display_name", "")
                    datatype = attr.get("datatype", "")

                    # Ensure required fields are present
                    if not attribute_id or not name or not datatype:
                        log(f"⚠️ Skipping invalid attribute (missing id, name, or datatype): {attr}")
                        continue

                    version = attr.get("version", "1.0")
                    status = attr.get("status", "").lower()
                    required = attr.get("required", False)
                    reference_entity_id = attr.get("references")

                    # Check if attribute already exists for this entity
                    cursor.execute(
                        """
                        SELECT 1 FROM entity_attributes
                        WHERE attribute_id = %s AND entity_id = %s
                        """,
                        (attribute_id, entity_id)
                    )
                    exists = cursor.fetchone()

                    if exists:
                        log(f"⚠️ Attribute already exists: {entity_id}.{attribute_id}")
                        continue

                    cursor.execute(
                        """
                        INSERT INTO entity_attributes
                        (attribute_id, entity_id, name, display_name, datatype, version, status, required, reference_entity_id, created_at, updated_at)
                        VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                        ON CONFLICT (attribute_id) DO NOTHING
                        """,
                        (attribute_id, entity_id, name, display_name, datatype, version, status, required, reference_entity_id,
                         current_time, current_time)
                    )
                    pg_conn.commit()
                    log(f"✅ Inserted Entity Attribute: {entity_id}.{attribute_id}")

                    # Insert Attribute Validations
                    for validation in attr.get("validations", []):
                        rule = validation.get("rule", "").lower()
                        expression = validation.get("expression", "").lower()

                        if not rule or not expression:
                            continue

                        try:
                            cursor.execute(
                                """
                                INSERT INTO attribute_validations
                                (attribute_id, rule, expression, created_at, updated_at)
                                VALUES (%s, %s, %s, %s, %s)
                                ON CONFLICT DO NOTHING
                                """,
                                (attribute_id, rule, expression, current_time, current_time)
                            )
                            pg_conn.commit()
                            log(f"✅ Inserted Validation for {attribute_id}: {rule}")
                        except Exception as e:
                            pg_conn.rollback()
                            log(f"❌ Error inserting validation for {attribute_id}: {e}")

                    # Insert Enum Values
                    if datatype.lower() == "enum":
                        for value in attr.get("values", []):
                            try:
                                cursor.execute(
                                    """
                                    INSERT INTO attribute_enum_values
                                    (attribute_id, value, created_at, updated_at)
                                    VALUES (%s, %s, %s, %s)
                                    ON CONFLICT DO NOTHING
                                    """,
                                    (attribute_id.lower(), value.lower(), current_time, current_time)
                                )
                                pg_conn.commit()
                                log(f"✅ Inserted Enum Value for {attribute_id}: {value}")
                            except Exception as e:
                                pg_conn.rollback()
                                log(f"❌ Error inserting enum value {value} for {attribute_id}: {e}")

                except Exception as e:
                    pg_conn.rollback()
                    log(f"❌ Error inserting entity attribute {attr.get('id', 'unknown')}: {e}")
        
        # ✅ Insert this AFTER inserting entity_attributes
        log("\n➡️ Generating Entity Attribute Metadata from actual DB inserts...")
        for entity in workflow_data.get("entities", []):
            entity_id = entity.get("id", "").lower()
            if entity_id not in processed_entities:
                log(f"⚠️ Skipping metadata for unprocessed entity: {entity_id}")
                continue

            # Fetch all inserted attributes from DB
            cursor.execute("""
                SELECT attribute_id, name
                FROM entity_attributes
                WHERE entity_id = %s
            """, (entity_id,))
            
            attribute_rows = cursor.fetchall()
            attribute_map = {}
            
            for attr_id, attr_name in attribute_rows:
                attr_id = attr_id.lower()
                attr_name = attr_name.strip()

                attribute_map[attr_id] = attr_name

                try:
                    cursor.execute("""
                        INSERT INTO entity_attribute_metadata
                        (entity_id, attribute_id, attribute_name, required)
                        VALUES (%s, %s, %s, %s)
                        ON CONFLICT DO NOTHING
                    """, (entity_id, attr_id, attr_name, False))  # Default required=False unless specified elsewhere

                    pg_conn.commit()
                    log(f"✅ Inserted Entity Attribute Metadata: {entity_id}.{attr_id}")
                except Exception as e:
                    pg_conn.rollback()
                    log(f"❌ Error inserting attribute metadata for {entity_id}.{attr_id}: {e}")

        log(f"🔁 Reconstructed attribute maps successfully.")

        # ===== STEP 3: ENTITY RELATIONSHIPS =====
        log("\n==== STEP 3: INSERTING ENTITY RELATIONSHIPS ====")

        log("\n➡️ Inserting Entity Relationships...")
        for entity in workflow_data.get("entities", []):
            entity_id = entity.get("id", "").lower()
            if entity_id not in processed_entities:
                log(f"⚠️ Skipping relationships for unprocessed entity: {entity_id}")
                continue

            for relationship in entity.get("relationships", []):
                try:
                    target_entity_id = relationship.get("entity_id", "").lower()
                    relationship_type = relationship.get("type", "").lower()
                    source_attribute_id = relationship.get("through_attribute", "").lower()
                    target_attribute_id = relationship.get("to_attribute", "").lower()

                    cursor.execute(
                        """
                        INSERT INTO entity_relationships
                        (source_entity_id, target_entity_id, relationship_type, source_attribute_id, target_attribute_id, created_at, updated_at)
                        VALUES (%s, %s, %s, %s, %s, %s, %s)
                        ON CONFLICT DO NOTHING
                        """,
                        (entity_id, target_entity_id, relationship_type, source_attribute_id, target_attribute_id,
                         current_time, current_time)
                    )
                    pg_conn.commit()
                    log(f"✅ Inserted Relationship: {entity_id} → {target_entity_id}")
                except Exception as e:
                    pg_conn.rollback()
                    log(f"❌ Error inserting relationship from {entity_id} to {relationship.get('entity_id', 'unknown')}: {e}")

        # ===== STEP 4: PERMISSIONS =====
        log("\n==== STEP 4: INSERTING PERMISSIONS ====")

        # Insert Entity Permissions
        log("\n➡️ Inserting Entity Permissions...")
        for role in tenant.get("roles", []):
            role_id = role.get("id", "").lower()
            for entity_access in role.get("access", {}).get("entities", []):
                entity_id = entity_access.get("entity_id", "").lower()
                for permission in entity_access.get("permissions", []):
                    try:
                        cursor.execute(
                            """
                            INSERT INTO entity_permissions
                            (role_id, entity_id, permission_id, created_at, updated_at)
                            VALUES (%s, %s, %s, %s, %s)
                            ON CONFLICT DO NOTHING
                            """,
                            (role_id, entity_id, permission.lower(), current_time, current_time)
                        )
                        pg_conn.commit()
                        log(f"✅ Inserted Entity Permission: Role {role_id} - Entity {entity_id} - Permission {permission}")
                    except Exception as e:
                        pg_conn.rollback()
                        log(f"❌ Error inserting entity permission {permission} for role {role_id}: {e}")

        # Insert Objective Permissions
        log("\n➡️ Inserting Objective Permissions...")
        for role in tenant.get("roles", []):
            role_id = role.get("id", "").lower()
            for objective_access in role.get("access", {}).get("objectives", []):
                objective_id = objective_access.get("objective_id", "").lower()
                for permission in objective_access.get("permissions", []):
                    try:
                        cursor.execute(
                            """
                            INSERT INTO objective_permissions
                            (role_id, objective_id, permission_id, created_at, updated_at)
                            VALUES (%s, %s, %s, %s, %s)
                            ON CONFLICT DO NOTHING
                            """,
                            (role_id, objective_id, permission.lower(), current_time, current_time)
                        )
                        pg_conn.commit()
                        log(f"✅ Inserted Objective Permission: Role {role_id} - Objective {objective_id} - Permission {permission}")
                    except Exception as e:
                        pg_conn.rollback()
                        log(f"❌ Error inserting objective permission {permission} for role {role_id}: {e}")

        # ===== STEP 5: GLOBAL OBJECTIVES =====
        log("\n==== STEP 5: INSERTING GLOBAL OBJECTIVES ====")

        # Insert Global Objectives
        log("\n➡️ Inserting Global Objectives...")
        for go in workflow_data.get("global_objectives", []):
            try:
                go_id = go.get("id", "").lower()
                name = go.get("name", "").lower()
                version = go.get("version", "1.0").lower()
                status = go.get("status", "").lower()
                description = go.get("description", "").lower()

                cursor.execute(
                    """
                    INSERT INTO global_objectives
                    (go_id, name, version, status, description,tenant_id, created_at, updated_at)
                    VALUES (%s, %s, %s,%s, %s, %s, %s, %s)
                    ON CONFLICT (go_id) DO NOTHING
                    """,
                    (go_id, name, version, status, description, tenant_id.lower(), current_time, current_time)
                )
                pg_conn.commit()
                log(f"✅ Inserted Global Objective: {go_id} - {name}")

                # Insert Input Stack
                if "input_stack" in go:
                    input_stack_description = go["input_stack"].get("description", "").lower()

                    cursor.execute(
                        """
                        INSERT INTO input_stack (go_id, description, created_at, updated_at)
                        VALUES (%s, %s, %s, %s)
                        ON CONFLICT DO NOTHING
                        RETURNING id
                        """,
                        (go_id, input_stack_description.lower(), current_time, current_time)
                    )

                    try:
                        result = cursor.fetchone()
                        pg_conn.commit()

                        if result:
                            input_stack_id = result[0]
                            log(f"✅ Created Input Stack for {go_id}")

                            # Insert Input Items
                            for input_item in go["input_stack"].get("inputs", []):
                                item_id = input_item.get("id", "").lower()
                                slot_id = input_item.get("slot_id", "").lower()
                                contextual_id = input_item.get("contextual_id", "").lower()
                                entity_reference = input_item.get("entity_reference", "").lower()
                                if not entity_reference:
                                    raise ValueError(f"Missing entity_reference for input item {input_item.get('id')}")

                                attribute_reference = input_item.get("attribute_reference", "").lower()
                                source_type = input_item["source"]["type"].lower()
                                source_description = input_item["source"].get("description", "").lower()
                                required = input_item.get("required", False)

                                try:
                                    cursor.execute(
                                        """
                                        INSERT INTO input_items
                                        (id, input_stack_id, slot_id, contextual_id, entity_reference,
                                         attribute_reference, source_type, source_description, required,
                                         created_at, updated_at)
                                        VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                                        ON CONFLICT DO NOTHING
                                        """,
                                        (item_id, input_stack_id, slot_id, contextual_id, entity_reference,
                                         attribute_reference, source_type, source_description, required,
                                         current_time, current_time)
                                    )
                                    pg_conn.commit()
                                    log(f"✅ Inserted Input Item: {item_id}")
                                except Exception as e:
                                    pg_conn.rollback()
                                    log(f"❌ Error inserting input item {item_id}: {e}")

                            # Insert System Functions for Input Stack
                            for function in go["input_stack"].get("system_functions", []):
                                function_id = function.get("function_id", "").lower()
                                function_name = function.get("function_name", "").lower()
                                function_type = function.get("function_type", "").lower()
                                parameters = function.get("parameters", {})
                                output_to = function.get("output_to", "").lower()

                                try:
                                    cursor.execute(
                                        """
                                        INSERT INTO system_functions
                                        (function_id, function_name, function_type, stack_type, stack_id, parameters, output_to,
                                        created_at, updated_at)
                                        VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s)
                                        ON CONFLICT DO NOTHING
                                        """,
                                        (function_id, function_name, function_type, "input", input_stack_id,
                                         Json(parameters), output_to, current_time, current_time)
                                    )
                                    pg_conn.commit()
                                    log(f"✅ Inserted System Function: {function_id}")
                                except Exception as e:
                                    pg_conn.rollback()
                                    log(f"❌ Error inserting system function {function_id}: {e}")
                        else:
                            # Get existing input stack ID
                            cursor.execute(
                                """
                                SELECT id FROM input_stack WHERE go_id = %s
                                """,
                                (go_id,)
                            )
                            result = cursor.fetchone()
                            if result:
                                log(f"⚠️ Input Stack for {go_id} already exists")
                            else:
                                log(f"⚠️ Failed to create Input Stack for {go_id}")
                    except Exception as e:
                        pg_conn.rollback()
                        log(f"⚠️ Error processing input stack: {e}")

                # Insert Output Stack
                if "output_stack" in go:
                    output_stack_description = go["output_stack"].get("description", "")

                    cursor.execute(
                        """
                        INSERT INTO output_stack (go_id, description, created_at, updated_at)
                        VALUES (%s, %s, %s, %s)
                        ON CONFLICT DO NOTHING
                        RETURNING id
                        """,
                        (go_id, output_stack_description, current_time, current_time)
                    )

                    try:
                        result = cursor.fetchone()
                        pg_conn.commit()

                        if result:
                            output_stack_id = result[0]
                            log(f"✅ Created Output Stack for {go_id}")

                            # Insert Output Items
                            for output_item in go["output_stack"].get("outputs", []):
                                item_id = output_item.get("id", "").lower()
                                slot_id = output_item.get("slot_id", "").lower()
                                contextual_id = output_item.get("contextual_id", "").lower()
                                output_entity = output_item.get("output_entity", "").lower()
                                output_attribute = output_item.get("output_attribute", "").lower()
                                data_type = output_item.get("data_type", "").lower()

                                try:
                                    cursor.execute(
                                        """
                                        INSERT INTO output_items
                                        (id, output_stack_id, slot_id, contextual_id, output_entity, output_attribute, data_type,
                                        created_at, updated_at)
                                        VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s)
                                        ON CONFLICT DO NOTHING
                                        """,
                                        (item_id, output_stack_id, slot_id, contextual_id, output_entity, output_attribute, data_type,
                                        current_time, current_time)
                                    )
                                    pg_conn.commit()
                                    log(f"✅ Inserted Output Item: {item_id}")

                                    # Insert Output Triggers
                                    if "triggers" in output_item:
                                        for trigger in output_item["triggers"]["items"]:
                                            trigger_id = trigger.get("id", "").lower()
                                            target_objective = trigger.get("target_objective", "").lower()
                                            target_input = trigger.get("target_input", "").lower()
                                            mapping_type = trigger.get("mapping_type", "").lower()

                                            condition = trigger.get("condition", {})
                                            condition_type = condition.get("condition_type", "").lower()
                                            condition_entity = condition.get("entity", "").lower()
                                            condition_attribute = condition.get("attribute", "").lower()
                                            condition_operator = condition.get("operator", "").lower()
                                            condition_value = condition.get("value", "").lower()

                                            try:
                                                cursor.execute(
                                                    """
                                                    INSERT INTO output_triggers
                                                    (id, output_item_id, output_stack_id, target_objective, target_input,
                                                     mapping_type, condition_type, condition_entity, condition_attribute,
                                                     condition_operator, condition_value, created_at, updated_at)
                                                    VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                                                    ON CONFLICT DO NOTHING
                                                    """,
                                                    (trigger_id, item_id, output_stack_id, target_objective, target_input,
                                                     mapping_type, condition_type, condition_entity, condition_attribute,
                                                     condition_operator, condition_value, current_time, current_time)
                                                )
                                                pg_conn.commit()
                                                log(f"✅ Inserted Output Trigger: {trigger_id}")
                                            except Exception as e:
                                                pg_conn.rollback()
                                                log(f"❌ Error inserting output trigger {trigger_id}: {e}")
                                except Exception as e:
                                    pg_conn.rollback()
                                    log(f"❌ Error inserting output item {item_id}: {e}")
                        else:
                            # Get existing output stack ID
                            cursor.execute(
                                """
                                SELECT id FROM output_stack WHERE go_id = %s
                                """,
                                (go_id,)
                            )
                            result = cursor.fetchone()
                            if result:
                                log(f"⚠️ Output Stack for {go_id} already exists")
                            else:
                                log(f"⚠️ Failed to create Output Stack for {go_id}")
                    except Exception as e:
                        pg_conn.rollback()
                        log(f"⚠️ Error processing output stack: {e}")

                # Insert Data Mapping Stack
                if "data_mapping_stack" in go:
                    mapping_stack_description = go["data_mapping_stack"].get("description", "")

                    cursor.execute(
                        """
                        INSERT INTO data_mapping_stack (go_id, description, created_at, updated_at)
                        VALUES (%s, %s, %s, %s)
                        ON CONFLICT DO NOTHING
                        RETURNING id
                        """,
                        (go_id, mapping_stack_description, current_time, current_time)
                    )

                    try:
                        result = cursor.fetchone()
                        pg_conn.commit()

                        if result:
                            mapping_stack_id = result[0]
                            log(f"✅ Created Data Mapping Stack for {go_id}")

                            # Insert Data Mappings
                            for mapping in go["data_mapping_stack"].get("mappings", []):
                                mapping_id = mapping.get("id", "").lower()
                                source = mapping.get("source", "").lower()
                                target = mapping.get("target", "").lower()
                                mapping_type = mapping.get("mapping_type", "").lower()

                                try:
                                    cursor.execute(
                                        """
                                        INSERT INTO data_mappings
                                        (id, mapping_stack_id, source, target, mapping_type, created_at, updated_at)
                                        VALUES (%s, %s, %s, %s, %s, %s, %s)
                                        ON CONFLICT DO NOTHING
                                        """,
                                        (mapping_id, mapping_stack_id, source, target, mapping_type,
                                         current_time, current_time)
                                    )
                                    pg_conn.commit()
                                    log(f"✅ Inserted Data Mapping: {mapping_id}")
                                except Exception as e:
                                    pg_conn.rollback()
                                    log(f"❌ Error inserting data mapping {mapping_id}: {e}")

                            # Insert Mapping Rules
                            for rule in go["data_mapping_stack"].get("rules", []):
                                rule_id = rule.get("id", "").lower()
                                description = rule.get("description", "").lower()

                                rule_condition = rule.get("rule_condition", {})
                                condition_type = rule_condition.get("condition_type", "").lower()
                                condition_entity = rule_condition.get("entity", "").lower()
                                condition_attribute = rule_condition.get("attribute", "").lower()
                                condition_operator = rule_condition.get("operator", "").lower()
                                condition_value = rule_condition.get("value", "").lower()
                                error_message = rule.get("error_message", "").lower()

                                try:
                                    cursor.execute(
                                        """
                                        INSERT INTO mapping_rules
                                        (id, mapping_stack_id, description, condition_type, condition_entity,
                                         condition_attribute, condition_operator, condition_value, error_message,
                                         created_at, updated_at)
                                        VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                                        ON CONFLICT DO NOTHING
                                        """,
                                        (rule_id, mapping_stack_id, description, condition_type, condition_entity,
                                         condition_attribute, condition_operator, condition_value, error_message,
                                         current_time, current_time)
                                    )
                                    pg_conn.commit()
                                    log(f"✅ Inserted Mapping Rule: {rule_id}")
                                except Exception as e:
                                    pg_conn.rollback()
                                    log(f"❌ Error inserting mapping rule {rule_id}: {e}")
                        else:
                            # Get existing mapping stack ID
                            try:
                                cursor.execute(
                                    """
                                    SELECT id FROM data_mapping_stack WHERE go_id = %s
                                    """,
                                    (go_id,)
                                )
                                result = cursor.fetchone()
                                if result:
                                    log(f"⚠️ Data Mapping Stack for {go_id} already exists")
                                else:
                                    log(f"⚠️ Failed to create Data Mapping Stack for {go_id}")
                            except Exception as e:
                                log(f"⚠️ Error checking data mapping stack: {e}")
                    except Exception as e:
                        pg_conn.rollback()
                        log(f"⚠️ Error processing data mapping stack: {e}")

            except Exception as e:
                pg_conn.rollback()
                log(f"❌ Error processing Global Objective {go.get('id', 'unknown')}: {e}")
                traceback.print_exc()
                log(traceback.format_exc())

        # ===== STEP 6: LOCAL OBJECTIVES =====
        log("\n==== STEP 6: INSERTING LOCAL OBJECTIVES ====")
        
        # First pass: Insert base Local Objectives
        log("\n➡️ Inserting base Local Objectives...")
        local_objectives = workflow_data.get("local_objectives", [])
        if not local_objectives:
            log("⚠️ No local objectives found in data.")
        else:
            # First pass: Insert base LO records
            for lo in local_objectives:
                try:
                    lo_id = lo.get("id", "").lower()
                    contextual_id = lo.get("contextual_id", "").lower()
                    name = lo.get("name", "")
                    function_type = lo.get("function_type", "")
                    workflow_source = lo.get("workflow_source")
                    go_id = lo.get("go_id", "").lower()
                    if not go_id and "." in contextual_id:
                        go_id = contextual_id.split(".")[0].lower()
                    
                    cursor.execute(
                        """
                        INSERT INTO local_objectives
                        (lo_id, contextual_id, name, function_type, workflow_source, go_id, created_at, updated_at)
                        VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
                        ON CONFLICT (lo_id) DO NOTHING
                        """,
                        (lo_id, contextual_id, name, function_type, workflow_source, go_id, current_time, current_time)
                    )
                    pg_conn.commit()
                    log(f"✅ Inserted Local Objective: {lo_id} - {name}")
                except Exception as e:
                    pg_conn.rollback()
                    log(f"❌ Error inserting local objective {lo.get('id', 'unknown')}: {e}")
            
            # Second pass: Insert relationships and additional data
            log("\n➡️ Inserting Local Objective relationships and data...")
            for lo in local_objectives:
                try:
                    lo_id = lo.get("id", "").lower()
                    
                    # Insert execution pathway
                    if "execution_pathway" in lo:
                        process_execution_pathway(cursor, lo, lo_id, current_time)
                    
                    # Insert agent stack
                    if "agent_stack" in lo and "agents" in lo["agent_stack"]:
                        process_agent_stack(cursor, lo, lo_id, current_time, tenant_id)
                        
                    # Insert input stack and items - using a single unified method
                    if "input_stack" in lo:
                        process_input_stack(cursor, lo, lo_id, current_time)
                    
                    # Insert output stack
                    if "output_stack" in lo:
                        process_output_stack(cursor, lo, lo_id, current_time)
                    
                    # Insert data mapping stack
                    if "data_mapping_stack" in lo:
                        process_data_mapping_stack(cursor, lo, lo_id, current_time)
                
                except Exception as e:
                    pg_conn.rollback()
                    log(f"❌ Error processing additional data for {lo.get('id', 'unknown')}: {e}")
                    traceback.print_exc()
                    log(traceback.format_exc())
                    continue

        # Validate nested functions at the end
        log("\n==== VALIDATING DATA INTEGRITY ====")
        cursor = pg_conn.cursor()
        try:
            validate_nested_functions(cursor, schema_name)
        except Exception as e:
            log(f"❌ Error validating nested functions: {e}")
            traceback.print_exc()
        finally:
            cursor.close()
            
        log("✅ MIGRATION COMPLETE")
        log(f"\n🏁 Migration of data to PostgreSQL schema {schema_name} complete!")
        
    

    except Exception as e:
        if pg_conn and cursor:
            pg_conn.rollback()
        log(f"❌ Error migrating workflow data: {e}")
        traceback.print_exc()
        log(traceback.format_exc())

    finally:
        if 'cursor' in locals() and cursor and not cursor.closed:
            cursor.close()

def generate_inserts_from_drafts(mongo_uri="mongodb://localhost:27017/", db_name="workflow_system", collection="workflow"):
    """Generate PostgreSQL inserts from MongoDB draft documents."""
    return direct_mongodb_to_postgres(mongo_uri, db_name, collection)

def direct_mongodb_to_postgres(mongodb_uri="mongodb://localhost:27017/",
                              db_name="workflow_system",
                              collection_name="workflow"):
    """
    Directly migrate data from MongoDB to PostgreSQL without using an intermediate file.
    Uses schemas for multi-tenant separation.
    """
    try:
        from pymongo import MongoClient

        # Connect to MongoDB
        log(f"➡️ Connecting to MongoDB at {mongodb_uri}...")
        client = MongoClient(mongodb_uri)
        db = client[db_name]
        collection = db[collection_name]

        log(f"➡️ Retrieving draft documents from {db_name}.{collection_name}...")
        # Find documents with status "draft"
        draft_workflows = list(collection.find({"status": "draft"}))

        draft_count = len(draft_workflows)

        if draft_count == 0:
            log("ℹ️ No draft documents found in MongoDB collection.")
            return True

        log(f"✅ Found {draft_count} draft document(s) to process.")

        # Process each draft document
        for workflow_doc in draft_workflows:
            try:
                # Extract workflow data
                workflow_data = workflow_doc.get("workflow_data", {})
                if not workflow_data:
                    log("❌ No workflow_data found in MongoDB document.")
                    return False

                # Get tenant ID
                tenant = workflow_data.get("tenant", {})
                tenant_id = tenant.get("id", "T001")
                log(f"➡️ Using tenant ID: {tenant_id}")

                # Connect to PostgreSQL
                log(f"➡️ Connecting to PostgreSQL...")
                pg_conn = connect_with_retry(PG_CONFIG)
                log(f"✅ Connected to PostgreSQL")

                # Check and create schema
                schema_name = "workflow_runtime"

                # Create tables in schema
                if os.path.exists(SQL_SCHEMA_FILE):
                    log(f"➡️ Creating tables in schema {schema_name}...")
                    create_tables_in_schema(pg_conn, SQL_SCHEMA_FILE, schema_name)

                # Migrate workflow data to schema
                migrate_workflow_data_to_schema(pg_conn, workflow_data, schema_name)
                
                # Update the document status to "complete" in MongoDB
                # collection.update_one(
                #     {"_id": workflow_doc["_id"]},
                #     {"$set": {"status": "complete"}}
                # )
            except Exception as e:
                log(f"❌ Error processing document: {e}")
                traceback.print_exc()
                log(traceback.format_exc())
                continue
                
        # Close connections
        pg_conn.close()
        client.close()
        return True
    except Exception as e:
        log(f"❌ Error during direct MongoDB to PostgreSQL migration: {e}")
        traceback.print_exc()
        log(traceback.format_exc())
        return False

def file_mongodb_to_postgres(json_file_path):
    """
    Migrate workflow data from a JSON file to PostgreSQL using schemas.
    """
    try:
        # Read data from JSON file
        log(f"➡️ Reading data from {json_file_path}...")
        with open(json_file_path, 'r') as f:
            workflow_doc = json.load(f)

        # Extract workflow data
        workflow_data = workflow_doc.get("workflow_data", {})
        if not workflow_data:
            log("❌ No workflow_data found in JSON document.")
            return False

        # Get tenant ID
        tenant = workflow_data.get("tenant", {})
        tenant_id = tenant.get("id", "T001")
        log(f"➡️ Using tenant ID: {tenant_id}")

        # Connect to PostgreSQL
        log(f"➡️ Connecting to PostgreSQL...")
        pg_conn = connect_with_retry(PG_CONFIG)
        log(f"✅ Connected to PostgreSQL")

        # Check and create schema
        schema_name = check_and_create_schema(pg_conn, tenant_id)

        # Create tables in schema
        if os.path.exists(SQL_SCHEMA_FILE):
            log(f"➡️ Creating tables in schema {schema_name}...")
            create_tables_in_schema(pg_conn, SQL_SCHEMA_FILE, schema_name)

        # Migrate workflow data to schema
        migrate_workflow_data_to_schema(pg_conn, workflow_data, schema_name)

        # Close connection
        pg_conn.close()
        return True

    except Exception as e:
        log(f"❌ Error during file-based migration: {e}")
        traceback.print_exc()
        log(traceback.format_exc())
        return False

# === Utility Functions for Insert Generation ===
def generate_user_inserts(yaml_data):
    '''Generate SQL inserts for User entities.'''
    inserts = []
    
    # Extract User entities
    entities = yaml_data.get("entities", [])
    user_entities = [e for e in entities if e.get("name") == "User"]
    
    for user in user_entities:
        # Generate SQL for User entity
        user_id = user.get("id")
        attributes = user.get("attributes", [])
        
        # Find required attributes
        user_id_attr = next((a for a in attributes if a.get("name") == "user_id"), None)
        username_attr = next((a for a in attributes if a.get("name") == "username"), None)
        email_attr = next((a for a in attributes if a.get("name") == "email"), None)
        status_attr = next((a for a in attributes if a.get("name") == "status"), None)
        
        if user_id_attr and username_attr and email_attr and status_attr:
            sql = f'''
            INSERT INTO users (user_id, username, email, status)
            VALUES ('{user_id_attr.get("default_value", "")}', 
                    '{username_attr.get("default_value", "")}', 
                    '{email_attr.get("default_value", "")}', 
                    '{status_attr.get("default_value", "active")}');
            '''
            inserts.append(sql)
        
        # Generate SQL for entity table
        sql = f'''
        INSERT INTO entities (id, name, type, version, status, entity_class)
        VALUES ('{user_id}', '{user.get("name")}', '{user.get("type")}', 
                '{user.get("version")}', '{user.get("status")}', 'organizational');
        '''
        inserts.append(sql)
    
    return inserts

def generate_role_inserts(yaml_data):
    '''Generate SQL inserts for Role entities.'''
    inserts = []
    
    # Extract Role entities
    entities = yaml_data.get("entities", [])
    role_entities = [e for e in entities if e.get("name") == "Role"]
    
    for role in role_entities:
        # Generate SQL for Role entity
        role_id = role.get("id")
        attributes = role.get("attributes", [])
        
        # Find required attributes
        role_id_attr = next((a for a in attributes if a.get("name") == "role_id"), None)
        name_attr = next((a for a in attributes if a.get("name") == "name"), None)
        
        if role_id_attr and name_attr:
            sql = f'''
            INSERT INTO roles (role_id, name, description)
            VALUES ('{role_id_attr.get("default_value", "")}', 
                    '{name_attr.get("default_value", "")}', 
                    '');
            '''
            inserts.append(sql)
        
        # Generate SQL for entity table
        sql = f'''
        INSERT INTO entities (id, name, type, version, status, entity_class)
        VALUES ('{role_id}', '{role.get("name")}', '{role.get("type")}', 
                '{role.get("version")}', '{role.get("status")}', 'organizational');
        '''
        inserts.append(sql)
    
    return inserts

def generate_user_role_relationship_inserts(yaml_data):
    '''Generate SQL inserts for User-Role relationships.'''
    inserts = []
    
    # Extract tenant roles
    tenant_roles = []
    if "tenant" in yaml_data and "roles" in yaml_data["tenant"]:
        tenant_roles = yaml_data["tenant"]["roles"]
    
    # Extract agent permissions
    agent_permissions = []
    local_objectives = yaml_data.get("local_objectives", [])
    for lo in local_objectives:
        if "agent_stack" not in lo or "agents" not in lo["agent_stack"]:
            continue
        
        for agent in lo["agent_stack"]["agents"]:
            role = agent.get("role", "")
            users = agent.get("users", [])
            
            # Process user-specific permissions
            for user in users:
                if isinstance(user, str):
                    # Simple email format
                    agent_permissions.append({
                        "role": role,
                        "user": user
                    })
                elif isinstance(user, dict) and "email" in user:
                    # Extended format with user-specific rights
                    agent_permissions.append({
                        "role": role,
                        "user": user["email"]
                    })
    
    # Generate relationships
    for role in tenant_roles:
        role_id = role.get("id")
        
        for perm in agent_permissions:
            if perm.get("role") == role_id and "user" in perm:
                user_email = perm["user"]
                
                # SQL to find user_id by email
                sql = f'''
                -- Find user_id for {user_email}
                SET @user_id = (SELECT user_id FROM users WHERE email = '{user_email}');
                
                -- Insert into user_roles table
                INSERT INTO user_roles (user_id, role_id)
                VALUES (@user_id, '{role_id}');
                
                -- Insert into entity_relationships table
                INSERT INTO entity_relationships (source_entity, source_id, target_entity, target_id, relationship_type)
                VALUES ('User', @user_id, 'Role', '{role_id}', 'has_role');
                '''
                inserts.append(sql)
    
    return inserts

# === Function to verify slot ID integrity ===
def verify_slot_id_integrity(pg_conn, schema_name):
    """Verify the integrity of slot ID mappings in the database."""
    log("\n==== VERIFYING SLOT ID INTEGRITY ====")
    
    cursor = pg_conn.cursor()
    try:
        # Set search path to the workflow_runtime schema
        cursor.execute(f"SET search_path TO {schema_name}")
        
        # Get all input items
        cursor.execute("""
            SELECT i.id, i.slot_id, i.source_description, lo.lo_id
            FROM lo_input_items i
            JOIN lo_input_stack s ON i.input_stack_id = s.id
            JOIN local_objectives lo ON i.lo_id = lo.lo_id
            WHERE i.slot_id IS NOT NULL AND i.slot_id != ''
        """)
        
        items = cursor.fetchall()
        
        log(f"🔍 Checking {len(items)} input items for slot ID integrity")
        
        integrity_issues = []
        
        for item_id, slot_id, description, lo_id in items:
            parts = slot_id.split('.')
            if len(parts) >= 2:
                entity_id = parts[0]
                attribute_id = parts[1]
                
                # Verify that the entity exists
                cursor.execute(
                    """
                    SELECT 1 FROM entities WHERE entity_id = %s
                    """,
                    (entity_id,)
                )
                
                if not cursor.fetchone():
                    integrity_issues.append(f"Entity {entity_id} not found for input {item_id} in {lo_id}")
                    continue
                
                # Verify that the attribute exists for this entity
                cursor.execute(
                    """
                    SELECT display_name FROM entity_attributes 
                    WHERE entity_id = %s AND attribute_id = %s
                    """,
                    (entity_id, attribute_id)
                )
                
                result = cursor.fetchone()
                if not result:
                    integrity_issues.append(f"Attribute {attribute_id} not found for entity {entity_id} in input {item_id}")
                elif description and result[0] != description:
                    integrity_issues.append(f"Attribute {attribute_id} has display name '{result[0]}' but input {item_id} has source description '{description}'")
        
        if integrity_issues:
            log(f"⚠️ Found {len(integrity_issues)} integrity issues:")
            for issue in integrity_issues:
                log(f"  - {issue}")
            return False
        else:
            log("✅ All slot IDs have integrity")
            return True
        
    except Exception as e:
        log(f"❌ Error verifying slot ID integrity: {e}")
        traceback.print_exc()
        return False
    finally:
        cursor.close()

# === Main entry point ===
if __name__ == "__main__":
    try:
        # Set up signal handlers for graceful shutdown
        def signal_handler(sig, frame):
            log(f"\n⚠️ Received signal {sig}, shutting down gracefully...")
            sys.exit(0)
            
        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)
        
        # Execute migration
        if len(sys.argv) > 1:
            # If a JSON file path is provided as an argument
            json_file_path = sys.argv[1]
            if os.path.exists(json_file_path):
                file_mongodb_to_postgres(json_file_path)
            else:
                log(f"❌ JSON file not found: {json_file_path}")
        else:
            # Default: try MongoDB direct migration
            direct_mongodb_to_postgres()
            
    except Exception as e:
        log(f"❌ Unhandled exception: {e}")
        traceback.print_exc()
        sys.exit(1)
