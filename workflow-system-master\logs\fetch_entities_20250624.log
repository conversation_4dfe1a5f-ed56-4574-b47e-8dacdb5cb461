{"timestamp": "2025-06-24T10:33:27.306447", "endpoint": "fetch/entities", "input": {}, "output": {"success": true, "postgres_entities": [], "mongo_drafts": [{"_id": "685442ebf7fe510cb8237bf5", "entity_id": "E13", "name": "LeaveApplication", "display_name": "Leave Application", "tenant_id": "T1001", "tenant_name": "Alpha Corp", "business_domain": "Human Resources", "category": "Leave Management", "tags": ["leave", "application", "request", "approval"], "archival_strategy": "archive_only", "icon": "file-text", "colour_theme": "blue", "version": 1, "status": "draft", "type": "transaction", "description": "Represents an employee's request for time off from work", "table_name": "e13_LeaveApplication", "natural_language": "Tenant: Alpha Corp\n\nLeaveApplication has leaveId^PK, employeeId^FK, startDate, endDate, numDays[derived], reason, leaveTypeName, leaveSubTypeName, status, requiresDocumentation, documentationProvided, submissionDate, approvalDate, approvedBy^FK, comments, isRetroactive[derived], insufficientBalance[derived], lowTeamAvailability[derived], allowedNumberOfDays.\n\nEntity: LeaveApplication\n- Entity Name: LeaveApplication\n- Display Name: Leave Application\n- Type: transaction\n- Description: Represents an employee's request for time off from work\n- Business Domain: Human Resources\n- Category: Leave Management\n- Tags: leave, application, request, approval\n- Archival Strategy: archive_only\n- Icon: file-text\n- Colour Theme: blue", "created_at": "2025-06-19T17:03:39.677413", "updated_at": "2025-06-19T17:03:39.677423", "created_by": "Tarun", "updated_by": "Tarun", "entity_status": "new", "changes_detected": [], "icon_type": "text", "icon_content": ""}, {"_id": "6854484e00387ff38636d6dc", "entity_id": "E14", "name": "LeaveApplication", "display_name": "Leave Application", "tenant_id": "T1007", "tenant_name": "Acme Corp", "business_domain": "Human Resources", "category": "Leave Management", "tags": ["leave", "application", "request", "approval"], "archival_strategy": "archive_only", "icon": "file-text", "colour_theme": "blue", "version": 1, "status": "draft", "type": "transaction", "description": "Represents an employee's request for time off from work", "table_name": "e14_LeaveApplication", "natural_language": "Tenant: Acme Corp\n\nLeaveApplication has leaveId^PK, employeeId^FK, startDate, endDate, numDays[derived], reason, leaveTypeName, leaveSubTypeName, status, requiresDocumentation, documentationProvided, submissionDate, approvalDate, approvedBy^FK, comments, isRetroactive[derived], insufficientBalance[derived], lowTeamAvailability[derived], allowedNumberOfDays.\n\nEntity: LeaveApplication\n- Entity Name: LeaveApplication\n- Display Name: Leave Application\n- Type: transaction\n- Description: Represents an employee's request for time off from work\n- Business Domain: Human Resources\n- Category: Leave Management\n- Tags: leave, application, request, approval\n- Archival Strategy: archive_only\n- Icon: file-text\n- Colour Theme: blue", "created_at": "2025-06-19T17:26:38.418997", "updated_at": "2025-06-19T17:26:38.419004", "created_by": "Tarun", "updated_by": "Tarun", "entity_status": "new", "changes_detected": [], "icon_type": "text", "icon_content": ""}, {"_id": "68550f7b522219aecde6faed", "entity_id": "E15", "name": "Employee", "display_name": "Employee", "tenant_id": "T2", "tenant_name": "Acme Corp", "business_domain": "Human Resources", "category": "Personnel Management", "tags": ["employee", "personnel", "staff", "hr"], "archival_strategy": "archive_only", "icon": "user", "colour_theme": "green", "version": 1, "status": "draft", "type": "master", "description": "Represents an employee in the organization", "table_name": "e15_Employee", "natural_language": "Tenant: Acme Corp\n\nEmployee has employeeId^PK, firstName, lastName, email, phone, departmentId^FK, managerId^FK, jobTitle, hireDate, employmentStatus, annualLeaveBalance, sickLeaveBalance, personalLeaveBalance, totalLeaveEntitlement.\n\nEntity: Employee\n- Entity Name: Employee\n- Display Name: Employee\n- Type: master\n- Description: Represents an employee in the organization\n- Business Domain: Human Resources\n- Category: Personnel Management\n- Tags: employee, personnel, staff, hr\n- Archival Strategy: archive_only\n- Icon: user\n- Colour Theme: green", "created_at": "2025-06-20T07:36:27.358450", "updated_at": "2025-06-20T07:36:27.358458", "created_by": "Tarun", "updated_by": "Tarun", "entity_status": "new", "changes_detected": [], "icon_type": "text", "icon_content": ""}, {"_id": "68556dd43b82120ee564371f", "entity_id": "E44", "name": "Employee", "display_name": "Employee", "tenant_id": "T1003", "tenant_name": "Alpha Corp", "business_domain": "Human Resources", "category": "Employee Management", "tags": ["employee", "staff", "personnel"], "archival_strategy": "archive_only", "icon": "user", "colour_theme": "green", "version": 1, "status": "draft", "type": "master", "description": "Represents an employee in the organization", "table_name": "e44_Employee", "natural_language": "Tenant: Alpha Corp\n\nEmployee has employeeId^PK, firstName, lastName, email, phone, departmentId^FK, hireDate, annualLeaveBalance, sickLeaveBalance, personalLeaveBalance, totalLeaveEntitlement.\n\nEntity: Employee\n- Entity Name: Employee\n- Display Name: Employee\n- Type: master\n- Description: Represents an employee in the organization\n- Business Domain: Human Resources\n- Category: Employee Management\n- Tags: employee, staff, personnel\n- Archival Strategy: archive_only\n- Icon: user\n- Colour Theme: green", "created_at": "2025-06-20T14:19:00.007410", "updated_at": "2025-06-20T14:19:00.007414", "created_by": "Tarun", "updated_by": "Tarun", "entity_status": "new", "changes_detected": [], "icon_type": "text", "icon_content": ""}, {"_id": "6858f51c1195bbcbf7aa7692", "entity_id": "E45", "name": "Employee", "display_name": "Employee", "tenant_id": "T1007", "tenant_name": "Acme Corp Test", "business_domain": "Human Resources", "category": "Employee Management", "tags": ["employee", "hr"], "archival_strategy": "archive_only", "icon": "user", "colour_theme": "green", "version": 1, "status": "draft", "type": "master", "description": "Employee information", "table_name": "e45_Employee", "natural_language": "Tenant: Acme Corp Test\n\nEmployee has employeeId^PK, firstName, lastName, employmentStatus.\n\nEntity: Employee\n- Entity Name: Employee\n- Display Name: Employee\n- Type: master\n- Description: Employee information\n- Business Domain: Human Resources\n- Category: Employee Management\n- Tags: employee, hr\n- Archival Strategy: archive_only\n- Icon: user\n- Colour Theme: green", "created_at": "2025-06-23T06:33:00.529304", "updated_at": "2025-06-23T06:33:00.529311", "created_by": "Tarun", "updated_by": "Tarun", "entity_status": "new", "changes_detected": [], "icon_type": "text", "icon_content": ""}, {"_id": "685938b78ec2e81e85adbc10", "entity_id": "E46", "name": "Employee", "display_name": "Employee", "tenant_id": "T1001", "tenant_name": "Alpha Corp", "business_domain": "Human Resources", "category": "Employee Management", "tags": ["employee", "staff", "personnel"], "archival_strategy": "archive_only", "icon": "user", "colour_theme": "green", "version": 1, "status": "draft", "type": "master", "description": "Represents an employee in the organization", "table_name": "e46_Employee", "natural_language": "Tenant: Alpha Corp\n\nEmployee has employeeId^PK, firstName, lastName, email, phone, departmentId^FK, hireDate, annualLeaveBalance, sickLeaveBalance, personalLeaveBalance, totalLeaveEntitlement.\n\nEntity: Employee\n- Entity Name: Employee\n- Display Name: Employee\n- Type: master\n- Description: Represents an employee in the organization\n- Business Domain: Human Resources\n- Category: Employee Management\n- Tags: employee, staff, personnel\n- Archival Strategy: archive_only\n- Icon: user\n- Colour Theme: green", "created_at": "2025-06-23T11:21:27.162669", "updated_at": "2025-06-23T11:21:27.162678", "created_by": "Tarun", "updated_by": "Tarun", "entity_status": "new", "changes_detected": [], "icon_type": "text", "icon_content": ""}, {"_id": "685a7ec20ab469c89675f244", "entity_id": "E47", "name": "LeaveApplication", "display_name": "Leave Application", "tenant_id": "t999", "tenant_name": "t999", "business_domain": "Human Resources", "category": "Leave Management", "tags": ["leave", "application", "request", "approval"], "archival_strategy": "archive_only", "icon": "file-text", "colour_theme": "blue", "version": 1, "status": "draft", "type": "transaction", "description": "Represents an employee request for time off from work", "table_name": "e47_LeaveApplication", "natural_language": "Tenant: t999\n\nLeaveApplication has leaveId^PK, employeeId^FK, startDate, endDate, numDays[derived], reason, leaveTypeName, leaveSubTypeName, status, requiresDocumentation, documentationProvided, submissionDate, approvalDate, approvedBy^FK, comments, isRetroactive[derived], insufficientBalance[derived], lowTeamAvailability[derived], allowedNumberOfDays.\n\nEntity: LeaveApplication\n- Entity Name: LeaveApplication\n- Display Name: Leave Application\n- Type: transaction\n- Description: Represents an employee request for time off from work\n- Business Domain: Human Resources\n- Category: Leave Management\n- Tags: leave, application, request, approval\n- Archival Strategy: archive_only\n- Icon: file-text\n- Colour Theme: blue", "created_at": "2025-06-24T10:32:34.128331", "updated_at": "2025-06-24T10:32:34.128338", "created_by": "Tarun", "updated_by": "Tarun", "entity_status": "new", "changes_detected": [], "icon_type": "text", "icon_content": ""}], "total_postgres": 0, "total_drafts": 7, "operation": "fetch"}, "status": "success"}
{"timestamp": "2025-06-24T10:44:27.608703", "endpoint": "fetch/entities", "input": {}, "output": {"success": true, "postgres_entities": [], "mongo_drafts": [{"_id": "685442ebf7fe510cb8237bf5", "entity_id": "E13", "name": "LeaveApplication", "display_name": "Leave Application", "tenant_id": "T1001", "tenant_name": "Alpha Corp", "business_domain": "Human Resources", "category": "Leave Management", "tags": ["leave", "application", "request", "approval"], "archival_strategy": "archive_only", "icon": "file-text", "colour_theme": "blue", "version": 1, "status": "draft", "type": "transaction", "description": "Represents an employee's request for time off from work", "table_name": "e13_LeaveApplication", "natural_language": "Tenant: Alpha Corp\n\nLeaveApplication has leaveId^PK, employeeId^FK, startDate, endDate, numDays[derived], reason, leaveTypeName, leaveSubTypeName, status, requiresDocumentation, documentationProvided, submissionDate, approvalDate, approvedBy^FK, comments, isRetroactive[derived], insufficientBalance[derived], lowTeamAvailability[derived], allowedNumberOfDays.\n\nEntity: LeaveApplication\n- Entity Name: LeaveApplication\n- Display Name: Leave Application\n- Type: transaction\n- Description: Represents an employee's request for time off from work\n- Business Domain: Human Resources\n- Category: Leave Management\n- Tags: leave, application, request, approval\n- Archival Strategy: archive_only\n- Icon: file-text\n- Colour Theme: blue", "created_at": "2025-06-19T17:03:39.677413", "updated_at": "2025-06-19T17:03:39.677423", "created_by": "Tarun", "updated_by": "Tarun", "entity_status": "new", "changes_detected": [], "icon_type": "text", "icon_content": ""}, {"_id": "6854484e00387ff38636d6dc", "entity_id": "E14", "name": "LeaveApplication", "display_name": "Leave Application", "tenant_id": "T1007", "tenant_name": "Acme Corp", "business_domain": "Human Resources", "category": "Leave Management", "tags": ["leave", "application", "request", "approval"], "archival_strategy": "archive_only", "icon": "file-text", "colour_theme": "blue", "version": 1, "status": "draft", "type": "transaction", "description": "Represents an employee's request for time off from work", "table_name": "e14_LeaveApplication", "natural_language": "Tenant: Acme Corp\n\nLeaveApplication has leaveId^PK, employeeId^FK, startDate, endDate, numDays[derived], reason, leaveTypeName, leaveSubTypeName, status, requiresDocumentation, documentationProvided, submissionDate, approvalDate, approvedBy^FK, comments, isRetroactive[derived], insufficientBalance[derived], lowTeamAvailability[derived], allowedNumberOfDays.\n\nEntity: LeaveApplication\n- Entity Name: LeaveApplication\n- Display Name: Leave Application\n- Type: transaction\n- Description: Represents an employee's request for time off from work\n- Business Domain: Human Resources\n- Category: Leave Management\n- Tags: leave, application, request, approval\n- Archival Strategy: archive_only\n- Icon: file-text\n- Colour Theme: blue", "created_at": "2025-06-19T17:26:38.418997", "updated_at": "2025-06-19T17:26:38.419004", "created_by": "Tarun", "updated_by": "Tarun", "entity_status": "new", "changes_detected": [], "icon_type": "text", "icon_content": ""}, {"_id": "68550f7b522219aecde6faed", "entity_id": "E15", "name": "Employee", "display_name": "Employee", "tenant_id": "T2", "tenant_name": "Acme Corp", "business_domain": "Human Resources", "category": "Personnel Management", "tags": ["employee", "personnel", "staff", "hr"], "archival_strategy": "archive_only", "icon": "user", "colour_theme": "green", "version": 1, "status": "draft", "type": "master", "description": "Represents an employee in the organization", "table_name": "e15_Employee", "natural_language": "Tenant: Acme Corp\n\nEmployee has employeeId^PK, firstName, lastName, email, phone, departmentId^FK, managerId^FK, jobTitle, hireDate, employmentStatus, annualLeaveBalance, sickLeaveBalance, personalLeaveBalance, totalLeaveEntitlement.\n\nEntity: Employee\n- Entity Name: Employee\n- Display Name: Employee\n- Type: master\n- Description: Represents an employee in the organization\n- Business Domain: Human Resources\n- Category: Personnel Management\n- Tags: employee, personnel, staff, hr\n- Archival Strategy: archive_only\n- Icon: user\n- Colour Theme: green", "created_at": "2025-06-20T07:36:27.358450", "updated_at": "2025-06-20T07:36:27.358458", "created_by": "Tarun", "updated_by": "Tarun", "entity_status": "new", "changes_detected": [], "icon_type": "text", "icon_content": ""}, {"_id": "68556dd43b82120ee564371f", "entity_id": "E44", "name": "Employee", "display_name": "Employee", "tenant_id": "T1003", "tenant_name": "Alpha Corp", "business_domain": "Human Resources", "category": "Employee Management", "tags": ["employee", "staff", "personnel"], "archival_strategy": "archive_only", "icon": "user", "colour_theme": "green", "version": 1, "status": "draft", "type": "master", "description": "Represents an employee in the organization", "table_name": "e44_Employee", "natural_language": "Tenant: Alpha Corp\n\nEmployee has employeeId^PK, firstName, lastName, email, phone, departmentId^FK, hireDate, annualLeaveBalance, sickLeaveBalance, personalLeaveBalance, totalLeaveEntitlement.\n\nEntity: Employee\n- Entity Name: Employee\n- Display Name: Employee\n- Type: master\n- Description: Represents an employee in the organization\n- Business Domain: Human Resources\n- Category: Employee Management\n- Tags: employee, staff, personnel\n- Archival Strategy: archive_only\n- Icon: user\n- Colour Theme: green", "created_at": "2025-06-20T14:19:00.007410", "updated_at": "2025-06-20T14:19:00.007414", "created_by": "Tarun", "updated_by": "Tarun", "entity_status": "new", "changes_detected": [], "icon_type": "text", "icon_content": ""}, {"_id": "6858f51c1195bbcbf7aa7692", "entity_id": "E45", "name": "Employee", "display_name": "Employee", "tenant_id": "T1007", "tenant_name": "Acme Corp Test", "business_domain": "Human Resources", "category": "Employee Management", "tags": ["employee", "hr"], "archival_strategy": "archive_only", "icon": "user", "colour_theme": "green", "version": 1, "status": "draft", "type": "master", "description": "Employee information", "table_name": "e45_Employee", "natural_language": "Tenant: Acme Corp Test\n\nEmployee has employeeId^PK, firstName, lastName, employmentStatus.\n\nEntity: Employee\n- Entity Name: Employee\n- Display Name: Employee\n- Type: master\n- Description: Employee information\n- Business Domain: Human Resources\n- Category: Employee Management\n- Tags: employee, hr\n- Archival Strategy: archive_only\n- Icon: user\n- Colour Theme: green", "created_at": "2025-06-23T06:33:00.529304", "updated_at": "2025-06-23T06:33:00.529311", "created_by": "Tarun", "updated_by": "Tarun", "entity_status": "new", "changes_detected": [], "icon_type": "text", "icon_content": ""}, {"_id": "685938b78ec2e81e85adbc10", "entity_id": "E46", "name": "Employee", "display_name": "Employee", "tenant_id": "T1001", "tenant_name": "Alpha Corp", "business_domain": "Human Resources", "category": "Employee Management", "tags": ["employee", "staff", "personnel"], "archival_strategy": "archive_only", "icon": "user", "colour_theme": "green", "version": 1, "status": "draft", "type": "master", "description": "Represents an employee in the organization", "table_name": "e46_Employee", "natural_language": "Tenant: Alpha Corp\n\nEmployee has employeeId^PK, firstName, lastName, email, phone, departmentId^FK, hireDate, annualLeaveBalance, sickLeaveBalance, personalLeaveBalance, totalLeaveEntitlement.\n\nEntity: Employee\n- Entity Name: Employee\n- Display Name: Employee\n- Type: master\n- Description: Represents an employee in the organization\n- Business Domain: Human Resources\n- Category: Employee Management\n- Tags: employee, staff, personnel\n- Archival Strategy: archive_only\n- Icon: user\n- Colour Theme: green", "created_at": "2025-06-23T11:21:27.162669", "updated_at": "2025-06-23T11:21:27.162678", "created_by": "Tarun", "updated_by": "Tarun", "entity_status": "new", "changes_detected": [], "icon_type": "text", "icon_content": ""}, {"_id": "685a7ec20ab469c89675f244", "entity_id": "E47", "name": "LeaveApplication", "display_name": "Leave Application", "tenant_id": "t999", "tenant_name": "t999", "business_domain": "Human Resources", "category": "Leave Management", "tags": ["leave", "application", "request", "approval"], "archival_strategy": "archive_only", "icon": "file-text", "colour_theme": "blue", "version": 1, "status": "draft", "type": "transaction", "description": "Represents an employee request for time off from work", "table_name": "e47_LeaveApplication", "natural_language": "Tenant: t999\n\nLeaveApplication has leaveId^PK, employeeId^FK, startDate, endDate, numDays[derived], reason, leaveTypeName, leaveSubTypeName, status, requiresDocumentation, documentationProvided, submissionDate, approvalDate, approvedBy^FK, comments, isRetroactive[derived], insufficientBalance[derived], lowTeamAvailability[derived], allowedNumberOfDays.\n\nEntity: LeaveApplication\n- Entity Name: LeaveApplication\n- Display Name: Leave Application\n- Type: transaction\n- Description: Represents an employee request for time off from work\n- Business Domain: Human Resources\n- Category: Leave Management\n- Tags: leave, application, request, approval\n- Archival Strategy: archive_only\n- Icon: file-text\n- Colour Theme: blue", "created_at": "2025-06-24T10:32:34.128331", "updated_at": "2025-06-24T10:32:34.128338", "created_by": "Tarun", "updated_by": "Tarun", "entity_status": "new", "changes_detected": [], "icon_type": "text", "icon_content": ""}], "total_postgres": 0, "total_drafts": 7, "operation": "fetch"}, "status": "success"}
{"timestamp": "2025-06-24T11:36:08.022926", "endpoint": "fetch/entities", "input": {}, "output": {"success": true, "postgres_entities": [], "mongo_drafts": [{"_id": "685a8d23a42e36439cfded3a", "entity_id": "E48", "name": "TestEntity", "display_name": "Test Entity", "tenant_id": "t999", "tenant_name": "", "business_domain": "Testing", "category": "Test Management", "tags": [], "archival_strategy": "", "icon": "", "colour_theme": "", "version": 1, "status": "draft", "type": "transaction", "description": "Test entity for entities_drafts collection", "table_name": "e48_TestEntity", "natural_language": "Entity: TestEntity\n- Entity Name: TestEntity\n- Display Name: Test Entity\n- Type: transaction\n- Description: Test entity for entities_drafts collection\n- Business Domain: Testing\n- Category: Test Management", "created_at": "2025-06-24T11:33:55.338334", "updated_at": "2025-06-24T11:33:55.338343", "created_by": "Tarun", "updated_by": "Tarun", "entity_status": "new", "changes_detected": [], "icon_type": "text", "icon_content": ""}], "total_postgres": 0, "total_drafts": 1, "operation": "fetch"}, "status": "success"}
{"timestamp": "2025-06-24T12:30:58.828706", "endpoint": "fetch/entities", "input": {}, "output": {"success": true, "postgres_entities": [], "mongo_drafts": [], "total_postgres": 0, "total_drafts": 0, "operation": "fetch"}, "status": "success"}
{"timestamp": "2025-06-24T12:32:41.020378", "endpoint": "fetch/entities", "input": {}, "output": {"success": true, "postgres_entities": [], "mongo_drafts": [{"_id": "685a9abda30d43107e83f575", "entity_id": "E48", "name": "TestEntity2", "display_name": "Test Entity 2", "tenant_id": "t999", "tenant_name": "", "business_domain": "Testing", "category": "Test Management", "tags": [], "archival_strategy": "", "icon": "", "colour_theme": "", "version": 1, "status": "draft", "type": "master", "description": "Second test entity for mongo-save verification", "table_name": "e48_TestEntity2", "natural_language": "Entity: TestEntity2\n- Entity Name: TestEntity2\n- Display Name: Test Entity 2\n- Type: master\n- Description: Second test entity for mongo-save verification\n- Business Domain: Testing\n- Category: Test Management", "created_at": "2025-06-24T12:31:57.709761", "updated_at": "2025-06-24T12:31:57.709768", "created_by": "Tarun", "updated_by": "Tarun", "entity_status": "new", "changes_detected": [], "icon_type": "text", "icon_content": ""}], "total_postgres": 0, "total_drafts": 1, "operation": "fetch"}, "status": "success"}
{"timestamp": "2025-06-24T13:10:30.310422", "endpoint": "fetch/entities", "input": {}, "output": {"success": true, "postgres_entities": [], "mongo_drafts": [{"_id": "685a9abda30d43107e83f575", "entity_id": "E48", "name": "TestEntity2", "display_name": "Test Entity 2", "tenant_id": "t999", "tenant_name": "", "business_domain": "Testing", "category": "Test Management", "tags": [], "archival_strategy": "", "icon": "", "colour_theme": "", "version": 1, "status": "draft", "type": "master", "description": "Second test entity for mongo-save verification", "table_name": "e48_TestEntity2", "natural_language": "Entity: TestEntity2\n- Entity Name: TestEntity2\n- Display Name: Test Entity 2\n- Type: master\n- Description: Second test entity for mongo-save verification\n- Business Domain: Testing\n- Category: Test Management", "created_at": "2025-06-24T12:31:57.709761", "updated_at": "2025-06-24T12:31:57.709768", "created_by": "Tarun", "updated_by": "Tarun", "entity_status": "new", "changes_detected": [], "icon_type": "text", "icon_content": ""}], "total_postgres": 0, "total_drafts": 1, "operation": "fetch"}, "status": "success"}
{"timestamp": "2025-06-24T13:13:14.940473", "endpoint": "fetch/entities", "input": {}, "output": {"success": true, "postgres_entities": [], "mongo_drafts": [{"_id": "685a9abda30d43107e83f575", "entity_id": "E48", "name": "TestEntity2", "display_name": "Test Entity 2", "tenant_id": "t999", "tenant_name": "", "business_domain": "Testing", "category": "Test Management", "tags": [], "archival_strategy": "", "icon": "", "colour_theme": "", "version": 1, "status": "draft", "type": "master", "description": "Second test entity for mongo-save verification", "table_name": "e48_TestEntity2", "natural_language": "Entity: TestEntity2\n- Entity Name: TestEntity2\n- Display Name: Test Entity 2\n- Type: master\n- Description: Second test entity for mongo-save verification\n- Business Domain: Testing\n- Category: Test Management", "created_at": "2025-06-24T12:31:57.709761", "updated_at": "2025-06-24T12:31:57.709768", "created_by": "Tarun", "updated_by": "Tarun", "entity_status": "new", "changes_detected": [], "icon_type": "text", "icon_content": ""}], "total_postgres": 0, "total_drafts": 1, "operation": "fetch"}, "status": "success"}
