"""
Function Repository module that handles function registration and execution
"""
from typing import Dict, Any, Callable, List, Optional
import inspect
import logging

logger = logging.getLogger(__name__)

class FunctionRepository:
    """
    Repository for registering and executing system functions.
    Uses decorators for registering functions into categories.
    """
    
    def __init__(self):
        self.functions = {}  # ✅ Ensure this is always initialized
        logger.info("Function Repository initialized with empty function registry.")

    def register(self, category: str, name: Optional[str] = None):
        """
        Decorator for registering a function in the repository.
        """
        def decorator(func: Callable) -> Callable:
            func_name = name or func.__name__
            if category not in self.functions:
                self.functions[category] = {}
            
            if func_name in self.functions[category]:
                logger.warning(f"Function {func_name} in category {category} is being overwritten")
            
            self.functions[category][func_name] = func
            logger.info(f"Registered function {func_name} in category {category}")
            return func
        
        return decorator
    
    def auto_execute(self, function_name: str, *args, **kwargs) -> Any:
        """
        Automatically find the right category and execute a function.
        
        Args:
            function_name: The name of the function to execute
            *args, **kwargs: Additional arguments to pass to the function
            
        Returns:
            Any: The result of the function execution
        """
        # Handle case where function_name is actually input data (dict)
        if isinstance(function_name, dict):
            print(f"WARNING: function_name is a dictionary, likely a parameter order issue.")
            # Assume first arg is LO ID and function_name is input data
            lo_id = args[0] if args else None
            input_data = function_name
            
            # Default to create function
            print(f"DEBUG: Using default 'create' function with LO ID: {lo_id}")
            return self.execute("database", "create", *args[1:] if args else (), input_data)
        
        # Find the category for this function
        function_name = function_name.lower()
        category = None
        
        # Look for exact function name match
        for cat, funcs in self.functions.items():
            if function_name in funcs:
                category = cat
                break
        
        # If not found, try partial matches
        if not category:
            for cat, funcs in self.functions.items():
                for func in funcs:
                    if function_name.startswith(func) or func.startswith(function_name):
                        category = cat
                        function_name = func  # Use the actual function name
                        break
                if category:
                    break
        
        # If still not found, default to database.create
        if not category:
            print(f"WARNING: Function '{function_name}' not found in any category. Using database.create.")
            return self.execute("database", "create", *args, **kwargs)
        
        # Execute with the found category
        print(f"DEBUG: Auto-executing function '{function_name}' in category '{category}'")
        return self.execute(category, function_name, *args, **kwargs)
    
    def execute(self, category: str, function_name: str, *args, **kwargs) -> Any:
        """
        Execute a function by category and name.
        
        Args:
            category: The function category (e.g., "database", "validation")
            function_name: The name of the function to execute
            *args, **kwargs: Additional arguments to pass to the function
        
        Returns:
            Any: The result of the function execution
        """

        if not isinstance(function_name, str):
            raise ValueError(f"Invalid function_name: expected str, got {type(function_name)}")

        category = category.lower()
        function_name = function_name.lower()
        print(f"DEBUG: Executing function '{function_name}' in category '{category}'")

        
        if category in self.functions:
            print(f"DEBUG: Functions in category '{category}': {list(self.functions[category].keys())}")
    

        if category not in self.functions or function_name not in self.functions[category]:
            raise ValueError(f"Function {function_name} in category {category} not found")
        
        if function_name not in self.functions[category]:
            raise ValueError(f"Function '{function_name}' not found in category '{category}'. Available functions: {list(self.functions[category].keys())}")
    

        function = self.functions[category][function_name]
        
        try:
            return function(*args, **kwargs)
        except TypeError as e:
            # If kwargs-only function throws "got 1 positional argument" error, try calling with just kwargs
            if "positional argument" in str(e) and not args:
                print("Retrying function execution using only kwargs...")
                return function(**kwargs)
            else:
                raise

        
       
    
    def get_function(self, category: str, function_name: str) -> Optional[Callable]:
        """Get a function by category and name without executing it."""
        if category not in self.functions or function_name not in self.functions[category]:
            return None
        return self.functions[category][function_name]
    
    def list_categories(self) -> List[str]:
        """List all available function categories."""
        return list(self.functions.keys())
    
    def list_functions(self, category: Optional[str] = None) -> Dict[str, List[str]]:
        """
        List available functions, optionally filtered by category.
        """
        if category:
            if category not in self.functions:
                return {}
            return {category: list(self.functions[category].keys())}
        
        return {cat: list(funcs.keys()) for cat, funcs in self.functions.items()}
    
    def get_function_info(self, category: str, function_name: str) -> Dict[str, Any]:
        """
        Get detailed information about a function.
        """
        function = self.get_function(category, function_name)
        if not function:
            return {}
        
        signature = inspect.signature(function)
        docstring = inspect.getdoc(function) or ""
        
        return {
            "name": function_name,
            "category": category,
            "signature": str(signature),
            "docstring": docstring,
            "parameters": [
                {
                    "name": name,
                    "kind": str(param.kind),
                    "default": None if param.default is inspect.Parameter.empty else param.default,
                    "annotation": str(param.annotation) if param.annotation is not inspect.Parameter.empty else None
                }
                for name, param in signature.parameters.items()
            ]
        }

# ✅ Create a global instance of the repository
function_repository = FunctionRepository()
logger.info("Function Repository initialized and ready for function registration")
