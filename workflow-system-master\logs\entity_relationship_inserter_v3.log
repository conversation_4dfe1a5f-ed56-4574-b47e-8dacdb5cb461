2025-06-23 14:03:49,695 - inserters.v3.roles.entity_relationship_inserter - INFO - Starting process_mongo_entity_relationships_to_workflow_temp
2025-06-23 14:03:49,698 - inserters.v3.roles.entity_relationship_inserter - INFO - Found 1 entity relationships with status 'draft' in MongoDB
2025-06-23 14:03:49,706 - inserters.v3.roles.entity_relationship_inserter - INFO - Entity relationship 1 already exists in workflow_temp
2025-06-23 14:03:49,706 - inserters.v3.roles.entity_relationship_inserter - INFO - Completed process_mongo_entity_relationships_to_workflow_temp: 0 successful, 0 failed
2025-06-23 14:09:00,811 - inserters.v3.roles.entity_relationship_inserter - INFO - Starting process_mongo_entity_relationships_to_workflow_temp
2025-06-23 14:09:00,813 - inserters.v3.roles.entity_relationship_inserter - INFO - Found 1 entity relationships with status 'draft' in MongoDB
2025-06-23 14:09:00,820 - inserters.v3.roles.entity_relationship_inserter - INFO - Entity relationship 1 already exists in workflow_temp
2025-06-23 14:09:00,820 - inserters.v3.roles.entity_relationship_inserter - INFO - Completed process_mongo_entity_relationships_to_workflow_temp: 0 successful, 0 failed
2025-06-23 14:32:33,317 - inserters.v3.roles.entity_relationship_inserter - INFO - Starting process_mongo_entity_relationships_to_workflow_temp
2025-06-23 14:32:33,320 - inserters.v3.roles.entity_relationship_inserter - INFO - Found 1 entity relationships with status 'draft' in MongoDB
2025-06-23 14:32:33,327 - inserters.v3.roles.entity_relationship_inserter - INFO - Entity relationship 1 already exists in workflow_temp
2025-06-23 14:32:33,327 - inserters.v3.roles.entity_relationship_inserter - INFO - Completed process_mongo_entity_relationships_to_workflow_temp: 0 successful, 0 failed
2025-06-23 14:38:52,694 - inserters.v3.roles.entity_relationship_inserter - INFO - Starting process_mongo_entity_relationships_to_workflow_temp
2025-06-23 14:38:52,697 - inserters.v3.roles.entity_relationship_inserter - INFO - Found 1 entity relationships with status 'draft' in MongoDB
2025-06-23 14:38:52,705 - inserters.v3.roles.entity_relationship_inserter - INFO - Entity relationship 1 already exists in workflow_temp
2025-06-23 14:38:52,705 - inserters.v3.roles.entity_relationship_inserter - INFO - Completed process_mongo_entity_relationships_to_workflow_temp: 0 successful, 0 failed
2025-06-24 04:45:02,308 - inserters.v3.roles.entity_relationship_inserter - INFO - Starting process_mongo_entity_relationships_to_workflow_temp
2025-06-24 04:45:02,311 - inserters.v3.roles.entity_relationship_inserter - INFO - Found 1 entity relationships with status 'draft' in MongoDB
2025-06-24 04:45:02,319 - inserters.v3.roles.entity_relationship_inserter - INFO - Entity relationship 1 already exists in workflow_temp
2025-06-24 04:45:02,319 - inserters.v3.roles.entity_relationship_inserter - INFO - Completed process_mongo_entity_relationships_to_workflow_temp: 0 successful, 0 failed
2025-06-24 11:42:23,258 - inserters.v3.roles.entity_relationship_inserter - INFO - Starting process_mongo_entity_relationships_to_workflow_runtime
2025-06-24 11:42:23,262 - inserters.v3.roles.entity_relationship_inserter - INFO - Found 0 deployed_to_temp + 1 draft = 1 total entity relationships in MongoDB
2025-06-24 11:42:23,269 - inserters.v3.roles.entity_relationship_inserter - INFO - Starting insert_entity_relationship_to_workflow_runtime for relationship: 1
2025-06-24 11:42:23,269 - inserters.v3.roles.entity_relationship_inserter - INFO - Field validation passed: 1/1 required fields, 15/15 optional fields
2025-06-24 11:42:23,284 - inserters.v3.roles.entity_relationship_inserter - INFO - Generated next relationship ID: 1 -> 13
2025-06-24 11:42:23,284 - inserters.v3.roles.entity_relationship_inserter - INFO - Incremented relationship ID from 1 to 13
2025-06-24 11:42:23,284 - inserters.v3.roles.entity_relationship_inserter - INFO - Additional MongoDB fields preserved: {'original_created_at': '2025-06-20T07:53:58.261487', 'original_updated_at': '2025-06-20T07:53:58.261492', 'original_created_by': 'Tarun', 'original_updated_by': 'Tarun', 'original_relationship_id': 1}
2025-06-24 11:42:23,287 - inserters.v3.roles.entity_relationship_inserter - INFO - Successfully inserted entity relationship to workflow_runtime with ID: 13
2025-06-24 11:42:23,289 - inserters.v3.roles.entity_relationship_inserter - INFO - Updated MongoDB status to deployed_to_production for relationship: 13
2025-06-24 11:42:23,290 - inserters.v3.roles.entity_relationship_inserter - INFO - Completed process_mongo_entity_relationships_to_workflow_runtime: 1 successful, 0 failed
