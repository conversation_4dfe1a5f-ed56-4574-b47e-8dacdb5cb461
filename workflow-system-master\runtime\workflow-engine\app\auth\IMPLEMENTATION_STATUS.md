# RBAC Implementation Status

This document provides a summary of the current status of the RBAC implementation for the Workflow System.

## Completed

- ✅ **Database Schema Design**: Created SQL scripts for database schema updates and data migration
- ✅ **Authentication Service**: Implemented JWT token generation, validation, and session management
- ✅ **Authentication Middleware**: Implemented token extraction and security context population
- ✅ **Authentication API Endpoints**: Implemented login, refresh, logout, and user management endpoints
- ✅ **Permission Service**: Implemented permission evaluation based on roles, organizational hierarchy, and context
- ✅ **Permission API Endpoints**: Implemented permission check endpoints
- ✅ **Testing**: Created test scripts for authentication and permission services and verified the implementation
  - ✅ Successfully tested ownership-based permission conditions
  - ⏳ Time-based, attribute-based, and custom condition evaluations are not yet implemented
- ✅ **Integration Example**: Created an example of how to integrate the authentication and permission services
- ✅ **Documentation**: Created documentation for the authentication and permission services
- ✅ **Integration Planning**: Created a detailed plan for integrating the authentication and permission services into the workflow engine
- ✅ **Workflow Engine Integration**: Updated parameter resolver and execution engine to support security context
- ✅ **API Integration**: Updated API endpoints with authentication and permission checks
  - ✅ Added authentication to global_objectives.py
  - ✅ Verified authentication for workflow API endpoints
  - ✅ Created documentation for workflow APIs authentication
- ✅ **Migration Scripts**: Created scripts to apply database changes to the production environment
- ✅ **Entity Service Enhancements**: Implemented user/role as entities and relationship management
  - ✅ Defined User and Role entity structures in YAML
  - ✅ Created enhanced agent stack with user-specific permissions
  - ✅ Developed validation script for RBAC in YAML files
  - ✅ Created example User Management workflow
  - ✅ Integrated RBAC validation into the existing validation engine
  - ✅ Created scripts to update existing YAML files for RBAC compliance
  - ✅ Developed parser enhancement script for User and Role entities
- ✅ **RBAC Validation**: Implemented validation for RBAC requirements in YAML files
  - ✅ Created rbac_validator.py with validation functions
  - ✅ Created validate_rbac.py script for standalone validation
  - ✅ Created validate_rbac_yaml.sh shell script for easy validation
  - ✅ Integrated RBAC validation into the main pipeline runner flow
- ✅ **System Functions for User Management**: Implemented approach for user management operations using existing generic functions
  - ✅ Created rbac_system_functions.py with examples of how to use existing functions
  - ✅ Created RBAC_USAGE_README.md with instructions on how to use the functions
  - ✅ Created update_existing_yamls.sh script to update existing YAML files

- ✅ **End-to-End Testing**: Created and implemented test scripts for the complete implementation
  - ✅ Created and tested scripts for authentication flow (test_auth_flow.py)
  - ✅ Created and tested scripts for workflow APIs with authentication (test_workflow_apis_with_auth.py)
  - ✅ Created and tested scripts for RBAC validation in the pipeline runner flow (test_rbac_validation.py)
- ✅ **Workflow Engine Updates**: Enhanced the workflow engine to check permissions based on both roles and specific users
  - ✅ Created enhance_parser.py script to generate code updates
  - ✅ Applied code updates to workflow_engine.py
  - ✅ Created workflow_engine.py with permission checking functions
  - ✅ Updated system_functions.py with RBAC-specific functions
  - ✅ Updated db_inserter.py and insert_generator.py with RBAC-specific functions
- ✅ **Dependent Dropdown Handling**: Fixed issues with dependent dropdowns
  - ✅ Created leave_sub_types table and populated it with sample data for testing
  - ✅ Fixed insert_generator.py to dynamically handle slot_id values based on source_description

## In Progress

- 🔄 **Permission Condition Evaluation**: Implementing condition evaluations in the permission service:
  - Time-based condition evaluation
  - Attribute-based condition evaluation
  - Custom condition evaluation
- 🔄 **Entity Attribute Mapping**: Ensuring correct mapping between entity attributes in YAML and database
  - Fixed insert_generator.py to dynamically map slot_id values based on source_description
  - Need to test with various entity types and attribute combinations

## Recent Fixes

### Entity Attribute Mapping Issue

We identified and fixed an issue with the entity attribute mapping in the insert_generator.py file. The issue was that the slot_id values in the database were using the User entity attributes (at001, at002, etc.) instead of the correct entity attributes (e.g., LeaveApplication entity attributes: at016, at017, etc.). This caused a mismatch between what was expected and what was actually in the database.

**Problem:**
- When executing the workflow APIs test (test_workflow_apis_with_auth.py) with GO002 (Leave Application Workflow), it selected LO004 (Apply for Leave), but the input fields didn't match what was expected.
- The API was returning input fields like "Username", "Email", "First Name" instead of "Leave ID", "Employee ID", "Start Date", etc.
- This was because the slot_id values in the database were using the User entity attributes instead of the LeaveApplication entity attributes.

**Solution:**
1. Created a leave_sub_types table and populated it with sample data to support the dependent dropdown for leave_sub_type.
2. Modified the insert_generator.py file to dynamically handle slot_id values based on source_description:
   - Added code to query the database for the correct entity and attribute based on source_description
   - Updated the slot_id to use the correct entity and attribute IDs
   - Added similar logic for nested functions to handle output_to values correctly

**Implementation:**
- Created a script (fix_insert_generator.py) to modify the insert_generator.py file
- The script adds code to dynamically map slot_id values based on source_description
- It also adds code to dynamically map output_to values for nested functions

## Next Steps

1. **Test the Entity Attribute Mapping Fix**: Verify that the fix for the entity attribute mapping issue works correctly
   ```bash
   cd runtime/workflow-engine
   python3 test_workflow_apis_with_auth.py
   ```
   - Select GO002 (Leave Application Workflow)
   - Verify that the input fields match what's expected (Leave ID, Employee ID, Start Date, etc.)
   - Verify that the dependent dropdown for leave_sub_type works correctly

2. **Test User-Specific Permissions**: Verify that user-specific permissions are enforced correctly
   - Create test users with different roles and permissions
   - Verify that users can only access the workflows they have permission to access
   - Verify that user-specific permissions override role-based permissions

3. **Test Role-Based Permissions**: Verify that role-based permissions are enforced correctly
   - Create test roles with different permissions
   - Assign users to these roles
   - Verify that users can only access the workflows their roles have permission to access

4. **Test Using User as an Entity**: Verify that the User entity can be used in workflows
   - Create a workflow that uses the User entity
   - Verify that the User entity attributes are correctly mapped
   - Verify that operations on the User entity (create, update, delete) work correctly

5. **Implement Advanced Permission Conditions**: Complete the implementation of advanced permission conditions
   - Time-based condition evaluation
   - Attribute-based condition evaluation
   - Custom condition evaluation

6. **Extend Documentation**: Create additional documentation for developers on how to use the RBAC functionality
   - Create examples of common RBAC patterns
   - Document best practices for role and permission design
   - Create tutorials for implementing RBAC in new workflows

7. **Monitor Production Deployment**: Monitor the RBAC implementation in production to ensure it's working correctly
   - Watch for any permission-related issues
   - Ensure user-specific permissions are being enforced correctly
   - Verify that YAML validation is catching RBAC-related issues

## Implementation Details

### Authentication Service

The authentication service provides:

- User authentication with username and password
- JWT token generation and validation
- Session management
- User creation and management

### Permission Service

The permission service provides:

- Permission checking based on roles, organizational hierarchy, and context
- Permission evaluation for different resource types
- Contextual permission evaluation

### Integration

The authentication and permission services can be integrated into the workflow engine by:

1. Adding the authentication middleware to the FastAPI application
2. Including the authentication and permission routes
3. Using the authentication and permission dependencies in the routes

### Database Schema

The authentication and permission services require the following database tables:

- `users`: User information
- `user_roles`: User role assignments
- `user_oauth_tokens`: OAuth tokens
- `user_sessions`: User sessions
- `roles`: Role definitions
- `permission_contexts`: Permission contexts
- `permission_types`: Permission types
- `role_permissions`: Role permission assignments
- `organizational_units`: Organizational units
- `user_organizations`: User organizational unit assignments
- `tenants`: Tenant information

## User & Role Entity Integration Plan

### Overview

To integrate User and Role as entities in the workflow system, we need to:

1. Define User and Role as entities in the YAML structure
2. Ensure the Python parser correctly processes these entities
3. Update the database schema to store User and Role entities
4. Implement workflow functionality to update user attributes
5. Create workflow examples that demonstrate user and role management

### 1. Entity Definitions

The User and Role entity structures must exactly match the current RBAC implementation to avoid bugs. Based on the existing authentication service, we'll define:

#### User Entity

```yaml
- id: "e003"
  name: "User"
  type: "Master"
  version: "1.0"
  status: "Active"
  attributes_metadata:
    attribute_prefix: "at"
    attribute_map:
      "at101": "user_id"
      "at102": "username"
      "at103": "email"
      "at104": "first_name"
      "at105": "last_name"
      "at106": "status"
      "at107": "password_hash"
      "at108": "disabled"
    required_attributes:
      - "at101"
      - "at102"
      - "at103"
      - "at106"
  attributes:
    - id: "at101"
      name: "user_id"
      display_name: "User ID"
      datatype: "String"
      required: true
      version: "1.0"
      status: "Deployed"
    # ... other attributes
```

#### Role Entity

```yaml
- id: "e004"
  name: "Role"
  type: "Master"
  version: "1.0"
  status: "Active"
  attributes_metadata:
    attribute_prefix: "at"
    attribute_map:
      "at201": "role_id"
      "at202": "name"
      "at203": "description"
      "at204": "inherits_from"
      "at205": "tenant_id"
    required_attributes:
      - "at201"
      - "at202"
  attributes:
    - id: "at201"
      name: "role_id"
      display_name: "Role ID"
      datatype: "String"
      required: true
      version: "1.0"
      status: "Deployed"
    # ... other attributes
```

### 2. Enhanced Agent Stack for User-Specific Permissions

The agent stack has been enhanced to support both role-based and user-specific permissions:

```yaml
agent_stack:
  agents:
    - role: "r001"  # Administrator role
      rights: ["Execute", "Read"]
      users: []  # Empty means all users with this role have these rights
    - role: "r002"  # UserManager role
      rights: ["Read"]
      users: ["<EMAIL>"]  # Only this specific user manager can read
    - role: ""  # No role specified, just specific users
      rights: ["Read"]
      users: ["<EMAIL>", "<EMAIL>"]  # These specific users can read regardless of role
```

Key features of the enhanced agent stack:

1. **Role-Based Access**: When `users` is empty, all users in that role have the specified rights
2. **User-Specific Access**: When specific users are listed, only those users have the specified rights
3. **Mixed Permissions**: Different users/roles can have different rights on the same LO
4. **Email-Based Identification**: Users are identified by their email addresses at design time
5. **Permission Hierarchy**: The system checks both role-based and user-specific permissions

### 3. RBAC Validation

The RBAC validation ensures that workflow YAML files comply with the RBAC requirements:

1. **User and Role Entity Definitions**: Validates that User and Role entities have all mandatory attributes.
2. **Agent Stack**: Validates that the agent stack is properly defined with role and user-specific permissions.
3. **Email-Based User Identification**: Validates that user identifiers in the agent stack are valid email addresses.
4. **Workflow Source Constraints**: Validates that only one Local Objective has `workflow_source: "origin"`.

The validation is performed by the `validate_rbac.py` script, which can be run directly or through the `validate_rbac_yaml.sh` shell script.

### 4. Workflow Engine Permission Checking

The workflow engine has been updated to check permissions based on both roles and specific users:

1. When a workflow instance is initiated, the engine checks who has execute access to the LO.
2. If specific users are defined in the agent stack, only those users will see the task in their pending transactions.
3. If no specific users are defined (empty users array), all users with the specified role will see the task.
4. If multiple users/roles have execute rights, the task appears in all of their pending transactions.
5. Once any authorized user executes the task, it is removed from all pending transactions.
6. The system matches users based on their email ID, which is specified at design time.

### 5. System Functions

Instead of creating specific functions for user management operations, we use the existing generic functions (`create`, `update`, `fetch`, etc.) that are already present in the system_functions.py file. The `rbac_system_functions.py` file provides examples of how to use these generic functions for user management operations:

#### Creating a User

```python
from app.services.system_functions import create

# Prepare the data for the user entity
user_data = {
    "username": "johndoe",
    "email": "<EMAIL>",
    "status": "active"
}

# Add optional fields if provided
if first_name:
    user_data["first_name"] = first_name
if last_name:
    user_data["last_name"] = last_name

# Use the existing 'create' function to create the user
# The entity_id "e003" corresponds to the User entity
user = create(db, "e003", user_data)
```

#### Assigning a Role to a User

```python
from app.services.system_functions import create

# Prepare the data for the user-role relationship
relationship_data = {
    "user_id": user_id,
    "role_id": role_id
}

# Use the existing 'create' function to create the relationship
relationship = create(db, "user_roles", relationship_data)
```

For more examples, see the `rbac_system_functions.py` file and the `RBAC_USAGE_README.md` file.

## Conclusion

The RBAC implementation has made significant progress. The foundation for treating Users and Roles as entities within the workflow system has been successfully implemented. The validation engine has been updated to include RBAC validation, and scripts have been created to update existing YAML files to comply with RBAC requirements.

We've also fixed issues with dependent dropdowns and entity attribute mapping, which were causing problems with the workflow APIs test. The insert_generator.py file has been updated to dynamically handle slot_id values based on source_description, which should resolve the issues with input fields not matching what's expected.

The next steps are to test the fixes we've made, verify that user-specific and role-based permissions are enforced correctly, test using the User entity in workflows, implement advanced permission conditions, extend the documentation, and monitor the production deployment.
