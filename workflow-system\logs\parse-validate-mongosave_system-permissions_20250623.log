{"timestamp": "2025-06-23T05:08:17.295354", "endpoint": "parse-validate-mongosave/system-permissions", "input": {"natural_language": "Tenant: Acme Corp\n\npermission_id | permission_name | permission_type | resource_identifier | actions | description | scope | natural_language | version | status\n\nPERM_ENTITY_LEAVEAPP | LeaveApplication Entity | entity | LeaveApplication | [\"create\", \"read\", \"update\", \"delete\"] | Access to LeaveApplication entity | tenant_records | Permission to access leave application entity | 1 | active\n\nPERM_ATTR_LEAVE_ID | LeaveApplication leaveId Attribute | attribute | LeaveApplication.leaveId | [\"read\"] | Access to LeaveApplication leaveId attribute | own_records | Permission to read leave ID attribute | 1 | active\n\nPERM_ATTR_EMPLOYEE_ID | LeaveApplication employeeId Attribute | attribute | LeaveApplication.employeeId | [\"create\", \"read\", \"update\"] | Access to LeaveApplication employeeId attribute | own_records | Permission to access employee ID attribute | 1 | active\n\nPERM_ATTR_START_DATE | LeaveApplication startDate Attribute | attribute | LeaveApplication.startDate | [\"create\", \"read\", \"update\"] | Access to LeaveApplication startDate attribute | own_records | Permission to access start date attribute | 1 | active", "tenant_id": "T_ACME_CORP", "edit_draft": false, "edit_temp": false, "edit_production": false}, "output": {"success": true, "permission_results": [{"success": false, "errors": ["Entity with entity_id E_LeaveApplication_1750655297 does not exist"], "parsed_data": {"permission_id": "PERM_ENTITY_LEAVEAPP", "permission_name": "LeaveApplication Entity", "permission_type": "entity", "resource_identifier": "LeaveApplication", "actions": ["create", "read", "update", "delete"], "scope": "tenant_records", "entity_id": "E_LeaveApplication_1750655297", "attribute_id": "", "go_id": "", "lo_id": "", "tenant_id": "T_ACME_CORP", "description": "Access to LeaveApplication entity", "natural_language": "Permission to access leave application entity", "version": 1, "status": "active", "created_at": "2025-06-23T05:08:17.134395", "updated_at": "2025-06-23T05:08:17.134395", "created_by": "Tarun", "updated_by": "Tarun", "permission_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Entity with entity_id E_LeaveApplication_1750655297 does not exist", "Attribute with attribute_id A_E_LeaveApplication_1750655297_leaveId_1750655297 does not exist"], "parsed_data": {"permission_id": "PERM_ATTR_LEAVE_ID", "permission_name": "LeaveApplication leaveId Attribute", "permission_type": "attribute", "resource_identifier": "LeaveApplication.leaveId", "actions": ["read"], "scope": "own_records", "entity_id": "E_LeaveApplication_1750655297", "attribute_id": "A_E_LeaveApplication_1750655297_leaveId_1750655297", "go_id": "", "lo_id": "", "tenant_id": "T_ACME_CORP", "description": "Access to LeaveApplication leaveId attribute", "natural_language": "Permission to read leave ID attribute", "version": 1, "status": "active", "created_at": "2025-06-23T05:08:17.160712", "updated_at": "2025-06-23T05:08:17.160712", "created_by": "Tarun", "updated_by": "Tarun", "permission_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Entity with entity_id E_LeaveApplication_1750655297 does not exist", "Attribute with attribute_id A_E_LeaveApplication_1750655297_employeeId_1750655297 does not exist"], "parsed_data": {"permission_id": "PERM_ATTR_EMPLOYEE_ID", "permission_name": "LeaveApplication employeeId Attribute", "permission_type": "attribute", "resource_identifier": "LeaveApplication.employeeId", "actions": ["create", "read", "update"], "scope": "own_records", "entity_id": "E_LeaveApplication_1750655297", "attribute_id": "A_E_LeaveApplication_1750655297_employeeId_1750655297", "go_id": "", "lo_id": "", "tenant_id": "T_ACME_CORP", "description": "Access to LeaveApplication employeeId attribute", "natural_language": "Permission to access employee ID attribute", "version": 1, "status": "active", "created_at": "2025-06-23T05:08:17.185014", "updated_at": "2025-06-23T05:08:17.185014", "created_by": "Tarun", "updated_by": "Tarun", "permission_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Entity with entity_id E_LeaveApplication_1750655297 does not exist", "Attribute with attribute_id A_E_LeaveApplication_1750655297_startDate_1750655297 does not exist"], "parsed_data": {"permission_id": "PERM_ATTR_START_DATE", "permission_name": "LeaveApplication startDate Attribute", "permission_type": "attribute", "resource_identifier": "LeaveApplication.startDate", "actions": ["create", "read", "update"], "scope": "own_records", "entity_id": "E_LeaveApplication_1750655297", "attribute_id": "A_E_LeaveApplication_1750655297_startDate_1750655297", "go_id": "", "lo_id": "", "tenant_id": "T_ACME_CORP", "description": "Access to LeaveApplication startDate attribute", "natural_language": "Permission to access start date attribute", "version": 1, "status": "active", "created_at": "2025-06-23T05:08:17.209349", "updated_at": "2025-06-23T05:08:17.209349", "created_by": "Tarun", "updated_by": "Tarun", "permission_status": "new", "changes_detected": []}}], "operation": "parse_validate_mongosave", "total_permissions": 4}, "status": "success"}
{"timestamp": "2025-06-23T05:26:03.120160", "endpoint": "parse-validate-mongosave/system-permissions", "input": {"natural_language": "Tenant: Acme Corp\n\npermission_id | permission_name | permission_type | resource_identifier | actions | description | scope | natural_language | version | status\n\nPERM_ENTITY_LEAVEAPP | LeaveApplication Entity | entity | LeaveApplication | [\"create\", \"read\", \"update\", \"delete\"] | Access to LeaveApplication entity | tenant_records | Permission to access leave application entity | 1 | active\n\nPERM_ATTR_LEAVE_ID | LeaveApplication leaveId Attribute | attribute | LeaveApplication.leaveId | [\"read\"] | Access to LeaveApplication leaveId attribute | own_records | Permission to read leave ID attribute | 1 | active\n\nPERM_ATTR_EMPLOYEE_ID | LeaveApplication employeeId Attribute | attribute | LeaveApplication.employeeId | [\"create\", \"read\", \"update\"] | Access to LeaveApplication employeeId attribute | own_records | Permission to access employee ID attribute | 1 | active\n\nPERM_ATTR_START_DATE | LeaveApplication startDate Attribute | attribute | LeaveApplication.startDate | [\"create\", \"read\", \"update\"] | Access to LeaveApplication startDate attribute | own_records | Permission to access start date attribute | 1 | active", "tenant_id": "T_ACME_CORP", "edit_draft": false, "edit_temp": false, "edit_production": false}, "output": {"success": true, "permission_results": [{"success": false, "errors": ["Entity with entity_id E_LeaveApplication_1750656362 does not exist"], "parsed_data": {"permission_id": "PERM_ENTITY_LEAVEAPP", "permission_name": "LeaveApplication Entity", "permission_type": "entity", "resource_identifier": "LeaveApplication", "actions": ["create", "read", "update", "delete"], "scope": "tenant_records", "entity_id": "E_LeaveApplication_1750656362", "attribute_id": "", "go_id": "", "lo_id": "", "tenant_id": "T_ACME_CORP", "description": "Access to LeaveApplication entity", "natural_language": "Permission to access leave application entity", "version": 1, "status": "active", "created_at": "2025-06-23T05:26:02.953240", "updated_at": "2025-06-23T05:26:02.953240", "created_by": "Tarun", "updated_by": "Tarun", "permission_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Entity with entity_id E_LeaveApplication_1750656362 does not exist", "Attribute with attribute_id A_E_LeaveApplication_1750656362_leaveId_1750656362 does not exist"], "parsed_data": {"permission_id": "PERM_ATTR_LEAVE_ID", "permission_name": "LeaveApplication leaveId Attribute", "permission_type": "attribute", "resource_identifier": "LeaveApplication.leaveId", "actions": ["read"], "scope": "own_records", "entity_id": "E_LeaveApplication_1750656362", "attribute_id": "A_E_LeaveApplication_1750656362_leaveId_1750656362", "go_id": "", "lo_id": "", "tenant_id": "T_ACME_CORP", "description": "Access to LeaveApplication leaveId attribute", "natural_language": "Permission to read leave ID attribute", "version": 1, "status": "active", "created_at": "2025-06-23T05:26:02.979150", "updated_at": "2025-06-23T05:26:02.979150", "created_by": "Tarun", "updated_by": "Tarun", "permission_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Entity with entity_id E_LeaveApplication_1750656362 does not exist", "Attribute with attribute_id A_E_LeaveApplication_1750656362_employeeId_1750656362 does not exist"], "parsed_data": {"permission_id": "PERM_ATTR_EMPLOYEE_ID", "permission_name": "LeaveApplication employeeId Attribute", "permission_type": "attribute", "resource_identifier": "LeaveApplication.employeeId", "actions": ["create", "read", "update"], "scope": "own_records", "entity_id": "E_LeaveApplication_1750656362", "attribute_id": "A_E_LeaveApplication_1750656362_employeeId_1750656362", "go_id": "", "lo_id": "", "tenant_id": "T_ACME_CORP", "description": "Access to LeaveApplication employeeId attribute", "natural_language": "Permission to access employee ID attribute", "version": 1, "status": "active", "created_at": "2025-06-23T05:26:03.003630", "updated_at": "2025-06-23T05:26:03.003630", "created_by": "Tarun", "updated_by": "Tarun", "permission_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Entity with entity_id E_LeaveApplication_1750656363 does not exist", "Attribute with attribute_id A_E_LeaveApplication_1750656363_startDate_1750656363 does not exist"], "parsed_data": {"permission_id": "PERM_ATTR_START_DATE", "permission_name": "LeaveApplication startDate Attribute", "permission_type": "attribute", "resource_identifier": "LeaveApplication.startDate", "actions": ["create", "read", "update"], "scope": "own_records", "entity_id": "E_LeaveApplication_1750656363", "attribute_id": "A_E_LeaveApplication_1750656363_startDate_1750656363", "go_id": "", "lo_id": "", "tenant_id": "T_ACME_CORP", "description": "Access to LeaveApplication startDate attribute", "natural_language": "Permission to access start date attribute", "version": 1, "status": "active", "created_at": "2025-06-23T05:26:03.031636", "updated_at": "2025-06-23T05:26:03.031636", "created_by": "Tarun", "updated_by": "Tarun", "permission_status": "new", "changes_detected": []}}], "operation": "parse_validate_mongosave", "total_permissions": 4}, "status": "success"}
{"timestamp": "2025-06-23T06:04:18.678509", "endpoint": "parse-validate-mongosave/system-permissions", "input": {"natural_language": "Tenant: Acme Corp Test\n\npermission_id | permission_name | permission_type | resource_identifier | actions | description | scope | natural_language | version | status\n\nPERM_ENTITY_LEAVEAPP | LeaveApplication Entity | entity | LeaveApplication | [\"create\", \"read\", \"update\", \"delete\"] | Access to LeaveApplication entity | tenant_records | Permission to access leave application entity | 1 | active\n\nPERM_ATTR_LEAVE_ID | LeaveApplication leaveId Attribute | attribute | LeaveApplication.leaveId | [\"read\"] | Access to LeaveApplication leaveId attribute | own_records | Permission to read leave ID attribute | 1 | active\n\nPERM_ATTR_STATUS | LeaveApplication status Attribute | attribute | LeaveApplication.status | [\"read\", \"update\"] | Access to LeaveApplication status attribute | department_records | Permission to access status attribute | 1 | active", "tenant_id": "T1007", "edit_draft": false, "edit_temp": false, "edit_production": false}, "output": {"success": true, "permission_results": [{"success": false, "errors": ["Entity with entity_id E_LeaveApplication_1750658658 does not exist"], "parsed_data": {"permission_id": "PERM_ENTITY_LEAVEAPP", "permission_name": "LeaveApplication Entity", "permission_type": "entity", "resource_identifier": "LeaveApplication", "actions": ["create", "read", "update", "delete"], "scope": "tenant_records", "entity_id": "E_LeaveApplication_1750658658", "attribute_id": "", "go_id": "", "lo_id": "", "tenant_id": "T1007", "description": "Access to LeaveApplication entity", "natural_language": "Permission to access leave application entity", "version": 1, "status": "active", "created_at": "2025-06-23T06:04:18.562862", "updated_at": "2025-06-23T06:04:18.562862", "created_by": "Tarun", "updated_by": "Tarun", "permission_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Entity with entity_id E_LeaveApplication_1750658658 does not exist", "Attribute with attribute_id A_E_LeaveApplication_1750658658_leaveId_1750658658 does not exist"], "parsed_data": {"permission_id": "PERM_ATTR_LEAVE_ID", "permission_name": "LeaveApplication leaveId Attribute", "permission_type": "attribute", "resource_identifier": "LeaveApplication.leaveId", "actions": ["read"], "scope": "own_records", "entity_id": "E_LeaveApplication_1750658658", "attribute_id": "A_E_LeaveApplication_1750658658_leaveId_1750658658", "go_id": "", "lo_id": "", "tenant_id": "T1007", "description": "Access to LeaveApplication leaveId attribute", "natural_language": "Permission to read leave ID attribute", "version": 1, "status": "active", "created_at": "2025-06-23T06:04:18.592109", "updated_at": "2025-06-23T06:04:18.592109", "created_by": "Tarun", "updated_by": "Tarun", "permission_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Entity with entity_id E_LeaveApplication_1750658658 does not exist", "Attribute with attribute_id A_E_LeaveApplication_1750658658_status_1750658658 does not exist"], "parsed_data": {"permission_id": "PERM_ATTR_STATUS", "permission_name": "LeaveApplication status Attribute", "permission_type": "attribute", "resource_identifier": "LeaveApplication.status", "actions": ["read", "update"], "scope": "department_records", "entity_id": "E_LeaveApplication_1750658658", "attribute_id": "A_E_LeaveApplication_1750658658_status_1750658658", "go_id": "", "lo_id": "", "tenant_id": "T1007", "description": "Access to LeaveApplication status attribute", "natural_language": "Permission to access status attribute", "version": 1, "status": "active", "created_at": "2025-06-23T06:04:18.617292", "updated_at": "2025-06-23T06:04:18.617292", "created_by": "Tarun", "updated_by": "Tarun", "permission_status": "new", "changes_detected": []}}], "operation": "parse_validate_mongosave", "total_permissions": 3}, "status": "success"}
{"timestamp": "2025-06-23T06:06:40.523932", "endpoint": "parse-validate-mongosave/system-permissions", "input": {"natural_language": "Tenant: Test\n\npermission_id | permission_name | permission_type | resource_identifier | actions | description | scope | natural_language | version | status\n\nPERM_TEST_GENERAL | General Test Permission | general | TestResource | [\"read\"] | General test permission | tenant_records | Permission for testing purposes | 1 | active", "tenant_id": null, "edit_draft": false, "edit_temp": false, "edit_production": false}, "output": {"success": true, "permission_results": [{"success": false, "errors": ["Entity with entity_id E_TestResource_1750658800 does not exist"], "parsed_data": {"permission_id": "PERM_TEST_GENERAL", "permission_name": "General Test Permission", "permission_type": "system", "resource_identifier": "TestResource", "actions": ["read"], "scope": "tenant_records", "entity_id": "E_TestResource_1750658800", "attribute_id": "", "go_id": "", "lo_id": "", "tenant_id": null, "description": "General test permission", "natural_language": "Permission for testing purposes", "version": 1, "status": "active", "created_at": "2025-06-23T06:06:40.506601", "updated_at": "2025-06-23T06:06:40.506601", "created_by": "Tarun", "updated_by": "Tarun", "permission_status": "new", "changes_detected": []}}], "operation": "parse_validate_mongosave", "total_permissions": 1}, "status": "success"}
{"timestamp": "2025-06-23T06:41:34.261243", "endpoint": "parse-validate-mongosave/system-permissions", "input": {"natural_language": "Tenant: Acme Corp Test\n\npermission_id | permission_name | permission_type | resource_identifier | actions | description | scope | natural_language | version | status\n\nPERM_ENTITY_EMPLOYEE | Employee Entity | entity | Employee | [\"create\", \"read\", \"update\", \"delete\"] | Access to Employee entity | tenant_records | Permission to access employee entity | 1 | active\n\nPERM_ATTR_EMP_STATUS | Employee employmentStatus Attribute | attribute | Employee.employmentStatus | [\"read\", \"update\"] | Access to Employee employmentStatus attribute | department_records | Permission to access employment status attribute | 1 | active\n\nPERM_GENERAL_READ | General Read Permission | general | SystemResource | [\"read\"] | General read access to system resources | tenant_records | Permission for general read operations | 1 | active", "tenant_id": "T1007", "edit_draft": false, "edit_temp": false, "edit_production": false}, "output": {"success": true, "permission_results": [{"success": false, "errors": ["Entity with entity_id E_Employee_1750660894 does not exist"], "parsed_data": {"permission_id": "PERM_ENTITY_EMPLOYEE", "permission_name": "Employee Entity", "permission_type": "entity", "resource_identifier": "Employee", "actions": ["create", "read", "update", "delete"], "scope": "tenant_records", "entity_id": "E_Employee_1750660894", "attribute_id": "", "go_id": "", "lo_id": "", "tenant_id": "T1007", "description": "Access to Employee entity", "natural_language": "Permission to access employee entity", "version": 1, "status": "active", "created_at": "2025-06-23T06:41:34.153432", "updated_at": "2025-06-23T06:41:34.153432", "created_by": "Tarun", "updated_by": "Tarun", "permission_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Entity with entity_id E_Employee_1750660894 does not exist", "Attribute with attribute_id A_E_Employee_1750660894_employmentStatus_1750660894 does not exist"], "parsed_data": {"permission_id": "PERM_ATTR_EMP_STATUS", "permission_name": "Employee employmentStatus Attribute", "permission_type": "attribute", "resource_identifier": "Employee.employmentStatus", "actions": ["read", "update"], "scope": "department_records", "entity_id": "E_Employee_1750660894", "attribute_id": "A_E_Employee_1750660894_employmentStatus_1750660894", "go_id": "", "lo_id": "", "tenant_id": "T1007", "description": "Access to Employee employmentStatus attribute", "natural_language": "Permission to access employment status attribute", "version": 1, "status": "active", "created_at": "2025-06-23T06:41:34.180990", "updated_at": "2025-06-23T06:41:34.180990", "created_by": "Tarun", "updated_by": "Tarun", "permission_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Entity with entity_id E_SystemResource_1750660894 does not exist"], "parsed_data": {"permission_id": "PERM_GENERAL_READ", "permission_name": "General Read Permission", "permission_type": "system", "resource_identifier": "SystemResource", "actions": ["read"], "scope": "tenant_records", "entity_id": "E_SystemResource_1750660894", "attribute_id": "", "go_id": "", "lo_id": "", "tenant_id": "T1007", "description": "General read access to system resources", "natural_language": "Permission for general read operations", "version": 1, "status": "active", "created_at": "2025-06-23T06:41:34.198813", "updated_at": "2025-06-23T06:41:34.198813", "created_by": "Tarun", "updated_by": "Tarun", "permission_status": "new", "changes_detected": []}}], "operation": "parse_validate_mongosave", "total_permissions": 3}, "status": "success"}
{"timestamp": "2025-06-23T06:42:13.930689", "endpoint": "parse-validate-mongosave/system-permissions", "input": {"natural_language": "Tenant: Test\n\npermission_id | permission_name | permission_type | resource_identifier | actions | description | scope | natural_language | version | status\n\nPERM_GENERAL_READ | General Read Permission | general | SystemResource | [\"read\"] | General read access to system resources | tenant_records | Permission for general read operations | 1 | active", "tenant_id": null, "edit_draft": false, "edit_temp": false, "edit_production": false}, "output": {"success": true, "permission_results": [{"success": false, "errors": ["Entity with entity_id E_SystemResource_1750660933 does not exist"], "parsed_data": {"permission_id": "PERM_GENERAL_READ", "permission_name": "General Read Permission", "permission_type": "system", "resource_identifier": "SystemResource", "actions": ["read"], "scope": "tenant_records", "entity_id": "E_SystemResource_1750660933", "attribute_id": "", "go_id": "", "lo_id": "", "tenant_id": null, "description": "General read access to system resources", "natural_language": "Permission for general read operations", "version": 1, "status": "active", "created_at": "2025-06-23T06:42:13.914370", "updated_at": "2025-06-23T06:42:13.914370", "created_by": "Tarun", "updated_by": "Tarun", "permission_status": "new", "changes_detected": []}}], "operation": "parse_validate_mongosave", "total_permissions": 1}, "status": "success"}
{"timestamp": "2025-06-23T07:15:42.620040", "endpoint": "parse-validate-mongosave/system-permissions", "input": {"natural_language": "Tenant: Alpha Corp\n\npermission_id | permission_name | permission_type | resource_identifier | actions | description | scope | natural_language | version | status\n\nPERM_ENTITY_EMPLOYEE | Employee Entity | entity | Employee | [\"create\", \"read\", \"update\", \"delete\"] | Access to Employee entity | tenant_records | Permission to access employee entity | 1 | active\n\nPERM_ATTR_EMP_LEAVE | Employee totalLeaveEntitlement Attribute | attribute | Employee.totalLeaveEntitlement | [\"read\", \"update\"] | Access to Employee totalLeaveEntitlement attribute | department_records | Permission to access total leave entitlement attribute | 1 | active", "tenant_id": "T1001", "edit_draft": false, "edit_temp": false, "edit_production": false}, "output": {"success": true, "permission_results": [{"success": false, "error": "Item already exists in draft. Use edit_draft=true to update.", "existing_in": "mongo", "existing_document": {"_id": "6858f78311d79f18c46b140b", "permission_id": "PERM_ENTITY_EMPLOYEE", "permission_name": "Employee Entity Permission", "permission_type": "entity", "resource_identifier": "Employee", "actions": ["create", "read", "update", "delete"], "scope": "tenant_records", "entity_id": "E45", "attribute_id": "", "tenant_id": "T1007", "description": "Access to Employee entity", "natural_language": "Permission to access employee entity", "version": 1, "status": "draft", "created_at": "2025-06-23T06:43:15.084Z", "updated_at": "2025-06-23T06:43:15.084Z", "created_by": "Tarun", "updated_by": "Tarun", "permission_status": "new", "changes_detected": []}, "parsed_data": {"permission_id": "PERM_ENTITY_EMPLOYEE", "permission_name": "Employee Entity", "permission_type": "entity", "resource_identifier": "Employee", "actions": ["create", "read", "update", "delete"], "scope": "tenant_records", "entity_id": "E13", "attribute_id": "", "go_id": "", "lo_id": "", "tenant_id": "T1001", "description": "Access to Employee entity", "natural_language": "Permission to access employee entity", "version": 1, "status": "active", "created_at": "2025-06-23T07:15:42.568833", "updated_at": "2025-06-23T07:15:42.568833", "created_by": "Tarun", "updated_by": "Tarun", "permission_status": "existing", "changes_detected": []}}, {"success": true, "saved_data": {"permission_id": "PERM_ATTR_EMP_LEAVE", "permission_name": "Employee totalLeaveEntitlement Attribute", "permission_type": "attribute", "resource_identifier": "Employee.totalLeaveEntitlement", "actions": ["read", "update"], "scope": "department_records", "entity_id": "E13", "attribute_id": "E13.At11", "go_id": "", "lo_id": "", "tenant_id": "T1001", "description": "Access to Employee totalLeaveEntitlement attribute", "natural_language": "Permission to access total leave entitlement attribute", "version": 1, "status": "draft", "created_at": "2025-06-23T07:15:42.617971", "updated_at": "2025-06-23T07:15:42.617979", "created_by": "Tarun", "updated_by": "Tarun", "permission_status": "new", "changes_detected": [], "_id": "6858ff1e909a273e7e5d8167"}, "validation_result": {"structure_errors": [], "required_field_errors": [], "data_type_errors": [], "custom_errors": []}, "dependency_errors": [], "uniqueness_result": {"is_unique": true, "status": "unique", "message": "Permission with permission_id PERM_ATTR_EMP_LEAVE is unique"}}], "operation": "parse_validate_mongosave", "total_permissions": 2}, "status": "success"}
{"timestamp": "2025-06-23T09:04:52.571884", "endpoint": "parse-validate-mongosave/system-permissions", "input": {"natural_language": "Tenant: Acme Corp\n\npermission_id | permission_name | permission_type | resource_identifier | actions | description | scope | natural_language | version | status\n\nPERM_ENTITY_LEAVEAPP | LeaveApplication Entity | entity | LeaveApplication | [\"create\", \"read\", \"update\", \"delete\"] | Access to LeaveApplication entity | tenant_records | Permission to access leave application entity | 1 | active\n\nPERM_ATTR_LEAVE_ID | LeaveApplication leaveId Attribute | attribute | LeaveApplication.leaveId | [\"read\"] | Access to LeaveApplication leaveId attribute | own_records | Permission to read leave ID attribute | 1 | active\n\nPERM_ATTR_EMPLOYEE_ID | LeaveApplication employeeId Attribute | attribute | LeaveApplication.employeeId | [\"create\", \"read\", \"update\"] | Access to LeaveApplication employeeId attribute | own_records | Permission to access employee ID attribute | 1 | active\n\nPERM_ATTR_START_DATE | LeaveApplication startDate Attribute | attribute | LeaveApplication.startDate | [\"create\", \"read\", \"update\"] | Access to LeaveApplication startDate attribute | own_records | Permission to access start date attribute | 1 | active\n\nPERM_ATTR_END_DATE | LeaveApplication endDate Attribute | attribute | LeaveApplication.endDate | [\"create\", \"read\", \"update\"] | Access to LeaveApplication endDate attribute | own_records | Permission to access end date attribute | 1 | active\n\nPERM_ATTR_NUM_DAYS | LeaveApplication numDays Attribute | attribute | LeaveApplication.numDays | [\"read\"] | Access to LeaveApplication numDays attribute | own_records | Permission to read number of days attribute | 1 | active\n\nPERM_ATTR_REASON | LeaveApplication reason Attribute | attribute | LeaveApplication.reason | [\"create\", \"read\", \"update\"] | Access to LeaveApplication reason attribute | own_records | Permission to access reason attribute | 1 | active\n\nPERM_ATTR_LEAVE_TYPE | LeaveApplication leaveTypeName Attribute | attribute | LeaveApplication.leaveTypeName | [\"create\", \"read\", \"update\"] | Access to LeaveApplication leaveTypeName attribute | tenant_records | Permission to access leave type name attribute | 1 | active\n\nPERM_ATTR_LEAVE_SUBTYPE | LeaveApplication leaveSubTypeName Attribute | attribute | LeaveApplication.leaveSubTypeName | [\"create\", \"read\", \"update\"] | Access to LeaveApplication leaveSubTypeName attribute | tenant_records | Permission to access leave sub-type name attribute | 1 | active\n\nPERM_ATTR_STATUS | LeaveApplication status Attribute | attribute | LeaveApplication.status | [\"read\", \"update\"] | Access to LeaveApplication status attribute | department_records | Permission to access status attribute | 1 | active\n\nPERM_ATTR_REQ_DOC | LeaveApplication requiresDocumentation Attribute | attribute | LeaveApplication.requiresDocumentation | [\"read\"] | Access to LeaveApplication requiresDocumentation attribute | tenant_records | Permission to read requires documentation attribute | 1 | active\n\nPERM_ATTR_DOC_PROVIDED | LeaveApplication documentationProvided Attribute | attribute | LeaveApplication.documentationProvided | [\"create\", \"read\", \"update\"] | Access to LeaveApplication documentationProvided attribute | own_records | Permission to access documentation provided attribute | 1 | active\n\nPERM_ATTR_SUBMISSION_DATE | LeaveApplication submissionDate Attribute | attribute | LeaveApplication.submissionDate | [\"read\"] | Access to LeaveApplication submissionDate attribute | own_records | Permission to read submission date attribute | 1 | active\n\nPERM_ATTR_APPROVAL_DATE | LeaveApplication approvalDate Attribute | attribute | LeaveApplication.approvalDate | [\"read\"] | Access to LeaveApplication approvalDate attribute | department_records | Permission to read approval date attribute | 1 | active\n\nPERM_ATTR_APPROVED_BY | LeaveApplication approvedBy Attribute | attribute | LeaveApplication.approvedBy | [\"read\", \"update\"] | Access to LeaveApplication approvedBy attribute | department_records | Permission to access approved by attribute | 1 | active\n\nPERM_ATTR_COMMENTS | LeaveApplication comments Attribute | attribute | LeaveApplication.comments | [\"create\", \"read\", \"update\"] | Access to LeaveApplication comments attribute | department_records | Permission to access comments attribute | 1 | active\n\nPERM_ENTITY_EMPLOYEE | Employee Entity | entity | Employee | [\"create\", \"read\", \"update\", \"delete\"] | Access to Employee entity | department_records | Permission to access employee entity | 1 | active\n\nPERM_ATTR_EMP_ID | Employee employeeId Attribute | attribute | Employee.employeeId | [\"read\"] | Access to Employee employeeId attribute | own_records | Permission to read employee ID attribute | 1 | active\n\nPERM_ATTR_FIRST_NAME | Employee firstName Attribute | attribute | Employee.firstName | [\"create\", \"read\", \"update\"] | Access to Employee firstName attribute | own_records | Permission to access first name attribute | 1 | active\n\nPERM_ATTR_LAST_NAME | Employee lastName Attribute | attribute | Employee.lastName | [\"create\", \"read\", \"update\"] | Access to Employee lastName attribute | own_records | Permission to access last name attribute | 1 | active\n\nPERM_ATTR_EMAIL | Employee email Attribute | attribute | Employee.email | [\"create\", \"read\", \"update\"] | Access to Employee email attribute | own_records | Permission to access email attribute | 1 | active\n\nPERM_ATTR_PHONE | Employee phone Attribute | attribute | Employee.phone | [\"create\", \"read\", \"update\"] | Access to Employee phone attribute | own_records | Permission to access phone attribute | 1 | active\n\nPERM_ATTR_DEPT_ID | Employee departmentId Attribute | attribute | Employee.departmentId | [\"read\"] | Access to Employee departmentId attribute | department_records | Permission to read department ID attribute | 1 | active\n\nPERM_ATTR_MANAGER_ID | Employee managerId Attribute | attribute | Employee.managerId | [\"read\"] | Access to Employee managerId attribute | department_records | Permission to read manager ID attribute | 1 | active\n\nPERM_ATTR_JOB_TITLE | Employee jobTitle Attribute | attribute | Employee.jobTitle | [\"create\", \"read\", \"update\"] | Access to Employee jobTitle attribute | own_records | Permission to access job title attribute | 1 | active\n\nPERM_ATTR_HIRE_DATE | Employee hireDate Attribute | attribute | Employee.hireDate | [\"read\"] | Access to Employee hireDate attribute | department_records | Permission to read hire date attribute | 1 | active\n\nPERM_ATTR_EMP_STATUS | Employee employmentStatus Attribute | attribute | Employee.employmentStatus | [\"read\"] | Access to Employee employmentStatus attribute | department_records | Permission to read employment status attribute | 1 | active\n\nPERM_ATTR_ANNUAL_LEAVE | Employee annualLeaveBalance Attribute | attribute | Employee.annualLeaveBalance | [\"read\"] | Access to Employee annualLeaveBalance attribute | own_records | Permission to read annual leave balance attribute | 1 | active\n\nPERM_ATTR_SICK_LEAVE | Employee sickLeaveBalance Attribute | attribute | Employee.sickLeaveBalance | [\"read\"] | Access to Employee sickLeaveBalance attribute | own_records | Permission to read sick leave balance attribute | 1 | active\n\nPERM_ATTR_PERSONAL_LEAVE | Employee personalLeaveBalance Attribute | attribute | Employee.personalLeaveBalance | [\"read\"] | Access to Employee personalLeaveBalance attribute | own_records | Permission to read personal leave balance attribute | 1 | active\n\nPERM_ATTR_TOTAL_ENTITLEMENT | Employee totalLeaveEntitlement Attribute | attribute | Employee.totalLeaveEntitlement | [\"read\"] | Access to Employee totalLeaveEntitlement attribute | own_records | Permission to read total leave entitlement attribute | 1 | active\n", "tenant_id": "T1001", "edit_draft": true, "edit_temp": false, "edit_production": false}, "output": {"success": true, "permission_results": [{"success": false, "errors": ["Entity with entity_id E_LeaveApplication_1750669491 does not exist"], "parsed_data": {"permission_id": "PERM_ENTITY_LEAVEAPP", "permission_name": "LeaveApplication Entity", "permission_type": "entity", "resource_identifier": "LeaveApplication", "actions": ["create", "read", "update", "delete"], "scope": "tenant_records", "entity_id": "E_LeaveApplication_1750669491", "attribute_id": "", "go_id": "", "lo_id": "", "tenant_id": "T1001", "description": "Access to LeaveApplication entity", "natural_language": "Permission to access leave application entity", "version": 1, "status": "active", "created_at": "2025-06-23T09:04:51.277044", "updated_at": "2025-06-23T09:04:51.277044", "created_by": "Tarun", "updated_by": "Tarun", "permission_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Entity with entity_id E_LeaveApplication_1750669491 does not exist", "Attribute with attribute_id A_E_LeaveApplication_1750669491_leaveId_1750669491 does not exist"], "parsed_data": {"permission_id": "PERM_ATTR_LEAVE_ID", "permission_name": "LeaveApplication leaveId Attribute", "permission_type": "attribute", "resource_identifier": "LeaveApplication.leaveId", "actions": ["read"], "scope": "own_records", "entity_id": "E_LeaveApplication_1750669491", "attribute_id": "A_E_LeaveApplication_1750669491_leaveId_1750669491", "go_id": "", "lo_id": "", "tenant_id": "T1001", "description": "Access to LeaveApplication leaveId attribute", "natural_language": "Permission to read leave ID attribute", "version": 1, "status": "active", "created_at": "2025-06-23T09:04:51.306654", "updated_at": "2025-06-23T09:04:51.306654", "created_by": "Tarun", "updated_by": "Tarun", "permission_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Entity with entity_id E_LeaveApplication_1750669491 does not exist", "Attribute with attribute_id A_E_LeaveApplication_1750669491_employeeId_1750669491 does not exist"], "parsed_data": {"permission_id": "PERM_ATTR_EMPLOYEE_ID", "permission_name": "LeaveApplication employeeId Attribute", "permission_type": "attribute", "resource_identifier": "LeaveApplication.employeeId", "actions": ["create", "read", "update"], "scope": "own_records", "entity_id": "E_LeaveApplication_1750669491", "attribute_id": "A_E_LeaveApplication_1750669491_employeeId_1750669491", "go_id": "", "lo_id": "", "tenant_id": "T1001", "description": "Access to LeaveApplication employeeId attribute", "natural_language": "Permission to access employee ID attribute", "version": 1, "status": "active", "created_at": "2025-06-23T09:04:51.335267", "updated_at": "2025-06-23T09:04:51.335267", "created_by": "Tarun", "updated_by": "Tarun", "permission_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Entity with entity_id E_LeaveApplication_1750669491 does not exist", "Attribute with attribute_id A_E_LeaveApplication_1750669491_startDate_1750669491 does not exist"], "parsed_data": {"permission_id": "PERM_ATTR_START_DATE", "permission_name": "LeaveApplication startDate Attribute", "permission_type": "attribute", "resource_identifier": "LeaveApplication.startDate", "actions": ["create", "read", "update"], "scope": "own_records", "entity_id": "E_LeaveApplication_1750669491", "attribute_id": "A_E_LeaveApplication_1750669491_startDate_1750669491", "go_id": "", "lo_id": "", "tenant_id": "T1001", "description": "Access to LeaveApplication startDate attribute", "natural_language": "Permission to access start date attribute", "version": 1, "status": "active", "created_at": "2025-06-23T09:04:51.362727", "updated_at": "2025-06-23T09:04:51.362727", "created_by": "Tarun", "updated_by": "Tarun", "permission_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Entity with entity_id E_LeaveApplication_1750669491 does not exist", "Attribute with attribute_id A_E_LeaveApplication_1750669491_endDate_1750669491 does not exist"], "parsed_data": {"permission_id": "PERM_ATTR_END_DATE", "permission_name": "LeaveApplication endDate Attribute", "permission_type": "attribute", "resource_identifier": "LeaveApplication.endDate", "actions": ["create", "read", "update"], "scope": "own_records", "entity_id": "E_LeaveApplication_1750669491", "attribute_id": "A_E_LeaveApplication_1750669491_endDate_1750669491", "go_id": "", "lo_id": "", "tenant_id": "T1001", "description": "Access to LeaveApplication endDate attribute", "natural_language": "Permission to access end date attribute", "version": 1, "status": "active", "created_at": "2025-06-23T09:04:51.390760", "updated_at": "2025-06-23T09:04:51.390760", "created_by": "Tarun", "updated_by": "Tarun", "permission_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Entity with entity_id E_LeaveApplication_1750669491 does not exist", "Attribute with attribute_id A_E_LeaveApplication_1750669491_numDays_1750669491 does not exist"], "parsed_data": {"permission_id": "PERM_ATTR_NUM_DAYS", "permission_name": "LeaveApplication numDays Attribute", "permission_type": "attribute", "resource_identifier": "LeaveApplication.numDays", "actions": ["read"], "scope": "own_records", "entity_id": "E_LeaveApplication_1750669491", "attribute_id": "A_E_LeaveApplication_1750669491_numDays_1750669491", "go_id": "", "lo_id": "", "tenant_id": "T1001", "description": "Access to LeaveApplication numDays attribute", "natural_language": "Permission to read number of days attribute", "version": 1, "status": "active", "created_at": "2025-06-23T09:04:51.420512", "updated_at": "2025-06-23T09:04:51.420512", "created_by": "Tarun", "updated_by": "Tarun", "permission_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Entity with entity_id E_LeaveApplication_1750669491 does not exist", "Attribute with attribute_id A_E_LeaveApplication_1750669491_reason_1750669491 does not exist"], "parsed_data": {"permission_id": "PERM_ATTR_REASON", "permission_name": "LeaveApplication reason Attribute", "permission_type": "attribute", "resource_identifier": "LeaveApplication.reason", "actions": ["create", "read", "update"], "scope": "own_records", "entity_id": "E_LeaveApplication_1750669491", "attribute_id": "A_E_LeaveApplication_1750669491_reason_1750669491", "go_id": "", "lo_id": "", "tenant_id": "T1001", "description": "Access to LeaveApplication reason attribute", "natural_language": "Permission to access reason attribute", "version": 1, "status": "active", "created_at": "2025-06-23T09:04:51.448518", "updated_at": "2025-06-23T09:04:51.448518", "created_by": "Tarun", "updated_by": "Tarun", "permission_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Entity with entity_id E_LeaveApplication_1750669491 does not exist", "Attribute with attribute_id A_E_LeaveApplication_1750669491_leaveTypeName_1750669491 does not exist"], "parsed_data": {"permission_id": "PERM_ATTR_LEAVE_TYPE", "permission_name": "LeaveApplication leaveTypeName Attribute", "permission_type": "attribute", "resource_identifier": "LeaveApplication.leaveTypeName", "actions": ["create", "read", "update"], "scope": "tenant_records", "entity_id": "E_LeaveApplication_1750669491", "attribute_id": "A_E_LeaveApplication_1750669491_leaveTypeName_1750669491", "go_id": "", "lo_id": "", "tenant_id": "T1001", "description": "Access to LeaveApplication leaveTypeName attribute", "natural_language": "Permission to access leave type name attribute", "version": 1, "status": "active", "created_at": "2025-06-23T09:04:51.475370", "updated_at": "2025-06-23T09:04:51.475370", "created_by": "Tarun", "updated_by": "Tarun", "permission_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Entity with entity_id E_LeaveApplication_1750669491 does not exist", "Attribute with attribute_id A_E_LeaveApplication_1750669491_leaveSubTypeName_1750669491 does not exist"], "parsed_data": {"permission_id": "PERM_ATTR_LEAVE_SUBTYPE", "permission_name": "LeaveApplication leaveSubTypeName Attribute", "permission_type": "attribute", "resource_identifier": "LeaveApplication.leaveSubTypeName", "actions": ["create", "read", "update"], "scope": "tenant_records", "entity_id": "E_LeaveApplication_1750669491", "attribute_id": "A_E_LeaveApplication_1750669491_leaveSubTypeName_1750669491", "go_id": "", "lo_id": "", "tenant_id": "T1001", "description": "Access to LeaveApplication leaveSubTypeName attribute", "natural_language": "Permission to access leave sub-type name attribute", "version": 1, "status": "active", "created_at": "2025-06-23T09:04:51.499887", "updated_at": "2025-06-23T09:04:51.499887", "created_by": "Tarun", "updated_by": "Tarun", "permission_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Entity with entity_id E_LeaveApplication_1750669491 does not exist", "Attribute with attribute_id A_E_LeaveApplication_1750669491_status_1750669491 does not exist"], "parsed_data": {"permission_id": "PERM_ATTR_STATUS", "permission_name": "LeaveApplication status Attribute", "permission_type": "attribute", "resource_identifier": "LeaveApplication.status", "actions": ["read", "update"], "scope": "department_records", "entity_id": "E_LeaveApplication_1750669491", "attribute_id": "A_E_LeaveApplication_1750669491_status_1750669491", "go_id": "", "lo_id": "", "tenant_id": "T1001", "description": "Access to LeaveApplication status attribute", "natural_language": "Permission to access status attribute", "version": 1, "status": "active", "created_at": "2025-06-23T09:04:51.530110", "updated_at": "2025-06-23T09:04:51.530110", "created_by": "Tarun", "updated_by": "Tarun", "permission_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Entity with entity_id E_LeaveApplication_1750669491 does not exist", "Attribute with attribute_id A_E_LeaveApplication_1750669491_requiresDocumentation_1750669491 does not exist"], "parsed_data": {"permission_id": "PERM_ATTR_REQ_DOC", "permission_name": "LeaveApplication requiresDocumentation Attribute", "permission_type": "attribute", "resource_identifier": "LeaveApplication.requiresDocumentation", "actions": ["read"], "scope": "tenant_records", "entity_id": "E_LeaveApplication_1750669491", "attribute_id": "A_E_LeaveApplication_1750669491_requiresDocumentation_1750669491", "go_id": "", "lo_id": "", "tenant_id": "T1001", "description": "Access to LeaveApplication requiresDocumentation attribute", "natural_language": "Permission to read requires documentation attribute", "version": 1, "status": "active", "created_at": "2025-06-23T09:04:51.558828", "updated_at": "2025-06-23T09:04:51.558828", "created_by": "Tarun", "updated_by": "Tarun", "permission_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Entity with entity_id E_LeaveApplication_1750669491 does not exist", "Attribute with attribute_id A_E_LeaveApplication_1750669491_documentationProvided_1750669491 does not exist"], "parsed_data": {"permission_id": "PERM_ATTR_DOC_PROVIDED", "permission_name": "LeaveApplication documentationProvided Attribute", "permission_type": "attribute", "resource_identifier": "LeaveApplication.documentationProvided", "actions": ["create", "read", "update"], "scope": "own_records", "entity_id": "E_LeaveApplication_1750669491", "attribute_id": "A_E_LeaveApplication_1750669491_documentationProvided_1750669491", "go_id": "", "lo_id": "", "tenant_id": "T1001", "description": "Access to LeaveApplication documentationProvided attribute", "natural_language": "Permission to access documentation provided attribute", "version": 1, "status": "active", "created_at": "2025-06-23T09:04:51.583117", "updated_at": "2025-06-23T09:04:51.583117", "created_by": "Tarun", "updated_by": "Tarun", "permission_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Entity with entity_id E_LeaveApplication_1750669491 does not exist", "Attribute with attribute_id A_E_LeaveApplication_1750669491_submissionDate_1750669491 does not exist"], "parsed_data": {"permission_id": "PERM_ATTR_SUBMISSION_DATE", "permission_name": "LeaveApplication submissionDate Attribute", "permission_type": "attribute", "resource_identifier": "LeaveApplication.submissionDate", "actions": ["read"], "scope": "own_records", "entity_id": "E_LeaveApplication_1750669491", "attribute_id": "A_E_LeaveApplication_1750669491_submissionDate_1750669491", "go_id": "", "lo_id": "", "tenant_id": "T1001", "description": "Access to LeaveApplication submissionDate attribute", "natural_language": "Permission to read submission date attribute", "version": 1, "status": "active", "created_at": "2025-06-23T09:04:51.610631", "updated_at": "2025-06-23T09:04:51.610631", "created_by": "Tarun", "updated_by": "Tarun", "permission_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Entity with entity_id E_LeaveApplication_1750669491 does not exist", "Attribute with attribute_id A_E_LeaveApplication_1750669491_approvalDate_1750669491 does not exist"], "parsed_data": {"permission_id": "PERM_ATTR_APPROVAL_DATE", "permission_name": "LeaveApplication approvalDate Attribute", "permission_type": "attribute", "resource_identifier": "LeaveApplication.approvalDate", "actions": ["read"], "scope": "department_records", "entity_id": "E_LeaveApplication_1750669491", "attribute_id": "A_E_LeaveApplication_1750669491_approvalDate_1750669491", "go_id": "", "lo_id": "", "tenant_id": "T1001", "description": "Access to LeaveApplication approvalDate attribute", "natural_language": "Permission to read approval date attribute", "version": 1, "status": "active", "created_at": "2025-06-23T09:04:51.638636", "updated_at": "2025-06-23T09:04:51.638636", "created_by": "Tarun", "updated_by": "Tarun", "permission_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Entity with entity_id E_LeaveApplication_1750669491 does not exist", "Attribute with attribute_id A_E_LeaveApplication_1750669491_approvedBy_1750669491 does not exist"], "parsed_data": {"permission_id": "PERM_ATTR_APPROVED_BY", "permission_name": "LeaveApplication approvedBy Attribute", "permission_type": "attribute", "resource_identifier": "LeaveApplication.approvedBy", "actions": ["read", "update"], "scope": "department_records", "entity_id": "E_LeaveApplication_1750669491", "attribute_id": "A_E_LeaveApplication_1750669491_approvedBy_1750669491", "go_id": "", "lo_id": "", "tenant_id": "T1001", "description": "Access to LeaveApplication approvedBy attribute", "natural_language": "Permission to access approved by attribute", "version": 1, "status": "active", "created_at": "2025-06-23T09:04:51.665938", "updated_at": "2025-06-23T09:04:51.665938", "created_by": "Tarun", "updated_by": "Tarun", "permission_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Entity with entity_id E_LeaveApplication_1750669491 does not exist", "Attribute with attribute_id A_E_LeaveApplication_1750669491_comments_1750669491 does not exist"], "parsed_data": {"permission_id": "PERM_ATTR_COMMENTS", "permission_name": "LeaveApplication comments Attribute", "permission_type": "attribute", "resource_identifier": "LeaveApplication.comments", "actions": ["create", "read", "update"], "scope": "department_records", "entity_id": "E_LeaveApplication_1750669491", "attribute_id": "A_E_LeaveApplication_1750669491_comments_1750669491", "go_id": "", "lo_id": "", "tenant_id": "T1001", "description": "Access to LeaveApplication comments attribute", "natural_language": "Permission to access comments attribute", "version": 1, "status": "active", "created_at": "2025-06-23T09:04:51.693240", "updated_at": "2025-06-23T09:04:51.693240", "created_by": "Tarun", "updated_by": "Tarun", "permission_status": "new", "changes_detected": []}}, {"success": true, "saved_data": {"permission_id": "PERM_ENTITY_EMPLOYEE", "permission_name": "Employee Entity", "permission_type": "entity", "resource_identifier": "Employee", "actions": ["create", "read", "update", "delete"], "scope": "department_records", "entity_id": "E13", "attribute_id": "", "go_id": "", "lo_id": "", "tenant_id": "T1001", "description": "Access to Employee entity", "natural_language": "Permission to access employee entity", "version": 2, "status": "active", "created_at": "2025-06-23T09:04:51.704252", "updated_at": "2025-06-23T09:04:52.306071", "created_by": "Tarun", "updated_by": "Tarun", "permission_status": "existing", "changes_detected": [], "_id": "6858f78311d79f18c46b140b"}, "validation_result": {"structure_errors": [], "required_field_errors": [], "data_type_errors": [], "custom_errors": []}, "dependency_errors": [], "uniqueness_result": {"is_unique": false, "status": "exists_in_mongo", "message": "Permission with permission_id PERM_ENTITY_EMPLOYEE already exists in MongoDB drafts", "existing_document": {"_id": "6858f78311d79f18c46b140b", "permission_id": "PERM_ENTITY_EMPLOYEE", "permission_name": "Employee Entity Permission", "permission_type": "entity", "resource_identifier": "Employee", "actions": ["create", "read", "update", "delete"], "scope": "tenant_records", "entity_id": "E45", "attribute_id": "", "tenant_id": "T1007", "description": "Access to Employee entity", "natural_language": "Permission to access employee entity", "version": 1, "status": "draft", "created_at": "2025-06-23T06:43:15.084Z", "updated_at": "2025-06-23T06:43:15.084Z", "created_by": "Tarun", "updated_by": "Tarun", "permission_status": "new", "changes_detected": []}}}, {"success": false, "errors": ["Attribute with attribute_id A_E13_employeeId_1750669491 does not exist"], "parsed_data": {"permission_id": "PERM_ATTR_EMP_ID", "permission_name": "Employee employeeId Attribute", "permission_type": "attribute", "resource_identifier": "Employee.employeeId", "actions": ["read"], "scope": "own_records", "entity_id": "E13", "attribute_id": "A_E13_employeeId_1750669491", "go_id": "", "lo_id": "", "tenant_id": "T1001", "description": "Access to Employee employeeId attribute", "natural_language": "Permission to read employee ID attribute", "version": 1, "status": "active", "created_at": "2025-06-23T09:04:51.722820", "updated_at": "2025-06-23T09:04:51.722820", "created_by": "Tarun", "updated_by": "Tarun", "permission_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Attribute with attribute_id A_E13_firstName_1750669491 does not exist"], "parsed_data": {"permission_id": "PERM_ATTR_FIRST_NAME", "permission_name": "Employee firstName Attribute", "permission_type": "attribute", "resource_identifier": "Employee.firstName", "actions": ["create", "read", "update"], "scope": "own_records", "entity_id": "E13", "attribute_id": "A_E13_firstName_1750669491", "go_id": "", "lo_id": "", "tenant_id": "T1001", "description": "Access to Employee firstName attribute", "natural_language": "Permission to access first name attribute", "version": 1, "status": "active", "created_at": "2025-06-23T09:04:51.739839", "updated_at": "2025-06-23T09:04:51.739839", "created_by": "Tarun", "updated_by": "Tarun", "permission_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Attribute with attribute_id A_E13_lastName_1750669491 does not exist"], "parsed_data": {"permission_id": "PERM_ATTR_LAST_NAME", "permission_name": "Employee lastName Attribute", "permission_type": "attribute", "resource_identifier": "Employee.lastName", "actions": ["create", "read", "update"], "scope": "own_records", "entity_id": "E13", "attribute_id": "A_E13_lastName_1750669491", "go_id": "", "lo_id": "", "tenant_id": "T1001", "description": "Access to Employee lastName attribute", "natural_language": "Permission to access last name attribute", "version": 1, "status": "active", "created_at": "2025-06-23T09:04:51.758800", "updated_at": "2025-06-23T09:04:51.758800", "created_by": "Tarun", "updated_by": "Tarun", "permission_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Attribute with attribute_id A_E13_email_1750669491 does not exist"], "parsed_data": {"permission_id": "PERM_ATTR_EMAIL", "permission_name": "Employee email Attribute", "permission_type": "attribute", "resource_identifier": "Employee.email", "actions": ["create", "read", "update"], "scope": "own_records", "entity_id": "E13", "attribute_id": "A_E13_email_1750669491", "go_id": "", "lo_id": "", "tenant_id": "T1001", "description": "Access to Employee email attribute", "natural_language": "Permission to access email attribute", "version": 1, "status": "active", "created_at": "2025-06-23T09:04:51.777470", "updated_at": "2025-06-23T09:04:51.777470", "created_by": "Tarun", "updated_by": "Tarun", "permission_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Attribute with attribute_id A_E13_phone_1750669491 does not exist"], "parsed_data": {"permission_id": "PERM_ATTR_PHONE", "permission_name": "Employee phone Attribute", "permission_type": "attribute", "resource_identifier": "Employee.phone", "actions": ["create", "read", "update"], "scope": "own_records", "entity_id": "E13", "attribute_id": "A_E13_phone_1750669491", "go_id": "", "lo_id": "", "tenant_id": "T1001", "description": "Access to Employee phone attribute", "natural_language": "Permission to access phone attribute", "version": 1, "status": "active", "created_at": "2025-06-23T09:04:51.794893", "updated_at": "2025-06-23T09:04:51.794893", "created_by": "Tarun", "updated_by": "Tarun", "permission_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Attribute with attribute_id A_E13_departmentId_1750669491 does not exist"], "parsed_data": {"permission_id": "PERM_ATTR_DEPT_ID", "permission_name": "Employee departmentId Attribute", "permission_type": "attribute", "resource_identifier": "Employee.departmentId", "actions": ["read"], "scope": "department_records", "entity_id": "E13", "attribute_id": "A_E13_departmentId_1750669491", "go_id": "", "lo_id": "", "tenant_id": "T1001", "description": "Access to Employee departmentId attribute", "natural_language": "Permission to read department ID attribute", "version": 1, "status": "active", "created_at": "2025-06-23T09:04:51.813515", "updated_at": "2025-06-23T09:04:51.813515", "created_by": "Tarun", "updated_by": "Tarun", "permission_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Attribute with attribute_id A_E13_managerId_1750669491 does not exist"], "parsed_data": {"permission_id": "PERM_ATTR_MANAGER_ID", "permission_name": "Employee managerId Attribute", "permission_type": "attribute", "resource_identifier": "Employee.managerId", "actions": ["read"], "scope": "department_records", "entity_id": "E13", "attribute_id": "A_E13_managerId_1750669491", "go_id": "", "lo_id": "", "tenant_id": "T1001", "description": "Access to Employee managerId attribute", "natural_language": "Permission to read manager ID attribute", "version": 1, "status": "active", "created_at": "2025-06-23T09:04:51.831278", "updated_at": "2025-06-23T09:04:51.831278", "created_by": "Tarun", "updated_by": "Tarun", "permission_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Attribute with attribute_id A_E13_jobTitle_1750669491 does not exist"], "parsed_data": {"permission_id": "PERM_ATTR_JOB_TITLE", "permission_name": "Employee jobTitle Attribute", "permission_type": "attribute", "resource_identifier": "Employee.jobTitle", "actions": ["create", "read", "update"], "scope": "own_records", "entity_id": "E13", "attribute_id": "A_E13_jobTitle_1750669491", "go_id": "", "lo_id": "", "tenant_id": "T1001", "description": "Access to Employee jobTitle attribute", "natural_language": "Permission to access job title attribute", "version": 1, "status": "active", "created_at": "2025-06-23T09:04:51.849945", "updated_at": "2025-06-23T09:04:51.849945", "created_by": "Tarun", "updated_by": "Tarun", "permission_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Attribute with attribute_id A_E13_hireDate_1750669491 does not exist"], "parsed_data": {"permission_id": "PERM_ATTR_HIRE_DATE", "permission_name": "Employee hireDate Attribute", "permission_type": "attribute", "resource_identifier": "Employee.hireDate", "actions": ["read"], "scope": "department_records", "entity_id": "E13", "attribute_id": "A_E13_hireDate_1750669491", "go_id": "", "lo_id": "", "tenant_id": "T1001", "description": "Access to Employee hireDate attribute", "natural_language": "Permission to read hire date attribute", "version": 1, "status": "active", "created_at": "2025-06-23T09:04:51.871912", "updated_at": "2025-06-23T09:04:51.871912", "created_by": "Tarun", "updated_by": "Tarun", "permission_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Attribute with attribute_id A_E13_employmentStatus_1750669491 does not exist"], "parsed_data": {"permission_id": "PERM_ATTR_EMP_STATUS", "permission_name": "Employee employmentStatus Attribute", "permission_type": "attribute", "resource_identifier": "Employee.employmentStatus", "actions": ["read"], "scope": "department_records", "entity_id": "E13", "attribute_id": "A_E13_employmentStatus_1750669491", "go_id": "", "lo_id": "", "tenant_id": "T1001", "description": "Access to Employee employmentStatus attribute", "natural_language": "Permission to read employment status attribute", "version": 1, "status": "active", "created_at": "2025-06-23T09:04:51.888828", "updated_at": "2025-06-23T09:04:51.888828", "created_by": "Tarun", "updated_by": "Tarun", "permission_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Attribute with attribute_id A_E13_annualLeaveBalance_1750669491 does not exist"], "parsed_data": {"permission_id": "PERM_ATTR_ANNUAL_LEAVE", "permission_name": "Employee annualLeaveBalance Attribute", "permission_type": "attribute", "resource_identifier": "Employee.annualLeaveBalance", "actions": ["read"], "scope": "own_records", "entity_id": "E13", "attribute_id": "A_E13_annualLeaveBalance_1750669491", "go_id": "", "lo_id": "", "tenant_id": "T1001", "description": "Access to Employee annualLeaveBalance attribute", "natural_language": "Permission to read annual leave balance attribute", "version": 1, "status": "active", "created_at": "2025-06-23T09:04:51.906602", "updated_at": "2025-06-23T09:04:51.906602", "created_by": "Tarun", "updated_by": "Tarun", "permission_status": "new", "changes_detected": []}}, {"success": true, "saved_data": {"permission_id": "PERM_ATTR_SICK_LEAVE", "permission_name": "Employee sickLeaveBalance Attribute", "permission_type": "attribute", "resource_identifier": "Employee.sickLeaveBalance", "actions": ["read"], "scope": "own_records", "entity_id": "E13", "attribute_id": "E13.At9", "go_id": "", "lo_id": "", "tenant_id": "T1001", "description": "Access to Employee sickLeaveBalance attribute", "natural_language": "Permission to read sick leave balance attribute", "version": 1, "status": "draft", "created_at": "2025-06-23T09:04:52.529883", "updated_at": "2025-06-23T09:04:52.529888", "created_by": "Tarun", "updated_by": "Tarun", "permission_status": "new", "changes_detected": [], "_id": "685918b45e423f11979d4094"}, "validation_result": {"structure_errors": [], "required_field_errors": [], "data_type_errors": [], "custom_errors": []}, "dependency_errors": [], "uniqueness_result": {"is_unique": true, "status": "unique", "message": "Permission with permission_id PERM_ATTR_SICK_LEAVE is unique"}}, {"success": true, "saved_data": {"permission_id": "PERM_ATTR_PERSONAL_LEAVE", "permission_name": "Employee personalLeaveBalance Attribute", "permission_type": "attribute", "resource_identifier": "Employee.personalLeaveBalance", "actions": ["read"], "scope": "own_records", "entity_id": "E13", "attribute_id": "E13.At10", "go_id": "", "lo_id": "", "tenant_id": "T1001", "description": "Access to Employee personalLeaveBalance attribute", "natural_language": "Permission to read personal leave balance attribute", "version": 1, "status": "draft", "created_at": "2025-06-23T09:04:52.551828", "updated_at": "2025-06-23T09:04:52.551832", "created_by": "Tarun", "updated_by": "Tarun", "permission_status": "new", "changes_detected": [], "_id": "685918b45e423f11979d4095"}, "validation_result": {"structure_errors": [], "required_field_errors": [], "data_type_errors": [], "custom_errors": []}, "dependency_errors": [], "uniqueness_result": {"is_unique": true, "status": "unique", "message": "Permission with permission_id PERM_ATTR_PERSONAL_LEAVE is unique"}}, {"success": true, "saved_data": {"permission_id": "PERM_ATTR_TOTAL_ENTITLEMENT", "permission_name": "Employee totalLeaveEntitlement Attribute", "permission_type": "attribute", "resource_identifier": "Employee.totalLeaveEntitlement", "actions": ["read"], "scope": "own_records", "entity_id": "E13", "attribute_id": "E13.At11", "go_id": "", "lo_id": "", "tenant_id": "T1001", "description": "Access to Employee totalLeaveEntitlement attribute", "natural_language": "Permission to read total leave entitlement attribute", "version": 1, "status": "draft", "created_at": "2025-06-23T09:04:52.570770", "updated_at": "2025-06-23T09:04:52.570774", "created_by": "Tarun", "updated_by": "Tarun", "permission_status": "new", "changes_detected": [], "_id": "685918b45e423f11979d4096"}, "validation_result": {"structure_errors": [], "required_field_errors": [], "data_type_errors": [], "custom_errors": []}, "dependency_errors": [], "uniqueness_result": {"is_unique": true, "status": "unique", "message": "Permission with permission_id PERM_ATTR_TOTAL_ENTITLEMENT is unique"}}], "operation": "parse_validate_mongosave", "total_permissions": 31}, "status": "success"}
{"timestamp": "2025-06-23T09:45:32.089041", "endpoint": "parse-validate-mongosave/system-permissions", "input": {"natural_language": "Tenant: Acme Corp\n\npermission_id | permission_name | permission_type | resource_identifier | actions | description | scope | natural_language | version | status\n\nPERM_ENTITY_LEAVEAPP | LeaveApplication Entity | entity | LeaveApplication | [\"create\", \"read\", \"update\", \"delete\"] | Access to LeaveApplication entity | tenant_records | Permission to access leave application entity | 1 | active\n\nPERM_ATTR_LEAVE_ID | LeaveApplication leaveId Attribute | attribute | LeaveApplication.leaveId | [\"read\"] | Access to LeaveApplication leaveId attribute | own_records | Permission to read leave ID attribute | 1 | active\n\nPERM_ATTR_EMPLOYEE_ID | LeaveApplication employeeId Attribute | attribute | LeaveApplication.employeeId | [\"create\", \"read\", \"update\"] | Access to LeaveApplication employeeId attribute | own_records | Permission to access employee ID attribute | 1 | active\n\nPERM_ATTR_START_DATE | LeaveApplication startDate Attribute | attribute | LeaveApplication.startDate | [\"create\", \"read\", \"update\"] | Access to LeaveApplication startDate attribute | own_records | Permission to access start date attribute | 1 | active", "tenant_id": "T_ACME_CORP", "edit_draft": false, "edit_temp": false, "edit_production": false}, "output": {"success": true, "permission_results": [{"success": false, "errors": ["Entity with entity_id E_LeaveApplication_1750671931 does not exist"], "parsed_data": {"permission_id": "PERM_ENTITY_LEAVEAPP", "permission_name": "LeaveApplication Entity", "permission_type": "entity", "resource_identifier": "LeaveApplication", "actions": ["create", "read", "update", "delete"], "scope": "tenant_records", "entity_id": "E_LeaveApplication_1750671931", "attribute_id": "", "go_id": "", "lo_id": "", "tenant_id": "T_ACME_CORP", "description": "Access to LeaveApplication entity", "natural_language": "Permission to access leave application entity", "version": 1, "status": "active", "created_at": "2025-06-23T09:45:31.943352", "updated_at": "2025-06-23T09:45:31.943352", "created_by": "Tarun", "updated_by": "Tarun", "permission_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Entity with entity_id E_LeaveApplication_1750671931 does not exist", "Attribute with attribute_id A_E_LeaveApplication_1750671931_leaveId_1750671931 does not exist"], "parsed_data": {"permission_id": "PERM_ATTR_LEAVE_ID", "permission_name": "LeaveApplication leaveId Attribute", "permission_type": "attribute", "resource_identifier": "LeaveApplication.leaveId", "actions": ["read"], "scope": "own_records", "entity_id": "E_LeaveApplication_1750671931", "attribute_id": "A_E_LeaveApplication_1750671931_leaveId_1750671931", "go_id": "", "lo_id": "", "tenant_id": "T_ACME_CORP", "description": "Access to LeaveApplication leaveId attribute", "natural_language": "Permission to read leave ID attribute", "version": 1, "status": "active", "created_at": "2025-06-23T09:45:31.969740", "updated_at": "2025-06-23T09:45:31.969740", "created_by": "Tarun", "updated_by": "Tarun", "permission_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Entity with entity_id E_LeaveApplication_1750671931 does not exist", "Attribute with attribute_id A_E_LeaveApplication_1750671931_employeeId_1750671931 does not exist"], "parsed_data": {"permission_id": "PERM_ATTR_EMPLOYEE_ID", "permission_name": "LeaveApplication employeeId Attribute", "permission_type": "attribute", "resource_identifier": "LeaveApplication.employeeId", "actions": ["create", "read", "update"], "scope": "own_records", "entity_id": "E_LeaveApplication_1750671931", "attribute_id": "A_E_LeaveApplication_1750671931_employeeId_1750671931", "go_id": "", "lo_id": "", "tenant_id": "T_ACME_CORP", "description": "Access to LeaveApplication employeeId attribute", "natural_language": "Permission to access employee ID attribute", "version": 1, "status": "active", "created_at": "2025-06-23T09:45:31.991064", "updated_at": "2025-06-23T09:45:31.991064", "created_by": "Tarun", "updated_by": "Tarun", "permission_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Entity with entity_id E_LeaveApplication_1750671931 does not exist", "Attribute with attribute_id A_E_LeaveApplication_1750671931_startDate_1750671932 does not exist"], "parsed_data": {"permission_id": "PERM_ATTR_START_DATE", "permission_name": "LeaveApplication startDate Attribute", "permission_type": "attribute", "resource_identifier": "LeaveApplication.startDate", "actions": ["create", "read", "update"], "scope": "own_records", "entity_id": "E_LeaveApplication_1750671931", "attribute_id": "A_E_LeaveApplication_1750671931_startDate_1750671932", "go_id": "", "lo_id": "", "tenant_id": "T_ACME_CORP", "description": "Access to LeaveApplication startDate attribute", "natural_language": "Permission to access start date attribute", "version": 1, "status": "active", "created_at": "2025-06-23T09:45:32.012131", "updated_at": "2025-06-23T09:45:32.012131", "created_by": "Tarun", "updated_by": "Tarun", "permission_status": "new", "changes_detected": []}}], "operation": "parse_validate_mongosave", "total_permissions": 4}, "status": "success"}
{"timestamp": "2025-06-23T09:52:02.503902", "endpoint": "parse-validate-mongosave/system-permissions", "input": {"natural_language": "Tenant: Acme Corp\n\npermission_id | permission_name | permission_type | resource_identifier | actions | description | scope | natural_language | version | status\n\nPERM_ENTITY_LEAVEAPP | LeaveApplication Entity | entity | LeaveApplication | [\"create\", \"read\", \"update\", \"delete\"] | Access to LeaveApplication entity | tenant_records | Permission to access leave application entity | 1 | active\n\nPERM_ATTR_LEAVE_ID | LeaveApplication leaveId Attribute | attribute | LeaveApplication.leaveId | [\"read\"] | Access to LeaveApplication leaveId attribute | own_records | Permission to read leave ID attribute | 1 | active\n\nPERM_ATTR_EMPLOYEE_ID | LeaveApplication employeeId Attribute | attribute | LeaveApplication.employeeId | [\"create\", \"read\", \"update\"] | Access to LeaveApplication employeeId attribute | own_records | Permission to access employee ID attribute | 1 | active\n\nPERM_ATTR_START_DATE | LeaveApplication startDate Attribute | attribute | LeaveApplication.startDate | [\"create\", \"read\", \"update\"] | Access to LeaveApplication startDate attribute | own_records | Permission to access start date attribute | 1 | active\n\nPERM_ATTR_END_DATE | LeaveApplication endDate Attribute | attribute | LeaveApplication.endDate | [\"create\", \"read\", \"update\"] | Access to LeaveApplication endDate attribute | own_records | Permission to access end date attribute | 1 | active\n\nPERM_ATTR_NUM_DAYS | LeaveApplication numDays Attribute | attribute | LeaveApplication.numDays | [\"read\"] | Access to LeaveApplication numDays attribute | own_records | Permission to read number of days attribute | 1 | active\n\nPERM_ATTR_REASON | LeaveApplication reason Attribute | attribute | LeaveApplication.reason | [\"create\", \"read\", \"update\"] | Access to LeaveApplication reason attribute | own_records | Permission to access reason attribute | 1 | active\n\nPERM_ATTR_LEAVE_TYPE | LeaveApplication leaveTypeName Attribute | attribute | LeaveApplication.leaveTypeName | [\"create\", \"read\", \"update\"] | Access to LeaveApplication leaveTypeName attribute | tenant_records | Permission to access leave type name attribute | 1 | active\n\nPERM_ATTR_LEAVE_SUBTYPE | LeaveApplication leaveSubTypeName Attribute | attribute | LeaveApplication.leaveSubTypeName | [\"create\", \"read\", \"update\"] | Access to LeaveApplication leaveSubTypeName attribute | tenant_records | Permission to access leave sub-type name attribute | 1 | active\n\nPERM_ATTR_STATUS | LeaveApplication status Attribute | attribute | LeaveApplication.status | [\"read\", \"update\"] | Access to LeaveApplication status attribute | department_records | Permission to access status attribute | 1 | active\n\nPERM_ATTR_REQ_DOC | LeaveApplication requiresDocumentation Attribute | attribute | LeaveApplication.requiresDocumentation | [\"read\"] | Access to LeaveApplication requiresDocumentation attribute | tenant_records | Permission to read requires documentation attribute | 1 | active\n\nPERM_ATTR_DOC_PROVIDED | LeaveApplication documentationProvided Attribute | attribute | LeaveApplication.documentationProvided | [\"create\", \"read\", \"update\"] | Access to LeaveApplication documentationProvided attribute | own_records | Permission to access documentation provided attribute | 1 | active\n\nPERM_ATTR_SUBMISSION_DATE | LeaveApplication submissionDate Attribute | attribute | LeaveApplication.submissionDate | [\"read\"] | Access to LeaveApplication submissionDate attribute | own_records | Permission to read submission date attribute | 1 | active\n\nPERM_ATTR_APPROVAL_DATE | LeaveApplication approvalDate Attribute | attribute | LeaveApplication.approvalDate | [\"read\"] | Access to LeaveApplication approvalDate attribute | department_records | Permission to read approval date attribute | 1 | active\n\nPERM_ATTR_APPROVED_BY | LeaveApplication approvedBy Attribute | attribute | LeaveApplication.approvedBy | [\"read\", \"update\"] | Access to LeaveApplication approvedBy attribute | department_records | Permission to access approved by attribute | 1 | active\n\nPERM_ATTR_COMMENTS | LeaveApplication comments Attribute | attribute | LeaveApplication.comments | [\"create\", \"read\", \"update\"] | Access to LeaveApplication comments attribute | department_records | Permission to access comments attribute | 1 | active\n\nPERM_ENTITY_EMPLOYEE | Employee Entity | entity | Employee | [\"create\", \"read\", \"update\", \"delete\"] | Access to Employee entity | department_records | Permission to access employee entity | 1 | active\n\nPERM_ATTR_EMP_ID | Employee employeeId Attribute | attribute | Employee.employeeId | [\"read\"] | Access to Employee employeeId attribute | own_records | Permission to read employee ID attribute | 1 | active\n\nPERM_ATTR_FIRST_NAME | Employee firstName Attribute | attribute | Employee.firstName | [\"create\", \"read\", \"update\"] | Access to Employee firstName attribute | own_records | Permission to access first name attribute | 1 | active\n\nPERM_ATTR_LAST_NAME | Employee lastName Attribute | attribute | Employee.lastName | [\"create\", \"read\", \"update\"] | Access to Employee lastName attribute | own_records | Permission to access last name attribute | 1 | active\n\nPERM_ATTR_EMAIL | Employee email Attribute | attribute | Employee.email | [\"create\", \"read\", \"update\"] | Access to Employee email attribute | own_records | Permission to access email attribute | 1 | active\n\nPERM_ATTR_PHONE | Employee phone Attribute | attribute | Employee.phone | [\"create\", \"read\", \"update\"] | Access to Employee phone attribute | own_records | Permission to access phone attribute | 1 | active\n\nPERM_ATTR_DEPT_ID | Employee departmentId Attribute | attribute | Employee.departmentId | [\"read\"] | Access to Employee departmentId attribute | department_records | Permission to read department ID attribute | 1 | active\n\nPERM_ATTR_MANAGER_ID | Employee managerId Attribute | attribute | Employee.managerId | [\"read\"] | Access to Employee managerId attribute | department_records | Permission to read manager ID attribute | 1 | active\n\nPERM_ATTR_JOB_TITLE | Employee jobTitle Attribute | attribute | Employee.jobTitle | [\"create\", \"read\", \"update\"] | Access to Employee jobTitle attribute | own_records | Permission to access job title attribute | 1 | active\n\nPERM_ATTR_HIRE_DATE | Employee hireDate Attribute | attribute | Employee.hireDate | [\"read\"] | Access to Employee hireDate attribute | department_records | Permission to read hire date attribute | 1 | active\n\nPERM_ATTR_EMP_STATUS | Employee employmentStatus Attribute | attribute | Employee.employmentStatus | [\"read\"] | Access to Employee employmentStatus attribute | department_records | Permission to read employment status attribute | 1 | active\n\nPERM_ATTR_ANNUAL_LEAVE | Employee annualLeaveBalance Attribute | attribute | Employee.annualLeaveBalance | [\"read\"] | Access to Employee annualLeaveBalance attribute | own_records | Permission to read annual leave balance attribute | 1 | active\n\nPERM_ATTR_SICK_LEAVE | Employee sickLeaveBalance Attribute | attribute | Employee.sickLeaveBalance | [\"read\"] | Access to Employee sickLeaveBalance attribute | own_records | Permission to read sick leave balance attribute | 1 | active\n\nPERM_ATTR_PERSONAL_LEAVE | Employee personalLeaveBalance Attribute | attribute | Employee.personalLeaveBalance | [\"read\"] | Access to Employee personalLeaveBalance attribute | own_records | Permission to read personal leave balance attribute | 1 | active\n\nPERM_ATTR_TOTAL_ENTITLEMENT | Employee totalLeaveEntitlement Attribute | attribute | Employee.totalLeaveEntitlement | [\"read\"] | Access to Employee totalLeaveEntitlement attribute | own_records | Permission to read total leave entitlement attribute | 1 | active\n", "tenant_id": "T1001", "edit_draft": true, "edit_temp": false, "edit_production": false}, "output": {"success": true, "permission_results": [{"success": false, "errors": ["Entity with entity_id E_LeaveApplication_1750672321 does not exist"], "parsed_data": {"permission_id": "PERM_ENTITY_LEAVEAPP", "permission_name": "LeaveApplication Entity", "permission_type": "entity", "resource_identifier": "LeaveApplication", "actions": ["create", "read", "update", "delete"], "scope": "tenant_records", "entity_id": "E_LeaveApplication_1750672321", "attribute_id": "", "go_id": "", "lo_id": "", "tenant_id": "T1001", "description": "Access to LeaveApplication entity", "natural_language": "Permission to access leave application entity", "version": 1, "status": "active", "created_at": "2025-06-23T09:52:01.180751", "updated_at": "2025-06-23T09:52:01.180751", "created_by": "Tarun", "updated_by": "Tarun", "permission_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Entity with entity_id E_LeaveApplication_1750672321 does not exist", "Attribute with attribute_id A_E_LeaveApplication_1750672321_leaveId_1750672321 does not exist"], "parsed_data": {"permission_id": "PERM_ATTR_LEAVE_ID", "permission_name": "LeaveApplication leaveId Attribute", "permission_type": "attribute", "resource_identifier": "LeaveApplication.leaveId", "actions": ["read"], "scope": "own_records", "entity_id": "E_LeaveApplication_1750672321", "attribute_id": "A_E_LeaveApplication_1750672321_leaveId_1750672321", "go_id": "", "lo_id": "", "tenant_id": "T1001", "description": "Access to LeaveApplication leaveId attribute", "natural_language": "Permission to read leave ID attribute", "version": 1, "status": "active", "created_at": "2025-06-23T09:52:01.209550", "updated_at": "2025-06-23T09:52:01.209550", "created_by": "Tarun", "updated_by": "Tarun", "permission_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Entity with entity_id E_LeaveApplication_1750672321 does not exist", "Attribute with attribute_id A_E_LeaveApplication_1750672321_employeeId_1750672321 does not exist"], "parsed_data": {"permission_id": "PERM_ATTR_EMPLOYEE_ID", "permission_name": "LeaveApplication employeeId Attribute", "permission_type": "attribute", "resource_identifier": "LeaveApplication.employeeId", "actions": ["create", "read", "update"], "scope": "own_records", "entity_id": "E_LeaveApplication_1750672321", "attribute_id": "A_E_LeaveApplication_1750672321_employeeId_1750672321", "go_id": "", "lo_id": "", "tenant_id": "T1001", "description": "Access to LeaveApplication employeeId attribute", "natural_language": "Permission to access employee ID attribute", "version": 1, "status": "active", "created_at": "2025-06-23T09:52:01.237410", "updated_at": "2025-06-23T09:52:01.237410", "created_by": "Tarun", "updated_by": "Tarun", "permission_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Entity with entity_id E_LeaveApplication_1750672321 does not exist", "Attribute with attribute_id A_E_LeaveApplication_1750672321_startDate_1750672321 does not exist"], "parsed_data": {"permission_id": "PERM_ATTR_START_DATE", "permission_name": "LeaveApplication startDate Attribute", "permission_type": "attribute", "resource_identifier": "LeaveApplication.startDate", "actions": ["create", "read", "update"], "scope": "own_records", "entity_id": "E_LeaveApplication_1750672321", "attribute_id": "A_E_LeaveApplication_1750672321_startDate_1750672321", "go_id": "", "lo_id": "", "tenant_id": "T1001", "description": "Access to LeaveApplication startDate attribute", "natural_language": "Permission to access start date attribute", "version": 1, "status": "active", "created_at": "2025-06-23T09:52:01.266011", "updated_at": "2025-06-23T09:52:01.266011", "created_by": "Tarun", "updated_by": "Tarun", "permission_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Entity with entity_id E_LeaveApplication_1750672321 does not exist", "Attribute with attribute_id A_E_LeaveApplication_1750672321_endDate_1750672321 does not exist"], "parsed_data": {"permission_id": "PERM_ATTR_END_DATE", "permission_name": "LeaveApplication endDate Attribute", "permission_type": "attribute", "resource_identifier": "LeaveApplication.endDate", "actions": ["create", "read", "update"], "scope": "own_records", "entity_id": "E_LeaveApplication_1750672321", "attribute_id": "A_E_LeaveApplication_1750672321_endDate_1750672321", "go_id": "", "lo_id": "", "tenant_id": "T1001", "description": "Access to LeaveApplication endDate attribute", "natural_language": "Permission to access end date attribute", "version": 1, "status": "active", "created_at": "2025-06-23T09:52:01.293498", "updated_at": "2025-06-23T09:52:01.293498", "created_by": "Tarun", "updated_by": "Tarun", "permission_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Entity with entity_id E_LeaveApplication_1750672321 does not exist", "Attribute with attribute_id A_E_LeaveApplication_1750672321_numDays_1750672321 does not exist"], "parsed_data": {"permission_id": "PERM_ATTR_NUM_DAYS", "permission_name": "LeaveApplication numDays Attribute", "permission_type": "attribute", "resource_identifier": "LeaveApplication.numDays", "actions": ["read"], "scope": "own_records", "entity_id": "E_LeaveApplication_1750672321", "attribute_id": "A_E_LeaveApplication_1750672321_numDays_1750672321", "go_id": "", "lo_id": "", "tenant_id": "T1001", "description": "Access to LeaveApplication numDays attribute", "natural_language": "Permission to read number of days attribute", "version": 1, "status": "active", "created_at": "2025-06-23T09:52:01.320508", "updated_at": "2025-06-23T09:52:01.320508", "created_by": "Tarun", "updated_by": "Tarun", "permission_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Entity with entity_id E_LeaveApplication_1750672321 does not exist", "Attribute with attribute_id A_E_LeaveApplication_1750672321_reason_1750672321 does not exist"], "parsed_data": {"permission_id": "PERM_ATTR_REASON", "permission_name": "LeaveApplication reason Attribute", "permission_type": "attribute", "resource_identifier": "LeaveApplication.reason", "actions": ["create", "read", "update"], "scope": "own_records", "entity_id": "E_LeaveApplication_1750672321", "attribute_id": "A_E_LeaveApplication_1750672321_reason_1750672321", "go_id": "", "lo_id": "", "tenant_id": "T1001", "description": "Access to LeaveApplication reason attribute", "natural_language": "Permission to access reason attribute", "version": 1, "status": "active", "created_at": "2025-06-23T09:52:01.344242", "updated_at": "2025-06-23T09:52:01.344242", "created_by": "Tarun", "updated_by": "Tarun", "permission_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Entity with entity_id E_LeaveApplication_1750672321 does not exist", "Attribute with attribute_id A_E_LeaveApplication_1750672321_leaveTypeName_1750672321 does not exist"], "parsed_data": {"permission_id": "PERM_ATTR_LEAVE_TYPE", "permission_name": "LeaveApplication leaveTypeName Attribute", "permission_type": "attribute", "resource_identifier": "LeaveApplication.leaveTypeName", "actions": ["create", "read", "update"], "scope": "tenant_records", "entity_id": "E_LeaveApplication_1750672321", "attribute_id": "A_E_LeaveApplication_1750672321_leaveTypeName_1750672321", "go_id": "", "lo_id": "", "tenant_id": "T1001", "description": "Access to LeaveApplication leaveTypeName attribute", "natural_language": "Permission to access leave type name attribute", "version": 1, "status": "active", "created_at": "2025-06-23T09:52:01.368452", "updated_at": "2025-06-23T09:52:01.368452", "created_by": "Tarun", "updated_by": "Tarun", "permission_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Entity with entity_id E_LeaveApplication_1750672321 does not exist", "Attribute with attribute_id A_E_LeaveApplication_1750672321_leaveSubTypeName_1750672321 does not exist"], "parsed_data": {"permission_id": "PERM_ATTR_LEAVE_SUBTYPE", "permission_name": "LeaveApplication leaveSubTypeName Attribute", "permission_type": "attribute", "resource_identifier": "LeaveApplication.leaveSubTypeName", "actions": ["create", "read", "update"], "scope": "tenant_records", "entity_id": "E_LeaveApplication_1750672321", "attribute_id": "A_E_LeaveApplication_1750672321_leaveSubTypeName_1750672321", "go_id": "", "lo_id": "", "tenant_id": "T1001", "description": "Access to LeaveApplication leaveSubTypeName attribute", "natural_language": "Permission to access leave sub-type name attribute", "version": 1, "status": "active", "created_at": "2025-06-23T09:52:01.392635", "updated_at": "2025-06-23T09:52:01.392635", "created_by": "Tarun", "updated_by": "Tarun", "permission_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Entity with entity_id E_LeaveApplication_1750672321 does not exist", "Attribute with attribute_id A_E_LeaveApplication_1750672321_status_1750672321 does not exist"], "parsed_data": {"permission_id": "PERM_ATTR_STATUS", "permission_name": "LeaveApplication status Attribute", "permission_type": "attribute", "resource_identifier": "LeaveApplication.status", "actions": ["read", "update"], "scope": "department_records", "entity_id": "E_LeaveApplication_1750672321", "attribute_id": "A_E_LeaveApplication_1750672321_status_1750672321", "go_id": "", "lo_id": "", "tenant_id": "T1001", "description": "Access to LeaveApplication status attribute", "natural_language": "Permission to access status attribute", "version": 1, "status": "active", "created_at": "2025-06-23T09:52:01.419703", "updated_at": "2025-06-23T09:52:01.419703", "created_by": "Tarun", "updated_by": "Tarun", "permission_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Entity with entity_id E_LeaveApplication_1750672321 does not exist", "Attribute with attribute_id A_E_LeaveApplication_1750672321_requiresDocumentation_1750672321 does not exist"], "parsed_data": {"permission_id": "PERM_ATTR_REQ_DOC", "permission_name": "LeaveApplication requiresDocumentation Attribute", "permission_type": "attribute", "resource_identifier": "LeaveApplication.requiresDocumentation", "actions": ["read"], "scope": "tenant_records", "entity_id": "E_LeaveApplication_1750672321", "attribute_id": "A_E_LeaveApplication_1750672321_requiresDocumentation_1750672321", "go_id": "", "lo_id": "", "tenant_id": "T1001", "description": "Access to LeaveApplication requiresDocumentation attribute", "natural_language": "Permission to read requires documentation attribute", "version": 1, "status": "active", "created_at": "2025-06-23T09:52:01.442872", "updated_at": "2025-06-23T09:52:01.442872", "created_by": "Tarun", "updated_by": "Tarun", "permission_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Entity with entity_id E_LeaveApplication_1750672321 does not exist", "Attribute with attribute_id A_E_LeaveApplication_1750672321_documentationProvided_1750672321 does not exist"], "parsed_data": {"permission_id": "PERM_ATTR_DOC_PROVIDED", "permission_name": "LeaveApplication documentationProvided Attribute", "permission_type": "attribute", "resource_identifier": "LeaveApplication.documentationProvided", "actions": ["create", "read", "update"], "scope": "own_records", "entity_id": "E_LeaveApplication_1750672321", "attribute_id": "A_E_LeaveApplication_1750672321_documentationProvided_1750672321", "go_id": "", "lo_id": "", "tenant_id": "T1001", "description": "Access to LeaveApplication documentationProvided attribute", "natural_language": "Permission to access documentation provided attribute", "version": 1, "status": "active", "created_at": "2025-06-23T09:52:01.463633", "updated_at": "2025-06-23T09:52:01.463633", "created_by": "Tarun", "updated_by": "Tarun", "permission_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Entity with entity_id E_LeaveApplication_1750672321 does not exist", "Attribute with attribute_id A_E_LeaveApplication_1750672321_submissionDate_1750672321 does not exist"], "parsed_data": {"permission_id": "PERM_ATTR_SUBMISSION_DATE", "permission_name": "LeaveApplication submissionDate Attribute", "permission_type": "attribute", "resource_identifier": "LeaveApplication.submissionDate", "actions": ["read"], "scope": "own_records", "entity_id": "E_LeaveApplication_1750672321", "attribute_id": "A_E_LeaveApplication_1750672321_submissionDate_1750672321", "go_id": "", "lo_id": "", "tenant_id": "T1001", "description": "Access to LeaveApplication submissionDate attribute", "natural_language": "Permission to read submission date attribute", "version": 1, "status": "active", "created_at": "2025-06-23T09:52:01.488865", "updated_at": "2025-06-23T09:52:01.488865", "created_by": "Tarun", "updated_by": "Tarun", "permission_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Entity with entity_id E_LeaveApplication_1750672321 does not exist", "Attribute with attribute_id A_E_LeaveApplication_1750672321_approvalDate_1750672321 does not exist"], "parsed_data": {"permission_id": "PERM_ATTR_APPROVAL_DATE", "permission_name": "LeaveApplication approvalDate Attribute", "permission_type": "attribute", "resource_identifier": "LeaveApplication.approvalDate", "actions": ["read"], "scope": "department_records", "entity_id": "E_LeaveApplication_1750672321", "attribute_id": "A_E_LeaveApplication_1750672321_approvalDate_1750672321", "go_id": "", "lo_id": "", "tenant_id": "T1001", "description": "Access to LeaveApplication approvalDate attribute", "natural_language": "Permission to read approval date attribute", "version": 1, "status": "active", "created_at": "2025-06-23T09:52:01.513048", "updated_at": "2025-06-23T09:52:01.513048", "created_by": "Tarun", "updated_by": "Tarun", "permission_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Entity with entity_id E_LeaveApplication_1750672321 does not exist", "Attribute with attribute_id A_E_LeaveApplication_1750672321_approvedBy_1750672321 does not exist"], "parsed_data": {"permission_id": "PERM_ATTR_APPROVED_BY", "permission_name": "LeaveApplication approvedBy Attribute", "permission_type": "attribute", "resource_identifier": "LeaveApplication.approvedBy", "actions": ["read", "update"], "scope": "department_records", "entity_id": "E_LeaveApplication_1750672321", "attribute_id": "A_E_LeaveApplication_1750672321_approvedBy_1750672321", "go_id": "", "lo_id": "", "tenant_id": "T1001", "description": "Access to LeaveApplication approvedBy attribute", "natural_language": "Permission to access approved by attribute", "version": 1, "status": "active", "created_at": "2025-06-23T09:52:01.536581", "updated_at": "2025-06-23T09:52:01.536581", "created_by": "Tarun", "updated_by": "Tarun", "permission_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Entity with entity_id E_LeaveApplication_1750672321 does not exist", "Attribute with attribute_id A_E_LeaveApplication_1750672321_comments_1750672321 does not exist"], "parsed_data": {"permission_id": "PERM_ATTR_COMMENTS", "permission_name": "LeaveApplication comments Attribute", "permission_type": "attribute", "resource_identifier": "LeaveApplication.comments", "actions": ["create", "read", "update"], "scope": "department_records", "entity_id": "E_LeaveApplication_1750672321", "attribute_id": "A_E_LeaveApplication_1750672321_comments_1750672321", "go_id": "", "lo_id": "", "tenant_id": "T1001", "description": "Access to LeaveApplication comments attribute", "natural_language": "Permission to access comments attribute", "version": 1, "status": "active", "created_at": "2025-06-23T09:52:01.562251", "updated_at": "2025-06-23T09:52:01.562251", "created_by": "Tarun", "updated_by": "Tarun", "permission_status": "new", "changes_detected": []}}, {"success": true, "saved_data": {"permission_id": "PERM_ENTITY_EMPLOYEE", "permission_name": "Employee Entity", "permission_type": "entity", "resource_identifier": "Employee", "actions": ["create", "read", "update", "delete"], "scope": "department_records", "entity_id": "E13", "attribute_id": "", "go_id": "", "lo_id": "", "tenant_id": "T1001", "description": "Access to Employee entity", "natural_language": "Permission to access employee entity", "version": 3, "status": "active", "created_at": "2025-06-23T09:52:01.573557", "updated_at": "2025-06-23T09:52:02.153700", "created_by": "Tarun", "updated_by": "Tarun", "permission_status": "existing", "changes_detected": [], "_id": "6858f78311d79f18c46b140b"}, "validation_result": {"structure_errors": [], "required_field_errors": [], "data_type_errors": [], "custom_errors": []}, "dependency_errors": [], "uniqueness_result": {"is_unique": false, "status": "exists_in_mongo", "message": "Permission with permission_id PERM_ENTITY_EMPLOYEE already exists in MongoDB drafts", "existing_document": {"_id": "6858f78311d79f18c46b140b", "permission_id": "PERM_ENTITY_EMPLOYEE", "permission_name": "Employee Entity", "permission_type": "entity", "resource_identifier": "Employee", "actions": ["create", "read", "update", "delete"], "scope": "department_records", "entity_id": "E13", "attribute_id": "", "go_id": "", "lo_id": "", "tenant_id": "T1001", "description": "Access to Employee entity", "natural_language": "Permission to access employee entity", "version": 2, "status": "active", "created_at": "2025-06-23T09:04:51.704252", "updated_at": "2025-06-23T09:04:52.306071", "created_by": "Tarun", "updated_by": "Tarun", "permission_status": "existing", "changes_detected": []}}}, {"success": false, "errors": ["Attribute with attribute_id A_E13_employeeId_1750672321 does not exist"], "parsed_data": {"permission_id": "PERM_ATTR_EMP_ID", "permission_name": "Employee employeeId Attribute", "permission_type": "attribute", "resource_identifier": "Employee.employeeId", "actions": ["read"], "scope": "own_records", "entity_id": "E13", "attribute_id": "A_E13_employeeId_1750672321", "go_id": "", "lo_id": "", "tenant_id": "T1001", "description": "Access to Employee employeeId attribute", "natural_language": "Permission to read employee ID attribute", "version": 1, "status": "active", "created_at": "2025-06-23T09:52:01.592040", "updated_at": "2025-06-23T09:52:01.592040", "created_by": "Tarun", "updated_by": "Tarun", "permission_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Attribute with attribute_id A_E13_firstName_1750672321 does not exist"], "parsed_data": {"permission_id": "PERM_ATTR_FIRST_NAME", "permission_name": "Employee firstName Attribute", "permission_type": "attribute", "resource_identifier": "Employee.firstName", "actions": ["create", "read", "update"], "scope": "own_records", "entity_id": "E13", "attribute_id": "A_E13_firstName_1750672321", "go_id": "", "lo_id": "", "tenant_id": "T1001", "description": "Access to Employee firstName attribute", "natural_language": "Permission to access first name attribute", "version": 1, "status": "active", "created_at": "2025-06-23T09:52:01.608964", "updated_at": "2025-06-23T09:52:01.608964", "created_by": "Tarun", "updated_by": "Tarun", "permission_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Attribute with attribute_id A_E13_lastName_1750672321 does not exist"], "parsed_data": {"permission_id": "PERM_ATTR_LAST_NAME", "permission_name": "Employee lastName Attribute", "permission_type": "attribute", "resource_identifier": "Employee.lastName", "actions": ["create", "read", "update"], "scope": "own_records", "entity_id": "E13", "attribute_id": "A_E13_lastName_1750672321", "go_id": "", "lo_id": "", "tenant_id": "T1001", "description": "Access to Employee lastName attribute", "natural_language": "Permission to access last name attribute", "version": 1, "status": "active", "created_at": "2025-06-23T09:52:01.624862", "updated_at": "2025-06-23T09:52:01.624862", "created_by": "Tarun", "updated_by": "Tarun", "permission_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Attribute with attribute_id A_E13_email_1750672321 does not exist"], "parsed_data": {"permission_id": "PERM_ATTR_EMAIL", "permission_name": "Employee email Attribute", "permission_type": "attribute", "resource_identifier": "Employee.email", "actions": ["create", "read", "update"], "scope": "own_records", "entity_id": "E13", "attribute_id": "A_E13_email_1750672321", "go_id": "", "lo_id": "", "tenant_id": "T1001", "description": "Access to Employee email attribute", "natural_language": "Permission to access email attribute", "version": 1, "status": "active", "created_at": "2025-06-23T09:52:01.642822", "updated_at": "2025-06-23T09:52:01.642822", "created_by": "Tarun", "updated_by": "Tarun", "permission_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Attribute with attribute_id A_E13_phone_1750672321 does not exist"], "parsed_data": {"permission_id": "PERM_ATTR_PHONE", "permission_name": "Employee phone Attribute", "permission_type": "attribute", "resource_identifier": "Employee.phone", "actions": ["create", "read", "update"], "scope": "own_records", "entity_id": "E13", "attribute_id": "A_E13_phone_1750672321", "go_id": "", "lo_id": "", "tenant_id": "T1001", "description": "Access to Employee phone attribute", "natural_language": "Permission to access phone attribute", "version": 1, "status": "active", "created_at": "2025-06-23T09:52:01.660492", "updated_at": "2025-06-23T09:52:01.660492", "created_by": "Tarun", "updated_by": "Tarun", "permission_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Attribute with attribute_id A_E13_departmentId_1750672321 does not exist"], "parsed_data": {"permission_id": "PERM_ATTR_DEPT_ID", "permission_name": "Employee departmentId Attribute", "permission_type": "attribute", "resource_identifier": "Employee.departmentId", "actions": ["read"], "scope": "department_records", "entity_id": "E13", "attribute_id": "A_E13_departmentId_1750672321", "go_id": "", "lo_id": "", "tenant_id": "T1001", "description": "Access to Employee departmentId attribute", "natural_language": "Permission to read department ID attribute", "version": 1, "status": "active", "created_at": "2025-06-23T09:52:01.677845", "updated_at": "2025-06-23T09:52:01.677845", "created_by": "Tarun", "updated_by": "Tarun", "permission_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Attribute with attribute_id A_E13_managerId_1750672321 does not exist"], "parsed_data": {"permission_id": "PERM_ATTR_MANAGER_ID", "permission_name": "Employee managerId Attribute", "permission_type": "attribute", "resource_identifier": "Employee.managerId", "actions": ["read"], "scope": "department_records", "entity_id": "E13", "attribute_id": "A_E13_managerId_1750672321", "go_id": "", "lo_id": "", "tenant_id": "T1001", "description": "Access to Employee managerId attribute", "natural_language": "Permission to read manager ID attribute", "version": 1, "status": "active", "created_at": "2025-06-23T09:52:01.694748", "updated_at": "2025-06-23T09:52:01.694748", "created_by": "Tarun", "updated_by": "Tarun", "permission_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Attribute with attribute_id A_E13_jobTitle_1750672321 does not exist"], "parsed_data": {"permission_id": "PERM_ATTR_JOB_TITLE", "permission_name": "Employee jobTitle Attribute", "permission_type": "attribute", "resource_identifier": "Employee.jobTitle", "actions": ["create", "read", "update"], "scope": "own_records", "entity_id": "E13", "attribute_id": "A_E13_jobTitle_1750672321", "go_id": "", "lo_id": "", "tenant_id": "T1001", "description": "Access to Employee jobTitle attribute", "natural_language": "Permission to access job title attribute", "version": 1, "status": "active", "created_at": "2025-06-23T09:52:01.711870", "updated_at": "2025-06-23T09:52:01.711870", "created_by": "Tarun", "updated_by": "Tarun", "permission_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Attribute with attribute_id A_E13_hireDate_1750672321 does not exist"], "parsed_data": {"permission_id": "PERM_ATTR_HIRE_DATE", "permission_name": "Employee hireDate Attribute", "permission_type": "attribute", "resource_identifier": "Employee.hireDate", "actions": ["read"], "scope": "department_records", "entity_id": "E13", "attribute_id": "A_E13_hireDate_1750672321", "go_id": "", "lo_id": "", "tenant_id": "T1001", "description": "Access to Employee hireDate attribute", "natural_language": "Permission to read hire date attribute", "version": 1, "status": "active", "created_at": "2025-06-23T09:52:01.729313", "updated_at": "2025-06-23T09:52:01.729313", "created_by": "Tarun", "updated_by": "Tarun", "permission_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Attribute with attribute_id A_E13_employmentStatus_1750672321 does not exist"], "parsed_data": {"permission_id": "PERM_ATTR_EMP_STATUS", "permission_name": "Employee employmentStatus Attribute", "permission_type": "attribute", "resource_identifier": "Employee.employmentStatus", "actions": ["read"], "scope": "department_records", "entity_id": "E13", "attribute_id": "A_E13_employmentStatus_1750672321", "go_id": "", "lo_id": "", "tenant_id": "T1001", "description": "Access to Employee employmentStatus attribute", "natural_language": "Permission to read employment status attribute", "version": 1, "status": "active", "created_at": "2025-06-23T09:52:01.744837", "updated_at": "2025-06-23T09:52:01.744837", "created_by": "Tarun", "updated_by": "Tarun", "permission_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Attribute with attribute_id A_E13_annualLeaveBalance_1750672321 does not exist"], "parsed_data": {"permission_id": "PERM_ATTR_ANNUAL_LEAVE", "permission_name": "Employee annualLeaveBalance Attribute", "permission_type": "attribute", "resource_identifier": "Employee.annualLeaveBalance", "actions": ["read"], "scope": "own_records", "entity_id": "E13", "attribute_id": "A_E13_annualLeaveBalance_1750672321", "go_id": "", "lo_id": "", "tenant_id": "T1001", "description": "Access to Employee annualLeaveBalance attribute", "natural_language": "Permission to read annual leave balance attribute", "version": 1, "status": "active", "created_at": "2025-06-23T09:52:01.759322", "updated_at": "2025-06-23T09:52:01.759322", "created_by": "Tarun", "updated_by": "Tarun", "permission_status": "new", "changes_detected": []}}, {"success": true, "saved_data": {"permission_id": "PERM_ATTR_SICK_LEAVE", "permission_name": "Employee sickLeaveBalance Attribute", "permission_type": "attribute", "resource_identifier": "Employee.sickLeaveBalance", "actions": ["read"], "scope": "own_records", "entity_id": "E13", "attribute_id": "E13.At9", "go_id": "", "lo_id": "", "tenant_id": "T1001", "description": "Access to Employee sickLeaveBalance attribute", "natural_language": "Permission to read sick leave balance attribute", "version": 2, "status": "active", "created_at": "2025-06-23T09:52:01.769872", "updated_at": "2025-06-23T09:52:02.444787", "created_by": "Tarun", "updated_by": "Tarun", "permission_status": "existing", "changes_detected": [], "_id": "685918b45e423f11979d4094"}, "validation_result": {"structure_errors": [], "required_field_errors": [], "data_type_errors": [], "custom_errors": []}, "dependency_errors": [], "uniqueness_result": {"is_unique": false, "status": "exists_in_mongo", "message": "Permission with permission_id PERM_ATTR_SICK_LEAVE already exists in MongoDB drafts", "existing_document": {"_id": "685918b45e423f11979d4094", "permission_id": "PERM_ATTR_SICK_LEAVE", "permission_name": "Employee sickLeaveBalance Attribute", "permission_type": "attribute", "resource_identifier": "Employee.sickLeaveBalance", "actions": ["read"], "scope": "own_records", "entity_id": "E13", "attribute_id": "E13.At9", "go_id": "", "lo_id": "", "tenant_id": "T1001", "description": "Access to Employee sickLeaveBalance attribute", "natural_language": "Permission to read sick leave balance attribute", "version": 1, "status": "draft", "created_at": "2025-06-23T09:04:52.529883", "updated_at": "2025-06-23T09:04:52.529888", "created_by": "Tarun", "updated_by": "Tarun", "permission_status": "new", "changes_detected": []}}}, {"success": true, "saved_data": {"permission_id": "PERM_ATTR_PERSONAL_LEAVE", "permission_name": "Employee personalLeaveBalance Attribute", "permission_type": "attribute", "resource_identifier": "Employee.personalLeaveBalance", "actions": ["read"], "scope": "own_records", "entity_id": "E13", "attribute_id": "E13.At10", "go_id": "", "lo_id": "", "tenant_id": "T1001", "description": "Access to Employee personalLeaveBalance attribute", "natural_language": "Permission to read personal leave balance attribute", "version": 2, "status": "active", "created_at": "2025-06-23T09:52:01.779526", "updated_at": "2025-06-23T09:52:02.474658", "created_by": "Tarun", "updated_by": "Tarun", "permission_status": "existing", "changes_detected": [], "_id": "685918b45e423f11979d4095"}, "validation_result": {"structure_errors": [], "required_field_errors": [], "data_type_errors": [], "custom_errors": []}, "dependency_errors": [], "uniqueness_result": {"is_unique": false, "status": "exists_in_mongo", "message": "Permission with permission_id PERM_ATTR_PERSONAL_LEAVE already exists in MongoDB drafts", "existing_document": {"_id": "685918b45e423f11979d4095", "permission_id": "PERM_ATTR_PERSONAL_LEAVE", "permission_name": "Employee personalLeaveBalance Attribute", "permission_type": "attribute", "resource_identifier": "Employee.personalLeaveBalance", "actions": ["read"], "scope": "own_records", "entity_id": "E13", "attribute_id": "E13.At10", "go_id": "", "lo_id": "", "tenant_id": "T1001", "description": "Access to Employee personalLeaveBalance attribute", "natural_language": "Permission to read personal leave balance attribute", "version": 1, "status": "draft", "created_at": "2025-06-23T09:04:52.551828", "updated_at": "2025-06-23T09:04:52.551832", "created_by": "Tarun", "updated_by": "Tarun", "permission_status": "new", "changes_detected": []}}}, {"success": true, "saved_data": {"permission_id": "PERM_ATTR_TOTAL_ENTITLEMENT", "permission_name": "Employee totalLeaveEntitlement Attribute", "permission_type": "attribute", "resource_identifier": "Employee.totalLeaveEntitlement", "actions": ["read"], "scope": "own_records", "entity_id": "E13", "attribute_id": "E13.At11", "go_id": "", "lo_id": "", "tenant_id": "T1001", "description": "Access to Employee totalLeaveEntitlement attribute", "natural_language": "Permission to read total leave entitlement attribute", "version": 2, "status": "active", "created_at": "2025-06-23T09:52:01.788299", "updated_at": "2025-06-23T09:52:02.501559", "created_by": "Tarun", "updated_by": "Tarun", "permission_status": "existing", "changes_detected": [], "_id": "685918b45e423f11979d4096"}, "validation_result": {"structure_errors": [], "required_field_errors": [], "data_type_errors": [], "custom_errors": []}, "dependency_errors": [], "uniqueness_result": {"is_unique": false, "status": "exists_in_mongo", "message": "Permission with permission_id PERM_ATTR_TOTAL_ENTITLEMENT already exists in MongoDB drafts", "existing_document": {"_id": "685918b45e423f11979d4096", "permission_id": "PERM_ATTR_TOTAL_ENTITLEMENT", "permission_name": "Employee totalLeaveEntitlement Attribute", "permission_type": "attribute", "resource_identifier": "Employee.totalLeaveEntitlement", "actions": ["read"], "scope": "own_records", "entity_id": "E13", "attribute_id": "E13.At11", "go_id": "", "lo_id": "", "tenant_id": "T1001", "description": "Access to Employee totalLeaveEntitlement attribute", "natural_language": "Permission to read total leave entitlement attribute", "version": 1, "status": "draft", "created_at": "2025-06-23T09:04:52.570770", "updated_at": "2025-06-23T09:04:52.570774", "created_by": "Tarun", "updated_by": "Tarun", "permission_status": "new", "changes_detected": []}}}], "operation": "parse_validate_mongosave", "total_permissions": 31}, "status": "success"}
{"timestamp": "2025-06-23T09:53:08.957768", "endpoint": "parse-validate-mongosave/system-permissions", "input": {"natural_language": "Tenant: T1001\n\npermission_id | permission_name | permission_type | resource_identifier | actions | description | scope | natural_language | version | status\n\nPERM_ATTR_TOTAL_ENTITLEMENT | Employee totalLeaveEntitlement Attribute | attribute | Employee.totalLeaveEntitlement | [\"read\"] | Access to Employee totalLeaveEntitlement attribute | own_records | Permission to read total leave entitlement attribute | 1 | active\nPERM_ATTR_SICK_LEAVE | Employee sickLeaveBalance Attribute | attribute | Employee.sickLeaveBalance | [\"read\"] | Access to Employee sickLeaveBalance attribute | own_records | Permission to read sick leave balance attribute | 1 | active", "tenant_id": "T1001", "edit_draft": true, "edit_temp": false, "edit_production": false}, "output": {"success": true, "permission_results": [{"success": true, "saved_data": {"permission_id": "PERM_ATTR_TOTAL_ENTITLEMENT", "permission_name": "Employee totalLeaveEntitlement Attribute", "permission_type": "attribute", "resource_identifier": "Employee.totalLeaveEntitlement", "actions": ["read"], "scope": "own_records", "entity_id": "E13", "attribute_id": "E13.At11", "go_id": "", "lo_id": "", "tenant_id": "T1001", "description": "Access to Employee totalLeaveEntitlement attribute", "natural_language": "Permission to read total leave entitlement attribute", "version": 3, "status": "active", "created_at": "2025-06-23T09:53:08.902296", "updated_at": "2025-06-23T09:53:08.935931", "created_by": "Tarun", "updated_by": "Tarun", "permission_status": "existing", "changes_detected": [], "_id": "685918b45e423f11979d4096"}, "validation_result": {"structure_errors": [], "required_field_errors": [], "data_type_errors": [], "custom_errors": []}, "dependency_errors": [], "uniqueness_result": {"is_unique": false, "status": "exists_in_mongo", "message": "Permission with permission_id PERM_ATTR_TOTAL_ENTITLEMENT already exists in MongoDB drafts", "existing_document": {"_id": "685918b45e423f11979d4096", "permission_id": "PERM_ATTR_TOTAL_ENTITLEMENT", "permission_name": "Employee totalLeaveEntitlement Attribute", "permission_type": "attribute", "resource_identifier": "Employee.totalLeaveEntitlement", "actions": ["read"], "scope": "own_records", "entity_id": "E13", "attribute_id": "E13.At11", "go_id": "", "lo_id": "", "tenant_id": "T1001", "description": "Access to Employee totalLeaveEntitlement attribute", "natural_language": "Permission to read total leave entitlement attribute", "version": 2, "status": "active", "created_at": "2025-06-23T09:52:01.788299", "updated_at": "2025-06-23T09:52:02.501559", "created_by": "Tarun", "updated_by": "Tarun", "permission_status": "existing", "changes_detected": []}}}, {"success": true, "saved_data": {"permission_id": "PERM_ATTR_SICK_LEAVE", "permission_name": "Employee sickLeaveBalance Attribute", "permission_type": "attribute", "resource_identifier": "Employee.sickLeaveBalance", "actions": ["read"], "scope": "own_records", "entity_id": "E13", "attribute_id": "E13.At9", "go_id": "", "lo_id": "", "tenant_id": "T1001", "description": "Access to Employee sickLeaveBalance attribute", "natural_language": "Permission to read sick leave balance attribute", "version": 3, "status": "active", "created_at": "2025-06-23T09:53:08.913361", "updated_at": "2025-06-23T09:53:08.956773", "created_by": "Tarun", "updated_by": "Tarun", "permission_status": "existing", "changes_detected": [], "_id": "685918b45e423f11979d4094"}, "validation_result": {"structure_errors": [], "required_field_errors": [], "data_type_errors": [], "custom_errors": []}, "dependency_errors": [], "uniqueness_result": {"is_unique": false, "status": "exists_in_mongo", "message": "Permission with permission_id PERM_ATTR_SICK_LEAVE already exists in MongoDB drafts", "existing_document": {"_id": "685918b45e423f11979d4094", "permission_id": "PERM_ATTR_SICK_LEAVE", "permission_name": "Employee sickLeaveBalance Attribute", "permission_type": "attribute", "resource_identifier": "Employee.sickLeaveBalance", "actions": ["read"], "scope": "own_records", "entity_id": "E13", "attribute_id": "E13.At9", "go_id": "", "lo_id": "", "tenant_id": "T1001", "description": "Access to Employee sickLeaveBalance attribute", "natural_language": "Permission to read sick leave balance attribute", "version": 2, "status": "active", "created_at": "2025-06-23T09:52:01.769872", "updated_at": "2025-06-23T09:52:02.444787", "created_by": "Tarun", "updated_by": "Tarun", "permission_status": "existing", "changes_detected": []}}}], "operation": "parse_validate_mongosave", "total_permissions": 2}, "status": "success"}
{"timestamp": "2025-06-23T09:53:36.861259", "endpoint": "parse-validate-mongosave/system-permissions", "input": {"natural_language": "Tenant: T1001\n\npermission_id | permission_name | permission_type | resource_identifier | actions | description | scope | natural_language | version | status\n\nPERM_ATTR_TOTAL_ENTITLEMENT | Employee totalLeaveEntitlement Attribute | attribute | Employee.totalLeaveEntitlement | [\"read\"] | Access to Employee totalLeaveEntitlement attribute | own_records | Permission to read total leave entitlement attribute | 1 | active", "tenant_id": "T1001", "edit_draft": true, "edit_temp": false, "edit_production": false}, "output": {"success": true, "permission_results": [{"success": true, "saved_data": {"permission_id": "PERM_ATTR_TOTAL_ENTITLEMENT", "permission_name": "Employee totalLeaveEntitlement Attribute", "permission_type": "attribute", "resource_identifier": "Employee.totalLeaveEntitlement", "actions": ["read"], "scope": "own_records", "entity_id": "E13", "attribute_id": "E13.At11", "go_id": "", "lo_id": "", "tenant_id": "T1001", "description": "Access to Employee totalLeaveEntitlement attribute", "natural_language": "Permission to read total leave entitlement attribute", "version": 4, "status": "active", "created_at": "2025-06-23T09:53:36.835115", "updated_at": "2025-06-23T09:53:36.859667", "created_by": "Tarun", "updated_by": "Tarun", "permission_status": "existing", "changes_detected": [], "_id": "685918b45e423f11979d4096"}, "validation_result": {"structure_errors": [], "required_field_errors": [], "data_type_errors": [], "custom_errors": []}, "dependency_errors": [], "uniqueness_result": {"is_unique": false, "status": "exists_in_mongo", "message": "Permission with permission_id PERM_ATTR_TOTAL_ENTITLEMENT already exists in MongoDB drafts", "existing_document": {"_id": "685918b45e423f11979d4096", "permission_id": "PERM_ATTR_TOTAL_ENTITLEMENT", "permission_name": "Employee totalLeaveEntitlement Attribute", "permission_type": "attribute", "resource_identifier": "Employee.totalLeaveEntitlement", "actions": ["read"], "scope": "own_records", "entity_id": "E13", "attribute_id": "E13.At11", "go_id": "", "lo_id": "", "tenant_id": "T1001", "description": "Access to Employee totalLeaveEntitlement attribute", "natural_language": "Permission to read total leave entitlement attribute", "version": 3, "status": "active", "created_at": "2025-06-23T09:53:08.902296", "updated_at": "2025-06-23T09:53:08.935931", "created_by": "Tarun", "updated_by": "Tarun", "permission_status": "existing", "changes_detected": []}}}], "operation": "parse_validate_mongosave", "total_permissions": 1}, "status": "success"}
{"timestamp": "2025-06-23T09:54:03.951151", "endpoint": "parse-validate-mongosave/system-permissions", "input": {"natural_language": "Tenant: T1001\n\npermission_id | permission_name | permission_type | resource_identifier | actions | description | scope | natural_language | version | status\n\nPERM_ATTR_TOTAL_ENTITLEMENT | Employee totalLeaveEntitlement Attribute | attribute | Employee.totalLeaveEntitlement | [\"read\"] | Access to Employee totalLeaveEntitlement attribute | own_records | Permission to read total leave entitlement attribute | 1 | active", "tenant_id": "T1001", "edit_draft": true, "edit_temp": false, "edit_production": false}, "output": {"success": true, "permission_results": [{"success": true, "saved_data": {"permission_id": "PERM_ATTR_TOTAL_ENTITLEMENT", "permission_name": "Employee totalLeaveEntitlement Attribute", "permission_type": "attribute", "resource_identifier": "Employee.totalLeaveEntitlement", "actions": ["read"], "scope": "own_records", "entity_id": "E13", "attribute_id": "E13.At11", "go_id": "", "lo_id": "", "tenant_id": "T1001", "description": "Access to Employee totalLeaveEntitlement attribute", "natural_language": "Permission to read total leave entitlement attribute", "version": 5, "status": "active", "created_at": "2025-06-23T09:54:03.926234", "updated_at": "2025-06-23T09:54:03.949344", "created_by": "Tarun", "updated_by": "Tarun", "permission_status": "existing", "changes_detected": [], "_id": "685918b45e423f11979d4096"}, "validation_result": {"structure_errors": [], "required_field_errors": [], "data_type_errors": [], "custom_errors": []}, "dependency_errors": [], "uniqueness_result": {"is_unique": false, "status": "exists_in_mongo", "message": "Permission with permission_id PERM_ATTR_TOTAL_ENTITLEMENT already exists in MongoDB drafts", "existing_document": {"_id": "685918b45e423f11979d4096", "permission_id": "PERM_ATTR_TOTAL_ENTITLEMENT", "permission_name": "Employee totalLeaveEntitlement Attribute", "permission_type": "attribute", "resource_identifier": "Employee.totalLeaveEntitlement", "actions": ["read"], "scope": "own_records", "entity_id": "E13", "attribute_id": "E13.At11", "go_id": "", "lo_id": "", "tenant_id": "T1001", "description": "Access to Employee totalLeaveEntitlement attribute", "natural_language": "Permission to read total leave entitlement attribute", "version": 4, "status": "active", "created_at": "2025-06-23T09:53:36.835115", "updated_at": "2025-06-23T09:53:36.859667", "created_by": "Tarun", "updated_by": "Tarun", "permission_status": "existing", "changes_detected": []}}}], "operation": "parse_validate_mongosave", "total_permissions": 1}, "status": "success"}
{"timestamp": "2025-06-23T09:54:52.010651", "endpoint": "parse-validate-mongosave/system-permissions", "input": {"natural_language": "Tenant: T1001\n\npermission_id | permission_name | permission_type | resource_identifier | actions | description | scope | natural_language | version | status\n\nPERM_ATTR_SICK_LEAVE | Employee sickLeaveBalance Attribute | attribute | Employee.sickLeaveBalance | [\"read\"] | Access to Employee sickLeaveBalance attribute | own_records | Permission to read sick leave balance attribute | 1 | active\nPERM_ATTR_PERSONAL_LEAVE | Employee personalLeaveBalance Attribute | attribute | Employee.personalLeaveBalance | [\"read\"] | Access to Employee personalLeaveBalance attribute | own_records | Permission to read personal leave balance attribute | 1 | active", "tenant_id": "T1001", "edit_draft": true, "edit_temp": false, "edit_production": false}, "output": {"success": true, "permission_results": [{"success": true, "saved_data": {"permission_id": "PERM_ATTR_SICK_LEAVE", "permission_name": "Employee sickLeaveBalance Attribute", "permission_type": "attribute", "resource_identifier": "Employee.sickLeaveBalance", "actions": ["read"], "scope": "own_records", "entity_id": "E13", "attribute_id": "E13.At9", "go_id": "", "lo_id": "", "tenant_id": "T1001", "description": "Access to Employee sickLeaveBalance attribute", "natural_language": "Permission to read sick leave balance attribute", "version": 4, "status": "active", "created_at": "2025-06-23T09:54:51.941521", "updated_at": "2025-06-23T09:54:51.982928", "created_by": "Tarun", "updated_by": "Tarun", "permission_status": "existing", "changes_detected": [], "_id": "685918b45e423f11979d4094"}, "validation_result": {"structure_errors": [], "required_field_errors": [], "data_type_errors": [], "custom_errors": []}, "dependency_errors": [], "uniqueness_result": {"is_unique": false, "status": "exists_in_mongo", "message": "Permission with permission_id PERM_ATTR_SICK_LEAVE already exists in MongoDB drafts", "existing_document": {"_id": "685918b45e423f11979d4094", "permission_id": "PERM_ATTR_SICK_LEAVE", "permission_name": "Employee sickLeaveBalance Attribute", "permission_type": "attribute", "resource_identifier": "Employee.sickLeaveBalance", "actions": ["read"], "scope": "own_records", "entity_id": "E13", "attribute_id": "E13.At9", "go_id": "", "lo_id": "", "tenant_id": "T1001", "description": "Access to Employee sickLeaveBalance attribute", "natural_language": "Permission to read sick leave balance attribute", "version": 3, "status": "active", "created_at": "2025-06-23T09:53:08.913361", "updated_at": "2025-06-23T09:53:08.956773", "created_by": "Tarun", "updated_by": "Tarun", "permission_status": "existing", "changes_detected": []}}}, {"success": true, "saved_data": {"permission_id": "PERM_ATTR_PERSONAL_LEAVE", "permission_name": "Employee personalLeaveBalance Attribute", "permission_type": "attribute", "resource_identifier": "Employee.personalLeaveBalance", "actions": ["read"], "scope": "own_records", "entity_id": "E13", "attribute_id": "E13.At10", "go_id": "", "lo_id": "", "tenant_id": "T1001", "description": "Access to Employee personalLeaveBalance attribute", "natural_language": "Permission to read personal leave balance attribute", "version": 3, "status": "active", "created_at": "2025-06-23T09:54:51.954343", "updated_at": "2025-06-23T09:54:52.008742", "created_by": "Tarun", "updated_by": "Tarun", "permission_status": "existing", "changes_detected": [], "_id": "685918b45e423f11979d4095"}, "validation_result": {"structure_errors": [], "required_field_errors": [], "data_type_errors": [], "custom_errors": []}, "dependency_errors": [], "uniqueness_result": {"is_unique": false, "status": "exists_in_mongo", "message": "Permission with permission_id PERM_ATTR_PERSONAL_LEAVE already exists in MongoDB drafts", "existing_document": {"_id": "685918b45e423f11979d4095", "permission_id": "PERM_ATTR_PERSONAL_LEAVE", "permission_name": "Employee personalLeaveBalance Attribute", "permission_type": "attribute", "resource_identifier": "Employee.personalLeaveBalance", "actions": ["read"], "scope": "own_records", "entity_id": "E13", "attribute_id": "E13.At10", "go_id": "", "lo_id": "", "tenant_id": "T1001", "description": "Access to Employee personalLeaveBalance attribute", "natural_language": "Permission to read personal leave balance attribute", "version": 2, "status": "active", "created_at": "2025-06-23T09:52:01.779526", "updated_at": "2025-06-23T09:52:02.474658", "created_by": "Tarun", "updated_by": "Tarun", "permission_status": "existing", "changes_detected": []}}}], "operation": "parse_validate_mongosave", "total_permissions": 2}, "status": "success"}
{"timestamp": "2025-06-23T09:56:17.717324", "endpoint": "parse-validate-mongosave/system-permissions", "input": {"natural_language": "Tenant: Acme Corp\n\npermission_id | permission_name | permission_type | resource_identifier | actions | description | scope | natural_language | version | status\n\nPERM_ENTITY_LEAVEAPP | LeaveApplication Entity | entity | LeaveApplication | [\"create\", \"read\", \"update\", \"delete\"] | Access to LeaveApplication entity | tenant_records | Permission to access leave application entity | 1 | active\n\nPERM_ATTR_LEAVE_ID | LeaveApplication leaveId Attribute | attribute | LeaveApplication.leaveId | [\"read\"] | Access to LeaveApplication leaveId attribute | own_records | Permission to read leave ID attribute | 1 | active\n\nPERM_ATTR_EMPLOYEE_ID | LeaveApplication employeeId Attribute | attribute | LeaveApplication.employeeId | [\"create\", \"read\", \"update\"] | Access to LeaveApplication employeeId attribute | own_records | Permission to access employee ID attribute | 1 | active\n\nPERM_ATTR_START_DATE | LeaveApplication startDate Attribute | attribute | LeaveApplication.startDate | [\"create\", \"read\", \"update\"] | Access to LeaveApplication startDate attribute | own_records | Permission to access start date attribute | 1 | active", "tenant_id": "T_ACME_CORP", "edit_draft": false, "edit_temp": false, "edit_production": false}, "output": {"success": true, "permission_results": [{"success": false, "errors": ["Entity with entity_id E_LeaveApplication_1750672577 does not exist"], "parsed_data": {"permission_id": "PERM_ENTITY_LEAVEAPP", "permission_name": "LeaveApplication Entity", "permission_type": "entity", "resource_identifier": "LeaveApplication", "actions": ["create", "read", "update", "delete"], "scope": "tenant_records", "entity_id": "E_LeaveApplication_1750672577", "attribute_id": "", "go_id": "", "lo_id": "", "tenant_id": "T_ACME_CORP", "description": "Access to LeaveApplication entity", "natural_language": "Permission to access leave application entity", "version": 1, "status": "active", "created_at": "2025-06-23T09:56:17.554614", "updated_at": "2025-06-23T09:56:17.554614", "created_by": "Tarun", "updated_by": "Tarun", "permission_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Entity with entity_id E_LeaveApplication_1750672577 does not exist", "Attribute with attribute_id A_E_LeaveApplication_1750672577_leaveId_1750672577 does not exist"], "parsed_data": {"permission_id": "PERM_ATTR_LEAVE_ID", "permission_name": "LeaveApplication leaveId Attribute", "permission_type": "attribute", "resource_identifier": "LeaveApplication.leaveId", "actions": ["read"], "scope": "own_records", "entity_id": "E_LeaveApplication_1750672577", "attribute_id": "A_E_LeaveApplication_1750672577_leaveId_1750672577", "go_id": "", "lo_id": "", "tenant_id": "T_ACME_CORP", "description": "Access to LeaveApplication leaveId attribute", "natural_language": "Permission to read leave ID attribute", "version": 1, "status": "active", "created_at": "2025-06-23T09:56:17.585093", "updated_at": "2025-06-23T09:56:17.585093", "created_by": "Tarun", "updated_by": "Tarun", "permission_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Entity with entity_id E_LeaveApplication_1750672577 does not exist", "Attribute with attribute_id A_E_LeaveApplication_1750672577_employeeId_1750672577 does not exist"], "parsed_data": {"permission_id": "PERM_ATTR_EMPLOYEE_ID", "permission_name": "LeaveApplication employeeId Attribute", "permission_type": "attribute", "resource_identifier": "LeaveApplication.employeeId", "actions": ["create", "read", "update"], "scope": "own_records", "entity_id": "E_LeaveApplication_1750672577", "attribute_id": "A_E_LeaveApplication_1750672577_employeeId_1750672577", "go_id": "", "lo_id": "", "tenant_id": "T_ACME_CORP", "description": "Access to LeaveApplication employeeId attribute", "natural_language": "Permission to access employee ID attribute", "version": 1, "status": "active", "created_at": "2025-06-23T09:56:17.612097", "updated_at": "2025-06-23T09:56:17.612097", "created_by": "Tarun", "updated_by": "Tarun", "permission_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Entity with entity_id E_LeaveApplication_1750672577 does not exist", "Attribute with attribute_id A_E_LeaveApplication_1750672577_startDate_1750672577 does not exist"], "parsed_data": {"permission_id": "PERM_ATTR_START_DATE", "permission_name": "LeaveApplication startDate Attribute", "permission_type": "attribute", "resource_identifier": "LeaveApplication.startDate", "actions": ["create", "read", "update"], "scope": "own_records", "entity_id": "E_LeaveApplication_1750672577", "attribute_id": "A_E_LeaveApplication_1750672577_startDate_1750672577", "go_id": "", "lo_id": "", "tenant_id": "T_ACME_CORP", "description": "Access to LeaveApplication startDate attribute", "natural_language": "Permission to access start date attribute", "version": 1, "status": "active", "created_at": "2025-06-23T09:56:17.634973", "updated_at": "2025-06-23T09:56:17.634973", "created_by": "Tarun", "updated_by": "Tarun", "permission_status": "new", "changes_detected": []}}], "operation": "parse_validate_mongosave", "total_permissions": 4}, "status": "success"}
{"timestamp": "2025-06-23T09:57:19.971269", "endpoint": "parse-validate-mongosave/system-permissions", "input": {"natural_language": "Tenant: Acme Corp\n\npermission_id | permission_name | permission_type | resource_identifier | actions | description | scope | natural_language | version | status\n\nPERM_ENTITY_LEAVEAPP | LeaveApplication Entity | entity | LeaveApplication | [\"create\", \"read\", \"update\", \"delete\"] | Access to LeaveApplication entity | tenant_records | Permission to access leave application entity | 1 | active\n\nPERM_ATTR_LEAVE_ID | LeaveApplication leaveId Attribute | attribute | LeaveApplication.leaveId | [\"read\"] | Access to LeaveApplication leaveId attribute | own_records | Permission to read leave ID attribute | 1 | active\n\nPERM_ATTR_EMPLOYEE_ID | LeaveApplication employeeId Attribute | attribute | LeaveApplication.employeeId | [\"create\", \"read\", \"update\"] | Access to LeaveApplication employeeId attribute | own_records | Permission to access employee ID attribute | 1 | active\n\nPERM_ATTR_START_DATE | LeaveApplication startDate Attribute | attribute | LeaveApplication.startDate | [\"create\", \"read\", \"update\"] | Access to LeaveApplication startDate attribute | own_records | Permission to access start date attribute | 1 | active", "tenant_id": "T_ACME_CORP", "edit_draft": false, "edit_temp": false, "edit_production": false}, "output": {"success": true, "permission_results": [{"success": false, "errors": ["Entity with entity_id E_LeaveApplication_1750672639 does not exist"], "parsed_data": {"permission_id": "PERM_ENTITY_LEAVEAPP", "permission_name": "LeaveApplication Entity", "permission_type": "entity", "resource_identifier": "LeaveApplication", "actions": ["create", "read", "update", "delete"], "scope": "tenant_records", "entity_id": "E_LeaveApplication_1750672639", "attribute_id": "", "go_id": "", "lo_id": "", "tenant_id": "T_ACME_CORP", "description": "Access to LeaveApplication entity", "natural_language": "Permission to access leave application entity", "version": 1, "status": "active", "created_at": "2025-06-23T09:57:19.822249", "updated_at": "2025-06-23T09:57:19.822249", "created_by": "Tarun", "updated_by": "Tarun", "permission_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Entity with entity_id E_LeaveApplication_1750672639 does not exist", "Attribute with attribute_id A_E_LeaveApplication_1750672639_leaveId_1750672639 does not exist"], "parsed_data": {"permission_id": "PERM_ATTR_LEAVE_ID", "permission_name": "LeaveApplication leaveId Attribute", "permission_type": "attribute", "resource_identifier": "LeaveApplication.leaveId", "actions": ["read"], "scope": "own_records", "entity_id": "E_LeaveApplication_1750672639", "attribute_id": "A_E_LeaveApplication_1750672639_leaveId_1750672639", "go_id": "", "lo_id": "", "tenant_id": "T_ACME_CORP", "description": "Access to LeaveApplication leaveId attribute", "natural_language": "Permission to read leave ID attribute", "version": 1, "status": "active", "created_at": "2025-06-23T09:57:19.844861", "updated_at": "2025-06-23T09:57:19.844861", "created_by": "Tarun", "updated_by": "Tarun", "permission_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Entity with entity_id E_LeaveApplication_1750672639 does not exist", "Attribute with attribute_id A_E_LeaveApplication_1750672639_employeeId_1750672639 does not exist"], "parsed_data": {"permission_id": "PERM_ATTR_EMPLOYEE_ID", "permission_name": "LeaveApplication employeeId Attribute", "permission_type": "attribute", "resource_identifier": "LeaveApplication.employeeId", "actions": ["create", "read", "update"], "scope": "own_records", "entity_id": "E_LeaveApplication_1750672639", "attribute_id": "A_E_LeaveApplication_1750672639_employeeId_1750672639", "go_id": "", "lo_id": "", "tenant_id": "T_ACME_CORP", "description": "Access to LeaveApplication employeeId attribute", "natural_language": "Permission to access employee ID attribute", "version": 1, "status": "active", "created_at": "2025-06-23T09:57:19.866346", "updated_at": "2025-06-23T09:57:19.866346", "created_by": "Tarun", "updated_by": "Tarun", "permission_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Entity with entity_id E_LeaveApplication_1750672639 does not exist", "Attribute with attribute_id A_E_LeaveApplication_1750672639_startDate_1750672639 does not exist"], "parsed_data": {"permission_id": "PERM_ATTR_START_DATE", "permission_name": "LeaveApplication startDate Attribute", "permission_type": "attribute", "resource_identifier": "LeaveApplication.startDate", "actions": ["create", "read", "update"], "scope": "own_records", "entity_id": "E_LeaveApplication_1750672639", "attribute_id": "A_E_LeaveApplication_1750672639_startDate_1750672639", "go_id": "", "lo_id": "", "tenant_id": "T_ACME_CORP", "description": "Access to LeaveApplication startDate attribute", "natural_language": "Permission to access start date attribute", "version": 1, "status": "active", "created_at": "2025-06-23T09:57:19.888152", "updated_at": "2025-06-23T09:57:19.888152", "created_by": "Tarun", "updated_by": "Tarun", "permission_status": "new", "changes_detected": []}}], "operation": "parse_validate_mongosave", "total_permissions": 4}, "status": "success"}
{"timestamp": "2025-06-23T09:58:14.994499", "endpoint": "parse-validate-mongosave/system-permissions", "input": {"natural_language": "Tenant: Acme Corp\n\npermission_id | permission_name | permission_type | resource_identifier | actions | description | scope | natural_language | version | status\n\nPERM_ATTR_TOTAL_ENTITLEMENT | Employee totalLeaveEntitlement Attribute | attribute | Employee.totalLeaveEntitlement | [\"read\"] | Access to Employee totalLeaveEntitlement attribute | own_records | Permission to read total leave entitlement attribute | 1 | active", "tenant_id": "T_ACME_CORP", "edit_draft": false, "edit_temp": false, "edit_production": false}, "output": {"success": true, "permission_results": [{"success": false, "errors": ["Entity with entity_id E_Employee_1750672694 does not exist", "Attribute with attribute_id A_E_Employee_1750672694_totalLeaveEntitlement_1750672694 does not exist"], "parsed_data": {"permission_id": "PERM_ATTR_TOTAL_ENTITLEMENT", "permission_name": "Employee totalLeaveEntitlement Attribute", "permission_type": "attribute", "resource_identifier": "Employee.totalLeaveEntitlement", "actions": ["read"], "scope": "own_records", "entity_id": "E_Employee_1750672694", "attribute_id": "A_E_Employee_1750672694_totalLeaveEntitlement_1750672694", "go_id": "", "lo_id": "", "tenant_id": "T_ACME_CORP", "description": "Access to Employee totalLeaveEntitlement attribute", "natural_language": "Permission to read total leave entitlement attribute", "version": 1, "status": "active", "created_at": "2025-06-23T09:58:14.965228", "updated_at": "2025-06-23T09:58:14.965228", "created_by": "Tarun", "updated_by": "Tarun", "permission_status": "existing", "changes_detected": []}}], "operation": "parse_validate_mongosave", "total_permissions": 1}, "status": "success"}
{"timestamp": "2025-06-23T09:58:53.546324", "endpoint": "parse-validate-mongosave/system-permissions", "input": {"natural_language": "Tenant: Acme Corp\n\npermission_id | permission_name | permission_type | resource_identifier | actions | description | scope | natural_language | version | status\n\nPERM_ATTR_TOTAL_ENTITLEMENT | Employee totalLeaveEntitlement Attribute | attribute | Employee.totalLeaveEntitlement | [\"read\"] | Access to Employee totalLeaveEntitlement attribute | own_records | Permission to read total leave entitlement attribute | 1 | active", "tenant_id": "T1001", "edit_draft": false, "edit_temp": false, "edit_production": false}, "output": {"success": true, "permission_results": [{"success": false, "error": "Item already exists in draft. Use edit_draft=true to update.", "existing_in": "mongo", "existing_document": {"_id": "685918b45e423f11979d4096", "permission_id": "PERM_ATTR_TOTAL_ENTITLEMENT", "permission_name": "Employee totalLeaveEntitlement Attribute", "permission_type": "attribute", "resource_identifier": "Employee.totalLeaveEntitlement", "actions": ["read"], "scope": "own_records", "entity_id": "E13", "attribute_id": "E13.At11", "go_id": "", "lo_id": "", "tenant_id": "T1001", "description": "Access to Employee totalLeaveEntitlement attribute", "natural_language": "Permission to read total leave entitlement attribute", "version": 5, "status": "active", "created_at": "2025-06-23T09:54:03.926234", "updated_at": "2025-06-23T09:54:03.949344", "created_by": "Tarun", "updated_by": "Tarun", "permission_status": "existing", "changes_detected": []}, "parsed_data": {"permission_id": "PERM_ATTR_TOTAL_ENTITLEMENT", "permission_name": "Employee totalLeaveEntitlement Attribute", "permission_type": "attribute", "resource_identifier": "Employee.totalLeaveEntitlement", "actions": ["read"], "scope": "own_records", "entity_id": "E13", "attribute_id": "E13.At11", "go_id": "", "lo_id": "", "tenant_id": "T1001", "description": "Access to Employee totalLeaveEntitlement attribute", "natural_language": "Permission to read total leave entitlement attribute", "version": 1, "status": "active", "created_at": "2025-06-23T09:58:53.521471", "updated_at": "2025-06-23T09:58:53.521471", "created_by": "Tarun", "updated_by": "Tarun", "permission_status": "existing", "changes_detected": []}}], "operation": "parse_validate_mongosave", "total_permissions": 1}, "status": "success"}
{"timestamp": "2025-06-23T14:07:59.773598", "endpoint": "parse-validate-mongosave/system-permissions", "input": {"natural_language": "Manager can manage products. Customer can only view products", "tenant_id": "tenant_123", "edit_draft": false, "edit_temp": false, "edit_production": false}, "output": {"success": true, "permission_results": [], "operation": "parse_validate_mongosave", "total_permissions": 0}, "status": "success"}
