from typing import List, Dict, Any, Optional
from datetime import datetime
from app.models.workflow import WorkflowExecutionLog
from app.db.mongo_db import get_collection
import logging

logger = logging.getLogger(__name__)

class ExecutionLogRepository:
    """Repository for managing workflow execution logs."""
    
    COLLECTION_NAME = "workflow_execution_logs"
    
    @classmethod
    async def create_log(cls, log_data: WorkflowExecutionLog) -> str:
        """
        Create a new workflow execution log entry.
        
        Args:
            log_data: The log data to insert
            
        Returns:
            str: ID of the inserted document
        """
        try:
            collection = get_collection(cls.COLLECTION_NAME)
            log_dict = log_data.dict()
            result = collection.insert_one(log_dict)
            return str(result.inserted_id)
        except Exception as e:
            logger.error(f"Error creating execution log: {e}")
            raise
    
    @classmethod
    async def get_logs_by_instance(cls, instance_id: str) -> List[WorkflowExecutionLog]:
        """
        Get all execution logs for a workflow instance.
        
        Args:
            instance_id: Workflow instance ID
            
        Returns:
            List[WorkflowExecutionLog]: List of execution logs
        """
        try:
            collection = get_collection(cls.COLLECTION_NAME)
            logs = list(collection.find({"instance_id": instance_id}).sort("timestamp", 1))
            return [WorkflowExecutionLog(**log) for log in logs]
        except Exception as e:
            logger.error(f"Error retrieving execution logs: {e}")
            raise
    
    @classmethod
    async def get_recent_logs(cls, limit: int = 100) -> List[WorkflowExecutionLog]:
        """
        Get most recent workflow execution logs.
        
        Args:
            limit: Maximum number of logs to return
            
        Returns:
            List[WorkflowExecutionLog]: List of execution logs
        """
        try:
            collection = get_collection(cls.COLLECTION_NAME)
            logs = list(collection.find().sort("timestamp", -1).limit(limit))
            return [WorkflowExecutionLog(**log) for log in logs]
        except Exception as e:
            logger.error(f"Error retrieving recent execution logs: {e}")
            raise
    
    @classmethod
    async def get_logs_by_go_id(cls, go_id: str, limit: int = 100) -> List[WorkflowExecutionLog]:
        """
        Get execution logs for a specific Global Objective.
        
        Args:
            go_id: Global Objective ID
            limit: Maximum number of logs to return
            
        Returns:
            List[WorkflowExecutionLog]: List of execution logs
        """
        try:
            collection = get_collection(cls.COLLECTION_NAME)
            logs = list(collection.find({"go_id": go_id}).sort("timestamp", -1).limit(limit))
            return [WorkflowExecutionLog(**log) for log in logs]
        except Exception as e:
            logger.error(f"Error retrieving GO execution logs: {e}")
            raise
    
    @classmethod
    async def get_logs_by_event_type(cls, event_type: str, limit: int = 100) -> List[WorkflowExecutionLog]:
        """
        Get execution logs by event type.
        
        Args:
            event_type: Type of event to filter by
            limit: Maximum number of logs to return
            
        Returns:
            List[WorkflowExecutionLog]: List of execution logs
        """
        try:
            collection = get_collection(cls.COLLECTION_NAME)
            logs = list(collection.find({"event_type": event_type}).sort("timestamp", -1).limit(limit))
            return [WorkflowExecutionLog(**log) for log in logs]
        except Exception as e:
            logger.error(f"Error retrieving event type logs: {e}")
            raise
