2025-05-12 05:12:04 | ➡️ Connecting to MongoDB at mongodb://localhost:27017/...
2025-05-12 05:12:04 | ➡️ Retrieving draft documents from workflow_system.workflow...
2025-05-12 05:12:04 | ✅ Found 1 draft document(s) to process.
2025-05-12 05:12:04 | ➡️ Using tenant ID: t001
2025-05-12 05:12:04 | ➡️ Connecting to PostgreSQL...
2025-05-12 05:12:04 | ✅ Connected to PostgreSQL
2025-05-12 05:12:04 | 🚀 STARTING WORKFLOW DATA MIGRATION
2025-05-12 05:12:04 | 
==== STEP 1: INSERTING BASE ENTITIES ====
2025-05-12 05:12:04 | ➡️ Inserting Tenant: ehrsystem001...
2025-05-12 05:12:04 | ✅ Inserted Tenant: ehrsystem001
2025-05-12 05:12:04 | 
➡️ Ensuring Standard Permission Types...
2025-05-12 05:12:04 | ✅ Ensured Permission Type: read
2025-05-12 05:12:04 | ✅ Ensured Permission Type: create
2025-05-12 05:12:04 | ✅ Ensured Permission Type: update
2025-05-12 05:12:04 | ✅ Ensured Permission Type: delete
2025-05-12 05:12:04 | ✅ Ensured Permission Type: execute
2025-05-12 05:12:04 | 
➡️ Inserting Additional Permission Types from YAML...
2025-05-12 05:12:04 | 
➡️ Inserting Roles...
2025-05-12 05:12:04 | ✅ Inserted Role: r001 - Patient
2025-05-12 05:12:04 | ✅ Inserted Role: r002 - Doctor
2025-05-12 05:12:04 | ✅ Inserted Role: r003 - Nurse
2025-05-12 05:12:04 | ✅ Inserted Role: r004 - Admin
2025-05-12 05:12:04 | 
➡️ Inserting Entities...
2025-05-12 05:12:04 | ✅ Inserted Entity: e007 - User
2025-05-12 05:12:04 | ✅ Inserted Entity: e008 - Role
2025-05-12 05:12:04 | ✅ Inserted Entity: e009 - Patient
2025-05-12 05:12:04 | ⚠️ Skipping duplicate entity: e008
2025-05-12 05:12:04 | ⚠️ Skipping duplicate entity: e007
2025-05-12 05:12:04 | ⚠️ Skipping duplicate entity: e008
2025-05-12 05:12:04 | ⚠️ Skipping duplicate entity: e009
2025-05-12 05:12:04 | 
==== STEP 2: INSERTING ENTITY ATTRIBUTES AND METADATA ====
2025-05-12 05:12:04 | 
➡️ Inserting Entity Attributes...
2025-05-12 05:12:04 | ✅ Inserted Entity Attribute: e007.at016
2025-05-12 05:12:04 | ✅ Inserted Entity Attribute: e007.at017
2025-05-12 05:12:04 | ✅ Inserted Entity Attribute: e007.at031
2025-05-12 05:12:04 | ✅ Inserted Entity Attribute: e007.at018
2025-05-12 05:12:04 | ✅ Inserted Entity Attribute: e007.at019
2025-05-12 05:12:04 | ✅ Inserted Entity Attribute: e007.at020
2025-05-12 05:12:04 | ✅ Inserted Entity Attribute: e007.at032
2025-05-12 05:12:04 | ✅ Inserted Entity Attribute: e007.at021
2025-05-12 05:12:04 | ✅ Inserted Enum Value for at021: Active
2025-05-12 05:12:04 | ✅ Inserted Enum Value for at021: Inactive
2025-05-12 05:12:04 | ✅ Inserted Enum Value for at021: Suspended
2025-05-12 05:12:04 | ✅ Inserted Entity Attribute: e007.at033
2025-05-12 05:12:04 | ✅ Inserted Entity Attribute: e007.at034
2025-05-12 05:12:04 | ✅ Inserted Entity Attribute: e007.at035
2025-05-12 05:12:04 | ✅ Inserted Entity Attribute: e008.at026
2025-05-12 05:12:04 | ✅ Inserted Entity Attribute: e008.at027
2025-05-12 05:12:04 | ✅ Inserted Entity Attribute: e008.at028
2025-05-12 05:12:04 | ✅ Inserted Entity Attribute: e008.at036
2025-05-12 05:12:04 | ✅ Inserted Entity Attribute: e008.at037
2025-05-12 05:12:04 | ✅ Inserted Enum Value for at037: Active
2025-05-12 05:12:04 | ✅ Inserted Enum Value for at037: Inactive
2025-05-12 05:12:04 | ✅ Inserted Entity Attribute: e008.at038
2025-05-12 05:12:04 | ✅ Inserted Entity Attribute: e008.at039
2025-05-12 05:12:04 | ✅ Inserted Entity Attribute: e008.at040
2025-05-12 05:12:04 | ✅ Inserted Entity Attribute: e009.at041
2025-05-12 05:12:04 | ✅ Inserted Entity Attribute: e009.at042
2025-05-12 05:12:04 | ✅ Inserted Entity Attribute: e009.at043
2025-05-12 05:12:04 | ✅ Inserted Entity Attribute: e009.at044
2025-05-12 05:12:04 | ✅ Inserted Entity Attribute: e009.at045
2025-05-12 05:12:04 | ✅ Inserted Enum Value for at045: Male
2025-05-12 05:12:04 | ✅ Inserted Enum Value for at045: Female
2025-05-12 05:12:04 | ✅ Inserted Enum Value for at045: Other
2025-05-12 05:12:04 | ✅ Inserted Entity Attribute: e009.at046
2025-05-12 05:12:04 | ✅ Inserted Entity Attribute: e009.at047
2025-05-12 05:12:04 | ✅ Inserted Entity Attribute: e009.at048
2025-05-12 05:12:04 | ✅ Inserted Entity Attribute: e009.at049
2025-05-12 05:12:04 | ✅ Inserted Entity Attribute: e009.at050
2025-05-12 05:12:04 | ✅ Inserted Enum Value for at050: A+
2025-05-12 05:12:04 | ✅ Inserted Enum Value for at050: A-
2025-05-12 05:12:04 | ✅ Inserted Enum Value for at050: B+
2025-05-12 05:12:04 | ✅ Inserted Enum Value for at050: B-
2025-05-12 05:12:04 | ✅ Inserted Enum Value for at050: AB+
2025-05-12 05:12:04 | ✅ Inserted Enum Value for at050: AB-
2025-05-12 05:12:04 | ✅ Inserted Enum Value for at050: O+
2025-05-12 05:12:04 | ✅ Inserted Enum Value for at050: O-
2025-05-12 05:12:04 | ✅ Inserted Enum Value for at050: Unknown
2025-05-12 05:12:04 | ✅ Inserted Entity Attribute: e009.at051
2025-05-12 05:12:04 | ✅ Inserted Entity Attribute: e009.at052
2025-05-12 05:12:04 | ✅ Inserted Entity Attribute: e008.at053
2025-05-12 05:12:04 | ✅ Inserted Entity Attribute: e008.at054
2025-05-12 05:12:04 | ✅ Inserted Entity Attribute: e008.at055
2025-05-12 05:12:04 | ✅ Inserted Entity Attribute: e008.at056
2025-05-12 05:12:04 | ✅ Inserted Entity Attribute: e008.at057
2025-05-12 05:12:04 | ✅ Inserted Entity Attribute: e008.at058
2025-05-12 05:12:04 | ✅ Inserted Entity Attribute: e008.at059
2025-05-12 05:12:04 | ✅ Inserted Enum Value for at059: Scheduled
2025-05-12 05:12:04 | ✅ Inserted Enum Value for at059: Completed
2025-05-12 05:12:04 | ✅ Inserted Enum Value for at059: Cancelled
2025-05-12 05:12:04 | ✅ Inserted Enum Value for at059: No-Show
2025-05-12 05:12:04 | ✅ Inserted Entity Attribute: e008.at060
2025-05-12 05:12:04 | ✅ Inserted Entity Attribute: e007.at061
2025-05-12 05:12:04 | ✅ Inserted Entity Attribute: e007.at062
2025-05-12 05:12:04 | ✅ Inserted Entity Attribute: e007.at063
2025-05-12 05:12:04 | ✅ Inserted Entity Attribute: e007.at064
2025-05-12 05:12:04 | ✅ Inserted Entity Attribute: e007.at065
2025-05-12 05:12:04 | ✅ Inserted Entity Attribute: e007.at066
2025-05-12 05:12:04 | ✅ Inserted Entity Attribute: e007.at067
2025-05-12 05:12:04 | ✅ Inserted Entity Attribute: e007.at068
2025-05-12 05:12:04 | ✅ Inserted Entity Attribute: e008.at069
2025-05-12 05:12:04 | ✅ Inserted Entity Attribute: e008.at070
2025-05-12 05:12:04 | ✅ Inserted Entity Attribute: e008.at071
2025-05-12 05:12:04 | ✅ Inserted Entity Attribute: e008.at072
2025-05-12 05:12:04 | ✅ Inserted Entity Attribute: e008.at073
2025-05-12 05:12:04 | ✅ Inserted Entity Attribute: e008.at074
2025-05-12 05:12:04 | ✅ Inserted Entity Attribute: e008.at075
2025-05-12 05:12:04 | ✅ Inserted Entity Attribute: e008.at076
2025-05-12 05:12:04 | ✅ Inserted Entity Attribute: e008.at077
2025-05-12 05:12:04 | ✅ Inserted Entity Attribute: e008.at078
2025-05-12 05:12:04 | ✅ Inserted Entity Attribute: e009.at079
2025-05-12 05:12:04 | ✅ Inserted Entity Attribute: e009.at080
2025-05-12 05:12:04 | ✅ Inserted Entity Attribute: e009.at081
2025-05-12 05:12:04 | ✅ Inserted Entity Attribute: e009.at082
2025-05-12 05:12:04 | ✅ Inserted Entity Attribute: e009.at083
2025-05-12 05:12:04 | ✅ Inserted Entity Attribute: e009.at084
2025-05-12 05:12:04 | ✅ Inserted Entity Attribute: e009.at085
2025-05-12 05:12:04 | ✅ Inserted Entity Attribute: e009.at086
2025-05-12 05:12:04 | ✅ Inserted Entity Attribute: e009.at087
2025-05-12 05:12:04 | ✅ Inserted Entity Attribute: e009.at088
2025-05-12 05:12:04 | 
➡️ Generating Entity Attribute Metadata from actual DB inserts...
2025-05-12 05:12:04 | ✅ Inserted Entity Attribute Metadata: e007.at031
2025-05-12 05:12:04 | ✅ Inserted Entity Attribute Metadata: e007.at032
2025-05-12 05:12:04 | ✅ Inserted Entity Attribute Metadata: e007.at033
2025-05-12 05:12:04 | ✅ Inserted Entity Attribute Metadata: e007.at034
2025-05-12 05:12:04 | ✅ Inserted Entity Attribute Metadata: e007.at035
2025-05-12 05:12:04 | ✅ Inserted Entity Attribute Metadata: e007.at061
2025-05-12 05:12:04 | ✅ Inserted Entity Attribute Metadata: e007.at062
2025-05-12 05:12:04 | ✅ Inserted Entity Attribute Metadata: e007.at063
2025-05-12 05:12:04 | ✅ Inserted Entity Attribute Metadata: e007.at064
2025-05-12 05:12:04 | ✅ Inserted Entity Attribute Metadata: e007.at065
2025-05-12 05:12:04 | ✅ Inserted Entity Attribute Metadata: e007.at066
2025-05-12 05:12:04 | ✅ Inserted Entity Attribute Metadata: e007.at067
2025-05-12 05:12:04 | ✅ Inserted Entity Attribute Metadata: e007.at068
2025-05-12 05:12:04 | ✅ Inserted Entity Attribute Metadata: e008.at036
2025-05-12 05:12:04 | ✅ Inserted Entity Attribute Metadata: e008.at037
2025-05-12 05:12:04 | ✅ Inserted Entity Attribute Metadata: e008.at038
2025-05-12 05:12:04 | ✅ Inserted Entity Attribute Metadata: e008.at039
2025-05-12 05:12:04 | ✅ Inserted Entity Attribute Metadata: e008.at040
2025-05-12 05:12:04 | ✅ Inserted Entity Attribute Metadata: e008.at053
2025-05-12 05:12:04 | ✅ Inserted Entity Attribute Metadata: e008.at054
2025-05-12 05:12:04 | ✅ Inserted Entity Attribute Metadata: e008.at055
2025-05-12 05:12:04 | ✅ Inserted Entity Attribute Metadata: e008.at056
2025-05-12 05:12:04 | ✅ Inserted Entity Attribute Metadata: e008.at057
2025-05-12 05:12:04 | ✅ Inserted Entity Attribute Metadata: e008.at058
2025-05-12 05:12:04 | ✅ Inserted Entity Attribute Metadata: e008.at059
2025-05-12 05:12:04 | ✅ Inserted Entity Attribute Metadata: e008.at060
2025-05-12 05:12:04 | ✅ Inserted Entity Attribute Metadata: e008.at069
2025-05-12 05:12:04 | ✅ Inserted Entity Attribute Metadata: e008.at070
2025-05-12 05:12:04 | ✅ Inserted Entity Attribute Metadata: e008.at071
2025-05-12 05:12:04 | ✅ Inserted Entity Attribute Metadata: e008.at072
2025-05-12 05:12:04 | ✅ Inserted Entity Attribute Metadata: e008.at073
2025-05-12 05:12:04 | ✅ Inserted Entity Attribute Metadata: e008.at074
2025-05-12 05:12:04 | ✅ Inserted Entity Attribute Metadata: e008.at075
2025-05-12 05:12:04 | ✅ Inserted Entity Attribute Metadata: e008.at076
2025-05-12 05:12:04 | ✅ Inserted Entity Attribute Metadata: e008.at077
2025-05-12 05:12:04 | ✅ Inserted Entity Attribute Metadata: e008.at078
2025-05-12 05:12:04 | ✅ Inserted Entity Attribute Metadata: e009.at041
2025-05-12 05:12:04 | ✅ Inserted Entity Attribute Metadata: e009.at042
2025-05-12 05:12:04 | ✅ Inserted Entity Attribute Metadata: e009.at043
2025-05-12 05:12:04 | ✅ Inserted Entity Attribute Metadata: e009.at044
2025-05-12 05:12:04 | ✅ Inserted Entity Attribute Metadata: e009.at045
2025-05-12 05:12:04 | ✅ Inserted Entity Attribute Metadata: e009.at046
2025-05-12 05:12:04 | ✅ Inserted Entity Attribute Metadata: e009.at047
2025-05-12 05:12:04 | ✅ Inserted Entity Attribute Metadata: e009.at048
2025-05-12 05:12:04 | ✅ Inserted Entity Attribute Metadata: e009.at049
2025-05-12 05:12:04 | ✅ Inserted Entity Attribute Metadata: e009.at050
2025-05-12 05:12:04 | ✅ Inserted Entity Attribute Metadata: e009.at051
2025-05-12 05:12:04 | ✅ Inserted Entity Attribute Metadata: e009.at052
2025-05-12 05:12:04 | ✅ Inserted Entity Attribute Metadata: e009.at079
2025-05-12 05:12:04 | ✅ Inserted Entity Attribute Metadata: e009.at080
2025-05-12 05:12:04 | ✅ Inserted Entity Attribute Metadata: e009.at081
2025-05-12 05:12:04 | ✅ Inserted Entity Attribute Metadata: e009.at082
2025-05-12 05:12:04 | ✅ Inserted Entity Attribute Metadata: e009.at083
2025-05-12 05:12:04 | ✅ Inserted Entity Attribute Metadata: e009.at084
2025-05-12 05:12:04 | ✅ Inserted Entity Attribute Metadata: e009.at085
2025-05-12 05:12:04 | ✅ Inserted Entity Attribute Metadata: e009.at086
2025-05-12 05:12:04 | ✅ Inserted Entity Attribute Metadata: e009.at087
2025-05-12 05:12:04 | ✅ Inserted Entity Attribute Metadata: e009.at088
2025-05-12 05:12:04 | ✅ Inserted Entity Attribute Metadata: e008.at036
2025-05-12 05:12:04 | ✅ Inserted Entity Attribute Metadata: e008.at037
2025-05-12 05:12:04 | ✅ Inserted Entity Attribute Metadata: e008.at038
2025-05-12 05:12:04 | ✅ Inserted Entity Attribute Metadata: e008.at039
2025-05-12 05:12:04 | ✅ Inserted Entity Attribute Metadata: e008.at040
2025-05-12 05:12:04 | ✅ Inserted Entity Attribute Metadata: e008.at053
2025-05-12 05:12:04 | ✅ Inserted Entity Attribute Metadata: e008.at054
2025-05-12 05:12:04 | ✅ Inserted Entity Attribute Metadata: e008.at055
2025-05-12 05:12:04 | ✅ Inserted Entity Attribute Metadata: e008.at056
2025-05-12 05:12:04 | ✅ Inserted Entity Attribute Metadata: e008.at057
2025-05-12 05:12:04 | ✅ Inserted Entity Attribute Metadata: e008.at058
2025-05-12 05:12:04 | ✅ Inserted Entity Attribute Metadata: e008.at059
2025-05-12 05:12:04 | ✅ Inserted Entity Attribute Metadata: e008.at060
2025-05-12 05:12:04 | ✅ Inserted Entity Attribute Metadata: e008.at069
2025-05-12 05:12:04 | ✅ Inserted Entity Attribute Metadata: e008.at070
2025-05-12 05:12:04 | ✅ Inserted Entity Attribute Metadata: e008.at071
2025-05-12 05:12:04 | ✅ Inserted Entity Attribute Metadata: e008.at072
2025-05-12 05:12:04 | ✅ Inserted Entity Attribute Metadata: e008.at073
2025-05-12 05:12:04 | ✅ Inserted Entity Attribute Metadata: e008.at074
2025-05-12 05:12:04 | ✅ Inserted Entity Attribute Metadata: e008.at075
2025-05-12 05:12:04 | ✅ Inserted Entity Attribute Metadata: e008.at076
2025-05-12 05:12:04 | ✅ Inserted Entity Attribute Metadata: e008.at077
2025-05-12 05:12:04 | ✅ Inserted Entity Attribute Metadata: e008.at078
2025-05-12 05:12:04 | ✅ Inserted Entity Attribute Metadata: e007.at031
2025-05-12 05:12:04 | ✅ Inserted Entity Attribute Metadata: e007.at032
2025-05-12 05:12:04 | ✅ Inserted Entity Attribute Metadata: e007.at033
2025-05-12 05:12:04 | ✅ Inserted Entity Attribute Metadata: e007.at034
2025-05-12 05:12:04 | ✅ Inserted Entity Attribute Metadata: e007.at035
2025-05-12 05:12:04 | ✅ Inserted Entity Attribute Metadata: e007.at061
2025-05-12 05:12:04 | ✅ Inserted Entity Attribute Metadata: e007.at062
2025-05-12 05:12:04 | ✅ Inserted Entity Attribute Metadata: e007.at063
2025-05-12 05:12:04 | ✅ Inserted Entity Attribute Metadata: e007.at064
2025-05-12 05:12:04 | ✅ Inserted Entity Attribute Metadata: e007.at065
2025-05-12 05:12:04 | ✅ Inserted Entity Attribute Metadata: e007.at066
2025-05-12 05:12:04 | ✅ Inserted Entity Attribute Metadata: e007.at067
2025-05-12 05:12:04 | ✅ Inserted Entity Attribute Metadata: e007.at068
2025-05-12 05:12:04 | ✅ Inserted Entity Attribute Metadata: e008.at036
2025-05-12 05:12:04 | ✅ Inserted Entity Attribute Metadata: e008.at037
2025-05-12 05:12:04 | ✅ Inserted Entity Attribute Metadata: e008.at038
2025-05-12 05:12:04 | ✅ Inserted Entity Attribute Metadata: e008.at039
2025-05-12 05:12:04 | ✅ Inserted Entity Attribute Metadata: e008.at040
2025-05-12 05:12:04 | ✅ Inserted Entity Attribute Metadata: e008.at053
2025-05-12 05:12:04 | ✅ Inserted Entity Attribute Metadata: e008.at054
2025-05-12 05:12:04 | ✅ Inserted Entity Attribute Metadata: e008.at055
2025-05-12 05:12:04 | ✅ Inserted Entity Attribute Metadata: e008.at056
2025-05-12 05:12:04 | ✅ Inserted Entity Attribute Metadata: e008.at057
2025-05-12 05:12:04 | ✅ Inserted Entity Attribute Metadata: e008.at058
2025-05-12 05:12:04 | ✅ Inserted Entity Attribute Metadata: e008.at059
2025-05-12 05:12:04 | ✅ Inserted Entity Attribute Metadata: e008.at060
2025-05-12 05:12:04 | ✅ Inserted Entity Attribute Metadata: e008.at069
2025-05-12 05:12:04 | ✅ Inserted Entity Attribute Metadata: e008.at070
2025-05-12 05:12:04 | ✅ Inserted Entity Attribute Metadata: e008.at071
2025-05-12 05:12:04 | ✅ Inserted Entity Attribute Metadata: e008.at072
2025-05-12 05:12:04 | ✅ Inserted Entity Attribute Metadata: e008.at073
2025-05-12 05:12:04 | ✅ Inserted Entity Attribute Metadata: e008.at074
2025-05-12 05:12:04 | ✅ Inserted Entity Attribute Metadata: e008.at075
2025-05-12 05:12:04 | ✅ Inserted Entity Attribute Metadata: e008.at076
2025-05-12 05:12:04 | ✅ Inserted Entity Attribute Metadata: e008.at077
2025-05-12 05:12:04 | ✅ Inserted Entity Attribute Metadata: e008.at078
2025-05-12 05:12:04 | ✅ Inserted Entity Attribute Metadata: e009.at041
2025-05-12 05:12:04 | ✅ Inserted Entity Attribute Metadata: e009.at042
2025-05-12 05:12:04 | ✅ Inserted Entity Attribute Metadata: e009.at043
2025-05-12 05:12:04 | ✅ Inserted Entity Attribute Metadata: e009.at044
2025-05-12 05:12:04 | ✅ Inserted Entity Attribute Metadata: e009.at045
2025-05-12 05:12:04 | ✅ Inserted Entity Attribute Metadata: e009.at046
2025-05-12 05:12:04 | ✅ Inserted Entity Attribute Metadata: e009.at047
2025-05-12 05:12:04 | ✅ Inserted Entity Attribute Metadata: e009.at048
2025-05-12 05:12:04 | ✅ Inserted Entity Attribute Metadata: e009.at049
2025-05-12 05:12:04 | ✅ Inserted Entity Attribute Metadata: e009.at050
2025-05-12 05:12:04 | ✅ Inserted Entity Attribute Metadata: e009.at051
2025-05-12 05:12:04 | ✅ Inserted Entity Attribute Metadata: e009.at052
2025-05-12 05:12:04 | ✅ Inserted Entity Attribute Metadata: e009.at079
2025-05-12 05:12:04 | ✅ Inserted Entity Attribute Metadata: e009.at080
2025-05-12 05:12:04 | ✅ Inserted Entity Attribute Metadata: e009.at081
2025-05-12 05:12:04 | ✅ Inserted Entity Attribute Metadata: e009.at082
2025-05-12 05:12:04 | ✅ Inserted Entity Attribute Metadata: e009.at083
2025-05-12 05:12:04 | ✅ Inserted Entity Attribute Metadata: e009.at084
2025-05-12 05:12:04 | ✅ Inserted Entity Attribute Metadata: e009.at085
2025-05-12 05:12:04 | ✅ Inserted Entity Attribute Metadata: e009.at086
2025-05-12 05:12:04 | ✅ Inserted Entity Attribute Metadata: e009.at087
2025-05-12 05:12:04 | ✅ Inserted Entity Attribute Metadata: e009.at088
2025-05-12 05:12:04 | 🔁 Reconstructed attribute maps successfully.
2025-05-12 05:12:04 | 
==== STEP 3: INSERTING ENTITY RELATIONSHIPS ====
2025-05-12 05:12:04 | 
➡️ Inserting Entity Relationships...
2025-05-12 05:12:04 | 
==== STEP 4: INSERTING PERMISSIONS ====
2025-05-12 05:12:04 | 
➡️ Inserting Entity Permissions...
2025-05-12 05:12:04 | ✅ Inserted Entity Permission: Role r001 - Entity e009 - Permission Read
2025-05-12 05:12:04 | ✅ Inserted Entity Permission: Role r001 - Entity e006 - Permission Read
2025-05-12 05:12:04 | ✅ Inserted Entity Permission: Role r001 - Entity e007 - Permission Read
2025-05-12 05:12:04 | ✅ Inserted Entity Permission: Role r002 - Entity e009 - Permission Read
2025-05-12 05:12:04 | ✅ Inserted Entity Permission: Role r002 - Entity e009 - Permission Create
2025-05-12 05:12:04 | ✅ Inserted Entity Permission: Role r002 - Entity e009 - Permission Update
2025-05-12 05:12:04 | ✅ Inserted Entity Permission: Role r002 - Entity e006 - Permission Read
2025-05-12 05:12:04 | ✅ Inserted Entity Permission: Role r002 - Entity e006 - Permission Create
2025-05-12 05:12:04 | ✅ Inserted Entity Permission: Role r002 - Entity e006 - Permission Update
2025-05-12 05:12:04 | ✅ Inserted Entity Permission: Role r002 - Entity e007 - Permission Read
2025-05-12 05:12:04 | ✅ Inserted Entity Permission: Role r002 - Entity e007 - Permission Create
2025-05-12 05:12:04 | ✅ Inserted Entity Permission: Role r002 - Entity e007 - Permission Update
2025-05-12 05:12:04 | ✅ Inserted Entity Permission: Role r002 - Entity e008 - Permission Read
2025-05-12 05:12:04 | ✅ Inserted Entity Permission: Role r002 - Entity e008 - Permission Create
2025-05-12 05:12:04 | ✅ Inserted Entity Permission: Role r002 - Entity e008 - Permission Update
2025-05-12 05:12:04 | ✅ Inserted Entity Permission: Role r003 - Entity e009 - Permission Read
2025-05-12 05:12:04 | ✅ Inserted Entity Permission: Role r003 - Entity e009 - Permission Create
2025-05-12 05:12:04 | ✅ Inserted Entity Permission: Role r003 - Entity e006 - Permission Read
2025-05-12 05:12:04 | ✅ Inserted Entity Permission: Role r003 - Entity e006 - Permission Create
2025-05-12 05:12:04 | ✅ Inserted Entity Permission: Role r003 - Entity e007 - Permission Read
2025-05-12 05:12:04 | ✅ Inserted Entity Permission: Role r003 - Entity e007 - Permission Create
2025-05-12 05:12:04 | ✅ Inserted Entity Permission: Role r004 - Entity e009 - Permission Read
2025-05-12 05:12:04 | ✅ Inserted Entity Permission: Role r004 - Entity e009 - Permission Create
2025-05-12 05:12:04 | ✅ Inserted Entity Permission: Role r004 - Entity e009 - Permission Update
2025-05-12 05:12:04 | ✅ Inserted Entity Permission: Role r004 - Entity e009 - Permission Delete
2025-05-12 05:12:04 | ✅ Inserted Entity Permission: Role r004 - Entity e006 - Permission Read
2025-05-12 05:12:04 | ✅ Inserted Entity Permission: Role r004 - Entity e006 - Permission Create
2025-05-12 05:12:04 | ✅ Inserted Entity Permission: Role r004 - Entity e006 - Permission Update
2025-05-12 05:12:04 | ✅ Inserted Entity Permission: Role r004 - Entity e006 - Permission Delete
2025-05-12 05:12:04 | ✅ Inserted Entity Permission: Role r004 - Entity e007 - Permission Read
2025-05-12 05:12:04 | ✅ Inserted Entity Permission: Role r004 - Entity e007 - Permission Create
2025-05-12 05:12:04 | ✅ Inserted Entity Permission: Role r004 - Entity e007 - Permission Update
2025-05-12 05:12:04 | ✅ Inserted Entity Permission: Role r004 - Entity e007 - Permission Delete
2025-05-12 05:12:04 | ✅ Inserted Entity Permission: Role r004 - Entity e008 - Permission Read
2025-05-12 05:12:04 | ✅ Inserted Entity Permission: Role r004 - Entity e008 - Permission Create
2025-05-12 05:12:04 | ✅ Inserted Entity Permission: Role r004 - Entity e008 - Permission Update
2025-05-12 05:12:04 | ✅ Inserted Entity Permission: Role r004 - Entity e008 - Permission Delete
2025-05-12 05:12:04 | ✅ Inserted Entity Permission: Role r004 - Entity e009 - Permission Read
2025-05-12 05:12:04 | ✅ Inserted Entity Permission: Role r004 - Entity e009 - Permission Create
2025-05-12 05:12:04 | ✅ Inserted Entity Permission: Role r004 - Entity e009 - Permission Update
2025-05-12 05:12:04 | ✅ Inserted Entity Permission: Role r004 - Entity e009 - Permission Delete
2025-05-12 05:12:04 | 
➡️ Inserting Objective Permissions...
2025-05-12 05:12:04 | ✅ Inserted Objective Permission: Role r001 - Objective go001.lo007 - Permission Execute
2025-05-12 05:12:04 | ✅ Inserted Objective Permission: Role r002 - Objective go001.lo005 - Permission Execute
2025-05-12 05:12:04 | ✅ Inserted Objective Permission: Role r002 - Objective go001.lo006 - Permission Execute
2025-05-12 05:12:04 | ✅ Inserted Objective Permission: Role r003 - Objective go001.lo005 - Permission Execute
2025-05-12 05:12:04 | ✅ Inserted Objective Permission: Role r004 - Objective go001.lo007 - Permission Execute
2025-05-12 05:12:04 | ✅ Inserted Objective Permission: Role r004 - Objective go001.lo005 - Permission Execute
2025-05-12 05:12:04 | ✅ Inserted Objective Permission: Role r004 - Objective go001.lo006 - Permission Execute
2025-05-12 05:12:04 | ✅ Inserted Objective Permission: Role r004 - Objective go001.lo007 - Permission Execute
2025-05-12 05:12:04 | 
==== STEP 5: INSERTING GLOBAL OBJECTIVES ====
2025-05-12 05:12:04 | 
➡️ Inserting Global Objectives...
2025-05-12 05:12:04 | 
==== STEP 6: INSERTING LOCAL OBJECTIVES ====
2025-05-12 05:12:04 | 
➡️ Inserting base Local Objectives...
2025-05-12 05:12:04 | ✅ Inserted Local Objective: lo007 - Patient Registration
2025-05-12 05:12:04 | ✅ Inserted Local Objective: lo005 - Appointment Scheduling
2025-05-12 05:12:04 | ✅ Inserted Local Objective: lo006 - Medical Records and Prescriptions
2025-05-12 05:12:04 | ✅ Inserted Local Objective: lo007 - Lab Reports and Results
2025-05-12 05:12:04 | 
➡️ Inserting Local Objective relationships and data...
2025-05-12 05:12:04 | ✅ Inserted Execution Pathway: lo007 -> lo005
2025-05-12 05:12:04 | ✅ Created Agent Stack for lo007
2025-05-12 05:12:04 | DEBUG: Role ID from YAML: 'r004'
2025-05-12 05:12:04 | ✅ Inserted 1 Agent Rights for role r004 in stack for lo007
2025-05-12 05:12:04 | 🔍 Creating input stack for lo007...
2025-05-12 05:12:04 | 🔧 insert_and_get_stack_id called for lo_input_stack, lo_id=lo007
2025-05-12 05:12:04 | 🔧 Executing SQL: 
        INSERT INTO lo_input_stack (lo_id, description, created_at, updated_at)
        VALUES (%s, %s, %s, %s)
        ON CONFLICT (lo_id) DO NOTHING
        RETURNING id
    
2025-05-12 05:12:04 | 🔧 Parameters: (lo007, 'Capture patient registration details', 2025-05-12 05:12:04.274172, 2025-05-12 05:12:04.274172)
2025-05-12 05:12:04 | 🔧 Successfully got ID: 2115
2025-05-12 05:12:04 | ✅ Created or retrieved Input Stack for lo007 with ID: 2115
2025-05-12 05:12:04 | 🔄 Assigned correct slot_id for in001: e009.at041.in001
2025-05-12 05:12:04 | ✅ Inserted Input Item in001 for lo007
2025-05-12 05:12:04 | ✅ Inserted Validation for in001: validate_pattern
2025-05-12 05:12:04 | 🔧 insert_and_get_stack_id called for lo_output_stack, lo_id=lo007
2025-05-12 05:12:04 | 🔧 Executing SQL: 
        INSERT INTO lo_output_stack (lo_id, description, created_at, updated_at)
        VALUES (%s, %s, %s, %s)
        ON CONFLICT (lo_id) DO NOTHING
        RETURNING id
    
2025-05-12 05:12:04 | 🔧 Parameters: (lo007, 'Patient registration completed', 2025-05-12 05:12:04.274172, 2025-05-12 05:12:04.274172)
2025-05-12 05:12:04 | 🔧 Successfully got ID: 2082
2025-05-12 05:12:04 | ✅ Created or retrieved Output Stack for lo007
2025-05-12 05:12:04 | ✅ Inserted Output Item out001 for lo007
2025-05-12 05:12:04 | ✅ Inserted Output Item out002 for lo007
2025-05-12 05:12:04 | ✅ Inserted Execution Pathway: lo005 -> lo006
2025-05-12 05:12:04 | ✅ Created Agent Stack for lo005
2025-05-12 05:12:04 | DEBUG: Role ID from YAML: 'r002'
2025-05-12 05:12:04 | ✅ Inserted 1 Agent Rights for role r002 in stack for lo005
2025-05-12 05:12:04 | DEBUG: Role ID from YAML: 'r003'
2025-05-12 05:12:04 | ✅ Inserted 1 Agent Rights for role r003 in stack for lo005
2025-05-12 05:12:04 | 🔍 Creating input stack for lo005...
2025-05-12 05:12:04 | 🔧 insert_and_get_stack_id called for lo_input_stack, lo_id=lo005
2025-05-12 05:12:04 | 🔧 Executing SQL: 
        INSERT INTO lo_input_stack (lo_id, description, created_at, updated_at)
        VALUES (%s, %s, %s, %s)
        ON CONFLICT (lo_id) DO NOTHING
        RETURNING id
    
2025-05-12 05:12:04 | 🔧 Parameters: (lo005, 'Schedule patient appointment', 2025-05-12 05:12:04.274172, 2025-05-12 05:12:04.274172)
2025-05-12 05:12:04 | 🔧 Successfully got ID: 2116
2025-05-12 05:12:04 | ✅ Created or retrieved Input Stack for lo005 with ID: 2116
2025-05-12 05:12:04 | ⚠️ Could not find attribute for 'Appointment ID' in entity e006
2025-05-12 05:12:04 | ✅ Inserted Input Item in004 for lo005
2025-05-12 05:12:04 | ✅ Inserted Validation for in004: validate_pattern
2025-05-12 05:12:04 | 🔧 insert_and_get_stack_id called for lo_output_stack, lo_id=lo005
2025-05-12 05:12:04 | 🔧 Executing SQL: 
        INSERT INTO lo_output_stack (lo_id, description, created_at, updated_at)
        VALUES (%s, %s, %s, %s)
        ON CONFLICT (lo_id) DO NOTHING
        RETURNING id
    
2025-05-12 05:12:04 | 🔧 Parameters: (lo005, 'Appointment scheduled', 2025-05-12 05:12:04.274172, 2025-05-12 05:12:04.274172)
2025-05-12 05:12:04 | 🔧 Successfully got ID: 2083
2025-05-12 05:12:04 | ✅ Created or retrieved Output Stack for lo005
2025-05-12 05:12:04 | ✅ Inserted Output Item out001 for lo005
2025-05-12 05:12:04 | ✅ Inserted Output Item out002 for lo005
2025-05-12 05:12:04 | ✅ Inserted Execution Pathway: lo006 -> lo007
2025-05-12 05:12:04 | ✅ Created Agent Stack for lo006
2025-05-12 05:12:04 | DEBUG: Role ID from YAML: 'r002'
2025-05-12 05:12:04 | ✅ Inserted 1 Agent Rights for role r002 in stack for lo006
2025-05-12 05:12:04 | 🔍 Creating input stack for lo006...
2025-05-12 05:12:04 | 🔧 insert_and_get_stack_id called for lo_input_stack, lo_id=lo006
2025-05-12 05:12:04 | 🔧 Executing SQL: 
        INSERT INTO lo_input_stack (lo_id, description, created_at, updated_at)
        VALUES (%s, %s, %s, %s)
        ON CONFLICT (lo_id) DO NOTHING
        RETURNING id
    
2025-05-12 05:12:04 | 🔧 Parameters: (lo006, 'Create medical record and prescription', 2025-05-12 05:12:04.274172, 2025-05-12 05:12:04.274172)
2025-05-12 05:12:04 | 🔧 Successfully got ID: 2117
2025-05-12 05:12:04 | ✅ Created or retrieved Input Stack for lo006 with ID: 2117
2025-05-12 05:12:04 | ⚠️ Could not find attribute for 'Record ID' in entity e007
2025-05-12 05:12:04 | ✅ Inserted Input Item in001 for lo006
2025-05-12 05:12:04 | ✅ Inserted Validation for in001: validate_pattern
2025-05-12 05:12:04 | 🔄 Assigned correct slot_id for in008: e008.at069.in008
2025-05-12 05:12:04 | ✅ Inserted Input Item in008 for lo006
2025-05-12 05:12:04 | ✅ Inserted Validation for in008: validate_pattern
2025-05-12 05:12:04 | 🔧 insert_and_get_stack_id called for lo_output_stack, lo_id=lo006
2025-05-12 05:12:04 | 🔧 Executing SQL: 
        INSERT INTO lo_output_stack (lo_id, description, created_at, updated_at)
        VALUES (%s, %s, %s, %s)
        ON CONFLICT (lo_id) DO NOTHING
        RETURNING id
    
2025-05-12 05:12:04 | 🔧 Parameters: (lo006, 'Medical record and prescription created', 2025-05-12 05:12:04.274172, 2025-05-12 05:12:04.274172)
2025-05-12 05:12:04 | 🔧 Successfully got ID: 2084
2025-05-12 05:12:04 | ✅ Created or retrieved Output Stack for lo006
2025-05-12 05:12:04 | ✅ Inserted Output Item out001 for lo006
2025-05-12 05:12:04 | ✅ Inserted Output Item out002 for lo006
2025-05-12 05:12:04 | ✅ Inserted Output Item out003 for lo006
2025-05-12 05:12:04 | ✅ Inserted Terminal Pathway for lo007
2025-05-12 05:12:04 | ⚠️ Execution pathway already exists for lo007, skipping
2025-05-12 05:12:04 | ⚠️ Using existing Agent Stack for lo007
2025-05-12 05:12:04 | DEBUG: Role ID from YAML: 'r002'
2025-05-12 05:12:04 | ✅ Inserted 1 Agent Rights for role r002 in stack for lo007
2025-05-12 05:12:04 | 🔍 Creating input stack for lo007...
2025-05-12 05:12:04 | 🔧 insert_and_get_stack_id called for lo_input_stack, lo_id=lo007
2025-05-12 05:12:04 | 🔧 Executing SQL: 
        INSERT INTO lo_input_stack (lo_id, description, created_at, updated_at)
        VALUES (%s, %s, %s, %s)
        ON CONFLICT (lo_id) DO NOTHING
        RETURNING id
    
2025-05-12 05:12:04 | 🔧 Parameters: (lo007, 'Create lab report and results', 2025-05-12 05:12:04.274172, 2025-05-12 05:12:04.274172)
2025-05-12 05:12:04 | 🔧 No ID returned (possible conflict), selecting existing ID
2025-05-12 05:12:04 | 🔧 Successfully got ID: 2115
2025-05-12 05:12:04 | ✅ Created or retrieved Input Stack for lo007 with ID: 2115
2025-05-12 05:12:04 | 🔄 Assigned correct slot_id for in001: e009.at079.in001
2025-05-12 05:12:04 | ⚠️ Skipped duplicate input item: lo007.in001
2025-05-12 05:12:04 | ✅ Inserted Validation for in001: validate_pattern
2025-05-12 05:12:04 | 🔧 insert_and_get_stack_id called for lo_output_stack, lo_id=lo007
2025-05-12 05:12:04 | 🔧 Executing SQL: 
        INSERT INTO lo_output_stack (lo_id, description, created_at, updated_at)
        VALUES (%s, %s, %s, %s)
        ON CONFLICT (lo_id) DO NOTHING
        RETURNING id
    
2025-05-12 05:12:04 | 🔧 Parameters: (lo007, 'Lab report and results created', 2025-05-12 05:12:04.274172, 2025-05-12 05:12:04.274172)
2025-05-12 05:12:04 | 🔧 No ID returned (possible conflict), selecting existing ID
2025-05-12 05:12:04 | 🔧 Successfully got ID: 2082
2025-05-12 05:12:04 | ✅ Created or retrieved Output Stack for lo007
2025-05-12 05:12:04 | ⚠️ Skipped duplicate output item: lo007.out001
2025-05-12 05:12:04 | ⚠️ Skipped duplicate output item: lo007.out002
2025-05-12 05:12:04 | 
==== VALIDATING DATA INTEGRITY ====
2025-05-12 05:12:04 | 
==== VALIDATING NESTED FUNCTIONS ====
2025-05-12 05:12:04 | ✅ All nested functions validated successfully
2025-05-12 05:12:04 | ✅ MIGRATION COMPLETE
2025-05-12 05:12:04 | 
🏁 Migration of data to PostgreSQL schema workflow_runtime complete!
2025-05-12 07:26:04 | ➡️ Connecting to MongoDB at mongodb://localhost:27017/...
2025-05-12 07:26:04 | ➡️ Retrieving draft documents from workflow_system.workflow...
2025-05-12 07:26:04 | ✅ Found 1 draft document(s) to process.
2025-05-12 07:26:04 | ➡️ Using tenant ID: t001
2025-05-12 07:26:04 | ➡️ Connecting to PostgreSQL...
2025-05-12 07:26:04 | ✅ Connected to PostgreSQL
2025-05-12 07:26:04 | 🚀 STARTING WORKFLOW DATA MIGRATION
2025-05-12 07:26:04 | 
==== STEP 1: INSERTING BASE ENTITIES ====
2025-05-12 07:26:04 | ➡️ Inserting Tenant: ehrsystem001...
2025-05-12 07:26:04 | ✅ Inserted Tenant: ehrsystem001
2025-05-12 07:26:04 | 
➡️ Ensuring Standard Permission Types...
2025-05-12 07:26:04 | ✅ Ensured Permission Type: read
2025-05-12 07:26:04 | ✅ Ensured Permission Type: create
2025-05-12 07:26:04 | ✅ Ensured Permission Type: update
2025-05-12 07:26:04 | ✅ Ensured Permission Type: delete
2025-05-12 07:26:04 | ✅ Ensured Permission Type: execute
2025-05-12 07:26:04 | 
➡️ Inserting Additional Permission Types from YAML...
2025-05-12 07:26:04 | 
➡️ Inserting Roles...
2025-05-12 07:26:04 | ✅ Inserted Role: r001 - Patient
2025-05-12 07:26:04 | ✅ Inserted Role: r002 - Doctor
2025-05-12 07:26:04 | ✅ Inserted Role: r003 - Nurse
2025-05-12 07:26:04 | ✅ Inserted Role: r004 - Admin
2025-05-12 07:26:04 | 
➡️ Inserting Entities...
2025-05-12 07:26:04 | ✅ Inserted Entity: e007 - User
2025-05-12 07:26:04 | ✅ Inserted Entity: e008 - Role
2025-05-12 07:26:04 | ✅ Inserted Entity: e009 - Patient
2025-05-12 07:26:04 | ✅ Inserted Entity: e010 - Appointment
2025-05-12 07:26:04 | ✅ Inserted Entity: e011 - MedicalRecord
2025-05-12 07:26:04 | ✅ Inserted Entity: e012 - Prescription
2025-05-12 07:26:04 | ✅ Inserted Entity: e013 - LabReport
2025-05-12 07:26:04 | 
==== STEP 2: INSERTING ENTITY ATTRIBUTES AND METADATA ====
2025-05-12 07:26:04 | 
➡️ Inserting Entity Attributes...
2025-05-12 07:26:04 | ✅ Inserted Entity Attribute: e007.at089
2025-05-12 07:26:04 | ✅ Inserted Entity Attribute: e007.at090
2025-05-12 07:26:04 | ⚠️ Attribute already exists: e007.at031
2025-05-12 07:26:04 | ✅ Inserted Entity Attribute: e007.at091
2025-05-12 07:26:04 | ✅ Inserted Entity Attribute: e007.at092
2025-05-12 07:26:04 | ✅ Inserted Entity Attribute: e007.at093
2025-05-12 07:26:04 | ⚠️ Attribute already exists: e007.at032
2025-05-12 07:26:04 | ✅ Inserted Entity Attribute: e007.at094
2025-05-12 07:26:04 | ✅ Inserted Enum Value for at094: Active
2025-05-12 07:26:04 | ✅ Inserted Enum Value for at094: Inactive
2025-05-12 07:26:04 | ✅ Inserted Enum Value for at094: Suspended
2025-05-12 07:26:04 | ⚠️ Attribute already exists: e007.at033
2025-05-12 07:26:04 | ⚠️ Attribute already exists: e007.at034
2025-05-12 07:26:04 | ✅ Inserted Entity Attribute: e008.at095
2025-05-12 07:26:04 | ✅ Inserted Entity Attribute: e008.at096
2025-05-12 07:26:04 | ✅ Inserted Entity Attribute: e008.at097
2025-05-12 07:26:04 | ⚠️ Attribute already exists: e008.at036
2025-05-12 07:26:04 | ⚠️ Attribute already exists: e008.at059
2025-05-12 07:26:04 | ⚠️ Attribute already exists: e008.at038
2025-05-12 07:26:04 | ⚠️ Attribute already exists: e008.at039
2025-05-12 07:26:04 | ⚠️ Attribute already exists: e009.at080
2025-05-12 07:26:04 | ⚠️ Attribute already exists: e009.at042
2025-05-12 07:26:04 | ⚠️ Attribute already exists: e009.at043
2025-05-12 07:26:04 | ⚠️ Attribute already exists: e009.at044
2025-05-12 07:26:04 | ⚠️ Attribute already exists: e009.at045
2025-05-12 07:26:04 | ⚠️ Attribute already exists: e009.at048
2025-05-12 07:26:04 | ✅ Inserted Entity Attribute: e009.at098
2025-05-12 07:26:04 | ⚠️ Attribute already exists: e009.at047
2025-05-12 07:26:04 | ✅ Inserted Entity Attribute: e009.at099
2025-05-12 07:26:04 | ✅ Inserted Entity Attribute: e009.at100
2025-05-12 07:26:04 | ✅ Inserted Entity Attribute: e010.at080
2025-05-12 07:26:04 | ✅ Inserted Entity Attribute: e010.at042
2025-05-12 07:26:04 | ✅ Inserted Entity Attribute: e010.at043
2025-05-12 07:26:04 | ✅ Inserted Entity Attribute: e010.at044
2025-05-12 07:26:04 | ✅ Inserted Entity Attribute: e010.at045
2025-05-12 07:26:04 | ✅ Inserted Entity Attribute: e010.at048
2025-05-12 07:26:04 | ✅ Inserted Entity Attribute: e010.at098
2025-05-12 07:26:04 | ✅ Inserted Enum Value for at098: Scheduled
2025-05-12 07:26:04 | ✅ Inserted Enum Value for at098: Completed
2025-05-12 07:26:04 | ✅ Inserted Enum Value for at098: Cancelled
2025-05-12 07:26:04 | ✅ Inserted Enum Value for at098: No-Show
2025-05-12 07:26:04 | ✅ Inserted Entity Attribute: e010.at047
2025-05-12 07:26:04 | ✅ Inserted Entity Attribute: e011.at099
2025-05-12 07:26:04 | ✅ Inserted Entity Attribute: e011.at100
2025-05-12 07:26:04 | ✅ Inserted Entity Attribute: e011.at111
2025-05-12 07:26:04 | ✅ Inserted Entity Attribute: e011.at112
2025-05-12 07:26:04 | ✅ Inserted Entity Attribute: e011.at113
2025-05-12 07:26:04 | ✅ Inserted Entity Attribute: e011.at114
2025-05-12 07:26:04 | ✅ Inserted Entity Attribute: e011.at115
2025-05-12 07:26:04 | ✅ Inserted Entity Attribute: e011.at116
2025-05-12 07:26:04 | ✅ Inserted Entity Attribute: e011.at117
2025-05-12 07:26:04 | ✅ Inserted Entity Attribute: e012.at118
2025-05-12 07:26:04 | ✅ Inserted Entity Attribute: e012.at119
2025-05-12 07:26:04 | ✅ Inserted Entity Attribute: e012.at120
2025-05-12 07:26:04 | ✅ Inserted Entity Attribute: e012.at121
2025-05-12 07:26:04 | ✅ Inserted Entity Attribute: e012.at122
2025-05-12 07:26:04 | ✅ Inserted Entity Attribute: e012.at123
2025-05-12 07:26:04 | ✅ Inserted Entity Attribute: e012.at124
2025-05-12 07:26:04 | ✅ Inserted Entity Attribute: e012.at125
2025-05-12 07:26:04 | ✅ Inserted Entity Attribute: e012.at126
2025-05-12 07:26:04 | ✅ Inserted Entity Attribute: e012.at127
2025-05-12 07:26:04 | ✅ Inserted Entity Attribute: e013.at128
2025-05-12 07:26:04 | ✅ Inserted Entity Attribute: e013.at129
2025-05-12 07:26:04 | ✅ Inserted Entity Attribute: e013.at130
2025-05-12 07:26:04 | ✅ Inserted Entity Attribute: e013.at131
2025-05-12 07:26:04 | ✅ Inserted Entity Attribute: e013.at132
2025-05-12 07:26:04 | ✅ Inserted Entity Attribute: e013.at133
2025-05-12 07:26:04 | ✅ Inserted Entity Attribute: e013.at134
2025-05-12 07:26:04 | ✅ Inserted Entity Attribute: e013.at135
2025-05-12 07:26:04 | ✅ Inserted Entity Attribute: e013.at136
2025-05-12 07:26:04 | 
➡️ Generating Entity Attribute Metadata from actual DB inserts...
2025-05-12 07:26:04 | ✅ Inserted Entity Attribute Metadata: e007.at031
2025-05-12 07:26:04 | ✅ Inserted Entity Attribute Metadata: e007.at032
2025-05-12 07:26:04 | ✅ Inserted Entity Attribute Metadata: e007.at033
2025-05-12 07:26:04 | ✅ Inserted Entity Attribute Metadata: e007.at034
2025-05-12 07:26:04 | ✅ Inserted Entity Attribute Metadata: e007.at035
2025-05-12 07:26:04 | ✅ Inserted Entity Attribute Metadata: e007.at061
2025-05-12 07:26:04 | ✅ Inserted Entity Attribute Metadata: e007.at062
2025-05-12 07:26:04 | ✅ Inserted Entity Attribute Metadata: e007.at063
2025-05-12 07:26:04 | ✅ Inserted Entity Attribute Metadata: e007.at064
2025-05-12 07:26:04 | ✅ Inserted Entity Attribute Metadata: e007.at065
2025-05-12 07:26:04 | ✅ Inserted Entity Attribute Metadata: e007.at066
2025-05-12 07:26:04 | ✅ Inserted Entity Attribute Metadata: e007.at067
2025-05-12 07:26:04 | ✅ Inserted Entity Attribute Metadata: e007.at068
2025-05-12 07:26:04 | ✅ Inserted Entity Attribute Metadata: e007.at089
2025-05-12 07:26:04 | ✅ Inserted Entity Attribute Metadata: e007.at090
2025-05-12 07:26:04 | ✅ Inserted Entity Attribute Metadata: e007.at091
2025-05-12 07:26:04 | ✅ Inserted Entity Attribute Metadata: e007.at092
2025-05-12 07:26:04 | ✅ Inserted Entity Attribute Metadata: e007.at093
2025-05-12 07:26:04 | ✅ Inserted Entity Attribute Metadata: e007.at094
2025-05-12 07:26:04 | ✅ Inserted Entity Attribute Metadata: e008.at036
2025-05-12 07:26:04 | ✅ Inserted Entity Attribute Metadata: e008.at037
2025-05-12 07:26:04 | ✅ Inserted Entity Attribute Metadata: e008.at038
2025-05-12 07:26:04 | ✅ Inserted Entity Attribute Metadata: e008.at039
2025-05-12 07:26:04 | ✅ Inserted Entity Attribute Metadata: e008.at040
2025-05-12 07:26:04 | ✅ Inserted Entity Attribute Metadata: e008.at053
2025-05-12 07:26:04 | ✅ Inserted Entity Attribute Metadata: e008.at054
2025-05-12 07:26:04 | ✅ Inserted Entity Attribute Metadata: e008.at055
2025-05-12 07:26:04 | ✅ Inserted Entity Attribute Metadata: e008.at056
2025-05-12 07:26:04 | ✅ Inserted Entity Attribute Metadata: e008.at057
2025-05-12 07:26:04 | ✅ Inserted Entity Attribute Metadata: e008.at058
2025-05-12 07:26:04 | ✅ Inserted Entity Attribute Metadata: e008.at059
2025-05-12 07:26:04 | ✅ Inserted Entity Attribute Metadata: e008.at060
2025-05-12 07:26:04 | ✅ Inserted Entity Attribute Metadata: e008.at069
2025-05-12 07:26:04 | ✅ Inserted Entity Attribute Metadata: e008.at070
2025-05-12 07:26:04 | ✅ Inserted Entity Attribute Metadata: e008.at071
2025-05-12 07:26:04 | ✅ Inserted Entity Attribute Metadata: e008.at072
2025-05-12 07:26:04 | ✅ Inserted Entity Attribute Metadata: e008.at073
2025-05-12 07:26:04 | ✅ Inserted Entity Attribute Metadata: e008.at074
2025-05-12 07:26:04 | ✅ Inserted Entity Attribute Metadata: e008.at075
2025-05-12 07:26:04 | ✅ Inserted Entity Attribute Metadata: e008.at076
2025-05-12 07:26:04 | ✅ Inserted Entity Attribute Metadata: e008.at077
2025-05-12 07:26:04 | ✅ Inserted Entity Attribute Metadata: e008.at078
2025-05-12 07:26:04 | ✅ Inserted Entity Attribute Metadata: e008.at095
2025-05-12 07:26:04 | ✅ Inserted Entity Attribute Metadata: e008.at096
2025-05-12 07:26:04 | ✅ Inserted Entity Attribute Metadata: e008.at097
2025-05-12 07:26:04 | ✅ Inserted Entity Attribute Metadata: e009.at041
2025-05-12 07:26:04 | ✅ Inserted Entity Attribute Metadata: e009.at042
2025-05-12 07:26:04 | ✅ Inserted Entity Attribute Metadata: e009.at043
2025-05-12 07:26:04 | ✅ Inserted Entity Attribute Metadata: e009.at044
2025-05-12 07:26:04 | ✅ Inserted Entity Attribute Metadata: e009.at045
2025-05-12 07:26:04 | ✅ Inserted Entity Attribute Metadata: e009.at046
2025-05-12 07:26:04 | ✅ Inserted Entity Attribute Metadata: e009.at047
2025-05-12 07:26:04 | ✅ Inserted Entity Attribute Metadata: e009.at048
2025-05-12 07:26:04 | ✅ Inserted Entity Attribute Metadata: e009.at049
2025-05-12 07:26:04 | ✅ Inserted Entity Attribute Metadata: e009.at050
2025-05-12 07:26:04 | ✅ Inserted Entity Attribute Metadata: e009.at051
2025-05-12 07:26:04 | ✅ Inserted Entity Attribute Metadata: e009.at052
2025-05-12 07:26:04 | ✅ Inserted Entity Attribute Metadata: e009.at079
2025-05-12 07:26:04 | ✅ Inserted Entity Attribute Metadata: e009.at080
2025-05-12 07:26:04 | ✅ Inserted Entity Attribute Metadata: e009.at081
2025-05-12 07:26:04 | ✅ Inserted Entity Attribute Metadata: e009.at082
2025-05-12 07:26:04 | ✅ Inserted Entity Attribute Metadata: e009.at083
2025-05-12 07:26:04 | ✅ Inserted Entity Attribute Metadata: e009.at084
2025-05-12 07:26:04 | ✅ Inserted Entity Attribute Metadata: e009.at085
2025-05-12 07:26:04 | ✅ Inserted Entity Attribute Metadata: e009.at086
2025-05-12 07:26:04 | ✅ Inserted Entity Attribute Metadata: e009.at087
2025-05-12 07:26:04 | ✅ Inserted Entity Attribute Metadata: e009.at088
2025-05-12 07:26:04 | ✅ Inserted Entity Attribute Metadata: e009.at098
2025-05-12 07:26:04 | ✅ Inserted Entity Attribute Metadata: e009.at099
2025-05-12 07:26:04 | ✅ Inserted Entity Attribute Metadata: e009.at100
2025-05-12 07:26:04 | ✅ Inserted Entity Attribute Metadata: e011.at111
2025-05-12 07:26:04 | ✅ Inserted Entity Attribute Metadata: e011.at112
2025-05-12 07:26:04 | ✅ Inserted Entity Attribute Metadata: e011.at113
2025-05-12 07:26:04 | ✅ Inserted Entity Attribute Metadata: e011.at114
2025-05-12 07:26:04 | ✅ Inserted Entity Attribute Metadata: e011.at115
2025-05-12 07:26:04 | ✅ Inserted Entity Attribute Metadata: e011.at116
2025-05-12 07:26:04 | ✅ Inserted Entity Attribute Metadata: e011.at117
2025-05-12 07:26:04 | ✅ Inserted Entity Attribute Metadata: e012.at118
2025-05-12 07:26:04 | ✅ Inserted Entity Attribute Metadata: e012.at119
2025-05-12 07:26:04 | ✅ Inserted Entity Attribute Metadata: e012.at120
2025-05-12 07:26:04 | ✅ Inserted Entity Attribute Metadata: e012.at121
2025-05-12 07:26:04 | ✅ Inserted Entity Attribute Metadata: e012.at122
2025-05-12 07:26:04 | ✅ Inserted Entity Attribute Metadata: e012.at123
2025-05-12 07:26:04 | ✅ Inserted Entity Attribute Metadata: e012.at124
2025-05-12 07:26:04 | ✅ Inserted Entity Attribute Metadata: e012.at125
2025-05-12 07:26:04 | ✅ Inserted Entity Attribute Metadata: e012.at126
2025-05-12 07:26:04 | ✅ Inserted Entity Attribute Metadata: e012.at127
2025-05-12 07:26:04 | ✅ Inserted Entity Attribute Metadata: e013.at128
2025-05-12 07:26:04 | ✅ Inserted Entity Attribute Metadata: e013.at129
2025-05-12 07:26:04 | ✅ Inserted Entity Attribute Metadata: e013.at130
2025-05-12 07:26:04 | ✅ Inserted Entity Attribute Metadata: e013.at131
2025-05-12 07:26:04 | ✅ Inserted Entity Attribute Metadata: e013.at132
2025-05-12 07:26:04 | ✅ Inserted Entity Attribute Metadata: e013.at133
2025-05-12 07:26:04 | ✅ Inserted Entity Attribute Metadata: e013.at134
2025-05-12 07:26:04 | ✅ Inserted Entity Attribute Metadata: e013.at135
2025-05-12 07:26:04 | ✅ Inserted Entity Attribute Metadata: e013.at136
2025-05-12 07:26:04 | 🔁 Reconstructed attribute maps successfully.
2025-05-12 07:26:04 | 
==== STEP 3: INSERTING ENTITY RELATIONSHIPS ====
2025-05-12 07:26:04 | 
➡️ Inserting Entity Relationships...
2025-05-12 07:26:04 | 
==== STEP 4: INSERTING PERMISSIONS ====
2025-05-12 07:26:04 | 
➡️ Inserting Entity Permissions...
2025-05-12 07:26:04 | ✅ Inserted Entity Permission: Role r001 - Entity e009 - Permission Read
2025-05-12 07:26:04 | ✅ Inserted Entity Permission: Role r001 - Entity e010 - Permission Read
2025-05-12 07:26:04 | ✅ Inserted Entity Permission: Role r001 - Entity e011 - Permission Read
2025-05-12 07:26:04 | ✅ Inserted Entity Permission: Role r002 - Entity e009 - Permission Read
2025-05-12 07:26:04 | ✅ Inserted Entity Permission: Role r002 - Entity e009 - Permission Create
2025-05-12 07:26:04 | ✅ Inserted Entity Permission: Role r002 - Entity e009 - Permission Update
2025-05-12 07:26:04 | ✅ Inserted Entity Permission: Role r002 - Entity e010 - Permission Read
2025-05-12 07:26:04 | ✅ Inserted Entity Permission: Role r002 - Entity e010 - Permission Create
2025-05-12 07:26:04 | ✅ Inserted Entity Permission: Role r002 - Entity e010 - Permission Update
2025-05-12 07:26:04 | ✅ Inserted Entity Permission: Role r002 - Entity e011 - Permission Read
2025-05-12 07:26:04 | ✅ Inserted Entity Permission: Role r002 - Entity e011 - Permission Create
2025-05-12 07:26:04 | ✅ Inserted Entity Permission: Role r002 - Entity e011 - Permission Update
2025-05-12 07:26:04 | ✅ Inserted Entity Permission: Role r002 - Entity e012 - Permission Read
2025-05-12 07:26:04 | ✅ Inserted Entity Permission: Role r002 - Entity e012 - Permission Create
2025-05-12 07:26:04 | ✅ Inserted Entity Permission: Role r002 - Entity e012 - Permission Update
2025-05-12 07:26:04 | ✅ Inserted Entity Permission: Role r003 - Entity e009 - Permission Read
2025-05-12 07:26:04 | ✅ Inserted Entity Permission: Role r003 - Entity e009 - Permission Create
2025-05-12 07:26:04 | ✅ Inserted Entity Permission: Role r003 - Entity e010 - Permission Read
2025-05-12 07:26:04 | ✅ Inserted Entity Permission: Role r003 - Entity e010 - Permission Create
2025-05-12 07:26:04 | ✅ Inserted Entity Permission: Role r003 - Entity e011 - Permission Read
2025-05-12 07:26:04 | ✅ Inserted Entity Permission: Role r003 - Entity e011 - Permission Create
2025-05-12 07:26:04 | ✅ Inserted Entity Permission: Role r004 - Entity e009 - Permission Read
2025-05-12 07:26:04 | ✅ Inserted Entity Permission: Role r004 - Entity e009 - Permission Create
2025-05-12 07:26:04 | ✅ Inserted Entity Permission: Role r004 - Entity e009 - Permission Update
2025-05-12 07:26:04 | ✅ Inserted Entity Permission: Role r004 - Entity e009 - Permission Delete
2025-05-12 07:26:04 | ✅ Inserted Entity Permission: Role r004 - Entity e010 - Permission Read
2025-05-12 07:26:04 | ✅ Inserted Entity Permission: Role r004 - Entity e010 - Permission Create
2025-05-12 07:26:04 | ✅ Inserted Entity Permission: Role r004 - Entity e010 - Permission Update
2025-05-12 07:26:04 | ✅ Inserted Entity Permission: Role r004 - Entity e010 - Permission Delete
2025-05-12 07:26:04 | ✅ Inserted Entity Permission: Role r004 - Entity e011 - Permission Read
2025-05-12 07:26:04 | ✅ Inserted Entity Permission: Role r004 - Entity e011 - Permission Create
2025-05-12 07:26:04 | ✅ Inserted Entity Permission: Role r004 - Entity e011 - Permission Update
2025-05-12 07:26:04 | ✅ Inserted Entity Permission: Role r004 - Entity e011 - Permission Delete
2025-05-12 07:26:04 | ✅ Inserted Entity Permission: Role r004 - Entity e012 - Permission Read
2025-05-12 07:26:04 | ✅ Inserted Entity Permission: Role r004 - Entity e012 - Permission Create
2025-05-12 07:26:04 | ✅ Inserted Entity Permission: Role r004 - Entity e012 - Permission Update
2025-05-12 07:26:04 | ✅ Inserted Entity Permission: Role r004 - Entity e012 - Permission Delete
2025-05-12 07:26:04 | ✅ Inserted Entity Permission: Role r004 - Entity e013 - Permission Read
2025-05-12 07:26:04 | ✅ Inserted Entity Permission: Role r004 - Entity e013 - Permission Create
2025-05-12 07:26:04 | ✅ Inserted Entity Permission: Role r004 - Entity e013 - Permission Update
2025-05-12 07:26:04 | ✅ Inserted Entity Permission: Role r004 - Entity e013 - Permission Delete
2025-05-12 07:26:04 | 
➡️ Inserting Objective Permissions...
2025-05-12 07:26:04 | ✅ Inserted Objective Permission: Role r001 - Objective go002.lo008 - Permission Execute
2025-05-12 07:26:04 | ✅ Inserted Objective Permission: Role r002 - Objective go002.lo009 - Permission Execute
2025-05-12 07:26:04 | ✅ Inserted Objective Permission: Role r002 - Objective go002.lo010 - Permission Execute
2025-05-12 07:26:04 | ✅ Inserted Objective Permission: Role r003 - Objective go002.lo009 - Permission Execute
2025-05-12 07:26:04 | ✅ Inserted Objective Permission: Role r004 - Objective go002.lo008 - Permission Execute
2025-05-12 07:26:04 | ✅ Inserted Objective Permission: Role r004 - Objective go002.lo009 - Permission Execute
2025-05-12 07:26:04 | ✅ Inserted Objective Permission: Role r004 - Objective go002.lo010 - Permission Execute
2025-05-12 07:26:04 | ✅ Inserted Objective Permission: Role r004 - Objective go002.lo011 - Permission Execute
2025-05-12 07:26:04 | 
==== STEP 5: INSERTING GLOBAL OBJECTIVES ====
2025-05-12 07:26:04 | 
➡️ Inserting Global Objectives...
2025-05-12 07:26:04 | ✅ Inserted Global Objective: go002 - patient care workflow
2025-05-12 07:26:04 | ✅ Created Input Stack for go002
2025-05-12 07:26:04 | ⚠️ Error processing input stack: Missing entity_reference for input item in001
2025-05-12 07:26:04 | ✅ Created Output Stack for go002
2025-05-12 07:26:04 | ❌ Error inserting output item out001: insert or update on table "output_items" violates foreign key constraint "output_items_output_entity_fkey"
DETAIL:  Key (output_entity)=() is not present in table "entities".

2025-05-12 07:26:04 | ❌ Error inserting output item out002: insert or update on table "output_items" violates foreign key constraint "output_items_output_entity_fkey"
DETAIL:  Key (output_entity)=() is not present in table "entities".

2025-05-12 07:26:04 | ✅ Created Data Mapping Stack for go002
2025-05-12 07:26:04 | ✅ Inserted Data Mapping: dm001
2025-05-12 07:26:04 | 
==== STEP 6: INSERTING LOCAL OBJECTIVES ====
2025-05-12 07:26:04 | 
➡️ Inserting base Local Objectives...
2025-05-12 07:26:04 | ✅ Inserted Local Objective: lo008 - Patient Registration
2025-05-12 07:26:04 | ✅ Inserted Local Objective: lo009 - Appointment Scheduling
2025-05-12 07:26:04 | ✅ Inserted Local Objective: lo010 - Medical Records and Prescriptions
2025-05-12 07:26:04 | ✅ Inserted Local Objective: lo011 - Lab Reports and Results
2025-05-12 07:26:04 | 
➡️ Inserting Local Objective relationships and data...
2025-05-12 07:26:04 | ✅ Inserted Execution Pathway: lo008 -> lo009
2025-05-12 07:26:04 | ✅ Created Agent Stack for lo008
2025-05-12 07:26:04 | DEBUG: Role ID from YAML: 'r004'
2025-05-12 07:26:04 | ✅ Inserted 1 Agent Rights for role r004 in stack for lo008
2025-05-12 07:26:04 | 🔍 Creating input stack for lo008...
2025-05-12 07:26:04 | 🔧 insert_and_get_stack_id called for lo_input_stack, lo_id=lo008
2025-05-12 07:26:04 | 🔧 Executing SQL: 
        INSERT INTO lo_input_stack (lo_id, description, created_at, updated_at)
        VALUES (%s, %s, %s, %s)
        ON CONFLICT (lo_id) DO NOTHING
        RETURNING id
    
2025-05-12 07:26:04 | 🔧 Parameters: (lo008, 'Capture patient registration details', 2025-05-12 07:26:04.338061, 2025-05-12 07:26:04.338061)
2025-05-12 07:26:04 | 🔧 Successfully got ID: 2119
2025-05-12 07:26:04 | ✅ Created or retrieved Input Stack for lo008 with ID: 2119
2025-05-12 07:26:04 | 🔄 Assigned correct slot_id for in001: e009.at041.in001
2025-05-12 07:26:04 | ✅ Inserted Input Item in001 for lo008
2025-05-12 07:26:04 | ✅ Inserted Validation for in001: validate_required
2025-05-12 07:26:04 | 🔄 Assigned correct slot_id for in002: e009.at042.in002
2025-05-12 07:26:04 | ✅ Inserted Input Item in002 for lo008
2025-05-12 07:26:04 | ✅ Inserted Validation for in002: validate_required
2025-05-12 07:26:04 | 🔄 Assigned correct slot_id for in003: e009.at043.in003
2025-05-12 07:26:04 | ✅ Inserted Input Item in003 for lo008
2025-05-12 07:26:04 | ✅ Inserted Validation for in003: validate_required
2025-05-12 07:26:04 | 🔄 Assigned correct slot_id for in004: e009.at044.in004
2025-05-12 07:26:04 | ✅ Inserted Input Item in004 for lo008
2025-05-12 07:26:04 | ✅ Inserted Validation for in004: validate_required
2025-05-12 07:26:04 | 🔄 Assigned correct slot_id for in005: e009.at045.in005
2025-05-12 07:26:04 | ✅ Inserted Input Item in005 for lo008
2025-05-12 07:26:04 | ✅ Inserted Validation for in005: validate_required
2025-05-12 07:26:04 | ✅ Inserted Validation for in005: enum_check
2025-05-12 07:26:04 | 🔄 Assigned correct slot_id for in006: e009.at098.in006
2025-05-12 07:26:04 | ✅ Inserted Input Item in006 for lo008
2025-05-12 07:26:04 | ✅ Inserted Validation for in006: validate_required
2025-05-12 07:26:04 | 🔧 insert_and_get_stack_id called for lo_output_stack, lo_id=lo008
2025-05-12 07:26:04 | 🔧 Executing SQL: 
        INSERT INTO lo_output_stack (lo_id, description, created_at, updated_at)
        VALUES (%s, %s, %s, %s)
        ON CONFLICT (lo_id) DO NOTHING
        RETURNING id
    
2025-05-12 07:26:04 | 🔧 Parameters: (lo008, 'Patient registration completed', 2025-05-12 07:26:04.338061, 2025-05-12 07:26:04.338061)
2025-05-12 07:26:04 | 🔧 Successfully got ID: 2086
2025-05-12 07:26:04 | ✅ Created or retrieved Output Stack for lo008
2025-05-12 07:26:04 | ✅ Inserted Output Item out001 for lo008
2025-05-12 07:26:04 | ✅ Inserted Output Item out002 for lo008
2025-05-12 07:26:04 | ✅ Inserted Output Item out003 for lo008
2025-05-12 07:26:04 | ✅ Inserted Output Item out004 for lo008
2025-05-12 07:26:04 | ✅ Inserted Output Item out005 for lo008
2025-05-12 07:26:04 | ✅ Inserted Output Item out006 for lo008
2025-05-12 07:26:04 | ✅ Inserted Output Item out007 for lo008
2025-05-12 07:26:04 | ✅ Inserted Execution Pathway: lo009 -> lo010
2025-05-12 07:26:04 | ✅ Created Agent Stack for lo009
2025-05-12 07:26:04 | DEBUG: Role ID from YAML: 'r002'
2025-05-12 07:26:04 | ✅ Inserted 1 Agent Rights for role r002 in stack for lo009
2025-05-12 07:26:04 | DEBUG: Role ID from YAML: 'r003'
2025-05-12 07:26:04 | ✅ Inserted 1 Agent Rights for role r003 in stack for lo009
2025-05-12 07:26:04 | 🔍 Creating input stack for lo009...
2025-05-12 07:26:04 | 🔧 insert_and_get_stack_id called for lo_input_stack, lo_id=lo009
2025-05-12 07:26:04 | 🔧 Executing SQL: 
        INSERT INTO lo_input_stack (lo_id, description, created_at, updated_at)
        VALUES (%s, %s, %s, %s)
        ON CONFLICT (lo_id) DO NOTHING
        RETURNING id
    
2025-05-12 07:26:04 | 🔧 Parameters: (lo009, 'Schedule patient appointment', 2025-05-12 07:26:04.338061, 2025-05-12 07:26:04.338061)
2025-05-12 07:26:04 | 🔧 Successfully got ID: 2120
2025-05-12 07:26:04 | ✅ Created or retrieved Input Stack for lo009 with ID: 2120
2025-05-12 07:26:04 | ⚠️ Could not find attribute for 'Appointment ID' in entity e010
2025-05-12 07:26:04 | ✅ Inserted Input Item in001 for lo009
2025-05-12 07:26:04 | ✅ Inserted Validation for in001: validate_required
2025-05-12 07:26:04 | ⚠️ Could not find attribute for 'Patient ID' in entity e010
2025-05-12 07:26:04 | ✅ Inserted Input Item in002 for lo009
2025-05-12 07:26:04 | ⚠️ Could not find attribute for 'Doctor ID' in entity e010
2025-05-12 07:26:04 | ✅ Inserted Input Item in003 for lo009
2025-05-12 07:26:04 | ✅ Inserted Validation for in003: validate_required
2025-05-12 07:26:04 | ⚠️ Could not find attribute for 'Appointment Date' in entity e010
2025-05-12 07:26:04 | ✅ Inserted Input Item in004 for lo009
2025-05-12 07:26:04 | ✅ Inserted Validation for in004: validate_required
2025-05-12 07:26:04 | ⚠️ Could not find attribute for 'Appointment Time' in entity e010
2025-05-12 07:26:04 | ✅ Inserted Input Item in005 for lo009
2025-05-12 07:26:04 | ✅ Inserted Validation for in005: validate_required
2025-05-12 07:26:04 | ⚠️ Could not find attribute for 'Reason' in entity e010
2025-05-12 07:26:04 | ✅ Inserted Input Item in006 for lo009
2025-05-12 07:26:04 | ✅ Inserted Validation for in006: validate_required
2025-05-12 07:26:04 | ⚠️ Could not find attribute for 'Status' in entity e010
2025-05-12 07:26:04 | ✅ Inserted Input Item in007 for lo009
2025-05-12 07:26:04 | ✅ Inserted Validation for in007: validate_required
2025-05-12 07:26:04 | ✅ Inserted Validation for in007: enum_check
2025-05-12 07:26:04 | 🔧 insert_and_get_stack_id called for lo_output_stack, lo_id=lo009
2025-05-12 07:26:04 | 🔧 Executing SQL: 
        INSERT INTO lo_output_stack (lo_id, description, created_at, updated_at)
        VALUES (%s, %s, %s, %s)
        ON CONFLICT (lo_id) DO NOTHING
        RETURNING id
    
2025-05-12 07:26:04 | 🔧 Parameters: (lo009, 'Appointment scheduled', 2025-05-12 07:26:04.338061, 2025-05-12 07:26:04.338061)
2025-05-12 07:26:04 | 🔧 Successfully got ID: 2087
2025-05-12 07:26:04 | ✅ Created or retrieved Output Stack for lo009
2025-05-12 07:26:04 | ✅ Inserted Output Item out001 for lo009
2025-05-12 07:26:04 | ✅ Inserted Output Item out002 for lo009
2025-05-12 07:26:04 | ✅ Inserted Output Item out003 for lo009
2025-05-12 07:26:04 | ✅ Inserted Output Item out004 for lo009
2025-05-12 07:26:04 | ✅ Inserted Output Item out005 for lo009
2025-05-12 07:26:04 | ✅ Inserted Output Item out006 for lo009
2025-05-12 07:26:04 | ✅ Inserted Output Item out007 for lo009
2025-05-12 07:26:04 | ✅ Inserted Output Item out008 for lo009
2025-05-12 07:26:04 | ✅ Inserted Execution Pathway: lo010 -> lo011
2025-05-12 07:26:04 | ✅ Created Agent Stack for lo010
2025-05-12 07:26:04 | DEBUG: Role ID from YAML: 'r002'
2025-05-12 07:26:04 | ✅ Inserted 1 Agent Rights for role r002 in stack for lo010
2025-05-12 07:26:04 | 🔍 Creating input stack for lo010...
2025-05-12 07:26:04 | 🔧 insert_and_get_stack_id called for lo_input_stack, lo_id=lo010
2025-05-12 07:26:04 | 🔧 Executing SQL: 
        INSERT INTO lo_input_stack (lo_id, description, created_at, updated_at)
        VALUES (%s, %s, %s, %s)
        ON CONFLICT (lo_id) DO NOTHING
        RETURNING id
    
2025-05-12 07:26:04 | 🔧 Parameters: (lo010, 'Create medical record and prescription', 2025-05-12 07:26:04.338061, 2025-05-12 07:26:04.338061)
2025-05-12 07:26:04 | 🔧 Successfully got ID: 2121
2025-05-12 07:26:04 | ✅ Created or retrieved Input Stack for lo010 with ID: 2121
2025-05-12 07:26:04 | ⚠️ Could not find attribute for 'Record ID' in entity e011
2025-05-12 07:26:04 | ✅ Inserted Input Item in001 for lo010
2025-05-12 07:26:04 | ✅ Inserted Validation for in001: validate_required
2025-05-12 07:26:04 | ⚠️ Could not find attribute for 'Patient ID' in entity e011
2025-05-12 07:26:04 | ✅ Inserted Input Item in002 for lo010
2025-05-12 07:26:04 | 🔄 Assigned correct slot_id for in003: e011.at111.in003
2025-05-12 07:26:04 | ✅ Inserted Input Item in003 for lo010
2025-05-12 07:26:04 | 🔄 Assigned correct slot_id for in004: e011.at112.in004
2025-05-12 07:26:04 | ✅ Inserted Input Item in004 for lo010
2025-05-12 07:26:04 | ✅ Inserted Validation for in004: validate_required
2025-05-12 07:26:04 | 🔄 Assigned correct slot_id for in005: e011.at113.in005
2025-05-12 07:26:04 | ✅ Inserted Input Item in005 for lo010
2025-05-12 07:26:04 | ✅ Inserted Validation for in005: validate_required
2025-05-12 07:26:04 | 🔄 Assigned correct slot_id for in006: e011.at114.in006
2025-05-12 07:26:04 | ✅ Inserted Input Item in006 for lo010
2025-05-12 07:26:04 | 🔄 Assigned correct slot_id for in007: e012.at118.in007
2025-05-12 07:26:04 | ✅ Inserted Input Item in007 for lo010
2025-05-12 07:26:04 | ✅ Inserted Validation for in007: validate_required
2025-05-12 07:26:04 | 🔄 Assigned correct slot_id for in008: e012.at122.in008
2025-05-12 07:26:04 | ✅ Inserted Input Item in008 for lo010
2025-05-12 07:26:04 | ✅ Inserted Validation for in008: validate_required
2025-05-12 07:26:04 | 🔄 Assigned correct slot_id for in009: e012.at123.in009
2025-05-12 07:26:04 | ✅ Inserted Input Item in009 for lo010
2025-05-12 07:26:04 | ✅ Inserted Validation for in009: validate_required
2025-05-12 07:26:04 | 🔄 Assigned correct slot_id for in010: e012.at124.in010
2025-05-12 07:26:04 | ✅ Inserted Input Item in010 for lo010
2025-05-12 07:26:04 | ✅ Inserted Validation for in010: validate_required
2025-05-12 07:26:04 | 🔄 Assigned correct slot_id for in011: e012.at125.in011
2025-05-12 07:26:04 | ✅ Inserted Input Item in011 for lo010
2025-05-12 07:26:04 | ✅ Inserted Validation for in011: validate_required
2025-05-12 07:26:04 | 🔧 insert_and_get_stack_id called for lo_output_stack, lo_id=lo010
2025-05-12 07:26:04 | 🔧 Executing SQL: 
        INSERT INTO lo_output_stack (lo_id, description, created_at, updated_at)
        VALUES (%s, %s, %s, %s)
        ON CONFLICT (lo_id) DO NOTHING
        RETURNING id
    
2025-05-12 07:26:04 | 🔧 Parameters: (lo010, 'Medical record and prescription created', 2025-05-12 07:26:04.338061, 2025-05-12 07:26:04.338061)
2025-05-12 07:26:04 | 🔧 Successfully got ID: 2088
2025-05-12 07:26:04 | ✅ Created or retrieved Output Stack for lo010
2025-05-12 07:26:04 | ✅ Inserted Output Item out001 for lo010
2025-05-12 07:26:04 | ✅ Inserted Output Item out002 for lo010
2025-05-12 07:26:04 | ✅ Inserted Output Item out003 for lo010
2025-05-12 07:26:04 | ✅ Inserted Output Item out004 for lo010
2025-05-12 07:26:04 | ✅ Inserted Output Item out005 for lo010
2025-05-12 07:26:04 | ✅ Inserted Output Item out006 for lo010
2025-05-12 07:26:04 | ✅ Inserted Output Item out007 for lo010
2025-05-12 07:26:04 | ✅ Inserted Output Item out008 for lo010
2025-05-12 07:26:04 | ✅ Inserted Output Item out009 for lo010
2025-05-12 07:26:04 | ✅ Inserted Output Item out010 for lo010
2025-05-12 07:26:04 | ✅ Inserted Output Item out011 for lo010
2025-05-12 07:26:04 | ✅ Inserted Output Item out012 for lo010
2025-05-12 07:26:04 | ✅ Inserted Terminal Pathway for lo011
2025-05-12 07:26:04 | ✅ Inserted Execution Pathway (Terminal): lo011
2025-05-12 07:26:04 | ✅ Created Agent Stack for lo011
2025-05-12 07:26:04 | DEBUG: Role ID from YAML: 'r002'
2025-05-12 07:26:04 | ✅ Inserted 1 Agent Rights for role r002 in stack for lo011
2025-05-12 07:26:04 | 🔍 Creating input stack for lo011...
2025-05-12 07:26:04 | 🔧 insert_and_get_stack_id called for lo_input_stack, lo_id=lo011
2025-05-12 07:26:04 | 🔧 Executing SQL: 
        INSERT INTO lo_input_stack (lo_id, description, created_at, updated_at)
        VALUES (%s, %s, %s, %s)
        ON CONFLICT (lo_id) DO NOTHING
        RETURNING id
    
2025-05-12 07:26:04 | 🔧 Parameters: (lo011, 'Create lab report and results', 2025-05-12 07:26:04.338061, 2025-05-12 07:26:04.338061)
2025-05-12 07:26:04 | 🔧 Successfully got ID: 2122
2025-05-12 07:26:04 | ✅ Created or retrieved Input Stack for lo011 with ID: 2122
2025-05-12 07:26:04 | ⚠️ Could not find attribute for 'Lab Report ID' in entity e013
2025-05-12 07:26:04 | ✅ Inserted Input Item in001 for lo011
2025-05-12 07:26:04 | ✅ Inserted Validation for in001: validate_required
2025-05-12 07:26:04 | 🔄 Assigned correct slot_id for in002: e013.at129.in002
2025-05-12 07:26:04 | ✅ Inserted Input Item in002 for lo011
2025-05-12 07:26:04 | 🔄 Assigned correct slot_id for in003: e013.at130.in003
2025-05-12 07:26:04 | ✅ Inserted Input Item in003 for lo011
2025-05-12 07:26:04 | 🔄 Assigned correct slot_id for in004: e013.at131.in004
2025-05-12 07:26:04 | ✅ Inserted Input Item in004 for lo011
2025-05-12 07:26:04 | ✅ Inserted Validation for in004: validate_required
2025-05-12 07:26:04 | 🔄 Assigned correct slot_id for in005: e013.at132.in005
2025-05-12 07:26:04 | ✅ Inserted Input Item in005 for lo011
2025-05-12 07:26:04 | ✅ Inserted Validation for in005: validate_required
2025-05-12 07:26:04 | 🔄 Assigned correct slot_id for in006: e013.at133.in006
2025-05-12 07:26:04 | ✅ Inserted Input Item in006 for lo011
2025-05-12 07:26:04 | ✅ Inserted Validation for in006: validate_required
2025-05-12 07:26:04 | 🔧 insert_and_get_stack_id called for lo_output_stack, lo_id=lo011
2025-05-12 07:26:04 | 🔧 Executing SQL: 
        INSERT INTO lo_output_stack (lo_id, description, created_at, updated_at)
        VALUES (%s, %s, %s, %s)
        ON CONFLICT (lo_id) DO NOTHING
        RETURNING id
    
2025-05-12 07:26:04 | 🔧 Parameters: (lo011, 'Lab report and results created', 2025-05-12 07:26:04.338061, 2025-05-12 07:26:04.338061)
2025-05-12 07:26:04 | 🔧 Successfully got ID: 2089
2025-05-12 07:26:04 | ✅ Created or retrieved Output Stack for lo011
2025-05-12 07:26:04 | ✅ Inserted Output Item out001 for lo011
2025-05-12 07:26:04 | ✅ Inserted Output Item out002 for lo011
2025-05-12 07:26:04 | ✅ Inserted Output Item out003 for lo011
2025-05-12 07:26:04 | ✅ Inserted Output Item out004 for lo011
2025-05-12 07:26:04 | ✅ Inserted Output Item out005 for lo011
2025-05-12 07:26:04 | ✅ Inserted Output Item out006 for lo011
2025-05-12 07:26:04 | ✅ Inserted Output Item out007 for lo011
2025-05-12 07:26:04 | 
==== VALIDATING DATA INTEGRITY ====
2025-05-12 07:26:04 | 
==== VALIDATING NESTED FUNCTIONS ====
2025-05-12 07:26:04 | ✅ All nested functions validated successfully
2025-05-12 07:26:04 | ✅ MIGRATION COMPLETE
2025-05-12 07:26:04 | 
🏁 Migration of data to PostgreSQL schema workflow_runtime complete!
2025-05-12 08:46:51 | ➡️ Connecting to MongoDB at mongodb://localhost:27017/...
2025-05-12 08:46:51 | ➡️ Retrieving draft documents from workflow_system.workflow...
2025-05-12 08:46:51 | ✅ Found 1 draft document(s) to process.
2025-05-12 08:46:51 | ➡️ Using tenant ID: t001
2025-05-12 08:46:51 | ➡️ Connecting to PostgreSQL...
2025-05-12 08:46:51 | ✅ Connected to PostgreSQL
2025-05-12 08:46:51 | 🚀 STARTING WORKFLOW DATA MIGRATION
2025-05-12 08:46:51 | 
==== STEP 1: INSERTING BASE ENTITIES ====
2025-05-12 08:46:51 | ➡️ Inserting Tenant: dermatologylims001...
2025-05-12 08:46:51 | ✅ Inserted Tenant: dermatologylims001
2025-05-12 08:46:51 | 
➡️ Ensuring Standard Permission Types...
2025-05-12 08:46:51 | ✅ Ensured Permission Type: read
2025-05-12 08:46:51 | ✅ Ensured Permission Type: create
2025-05-12 08:46:51 | ✅ Ensured Permission Type: update
2025-05-12 08:46:51 | ✅ Ensured Permission Type: delete
2025-05-12 08:46:51 | ✅ Ensured Permission Type: execute
2025-05-12 08:46:51 | 
➡️ Inserting Additional Permission Types from YAML...
2025-05-12 08:46:51 | 
➡️ Inserting Roles...
2025-05-12 08:46:51 | ✅ Inserted Role: r001 - Lab Technician
2025-05-12 08:46:51 | ✅ Inserted Role: r002 - Lab Manager
2025-05-12 08:46:51 | ✅ Inserted Role: r003 - Dermatologist
2025-05-12 08:46:51 | ✅ Inserted Role: r004 - Administrator
2025-05-12 08:46:51 | 
➡️ Inserting Entities...
2025-05-12 08:46:51 | ✅ Inserted Entity: e014 - SpecimenSample
2025-05-12 08:46:51 | ✅ Inserted Entity: e007 - User
2025-05-12 08:46:51 | ✅ Inserted Entity: e008 - Role
2025-05-12 08:46:51 | 
==== STEP 2: INSERTING ENTITY ATTRIBUTES AND METADATA ====
2025-05-12 08:46:51 | 
➡️ Inserting Entity Attributes...
2025-05-12 08:46:51 | ✅ Inserted Entity Attribute: e014.at137
2025-05-12 08:46:51 | ✅ Inserted Entity Attribute: e014.at138
2025-05-12 08:46:51 | ✅ Inserted Entity Attribute: e014.at139
2025-05-12 08:46:51 | ✅ Inserted Entity Attribute: e014.at140
2025-05-12 08:46:51 | ✅ Inserted Entity Attribute: e014.at141
2025-05-12 08:46:51 | ✅ Inserted Entity Attribute: e014.at142
2025-05-12 08:46:51 | ✅ Inserted Entity Attribute: e014.at143
2025-05-12 08:46:51 | ✅ Inserted Entity Attribute: e014.at144
2025-05-12 08:46:51 | ✅ Inserted Entity Attribute: e014.at145
2025-05-12 08:46:51 | ⚠️ Attribute already exists: e007.at089
2025-05-12 08:46:51 | ⚠️ Attribute already exists: e007.at090
2025-05-12 08:46:51 | ⚠️ Attribute already exists: e007.at091
2025-05-12 08:46:51 | ⚠️ Attribute already exists: e007.at032
2025-05-12 08:46:51 | ⚠️ Attribute already exists: e007.at094
2025-05-12 08:46:51 | ⚠️ Attribute already exists: e007.at092
2025-05-12 08:46:51 | ⚠️ Attribute already exists: e007.at093
2025-05-12 08:46:51 | ⚠️ Attribute already exists: e008.at095
2025-05-12 08:46:51 | ⚠️ Attribute already exists: e008.at096
2025-05-12 08:46:51 | ⚠️ Attribute already exists: e008.at097
2025-05-12 08:46:51 | ⚠️ Attribute already exists: e008.at059
2025-05-12 08:46:51 | ⚠️ Attribute already exists: e008.at036
2025-05-12 08:46:51 | 
➡️ Generating Entity Attribute Metadata from actual DB inserts...
2025-05-12 08:46:51 | ✅ Inserted Entity Attribute Metadata: e014.at137
2025-05-12 08:46:51 | ✅ Inserted Entity Attribute Metadata: e014.at138
2025-05-12 08:46:51 | ✅ Inserted Entity Attribute Metadata: e014.at139
2025-05-12 08:46:51 | ✅ Inserted Entity Attribute Metadata: e014.at140
2025-05-12 08:46:51 | ✅ Inserted Entity Attribute Metadata: e014.at141
2025-05-12 08:46:51 | ✅ Inserted Entity Attribute Metadata: e014.at142
2025-05-12 08:46:51 | ✅ Inserted Entity Attribute Metadata: e014.at143
2025-05-12 08:46:51 | ✅ Inserted Entity Attribute Metadata: e014.at144
2025-05-12 08:46:51 | ✅ Inserted Entity Attribute Metadata: e014.at145
2025-05-12 08:46:51 | ✅ Inserted Entity Attribute Metadata: e007.at031
2025-05-12 08:46:51 | ✅ Inserted Entity Attribute Metadata: e007.at032
2025-05-12 08:46:51 | ✅ Inserted Entity Attribute Metadata: e007.at033
2025-05-12 08:46:51 | ✅ Inserted Entity Attribute Metadata: e007.at034
2025-05-12 08:46:51 | ✅ Inserted Entity Attribute Metadata: e007.at035
2025-05-12 08:46:51 | ✅ Inserted Entity Attribute Metadata: e007.at061
2025-05-12 08:46:51 | ✅ Inserted Entity Attribute Metadata: e007.at062
2025-05-12 08:46:51 | ✅ Inserted Entity Attribute Metadata: e007.at063
2025-05-12 08:46:51 | ✅ Inserted Entity Attribute Metadata: e007.at064
2025-05-12 08:46:51 | ✅ Inserted Entity Attribute Metadata: e007.at065
2025-05-12 08:46:51 | ✅ Inserted Entity Attribute Metadata: e007.at066
2025-05-12 08:46:51 | ✅ Inserted Entity Attribute Metadata: e007.at067
2025-05-12 08:46:51 | ✅ Inserted Entity Attribute Metadata: e007.at068
2025-05-12 08:46:51 | ✅ Inserted Entity Attribute Metadata: e007.at089
2025-05-12 08:46:51 | ✅ Inserted Entity Attribute Metadata: e007.at090
2025-05-12 08:46:51 | ✅ Inserted Entity Attribute Metadata: e007.at091
2025-05-12 08:46:51 | ✅ Inserted Entity Attribute Metadata: e007.at092
2025-05-12 08:46:51 | ✅ Inserted Entity Attribute Metadata: e007.at093
2025-05-12 08:46:51 | ✅ Inserted Entity Attribute Metadata: e007.at094
2025-05-12 08:46:51 | ✅ Inserted Entity Attribute Metadata: e008.at036
2025-05-12 08:46:51 | ✅ Inserted Entity Attribute Metadata: e008.at037
2025-05-12 08:46:51 | ✅ Inserted Entity Attribute Metadata: e008.at038
2025-05-12 08:46:51 | ✅ Inserted Entity Attribute Metadata: e008.at039
2025-05-12 08:46:51 | ✅ Inserted Entity Attribute Metadata: e008.at040
2025-05-12 08:46:51 | ✅ Inserted Entity Attribute Metadata: e008.at053
2025-05-12 08:46:51 | ✅ Inserted Entity Attribute Metadata: e008.at054
2025-05-12 08:46:51 | ✅ Inserted Entity Attribute Metadata: e008.at055
2025-05-12 08:46:51 | ✅ Inserted Entity Attribute Metadata: e008.at056
2025-05-12 08:46:51 | ✅ Inserted Entity Attribute Metadata: e008.at057
2025-05-12 08:46:51 | ✅ Inserted Entity Attribute Metadata: e008.at058
2025-05-12 08:46:51 | ✅ Inserted Entity Attribute Metadata: e008.at059
2025-05-12 08:46:51 | ✅ Inserted Entity Attribute Metadata: e008.at060
2025-05-12 08:46:51 | ✅ Inserted Entity Attribute Metadata: e008.at069
2025-05-12 08:46:51 | ✅ Inserted Entity Attribute Metadata: e008.at070
2025-05-12 08:46:51 | ✅ Inserted Entity Attribute Metadata: e008.at071
2025-05-12 08:46:51 | ✅ Inserted Entity Attribute Metadata: e008.at072
2025-05-12 08:46:51 | ✅ Inserted Entity Attribute Metadata: e008.at073
2025-05-12 08:46:51 | ✅ Inserted Entity Attribute Metadata: e008.at074
2025-05-12 08:46:51 | ✅ Inserted Entity Attribute Metadata: e008.at075
2025-05-12 08:46:51 | ✅ Inserted Entity Attribute Metadata: e008.at076
2025-05-12 08:46:51 | ✅ Inserted Entity Attribute Metadata: e008.at077
2025-05-12 08:46:51 | ✅ Inserted Entity Attribute Metadata: e008.at078
2025-05-12 08:46:51 | ✅ Inserted Entity Attribute Metadata: e008.at095
2025-05-12 08:46:51 | ✅ Inserted Entity Attribute Metadata: e008.at096
2025-05-12 08:46:51 | ✅ Inserted Entity Attribute Metadata: e008.at097
2025-05-12 08:46:51 | 🔁 Reconstructed attribute maps successfully.
2025-05-12 08:46:51 | 
==== STEP 3: INSERTING ENTITY RELATIONSHIPS ====
2025-05-12 08:46:51 | 
➡️ Inserting Entity Relationships...
2025-05-12 08:46:51 | 
==== STEP 4: INSERTING PERMISSIONS ====
2025-05-12 08:46:51 | 
➡️ Inserting Entity Permissions...
2025-05-12 08:46:51 | ✅ Inserted Entity Permission: Role r001 - Entity e014 - Permission Read
2025-05-12 08:46:51 | ✅ Inserted Entity Permission: Role r001 - Entity e014 - Permission Create
2025-05-12 08:46:51 | ✅ Inserted Entity Permission: Role r001 - Entity e014 - Permission Update
2025-05-12 08:46:51 | ✅ Inserted Entity Permission: Role r001 - Entity e002 - Permission Read
2025-05-12 08:46:51 | ✅ Inserted Entity Permission: Role r001 - Entity e002 - Permission Create
2025-05-12 08:46:51 | ✅ Inserted Entity Permission: Role r001 - Entity e003 - Permission Read
2025-05-12 08:46:51 | ✅ Inserted Entity Permission: Role r001 - Entity e003 - Permission Create
2025-05-12 08:46:51 | ✅ Inserted Entity Permission: Role r001 - Entity e004 - Permission Read
2025-05-12 08:46:51 | ✅ Inserted Entity Permission: Role r001 - Entity e005 - Permission Read
2025-05-12 08:46:51 | ✅ Inserted Entity Permission: Role r001 - Entity e006 - Permission Read
2025-05-12 08:46:51 | ✅ Inserted Entity Permission: Role r001 - Entity e007 - Permission Read
2025-05-12 08:46:51 | ✅ Inserted Entity Permission: Role r001 - Entity e008 - Permission Read
2025-05-12 08:46:51 | ✅ Inserted Entity Permission: Role r002 - Entity e014 - Permission Read
2025-05-12 08:46:51 | ✅ Inserted Entity Permission: Role r002 - Entity e014 - Permission Create
2025-05-12 08:46:51 | ✅ Inserted Entity Permission: Role r002 - Entity e014 - Permission Update
2025-05-12 08:46:51 | ✅ Inserted Entity Permission: Role r002 - Entity e002 - Permission Read
2025-05-12 08:46:51 | ✅ Inserted Entity Permission: Role r002 - Entity e002 - Permission Create
2025-05-12 08:46:51 | ✅ Inserted Entity Permission: Role r002 - Entity e002 - Permission Update
2025-05-12 08:46:51 | ✅ Inserted Entity Permission: Role r002 - Entity e003 - Permission Read
2025-05-12 08:46:51 | ✅ Inserted Entity Permission: Role r002 - Entity e003 - Permission Create
2025-05-12 08:46:51 | ✅ Inserted Entity Permission: Role r002 - Entity e003 - Permission Update
2025-05-12 08:46:51 | ✅ Inserted Entity Permission: Role r002 - Entity e004 - Permission Read
2025-05-12 08:46:51 | ✅ Inserted Entity Permission: Role r002 - Entity e004 - Permission Create
2025-05-12 08:46:51 | ✅ Inserted Entity Permission: Role r002 - Entity e004 - Permission Update
2025-05-12 08:46:51 | ✅ Inserted Entity Permission: Role r002 - Entity e005 - Permission Read
2025-05-12 08:46:51 | ✅ Inserted Entity Permission: Role r002 - Entity e005 - Permission Create
2025-05-12 08:46:51 | ✅ Inserted Entity Permission: Role r002 - Entity e005 - Permission Update
2025-05-12 08:46:51 | ✅ Inserted Entity Permission: Role r002 - Entity e006 - Permission Read
2025-05-12 08:46:51 | ✅ Inserted Entity Permission: Role r002 - Entity e006 - Permission Create
2025-05-12 08:46:51 | ✅ Inserted Entity Permission: Role r002 - Entity e006 - Permission Update
2025-05-12 08:46:51 | ✅ Inserted Entity Permission: Role r002 - Entity e007 - Permission Read
2025-05-12 08:46:51 | ✅ Inserted Entity Permission: Role r002 - Entity e008 - Permission Read
2025-05-12 08:46:51 | ✅ Inserted Entity Permission: Role r003 - Entity e014 - Permission Read
2025-05-12 08:46:51 | ✅ Inserted Entity Permission: Role r003 - Entity e002 - Permission Read
2025-05-12 08:46:51 | ✅ Inserted Entity Permission: Role r003 - Entity e002 - Permission Create
2025-05-12 08:46:51 | ✅ Inserted Entity Permission: Role r003 - Entity e003 - Permission Read
2025-05-12 08:46:51 | ✅ Inserted Entity Permission: Role r003 - Entity e004 - Permission Read
2025-05-12 08:46:51 | ✅ Inserted Entity Permission: Role r003 - Entity e004 - Permission Create
2025-05-12 08:46:51 | ✅ Inserted Entity Permission: Role r003 - Entity e004 - Permission Update
2025-05-12 08:46:51 | ✅ Inserted Entity Permission: Role r003 - Entity e005 - Permission Read
2025-05-12 08:46:51 | ✅ Inserted Entity Permission: Role r003 - Entity e006 - Permission Read
2025-05-12 08:46:51 | ✅ Inserted Entity Permission: Role r003 - Entity e007 - Permission Read
2025-05-12 08:46:51 | ✅ Inserted Entity Permission: Role r003 - Entity e008 - Permission Read
2025-05-12 08:46:51 | ✅ Inserted Entity Permission: Role r004 - Entity e014 - Permission Read
2025-05-12 08:46:51 | ✅ Inserted Entity Permission: Role r004 - Entity e014 - Permission Create
2025-05-12 08:46:51 | ✅ Inserted Entity Permission: Role r004 - Entity e014 - Permission Update
2025-05-12 08:46:51 | ✅ Inserted Entity Permission: Role r004 - Entity e014 - Permission Delete
2025-05-12 08:46:51 | ✅ Inserted Entity Permission: Role r004 - Entity e002 - Permission Read
2025-05-12 08:46:51 | ✅ Inserted Entity Permission: Role r004 - Entity e002 - Permission Create
2025-05-12 08:46:51 | ✅ Inserted Entity Permission: Role r004 - Entity e002 - Permission Update
2025-05-12 08:46:51 | ✅ Inserted Entity Permission: Role r004 - Entity e002 - Permission Delete
2025-05-12 08:46:51 | ✅ Inserted Entity Permission: Role r004 - Entity e003 - Permission Read
2025-05-12 08:46:51 | ✅ Inserted Entity Permission: Role r004 - Entity e003 - Permission Create
2025-05-12 08:46:51 | ✅ Inserted Entity Permission: Role r004 - Entity e003 - Permission Update
2025-05-12 08:46:51 | ✅ Inserted Entity Permission: Role r004 - Entity e003 - Permission Delete
2025-05-12 08:46:51 | ✅ Inserted Entity Permission: Role r004 - Entity e004 - Permission Read
2025-05-12 08:46:51 | ✅ Inserted Entity Permission: Role r004 - Entity e004 - Permission Create
2025-05-12 08:46:51 | ✅ Inserted Entity Permission: Role r004 - Entity e004 - Permission Update
2025-05-12 08:46:51 | ✅ Inserted Entity Permission: Role r004 - Entity e004 - Permission Delete
2025-05-12 08:46:51 | ✅ Inserted Entity Permission: Role r004 - Entity e005 - Permission Read
2025-05-12 08:46:51 | ✅ Inserted Entity Permission: Role r004 - Entity e005 - Permission Create
2025-05-12 08:46:51 | ✅ Inserted Entity Permission: Role r004 - Entity e005 - Permission Update
2025-05-12 08:46:51 | ✅ Inserted Entity Permission: Role r004 - Entity e005 - Permission Delete
2025-05-12 08:46:51 | ✅ Inserted Entity Permission: Role r004 - Entity e006 - Permission Read
2025-05-12 08:46:51 | ✅ Inserted Entity Permission: Role r004 - Entity e006 - Permission Create
2025-05-12 08:46:51 | ✅ Inserted Entity Permission: Role r004 - Entity e006 - Permission Update
2025-05-12 08:46:51 | ✅ Inserted Entity Permission: Role r004 - Entity e006 - Permission Delete
2025-05-12 08:46:51 | ✅ Inserted Entity Permission: Role r004 - Entity e007 - Permission Read
2025-05-12 08:46:51 | ✅ Inserted Entity Permission: Role r004 - Entity e007 - Permission Create
2025-05-12 08:46:51 | ✅ Inserted Entity Permission: Role r004 - Entity e007 - Permission Update
2025-05-12 08:46:51 | ✅ Inserted Entity Permission: Role r004 - Entity e007 - Permission Delete
2025-05-12 08:46:51 | ✅ Inserted Entity Permission: Role r004 - Entity e008 - Permission Read
2025-05-12 08:46:51 | ✅ Inserted Entity Permission: Role r004 - Entity e008 - Permission Create
2025-05-12 08:46:51 | ✅ Inserted Entity Permission: Role r004 - Entity e008 - Permission Update
2025-05-12 08:46:51 | ✅ Inserted Entity Permission: Role r004 - Entity e008 - Permission Delete
2025-05-12 08:46:51 | 
➡️ Inserting Objective Permissions...
2025-05-12 08:46:51 | ✅ Inserted Objective Permission: Role r001 - Objective go003.lo012 - Permission Execute
2025-05-12 08:46:51 | ✅ Inserted Objective Permission: Role r001 - Objective go003.lo002 - Permission Execute
2025-05-12 08:46:51 | ✅ Inserted Objective Permission: Role r002 - Objective go003.lo012 - Permission Execute
2025-05-12 08:46:51 | ✅ Inserted Objective Permission: Role r002 - Objective go003.lo002 - Permission Execute
2025-05-12 08:46:51 | ✅ Inserted Objective Permission: Role r002 - Objective go003.lo003 - Permission Execute
2025-05-12 08:46:51 | ✅ Inserted Objective Permission: Role r003 - Objective go003.lo004 - Permission Execute
2025-05-12 08:46:51 | ✅ Inserted Objective Permission: Role r004 - Objective go003.lo012 - Permission Execute
2025-05-12 08:46:51 | ✅ Inserted Objective Permission: Role r004 - Objective go003.lo002 - Permission Execute
2025-05-12 08:46:51 | ✅ Inserted Objective Permission: Role r004 - Objective go003.lo003 - Permission Execute
2025-05-12 08:46:51 | ✅ Inserted Objective Permission: Role r004 - Objective go003.lo004 - Permission Execute
2025-05-12 08:46:51 | 
==== STEP 5: INSERTING GLOBAL OBJECTIVES ====
2025-05-12 08:46:51 | 
➡️ Inserting Global Objectives...
2025-05-12 08:46:51 | ✅ Inserted Global Objective: go003 - dermatology lab sample processing workflow
2025-05-12 08:46:51 | ✅ Created Input Stack for go003
2025-05-12 08:46:51 | ⚠️ Error processing input stack: Missing entity_reference for input item in001
2025-05-12 08:46:51 | ✅ Created Output Stack for go003
2025-05-12 08:46:51 | ❌ Error inserting output item out001: insert or update on table "output_items" violates foreign key constraint "output_items_output_entity_fkey"
DETAIL:  Key (output_entity)=() is not present in table "entities".

2025-05-12 08:46:51 | ✅ Created Data Mapping Stack for go003
2025-05-12 08:46:51 | ✅ Inserted Data Mapping: map001
2025-05-12 08:46:51 | ✅ Inserted Data Mapping: map002
2025-05-12 08:46:51 | 
==== STEP 6: INSERTING LOCAL OBJECTIVES ====
2025-05-12 08:46:51 | 
➡️ Inserting base Local Objectives...
2025-05-12 08:46:51 | ✅ Inserted Local Objective: lo012 - Register Sample and Create Test Request
2025-05-12 08:46:51 | 
➡️ Inserting Local Objective relationships and data...
2025-05-12 08:46:51 | ✅ Inserted Execution Pathway: lo012 -> lo002
2025-05-12 08:46:51 | ✅ Created Agent Stack for lo012
2025-05-12 08:46:51 | DEBUG: Role ID from YAML: 'r001'
2025-05-12 08:46:51 | ❌ Error processing agent stack for lo012: insert or update on table "agent_rights" violates foreign key constraint "agent_rights_right_id_fkey"
DETAIL:  Key (right_id)=(write) is not present in table "permission_types".

2025-05-12 08:46:51 | ❌ Error processing additional data for lo012: insert or update on table "agent_rights" violates foreign key constraint "agent_rights_right_id_fkey"
DETAIL:  Key (right_id)=(write) is not present in table "permission_types".

2025-05-12 08:46:51 | Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/insert_generator.py", line 2051, in migrate_workflow_data_to_schema
    process_agent_stack(cursor, lo, lo_id, current_time, tenant_id)
  File "/home/<USER>/workflow-system/chat-yaml-builder/insert_generator.py", line 382, in process_agent_stack
    process_agent_rights(cursor, agent, agent_stack_id, lo_id, tenant_id, current_time)
  File "/home/<USER>/workflow-system/chat-yaml-builder/insert_generator.py", line 436, in process_agent_rights
    cursor.execute(
psycopg2.errors.ForeignKeyViolation: insert or update on table "agent_rights" violates foreign key constraint "agent_rights_right_id_fkey"
DETAIL:  Key (right_id)=(write) is not present in table "permission_types".


2025-05-12 08:46:51 | 
==== VALIDATING DATA INTEGRITY ====
2025-05-12 08:46:51 | 
==== VALIDATING NESTED FUNCTIONS ====
2025-05-12 08:46:51 | ✅ All nested functions validated successfully
2025-05-12 08:46:51 | ✅ MIGRATION COMPLETE
2025-05-12 08:46:51 | 
🏁 Migration of data to PostgreSQL schema workflow_runtime complete!
