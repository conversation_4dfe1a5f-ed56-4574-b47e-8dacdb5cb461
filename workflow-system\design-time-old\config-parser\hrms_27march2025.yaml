# Enterprise Workflow System Configuration for HRMS

# Tenant Configuration
tenant:
  id: "T001"
  name: "HR001"
  roles:
    - id: "R001"
      name: "<PERSON><PERSON><PERSON><PERSON><PERSON>"
      inherits_from: null
      access:
        entities:
          - entity_id: "E001"  # Employee
            permissions: ["Read", "Create", "Update", "Delete", "Assign"]
          - entity_id: "E002"  # Leave
            permissions: ["Read", "Create", "Update", "Delete", "Assign"]
          - entity_id: "E003"  # LeaveBalance
            permissions: ["Read", "Create", "Update", "Delete"]
        objectives:
          - objective_id: "GO001"  # Leave Management
            permissions: ["Execute", "Transact"]
          - objective_id: "GO002"  # Leave Reports
            permissions: ["Information"]
    
    - id: "R002"
      name: "Manager"
      inherits_from: null
      access:
        entities:
          - entity_id: "E001"  # Employee
            permissions: ["Read"]
          - entity_id: "E002"  # Leave
            permissions: ["Read", "Update"]  # Update covers both Approve and Reject
          - entity_id: "E003"  # LeaveBalance
            permissions: ["Read"]
        objectives:
          - objective_id: "GO001"  # Leave Management
            permissions: ["Execute"]
          - objective_id: "GO001.LO003"  # Approve Leave
            permissions: ["Execute", "Update"]  # Update includes approval functions
    
    - id: "R003"
      name: "Employee"
      inherits_from: null
      access:
        entities:
          - entity_id: "E001"  # Employee
            permissions: ["Read"]
          - entity_id: "E002"  # Leave
            permissions: ["Read", "Create", "Update"]  # Update includes Cancel operation
          - entity_id: "E003"  # LeaveBalance
            permissions: ["Read"]
        objectives:
          - objective_id: "GO001"  # Leave Management
            permissions: ["Execute"]
          - objective_id: "GO001.LO001"  # Apply for Leave
            permissions: ["Execute"]

# Permission Types Definition
permission_types:
  # Execute: Limited to executing only their own transactions
  - id: "Execute" 
    description: "Can execute only their own assigned transactions"
    capabilities: ["ExecuteOwn"]
  
  # Transact: Can execute and view transactions for their team/group
  - id: "Transact"
    description: "Can view other's transactions and execute both their own and others in the same group"
    capabilities: ["ExecuteOwn", "ExecuteOthers", "ViewGroup"]
  
  # Information: Read-only access to all transactions
  - id: "Information"
    description: "Can view all transaction information but cannot execute any transactions"
    capabilities: ["ViewAll"]
  
  # CRUD Permissions
  - id: "Read"
    description: "Can view entity data"
    capabilities: ["ViewData"]
  
  - id: "Create"
    description: "Can create new entity records"
    capabilities: ["CreateData"]
  
  - id: "Update"
    description: "Can modify existing entity records including approvals and rejections"
    capabilities: ["ModifyData", "ApproveReject"]
  
  - id: "Delete"
    description: "Can remove entity records"
    capabilities: ["RemoveData"]
  
  - id: "Assign"
    description: "Can assign entity records to users"
    capabilities: ["AssignData"]
# Entities
entities:
  # Regular Entity: Employee
  - id: "E001"
    name: "Employee"
    version: "1.0"
    status: "Active"
    type: "Regular"
    attributes_metadata:
      attribute_prefix: "At"
      attribute_map:
        "At001": "EmployeeID"
        "At002": "FirstName"
        "At003": "LastName"
        "At004": "Email"
        "At005": "Department"
        "At006": "Designation"
        "At007": "JoiningDate"
        "At008": "ReportingManager"
      required_attributes: ["At001", "At002", "At003", "At004", "At005", "At006", "At007"]
    attributes:
      - id: "At001"
        name: "EmployeeID"
        display_name: "Employee ID"
        datatype: "String"
        version: "1.0"
        status: "Deployed"
        required: true
      
      - id: "At002"
        name: "FirstName"
        display_name: "First Name"
        datatype: "String"
        version: "1.0"
        status: "Deployed"
        required: true
      
      - id: "At003"
        name: "LastName"
        display_name: "Last Name"
        datatype: "String"
        version: "1.0"
        status: "Deployed"
        required: true
      
      - id: "At004"
        name: "Email"
        display_name: "Email"
        datatype: "String"
        version: "1.0"
        status: "Deployed"
        required: true
        validations:
          - rule: "Email format validation"
            expression: "^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$"
      
      - id: "At005"
        name: "Department"
        display_name: "Department"
        datatype: "String"
        version: "1.0"
        status: "Deployed"
        required: true
      
      - id: "At006"
        name: "Designation"
        display_name: "Designation"
        datatype: "String"
        version: "1.0"
        status: "Deployed"
        required: true
      
      - id: "At007"
        name: "JoiningDate"
        display_name: "Joining Date"
        datatype: "Date"
        version: "1.0"
        status: "Deployed"
        required: true
      
      - id: "At008"
        name: "ReportingManager"
        display_name: "Reporting Manager"
        datatype: "Reference"
        version: "1.0"
        status: "Deployed"
        references: "E001"  # Self-reference to Employee
        required: false
    
    relationships:
      - entity_id: "E003"  # LeaveBalance
        type: "OneToMany"
        through_attribute: "E001.At001"  # Employee.EmployeeID
        to_attribute: "E003.At001"  # LeaveBalance.EmployeeID

  # Regular Entity: Leave
  - id: "E002"
    name: "Leave"
    version: "1.0"
    status: "Active"
    type: "Regular"
    attributes_metadata:
      attribute_prefix: "At"
      attribute_map:
        "At101": "LeaveID"
        "At102": "EmployeeID"
        "At103": "LeaveType"
        "At104": "StartDate"
        "At105": "EndDate"
        "At106": "NumberOfDays"
        "At107": "Status"
        "At108": "Reason"
        "At109": "AppliedDate"
        "At110": "ApprovedBy"
        "At111": "ApprovalDate"
        "At112": "IsLossOfPay"
        "At113": "Comments"
      required_attributes: ["At101", "At102", "At103", "At104", "At105", "At106", "At107", "At108", "At109"]
    attributes:
      - id: "At101"
        name: "LeaveID"
        display_name: "Leave ID"
        datatype: "String"
        version: "1.0"
        status: "Deployed"
        required: true
      
      - id: "At102"
        name: "EmployeeID"
        display_name: "Employee ID"
        datatype: "Reference"
        version: "1.0"
        status: "Deployed"
        references: "E001" # Employee
        required: true
      
      - id: "At103"
        name: "LeaveType"
        display_name: "Leave Type"
        datatype: "Enum"
        version: "1.0"
        status: "Deployed"
        required: true
        values: ["Annual", "Sick", "Personal", "Maternity", "Paternity", "Bereavement", "Unpaid"]
      
      - id: "At104"
        name: "StartDate"
        display_name: "Start Date"
        datatype: "Date"
        version: "1.0"
        status: "Deployed"
        required: true
      
      - id: "At105"
        name: "EndDate"
        display_name: "End Date"
        datatype: "Date"
        version: "1.0"
        status: "Deployed"
        required: true
      
      - id: "At106"
        name: "NumberOfDays"
        display_name: "Number of Days"
        datatype: "Number"
        version: "1.0"
        status: "Deployed"
        required: true
      
      - id: "At107"
        name: "Status"
        display_name: "Status"
        datatype: "Enum"
        version: "1.0"
        status: "Deployed"
        required: true
        values: ["Applied", "Pending", "Approved", "Rejected", "Cancelled"]
      
      - id: "At108"
        name: "Reason"
        display_name: "Reason"
        datatype: "String"
        version: "1.0"
        status: "Deployed"
        required: true
      
      - id: "At109"
        name: "AppliedDate"
        display_name: "Applied Date"
        datatype: "DateTime"
        version: "1.0"
        status: "Deployed"
        required: true
      
      - id: "At110"
        name: "ApprovedBy"
        display_name: "Approved By"
        datatype: "Reference"
        version: "1.0"
        status: "Deployed"
        references: "E001" # Employee (Manager)
        required: false
      
      - id: "At111"
        name: "ApprovalDate"
        display_name: "Approval Date"
        datatype: "DateTime"
        version: "1.0"
        status: "Deployed"
        required: false
      
      - id: "At112"
        name: "IsLossOfPay"
        display_name: "Is Loss Of Pay"
        datatype: "Boolean"
        version: "1.0"
        status: "Deployed"
        required: false
      
      - id: "At113"
        name: "Comments"
        display_name: "Comments"
        datatype: "String"
        version: "1.0"
        status: "Deployed"
        required: false
    
    relationships:
      - entity_id: "E001"  # Employee
        type: "ManyToOne"
        through_attribute: "E002.At102"  # Leave.EmployeeID
        to_attribute: "E001.At001"  # Employee.EmployeeID    
  # Entities
    entities:
  # Regular Entity: Employee
  - id: "E001"
    name: "Employee"
    version: "1.0"
    status: "Active"
    type: "Regular"
    attributes_metadata:
      attribute_prefix: "At"
      attribute_map:
        "At001": "EmployeeID"
        "At002": "FirstName"
        "At003": "LastName"
        "At004": "Email"
        "At005": "Department"
        "At006": "Designation"
        "At007": "JoiningDate"
        "At008": "ReportingManager"
      required_attributes: ["At001", "At002", "At003", "At004", "At005", "At006", "At007"]
    attributes:
      - id: "At001"
        name: "EmployeeID"
        display_name: "Employee ID"
        datatype: "String"
        version: "1.0"
        status: "Deployed"
        required: true
      
      - id: "At002"
        name: "FirstName"
        display_name: "First Name"
        datatype: "String"
        version: "1.0"
        status: "Deployed"
        required: true
      
      - id: "At003"
        name: "LastName"
        display_name: "Last Name"
        datatype: "String"
        version: "1.0"
        status: "Deployed"
        required: true
      
      - id: "At004"
        name: "Email"
        display_name: "Email"
        datatype: "String"
        version: "1.0"
        status: "Deployed"
        required: true
        validations:
          - rule: "Email format validation"
            expression: "^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$"
      
      - id: "At005"
        name: "Department"
        display_name: "Department"
        datatype: "String"
        version: "1.0"
        status: "Deployed"
        required: true
      
      - id: "At006"
        name: "Designation"
        display_name: "Designation"
        datatype: "String"
        version: "1.0"
        status: "Deployed"
        required: true
      
      - id: "At007"
        name: "JoiningDate"
        display_name: "Joining Date"
        datatype: "Date"
        version: "1.0"
        status: "Deployed"
        required: true
      
      - id: "At008"
        name: "ReportingManager"
        display_name: "Reporting Manager"
        datatype: "Reference"
        version: "1.0"
        status: "Deployed"
        references: "E001"  # Self-reference to Employee
        required: false
    
    relationships:
      - entity_id: "E003"  # LeaveBalance
        type: "OneToMany"
        through_attribute: "E001.At001"  # Employee.EmployeeID
        to_attribute: "E003.At001"  # LeaveBalance.EmployeeID

  # Regular Entity: Leave
  - id: "E002"
    name: "Leave"
    version: "1.0"
    status: "Active"
    type: "Regular"
    attributes_metadata:
      attribute_prefix: "At"
      attribute_map:
        "At101": "LeaveID"
        "At102": "EmployeeID"
        "At103": "LeaveType"
        "At104": "StartDate"
        "At105": "EndDate"
        "At106": "NumberOfDays"
        "At107": "Status"
        "At108": "Reason"
        "At109": "AppliedDate"
        "At110": "ApprovedBy"
        "At111": "ApprovalDate"
        "At112": "IsLossOfPay"
        "At113": "Comments"
      required_attributes: ["At101", "At102", "At103", "At104", "At105", "At106", "At107", "At108", "At109"]
    attributes:
      - id: "At101"
        name: "LeaveID"
        display_name: "Leave ID"
        datatype: "String"
        version: "1.0"
        status: "Deployed"
        required: true
      
      - id: "At102"
        name: "EmployeeID"
        display_name: "Employee ID"
        datatype: "Reference"
        version: "1.0"
        status: "Deployed"
        references: "E001" # Employee
        required: true
      
      - id: "At103"
        name: "LeaveType"
        display_name: "Leave Type"
        datatype: "Enum"
        version: "1.0"
        status: "Deployed"
        required: true
        values: ["Annual", "Sick", "Personal", "Maternity", "Paternity", "Bereavement", "Unpaid"]
      
      - id: "At104"
        name: "StartDate"
        display_name: "Start Date"
        datatype: "Date"
        version: "1.0"
        status: "Deployed"
        required: true
      
      - id: "At105"
        name: "EndDate"
        display_name: "End Date"
        datatype: "Date"
        version: "1.0"
        status: "Deployed"
        required: true
      
      - id: "At106"
        name: "NumberOfDays"
        display_name: "Number of Days"
        datatype: "Number"
        version: "1.0"
        status: "Deployed"
        required: true
      
      - id: "At107"
        name: "Status"
        display_name: "Status"
        datatype: "Enum"
        version: "1.0"
        status: "Deployed"
        required: true
        values: ["Applied", "Pending", "Approved", "Rejected", "Cancelled"]
      
      - id: "At108"
        name: "Reason"
        display_name: "Reason"
        datatype: "String"
        version: "1.0"
        status: "Deployed"
        required: true
      
      - id: "At109"
        name: "AppliedDate"
        display_name: "Applied Date"
        datatype: "DateTime"
        version: "1.0"
        status: "Deployed"
        required: true
      
      - id: "At110"
        name: "ApprovedBy"
        display_name: "Approved By"
        datatype: "Reference"
        version: "1.0"
        status: "Deployed"
        references: "E001" # Employee (Manager)
        required: false
      
      - id: "At111"
        name: "ApprovalDate"
        display_name: "Approval Date"
        datatype: "DateTime"
        version: "1.0"
        status: "Deployed"
        required: false
      
      - id: "At112"
        name: "IsLossOfPay"
        display_name: "Is Loss Of Pay"
        datatype: "Boolean"
        version: "1.0"
        status: "Deployed"
        required: false
      
      - id: "At113"
        name: "Comments"
        display_name: "Comments"
        datatype: "String"
        version: "1.0"
        status: "Deployed"
        required: false
    
    relationships:
      - entity_id: "E001"  # Employee
        type: "ManyToOne"
        through_attribute: "E002.At102"  # Leave.EmployeeID
        to_attribute: "E001.At001"  # Employee.EmployeeID
  # Regular Entity: LeaveBalance
  - id: "E003"
    name: "LeaveBalance"
    version: "1.0"
    status: "Active"
    type: "Regular"
    attributes_metadata:
      attribute_prefix: "At"
      attribute_map:
        "At201": "EmployeeID"
        "At202": "LeaveType"
        "At203": "BalanceYear"
        "At204": "TotalEntitlement"
        "At205": "Used"
        "At206": "Remaining"
        "At207": "LastUpdated"
      required_attributes: ["At201", "At202", "At203", "At204", "At205", "At206"]
    attributes:
      - id: "At201"
        name: "EmployeeID"
        display_name: "Employee ID"
        datatype: "Reference"
        version: "1.0"
        status: "Deployed"
        references: "E001" # Employee
        required: true
      
      - id: "At202"
        name: "LeaveType"
        display_name: "Leave Type"
        datatype: "Enum"
        version: "1.0"
        status: "Deployed"
        required: true
        values: ["Annual", "Sick", "Personal", "Maternity", "Paternity", "Bereavement"]
      
      - id: "At203"
        name: "BalanceYear"
        display_name: "Balance Year"
        datatype: "Number"
        version: "1.0"
        status: "Deployed"
        required: true
      
      - id: "At204"
        name: "TotalEntitlement"
        display_name: "Total Entitlement"
        datatype: "Number"
        version: "1.0"
        status: "Deployed"
        required: true
      
      - id: "At205"
        name: "Used"
        display_name: "Used"
        datatype: "Number"
        version: "1.0"
        status: "Deployed"
        required: true
      
      - id: "At206"
        name: "Remaining"
        display_name: "Remaining"
        datatype: "Number"
        version: "1.0"
        status: "Deployed"
        required: true
      
      - id: "At207"
        name: "LastUpdated"
        display_name: "Last Updated"
        datatype: "DateTime"
        version: "1.0"
        status: "Deployed"
        required: false
    
    relationships:
      - entity_id: "E001"  # Employee
        type: "ManyToOne"
        through_attribute: "E003.At201"  # LeaveBalance.EmployeeID
        to_attribute: "E001.At001"  # Employee.EmployeeID

  # System Entity: WorkflowResults
  - id: "E004"
    name: "WorkflowResults"
    version: "1.0"
    status: "Active"
    type: "System"
    description: "Contains workflow execution results and metrics"
    attributes_metadata:
      attribute_prefix: "At"
      attribute_map:
        "At301": "LeavesPendingApproval"
        "At302": "LeavesApprovedCount"
        "At303": "LeavesRejectedCount"
        "At304": "Duration"
        "At305": "StartTime"
        "At306": "EndTime"
        "At307": "Status"
      required_attributes: ["At305", "At306", "At307"]
    attributes:
      - id: "At301"
        name: "LeavesPendingApproval"
        display_name: "Leaves Pending Approval"
        datatype: "Integer"
        version: "1.0"
        status: "Deployed"
        required: false
        description: "Number of leaves pending approval"
      
      - id: "At302"
        name: "LeavesApprovedCount"
        display_name: "Leaves Approved Count"
        datatype: "Integer"
        version: "1.0"
        status: "Deployed"
        required: false
        description: "Number of leaves approved"
      
      - id: "At303"
        name: "LeavesRejectedCount"
        display_name: "Leaves Rejected Count"
        datatype: "Integer"
        version: "1.0"
        status: "Deployed"
        required: false
        description: "Number of leaves rejected"
      
      - id: "At304"
        name: "Duration"
        display_name: "Duration (ms)"
        datatype: "Integer"
        version: "1.0"
        status: "Deployed"
        required: false
        description: "Workflow execution duration in milliseconds"
      
      - id: "At305"
        name: "StartTime"
        display_name: "Start Time"
        datatype: "DateTime"
        version: "1.0"
        status: "Deployed"
        required: true
        description: "Workflow start timestamp"
      
      - id: "At306"
        name: "EndTime"
        display_name: "End Time"
        datatype: "DateTime"
        version: "1.0"
        status: "Deployed"
        required: true
        description: "Workflow end timestamp"
      
      - id: "At307"
        name: "Status"
        display_name: "Status"
        datatype: "String"
        version: "1.0"
        status: "Deployed"
        required: true
        description: "Final workflow execution status"
        values: ["Completed", "Failed", "Partial", "Cancelled"]

  # System Entity: TeamAvailability
  - id: "E005"
    name: "TeamAvailability"
    version: "1.0"
    status: "Active"
    type: "System"
    description: "Contains team availability information for leave approval decisions"
    attributes_metadata:
      attribute_prefix: "At"
      attribute_map:
        "At401": "Department"
        "At402": "Designation" 
        "At403": "Date"
        "At404": "TotalEmployees"
        "At405": "AvailableEmployees"
        "At406": "OnLeaveEmployees"
      required_attributes: ["At401", "At402", "At403", "At404", "At405", "At406"]
    attributes:
      - id: "At401"
        name: "Department"
        display_name: "Department"
        datatype: "String"
        version: "1.0"
        status: "Deployed"
        required: true
      
      - id: "At402"
        name: "Designation"
        display_name: "Designation"
        datatype: "String"
        version: "1.0"
        status: "Deployed"
        required: true
      
      - id: "At403"
        name: "Date"
        display_name: "Date"
        datatype: "Date"
        version: "1.0"
        status: "Deployed"
        required: true
      
      - id: "At404"
        name: "TotalEmployees"
        display_name: "Total Employees"
        datatype: "Integer"
        version: "1.0"
        status: "Deployed"
        required: true
      
      - id: "At405"
        name: "AvailableEmployees"
        display_name: "Available Employees"
        datatype: "Integer"
        version: "1.0"
        status: "Deployed"
        required: true
      
      - id: "At406"
        name: "OnLeaveEmployees"
        display_name: "On Leave Employees"
        datatype: "Integer"
        version: "1.0"
        status: "Deployed"
        required: true
# Global Objectives
global_objectives:
  - id: "GO001"
    name: "Leave Management Process"
    version: "1.0"
    status: "Active"
    
    # Input Stack for Global Objective
    input_stack:
      description: "Defines inputs from other global objectives for leave management process"
      inputs:
        - id: "I001"
          slot_id: "PreviousWorkflowResult"
          contextual_id: "GO001.I001"
          entity_reference: "E004"  # WorkflowResults entity
          attribute_reference: "At307"  # Status attribute
          source:
            type: "System"
            description: "Result from preceding workflow"
          required: false
        
        - id: "I002"
          slot_id: "EmployeeBulkData"
          contextual_id: "GO001.I002"
          source:
            type: "System"
            description: "Bulk employee data from HR master data workflow"
          required: false
      
      system_functions:
        - function_id: "SF001"  
          function_name: "validate_employee_exists"
          function_type: "validation"
          parameters:
            entity: "E001"
            attribute: "At001"
            value_source: "GO001.I001"
          output_to: "EmployeeValidationResult"
        
        - function_id: "SF002"
          function_name: "log_workflow_event"
          function_type: "logging"
          parameters:
            event_type: "WorkflowStart"
            workflow_id: "GO001"
            data:
              employee_id:
                source_type: "input"
                input_id: "GO001.I001"
    
    # Output Stack for Global Objective
    output_stack:
      description: "Defines outputs that other global objectives can consume"
      outputs:
        - id: "O001"
          slot_id: "LeaveProcessedCount"
          contextual_id: "GO001.O001"
          output_entity: "E004"  # WorkflowResults entity
          output_attribute: "At302"  # LeavesApprovedCount attribute
          data_type: "Integer"
          triggers:
            description: "Triggers notifications to HR dashboard workflow"
            items:
              - id: "T001"
                target_objective: "GO002"  # HR Dashboard
                target_input: "GO002.I001"  # LeaveMetrics
                mapping_type: "Direct"
                condition:
                  condition_type: "attribute_comparison"
                  entity: "E004"
                  attribute: "At307"  # Status
                  operator: "equals"
                  value: "Completed"
        
        - id: "O002"
          slot_id: "WorkflowCompletionStatus"
          contextual_id: "GO001.O002"
          output_entity: "E004"  # WorkflowResults entity
          output_attribute: "At307"  # Status attribute
          data_type: "String"
          triggers:
            description: "Provides workflow completion status to dependent workflows"
            items:
              - id: "T001"
                target_objective: "GO003"  # Payroll Processing
                target_input: "GO003.I002"  # LeaveStatus
                mapping_type: "Direct"
      
      system_functions:
        - function_id: "SF003"
          function_name: "aggregate_results"
          function_type: "data_processing"
          parameters:
            target_entity: "E004"  # WorkflowResults entity
            include_timing: true
            timing_attributes: {
              start_time: "At305",
              end_time: "At306",
              duration: "At304"
            }
        
        - function_id: "SF004"
          function_name: "log_workflow_completion"
          function_type: "logging"
          parameters:
            event_type: "WorkflowCompleted"
            workflow_id: "GO001"
            result_entity: "E004"
            include_timing: true
    
    # Data Mapping Stack for Global Objective
    data_mapping_stack:
      description: "Defines data movement between global objectives"
      mappings:
        - id: "M001"
          source: "GO002.O001"  # Employee Data Management Output
          target: "GO001.I002"  # Leave Management Input (EmployeeBulkData)
          mapping_type: "Direct"
        
        - id: "M002"
          source: "GO001.O001"  # Leave Management Output (LeaveProcessedCount)
          target: "GO003.I001"  # Payroll Processing Input
          mapping_type: "Direct"
      
      rules:
        - id: "R001"
          description: "Ensure current workflow status isn't a failure before accepting input"
          rule_condition:
            condition_type: "attribute_comparison"
            entity: "E004"  # WorkflowResults
            attribute: "At307"  # Status attribute
            operator: "not_equals"
            value: "Failed"
          error_message: "Cannot proceed with leave management when a dependent workflow failed"
# Local Objectives
# Local Objectives
local_objectives:
  # LO001: Apply for Leave
  - id: "LO001"
    contextual_id: "GO001.LO001"
    name: "Apply for Leave"
    function_type: "Create"
    workflow_source: "Origin"
    execution_pathway:
      type: "Sequential"
      next_lo: "LO002"  # Check Leave Eligibility

    # Agent Stack
    agent_stack:
      agents:
        - role: "R003"  # Employee
          rights: ["Execute"]
        - role: "R001"  # HRManager
          rights: ["Transact", "Information"]

    # Input Stack
    input_stack:
      description: "Collects leave application details from user"
      inputs:
        - id: "I001"
          slot_id: "E002.At102"  # Leave.EmployeeID
          contextual_id: "GO001.LO001.I001"
          source:
            type: "System"
            description: "Employee ID from logged-in user"
          required: true
          validations:
            - rule: "Employee ID exists"
              rule_type: "entity_exists"
              entity: "E001"
              attribute: "At001"
              error_message: "Employee ID does not exist"

        - id: "I002"
          slot_id: "E002.At103"  # Leave.LeaveType
          contextual_id: "GO001.LO001.I002"
          source:
            type: "User"
            description: "Type of leave requested"
          required: true
          validations:
            - rule: "Leave type is valid"
              rule_type: "enum_validation"
              allowed_values: ["Annual", "Sick", "Personal", "Maternity", "Paternity", "Bereavement", "Unpaid"]
              error_message: "Please select a valid leave type"

        - id: "I003"
          slot_id: "E002.At104" # Leave.StartDate
          contextual_id: "GO001.LO001.I003"
          source:
            type: "User"
            description: "First day of leave"
          required: true
          validations:
            - rule: "Start date is in the future"
              rule_type: "date_validation"
              validation_method: "future_date"
              error_message: "Start date must be in the future"

        - id: "I004"
          slot_id: "E002.At105"  # Leave.EndDate
          contextual_id: "GO001.LO001.I004"
          source:
            type: "User"
            description: "Last day of leave"
          required: true
          validations:
            - rule: "End date is after or equal to start date"
              rule_type: "date_validation"
              validation_method: "after_or_equal_to"
              reference_date_source: "GO001.LO001.I003"
              error_message: "End date must be after or equal to start date"

        - id: "I005"
          slot_id: "E002.At108"  # Leave.Reason
          contextual_id: "GO001.LO001.I005"
          source:
            type: "User"
            description: "Reason for leave"
          required: true
          validations:
            - rule: "Reason is not empty"
              rule_type: "attribute_validation"
              validation_method: "not_empty"
              error_message: "Please provide a reason for your leave"

    # Execution Rules
    execution_rules:
      - id: "ExR001"
        contextual_id: "GO001.LO001.ExR001"
        description: "Calculate number of days and create leave request"
        structured_rule:
          condition: "AllInputsValid('GO001.LO001')"
          action:
            - "CalculateBusinessDays('GO001.LO001.I003', 'GO001.LO001.I004', 'NumberOfDays')"
            - "CreateLeaveRequest()"

      - id: "ExR002"
        contextual_id: "GO001.LO001.ExR002"
        description: "After successful creation, proceed to next LO"
        structured_rule:
          condition: "EntityAttributeNotEmpty('E002', 'At101')"
          action: "TransitionToNextLO('GO001.LO002')"

    # Output Stack
    output_stack:
      description: "Outputs from leave application"
      outputs:
        - id: "O001"
          slot_id: "E002.At101"  # Leave.LeaveID
          contextual_id: "GO001.LO001.O001"
          source: "System"
# LO002: Check Leave Eligibility
#local_objectives:
  - id: "LO002"
    contextual_id: "GO001.LO002"
    name: "Check Leave Eligibility"
    function_type: "Process"
    execution_pathway:
      type: "Sequential"
      next_lo: "LO003"  # Manager Approval

    # Agent Stack
    agent_stack:
      agents:
        - role: "System"
          rights: ["Execute"]

    # Input Stack
    input_stack:
      description: "Inputs for leave eligibility check"
      inputs:
        - id: "I001"
          slot_id: "E002.At101"  # Leave.LeaveID
          contextual_id: "GO001.LO002.I001"
          source:
            type: "System"
            description: "Leave ID from previous step"
          required: true

        - id: "I002"
          slot_id: "E002.At103"  # Leave.LeaveType
          contextual_id: "GO001.LO002.I002"
          source:
            type: "System"
            description: "Type of leave requested"
          required: true

        - id: "I003"
          slot_id: "E002.At106"  # Leave.NumberOfDays
          contextual_id: "GO001.LO002.I003"
          source:
            type: "System"
            description: "Number of leave days requested"
          required: true

    # Execution Rules
    execution_rules:
      - id: "ExR001"
        contextual_id: "GO001.LO002.ExR001"
        description: "Check leave balance and determine if LOP is applicable"
        structured_rule:
          condition: "NotEmpty(${E002.At101})"
          action:
            - "GetLeaveBalance(${E002.At102}, ${E002.At103}, 'CurrentBalance')"
            - "CheckLOPApplicability(${E002.At103}, ${E002.At106}, 'CurrentBalance', 'IsLOPApplicable')"
            - "UpdateLeaveRequest(${E002.At101}, {At112: 'IsLOPApplicable'})"

      - id: "ExR002"
        contextual_id: "GO001.LO002.ExR002"
        description: "After eligibility check, proceed to manager approval"
        structured_rule:
          condition: "ProcessCompleted('CheckLOPApplicability')"
          action: "TransitionToNextLO('GO001.LO003')"

    # System Functions
    system_functions:
      - function_id: "SF005"
        function_name: "get_leave_balance"
        function_type: "data_retrieval"
        parameters:
          employee_id: "${E002.At102}"
          leave_type: "${E002.At103}"
          entity: "E003"  # LeaveBalance entity
          filter:
            At201: "${E002.At102}"
            At202: "${E002.At103}"
            At203: "CurrentYear"
          attribute: "At206"  # Remaining attribute
        output_to: "CurrentBalance"

      - function_id: "SF006"
        function_name: "check_lop_applicability"
        function_type: "calculation"
        parameters:
          leave_type: "${E002.At103}"
          requested_days: "${E002.At106}"
          available_balance: "CurrentBalance"
        output_to: "IsLOPApplicable"

    # Output Stack
    output_stack:
      description: "Outputs from eligibility check"
      outputs:
        - id: "O001"
          slot_id: "E002.At101"  # Leave.LeaveID
          contextual_id: "GO001.LO002.O001"
          source:
            type: "System"
            description: "Leave request ID"
          triggers:
            description: "Triggers manager approval"
            items:
              - id: "T001"
                target_objective: "GO001.LO003"
                target_input: "GO001.LO003.I001"
                mapping_type: "Direct"

        - id: "O002"
          slot_id: "E002.At112"  # Leave.IsLossOfPay
          contextual_id: "GO001.LO002.O002"
          source:
            type: "System"
            description: "Whether this is a loss of pay leave"
          triggers:
            description: "Sends LOP status to manager approval"
            items:
              - id: "T002"
                target_objective: "GO001.LO003"
                target_input: "GO001.LO003.I002"
                mapping_type: "Direct"

    # Data Mapping Stack
    data_mapping_stack:
      description: "Defines how this LO's outputs map to other LOs"
      mappings:
        - id: "LM002"
          source: "GO001.LO002.O001"  # Leave Eligibility Check Output
          target: "GO001.LO003.I001"  # Manager Approval Input
          mapping_type: "Direct"

        - id: "LM003"
          source: "GO001.LO002.O002"  # LOP Status Output
          target: "GO001.LO003.I002"  # Manager Approval Input (LOP Info)
          mapping_type: "Direct"

    # Success Message
    success_message: "Leave eligibility check completed"

    # UI Stack
    ui_stack:
      type: "System"
      status: "NotApplicable"
      description: "No UI interaction needed for this step"
#local_objectives:
  - id: "LO003"
    contextual_id: "GO001.LO003"
    name: "Manager Approval"
    function_type: "Update"
    execution_pathway:
      type: "Alternative"
      conditions:
        - condition:
            condition_type: "attribute_comparison"
            entity: "E002"
            attribute: "At107"  # Leave.Status
            operator: "equals"
            value: "Approved"
          next_lo: "LO004"  # Update Leave Balance
        - condition:
            condition_type: "attribute_comparison"
            entity: "E002"
            attribute: "At107"  # Leave.Status
            operator: "equals"
            value: "Rejected"
          next_lo: "LO005"  # Notify Employee

    # Agent Stack
    agent_stack:
      agents:
        - role: "R002"  # Manager
          rights: ["Execute", "Update"]  # Update includes approval functions

    # Input Stack
    input_stack:
      description: "Inputs for manager approval"
      inputs:
        - id: "I001"
          slot_id: "E002.At101"  # Leave.LeaveID
          contextual_id: "GO001.LO003.I001"
          source:
            type: "System"
            description: "Leave ID from previous step"
          required: true

        - id: "I002"
          slot_id: "E002.At112"  # Leave.IsLossOfPay
          contextual_id: "GO001.LO003.I002"
          source:
            type: "System"
            description: "LOP status from eligibility check"
          required: true

        - id: "I003"
          slot_id: "TeamAvailabilityData"
          contextual_id: "GO001.LO003.I003"
          source:
            type: "System"
            description: "Team availability data during leave period"
            data_source:
              source_type: "function"
              function_name: "get_team_availability"
              parameters:
                leave_request_id: "${E002.At101}"
                department_field: "E001.At005"
                designation_field: "E001.At006"
                date_range:
                  start_date: "${E002.At104}"
                  end_date: "${E002.At105}"
          required: true

        - id: "I004"
          slot_id: "E002.At107"  # Leave.Status
          contextual_id: "GO001.LO003.I004"
          source:
            type: "User"
            description: "Approval decision"
            data_type: "Enum"
            allowed_values: ["Approved", "Rejected"]
          required: true

        - id: "I005"
          slot_id: "E002.At113"  # Leave.Comments
          contextual_id: "GO001.LO003.I005"
          source:
            type: "User"
            description: "Manager comments"
          required: false

    # Execution Rules
    execution_rules:
      - id: "ExR001"
        contextual_id: "GO001.LO003.ExR001"
        description: "Enforce minimum team availability requirement"
        structured_rule:
          condition: "CheckTeamAvailability('TeamAvailabilityData', 3)"
          action: "EnableApprovalOption()"

      - id: "ExR002"
        contextual_id: "GO001.LO003.ExR002"
        description: "Prevent approval when team availability is below threshold"
        structured_rule:
          condition: "NOT(CheckTeamAvailability('TeamAvailabilityData', 3))"
          action:
            - "DisableApprovalOption()"
            - "ShowWarningMessage('Cannot approve leave as minimum team availability requirement will not be met')"

      - id: "ExR003"
        contextual_id: "GO001.LO003.ExR003"
        description: "Process approval decision"
        structured_rule:
          condition: "InputProvided('GO001.LO003.I004')"
          action:
            - "UpdateLeaveRequest(${E002.At101}, {At107: ${E002.At107}, At110: CurrentUserId(), At111: CurrentDateTime(), At113: ${E002.At113}})"

      - id: "ExR004"
        contextual_id: "GO001.LO003.ExR004"
        description: "After approval decision, proceed to next LO based on decision"
        structured_rule:
          condition: "EntityAttributeEquals('E002', ${E002.At101}, 'At107', 'Approved')"
          action: "TransitionToNextLO('GO001.LO004')"

      - id: "ExR005"
        contextual_id: "GO001.LO003.ExR005"
        description: "If rejected, proceed to notification LO"
        structured_rule:
          condition: "EntityAttributeEquals('E002', ${E002.At101}, 'At107', 'Rejected')"
          action: "TransitionToNextLO('GO001.LO005')"

    # System Functions
    system_functions:
      - function_id: "SF007"
        function_name: "get_team_availability"
        function_type: "data_retrieval"
        parameters:
          leave_request_id: "${E002.At101}"
          date_range:
            start_date: "${E002.At104}"
            end_date: "${E002.At105}"
        output_to: "TeamAvailabilityData"

      - function_id: "SF008"
        function_name: "check_team_availability"
        function_type: "validation"
        parameters:
          availability_data: "TeamAvailabilityData"
          minimum_threshold: 3
        output_to: "IsTeamAvailabilitySufficient"

    # Output Stack
    output_stack:
      description: "Outputs from manager approval"
      outputs:
        - id: "O001"
          slot_id: "E002.At107"  # Leave.Status
          contextual_id: "GO001.LO003.O001"
          source:
            type: "System"
            description: "Approval decision"
          triggers:
            description: "Triggers next steps based on approval decision"
            items:
              - id: "T001"
                target_objective: "GO001.LO004"  # Update Leave Balance
                target_input: "GO001.LO004.I001"
                mapping_type: "Direct"
                condition:
                  condition_type: "attribute_comparison"
                  entity: "E002"
                  attribute: "At107"  # Leave.Status
                  operator: "equals"
                  value: "Approved"
              - id: "T002"
                target_objective: "GO001.LO005"  # Notify Employee
                target_input: "GO001.LO005.I001"
                mapping_type: "Direct"

    # Data Mapping Stack
    data_mapping_stack:
      description: "Defines how this LO's outputs map to other LOs"
      mappings:
        - id: "LM004"
          source: "GO001.LO003.O001"  # Manager Approval Output
          target: "GO001.LO004.I001"  # Leave Balance Update Input
          mapping_type: "Conditional"
          condition:
            condition_type: "attribute_comparison"
            entity: "E002"
            attribute: "At107"  # Leave.Status
            operator: "equals"
            value: "Approved"

    # Success Message
    success_message_template:
      message_type: "conditional"
      conditions:
        - condition: "EntityAttributeEquals('E002', ${E002.At101}, 'At107', 'Approved')"
          message: "Leave request has been approved"
        - condition: "EntityAttributeEquals('E002', ${E002.At101}, 'At107', 'Rejected')"
          message: "Leave request has been rejected"
      default_message: "Leave request has been processed"

    # UI Stack
    ui_stack:
      type: "Human"
      status: "Applicable"
      description: "Defines the UI components for manager approval"
      overall_control: "Form"
      form_title: "Leave Approval"
      submit_button_text: "Submit Decision"
      cancel_button_text: "Cancel"
      elements:
        - entity_attribute: "TeamAvailabilityData"
          ui_control: "DataGrid"
          helper_text: "Team availability during leave period"
          display_properties:
            - field: "Date"
              display_name: "Date"
            - field: "AvailableEmployees"
              display_name: "Available Team Members"
            - field: "TotalEmployees"
              display_name: "Total Team Members"
          visibility_condition: "Always"
# Runtime Metrics Stack for Global Objective
    runtime_metrics_stack:
      description: "Captures execution metrics for the entire workflow"
      metrics_entity: "E004"  # WorkflowResults entity
      metrics_record:
        objective_id: "GO001"
        aggregation_attributes:
          child_objectives_count: "count_executed_los"
          total_duration: "sum_child_durations"
          average_lo_duration: "average_child_durations"
          slowest_lo: "find_slowest_lo"
          error_count_total: "sum_error_counts"
      execution_path_tracking:
        enabled: true
        store_complete_path: true
        path_analysis:
          identify_bottlenecks: true
          compare_to_historical: true
          path_efficiency_score: "calculate_path_efficiency"
      reporting:
        generate_execution_summary: true
        store_summary_location: "WorkflowSummaries"
        notification_threshold:
          error_count: 1
          duration_percentile: 90  # Notify if duration is in the 90th percentile

