{"timestamp": "2025-06-24T10:43:52.116244", "endpoint": "parse-validate-mongosave/attribute-validations", "input": {"natural_language": "Validation Rule: leaveId must be unique\nAttribute: leaveId\nValidation Type: uniqueness\nError Message: Leave ID must be unique\nTenant: t999", "tenant_id": "t999", "edit_draft": true, "edit_temp": false, "edit_production": false}, "output": {"success": true, "saved_results": [], "total_rules": 0, "successful_saves": 0, "failed_saves": 0, "validation_result": {"structure_errors": [], "required_field_errors": [], "data_type_errors": [], "custom_errors": []}, "uniqueness_result": {"is_unique": true, "status": "unique", "message": "No existing data to compare against", "conflicts": []}, "operation": "parse_validate_mongosave"}, "status": "success"}
{"timestamp": "2025-06-24T12:41:37.792075", "endpoint": "parse-validate-mongosave/attribute-validations", "input": {"natural_language": "Validation Rule: testId must be unique\nAttribute: testId\nValidation Type: uniqueness\nError Message: Test ID must be unique across all records\nTenant: t999", "tenant_id": "t999", "edit_draft": true, "edit_temp": false, "edit_production": false}, "output": {"success": true, "saved_results": [], "total_rules": 0, "successful_saves": 0, "failed_saves": 0, "validation_result": {"structure_errors": [], "required_field_errors": [], "data_type_errors": [], "custom_errors": []}, "uniqueness_result": {"is_unique": true, "status": "unique", "message": "No existing data to compare against", "conflicts": []}, "operation": "parse_validate_mongosave"}, "status": "success"}
{"timestamp": "2025-06-24T12:49:28.928388", "endpoint": "parse-validate-mongosave/attribute-validations", "input": {"natural_language": "validation_rule_id | attribute_id | validation_type | validation_value | error_message | is_active | description\nVR_TESTID_UNIQUE | TestEntity2.testId | uniqueness | true | Test ID must be unique | true | Uniqueness validation for test ID\nTenant: t999", "tenant_id": "t999", "edit_draft": true, "edit_temp": false, "edit_production": false}, "output": {"success": true, "saved_results": [], "total_rules": 0, "successful_saves": 0, "failed_saves": 0, "validation_result": {"structure_errors": [], "required_field_errors": [], "data_type_errors": [], "custom_errors": []}, "uniqueness_result": {"is_unique": true, "status": "unique", "message": "No existing data to compare against", "conflicts": []}, "operation": "parse_validate_mongosave"}, "status": "success"}
{"timestamp": "2025-06-24T12:55:39.278895", "endpoint": "parse-validate-mongosave/attribute-validations", "input": {"natural_language": "Entity Name | Attribute Name | Left Operand | Operator | Right Operand | Success Value/Range | Warning Value/Range | Failure Value/Range | Multi Condition Operator | Warning Message | Success Message | Error Message\nTestEntity2 | testId | testId | IS_UNIQUE | | true | | false | | Test ID must be unique | Test ID is valid | Test ID already exists\nTenant: t999", "tenant_id": "t999", "edit_draft": true, "edit_temp": false, "edit_production": false}, "output": {"success": true, "saved_results": [{"success": true, "saved_data": {"validation_id": 1, "attribute_id": "A_TestEntity2_testId_1750769739", "entity_name": "TestEntity2", "attribute_name": "testId", "left_operand": "testId", "operator": "IS_UNIQUE", "right_operand": "", "success_value": "true", "warning_value": "", "failure_value": "false", "multi_condition_operator": null, "warning_message": "Test ID must be unique", "success_message": "Test ID is valid", "error_message": "Test ID already exists", "natural_language": "TestEntity2.testId: testId IS_UNIQUE ", "version": 1, "status": "draft", "created_at": "2025-06-24T12:55:39.276618", "updated_at": "2025-06-24T12:55:39.276621", "created_by": "system", "updated_by": "system", "_id": "685aa04ba30d43107e83f578"}, "validation_id": 1}], "total_rules": 1, "successful_saves": 1, "failed_saves": 0, "validation_result": {"structure_errors": [], "required_field_errors": [], "data_type_errors": [], "custom_errors": []}, "uniqueness_result": {"is_unique": true, "status": "unique", "message": "No existing data to compare against", "conflicts": []}, "operation": "parse_validate_mongosave"}, "status": "success"}
