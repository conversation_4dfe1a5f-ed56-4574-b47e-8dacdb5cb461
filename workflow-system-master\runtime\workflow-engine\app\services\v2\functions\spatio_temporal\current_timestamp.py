"""
Current Timestamp Function

This function returns the current timestamp in various formats.
"""

import datetime
from typing import Dict, Any, Optional
from app.services.v2.base_function import BaseSystemFunction
from app.services.v2.models import SystemFunctionInput, SystemFunctionOutput, ExecutionStatus, FunctionCategory, FunctionExecutionContext


class CurrentTimestampFunction(BaseSystemFunction):
    """Function to get current timestamp"""
    
    def get_category(self) -> FunctionCategory:
        return FunctionCategory.UTILITY
    
    def get_required_inputs(self) -> list[str]:
        return []  # No required parameters
    
    def get_optional_inputs(self) -> list[str]:
        return [
            "format",           # Format string (default: ISO format)
            "timezone",         # Timezone (default: UTC)
            "user_id"          # User ID for audit purposes
        ]
    
    def execute_function(self, input_data: SystemFunctionInput, context: FunctionExecutionContext) -> Any:
        """
        Execute current timestamp function
        
        Args:
            input_data: Standardized system function input
            context: Function execution context
        
        Returns:
            Current timestamp in requested format
        """
        self.log_info("🔧 Starting current_timestamp execution", context)
        
        # Get parameters from input values
        format_str = self.get_input_value(input_data, 'format', 'iso')
        timezone_str = self.get_input_value(input_data, 'timezone', 'UTC')
        user_id = self.get_input_value(input_data, 'user_id')
        
        self.log_info(f"📋 Parameters: format={format_str}, timezone={timezone_str}, user_id={user_id}", context)
        
        # Get current timestamp
        now = datetime.datetime.now(datetime.timezone.utc)
        
        # Format timestamp based on requested format
        if format_str.lower() == 'iso':
            timestamp = now.isoformat()
        elif format_str.lower() == 'epoch':
            timestamp = int(now.timestamp())
        elif format_str.lower() == 'date':
            timestamp = now.strftime('%Y-%m-%d')
        elif format_str.lower() == 'datetime':
            timestamp = now.strftime('%Y-%m-%d %H:%M:%S')
        elif format_str.lower() == 'time':
            timestamp = now.strftime('%H:%M:%S')
        elif format_str.lower() == 'timestamp':
            timestamp = now.strftime('%Y%m%d%H%M%S')
        else:
            # Custom format string
            try:
                timestamp = now.strftime(format_str)
            except ValueError as e:
                self.log_error(f"Invalid format string: {format_str}", context)
                timestamp = now.isoformat()  # Fallback to ISO format
        
        self.log_info(f"✅ Generated timestamp: {timestamp}", context)
        
        return timestamp  # Return the timestamp directly for simple use
