#!/usr/bin/env python3

"""
This script fixes the dependencies in the lo_input_items table.
It updates the dependencies field to use the new IDs after fixing duplicate IDs.
"""

import psycopg2
from datetime import datetime
import os
import json
import traceback

# Set log path to current working directory
LOG_PATH = os.path.join(
    os.path.dirname(os.path.abspath(__file__)),
    f"fix_dependencies_log_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
)

# Logging function
def log(msg):
    with open(LOG_PATH, "a") as f:
        f.write(f"{datetime.now().strftime('%Y-%m-%d %H:%M:%S')} | {msg}\n")
    print(msg)

# Database configuration
PG_CONFIG = {
    "dbname": "workflow_system",
    "user": "postgres",
    "password": "workflow_postgres_secure_password",
    "host": "**********",
    "port": "5432"
}

def connect_to_db():
    """Connect to the PostgreSQL database."""
    try:
        conn = psycopg2.connect(**PG_CONFIG)
        log("✅ Connected to PostgreSQL database")
        return conn
    except Exception as e:
        log(f"❌ Error connecting to database: {e}")
        raise

def fix_dependencies():
    """Fix dependencies in the lo_input_items table."""
    conn = connect_to_db()
    cursor = conn.cursor()
    
    try:
        # Set search path to the workflow_runtime schema
        cursor.execute("SET search_path TO workflow_runtime")
        
        # Find all items with dependencies
        log("🔍 Finding items with dependencies...")
        cursor.execute("""
            SELECT id, input_stack_id, dependencies
            FROM lo_input_items
            WHERE dependencies IS NOT NULL AND dependencies::text != 'null'
        """)
        
        items_with_deps = cursor.fetchall()
        
        if not items_with_deps:
            log("✅ No items with dependencies found")
            return
        
        log(f"⚠️ Found {len(items_with_deps)} items with dependencies")
        
        # Get a mapping of old IDs to new IDs
        cursor.execute("""
            SELECT id
            FROM lo_input_items
            WHERE id LIKE '%\\_%'
        """)
        
        new_ids = cursor.fetchall()
        id_mapping = {}
        
        for new_id in new_ids:
            base_id = new_id[0].split('_')[0]
            id_mapping[base_id] = new_id[0]
        
        log(f"📊 ID mapping: {id_mapping}")
        
        # Process each item with dependencies
        for item_id, input_stack_id, dependencies in items_with_deps:
            log(f"🔧 Processing dependencies for {item_id}")
            
            # Parse the dependencies JSON
            if isinstance(dependencies, str):
                deps = json.loads(dependencies)
            else:
                deps = dependencies
            
            # Check if any dependencies need to be updated
            updated_deps = []
            needs_update = False
            
            for dep in deps:
                if dep in id_mapping:
                    updated_deps.append(id_mapping[dep])
                    needs_update = True
                    log(f"🔄 Updating dependency {dep} to {id_mapping[dep]}")
                else:
                    updated_deps.append(dep)
            
            if needs_update:
                # Update the dependencies
                cursor.execute("""
                    UPDATE lo_input_items
                    SET dependencies = %s
                    WHERE id = %s AND input_stack_id = %s
                """, (json.dumps(updated_deps), item_id, input_stack_id))
                
                conn.commit()
                log(f"✅ Updated dependencies for {item_id}: {deps} -> {updated_deps}")
        
        # Now update the input_dependencies table
        log("🔍 Updating input_dependencies table...")
        cursor.execute("""
            SELECT id, input_item_id, input_stack_id, depends_on_id, depends_on_stack_id
            FROM input_dependencies
        """)
        
        dependencies = cursor.fetchall()
        
        for dep_id, input_item_id, input_stack_id, depends_on_id, depends_on_stack_id in dependencies:
            if depends_on_id in id_mapping:
                cursor.execute("""
                    UPDATE input_dependencies
                    SET depends_on_id = %s
                    WHERE id = %s
                """, (id_mapping[depends_on_id], dep_id))
                
                conn.commit()
                log(f"✅ Updated input_dependencies: {depends_on_id} -> {id_mapping[depends_on_id]}")
        
        log("✅ All dependencies have been fixed")
        
    except Exception as e:
        if conn:
            conn.rollback()
        log(f"❌ Error fixing dependencies: {e}")
        log(traceback.format_exc())
    finally:
        if cursor:
            cursor.close()
        if conn:
            conn.close()

if __name__ == "__main__":
    fix_dependencies()
