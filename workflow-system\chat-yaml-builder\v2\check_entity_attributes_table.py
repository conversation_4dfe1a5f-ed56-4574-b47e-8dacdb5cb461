#!/usr/bin/env python3
"""
Script to check the entity_attributes table structure.
"""

import os
import logging
import psycopg2

# Set up logging
logging.basicConfig(level=logging.INFO, 
                   format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
                   handlers=[
                       logging.StreamHandler()
                   ])
logger = logging.getLogger('check_entity_attributes_table')

def get_db_connection(schema_name: str = None):
    """
    Get a database connection.
    
    Args:
        schema_name: Optional schema name to set as the search path
        
    Returns:
        Database connection object
    """
    # Get database connection parameters from environment variables or use defaults
    db_host = os.environ.get('DB_HOST', '**********')
    db_port = os.environ.get('DB_PORT', '5432')
    db_name = os.environ.get('DB_NAME', 'workflow_system')
    db_user = os.environ.get('DB_USER', 'postgres')
    db_password = os.environ.get('DB_PASSWORD', 'workflow_postgres_secure_password')
    
    # Connect to the database
    conn = psycopg2.connect(
        host=db_host,
        port=db_port,
        dbname=db_name,
        user=db_user,
        password=db_password
    )
    
    # Set schema search path if provided
    if schema_name:
        with conn.cursor() as cursor:
            cursor.execute(f"SET search_path TO {schema_name}")
            conn.commit()
    
    return conn

def check_entity_attributes_table():
    """
    Check the entity_attributes table structure.
    """
    schema_name = 'workflow_temp'
    
    try:
        conn = get_db_connection(schema_name)
        
        with conn.cursor() as cursor:
            # Get table columns
            cursor.execute("""
                SELECT column_name, data_type, is_nullable
                FROM information_schema.columns
                WHERE table_schema = %s AND table_name = %s
                ORDER BY ordinal_position
            """, (schema_name, 'entity_attributes'))
            
            logger.info("Columns in entity_attributes table:")
            for col in cursor.fetchall():
                logger.info(f"  - {col}")
            
            # Get a sample row
            cursor.execute(f"""
                SELECT *
                FROM {schema_name}.entity_attributes
                LIMIT 1
            """)
            
            if cursor.description:
                column_names = [desc[0] for desc in cursor.description]
                logger.info(f"Column names: {column_names}")
                
                row = cursor.fetchone()
                if row:
                    logger.info("Sample row:")
                    for i, val in enumerate(row):
                        logger.info(f"  - {column_names[i]}: {val}")
    except Exception as e:
        logger.error(f"Error checking entity_attributes table: {str(e)}")
    finally:
        if conn:
            conn.close()

if __name__ == "__main__":
    check_entity_attributes_table()
