import psycopg2
import yaml
import re
import logging
import os
from collections import defaultdict
from datetime import datetime

# Set up logging
log_dir = os.path.dirname(os.path.abspath(__file__))
log_file = os.path.join(log_dir, f"id_generator_debug_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log")
logging.basicConfig(
    filename=log_file,
    level=logging.DEBUG,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

def debug_log(message):
    """Log a debug message to both file and console."""
    logging.debug(message)
    print(f"DEBUG: {message}")

def get_connection():
    debug_log("Connecting to PostgreSQL database...")
    conn = psycopg2.connect(
        dbname="workflow_system",
        user="postgres",
        password="workflow_postgres_secure_password",
        host="**********",  # Updated to match insert_generator.py
        port="5432"
    )
    debug_log("Connected to PostgreSQL database")
    return conn

ID_CONFIG = {
    "go": ("global_objectives", "go_id"),
    "lo": ("local_objectives", "lo_id"),
    "e": ("entities", "entity_id"),
    "at": ("entity_attributes", "attribute_id"),
    "is": ("input_stack", "id"),
    "os": ("output_stack", "id"),
    "in": ("input_items", "id"),
    "out": ("output_items", "id"),
    "dm": ("data_mappings", "id"),
    "dms": ("data_mapping_stack", "id"),
    "nf": ("lo_nested_functions", "id"),
    "vr": ("lo_input_validations", "id"),
    "tr": ("output_triggers", "id"),
    "uis": ("ui_stack", "id"),
    "uie": ("ui_elements", "id")
}

def fetch_current_max_ids(conn):
    debug_log("Fetching current max IDs from database...")
    max_ids = {}
    cursor = conn.cursor()
    for prefix, (table, column) in ID_CONFIG.items():
        try:
            query = f"""
                SELECT MAX(CAST({column} AS TEXT)) FROM workflow_runtime.{table}
                WHERE CAST({column} AS TEXT) ~ %s
            """
            params = [f"^{prefix}[0-9]+$"]
            debug_log(f"Executing query: {query} with params: {params}")
            
            cursor.execute(query, params)
            result = cursor.fetchone()[0]
            debug_log(f"Result for {prefix}: {result}")
            
            max_ids[prefix] = int(re.findall(r'\d+', result)[0]) if result else 0
            debug_log(f"Max ID for {prefix}: {max_ids[prefix]}")
        except Exception as e:
            debug_log(f"⚠️ Skipping {table}.{column}: {e}")
            max_ids[prefix] = 0
            conn.rollback()  # Prevents "current transaction is aborted" for next queries
    cursor.close()
    debug_log(f"Fetched max IDs: {max_ids}")
    return max_ids

def fetch_existing_entities_and_attributes(conn):
    debug_log("Fetching existing entities and attributes from database...")
    entity_map = defaultdict(dict)
    cursor = conn.cursor()
    
    # Fetch entities
    query = "SELECT entity_id, name FROM workflow_runtime.entities"
    debug_log(f"Executing query: {query}")
    cursor.execute(query)
    entities = cursor.fetchall()
    debug_log(f"Fetched entities: {entities}")
    
    for eid, name in entities:
        debug_log(f"Processing entity: {eid}, {name}")
        entity_map[name.lower()] = {"entity_id": eid, "attributes": {}}

    # Fetch attributes
    query = "SELECT entity_id, attribute_id, name FROM workflow_runtime.entity_attributes"
    debug_log(f"Executing query: {query}")
    cursor.execute(query)
    attributes = cursor.fetchall()
    debug_log(f"Fetched attributes: {attributes}")
    
    # Create a direct entity_id to attributes mapping for easier lookup
    entity_attributes = defaultdict(dict)
    for eid, aid, name in attributes:
        debug_log(f"Processing attribute: {eid}, {aid}, {name}")
        entity_attributes[eid][name.lower()] = aid
    
    # Populate the entity_map with attributes
    for eid, aid, name in attributes:
        for entity_name, entity_data in entity_map.items():
            if entity_data["entity_id"] == eid:
                debug_log(f"Adding attribute {aid} ({name}) to entity {entity_name}")
                entity_data["attributes"][name.lower()] = aid
    
    cursor.close()
    debug_log(f"Entity map: {dict(entity_map)}")
    debug_log(f"Direct entity attributes map: {dict(entity_attributes)}")
    return entity_map, entity_attributes

def get_next_id(prefix, counters):
    counters[prefix] += 1
    new_id = f"{prefix}{str(counters[prefix]).zfill(3)}"
    debug_log(f"Generated new ID: {new_id}")
    return new_id

def enrich_yaml_ids(obj, counters, seen_ids, entity_map, entity_attributes, path=""):
    current_path = path
    
    if isinstance(obj, dict):
        if 'id' in obj:
            current_path = f"{path}.{obj['id']}" if path else obj['id']
            debug_log(f"Processing object at path: {current_path}")
        
        if 'id' in obj and 'name' in obj and 'attributes' in obj and 'type' in obj:
            # Handle Entity
            ename = obj['name'].lower()
            debug_log(f"Processing entity: {ename} with ID: {obj['id']}")
            
            if ename in entity_map:
                debug_log(f"Entity {ename} found in entity_map")
                seen_ids[obj['id']] = entity_map[ename]['entity_id']
                debug_log(f"Mapping entity ID {obj['id']} to {entity_map[ename]['entity_id']}")
                obj['id'] = seen_ids[obj['id']]
                
                for attr in obj['attributes']:
                    aname = attr['name'].lower()
                    debug_log(f"Processing attribute: {aname} with ID: {attr['id']}")
                    
                    if aname in entity_map[ename]['attributes']:
                        debug_log(f"Attribute {aname} found in entity_map for entity {ename}")
                        seen_ids[attr['id']] = entity_map[ename]['attributes'][aname]
                        debug_log(f"Mapping attribute ID {attr['id']} to {entity_map[ename]['attributes'][aname]}")
                        attr['id'] = seen_ids[attr['id']]
                    else:
                        debug_log(f"Attribute {aname} not found in entity_map for entity {ename}")
                        new_attr_id = get_next_id("at", counters)
                        seen_ids[attr['id']] = new_attr_id
                        debug_log(f"Mapping attribute ID {attr['id']} to new ID {new_attr_id}")
                        attr['id'] = new_attr_id
            else:
                debug_log(f"Entity {ename} not found in entity_map")
                new_eid = get_next_id("e", counters)
                seen_ids[obj['id']] = new_eid
                debug_log(f"Mapping entity ID {obj['id']} to new ID {new_eid}")
                obj['id'] = new_eid
                
                for attr in obj['attributes']:
                    debug_log(f"Processing attribute: {attr['name']} with ID: {attr['id']}")
                    new_attr_id = get_next_id("at", counters)
                    seen_ids[attr['id']] = new_attr_id
                    debug_log(f"Mapping attribute ID {attr['id']} to new ID {new_attr_id}")
                    attr['id'] = new_attr_id

        elif 'id' in obj and 'name' in obj:
            # Handle Global and Local Objectives
            oid = obj['id'].lower()
            debug_log(f"Processing objective: {oid}")
            
            if oid.startswith("go") or oid.startswith("lo"):
                prefix = oid[:2]
                new_oid = get_next_id(prefix, counters)
                seen_ids[oid] = new_oid
                debug_log(f"Mapping objective ID {oid} to new ID {new_oid}")
                obj['id'] = new_oid

                # Also update contextual_id if it's a Local Objective
                if prefix == "lo":
                    # Try finding GO ID from the current scope
                    parent_go = obj.get("go_id") or find_parent_go_id(obj)
                    if parent_go:
                        go_enriched = seen_ids.get(parent_go.lower(), parent_go.lower())
                        debug_log(f"Updating contextual_id for {oid} with parent GO {parent_go} -> {go_enriched}")
                        obj['contextual_id'] = f"{go_enriched}.{new_oid}"
                        obj['go_id'] = go_enriched

        # Special handling for nested_function
        if 'nested_function' in obj:
            debug_log(f"Found nested_function at path: {current_path}")
            nested_func = obj['nested_function']
            debug_log(f"Nested function details: {nested_func}")
            
            if 'parameters' in nested_func:
                debug_log(f"Nested function parameters before enrichment: {nested_func['parameters']}")

        # Continue recursive enrichment for all keys in object
        for k, v in obj.items():
            enrich_yaml_ids(v, counters, seen_ids, entity_map, entity_attributes, current_path)

    elif isinstance(obj, list):
        for i, item in enumerate(obj):
            enrich_yaml_ids(item, counters, seen_ids, entity_map, entity_attributes, f"{current_path}[{i}]")

def replace_references(obj, seen_ids, entity_map, entity_attributes, path=""):
    current_path = path
    
    if isinstance(obj, dict):
        if 'id' in obj:
            current_path = f"{path}.{obj['id']}" if path else obj['id']
            debug_log(f"Replacing references at path: {current_path}")
        
        for k, v in obj.items():
            if isinstance(v, str):
                original_v = v
                for old_id, new_id in seen_ids.items():
                    v = re.sub(rf"\b{old_id}\b", new_id, v)
                    v = re.sub(rf"\${{\s*{old_id}\s*}}", f"${{{new_id}}}", v)
                if original_v != v:
                    debug_log(f"Replaced string reference at {current_path}.{k}: {original_v} -> {v}")
                obj[k] = v
            
            # Special handling for nested_function parameters
            elif k == "parameters" and isinstance(v, dict):
                debug_log(f"Processing nested function parameters at {current_path}.{k}: {v}")
                
                # Process entity references in parameters first
                entity_id = None
                if "entity" in v and isinstance(v["entity"], str):
                    entity_ref = v["entity"]
                    original_entity = entity_ref
                    debug_log(f"Processing entity reference: {entity_ref}")
                    
                    if entity_ref in seen_ids:
                        entity_id = seen_ids[entity_ref]
                        v["entity"] = entity_id
                        debug_log(f"Mapped entity reference: {original_entity} -> {entity_id}")
                    else:
                        debug_log(f"Entity reference {entity_ref} not found in seen_ids")
                
                # Process attribute references in parameters with entity context
                if "attribute" in v and isinstance(v["attribute"], str):
                    attr_ref = v["attribute"]
                    original_attr = attr_ref
                    debug_log(f"Processing attribute reference: {attr_ref}")
                    
                    # Check if we have an entity context
                    if entity_id:
                        debug_log(f"Have entity context: {entity_id}")
                        
                        # Find the entity in entity_map
                        entity_found = False
                        for entity_name, entity_data in entity_map.items():
                            if entity_data["entity_id"] == entity_id:
                                entity_found = True
                                debug_log(f"Found entity {entity_name} with ID {entity_id}")
                                
                                # Check if the attribute exists for this entity
                                if attr_ref in seen_ids:
                                    attr_id = seen_ids[attr_ref]
                                    debug_log(f"Attribute {attr_ref} maps to {attr_id} in seen_ids")
                                    
                                    # Check if this attribute belongs to the entity
                                    attr_belongs_to_entity = False
                                    for attr_name, mapped_attr_id in entity_data["attributes"].items():
                                        if mapped_attr_id == attr_id:
                                            attr_belongs_to_entity = True
                                            debug_log(f"Attribute {attr_id} belongs to entity {entity_id} (name: {attr_name})")
                                            break
                                    
                                    if attr_belongs_to_entity:
                                        debug_log(f"Applying entity-specific mapping: {attr_ref} -> {attr_id}")
                                        v["attribute"] = attr_id
                                    else:
                                        debug_log(f"Attribute {attr_id} does not belong to entity {entity_id}")
                                        
                                        # Try to find a suitable attribute for this entity
                                        if entity_id in entity_attributes:
                                            # Look for an attribute with 'id' in its name
                                            for attr_name, attr_id in entity_attributes[entity_id].items():
                                                if 'id' in attr_name.lower():
                                                    debug_log(f"Found ID-like attribute {attr_id} ({attr_name}) for entity {entity_id}")
                                                    v["attribute"] = attr_id
                                                    break
                                            else:
                                                # If no ID-like attribute found, use the first attribute
                                                if entity_attributes[entity_id]:
                                                    first_attr = list(entity_attributes[entity_id].values())[0]
                                                    debug_log(f"Using first available attribute {first_attr} for entity {entity_id}")
                                                    v["attribute"] = first_attr
                                break
                        
                        if not entity_found:
                            debug_log(f"Entity {entity_id} not found in entity_map")
                    
                    # If no entity-specific mapping was applied, fall back to direct mapping
                    if v["attribute"] == attr_ref and attr_ref in seen_ids:
                        debug_log(f"Applying direct mapping: {attr_ref} -> {seen_ids[attr_ref]}")
                        v["attribute"] = seen_ids[attr_ref]
                    
                    debug_log(f"Final attribute value: {v['attribute']} (original: {original_attr})")
                
                # Process output_to with entity context
                if "output_to" in obj and isinstance(obj["output_to"], str) and entity_id:
                    output_to = obj["output_to"]
                    original_output = output_to
                    debug_log(f"Processing output_to: {output_to}")
                    
                    # Check if output_to is in seen_ids
                    if output_to in seen_ids:
                        mapped_output = seen_ids[output_to]
                        debug_log(f"Output {output_to} maps to {mapped_output} in seen_ids")
                        
                        # Check if this output belongs to the entity
                        for entity_name, entity_data in entity_map.items():
                            if entity_data["entity_id"] == entity_id:
                                for attr_name, attr_id in entity_data["attributes"].items():
                                    if attr_id == mapped_output:
                                        debug_log(f"Output {mapped_output} belongs to entity {entity_id} (name: {attr_name})")
                                        obj["output_to"] = mapped_output
                                        break
                                break
                    
                    # If no entity-specific mapping was applied, fall back to direct mapping
                    if obj["output_to"] == output_to and output_to in seen_ids:
                        debug_log(f"Applying direct mapping for output_to: {output_to} -> {seen_ids[output_to]}")
                        obj["output_to"] = seen_ids[output_to]
                    
                    debug_log(f"Final output_to value: {obj['output_to']} (original: {original_output})")
                
                # Process other parameters recursively
                replace_references(v, seen_ids, entity_map, entity_attributes, f"{current_path}.{k}")
            else:
                replace_references(v, seen_ids, entity_map, entity_attributes, f"{current_path}.{k}")
    
    elif isinstance(obj, list):
        for i, item in enumerate(obj):
            replace_references(item, seen_ids, entity_map, entity_attributes, f"{current_path}[{i}]")

def assign_unique_ids_to_yaml(yaml_text, conn):
    debug_log("\n🔍 Parsing and enriching YAML IDs...")
    data = yaml.safe_load(yaml_text)
    debug_log(f"Loaded YAML data")
    
    max_ids = fetch_current_max_ids(conn)
    counters = defaultdict(int, max_ids)
    seen_ids = {}
    entity_map, entity_attributes = fetch_existing_entities_and_attributes(conn)

    debug_log("\n🔢 Max ID counters from Postgres:")
    for prefix, count in counters.items():
        debug_log(f"  {prefix.upper():4} → {count}")

    debug_log("\n🔍 Starting YAML ID enrichment...")
    enrich_yaml_ids(data, counters, seen_ids, entity_map, entity_attributes)
    
    debug_log("\n🔍 Starting reference replacement...")
    replace_references(data, seen_ids, entity_map, entity_attributes)

    debug_log("\n🔁 ID Mappings:")
    for old_id, new_id in seen_ids.items():
        debug_log(f"  🔁 {old_id} → {new_id}")

    debug_log(f"\n✅ ID enrichment complete. Total replacements: {len(seen_ids)}\n")
    
    # Check for nested functions with generate_id
    debug_log("\n🔍 Checking nested functions with generate_id:")
    check_nested_functions(data)
    
    return data

def check_nested_functions(data):
    """Check all nested functions in the data to verify generate_id parameters."""
    def _check_obj(obj, path=""):
        if isinstance(obj, dict):
            current_path = path
            if 'id' in obj:
                current_path = f"{path}.{obj['id']}" if path else obj['id']
            
            if 'nested_function' in obj:
                nested_func = obj['nested_function']
                if nested_func.get('function_name') == 'generate_id':
                    debug_log(f"Found generate_id at {current_path}:")
                    debug_log(f"  Parameters: {nested_func.get('parameters', {})}")
                    debug_log(f"  Output to: {nested_func.get('output_to', '')}")
            
            for k, v in obj.items():
                _check_obj(v, current_path)
        elif isinstance(obj, list):
            for i, item in enumerate(obj):
                _check_obj(item, f"{path}[{i}]")
    
    _check_obj(data)

def find_parent_go_id(lo_obj):
    # Best-effort: try to extract go_id from contextual_id
    ctx = lo_obj.get("contextual_id", "")
    if "." in ctx:
        return ctx.split(".")[0]
    return None

def main():
    debug_log("Starting ID Generator Debug")
    
    try:
        with open("runtime/workflow-engine/yamls/leavemanagement_new_rbac.yaml", "r") as f:
            yaml_input = f.read()
            debug_log("Loaded YAML file")

        conn = get_connection()
        enriched = assign_unique_ids_to_yaml(yaml_input, conn)
        debug_log("YAML enrichment completed")

        with open("runtime/workflow-engine/yamls/enriched_debug.yaml", "w") as f:
            yaml.dump(enriched, f, sort_keys=False)
            debug_log("Saved enriched YAML to enriched_debug.yaml")

        debug_log("ID Generator Debug completed successfully")
        print(f"Debug log saved to: {log_file}")
        print("Enriched YAML saved to: runtime/workflow-engine/yamls/enriched_debug.yaml")
    
    except Exception as e:
        debug_log(f"Error in ID Generator Debug: {e}")
        import traceback
        debug_log(traceback.format_exc())
        print(f"Error: {e}")
        print(f"Debug log saved to: {log_file}")

if __name__ == "__main__":
    main()

# Export the main function for external use
enrich_ids = assign_unique_ids_to_yaml
