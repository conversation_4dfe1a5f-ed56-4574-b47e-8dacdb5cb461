#!/usr/bin/env python3
"""
Test script to verify that entity relationships are correctly parsed and deployed to the database.
"""

import os
import logging
import json
from parsers.entity_parser import parse_entities
from deployers.entity_deployer_v2 import deploy_entities, execute_query

# Set up logging
logging.basicConfig(level=logging.INFO, 
                   format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
                   handlers=[
                       logging.FileHandler('/home/<USER>/workflow-system/chat-yaml-builder/v2/entity_relationships.log')
                   ])
logger = logging.getLogger('test_entity_relationships')

def test_entity_relationships():
    """
    Test that entity relationships are correctly parsed and deployed to the database.
    """
    # Create a test entity definition with relationships
    entity_def = """
Employee has employeeId^PK, firstName, lastName, email, phoneNumber, departmentId^FK, managerId^FK, hireDate, status(Active, Inactive, OnLeave), salary, performanceRating, probationDays, minSalary.

* Employee has many-to-one relationship with Department using Employee.departmentId to Department.departmentId^PK
* Employee has many-to-one relationship with Employee using Employee.managerId to Employee.employeeId^PK

Relationship: Employee to Department
Relationship Properties:
On Delete: Restrict
On Update: Cascade
Foreign Key Type: Non-Nullable
Description: Each employee must belong to a department

Relationship: Employee to Employee
Relationship Properties:
On Delete: Restrict
On Update: Cascade
Foreign Key Type: Nullable
Description: An employee may have a manager who is also an employee

Department has departmentId^PK, name, location, managerId^FK, budget, status(Active, Inactive).
"""
    
    # Parse the entity definition
    entities_data, warnings = parse_entities(entity_def)
    
    # Print any warnings
    if warnings:
        logger.warning("Warnings during parsing:")
        for warning in warnings:
            logger.warning(f"  - {warning}")
    
    # Check if Employee entity was parsed
    if 'entities' in entities_data and 'Employee' in entities_data['entities']:
        employee_entity = entities_data['entities']['Employee']
        
        # Check if relationships were parsed
        if 'relationships' in employee_entity:
            logger.info("\nEmployee entity relationships:")
            for rel_name, rel_def in employee_entity['relationships'].items():
                logger.info(f"  - {rel_name}:")
                for key, value in rel_def.items():
                    if key == 'properties':
                        logger.info(f"    - {key}:")
                        for prop_key, prop_value in value.items():
                            logger.info(f"      - {prop_key}: {prop_value}")
                    else:
                        logger.info(f"    - {key}: {value}")
        else:
            logger.warning("No relationships found in Employee entity")
    else:
        logger.error("Employee entity not found in parsed data")
        return
    
    # Deploy the entities to the database
    schema_name = 'workflow_temp'
    success, messages = deploy_entities(entities_data, schema_name)
    
    if not success:
        logger.error("Failed to deploy entities:")
        for message in messages:
            logger.error(f"  - {message}")
        return
    
    logger.info("Successfully deployed entities")
    
    # Verify the entity relationships were deployed
    success, messages, result = execute_query(
        f"""
        SELECT er.id, er.source_entity_id, er.target_entity_id, er.relationship_type, 
               er.source_attribute_id, er.target_attribute_id, er.on_delete, er.on_update, er.foreign_key_type
        FROM {schema_name}.entity_relationships er
        JOIN {schema_name}.entities e1 ON er.source_entity_id = e1.entity_id
        JOIN {schema_name}.entities e2 ON er.target_entity_id = e2.entity_id
        WHERE e1.name = 'Employee'
        """,
        schema_name=schema_name
    )
    
    if success and result:
        logger.info("\nEmployee entity relationships in the database:")
        for row in result:
            logger.info(f"  - {row}")
    else:
        logger.warning("No relationships found for Employee entity in the database")
    
    # Verify the foreign key constraints were created
    success, messages, result = execute_query(
        f"""
        SELECT tc.constraint_name, tc.table_name, kcu.column_name, 
               ccu.table_name AS foreign_table_name, ccu.column_name AS foreign_column_name,
               rc.delete_rule, rc.update_rule
        FROM information_schema.table_constraints AS tc 
        JOIN information_schema.key_column_usage AS kcu
          ON tc.constraint_name = kcu.constraint_name
          AND tc.table_schema = kcu.table_schema
        JOIN information_schema.constraint_column_usage AS ccu
          ON ccu.constraint_name = tc.constraint_name
          AND ccu.table_schema = tc.table_schema
        JOIN information_schema.referential_constraints AS rc
          ON rc.constraint_name = tc.constraint_name
          AND rc.constraint_schema = tc.table_schema
        WHERE tc.constraint_type = 'FOREIGN KEY'
          AND tc.table_schema = %s
          AND tc.table_name LIKE 'e%_employee'
        """,
        (schema_name,),
        schema_name=schema_name
    )
    
    if success and result:
        logger.info("\nForeign key constraints in the database:")
        for row in result:
            logger.info(f"  - {row}")
    else:
        logger.warning("No foreign key constraints found for Employee entity in the database")

if __name__ == "__main__":
    test_entity_relationships()
