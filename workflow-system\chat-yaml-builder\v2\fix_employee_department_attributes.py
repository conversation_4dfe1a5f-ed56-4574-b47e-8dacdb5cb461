#!/usr/bin/env python3
"""
Fix the Employee and Department attributes and relationship.
"""

import os
import psycopg2
import logging

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger('attribute_fixer')

def get_db_connection(schema_name=None):
    """Get database connection with optional schema"""
    db_host = os.environ.get('DB_HOST', '**********')
    db_port = os.environ.get('DB_PORT', '5432')
    db_name = os.environ.get('DB_NAME', 'workflow_system')
    db_user = os.environ.get('DB_USER', 'postgres')
    db_password = os.environ.get('DB_PASSWORD', 'workflow_postgres_secure_password')
    
    conn = psycopg2.connect(
        host=db_host,
        port=db_port,
        dbname=db_name,
        user=db_user,
        password=db_password
    )
    
    if schema_name:
        with conn.cursor() as cursor:
            cursor.execute(f"SET search_path TO {schema_name}")
            conn.commit()
    
    return conn

def fix_attributes_and_relationship():
    """Fix the Employee and Department attributes and relationship"""
    schema_name = 'workflow_temp'
    conn = get_db_connection(schema_name)
    
    try:
        # Get entity IDs
        with conn.cursor() as cursor:
            cursor.execute("SELECT entity_id, name FROM entities WHERE name IN ('Employee', 'Department')")
            entities = {row[1]: row[0] for row in cursor.fetchall()}
            
            if 'Employee' not in entities or 'Department' not in entities:
                logger.error("Employee or Department entity not found")
                return
            
            employee_entity_id = entities['Employee']
            department_entity_id = entities['Department']
            
            logger.info(f"Found Employee entity ID: {employee_entity_id}")
            logger.info(f"Found Department entity ID: {department_entity_id}")
            
            # Check if departmentId attribute exists for Employee
            cursor.execute("""
                SELECT attribute_id, name FROM entity_attributes 
                WHERE entity_id = %s AND name = 'departmentId'
            """, (employee_entity_id,))
            employee_dept_attr = cursor.fetchone()
            
            # Create departmentId attribute for Employee if it doesn't exist
            if not employee_dept_attr:
                logger.info("Creating departmentId attribute for Employee")
                
                # Get next attribute ID
                cursor.execute("""
                    SELECT attribute_id FROM entity_attributes
                    WHERE entity_id = %s
                    ORDER BY attribute_id DESC
                    LIMIT 1
                """, (employee_entity_id,))
                
                result = cursor.fetchone()
                if result and result[0].startswith(f'{employee_entity_id}.At'):
                    num = int(result[0].split('At')[1]) + 1
                    attr_id = f"{employee_entity_id}.At{num}"
                else:
                    attr_id = f"{employee_entity_id}.At1"
                
                # Insert attribute
                cursor.execute("""
                    INSERT INTO entity_attributes 
                    (attribute_id, entity_id, name, type, foreign_key, created_at, updated_at)
                    VALUES (%s, %s, %s, %s, %s, NOW(), NOW())
                """, (attr_id, employee_entity_id, 'departmentId', 'string', True))
                
                conn.commit()
                employee_dept_attr = (attr_id, 'departmentId')
                logger.info(f"Created departmentId attribute for Employee with ID: {attr_id}")
            else:
                logger.info(f"Found departmentId attribute for Employee: {employee_dept_attr[0]}")
            
            # Check if departmentId attribute exists for Department
            cursor.execute("""
                SELECT attribute_id, name FROM entity_attributes 
                WHERE entity_id = %s AND name = 'departmentId'
            """, (department_entity_id,))
            department_id_attr = cursor.fetchone()
            
            # Create departmentId attribute for Department if it doesn't exist
            if not department_id_attr:
                logger.info("Creating departmentId attribute for Department")
                
                # Get next attribute ID
                cursor.execute("""
                    SELECT attribute_id FROM entity_attributes
                    WHERE entity_id = %s
                    ORDER BY attribute_id DESC
                    LIMIT 1
                """, (department_entity_id,))
                
                result = cursor.fetchone()
                if result and result[0].startswith(f'{department_entity_id}.At'):
                    num = int(result[0].split('At')[1]) + 1
                    attr_id = f"{department_entity_id}.At{num}"
                else:
                    attr_id = f"{department_entity_id}.At1"
                
                # Insert attribute
                cursor.execute("""
                    INSERT INTO entity_attributes 
                    (attribute_id, entity_id, name, type, primary_key, created_at, updated_at)
                    VALUES (%s, %s, %s, %s, %s, NOW(), NOW())
                """, (attr_id, department_entity_id, 'departmentId', 'string', True))
                
                conn.commit()
                department_id_attr = (attr_id, 'departmentId')
                logger.info(f"Created departmentId attribute for Department with ID: {attr_id}")
            else:
                logger.info(f"Found departmentId attribute for Department: {department_id_attr[0]}")
            
            # Check if relationship exists
            cursor.execute("""
                SELECT id FROM entity_relationships
                WHERE source_entity_id = %s AND target_entity_id = %s
                AND source_attribute_id = %s AND target_attribute_id = %s
            """, (employee_entity_id, department_entity_id, 
                  employee_dept_attr[0], department_id_attr[0]))
            
            relationship = cursor.fetchone()
            
            # Set relationship properties
            on_delete = "RESTRICT"
            on_update = "CASCADE"
            foreign_key_type = "NON-NULLABLE"
            relationship_type = "many-to-one"
            
            if relationship:
                # Update existing relationship
                relationship_id = relationship[0]
                logger.info(f"Updating existing relationship with ID: {relationship_id}")
                
                cursor.execute("""
                    UPDATE entity_relationships
                    SET relationship_type = %s,
                        on_delete = %s,
                        on_update = %s,
                        foreign_key_type = %s,
                        updated_at = NOW()
                    WHERE id = %s
                """, (relationship_type, on_delete, on_update, foreign_key_type, relationship_id))
                
                conn.commit()
            else:
                # Create new relationship
                logger.info("Creating new Employee to Department relationship")
                
                cursor.execute("""
                    INSERT INTO entity_relationships
                    (source_entity_id, target_entity_id, relationship_type,
                     source_attribute_id, target_attribute_id,
                     on_delete, on_update, foreign_key_type,
                     created_at, updated_at)
                    VALUES (%s, %s, %s, %s, %s, %s, %s, %s, NOW(), NOW())
                    RETURNING id
                """, (employee_entity_id, department_entity_id, relationship_type,
                      employee_dept_attr[0], department_id_attr[0],
                      on_delete, on_update, foreign_key_type))
                
                relationship_id = cursor.fetchone()[0]
                conn.commit()
                logger.info(f"Created relationship with ID: {relationship_id}")
            
            # Verify relationship properties
            cursor.execute("""
                SELECT 
                    er.id, 
                    s.name as source_entity, 
                    t.name as target_entity, 
                    er.relationship_type, 
                    sa.name as source_attribute, 
                    ta.name as target_attribute,
                    er.on_delete,
                    er.on_update,
                    er.foreign_key_type
                FROM 
                    entity_relationships er
                    JOIN entities s ON er.source_entity_id = s.entity_id
                    JOIN entities t ON er.target_entity_id = t.entity_id
                    JOIN entity_attributes sa ON er.source_attribute_id = sa.attribute_id
                    JOIN entity_attributes ta ON er.target_attribute_id = ta.attribute_id
                WHERE 
                    er.id = %s
            """, (relationship_id,))
            
            result = cursor.fetchone()
            
            if result:
                logger.info("Relationship verification:")
                logger.info(f"  - ID: {result[0]}")
                logger.info(f"  - Source: {result[1]}.{result[4]}")
                logger.info(f"  - Target: {result[2]}.{result[5]}")
                logger.info(f"  - Type: {result[3]}")
                logger.info(f"  - ON DELETE: {result[6]}")
                logger.info(f"  - ON UPDATE: {result[7]}")
                logger.info(f"  - Foreign Key Type: {result[8]}")
            else:
                logger.error("Failed to verify relationship")
    except Exception as e:
        logger.error(f"Error: {str(e)}")
        conn.rollback()
    finally:
        conn.close()

if __name__ == "__main__":
    fix_attributes_and_relationship()
