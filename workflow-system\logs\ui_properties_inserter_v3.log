2025-06-23 11:30:14,886 - inserters.v3.roles.ui_properties_inserter - INFO - Starting deploy_single_ui_property_to_workflow_temp for UI property: UI1
2025-06-23 11:30:57,514 - inserters.v3.roles.ui_properties_inserter - INFO - Starting deploy_single_ui_property_to_workflow_temp for UI property: UI5
2025-06-23 11:31:58,136 - inserters.v3.roles.ui_properties_inserter - INFO - Starting deploy_single_ui_property_to_workflow_temp for UI property: UI1
2025-06-23 11:32:27,532 - inserters.v3.roles.ui_properties_inserter - INFO - Starting deploy_single_ui_property_to_workflow_temp for UI property: UI5
2025-06-23 14:03:50,421 - inserters.v3.roles.ui_properties_inserter - INFO - Starting deploy_single_ui_property_to_workflow_temp for UI property: UI001
2025-06-23 14:03:50,562 - inserters.v3.roles.ui_properties_inserter - INFO - Starting process_mongo_ui_properties_to_workflow_temp
2025-06-23 14:03:50,564 - inserters.v3.roles.ui_properties_inserter - INFO - Found 1 UI properties with status 'draft' in MongoDB
2025-06-23 14:03:50,573 - inserters.v3.roles.ui_properties_inserter - INFO - Starting insert_ui_property_to_workflow_temp for UI property: UI1
2025-06-23 14:03:50,573 - inserters.v3.roles.ui_properties_inserter - INFO - Field validation passed: 1/1 required fields, 21/21 optional fields
2025-06-23 14:03:50,590 - inserters.v3.roles.ui_properties_inserter - INFO - Generated next UI property ID: UI1 -> UI7
2025-06-23 14:03:50,590 - inserters.v3.roles.ui_properties_inserter - INFO - Incremented UI property ID from UI1 to UI7
2025-06-23 14:03:50,590 - inserters.v3.roles.ui_properties_inserter - INFO - Additional MongoDB fields preserved: {'original_created_at': '2025-06-23T09:00:43.903023', 'original_updated_at': '2025-06-23T09:00:45.190172', 'original_created_by': 'Tarun', 'original_updated_by': 'Tarun', 'original_ui_property_id': 'UI1', 'mongo_id': '685903f05e423f11979d4093'}
2025-06-23 14:03:50,592 - inserters.v3.roles.ui_properties_inserter - INFO - Successfully inserted UI property to workflow_temp with ID: UI7
2025-06-23 14:03:50,595 - inserters.v3.roles.ui_properties_inserter - INFO - Updated MongoDB status to deployed_to_temp for UI property: UI7
2025-06-23 14:03:50,595 - inserters.v3.roles.ui_properties_inserter - INFO - Completed process_mongo_ui_properties_to_workflow_temp: 1 successful, 0 failed
2025-06-23 14:09:01,499 - inserters.v3.roles.ui_properties_inserter - INFO - Starting deploy_single_ui_property_to_workflow_temp for UI property: UI001
2025-06-23 14:09:01,601 - inserters.v3.roles.ui_properties_inserter - INFO - Starting process_mongo_ui_properties_to_workflow_temp
2025-06-23 14:09:01,604 - inserters.v3.roles.ui_properties_inserter - INFO - Found 0 UI properties with status 'draft' in MongoDB
2025-06-23 14:09:01,604 - inserters.v3.roles.ui_properties_inserter - INFO - Completed process_mongo_ui_properties_to_workflow_temp: 0 successful, 0 failed
2025-06-23 14:32:33,836 - inserters.v3.roles.ui_properties_inserter - INFO - Starting deploy_single_ui_property_to_workflow_temp for UI property: UI001
2025-06-23 14:32:33,918 - inserters.v3.roles.ui_properties_inserter - INFO - Starting process_mongo_ui_properties_to_workflow_temp
2025-06-23 14:32:33,921 - inserters.v3.roles.ui_properties_inserter - INFO - Found 0 UI properties with status 'draft' in MongoDB
2025-06-23 14:32:33,921 - inserters.v3.roles.ui_properties_inserter - INFO - Completed process_mongo_ui_properties_to_workflow_temp: 0 successful, 0 failed
2025-06-23 14:38:53,271 - inserters.v3.roles.ui_properties_inserter - INFO - Starting deploy_single_ui_property_to_workflow_temp for UI property: UI001
2025-06-23 14:38:53,399 - inserters.v3.roles.ui_properties_inserter - INFO - Starting process_mongo_ui_properties_to_workflow_temp
2025-06-23 14:38:53,401 - inserters.v3.roles.ui_properties_inserter - INFO - Found 0 UI properties with status 'draft' in MongoDB
2025-06-23 14:38:53,401 - inserters.v3.roles.ui_properties_inserter - INFO - Completed process_mongo_ui_properties_to_workflow_temp: 0 successful, 0 failed
2025-06-24 04:45:02,866 - inserters.v3.roles.ui_properties_inserter - INFO - Starting deploy_single_ui_property_to_workflow_temp for UI property: UI001
2025-06-24 04:45:03,007 - inserters.v3.roles.ui_properties_inserter - INFO - Starting process_mongo_ui_properties_to_workflow_temp
2025-06-24 04:45:03,010 - inserters.v3.roles.ui_properties_inserter - INFO - Found 0 UI properties with status 'draft' in MongoDB
2025-06-24 04:45:03,010 - inserters.v3.roles.ui_properties_inserter - INFO - Completed process_mongo_ui_properties_to_workflow_temp: 0 successful, 0 failed
2025-06-24 11:43:35,694 - inserters.v3.roles.ui_properties_inserter - INFO - Starting process_mongo_ui_properties_to_workflow_runtime
2025-06-24 11:43:35,700 - inserters.v3.roles.ui_properties_inserter - INFO - Found 2 deployed_to_temp + 0 draft = 2 total UI properties in MongoDB
2025-06-24 11:43:35,708 - inserters.v3.roles.ui_properties_inserter - INFO - Starting insert_ui_property_to_workflow_runtime for UI property: UI5
2025-06-24 11:43:35,708 - inserters.v3.roles.ui_properties_inserter - INFO - Field validation passed: 1/1 required fields, 21/21 optional fields
2025-06-24 11:43:35,724 - inserters.v3.roles.ui_properties_inserter - INFO - Generated next UI property ID: UI5 -> UI8
2025-06-24 11:43:35,724 - inserters.v3.roles.ui_properties_inserter - INFO - Incremented UI property ID from UI5 to UI8
2025-06-24 11:43:35,724 - inserters.v3.roles.ui_properties_inserter - INFO - Additional MongoDB fields preserved: {'original_created_at': '2025-06-20T09:54:22.597847', 'original_updated_at': '2025-06-20T12:19:39.187272', 'original_created_by': 'Tarun', 'original_updated_by': 'Tarun', 'original_ui_property_id': 'UI5', 'mongo_id': '68552fce229f6505fc03bb6b'}
2025-06-24 11:43:35,727 - inserters.v3.roles.ui_properties_inserter - INFO - Successfully inserted UI property to workflow_runtime with ID: UI8
2025-06-24 11:43:35,730 - inserters.v3.roles.ui_properties_inserter - INFO - Updated MongoDB status to deployed_to_production for UI property: UI8
2025-06-24 11:43:35,737 - inserters.v3.roles.ui_properties_inserter - INFO - Starting insert_ui_property_to_workflow_runtime for UI property: UI1
2025-06-24 11:43:35,738 - inserters.v3.roles.ui_properties_inserter - INFO - Field validation passed: 1/1 required fields, 21/21 optional fields
2025-06-24 11:43:35,752 - inserters.v3.roles.ui_properties_inserter - INFO - Generated next UI property ID: UI1 -> UI9
2025-06-24 11:43:35,752 - inserters.v3.roles.ui_properties_inserter - INFO - Incremented UI property ID from UI1 to UI9
2025-06-24 11:43:35,752 - inserters.v3.roles.ui_properties_inserter - INFO - Additional MongoDB fields preserved: {'original_created_at': '2025-06-23T09:00:43.903023', 'original_updated_at': '2025-06-23T14:03:50.593286', 'original_created_by': 'Tarun', 'original_updated_by': 'Tarun', 'original_ui_property_id': 'UI1', 'mongo_id': '685903f05e423f11979d4093'}
2025-06-24 11:43:35,755 - inserters.v3.roles.ui_properties_inserter - INFO - Successfully inserted UI property to workflow_runtime with ID: UI9
2025-06-24 11:43:35,757 - inserters.v3.roles.ui_properties_inserter - INFO - Updated MongoDB status to deployed_to_production for UI property: UI9
2025-06-24 11:43:35,757 - inserters.v3.roles.ui_properties_inserter - INFO - Completed process_mongo_ui_properties_to_workflow_runtime: 2 successful, 0 failed
