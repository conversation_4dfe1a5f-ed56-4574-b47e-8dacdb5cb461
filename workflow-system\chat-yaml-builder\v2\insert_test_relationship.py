"""
<PERSON><PERSON><PERSON> to directly insert a test relationship into the database.
"""

import os
import sys
import logging

# Add parent directory to path to import modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from db_utils import execute_query

# Set up logging
logging.basicConfig(level=logging.INFO, 
                    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger('insert_test_relationship')

def insert_test_relationship(schema_name: str = 'workflow_temp') -> None:
    """
    Insert a test relationship into the database.
    
    Args:
        schema_name: Database schema name
    """
    logger.info(f"Inserting test relationship into schema: {schema_name}")
    
    # First, check if the entity_relationships table exists
    success, query_messages, table_exists = execute_query(
        f"""
        SELECT EXISTS (
            SELECT FROM information_schema.tables 
            WHERE table_schema = %s
            AND table_name = 'entity_relationships'
        )
        """,
        (schema_name,)
    )
    
    if not success:
        logger.error(f"Failed to check if entity_relationships table exists: {query_messages}")
        return
    
    if not table_exists or not table_exists[0][0]:
        logger.error(f"entity_relationships table does not exist in schema {schema_name}")
        return
    
    logger.info("entity_relationships table exists")
    
    # Check if there are any entities in the database
    success, query_messages, entities = execute_query(
        f"SELECT entity_id, name FROM {schema_name}.entities",
        schema_name=schema_name
    )
    
    if not success:
        logger.error(f"Failed to query entities: {query_messages}")
        return
    
    if not entities:
        logger.error("No entities found in the database")
        return
    
    logger.info(f"Found {len(entities)} entities in the database:")
    for entity in entities:
        logger.info(f"  - {entity}")
    
    # Get the Employee entity ID
    employee_entity_id = None
    department_entity_id = None
    for entity in entities:
        if entity[1] == 'Employee':
            employee_entity_id = entity[0]
        elif entity[1] == 'Department':
            department_entity_id = entity[0]
    
    if not employee_entity_id:
        logger.error("Employee entity not found")
        return
    
    if not department_entity_id:
        logger.error("Department entity not found")
        return
    
    logger.info(f"Employee entity ID: {employee_entity_id}")
    logger.info(f"Department entity ID: {department_entity_id}")
    
    # Get the departmentId attribute ID for Employee
    success, query_messages, dept_id_attr = execute_query(
        f"SELECT attribute_id FROM {schema_name}.entity_attributes WHERE entity_id = %s AND name = %s",
        (employee_entity_id, 'departmentId'),
        schema_name
    )
    
    if not success or not dept_id_attr:
        logger.error(f"Failed to get departmentId attribute ID: {query_messages}")
        return
    
    dept_id_attr_id = dept_id_attr[0][0]
    logger.info(f"departmentId attribute ID: {dept_id_attr_id}")
    
    # Get the departmentId attribute ID for Department
    success, query_messages, dept_pk_attr = execute_query(
        f"SELECT attribute_id FROM {schema_name}.entity_attributes WHERE entity_id = %s AND name = %s",
        (department_entity_id, 'departmentId'),
        schema_name
    )
    
    if not success or not dept_pk_attr:
        logger.error(f"Failed to get departmentId attribute ID for Department: {query_messages}")
        return
    
    dept_pk_attr_id = dept_pk_attr[0][0]
    logger.info(f"departmentId attribute ID for Department: {dept_pk_attr_id}")
    
    # Insert the relationship
    logger.info("Inserting relationship")
    success, query_messages, _ = execute_query(
        f"""
        INSERT INTO {schema_name}.entity_relationships (
            source_entity_id, target_entity_id, relationship_type, 
            source_attribute_id, target_attribute_id,
            created_at, updated_at
        ) VALUES (%s, %s, %s, %s, %s, NOW(), NOW())
        """,
        (
            employee_entity_id,
            department_entity_id,
            'many-to-one',
            dept_id_attr_id,
            dept_pk_attr_id
        ),
        schema_name
    )
    
    if not success:
        logger.error(f"Failed to insert relationship: {query_messages}")
        return
    
    logger.info("Successfully inserted relationship")
    
    # Verify the relationship was inserted
    success, query_messages, relationships = execute_query(
        f"""
        SELECT er.id, s.name as source_entity, t.name as target_entity, 
               er.relationship_type, sa.name as source_attribute, ta.name as target_attribute
        FROM {schema_name}.entity_relationships er
        JOIN {schema_name}.entities s ON er.source_entity_id = s.entity_id
        JOIN {schema_name}.entities t ON er.target_entity_id = t.entity_id
        JOIN {schema_name}.entity_attributes sa ON er.source_attribute_id = sa.attribute_id
        JOIN {schema_name}.entity_attributes ta ON er.target_attribute_id = ta.attribute_id
        ORDER BY s.name, t.name
        """,
        schema_name=schema_name
    )
    
    if not success:
        logger.error(f"Failed to verify relationship: {query_messages}")
        return
    
    if not relationships:
        logger.warning("No relationships found in the database after insertion")
    else:
        logger.info("Relationships in the database:")
        for rel in relationships:
            logger.info(f"  - {rel[1]} -> {rel[2]} ({rel[3]}) using {rel[4]} -> {rel[5]}")

if __name__ == "__main__":
    schema_name = sys.argv[1] if len(sys.argv) > 1 else "workflow_temp"
    insert_test_relationship(schema_name)
