2025-05-12 04:52:10,596 - entity_example_detailed - INFO - === Parsing Entity ===
2025-05-12 04:52:10,596 - prescriptive_parser - INFO - Initializing PrescriptiveParser
2025-05-12 04:52:10,596 - entity_example_detailed - INFO - Parsing prescriptive text:
2025-05-12 04:52:10,596 - entity_example_detailed - INFO - Employee has employeeId^PK, firstName, lastName, email, phoneNumber, departmentId^FK, managerId^FK, hireDate, status (Active, Inactive, OnLeave), salary, performanceRating.

* Employee has many-to-one relationship with Department using Employee.departmentId to Department.departmentId^PK
* Employee has many-to-one relationship with Employee using Employee.managerId to Employee.employeeId^PK

* Employee.email must be unique
* Employee.email must match pattern "^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$"
* Employee.hireDate must be before current date
* Employee.salary must be greater than 0

BusinessRule EMP001 for Employee:
* Employee.status must be Active to receive performance reviews
* Employee.hireDate must be at least 90 days before Employee.performanceRating can be set

Employee has employeeId, firstName, lastName, fullName[derived].

CalculatedField CF001 for Employee.fullName:
* Formula: CONCAT(firstName, ' ', lastName)
* Logic Layer: Application
* Caching: Session
* Dependencies: Employee.firstName, Employee.lastName

* Archive Strategy for Employee:
  - Trigger: Event-based
  - Criteria: When Employee.status changes to 'Inactive' and remains so for 1 year
  - Retention: 7 years
  - Storage: Cold storage
  - Access Pattern: Read-only through HR archive portal
  - Restoration: Manual process requiring HR Director approval

* History Tracking for Employee:
  - Tracked Attributes: Employee.salary, Employee.departmentId, Employee.managerId, Employee.status
  - Tracking Method: Audit table
  - Granularity: Change level
  - Retention: 7 years
  - Access Control: HR Managers and Compliance Officers only

Department has departmentId^PK, name, managerId^FK, location, budget.

* Department has one-to-one relationship with Employee using Department.managerId to Employee.employeeId^PK
* Department has one-to-many relationship with Employee using Department.departmentId to Employee.departmentId^FK

* Department.name must be unique
* Department.budget must be greater than 0

BusinessRule DEPT001 for Department:
* Department.budget must be approved by Finance before changes
* Department.managerId must reference an Employee with status = 'Active'

* Archive Strategy for Department:
  - Trigger: Event-based
  - Criteria: When Department is marked as 'Dissolved'
  - Retention: 10 years
  - Storage: Cold storage
  - Access Pattern: Read-only through Admin portal
  - Restoration: Manual process requiring CEO approval

2025-05-12 04:52:10,596 - prescriptive_parser - INFO - Parsing entities prescriptive text
2025-05-12 04:52:10,597 - entity_parser - INFO - Starting to parse entity definitions
2025-05-12 04:52:10,597 - entity_parser - INFO - Parsing entity: Employee
2025-05-12 04:52:10,597 - entity_parser - INFO - Successfully parsed entity 'Employee'
2025-05-12 04:52:10,597 - entity_parser - INFO - Parsing entity: Employee
2025-05-12 04:52:10,597 - entity_parser - INFO - Successfully parsed entity 'Employee'
2025-05-12 04:52:10,598 - entity_parser - INFO - Parsing entity: Department
2025-05-12 04:52:10,598 - entity_parser - INFO - Successfully parsed entity 'Department'
2025-05-12 04:52:10,598 - entity_parser - INFO - Successfully parsed 2 entities
2025-05-12 04:52:10,598 - entity_example_detailed - INFO - Parsed entity data:
2025-05-12 04:52:10,598 - entity_example_detailed - INFO - Entity: Employee
2025-05-12 04:52:10,598 - entity_example_detailed - INFO -   Description: 
2025-05-12 04:52:10,598 - entity_example_detailed - INFO -   Attributes:
2025-05-12 04:52:10,598 - entity_example_detailed - INFO -     employeeId: {'type': 'string', 'required': False, 'primary_key': False, 'foreign_key': False}
2025-05-12 04:52:10,598 - entity_example_detailed - INFO -     firstName: {'type': 'string', 'required': False, 'primary_key': False, 'foreign_key': False}
2025-05-12 04:52:10,598 - entity_example_detailed - INFO -     lastName: {'type': 'string', 'required': False, 'primary_key': False, 'foreign_key': False}
2025-05-12 04:52:10,598 - entity_example_detailed - INFO -     fullName[derived]: {'type': 'string', 'required': False, 'primary_key': False, 'foreign_key': False}
2025-05-12 04:52:10,598 - entity_example_detailed - INFO -   Relationships:
2025-05-12 04:52:10,598 - entity_example_detailed - INFO -   Business Rules:
2025-05-12 04:52:10,598 - entity_example_detailed - INFO -   Calculated Fields:
2025-05-12 04:52:10,598 - entity_example_detailed - INFO -     CF001: {'attribute': 'fullName', 'formula': "CONCAT(firstName, ' ', lastName)", 'logic_layer': 'Application', 'caching': 'Session', 'dependencies': 'Employee.firstName, Employee.lastName', 'archive_strategy_for_employee': '', 'history_tracking_for_employee': ''}
2025-05-12 04:52:10,598 - entity_example_detailed - INFO -   Lifecycle Management:
2025-05-12 04:52:10,598 - entity_example_detailed - INFO - Entity: Department
2025-05-12 04:52:10,598 - entity_example_detailed - INFO -   Description: 
2025-05-12 04:52:10,598 - entity_example_detailed - INFO -   Attributes:
2025-05-12 04:52:10,598 - entity_example_detailed - INFO -     departmentId: {'type': 'string', 'required': True, 'primary_key': True, 'foreign_key': False}
2025-05-12 04:52:10,598 - entity_example_detailed - INFO -     name: {'type': 'string', 'required': False, 'primary_key': False, 'foreign_key': False}
2025-05-12 04:52:10,598 - entity_example_detailed - INFO -     managerId: {'type': 'string', 'required': False, 'primary_key': False, 'foreign_key': True}
2025-05-12 04:52:10,598 - entity_example_detailed - INFO -     location: {'type': 'string', 'required': False, 'primary_key': False, 'foreign_key': False}
2025-05-12 04:52:10,598 - entity_example_detailed - INFO -     budget: {'type': 'string', 'required': False, 'primary_key': False, 'foreign_key': False}
2025-05-12 04:52:10,598 - entity_example_detailed - INFO -   Relationships:
2025-05-12 04:52:10,598 - entity_example_detailed - INFO -     one-to-one_Employee: {'type': 'one-to-one', 'entity': 'Employee', 'source_attribute': 'managerId', 'target_attribute': 'employeeId'}
2025-05-12 04:52:10,598 - entity_example_detailed - INFO -     one-to-many_Employee: {'type': 'one-to-many', 'entity': 'Employee', 'source_attribute': 'departmentId', 'target_attribute': 'departmentId'}
2025-05-12 04:52:10,598 - entity_example_detailed - INFO -   Business Rules:
2025-05-12 04:52:10,598 - entity_example_detailed - INFO -     DEPT001: {'conditions': ['Department.budget must be approved by Finance before changes', "Department.managerId must reference an Employee with status = 'Active'", 'Archive Strategy for Department:']}
2025-05-12 04:52:10,598 - entity_example_detailed - INFO -   Calculated Fields:
2025-05-12 04:52:10,598 - entity_example_detailed - INFO -   Lifecycle Management:
2025-05-12 04:52:10,598 - entity_example_detailed - INFO - === Comparing with insert_generator.py ===
2025-05-12 04:52:10,598 - entity_example_detailed - INFO - Converted entity data to insert_generator.py format:
2025-05-12 04:52:10,598 - entity_example_detailed - INFO - {
  "entities": [
    {
      "id": "en_employee",
      "name": "Employee",
      "type": "standard",
      "version": "1.0",
      "status": "active",
      "attributes": [
        {
          "id": "at_employeeid",
          "name": "employeeId",
          "display_name": "employeeId",
          "datatype": "string",
          "required": false,
          "version": "1.0",
          "status": "active"
        },
        {
          "id": "at_firstname",
          "name": "firstName",
          "display_name": "firstName",
          "datatype": "string",
          "required": false,
          "version": "1.0",
          "status": "active"
        },
        {
          "id": "at_lastname",
          "name": "lastName",
          "display_name": "lastName",
          "datatype": "string",
          "required": false,
          "version": "1.0",
          "status": "active"
        },
        {
          "id": "at_fullname[derived]",
          "name": "fullName[derived]",
          "display_name": "fullName[derived]",
          "datatype": "string",
          "required": false,
          "version": "1.0",
          "status": "active"
        }
      ]
    },
    {
      "id": "en_department",
      "name": "Department",
      "type": "standard",
      "version": "1.0",
      "status": "active",
      "attributes": [
        {
          "id": "at_departmentid",
          "name": "departmentId",
          "display_name": "departmentId",
          "datatype": "string",
          "required": true,
          "version": "1.0",
          "status": "active"
        },
        {
          "id": "at_name",
          "name": "name",
          "display_name": "name",
          "datatype": "string",
          "required": false,
          "version": "1.0",
          "status": "active"
        },
        {
          "id": "at_managerid",
          "name": "managerId",
          "display_name": "managerId",
          "datatype": "string",
          "required": false,
          "version": "1.0",
          "status": "active"
        },
        {
          "id": "at_location",
          "name": "location",
          "display_name": "location",
          "datatype": "string",
          "required": false,
          "version": "1.0",
          "status": "active"
        },
        {
          "id": "at_budget",
          "name": "budget",
          "display_name": "budget",
          "datatype": "string",
          "required": false,
          "version": "1.0",
          "status": "active"
        }
      ],
      "relationships": [
        {
          "entity_id": "Employee",
          "type": "one-to-one",
          "through_attribute": "",
          "to_attribute": ""
        },
        {
          "entity_id": "Employee",
          "type": "one-to-many",
          "through_attribute": "",
          "to_attribute": ""
        }
      ]
    }
  ]
}
2025-05-12 04:52:10,599 - entity_example_detailed - INFO - [COMPARISON] Simulating insert_generator.py processing...
2025-05-12 04:52:10,599 - entity_example_detailed - INFO - [COMPARISON] Entity table inserts:
2025-05-12 04:52:10,599 - entity_example_detailed - INFO - [COMPARISON] SQL: 
        INSERT INTO entities (entity_id, name, type, version, status, created_at, updated_at)
        VALUES ('en_employee', 'Employee', 'standard', '1.0', 'active', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);
        
2025-05-12 04:52:10,599 - entity_example_detailed - INFO - [COMPARISON] SQL: 
        INSERT INTO entities (entity_id, name, type, version, status, created_at, updated_at)
        VALUES ('en_department', 'Department', 'standard', '1.0', 'active', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);
        
2025-05-12 04:52:10,599 - entity_example_detailed - INFO - [COMPARISON] Entity attribute inserts:
2025-05-12 04:52:10,599 - entity_example_detailed - INFO - [COMPARISON] SQL: 
            INSERT INTO entity_attributes (attribute_id, entity_id, name, display_name, datatype, version, status, required, created_at, updated_at)
            VALUES ('at_employeeid', 'en_employee', 'employeeId', 'employeeId', 'string', '1.0', 'active', False, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);
            
2025-05-12 04:52:10,599 - entity_example_detailed - INFO - [COMPARISON] SQL: 
            INSERT INTO entity_attributes (attribute_id, entity_id, name, display_name, datatype, version, status, required, created_at, updated_at)
            VALUES ('at_firstname', 'en_employee', 'firstName', 'firstName', 'string', '1.0', 'active', False, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);
            
2025-05-12 04:52:10,599 - entity_example_detailed - INFO - [COMPARISON] SQL: 
            INSERT INTO entity_attributes (attribute_id, entity_id, name, display_name, datatype, version, status, required, created_at, updated_at)
            VALUES ('at_lastname', 'en_employee', 'lastName', 'lastName', 'string', '1.0', 'active', False, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);
            
2025-05-12 04:52:10,599 - entity_example_detailed - INFO - [COMPARISON] SQL: 
            INSERT INTO entity_attributes (attribute_id, entity_id, name, display_name, datatype, version, status, required, created_at, updated_at)
            VALUES ('at_fullname[derived]', 'en_employee', 'fullName[derived]', 'fullName[derived]', 'string', '1.0', 'active', False, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);
            
2025-05-12 04:52:10,599 - entity_example_detailed - INFO - [COMPARISON] SQL: 
            INSERT INTO entity_attributes (attribute_id, entity_id, name, display_name, datatype, version, status, required, created_at, updated_at)
            VALUES ('at_departmentid', 'en_department', 'departmentId', 'departmentId', 'string', '1.0', 'active', True, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);
            
2025-05-12 04:52:10,599 - entity_example_detailed - INFO - [COMPARISON] SQL: 
            INSERT INTO entity_attributes (attribute_id, entity_id, name, display_name, datatype, version, status, required, created_at, updated_at)
            VALUES ('at_name', 'en_department', 'name', 'name', 'string', '1.0', 'active', False, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);
            
2025-05-12 04:52:10,599 - entity_example_detailed - INFO - [COMPARISON] SQL: 
            INSERT INTO entity_attributes (attribute_id, entity_id, name, display_name, datatype, version, status, required, created_at, updated_at)
            VALUES ('at_managerid', 'en_department', 'managerId', 'managerId', 'string', '1.0', 'active', False, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);
            
2025-05-12 04:52:10,599 - entity_example_detailed - INFO - [COMPARISON] SQL: 
            INSERT INTO entity_attributes (attribute_id, entity_id, name, display_name, datatype, version, status, required, created_at, updated_at)
            VALUES ('at_location', 'en_department', 'location', 'location', 'string', '1.0', 'active', False, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);
            
2025-05-12 04:52:10,599 - entity_example_detailed - INFO - [COMPARISON] SQL: 
            INSERT INTO entity_attributes (attribute_id, entity_id, name, display_name, datatype, version, status, required, created_at, updated_at)
            VALUES ('at_budget', 'en_department', 'budget', 'budget', 'string', '1.0', 'active', False, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);
            
2025-05-12 04:52:10,599 - entity_example_detailed - INFO - [COMPARISON] Entity relationship inserts:
2025-05-12 04:52:10,599 - entity_example_detailed - INFO - [COMPARISON] SQL: 
                INSERT INTO entity_relationships (source_entity_id, target_entity_id, relationship_type, source_attribute_id, target_attribute_id, created_at, updated_at)
                VALUES ('en_department', 'Employee', 'one-to-one', '', '', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);
                
2025-05-12 04:52:10,599 - entity_example_detailed - INFO - [COMPARISON] SQL: 
                INSERT INTO entity_relationships (source_entity_id, target_entity_id, relationship_type, source_attribute_id, target_attribute_id, created_at, updated_at)
                VALUES ('en_department', 'Employee', 'one-to-many', '', '', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);
                
2025-05-12 04:52:10,599 - entity_example_detailed - INFO - [COMPARISON] Comparing with v2 deployer:
2025-05-12 04:52:10,599 - entity_example_detailed - INFO - [COMPARISON] 1. v2 deployer uses prescriptive_parser.py to parse prescriptive text into structured data
2025-05-12 04:52:10,599 - entity_example_detailed - INFO - [COMPARISON] 2. v2 deployer uses component_deployer.py to deploy the structured data to the database
2025-05-12 04:52:10,599 - entity_example_detailed - INFO - [COMPARISON] 3. v2 deployer uses entity_deployer.py to deploy entities to the database
2025-05-12 04:52:10,599 - entity_example_detailed - INFO - [COMPARISON] Key differences:
2025-05-12 04:52:10,599 - entity_example_detailed - INFO - [COMPARISON] 1. insert_generator.py uses a JSON file as input, while v2 uses prescriptive text
2025-05-12 04:52:10,599 - entity_example_detailed - INFO - [COMPARISON] 2. insert_generator.py generates SQL statements, while v2 executes them directly
2025-05-12 04:52:10,599 - entity_example_detailed - INFO - [COMPARISON] 3. insert_generator.py handles multiple components at once, while v2 can handle them separately
2025-05-12 04:52:10,599 - entity_example_detailed - INFO - [COMPARISON] 4. v2 has more validation and error handling
2025-05-12 04:52:10,599 - entity_example_detailed - INFO - [COMPARISON] 5. v2 uses a temporary schema for validation before promoting to runtime schema
2025-05-12 04:52:10,599 - entity_example_detailed - INFO - === Deploying Entity ===
2025-05-12 04:52:10,599 - component_deployer - INFO - ComponentDeployer initialized with use_temp_schema=True
2025-05-12 04:52:10,604 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:10,636 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:10,640 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:10,650 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:10,657 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:10,663 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:10,670 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:10,676 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:10,683 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:10,690 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:10,697 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:10,703 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:10,710 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:10,716 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:10,723 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:10,729 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:10,735 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:10,737 - component_deployer - INFO - Analyzing workflow_runtime schema to create necessary tables in temporary schema
2025-05-12 04:52:10,740 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:10,746 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:10,750 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:10,756 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:10,764 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:10,772 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:10,780 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:10,790 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:10,800 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:10,808 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:10,818 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:10,826 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:10,833 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:10,845 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:10,855 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:10,863 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:10,874 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:10,883 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:10,892 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:10,903 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:10,912 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:10,921 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:10,931 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:10,939 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:10,947 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:10,958 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:10,968 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:10,977 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:10,988 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:10,997 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:11,005 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:11,016 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:11,025 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:11,034 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:11,045 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:11,054 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:11,061 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:11,072 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:11,080 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:11,089 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:11,099 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:11,109 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:11,118 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:11,130 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:11,140 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:11,146 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:11,155 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:11,163 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:11,169 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:11,180 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:11,190 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:11,199 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:11,208 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:11,216 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:11,224 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:11,234 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:11,242 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:11,250 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:11,260 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:11,268 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:11,275 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:11,283 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:11,291 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:11,298 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:11,306 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:11,314 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:11,321 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:11,330 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:11,337 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:11,344 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:11,352 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:11,363 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:11,373 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:11,383 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:11,391 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:11,401 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:11,411 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:11,421 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:11,430 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:11,441 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:11,450 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:11,459 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:11,469 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:11,479 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:11,488 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:11,499 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:11,509 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:11,518 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:11,528 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:11,539 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:11,547 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:11,556 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:11,566 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:11,574 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:11,584 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:11,593 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:11,602 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:11,613 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:11,622 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:11,630 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:11,640 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:11,649 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:11,657 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:11,667 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:11,676 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:11,684 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:11,694 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:11,703 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:11,711 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:11,721 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:11,731 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:11,739 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:11,749 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:11,760 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:11,766 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:11,776 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:11,786 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:11,793 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:11,803 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:11,812 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:11,821 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:11,832 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:11,842 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:11,850 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:11,860 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:11,870 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:11,877 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:11,887 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:11,897 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:11,904 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:11,915 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:11,925 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:11,933 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:11,943 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:11,953 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:11,962 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:11,972 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:11,983 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:11,991 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:12,001 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:12,011 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:12,018 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:12,029 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:12,037 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:12,044 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:12,055 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:12,064 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:12,070 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:12,081 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:12,091 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:12,097 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:12,108 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:12,118 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:12,129 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:12,139 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:12,150 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:12,159 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:12,168 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:12,176 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:12,184 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:12,193 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:12,201 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:12,207 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:12,218 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:12,229 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:12,237 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:12,247 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:12,256 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:12,263 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:12,273 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:12,281 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:12,291 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:12,301 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:12,311 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:12,319 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:12,328 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:12,336 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:12,343 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:12,353 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:12,361 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:12,367 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:12,378 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:12,387 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:12,395 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:12,406 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:12,414 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:12,423 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:12,433 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:12,442 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:12,452 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:12,463 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:12,473 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:12,481 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:12,556 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:12,563 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:12,569 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:12,576 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:12,582 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:12,588 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:12,593 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:12,600 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:12,601 - db_utils - ERROR - Database error: column "tenant_id" referenced in foreign key constraint does not exist
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.UndefinedColumn: column "tenant_id" referenced in foreign key constraint does not exist

2025-05-12 04:52:12,608 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:12,609 - db_utils - ERROR - Database error: constraint "entity_attributes_entity_id_fkey" for relation "entity_attributes" already exists
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.DuplicateObject: constraint "entity_attributes_entity_id_fkey" for relation "entity_attributes" already exists

2025-05-12 04:52:12,613 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:12,620 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:12,621 - db_utils - ERROR - Database error: constraint "lo_input_items_lo_id_fkey" for relation "lo_input_items" already exists
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.DuplicateObject: constraint "lo_input_items_lo_id_fkey" for relation "lo_input_items" already exists

2025-05-12 04:52:12,626 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:12,627 - db_utils - ERROR - Database error: column "inherits_from" referenced in foreign key constraint does not exist
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.UndefinedColumn: column "inherits_from" referenced in foreign key constraint does not exist

2025-05-12 04:52:12,633 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:12,640 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:12,647 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:12,653 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:12,661 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:12,668 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:12,673 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:12,679 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:12,684 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:12,690 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:12,695 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:12,696 - db_utils - ERROR - Database error: constraint "input_dependencies_input_item_id_input_stack_id_fkey" for relation "input_dependencies" already exists
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.DuplicateObject: constraint "input_dependencies_input_item_id_input_stack_id_fkey" for relation "input_dependencies" already exists

2025-05-12 04:52:12,701 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:12,702 - db_utils - ERROR - Database error: constraint "input_dependencies_input_item_id_input_stack_id_fkey" for relation "input_dependencies" already exists
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.DuplicateObject: constraint "input_dependencies_input_item_id_input_stack_id_fkey" for relation "input_dependencies" already exists

2025-05-12 04:52:12,706 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:12,707 - db_utils - ERROR - Database error: constraint "input_dependencies_input_item_id_input_stack_id_fkey" for relation "input_dependencies" already exists
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.DuplicateObject: constraint "input_dependencies_input_item_id_input_stack_id_fkey" for relation "input_dependencies" already exists

2025-05-12 04:52:12,710 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:12,717 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:12,718 - db_utils - ERROR - Database error: constraint "input_dependencies_depends_on_id_depends_on_stack_id_fkey" for relation "input_dependencies" already exists
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.DuplicateObject: constraint "input_dependencies_depends_on_id_depends_on_stack_id_fkey" for relation "input_dependencies" already exists

2025-05-12 04:52:12,722 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:12,723 - db_utils - ERROR - Database error: constraint "input_dependencies_depends_on_id_depends_on_stack_id_fkey" for relation "input_dependencies" already exists
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.DuplicateObject: constraint "input_dependencies_depends_on_id_depends_on_stack_id_fkey" for relation "input_dependencies" already exists

2025-05-12 04:52:12,726 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:12,727 - db_utils - ERROR - Database error: constraint "input_dependencies_depends_on_id_depends_on_stack_id_fkey" for relation "input_dependencies" already exists
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.DuplicateObject: constraint "input_dependencies_depends_on_id_depends_on_stack_id_fkey" for relation "input_dependencies" already exists

2025-05-12 04:52:12,730 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:12,731 - db_utils - ERROR - Database error: constraint "lo_nested_functions_lo_id_fkey" for relation "lo_nested_functions" already exists
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.DuplicateObject: constraint "lo_nested_functions_lo_id_fkey" for relation "lo_nested_functions" already exists

2025-05-12 04:52:12,737 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:12,742 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:12,748 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:12,753 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:12,754 - db_utils - ERROR - Database error: constraint "dropdown_data_sources_input_item_id_input_stack_id_fkey" for relation "dropdown_data_sources" already exists
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.DuplicateObject: constraint "dropdown_data_sources_input_item_id_input_stack_id_fkey" for relation "dropdown_data_sources" already exists

2025-05-12 04:52:12,758 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:12,759 - db_utils - ERROR - Database error: constraint "dropdown_data_sources_input_item_id_input_stack_id_fkey" for relation "dropdown_data_sources" already exists
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.DuplicateObject: constraint "dropdown_data_sources_input_item_id_input_stack_id_fkey" for relation "dropdown_data_sources" already exists

2025-05-12 04:52:12,763 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:12,764 - db_utils - ERROR - Database error: constraint "dropdown_data_sources_input_item_id_input_stack_id_fkey" for relation "dropdown_data_sources" already exists
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.DuplicateObject: constraint "dropdown_data_sources_input_item_id_input_stack_id_fkey" for relation "dropdown_data_sources" already exists

2025-05-12 04:52:12,769 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:12,775 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:12,776 - db_utils - ERROR - Database error: constraint "lo_output_items_lo_id_fkey" for relation "lo_output_items" already exists
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.DuplicateObject: constraint "lo_output_items_lo_id_fkey" for relation "lo_output_items" already exists

2025-05-12 04:52:12,780 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:12,786 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:12,792 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:12,797 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:12,802 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:12,803 - db_utils - ERROR - Database error: there is no unique constraint matching given keys for referenced table "output_items"
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.InvalidForeignKey: there is no unique constraint matching given keys for referenced table "output_items"

2025-05-12 04:52:12,808 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:12,809 - db_utils - ERROR - Database error: there is no unique constraint matching given keys for referenced table "output_items"
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.InvalidForeignKey: there is no unique constraint matching given keys for referenced table "output_items"

2025-05-12 04:52:12,813 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:12,814 - db_utils - ERROR - Database error: there is no unique constraint matching given keys for referenced table "output_items"
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.InvalidForeignKey: there is no unique constraint matching given keys for referenced table "output_items"

2025-05-12 04:52:12,819 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:12,820 - db_utils - ERROR - Database error: there is no unique constraint matching given keys for referenced table "output_items"
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.InvalidForeignKey: there is no unique constraint matching given keys for referenced table "output_items"

2025-05-12 04:52:12,824 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:12,830 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:12,836 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:12,842 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:12,847 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:12,847 - db_utils - ERROR - Database error: constraint "local_objectives_go_id_fkey" for relation "local_objectives" already exists
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.DuplicateObject: constraint "local_objectives_go_id_fkey" for relation "local_objectives" already exists

2025-05-12 04:52:12,852 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:12,858 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:12,864 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:12,870 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:12,876 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:12,877 - db_utils - ERROR - Database error: constraint "lo_input_stack_lo_id_fkey" for relation "lo_input_stack" already exists
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.DuplicateObject: constraint "lo_input_stack_lo_id_fkey" for relation "lo_input_stack" already exists

2025-05-12 04:52:12,880 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:12,881 - db_utils - ERROR - Database error: constraint "lo_input_items_input_stack_id_fkey" for relation "lo_input_items" already exists
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.DuplicateObject: constraint "lo_input_items_input_stack_id_fkey" for relation "lo_input_items" already exists

2025-05-12 04:52:12,885 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:12,891 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:12,891 - db_utils - ERROR - Database error: constraint "lo_input_validations_input_item_id_input_stack_id_fkey" for relation "lo_input_validations" already exists
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.DuplicateObject: constraint "lo_input_validations_input_item_id_input_stack_id_fkey" for relation "lo_input_validations" already exists

2025-05-12 04:52:12,896 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:12,897 - db_utils - ERROR - Database error: constraint "lo_input_validations_input_item_id_input_stack_id_fkey" for relation "lo_input_validations" already exists
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.DuplicateObject: constraint "lo_input_validations_input_item_id_input_stack_id_fkey" for relation "lo_input_validations" already exists

2025-05-12 04:52:12,901 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:12,901 - db_utils - ERROR - Database error: constraint "lo_input_validations_input_item_id_input_stack_id_fkey" for relation "lo_input_validations" already exists
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.DuplicateObject: constraint "lo_input_validations_input_item_id_input_stack_id_fkey" for relation "lo_input_validations" already exists

2025-05-12 04:52:12,906 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:12,906 - db_utils - ERROR - Database error: constraint "lo_output_stack_lo_id_fkey" for relation "lo_output_stack" already exists
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.DuplicateObject: constraint "lo_output_stack_lo_id_fkey" for relation "lo_output_stack" already exists

2025-05-12 04:52:12,911 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:12,912 - db_utils - ERROR - Database error: constraint "lo_output_items_output_stack_id_fkey" for relation "lo_output_items" already exists
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.DuplicateObject: constraint "lo_output_items_output_stack_id_fkey" for relation "lo_output_items" already exists

2025-05-12 04:52:12,916 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:12,922 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:12,928 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:12,929 - db_utils - ERROR - Database error: constraint "lo_output_triggers_output_item_id_output_stack_id_fkey" for relation "lo_output_triggers" already exists
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.DuplicateObject: constraint "lo_output_triggers_output_item_id_output_stack_id_fkey" for relation "lo_output_triggers" already exists

2025-05-12 04:52:12,934 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:12,934 - db_utils - ERROR - Database error: constraint "lo_output_triggers_output_item_id_output_stack_id_fkey" for relation "lo_output_triggers" already exists
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.DuplicateObject: constraint "lo_output_triggers_output_item_id_output_stack_id_fkey" for relation "lo_output_triggers" already exists

2025-05-12 04:52:12,939 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:12,940 - db_utils - ERROR - Database error: constraint "lo_output_triggers_output_item_id_output_stack_id_fkey" for relation "lo_output_triggers" already exists
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.DuplicateObject: constraint "lo_output_triggers_output_item_id_output_stack_id_fkey" for relation "lo_output_triggers" already exists

2025-05-12 04:52:12,944 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:12,944 - db_utils - ERROR - Database error: constraint "lo_data_mapping_stack_lo_id_fkey" for relation "lo_data_mapping_stack" already exists
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.DuplicateObject: constraint "lo_data_mapping_stack_lo_id_fkey" for relation "lo_data_mapping_stack" already exists

2025-05-12 04:52:12,949 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:12,949 - db_utils - ERROR - Database error: constraint "lo_data_mappings_mapping_stack_id_fkey" for relation "lo_data_mappings" already exists
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.DuplicateObject: constraint "lo_data_mappings_mapping_stack_id_fkey" for relation "lo_data_mappings" already exists

2025-05-12 04:52:12,954 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:12,960 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:12,967 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:12,967 - db_utils - ERROR - Database error: constraint "input_data_sources_input_item_id_input_stack_id_fkey" for relation "input_data_sources" already exists
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.DuplicateObject: constraint "input_data_sources_input_item_id_input_stack_id_fkey" for relation "input_data_sources" already exists

2025-05-12 04:52:12,972 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:12,973 - db_utils - ERROR - Database error: constraint "input_data_sources_input_item_id_input_stack_id_fkey" for relation "input_data_sources" already exists
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.DuplicateObject: constraint "input_data_sources_input_item_id_input_stack_id_fkey" for relation "input_data_sources" already exists

2025-05-12 04:52:12,977 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:12,978 - db_utils - ERROR - Database error: constraint "input_data_sources_input_item_id_input_stack_id_fkey" for relation "input_data_sources" already exists
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.DuplicateObject: constraint "input_data_sources_input_item_id_input_stack_id_fkey" for relation "input_data_sources" already exists

2025-05-12 04:52:12,982 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:12,988 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:12,994 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:13,000 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:13,007 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:13,012 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:13,018 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:13,026 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:13,027 - db_utils - ERROR - Database error: there is no unique constraint matching given keys for referenced table "roles"
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.InvalidForeignKey: there is no unique constraint matching given keys for referenced table "roles"

2025-05-12 04:52:13,032 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:13,038 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:13,044 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:13,049 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:13,055 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:13,061 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:13,067 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:13,072 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:13,079 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:13,085 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:13,092 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:13,098 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:13,100 - component_deployer - INFO - Successfully analyzed workflow_runtime schema and created necessary tables
2025-05-12 04:52:13,100 - entity_example_detailed - INFO - Deploying entity to schema workflow_temp
2025-05-12 04:52:57,371 - entity_example_detailed - INFO - === Parsing Entity ===
2025-05-12 04:52:57,372 - prescriptive_parser - INFO - Initializing PrescriptiveParser
2025-05-12 04:52:57,372 - entity_example_detailed - INFO - Parsing prescriptive text:
2025-05-12 04:52:57,372 - entity_example_detailed - INFO - Employee has employeeId^PK, firstName, lastName, email, phoneNumber, departmentId^FK, managerId^FK, hireDate, status (Active, Inactive, OnLeave), salary, performanceRating.

* Employee has many-to-one relationship with Department using Employee.departmentId to Department.departmentId^PK
* Employee has many-to-one relationship with Employee using Employee.managerId to Employee.employeeId^PK

* Employee.email must be unique
* Employee.email must match pattern "^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$"
* Employee.hireDate must be before current date
* Employee.salary must be greater than 0

BusinessRule EMP001 for Employee:
* Employee.status must be Active to receive performance reviews
* Employee.hireDate must be at least 90 days before Employee.performanceRating can be set

Employee has employeeId, firstName, lastName, fullName[derived].

CalculatedField CF001 for Employee.fullName:
* Formula: CONCAT(firstName, ' ', lastName)
* Logic Layer: Application
* Caching: Session
* Dependencies: Employee.firstName, Employee.lastName

* Archive Strategy for Employee:
  - Trigger: Event-based
  - Criteria: When Employee.status changes to 'Inactive' and remains so for 1 year
  - Retention: 7 years
  - Storage: Cold storage
  - Access Pattern: Read-only through HR archive portal
  - Restoration: Manual process requiring HR Director approval

* History Tracking for Employee:
  - Tracked Attributes: Employee.salary, Employee.departmentId, Employee.managerId, Employee.status
  - Tracking Method: Audit table
  - Granularity: Change level
  - Retention: 7 years
  - Access Control: HR Managers and Compliance Officers only

Department has departmentId^PK, name, managerId^FK, location, budget.

* Department has one-to-one relationship with Employee using Department.managerId to Employee.employeeId^PK
* Department has one-to-many relationship with Employee using Department.departmentId to Employee.departmentId^FK

* Department.name must be unique
* Department.budget must be greater than 0

BusinessRule DEPT001 for Department:
* Department.budget must be approved by Finance before changes
* Department.managerId must reference an Employee with status = 'Active'

* Archive Strategy for Department:
  - Trigger: Event-based
  - Criteria: When Department is marked as 'Dissolved'
  - Retention: 10 years
  - Storage: Cold storage
  - Access Pattern: Read-only through Admin portal
  - Restoration: Manual process requiring CEO approval

2025-05-12 04:52:57,372 - prescriptive_parser - INFO - Parsing entities prescriptive text
2025-05-12 04:52:57,372 - entity_parser - INFO - Starting to parse entity definitions
2025-05-12 04:52:57,372 - entity_parser - INFO - Parsing entity: Employee
2025-05-12 04:52:57,372 - entity_parser - INFO - Successfully parsed entity 'Employee'
2025-05-12 04:52:57,373 - entity_parser - INFO - Parsing entity: Employee
2025-05-12 04:52:57,373 - entity_parser - INFO - Successfully parsed entity 'Employee'
2025-05-12 04:52:57,373 - entity_parser - INFO - Parsing entity: Department
2025-05-12 04:52:57,373 - entity_parser - INFO - Successfully parsed entity 'Department'
2025-05-12 04:52:57,373 - entity_parser - INFO - Successfully parsed 2 entities
2025-05-12 04:52:57,373 - entity_example_detailed - INFO - Parsed entity data:
2025-05-12 04:52:57,373 - entity_example_detailed - INFO - Entity: Employee
2025-05-12 04:52:57,373 - entity_example_detailed - INFO -   Description: 
2025-05-12 04:52:57,373 - entity_example_detailed - INFO -   Attributes:
2025-05-12 04:52:57,373 - entity_example_detailed - INFO -     employeeId: {'type': 'string', 'required': False, 'primary_key': False, 'foreign_key': False}
2025-05-12 04:52:57,373 - entity_example_detailed - INFO -     firstName: {'type': 'string', 'required': False, 'primary_key': False, 'foreign_key': False}
2025-05-12 04:52:57,373 - entity_example_detailed - INFO -     lastName: {'type': 'string', 'required': False, 'primary_key': False, 'foreign_key': False}
2025-05-12 04:52:57,373 - entity_example_detailed - INFO -     fullName[derived]: {'type': 'string', 'required': False, 'primary_key': False, 'foreign_key': False}
2025-05-12 04:52:57,373 - entity_example_detailed - INFO -   Relationships:
2025-05-12 04:52:57,373 - entity_example_detailed - INFO -   Business Rules:
2025-05-12 04:52:57,373 - entity_example_detailed - INFO -   Calculated Fields:
2025-05-12 04:52:57,373 - entity_example_detailed - INFO -     CF001: {'attribute': 'fullName', 'formula': "CONCAT(firstName, ' ', lastName)", 'logic_layer': 'Application', 'caching': 'Session', 'dependencies': 'Employee.firstName, Employee.lastName', 'archive_strategy_for_employee': '', 'history_tracking_for_employee': ''}
2025-05-12 04:52:57,373 - entity_example_detailed - INFO -   Lifecycle Management:
2025-05-12 04:52:57,373 - entity_example_detailed - INFO - Entity: Department
2025-05-12 04:52:57,373 - entity_example_detailed - INFO -   Description: 
2025-05-12 04:52:57,373 - entity_example_detailed - INFO -   Attributes:
2025-05-12 04:52:57,373 - entity_example_detailed - INFO -     departmentId: {'type': 'string', 'required': True, 'primary_key': True, 'foreign_key': False}
2025-05-12 04:52:57,373 - entity_example_detailed - INFO -     name: {'type': 'string', 'required': False, 'primary_key': False, 'foreign_key': False}
2025-05-12 04:52:57,373 - entity_example_detailed - INFO -     managerId: {'type': 'string', 'required': False, 'primary_key': False, 'foreign_key': True}
2025-05-12 04:52:57,373 - entity_example_detailed - INFO -     location: {'type': 'string', 'required': False, 'primary_key': False, 'foreign_key': False}
2025-05-12 04:52:57,373 - entity_example_detailed - INFO -     budget: {'type': 'string', 'required': False, 'primary_key': False, 'foreign_key': False}
2025-05-12 04:52:57,373 - entity_example_detailed - INFO -   Relationships:
2025-05-12 04:52:57,373 - entity_example_detailed - INFO -     one-to-one_Employee: {'type': 'one-to-one', 'entity': 'Employee', 'source_attribute': 'managerId', 'target_attribute': 'employeeId'}
2025-05-12 04:52:57,373 - entity_example_detailed - INFO -     one-to-many_Employee: {'type': 'one-to-many', 'entity': 'Employee', 'source_attribute': 'departmentId', 'target_attribute': 'departmentId'}
2025-05-12 04:52:57,373 - entity_example_detailed - INFO -   Business Rules:
2025-05-12 04:52:57,373 - entity_example_detailed - INFO -     DEPT001: {'conditions': ['Department.budget must be approved by Finance before changes', "Department.managerId must reference an Employee with status = 'Active'", 'Archive Strategy for Department:']}
2025-05-12 04:52:57,373 - entity_example_detailed - INFO -   Calculated Fields:
2025-05-12 04:52:57,373 - entity_example_detailed - INFO -   Lifecycle Management:
2025-05-12 04:52:57,373 - entity_example_detailed - INFO - === Comparing with insert_generator.py ===
2025-05-12 04:52:57,373 - entity_example_detailed - INFO - Converted entity data to insert_generator.py format:
2025-05-12 04:52:57,374 - entity_example_detailed - INFO - {
  "entities": [
    {
      "id": "en_employee",
      "name": "Employee",
      "type": "standard",
      "version": "1.0",
      "status": "active",
      "attributes": [
        {
          "id": "at_employeeid",
          "name": "employeeId",
          "display_name": "employeeId",
          "datatype": "string",
          "required": false,
          "version": "1.0",
          "status": "active"
        },
        {
          "id": "at_firstname",
          "name": "firstName",
          "display_name": "firstName",
          "datatype": "string",
          "required": false,
          "version": "1.0",
          "status": "active"
        },
        {
          "id": "at_lastname",
          "name": "lastName",
          "display_name": "lastName",
          "datatype": "string",
          "required": false,
          "version": "1.0",
          "status": "active"
        },
        {
          "id": "at_fullname[derived]",
          "name": "fullName[derived]",
          "display_name": "fullName[derived]",
          "datatype": "string",
          "required": false,
          "version": "1.0",
          "status": "active"
        }
      ]
    },
    {
      "id": "en_department",
      "name": "Department",
      "type": "standard",
      "version": "1.0",
      "status": "active",
      "attributes": [
        {
          "id": "at_departmentid",
          "name": "departmentId",
          "display_name": "departmentId",
          "datatype": "string",
          "required": true,
          "version": "1.0",
          "status": "active"
        },
        {
          "id": "at_name",
          "name": "name",
          "display_name": "name",
          "datatype": "string",
          "required": false,
          "version": "1.0",
          "status": "active"
        },
        {
          "id": "at_managerid",
          "name": "managerId",
          "display_name": "managerId",
          "datatype": "string",
          "required": false,
          "version": "1.0",
          "status": "active"
        },
        {
          "id": "at_location",
          "name": "location",
          "display_name": "location",
          "datatype": "string",
          "required": false,
          "version": "1.0",
          "status": "active"
        },
        {
          "id": "at_budget",
          "name": "budget",
          "display_name": "budget",
          "datatype": "string",
          "required": false,
          "version": "1.0",
          "status": "active"
        }
      ],
      "relationships": [
        {
          "entity_id": "Employee",
          "type": "one-to-one",
          "through_attribute": "",
          "to_attribute": ""
        },
        {
          "entity_id": "Employee",
          "type": "one-to-many",
          "through_attribute": "",
          "to_attribute": ""
        }
      ]
    }
  ]
}
2025-05-12 04:52:57,374 - entity_example_detailed - INFO - [COMPARISON] Simulating insert_generator.py processing...
2025-05-12 04:52:57,374 - entity_example_detailed - INFO - [COMPARISON] Entity table inserts:
2025-05-12 04:52:57,374 - entity_example_detailed - INFO - [COMPARISON] SQL: 
        INSERT INTO entities (entity_id, name, type, version, status, created_at, updated_at)
        VALUES ('en_employee', 'Employee', 'standard', '1.0', 'active', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);
        
2025-05-12 04:52:57,374 - entity_example_detailed - INFO - [COMPARISON] SQL: 
        INSERT INTO entities (entity_id, name, type, version, status, created_at, updated_at)
        VALUES ('en_department', 'Department', 'standard', '1.0', 'active', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);
        
2025-05-12 04:52:57,374 - entity_example_detailed - INFO - [COMPARISON] Entity attribute inserts:
2025-05-12 04:52:57,374 - entity_example_detailed - INFO - [COMPARISON] SQL: 
            INSERT INTO entity_attributes (attribute_id, entity_id, name, display_name, datatype, version, status, required, created_at, updated_at)
            VALUES ('at_employeeid', 'en_employee', 'employeeId', 'employeeId', 'string', '1.0', 'active', False, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);
            
2025-05-12 04:52:57,374 - entity_example_detailed - INFO - [COMPARISON] SQL: 
            INSERT INTO entity_attributes (attribute_id, entity_id, name, display_name, datatype, version, status, required, created_at, updated_at)
            VALUES ('at_firstname', 'en_employee', 'firstName', 'firstName', 'string', '1.0', 'active', False, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);
            
2025-05-12 04:52:57,374 - entity_example_detailed - INFO - [COMPARISON] SQL: 
            INSERT INTO entity_attributes (attribute_id, entity_id, name, display_name, datatype, version, status, required, created_at, updated_at)
            VALUES ('at_lastname', 'en_employee', 'lastName', 'lastName', 'string', '1.0', 'active', False, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);
            
2025-05-12 04:52:57,374 - entity_example_detailed - INFO - [COMPARISON] SQL: 
            INSERT INTO entity_attributes (attribute_id, entity_id, name, display_name, datatype, version, status, required, created_at, updated_at)
            VALUES ('at_fullname[derived]', 'en_employee', 'fullName[derived]', 'fullName[derived]', 'string', '1.0', 'active', False, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);
            
2025-05-12 04:52:57,374 - entity_example_detailed - INFO - [COMPARISON] SQL: 
            INSERT INTO entity_attributes (attribute_id, entity_id, name, display_name, datatype, version, status, required, created_at, updated_at)
            VALUES ('at_departmentid', 'en_department', 'departmentId', 'departmentId', 'string', '1.0', 'active', True, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);
            
2025-05-12 04:52:57,374 - entity_example_detailed - INFO - [COMPARISON] SQL: 
            INSERT INTO entity_attributes (attribute_id, entity_id, name, display_name, datatype, version, status, required, created_at, updated_at)
            VALUES ('at_name', 'en_department', 'name', 'name', 'string', '1.0', 'active', False, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);
            
2025-05-12 04:52:57,374 - entity_example_detailed - INFO - [COMPARISON] SQL: 
            INSERT INTO entity_attributes (attribute_id, entity_id, name, display_name, datatype, version, status, required, created_at, updated_at)
            VALUES ('at_managerid', 'en_department', 'managerId', 'managerId', 'string', '1.0', 'active', False, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);
            
2025-05-12 04:52:57,374 - entity_example_detailed - INFO - [COMPARISON] SQL: 
            INSERT INTO entity_attributes (attribute_id, entity_id, name, display_name, datatype, version, status, required, created_at, updated_at)
            VALUES ('at_location', 'en_department', 'location', 'location', 'string', '1.0', 'active', False, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);
            
2025-05-12 04:52:57,374 - entity_example_detailed - INFO - [COMPARISON] SQL: 
            INSERT INTO entity_attributes (attribute_id, entity_id, name, display_name, datatype, version, status, required, created_at, updated_at)
            VALUES ('at_budget', 'en_department', 'budget', 'budget', 'string', '1.0', 'active', False, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);
            
2025-05-12 04:52:57,374 - entity_example_detailed - INFO - [COMPARISON] Entity relationship inserts:
2025-05-12 04:52:57,374 - entity_example_detailed - INFO - [COMPARISON] SQL: 
                INSERT INTO entity_relationships (source_entity_id, target_entity_id, relationship_type, source_attribute_id, target_attribute_id, created_at, updated_at)
                VALUES ('en_department', 'Employee', 'one-to-one', '', '', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);
                
2025-05-12 04:52:57,374 - entity_example_detailed - INFO - [COMPARISON] SQL: 
                INSERT INTO entity_relationships (source_entity_id, target_entity_id, relationship_type, source_attribute_id, target_attribute_id, created_at, updated_at)
                VALUES ('en_department', 'Employee', 'one-to-many', '', '', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);
                
2025-05-12 04:52:57,374 - entity_example_detailed - INFO - [COMPARISON] Comparing with v2 deployer:
2025-05-12 04:52:57,374 - entity_example_detailed - INFO - [COMPARISON] 1. v2 deployer uses prescriptive_parser.py to parse prescriptive text into structured data
2025-05-12 04:52:57,374 - entity_example_detailed - INFO - [COMPARISON] 2. v2 deployer uses component_deployer.py to deploy the structured data to the database
2025-05-12 04:52:57,374 - entity_example_detailed - INFO - [COMPARISON] 3. v2 deployer uses entity_deployer.py to deploy entities to the database
2025-05-12 04:52:57,374 - entity_example_detailed - INFO - [COMPARISON] Key differences:
2025-05-12 04:52:57,374 - entity_example_detailed - INFO - [COMPARISON] 1. insert_generator.py uses a JSON file as input, while v2 uses prescriptive text
2025-05-12 04:52:57,374 - entity_example_detailed - INFO - [COMPARISON] 2. insert_generator.py generates SQL statements, while v2 executes them directly
2025-05-12 04:52:57,374 - entity_example_detailed - INFO - [COMPARISON] 3. insert_generator.py handles multiple components at once, while v2 can handle them separately
2025-05-12 04:52:57,374 - entity_example_detailed - INFO - [COMPARISON] 4. v2 has more validation and error handling
2025-05-12 04:52:57,374 - entity_example_detailed - INFO - [COMPARISON] 5. v2 uses a temporary schema for validation before promoting to runtime schema
2025-05-12 04:52:57,374 - entity_example_detailed - INFO - === Deploying Entity ===
2025-05-12 04:52:57,374 - component_deployer - INFO - ComponentDeployer initialized with use_temp_schema=True
2025-05-12 04:52:57,382 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:57,421 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:57,426 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:57,434 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:57,442 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:57,451 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:57,459 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:57,468 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:57,477 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:57,486 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:57,495 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:57,503 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:57,511 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:57,519 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:57,527 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:57,535 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:57,543 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:57,544 - component_deployer - INFO - Analyzing workflow_runtime schema to create necessary tables in temporary schema
2025-05-12 04:52:57,548 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:57,556 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:57,561 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:57,568 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:57,580 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:57,589 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:57,597 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:57,606 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:57,614 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:57,621 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:57,631 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:57,639 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:57,646 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:57,656 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:57,664 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:57,672 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:57,681 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:57,689 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:57,697 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:57,706 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:57,715 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:57,722 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:57,734 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:57,743 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:57,750 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:57,759 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:57,768 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:57,777 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:57,787 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:57,797 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:57,805 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:57,814 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:57,823 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:57,830 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:57,840 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:57,849 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:57,856 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:57,867 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:57,878 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:57,886 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:57,896 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:57,905 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:57,912 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:57,922 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:57,930 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:57,936 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:57,945 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:57,953 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:57,960 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:57,969 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:57,977 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:57,984 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:57,993 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:58,003 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:58,011 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:58,020 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:58,029 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:58,036 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:58,047 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:58,056 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:58,061 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:58,071 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:58,080 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:58,086 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:58,095 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:58,105 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:58,111 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:58,120 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:58,129 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:58,136 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:58,147 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:58,157 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:58,164 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:58,172 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:58,180 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:58,188 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:58,199 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:58,208 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:58,214 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:58,223 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:58,231 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:58,239 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:58,250 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:58,259 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:58,265 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:58,275 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:58,284 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:58,292 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:58,302 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:58,311 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:58,319 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:58,328 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:58,336 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:58,342 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:58,352 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:58,359 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:58,367 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:58,376 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:58,385 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:58,391 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:58,400 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:58,407 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:58,415 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:58,424 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:58,431 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:58,439 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:58,448 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:58,455 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:58,462 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:58,470 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:58,478 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:58,484 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:58,493 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:58,501 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:58,508 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:58,516 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:58,524 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:58,530 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:58,539 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:58,547 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:58,553 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:58,562 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:58,570 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:58,577 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:58,587 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:58,595 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:58,602 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:58,611 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:58,620 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:58,627 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:58,636 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:58,646 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:58,655 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:58,665 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:58,673 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:58,682 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:58,692 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:58,702 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:58,710 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:58,719 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:58,728 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:58,736 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:58,746 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:58,756 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:58,766 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:58,776 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:58,785 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:58,793 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:58,802 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:58,812 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:58,819 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:58,830 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:58,838 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:58,846 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:58,857 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:58,866 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:58,873 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:58,883 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:58,891 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:58,898 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:58,908 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:58,916 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:58,922 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:58,932 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:58,940 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:58,949 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:58,960 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:58,968 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:58,975 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:58,985 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:58,994 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:59,001 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:59,010 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:59,019 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:59,027 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:59,038 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:59,046 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:59,055 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:59,067 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:59,075 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:59,083 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:59,093 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:59,101 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:59,109 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:59,120 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:59,128 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:59,136 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:59,146 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:59,155 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:59,163 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:59,174 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:59,184 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:59,192 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:59,269 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:59,277 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:59,286 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:59,294 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:59,301 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:59,308 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:59,316 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:59,323 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:59,331 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:59,337 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:59,344 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:59,351 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:59,358 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:59,365 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:59,371 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:59,378 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:59,379 - db_utils - ERROR - Database error: constraint "entity_attributes_entity_id_fkey" for relation "entity_attributes" already exists
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.DuplicateObject: constraint "entity_attributes_entity_id_fkey" for relation "entity_attributes" already exists

2025-05-12 04:52:59,385 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:59,391 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:59,397 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:59,398 - db_utils - ERROR - Database error: constraint "lo_input_items_input_stack_id_fkey" for relation "lo_input_items" already exists
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.DuplicateObject: constraint "lo_input_items_input_stack_id_fkey" for relation "lo_input_items" already exists

2025-05-12 04:52:59,403 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:59,404 - db_utils - ERROR - Database error: constraint "lo_input_items_lo_id_fkey" for relation "lo_input_items" already exists
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.DuplicateObject: constraint "lo_input_items_lo_id_fkey" for relation "lo_input_items" already exists

2025-05-12 04:52:59,408 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:59,414 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:59,415 - db_utils - ERROR - Database error: there is no unique constraint matching given keys for referenced table "roles"
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.InvalidForeignKey: there is no unique constraint matching given keys for referenced table "roles"

2025-05-12 04:52:59,419 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:59,425 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:59,425 - db_utils - ERROR - Database error: constraint "lo_nested_functions_lo_id_fkey" for relation "lo_nested_functions" already exists
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.DuplicateObject: constraint "lo_nested_functions_lo_id_fkey" for relation "lo_nested_functions" already exists

2025-05-12 04:52:59,430 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:59,431 - db_utils - ERROR - Database error: constraint "lo_output_items_lo_id_fkey" for relation "lo_output_items" already exists
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.DuplicateObject: constraint "lo_output_items_lo_id_fkey" for relation "lo_output_items" already exists

2025-05-12 04:52:59,435 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:59,435 - db_utils - ERROR - Database error: constraint "lo_output_items_output_stack_id_fkey" for relation "lo_output_items" already exists
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.DuplicateObject: constraint "lo_output_items_output_stack_id_fkey" for relation "lo_output_items" already exists

2025-05-12 04:52:59,439 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:59,446 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:59,452 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:59,452 - db_utils - ERROR - Database error: constraint "input_dependencies_depends_on_id_depends_on_stack_id_fkey" for relation "input_dependencies" already exists
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.DuplicateObject: constraint "input_dependencies_depends_on_id_depends_on_stack_id_fkey" for relation "input_dependencies" already exists

2025-05-12 04:52:59,456 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:59,457 - db_utils - ERROR - Database error: constraint "input_dependencies_depends_on_id_depends_on_stack_id_fkey" for relation "input_dependencies" already exists
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.DuplicateObject: constraint "input_dependencies_depends_on_id_depends_on_stack_id_fkey" for relation "input_dependencies" already exists

2025-05-12 04:52:59,462 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:59,462 - db_utils - ERROR - Database error: constraint "input_dependencies_depends_on_id_depends_on_stack_id_fkey" for relation "input_dependencies" already exists
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.DuplicateObject: constraint "input_dependencies_depends_on_id_depends_on_stack_id_fkey" for relation "input_dependencies" already exists

2025-05-12 04:52:59,467 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:59,472 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:59,472 - db_utils - ERROR - Database error: constraint "input_dependencies_input_item_id_input_stack_id_fkey" for relation "input_dependencies" already exists
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.DuplicateObject: constraint "input_dependencies_input_item_id_input_stack_id_fkey" for relation "input_dependencies" already exists

2025-05-12 04:52:59,477 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:59,478 - db_utils - ERROR - Database error: constraint "input_dependencies_input_item_id_input_stack_id_fkey" for relation "input_dependencies" already exists
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.DuplicateObject: constraint "input_dependencies_input_item_id_input_stack_id_fkey" for relation "input_dependencies" already exists

2025-05-12 04:52:59,482 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:59,483 - db_utils - ERROR - Database error: constraint "input_dependencies_input_item_id_input_stack_id_fkey" for relation "input_dependencies" already exists
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.DuplicateObject: constraint "input_dependencies_input_item_id_input_stack_id_fkey" for relation "input_dependencies" already exists

2025-05-12 04:52:59,486 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:59,493 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:59,499 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:59,505 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:59,511 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:59,512 - db_utils - ERROR - Database error: constraint "lo_data_mapping_stack_lo_id_fkey" for relation "lo_data_mapping_stack" already exists
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.DuplicateObject: constraint "lo_data_mapping_stack_lo_id_fkey" for relation "lo_data_mapping_stack" already exists

2025-05-12 04:52:59,517 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:59,522 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:59,529 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:59,534 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:59,539 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:59,540 - db_utils - ERROR - Database error: column "inherits_from" referenced in foreign key constraint does not exist
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.UndefinedColumn: column "inherits_from" referenced in foreign key constraint does not exist

2025-05-12 04:52:59,543 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:59,544 - db_utils - ERROR - Database error: column "tenant_id" referenced in foreign key constraint does not exist
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.UndefinedColumn: column "tenant_id" referenced in foreign key constraint does not exist

2025-05-12 04:52:59,548 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:59,554 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:59,559 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:59,565 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:59,570 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:59,571 - db_utils - ERROR - Database error: there is no unique constraint matching given keys for referenced table "output_items"
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.InvalidForeignKey: there is no unique constraint matching given keys for referenced table "output_items"

2025-05-12 04:52:59,575 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:59,576 - db_utils - ERROR - Database error: there is no unique constraint matching given keys for referenced table "output_items"
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.InvalidForeignKey: there is no unique constraint matching given keys for referenced table "output_items"

2025-05-12 04:52:59,580 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:59,580 - db_utils - ERROR - Database error: there is no unique constraint matching given keys for referenced table "output_items"
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.InvalidForeignKey: there is no unique constraint matching given keys for referenced table "output_items"

2025-05-12 04:52:59,585 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:59,585 - db_utils - ERROR - Database error: there is no unique constraint matching given keys for referenced table "output_items"
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.InvalidForeignKey: there is no unique constraint matching given keys for referenced table "output_items"

2025-05-12 04:52:59,589 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:59,596 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:59,602 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:59,608 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:59,614 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:59,619 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:59,624 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:59,625 - db_utils - ERROR - Database error: constraint "lo_input_validations_input_item_id_input_stack_id_fkey" for relation "lo_input_validations" already exists
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.DuplicateObject: constraint "lo_input_validations_input_item_id_input_stack_id_fkey" for relation "lo_input_validations" already exists

2025-05-12 04:52:59,629 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:59,630 - db_utils - ERROR - Database error: constraint "lo_input_validations_input_item_id_input_stack_id_fkey" for relation "lo_input_validations" already exists
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.DuplicateObject: constraint "lo_input_validations_input_item_id_input_stack_id_fkey" for relation "lo_input_validations" already exists

2025-05-12 04:52:59,634 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:59,634 - db_utils - ERROR - Database error: constraint "lo_input_validations_input_item_id_input_stack_id_fkey" for relation "lo_input_validations" already exists
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.DuplicateObject: constraint "lo_input_validations_input_item_id_input_stack_id_fkey" for relation "lo_input_validations" already exists

2025-05-12 04:52:59,638 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:59,644 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:59,645 - db_utils - ERROR - Database error: constraint "local_objectives_go_id_fkey" for relation "local_objectives" already exists
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.DuplicateObject: constraint "local_objectives_go_id_fkey" for relation "local_objectives" already exists

2025-05-12 04:52:59,651 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:59,657 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:59,663 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:59,668 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:59,674 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:59,680 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:59,681 - db_utils - ERROR - Database error: constraint "lo_input_stack_lo_id_fkey" for relation "lo_input_stack" already exists
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.DuplicateObject: constraint "lo_input_stack_lo_id_fkey" for relation "lo_input_stack" already exists

2025-05-12 04:52:59,685 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:59,692 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:59,698 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:59,699 - db_utils - ERROR - Database error: constraint "lo_output_stack_lo_id_fkey" for relation "lo_output_stack" already exists
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.DuplicateObject: constraint "lo_output_stack_lo_id_fkey" for relation "lo_output_stack" already exists

2025-05-12 04:52:59,702 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:59,708 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:59,715 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:59,721 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:59,722 - db_utils - ERROR - Database error: constraint "lo_data_mappings_mapping_stack_id_fkey" for relation "lo_data_mappings" already exists
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.DuplicateObject: constraint "lo_data_mappings_mapping_stack_id_fkey" for relation "lo_data_mappings" already exists

2025-05-12 04:52:59,726 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:59,733 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:59,734 - db_utils - ERROR - Database error: constraint "lo_output_triggers_output_item_id_output_stack_id_fkey" for relation "lo_output_triggers" already exists
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.DuplicateObject: constraint "lo_output_triggers_output_item_id_output_stack_id_fkey" for relation "lo_output_triggers" already exists

2025-05-12 04:52:59,738 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:59,738 - db_utils - ERROR - Database error: constraint "lo_output_triggers_output_item_id_output_stack_id_fkey" for relation "lo_output_triggers" already exists
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.DuplicateObject: constraint "lo_output_triggers_output_item_id_output_stack_id_fkey" for relation "lo_output_triggers" already exists

2025-05-12 04:52:59,742 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:59,742 - db_utils - ERROR - Database error: constraint "lo_output_triggers_output_item_id_output_stack_id_fkey" for relation "lo_output_triggers" already exists
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.DuplicateObject: constraint "lo_output_triggers_output_item_id_output_stack_id_fkey" for relation "lo_output_triggers" already exists

2025-05-12 04:52:59,747 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:59,754 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:59,755 - db_utils - ERROR - Database error: constraint "input_data_sources_input_item_id_input_stack_id_fkey" for relation "input_data_sources" already exists
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.DuplicateObject: constraint "input_data_sources_input_item_id_input_stack_id_fkey" for relation "input_data_sources" already exists

2025-05-12 04:52:59,759 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:59,759 - db_utils - ERROR - Database error: constraint "input_data_sources_input_item_id_input_stack_id_fkey" for relation "input_data_sources" already exists
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.DuplicateObject: constraint "input_data_sources_input_item_id_input_stack_id_fkey" for relation "input_data_sources" already exists

2025-05-12 04:52:59,764 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:59,765 - db_utils - ERROR - Database error: constraint "input_data_sources_input_item_id_input_stack_id_fkey" for relation "input_data_sources" already exists
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.DuplicateObject: constraint "input_data_sources_input_item_id_input_stack_id_fkey" for relation "input_data_sources" already exists

2025-05-12 04:52:59,770 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:59,776 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:59,783 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:59,790 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:59,796 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:59,802 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:59,803 - db_utils - ERROR - Database error: constraint "dropdown_data_sources_input_item_id_input_stack_id_fkey" for relation "dropdown_data_sources" already exists
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.DuplicateObject: constraint "dropdown_data_sources_input_item_id_input_stack_id_fkey" for relation "dropdown_data_sources" already exists

2025-05-12 04:52:59,808 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:59,809 - db_utils - ERROR - Database error: constraint "dropdown_data_sources_input_item_id_input_stack_id_fkey" for relation "dropdown_data_sources" already exists
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.DuplicateObject: constraint "dropdown_data_sources_input_item_id_input_stack_id_fkey" for relation "dropdown_data_sources" already exists

2025-05-12 04:52:59,813 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:59,814 - db_utils - ERROR - Database error: constraint "dropdown_data_sources_input_item_id_input_stack_id_fkey" for relation "dropdown_data_sources" already exists
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.DuplicateObject: constraint "dropdown_data_sources_input_item_id_input_stack_id_fkey" for relation "dropdown_data_sources" already exists

2025-05-12 04:52:59,819 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:52:59,821 - component_deployer - INFO - Successfully analyzed workflow_runtime schema and created necessary tables
2025-05-12 04:52:59,821 - entity_example_detailed - INFO - Deploying entity to schema workflow_temp
2025-05-12 04:52:59,821 - component_deployer - INFO - Deploying component of type: entities
2025-05-12 04:52:59,821 - component_deployer - ERROR - Deployment error: 'dict' object has no attribute 'read'
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/component_deployer.py", line 109, in deploy_component
    component_data = yaml.safe_load(component_yaml)
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/dist-packages/yaml/__init__.py", line 125, in safe_load
    return load(stream, SafeLoader)
           ^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/dist-packages/yaml/__init__.py", line 79, in load
    loader = Loader(stream)
             ^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/dist-packages/yaml/loader.py", line 34, in __init__
    Reader.__init__(self, stream)
  File "/usr/local/lib/python3.11/dist-packages/yaml/reader.py", line 85, in __init__
    self.determine_encoding()
  File "/usr/local/lib/python3.11/dist-packages/yaml/reader.py", line 124, in determine_encoding
    self.update_raw()
  File "/usr/local/lib/python3.11/dist-packages/yaml/reader.py", line 178, in update_raw
    data = self.stream.read(size)
           ^^^^^^^^^^^^^^^^
AttributeError: 'dict' object has no attribute 'read'
2025-05-12 04:52:59,822 - entity_example_detailed - INFO - Deploy messages:
2025-05-12 04:52:59,823 - entity_example_detailed - INFO -   Deployment error: 'dict' object has no attribute 'read'
2025-05-12 04:52:59,823 - entity_example_detailed - INFO - === Summary ===
2025-05-12 04:52:59,823 - entity_example_detailed - INFO - Deployment successful: False
2025-05-12 04:56:40,854 - entity_example_detailed - INFO - === Parsing Entity ===
2025-05-12 04:56:40,854 - prescriptive_parser - INFO - Initializing PrescriptiveParser
2025-05-12 04:56:40,854 - entity_example_detailed - INFO - Parsing prescriptive text:
2025-05-12 04:56:40,854 - entity_example_detailed - INFO - Employee has employeeId^PK, firstName, lastName, email, phoneNumber, departmentId^FK, managerId^FK, hireDate, status (Active, Inactive, OnLeave), salary, performanceRating.

* Employee has many-to-one relationship with Department using Employee.departmentId to Department.departmentId^PK
* Employee has many-to-one relationship with Employee using Employee.managerId to Employee.employeeId^PK

* Employee.email must be unique
* Employee.email must match pattern "^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$"
* Employee.hireDate must be before current date
* Employee.salary must be greater than 0

BusinessRule EMP001 for Employee:
* Employee.status must be Active to receive performance reviews
* Employee.hireDate must be at least 90 days before Employee.performanceRating can be set

Employee has employeeId, firstName, lastName, fullName[derived].

CalculatedField CF001 for Employee.fullName:
* Formula: CONCAT(firstName, ' ', lastName)
* Logic Layer: Application
* Caching: Session
* Dependencies: Employee.firstName, Employee.lastName

* Archive Strategy for Employee:
  - Trigger: Event-based
  - Criteria: When Employee.status changes to 'Inactive' and remains so for 1 year
  - Retention: 7 years
  - Storage: Cold storage
  - Access Pattern: Read-only through HR archive portal
  - Restoration: Manual process requiring HR Director approval

* History Tracking for Employee:
  - Tracked Attributes: Employee.salary, Employee.departmentId, Employee.managerId, Employee.status
  - Tracking Method: Audit table
  - Granularity: Change level
  - Retention: 7 years
  - Access Control: HR Managers and Compliance Officers only

Department has departmentId^PK, name, managerId^FK, location, budget.

* Department has one-to-one relationship with Employee using Department.managerId to Employee.employeeId^PK
* Department has one-to-many relationship with Employee using Department.departmentId to Employee.departmentId^FK

* Department.name must be unique
* Department.budget must be greater than 0

BusinessRule DEPT001 for Department:
* Department.budget must be approved by Finance before changes
* Department.managerId must reference an Employee with status = 'Active'

* Archive Strategy for Department:
  - Trigger: Event-based
  - Criteria: When Department is marked as 'Dissolved'
  - Retention: 10 years
  - Storage: Cold storage
  - Access Pattern: Read-only through Admin portal
  - Restoration: Manual process requiring CEO approval

2025-05-12 04:56:40,855 - prescriptive_parser - INFO - Parsing entities prescriptive text
2025-05-12 04:56:40,855 - entity_parser - INFO - Starting to parse entity definitions
2025-05-12 04:56:40,855 - entity_parser - INFO - Parsing entity: Employee
2025-05-12 04:56:40,855 - entity_parser - INFO - Successfully parsed entity 'Employee'
2025-05-12 04:56:40,855 - entity_parser - INFO - Parsing entity: Employee
2025-05-12 04:56:40,855 - entity_parser - INFO - Successfully parsed entity 'Employee'
2025-05-12 04:56:40,856 - entity_parser - INFO - Parsing entity: Department
2025-05-12 04:56:40,856 - entity_parser - INFO - Successfully parsed entity 'Department'
2025-05-12 04:56:40,856 - entity_parser - INFO - Successfully parsed 2 entities
2025-05-12 04:56:40,856 - entity_example_detailed - INFO - Parsed entity data:
2025-05-12 04:56:40,856 - entity_example_detailed - INFO - Entity: Employee
2025-05-12 04:56:40,856 - entity_example_detailed - INFO -   Description: 
2025-05-12 04:56:40,856 - entity_example_detailed - INFO -   Attributes:
2025-05-12 04:56:40,856 - entity_example_detailed - INFO -     employeeId: {'type': 'string', 'required': False, 'primary_key': False, 'foreign_key': False}
2025-05-12 04:56:40,856 - entity_example_detailed - INFO -     firstName: {'type': 'string', 'required': False, 'primary_key': False, 'foreign_key': False}
2025-05-12 04:56:40,856 - entity_example_detailed - INFO -     lastName: {'type': 'string', 'required': False, 'primary_key': False, 'foreign_key': False}
2025-05-12 04:56:40,856 - entity_example_detailed - INFO -     fullName[derived]: {'type': 'string', 'required': False, 'primary_key': False, 'foreign_key': False}
2025-05-12 04:56:40,856 - entity_example_detailed - INFO -   Relationships:
2025-05-12 04:56:40,856 - entity_example_detailed - INFO -   Business Rules:
2025-05-12 04:56:40,856 - entity_example_detailed - INFO -   Calculated Fields:
2025-05-12 04:56:40,856 - entity_example_detailed - INFO -     CF001: {'attribute': 'fullName', 'formula': "CONCAT(firstName, ' ', lastName)", 'logic_layer': 'Application', 'caching': 'Session', 'dependencies': 'Employee.firstName, Employee.lastName', 'archive_strategy_for_employee': '', 'history_tracking_for_employee': ''}
2025-05-12 04:56:40,856 - entity_example_detailed - INFO -   Lifecycle Management:
2025-05-12 04:56:40,856 - entity_example_detailed - INFO - Entity: Department
2025-05-12 04:56:40,856 - entity_example_detailed - INFO -   Description: 
2025-05-12 04:56:40,856 - entity_example_detailed - INFO -   Attributes:
2025-05-12 04:56:40,856 - entity_example_detailed - INFO -     departmentId: {'type': 'string', 'required': True, 'primary_key': True, 'foreign_key': False}
2025-05-12 04:56:40,856 - entity_example_detailed - INFO -     name: {'type': 'string', 'required': False, 'primary_key': False, 'foreign_key': False}
2025-05-12 04:56:40,856 - entity_example_detailed - INFO -     managerId: {'type': 'string', 'required': False, 'primary_key': False, 'foreign_key': True}
2025-05-12 04:56:40,856 - entity_example_detailed - INFO -     location: {'type': 'string', 'required': False, 'primary_key': False, 'foreign_key': False}
2025-05-12 04:56:40,856 - entity_example_detailed - INFO -     budget: {'type': 'string', 'required': False, 'primary_key': False, 'foreign_key': False}
2025-05-12 04:56:40,856 - entity_example_detailed - INFO -   Relationships:
2025-05-12 04:56:40,856 - entity_example_detailed - INFO -     one-to-one_Employee: {'type': 'one-to-one', 'entity': 'Employee', 'source_attribute': 'managerId', 'target_attribute': 'employeeId'}
2025-05-12 04:56:40,856 - entity_example_detailed - INFO -     one-to-many_Employee: {'type': 'one-to-many', 'entity': 'Employee', 'source_attribute': 'departmentId', 'target_attribute': 'departmentId'}
2025-05-12 04:56:40,856 - entity_example_detailed - INFO -   Business Rules:
2025-05-12 04:56:40,856 - entity_example_detailed - INFO -     DEPT001: {'conditions': ['Department.budget must be approved by Finance before changes', "Department.managerId must reference an Employee with status = 'Active'", 'Archive Strategy for Department:']}
2025-05-12 04:56:40,856 - entity_example_detailed - INFO -   Calculated Fields:
2025-05-12 04:56:40,856 - entity_example_detailed - INFO -   Lifecycle Management:
2025-05-12 04:56:40,856 - entity_example_detailed - INFO - === Comparing with insert_generator.py ===
2025-05-12 04:56:40,856 - entity_example_detailed - INFO - Converted entity data to insert_generator.py format:
2025-05-12 04:56:40,856 - entity_example_detailed - INFO - {
  "entities": [
    {
      "id": "en_employee",
      "name": "Employee",
      "type": "standard",
      "version": "1.0",
      "status": "active",
      "attributes": [
        {
          "id": "at_employeeid",
          "name": "employeeId",
          "display_name": "employeeId",
          "datatype": "string",
          "required": false,
          "version": "1.0",
          "status": "active"
        },
        {
          "id": "at_firstname",
          "name": "firstName",
          "display_name": "firstName",
          "datatype": "string",
          "required": false,
          "version": "1.0",
          "status": "active"
        },
        {
          "id": "at_lastname",
          "name": "lastName",
          "display_name": "lastName",
          "datatype": "string",
          "required": false,
          "version": "1.0",
          "status": "active"
        },
        {
          "id": "at_fullname[derived]",
          "name": "fullName[derived]",
          "display_name": "fullName[derived]",
          "datatype": "string",
          "required": false,
          "version": "1.0",
          "status": "active"
        }
      ]
    },
    {
      "id": "en_department",
      "name": "Department",
      "type": "standard",
      "version": "1.0",
      "status": "active",
      "attributes": [
        {
          "id": "at_departmentid",
          "name": "departmentId",
          "display_name": "departmentId",
          "datatype": "string",
          "required": true,
          "version": "1.0",
          "status": "active"
        },
        {
          "id": "at_name",
          "name": "name",
          "display_name": "name",
          "datatype": "string",
          "required": false,
          "version": "1.0",
          "status": "active"
        },
        {
          "id": "at_managerid",
          "name": "managerId",
          "display_name": "managerId",
          "datatype": "string",
          "required": false,
          "version": "1.0",
          "status": "active"
        },
        {
          "id": "at_location",
          "name": "location",
          "display_name": "location",
          "datatype": "string",
          "required": false,
          "version": "1.0",
          "status": "active"
        },
        {
          "id": "at_budget",
          "name": "budget",
          "display_name": "budget",
          "datatype": "string",
          "required": false,
          "version": "1.0",
          "status": "active"
        }
      ],
      "relationships": [
        {
          "entity_id": "Employee",
          "type": "one-to-one",
          "through_attribute": "",
          "to_attribute": ""
        },
        {
          "entity_id": "Employee",
          "type": "one-to-many",
          "through_attribute": "",
          "to_attribute": ""
        }
      ]
    }
  ]
}
2025-05-12 04:56:40,857 - entity_example_detailed - INFO - [COMPARISON] Simulating insert_generator.py processing...
2025-05-12 04:56:40,857 - entity_example_detailed - INFO - [COMPARISON] Entity table inserts:
2025-05-12 04:56:40,857 - entity_example_detailed - INFO - [COMPARISON] SQL: 
        INSERT INTO entities (entity_id, name, type, version, status, created_at, updated_at)
        VALUES ('en_employee', 'Employee', 'standard', '1.0', 'active', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);
        
2025-05-12 04:56:40,857 - entity_example_detailed - INFO - [COMPARISON] SQL: 
        INSERT INTO entities (entity_id, name, type, version, status, created_at, updated_at)
        VALUES ('en_department', 'Department', 'standard', '1.0', 'active', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);
        
2025-05-12 04:56:40,857 - entity_example_detailed - INFO - [COMPARISON] Entity attribute inserts:
2025-05-12 04:56:40,857 - entity_example_detailed - INFO - [COMPARISON] SQL: 
            INSERT INTO entity_attributes (attribute_id, entity_id, name, display_name, datatype, version, status, required, created_at, updated_at)
            VALUES ('at_employeeid', 'en_employee', 'employeeId', 'employeeId', 'string', '1.0', 'active', False, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);
            
2025-05-12 04:56:40,857 - entity_example_detailed - INFO - [COMPARISON] SQL: 
            INSERT INTO entity_attributes (attribute_id, entity_id, name, display_name, datatype, version, status, required, created_at, updated_at)
            VALUES ('at_firstname', 'en_employee', 'firstName', 'firstName', 'string', '1.0', 'active', False, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);
            
2025-05-12 04:56:40,857 - entity_example_detailed - INFO - [COMPARISON] SQL: 
            INSERT INTO entity_attributes (attribute_id, entity_id, name, display_name, datatype, version, status, required, created_at, updated_at)
            VALUES ('at_lastname', 'en_employee', 'lastName', 'lastName', 'string', '1.0', 'active', False, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);
            
2025-05-12 04:56:40,857 - entity_example_detailed - INFO - [COMPARISON] SQL: 
            INSERT INTO entity_attributes (attribute_id, entity_id, name, display_name, datatype, version, status, required, created_at, updated_at)
            VALUES ('at_fullname[derived]', 'en_employee', 'fullName[derived]', 'fullName[derived]', 'string', '1.0', 'active', False, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);
            
2025-05-12 04:56:40,857 - entity_example_detailed - INFO - [COMPARISON] SQL: 
            INSERT INTO entity_attributes (attribute_id, entity_id, name, display_name, datatype, version, status, required, created_at, updated_at)
            VALUES ('at_departmentid', 'en_department', 'departmentId', 'departmentId', 'string', '1.0', 'active', True, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);
            
2025-05-12 04:56:40,857 - entity_example_detailed - INFO - [COMPARISON] SQL: 
            INSERT INTO entity_attributes (attribute_id, entity_id, name, display_name, datatype, version, status, required, created_at, updated_at)
            VALUES ('at_name', 'en_department', 'name', 'name', 'string', '1.0', 'active', False, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);
            
2025-05-12 04:56:40,857 - entity_example_detailed - INFO - [COMPARISON] SQL: 
            INSERT INTO entity_attributes (attribute_id, entity_id, name, display_name, datatype, version, status, required, created_at, updated_at)
            VALUES ('at_managerid', 'en_department', 'managerId', 'managerId', 'string', '1.0', 'active', False, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);
            
2025-05-12 04:56:40,857 - entity_example_detailed - INFO - [COMPARISON] SQL: 
            INSERT INTO entity_attributes (attribute_id, entity_id, name, display_name, datatype, version, status, required, created_at, updated_at)
            VALUES ('at_location', 'en_department', 'location', 'location', 'string', '1.0', 'active', False, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);
            
2025-05-12 04:56:40,857 - entity_example_detailed - INFO - [COMPARISON] SQL: 
            INSERT INTO entity_attributes (attribute_id, entity_id, name, display_name, datatype, version, status, required, created_at, updated_at)
            VALUES ('at_budget', 'en_department', 'budget', 'budget', 'string', '1.0', 'active', False, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);
            
2025-05-12 04:56:40,857 - entity_example_detailed - INFO - [COMPARISON] Entity relationship inserts:
2025-05-12 04:56:40,857 - entity_example_detailed - INFO - [COMPARISON] SQL: 
                INSERT INTO entity_relationships (source_entity_id, target_entity_id, relationship_type, source_attribute_id, target_attribute_id, created_at, updated_at)
                VALUES ('en_department', 'Employee', 'one-to-one', '', '', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);
                
2025-05-12 04:56:40,857 - entity_example_detailed - INFO - [COMPARISON] SQL: 
                INSERT INTO entity_relationships (source_entity_id, target_entity_id, relationship_type, source_attribute_id, target_attribute_id, created_at, updated_at)
                VALUES ('en_department', 'Employee', 'one-to-many', '', '', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);
                
2025-05-12 04:56:40,857 - entity_example_detailed - INFO - [COMPARISON] Comparing with v2 deployer:
2025-05-12 04:56:40,857 - entity_example_detailed - INFO - [COMPARISON] 1. v2 deployer uses prescriptive_parser.py to parse prescriptive text into structured data
2025-05-12 04:56:40,857 - entity_example_detailed - INFO - [COMPARISON] 2. v2 deployer uses component_deployer.py to deploy the structured data to the database
2025-05-12 04:56:40,857 - entity_example_detailed - INFO - [COMPARISON] 3. v2 deployer uses entity_deployer.py to deploy entities to the database
2025-05-12 04:56:40,857 - entity_example_detailed - INFO - [COMPARISON] Key differences:
2025-05-12 04:56:40,857 - entity_example_detailed - INFO - [COMPARISON] 1. insert_generator.py uses a JSON file as input, while v2 uses prescriptive text
2025-05-12 04:56:40,857 - entity_example_detailed - INFO - [COMPARISON] 2. insert_generator.py generates SQL statements, while v2 executes them directly
2025-05-12 04:56:40,857 - entity_example_detailed - INFO - [COMPARISON] 3. insert_generator.py handles multiple components at once, while v2 can handle them separately
2025-05-12 04:56:40,857 - entity_example_detailed - INFO - [COMPARISON] 4. v2 has more validation and error handling
2025-05-12 04:56:40,857 - entity_example_detailed - INFO - [COMPARISON] 5. v2 uses a temporary schema for validation before promoting to runtime schema
2025-05-12 04:56:40,857 - entity_example_detailed - INFO - === Deploying Entity ===
2025-05-12 04:56:40,857 - component_deployer - INFO - ComponentDeployer initialized with use_temp_schema=True
2025-05-12 04:56:40,863 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:40,903 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:40,908 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:40,918 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:40,926 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:40,935 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:40,943 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:40,953 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:40,961 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:40,970 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:40,977 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:40,985 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:40,994 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:41,001 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:41,009 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:41,017 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:41,026 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:41,027 - component_deployer - INFO - Analyzing workflow_runtime schema to create necessary tables in temporary schema
2025-05-12 04:56:41,033 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:41,041 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:41,046 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:41,054 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:41,064 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:41,074 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:41,084 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:41,095 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:41,104 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:41,111 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:41,120 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:41,128 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:41,135 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:41,146 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:41,155 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:41,163 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:41,173 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:41,183 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:41,191 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:41,201 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:41,211 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:41,221 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:41,230 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:41,239 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:41,246 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:41,257 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:41,265 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:41,273 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:41,282 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:41,291 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:41,298 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:41,309 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:41,318 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:41,326 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:41,337 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:41,346 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:41,352 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:41,361 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:41,369 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:41,378 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:41,388 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:41,396 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:41,404 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:41,415 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:41,425 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:41,434 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:41,445 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:41,454 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:41,479 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:41,489 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:41,499 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:41,509 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:41,519 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:41,529 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:41,537 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:41,546 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:41,556 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:41,563 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:41,572 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:41,580 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:41,587 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:41,595 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:41,603 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:41,609 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:41,617 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:41,627 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:41,636 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:41,646 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:41,655 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:41,662 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:41,671 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:41,681 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:41,690 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:41,699 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:41,708 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:41,715 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:41,726 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:41,735 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:41,741 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:41,752 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:41,763 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:41,772 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:41,782 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:41,792 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:41,800 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:41,811 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:41,820 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:41,829 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:41,839 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:41,849 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:41,855 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:41,864 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:41,874 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:41,880 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:41,889 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:41,899 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:41,905 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:41,913 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:41,922 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:41,929 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:41,940 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:41,948 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:41,954 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:41,963 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:41,971 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:41,977 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:41,985 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:41,993 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:41,999 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:42,007 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:42,015 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:42,021 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:42,029 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:42,037 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:42,043 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:42,053 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:42,062 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:42,071 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:42,079 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:42,089 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:42,095 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:42,105 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:42,113 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:42,121 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:42,131 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:42,138 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:42,144 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:42,152 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:42,160 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:42,166 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:42,175 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:42,183 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:42,189 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:42,197 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:42,206 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:42,214 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:42,225 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:42,234 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:42,242 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:42,251 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:42,260 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:42,266 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:42,275 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:42,284 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:42,289 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:42,298 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:42,307 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:42,313 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:42,322 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:42,330 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:42,337 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:42,345 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:42,354 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:42,360 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:42,370 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:42,379 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:42,386 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:42,395 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:42,404 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:42,410 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:42,420 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:42,427 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:42,435 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:42,443 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:42,452 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:42,458 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:42,468 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:42,476 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:42,484 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:42,493 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:42,502 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:42,508 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:42,518 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:42,526 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:42,534 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:42,542 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:42,552 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:42,559 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:42,567 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:42,575 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:42,581 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:42,590 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:42,597 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:42,603 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:42,612 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:42,620 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:42,626 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:42,635 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:42,642 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:42,648 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:42,657 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:42,664 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:42,671 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:42,744 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:42,750 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:42,756 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:42,761 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:42,767 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:42,772 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:42,777 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:42,782 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:42,787 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:42,791 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:42,796 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:42,801 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:42,806 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:42,811 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:42,816 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:42,821 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:42,821 - db_utils - ERROR - Database error: constraint "entity_attributes_entity_id_fkey" for relation "entity_attributes" already exists
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.DuplicateObject: constraint "entity_attributes_entity_id_fkey" for relation "entity_attributes" already exists

2025-05-12 04:56:42,825 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:42,829 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:42,834 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:42,835 - db_utils - ERROR - Database error: constraint "lo_input_items_input_stack_id_fkey" for relation "lo_input_items" already exists
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.DuplicateObject: constraint "lo_input_items_input_stack_id_fkey" for relation "lo_input_items" already exists

2025-05-12 04:56:42,839 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:42,839 - db_utils - ERROR - Database error: constraint "lo_input_items_lo_id_fkey" for relation "lo_input_items" already exists
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.DuplicateObject: constraint "lo_input_items_lo_id_fkey" for relation "lo_input_items" already exists

2025-05-12 04:56:42,842 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:42,847 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:42,848 - db_utils - ERROR - Database error: there is no unique constraint matching given keys for referenced table "roles"
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.InvalidForeignKey: there is no unique constraint matching given keys for referenced table "roles"

2025-05-12 04:56:42,851 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:42,856 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:42,856 - db_utils - ERROR - Database error: constraint "lo_nested_functions_lo_id_fkey" for relation "lo_nested_functions" already exists
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.DuplicateObject: constraint "lo_nested_functions_lo_id_fkey" for relation "lo_nested_functions" already exists

2025-05-12 04:56:42,860 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:42,860 - db_utils - ERROR - Database error: constraint "lo_output_items_lo_id_fkey" for relation "lo_output_items" already exists
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.DuplicateObject: constraint "lo_output_items_lo_id_fkey" for relation "lo_output_items" already exists

2025-05-12 04:56:42,863 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:42,864 - db_utils - ERROR - Database error: constraint "lo_output_items_output_stack_id_fkey" for relation "lo_output_items" already exists
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.DuplicateObject: constraint "lo_output_items_output_stack_id_fkey" for relation "lo_output_items" already exists

2025-05-12 04:56:42,867 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:42,874 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:42,880 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:42,880 - db_utils - ERROR - Database error: constraint "input_dependencies_depends_on_id_depends_on_stack_id_fkey" for relation "input_dependencies" already exists
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.DuplicateObject: constraint "input_dependencies_depends_on_id_depends_on_stack_id_fkey" for relation "input_dependencies" already exists

2025-05-12 04:56:42,884 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:42,884 - db_utils - ERROR - Database error: constraint "input_dependencies_depends_on_id_depends_on_stack_id_fkey" for relation "input_dependencies" already exists
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.DuplicateObject: constraint "input_dependencies_depends_on_id_depends_on_stack_id_fkey" for relation "input_dependencies" already exists

2025-05-12 04:56:42,888 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:42,889 - db_utils - ERROR - Database error: constraint "input_dependencies_depends_on_id_depends_on_stack_id_fkey" for relation "input_dependencies" already exists
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.DuplicateObject: constraint "input_dependencies_depends_on_id_depends_on_stack_id_fkey" for relation "input_dependencies" already exists

2025-05-12 04:56:42,892 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:42,897 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:42,898 - db_utils - ERROR - Database error: constraint "input_dependencies_input_item_id_input_stack_id_fkey" for relation "input_dependencies" already exists
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.DuplicateObject: constraint "input_dependencies_input_item_id_input_stack_id_fkey" for relation "input_dependencies" already exists

2025-05-12 04:56:42,902 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:42,902 - db_utils - ERROR - Database error: constraint "input_dependencies_input_item_id_input_stack_id_fkey" for relation "input_dependencies" already exists
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.DuplicateObject: constraint "input_dependencies_input_item_id_input_stack_id_fkey" for relation "input_dependencies" already exists

2025-05-12 04:56:42,906 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:42,906 - db_utils - ERROR - Database error: constraint "input_dependencies_input_item_id_input_stack_id_fkey" for relation "input_dependencies" already exists
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.DuplicateObject: constraint "input_dependencies_input_item_id_input_stack_id_fkey" for relation "input_dependencies" already exists

2025-05-12 04:56:42,910 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:42,915 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:42,920 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:42,925 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:42,930 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:42,931 - db_utils - ERROR - Database error: constraint "lo_data_mapping_stack_lo_id_fkey" for relation "lo_data_mapping_stack" already exists
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.DuplicateObject: constraint "lo_data_mapping_stack_lo_id_fkey" for relation "lo_data_mapping_stack" already exists

2025-05-12 04:56:42,934 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:42,940 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:42,944 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:42,949 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:42,954 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:42,955 - db_utils - ERROR - Database error: column "inherits_from" referenced in foreign key constraint does not exist
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.UndefinedColumn: column "inherits_from" referenced in foreign key constraint does not exist

2025-05-12 04:56:42,960 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:42,961 - db_utils - ERROR - Database error: column "tenant_id" referenced in foreign key constraint does not exist
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.UndefinedColumn: column "tenant_id" referenced in foreign key constraint does not exist

2025-05-12 04:56:42,965 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:42,970 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:42,976 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:42,983 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:42,989 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:42,990 - db_utils - ERROR - Database error: there is no unique constraint matching given keys for referenced table "output_items"
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.InvalidForeignKey: there is no unique constraint matching given keys for referenced table "output_items"

2025-05-12 04:56:42,996 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:42,996 - db_utils - ERROR - Database error: there is no unique constraint matching given keys for referenced table "output_items"
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.InvalidForeignKey: there is no unique constraint matching given keys for referenced table "output_items"

2025-05-12 04:56:43,000 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:43,001 - db_utils - ERROR - Database error: there is no unique constraint matching given keys for referenced table "output_items"
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.InvalidForeignKey: there is no unique constraint matching given keys for referenced table "output_items"

2025-05-12 04:56:43,006 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:43,007 - db_utils - ERROR - Database error: there is no unique constraint matching given keys for referenced table "output_items"
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.InvalidForeignKey: there is no unique constraint matching given keys for referenced table "output_items"

2025-05-12 04:56:43,011 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:43,016 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:43,023 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:43,028 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:43,035 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:43,043 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:43,049 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:43,049 - db_utils - ERROR - Database error: constraint "lo_input_validations_input_item_id_input_stack_id_fkey" for relation "lo_input_validations" already exists
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.DuplicateObject: constraint "lo_input_validations_input_item_id_input_stack_id_fkey" for relation "lo_input_validations" already exists

2025-05-12 04:56:43,055 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:43,056 - db_utils - ERROR - Database error: constraint "lo_input_validations_input_item_id_input_stack_id_fkey" for relation "lo_input_validations" already exists
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.DuplicateObject: constraint "lo_input_validations_input_item_id_input_stack_id_fkey" for relation "lo_input_validations" already exists

2025-05-12 04:56:43,062 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:43,063 - db_utils - ERROR - Database error: constraint "lo_input_validations_input_item_id_input_stack_id_fkey" for relation "lo_input_validations" already exists
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.DuplicateObject: constraint "lo_input_validations_input_item_id_input_stack_id_fkey" for relation "lo_input_validations" already exists

2025-05-12 04:56:43,068 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:43,075 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:43,076 - db_utils - ERROR - Database error: constraint "local_objectives_go_id_fkey" for relation "local_objectives" already exists
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.DuplicateObject: constraint "local_objectives_go_id_fkey" for relation "local_objectives" already exists

2025-05-12 04:56:43,081 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:43,087 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:43,094 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:43,100 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:43,106 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:43,111 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:43,112 - db_utils - ERROR - Database error: constraint "lo_input_stack_lo_id_fkey" for relation "lo_input_stack" already exists
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.DuplicateObject: constraint "lo_input_stack_lo_id_fkey" for relation "lo_input_stack" already exists

2025-05-12 04:56:43,115 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:43,121 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:43,126 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:43,126 - db_utils - ERROR - Database error: constraint "lo_output_stack_lo_id_fkey" for relation "lo_output_stack" already exists
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.DuplicateObject: constraint "lo_output_stack_lo_id_fkey" for relation "lo_output_stack" already exists

2025-05-12 04:56:43,130 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:43,137 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:43,143 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:43,148 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:43,148 - db_utils - ERROR - Database error: constraint "lo_data_mappings_mapping_stack_id_fkey" for relation "lo_data_mappings" already exists
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.DuplicateObject: constraint "lo_data_mappings_mapping_stack_id_fkey" for relation "lo_data_mappings" already exists

2025-05-12 04:56:43,152 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:43,158 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:43,158 - db_utils - ERROR - Database error: constraint "lo_output_triggers_output_item_id_output_stack_id_fkey" for relation "lo_output_triggers" already exists
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.DuplicateObject: constraint "lo_output_triggers_output_item_id_output_stack_id_fkey" for relation "lo_output_triggers" already exists

2025-05-12 04:56:43,163 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:43,164 - db_utils - ERROR - Database error: constraint "lo_output_triggers_output_item_id_output_stack_id_fkey" for relation "lo_output_triggers" already exists
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.DuplicateObject: constraint "lo_output_triggers_output_item_id_output_stack_id_fkey" for relation "lo_output_triggers" already exists

2025-05-12 04:56:43,169 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:43,170 - db_utils - ERROR - Database error: constraint "lo_output_triggers_output_item_id_output_stack_id_fkey" for relation "lo_output_triggers" already exists
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.DuplicateObject: constraint "lo_output_triggers_output_item_id_output_stack_id_fkey" for relation "lo_output_triggers" already exists

2025-05-12 04:56:43,174 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:43,181 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:43,182 - db_utils - ERROR - Database error: constraint "input_data_sources_input_item_id_input_stack_id_fkey" for relation "input_data_sources" already exists
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.DuplicateObject: constraint "input_data_sources_input_item_id_input_stack_id_fkey" for relation "input_data_sources" already exists

2025-05-12 04:56:43,186 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:43,186 - db_utils - ERROR - Database error: constraint "input_data_sources_input_item_id_input_stack_id_fkey" for relation "input_data_sources" already exists
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.DuplicateObject: constraint "input_data_sources_input_item_id_input_stack_id_fkey" for relation "input_data_sources" already exists

2025-05-12 04:56:43,190 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:43,190 - db_utils - ERROR - Database error: constraint "input_data_sources_input_item_id_input_stack_id_fkey" for relation "input_data_sources" already exists
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.DuplicateObject: constraint "input_data_sources_input_item_id_input_stack_id_fkey" for relation "input_data_sources" already exists

2025-05-12 04:56:43,195 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:43,200 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:43,207 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:43,214 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:43,221 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:43,227 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:43,227 - db_utils - ERROR - Database error: constraint "dropdown_data_sources_input_item_id_input_stack_id_fkey" for relation "dropdown_data_sources" already exists
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.DuplicateObject: constraint "dropdown_data_sources_input_item_id_input_stack_id_fkey" for relation "dropdown_data_sources" already exists

2025-05-12 04:56:43,233 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:43,233 - db_utils - ERROR - Database error: constraint "dropdown_data_sources_input_item_id_input_stack_id_fkey" for relation "dropdown_data_sources" already exists
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.DuplicateObject: constraint "dropdown_data_sources_input_item_id_input_stack_id_fkey" for relation "dropdown_data_sources" already exists

2025-05-12 04:56:43,238 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:43,239 - db_utils - ERROR - Database error: constraint "dropdown_data_sources_input_item_id_input_stack_id_fkey" for relation "dropdown_data_sources" already exists
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.DuplicateObject: constraint "dropdown_data_sources_input_item_id_input_stack_id_fkey" for relation "dropdown_data_sources" already exists

2025-05-12 04:56:43,245 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:43,247 - component_deployer - INFO - Successfully analyzed workflow_runtime schema and created necessary tables
2025-05-12 04:56:43,247 - entity_example_detailed - INFO - Deploying entity to schema workflow_temp
2025-05-12 04:56:43,247 - component_deployer - INFO - Deploying entities from prescriptive text
2025-05-12 04:56:43,247 - prescriptive_parser - INFO - Initializing PrescriptiveParser
2025-05-12 04:56:43,247 - prescriptive_parser - INFO - Parsing entities prescriptive text
2025-05-12 04:56:43,247 - entity_parser - INFO - Starting to parse entity definitions
2025-05-12 04:56:43,247 - entity_parser - INFO - Parsing entity: Employee
2025-05-12 04:56:43,247 - entity_parser - INFO - Successfully parsed entity 'Employee'
2025-05-12 04:56:43,247 - entity_parser - INFO - Parsing entity: Employee
2025-05-12 04:56:43,248 - entity_parser - INFO - Successfully parsed entity 'Employee'
2025-05-12 04:56:43,248 - entity_parser - INFO - Parsing entity: Department
2025-05-12 04:56:43,248 - entity_parser - INFO - Successfully parsed entity 'Department'
2025-05-12 04:56:43,248 - entity_parser - INFO - Successfully parsed 2 entities
2025-05-12 04:56:43,252 - component_deployer - INFO - Deploying component of type: entities
2025-05-12 04:56:43,260 - component_deployer - INFO - Using schema: workflow_temp
2025-05-12 04:56:43,260 - entity_deployer - INFO - Deploying entities to workflow_temp
2025-05-12 04:56:43,265 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:43,272 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:43,281 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:43,290 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:43,299 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:43,300 - entity_deployer - INFO - Inserted entity 'Department' into workflow_temp.entities
2025-05-12 04:56:43,304 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:43,309 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:43,319 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:43,327 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:43,337 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:43,339 - entity_deployer - INFO - Inserted attribute 'budget' for entity 'entity_department' into workflow_temp.entity_attributes
2025-05-12 04:56:43,343 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:43,344 - entity_deployer - INFO - Inserted attribute 'departmentId' for entity 'entity_department' into workflow_temp.entity_attributes
2025-05-12 04:56:43,349 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:43,350 - entity_deployer - INFO - Inserted attribute 'location' for entity 'entity_department' into workflow_temp.entity_attributes
2025-05-12 04:56:43,354 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:43,355 - entity_deployer - INFO - Inserted attribute 'managerId' for entity 'entity_department' into workflow_temp.entity_attributes
2025-05-12 04:56:43,359 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:43,360 - entity_deployer - INFO - Inserted attribute 'name' for entity 'entity_department' into workflow_temp.entity_attributes
2025-05-12 04:56:43,365 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:43,370 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:43,375 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:43,376 - entity_deployer - WARNING - Warning: Relationship 'one-to-many_Employee' in entity 'entity_department' references non-existent entity 'entity_employee'
2025-05-12 04:56:43,381 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:43,382 - entity_deployer - WARNING - Warning: Relationship 'one-to-one_Employee' in entity 'entity_department' references non-existent entity 'entity_employee'
2025-05-12 04:56:43,386 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:43,391 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:43,397 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:43,398 - entity_deployer - INFO - Inserted business rule 'DEPT001' for entity 'entity_department' into workflow_temp.entity_business_rules
2025-05-12 04:56:43,402 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:43,406 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:43,416 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:43,425 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:43,433 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:43,434 - entity_deployer - INFO - Inserted entity 'Employee' into workflow_temp.entities
2025-05-12 04:56:43,437 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:43,442 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:43,450 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:43,458 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:43,466 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:43,468 - entity_deployer - INFO - Inserted attribute 'employeeId' for entity 'entity_employee' into workflow_temp.entity_attributes
2025-05-12 04:56:43,471 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:43,473 - entity_deployer - INFO - Inserted attribute 'firstName' for entity 'entity_employee' into workflow_temp.entity_attributes
2025-05-12 04:56:43,476 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:43,477 - entity_deployer - INFO - Inserted attribute 'fullName[derived]' for entity 'entity_employee' into workflow_temp.entity_attributes
2025-05-12 04:56:43,481 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:43,483 - entity_deployer - INFO - Inserted attribute 'lastName' for entity 'entity_employee' into workflow_temp.entity_attributes
2025-05-12 04:56:43,495 - entity_deployer - INFO - Entity deployment completed successfully
2025-05-12 04:56:43,495 - component_deployer - INFO - Component deployment succeeded
2025-05-12 04:56:43,495 - entity_example_detailed - INFO - Deploy messages:
2025-05-12 04:56:43,495 - entity_example_detailed - INFO -   Inserted entity 'Department' into workflow_temp.entities
2025-05-12 04:56:43,495 - entity_example_detailed - INFO -   Inserted attribute 'budget' for entity 'entity_department' into workflow_temp.entity_attributes
2025-05-12 04:56:43,495 - entity_example_detailed - INFO -   Inserted attribute 'departmentId' for entity 'entity_department' into workflow_temp.entity_attributes
2025-05-12 04:56:43,495 - entity_example_detailed - INFO -   Inserted attribute 'location' for entity 'entity_department' into workflow_temp.entity_attributes
2025-05-12 04:56:43,495 - entity_example_detailed - INFO -   Inserted attribute 'managerId' for entity 'entity_department' into workflow_temp.entity_attributes
2025-05-12 04:56:43,495 - entity_example_detailed - INFO -   Inserted attribute 'name' for entity 'entity_department' into workflow_temp.entity_attributes
2025-05-12 04:56:43,495 - entity_example_detailed - INFO -   Warning: Relationship 'one-to-many_Employee' in entity 'entity_department' references non-existent entity 'entity_employee'
2025-05-12 04:56:43,495 - entity_example_detailed - INFO -   Warning: Relationship 'one-to-one_Employee' in entity 'entity_department' references non-existent entity 'entity_employee'
2025-05-12 04:56:43,495 - entity_example_detailed - INFO -   Inserted business rule 'DEPT001' for entity 'entity_department' into workflow_temp.entity_business_rules
2025-05-12 04:56:43,495 - entity_example_detailed - INFO -   Inserted entity 'Employee' into workflow_temp.entities
2025-05-12 04:56:43,495 - entity_example_detailed - INFO -   Inserted attribute 'employeeId' for entity 'entity_employee' into workflow_temp.entity_attributes
2025-05-12 04:56:43,495 - entity_example_detailed - INFO -   Inserted attribute 'firstName' for entity 'entity_employee' into workflow_temp.entity_attributes
2025-05-12 04:56:43,495 - entity_example_detailed - INFO -   Inserted attribute 'fullName[derived]' for entity 'entity_employee' into workflow_temp.entity_attributes
2025-05-12 04:56:43,495 - entity_example_detailed - INFO -   Inserted attribute 'lastName' for entity 'entity_employee' into workflow_temp.entity_attributes
2025-05-12 04:56:43,495 - entity_example_detailed - INFO -   Inserted document into components
2025-05-12 04:56:43,495 - entity_example_detailed - INFO - === Querying Deployed Entity ===
2025-05-12 04:56:43,495 - entity_example_detailed - INFO - Querying workflow_temp.entities table
2025-05-12 04:56:43,499 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:43,500 - entity_example_detailed - INFO - Entities:
2025-05-12 04:56:43,500 - entity_example_detailed - INFO -   ('entity_department', 'Department', '', {}, {})
2025-05-12 04:56:43,500 - entity_example_detailed - INFO -   ('entity_employee', 'Employee', '', {}, {})
2025-05-12 04:56:43,500 - entity_example_detailed - INFO - Querying workflow_temp.entity_attributes table
2025-05-12 04:56:43,504 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 04:56:43,505 - entity_example_detailed - INFO - Entity Attributes:
2025-05-12 04:56:43,505 - entity_example_detailed - INFO -   ('entity_department_attr_budget', 'entity_department', 'budget', 'string', False, None, False, None, [])
2025-05-12 04:56:43,505 - entity_example_detailed - INFO -   ('entity_department_attr_departmentid', 'entity_department', 'departmentId', 'string', True, None, False, None, [])
2025-05-12 04:56:43,505 - entity_example_detailed - INFO -   ('entity_department_attr_location', 'entity_department', 'location', 'string', False, None, False, None, [])
2025-05-12 04:56:43,505 - entity_example_detailed - INFO -   ('entity_department_attr_managerid', 'entity_department', 'managerId', 'string', False, None, False, None, [])
2025-05-12 04:56:43,505 - entity_example_detailed - INFO -   ('entity_department_attr_name', 'entity_department', 'name', 'string', False, None, False, None, [])
2025-05-12 04:56:43,505 - entity_example_detailed - INFO -   ('entity_employee_attr_employeeid', 'entity_employee', 'employeeId', 'string', False, None, False, None, [])
2025-05-12 04:56:43,505 - entity_example_detailed - INFO -   ('entity_employee_attr_firstname', 'entity_employee', 'firstName', 'string', False, None, False, None, [])
2025-05-12 04:56:43,505 - entity_example_detailed - INFO -   ('entity_employee_attr_fullname[derived]', 'entity_employee', 'fullName[derived]', 'string', False, None, False, None, [])
2025-05-12 04:56:43,505 - entity_example_detailed - INFO -   ('entity_employee_attr_lastname', 'entity_employee', 'lastName', 'string', False, None, False, None, [])
2025-05-12 04:56:43,505 - entity_example_detailed - INFO - === Summary ===
2025-05-12 04:56:43,505 - entity_example_detailed - INFO - Deployment successful: True
2025-05-12 06:42:21,619 - entity_example_detailed - INFO - === Parsing Entity ===
2025-05-12 06:42:21,619 - prescriptive_parser - INFO - Initializing PrescriptiveParser
2025-05-12 06:42:21,619 - entity_example_detailed - INFO - Parsing prescriptive text:
2025-05-12 06:42:21,619 - entity_example_detailed - INFO - Employee has employeeId^PK, firstName, lastName, email, phoneNumber, departmentId^FK, managerId^FK, hireDate, status (Active, Inactive, OnLeave), salary, performanceRating.

* Employee has many-to-one relationship with Department using Employee.departmentId to Department.departmentId^PK
* Employee has many-to-one relationship with Employee using Employee.managerId to Employee.employeeId^PK

* Employee.email must be unique
* Employee.email must match pattern "^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$"
* Employee.hireDate must be before current date
* Employee.salary must be greater than 0

BusinessRule EMP001 for Employee:
* Employee.status must be Active to receive performance reviews
* Employee.hireDate must be at least 90 days before Employee.performanceRating can be set

Employee has employeeId, firstName, lastName, fullName[derived].

CalculatedField CF001 for Employee.fullName:
* Formula: CONCAT(firstName, ' ', lastName)
* Logic Layer: Application
* Caching: Session
* Dependencies: Employee.firstName, Employee.lastName

* Archive Strategy for Employee:
  - Trigger: Event-based
  - Criteria: When Employee.status changes to 'Inactive' and remains so for 1 year
  - Retention: 7 years
  - Storage: Cold storage
  - Access Pattern: Read-only through HR archive portal
  - Restoration: Manual process requiring HR Director approval

* History Tracking for Employee:
  - Tracked Attributes: Employee.salary, Employee.departmentId, Employee.managerId, Employee.status
  - Tracking Method: Audit table
  - Granularity: Change level
  - Retention: 7 years
  - Access Control: HR Managers and Compliance Officers only

Department has departmentId^PK, name, managerId^FK, location, budget.

* Department has one-to-one relationship with Employee using Department.managerId to Employee.employeeId^PK
* Department has one-to-many relationship with Employee using Department.departmentId to Employee.departmentId^FK

* Department.name must be unique
* Department.budget must be greater than 0

BusinessRule DEPT001 for Department:
* Department.budget must be approved by Finance before changes
* Department.managerId must reference an Employee with status = 'Active'

* Archive Strategy for Department:
  - Trigger: Event-based
  - Criteria: When Department is marked as 'Dissolved'
  - Retention: 10 years
  - Storage: Cold storage
  - Access Pattern: Read-only through Admin portal
  - Restoration: Manual process requiring CEO approval

2025-05-12 06:42:21,619 - prescriptive_parser - INFO - Parsing entities prescriptive text
2025-05-12 06:42:21,619 - entity_parser - INFO - Starting to parse entity definitions
2025-05-12 06:42:21,619 - entity_parser - INFO - Parsing entity: Employee
2025-05-12 06:42:21,620 - entity_parser - INFO - Successfully parsed entity 'Employee'
2025-05-12 06:42:21,620 - entity_parser - INFO - Parsing entity: Employee
2025-05-12 06:42:21,620 - entity_parser - INFO - Successfully parsed entity 'Employee'
2025-05-12 06:42:21,620 - entity_parser - INFO - Parsing entity: Department
2025-05-12 06:42:21,620 - entity_parser - INFO - Successfully parsed entity 'Department'
2025-05-12 06:42:21,620 - entity_parser - INFO - Successfully parsed 2 entities
2025-05-12 06:42:21,620 - entity_example_detailed - INFO - Parsed entity data:
2025-05-12 06:42:21,620 - entity_example_detailed - INFO - Entity: Employee
2025-05-12 06:42:21,620 - entity_example_detailed - INFO -   Description: 
2025-05-12 06:42:21,620 - entity_example_detailed - INFO -   Attributes:
2025-05-12 06:42:21,620 - entity_example_detailed - INFO -     employeeId: {'type': 'string', 'required': False, 'primary_key': False, 'foreign_key': False}
2025-05-12 06:42:21,620 - entity_example_detailed - INFO -     firstName: {'type': 'string', 'required': False, 'primary_key': False, 'foreign_key': False}
2025-05-12 06:42:21,620 - entity_example_detailed - INFO -     lastName: {'type': 'string', 'required': False, 'primary_key': False, 'foreign_key': False}
2025-05-12 06:42:21,620 - entity_example_detailed - INFO -     fullName[derived]: {'type': 'string', 'required': False, 'primary_key': False, 'foreign_key': False}
2025-05-12 06:42:21,620 - entity_example_detailed - INFO -   Relationships:
2025-05-12 06:42:21,620 - entity_example_detailed - INFO -   Business Rules:
2025-05-12 06:42:21,620 - entity_example_detailed - INFO -   Calculated Fields:
2025-05-12 06:42:21,620 - entity_example_detailed - INFO -     CF001: {'attribute': 'fullName', 'formula': "CONCAT(firstName, ' ', lastName)", 'logic_layer': 'Application', 'caching': 'Session', 'dependencies': 'Employee.firstName, Employee.lastName', 'archive_strategy_for_employee': '', 'history_tracking_for_employee': ''}
2025-05-12 06:42:21,620 - entity_example_detailed - INFO -   Lifecycle Management:
2025-05-12 06:42:21,620 - entity_example_detailed - INFO - Entity: Department
2025-05-12 06:42:21,620 - entity_example_detailed - INFO -   Description: 
2025-05-12 06:42:21,620 - entity_example_detailed - INFO -   Attributes:
2025-05-12 06:42:21,621 - entity_example_detailed - INFO -     departmentId: {'type': 'string', 'required': True, 'primary_key': True, 'foreign_key': False}
2025-05-12 06:42:21,621 - entity_example_detailed - INFO -     name: {'type': 'string', 'required': False, 'primary_key': False, 'foreign_key': False}
2025-05-12 06:42:21,621 - entity_example_detailed - INFO -     managerId: {'type': 'string', 'required': False, 'primary_key': False, 'foreign_key': True}
2025-05-12 06:42:21,621 - entity_example_detailed - INFO -     location: {'type': 'string', 'required': False, 'primary_key': False, 'foreign_key': False}
2025-05-12 06:42:21,621 - entity_example_detailed - INFO -     budget: {'type': 'string', 'required': False, 'primary_key': False, 'foreign_key': False}
2025-05-12 06:42:21,621 - entity_example_detailed - INFO -   Relationships:
2025-05-12 06:42:21,621 - entity_example_detailed - INFO -     one-to-one_Employee: {'type': 'one-to-one', 'entity': 'Employee', 'source_attribute': 'managerId', 'target_attribute': 'employeeId'}
2025-05-12 06:42:21,621 - entity_example_detailed - INFO -     one-to-many_Employee: {'type': 'one-to-many', 'entity': 'Employee', 'source_attribute': 'departmentId', 'target_attribute': 'departmentId'}
2025-05-12 06:42:21,621 - entity_example_detailed - INFO -   Business Rules:
2025-05-12 06:42:21,621 - entity_example_detailed - INFO -     DEPT001: {'conditions': ['Department.budget must be approved by Finance before changes', "Department.managerId must reference an Employee with status = 'Active'", 'Archive Strategy for Department:']}
2025-05-12 06:42:21,621 - entity_example_detailed - INFO -   Calculated Fields:
2025-05-12 06:42:21,621 - entity_example_detailed - INFO -   Lifecycle Management:
2025-05-12 06:42:21,621 - entity_example_detailed - INFO - === Comparing with insert_generator.py ===
2025-05-12 06:42:21,621 - entity_example_detailed - INFO - Converted entity data to insert_generator.py format:
2025-05-12 06:42:21,621 - entity_example_detailed - INFO - {
  "entities": [
    {
      "id": "en_employee",
      "name": "Employee",
      "type": "standard",
      "version": "1.0",
      "status": "active",
      "attributes": [
        {
          "id": "at_employeeid",
          "name": "employeeId",
          "display_name": "employeeId",
          "datatype": "string",
          "required": false,
          "version": "1.0",
          "status": "active"
        },
        {
          "id": "at_firstname",
          "name": "firstName",
          "display_name": "firstName",
          "datatype": "string",
          "required": false,
          "version": "1.0",
          "status": "active"
        },
        {
          "id": "at_lastname",
          "name": "lastName",
          "display_name": "lastName",
          "datatype": "string",
          "required": false,
          "version": "1.0",
          "status": "active"
        },
        {
          "id": "at_fullname[derived]",
          "name": "fullName[derived]",
          "display_name": "fullName[derived]",
          "datatype": "string",
          "required": false,
          "version": "1.0",
          "status": "active"
        }
      ]
    },
    {
      "id": "en_department",
      "name": "Department",
      "type": "standard",
      "version": "1.0",
      "status": "active",
      "attributes": [
        {
          "id": "at_departmentid",
          "name": "departmentId",
          "display_name": "departmentId",
          "datatype": "string",
          "required": true,
          "version": "1.0",
          "status": "active"
        },
        {
          "id": "at_name",
          "name": "name",
          "display_name": "name",
          "datatype": "string",
          "required": false,
          "version": "1.0",
          "status": "active"
        },
        {
          "id": "at_managerid",
          "name": "managerId",
          "display_name": "managerId",
          "datatype": "string",
          "required": false,
          "version": "1.0",
          "status": "active"
        },
        {
          "id": "at_location",
          "name": "location",
          "display_name": "location",
          "datatype": "string",
          "required": false,
          "version": "1.0",
          "status": "active"
        },
        {
          "id": "at_budget",
          "name": "budget",
          "display_name": "budget",
          "datatype": "string",
          "required": false,
          "version": "1.0",
          "status": "active"
        }
      ],
      "relationships": [
        {
          "entity_id": "Employee",
          "type": "one-to-one",
          "through_attribute": "",
          "to_attribute": ""
        },
        {
          "entity_id": "Employee",
          "type": "one-to-many",
          "through_attribute": "",
          "to_attribute": ""
        }
      ]
    }
  ]
}
2025-05-12 06:42:21,621 - entity_example_detailed - INFO - [COMPARISON] Simulating insert_generator.py processing...
2025-05-12 06:42:21,621 - entity_example_detailed - INFO - [COMPARISON] Entity table inserts:
2025-05-12 06:42:21,621 - entity_example_detailed - INFO - [COMPARISON] SQL: 
        INSERT INTO entities (entity_id, name, type, version, status, created_at, updated_at)
        VALUES ('en_employee', 'Employee', 'standard', '1.0', 'active', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);
        
2025-05-12 06:42:21,621 - entity_example_detailed - INFO - [COMPARISON] SQL: 
        INSERT INTO entities (entity_id, name, type, version, status, created_at, updated_at)
        VALUES ('en_department', 'Department', 'standard', '1.0', 'active', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);
        
2025-05-12 06:42:21,621 - entity_example_detailed - INFO - [COMPARISON] Entity attribute inserts:
2025-05-12 06:42:21,621 - entity_example_detailed - INFO - [COMPARISON] SQL: 
            INSERT INTO entity_attributes (attribute_id, entity_id, name, display_name, datatype, version, status, required, created_at, updated_at)
            VALUES ('at_employeeid', 'en_employee', 'employeeId', 'employeeId', 'string', '1.0', 'active', False, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);
            
2025-05-12 06:42:21,621 - entity_example_detailed - INFO - [COMPARISON] SQL: 
            INSERT INTO entity_attributes (attribute_id, entity_id, name, display_name, datatype, version, status, required, created_at, updated_at)
            VALUES ('at_firstname', 'en_employee', 'firstName', 'firstName', 'string', '1.0', 'active', False, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);
            
2025-05-12 06:42:21,621 - entity_example_detailed - INFO - [COMPARISON] SQL: 
            INSERT INTO entity_attributes (attribute_id, entity_id, name, display_name, datatype, version, status, required, created_at, updated_at)
            VALUES ('at_lastname', 'en_employee', 'lastName', 'lastName', 'string', '1.0', 'active', False, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);
            
2025-05-12 06:42:21,621 - entity_example_detailed - INFO - [COMPARISON] SQL: 
            INSERT INTO entity_attributes (attribute_id, entity_id, name, display_name, datatype, version, status, required, created_at, updated_at)
            VALUES ('at_fullname[derived]', 'en_employee', 'fullName[derived]', 'fullName[derived]', 'string', '1.0', 'active', False, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);
            
2025-05-12 06:42:21,621 - entity_example_detailed - INFO - [COMPARISON] SQL: 
            INSERT INTO entity_attributes (attribute_id, entity_id, name, display_name, datatype, version, status, required, created_at, updated_at)
            VALUES ('at_departmentid', 'en_department', 'departmentId', 'departmentId', 'string', '1.0', 'active', True, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);
            
2025-05-12 06:42:21,621 - entity_example_detailed - INFO - [COMPARISON] SQL: 
            INSERT INTO entity_attributes (attribute_id, entity_id, name, display_name, datatype, version, status, required, created_at, updated_at)
            VALUES ('at_name', 'en_department', 'name', 'name', 'string', '1.0', 'active', False, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);
            
2025-05-12 06:42:21,621 - entity_example_detailed - INFO - [COMPARISON] SQL: 
            INSERT INTO entity_attributes (attribute_id, entity_id, name, display_name, datatype, version, status, required, created_at, updated_at)
            VALUES ('at_managerid', 'en_department', 'managerId', 'managerId', 'string', '1.0', 'active', False, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);
            
2025-05-12 06:42:21,621 - entity_example_detailed - INFO - [COMPARISON] SQL: 
            INSERT INTO entity_attributes (attribute_id, entity_id, name, display_name, datatype, version, status, required, created_at, updated_at)
            VALUES ('at_location', 'en_department', 'location', 'location', 'string', '1.0', 'active', False, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);
            
2025-05-12 06:42:21,621 - entity_example_detailed - INFO - [COMPARISON] SQL: 
            INSERT INTO entity_attributes (attribute_id, entity_id, name, display_name, datatype, version, status, required, created_at, updated_at)
            VALUES ('at_budget', 'en_department', 'budget', 'budget', 'string', '1.0', 'active', False, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);
            
2025-05-12 06:42:21,621 - entity_example_detailed - INFO - [COMPARISON] Entity relationship inserts:
2025-05-12 06:42:21,621 - entity_example_detailed - INFO - [COMPARISON] SQL: 
                INSERT INTO entity_relationships (source_entity_id, target_entity_id, relationship_type, source_attribute_id, target_attribute_id, created_at, updated_at)
                VALUES ('en_department', 'Employee', 'one-to-one', '', '', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);
                
2025-05-12 06:42:21,621 - entity_example_detailed - INFO - [COMPARISON] SQL: 
                INSERT INTO entity_relationships (source_entity_id, target_entity_id, relationship_type, source_attribute_id, target_attribute_id, created_at, updated_at)
                VALUES ('en_department', 'Employee', 'one-to-many', '', '', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);
                
2025-05-12 06:42:21,621 - entity_example_detailed - INFO - [COMPARISON] Comparing with v2 deployer:
2025-05-12 06:42:21,622 - entity_example_detailed - INFO - [COMPARISON] 1. v2 deployer uses prescriptive_parser.py to parse prescriptive text into structured data
2025-05-12 06:42:21,622 - entity_example_detailed - INFO - [COMPARISON] 2. v2 deployer uses component_deployer.py to deploy the structured data to the database
2025-05-12 06:42:21,622 - entity_example_detailed - INFO - [COMPARISON] 3. v2 deployer uses entity_deployer.py to deploy entities to the database
2025-05-12 06:42:21,622 - entity_example_detailed - INFO - [COMPARISON] Key differences:
2025-05-12 06:42:21,622 - entity_example_detailed - INFO - [COMPARISON] 1. insert_generator.py uses a JSON file as input, while v2 uses prescriptive text
2025-05-12 06:42:21,622 - entity_example_detailed - INFO - [COMPARISON] 2. insert_generator.py generates SQL statements, while v2 executes them directly
2025-05-12 06:42:21,622 - entity_example_detailed - INFO - [COMPARISON] 3. insert_generator.py handles multiple components at once, while v2 can handle them separately
2025-05-12 06:42:21,622 - entity_example_detailed - INFO - [COMPARISON] 4. v2 has more validation and error handling
2025-05-12 06:42:21,622 - entity_example_detailed - INFO - [COMPARISON] 5. v2 uses a temporary schema for validation before promoting to runtime schema
2025-05-12 06:42:21,622 - entity_example_detailed - INFO - === Deploying Entity ===
2025-05-12 06:42:21,622 - component_deployer - INFO - ComponentDeployer initialized with use_temp_schema=True
2025-05-12 06:42:21,627 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:21,668 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:21,674 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:21,683 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:21,690 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:21,698 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:21,704 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:21,711 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:21,719 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:21,729 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:21,737 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:21,746 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:21,755 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:21,762 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:21,770 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:21,779 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:21,787 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:21,789 - component_deployer - INFO - Analyzing workflow_runtime schema to create necessary tables in temporary schema
2025-05-12 06:42:21,794 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:21,802 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:21,806 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:21,813 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:21,824 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:21,835 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:21,842 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:21,853 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:21,862 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:21,871 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:21,883 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:21,892 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:21,900 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:21,909 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:21,919 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:21,929 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:21,939 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:21,949 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:21,958 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:21,969 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:21,980 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:21,988 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:21,997 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:22,008 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:22,016 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:22,027 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:22,037 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:22,045 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:22,057 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:22,067 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:22,075 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:22,085 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:22,094 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:22,101 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:22,110 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:22,119 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:22,130 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:22,138 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:22,148 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:22,174 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:22,185 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:22,195 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:22,204 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:22,214 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:22,224 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:22,232 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:22,243 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:22,251 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:22,259 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:22,268 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:22,278 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:22,285 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:22,296 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:22,306 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:22,313 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:22,323 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:22,331 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:22,339 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:22,349 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:22,358 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:22,366 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:22,376 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:22,386 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:22,393 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:22,403 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:22,412 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:22,420 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:22,430 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:22,440 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:22,448 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:22,460 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:22,468 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:22,476 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:22,488 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:22,496 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:22,504 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:22,514 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:22,524 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:22,533 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:22,542 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:22,551 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:22,558 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:22,569 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:22,577 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:22,585 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:22,594 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:22,604 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:22,610 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:22,621 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:22,629 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:22,637 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:22,646 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:22,655 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:22,662 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:22,672 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:22,680 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:22,688 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:22,699 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:22,709 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:22,717 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:22,726 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:22,736 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:22,742 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:22,751 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:22,759 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:22,767 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:22,778 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:22,786 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:22,793 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:22,803 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:22,811 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:22,818 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:22,828 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:22,836 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:22,844 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:22,854 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:22,863 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:22,870 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:22,881 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:22,889 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:22,896 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:22,907 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:22,915 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:22,922 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:22,932 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:22,940 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:22,947 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:22,957 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:22,967 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:22,975 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:22,985 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:22,994 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:23,002 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:23,012 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:23,021 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:23,027 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:23,038 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:23,048 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:23,057 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:23,067 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:23,078 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:23,085 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:23,095 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:23,105 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:23,112 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:23,122 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:23,132 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:23,141 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:23,152 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:23,162 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:23,170 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:23,180 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:23,189 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:23,197 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:23,207 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:23,215 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:23,223 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:23,232 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:23,242 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:23,250 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:23,261 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:23,271 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:23,279 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:23,289 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:23,297 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:23,305 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:23,314 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:23,323 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:23,332 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:23,341 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:23,350 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:23,358 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:23,367 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:23,377 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:23,383 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:23,392 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:23,400 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:23,408 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:23,417 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:23,425 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:23,431 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:23,440 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:23,447 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:23,457 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:23,466 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:23,474 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:23,483 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:23,494 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:23,502 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:23,512 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:23,521 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:23,531 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:23,539 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:23,549 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:23,559 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:23,567 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:23,577 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:23,587 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:23,597 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:23,606 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:23,616 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:23,625 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:23,634 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:23,644 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:23,652 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:23,661 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:23,671 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:23,680 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:23,759 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:23,768 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:23,776 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:23,784 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:23,790 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:23,798 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:23,804 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:23,811 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:23,819 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:23,825 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:23,830 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:23,838 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:23,843 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:23,849 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:23,856 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:23,861 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:23,862 - db_utils - ERROR - Database error: constraint "entity_attributes_entity_id_fkey" for relation "entity_attributes" already exists
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.DuplicateObject: constraint "entity_attributes_entity_id_fkey" for relation "entity_attributes" already exists

2025-05-12 06:42:23,866 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:23,873 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:23,878 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:23,879 - db_utils - ERROR - Database error: constraint "lo_input_items_input_stack_id_fkey" for relation "lo_input_items" already exists
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.DuplicateObject: constraint "lo_input_items_input_stack_id_fkey" for relation "lo_input_items" already exists

2025-05-12 06:42:23,882 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:23,883 - db_utils - ERROR - Database error: constraint "lo_input_items_lo_id_fkey" for relation "lo_input_items" already exists
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.DuplicateObject: constraint "lo_input_items_lo_id_fkey" for relation "lo_input_items" already exists

2025-05-12 06:42:23,888 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:23,893 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:23,894 - db_utils - ERROR - Database error: there is no unique constraint matching given keys for referenced table "roles"
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.InvalidForeignKey: there is no unique constraint matching given keys for referenced table "roles"

2025-05-12 06:42:23,899 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:23,906 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:23,907 - db_utils - ERROR - Database error: constraint "lo_nested_functions_lo_id_fkey" for relation "lo_nested_functions" already exists
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.DuplicateObject: constraint "lo_nested_functions_lo_id_fkey" for relation "lo_nested_functions" already exists

2025-05-12 06:42:23,911 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:23,912 - db_utils - ERROR - Database error: constraint "lo_output_items_lo_id_fkey" for relation "lo_output_items" already exists
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.DuplicateObject: constraint "lo_output_items_lo_id_fkey" for relation "lo_output_items" already exists

2025-05-12 06:42:23,916 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:23,916 - db_utils - ERROR - Database error: constraint "lo_output_items_output_stack_id_fkey" for relation "lo_output_items" already exists
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.DuplicateObject: constraint "lo_output_items_output_stack_id_fkey" for relation "lo_output_items" already exists

2025-05-12 06:42:23,921 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:23,926 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:23,934 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:23,934 - db_utils - ERROR - Database error: constraint "input_dependencies_depends_on_id_depends_on_stack_id_fkey" for relation "input_dependencies" already exists
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.DuplicateObject: constraint "input_dependencies_depends_on_id_depends_on_stack_id_fkey" for relation "input_dependencies" already exists

2025-05-12 06:42:23,940 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:23,941 - db_utils - ERROR - Database error: constraint "input_dependencies_depends_on_id_depends_on_stack_id_fkey" for relation "input_dependencies" already exists
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.DuplicateObject: constraint "input_dependencies_depends_on_id_depends_on_stack_id_fkey" for relation "input_dependencies" already exists

2025-05-12 06:42:23,945 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:23,945 - db_utils - ERROR - Database error: constraint "input_dependencies_depends_on_id_depends_on_stack_id_fkey" for relation "input_dependencies" already exists
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.DuplicateObject: constraint "input_dependencies_depends_on_id_depends_on_stack_id_fkey" for relation "input_dependencies" already exists

2025-05-12 06:42:23,950 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:23,957 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:23,958 - db_utils - ERROR - Database error: constraint "input_dependencies_input_item_id_input_stack_id_fkey" for relation "input_dependencies" already exists
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.DuplicateObject: constraint "input_dependencies_input_item_id_input_stack_id_fkey" for relation "input_dependencies" already exists

2025-05-12 06:42:23,962 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:23,963 - db_utils - ERROR - Database error: constraint "input_dependencies_input_item_id_input_stack_id_fkey" for relation "input_dependencies" already exists
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.DuplicateObject: constraint "input_dependencies_input_item_id_input_stack_id_fkey" for relation "input_dependencies" already exists

2025-05-12 06:42:23,967 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:23,967 - db_utils - ERROR - Database error: constraint "input_dependencies_input_item_id_input_stack_id_fkey" for relation "input_dependencies" already exists
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.DuplicateObject: constraint "input_dependencies_input_item_id_input_stack_id_fkey" for relation "input_dependencies" already exists

2025-05-12 06:42:23,971 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:23,978 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:23,985 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:23,991 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:23,998 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:23,999 - db_utils - ERROR - Database error: constraint "lo_data_mapping_stack_lo_id_fkey" for relation "lo_data_mapping_stack" already exists
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.DuplicateObject: constraint "lo_data_mapping_stack_lo_id_fkey" for relation "lo_data_mapping_stack" already exists

2025-05-12 06:42:24,005 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:24,011 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:24,018 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:24,025 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:24,031 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:24,031 - db_utils - ERROR - Database error: column "inherits_from" referenced in foreign key constraint does not exist
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.UndefinedColumn: column "inherits_from" referenced in foreign key constraint does not exist

2025-05-12 06:42:24,037 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:24,037 - db_utils - ERROR - Database error: column "tenant_id" referenced in foreign key constraint does not exist
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.UndefinedColumn: column "tenant_id" referenced in foreign key constraint does not exist

2025-05-12 06:42:24,042 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:24,047 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:24,054 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:24,060 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:24,067 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:24,068 - db_utils - ERROR - Database error: there is no unique constraint matching given keys for referenced table "output_items"
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.InvalidForeignKey: there is no unique constraint matching given keys for referenced table "output_items"

2025-05-12 06:42:24,071 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:24,072 - db_utils - ERROR - Database error: there is no unique constraint matching given keys for referenced table "output_items"
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.InvalidForeignKey: there is no unique constraint matching given keys for referenced table "output_items"

2025-05-12 06:42:24,077 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:24,078 - db_utils - ERROR - Database error: there is no unique constraint matching given keys for referenced table "output_items"
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.InvalidForeignKey: there is no unique constraint matching given keys for referenced table "output_items"

2025-05-12 06:42:24,084 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:24,084 - db_utils - ERROR - Database error: there is no unique constraint matching given keys for referenced table "output_items"
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.InvalidForeignKey: there is no unique constraint matching given keys for referenced table "output_items"

2025-05-12 06:42:24,088 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:24,095 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:24,102 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:24,108 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:24,115 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:24,123 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:24,130 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:24,131 - db_utils - ERROR - Database error: constraint "lo_input_validations_input_item_id_input_stack_id_fkey" for relation "lo_input_validations" already exists
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.DuplicateObject: constraint "lo_input_validations_input_item_id_input_stack_id_fkey" for relation "lo_input_validations" already exists

2025-05-12 06:42:24,136 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:24,137 - db_utils - ERROR - Database error: constraint "lo_input_validations_input_item_id_input_stack_id_fkey" for relation "lo_input_validations" already exists
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.DuplicateObject: constraint "lo_input_validations_input_item_id_input_stack_id_fkey" for relation "lo_input_validations" already exists

2025-05-12 06:42:24,142 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:24,143 - db_utils - ERROR - Database error: constraint "lo_input_validations_input_item_id_input_stack_id_fkey" for relation "lo_input_validations" already exists
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.DuplicateObject: constraint "lo_input_validations_input_item_id_input_stack_id_fkey" for relation "lo_input_validations" already exists

2025-05-12 06:42:24,148 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:24,155 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:24,156 - db_utils - ERROR - Database error: constraint "local_objectives_go_id_fkey" for relation "local_objectives" already exists
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.DuplicateObject: constraint "local_objectives_go_id_fkey" for relation "local_objectives" already exists

2025-05-12 06:42:24,161 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:24,168 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:24,176 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:24,182 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:24,189 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:24,195 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:24,196 - db_utils - ERROR - Database error: constraint "lo_input_stack_lo_id_fkey" for relation "lo_input_stack" already exists
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.DuplicateObject: constraint "lo_input_stack_lo_id_fkey" for relation "lo_input_stack" already exists

2025-05-12 06:42:24,200 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:24,208 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:24,215 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:24,215 - db_utils - ERROR - Database error: constraint "lo_output_stack_lo_id_fkey" for relation "lo_output_stack" already exists
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.DuplicateObject: constraint "lo_output_stack_lo_id_fkey" for relation "lo_output_stack" already exists

2025-05-12 06:42:24,221 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:24,226 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:24,234 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:24,242 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:24,243 - db_utils - ERROR - Database error: constraint "lo_data_mappings_mapping_stack_id_fkey" for relation "lo_data_mappings" already exists
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.DuplicateObject: constraint "lo_data_mappings_mapping_stack_id_fkey" for relation "lo_data_mappings" already exists

2025-05-12 06:42:24,247 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:24,254 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:24,255 - db_utils - ERROR - Database error: constraint "lo_output_triggers_output_item_id_output_stack_id_fkey" for relation "lo_output_triggers" already exists
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.DuplicateObject: constraint "lo_output_triggers_output_item_id_output_stack_id_fkey" for relation "lo_output_triggers" already exists

2025-05-12 06:42:24,261 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:24,262 - db_utils - ERROR - Database error: constraint "lo_output_triggers_output_item_id_output_stack_id_fkey" for relation "lo_output_triggers" already exists
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.DuplicateObject: constraint "lo_output_triggers_output_item_id_output_stack_id_fkey" for relation "lo_output_triggers" already exists

2025-05-12 06:42:24,265 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:24,266 - db_utils - ERROR - Database error: constraint "lo_output_triggers_output_item_id_output_stack_id_fkey" for relation "lo_output_triggers" already exists
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.DuplicateObject: constraint "lo_output_triggers_output_item_id_output_stack_id_fkey" for relation "lo_output_triggers" already exists

2025-05-12 06:42:24,271 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:24,278 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:24,279 - db_utils - ERROR - Database error: constraint "input_data_sources_input_item_id_input_stack_id_fkey" for relation "input_data_sources" already exists
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.DuplicateObject: constraint "input_data_sources_input_item_id_input_stack_id_fkey" for relation "input_data_sources" already exists

2025-05-12 06:42:24,283 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:24,283 - db_utils - ERROR - Database error: constraint "input_data_sources_input_item_id_input_stack_id_fkey" for relation "input_data_sources" already exists
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.DuplicateObject: constraint "input_data_sources_input_item_id_input_stack_id_fkey" for relation "input_data_sources" already exists

2025-05-12 06:42:24,289 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:24,289 - db_utils - ERROR - Database error: constraint "input_data_sources_input_item_id_input_stack_id_fkey" for relation "input_data_sources" already exists
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.DuplicateObject: constraint "input_data_sources_input_item_id_input_stack_id_fkey" for relation "input_data_sources" already exists

2025-05-12 06:42:24,295 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:24,300 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:24,307 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:24,314 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:24,320 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:24,327 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:24,328 - db_utils - ERROR - Database error: constraint "dropdown_data_sources_input_item_id_input_stack_id_fkey" for relation "dropdown_data_sources" already exists
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.DuplicateObject: constraint "dropdown_data_sources_input_item_id_input_stack_id_fkey" for relation "dropdown_data_sources" already exists

2025-05-12 06:42:24,333 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:24,334 - db_utils - ERROR - Database error: constraint "dropdown_data_sources_input_item_id_input_stack_id_fkey" for relation "dropdown_data_sources" already exists
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.DuplicateObject: constraint "dropdown_data_sources_input_item_id_input_stack_id_fkey" for relation "dropdown_data_sources" already exists

2025-05-12 06:42:24,338 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:24,338 - db_utils - ERROR - Database error: constraint "dropdown_data_sources_input_item_id_input_stack_id_fkey" for relation "dropdown_data_sources" already exists
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.DuplicateObject: constraint "dropdown_data_sources_input_item_id_input_stack_id_fkey" for relation "dropdown_data_sources" already exists

2025-05-12 06:42:24,344 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:24,346 - component_deployer - INFO - Successfully analyzed workflow_runtime schema and created necessary tables
2025-05-12 06:42:24,346 - entity_example_detailed - INFO - Deploying entity to schema workflow_temp
2025-05-12 06:42:24,346 - component_deployer - INFO - Deploying entities from prescriptive text
2025-05-12 06:42:24,346 - prescriptive_parser - INFO - Initializing PrescriptiveParser
2025-05-12 06:42:24,346 - prescriptive_parser - INFO - Parsing entities prescriptive text
2025-05-12 06:42:24,346 - entity_parser - INFO - Starting to parse entity definitions
2025-05-12 06:42:24,347 - entity_parser - INFO - Parsing entity: Employee
2025-05-12 06:42:24,347 - entity_parser - INFO - Successfully parsed entity 'Employee'
2025-05-12 06:42:24,347 - entity_parser - INFO - Parsing entity: Employee
2025-05-12 06:42:24,347 - entity_parser - INFO - Successfully parsed entity 'Employee'
2025-05-12 06:42:24,347 - entity_parser - INFO - Parsing entity: Department
2025-05-12 06:42:24,347 - entity_parser - INFO - Successfully parsed entity 'Department'
2025-05-12 06:42:24,347 - entity_parser - INFO - Successfully parsed 2 entities
2025-05-12 06:42:24,353 - component_deployer - INFO - Deploying component of type: entities
2025-05-12 06:42:24,361 - component_deployer - INFO - Using schema: workflow_temp
2025-05-12 06:42:24,361 - entity_deployer - INFO - Deploying entities to workflow_temp
2025-05-12 06:42:24,366 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:24,372 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:24,381 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:24,390 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:24,400 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:24,401 - entity_deployer - INFO - Inserted entity 'Department' into workflow_temp.entities
2025-05-12 06:42:24,407 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:24,413 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:24,422 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:24,431 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:24,441 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:24,442 - entity_deployer - INFO - Inserted attribute 'budget' for entity 'entity_department' into workflow_temp.entity_attributes
2025-05-12 06:42:24,448 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:24,450 - entity_deployer - INFO - Inserted attribute 'departmentId' for entity 'entity_department' into workflow_temp.entity_attributes
2025-05-12 06:42:24,455 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:24,457 - entity_deployer - INFO - Inserted attribute 'location' for entity 'entity_department' into workflow_temp.entity_attributes
2025-05-12 06:42:24,461 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:24,463 - entity_deployer - INFO - Inserted attribute 'managerId' for entity 'entity_department' into workflow_temp.entity_attributes
2025-05-12 06:42:24,467 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:24,470 - entity_deployer - INFO - Inserted attribute 'name' for entity 'entity_department' into workflow_temp.entity_attributes
2025-05-12 06:42:24,475 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:24,481 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:24,486 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:24,487 - entity_deployer - WARNING - Warning: Relationship 'one-to-many_Employee' in entity 'entity_department' references non-existent entity 'entity_employee'
2025-05-12 06:42:24,492 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:24,494 - entity_deployer - WARNING - Warning: Relationship 'one-to-one_Employee' in entity 'entity_department' references non-existent entity 'entity_employee'
2025-05-12 06:42:24,498 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:24,505 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:24,510 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:24,511 - entity_deployer - INFO - Inserted business rule 'DEPT001' for entity 'entity_department' into workflow_temp.entity_business_rules
2025-05-12 06:42:24,516 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:24,522 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:24,530 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:24,539 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:24,548 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:24,549 - entity_deployer - INFO - Inserted entity 'Employee' into workflow_temp.entities
2025-05-12 06:42:24,554 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:24,560 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:24,569 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:24,578 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:24,588 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:24,589 - entity_deployer - INFO - Inserted attribute 'employeeId' for entity 'entity_employee' into workflow_temp.entity_attributes
2025-05-12 06:42:24,594 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:24,596 - entity_deployer - INFO - Inserted attribute 'firstName' for entity 'entity_employee' into workflow_temp.entity_attributes
2025-05-12 06:42:24,601 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:24,602 - entity_deployer - INFO - Inserted attribute 'fullName[derived]' for entity 'entity_employee' into workflow_temp.entity_attributes
2025-05-12 06:42:24,606 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:24,607 - entity_deployer - INFO - Inserted attribute 'lastName' for entity 'entity_employee' into workflow_temp.entity_attributes
2025-05-12 06:42:24,619 - entity_deployer - INFO - Entity deployment completed successfully
2025-05-12 06:42:24,619 - component_deployer - INFO - Component deployment succeeded
2025-05-12 06:42:24,619 - entity_example_detailed - INFO - Deploy messages:
2025-05-12 06:42:24,619 - entity_example_detailed - INFO -   Inserted entity 'Department' into workflow_temp.entities
2025-05-12 06:42:24,619 - entity_example_detailed - INFO -   Inserted attribute 'budget' for entity 'entity_department' into workflow_temp.entity_attributes
2025-05-12 06:42:24,619 - entity_example_detailed - INFO -   Inserted attribute 'departmentId' for entity 'entity_department' into workflow_temp.entity_attributes
2025-05-12 06:42:24,619 - entity_example_detailed - INFO -   Inserted attribute 'location' for entity 'entity_department' into workflow_temp.entity_attributes
2025-05-12 06:42:24,619 - entity_example_detailed - INFO -   Inserted attribute 'managerId' for entity 'entity_department' into workflow_temp.entity_attributes
2025-05-12 06:42:24,619 - entity_example_detailed - INFO -   Inserted attribute 'name' for entity 'entity_department' into workflow_temp.entity_attributes
2025-05-12 06:42:24,619 - entity_example_detailed - INFO -   Warning: Relationship 'one-to-many_Employee' in entity 'entity_department' references non-existent entity 'entity_employee'
2025-05-12 06:42:24,619 - entity_example_detailed - INFO -   Warning: Relationship 'one-to-one_Employee' in entity 'entity_department' references non-existent entity 'entity_employee'
2025-05-12 06:42:24,619 - entity_example_detailed - INFO -   Inserted business rule 'DEPT001' for entity 'entity_department' into workflow_temp.entity_business_rules
2025-05-12 06:42:24,619 - entity_example_detailed - INFO -   Inserted entity 'Employee' into workflow_temp.entities
2025-05-12 06:42:24,619 - entity_example_detailed - INFO -   Inserted attribute 'employeeId' for entity 'entity_employee' into workflow_temp.entity_attributes
2025-05-12 06:42:24,619 - entity_example_detailed - INFO -   Inserted attribute 'firstName' for entity 'entity_employee' into workflow_temp.entity_attributes
2025-05-12 06:42:24,620 - entity_example_detailed - INFO -   Inserted attribute 'fullName[derived]' for entity 'entity_employee' into workflow_temp.entity_attributes
2025-05-12 06:42:24,620 - entity_example_detailed - INFO -   Inserted attribute 'lastName' for entity 'entity_employee' into workflow_temp.entity_attributes
2025-05-12 06:42:24,620 - entity_example_detailed - INFO -   Inserted document into components
2025-05-12 06:42:24,620 - entity_example_detailed - INFO - === Querying Deployed Entity ===
2025-05-12 06:42:24,620 - entity_example_detailed - INFO - Querying workflow_temp.entities table
2025-05-12 06:42:24,624 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:24,625 - entity_example_detailed - INFO - Entities:
2025-05-12 06:42:24,625 - entity_example_detailed - INFO -   ('entity_department', 'Department', '', {}, {})
2025-05-12 06:42:24,625 - entity_example_detailed - INFO -   ('entity_employee', 'Employee', '', {}, {})
2025-05-12 06:42:24,625 - entity_example_detailed - INFO - Querying workflow_temp.entity_attributes table
2025-05-12 06:42:24,629 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 06:42:24,630 - entity_example_detailed - INFO - Entity Attributes:
2025-05-12 06:42:24,630 - entity_example_detailed - INFO -   ('entity_department_attr_budget', 'entity_department', 'budget', 'string', False, None, False, None, [])
2025-05-12 06:42:24,630 - entity_example_detailed - INFO -   ('entity_department_attr_departmentid', 'entity_department', 'departmentId', 'string', True, None, False, None, [])
2025-05-12 06:42:24,630 - entity_example_detailed - INFO -   ('entity_department_attr_location', 'entity_department', 'location', 'string', False, None, False, None, [])
2025-05-12 06:42:24,630 - entity_example_detailed - INFO -   ('entity_department_attr_managerid', 'entity_department', 'managerId', 'string', False, None, False, None, [])
2025-05-12 06:42:24,630 - entity_example_detailed - INFO -   ('entity_department_attr_name', 'entity_department', 'name', 'string', False, None, False, None, [])
2025-05-12 06:42:24,630 - entity_example_detailed - INFO -   ('entity_employee_attr_employeeid', 'entity_employee', 'employeeId', 'string', False, None, False, None, [])
2025-05-12 06:42:24,630 - entity_example_detailed - INFO -   ('entity_employee_attr_firstname', 'entity_employee', 'firstName', 'string', False, None, False, None, [])
2025-05-12 06:42:24,630 - entity_example_detailed - INFO -   ('entity_employee_attr_fullname[derived]', 'entity_employee', 'fullName[derived]', 'string', False, None, False, None, [])
2025-05-12 06:42:24,630 - entity_example_detailed - INFO -   ('entity_employee_attr_lastname', 'entity_employee', 'lastName', 'string', False, None, False, None, [])
2025-05-12 06:42:24,630 - entity_example_detailed - INFO - === Summary ===
2025-05-12 06:42:24,630 - entity_example_detailed - INFO - Deployment successful: True
2025-05-12 08:00:59,011 - entity_example_detailed - INFO - === Parsing Entity ===
2025-05-12 08:00:59,012 - prescriptive_parser - INFO - Initializing PrescriptiveParser
2025-05-12 08:00:59,012 - entity_example_detailed - INFO - Parsing prescriptive text:
2025-05-12 08:00:59,012 - entity_example_detailed - INFO - Employee has employeeId^PK, firstName, lastName, email, phoneNumber, departmentId^FK, managerId^FK, hireDate, status (Active, Inactive, OnLeave), salary, performanceRating.

* Employee has many-to-one relationship with Department using Employee.departmentId to Department.departmentId^PK
* Employee has many-to-one relationship with Employee using Employee.managerId to Employee.employeeId^PK

* Employee.email must be unique
* Employee.email must match pattern "^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$"
* Employee.hireDate must be before current date
* Employee.salary must be greater than 0

BusinessRule EMP001 for Employee:
* Employee.status must be Active to receive performance reviews
* Employee.hireDate must be at least 90 days before Employee.performanceRating can be set

Employee has employeeId, firstName, lastName, fullName[derived].

CalculatedField CF001 for Employee.fullName:
* Formula: CONCAT(firstName, ' ', lastName)
* Logic Layer: Application
* Caching: Session
* Dependencies: Employee.firstName, Employee.lastName

* Archive Strategy for Employee:
  - Trigger: Event-based
  - Criteria: When Employee.status changes to 'Inactive' and remains so for 1 year
  - Retention: 7 years
  - Storage: Cold storage
  - Access Pattern: Read-only through HR archive portal
  - Restoration: Manual process requiring HR Director approval

* History Tracking for Employee:
  - Tracked Attributes: Employee.salary, Employee.departmentId, Employee.managerId, Employee.status
  - Tracking Method: Audit table
  - Granularity: Change level
  - Retention: 7 years
  - Access Control: HR Managers and Compliance Officers only

Department has departmentId^PK, name, managerId^FK, location, budget.

* Department has one-to-one relationship with Employee using Department.managerId to Employee.employeeId^PK
* Department has one-to-many relationship with Employee using Department.departmentId to Employee.departmentId^FK

* Department.name must be unique
* Department.budget must be greater than 0

BusinessRule DEPT001 for Department:
* Department.budget must be approved by Finance before changes
* Department.managerId must reference an Employee with status = 'Active'

* Archive Strategy for Department:
  - Trigger: Event-based
  - Criteria: When Department is marked as 'Dissolved'
  - Retention: 10 years
  - Storage: Cold storage
  - Access Pattern: Read-only through Admin portal
  - Restoration: Manual process requiring CEO approval

2025-05-12 08:00:59,012 - prescriptive_parser - INFO - Parsing entities prescriptive text
2025-05-12 08:00:59,012 - entity_parser - INFO - Starting to parse entity definitions
2025-05-12 08:00:59,012 - entity_parser - INFO - Parsing entity: Employee
2025-05-12 08:00:59,013 - entity_parser - INFO - Successfully parsed entity 'Employee'
2025-05-12 08:00:59,013 - entity_parser - INFO - Parsing entity: Employee
2025-05-12 08:00:59,013 - entity_parser - INFO - Successfully parsed entity 'Employee'
2025-05-12 08:00:59,013 - entity_parser - INFO - Parsing entity: Department
2025-05-12 08:00:59,013 - entity_parser - INFO - Successfully parsed entity 'Department'
2025-05-12 08:00:59,013 - entity_parser - INFO - Successfully parsed 2 entities
2025-05-12 08:00:59,013 - entity_example_detailed - INFO - Parsed entity data:
2025-05-12 08:00:59,013 - entity_example_detailed - INFO - Entity: Employee
2025-05-12 08:00:59,013 - entity_example_detailed - INFO -   Description: 
2025-05-12 08:00:59,013 - entity_example_detailed - INFO -   Attributes:
2025-05-12 08:00:59,013 - entity_example_detailed - INFO -     employeeId: {'type': 'string', 'required': False, 'primary_key': False, 'foreign_key': False}
2025-05-12 08:00:59,013 - entity_example_detailed - INFO -     firstName: {'type': 'string', 'required': False, 'primary_key': False, 'foreign_key': False}
2025-05-12 08:00:59,013 - entity_example_detailed - INFO -     lastName: {'type': 'string', 'required': False, 'primary_key': False, 'foreign_key': False}
2025-05-12 08:00:59,013 - entity_example_detailed - INFO -     fullName[derived]: {'type': 'string', 'required': False, 'primary_key': False, 'foreign_key': False}
2025-05-12 08:00:59,013 - entity_example_detailed - INFO -   Relationships:
2025-05-12 08:00:59,013 - entity_example_detailed - INFO -   Business Rules:
2025-05-12 08:00:59,013 - entity_example_detailed - INFO -   Calculated Fields:
2025-05-12 08:00:59,013 - entity_example_detailed - INFO -     CF001: {'attribute': 'fullName', 'formula': "CONCAT(firstName, ' ', lastName)", 'logic_layer': 'Application', 'caching': 'Session', 'dependencies': 'Employee.firstName, Employee.lastName', 'archive_strategy_for_employee': '', 'history_tracking_for_employee': ''}
2025-05-12 08:00:59,013 - entity_example_detailed - INFO -   Lifecycle Management:
2025-05-12 08:00:59,013 - entity_example_detailed - INFO - Entity: Department
2025-05-12 08:00:59,013 - entity_example_detailed - INFO -   Description: 
2025-05-12 08:00:59,013 - entity_example_detailed - INFO -   Attributes:
2025-05-12 08:00:59,013 - entity_example_detailed - INFO -     departmentId: {'type': 'string', 'required': True, 'primary_key': True, 'foreign_key': False}
2025-05-12 08:00:59,013 - entity_example_detailed - INFO -     name: {'type': 'string', 'required': False, 'primary_key': False, 'foreign_key': False}
2025-05-12 08:00:59,013 - entity_example_detailed - INFO -     managerId: {'type': 'string', 'required': False, 'primary_key': False, 'foreign_key': True}
2025-05-12 08:00:59,013 - entity_example_detailed - INFO -     location: {'type': 'string', 'required': False, 'primary_key': False, 'foreign_key': False}
2025-05-12 08:00:59,013 - entity_example_detailed - INFO -     budget: {'type': 'string', 'required': False, 'primary_key': False, 'foreign_key': False}
2025-05-12 08:00:59,013 - entity_example_detailed - INFO -   Relationships:
2025-05-12 08:00:59,013 - entity_example_detailed - INFO -     one-to-one_Employee: {'type': 'one-to-one', 'entity': 'Employee', 'source_attribute': 'managerId', 'target_attribute': 'employeeId'}
2025-05-12 08:00:59,013 - entity_example_detailed - INFO -     one-to-many_Employee: {'type': 'one-to-many', 'entity': 'Employee', 'source_attribute': 'departmentId', 'target_attribute': 'departmentId'}
2025-05-12 08:00:59,013 - entity_example_detailed - INFO -   Business Rules:
2025-05-12 08:00:59,013 - entity_example_detailed - INFO -     DEPT001: {'conditions': ['Department.budget must be approved by Finance before changes', "Department.managerId must reference an Employee with status = 'Active'", 'Archive Strategy for Department:']}
2025-05-12 08:00:59,013 - entity_example_detailed - INFO -   Calculated Fields:
2025-05-12 08:00:59,013 - entity_example_detailed - INFO -   Lifecycle Management:
2025-05-12 08:00:59,013 - entity_example_detailed - INFO - === Comparing with insert_generator.py ===
2025-05-12 08:00:59,013 - entity_example_detailed - INFO - Converted entity data to insert_generator.py format:
2025-05-12 08:00:59,014 - entity_example_detailed - INFO - {
  "entities": [
    {
      "id": "en_employee",
      "name": "Employee",
      "type": "standard",
      "version": "1.0",
      "status": "active",
      "attributes": [
        {
          "id": "at_employeeid",
          "name": "employeeId",
          "display_name": "employeeId",
          "datatype": "string",
          "required": false,
          "version": "1.0",
          "status": "active"
        },
        {
          "id": "at_firstname",
          "name": "firstName",
          "display_name": "firstName",
          "datatype": "string",
          "required": false,
          "version": "1.0",
          "status": "active"
        },
        {
          "id": "at_lastname",
          "name": "lastName",
          "display_name": "lastName",
          "datatype": "string",
          "required": false,
          "version": "1.0",
          "status": "active"
        },
        {
          "id": "at_fullname[derived]",
          "name": "fullName[derived]",
          "display_name": "fullName[derived]",
          "datatype": "string",
          "required": false,
          "version": "1.0",
          "status": "active"
        }
      ]
    },
    {
      "id": "en_department",
      "name": "Department",
      "type": "standard",
      "version": "1.0",
      "status": "active",
      "attributes": [
        {
          "id": "at_departmentid",
          "name": "departmentId",
          "display_name": "departmentId",
          "datatype": "string",
          "required": true,
          "version": "1.0",
          "status": "active"
        },
        {
          "id": "at_name",
          "name": "name",
          "display_name": "name",
          "datatype": "string",
          "required": false,
          "version": "1.0",
          "status": "active"
        },
        {
          "id": "at_managerid",
          "name": "managerId",
          "display_name": "managerId",
          "datatype": "string",
          "required": false,
          "version": "1.0",
          "status": "active"
        },
        {
          "id": "at_location",
          "name": "location",
          "display_name": "location",
          "datatype": "string",
          "required": false,
          "version": "1.0",
          "status": "active"
        },
        {
          "id": "at_budget",
          "name": "budget",
          "display_name": "budget",
          "datatype": "string",
          "required": false,
          "version": "1.0",
          "status": "active"
        }
      ],
      "relationships": [
        {
          "entity_id": "Employee",
          "type": "one-to-one",
          "through_attribute": "",
          "to_attribute": ""
        },
        {
          "entity_id": "Employee",
          "type": "one-to-many",
          "through_attribute": "",
          "to_attribute": ""
        }
      ]
    }
  ]
}
2025-05-12 08:00:59,014 - entity_example_detailed - INFO - [COMPARISON] Simulating insert_generator.py processing...
2025-05-12 08:00:59,014 - entity_example_detailed - INFO - [COMPARISON] Entity table inserts:
2025-05-12 08:00:59,014 - entity_example_detailed - INFO - [COMPARISON] SQL: 
        INSERT INTO entities (entity_id, name, type, version, status, created_at, updated_at)
        VALUES ('en_employee', 'Employee', 'standard', '1.0', 'active', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);
        
2025-05-12 08:00:59,014 - entity_example_detailed - INFO - [COMPARISON] SQL: 
        INSERT INTO entities (entity_id, name, type, version, status, created_at, updated_at)
        VALUES ('en_department', 'Department', 'standard', '1.0', 'active', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);
        
2025-05-12 08:00:59,014 - entity_example_detailed - INFO - [COMPARISON] Entity attribute inserts:
2025-05-12 08:00:59,014 - entity_example_detailed - INFO - [COMPARISON] SQL: 
            INSERT INTO entity_attributes (attribute_id, entity_id, name, display_name, datatype, version, status, required, created_at, updated_at)
            VALUES ('at_employeeid', 'en_employee', 'employeeId', 'employeeId', 'string', '1.0', 'active', False, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);
            
2025-05-12 08:00:59,014 - entity_example_detailed - INFO - [COMPARISON] SQL: 
            INSERT INTO entity_attributes (attribute_id, entity_id, name, display_name, datatype, version, status, required, created_at, updated_at)
            VALUES ('at_firstname', 'en_employee', 'firstName', 'firstName', 'string', '1.0', 'active', False, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);
            
2025-05-12 08:00:59,014 - entity_example_detailed - INFO - [COMPARISON] SQL: 
            INSERT INTO entity_attributes (attribute_id, entity_id, name, display_name, datatype, version, status, required, created_at, updated_at)
            VALUES ('at_lastname', 'en_employee', 'lastName', 'lastName', 'string', '1.0', 'active', False, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);
            
2025-05-12 08:00:59,014 - entity_example_detailed - INFO - [COMPARISON] SQL: 
            INSERT INTO entity_attributes (attribute_id, entity_id, name, display_name, datatype, version, status, required, created_at, updated_at)
            VALUES ('at_fullname[derived]', 'en_employee', 'fullName[derived]', 'fullName[derived]', 'string', '1.0', 'active', False, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);
            
2025-05-12 08:00:59,014 - entity_example_detailed - INFO - [COMPARISON] SQL: 
            INSERT INTO entity_attributes (attribute_id, entity_id, name, display_name, datatype, version, status, required, created_at, updated_at)
            VALUES ('at_departmentid', 'en_department', 'departmentId', 'departmentId', 'string', '1.0', 'active', True, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);
            
2025-05-12 08:00:59,014 - entity_example_detailed - INFO - [COMPARISON] SQL: 
            INSERT INTO entity_attributes (attribute_id, entity_id, name, display_name, datatype, version, status, required, created_at, updated_at)
            VALUES ('at_name', 'en_department', 'name', 'name', 'string', '1.0', 'active', False, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);
            
2025-05-12 08:00:59,014 - entity_example_detailed - INFO - [COMPARISON] SQL: 
            INSERT INTO entity_attributes (attribute_id, entity_id, name, display_name, datatype, version, status, required, created_at, updated_at)
            VALUES ('at_managerid', 'en_department', 'managerId', 'managerId', 'string', '1.0', 'active', False, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);
            
2025-05-12 08:00:59,014 - entity_example_detailed - INFO - [COMPARISON] SQL: 
            INSERT INTO entity_attributes (attribute_id, entity_id, name, display_name, datatype, version, status, required, created_at, updated_at)
            VALUES ('at_location', 'en_department', 'location', 'location', 'string', '1.0', 'active', False, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);
            
2025-05-12 08:00:59,014 - entity_example_detailed - INFO - [COMPARISON] SQL: 
            INSERT INTO entity_attributes (attribute_id, entity_id, name, display_name, datatype, version, status, required, created_at, updated_at)
            VALUES ('at_budget', 'en_department', 'budget', 'budget', 'string', '1.0', 'active', False, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);
            
2025-05-12 08:00:59,014 - entity_example_detailed - INFO - [COMPARISON] Entity relationship inserts:
2025-05-12 08:00:59,014 - entity_example_detailed - INFO - [COMPARISON] SQL: 
                INSERT INTO entity_relationships (source_entity_id, target_entity_id, relationship_type, source_attribute_id, target_attribute_id, created_at, updated_at)
                VALUES ('en_department', 'Employee', 'one-to-one', '', '', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);
                
2025-05-12 08:00:59,014 - entity_example_detailed - INFO - [COMPARISON] SQL: 
                INSERT INTO entity_relationships (source_entity_id, target_entity_id, relationship_type, source_attribute_id, target_attribute_id, created_at, updated_at)
                VALUES ('en_department', 'Employee', 'one-to-many', '', '', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);
                
2025-05-12 08:00:59,014 - entity_example_detailed - INFO - [COMPARISON] Comparing with v2 deployer:
2025-05-12 08:00:59,014 - entity_example_detailed - INFO - [COMPARISON] 1. v2 deployer uses prescriptive_parser.py to parse prescriptive text into structured data
2025-05-12 08:00:59,014 - entity_example_detailed - INFO - [COMPARISON] 2. v2 deployer uses component_deployer.py to deploy the structured data to the database
2025-05-12 08:00:59,014 - entity_example_detailed - INFO - [COMPARISON] 3. v2 deployer uses entity_deployer.py to deploy entities to the database
2025-05-12 08:00:59,014 - entity_example_detailed - INFO - [COMPARISON] Key differences:
2025-05-12 08:00:59,014 - entity_example_detailed - INFO - [COMPARISON] 1. insert_generator.py uses a JSON file as input, while v2 uses prescriptive text
2025-05-12 08:00:59,014 - entity_example_detailed - INFO - [COMPARISON] 2. insert_generator.py generates SQL statements, while v2 executes them directly
2025-05-12 08:00:59,014 - entity_example_detailed - INFO - [COMPARISON] 3. insert_generator.py handles multiple components at once, while v2 can handle them separately
2025-05-12 08:00:59,014 - entity_example_detailed - INFO - [COMPARISON] 4. v2 has more validation and error handling
2025-05-12 08:00:59,014 - entity_example_detailed - INFO - [COMPARISON] 5. v2 uses a temporary schema for validation before promoting to runtime schema
2025-05-12 08:00:59,014 - entity_example_detailed - INFO - === Deploying Entity ===
2025-05-12 08:00:59,014 - component_deployer - INFO - ComponentDeployer initialized with use_temp_schema=True
2025-05-12 08:00:59,020 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:00:59,064 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:00:59,069 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:00:59,078 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:00:59,088 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:00:59,096 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:00:59,104 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:00:59,112 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:00:59,121 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:00:59,131 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:00:59,141 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:00:59,150 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:00:59,160 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:00:59,168 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:00:59,176 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:00:59,184 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:00:59,193 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:00:59,194 - component_deployer - INFO - Analyzing workflow_runtime schema to create necessary tables in temporary schema
2025-05-12 08:00:59,200 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:00:59,207 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:00:59,213 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:00:59,220 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:00:59,230 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:00:59,241 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:00:59,250 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:00:59,261 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:00:59,272 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:00:59,280 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:00:59,292 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:00:59,302 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:00:59,310 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:00:59,320 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:00:59,329 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:00:59,339 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:00:59,351 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:00:59,362 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:00:59,369 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:00:59,381 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:00:59,391 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:00:59,400 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:00:59,410 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:00:59,419 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:00:59,428 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:00:59,438 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:00:59,446 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:00:59,453 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:00:59,464 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:00:59,474 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:00:59,484 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:00:59,496 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:00:59,506 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:00:59,513 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:00:59,525 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:00:59,535 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:00:59,545 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:00:59,555 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:00:59,565 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:00:59,573 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:00:59,583 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:00:59,593 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:00:59,599 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:00:59,610 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:00:59,620 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:00:59,629 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:00:59,638 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:00:59,648 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:00:59,655 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:00:59,665 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:00:59,675 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:00:59,683 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:00:59,694 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:00:59,703 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:00:59,710 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:00:59,721 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:00:59,731 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:00:59,738 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:00:59,749 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:00:59,759 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:00:59,767 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:00:59,777 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:00:59,787 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:00:59,797 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:00:59,808 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:00:59,819 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:00:59,829 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:00:59,839 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:00:59,850 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:00:59,860 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:00:59,872 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:00:59,883 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:00:59,891 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:00:59,900 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:00:59,910 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:00:59,924 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:00:59,935 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:00:59,945 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:00:59,957 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:00:59,969 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:00:59,979 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:00:59,988 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:01:00,000 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:01:00,009 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:01:00,021 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:01:00,032 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:01:00,040 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:01:00,066 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:01:00,076 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:01:00,086 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:01:00,096 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:01:00,107 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:01:00,117 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:01:00,126 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:01:00,138 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:01:00,149 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:01:00,156 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:01:00,166 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:01:00,174 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:01:00,184 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:01:00,193 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:01:00,201 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:01:00,207 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:01:00,219 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:01:00,228 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:01:00,237 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:01:00,248 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:01:00,259 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:01:00,268 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:01:00,278 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:01:00,285 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:01:00,292 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:01:00,303 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:01:00,313 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:01:00,321 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:01:00,332 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:01:00,343 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:01:00,352 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:01:00,363 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:01:00,373 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:01:00,382 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:01:00,394 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:01:00,403 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:01:00,411 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:01:00,423 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:01:00,433 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:01:00,440 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:01:00,451 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:01:00,461 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:01:00,470 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:01:00,481 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:01:00,491 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:01:00,500 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:01:00,511 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:01:00,521 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:01:00,529 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:01:00,541 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:01:00,551 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:01:00,561 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:01:00,570 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:01:00,581 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:01:00,588 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:01:00,599 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:01:00,609 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:01:00,617 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:01:00,628 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:01:00,638 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:01:00,647 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:01:00,657 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:01:00,669 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:01:00,677 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:01:00,689 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:01:00,698 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:01:00,707 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:01:00,718 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:01:00,727 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:01:00,733 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:01:00,744 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:01:00,753 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:01:00,759 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:01:00,770 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:01:00,779 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:01:00,787 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:01:00,799 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:01:00,808 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:01:00,815 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:01:00,823 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:01:00,832 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:01:00,839 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:01:00,849 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:01:00,859 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:01:00,867 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:01:00,878 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:01:00,889 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:01:00,897 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:01:00,906 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:01:00,915 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:01:00,923 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:01:00,934 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:01:00,943 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:01:00,952 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:01:00,963 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:01:00,973 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:01:00,981 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:01:00,992 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:01:01,002 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:01:01,011 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:01:01,022 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:01:01,032 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:01:01,042 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:01:01,054 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:01:01,065 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:01:01,074 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:01:01,085 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:01:01,096 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:01:01,105 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:01:01,117 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:01:01,126 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:01:01,133 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:01:01,142 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:01:01,153 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:01:01,160 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:01:01,171 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:01:01,180 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:01:01,187 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:01:01,198 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:01:01,207 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:01:01,216 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:01:01,296 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:01:01,305 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:01:01,313 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:01:01,321 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:01:01,330 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:01:01,337 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:01:01,345 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:01:01,351 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:01:01,358 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:01:01,364 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:01:01,371 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:01:01,376 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:01:01,384 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:01:01,390 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:01:01,397 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:01:01,403 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:01:01,404 - db_utils - ERROR - Database error: constraint "entity_attributes_entity_id_fkey" for relation "entity_attributes" already exists
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.DuplicateObject: constraint "entity_attributes_entity_id_fkey" for relation "entity_attributes" already exists

2025-05-12 08:01:01,408 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:01:01,413 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:01:01,420 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:01:01,420 - db_utils - ERROR - Database error: constraint "lo_input_items_input_stack_id_fkey" for relation "lo_input_items" already exists
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.DuplicateObject: constraint "lo_input_items_input_stack_id_fkey" for relation "lo_input_items" already exists

2025-05-12 08:01:01,426 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:01:01,427 - db_utils - ERROR - Database error: constraint "lo_input_items_lo_id_fkey" for relation "lo_input_items" already exists
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.DuplicateObject: constraint "lo_input_items_lo_id_fkey" for relation "lo_input_items" already exists

2025-05-12 08:01:01,431 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:01:01,438 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:01:01,439 - db_utils - ERROR - Database error: there is no unique constraint matching given keys for referenced table "roles"
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.InvalidForeignKey: there is no unique constraint matching given keys for referenced table "roles"

2025-05-12 08:01:01,443 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:01:01,450 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:01:01,451 - db_utils - ERROR - Database error: constraint "lo_nested_functions_lo_id_fkey" for relation "lo_nested_functions" already exists
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.DuplicateObject: constraint "lo_nested_functions_lo_id_fkey" for relation "lo_nested_functions" already exists

2025-05-12 08:01:01,456 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:01:01,457 - db_utils - ERROR - Database error: constraint "lo_output_items_lo_id_fkey" for relation "lo_output_items" already exists
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.DuplicateObject: constraint "lo_output_items_lo_id_fkey" for relation "lo_output_items" already exists

2025-05-12 08:01:01,462 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:01:01,463 - db_utils - ERROR - Database error: constraint "lo_output_items_output_stack_id_fkey" for relation "lo_output_items" already exists
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.DuplicateObject: constraint "lo_output_items_output_stack_id_fkey" for relation "lo_output_items" already exists

2025-05-12 08:01:01,469 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:01:01,477 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:01:01,484 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:01:01,485 - db_utils - ERROR - Database error: constraint "input_dependencies_depends_on_id_depends_on_stack_id_fkey" for relation "input_dependencies" already exists
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.DuplicateObject: constraint "input_dependencies_depends_on_id_depends_on_stack_id_fkey" for relation "input_dependencies" already exists

2025-05-12 08:01:01,491 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:01:01,492 - db_utils - ERROR - Database error: constraint "input_dependencies_depends_on_id_depends_on_stack_id_fkey" for relation "input_dependencies" already exists
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.DuplicateObject: constraint "input_dependencies_depends_on_id_depends_on_stack_id_fkey" for relation "input_dependencies" already exists

2025-05-12 08:01:01,497 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:01:01,498 - db_utils - ERROR - Database error: constraint "input_dependencies_depends_on_id_depends_on_stack_id_fkey" for relation "input_dependencies" already exists
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.DuplicateObject: constraint "input_dependencies_depends_on_id_depends_on_stack_id_fkey" for relation "input_dependencies" already exists

2025-05-12 08:01:01,503 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:01:01,510 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:01:01,511 - db_utils - ERROR - Database error: constraint "input_dependencies_input_item_id_input_stack_id_fkey" for relation "input_dependencies" already exists
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.DuplicateObject: constraint "input_dependencies_input_item_id_input_stack_id_fkey" for relation "input_dependencies" already exists

2025-05-12 08:01:01,515 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:01:01,516 - db_utils - ERROR - Database error: constraint "input_dependencies_input_item_id_input_stack_id_fkey" for relation "input_dependencies" already exists
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.DuplicateObject: constraint "input_dependencies_input_item_id_input_stack_id_fkey" for relation "input_dependencies" already exists

2025-05-12 08:01:01,521 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:01:01,522 - db_utils - ERROR - Database error: constraint "input_dependencies_input_item_id_input_stack_id_fkey" for relation "input_dependencies" already exists
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.DuplicateObject: constraint "input_dependencies_input_item_id_input_stack_id_fkey" for relation "input_dependencies" already exists

2025-05-12 08:01:01,527 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:01:01,534 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:01:01,541 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:01:01,549 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:01:01,556 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:01:01,556 - db_utils - ERROR - Database error: constraint "lo_data_mapping_stack_lo_id_fkey" for relation "lo_data_mapping_stack" already exists
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.DuplicateObject: constraint "lo_data_mapping_stack_lo_id_fkey" for relation "lo_data_mapping_stack" already exists

2025-05-12 08:01:01,562 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:01:01,569 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:01:01,575 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:01:01,583 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:01:01,589 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:01:01,590 - db_utils - ERROR - Database error: column "inherits_from" referenced in foreign key constraint does not exist
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.UndefinedColumn: column "inherits_from" referenced in foreign key constraint does not exist

2025-05-12 08:01:01,594 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:01:01,594 - db_utils - ERROR - Database error: column "tenant_id" referenced in foreign key constraint does not exist
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.UndefinedColumn: column "tenant_id" referenced in foreign key constraint does not exist

2025-05-12 08:01:01,600 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:01:01,606 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:01:01,612 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:01:01,619 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:01:01,625 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:01:01,626 - db_utils - ERROR - Database error: there is no unique constraint matching given keys for referenced table "output_items"
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.InvalidForeignKey: there is no unique constraint matching given keys for referenced table "output_items"

2025-05-12 08:01:01,630 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:01:01,630 - db_utils - ERROR - Database error: there is no unique constraint matching given keys for referenced table "output_items"
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.InvalidForeignKey: there is no unique constraint matching given keys for referenced table "output_items"

2025-05-12 08:01:01,636 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:01:01,637 - db_utils - ERROR - Database error: there is no unique constraint matching given keys for referenced table "output_items"
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.InvalidForeignKey: there is no unique constraint matching given keys for referenced table "output_items"

2025-05-12 08:01:01,641 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:01:01,642 - db_utils - ERROR - Database error: there is no unique constraint matching given keys for referenced table "output_items"
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.InvalidForeignKey: there is no unique constraint matching given keys for referenced table "output_items"

2025-05-12 08:01:01,646 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:01:01,654 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:01:01,660 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:01:01,666 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:01:01,672 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:01:01,679 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:01:01,685 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:01:01,685 - db_utils - ERROR - Database error: constraint "lo_input_validations_input_item_id_input_stack_id_fkey" for relation "lo_input_validations" already exists
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.DuplicateObject: constraint "lo_input_validations_input_item_id_input_stack_id_fkey" for relation "lo_input_validations" already exists

2025-05-12 08:01:01,689 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:01:01,690 - db_utils - ERROR - Database error: constraint "lo_input_validations_input_item_id_input_stack_id_fkey" for relation "lo_input_validations" already exists
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.DuplicateObject: constraint "lo_input_validations_input_item_id_input_stack_id_fkey" for relation "lo_input_validations" already exists

2025-05-12 08:01:01,695 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:01:01,696 - db_utils - ERROR - Database error: constraint "lo_input_validations_input_item_id_input_stack_id_fkey" for relation "lo_input_validations" already exists
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.DuplicateObject: constraint "lo_input_validations_input_item_id_input_stack_id_fkey" for relation "lo_input_validations" already exists

2025-05-12 08:01:01,700 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:01:01,705 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:01:01,706 - db_utils - ERROR - Database error: constraint "local_objectives_go_id_fkey" for relation "local_objectives" already exists
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.DuplicateObject: constraint "local_objectives_go_id_fkey" for relation "local_objectives" already exists

2025-05-12 08:01:01,711 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:01:01,717 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:01:01,723 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:01:01,729 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:01:01,735 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:01:01,740 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:01:01,741 - db_utils - ERROR - Database error: constraint "lo_input_stack_lo_id_fkey" for relation "lo_input_stack" already exists
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.DuplicateObject: constraint "lo_input_stack_lo_id_fkey" for relation "lo_input_stack" already exists

2025-05-12 08:01:01,746 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:01:01,751 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:01:01,757 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:01:01,757 - db_utils - ERROR - Database error: constraint "lo_output_stack_lo_id_fkey" for relation "lo_output_stack" already exists
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.DuplicateObject: constraint "lo_output_stack_lo_id_fkey" for relation "lo_output_stack" already exists

2025-05-12 08:01:01,761 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:01:01,767 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:01:01,772 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:01:01,778 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:01:01,779 - db_utils - ERROR - Database error: constraint "lo_data_mappings_mapping_stack_id_fkey" for relation "lo_data_mappings" already exists
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.DuplicateObject: constraint "lo_data_mappings_mapping_stack_id_fkey" for relation "lo_data_mappings" already exists

2025-05-12 08:01:01,783 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:01:01,788 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:01:01,789 - db_utils - ERROR - Database error: constraint "lo_output_triggers_output_item_id_output_stack_id_fkey" for relation "lo_output_triggers" already exists
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.DuplicateObject: constraint "lo_output_triggers_output_item_id_output_stack_id_fkey" for relation "lo_output_triggers" already exists

2025-05-12 08:01:01,792 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:01:01,793 - db_utils - ERROR - Database error: constraint "lo_output_triggers_output_item_id_output_stack_id_fkey" for relation "lo_output_triggers" already exists
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.DuplicateObject: constraint "lo_output_triggers_output_item_id_output_stack_id_fkey" for relation "lo_output_triggers" already exists

2025-05-12 08:01:01,797 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:01:01,798 - db_utils - ERROR - Database error: constraint "lo_output_triggers_output_item_id_output_stack_id_fkey" for relation "lo_output_triggers" already exists
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.DuplicateObject: constraint "lo_output_triggers_output_item_id_output_stack_id_fkey" for relation "lo_output_triggers" already exists

2025-05-12 08:01:01,803 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:01:01,811 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:01:01,812 - db_utils - ERROR - Database error: constraint "input_data_sources_input_item_id_input_stack_id_fkey" for relation "input_data_sources" already exists
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.DuplicateObject: constraint "input_data_sources_input_item_id_input_stack_id_fkey" for relation "input_data_sources" already exists

2025-05-12 08:01:01,816 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:01:01,817 - db_utils - ERROR - Database error: constraint "input_data_sources_input_item_id_input_stack_id_fkey" for relation "input_data_sources" already exists
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.DuplicateObject: constraint "input_data_sources_input_item_id_input_stack_id_fkey" for relation "input_data_sources" already exists

2025-05-12 08:01:01,821 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:01:01,822 - db_utils - ERROR - Database error: constraint "input_data_sources_input_item_id_input_stack_id_fkey" for relation "input_data_sources" already exists
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.DuplicateObject: constraint "input_data_sources_input_item_id_input_stack_id_fkey" for relation "input_data_sources" already exists

2025-05-12 08:01:01,827 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:01:01,833 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:01:01,839 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:01:01,847 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:01:01,853 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:01:01,859 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:01:01,859 - db_utils - ERROR - Database error: constraint "dropdown_data_sources_input_item_id_input_stack_id_fkey" for relation "dropdown_data_sources" already exists
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.DuplicateObject: constraint "dropdown_data_sources_input_item_id_input_stack_id_fkey" for relation "dropdown_data_sources" already exists

2025-05-12 08:01:01,864 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:01:01,865 - db_utils - ERROR - Database error: constraint "dropdown_data_sources_input_item_id_input_stack_id_fkey" for relation "dropdown_data_sources" already exists
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.DuplicateObject: constraint "dropdown_data_sources_input_item_id_input_stack_id_fkey" for relation "dropdown_data_sources" already exists

2025-05-12 08:01:01,869 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:01:01,869 - db_utils - ERROR - Database error: constraint "dropdown_data_sources_input_item_id_input_stack_id_fkey" for relation "dropdown_data_sources" already exists
Traceback (most recent call last):
  File "/home/<USER>/workflow-system/chat-yaml-builder/v2/db_utils.py", line 56, in execute_query
    cursor.execute(query)
psycopg2.errors.DuplicateObject: constraint "dropdown_data_sources_input_item_id_input_stack_id_fkey" for relation "dropdown_data_sources" already exists

2025-05-12 08:01:01,874 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:01:01,876 - component_deployer - INFO - Successfully analyzed workflow_runtime schema and created necessary tables
2025-05-12 08:01:01,876 - entity_example_detailed - INFO - Deploying entity to schema workflow_temp
2025-05-12 08:01:01,876 - component_deployer - INFO - Deploying entities from prescriptive text
2025-05-12 08:01:01,877 - prescriptive_parser - INFO - Initializing PrescriptiveParser
2025-05-12 08:01:01,877 - prescriptive_parser - INFO - Parsing entities prescriptive text
2025-05-12 08:01:01,877 - entity_parser - INFO - Starting to parse entity definitions
2025-05-12 08:01:01,877 - entity_parser - INFO - Parsing entity: Employee
2025-05-12 08:01:01,877 - entity_parser - INFO - Successfully parsed entity 'Employee'
2025-05-12 08:01:01,877 - entity_parser - INFO - Parsing entity: Employee
2025-05-12 08:01:01,877 - entity_parser - INFO - Successfully parsed entity 'Employee'
2025-05-12 08:01:01,877 - entity_parser - INFO - Parsing entity: Department
2025-05-12 08:01:01,877 - entity_parser - INFO - Successfully parsed entity 'Department'
2025-05-12 08:01:01,877 - entity_parser - INFO - Successfully parsed 2 entities
2025-05-12 08:01:01,882 - component_deployer - INFO - Deploying component of type: entities
2025-05-12 08:01:01,890 - component_deployer - INFO - Using schema: workflow_temp
2025-05-12 08:01:01,890 - entity_deployer - INFO - Deploying entities to workflow_temp
2025-05-12 08:01:01,895 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:01:01,902 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:01:01,911 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:01:01,921 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:01:01,931 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:01:01,932 - entity_deployer - INFO - Inserted entity 'Department' into workflow_temp.entities
2025-05-12 08:01:01,937 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:01:01,942 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:01:01,951 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:01:01,961 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:01:01,970 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:01:01,972 - entity_deployer - INFO - Inserted attribute 'budget' for entity 'entity_department' into workflow_temp.entity_attributes
2025-05-12 08:01:01,977 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:01:01,978 - entity_deployer - INFO - Inserted attribute 'departmentId' for entity 'entity_department' into workflow_temp.entity_attributes
2025-05-12 08:01:01,983 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:01:01,985 - entity_deployer - INFO - Inserted attribute 'location' for entity 'entity_department' into workflow_temp.entity_attributes
2025-05-12 08:01:01,988 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:01:01,990 - entity_deployer - INFO - Inserted attribute 'managerId' for entity 'entity_department' into workflow_temp.entity_attributes
2025-05-12 08:01:01,994 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:01:01,996 - entity_deployer - INFO - Inserted attribute 'name' for entity 'entity_department' into workflow_temp.entity_attributes
2025-05-12 08:01:01,999 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:01:02,008 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:01:02,016 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:01:02,017 - entity_deployer - WARNING - Warning: Relationship 'one-to-many_Employee' in entity 'entity_department' references non-existent entity 'entity_employee'
2025-05-12 08:01:02,021 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:01:02,023 - entity_deployer - WARNING - Warning: Relationship 'one-to-one_Employee' in entity 'entity_department' references non-existent entity 'entity_employee'
2025-05-12 08:01:02,026 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:01:02,033 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:01:02,038 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:01:02,039 - entity_deployer - INFO - Inserted business rule 'DEPT001' for entity 'entity_department' into workflow_temp.entity_business_rules
2025-05-12 08:01:02,042 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:01:02,047 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:01:02,056 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:01:02,065 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:01:02,074 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:01:02,075 - entity_deployer - INFO - Inserted entity 'Employee' into workflow_temp.entities
2025-05-12 08:01:02,079 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:01:02,084 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:01:02,092 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:01:02,103 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:01:02,111 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:01:02,113 - entity_deployer - INFO - Inserted attribute 'employeeId' for entity 'entity_employee' into workflow_temp.entity_attributes
2025-05-12 08:01:02,117 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:01:02,119 - entity_deployer - INFO - Inserted attribute 'firstName' for entity 'entity_employee' into workflow_temp.entity_attributes
2025-05-12 08:01:02,123 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:01:02,124 - entity_deployer - INFO - Inserted attribute 'fullName[derived]' for entity 'entity_employee' into workflow_temp.entity_attributes
2025-05-12 08:01:02,128 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:01:02,129 - entity_deployer - INFO - Inserted attribute 'lastName' for entity 'entity_employee' into workflow_temp.entity_attributes
2025-05-12 08:01:02,143 - entity_deployer - INFO - Entity deployment completed successfully
2025-05-12 08:01:02,143 - component_deployer - INFO - Component deployment succeeded
2025-05-12 08:01:02,143 - entity_example_detailed - INFO - Deploy messages:
2025-05-12 08:01:02,143 - entity_example_detailed - INFO -   Inserted entity 'Department' into workflow_temp.entities
2025-05-12 08:01:02,143 - entity_example_detailed - INFO -   Inserted attribute 'budget' for entity 'entity_department' into workflow_temp.entity_attributes
2025-05-12 08:01:02,143 - entity_example_detailed - INFO -   Inserted attribute 'departmentId' for entity 'entity_department' into workflow_temp.entity_attributes
2025-05-12 08:01:02,143 - entity_example_detailed - INFO -   Inserted attribute 'location' for entity 'entity_department' into workflow_temp.entity_attributes
2025-05-12 08:01:02,143 - entity_example_detailed - INFO -   Inserted attribute 'managerId' for entity 'entity_department' into workflow_temp.entity_attributes
2025-05-12 08:01:02,143 - entity_example_detailed - INFO -   Inserted attribute 'name' for entity 'entity_department' into workflow_temp.entity_attributes
2025-05-12 08:01:02,143 - entity_example_detailed - INFO -   Warning: Relationship 'one-to-many_Employee' in entity 'entity_department' references non-existent entity 'entity_employee'
2025-05-12 08:01:02,143 - entity_example_detailed - INFO -   Warning: Relationship 'one-to-one_Employee' in entity 'entity_department' references non-existent entity 'entity_employee'
2025-05-12 08:01:02,143 - entity_example_detailed - INFO -   Inserted business rule 'DEPT001' for entity 'entity_department' into workflow_temp.entity_business_rules
2025-05-12 08:01:02,143 - entity_example_detailed - INFO -   Inserted entity 'Employee' into workflow_temp.entities
2025-05-12 08:01:02,143 - entity_example_detailed - INFO -   Inserted attribute 'employeeId' for entity 'entity_employee' into workflow_temp.entity_attributes
2025-05-12 08:01:02,143 - entity_example_detailed - INFO -   Inserted attribute 'firstName' for entity 'entity_employee' into workflow_temp.entity_attributes
2025-05-12 08:01:02,143 - entity_example_detailed - INFO -   Inserted attribute 'fullName[derived]' for entity 'entity_employee' into workflow_temp.entity_attributes
2025-05-12 08:01:02,143 - entity_example_detailed - INFO -   Inserted attribute 'lastName' for entity 'entity_employee' into workflow_temp.entity_attributes
2025-05-12 08:01:02,143 - entity_example_detailed - INFO -   Inserted document into components
2025-05-12 08:01:02,143 - entity_example_detailed - INFO - === Querying Deployed Entity ===
2025-05-12 08:01:02,144 - entity_example_detailed - INFO - Querying workflow_temp.entities table
2025-05-12 08:01:02,148 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:01:02,149 - entity_example_detailed - INFO - Entities:
2025-05-12 08:01:02,149 - entity_example_detailed - INFO -   ('entity_department', 'Department', '', {}, {})
2025-05-12 08:01:02,149 - entity_example_detailed - INFO -   ('entity_employee', 'Employee', '', {}, {})
2025-05-12 08:01:02,149 - entity_example_detailed - INFO - Querying workflow_temp.entity_attributes table
2025-05-12 08:01:02,153 - db_connection - INFO - Connected to database workflow_system on **********:5432
2025-05-12 08:01:02,154 - entity_example_detailed - INFO - Entity Attributes:
2025-05-12 08:01:02,154 - entity_example_detailed - INFO -   ('entity_department_attr_budget', 'entity_department', 'budget', 'string', False, None, False, None, [])
2025-05-12 08:01:02,154 - entity_example_detailed - INFO -   ('entity_department_attr_departmentid', 'entity_department', 'departmentId', 'string', True, None, False, None, [])
2025-05-12 08:01:02,154 - entity_example_detailed - INFO -   ('entity_department_attr_location', 'entity_department', 'location', 'string', False, None, False, None, [])
2025-05-12 08:01:02,154 - entity_example_detailed - INFO -   ('entity_department_attr_managerid', 'entity_department', 'managerId', 'string', False, None, False, None, [])
2025-05-12 08:01:02,154 - entity_example_detailed - INFO -   ('entity_department_attr_name', 'entity_department', 'name', 'string', False, None, False, None, [])
2025-05-12 08:01:02,154 - entity_example_detailed - INFO -   ('entity_employee_attr_employeeid', 'entity_employee', 'employeeId', 'string', False, None, False, None, [])
2025-05-12 08:01:02,154 - entity_example_detailed - INFO -   ('entity_employee_attr_firstname', 'entity_employee', 'firstName', 'string', False, None, False, None, [])
2025-05-12 08:01:02,154 - entity_example_detailed - INFO -   ('entity_employee_attr_fullname[derived]', 'entity_employee', 'fullName[derived]', 'string', False, None, False, None, [])
2025-05-12 08:01:02,155 - entity_example_detailed - INFO -   ('entity_employee_attr_lastname', 'entity_employee', 'lastName', 'string', False, None, False, None, [])
2025-05-12 08:01:02,155 - entity_example_detailed - INFO - === Summary ===
2025-05-12 08:01:02,155 - entity_example_detailed - INFO - Deployment successful: True
