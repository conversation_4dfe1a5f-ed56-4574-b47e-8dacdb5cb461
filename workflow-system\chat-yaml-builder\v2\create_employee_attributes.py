#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to create Employee attributes in the database and set their default values.
"""

import os
import logging
from deployers.entity_deployer_v2 import execute_query

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger('create_employee_attributes')

def create_employee_attributes():
    """
    Create Employee attributes in the database and set their default values.
    """
    schema_name = 'workflow_temp'
    
    # Get the Employee entity ID from the database
    success, messages, result = execute_query(
        f"SELECT entity_id FROM {schema_name}.entities WHERE name = 'Employee'",
        schema_name=schema_name
    )
    
    if not success or not result:
        logger.error("Employee entity not found in the database")
        return
    
    employee_id = result[0][0]
    logger.info(f"Found Employee entity in database with ID: {employee_id}")
    
    # Create the attributes if they don't exist
    attributes = [
        {
            'name': 'status',
            'type': 'enum',
            'default_value': 'Active',
            'display_name': 'Status'
        },
        {
            'name': 'hireDate',
            'type': 'date',
            'default_value': 'CURRENT_DATE',
            'display_name': 'Hire Date'
        },
        {
            'name': 'probationDays',
            'type': 'integer',
            'default_value': '90',  # Using default_value to store property_value
            'display_name': 'Probation Days'
        },
        {
            'name': 'minSalary',
            'type': 'decimal',
            'default_value': '30000',  # Using default_value to store property_value
            'display_name': 'Minimum Salary'
        }
    ]
    
    for attr in attributes:
        # Check if attribute already exists
        success, messages, result = execute_query(
            f"""
            SELECT attribute_id FROM {schema_name}.entity_attributes 
            WHERE entity_id = %s AND name = %s
            """,
            (employee_id, attr['name']),
            schema_name
        )
        
        if success and result:
            # Attribute exists, update it
            attribute_id = result[0][0]
            logger.info(f"Attribute '{attr['name']}' already exists with ID {attribute_id}, updating it")
            
            success, messages, result = execute_query(
                f"""
                UPDATE {schema_name}.entity_attributes
                SET type = %s, default_value = %s, display_name = %s
                WHERE attribute_id = %s
                """,
                (attr['type'], attr['default_value'], attr['display_name'], attribute_id),
                schema_name
            )
            
            if success:
                logger.info(f"Updated attribute '{attr['name']}' with default value '{attr['default_value']}'")
            else:
                logger.error(f"Failed to update attribute '{attr['name']}': {messages}")
        else:
            # Attribute doesn't exist, create it
            # Generate new attribute ID
            success, messages, result = execute_query(
                f"""
                SELECT attribute_id FROM {schema_name}.entity_attributes
                WHERE entity_id = %s
                ORDER BY attribute_id DESC
                LIMIT 1
                """,
                (employee_id,),
                schema_name
            )
            
            attribute_id = None
            if success and result:
                # Extract numeric part and increment
                attr_id = result[0][0]
                if attr_id.startswith(f'{employee_id}.At'):
                    num = int(attr_id.split('At')[1]) + 1
                    attribute_id = f"{employee_id}.At{num}"
            
            if not attribute_id:
                # If no attributes exist for this entity, create a new one with entity_id prefix
                attribute_id = f"{employee_id}.At1"
            
            logger.info(f"Creating new attribute '{attr['name']}' with ID {attribute_id}")
            
            success, messages, result = execute_query(
                f"""
                INSERT INTO {schema_name}.entity_attributes (
                    attribute_id, entity_id, name, type, default_value, display_name,
                    created_at, updated_at, datatype, status
                ) VALUES (%s, %s, %s, %s, %s, %s, NOW(), NOW(), %s, 'Active')
                """,
                (attribute_id, employee_id, attr['name'], attr['type'], attr['default_value'], attr['display_name'], attr['type']),
                schema_name
            )
            
            if success:
                logger.info(f"Created attribute '{attr['name']}' with default value '{attr['default_value']}'")
            else:
                logger.error(f"Failed to create attribute '{attr['name']}': {messages}")
    
    # Verify the attributes were created/updated
    success, messages, result = execute_query(
        f"""
        SELECT 
            name, 
            type,
            default_value,
            display_name
        FROM 
            {schema_name}.entity_attributes
        WHERE 
            entity_id = %s AND
            name IN ('status', 'hireDate', 'probationDays', 'minSalary')
        """,
        (employee_id,),
        schema_name
    )
    
    if success and result:
        logger.info("\nEmployee attributes in the database:")
        for row in result:
            attr_name = row[0]
            attr_type = row[1]
            default_value = row[2]
            display_name = row[3]
            logger.info(f"  - Attribute: {attr_name}")
            logger.info(f"    - Type: {attr_type}")
            logger.info(f"    - Default Value: {default_value}")
            logger.info(f"    - Display Name: {display_name}")
    else:
        logger.error("No Employee attributes found in the database after creation/update")

if __name__ == "__main__":
    create_employee_attributes()
