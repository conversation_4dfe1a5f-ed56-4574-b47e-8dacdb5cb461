import unittest
from typing import Dict, Any
import sys
import os
import re

# Add project root to path if needed
project_root = '/home/<USER>/workflow-system/runtime/workflow-engine'
if project_root not in sys.path:
    sys.path.insert(0, project_root)

# Import the function to test
from app.services.system_functions import string_template

class TestStringTemplate(unittest.TestCase):
    def test_basic_template(self):
        """Test basic template with one variable."""
        template = "Hello, {{name}}!"
        data = {"name": "<PERSON>"}
        
        result = string_template(template, data)
        
        self.assertEqual(result, "Hello, <PERSON>!")
    
    def test_multiple_variables(self):
        """Test template with multiple variables."""
        template = "{{greeting}}, {{name}}! Welcome to {{place}}."
        data = {
            "greeting": "Hello",
            "name": "<PERSON>",
            "place": "New York"
        }
        
        result = string_template(template, data)
        
        self.assertEqual(result, "Hello, John! Welcome to New York.")
    
    def test_repeated_variables(self):
        """Test template with repeated variables."""
        template = "{{name}} is {{age}} years old. {{name}} lives in {{city}}."
        data = {
            "name": "<PERSON>",
            "age": 30,
            "city": "New York"
        }
        
        result = string_template(template, data)
        
        self.assertEqual(result, "<PERSON> is 30 years old. <PERSON> lives in New York.")
    
    def test_missing_variables(self):
        """Test template with missing variables."""
        template = "Hello, {{name}}! Your age is {{age}}."
        data = {"name": "John"}
        
        result = string_template(template, data)
        
        # Missing variables should remain as template placeholders
        self.assertEqual(result, "Hello, John! Your age is {{age}}.")
    
    def test_non_string_variables(self):
        """Test template with non-string variables."""
        template = "{{name}} is {{age}} years old and has ${{balance}} in the bank."
        data = {
            "name": "John",
            "age": 30,
            "balance": 1234.56
        }
        
        result = string_template(template, data)
        
        self.assertEqual(result, "John is 30 years old and has $1234.56 in the bank.")
    
    def test_boolean_variables(self):
        """Test template with boolean variables."""
        template = "User active: {{is_active}}"
        data = {"is_active": True}
        
        result = string_template(template, data)
        
        self.assertEqual(result, "User active: True")
    
    def test_none_variables(self):
        """Test template with None variables."""
        template = "Value: {{value}}"
        data = {"value": None}
        
        result = string_template(template, data)
        
        self.assertEqual(result, "Value: None")
    
    def test_empty_template(self):
        """Test with empty template."""
        template = ""
        data = {"name": "John"}
        
        result = string_template(template, data)
        
        self.assertEqual(result, "")
    
    def test_template_without_variables(self):
        """Test template without any variables."""
        template = "Hello, world!"
        data = {"name": "John"}
        
        result = string_template(template, data)
        
        self.assertEqual(result, "Hello, world!")
    
    def test_empty_data(self):
        """Test with empty data dictionary."""
        template = "Hello, {{name}}!"
        data = {}
        
        result = string_template(template, data)
        
        self.assertEqual(result, "Hello, {{name}}!")
    
    def test_complex_nested_text(self):
        """Test with more complex text containing variables."""
        template = """
        # User Profile
        
        **Name**: {{first_name}} {{last_name}}
        **Email**: {{email}}
        **Age**: {{age}}
        **Location**: {{city}}, {{country}}
        
        Welcome {{first_name}} to our platform!
        """
        
        data = {
            "first_name": "John",
            "last_name": "Doe",
            "email": "<EMAIL>",
            "age": 30,
            "city": "New York",
            "country": "USA"
        }
        
        expected = """
        # User Profile
        
        **Name**: John Doe
        **Email**: <EMAIL>
        **Age**: 30
        **Location**: New York, USA
        
        Welcome John to our platform!
        """
        
        result = string_template(template, data)
        
        self.assertEqual(result, expected)
    
    def test_special_characters(self):
        """Test with special characters in data."""
        template = "Message: {{message}}"
        data = {"message": "Hello & welcome! < > \"'"}
        
        result = string_template(template, data)
        
        self.assertEqual(result, "Message: Hello & welcome! < > \"'")
    
    def test_similar_variable_names(self):
        """Test with similar variable names."""
        template = "{{var}} and {{variable}}"
        data = {
            "var": "Short",
            "variable": "Long"
        }
        
        result = string_template(template, data)
        
        self.assertEqual(result, "Short and Long")
    
    def test_invalid_variable_name(self):
        """Test with invalid variable names that shouldn't match."""
        template = "{{}} {{1var}} {{var-name}} {{var_name}}"
        data = {
            "var_name": "Valid"
        }
        
        result = string_template(template, data)
        
        # Only var_name should be replaced
        self.assertEqual(result, "{{}} {{1var}} {{var-name}} Valid")

if __name__ == '__main__':
    unittest.main()