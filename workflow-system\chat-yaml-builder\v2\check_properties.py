#!/usr/bin/env python3
"""
Check if entity and attribute properties are being correctly parsed.
"""

import sys
import os
from parsers.entity_parser import parse_entities

def main():
    # Read sample text
    sample_path = os.path.join(os.path.dirname(__file__), 'samples', 'sample_entity_output2.txt')
    with open(sample_path, 'r') as f:
        sample_text = f.read()
    
    # Parse entities
    entities_data, warnings = parse_entities(sample_text)
    
    # Check if Employee has metadata
    if 'Employee' in entities_data['entities']:
        employee_data = entities_data['entities']['Employee']
        print("Employee Metadata:")
        print(employee_data.get('metadata', {}))
        
        # Check if Employee.email has metadata
        if 'attributes' in employee_data and 'email' in employee_data['attributes']:
            email_attr = employee_data['attributes']['email']
            print("\nEmployee.email Metadata:")
            print(email_attr.get('metadata', {}))
            
            # Check if Employee.email has display_name
            if 'metadata' in email_attr and 'display_name' in email_attr['metadata']:
                print("\nEmployee.email Display Name:", email_attr['metadata']['display_name'])
            
            # Check if Employee.email has validation
            if 'metadata' in email_attr and 'validation' in email_attr['metadata']:
                print("\nEmployee.email Validation:", email_attr['metadata']['validation'])
                print("Employee.email Error Message:", email_attr['metadata'].get('error_message', ''))
    else:
        print("Employee entity not found")

if __name__ == '__main__':
    main()
