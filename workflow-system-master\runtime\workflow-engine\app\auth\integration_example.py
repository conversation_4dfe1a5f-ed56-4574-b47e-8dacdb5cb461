"""
Integration Example for Authentication and Permission Services.

This example shows how to integrate the authentication and permission services
into the workflow engine.
"""

import os
from typing import Dict, Any, List, Optional

from fastapi import FastAPI, Depends, HTTPException, status, Request
from fastapi.middleware.cors import CORSMiddleware
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker, Session

# Import authentication and permission modules
from app.auth.auth_middleware import (
    AuthMiddleware, 
    SecurityContext, 
    require_auth, 
    require_role, 
    require_permission
)
from app.auth.auth_routes import router as auth_router
from app.auth.permission.permission_routes import router as permission_router
from app.auth.permission.permission_service import (
    PermissionService, 
    PermissionContext, 
    PermissionType, 
    ResourceType
)

# Database configuration
# Connect to the database in the Docker container
# Using localhost since we're running outside of the Docker network
DATABASE_URL = os.getenv("DATABASE_URL", "postgresql://postgres:workflow_postgres_secure_password@localhost:5433/workflow_system")

# Create database engine and session
engine = create_engine(DATABASE_URL)
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

def get_db():
    """Get database session."""
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()

# Create FastAPI app
app = FastAPI(
    title="Workflow System",
    description="Workflow System with Authentication and Permission Services",
    version="1.0.0"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Add authentication middleware
app.add_middleware(
    AuthMiddleware,
    exclude_paths=["/docs", "/redoc", "/openapi.json", "/auth/token", "/auth/refresh"]
)

# Include authentication and permission routes
app.include_router(auth_router)
app.include_router(permission_router)

# Example routes
@app.get("/")
async def root():
    """Public endpoint."""
    return {"message": "Welcome to the Workflow System"}

@app.get("/protected")
async def protected_route(security_context: SecurityContext = Depends(require_auth)):
    """Protected endpoint that requires authentication."""
    return {
        "message": "This is a protected route",
        "user": {
            "user_id": security_context.user_id,
            "username": security_context.username,
            "roles": security_context.roles
        }
    }

@app.get("/admin-only")
async def admin_only_route(security_context: SecurityContext = Depends(require_role("Administrator"))):
    """Protected endpoint that requires the Administrator role."""
    return {
        "message": "This is an admin-only route",
        "user": {
            "user_id": security_context.user_id,
            "username": security_context.username,
            "roles": security_context.roles
        }
    }

@app.get("/entities/{entity_id}")
async def get_entity(
    entity_id: str,
    security_context: SecurityContext = Depends(require_auth),
    db: Session = Depends(get_db)
):
    """
    Protected endpoint that requires read permission for the entity.
    
    This example shows how to check permissions for a specific resource.
    """
    # Create permission service
    permission_service = PermissionService(db)
    
    # Create permission context
    permission_context = PermissionContext(
        resource_type=ResourceType.ENTITY,
        resource_id=entity_id,
        permission_type=PermissionType.READ
    )
    
    # Check permission
    if not permission_service.check_permission(security_context, permission_context):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail=f"Permission denied: {PermissionType.READ} on {ResourceType.ENTITY} {entity_id}"
        )
    
    # Get entity from database (example)
    entity = {"entity_id": entity_id, "name": f"Entity {entity_id}", "type": "Example"}
    
    return entity

@app.post("/entities/{entity_id}")
async def update_entity(
    entity_id: str,
    entity_data: Dict[str, Any],
    security_context: SecurityContext = Depends(require_auth),
    db: Session = Depends(get_db)
):
    """
    Protected endpoint that requires write permission for the entity.
    
    This example shows how to check permissions for a specific resource.
    """
    # Create permission service
    permission_service = PermissionService(db)
    
    # Create permission context
    permission_context = PermissionContext(
        resource_type=ResourceType.ENTITY,
        resource_id=entity_id,
        permission_type=PermissionType.WRITE
    )
    
    # Check permission
    if not permission_service.check_permission(security_context, permission_context):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail=f"Permission denied: {PermissionType.WRITE} on {ResourceType.ENTITY} {entity_id}"
        )
    
    # Update entity in database (example)
    entity = {"entity_id": entity_id, **entity_data}
    
    return entity

@app.delete("/entities/{entity_id}")
async def delete_entity(
    entity_id: str,
    security_context: SecurityContext = Depends(require_auth),
    db: Session = Depends(get_db)
):
    """
    Protected endpoint that requires delete permission for the entity.
    
    This example shows how to check permissions for a specific resource.
    """
    # Create permission service
    permission_service = PermissionService(db)
    
    # Create permission context
    permission_context = PermissionContext(
        resource_type=ResourceType.ENTITY,
        resource_id=entity_id,
        permission_type=PermissionType.DELETE
    )
    
    # Require permission (this will raise an exception if permission is denied)
    permission_service.require_permission(security_context, permission_context)
    
    # Delete entity from database (example)
    
    return {"message": f"Entity {entity_id} deleted"}

@app.get("/workflows/{workflow_id}")
async def get_workflow(
    workflow_id: str,
    security_context: SecurityContext = Depends(require_auth),
    db: Session = Depends(get_db)
):
    """
    Protected endpoint that requires read permission for the workflow.
    
    This example shows how to check permissions for a specific resource.
    """
    # Create permission service
    permission_service = PermissionService(db)
    
    # Create permission context
    permission_context = PermissionContext(
        resource_type=ResourceType.FUNCTION,
        resource_id=workflow_id,
        permission_type=PermissionType.READ
    )
    
    # Check permission
    if not permission_service.check_permission(security_context, permission_context):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail=f"Permission denied: {PermissionType.READ} on {ResourceType.FUNCTION} {workflow_id}"
        )
    
    # Get workflow from database (example)
    workflow = {"workflow_id": workflow_id, "name": f"Workflow {workflow_id}", "status": "Active"}
    
    return workflow

@app.post("/workflows/{workflow_id}/execute")
async def execute_workflow(
    workflow_id: str,
    input_data: Dict[str, Any],
    security_context: SecurityContext = Depends(require_auth),
    db: Session = Depends(get_db)
):
    """
    Protected endpoint that requires execute permission for the workflow.
    
    This example shows how to check permissions for a specific resource.
    """
    # Create permission service
    permission_service = PermissionService(db)
    
    # Create permission context
    permission_context = PermissionContext(
        resource_type=ResourceType.FUNCTION,
        resource_id=workflow_id,
        permission_type=PermissionType.EXECUTE
    )
    
    # Check permission
    if not permission_service.check_permission(security_context, permission_context):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail=f"Permission denied: {PermissionType.EXECUTE} on {ResourceType.FUNCTION} {workflow_id}"
        )
    
    # Execute workflow (example)
    result = {"workflow_id": workflow_id, "status": "Executed", "result": "Success"}
    
    return result

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
