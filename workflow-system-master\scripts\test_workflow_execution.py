#!/usr/bin/env python3
"""
Test the workflow execution engine with a sample workflow.
"""
import sys
import os
import logging
import asyncio
from pathlib import Path
from datetime import datetime

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Make sure we can import from the app directory
project_root = Path(__file__).parent.parent.absolute()
workflow_engine_path = project_root / "runtime" / "workflow-engine"
sys.path.append(str(workflow_engine_path))

async def test_workflow_execution():
    """Test the workflow execution engine with a sample workflow."""
    try:
        from app.models.workflow import (
            GlobalObjective, LocalObjective, ExecutionPathway, Agent, AgentStack,
            Input, InputSource, Output, InputStack, OutputStack, ExecutionPathwayType
        )
        from app.services.workflow.execution_engine import WorkflowExecutionEngine
        from app.services.function_repository import FunctionRepository
        
        logger.info("Creating test workflow...")
        
        # Register mock function
        function_repo = FunctionRepository()
        
        @function_repo.register("test", "test_function")
        async def mock_function(input_data):
            logger.info(f"Executing mock function with input: {input_data}")
            # Simulate processing
            await asyncio.sleep(0.5)
            return {"result": "success", "processed": True, "timestamp": datetime.now().isoformat()}
        
        # Create test Local Objective
        test_lo = LocalObjective(
            id="LO001",
            contextual_id="GO001.LO001",
            name="Test Local Objective",
            parent_objective_id="GO001",
            function_type="test_function",
            workflow_source="Test",
            version="1.0",
            execution_pathway=ExecutionPathway(
                type=ExecutionPathwayType.TERMINAL
            ),
            agent_stack=AgentStack(
                agents=[Agent(role="System", rights=["Execute"])]
            ),
            input_stack=InputStack(
                description="Test input stack",
                inputs=[
                    Input(
                        slot_id="test_input",
                        source=InputSource(
                            type="System",
                            value="test_value"
                        ),
                        required=True
                    )
                ]
            ),
            output_stack=OutputStack(
                description="Test output stack",
                outputs=[
                    Output(
                        slot_id="test_output",
                        source="result"
                    )
                ]
            )
        )
        
        # Create test Global Objective
        test_go = GlobalObjective(
            tenant_id="T001",
            objective_id="GO001",
            contextual_id="GO001",
            name="Test Global Objective",
            version="1.0",
            local_objectives=[test_lo]
        )
        
        # Create execution engine
        engine = WorkflowExecutionEngine("T001")
        
        # Create workflow instance
        logger.info("Creating workflow instance...")
        instance = await engine.create_workflow_instance(test_go, "user-001")
        
        # Execute workflow
        logger.info("Executing workflow...")
        result = await engine.execute(instance, test_go)
        
        logger.info(f"Workflow execution completed. Result: {result}")
        return True
        
    except Exception as e:
        logger.error(f"Workflow execution test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    asyncio.run(test_workflow_execution())
