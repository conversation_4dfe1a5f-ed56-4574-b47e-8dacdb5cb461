-- Purchase Furniture Entity Tables Creation Script
-- Creates actual database tables for entities E13, E14, E15, E16

SET search_path TO workflow_runtime;

-- =====================================================
-- CREATE ENTITY TABLES
-- =====================================================

-- E13: FurnitureOrder Table
CREATE TABLE IF NOT EXISTS e13_furnitureorder (
    id SERIAL,
    orderid VARCHAR(255) NOT NULL,
    userid VARCHAR(255),
    furnituretypeid VARCHAR(255),
    productid VARCHAR(255),
    quantity INTEGER,
    unitprice DECIMAL(10,2),
    subtotal DECIMAL(10,2),
    gstamount DECIMAL(10,2),
    totalamount DECIMAL(10,2),
    paymentmethod VARCHAR(20),
    orderstatus VARCHAR(20),
    orderdate TIMESTAMP WITHOUT TIME ZONE,
    created_at TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    created_by VARCHAR(255) DEFAULT 'system',
    updated_by VARCHAR(255) DEFAULT 'system',
    CONSTRAINT e13_furnitureorder_pkey PRIMARY KEY (orderid)
);

-- E14: FurnitureProduct Table
CREATE TABLE IF NOT EXISTS e14_furnitureproduct (
    id SERIAL,
    productid VARCHAR(255) NOT NULL,
    productname VARCHAR(255),
    furnituretypeid VARCHAR(255),
    price DECIMAL(10,2),
    availableinventory INTEGER,
    description TEXT,
    created_at TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    created_by VARCHAR(255) DEFAULT 'system',
    updated_by VARCHAR(255) DEFAULT 'system',
    CONSTRAINT e14_furnitureproduct_pkey PRIMARY KEY (productid)
);

-- E15: FurnitureType Table
CREATE TABLE IF NOT EXISTS e15_furnituretype (
    id SERIAL,
    furnituretypeid VARCHAR(255) NOT NULL,
    typename VARCHAR(100),
    description TEXT,
    created_at TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    created_by VARCHAR(255) DEFAULT 'system',
    updated_by VARCHAR(255) DEFAULT 'system',
    CONSTRAINT e15_furnituretype_pkey PRIMARY KEY (furnituretypeid)
);

-- E16: PaymentDetails Table
CREATE TABLE IF NOT EXISTS e16_paymentdetails (
    id SERIAL,
    paymentid VARCHAR(255) NOT NULL,
    orderid VARCHAR(255),
    paymentmethod VARCHAR(20),
    upiid VARCHAR(100),
    cardname VARCHAR(100),
    cardnumber VARCHAR(20),
    cvv VARCHAR(4),
    expirydate VARCHAR(7),
    paymentstatus VARCHAR(20),
    created_at TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    created_by VARCHAR(255) DEFAULT 'system',
    updated_by VARCHAR(255) DEFAULT 'system',
    CONSTRAINT e16_paymentdetails_pkey PRIMARY KEY (paymentid)
);

-- =====================================================
-- CREATE UPDATE TRIGGERS
-- =====================================================

-- Create or replace the update function
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Add triggers for each table
CREATE TRIGGER update_e13_furnitureorder_updated_at 
    BEFORE UPDATE ON e13_furnitureorder 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_e14_furnitureproduct_updated_at 
    BEFORE UPDATE ON e14_furnitureproduct 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_e15_furnituretype_updated_at 
    BEFORE UPDATE ON e15_furnituretype 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_e16_paymentdetails_updated_at 
    BEFORE UPDATE ON e16_paymentdetails 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

COMMIT;
