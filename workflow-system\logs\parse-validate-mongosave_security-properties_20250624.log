{"timestamp": "2025-06-24T12:40:47.484891", "endpoint": "parse-validate-mongosave/security-properties", "input": {"natural_language": "Security Property: TestEntity2 Access Control\nEntity: TestEntity2\nProperty Type: access_control\nProperty Name: read_permission\nProperty Value: role_based\nDescription: Role-based read access for TestEntity2\nSecurity Level: medium", "tenant_id": "t999", "edit_draft": true, "edit_temp": false, "edit_production": false}, "output": {"success": true, "security_property_results": [], "operation": "parse_validate_mongosave", "total_security_properties": 0}, "status": "success"}
{"timestamp": "2025-06-24T12:54:00.269966", "endpoint": "parse-validate-mongosave/security-properties", "input": {"natural_language": "Entity.Attribute | Classification | PII Type | Encryption Required | Encryption Type | Masking Required | Masking Pattern | Access Level | Audit Trail | Data Residency | Retention Override | Anonymization Required | Anonymization Method | Compliance Frameworks\nTestEntity2.testId | internal | none | false | aes256 | false | | read_internal | true | global | | false | hash | GDPR\nTenant: t999", "tenant_id": "t999", "edit_draft": true, "edit_temp": false, "edit_production": false}, "output": {"success": true, "security_property_results": [{"success": false, "errors": ["Entity with entity_id E_TestEntity2_1750769640 does not exist", "Attribute with attribute_id A_E_TestEntity2_1750769640_testId_1750769640 does not exist"], "parsed_data": {"security_property_id": "SEC6", "entity_id": "E_TestEntity2_1750769640", "attribute_id": "A_E_TestEntity2_1750769640_testId_1750769640", "classification": "internal", "pii_type": "none", "encryption_required": false, "encryption_type": "aes256", "masking_required": false, "masking_pattern": "", "access_level": "read_internal", "audit_trail": true, "data_residency": "global", "retention_override": "", "anonymization_required": false, "anonymization_method": "hash", "compliance_frameworks": "GDPR", "data_purpose": "", "consent_required": false, "cross_border_transfer": false, "backup_encryption": false, "secure_deletion": false, "natural_language": "Entity: TestEntity2\nAttribute: testId\nClassification: internal\nAccess Level: read_internal\nAudit Trail: true\nData Residency: global\nCompliance Frameworks: GDPR", "version": 1, "status": "draft", "created_at": "2025-06-24T12:54:00.240449", "updated_at": "2025-06-24T12:54:00.240449", "created_by": "Tarun", "updated_by": "Tarun", "security_property_status": "new", "changes_detected": []}}], "operation": "parse_validate_mongosave", "total_security_properties": 1}, "status": "success"}
