/* global use, db */
// MongoDB Playground
// Use Ctrl+Space inside a snippet or a string literal to trigger completions.

// The current database to use.
use('workflow_system');

// Search for documents in the current collection.
db.getCollection('workflow')
  .find(
    {
      /*
      * Filter
      * fieldA: value or expression
      */
     "id": "GO100",
    },
    {
      /*
      * Projection
      * _id: 0, // exclude _id
      * fieldA: 1 // include field
      */
    }
  )
  .sort({
    /*
    * fieldA: 1 // ascending
    * fieldB: -1 // descending
    */
  });
