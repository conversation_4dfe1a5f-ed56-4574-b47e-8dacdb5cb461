from fastapi import <PERSON><PERSON><PERSON>, HTTPException, Depends, status
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
from typing import Dict, List, Optional, Any
import logging

# Set up logging
logger = logging.getLogger("prescriptive_api")

app = FastAPI(title="Prescriptive API", description="API for managing prescriptive text")

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Adjust in production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Models
class PrescriptiveRequest(BaseModel):
    component_type: str  # 'entities', 'go_definitions', 'lo_definitions', 'roles'
    prescriptive_text: str
    validate_only: bool = False

class ValidationResponse(BaseModel):
    is_valid: bool
    messages: List[str]
    parsed_data: Optional[Dict[str, Any]] = None

class EntityResponse(BaseModel):
    entity_id: str
    name: str
    attributes: Dict[str, Any]
    relationships: Optional[Dict[str, Any]] = None
    business_rules: Optional[Dict[str, Any]] = None
    validations: Optional[Dict[str, Any]] = None
    calculated_fields: Optional[Dict[str, Any]] = None
    lifecycle_management: Optional[Dict[str, Any]] = None

# Routes
@app.post("/validate", response_model=ValidationResponse)
async def validate_prescriptive(request: PrescriptiveRequest):
    """
    Validate prescriptive text without saving to database
    """
    try:
        # Import parser based on component type
        if request.component_type == 'entities':
            from v2.parsers.entity_parser import parse_entities
            parsed_data, warnings = parse_entities(request.prescriptive_text)
        elif request.component_type == 'go_definitions':
            from v2.parsers.go_parser import parse_go_definitions
            parsed_data, warnings = parse_go_definitions(request.prescriptive_text)
        elif request.component_type == 'lo_definitions':
            from v2.parsers.lo_parser import parse_lo_definitions
            parsed_data, warnings = parse_lo_definitions(request.prescriptive_text)
        elif request.component_type == 'roles':
            from v2.parsers.role_parser import parse_roles
            parsed_data, warnings = parse_roles(request.prescriptive_text)
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Unknown component type: {request.component_type}"
            )
        
        # Validate parsed data
        is_valid = len(warnings) == 0
        
        return {
            "is_valid": is_valid,
            "messages": warnings,
            "parsed_data": parsed_data
        }
    except Exception as e:
        logger.error(f"Error validating prescriptive text: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error validating prescriptive text: {str(e)}"
        )

@app.post("/deploy", response_model=ValidationResponse)
async def deploy_prescriptive(request: PrescriptiveRequest):
    """
    Parse prescriptive text and deploy to database
    """
    try:
        # First validate
        validation_response = await validate_prescriptive(request)
        
        if not validation_response["is_valid"]:
            return validation_response
        
        # If validate_only is True, return validation results without deploying
        if request.validate_only:
            return validation_response
        
        # Deploy to database
        from v2.component_deployer import ComponentDeployer
        
        deployer = ComponentDeployer(use_temp_schema=False)
        success, messages = deployer.deploy_from_prescriptive_text(
            request.component_type, 
            request.prescriptive_text
        )
        
        return {
            "is_valid": success,
            "messages": messages,
            "parsed_data": validation_response["parsed_data"]
        }
    except Exception as e:
        logger.error(f"Error deploying prescriptive text: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error deploying prescriptive text: {str(e)}"
        )

@app.get("/entities", response_model=List[EntityResponse])
async def get_entities():
    """
    Get all entities from the database
    """
    try:
        from v2.db_utils import execute_query
        
        success, messages, result = execute_query(
            """
            SELECT e.entity_id, e.name, e.description, e.metadata, e.lifecycle_management
            FROM workflow_runtime.entities e
            ORDER BY e.name
            """
        )
        
        if not success:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Error querying entities: {messages}"
            )
        
        entities = []
        for row in result:
            entity_id, name, description, metadata, lifecycle_management = row
            
            # Get attributes
            success, attr_messages, attr_result = execute_query(
                """
                SELECT a.attribute_id, a.name, a.type, a.required, a.calculated_field, 
                       a.calculation_formula, a.dependencies
                FROM workflow_runtime.entity_attributes a
                WHERE a.entity_id = %s
                ORDER BY a.name
                """,
                (entity_id,)
            )
            
            if not success:
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail=f"Error querying attributes: {attr_messages}"
                )
            
            attributes = {}
            for attr_row in attr_result:
                attr_id, attr_name, attr_type, attr_required, attr_calculated, attr_formula, attr_deps = attr_row
                
                attributes[attr_name] = {
                    "attribute_id": attr_id,
                    "type": attr_type,
                    "required": attr_required,
                    "calculated_field": attr_calculated,
                    "calculation_formula": attr_formula,
                    "dependencies": attr_deps
                }
                
                # Get enum values if applicable
                if attr_type == 'enum':
                    success, enum_messages, enum_result = execute_query(
                        """
                        SELECT value, display_name, sort_order
                        FROM workflow_runtime.attribute_enum_values
                        WHERE attribute_id = %s
                        ORDER BY sort_order
                        """,
                        (attr_id,)
                    )
                    
                    if success and enum_result:
                        attributes[attr_name]["enum_values"] = [row[0] for row in enum_result]
                
                # Get validations
                success, val_messages, val_result = execute_query(
                    """
                    SELECT validation_name, validation_type, validation_expression, error_message
                    FROM workflow_runtime.attribute_validations
                    WHERE attribute_id = %s
                    """,
                    (attr_id,)
                )
                
                if success and val_result:
                    validations = {}
                    for val_row in val_result:
                        val_name, val_type, val_expr, val_error = val_row
                        validations[val_name] = {
                            "type": val_type,
                            "constraint": val_expr,
                            "error_message": val_error
                        }
                    
                    attributes[attr_name]["validations"] = validations
            
            # Get relationships
            success, rel_messages, rel_result = execute_query(
                """
                SELECT r.relationship_type, e2.name as target_entity, 
                       a1.name as source_attribute, a2.name as target_attribute
                FROM workflow_runtime.entity_relationships r
                JOIN workflow_runtime.entities e2 ON r.target_entity_id = e2.entity_id
                JOIN workflow_runtime.entity_attributes a1 ON r.source_attribute_id = a1.attribute_id
                JOIN workflow_runtime.entity_attributes a2 ON r.target_attribute_id = a2.attribute_id
                WHERE r.source_entity_id = %s
                """,
                (entity_id,)
            )
            
            relationships = {}
            if success and rel_result:
                for rel_row in rel_result:
                    rel_type, target_entity, source_attr, target_attr = rel_row
                    rel_name = f"{rel_type}_{target_entity}"
                    relationships[rel_name] = {
                        "type": rel_type,
                        "entity": target_entity,
                        "source_attribute": source_attr,
                        "target_attribute": target_attr
                    }
            
            # Get business rules
            success, rule_messages, rule_result = execute_query(
                """
                SELECT rule_id, name, description, condition
                FROM workflow_runtime.entity_business_rules
                WHERE entity_id = %s
                """,
                (entity_id,)
            )
            
            business_rules = {}
            if success and rule_result:
                for rule_row in rule_result:
                    rule_id, rule_name, rule_desc, rule_condition = rule_row
                    conditions = rule_condition.split('\n') if rule_condition else []
                    business_rules[rule_name] = {
                        "rule_id": rule_id,
                        "description": rule_desc,
                        "conditions": conditions
                    }
            
            entity = {
                "entity_id": entity_id,
                "name": name,
                "attributes": attributes,
                "relationships": relationships,
                "business_rules": business_rules,
                "lifecycle_management": lifecycle_management
            }
            
            entities.append(entity)
        
        return entities
    except Exception as e:
        logger.error(f"Error retrieving entities: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error retrieving entities: {str(e)}"
        )

@app.get("/entities/{entity_id}", response_model=EntityResponse)
async def get_entity(entity_id: str):
    """
    Get a specific entity by ID
    """
    try:
        from v2.db_utils import execute_query
        
        success, messages, result = execute_query(
            """
            SELECT e.entity_id, e.name, e.description, e.metadata, e.lifecycle_management
            FROM workflow_runtime.entities e
            WHERE e.entity_id = %s
            """,
            (entity_id,)
        )
        
        if not success:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Error querying entity: {messages}"
            )
        
        if not result:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Entity with ID {entity_id} not found"
            )
        
        entity_id, name, description, metadata, lifecycle_management = result[0]
        
        # Get attributes (same as in get_entities)
        # ... (code omitted for brevity, same as in get_entities)
        
        # Return entity
        # ... (code omitted for brevity, same as in get_entities)
        
    except Exception as e:
        logger.error(f"Error retrieving entity: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error retrieving entity: {str(e)}"
        )

# Main entry point
if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
