# 📁 FILE: validate_yaml.py

import tempfile
from validate_engine import EXPECTED_STRUCTURE, load_yaml, validate_value
from rbac_validator import validate_rbac
from typing import Any, Dict
import yaml

def validate_yaml(yaml_text: str) -> Dict[str, Any]:
    """Validates a YAML string and returns validation status with detailed errors."""
    try:
        # ✅ Step 1: Confirm YAML is parseable (before even saving to file)
        try:
            yaml.safe_load(yaml_text)
        except yaml.YAMLError as e:
            return {"valid": False, "error": f"❌ YAML Parse Error:\n```\n{e}\n```"}

        # ✅ Step 2: Save YAML to a temp file to reuse existing engine
        with tempfile.NamedTemporaryFile(delete=False, suffix=".yaml", mode="w") as tmp:
            tmp.write(yaml_text)
            temp_path = tmp.name

        # ✅ Step 3: Load YAML as object
        data = load_yaml(temp_path)
        if data is None:
            return {"valid": False, "error": "❌ YAML could not be parsed into a dictionary structure."}

        # ✅ Step 4: Validate against schema
        errors = []
        for key, spec in EXPECTED_STRUCTURE.items():
            if spec.get("required", False) and key not in data:
                errors.append(f"❌ Missing top-level key: `{key}`")
                continue
            if key in data:
                errors += validate_value(data[key], spec, key)

        # ✅ Step 5: Perform RBAC validation
        rbac_results = validate_rbac(data)
        if not rbac_results["valid"]:
            errors.extend(rbac_results["errors"])
        
        # ✅ Step 6: Return structured result
        if errors:
            return {"valid": False, "error": "\n".join(errors)}
        else:
            return {"valid": True, "rbac_validation": rbac_results["sections"]}

    except Exception as e:
        return {"valid": False, "error": f"❌ Unexpected Error:\n```\n{str(e)}\n```"}



# import yaml
# from validate import validate_yaml_file  # Import your full engine
# import tempfile

# def validate_yaml(yaml_text: str) -> dict:
#     """Wrapper to validate YAML string and return result as dict for Streamlit."""
#     try:
#         # Save to a temporary file (your existing validate_yaml_file expects a path)
#         with tempfile.NamedTemporaryFile(delete=False, suffix=".yaml", mode="w") as temp_file:
#             temp_file.write(yaml_text)
#             temp_path = temp_file.name
        
#         # Validate the temp file using your full logic
#         success = validate_yaml_file(temp_path)
#         if success:
#             return {"valid": True}
#         else:
#             return {"valid": False, "error": "Structure does not match expected schema. See logs for details."}
    
#     except Exception as e:
#         return {"valid": False, "error": str(e)}
