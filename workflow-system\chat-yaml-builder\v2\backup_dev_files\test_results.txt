=== YAML Builder v2 Test Results ===

Component Validators:
- roles: PASS
- entities: PASS
- go_definitions: PASS
- lo_definitions: PASS

Registry Validator: PASS

YAML Assembler: PASS

ID Generator: PASS

Component Deployers:
- roles: PASS
  - Inserted role 'Admin' into workflow_temp.roles
  - Created workflow_temp.role_permissions table
  - Deployed permissions for role 'role_admin'
  - Inserted role 'Manager' into workflow_temp.roles
  - Deployed permissions for role 'role_manager'
  - Created workflow_temp.role_inheritance table
  - Warning: Role 'role_manager' inherits non-existent role 'role_user'
  - Deployed inheritance for role 'role_manager'
  - Inserted role 'User' into workflow_temp.roles
  - Deployed permissions for role 'role_user'
  - Inserted document into components
- entities: PASS
  - Inserted entity 'Department' into workflow_temp.entities
  - Inserted attribute 'id' for entity 'entity_department' into workflow_temp.entity_attributes
  - Inserted attribute 'name' for entity 'entity_department' into workflow_temp.entity_attributes
  - Inserted attribute 'budget' for entity 'entity_department' into workflow_temp.entity_attributes
  - Inserted attribute 'manager_id' for entity 'entity_department' into workflow_temp.entity_attributes
  - Created workflow_temp.entity_relationships table
  - Warning: Relationship 'employees' in entity 'entity_department' references non-existent entity 'entity_employee'
  - Inserted entity 'Employee' into workflow_temp.entities
  - Inserted attribute 'id' for entity 'entity_employee' into workflow_temp.entity_attributes
  - Inserted attribute 'name' for entity 'entity_employee' into workflow_temp.entity_attributes
  - Inserted attribute 'email' for entity 'entity_employee' into workflow_temp.entity_attributes
  - Inserted attribute 'department' for entity 'entity_employee' into workflow_temp.entity_attributes
  - Inserted attribute 'manager' for entity 'entity_employee' into workflow_temp.entity_attributes
  - Inserted attribute 'salary' for entity 'entity_employee' into workflow_temp.entity_attributes
  - Inserted relationship 'manages' for entity 'entity_employee' into workflow_temp.entity_relationships
  - Inserted relationship 'belongsTo' for entity 'entity_employee' into workflow_temp.entity_relationships
  - Created workflow_temp.entity_business_rules table
  - Inserted business rule 'salary_cap' for entity 'entity_employee' into workflow_temp.entity_business_rules
  - Inserted document into components
- go_definitions: PASS
  - Inserted GO 'EmployeeOnboarding' into workflow_temp.global_objectives
  - Created workflow_temp.go_lo_mapping table
  - Inserted LO mapping 'CollectPersonalInfo' for GO 'go_employeeonboarding' into workflow_temp.go_lo_mapping
  - Inserted LO mapping 'AssignDepartment' for GO 'go_employeeonboarding' into workflow_temp.go_lo_mapping
  - Inserted LO mapping 'SetupAccounts' for GO 'go_employeeonboarding' into workflow_temp.go_lo_mapping
  - Inserted LO mapping 'ScheduleTraining' for GO 'go_employeeonboarding' into workflow_temp.go_lo_mapping
  - Created workflow_temp.go_performance_metrics table
  - Inserted performance metric 'onboarding_time' for GO 'go_employeeonboarding' into workflow_temp.go_performance_metrics
  - Inserted performance metric 'completion_rate' for GO 'go_employeeonboarding' into workflow_temp.go_performance_metrics
  - Inserted document into components
- lo_definitions: PASS
  - Inserted LO 'CollectPersonalInfo' into workflow_temp.local_objectives
  - Inserted input item 'name' for LO 'lo_collectpersonalinfo' into workflow_temp.lo_input_items
  - Inserted input item 'email' for LO 'lo_collectpersonalinfo' into workflow_temp.lo_input_items
  - Inserted input item 'phone' for LO 'lo_collectpersonalinfo' into workflow_temp.lo_input_items
  - Inserted input item 'address' for LO 'lo_collectpersonalinfo' into workflow_temp.lo_input_items
  - Inserted input item 'emergency_contact' for LO 'lo_collectpersonalinfo' into workflow_temp.lo_input_items
  - Inserted output item 'employee_record' for LO 'lo_collectpersonalinfo' into workflow_temp.lo_output_items
  - Inserted output item 'employee_id' for LO 'lo_collectpersonalinfo' into workflow_temp.lo_output_items
  - Inserted LO 'AssignDepartment' into workflow_temp.local_objectives
  - Inserted input item 'employee_id' for LO 'lo_assigndepartment' into workflow_temp.lo_input_items
  - Inserted input item 'department' for LO 'lo_assigndepartment' into workflow_temp.lo_input_items
  - Inserted input item 'manager' for LO 'lo_assigndepartment' into workflow_temp.lo_input_items
  - Inserted output item 'department_assignment' for LO 'lo_assigndepartment' into workflow_temp.lo_output_items
  - Created workflow_temp.lo_dependencies table
  - Inserted dependency on LO 'CollectPersonalInfo' for LO 'lo_assigndepartment' into workflow_temp.lo_dependencies
  - Inserted LO 'SetupAccounts' into workflow_temp.local_objectives
  - Inserted input item 'employee_id' for LO 'lo_setupaccounts' into workflow_temp.lo_input_items
  - Inserted input item 'department' for LO 'lo_setupaccounts' into workflow_temp.lo_input_items
  - Inserted input item 'email_account' for LO 'lo_setupaccounts' into workflow_temp.lo_input_items
  - Inserted input item 'vpn_access' for LO 'lo_setupaccounts' into workflow_temp.lo_input_items
  - Inserted input item 'system_access' for LO 'lo_setupaccounts' into workflow_temp.lo_input_items
  - Inserted output item 'account_setup' for LO 'lo_setupaccounts' into workflow_temp.lo_output_items
  - Inserted output item 'email_address' for LO 'lo_setupaccounts' into workflow_temp.lo_output_items
  - Inserted dependency on LO 'AssignDepartment' for LO 'lo_setupaccounts' into workflow_temp.lo_dependencies
  - Inserted LO 'ScheduleTraining' into workflow_temp.local_objectives
  - Inserted input item 'employee_id' for LO 'lo_scheduletraining' into workflow_temp.lo_input_items
  - Inserted input item 'department' for LO 'lo_scheduletraining' into workflow_temp.lo_input_items
  - Inserted input item 'orientation_date' for LO 'lo_scheduletraining' into workflow_temp.lo_input_items
  - Inserted input item 'department_training_date' for LO 'lo_scheduletraining' into workflow_temp.lo_input_items
  - Inserted input item 'system_training' for LO 'lo_scheduletraining' into workflow_temp.lo_input_items
  - Inserted output item 'training_schedule' for LO 'lo_scheduletraining' into workflow_temp.lo_output_items
  - Inserted dependency on LO 'SetupAccounts' for LO 'lo_scheduletraining' into workflow_temp.lo_dependencies
  - Inserted document into components

=== Summary ===
Total tests: 11
Passed tests: 11
Failed tests: 0
Success rate: 100.00%
