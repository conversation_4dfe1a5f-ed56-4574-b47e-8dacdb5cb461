2025-06-23 10:56:38,408 - inserters.v3.roles.entity_attributes_inserter - INFO - Starting deploy_single_entity_attribute_to_workflow_temp for attribute: E45.At4
2025-06-23 10:59:40,871 - inserters.v3.roles.entity_attributes_inserter - INFO - Starting deploy_single_entity_attribute_to_workflow_runtime for attribute: E45.At4
2025-06-23 11:21:56,165 - inserters.v3.roles.entity_attributes_inserter - INFO - Starting deploy_single_entity_attribute_to_workflow_temp for attribute: E44.At11
2025-06-23 14:03:49,127 - inserters.v3.roles.entity_attributes_inserter - INFO - Starting deploy_single_entity_attribute_to_workflow_temp for attribute: ATTR001
2025-06-23 14:03:49,215 - inserters.v3.roles.entity_attributes_inserter - INFO - Starting process_mongo_entity_attributes_to_workflow_temp
2025-06-23 14:03:49,218 - inserters.v3.roles.entity_attributes_inserter - INFO - Found 9 entity attributes with status 'draft' in MongoDB
2025-06-23 14:03:49,226 - inserters.v3.roles.entity_attributes_inserter - INFO - Entity attribute E44.At2 already exists in workflow_temp
2025-06-23 14:03:49,235 - inserters.v3.roles.entity_attributes_inserter - INFO - Entity attribute E44.At4 already exists in workflow_temp
2025-06-23 14:03:49,242 - inserters.v3.roles.entity_attributes_inserter - INFO - Starting insert_entity_attribute_to_workflow_temp for attribute: E44.At6
2025-06-23 14:03:49,242 - inserters.v3.roles.entity_attributes_inserter - INFO - Optional fields missing from MongoDB data: ['required', 'calculated_field']
2025-06-23 14:03:49,242 - inserters.v3.roles.entity_attributes_inserter - INFO - Field validation passed: 1/1 required fields, 21/23 optional fields
2025-06-23 14:03:49,258 - inserters.v3.roles.entity_attributes_inserter - INFO - Generated next attribute ID: E44.At6 -> E44.At13
2025-06-23 14:03:49,258 - inserters.v3.roles.entity_attributes_inserter - INFO - Incremented attribute ID from E44.At6 to E44.At13
2025-06-23 14:03:49,258 - inserters.v3.roles.entity_attributes_inserter - INFO - Additional MongoDB fields preserved: {'original_created_at': '2025-06-20T14:19:55.220066', 'original_updated_at': '2025-06-20T14:19:55.220073', 'original_created_by': 'Tarun', 'original_updated_by': 'Tarun', 'original_attribute_id': 'E44.At6'}
2025-06-23 14:03:49,259 - inserters.v3.roles.entity_attributes_inserter - INFO - Successfully inserted entity attribute to workflow_temp with ID: E44.At13
2025-06-23 14:03:49,262 - inserters.v3.roles.entity_attributes_inserter - INFO - Updated MongoDB status to deployed_to_temp for attribute: E44.At13
2025-06-23 14:03:49,269 - inserters.v3.roles.entity_attributes_inserter - INFO - Entity attribute E44.At8 already exists in workflow_temp
2025-06-23 14:03:49,277 - inserters.v3.roles.entity_attributes_inserter - INFO - Entity attribute E44.At10 already exists in workflow_temp
2025-06-23 14:03:49,285 - inserters.v3.roles.entity_attributes_inserter - INFO - Starting insert_entity_attribute_to_workflow_temp for attribute: .At1
2025-06-23 14:03:49,285 - inserters.v3.roles.entity_attributes_inserter - INFO - Optional fields missing from MongoDB data: ['required', 'calculated_field']
2025-06-23 14:03:49,285 - inserters.v3.roles.entity_attributes_inserter - INFO - Field validation passed: 1/1 required fields, 21/23 optional fields
2025-06-23 14:03:49,289 - inserters.v3.roles.entity_attributes_inserter - WARNING - Invalid attribute ID format: .At1. Expected format: E1.At2
2025-06-23 14:03:49,289 - inserters.v3.roles.entity_attributes_inserter - INFO - Incremented attribute ID from .At1 to .At1
2025-06-23 14:03:49,289 - inserters.v3.roles.entity_attributes_inserter - INFO - Additional MongoDB fields preserved: {'original_created_at': '2025-06-23T06:36:14.276890', 'original_updated_at': '2025-06-23T06:36:14.276897', 'original_created_by': 'Tarun', 'original_updated_by': 'Tarun', 'original_attribute_id': '.At1'}
2025-06-23 14:03:49,290 - inserters.v3.roles.entity_attributes_inserter - INFO - Successfully inserted entity attribute to workflow_temp with ID: .At1
2025-06-23 14:03:49,292 - inserters.v3.roles.entity_attributes_inserter - INFO - Updated MongoDB status to deployed_to_temp for attribute: .At1
2025-06-23 14:03:49,299 - inserters.v3.roles.entity_attributes_inserter - INFO - Starting insert_entity_attribute_to_workflow_temp for attribute: .At2
2025-06-23 14:03:49,299 - inserters.v3.roles.entity_attributes_inserter - INFO - Optional fields missing from MongoDB data: ['required', 'calculated_field']
2025-06-23 14:03:49,299 - inserters.v3.roles.entity_attributes_inserter - INFO - Field validation passed: 1/1 required fields, 21/23 optional fields
2025-06-23 14:03:49,305 - inserters.v3.roles.entity_attributes_inserter - WARNING - Invalid attribute ID format: .At2. Expected format: E1.At2
2025-06-23 14:03:49,305 - inserters.v3.roles.entity_attributes_inserter - INFO - Incremented attribute ID from .At2 to .At2
2025-06-23 14:03:49,305 - inserters.v3.roles.entity_attributes_inserter - INFO - Additional MongoDB fields preserved: {'original_created_at': '2025-06-23T06:36:14.286765', 'original_updated_at': '2025-06-23T06:36:14.286773', 'original_created_by': 'Tarun', 'original_updated_by': 'Tarun', 'original_attribute_id': '.At2'}
2025-06-23 14:03:49,306 - inserters.v3.roles.entity_attributes_inserter - INFO - Successfully inserted entity attribute to workflow_temp with ID: .At2
2025-06-23 14:03:49,308 - inserters.v3.roles.entity_attributes_inserter - INFO - Updated MongoDB status to deployed_to_temp for attribute: .At2
2025-06-23 14:03:49,314 - inserters.v3.roles.entity_attributes_inserter - INFO - Starting insert_entity_attribute_to_workflow_temp for attribute: .At3
2025-06-23 14:03:49,314 - inserters.v3.roles.entity_attributes_inserter - INFO - Optional fields missing from MongoDB data: ['required', 'calculated_field']
2025-06-23 14:03:49,314 - inserters.v3.roles.entity_attributes_inserter - INFO - Field validation passed: 1/1 required fields, 21/23 optional fields
2025-06-23 14:03:49,320 - inserters.v3.roles.entity_attributes_inserter - WARNING - Invalid attribute ID format: .At3. Expected format: E1.At2
2025-06-23 14:03:49,320 - inserters.v3.roles.entity_attributes_inserter - INFO - Incremented attribute ID from .At3 to .At3
2025-06-23 14:03:49,320 - inserters.v3.roles.entity_attributes_inserter - INFO - Additional MongoDB fields preserved: {'original_created_at': '2025-06-23T06:36:14.298690', 'original_updated_at': '2025-06-23T06:36:14.298698', 'original_created_by': 'Tarun', 'original_updated_by': 'Tarun', 'original_attribute_id': '.At3'}
2025-06-23 14:03:49,322 - inserters.v3.roles.entity_attributes_inserter - INFO - Successfully inserted entity attribute to workflow_temp with ID: .At3
2025-06-23 14:03:49,324 - inserters.v3.roles.entity_attributes_inserter - INFO - Updated MongoDB status to deployed_to_temp for attribute: .At3
2025-06-23 14:03:49,330 - inserters.v3.roles.entity_attributes_inserter - INFO - Starting insert_entity_attribute_to_workflow_temp for attribute: E45.At4
2025-06-23 14:03:49,330 - inserters.v3.roles.entity_attributes_inserter - INFO - Optional fields missing from MongoDB data: ['required', 'calculated_field']
2025-06-23 14:03:49,330 - inserters.v3.roles.entity_attributes_inserter - INFO - Field validation passed: 1/1 required fields, 21/23 optional fields
2025-06-23 14:03:49,345 - inserters.v3.roles.entity_attributes_inserter - INFO - Generated next attribute ID: E45.At4 -> E45.At5
2025-06-23 14:03:49,345 - inserters.v3.roles.entity_attributes_inserter - INFO - Incremented attribute ID from E45.At4 to E45.At5
2025-06-23 14:03:49,345 - inserters.v3.roles.entity_attributes_inserter - INFO - Additional MongoDB fields preserved: {'original_created_at': '2025-06-23T06:36:14.308383', 'original_updated_at': '2025-06-23T06:36:14.308390', 'original_created_by': 'Tarun', 'original_updated_by': 'Tarun', 'original_attribute_id': 'E45.At4'}
2025-06-23 14:03:49,346 - inserters.v3.roles.entity_attributes_inserter - INFO - Successfully inserted entity attribute to workflow_temp with ID: E45.At5
2025-06-23 14:03:49,348 - inserters.v3.roles.entity_attributes_inserter - INFO - Updated MongoDB status to deployed_to_temp for attribute: E45.At5
2025-06-23 14:03:49,349 - inserters.v3.roles.entity_attributes_inserter - INFO - Completed process_mongo_entity_attributes_to_workflow_temp: 5 successful, 0 failed
2025-06-23 14:09:00,476 - inserters.v3.roles.entity_attributes_inserter - INFO - Starting deploy_single_entity_attribute_to_workflow_temp for attribute: ATTR001
2025-06-23 14:09:00,539 - inserters.v3.roles.entity_attributes_inserter - INFO - Starting process_mongo_entity_attributes_to_workflow_temp
2025-06-23 14:09:00,542 - inserters.v3.roles.entity_attributes_inserter - INFO - Found 4 entity attributes with status 'draft' in MongoDB
2025-06-23 14:09:00,550 - inserters.v3.roles.entity_attributes_inserter - INFO - Entity attribute E44.At2 already exists in workflow_temp
2025-06-23 14:09:00,558 - inserters.v3.roles.entity_attributes_inserter - INFO - Entity attribute E44.At4 already exists in workflow_temp
2025-06-23 14:09:00,565 - inserters.v3.roles.entity_attributes_inserter - INFO - Entity attribute E44.At8 already exists in workflow_temp
2025-06-23 14:09:00,572 - inserters.v3.roles.entity_attributes_inserter - INFO - Entity attribute E44.At10 already exists in workflow_temp
2025-06-23 14:09:00,573 - inserters.v3.roles.entity_attributes_inserter - INFO - Completed process_mongo_entity_attributes_to_workflow_temp: 0 successful, 0 failed
2025-06-23 14:32:32,826 - inserters.v3.roles.entity_attributes_inserter - INFO - Starting deploy_single_entity_attribute_to_workflow_temp for attribute: ATTR001
2025-06-23 14:32:32,959 - inserters.v3.roles.entity_attributes_inserter - INFO - Starting process_mongo_entity_attributes_to_workflow_temp
2025-06-23 14:32:32,962 - inserters.v3.roles.entity_attributes_inserter - INFO - Found 4 entity attributes with status 'draft' in MongoDB
2025-06-23 14:32:32,969 - inserters.v3.roles.entity_attributes_inserter - INFO - Entity attribute E44.At2 already exists in workflow_temp
2025-06-23 14:32:32,977 - inserters.v3.roles.entity_attributes_inserter - INFO - Entity attribute E44.At4 already exists in workflow_temp
2025-06-23 14:32:32,984 - inserters.v3.roles.entity_attributes_inserter - INFO - Entity attribute E44.At8 already exists in workflow_temp
2025-06-23 14:32:32,991 - inserters.v3.roles.entity_attributes_inserter - INFO - Entity attribute E44.At10 already exists in workflow_temp
2025-06-23 14:32:32,991 - inserters.v3.roles.entity_attributes_inserter - INFO - Completed process_mongo_entity_attributes_to_workflow_temp: 0 successful, 0 failed
2025-06-23 14:38:52,329 - inserters.v3.roles.entity_attributes_inserter - INFO - Starting deploy_single_entity_attribute_to_workflow_temp for attribute: ATTR001
2025-06-23 14:38:52,470 - inserters.v3.roles.entity_attributes_inserter - INFO - Starting process_mongo_entity_attributes_to_workflow_temp
2025-06-23 14:38:52,473 - inserters.v3.roles.entity_attributes_inserter - INFO - Found 4 entity attributes with status 'draft' in MongoDB
2025-06-23 14:38:52,481 - inserters.v3.roles.entity_attributes_inserter - INFO - Entity attribute E44.At2 already exists in workflow_temp
2025-06-23 14:38:52,490 - inserters.v3.roles.entity_attributes_inserter - INFO - Entity attribute E44.At4 already exists in workflow_temp
2025-06-23 14:38:52,498 - inserters.v3.roles.entity_attributes_inserter - INFO - Entity attribute E44.At8 already exists in workflow_temp
2025-06-23 14:38:52,506 - inserters.v3.roles.entity_attributes_inserter - INFO - Entity attribute E44.At10 already exists in workflow_temp
2025-06-23 14:38:52,506 - inserters.v3.roles.entity_attributes_inserter - INFO - Completed process_mongo_entity_attributes_to_workflow_temp: 0 successful, 0 failed
2025-06-24 04:45:01,872 - inserters.v3.roles.entity_attributes_inserter - INFO - Starting deploy_single_entity_attribute_to_workflow_temp for attribute: ATTR001
2025-06-24 04:45:02,008 - inserters.v3.roles.entity_attributes_inserter - INFO - Starting process_mongo_entity_attributes_to_workflow_temp
2025-06-24 04:45:02,011 - inserters.v3.roles.entity_attributes_inserter - INFO - Found 4 entity attributes with status 'draft' in MongoDB
2025-06-24 04:45:02,019 - inserters.v3.roles.entity_attributes_inserter - INFO - Entity attribute E44.At2 already exists in workflow_temp
2025-06-24 04:45:02,027 - inserters.v3.roles.entity_attributes_inserter - INFO - Entity attribute E44.At4 already exists in workflow_temp
2025-06-24 04:45:02,035 - inserters.v3.roles.entity_attributes_inserter - INFO - Entity attribute E44.At8 already exists in workflow_temp
2025-06-24 04:45:02,043 - inserters.v3.roles.entity_attributes_inserter - INFO - Entity attribute E44.At10 already exists in workflow_temp
2025-06-24 04:45:02,043 - inserters.v3.roles.entity_attributes_inserter - INFO - Completed process_mongo_entity_attributes_to_workflow_temp: 0 successful, 0 failed
2025-06-24 04:57:39,485 - inserters.v3.roles.entity_attributes_inserter - INFO - Starting deploy_single_entity_attribute_to_workflow_temp for attribute: E44.At2
2025-06-24 04:59:39,049 - inserters.v3.roles.entity_attributes_inserter - INFO - Starting deploy_single_entity_attribute_to_workflow_temp for attribute: E44.At4
2025-06-24 04:59:51,874 - inserters.v3.roles.entity_attributes_inserter - INFO - Starting deploy_single_entity_attribute_to_workflow_temp for attribute: E44.At4
2025-06-24 11:41:08,614 - inserters.v3.roles.entity_attributes_inserter - INFO - Starting process_mongo_entity_attributes_to_workflow_runtime
2025-06-24 11:41:08,620 - inserters.v3.roles.entity_attributes_inserter - INFO - Found 18 deployed_to_temp + 4 draft = 22 total entity attributes in MongoDB
2025-06-24 11:41:08,629 - inserters.v3.roles.entity_attributes_inserter - INFO - Entity attribute E15.At1 already exists in workflow_runtime
2025-06-24 11:41:08,637 - inserters.v3.roles.entity_attributes_inserter - INFO - Entity attribute E15.At2 already exists in workflow_runtime
2025-06-24 11:41:08,646 - inserters.v3.roles.entity_attributes_inserter - INFO - Entity attribute E15.At3 already exists in workflow_runtime
2025-06-24 11:41:08,653 - inserters.v3.roles.entity_attributes_inserter - INFO - Starting insert_entity_attribute_to_workflow_runtime for attribute: E15.At4
2025-06-24 11:41:08,654 - inserters.v3.roles.entity_attributes_inserter - INFO - Optional fields missing from MongoDB data: ['required', 'calculated_field']
2025-06-24 11:41:08,654 - inserters.v3.roles.entity_attributes_inserter - INFO - Field validation passed: 1/1 required fields, 21/23 optional fields
2025-06-24 11:41:08,669 - inserters.v3.roles.entity_attributes_inserter - INFO - Generated next attribute ID: E15.At4 -> E15.At5
2025-06-24 11:41:08,669 - inserters.v3.roles.entity_attributes_inserter - INFO - Incremented attribute ID from E15.At4 to E15.At5
2025-06-24 11:41:08,669 - inserters.v3.roles.entity_attributes_inserter - INFO - Additional MongoDB fields preserved: {'original_created_at': '2025-06-20T07:48:02.727028', 'original_updated_at': '2025-06-20T14:04:00.512079', 'original_created_by': 'Tarun', 'original_updated_by': 'Tarun', 'original_attribute_id': 'E15.At4'}
2025-06-24 11:41:08,671 - inserters.v3.roles.entity_attributes_inserter - INFO - Successfully inserted entity attribute to workflow_runtime with ID: E15.At5
2025-06-24 11:41:08,674 - inserters.v3.roles.entity_attributes_inserter - INFO - Updated MongoDB status to deployed_to_production for attribute: E15.At5
2025-06-24 11:41:08,680 - inserters.v3.roles.entity_attributes_inserter - INFO - Entity attribute E13.At11 already exists in workflow_runtime
2025-06-24 11:41:08,687 - inserters.v3.roles.entity_attributes_inserter - INFO - Entity attribute E13.At10 already exists in workflow_runtime
2025-06-24 11:41:08,692 - inserters.v3.roles.entity_attributes_inserter - INFO - Entity attribute E13.At9 already exists in workflow_runtime
2025-06-24 11:41:08,699 - inserters.v3.roles.entity_attributes_inserter - INFO - Starting insert_entity_attribute_to_workflow_runtime for attribute: E44.At1
2025-06-24 11:41:08,699 - inserters.v3.roles.entity_attributes_inserter - INFO - Optional fields missing from MongoDB data: ['required', 'calculated_field']
2025-06-24 11:41:08,699 - inserters.v3.roles.entity_attributes_inserter - INFO - Field validation passed: 1/1 required fields, 21/23 optional fields
2025-06-24 11:41:08,710 - inserters.v3.roles.entity_attributes_inserter - INFO - Generated next attribute ID: E44.At1 -> E44.At14
2025-06-24 11:41:08,711 - inserters.v3.roles.entity_attributes_inserter - INFO - Incremented attribute ID from E44.At1 to E44.At14
2025-06-24 11:41:08,711 - inserters.v3.roles.entity_attributes_inserter - INFO - Additional MongoDB fields preserved: {'original_created_at': '2025-06-20T14:19:55.140980', 'original_updated_at': '2025-06-20T14:20:53.143140', 'original_created_by': 'Tarun', 'original_updated_by': 'Tarun', 'original_attribute_id': 'E44.At1'}
2025-06-24 11:41:08,713 - inserters.v3.roles.entity_attributes_inserter - INFO - Successfully inserted entity attribute to workflow_runtime with ID: E44.At14
2025-06-24 11:41:08,715 - inserters.v3.roles.entity_attributes_inserter - INFO - Updated MongoDB status to deployed_to_production for attribute: E44.At14
2025-06-24 11:41:08,722 - inserters.v3.roles.entity_attributes_inserter - INFO - Starting insert_entity_attribute_to_workflow_runtime for attribute: E44.At3
2025-06-24 11:41:08,723 - inserters.v3.roles.entity_attributes_inserter - INFO - Optional fields missing from MongoDB data: ['required', 'calculated_field']
2025-06-24 11:41:08,723 - inserters.v3.roles.entity_attributes_inserter - INFO - Field validation passed: 1/1 required fields, 21/23 optional fields
2025-06-24 11:41:08,737 - inserters.v3.roles.entity_attributes_inserter - INFO - Generated next attribute ID: E44.At3 -> E44.At15
2025-06-24 11:41:08,737 - inserters.v3.roles.entity_attributes_inserter - INFO - Incremented attribute ID from E44.At3 to E44.At15
2025-06-24 11:41:08,737 - inserters.v3.roles.entity_attributes_inserter - INFO - Additional MongoDB fields preserved: {'original_created_at': '2025-06-20T14:19:55.171349', 'original_updated_at': '2025-06-20T14:20:53.176389', 'original_created_by': 'Tarun', 'original_updated_by': 'Tarun', 'original_attribute_id': 'E44.At3'}
2025-06-24 11:41:08,739 - inserters.v3.roles.entity_attributes_inserter - INFO - Successfully inserted entity attribute to workflow_runtime with ID: E44.At15
2025-06-24 11:41:08,742 - inserters.v3.roles.entity_attributes_inserter - INFO - Updated MongoDB status to deployed_to_production for attribute: E44.At15
2025-06-24 11:41:08,748 - inserters.v3.roles.entity_attributes_inserter - INFO - Starting insert_entity_attribute_to_workflow_runtime for attribute: E44.At5
2025-06-24 11:41:08,748 - inserters.v3.roles.entity_attributes_inserter - INFO - Optional fields missing from MongoDB data: ['required', 'calculated_field']
2025-06-24 11:41:08,748 - inserters.v3.roles.entity_attributes_inserter - INFO - Field validation passed: 1/1 required fields, 21/23 optional fields
2025-06-24 11:41:08,762 - inserters.v3.roles.entity_attributes_inserter - INFO - Generated next attribute ID: E44.At5 -> E44.At16
2025-06-24 11:41:08,762 - inserters.v3.roles.entity_attributes_inserter - INFO - Incremented attribute ID from E44.At5 to E44.At16
2025-06-24 11:41:08,762 - inserters.v3.roles.entity_attributes_inserter - INFO - Additional MongoDB fields preserved: {'original_created_at': '2025-06-20T14:19:55.204274', 'original_updated_at': '2025-06-20T14:20:53.210561', 'original_created_by': 'Tarun', 'original_updated_by': 'Tarun', 'original_attribute_id': 'E44.At5'}
2025-06-24 11:41:08,764 - inserters.v3.roles.entity_attributes_inserter - INFO - Successfully inserted entity attribute to workflow_runtime with ID: E44.At16
2025-06-24 11:41:08,767 - inserters.v3.roles.entity_attributes_inserter - INFO - Updated MongoDB status to deployed_to_production for attribute: E44.At16
2025-06-24 11:41:08,774 - inserters.v3.roles.entity_attributes_inserter - INFO - Starting insert_entity_attribute_to_workflow_runtime for attribute: E44.At6
2025-06-24 11:41:08,774 - inserters.v3.roles.entity_attributes_inserter - INFO - Optional fields missing from MongoDB data: ['required', 'calculated_field']
2025-06-24 11:41:08,774 - inserters.v3.roles.entity_attributes_inserter - INFO - Field validation passed: 1/1 required fields, 21/23 optional fields
2025-06-24 11:41:08,785 - inserters.v3.roles.entity_attributes_inserter - INFO - Generated next attribute ID: E44.At6 -> E44.At17
2025-06-24 11:41:08,785 - inserters.v3.roles.entity_attributes_inserter - INFO - Incremented attribute ID from E44.At6 to E44.At17
2025-06-24 11:41:08,785 - inserters.v3.roles.entity_attributes_inserter - INFO - Additional MongoDB fields preserved: {'original_created_at': '2025-06-20T14:19:55.220066', 'original_updated_at': '2025-06-23T14:03:49.260036', 'original_created_by': 'Tarun', 'original_updated_by': 'Tarun', 'original_attribute_id': 'E44.At6'}
2025-06-24 11:41:08,788 - inserters.v3.roles.entity_attributes_inserter - INFO - Successfully inserted entity attribute to workflow_runtime with ID: E44.At17
2025-06-24 11:41:08,789 - inserters.v3.roles.entity_attributes_inserter - INFO - Updated MongoDB status to deployed_to_production for attribute: E44.At17
2025-06-24 11:41:08,794 - inserters.v3.roles.entity_attributes_inserter - INFO - Starting insert_entity_attribute_to_workflow_runtime for attribute: E44.At7
2025-06-24 11:41:08,794 - inserters.v3.roles.entity_attributes_inserter - INFO - Optional fields missing from MongoDB data: ['required', 'calculated_field']
2025-06-24 11:41:08,794 - inserters.v3.roles.entity_attributes_inserter - INFO - Field validation passed: 1/1 required fields, 21/23 optional fields
2025-06-24 11:41:08,805 - inserters.v3.roles.entity_attributes_inserter - INFO - Generated next attribute ID: E44.At7 -> E44.At18
2025-06-24 11:41:08,805 - inserters.v3.roles.entity_attributes_inserter - INFO - Incremented attribute ID from E44.At7 to E44.At18
2025-06-24 11:41:08,805 - inserters.v3.roles.entity_attributes_inserter - INFO - Additional MongoDB fields preserved: {'original_created_at': '2025-06-20T14:19:55.235248', 'original_updated_at': '2025-06-20T14:20:53.247928', 'original_created_by': 'Tarun', 'original_updated_by': 'Tarun', 'original_attribute_id': 'E44.At7'}
2025-06-24 11:41:08,807 - inserters.v3.roles.entity_attributes_inserter - INFO - Successfully inserted entity attribute to workflow_runtime with ID: E44.At18
2025-06-24 11:41:08,810 - inserters.v3.roles.entity_attributes_inserter - INFO - Updated MongoDB status to deployed_to_production for attribute: E44.At18
2025-06-24 11:41:08,815 - inserters.v3.roles.entity_attributes_inserter - INFO - Starting insert_entity_attribute_to_workflow_runtime for attribute: E44.At9
2025-06-24 11:41:08,815 - inserters.v3.roles.entity_attributes_inserter - INFO - Optional fields missing from MongoDB data: ['required', 'calculated_field']
2025-06-24 11:41:08,815 - inserters.v3.roles.entity_attributes_inserter - INFO - Field validation passed: 1/1 required fields, 21/23 optional fields
2025-06-24 11:41:08,825 - inserters.v3.roles.entity_attributes_inserter - INFO - Generated next attribute ID: E44.At9 -> E44.At19
2025-06-24 11:41:08,825 - inserters.v3.roles.entity_attributes_inserter - INFO - Incremented attribute ID from E44.At9 to E44.At19
2025-06-24 11:41:08,825 - inserters.v3.roles.entity_attributes_inserter - INFO - Additional MongoDB fields preserved: {'original_created_at': '2025-06-20T14:19:55.270902', 'original_updated_at': '2025-06-20T14:20:53.286452', 'original_created_by': 'Tarun', 'original_updated_by': 'Tarun', 'original_attribute_id': 'E44.At9'}
2025-06-24 11:41:08,827 - inserters.v3.roles.entity_attributes_inserter - INFO - Successfully inserted entity attribute to workflow_runtime with ID: E44.At19
2025-06-24 11:41:08,828 - inserters.v3.roles.entity_attributes_inserter - INFO - Updated MongoDB status to deployed_to_production for attribute: E44.At19
2025-06-24 11:41:08,833 - inserters.v3.roles.entity_attributes_inserter - INFO - Starting insert_entity_attribute_to_workflow_runtime for attribute: E44.At11
2025-06-24 11:41:08,833 - inserters.v3.roles.entity_attributes_inserter - INFO - Optional fields missing from MongoDB data: ['required', 'calculated_field']
2025-06-24 11:41:08,833 - inserters.v3.roles.entity_attributes_inserter - INFO - Field validation passed: 1/1 required fields, 21/23 optional fields
2025-06-24 11:41:08,847 - inserters.v3.roles.entity_attributes_inserter - INFO - Generated next attribute ID: E44.At11 -> E44.At20
2025-06-24 11:41:08,847 - inserters.v3.roles.entity_attributes_inserter - INFO - Incremented attribute ID from E44.At11 to E44.At20
2025-06-24 11:41:08,847 - inserters.v3.roles.entity_attributes_inserter - INFO - Additional MongoDB fields preserved: {'original_created_at': '2025-06-20T14:19:55.303369', 'original_updated_at': '2025-06-20T14:20:53.319983', 'original_created_by': 'Tarun', 'original_updated_by': 'Tarun', 'original_attribute_id': 'E44.At11'}
2025-06-24 11:41:08,849 - inserters.v3.roles.entity_attributes_inserter - INFO - Successfully inserted entity attribute to workflow_runtime with ID: E44.At20
2025-06-24 11:41:08,851 - inserters.v3.roles.entity_attributes_inserter - INFO - Updated MongoDB status to deployed_to_production for attribute: E44.At20
2025-06-24 11:41:08,857 - inserters.v3.roles.entity_attributes_inserter - INFO - Starting insert_entity_attribute_to_workflow_runtime for attribute: .At1
2025-06-24 11:41:08,857 - inserters.v3.roles.entity_attributes_inserter - INFO - Optional fields missing from MongoDB data: ['required', 'calculated_field']
2025-06-24 11:41:08,857 - inserters.v3.roles.entity_attributes_inserter - INFO - Field validation passed: 1/1 required fields, 21/23 optional fields
2025-06-24 11:41:08,862 - inserters.v3.roles.entity_attributes_inserter - WARNING - Invalid attribute ID format: .At1. Expected format: E1.At2
2025-06-24 11:41:08,862 - inserters.v3.roles.entity_attributes_inserter - INFO - Incremented attribute ID from .At1 to .At1
2025-06-24 11:41:08,862 - inserters.v3.roles.entity_attributes_inserter - INFO - Additional MongoDB fields preserved: {'original_created_at': '2025-06-23T06:36:14.276890', 'original_updated_at': '2025-06-23T14:03:49.290951', 'original_created_by': 'Tarun', 'original_updated_by': 'Tarun', 'original_attribute_id': '.At1'}
2025-06-24 11:41:08,865 - inserters.v3.roles.entity_attributes_inserter - INFO - Successfully inserted entity attribute to workflow_runtime with ID: .At1
2025-06-24 11:41:08,867 - inserters.v3.roles.entity_attributes_inserter - INFO - Updated MongoDB status to deployed_to_production for attribute: .At1
2025-06-24 11:41:08,873 - inserters.v3.roles.entity_attributes_inserter - INFO - Starting insert_entity_attribute_to_workflow_runtime for attribute: .At2
2025-06-24 11:41:08,873 - inserters.v3.roles.entity_attributes_inserter - INFO - Optional fields missing from MongoDB data: ['required', 'calculated_field']
2025-06-24 11:41:08,874 - inserters.v3.roles.entity_attributes_inserter - INFO - Field validation passed: 1/1 required fields, 21/23 optional fields
2025-06-24 11:41:08,879 - inserters.v3.roles.entity_attributes_inserter - WARNING - Invalid attribute ID format: .At2. Expected format: E1.At2
2025-06-24 11:41:08,879 - inserters.v3.roles.entity_attributes_inserter - INFO - Incremented attribute ID from .At2 to .At2
2025-06-24 11:41:08,879 - inserters.v3.roles.entity_attributes_inserter - INFO - Additional MongoDB fields preserved: {'original_created_at': '2025-06-23T06:36:14.286765', 'original_updated_at': '2025-06-23T14:03:49.306821', 'original_created_by': 'Tarun', 'original_updated_by': 'Tarun', 'original_attribute_id': '.At2'}
2025-06-24 11:41:08,881 - inserters.v3.roles.entity_attributes_inserter - INFO - Successfully inserted entity attribute to workflow_runtime with ID: .At2
2025-06-24 11:41:08,883 - inserters.v3.roles.entity_attributes_inserter - INFO - Updated MongoDB status to deployed_to_production for attribute: .At2
2025-06-24 11:41:08,890 - inserters.v3.roles.entity_attributes_inserter - INFO - Starting insert_entity_attribute_to_workflow_runtime for attribute: .At3
2025-06-24 11:41:08,890 - inserters.v3.roles.entity_attributes_inserter - INFO - Optional fields missing from MongoDB data: ['required', 'calculated_field']
2025-06-24 11:41:08,890 - inserters.v3.roles.entity_attributes_inserter - INFO - Field validation passed: 1/1 required fields, 21/23 optional fields
2025-06-24 11:41:08,895 - inserters.v3.roles.entity_attributes_inserter - WARNING - Invalid attribute ID format: .At3. Expected format: E1.At2
2025-06-24 11:41:08,895 - inserters.v3.roles.entity_attributes_inserter - INFO - Incremented attribute ID from .At3 to .At3
2025-06-24 11:41:08,895 - inserters.v3.roles.entity_attributes_inserter - INFO - Additional MongoDB fields preserved: {'original_created_at': '2025-06-23T06:36:14.298690', 'original_updated_at': '2025-06-23T14:03:49.322301', 'original_created_by': 'Tarun', 'original_updated_by': 'Tarun', 'original_attribute_id': '.At3'}
2025-06-24 11:41:08,897 - inserters.v3.roles.entity_attributes_inserter - INFO - Successfully inserted entity attribute to workflow_runtime with ID: .At3
2025-06-24 11:41:08,899 - inserters.v3.roles.entity_attributes_inserter - INFO - Updated MongoDB status to deployed_to_production for attribute: .At3
2025-06-24 11:41:08,905 - inserters.v3.roles.entity_attributes_inserter - INFO - Starting insert_entity_attribute_to_workflow_runtime for attribute: E45.At4
2025-06-24 11:41:08,905 - inserters.v3.roles.entity_attributes_inserter - INFO - Optional fields missing from MongoDB data: ['required', 'calculated_field']
2025-06-24 11:41:08,905 - inserters.v3.roles.entity_attributes_inserter - INFO - Field validation passed: 1/1 required fields, 21/23 optional fields
2025-06-24 11:41:08,917 - inserters.v3.roles.entity_attributes_inserter - INFO - Generated next attribute ID: E45.At4 -> E45.At6
2025-06-24 11:41:08,917 - inserters.v3.roles.entity_attributes_inserter - INFO - Incremented attribute ID from E45.At4 to E45.At6
2025-06-24 11:41:08,917 - inserters.v3.roles.entity_attributes_inserter - INFO - Additional MongoDB fields preserved: {'original_created_at': '2025-06-23T06:36:14.308383', 'original_updated_at': '2025-06-23T14:03:49.346904', 'original_created_by': 'Tarun', 'original_updated_by': 'Tarun', 'original_attribute_id': 'E45.At4'}
2025-06-24 11:41:08,919 - inserters.v3.roles.entity_attributes_inserter - INFO - Successfully inserted entity attribute to workflow_runtime with ID: E45.At6
2025-06-24 11:41:08,922 - inserters.v3.roles.entity_attributes_inserter - INFO - Updated MongoDB status to deployed_to_production for attribute: E45.At6
2025-06-24 11:41:08,926 - inserters.v3.roles.entity_attributes_inserter - INFO - Starting insert_entity_attribute_to_workflow_runtime for attribute: E44.At2
2025-06-24 11:41:08,926 - inserters.v3.roles.entity_attributes_inserter - INFO - Optional fields missing from MongoDB data: ['required', 'calculated_field']
2025-06-24 11:41:08,926 - inserters.v3.roles.entity_attributes_inserter - INFO - Field validation passed: 1/1 required fields, 21/23 optional fields
2025-06-24 11:41:08,936 - inserters.v3.roles.entity_attributes_inserter - INFO - Generated next attribute ID: E44.At2 -> E44.At21
2025-06-24 11:41:08,936 - inserters.v3.roles.entity_attributes_inserter - INFO - Incremented attribute ID from E44.At2 to E44.At21
2025-06-24 11:41:08,936 - inserters.v3.roles.entity_attributes_inserter - INFO - Additional MongoDB fields preserved: {'original_created_at': '2025-06-20T14:19:55.155754', 'original_updated_at': '2025-06-20T14:19:55.155760', 'original_created_by': 'Tarun', 'original_updated_by': 'Tarun', 'original_attribute_id': 'E44.At2'}
2025-06-24 11:41:08,938 - inserters.v3.roles.entity_attributes_inserter - INFO - Successfully inserted entity attribute to workflow_runtime with ID: E44.At21
2025-06-24 11:41:08,940 - inserters.v3.roles.entity_attributes_inserter - INFO - Updated MongoDB status to deployed_to_production for attribute: E44.At21
2025-06-24 11:41:08,944 - inserters.v3.roles.entity_attributes_inserter - INFO - Starting insert_entity_attribute_to_workflow_runtime for attribute: E44.At4
2025-06-24 11:41:08,944 - inserters.v3.roles.entity_attributes_inserter - INFO - Optional fields missing from MongoDB data: ['required', 'calculated_field']
2025-06-24 11:41:08,945 - inserters.v3.roles.entity_attributes_inserter - INFO - Field validation passed: 1/1 required fields, 21/23 optional fields
2025-06-24 11:41:08,958 - inserters.v3.roles.entity_attributes_inserter - INFO - Generated next attribute ID: E44.At4 -> E44.At22
2025-06-24 11:41:08,958 - inserters.v3.roles.entity_attributes_inserter - INFO - Incremented attribute ID from E44.At4 to E44.At22
2025-06-24 11:41:08,958 - inserters.v3.roles.entity_attributes_inserter - INFO - Additional MongoDB fields preserved: {'original_created_at': '2025-06-20T14:19:55.187810', 'original_updated_at': '2025-06-20T14:19:55.187815', 'original_created_by': 'Tarun', 'original_updated_by': 'Tarun', 'original_attribute_id': 'E44.At4'}
2025-06-24 11:41:08,960 - inserters.v3.roles.entity_attributes_inserter - INFO - Successfully inserted entity attribute to workflow_runtime with ID: E44.At22
2025-06-24 11:41:08,963 - inserters.v3.roles.entity_attributes_inserter - INFO - Updated MongoDB status to deployed_to_production for attribute: E44.At22
2025-06-24 11:41:08,967 - inserters.v3.roles.entity_attributes_inserter - INFO - Starting insert_entity_attribute_to_workflow_runtime for attribute: E44.At8
2025-06-24 11:41:08,968 - inserters.v3.roles.entity_attributes_inserter - INFO - Optional fields missing from MongoDB data: ['required', 'calculated_field']
2025-06-24 11:41:08,968 - inserters.v3.roles.entity_attributes_inserter - INFO - Field validation passed: 1/1 required fields, 21/23 optional fields
2025-06-24 11:41:08,977 - inserters.v3.roles.entity_attributes_inserter - INFO - Generated next attribute ID: E44.At8 -> E44.At23
2025-06-24 11:41:08,977 - inserters.v3.roles.entity_attributes_inserter - INFO - Incremented attribute ID from E44.At8 to E44.At23
2025-06-24 11:41:08,977 - inserters.v3.roles.entity_attributes_inserter - INFO - Additional MongoDB fields preserved: {'original_created_at': '2025-06-20T14:19:55.252699', 'original_updated_at': '2025-06-20T14:19:55.252705', 'original_created_by': 'Tarun', 'original_updated_by': 'Tarun', 'original_attribute_id': 'E44.At8'}
2025-06-24 11:41:08,979 - inserters.v3.roles.entity_attributes_inserter - INFO - Successfully inserted entity attribute to workflow_runtime with ID: E44.At23
2025-06-24 11:41:08,981 - inserters.v3.roles.entity_attributes_inserter - INFO - Updated MongoDB status to deployed_to_production for attribute: E44.At23
2025-06-24 11:41:08,985 - inserters.v3.roles.entity_attributes_inserter - INFO - Starting insert_entity_attribute_to_workflow_runtime for attribute: E44.At10
2025-06-24 11:41:08,985 - inserters.v3.roles.entity_attributes_inserter - INFO - Optional fields missing from MongoDB data: ['required', 'calculated_field']
2025-06-24 11:41:08,985 - inserters.v3.roles.entity_attributes_inserter - INFO - Field validation passed: 1/1 required fields, 21/23 optional fields
2025-06-24 11:41:08,996 - inserters.v3.roles.entity_attributes_inserter - INFO - Generated next attribute ID: E44.At10 -> E44.At24
2025-06-24 11:41:08,996 - inserters.v3.roles.entity_attributes_inserter - INFO - Incremented attribute ID from E44.At10 to E44.At24
2025-06-24 11:41:08,996 - inserters.v3.roles.entity_attributes_inserter - INFO - Additional MongoDB fields preserved: {'original_created_at': '2025-06-20T14:19:55.287901', 'original_updated_at': '2025-06-20T14:19:55.287906', 'original_created_by': 'Tarun', 'original_updated_by': 'Tarun', 'original_attribute_id': 'E44.At10'}
2025-06-24 11:41:08,998 - inserters.v3.roles.entity_attributes_inserter - INFO - Successfully inserted entity attribute to workflow_runtime with ID: E44.At24
2025-06-24 11:41:09,000 - inserters.v3.roles.entity_attributes_inserter - INFO - Updated MongoDB status to deployed_to_production for attribute: E44.At24
2025-06-24 11:41:09,000 - inserters.v3.roles.entity_attributes_inserter - INFO - Completed process_mongo_entity_attributes_to_workflow_runtime: 16 successful, 0 failed
