#!/usr/bin/env python3
"""
Script to drop and recreate the workflow_temp schema.
"""

import os
import sys
import logging

# Add parent directory to path to import common modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from common.db_connection import get_db_connection
from db_utils import execute_query

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger('recreate_temp_schema')

def main():
    """
    Main function.
    """
    # Drop the workflow_temp schema
    drop_query = """
        DROP SCHEMA IF EXISTS workflow_temp CASCADE
    """
    
    success, messages, _ = execute_query(drop_query)
    
    if not success:
        logger.error(f"Failed to drop schema workflow_temp: {messages}")
        return False
    
    logger.info("Dropped schema workflow_temp")
    
    # Create the workflow_temp schema
    create_query = """
        CREATE SCHEMA workflow_temp
    """
    
    success, messages, _ = execute_query(create_query)
    
    if not success:
        logger.error(f"Failed to create schema workflow_temp: {messages}")
        return False
    
    logger.info("Created schema workflow_temp")
    
    # Copy all tables from workflow_runtime to workflow_temp
    copy_query = """
        SELECT table_name
        FROM information_schema.tables
        WHERE table_schema = 'workflow_runtime'
        AND table_type = 'BASE TABLE'
    """
    
    success, messages, result = execute_query(copy_query)
    
    if not success:
        logger.error(f"Failed to get tables from workflow_runtime: {messages}")
        return False
    
    tables = [row[0] for row in result]
    logger.info(f"Found {len(tables)} tables in workflow_runtime")
    
    for table in tables:
        # Get the table definition
        table_def_query = f"""
            SELECT column_name, data_type, column_default, is_nullable
            FROM information_schema.columns
            WHERE table_schema = 'workflow_runtime'
            AND table_name = '{table}'
            ORDER BY ordinal_position
        """
        
        success, messages, columns = execute_query(table_def_query)
        
        if not success:
            logger.error(f"Failed to get columns for table {table}: {messages}")
            continue
        
        # Create the table in workflow_temp
        column_defs = []
        for column in columns:
            column_name = column[0]
            data_type = column[1]
            default_value = column[2]
            is_nullable = column[3]
            
            column_def = f"{column_name} {data_type}"
            
            if default_value:
                column_def += f" DEFAULT {default_value}"
            
            if is_nullable == 'NO':
                column_def += " NOT NULL"
            
            column_defs.append(column_def)
        
        create_table_query = f"""
            CREATE TABLE workflow_temp.{table} (
                {', '.join(column_defs)}
            )
        """
        
        success, messages, _ = execute_query(create_table_query)
        
        if not success:
            logger.error(f"Failed to create table workflow_temp.{table}: {messages}")
            continue
        
        logger.info(f"Created table workflow_temp.{table}")
    
    logger.info("Successfully recreated workflow_temp schema")
    return True

if __name__ == '__main__':
    main()
