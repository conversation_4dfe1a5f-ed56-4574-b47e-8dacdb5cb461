# save_to_mongo.py

import yaml
import pymongo

MONGO_URI = "mongodb://localhost:27017/"
DATABASE_NAME = "workflow_system"
COLLECTION_NAME = "workflow"

def connect_mongo():
    client = pymongo.MongoClient(MONGO_URI)
    return client[DATABASE_NAME]

def save_enriched_yaml(parsed_data):
    try:
        db = connect_mongo()
        #parsed_data = yaml.safe_load(enriched_yaml_text)

        document = {
            "workflow_data": parsed_data,
            "status": "draft"
        }

        db[COLLECTION_NAME].insert_one(document)
        print(f"✅ Enriched YAML saved to MongoDB in `{COLLECTION_NAME}` collection.")
    except Exception as e:
        print(f"❌ Failed to save enriched YAML: {e}")
