#!/usr/bin/env python3
"""
Test script to verify that validation rules are correctly parsed from the entity definition.
"""

import os
import json
import logging
from parsers.entity_parser import parse_entities

# Set up logging
logging.basicConfig(level=logging.INFO, 
                   format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
                   handlers=[
                       logging.FileHandler('/home/<USER>/workflow-system/chat-yaml-builder/v2/validation_parsing_output.log'),
                       logging.StreamHandler()
                   ])
logger = logging.getLogger('test_validation_parsing')

def test_validation_parsing():
    """
    Test that validation rules are correctly parsed from the entity definition.
    """
    # Read the sample entity output file
    sample_file_path = '/home/<USER>/workflow-system/chat-yaml-builder/v2/samples/sample_entity_output2.txt'
    with open(sample_file_path, 'r') as f:
        entity_def = f.read()
    
    # Parse the entity definition
    entities_data, warnings = parse_entities(entity_def)
    
    # Print any warnings
    if warnings:
        logger.warning("Warnings during parsing:")
        for warning in warnings:
            logger.warning(f"  - {warning}")
    
    # Check if Employee entity was parsed
    if 'entities' in entities_data and 'Employee' in entities_data['entities']:
        employee_entity = entities_data['entities']['Employee']
        
        # Check if validations were parsed
        if 'validations' in employee_entity:
            logger.info("\nEmployee validations:")
            for val_name, val_def in employee_entity['validations'].items():
                logger.info(f"\n  - Validation: {val_name}")
                for prop_name, prop_value in val_def.items():
                    logger.info(f"    - {prop_name}: {prop_value}")
        else:
            logger.warning("No validations found in Employee entity")
            
            # Check if there are any validation-like entries in the entity definition
            logger.info("\nSearching for validation-like entries in the entity definition:")
            validation_lines = [line for line in entity_def.split('\n') if 'must' in line and 'Employee.' in line]
            for line in validation_lines:
                logger.info(f"  - {line.strip()}")
    else:
        logger.error("Employee entity not found in parsed data")
    
    # Print the full parsed data as JSON
    logger.info("\nFull parsed data:")
    logger.info(json.dumps(entities_data, indent=2))

if __name__ == "__main__":
    test_validation_parsing()
