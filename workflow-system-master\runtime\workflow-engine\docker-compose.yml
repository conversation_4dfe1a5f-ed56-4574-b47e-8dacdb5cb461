version: '3.8'

services:
  workflow-engine:
    build:
      context: ./runtime/workflow-engine
      dockerfile: Dockerfile
    container_name: workflow_engine
    restart: always
    ports:
      - "8000:8000"
    volumes:
      - ./runtime/workflow-engine:/app
    environment:
      - PYTHONUNBUFFERED=1
    depends_on:
      - mongodb
      - postgres
      - redis

  # Other services from your existing docker-compose.yml
  mongodb:
    # ... (keep existing MongoDB configuration)

  postgres:
    # ... (keep existing PostgreSQL configuration)

  redis:
    # ... (keep existing Redis configuration)

networks:
  workflow_network:
    driver: bridge

volumes:
  # ... (keep existing volume configurations)
