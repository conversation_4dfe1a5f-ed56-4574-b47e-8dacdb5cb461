import yaml
import os

# Define expected YAML structure with full validation for execution_rules, success_message_template, output_stack, and ui_stack
expected_structure = {
    "tenant": {
        "id": str,
        "name": str,
        "roles": [
            {
                "id": str,
                "name": str,
                "inherits_from": (str, type(None)),
                "access": {
                    "entities": [
                        {
                            "entity_id": str,
                            "permissions": list
                        }
                    ],
                    "objectives": [
                        {
                            "objective_id": str,
                            "permissions": list
                        }
                    ]
                }
            }
        ]
    },
    "permission_types": [
        {
            "id": str,
            "description": str,
            "capabilities": list
        }
    ],
    "entities": [
        {
            "id": str,
            "name": str,
            "version": str,
            "status": str,
            "type": str,
            "attributes_metadata": {
                "attribute_prefix": str,
                "attribute_map": dict,
                "required_attributes": list
            },
            "attributes": [
                {
                    "id": str,
                    "name": str,
                    "display_name": str,
                    "datatype": str,
                    "version": str,
                    "status": str,
                    "required": bool
                }
            ],
            "nested_entities": (list, type(None))  # Optional
        }
    ],
    "global_objectives": (list, type(None)),  # Optional, will validate if present
    "local_objectives": [
        {
            "id": str,
            "contextual_id": str,
            "name": str,
            "function_type": str,
            "workflow_source": str,
            "execution_pathway": {
                "type": str,
                "next_lo": (str, type(None))  # Optional
            },
            "agent_stack": {
                "agents": [
                    {
                        "role": str,
                        "rights": list
                    }
                ]
            },
            "input_stack": {
                "description": str,
                "inputs": [
                    {
                        "id": str,
                        "slot_id": str,
                        "contextual_id": str,
                        "source": {
                            "type": str,
                            "description": str,
                            "data_source": (dict, type(None))  # Optional
                        },
                        "required": bool,
                        "validations": (list, type(None)),  # Optional
                        "access_roles": (list, type(None))  # Optional
                    }
                ]
            },
            "execution_rules": [  # **Full Structure Validation**
                {
                    "id": str,
                    "contextual_id": str,
                    "description": str,
                    "rule_condition": {
                        "condition_type": str,
                        "function_name": str,
                        "parameters": dict
                    },
                    "action": {
                        "action_type": str,
                        "function_name": str,
                        "parameters": dict,
                        "output_attribute": (str, type(None)),  # Optional
                        "output_variable": (str, type(None))  # Optional
                    }
                }
            ],
            "success_message_template": {  # **Full Structure Validation**
                "message_type": str,
                "format_string": str,
                "format_parameters": [
                    {
                        "entity": str,
                        "attribute": str
                    }
                ]
            },
            "output_stack": {  # **Full Structure Validation**
                "description": str,
                "outputs": [
                    {
                        "id": str,
                        "slot_id": str,
                        "contextual_id": str,
                        "source": {
                            "type": str,
                            "description": str,
                            "data_source": (dict, type(None))  # Optional
                        },
                        "output_entity": (str, type(None)),  # Optional
                        "output_attribute": (str, type(None)),  # Optional
                        "required": (bool, type(None)),  # Optional
                        "data_type": (str, type(None))  # Optional
                    }
                ]
            },
            "ui_stack": {  # **Full Structure Validation**
                "type": str,
                "status": str,
                "description": str,
                "overall_control": str,
                "form_title": str,
                "submit_button_text": str,
                "cancel_button_text": (str, type(None)),  # Optional
                "elements": [
                    {
                        "entity_attribute": str,
                        "ui_control": str,
                        "helper_text": (str, type(None)),  # Optional
                        "error_message": (str, type(None))  # Optional
                    }
                ]
            }
        }
    ]
}


def load_yaml(file_path):
    """ Loads the YAML file """
    try:
        with open(file_path, "r", encoding="utf-8") as file:
            return yaml.safe_load(file)
    except yaml.YAMLError as e:
        print(f"❌ Error loading YAML file: {e}")
    except FileNotFoundError:
        print(f"❌ Error: File {file_path} not found.")
    return None


def validate_structure(yaml_data, expected_structure, path=""):
    """ Validates the structure of YAML data against the expected schema """
    errors = []
    attribute_ids = {}  # Dictionary to track attribute ID occurrences

    def check_key(key_path, data, expected_type):
        """ Helper function to check the type of a key """
        if isinstance(expected_type, list):
            if not isinstance(data, list):
                errors.append(f"❌ {key_path} should be a list but found {type(data).__name__}")
            else:
                for i, item in enumerate(data):
                    if isinstance(item, dict):
                        check_structure(f"{key_path}[{i}]", item, expected_type[0])
                    elif not isinstance(item, expected_type[0]):
                        errors.append(f"❌ {key_path}[{i}] should be {expected_type[0].__name__} but found {type(item).__name__}")
        elif isinstance(expected_type, tuple):
            if data is not None and not isinstance(data, expected_type):
                errors.append(f"❌ {key_path} should be one of {expected_type} but found {type(data).__name__}")
        elif isinstance(expected_type, dict):
            if not isinstance(data, dict):
                errors.append(f"❌ {key_path} should be a dictionary but found {type(data).__name__}")
            else:
                check_structure(key_path, data, expected_type)
        else:
            if not isinstance(data, expected_type):
                errors.append(f"❌ {key_path} should be {expected_type.__name__}, but found {type(data).__name__}")

    def check_structure(parent_path, data, expected_keys):
        """ Recursively validates a dictionary structure """
        for key, expected_type in expected_keys.items():
            key_path = f"{parent_path}.{key}" if parent_path else key

            if isinstance(expected_type, tuple) and data.get(key) is None:
                continue

            if key in data:
                check_key(key_path, data[key], expected_type)
            else:
                if not isinstance(expected_type, tuple):
                    errors.append(f"⚠️ Missing key: {key_path}")

    check_structure("", yaml_data, expected_structure)
    return errors


def validate_yaml(file_path):
    """ Load and validate the YAML file """
    yaml_data = load_yaml(file_path)
    if yaml_data is None:
        print("❌ Error: YAML file could not be loaded.")
        return

    print(f"🔍 Validating structure for {file_path}...")
    errors = validate_structure(yaml_data, expected_structure)
    if errors:
        print("\n".join(errors))
    else:
        print("✅ YAML structure is valid!")


if __name__ == "__main__":
    yaml_file = "hrms.yaml"
    if os.path.exists(yaml_file):
        validate_yaml(yaml_file)
    else:
        print(f"❌ Error: {yaml_file} not found in the current directory.")
