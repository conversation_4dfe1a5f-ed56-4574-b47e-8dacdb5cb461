from fastapi import FastAP<PERSON>, HTTPException, APIRouter, Request
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
from typing import List, Optional
from openai import OpenAI
from pymongo import MongoClient
from validate_yaml import validate_yaml
from pipeline_runner import run_pipeline_from_text
from dotenv import load_dotenv
import os
import uuid
from datetime import datetime

load_dotenv()

client = OpenAI(api_key=os.getenv("OPENAI_API_KEY"))
mongo = MongoClient(os.getenv("MONGODB_URI"))
db = mongo["workflow_chat"]
convo_col = db["conversations"]

# Load templates
with open("/home/<USER>/workflow-system/chat-yaml-builder/final_chatgpt_system_prompt.yaml.txt", "r") as f:
    system_prompt = f.read()

with open("/home/<USER>/workflow-system/chat-yaml-builder/prescriptive_prompt_template.txt", "r") as f:
    prescriptive_template = f.read()

with open("/home/<USER>/workflow-system/chat-yaml-builder/java_prompt_template.txt", "r") as f:
    java_template = f.read()

# Init
app = FastAPI()
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_methods=["*"],
    allow_headers=["*"],
)

# Data models
class ChatRequest(BaseModel):
    message: str
    conversation_id: Optional[str] = None

class ChatResponse(BaseModel):
    conversation_id: str
    yaml_output: Optional[str]
    prescriptive_text: Optional[str]
    java_code: Optional[str]
    validation_status: str
    validation_error: Optional[str] = None

class SavedSolution(BaseModel):
    conversation_id: str
    yaml_version: str
    yaml_content: str
    chat_history: List[dict]
    prescriptive_text: Optional[str] = ""
    java_code: Optional[str] = ""


# Helpers
def get_prescriptive_prompt_from_yaml(yaml_text: str) -> str:
    return prescriptive_template.replace("[INSERT YAML HERE]", f"\n```yaml\n{yaml_text.strip()}\n```")

def get_java_prompt_from_yaml(yaml_text: str) -> str:
    return java_template.replace("[INSERT YAML HERE]", f"\n```yaml\n{yaml_text.strip()}\n```")

def save_generated_content(conversation_id, user_message, yaml_output, prescriptive_text, java_code):
    latest = list(convo_col.find({"conversation_id": conversation_id}).sort("version", -1).limit(1))
    latest_version = latest[0]["version"] if latest else 0  # ✅ FIXED

    new_doc = {
        "conversation_id": conversation_id,
        "version": latest_version + 1,
        "user_message": user_message,
        "yaml_output": yaml_output,
        "prescriptive_text": prescriptive_text,
        "java_code": java_code,
        "status": "draft",
        "deployment_id": None,
        "created_on": datetime.utcnow(),
        "deployed_on": None
    }

    result = convo_col.insert_one(new_doc)
    return str(result.inserted_id), new_doc["version"]


# Main Chat Endpoint
@app.post("/api/chat", response_model=ChatResponse)
def chat(chat_req: ChatRequest):
    user_message = chat_req.message.strip()
    if not user_message:
        raise HTTPException(status_code=400, detail="Empty message")

    conversation_id = chat_req.conversation_id or f"chat_{uuid.uuid4().hex[:6]}"

    messages = [
        {"role": "system", "content": system_prompt},
        {"role": "user", "content": user_message}
    ]

    response = client.chat.completions.create(
        model="gpt-4-1106-preview",
        messages=messages,
        temperature=0.2,
    )
    reply = response.choices[0].message.content.strip()

    yaml_output = ""
    prescriptive_text = ""
    java_code = ""

    if "```yaml" in reply:
        parts = reply.split("```yaml")
        prescriptive_text = parts[0].strip()
        yaml_output = parts[1].replace("```", "").strip()

        prescriptive_prompt = get_prescriptive_prompt_from_yaml(yaml_output)
        prescriptive_response = client.chat.completions.create(
            model="gpt-4-1106-preview",
            messages=[
                {"role": "system", "content": "You are a workflow documentation expert."},
                {"role": "user", "content": prescriptive_prompt}
            ],
            temperature=0.2,
        )
        prescriptive_text = prescriptive_response.choices[0].message.content.strip()

        java_prompt = get_java_prompt_from_yaml(yaml_output)
        java_response = client.chat.completions.create(
            model="gpt-4-1106-preview",
            messages=[
                {"role": "system", "content": "You are a Java developer. Only output valid Java code."},
                {"role": "user", "content": java_prompt}
            ],
            temperature=0.2,
        )
        java_code = java_response.choices[0].message.content.strip()
    else:
        prescriptive_text = reply

    validation_result = validate_yaml(yaml_output)
    validation_status = "valid" if validation_result["valid"] else "invalid"

    # save_generated_content(conversation_id, user_message, yaml_output, prescriptive_text, java_code)

    inserted_id, _ = save_generated_content(
        conversation_id=conversation_id,
        user_message=user_message,
        yaml_output=yaml_output,
        prescriptive_text=prescriptive_text,
        java_code=java_code
    )

    convo_col.delete_many({"conversation_id": {"$exists": False}})



    return ChatResponse(
    conversation_id=conversation_id,
    yaml_output=yaml_output,
    prescriptive_text=prescriptive_text,
    java_code=java_code,
    validation_status=validation_status,
    validation_error=None if validation_result["valid"] else validation_result["error"]
)

@app.post("/api/deploy_by_id")
def deploy_yaml_by_id(req: dict):
    deployment_id = req.get("deployment_id")
    conversation_id = req.get("conversation_id")

    if not deployment_id:
        if not conversation_id:
            raise HTTPException(status_code=400, detail="deployment_id or conversation_id required")

        latest = list(convo_col.find({"conversation_id": conversation_id}).sort("version", -1).limit(1))
        if not latest:
            raise HTTPException(status_code=404, detail="No record found for conversation")
        record = latest[0]

        deployment_id = f"dwf{record['version']:03d}"
        convo_col.update_one({"_id": record["_id"]}, {
            "$set": {
                "deployment_id": deployment_id,
                "status": "published",
                "deployed_on": datetime.utcnow()
            }
        })
    else:
        record = convo_col.find_one({"deployment_id": deployment_id})

    if not record:
        raise HTTPException(status_code=404, detail="Deployment ID not found")

    yaml_text = record.get("yaml_output")
    success, msg = run_pipeline_from_text(yaml_text)

    return {
        "success": success,
        "message": msg,
        "deployment_id": deployment_id
    }

@app.get("/api/solutions", response_model=List[SavedSolution])
def get_saved_solutions():
    raw_solutions = convo_col.find({}, {
        "_id": 0,
        "conversation_id": 1,
        "yaml_output": 1,
        "prescriptive_text": 1,
        "java_code": 1,
        "version": 1,
        "chat_history": 1
    }).sort("created_on", -1)

    solutions = []

    for doc in raw_solutions:
        try:
            # Ensure required fields have safe defaults
            doc["yaml_version"] = f"v-{doc.get('version', 0):06x}"
            doc["yaml_content"] = doc.pop("yaml_output", "")
            doc["prescriptive_text"] = doc.get("prescriptive_text", "")
            doc["java_code"] = doc.get("java_code", "")
            doc["chat_history"] = doc.get("chat_history", [])
            doc["conversation_id"] = doc.get("conversation_id", "unknown")

            # Let Pydantic validate it properly now
            solutions.append(SavedSolution(**doc))
        except Exception as e:
            print("⚠️ Skipping malformed record:", doc)
            print("Reason:", e)

    return solutions

# Run the FastAPI app on port 8020
if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8020)
