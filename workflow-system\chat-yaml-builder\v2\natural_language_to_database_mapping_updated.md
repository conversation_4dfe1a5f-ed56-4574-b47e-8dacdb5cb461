# Natural Language Prescriptives to Database Mapping

This document provides a comprehensive mapping of natural language prescriptives to database tables in the Postgres database. It includes detailed information about how each component of the prescriptive language is parsed, validated, and stored in the database.

## Table of Contents

1. [Entity Mapping](#entity-mapping)
2. [Global Objective (GO) Mapping](#global-objective-mapping)
3. [Local Objective (LO) Mapping](#local-objective-mapping)
4. [Role Mapping](#role-mapping)
5. [Validation Rules](#validation-rules)
6. [Implementation Plan](#implementation-plan)

## Entity Mapping

Entities are defined in natural language prescriptives and mapped to multiple database tables.

### Entity Definition

Natural language format (based on sample_entity_output2.txt):
```
EntityName has attribute1^PK, attribute2, attribute3 (enum1, enum2), attribute4^FK, attribute5[derived].

Relationships for EntityName:
EntityName has relationship-type relationship with TargetEntity using EntityName.attribute to TargetEntity.attribute
EntityName has relationship-type relationship with TargetEntity using EntityName.attribute to TargetEntity.attribute

Defaults for EntityName:
EntityName.attribute PROPERTY_NAME = value
EntityName.attribute DEFAULT_VALUE = value

Validations for EntityName:
EntityName.attribute must be unique
EntityName.attribute must be greater than value
EntityName.attribute must be greater than or equal to value
EntityName.attribute must not be in the future
EntityName.attribute must be after otherAttribute

BusinessRule for EntityName:

rule_name_1
Inputs: EntityName with attribute1, attribute2, attribute3

Operation: conditional_logic({condition details}, {success details}, {failure details}, EntityName.result)

Description: Detailed description of the business rule

Output: EntityName with result

Error: Error message when rule fails

Validation: When to apply the rule (e.g., PRE_UPDATE)

rule_name_2
Inputs: EntityName with attribute1; RelatedEntity with attribute1, attribute2

Operation: complex operation with multiple steps and conditions

Description: Detailed description of the business rule

Output: EntityName with result1, result2

Error: Error message when rule fails

Trigger: When to trigger the rule (e.g., POST_UPDATE)

CalculatedField for EntityName.calculatedAttribute:
* Formula: calculation formula using attributes and functions
* Logic Layer: Database or Application
* Caching: Request or Session
* Dependencies: EntityName.attribute1, EntityName.attribute2

EntityName has attribute1^FK, attribute2^FK with TargetEntity constrains ConstrainedEntity.

EntityName.attribute must belong to selected EntityName.otherAttribute

Entity Additional Properties:
Display Name: Friendly Name
Type: Core Entity
Description: Entity description

Attribute Additional Properties:
Attribute name: attributeName
Key: Primary or Foreign or Non-unique
Display Name: Friendly Attribute Name
Data Type: Integer or Decimal or String or Date or Enum
Type: Mandatory or Optional or Calculated
Format: Format pattern
Values: Range or list of values
Default: Default value
Validation: Validation type
Error Message: "Error message"
Description: Attribute description

Relationship: EntityName to TargetEntity

Relationship Properties:
On Delete: Restrict or Cascade or Set Null
On Update: Restrict or Cascade
Foreign Key Type: Non-Nullable or Nullable

Synthetic: EntityName has attribute1 = value1, attribute2 = value2, attribute3 = value3.
EntityName has attribute1 = value4, attribute2 = value5, attribute3 = value6.

Confidential: EntityName.attribute1, EntityName.attribute2

Internal: EntityName.attribute3, EntityName.attribute4
Public: EntityName.attribute5, EntityName.attribute6

Loading for EntityName.RelatedEntity1: Eager Loading
Loading for EntityName.RelatedEntity2: Lazy Loading

Archive Strategy for EntityName:
Trigger: Event-based or Time-based
Criteria: Archiving criteria
Retention: Retention period
Storage: Storage type
Access Pattern: Access pattern
Restoration: Restoration process

History Tracking for EntityName:
Tracked Attributes: EntityName.attribute1, EntityName.attribute2
Tracking Method: Tracking method
Granularity: Change level
Retention: Retention period
Access Control: Access control rules

Workflow: WorkflowName for EntityName
States: State1, State2, State3
Transitions:
State1 → State2 [Role1, Role2]
State2 → State3 [Role3, Role4]
Actions: Action1, Action2, Action3

BusinessRule Placement:
* Local Objective: LOName
* Global Objective: GOName
* Chapter: ChapterName
* Book: BookName
* Tenant: TenantName

Workflow WorkflowName Placement:
Global Objective: GOName
Book: BookName
Chapter: ChapterName
Tenant: TenantName

Entity Placement for EntityName:
Tenant: TenantName
Book: BookName
Chapter: ChapterName
Global Objectives: GOName1, GOName2
Local Objectives: LOName1, LOName2
```

### Database Tables

#### 1. `entities` Table

Stores basic entity information.

| Column | Description | Example Value |
|--------|-------------|--------------|
| id | Auto-incrementing primary key | 1 |
| entity_id | Unique identifier for the entity (E1, E2, etc.) | E1 |
| name | Entity name | Loan |
| version | Entity version | 1.0 |
| status | Entity status | Active |
| type | Entity type | Core Entity |
| display_name | Display name for the entity | Loan Agreement |
| description | Entity description | Represents a loan agreement between the financial institution and a customer |
| metadata | Additional entity metadata (JSON) | {"confidential_attributes": ["interestRate", "originationFee"], "internal_attributes": ["remainingBalance"]} |
| lifecycle_management | Lifecycle management information (JSON) | {"archive_strategy": {"trigger": "Event-based", "criteria": "When Loan.status changes to 'Closed'"}} |
| version_type | Version type | v2 |
| tenant_id | Reference to the tenant | t001 |
| book_id | Reference to the book | b001 |
| chapter_id | Reference to the chapter | c001 |
| created_at | Creation timestamp | 2023-01-01 00:00:00 |
| updated_at | Update timestamp | 2023-01-01 00:00:00 |

#### 2. `entity_attributes` Table

Stores entity attribute information.

| Column | Description | Example Value |
|--------|-------------|--------------|
| id | Auto-incrementing primary key | 1 |
| attribute_id | Unique identifier for the attribute (E1.At1, E1.At2, etc.) | E1.At1 |
| entity_id | Reference to the entity | E1 |
| name | Attribute name | loanId |
| display_name | Display name for the attribute | Loan ID |
| datatype | Data type of the attribute | Integer |
| format | Format pattern | "LN-######" |
| min_value | Minimum value | 1000.00 |
| max_value | Maximum value | 10000000.00 |
| status | Attribute status | Active |
| required | Whether the attribute is required | true |
| is_primary_key | Whether the attribute is a primary key | true |
| is_foreign_key | Whether the attribute is a foreign key | false |
| reference_entity_id | Reference to another entity (for foreign keys) | E2 |
| reference_attribute_id | Reference to another attribute (for foreign keys) | E2.At1 |
| default_value | Default value for the attribute | "Active" |
| calculated_field | Whether the attribute is calculated | false |
| calculation_formula | Formula for calculated fields | "Loan.loanAmount - SUM(Payment.amount WHERE Payment.loanId = Loan.loanId AND Payment.status = 'Completed')" |
| dependencies | Dependencies for calculated fields (JSON) | ["loanAmount", "Payment.amount", "Payment.status"] |
| type | Attribute type | Mandatory |
| created_at | Creation timestamp | 2023-01-01 00:00:00 |
| updated_at | Update timestamp | 2023-01-01 00:00:00 |

#### 3. `entity_attribute_metadata` Table

Stores additional metadata for entity attributes.

| Column | Description | Example Value |
|--------|-------------|--------------|
| id | Auto-incrementing primary key | 1 |
| entity_id | Reference to the entity | E1 |
| attribute_id | Reference to the attribute | E1.At1 |
| attribute_name | Attribute name | loanId |
| key_type | Key type (Primary, Foreign, Non-unique) | Primary |
| display_name | Display name for the attribute | Loan ID |
| data_type | Data type | Integer |
| type | Type (Mandatory, Optional, Calculated) | Mandatory |
| format | Format pattern | "LN-######" |
| values | Range or list of values | "1000.00-10000000.00" |
| default_value | Default value | null |
| validation_type | Validation type | Auto-increment |
| error_message | Error message | "Invalid Loan ID format" |
| description | Attribute description | "Unique identifier for the loan" |
| created_at | Creation timestamp | 2023-01-01 00:00:00 |
| updated_at | Update timestamp | 2023-01-01 00:00:00 |

#### 4. `attribute_enum_values` Table

Stores enum values for attributes.

| Column | Description | Example Value |
|--------|-------------|--------------|
| id | Auto-incrementing primary key | 1 |
| attribute_id | Reference to the attribute | E1.At8 |
| value | Enum value | Active |
| display_name | Display name for the enum value | Active |
| sort_order | Sort order for the enum value | 1 |
| created_at | Creation timestamp | 2023-01-01 00:00:00 |
| updated_at | Update timestamp | 2023-01-01 00:00:00 |

#### 5. `entity_relationships` Table

Stores relationships between entities.

| Column | Description | Example Value |
|--------|-------------|--------------|
| id | Auto-incrementing primary key | 1 |
| source_entity_id | Source entity ID | E1 |
| target_entity_id | Target entity ID | E2 |
| relationship_type | Type of relationship | many-to-one |
| source_attribute_id | Source attribute ID | E1.At2 |
| target_attribute_id | Target attribute ID | E2.At1 |
| on_delete | On delete behavior | RESTRICT |
| on_update | On update behavior | CASCADE |
| foreign_key_type | Foreign key type | NON-NULLABLE |
| created_at | Creation timestamp | 2023-01-01 00:00:00 |
| updated_at | Update timestamp | 2023-01-01 00:00:00 |

#### 6. `entity_business_rules` Table

Stores business rules for entities.

| Column | Description | Example Value |
|--------|-------------|--------------|
| id | Auto-incrementing primary key | 1 |
| rule_id | Unique identifier for the rule | validate_loan_update_status_1 |
| entity_id | Reference to the entity | E1 |
| name | Rule name | validate_loan_update_status |
| description | Rule description | "Validates that a Loan's status is Active before allowing any update operations on loan terms" |
| inputs | Rule inputs (JSON) | ["status", "loanId", "paymentFrequency", "term", "interestRate", "lateFeePercentage", "earlyPaymentPenalty"] |
| operation | Rule operation | "conditional_logic({\"condition\": \"AND\", \"rules\": [{\"field\": \"FUNCTION_TYPE\", \"operator\": \"equals\", \"value\": \"update\"}, {\"field\": \"Loan.status\", \"operator\": \"notEquals\", \"value\": \"Active\"}]}, {\"result\": \"error\", \"message\": \"Loan must be Active for any modifications\"}, {\"result\": \"success\"}, Loan.validationResult)" |
| outputs | Rule outputs (JSON) | ["validationResult"] |
| error_message | Error message | "Loan must be Active for any modifications" |
| validation_type | Validation type | PRE_UPDATE |
| active | Whether the rule is active | true |
| created_at | Creation timestamp | 2023-01-01 00:00:00 |
| updated_at | Update timestamp | 2023-01-01 00:00:00 |

#### 7. `entity_calculated_fields` Table

Stores calculated fields for entities.

| Column | Description | Example Value |
|--------|-------------|--------------|
| id | Auto-incrementing primary key | 1 |
| entity_id | Reference to the entity | E1 |
| attribute_id | Reference to the attribute | E1.At10 |
| formula | Calculation formula | "Loan.loanAmount - SUM(Payment.amount WHERE Payment.loanId = Loan.loanId AND Payment.status = 'Completed')" |
| logic_layer | Logic layer | Database |
| caching | Caching strategy | Request |
| dependencies | Dependencies (JSON) | ["loanAmount", "Payment.amount", "Payment.status"] |
| created_at | Creation timestamp | 2023-01-01 00:00:00 |
| updated_at | Update timestamp | 2023-01-01 00:00:00 |

#### 8. `attribute_validations` Table

Stores validations for attributes.

| Column | Description | Example Value |
|--------|-------------|--------------|
| id | Auto-incrementing primary key | 1 |
| attribute_id | Reference to the attribute | E1.At3 |
| validation_name | Validation name | amount_greater_than_zero |
| validation_type | Validation type | range |
| validation_expression | Validation expression | "loanAmount > 0" |
| error_message | Error message | "Loan amount must be greater than 0" |
| created_at | Creation timestamp | 2023-01-01 00:00:00 |
| updated_at | Update timestamp | 2023-01-01 00:00:00 |

#### 9. `entity_constraints` Table

Stores constraints between entities.

| Column | Description | Example Value |
|--------|-------------|--------------|
| id | Auto-incrementing primary key | 1 |
| entity_id | Reference to the entity | E1 |
| target_entity_id | Reference to the target entity | E2 |
| constrained_attribute_id | Reference to the constrained attribute | E1.At15 |
| foreign_key_attribute_ids | Foreign key attribute IDs (JSON) | ["E1.At2"] |
| constraint_type | Constraint type | belongs_to |
| created_at | Creation timestamp | 2023-01-01 00:00:00 |
| updated_at | Update timestamp | 2023-01-01 00:00:00 |

#### 10. `entity_archive_strategy` Table

Stores archive strategy for entities.

| Column | Description | Example Value |
|--------|-------------|--------------|
| id | Auto-incrementing primary key | 1 |
| entity_id | Reference to the entity | E1 |
| trigger | Trigger type | Event-based |
| criteria | Archiving criteria | "When Loan.status changes to 'Closed' and remains so for 2 years" |
| retention | Retention period | "10 years" |
| storage | Storage type | "Cold storage" |
| access_pattern | Access pattern | "Read-only through Financial archive portal" |
| restoration | Restoration process | "Manual process requiring Financial Director approval" |
| created_at | Creation timestamp | 2023-01-01 00:00:00 |
| updated_at | Update timestamp | 2023-01-01 00:00:00 |

#### 11. `entity_history_tracking` Table

Stores history tracking for entities.

| Column | Description | Example Value |
|--------|-------------|--------------|
| id | Auto-incrementing primary key | 1 |
| entity_id | Reference to the entity | E1 |
| tracked_attributes | Tracked attributes (JSON) | ["status", "interestRate", "remainingBalance", "term"] |
| tracking_method | Tracking method | "Audit table" |
| granularity | Granularity | "Change level" |
| retention | Retention period | "10 years" |
| access_control | Access control | "Financial Managers and Compliance Officers only" |
| created_at | Creation timestamp | 2023-01-01 00:00:00 |
| updated_at | Update timestamp | 2023-01-01 00:00:00 |

#### 12. Entity Tables (e.g., `e1_loan`)

Each entity gets its own table with columns for each attribute. The primary key is determined by the natural language prescriptive (^PK marker).

| Column | Description | Example Value |
|--------|-------------|--------------|
| id | Auto-incrementing ID (not necessarily the primary key) | 1 |
| loanid | Loan ID (primary key as defined in the prescriptive) | LN-123456 |
| customerid | Customer ID | CUS-123456 |
| loanamount | Loan amount | 250000.00 |
| interestrate | Interest rate | 4.5 |
| term | Term in months | 360 |
| startdate | Start date | 2024-01-10 |
| enddate | End date | 2054-01-10 |
| status | Status | Active |
| paymentfrequency | Payment frequency | Monthly |
| totalpaymentsmade | Total payments made | 4 |
| remainingbalance | Remaining balance | 247856.32 |
| loantype | Loan type | Mortgage |
| collateralid | Collateral ID | COL-123456 |
| originationfee | Origination fee | 2500.00 |
| latefeepercent | Late fee percentage | 5.0 |
| earlypaymentpenalty | Early payment penalty | 1.0 |
| created_at | Creation timestamp | 2023-01-01 00:00:00 |
| created_by | Creator | system |
| updated_at | Update timestamp | 2023-01-01 00:00:00 |
| updated_by | Updater | system |

### Mapping Process

1. The `entity_parser.py` parses the natural language prescriptive text and extracts entity information.
2. **Validation Process**:
   - Validate entity names for uniqueness
   - Validate attribute names for uniqueness within an entity
   - Validate foreign key references to existing entities
   - Validate relationship source and target entities and attributes
   - Validate business rule entities and attributes
   - Validate calculated field dependencies
   - If any validation fails, log detailed error messages and suggestions for fixes
3. The `entity_deployer.py` deploys the parsed entity information to the database tables.
3. The entity ID is auto-generated as E1, E2, etc.
4. Attribute IDs are auto-generated as E1.At1, E1.At2, etc.
5. Entity tables are created with the naming convention e1_entityname, e2_entityname, etc.
6. Relationships, business rules, and validations are stored in their respective tables.

### Line-by-Line Mapping

Here's a detailed line-by-line mapping of the natural language prescriptive to database tables and columns:

1. `EntityName has attribute1^PK, attribute2, attribute3 (enum1, enum2), attribute4^FK, attribute5[derived].`
   - Creates a record in `entities` table with auto-generated `entity_id` and `name` = EntityName
   - Creates records in `entity_attributes` table for each attribute with auto-generated `attribute_id`
   - Sets `is_primary_key` = true for attributes marked with ^PK
   - Sets `is_foreign_key` = true for attributes marked with ^FK
   - Sets `calculated_field` = true for attributes marked with [derived]
   - For attributes with enum values, creates records in `attribute_enum_values` table

2. `Relationships for EntityName:`
   - Creates records in `entity_relationships` table for each relationship

3. `EntityName has relationship-type relationship with TargetEntity using EntityName.attribute to TargetEntity.attribute`
   - Creates a record in `entity_relationships` table with:
     - `source_entity_id` = EntityName's entity_id
     - `target_entity_id` = TargetEntity's entity_id
     - `relationship_type` = relationship-type
     - `source_attribute_id` = EntityName.attribute's attribute_id
     - `target_attribute_id` = TargetEntity.attribute's attribute_id

4. `Defaults for EntityName:`
   - Updates records in `entity_attributes` table with default values

5. `EntityName.attribute PROPERTY_NAME = value`
   - Creates or updates metadata in `entity_attribute_metadata` table

6. `EntityName.attribute DEFAULT_VALUE = value`
   - Updates `default_value` in `entity_attributes` table

7. `Validations for EntityName:`
   - Creates records in `attribute_validations` table

8. `EntityName.attribute must be unique`
   - Creates a record in `attribute_validations` table with `validation_type` = 'unique'

9. `BusinessRule for EntityName:`
   - Creates records in `entity_business_rules` table

10. `rule_name_1`
    - Creates a record in `entity_business_rules` table with `rule_id` = rule_name_1

11. `Inputs: EntityName with attribute1, attribute2, attribute3`
    - Updates `inputs` in `entity_business_rules` table

12. `Operation: conditional_logic({condition details}, {success details}, {failure details}, EntityName.result)`
    - Updates `operation` in `entity_business_rules` table

13. `Description: Detailed description of the business rule`
    - Updates `description` in `entity_business_rules` table

14. `Output: EntityName with result`
    - Updates `outputs` in `entity_business_rules` table

15. `Error: Error message when rule fails`
    - Updates `error_message` in `entity_business_rules` table

16. `Validation: When to apply the rule (e.g., PRE_UPDATE)`
    - Updates `validation_type` in `entity_business_rules` table

17. `CalculatedField for EntityName.calculatedAttribute:`
    - Creates a record in `entity_calculated_fields` table

18. `* Formula: calculation formula using attributes and functions`
    - Updates `formula` in `entity_calculated_fields` table

19. `* Logic Layer: Database or Application`
    - Updates `logic_layer` in `entity_calculated_fields` table

20. `* Caching: Request or Session`
    - Updates `caching` in `entity_calculated_fields` table

21. `* Dependencies: EntityName.attribute1, EntityName.attribute2`
    - Updates `dependencies` in `entity_calculated_fields` table

22. `EntityName has attribute1^FK, attribute2^FK with TargetEntity constrains ConstrainedEntity.`
    - Creates a record in `entity_constraints` table

23. `EntityName.attribute must belong to selected EntityName.otherAttribute`
    - Creates a record in `entity_constraints` table with `constraint_type` = 'belongs_to'

24. `Entity Additional Properties:`
    - Updates metadata in `entities` table

25. `Display Name: Friendly Name`
    - Updates `display_name` in `entities` table

26. `Type: Core Entity`
    - Updates `type` in `entities` table

27. `Description: Entity description`
    - Updates `description` in `entities` table

28. `Attribute Additional Properties:`
    - Creates or updates records in `entity_attribute_metadata` table

29. `Attribute name: attributeName`
    - Identifies the attribute in `entity_attribute_metadata` table

30. `Key: Primary or Foreign or Non-unique`
    - Updates `key_type` in `entity_attribute_metadata` table

31. `Display Name: Friendly Attribute Name`
    - Updates `display_name` in `entity_attribute_metadata` table

32. `Data Type: Integer or Decimal or String or Date or Enum`
    - Updates `data_type` in `entity_attribute_metadata` table

33. `Type: Mandatory or Optional or Calculated`
    - Updates `type` in `entity_attribute_metadata` table

34. `Format: Format pattern`
    - Updates `format` in `entity_attribute_metadata` table

35. `Values: Range or list of values`
    - Updates `values` in `entity_attribute_metadata` table

36. `Default: Default value`
    - Updates `default_value` in `entity_attribute_metadata` table

37. `Validation: Validation type`
    - Updates `validation_type` in `entity_attribute_metadata` table

38. `Error Message: "Error message"`
    - Updates `error_message` in `entity_attribute_metadata` table

39. `Description: Attribute description`
    - Updates `description` in `entity_attribute_metadata` table

40. `Relationship: EntityName to TargetEntity`
    - Identifies the relationship in `entity_relationships` table

41. `Relationship Properties:`
    - Updates properties in `entity_relationships` table

42. `On Delete: Restrict or Cascade or Set Null`
    - Updates `on_delete` in `entity_relationships` table

43. `On Update: Restrict or Cascade`
    - Updates `on_update` in `entity_relationships` table

44. `Foreign Key Type: Non-Nullable or Nullable`
    - Updates `foreign_key_type` in `entity_relationships` table

45. `Synthetic: EntityName has attribute1 = value1, attribute2 = value2, attribute3 = value3.`
    - Used to generate sample data for the entity table (e.g., `e1_entityname`)

46. `Confidential: EntityName.attribute1, EntityName.attribute2`
    - Updates `metadata` in `entities` table with confidential attributes

47. `Internal: EntityName.attribute3, EntityName.attribute4`
    - Updates `metadata` in `entities` table with internal attributes

48. `Public: EntityName.attribute5, EntityName.attribute6`
    - Updates `metadata` in `entities` table with public attributes

49. `Loading for EntityName.RelatedEntity1: Eager Loading`
    - Updates `metadata` in `entity_relationships` table with loading strategy

50. `Archive Strategy for EntityName:`
    - Creates a record in `entity_archive_strategy` table

51. `Trigger: Event-based or Time-based`
    - Updates `trigger` in `entity_archive_strategy` table

52. `Criteria: Archiving criteria`
    - Updates `criteria` in `entity_archive_strategy` table

53. `Retention: Retention period`
    - Updates `retention` in `entity_archive_strategy` table

54. `Storage: Storage type`
    - Updates `storage` in `entity_archive_strategy` table

55. `Access Pattern: Access pattern`
    - Updates `access_pattern` in `entity_archive_strategy` table

56. `Restoration: Restoration process`
    - Updates `restoration` in `entity_archive_strategy` table

57. `History Tracking for EntityName:`
    - Creates a record in `entity_history_tracking` table

58. `Tracked Attributes: EntityName.attribute1, EntityName.attribute2`
    - Updates `tracked_attributes` in `entity_history_tracking` table

59. `Tracking Method: Tracking method`
    - Updates `tracking_method` in `entity_history_tracking` table

60. `Granularity: Change level`
    - Updates `granularity` in `entity_history_tracking` table

61. `Retention: Retention period`
    - Updates `retention` in `entity_history_tracking` table

62. `Access Control: Access control rules`
    - Updates `access_control` in `entity_history_tracking` table

## Global Objective Mapping

Global Objectives (GOs) are defined in natural language prescriptives and mapped to multiple database tables.

### GO Definition

Natural language format:
```
## Process Name

Core Metadata:
- name: "Process Name"
- version: "1.0"
- status: "Active"
- description: "Process description"
- primary_entity: "EntityName"
- classification: "Process Type"
  - Global Objective: "Global Objective Name"
  - Book: "Book Name"
  - Chapter: "Chapter Name"
  - Tenant: "Tenant Name"

Process Ownership:
- Originator: Role1
- Process Owner: Role2
- Business Sponsor: Role3

Trigger Definition:
- Trigger Type: event-driven
- Trigger Condition: Condition description
- Trigger Frequency: on-demand
- Trigger Parameters: param1, param2

Data Management:

Input Stack:
- Entity1 with attr1*, attr2, attr3; Entity2 with attr1*, attr2.

Input Mapping Stack:
- Entity1.attr1 maps to LO1 → Entity3.attr1
- Entity1.attr2 maps to LO1 → Entity3.attr2

Output Stack:
- Entity3 with attr1*, attr2*, attr3*.

Output Mapping Stack:
- Entity3.attr1 maps to "GO Name" → Entity4.attr1
- Entity3.attr2 maps to "GO Name" → Entity4.attr2

Data Constraints:

DB Stack:
- Entity3.attr1 (string) is mandatory and unique. Error message: "Error message"
- Entity3.attr2 (date) is mandatory and must be a valid date. Error message: "Error message"

Process Definition:

Process Flow:
1. LO1 [HUMAN] - LO description
   a. If condition1, route to LO2
   b. If condition2, route to LO3
2. LO2 [SYSTEM] - LO description
   a. Route to LO4
3. LO3 [HUMAN] - LO description
   a. Route to LO4
4. LO4 [SYSTEM] - LO description
   a. Route to Terminal

Parallel Flows:
- After LO1:
  * LO5 - LO description
  * LO6 - LO description
- Join at: LO4

Rollback Pathways:
- LO1 ↔ LO7
- Full rollback pathway: LO7 → LO8 → LO9

Business Rules:
1. Business rule description - Enforced by LO1
2. Business rule description - Enforced by LO2

Integration Points:

GO Relationships:
- GO sends output to "GO Name" for Entity4 processing via LO4.
- GO receives input from "GO Name" for Entity1 processing.

External Systems:
- System1: Two-way integration for data exchange
- System2: Outbound integration for notifications

Performance Metadata:
- cycle_time: "2 business days"
- number_of_pathways: 4
- volume_metrics:
  * average_volume: 120
  * peak_volume: 250
  * unit: "requests/month"
- sla_thresholds:
  * threshold1: "1 business day"
  * threshold2: "1 hour"
- critical_lo_performance:
  * "LO1": "24 hours maximum"
  * "LO2": "1 hour maximum"

Process Mining Schema:
...

Sample Data:
- Entity3.attr1: "value1", "value2", "value3"
- Entity3.attr2: "value1", "value2", "value3"
```

### Database Tables

#### 1. `global_objectives` Table

Stores basic GO information.

| Column | Description | Example Value |
|--------|-------------|--------------|
| id | Auto-incrementing primary key | 1 |
| go_id | Unique identifier for the GO (auto-generated) | GO1 |
| name | GO name | Process Leave Requests |
| version | GO version | 1.0 |
| status | GO status | Active |
| description | GO description | Manages employee leave requests from submission to approval or rejection |
| primary_entity | Primary entity | Employee |
| classification | Classification | Process Type |
| created_at | Creation timestamp | 2023-01-01 00:00:00 |
| updated_at | Update timestamp | 2023-01-01 00:00:00 |
| tenant_id | Reference to the tenant | t001 |
| tenant_name | Tenant name | Acme Corporation |
| book_id | Reference to the book | b001 |
| book_name | Book name | Employee Leave Management |
| chapter_id | Reference to the chapter | c001 |
| chapter_name | Chapter name | Leave Request Lifecycle |
| process_mining_schema | Process mining schema (JSON) | {...} |
| performance_metadata | Performance metadata (JSON) | {...} |
| trigger_definition | Trigger definition (JSON) | {...} |
| validation_checklist | Validation checklist (JSON) | {...} |
| sample_data | Sample data (JSON) | {...} |
| business_rules | Business rules (JSON) | [...] |
| data_constraints | Data constraints (JSON) | [...] |
| rollback_pathways | Rollback pathways (JSON) | [...] |
| version_type | Version type | v2 |

#### 2. `tenants` Table

Stores tenant information.

| Column | Description | Example Value |
|--------|-------------|--------------|
| id | Auto-incrementing primary key | 1 |
| tenant_id | Unique identifier for the tenant | t001 |
| name | Tenant name | Acme Corporation |
| description | Tenant description | |
| created_at | Creation timestamp | 2023-01-01 00:00:00 |
| updated_at | Update timestamp | 2023-01-01 00:00:00 |

#### 3. `books` Table

Stores book information.

| Column | Description | Example Value |
|--------|-------------|--------------|
| id | Auto-incrementing primary key | 1 |
| book_id | Unique identifier for the book | b001 |
| tenant_id | Reference to the tenant | t001 |
| name | Book name | Employee Leave Management |
| description | Book description | |
| created_at | Creation timestamp | 2023-01-01 00:00:00 |
| updated_at | Update timestamp | 2023-01-01 00:00:00 |

#### 4. `chapters` Table

Stores chapter information.

| Column | Description | Example Value |
|--------|-------------|--------------|
| id | Auto-incrementing primary key | 1 |
| chapter_id | Unique identifier for the chapter | c001 |
| book_id | Reference to the book | b001 |
| name | Chapter name | Leave Request Lifecycle |
| description | Chapter description | |
| created_at | Creation timestamp | 2023-01-01 00:00:00 |
| updated_at | Update timestamp | 2023-01-01 00:00:00 |

#### 5. `input_stack` Table

Stores GO input stack information.

| Column | Description | Example Value |
|--------|-------------|--------------|
| id | Auto-incrementing primary key | 1 |
| go_id | Reference to the GO | GO1 |
| description | Input stack description | Input stack for GO1 |
| created_at | Creation timestamp | 2023-01-01 00:00:00 |
| updated_at | Update timestamp | 2023-01-01 00:00:00 |

#### 6. `input_items` Table

Stores GO input items.

| Column | Description | Example Value |
|--------|-------------|--------------|
| id | Auto-incrementing primary key | 1 |
| input_stack_id | Reference to the input stack | 1 |
| slot_id | Slot ID | Employee.employeeId |
| contextual_id | Contextual ID | Employee.employeeId |
| entity_reference | Reference to the entity | E1 |
| attribute_reference | Reference to the attribute | E1.At1 |
| source_type | Source type | user |
| source_description | Source description | |
| required | Whether the input is required | true |
| created_at | Creation timestamp | 2023-01-01 00:00:00 |
| updated_at | Update timestamp | 2023-01-01 00:00:00 |

#### 7. `output_stack` Table

Stores GO output stack information.

| Column | Description | Example Value |
|--------|-------------|--------------|
| id | Auto-incrementing primary key | 1 |
| go_id | Reference to the GO | GO1 |
| description | Output stack description | Output stack for GO1 |
| created_at | Creation timestamp | 2023-01-01 00:00:00 |
| updated_at | Update timestamp | 2023-01-01 00:00:00 |

#### 8. `output_items` Table

Stores GO output items.

| Column | Description | Example Value |
|--------|-------------|--------------|
| id | Auto-incrementing primary key | 1 |
| output_stack_id | Reference to the output stack | 1 |
| slot_id | Slot ID | LeaveApplication.leaveId |
| contextual_id | Contextual ID | LeaveApplication.leaveId |
| output_entity | Reference to the entity | E3 |
| output_attribute | Reference to the attribute | E3.At1 |
| data_type | Data type | string |
| created_at | Creation timestamp | 2023-01-01 00:00:00 |
| updated_at | Update timestamp | 2023-01-01 00:00:00 |

#### 9. `output_triggers` Table

Stores GO output triggers for GO-to-GO mappings.

| Column | Description | Example Value |
|--------|-------------|--------------|
| id | Auto-incrementing primary key | 1 |
| output_item_id | Reference to the output item | item1 |
| output_stack_id | Reference to the output stack | 1 |
| target_objective | Reference to the target GO | GO2 |
| target_input | Reference to the target input | input1 |
| mapping_type | Mapping type | direct |
| created_at | Creation timestamp | 2023-01-01 00:00:00 |
| updated_at | Update timestamp | 2023-01-01 00:00:00 |

#### 10. `go_lo_mapping` Table

Stores mappings between GOs and LOs.

| Column | Description | Example Value |
|--------|-------------|--------------|
| id | Auto-incrementing primary key | 1 |
| mapping_id | Unique identifier for the mapping | GO1_SubmitLeaveRequest |
| go_id | Reference to the GO | GO1 |
| lo_name | Name of the LO | SubmitLeaveRequest |
| sequence_number | Sequence number | 1 |
| created_at | Creation timestamp | 2023-01-01 00:00:00 |
| updated_at | Update timestamp | 2023-01-01 00:00:00 |

### Mapping Process

1. The `go_parser.py` parses the natural language prescriptive text and extracts GO information.
2. The `go_deployer.py` deploys the parsed GO information to the database tables.
3. The GO ID is auto-generated as GO1, GO2, etc.
4. Tenant, book, and chapter information is stored in their respective tables.
5. Input stack, output stack, and mappings are stored in their respective tables.
6. Process mining schema, performance metadata, and other JSON data are stored in the global_objectives table.

### Validation Process

During the parsing and deployment process, the following validations are performed:

1. **Entity References Validation**:
   - Verify that all entities referenced in the GO exist in the database.
   - Verify that all attributes referenced in the GO exist in the referenced entities.
   - If an entity or attribute doesn't exist, log a warning and continue with other validations.

2. **LO References Validation**:
   - Verify that all LOs referenced in the GO exist in the database.
   - If an LO doesn't exist, log a warning and continue with other validations.

3. **GO References Validation**:
   - Verify that all GOs referenced in the GO exist in the database.
   - If a GO doesn't exist, log a warning and continue with other validations.

4. **Data Constraints Validation**:
   - Verify that all data constraints reference valid entities and attributes.
   - If a constraint references an invalid entity or attribute, log a warning and continue with other validations.

5. **Business Rules Validation**:
   - Verify that all business rules reference valid entities and attributes.
   - If a rule references an invalid entity or attribute, log a warning and continue with other validations.

### Error Handling

If any validation fails, the system will:

1. Log detailed error messages with the specific validation that failed.
2. Provide suggestions for how to fix the validation errors.
3. Continue with other validations to identify all issues at once.
4. Return a comprehensive list of validation errors to the user.

### Line-by-Line Mapping

Here's a detailed line-by-line mapping of the natural language prescriptive to database tables and columns:

1. `## Process Name`
   - Creates a record in `global_objectives` table with auto-generated `go_id` and `name` = Process Name

2. `Core Metadata:`
   - Updates metadata in `global_objectives` table

3. `- name: "Process Name"`
   - Updates `name` in `global_objectives` table

4. `- version: "1.0"`
   - Updates `version` in `global_objectives` table

5. `- status: "Active"`
   - Updates `status` in `global_objectives` table

6. `- description: "Process description"`
   - Updates `description` in `global_objectives` table

7. `- primary_entity: "EntityName"`
   - Updates `primary_entity` in `global_objectives` table

8. `- classification: "Process Type"`
   - Updates `classification` in `global_objectives` table

9. `  - Global Objective: "Global Objective Name"`
   - Updates metadata in `global_objectives` table

10. `  - Book: "Book Name"`
    - Creates or updates a record in `books` table
    - Updates `book_id` and `book_name` in `global_objectives` table

11. `  - Chapter: "Chapter Name"`
    - Creates or updates a record in `chapters` table
    - Updates `chapter_id` and `chapter_name` in `global_objectives` table

12. `  - Tenant: "Tenant Name"`
    - Creates or updates a record in `tenants` table
    - Updates `tenant_id` and `tenant_name` in `global_objectives` table

13. `Process Ownership:`
    - Updates `process_ownership` in `global_objectives` table

14. `Trigger Definition:`
    - Updates `trigger_definition` in `global_objectives` table

15. `Input Stack:`
    - Creates a record in `input_stack` table with reference to the GO

16. `- Entity1 with attr1*, attr2, attr3; Entity2 with attr1*, attr2.`
    - Creates records in `input_items` table for each attribute
    - Sets `required` = true for attributes marked with *

17. `Input Mapping Stack:`
    - Updates metadata in `global_objectives` table

18. `Output Stack:`
    - Creates a record in `output_stack` table with reference to the GO

19. `- Entity3 with attr1*, attr2*, attr3*.`
    - Creates records in `output_items` table for each attribute
    - Sets `required` = true for attributes marked with *

20. `Output Mapping Stack:`
    - Creates records in `output_triggers` table for GO-to-GO mappings

21. `- Entity3.attr1 maps to "GO Name" → Entity4.attr1`
    - Creates a record in `output_triggers` table with:
      - `output_item_id` = Entity3.attr1's output_item_id
      - `target_objective` = GO Name's go_id
      - `target_input` = Entity4.attr1's input_item_id

22. `Data Constraints:`
    - Updates `data_constraints` in `global_objectives` table

23. `Process Flow:`
    - Creates records in `go_lo_mapping` table for each LO in the process flow

24. `1. LO1 [HUMAN] - LO description`
    - Creates a record in `go_lo_mapping` table with:
      - `go_id` = GO's go_id
      - `lo_name` = actual LO name (e.g., SubmitLeaveRequest)
      - `sequence_number` = 1
    - Note: LO names should be actual descriptive names, not generic LO1, LO2, etc.
    - Important: LOs must be created from the GO table to ensure proper mappings between GOs and LOs

25. `Parallel Flows:`
    - Updates metadata in `global_objectives` table

26. `Rollback Pathways:`
    - Updates `rollback_pathways` in `global_objectives` table

27. `Business Rules:`
    - Updates `business_rules` in `global_objectives` table

28. `GO Relationships:`
    - Creates records in `output_triggers` table for GO-to-GO relationships

29. `Performance Metadata:`
    - Updates `performance_metadata` in `global_objectives` table

30. `Process Mining Schema:`
    - Updates `process_mining_schema` in `global_objectives` table

31. `Sample Data:`
    - Updates `sample_data` in `global_objectives` table

## Local Objective Mapping

Local Objectives (LOs) are defined in natural language prescriptives and mapped to multiple database tables.

### LO Definition

Natural language format:
```
## LO Name

name: "LO Name"
function_type: "Create"
workflow_source: "origin"
description: "LO description"
version: "1.0"
status: "Active"

*Role has execution rights*

*Inputs: Entity with attr1*, attr2*, attr3 [info]*
* System generates Entity.attr1 using generate_id with prefix "prefix".
* System calculates Entity.attr2 using calculate_formula with attr3 and attr4.
* System defaults Entity.attr3 to "default_value".

*Outputs: Entity with attr1, attr2, attr3*
* System returns Entity.attr1 for reference in notifications.
* System captures Entity.attr2 using current_timestamp for audit trails.
* System transforms Entity.attr3 for display using format_enum_value.

*DB Stack:*
* Entity.attr1 (string) is mandatory and unique. Error message: "Error message"
* Entity.attr2 (date) is mandatory and must be a valid date. Error message: "Error message"

*UI Stack:*
* Entity.attr1 displays as oj-input-text with readonly property.
* Entity.attr2 displays as oj-input-date with min-value set to current date.
* Entity.attr3 displays as oj-text-area with rows=3 and maxlength=500.
* Entity.attr4 with visible=false.
* System provides contextual help for Entity.attr1 explaining "Help text".

*Mapping Stack:*
* LO1.output.attr1 maps to LO2.input.attr1 using direct mapping.
* LO1.output.attr2 maps to LO2.input.attr2 using direct mapping.

*Nested Functions:*
* Function generate_id(prefix) for Entity.attr1
  - Description: Generates a unique ID with the given prefix
  - Parameters: prefix (string)
  - Returns: string
  - Writes to: Entity.attr1
* Function calculate_total(attr1, attr2) for Entity.attr3
  - Description: Calculates the total based on attr1 and attr2
  - Parameters: attr1 (number), attr2 (number)
  - Returns: number
  - Writes to: Entity.attr3

Execution pathway:
* When Entity.attr1 = value1, route to LO2.
* When Entity.attr2 = value2, route to LO3.
* When Entity.attr3 = value3, system flags Entity.attr4 to true, route to LO4.

Synthetic values:
* Entity.attr1: "value1", "value2", "value3"
* Entity.attr2: "value1", "value2", "value3"
```

### Database Tables

#### 1. `local_objectives` Table

Stores basic LO information.

| Column | Description | Example Value |
|--------|-------------|--------------|
| id | Auto-incrementing primary key | 1 |
| lo_name | Name of the LO | SubmitLeaveRequest |
| function_type | Function type | Create |
| workflow_source | Workflow source | origin |
| go_id | Reference to the GO | GO1 |
| created_at | Creation timestamp | 2023-01-01 00:00:00 |
| updated_at | Update timestamp | 2023-01-01 00:00:00 |
| system_function | System function | |
| description | LO description | Allows employees to submit leave requests |
| ui_stack | UI stack (JSON) | {...} |
| mapping_stack | Mapping stack (JSON) | {...} |
| version_type | Version type | v2 |
| tenant_id | Reference to the tenant | t001 |
| tenant_name | Tenant name | Acme Corporation |
| book_id | Reference to the book | b001 |
| book_name | Book name | Employee Leave Management |
| chapter_id | Reference to the chapter | c001 |
| chapter_name | Chapter name | Leave Request Lifecycle |
| version | Version | 1.0 |
| status | Status | Active |
| agent_type | Agent type (HUMAN or SYSTEM) | HUMAN |

#### 2. `lo_input_stack` Table

Stores LO input stack information.

| Column | Description | Example Value |
|--------|-------------|--------------|
| id | Auto-incrementing primary key | 1 |
| lo_name | Reference to the LO | SubmitLeaveRequest |
| description | Input stack description | Input stack for SubmitLeaveRequest |
| created_at | Creation timestamp | 2023-01-01 00:00:00 |
| updated_at | Update timestamp | 2023-01-01 00:00:00 |

#### 3. `lo_input_items` Table

Stores LO input items.

| Column | Description | Example Value |
|--------|-------------|--------------|
| id | Auto-incrementing primary key | 1 |
| input_stack_id | Reference to the input stack | 1 |
| slot_id | Slot ID | LeaveApplication.leaveId |
| contextual_id | Contextual ID | LeaveApplication.leaveId |
| source_type | Source type | user |
| source_description | Source description | |
| required | Whether the input is required | true |
| lo_name | Reference to the LO | SubmitLeaveRequest |
| data_type | Data type | string |
| ui_control | UI control | oj-input-text |
| nested_function | Nested function (JSON) | {...} |
| nested_functions | Nested functions (JSON) | [...] |
| metadata | Metadata (JSON) | {...} |
| dependencies | Dependencies (JSON) | [...] |
| dependency_type | Dependency type | |
| lookup_function | Lookup function (JSON) | {...} |
| is_visible | Whether the input is visible | true |
| default_value | Default value | |
| help_text | Help text | |
| dependency_info | Dependency information (JSON) | {...} |
| name | Item name | leaveId |
| type | Item type | string |
| created_at | Creation timestamp | 2023-01-01 00:00:00 |
| updated_at | Update timestamp | 2023-01-01 00:00:00 |

#### 4. `lo_input_validations` Table

Stores LO input validations.

| Column | Description | Example Value |
|--------|-------------|--------------|
| id | Auto-incrementing primary key | 1 |
| input_item_id | Reference to the input item | 1 |
| input_stack_id | Reference to the input stack | 1 |
| rule | Validation rule | |
| rule_type | Rule type | |
| entity | Entity | |
| attribute | Attribute | |
| validation_method | Validation method | |
| reference_date_source | Reference date source | |
| allowed_values | Allowed values (JSON) | [...] |
| error_message | Error message | |
| created_at | Creation timestamp | 2023-01-01 00:00:00 |
| updated_at | Update timestamp | 2023-01-01 00:00:00 |

#### 5. `lo_output_stack` Table

Stores LO output stack information.

| Column | Description | Example Value |
|--------|-------------|--------------|
| id | Auto-incrementing primary key | 1 |
| lo_name | Reference to the LO | SubmitLeaveRequest |
| description | Output stack description | Output stack for SubmitLeaveRequest |
| created_at | Creation timestamp | 2023-01-01 00:00:00 |
| updated_at | Update timestamp | 2023-01-01 00:00:00 |

#### 6. `lo_output_items` Table

Stores LO output items.

| Column | Description | Example Value |
|--------|-------------|--------------|
| id | Auto-incrementing primary key | 1 |
| output_stack_id | Reference to the output stack | 1 |
| slot_id | Slot ID | LeaveApplication.leaveId |
| contextual_id | Contextual ID | LeaveApplication.leaveId |
| source | Source | system |
| lo_name | Reference to the LO | SubmitLeaveRequest |
| name | Item name | leaveId |
| type | Item type | string |
| created_at | Creation timestamp | 2023-01-01 00:00:00 |
| updated_at | Update timestamp | 2023-01-01 00:00:00 |

#### 7. `lo_output_triggers` Table

Stores LO output triggers for LO-to-LO mappings.

| Column | Description | Example Value |
|--------|-------------|--------------|
| id | Auto-incrementing primary key | 1 |
| output_item_id | Reference to the output item | 1 |
| output_stack_id | Reference to the output stack | 1 |
| target_lo | Reference to the target LO | ApproveLeaveRequest |
| target_input | Reference to the target input | leaveId |
| mapping_type | Mapping type | direct |
| created_at | Creation timestamp | 2023-01-01 00:00:00 |
| updated_at | Update timestamp | 2023-01-01 00:00:00 |

#### 8. `execution_pathways` Table

Stores LO execution pathways.

| Column | Description | Example Value |
|--------|-------------|--------------|
| id | Auto-incrementing primary key | 1 |
| lo_name | Reference to the LO | SubmitLeaveRequest |
| pathway_type | Pathway type | conditional |
| next_lo | Reference to the next LO | ApproveLeaveRequest |
| created_at | Creation timestamp | 2023-01-01 00:00:00 |
| updated_at | Update timestamp | 2023-01-01 00:00:00 |

#### 9. `execution_pathway_conditions` Table

Stores LO execution pathway conditions.

| Column | Description | Example Value |
|--------|-------------|--------------|
| id | Auto-incrementing primary key | 1 |
| lo_name | Reference to the LO | SubmitLeaveRequest |
| condition_type | Condition type | attribute |
| condition_entity | Condition entity | LeaveApplication |
| condition_attribute | Condition attribute | status |
| condition_operator | Condition operator | = |
| condition_value | Condition value | Approved |
| next_lo | Reference to the next LO | ApproveLeaveRequest |
| created_at | Creation timestamp | 2023-01-01 00:00:00 |
| updated_at | Update timestamp | 2023-01-01 00:00:00 |

#### 10. `lo_nested_functions` Table

Stores LO nested functions.

| Column | Description | Example Value |
|--------|-------------|--------------|
| id | Auto-incrementing primary key | 1 |
| lo_name | Reference to the LO | SubmitLeaveRequest |
| function_name | Function name | generate_id |
| description | Function description | Generates a unique ID with the given prefix |
| parameters | Parameters (JSON) | [{"name": "prefix", "type": "string"}] |
| returns | Return type | string |
| writes_to | Writes to | LeaveApplication.leaveId |
| input_item_id | Reference to the input item | 1 |
| created_at | Creation timestamp | 2023-01-01 00:00:00 |
| updated_at | Update timestamp | 2023-01-01 00:00:00 |

#### 11. `lo_system_functions` Table

Stores LO system functions.

| Column | Description | Example Value |
|--------|-------------|--------------|
| id | Auto-incrementing primary key | 1 |
| function_id | Unique identifier for the function | func1 |
| lo_name | Reference to the LO | SubmitLeaveRequest |
| function_name | Function name | generate_id |
| function_type | Function type | standard |
| parameters | Parameters (JSON) | {"prefix": "LR"} |
| output_to | Output to | |
| created_at | Creation timestamp | 2023-01-01 00:00:00 |
| updated_at | Update timestamp | 2023-01-01 00:00:00 |

### Mapping Process

1. The `lo_parser.py` parses the natural language prescriptive text and extracts LO information.
2. The `lo_deployer.py` deploys the parsed LO information to the database tables.
3. Input stack, output stack, and mappings are stored in their respective tables.
4. Execution pathways and conditions are stored in their respective tables.
5. Nested functions and system functions are stored in their respective tables.

### Validation Process

During the parsing and deployment process, the following validations are performed:

1. **Entity References Validation**:
   - Verify that all entities referenced in the LO exist in the database.
   - Verify that all attributes referenced in the LO exist in the referenced entities.
   - If an entity or attribute doesn't exist, log a warning and continue with other validations.

2. **LO References Validation**:
   - Verify that all LOs referenced in the LO exist in the database.
   - If an LO doesn't exist, log a warning and continue with other validations.

3. **GO References Validation**:
   - Verify that the GO referenced in the LO exists in the database.
   - If the GO doesn't exist, log a warning and continue with other validations.

4. **Nested Functions Validation**:
   - Verify that all nested functions reference valid entities and attributes.
   - Verify that all nested functions have valid parameters and return types.
   - If a nested function references an invalid entity or attribute, log a warning and continue with other validations.

5. **System Functions Validation**:
   - Verify that all system functions have valid parameters and output destinations.
   - If a system function has invalid parameters or output destinations, log a warning and continue with other validations.

### Error Handling

If any validation fails, the system will:

1. Log detailed error messages with the specific validation that failed.
2. Provide suggestions for how to fix the validation errors.
3. Continue with other validations to identify all issues at once.
4. Return a comprehensive list of validation errors to the user.

### Line-by-Line Mapping

Here's a detailed line-by-line mapping of the natural language prescriptive to database tables and columns:

1. `## LO Name`
   - Creates a record in `local_objectives` table with `lo_name` = LO Name

2. `name: "LO Name"`
   - Updates `lo_name` in `local_objectives` table

3. `function_type: "Create"`
   - Updates `function_type` in `local_objectives` table

4. `workflow_source: "origin"`
   - Updates `workflow_source` in `local_objectives` table

5. `description: "LO description"`
   - Updates `description` in `local_objectives` table

6. `version: "1.0"`
   - Updates `version` in `local_objectives` table

7. `status: "Active"`
   - Updates `status` in `local_objectives` table

8. `*Role has execution rights*`
   - Creates records in `role_permissions` table for the role and LO

9. `*Inputs: Entity with attr1*, attr2*, attr3 [info]*`
   - Creates a record in `lo_input_stack` table with reference to the LO
   - Creates records in `lo_input_items` table for each attribute
   - Sets `required` = true for attributes marked with *
   - Sets `is_info` = true for attributes marked with [info]

10. `* System generates Entity.attr1 using generate_id with prefix "prefix".`
    - Creates a record in `lo_system_functions` table with:
      - `lo_name` = LO Name
      - `function_name` = generate_id
      - `parameters` = {"prefix": "prefix"}
      - `output_to` = Entity.attr1

11. `*Outputs: Entity with attr1, attr2, attr3*`
    - Creates a record in `lo_output_stack` table with reference to the LO
    - Creates records in `lo_output_items` table for each attribute

12. `*DB Stack:*`
    - Creates records in `lo_input_validations` table for each validation

13. `* Entity.attr1 (string) is mandatory and unique. Error message: "Error message"`
    - Creates a record in `lo_input_validations` table with:
      - `rule_type` = unique
      - `entity` = Entity
      - `attribute` = attr1
      - `error_message` = "Error message"

14. `*UI Stack:*`
    - Updates `ui_stack` in `local_objectives` table

15. `* Entity.attr1 displays as oj-input-text with readonly property.`
    - Updates `ui_control` in `lo_input_items` table for Entity.attr1
    - Updates `metadata` in `lo_input_items` table with UI properties

16. `*Mapping Stack:*`
    - Creates records in `lo_output_triggers` table for LO-to-LO mappings

17. `* LO1.output.attr1 maps to LO2.input.attr1 using direct mapping.`
    - Creates a record in `lo_output_triggers` table with:
      - `output_item_id` = LO1.output.attr1's output_item_id
      - `target_lo` = LO2
      - `target_input` = attr1
      - `mapping_type` = direct

18. `*Nested Functions:*`
    - Creates records in `lo_nested_functions` table for each nested function

19. `* Function generate_id(prefix) for Entity.attr1`
    - Creates a record in `lo_nested_functions` table with:
      - `lo_name` = LO Name
      - `function_name` = generate_id
      - `parameters` = [{"name": "prefix", "type": "string"}]
      - `writes_to` = Entity.attr1

20. `Execution pathway:`
    - Creates records in `execution_pathways` table for each pathway

21. `* When Entity.attr1 = value1, route to LO2.`
    - Creates a record in `execution_pathways` table with:
      - `lo_name` = LO Name
      - `pathway_type` = conditional
      - `next_lo` = LO2
    - Creates a record in `execution_pathway_conditions` table with:
      - `lo_name` = LO Name
      - `condition_entity` = Entity
      - `condition_attribute` = attr1
      - `condition_operator` = =
      - `condition_value` = value1
      - `next_lo` = LO2

22. `Synthetic values:`
    - Used to generate sample data for testing

## Role Mapping

Roles are defined in natural language prescriptives and mapped to multiple database tables.

### Role Definition

Natural language format:
```
Role RoleName:
- Create: Entity1, Entity2
- Read: Entity1, Entity2, Entity3
- Update: Entity1, Entity2
- GO: GO1 as Originator, GO2 as ProcessOwner
- Scope: Own
- Classification: Standard
- Special: special conditions

Role RoleName2 inherits RoleName:
- Create: Entity3, Entity4
- Read: Entity3, Entity4, Entity5
- Update: Entity3, Entity4
- GO: GO3 as Originator, GO4 as ProcessOwner
- Scope: Team
- Classification: Standard
- Special: special conditions
```

### Database Tables

#### 1. `roles` Table

Stores basic role information.

| Column | Description | Example Value |
|--------|-------------|--------------|
| id | Auto-incrementing primary key | 1 |
| role_id | Unique identifier for the role (auto-generated) | role_employee |
| name | Role name | Employee |
| tenant_id | Reference to the tenant | t001 |
| created_at | Creation timestamp | 2023-01-01 00:00:00 |
| updated_at | Update timestamp | 2023-01-01 00:00:00 |
| description | Role description | |
| permissions | Permissions (JSON) | [...] |
| scope | Role scope | Own |
| classification | Role classification | Standard |
| special_conditions | Special conditions | budget approval up to $10K |
| version_type | Version type | v2 |

#### 2. `role_permissions` Table

Stores role permissions.

| Column | Description | Example Value |
|--------|-------------|--------------|
| id | Auto-incrementing primary key | 1 |
| role_id | Reference to the role | role_employee |
| context_id | Reference to the permission context | default |
| created_at | Creation timestamp | 2023-01-01 00:00:00 |
| updated_at | Update timestamp | 2023-01-01 00:00:00 |
| permission_name | Permission name | create:Entity1 |

#### 3. `permission_contexts` Table

Stores permission contexts.

| Column | Description | Example Value |
|--------|-------------|--------------|
| id | Auto-incrementing primary key | 1 |
| context_id | Unique identifier for the context | default |
| name | Context name | Default Context |
| description | Context description | Default permission context |
| context_type | Context type | global |
| created_at | Creation timestamp | 2023-01-01 00:00:00 |
| updated_at | Update timestamp | 2023-01-01 00:00:00 |

#### 4. `role_inheritance` Table

Stores role inheritance.

| Column | Description | Example Value |
|--------|-------------|--------------|
| id | Auto-incrementing primary key | 1 |
| role_id | Reference to the role | role_manager |
| parent_role_id | Reference to the parent role | role_employee |
| created_at | Creation timestamp | 2023-01-01 00:00:00 |
| updated_at | Update timestamp | 2023-01-01 00:00:00 |

### Mapping Process

1. The `role_parser.py` parses the natural language prescriptive text and extracts role information.
2. The `role_deployer.py` deploys the parsed role information to the database tables.
3. The role ID is auto-generated as role_rolename, role_rolename2, etc.
4. Permissions are stored in the role_permissions table.
5. Inheritance is stored in the role_inheritance table.

### Validation Process

During the parsing and deployment process, the following validations are performed:

1. **Entity References Validation**:
   - Verify that all entities referenced in the role permissions exist in the database.
   - If an entity doesn't exist, log a warning and continue with other validations.

2. **GO References Validation**:
   - Verify that all GOs referenced in the role permissions exist in the database.
   - If a GO doesn't exist, log a warning and continue with other validations.

3. **Role References Validation**:
   - Verify that all roles referenced in the inheritance exist in the database.
   - If a role doesn't exist, log a warning and continue with other validations.

### Error Handling

If any validation fails, the system will:

1. Log detailed error messages with the specific validation that failed.
2. Provide suggestions for how to fix the validation errors.
3. Continue with other validations to identify all issues at once.
4. Return a comprehensive list of validation errors to the user.

### Line-by-Line Mapping

Here's a detailed line-by-line mapping of the natural language prescriptive to database tables and columns:

1. `Role RoleName:`
   - Creates a record in `roles` table with auto-generated `role_id` and `name` = RoleName

2. `- Create: Entity1, Entity2`
   - Creates records in `role_permissions` table for each entity with permission_name = create:Entity1, create:Entity2

3. `- Read: Entity1, Entity2, Entity3`
   - Creates records in `role_permissions` table for each entity with permission_name = read:Entity1, read:Entity2, read:Entity3

4. `- Update: Entity1, Entity2`
   - Creates records in `role_permissions` table for each entity with permission_name = update:Entity1, update:Entity2

5. `- GO: GO1 as Originator, GO2 as ProcessOwner`
   - Creates records in `role_permissions` table for each GO with permission_name = go:GO1:Originator, go:GO2:ProcessOwner

6. `- Scope: Own`
   - Updates `scope` in `roles` table

7. `- Classification: Standard`
   - Updates `classification` in `roles` table

8. `- Special: special conditions`
   - Updates `special_conditions` in `roles` table

9. `Role RoleName2 inherits RoleName:`
   - Creates a record in `roles` table with auto-generated `role_id` and `name` = RoleName2
   - Creates a record in `role_inheritance` table with role_id = role_rolename2 and parent_role_id = role_rolename

## Validation Rules

The following validation rules are applied during the parsing and deployment process:

### Entity Validation

1. Entity names must be unique.
2. Attribute names must be unique within an entity.
3. Foreign key attributes must reference existing entities.
4. Relationship source and target entities must exist.
5. Relationship source and target attributes must exist.
6. Business rule entities must exist.
7. Calculated field attributes must exist.

### GO Validation

1. GO names must be unique.
2. Entity references in input and output stacks must reference existing entities.
3. Attribute references in input and output stacks must reference existing attributes.
4. LO references in process flow must reference existing LOs.
5. GO references in output mapping stack must reference existing GOs.
6. Business rules must reference existing LOs.
7. Rollback pathways must reference existing LOs.

### LO Validation

1. LO names must be unique.
2. Entity references in input and output stacks must reference existing entities.
3. Attribute references in input and output stacks must reference existing attributes.
4. LO references in execution pathways must reference existing LOs.
5. System functions must be valid and function_type must match existing system functions.
6. UI controls must be valid.
7. Nested functions must have valid parameters and return types.
8. UI stack properties must be valid (e.g., visible=true/false).

### Role Validation

1. Role names must be unique.
2. Entity references in permissions must reference existing entities.
3. GO references in permissions must reference existing GOs.
4. Inherited roles must exist.

## Implementation Plan

To implement the parser and deployer logic from Natural Language Prescriptives to Postgres Database, the following steps should be taken:

### 1. Entity Implementation

1. Parse entity definitions using `entity_parser.py`.
2. Validate entity references and relationships.
3. Deploy entities to the database using `entity_deployer.py`.
4. Create entity tables with the naming convention e1_entityname, e2_entityname, etc.
5. Populate entity attributes, relationships, business rules, and validations.

### 2. GO Implementation

1. Parse GO definitions using `go_parser.py`.
2. Validate entity references, LO references, and GO references.
3. Deploy GOs to the database using `go_deployer.py`.
4. Create tenant, book, and chapter records if needed.
5. Populate input stack, output stack, and mappings.
6. Store process mining schema, performance metadata, and other JSON data.

### 3. LO Implementation

1. Parse LO definitions using `lo_parser.py`.
2. Validate entity references, LO references, and system functions.
3. Deploy LOs to the database using `lo_deployer.py`.
4. Populate input stack, output stack, and mappings.
5. Create execution pathways and conditions.
6. Store nested functions, system functions, and UI controls.

### 4. Role Implementation

1. Parse role definitions using `role_parser.py`.
2. Validate entity references, GO references, and inherited roles.
3. Deploy roles to the database using `role_deployer.py`.
4. Create permission contexts if needed.
5. Populate role permissions and inheritance.

### 5. Validation Implementation

1. Implement validation checks in each parser to ensure data integrity.
2. Validate references between components (e.g., GO references to LOs, LO references to entities).
3. Ensure that all required fields are present and valid.
4. Check for duplicate names and IDs.
5. Validate that referenced entities, attributes, LOs, and GOs exist.

### 6. Deployment Sequence

The deployment sequence is important to ensure that dependencies are satisfied:

1. Deploy entities first, as they are referenced by GOs, LOs, and roles.
2. Deploy GOs next, as they are referenced by LOs and roles.
3. Deploy LOs next, as they are referenced by roles.
4. Deploy roles last, as they reference entities, GOs, and LOs.

### 7. Error Handling

1. Implement comprehensive error handling in each parser and deployer.
2. Log errors and warnings for debugging purposes.
3. Provide clear error messages to the user.
4. Roll back database changes if an error occurs during deployment.

### 8. Testing

1. Create unit tests for each parser and deployer.
2. Create integration tests to ensure that the components work together correctly.
3. Test with sample data to ensure that the system works as expected.
4. Test error handling to ensure that the system gracefully handles invalid input.

### 9. Documentation

1. Document the mapping between natural language prescriptives and database tables.
2. Document the validation rules and error messages.
3. Document the deployment sequence and dependencies.
4. Document the testing process and results.

### 10. Maintenance

1. Implement a versioning system to track changes to the parsers and deployers.
2. Implement a migration system to handle changes to the database schema.
3. Implement a backup system to ensure that data is not lost during deployment.
4. Implement a monitoring system to track the performance of the parsers and deployers.
