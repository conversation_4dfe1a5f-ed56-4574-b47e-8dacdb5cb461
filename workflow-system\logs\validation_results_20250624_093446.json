{"success": false, "component_type": "entities", "target_schema": "workflow_temp", "validation_timestamp": "2025-06-24T09:34:46.110153", "errors": [{"rule_id": "RULE_11", "message": "Entity 'E14' must have at least one attribute", "severity": "error", "entity_id": "E14", "attribute_id": null, "timestamp": "2025-06-24T09:34:46.110125"}], "warnings": [{"rule_id": "RULE_02", "message": "Invalid status transition from deployed_to_production to deployed_to_temp", "severity": "warning", "entity_id": "E14", "attribute_id": null, "timestamp": "2025-06-24T09:34:46.073268"}, {"rule_id": "RULE_07", "message": "Version mismatch between schemas for entity E14: 4 vs 1", "severity": "warning", "entity_id": "E14", "attribute_id": null, "timestamp": "2025-06-24T09:34:46.102229"}], "error_count": 1, "warning_count": 2}