#!/usr/bin/env python3
"""
Generate a migration script to update the workflow_runtime schema.

This script analyzes the temporary schema created by deploy_to_temp_schema.py
and generates SQL statements to update the workflow_runtime schema with the
necessary columns.
"""

import os
import sys
import logging
import argparse
from typing import Dict, List, Tuple, Any, Optional

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('generate_migration_script')

# Import required modules
from db_utils import execute_query

def compare_schemas(source_schema: str, target_schema: str) -> Tuple[bool, List[str], List[str]]:
    """
    Compare the source and target schemas and generate SQL statements to update the target schema.
    
    Args:
        source_schema: Source schema (e.g., workflow_temp)
        target_schema: Target schema (e.g., workflow_runtime)
        
    Returns:
        Tuple containing:
            - Boolean indicating if comparison was successful
            - List of SQL statements to update the target schema
            - List of messages (warnings, errors, or success messages)
    """
    messages = []
    sql_statements = []
    
    try:
        # Get list of tables in source schema
        success, query_messages, result = execute_query(
            f"""
            SELECT table_name
            FROM information_schema.tables
            WHERE table_schema = %s
            AND table_type = 'BASE TABLE'
            ORDER BY table_name
            """,
            (source_schema,)
        )
        
        if not success:
            messages.extend(query_messages)
            return False, [], messages
        
        if not result:
            messages.append(f"No tables found in schema '{source_schema}'")
            return False, [], messages
        
        source_tables = [row[0] for row in result]
        messages.append(f"Found {len(source_tables)} tables in schema '{source_schema}'")
        
        # Get list of tables in target schema
        success, query_messages, result = execute_query(
            f"""
            SELECT table_name
            FROM information_schema.tables
            WHERE table_schema = %s
            AND table_type = 'BASE TABLE'
            ORDER BY table_name
            """,
            (target_schema,)
        )
        
        if not success:
            messages.extend(query_messages)
            return False, [], messages
        
        target_tables = [row[0] for row in result] if result else []
        messages.append(f"Found {len(target_tables)} tables in schema '{target_schema}'")
        
        # Compare tables
        for table in source_tables:
            if table not in target_tables:
                # Table doesn't exist in target schema, create it
                success, query_messages, result = execute_query(
                    f"""
                    SELECT column_name, data_type, character_maximum_length, 
                           is_nullable, column_default
                    FROM information_schema.columns
                    WHERE table_schema = %s
                    AND table_name = %s
                    ORDER BY ordinal_position
                    """,
                    (source_schema, table)
                )
                
                if not success:
                    messages.extend(query_messages)
                    continue
                
                if not result:
                    messages.append(f"No columns found for table '{table}' in schema '{source_schema}'")
                    continue
                
                # Build CREATE TABLE statement
                columns = []
                for row in result:
                    column_name, data_type, max_length, is_nullable, default = row
                    
                    # Format data type
                    if data_type == 'character varying' and max_length:
                        data_type = f"VARCHAR({max_length})"
                    elif data_type == 'character' and max_length:
                        data_type = f"CHAR({max_length})"
                    
                    # Format nullable
                    nullable = "NULL" if is_nullable == "YES" else "NOT NULL"
                    
                    # Format default
                    default_clause = f"DEFAULT {default}" if default else ""
                    
                    # Combine
                    column_def = f"{column_name} {data_type} {nullable} {default_clause}".strip()
                    columns.append(column_def)
                
                # Get primary key
                success, query_messages, result = execute_query(
                    f"""
                    SELECT kcu.column_name
                    FROM information_schema.table_constraints tc
                    JOIN information_schema.key_column_usage kcu
                    ON tc.constraint_name = kcu.constraint_name
                    AND tc.table_schema = kcu.table_schema
                    WHERE tc.constraint_type = 'PRIMARY KEY'
                    AND tc.table_schema = %s
                    AND tc.table_name = %s
                    ORDER BY kcu.ordinal_position
                    """,
                    (source_schema, table)
                )
                
                if success and result:
                    pk_columns = [row[0] for row in result]
                    if pk_columns:
                        columns.append(f"PRIMARY KEY ({', '.join(pk_columns)})")
                
                # Create table
                column_sql = ",\n    ".join(columns)
                create_table_sql = f"""
CREATE TABLE {target_schema}.{table} (
    {column_sql}
);
"""
                
                sql_statements.append(create_table_sql)
                messages.append(f"Generated CREATE TABLE statement for '{table}'")
            else:
                # Table exists in target schema, compare columns
                success, query_messages, result = execute_query(
                    f"""
                    SELECT column_name, data_type, character_maximum_length, 
                           is_nullable, column_default
                    FROM information_schema.columns
                    WHERE table_schema = %s
                    AND table_name = %s
                    ORDER BY ordinal_position
                    """,
                    (source_schema, table)
                )
                
                if not success:
                    messages.extend(query_messages)
                    continue
                
                if not result:
                    messages.append(f"No columns found for table '{table}' in schema '{source_schema}'")
                    continue
                
                source_columns = {row[0]: row for row in result}
                
                success, query_messages, result = execute_query(
                    f"""
                    SELECT column_name, data_type, character_maximum_length, 
                           is_nullable, column_default
                    FROM information_schema.columns
                    WHERE table_schema = %s
                    AND table_name = %s
                    ORDER BY ordinal_position
                    """,
                    (target_schema, table)
                )
                
                if not success:
                    messages.extend(query_messages)
                    continue
                
                if not result:
                    messages.append(f"No columns found for table '{table}' in schema '{target_schema}'")
                    continue
                
                target_columns = {row[0]: row for row in result}
                
                # Find columns that exist in source but not in target
                for column_name, column_info in source_columns.items():
                    if column_name not in target_columns:
                        # Column doesn't exist in target schema, add it
                        data_type, max_length, is_nullable, default = column_info[1:]
                        
                        # Format data type
                        if data_type == 'character varying' and max_length:
                            data_type = f"VARCHAR({max_length})"
                        elif data_type == 'character' and max_length:
                            data_type = f"CHAR({max_length})"
                        
                        # Format nullable
                        nullable = "NULL" if is_nullable == "YES" else "NOT NULL"
                        
                        # Format default
                        default_clause = f"DEFAULT {default}" if default else ""
                        
                        # Combine
                        column_def = f"{column_name} {data_type} {nullable} {default_clause}".strip()
                        
                        # Add column
                        alter_table_sql = f"""
ALTER TABLE {target_schema}.{table} ADD COLUMN {column_def};
"""
                        
                        sql_statements.append(alter_table_sql)
                        messages.append(f"Generated ALTER TABLE statement to add column '{column_name}' to '{table}'")
                    else:
                        # Column exists in target schema, compare data types
                        source_data_type, source_max_length, source_is_nullable, source_default = column_info[1:]
                        target_data_type, target_max_length, target_is_nullable, target_default = target_columns[column_name][1:]
                        
                        # Format data types
                        if source_data_type == 'character varying' and source_max_length:
                            source_data_type = f"VARCHAR({source_max_length})"
                        elif source_data_type == 'character' and source_max_length:
                            source_data_type = f"CHAR({source_max_length})"
                        
                        if target_data_type == 'character varying' and target_max_length:
                            target_data_type = f"VARCHAR({target_max_length})"
                        elif target_data_type == 'character' and target_max_length:
                            target_data_type = f"CHAR({target_max_length})"
                        
                        # Compare data types
                        if source_data_type != target_data_type:
                            # Data types are different, alter column
                            alter_table_sql = f"""
ALTER TABLE {target_schema}.{table} ALTER COLUMN {column_name} TYPE {source_data_type};
"""
                            
                            sql_statements.append(alter_table_sql)
                            messages.append(f"Generated ALTER TABLE statement to change data type of column '{column_name}' in '{table}'")
                        
                        # Compare nullability
                        if source_is_nullable != target_is_nullable:
                            # Nullability is different, alter column
                            if source_is_nullable == "YES":
                                alter_table_sql = f"""
ALTER TABLE {target_schema}.{table} ALTER COLUMN {column_name} DROP NOT NULL;
"""
                            else:
                                alter_table_sql = f"""
ALTER TABLE {target_schema}.{table} ALTER COLUMN {column_name} SET NOT NULL;
"""
                            
                            sql_statements.append(alter_table_sql)
                            messages.append(f"Generated ALTER TABLE statement to change nullability of column '{column_name}' in '{table}'")
                        
                        # Compare defaults
                        if source_default != target_default:
                            # Defaults are different, alter column
                            if source_default:
                                alter_table_sql = f"""
ALTER TABLE {target_schema}.{table} ALTER COLUMN {column_name} SET DEFAULT {source_default};
"""
                            else:
                                alter_table_sql = f"""
ALTER TABLE {target_schema}.{table} ALTER COLUMN {column_name} DROP DEFAULT;
"""
                            
                            sql_statements.append(alter_table_sql)
                            messages.append(f"Generated ALTER TABLE statement to change default of column '{column_name}' in '{table}'")
        
        # Get list of foreign keys in source schema
        success, query_messages, result = execute_query(
            f"""
            SELECT
                tc.table_name,
                kcu.column_name,
                ccu.table_name AS foreign_table_name,
                ccu.column_name AS foreign_column_name,
                tc.constraint_name
            FROM information_schema.table_constraints tc
            JOIN information_schema.key_column_usage kcu
            ON tc.constraint_name = kcu.constraint_name
            AND tc.table_schema = kcu.table_schema
            JOIN information_schema.constraint_column_usage ccu
            ON ccu.constraint_name = tc.constraint_name
            AND ccu.table_schema = tc.table_schema
            WHERE tc.constraint_type = 'FOREIGN KEY'
            AND tc.table_schema = %s
            """,
            (source_schema,)
        )
        
        if success and result:
            # Add foreign keys to target schema
            for row in result:
                table_name, column_name, foreign_table_name, foreign_column_name, constraint_name = row
                
                # Skip if table doesn't exist in target schema
                if table_name not in target_tables:
                    continue
                
                # Skip if foreign table doesn't exist in target schema
                if foreign_table_name not in target_tables:
                    continue
                
                # Check if constraint already exists in target schema
                success, query_messages, constraint_result = execute_query(
                    f"""
                    SELECT constraint_name
                    FROM information_schema.table_constraints
                    WHERE constraint_type = 'FOREIGN KEY'
                    AND table_schema = %s
                    AND table_name = %s
                    AND constraint_name = %s
                    """,
                    (target_schema, table_name, constraint_name)
                )
                
                if not success:
                    messages.extend(query_messages)
                    continue
                
                constraint_exists = constraint_result and len(constraint_result) > 0
                
                if not constraint_exists:
                    # Create foreign key
                    alter_table_sql = f"""
ALTER TABLE {target_schema}.{table_name}
ADD CONSTRAINT {constraint_name}
FOREIGN KEY ({column_name})
REFERENCES {target_schema}.{foreign_table_name} ({foreign_column_name});
"""
                    
                    sql_statements.append(alter_table_sql)
                    messages.append(f"Generated ALTER TABLE statement to add foreign key '{constraint_name}' to '{table_name}'")
        
        messages.append(f"Schema comparison completed successfully")
        return True, sql_statements, messages
    except Exception as e:
        error_msg = f"Error comparing schemas: {str(e)}"
        logger.error(error_msg, exc_info=True)
        messages.append(error_msg)
        return False, [], messages

def main():
    """Main function."""
    parser = argparse.ArgumentParser(description='Generate a migration script to update the workflow_runtime schema')
    parser.add_argument('--source-schema', default='workflow_temp', help='Source schema (e.g., workflow_temp)')
    parser.add_argument('--target-schema', default='workflow_runtime', help='Target schema (e.g., workflow_runtime)')
    parser.add_argument('--output-file', default='migration_script.sql', help='Output file for the migration script')
    args = parser.parse_args()
    
    logger.info(f"Comparing schemas '{args.source_schema}' and '{args.target_schema}'")
    
    # Compare schemas
    success, sql_statements, messages = compare_schemas(args.source_schema, args.target_schema)
    
    for message in messages:
        logger.info(message)
    
    if not success:
        logger.error("Schema comparison failed")
        sys.exit(1)
    
    if not sql_statements:
        logger.info("No differences found between schemas")
        sys.exit(0)
    
    # Write SQL statements to file
    try:
        with open(args.output_file, 'w') as f:
            f.write("-- Migration script to update the workflow_runtime schema\n")
            f.write(f"-- Generated by {os.path.basename(__file__)}\n")
            f.write(f"-- Source schema: {args.source_schema}\n")
            f.write(f"-- Target schema: {args.target_schema}\n\n")
            
            for sql in sql_statements:
                f.write(sql)
                f.write("\n")
        
        logger.info(f"Migration script written to {args.output_file}")
    except Exception as e:
        logger.error(f"Error writing migration script: {str(e)}")
        sys.exit(1)

if __name__ == '__main__':
    main()
