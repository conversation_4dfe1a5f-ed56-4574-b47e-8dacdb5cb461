{"timestamp": "2025-06-23T05:09:43.912694", "endpoint": "parse-validate-mongosave/attribute-enum-values", "input": {"natural_language": "Tenant: Acme Corp\n\nEntity.Attribute | Enum Name | Value | Display | Description | Sort Order | Active\n\nLeaveApplication.status | Leave Application Status | Pending | Pending | Leave request is pending approval | 1 | true\n\nLeaveApplication.status | Leave Application Status | Approved | Approved | Leave request has been approved | 2 | true\n\nLeaveApplication.status | Leave Application Status | Rejected | Rejected | Leave request has been rejected | 3 | true\n\nLeaveApplication.status | Leave Application Status | Cancelled | Cancelled | Leave request has been cancelled | 4 | true\n\nLeaveApplication.requiresDocumentation | Documentation Requirement | true | Yes | Documentation is required | 1 | true\n\nLeaveApplication.requiresDocumentation | Documentation Requirement | false | No | Documentation is not required | 2 | true\n\nEmployee.employmentStatus | Employment Status | Active | Active | Employee is currently active | 1 | true\n\nEmployee.employmentStatus | Employment Status | Inactive | Inactive | Employee is temporarily inactive | 2 | true\n\nEmployee.employmentStatus | Employment Status | Terminated | Terminated | Employee has been terminated | 3 | true", "tenant_id": "T_ACME_CORP", "edit_draft": false, "edit_temp": false, "edit_production": false}, "output": {"success": true, "enum_value_results": [{"success": false, "errors": ["Attribute with attribute_id A_E_LeaveApplication_1750655383_status_1750655383 does not exist"], "parsed_data": {"enum_value_id": "EV_LEAVEAPPLICATION_STATUS_PENDING", "attribute_id": "A_E_LeaveApplication_1750655383_status_1750655383", "value": "Pending", "display_name": "Pending", "description": "Leave request is pending approval", "sort_order": 1, "is_active": true, "natural_language": "Entity.Attribute: LeaveApplication.status\nEnum Name: Leave Application Status\nValue: Pending\nDisplay: Pending\nDescription: Leave request is pending approval", "version": 1, "status": "active", "created_at": "2025-06-23T05:09:43.563618", "updated_at": "2025-06-23T05:09:43.563618", "created_by": "Tarun", "updated_by": "Tarun", "enum_value_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Attribute with attribute_id A_E_LeaveApplication_1750655383_status_1750655383 does not exist"], "parsed_data": {"enum_value_id": "EV_LEAVEAPPLICATION_STATUS_APPROVED", "attribute_id": "A_E_LeaveApplication_1750655383_status_1750655383", "value": "Approved", "display_name": "Approved", "description": "Leave request has been approved", "sort_order": 2, "is_active": true, "natural_language": "Entity.Attribute: LeaveApplication.status\nEnum Name: Leave Application Status\nValue: Approved\nDisplay: Approved\nDescription: Leave request has been approved", "version": 1, "status": "active", "created_at": "2025-06-23T05:09:43.589729", "updated_at": "2025-06-23T05:09:43.589729", "created_by": "Tarun", "updated_by": "Tarun", "enum_value_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Attribute with attribute_id A_E_LeaveApplication_1750655383_status_1750655383 does not exist"], "parsed_data": {"enum_value_id": "EV_LEAVEAPPLICATION_STATUS_REJECTED", "attribute_id": "A_E_LeaveApplication_1750655383_status_1750655383", "value": "Rejected", "display_name": "Rejected", "description": "Leave request has been rejected", "sort_order": 3, "is_active": true, "natural_language": "Entity.Attribute: LeaveApplication.status\nEnum Name: Leave Application Status\nValue: Rejected\nDisplay: Rejected\nDescription: Leave request has been rejected", "version": 1, "status": "active", "created_at": "2025-06-23T05:09:43.613130", "updated_at": "2025-06-23T05:09:43.613130", "created_by": "Tarun", "updated_by": "Tarun", "enum_value_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Attribute with attribute_id A_E_LeaveApplication_1750655383_status_1750655383 does not exist"], "parsed_data": {"enum_value_id": "EV_LEAVEAPPLICATION_STATUS_CANCELLED", "attribute_id": "A_E_LeaveApplication_1750655383_status_1750655383", "value": "Cancelled", "display_name": "Cancelled", "description": "Leave request has been cancelled", "sort_order": 4, "is_active": true, "natural_language": "Entity.Attribute: LeaveApplication.status\nEnum Name: Leave Application Status\nValue: Cancelled\nDisplay: Cancelled\nDescription: Leave request has been cancelled", "version": 1, "status": "active", "created_at": "2025-06-23T05:09:43.634205", "updated_at": "2025-06-23T05:09:43.634205", "created_by": "Tarun", "updated_by": "Tarun", "enum_value_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Attribute with attribute_id A_E_LeaveApplication_1750655383_requiresDocumentation_1750655383 does not exist"], "parsed_data": {"enum_value_id": "EV_LEAVEAPPLICATION_REQUIRESDOCUMENTATION_TRUE", "attribute_id": "A_E_LeaveApplication_1750655383_requiresDocumentation_1750655383", "value": "true", "display_name": "Yes", "description": "Documentation is required", "sort_order": 1, "is_active": true, "natural_language": "Entity.Attribute: LeaveApplication.requiresDocumentation\nEnum Name: Documentation Requirement\nValue: true\nDisplay: Yes\nDescription: Documentation is required", "version": 1, "status": "active", "created_at": "2025-06-23T05:09:43.656377", "updated_at": "2025-06-23T05:09:43.656377", "created_by": "Tarun", "updated_by": "Tarun", "enum_value_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Attribute with attribute_id A_E_LeaveApplication_1750655383_requiresDocumentation_1750655383 does not exist"], "parsed_data": {"enum_value_id": "EV_LEAVEAPPLICATION_REQUIRESDOCUMENTATION_FALSE", "attribute_id": "A_E_LeaveApplication_1750655383_requiresDocumentation_1750655383", "value": "false", "display_name": "No", "description": "Documentation is not required", "sort_order": 2, "is_active": true, "natural_language": "Entity.Attribute: LeaveApplication.requiresDocumentation\nEnum Name: Documentation Requirement\nValue: false\nDisplay: No\nDescription: Documentation is not required", "version": 1, "status": "active", "created_at": "2025-06-23T05:09:43.676967", "updated_at": "2025-06-23T05:09:43.676967", "created_by": "Tarun", "updated_by": "Tarun", "enum_value_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Attribute with attribute_id A_E_Employee_1750655383_employmentStatus_1750655383 does not exist"], "parsed_data": {"enum_value_id": "EV_EMPLOYEE_EMPLOYMENTSTATUS_ACTIVE", "attribute_id": "A_E_Employee_1750655383_employmentStatus_1750655383", "value": "Active", "display_name": "Active", "description": "Employee is currently active", "sort_order": 1, "is_active": true, "natural_language": "Entity.Attribute: Employee.employmentStatus\nEnum Name: Employment Status\nValue: Active\nDisplay: Active\nDescription: Employee is currently active", "version": 1, "status": "active", "created_at": "2025-06-23T05:09:43.698525", "updated_at": "2025-06-23T05:09:43.698525", "created_by": "Tarun", "updated_by": "Tarun", "enum_value_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Attribute with attribute_id A_E_Employee_1750655383_employmentStatus_1750655383 does not exist"], "parsed_data": {"enum_value_id": "EV_EMPLOYEE_EMPLOYMENTSTATUS_INACTIVE", "attribute_id": "A_E_Employee_1750655383_employmentStatus_1750655383", "value": "Inactive", "display_name": "Inactive", "description": "Employee is temporarily inactive", "sort_order": 2, "is_active": true, "natural_language": "Entity.Attribute: Employee.employmentStatus\nEnum Name: Employment Status\nValue: Inactive\nDisplay: Inactive\nDescription: Employee is temporarily inactive", "version": 1, "status": "active", "created_at": "2025-06-23T05:09:43.717710", "updated_at": "2025-06-23T05:09:43.717710", "created_by": "Tarun", "updated_by": "Tarun", "enum_value_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Attribute with attribute_id A_E_Employee_1750655383_employmentStatus_1750655383 does not exist"], "parsed_data": {"enum_value_id": "EV_EMPLOYEE_EMPLOYMENTSTATUS_TERMINATED", "attribute_id": "A_E_Employee_1750655383_employmentStatus_1750655383", "value": "Terminated", "display_name": "Terminated", "description": "Employee has been terminated", "sort_order": 3, "is_active": true, "natural_language": "Entity.Attribute: Employee.employmentStatus\nEnum Name: Employment Status\nValue: Terminated\nDisplay: Terminated\nDescription: Employee has been terminated", "version": 1, "status": "active", "created_at": "2025-06-23T05:09:43.737666", "updated_at": "2025-06-23T05:09:43.737666", "created_by": "Tarun", "updated_by": "Tarun", "enum_value_status": "new", "changes_detected": []}}], "operation": "parse_validate_mongosave", "total_enum_values": 9}, "status": "success"}
{"timestamp": "2025-06-23T05:49:13.201495", "endpoint": "parse-validate-mongosave/attribute-enum-values", "input": {"natural_language": "Tenant: Acme Corp Test\n\nEntity.Attribute | Enum Name | Value | Display | Description | Sort Order | Active\n\nLeaveApplication.status | Leave Application Status | Pending | Pending | Leave request is pending approval | 1 | true\nLeaveApplication.status | Leave Application Status | Approved | Approved | Leave request has been approved | 2 | true\nLeaveApplication.status | Leave Application Status | Rejected | Rejected | Leave request has been rejected | 3 | true", "tenant_id": "T1007", "edit_draft": false, "edit_temp": false, "edit_production": false}, "output": {"success": true, "enum_value_results": [{"success": false, "errors": ["Attribute with attribute_id A_E_LeaveApplication_1750657753_status_1750657753 does not exist"], "parsed_data": {"enum_value_id": "EV_LEAVEAPPLICATION_STATUS_PENDING", "attribute_id": "A_E_LeaveApplication_1750657753_status_1750657753", "value": "Pending", "display_name": "Pending", "description": "Leave request is pending approval", "sort_order": 1, "is_active": true, "natural_language": "Entity.Attribute: LeaveApplication.status\nEnum Name: Leave Application Status\nValue: Pending\nDisplay: Pending\nDescription: Leave request is pending approval", "version": 1, "status": "active", "created_at": "2025-06-23T05:49:13.085554", "updated_at": "2025-06-23T05:49:13.085554", "created_by": "Tarun", "updated_by": "Tarun", "enum_value_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Attribute with attribute_id A_E_LeaveApplication_1750657753_status_1750657753 does not exist"], "parsed_data": {"enum_value_id": "EV_LEAVEAPPLICATION_STATUS_APPROVED", "attribute_id": "A_E_LeaveApplication_1750657753_status_1750657753", "value": "Approved", "display_name": "Approved", "description": "Leave request has been approved", "sort_order": 2, "is_active": true, "natural_language": "Entity.Attribute: LeaveApplication.status\nEnum Name: Leave Application Status\nValue: Approved\nDisplay: Approved\nDescription: Leave request has been approved", "version": 1, "status": "active", "created_at": "2025-06-23T05:49:13.110472", "updated_at": "2025-06-23T05:49:13.110472", "created_by": "Tarun", "updated_by": "Tarun", "enum_value_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Attribute with attribute_id A_E_LeaveApplication_1750657753_status_1750657753 does not exist"], "parsed_data": {"enum_value_id": "EV_LEAVEAPPLICATION_STATUS_REJECTED", "attribute_id": "A_E_LeaveApplication_1750657753_status_1750657753", "value": "Rejected", "display_name": "Rejected", "description": "Leave request has been rejected", "sort_order": 3, "is_active": true, "natural_language": "Entity.Attribute: LeaveApplication.status\nEnum Name: Leave Application Status\nValue: Rejected\nDisplay: Rejected\nDescription: Leave request has been rejected", "version": 1, "status": "active", "created_at": "2025-06-23T05:49:13.133372", "updated_at": "2025-06-23T05:49:13.133372", "created_by": "Tarun", "updated_by": "Tarun", "enum_value_status": "new", "changes_detected": []}}], "operation": "parse_validate_mongosave", "total_enum_values": 3}, "status": "success"}
{"timestamp": "2025-06-23T05:49:51.939120", "endpoint": "parse-validate-mongosave/attribute-enum-values", "input": {"natural_language": "Tenant: Test\n\nEntity.Attribute | Enum Name | Value | Display | Description | Sort Order | Active\n\nCommon.status | General Status | Active | Active | Item is currently active | 1 | true\nCommon.status | General Status | Inactive | Inactive | Item is inactive | 2 | true", "tenant_id": null, "edit_draft": false, "edit_temp": false, "edit_production": false}, "output": {"success": true, "enum_value_results": [{"success": false, "errors": ["Attribute with attribute_id A_E_Common_1750657791_status_1750657791 does not exist"], "parsed_data": {"enum_value_id": "EV_COMMON_STATUS_ACTIVE", "attribute_id": "A_E_Common_1750657791_status_1750657791", "value": "Active", "display_name": "Active", "description": "Item is currently active", "sort_order": 1, "is_active": true, "natural_language": "Entity.Attribute: Common.status\nEnum Name: General Status\nValue: Active\nDisplay: Active\nDescription: Item is currently active", "version": 1, "status": "active", "created_at": "2025-06-23T05:49:51.870936", "updated_at": "2025-06-23T05:49:51.870936", "created_by": "Tarun", "updated_by": "Tarun", "enum_value_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Attribute with attribute_id A_E_Common_1750657791_status_1750657791 does not exist"], "parsed_data": {"enum_value_id": "EV_COMMON_STATUS_INACTIVE", "attribute_id": "A_E_Common_1750657791_status_1750657791", "value": "Inactive", "display_name": "Inactive", "description": "Item is inactive", "sort_order": 2, "is_active": true, "natural_language": "Entity.Attribute: Common.status\nEnum Name: General Status\nValue: Inactive\nDisplay: Inactive\nDescription: Item is inactive", "version": 1, "status": "active", "created_at": "2025-06-23T05:49:51.894054", "updated_at": "2025-06-23T05:49:51.894054", "created_by": "Tarun", "updated_by": "Tarun", "enum_value_status": "new", "changes_detected": []}}], "operation": "parse_validate_mongosave", "total_enum_values": 2}, "status": "success"}
{"timestamp": "2025-06-23T06:29:01.022727", "endpoint": "parse-validate-mongosave/attribute-enum-values", "input": {"natural_language": "Tenant: Acme Corp Test\n\nEntity.Attribute | Enum Name | Value | Display | Description | Sort Order | Active\n\nEmployee.employmentStatus | Employment Status | Active | Active | Employee is currently active | 1 | true\nEmployee.employmentStatus | Employment Status | Inactive | Inactive | Employee is temporarily inactive | 2 | true\nEmployee.employmentStatus | Employment Status | Terminated | Terminated | Employee has been terminated | 3 | true", "tenant_id": "T1007", "edit_draft": false, "edit_temp": false, "edit_production": false}, "output": {"success": true, "enum_value_results": [{"success": false, "errors": ["Attribute with attribute_id A_E_Employee_1750660140_employmentStatus_1750660140 does not exist"], "parsed_data": {"enum_value_id": "EV_EMPLOYEE_EMPLOYMENTSTATUS_ACTIVE", "attribute_id": "A_E_Employee_1750660140_employmentStatus_1750660140", "value": "Active", "display_name": "Active", "description": "Employee is currently active", "sort_order": 1, "is_active": true, "natural_language": "Entity.Attribute: Employee.employmentStatus\nEnum Name: Employment Status\nValue: Active\nDisplay: Active\nDescription: Employee is currently active", "version": 1, "status": "active", "created_at": "2025-06-23T06:29:00.910713", "updated_at": "2025-06-23T06:29:00.910713", "created_by": "Tarun", "updated_by": "Tarun", "enum_value_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Attribute with attribute_id A_E_Employee_1750660140_employmentStatus_1750660140 does not exist"], "parsed_data": {"enum_value_id": "EV_EMPLOYEE_EMPLOYMENTSTATUS_INACTIVE", "attribute_id": "A_E_Employee_1750660140_employmentStatus_1750660140", "value": "Inactive", "display_name": "Inactive", "description": "Employee is temporarily inactive", "sort_order": 2, "is_active": true, "natural_language": "Entity.Attribute: Employee.employmentStatus\nEnum Name: Employment Status\nValue: Inactive\nDisplay: Inactive\nDescription: Employee is temporarily inactive", "version": 1, "status": "active", "created_at": "2025-06-23T06:29:00.934407", "updated_at": "2025-06-23T06:29:00.934407", "created_by": "Tarun", "updated_by": "Tarun", "enum_value_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Attribute with attribute_id A_E_Employee_1750660140_employmentStatus_1750660140 does not exist"], "parsed_data": {"enum_value_id": "EV_EMPLOYEE_EMPLOYMENTSTATUS_TERMINATED", "attribute_id": "A_E_Employee_1750660140_employmentStatus_1750660140", "value": "Terminated", "display_name": "Terminated", "description": "Employee has been terminated", "sort_order": 3, "is_active": true, "natural_language": "Entity.Attribute: Employee.employmentStatus\nEnum Name: Employment Status\nValue: Terminated\nDisplay: Terminated\nDescription: Employee has been terminated", "version": 1, "status": "active", "created_at": "2025-06-23T06:29:00.961480", "updated_at": "2025-06-23T06:29:00.961480", "created_by": "Tarun", "updated_by": "Tarun", "enum_value_status": "new", "changes_detected": []}}], "operation": "parse_validate_mongosave", "total_enum_values": 3}, "status": "success"}
{"timestamp": "2025-06-23T06:30:48.139532", "endpoint": "parse-validate-mongosave/attribute-enum-values", "input": {"natural_language": "Tenant: Test\n\nEntity.Attribute | Enum Name | Value | Display | Description | Sort Order | Active\n\nCommon.status | General Status | Active | Active | Item is currently active | 1 | true\nCommon.status | General Status | Inactive | Inactive | Item is inactive | 2 | true", "tenant_id": null, "edit_draft": false, "edit_temp": false, "edit_production": false}, "output": {"success": true, "enum_value_results": [{"success": false, "errors": ["Attribute with attribute_id A_E_Common_1750660248_status_1750660248 does not exist"], "parsed_data": {"enum_value_id": "EV_COMMON_STATUS_ACTIVE", "attribute_id": "A_E_Common_1750660248_status_1750660248", "value": "Active", "display_name": "Active", "description": "Item is currently active", "sort_order": 1, "is_active": true, "natural_language": "Entity.Attribute: Common.status\nEnum Name: General Status\nValue: Active\nDisplay: Active\nDescription: Item is currently active", "version": 1, "status": "active", "created_at": "2025-06-23T06:30:48.074987", "updated_at": "2025-06-23T06:30:48.074987", "created_by": "Tarun", "updated_by": "Tarun", "enum_value_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Attribute with attribute_id A_E_Common_1750660248_status_1750660248 does not exist"], "parsed_data": {"enum_value_id": "EV_COMMON_STATUS_INACTIVE", "attribute_id": "A_E_Common_1750660248_status_1750660248", "value": "Inactive", "display_name": "Inactive", "description": "Item is inactive", "sort_order": 2, "is_active": true, "natural_language": "Entity.Attribute: Common.status\nEnum Name: General Status\nValue: Inactive\nDisplay: Inactive\nDescription: Item is inactive", "version": 1, "status": "active", "created_at": "2025-06-23T06:30:48.096763", "updated_at": "2025-06-23T06:30:48.096763", "created_by": "Tarun", "updated_by": "Tarun", "enum_value_status": "new", "changes_detected": []}}], "operation": "parse_validate_mongosave", "total_enum_values": 2}, "status": "success"}
{"timestamp": "2025-06-23T06:37:52.677793", "endpoint": "parse-validate-mongosave/attribute-enum-values", "input": {"natural_language": "Tenant: Acme Corp Test\n\nEntity.Attribute | Enum Name | Value | Display | Description | Sort Order | Active\n\nEmployee.employmentStatus | Employment Status | Active | Active | Employee is currently active | 1 | true\nEmployee.employmentStatus | Employment Status | Inactive | Inactive | Employee is temporarily inactive | 2 | true\nEmployee.employmentStatus | Employment Status | Terminated | Terminated | Employee has been terminated | 3 | true", "tenant_id": "T1007", "edit_draft": false, "edit_temp": false, "edit_production": false}, "output": {"success": true, "enum_value_results": [{"success": false, "errors": ["Attribute with attribute_id A_E_Employee_1750660672_employmentStatus_1750660672 does not exist"], "parsed_data": {"enum_value_id": "EV_EMPLOYEE_EMPLOYMENTSTATUS_ACTIVE", "attribute_id": "A_E_Employee_1750660672_employmentStatus_1750660672", "value": "Active", "display_name": "Active", "description": "Employee is currently active", "sort_order": 1, "is_active": true, "natural_language": "Entity.Attribute: Employee.employmentStatus\nEnum Name: Employment Status\nValue: Active\nDisplay: Active\nDescription: Employee is currently active", "version": 1, "status": "active", "created_at": "2025-06-23T06:37:52.554747", "updated_at": "2025-06-23T06:37:52.554747", "created_by": "Tarun", "updated_by": "Tarun", "enum_value_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Attribute with attribute_id A_E_Employee_1750660672_employmentStatus_1750660672 does not exist"], "parsed_data": {"enum_value_id": "EV_EMPLOYEE_EMPLOYMENTSTATUS_INACTIVE", "attribute_id": "A_E_Employee_1750660672_employmentStatus_1750660672", "value": "Inactive", "display_name": "Inactive", "description": "Employee is temporarily inactive", "sort_order": 2, "is_active": true, "natural_language": "Entity.Attribute: Employee.employmentStatus\nEnum Name: Employment Status\nValue: Inactive\nDisplay: Inactive\nDescription: Employee is temporarily inactive", "version": 1, "status": "active", "created_at": "2025-06-23T06:37:52.582914", "updated_at": "2025-06-23T06:37:52.582914", "created_by": "Tarun", "updated_by": "Tarun", "enum_value_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Attribute with attribute_id A_E_Employee_1750660672_employmentStatus_1750660672 does not exist"], "parsed_data": {"enum_value_id": "EV_EMPLOYEE_EMPLOYMENTSTATUS_TERMINATED", "attribute_id": "A_E_Employee_1750660672_employmentStatus_1750660672", "value": "Terminated", "display_name": "Terminated", "description": "Employee has been terminated", "sort_order": 3, "is_active": true, "natural_language": "Entity.Attribute: Employee.employmentStatus\nEnum Name: Employment Status\nValue: Terminated\nDisplay: Terminated\nDescription: Employee has been terminated", "version": 1, "status": "active", "created_at": "2025-06-23T06:37:52.608887", "updated_at": "2025-06-23T06:37:52.608887", "created_by": "Tarun", "updated_by": "Tarun", "enum_value_status": "new", "changes_detected": []}}], "operation": "parse_validate_mongosave", "total_enum_values": 3}, "status": "success"}
{"timestamp": "2025-06-23T06:53:47.130184", "endpoint": "parse-validate-mongosave/attribute-enum-values", "input": {"natural_language": "Tenant: Acme Corp Test\n\nEntity.Attribute | Enum Name | Value | Display | Description | Sort Order | Active\n\nEmployee.employmentStatus | Employment Status | Active | Active | Employee is currently active | 1 | true\nEmployee.employmentStatus | Employment Status | Inactive | Inactive | Employee is temporarily inactive | 2 | true\nEmployee.employmentStatus | Employment Status | Terminated | Terminated | Employee has been terminated | 3 | true", "tenant_id": "T1007", "edit_draft": false, "edit_temp": false, "edit_production": false}, "output": {"success": true, "enum_value_results": [{"success": false, "errors": ["Attribute with attribute_id A_E_Employee_1750661626_employmentStatus_1750661627 does not exist"], "parsed_data": {"enum_value_id": "EV_EMPLOYEE_EMPLOYMENTSTATUS_ACTIVE", "attribute_id": "A_E_Employee_1750661626_employmentStatus_1750661627", "value": "Active", "display_name": "Active", "description": "Employee is currently active", "sort_order": 1, "is_active": true, "natural_language": "Entity.Attribute: Employee.employmentStatus\nEnum Name: Employment Status\nValue: Active\nDisplay: Active\nDescription: Employee is currently active", "version": 1, "status": "active", "created_at": "2025-06-23T06:53:47.010558", "updated_at": "2025-06-23T06:53:47.010558", "created_by": "Tarun", "updated_by": "Tarun", "enum_value_status": "existing", "changes_detected": []}}, {"success": false, "errors": ["Attribute with attribute_id A_E_Employee_1750661627_employmentStatus_1750661627 does not exist"], "parsed_data": {"enum_value_id": "EV_EMPLOYEE_EMPLOYMENTSTATUS_INACTIVE", "attribute_id": "A_E_Employee_1750661627_employmentStatus_1750661627", "value": "Inactive", "display_name": "Inactive", "description": "Employee is temporarily inactive", "sort_order": 2, "is_active": true, "natural_language": "Entity.Attribute: Employee.employmentStatus\nEnum Name: Employment Status\nValue: Inactive\nDisplay: Inactive\nDescription: Employee is temporarily inactive", "version": 1, "status": "active", "created_at": "2025-06-23T06:53:47.037084", "updated_at": "2025-06-23T06:53:47.037084", "created_by": "Tarun", "updated_by": "Tarun", "enum_value_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Attribute with attribute_id A_E_Employee_1750661627_employmentStatus_1750661627 does not exist"], "parsed_data": {"enum_value_id": "EV_EMPLOYEE_EMPLOYMENTSTATUS_TERMINATED", "attribute_id": "A_E_Employee_1750661627_employmentStatus_1750661627", "value": "Terminated", "display_name": "Terminated", "description": "Employee has been terminated", "sort_order": 3, "is_active": true, "natural_language": "Entity.Attribute: Employee.employmentStatus\nEnum Name: Employment Status\nValue: Terminated\nDisplay: Terminated\nDescription: Employee has been terminated", "version": 1, "status": "active", "created_at": "2025-06-23T06:53:47.066063", "updated_at": "2025-06-23T06:53:47.066063", "created_by": "Tarun", "updated_by": "Tarun", "enum_value_status": "new", "changes_detected": []}}], "operation": "parse_validate_mongosave", "total_enum_values": 3}, "status": "success"}
{"timestamp": "2025-06-23T06:54:14.210165", "endpoint": "parse-validate-mongosave/attribute-enum-values", "input": {"natural_language": "Tenant: Acme Corp Test\n\nEntity.Attribute | Enum Name | Value | Display | Description | Sort Order | Active\n\nEmployee.employeeId | Employee ID Status | Active | Active | Employee ID is active | 1 | true\nEmployee.employeeId | Employee ID Status | Inactive | Inactive | Employee ID is inactive | 2 | true", "tenant_id": "T1007", "edit_draft": false, "edit_temp": false, "edit_production": false}, "output": {"success": true, "enum_value_results": [{"success": false, "errors": ["Attribute with attribute_id A_E_Employee_1750661654_employeeId_1750661654 does not exist"], "parsed_data": {"enum_value_id": "EV_EMPLOYEE_EMPLOYEEID_ACTIVE", "attribute_id": "A_E_Employee_1750661654_employeeId_1750661654", "value": "Active", "display_name": "Active", "description": "Employee ID is active", "sort_order": 1, "is_active": true, "natural_language": "Entity.Attribute: Employee.employeeId\nEnum Name: Employee ID Status\nValue: Active\nDisplay: Active\nDescription: Employee ID is active", "version": 1, "status": "active", "created_at": "2025-06-23T06:54:14.136409", "updated_at": "2025-06-23T06:54:14.136409", "created_by": "Tarun", "updated_by": "Tarun", "enum_value_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Attribute with attribute_id A_E_Employee_1750661654_employeeId_1750661654 does not exist"], "parsed_data": {"enum_value_id": "EV_EMPLOYEE_EMPLOYEEID_INACTIVE", "attribute_id": "A_E_Employee_1750661654_employeeId_1750661654", "value": "Inactive", "display_name": "Inactive", "description": "Employee ID is inactive", "sort_order": 2, "is_active": true, "natural_language": "Entity.Attribute: Employee.employeeId\nEnum Name: Employee ID Status\nValue: Inactive\nDisplay: Inactive\nDescription: Employee ID is inactive", "version": 1, "status": "active", "created_at": "2025-06-23T06:54:14.162413", "updated_at": "2025-06-23T06:54:14.162413", "created_by": "Tarun", "updated_by": "Tarun", "enum_value_status": "new", "changes_detected": []}}], "operation": "parse_validate_mongosave", "total_enum_values": 2}, "status": "success"}
{"timestamp": "2025-06-23T06:59:40.257839", "endpoint": "parse-validate-mongosave/attribute-enum-values", "input": {"natural_language": "Tenant: Acme Corp Test\n\nEntity.Attribute | Enum Name | Value | Display | Description | Sort Order | Active\n\nEmployee.employmentStatus | Employment Status | Active | Active | Employee is currently active | 1 | true\nEmployee.employmentStatus | Employment Status | Inactive | Inactive | Employee is temporarily inactive | 2 | true", "tenant_id": "T1007", "edit_draft": false, "edit_temp": false, "edit_production": false}, "output": {"success": true, "enum_value_results": [{"success": false, "errors": ["Attribute with attribute_id A_E_Employee_1750661980_employmentStatus_1750661980 does not exist"], "parsed_data": {"enum_value_id": "EV_EMPLOYEE_EMPLOYMENTSTATUS_ACTIVE", "attribute_id": "A_E_Employee_1750661980_employmentStatus_1750661980", "value": "Active", "display_name": "Active", "description": "Employee is currently active", "sort_order": 1, "is_active": true, "natural_language": "Entity.Attribute: Employee.employmentStatus\nEnum Name: Employment Status\nValue: Active\nDisplay: Active\nDescription: Employee is currently active", "version": 1, "status": "active", "created_at": "2025-06-23T06:59:40.187747", "updated_at": "2025-06-23T06:59:40.187747", "created_by": "Tarun", "updated_by": "Tarun", "enum_value_status": "existing", "changes_detected": []}}, {"success": false, "errors": ["Attribute with attribute_id A_E_Employee_1750661980_employmentStatus_1750661980 does not exist"], "parsed_data": {"enum_value_id": "EV_EMPLOYEE_EMPLOYMENTSTATUS_INACTIVE", "attribute_id": "A_E_Employee_1750661980_employmentStatus_1750661980", "value": "Inactive", "display_name": "Inactive", "description": "Employee is temporarily inactive", "sort_order": 2, "is_active": true, "natural_language": "Entity.Attribute: Employee.employmentStatus\nEnum Name: Employment Status\nValue: Inactive\nDisplay: Inactive\nDescription: Employee is temporarily inactive", "version": 1, "status": "active", "created_at": "2025-06-23T06:59:40.216362", "updated_at": "2025-06-23T06:59:40.216362", "created_by": "Tarun", "updated_by": "Tarun", "enum_value_status": "new", "changes_detected": []}}], "operation": "parse_validate_mongosave", "total_enum_values": 2}, "status": "success"}
{"timestamp": "2025-06-23T07:06:28.177865", "endpoint": "parse-validate-mongosave/attribute-enum-values", "input": {"natural_language": "Tenant: Alpha Corp\n\nEntity.Attribute | Enum Name | Value | Display | Description | Sort Order | Active\n\nEmployee.employeeId | Employee ID Status | Active | Active | Employee ID is active | 1 | true\nEmployee.employeeId | Employee ID Status | Inactive | Inactive | Employee ID is inactive | 2 | true", "tenant_id": "T1001", "edit_draft": false, "edit_temp": false, "edit_production": false}, "output": {"success": true, "enum_value_results": [{"success": false, "errors": ["Attribute with attribute_id A_E13_employeeId_1750662388 does not exist"], "parsed_data": {"enum_value_id": "EV_EMPLOYEE_EMPLOYEEID_ACTIVE", "attribute_id": "A_E13_employeeId_1750662388", "value": "Active", "display_name": "Active", "description": "Employee ID is active", "sort_order": 1, "is_active": true, "natural_language": "Entity.Attribute: Employee.employeeId\nEnum Name: Employee ID Status\nValue: Active\nDisplay: Active\nDescription: Employee ID is active", "version": 1, "status": "active", "created_at": "2025-06-23T07:06:28.108706", "updated_at": "2025-06-23T07:06:28.108706", "created_by": "Tarun", "updated_by": "Tarun", "enum_value_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Attribute with attribute_id A_E13_employeeId_1750662388 does not exist"], "parsed_data": {"enum_value_id": "EV_EMPLOYEE_EMPLOYEEID_INACTIVE", "attribute_id": "A_E13_employeeId_1750662388", "value": "Inactive", "display_name": "Inactive", "description": "Employee ID is inactive", "sort_order": 2, "is_active": true, "natural_language": "Entity.Attribute: Employee.employeeId\nEnum Name: Employee ID Status\nValue: Inactive\nDisplay: Inactive\nDescription: Employee ID is inactive", "version": 1, "status": "active", "created_at": "2025-06-23T07:06:28.129938", "updated_at": "2025-06-23T07:06:28.129938", "created_by": "Tarun", "updated_by": "Tarun", "enum_value_status": "new", "changes_detected": []}}], "operation": "parse_validate_mongosave", "total_enum_values": 2}, "status": "success"}
{"timestamp": "2025-06-23T07:08:17.714106", "endpoint": "parse-validate-mongosave/attribute-enum-values", "input": {"natural_language": "Tenant: Alpha Corp\n\nEntity.Attribute | Enum Name | Value | Display | Description | Sort Order | Active\n\nEmployee.employeeId | Employee ID Status | Active | Active | Employee ID is active | 1 | true\nEmployee.employeeId | Employee ID Status | Inactive | Inactive | Employee ID is inactive | 2 | true", "tenant_id": "T1001", "edit_draft": false, "edit_temp": false, "edit_production": false}, "output": {"success": true, "enum_value_results": [{"success": false, "errors": ["Attribute with attribute_id A_E13_employeeId_1750662497 does not exist"], "parsed_data": {"enum_value_id": "EV_EMPLOYEE_EMPLOYEEID_ACTIVE", "attribute_id": "A_E13_employeeId_1750662497", "value": "Active", "display_name": "Active", "description": "Employee ID is active", "sort_order": 1, "is_active": true, "natural_language": "Entity.Attribute: Employee.employeeId\nEnum Name: Employee ID Status\nValue: Active\nDisplay: Active\nDescription: Employee ID is active", "version": 1, "status": "active", "created_at": "2025-06-23T07:08:17.649022", "updated_at": "2025-06-23T07:08:17.649022", "created_by": "Tarun", "updated_by": "Tarun", "enum_value_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Attribute with attribute_id A_E13_employeeId_1750662497 does not exist"], "parsed_data": {"enum_value_id": "EV_EMPLOYEE_EMPLOYEEID_INACTIVE", "attribute_id": "A_E13_employeeId_1750662497", "value": "Inactive", "display_name": "Inactive", "description": "Employee ID is inactive", "sort_order": 2, "is_active": true, "natural_language": "Entity.Attribute: Employee.employeeId\nEnum Name: Employee ID Status\nValue: Inactive\nDisplay: Inactive\nDescription: Employee ID is inactive", "version": 1, "status": "active", "created_at": "2025-06-23T07:08:17.669326", "updated_at": "2025-06-23T07:08:17.669326", "created_by": "Tarun", "updated_by": "Tarun", "enum_value_status": "new", "changes_detected": []}}], "operation": "parse_validate_mongosave", "total_enum_values": 2}, "status": "success"}
{"timestamp": "2025-06-23T07:09:31.435593", "endpoint": "parse-validate-mongosave/attribute-enum-values", "input": {"natural_language": "Tenant: Alpha Corp\n\nEntity.Attribute | Enum Name | Value | Display | Description | Sort Order | Active\n\nEmployee.totalLeaveEntitlement | Leave Entitlement Status | Active | Active | Leave entitlement is active | 1 | true\nEmployee.totalLeaveEntitlement | Leave Entitlement Status | Inactive | Inactive | Leave entitlement is inactive | 2 | true", "tenant_id": "T1001", "edit_draft": false, "edit_temp": false, "edit_production": false}, "output": {"success": true, "enum_value_results": [{"success": true, "saved_data": {"enum_value_id": "EV_EMPLOYEE_TOTALLEAVEENTITLEMENT_ACTIVE", "attribute_id": "E13.At11", "value": "Active", "display_name": "Active", "description": "Leave entitlement is active", "sort_order": 1, "is_active": true, "natural_language": "Entity.Attribute: Employee.totalLeaveEntitlement\nEnum Name: Leave Entitlement Status\nValue: Active\nDisplay: Active\nDescription: Leave entitlement is active", "version": 1, "status": "draft", "created_at": "2025-06-23T07:09:31.410029", "updated_at": "2025-06-23T07:09:31.410043", "created_by": "Tarun", "updated_by": "Tarun", "enum_value_status": "new", "changes_detected": [], "_id": "6858fdabd979f8cce2c8e458"}, "validation_result": {"structure_errors": [], "required_field_errors": [], "data_type_errors": [], "custom_errors": []}, "dependency_errors": [], "uniqueness_result": {"is_unique": true, "status": "unique", "message": "Enum value with enum_value_id EV_EMPLOYEE_TOTALLEAVEENTITLEMENT_ACTIVE is unique"}}, {"success": true, "saved_data": {"enum_value_id": "EV_EMPLOYEE_TOTALLEAVEENTITLEMENT_INACTIVE", "attribute_id": "E13.At11", "value": "Inactive", "display_name": "Inactive", "description": "Leave entitlement is inactive", "sort_order": 2, "is_active": true, "natural_language": "Entity.Attribute: Employee.totalLeaveEntitlement\nEnum Name: Leave Entitlement Status\nValue: Inactive\nDisplay: Inactive\nDescription: Leave entitlement is inactive", "version": 1, "status": "draft", "created_at": "2025-06-23T07:09:31.433776", "updated_at": "2025-06-23T07:09:31.433784", "created_by": "Tarun", "updated_by": "Tarun", "enum_value_status": "new", "changes_detected": [], "_id": "6858fdabd979f8cce2c8e459"}, "validation_result": {"structure_errors": [], "required_field_errors": [], "data_type_errors": [], "custom_errors": []}, "dependency_errors": [], "uniqueness_result": {"is_unique": true, "status": "unique", "message": "Enum value with enum_value_id EV_EMPLOYEE_TOTALLEAVEENTITLEMENT_INACTIVE is unique"}}], "operation": "parse_validate_mongosave", "total_enum_values": 2}, "status": "success"}
{"timestamp": "2025-06-23T08:25:55.905527", "endpoint": "parse-validate-mongosave/attribute-enum-values", "input": {"natural_language": "Tenant: Acme Corp\n\nEntity.Attribute | Enum Name | Value | Display | Description | Sort Order | Active\n\nLeaveApplication.status | Leave Application Status | Pending | Pending | Leave request is pending approval | 1 | true\n\nLeaveApplication.status | Leave Application Status | Approved | Approved | Leave request has been approved | 2 | true\n\nLeaveApplication.status | Leave Application Status | Rejected | Rejected | Leave request has been rejected | 3 | true\n\nLeaveApplication.status | Leave Application Status | Cancelled | Cancelled | Leave request has been cancelled | 4 | true\n\nLeaveApplication.requiresDocumentation | Documentation Requirement | true | Yes | Documentation is required | 1 | true\n\nLeaveApplication.requiresDocumentation | Documentation Requirement | false | No | Documentation is not required | 2 | true\n\nEmployee.employmentStatus | Employment Status | Active | Active | Employee is currently active | 1 | true\n\nEmployee.employmentStatus | Employment Status | Inactive | Inactive | Employee is temporarily inactive | 2 | true\n\nEmployee.employmentStatus | Employment Status | Terminated | Terminated | Employee has been terminated | 3 | true", "tenant_id": "T_ACME_CORP", "edit_draft": false, "edit_temp": false, "edit_production": false}, "output": {"success": true, "enum_value_results": [{"success": false, "errors": ["Attribute with attribute_id A_E_LeaveApplication_1750667155_status_1750667155 does not exist"], "parsed_data": {"enum_value_id": "EV_LEAVEAPPLICATION_STATUS_PENDING", "attribute_id": "A_E_LeaveApplication_1750667155_status_1750667155", "value": "Pending", "display_name": "Pending", "description": "Leave request is pending approval", "sort_order": 1, "is_active": true, "natural_language": "Entity.Attribute: LeaveApplication.status\nEnum Name: Leave Application Status\nValue: Pending\nDisplay: Pending\nDescription: Leave request is pending approval", "version": 1, "status": "active", "created_at": "2025-06-23T08:25:55.541565", "updated_at": "2025-06-23T08:25:55.541565", "created_by": "Tarun", "updated_by": "Tarun", "enum_value_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Attribute with attribute_id A_E_LeaveApplication_1750667155_status_1750667155 does not exist"], "parsed_data": {"enum_value_id": "EV_LEAVEAPPLICATION_STATUS_APPROVED", "attribute_id": "A_E_LeaveApplication_1750667155_status_1750667155", "value": "Approved", "display_name": "Approved", "description": "Leave request has been approved", "sort_order": 2, "is_active": true, "natural_language": "Entity.Attribute: LeaveApplication.status\nEnum Name: Leave Application Status\nValue: Approved\nDisplay: Approved\nDescription: Leave request has been approved", "version": 1, "status": "active", "created_at": "2025-06-23T08:25:55.566939", "updated_at": "2025-06-23T08:25:55.566939", "created_by": "Tarun", "updated_by": "Tarun", "enum_value_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Attribute with attribute_id A_E_LeaveApplication_1750667155_status_1750667155 does not exist"], "parsed_data": {"enum_value_id": "EV_LEAVEAPPLICATION_STATUS_REJECTED", "attribute_id": "A_E_LeaveApplication_1750667155_status_1750667155", "value": "Rejected", "display_name": "Rejected", "description": "Leave request has been rejected", "sort_order": 3, "is_active": true, "natural_language": "Entity.Attribute: LeaveApplication.status\nEnum Name: Leave Application Status\nValue: Rejected\nDisplay: Rejected\nDescription: Leave request has been rejected", "version": 1, "status": "active", "created_at": "2025-06-23T08:25:55.589036", "updated_at": "2025-06-23T08:25:55.589036", "created_by": "Tarun", "updated_by": "Tarun", "enum_value_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Attribute with attribute_id A_E_LeaveApplication_1750667155_status_1750667155 does not exist"], "parsed_data": {"enum_value_id": "EV_LEAVEAPPLICATION_STATUS_CANCELLED", "attribute_id": "A_E_LeaveApplication_1750667155_status_1750667155", "value": "Cancelled", "display_name": "Cancelled", "description": "Leave request has been cancelled", "sort_order": 4, "is_active": true, "natural_language": "Entity.Attribute: LeaveApplication.status\nEnum Name: Leave Application Status\nValue: Cancelled\nDisplay: Cancelled\nDescription: Leave request has been cancelled", "version": 1, "status": "active", "created_at": "2025-06-23T08:25:55.613937", "updated_at": "2025-06-23T08:25:55.613937", "created_by": "Tarun", "updated_by": "Tarun", "enum_value_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Attribute with attribute_id A_E_LeaveApplication_1750667155_requiresDocumentation_1750667155 does not exist"], "parsed_data": {"enum_value_id": "EV_LEAVEAPPLICATION_REQUIRESDOCUMENTATION_TRUE", "attribute_id": "A_E_LeaveApplication_1750667155_requiresDocumentation_1750667155", "value": "true", "display_name": "Yes", "description": "Documentation is required", "sort_order": 1, "is_active": true, "natural_language": "Entity.Attribute: LeaveApplication.requiresDocumentation\nEnum Name: Documentation Requirement\nValue: true\nDisplay: Yes\nDescription: Documentation is required", "version": 1, "status": "active", "created_at": "2025-06-23T08:25:55.635161", "updated_at": "2025-06-23T08:25:55.635161", "created_by": "Tarun", "updated_by": "Tarun", "enum_value_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Attribute with attribute_id A_E_LeaveApplication_1750667155_requiresDocumentation_1750667155 does not exist"], "parsed_data": {"enum_value_id": "EV_LEAVEAPPLICATION_REQUIRESDOCUMENTATION_FALSE", "attribute_id": "A_E_LeaveApplication_1750667155_requiresDocumentation_1750667155", "value": "false", "display_name": "No", "description": "Documentation is not required", "sort_order": 2, "is_active": true, "natural_language": "Entity.Attribute: LeaveApplication.requiresDocumentation\nEnum Name: Documentation Requirement\nValue: false\nDisplay: No\nDescription: Documentation is not required", "version": 1, "status": "active", "created_at": "2025-06-23T08:25:55.659789", "updated_at": "2025-06-23T08:25:55.659789", "created_by": "Tarun", "updated_by": "Tarun", "enum_value_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Attribute with attribute_id A_E_Employee_1750667155_employmentStatus_1750667155 does not exist"], "parsed_data": {"enum_value_id": "EV_EMPLOYEE_EMPLOYMENTSTATUS_ACTIVE", "attribute_id": "A_E_Employee_1750667155_employmentStatus_1750667155", "value": "Active", "display_name": "Active", "description": "Employee is currently active", "sort_order": 1, "is_active": true, "natural_language": "Entity.Attribute: Employee.employmentStatus\nEnum Name: Employment Status\nValue: Active\nDisplay: Active\nDescription: Employee is currently active", "version": 1, "status": "active", "created_at": "2025-06-23T08:25:55.680774", "updated_at": "2025-06-23T08:25:55.680774", "created_by": "Tarun", "updated_by": "Tarun", "enum_value_status": "existing", "changes_detected": []}}, {"success": false, "errors": ["Attribute with attribute_id A_E_Employee_1750667155_employmentStatus_1750667155 does not exist"], "parsed_data": {"enum_value_id": "EV_EMPLOYEE_EMPLOYMENTSTATUS_INACTIVE", "attribute_id": "A_E_Employee_1750667155_employmentStatus_1750667155", "value": "Inactive", "display_name": "Inactive", "description": "Employee is temporarily inactive", "sort_order": 2, "is_active": true, "natural_language": "Entity.Attribute: Employee.employmentStatus\nEnum Name: Employment Status\nValue: Inactive\nDisplay: Inactive\nDescription: Employee is temporarily inactive", "version": 1, "status": "active", "created_at": "2025-06-23T08:25:55.703543", "updated_at": "2025-06-23T08:25:55.703543", "created_by": "Tarun", "updated_by": "Tarun", "enum_value_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Attribute with attribute_id A_E_Employee_1750667155_employmentStatus_1750667155 does not exist"], "parsed_data": {"enum_value_id": "EV_EMPLOYEE_EMPLOYMENTSTATUS_TERMINATED", "attribute_id": "A_E_Employee_1750667155_employmentStatus_1750667155", "value": "Terminated", "display_name": "Terminated", "description": "Employee has been terminated", "sort_order": 3, "is_active": true, "natural_language": "Entity.Attribute: Employee.employmentStatus\nEnum Name: Employment Status\nValue: Terminated\nDisplay: Terminated\nDescription: Employee has been terminated", "version": 1, "status": "active", "created_at": "2025-06-23T08:25:55.724099", "updated_at": "2025-06-23T08:25:55.724099", "created_by": "Tarun", "updated_by": "Tarun", "enum_value_status": "new", "changes_detected": []}}], "operation": "parse_validate_mongosave", "total_enum_values": 9}, "status": "success"}
{"timestamp": "2025-06-23T08:29:04.171857", "endpoint": "parse-validate-mongosave/attribute-enum-values", "input": {"natural_language": "Tenant: Acme Corp\n\nEntity.Attribute | Enum Name | Value | Display | Description | Sort Order | Active\n\nLeaveApplication.status | Leave Application Status | Pending | Pending | Leave request is pending approval | 1 | true\n\nLeaveApplication.status | Leave Application Status | Approved | Approved | Leave request has been approved | 2 | true\n\nLeaveApplication.status | Leave Application Status | Rejected | Rejected | Leave request has been rejected | 3 | true\n\nLeaveApplication.status | Leave Application Status | Cancelled | Cancelled | Leave request has been cancelled | 4 | true\n\nLeaveApplication.requiresDocumentation | Documentation Requirement | true | Yes | Documentation is required | 1 | true\n\nLeaveApplication.requiresDocumentation | Documentation Requirement | false | No | Documentation is not required | 2 | true\n\nEmployee.employmentStatus | Employment Status | Active | Active | Employee is currently active | 1 | true\n\nEmployee.employmentStatus | Employment Status | Inactive | Inactive | Employee is temporarily inactive | 2 | true\n\nEmployee.employmentStatus | Employment Status | Terminated | Terminated | Employee has been terminated | 3 | true", "tenant_id": "T_ACME_CORP", "edit_draft": false, "edit_temp": false, "edit_production": false}, "output": {"success": true, "enum_value_results": [{"success": false, "errors": ["Attribute with attribute_id A_E_LeaveApplication_1750667343_status_1750667343 does not exist"], "parsed_data": {"enum_value_id": "EV_LEAVEAPPLICATION_STATUS_PENDING", "attribute_id": "A_E_LeaveApplication_1750667343_status_1750667343", "value": "Pending", "display_name": "Pending", "description": "Leave request is pending approval", "sort_order": 1, "is_active": true, "natural_language": "Entity.Attribute: LeaveApplication.status\nEnum Name: Leave Application Status\nValue: Pending\nDisplay: Pending\nDescription: Leave request is pending approval", "version": 1, "status": "active", "created_at": "2025-06-23T08:29:03.780296", "updated_at": "2025-06-23T08:29:03.780296", "created_by": "Tarun", "updated_by": "Tarun", "enum_value_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Attribute with attribute_id A_E_LeaveApplication_1750667343_status_1750667343 does not exist"], "parsed_data": {"enum_value_id": "EV_LEAVEAPPLICATION_STATUS_APPROVED", "attribute_id": "A_E_LeaveApplication_1750667343_status_1750667343", "value": "Approved", "display_name": "Approved", "description": "Leave request has been approved", "sort_order": 2, "is_active": true, "natural_language": "Entity.Attribute: LeaveApplication.status\nEnum Name: Leave Application Status\nValue: Approved\nDisplay: Approved\nDescription: Leave request has been approved", "version": 1, "status": "active", "created_at": "2025-06-23T08:29:03.807893", "updated_at": "2025-06-23T08:29:03.807893", "created_by": "Tarun", "updated_by": "Tarun", "enum_value_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Attribute with attribute_id A_E_LeaveApplication_1750667343_status_1750667343 does not exist"], "parsed_data": {"enum_value_id": "EV_LEAVEAPPLICATION_STATUS_REJECTED", "attribute_id": "A_E_LeaveApplication_1750667343_status_1750667343", "value": "Rejected", "display_name": "Rejected", "description": "Leave request has been rejected", "sort_order": 3, "is_active": true, "natural_language": "Entity.Attribute: LeaveApplication.status\nEnum Name: Leave Application Status\nValue: Rejected\nDisplay: Rejected\nDescription: Leave request has been rejected", "version": 1, "status": "active", "created_at": "2025-06-23T08:29:03.834741", "updated_at": "2025-06-23T08:29:03.834741", "created_by": "Tarun", "updated_by": "Tarun", "enum_value_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Attribute with attribute_id A_E_LeaveApplication_1750667343_status_1750667343 does not exist"], "parsed_data": {"enum_value_id": "EV_LEAVEAPPLICATION_STATUS_CANCELLED", "attribute_id": "A_E_LeaveApplication_1750667343_status_1750667343", "value": "Cancelled", "display_name": "Cancelled", "description": "Leave request has been cancelled", "sort_order": 4, "is_active": true, "natural_language": "Entity.Attribute: LeaveApplication.status\nEnum Name: Leave Application Status\nValue: Cancelled\nDisplay: Cancelled\nDescription: Leave request has been cancelled", "version": 1, "status": "active", "created_at": "2025-06-23T08:29:03.861279", "updated_at": "2025-06-23T08:29:03.861279", "created_by": "Tarun", "updated_by": "Tarun", "enum_value_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Attribute with attribute_id A_E_LeaveApplication_1750667343_requiresDocumentation_1750667343 does not exist"], "parsed_data": {"enum_value_id": "EV_LEAVEAPPLICATION_REQUIRESDOCUMENTATION_TRUE", "attribute_id": "A_E_LeaveApplication_1750667343_requiresDocumentation_1750667343", "value": "true", "display_name": "Yes", "description": "Documentation is required", "sort_order": 1, "is_active": true, "natural_language": "Entity.Attribute: LeaveApplication.requiresDocumentation\nEnum Name: Documentation Requirement\nValue: true\nDisplay: Yes\nDescription: Documentation is required", "version": 1, "status": "active", "created_at": "2025-06-23T08:29:03.889894", "updated_at": "2025-06-23T08:29:03.889894", "created_by": "Tarun", "updated_by": "Tarun", "enum_value_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Attribute with attribute_id A_E_LeaveApplication_1750667343_requiresDocumentation_1750667343 does not exist"], "parsed_data": {"enum_value_id": "EV_LEAVEAPPLICATION_REQUIRESDOCUMENTATION_FALSE", "attribute_id": "A_E_LeaveApplication_1750667343_requiresDocumentation_1750667343", "value": "false", "display_name": "No", "description": "Documentation is not required", "sort_order": 2, "is_active": true, "natural_language": "Entity.Attribute: LeaveApplication.requiresDocumentation\nEnum Name: Documentation Requirement\nValue: false\nDisplay: No\nDescription: Documentation is not required", "version": 1, "status": "active", "created_at": "2025-06-23T08:29:03.917080", "updated_at": "2025-06-23T08:29:03.917080", "created_by": "Tarun", "updated_by": "Tarun", "enum_value_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Attribute with attribute_id A_E_Employee_1750667343_employmentStatus_1750667343 does not exist"], "parsed_data": {"enum_value_id": "EV_EMPLOYEE_EMPLOYMENTSTATUS_ACTIVE", "attribute_id": "A_E_Employee_1750667343_employmentStatus_1750667343", "value": "Active", "display_name": "Active", "description": "Employee is currently active", "sort_order": 1, "is_active": true, "natural_language": "Entity.Attribute: Employee.employmentStatus\nEnum Name: Employment Status\nValue: Active\nDisplay: Active\nDescription: Employee is currently active", "version": 1, "status": "active", "created_at": "2025-06-23T08:29:03.943979", "updated_at": "2025-06-23T08:29:03.943979", "created_by": "Tarun", "updated_by": "Tarun", "enum_value_status": "existing", "changes_detected": []}}, {"success": false, "errors": ["Attribute with attribute_id A_E_Employee_1750667343_employmentStatus_1750667343 does not exist"], "parsed_data": {"enum_value_id": "EV_EMPLOYEE_EMPLOYMENTSTATUS_INACTIVE", "attribute_id": "A_E_Employee_1750667343_employmentStatus_1750667343", "value": "Inactive", "display_name": "Inactive", "description": "Employee is temporarily inactive", "sort_order": 2, "is_active": true, "natural_language": "Entity.Attribute: Employee.employmentStatus\nEnum Name: Employment Status\nValue: Inactive\nDisplay: Inactive\nDescription: Employee is temporarily inactive", "version": 1, "status": "active", "created_at": "2025-06-23T08:29:03.968877", "updated_at": "2025-06-23T08:29:03.968877", "created_by": "Tarun", "updated_by": "Tarun", "enum_value_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Attribute with attribute_id A_E_Employee_1750667343_employmentStatus_1750667343 does not exist"], "parsed_data": {"enum_value_id": "EV_EMPLOYEE_EMPLOYMENTSTATUS_TERMINATED", "attribute_id": "A_E_Employee_1750667343_employmentStatus_1750667343", "value": "Terminated", "display_name": "Terminated", "description": "Employee has been terminated", "sort_order": 3, "is_active": true, "natural_language": "Entity.Attribute: Employee.employmentStatus\nEnum Name: Employment Status\nValue: Terminated\nDisplay: Terminated\nDescription: Employee has been terminated", "version": 1, "status": "active", "created_at": "2025-06-23T08:29:03.994033", "updated_at": "2025-06-23T08:29:03.994033", "created_by": "Tarun", "updated_by": "Tarun", "enum_value_status": "new", "changes_detected": []}}], "operation": "parse_validate_mongosave", "total_enum_values": 9}, "status": "success"}
{"timestamp": "2025-06-23T09:04:02.951753", "endpoint": "parse-validate-mongosave/attribute-enum-values", "input": {"natural_language": "Tenant: Acme Corp\n\nEntity.Attribute | Enum Name | Value | Display | Description | Sort Order | Active\n\nLeaveApplication.status | Leave Application Status | Pending | Pending | Leave request is pending approval | 1 | true\nLeaveApplication.status | Leave Application Status | Approved | Approved | Leave request has been approved | 2 | true\nLeaveApplication.status | Leave Application Status | Rejected | Rejected | Leave request has been rejected | 3 | true\nLeaveApplication.status | Leave Application Status | Cancelled | Cancelled | Leave request has been cancelled | 4 | true\nLeaveApplication.requiresDocumentation | Documentation Requirement | true | Yes | Documentation is required | 1 | true\nLeaveApplication.requiresDocumentation | Documentation Requirement | false | No | Documentation is not required | 2 | true\nLeaveApplication.documentationProvided | Documentation Status | true | Provided | Documentation has been provided | 1 | true\nLeaveApplication.documentationProvided | Documentation Status | false | Not Provided | Documentation has not been provided | 2 | true\nEmployee.employmentStatus | Employment Status | Active | Active | Employee is currently active | 1 | true\nEmployee.employmentStatus | Employment Status | Inactive | Inactive | Employee is temporarily inactive | 2 | true\nEmployee.employmentStatus | Employment Status | Terminated | Terminated | Employee has been terminated | 3 | true\nLeaveType.requiresDocumentation | Documentation Requirement | true | Yes | This leave type requires documentation | 1 | true\nLeaveType.requiresDocumentation | Documentation Requirement | false | No | This leave type does not require documentation | 2 | true\nLeaveType.isCarryForward | Carry Forward Policy | true | Allowed | Unused days can be carried forward | 1 | true\nLeaveType.isCarryForward | Carry Forward Policy | false | Not Allowed | Unused days cannot be carried forward | 2 | true\nLeaveType.status | Leave Type Status | Active | Active | Leave type is currently active | 1 | true\nLeaveType.status | Leave Type Status | Inactive | Inactive | Leave type is inactive | 2 | true\nLeaveSubType.requiresManagerApproval | Manager Approval Requirement | true | Required | Manager approval is required | 1 | true\nLeaveSubType.requiresManagerApproval | Manager Approval Requirement | false | Not Required | Manager approval is not required | 2 | true\nLeaveSubType.status | Leave Sub-Type Status | Active | Active | Leave sub-type is currently active | 1 | true\nLeaveSubType.status | Leave Sub-Type Status | Inactive | Inactive | Leave sub-type is inactive | 2 | true\nConstants.dataType | Data Type | String | String | Text data type | 1 | true\nConstants.dataType | Data Type | Integer | Integer | Whole number data type | 2 | true\nConstants.dataType | Data Type | Decimal | Decimal | Decimal number data type | 3 | true\nConstants.dataType | Data Type | Boolean | Boolean | True/False data type | 4 | true\nConstants.dataType | Data Type | Date | Date | Date data type | 5 | true\nConstants.status | Constant Status | Active | Active | Constant is currently active | 1 | true\nConstants.status | Constant Status | Inactive | Inactive | Constant is inactive | 2 | true\nCommon.status | General Status | Active | Active | Item is currently active | 1 | true\nCommon.status | General Status | Inactive | Inactive | Item is inactive | 2 | true\nCommon.status | General Status | Draft | Draft | Item is in draft state | 3 | true\nCommon.status | General Status | Archived | Archived | Item has been archived | 4 | true\nCommon.yesNo | Yes/No | true | Yes | Affirmative response | 1 | true\nCommon.yesNo | Yes/No | false | No | Negative response | 2 | true\nLeaveType.leaveTypeName | Annual Leave Types | Annual Leave | Annual Leave | Standard annual vacation leave | 1 | true\nLeaveType.leaveTypeName | Annual Leave Types | Sick Leave | Sick Leave | Medical leave for illness | 2 | true\nLeaveType.leaveTypeName | Annual Leave Types | Personal Leave | Personal Leave | Personal time off | 3 | true\nLeaveType.leaveTypeName | Annual Leave Types | Maternity Leave | Maternity Leave | Leave for new mothers | 4 | true\nLeaveType.leaveTypeName | Annual Leave Types | Paternity Leave | Paternity Leave | Leave for new fathers | 5 | true\nLeaveType.leaveTypeName | Annual Leave Types | Bereavement Leave | Bereavement Leave | Leave for family bereavement | 6 | true\nLeaveType.leaveTypeName | Annual Leave Types | Study Leave | Study Leave | Leave for educational purposes | 7 | true\nLeaveType.leaveTypeName | Annual Leave Types | Emergency Leave | Emergency Leave | Leave for emergency situations | 8 | true\nLeaveSubType.leaveSubTypeName | Sick Leave Sub-Types | Short Term Illness | Short Term Illness | Illness lasting less than 5 days | 1 | true\nLeaveSubType.leaveSubTypeName | Sick Leave Sub-Types | Long Term Illness | Long Term Illness | Illness lasting more than 5 days | 2 | true\nLeaveSubType.leaveSubTypeName | Sick Leave Sub-Types | Medical Appointment | Medical Appointment | Time off for medical appointments | 3 | true\nLeaveSubType.leaveSubTypeName | Sick Leave Sub-Types | Mental Health | Mental Health | Mental health related leave | 4 | true\nLeaveSubType.leaveSubTypeName | Personal Leave Sub-Types | Family Event | Family Event | Family celebrations or events | 1 | true\nLeaveSubType.leaveSubTypeName | Personal Leave Sub-Types | Personal Business | Personal Business | Personal business matters | 2 | true\nLeaveSubType.leaveSubTypeName | Personal Leave Sub-Types | Religious Observance | Religious Observance | Religious holidays or observances | 3 | true\nLeaveSubType.leaveSubTypeName | Personal Leave Sub-Types | Moving/Relocation | Moving/Relocation | Time off for moving or relocation | 4 | true", "tenant_id": "T1001", "edit_draft": true, "edit_temp": false, "edit_production": false}, "output": {"success": true, "enum_value_results": [{"success": false, "errors": ["Attribute with attribute_id A_E_LeaveApplication_1750669440_status_1750669440 does not exist"], "parsed_data": {"enum_value_id": "EV_LEAVEAPPLICATION_STATUS_PENDING", "attribute_id": "A_E_LeaveApplication_1750669440_status_1750669440", "value": "Pending", "display_name": "Pending", "description": "Leave request is pending approval", "sort_order": 1, "is_active": true, "natural_language": "Entity.Attribute: LeaveApplication.status\nEnum Name: Leave Application Status\nValue: Pending\nDisplay: Pending\nDescription: Leave request is pending approval", "version": 1, "status": "active", "created_at": "2025-06-23T09:04:00.868820", "updated_at": "2025-06-23T09:04:00.868820", "created_by": "Tarun", "updated_by": "Tarun", "enum_value_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Attribute with attribute_id A_E_LeaveApplication_1750669440_status_1750669440 does not exist"], "parsed_data": {"enum_value_id": "EV_LEAVEAPPLICATION_STATUS_APPROVED", "attribute_id": "A_E_LeaveApplication_1750669440_status_1750669440", "value": "Approved", "display_name": "Approved", "description": "Leave request has been approved", "sort_order": 2, "is_active": true, "natural_language": "Entity.Attribute: LeaveApplication.status\nEnum Name: Leave Application Status\nValue: Approved\nDisplay: Approved\nDescription: Leave request has been approved", "version": 1, "status": "active", "created_at": "2025-06-23T09:04:00.895700", "updated_at": "2025-06-23T09:04:00.895700", "created_by": "Tarun", "updated_by": "Tarun", "enum_value_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Attribute with attribute_id A_E_LeaveApplication_1750669440_status_1750669440 does not exist"], "parsed_data": {"enum_value_id": "EV_LEAVEAPPLICATION_STATUS_REJECTED", "attribute_id": "A_E_LeaveApplication_1750669440_status_1750669440", "value": "Rejected", "display_name": "Rejected", "description": "Leave request has been rejected", "sort_order": 3, "is_active": true, "natural_language": "Entity.Attribute: LeaveApplication.status\nEnum Name: Leave Application Status\nValue: Rejected\nDisplay: Rejected\nDescription: Leave request has been rejected", "version": 1, "status": "active", "created_at": "2025-06-23T09:04:00.920384", "updated_at": "2025-06-23T09:04:00.920384", "created_by": "Tarun", "updated_by": "Tarun", "enum_value_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Attribute with attribute_id A_E_LeaveApplication_1750669440_status_1750669440 does not exist"], "parsed_data": {"enum_value_id": "EV_LEAVEAPPLICATION_STATUS_CANCELLED", "attribute_id": "A_E_LeaveApplication_1750669440_status_1750669440", "value": "Cancelled", "display_name": "Cancelled", "description": "Leave request has been cancelled", "sort_order": 4, "is_active": true, "natural_language": "Entity.Attribute: LeaveApplication.status\nEnum Name: Leave Application Status\nValue: Cancelled\nDisplay: Cancelled\nDescription: Leave request has been cancelled", "version": 1, "status": "active", "created_at": "2025-06-23T09:04:00.953594", "updated_at": "2025-06-23T09:04:00.953594", "created_by": "Tarun", "updated_by": "Tarun", "enum_value_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Attribute with attribute_id A_E_LeaveApplication_1750669440_requiresDocumentation_1750669440 does not exist"], "parsed_data": {"enum_value_id": "EV_LEAVEAPPLICATION_REQUIRESDOCUMENTATION_TRUE", "attribute_id": "A_E_LeaveApplication_1750669440_requiresDocumentation_1750669440", "value": "true", "display_name": "Yes", "description": "Documentation is required", "sort_order": 1, "is_active": true, "natural_language": "Entity.Attribute: LeaveApplication.requiresDocumentation\nEnum Name: Documentation Requirement\nValue: true\nDisplay: Yes\nDescription: Documentation is required", "version": 1, "status": "active", "created_at": "2025-06-23T09:04:00.980928", "updated_at": "2025-06-23T09:04:00.980928", "created_by": "Tarun", "updated_by": "Tarun", "enum_value_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Attribute with attribute_id A_E_LeaveApplication_1750669440_requiresDocumentation_1750669441 does not exist"], "parsed_data": {"enum_value_id": "EV_LEAVEAPPLICATION_REQUIRESDOCUMENTATION_FALSE", "attribute_id": "A_E_LeaveApplication_1750669440_requiresDocumentation_1750669441", "value": "false", "display_name": "No", "description": "Documentation is not required", "sort_order": 2, "is_active": true, "natural_language": "Entity.Attribute: LeaveApplication.requiresDocumentation\nEnum Name: Documentation Requirement\nValue: false\nDisplay: No\nDescription: Documentation is not required", "version": 1, "status": "active", "created_at": "2025-06-23T09:04:01.009184", "updated_at": "2025-06-23T09:04:01.009184", "created_by": "Tarun", "updated_by": "Tarun", "enum_value_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Attribute with attribute_id A_E_LeaveApplication_1750669441_documentationProvided_1750669441 does not exist"], "parsed_data": {"enum_value_id": "EV_LEAVEAPPLICATION_DOCUMENTATIONPROVIDED_TRUE", "attribute_id": "A_E_LeaveApplication_1750669441_documentationProvided_1750669441", "value": "true", "display_name": "Provided", "description": "Documentation has been provided", "sort_order": 1, "is_active": true, "natural_language": "Entity.Attribute: LeaveApplication.documentationProvided\nEnum Name: Documentation Status\nValue: true\nDisplay: Provided\nDescription: Documentation has been provided", "version": 1, "status": "active", "created_at": "2025-06-23T09:04:01.035728", "updated_at": "2025-06-23T09:04:01.035728", "created_by": "Tarun", "updated_by": "Tarun", "enum_value_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Attribute with attribute_id A_E_LeaveApplication_1750669441_documentationProvided_1750669441 does not exist"], "parsed_data": {"enum_value_id": "EV_LEAVEAPPLICATION_DOCUMENTATIONPROVIDED_FALSE", "attribute_id": "A_E_LeaveApplication_1750669441_documentationProvided_1750669441", "value": "false", "display_name": "Not Provided", "description": "Documentation has not been provided", "sort_order": 2, "is_active": true, "natural_language": "Entity.Attribute: LeaveApplication.documentationProvided\nEnum Name: Documentation Status\nValue: false\nDisplay: Not Provided\nDescription: Documentation has not been provided", "version": 1, "status": "active", "created_at": "2025-06-23T09:04:01.056515", "updated_at": "2025-06-23T09:04:01.056515", "created_by": "Tarun", "updated_by": "Tarun", "enum_value_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Attribute with attribute_id A_E13_employmentStatus_1750669441 does not exist"], "parsed_data": {"enum_value_id": "EV_EMPLOYEE_EMPLOYMENTSTATUS_ACTIVE", "attribute_id": "A_E13_employmentStatus_1750669441", "value": "Active", "display_name": "Active", "description": "Employee is currently active", "sort_order": 1, "is_active": true, "natural_language": "Entity.Attribute: Employee.employmentStatus\nEnum Name: Employment Status\nValue: Active\nDisplay: Active\nDescription: Employee is currently active", "version": 1, "status": "active", "created_at": "2025-06-23T09:04:01.074792", "updated_at": "2025-06-23T09:04:01.074792", "created_by": "Tarun", "updated_by": "Tarun", "enum_value_status": "existing", "changes_detected": []}}, {"success": false, "errors": ["Attribute with attribute_id A_E13_employmentStatus_1750669441 does not exist"], "parsed_data": {"enum_value_id": "EV_EMPLOYEE_EMPLOYMENTSTATUS_INACTIVE", "attribute_id": "A_E13_employmentStatus_1750669441", "value": "Inactive", "display_name": "Inactive", "description": "Employee is temporarily inactive", "sort_order": 2, "is_active": true, "natural_language": "Entity.Attribute: Employee.employmentStatus\nEnum Name: Employment Status\nValue: Inactive\nDisplay: Inactive\nDescription: Employee is temporarily inactive", "version": 1, "status": "active", "created_at": "2025-06-23T09:04:01.093011", "updated_at": "2025-06-23T09:04:01.093011", "created_by": "Tarun", "updated_by": "Tarun", "enum_value_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Attribute with attribute_id A_E13_employmentStatus_1750669441 does not exist"], "parsed_data": {"enum_value_id": "EV_EMPLOYEE_EMPLOYMENTSTATUS_TERMINATED", "attribute_id": "A_E13_employmentStatus_1750669441", "value": "Terminated", "display_name": "Terminated", "description": "Employee has been terminated", "sort_order": 3, "is_active": true, "natural_language": "Entity.Attribute: Employee.employmentStatus\nEnum Name: Employment Status\nValue: Terminated\nDisplay: Terminated\nDescription: Employee has been terminated", "version": 1, "status": "active", "created_at": "2025-06-23T09:04:01.111625", "updated_at": "2025-06-23T09:04:01.111625", "created_by": "Tarun", "updated_by": "Tarun", "enum_value_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Attribute with attribute_id A_E_LeaveType_1750669441_requiresDocumentation_1750669441 does not exist"], "parsed_data": {"enum_value_id": "EV_LEAVETYPE_REQUIRESDOCUMENTATION_TRUE", "attribute_id": "A_E_LeaveType_1750669441_requiresDocumentation_1750669441", "value": "true", "display_name": "Yes", "description": "This leave type requires documentation", "sort_order": 1, "is_active": true, "natural_language": "Entity.Attribute: LeaveType.requiresDocumentation\nEnum Name: Documentation Requirement\nValue: true\nDisplay: Yes\nDescription: This leave type requires documentation", "version": 1, "status": "active", "created_at": "2025-06-23T09:04:01.137383", "updated_at": "2025-06-23T09:04:01.137383", "created_by": "Tarun", "updated_by": "Tarun", "enum_value_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Attribute with attribute_id A_E_LeaveType_1750669441_requiresDocumentation_1750669441 does not exist"], "parsed_data": {"enum_value_id": "EV_LEAVETYPE_REQUIRESDOCUMENTATION_FALSE", "attribute_id": "A_E_LeaveType_1750669441_requiresDocumentation_1750669441", "value": "false", "display_name": "No", "description": "This leave type does not require documentation", "sort_order": 2, "is_active": true, "natural_language": "Entity.Attribute: LeaveType.requiresDocumentation\nEnum Name: Documentation Requirement\nValue: false\nDisplay: No\nDescription: This leave type does not require documentation", "version": 1, "status": "active", "created_at": "2025-06-23T09:04:01.159284", "updated_at": "2025-06-23T09:04:01.159284", "created_by": "Tarun", "updated_by": "Tarun", "enum_value_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Attribute with attribute_id A_E_LeaveType_1750669441_isCarryForward_1750669441 does not exist"], "parsed_data": {"enum_value_id": "EV_LEAVETYPE_ISCARRYFORWARD_TRUE", "attribute_id": "A_E_LeaveType_1750669441_isCarryForward_1750669441", "value": "true", "display_name": "Allowed", "description": "Unused days can be carried forward", "sort_order": 1, "is_active": true, "natural_language": "Entity.Attribute: LeaveType.isCarryForward\nEnum Name: Carry Forward Policy\nValue: true\nDisplay: Allowed\nDescription: Unused days can be carried forward", "version": 1, "status": "active", "created_at": "2025-06-23T09:04:01.184029", "updated_at": "2025-06-23T09:04:01.184029", "created_by": "Tarun", "updated_by": "Tarun", "enum_value_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Attribute with attribute_id A_E_LeaveType_1750669441_isCarryForward_1750669441 does not exist"], "parsed_data": {"enum_value_id": "EV_LEAVETYPE_ISCARRYFORWARD_FALSE", "attribute_id": "A_E_LeaveType_1750669441_isCarryForward_1750669441", "value": "false", "display_name": "Not Allowed", "description": "Unused days cannot be carried forward", "sort_order": 2, "is_active": true, "natural_language": "Entity.Attribute: LeaveType.isCarryForward\nEnum Name: Carry Forward Policy\nValue: false\nDisplay: Not Allowed\nDescription: Unused days cannot be carried forward", "version": 1, "status": "active", "created_at": "2025-06-23T09:04:01.207285", "updated_at": "2025-06-23T09:04:01.207285", "created_by": "Tarun", "updated_by": "Tarun", "enum_value_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Attribute with attribute_id A_E_LeaveType_1750669441_status_1750669441 does not exist"], "parsed_data": {"enum_value_id": "EV_LEAVETYPE_STATUS_ACTIVE", "attribute_id": "A_E_LeaveType_1750669441_status_1750669441", "value": "Active", "display_name": "Active", "description": "Leave type is currently active", "sort_order": 1, "is_active": true, "natural_language": "Entity.Attribute: LeaveType.status\nEnum Name: Leave Type Status\nValue: Active\nDisplay: Active\nDescription: Leave type is currently active", "version": 1, "status": "active", "created_at": "2025-06-23T09:04:01.230268", "updated_at": "2025-06-23T09:04:01.230268", "created_by": "Tarun", "updated_by": "Tarun", "enum_value_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Attribute with attribute_id A_E_LeaveType_1750669441_status_1750669441 does not exist"], "parsed_data": {"enum_value_id": "EV_LEAVETYPE_STATUS_INACTIVE", "attribute_id": "A_E_LeaveType_1750669441_status_1750669441", "value": "Inactive", "display_name": "Inactive", "description": "Leave type is inactive", "sort_order": 2, "is_active": true, "natural_language": "Entity.Attribute: LeaveType.status\nEnum Name: Leave Type Status\nValue: Inactive\nDisplay: Inactive\nDescription: Leave type is inactive", "version": 1, "status": "active", "created_at": "2025-06-23T09:04:01.252422", "updated_at": "2025-06-23T09:04:01.252422", "created_by": "Tarun", "updated_by": "Tarun", "enum_value_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Attribute with attribute_id A_E_LeaveSubType_1750669441_requiresManagerApproval_1750669441 does not exist"], "parsed_data": {"enum_value_id": "EV_LEAVESUBTYPE_REQUIRESMANAGERAPPROVAL_TRUE", "attribute_id": "A_E_LeaveSubType_1750669441_requiresManagerApproval_1750669441", "value": "true", "display_name": "Required", "description": "Manager approval is required", "sort_order": 1, "is_active": true, "natural_language": "Entity.Attribute: LeaveSubType.requiresManagerApproval\nEnum Name: Manager Approval Requirement\nValue: true\nDisplay: Required\nDescription: Manager approval is required", "version": 1, "status": "active", "created_at": "2025-06-23T09:04:01.274153", "updated_at": "2025-06-23T09:04:01.274153", "created_by": "Tarun", "updated_by": "Tarun", "enum_value_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Attribute with attribute_id A_E_LeaveSubType_1750669441_requiresManagerApproval_1750669441 does not exist"], "parsed_data": {"enum_value_id": "EV_LEAVESUBTYPE_REQUIRESMANAGERAPPROVAL_FALSE", "attribute_id": "A_E_LeaveSubType_1750669441_requiresManagerApproval_1750669441", "value": "false", "display_name": "Not Required", "description": "Manager approval is not required", "sort_order": 2, "is_active": true, "natural_language": "Entity.Attribute: LeaveSubType.requiresManagerApproval\nEnum Name: Manager Approval Requirement\nValue: false\nDisplay: Not Required\nDescription: Manager approval is not required", "version": 1, "status": "active", "created_at": "2025-06-23T09:04:01.297993", "updated_at": "2025-06-23T09:04:01.297993", "created_by": "Tarun", "updated_by": "Tarun", "enum_value_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Attribute with attribute_id A_E_LeaveSubType_1750669441_status_1750669441 does not exist"], "parsed_data": {"enum_value_id": "EV_LEAVESUBTYPE_STATUS_ACTIVE", "attribute_id": "A_E_LeaveSubType_1750669441_status_1750669441", "value": "Active", "display_name": "Active", "description": "Leave sub-type is currently active", "sort_order": 1, "is_active": true, "natural_language": "Entity.Attribute: LeaveSubType.status\nEnum Name: Leave Sub-Type Status\nValue: Active\nDisplay: Active\nDescription: Leave sub-type is currently active", "version": 1, "status": "active", "created_at": "2025-06-23T09:04:01.320066", "updated_at": "2025-06-23T09:04:01.320066", "created_by": "Tarun", "updated_by": "Tarun", "enum_value_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Attribute with attribute_id A_E_LeaveSubType_1750669441_status_1750669441 does not exist"], "parsed_data": {"enum_value_id": "EV_LEAVESUBTYPE_STATUS_INACTIVE", "attribute_id": "A_E_LeaveSubType_1750669441_status_1750669441", "value": "Inactive", "display_name": "Inactive", "description": "Leave sub-type is inactive", "sort_order": 2, "is_active": true, "natural_language": "Entity.Attribute: LeaveSubType.status\nEnum Name: Leave Sub-Type Status\nValue: Inactive\nDisplay: Inactive\nDescription: Leave sub-type is inactive", "version": 1, "status": "active", "created_at": "2025-06-23T09:04:01.340958", "updated_at": "2025-06-23T09:04:01.340958", "created_by": "Tarun", "updated_by": "Tarun", "enum_value_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Attribute with attribute_id A_E_Constants_1750669441_dataType_1750669441 does not exist"], "parsed_data": {"enum_value_id": "EV_CONSTANTS_DATATYPE_STRING", "attribute_id": "A_E_Constants_1750669441_dataType_1750669441", "value": "String", "display_name": "String", "description": "Text data type", "sort_order": 1, "is_active": true, "natural_language": "Entity.Attribute: Constants.dataType\nEnum Name: Data Type\nValue: String\nDisplay: String\nDescription: Text data type", "version": 1, "status": "active", "created_at": "2025-06-23T09:04:01.361956", "updated_at": "2025-06-23T09:04:01.361956", "created_by": "Tarun", "updated_by": "Tarun", "enum_value_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Attribute with attribute_id A_E_Constants_1750669441_dataType_1750669441 does not exist"], "parsed_data": {"enum_value_id": "EV_CONSTANTS_DATATYPE_INTEGER", "attribute_id": "A_E_Constants_1750669441_dataType_1750669441", "value": "Integer", "display_name": "Integer", "description": "Whole number data type", "sort_order": 2, "is_active": true, "natural_language": "Entity.Attribute: Constants.dataType\nEnum Name: Data Type\nValue: Integer\nDisplay: Integer\nDescription: Whole number data type", "version": 1, "status": "active", "created_at": "2025-06-23T09:04:01.383675", "updated_at": "2025-06-23T09:04:01.383675", "created_by": "Tarun", "updated_by": "Tarun", "enum_value_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Attribute with attribute_id A_E_Constants_1750669441_dataType_1750669441 does not exist"], "parsed_data": {"enum_value_id": "EV_CONSTANTS_DATATYPE_DECIMAL", "attribute_id": "A_E_Constants_1750669441_dataType_1750669441", "value": "Decimal", "display_name": "Decimal", "description": "Decimal number data type", "sort_order": 3, "is_active": true, "natural_language": "Entity.Attribute: Constants.dataType\nEnum Name: Data Type\nValue: Decimal\nDisplay: Decimal\nDescription: Decimal number data type", "version": 1, "status": "active", "created_at": "2025-06-23T09:04:01.404904", "updated_at": "2025-06-23T09:04:01.404904", "created_by": "Tarun", "updated_by": "Tarun", "enum_value_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Attribute with attribute_id A_E_Constants_1750669441_dataType_1750669441 does not exist"], "parsed_data": {"enum_value_id": "EV_CONSTANTS_DATATYPE_BOOLEAN", "attribute_id": "A_E_Constants_1750669441_dataType_1750669441", "value": "Boolean", "display_name": "Boolean", "description": "True/False data type", "sort_order": 4, "is_active": true, "natural_language": "Entity.Attribute: Constants.dataType\nEnum Name: Data Type\nValue: Boolean\nDisplay: Boolean\nDescription: True/False data type", "version": 1, "status": "active", "created_at": "2025-06-23T09:04:01.425918", "updated_at": "2025-06-23T09:04:01.425918", "created_by": "Tarun", "updated_by": "Tarun", "enum_value_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Attribute with attribute_id A_E_Constants_1750669441_dataType_1750669441 does not exist"], "parsed_data": {"enum_value_id": "EV_CONSTANTS_DATATYPE_DATE", "attribute_id": "A_E_Constants_1750669441_dataType_1750669441", "value": "Date", "display_name": "Date", "description": "Date data type", "sort_order": 5, "is_active": true, "natural_language": "Entity.Attribute: Constants.dataType\nEnum Name: Data Type\nValue: Date\nDisplay: Date\nDescription: Date data type", "version": 1, "status": "active", "created_at": "2025-06-23T09:04:01.447369", "updated_at": "2025-06-23T09:04:01.447369", "created_by": "Tarun", "updated_by": "Tarun", "enum_value_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Attribute with attribute_id A_E_Constants_1750669441_status_1750669441 does not exist"], "parsed_data": {"enum_value_id": "EV_CONSTANTS_STATUS_ACTIVE", "attribute_id": "A_E_Constants_1750669441_status_1750669441", "value": "Active", "display_name": "Active", "description": "Constant is currently active", "sort_order": 1, "is_active": true, "natural_language": "Entity.Attribute: Constants.status\nEnum Name: Constant Status\nValue: Active\nDisplay: Active\nDescription: Constant is currently active", "version": 1, "status": "active", "created_at": "2025-06-23T09:04:01.469110", "updated_at": "2025-06-23T09:04:01.469110", "created_by": "Tarun", "updated_by": "Tarun", "enum_value_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Attribute with attribute_id A_E_Constants_1750669441_status_1750669441 does not exist"], "parsed_data": {"enum_value_id": "EV_CONSTANTS_STATUS_INACTIVE", "attribute_id": "A_E_Constants_1750669441_status_1750669441", "value": "Inactive", "display_name": "Inactive", "description": "Constant is inactive", "sort_order": 2, "is_active": true, "natural_language": "Entity.Attribute: Constants.status\nEnum Name: Constant Status\nValue: Inactive\nDisplay: Inactive\nDescription: Constant is inactive", "version": 1, "status": "active", "created_at": "2025-06-23T09:04:01.489849", "updated_at": "2025-06-23T09:04:01.489849", "created_by": "Tarun", "updated_by": "Tarun", "enum_value_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Attribute with attribute_id A_E_Common_1750669441_status_1750669441 does not exist"], "parsed_data": {"enum_value_id": "EV_COMMON_STATUS_ACTIVE", "attribute_id": "A_E_Common_1750669441_status_1750669441", "value": "Active", "display_name": "Active", "description": "Item is currently active", "sort_order": 1, "is_active": true, "natural_language": "Entity.Attribute: Common.status\nEnum Name: General Status\nValue: Active\nDisplay: Active\nDescription: Item is currently active", "version": 1, "status": "active", "created_at": "2025-06-23T09:04:01.511082", "updated_at": "2025-06-23T09:04:01.511082", "created_by": "Tarun", "updated_by": "Tarun", "enum_value_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Attribute with attribute_id A_E_Common_1750669441_status_1750669441 does not exist"], "parsed_data": {"enum_value_id": "EV_COMMON_STATUS_INACTIVE", "attribute_id": "A_E_Common_1750669441_status_1750669441", "value": "Inactive", "display_name": "Inactive", "description": "Item is inactive", "sort_order": 2, "is_active": true, "natural_language": "Entity.Attribute: Common.status\nEnum Name: General Status\nValue: Inactive\nDisplay: Inactive\nDescription: Item is inactive", "version": 1, "status": "active", "created_at": "2025-06-23T09:04:01.533145", "updated_at": "2025-06-23T09:04:01.533145", "created_by": "Tarun", "updated_by": "Tarun", "enum_value_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Attribute with attribute_id A_E_Common_1750669441_status_1750669441 does not exist"], "parsed_data": {"enum_value_id": "EV_COMMON_STATUS_DRAFT", "attribute_id": "A_E_Common_1750669441_status_1750669441", "value": "Draft", "display_name": "Draft", "description": "Item is in draft state", "sort_order": 3, "is_active": true, "natural_language": "Entity.Attribute: Common.status\nEnum Name: General Status\nValue: Draft\nDisplay: Draft\nDescription: Item is in draft state", "version": 1, "status": "active", "created_at": "2025-06-23T09:04:01.556897", "updated_at": "2025-06-23T09:04:01.556897", "created_by": "Tarun", "updated_by": "Tarun", "enum_value_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Attribute with attribute_id A_E_Common_1750669441_status_1750669441 does not exist"], "parsed_data": {"enum_value_id": "EV_COMMON_STATUS_ARCHIVED", "attribute_id": "A_E_Common_1750669441_status_1750669441", "value": "Archived", "display_name": "Archived", "description": "Item has been archived", "sort_order": 4, "is_active": true, "natural_language": "Entity.Attribute: Common.status\nEnum Name: General Status\nValue: Archived\nDisplay: Archived\nDescription: Item has been archived", "version": 1, "status": "active", "created_at": "2025-06-23T09:04:01.576313", "updated_at": "2025-06-23T09:04:01.576313", "created_by": "Tarun", "updated_by": "Tarun", "enum_value_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Attribute with attribute_id A_E_Common_1750669441_yesNo_1750669441 does not exist"], "parsed_data": {"enum_value_id": "EV_COMMON_YESNO_TRUE", "attribute_id": "A_E_Common_1750669441_yesNo_1750669441", "value": "true", "display_name": "Yes", "description": "Affirmative response", "sort_order": 1, "is_active": true, "natural_language": "Entity.Attribute: Common.yesNo\nEnum Name: Yes/No\nValue: true\nDisplay: Yes\nDescription: Affirmative response", "version": 1, "status": "active", "created_at": "2025-06-23T09:04:01.597828", "updated_at": "2025-06-23T09:04:01.597828", "created_by": "Tarun", "updated_by": "Tarun", "enum_value_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Attribute with attribute_id A_E_Common_1750669441_yesNo_1750669441 does not exist"], "parsed_data": {"enum_value_id": "EV_COMMON_YESNO_FALSE", "attribute_id": "A_E_Common_1750669441_yesNo_1750669441", "value": "false", "display_name": "No", "description": "Negative response", "sort_order": 2, "is_active": true, "natural_language": "Entity.Attribute: Common.yesNo\nEnum Name: Yes/No\nValue: false\nDisplay: No\nDescription: Negative response", "version": 1, "status": "active", "created_at": "2025-06-23T09:04:01.618936", "updated_at": "2025-06-23T09:04:01.618936", "created_by": "Tarun", "updated_by": "Tarun", "enum_value_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Attribute with attribute_id A_E_LeaveType_1750669441_leaveTypeName_1750669441 does not exist"], "parsed_data": {"enum_value_id": "EV_LEAVETYPE_LEAVETYPENAME_ANNUAL_LEAVE", "attribute_id": "A_E_LeaveType_1750669441_leaveTypeName_1750669441", "value": "Annual Leave", "display_name": "Annual Leave", "description": "Standard annual vacation leave", "sort_order": 1, "is_active": true, "natural_language": "Entity.Attribute: LeaveType.leaveTypeName\nEnum Name: Annual Leave Types\nValue: Annual Leave\nDisplay: Annual Leave\nDescription: Standard annual vacation leave", "version": 1, "status": "active", "created_at": "2025-06-23T09:04:01.641180", "updated_at": "2025-06-23T09:04:01.641180", "created_by": "Tarun", "updated_by": "Tarun", "enum_value_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Attribute with attribute_id A_E_LeaveType_1750669441_leaveTypeName_1750669441 does not exist"], "parsed_data": {"enum_value_id": "EV_LEAVETYPE_LEAVETYPENAME_SICK_LEAVE", "attribute_id": "A_E_LeaveType_1750669441_leaveTypeName_1750669441", "value": "Sick Leave", "display_name": "Sick Leave", "description": "Medical leave for illness", "sort_order": 2, "is_active": true, "natural_language": "Entity.Attribute: LeaveType.leaveTypeName\nEnum Name: Annual Leave Types\nValue: Sick Leave\nDisplay: Sick Leave\nDescription: Medical leave for illness", "version": 1, "status": "active", "created_at": "2025-06-23T09:04:01.660952", "updated_at": "2025-06-23T09:04:01.660952", "created_by": "Tarun", "updated_by": "Tarun", "enum_value_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Attribute with attribute_id A_E_LeaveType_1750669441_leaveTypeName_1750669441 does not exist"], "parsed_data": {"enum_value_id": "EV_LEAVETYPE_LEAVETYPENAME_PERSONAL_LEAVE", "attribute_id": "A_E_LeaveType_1750669441_leaveTypeName_1750669441", "value": "Personal Leave", "display_name": "Personal Leave", "description": "Personal time off", "sort_order": 3, "is_active": true, "natural_language": "Entity.Attribute: LeaveType.leaveTypeName\nEnum Name: Annual Leave Types\nValue: Personal Leave\nDisplay: Personal Leave\nDescription: Personal time off", "version": 1, "status": "active", "created_at": "2025-06-23T09:04:01.684314", "updated_at": "2025-06-23T09:04:01.684314", "created_by": "Tarun", "updated_by": "Tarun", "enum_value_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Attribute with attribute_id A_E_LeaveType_1750669441_leaveTypeName_1750669441 does not exist"], "parsed_data": {"enum_value_id": "EV_LEAVETYPE_LEAVETYPENAME_MATERNITY_LEAVE", "attribute_id": "A_E_LeaveType_1750669441_leaveTypeName_1750669441", "value": "Maternity Leave", "display_name": "Maternity Leave", "description": "Leave for new mothers", "sort_order": 4, "is_active": true, "natural_language": "Entity.Attribute: LeaveType.leaveTypeName\nEnum Name: Annual Leave Types\nValue: Maternity Leave\nDisplay: Maternity Leave\nDescription: Leave for new mothers", "version": 1, "status": "active", "created_at": "2025-06-23T09:04:01.704262", "updated_at": "2025-06-23T09:04:01.704262", "created_by": "Tarun", "updated_by": "Tarun", "enum_value_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Attribute with attribute_id A_E_LeaveType_1750669441_leaveTypeName_1750669441 does not exist"], "parsed_data": {"enum_value_id": "EV_LEAVETYPE_LEAVETYPENAME_PATERNITY_LEAVE", "attribute_id": "A_E_LeaveType_1750669441_leaveTypeName_1750669441", "value": "Paternity Leave", "display_name": "Paternity Leave", "description": "Leave for new fathers", "sort_order": 5, "is_active": true, "natural_language": "Entity.Attribute: LeaveType.leaveTypeName\nEnum Name: Annual Leave Types\nValue: Paternity Leave\nDisplay: Paternity Leave\nDescription: Leave for new fathers", "version": 1, "status": "active", "created_at": "2025-06-23T09:04:01.727186", "updated_at": "2025-06-23T09:04:01.727186", "created_by": "Tarun", "updated_by": "Tarun", "enum_value_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Attribute with attribute_id A_E_LeaveType_1750669441_leaveTypeName_1750669441 does not exist"], "parsed_data": {"enum_value_id": "EV_LEAVETYPE_LEAVETYPENAME_BEREAVEMENT_LEAVE", "attribute_id": "A_E_LeaveType_1750669441_leaveTypeName_1750669441", "value": "Bereavement Leave", "display_name": "Bereavement Leave", "description": "Leave for family bereavement", "sort_order": 6, "is_active": true, "natural_language": "Entity.Attribute: LeaveType.leaveTypeName\nEnum Name: Annual Leave Types\nValue: Bereavement Leave\nDisplay: Bereavement Leave\nDescription: Leave for family bereavement", "version": 1, "status": "active", "created_at": "2025-06-23T09:04:01.747331", "updated_at": "2025-06-23T09:04:01.747331", "created_by": "Tarun", "updated_by": "Tarun", "enum_value_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Attribute with attribute_id A_E_LeaveType_1750669441_leaveTypeName_1750669441 does not exist"], "parsed_data": {"enum_value_id": "EV_LEAVETYPE_LEAVETYPENAME_STUDY_LEAVE", "attribute_id": "A_E_LeaveType_1750669441_leaveTypeName_1750669441", "value": "Study Leave", "display_name": "Study Leave", "description": "Leave for educational purposes", "sort_order": 7, "is_active": true, "natural_language": "Entity.Attribute: LeaveType.leaveTypeName\nEnum Name: Annual Leave Types\nValue: Study Leave\nDisplay: Study Leave\nDescription: Leave for educational purposes", "version": 1, "status": "active", "created_at": "2025-06-23T09:04:01.768138", "updated_at": "2025-06-23T09:04:01.768138", "created_by": "Tarun", "updated_by": "Tarun", "enum_value_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Attribute with attribute_id A_E_LeaveType_1750669441_leaveTypeName_1750669441 does not exist"], "parsed_data": {"enum_value_id": "EV_LEAVETYPE_LEAVETYPENAME_EMERGENCY_LEAVE", "attribute_id": "A_E_LeaveType_1750669441_leaveTypeName_1750669441", "value": "Emergency Leave", "display_name": "Emergency Leave", "description": "Leave for emergency situations", "sort_order": 8, "is_active": true, "natural_language": "Entity.Attribute: LeaveType.leaveTypeName\nEnum Name: Annual Leave Types\nValue: Emergency Leave\nDisplay: Emergency Leave\nDescription: Leave for emergency situations", "version": 1, "status": "active", "created_at": "2025-06-23T09:04:01.788292", "updated_at": "2025-06-23T09:04:01.788292", "created_by": "Tarun", "updated_by": "Tarun", "enum_value_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Attribute with attribute_id A_E_LeaveSubType_1750669441_leaveSubTypeName_1750669441 does not exist"], "parsed_data": {"enum_value_id": "EV_LEAVESUBTYPE_LEAVESUBTYPENAME_SHORT_TERM_ILLNESS", "attribute_id": "A_E_LeaveSubType_1750669441_leaveSubTypeName_1750669441", "value": "Short Term Illness", "display_name": "Short Term Illness", "description": "Illness lasting less than 5 days", "sort_order": 1, "is_active": true, "natural_language": "Entity.Attribute: LeaveSubType.leaveSubTypeName\nEnum Name: Sick Leave Sub-Types\nValue: Short Term Illness\nDisplay: Short Term Illness\nDescription: Illness lasting less than 5 days", "version": 1, "status": "active", "created_at": "2025-06-23T09:04:01.808183", "updated_at": "2025-06-23T09:04:01.808183", "created_by": "Tarun", "updated_by": "Tarun", "enum_value_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Attribute with attribute_id A_E_LeaveSubType_1750669441_leaveSubTypeName_1750669441 does not exist"], "parsed_data": {"enum_value_id": "EV_LEAVESUBTYPE_LEAVESUBTYPENAME_LONG_TERM_ILLNESS", "attribute_id": "A_E_LeaveSubType_1750669441_leaveSubTypeName_1750669441", "value": "Long Term Illness", "display_name": "Long Term Illness", "description": "Illness lasting more than 5 days", "sort_order": 2, "is_active": true, "natural_language": "Entity.Attribute: LeaveSubType.leaveSubTypeName\nEnum Name: Sick Leave Sub-Types\nValue: Long Term Illness\nDisplay: Long Term Illness\nDescription: Illness lasting more than 5 days", "version": 1, "status": "active", "created_at": "2025-06-23T09:04:01.828060", "updated_at": "2025-06-23T09:04:01.828060", "created_by": "Tarun", "updated_by": "Tarun", "enum_value_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Attribute with attribute_id A_E_LeaveSubType_1750669441_leaveSubTypeName_1750669441 does not exist"], "parsed_data": {"enum_value_id": "EV_LEAVESUBTYPE_LEAVESUBTYPENAME_MEDICAL_APPOINTMENT", "attribute_id": "A_E_LeaveSubType_1750669441_leaveSubTypeName_1750669441", "value": "Medical Appointment", "display_name": "Medical Appointment", "description": "Time off for medical appointments", "sort_order": 3, "is_active": true, "natural_language": "Entity.Attribute: LeaveSubType.leaveSubTypeName\nEnum Name: Sick Leave Sub-Types\nValue: Medical Appointment\nDisplay: Medical Appointment\nDescription: Time off for medical appointments", "version": 1, "status": "active", "created_at": "2025-06-23T09:04:01.851380", "updated_at": "2025-06-23T09:04:01.851380", "created_by": "Tarun", "updated_by": "Tarun", "enum_value_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Attribute with attribute_id A_E_LeaveSubType_1750669441_leaveSubTypeName_1750669441 does not exist"], "parsed_data": {"enum_value_id": "EV_LEAVESUBTYPE_LEAVESUBTYPENAME_MENTAL_HEALTH", "attribute_id": "A_E_LeaveSubType_1750669441_leaveSubTypeName_1750669441", "value": "Mental Health", "display_name": "Mental Health", "description": "Mental health related leave", "sort_order": 4, "is_active": true, "natural_language": "Entity.Attribute: LeaveSubType.leaveSubTypeName\nEnum Name: Sick Leave Sub-Types\nValue: Mental Health\nDisplay: Mental Health\nDescription: Mental health related leave", "version": 1, "status": "active", "created_at": "2025-06-23T09:04:01.873295", "updated_at": "2025-06-23T09:04:01.873295", "created_by": "Tarun", "updated_by": "Tarun", "enum_value_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Attribute with attribute_id A_E_LeaveSubType_1750669441_leaveSubTypeName_1750669441 does not exist"], "parsed_data": {"enum_value_id": "EV_LEAVESUBTYPE_LEAVESUBTYPENAME_FAMILY_EVENT", "attribute_id": "A_E_LeaveSubType_1750669441_leaveSubTypeName_1750669441", "value": "Family Event", "display_name": "Family Event", "description": "Family celebrations or events", "sort_order": 1, "is_active": true, "natural_language": "Entity.Attribute: LeaveSubType.leaveSubTypeName\nEnum Name: Personal Leave Sub-Types\nValue: Family Event\nDisplay: Family Event\nDescription: Family celebrations or events", "version": 1, "status": "active", "created_at": "2025-06-23T09:04:01.893152", "updated_at": "2025-06-23T09:04:01.893152", "created_by": "Tarun", "updated_by": "Tarun", "enum_value_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Attribute with attribute_id A_E_LeaveSubType_1750669441_leaveSubTypeName_1750669441 does not exist"], "parsed_data": {"enum_value_id": "EV_LEAVESUBTYPE_LEAVESUBTYPENAME_PERSONAL_BUSINESS", "attribute_id": "A_E_LeaveSubType_1750669441_leaveSubTypeName_1750669441", "value": "Personal Business", "display_name": "Personal Business", "description": "Personal business matters", "sort_order": 2, "is_active": true, "natural_language": "Entity.Attribute: LeaveSubType.leaveSubTypeName\nEnum Name: Personal Leave Sub-Types\nValue: Personal Business\nDisplay: Personal Business\nDescription: Personal business matters", "version": 1, "status": "active", "created_at": "2025-06-23T09:04:01.915742", "updated_at": "2025-06-23T09:04:01.915742", "created_by": "Tarun", "updated_by": "Tarun", "enum_value_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Attribute with attribute_id A_E_LeaveSubType_1750669441_leaveSubTypeName_1750669441 does not exist"], "parsed_data": {"enum_value_id": "EV_LEAVESUBTYPE_LEAVESUBTYPENAME_RELIGIOUS_OBSERVANCE", "attribute_id": "A_E_LeaveSubType_1750669441_leaveSubTypeName_1750669441", "value": "Religious Observance", "display_name": "Religious Observance", "description": "Religious holidays or observances", "sort_order": 3, "is_active": true, "natural_language": "Entity.Attribute: LeaveSubType.leaveSubTypeName\nEnum Name: Personal Leave Sub-Types\nValue: Religious Observance\nDisplay: Religious Observance\nDescription: Religious holidays or observances", "version": 1, "status": "active", "created_at": "2025-06-23T09:04:01.936915", "updated_at": "2025-06-23T09:04:01.936915", "created_by": "Tarun", "updated_by": "Tarun", "enum_value_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Attribute with attribute_id A_E_LeaveSubType_1750669441_leaveSubTypeName_1750669441 does not exist"], "parsed_data": {"enum_value_id": "EV_LEAVESUBTYPE_LEAVESUBTYPENAME_MOVING_RELOCATION", "attribute_id": "A_E_LeaveSubType_1750669441_leaveSubTypeName_1750669441", "value": "Moving/Relocation", "display_name": "Moving/Relocation", "description": "Time off for moving or relocation", "sort_order": 4, "is_active": true, "natural_language": "Entity.Attribute: LeaveSubType.leaveSubTypeName\nEnum Name: Personal Leave Sub-Types\nValue: Moving/Relocation\nDisplay: Moving/Relocation\nDescription: Time off for moving or relocation", "version": 1, "status": "active", "created_at": "2025-06-23T09:04:01.958065", "updated_at": "2025-06-23T09:04:01.958065", "created_by": "Tarun", "updated_by": "Tarun", "enum_value_status": "new", "changes_detected": []}}], "operation": "parse_validate_mongosave", "total_enum_values": 50}, "status": "success"}
{"timestamp": "2025-06-23T09:55:55.016617", "endpoint": "parse-validate-mongosave/attribute-enum-values", "input": {"natural_language": "Tenant: Acme Corp\n\nEntity.Attribute | Enum Name | Value | Display | Description | Sort Order | Active\n\nLeaveApplication.status | Leave Application Status | Pending | Pending | Leave request is pending approval | 1 | true\n\nLeaveApplication.status | Leave Application Status | Approved | Approved | Leave request has been approved | 2 | true\n\nLeaveApplication.status | Leave Application Status | Rejected | Rejected | Leave request has been rejected | 3 | true\n\nLeaveApplication.status | Leave Application Status | Cancelled | Cancelled | Leave request has been cancelled | 4 | true\n\nLeaveApplication.requiresDocumentation | Documentation Requirement | true | Yes | Documentation is required | 1 | true\n\nLeaveApplication.requiresDocumentation | Documentation Requirement | false | No | Documentation is not required | 2 | true\n\nEmployee.employmentStatus | Employment Status | Active | Active | Employee is currently active | 1 | true\n\nEmployee.employmentStatus | Employment Status | Inactive | Inactive | Employee is temporarily inactive | 2 | true\n\nEmployee.employmentStatus | Employment Status | Terminated | Terminated | Employee has been terminated | 3 | true", "tenant_id": "T_ACME_CORP", "edit_draft": false, "edit_temp": false, "edit_production": false}, "output": {"success": true, "enum_value_results": [{"success": false, "errors": ["Attribute with attribute_id A_E_LeaveApplication_1750672554_status_1750672554 does not exist"], "parsed_data": {"enum_value_id": "EV_LEAVEAPPLICATION_STATUS_PENDING", "attribute_id": "A_E_LeaveApplication_1750672554_status_1750672554", "value": "Pending", "display_name": "Pending", "description": "Leave request is pending approval", "sort_order": 1, "is_active": true, "natural_language": "Entity.Attribute: LeaveApplication.status\nEnum Name: Leave Application Status\nValue: Pending\nDisplay: Pending\nDescription: Leave request is pending approval", "version": 1, "status": "active", "created_at": "2025-06-23T09:55:54.673394", "updated_at": "2025-06-23T09:55:54.673394", "created_by": "Tarun", "updated_by": "Tarun", "enum_value_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Attribute with attribute_id A_E_LeaveApplication_1750672554_status_1750672554 does not exist"], "parsed_data": {"enum_value_id": "EV_LEAVEAPPLICATION_STATUS_APPROVED", "attribute_id": "A_E_LeaveApplication_1750672554_status_1750672554", "value": "Approved", "display_name": "Approved", "description": "Leave request has been approved", "sort_order": 2, "is_active": true, "natural_language": "Entity.Attribute: LeaveApplication.status\nEnum Name: Leave Application Status\nValue: Approved\nDisplay: Approved\nDescription: Leave request has been approved", "version": 1, "status": "active", "created_at": "2025-06-23T09:55:54.698495", "updated_at": "2025-06-23T09:55:54.698495", "created_by": "Tarun", "updated_by": "Tarun", "enum_value_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Attribute with attribute_id A_E_LeaveApplication_1750672554_status_1750672554 does not exist"], "parsed_data": {"enum_value_id": "EV_LEAVEAPPLICATION_STATUS_REJECTED", "attribute_id": "A_E_LeaveApplication_1750672554_status_1750672554", "value": "Rejected", "display_name": "Rejected", "description": "Leave request has been rejected", "sort_order": 3, "is_active": true, "natural_language": "Entity.Attribute: LeaveApplication.status\nEnum Name: Leave Application Status\nValue: Rejected\nDisplay: Rejected\nDescription: Leave request has been rejected", "version": 1, "status": "active", "created_at": "2025-06-23T09:55:54.721811", "updated_at": "2025-06-23T09:55:54.721811", "created_by": "Tarun", "updated_by": "Tarun", "enum_value_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Attribute with attribute_id A_E_LeaveApplication_1750672554_status_1750672554 does not exist"], "parsed_data": {"enum_value_id": "EV_LEAVEAPPLICATION_STATUS_CANCELLED", "attribute_id": "A_E_LeaveApplication_1750672554_status_1750672554", "value": "Cancelled", "display_name": "Cancelled", "description": "Leave request has been cancelled", "sort_order": 4, "is_active": true, "natural_language": "Entity.Attribute: LeaveApplication.status\nEnum Name: Leave Application Status\nValue: Cancelled\nDisplay: Cancelled\nDescription: Leave request has been cancelled", "version": 1, "status": "active", "created_at": "2025-06-23T09:55:54.744322", "updated_at": "2025-06-23T09:55:54.744322", "created_by": "Tarun", "updated_by": "Tarun", "enum_value_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Attribute with attribute_id A_E_LeaveApplication_1750672554_requiresDocumentation_1750672554 does not exist"], "parsed_data": {"enum_value_id": "EV_LEAVEAPPLICATION_REQUIRESDOCUMENTATION_TRUE", "attribute_id": "A_E_LeaveApplication_1750672554_requiresDocumentation_1750672554", "value": "true", "display_name": "Yes", "description": "Documentation is required", "sort_order": 1, "is_active": true, "natural_language": "Entity.Attribute: LeaveApplication.requiresDocumentation\nEnum Name: Documentation Requirement\nValue: true\nDisplay: Yes\nDescription: Documentation is required", "version": 1, "status": "active", "created_at": "2025-06-23T09:55:54.765123", "updated_at": "2025-06-23T09:55:54.765123", "created_by": "Tarun", "updated_by": "Tarun", "enum_value_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Attribute with attribute_id A_E_LeaveApplication_1750672554_requiresDocumentation_1750672554 does not exist"], "parsed_data": {"enum_value_id": "EV_LEAVEAPPLICATION_REQUIRESDOCUMENTATION_FALSE", "attribute_id": "A_E_LeaveApplication_1750672554_requiresDocumentation_1750672554", "value": "false", "display_name": "No", "description": "Documentation is not required", "sort_order": 2, "is_active": true, "natural_language": "Entity.Attribute: LeaveApplication.requiresDocumentation\nEnum Name: Documentation Requirement\nValue: false\nDisplay: No\nDescription: Documentation is not required", "version": 1, "status": "active", "created_at": "2025-06-23T09:55:54.787458", "updated_at": "2025-06-23T09:55:54.787458", "created_by": "Tarun", "updated_by": "Tarun", "enum_value_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Attribute with attribute_id A_E_Employee_1750672554_employmentStatus_1750672554 does not exist"], "parsed_data": {"enum_value_id": "EV_EMPLOYEE_EMPLOYMENTSTATUS_ACTIVE", "attribute_id": "A_E_Employee_1750672554_employmentStatus_1750672554", "value": "Active", "display_name": "Active", "description": "Employee is currently active", "sort_order": 1, "is_active": true, "natural_language": "Entity.Attribute: Employee.employmentStatus\nEnum Name: Employment Status\nValue: Active\nDisplay: Active\nDescription: Employee is currently active", "version": 1, "status": "active", "created_at": "2025-06-23T09:55:54.809306", "updated_at": "2025-06-23T09:55:54.809306", "created_by": "Tarun", "updated_by": "Tarun", "enum_value_status": "existing", "changes_detected": []}}, {"success": false, "errors": ["Attribute with attribute_id A_E_Employee_1750672554_employmentStatus_1750672554 does not exist"], "parsed_data": {"enum_value_id": "EV_EMPLOYEE_EMPLOYMENTSTATUS_INACTIVE", "attribute_id": "A_E_Employee_1750672554_employmentStatus_1750672554", "value": "Inactive", "display_name": "Inactive", "description": "Employee is temporarily inactive", "sort_order": 2, "is_active": true, "natural_language": "Entity.Attribute: Employee.employmentStatus\nEnum Name: Employment Status\nValue: Inactive\nDisplay: Inactive\nDescription: Employee is temporarily inactive", "version": 1, "status": "active", "created_at": "2025-06-23T09:55:54.827133", "updated_at": "2025-06-23T09:55:54.827133", "created_by": "Tarun", "updated_by": "Tarun", "enum_value_status": "new", "changes_detected": []}}, {"success": false, "errors": ["Attribute with attribute_id A_E_Employee_1750672554_employmentStatus_1750672554 does not exist"], "parsed_data": {"enum_value_id": "EV_EMPLOYEE_EMPLOYMENTSTATUS_TERMINATED", "attribute_id": "A_E_Employee_1750672554_employmentStatus_1750672554", "value": "Terminated", "display_name": "Terminated", "description": "Employee has been terminated", "sort_order": 3, "is_active": true, "natural_language": "Entity.Attribute: Employee.employmentStatus\nEnum Name: Employment Status\nValue: Terminated\nDisplay: Terminated\nDescription: Employee has been terminated", "version": 1, "status": "active", "created_at": "2025-06-23T09:55:54.846404", "updated_at": "2025-06-23T09:55:54.846404", "created_by": "Tarun", "updated_by": "Tarun", "enum_value_status": "new", "changes_detected": []}}], "operation": "parse_validate_mongosave", "total_enum_values": 9}, "status": "success"}
{"timestamp": "2025-06-23T14:07:59.919624", "endpoint": "parse-validate-mongosave/attribute-enum-values", "input": {"natural_language": "Size field should have enum values: small, medium, large, extra-large", "tenant_id": "tenant_123", "edit_draft": false, "edit_temp": false, "edit_production": false}, "output": {"success": true, "enum_value_results": [], "operation": "parse_validate_mongosave", "total_enum_values": 0}, "status": "success"}
