-- Purchase Furniture Global Objective Insert Script
-- Inserts GO2: Purchase Furniture

SET search_path TO workflow_runtime;

-- =====================================================
-- GLOBAL OBJECTIVE (GO2)
-- =====================================================
INSERT INTO global_objectives (
    go_id, name, version, status, description, created_at, updated_at, 
    tenant_id, last_used, deleted_mark, version_type, metadata, auto_id, 
    primary_entity, classification, tenant_name, book_id, book_name, 
    chapter_id, chapter_name, created_by, updated_by, natural_language
) VALUES (
    'GO2', 
    'Purchase Furniture', 
    '1.0', 
    'Active', 
    'Manages furniture purchase process from selection to payment and inventory update', 
    NOW(), 
    NOW(), 
    'T2',
    NOW(), 
    false, 
    'v2', 
    '{}', 
    1834593450, 
    'FurnitureOrder', 
    'workflow', 
    'Acme Corporation', 
    'b002', 
    'Furniture Purchase Management', 
    'c002', 
    'Furniture Purchase Lifecycle', 
    'system', 
    'system', 
    'Purchase Furniture workflow for managing furniture orders'
);

-- =====================================================
-- EXECUTION PATHWAYS
-- =====================================================
INSERT INTO execution_pathways (
    id, execution_pathway_id, lo_id, pathway_type, next_lo, created_at, 
    created_by, updated_at, updated_by, version, natural_language
) VALUES 
('GO2.LO1.EP1_ID', 'GO2.LO1.EP1', 'GO2.LO1', 'SEQUENTIAL', 'GO2.LO2', NOW(), 'system', NOW(), 'system', '1.0', 'After furniture selection, proceed to cart and payment processing'),
('GO2.LO2.EP2_ID', 'GO2.LO2.EP2', 'GO2.LO2', 'SEQUENTIAL', 'GO2.LO3', NOW(), 'system', NOW(), 'system', '1.0', 'After payment processing, complete the order and update inventory'),
('GO2.LO3.EP3_ID', 'GO2.LO3.EP3', 'GO2.LO3', 'terminal', NULL, NOW(), 'system', NOW(), 'system', '1.0', 'Order completion is terminal - workflow ends');

-- =====================================================
-- CONSTANTS
-- =====================================================
INSERT INTO constants (
    constant_id, attribute, value, description, tenant_id, allow_override, 
    override_permissions, created_at, updated_at, created_by, updated_by, 
    status, entity_name, attribute_name, auto_id
) VALUES 
('GST_RATE', 'gst_percentage', '18', 'GST rate for furniture purchases', 'T2', false, NULL, NOW(), NOW(), 'system', 'system', 'Active', 'FurnitureOrder', 'gstamount', 1001),
('PAYMENT_METHODS', 'allowed_methods', '["UPI", "Credit Card"]', 'Allowed payment methods for furniture orders', 'T2', false, NULL, NOW(), NOW(), 'system', 'system', 'Active', 'FurnitureOrder', 'paymentmethod', 1002);

COMMIT;
