"""
YAML Assembler for YAML Builder v2

This module provides functionality for assembling individual components
(roles, entities, GO definitions, LO definitions) into a complete YAML file.
"""

import os
import sys
import yaml
import json
from typing import Dict, List, Tuple, Any, Optional

# Add parent directory to path to import common modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from common.db_connection import get_db_connection

class YAMLAssembler:
    """
    Assembles individual components into a complete YAML file.
    """
    
    def __init__(self):
        """Initialize the YAML assembler."""
        pass
        
    def assemble_yaml(self, components: Dict[str, str]) -> Tuple[str, List[str]]:
        """
        Assemble individual components into a complete YAML file.
        
        Args:
            components: Dictionary mapping component types to their YAML strings
                        (e.g., {'roles': '...', 'entities': '...', ...})
            
        Returns:
            Tuple containing:
                - Assembled YAML string
                - List of warning messages (empty if no warnings)
        """
        warnings = []
        
        try:
            # Parse all components
            parsed_components = {}
            for component_type, component_yaml in components.items():
                try:
                    parsed_components[component_type] = yaml.safe_load(component_yaml)
                except yaml.YAMLError as e:
                    warnings.append(f"YAML parsing error in {component_type}: {str(e)}")
                    continue
            
            # Initialize the assembled YAML
            assembled_data = {
                'version': 'v2',
                'metadata': {
                    'generated_by': 'YAML Builder v2',
                    'component_based': True
                }
            }
            
            # Add roles
            if 'roles' in parsed_components and 'roles' in parsed_components['roles']:
                assembled_data['roles'] = parsed_components['roles']['roles']
            else:
                warnings.append("No roles found in components")
            
            # Add entities
            if 'entities' in parsed_components and 'entities' in parsed_components['entities']:
                assembled_data['entities'] = parsed_components['entities']['entities']
            else:
                warnings.append("No entities found in components")
            
            # Add global objectives
            if 'go_definitions' in parsed_components and 'global_objectives' in parsed_components['go_definitions']:
                assembled_data['global_objectives'] = parsed_components['go_definitions']['global_objectives']
            else:
                warnings.append("No global objectives found in components")
            
            # Add local objectives
            if 'lo_definitions' in parsed_components and 'local_objectives' in parsed_components['lo_definitions']:
                assembled_data['local_objectives'] = parsed_components['lo_definitions']['local_objectives']
            else:
                warnings.append("No local objectives found in components")
            
            # Convert to YAML string
            assembled_yaml = yaml.dump(assembled_data, default_flow_style=False, sort_keys=False)
            
            return assembled_yaml, warnings
        except Exception as e:
            warnings.append(f"YAML assembly error: {str(e)}")
            return "", warnings
    
    def disassemble_yaml(self, yaml_string: str) -> Tuple[Dict[str, str], List[str]]:
        """
        Disassemble a complete YAML file into individual components.
        
        Args:
            yaml_string: Complete YAML string
            
        Returns:
            Tuple containing:
                - Dictionary mapping component types to their YAML strings
                - List of warning messages (empty if no warnings)
        """
        warnings = []
        components = {}
        
        try:
            # Parse YAML
            data = yaml.safe_load(yaml_string)
            
            # Check version
            if 'version' in data and data['version'] != 'v2':
                warnings.append(f"YAML version is {data.get('version', 'unknown')}, expected 'v2'")
            
            # Extract roles
            if 'roles' in data:
                roles_data = {'roles': data['roles']}
                components['roles'] = yaml.dump(roles_data, default_flow_style=False, sort_keys=False)
            else:
                warnings.append("No roles found in YAML")
            
            # Extract entities
            if 'entities' in data:
                entities_data = {'entities': data['entities']}
                components['entities'] = yaml.dump(entities_data, default_flow_style=False, sort_keys=False)
            else:
                warnings.append("No entities found in YAML")
            
            # Extract global objectives
            if 'global_objectives' in data:
                go_data = {'global_objectives': data['global_objectives']}
                components['go_definitions'] = yaml.dump(go_data, default_flow_style=False, sort_keys=False)
            else:
                warnings.append("No global objectives found in YAML")
            
            # Extract local objectives
            if 'local_objectives' in data:
                lo_data = {'local_objectives': data['local_objectives']}
                components['lo_definitions'] = yaml.dump(lo_data, default_flow_style=False, sort_keys=False)
            else:
                warnings.append("No local objectives found in YAML")
            
            return components, warnings
        except yaml.YAMLError as e:
            warnings.append(f"YAML parsing error: {str(e)}")
            return {}, warnings
        except Exception as e:
            warnings.append(f"YAML disassembly error: {str(e)}")
            return {}, warnings
    
    def convert_v1_to_v2(self, v1_yaml: str) -> Tuple[Dict[str, str], List[str]]:
        """
        Convert a v1 YAML file to v2 components.
        
        Args:
            v1_yaml: v1 YAML string
            
        Returns:
            Tuple containing:
                - Dictionary mapping component types to their YAML strings
                - List of warning messages (empty if no warnings)
        """
        warnings = []
        components = {}
        
        try:
            # Parse YAML
            data = yaml.safe_load(v1_yaml)
            
            # Extract roles
            if 'roles' in data:
                roles_data = {'roles': data['roles']}
                components['roles'] = yaml.dump(roles_data, default_flow_style=False, sort_keys=False)
            else:
                warnings.append("No roles found in v1 YAML")
            
            # Extract entities
            if 'entities' in data:
                entities_data = {'entities': data['entities']}
                components['entities'] = yaml.dump(entities_data, default_flow_style=False, sort_keys=False)
            else:
                warnings.append("No entities found in v1 YAML")
            
            # Extract global objectives
            if 'global_objectives' in data:
                go_data = {'global_objectives': data['global_objectives']}
                components['go_definitions'] = yaml.dump(go_data, default_flow_style=False, sort_keys=False)
            else:
                warnings.append("No global objectives found in v1 YAML")
            
            # Extract local objectives
            if 'local_objectives' in data:
                lo_data = {'local_objectives': data['local_objectives']}
                components['lo_definitions'] = yaml.dump(lo_data, default_flow_style=False, sort_keys=False)
            else:
                warnings.append("No local objectives found in v1 YAML")
            
            # Add conversion warning
            warnings.append("Converted from v1 YAML. Please review the components for completeness.")
            
            return components, warnings
        except yaml.YAMLError as e:
            warnings.append(f"YAML parsing error: {str(e)}")
            return {}, warnings
        except Exception as e:
            warnings.append(f"v1 to v2 conversion error: {str(e)}")
            return {}, warnings


# Example usage
if __name__ == "__main__":
    assembler = YAMLAssembler()
    
    # Example components
    roles_yaml = """
    roles:
      admin:
        description: Administrator role
        permissions:
          - create_user
          - delete_user
      user:
        description: Regular user role
        permissions:
          - view_dashboard
    """
    
    entities_yaml = """
    entities:
      user:
        attributes:
          id:
            type: string
          name:
            type: string
      profile:
        attributes:
          user_id:
            type: string
          bio:
            type: string
    """
    
    components = {
        'roles': roles_yaml,
        'entities': entities_yaml
    }
    
    # Assemble YAML
    assembled_yaml, warnings = assembler.assemble_yaml(components)
    print("Assembled YAML:")
    print(assembled_yaml)
    
    if warnings:
        print("\nWarnings:")
        for warning in warnings:
            print(f"- {warning}")
    
    # Disassemble YAML
    disassembled_components, warnings = assembler.disassemble_yaml(assembled_yaml)
    print("\nDisassembled components:")
    for component_type, component_yaml in disassembled_components.items():
        print(f"\n{component_type}:")
        print(component_yaml)
    
    if warnings:
        print("\nWarnings:")
        for warning in warnings:
            print(f"- {warning}")
