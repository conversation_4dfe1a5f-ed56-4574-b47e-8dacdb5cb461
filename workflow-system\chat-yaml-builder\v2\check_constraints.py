#!/usr/bin/env python3
"""
Check if constraints and relationship properties are being correctly parsed and stored in the database.
"""

import sys
import os
import json
import psycopg2
from parsers.entity_parser import parse_entities

def get_db_connection(schema_name=None):
    """Get database connection with optional schema"""
    db_host = os.environ.get('DB_HOST', '**********')
    db_port = os.environ.get('DB_PORT', '5432')
    db_name = os.environ.get('DB_NAME', 'workflow_system')
    db_user = os.environ.get('DB_USER', 'postgres')
    db_password = os.environ.get('DB_PASSWORD', 'workflow_postgres_secure_password')
    
    conn = psycopg2.connect(
        host=db_host,
        port=db_port,
        dbname=db_name,
        user=db_user,
        password=db_password
    )
    
    if schema_name:
        with conn.cursor() as cursor:
            cursor.execute(f"SET search_path TO {schema_name}")
            conn.commit()
    
    return conn

def check_db_relationship_properties():
    """Check if relationship properties are stored in the database"""
    schema_name = 'workflow_temp'
    conn = get_db_connection(schema_name)
    
    try:
        # Check for Employee to Department relationship in the database
        with conn.cursor() as cursor:
            cursor.execute("""
            SELECT 
                er.id, 
                s.name as source_entity, 
                t.name as target_entity, 
                er.relationship_type, 
                sa.name as source_attribute, 
                ta.name as target_attribute,
                er.on_delete,
                er.on_update,
                er.foreign_key_type
            FROM 
                entity_relationships er
                JOIN entities s ON er.source_entity_id = s.entity_id
                JOIN entities t ON er.target_entity_id = t.entity_id
                JOIN entity_attributes sa ON er.source_attribute_id = sa.attribute_id
                JOIN entity_attributes ta ON er.target_attribute_id = ta.attribute_id
            WHERE 
                s.name = 'Employee' AND t.name = 'Department'
            """)
            
            result = cursor.fetchall()
            
            if result:
                print("\nEmployee to Department relationship in the database:")
                for row in result:
                    print(f"  - ID: {row[0]}")
                    print(f"  - Source Entity: {row[1]}")
                    print(f"  - Target Entity: {row[2]}")
                    print(f"  - Relationship Type: {row[3]}")
                    print(f"  - Source Attribute: {row[4]}")
                    print(f"  - Target Attribute: {row[5]}")
                    print(f"  - ON DELETE: {row[6]}")
                    print(f"  - ON UPDATE: {row[7]}")
                    print(f"  - Foreign Key Type: {row[8]}")
            else:
                print("\nNo Employee to Department relationship found in the database.")
                
                # Check if the departmentId attribute exists for Employee
                cursor.execute("""
                SELECT 
                    attribute_id, name, entity_id
                FROM 
                    entity_attributes
                WHERE 
                    entity_id = (SELECT entity_id FROM entities WHERE name = 'Employee')
                    AND name = 'departmentId'
                """)
                
                result = cursor.fetchone()
                if result:
                    print(f"Found departmentId attribute for Employee: {result[0]}")
                else:
                    print("No departmentId attribute found for Employee")
    except Exception as e:
        print(f"Error checking database: {str(e)}")
    finally:
        conn.close()

def main():
    # Read sample text
    sample_path = os.path.join(os.path.dirname(__file__), 'samples', 'sample_entity_output2.txt')
    with open(sample_path, 'r') as f:
        sample_text = f.read()
    
    # Parse entities
    entities_data, warnings = parse_entities(sample_text)
    
    # Check if Employee has constraints
    if 'Employee' in entities_data['entities']:
        employee_data = entities_data['entities']['Employee']
        print("Employee Constraints:")
        print(json.dumps(employee_data.get('constraints', {}), indent=2))
        
        # Check if Employee has validations
        print("\nEmployee Validations:")
        print(json.dumps(employee_data.get('validations', {}), indent=2))
        
        # Check if Employee has relationships
        print("\nEmployee Relationships:")
        relationships = employee_data.get('relationships', {})
        print(json.dumps(relationships, indent=2))
        
        # Check specifically for Employee to Department relationship
        for rel_name, rel_data in relationships.items():
            if rel_data.get('entity') == 'Department':
                print(f"\nFound Employee to Department relationship: {rel_name}")
                print(f"  Type: {rel_data.get('type')}")
                print(f"  Source Attribute: {rel_data.get('source_attribute')}")
                print(f"  Target Attribute: {rel_data.get('target_attribute')}")
                
                # Check for relationship properties
                properties = rel_data.get('properties', {})
                print("  Properties:")
                for prop_name, prop_value in properties.items():
                    print(f"    {prop_name}: {prop_value}")
    else:
        print("Employee entity not found")
    
    # Check if relationship properties are stored in the database
    check_db_relationship_properties()

if __name__ == '__main__':
    main()
