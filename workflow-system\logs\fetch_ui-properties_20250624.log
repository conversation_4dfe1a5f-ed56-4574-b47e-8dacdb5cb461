{"timestamp": "2025-06-24T13:11:51.157419", "endpoint": "fetch/ui-properties", "input": {}, "output": {"success": true, "postgres_ui_properties": [{"id": 1, "ui_property_id": "UI8", "entity_id": "E15", "attribute_id": "E15.At1", "control_type": "text_input", "display_format": "EMP###", "input_mask": "EMP###", "placeholder_text": "Auto-generated", "auto_complete": false, "read_only": true, "validation_display": "inline", "help_text_position": "below", "label": "Employee ID", "required_indicator": false, "natural_language": "Entity: Employee\nAttribute: employeeId\nControl Type: text_input\nLabel: Employee ID\nDisplay Format: EMP###\nInput Mask: EMP###\nPlaceholder Text: Auto-generated\nRead Only: true\nValidation Display: inline\nHelp Text Position: below", "version": 2, "status": "deployed_to_temp", "created_by": "system", "updated_by": "system", "created_at": "2025-06-24T11:43:35.725023", "updated_at": "2025-06-24T11:43:35.725023", "ui_property_status": "new", "changes_detected": []}, {"id": 2, "ui_property_id": "UI9", "entity_id": "E13", "attribute_id": "E13.At11", "control_type": "number_input", "display_format": "##.# days", "input_mask": "##.#", "placeholder_text": "Enter entitlement", "auto_complete": false, "read_only": false, "validation_display": "inline", "help_text_position": "below", "label": "Total Leave Entitlement", "required_indicator": false, "natural_language": "Entity: Employee\nAttribute: totalLeaveEntitlement\nControl Type: number_input\nLabel: Total Leave Entitlement\nDisplay Format: ##.# days\nInput Mask: ##.#\nPlaceholder Text: Enter entitlement\nValidation Display: inline\nHelp Text Position: below", "version": 5, "status": "deployed_to_temp", "created_by": "system", "updated_by": "system", "created_at": "2025-06-24T11:43:35.752704", "updated_at": "2025-06-24T11:43:35.752704", "ui_property_status": "existing", "changes_detected": []}], "mongo_drafts": [{"_id": "68552fce229f6505fc03bb6b", "ui_property_id": "UI5", "entity_id": "E15", "attribute_id": "E15.At1", "control_type": "text_input", "display_format": "EMP###", "input_mask": "EMP###", "placeholder_text": "Auto-generated", "auto_complete": false, "read_only": true, "validation_display": "inline", "help_text_position": "below", "label": "Employee ID", "required_indicator": false, "natural_language": "Entity: Employee\nAttribute: employeeId\nControl Type: text_input\nLabel: Employee ID\nDisplay Format: EMP###\nInput Mask: EMP###\nPlaceholder Text: Auto-generated\nRead Only: true\nValidation Display: inline\nHelp Text Position: below", "version": 3, "status": "deployed_to_production", "created_at": "2025-06-20T09:54:22.597847", "updated_at": "2025-06-24T11:43:35.728042", "created_by": "<PERSON><PERSON>", "updated_by": "<PERSON><PERSON>", "ui_property_status": "new", "changes_detected": []}, {"_id": "685903f05e423f11979d4093", "ui_property_id": "UI1", "entity_id": "E13", "attribute_id": "E13.At11", "control_type": "number_input", "display_format": "##.# days", "input_mask": "##.#", "placeholder_text": "Enter entitlement", "auto_complete": false, "read_only": false, "validation_display": "inline", "help_text_position": "below", "label": "Total Leave Entitlement", "required_indicator": false, "natural_language": "Entity: Employee\nAttribute: totalLeaveEntitlement\nControl Type: number_input\nLabel: Total Leave Entitlement\nDisplay Format: ##.# days\nInput Mask: ##.#\nPlaceholder Text: Enter entitlement\nValidation Display: inline\nHelp Text Position: below", "version": 6, "status": "deployed_to_production", "created_at": "2025-06-23T09:00:43.903023", "updated_at": "2025-06-24T11:43:35.755467", "created_by": "<PERSON><PERSON>", "updated_by": "<PERSON><PERSON>", "ui_property_status": "existing", "changes_detected": []}], "total_postgres": 2, "total_drafts": 2, "operation": "fetch"}, "status": "success"}