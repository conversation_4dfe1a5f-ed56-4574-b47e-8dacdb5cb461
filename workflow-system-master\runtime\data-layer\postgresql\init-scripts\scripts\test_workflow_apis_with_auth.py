#!/usr/bin/env python3
"""
Workflow APIs Authentication Test Script

This script demonstrates how to use authentication tokens with the workflow APIs:
1. User Registration
2. <PERSON><PERSON> to get access token
3. Test workflow APIs with authentication token
4. Test workflow APIs without authentication (error case)

Usage:
    python test_workflow_apis_with_auth.py
"""

import requests
import json
import uuid
import sys

# Configuration
BASE_URL = "http://localhost:8000/api/v1"
USERNAME = f"testuser_{uuid.uuid4().hex[:8]}"  # Generate a unique username
PASSWORD = "password123"
EMAIL = f"{USERNAME}@example.com"

def print_separator(title):
    """Print a separator with a title."""
    print("\n" + "=" * 80)
    print(f" {title} ".center(80, "="))
    print("=" * 80 + "\n")

def print_response(response):
    """Print the response details."""
    print(f"Status Code: {response.status_code}")
    print(f"Headers: {json.dumps(dict(response.headers), indent=2)}")
    
    try:
        print(f"Response: {json.dumps(response.json(), indent=2)}")
    except json.JSONDecodeError:
        print(f"Response: {response.text}")
    
    print()

def register_user():
    """Register a new user."""
    print_separator("1. User Registration")
    
    url = f"{BASE_URL}/auth/auth/register"
    headers = {"Content-Type": "application/json"}
    data = {
        "username": USERNAME,
        "email": EMAIL,
        "password": PASSWORD,
        "first_name": "Test",
        "last_name": "User"
    }
    
    print(f"Request URL: {url}")
    print(f"Request Headers: {json.dumps(headers, indent=2)}")
    print(f"Request Body: {json.dumps(data, indent=2)}")
    
    response = requests.post(url, headers=headers, json=data)
    print_response(response)
    
    if response.status_code != 201:
        print(f"Error registering user: {response.text}")
        return None
    
    return response.json()

def login():
    """Login to get access and refresh tokens."""
    print_separator("2. Login")
    
    url = f"{BASE_URL}/auth/auth/token"
    headers = {"Content-Type": "application/x-www-form-urlencoded"}
    data = {
        "username": USERNAME,
        "password": PASSWORD
    }
    
    print(f"Request URL: {url}")
    print(f"Request Headers: {json.dumps(headers, indent=2)}")
    print(f"Request Body: {data}")
    
    response = requests.post(url, headers=headers, data=data)
    print_response(response)
    
    if response.status_code != 200:
        print(f"Error logging in: {response.text}")
        return None
    
    return response.json()

def get_global_objectives(access_token=None):
    """Get global objectives with or without authentication."""
    print_separator("3. Get Global Objectives")
    
    url = f"{BASE_URL}/global-objectives/?tenant_id=t001"
    headers = {}
    
    if access_token:
        headers["Authorization"] = f"Bearer {access_token}"
        print("With Authentication Token")
    else:
        print("Without Authentication Token (Error Case)")
    
    print(f"Request URL: {url}")
    print(f"Request Headers: {json.dumps(headers, indent=2)}")
    
    response = requests.get(url, headers=headers)
    print_response(response)
    
    return response.json() if response.status_code == 200 else None

def create_workflow_instance(access_token=None):
    """Create a workflow instance with or without authentication."""
    print_separator("4. Create Workflow Instance")
    
    url = f"{BASE_URL}/workflows/instances"
    headers = {"Content-Type": "application/json"}
    
    if access_token:
        headers["Authorization"] = f"Bearer {access_token}"
        print("With Authentication Token")
    else:
        print("Without Authentication Token (Error Case)")
    
    data = {
        "go_id": "go002",  # Using the Audit Data Management objective
        "tenant_id": "t001",
        "user_id": "user-001",
        "initial_data": {
            "use_ai_assistance": True
        }
    }
    
    print(f"Request URL: {url}")
    print(f"Request Headers: {json.dumps(headers, indent=2)}")
    print(f"Request Body: {json.dumps(data, indent=2)}")
    
    response = requests.post(url, headers=headers, json=data)
    print_response(response)
    
    return response.json() if response.status_code == 200 else None

def start_workflow_instance(instance_id, access_token=None):
    """Start a workflow instance with or without authentication."""
    print_separator("5. Start Workflow Instance")
    
    url = f"{BASE_URL}/workflows/instances/{instance_id}/start"
    headers = {"Content-Type": "application/json"}
    
    if access_token:
        headers["Authorization"] = f"Bearer {access_token}"
        print("With Authentication Token")
    else:
        print("Without Authentication Token (Error Case)")
    
    data = {
        "user_id": "user-001"
    }
    
    print(f"Request URL: {url}")
    print(f"Request Headers: {json.dumps(headers, indent=2)}")
    print(f"Request Body: {json.dumps(data, indent=2)}")
    
    response = requests.post(url, headers=headers, json=data)
    print_response(response)
    
    return response.json() if response.status_code == 200 else None

def fetch_workflow_inputs(instance_id, access_token=None):
    """Fetch workflow inputs with or without authentication."""
    print_separator("6. Fetch Workflow Inputs")
    
    url = f"{BASE_URL}/workflows/instances/{instance_id}/inputs"
    headers = {}
    
    if access_token:
        headers["Authorization"] = f"Bearer {access_token}"
        print("With Authentication Token")
    else:
        print("Without Authentication Token (Error Case)")
    
    print(f"Request URL: {url}")
    print(f"Request Headers: {json.dumps(headers, indent=2)}")
    
    response = requests.get(url, headers=headers)
    print_response(response)
    
    return response.json() if response.status_code == 200 else None

def execute_workflow_step(instance_id, input_data, access_token=None):
    """Execute a workflow step with or without authentication."""
    print_separator("7. Execute Workflow Step")
    
    url = f"{BASE_URL}/workflows/instances/{instance_id}/execute"
    headers = {"Content-Type": "application/json"}
    
    if access_token:
        headers["Authorization"] = f"Bearer {access_token}"
        print("With Authentication Token")
    else:
        print("Without Authentication Token (Error Case)")
    
    data = {
        "input_data": input_data,
        "user_id": "user-001"
    }
    
    print(f"Request URL: {url}")
    print(f"Request Headers: {json.dumps(headers, indent=2)}")
    print(f"Request Body: {json.dumps(data, indent=2)}")
    
    response = requests.post(url, headers=headers, json=data)
    print_response(response)
    
    return response.json() if response.status_code == 200 else None

def main():
    """Main function."""
    print_separator("Workflow APIs Authentication Test")
    print(f"Testing with username: {USERNAME}")
    print(f"Testing with email: {EMAIL}")
    
    # 1. Register a new user
    user = register_user()
    if not user:
        sys.exit(1)
    
    # 2. Login to get access token
    tokens = login()
    if not tokens:
        sys.exit(1)
    
    access_token = tokens["access_token"]
    
    # 3. Test workflow APIs with authentication token
    
    # 3.1 Get global objectives with authentication
    objectives = get_global_objectives(access_token)
    
    # 3.2 Get global objectives without authentication (error case)
    get_global_objectives()
    
    # 3.3 Create workflow instance with authentication
    instance = create_workflow_instance(access_token)
    if not instance:
        print("Failed to create workflow instance. Skipping remaining tests.")
        sys.exit(1)
    
    instance_id = instance["instance_id"]
    
    # 3.4 Create workflow instance without authentication (error case)
    create_workflow_instance()
    
    # 3.5 Start workflow instance with authentication
    started_instance = start_workflow_instance(instance_id, access_token)
    if not started_instance:
        print("Failed to start workflow instance. Skipping remaining tests.")
        sys.exit(1)
    
    # 3.6 Start workflow instance without authentication (error case)
    start_workflow_instance(instance_id)
    
    # 3.7 Fetch workflow inputs with authentication
    inputs = fetch_workflow_inputs(instance_id, access_token)
    if not inputs:
        print("Failed to fetch workflow inputs. Skipping remaining tests.")
        sys.exit(1)
    
    # 3.8 Fetch workflow inputs without authentication (error case)
    fetch_workflow_inputs(instance_id)
    
    # 3.9 Execute workflow step with authentication
    input_data = {}
    for field in inputs.get("input_fields", []):
        if field["required"]:
            if field["data_type"] == "String":
                input_data[field["display_name"]] = "Test Value"
            elif field["data_type"] == "DateTime":
                input_data[field["display_name"]] = "2025-04-27T06:00:00Z"
            elif field["data_type"] == "Enum" and field.get("allowed_values"):
                input_data[field["display_name"]] = field["allowed_values"][0]
    
    execute_workflow_step(instance_id, input_data, access_token)
    
    # 3.10 Execute workflow step without authentication (error case)
    execute_workflow_step(instance_id, input_data)
    
    print_separator("Workflow APIs Authentication Test Completed Successfully")

if __name__ == "__main__":
    main()
