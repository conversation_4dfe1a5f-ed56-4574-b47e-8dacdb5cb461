#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to check the validation constraints for the Employee entity in the database.
"""

import logging
from deployers.entity_deployer_v2 import execute_query

# Set up logging
logging.basicConfig(level=logging.INFO, 
                   format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger('check_employee_validations')

def check_employee_validations():
    """
    Check the validation constraints for the Employee entity in the database.
    """
    schema_name = 'workflow_temp'
    
    # Get the Employee entity ID from the database
    success, messages, result = execute_query(
        f"SELECT entity_id FROM {schema_name}.entities WHERE name = 'Employee'",
        schema_name=schema_name
    )
    
    if not success or not result:
        logger.error("Employee entity not found in the database")
        return
    
    employee_id = result[0][0]
    logger.info(f"Found Employee entity in database with ID: {employee_id}")
    
    # Get the entity table name
    entity_num = employee_id[1:]  # Remove 'E' prefix
    table_name = f"e{entity_num}_employee"
    
    # Check if the table exists
    success, messages, result = execute_query(
        f"""
        SELECT EXISTS (
            SELECT FROM information_schema.tables 
            WHERE table_schema = %s
            AND table_name = %s
        )
        """,
        (schema_name, table_name),
        schema_name
    )
    
    if not success or not result or not result[0][0]:
        logger.error(f"Employee table '{table_name}' not found in the database")
        return
    
    logger.info(f"Found Employee table '{table_name}' in the database")
    
    # Get all constraints for the table
    success, messages, result = execute_query(
        f"""
        SELECT 
            tc.constraint_name, 
            tc.constraint_type,
            ccu.column_name,
            pgc.confrelid::regclass AS referenced_table,
            pgc.confkey AS referenced_columns,
            pg_get_constraintdef(pgc.oid) AS constraint_definition
        FROM 
            information_schema.table_constraints tc
        LEFT JOIN 
            information_schema.constraint_column_usage ccu
            ON tc.constraint_name = ccu.constraint_name
            AND tc.table_schema = ccu.table_schema
        LEFT JOIN 
            pg_constraint pgc
            ON tc.constraint_name = pgc.conname
        WHERE 
            tc.table_schema = %s
            AND tc.table_name = %s
        ORDER BY 
            tc.constraint_type, 
            tc.constraint_name
        """,
        (schema_name, table_name),
        schema_name
    )
    
    if success and result:
        logger.info("\nEmployee validation constraints in the database:")
        logger.info("=" * 80)
        logger.info(f"{'Constraint Name':<30} {'Type':<15} {'Column':<15} {'Definition'}")
        logger.info("-" * 80)
        for row in result:
            constraint_name = row[0]
            constraint_type = row[1]
            column_name = row[2] if row[2] else "N/A"
            constraint_def = row[5] if row[5] else "N/A"
            logger.info(f"{constraint_name:<30} {constraint_type:<15} {column_name:<15} {constraint_def}")
        logger.info("=" * 80)
    else:
        logger.warning("No constraints found for the Employee table")
    
    # Get the table definition
    success, messages, result = execute_query(
        f"""
        SELECT 
            column_name, 
            data_type, 
            is_nullable, 
            column_default
        FROM 
            information_schema.columns
        WHERE 
            table_schema = %s
            AND table_name = %s
        ORDER BY 
            ordinal_position
        """,
        (schema_name, table_name),
        schema_name
    )
    
    if success and result:
        logger.info("\nEmployee table columns:")
        logger.info("=" * 80)
        logger.info(f"{'Column Name':<20} {'Data Type':<15} {'Nullable':<10} {'Default Value'}")
        logger.info("-" * 80)
        for row in result:
            column_name = row[0]
            data_type = row[1]
            is_nullable = row[2]
            column_default = row[3] if row[3] else "NULL"
            logger.info(f"{column_name:<20} {data_type:<15} {is_nullable:<10} {column_default}")
        logger.info("=" * 80)
    else:
        logger.warning("No columns found for the Employee table")
    
    # Check if there's an entity_validations table
    success, messages, result = execute_query(
        f"""
        SELECT EXISTS (
            SELECT FROM information_schema.tables 
            WHERE table_schema = %s
            AND table_name = 'entity_validations'
        )
        """,
        (schema_name,),
        schema_name
    )
    
    if success and result and result[0][0]:
        # Get validations from the entity_validations table
        success, messages, result = execute_query(
            f"""
            SELECT 
                validation_id, 
                name, 
                attribute, 
                constraint_type, 
                error_message
            FROM 
                {schema_name}.entity_validations
            WHERE 
                entity_id = %s
            """,
            (employee_id,),
            schema_name
        )
        
        if success and result:
            logger.info("\nEmployee validations in the entity_validations table:")
            logger.info("=" * 80)
            logger.info(f"{'Validation ID':<20} {'Name':<40} {'Attribute':<15} {'Constraint Type':<20} {'Error Message'}")
            logger.info("-" * 80)
            for row in result:
                validation_id = row[0]
                name = row[1]
                attribute = row[2]
                constraint_type = row[3] if row[3] else "N/A"
                error_message = row[4] if row[4] else "N/A"
                logger.info(f"{validation_id:<20} {name:<40} {attribute:<15} {constraint_type:<20} {error_message}")
            logger.info("=" * 80)
        else:
            logger.warning("No validations found for the Employee entity in the entity_validations table")
    else:
        logger.warning("entity_validations table not found in the database")

if __name__ == "__main__":
    check_employee_validations()
