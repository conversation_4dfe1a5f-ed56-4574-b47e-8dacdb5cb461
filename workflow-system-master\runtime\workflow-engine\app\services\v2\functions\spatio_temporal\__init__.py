"""
Spatio-Temporal Functions Module

This module contains functions related to space and time operations including:
- current_timestamp: Get current timestamp
- add_days: Add days to a date
- subtract_days: Subtract days from a date
- format_date: Format date strings
- calculate_duration: Calculate duration between dates
- timezone_convert: Convert between timezones

Future functions to be added:
- add_hours, add_minutes, add_seconds
- subtract_hours, subtract_minutes, subtract_seconds
- date_diff: Calculate difference between dates
- business_days: Calculate business days between dates
- is_weekend: Check if date is weekend
- is_holiday: Check if date is holiday
- spatial functions for location-based operations
"""

from .current_timestamp import CurrentTimestampFunction

__all__ = [
    'CurrentTimestampFunction'
]
