"""
Core utility functions for the workflow engine.
This module contains base utility functions used throughout the application.
"""
from typing import Dict, Any, List, Optional, Union
from app.services.function_repository import function_repository

# Core utility functions
@function_repository.register("core", "success")
def success(data: Dict[str, Any] = None, message: str = "Operation successful") -> Dict[str, Any]:
    """Return a standardized success response."""
    return {
        "success": True,
        "message": message,
        "data": data or {}
    }

@function_repository.register("core", "error")
def error(message: str = "Operation failed", error_code: str = "ERROR", data: Dict[str, Any] = None) -> Dict[str, Any]:
    """Return a standardized error response."""
    return {
        "success": False,
        "message": message,
        "error_code": error_code,
        "data": data or {}
    }

@function_repository.register("core", "validate")
def validate(data: Dict[str, Any], rules: Dict[str, Any]) -> Dict[str, Any]:
    """Validate data against a set of rules."""
    errors = {}
    for field, rule in rules.items():
        if rule.get("required", False) and (field not in data or data[field] is None or data[field] == ""):
            errors[field] = f"{field} is required"
        
        if field in data and data[field] is not None and data[field] != "":
            if "type" in rule:
                if rule["type"] == "str" and not isinstance(data[field], str):
                    errors[field] = f"{field} must be a string"
                elif rule["type"] == "int" and not isinstance(data[field], int):
                    errors[field] = f"{field} must be an integer"
                elif rule["type"] == "float" and not isinstance(data[field], (int, float)):
                    errors[field] = f"{field} must be a number"
                elif rule["type"] == "bool" and not isinstance(data[field], bool):
                    errors[field] = f"{field} must be a boolean"
                elif rule["type"] == "list" and not isinstance(data[field], list):
                    errors[field] = f"{field} must be a list"
                elif rule["type"] == "dict" and not isinstance(data[field], dict):
                    errors[field] = f"{field} must be an object"
    
    return {
        "valid": len(errors) == 0,
        "errors": errors
    }

print("✅ Core functions module loaded successfully")
