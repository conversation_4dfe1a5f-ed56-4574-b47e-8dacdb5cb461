#!/usr/bin/env python3
"""
Script to deploy Employee validation rules to the database.
"""

import os
import logging
from parsers.entity_parser import parse_entities
from deployers.entity_deployer_v2 import execute_query

# Set up logging
logging.basicConfig(level=logging.INFO, 
                   format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
                   handlers=[
                       logging.FileHandler('/home/<USER>/workflow-system/chat-yaml-builder/v2/validation_deployment.log'),
                       logging.StreamHandler()
                   ])
logger = logging.getLogger('deploy_employee_validations')

def deploy_employee_validations():
    """
    Deploy Employee validation rules to the database.
    """
    # Read the sample entity output file
    sample_file_path = '/home/<USER>/workflow-system/chat-yaml-builder/v2/samples/sample_entity_output2.txt'
    with open(sample_file_path, 'r') as f:
        entity_def = f.read()
    
    # Parse the entity definition
    entities_data, warnings = parse_entities(entity_def)
    
    # Print any warnings
    if warnings:
        logger.warning("Warnings during parsing:")
        for warning in warnings:
            logger.warning(f"  - {warning}")
    
    # Check if Employee entity was parsed
    if 'entities' in entities_data and 'Employee' in entities_data['entities']:
        employee_entity = entities_data['entities']['Employee']
        
        # Check if validations were parsed
        if 'validations' in employee_entity:
            logger.info("\nEmployee validations:")
            for val_name, val_def in employee_entity['validations'].items():
                logger.info(f"\n  - Validation: {val_name}")
                for prop_name, prop_value in val_def.items():
                    logger.info(f"    - {prop_name}: {prop_value}")
        else:
            logger.warning("No validations found in Employee entity")
            return
    else:
        logger.error("Employee entity not found in parsed data")
        return
    
    # Get the Employee entity ID from the database
    schema_name = 'workflow_temp'
    success, messages, result = execute_query(
        f"SELECT entity_id FROM {schema_name}.entities WHERE name = 'Employee'",
        schema_name=schema_name
    )
    
    if not success or not result:
        logger.error("Employee entity not found in the database")
        return
    
    employee_id = result[0][0]
    logger.info(f"Found Employee entity in database with ID: {employee_id}")
    
    # Get the entity table name
    entity_num = employee_id[1:]  # Remove 'E' prefix
    table_name = f"e{entity_num}_employee"
    
    # Check if the table exists
    success, messages, result = execute_query(
        f"""
        SELECT EXISTS (
            SELECT FROM information_schema.tables 
            WHERE table_schema = %s
            AND table_name = %s
        )
        """,
        (schema_name, table_name),
        schema_name
    )
    
    if not success or not result or not result[0][0]:
        logger.error(f"Employee table '{table_name}' not found in the database")
        return
    
    logger.info(f"Found Employee table '{table_name}' in the database")
    
    # Deploy the validations to the database
    validations = employee_entity['validations']
    
    # 1. email must be unique
    if 'email_be_unique' in validations:
        # Check if the unique constraint already exists
        success, messages, result = execute_query(
            f"""
            SELECT EXISTS (
                SELECT FROM information_schema.table_constraints
                WHERE table_schema = %s
                AND table_name = %s
                AND constraint_name = 'unique_email'
            )
            """,
            (schema_name, table_name),
            schema_name
        )
        
        if success and result and not result[0][0]:
            # Create the unique constraint
            success, messages, result = execute_query(
                f"""
                ALTER TABLE {schema_name}."{table_name}"
                ADD CONSTRAINT unique_email UNIQUE (email)
                """,
                schema_name=schema_name
            )
            
            if success:
                logger.info("Created unique constraint for email")
            else:
                logger.error(f"Failed to create unique constraint for email: {messages}")
        else:
            logger.info("Unique constraint for email already exists")
    
    # 2. email must match pattern "^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$"
    # This validation requires a CHECK constraint
    if 'email_match_pattern_"^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$"' in validations:
        # Check if the check constraint already exists
        success, messages, result = execute_query(
            f"""
            SELECT EXISTS (
                SELECT FROM information_schema.table_constraints
                WHERE table_schema = %s
                AND table_name = %s
                AND constraint_name = 'check_email_pattern'
            )
            """,
            (schema_name, table_name),
            schema_name
        )
        
        if success and result and not result[0][0]:
            # Create the check constraint with a simpler pattern
            success, messages, result = execute_query(
                f"""
                ALTER TABLE {schema_name}."{table_name}"
                ADD CONSTRAINT check_email_pattern CHECK (email ~ '.*@.*\\..*')
                """,
                schema_name=schema_name
            )
            
            if success:
                logger.info("Created check constraint for email pattern")
            else:
                logger.error(f"Failed to create check constraint for email pattern: {messages}")
        else:
            logger.info("Check constraint for email pattern already exists")
    
    # 3. hireDate must be before current date
    if 'hireDate_be_before_current_date' in validations:
        # Check if the check constraint already exists
        success, messages, result = execute_query(
            f"""
            SELECT EXISTS (
                SELECT FROM information_schema.table_constraints
                WHERE table_schema = %s
                AND table_name = %s
                AND constraint_name = 'check_hiredate_before_current_date'
            )
            """,
            (schema_name, table_name),
            schema_name
        )
        
        if success and result and not result[0][0]:
            # Create the check constraint with type casting
            success, messages, result = execute_query(
                f"""
                ALTER TABLE {schema_name}."{table_name}"
                ADD CONSTRAINT check_hiredate_before_current_date CHECK (CAST(hiredate AS DATE) <= CURRENT_DATE)
                """,
                schema_name=schema_name
            )
            
            if success:
                logger.info("Created check constraint for hireDate before current date")
            else:
                logger.error(f"Failed to create check constraint for hireDate before current date: {messages}")
        else:
            logger.info("Check constraint for hireDate before current date already exists")
    
    # 4. salary must be greater than 0
    if 'salary_be_greater_than_0' in validations:
        # Check if the check constraint already exists
        success, messages, result = execute_query(
            f"""
            SELECT EXISTS (
                SELECT FROM information_schema.table_constraints
                WHERE table_schema = %s
                AND table_name = %s
                AND constraint_name = 'check_salary_greater_than_0'
            )
            """,
            (schema_name, table_name),
            schema_name
        )
        
        if success and result and not result[0][0]:
            # Create the check constraint with type casting
            success, messages, result = execute_query(
                f"""
                ALTER TABLE {schema_name}."{table_name}"
                ADD CONSTRAINT check_salary_greater_than_0 CHECK (CAST(salary AS NUMERIC) > 0)
                """,
                schema_name=schema_name
            )
            
            if success:
                logger.info("Created check constraint for salary greater than 0")
            else:
                logger.error(f"Failed to create check constraint for salary greater than 0: {messages}")
        else:
            logger.info("Check constraint for salary greater than 0 already exists")
    
    # Verify the constraints were created
    success, messages, result = execute_query(
        f"""
        SELECT constraint_name, constraint_type
        FROM information_schema.table_constraints
        WHERE table_schema = %s
        AND table_name = %s
        AND constraint_name IN (
            'unique_email',
            'check_email_pattern',
            'check_hiredate_before_current_date',
            'check_salary_greater_than_0'
        )
        """,
        (schema_name, table_name),
        schema_name
    )
    
    if success and result:
        logger.info("\nEmployee validation constraints in the database:")
        for row in result:
            constraint_name = row[0]
            constraint_type = row[1]
            logger.info(f"  - {constraint_name}: {constraint_type}")
    else:
        logger.warning("No Employee validation constraints found in the database")
    
    # Store the validations in the entity_validations table if it exists
    success, messages, result = execute_query(
        f"""
        SELECT EXISTS (
            SELECT FROM information_schema.tables 
            WHERE table_schema = %s
            AND table_name = 'entity_validations'
        )
        """,
        (schema_name,),
        schema_name
    )
    
    if success and result and result[0][0]:
        logger.info("Found entity_validations table, storing validations")
        
        # Get the validation IDs
        for val_name, val_def in validations.items():
            if val_name in ['email_be_unique', 'email_match_pattern_"^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$"', 'hireDate_be_before_current_date', 'salary_be_greater_than_0']:
                # Check if the validation already exists
                success, messages, result = execute_query(
                    f"""
                    SELECT validation_id FROM {schema_name}.entity_validations
                    WHERE entity_id = %s AND name = %s
                    """,
                    (employee_id, val_name),
                    schema_name
                )
                
                if success and result:
                    # Validation already exists, update it
                    validation_id = result[0][0]
                    logger.info(f"Validation '{val_name}' already exists with ID {validation_id}, updating it")
                    
                    success, messages, result = execute_query(
                        f"""
                        UPDATE {schema_name}.entity_validations
                        SET attribute = %s, constraint_type = %s, error_message = %s
                        WHERE validation_id = %s
                        """,
                        (val_def.get('attribute', ''), val_def.get('constraint', ''), val_def.get('error_message', ''), validation_id),
                        schema_name
                    )
                    
                    if success:
                        logger.info(f"Updated validation '{val_name}'")
                    else:
                        logger.error(f"Failed to update validation '{val_name}': {messages}")
                else:
                    # Validation doesn't exist, create it
                    # Generate new validation ID
                    success, messages, result = execute_query(
                        f"""
                        SELECT validation_id FROM {schema_name}.entity_validations
                        WHERE entity_id = %s
                        ORDER BY validation_id DESC
                        LIMIT 1
                        """,
                        (employee_id,),
                        schema_name
                    )
                    
                    validation_id = None
                    if success and result:
                        # Extract numeric part and increment
                        val_id = result[0][0]
                        if val_id.startswith(f'{employee_id}.Val'):
                            num = int(val_id.split('Val')[1]) + 1
                            validation_id = f"{employee_id}.Val{num}"
                    
                    if not validation_id:
                        # If no validations exist for this entity, create a new one with entity_id prefix
                        validation_id = f"{employee_id}.Val1"
                    
                    logger.info(f"Creating new validation '{val_name}' with ID {validation_id}")
                    
                    success, messages, result = execute_query(
                        f"""
                        INSERT INTO {schema_name}.entity_validations (
                            validation_id, entity_id, name, attribute, constraint_type, error_message,
                            created_at, updated_at
                        ) VALUES (%s, %s, %s, %s, %s, %s, NOW(), NOW())
                        """,
                        (validation_id, employee_id, val_name, val_def.get('attribute', ''), val_def.get('constraint', ''), val_def.get('error_message', '')),
                        schema_name
                    )
                    
                    if success:
                        logger.info(f"Created validation '{val_name}'")
                    else:
                        logger.error(f"Failed to create validation '{val_name}': {messages}")
    else:
        logger.warning("entity_validations table not found, skipping validation storage")

if __name__ == "__main__":
    deploy_employee_validations()
