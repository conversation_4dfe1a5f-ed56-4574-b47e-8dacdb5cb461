#!/usr/bin/env python3

# This script fixes the syntax error in insert_generator.py
# The error is an incomplete try block at line 706

import os
import re

def fix_insert_generator():
    file_path = "insert_generator.py"
    backup_path = "insert_generator.py.bak"
    
    # Create a backup of the original file
    if not os.path.exists(backup_path):
        with open(file_path, 'r') as src, open(backup_path, 'w') as dst:
            dst.write(src.read())
        print(f"Created backup at {backup_path}")
    
    # Read the file content
    with open(file_path, 'r') as f:
        content = f.readlines()
    
    # Fix the file by properly indenting the try-except blocks
    fixed_content = []
    i = 0
    while i < len(content):
        line = content[i]
        
        # Check for try blocks without proper indentation
        if re.match(r'^try:\s*$', line.strip()):
            # Get the indentation level
            indent = re.match(r'^(\s*)', line).group(1)
            
            # Check if the next line is properly indented
            if i + 1 < len(content):
                next_line = content[i + 1]
                next_indent = re.match(r'^(\s*)', next_line).group(1)
                
                # If the next line is not indented more than the try line, it's an error
                if len(next_indent) <= len(indent) and not next_line.strip().startswith('except'):
                    # Add the try line
                    fixed_content.append(line)
                    # Add a proper except block
                    fixed_content.append(f"{indent}    pass  # Added by fix script\n")
                    fixed_content.append(f"{indent}except Exception as e:\n")
                    fixed_content.append(f"{indent}    log(f\"Error: {{e}}\")\n")
                    fixed_content.append(f"{indent}    traceback.print_exc()\n\n")
                else:
                    # The try block is properly formatted, keep it as is
                    fixed_content.append(line)
            else:
                # This is the last line, add a proper except block
                fixed_content.append(line)
                fixed_content.append(f"{indent}    pass  # Added by fix script\n")
                fixed_content.append(f"{indent}except Exception as e:\n")
                fixed_content.append(f"{indent}    log(f\"Error: {{e}}\")\n")
                fixed_content.append(f"{indent}    traceback.print_exc()\n")
        else:
            # Keep the line as is
            fixed_content.append(line)
        
        i += 1
    
    # Write the fixed content back to the file
    with open(file_path, 'w') as f:
        f.writelines(fixed_content)
    
    print(f"Fixed syntax errors in {file_path}")

if __name__ == "__main__":
    os.chdir(os.path.dirname(os.path.abspath(__file__)))
    fix_insert_generator()
