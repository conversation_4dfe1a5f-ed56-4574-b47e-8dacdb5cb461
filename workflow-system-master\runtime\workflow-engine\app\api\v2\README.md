# API v2 Documentation

## Overview

The v2 API implements a microservices architecture where each API endpoint is organized into separate service modules for better maintainability, scalability, and separation of concerns.

## Architecture

```
app/api/v2/
├── __init__.py                 # Package initialization
├── api.py                      # Main v2 router
├── README.md                   # This documentation
└── auth/                       # Authentication microservice
    ├── __init__.py            # Service initialization
    ├── models.py              # Pydantic models
    ├── service.py             # Business logic
    └── routes.py              # FastAPI routes
```

## Authentication Service

### Register API

**Endpoint:** `POST /api/v2/auth/register`

**Description:** Register a new user in the system with the provided information.

#### Request Model

```json
{
  "username": "string (3-50 chars, required)",
  "email": "string (valid email, required)",
  "password": "string (min 8 chars, required)",
  "first_name": "string (optional, max 100 chars)",
  "last_name": "string (optional, max 100 chars)",
  "roles": ["string"] (optional, default: ["User"]),
  "tenant_id": "string (optional)"
}
```

#### Response Model

```json
{
  "user_id": "string (UUID)",
  "username": "string",
  "email": "string",
  "first_name": "string",
  "last_name": "string",
  "status": "string (default: active)",
  "roles": ["string"],
  "tenant_id": "string",
  "disabled": "boolean (default: false)",
  "created_at": "datetime"
}
```

#### Example Request

```bash
curl -X POST "http://localhost:8000/api/v2/auth/register" \
  -H "Content-Type: application/json" \
  -d '{
    "username": "johndoe",
    "email": "<EMAIL>",
    "password": "secure123",
    "first_name": "John",
    "last_name": "Doe",
    "roles": ["Administrator"],
    "tenant_id": "t001"
  }'
```

#### Example Response

```json
{
  "user_id": "0be32e4c-a2d6-40f5-8958-f312ddf7dd45",
  "username": "johndoe",
  "email": "<EMAIL>",
  "first_name": "John",
  "last_name": "Doe",
  "status": "active",
  "roles": ["Administrator"],
  "tenant_id": "t001",
  "disabled": false,
  "created_at": "2025-05-24T15:16:00Z"
}
```

#### Error Responses

- **400 Bad Request:** Invalid input data
- **409 Conflict:** User with username or email already exists
- **500 Internal Server Error:** Server error during registration

### Get User API

**Endpoint:** `GET /api/v2/auth/user/{user_id}`

**Description:** Retrieve user information by user ID.

#### Response Model

Same as Register API response model.

#### Example Request

```bash
curl -X GET "http://localhost:8000/api/v2/auth/user/0be32e4c-a2d6-40f5-8958-f312ddf7dd45"
```

## Database Tables Used

The v2 Register API uses the following database tables:

### workflow_runtime.users
- `user_id` (VARCHAR) - Primary key, UUID
- `username` (VARCHAR) - Unique username
- `email` (VARCHAR) - User email address
- `password_hash` (VARCHAR) - Hashed password
- `first_name` (VARCHAR) - User's first name
- `last_name` (VARCHAR) - User's last name
- `status` (VARCHAR) - User status (active/inactive)
- `created_at` (TIMESTAMP) - Creation timestamp
- `updated_at` (TIMESTAMP) - Last update timestamp
- `last_login` (TIMESTAMP) - Last login timestamp

### workflow_runtime.user_roles
- `user_id` (VARCHAR) - Foreign key to users table
- `username` (VARCHAR) - Username
- `role` (VARCHAR) - Role name
- `tenant_id` (VARCHAR) - Tenant identifier

### workflow_runtime.tenants
- `tenant_id` (VARCHAR) - Primary key, tenant identifier
- `name` (VARCHAR) - Tenant name
- `created_at` (TIMESTAMP) - Creation timestamp
- `updated_at` (TIMESTAMP) - Last update timestamp

## Key Features

### 1. Microservices Architecture
- Each service is self-contained with its own models, business logic, and routes
- Easy to maintain and extend
- Clear separation of concerns

### 2. Simplified User Management
- Removed dependency on `user_organizations` table as requested
- Streamlined user registration process
- Focus on core user attributes and roles

### 3. Comprehensive Error Handling
- Detailed error responses with error codes
- Proper HTTP status codes
- Structured error messages

### 4. Input Validation
- Pydantic models for request/response validation
- Field-level validation with appropriate constraints
- Email format validation

### 5. Security
- Password hashing using bcrypt
- UUID-based user IDs
- Proper error handling to prevent information leakage

## Differences from v1

1. **Architecture:** Microservices vs monolithic structure
2. **Organization:** Removed dependency on user_organizations table
3. **Error Handling:** More structured and detailed error responses
4. **Validation:** Enhanced input validation with Pydantic models
5. **Documentation:** Comprehensive API documentation with examples

## Health Check Endpoints

### API Health Check
**Endpoint:** `GET /api/v2/health`

Returns the health status of the v2 API and its services.

### API Information
**Endpoint:** `GET /api/v2/info`

Returns information about the v2 API, available services, and endpoints.

## Testing

To test the v2 Register API:

1. Ensure the database is running and migrated
2. Start the FastAPI application
3. Use the provided curl examples or access the Swagger UI at `/docs`
4. The v2 endpoints will be available under the "Authentication v2" section

## Future Extensions

The microservices architecture makes it easy to add new services:

1. Create a new service directory (e.g., `app/api/v2/users/`)
2. Implement models, service logic, and routes
3. Add the router to `app/api/v2/api.py`
4. Update documentation

This structure supports horizontal scaling and independent deployment of services.
