{"timestamp": "2025-06-23T06:27:30.150489", "endpoint": "fetch/entities", "input": {}, "output": {"success": true, "postgres_entities": [], "mongo_drafts": [{"_id": "685442ebf7fe510cb8237bf5", "entity_id": "E13", "name": "LeaveApplication", "display_name": "Leave Application", "tenant_id": "T1001", "tenant_name": "Alpha Corp", "business_domain": "Human Resources", "category": "Leave Management", "tags": ["leave", "application", "request", "approval"], "archival_strategy": "archive_only", "icon": "file-text", "colour_theme": "blue", "version": 1, "status": "draft", "type": "transaction", "description": "Represents an employee's request for time off from work", "table_name": "e13_LeaveApplication", "natural_language": "Tenant: Alpha Corp\n\nLeaveApplication has leaveId^PK, employeeId^FK, startDate, endDate, numDays[derived], reason, leaveTypeName, leaveSubTypeName, status, requiresDocumentation, documentationProvided, submissionDate, approvalDate, approvedBy^FK, comments, isRetroactive[derived], insufficientBalance[derived], lowTeamAvailability[derived], allowedNumberOfDays.\n\nEntity: LeaveApplication\n- Entity Name: LeaveApplication\n- Display Name: Leave Application\n- Type: transaction\n- Description: Represents an employee's request for time off from work\n- Business Domain: Human Resources\n- Category: Leave Management\n- Tags: leave, application, request, approval\n- Archival Strategy: archive_only\n- Icon: file-text\n- Colour Theme: blue", "created_at": "2025-06-19T17:03:39.677413", "updated_at": "2025-06-19T17:03:39.677423", "created_by": "Tarun", "updated_by": "Tarun", "entity_status": "new", "changes_detected": [], "icon_type": "text", "icon_content": ""}, {"_id": "6854484e00387ff38636d6dc", "entity_id": "E14", "name": "LeaveApplication", "display_name": "Leave Application", "tenant_id": "T1007", "tenant_name": "Acme Corp", "business_domain": "Human Resources", "category": "Leave Management", "tags": ["leave", "application", "request", "approval"], "archival_strategy": "archive_only", "icon": "file-text", "colour_theme": "blue", "version": 1, "status": "draft", "type": "transaction", "description": "Represents an employee's request for time off from work", "table_name": "e14_LeaveApplication", "natural_language": "Tenant: Acme Corp\n\nLeaveApplication has leaveId^PK, employeeId^FK, startDate, endDate, numDays[derived], reason, leaveTypeName, leaveSubTypeName, status, requiresDocumentation, documentationProvided, submissionDate, approvalDate, approvedBy^FK, comments, isRetroactive[derived], insufficientBalance[derived], lowTeamAvailability[derived], allowedNumberOfDays.\n\nEntity: LeaveApplication\n- Entity Name: LeaveApplication\n- Display Name: Leave Application\n- Type: transaction\n- Description: Represents an employee's request for time off from work\n- Business Domain: Human Resources\n- Category: Leave Management\n- Tags: leave, application, request, approval\n- Archival Strategy: archive_only\n- Icon: file-text\n- Colour Theme: blue", "created_at": "2025-06-19T17:26:38.418997", "updated_at": "2025-06-19T17:26:38.419004", "created_by": "Tarun", "updated_by": "Tarun", "entity_status": "new", "changes_detected": [], "icon_type": "text", "icon_content": ""}, {"_id": "68550f7b522219aecde6faed", "entity_id": "E15", "name": "Employee", "display_name": "Employee", "tenant_id": "T2", "tenant_name": "Acme Corp", "business_domain": "Human Resources", "category": "Personnel Management", "tags": ["employee", "personnel", "staff", "hr"], "archival_strategy": "archive_only", "icon": "user", "colour_theme": "green", "version": 1, "status": "draft", "type": "master", "description": "Represents an employee in the organization", "table_name": "e15_Employee", "natural_language": "Tenant: Acme Corp\n\nEmployee has employeeId^PK, firstName, lastName, email, phone, departmentId^FK, managerId^FK, jobTitle, hireDate, employmentStatus, annualLeaveBalance, sickLeaveBalance, personalLeaveBalance, totalLeaveEntitlement.\n\nEntity: Employee\n- Entity Name: Employee\n- Display Name: Employee\n- Type: master\n- Description: Represents an employee in the organization\n- Business Domain: Human Resources\n- Category: Personnel Management\n- Tags: employee, personnel, staff, hr\n- Archival Strategy: archive_only\n- Icon: user\n- Colour Theme: green", "created_at": "2025-06-20T07:36:27.358450", "updated_at": "2025-06-20T07:36:27.358458", "created_by": "Tarun", "updated_by": "Tarun", "entity_status": "new", "changes_detected": [], "icon_type": "text", "icon_content": ""}, {"_id": "68556dd43b82120ee564371f", "entity_id": "E44", "name": "Employee", "display_name": "Employee", "tenant_id": "T1003", "tenant_name": "Alpha Corp", "business_domain": "Human Resources", "category": "Employee Management", "tags": ["employee", "staff", "personnel"], "archival_strategy": "archive_only", "icon": "user", "colour_theme": "green", "version": 1, "status": "draft", "type": "master", "description": "Represents an employee in the organization", "table_name": "e44_Employee", "natural_language": "Tenant: Alpha Corp\n\nEmployee has employeeId^PK, firstName, lastName, email, phone, departmentId^FK, hireDate, annualLeaveBalance, sickLeaveBalance, personalLeaveBalance, totalLeaveEntitlement.\n\nEntity: Employee\n- Entity Name: Employee\n- Display Name: Employee\n- Type: master\n- Description: Represents an employee in the organization\n- Business Domain: Human Resources\n- Category: Employee Management\n- Tags: employee, staff, personnel\n- Archival Strategy: archive_only\n- Icon: user\n- Colour Theme: green", "created_at": "2025-06-20T14:19:00.007410", "updated_at": "2025-06-20T14:19:00.007414", "created_by": "Tarun", "updated_by": "Tarun", "entity_status": "new", "changes_detected": [], "icon_type": "text", "icon_content": ""}], "total_postgres": 0, "total_drafts": 4, "operation": "fetch"}, "status": "success"}
{"timestamp": "2025-06-23T06:27:56.274062", "endpoint": "fetch/entities", "input": {}, "output": {"success": true, "postgres_entities": [], "mongo_drafts": [{"_id": "685442ebf7fe510cb8237bf5", "entity_id": "E13", "name": "LeaveApplication", "display_name": "Leave Application", "tenant_id": "T1001", "tenant_name": "Alpha Corp", "business_domain": "Human Resources", "category": "Leave Management", "tags": ["leave", "application", "request", "approval"], "archival_strategy": "archive_only", "icon": "file-text", "colour_theme": "blue", "version": 1, "status": "draft", "type": "transaction", "description": "Represents an employee's request for time off from work", "table_name": "e13_LeaveApplication", "natural_language": "Tenant: Alpha Corp\n\nLeaveApplication has leaveId^PK, employeeId^FK, startDate, endDate, numDays[derived], reason, leaveTypeName, leaveSubTypeName, status, requiresDocumentation, documentationProvided, submissionDate, approvalDate, approvedBy^FK, comments, isRetroactive[derived], insufficientBalance[derived], lowTeamAvailability[derived], allowedNumberOfDays.\n\nEntity: LeaveApplication\n- Entity Name: LeaveApplication\n- Display Name: Leave Application\n- Type: transaction\n- Description: Represents an employee's request for time off from work\n- Business Domain: Human Resources\n- Category: Leave Management\n- Tags: leave, application, request, approval\n- Archival Strategy: archive_only\n- Icon: file-text\n- Colour Theme: blue", "created_at": "2025-06-19T17:03:39.677413", "updated_at": "2025-06-19T17:03:39.677423", "created_by": "Tarun", "updated_by": "Tarun", "entity_status": "new", "changes_detected": [], "icon_type": "text", "icon_content": ""}, {"_id": "6854484e00387ff38636d6dc", "entity_id": "E14", "name": "LeaveApplication", "display_name": "Leave Application", "tenant_id": "T1007", "tenant_name": "Acme Corp", "business_domain": "Human Resources", "category": "Leave Management", "tags": ["leave", "application", "request", "approval"], "archival_strategy": "archive_only", "icon": "file-text", "colour_theme": "blue", "version": 1, "status": "draft", "type": "transaction", "description": "Represents an employee's request for time off from work", "table_name": "e14_LeaveApplication", "natural_language": "Tenant: Acme Corp\n\nLeaveApplication has leaveId^PK, employeeId^FK, startDate, endDate, numDays[derived], reason, leaveTypeName, leaveSubTypeName, status, requiresDocumentation, documentationProvided, submissionDate, approvalDate, approvedBy^FK, comments, isRetroactive[derived], insufficientBalance[derived], lowTeamAvailability[derived], allowedNumberOfDays.\n\nEntity: LeaveApplication\n- Entity Name: LeaveApplication\n- Display Name: Leave Application\n- Type: transaction\n- Description: Represents an employee's request for time off from work\n- Business Domain: Human Resources\n- Category: Leave Management\n- Tags: leave, application, request, approval\n- Archival Strategy: archive_only\n- Icon: file-text\n- Colour Theme: blue", "created_at": "2025-06-19T17:26:38.418997", "updated_at": "2025-06-19T17:26:38.419004", "created_by": "Tarun", "updated_by": "Tarun", "entity_status": "new", "changes_detected": [], "icon_type": "text", "icon_content": ""}, {"_id": "68550f7b522219aecde6faed", "entity_id": "E15", "name": "Employee", "display_name": "Employee", "tenant_id": "T2", "tenant_name": "Acme Corp", "business_domain": "Human Resources", "category": "Personnel Management", "tags": ["employee", "personnel", "staff", "hr"], "archival_strategy": "archive_only", "icon": "user", "colour_theme": "green", "version": 1, "status": "draft", "type": "master", "description": "Represents an employee in the organization", "table_name": "e15_Employee", "natural_language": "Tenant: Acme Corp\n\nEmployee has employeeId^PK, firstName, lastName, email, phone, departmentId^FK, managerId^FK, jobTitle, hireDate, employmentStatus, annualLeaveBalance, sickLeaveBalance, personalLeaveBalance, totalLeaveEntitlement.\n\nEntity: Employee\n- Entity Name: Employee\n- Display Name: Employee\n- Type: master\n- Description: Represents an employee in the organization\n- Business Domain: Human Resources\n- Category: Personnel Management\n- Tags: employee, personnel, staff, hr\n- Archival Strategy: archive_only\n- Icon: user\n- Colour Theme: green", "created_at": "2025-06-20T07:36:27.358450", "updated_at": "2025-06-20T07:36:27.358458", "created_by": "Tarun", "updated_by": "Tarun", "entity_status": "new", "changes_detected": [], "icon_type": "text", "icon_content": ""}, {"_id": "68556dd43b82120ee564371f", "entity_id": "E44", "name": "Employee", "display_name": "Employee", "tenant_id": "T1003", "tenant_name": "Alpha Corp", "business_domain": "Human Resources", "category": "Employee Management", "tags": ["employee", "staff", "personnel"], "archival_strategy": "archive_only", "icon": "user", "colour_theme": "green", "version": 1, "status": "draft", "type": "master", "description": "Represents an employee in the organization", "table_name": "e44_Employee", "natural_language": "Tenant: Alpha Corp\n\nEmployee has employeeId^PK, firstName, lastName, email, phone, departmentId^FK, hireDate, annualLeaveBalance, sickLeaveBalance, personalLeaveBalance, totalLeaveEntitlement.\n\nEntity: Employee\n- Entity Name: Employee\n- Display Name: Employee\n- Type: master\n- Description: Represents an employee in the organization\n- Business Domain: Human Resources\n- Category: Employee Management\n- Tags: employee, staff, personnel\n- Archival Strategy: archive_only\n- Icon: user\n- Colour Theme: green", "created_at": "2025-06-20T14:19:00.007410", "updated_at": "2025-06-20T14:19:00.007414", "created_by": "Tarun", "updated_by": "Tarun", "entity_status": "new", "changes_detected": [], "icon_type": "text", "icon_content": ""}], "total_postgres": 0, "total_drafts": 4, "operation": "fetch"}, "status": "success"}
{"timestamp": "2025-06-23T06:46:11.277311", "endpoint": "fetch/entities", "input": {}, "output": {"success": true, "postgres_entities": [], "mongo_drafts": [{"_id": "685442ebf7fe510cb8237bf5", "entity_id": "E13", "name": "LeaveApplication", "display_name": "Leave Application", "tenant_id": "T1001", "tenant_name": "Alpha Corp", "business_domain": "Human Resources", "category": "Leave Management", "tags": ["leave", "application", "request", "approval"], "archival_strategy": "archive_only", "icon": "file-text", "colour_theme": "blue", "version": 1, "status": "draft", "type": "transaction", "description": "Represents an employee's request for time off from work", "table_name": "e13_LeaveApplication", "natural_language": "Tenant: Alpha Corp\n\nLeaveApplication has leaveId^PK, employeeId^FK, startDate, endDate, numDays[derived], reason, leaveTypeName, leaveSubTypeName, status, requiresDocumentation, documentationProvided, submissionDate, approvalDate, approvedBy^FK, comments, isRetroactive[derived], insufficientBalance[derived], lowTeamAvailability[derived], allowedNumberOfDays.\n\nEntity: LeaveApplication\n- Entity Name: LeaveApplication\n- Display Name: Leave Application\n- Type: transaction\n- Description: Represents an employee's request for time off from work\n- Business Domain: Human Resources\n- Category: Leave Management\n- Tags: leave, application, request, approval\n- Archival Strategy: archive_only\n- Icon: file-text\n- Colour Theme: blue", "created_at": "2025-06-19T17:03:39.677413", "updated_at": "2025-06-19T17:03:39.677423", "created_by": "Tarun", "updated_by": "Tarun", "entity_status": "new", "changes_detected": [], "icon_type": "text", "icon_content": ""}, {"_id": "6854484e00387ff38636d6dc", "entity_id": "E14", "name": "LeaveApplication", "display_name": "Leave Application", "tenant_id": "T1007", "tenant_name": "Acme Corp", "business_domain": "Human Resources", "category": "Leave Management", "tags": ["leave", "application", "request", "approval"], "archival_strategy": "archive_only", "icon": "file-text", "colour_theme": "blue", "version": 1, "status": "draft", "type": "transaction", "description": "Represents an employee's request for time off from work", "table_name": "e14_LeaveApplication", "natural_language": "Tenant: Acme Corp\n\nLeaveApplication has leaveId^PK, employeeId^FK, startDate, endDate, numDays[derived], reason, leaveTypeName, leaveSubTypeName, status, requiresDocumentation, documentationProvided, submissionDate, approvalDate, approvedBy^FK, comments, isRetroactive[derived], insufficientBalance[derived], lowTeamAvailability[derived], allowedNumberOfDays.\n\nEntity: LeaveApplication\n- Entity Name: LeaveApplication\n- Display Name: Leave Application\n- Type: transaction\n- Description: Represents an employee's request for time off from work\n- Business Domain: Human Resources\n- Category: Leave Management\n- Tags: leave, application, request, approval\n- Archival Strategy: archive_only\n- Icon: file-text\n- Colour Theme: blue", "created_at": "2025-06-19T17:26:38.418997", "updated_at": "2025-06-19T17:26:38.419004", "created_by": "Tarun", "updated_by": "Tarun", "entity_status": "new", "changes_detected": [], "icon_type": "text", "icon_content": ""}, {"_id": "68550f7b522219aecde6faed", "entity_id": "E15", "name": "Employee", "display_name": "Employee", "tenant_id": "T2", "tenant_name": "Acme Corp", "business_domain": "Human Resources", "category": "Personnel Management", "tags": ["employee", "personnel", "staff", "hr"], "archival_strategy": "archive_only", "icon": "user", "colour_theme": "green", "version": 1, "status": "draft", "type": "master", "description": "Represents an employee in the organization", "table_name": "e15_Employee", "natural_language": "Tenant: Acme Corp\n\nEmployee has employeeId^PK, firstName, lastName, email, phone, departmentId^FK, managerId^FK, jobTitle, hireDate, employmentStatus, annualLeaveBalance, sickLeaveBalance, personalLeaveBalance, totalLeaveEntitlement.\n\nEntity: Employee\n- Entity Name: Employee\n- Display Name: Employee\n- Type: master\n- Description: Represents an employee in the organization\n- Business Domain: Human Resources\n- Category: Personnel Management\n- Tags: employee, personnel, staff, hr\n- Archival Strategy: archive_only\n- Icon: user\n- Colour Theme: green", "created_at": "2025-06-20T07:36:27.358450", "updated_at": "2025-06-20T07:36:27.358458", "created_by": "Tarun", "updated_by": "Tarun", "entity_status": "new", "changes_detected": [], "icon_type": "text", "icon_content": ""}, {"_id": "68556dd43b82120ee564371f", "entity_id": "E44", "name": "Employee", "display_name": "Employee", "tenant_id": "T1003", "tenant_name": "Alpha Corp", "business_domain": "Human Resources", "category": "Employee Management", "tags": ["employee", "staff", "personnel"], "archival_strategy": "archive_only", "icon": "user", "colour_theme": "green", "version": 1, "status": "draft", "type": "master", "description": "Represents an employee in the organization", "table_name": "e44_Employee", "natural_language": "Tenant: Alpha Corp\n\nEmployee has employeeId^PK, firstName, lastName, email, phone, departmentId^FK, hireDate, annualLeaveBalance, sickLeaveBalance, personalLeaveBalance, totalLeaveEntitlement.\n\nEntity: Employee\n- Entity Name: Employee\n- Display Name: Employee\n- Type: master\n- Description: Represents an employee in the organization\n- Business Domain: Human Resources\n- Category: Employee Management\n- Tags: employee, staff, personnel\n- Archival Strategy: archive_only\n- Icon: user\n- Colour Theme: green", "created_at": "2025-06-20T14:19:00.007410", "updated_at": "2025-06-20T14:19:00.007414", "created_by": "Tarun", "updated_by": "Tarun", "entity_status": "new", "changes_detected": [], "icon_type": "text", "icon_content": ""}, {"_id": "6858f51c1195bbcbf7aa7692", "entity_id": "E45", "name": "Employee", "display_name": "Employee", "tenant_id": "T1007", "tenant_name": "Acme Corp Test", "business_domain": "Human Resources", "category": "Employee Management", "tags": ["employee", "hr"], "archival_strategy": "archive_only", "icon": "user", "colour_theme": "green", "version": 1, "status": "draft", "type": "master", "description": "Employee information", "table_name": "e45_Employee", "natural_language": "Tenant: Acme Corp Test\n\nEmployee has employeeId^PK, firstName, lastName, employmentStatus.\n\nEntity: Employee\n- Entity Name: Employee\n- Display Name: Employee\n- Type: master\n- Description: Employee information\n- Business Domain: Human Resources\n- Category: Employee Management\n- Tags: employee, hr\n- Archival Strategy: archive_only\n- Icon: user\n- Colour Theme: green", "created_at": "2025-06-23T06:33:00.529304", "updated_at": "2025-06-23T06:33:00.529311", "created_by": "Tarun", "updated_by": "Tarun", "entity_status": "new", "changes_detected": [], "icon_type": "text", "icon_content": ""}], "total_postgres": 0, "total_drafts": 5, "operation": "fetch"}, "status": "success"}
{"timestamp": "2025-06-23T08:53:01.907363", "endpoint": "fetch/entities", "input": {}, "output": {"success": true, "postgres_entities": [], "mongo_drafts": [{"_id": "685442ebf7fe510cb8237bf5", "entity_id": "E13", "name": "LeaveApplication", "display_name": "Leave Application", "tenant_id": "T1001", "tenant_name": "Alpha Corp", "business_domain": "Human Resources", "category": "Leave Management", "tags": ["leave", "application", "request", "approval"], "archival_strategy": "archive_only", "icon": "file-text", "colour_theme": "blue", "version": 1, "status": "draft", "type": "transaction", "description": "Represents an employee's request for time off from work", "table_name": "e13_LeaveApplication", "natural_language": "Tenant: Alpha Corp\n\nLeaveApplication has leaveId^PK, employeeId^FK, startDate, endDate, numDays[derived], reason, leaveTypeName, leaveSubTypeName, status, requiresDocumentation, documentationProvided, submissionDate, approvalDate, approvedBy^FK, comments, isRetroactive[derived], insufficientBalance[derived], lowTeamAvailability[derived], allowedNumberOfDays.\n\nEntity: LeaveApplication\n- Entity Name: LeaveApplication\n- Display Name: Leave Application\n- Type: transaction\n- Description: Represents an employee's request for time off from work\n- Business Domain: Human Resources\n- Category: Leave Management\n- Tags: leave, application, request, approval\n- Archival Strategy: archive_only\n- Icon: file-text\n- Colour Theme: blue", "created_at": "2025-06-19T17:03:39.677413", "updated_at": "2025-06-19T17:03:39.677423", "created_by": "Tarun", "updated_by": "Tarun", "entity_status": "new", "changes_detected": [], "icon_type": "text", "icon_content": ""}, {"_id": "6854484e00387ff38636d6dc", "entity_id": "E14", "name": "LeaveApplication", "display_name": "Leave Application", "tenant_id": "T1007", "tenant_name": "Acme Corp", "business_domain": "Human Resources", "category": "Leave Management", "tags": ["leave", "application", "request", "approval"], "archival_strategy": "archive_only", "icon": "file-text", "colour_theme": "blue", "version": 1, "status": "draft", "type": "transaction", "description": "Represents an employee's request for time off from work", "table_name": "e14_LeaveApplication", "natural_language": "Tenant: Acme Corp\n\nLeaveApplication has leaveId^PK, employeeId^FK, startDate, endDate, numDays[derived], reason, leaveTypeName, leaveSubTypeName, status, requiresDocumentation, documentationProvided, submissionDate, approvalDate, approvedBy^FK, comments, isRetroactive[derived], insufficientBalance[derived], lowTeamAvailability[derived], allowedNumberOfDays.\n\nEntity: LeaveApplication\n- Entity Name: LeaveApplication\n- Display Name: Leave Application\n- Type: transaction\n- Description: Represents an employee's request for time off from work\n- Business Domain: Human Resources\n- Category: Leave Management\n- Tags: leave, application, request, approval\n- Archival Strategy: archive_only\n- Icon: file-text\n- Colour Theme: blue", "created_at": "2025-06-19T17:26:38.418997", "updated_at": "2025-06-19T17:26:38.419004", "created_by": "Tarun", "updated_by": "Tarun", "entity_status": "new", "changes_detected": [], "icon_type": "text", "icon_content": ""}, {"_id": "68550f7b522219aecde6faed", "entity_id": "E15", "name": "Employee", "display_name": "Employee", "tenant_id": "T2", "tenant_name": "Acme Corp", "business_domain": "Human Resources", "category": "Personnel Management", "tags": ["employee", "personnel", "staff", "hr"], "archival_strategy": "archive_only", "icon": "user", "colour_theme": "green", "version": 1, "status": "draft", "type": "master", "description": "Represents an employee in the organization", "table_name": "e15_Employee", "natural_language": "Tenant: Acme Corp\n\nEmployee has employeeId^PK, firstName, lastName, email, phone, departmentId^FK, managerId^FK, jobTitle, hireDate, employmentStatus, annualLeaveBalance, sickLeaveBalance, personalLeaveBalance, totalLeaveEntitlement.\n\nEntity: Employee\n- Entity Name: Employee\n- Display Name: Employee\n- Type: master\n- Description: Represents an employee in the organization\n- Business Domain: Human Resources\n- Category: Personnel Management\n- Tags: employee, personnel, staff, hr\n- Archival Strategy: archive_only\n- Icon: user\n- Colour Theme: green", "created_at": "2025-06-20T07:36:27.358450", "updated_at": "2025-06-20T07:36:27.358458", "created_by": "Tarun", "updated_by": "Tarun", "entity_status": "new", "changes_detected": [], "icon_type": "text", "icon_content": ""}, {"_id": "68556dd43b82120ee564371f", "entity_id": "E44", "name": "Employee", "display_name": "Employee", "tenant_id": "T1003", "tenant_name": "Alpha Corp", "business_domain": "Human Resources", "category": "Employee Management", "tags": ["employee", "staff", "personnel"], "archival_strategy": "archive_only", "icon": "user", "colour_theme": "green", "version": 1, "status": "draft", "type": "master", "description": "Represents an employee in the organization", "table_name": "e44_Employee", "natural_language": "Tenant: Alpha Corp\n\nEmployee has employeeId^PK, firstName, lastName, email, phone, departmentId^FK, hireDate, annualLeaveBalance, sickLeaveBalance, personalLeaveBalance, totalLeaveEntitlement.\n\nEntity: Employee\n- Entity Name: Employee\n- Display Name: Employee\n- Type: master\n- Description: Represents an employee in the organization\n- Business Domain: Human Resources\n- Category: Employee Management\n- Tags: employee, staff, personnel\n- Archival Strategy: archive_only\n- Icon: user\n- Colour Theme: green", "created_at": "2025-06-20T14:19:00.007410", "updated_at": "2025-06-20T14:19:00.007414", "created_by": "Tarun", "updated_by": "Tarun", "entity_status": "new", "changes_detected": [], "icon_type": "text", "icon_content": ""}, {"_id": "6858f51c1195bbcbf7aa7692", "entity_id": "E45", "name": "Employee", "display_name": "Employee", "tenant_id": "T1007", "tenant_name": "Acme Corp Test", "business_domain": "Human Resources", "category": "Employee Management", "tags": ["employee", "hr"], "archival_strategy": "archive_only", "icon": "user", "colour_theme": "green", "version": 1, "status": "draft", "type": "master", "description": "Employee information", "table_name": "e45_Employee", "natural_language": "Tenant: Acme Corp Test\n\nEmployee has employeeId^PK, firstName, lastName, employmentStatus.\n\nEntity: Employee\n- Entity Name: Employee\n- Display Name: Employee\n- Type: master\n- Description: Employee information\n- Business Domain: Human Resources\n- Category: Employee Management\n- Tags: employee, hr\n- Archival Strategy: archive_only\n- Icon: user\n- Colour Theme: green", "created_at": "2025-06-23T06:33:00.529304", "updated_at": "2025-06-23T06:33:00.529311", "created_by": "Tarun", "updated_by": "Tarun", "entity_status": "new", "changes_detected": [], "icon_type": "text", "icon_content": ""}], "total_postgres": 0, "total_drafts": 5, "operation": "fetch"}, "status": "success"}
{"timestamp": "2025-06-23T08:53:26.262404", "endpoint": "fetch/entities", "input": {}, "output": {"success": true, "postgres_entities": [], "mongo_drafts": [{"_id": "685442ebf7fe510cb8237bf5", "entity_id": "E13", "name": "LeaveApplication", "display_name": "Leave Application", "tenant_id": "T1001", "tenant_name": "Alpha Corp", "business_domain": "Human Resources", "category": "Leave Management", "tags": ["leave", "application", "request", "approval"], "archival_strategy": "archive_only", "icon": "file-text", "colour_theme": "blue", "version": 1, "status": "draft", "type": "transaction", "description": "Represents an employee's request for time off from work", "table_name": "e13_LeaveApplication", "natural_language": "Tenant: Alpha Corp\n\nLeaveApplication has leaveId^PK, employeeId^FK, startDate, endDate, numDays[derived], reason, leaveTypeName, leaveSubTypeName, status, requiresDocumentation, documentationProvided, submissionDate, approvalDate, approvedBy^FK, comments, isRetroactive[derived], insufficientBalance[derived], lowTeamAvailability[derived], allowedNumberOfDays.\n\nEntity: LeaveApplication\n- Entity Name: LeaveApplication\n- Display Name: Leave Application\n- Type: transaction\n- Description: Represents an employee's request for time off from work\n- Business Domain: Human Resources\n- Category: Leave Management\n- Tags: leave, application, request, approval\n- Archival Strategy: archive_only\n- Icon: file-text\n- Colour Theme: blue", "created_at": "2025-06-19T17:03:39.677413", "updated_at": "2025-06-19T17:03:39.677423", "created_by": "Tarun", "updated_by": "Tarun", "entity_status": "new", "changes_detected": [], "icon_type": "text", "icon_content": ""}, {"_id": "6854484e00387ff38636d6dc", "entity_id": "E14", "name": "LeaveApplication", "display_name": "Leave Application", "tenant_id": "T1007", "tenant_name": "Acme Corp", "business_domain": "Human Resources", "category": "Leave Management", "tags": ["leave", "application", "request", "approval"], "archival_strategy": "archive_only", "icon": "file-text", "colour_theme": "blue", "version": 1, "status": "draft", "type": "transaction", "description": "Represents an employee's request for time off from work", "table_name": "e14_LeaveApplication", "natural_language": "Tenant: Acme Corp\n\nLeaveApplication has leaveId^PK, employeeId^FK, startDate, endDate, numDays[derived], reason, leaveTypeName, leaveSubTypeName, status, requiresDocumentation, documentationProvided, submissionDate, approvalDate, approvedBy^FK, comments, isRetroactive[derived], insufficientBalance[derived], lowTeamAvailability[derived], allowedNumberOfDays.\n\nEntity: LeaveApplication\n- Entity Name: LeaveApplication\n- Display Name: Leave Application\n- Type: transaction\n- Description: Represents an employee's request for time off from work\n- Business Domain: Human Resources\n- Category: Leave Management\n- Tags: leave, application, request, approval\n- Archival Strategy: archive_only\n- Icon: file-text\n- Colour Theme: blue", "created_at": "2025-06-19T17:26:38.418997", "updated_at": "2025-06-19T17:26:38.419004", "created_by": "Tarun", "updated_by": "Tarun", "entity_status": "new", "changes_detected": [], "icon_type": "text", "icon_content": ""}, {"_id": "68550f7b522219aecde6faed", "entity_id": "E15", "name": "Employee", "display_name": "Employee", "tenant_id": "T2", "tenant_name": "Acme Corp", "business_domain": "Human Resources", "category": "Personnel Management", "tags": ["employee", "personnel", "staff", "hr"], "archival_strategy": "archive_only", "icon": "user", "colour_theme": "green", "version": 1, "status": "draft", "type": "master", "description": "Represents an employee in the organization", "table_name": "e15_Employee", "natural_language": "Tenant: Acme Corp\n\nEmployee has employeeId^PK, firstName, lastName, email, phone, departmentId^FK, managerId^FK, jobTitle, hireDate, employmentStatus, annualLeaveBalance, sickLeaveBalance, personalLeaveBalance, totalLeaveEntitlement.\n\nEntity: Employee\n- Entity Name: Employee\n- Display Name: Employee\n- Type: master\n- Description: Represents an employee in the organization\n- Business Domain: Human Resources\n- Category: Personnel Management\n- Tags: employee, personnel, staff, hr\n- Archival Strategy: archive_only\n- Icon: user\n- Colour Theme: green", "created_at": "2025-06-20T07:36:27.358450", "updated_at": "2025-06-20T07:36:27.358458", "created_by": "Tarun", "updated_by": "Tarun", "entity_status": "new", "changes_detected": [], "icon_type": "text", "icon_content": ""}, {"_id": "68556dd43b82120ee564371f", "entity_id": "E44", "name": "Employee", "display_name": "Employee", "tenant_id": "T1003", "tenant_name": "Alpha Corp", "business_domain": "Human Resources", "category": "Employee Management", "tags": ["employee", "staff", "personnel"], "archival_strategy": "archive_only", "icon": "user", "colour_theme": "green", "version": 1, "status": "draft", "type": "master", "description": "Represents an employee in the organization", "table_name": "e44_Employee", "natural_language": "Tenant: Alpha Corp\n\nEmployee has employeeId^PK, firstName, lastName, email, phone, departmentId^FK, hireDate, annualLeaveBalance, sickLeaveBalance, personalLeaveBalance, totalLeaveEntitlement.\n\nEntity: Employee\n- Entity Name: Employee\n- Display Name: Employee\n- Type: master\n- Description: Represents an employee in the organization\n- Business Domain: Human Resources\n- Category: Employee Management\n- Tags: employee, staff, personnel\n- Archival Strategy: archive_only\n- Icon: user\n- Colour Theme: green", "created_at": "2025-06-20T14:19:00.007410", "updated_at": "2025-06-20T14:19:00.007414", "created_by": "Tarun", "updated_by": "Tarun", "entity_status": "new", "changes_detected": [], "icon_type": "text", "icon_content": ""}, {"_id": "6858f51c1195bbcbf7aa7692", "entity_id": "E45", "name": "Employee", "display_name": "Employee", "tenant_id": "T1007", "tenant_name": "Acme Corp Test", "business_domain": "Human Resources", "category": "Employee Management", "tags": ["employee", "hr"], "archival_strategy": "archive_only", "icon": "user", "colour_theme": "green", "version": 1, "status": "draft", "type": "master", "description": "Employee information", "table_name": "e45_Employee", "natural_language": "Tenant: Acme Corp Test\n\nEmployee has employeeId^PK, firstName, lastName, employmentStatus.\n\nEntity: Employee\n- Entity Name: Employee\n- Display Name: Employee\n- Type: master\n- Description: Employee information\n- Business Domain: Human Resources\n- Category: Employee Management\n- Tags: employee, hr\n- Archival Strategy: archive_only\n- Icon: user\n- Colour Theme: green", "created_at": "2025-06-23T06:33:00.529304", "updated_at": "2025-06-23T06:33:00.529311", "created_by": "Tarun", "updated_by": "Tarun", "entity_status": "new", "changes_detected": [], "icon_type": "text", "icon_content": ""}], "total_postgres": 0, "total_drafts": 5, "operation": "fetch"}, "status": "success"}
{"timestamp": "2025-06-23T09:36:27.147024", "endpoint": "fetch/entities", "input": {}, "output": {"success": true, "postgres_entities": [], "mongo_drafts": [{"_id": "685442ebf7fe510cb8237bf5", "entity_id": "E13", "name": "LeaveApplication", "display_name": "Leave Application", "tenant_id": "T1001", "tenant_name": "Alpha Corp", "business_domain": "Human Resources", "category": "Leave Management", "tags": ["leave", "application", "request", "approval"], "archival_strategy": "archive_only", "icon": "file-text", "colour_theme": "blue", "version": 1, "status": "draft", "type": "transaction", "description": "Represents an employee's request for time off from work", "table_name": "e13_LeaveApplication", "natural_language": "Tenant: Alpha Corp\n\nLeaveApplication has leaveId^PK, employeeId^FK, startDate, endDate, numDays[derived], reason, leaveTypeName, leaveSubTypeName, status, requiresDocumentation, documentationProvided, submissionDate, approvalDate, approvedBy^FK, comments, isRetroactive[derived], insufficientBalance[derived], lowTeamAvailability[derived], allowedNumberOfDays.\n\nEntity: LeaveApplication\n- Entity Name: LeaveApplication\n- Display Name: Leave Application\n- Type: transaction\n- Description: Represents an employee's request for time off from work\n- Business Domain: Human Resources\n- Category: Leave Management\n- Tags: leave, application, request, approval\n- Archival Strategy: archive_only\n- Icon: file-text\n- Colour Theme: blue", "created_at": "2025-06-19T17:03:39.677413", "updated_at": "2025-06-19T17:03:39.677423", "created_by": "Tarun", "updated_by": "Tarun", "entity_status": "new", "changes_detected": [], "icon_type": "text", "icon_content": ""}, {"_id": "6854484e00387ff38636d6dc", "entity_id": "E14", "name": "LeaveApplication", "display_name": "Leave Application", "tenant_id": "T1007", "tenant_name": "Acme Corp", "business_domain": "Human Resources", "category": "Leave Management", "tags": ["leave", "application", "request", "approval"], "archival_strategy": "archive_only", "icon": "file-text", "colour_theme": "blue", "version": 1, "status": "draft", "type": "transaction", "description": "Represents an employee's request for time off from work", "table_name": "e14_LeaveApplication", "natural_language": "Tenant: Acme Corp\n\nLeaveApplication has leaveId^PK, employeeId^FK, startDate, endDate, numDays[derived], reason, leaveTypeName, leaveSubTypeName, status, requiresDocumentation, documentationProvided, submissionDate, approvalDate, approvedBy^FK, comments, isRetroactive[derived], insufficientBalance[derived], lowTeamAvailability[derived], allowedNumberOfDays.\n\nEntity: LeaveApplication\n- Entity Name: LeaveApplication\n- Display Name: Leave Application\n- Type: transaction\n- Description: Represents an employee's request for time off from work\n- Business Domain: Human Resources\n- Category: Leave Management\n- Tags: leave, application, request, approval\n- Archival Strategy: archive_only\n- Icon: file-text\n- Colour Theme: blue", "created_at": "2025-06-19T17:26:38.418997", "updated_at": "2025-06-19T17:26:38.419004", "created_by": "Tarun", "updated_by": "Tarun", "entity_status": "new", "changes_detected": [], "icon_type": "text", "icon_content": ""}, {"_id": "68550f7b522219aecde6faed", "entity_id": "E15", "name": "Employee", "display_name": "Employee", "tenant_id": "T2", "tenant_name": "Acme Corp", "business_domain": "Human Resources", "category": "Personnel Management", "tags": ["employee", "personnel", "staff", "hr"], "archival_strategy": "archive_only", "icon": "user", "colour_theme": "green", "version": 1, "status": "draft", "type": "master", "description": "Represents an employee in the organization", "table_name": "e15_Employee", "natural_language": "Tenant: Acme Corp\n\nEmployee has employeeId^PK, firstName, lastName, email, phone, departmentId^FK, managerId^FK, jobTitle, hireDate, employmentStatus, annualLeaveBalance, sickLeaveBalance, personalLeaveBalance, totalLeaveEntitlement.\n\nEntity: Employee\n- Entity Name: Employee\n- Display Name: Employee\n- Type: master\n- Description: Represents an employee in the organization\n- Business Domain: Human Resources\n- Category: Personnel Management\n- Tags: employee, personnel, staff, hr\n- Archival Strategy: archive_only\n- Icon: user\n- Colour Theme: green", "created_at": "2025-06-20T07:36:27.358450", "updated_at": "2025-06-20T07:36:27.358458", "created_by": "Tarun", "updated_by": "Tarun", "entity_status": "new", "changes_detected": [], "icon_type": "text", "icon_content": ""}, {"_id": "68556dd43b82120ee564371f", "entity_id": "E44", "name": "Employee", "display_name": "Employee", "tenant_id": "T1003", "tenant_name": "Alpha Corp", "business_domain": "Human Resources", "category": "Employee Management", "tags": ["employee", "staff", "personnel"], "archival_strategy": "archive_only", "icon": "user", "colour_theme": "green", "version": 1, "status": "draft", "type": "master", "description": "Represents an employee in the organization", "table_name": "e44_Employee", "natural_language": "Tenant: Alpha Corp\n\nEmployee has employeeId^PK, firstName, lastName, email, phone, departmentId^FK, hireDate, annualLeaveBalance, sickLeaveBalance, personalLeaveBalance, totalLeaveEntitlement.\n\nEntity: Employee\n- Entity Name: Employee\n- Display Name: Employee\n- Type: master\n- Description: Represents an employee in the organization\n- Business Domain: Human Resources\n- Category: Employee Management\n- Tags: employee, staff, personnel\n- Archival Strategy: archive_only\n- Icon: user\n- Colour Theme: green", "created_at": "2025-06-20T14:19:00.007410", "updated_at": "2025-06-20T14:19:00.007414", "created_by": "Tarun", "updated_by": "Tarun", "entity_status": "new", "changes_detected": [], "icon_type": "text", "icon_content": ""}, {"_id": "6858f51c1195bbcbf7aa7692", "entity_id": "E45", "name": "Employee", "display_name": "Employee", "tenant_id": "T1007", "tenant_name": "Acme Corp Test", "business_domain": "Human Resources", "category": "Employee Management", "tags": ["employee", "hr"], "archival_strategy": "archive_only", "icon": "user", "colour_theme": "green", "version": 1, "status": "draft", "type": "master", "description": "Employee information", "table_name": "e45_Employee", "natural_language": "Tenant: Acme Corp Test\n\nEmployee has employeeId^PK, firstName, lastName, employmentStatus.\n\nEntity: Employee\n- Entity Name: Employee\n- Display Name: Employee\n- Type: master\n- Description: Employee information\n- Business Domain: Human Resources\n- Category: Employee Management\n- Tags: employee, hr\n- Archival Strategy: archive_only\n- Icon: user\n- Colour Theme: green", "created_at": "2025-06-23T06:33:00.529304", "updated_at": "2025-06-23T06:33:00.529311", "created_by": "Tarun", "updated_by": "Tarun", "entity_status": "new", "changes_detected": [], "icon_type": "text", "icon_content": ""}], "total_postgres": 0, "total_drafts": 5, "operation": "fetch"}, "status": "success"}
{"timestamp": "2025-06-23T10:37:56.684078", "endpoint": "fetch/entities", "input": {}, "output": {"success": true, "postgres_entities": [], "mongo_drafts": [{"_id": "685442ebf7fe510cb8237bf5", "entity_id": "E13", "name": "LeaveApplication", "display_name": "Leave Application", "tenant_id": "T1001", "tenant_name": "Alpha Corp", "business_domain": "Human Resources", "category": "Leave Management", "tags": ["leave", "application", "request", "approval"], "archival_strategy": "archive_only", "icon": "file-text", "colour_theme": "blue", "version": 1, "status": "draft", "type": "transaction", "description": "Represents an employee's request for time off from work", "table_name": "e13_LeaveApplication", "natural_language": "Tenant: Alpha Corp\n\nLeaveApplication has leaveId^PK, employeeId^FK, startDate, endDate, numDays[derived], reason, leaveTypeName, leaveSubTypeName, status, requiresDocumentation, documentationProvided, submissionDate, approvalDate, approvedBy^FK, comments, isRetroactive[derived], insufficientBalance[derived], lowTeamAvailability[derived], allowedNumberOfDays.\n\nEntity: LeaveApplication\n- Entity Name: LeaveApplication\n- Display Name: Leave Application\n- Type: transaction\n- Description: Represents an employee's request for time off from work\n- Business Domain: Human Resources\n- Category: Leave Management\n- Tags: leave, application, request, approval\n- Archival Strategy: archive_only\n- Icon: file-text\n- Colour Theme: blue", "created_at": "2025-06-19T17:03:39.677413", "updated_at": "2025-06-19T17:03:39.677423", "created_by": "Tarun", "updated_by": "Tarun", "entity_status": "new", "changes_detected": [], "icon_type": "text", "icon_content": ""}, {"_id": "6854484e00387ff38636d6dc", "entity_id": "E14", "name": "LeaveApplication", "display_name": "Leave Application", "tenant_id": "T1007", "tenant_name": "Acme Corp", "business_domain": "Human Resources", "category": "Leave Management", "tags": ["leave", "application", "request", "approval"], "archival_strategy": "archive_only", "icon": "file-text", "colour_theme": "blue", "version": 1, "status": "draft", "type": "transaction", "description": "Represents an employee's request for time off from work", "table_name": "e14_LeaveApplication", "natural_language": "Tenant: Acme Corp\n\nLeaveApplication has leaveId^PK, employeeId^FK, startDate, endDate, numDays[derived], reason, leaveTypeName, leaveSubTypeName, status, requiresDocumentation, documentationProvided, submissionDate, approvalDate, approvedBy^FK, comments, isRetroactive[derived], insufficientBalance[derived], lowTeamAvailability[derived], allowedNumberOfDays.\n\nEntity: LeaveApplication\n- Entity Name: LeaveApplication\n- Display Name: Leave Application\n- Type: transaction\n- Description: Represents an employee's request for time off from work\n- Business Domain: Human Resources\n- Category: Leave Management\n- Tags: leave, application, request, approval\n- Archival Strategy: archive_only\n- Icon: file-text\n- Colour Theme: blue", "created_at": "2025-06-19T17:26:38.418997", "updated_at": "2025-06-19T17:26:38.419004", "created_by": "Tarun", "updated_by": "Tarun", "entity_status": "new", "changes_detected": [], "icon_type": "text", "icon_content": ""}, {"_id": "68550f7b522219aecde6faed", "entity_id": "E15", "name": "Employee", "display_name": "Employee", "tenant_id": "T2", "tenant_name": "Acme Corp", "business_domain": "Human Resources", "category": "Personnel Management", "tags": ["employee", "personnel", "staff", "hr"], "archival_strategy": "archive_only", "icon": "user", "colour_theme": "green", "version": 1, "status": "draft", "type": "master", "description": "Represents an employee in the organization", "table_name": "e15_Employee", "natural_language": "Tenant: Acme Corp\n\nEmployee has employeeId^PK, firstName, lastName, email, phone, departmentId^FK, managerId^FK, jobTitle, hireDate, employmentStatus, annualLeaveBalance, sickLeaveBalance, personalLeaveBalance, totalLeaveEntitlement.\n\nEntity: Employee\n- Entity Name: Employee\n- Display Name: Employee\n- Type: master\n- Description: Represents an employee in the organization\n- Business Domain: Human Resources\n- Category: Personnel Management\n- Tags: employee, personnel, staff, hr\n- Archival Strategy: archive_only\n- Icon: user\n- Colour Theme: green", "created_at": "2025-06-20T07:36:27.358450", "updated_at": "2025-06-20T07:36:27.358458", "created_by": "Tarun", "updated_by": "Tarun", "entity_status": "new", "changes_detected": [], "icon_type": "text", "icon_content": ""}, {"_id": "68556dd43b82120ee564371f", "entity_id": "E44", "name": "Employee", "display_name": "Employee", "tenant_id": "T1003", "tenant_name": "Alpha Corp", "business_domain": "Human Resources", "category": "Employee Management", "tags": ["employee", "staff", "personnel"], "archival_strategy": "archive_only", "icon": "user", "colour_theme": "green", "version": 1, "status": "draft", "type": "master", "description": "Represents an employee in the organization", "table_name": "e44_Employee", "natural_language": "Tenant: Alpha Corp\n\nEmployee has employeeId^PK, firstName, lastName, email, phone, departmentId^FK, hireDate, annualLeaveBalance, sickLeaveBalance, personalLeaveBalance, totalLeaveEntitlement.\n\nEntity: Employee\n- Entity Name: Employee\n- Display Name: Employee\n- Type: master\n- Description: Represents an employee in the organization\n- Business Domain: Human Resources\n- Category: Employee Management\n- Tags: employee, staff, personnel\n- Archival Strategy: archive_only\n- Icon: user\n- Colour Theme: green", "created_at": "2025-06-20T14:19:00.007410", "updated_at": "2025-06-20T14:19:00.007414", "created_by": "Tarun", "updated_by": "Tarun", "entity_status": "new", "changes_detected": [], "icon_type": "text", "icon_content": ""}, {"_id": "6858f51c1195bbcbf7aa7692", "entity_id": "E45", "name": "Employee", "display_name": "Employee", "tenant_id": "T1007", "tenant_name": "Acme Corp Test", "business_domain": "Human Resources", "category": "Employee Management", "tags": ["employee", "hr"], "archival_strategy": "archive_only", "icon": "user", "colour_theme": "green", "version": 1, "status": "draft", "type": "master", "description": "Employee information", "table_name": "e45_Employee", "natural_language": "Tenant: Acme Corp Test\n\nEmployee has employeeId^PK, firstName, lastName, employmentStatus.\n\nEntity: Employee\n- Entity Name: Employee\n- Display Name: Employee\n- Type: master\n- Description: Employee information\n- Business Domain: Human Resources\n- Category: Employee Management\n- Tags: employee, hr\n- Archival Strategy: archive_only\n- Icon: user\n- Colour Theme: green", "created_at": "2025-06-23T06:33:00.529304", "updated_at": "2025-06-23T06:33:00.529311", "created_by": "Tarun", "updated_by": "Tarun", "entity_status": "new", "changes_detected": [], "icon_type": "text", "icon_content": ""}], "total_postgres": 0, "total_drafts": 5, "operation": "fetch"}, "status": "success"}
{"timestamp": "2025-06-23T11:22:46.768525", "endpoint": "fetch/entities", "input": {}, "output": {"success": true, "postgres_entities": [], "mongo_drafts": [{"_id": "685442ebf7fe510cb8237bf5", "entity_id": "E13", "name": "LeaveApplication", "display_name": "Leave Application", "tenant_id": "T1001", "tenant_name": "Alpha Corp", "business_domain": "Human Resources", "category": "Leave Management", "tags": ["leave", "application", "request", "approval"], "archival_strategy": "archive_only", "icon": "file-text", "colour_theme": "blue", "version": 1, "status": "draft", "type": "transaction", "description": "Represents an employee's request for time off from work", "table_name": "e13_LeaveApplication", "natural_language": "Tenant: Alpha Corp\n\nLeaveApplication has leaveId^PK, employeeId^FK, startDate, endDate, numDays[derived], reason, leaveTypeName, leaveSubTypeName, status, requiresDocumentation, documentationProvided, submissionDate, approvalDate, approvedBy^FK, comments, isRetroactive[derived], insufficientBalance[derived], lowTeamAvailability[derived], allowedNumberOfDays.\n\nEntity: LeaveApplication\n- Entity Name: LeaveApplication\n- Display Name: Leave Application\n- Type: transaction\n- Description: Represents an employee's request for time off from work\n- Business Domain: Human Resources\n- Category: Leave Management\n- Tags: leave, application, request, approval\n- Archival Strategy: archive_only\n- Icon: file-text\n- Colour Theme: blue", "created_at": "2025-06-19T17:03:39.677413", "updated_at": "2025-06-19T17:03:39.677423", "created_by": "Tarun", "updated_by": "Tarun", "entity_status": "new", "changes_detected": [], "icon_type": "text", "icon_content": ""}, {"_id": "6854484e00387ff38636d6dc", "entity_id": "E14", "name": "LeaveApplication", "display_name": "Leave Application", "tenant_id": "T1007", "tenant_name": "Acme Corp", "business_domain": "Human Resources", "category": "Leave Management", "tags": ["leave", "application", "request", "approval"], "archival_strategy": "archive_only", "icon": "file-text", "colour_theme": "blue", "version": 1, "status": "draft", "type": "transaction", "description": "Represents an employee's request for time off from work", "table_name": "e14_LeaveApplication", "natural_language": "Tenant: Acme Corp\n\nLeaveApplication has leaveId^PK, employeeId^FK, startDate, endDate, numDays[derived], reason, leaveTypeName, leaveSubTypeName, status, requiresDocumentation, documentationProvided, submissionDate, approvalDate, approvedBy^FK, comments, isRetroactive[derived], insufficientBalance[derived], lowTeamAvailability[derived], allowedNumberOfDays.\n\nEntity: LeaveApplication\n- Entity Name: LeaveApplication\n- Display Name: Leave Application\n- Type: transaction\n- Description: Represents an employee's request for time off from work\n- Business Domain: Human Resources\n- Category: Leave Management\n- Tags: leave, application, request, approval\n- Archival Strategy: archive_only\n- Icon: file-text\n- Colour Theme: blue", "created_at": "2025-06-19T17:26:38.418997", "updated_at": "2025-06-19T17:26:38.419004", "created_by": "Tarun", "updated_by": "Tarun", "entity_status": "new", "changes_detected": [], "icon_type": "text", "icon_content": ""}, {"_id": "68550f7b522219aecde6faed", "entity_id": "E15", "name": "Employee", "display_name": "Employee", "tenant_id": "T2", "tenant_name": "Acme Corp", "business_domain": "Human Resources", "category": "Personnel Management", "tags": ["employee", "personnel", "staff", "hr"], "archival_strategy": "archive_only", "icon": "user", "colour_theme": "green", "version": 1, "status": "draft", "type": "master", "description": "Represents an employee in the organization", "table_name": "e15_Employee", "natural_language": "Tenant: Acme Corp\n\nEmployee has employeeId^PK, firstName, lastName, email, phone, departmentId^FK, managerId^FK, jobTitle, hireDate, employmentStatus, annualLeaveBalance, sickLeaveBalance, personalLeaveBalance, totalLeaveEntitlement.\n\nEntity: Employee\n- Entity Name: Employee\n- Display Name: Employee\n- Type: master\n- Description: Represents an employee in the organization\n- Business Domain: Human Resources\n- Category: Personnel Management\n- Tags: employee, personnel, staff, hr\n- Archival Strategy: archive_only\n- Icon: user\n- Colour Theme: green", "created_at": "2025-06-20T07:36:27.358450", "updated_at": "2025-06-20T07:36:27.358458", "created_by": "Tarun", "updated_by": "Tarun", "entity_status": "new", "changes_detected": [], "icon_type": "text", "icon_content": ""}, {"_id": "68556dd43b82120ee564371f", "entity_id": "E44", "name": "Employee", "display_name": "Employee", "tenant_id": "T1003", "tenant_name": "Alpha Corp", "business_domain": "Human Resources", "category": "Employee Management", "tags": ["employee", "staff", "personnel"], "archival_strategy": "archive_only", "icon": "user", "colour_theme": "green", "version": 1, "status": "draft", "type": "master", "description": "Represents an employee in the organization", "table_name": "e44_Employee", "natural_language": "Tenant: Alpha Corp\n\nEmployee has employeeId^PK, firstName, lastName, email, phone, departmentId^FK, hireDate, annualLeaveBalance, sickLeaveBalance, personalLeaveBalance, totalLeaveEntitlement.\n\nEntity: Employee\n- Entity Name: Employee\n- Display Name: Employee\n- Type: master\n- Description: Represents an employee in the organization\n- Business Domain: Human Resources\n- Category: Employee Management\n- Tags: employee, staff, personnel\n- Archival Strategy: archive_only\n- Icon: user\n- Colour Theme: green", "created_at": "2025-06-20T14:19:00.007410", "updated_at": "2025-06-20T14:19:00.007414", "created_by": "Tarun", "updated_by": "Tarun", "entity_status": "new", "changes_detected": [], "icon_type": "text", "icon_content": ""}, {"_id": "6858f51c1195bbcbf7aa7692", "entity_id": "E45", "name": "Employee", "display_name": "Employee", "tenant_id": "T1007", "tenant_name": "Acme Corp Test", "business_domain": "Human Resources", "category": "Employee Management", "tags": ["employee", "hr"], "archival_strategy": "archive_only", "icon": "user", "colour_theme": "green", "version": 1, "status": "draft", "type": "master", "description": "Employee information", "table_name": "e45_Employee", "natural_language": "Tenant: Acme Corp Test\n\nEmployee has employeeId^PK, firstName, lastName, employmentStatus.\n\nEntity: Employee\n- Entity Name: Employee\n- Display Name: Employee\n- Type: master\n- Description: Employee information\n- Business Domain: Human Resources\n- Category: Employee Management\n- Tags: employee, hr\n- Archival Strategy: archive_only\n- Icon: user\n- Colour Theme: green", "created_at": "2025-06-23T06:33:00.529304", "updated_at": "2025-06-23T06:33:00.529311", "created_by": "Tarun", "updated_by": "Tarun", "entity_status": "new", "changes_detected": [], "icon_type": "text", "icon_content": ""}, {"_id": "685938b78ec2e81e85adbc10", "entity_id": "E46", "name": "Employee", "display_name": "Employee", "tenant_id": "T1001", "tenant_name": "Alpha Corp", "business_domain": "Human Resources", "category": "Employee Management", "tags": ["employee", "staff", "personnel"], "archival_strategy": "archive_only", "icon": "user", "colour_theme": "green", "version": 1, "status": "draft", "type": "master", "description": "Represents an employee in the organization", "table_name": "e46_Employee", "natural_language": "Tenant: Alpha Corp\n\nEmployee has employeeId^PK, firstName, lastName, email, phone, departmentId^FK, hireDate, annualLeaveBalance, sickLeaveBalance, personalLeaveBalance, totalLeaveEntitlement.\n\nEntity: Employee\n- Entity Name: Employee\n- Display Name: Employee\n- Type: master\n- Description: Represents an employee in the organization\n- Business Domain: Human Resources\n- Category: Employee Management\n- Tags: employee, staff, personnel\n- Archival Strategy: archive_only\n- Icon: user\n- Colour Theme: green", "created_at": "2025-06-23T11:21:27.162669", "updated_at": "2025-06-23T11:21:27.162678", "created_by": "Tarun", "updated_by": "Tarun", "entity_status": "new", "changes_detected": [], "icon_type": "text", "icon_content": ""}], "total_postgres": 0, "total_drafts": 6, "operation": "fetch"}, "status": "success"}
{"timestamp": "2025-06-23T14:08:00.079366", "endpoint": "fetch/entities", "input": {}, "output": {"success": true, "postgres_entities": [], "mongo_drafts": [{"_id": "685442ebf7fe510cb8237bf5", "entity_id": "E13", "name": "LeaveApplication", "display_name": "Leave Application", "tenant_id": "T1001", "tenant_name": "Alpha Corp", "business_domain": "Human Resources", "category": "Leave Management", "tags": ["leave", "application", "request", "approval"], "archival_strategy": "archive_only", "icon": "file-text", "colour_theme": "blue", "version": 1, "status": "draft", "type": "transaction", "description": "Represents an employee's request for time off from work", "table_name": "e13_LeaveApplication", "natural_language": "Tenant: Alpha Corp\n\nLeaveApplication has leaveId^PK, employeeId^FK, startDate, endDate, numDays[derived], reason, leaveTypeName, leaveSubTypeName, status, requiresDocumentation, documentationProvided, submissionDate, approvalDate, approvedBy^FK, comments, isRetroactive[derived], insufficientBalance[derived], lowTeamAvailability[derived], allowedNumberOfDays.\n\nEntity: LeaveApplication\n- Entity Name: LeaveApplication\n- Display Name: Leave Application\n- Type: transaction\n- Description: Represents an employee's request for time off from work\n- Business Domain: Human Resources\n- Category: Leave Management\n- Tags: leave, application, request, approval\n- Archival Strategy: archive_only\n- Icon: file-text\n- Colour Theme: blue", "created_at": "2025-06-19T17:03:39.677413", "updated_at": "2025-06-19T17:03:39.677423", "created_by": "Tarun", "updated_by": "Tarun", "entity_status": "new", "changes_detected": [], "icon_type": "text", "icon_content": ""}, {"_id": "6854484e00387ff38636d6dc", "entity_id": "E14", "name": "LeaveApplication", "display_name": "Leave Application", "tenant_id": "T1007", "tenant_name": "Acme Corp", "business_domain": "Human Resources", "category": "Leave Management", "tags": ["leave", "application", "request", "approval"], "archival_strategy": "archive_only", "icon": "file-text", "colour_theme": "blue", "version": 1, "status": "draft", "type": "transaction", "description": "Represents an employee's request for time off from work", "table_name": "e14_LeaveApplication", "natural_language": "Tenant: Acme Corp\n\nLeaveApplication has leaveId^PK, employeeId^FK, startDate, endDate, numDays[derived], reason, leaveTypeName, leaveSubTypeName, status, requiresDocumentation, documentationProvided, submissionDate, approvalDate, approvedBy^FK, comments, isRetroactive[derived], insufficientBalance[derived], lowTeamAvailability[derived], allowedNumberOfDays.\n\nEntity: LeaveApplication\n- Entity Name: LeaveApplication\n- Display Name: Leave Application\n- Type: transaction\n- Description: Represents an employee's request for time off from work\n- Business Domain: Human Resources\n- Category: Leave Management\n- Tags: leave, application, request, approval\n- Archival Strategy: archive_only\n- Icon: file-text\n- Colour Theme: blue", "created_at": "2025-06-19T17:26:38.418997", "updated_at": "2025-06-19T17:26:38.419004", "created_by": "Tarun", "updated_by": "Tarun", "entity_status": "new", "changes_detected": [], "icon_type": "text", "icon_content": ""}, {"_id": "68550f7b522219aecde6faed", "entity_id": "E15", "name": "Employee", "display_name": "Employee", "tenant_id": "T2", "tenant_name": "Acme Corp", "business_domain": "Human Resources", "category": "Personnel Management", "tags": ["employee", "personnel", "staff", "hr"], "archival_strategy": "archive_only", "icon": "user", "colour_theme": "green", "version": 1, "status": "draft", "type": "master", "description": "Represents an employee in the organization", "table_name": "e15_Employee", "natural_language": "Tenant: Acme Corp\n\nEmployee has employeeId^PK, firstName, lastName, email, phone, departmentId^FK, managerId^FK, jobTitle, hireDate, employmentStatus, annualLeaveBalance, sickLeaveBalance, personalLeaveBalance, totalLeaveEntitlement.\n\nEntity: Employee\n- Entity Name: Employee\n- Display Name: Employee\n- Type: master\n- Description: Represents an employee in the organization\n- Business Domain: Human Resources\n- Category: Personnel Management\n- Tags: employee, personnel, staff, hr\n- Archival Strategy: archive_only\n- Icon: user\n- Colour Theme: green", "created_at": "2025-06-20T07:36:27.358450", "updated_at": "2025-06-20T07:36:27.358458", "created_by": "Tarun", "updated_by": "Tarun", "entity_status": "new", "changes_detected": [], "icon_type": "text", "icon_content": ""}, {"_id": "68556dd43b82120ee564371f", "entity_id": "E44", "name": "Employee", "display_name": "Employee", "tenant_id": "T1003", "tenant_name": "Alpha Corp", "business_domain": "Human Resources", "category": "Employee Management", "tags": ["employee", "staff", "personnel"], "archival_strategy": "archive_only", "icon": "user", "colour_theme": "green", "version": 1, "status": "draft", "type": "master", "description": "Represents an employee in the organization", "table_name": "e44_Employee", "natural_language": "Tenant: Alpha Corp\n\nEmployee has employeeId^PK, firstName, lastName, email, phone, departmentId^FK, hireDate, annualLeaveBalance, sickLeaveBalance, personalLeaveBalance, totalLeaveEntitlement.\n\nEntity: Employee\n- Entity Name: Employee\n- Display Name: Employee\n- Type: master\n- Description: Represents an employee in the organization\n- Business Domain: Human Resources\n- Category: Employee Management\n- Tags: employee, staff, personnel\n- Archival Strategy: archive_only\n- Icon: user\n- Colour Theme: green", "created_at": "2025-06-20T14:19:00.007410", "updated_at": "2025-06-20T14:19:00.007414", "created_by": "Tarun", "updated_by": "Tarun", "entity_status": "new", "changes_detected": [], "icon_type": "text", "icon_content": ""}, {"_id": "6858f51c1195bbcbf7aa7692", "entity_id": "E45", "name": "Employee", "display_name": "Employee", "tenant_id": "T1007", "tenant_name": "Acme Corp Test", "business_domain": "Human Resources", "category": "Employee Management", "tags": ["employee", "hr"], "archival_strategy": "archive_only", "icon": "user", "colour_theme": "green", "version": 1, "status": "draft", "type": "master", "description": "Employee information", "table_name": "e45_Employee", "natural_language": "Tenant: Acme Corp Test\n\nEmployee has employeeId^PK, firstName, lastName, employmentStatus.\n\nEntity: Employee\n- Entity Name: Employee\n- Display Name: Employee\n- Type: master\n- Description: Employee information\n- Business Domain: Human Resources\n- Category: Employee Management\n- Tags: employee, hr\n- Archival Strategy: archive_only\n- Icon: user\n- Colour Theme: green", "created_at": "2025-06-23T06:33:00.529304", "updated_at": "2025-06-23T06:33:00.529311", "created_by": "Tarun", "updated_by": "Tarun", "entity_status": "new", "changes_detected": [], "icon_type": "text", "icon_content": ""}, {"_id": "685938b78ec2e81e85adbc10", "entity_id": "E46", "name": "Employee", "display_name": "Employee", "tenant_id": "T1001", "tenant_name": "Alpha Corp", "business_domain": "Human Resources", "category": "Employee Management", "tags": ["employee", "staff", "personnel"], "archival_strategy": "archive_only", "icon": "user", "colour_theme": "green", "version": 1, "status": "draft", "type": "master", "description": "Represents an employee in the organization", "table_name": "e46_Employee", "natural_language": "Tenant: Alpha Corp\n\nEmployee has employeeId^PK, firstName, lastName, email, phone, departmentId^FK, hireDate, annualLeaveBalance, sickLeaveBalance, personalLeaveBalance, totalLeaveEntitlement.\n\nEntity: Employee\n- Entity Name: Employee\n- Display Name: Employee\n- Type: master\n- Description: Represents an employee in the organization\n- Business Domain: Human Resources\n- Category: Employee Management\n- Tags: employee, staff, personnel\n- Archival Strategy: archive_only\n- Icon: user\n- Colour Theme: green", "created_at": "2025-06-23T11:21:27.162669", "updated_at": "2025-06-23T11:21:27.162678", "created_by": "Tarun", "updated_by": "Tarun", "entity_status": "new", "changes_detected": [], "icon_type": "text", "icon_content": ""}], "total_postgres": 0, "total_drafts": 6, "operation": "fetch"}, "status": "success"}
