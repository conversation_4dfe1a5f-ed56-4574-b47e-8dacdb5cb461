# LO Schema Analysis and Implementation Plan

This document provides a detailed analysis of the workflow_runtime schema tables related to Local Objectives (LOs) and outlines the implementation plan for the LO parser and deployer.

## 1. Schema Analysis

### 1.1 Local Objectives Table

**workflow_runtime.local_objectives**
```sql
Table "workflow_runtime.local_objectives"
     Column      |            Type             | Collation | Nullable |         Default
-----------------+-----------------------------+-----------+----------+-------------------------
 lo_id           | character varying(50)       |           | not null |
 contextual_id   | character varying(100)      |           | not null |
 name            | character varying(100)      |           | not null |
 function_type   | character varying(50)       |           | not null |
 workflow_source | character varying(50)       |           | not null |
 go_id           | character varying(50)       |           |          |
 created_at      | timestamp without time zone |           |          | CURRENT_TIMESTAMP
 updated_at      | timestamp without time zone |           |          | CURRENT_TIMESTAMP
 system_function | character varying(100)      |           |          | NULL::character varying
 description     | text                        |           |          |
 ui_stack        | jsonb                       |           |          |
 mapping_stack   | jsonb                       |           |          |
 version_type    | character varying(10)       |           |          | 'v2'::character varying
```

The local_objectives table stores the basic information about LOs, including ID, name, function type, workflow source, etc. It has foreign key relationships with many other tables, indicating that it's a central table in the schema.

### 1.2 LO Input Stack Tables

**workflow_runtime.lo_input_stack**
```sql
Table "workflow_runtime.lo_input_stack"
   Column    |            Type             | Collation | Nullable |                  Default
-------------+-----------------------------+-----------+----------+--------------------------------------------
 id          | integer                     |           | not null | nextval('lo_input_stack_id_seq'::regclass)
 lo_id       | character varying(50)       |           | not null |
 description | text                        |           |          |
 created_at  | timestamp without time zone |           |          | CURRENT_TIMESTAMP
 updated_at  | timestamp without time zone |           |          | CURRENT_TIMESTAMP
```

**workflow_runtime.lo_input_items**
```sql
Table "workflow_runtime.lo_input_items"
       Column       |            Type             | Collation | Nullable |               Default
--------------------+-----------------------------+-----------+----------+--------------------------------------
 id                 | character varying(50)       |           | not null |
 input_stack_id     | integer                     |           | not null |
 slot_id            | character varying(100)      |           | not null |
 contextual_id      | character varying(100)      |           | not null |
 source_type        | input_source_type           |           | not null |
 source_description | text                        |           |          |
 required           | boolean                     |           |          | false
 created_at         | timestamp without time zone |           |          | CURRENT_TIMESTAMP
 updated_at         | timestamp without time zone |           |          | CURRENT_TIMESTAMP
 lo_id              | character varying(50)       |           |          |
 data_type          | character varying(50)       |           |          |
 ui_control         | character varying(50)       |           |          |
```

The lo_input_stack and lo_input_items tables store the input stack and input items for LOs. The lo_input_stack table has a one-to-many relationship with the lo_input_items table.

### 1.3 LO Output Stack Tables

**workflow_runtime.lo_output_stack**
```sql
Table "workflow_runtime.lo_output_stack"
   Column    |            Type             | Collation | Nullable |                   Default
-------------+-----------------------------+-----------+----------+---------------------------------------------
 id          | integer                     |           | not null | nextval('lo_output_stack_id_seq'::regclass)
 lo_id       | character varying(50)       |           | not null |
 description | text                        |           |          |
 created_at  | timestamp without time zone |           |          | CURRENT_TIMESTAMP
 updated_at  | timestamp without time zone |           |          | CURRENT_TIMESTAMP
```

**workflow_runtime.lo_output_items**
```sql
Table "workflow_runtime.lo_output_items"
      Column      |            Type             | Collation | Nullable |               Default
-----------------+-----------------------------+-----------+----------+--------------------------------------
 id              | character varying(50)       |           | not null |
 output_stack_id | integer                     |           | not null |
 slot_id         | character varying(100)      |           | not null |
 contextual_id   | character varying(100)      |           | not null |
 source          | character varying(100)      |           |          |
 created_at      | timestamp without time zone |           |          | CURRENT_TIMESTAMP
 updated_at      | timestamp without time zone |           |          | CURRENT_TIMESTAMP
 lo_id           | character varying(50)       |           |          |
 item_id         | character varying(100)      |           |          | 'default_item_id'::character varying
 name            | character varying(100)      |           |          | 'default_name'::character varying
 type            | character varying(50)       |           |          | 'string'::character varying
```

The lo_output_stack and lo_output_items tables store the output stack and output items for LOs. The lo_output_stack table has a one-to-many relationship with the lo_output_items table.

### 1.4 Execution Pathways Tables

**workflow_runtime.execution_pathways**
```sql
Table "workflow_runtime.execution_pathways"
    Column    |            Type             | Collation | Nullable |                    Default
--------------+-----------------------------+-----------+----------+------------------------------------------------
 lo_id        | character varying(50)       |           | not null |
 pathway_type | character varying(50)       |           | not null |
 next_lo      | character varying(50)       |           |          |
 created_at   | timestamp without time zone |           |          | CURRENT_TIMESTAMP
 updated_at   | timestamp without time zone |           |          | CURRENT_TIMESTAMP
 id           | integer                     |           | not null | nextval('execution_pathways_id_seq'::regclass)
```

**workflow_runtime.execution_pathway_conditions**
```sql
Table "workflow_runtime.execution_pathway_conditions"
       Column        |            Type             | Collation | Nullable |                         Default
---------------------+-----------------------------+-----------+----------+----------------------------------------------- -----------
 id                  | integer                     |           | not null | nextval('execution_pathway_conditions_id_seq': :regclass)
 lo_id               | character varying(50)       |           | not null |
 condition_type      | character varying(50)       |           | not null |
 condition_entity    | character varying(50)       |           |          |
 condition_attribute | character varying(50)       |           |          |
 condition_operator  | character varying(20)       |           |          |
 condition_value     | text                        |           |          |
 next_lo             | character varying(50)       |           | not null |
 created_at          | timestamp without time zone |           |          | CURRENT_TIMESTAMP
 updated_at          | timestamp without time zone |           |          | CURRENT_TIMESTAMP
```

The execution_pathways and execution_pathway_conditions tables store the execution pathways and conditions for LOs. The execution_pathways table defines the next LO to execute, while the execution_pathway_conditions table defines the conditions under which to execute the next LO.

### 1.5 GO-LO Mapping Table

**workflow_runtime.go_lo_mapping**
```sql
Table "workflow_runtime.go_lo_mapping"
     Column      |          Type          | Collation | Nullable | Default
-----------------+------------------------+-----------+----------+---------
 mapping_id      | character varying(100) |           | not null |
 go_id           | character varying(50)  |           |          |
 lo_id           | character varying(50)  |           | not null |
 sequence_number | integer                |           |          | 0
```

The go_lo_mapping table maps GOs to LOs, defining the sequence of LOs within a GO.

### 1.6 LO System Functions Table

**workflow_runtime.lo_system_functions**
```sql
Table "workflow_runtime.lo_system_functions"
    Column     |            Type             | Collation | Nullable |      Default
---------------+-----------------------------+-----------+----------+-------------------
 function_id   | character varying(50)       |           | not null |
 lo_id         | character varying(50)       |           | not null |
 function_name | character varying(100)      |           | not null |
 function_type | character varying(50)       |           | not null |
 parameters    | jsonb                       |           |          |
 output_to     | character varying(100)      |           |          |
 created_at    | timestamp without time zone |           |          | CURRENT_TIMESTAMP
 updated_at    | timestamp without time zone |           |          | CURRENT_TIMESTAMP
```

The lo_system_functions table stores the system functions used by LOs, including function name, type, parameters, and output destination.

### 1.7 LO Input Validations Table

**workflow_runtime.lo_input_validations**
```sql
Table "workflow_runtime.lo_input_validations"
        Column         |            Type             | Collation | Nullable |                     Default
-----------------------+-----------------------------+-----------+----------+--------------------------------------------- -----
 id                    | integer                     |           | not null | nextval('lo_input_validations_id_seq'::regcl ass)
 input_item_id         | character varying(50)       |           | not null |
 input_stack_id        | integer                     |           | not null |
 rule                  | character varying(100)      |           | not null |
 rule_type             | character varying(50)       |           | not null |
 entity                | character varying(50)       |           |          |
 attribute             | character varying(50)       |           |          |
 validation_method     | character varying(50)       |           |          |
 reference_date_source | character varying(100)      |           |          |
 allowed_values        | jsonb                       |           |          |
 error_message         | text                        |           |          |
 created_at            | timestamp without time zone |           |          | CURRENT_TIMESTAMP
 updated_at            | timestamp without time zone |           |          | CURRENT_TIMESTAMP
```

The lo_input_validations table stores the validation rules for LO input items, including rule type, entity, attribute, validation method, allowed values, and error message.

### 1.8 LO Output Triggers Table

**workflow_runtime.lo_output_triggers**
```sql
Table "workflow_runtime.lo_output_triggers"
      Column      |            Type             | Collation | Nullable |      Default
------------------+-----------------------------+-----------+----------+-------------------
 id               | character varying(50)       |           | not null |
 output_item_id   | character varying(50)       |           | not null |
 output_stack_id  | integer                     |           | not null |
 target_objective | character varying(100)      |           | not null |
 target_input     | character varying(100)      |           | not null |
 mapping_type     | character varying(50)       |           | not null |
 created_at       | timestamp without time zone |           |          | CURRENT_TIMESTAMP
 updated_at       | timestamp without time zone |           |          | CURRENT_TIMESTAMP
```

The lo_output_triggers table stores the triggers for LO output items, defining how output items trigger other objectives.

## 2. Implementation Plan for LO Parser

### 2.1 Parse LO Metadata

The LO parser needs to extract the following metadata from the prescriptive text:

- lo_id (generated by the system)
- name
- function_type
- workflow_source
- go_id (from the contextual ID)
- description
- ui_stack
- mapping_stack
- version_type

Example implementation:

```python
def parse_lo_metadata(lo_block: str) -> Dict:
    """
    Parse LO metadata from a block of prescriptive text.
    
    Args:
        lo_block: Block of prescriptive text containing LO definition
        
    Returns:
        Dictionary of LO metadata
    """
    metadata = {}
    
    # Extract name
    name_match = re.search(r'name:\s*"([^"]+)"', lo_block)
    if name_match:
        metadata["name"] = name_match.group(1).strip()
    
    # Extract function_type
    function_type_match = re.search(r'function_type:\s*"([^"]+)"', lo_block)
    if function_type_match:
        metadata["function_type"] = function_type_match.group(1).strip()
    
    # Extract workflow_source
    workflow_source_match = re.search(r'workflow_source:\s*"([^"]+)"', lo_block)
    if workflow_source_match:
        metadata["workflow_source"] = workflow_source_match.group(1).strip()
    
    # Extract description
    description_match = re.search(r'description:\s*"([^"]+)"', lo_block)
    if description_match:
        metadata["description"] = description_match.group(1).strip()
    
    # Extract version
    version_match = re.search(r'version:\s*"([^"]+)"', lo_block)
    if version_match:
        metadata["version"] = version_match.group(1).strip()
    
    # Extract status
    status_match = re.search(r'status:\s*"([^"]+)"', lo_block)
    if status_match:
        metadata["status"] = status_match.group(1).strip()
    
    # Set version_type
    metadata["version_type"] = "v2"
    
    return metadata
```

### 2.2 Parse LO Input Stack

The LO parser needs to extract the input stack and input items from the prescriptive text:

Example implementation:

```python
def parse_lo_input_stack(lo_block: str) -> Dict:
    """
    Parse LO input stack from a block of prescriptive text.
    
    Args:
        lo_block: Block of prescriptive text containing LO definition
        
    Returns:
        Dictionary of LO input stack and items
    """
    input_stack = {
        "description": "Input stack for LO",
        "items": []
    }
    
    # Extract input section
    input_match = re.search(r'\*Inputs:\s+([^*]+)', lo_block)
    if not input_match:
        return input_stack
    
    input_text = input_match.group(1).strip()
    
    # Extract entity and attributes
    entity_match = re.search(r'(\w+)\s+with\s+(.+)', input_text)
    if not entity_match:
        return input_stack
    
    entity_name = entity_match.group(1).strip()
    attributes_text = entity_match.group(2).strip()
    
    # Extract attributes
    for attr in attributes_text.split(','):
        attr = attr.strip()
        
        # Skip empty attributes
        if not attr:
            continue
        
        # Check if attribute has a type or constraint
        attr_parts = attr.split()
        attr_name = attr_parts[0].strip()
        
        # Remove any trailing characters like *, ;
        attr_name = re.sub(r'[*;]$', '', attr_name)
        
        # Check if attribute is required
        required = '*' in attr
        
        # Check for enum values
        enum_values = []
        enum_match = re.search(r'\(([^)]+)\)', attr)
        if enum_match:
            enum_text = enum_match.group(1).strip()
            enum_values = [val.strip() for val in enum_text.split(',')]
        
        # Check for dependencies
        depends_on = None
        depends_match = re.search(r'\[depends on:\s*(\w+)\]', attr)
        if depends_match:
            depends_on = depends_match.group(1).strip()
        
        # Check for info flag
        is_info = '[info]' in attr
        
        # Check for optional flag
        is_optional = '[optional]' in attr
        
        # Add attribute to input items
        input_item = {
            "id": f"{entity_name.lower()}_{attr_name.lower()}",
            "slot_id": f"{entity_name}.{attr_name}",
            "contextual_id": f"{entity_name}.{attr_name}",
            "source_type": "user",
            "required": required and not is_optional,
            "data_type": "string",  # Default data type
            "name": attr_name,
            "type": "string"  # Default type
        }
        
        if enum_values:
            input_item["allowed_values"] = enum_values
        
        if depends_on:
            input_item["depends_on"] = depends_on
        
        if is_info:
            input_item["is_info"] = True
        
        input_stack["items"].append(input_item)
    
    # Extract system actions
    system_actions_match = re.findall(r'\*\s+System\s+([^*]+)', lo_block)
    for action in system_actions_match:
        action = action.strip()
        
        # Skip empty actions
        if not action:
            continue
        
        # Extract action type
        action_type_match = re.search(r'(\w+)\s+', action)
        if not action_type_match:
            continue
        
        action_type = action_type_match.group(1).strip()
        
        # Extract entity and attribute
        entity_attr_match = re.search(r'(\w+)\.(\w+)', action)
        if not entity_attr_match:
            continue
        
        entity_name = entity_attr_match.group(1).strip()
        attr_name = entity_attr_match.group(2).strip()
        
        # Extract function and parameters
        function_match = re.search(r'using\s+(\w+)(?:\s+with\s+(.+))?', action)
        if not function_match:
            continue
        
        function_name = function_match.group(1).strip()
        parameters = function_match.group(2).strip() if function_match.group(2) else None
        
        # Add system action to input items
        for item in input_stack["items"]:
            if item["slot_id"] == f"{entity_name}.{attr_name}":
                item["system_action"] = {
                    "action_type": action_type,
                    "function": function_name,
                    "parameters": parameters
                }
                break
    
    return input_stack
```

### 2.3 Parse LO Output Stack

The LO parser needs to extract the output stack and output items from the prescriptive text:

Example implementation:

```python
def parse_lo_output_stack(lo_block: str) -> Dict:
    """
    Parse LO output stack from a block of prescriptive text.
    
    Args:
        lo_block: Block of prescriptive text containing LO definition
        
    Returns:
        Dictionary of LO output stack and items
    """
    output_stack = {
        "description": "Output stack for LO",
        "items": []
    }
    
    # Extract output section
    output_match = re.search(r'\*Outputs:\s+([^*]+)', lo_block)
    if not output_match:
        return output_stack
    
    output_text = output_match.group(1).strip()
    
    # Extract entity and attributes
    entity_match = re.search(r'(\w+)\s+with\s+(.+)', output_text)
    if not entity_match:
        return output_stack
    
    entity_name = entity_match.group(1).strip()
    attributes_text = entity_match.group(2).strip()
    
    # Extract attributes
    for attr in attributes_text.split(','):
        attr = attr.strip()
        
        # Skip empty attributes
        if not attr:
            continue
        
        # Check if attribute has a type or constraint
        attr_parts = attr.split()
        attr_name = attr_parts[0].strip()
        
        # Remove any trailing characters like *, ;
        attr_name = re.sub(r'[*;]$', '', attr_name)
        
        # Add attribute to output items
        output_item = {
            "id": f"{entity_name.lower()}_{attr_name.lower()}",
            "slot_id": f"{entity_name}.{attr_name}",
            "contextual_id": f"{entity_name}.{attr_name}",
            "source": "system",
            "name": attr_name,
            "type": "string"  # Default type
        }
        
        output_stack["items"].append(output_item)
    
    # Extract system actions
    system_actions_match = re.findall(r'\*\s+System\s+([^*]+)', lo_block)
    for action in system_actions_match:
        action = action.strip()
        
        # Skip empty actions
        if not action:
            continue
        
        # Extract action type
        action_type_match = re.search(r'(\w+)\s+', action)
        if not action_type_match:
            continue
        
        action_type = action_type_match.group(1).strip()
        
        # Extract entity and attribute
        entity_attr_match = re.search(r'(\w+)\.(\w+)', action)
        if not entity_attr_match:
            continue
        
        entity_name = entity_attr_match.group(1).strip()
        attr_name = entity_attr_match.group(2).strip()
        
        # Extract function and parameters
        function_match = re.search(r'using\s+(\w+)(?:\s+with\s+(.+))?', action)
        if not function_match:
            continue
        
        function_name = function_match.group(1).strip()
        parameters = function_match.group(2).strip() if function_match.group(2) else None
        
        # Add system action to output items
        for item in output_stack["items"]:
            if item["slot_id"] == f"{entity_name}.{attr_name}":
                item["system_action"] = {
                    "action_type": action_type,
                    "function": function_name,
                    "parameters": parameters
                }
                break
    
    return output_stack
```

### 2.4 Parse LO Execution Pathways

The LO parser needs to extract the execution pathways and conditions from the prescriptive text:

Example implementation:

```python
def parse_lo_execution_pathways(lo_block: str) -> List[Dict]:
    """
    Parse LO execution pathways from a block of prescriptive text.
    
    Args:
        lo_block: Block of prescriptive text containing LO definition
        
    Returns:
        List of LO execution pathways
    """
    execution_pathways = []
    
    # Extract execution pathway section
    pathway_match = re.search(r'Execution pathway:\s*([^*]+)', lo_block)
    if not pathway_match:
        return execution_pathways
    
    pathway_text = pathway_match.group(1).strip()
    
    # Extract pathways
    pathway_lines = pathway_text.split('\n')
    for line in pathway_lines:
        line = line.strip()
        
        # Skip empty lines
        if not line:
            continue
        
        # Skip lines that don't start with *
        if not line.startswith('*'):
            continue
        
        # Remove the leading *
        line = line[1:].strip()
        
        # Extract condition and next LO
        condition_match = re.search(r'When\s+([^,]+),\s+route\s+to\s+(\w+)', line)
        if condition_match:
            condition = condition_match.group(1).strip()
            next_lo = condition_match.group(2).strip()
            
            # Extract entity, attribute, operator, and value
            entity_attr_match = re.search(r'(\w+)\.(\w+)\s+([=<>!]+)\s+(.+)', condition)
            if entity_attr_match:
                entity = entity_attr_match.group(1).strip()
                attribute = entity_attr_match.group(2).strip()
                operator = entity_attr_match.group(3).strip()
                value = entity_attr_match.group(4).strip()
                
                # Add execution pathway
                execution_pathway = {
                    "pathway_type": "conditional",
                    "next_lo": next_lo,
                    "condition": {
                        "type": "attribute",
                        "entity": entity,
                        "attribute": attribute,
                        "operator": operator,
                        "value": value
                    }
                }
                
                execution_pathways.append(execution_pathway)
            else:
                # Add execution pathway without condition details
                execution_pathway = {
                    "pathway_type": "conditional",
                    "next_lo": next_lo,
                    "condition": {
                        "type": "custom",
                        "expression": condition
                    }
                }
                
                execution_pathways.append(execution_pathway)
        
        # Extract system flag and next LO
        flag_match = re.search(r'When\s+([^,]+),\s+system\s+flags\s+(\w+)\.(\w+)\s+to\s+(\w+),\s+route\s+to\s+(\w+)', line)
        if flag_match:
            condition = flag_match.group(1).strip()
            entity = flag_match.group(2).strip()
            attribute = flag_match.group(3).strip()
            flag_value = flag_match.group(4).strip()
            next_lo = flag_match.group(5).strip()
            
            # Add execution pathway with system flag
            execution_pathway = {
                "pathway_type": "conditional",
                "next_lo": next_lo,
                "condition": {
                    "type": "custom",
                    "expression": condition
                },
                "system_flag": {
                    "entity": entity,
                    "attribute": attribute,
                    "value": flag_value
                }
            }
            
            execution_pathways.append(execution_pathway)
    
    return execution_pathways
```

### 2.5 Parse LO DB Stack

The LO parser needs to extract the DB stack from the prescriptive text:

Example implementation:

```python
def parse_lo_db_stack(lo_block: str) -> List[Dict]:
    """
    Parse LO DB stack from a block of prescriptive text.
    
    Args:
        lo_block: Block of prescriptive text containing LO definition
        
    Returns:
        List of LO DB stack items
    """
    db_stack = []
    
    # Extract DB stack section
    db_stack_match = re.search(r'\*DB Stack:\*\s*([^*]+)', lo_block)
    if not db_stack_match:
        return db_stack
    
    db_stack_text = db_stack_match.group(1).strip()
    
    # Extract DB stack items
    db_stack_lines = db_stack_text.split('\n')
    for line in db_stack_lines:
        line = line.strip()
        
        # Skip empty lines
        if not line:
            continue
        
        # Skip lines that don't start with *
        if not line.startswith('*'):
            continue
        
        # Remove the leading *
        line = line[1:].strip()
        
        # Extract entity, attribute, data type, constraint, and error message
        db_match = re.search(r'(\w+)\.(\w+)\s+\(([^)]+)\)\s+is\s+([^.]+)\.\s+Error message:\s+"([^"]+)"', line)
        if db_match:
            entity = db_match.group(1).strip()
            attribute = db_match.group(2).strip()
            data_type = db_match.group(3).strip()
            constraint = db_match.group(4).strip()
            error_message = db_match.group(5).strip()
            
            # Add DB stack item
            db_stack_item = {
                "entity": entity,
                "attribute": attribute,
                "data_type": data_type,
                "constraint": constraint,
                "error_message": error_message
            }
            
            db_stack.append(db_stack_item)
    
    return db_stack
```

### 2.6 Parse LO UI Stack

The LO parser needs to extract the UI stack from the prescriptive text:

Example implementation:

```python
def parse_lo_ui_stack(lo_block: str) -> List[Dict]:
    """
    Parse LO UI stack from a block of prescriptive text.
    
    Args:
        lo_block: Block of prescriptive text containing LO definition
        
    Returns:
        List of LO UI stack items
    """
    ui_stack = []
    
    # Extract UI stack section
    ui_stack_match = re.search(r'\*UI Stack:\*\s*([^*]+)', lo_block)
    if not ui_stack_match:
        return ui_stack
    
    ui_stack_text = ui_stack_match.group(1).strip()
    
    # Extract UI stack items
    ui_stack_lines = ui_stack_text.split('\n')
    for line in ui_stack_lines:
        line = line.strip()
        
        # Skip empty lines
        if not line:
            continue
        
        # Skip lines that don't start with *
        if not line.startswith('*'):
            continue
        
        # Remove the leading *
        line = line[1:].strip()
        
        # Extract entity, attribute, UI control, and properties
        ui_match = re.search(r'(\w+)\.(\w+)\s+displays\s+as\s+(\w+(?:-\w+)*)\s+with\s+(.+)', line)
        if ui_match:
            entity = ui_match.group(1).strip()
            attribute = ui_match.group(2).strip()
            ui_control = ui_match.group(3).strip()
            properties_text = ui_match.group(4).strip()
            
            # Extract properties
            properties = {}
            for prop in properties_text.split('and'):
                prop = prop.strip()
                
                # Skip empty properties
                if not prop:
                    continue
                
                # Extract property name and value
                prop
