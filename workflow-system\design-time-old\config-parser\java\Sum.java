import java.util.Scanner;

public class Sum {
    public static void main(String[] args) {
        // Create a Scanner object to read input from command prompt
        Scanner scanner = new Scanner(System.in);
        
        // Prompt for first number
        System.out.print("Enter first number: ");
        double num1 = scanner.nextDouble();
        
        // Prompt for second number
        System.out.print("Enter second number: ");
        double num2 = scanner.nextDouble();
        
        // Calculate sum using the function
        double result = sum(num1, num2);
        
        // Display the result
        System.out.println("Sum: " + result);
        
        // Close the scanner
        scanner.close();
    }
    
    /**
     * Function to calculate the sum of two numbers
     * 
     * @param a First number
     * @param b Second number
     * @return Sum of the two numbers
     */
    public static double sum(double a, double b) {
        return a + b;
    }
}
