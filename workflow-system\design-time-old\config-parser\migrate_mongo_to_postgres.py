#!/usr/bin/env python3
"""
MongoDB to PostgreSQL Migration Script (Schema-Based)

This script migrates workflow data from MongoDB to a PostgreSQL database.
It creates a separate schema for each tenant within the same PostgreSQL database.

Usage:
    python migrate_mongodb_to_postgres.py

Configuration:
    Configure the database connection details and file paths in the script constants.
"""

import os
import sys
import time
import json
import traceback
import psycopg2
from psycopg2.extras import Json

# === Configuration ===
PG_CONFIG = {
    "dbname": "workflow_system",  # Use a single database for all tenants
    "user": "postgres",
    "password": "workflow_postgres_secure_password",
    "host": "**********",
    "port": "5432"
}

MONGO_JSON_FILE = "workflow_data.json"  # Path to MongoDB JSON export file
SQL_SCHEMA_FILE = "db_schema.sql"  # Path to SQL schema file

# === Function to connect to PostgreSQL with retry ===
def connect_with_retry(db_config, retries=5, delay=1):
    """Attempt to connect to PostgreSQL with retries."""
    for attempt in range(retries):
        try:
            return psycopg2.connect(**db_config)
        except psycopg2.OperationalError:
            print(f"⏳ Waiting for DB to be ready... retry {attempt + 1}/{retries}")
            time.sleep(delay)
    raise Exception("❌ Could not connect to database after multiple retries.")

# === Function to check and create schema if needed ===
def check_and_create_schema(pg_conn, tenant_id):
    """Check if schema exists and create if needed."""
    schema_name = f"tenant_{tenant_id.lower()}"
    
    try:
        with pg_conn.cursor() as cursor:
            # Check if schema exists
            cursor.execute("SELECT schema_name FROM information_schema.schemata WHERE schema_name = %s", (schema_name,))
            exists = cursor.fetchone()
            
            if exists:
                print(f"⚠️ Schema {schema_name} already exists.")
                recreate = input("Do you want to recreate the schema? (y/n): ").lower() == 'y'
                
                if recreate:
                    print(f"➡️ Dropping existing schema: {schema_name}")
                    # Use CASCADE to drop all objects in the schema
                    cursor.execute(f"DROP SCHEMA {schema_name} CASCADE")
                    pg_conn.commit()
                    print(f"✅ Dropped old schema: {schema_name}")
                    
                    cursor.execute(f"CREATE SCHEMA {schema_name}")
                    pg_conn.commit()
                    print(f"✅ Created schema: {schema_name}")
                else:
                    print(f"ℹ️ Using existing schema: {schema_name}")
            else:
                print(f"➡️ Creating schema: {schema_name}")
                cursor.execute(f"CREATE SCHEMA {schema_name}")
                pg_conn.commit()
                print(f"✅ Created schema: {schema_name}")
            
            # Set the search path to our schema for this connection
            cursor.execute(f"SET search_path TO {schema_name}")
            pg_conn.commit()
            
        return schema_name
    
    except Exception as e:
        pg_conn.rollback()
        print(f"❌ Error checking/creating schema: {e}")
        traceback.print_exc()
        sys.exit(1)

# === Function to create database tables from SQL file in the specific schema ===
def create_tables_in_schema(pg_conn, sql_path, schema_name):
    """Create database tables from SQL schema file in the specified schema."""
    try:
        # First, ensure we're using the right schema
        with pg_conn.cursor() as cursor:
            cursor.execute(f"SET search_path TO {schema_name}")
            pg_conn.commit()
        
        # Read the SQL file
        with open(sql_path, "r") as file:
            sql_content = file.read()
        
        # Split SQL by semicolon to execute each statement separately
        # This allows us to handle errors more gracefully
        statements = sql_content.split(';')
        
        with pg_conn.cursor() as cursor:
            for statement in statements:
                if statement.strip():  # Skip empty statements
                    try:
                        cursor.execute(statement)
                        pg_conn.commit()
                    except Exception as e:
                        pg_conn.rollback()
                        print(f"⚠️ Error executing statement: {e}")
                        print(f"Statement: {statement[:100]}...")  # Print first 100 chars of statement
        
        print(f"✅ Tables created successfully in schema {schema_name}.")
    except Exception as e:
        pg_conn.rollback()
        print(f"❌ Error creating tables: {e}")
        traceback.print_exc()

# === Function to migrate workflow data to PostgreSQL schema ===
def migrate_workflow_data_to_schema(pg_conn, workflow_data, schema_name):
    """
    Migrate workflow data to PostgreSQL schema.
    This function handles the actual data migration to the specified schema.
    """
    schema_name="workflow_runtime"
    cursor = None
    try:
        cursor = pg_conn.cursor()
        
        # Set search path to the tenant's schema
        cursor.execute(f"SET search_path TO {schema_name}")
        pg_conn.commit()
        
        # ===== STEP 1: BASE ENTITIES =====
        print("\n==== STEP 1: INSERTING BASE ENTITIES ====")
        
        # Insert Tenant
        tenant = workflow_data.get("tenant", {})
        tenant_id = tenant.get("id", "T001")
        tenant_name = tenant.get("name", "DefaultTenant")
        print(f"➡️ Inserting Tenant: {tenant_name}...")
        try:
            cursor.execute(
                "INSERT INTO tenants (tenant_id, name) VALUES (%s, %s) ON CONFLICT DO NOTHING",
                (tenant_id, tenant_name)
            )
            pg_conn.commit()
            print(f"✅ Inserted Tenant: {tenant_name}")
        except Exception as e:
            pg_conn.rollback()
            print(f"❌ Error inserting tenant: {e}")
        
        # Insert Permission Types
        print("\n➡️ Inserting Permission Types...")
        for permission in workflow_data.get("permission_types", []):
            try:
                permission_id = permission.get("id", "")
                description = permission.get("description", "")
                capabilities = permission.get("capabilities", [])
                
                cursor.execute(
                    """
                    INSERT INTO permission_types
                    (permission, description, capabilities)
                    VALUES (%s, %s, %s)
                    ON CONFLICT DO NOTHING
                    """,
                    (permission_id, description, Json(capabilities) if capabilities else None)
                )
                pg_conn.commit()
                print(f"✅ Inserted Permission Type: {permission_id}")
            except Exception as e:
                pg_conn.rollback()
                print(f"❌ Error inserting permission type {permission.get('id', 'unknown')}: {e}")
        
        # Insert Roles
        print("\n➡️ Inserting Roles...")
        for role in tenant.get("roles", []):
            try:
                role_id = role.get("id", "")
                role_name = role.get("name", "")
                inherits_from = role.get("inherits_from")
                
                cursor.execute(
                    """
                    INSERT INTO roles (role_id, name, tenant_id, inherits_from)
                    VALUES (%s, %s, %s, %s)
                    ON CONFLICT (role_id) DO NOTHING
                    """,
                    (role_id, role_name, tenant_id, inherits_from)
                )
                pg_conn.commit()
                print(f"✅ Inserted Role: {role_id} - {role_name}")
            except Exception as e:
                pg_conn.rollback()
                print(f"❌ Error inserting role {role.get('id', 'unknown')}: {e}")
        
        # Insert Entities (handle duplicates that might exist in MongoDB JSON)
        print("\n➡️ Inserting Entities...")
        processed_entities = set()  # Keep track of already processed entities
        
        for entity in workflow_data.get("entities", []):
            try:
                entity_id = entity.get("id", "")
                # Skip if we've already processed this entity (due to duplication in JSON)
                if entity_id in processed_entities:
                    print(f"⚠️ Skipping duplicate entity: {entity_id}")
                    continue
                
                name = entity.get("name", "")
                version = entity.get("version", "1.0")
                status = entity.get("status", "")
                entity_type = entity.get("type", "")
                attribute_prefix = entity.get("attributes_metadata", {}).get("attribute_prefix", "")
                description = entity.get("description", "")
                
                cursor.execute(
                    """
                    INSERT INTO entities 
                    (entity_id, name, version, status, type, attribute_prefix, description)
                    VALUES (%s, %s, %s, %s, %s, %s, %s)
                    ON CONFLICT (entity_id) DO NOTHING
                    """,
                    (entity_id, name, version, status, entity_type, attribute_prefix, description)
                )
                pg_conn.commit()
                processed_entities.add(entity_id)
                print(f"✅ Inserted Entity: {entity_id} - {name}")
            except Exception as e:
                pg_conn.rollback()
                print(f"❌ Error inserting entity {entity.get('id', 'unknown')}: {e}")
        
        # ===== STEP 2: ENTITY ATTRIBUTES AND METADATA =====
        print("\n==== STEP 2: INSERTING ENTITY ATTRIBUTES AND METADATA ====")
        
        # Insert Entity Attribute Metadata
        print("\n➡️ Inserting Entity Attribute Metadata...")
        for entity in workflow_data.get("entities", []):
            entity_id = entity.get("id", "")
            if entity_id not in processed_entities:
                print(f"⚠️ Skipping metadata for unprocessed entity: {entity_id}")
                continue
                
            for attr_id, attr_name in entity.get("attributes_metadata", {}).get("attribute_map", {}).items():
                required = attr_id in entity.get("attributes_metadata", {}).get("required_attributes", [])
                try:
                    cursor.execute(
                        """
                        INSERT INTO entity_attribute_metadata 
                        (entity_id, attribute_id, attribute_name, required)
                        VALUES (%s, %s, %s, %s)
                        ON CONFLICT DO NOTHING
                        """,
                        (entity_id, attr_id, attr_name, required)
                    )
                    pg_conn.commit()
                    print(f"✅ Inserted Entity Attribute Metadata: {entity_id}.{attr_id}")
                except Exception as e:
                    pg_conn.rollback()
                    print(f"❌ Error inserting entity attribute metadata {attr_id}: {e}")
        
        # Insert Entity Attributes
        print("\n➡️ Inserting Entity Attributes...")
        for entity in workflow_data.get("entities", []):
            entity_id = entity.get("id", "")
            if entity_id not in processed_entities:
                print(f"⚠️ Skipping attributes for unprocessed entity: {entity_id}")
                continue
                
            for attr in entity.get("attributes", []):
                try:
                    attribute_id = attr.get("id", "")
                    name = attr.get("name", "")
                    display_name = attr.get("display_name", "")
                    datatype = attr.get("datatype", "")
                    version = attr.get("version", "1.0")
                    status = attr.get("status", "")
                    required = attr.get("required", False)
                    reference_entity_id = attr.get("references")
                    
                    cursor.execute(
                        """
                        INSERT INTO entity_attributes 
                        (attribute_id, entity_id, name, display_name, datatype, version, status, required, reference_entity_id)
                        VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s)
                        ON CONFLICT (entity_id, attribute_id) DO NOTHING
                        """,
                        (attribute_id, entity_id, name, display_name, datatype, version, status, required, reference_entity_id)
                    )
                    pg_conn.commit()
                    print(f"✅ Inserted Entity Attribute: {entity_id}.{attribute_id}")
                    
                    # Insert Attribute Validations
                    for validation in attr.get("validations", []):
                        try:
                            rule = validation.get("rule", "")
                            expression = validation.get("expression", "")
                            
                            cursor.execute(
                                """
                                INSERT INTO attribute_validations 
                                (attribute_id, rule, expression)
                                VALUES (%s, %s, %s)
                                ON CONFLICT DO NOTHING
                                """,
                                (attribute_id, rule, expression)
                            )
                            pg_conn.commit()
                            print(f"✅ Inserted Validation for {attribute_id}: {rule}")
                        except Exception as e:
                            pg_conn.rollback()
                            print(f"❌ Error inserting validation for {attribute_id}: {e}")
                    
                    # Insert Enum Values
                    if datatype.lower() == "enum":
                        for value in attr.get("values", []):
                            try:
                                cursor.execute(
                                    """
                                    INSERT INTO attribute_enum_values 
                                    (attribute_id, value)
                                    VALUES (%s, %s)
                                    ON CONFLICT DO NOTHING
                                    """,
                                    (attribute_id, value)
                                )
                                pg_conn.commit()
                                print(f"✅ Inserted Enum Value for {attribute_id}: {value}")
                            except Exception as e:
                                pg_conn.rollback()
                                print(f"❌ Error inserting enum value {value} for {attribute_id}: {e}")
                    
                except Exception as e:
                    pg_conn.rollback()
                    print(f"❌ Error inserting entity attribute {attr.get('id', 'unknown')}: {e}")
        
        # ===== STEP 3: ENTITY RELATIONSHIPS =====
        print("\n==== STEP 3: INSERTING ENTITY RELATIONSHIPS ====")
        
        print("\n➡️ Inserting Entity Relationships...")
        for entity in workflow_data.get("entities", []):
            entity_id = entity.get("id", "")
            if entity_id not in processed_entities:
                print(f"⚠️ Skipping relationships for unprocessed entity: {entity_id}")
                continue
                
            for relationship in entity.get("relationships", []):
                try:
                    target_entity_id = relationship.get("entity_id", "")
                    relationship_type = relationship.get("type", "")
                    source_attribute_id = relationship.get("through_attribute", "")
                    target_attribute_id = relationship.get("to_attribute", "")
                    
                    cursor.execute(
                        """
                        INSERT INTO entity_relationships 
                        (source_entity_id, target_entity_id, relationship_type, source_attribute_id, target_attribute_id)
                        VALUES (%s, %s, %s, %s, %s)
                        ON CONFLICT DO NOTHING
                        """,
                        (entity_id, target_entity_id, relationship_type, source_attribute_id, target_attribute_id)
                    )
                    pg_conn.commit()
                    print(f"✅ Inserted Relationship: {entity_id} → {target_entity_id}")
                except Exception as e:
                    pg_conn.rollback()
                    print(f"❌ Error inserting relationship from {entity_id} to {relationship.get('entity_id', 'unknown')}: {e}")
        
        # ===== STEP 4: PERMISSIONS =====
        print("\n==== STEP 4: INSERTING PERMISSIONS ====")
        
        # Insert Entity Permissions
        print("\n➡️ Inserting Entity Permissions...")
        for role in tenant.get("roles", []):
            role_id = role.get("id", "")
            for entity_access in role.get("access", {}).get("entities", []):
                entity_id = entity_access.get("entity_id", "")
                for permission in entity_access.get("permissions", []):
                    try:
                        cursor.execute(
                            """
                            INSERT INTO entity_permissions 
                            (role_id, entity_id, permission_id)
                            VALUES (%s, %s, %s)
                            ON CONFLICT DO NOTHING
                            """,
                            (role_id, entity_id, permission)
                        )
                        pg_conn.commit()
                        print(f"✅ Inserted Entity Permission: Role {role_id} - Entity {entity_id} - Permission {permission}")
                    except Exception as e:
                        pg_conn.rollback()
                        print(f"❌ Error inserting entity permission {permission} for role {role_id}: {e}")
        
        # Insert Objective Permissions
        print("\n➡️ Inserting Objective Permissions...")
        for role in tenant.get("roles", []):
            role_id = role.get("id", "")
            for objective_access in role.get("access", {}).get("objectives", []):
                objective_id = objective_access.get("objective_id", "")
                for permission in objective_access.get("permissions", []):
                    try:
                        cursor.execute(
                            """
                            INSERT INTO objective_permissions 
                            (role_id, objective_id, permission_id)
                            VALUES (%s, %s, %s)
                            ON CONFLICT DO NOTHING
                            """,
                            (role_id, objective_id, permission)
                        )
                        pg_conn.commit()
                        print(f"✅ Inserted Objective Permission: Role {role_id} - Objective {objective_id} - Permission {permission}")
                    except Exception as e:
                        pg_conn.rollback()
                        print(f"❌ Error inserting objective permission {permission} for role {role_id}: {e}")
        
        # ===== STEP 5: GLOBAL OBJECTIVES =====
        print("\n==== STEP 5: INSERTING GLOBAL OBJECTIVES ====")
        
        # Insert Global Objectives
        print("\n➡️ Inserting Global Objectives...")
        for go in workflow_data.get("global_objectives", []):
            try:
                go_id = go.get("id", "")
                name = go.get("name", "")
                version = go.get("version", "1.0")
                status = go.get("status", "")
                description = go.get("description", "")
                
                cursor.execute(
                    """
                    INSERT INTO global_objectives 
                    (go_id, name, version, status, description)
                    VALUES (%s, %s, %s, %s, %s)
                    ON CONFLICT (go_id) DO NOTHING
                    """,
                    (go_id, name, version, status, description)
                )
                pg_conn.commit()
                print(f"✅ Inserted Global Objective: {go_id} - {name}")
                
                # Insert Input Stack
                if "input_stack" in go:
                    input_stack_description = go["input_stack"].get("description", "")
                    
                    cursor.execute(
                        """
                        INSERT INTO input_stack (go_id, description)
                        VALUES (%s, %s)
                        ON CONFLICT DO NOTHING
                        RETURNING id
                        """,
                        (go_id, input_stack_description)
                    )
                    
                    try:
                        result = cursor.fetchone()
                        pg_conn.commit()
                        
                        if result:
                            input_stack_id = result[0]
                            print(f"✅ Created Input Stack for {go_id}")
                            
                            # Insert Input Items
                            for input_item in go["input_stack"].get("inputs", []):
                                item_id = input_item.get("id", "")
                                slot_id = input_item.get("slot_id", "")
                                contextual_id = input_item.get("contextual_id", "")
                                entity_reference = input_item.get("entity_reference", "")
                                attribute_reference = input_item.get("attribute_reference", "")
                                source_type = input_item["source"]["type"]
                                source_description = input_item["source"].get("description", "")
                                required = input_item.get("required", False)
                                
                                try:
                                    cursor.execute(
                                        """
                                        INSERT INTO input_items 
                                        (id, input_stack_id, slot_id, contextual_id, entity_reference, 
                                         attribute_reference, source_type, source_description, required)
                                        VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s)
                                        ON CONFLICT DO NOTHING
                                        """,
                                        (item_id, input_stack_id, slot_id, contextual_id, entity_reference, 
                                         attribute_reference, source_type, source_description, required)
                                    )
                                    pg_conn.commit()
                                    print(f"✅ Inserted Input Item: {item_id}")
                                except Exception as e:
                                    pg_conn.rollback()
                                    print(f"❌ Error inserting input item {item_id}: {e}")
                            
                            # Insert System Functions for Input Stack
                            for function in go["input_stack"].get("system_functions", []):
                                function_id = function.get("function_id", "")
                                function_name = function.get("function_name", "")
                                function_type = function.get("function_type", "")
                                parameters = function.get("parameters", {})
                                output_to = function.get("output_to", "")
                                
                                try:
                                    cursor.execute(
                                        """
                                        INSERT INTO system_functions 
                                        (function_id, function_name, function_type, stack_type, stack_id, parameters, output_to)
                                        VALUES (%s, %s, %s, %s, %s, %s, %s)
                                        ON CONFLICT DO NOTHING
                                        """,
                                        (function_id, function_name, function_type, "input", input_stack_id, 
                                         Json(parameters), output_to)
                                    )
                                    pg_conn.commit()
                                    print(f"✅ Inserted System Function: {function_id}")
                                except Exception as e:
                                    pg_conn.rollback()
                                    print(f"❌ Error inserting system function {function_id}: {e}")
                        else:
                            # Get existing input stack ID
                            cursor.execute(
                                """
                                SELECT id FROM input_stack WHERE go_id = %s
                                """,
                                (go_id,)
                            )
                            result = cursor.fetchone()
                            if result:
                                print(f"⚠️ Input Stack for {go_id} already exists")
                            else:
                                print(f"⚠️ Failed to create Input Stack for {go_id}")
                    except Exception as e:
                        pg_conn.rollback()
                        print(f"⚠️ Error processing input stack: {e}")
                
                # Insert Output Stack
                if "output_stack" in go:
                    output_stack_description = go["output_stack"].get("description", "")
                    
                    cursor.execute(
                        """
                        INSERT INTO output_stack (go_id, description)
                        VALUES (%s, %s)
                        ON CONFLICT DO NOTHING
                        RETURNING id
                        """,
                        (go_id, output_stack_description)
                    )
                    
                    try:
                        result = cursor.fetchone()
                        pg_conn.commit()
                        
                        if result:
                            output_stack_id = result[0]
                            print(f"✅ Created Output Stack for {go_id}")
                            
                            # Insert Output Items
                            for output_item in go["output_stack"].get("outputs", []):
                                item_id = output_item.get("id", "")
                                slot_id = output_item.get("slot_id", "")
                                contextual_id = output_item.get("contextual_id", "")
                                output_entity = output_item.get("output_entity", "")
                                output_attribute = output_item.get("output_attribute", "")
                                data_type = output_item.get("data_type", "")
                                
                                try:
                                    cursor.execute(
                                        """
                                        INSERT INTO output_items 
                                        (id, output_stack_id, slot_id, contextual_id, output_entity, output_attribute, data_type)
                                        VALUES (%s, %s, %s, %s, %s, %s, %s)
                                        ON CONFLICT DO NOTHING
                                        """,
                                        (item_id, output_stack_id, slot_id, contextual_id, output_entity, output_attribute, data_type)
                                    )
                                    pg_conn.commit()
                                    print(f"✅ Inserted Output Item: {item_id}")
                                    
                                    # Insert Output Triggers
                                    if "triggers" in output_item:
                                        for trigger in output_item["triggers"]["items"]:
                                            trigger_id = trigger.get("id", "")
                                            target_objective = trigger.get("target_objective", "")
                                            target_input = trigger.get("target_input", "")
                                            mapping_type = trigger.get("mapping_type", "")
                                            
                                            condition = trigger.get("condition", {})
                                            condition_type = condition.get("condition_type", "")
                                            condition_entity = condition.get("entity", "")
                                            condition_attribute = condition.get("attribute", "")
                                            condition_operator = condition.get("operator", "")
                                            condition_value = condition.get("value", "")
                                            
                                            try:
                                                cursor.execute(
                                                    """
                                                    INSERT INTO output_triggers 
                                                    (id, output_item_id, output_stack_id, target_objective, target_input, 
                                                     mapping_type, condition_type, condition_entity, condition_attribute, 
                                                     condition_operator, condition_value)
                                                    VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                                                    ON CONFLICT DO NOTHING
                                                    """,
                                                    (trigger_id, item_id, output_stack_id, target_objective, target_input, 
                                                     mapping_type, condition_type, condition_entity, condition_attribute, 
                                                     condition_operator, condition_value)
                                                )
                                                pg_conn.commit()
                                                print(f"✅ Inserted Output Trigger: {trigger_id}")
                                            except Exception as e:
                                                pg_conn.rollback()
                                                print(f"❌ Error inserting output trigger {trigger_id}: {e}")
                                except Exception as e:
                                    pg_conn.rollback()
                                    print(f"❌ Error inserting output item {item_id}: {e}")
                        else:
                            # Get existing output stack ID
                            cursor.execute(
                                """
                                SELECT id FROM output_stack WHERE go_id = %s
                                """,
                                (go_id,)
                            )
                            result = cursor.fetchone()
                            if result:
                                print(f"⚠️ Output Stack for {go_id} already exists")
                            else:
                                print(f"⚠️ Failed to create Output Stack for {go_id}")
                    except Exception as e:
                        pg_conn.rollback()
                        print(f"⚠️ Error processing output stack: {e}")
                
                # Insert Data Mapping Stack
                if "data_mapping_stack" in go:
                    mapping_stack_description = go["data_mapping_stack"].get("description", "")
                    
                    cursor.execute(
                        """
                        INSERT INTO data_mapping_stack (go_id, description)
                        VALUES (%s, %s)
                        ON CONFLICT DO NOTHING
                        RETURNING id
                        """,
                        (go_id, mapping_stack_description)
                    )
                    
                    try:
                        result = cursor.fetchone()
                        pg_conn.commit()
                        
                        if result:
                            mapping_stack_id = result[0]
                            print(f"✅ Created Data Mapping Stack for {go_id}")
                            
                            # Insert Data Mappings
                            for mapping in go["data_mapping_stack"].get("mappings", []):
                                mapping_id = mapping.get("id", "")
                                source = mapping.get("source", "")
                                target = mapping.get("target", "")
                                mapping_type = mapping.get("mapping_type", "")
                                
                                try:
                                    cursor.execute(
                                        """
                                        INSERT INTO data_mappings 
                                        (id, mapping_stack_id, source, target, mapping_type)
                                        VALUES (%s, %s, %s, %s, %s)
                                        ON CONFLICT DO NOTHING
                                        """,
                                        (mapping_id, mapping_stack_id, source, target, mapping_type)
                                    )
                                    pg_conn.commit()
                                    print(f"✅ Inserted Data Mapping: {mapping_id}")
                                except Exception as e:
                                    pg_conn.rollback()
                                    print(f"❌ Error inserting data mapping {mapping_id}: {e}")
                            
                            # Insert Mapping Rules
                            for rule in go["data_mapping_stack"].get("rules", []):
                                rule_id = rule.get("id", "")
                                description = rule.get("description", "")
                                
                                rule_condition = rule.get("rule_condition", {})
                                condition_type = rule_condition.get("condition_type", "")
                                condition_entity = rule_condition.get("entity", "")
                                condition_attribute = rule_condition.get("attribute", "")
                                condition_operator = rule_condition.get("operator", "")
                                condition_value = rule_condition.get("value", "")
                                error_message = rule.get("error_message", "")
                                
                                try:
                                    cursor.execute(
                                        """
                                        INSERT INTO mapping_rules 
                                        (id, mapping_stack_id, description, condition_type, condition_entity, 
                                         condition_attribute, condition_operator, condition_value, error_message)
                                        VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s)
                                        ON CONFLICT DO NOTHING
                                        """,
                                        (rule_id, mapping_stack_id, description, condition_type, condition_entity, 
                                         condition_attribute, condition_operator, condition_value, error_message)
                                    )
                                    pg_conn.commit()
                                    print(f"✅ Inserted Mapping Rule: {rule_id}")
                                except Exception as e:
                                    pg_conn.rollback()
                                    print(f"❌ Error inserting mapping rule {rule_id}: {e}")
                        else:
                            # Get existing mapping stack ID
                            try:
                                cursor.execute(
                                    """
                                    SELECT id FROM data_mapping_stack WHERE go_id = %s
                                    """,
                                    (go_id,)
                                )
                                result = cursor.fetchone()
                                if result:
                                    print(f"⚠️ Data Mapping Stack for {go_id} already exists")
                                else:
                                    print(f"⚠️ Failed to create Data Mapping Stack for {go_id}")
                            except Exception as e:
                                print(f"⚠️ Error checking data mapping stack: {e}")
                    except Exception as e:
                        pg_conn.rollback()
                        print(f"⚠️ Error processing data mapping stack: {e}")
                
            except Exception as e:
                pg_conn.rollback()
                print(f"❌ Error processing Global Objective {go.get('id', 'unknown')}: {e}")
                traceback.print_exc()
        
        # ===== STEP 6: LOCAL OBJECTIVES =====
        print("\n==== STEP 6: INSERTING LOCAL OBJECTIVES ====")
        
        # First insert all local objectives without relationships
        print("\n➡️ Inserting base Local Objectives...")
        local_objectives = workflow_data.get("local_objectives", [])
        if not local_objectives:
            print("⚠️ No local objectives found in data.")
        else:
            for lo in local_objectives:
                try:
                    lo_id = lo.get("id", "").lower()  # Ensure consistent casing
                    contextual_id = lo.get("contextual_id", "")
                    name = lo.get("name", "")
                    function_type = lo.get("function_type", "")
                    workflow_source = lo.get("workflow_source", "origin")
                    go_id = contextual_id.split(".")[0] if "." in contextual_id else ""  # Extract GO ID from contextual ID
                    
                    cursor.execute(
                        """
                        INSERT INTO local_objectives 
                        (lo_id, contextual_id, name, function_type, workflow_source, go_id)
                        VALUES (%s, %s, %s, %s, %s, %s)
                        ON CONFLICT (lo_id) DO NOTHING
                        """,
                        (lo_id, contextual_id, name, function_type, workflow_source, go_id)
                    )
                    pg_conn.commit()
                    print(f"✅ Inserted Local Objective: {lo_id} - {name}")
                except Exception as e:
                    pg_conn.rollback()
                    print(f"❌ Error inserting local objective {lo.get('id', 'unknown')}: {e}")
            
            # Now insert relationships and additional data
            print("\n➡️ Inserting Local Objective relationships and data...")
            for lo in local_objectives:
                try:
                    lo_id = lo.get("id", "").lower()  # Ensure consistent casing
                    
                    # Insert execution pathway
                    if "execution_pathway" in lo:
                        pathway = lo["execution_pathway"]
                        pathway_type = pathway.get("type", "")
                        
                        if pathway_type.lower() == "sequential" and "next_lo" in pathway:
                            next_lo = pathway["next_lo"].lower()  # Ensure consistent casing
                            
                            cursor.execute(
                                """
                                INSERT INTO execution_pathways
                                (lo_id, pathway_type, next_lo)
                                VALUES (%s, %s, %s)
                                ON CONFLICT (lo_id) DO NOTHING
                                """,
                                (lo_id, pathway_type, next_lo)
                            )
                            pg_conn.commit()
                            print(f"✅ Inserted Sequential Execution Pathway: {lo_id} -> {next_lo}")
                        
                        elif pathway_type.lower() == "alternative" and "conditions" in pathway:
                            for condition in pathway["conditions"]:
                                cond = condition["condition"]
                                next_lo = condition["next_lo"].lower()  # Ensure consistent casing
                                
                                cursor.execute(
                                    """
                                    INSERT INTO execution_pathway_conditions
                                    (lo_id, condition_type, condition_entity, condition_attribute, 
                                    condition_operator, condition_value, next_lo)
                                    VALUES (%s, %s, %s, %s, %s, %s, %s)
                                    ON CONFLICT DO NOTHING
                                    """,
                                    (
                                        lo_id,
                                        cond["condition_type"],
                                        cond["entity"].lower(),
                                        cond["attribute"],
                                        cond["operator"],
                                        cond["value"],
                                        next_lo
                                    )
                                )
                                pg_conn.commit()
                                print(f"✅ Inserted Alternative Execution Pathway Condition: {lo_id} -> {next_lo}")
                    
                    # Insert agent stack
                    if "agent_stack" in lo and "agents" in lo["agent_stack"]:
                        # First create the agent stack
                        cursor.execute(
                            """
                            INSERT INTO agent_stack (lo_id)
                            VALUES (%s)
                            ON CONFLICT (lo_id) DO NOTHING
                            RETURNING id
                            """,
                            (lo_id,)
                        )
                        result = cursor.fetchone()
                        pg_conn.commit()
                        
                        if result:
                            agent_stack_id = result[0]
                            print(f"✅ Created Agent Stack for {lo_id}")
                            
                            # Insert agent rights
                            for agent in lo["agent_stack"]["agents"]:
                                role_id = agent["role"]  # Keep original case
                                print(f"DEBUG: Role ID from YAML: '{role_id}'")
                                
                                # Check if the role exists before inserting
                                cursor.execute("SELECT 1 FROM roles WHERE role_id = %s", (role_id,))
                                role_exists = cursor.fetchone()
                                
                                if not role_exists:
                                    print(f"⚠️ Role {role_id} not found with original case, trying case-insensitive search")
                                    # Try with case-insensitive search
                                    cursor.execute("SELECT role_id FROM roles WHERE LOWER(role_id) = LOWER(%s)", (role_id,))
                                    role_result = cursor.fetchone()
                                    
                                    if role_result:
                                        print(f"⚠️ Found role with different case: {role_result[0]}")
                                        role_id = role_result[0]  # Use the role_id with the case found in the database
                                    else:
                                        print(f"⚠️ Role {role_id} does not exist in the roles table. Creating it...")
                                        cursor.execute(
                                            """
                                            INSERT INTO roles (role_id, name, tenant_id)
                                            VALUES (%s, %s, %s)
                                            ON CONFLICT DO NOTHING
                                            """,
                                            (role_id, f"Auto-generated {role_id}", tenant_id)
                                        )
                                        pg_conn.commit()
                                        print(f"✅ Created missing role: {role_id}")
                                
                                # Now insert the agent rights with confirmed role_id
                                for right in agent.get("rights", []):
                                    cursor.execute(
                                        """
                                        INSERT INTO agent_rights
                                        (agent_stack_id, role_id, right_id)
                                        VALUES (%s, %s, %s)
                                        ON CONFLICT DO NOTHING
                                        """,
                                        (agent_stack_id, role_id, right.lower())
                                    )
                                    pg_conn.commit()
                                print(f"✅ Inserted Agent Rights for role {role_id} in stack for {lo_id}")
                        else:
                            # Get existing agent stack ID
                            cursor.execute(
                                """
                                SELECT id FROM agent_stack WHERE lo_id = %s
                                """,
                                (lo_id,)
                            )
                            result = cursor.fetchone()
                            if result:
                                agent_stack_id = result[0]
                                print(f"⚠️ Using existing Agent Stack for {lo_id}")
                                
                                # Insert agent rights (same as above)
                                for agent in lo["agent_stack"]["agents"]:
                                    role_id = agent["role"]  # Keep original case
                                    
                                    # Check if the role exists before inserting
                                    cursor.execute("SELECT 1 FROM roles WHERE role_id = %s", (role_id,))
                                    role_exists = cursor.fetchone()
                                    
                                    if not role_exists:
                                        print(f"⚠️ Role {role_id} not found with original case, trying case-insensitive search")
                                        # Try with case-insensitive search
                                        cursor.execute("SELECT role_id FROM roles WHERE LOWER(role_id) = LOWER(%s)", (role_id,))
                                        role_result = cursor.fetchone()
                                        
                                        if role_result:
                                            print(f"⚠️ Found role with different case: {role_result[0]}")
                                            role_id = role_result[0]  # Use the role_id with the case found in the database
                                        else:
                                            print(f"⚠️ Role {role_id} does not exist in the roles table. Creating it...")
                                            cursor.execute(
                                                """
                                                INSERT INTO roles (role_id, name, tenant_id)
                                                VALUES (%s, %s, %s)
                                                ON CONFLICT DO NOTHING
                                                """,
                                                (role_id, f"Auto-generated {role_id}", tenant_id)
                                            )
                                            pg_conn.commit()
                                            print(f"✅ Created missing role: {role_id}")
                                    
                                    # Now insert the agent rights with confirmed role_id
                                    for right in agent.get("rights", []):
                                        cursor.execute(
                                            """
                                            INSERT INTO agent_rights
                                            (agent_stack_id, role_id, right_id)
                                            VALUES (%s, %s, %s)
                                            ON CONFLICT DO NOTHING
                                            """,
                                            (agent_stack_id, role_id, right.lower())
                                        )
                                        pg_conn.commit()
                                    print(f"✅ Inserted Agent Rights for role {role_id} in existing stack for {lo_id}")
                            else:
                                print(f"⚠️ Cannot find agent stack for {lo_id}")
                    
                    # Insert input stack
                    if "input_stack" in lo:
                        cursor.execute(
                            """
                            INSERT INTO lo_input_stack (lo_id, description)
                            VALUES (%s, %s)
                            ON CONFLICT (lo_id) DO NOTHING
                            RETURNING id
                            """,
                            (lo_id, lo["input_stack"].get("description", ""))
                        )
                        result = cursor.fetchone()
                        pg_conn.commit()
                        
                        if result:
                            input_stack_id = result[0]
                            print(f"✅ Created Input Stack for {lo_id}")
                        else:
                            # Get existing input stack ID
                            cursor.execute(
                                """
                                SELECT id FROM lo_input_stack WHERE lo_id = %s
                                """,
                                (lo_id,)
                            )
                            result = cursor.fetchone()
                            if result:
                                input_stack_id = result[0]
                                print(f"⚠️ Using existing Input Stack for {lo_id}")
                            else:
                                print(f"⚠️ Cannot find input stack for {lo_id}")
                                continue
                        
                        # Insert input items
                        if "inputs" in lo["input_stack"]:
                            for input_item in lo["input_stack"]["inputs"]:
                                try:
                                    item_id = input_item["id"]
                                    slot_id = input_item["slot_id"]
                                    contextual_id = input_item["contextual_id"]
                                    source_type = input_item["source"]["type"].lower()
                                    source_description = input_item["source"].get("description", "")
                                    required = input_item.get("required", False)
                                    data_type = input_item.get("data_type", "string").lower()
                                    ui_control = input_item.get("ui_control", "")
                                    
                                    # Extract nested function data if present
                                    nested_function = Json(input_item.get("nested_function", {})) if "nested_function" in input_item else None
                                    nested_functions = Json(input_item.get("nested_functions", [])) if "nested_functions" in input_item else None
                                    
                                    cursor.execute(
                                        """
                                        INSERT INTO lo_input_items
                                        (id, input_stack_id, slot_id, contextual_id, source_type, source_description,
                                        required, data_type, ui_control, nested_function, nested_functions)
                                        VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                                        ON CONFLICT DO NOTHING
                                        """,
                                        (
                                            item_id,
                                            input_stack_id,
                                            slot_id,
                                            contextual_id,
                                            source_type,
                                            source_description,
                                            required,
                                            data_type,
                                            ui_control,
                                            nested_function,
                                            nested_functions
                                        )
                                    )
                                    pg_conn.commit()
                                    print(f"✅ Inserted Input Item {item_id} for {lo_id}")
                                    
                                    # Insert input validations
                                    if "validations" in input_item:
                                        for validation in input_item["validations"]:
                                            cursor.execute(
                                                """
                                                INSERT INTO lo_input_validations
                                                (input_item_id, input_stack_id, rule, rule_type, error_message)
                                                VALUES (%s, %s, %s, %s, %s)
                                                ON CONFLICT DO NOTHING
                                                """,
                                                (
                                                    item_id,
                                                    input_stack_id,
                                                    validation["rule"],
                                                    validation["rule_type"],
                                                    ""  # No error_message in yaml
                                                )
                                            )
                                            pg_conn.commit()
                                        print(f"✅ Inserted Validations for Input Item {item_id}")
                                except Exception as e:
                                    pg_conn.rollback()
                                    print(f"⚠️ Error with input item {input_item.get('id', 'unknown')}: {e}")
                                    continue
                    
                    # Insert output stack
                    if "output_stack" in lo:
                        cursor.execute(
                            """
                            INSERT INTO lo_output_stack (lo_id, description)
                            VALUES (%s, %s)
                            ON CONFLICT (lo_id) DO NOTHING
                            RETURNING id
                            """,
                            (lo_id, lo["output_stack"].get("description", ""))
                        )
                        result = cursor.fetchone()
                        pg_conn.commit()
                        
                        if result:
                            output_stack_id = result[0]
                            print(f"✅ Created Output Stack for {lo_id}")
                        else:
                            # Get existing output stack ID
                            cursor.execute(
                                """
                                SELECT id FROM lo_output_stack WHERE lo_id = %s
                                """,
                                (lo_id,)
                            )
                            result = cursor.fetchone()
                            if result:
                                output_stack_id = result[0]
                                print(f"⚠️ Using existing Output Stack for {lo_id}")
                            else:
                                print(f"⚠️ Cannot find output stack for {lo_id}")
                                continue
                        
                        # Insert output items
                        if "outputs" in lo["output_stack"]:
                            for output_item in lo["output_stack"]["outputs"]:
                                try:
                                    item_id = output_item["id"]
                                    slot_id = output_item["slot_id"]
                                    contextual_id = output_item["contextual_id"]
                                    source_type = output_item["source"]["type"].lower()
                                    value = output_item["source"].get("value", "")
                                    data_type = output_item.get("data_type", "string").lower()
                                    
                                    cursor.execute(
                                        """
                                        INSERT INTO lo_output_items
                                        (id, output_stack_id, slot_id, contextual_id, source, value, data_type)
                                        VALUES (%s, %s, %s, %s, %s, %s, %s)
                                        ON CONFLICT DO NOTHING
                                        """,
                                        (
                                            item_id,
                                            output_stack_id,
                                            slot_id,
                                            contextual_id,
                                            source_type,
                                            value,
                                            data_type
                                        )
                                    )
                                    pg_conn.commit()
                                    print(f"✅ Inserted Output Item {item_id} for {lo_id}")
                                except Exception as e:
                                    pg_conn.rollback()
                                    print(f"⚠️ Error with output item {output_item.get('id', 'unknown')}: {e}")
                                    continue
                    
                    # Insert data mapping stack
                    if "data_mapping_stack" in lo:
                        cursor.execute(
                            """
                            INSERT INTO lo_data_mapping_stack (lo_id, description)
                            VALUES (%s, %s)
                            ON CONFLICT (lo_id) DO NOTHING
                            RETURNING id
                            """,
                            (lo_id, lo["data_mapping_stack"].get("description", ""))
                        )
                        result = cursor.fetchone()
                        pg_conn.commit()
                        
                        if result:
                            mapping_stack_id = result[0]
                            print(f"✅ Created Data Mapping Stack for {lo_id}")
                        else:
                            # Get existing mapping stack ID
                            cursor.execute(
                                """
                                SELECT id FROM lo_data_mapping_stack WHERE lo_id = %s
                                """,
                                (lo_id,)
                            )
                            result = cursor.fetchone()
                            if result:
                                mapping_stack_id = result[0]
                                print(f"⚠️ Using existing Data Mapping Stack for {lo_id}")
                            else:
                                print(f"⚠️ Cannot find data mapping stack for {lo_id}")
                                continue
                        
                        # Insert data mappings
                        if "mappings" in lo["data_mapping_stack"]:
                            for mapping in lo["data_mapping_stack"]["mappings"]:
                                try:
                                    mapping_id = mapping["id"]
                                    source = mapping["source"]
                                    target = mapping["target"]
                                    mapping_type = mapping["mapping_type"]
                                    
                                    cursor.execute(
                                        """
                                        INSERT INTO lo_data_mappings
                                        (id, mapping_stack_id, source, target, mapping_type)
                                        VALUES (%s, %s, %s, %s, %s)
                                        ON CONFLICT DO NOTHING
                                        """,
                                        (
                                            mapping_id,
                                            mapping_stack_id,
                                            source,
                                            target,
                                            mapping_type
                                        )
                                    )
                                    pg_conn.commit()
                                    print(f"✅ Inserted Data Mapping {mapping_id} for {lo_id}")
                                except Exception as e:
                                    pg_conn.rollback()
                                    print(f"⚠️ Error with data mapping {mapping.get('id', 'unknown')}: {e}")
                                    continue
                
                except Exception as e:
                    pg_conn.rollback()
                    print(f"❌ Error processing additional data for {lo.get('id', 'unknown')}: {e}")
                    traceback.print_exc()
                    continue
        
        print(f"\n🏁 Migration of data to PostgreSQL schema {schema_name} complete!")
        
    except Exception as e:
        if pg_conn and cursor:
            pg_conn.rollback()
        print(f"❌ Error migrating workflow data: {e}")
        traceback.print_exc()
    finally:
        if 'cursor' in locals() and cursor and not cursor.closed:
            cursor.close()

# === Direct MongoDB to PostgreSQL Migration using schemas ===
def direct_mongodb_to_postgres(mongodb_uri="mongodb://localhost:27017/", 
                              db_name="workflow_system", 
                              collection_name="workflow"):
    """
    Directly migrate data from MongoDB to PostgreSQL without using an intermediate file.
    Uses schemas for multi-tenant separation.
    """
    try:
        from pymongo import MongoClient
        
        # Connect to MongoDB
        print(f"➡️ Connecting to MongoDB at {mongodb_uri}...")
        client = MongoClient(mongodb_uri)
        db = client[db_name]
        collection = db[collection_name]
        
        print(f"➡️ Retrieving data from {db_name}.{collection_name}...")
        workflow_doc = collection.find_one()
        
        if not workflow_doc:
            print("❌ No data found in MongoDB collection.")
            return False
            
        # Extract workflow data
        workflow_data = workflow_doc.get("workflow_data", {})
        if not workflow_data:
            print("❌ No workflow_data found in MongoDB document.")
            return False
            
        # Get tenant ID
        tenant = workflow_data.get("tenant", {})
        tenant_id = tenant.get("id", "T001")
        print(f"➡️ Using tenant ID: {tenant_id}")
        
        # Connect to PostgreSQL
        print(f"➡️ Connecting to PostgreSQL...")
        pg_conn = connect_with_retry(PG_CONFIG)
        print(f"✅ Connected to PostgreSQL")
        
        # Check and create schema
        schema_name = check_and_create_schema(pg_conn, tenant_id)
        
        # Create tables in schema
        if os.path.exists(SQL_SCHEMA_FILE):
            print(f"➡️ Creating tables in schema {schema_name}...")
            create_tables_in_schema(pg_conn, SQL_SCHEMA_FILE, schema_name)
        
        # Migrate workflow data to schema
        migrate_workflow_data_to_schema(pg_conn, workflow_data, schema_name)
        
        # Close connections
        pg_conn.close()
        client.close()
        
        return True
    
    except Exception as e:
        print(f"❌ Error during direct MongoDB to PostgreSQL migration: {e}")
        traceback.print_exc()
        return False

# === File-based MongoDB to PostgreSQL Migration using schemas ===
def file_mongodb_to_postgres(json_file_path):
    """
    Migrate workflow data from a JSON file to PostgreSQL using schemas.
    """
    try:
        # Read data from JSON file
        print(f"➡️ Reading data from {json_file_path}...")
        with open(json_file_path, 'r') as f:
            workflow_doc = json.load(f)
        
        # Extract workflow data
        workflow_data = workflow_doc.get("workflow_data", {})
        if not workflow_data:
            print("❌ No workflow_data found in JSON document.")
            return False
            
        # Get tenant ID
        tenant = workflow_data.get("tenant", {})
        tenant_id = tenant.get("id", "T001")
        print(f"➡️ Using tenant ID: {tenant_id}")
        
        # Connect to PostgreSQL
        print(f"➡️ Connecting to PostgreSQL...")
        pg_conn = connect_with_retry(PG_CONFIG)
        print(f"✅ Connected to PostgreSQL")
        
        # Check and create schema
        schema_name = check_and_create_schema(pg_conn, tenant_id)
        
        # Create tables in schema
        if os.path.exists(SQL_SCHEMA_FILE):
            print(f"➡️ Creating tables in schema {schema_name}...")
            create_tables_in_schema(pg_conn, SQL_SCHEMA_FILE, schema_name)
        
        # Migrate workflow data to schema
        migrate_workflow_data_to_schema(pg_conn, workflow_data, schema_name)
        
        # Close connection
        pg_conn.close()
        
        return True
    
    except Exception as e:
        print(f"❌ Error during file-based migration: {e}")
        traceback.print_exc()
        return False

# === Main entry point ===
if __name__ == "__main__":
    
    mongodb_uri =  "mongodb://localhost:27017/"
    db_name =  "workflow_system"
    collection_name =  "workflow"
    direct_mongodb_to_postgres(mongodb_uri, db_name, collection_name)
    
   
