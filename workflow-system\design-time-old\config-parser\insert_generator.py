#!/usr/bin/env python3
"""
MongoDB to PostgreSQL Migration Script (Schema-Based)

This script migrates workflow data from MongoDB to a PostgreSQL database.
It creates a separate schema for each tenant within the same PostgreSQL database.
All tables include created_at and updated_at timestamps.

Usage:
    python migrate_mongodb_to_postgres.py

Configuration:
    Configure the database connection details and file paths in the script constants.
"""

import os
import sys
import signal
import time
import json
import traceback
import psycopg2
from psycopg2.extras import Json
from datetime import datetime

# === Configuration ===
PG_CONFIG = {
    "dbname": "workflow_system",  # Use a single database for all tenants
    "user": "postgres",
    "password": "workflow_postgres_secure_password",
    "host": "**********",
    "port": "5432"
}


MONGO_JSON_FILE = "workflow_data.json"  # Path to MongoDB JSON export file
SQL_SCHEMA_FILE = "db_schema.sql"  # Path to SQL schema file

def generate_inserts_from_drafts(mongo_uri="mongodb://localhost:27017/", db_name="workflow_system", collection="workflow"):
    return direct_mongodb_to_postgres(mongo_uri, db_name, collection)


# === Function to connect to PostgreSQL with retry ===
def connect_with_retry(db_config, retries=5, delay=1):
    """Attempt to connect to PostgreSQL with retries."""
    for attempt in range(retries):
        try:
            return psycopg2.connect(**db_config)
        except psycopg2.OperationalError:
            print(f"⏳ Waiting for DB to be ready... retry {attempt + 1}/{retries}")
            time.sleep(delay)
    raise Exception("❌ Could not connect to database after multiple retries.")

# === Function to check and create schema if needed ===
def check_and_create_schema(pg_conn, tenant_id):
    """Check if schema exists and create if needed."""
    #schema_name = f"tenant_{tenant_id.lower()}"
    schema_name = "workflow_runtime"

    try:
        with pg_conn.cursor() as cursor:
            # Check if schema exists
            cursor.execute("SELECT schema_name FROM information_schema.schemata WHERE schema_name = %s", (schema_name,))
            exists = cursor.fetchone()

           # if exists:
            #    print(f"⚠️ Schema {schema_name} already exists.")
                # recreate = input("Do you want to recreate the schema? (y/n): ").lower() == 'y'

                # if recreate:
                #     print(f"➡️ Dropping existing schema: {schema_name}")
                #     # Use CASCADE to drop all objects in the schema
                #     cursor.execute(f"DROP SCHEMA {schema_name} CASCADE")
                #     pg_conn.commit()
                #     print(f"✅ Dropped old schema: {schema_name}")

                #     cursor.execute(f"CREATE SCHEMA {schema_name}")
                #     pg_conn.commit()
                #     print(f"✅ Created schema: {schema_name}")
                # else:
                #     print(f"ℹ️ Using existing schema: {schema_name}")
            #else:
             #   print(f"➡️ Creating schema: {schema_name}")
              #  cursor.execute(f"CREATE SCHEMA {schema_name}")
              #  pg_conn.commit()
              #  print(f"✅ Created schema: {schema_name}")

            # Set the search path to our schema for this connection
            cursor.execute(f"SET search_path TO {schema_name}")
            pg_conn.commit()

        return schema_name

    except Exception as e:
        pg_conn.rollback()
        print(f"❌ Error checking/creating schema: {e}")
        traceback.print_exc()
        sys.exit(1)

# === Function to create database tables from SQL file in the specific schema ===
def create_tables_in_schema(pg_conn, sql_path, schema_name):
    """Create database tables from SQL schema file in the specified schema."""
    try:
        # First, ensure we're using the right schema
        with pg_conn.cursor() as cursor:
            cursor.execute(f"SET search_path TO {schema_name}")
            pg_conn.commit()

        # Read the SQL file
        with open(sql_path, "r") as file:
            sql_content = file.read()

        # Add created_at and updated_at columns to every CREATE TABLE statement
        sql_lines = sql_content.split('\n')
        modified_sql = []
        inside_create_table = False

        for line in sql_lines:
            if line.strip().startswith('CREATE TABLE IF NOT EXISTS'):
                inside_create_table = True
                modified_sql.append(line)
            elif inside_create_table and line.strip().endswith(');'):
                # Add timestamp columns before closing parenthesis
                indentation = line.split(');')[0]
                modified_sql.append(f"{indentation},")
                modified_sql.append(f"{indentation}    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,")
                modified_sql.append(f"{indentation}    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP);")
                inside_create_table = False
            else:
                modified_sql.append(line)

        # Join the modified lines back into a single string
        modified_sql_content = '\n'.join(modified_sql)

        # Split SQL by semicolon to execute each statement separately
        # This allows us to handle errors more gracefully
        statements = modified_sql_content.split(';')

        with pg_conn.cursor() as cursor:
            for statement in statements:
                if statement.strip():  # Skip empty statements
                    try:
                        cursor.execute(statement)
                        pg_conn.commit()
                    except Exception as e:
                        pg_conn.rollback()
                        print(f"⚠️ Error executing statement: {e}")
                        print(f"Statement: {statement[:100]}...")  # Print first 100 chars of statement

        print(f"✅ Tables created successfully in schema {schema_name} with timestamp columns.")
    except Exception as e:
        pg_conn.rollback()
        print(f"❌ Error creating tables: {e}")
        traceback.print_exc()

# === Function to migrate workflow data to PostgreSQL schema ===
def migrate_workflow_data_to_schema(pg_conn, workflow_data, schema_name):
    """
    Migrate workflow data to PostgreSQL schema.
    This function handles the actual data migration to the specified schema.
    """
    cursor = None
    try:
        cursor = pg_conn.cursor()

        # Set search path to the tenant's schema
        cursor.execute(f"SET search_path TO {schema_name}")
        pg_conn.commit()

        # Current timestamp for created_at and updated_at fields
        current_time = datetime.now()

        # ===== STEP 1: BASE ENTITIES =====
        print("\n==== STEP 1: INSERTING BASE ENTITIES ====")

        # Insert Tenant
        tenant = workflow_data.get("tenant", {})
        tenant_id = tenant.get("id", "T001").lower()
        tenant_name = tenant.get("name", "DefaultTenant").lower()
        print(f"➡️ Inserting Tenant: {tenant_name}...")
        try:
            cursor.execute(
                """
                INSERT INTO tenants
                (tenant_id, name, created_at, updated_at)
                VALUES (%s, %s, %s, %s)
                ON CONFLICT (tenant_id) DO NOTHING
                """,
                (tenant_id, tenant_name, current_time, current_time)
            )
            pg_conn.commit()
            print(f"✅ Inserted Tenant: {tenant_name}")
        except Exception as e:
            pg_conn.rollback()
            print(f"❌ Error inserting tenant: {e}")

        # Insert Permission Types
        print("\n➡️ Inserting Permission Types...")
        for permission in workflow_data.get("permission_types", []):
            try:
                permission_id = permission.get("id", "").lower()
                description = permission.get("description", "").capitalize()
                #capabilities = permission.get("capabilities", [])

                cursor.execute(
                    """
                    INSERT INTO permission_types
                    (permission_id, description)
                    VALUES (%s, %s)
                    ON CONFLICT (permission_id) DO NOTHING
                    """,
                    (
                        permission_id,
                        description
                    )
                )
                pg_conn.commit()
                print(f"✅ Inserted Permission Type: {permission_id}")
            except Exception as e:
                pg_conn.rollback()
                print(f"❌ Error inserting permission type {permission.get('id', 'unknown')}: {e}")

        # Insert Roles
        print("\n➡️ Inserting Roles...")
        for role in tenant.get("roles", []):
            try:
                role_id = role.get("id", "").lower()
                role_name = role.get("name", "")
                inherits_from = role.get("inherits_from")

                cursor.execute(
                    """
                    INSERT INTO roles
                    (role_id, name, tenant_id, inherits_from, created_at, updated_at)
                    VALUES (%s, %s, %s, %s, %s, %s)
                    ON CONFLICT (role_id) DO NOTHING
                    """,
                    (role_id, role_name, tenant_id, inherits_from, current_time, current_time)
                )
                pg_conn.commit()
                print(f"✅ Inserted Role: {role_id} - {role_name}")
            except Exception as e:
                pg_conn.rollback()
                print(f"❌ Error inserting role {role.get('id', 'unknown')}: {e}")

        # Insert Entities (handle duplicates that might exist in MongoDB JSON)
        print("\n➡️ Inserting Entities...")
        processed_entities = set()  # Keep track of already processed entities
        for entity in workflow_data.get("entities", []):
            try:
                entity_id = entity.get("id", "").lower()
                # Skip if we've already processed this entity (due to duplication in JSON)
                if entity_id in processed_entities:
                    print(f"⚠️ Skipping duplicate entity: {entity_id}")
                    continue

                name = entity.get("name", "")
                version = entity.get("version", "1.0")
                status = entity.get("status", "")
                entity_type = entity.get("type", "")
                attribute_prefix = entity.get("attributes_metadata", {}).get("attribute_prefix", "")
                description = entity.get("description", "").lower()

                cursor.execute(
                    """
                    INSERT INTO entities
                    (entity_id, name, version, status, type, attribute_prefix, description, created_at, updated_at)
                    VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s)
                    ON CONFLICT (entity_id) DO NOTHING
                    """,
                    (entity_id, name, version, status, entity_type, attribute_prefix, description,
                     current_time, current_time)
                )
                pg_conn.commit()
                processed_entities.add(entity_id)
                print(f"✅ Inserted Entity: {entity_id} - {name}")
            except Exception as e:
                pg_conn.rollback()
                print(f"❌ Error inserting entity {entity.get('id', 'unknown')}: {e}")

        # ===== STEP 2: ENTITY ATTRIBUTES AND METADATA =====
        print("\n==== STEP 2: INSERTING ENTITY ATTRIBUTES AND METADATA ====")

        # Insert Entity Attribute Metadata
        print("\n➡️ Inserting Entity Attribute Metadata...")
        for entity in workflow_data.get("entities", []):
            entity_id = entity.get("id", "").lower()
            if entity_id not in processed_entities:
                print(f"⚠️ Skipping metadata for unprocessed entity: {entity_id}")
                continue

            metadata = entity.get("attributes_metadata", {})
            attribute_map = metadata.get("attribute_map", {})
            required_attributes = metadata.get("required_attributes", [])

            for attr_id, attr_name in attribute_map.items():
                attr_id = attr_id.lower()
                try:
                    # Check if metadata already exists
                    cursor.execute(
                        """
                        SELECT 1 FROM entity_attribute_metadata
                        WHERE entity_id = %s AND attribute_id = %s
                        """,
                        (entity_id, attr_id)
                    )
                    exists = cursor.fetchone()

                    if exists:
                        print(f"⚠️ Metadata already exists: {entity_id}.{attr_id}")
                        continue

                    required = attr_id in [a.lower() for a in required_attributes]

                    cursor.execute(
                        """
                        INSERT INTO entity_attribute_metadata
                        (entity_id, attribute_id, attribute_name, required)
                        VALUES (%s, %s, %s, %s)
                        """,
                        (entity_id, attr_id, attr_name, required)
                    )
                    pg_conn.commit()
                    print(f"✅ Inserted Entity Attribute Metadata: {entity_id}.{attr_id}")
                except Exception as e:
                    pg_conn.rollback()
                    print(f"❌ Error inserting attribute metadata for {entity_id}.{attr_id}: {e}")


            # for attr_id, attr_name in entity.get("attributes_metadata", {}).get("attribute_map", {}).items():
                # required = attr_id in entity.get("attributes_metadata", {}).get("required_attributes", [])
                # try:
                #     cursor.execute(
                #         """
                #         INSERT INTO entity_attribute_metadata
                #         (entity_id, attribute_id, attribute_name, required)
                #         VALUES (%s, %s, %s, %s)
                #         ON CONFLICT DO NOTHING
                #         """,
                #         (entity_id.lower(), attr_id.lower(), attr_name, required)
                #     )
                #     pg_conn.commit()
                #     print(f"✅ Inserted Entity Attribute Metadata: {entity_id}.{attr_id}")
                # except Exception as e:
                #     pg_conn.rollback()
                #     print(f"❌ Error inserting entity attribute metadata {attr_id}: {e}")


        # Insert Entity Attributes
        print("\n➡️ Inserting Entity Attributes...")
        for entity in workflow_data.get("entities", []):
            entity_id = entity.get("id", "").lower()
            if entity_id not in processed_entities:
                print(f"⚠️ Skipping attributes for unprocessed entity: {entity_id}")
                continue

            for attr in entity.get("attributes", []):
                try:
                    attribute_id = attr.get("id", "").lower()
                    name = attr.get("name", "")
                    display_name = attr.get("display_name", "")
                    datatype = attr.get("datatype", "")

                    # Ensure required fields are present
                    if not attribute_id or not name or not datatype:
                        print(f"⚠️ Skipping invalid attribute (missing id, name, or datatype): {attr}")
                        continue

                    version = attr.get("version", "1.0")
                    status = attr.get("status", "").lower()
                    required = attr.get("required", False)
                    reference_entity_id = attr.get("references")

                    # Check if attribute already exists for this entity
                    cursor.execute(
                        """
                        SELECT 1 FROM entity_attributes
                        WHERE attribute_id = %s AND entity_id = %s
                        """,
                        (attribute_id, entity_id)
                    )
                    exists = cursor.fetchone()

                    if exists:
                        print(f"⚠️ Attribute already exists: {entity_id}.{attribute_id}")
                        continue

                    cursor.execute(
                        """
                        INSERT INTO entity_attributes
                        (attribute_id, entity_id, name, display_name, datatype, version, status, required, reference_entity_id, created_at, updated_at)
                        VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                        ON CONFLICT (attribute_id) DO NOTHING
                        """,
                        (attribute_id, entity_id, name, display_name, datatype, version, status, required, reference_entity_id,
                         current_time, current_time)
                    )
                    pg_conn.commit()
                    print(f"✅ Inserted Entity Attribute: {entity_id}.{attribute_id}")

                    # Insert Attribute Validations
                    for validation in attr.get("validations", []):

                            rule = validation.get("rule", "").lower()
                            expression = validation.get("expression", "").lower()

                            if not rule or not expression:
                                continue

                            try:

                                cursor.execute(
                                    """
                                    INSERT INTO attribute_validations
                                    (attribute_id, rule, expression, created_at, updated_at)
                                    VALUES (%s, %s, %s, %s, %s)
                                    ON CONFLICT DO NOTHING
                                    """,
                                    (attribute_id, rule, expression, current_time, current_time)
                                )
                                pg_conn.commit()
                                print(f"✅ Inserted Validation for {attribute_id}: {rule}")


                            except Exception as e:
                                pg_conn.rollback()
                                print(f"❌ Error inserting validation for {attribute_id}: {e}")

                    # Insert Enum Values
                    if datatype.lower() == "enum":
                        for value in attr.get("values", []):
                            try:
                                cursor.execute(
                                    """
                                    INSERT INTO attribute_enum_values
                                    (attribute_id, value, created_at, updated_at)
                                    VALUES (%s, %s, %s, %s)
                                    ON CONFLICT DO NOTHING
                                    """,
                                    (attribute_id.lower(), value.lower(), current_time, current_time)
                                )
                                pg_conn.commit()
                                print(f"✅ Inserted Enum Value for {attribute_id}: {value}")
                            except Exception as e:
                                pg_conn.rollback()
                                print(f"❌ Error inserting enum value {value} for {attribute_id}: {e}")

                except Exception as e:
                    pg_conn.rollback()
                    print(f"❌ Error inserting entity attribute {attr.get('id', 'unknown')}: {e}")

        # ===== STEP 3: ENTITY RELATIONSHIPS =====
        print("\n==== STEP 3: INSERTING ENTITY RELATIONSHIPS ====")

        print("\n➡️ Inserting Entity Relationships...")
        for entity in workflow_data.get("entities", []):
            entity_id = entity.get("id", "").lower()
            if entity_id not in processed_entities:
                print(f"⚠️ Skipping relationships for unprocessed entity: {entity_id}")
                continue

            for relationship in entity.get("relationships", []):
                try:
                    target_entity_id = relationship.get("entity_id", "").lower()
                    relationship_type = relationship.get("type", "").lower()
                    source_attribute_id = relationship.get("through_attribute", "").lower()
                    target_attribute_id = relationship.get("to_attribute", "").lower()

                    cursor.execute(
                        """
                        INSERT INTO entity_relationships
                        (source_entity_id, target_entity_id, relationship_type, source_attribute_id, target_attribute_id, created_at, updated_at)
                        VALUES (%s, %s, %s, %s, %s, %s, %s)
                        ON CONFLICT DO NOTHING
                        """,
                        (entity_id, target_entity_id, relationship_type, source_attribute_id, target_attribute_id,
                         current_time, current_time)
                    )
                    pg_conn.commit()
                    print(f"✅ Inserted Relationship: {entity_id} → {target_entity_id}")
                except Exception as e:
                    pg_conn.rollback()
                    print(f"❌ Error inserting relationship from {entity_id} to {relationship.get('entity_id', 'unknown')}: {e}")

        # ===== STEP 4: PERMISSIONS =====
        print("\n==== STEP 4: INSERTING PERMISSIONS ====")

        # Insert Entity Permissions
        print("\n➡️ Inserting Entity Permissions...")
        for role in tenant.get("roles", []):
            role_id = role.get("id", "").lower()
            for entity_access in role.get("access", {}).get("entities", []):
                entity_id = entity_access.get("entity_id", "").lower()
                for permission in entity_access.get("permissions", []):
                    try:
                        cursor.execute(
                            """
                            INSERT INTO entity_permissions
                            (role_id, entity_id, permission_id, created_at, updated_at)
                            VALUES (%s, %s, %s, %s, %s)
                            ON CONFLICT DO NOTHING
                            """,
                            (role_id, entity_id, permission.lower(), current_time, current_time)
                        )
                        pg_conn.commit()
                        print(f"✅ Inserted Entity Permission: Role {role_id} - Entity {entity_id} - Permission {permission}")
                    except Exception as e:
                        pg_conn.rollback()
                        print(f"❌ Error inserting entity permission {permission} for role {role_id}: {e}")

        # Insert Objective Permissions
        print("\n➡️ Inserting Objective Permissions...")
        for role in tenant.get("roles", []):
            role_id = role.get("id", "").lower()
            for objective_access in role.get("access", {}).get("objectives", []):
                objective_id = objective_access.get("objective_id", "").lower()
                for permission in objective_access.get("permissions", []):
                    try:
                        cursor.execute(
                            """
                            INSERT INTO objective_permissions
                            (role_id, objective_id, permission_id, created_at, updated_at)
                            VALUES (%s, %s, %s, %s, %s)
                            ON CONFLICT DO NOTHING
                            """,
                            (role_id, objective_id, permission.lower(), current_time, current_time)
                        )
                        pg_conn.commit()
                        print(f"✅ Inserted Objective Permission: Role {role_id} - Objective {objective_id} - Permission {permission}")
                    except Exception as e:
                        pg_conn.rollback()
                        print(f"❌ Error inserting objective permission {permission} for role {role_id}: {e}")

        # ===== STEP 5: GLOBAL OBJECTIVES =====
        print("\n==== STEP 5: INSERTING GLOBAL OBJECTIVES ====")

        # Insert Global Objectives
        print("\n➡️ Inserting Global Objectives...")
        for go in workflow_data.get("global_objectives", []):
            try:
                go_id = go.get("id", "").lower()
                name = go.get("name", "").lower()
                version = go.get("version", "1.0").lower()
                status = go.get("status", "").lower()
                description = go.get("description", "").lower()

                cursor.execute(
                    """
                    INSERT INTO global_objectives
                    (go_id, name, version, status, description,tenant_id, created_at, updated_at)
                    VALUES (%s, %s, %s,%s, %s, %s, %s, %s)
                    ON CONFLICT (go_id) DO NOTHING
                    """,
                    (go_id, name, version, status, description, tenant_id.lower(), current_time, current_time)
                )
                pg_conn.commit()
                print(f"✅ Inserted Global Objective: {go_id} - {name}")

                # Insert Input Stack
                if "input_stack" in go:
                    input_stack_description = go["input_stack"].get("description", "").lower()

                    cursor.execute(
                        """
                        INSERT INTO input_stack (go_id, description, created_at, updated_at)
                        VALUES (%s, %s, %s, %s)
                        ON CONFLICT DO NOTHING
                        RETURNING id
                        """,
                        (go_id, input_stack_description.lower(), current_time, current_time)
                    )

                    try:
                        result = cursor.fetchone()
                        pg_conn.commit()

                        if result:
                            input_stack_id = result[0]
                            print(f"✅ Created Input Stack for {go_id}")

                            # Insert Input Items
                            for input_item in go["input_stack"].get("inputs", []):
                                item_id = input_item.get("id", "").lower()
                                slot_id = input_item.get("slot_id", "").lower()
                                contextual_id = input_item.get("contextual_id", "").lower()
                                entity_reference = input_item.get("entity_reference", "").lower()
                                if not entity_reference:
                                    raise ValueError(f"Missing entity_reference for input item {input_item.get('id')}")

                                attribute_reference = input_item.get("attribute_reference", "").lower()
                                source_type = input_item["source"]["type"].lower()
                                source_description = input_item["source"].get("description", "").lower()
                                required = input_item.get("required", False)

                                try:
                                    cursor.execute(
                                        """
                                        INSERT INTO input_items
                                        (id, input_stack_id, slot_id, contextual_id, entity_reference,
                                         attribute_reference, source_type, source_description, required,
                                         created_at, updated_at)
                                        VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                                        ON CONFLICT DO NOTHING
                                        """,
                                        (item_id, input_stack_id, slot_id, contextual_id, entity_reference,
                                         attribute_reference, source_type, source_description, required,
                                         current_time, current_time)
                                    )
                                    pg_conn.commit()
                                    print(f"✅ Inserted Input Item: {item_id}")
                                except Exception as e:
                                    pg_conn.rollback()
                                    print(f"❌ Error inserting input item {item_id}: {e}")

                            # Insert System Functions for Input Stack
                            for function in go["input_stack"].get("system_functions", []):
                                function_id = function.get("function_id", "").lower()
                                function_name = function.get("function_name", "").lower()
                                function_type = function.get("function_type", "").lower()
                                parameters = function.get("parameters", {})
                                output_to = function.get("output_to", "").lower()

                                try:
                                    cursor.execute(
                                        """
                                        INSERT INTO system_functions
                                        (function_id, function_name, function_type, stack_type, stack_id, parameters, output_to,
                                        created_at, updated_at)
                                        VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s)
                                        ON CONFLICT DO NOTHING
                                        """,
                                        (function_id, function_name, function_type, "input", input_stack_id,
                                         Json(parameters), output_to, current_time, current_time)
                                    )
                                    pg_conn.commit()
                                    print(f"✅ Inserted System Function: {function_id}")
                                except Exception as e:
                                    pg_conn.rollback()
                                    print(f"❌ Error inserting system function {function_id}: {e}")
                        else:
                            # Get existing input stack ID
                            cursor.execute(
                                """
                                SELECT id FROM input_stack WHERE go_id = %s
                                """,
                                (go_id,)
                            )
                            result = cursor.fetchone()
                            if result:
                                print(f"⚠️ Input Stack for {go_id} already exists")
                            else:
                                print(f"⚠️ Failed to create Input Stack for {go_id}")
                    except Exception as e:
                        pg_conn.rollback()
                        print(f"⚠️ Error processing input stack: {e}")

                # Insert Output Stack
                if "output_stack" in go:
                    output_stack_description = go["output_stack"].get("description", "")

                    cursor.execute(
                        """
                        INSERT INTO output_stack (go_id, description, created_at, updated_at)
                        VALUES (%s, %s, %s, %s)
                        ON CONFLICT DO NOTHING
                        RETURNING id
                        """,
                        (go_id, output_stack_description, current_time, current_time)
                    )

                    try:
                        result = cursor.fetchone()
                        pg_conn.commit()

                        if result:
                            output_stack_id = result[0]
                            print(f"✅ Created Output Stack for {go_id}")

                            # Insert Output Items
                            for output_item in go["output_stack"].get("outputs", []):
                                item_id = output_item.get("id", "").lower()
                                slot_id = output_item.get("slot_id", "").lower()
                                contextual_id = output_item.get("contextual_id", "").lower()
                                output_entity = output_item.get("output_entity", "").lower()
                                output_attribute = output_item.get("output_attribute", "").lower()
                                data_type = output_item.get("data_type", "").lower()

                                try:
                                    cursor.execute(
                                        """
                                        INSERT INTO output_items
                                        (id, output_stack_id, slot_id, contextual_id, output_entity, output_attribute, data_type,
                                        created_at, updated_at)
                                        VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s)
                                        ON CONFLICT DO NOTHING
                                        """,
                                        (item_id, output_stack_id, slot_id, contextual_id, output_entity, output_attribute, data_type,
                                        current_time, current_time)
                                    )
                                    pg_conn.commit()
                                    print(f"✅ Inserted Output Item: {item_id}")

                                    # Insert Output Triggers
                                    if "triggers" in output_item:
                                        for trigger in output_item["triggers"]["items"]:
                                            trigger_id = trigger.get("id", "").lower()
                                            target_objective = trigger.get("target_objective", "").lower()
                                            target_input = trigger.get("target_input", "").lower()
                                            mapping_type = trigger.get("mapping_type", "").lower()

                                            condition = trigger.get("condition", {})
                                            condition_type = condition.get("condition_type", "").lower()
                                            condition_entity = condition.get("entity", "").lower()
                                            condition_attribute = condition.get("attribute", "").lower()
                                            condition_operator = condition.get("operator", "").lower()
                                            condition_value = condition.get("value", "").lower()

                                            try:
                                                cursor.execute(
                                                    """
                                                    INSERT INTO output_triggers
                                                    (id, output_item_id, output_stack_id, target_objective, target_input,
                                                     mapping_type, condition_type, condition_entity, condition_attribute,
                                                     condition_operator, condition_value, created_at, updated_at)
                                                    VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                                                    ON CONFLICT DO NOTHING
                                                    """,
                                                    (trigger_id, item_id, output_stack_id, target_objective, target_input,
                                                     mapping_type, condition_type, condition_entity, condition_attribute,
                                                     condition_operator, condition_value, current_time, current_time)
                                                )
                                                pg_conn.commit()
                                                print(f"✅ Inserted Output Trigger: {trigger_id}")
                                            except Exception as e:
                                                pg_conn.rollback()
                                                print(f"❌ Error inserting output trigger {trigger_id}: {e}")
                                except Exception as e:
                                    pg_conn.rollback()
                                    print(f"❌ Error inserting output item {item_id}: {e}")
                        else:
                            # Get existing output stack ID
                            cursor.execute(
                                """
                                SELECT id FROM output_stack WHERE go_id = %s
                                """,
                                (go_id,)
                            )
                            result = cursor.fetchone()
                            if result:
                                print(f"⚠️ Output Stack for {go_id} already exists")
                            else:
                                print(f"⚠️ Failed to create Output Stack for {go_id}")
                    except Exception as e:
                        pg_conn.rollback()
                        print(f"⚠️ Error processing output stack: {e}")

                # Insert Data Mapping Stack
                if "data_mapping_stack" in go:
                    mapping_stack_description = go["data_mapping_stack"].get("description", "")

                    cursor.execute(
                        """
                        INSERT INTO data_mapping_stack (go_id, description, created_at, updated_at)
                        VALUES (%s, %s, %s, %s)
                        ON CONFLICT DO NOTHING
                        RETURNING id
                        """,
                        (go_id, mapping_stack_description, current_time, current_time)
                    )

                    try:
                        result = cursor.fetchone()
                        pg_conn.commit()

                        if result:
                            mapping_stack_id = result[0]
                            print(f"✅ Created Data Mapping Stack for {go_id}")

                            # Insert Data Mappings
                            for mapping in go["data_mapping_stack"].get("mappings", []):
                                mapping_id = mapping.get("id", "").lower()
                                source = mapping.get("source", "").lower()
                                target = mapping.get("target", "").lower()
                                mapping_type = mapping.get("mapping_type", "").lower()

                                try:
                                    cursor.execute(
                                        """
                                        INSERT INTO data_mappings
                                        (id, mapping_stack_id, source, target, mapping_type, created_at, updated_at)
                                        VALUES (%s, %s, %s, %s, %s, %s, %s)
                                        ON CONFLICT DO NOTHING
                                        """,
                                        (mapping_id, mapping_stack_id, source, target, mapping_type,
                                         current_time, current_time)
                                    )
                                    pg_conn.commit()
                                    print(f"✅ Inserted Data Mapping: {mapping_id}")
                                except Exception as e:
                                    pg_conn.rollback()
                                    print(f"❌ Error inserting data mapping {mapping_id}: {e}")

                            # Insert Mapping Rules
                            for rule in go["data_mapping_stack"].get("rules", []):
                                rule_id = rule.get("id", "").lower()
                                description = rule.get("description", "").lower()

                                rule_condition = rule.get("rule_condition", {})
                                condition_type = rule_condition.get("condition_type", "").lower()
                                condition_entity = rule_condition.get("entity", "").lower()
                                condition_attribute = rule_condition.get("attribute", "").lower()
                                condition_operator = rule_condition.get("operator", "").lower()
                                condition_value = rule_condition.get("value", "").lower()
                                error_message = rule.get("error_message", "").lower()

                                try:
                                    cursor.execute(
                                        """
                                        INSERT INTO mapping_rules
                                        (id, mapping_stack_id, description, condition_type, condition_entity,
                                         condition_attribute, condition_operator, condition_value, error_message,
                                         created_at, updated_at)
                                        VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                                        ON CONFLICT DO NOTHING
                                        """,
                                        (rule_id, mapping_stack_id, description, condition_type, condition_entity,
                                         condition_attribute, condition_operator, condition_value, error_message,
                                         current_time, current_time)
                                    )
                                    pg_conn.commit()
                                    print(f"✅ Inserted Mapping Rule: {rule_id}")
                                except Exception as e:
                                    pg_conn.rollback()
                                    print(f"❌ Error inserting mapping rule {rule_id}: {e}")
                        else:
                            # Get existing mapping stack ID
                            try:
                                cursor.execute(
                                    """
                                    SELECT id FROM data_mapping_stack WHERE go_id = %s
                                    """,
                                    (go_id,)
                                )
                                result = cursor.fetchone()
                                if result:
                                    print(f"⚠️ Data Mapping Stack for {go_id} already exists")
                                else:
                                    print(f"⚠️ Failed to create Data Mapping Stack for {go_id}")
                            except Exception as e:
                                print(f"⚠️ Error checking data mapping stack: {e}")
                    except Exception as e:
                        pg_conn.rollback()
                        print(f"⚠️ Error processing data mapping stack: {e}")

            except Exception as e:
                pg_conn.rollback()
                print(f"❌ Error processing Global Objective {go.get('id', 'unknown')}: {e}")
                traceback.print_exc()
        #sys.exit(0)  # Graceful exit with status code 0
        # ===== STEP 6: LOCAL OBJECTIVES =====
        print("\n==== STEP 6: INSERTING LOCAL OBJECTIVES ====")

        # First insert all local objectives without relationships
        print("\n➡️ Inserting base Local Objectives...")
        local_objectives = workflow_data.get("local_objectives", [])
        if not local_objectives:
            print("⚠️ No local objectives found in data.")
        else:
            for lo in local_objectives:
                try:
                    lo_id = lo.get("id", "").lower()  # Ensure consistent casing
                    contextual_id = lo.get("contextual_id", "").lower()
                    name = lo.get("name", "")
                    function_type = lo.get("function_type", "")
                   # workflow_source = lo.get("workflow_source", "Origin")
                    workflow_source = lo.get("workflow_source")
                    go_id = lo.get("go_id", "").lower()
                    if not go_id:
                        go_id = contextual_id.split(".")[0].lower() if "." in contextual_id else ""

                    #go_id = contextual_id.split(".")[0] if "." in contextual_id else ""  # Extract GO ID from contextual ID

                    cursor.execute(
                        """
                        INSERT INTO local_objectives
                        (lo_id, contextual_id, name, function_type, workflow_source, go_id, created_at, updated_at)
                        VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
                        ON CONFLICT (lo_id) DO NOTHING
                        """,
                        (lo_id, contextual_id, name, function_type, workflow_source, go_id.lower() , current_time, current_time)
                    )
                    pg_conn.commit()
                    print(f"✅ Inserted Local Objective: {lo_id} - {name}")
                except Exception as e:
                    pg_conn.rollback()
                    print(f"❌ Error inserting local objective {lo.get('id', 'unknown')}: {e}")

            # Now insert relationships and additional data
            print("\n➡️ Inserting Local Objective relationships and data...")
            for lo in local_objectives:
                try:
                    lo_id = lo.get("id", "").lower()  # Ensure consistent casing

                    # Insert execution pathway
                    if "execution_pathway" in lo:
                        pathway = lo["execution_pathway"]
                        pathway_type = pathway.get("type", "")

#                        if pathway_type.lower() == "sequential" and "next_lo" in pathway:
                        next_lo = pathway["next_lo"].lower()  # Ensure consistent casing

                        cursor.execute(
                                "SELECT 1 FROM execution_pathways WHERE lo_id = %s AND next_lo = %s",
                                (lo_id, next_lo)
                            )

                        cursor.execute(
                                """
                                INSERT INTO execution_pathways
                                (lo_id, pathway_type, next_lo, created_at, updated_at)
                                VALUES (%s, %s, %s, %s, %s)
                                """,
                                (lo_id, pathway_type, next_lo, current_time, current_time)
                           )
                        pg_conn.commit()
                        print(f"✅ Inserted Sequential Execution Pathway: {lo_id} -> {next_lo}")

                        if pathway_type.lower() == "alternative" and "conditions" in pathway:
                         for condition in pathway["conditions"]:
                               cond = condition["condition"]
                               next_lo = condition["next_lo"].lower()  # Ensure consistent casing

                               cursor.execute(
                                   """
                                   SELECT 1 FROM execution_pathway_conditions
                                   WHERE lo_id = %s AND next_lo = %s
                                    """,
                                    (lo_id, next_lo)
                                )

                               exists = cursor.fetchone()
                               if not exists:
                                   cursor.execute(
                                        """
                                        INSERT INTO execution_pathway_conditions
                                        (lo_id, condition_type, condition_entity, condition_attribute,
                                        condition_operator, condition_value, next_lo, created_at, updated_at)
                                        VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s)

                                        """,
                                        (
                                            lo_id,
                                            cond["condition_type"],
                                            cond["entity"].lower(),
                                            cond["attribute"],
                                            cond["operator"],
                                            cond["value"],
                                            next_lo,
                                            current_time,
                                            current_time
                                        )
                                    )
                                   pg_conn.commit()
                                   print(f"✅ Inserted Alternative Execution Pathway Condition: {lo_id} -> {next_lo}")
                               else:
                                    print(f"⚠️ Skipped duplicate condition: {lo_id} -> {next_lo}")
                    # Insert agent stack
                    if "agent_stack" in lo and "agents" in lo["agent_stack"]:
                        # First create the agent stack
                        cursor.execute(
                            """
                            INSERT INTO agent_stack (lo_id, created_at, updated_at)
                            VALUES (%s, %s, %s)
                            RETURNING id
                            """,
                            (lo_id, current_time, current_time)
                        )
                        result = cursor.fetchone()
                        pg_conn.commit()

                        if result:
                            agent_stack_id = result[0]
                            print(f"✅ Created Agent Stack for {lo_id}")

                            # Insert agent rights
                            for agent in lo["agent_stack"]["agents"]:
                                role_id = agent["role"].lower()   # Keep original case
                                print(f"DEBUG: Role ID from YAML: '{role_id}'")

                                # Check if the role exists before inserting
                                cursor.execute("SELECT 1 FROM roles WHERE role_id = %s", (role_id,))
                                role_exists = cursor.fetchone()

                                if not role_exists:
                                    print(f"⚠️ Role {role_id} not found with original case, trying case-insensitive search")
                                    # Try with case-insensitive search
                                    cursor.execute("SELECT role_id FROM roles WHERE LOWER(role_id) = LOWER(%s)", (role_id,))
                                    role_result = cursor.fetchone()

                                    if role_result:
                                        print(f"⚠️ Found role with different case: {role_result[0]}")
                                        role_id = role_result[0]  # Use the role_id with the case found in the database
                                    else:
                                        print(f"⚠️ Role {role_id} does not exist in the roles table. Creating it...")
                                        cursor.execute(
                                            """
                                            INSERT INTO roles (role_id, name, tenant_id, created_at, updated_at)
                                            VALUES (%s, %s, %s, %s, %s)

                                            """,
                                            (role_id.lower() , f"Auto-generated {role_id}", tenant_id, current_time, current_time)
                                        )
                                        pg_conn.commit()
                                        print(f"✅ Created missing role: {role_id}")

                                # Now insert the agent rights with confirmed role_id
                                for right in agent.get("rights", []):
                                    cursor.execute(
                                        """
                                        INSERT INTO agent_rights
                                        (agent_stack_id, role_id, right_id, created_at, updated_at)
                                        VALUES (%s, %s, %s, %s, %s)

                                        """,
                                        (agent_stack_id , role_id , right.lower(), current_time, current_time)
                                    )
                                    pg_conn.commit()
                                print(f"✅ Inserted Agent Rights for role {role_id} in stack for {lo_id}")
                        else:
                            # Get existing agent stack ID
                            cursor.execute(
                                """
                                SELECT id FROM agent_stack WHERE lo_id = %s
                                """,
                                (lo_id,)
                            )
                            result = cursor.fetchone()
                            if result:
                                agent_stack_id = result[0]
                                print(f"⚠️ Using existing Agent Stack for {lo_id}")

                                # Insert agent rights (same as above)
                                for agent in lo["agent_stack"]["agents"]:
                                    role_id = agent["role"].lower()   # Keep original case

                                    # Check if the role exists before inserting
                                    cursor.execute("SELECT 1 FROM roles WHERE role_id = %s", (role_id,))
                                    role_exists = cursor.fetchone()

                                    if not role_exists:
                                        print(f"⚠️ Role {role_id} not found with original case, trying case-insensitive search")
                                        # Try with case-insensitive search
                                        cursor.execute("SELECT role_id FROM roles WHERE LOWER(role_id) = LOWER(%s)", (role_id,))
                                        role_result = cursor.fetchone()

                                        if role_result:
                                            print(f"⚠️ Found role with different case: {role_result[0]}")
                                            role_id = role_result[0]  # Use the role_id with the case found in the database
                                        else:
                                            print(f"⚠️ Role {role_id} does not exist in the roles table. Creating it...")
                                            cursor.execute(
                                                """
                                                INSERT INTO roles (role_id, name, tenant_id, created_at, updated_at)
                                                VALUES (%s, %s, %s, %s, %s)
                                                ON CONFLICT DO NOTHING
                                                """,
                                                (role_id, f"Auto-generated {role_id}", tenant_id, current_time, current_time)
                                            )
                                            pg_conn.commit()
                                            print(f"✅ Created missing role: {role_id}")

                                    # Now insert the agent rights with confirmed role_id
                                    for right in agent.get("rights", []):
                                        cursor.execute(
                                            """
                                            INSERT INTO agent_rights
                                            (agent_stack_id, role_id, right_id, created_at, updated_at)
                                            VALUES (%s, %s, %s, %s, %s)
                                            ON CONFLICT DO NOTHING
                                            """,
                                            (agent_stack_id.lower() , role_id.lower() , right.lower(), current_time, current_time)
                                        )
                                        pg_conn.commit()
                                    print(f"✅ Inserted Agent Rights for role {role_id} in existing stack for {lo_id}")
                            else:
                                print(f"⚠️ Cannot find agent stack for {lo_id}")
                    #sys.exit(0)  # Graceful exit with status code 0
                    # Insert input stack
                    if "input_stack" in lo:
                        cursor.execute(
                            """
                            INSERT INTO lo_input_stack (lo_id, description, created_at, updated_at)
                            VALUES (%s, %s, %s, %s)
                            RETURNING id
                            """,
                            (lo_id, lo["input_stack"].get("description", ""), current_time, current_time)
                        )
                        result = cursor.fetchone()
                        pg_conn.commit()

                        if result:
                            input_stack_id = result[0]
                            print(f"✅ Created Input Stack for {lo_id}")
                        else:
                            # Get existing input stack ID
                            cursor.execute(
                                """
                                SELECT id FROM lo_input_stack WHERE lo_id = %s
                                """,
                                (lo_id,)
                            )
                            result = cursor.fetchone()
                            if result:
                                input_stack_id = result[0]
                                print(f"⚠️ Using existing Input Stack for {lo_id}")
                            else:
                                print(f"⚠️ Cannot find input stack for {lo_id}")
                                continue

                        # Insert input items
                        if "inputs" in lo["input_stack"]:
                            for input_item in lo["input_stack"]["inputs"]:
                                try:
                                    item_id = input_item["id"].lower()
                                    slot_id = input_item["slot_id"].lower()
                                    contextual_id = input_item["contextual_id"].lower()
                                    source_type = to_capitalize(input_item["source"]["type"])
                                    source_description = input_item["source"].get("description", "")
                                    required = input_item.get("required", False)
                                    #data_type = input_item.get("data_type", "string")
                                    ui_control = input_item.get("ui_control", "")

                                    # Extract nested function data if present
                                    nested_function = Json(input_item.get("nested_function", {})) if "nested_function" in input_item else None
                                    nested_functions = Json(input_item.get("nested_functions", [])) if "nested_functions" in input_item else None
                                    cursor.execute(
                                        """
                                        SELECT 1 FROM lo_input_items WHERE id = %s AND input_stack_id = %s
                                        """,
                                        (item_id, input_stack_id)
                                    )
                                    if not cursor.fetchone():
                                        cursor.execute(
                                            """
                                            INSERT INTO lo_input_items
                                            (id, input_stack_id, lo_id, slot_id, contextual_id, source_type, source_description,
                                            required,ui_control, created_at, updated_at)
                                            VALUES (%s, %s, %s, %s,%s, %s, %s, %s, %s, %s, %s)
                                            """,
                                            (
                                                item_id,
                                                input_stack_id,
                                                lo_id,
                                                slot_id,
                                                contextual_id,
                                                source_type,
                                                source_description,
                                                required,
                                                ui_control,
                                                current_time,
                                                current_time
                                            )
                                        )

                                        pg_conn.commit()
                                        print(f"✅ Inserted Input Item {item_id} for {lo_id}")
                                    else:
                                        print(f"⚠️ Skipped duplicate input item: {lo_id}.{item_id}")

                                    # Insert input validations
                                    if "validations" in input_item:
                                        for validation in input_item["validations"]:
                                            cursor.execute(
                                                """
                                                INSERT INTO lo_input_validations
                                                (input_item_id, input_stack_id, rule, rule_type, error_message, created_at, updated_at)
                                                VALUES (%s, %s, %s, %s, %s, %s, %s)
                                                ON CONFLICT DO NOTHING
                                                """,
                                                (
                                                    item_id,
                                                    input_stack_id,
                                                    validation["rule"],
                                                    validation["rule_type"],
                                                    "",  # No error_message in yaml
                                                    current_time,
                                                    current_time
                                                )
                                            )
                                            pg_conn.commit()
                                            print(f"✅ Inserted Validations for Input Item {item_id}")
                                    # Process nested function (single)
                                    if "nested_function" in input_item:
                                        try:
                                            nested_func = input_item["nested_function"]
                                            # Check if nested_function is a dictionary before proceeding
                                            if isinstance(nested_func, dict):
                                                function_id = nested_func.get("id", "").lower()
                                                function_name = nested_func.get("function_name", "").lower()
                                                function_type = nested_func.get("function_type", "").lower()
                                                parameters = nested_func.get("parameters", {})
                                                output_to = nested_func.get("output_to", "").lower()

                                                cursor.execute(
                                                    """
                                                    INSERT INTO lo_nested_functions
                                                    (lo_id, nested_function_id, function_name, function_type, parameters,
                                                    input_contextual_id, output_to, created_at, updated_at)
                                                    VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s)
                                                    ON CONFLICT DO NOTHING
                                                    """,
                                                    (
                                                        lo_id,
                                                        function_id,
                                                        function_name,
                                                        function_type,
                                                        Json(parameters) if parameters else None,
                                                        contextual_id,
                                                        output_to,
                                                        current_time,
                                                        current_time
                                                    )
                                                )
                                                pg_conn.commit()
                                                print(f"✅ Inserted Nested Function: {function_id} for input {item_id}")
                                            else:
                                                print(f"⚠️ Nested function for input {item_id} is not a valid object: {nested_func}")
                                        except Exception as e:
                                            pg_conn.rollback()
                                            print(f"❌ Error inserting nested function for {item_id}: {e}")

                                    # Process nested functions (multiple)
                                    if "nested_functions" in input_item:
                                            try:
                                                nested_funcs = input_item["nested_functions"]
                                                # Check if nested_functions is a list before iterating
                                                if isinstance(nested_funcs, list):
                                                    for nested_func in nested_funcs:
                                                        if isinstance(nested_func, dict):
                                                            function_id = nested_func.get("id", "").lower()
                                                            function_name = nested_func.get("function_name", "").lower()
                                                            function_type = nested_func.get("function_type", "").lower()
                                                            parameters = nested_func.get("parameters", {})
                                                            output_to = nested_func.get("output_to", "").lower()

                                                            cursor.execute(
                                                                """
                                                                INSERT INTO lo_nested_functions
                                                                (lo_id, nested_function_id, function_name, function_type, parameters,
                                                                input_contextual_id, output_to, created_at, updated_at)
                                                                VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s)
                                                                ON CONFLICT DO NOTHING
                                                                """,
                                                                (
                                                                    lo_id,
                                                                    function_id,
                                                                    function_name,
                                                                    function_type,
                                                                    Json(parameters) if parameters else None,
                                                                    contextual_id,
                                                                    output_to,
                                                                    current_time,
                                                                    current_time
                                                                )
                                                            )
                                                            pg_conn.commit()
                                                            print(f"✅ Inserted Nested Function: {function_id} for input {item_id}")
                                                        else:
                                                            print(f"⚠️ A nested function item for input {item_id} is not a valid object: {nested_func}")
                                                else:
                                                    print(f"⚠️ Nested functions for input {item_id} is not a valid list: {nested_funcs}")
                                            except Exception as e:
                                                pg_conn.rollback()
                                                print(f"❌ Error processing nested functions for {item_id}: {e}")



                                except Exception as e:
                                    pg_conn.rollback()
                                    print(f"⚠️ Error with input item {input_item.get('id', 'unknown')}: {e}")
                                    continue
                                # Insert output stack
                    if "output_stack" in lo:
                        cursor.execute(
                            """
                            INSERT INTO lo_output_stack (lo_id, description, created_at, updated_at)
                            VALUES (%s, %s, %s, %s)

                            RETURNING id
                            """,
                            (lo_id, lo["output_stack"].get("description", ""), current_time, current_time)
                        )
                        result = cursor.fetchone()
                        pg_conn.commit()

                        if result:
                            output_stack_id = result[0]
                            print(f"✅ Created Output Stack for {lo_id}")
                        else:
                            # Get existing output stack ID
                            cursor.execute(
                                """
                                SELECT id FROM lo_output_stack WHERE lo_id = %s
                                """,
                                (lo_id,)
                            )
                            result = cursor.fetchone()
                            if result:
                                output_stack_id = result[0]
                                print(f"⚠️ Using existing Output Stack for {lo_id}")
                            else:
                                print(f"⚠️ Cannot find output stack for {lo_id}")
                                continue

                        # Insert output items
                        if "outputs" in lo["output_stack"]:
                            for output_item in lo["output_stack"]["outputs"]:
                                try:
                                    item_id = output_item["id"].lower()
                                    slot_id = output_item["slot_id"].lower()
                                    contextual_id = output_item["contextual_id"].lower()
                                    source_type = to_capitalize(output_item["source"]["type"])
                                    #value = output_item["source"].get("value", "")
                                    data_type = output_item.get("data_type", "string")

                                    cursor.execute(
                                        """
                                        SELECT 1 FROM lo_output_items WHERE id = %s AND output_stack_id = %s
                                        """,
                                        (item_id, output_stack_id)
                                    )

                                    if not cursor.fetchone():
                                        cursor.execute(
                                            """
                                            INSERT INTO lo_output_items
                                            (id, output_stack_id, lo_id, slot_id, contextual_id, source, created_at, updated_at)
                                            VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
                                            """,
                                            (
                                                item_id,
                                                output_stack_id,
                                                lo_id,
                                                slot_id,
                                                contextual_id,
                                                source_type,
                                                current_time,
                                                current_time
                                            )
                                        )
                                        pg_conn.commit()
                                        print(f"✅ Inserted Output Item {item_id} for {lo_id}")
                                    else:
                                        print(f"⚠️ Skipped duplicate output item: {lo_id}.{item_id}")
                                except Exception as e:
                                    pg_conn.rollback()
                                    print(f"⚠️ Error with output item {output_item.get('id', 'unknown')}: {e}")
                                    continue

                    # Insert data mapping stack
                    if "data_mapping_stack" in lo:
                        # Step 1: Try to insert — conflict is ignored
                        cursor.execute(
                            """
                            INSERT INTO lo_data_mapping_stack (lo_id, description, created_at, updated_at)
                            VALUES (%s, %s, %s, %s)

                            """,
                            (lo_id, lo["data_mapping_stack"].get("description", ""), current_time, current_time)
                        )
                        pg_conn.commit()

                        # Step 2: Always select the ID afterward
                        cursor.execute(
                            """
                            SELECT id FROM lo_data_mapping_stack WHERE lo_id = %s
                            """,
                            (lo_id,)
                        )
                        result = cursor.fetchone()

                        if result:
                            mapping_stack_id = result[0]
                            print(f"✅ Created or reused Data Mapping Stack for {lo_id}")
                        else:
                            print(f"⚠️ Could not find Data Mapping Stack for {lo_id}")
                            continue


                        # Insert data mappings
                        if "mappings" in lo["data_mapping_stack"]:
                            for mapping in lo["data_mapping_stack"]["mappings"]:
                                try:
                                    mapping_id = mapping["id"].lower()
                                    source = mapping["source"].lower()
                                    target = mapping["target"].lower()
                                    mapping_type = mapping["mapping_type"].lower()

                                    cursor.execute(
                                        """
                                        INSERT INTO lo_data_mappings
                                        (id, mapping_stack_id, source, target, mapping_type, created_at, updated_at)
                                        VALUES (%s, %s, %s, %s, %s, %s, %s)
                                        ON CONFLICT DO NOTHING
                                        """,
                                        (
                                            mapping_id,
                                            mapping_stack_id,
                                            source,
                                            target,
                                            mapping_type,
                                            current_time,
                                            current_time
                                        )
                                    )
                                    pg_conn.commit()
                                    print(f"✅ Inserted Data Mapping {mapping_id} for {lo_id}")
                                except Exception as e:
                                    pg_conn.rollback()
                                    print(f"⚠️ Error with data mapping {mapping.get('id', 'unknown')}: {e}")
                                    continue

                except Exception as e:
                    pg_conn.rollback()
                    print(f"❌ Error processing additional data for {lo.get('id', 'unknown')}: {e}")
                    traceback.print_exc()
                    continue

        print(f"\n🏁 Migration of data to PostgreSQL schema {schema_name} complete!")

    except Exception as e:
        if pg_conn and cursor:
            pg_conn.rollback()
        print(f"❌ Error migrating workflow data: {e}")
        traceback.print_exc()
    finally:
        if 'cursor' in locals() and cursor and not cursor.closed:
            cursor.close()
def to_capitalize(word):
    return word[0].upper() + word[1:].lower() if word else word
# === Direct MongoDB to PostgreSQL Migration using schemas ===
def direct_mongodb_to_postgres(mongodb_uri="mongodb://localhost:27017/",
                              db_name="workflow_system",
                              collection_name="workflow"):
    """
    Directly migrate data from MongoDB to PostgreSQL without using an intermediate file.
    Uses schemas for multi-tenant separation.
    """
    try:
        from pymongo import MongoClient

        # Connect to MongoDB
        print(f"➡️ Connecting to MongoDB at {mongodb_uri}...")
        client = MongoClient(mongodb_uri)
        db = client[db_name]
        collection = db[collection_name]

        print(f"➡️ Retrieving draft documents from {db_name}.{collection_name}...")
        # Find documents with status "draft"
        draft_workflows = list(collection.find({"status": "draft"}))

        draft_count = len(draft_workflows)

        if draft_count == 0:
            print("ℹ️ No draft documents found in MongoDB collection.")
            return True

        print(f"✅ Found {draft_count} draft document(s) to process.")


         # Process each draft document
        for workflow_doc in draft_workflows:
          try:
         # Extract workflow data
             workflow_data = workflow_doc.get("workflow_data", {})
             if not workflow_data:
              print("❌ No workflow_data found in MongoDB document.")
              return False

         # Get tenant ID
             tenant = workflow_data.get("tenant", {})
             tenant_id = tenant.get("id", "T001")
             print(f"➡️ Using tenant ID: {tenant_id}")

          # Connect to PostgreSQL
             print(f"➡️ Connecting to PostgreSQL...")
             pg_conn = connect_with_retry(PG_CONFIG)
             print(f"✅ Connected to PostgreSQL")

          # Check and create schema
             schema_name = "workflow_runtime"

          # Create tables in schema
             if os.path.exists(SQL_SCHEMA_FILE):
                print(f"➡️ Creating tables in schema {schema_name}...")
                create_tables_in_schema(pg_conn, SQL_SCHEMA_FILE, schema_name)

            # Migrate workflow data to schema
             migrate_workflow_data_to_schema(pg_conn, workflow_data, schema_name)
           # Update the document status to "complete" in MongoDB
#             collection.update_one(
 #                   {"_id": workflow_doc["_id"]},
  #                  {"$set": {"status": "complete"}}
   #             )
          except Exception as e:
                print(f"❌ Error processing document: {e}")
                traceback.print_exc()
                continue
        # Close connections
        pg_conn.close()
        client.close()
        return True
    except Exception as e:
        print(f"❌ Error during direct MongoDB to PostgreSQL migration: {e}")
        traceback.print_exc()
        return False

def file_mongodb_to_postgres(json_file_path):
    """
    Migrate workflow data from a JSON file to PostgreSQL using schemas.
    """
    try:
        # Read data from JSON file
        print(f"➡️ Reading data from {json_file_path}...")
        with open(json_file_path, 'r') as f:
            workflow_doc = json.load(f)

        # Extract workflow data
        workflow_data = workflow_doc.get("workflow_data", {})
        if not workflow_data:
            print("❌ No workflow_data found in JSON document.")
            return False

        # Get tenant ID
        tenant = workflow_data.get("tenant", {})
        tenant_id = tenant.get("id", "T001")
        print(f"➡️ Using tenant ID: {tenant_id}")

        # Connect to PostgreSQL
        print(f"➡️ Connecting to PostgreSQL...")
        pg_conn = connect_with_retry(PG_CONFIG)
        print(f"✅ Connected to PostgreSQL")

        # Check and create schema
        schema_name = check_and_create_schema(pg_conn, tenant_id)

        # Create tables in schema
        if os.path.exists(SQL_SCHEMA_FILE):
            print(f"➡️ Creating tables in schema {schema_name}...")
            create_tables_in_schema(pg_conn, SQL_SCHEMA_FILE, schema_name)

        # Migrate workflow data to schema
        migrate_workflow_data_to_schema(pg_conn, workflow_data, schema_name)

        # Close connection
        pg_conn.close()

        return True

    except Exception as e:
        print(f"❌ Error during file-based migration: {e}")
        traceback.print_exc()
        return False

# === Main entry point ===
if __name__ == "__main__":

     mongodb_uri =  "mongodb://localhost:27017/"
     db_name =  "workflow_system"
     collection_name =  "workflow"
     direct_mongodb_to_postgres(mongodb_uri, db_name, collection_name)
