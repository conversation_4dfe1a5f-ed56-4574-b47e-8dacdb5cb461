"""
Workflow Instances Service for v2 API

This module contains the business logic for workflow instances operations with RBAC support.
"""

import json
import uuid
import logging
from datetime import datetime
from typing import Optional, List, Dict, Any, Tuple
from sqlalchemy.orm import Session
from sqlalchemy.sql import text

from app.models.workflow import WorkflowInstance<PERSON>tatus
from .models import (
    WorkflowInstanceResponse, 
    WorkflowInstanceListResponse,
    CreateWorkflowInstanceRequest,
    StartWorkflowInstanceRequest
)

# Configure logging
logger = logging.getLogger(__name__)


class WorkflowInstancesService:
    """
    Service class for workflow instances operations with RBAC support.
    
    This service handles all business logic for workflow instances including:
    - Creating workflow instances
    - Starting workflow instances
    - Retrieving workflow instances
    - RBAC validation
    """
    
    def __init__(self, db_session: Session):
        self.db = db_session
        self.logger = logger
    
    def _get_workflow_access_roles(self) -> List[str]:
        """
        Dynamically fetch all roles that should have workflow access.
        This replaces hard-coded role lists with database-driven role management.
        
        Returns:
            List[str]: List of role names that have workflow access
        """
        try:
            # Foir now, get all roles except basic viewer roles
            # In a more sophisticated system, this could be based on permissions
            query = """
            SELECT DISTINCT role 
            FROM workflow_runtime.user_roles 
            WHERE role NOT IN ('Viewer', 'Guest')
            ORDER BY role
            """
            
            result = self.db.execute(text(query)).fetchall()
            roles = [row[0] for row in result if row[0]]
            
            self.logger.info(f"Dynamic workflow access roles: {roles}")
            return roles
            
        except Exception as e:
            self.logger.error(f"Error fetching workflow access roles: {str(e)}")
            # Fallback to basic roles if query fails
            return ['SystemAdmin', 'Administrator', 'Manager', 'User']
    
    def validate_global_objective_access(self, user_id: str, tenant_id: str, go_id: str) -> bool:
        """
        Validate that the user has write access to the first LO (origin) of the specified global objective.
        This follows the RBAC design where creating a workflow instance requires write permission to the first LO.
        
        Args:
            user_id: User ID
            tenant_id: Tenant ID
            go_id: Global Objective ID
            
        Returns:
            bool: True if user has access, False otherwise
        """
        try:
            self.logger.info(f"Validating GO access for user {user_id}, tenant {tenant_id}, GO {go_id}")
            
            # First, get the first LO (origin) for this GO
            first_lo_query = """
            SELECT lo_id FROM workflow_runtime.local_objectives
            WHERE go_id = :go_id
              AND LOWER(workflow_source) = 'origin'
            ORDER BY created_at ASC
            LIMIT 1
            """
            
            first_lo_result = self.db.execute(text(first_lo_query), {"go_id": go_id}).fetchone()
            
            if not first_lo_result:
                self.logger.warning(f"No origin LO found for GO {go_id}")
                return False
            
            first_lo_id = first_lo_result[0]
            self.logger.info(f"First LO for GO {go_id} is {first_lo_id}")
            
            # Check if user has write permission to the first LO using RBAC system
            # Get user role IDs
            role_query = """
            SELECT DISTINCT r.role_id
            FROM workflow_runtime.user_roles ur
            JOIN workflow_runtime.roles r ON ur.role = r.role_id
            WHERE ur.user_id = :user_id AND ur.tenant_id = :tenant_id
            """
            
            role_result = self.db.execute(text(role_query), {
                "user_id": user_id,
                "tenant_id": tenant_id
            }).fetchall()
            
            if not role_result:
                self.logger.warning(f"No roles found for user {user_id} in tenant {tenant_id}")
                return False
            
            user_role_ids = [row[0] for row in role_result]
            self.logger.info(f"User {user_id} has role IDs: {user_role_ids}")
            
            # Check LO-specific write permissions
            permission_query = """
            SELECT rsp.granted_actions
            FROM workflow_runtime.role_system_permissions rsp
            JOIN workflow_runtime.system_permissions sp ON rsp.permission_id = sp.permission_id
            WHERE rsp.role_id = ANY(:role_ids) 
            AND sp.resource_identifier = :resource
            """
            
            permission_result = self.db.execute(text(permission_query), {
                "role_ids": user_role_ids,
                "resource": first_lo_id
            }).fetchall()
            
            # Check if any role has write permission (create, update, or write)
            for row in permission_result:
                granted_actions = row[0]  # This is a JSON array
                if isinstance(granted_actions, list):
                    if any(a in granted_actions for a in ["create", "update", "write"]):
                        self.logger.info(f"User {user_id} granted write access to first LO {first_lo_id}")
                        return True
            
            self.logger.warning(f"User {user_id} denied write access to first LO {first_lo_id}")
            return False
            
        except Exception as e:
            self.logger.error(f"Error validating global objective access: {str(e)}")
            return False
    
    def validate_workflow_instance_access(self, user_id: str, tenant_id: str, instance_id: str) -> bool:
        """
        Validate that the user has access to the specified workflow instance.
        
        Args:
            user_id: User ID
            tenant_id: Tenant ID
            instance_id: Workflow Instance ID
            
        Returns:
            bool: True if user has access, False otherwise
        """
        try:
            # Get dynamic workflow access roles
            workflow_roles = self._get_workflow_access_roles()
            
            if not workflow_roles:
                self.logger.warning("No workflow access roles found")
                return False
            
            # Build dynamic query with roles
            roles_placeholder = ','.join([f"'{role}'" for role in workflow_roles])
            
            query = f"""
            SELECT COUNT(*)
            FROM workflow_runtime.workflow_instances wi
            JOIN workflow_runtime.user_roles ur ON ur.tenant_id = wi.tenant_id
            WHERE wi.instance_id = :instance_id 
              AND wi.tenant_id = :tenant_id
              AND ur.user_id = :user_id
              AND ur.role IN ({roles_placeholder})
            """
            
            result = self.db.execute(text(query), {
                "instance_id": instance_id,
                "tenant_id": tenant_id,
                "user_id": user_id
            }).fetchone()
            
            return result[0] > 0 if result else False
            
        except Exception as e:
            self.logger.error(f"Error validating workflow instance access: {str(e)}")
            return False
    
    def create_workflow_instance(
        self, 
        user_id: str, 
        tenant_id: str, 
        request: CreateWorkflowInstanceRequest
    ) -> Optional[WorkflowInstanceResponse]:
        """
        Create a new workflow instance.
        
        Args:
            user_id: User ID creating the instance
            tenant_id: Tenant ID
            request: Create workflow instance request
            
        Returns:
            Optional[WorkflowInstanceResponse]: Created workflow instance or None if failed
        """
        try:
            # Validate access to global objective
            if not self.validate_global_objective_access(user_id, tenant_id, request.go_id):
                self.logger.warning(f"User {user_id} does not have access to global objective {request.go_id}")
                return None
            
            # Generate instance ID
            instance_id = str(uuid.uuid4())
            current_time = datetime.utcnow()
            
            # Update last_used field in global_objectives
            update_last_used_query = """
            UPDATE workflow_runtime.global_objectives
            SET last_used = :current_time
            WHERE go_id = :go_id AND tenant_id = :tenant_id
            """
            self.db.execute(text(update_last_used_query), {
                "go_id": request.go_id, 
                "current_time": current_time,
                "tenant_id": tenant_id
            })
            
            # Create workflow instance
            create_query = """
            INSERT INTO workflow_runtime.workflow_instances (
                instance_id, go_id, tenant_id, status, started_by, started_at,
                current_lo_id, instance_data, is_test, version
            ) VALUES (
                :instance_id, :go_id, :tenant_id, :status, :started_by, :started_at,
                NULL, :instance_data, :is_test, :version
            )
            RETURNING *;
            """
            
            params = {
                "instance_id": instance_id,
                "go_id": request.go_id,
                "tenant_id": tenant_id,
                "status": WorkflowInstanceStatus.DRAFT.value,
                "started_by": request.user_id,
                "started_at": current_time,
                "instance_data": json.dumps({}),
                "is_test": request.test_mode,
                "version": "1.0"
            }
            
            result = self.db.execute(text(create_query), params).fetchone()
            
            if not result:
                self.logger.error("Failed to create workflow instance")
                return None
            
            # Create transaction log entry
            transaction_query = """
            INSERT INTO workflow_transaction (
                workflow_instance_id, go_id, lo_id, tenant_id, user_id, 
                status, input_stack, created_at, updated_at
            ) VALUES (
                :workflow_instance_id, :go_id, :lo_id, :tenant_id, :user_id,
                :status, :input_stack, NOW(), NOW()
            )
            """
            
            self.db.execute(text(transaction_query), {
                "workflow_instance_id": instance_id,
                "go_id": request.go_id,
                "lo_id": '',
                "tenant_id": tenant_id,
                "user_id": request.user_id,
                "status": 'pending',
                "input_stack": json.dumps({})
            })
            
            # Initialize input and output execution stacks for all local objectives
            self._initialize_execution_stacks(instance_id, request.go_id, request.user_id)
            
            # Pre-create all individual LO execution tables
            self._create_all_lo_execution_tables(request.go_id)
            
            # Commit transaction
            self.db.commit()
            
            # Convert result to response model
            return WorkflowInstanceResponse(
                instance_id=str(result.instance_id),
                go_id=result.go_id,
                tenant_id=result.tenant_id,
                status=WorkflowInstanceStatus(result.status),
                started_by=result.started_by,
                started_at=result.started_at,
                completed_at=result.completed_at,
                current_lo_id=str(result.current_lo_id) if result.current_lo_id else None,
                instance_data=json.loads(result.instance_data) if result.instance_data else {},
                is_test=result.is_test,
                version=result.version
            )
            
        except Exception as e:
            self.logger.error(f"Error creating workflow instance: {str(e)}")
            self.db.rollback()
            return None
    
    def _initialize_execution_stacks(self, instance_id: str, go_id: str, user_id: str = None) -> None:
        """
        Initialize input and output execution stacks for all local objectives and nested functions.
        
        Args:
            instance_id: Workflow instance ID
            go_id: Global Objective ID
            user_id: User ID creating the instance (for audit fields)
        """
        try:
            self.logger.info(f"🔧 Initializing execution stacks for instance {instance_id}, GO {go_id}")
            
            # Get all local objectives for this global objective
            los_query = """
            SELECT lo_id FROM workflow_runtime.local_objectives 
            WHERE go_id = :go_id
            ORDER BY lo_id
            """
            los = self.db.execute(text(los_query), {"go_id": go_id}).fetchall()
            
            self.logger.info(f"📋 Found {len(los)} local objectives for GO {go_id}")
            
            # Initialize input and output stacks for each local objective
            for lo in los:
                lo_id = lo[0]
                self.logger.info(f"🔧 Initializing execution stacks for LO {lo_id}")
                
                # 1. Initialize LO input execution stack with proper contextual_id and audit fields
                input_stack_query = """
                INSERT INTO workflow_runtime.lo_input_execution (
                    instance_id, lo_id, input_contextual_id, input_value, 
                    created_at, created_by, updated_at, updated_by,
                    lo_input_item_id, entity_id, attribute_id, entity_name, attribute_name, go_id, lo_id_ref
                )
                SELECT :instance_id, :lo_id, slot_id, NULL, 
                       NOW(), :user_id, NOW(), :user_id,
                       item_id, entity_id, attribute_id, entity_name, attribute_name, 
                       SPLIT_PART(:lo_id, '.', 1), :lo_id
                FROM workflow_runtime.lo_input_items 
                WHERE lo_id = :lo_id
                """
                input_result = self.db.execute(text(input_stack_query), {
                    "instance_id": instance_id, 
                    "lo_id": lo_id,
                    "user_id": user_id
                })
                self.logger.info(f"✅ Created {input_result.rowcount} input execution records for LO {lo_id}")
                
                # 2. Initialize LO output execution stack
                output_stack_query = """
                INSERT INTO workflow_runtime.lo_output_execution (
                    instance_id, lo_id, output_contextual_id, output_value, created_at,
                    lo_output_item_id, entity_id, attribute_id, entity_name, attribute_name, go_id, lo_id_ref
                )
                SELECT :instance_id, :lo_id, slot_id, NULL, NOW(),
                       item_id, entity_id, attribute_id, entity_name, attribute_name, 
                       SPLIT_PART(:lo_id, '.', 1), :lo_id
                FROM workflow_runtime.lo_output_items 
                WHERE lo_id = :lo_id
                """
                output_result = self.db.execute(text(output_stack_query), {
                    "instance_id": instance_id, 
                    "lo_id": lo_id
                })
                self.logger.info(f"✅ Created {output_result.rowcount} output execution records for LO {lo_id}")
                
                # 3. Initialize nested function input execution stacks
                nested_functions_query = """
                SELECT DISTINCT nested_function_id 
                FROM workflow_runtime.lo_input_items 
                WHERE lo_id = :lo_id AND nested_function_id IS NOT NULL
                """
                nested_functions = self.db.execute(text(nested_functions_query), {"lo_id": lo_id}).fetchall()
                
                for nf in nested_functions:
                    nested_function_id = nf[0]
                    self.logger.info(f"🔧 Initializing nested function {nested_function_id} for LO {lo_id}")
                    
                    # Initialize nested function input execution
                    nf_input_query = """
                    INSERT INTO workflow_runtime.lo_nested_function_input_execution (
                        instance_id, nested_function_id, input_contextual_id, input_value,
                        created_at, created_by, updated_at, updated_by
                    )
                    SELECT :instance_id, :nested_function_id, slot_id, NULL,
                           NOW(), :user_id, NOW(), :user_id
                    FROM workflow_runtime.lo_nested_function_input_items 
                    WHERE nested_function_id = :nested_function_id
                    """
                    nf_input_result = self.db.execute(text(nf_input_query), {
                        "instance_id": instance_id,
                        "nested_function_id": nested_function_id,
                        "user_id": user_id
                    })
                    self.logger.info(f"✅ Created {nf_input_result.rowcount} nested function input records for {nested_function_id}")
                    
                    # Initialize nested function output execution with proper format and entity/attribute info
                    # Get entity/attribute info from lo_nested_function_input_items table (take first match to avoid duplicates)
                    nf_output_query = """
                    INSERT INTO workflow_runtime.lo_nested_function_output_execution (
                        instance_id, nested_function_id, output_contextual_id, output_value,
                        execution_status, entity_id, attribute_id, created_at, created_by, updated_at, updated_by
                    )
                    SELECT DISTINCT CAST(:instance_id AS uuid), nfoi.nested_function_id, nfoi.item_id, NULL::jsonb,
                           'pending', 
                           COALESCE(nfii.entity_id, 'E1') as entity_id,
                           COALESCE(nfii.attribute_id, 'E1.At1') as attribute_id,
                           NOW(), :user_id, NOW(), :user_id
                    FROM workflow_runtime.lo_nested_function_output_items nfoi
                    LEFT JOIN (
                        SELECT DISTINCT nested_function_id, 
                               FIRST_VALUE(entity_id) OVER (PARTITION BY nested_function_id ORDER BY item_id) as entity_id,
                               FIRST_VALUE(attribute_id) OVER (PARTITION BY nested_function_id ORDER BY item_id) as attribute_id
                        FROM workflow_runtime.lo_nested_function_input_items
                    ) nfii ON nfoi.nested_function_id = nfii.nested_function_id
                    WHERE nfoi.nested_function_id = :nested_function_id
                    """
                    nf_output_result = self.db.execute(text(nf_output_query), {
                        "instance_id": instance_id,
                        "nested_function_id": nested_function_id,
                        "user_id": user_id
                    })
                    self.logger.info(f"✅ Created {nf_output_result.rowcount} nested function output records for {nested_function_id}")
            
            self.logger.info(f"🎉 Successfully initialized all execution stacks for instance {instance_id}")
                
        except Exception as e:
            self.logger.error(f"Error initializing execution stacks: {str(e)}")
            raise
    
    def _create_all_lo_execution_tables(self, go_id: str) -> None:
        """Pre-create all individual LO execution tables for the global objective"""
        try:
            self.logger.info(f"🔧 Pre-creating all LO execution tables for GO {go_id}")
            
            # Get all local objectives for this global objective
            los_query = """
            SELECT lo_id, name FROM workflow_runtime.local_objectives 
            WHERE go_id = :go_id
            ORDER BY lo_id
            """
            los = self.db.execute(text(los_query), {"go_id": go_id}).fetchall()
            
            for lo in los:
                lo_id = lo[0]
                lo_name = lo[1]
                
                # Create table name
                clean_lo_id = lo_id.replace(".", "_")
                clean_lo_name = lo_name.replace(" ", "").replace("-", "_").replace(".", "_")
                table_name = f"{clean_lo_id}_{clean_lo_name}"
                
                self.logger.info(f"🔧 Creating LO execution table: {table_name}")
                
                # Create the table with proper columns
                self._ensure_lo_execution_table_exists(table_name, lo_id)
                
            self.logger.info(f"✅ Successfully created all LO execution tables for GO {go_id}")
            
        except Exception as e:
            self.logger.error(f"Error creating LO execution tables: {str(e)}")
            raise
    
    def _ensure_lo_execution_table_exists(self, table_name: str, lo_id: str) -> None:
        """Create individual LO execution table with proper columns"""
        try:
            # Get input field names from lo_input_items
            input_fields_query = """
            SELECT DISTINCT ea.name as attribute_name, ea.datatype as data_type
            FROM workflow_runtime.lo_input_items lii
            JOIN workflow_runtime.entity_attributes ea ON lii.attribute_id = ea.attribute_id
            WHERE lii.lo_id = :lo_id AND ea.name IS NOT NULL
            ORDER BY ea.name
            """
            
            input_fields = self.db.execute(text(input_fields_query), {"lo_id": lo_id}).fetchall()
            
            # Get output field names from lo_output_items
            output_fields_query = """
            SELECT DISTINCT ea.name as attribute_name, ea.datatype as data_type
            FROM workflow_runtime.lo_output_items loo
            JOIN workflow_runtime.entity_attributes ea ON loo.attribute_id = ea.attribute_id
            WHERE loo.lo_id = :lo_id AND ea.name IS NOT NULL
            ORDER BY ea.name
            """
            
            output_fields = self.db.execute(text(output_fields_query), {"lo_id": lo_id}).fetchall()
            
            # Build column definitions
            columns = []
            columns.append("id SERIAL PRIMARY KEY")
            columns.append("workflow_instance_id UUID NOT NULL")
            columns.append("user_id VARCHAR(255) NOT NULL")
            columns.append("execution_timestamp TIMESTAMP NOT NULL")
            columns.append("status VARCHAR(50) NOT NULL")
            columns.append("created_at TIMESTAMP DEFAULT NOW()")
            
            # Add input columns (avoid duplicates)
            added_columns = set()
            for field in input_fields:
                if field.attribute_name:
                    col_name = f"input_{field.attribute_name.lower()}"
                    if col_name not in added_columns:
                        col_type = self._map_data_type_to_sql(field.data_type)
                        columns.append(f"{col_name} {col_type}")
                        added_columns.add(col_name)
            
            # Add output columns (avoid duplicates)
            for field in output_fields:
                if field.attribute_name:
                    col_name = f"output_{field.attribute_name.lower()}"
                    if col_name not in added_columns:
                        col_type = self._map_data_type_to_sql(field.data_type)
                        columns.append(f"{col_name} {col_type}")
                        added_columns.add(col_name)
            
            # Create table
            create_table_query = f"""
            CREATE TABLE IF NOT EXISTS workflow_runtime.{table_name} (
                {', '.join(columns)}
            )
            """
            
            self.db.execute(text(create_table_query))
            self.logger.info(f"✅ Created LO execution table {table_name} with {len(columns)} columns")
            
        except Exception as e:
            self.logger.error(f"Error creating LO execution table {table_name}: {str(e)}")
            raise
    
    def _map_data_type_to_sql(self, data_type: str) -> str:
        """Map entity attribute data types to SQL column types"""
        if not data_type:
            return "TEXT"
        
        data_type_lower = data_type.lower()
        
        if data_type_lower in ['string', 'text', 'varchar']:
            return "TEXT"
        elif data_type_lower in ['integer', 'int']:
            return "INTEGER"
        elif data_type_lower in ['boolean', 'bool']:
            return "BOOLEAN"
        elif data_type_lower in ['date']:
            return "DATE"
        elif data_type_lower in ['datetime', 'timestamp']:
            return "TIMESTAMP"
        elif data_type_lower in ['decimal', 'numeric', 'float']:
            return "DECIMAL"
        else:
            return "TEXT"  # Default fallback
    
    def start_workflow_instance(
        self, 
        user_id: str, 
        tenant_id: str, 
        instance_id: str,
        request: StartWorkflowInstanceRequest
    ) -> Optional[WorkflowInstanceResponse]:
        """
        Start a workflow instance.
        
        Args:
            user_id: User ID starting the instance
            tenant_id: Tenant ID
            instance_id: Workflow Instance ID
            request: Start workflow instance request
            
        Returns:
            Optional[WorkflowInstanceResponse]: Started workflow instance or None if failed
        """
        try:
            # Validate access to workflow instance
            if not self.validate_workflow_instance_access(user_id, tenant_id, instance_id):
                self.logger.warning(f"User {user_id} does not have access to workflow instance {instance_id}")
                return None
            
            # Get current workflow instance
            get_instance_query = """
            SELECT * FROM workflow_runtime.workflow_instances 
            WHERE instance_id = :instance_id AND tenant_id = :tenant_id
            """
            result = self.db.execute(text(get_instance_query), {
                "instance_id": instance_id,
                "tenant_id": tenant_id
            }).fetchone()
            
            if not result:
                self.logger.error(f"Workflow instance {instance_id} not found")
                return None
            
            # Validate instance is in DRAFT status
            if result.status != WorkflowInstanceStatus.DRAFT.value:
                self.logger.error(f"Workflow instance {instance_id} is not in DRAFT status")
                return None
            
            go_id = result.go_id
            
            # Get the first local objective (origin workflow source)
            first_lo_query = """
            SELECT lo_id FROM workflow_runtime.local_objectives
            WHERE go_id = :go_id
              AND LOWER(workflow_source) = 'origin'
            ORDER BY created_at ASC
            LIMIT 1
            """
            
            first_lo = self.db.execute(text(first_lo_query), {"go_id": go_id}).fetchone()
            
            if not first_lo:
                self.logger.error(f"No starting local objective found for workflow {go_id}")
                return None
            
            first_lo_id = str(first_lo[0])
            
            # Update workflow instance to ACTIVE status
            update_query = """
            UPDATE workflow_runtime.workflow_instances
            SET status = :status, current_lo_id = :current_lo_id, updated_at = NOW()
            WHERE instance_id = :instance_id AND tenant_id = :tenant_id
            RETURNING *;
            """
            
            update_result = self.db.execute(text(update_query), {
                "status": WorkflowInstanceStatus.ACTIVE.value,
                "current_lo_id": first_lo_id,
                "instance_id": instance_id,
                "tenant_id": tenant_id
            }).fetchone()
            
            if not update_result:
                self.logger.error("Failed to update workflow instance to ACTIVE")
                return None
            
            # Commit transaction
            self.db.commit()
            
            # Convert result to response model
            return WorkflowInstanceResponse(
                instance_id=str(update_result.instance_id),
                go_id=update_result.go_id,
                tenant_id=update_result.tenant_id,
                status=WorkflowInstanceStatus(update_result.status),
                started_by=update_result.started_by,
                started_at=update_result.started_at,
                completed_at=update_result.completed_at,
                current_lo_id=str(update_result.current_lo_id) if update_result.current_lo_id else None,
                instance_data=json.loads(update_result.instance_data) if update_result.instance_data else {},
                is_test=update_result.is_test,
                version=update_result.version
            )
            
        except Exception as e:
            self.logger.error(f"Error starting workflow instance: {str(e)}")
            self.db.rollback()
            return None
    
    def get_workflow_instance(
        self, 
        user_id: str, 
        tenant_id: str, 
        instance_id: str
    ) -> Optional[WorkflowInstanceResponse]:
        """
        Get a workflow instance by ID.
        
        Args:
            user_id: User ID requesting the instance
            tenant_id: Tenant ID
            instance_id: Workflow Instance ID
            
        Returns:
            Optional[WorkflowInstanceResponse]: Workflow instance or None if not found/no access
        """
        try:
            # Validate access to workflow instance
            if not self.validate_workflow_instance_access(user_id, tenant_id, instance_id):
                self.logger.warning(f"User {user_id} does not have access to workflow instance {instance_id}")
                return None
            
            # Get workflow instance
            query = """
            SELECT * FROM workflow_runtime.workflow_instances 
            WHERE instance_id = :instance_id AND tenant_id = :tenant_id
            """
            result = self.db.execute(text(query), {
                "instance_id": instance_id,
                "tenant_id": tenant_id
            }).fetchone()
            
            if not result:
                return None
            
            # Convert result to response model
            return WorkflowInstanceResponse(
                instance_id=str(result.instance_id),
                go_id=result.go_id,
                tenant_id=result.tenant_id,
                status=WorkflowInstanceStatus(result.status),
                started_by=result.started_by,
                started_at=result.started_at,
                completed_at=result.completed_at,
                current_lo_id=str(result.current_lo_id) if result.current_lo_id else None,
                instance_data=json.loads(result.instance_data) if result.instance_data else {},
                is_test=result.is_test,
                version=result.version
            )
            
        except Exception as e:
            self.logger.error(f"Error getting workflow instance: {str(e)}")
            return None
    
    def list_workflow_instances(
        self, 
        user_id: str, 
        tenant_id: str,
        status: Optional[str] = None,
        go_id: Optional[str] = None,
        page: int = 1,
        page_size: int = 10
    ) -> Optional[WorkflowInstanceListResponse]:
        """
        List workflow instances with filtering and pagination.
        
        Args:
            user_id: User ID requesting the list
            tenant_id: Tenant ID
            status: Optional status filter
            go_id: Optional global objective ID filter
            page: Page number (1-based)
            page_size: Number of items per page
            
        Returns:
            Optional[WorkflowInstanceListResponse]: List of workflow instances or None if error
        """
        try:
            # Get dynamic workflow access roles
            workflow_roles = self._get_workflow_access_roles()
            
            if not workflow_roles:
                self.logger.warning("No workflow access roles found")
                return None
            
            # Build dynamic query with roles
            roles_placeholder = ','.join([f"'{role}'" for role in workflow_roles])
            
            # Build base query with RBAC
            base_query = f"""
            FROM workflow_runtime.workflow_instances wi
            JOIN workflow_runtime.user_roles ur ON ur.tenant_id = wi.tenant_id
            WHERE wi.tenant_id = :tenant_id
              AND ur.user_id = :user_id
              AND ur.role IN ({roles_placeholder})
            """
            
            params = {
                "tenant_id": tenant_id,
                "user_id": user_id
            }
            
            # Add filters
            if status:
                base_query += " AND wi.status = :status"
                params["status"] = status
            
            if go_id:
                base_query += " AND wi.go_id = :go_id"
                params["go_id"] = go_id
            
            # Get total count
            count_query = f"SELECT COUNT(DISTINCT wi.instance_id) {base_query}"
            total_count = self.db.execute(text(count_query), params).scalar()
            
            # Get paginated results
            offset = (page - 1) * page_size
            list_query = f"""
            SELECT DISTINCT wi.* {base_query}
            ORDER BY wi.started_at DESC
            LIMIT :limit OFFSET :offset
            """
            params.update({
                "limit": page_size,
                "offset": offset
            })
            
            results = self.db.execute(text(list_query), params).fetchall()
            
            # Convert results to response models
            instances = []
            for result in results:
                instances.append(WorkflowInstanceResponse(
                    instance_id=str(result.instance_id),
                    go_id=result.go_id,
                    tenant_id=result.tenant_id,
                    status=WorkflowInstanceStatus(result.status),
                    started_by=result.started_by,
                    started_at=result.started_at,
                    completed_at=result.completed_at,
                    current_lo_id=str(result.current_lo_id) if result.current_lo_id else None,
                    instance_data=json.loads(result.instance_data) if result.instance_data else {},
                    is_test=result.is_test,
                    version=result.version
                ))
            
            return WorkflowInstanceListResponse(
                instances=instances,
                total_count=total_count,
                page=page,
                page_size=page_size
            )
            
        except Exception as e:
            self.logger.error(f"Error listing workflow instances: {str(e)}")
            return None
