{"timestamp": "2025-06-24T04:45:01.379259", "operation": "insert_attribute_validation_to_workflow_temp", "input_data": {"_id": "6852ee1a727e97ac18d0839e", "validation_id": 546, "attribute_id": "E8.At42", "entity_name": "Employee", "attribute_name": "employeeId", "left_operand": "employeeId", "operator": "IS_UNIQUE", "right_operand": "", "success_value": "unique", "warning_value": "", "failure_value": "duplicate", "multi_condition_operator": "FALSE", "warning_message": "", "success_message": "Employee ID is valid", "error_message": "Employee ID must be unique", "natural_language": "Employee.employeeId: employeeId IS_UNIQUE ", "version": 1, "status": "draft", "created_at": "2025-06-18T16:49:30.448481", "updated_at": "2025-06-18T16:49:30.448484", "created_by": "system", "updated_by": "system"}, "result": {"success": true, "inserted_id": 78, "schema": "workflow_temp", "validation_id": 546, "attribute_id": "E8.At42", "original_validation_id": 1, "original_attribute_id": "E8.At1"}, "status": "success"}
{"timestamp": "2025-06-24T04:45:01.381994", "operation": "deploy_single_attribute_validation_to_workflow_temp", "input_data": {"_id": "6852ee1a727e97ac18d0839e", "validation_id": 546, "attribute_id": "E8.At42", "entity_name": "Employee", "attribute_name": "employeeId", "left_operand": "employeeId", "operator": "IS_UNIQUE", "right_operand": "", "success_value": "unique", "warning_value": "", "failure_value": "duplicate", "multi_condition_operator": "FALSE", "warning_message": "", "success_message": "Employee ID is valid", "error_message": "Employee ID must be unique", "natural_language": "Employee.employeeId: employeeId IS_UNIQUE ", "version": 1, "status": "draft", "created_at": "2025-06-18T16:49:30.448481", "updated_at": "2025-06-18T16:49:30.448484", "created_by": "system", "updated_by": "system"}, "result": {"success": true, "inserted_id": 78, "schema": "workflow_temp", "validation_id": 546, "attribute_id": "E8.At42", "original_validation_id": 1, "original_attribute_id": "E8.At1"}, "status": "success"}
{"timestamp": "2025-06-24T04:45:01.527523", "operation": "insert_attribute_validation_to_workflow_temp", "input_data": {"_id": "6854488a754fbf4d729d3a7a", "validation_id": 556, "attribute_id": "E8.At43", "entity_name": "Employee", "attribute_name": "email", "left_operand": "email", "operator": "IS_UNIQUE", "right_operand": "", "success_value": "unique", "warning_value": "", "failure_value": "duplicate", "multi_condition_operator": "FALSE", "warning_message": "", "success_message": "Email is unique", "error_message": "Email must be unique", "natural_language": "Employee.email: email IS_UNIQUE ", "version": 1, "status": "draft", "created_at": "2025-06-19T17:27:38.404018", "updated_at": "2025-06-19T17:27:38.404025", "created_by": "system", "updated_by": "system"}, "result": {"success": true, "inserted_id": 79, "schema": "workflow_temp", "validation_id": 556, "attribute_id": "E8.At43", "original_validation_id": 2, "original_attribute_id": "E8.At4"}, "status": "success"}
{"timestamp": "2025-06-24T04:45:01.556872", "operation": "insert_attribute_validation_to_workflow_temp", "input_data": {"_id": "6854ee73fa0d6bfc16038bef", "validation_id": 566, "attribute_id": "E8.At44", "entity_name": "Employee", "attribute_name": "email", "left_operand": "email", "operator": "IS_UNIQUE", "right_operand": "", "success_value": "unique", "warning_value": "", "failure_value": "duplicate", "multi_condition_operator": "FALSE", "warning_message": "", "success_message": "Email is unique", "error_message": "Email must be unique", "natural_language": "Employee.email: email IS_UNIQUE ", "version": 1, "status": "draft", "created_at": "2025-06-20T05:15:31.135351", "updated_at": "2025-06-20T05:15:31.135357", "created_by": "system", "updated_by": "system"}, "result": {"success": true, "inserted_id": 80, "schema": "workflow_temp", "validation_id": 566, "attribute_id": "E8.At44", "original_validation_id": 6, "original_attribute_id": "E8.At4"}, "status": "success"}
{"timestamp": "2025-06-24T04:45:01.584838", "operation": "insert_attribute_validation_to_workflow_temp", "input_data": {"_id": "6854ee73fa0d6bfc16038bf0", "validation_id": 576, "attribute_id": "E8.At45", "entity_name": "Employee", "attribute_name": "employeeId", "left_operand": "employeeId", "operator": "IS_UNIQUE", "right_operand": "", "success_value": "unique", "warning_value": "", "failure_value": "duplicate", "multi_condition_operator": "FALSE", "warning_message": "", "success_message": "Employee ID is valid", "error_message": "Employee ID must be unique", "natural_language": "Employee.employeeId: employeeId IS_UNIQUE ", "version": 1, "status": "draft", "created_at": "2025-06-20T05:15:31.135903", "updated_at": "2025-06-20T05:15:31.135906", "created_by": "system", "updated_by": "system"}, "result": {"success": true, "inserted_id": 81, "schema": "workflow_temp", "validation_id": 576, "attribute_id": "E8.At45", "original_validation_id": 5, "original_attribute_id": "E8.At1"}, "status": "success"}
{"timestamp": "2025-06-24T04:45:01.612733", "operation": "insert_attribute_validation_to_workflow_temp", "input_data": {"_id": "6854f3ac9031164ff452a0f8", "validation_id": 586, "attribute_id": "E8.At46", "entity_name": "Employee", "attribute_name": "phone", "left_operand": "phone", "operator": "IS_UNIQUE", "right_operand": "", "success_value": "unique", "warning_value": "", "failure_value": "duplicate", "multi_condition_operator": "FALSE", "warning_message": "", "success_message": "Phone number is unique", "error_message": "Phone number must be unique", "natural_language": "Employee.phone: phone IS_UNIQUE ", "version": 1, "status": "draft", "created_at": "2025-06-20T05:37:48.522400", "updated_at": "2025-06-20T05:37:48.522404", "created_by": "system", "updated_by": "system"}, "result": {"success": true, "inserted_id": 82, "schema": "workflow_temp", "validation_id": 586, "attribute_id": "E8.At46", "original_validation_id": 7, "original_attribute_id": "E8.At4"}, "status": "success"}
{"timestamp": "2025-06-24T04:45:01.640452", "operation": "insert_attribute_validation_to_workflow_temp", "input_data": {"_id": "685508447147def4f91d1022", "validation_id": 596, "attribute_id": "E7.At36", "entity_name": "LeaveApplication", "attribute_name": "leaveId", "left_operand": "leaveId", "operator": "IS_UNIQUE", "right_operand": "", "success_value": "unique", "warning_value": "", "failure_value": "duplicate", "multi_condition_operator": "FALSE", "warning_message": "", "success_message": "Leave ID is valid", "error_message": "Leave ID must be unique", "natural_language": "LeaveApplication.leaveId: leaveId IS_UNIQUE ", "version": 1, "status": "draft", "created_at": "2025-06-20T07:05:40.927340", "updated_at": "2025-06-20T07:05:40.927345", "created_by": "system", "updated_by": "system"}, "result": {"success": true, "inserted_id": 83, "schema": "workflow_temp", "validation_id": 596, "attribute_id": "E7.At36", "original_validation_id": 1, "original_attribute_id": "E7.At1"}, "status": "success"}
{"timestamp": "2025-06-24T04:45:01.668267", "operation": "insert_attribute_validation_to_workflow_temp", "input_data": {"_id": "685508447147def4f91d1023", "validation_id": 606, "attribute_id": "E7.At37", "entity_name": "LeaveApplication", "attribute_name": "employeeId", "left_operand": "employeeId", "operator": "EXISTS_IN", "right_operand": "Employee.employeeId", "success_value": "exists", "warning_value": "", "failure_value": "not_exists", "multi_condition_operator": "FALSE", "warning_message": "", "success_message": "Employee ID is valid", "error_message": "Employee ID must exist in Employee table", "natural_language": "LeaveApplication.employeeId: employeeId EXISTS_IN Employee.employeeId", "version": 1, "status": "draft", "created_at": "2025-06-20T07:05:40.932582", "updated_at": "2025-06-20T07:05:40.932597", "created_by": "system", "updated_by": "system"}, "result": {"success": true, "inserted_id": 84, "schema": "workflow_temp", "validation_id": 606, "attribute_id": "E7.At37", "original_validation_id": 2, "original_attribute_id": "E7.At2"}, "status": "success"}
{"timestamp": "2025-06-24T04:45:01.694607", "operation": "insert_attribute_validation_to_workflow_temp", "input_data": {"_id": "685508447147def4f91d1024", "validation_id": 616, "attribute_id": "E7.At38", "entity_name": "LeaveApplication", "attribute_name": "startDate", "left_operand": "startDate", "operator": "IS_VALID_DATE", "right_operand": "", "success_value": "valid_date", "warning_value": "", "failure_value": "invalid_date", "multi_condition_operator": "FALSE", "warning_message": "", "success_message": "Start date is valid", "error_message": "Start date must be a valid date", "natural_language": "LeaveApplication.startDate: startDate IS_VALID_DATE ", "version": 1, "status": "draft", "created_at": "2025-06-20T07:05:40.934871", "updated_at": "2025-06-20T07:05:40.934878", "created_by": "system", "updated_by": "system"}, "result": {"success": true, "inserted_id": 85, "schema": "workflow_temp", "validation_id": 616, "attribute_id": "E7.At38", "original_validation_id": 3, "original_attribute_id": "E7.At3"}, "status": "success"}
{"timestamp": "2025-06-24T04:45:01.720623", "operation": "insert_attribute_validation_to_workflow_temp", "input_data": {"_id": "685508447147def4f91d1025", "validation_id": 626, "attribute_id": "E7.At39", "entity_name": "LeaveApplication", "attribute_name": "endDate", "left_operand": "endDate", "operator": "GREATER_THAN", "right_operand": "startDate", "success_value": "after_start", "warning_value": "", "failure_value": "before_or_equal_start", "multi_condition_operator": "FALSE", "warning_message": "", "success_message": "End date is valid", "error_message": "End date must be after start date", "natural_language": "LeaveApplication.endDate: endDate GREATER_THAN startDate", "version": 1, "status": "draft", "created_at": "2025-06-20T07:05:40.936694", "updated_at": "2025-06-20T07:05:40.936701", "created_by": "system", "updated_by": "system"}, "result": {"success": true, "inserted_id": 86, "schema": "workflow_temp", "validation_id": 626, "attribute_id": "E7.At39", "original_validation_id": 4, "original_attribute_id": "E7.At4"}, "status": "success"}
{"timestamp": "2025-06-24T04:45:01.722443", "operation": "process_mongo_attribute_validations_to_workflow_temp", "input_data": {"schema": "workflow_temp"}, "result": {"total_processed": 8, "successful_inserts": 8, "failed_inserts": 0, "details": [{"validation_id": 556, "attribute_id": "E8.At43", "status": "success", "details": {"success": true, "inserted_id": 79, "schema": "workflow_temp", "validation_id": 556, "attribute_id": "E8.At43", "original_validation_id": 2, "original_attribute_id": "E8.At4"}}, {"validation_id": 566, "attribute_id": "E8.At44", "status": "success", "details": {"success": true, "inserted_id": 80, "schema": "workflow_temp", "validation_id": 566, "attribute_id": "E8.At44", "original_validation_id": 6, "original_attribute_id": "E8.At4"}}, {"validation_id": 576, "attribute_id": "E8.At45", "status": "success", "details": {"success": true, "inserted_id": 81, "schema": "workflow_temp", "validation_id": 576, "attribute_id": "E8.At45", "original_validation_id": 5, "original_attribute_id": "E8.At1"}}, {"validation_id": 586, "attribute_id": "E8.At46", "status": "success", "details": {"success": true, "inserted_id": 82, "schema": "workflow_temp", "validation_id": 586, "attribute_id": "E8.At46", "original_validation_id": 7, "original_attribute_id": "E8.At4"}}, {"validation_id": 596, "attribute_id": "E7.At36", "status": "success", "details": {"success": true, "inserted_id": 83, "schema": "workflow_temp", "validation_id": 596, "attribute_id": "E7.At36", "original_validation_id": 1, "original_attribute_id": "E7.At1"}}, {"validation_id": 606, "attribute_id": "E7.At37", "status": "success", "details": {"success": true, "inserted_id": 84, "schema": "workflow_temp", "validation_id": 606, "attribute_id": "E7.At37", "original_validation_id": 2, "original_attribute_id": "E7.At2"}}, {"validation_id": 616, "attribute_id": "E7.At38", "status": "success", "details": {"success": true, "inserted_id": 85, "schema": "workflow_temp", "validation_id": 616, "attribute_id": "E7.At38", "original_validation_id": 3, "original_attribute_id": "E7.At3"}}, {"validation_id": 626, "attribute_id": "E7.At39", "status": "success", "details": {"success": true, "inserted_id": 86, "schema": "workflow_temp", "validation_id": 626, "attribute_id": "E7.At39", "original_validation_id": 4, "original_attribute_id": "E7.At4"}}]}, "status": "success"}
{"timestamp": "2025-06-24T11:47:14.616352", "operation": "insert_attribute_validation_to_workflow_runtime", "input_data": {"_id": "6854488a754fbf4d729d3a7a", "validation_id": 636, "attribute_id": "E8.At47", "entity_name": "Employee", "attribute_name": "email", "left_operand": "email", "operator": "IS_UNIQUE", "right_operand": "", "success_value": "unique", "warning_value": "", "failure_value": "duplicate", "multi_condition_operator": "FALSE", "warning_message": "", "success_message": "Email is unique", "error_message": "Email must be unique", "natural_language": "Employee.email: email IS_UNIQUE ", "version": 1, "status": "draft", "created_at": "2025-06-19T17:27:38.404018", "updated_at": "2025-06-19T17:27:38.404025", "created_by": "system", "updated_by": "system"}, "result": {"success": true, "inserted_id": 29, "schema": "workflow_runtime", "validation_id": 636, "attribute_id": "E8.At47", "original_validation_id": 2, "original_attribute_id": "E8.At4"}, "status": "success"}
{"timestamp": "2025-06-24T11:47:14.649770", "operation": "insert_attribute_validation_to_workflow_runtime", "input_data": {"_id": "6854ee73fa0d6bfc16038bef", "validation_id": 646, "attribute_id": "E8.At48", "entity_name": "Employee", "attribute_name": "email", "left_operand": "email", "operator": "IS_UNIQUE", "right_operand": "", "success_value": "unique", "warning_value": "", "failure_value": "duplicate", "multi_condition_operator": "FALSE", "warning_message": "", "success_message": "Email is unique", "error_message": "Email must be unique", "natural_language": "Employee.email: email IS_UNIQUE ", "version": 1, "status": "draft", "created_at": "2025-06-20T05:15:31.135351", "updated_at": "2025-06-20T05:15:31.135357", "created_by": "system", "updated_by": "system"}, "result": {"success": true, "inserted_id": 30, "schema": "workflow_runtime", "validation_id": 646, "attribute_id": "E8.At48", "original_validation_id": 6, "original_attribute_id": "E8.At4"}, "status": "success"}
{"timestamp": "2025-06-24T11:47:14.684488", "operation": "insert_attribute_validation_to_workflow_runtime", "input_data": {"_id": "6854ee73fa0d6bfc16038bf0", "validation_id": 656, "attribute_id": "E8.At49", "entity_name": "Employee", "attribute_name": "employeeId", "left_operand": "employeeId", "operator": "IS_UNIQUE", "right_operand": "", "success_value": "unique", "warning_value": "", "failure_value": "duplicate", "multi_condition_operator": "FALSE", "warning_message": "", "success_message": "Employee ID is valid", "error_message": "Employee ID must be unique", "natural_language": "Employee.employeeId: employeeId IS_UNIQUE ", "version": 1, "status": "draft", "created_at": "2025-06-20T05:15:31.135903", "updated_at": "2025-06-20T05:15:31.135906", "created_by": "system", "updated_by": "system"}, "result": {"success": true, "inserted_id": 31, "schema": "workflow_runtime", "validation_id": 656, "attribute_id": "E8.At49", "original_validation_id": 5, "original_attribute_id": "E8.At1"}, "status": "success"}
{"timestamp": "2025-06-24T11:47:14.714281", "operation": "insert_attribute_validation_to_workflow_runtime", "input_data": {"_id": "6854f3ac9031164ff452a0f8", "validation_id": 666, "attribute_id": "E8.At50", "entity_name": "Employee", "attribute_name": "phone", "left_operand": "phone", "operator": "IS_UNIQUE", "right_operand": "", "success_value": "unique", "warning_value": "", "failure_value": "duplicate", "multi_condition_operator": "FALSE", "warning_message": "", "success_message": "Phone number is unique", "error_message": "Phone number must be unique", "natural_language": "Employee.phone: phone IS_UNIQUE ", "version": 1, "status": "draft", "created_at": "2025-06-20T05:37:48.522400", "updated_at": "2025-06-20T05:37:48.522404", "created_by": "system", "updated_by": "system"}, "result": {"success": true, "inserted_id": 32, "schema": "workflow_runtime", "validation_id": 666, "attribute_id": "E8.At50", "original_validation_id": 7, "original_attribute_id": "E8.At4"}, "status": "success"}
{"timestamp": "2025-06-24T11:47:14.743142", "operation": "insert_attribute_validation_to_workflow_runtime", "input_data": {"_id": "685508447147def4f91d1022", "validation_id": 676, "attribute_id": "E7.At40", "entity_name": "LeaveApplication", "attribute_name": "leaveId", "left_operand": "leaveId", "operator": "IS_UNIQUE", "right_operand": "", "success_value": "unique", "warning_value": "", "failure_value": "duplicate", "multi_condition_operator": "FALSE", "warning_message": "", "success_message": "Leave ID is valid", "error_message": "Leave ID must be unique", "natural_language": "LeaveApplication.leaveId: leaveId IS_UNIQUE ", "version": 1, "status": "draft", "created_at": "2025-06-20T07:05:40.927340", "updated_at": "2025-06-20T07:05:40.927345", "created_by": "system", "updated_by": "system"}, "result": {"success": true, "inserted_id": 33, "schema": "workflow_runtime", "validation_id": 676, "attribute_id": "E7.At40", "original_validation_id": 1, "original_attribute_id": "E7.At1"}, "status": "success"}
{"timestamp": "2025-06-24T11:47:14.773679", "operation": "insert_attribute_validation_to_workflow_runtime", "input_data": {"_id": "685508447147def4f91d1023", "validation_id": 686, "attribute_id": "E7.At41", "entity_name": "LeaveApplication", "attribute_name": "employeeId", "left_operand": "employeeId", "operator": "EXISTS_IN", "right_operand": "Employee.employeeId", "success_value": "exists", "warning_value": "", "failure_value": "not_exists", "multi_condition_operator": "FALSE", "warning_message": "", "success_message": "Employee ID is valid", "error_message": "Employee ID must exist in Employee table", "natural_language": "LeaveApplication.employeeId: employeeId EXISTS_IN Employee.employeeId", "version": 1, "status": "draft", "created_at": "2025-06-20T07:05:40.932582", "updated_at": "2025-06-20T07:05:40.932597", "created_by": "system", "updated_by": "system"}, "result": {"success": true, "inserted_id": 34, "schema": "workflow_runtime", "validation_id": 686, "attribute_id": "E7.At41", "original_validation_id": 2, "original_attribute_id": "E7.At2"}, "status": "success"}
{"timestamp": "2025-06-24T11:47:14.807567", "operation": "insert_attribute_validation_to_workflow_runtime", "input_data": {"_id": "685508447147def4f91d1024", "validation_id": 696, "attribute_id": "E7.At42", "entity_name": "LeaveApplication", "attribute_name": "startDate", "left_operand": "startDate", "operator": "IS_VALID_DATE", "right_operand": "", "success_value": "valid_date", "warning_value": "", "failure_value": "invalid_date", "multi_condition_operator": "FALSE", "warning_message": "", "success_message": "Start date is valid", "error_message": "Start date must be a valid date", "natural_language": "LeaveApplication.startDate: startDate IS_VALID_DATE ", "version": 1, "status": "draft", "created_at": "2025-06-20T07:05:40.934871", "updated_at": "2025-06-20T07:05:40.934878", "created_by": "system", "updated_by": "system"}, "result": {"success": true, "inserted_id": 35, "schema": "workflow_runtime", "validation_id": 696, "attribute_id": "E7.At42", "original_validation_id": 3, "original_attribute_id": "E7.At3"}, "status": "success"}
{"timestamp": "2025-06-24T11:47:14.838666", "operation": "insert_attribute_validation_to_workflow_runtime", "input_data": {"_id": "685508447147def4f91d1025", "validation_id": 706, "attribute_id": "E7.At43", "entity_name": "LeaveApplication", "attribute_name": "endDate", "left_operand": "endDate", "operator": "GREATER_THAN", "right_operand": "startDate", "success_value": "after_start", "warning_value": "", "failure_value": "before_or_equal_start", "multi_condition_operator": "FALSE", "warning_message": "", "success_message": "End date is valid", "error_message": "End date must be after start date", "natural_language": "LeaveApplication.endDate: endDate GREATER_THAN startDate", "version": 1, "status": "draft", "created_at": "2025-06-20T07:05:40.936694", "updated_at": "2025-06-20T07:05:40.936701", "created_by": "system", "updated_by": "system"}, "result": {"success": true, "inserted_id": 36, "schema": "workflow_runtime", "validation_id": 706, "attribute_id": "E7.At43", "original_validation_id": 4, "original_attribute_id": "E7.At4"}, "status": "success"}
{"timestamp": "2025-06-24T11:47:14.840018", "operation": "process_mongo_attribute_validations_to_workflow_runtime", "input_data": {"schema": "workflow_runtime"}, "result": {"total_processed": 8, "successful_inserts": 8, "failed_inserts": 0, "details": [{"validation_id": 636, "attribute_id": "E8.At47", "status": "success", "details": {"success": true, "inserted_id": 29, "schema": "workflow_runtime", "validation_id": 636, "attribute_id": "E8.At47", "original_validation_id": 2, "original_attribute_id": "E8.At4"}}, {"validation_id": 646, "attribute_id": "E8.At48", "status": "success", "details": {"success": true, "inserted_id": 30, "schema": "workflow_runtime", "validation_id": 646, "attribute_id": "E8.At48", "original_validation_id": 6, "original_attribute_id": "E8.At4"}}, {"validation_id": 656, "attribute_id": "E8.At49", "status": "success", "details": {"success": true, "inserted_id": 31, "schema": "workflow_runtime", "validation_id": 656, "attribute_id": "E8.At49", "original_validation_id": 5, "original_attribute_id": "E8.At1"}}, {"validation_id": 666, "attribute_id": "E8.At50", "status": "success", "details": {"success": true, "inserted_id": 32, "schema": "workflow_runtime", "validation_id": 666, "attribute_id": "E8.At50", "original_validation_id": 7, "original_attribute_id": "E8.At4"}}, {"validation_id": 676, "attribute_id": "E7.At40", "status": "success", "details": {"success": true, "inserted_id": 33, "schema": "workflow_runtime", "validation_id": 676, "attribute_id": "E7.At40", "original_validation_id": 1, "original_attribute_id": "E7.At1"}}, {"validation_id": 686, "attribute_id": "E7.At41", "status": "success", "details": {"success": true, "inserted_id": 34, "schema": "workflow_runtime", "validation_id": 686, "attribute_id": "E7.At41", "original_validation_id": 2, "original_attribute_id": "E7.At2"}}, {"validation_id": 696, "attribute_id": "E7.At42", "status": "success", "details": {"success": true, "inserted_id": 35, "schema": "workflow_runtime", "validation_id": 696, "attribute_id": "E7.At42", "original_validation_id": 3, "original_attribute_id": "E7.At3"}}, {"validation_id": 706, "attribute_id": "E7.At43", "status": "success", "details": {"success": true, "inserted_id": 36, "schema": "workflow_runtime", "validation_id": 706, "attribute_id": "E7.At43", "original_validation_id": 4, "original_attribute_id": "E7.At4"}}]}, "status": "success"}
