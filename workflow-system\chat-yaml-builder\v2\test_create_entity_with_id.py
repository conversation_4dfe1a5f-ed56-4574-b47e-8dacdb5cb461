#!/usr/bin/env python3
"""
Test script to create a new entity and check if it has the auto-increment ID column.
"""

import os
import logging
from parsers.entity_parser import parse_entities
from deployers.entity_deployer_v2 import deploy_entities

# Set up logging
logging.basicConfig(level=logging.INFO, 
                   format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger('test_create_entity_with_id')

def test_create_entity_with_id():
    """
    Test that a new entity table has the auto-increment ID column.
    """
    # Define a simple entity
    entity_def = """
TestEntity has testId^PK, name, description.
"""
    
    # Parse the entity definition
    logger.info("Parsing entity definition")
    entities_data, warnings = parse_entities(entity_def)
    
    # Print any warnings
    if warnings:
        logger.warning("Warnings during parsing:")
        for warning in warnings:
            logger.warning(f"  - {warning}")
    
    # Deploy the entity to the database
    schema_name = 'workflow_temp'
    logger.info(f"Deploying entity to {schema_name}")
    success, messages = deploy_entities(entities_data, schema_name)
    
    if not success:
        logger.error("Failed to deploy entity:")
        for message in messages:
            logger.error(f"  - {message}")
        return
    
    logger.info("Successfully deployed entity")
    for message in messages:
        logger.info(f"  - {message}")
    
    # Check if the entity table has the auto-increment ID column
    import psycopg2
    
    # Connect to the database
    conn = psycopg2.connect(
        host=os.environ.get('DB_HOST', '**********'),
        port=os.environ.get('DB_PORT', '5432'),
        dbname=os.environ.get('DB_NAME', 'workflow_system'),
        user=os.environ.get('DB_USER', 'postgres'),
        password=os.environ.get('DB_PASSWORD', 'workflow_postgres_secure_password')
    )
    
    # Set schema search path
    with conn.cursor() as cursor:
        cursor.execute(f'SET search_path TO {schema_name}')
        
        # Get the entity ID
        cursor.execute(f"""
            SELECT entity_id FROM {schema_name}.entities
            WHERE name = 'TestEntity'
        """)
        
        entity_id = cursor.fetchone()[0]
        entity_num = entity_id[1:]  # Remove 'E' prefix
        table_name = f"e{entity_num}_testentity"
        
        # Get table columns
        cursor.execute(f"""
            SELECT column_name, data_type, column_default, is_nullable
            FROM information_schema.columns
            WHERE table_schema = '{schema_name}'
            AND table_name = '{table_name}'
            ORDER BY ordinal_position
        """)
        
        columns = cursor.fetchall()
        
        print(f"Table: {table_name}")
        print("Columns:")
        for column in columns:
            column_name, data_type, column_default, is_nullable = column
            print(f"  - {column_name}, {data_type}, {column_default}, {is_nullable}")
        
        # Check if the ID column exists
        id_column = None
        for column in columns:
            column_name, data_type, column_default, is_nullable = column
            if column_name == 'id':
                id_column = column
                break
        
        if id_column:
            column_name, data_type, column_default, is_nullable = id_column
            print(f"ID column found: {column_name}, {data_type}, {column_default}, {is_nullable}")
            
            # Check if the ID column is auto-increment
            if column_default and 'nextval' in column_default:
                print("ID column is auto-increment")
            else:
                print("ID column is not auto-increment")
        else:
            print("ID column not found")
        
        # Get primary key columns
        cursor.execute(f"""
            SELECT kcu.column_name
            FROM information_schema.table_constraints tc
            JOIN information_schema.key_column_usage kcu
            ON tc.constraint_name = kcu.constraint_name
            AND tc.table_schema = kcu.table_schema
            WHERE tc.constraint_type = 'PRIMARY KEY'
            AND tc.table_schema = '{schema_name}'
            AND tc.table_name = '{table_name}'
            ORDER BY kcu.ordinal_position
        """)
        
        pk_columns = cursor.fetchall()
        
        if pk_columns:
            pk_column_names = [col[0] for col in pk_columns]
            print(f"Primary key columns: {pk_column_names}")
        else:
            print("No primary key defined")
    
    # Close connection
    conn.close()

if __name__ == "__main__":
    test_create_entity_with_id()
