#!/usr/bin/env python3
"""
Test script to verify that calculated fields are correctly deployed to the database.
"""

import os
import logging
from parsers.entity_parser import parse_entities
from deployers.entity_deployer_v2 import deploy_entities, execute_query

# Set up logging
logging.basicConfig(level=logging.INFO, 
                   format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
                   handlers=[
                       logging.FileHandler('/home/<USER>/workflow-system/chat-yaml-builder/v2/calculated_field_deployment.log'),
                       logging.StreamHandler()
                   ])
logger = logging.getLogger('test_calculated_field_deployment')

def test_calculated_field_deployment():
    """
    Test that calculated fields are correctly deployed to the database.
    """
    # Read the sample entity output file
    sample_file_path = '/home/<USER>/workflow-system/chat-yaml-builder/v2/samples/sample_entity_output2.txt'
    with open(sample_file_path, 'r') as f:
        entity_def = f.read()
    
    # Add the specific calculated field we want to test
    calculated_field = """
CalculatedField CF001 for Employee.fullName:
* Formula: CONCAT(firstName, ' ', lastName)
* Logic Layer: Application
* Caching: Session
* Dependencies: Employee.firstName, Employee.lastName
"""
    
    # Append the calculated field to the entity definition
    entity_def_with_calc_field = entity_def + calculated_field
    
    # Parse the entity definition
    entities_data, warnings = parse_entities(entity_def_with_calc_field)
    
    # Print any warnings
    if warnings:
        logger.warning("Warnings during parsing:")
        for warning in warnings:
            logger.warning(f"  - {warning}")
    
    # Check if Employee entity was parsed
    if 'entities' in entities_data and 'Employee' in entities_data['entities']:
        employee_entity = entities_data['entities']['Employee']
        
        # Check if calculated fields were parsed
        if 'calculated_fields' in employee_entity:
            logger.info("\nEmployee calculated fields:")
            for field_id, field_def in employee_entity['calculated_fields'].items():
                logger.info(f"\n  - Calculated Field: {field_id}")
                for key, value in field_def.items():
                    logger.info(f"    - {key}: {value}")
        else:
            logger.warning("No calculated fields found in Employee entity")
            return
    else:
        logger.error("Employee entity not found in parsed data")
        return
    
    # Deploy the entities to the database
    schema_name = 'workflow_temp'
    success, messages = deploy_entities(entities_data, schema_name)
    
    if not success:
        logger.error("Failed to deploy entities:")
        for message in messages:
            logger.error(f"  - {message}")
        return
    
    logger.info("Successfully deployed entities")
    
    # Verify the calculated fields were deployed
    success, messages, result = execute_query(
        f"""
        SELECT cf.field_id, cf.attribute_name, cf.formula, cf.logic_layer, cf.caching, cf.dependencies, e.name as entity_name
        FROM {schema_name}.entity_calculated_fields cf
        JOIN {schema_name}.entities e ON cf.entity_id = e.entity_id
        WHERE e.name = 'Employee'
        """,
        schema_name=schema_name
    )
    
    if success and result:
        logger.info("\nEmployee calculated fields in the database:")
        logger.info("=" * 80)
        logger.info(f"{'Field ID':<20} {'Attribute':<20} {'Formula':<30} {'Logic Layer':<15} {'Caching':<15} {'Dependencies':<30}")
        logger.info("-" * 80)
        for row in result:
            field_id = row[0]
            attribute = row[1]
            formula = row[2]
            logic_layer = row[3] if row[3] else "N/A"
            caching = row[4] if row[4] else "N/A"
            dependencies = row[5] if row[5] else "N/A"
            entity_name = row[6]
            logger.info(f"{field_id:<20} {attribute:<20} {formula:<30} {logic_layer:<15} {caching:<15} {dependencies:<30}")
        logger.info("=" * 80)
    else:
        logger.warning("No Employee calculated fields found in the database")

if __name__ == "__main__":
    test_calculated_field_deployment()
