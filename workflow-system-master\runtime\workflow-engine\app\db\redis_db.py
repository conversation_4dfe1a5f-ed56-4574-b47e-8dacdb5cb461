import redis
from app.core.config import settings
import logging

logger = logging.getLogger(__name__)

def get_redis_client():
    try:
        client = redis.Redis(
            host=settings.REDIS_HOST,
            port=settings.REDIS_PORT,
            password=settings.REDIS_PASSWORD,
            decode_responses=True
        )
        return client
    except Exception as e:
        logger.error(f"Failed to connect to Redis: {e}")
        raise
