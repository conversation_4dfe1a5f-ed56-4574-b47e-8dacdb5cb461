#!/usr/bin/env python3
"""
Add missing columns to global_objectives table.

This script adds the missing columns (status, tenant_id, deleted_mark) to the global_objectives table
in the workflow_temp schema.
"""

import os
import sys
import logging
import argparse
from typing import Dict, List, Tuple, Any, Optional

# Add parent directory to path to import common modules
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
from db_utils import execute_query

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('add_missing_columns')

def add_missing_columns(schema_name: str) -> Tuple[bool, List[str]]:
    """
    Add missing columns to global_objectives table.

    Args:
        schema_name: Schema name to add columns to

    Returns:
        Tuple containing:
            - Boolean indicating if the operation was successful
            - List of messages (warnings, errors, or success messages)
    """
    messages = []
    
    try:
        # Add missing columns to global_objectives table
        success, query_messages, _ = execute_query(
            f"""
            ALTER TABLE {schema_name}.global_objectives
            ADD COLUMN IF NOT EXISTS status VARCHAR(20) NULL DEFAULT 'active'::character varying,
            ADD COLUMN IF NOT EXISTS tenant_id VARCHAR(50) NULL DEFAULT 't001'::character varying,
            ADD COLUMN IF NOT EXISTS deleted_mark BOOLEAN NULL DEFAULT false
            """
        )
        
        if not success:
            messages.extend(query_messages)
            return False, messages
        
        messages.append(f"Added missing columns to {schema_name}.global_objectives")
        logger.info(f"Added missing columns to {schema_name}.global_objectives")
        
        # Check if columns were added successfully
        success, query_messages, result = execute_query(
            f"""
            SELECT column_name
            FROM information_schema.columns
            WHERE table_schema = %s
            AND table_name = 'global_objectives'
            AND column_name IN ('status', 'tenant_id', 'deleted_mark')
            """,
            (schema_name,)
        )
        
        if not success:
            messages.extend(query_messages)
            return False, messages
        
        if not result:
            messages.append(f"Failed to add columns to {schema_name}.global_objectives")
            logger.error(f"Failed to add columns to {schema_name}.global_objectives")
            return False, messages
        
        added_columns = [row[0] for row in result]
        messages.append(f"Successfully added columns {added_columns} to {schema_name}.global_objectives")
        logger.info(f"Successfully added columns {added_columns} to {schema_name}.global_objectives")
        
        return True, messages
    except Exception as e:
        error_msg = f"Error adding missing columns: {str(e)}"
        logger.error(error_msg, exc_info=True)
        messages.append(error_msg)
        return False, messages

def main():
    """Main function."""
    parser = argparse.ArgumentParser(description='Add missing columns to global_objectives table')
    parser.add_argument('--schema-name', default='workflow_temp', help='Schema name to add columns to')
    args = parser.parse_args()
    
    success, messages = add_missing_columns(args.schema_name)
    
    for message in messages:
        print(message)
    
    if success:
        print("Successfully added missing columns to global_objectives table")
    else:
        print("Failed to add missing columns to global_objectives table")
        sys.exit(1)

if __name__ == '__main__':
    main()
