"""
Global Objectives Service for v2 API

This module contains the business logic for global objectives read operations with RBAC checks.
"""

import logging
from typing import Optional, List, Dict, Any

from sqlalchemy.orm import Session
from sqlalchemy.sql import text

from .models import (
    GlobalObjectiveResponse, GlobalObjectiveListResponse,
    PathwayDefinitionModel, PathwayFrequencyModel,
    LocalObjectiveModel, ValidationRuleDetailModel
)

# Configure logging
logger = logging.getLogger(__name__)


class RBACService:
    """Service class for Role-Based Access Control operations"""
    
    def __init__(self, db_session: Session):
        self.db = db_session
        self.logger = logger
    
    def check_user_permissions(self, user_id: str, tenant_id: str, resource: str, action: str) -> bool:
        """
        Check if user has permission to perform action on resource.
        
        For global objectives, books, and chapters access, we check if the user can originate 
        any LO that has workflow_source = 'origin'. If they can originate such an LO, 
        then they can see the corresponding GO, book, chapter everything.
        
        Args:
            user_id: User ID
            tenant_id: Tenant ID
            resource: Resource type (e.g., 'global_objectives')
            action: Action type (e.g., 'read', 'write', 'delete')
            
        Returns:
            bool: True if user has permission, False otherwise
        """
        try:
            self.logger.info(f"Checking permissions for user {user_id}, tenant {tenant_id}, resource {resource}, action {action}")
            
            # Get user role IDs (not role names)
            role_query = """
            SELECT DISTINCT r.role_id
            FROM workflow_runtime.user_roles ur
            JOIN workflow_runtime.roles r ON ur.role = r.role_id
            WHERE ur.user_id = :user_id AND ur.tenant_id = :tenant_id
            """
            
            role_result = self.db.execute(text(role_query), {
                "user_id": user_id,
                "tenant_id": tenant_id
            }).fetchall()
            
            if not role_result:
                self.logger.warning(f"No roles found for user {user_id} in tenant {tenant_id}")
                return False
            
            user_role_ids = [row[0] for row in role_result]
            self.logger.info(f"User {user_id} has role IDs: {user_role_ids}")
            
            # For global objectives, books, and chapters - check if user can originate any origin LO
            if resource == "global_objectives" and action == "read":
                return self._check_origin_lo_permissions(user_role_ids, tenant_id)
            
            # For other resources, use the same origin LO logic
            return self._check_origin_lo_permissions(user_role_ids, tenant_id)
            
        except Exception as e:
            self.logger.error(f"Error checking user permissions: {str(e)}")
            return False
    
    def _check_origin_lo_permissions(self, user_role_ids: List[str], tenant_id: str) -> bool:
        """
        Check if user can originate any LO that has workflow_source = 'origin'.
        This determines if they can see global objectives, books, and chapters.
        """
        try:
            # Get all LOs with workflow_source = 'origin' from all Global Objectives in this tenant
            origin_los_query = """
            SELECT DISTINCT lo.lo_id
            FROM workflow_runtime.local_objectives lo
            JOIN workflow_runtime.global_objectives go ON lo.go_id = go.go_id
            WHERE go.tenant_id = :tenant_id 
            AND lo.workflow_source = 'origin'
            AND go.deleted_mark = false
            """
            
            origin_los_result = self.db.execute(text(origin_los_query), {
                "tenant_id": tenant_id.lower()
            }).fetchall()
            
            if not origin_los_result:
                self.logger.warning(f"No origin LOs found for tenant {tenant_id}")
                return False
            
            origin_lo_ids = [row[0] for row in origin_los_result]
            self.logger.info(f"Found origin LOs: {origin_lo_ids}")
            
            # Check if user has permission to any of these origin LOs
            for lo_id in origin_lo_ids:
                permission_query = """
                SELECT rsp.granted_actions
                FROM workflow_runtime.role_system_permissions rsp
                JOIN workflow_runtime.system_permissions sp ON rsp.permission_id = sp.permission_id
                WHERE rsp.role_id = ANY(:role_ids) 
                AND sp.resource_identifier = :lo_id
                """
                
                permission_result = self.db.execute(text(permission_query), {
                    "role_ids": user_role_ids,
                    "lo_id": lo_id
                }).fetchall()
                
                # Check if any role has create/originate permission for this origin LO
                for row in permission_result:
                    granted_actions = row[0]  # This is a JSON array
                    if isinstance(granted_actions, list):
                        if any(action in granted_actions for action in ["create", "write"]):
                            self.logger.info(f"User can originate origin LO {lo_id}, granting global objectives access")
                            return True
            
            self.logger.warning(f"User cannot originate any origin LO, denying global objectives access")
            return False
            
        except Exception as e:
            self.logger.error(f"Error checking origin LO permissions: {str(e)}")
            return False
    
    def check_tenant_access(self, user_id: str, tenant_id: str) -> bool:
        """
        Check if user has access to the specified tenant.
        
        Args:
            user_id: User ID
            tenant_id: Tenant ID
            
        Returns:
            bool: True if user has access, False otherwise
        """
        try:
            query = """
            SELECT COUNT(*)
            FROM workflow_runtime.user_roles ur
            WHERE ur.user_id = :user_id AND ur.tenant_id = :tenant_id
            """
            
            result = self.db.execute(text(query), {
                "user_id": user_id,
                "tenant_id": tenant_id
            }).fetchone()
            
            return result[0] > 0 if result else False
            
        except Exception as e:
            self.logger.error(f"Error checking tenant access: {str(e)}")
            return False


class GlobalObjectivesService:
    """Service class for global objectives read operations"""
    
    def __init__(self, db_session: Session):
        self.db = db_session
        self.logger = logger
        self.rbac = RBACService(db_session)
    
    def _get_pathway_definitions(self, go_id: str) -> List[PathwayDefinitionModel]:
        """Get pathway definitions for a global objective"""
        try:
            query = """
            SELECT id, pathway_definitions_id, go_id, pathway_number, pathway_name, 
                   steps, created_at, created_by, updated_at, updated_by
            FROM workflow_runtime.pathway_definitions
            WHERE go_id = :go_id
            ORDER BY pathway_number
            """
            
            result = self.db.execute(text(query), {"go_id": go_id}).fetchall()
            
            return [
                PathwayDefinitionModel(
                    id=row.id,
                    pathway_definitions_id=row.pathway_definitions_id,
                    go_id=row.go_id,
                    pathway_number=row.pathway_number,
                    pathway_name=row.pathway_name,
                    steps=row.steps,
                    created_at=row.created_at,
                    created_by=row.created_by,
                    updated_at=row.updated_at,
                    updated_by=row.updated_by
                )
                for row in result
            ]
            
        except Exception as e:
            self.logger.error(f"Error getting pathway definitions for {go_id}: {str(e)}")
            return []
    
    def _get_pathway_frequency(self, go_id: str) -> List[PathwayFrequencyModel]:
        """Get pathway frequency data for a global objective"""
        try:
            query = """
            SELECT id, performance_discovery_id, go_id, pathway_name, frequency, 
                   percentage, average_duration, success_rate, created_at, created_by, 
                   updated_at, updated_by
            FROM workflow_runtime.pathway_frequency
            WHERE go_id = :go_id
            ORDER BY frequency DESC
            """
            
            result = self.db.execute(text(query), {"go_id": go_id}).fetchall()
            
            return [
                PathwayFrequencyModel(
                    id=row.id,
                    performance_discovery_id=row.performance_discovery_id,
                    go_id=row.go_id,
                    pathway_name=row.pathway_name,
                    frequency=row.frequency,
                    percentage=row.percentage,
                    average_duration=row.average_duration,
                    success_rate=row.success_rate,
                    created_at=row.created_at,
                    created_by=row.created_by,
                    updated_at=row.updated_at,
                    updated_by=row.updated_by
                )
                for row in result
            ]
            
        except Exception as e:
            self.logger.error(f"Error getting pathway frequency for {go_id}: {str(e)}")
            return []
    
    def _get_local_objectives(self, go_id: str) -> List[LocalObjectiveModel]:
        """Get local objectives for a global objective"""
        try:
            query = """
            SELECT id, lo_id, go_id, lo_number, lo_name, actor_type, 
                   created_at, created_by, updated_at, updated_by
            FROM workflow_runtime.local_objectives_list
            WHERE go_id = :go_id
            ORDER BY lo_number
            """
            
            result = self.db.execute(text(query), {"go_id": go_id}).fetchall()
            
            return [
                LocalObjectiveModel(
                    id=row.id,
                    lo_id=row.lo_id,
                    go_id=row.go_id,
                    lo_number=row.lo_number,
                    lo_name=row.lo_name,
                    actor_type=row.actor_type,
                    created_at=row.created_at,
                    created_by=row.created_by,
                    updated_at=row.updated_at,
                    updated_by=row.updated_by
                )
                for row in result
            ]
            
        except Exception as e:
            self.logger.error(f"Error getting local objectives for {go_id}: {str(e)}")
            return []
    
    def _get_validation_rule_details(self, go_id: str) -> List[ValidationRuleDetailModel]:
        """Get validation rule details for a global objective"""
        try:
            query = """
            SELECT id, rule_id, go_validation_rule_id, go_id, rule_name, rule_inputs, 
                   rule_operation, rule_description, rule_output, rule_error, 
                   rule_validation, created_at, created_by, updated_at, updated_by
            FROM workflow_runtime.go_validation_rule_details
            WHERE go_id = :go_id
            ORDER BY rule_name
            """
            
            result = self.db.execute(text(query), {"go_id": go_id}).fetchall()
            
            return [
                ValidationRuleDetailModel(
                    id=row.id,
                    rule_id=row.rule_id,
                    go_validation_rule_id=row.go_validation_rule_id,
                    go_id=row.go_id,
                    rule_name=row.rule_name,
                    rule_inputs=row.rule_inputs,
                    rule_operation=row.rule_operation,
                    rule_description=row.rule_description,
                    rule_output=row.rule_output,
                    rule_error=row.rule_error,
                    rule_validation=row.rule_validation,
                    created_at=row.created_at,
                    created_by=row.created_by,
                    updated_at=row.updated_at,
                    updated_by=row.updated_by
                )
                for row in result
            ]
            
        except Exception as e:
            self.logger.error(f"Error getting validation rule details for {go_id}: {str(e)}")
            return []
    
    def _build_global_objective_response(self, row) -> GlobalObjectiveResponse:
        """Build a complete global objective response with related data"""
        go_response = GlobalObjectiveResponse(
            go_id=row.go_id,
            name=row.name,
            version=row.version,
            status=row.status,
            description=row.description,
            created_at=row.created_at,
            updated_at=row.updated_at,
            tenant_id=row.tenant_id,
            last_used=row.last_used,
            deleted_mark=row.deleted_mark,
            version_type=row.version_type,
            metadata=row.metadata,
            auto_id=row.auto_id,
            primary_entity=row.primary_entity,
            classification=row.classification,
            tenant_name=row.tenant_name,
            book_id=row.book_id,
            book_name=row.book_name,
            chapter_id=row.chapter_id,
            chapter_name=row.chapter_name,
            created_by=row.created_by,
            updated_by=row.updated_by
        )
        
        # Add related data
        go_response.pathway_definitions = self._get_pathway_definitions(row.go_id)
        go_response.pathway_frequency = self._get_pathway_frequency(row.go_id)
        go_response.local_objectives = self._get_local_objectives(row.go_id)
        go_response.validation_rule_details = self._get_validation_rule_details(row.go_id)
        
        return go_response
    
    def get_all_global_objectives(
        self, 
        user_id: str, 
        tenant_id: str,
        limit: Optional[int] = None,
        offset: Optional[int] = None
    ) -> Optional[GlobalObjectiveListResponse]:
        """
        Get all global objectives with RBAC checks.
        
        Args:
            user_id: User ID
            tenant_id: Tenant ID
            limit: Optional limit for pagination
            offset: Optional offset for pagination
            
        Returns:
            Optional[GlobalObjectiveListResponse]: List of global objectives if successful
        """
        try:
            # Check RBAC permissions
            if not self.rbac.check_user_permissions(user_id, tenant_id, "global_objectives", "read"):
                self.logger.warning(f"User {user_id} denied read access to global_objectives")
                return None
            
            if not self.rbac.check_tenant_access(user_id, tenant_id):
                self.logger.warning(f"User {user_id} denied access to tenant {tenant_id}")
                return None
            
            # Build query
            base_query = """
            SELECT go_id, name, version, status, description, created_at, updated_at, 
                   tenant_id, last_used, deleted_mark, version_type, metadata, auto_id,
                   primary_entity, classification, tenant_name, book_id, book_name, 
                   chapter_id, chapter_name, created_by, updated_by
            FROM workflow_runtime.global_objectives
            WHERE tenant_id = :tenant_id AND deleted_mark = false
            ORDER BY go_id
            """
            
            count_query = """
            SELECT COUNT(*)
            FROM workflow_runtime.global_objectives
            WHERE tenant_id = :tenant_id AND deleted_mark = false
            """
            
            params = {"tenant_id": tenant_id.lower()}
            
            # Get total count
            total_count = self.db.execute(text(count_query), params).fetchone()[0]
            
            # Add pagination
            if limit:
                base_query += " LIMIT :limit"
                params["limit"] = limit
            
            if offset:
                base_query += " OFFSET :offset"
                params["offset"] = offset
            
            # Execute query
            result = self.db.execute(text(base_query), params).fetchall()
            
            # Build response objects
            objectives = []
            for row in result:
                go_response = self._build_global_objective_response(row)
                objectives.append(go_response)
            
            return GlobalObjectiveListResponse(
                objectives=objectives,
                total_count=total_count,
                filtered_count=len(objectives)
            )
            
        except Exception as e:
            self.logger.error(f"Error getting all global objectives: {str(e)}")
            return None
    
    def get_global_objective_by_id(self, user_id: str, tenant_id: str, go_id: str) -> Optional[GlobalObjectiveResponse]:
        """
        Get a specific global objective by ID with RBAC checks.
        
        Args:
            user_id: User ID
            tenant_id: Tenant ID
            go_id: Global objective ID
            
        Returns:
            Optional[GlobalObjectiveResponse]: Global objective if found and accessible
        """
        try:
            # Check RBAC permissions
            if not self.rbac.check_user_permissions(user_id, tenant_id, "global_objectives", "read"):
                self.logger.warning(f"User {user_id} denied read access to global_objectives")
                return None
            
            if not self.rbac.check_tenant_access(user_id, tenant_id):
                self.logger.warning(f"User {user_id} denied access to tenant {tenant_id}")
                return None
            
            query = """
            SELECT go_id, name, version, status, description, created_at, updated_at, 
                   tenant_id, last_used, deleted_mark, version_type, metadata, auto_id,
                   primary_entity, classification, tenant_name, book_id, book_name, 
                   chapter_id, chapter_name, created_by, updated_by
            FROM workflow_runtime.global_objectives
            WHERE go_id = :go_id AND tenant_id = :tenant_id AND deleted_mark = false
            """
            
            result = self.db.execute(text(query), {
                "go_id": go_id,
                "tenant_id": tenant_id.lower()
            }).fetchone()
            
            if not result:
                return None
            
            return self._build_global_objective_response(result)
            
        except Exception as e:
            self.logger.error(f"Error getting global objective {go_id}: {str(e)}")
            return None
    
    def get_all_books(self, user_id: str, tenant_id: str) -> Optional[List[Dict[str, Any]]]:
        """
        Get all unique books with RBAC checks.
        
        Args:
            user_id: User ID
            tenant_id: Tenant ID
            
        Returns:
            Optional[List[Dict[str, Any]]]: List of books if successful
        """
        try:
            # Check RBAC permissions
            if not self.rbac.check_user_permissions(user_id, tenant_id, "global_objectives", "read"):
                self.logger.warning(f"User {user_id} denied read access to global_objectives")
                return None
            
            if not self.rbac.check_tenant_access(user_id, tenant_id):
                self.logger.warning(f"User {user_id} denied access to tenant {tenant_id}")
                return None
            
            query = """
            SELECT DISTINCT book_id, book_name, COUNT(*) as objective_count
            FROM workflow_runtime.global_objectives
            WHERE tenant_id = :tenant_id AND deleted_mark = false 
              AND book_id IS NOT NULL AND book_name IS NOT NULL
            GROUP BY book_id, book_name
            ORDER BY book_name
            """
            
            result = self.db.execute(text(query), {"tenant_id": tenant_id.lower()}).fetchall()
            
            return [
                {
                    "book_id": row.book_id,
                    "book_name": row.book_name,
                    "objective_count": row.objective_count
                }
                for row in result
            ]
            
        except Exception as e:
            self.logger.error(f"Error getting all books: {str(e)}")
            return None
    
    def get_chapters_by_book(self, user_id: str, tenant_id: str, book_id: str) -> Optional[List[Dict[str, Any]]]:
        """
        Get all chapters for a specific book with RBAC checks.
        
        Args:
            user_id: User ID
            tenant_id: Tenant ID
            book_id: Book ID
            
        Returns:
            Optional[List[Dict[str, Any]]]: List of chapters if successful
        """
        try:
            # Check RBAC permissions
            if not self.rbac.check_user_permissions(user_id, tenant_id, "global_objectives", "read"):
                self.logger.warning(f"User {user_id} denied read access to global_objectives")
                return None
            
            if not self.rbac.check_tenant_access(user_id, tenant_id):
                self.logger.warning(f"User {user_id} denied access to tenant {tenant_id}")
                return None
            
            query = """
            SELECT DISTINCT chapter_id, chapter_name, book_id, book_name, COUNT(*) as objective_count
            FROM workflow_runtime.global_objectives
            WHERE tenant_id = :tenant_id AND deleted_mark = false 
              AND book_id = :book_id
              AND chapter_id IS NOT NULL AND chapter_name IS NOT NULL
            GROUP BY chapter_id, chapter_name, book_id, book_name
            ORDER BY chapter_name
            """
            
            result = self.db.execute(text(query), {
                "tenant_id": tenant_id.lower(),
                "book_id": book_id
            }).fetchall()
            
            return [
                {
                    "chapter_id": row.chapter_id,
                    "chapter_name": row.chapter_name,
                    "book_id": row.book_id,
                    "book_name": row.book_name,
                    "objective_count": row.objective_count
                }
                for row in result
            ]
            
        except Exception as e:
            self.logger.error(f"Error getting chapters for book {book_id}: {str(e)}")
            return None
    
    def get_objectives_by_book(
        self, 
        user_id: str, 
        tenant_id: str, 
        book_id: str,
        limit: Optional[int] = None,
        offset: Optional[int] = None
    ) -> Optional[GlobalObjectiveListResponse]:
        """
        Get all global objectives for a specific book with RBAC checks.
        
        Args:
            user_id: User ID
            tenant_id: Tenant ID
            book_id: Book ID
            limit: Optional limit for pagination
            offset: Optional offset for pagination
            
        Returns:
            Optional[GlobalObjectiveListResponse]: List of global objectives if successful
        """
        try:
            # Check RBAC permissions
            if not self.rbac.check_user_permissions(user_id, tenant_id, "global_objectives", "read"):
                self.logger.warning(f"User {user_id} denied read access to global_objectives")
                return None
            
            if not self.rbac.check_tenant_access(user_id, tenant_id):
                self.logger.warning(f"User {user_id} denied access to tenant {tenant_id}")
                return None
            
            # Build query
            base_query = """
            SELECT go_id, name, version, status, description, created_at, updated_at, 
                   tenant_id, last_used, deleted_mark, version_type, metadata, auto_id,
                   primary_entity, classification, tenant_name, book_id, book_name, 
                   chapter_id, chapter_name, created_by, updated_by
            FROM workflow_runtime.global_objectives
            WHERE tenant_id = :tenant_id AND deleted_mark = false AND book_id = :book_id
            ORDER BY go_id
            """
            
            count_query = """
            SELECT COUNT(*)
            FROM workflow_runtime.global_objectives
            WHERE tenant_id = :tenant_id AND deleted_mark = false AND book_id = :book_id
            """
            
            params = {"tenant_id": tenant_id.lower(), "book_id": book_id}
            
            # Get total count
            total_count = self.db.execute(text(count_query), params).fetchone()[0]
            
            # Add pagination
            if limit:
                base_query += " LIMIT :limit"
                params["limit"] = limit
            
            if offset:
                base_query += " OFFSET :offset"
                params["offset"] = offset
            
            # Execute query
            result = self.db.execute(text(base_query), params).fetchall()
            
            # Build response objects
            objectives = []
            for row in result:
                go_response = self._build_global_objective_response(row)
                objectives.append(go_response)
            
            return GlobalObjectiveListResponse(
                objectives=objectives,
                total_count=total_count,
                filtered_count=len(objectives)
            )
            
        except Exception as e:
            self.logger.error(f"Error getting objectives for book {book_id}: {str(e)}")
            return None
    
    def get_objectives_by_chapter(
        self, 
        user_id: str, 
        tenant_id: str, 
        chapter_id: str,
        limit: Optional[int] = None,
        offset: Optional[int] = None
    ) -> Optional[GlobalObjectiveListResponse]:
        """
        Get all global objectives for a specific chapter with RBAC checks.
        
        Args:
            user_id: User ID
            tenant_id: Tenant ID
            chapter_id: Chapter ID
            limit: Optional limit for pagination
            offset: Optional offset for pagination
            
        Returns:
            Optional[GlobalObjectiveListResponse]: List of global objectives if successful
        """
        try:
            # Check RBAC permissions
            if not self.rbac.check_user_permissions(user_id, tenant_id, "global_objectives", "read"):
                self.logger.warning(f"User {user_id} denied read access to global_objectives")
                return None
            
            if not self.rbac.check_tenant_access(user_id, tenant_id):
                self.logger.warning(f"User {user_id} denied access to tenant {tenant_id}")
                return None
            
            # Build query
            base_query = """
            SELECT go_id, name, version, status, description, created_at, updated_at, 
                   tenant_id, last_used, deleted_mark, version_type, metadata, auto_id,
                   primary_entity, classification, tenant_name, book_id, book_name, 
                   chapter_id, chapter_name, created_by, updated_by
            FROM workflow_runtime.global_objectives
            WHERE tenant_id = :tenant_id AND deleted_mark = false AND chapter_id = :chapter_id
            ORDER BY go_id
            """
            
            count_query = """
            SELECT COUNT(*)
            FROM workflow_runtime.global_objectives
            WHERE tenant_id = :tenant_id AND deleted_mark = false AND chapter_id = :chapter_id
            """
            
            params = {"tenant_id": tenant_id.lower(), "chapter_id": chapter_id}
            
            # Get total count
            total_count = self.db.execute(text(count_query), params).fetchone()[0]
            
            # Add pagination
            if limit:
                base_query += " LIMIT :limit"
                params["limit"] = limit
            
            if offset:
                base_query += " OFFSET :offset"
                params["offset"] = offset
            
            # Execute query
            result = self.db.execute(text(base_query), params).fetchall()
            
            # Build response objects
            objectives = []
            for row in result:
                go_response = self._build_global_objective_response(row)
                objectives.append(go_response)
            
            return GlobalObjectiveListResponse(
                objectives=objectives,
                total_count=total_count,
                filtered_count=len(objectives)
            )
            
        except Exception as e:
            self.logger.error(f"Error getting objectives for chapter {chapter_id}: {str(e)}")
            return None
