def update_entity_table_names(schema_name: str) -> <PERSON>ple[bool, List[str]]:
    """
    Update entity table names from old format (e000001_department, e000002_employee, etc.)
    to new format (e1_department, e2_employee, etc.).

    Args:
        schema_name: Schema name

    Returns:
        Tuple containing:
            - <PERSON><PERSON><PERSON> indicating if update was successful
            - List of messages (warnings, errors, or success messages)
    """
    messages = []
    conn = None

    try:
        logger.info(f'Updating entity table names in schema {schema_name}')

        # Get database connection
        conn = get_db_connection(schema_name)

        with conn.cursor() as cursor:
            # Find all tables in the schema that match the old format pattern
            cursor.execute(f'''
                SELECT table_name
                FROM information_schema.tables
                WHERE table_schema = %s
                AND table_name ~ '^e[0-9]{6}_.*$'
            ''', (schema_name,))

            tables = cursor.fetchall()
            logger.info(f'Found {len(tables)} tables matching the old format pattern')

            # For each table, rename it to the new format
            for table in tables:
                old_table_name = table[0]
                
                # Extract the entity ID and table name
                match = re.match(r'^e([0-9]{6})_(.*)$', old_table_name)
                if match:
                    entity_num = int(match.group(1))
                    table_suffix = match.group(2)
                    
                    # Create the new table name
                    new_table_name = f'e{entity_num}_{table_suffix}'
                    
                    logger.info(f'Renaming table {old_table_name} to {new_table_name}')
                    
                    # Rename the table
                    cursor.execute(f'ALTER TABLE {schema_name}.{old_table_name} RENAME TO {new_table_name}')
                    
                    messages.append(f'Renamed table {old_table_name} to {new_table_name}')
            
            # Commit the changes
            conn.commit()
            
            logger.info('Successfully updated entity table names')
            messages.append('Successfully updated entity table names')
            
            return True, messages
    except Exception as e:
        logger.error(f'Error updating entity table names: {str(e)}')
        messages.append(f'Error updating entity table names: {str(e)}')
        
        if conn:
            conn.rollback()
        
        return False, messages
    finally:
        if conn:
            conn.close()
