tenant:
  id: "t001"
  name: "UserManagement001"
  roles:
    - id: "r001"
      name: "Administrator"
      inherits_from: null
      access:
        entities:
          - entity_id: "e001"  # User entity
            permissions: ["Read", "Create", "Update", "Delete"]
          - entity_id: "e002"  # Role entity
            permissions: ["Read", "Create", "Update", "Delete"]
        objectives:
          - objective_id: "go001.lo001"  # Create User
            permissions: ["Execute"]
          - objective_id: "go001.lo002"  # Update User Organization
            permissions: ["Execute"]
          - objective_id: "go001.lo003"  # Update User Password
            permissions: ["Execute"]
    - id: "r002"
      name: "UserManager"
      inherits_from: null
      access:
        entities:
          - entity_id: "e001"  # User entity
            permissions: ["Read", "Create", "Update"]
          - entity_id: "e002"  # Role entity
            permissions: ["Read"]
        objectives:
          - objective_id: "go001.lo001"  # Create User
            permissions: ["Execute"]
          - objective_id: "go001.lo002"  # Update User Organization
            permissions: ["Execute"]

workflow_data:
  software_type: "User Management"
  industry: "System Administration"
  version: "1.0"
  created_by: "system"
  created_on: "{{timestamp}}"

permission_types:
  - id: "read"
    description: "Can read entity data"
    capabilities: ["GET"]
  - id: "create"
    description: "Can create new entity records"
    capabilities: ["POST"]
  - id: "update"
    description: "Can update existing records"
    capabilities: ["PUT"]
  - id: "delete"
    description: "Can delete entity records"
    capabilities: ["DELETE"]
  - id: "execute"
    description: "Can execute workflows"
    capabilities: ["EXECUTE"]

entities:
  - id: "e001"
    name: "User"
    type: "Master"
    version: "1.0"
    status: "Active"
    attributes_metadata:
      attribute_prefix: "at"
      attribute_map:
        "at101": "user_id"
        "at102": "username"
        "at103": "email"
        "at104": "first_name"
        "at105": "last_name"
        "at106": "status"
        "at107": "password_hash"
        "at108": "disabled"
        "at109": "organization"
        "at110": "team"
      required_attributes:
        - "at101"
        - "at102"
        - "at103"
        - "at106"
    attributes:
      - id: "at101"
        name: "user_id"
        display_name: "User ID"
        datatype: "String"
        required: true
        version: "1.0"
        status: "Deployed"
      - id: "at102"
        name: "username"
        display_name: "Username"
        datatype: "String"
        required: true
        version: "1.0"
        status: "Deployed"
      - id: "at103"
        name: "email"
        display_name: "Email"
        datatype: "String"
        required: true
        version: "1.0"
        status: "Deployed"
      - id: "at104"
        name: "first_name"
        display_name: "First Name"
        datatype: "String"
        required: false
        version: "1.0"
        status: "Deployed"
      - id: "at105"
        name: "last_name"
        display_name: "Last Name"
        datatype: "String"
        required: false
        version: "1.0"
        status: "Deployed"
      - id: "at106"
        name: "status"
        display_name: "Status"
        datatype: "Enum"
        required: true
        values: ["active", "inactive", "suspended"]
        version: "1.0"
        status: "Deployed"
      - id: "at107"
        name: "password_hash"
        display_name: "Password Hash"
        datatype: "String"
        required: false
        version: "1.0"
        status: "Deployed"
      - id: "at108"
        name: "disabled"
        display_name: "Disabled"
        datatype: "Boolean"
        required: false
        version: "1.0"
        status: "Deployed"
      - id: "at109"
        name: "organization"
        display_name: "Organization"
        datatype: "String"
        required: false
        version: "1.0"
        status: "Deployed"
      - id: "at110"
        name: "team"
        display_name: "Team"
        datatype: "String"
        required: false
        version: "1.0"
        status: "Deployed"

  - id: "e002"
    name: "Role"
    type: "Master"
    version: "1.0"
    status: "Active"
    attributes_metadata:
      attribute_prefix: "at"
      attribute_map:
        "at201": "role_id"
        "at202": "name"
        "at203": "description"
        "at204": "inherits_from"
        "at205": "tenant_id"
      required_attributes:
        - "at201"
        - "at202"
    attributes:
      - id: "at201"
        name: "role_id"
        display_name: "Role ID"
        datatype: "String"
        required: true
        version: "1.0"
        status: "Deployed"
      - id: "at202"
        name: "name"
        display_name: "Name"
        datatype: "String"
        required: true
        version: "1.0"
        status: "Deployed"
      - id: "at203"
        name: "description"
        display_name: "Description"
        datatype: "String"
        required: false
        version: "1.0"
        status: "Deployed"
      - id: "at204"
        name: "inherits_from"
        display_name: "Inherits From"
        datatype: "String"
        required: false
        version: "1.0"
        status: "Deployed"
      - id: "at205"
        name: "tenant_id"
        display_name: "Tenant ID"
        datatype: "String"
        required: false
        version: "1.0"
        status: "Deployed"

global_objectives:
  - id: "go001"
    name: "User Management Workflow"
    version: "1.0"
    status: "Active"
    input_stack:
      description: "Global inputs"
      inputs: []
    output_stack:
      description: "Global outputs"
      outputs: []
    data_mapping_stack:
      description: "Data handover between GOs"
      mappings: []

local_objectives:
  - id: "lo001"
    contextual_id: "go001.lo001"
    name: "Create User"
    workflow_source: "origin"
    function_type: "Create"
    agent_stack:
      agents:
        - role: "r001"  # Administrator role
          rights: ["Execute", "Read"]
          users: []  # Empty means all users with this role have these rights
        - role: "r002"  # UserManager role
          rights: ["Read", "Execute"]
          users: ["<EMAIL>"]  # Only this specific user manager can execute
    input_stack:
      description: "Capture user details"
      inputs:
        - id: "in001"
          slot_id: "e001.at102.in001"
          contextual_id: "go001.lo001.in001"
          source:
            type: "user"
            description: "Username"
          required: true
          data_type: "string"
          ui_control: "oj-input-text"
          metadata:
            usage: ""
          validations:
            - rule: "Username is required"
              rule_type: "validate_required"
              validation_method: "validate_required"
              error_message: "Username cannot be empty"
            - rule: "Username must be unique"
              rule_type: "validate_unique"
              validation_method: "validate_unique"
              error_message: "Username already exists"
              
        - id: "in002"
          slot_id: "e001.at103.in002"
          contextual_id: "go001.lo001.in002"
          source:
            type: "user"
            description: "Email"
          required: true
          data_type: "string"
          ui_control: "oj-input-text"
          metadata:
            usage: ""
          validations:
            - rule: "Email is required"
              rule_type: "validate_required"
              validation_method: "validate_required"
              error_message: "Email cannot be empty"
            - rule: "Email must be valid"
              rule_type: "validate_email"
              validation_method: "validate_email"
              error_message: "Invalid email format"
              
        - id: "in003"
          slot_id: "e001.at104.in003"
          contextual_id: "go001.lo001.in003"
          source:
            type: "user"
            description: "First Name"
          required: false
          data_type: "string"
          ui_control: "oj-input-text"
          metadata:
            usage: ""
          validations: []
          
        - id: "in004"
          slot_id: "e001.at105.in004"
          contextual_id: "go001.lo001.in004"
          source:
            type: "user"
            description: "Last Name"
          required: false
          data_type: "string"
          ui_control: "oj-input-text"
          metadata:
            usage: ""
          validations: []
          
        - id: "in005"
          slot_id: "e001.at106.in005"
          contextual_id: "go001.lo001.in005"
          source:
            type: "user"
            description: "Status"
          required: true
          data_type: "enum"
          allowed_values: ["active", "inactive", "suspended"]
          ui_control: "oj-select-single"
          metadata:
            usage: ""
          validations:
            - rule: "Status must be valid"
              rule_type: "enum_check"
              validation_method: "enum_check"
              allowed_values: ["active", "inactive", "suspended"]
              error_message: "Invalid status value"
          nested_function:
            id: "nf001"
            function_name: "to_lowercase"
            function_type: "transform"
            parameters: {}
            output_to: "at106"
            
        - id: "in006"
          slot_id: "e001.at107.in006"
          contextual_id: "go001.lo001.in006"
          source:
            type: "user"
            description: "Password"
          required: true
          data_type: "string"
          ui_control: "oj-input-password"
          metadata:
            usage: ""
          validations:
            - rule: "Password is required"
              rule_type: "validate_required"
              validation_method: "validate_required"
              error_message: "Password cannot be empty"
            - rule: "Password must be strong"
              rule_type: "validate_password_strength"
              validation_method: "validate_password_strength"
              error_message: "Password must be at least 8 characters with letters and numbers"
          nested_function:
            id: "nf002"
            function_name: "hash_password"
            function_type: "security"
            parameters: {}
            output_to: "at107"
            
        - id: "in007"
          slot_id: "e001.at109.in007"
          contextual_id: "go001.lo001.in007"
          source:
            type: "user"
            description: "Organization"
          required: false
          data_type: "string"
          ui_control: "oj-input-text"
          metadata:
            usage: ""
          validations: []
          
        - id: "in008"
          slot_id: "e001.at110.in008"
          contextual_id: "go001.lo001.in008"
          source:
            type: "user"
            description: "Team"
          required: false
          data_type: "string"
          ui_control: "oj-input-text"
          metadata:
            usage: ""
          validations: []
          
    output_stack:
      description: "User created"
      outputs:
        - id: "out001"
          slot_id: "executionstatus.out001"
          contextual_id: "go001.lo001.out001"
          source:
            type: "system"
            description: "Success or failure"
          data_type: "string"
          
        - id: "out002"
          slot_id: "e001.at101.out002"
          contextual_id: "go001.lo001.out002"
          source:
            type: "system"
            description: "User ID"
          data_type: "string"
          
    data_mapping_stack:
      description: "Data handover between LOs"
      mappings:
        - id: "map001"
          source: "lo001.out002"
          target: "lo002.in001"
          mapping_type: "direct"
          
    execution_pathway:
      type: "alternate"
      conditions:
        - condition:
            condition_type: "attribute_comparison"
            entity: "e001"
            attribute: "at109"
            operator: "not_empty"
            value: true
          next_lo: "lo002"
        - condition:
            condition_type: "attribute_comparison"
            entity: "e001"
            attribute: "at109"
            operator: "empty"
            value: true
          next_lo: "lo003"

  - id: "lo002"
    contextual_id: "go001.lo002"
    name: "Update User Organization"
    workflow_source: "intermediate"
    function_type: "Update"
    agent_stack:
      agents:
        - role: "r001"  # Administrator role
          rights: ["Execute", "Read"]
          users: []  # Empty means all users with this role have these rights
        - role: "r002"  # UserManager role
          rights: ["Read"]
          users: []  # All user managers can read
        - role: "r002"  # UserManager role with specific users
          rights: ["Execute"]
          users: ["<EMAIL>", "<EMAIL>"]  # Only these specific users can execute
    input_stack:
      description: "Update user organization"
      inputs:
        - id: "in001"
          slot_id: "e001.at101.in001"
          contextual_id: "go001.lo002.in001"
          source:
            type: "system"
            description: "User ID"
          required: true
          data_type: "string"
          ui_control: "oj-input-text"
          metadata:
            usage: "lookup"
          validations:
            - rule: "User ID must be present"
              rule_type: "validate_required"
              validation_method: "validate_required"
              error_message: "User ID is required"
            - rule: "User ID must exist"
              rule_type: "entity_exists"
              validation_method: "entity_exists"
              entity: "e001"
              attribute: "at101"
              error_message: "User ID not found in the system"
              
        - id: "in002"
          slot_id: "e001.at109.in002"
          contextual_id: "go001.lo002.in002"
          source:
            type: "user"
            description: "Organization"
          required: true
          data_type: "string"
          ui_control: "oj-input-text"
          metadata:
            usage: "update"
          validations:
            - rule: "Organization is required"
              rule_type: "validate_required"
              validation_method: "validate_required"
              error_message: "Organization cannot be empty"
              
        - id: "in003"
          slot_id: "e001.at110.in003"
          contextual_id: "go001.lo002.in003"
          source:
            type: "user"
            description: "Team"
          required: false
          data_type: "string"
          ui_control: "oj-input-text"
          metadata:
            usage: "update"
          validations: []
          
    output_stack:
      description: "User organization updated"
      outputs:
        - id: "out001"
          slot_id: "executionstatus.out001"
          contextual_id: "go001.lo002.out001"
          source:
            type: "system"
            description: "Success or failure"
          data_type: "string"
          
        - id: "out002"
          slot_id: "e001.at101.out002"
          contextual_id: "go001.lo002.out002"
          source:
            type: "system"
            description: "User ID"
          data_type: "string"
          
    data_mapping_stack:
      description: "Data handover between LOs"
      mappings:
        - id: "map001"
          source: "lo002.out002"
          target: "lo003.in001"
          mapping_type: "direct"
          
    execution_pathway:
      type: "alternate"
      conditions:
        - condition:
            condition_type: "attribute_comparison"
            entity: "e001"
            attribute: "at106"
            operator: "equals"
            value: "active"
          next_lo: "lo003"
        - condition:
            condition_type: "attribute_comparison"
            entity: "e001"
            attribute: "at106"
            operator: "not_equals"
            value: "active"
          next_lo: "lo003"  # Changed from empty string to lo003

  - id: "lo003"
    contextual_id: "go001.lo003"
    name: "Update User Password"
    workflow_source: "intermediate"
    function_type: "Update"
    agent_stack:
      agents:
        - role: "r001"  # Administrator role
          rights: ["Execute", "Read"]
          users: []  # All administrators can execute and read
        # Specific users from any role can also be given access
        - role: ""  # No role specified, just specific users
          rights: ["Execute"]
          users: ["<EMAIL>"]  # This specific user can execute regardless of role
    input_stack:
      description: "Update user password"
      inputs:
        - id: "in001"
          slot_id: "e001.at101.in001"
          contextual_id: "go001.lo003.in001"
          source:
            type: "system"
            description: "User ID"
          required: true
          data_type: "string"
          ui_control: "oj-input-text"
          metadata:
            usage: "lookup"
          validations:
            - rule: "User ID must be present"
              rule_type: "validate_required"
              validation_method: "validate_required"
              error_message: "User ID is required"
            - rule: "User ID must exist"
              rule_type: "entity_exists"
              validation_method: "entity_exists"
              entity: "e001"
              attribute: "at101"
              error_message: "User ID not found in the system"
              
        - id: "in002"
          slot_id: "e001.at107.in002"
          contextual_id: "go001.lo003.in002"
          source:
            type: "user"
            description: "New Password"
          required: true
          data_type: "string"
          ui_control: "oj-input-password"
          metadata:
            usage: "update"
          validations:
            - rule: "Password is required"
              rule_type: "validate_required"
              validation_method: "validate_required"
              error_message: "Password cannot be empty"
            - rule: "Password must be strong"
              rule_type: "validate_password_strength"
              validation_method: "validate_password_strength"
              error_message: "Password must be at least 8 characters with letters and numbers"
          nested_function:
            id: "nf003"
            function_name: "hash_password"
            function_type: "security"
            parameters: {}
            output_to: "at107"
            
    output_stack:
      description: "User password updated"
      outputs:
        - id: "out001"
          slot_id: "executionstatus.out001"
          contextual_id: "go001.lo003.out001"
          source:
            type: "system"
            description: "Success or failure"
          data_type: "string"
          
        - id: "out002"
          slot_id: "e001.at101.out002"
          contextual_id: "go001.lo003.out002"
          source:
            type: "system"
            description: "User ID"
          data_type: "string"
          
    data_mapping_stack:
      description: "Data handover between LOs"
      mappings: []
          
    execution_pathway:
      type: "terminal"
      next_lo: ""
