{"success": false, "component_type": "entities", "target_schema": "workflow_temp", "validation_timestamp": "2025-06-23T11:20:24.314000", "errors": [{"rule_id": "RULE_05", "message": "Entity category 'employee management' must be one of: ['master', 'transaction', 'reference', 'aggregate', 'contextual']", "severity": "error", "entity_id": "E13", "attribute_id": null, "timestamp": "2025-06-23T11:20:24.298401"}, {"rule_id": "RULE_11", "message": "Entity 'E13' must have at least one attribute", "severity": "error", "entity_id": "E13", "attribute_id": null, "timestamp": "2025-06-23T11:20:24.313969"}], "warnings": [{"rule_id": "RULE_02", "message": "Invalid status transition from deployed_to_production to deployed_to_temp", "severity": "warning", "entity_id": "E13", "attribute_id": null, "timestamp": "2025-06-23T11:20:24.275530"}, {"rule_id": "RULE_07", "message": "Version mismatch between schemas for entity E13: 3 vs 1", "severity": "warning", "entity_id": "E13", "attribute_id": null, "timestamp": "2025-06-23T11:20:24.306628"}], "error_count": 2, "warning_count": 2}