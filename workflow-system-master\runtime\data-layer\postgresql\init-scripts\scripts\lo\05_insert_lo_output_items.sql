-- Purchase Furniture LO Output Items Insert Script
-- Inserts output items for GO2.LO1, GO2.LO2, GO2.LO3

SET search_path TO workflow_runtime;

-- =====================================================
-- LO OUTPUT ITEMS
-- =====================================================

-- GO2.LO1 Output Items
INSERT INTO lo_output_items (
    item_id, output_stack_id, slot_id, source_type, source_description, 
    lo_id, data_type, ui_control, is_visible, name, type, 
    created_at, updated_at, created_by, updated_by, entity_id, attribute_id, 
    entity_name, attribute_name, version, natural_language
) VALUES 
('GO2.LO1.OP1.IT1', 'GO2.LO1.OP1', 'GO2.LO1.OSL1', 'output', 'Selected furniture type ID', 'GO2.LO1', 'string', 'label', true, 'furnituretypeid', 'output', NOW(), NOW(), 'system', 'system', 'E15', 'E15.At1', 'FurnitureType', 'furnituretypeid', '1.0', 'Output furniture type selection'),
('GO2.LO1.OP1.IT2', 'GO2.LO1.OP1', 'GO2.LO1.OSL2', 'output', 'Selected product ID', 'GO2.LO1', 'string', 'label', true, 'productid', 'output', NOW(), NOW(), 'system', 'system', 'E14', 'E14.At1', 'FurnitureProduct', 'productid', '1.0', 'Output product selection'),
('GO2.LO1.OP1.IT3', 'GO2.LO1.OP1', 'GO2.LO1.OSL3', 'output', 'Product name', 'GO2.LO1', 'string', 'label', true, 'productname', 'output', NOW(), NOW(), 'system', 'system', 'E14', 'E14.At2', 'FurnitureProduct', 'productname', '1.0', 'Output product name'),
('GO2.LO1.OP1.IT4', 'GO2.LO1.OP1', 'GO2.LO1.OSL4', 'output', 'Unit price', 'GO2.LO1', 'decimal', 'label', true, 'unitprice', 'output', NOW(), NOW(), 'system', 'system', 'E14', 'E14.At4', 'FurnitureProduct', 'price', '1.0', 'Output unit price');

-- GO2.LO2 Output Items
INSERT INTO lo_output_items (
    item_id, output_stack_id, slot_id, source_type, source_description, 
    lo_id, data_type, ui_control, is_visible, name, type, 
    created_at, updated_at, created_by, updated_by, entity_id, attribute_id, 
    entity_name, attribute_name, version, natural_language
) VALUES 
('GO2.LO2.OP1.IT1', 'GO2.LO2.OP1', 'GO2.LO2.OSL1', 'output', 'Order quantity', 'GO2.LO2', 'integer', 'label', true, 'quantity', 'output', NOW(), NOW(), 'system', 'system', 'E13', 'E13.At5', 'FurnitureOrder', 'quantity', '1.0', 'Output order quantity'),
('GO2.LO2.OP1.IT2', 'GO2.LO2.OP1', 'GO2.LO2.OSL2', 'output', 'Calculated subtotal', 'GO2.LO2', 'decimal', 'label', true, 'subtotal', 'output', NOW(), NOW(), 'system', 'system', 'E13', 'E13.At7', 'FurnitureOrder', 'subtotal', '1.0', 'Output calculated subtotal'),
('GO2.LO2.OP1.IT3', 'GO2.LO2.OP1', 'GO2.LO2.OSL3', 'output', 'GST amount', 'GO2.LO2', 'decimal', 'label', true, 'gstamount', 'output', NOW(), NOW(), 'system', 'system', 'E13', 'E13.At8', 'FurnitureOrder', 'gstamount', '1.0', 'Output GST amount'),
('GO2.LO2.OP1.IT4', 'GO2.LO2.OP1', 'GO2.LO2.OSL4', 'output', 'Total amount', 'GO2.LO2', 'decimal', 'label', true, 'totalamount', 'output', NOW(), NOW(), 'system', 'system', 'E13', 'E13.At9', 'FurnitureOrder', 'totalamount', '1.0', 'Output total amount'),
('GO2.LO2.OP1.IT5', 'GO2.LO2.OP1', 'GO2.LO2.OSL5', 'output', 'Payment method', 'GO2.LO2', 'string', 'label', true, 'paymentmethod', 'output', NOW(), NOW(), 'system', 'system', 'E13', 'E13.At10', 'FurnitureOrder', 'paymentmethod', '1.0', 'Output payment method'),
('GO2.LO2.OP1.IT6', 'GO2.LO2.OP1', 'GO2.LO2.OSL6', 'output', 'Payment details', 'GO2.LO2', 'string', 'label', true, 'paymentdetails', 'output', NOW(), NOW(), 'system', 'system', 'E16', 'E16.At1', 'PaymentDetails', 'paymentid', '1.0', 'Output payment details');

-- GO2.LO3 Output Items
INSERT INTO lo_output_items (
    item_id, output_stack_id, slot_id, source_type, source_description, 
    lo_id, data_type, ui_control, is_visible, name, type, 
    created_at, updated_at, created_by, updated_by, entity_id, attribute_id, 
    entity_name, attribute_name, version, natural_language
) VALUES 
('GO2.LO3.OP1.IT1', 'GO2.LO3.OP1', 'GO2.LO3.OSL1', 'output', 'Order ID', 'GO2.LO3', 'string', 'label', true, 'orderid', 'output', NOW(), NOW(), 'system', 'system', 'E13', 'E13.At1', 'FurnitureOrder', 'orderid', '1.0', 'Output order ID'),
('GO2.LO3.OP1.IT2', 'GO2.LO3.OP1', 'GO2.LO3.OSL2', 'output', 'Order status', 'GO2.LO3', 'string', 'label', true, 'orderstatus', 'output', NOW(), NOW(), 'system', 'system', 'E13', 'E13.At11', 'FurnitureOrder', 'orderstatus', '1.0', 'Output order status'),
('GO2.LO3.OP1.IT3', 'GO2.LO3.OP1', 'GO2.LO3.OSL3', 'output', 'Inventory updated', 'GO2.LO3', 'boolean', 'label', true, 'inventoryupdated', 'output', NOW(), NOW(), 'system', 'system', 'E14', 'E14.At5', 'FurnitureProduct', 'availableinventory', '1.0', 'Output inventory update status');

COMMIT;
