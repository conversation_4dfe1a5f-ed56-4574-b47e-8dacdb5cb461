{"timestamp": "2025-06-24T10:36:05.618210", "endpoint": "parse-validate-mongosave/entity-attributes", "input": {"natural_language": "Attribute Name: leaveId\nDisplay Name: Leave ID\nData Type: string\nRequired: true\nUnique: true\nDefault Type: computation\nDefault Value: LV-{YYYY}-{sequence}\nDescription: Unique identifier for the leave application\nHelper Text: Auto-generated leave identifier", "entity_context": {"entity_name": "LeaveApplication", "entity_id": "E_LeaveApplication"}, "edit_draft": true, "edit_temp": false, "edit_production": false}, "output": {"success": true, "attribute_results": [], "operation": "parse_validate_mongosave", "total_attributes": 0}, "status": "success"}
{"timestamp": "2025-06-24T12:33:39.036395", "endpoint": "parse-validate-mongosave/entity-attributes", "input": {"natural_language": "Attribute Name: testId\nDisplay Name: Test ID\nData Type: string\nRequired: true\nUnique: true\nDefault Type: computation\nDefault Value: TEST-{YYYY}-{sequence}\nDescription: Unique identifier for test entity\nHelper Text: Auto-generated test identifier", "entity_context": {"entity_name": "TestEntity2", "entity_id": "E_TestEntity2"}, "edit_draft": true, "edit_temp": false, "edit_production": false}, "output": {"success": true, "attribute_results": [], "operation": "parse_validate_mongosave", "total_attributes": 0}, "status": "success"}
