"""
List all tables in the workflow_temp schema.
"""

from db_utils import execute_query

def main():
    # List tables in workflow_temp schema
    print("Listing tables in workflow_temp schema")
    success, messages, tables = execute_query(
        """
        SELECT table_name 
        FROM information_schema.tables 
        WHERE table_schema = 'workflow_temp'
        ORDER BY table_name
        """,
        schema_name="workflow_temp"
    )
    
    if success and tables:
        print("Tables in workflow_temp schema:")
        for table in tables:
            print(f"  {table[0]}")
    else:
        print("Failed to list tables")
        if messages:
            print("Messages:")
            for message in messages:
                print(f"  {message}")

if __name__ == "__main__":
    main()
