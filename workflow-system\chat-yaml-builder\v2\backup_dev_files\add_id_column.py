#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to add the id column to the lo_input_items table in the workflow_temp schema.
"""

import os
import sys
import logging

# Add parent directory to path to import common modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from common.db_connection import get_db_connection
from db_utils import execute_query

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger('add_id_column')

def main():
    """
    Main function.
    """
    # Check if the id column exists in the lo_input_items table
    check_query = """
        SELECT EXISTS (
            SELECT FROM information_schema.columns
            WHERE table_schema = 'workflow_temp'
            AND table_name = 'lo_input_items'
            AND column_name = 'id'
        )
    """
    
    success, messages, result = execute_query(check_query)
    
    if not success:
        logger.error(f"Failed to check if id column exists: {messages}")
        return False
    
    id_column_exists = result and result[0][0]
    
    if id_column_exists:
        logger.info("id column already exists in workflow_temp.lo_input_items")
        return True
    
    # Add the id column to the lo_input_items table
    add_column_query = """
        ALTER TABLE workflow_temp.lo_input_items
        ADD COLUMN id VARCHAR(100)
    """
    
    success, messages, _ = execute_query(add_column_query)
    
    if not success:
        logger.error(f"Failed to add id column: {messages}")
        return False
    
    logger.info("Added id column to workflow_temp.lo_input_items")
    
    return True

if __name__ == '__main__':
    main()
