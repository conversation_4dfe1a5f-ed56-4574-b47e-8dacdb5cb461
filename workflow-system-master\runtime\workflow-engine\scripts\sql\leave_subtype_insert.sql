-- Insert query for Leave Subtypes
INSERT INTO workflow_runtime.leave_sub_type 
(created_by, updated_by, leave_type, sub_type_id, sub_type_name, active)
VALUES
-- Annual Leave subtypes
('system', 'system', 'Annual Leave', 'AL001', 'Regular Annual Leave', true),
('system', 'system', 'Annual Leave', 'AL002', 'Advance Annual Leave', true),
('system', 'system', 'Annual Leave', 'AL003', 'Compensatory Leave', true),
('system', 'system', 'Annual Leave', 'AL004', 'Earned Leave', true),

-- Sick Leave subtypes
('system', 'system', 'Sick Leave', 'SL001', 'Short-term Illness', true),
('system', 'system', 'Sick Leave', 'SL002', 'Long-term Illness', true),
('system', 'system', 'Sick Leave', 'SL003', 'Medical Appointment', true),
('system', 'system', 'Sick Leave', 'SL004', 'Hospitalization', true),

-- Parental Leave subtypes
('system', 'system', 'Parental Leave', 'PL001', 'Maternity Leave', true),
('system', 'system', 'Parental Leave', 'PL002', 'Paternity Leave', true),
('system', 'system', 'Parental Leave', 'PL003', 'Adoption Leave', true),
('system', 'system', 'Parental Leave', 'PL004', 'Child Care Leave', true),

-- Bereavement subtypes
('system', 'system', 'Bereavement', 'BL001', 'Immediate Family', true),
('system', 'system', 'Bereavement', 'BL002', 'Extended Family', true),
('system', 'system', 'Bereavement', 'BL003', 'Funeral Attendance', true),
('system', 'system', 'Bereavement', 'BL004', 'Compassionate Leave', true);
