--
-- PostgreSQL database dump
--

-- Dumped from database version 15.12 (Debian 15.12-1.pgdg120+1)
-- Dumped by pg_dump version 15.12 (Debian 15.12-1.pgdg120+1)

SET statement_timeout = 0;
SET lock_timeout = 0;
SET idle_in_transaction_session_timeout = 0;
SET client_encoding = 'UTF8';
SET standard_conforming_strings = on;
SELECT pg_catalog.set_config('search_path', '', false);
SET check_function_bodies = false;
SET xmloption = content;
SET client_min_messages = warning;
SET row_security = off;

--
-- Name: function_dev; Type: SCHEMA; Schema: -; Owner: postgres
--

CREATE SCHEMA function_dev;


ALTER SCHEMA function_dev OWNER TO postgres;

--
-- Name: tenant_t001; Type: SCHEMA; Schema: -; Owner: postgres
--

CREATE SCHEMA tenant_t001;


ALTER SCHEMA tenant_t001 OWNER TO postgres;

--
-- Name: workflow_runtime; Type: SCHEMA; Schema: -; Owner: postgres
--

CREATE SCHEMA workflow_runtime;


ALTER SCHEMA workflow_runtime OWNER TO postgres;

--
-- Name: uuid-ossp; Type: EXTENSION; Schema: -; Owner: -
--

CREATE EXTENSION IF NOT EXISTS "uuid-ossp" WITH SCHEMA public;


--
-- Name: EXTENSION "uuid-ossp"; Type: COMMENT; Schema: -; Owner: 
--

COMMENT ON EXTENSION "uuid-ossp" IS 'generate universally unique identifiers (UUIDs)';


--
-- Name: input_source_type; Type: TYPE; Schema: workflow_runtime; Owner: postgres
--

CREATE TYPE workflow_runtime.input_source_type AS ENUM (
    'user',
    'information',
    'system',
    'system_dependent'
);


ALTER TYPE workflow_runtime.input_source_type OWNER TO postgres;

--
-- Name: update_modified_column(); Type: FUNCTION; Schema: workflow_runtime; Owner: postgres
--

CREATE FUNCTION workflow_runtime.update_modified_column() RETURNS trigger
    LANGUAGE plpgsql
    AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$;


ALTER FUNCTION workflow_runtime.update_modified_column() OWNER TO postgres;

--
-- Name: update_timestamp(); Type: FUNCTION; Schema: workflow_runtime; Owner: postgres
--

CREATE FUNCTION workflow_runtime.update_timestamp() RETURNS trigger
    LANGUAGE plpgsql
    AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$;


ALTER FUNCTION workflow_runtime.update_timestamp() OWNER TO postgres;

SET default_tablespace = '';

SET default_table_access_method = heap;

--
-- Name: agent_rights; Type: TABLE; Schema: workflow_runtime; Owner: postgres
--

CREATE TABLE workflow_runtime.agent_rights (
    id integer NOT NULL,
    agent_stack_id integer NOT NULL,
    role_id character varying(50) NOT NULL,
    right_id character varying(50) NOT NULL,
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE workflow_runtime.agent_rights OWNER TO postgres;

--
-- Name: agent_rights_id_seq; Type: SEQUENCE; Schema: workflow_runtime; Owner: postgres
--

CREATE SEQUENCE workflow_runtime.agent_rights_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE workflow_runtime.agent_rights_id_seq OWNER TO postgres;

--
-- Name: agent_rights_id_seq; Type: SEQUENCE OWNED BY; Schema: workflow_runtime; Owner: postgres
--

ALTER SEQUENCE workflow_runtime.agent_rights_id_seq OWNED BY workflow_runtime.agent_rights.id;


--
-- Name: agent_stack; Type: TABLE; Schema: workflow_runtime; Owner: postgres
--

CREATE TABLE workflow_runtime.agent_stack (
    id integer NOT NULL,
    lo_id character varying(50) NOT NULL,
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE workflow_runtime.agent_stack OWNER TO postgres;

--
-- Name: agent_stack_id_seq; Type: SEQUENCE; Schema: workflow_runtime; Owner: postgres
--

CREATE SEQUENCE workflow_runtime.agent_stack_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE workflow_runtime.agent_stack_id_seq OWNER TO postgres;

--
-- Name: agent_stack_id_seq; Type: SEQUENCE OWNED BY; Schema: workflow_runtime; Owner: postgres
--

ALTER SEQUENCE workflow_runtime.agent_stack_id_seq OWNED BY workflow_runtime.agent_stack.id;


--
-- Name: alembic_version; Type: TABLE; Schema: workflow_runtime; Owner: postgres
--

CREATE TABLE workflow_runtime.alembic_version (
    version_num character varying(32) NOT NULL
);


ALTER TABLE workflow_runtime.alembic_version OWNER TO postgres;

--
-- Name: asset_request; Type: TABLE; Schema: workflow_runtime; Owner: postgres
--

CREATE TABLE workflow_runtime.asset_request (
    id integer NOT NULL,
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    created_by character varying(255),
    updated_by character varying(255),
    request_id character varying(255) NOT NULL,
    employee_id character varying(255) NOT NULL,
    asset_type character varying(50) NOT NULL,
    specifications character varying(255),
    request_date date NOT NULL,
    approval_status character varying(50),
    approval_date date,
    approval_remarks character varying(255),
    CONSTRAINT chk_asset_request_approval_status CHECK (((approval_status)::text = ANY ((ARRAY['Approved'::character varying, 'Rejected'::character varying, 'Pending'::character varying])::text[]))),
    CONSTRAINT chk_asset_request_asset_type CHECK (((asset_type)::text = ANY ((ARRAY['Stationary'::character varying, 'System'::character varying, 'Furniture'::character varying, 'Electronics'::character varying, 'Software'::character varying])::text[])))
);


ALTER TABLE workflow_runtime.asset_request OWNER TO postgres;

--
-- Name: asset_request_id_seq; Type: SEQUENCE; Schema: workflow_runtime; Owner: postgres
--

CREATE SEQUENCE workflow_runtime.asset_request_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE workflow_runtime.asset_request_id_seq OWNER TO postgres;

--
-- Name: asset_request_id_seq; Type: SEQUENCE OWNED BY; Schema: workflow_runtime; Owner: postgres
--

ALTER SEQUENCE workflow_runtime.asset_request_id_seq OWNED BY workflow_runtime.asset_request.id;


--
-- Name: attribute_enum_values; Type: TABLE; Schema: workflow_runtime; Owner: postgres
--

CREATE TABLE workflow_runtime.attribute_enum_values (
    id integer NOT NULL,
    attribute_id character varying(50) NOT NULL,
    value character varying(100) NOT NULL,
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE workflow_runtime.attribute_enum_values OWNER TO postgres;

--
-- Name: attribute_enum_values_id_seq; Type: SEQUENCE; Schema: workflow_runtime; Owner: postgres
--

CREATE SEQUENCE workflow_runtime.attribute_enum_values_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE workflow_runtime.attribute_enum_values_id_seq OWNER TO postgres;

--
-- Name: attribute_enum_values_id_seq; Type: SEQUENCE OWNED BY; Schema: workflow_runtime; Owner: postgres
--

ALTER SEQUENCE workflow_runtime.attribute_enum_values_id_seq OWNED BY workflow_runtime.attribute_enum_values.id;


--
-- Name: attribute_ui_controls; Type: TABLE; Schema: workflow_runtime; Owner: postgres
--

CREATE TABLE workflow_runtime.attribute_ui_controls (
    entity_id text NOT NULL,
    attribute_id text NOT NULL,
    ui_control text NOT NULL,
    created_at timestamp without time zone DEFAULT now(),
    updated_at timestamp without time zone DEFAULT now()
);


ALTER TABLE workflow_runtime.attribute_ui_controls OWNER TO postgres;

--
-- Name: attribute_validations; Type: TABLE; Schema: workflow_runtime; Owner: postgres
--

CREATE TABLE workflow_runtime.attribute_validations (
    id integer NOT NULL,
    attribute_id character varying(50) NOT NULL,
    rule character varying(100) NOT NULL,
    expression text,
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE workflow_runtime.attribute_validations OWNER TO postgres;

--
-- Name: attribute_validations_id_seq; Type: SEQUENCE; Schema: workflow_runtime; Owner: postgres
--

CREATE SEQUENCE workflow_runtime.attribute_validations_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE workflow_runtime.attribute_validations_id_seq OWNER TO postgres;

--
-- Name: attribute_validations_id_seq; Type: SEQUENCE OWNED BY; Schema: workflow_runtime; Owner: postgres
--

ALTER SEQUENCE workflow_runtime.attribute_validations_id_seq OWNED BY workflow_runtime.attribute_validations.id;


--
-- Name: attribute_validations_id_seq1; Type: SEQUENCE; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE workflow_runtime.attribute_validations ALTER COLUMN id ADD GENERATED BY DEFAULT AS IDENTITY (
    SEQUENCE NAME workflow_runtime.attribute_validations_id_seq1
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);


--
-- Name: conditional_success_messages; Type: TABLE; Schema: workflow_runtime; Owner: postgres
--

CREATE TABLE workflow_runtime.conditional_success_messages (
    id integer NOT NULL,
    lo_id character varying(50) NOT NULL,
    condition_expression text,
    message text NOT NULL,
    is_default boolean DEFAULT false,
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE workflow_runtime.conditional_success_messages OWNER TO postgres;

--
-- Name: conditional_success_messages_id_seq; Type: SEQUENCE; Schema: workflow_runtime; Owner: postgres
--

CREATE SEQUENCE workflow_runtime.conditional_success_messages_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE workflow_runtime.conditional_success_messages_id_seq OWNER TO postgres;

--
-- Name: conditional_success_messages_id_seq; Type: SEQUENCE OWNED BY; Schema: workflow_runtime; Owner: postgres
--

ALTER SEQUENCE workflow_runtime.conditional_success_messages_id_seq OWNED BY workflow_runtime.conditional_success_messages.id;


--
-- Name: data_mapping_stack; Type: TABLE; Schema: workflow_runtime; Owner: postgres
--

CREATE TABLE workflow_runtime.data_mapping_stack (
    id integer NOT NULL,
    go_id character varying(50) NOT NULL,
    description text,
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE workflow_runtime.data_mapping_stack OWNER TO postgres;

--
-- Name: data_mapping_stack_id_seq; Type: SEQUENCE; Schema: workflow_runtime; Owner: postgres
--

CREATE SEQUENCE workflow_runtime.data_mapping_stack_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE workflow_runtime.data_mapping_stack_id_seq OWNER TO postgres;

--
-- Name: data_mapping_stack_id_seq; Type: SEQUENCE OWNED BY; Schema: workflow_runtime; Owner: postgres
--

ALTER SEQUENCE workflow_runtime.data_mapping_stack_id_seq OWNED BY workflow_runtime.data_mapping_stack.id;


--
-- Name: data_mappings; Type: TABLE; Schema: workflow_runtime; Owner: postgres
--

CREATE TABLE workflow_runtime.data_mappings (
    id character varying(50) NOT NULL,
    mapping_stack_id integer NOT NULL,
    source character varying(50) NOT NULL,
    target character varying(50) NOT NULL,
    mapping_type character varying(50) NOT NULL,
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE workflow_runtime.data_mappings OWNER TO postgres;

--
-- Name: department; Type: TABLE; Schema: workflow_runtime; Owner: postgres
--

CREATE TABLE workflow_runtime.department (
    id integer NOT NULL,
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    created_by character varying(255),
    updated_by character varying(255),
    departmentid character varying(255),
    departmentname character varying(255),
    managerid character varying(255),
    active boolean
);


ALTER TABLE workflow_runtime.department OWNER TO postgres;

--
-- Name: department_id_seq; Type: SEQUENCE; Schema: workflow_runtime; Owner: postgres
--

CREATE SEQUENCE workflow_runtime.department_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE workflow_runtime.department_id_seq OWNER TO postgres;

--
-- Name: department_id_seq; Type: SEQUENCE OWNED BY; Schema: workflow_runtime; Owner: postgres
--

ALTER SEQUENCE workflow_runtime.department_id_seq OWNED BY workflow_runtime.department.id;


--
-- Name: dropdown_data_sources; Type: TABLE; Schema: workflow_runtime; Owner: postgres
--

CREATE TABLE workflow_runtime.dropdown_data_sources (
    id integer NOT NULL,
    input_item_id character varying(50) NOT NULL,
    input_stack_id integer NOT NULL,
    source_type character varying(50) NOT NULL,
    query_text text,
    function_name character varying(255),
    function_params jsonb,
    value_field character varying(100) NOT NULL,
    display_field character varying(100) NOT NULL,
    depends_on_fields jsonb,
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE workflow_runtime.dropdown_data_sources OWNER TO postgres;

--
-- Name: dropdown_data_sources_id_seq; Type: SEQUENCE; Schema: workflow_runtime; Owner: postgres
--

CREATE SEQUENCE workflow_runtime.dropdown_data_sources_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE workflow_runtime.dropdown_data_sources_id_seq OWNER TO postgres;

--
-- Name: dropdown_data_sources_id_seq; Type: SEQUENCE OWNED BY; Schema: workflow_runtime; Owner: postgres
--

ALTER SEQUENCE workflow_runtime.dropdown_data_sources_id_seq OWNED BY workflow_runtime.dropdown_data_sources.id;


--
-- Name: entities; Type: TABLE; Schema: workflow_runtime; Owner: postgres
--

CREATE TABLE workflow_runtime.entities (
    entity_id character varying(50) NOT NULL,
    name character varying(100) NOT NULL,
    version character varying(20) DEFAULT '1.0'::character varying,
    status character varying(50) NOT NULL,
    type character varying(50) NOT NULL,
    attribute_prefix character varying(10),
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    description text
);


ALTER TABLE workflow_runtime.entities OWNER TO postgres;

--
-- Name: entity_attribute_metadata; Type: TABLE; Schema: workflow_runtime; Owner: postgres
--

CREATE TABLE workflow_runtime.entity_attribute_metadata (
    entity_id character varying(50) NOT NULL,
    attribute_id character varying(50) NOT NULL,
    attribute_name character varying(100) NOT NULL,
    required boolean DEFAULT false
);


ALTER TABLE workflow_runtime.entity_attribute_metadata OWNER TO postgres;

--
-- Name: entity_attributes; Type: TABLE; Schema: workflow_runtime; Owner: postgres
--

CREATE TABLE workflow_runtime.entity_attributes (
    attribute_id character varying(50) NOT NULL,
    entity_id character varying(50) NOT NULL,
    name character varying(100) NOT NULL,
    display_name character varying(100) NOT NULL,
    datatype character varying(50) NOT NULL,
    version character varying(20) DEFAULT '1.0'::character varying,
    status character varying(50) NOT NULL,
    required boolean DEFAULT false,
    reference_entity_id character varying(50),
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE workflow_runtime.entity_attributes OWNER TO postgres;

--
-- Name: entity_permissions; Type: TABLE; Schema: workflow_runtime; Owner: postgres
--

CREATE TABLE workflow_runtime.entity_permissions (
    id integer NOT NULL,
    role_id character varying(50) NOT NULL,
    entity_id character varying(50) NOT NULL,
    permission_id character varying(50) NOT NULL,
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE workflow_runtime.entity_permissions OWNER TO postgres;

--
-- Name: entity_permissions_id_seq; Type: SEQUENCE; Schema: workflow_runtime; Owner: postgres
--

CREATE SEQUENCE workflow_runtime.entity_permissions_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE workflow_runtime.entity_permissions_id_seq OWNER TO postgres;

--
-- Name: entity_permissions_id_seq; Type: SEQUENCE OWNED BY; Schema: workflow_runtime; Owner: postgres
--

ALTER SEQUENCE workflow_runtime.entity_permissions_id_seq OWNED BY workflow_runtime.entity_permissions.id;


--
-- Name: entity_relationships; Type: TABLE; Schema: workflow_runtime; Owner: postgres
--

CREATE TABLE workflow_runtime.entity_relationships (
    id integer NOT NULL,
    source_entity_id character varying(50) NOT NULL,
    target_entity_id character varying(50) NOT NULL,
    relationship_type character varying(50) NOT NULL,
    source_attribute_id character varying(50) NOT NULL,
    target_attribute_id character varying(50) NOT NULL,
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE workflow_runtime.entity_relationships OWNER TO postgres;

--
-- Name: entity_relationships_id_seq; Type: SEQUENCE; Schema: workflow_runtime; Owner: postgres
--

CREATE SEQUENCE workflow_runtime.entity_relationships_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE workflow_runtime.entity_relationships_id_seq OWNER TO postgres;

--
-- Name: entity_relationships_id_seq; Type: SEQUENCE OWNED BY; Schema: workflow_runtime; Owner: postgres
--

ALTER SEQUENCE workflow_runtime.entity_relationships_id_seq OWNED BY workflow_runtime.entity_relationships.id;


--
-- Name: execution_path_tracking; Type: TABLE; Schema: workflow_runtime; Owner: postgres
--

CREATE TABLE workflow_runtime.execution_path_tracking (
    id integer NOT NULL,
    metrics_stack_id integer NOT NULL,
    enabled boolean DEFAULT true,
    store_complete_path boolean DEFAULT true,
    identify_bottlenecks boolean DEFAULT false,
    compare_to_historical boolean DEFAULT false,
    path_efficiency_function character varying(100),
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE workflow_runtime.execution_path_tracking OWNER TO postgres;

--
-- Name: execution_path_tracking_id_seq; Type: SEQUENCE; Schema: workflow_runtime; Owner: postgres
--

CREATE SEQUENCE workflow_runtime.execution_path_tracking_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE workflow_runtime.execution_path_tracking_id_seq OWNER TO postgres;

--
-- Name: execution_path_tracking_id_seq; Type: SEQUENCE OWNED BY; Schema: workflow_runtime; Owner: postgres
--

ALTER SEQUENCE workflow_runtime.execution_path_tracking_id_seq OWNED BY workflow_runtime.execution_path_tracking.id;


--
-- Name: execution_pathway_conditions; Type: TABLE; Schema: workflow_runtime; Owner: postgres
--

CREATE TABLE workflow_runtime.execution_pathway_conditions (
    id integer NOT NULL,
    lo_id character varying(50) NOT NULL,
    condition_type character varying(50) NOT NULL,
    condition_entity character varying(50),
    condition_attribute character varying(50),
    condition_operator character varying(20),
    condition_value text,
    next_lo character varying(50) NOT NULL,
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE workflow_runtime.execution_pathway_conditions OWNER TO postgres;

--
-- Name: execution_pathway_conditions_id_seq; Type: SEQUENCE; Schema: workflow_runtime; Owner: postgres
--

CREATE SEQUENCE workflow_runtime.execution_pathway_conditions_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE workflow_runtime.execution_pathway_conditions_id_seq OWNER TO postgres;

--
-- Name: execution_pathway_conditions_id_seq; Type: SEQUENCE OWNED BY; Schema: workflow_runtime; Owner: postgres
--

ALTER SEQUENCE workflow_runtime.execution_pathway_conditions_id_seq OWNED BY workflow_runtime.execution_pathway_conditions.id;


--
-- Name: execution_pathways; Type: TABLE; Schema: workflow_runtime; Owner: postgres
--

CREATE TABLE workflow_runtime.execution_pathways (
    lo_id character varying(50) NOT NULL,
    pathway_type character varying(50) NOT NULL,
    next_lo character varying(50),
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    id integer NOT NULL
);


ALTER TABLE workflow_runtime.execution_pathways OWNER TO postgres;

--
-- Name: execution_pathways_id_seq; Type: SEQUENCE; Schema: workflow_runtime; Owner: postgres
--

CREATE SEQUENCE workflow_runtime.execution_pathways_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE workflow_runtime.execution_pathways_id_seq OWNER TO postgres;

--
-- Name: execution_pathways_id_seq; Type: SEQUENCE OWNED BY; Schema: workflow_runtime; Owner: postgres
--

ALTER SEQUENCE workflow_runtime.execution_pathways_id_seq OWNED BY workflow_runtime.execution_pathways.id;


--
-- Name: execution_rules; Type: TABLE; Schema: workflow_runtime; Owner: postgres
--

CREATE TABLE workflow_runtime.execution_rules (
    id character varying(50) NOT NULL,
    lo_id character varying(50) NOT NULL,
    contextual_id character varying(100) NOT NULL,
    description text,
    structured_rule jsonb,
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE workflow_runtime.execution_rules OWNER TO postgres;

--
-- Name: global_objectives; Type: TABLE; Schema: workflow_runtime; Owner: postgres
--

CREATE TABLE workflow_runtime.global_objectives (
    go_id character varying(50) NOT NULL,
    name character varying(100) NOT NULL,
    version character varying(20) DEFAULT '1.0'::character varying,
    status character varying(50) NOT NULL,
    description text,
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    tenant_id character varying(50),
    last_used timestamp without time zone,
    deleted_mark boolean DEFAULT false
);


ALTER TABLE workflow_runtime.global_objectives OWNER TO postgres;

--
-- Name: input_data_sources; Type: TABLE; Schema: workflow_runtime; Owner: postgres
--

CREATE TABLE workflow_runtime.input_data_sources (
    id integer NOT NULL,
    input_item_id character varying(50) NOT NULL,
    input_stack_id integer NOT NULL,
    source_type character varying(50) NOT NULL,
    function_name character varying(100),
    parameters jsonb,
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE workflow_runtime.input_data_sources OWNER TO postgres;

--
-- Name: input_data_sources_id_seq; Type: SEQUENCE; Schema: workflow_runtime; Owner: postgres
--

CREATE SEQUENCE workflow_runtime.input_data_sources_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE workflow_runtime.input_data_sources_id_seq OWNER TO postgres;

--
-- Name: input_data_sources_id_seq; Type: SEQUENCE OWNED BY; Schema: workflow_runtime; Owner: postgres
--

ALTER SEQUENCE workflow_runtime.input_data_sources_id_seq OWNED BY workflow_runtime.input_data_sources.id;


--
-- Name: input_dependencies; Type: TABLE; Schema: workflow_runtime; Owner: postgres
--

CREATE TABLE workflow_runtime.input_dependencies (
    id integer NOT NULL,
    input_item_id character varying(50) NOT NULL,
    input_stack_id integer NOT NULL,
    depends_on_id character varying(50) NOT NULL,
    depends_on_stack_id integer NOT NULL,
    dependency_type character varying(50) NOT NULL,
    condition_expression text,
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE workflow_runtime.input_dependencies OWNER TO postgres;

--
-- Name: input_dependencies_id_seq; Type: SEQUENCE; Schema: workflow_runtime; Owner: postgres
--

CREATE SEQUENCE workflow_runtime.input_dependencies_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE workflow_runtime.input_dependencies_id_seq OWNER TO postgres;

--
-- Name: input_dependencies_id_seq; Type: SEQUENCE OWNED BY; Schema: workflow_runtime; Owner: postgres
--

ALTER SEQUENCE workflow_runtime.input_dependencies_id_seq OWNED BY workflow_runtime.input_dependencies.id;


--
-- Name: input_items; Type: TABLE; Schema: workflow_runtime; Owner: postgres
--

CREATE TABLE workflow_runtime.input_items (
    id character varying(50) NOT NULL,
    input_stack_id integer NOT NULL,
    slot_id character varying(50) NOT NULL,
    contextual_id character varying(50) NOT NULL,
    entity_reference character varying(50),
    attribute_reference character varying(50),
    source_type character varying(50) NOT NULL,
    source_description text,
    required boolean DEFAULT false,
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE workflow_runtime.input_items OWNER TO postgres;

--
-- Name: input_stack; Type: TABLE; Schema: workflow_runtime; Owner: postgres
--

CREATE TABLE workflow_runtime.input_stack (
    id integer NOT NULL,
    go_id character varying(50) NOT NULL,
    description text,
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE workflow_runtime.input_stack OWNER TO postgres;

--
-- Name: input_stack_id_seq; Type: SEQUENCE; Schema: workflow_runtime; Owner: postgres
--

CREATE SEQUENCE workflow_runtime.input_stack_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE workflow_runtime.input_stack_id_seq OWNER TO postgres;

--
-- Name: input_stack_id_seq; Type: SEQUENCE OWNED BY; Schema: workflow_runtime; Owner: postgres
--

ALTER SEQUENCE workflow_runtime.input_stack_id_seq OWNED BY workflow_runtime.input_stack.id;


--
-- Name: leave_application; Type: TABLE; Schema: workflow_runtime; Owner: postgres
--

CREATE TABLE workflow_runtime.leave_application (
    id integer NOT NULL,
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    created_by character varying(255),
    updated_by character varying(255),
    leave_id character varying(255),
    employee_id character varying(255),
    start_date date,
    end_date date,
    num_days integer,
    reason character varying(255),
    status character varying(50),
    remarks character varying(255),
    approved_by character varying(255),
    leave_type character varying(50),
    leave_sub_type character varying(255),
    leave_request_instructions character varying(255),
    short_leave_policy character varying(255),
    extended_leave_policy character varying(255)
);


ALTER TABLE workflow_runtime.leave_application OWNER TO postgres;

--
-- Name: leave_application_id_seq; Type: SEQUENCE; Schema: workflow_runtime; Owner: postgres
--

CREATE SEQUENCE workflow_runtime.leave_application_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE workflow_runtime.leave_application_id_seq OWNER TO postgres;

--
-- Name: leave_application_id_seq; Type: SEQUENCE OWNED BY; Schema: workflow_runtime; Owner: postgres
--

ALTER SEQUENCE workflow_runtime.leave_application_id_seq OWNED BY workflow_runtime.leave_application.id;


--
-- Name: leave_sub_type; Type: TABLE; Schema: workflow_runtime; Owner: postgres
--

CREATE TABLE workflow_runtime.leave_sub_type (
    id integer NOT NULL,
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    created_by character varying(255),
    updated_by character varying(255),
    leave_type character varying(255),
    sub_type_id character varying(255),
    sub_type_name character varying(255),
    active boolean
);


ALTER TABLE workflow_runtime.leave_sub_type OWNER TO postgres;

--
-- Name: leave_sub_type_id_seq; Type: SEQUENCE; Schema: workflow_runtime; Owner: postgres
--

CREATE SEQUENCE workflow_runtime.leave_sub_type_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE workflow_runtime.leave_sub_type_id_seq OWNER TO postgres;

--
-- Name: leave_sub_type_id_seq; Type: SEQUENCE OWNED BY; Schema: workflow_runtime; Owner: postgres
--

ALTER SEQUENCE workflow_runtime.leave_sub_type_id_seq OWNED BY workflow_runtime.leave_sub_type.id;


--
-- Name: lo_data_mapping_stack; Type: TABLE; Schema: workflow_runtime; Owner: postgres
--

CREATE TABLE workflow_runtime.lo_data_mapping_stack (
    id integer NOT NULL,
    lo_id character varying(50) NOT NULL,
    description text,
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE workflow_runtime.lo_data_mapping_stack OWNER TO postgres;

--
-- Name: lo_data_mapping_stack_id_seq; Type: SEQUENCE; Schema: workflow_runtime; Owner: postgres
--

CREATE SEQUENCE workflow_runtime.lo_data_mapping_stack_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE workflow_runtime.lo_data_mapping_stack_id_seq OWNER TO postgres;

--
-- Name: lo_data_mapping_stack_id_seq; Type: SEQUENCE OWNED BY; Schema: workflow_runtime; Owner: postgres
--

ALTER SEQUENCE workflow_runtime.lo_data_mapping_stack_id_seq OWNED BY workflow_runtime.lo_data_mapping_stack.id;


--
-- Name: lo_data_mappings; Type: TABLE; Schema: workflow_runtime; Owner: postgres
--

CREATE TABLE workflow_runtime.lo_data_mappings (
    id character varying(50) NOT NULL,
    mapping_stack_id integer NOT NULL,
    source character varying(100) NOT NULL,
    target character varying(100) NOT NULL,
    mapping_type character varying(50) NOT NULL,
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE workflow_runtime.lo_data_mappings OWNER TO postgres;

--
-- Name: lo_input_execution; Type: TABLE; Schema: workflow_runtime; Owner: postgres
--

CREATE TABLE workflow_runtime.lo_input_execution (
    id integer NOT NULL,
    instance_id uuid NOT NULL,
    lo_id text NOT NULL,
    input_contextual_id text NOT NULL,
    input_value jsonb,
    created_at timestamp without time zone DEFAULT now()
);


ALTER TABLE workflow_runtime.lo_input_execution OWNER TO postgres;

--
-- Name: lo_input_execution_id_seq; Type: SEQUENCE; Schema: workflow_runtime; Owner: postgres
--

CREATE SEQUENCE workflow_runtime.lo_input_execution_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE workflow_runtime.lo_input_execution_id_seq OWNER TO postgres;

--
-- Name: lo_input_execution_id_seq; Type: SEQUENCE OWNED BY; Schema: workflow_runtime; Owner: postgres
--

ALTER SEQUENCE workflow_runtime.lo_input_execution_id_seq OWNED BY workflow_runtime.lo_input_execution.id;


--
-- Name: lo_input_items; Type: TABLE; Schema: workflow_runtime; Owner: postgres
--

CREATE TABLE workflow_runtime.lo_input_items (
    id character varying(50) NOT NULL,
    input_stack_id integer NOT NULL,
    slot_id character varying(100) NOT NULL,
    contextual_id character varying(100) NOT NULL,
    source_type workflow_runtime.input_source_type NOT NULL,
    source_description text,
    required boolean DEFAULT false,
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    lo_id character varying(50),
    data_type character varying(50),
    ui_control character varying(50),
    nested_function jsonb,
    nested_functions jsonb,
    metadata jsonb,
    dependencies jsonb,
    dependency_type character varying(50) DEFAULT NULL::character varying,
    lookup_function jsonb,
    is_visible boolean DEFAULT true
);


ALTER TABLE workflow_runtime.lo_input_items OWNER TO postgres;

--
-- Name: lo_input_stack; Type: TABLE; Schema: workflow_runtime; Owner: postgres
--

CREATE TABLE workflow_runtime.lo_input_stack (
    id integer NOT NULL,
    lo_id character varying(50) NOT NULL,
    description text,
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE workflow_runtime.lo_input_stack OWNER TO postgres;

--
-- Name: lo_input_stack_id_seq; Type: SEQUENCE; Schema: workflow_runtime; Owner: postgres
--

CREATE SEQUENCE workflow_runtime.lo_input_stack_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE workflow_runtime.lo_input_stack_id_seq OWNER TO postgres;

--
-- Name: lo_input_stack_id_seq; Type: SEQUENCE OWNED BY; Schema: workflow_runtime; Owner: postgres
--

ALTER SEQUENCE workflow_runtime.lo_input_stack_id_seq OWNED BY workflow_runtime.lo_input_stack.id;


--
-- Name: lo_input_validations; Type: TABLE; Schema: workflow_runtime; Owner: postgres
--

CREATE TABLE workflow_runtime.lo_input_validations (
    id integer NOT NULL,
    input_item_id character varying(50) NOT NULL,
    input_stack_id integer NOT NULL,
    rule character varying(100) NOT NULL,
    rule_type character varying(50) NOT NULL,
    entity character varying(50),
    attribute character varying(50),
    validation_method character varying(50),
    reference_date_source character varying(100),
    allowed_values jsonb,
    error_message text,
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE workflow_runtime.lo_input_validations OWNER TO postgres;

--
-- Name: lo_input_validations_id_seq; Type: SEQUENCE; Schema: workflow_runtime; Owner: postgres
--

CREATE SEQUENCE workflow_runtime.lo_input_validations_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE workflow_runtime.lo_input_validations_id_seq OWNER TO postgres;

--
-- Name: lo_input_validations_id_seq; Type: SEQUENCE OWNED BY; Schema: workflow_runtime; Owner: postgres
--

ALTER SEQUENCE workflow_runtime.lo_input_validations_id_seq OWNED BY workflow_runtime.lo_input_validations.id;


--
-- Name: lo_nested_functions; Type: TABLE; Schema: workflow_runtime; Owner: postgres
--

CREATE TABLE workflow_runtime.lo_nested_functions (
    id integer NOT NULL,
    lo_id character varying(50) NOT NULL,
    nested_function_id character varying(50) NOT NULL,
    function_name character varying(100) NOT NULL,
    function_type character varying(50) NOT NULL,
    parameters jsonb,
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    input_contextual_id text,
    output_to text
);


ALTER TABLE workflow_runtime.lo_nested_functions OWNER TO postgres;

--
-- Name: lo_nested_functions_id_seq; Type: SEQUENCE; Schema: workflow_runtime; Owner: postgres
--

CREATE SEQUENCE workflow_runtime.lo_nested_functions_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE workflow_runtime.lo_nested_functions_id_seq OWNER TO postgres;

--
-- Name: lo_nested_functions_id_seq; Type: SEQUENCE OWNED BY; Schema: workflow_runtime; Owner: postgres
--

ALTER SEQUENCE workflow_runtime.lo_nested_functions_id_seq OWNED BY workflow_runtime.lo_nested_functions.id;


--
-- Name: lo_output_execution; Type: TABLE; Schema: workflow_runtime; Owner: postgres
--

CREATE TABLE workflow_runtime.lo_output_execution (
    id integer NOT NULL,
    instance_id uuid NOT NULL,
    lo_id text NOT NULL,
    output_contextual_id text NOT NULL,
    output_value jsonb,
    created_at timestamp without time zone DEFAULT now()
);


ALTER TABLE workflow_runtime.lo_output_execution OWNER TO postgres;

--
-- Name: lo_output_execution_id_seq; Type: SEQUENCE; Schema: workflow_runtime; Owner: postgres
--

CREATE SEQUENCE workflow_runtime.lo_output_execution_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE workflow_runtime.lo_output_execution_id_seq OWNER TO postgres;

--
-- Name: lo_output_execution_id_seq; Type: SEQUENCE OWNED BY; Schema: workflow_runtime; Owner: postgres
--

ALTER SEQUENCE workflow_runtime.lo_output_execution_id_seq OWNED BY workflow_runtime.lo_output_execution.id;


--
-- Name: lo_output_items; Type: TABLE; Schema: workflow_runtime; Owner: postgres
--

CREATE TABLE workflow_runtime.lo_output_items (
    id character varying(50) NOT NULL,
    output_stack_id integer NOT NULL,
    slot_id character varying(100) NOT NULL,
    contextual_id character varying(100) NOT NULL,
    source character varying(100),
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    lo_id text
);


ALTER TABLE workflow_runtime.lo_output_items OWNER TO postgres;

--
-- Name: lo_output_stack; Type: TABLE; Schema: workflow_runtime; Owner: postgres
--

CREATE TABLE workflow_runtime.lo_output_stack (
    id integer NOT NULL,
    lo_id character varying(50) NOT NULL,
    description text,
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE workflow_runtime.lo_output_stack OWNER TO postgres;

--
-- Name: lo_output_stack_id_seq; Type: SEQUENCE; Schema: workflow_runtime; Owner: postgres
--

CREATE SEQUENCE workflow_runtime.lo_output_stack_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE workflow_runtime.lo_output_stack_id_seq OWNER TO postgres;

--
-- Name: lo_output_stack_id_seq; Type: SEQUENCE OWNED BY; Schema: workflow_runtime; Owner: postgres
--

ALTER SEQUENCE workflow_runtime.lo_output_stack_id_seq OWNED BY workflow_runtime.lo_output_stack.id;


--
-- Name: lo_output_triggers; Type: TABLE; Schema: workflow_runtime; Owner: postgres
--

CREATE TABLE workflow_runtime.lo_output_triggers (
    id character varying(50) NOT NULL,
    output_item_id character varying(50) NOT NULL,
    output_stack_id integer NOT NULL,
    target_objective character varying(100) NOT NULL,
    target_input character varying(100) NOT NULL,
    mapping_type character varying(50) NOT NULL,
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE workflow_runtime.lo_output_triggers OWNER TO postgres;

--
-- Name: lo_system_functions; Type: TABLE; Schema: workflow_runtime; Owner: postgres
--

CREATE TABLE workflow_runtime.lo_system_functions (
    function_id character varying(50) NOT NULL,
    lo_id character varying(50) NOT NULL,
    function_name character varying(100) NOT NULL,
    function_type character varying(50) NOT NULL,
    parameters jsonb,
    output_to character varying(100),
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE workflow_runtime.lo_system_functions OWNER TO postgres;

--
-- Name: local_objectives; Type: TABLE; Schema: workflow_runtime; Owner: postgres
--

CREATE TABLE workflow_runtime.local_objectives (
    lo_id character varying(50) NOT NULL,
    contextual_id character varying(100) NOT NULL,
    name character varying(100) NOT NULL,
    function_type character varying(50) NOT NULL,
    workflow_source character varying(50) NOT NULL,
    go_id character varying(50) NOT NULL,
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    system_function character varying(100) DEFAULT NULL::character varying
);


ALTER TABLE workflow_runtime.local_objectives OWNER TO postgres;

--
-- Name: mapping_rules; Type: TABLE; Schema: workflow_runtime; Owner: postgres
--

CREATE TABLE workflow_runtime.mapping_rules (
    id character varying(50) NOT NULL,
    mapping_stack_id integer NOT NULL,
    description text,
    condition_type character varying(50),
    condition_entity character varying(50),
    condition_attribute character varying(50),
    condition_operator character varying(20),
    condition_value text,
    error_message text,
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE workflow_runtime.mapping_rules OWNER TO postgres;

--
-- Name: metrics_aggregation; Type: TABLE; Schema: workflow_runtime; Owner: postgres
--

CREATE TABLE workflow_runtime.metrics_aggregation (
    id integer NOT NULL,
    metrics_stack_id integer NOT NULL,
    attribute_name character varying(100) NOT NULL,
    aggregation_function character varying(100) NOT NULL,
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE workflow_runtime.metrics_aggregation OWNER TO postgres;

--
-- Name: metrics_aggregation_id_seq; Type: SEQUENCE; Schema: workflow_runtime; Owner: postgres
--

CREATE SEQUENCE workflow_runtime.metrics_aggregation_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE workflow_runtime.metrics_aggregation_id_seq OWNER TO postgres;

--
-- Name: metrics_aggregation_id_seq; Type: SEQUENCE OWNED BY; Schema: workflow_runtime; Owner: postgres
--

ALTER SEQUENCE workflow_runtime.metrics_aggregation_id_seq OWNED BY workflow_runtime.metrics_aggregation.id;


--
-- Name: metrics_reporting; Type: TABLE; Schema: workflow_runtime; Owner: postgres
--

CREATE TABLE workflow_runtime.metrics_reporting (
    id integer NOT NULL,
    metrics_stack_id integer NOT NULL,
    generate_execution_summary boolean DEFAULT true,
    store_summary_location character varying(255),
    error_count_threshold integer,
    duration_percentile integer,
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE workflow_runtime.metrics_reporting OWNER TO postgres;

--
-- Name: metrics_reporting_id_seq; Type: SEQUENCE; Schema: workflow_runtime; Owner: postgres
--

CREATE SEQUENCE workflow_runtime.metrics_reporting_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE workflow_runtime.metrics_reporting_id_seq OWNER TO postgres;

--
-- Name: metrics_reporting_id_seq; Type: SEQUENCE OWNED BY; Schema: workflow_runtime; Owner: postgres
--

ALTER SEQUENCE workflow_runtime.metrics_reporting_id_seq OWNED BY workflow_runtime.metrics_reporting.id;


--
-- Name: objective_permissions; Type: TABLE; Schema: workflow_runtime; Owner: postgres
--

CREATE TABLE workflow_runtime.objective_permissions (
    id integer NOT NULL,
    role_id character varying(50) NOT NULL,
    objective_id character varying(50) NOT NULL,
    permission_id character varying(50) NOT NULL,
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE workflow_runtime.objective_permissions OWNER TO postgres;

--
-- Name: objective_permissions_id_seq; Type: SEQUENCE; Schema: workflow_runtime; Owner: postgres
--

CREATE SEQUENCE workflow_runtime.objective_permissions_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE workflow_runtime.objective_permissions_id_seq OWNER TO postgres;

--
-- Name: objective_permissions_id_seq; Type: SEQUENCE OWNED BY; Schema: workflow_runtime; Owner: postgres
--

ALTER SEQUENCE workflow_runtime.objective_permissions_id_seq OWNED BY workflow_runtime.objective_permissions.id;


--
-- Name: organizational_units; Type: TABLE; Schema: workflow_runtime; Owner: postgres
--

CREATE TABLE workflow_runtime.organizational_units (
    org_unit_id character varying(50) NOT NULL,
    name character varying(100) NOT NULL,
    parent_org_unit_id character varying(50),
    tenant_id character varying(50) NOT NULL,
    description text,
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE workflow_runtime.organizational_units OWNER TO postgres;

--
-- Name: output_items; Type: TABLE; Schema: workflow_runtime; Owner: postgres
--

CREATE TABLE workflow_runtime.output_items (
    id character varying(50) NOT NULL,
    output_stack_id integer NOT NULL,
    slot_id character varying(50) NOT NULL,
    contextual_id character varying(50) NOT NULL,
    output_entity character varying(50),
    output_attribute character varying(50),
    data_type character varying(50) NOT NULL,
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE workflow_runtime.output_items OWNER TO postgres;

--
-- Name: output_stack; Type: TABLE; Schema: workflow_runtime; Owner: postgres
--

CREATE TABLE workflow_runtime.output_stack (
    id integer NOT NULL,
    go_id character varying(50) NOT NULL,
    description text,
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE workflow_runtime.output_stack OWNER TO postgres;

--
-- Name: output_stack_id_seq; Type: SEQUENCE; Schema: workflow_runtime; Owner: postgres
--

CREATE SEQUENCE workflow_runtime.output_stack_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE workflow_runtime.output_stack_id_seq OWNER TO postgres;

--
-- Name: output_stack_id_seq; Type: SEQUENCE OWNED BY; Schema: workflow_runtime; Owner: postgres
--

ALTER SEQUENCE workflow_runtime.output_stack_id_seq OWNED BY workflow_runtime.output_stack.id;


--
-- Name: output_triggers; Type: TABLE; Schema: workflow_runtime; Owner: postgres
--

CREATE TABLE workflow_runtime.output_triggers (
    id character varying(50) NOT NULL,
    output_item_id character varying(50) NOT NULL,
    output_stack_id integer NOT NULL,
    target_objective character varying(50) NOT NULL,
    target_input character varying(50) NOT NULL,
    mapping_type character varying(50) NOT NULL,
    condition_type character varying(50),
    condition_entity character varying(50),
    condition_attribute character varying(50),
    condition_operator character varying(20),
    condition_value text,
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE workflow_runtime.output_triggers OWNER TO postgres;

--
-- Name: permission_capabilities; Type: TABLE; Schema: workflow_runtime; Owner: postgres
--

CREATE TABLE workflow_runtime.permission_capabilities (
    capability_id character varying(50) NOT NULL,
    permission_id character varying(50) NOT NULL
);


ALTER TABLE workflow_runtime.permission_capabilities OWNER TO postgres;

--
-- Name: permission_contexts; Type: TABLE; Schema: workflow_runtime; Owner: postgres
--

CREATE TABLE workflow_runtime.permission_contexts (
    context_id character varying(50) NOT NULL,
    name character varying(100) NOT NULL,
    description text,
    context_type character varying(50) NOT NULL,
    context_rules jsonb DEFAULT '{}'::jsonb,
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT permission_contexts_context_type_check CHECK (((context_type)::text = ANY ((ARRAY['global'::character varying, 'entity'::character varying, 'objective'::character varying, 'function'::character varying])::text[])))
);


ALTER TABLE workflow_runtime.permission_contexts OWNER TO postgres;

--
-- Name: permission_types; Type: TABLE; Schema: workflow_runtime; Owner: postgres
--

CREATE TABLE workflow_runtime.permission_types (
    permission_id character varying(50) NOT NULL,
    description text,
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE workflow_runtime.permission_types OWNER TO postgres;

--
-- Name: role; Type: TABLE; Schema: workflow_runtime; Owner: postgres
--

CREATE TABLE workflow_runtime.role (
    id integer NOT NULL,
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    created_by character varying(255),
    updated_by character varying(255),
    role_id character varying(255),
    name character varying(255),
    description character varying(255),
    inherits_from character varying(255),
    tenant_id character varying(255)
);


ALTER TABLE workflow_runtime.role OWNER TO postgres;

--
-- Name: role_id_seq; Type: SEQUENCE; Schema: workflow_runtime; Owner: postgres
--

CREATE SEQUENCE workflow_runtime.role_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE workflow_runtime.role_id_seq OWNER TO postgres;

--
-- Name: role_id_seq; Type: SEQUENCE OWNED BY; Schema: workflow_runtime; Owner: postgres
--

ALTER SEQUENCE workflow_runtime.role_id_seq OWNED BY workflow_runtime.role.id;


--
-- Name: role_permissions; Type: TABLE; Schema: workflow_runtime; Owner: postgres
--

CREATE TABLE workflow_runtime.role_permissions (
    id integer NOT NULL,
    role_id character varying(50) NOT NULL,
    context_id character varying(50) NOT NULL,
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE workflow_runtime.role_permissions OWNER TO postgres;

--
-- Name: role_permissions_id_seq; Type: SEQUENCE; Schema: workflow_runtime; Owner: postgres
--

CREATE SEQUENCE workflow_runtime.role_permissions_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE workflow_runtime.role_permissions_id_seq OWNER TO postgres;

--
-- Name: role_permissions_id_seq; Type: SEQUENCE OWNED BY; Schema: workflow_runtime; Owner: postgres
--

ALTER SEQUENCE workflow_runtime.role_permissions_id_seq OWNED BY workflow_runtime.role_permissions.id;


--
-- Name: roles; Type: TABLE; Schema: workflow_runtime; Owner: postgres
--

CREATE TABLE workflow_runtime.roles (
    role_id character varying(50) NOT NULL,
    name character varying(100) NOT NULL,
    tenant_id character varying(50) NOT NULL,
    inherits_from character varying(50),
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE workflow_runtime.roles OWNER TO postgres;

--
-- Name: runtime_metrics_stack; Type: TABLE; Schema: workflow_runtime; Owner: postgres
--

CREATE TABLE workflow_runtime.runtime_metrics_stack (
    id integer NOT NULL,
    go_id character varying(50) NOT NULL,
    description text,
    metrics_entity character varying(50) NOT NULL,
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE workflow_runtime.runtime_metrics_stack OWNER TO postgres;

--
-- Name: runtime_metrics_stack_id_seq; Type: SEQUENCE; Schema: workflow_runtime; Owner: postgres
--

CREATE SEQUENCE workflow_runtime.runtime_metrics_stack_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE workflow_runtime.runtime_metrics_stack_id_seq OWNER TO postgres;

--
-- Name: runtime_metrics_stack_id_seq; Type: SEQUENCE OWNED BY; Schema: workflow_runtime; Owner: postgres
--

ALTER SEQUENCE workflow_runtime.runtime_metrics_stack_id_seq OWNED BY workflow_runtime.runtime_metrics_stack.id;


--
-- Name: success_messages; Type: TABLE; Schema: workflow_runtime; Owner: postgres
--

CREATE TABLE workflow_runtime.success_messages (
    lo_id character varying(50) NOT NULL,
    message text NOT NULL,
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE workflow_runtime.success_messages OWNER TO postgres;

--
-- Name: system_functions; Type: TABLE; Schema: workflow_runtime; Owner: postgres
--

CREATE TABLE workflow_runtime.system_functions (
    function_id character varying(50) NOT NULL,
    function_name character varying(100) NOT NULL,
    function_type character varying(50) NOT NULL,
    stack_type character varying(50) NOT NULL,
    stack_id integer NOT NULL,
    parameters jsonb,
    output_to character varying(100),
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE workflow_runtime.system_functions OWNER TO postgres;

--
-- Name: team_availability; Type: TABLE; Schema: workflow_runtime; Owner: postgres
--

CREATE TABLE workflow_runtime.team_availability (
    id integer NOT NULL,
    department character varying(255),
    designation character varying(255),
    date character varying(255),
    total_employees integer,
    available_employees integer,
    on_leave_employees integer
);


ALTER TABLE workflow_runtime.team_availability OWNER TO postgres;

--
-- Name: team_availability_id_seq; Type: SEQUENCE; Schema: workflow_runtime; Owner: postgres
--

CREATE SEQUENCE workflow_runtime.team_availability_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE workflow_runtime.team_availability_id_seq OWNER TO postgres;

--
-- Name: team_availability_id_seq; Type: SEQUENCE OWNED BY; Schema: workflow_runtime; Owner: postgres
--

ALTER SEQUENCE workflow_runtime.team_availability_id_seq OWNED BY workflow_runtime.team_availability.id;


--
-- Name: tenants; Type: TABLE; Schema: workflow_runtime; Owner: postgres
--

CREATE TABLE workflow_runtime.tenants (
    tenant_id character varying(50) NOT NULL,
    name character varying(100) NOT NULL,
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE workflow_runtime.tenants OWNER TO postgres;

--
-- Name: terminal_pathways; Type: TABLE; Schema: workflow_runtime; Owner: postgres
--

CREATE TABLE workflow_runtime.terminal_pathways (
    lo_id character varying(50) NOT NULL,
    terminal_type character varying(50) NOT NULL,
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE workflow_runtime.terminal_pathways OWNER TO postgres;

--
-- Name: ui_elements; Type: TABLE; Schema: workflow_runtime; Owner: postgres
--

CREATE TABLE workflow_runtime.ui_elements (
    id integer NOT NULL,
    ui_stack_id integer NOT NULL,
    entity_attribute character varying(100) NOT NULL,
    ui_control character varying(50) NOT NULL,
    helper_text text,
    format_as character varying(50),
    options jsonb,
    display_properties jsonb,
    enable_condition text,
    visibility_condition text,
    required_condition text,
    display_text jsonb,
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE workflow_runtime.ui_elements OWNER TO postgres;

--
-- Name: ui_elements_id_seq; Type: SEQUENCE; Schema: workflow_runtime; Owner: postgres
--

CREATE SEQUENCE workflow_runtime.ui_elements_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE workflow_runtime.ui_elements_id_seq OWNER TO postgres;

--
-- Name: ui_elements_id_seq; Type: SEQUENCE OWNED BY; Schema: workflow_runtime; Owner: postgres
--

ALTER SEQUENCE workflow_runtime.ui_elements_id_seq OWNED BY workflow_runtime.ui_elements.id;


--
-- Name: ui_stack; Type: TABLE; Schema: workflow_runtime; Owner: postgres
--

CREATE TABLE workflow_runtime.ui_stack (
    id integer NOT NULL,
    lo_id character varying(50) NOT NULL,
    type character varying(50) NOT NULL,
    status character varying(50) NOT NULL,
    description text,
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE workflow_runtime.ui_stack OWNER TO postgres;

--
-- Name: ui_stack_id_seq; Type: SEQUENCE; Schema: workflow_runtime; Owner: postgres
--

CREATE SEQUENCE workflow_runtime.ui_stack_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE workflow_runtime.ui_stack_id_seq OWNER TO postgres;

--
-- Name: ui_stack_id_seq; Type: SEQUENCE OWNED BY; Schema: workflow_runtime; Owner: postgres
--

ALTER SEQUENCE workflow_runtime.ui_stack_id_seq OWNED BY workflow_runtime.ui_stack.id;


--
-- Name: user; Type: TABLE; Schema: workflow_runtime; Owner: postgres
--

CREATE TABLE workflow_runtime."user" (
    id integer NOT NULL,
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    created_by character varying(255),
    updated_by character varying(255),
    user_id character varying(255),
    username character varying(255),
    email character varying(255),
    first_name character varying(255),
    last_name character varying(255),
    status character varying(50),
    password_hash character varying(255),
    disabled boolean,
    organization character varying(255),
    team character varying(255)
);


ALTER TABLE workflow_runtime."user" OWNER TO postgres;

--
-- Name: user_id_seq; Type: SEQUENCE; Schema: workflow_runtime; Owner: postgres
--

CREATE SEQUENCE workflow_runtime.user_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE workflow_runtime.user_id_seq OWNER TO postgres;

--
-- Name: user_id_seq; Type: SEQUENCE OWNED BY; Schema: workflow_runtime; Owner: postgres
--

ALTER SEQUENCE workflow_runtime.user_id_seq OWNED BY workflow_runtime."user".id;


--
-- Name: user_oauth_tokens; Type: TABLE; Schema: workflow_runtime; Owner: postgres
--

CREATE TABLE workflow_runtime.user_oauth_tokens (
    token_id character varying(50) NOT NULL,
    user_id character varying(50) NOT NULL,
    access_token character varying(1000) NOT NULL,
    refresh_token character varying(1000) NOT NULL,
    token_type character varying(20) DEFAULT 'Bearer'::character varying,
    expires_at timestamp without time zone NOT NULL,
    scope character varying(255),
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE workflow_runtime.user_oauth_tokens OWNER TO postgres;

--
-- Name: user_organizations; Type: TABLE; Schema: workflow_runtime; Owner: postgres
--

CREATE TABLE workflow_runtime.user_organizations (
    id integer NOT NULL,
    user_id character varying(50) NOT NULL,
    org_unit_id character varying(50) NOT NULL,
    is_primary boolean DEFAULT false,
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE workflow_runtime.user_organizations OWNER TO postgres;

--
-- Name: user_organizations_id_seq; Type: SEQUENCE; Schema: workflow_runtime; Owner: postgres
--

CREATE SEQUENCE workflow_runtime.user_organizations_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE workflow_runtime.user_organizations_id_seq OWNER TO postgres;

--
-- Name: user_organizations_id_seq; Type: SEQUENCE OWNED BY; Schema: workflow_runtime; Owner: postgres
--

ALTER SEQUENCE workflow_runtime.user_organizations_id_seq OWNED BY workflow_runtime.user_organizations.id;


--
-- Name: user_roles; Type: TABLE; Schema: workflow_runtime; Owner: postgres
--

CREATE TABLE workflow_runtime.user_roles (
    user_id character varying(50) NOT NULL,
    username character varying(100) NOT NULL,
    role character varying(50) NOT NULL,
    tenant_id character varying(50)
);


ALTER TABLE workflow_runtime.user_roles OWNER TO postgres;

--
-- Name: user_sessions; Type: TABLE; Schema: workflow_runtime; Owner: postgres
--

CREATE TABLE workflow_runtime.user_sessions (
    session_id character varying(50) NOT NULL,
    user_id character varying(50) NOT NULL,
    token character varying(1000) NOT NULL,
    ip_address character varying(50),
    user_agent text,
    expires_at timestamp without time zone NOT NULL,
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE workflow_runtime.user_sessions OWNER TO postgres;

--
-- Name: users; Type: TABLE; Schema: workflow_runtime; Owner: postgres
--

CREATE TABLE workflow_runtime.users (
    user_id character varying(50) NOT NULL,
    username character varying(100) NOT NULL,
    email character varying(255) NOT NULL,
    password_hash character varying(255) NOT NULL,
    first_name character varying(100),
    last_name character varying(100),
    status character varying(20) DEFAULT 'active'::character varying NOT NULL,
    last_login timestamp without time zone,
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT users_status_check CHECK (((status)::text = ANY ((ARRAY['active'::character varying, 'inactive'::character varying, 'locked'::character varying, 'pending'::character varying])::text[])))
);


ALTER TABLE workflow_runtime.users OWNER TO postgres;

--
-- Name: workflow_instances; Type: TABLE; Schema: workflow_runtime; Owner: postgres
--

CREATE TABLE workflow_runtime.workflow_instances (
    instance_id uuid NOT NULL,
    go_id character varying(50) NOT NULL,
    tenant_id character varying(50) NOT NULL,
    status character varying(20) NOT NULL,
    started_by character varying(50) NOT NULL,
    started_at timestamp without time zone DEFAULT now(),
    current_lo_id character varying(50),
    instance_data jsonb DEFAULT '{}'::jsonb,
    is_test boolean DEFAULT false,
    version character varying(10) DEFAULT '1.0'::character varying,
    updated_at timestamp without time zone DEFAULT now()
);


ALTER TABLE workflow_runtime.workflow_instances OWNER TO postgres;

--
-- Name: workflow_results; Type: TABLE; Schema: workflow_runtime; Owner: postgres
--

CREATE TABLE workflow_runtime.workflow_results (
    id integer NOT NULL,
    leaves_pending_approval integer,
    leaves_approved_count integer,
    leaves_rejected_count integer,
    duration integer,
    start_time character varying(255),
    end_time character varying(255),
    status character varying(255)
);


ALTER TABLE workflow_runtime.workflow_results OWNER TO postgres;

--
-- Name: workflow_results_id_seq; Type: SEQUENCE; Schema: workflow_runtime; Owner: postgres
--

CREATE SEQUENCE workflow_runtime.workflow_results_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE workflow_runtime.workflow_results_id_seq OWNER TO postgres;

--
-- Name: workflow_results_id_seq; Type: SEQUENCE OWNED BY; Schema: workflow_runtime; Owner: postgres
--

ALTER SEQUENCE workflow_runtime.workflow_results_id_seq OWNED BY workflow_runtime.workflow_results.id;


--
-- Name: workflow_transaction; Type: TABLE; Schema: workflow_runtime; Owner: postgres
--

CREATE TABLE workflow_runtime.workflow_transaction (
    id integer NOT NULL,
    workflow_instance_id character varying(100),
    go_id character varying(50),
    lo_id character varying(50),
    status character varying(50),
    input_stack json,
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    tenant_id character varying(50),
    tenant_name character varying(100),
    user_id character varying(50),
    output_stack json
);


ALTER TABLE workflow_runtime.workflow_transaction OWNER TO postgres;

--
-- Name: workflow_transaction_id_seq; Type: SEQUENCE; Schema: workflow_runtime; Owner: postgres
--

CREATE SEQUENCE workflow_runtime.workflow_transaction_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE workflow_runtime.workflow_transaction_id_seq OWNER TO postgres;

--
-- Name: workflow_transaction_id_seq; Type: SEQUENCE OWNED BY; Schema: workflow_runtime; Owner: postgres
--

ALTER SEQUENCE workflow_runtime.workflow_transaction_id_seq OWNED BY workflow_runtime.workflow_transaction.id;


--
-- Name: agent_rights id; Type: DEFAULT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.agent_rights ALTER COLUMN id SET DEFAULT nextval('workflow_runtime.agent_rights_id_seq'::regclass);


--
-- Name: agent_stack id; Type: DEFAULT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.agent_stack ALTER COLUMN id SET DEFAULT nextval('workflow_runtime.agent_stack_id_seq'::regclass);


--
-- Name: asset_request id; Type: DEFAULT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.asset_request ALTER COLUMN id SET DEFAULT nextval('workflow_runtime.asset_request_id_seq'::regclass);


--
-- Name: attribute_enum_values id; Type: DEFAULT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.attribute_enum_values ALTER COLUMN id SET DEFAULT nextval('workflow_runtime.attribute_enum_values_id_seq'::regclass);


--
-- Name: conditional_success_messages id; Type: DEFAULT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.conditional_success_messages ALTER COLUMN id SET DEFAULT nextval('workflow_runtime.conditional_success_messages_id_seq'::regclass);


--
-- Name: data_mapping_stack id; Type: DEFAULT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.data_mapping_stack ALTER COLUMN id SET DEFAULT nextval('workflow_runtime.data_mapping_stack_id_seq'::regclass);


--
-- Name: department id; Type: DEFAULT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.department ALTER COLUMN id SET DEFAULT nextval('workflow_runtime.department_id_seq'::regclass);


--
-- Name: dropdown_data_sources id; Type: DEFAULT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.dropdown_data_sources ALTER COLUMN id SET DEFAULT nextval('workflow_runtime.dropdown_data_sources_id_seq'::regclass);


--
-- Name: entity_permissions id; Type: DEFAULT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.entity_permissions ALTER COLUMN id SET DEFAULT nextval('workflow_runtime.entity_permissions_id_seq'::regclass);


--
-- Name: entity_relationships id; Type: DEFAULT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.entity_relationships ALTER COLUMN id SET DEFAULT nextval('workflow_runtime.entity_relationships_id_seq'::regclass);


--
-- Name: execution_path_tracking id; Type: DEFAULT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.execution_path_tracking ALTER COLUMN id SET DEFAULT nextval('workflow_runtime.execution_path_tracking_id_seq'::regclass);


--
-- Name: execution_pathway_conditions id; Type: DEFAULT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.execution_pathway_conditions ALTER COLUMN id SET DEFAULT nextval('workflow_runtime.execution_pathway_conditions_id_seq'::regclass);


--
-- Name: execution_pathways id; Type: DEFAULT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.execution_pathways ALTER COLUMN id SET DEFAULT nextval('workflow_runtime.execution_pathways_id_seq'::regclass);


--
-- Name: input_data_sources id; Type: DEFAULT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.input_data_sources ALTER COLUMN id SET DEFAULT nextval('workflow_runtime.input_data_sources_id_seq'::regclass);


--
-- Name: input_dependencies id; Type: DEFAULT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.input_dependencies ALTER COLUMN id SET DEFAULT nextval('workflow_runtime.input_dependencies_id_seq'::regclass);


--
-- Name: input_stack id; Type: DEFAULT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.input_stack ALTER COLUMN id SET DEFAULT nextval('workflow_runtime.input_stack_id_seq'::regclass);


--
-- Name: leave_application id; Type: DEFAULT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.leave_application ALTER COLUMN id SET DEFAULT nextval('workflow_runtime.leave_application_id_seq'::regclass);


--
-- Name: leave_sub_type id; Type: DEFAULT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.leave_sub_type ALTER COLUMN id SET DEFAULT nextval('workflow_runtime.leave_sub_type_id_seq'::regclass);


--
-- Name: lo_data_mapping_stack id; Type: DEFAULT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.lo_data_mapping_stack ALTER COLUMN id SET DEFAULT nextval('workflow_runtime.lo_data_mapping_stack_id_seq'::regclass);


--
-- Name: lo_input_execution id; Type: DEFAULT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.lo_input_execution ALTER COLUMN id SET DEFAULT nextval('workflow_runtime.lo_input_execution_id_seq'::regclass);


--
-- Name: lo_input_stack id; Type: DEFAULT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.lo_input_stack ALTER COLUMN id SET DEFAULT nextval('workflow_runtime.lo_input_stack_id_seq'::regclass);


--
-- Name: lo_input_validations id; Type: DEFAULT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.lo_input_validations ALTER COLUMN id SET DEFAULT nextval('workflow_runtime.lo_input_validations_id_seq'::regclass);


--
-- Name: lo_nested_functions id; Type: DEFAULT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.lo_nested_functions ALTER COLUMN id SET DEFAULT nextval('workflow_runtime.lo_nested_functions_id_seq'::regclass);


--
-- Name: lo_output_execution id; Type: DEFAULT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.lo_output_execution ALTER COLUMN id SET DEFAULT nextval('workflow_runtime.lo_output_execution_id_seq'::regclass);


--
-- Name: lo_output_stack id; Type: DEFAULT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.lo_output_stack ALTER COLUMN id SET DEFAULT nextval('workflow_runtime.lo_output_stack_id_seq'::regclass);


--
-- Name: metrics_aggregation id; Type: DEFAULT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.metrics_aggregation ALTER COLUMN id SET DEFAULT nextval('workflow_runtime.metrics_aggregation_id_seq'::regclass);


--
-- Name: metrics_reporting id; Type: DEFAULT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.metrics_reporting ALTER COLUMN id SET DEFAULT nextval('workflow_runtime.metrics_reporting_id_seq'::regclass);


--
-- Name: objective_permissions id; Type: DEFAULT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.objective_permissions ALTER COLUMN id SET DEFAULT nextval('workflow_runtime.objective_permissions_id_seq'::regclass);


--
-- Name: output_stack id; Type: DEFAULT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.output_stack ALTER COLUMN id SET DEFAULT nextval('workflow_runtime.output_stack_id_seq'::regclass);


--
-- Name: role id; Type: DEFAULT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.role ALTER COLUMN id SET DEFAULT nextval('workflow_runtime.role_id_seq'::regclass);


--
-- Name: role_permissions id; Type: DEFAULT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.role_permissions ALTER COLUMN id SET DEFAULT nextval('workflow_runtime.role_permissions_id_seq'::regclass);


--
-- Name: runtime_metrics_stack id; Type: DEFAULT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.runtime_metrics_stack ALTER COLUMN id SET DEFAULT nextval('workflow_runtime.runtime_metrics_stack_id_seq'::regclass);


--
-- Name: team_availability id; Type: DEFAULT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.team_availability ALTER COLUMN id SET DEFAULT nextval('workflow_runtime.team_availability_id_seq'::regclass);


--
-- Name: ui_elements id; Type: DEFAULT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.ui_elements ALTER COLUMN id SET DEFAULT nextval('workflow_runtime.ui_elements_id_seq'::regclass);


--
-- Name: ui_stack id; Type: DEFAULT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.ui_stack ALTER COLUMN id SET DEFAULT nextval('workflow_runtime.ui_stack_id_seq'::regclass);


--
-- Name: user id; Type: DEFAULT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime."user" ALTER COLUMN id SET DEFAULT nextval('workflow_runtime.user_id_seq'::regclass);


--
-- Name: user_organizations id; Type: DEFAULT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.user_organizations ALTER COLUMN id SET DEFAULT nextval('workflow_runtime.user_organizations_id_seq'::regclass);


--
-- Name: workflow_results id; Type: DEFAULT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.workflow_results ALTER COLUMN id SET DEFAULT nextval('workflow_runtime.workflow_results_id_seq'::regclass);


--
-- Name: workflow_transaction id; Type: DEFAULT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.workflow_transaction ALTER COLUMN id SET DEFAULT nextval('workflow_runtime.workflow_transaction_id_seq'::regclass);


--
-- Data for Name: agent_rights; Type: TABLE DATA; Schema: workflow_runtime; Owner: postgres
--

COPY workflow_runtime.agent_rights (id, agent_stack_id, role_id, right_id, created_at, updated_at) FROM stdin;
3121	2375	r001	execute	2025-05-04 11:31:09.548741	2025-05-04 11:31:09.548741
3122	2376	r002	execute	2025-05-04 11:31:09.548741	2025-05-04 11:31:09.548741
3123	2376	r002	update	2025-05-04 11:31:09.548741	2025-05-04 11:31:09.548741
3124	2376	r003	read	2025-05-04 11:31:09.548741	2025-05-04 11:31:09.548741
3125	2377	r003	execute	2025-05-04 11:31:09.548741	2025-05-04 11:31:09.548741
3126	2377	r003	update	2025-05-04 11:31:09.548741	2025-05-04 11:31:09.548741
3127	2377		read	2025-05-04 11:31:09.548741	2025-05-04 11:31:09.548741
\.


--
-- Data for Name: agent_stack; Type: TABLE DATA; Schema: workflow_runtime; Owner: postgres
--

COPY workflow_runtime.agent_stack (id, lo_id, created_at, updated_at) FROM stdin;
2375	lo001	2025-05-04 11:31:09.548741	2025-05-04 11:31:09.548741
2376	lo002	2025-05-04 11:31:09.548741	2025-05-04 11:31:09.548741
2377	lo003	2025-05-04 11:31:09.548741	2025-05-04 11:31:09.548741
\.


--
-- Data for Name: alembic_version; Type: TABLE DATA; Schema: workflow_runtime; Owner: postgres
--

COPY workflow_runtime.alembic_version (version_num) FROM stdin;
\.


--
-- Data for Name: asset_request; Type: TABLE DATA; Schema: workflow_runtime; Owner: postgres
--

COPY workflow_runtime.asset_request (id, created_at, updated_at, created_by, updated_by, request_id, employee_id, asset_type, specifications, request_date, approval_status, approval_date, approval_remarks) FROM stdin;
\.


--
-- Data for Name: attribute_enum_values; Type: TABLE DATA; Schema: workflow_runtime; Owner: postgres
--

COPY workflow_runtime.attribute_enum_values (id, attribute_id, value, created_at, updated_at) FROM stdin;
1730	at007	pending	2025-05-04 11:31:09.548741	2025-05-04 11:31:09.548741
1731	at007	approved	2025-05-04 11:31:09.548741	2025-05-04 11:31:09.548741
1732	at007	rejected	2025-05-04 11:31:09.548741	2025-05-04 11:31:09.548741
1733	at010	annual leave	2025-05-04 11:31:09.548741	2025-05-04 11:31:09.548741
1734	at010	sick leave	2025-05-04 11:31:09.548741	2025-05-04 11:31:09.548741
1735	at010	parental leave	2025-05-04 11:31:09.548741	2025-05-04 11:31:09.548741
1736	at010	bereavement	2025-05-04 11:31:09.548741	2025-05-04 11:31:09.548741
1737	at021	active	2025-05-04 11:31:09.548741	2025-05-04 11:31:09.548741
1738	at021	inactive	2025-05-04 11:31:09.548741	2025-05-04 11:31:09.548741
1739	at021	suspended	2025-05-04 11:31:09.548741	2025-05-04 11:31:09.548741
\.


--
-- Data for Name: attribute_ui_controls; Type: TABLE DATA; Schema: workflow_runtime; Owner: postgres
--

COPY workflow_runtime.attribute_ui_controls (entity_id, attribute_id, ui_control, created_at, updated_at) FROM stdin;
\.


--
-- Data for Name: attribute_validations; Type: TABLE DATA; Schema: workflow_runtime; Owner: postgres
--

COPY workflow_runtime.attribute_validations (id, attribute_id, rule, expression, created_at, updated_at) FROM stdin;
\.


--
-- Data for Name: conditional_success_messages; Type: TABLE DATA; Schema: workflow_runtime; Owner: postgres
--

COPY workflow_runtime.conditional_success_messages (id, lo_id, condition_expression, message, is_default, created_at, updated_at) FROM stdin;
\.


--
-- Data for Name: data_mapping_stack; Type: TABLE DATA; Schema: workflow_runtime; Owner: postgres
--

COPY workflow_runtime.data_mapping_stack (id, go_id, description, created_at, updated_at) FROM stdin;
1073	go001	Data handover between GOs	2025-05-04 11:31:09.548741	2025-05-04 11:31:09.548741
\.


--
-- Data for Name: data_mappings; Type: TABLE DATA; Schema: workflow_runtime; Owner: postgres
--

COPY workflow_runtime.data_mappings (id, mapping_stack_id, source, target, mapping_type, created_at, updated_at) FROM stdin;
\.


--
-- Data for Name: department; Type: TABLE DATA; Schema: workflow_runtime; Owner: postgres
--

COPY workflow_runtime.department (id, created_at, updated_at, created_by, updated_by, departmentid, departmentname, managerid, active) FROM stdin;
\.


--
-- Data for Name: dropdown_data_sources; Type: TABLE DATA; Schema: workflow_runtime; Owner: postgres
--

COPY workflow_runtime.dropdown_data_sources (id, input_item_id, input_stack_id, source_type, query_text, function_name, function_params, value_field, display_field, depends_on_fields, created_at, updated_at) FROM stdin;
84	in025	1917	function	\N	fetch_enum_values	{"entity_id": "e001", "attribute_id": "at010"}	value	display	\N	2025-05-04 11:31:09.548741	2025-05-04 11:31:09.548741
85	in026	1917	function	\N	fetch_filtered_records	{"table": "workflow_runtime.leave_sub_type", "filter_value": "${in025}", "value_column": "sub_type_id", "filter_column": "leave_type", "display_column": "sub_type_name", "additional_filters": {"active": true}}	value	display	["in025"]	2025-05-04 11:31:09.548741	2025-05-04 11:31:09.548741
\.


--
-- Data for Name: entities; Type: TABLE DATA; Schema: workflow_runtime; Owner: postgres
--

COPY workflow_runtime.entities (entity_id, name, version, status, type, attribute_prefix, created_at, updated_at, description) FROM stdin;
e001	LeaveApplication	1.0	Active	Master	at	2025-05-04 11:31:09.548741	2025-05-04 11:31:09.548741	
e002	LeaveSubType	1.0	Active	Master	at	2025-05-04 11:31:09.548741	2025-05-04 11:31:09.548741	
e003	User	1.0	Active	Master	at	2025-05-04 11:31:09.548741	2025-05-04 11:31:09.548741	
e004	Role	1.0	Active	Master	at	2025-05-04 11:31:09.548741	2025-05-04 11:31:09.548741	
\.


--
-- Data for Name: entity_attribute_metadata; Type: TABLE DATA; Schema: workflow_runtime; Owner: postgres
--

COPY workflow_runtime.entity_attribute_metadata (entity_id, attribute_id, attribute_name, required) FROM stdin;
e001	at001	leaveID	f
e001	at002	employeeID	f
e001	at003	startDate	f
e001	at004	endDate	f
e001	at005	numDays	f
e001	at006	reason	f
e001	at007	status	f
e001	at008	remarks	f
e001	at009	approvedBy	f
e001	at010	leaveType	f
e001	at011	leaveSubType	f
e002	at012	leaveType	f
e002	at013	subTypeId	f
e002	at014	subTypeName	f
e002	at015	active	f
e003	at016	user_id	f
e003	at017	username	f
e003	at018	email	f
e003	at019	first_name	f
e003	at020	last_name	f
e003	at021	status	f
e003	at022	password_hash	f
e003	at023	disabled	f
e003	at024	organization	f
e003	at025	team	f
e004	at026	role_id	f
e004	at027	name	f
e004	at028	description	f
e004	at029	inherits_from	f
e004	at030	tenant_id	f
\.


--
-- Data for Name: entity_attributes; Type: TABLE DATA; Schema: workflow_runtime; Owner: postgres
--

COPY workflow_runtime.entity_attributes (attribute_id, entity_id, name, display_name, datatype, version, status, required, reference_entity_id, created_at, updated_at) FROM stdin;
at001	e001	leaveID	Leave ID	String	1.0	deployed	t	\N	2025-05-04 11:31:09.548741	2025-05-04 11:31:09.548741
at002	e001	employeeID	Employee ID	String	1.0	deployed	t	\N	2025-05-04 11:31:09.548741	2025-05-04 11:31:09.548741
at003	e001	startDate	Start Date	Date	1.0	deployed	t	\N	2025-05-04 11:31:09.548741	2025-05-04 11:31:09.548741
at004	e001	endDate	End Date	Date	1.0	deployed	t	\N	2025-05-04 11:31:09.548741	2025-05-04 11:31:09.548741
at005	e001	numDays	Number of Days	Integer	1.0	deployed	t	\N	2025-05-04 11:31:09.548741	2025-05-04 11:31:09.548741
at006	e001	reason	Leave Reason	String	1.0	deployed	t	\N	2025-05-04 11:31:09.548741	2025-05-04 11:31:09.548741
at007	e001	status	Status	Enum	1.0	deployed	t	\N	2025-05-04 11:31:09.548741	2025-05-04 11:31:09.548741
at008	e001	remarks	Remarks	String	1.0	deployed	f	\N	2025-05-04 11:31:09.548741	2025-05-04 11:31:09.548741
at009	e001	approvedBy	Approved By	String	1.0	deployed	f	\N	2025-05-04 11:31:09.548741	2025-05-04 11:31:09.548741
at010	e001	leaveType	Leave Type	Enum	1.0	deployed	t	\N	2025-05-04 11:31:09.548741	2025-05-04 11:31:09.548741
at011	e001	leaveSubType	Leave Sub-Type	String	1.0	deployed	f	\N	2025-05-04 11:31:09.548741	2025-05-04 11:31:09.548741
at012	e002	leaveType	Leave Type	String	1.0	deployed	t	\N	2025-05-04 11:31:09.548741	2025-05-04 11:31:09.548741
at013	e002	subTypeId	Sub Type ID	String	1.0	deployed	t	\N	2025-05-04 11:31:09.548741	2025-05-04 11:31:09.548741
at014	e002	subTypeName	Sub Type Name	String	1.0	deployed	t	\N	2025-05-04 11:31:09.548741	2025-05-04 11:31:09.548741
at015	e002	active	Active	Boolean	1.0	deployed	f	\N	2025-05-04 11:31:09.548741	2025-05-04 11:31:09.548741
at016	e003	user_id	User ID	String	1.0	deployed	t	\N	2025-05-04 11:31:09.548741	2025-05-04 11:31:09.548741
at017	e003	username	Username	String	1.0	deployed	t	\N	2025-05-04 11:31:09.548741	2025-05-04 11:31:09.548741
at018	e003	email	Email	String	1.0	deployed	t	\N	2025-05-04 11:31:09.548741	2025-05-04 11:31:09.548741
at019	e003	first_name	First Name	String	1.0	deployed	f	\N	2025-05-04 11:31:09.548741	2025-05-04 11:31:09.548741
at020	e003	last_name	Last Name	String	1.0	deployed	f	\N	2025-05-04 11:31:09.548741	2025-05-04 11:31:09.548741
at021	e003	status	Status	Enum	1.0	deployed	t	\N	2025-05-04 11:31:09.548741	2025-05-04 11:31:09.548741
at022	e003	password_hash	Password Hash	String	1.0	deployed	f	\N	2025-05-04 11:31:09.548741	2025-05-04 11:31:09.548741
at023	e003	disabled	Disabled	Boolean	1.0	deployed	f	\N	2025-05-04 11:31:09.548741	2025-05-04 11:31:09.548741
at024	e003	organization	Organization	String	1.0	deployed	f	\N	2025-05-04 11:31:09.548741	2025-05-04 11:31:09.548741
at025	e003	team	Team	String	1.0	deployed	f	\N	2025-05-04 11:31:09.548741	2025-05-04 11:31:09.548741
at026	e004	role_id	Role ID	String	1.0	deployed	t	\N	2025-05-04 11:31:09.548741	2025-05-04 11:31:09.548741
at027	e004	name	Name	String	1.0	deployed	t	\N	2025-05-04 11:31:09.548741	2025-05-04 11:31:09.548741
at028	e004	description	Description	String	1.0	deployed	f	\N	2025-05-04 11:31:09.548741	2025-05-04 11:31:09.548741
at029	e004	inherits_from	Inherits From	String	1.0	deployed	f	\N	2025-05-04 11:31:09.548741	2025-05-04 11:31:09.548741
at030	e004	tenant_id	Tenant ID	String	1.0	deployed	f	\N	2025-05-04 11:31:09.548741	2025-05-04 11:31:09.548741
\.


--
-- Data for Name: entity_permissions; Type: TABLE DATA; Schema: workflow_runtime; Owner: postgres
--

COPY workflow_runtime.entity_permissions (id, role_id, entity_id, permission_id, created_at, updated_at) FROM stdin;
300	r001	e001	read	2025-05-04 11:31:09.548741	2025-05-04 11:31:09.548741
301	r001	e001	create	2025-05-04 11:31:09.548741	2025-05-04 11:31:09.548741
302	r001	e003	read	2025-05-04 11:31:09.548741	2025-05-04 11:31:09.548741
303	r001	e004	read	2025-05-04 11:31:09.548741	2025-05-04 11:31:09.548741
304	r002	e001	read	2025-05-04 11:31:09.548741	2025-05-04 11:31:09.548741
305	r002	e001	update	2025-05-04 11:31:09.548741	2025-05-04 11:31:09.548741
306	r002	e003	read	2025-05-04 11:31:09.548741	2025-05-04 11:31:09.548741
307	r002	e004	read	2025-05-04 11:31:09.548741	2025-05-04 11:31:09.548741
308	r003	e001	read	2025-05-04 11:31:09.548741	2025-05-04 11:31:09.548741
309	r003	e001	update	2025-05-04 11:31:09.548741	2025-05-04 11:31:09.548741
310	r003	e003	read	2025-05-04 11:31:09.548741	2025-05-04 11:31:09.548741
311	r003	e003	update	2025-05-04 11:31:09.548741	2025-05-04 11:31:09.548741
312	r003	e004	read	2025-05-04 11:31:09.548741	2025-05-04 11:31:09.548741
\.


--
-- Data for Name: entity_relationships; Type: TABLE DATA; Schema: workflow_runtime; Owner: postgres
--

COPY workflow_runtime.entity_relationships (id, source_entity_id, target_entity_id, relationship_type, source_attribute_id, target_attribute_id, created_at, updated_at) FROM stdin;
\.


--
-- Data for Name: execution_path_tracking; Type: TABLE DATA; Schema: workflow_runtime; Owner: postgres
--

COPY workflow_runtime.execution_path_tracking (id, metrics_stack_id, enabled, store_complete_path, identify_bottlenecks, compare_to_historical, path_efficiency_function, created_at, updated_at) FROM stdin;
\.


--
-- Data for Name: execution_pathway_conditions; Type: TABLE DATA; Schema: workflow_runtime; Owner: postgres
--

COPY workflow_runtime.execution_pathway_conditions (id, lo_id, condition_type, condition_entity, condition_attribute, condition_operator, condition_value, next_lo, created_at, updated_at) FROM stdin;
37	lo001	attribute_comparison	e001	at005	greater_than	3	lo003	2025-05-04 11:31:09.548741	2025-05-04 11:31:09.548741
38	lo001	attribute_comparison	e001	at005	less_than_or_equal	3	lo002	2025-05-04 11:31:09.548741	2025-05-04 11:31:09.548741
\.


--
-- Data for Name: execution_pathways; Type: TABLE DATA; Schema: workflow_runtime; Owner: postgres
--

COPY workflow_runtime.execution_pathways (lo_id, pathway_type, next_lo, created_at, updated_at, id) FROM stdin;
lo001	alternate	\N	2025-05-04 11:31:09.548741	2025-05-04 11:31:09.548741	71
lo002	terminal	\N	2025-05-04 11:31:09.548741	2025-05-04 11:31:09.548741	72
lo003	terminal	\N	2025-05-04 11:31:09.548741	2025-05-04 11:31:09.548741	73
\.


--
-- Data for Name: execution_rules; Type: TABLE DATA; Schema: workflow_runtime; Owner: postgres
--

COPY workflow_runtime.execution_rules (id, lo_id, contextual_id, description, structured_rule, created_at, updated_at) FROM stdin;
\.


--
-- Data for Name: global_objectives; Type: TABLE DATA; Schema: workflow_runtime; Owner: postgres
--

COPY workflow_runtime.global_objectives (go_id, name, version, status, description, created_at, updated_at, tenant_id, last_used, deleted_mark) FROM stdin;
go001	leave application workflow	1.0	active		2025-05-04 11:31:09.548741	2025-05-04 11:31:09.548741	t001	2025-05-04 12:29:00.654352	f
\.


--
-- Data for Name: input_data_sources; Type: TABLE DATA; Schema: workflow_runtime; Owner: postgres
--

COPY workflow_runtime.input_data_sources (id, input_item_id, input_stack_id, source_type, function_name, parameters, created_at, updated_at) FROM stdin;
\.


--
-- Data for Name: input_dependencies; Type: TABLE DATA; Schema: workflow_runtime; Owner: postgres
--

COPY workflow_runtime.input_dependencies (id, input_item_id, input_stack_id, depends_on_id, depends_on_stack_id, dependency_type, condition_expression, created_at, updated_at) FROM stdin;
66	in026	1917	in025	1917	dropdown	value_depends_on_parent	2025-05-04 11:31:09.548741	2025-05-04 11:31:09.548741
67	in005	1917	in003	1917	calculation	calculate_from_parent	2025-05-04 11:31:09.548741	2025-05-04 11:31:09.548741
68	in005	1917	in004	1917	calculation	calculate_from_parent	2025-05-04 11:31:09.548741	2025-05-04 11:31:09.548741
\.


--
-- Data for Name: input_items; Type: TABLE DATA; Schema: workflow_runtime; Owner: postgres
--

COPY workflow_runtime.input_items (id, input_stack_id, slot_id, contextual_id, entity_reference, attribute_reference, source_type, source_description, required, created_at, updated_at) FROM stdin;
\.


--
-- Data for Name: input_stack; Type: TABLE DATA; Schema: workflow_runtime; Owner: postgres
--

COPY workflow_runtime.input_stack (id, go_id, description, created_at, updated_at) FROM stdin;
1073	go001	global inputs	2025-05-04 11:31:09.548741	2025-05-04 11:31:09.548741
\.


--
-- Data for Name: leave_application; Type: TABLE DATA; Schema: workflow_runtime; Owner: postgres
--

COPY workflow_runtime.leave_application (id, created_at, updated_at, created_by, updated_by, leave_id, employee_id, start_date, end_date, num_days, reason, status, remarks, approved_by, leave_type, leave_sub_type, leave_request_instructions, short_leave_policy, extended_leave_policy) FROM stdin;
1	2025-05-04 12:11:32.97785	2025-05-04 12:11:32.97785	\N	\N	1	Sample Employee ID	2025-05-04	2025-05-04	0	Sample Leave Reason	PENDING	\N	\N	annual leave	\N	\N	\N	\N
2	2025-05-04 12:18:00.896246	2025-05-04 12:18:00.896246	\N	\N	2	EMP-001	2025-05-01	2025-05-05	4	Annual vacation	PENDING	\N	\N	annual leave	\N	\N	\N	\N
3	2025-05-04 12:30:25.835804	2025-05-04 12:30:25.835804	\N	\N	3	EMP-001	2025-05-01	2025-05-05	4	Annual vacation	PENDING	\N	\N	annual leave	AL001	\N	\N	\N
\.


--
-- Data for Name: leave_sub_type; Type: TABLE DATA; Schema: workflow_runtime; Owner: postgres
--

COPY workflow_runtime.leave_sub_type (id, created_at, updated_at, created_by, updated_by, leave_type, sub_type_id, sub_type_name, active) FROM stdin;
33	2025-05-04 12:02:02.303114	2025-05-04 12:02:02.303114	system	system	Annual Leave	AL001	Regular Annual Leave	t
34	2025-05-04 12:02:02.303114	2025-05-04 12:02:02.303114	system	system	Annual Leave	AL002	Advance Annual Leave	t
35	2025-05-04 12:02:02.303114	2025-05-04 12:02:02.303114	system	system	Annual Leave	AL003	Compensatory Leave	t
36	2025-05-04 12:02:02.303114	2025-05-04 12:02:02.303114	system	system	Annual Leave	AL004	Earned Leave	t
37	2025-05-04 12:02:02.303114	2025-05-04 12:02:02.303114	system	system	Sick Leave	SL001	Short-term Illness	t
38	2025-05-04 12:02:02.303114	2025-05-04 12:02:02.303114	system	system	Sick Leave	SL002	Long-term Illness	t
39	2025-05-04 12:02:02.303114	2025-05-04 12:02:02.303114	system	system	Sick Leave	SL003	Medical Appointment	t
40	2025-05-04 12:02:02.303114	2025-05-04 12:02:02.303114	system	system	Sick Leave	SL004	Hospitalization	t
41	2025-05-04 12:02:02.303114	2025-05-04 12:02:02.303114	system	system	Parental Leave	PL001	Maternity Leave	t
42	2025-05-04 12:02:02.303114	2025-05-04 12:02:02.303114	system	system	Parental Leave	PL002	Paternity Leave	t
43	2025-05-04 12:02:02.303114	2025-05-04 12:02:02.303114	system	system	Parental Leave	PL003	Adoption Leave	t
44	2025-05-04 12:02:02.303114	2025-05-04 12:02:02.303114	system	system	Parental Leave	PL004	Child Care Leave	t
45	2025-05-04 12:02:02.303114	2025-05-04 12:02:02.303114	system	system	Bereavement	BL001	Immediate Family	t
46	2025-05-04 12:02:02.303114	2025-05-04 12:02:02.303114	system	system	Bereavement	BL002	Extended Family	t
47	2025-05-04 12:02:02.303114	2025-05-04 12:02:02.303114	system	system	Bereavement	BL003	Funeral Attendance	t
48	2025-05-04 12:02:02.303114	2025-05-04 12:02:02.303114	system	system	Bereavement	BL004	Compassionate Leave	t
\.


--
-- Data for Name: lo_data_mapping_stack; Type: TABLE DATA; Schema: workflow_runtime; Owner: postgres
--

COPY workflow_runtime.lo_data_mapping_stack (id, lo_id, description, created_at, updated_at) FROM stdin;
275	lo001	Data handover between LOs	2025-05-04 11:31:09.548741	2025-05-04 11:31:09.548741
276	lo002	Data handover between LOs	2025-05-04 11:31:09.548741	2025-05-04 11:31:09.548741
\.


--
-- Data for Name: lo_data_mappings; Type: TABLE DATA; Schema: workflow_runtime; Owner: postgres
--

COPY workflow_runtime.lo_data_mappings (id, mapping_stack_id, source, target, mapping_type, created_at, updated_at) FROM stdin;
map001	275	lo001.out002	lo002.in008	direct	2025-05-04 11:31:09.548741	2025-05-04 11:31:09.548741
map002	275	lo001.out003	lo002.in009	direct	2025-05-04 11:31:09.548741	2025-05-04 11:31:09.548741
map003	275	lo001.out004	lo002.in010	direct	2025-05-04 11:31:09.548741	2025-05-04 11:31:09.548741
map004	275	lo001.out005	lo002.in011	direct	2025-05-04 11:31:09.548741	2025-05-04 11:31:09.548741
map005	275	lo001.out006	lo002.in012	direct	2025-05-04 11:31:09.548741	2025-05-04 11:31:09.548741
map006	275	lo001.out007	lo002.in013	direct	2025-05-04 11:31:09.548741	2025-05-04 11:31:09.548741
map013	275	lo001.out029	lo002.in028	direct	2025-05-04 11:31:09.548741	2025-05-04 11:31:09.548741
map014	275	lo001.out030	lo002.in029	direct	2025-05-04 11:31:09.548741	2025-05-04 11:31:09.548741
map007	275	lo001.out002	lo003.in015	direct	2025-05-04 11:31:09.548741	2025-05-04 11:31:09.548741
map008	275	lo001.out003	lo003.in016	direct	2025-05-04 11:31:09.548741	2025-05-04 11:31:09.548741
map009	275	lo001.out004	lo003.in017	direct	2025-05-04 11:31:09.548741	2025-05-04 11:31:09.548741
map010	275	lo001.out005	lo003.in018	direct	2025-05-04 11:31:09.548741	2025-05-04 11:31:09.548741
map011	275	lo001.out006	lo003.in019	direct	2025-05-04 11:31:09.548741	2025-05-04 11:31:09.548741
map012	275	lo001.out007	lo003.in020	direct	2025-05-04 11:31:09.548741	2025-05-04 11:31:09.548741
map015	275	lo001.out029	lo003.in030	direct	2025-05-04 11:31:09.548741	2025-05-04 11:31:09.548741
map016	275	lo001.out030	lo003.in031	direct	2025-05-04 11:31:09.548741	2025-05-04 11:31:09.548741
\.


--
-- Data for Name: lo_input_execution; Type: TABLE DATA; Schema: workflow_runtime; Owner: postgres
--

COPY workflow_runtime.lo_input_execution (id, instance_id, lo_id, input_contextual_id, input_value, created_at) FROM stdin;
81220	e608752e-5a87-4e44-a449-b22d6abfc0c3	lo001	go001.lo001.in027	\N	2025-05-04 11:43:17.547153
81221	e608752e-5a87-4e44-a449-b22d6abfc0c3	lo001	go001.lo001.in001	\N	2025-05-04 11:43:17.547153
81222	e608752e-5a87-4e44-a449-b22d6abfc0c3	lo001	go001.lo001.in002	\N	2025-05-04 11:43:17.547153
81223	e608752e-5a87-4e44-a449-b22d6abfc0c3	lo001	go001.lo001.in025	\N	2025-05-04 11:43:17.547153
81224	e608752e-5a87-4e44-a449-b22d6abfc0c3	lo001	go001.lo001.in026	\N	2025-05-04 11:43:17.547153
81225	e608752e-5a87-4e44-a449-b22d6abfc0c3	lo001	go001.lo001.in003	\N	2025-05-04 11:43:17.547153
81226	e608752e-5a87-4e44-a449-b22d6abfc0c3	lo001	go001.lo001.in004	\N	2025-05-04 11:43:17.547153
81227	e608752e-5a87-4e44-a449-b22d6abfc0c3	lo001	go001.lo001.in005	\N	2025-05-04 11:43:17.547153
81228	e608752e-5a87-4e44-a449-b22d6abfc0c3	lo001	go001.lo001.in006	\N	2025-05-04 11:43:17.547153
81229	e608752e-5a87-4e44-a449-b22d6abfc0c3	lo001	go001.lo001.in007	\N	2025-05-04 11:43:17.547153
81230	e608752e-5a87-4e44-a449-b22d6abfc0c3	lo002	go001.lo002.in027	\N	2025-05-04 11:43:17.547153
81231	e608752e-5a87-4e44-a449-b22d6abfc0c3	lo002	go001.lo002.in008	\N	2025-05-04 11:43:17.547153
81232	e608752e-5a87-4e44-a449-b22d6abfc0c3	lo002	go001.lo002.in009	\N	2025-05-04 11:43:17.547153
81233	e608752e-5a87-4e44-a449-b22d6abfc0c3	lo002	go001.lo002.in010	\N	2025-05-04 11:43:17.547153
81234	e608752e-5a87-4e44-a449-b22d6abfc0c3	lo002	go001.lo002.in011	\N	2025-05-04 11:43:17.547153
81235	e608752e-5a87-4e44-a449-b22d6abfc0c3	lo002	go001.lo002.in012	\N	2025-05-04 11:43:17.547153
81236	e608752e-5a87-4e44-a449-b22d6abfc0c3	lo002	go001.lo002.in013	\N	2025-05-04 11:43:17.547153
81237	e608752e-5a87-4e44-a449-b22d6abfc0c3	lo002	go001.lo002.in028	\N	2025-05-04 11:43:17.547153
81238	e608752e-5a87-4e44-a449-b22d6abfc0c3	lo002	go001.lo002.in029	\N	2025-05-04 11:43:17.547153
81239	e608752e-5a87-4e44-a449-b22d6abfc0c3	lo002	go001.lo002.in014	\N	2025-05-04 11:43:17.547153
81240	e608752e-5a87-4e44-a449-b22d6abfc0c3	lo002	go001.lo002.in015	\N	2025-05-04 11:43:17.547153
81241	e608752e-5a87-4e44-a449-b22d6abfc0c3	lo002	go001.lo002.in016	\N	2025-05-04 11:43:17.547153
81242	e608752e-5a87-4e44-a449-b22d6abfc0c3	lo003	go001.lo003.in032	\N	2025-05-04 11:43:17.547153
81243	e608752e-5a87-4e44-a449-b22d6abfc0c3	lo003	go001.lo003.in015	\N	2025-05-04 11:43:17.547153
81244	e608752e-5a87-4e44-a449-b22d6abfc0c3	lo003	go001.lo003.in016	\N	2025-05-04 11:43:17.547153
81245	e608752e-5a87-4e44-a449-b22d6abfc0c3	lo003	go001.lo003.in017	\N	2025-05-04 11:43:17.547153
81246	e608752e-5a87-4e44-a449-b22d6abfc0c3	lo003	go001.lo003.in018	\N	2025-05-04 11:43:17.547153
81247	e608752e-5a87-4e44-a449-b22d6abfc0c3	lo003	go001.lo003.in019	\N	2025-05-04 11:43:17.547153
81248	e608752e-5a87-4e44-a449-b22d6abfc0c3	lo003	go001.lo003.in020	\N	2025-05-04 11:43:17.547153
81249	e608752e-5a87-4e44-a449-b22d6abfc0c3	lo003	go001.lo003.in030	\N	2025-05-04 11:43:17.547153
81250	e608752e-5a87-4e44-a449-b22d6abfc0c3	lo003	go001.lo003.in031	\N	2025-05-04 11:43:17.547153
81251	e608752e-5a87-4e44-a449-b22d6abfc0c3	lo003	go001.lo003.in021	\N	2025-05-04 11:43:17.547153
81252	e608752e-5a87-4e44-a449-b22d6abfc0c3	lo003	go001.lo003.in022	\N	2025-05-04 11:43:17.547153
81253	e608752e-5a87-4e44-a449-b22d6abfc0c3	lo003	go001.lo003.in023	\N	2025-05-04 11:43:17.547153
81254	d2f342dc-5038-45a6-b598-84effe01d9a8	lo001	go001.lo001.in027	\N	2025-05-04 12:11:32.934725
81258	d2f342dc-5038-45a6-b598-84effe01d9a8	lo001	go001.lo001.in026	\N	2025-05-04 12:11:32.934725
81264	d2f342dc-5038-45a6-b598-84effe01d9a8	lo002	go001.lo002.in027	\N	2025-05-04 12:11:32.934725
81269	d2f342dc-5038-45a6-b598-84effe01d9a8	lo002	go001.lo002.in012	\N	2025-05-04 12:11:32.934725
81272	d2f342dc-5038-45a6-b598-84effe01d9a8	lo002	go001.lo002.in029	\N	2025-05-04 12:11:32.934725
81273	d2f342dc-5038-45a6-b598-84effe01d9a8	lo002	go001.lo002.in014	\N	2025-05-04 12:11:32.934725
81274	d2f342dc-5038-45a6-b598-84effe01d9a8	lo002	go001.lo002.in015	\N	2025-05-04 12:11:32.934725
81275	d2f342dc-5038-45a6-b598-84effe01d9a8	lo002	go001.lo002.in016	\N	2025-05-04 12:11:32.934725
81276	d2f342dc-5038-45a6-b598-84effe01d9a8	lo003	go001.lo003.in032	\N	2025-05-04 12:11:32.934725
81281	d2f342dc-5038-45a6-b598-84effe01d9a8	lo003	go001.lo003.in019	\N	2025-05-04 12:11:32.934725
81284	d2f342dc-5038-45a6-b598-84effe01d9a8	lo003	go001.lo003.in031	\N	2025-05-04 12:11:32.934725
81285	d2f342dc-5038-45a6-b598-84effe01d9a8	lo003	go001.lo003.in021	\N	2025-05-04 12:11:32.934725
81286	d2f342dc-5038-45a6-b598-84effe01d9a8	lo003	go001.lo003.in022	\N	2025-05-04 12:11:32.934725
81287	d2f342dc-5038-45a6-b598-84effe01d9a8	lo003	go001.lo003.in023	\N	2025-05-04 12:11:32.934725
81256	d2f342dc-5038-45a6-b598-84effe01d9a8	lo001	go001.lo001.in002	"Sample Employee ID"	2025-05-04 12:11:32.934725
81259	d2f342dc-5038-45a6-b598-84effe01d9a8	lo001	go001.lo001.in003	"2025-05-04"	2025-05-04 12:11:32.934725
81260	d2f342dc-5038-45a6-b598-84effe01d9a8	lo001	go001.lo001.in004	"2025-05-04"	2025-05-04 12:11:32.934725
81262	d2f342dc-5038-45a6-b598-84effe01d9a8	lo001	go001.lo001.in006	"Sample Leave Reason"	2025-05-04 12:11:32.934725
81257	d2f342dc-5038-45a6-b598-84effe01d9a8	lo001	go001.lo001.in025	"annual leave"	2025-05-04 12:11:32.934725
81255	d2f342dc-5038-45a6-b598-84effe01d9a8	lo001	go001.lo001.in001	1	2025-05-04 12:11:32.934725
81261	d2f342dc-5038-45a6-b598-84effe01d9a8	lo001	go001.lo001.in005	0	2025-05-04 12:11:32.934725
81263	d2f342dc-5038-45a6-b598-84effe01d9a8	lo001	go001.lo001.in007	"PENDING"	2025-05-04 12:11:32.934725
81265	d2f342dc-5038-45a6-b598-84effe01d9a8	lo002	go001.lo002.in008	1	2025-05-04 12:11:32.934725
81277	d2f342dc-5038-45a6-b598-84effe01d9a8	lo003	go001.lo003.in015	1	2025-05-04 12:11:32.934725
81266	d2f342dc-5038-45a6-b598-84effe01d9a8	lo002	go001.lo002.in009	"Sample Employee ID"	2025-05-04 12:11:32.934725
81278	d2f342dc-5038-45a6-b598-84effe01d9a8	lo003	go001.lo003.in016	"Sample Employee ID"	2025-05-04 12:11:32.934725
81267	d2f342dc-5038-45a6-b598-84effe01d9a8	lo002	go001.lo002.in010	"2025-05-04"	2025-05-04 12:11:32.934725
81279	d2f342dc-5038-45a6-b598-84effe01d9a8	lo003	go001.lo003.in017	"2025-05-04"	2025-05-04 12:11:32.934725
81268	d2f342dc-5038-45a6-b598-84effe01d9a8	lo002	go001.lo002.in011	"2025-05-04"	2025-05-04 12:11:32.934725
81280	d2f342dc-5038-45a6-b598-84effe01d9a8	lo003	go001.lo003.in018	"2025-05-04"	2025-05-04 12:11:32.934725
81270	d2f342dc-5038-45a6-b598-84effe01d9a8	lo002	go001.lo002.in013	"Sample Leave Reason"	2025-05-04 12:11:32.934725
81282	d2f342dc-5038-45a6-b598-84effe01d9a8	lo003	go001.lo003.in020	"Sample Leave Reason"	2025-05-04 12:11:32.934725
81271	d2f342dc-5038-45a6-b598-84effe01d9a8	lo002	go001.lo002.in028	"annual leave"	2025-05-04 12:11:32.934725
81283	d2f342dc-5038-45a6-b598-84effe01d9a8	lo003	go001.lo003.in030	"annual leave"	2025-05-04 12:11:32.934725
81288	87944dfa-47bc-4235-a6ed-f8440758a0b4	lo001	go001.lo001.in027	\N	2025-05-04 12:15:15.102869
81290	87944dfa-47bc-4235-a6ed-f8440758a0b4	lo001	go001.lo001.in002	"EMP-001"	2025-05-04 12:15:15.102869
81291	87944dfa-47bc-4235-a6ed-f8440758a0b4	lo001	go001.lo001.in025	"annual leave"	2025-05-04 12:15:15.102869
81289	87944dfa-47bc-4235-a6ed-f8440758a0b4	lo001	go001.lo001.in001	2	2025-05-04 12:15:15.102869
81292	87944dfa-47bc-4235-a6ed-f8440758a0b4	lo001	go001.lo001.in026	\N	2025-05-04 12:15:15.102869
81298	87944dfa-47bc-4235-a6ed-f8440758a0b4	lo002	go001.lo002.in027	\N	2025-05-04 12:15:15.102869
81303	87944dfa-47bc-4235-a6ed-f8440758a0b4	lo002	go001.lo002.in012	\N	2025-05-04 12:15:15.102869
81306	87944dfa-47bc-4235-a6ed-f8440758a0b4	lo002	go001.lo002.in029	\N	2025-05-04 12:15:15.102869
81307	87944dfa-47bc-4235-a6ed-f8440758a0b4	lo002	go001.lo002.in014	\N	2025-05-04 12:15:15.102869
81308	87944dfa-47bc-4235-a6ed-f8440758a0b4	lo002	go001.lo002.in015	\N	2025-05-04 12:15:15.102869
81309	87944dfa-47bc-4235-a6ed-f8440758a0b4	lo002	go001.lo002.in016	\N	2025-05-04 12:15:15.102869
81310	87944dfa-47bc-4235-a6ed-f8440758a0b4	lo003	go001.lo003.in032	\N	2025-05-04 12:15:15.102869
81315	87944dfa-47bc-4235-a6ed-f8440758a0b4	lo003	go001.lo003.in019	\N	2025-05-04 12:15:15.102869
81318	87944dfa-47bc-4235-a6ed-f8440758a0b4	lo003	go001.lo003.in031	\N	2025-05-04 12:15:15.102869
81319	87944dfa-47bc-4235-a6ed-f8440758a0b4	lo003	go001.lo003.in021	\N	2025-05-04 12:15:15.102869
81320	87944dfa-47bc-4235-a6ed-f8440758a0b4	lo003	go001.lo003.in022	\N	2025-05-04 12:15:15.102869
81321	87944dfa-47bc-4235-a6ed-f8440758a0b4	lo003	go001.lo003.in023	\N	2025-05-04 12:15:15.102869
81293	87944dfa-47bc-4235-a6ed-f8440758a0b4	lo001	go001.lo001.in003	"2025-05-01"	2025-05-04 12:15:15.102869
81294	87944dfa-47bc-4235-a6ed-f8440758a0b4	lo001	go001.lo001.in004	"2025-05-05"	2025-05-04 12:15:15.102869
81296	87944dfa-47bc-4235-a6ed-f8440758a0b4	lo001	go001.lo001.in006	"Annual vacation"	2025-05-04 12:15:15.102869
81295	87944dfa-47bc-4235-a6ed-f8440758a0b4	lo001	go001.lo001.in005	4	2025-05-04 12:15:15.102869
81297	87944dfa-47bc-4235-a6ed-f8440758a0b4	lo001	go001.lo001.in007	"PENDING"	2025-05-04 12:15:15.102869
81299	87944dfa-47bc-4235-a6ed-f8440758a0b4	lo002	go001.lo002.in008	2	2025-05-04 12:15:15.102869
81311	87944dfa-47bc-4235-a6ed-f8440758a0b4	lo003	go001.lo003.in015	2	2025-05-04 12:15:15.102869
81300	87944dfa-47bc-4235-a6ed-f8440758a0b4	lo002	go001.lo002.in009	"EMP-001"	2025-05-04 12:15:15.102869
81312	87944dfa-47bc-4235-a6ed-f8440758a0b4	lo003	go001.lo003.in016	"EMP-001"	2025-05-04 12:15:15.102869
81301	87944dfa-47bc-4235-a6ed-f8440758a0b4	lo002	go001.lo002.in010	"2025-05-01"	2025-05-04 12:15:15.102869
81313	87944dfa-47bc-4235-a6ed-f8440758a0b4	lo003	go001.lo003.in017	"2025-05-01"	2025-05-04 12:15:15.102869
81302	87944dfa-47bc-4235-a6ed-f8440758a0b4	lo002	go001.lo002.in011	"2025-05-05"	2025-05-04 12:15:15.102869
81314	87944dfa-47bc-4235-a6ed-f8440758a0b4	lo003	go001.lo003.in018	"2025-05-05"	2025-05-04 12:15:15.102869
81304	87944dfa-47bc-4235-a6ed-f8440758a0b4	lo002	go001.lo002.in013	"Annual vacation"	2025-05-04 12:15:15.102869
81316	87944dfa-47bc-4235-a6ed-f8440758a0b4	lo003	go001.lo003.in020	"Annual vacation"	2025-05-04 12:15:15.102869
81305	87944dfa-47bc-4235-a6ed-f8440758a0b4	lo002	go001.lo002.in028	"annual leave"	2025-05-04 12:15:15.102869
81317	87944dfa-47bc-4235-a6ed-f8440758a0b4	lo003	go001.lo003.in030	"annual leave"	2025-05-04 12:15:15.102869
81322	aa2b73e1-5f04-4c78-8d1f-4fb251f5bbe5	lo001	go001.lo001.in027	\N	2025-05-04 12:29:00.657544
81332	aa2b73e1-5f04-4c78-8d1f-4fb251f5bbe5	lo002	go001.lo002.in027	\N	2025-05-04 12:29:00.657544
81337	aa2b73e1-5f04-4c78-8d1f-4fb251f5bbe5	lo002	go001.lo002.in012	\N	2025-05-04 12:29:00.657544
81340	aa2b73e1-5f04-4c78-8d1f-4fb251f5bbe5	lo002	go001.lo002.in029	\N	2025-05-04 12:29:00.657544
81341	aa2b73e1-5f04-4c78-8d1f-4fb251f5bbe5	lo002	go001.lo002.in014	\N	2025-05-04 12:29:00.657544
81342	aa2b73e1-5f04-4c78-8d1f-4fb251f5bbe5	lo002	go001.lo002.in015	\N	2025-05-04 12:29:00.657544
81343	aa2b73e1-5f04-4c78-8d1f-4fb251f5bbe5	lo002	go001.lo002.in016	\N	2025-05-04 12:29:00.657544
81344	aa2b73e1-5f04-4c78-8d1f-4fb251f5bbe5	lo003	go001.lo003.in032	\N	2025-05-04 12:29:00.657544
81349	aa2b73e1-5f04-4c78-8d1f-4fb251f5bbe5	lo003	go001.lo003.in019	\N	2025-05-04 12:29:00.657544
81352	aa2b73e1-5f04-4c78-8d1f-4fb251f5bbe5	lo003	go001.lo003.in031	\N	2025-05-04 12:29:00.657544
81353	aa2b73e1-5f04-4c78-8d1f-4fb251f5bbe5	lo003	go001.lo003.in021	\N	2025-05-04 12:29:00.657544
81354	aa2b73e1-5f04-4c78-8d1f-4fb251f5bbe5	lo003	go001.lo003.in022	\N	2025-05-04 12:29:00.657544
81355	aa2b73e1-5f04-4c78-8d1f-4fb251f5bbe5	lo003	go001.lo003.in023	\N	2025-05-04 12:29:00.657544
81324	aa2b73e1-5f04-4c78-8d1f-4fb251f5bbe5	lo001	go001.lo001.in002	"EMP-001"	2025-05-04 12:29:00.657544
81327	aa2b73e1-5f04-4c78-8d1f-4fb251f5bbe5	lo001	go001.lo001.in003	"2025-05-01"	2025-05-04 12:29:00.657544
81328	aa2b73e1-5f04-4c78-8d1f-4fb251f5bbe5	lo001	go001.lo001.in004	"2025-05-05"	2025-05-04 12:29:00.657544
81330	aa2b73e1-5f04-4c78-8d1f-4fb251f5bbe5	lo001	go001.lo001.in006	"Annual vacation"	2025-05-04 12:29:00.657544
81325	aa2b73e1-5f04-4c78-8d1f-4fb251f5bbe5	lo001	go001.lo001.in025	"annual leave"	2025-05-04 12:29:00.657544
81326	aa2b73e1-5f04-4c78-8d1f-4fb251f5bbe5	lo001	go001.lo001.in026	"AL001"	2025-05-04 12:29:00.657544
81323	aa2b73e1-5f04-4c78-8d1f-4fb251f5bbe5	lo001	go001.lo001.in001	3	2025-05-04 12:29:00.657544
81329	aa2b73e1-5f04-4c78-8d1f-4fb251f5bbe5	lo001	go001.lo001.in005	4	2025-05-04 12:29:00.657544
81331	aa2b73e1-5f04-4c78-8d1f-4fb251f5bbe5	lo001	go001.lo001.in007	"PENDING"	2025-05-04 12:29:00.657544
81333	aa2b73e1-5f04-4c78-8d1f-4fb251f5bbe5	lo002	go001.lo002.in008	3	2025-05-04 12:29:00.657544
81334	aa2b73e1-5f04-4c78-8d1f-4fb251f5bbe5	lo002	go001.lo002.in009	"EMP-001"	2025-05-04 12:29:00.657544
81335	aa2b73e1-5f04-4c78-8d1f-4fb251f5bbe5	lo002	go001.lo002.in010	"2025-05-01"	2025-05-04 12:29:00.657544
81336	aa2b73e1-5f04-4c78-8d1f-4fb251f5bbe5	lo002	go001.lo002.in011	"2025-05-05"	2025-05-04 12:29:00.657544
81338	aa2b73e1-5f04-4c78-8d1f-4fb251f5bbe5	lo002	go001.lo002.in013	"Annual vacation"	2025-05-04 12:29:00.657544
81339	aa2b73e1-5f04-4c78-8d1f-4fb251f5bbe5	lo002	go001.lo002.in028	"annual leave"	2025-05-04 12:29:00.657544
81345	aa2b73e1-5f04-4c78-8d1f-4fb251f5bbe5	lo003	go001.lo003.in015	3	2025-05-04 12:29:00.657544
81346	aa2b73e1-5f04-4c78-8d1f-4fb251f5bbe5	lo003	go001.lo003.in016	"EMP-001"	2025-05-04 12:29:00.657544
81347	aa2b73e1-5f04-4c78-8d1f-4fb251f5bbe5	lo003	go001.lo003.in017	"2025-05-01"	2025-05-04 12:29:00.657544
81348	aa2b73e1-5f04-4c78-8d1f-4fb251f5bbe5	lo003	go001.lo003.in018	"2025-05-05"	2025-05-04 12:29:00.657544
81350	aa2b73e1-5f04-4c78-8d1f-4fb251f5bbe5	lo003	go001.lo003.in020	"Annual vacation"	2025-05-04 12:29:00.657544
81351	aa2b73e1-5f04-4c78-8d1f-4fb251f5bbe5	lo003	go001.lo003.in030	"annual leave"	2025-05-04 12:29:00.657544
\.


--
-- Data for Name: lo_input_items; Type: TABLE DATA; Schema: workflow_runtime; Owner: postgres
--

COPY workflow_runtime.lo_input_items (id, input_stack_id, slot_id, contextual_id, source_type, source_description, required, created_at, updated_at, lo_id, data_type, ui_control, nested_function, nested_functions, metadata, dependencies, dependency_type, lookup_function, is_visible) FROM stdin;
in027	1917	e001.at999.in027	go001.lo001.in027	information	Leave Request Instructions	f	2025-05-04 11:31:09.548741	2025-05-04 11:31:09.548741	lo001	\N	oj-text	\N	\N	{"usage": "", "is_informational": true, "has_dropdown_source": false}	\N	\N	\N	t
in001	1917	e001.at001.in001	go001.lo001.in001	system	Auto-generated Leave ID	t	2025-05-04 11:31:09.548741	2025-05-04 11:31:09.548741	lo001	\N	oj-input-text	\N	\N	{"usage": "", "is_informational": false, "has_dropdown_source": false}	\N	\N	\N	t
in002	1917	e001.at002.in002	go001.lo001.in002	user	Employee ID	t	2025-05-04 11:31:09.548741	2025-05-04 11:31:09.548741	lo001	\N	oj-input-text	\N	\N	{"usage": "", "is_informational": false, "has_dropdown_source": false}	\N	\N	\N	t
in025	1917	e001.at010.in025	go001.lo001.in025	user	Leave Type	t	2025-05-04 11:31:09.548741	2025-05-04 11:31:09.548741	lo001	\N	oj-combobox-one	\N	\N	{"usage": "", "is_informational": false, "has_dropdown_source": true}	\N	\N	\N	t
in026	1917	e001.at011.in026	go001.lo001.in026	system_dependent	Leave Sub-Type	f	2025-05-04 11:31:09.548741	2025-05-04 11:31:09.548741	lo001	\N	oj-combobox-one	\N	\N	{"usage": "", "is_informational": false, "has_dropdown_source": true}	["in025"]	dropdown	\N	t
in003	1917	e001.at003.in003	go001.lo001.in003	user	Start Date	t	2025-05-04 11:31:09.548741	2025-05-04 11:31:09.548741	lo001	\N	oj-input-date	\N	\N	{"usage": "", "is_informational": false, "has_dropdown_source": false}	\N	\N	\N	t
in004	1917	e001.at004.in004	go001.lo001.in004	user	End Date	t	2025-05-04 11:31:09.548741	2025-05-04 11:31:09.548741	lo001	\N	oj-input-date	\N	\N	{"usage": "", "is_informational": false, "has_dropdown_source": false}	\N	\N	\N	t
in005	1917	e001.at005.in005	go001.lo001.in005	system_dependent	Number of Days (calculated)	t	2025-05-04 11:31:09.548741	2025-05-04 11:31:09.548741	lo001	\N	oj-input-number	\N	\N	{"usage": "", "is_informational": false, "has_dropdown_source": false}	["in003", "in004"]	calculation	\N	t
in006	1917	e001.at006.in006	go001.lo001.in006	user	Leave Reason	t	2025-05-04 11:31:09.548741	2025-05-04 11:31:09.548741	lo001	\N	oj-text-area	\N	\N	{"usage": "", "is_informational": false, "has_dropdown_source": false}	\N	\N	\N	t
in007	1917	e001.at007.in007	go001.lo001.in007	system	Status	t	2025-05-04 11:31:09.548741	2025-05-04 11:31:09.548741	lo001	\N	oj-select-single	\N	\N	{"usage": "", "is_informational": false, "has_dropdown_source": false}	\N	\N	\N	t
in027	1918	e001.at999.in027	go001.lo002.in027	information	Short Leave Policy	f	2025-05-04 11:31:09.548741	2025-05-04 11:31:09.548741	lo002	\N	oj-text	\N	\N	{"usage": "", "is_informational": true, "has_dropdown_source": false}	\N	\N	\N	t
in008	1918	e001.at001.in008	go001.lo002.in008	system	Leave ID	t	2025-05-04 11:31:09.548741	2025-05-04 11:31:09.548741	lo002	\N	oj-input-text	\N	\N	{"usage": "lookup", "is_informational": false, "has_dropdown_source": false}	\N	\N	\N	t
in009	1918	e001.at002.in009	go001.lo002.in009	system	Employee ID	t	2025-05-04 11:31:09.548741	2025-05-04 11:31:09.548741	lo002	\N	oj-input-text	\N	\N	{"usage": "lookup", "is_informational": false, "has_dropdown_source": false}	\N	\N	\N	t
in010	1918	e001.at003.in010	go001.lo002.in010	system	Start Date	t	2025-05-04 11:31:09.548741	2025-05-04 11:31:09.548741	lo002	\N	oj-input-date	\N	\N	{"usage": "lookup", "is_informational": false, "has_dropdown_source": false}	\N	\N	\N	t
in011	1918	e001.at004.in011	go001.lo002.in011	system	End Date	t	2025-05-04 11:31:09.548741	2025-05-04 11:31:09.548741	lo002	\N	oj-input-date	\N	\N	{"usage": "lookup", "is_informational": false, "has_dropdown_source": false}	\N	\N	\N	t
in012	1918	e001.at005.in012	go001.lo002.in012	system	Number of Days	t	2025-05-04 11:31:09.548741	2025-05-04 11:31:09.548741	lo002	\N	oj-input-number	\N	\N	{"usage": "lookup", "is_informational": false, "has_dropdown_source": false}	\N	\N	\N	t
in013	1918	e001.at006.in013	go001.lo002.in013	system	Leave Reason	t	2025-05-04 11:31:09.548741	2025-05-04 11:31:09.548741	lo002	\N	oj-text-area	\N	\N	{"usage": "lookup", "is_informational": false, "has_dropdown_source": false}	\N	\N	\N	t
in028	1918	e001.at010.in028	go001.lo002.in028	system	Leave Type	t	2025-05-04 11:31:09.548741	2025-05-04 11:31:09.548741	lo002	\N	oj-input-text	\N	\N	{"usage": "lookup", "is_informational": false, "has_dropdown_source": false}	\N	\N	\N	t
in029	1918	e001.at011.in029	go001.lo002.in029	system	Leave Sub-Type	f	2025-05-04 11:31:09.548741	2025-05-04 11:31:09.548741	lo002	\N	oj-input-text	\N	\N	{"usage": "lookup", "is_informational": false, "has_dropdown_source": false}	\N	\N	\N	t
in014	1918	e001.at007.in014	go001.lo002.in014	user	Status	t	2025-05-04 11:31:09.548741	2025-05-04 11:31:09.548741	lo002	\N	oj-select-single	\N	\N	{"usage": "update", "is_informational": false, "has_dropdown_source": false}	\N	\N	\N	t
in015	1918	e001.at008.in015	go001.lo002.in015	user	Remarks	f	2025-05-04 11:31:09.548741	2025-05-04 11:31:09.548741	lo002	\N	oj-text-area	\N	\N	{"usage": "update", "is_informational": false, "has_dropdown_source": false}	\N	\N	\N	t
in016	1918	e001.at009.in016	go001.lo002.in016	system	Approved By	f	2025-05-04 11:31:09.548741	2025-05-04 11:31:09.548741	lo002	\N	oj-input-text	\N	\N	{"usage": "update", "is_informational": false, "has_dropdown_source": false}	\N	\N	\N	t
in032	1919	e001.at999.in032	go001.lo003.in032	information	Extended Leave Policy	f	2025-05-04 11:31:09.548741	2025-05-04 11:31:09.548741	lo003	\N	oj-text	\N	\N	{"usage": "", "is_informational": true, "has_dropdown_source": false}	\N	\N	\N	t
in015	1919	e001.at001.in015	go001.lo003.in015	system	Leave ID	t	2025-05-04 11:31:09.548741	2025-05-04 11:31:09.548741	lo003	\N	oj-input-text	\N	\N	{"usage": "lookup", "is_informational": false, "has_dropdown_source": false}	\N	\N	\N	t
in016	1919	e001.at002.in016	go001.lo003.in016	system	Employee ID	t	2025-05-04 11:31:09.548741	2025-05-04 11:31:09.548741	lo003	\N	oj-input-text	\N	\N	{"usage": "lookup", "is_informational": false, "has_dropdown_source": false}	\N	\N	\N	t
in017	1919	e001.at003.in017	go001.lo003.in017	system	Start Date	t	2025-05-04 11:31:09.548741	2025-05-04 11:31:09.548741	lo003	\N	oj-input-date	\N	\N	{"usage": "lookup", "is_informational": false, "has_dropdown_source": false}	\N	\N	\N	t
in018	1919	e001.at004.in018	go001.lo003.in018	system	End Date	t	2025-05-04 11:31:09.548741	2025-05-04 11:31:09.548741	lo003	\N	oj-input-date	\N	\N	{"usage": "lookup", "is_informational": false, "has_dropdown_source": false}	\N	\N	\N	t
in019	1919	e001.at005.in019	go001.lo003.in019	system	Number of Days	t	2025-05-04 11:31:09.548741	2025-05-04 11:31:09.548741	lo003	\N	oj-input-number	\N	\N	{"usage": "lookup", "is_informational": false, "has_dropdown_source": false}	\N	\N	\N	t
in020	1919	e001.at006.in020	go001.lo003.in020	system	Leave Reason	t	2025-05-04 11:31:09.548741	2025-05-04 11:31:09.548741	lo003	\N	oj-text-area	\N	\N	{"usage": "lookup", "is_informational": false, "has_dropdown_source": false}	\N	\N	\N	t
in030	1919	e001.at010.in030	go001.lo003.in030	system	Leave Type	t	2025-05-04 11:31:09.548741	2025-05-04 11:31:09.548741	lo003	\N	oj-input-text	\N	\N	{"usage": "lookup", "is_informational": false, "has_dropdown_source": false}	\N	\N	\N	t
in031	1919	e001.at011.in031	go001.lo003.in031	system	Leave Sub-Type	f	2025-05-04 11:31:09.548741	2025-05-04 11:31:09.548741	lo003	\N	oj-input-text	\N	\N	{"usage": "lookup", "is_informational": false, "has_dropdown_source": false}	\N	\N	\N	t
in021	1919	e001.at007.in021	go001.lo003.in021	user	Status	t	2025-05-04 11:31:09.548741	2025-05-04 11:31:09.548741	lo003	\N	oj-select-single	\N	\N	{"usage": "update", "is_informational": false, "has_dropdown_source": false}	\N	\N	\N	t
in022	1919	e001.at008.in022	go001.lo003.in022	user	Remarks	f	2025-05-04 11:31:09.548741	2025-05-04 11:31:09.548741	lo003	\N	oj-text-area	\N	\N	{"usage": "update", "is_informational": false, "has_dropdown_source": false}	\N	\N	\N	t
in023	1919	e001.at009.in023	go001.lo003.in023	system	Approved By	f	2025-05-04 11:31:09.548741	2025-05-04 11:31:09.548741	lo003	\N	oj-input-text	\N	\N	{"usage": "update", "is_informational": false, "has_dropdown_source": false}	\N	\N	\N	t
\.


--
-- Data for Name: lo_input_stack; Type: TABLE DATA; Schema: workflow_runtime; Owner: postgres
--

COPY workflow_runtime.lo_input_stack (id, lo_id, description, created_at, updated_at) FROM stdin;
1917	lo001	Capture leave application details	2025-05-04 11:31:09.548741	2025-05-04 11:31:09.548741
1918	lo002	Manager reviews leave application	2025-05-04 11:31:09.548741	2025-05-04 11:31:09.548741
1919	lo003	HR Manager reviews leave application	2025-05-04 11:31:09.548741	2025-05-04 11:31:09.548741
\.


--
-- Data for Name: lo_input_validations; Type: TABLE DATA; Schema: workflow_runtime; Owner: postgres
--

COPY workflow_runtime.lo_input_validations (id, input_item_id, input_stack_id, rule, rule_type, entity, attribute, validation_method, reference_date_source, allowed_values, error_message, created_at, updated_at) FROM stdin;
1945	in001	1917	Leave ID must be unique	validate_required	\N	\N	validate_required	\N	\N	Leave ID is required	2025-05-04 11:31:09.548741	2025-05-04 11:31:09.548741
1946	in002	1917	Employee ID is required	validate_required	\N	\N	validate_required	\N	\N	Employee ID cannot be empty	2025-05-04 11:31:09.548741	2025-05-04 11:31:09.548741
1947	in025	1917	Leave Type is required	validate_required	\N	\N	validate_required	\N	\N	Leave Type is required	2025-05-04 11:31:09.548741	2025-05-04 11:31:09.548741
1948	in003	1917	Start Date is required	validate_required	\N	\N	validate_required	\N	\N	Start Date cannot be empty	2025-05-04 11:31:09.548741	2025-05-04 11:31:09.548741
1949	in004	1917	End Date is required	validate_required	\N	\N	validate_required	\N	\N	End Date cannot be empty	2025-05-04 11:31:09.548741	2025-05-04 11:31:09.548741
1950	in005	1917	Number of Days must be positive	validate_required	\N	\N	validate_required	\N	\N	Number of Days cannot be empty	2025-05-04 11:31:09.548741	2025-05-04 11:31:09.548741
1951	in006	1917	Reason is required	validate_required	\N	\N	validate_required	\N	\N	Leave reason cannot be empty	2025-05-04 11:31:09.548741	2025-05-04 11:31:09.548741
1952	in007	1917	Status must be valid	enum_check	\N	\N	enum_check	\N	["Pending", "Approved", "Rejected"]	Invalid status value	2025-05-04 11:31:09.548741	2025-05-04 11:31:09.548741
1953	in008	1918	Leave ID must be present	validate_required	\N	\N	validate_required	\N	\N	Leave ID is required	2025-05-04 11:31:09.548741	2025-05-04 11:31:09.548741
1954	in008	1918	Leave ID must exist	entity_exists	e001	at001	entity_exists	\N	\N	Leave ID not found in the system	2025-05-04 11:31:09.548741	2025-05-04 11:31:09.548741
1955	in009	1918	Employee ID is required	validate_required	\N	\N	validate_required	\N	\N	Employee ID is required	2025-05-04 11:31:09.548741	2025-05-04 11:31:09.548741
1956	in010	1918	Start Date is required	validate_required	\N	\N	validate_required	\N	\N	Start Date is required	2025-05-04 11:31:09.548741	2025-05-04 11:31:09.548741
1957	in011	1918	End Date is required	validate_required	\N	\N	validate_required	\N	\N	End Date is required	2025-05-04 11:31:09.548741	2025-05-04 11:31:09.548741
1958	in012	1918	Number of Days is required	validate_required	\N	\N	validate_required	\N	\N	Number of Days is required	2025-05-04 11:31:09.548741	2025-05-04 11:31:09.548741
1959	in013	1918	Reason is required	validate_required	\N	\N	validate_required	\N	\N	Leave reason is required	2025-05-04 11:31:09.548741	2025-05-04 11:31:09.548741
1960	in028	1918	Leave Type is required	validate_required	\N	\N	validate_required	\N	\N	Leave Type is required	2025-05-04 11:31:09.548741	2025-05-04 11:31:09.548741
1961	in014	1918	Status must be valid	enum_check	\N	\N	enum_check	\N	["Pending", "Approved", "Rejected"]	Invalid status value	2025-05-04 11:31:09.548741	2025-05-04 11:31:09.548741
1962	in015	1919	Leave ID must be present	validate_required	\N	\N	validate_required	\N	\N	Leave ID is required	2025-05-04 11:31:09.548741	2025-05-04 11:31:09.548741
1963	in015	1919	Leave ID must exist	entity_exists	e001	at001	entity_exists	\N	\N	Leave ID not found in the system	2025-05-04 11:31:09.548741	2025-05-04 11:31:09.548741
1964	in016	1919	Employee ID is required	validate_required	\N	\N	validate_required	\N	\N	Employee ID is required	2025-05-04 11:31:09.548741	2025-05-04 11:31:09.548741
1965	in017	1919	Start Date is required	validate_required	\N	\N	validate_required	\N	\N	Start Date is required	2025-05-04 11:31:09.548741	2025-05-04 11:31:09.548741
1966	in018	1919	End Date is required	validate_required	\N	\N	validate_required	\N	\N	End Date is required	2025-05-04 11:31:09.548741	2025-05-04 11:31:09.548741
1967	in019	1919	Number of Days is required	validate_required	\N	\N	validate_required	\N	\N	Number of Days is required	2025-05-04 11:31:09.548741	2025-05-04 11:31:09.548741
1968	in020	1919	Reason is required	validate_required	\N	\N	validate_required	\N	\N	Leave reason is required	2025-05-04 11:31:09.548741	2025-05-04 11:31:09.548741
1969	in030	1919	Leave Type is required	validate_required	\N	\N	validate_required	\N	\N	Leave Type is required	2025-05-04 11:31:09.548741	2025-05-04 11:31:09.548741
1970	in021	1919	Status must be valid	enum_check	\N	\N	enum_check	\N	["Pending", "Approved", "Rejected"]	Invalid status value	2025-05-04 11:31:09.548741	2025-05-04 11:31:09.548741
\.


--
-- Data for Name: lo_nested_functions; Type: TABLE DATA; Schema: workflow_runtime; Owner: postgres
--

COPY workflow_runtime.lo_nested_functions (id, lo_id, nested_function_id, function_name, function_type, parameters, created_at, updated_at, input_contextual_id, output_to) FROM stdin;
1598	lo001	nf006	to_uppercase	transform	{"text": "Please specify your leave dates and reason. If selecting Sick Leave for more than 3 days, a medical certificate will be required."}	2025-05-04 11:31:09.548741	2025-05-04 11:31:09.548741	go001.lo001.in027	info_text
1599	lo001	nf001	generate_id	utility	{"entity": "e001", "prefix": "LV", "attribute": "at001"}	2025-05-04 11:31:09.548741	2025-05-04 11:31:09.548741	go001.lo001.in001	at001
1600	lo001	nf002	subtract_days	math	{"end_date": "${in004}", "start_date": "${in003}"}	2025-05-04 11:31:09.548741	2025-05-04 11:31:09.548741	go001.lo001.in005	at005
1601	lo001	nf003	to_uppercase	transform	{"text": "Pending"}	2025-05-04 11:31:09.548741	2025-05-04 11:31:09.548741	go001.lo001.in007	at007
1602	lo002	nf007	to_uppercase	transform	{"text": "This leave request is for 3 days or less and requires your approval. Please review the details carefully."}	2025-05-04 11:31:09.548741	2025-05-04 11:31:09.548741	go001.lo002.in027	info_text
1603	lo002	nf004	current_timestamp	utility	\N	2025-05-04 11:31:09.548741	2025-05-04 11:31:09.548741	go001.lo002.in016	at009
1604	lo003	nf008	to_uppercase	transform	{"text": "This leave request is for more than 3 days and requires HR approval. Please verify the employee's leave balance before approval."}	2025-05-04 11:31:09.548741	2025-05-04 11:31:09.548741	go001.lo003.in032	info_text
1605	lo003	nf005	current_timestamp	utility	\N	2025-05-04 11:31:09.548741	2025-05-04 11:31:09.548741	go001.lo003.in023	at009
\.


--
-- Data for Name: lo_output_execution; Type: TABLE DATA; Schema: workflow_runtime; Owner: postgres
--

COPY workflow_runtime.lo_output_execution (id, instance_id, lo_id, output_contextual_id, output_value, created_at) FROM stdin;
43279	e608752e-5a87-4e44-a449-b22d6abfc0c3	lo001	go001.lo001.out001	\N	2025-05-04 11:43:17.547153
43280	e608752e-5a87-4e44-a449-b22d6abfc0c3	lo001	go001.lo001.out002	\N	2025-05-04 11:43:17.547153
43281	e608752e-5a87-4e44-a449-b22d6abfc0c3	lo001	go001.lo001.out003	\N	2025-05-04 11:43:17.547153
43282	e608752e-5a87-4e44-a449-b22d6abfc0c3	lo001	go001.lo001.out004	\N	2025-05-04 11:43:17.547153
43283	e608752e-5a87-4e44-a449-b22d6abfc0c3	lo001	go001.lo001.out005	\N	2025-05-04 11:43:17.547153
43284	e608752e-5a87-4e44-a449-b22d6abfc0c3	lo001	go001.lo001.out006	\N	2025-05-04 11:43:17.547153
43285	e608752e-5a87-4e44-a449-b22d6abfc0c3	lo001	go001.lo001.out007	\N	2025-05-04 11:43:17.547153
43286	e608752e-5a87-4e44-a449-b22d6abfc0c3	lo001	go001.lo001.out008	\N	2025-05-04 11:43:17.547153
43287	e608752e-5a87-4e44-a449-b22d6abfc0c3	lo001	go001.lo001.out029	\N	2025-05-04 11:43:17.547153
43288	e608752e-5a87-4e44-a449-b22d6abfc0c3	lo001	go001.lo001.out030	\N	2025-05-04 11:43:17.547153
43289	e608752e-5a87-4e44-a449-b22d6abfc0c3	lo002	go001.lo002.out009	\N	2025-05-04 11:43:17.547153
43290	e608752e-5a87-4e44-a449-b22d6abfc0c3	lo002	go001.lo002.out010	\N	2025-05-04 11:43:17.547153
43291	e608752e-5a87-4e44-a449-b22d6abfc0c3	lo002	go001.lo002.out011	\N	2025-05-04 11:43:17.547153
43292	e608752e-5a87-4e44-a449-b22d6abfc0c3	lo002	go001.lo002.out012	\N	2025-05-04 11:43:17.547153
43293	e608752e-5a87-4e44-a449-b22d6abfc0c3	lo002	go001.lo002.out013	\N	2025-05-04 11:43:17.547153
43294	e608752e-5a87-4e44-a449-b22d6abfc0c3	lo002	go001.lo002.out014	\N	2025-05-04 11:43:17.547153
43295	e608752e-5a87-4e44-a449-b22d6abfc0c3	lo002	go001.lo002.out015	\N	2025-05-04 11:43:17.547153
43296	e608752e-5a87-4e44-a449-b22d6abfc0c3	lo002	go001.lo002.out016	\N	2025-05-04 11:43:17.547153
43297	e608752e-5a87-4e44-a449-b22d6abfc0c3	lo002	go001.lo002.out017	\N	2025-05-04 11:43:17.547153
43298	e608752e-5a87-4e44-a449-b22d6abfc0c3	lo002	go001.lo002.out018	\N	2025-05-04 11:43:17.547153
43299	e608752e-5a87-4e44-a449-b22d6abfc0c3	lo002	go001.lo002.out031	\N	2025-05-04 11:43:17.547153
43300	e608752e-5a87-4e44-a449-b22d6abfc0c3	lo002	go001.lo002.out032	\N	2025-05-04 11:43:17.547153
43301	e608752e-5a87-4e44-a449-b22d6abfc0c3	lo003	go001.lo003.out019	\N	2025-05-04 11:43:17.547153
43302	e608752e-5a87-4e44-a449-b22d6abfc0c3	lo003	go001.lo003.out020	\N	2025-05-04 11:43:17.547153
43303	e608752e-5a87-4e44-a449-b22d6abfc0c3	lo003	go001.lo003.out021	\N	2025-05-04 11:43:17.547153
43304	e608752e-5a87-4e44-a449-b22d6abfc0c3	lo003	go001.lo003.out022	\N	2025-05-04 11:43:17.547153
43305	e608752e-5a87-4e44-a449-b22d6abfc0c3	lo003	go001.lo003.out023	\N	2025-05-04 11:43:17.547153
43306	e608752e-5a87-4e44-a449-b22d6abfc0c3	lo003	go001.lo003.out024	\N	2025-05-04 11:43:17.547153
43307	e608752e-5a87-4e44-a449-b22d6abfc0c3	lo003	go001.lo003.out025	\N	2025-05-04 11:43:17.547153
43308	e608752e-5a87-4e44-a449-b22d6abfc0c3	lo003	go001.lo003.out026	\N	2025-05-04 11:43:17.547153
43309	e608752e-5a87-4e44-a449-b22d6abfc0c3	lo003	go001.lo003.out027	\N	2025-05-04 11:43:17.547153
43310	e608752e-5a87-4e44-a449-b22d6abfc0c3	lo003	go001.lo003.out028	\N	2025-05-04 11:43:17.547153
43311	e608752e-5a87-4e44-a449-b22d6abfc0c3	lo003	go001.lo003.out033	\N	2025-05-04 11:43:17.547153
43312	e608752e-5a87-4e44-a449-b22d6abfc0c3	lo003	go001.lo003.out034	\N	2025-05-04 11:43:17.547153
43318	d2f342dc-5038-45a6-b598-84effe01d9a8	lo001	go001.lo001.out006	\N	2025-05-04 12:11:32.934725
43322	d2f342dc-5038-45a6-b598-84effe01d9a8	lo001	go001.lo001.out030	\N	2025-05-04 12:11:32.934725
43323	d2f342dc-5038-45a6-b598-84effe01d9a8	lo002	go001.lo002.out009	\N	2025-05-04 12:11:32.934725
43324	d2f342dc-5038-45a6-b598-84effe01d9a8	lo002	go001.lo002.out010	\N	2025-05-04 12:11:32.934725
43325	d2f342dc-5038-45a6-b598-84effe01d9a8	lo002	go001.lo002.out011	\N	2025-05-04 12:11:32.934725
43326	d2f342dc-5038-45a6-b598-84effe01d9a8	lo002	go001.lo002.out012	\N	2025-05-04 12:11:32.934725
43327	d2f342dc-5038-45a6-b598-84effe01d9a8	lo002	go001.lo002.out013	\N	2025-05-04 12:11:32.934725
43328	d2f342dc-5038-45a6-b598-84effe01d9a8	lo002	go001.lo002.out014	\N	2025-05-04 12:11:32.934725
43329	d2f342dc-5038-45a6-b598-84effe01d9a8	lo002	go001.lo002.out015	\N	2025-05-04 12:11:32.934725
43330	d2f342dc-5038-45a6-b598-84effe01d9a8	lo002	go001.lo002.out016	\N	2025-05-04 12:11:32.934725
43331	d2f342dc-5038-45a6-b598-84effe01d9a8	lo002	go001.lo002.out017	\N	2025-05-04 12:11:32.934725
43332	d2f342dc-5038-45a6-b598-84effe01d9a8	lo002	go001.lo002.out018	\N	2025-05-04 12:11:32.934725
43333	d2f342dc-5038-45a6-b598-84effe01d9a8	lo002	go001.lo002.out031	\N	2025-05-04 12:11:32.934725
43334	d2f342dc-5038-45a6-b598-84effe01d9a8	lo002	go001.lo002.out032	\N	2025-05-04 12:11:32.934725
43335	d2f342dc-5038-45a6-b598-84effe01d9a8	lo003	go001.lo003.out019	\N	2025-05-04 12:11:32.934725
43336	d2f342dc-5038-45a6-b598-84effe01d9a8	lo003	go001.lo003.out020	\N	2025-05-04 12:11:32.934725
43337	d2f342dc-5038-45a6-b598-84effe01d9a8	lo003	go001.lo003.out021	\N	2025-05-04 12:11:32.934725
43338	d2f342dc-5038-45a6-b598-84effe01d9a8	lo003	go001.lo003.out022	\N	2025-05-04 12:11:32.934725
43339	d2f342dc-5038-45a6-b598-84effe01d9a8	lo003	go001.lo003.out023	\N	2025-05-04 12:11:32.934725
43340	d2f342dc-5038-45a6-b598-84effe01d9a8	lo003	go001.lo003.out024	\N	2025-05-04 12:11:32.934725
43341	d2f342dc-5038-45a6-b598-84effe01d9a8	lo003	go001.lo003.out025	\N	2025-05-04 12:11:32.934725
43342	d2f342dc-5038-45a6-b598-84effe01d9a8	lo003	go001.lo003.out026	\N	2025-05-04 12:11:32.934725
43343	d2f342dc-5038-45a6-b598-84effe01d9a8	lo003	go001.lo003.out027	\N	2025-05-04 12:11:32.934725
43344	d2f342dc-5038-45a6-b598-84effe01d9a8	lo003	go001.lo003.out028	\N	2025-05-04 12:11:32.934725
43345	d2f342dc-5038-45a6-b598-84effe01d9a8	lo003	go001.lo003.out033	\N	2025-05-04 12:11:32.934725
43346	d2f342dc-5038-45a6-b598-84effe01d9a8	lo003	go001.lo003.out034	\N	2025-05-04 12:11:32.934725
43314	d2f342dc-5038-45a6-b598-84effe01d9a8	lo001	go001.lo001.out002	1	2025-05-04 12:11:32.934725
43315	d2f342dc-5038-45a6-b598-84effe01d9a8	lo001	go001.lo001.out003	"Sample Employee ID"	2025-05-04 12:11:32.934725
43316	d2f342dc-5038-45a6-b598-84effe01d9a8	lo001	go001.lo001.out004	"2025-05-04"	2025-05-04 12:11:32.934725
43317	d2f342dc-5038-45a6-b598-84effe01d9a8	lo001	go001.lo001.out005	"2025-05-04"	2025-05-04 12:11:32.934725
43319	d2f342dc-5038-45a6-b598-84effe01d9a8	lo001	go001.lo001.out007	"Sample Leave Reason"	2025-05-04 12:11:32.934725
43320	d2f342dc-5038-45a6-b598-84effe01d9a8	lo001	go001.lo001.out008	"PENDING"	2025-05-04 12:11:32.934725
43321	d2f342dc-5038-45a6-b598-84effe01d9a8	lo001	go001.lo001.out029	"annual leave"	2025-05-04 12:11:32.934725
43313	d2f342dc-5038-45a6-b598-84effe01d9a8	lo001	go001.lo001.out001	"Success"	2025-05-04 12:11:32.934725
43352	87944dfa-47bc-4235-a6ed-f8440758a0b4	lo001	go001.lo001.out006	\N	2025-05-04 12:15:15.102869
43356	87944dfa-47bc-4235-a6ed-f8440758a0b4	lo001	go001.lo001.out030	\N	2025-05-04 12:15:15.102869
43357	87944dfa-47bc-4235-a6ed-f8440758a0b4	lo002	go001.lo002.out009	\N	2025-05-04 12:15:15.102869
43358	87944dfa-47bc-4235-a6ed-f8440758a0b4	lo002	go001.lo002.out010	\N	2025-05-04 12:15:15.102869
43359	87944dfa-47bc-4235-a6ed-f8440758a0b4	lo002	go001.lo002.out011	\N	2025-05-04 12:15:15.102869
43360	87944dfa-47bc-4235-a6ed-f8440758a0b4	lo002	go001.lo002.out012	\N	2025-05-04 12:15:15.102869
43361	87944dfa-47bc-4235-a6ed-f8440758a0b4	lo002	go001.lo002.out013	\N	2025-05-04 12:15:15.102869
43362	87944dfa-47bc-4235-a6ed-f8440758a0b4	lo002	go001.lo002.out014	\N	2025-05-04 12:15:15.102869
43363	87944dfa-47bc-4235-a6ed-f8440758a0b4	lo002	go001.lo002.out015	\N	2025-05-04 12:15:15.102869
43364	87944dfa-47bc-4235-a6ed-f8440758a0b4	lo002	go001.lo002.out016	\N	2025-05-04 12:15:15.102869
43365	87944dfa-47bc-4235-a6ed-f8440758a0b4	lo002	go001.lo002.out017	\N	2025-05-04 12:15:15.102869
43348	87944dfa-47bc-4235-a6ed-f8440758a0b4	lo001	go001.lo001.out002	2	2025-05-04 12:15:15.102869
43349	87944dfa-47bc-4235-a6ed-f8440758a0b4	lo001	go001.lo001.out003	"EMP-001"	2025-05-04 12:15:15.102869
43350	87944dfa-47bc-4235-a6ed-f8440758a0b4	lo001	go001.lo001.out004	"2025-05-01"	2025-05-04 12:15:15.102869
43351	87944dfa-47bc-4235-a6ed-f8440758a0b4	lo001	go001.lo001.out005	"2025-05-05"	2025-05-04 12:15:15.102869
43353	87944dfa-47bc-4235-a6ed-f8440758a0b4	lo001	go001.lo001.out007	"Annual vacation"	2025-05-04 12:15:15.102869
43354	87944dfa-47bc-4235-a6ed-f8440758a0b4	lo001	go001.lo001.out008	"PENDING"	2025-05-04 12:15:15.102869
43366	87944dfa-47bc-4235-a6ed-f8440758a0b4	lo002	go001.lo002.out018	\N	2025-05-04 12:15:15.102869
43367	87944dfa-47bc-4235-a6ed-f8440758a0b4	lo002	go001.lo002.out031	\N	2025-05-04 12:15:15.102869
43368	87944dfa-47bc-4235-a6ed-f8440758a0b4	lo002	go001.lo002.out032	\N	2025-05-04 12:15:15.102869
43369	87944dfa-47bc-4235-a6ed-f8440758a0b4	lo003	go001.lo003.out019	\N	2025-05-04 12:15:15.102869
43370	87944dfa-47bc-4235-a6ed-f8440758a0b4	lo003	go001.lo003.out020	\N	2025-05-04 12:15:15.102869
43371	87944dfa-47bc-4235-a6ed-f8440758a0b4	lo003	go001.lo003.out021	\N	2025-05-04 12:15:15.102869
43372	87944dfa-47bc-4235-a6ed-f8440758a0b4	lo003	go001.lo003.out022	\N	2025-05-04 12:15:15.102869
43373	87944dfa-47bc-4235-a6ed-f8440758a0b4	lo003	go001.lo003.out023	\N	2025-05-04 12:15:15.102869
43374	87944dfa-47bc-4235-a6ed-f8440758a0b4	lo003	go001.lo003.out024	\N	2025-05-04 12:15:15.102869
43375	87944dfa-47bc-4235-a6ed-f8440758a0b4	lo003	go001.lo003.out025	\N	2025-05-04 12:15:15.102869
43376	87944dfa-47bc-4235-a6ed-f8440758a0b4	lo003	go001.lo003.out026	\N	2025-05-04 12:15:15.102869
43377	87944dfa-47bc-4235-a6ed-f8440758a0b4	lo003	go001.lo003.out027	\N	2025-05-04 12:15:15.102869
43378	87944dfa-47bc-4235-a6ed-f8440758a0b4	lo003	go001.lo003.out028	\N	2025-05-04 12:15:15.102869
43379	87944dfa-47bc-4235-a6ed-f8440758a0b4	lo003	go001.lo003.out033	\N	2025-05-04 12:15:15.102869
43380	87944dfa-47bc-4235-a6ed-f8440758a0b4	lo003	go001.lo003.out034	\N	2025-05-04 12:15:15.102869
43355	87944dfa-47bc-4235-a6ed-f8440758a0b4	lo001	go001.lo001.out029	"annual leave"	2025-05-04 12:15:15.102869
43347	87944dfa-47bc-4235-a6ed-f8440758a0b4	lo001	go001.lo001.out001	"Success"	2025-05-04 12:15:15.102869
43386	aa2b73e1-5f04-4c78-8d1f-4fb251f5bbe5	lo001	go001.lo001.out006	\N	2025-05-04 12:29:00.657544
43390	aa2b73e1-5f04-4c78-8d1f-4fb251f5bbe5	lo001	go001.lo001.out030	\N	2025-05-04 12:29:00.657544
43391	aa2b73e1-5f04-4c78-8d1f-4fb251f5bbe5	lo002	go001.lo002.out009	\N	2025-05-04 12:29:00.657544
43392	aa2b73e1-5f04-4c78-8d1f-4fb251f5bbe5	lo002	go001.lo002.out010	\N	2025-05-04 12:29:00.657544
43393	aa2b73e1-5f04-4c78-8d1f-4fb251f5bbe5	lo002	go001.lo002.out011	\N	2025-05-04 12:29:00.657544
43394	aa2b73e1-5f04-4c78-8d1f-4fb251f5bbe5	lo002	go001.lo002.out012	\N	2025-05-04 12:29:00.657544
43395	aa2b73e1-5f04-4c78-8d1f-4fb251f5bbe5	lo002	go001.lo002.out013	\N	2025-05-04 12:29:00.657544
43396	aa2b73e1-5f04-4c78-8d1f-4fb251f5bbe5	lo002	go001.lo002.out014	\N	2025-05-04 12:29:00.657544
43397	aa2b73e1-5f04-4c78-8d1f-4fb251f5bbe5	lo002	go001.lo002.out015	\N	2025-05-04 12:29:00.657544
43398	aa2b73e1-5f04-4c78-8d1f-4fb251f5bbe5	lo002	go001.lo002.out016	\N	2025-05-04 12:29:00.657544
43399	aa2b73e1-5f04-4c78-8d1f-4fb251f5bbe5	lo002	go001.lo002.out017	\N	2025-05-04 12:29:00.657544
43400	aa2b73e1-5f04-4c78-8d1f-4fb251f5bbe5	lo002	go001.lo002.out018	\N	2025-05-04 12:29:00.657544
43401	aa2b73e1-5f04-4c78-8d1f-4fb251f5bbe5	lo002	go001.lo002.out031	\N	2025-05-04 12:29:00.657544
43402	aa2b73e1-5f04-4c78-8d1f-4fb251f5bbe5	lo002	go001.lo002.out032	\N	2025-05-04 12:29:00.657544
43403	aa2b73e1-5f04-4c78-8d1f-4fb251f5bbe5	lo003	go001.lo003.out019	\N	2025-05-04 12:29:00.657544
43404	aa2b73e1-5f04-4c78-8d1f-4fb251f5bbe5	lo003	go001.lo003.out020	\N	2025-05-04 12:29:00.657544
43405	aa2b73e1-5f04-4c78-8d1f-4fb251f5bbe5	lo003	go001.lo003.out021	\N	2025-05-04 12:29:00.657544
43406	aa2b73e1-5f04-4c78-8d1f-4fb251f5bbe5	lo003	go001.lo003.out022	\N	2025-05-04 12:29:00.657544
43407	aa2b73e1-5f04-4c78-8d1f-4fb251f5bbe5	lo003	go001.lo003.out023	\N	2025-05-04 12:29:00.657544
43408	aa2b73e1-5f04-4c78-8d1f-4fb251f5bbe5	lo003	go001.lo003.out024	\N	2025-05-04 12:29:00.657544
43409	aa2b73e1-5f04-4c78-8d1f-4fb251f5bbe5	lo003	go001.lo003.out025	\N	2025-05-04 12:29:00.657544
43410	aa2b73e1-5f04-4c78-8d1f-4fb251f5bbe5	lo003	go001.lo003.out026	\N	2025-05-04 12:29:00.657544
43411	aa2b73e1-5f04-4c78-8d1f-4fb251f5bbe5	lo003	go001.lo003.out027	\N	2025-05-04 12:29:00.657544
43412	aa2b73e1-5f04-4c78-8d1f-4fb251f5bbe5	lo003	go001.lo003.out028	\N	2025-05-04 12:29:00.657544
43413	aa2b73e1-5f04-4c78-8d1f-4fb251f5bbe5	lo003	go001.lo003.out033	\N	2025-05-04 12:29:00.657544
43414	aa2b73e1-5f04-4c78-8d1f-4fb251f5bbe5	lo003	go001.lo003.out034	\N	2025-05-04 12:29:00.657544
43382	aa2b73e1-5f04-4c78-8d1f-4fb251f5bbe5	lo001	go001.lo001.out002	3	2025-05-04 12:29:00.657544
43383	aa2b73e1-5f04-4c78-8d1f-4fb251f5bbe5	lo001	go001.lo001.out003	"EMP-001"	2025-05-04 12:29:00.657544
43384	aa2b73e1-5f04-4c78-8d1f-4fb251f5bbe5	lo001	go001.lo001.out004	"2025-05-01"	2025-05-04 12:29:00.657544
43385	aa2b73e1-5f04-4c78-8d1f-4fb251f5bbe5	lo001	go001.lo001.out005	"2025-05-05"	2025-05-04 12:29:00.657544
43387	aa2b73e1-5f04-4c78-8d1f-4fb251f5bbe5	lo001	go001.lo001.out007	"Annual vacation"	2025-05-04 12:29:00.657544
43388	aa2b73e1-5f04-4c78-8d1f-4fb251f5bbe5	lo001	go001.lo001.out008	"PENDING"	2025-05-04 12:29:00.657544
43389	aa2b73e1-5f04-4c78-8d1f-4fb251f5bbe5	lo001	go001.lo001.out029	"annual leave"	2025-05-04 12:29:00.657544
43381	aa2b73e1-5f04-4c78-8d1f-4fb251f5bbe5	lo001	go001.lo001.out001	"Success"	2025-05-04 12:29:00.657544
\.


--
-- Data for Name: lo_output_items; Type: TABLE DATA; Schema: workflow_runtime; Owner: postgres
--

COPY workflow_runtime.lo_output_items (id, output_stack_id, slot_id, contextual_id, source, created_at, updated_at, lo_id) FROM stdin;
out001	1901	executionstatus.out001	go001.lo001.out001	system	2025-05-04 11:31:09.548741	2025-05-04 11:31:09.548741	lo001
out002	1901	e001.at001.out002	go001.lo001.out002	system	2025-05-04 11:31:09.548741	2025-05-04 11:31:09.548741	lo001
out003	1901	e001.at002.out003	go001.lo001.out003	system	2025-05-04 11:31:09.548741	2025-05-04 11:31:09.548741	lo001
out004	1901	e001.at003.out004	go001.lo001.out004	system	2025-05-04 11:31:09.548741	2025-05-04 11:31:09.548741	lo001
out005	1901	e001.at004.out005	go001.lo001.out005	system	2025-05-04 11:31:09.548741	2025-05-04 11:31:09.548741	lo001
out006	1901	e001.at005.out006	go001.lo001.out006	system	2025-05-04 11:31:09.548741	2025-05-04 11:31:09.548741	lo001
out007	1901	e001.at006.out007	go001.lo001.out007	system	2025-05-04 11:31:09.548741	2025-05-04 11:31:09.548741	lo001
out008	1901	e001.at007.out008	go001.lo001.out008	system	2025-05-04 11:31:09.548741	2025-05-04 11:31:09.548741	lo001
out029	1901	e001.at010.out029	go001.lo001.out029	system	2025-05-04 11:31:09.548741	2025-05-04 11:31:09.548741	lo001
out030	1901	e001.at011.out030	go001.lo001.out030	system	2025-05-04 11:31:09.548741	2025-05-04 11:31:09.548741	lo001
out009	1902	executionstatus.out009	go001.lo002.out009	system	2025-05-04 11:31:09.548741	2025-05-04 11:31:09.548741	lo002
out010	1902	e001.at001.out010	go001.lo002.out010	system	2025-05-04 11:31:09.548741	2025-05-04 11:31:09.548741	lo002
out011	1902	e001.at002.out011	go001.lo002.out011	system	2025-05-04 11:31:09.548741	2025-05-04 11:31:09.548741	lo002
out012	1902	e001.at003.out012	go001.lo002.out012	system	2025-05-04 11:31:09.548741	2025-05-04 11:31:09.548741	lo002
out013	1902	e001.at004.out013	go001.lo002.out013	system	2025-05-04 11:31:09.548741	2025-05-04 11:31:09.548741	lo002
out014	1902	e001.at005.out014	go001.lo002.out014	system	2025-05-04 11:31:09.548741	2025-05-04 11:31:09.548741	lo002
out015	1902	e001.at006.out015	go001.lo002.out015	system	2025-05-04 11:31:09.548741	2025-05-04 11:31:09.548741	lo002
out016	1902	e001.at007.out016	go001.lo002.out016	system	2025-05-04 11:31:09.548741	2025-05-04 11:31:09.548741	lo002
out017	1902	e001.at008.out017	go001.lo002.out017	system	2025-05-04 11:31:09.548741	2025-05-04 11:31:09.548741	lo002
out018	1902	e001.at009.out018	go001.lo002.out018	system	2025-05-04 11:31:09.548741	2025-05-04 11:31:09.548741	lo002
out031	1902	e001.at010.out031	go001.lo002.out031	system	2025-05-04 11:31:09.548741	2025-05-04 11:31:09.548741	lo002
out032	1902	e001.at011.out032	go001.lo002.out032	system	2025-05-04 11:31:09.548741	2025-05-04 11:31:09.548741	lo002
out019	1903	executionstatus.out019	go001.lo003.out019	system	2025-05-04 11:31:09.548741	2025-05-04 11:31:09.548741	lo003
out020	1903	e001.at001.out020	go001.lo003.out020	system	2025-05-04 11:31:09.548741	2025-05-04 11:31:09.548741	lo003
out021	1903	e001.at002.out021	go001.lo003.out021	system	2025-05-04 11:31:09.548741	2025-05-04 11:31:09.548741	lo003
out022	1903	e001.at003.out022	go001.lo003.out022	system	2025-05-04 11:31:09.548741	2025-05-04 11:31:09.548741	lo003
out023	1903	e001.at004.out023	go001.lo003.out023	system	2025-05-04 11:31:09.548741	2025-05-04 11:31:09.548741	lo003
out024	1903	e001.at005.out024	go001.lo003.out024	system	2025-05-04 11:31:09.548741	2025-05-04 11:31:09.548741	lo003
out025	1903	e001.at006.out025	go001.lo003.out025	system	2025-05-04 11:31:09.548741	2025-05-04 11:31:09.548741	lo003
out026	1903	e001.at007.out026	go001.lo003.out026	system	2025-05-04 11:31:09.548741	2025-05-04 11:31:09.548741	lo003
out027	1903	e001.at008.out027	go001.lo003.out027	system	2025-05-04 11:31:09.548741	2025-05-04 11:31:09.548741	lo003
out028	1903	e001.at009.out028	go001.lo003.out028	system	2025-05-04 11:31:09.548741	2025-05-04 11:31:09.548741	lo003
out033	1903	e001.at010.out033	go001.lo003.out033	system	2025-05-04 11:31:09.548741	2025-05-04 11:31:09.548741	lo003
out034	1903	e001.at011.out034	go001.lo003.out034	system	2025-05-04 11:31:09.548741	2025-05-04 11:31:09.548741	lo003
\.


--
-- Data for Name: lo_output_stack; Type: TABLE DATA; Schema: workflow_runtime; Owner: postgres
--

COPY workflow_runtime.lo_output_stack (id, lo_id, description, created_at, updated_at) FROM stdin;
1901	lo001	Leave application created	2025-05-04 11:31:09.548741	2025-05-04 11:31:09.548741
1902	lo002	Leave application updated by manager	2025-05-04 11:31:09.548741	2025-05-04 11:31:09.548741
1903	lo003	Leave application updated by HR manager	2025-05-04 11:31:09.548741	2025-05-04 11:31:09.548741
\.


--
-- Data for Name: lo_output_triggers; Type: TABLE DATA; Schema: workflow_runtime; Owner: postgres
--

COPY workflow_runtime.lo_output_triggers (id, output_item_id, output_stack_id, target_objective, target_input, mapping_type, created_at, updated_at) FROM stdin;
\.


--
-- Data for Name: lo_system_functions; Type: TABLE DATA; Schema: workflow_runtime; Owner: postgres
--

COPY workflow_runtime.lo_system_functions (function_id, lo_id, function_name, function_type, parameters, output_to, created_at, updated_at) FROM stdin;
\.


--
-- Data for Name: local_objectives; Type: TABLE DATA; Schema: workflow_runtime; Owner: postgres
--

COPY workflow_runtime.local_objectives (lo_id, contextual_id, name, function_type, workflow_source, go_id, created_at, updated_at, system_function) FROM stdin;
lo001	go001.lo001	Apply for Leave	Create	origin	go001	2025-05-04 11:31:09.548741	2025-05-04 11:31:09.548741	\N
lo002	go001.lo002	Manager Approval	Update	intermediate	go001	2025-05-04 11:31:09.548741	2025-05-04 11:31:09.548741	\N
lo003	go001.lo003	HR Manager Approval	Update	intermediate	go001	2025-05-04 11:31:09.548741	2025-05-04 11:31:09.548741	\N
\.


--
-- Data for Name: mapping_rules; Type: TABLE DATA; Schema: workflow_runtime; Owner: postgres
--

COPY workflow_runtime.mapping_rules (id, mapping_stack_id, description, condition_type, condition_entity, condition_attribute, condition_operator, condition_value, error_message, created_at, updated_at) FROM stdin;
\.


--
-- Data for Name: metrics_aggregation; Type: TABLE DATA; Schema: workflow_runtime; Owner: postgres
--

COPY workflow_runtime.metrics_aggregation (id, metrics_stack_id, attribute_name, aggregation_function, created_at, updated_at) FROM stdin;
\.


--
-- Data for Name: metrics_reporting; Type: TABLE DATA; Schema: workflow_runtime; Owner: postgres
--

COPY workflow_runtime.metrics_reporting (id, metrics_stack_id, generate_execution_summary, store_summary_location, error_count_threshold, duration_percentile, created_at, updated_at) FROM stdin;
\.


--
-- Data for Name: objective_permissions; Type: TABLE DATA; Schema: workflow_runtime; Owner: postgres
--

COPY workflow_runtime.objective_permissions (id, role_id, objective_id, permission_id, created_at, updated_at) FROM stdin;
3000	r001	go001.lo001	execute	2025-05-04 11:31:09.548741	2025-05-04 11:31:09.548741
3001	r002	go001.lo002	execute	2025-05-04 11:31:09.548741	2025-05-04 11:31:09.548741
3002	r003	go001.lo003	execute	2025-05-04 11:31:09.548741	2025-05-04 11:31:09.548741
\.


--
-- Data for Name: organizational_units; Type: TABLE DATA; Schema: workflow_runtime; Owner: postgres
--

COPY workflow_runtime.organizational_units (org_unit_id, name, parent_org_unit_id, tenant_id, description, created_at, updated_at) FROM stdin;
org_it	IT Department	\N	t001	Information Technology Department	2025-05-04 11:40:20.744492	2025-05-04 11:40:20.744492
\.


--
-- Data for Name: output_items; Type: TABLE DATA; Schema: workflow_runtime; Owner: postgres
--

COPY workflow_runtime.output_items (id, output_stack_id, slot_id, contextual_id, output_entity, output_attribute, data_type, created_at, updated_at) FROM stdin;
\.


--
-- Data for Name: output_stack; Type: TABLE DATA; Schema: workflow_runtime; Owner: postgres
--

COPY workflow_runtime.output_stack (id, go_id, description, created_at, updated_at) FROM stdin;
1073	go001	Global outputs	2025-05-04 11:31:09.548741	2025-05-04 11:31:09.548741
\.


--
-- Data for Name: output_triggers; Type: TABLE DATA; Schema: workflow_runtime; Owner: postgres
--

COPY workflow_runtime.output_triggers (id, output_item_id, output_stack_id, target_objective, target_input, mapping_type, condition_type, condition_entity, condition_attribute, condition_operator, condition_value, created_at, updated_at) FROM stdin;
\.


--
-- Data for Name: permission_capabilities; Type: TABLE DATA; Schema: workflow_runtime; Owner: postgres
--

COPY workflow_runtime.permission_capabilities (capability_id, permission_id) FROM stdin;
\.


--
-- Data for Name: permission_contexts; Type: TABLE DATA; Schema: workflow_runtime; Owner: postgres
--

COPY workflow_runtime.permission_contexts (context_id, name, description, context_type, context_rules, created_at, updated_at) FROM stdin;
\.


--
-- Data for Name: permission_types; Type: TABLE DATA; Schema: workflow_runtime; Owner: postgres
--

COPY workflow_runtime.permission_types (permission_id, description, created_at, updated_at) FROM stdin;
read	Can read entity data	2025-05-04 11:31:09.550235	2025-05-04 11:31:09.550235
create	Can create new records	2025-05-04 11:31:09.550634	2025-05-04 11:31:09.550634
update	Can update records	2025-05-04 11:31:09.550874	2025-05-04 11:31:09.550874
delete	Can delete records	2025-05-04 11:31:09.551135	2025-05-04 11:31:09.551135
execute	Can execute workflows	2025-05-04 11:31:09.551442	2025-05-04 11:31:09.551442
\.


--
-- Data for Name: role; Type: TABLE DATA; Schema: workflow_runtime; Owner: postgres
--

COPY workflow_runtime.role (id, created_at, updated_at, created_by, updated_by, role_id, name, description, inherits_from, tenant_id) FROM stdin;
\.


--
-- Data for Name: role_permissions; Type: TABLE DATA; Schema: workflow_runtime; Owner: postgres
--

COPY workflow_runtime.role_permissions (id, role_id, context_id, created_at, updated_at) FROM stdin;
\.


--
-- Data for Name: roles; Type: TABLE DATA; Schema: workflow_runtime; Owner: postgres
--

COPY workflow_runtime.roles (role_id, name, tenant_id, inherits_from, created_at, updated_at) FROM stdin;
r001	Employee	t001	\N	2025-05-04 11:31:09.548741	2025-05-04 11:31:09.548741
r002	Manager	t001	\N	2025-05-04 11:31:09.548741	2025-05-04 11:31:09.548741
r003	HR Manager	t001	\N	2025-05-04 11:31:09.548741	2025-05-04 11:31:09.548741
	Auto-generated 	t001	\N	2025-05-04 11:31:09.548741	2025-05-04 11:31:09.548741
r005	Administrator	t001	\N	2025-05-04 11:40:20.741898	2025-05-04 11:40:20.741898
\.


--
-- Data for Name: runtime_metrics_stack; Type: TABLE DATA; Schema: workflow_runtime; Owner: postgres
--

COPY workflow_runtime.runtime_metrics_stack (id, go_id, description, metrics_entity, created_at, updated_at) FROM stdin;
\.


--
-- Data for Name: success_messages; Type: TABLE DATA; Schema: workflow_runtime; Owner: postgres
--

COPY workflow_runtime.success_messages (lo_id, message, created_at, updated_at) FROM stdin;
\.


--
-- Data for Name: system_functions; Type: TABLE DATA; Schema: workflow_runtime; Owner: postgres
--

COPY workflow_runtime.system_functions (function_id, function_name, function_type, stack_type, stack_id, parameters, output_to, created_at, updated_at) FROM stdin;
\.


--
-- Data for Name: team_availability; Type: TABLE DATA; Schema: workflow_runtime; Owner: postgres
--

COPY workflow_runtime.team_availability (id, department, designation, date, total_employees, available_employees, on_leave_employees) FROM stdin;
\.


--
-- Data for Name: tenants; Type: TABLE DATA; Schema: workflow_runtime; Owner: postgres
--

COPY workflow_runtime.tenants (tenant_id, name, created_at, updated_at) FROM stdin;
t001	leavemanagement001	2025-05-04 11:31:09.548741	2025-05-04 11:31:09.548741
\.


--
-- Data for Name: terminal_pathways; Type: TABLE DATA; Schema: workflow_runtime; Owner: postgres
--

COPY workflow_runtime.terminal_pathways (lo_id, terminal_type, created_at, updated_at) FROM stdin;
lo002	end	2025-05-04 11:31:09.548741	2025-05-04 11:31:09.548741
lo003	end	2025-05-04 11:31:09.548741	2025-05-04 11:31:09.548741
\.


--
-- Data for Name: ui_elements; Type: TABLE DATA; Schema: workflow_runtime; Owner: postgres
--

COPY workflow_runtime.ui_elements (id, ui_stack_id, entity_attribute, ui_control, helper_text, format_as, options, display_properties, enable_condition, visibility_condition, required_condition, display_text, created_at, updated_at) FROM stdin;
\.


--
-- Data for Name: ui_stack; Type: TABLE DATA; Schema: workflow_runtime; Owner: postgres
--

COPY workflow_runtime.ui_stack (id, lo_id, type, status, description, created_at, updated_at) FROM stdin;
\.


--
-- Data for Name: user; Type: TABLE DATA; Schema: workflow_runtime; Owner: postgres
--

COPY workflow_runtime."user" (id, created_at, updated_at, created_by, updated_by, user_id, username, email, first_name, last_name, status, password_hash, disabled, organization, team) FROM stdin;
\.


--
-- Data for Name: user_oauth_tokens; Type: TABLE DATA; Schema: workflow_runtime; Owner: postgres
--

COPY workflow_runtime.user_oauth_tokens (token_id, user_id, access_token, refresh_token, token_type, expires_at, scope, created_at, updated_at) FROM stdin;
db970803-4b81-43b4-b321-5b9a7810c185	5546918c-ce0a-4219-8eae-af1dd461206e	eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiI1NTQ2OTE4Yy1jZTBhLTQyMTktOGVhZS1hZjFkZDQ2MTIwNmUiLCJ1c2VybmFtZSI6InRlc3R1c2VyMiIsInJvbGVzIjpbIkFkbWluaXN0cmF0b3IiXSwicGVybWlzc2lvbnMiOltdLCJvcmdfdW5pdHMiOlsib3JnX2l0Il0sInRlbmFudF9pZCI6InQwMDEiLCJleHAiOjE3NDYzNjA3Mzd9.2H11O7HiM3WRjje7P2bcQACLbObxCCXNb2Nst6UhVmw	eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiI1NTQ2OTE4Yy1jZTBhLTQyMTktOGVhZS1hZjFkZDQ2MTIwNmUiLCJleHAiOjE3NDY5NjM3Mzd9.CG_i5Om7eOqqUjcBttwXCbhFH7ev8jazTDKXjUwKijc	Bearer	2025-05-04 12:12:17.175376	\N	2025-05-04 11:42:17.173547	2025-05-04 11:42:17.173547
1944e347-6f69-4b2e-b843-7c10b4da7bd4	5546918c-ce0a-4219-8eae-af1dd461206e	eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiI1NTQ2OTE4Yy1jZTBhLTQyMTktOGVhZS1hZjFkZDQ2MTIwNmUiLCJ1c2VybmFtZSI6InRlc3R1c2VyMiIsInJvbGVzIjpbIkFkbWluaXN0cmF0b3IiXSwicGVybWlzc2lvbnMiOltdLCJvcmdfdW5pdHMiOlsib3JnX2l0Il0sInRlbmFudF9pZCI6InQwMDEiLCJleHAiOjE3NDYzNjA3NjZ9.gUw4X4D6x2O86FIQ719OaTxQZtF4c8E_jGb5Fr-L9QY	eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiI1NTQ2OTE4Yy1jZTBhLTQyMTktOGVhZS1hZjFkZDQ2MTIwNmUiLCJleHAiOjE3NDY5NjM3NjZ9.Dfyg2pAVwZ4Vo2kBAndyuI7MEAXPlYPpM3czppuYDgI	Bearer	2025-05-04 12:12:46.892861	\N	2025-05-04 11:42:46.887756	2025-05-04 11:42:46.887756
49589eb1-2ac8-4620-a6b0-e7ed6bc582d8	5546918c-ce0a-4219-8eae-af1dd461206e	eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiI1NTQ2OTE4Yy1jZTBhLTQyMTktOGVhZS1hZjFkZDQ2MTIwNmUiLCJ1c2VybmFtZSI6InRlc3R1c2VyMiIsInJvbGVzIjpbIkFkbWluaXN0cmF0b3IiXSwicGVybWlzc2lvbnMiOltdLCJvcmdfdW5pdHMiOlsib3JnX2l0Il0sInRlbmFudF9pZCI6InQwMDEiLCJleHAiOjE3NDYzNjI0OTJ9.EqeNgmv9TGB7GCXH0QANi2UaKCUJfri9vdjMzfM8M0Y	eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiI1NTQ2OTE4Yy1jZTBhLTQyMTktOGVhZS1hZjFkZDQ2MTIwNmUiLCJleHAiOjE3NDY5NjU0OTJ9.20QSgnNtLlTnzG6O2PBEnvjb0ZJtImlrYPW0r92Vdxw	Bearer	2025-05-04 12:41:32.894073	\N	2025-05-04 12:11:32.89198	2025-05-04 12:11:32.89198
76f5e1aa-1686-40bd-b4d2-4e9373ab89f1	5546918c-ce0a-4219-8eae-af1dd461206e	eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiI1NTQ2OTE4Yy1jZTBhLTQyMTktOGVhZS1hZjFkZDQ2MTIwNmUiLCJ1c2VybmFtZSI6InRlc3R1c2VyMiIsInJvbGVzIjpbIkFkbWluaXN0cmF0b3IiXSwicGVybWlzc2lvbnMiOltdLCJvcmdfdW5pdHMiOlsib3JnX2l0Il0sInRlbmFudF9pZCI6InQwMDEiLCJleHAiOjE3NDYzNjI2NTN9.A8TPrNg-os7WNyNdqZyiebwJTzIa5v0tQ7wyHJcNchw	eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiI1NTQ2OTE4Yy1jZTBhLTQyMTktOGVhZS1hZjFkZDQ2MTIwNmUiLCJleHAiOjE3NDY5NjU2NTN9.n8lbuBCcv0vDtD-aVqlN9SALFccIqV-2qMa_3-zhO54	Bearer	2025-05-04 12:44:13.12937	\N	2025-05-04 12:14:13.128241	2025-05-04 12:14:13.128241
acbdb9a4-44b3-42da-958b-29edec696724	5546918c-ce0a-4219-8eae-af1dd461206e	eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiI1NTQ2OTE4Yy1jZTBhLTQyMTktOGVhZS1hZjFkZDQ2MTIwNmUiLCJ1c2VybmFtZSI6InRlc3R1c2VyMiIsInJvbGVzIjpbIkFkbWluaXN0cmF0b3IiXSwicGVybWlzc2lvbnMiOltdLCJvcmdfdW5pdHMiOlsib3JnX2l0Il0sInRlbmFudF9pZCI6InQwMDEiLCJleHAiOjE3NDYzNjM1MDd9.bbdPCVajC08Whrectyw_8NPcnkum1HEADZEW98btG_4	eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiI1NTQ2OTE4Yy1jZTBhLTQyMTktOGVhZS1hZjFkZDQ2MTIwNmUiLCJleHAiOjE3NDY5NjY1MDd9.3HNKHjRzhG5Ns6ASfZ6tbI8ljlgY-y4pVLOo1t83dJ8	Bearer	2025-05-04 12:58:27.863991	\N	2025-05-04 12:28:27.862088	2025-05-04 12:28:27.862088
\.


--
-- Data for Name: user_organizations; Type: TABLE DATA; Schema: workflow_runtime; Owner: postgres
--

COPY workflow_runtime.user_organizations (id, user_id, org_unit_id, is_primary, created_at, updated_at) FROM stdin;
18	5546918c-ce0a-4219-8eae-af1dd461206e	org_it	t	2025-05-04 11:42:05.896448	2025-05-04 11:42:05.896448
\.


--
-- Data for Name: user_roles; Type: TABLE DATA; Schema: workflow_runtime; Owner: postgres
--

COPY workflow_runtime.user_roles (user_id, username, role, tenant_id) FROM stdin;
5546918c-ce0a-4219-8eae-af1dd461206e	testuser2	Administrator	t001
\.


--
-- Data for Name: user_sessions; Type: TABLE DATA; Schema: workflow_runtime; Owner: postgres
--

COPY workflow_runtime.user_sessions (session_id, user_id, token, ip_address, user_agent, expires_at, created_at, updated_at) FROM stdin;
fb2ee624-96af-4d38-a269-42dabbb30609	5546918c-ce0a-4219-8eae-af1dd461206e	eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiI1NTQ2OTE4Yy1jZTBhLTQyMTktOGVhZS1hZjFkZDQ2MTIwNmUiLCJ1c2VybmFtZSI6InRlc3R1c2VyMiIsInJvbGVzIjpbIkFkbWluaXN0cmF0b3IiXSwicGVybWlzc2lvbnMiOltdLCJvcmdfdW5pdHMiOlsib3JnX2l0Il0sInRlbmFudF9pZCI6InQwMDEiLCJleHAiOjE3NDYzNjA3Mzd9.2H11O7HiM3WRjje7P2bcQACLbObxCCXNb2Nst6UhVmw	**********	curl/7.81.0	2025-05-04 12:12:17.176422	2025-05-04 11:42:17.176632	2025-05-04 11:42:17.176632
ce895bb7-428f-4fa5-97f4-88aab0afaa2d	5546918c-ce0a-4219-8eae-af1dd461206e	eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiI1NTQ2OTE4Yy1jZTBhLTQyMTktOGVhZS1hZjFkZDQ2MTIwNmUiLCJ1c2VybmFtZSI6InRlc3R1c2VyMiIsInJvbGVzIjpbIkFkbWluaXN0cmF0b3IiXSwicGVybWlzc2lvbnMiOltdLCJvcmdfdW5pdHMiOlsib3JnX2l0Il0sInRlbmFudF9pZCI6InQwMDEiLCJleHAiOjE3NDYzNjA3NjZ9.gUw4X4D6x2O86FIQ719OaTxQZtF4c8E_jGb5Fr-L9QY	**********	curl/7.81.0	2025-05-04 12:12:46.895348	2025-05-04 11:42:46.894906	2025-05-04 11:42:46.894906
73a3575d-ccf3-44bb-8cfb-5d8d353144d7	5546918c-ce0a-4219-8eae-af1dd461206e	eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiI1NTQ2OTE4Yy1jZTBhLTQyMTktOGVhZS1hZjFkZDQ2MTIwNmUiLCJ1c2VybmFtZSI6InRlc3R1c2VyMiIsInJvbGVzIjpbIkFkbWluaXN0cmF0b3IiXSwicGVybWlzc2lvbnMiOltdLCJvcmdfdW5pdHMiOlsib3JnX2l0Il0sInRlbmFudF9pZCI6InQwMDEiLCJleHAiOjE3NDYzNjI0OTJ9.EqeNgmv9TGB7GCXH0QANi2UaKCUJfri9vdjMzfM8M0Y	**********	python-requests/2.32.3	2025-05-04 12:41:32.895239	2025-05-04 12:11:32.895461	2025-05-04 12:11:32.895461
bb063e25-1944-43df-aefd-318fb0f2f4d4	5546918c-ce0a-4219-8eae-af1dd461206e	eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiI1NTQ2OTE4Yy1jZTBhLTQyMTktOGVhZS1hZjFkZDQ2MTIwNmUiLCJ1c2VybmFtZSI6InRlc3R1c2VyMiIsInJvbGVzIjpbIkFkbWluaXN0cmF0b3IiXSwicGVybWlzc2lvbnMiOltdLCJvcmdfdW5pdHMiOlsib3JnX2l0Il0sInRlbmFudF9pZCI6InQwMDEiLCJleHAiOjE3NDYzNjI2NTN9.A8TPrNg-os7WNyNdqZyiebwJTzIa5v0tQ7wyHJcNchw	**********	curl/7.81.0	2025-05-04 12:44:13.129947	2025-05-04 12:14:13.130079	2025-05-04 12:14:13.130079
2274e1b7-a5d6-42e7-9d1e-cac7281cedec	5546918c-ce0a-4219-8eae-af1dd461206e	eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiI1NTQ2OTE4Yy1jZTBhLTQyMTktOGVhZS1hZjFkZDQ2MTIwNmUiLCJ1c2VybmFtZSI6InRlc3R1c2VyMiIsInJvbGVzIjpbIkFkbWluaXN0cmF0b3IiXSwicGVybWlzc2lvbnMiOltdLCJvcmdfdW5pdHMiOlsib3JnX2l0Il0sInRlbmFudF9pZCI6InQwMDEiLCJleHAiOjE3NDYzNjM1MDd9.bbdPCVajC08Whrectyw_8NPcnkum1HEADZEW98btG_4	**********	curl/7.81.0	2025-05-04 12:58:27.86517	2025-05-04 12:28:27.865525	2025-05-04 12:28:27.865525
\.


--
-- Data for Name: users; Type: TABLE DATA; Schema: workflow_runtime; Owner: postgres
--

COPY workflow_runtime.users (user_id, username, email, password_hash, first_name, last_name, status, last_login, created_at, updated_at) FROM stdin;
5546918c-ce0a-4219-8eae-af1dd461206e	testuser2	<EMAIL>	$2b$12$MOEDzMjb0.sr5r.mrCELy.rkM4MZx2m8yyYK5fSuxdMaDIG5abjDS	Test	User	active	2025-05-04 12:28:27.596272	2025-05-04 11:42:05.896448	2025-05-04 12:28:27.596272
\.


--
-- Data for Name: workflow_instances; Type: TABLE DATA; Schema: workflow_runtime; Owner: postgres
--

COPY workflow_runtime.workflow_instances (instance_id, go_id, tenant_id, status, started_by, started_at, current_lo_id, instance_data, is_test, version, updated_at) FROM stdin;
e608752e-5a87-4e44-a449-b22d6abfc0c3	go001	t001	Active	5546918c-ce0a-4219-8eae-af1dd461206e	2025-05-04 11:43:17.542601	lo001	{}	f	1.0	2025-05-04 11:43:32.743283
d2f342dc-5038-45a6-b598-84effe01d9a8	go001	t001	Active	5546918c-ce0a-4219-8eae-af1dd461206e	2025-05-04 12:11:32.933107	lo002	{}	f	1.0	2025-05-04 12:11:32.984051
87944dfa-47bc-4235-a6ed-f8440758a0b4	go001	t001	Active	5546918c-ce0a-4219-8eae-af1dd461206e	2025-05-04 12:15:15.095615	lo003	{}	f	1.0	2025-05-04 12:18:00.909318
aa2b73e1-5f04-4c78-8d1f-4fb251f5bbe5	go001	t001	Active	5546918c-ce0a-4219-8eae-af1dd461206e	2025-05-04 12:29:00.65539	lo003	{}	f	1.0	2025-05-04 12:30:25.848221
\.


--
-- Data for Name: workflow_results; Type: TABLE DATA; Schema: workflow_runtime; Owner: postgres
--

COPY workflow_runtime.workflow_results (id, leaves_pending_approval, leaves_approved_count, leaves_rejected_count, duration, start_time, end_time, status) FROM stdin;
\.


--
-- Data for Name: workflow_transaction; Type: TABLE DATA; Schema: workflow_runtime; Owner: postgres
--

COPY workflow_runtime.workflow_transaction (id, workflow_instance_id, go_id, lo_id, status, input_stack, created_at, updated_at, tenant_id, tenant_name, user_id, output_stack) FROM stdin;
1363	e608752e-5a87-4e44-a449-b22d6abfc0c3	go001		pending	{}	2025-05-04 11:43:17.545751	2025-05-04 11:43:17.545751	t001	\N	5546918c-ce0a-4219-8eae-af1dd461206e	\N
1364	d2f342dc-5038-45a6-b598-84effe01d9a8	go001		pending	{}	2025-05-04 12:11:32.934199	2025-05-04 12:11:32.934199	t001	\N	5546918c-ce0a-4219-8eae-af1dd461206e	\N
1365	d2f342dc-5038-45a6-b598-84effe01d9a8	go001	lo001	pending	[{"attribute_id": "at002", "value": "Sample Employee ID"}, {"attribute_id": "at003", "value": "2025-05-04"}, {"attribute_id": "at004", "value": "2025-05-04"}, {"attribute_id": "at006", "value": "Sample Leave Reason"}, {"attribute_id": "at010", "value": "annual leave"}]	2025-05-04 12:11:32.983241	2025-05-04 12:11:32.983241	t001	\N	5546918c-ce0a-4219-8eae-af1dd461206e	\N
1366	87944dfa-47bc-4235-a6ed-f8440758a0b4	go001		pending	{}	2025-05-04 12:15:15.101848	2025-05-04 12:15:15.101848	t001	\N	5546918c-ce0a-4219-8eae-af1dd461206e	\N
1367	87944dfa-47bc-4235-a6ed-f8440758a0b4	go001	lo001	pending	[{"attribute_id": "at002", "value": "EMP-001"}, {"attribute_id": "at003", "value": "2025-05-01"}, {"attribute_id": "at004", "value": "2025-05-05"}, {"attribute_id": "at006", "value": "Annual vacation"}, {"attribute_id": "at010", "value": "annual leave"}]	2025-05-04 12:18:00.908004	2025-05-04 12:18:00.908004	t001	\N	5546918c-ce0a-4219-8eae-af1dd461206e	\N
1368	aa2b73e1-5f04-4c78-8d1f-4fb251f5bbe5	go001		pending	{}	2025-05-04 12:29:00.656924	2025-05-04 12:29:00.656924	t001	\N	5546918c-ce0a-4219-8eae-af1dd461206e	\N
1369	aa2b73e1-5f04-4c78-8d1f-4fb251f5bbe5	go001	lo001	pending	[{"attribute_id": "at002", "value": "EMP-001"}, {"attribute_id": "at003", "value": "2025-05-01"}, {"attribute_id": "at004", "value": "2025-05-05"}, {"attribute_id": "at006", "value": "Annual vacation"}, {"attribute_id": "at010", "value": "annual leave"}, {"attribute_id": "at011", "value": "AL001"}]	2025-05-04 12:30:25.847038	2025-05-04 12:30:25.847038	t001	\N	5546918c-ce0a-4219-8eae-af1dd461206e	\N
\.


--
-- Name: agent_rights_id_seq; Type: SEQUENCE SET; Schema: workflow_runtime; Owner: postgres
--

SELECT pg_catalog.setval('workflow_runtime.agent_rights_id_seq', 3127, true);


--
-- Name: agent_stack_id_seq; Type: SEQUENCE SET; Schema: workflow_runtime; Owner: postgres
--

SELECT pg_catalog.setval('workflow_runtime.agent_stack_id_seq', 2377, true);


--
-- Name: asset_request_id_seq; Type: SEQUENCE SET; Schema: workflow_runtime; Owner: postgres
--

SELECT pg_catalog.setval('workflow_runtime.asset_request_id_seq', 1, false);


--
-- Name: attribute_enum_values_id_seq; Type: SEQUENCE SET; Schema: workflow_runtime; Owner: postgres
--

SELECT pg_catalog.setval('workflow_runtime.attribute_enum_values_id_seq', 1739, true);


--
-- Name: attribute_validations_id_seq; Type: SEQUENCE SET; Schema: workflow_runtime; Owner: postgres
--

SELECT pg_catalog.setval('workflow_runtime.attribute_validations_id_seq', 2, false);


--
-- Name: attribute_validations_id_seq1; Type: SEQUENCE SET; Schema: workflow_runtime; Owner: postgres
--

SELECT pg_catalog.setval('workflow_runtime.attribute_validations_id_seq1', 34, true);


--
-- Name: conditional_success_messages_id_seq; Type: SEQUENCE SET; Schema: workflow_runtime; Owner: postgres
--

SELECT pg_catalog.setval('workflow_runtime.conditional_success_messages_id_seq', 1, false);


--
-- Name: data_mapping_stack_id_seq; Type: SEQUENCE SET; Schema: workflow_runtime; Owner: postgres
--

SELECT pg_catalog.setval('workflow_runtime.data_mapping_stack_id_seq', 1073, true);


--
-- Name: department_id_seq; Type: SEQUENCE SET; Schema: workflow_runtime; Owner: postgres
--

SELECT pg_catalog.setval('workflow_runtime.department_id_seq', 1, false);


--
-- Name: dropdown_data_sources_id_seq; Type: SEQUENCE SET; Schema: workflow_runtime; Owner: postgres
--

SELECT pg_catalog.setval('workflow_runtime.dropdown_data_sources_id_seq', 85, true);


--
-- Name: entity_permissions_id_seq; Type: SEQUENCE SET; Schema: workflow_runtime; Owner: postgres
--

SELECT pg_catalog.setval('workflow_runtime.entity_permissions_id_seq', 312, true);


--
-- Name: entity_relationships_id_seq; Type: SEQUENCE SET; Schema: workflow_runtime; Owner: postgres
--

SELECT pg_catalog.setval('workflow_runtime.entity_relationships_id_seq', 1, false);


--
-- Name: execution_path_tracking_id_seq; Type: SEQUENCE SET; Schema: workflow_runtime; Owner: postgres
--

SELECT pg_catalog.setval('workflow_runtime.execution_path_tracking_id_seq', 1, false);


--
-- Name: execution_pathway_conditions_id_seq; Type: SEQUENCE SET; Schema: workflow_runtime; Owner: postgres
--

SELECT pg_catalog.setval('workflow_runtime.execution_pathway_conditions_id_seq', 38, true);


--
-- Name: execution_pathways_id_seq; Type: SEQUENCE SET; Schema: workflow_runtime; Owner: postgres
--

SELECT pg_catalog.setval('workflow_runtime.execution_pathways_id_seq', 73, true);


--
-- Name: input_data_sources_id_seq; Type: SEQUENCE SET; Schema: workflow_runtime; Owner: postgres
--

SELECT pg_catalog.setval('workflow_runtime.input_data_sources_id_seq', 1, false);


--
-- Name: input_dependencies_id_seq; Type: SEQUENCE SET; Schema: workflow_runtime; Owner: postgres
--

SELECT pg_catalog.setval('workflow_runtime.input_dependencies_id_seq', 68, true);


--
-- Name: input_stack_id_seq; Type: SEQUENCE SET; Schema: workflow_runtime; Owner: postgres
--

SELECT pg_catalog.setval('workflow_runtime.input_stack_id_seq', 1073, true);


--
-- Name: leave_application_id_seq; Type: SEQUENCE SET; Schema: workflow_runtime; Owner: postgres
--

SELECT pg_catalog.setval('workflow_runtime.leave_application_id_seq', 3, true);


--
-- Name: leave_sub_type_id_seq; Type: SEQUENCE SET; Schema: workflow_runtime; Owner: postgres
--

SELECT pg_catalog.setval('workflow_runtime.leave_sub_type_id_seq', 48, true);


--
-- Name: lo_data_mapping_stack_id_seq; Type: SEQUENCE SET; Schema: workflow_runtime; Owner: postgres
--

SELECT pg_catalog.setval('workflow_runtime.lo_data_mapping_stack_id_seq', 277, true);


--
-- Name: lo_input_execution_id_seq; Type: SEQUENCE SET; Schema: workflow_runtime; Owner: postgres
--

SELECT pg_catalog.setval('workflow_runtime.lo_input_execution_id_seq', 81355, true);


--
-- Name: lo_input_stack_id_seq; Type: SEQUENCE SET; Schema: workflow_runtime; Owner: postgres
--

SELECT pg_catalog.setval('workflow_runtime.lo_input_stack_id_seq', 1919, true);


--
-- Name: lo_input_validations_id_seq; Type: SEQUENCE SET; Schema: workflow_runtime; Owner: postgres
--

SELECT pg_catalog.setval('workflow_runtime.lo_input_validations_id_seq', 1970, true);


--
-- Name: lo_nested_functions_id_seq; Type: SEQUENCE SET; Schema: workflow_runtime; Owner: postgres
--

SELECT pg_catalog.setval('workflow_runtime.lo_nested_functions_id_seq', 1605, true);


--
-- Name: lo_output_execution_id_seq; Type: SEQUENCE SET; Schema: workflow_runtime; Owner: postgres
--

SELECT pg_catalog.setval('workflow_runtime.lo_output_execution_id_seq', 43414, true);


--
-- Name: lo_output_stack_id_seq; Type: SEQUENCE SET; Schema: workflow_runtime; Owner: postgres
--

SELECT pg_catalog.setval('workflow_runtime.lo_output_stack_id_seq', 1903, true);


--
-- Name: metrics_aggregation_id_seq; Type: SEQUENCE SET; Schema: workflow_runtime; Owner: postgres
--

SELECT pg_catalog.setval('workflow_runtime.metrics_aggregation_id_seq', 5, true);


--
-- Name: metrics_reporting_id_seq; Type: SEQUENCE SET; Schema: workflow_runtime; Owner: postgres
--

SELECT pg_catalog.setval('workflow_runtime.metrics_reporting_id_seq', 1, true);


--
-- Name: objective_permissions_id_seq; Type: SEQUENCE SET; Schema: workflow_runtime; Owner: postgres
--

SELECT pg_catalog.setval('workflow_runtime.objective_permissions_id_seq', 3002, true);


--
-- Name: output_stack_id_seq; Type: SEQUENCE SET; Schema: workflow_runtime; Owner: postgres
--

SELECT pg_catalog.setval('workflow_runtime.output_stack_id_seq', 1073, true);


--
-- Name: role_id_seq; Type: SEQUENCE SET; Schema: workflow_runtime; Owner: postgres
--

SELECT pg_catalog.setval('workflow_runtime.role_id_seq', 1, false);


--
-- Name: role_permissions_id_seq; Type: SEQUENCE SET; Schema: workflow_runtime; Owner: postgres
--

SELECT pg_catalog.setval('workflow_runtime.role_permissions_id_seq', 83, true);


--
-- Name: runtime_metrics_stack_id_seq; Type: SEQUENCE SET; Schema: workflow_runtime; Owner: postgres
--

SELECT pg_catalog.setval('workflow_runtime.runtime_metrics_stack_id_seq', 1, true);


--
-- Name: team_availability_id_seq; Type: SEQUENCE SET; Schema: workflow_runtime; Owner: postgres
--

SELECT pg_catalog.setval('workflow_runtime.team_availability_id_seq', 1, false);


--
-- Name: ui_elements_id_seq; Type: SEQUENCE SET; Schema: workflow_runtime; Owner: postgres
--

SELECT pg_catalog.setval('workflow_runtime.ui_elements_id_seq', 2, true);


--
-- Name: ui_stack_id_seq; Type: SEQUENCE SET; Schema: workflow_runtime; Owner: postgres
--

SELECT pg_catalog.setval('workflow_runtime.ui_stack_id_seq', 1, true);


--
-- Name: user_id_seq; Type: SEQUENCE SET; Schema: workflow_runtime; Owner: postgres
--

SELECT pg_catalog.setval('workflow_runtime.user_id_seq', 1, false);


--
-- Name: user_organizations_id_seq; Type: SEQUENCE SET; Schema: workflow_runtime; Owner: postgres
--

SELECT pg_catalog.setval('workflow_runtime.user_organizations_id_seq', 18, true);


--
-- Name: workflow_results_id_seq; Type: SEQUENCE SET; Schema: workflow_runtime; Owner: postgres
--

SELECT pg_catalog.setval('workflow_runtime.workflow_results_id_seq', 1, false);


--
-- Name: workflow_transaction_id_seq; Type: SEQUENCE SET; Schema: workflow_runtime; Owner: postgres
--

SELECT pg_catalog.setval('workflow_runtime.workflow_transaction_id_seq', 1369, true);


--
-- Name: agent_rights agent_rights_pkey; Type: CONSTRAINT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.agent_rights
    ADD CONSTRAINT agent_rights_pkey PRIMARY KEY (id);


--
-- Name: agent_stack agent_stack_pkey; Type: CONSTRAINT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.agent_stack
    ADD CONSTRAINT agent_stack_pkey PRIMARY KEY (id);


--
-- Name: alembic_version alembic_version_pkc; Type: CONSTRAINT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.alembic_version
    ADD CONSTRAINT alembic_version_pkc PRIMARY KEY (version_num);


--
-- Name: asset_request asset_request_pkey; Type: CONSTRAINT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.asset_request
    ADD CONSTRAINT asset_request_pkey PRIMARY KEY (id);


--
-- Name: attribute_enum_values attribute_enum_values_pkey; Type: CONSTRAINT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.attribute_enum_values
    ADD CONSTRAINT attribute_enum_values_pkey PRIMARY KEY (id);


--
-- Name: attribute_ui_controls attribute_ui_controls_pkey; Type: CONSTRAINT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.attribute_ui_controls
    ADD CONSTRAINT attribute_ui_controls_pkey PRIMARY KEY (entity_id, attribute_id);


--
-- Name: attribute_validations attribute_validations_pkey; Type: CONSTRAINT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.attribute_validations
    ADD CONSTRAINT attribute_validations_pkey PRIMARY KEY (id);


--
-- Name: conditional_success_messages conditional_success_messages_pkey; Type: CONSTRAINT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.conditional_success_messages
    ADD CONSTRAINT conditional_success_messages_pkey PRIMARY KEY (id);


--
-- Name: data_mapping_stack data_mapping_stack_pkey; Type: CONSTRAINT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.data_mapping_stack
    ADD CONSTRAINT data_mapping_stack_pkey PRIMARY KEY (id);


--
-- Name: data_mappings data_mappings_pkey; Type: CONSTRAINT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.data_mappings
    ADD CONSTRAINT data_mappings_pkey PRIMARY KEY (id, mapping_stack_id);


--
-- Name: department department_pkey; Type: CONSTRAINT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.department
    ADD CONSTRAINT department_pkey PRIMARY KEY (id);


--
-- Name: dropdown_data_sources dropdown_data_sources_pkey; Type: CONSTRAINT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.dropdown_data_sources
    ADD CONSTRAINT dropdown_data_sources_pkey PRIMARY KEY (id);


--
-- Name: entities entities_pkey; Type: CONSTRAINT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.entities
    ADD CONSTRAINT entities_pkey PRIMARY KEY (entity_id);


--
-- Name: entity_attribute_metadata entity_attribute_metadata_pkey; Type: CONSTRAINT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.entity_attribute_metadata
    ADD CONSTRAINT entity_attribute_metadata_pkey PRIMARY KEY (entity_id, attribute_id);


--
-- Name: entity_attributes entity_attributes_pkey; Type: CONSTRAINT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.entity_attributes
    ADD CONSTRAINT entity_attributes_pkey PRIMARY KEY (attribute_id);


--
-- Name: entity_permissions entity_permissions_pkey; Type: CONSTRAINT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.entity_permissions
    ADD CONSTRAINT entity_permissions_pkey PRIMARY KEY (id);


--
-- Name: entity_relationships entity_relationships_pkey; Type: CONSTRAINT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.entity_relationships
    ADD CONSTRAINT entity_relationships_pkey PRIMARY KEY (id);


--
-- Name: execution_path_tracking execution_path_tracking_pkey; Type: CONSTRAINT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.execution_path_tracking
    ADD CONSTRAINT execution_path_tracking_pkey PRIMARY KEY (id);


--
-- Name: execution_pathway_conditions execution_pathway_conditions_pkey; Type: CONSTRAINT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.execution_pathway_conditions
    ADD CONSTRAINT execution_pathway_conditions_pkey PRIMARY KEY (id);


--
-- Name: execution_pathways execution_pathways_pkey; Type: CONSTRAINT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.execution_pathways
    ADD CONSTRAINT execution_pathways_pkey PRIMARY KEY (id);


--
-- Name: execution_rules execution_rules_pkey; Type: CONSTRAINT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.execution_rules
    ADD CONSTRAINT execution_rules_pkey PRIMARY KEY (id, lo_id);


--
-- Name: global_objectives global_objectives_pkey; Type: CONSTRAINT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.global_objectives
    ADD CONSTRAINT global_objectives_pkey PRIMARY KEY (go_id);


--
-- Name: input_data_sources input_data_sources_pkey; Type: CONSTRAINT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.input_data_sources
    ADD CONSTRAINT input_data_sources_pkey PRIMARY KEY (id);


--
-- Name: input_dependencies input_dependencies_pkey; Type: CONSTRAINT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.input_dependencies
    ADD CONSTRAINT input_dependencies_pkey PRIMARY KEY (id);


--
-- Name: input_items input_items_pkey; Type: CONSTRAINT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.input_items
    ADD CONSTRAINT input_items_pkey PRIMARY KEY (id, input_stack_id);


--
-- Name: input_stack input_stack_pkey; Type: CONSTRAINT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.input_stack
    ADD CONSTRAINT input_stack_pkey PRIMARY KEY (id);


--
-- Name: leave_application leave_application_pkey; Type: CONSTRAINT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.leave_application
    ADD CONSTRAINT leave_application_pkey PRIMARY KEY (id);


--
-- Name: leave_sub_type leave_sub_type_pkey; Type: CONSTRAINT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.leave_sub_type
    ADD CONSTRAINT leave_sub_type_pkey PRIMARY KEY (id);


--
-- Name: lo_data_mapping_stack lo_data_mapping_stack_pkey; Type: CONSTRAINT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.lo_data_mapping_stack
    ADD CONSTRAINT lo_data_mapping_stack_pkey PRIMARY KEY (id);


--
-- Name: lo_data_mappings lo_data_mappings_pkey; Type: CONSTRAINT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.lo_data_mappings
    ADD CONSTRAINT lo_data_mappings_pkey PRIMARY KEY (id, mapping_stack_id);


--
-- Name: lo_input_execution lo_input_execution_pkey; Type: CONSTRAINT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.lo_input_execution
    ADD CONSTRAINT lo_input_execution_pkey PRIMARY KEY (id);


--
-- Name: lo_input_items lo_input_items_pkey; Type: CONSTRAINT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.lo_input_items
    ADD CONSTRAINT lo_input_items_pkey PRIMARY KEY (id, input_stack_id);


--
-- Name: lo_input_stack lo_input_stack_lo_id_unique; Type: CONSTRAINT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.lo_input_stack
    ADD CONSTRAINT lo_input_stack_lo_id_unique UNIQUE (lo_id);


--
-- Name: lo_input_stack lo_input_stack_pkey; Type: CONSTRAINT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.lo_input_stack
    ADD CONSTRAINT lo_input_stack_pkey PRIMARY KEY (id);


--
-- Name: lo_input_validations lo_input_validations_pkey; Type: CONSTRAINT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.lo_input_validations
    ADD CONSTRAINT lo_input_validations_pkey PRIMARY KEY (id);


--
-- Name: lo_nested_functions lo_nested_functions_pkey; Type: CONSTRAINT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.lo_nested_functions
    ADD CONSTRAINT lo_nested_functions_pkey PRIMARY KEY (id);


--
-- Name: lo_output_execution lo_output_execution_pkey; Type: CONSTRAINT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.lo_output_execution
    ADD CONSTRAINT lo_output_execution_pkey PRIMARY KEY (id);


--
-- Name: lo_output_items lo_output_items_pkey; Type: CONSTRAINT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.lo_output_items
    ADD CONSTRAINT lo_output_items_pkey PRIMARY KEY (id, output_stack_id);


--
-- Name: lo_output_stack lo_output_stack_pkey; Type: CONSTRAINT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.lo_output_stack
    ADD CONSTRAINT lo_output_stack_pkey PRIMARY KEY (id);


--
-- Name: lo_output_triggers lo_output_triggers_pkey; Type: CONSTRAINT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.lo_output_triggers
    ADD CONSTRAINT lo_output_triggers_pkey PRIMARY KEY (id, output_item_id, output_stack_id);


--
-- Name: lo_system_functions lo_system_functions_pkey; Type: CONSTRAINT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.lo_system_functions
    ADD CONSTRAINT lo_system_functions_pkey PRIMARY KEY (function_id, lo_id);


--
-- Name: local_objectives local_objectives_pkey; Type: CONSTRAINT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.local_objectives
    ADD CONSTRAINT local_objectives_pkey PRIMARY KEY (lo_id);


--
-- Name: mapping_rules mapping_rules_pkey; Type: CONSTRAINT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.mapping_rules
    ADD CONSTRAINT mapping_rules_pkey PRIMARY KEY (id, mapping_stack_id);


--
-- Name: metrics_aggregation metrics_aggregation_pkey; Type: CONSTRAINT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.metrics_aggregation
    ADD CONSTRAINT metrics_aggregation_pkey PRIMARY KEY (id);


--
-- Name: metrics_reporting metrics_reporting_pkey; Type: CONSTRAINT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.metrics_reporting
    ADD CONSTRAINT metrics_reporting_pkey PRIMARY KEY (id);


--
-- Name: objective_permissions objective_permissions_pkey; Type: CONSTRAINT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.objective_permissions
    ADD CONSTRAINT objective_permissions_pkey PRIMARY KEY (id);


--
-- Name: organizational_units organizational_units_pkey; Type: CONSTRAINT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.organizational_units
    ADD CONSTRAINT organizational_units_pkey PRIMARY KEY (org_unit_id);


--
-- Name: output_items output_items_pkey; Type: CONSTRAINT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.output_items
    ADD CONSTRAINT output_items_pkey PRIMARY KEY (id, output_stack_id);


--
-- Name: output_stack output_stack_pkey; Type: CONSTRAINT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.output_stack
    ADD CONSTRAINT output_stack_pkey PRIMARY KEY (id);


--
-- Name: output_triggers output_triggers_pkey; Type: CONSTRAINT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.output_triggers
    ADD CONSTRAINT output_triggers_pkey PRIMARY KEY (id, output_item_id, output_stack_id);


--
-- Name: permission_capabilities permission_capabilities_pkey; Type: CONSTRAINT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.permission_capabilities
    ADD CONSTRAINT permission_capabilities_pkey PRIMARY KEY (capability_id);


--
-- Name: permission_contexts permission_contexts_pkey; Type: CONSTRAINT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.permission_contexts
    ADD CONSTRAINT permission_contexts_pkey PRIMARY KEY (context_id);


--
-- Name: permission_types permission_types_pkey; Type: CONSTRAINT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.permission_types
    ADD CONSTRAINT permission_types_pkey PRIMARY KEY (permission_id);


--
-- Name: role_permissions role_permissions_pkey; Type: CONSTRAINT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.role_permissions
    ADD CONSTRAINT role_permissions_pkey PRIMARY KEY (id);


--
-- Name: role role_pkey; Type: CONSTRAINT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.role
    ADD CONSTRAINT role_pkey PRIMARY KEY (id);


--
-- Name: roles roles_pkey; Type: CONSTRAINT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.roles
    ADD CONSTRAINT roles_pkey PRIMARY KEY (role_id);


--
-- Name: runtime_metrics_stack runtime_metrics_stack_pkey; Type: CONSTRAINT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.runtime_metrics_stack
    ADD CONSTRAINT runtime_metrics_stack_pkey PRIMARY KEY (id);


--
-- Name: success_messages success_messages_pkey; Type: CONSTRAINT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.success_messages
    ADD CONSTRAINT success_messages_pkey PRIMARY KEY (lo_id);


--
-- Name: system_functions system_functions_pkey; Type: CONSTRAINT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.system_functions
    ADD CONSTRAINT system_functions_pkey PRIMARY KEY (function_id);


--
-- Name: team_availability team_availability_pkey; Type: CONSTRAINT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.team_availability
    ADD CONSTRAINT team_availability_pkey PRIMARY KEY (id);


--
-- Name: tenants tenants_pkey; Type: CONSTRAINT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.tenants
    ADD CONSTRAINT tenants_pkey PRIMARY KEY (tenant_id);


--
-- Name: terminal_pathways terminal_pathways_pkey; Type: CONSTRAINT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.terminal_pathways
    ADD CONSTRAINT terminal_pathways_pkey PRIMARY KEY (lo_id);


--
-- Name: ui_elements ui_elements_pkey; Type: CONSTRAINT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.ui_elements
    ADD CONSTRAINT ui_elements_pkey PRIMARY KEY (id);


--
-- Name: ui_stack ui_stack_pkey; Type: CONSTRAINT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.ui_stack
    ADD CONSTRAINT ui_stack_pkey PRIMARY KEY (id);


--
-- Name: agent_rights unique_agent_right; Type: CONSTRAINT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.agent_rights
    ADD CONSTRAINT unique_agent_right UNIQUE (agent_stack_id, role_id, right_id);


--
-- Name: agent_stack unique_agent_stack_lo_id; Type: CONSTRAINT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.agent_stack
    ADD CONSTRAINT unique_agent_stack_lo_id UNIQUE (lo_id);


--
-- Name: lo_data_mapping_stack unique_lo_data_mapping_stack_lo_id; Type: CONSTRAINT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.lo_data_mapping_stack
    ADD CONSTRAINT unique_lo_data_mapping_stack_lo_id UNIQUE (lo_id);


--
-- Name: lo_input_stack unique_lo_input_stack; Type: CONSTRAINT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.lo_input_stack
    ADD CONSTRAINT unique_lo_input_stack UNIQUE (id);


--
-- Name: lo_input_stack unique_lo_input_stack_lo_id; Type: CONSTRAINT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.lo_input_stack
    ADD CONSTRAINT unique_lo_input_stack_lo_id UNIQUE (lo_id);


--
-- Name: lo_output_stack unique_lo_output_stack_lo_id; Type: CONSTRAINT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.lo_output_stack
    ADD CONSTRAINT unique_lo_output_stack_lo_id UNIQUE (lo_id);


--
-- Name: roles unique_role_name; Type: CONSTRAINT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.roles
    ADD CONSTRAINT unique_role_name UNIQUE (name);


--
-- Name: role_permissions unique_role_permission; Type: CONSTRAINT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.role_permissions
    ADD CONSTRAINT unique_role_permission UNIQUE (role_id, context_id);


--
-- Name: user_organizations unique_user_org; Type: CONSTRAINT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.user_organizations
    ADD CONSTRAINT unique_user_org UNIQUE (user_id, org_unit_id);


--
-- Name: user_oauth_tokens user_oauth_tokens_pkey; Type: CONSTRAINT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.user_oauth_tokens
    ADD CONSTRAINT user_oauth_tokens_pkey PRIMARY KEY (token_id);


--
-- Name: user_organizations user_organizations_pkey; Type: CONSTRAINT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.user_organizations
    ADD CONSTRAINT user_organizations_pkey PRIMARY KEY (id);


--
-- Name: user user_pkey; Type: CONSTRAINT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime."user"
    ADD CONSTRAINT user_pkey PRIMARY KEY (id);


--
-- Name: user_roles user_roles_pkey; Type: CONSTRAINT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.user_roles
    ADD CONSTRAINT user_roles_pkey PRIMARY KEY (user_id);


--
-- Name: user_sessions user_sessions_pkey; Type: CONSTRAINT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.user_sessions
    ADD CONSTRAINT user_sessions_pkey PRIMARY KEY (session_id);


--
-- Name: users users_email_key; Type: CONSTRAINT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.users
    ADD CONSTRAINT users_email_key UNIQUE (email);


--
-- Name: users users_pkey; Type: CONSTRAINT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.users
    ADD CONSTRAINT users_pkey PRIMARY KEY (user_id);


--
-- Name: users users_username_key; Type: CONSTRAINT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.users
    ADD CONSTRAINT users_username_key UNIQUE (username);


--
-- Name: workflow_instances workflow_instances_pkey; Type: CONSTRAINT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.workflow_instances
    ADD CONSTRAINT workflow_instances_pkey PRIMARY KEY (instance_id);


--
-- Name: workflow_results workflow_results_pkey; Type: CONSTRAINT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.workflow_results
    ADD CONSTRAINT workflow_results_pkey PRIMARY KEY (id);


--
-- Name: workflow_transaction workflow_transaction_pkey; Type: CONSTRAINT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.workflow_transaction
    ADD CONSTRAINT workflow_transaction_pkey PRIMARY KEY (id);


--
-- Name: idx_attribute_enum_values_attribute_id; Type: INDEX; Schema: workflow_runtime; Owner: postgres
--

CREATE INDEX idx_attribute_enum_values_attribute_id ON workflow_runtime.attribute_enum_values USING btree (attribute_id);


--
-- Name: idx_attribute_validations_attribute_id; Type: INDEX; Schema: workflow_runtime; Owner: postgres
--

CREATE INDEX idx_attribute_validations_attribute_id ON workflow_runtime.attribute_validations USING btree (attribute_id);


--
-- Name: idx_dropdown_data_sources_input_item; Type: INDEX; Schema: workflow_runtime; Owner: postgres
--

CREATE INDEX idx_dropdown_data_sources_input_item ON workflow_runtime.dropdown_data_sources USING btree (input_item_id, input_stack_id);


--
-- Name: idx_entity_attribute_metadata_attribute_id; Type: INDEX; Schema: workflow_runtime; Owner: postgres
--

CREATE INDEX idx_entity_attribute_metadata_attribute_id ON workflow_runtime.entity_attribute_metadata USING btree (attribute_id);


--
-- Name: idx_entity_attribute_metadata_entity_id; Type: INDEX; Schema: workflow_runtime; Owner: postgres
--

CREATE INDEX idx_entity_attribute_metadata_entity_id ON workflow_runtime.entity_attribute_metadata USING btree (entity_id);


--
-- Name: idx_entity_attributes_entity_id; Type: INDEX; Schema: workflow_runtime; Owner: postgres
--

CREATE INDEX idx_entity_attributes_entity_id ON workflow_runtime.entity_attributes USING btree (entity_id);


--
-- Name: idx_entity_attributes_reference_entity_id; Type: INDEX; Schema: workflow_runtime; Owner: postgres
--

CREATE INDEX idx_entity_attributes_reference_entity_id ON workflow_runtime.entity_attributes USING btree (reference_entity_id);


--
-- Name: idx_entity_permissions_entity_id; Type: INDEX; Schema: workflow_runtime; Owner: postgres
--

CREATE INDEX idx_entity_permissions_entity_id ON workflow_runtime.entity_permissions USING btree (entity_id);


--
-- Name: idx_entity_permissions_permission_id; Type: INDEX; Schema: workflow_runtime; Owner: postgres
--

CREATE INDEX idx_entity_permissions_permission_id ON workflow_runtime.entity_permissions USING btree (permission_id);


--
-- Name: idx_entity_permissions_role_id; Type: INDEX; Schema: workflow_runtime; Owner: postgres
--

CREATE INDEX idx_entity_permissions_role_id ON workflow_runtime.entity_permissions USING btree (role_id);


--
-- Name: idx_entity_relationships_source_entity_id; Type: INDEX; Schema: workflow_runtime; Owner: postgres
--

CREATE INDEX idx_entity_relationships_source_entity_id ON workflow_runtime.entity_relationships USING btree (source_entity_id);


--
-- Name: idx_entity_relationships_target_entity_id; Type: INDEX; Schema: workflow_runtime; Owner: postgres
--

CREATE INDEX idx_entity_relationships_target_entity_id ON workflow_runtime.entity_relationships USING btree (target_entity_id);


--
-- Name: idx_input_dependencies_depends_on; Type: INDEX; Schema: workflow_runtime; Owner: postgres
--

CREATE INDEX idx_input_dependencies_depends_on ON workflow_runtime.input_dependencies USING btree (depends_on_id, depends_on_stack_id);


--
-- Name: idx_input_dependencies_input_item; Type: INDEX; Schema: workflow_runtime; Owner: postgres
--

CREATE INDEX idx_input_dependencies_input_item ON workflow_runtime.input_dependencies USING btree (input_item_id, input_stack_id);


--
-- Name: idx_objective_permissions_objective_id; Type: INDEX; Schema: workflow_runtime; Owner: postgres
--

CREATE INDEX idx_objective_permissions_objective_id ON workflow_runtime.objective_permissions USING btree (objective_id);


--
-- Name: idx_objective_permissions_permission_id; Type: INDEX; Schema: workflow_runtime; Owner: postgres
--

CREATE INDEX idx_objective_permissions_permission_id ON workflow_runtime.objective_permissions USING btree (permission_id);


--
-- Name: idx_objective_permissions_role_id; Type: INDEX; Schema: workflow_runtime; Owner: postgres
--

CREATE INDEX idx_objective_permissions_role_id ON workflow_runtime.objective_permissions USING btree (role_id);


--
-- Name: idx_roles_inherits_from; Type: INDEX; Schema: workflow_runtime; Owner: postgres
--

CREATE INDEX idx_roles_inherits_from ON workflow_runtime.roles USING btree (inherits_from);


--
-- Name: idx_roles_tenant_id; Type: INDEX; Schema: workflow_runtime; Owner: postgres
--

CREATE INDEX idx_roles_tenant_id ON workflow_runtime.roles USING btree (tenant_id);


--
-- Name: asset_request update_asset_request_timestamp; Type: TRIGGER; Schema: workflow_runtime; Owner: postgres
--

CREATE TRIGGER update_asset_request_timestamp BEFORE UPDATE ON workflow_runtime.asset_request FOR EACH ROW EXECUTE FUNCTION workflow_runtime.update_modified_column();


--
-- Name: department update_department_timestamp; Type: TRIGGER; Schema: workflow_runtime; Owner: postgres
--

CREATE TRIGGER update_department_timestamp BEFORE UPDATE ON workflow_runtime.department FOR EACH ROW EXECUTE FUNCTION workflow_runtime.update_modified_column();


--
-- Name: leave_application update_leave_application_timestamp; Type: TRIGGER; Schema: workflow_runtime; Owner: postgres
--

CREATE TRIGGER update_leave_application_timestamp BEFORE UPDATE ON workflow_runtime.leave_application FOR EACH ROW EXECUTE FUNCTION workflow_runtime.update_modified_column();


--
-- Name: leave_sub_type update_leave_sub_type_timestamp; Type: TRIGGER; Schema: workflow_runtime; Owner: postgres
--

CREATE TRIGGER update_leave_sub_type_timestamp BEFORE UPDATE ON workflow_runtime.leave_sub_type FOR EACH ROW EXECUTE FUNCTION workflow_runtime.update_modified_column();


--
-- Name: organizational_units update_organizational_units_timestamp; Type: TRIGGER; Schema: workflow_runtime; Owner: postgres
--

CREATE TRIGGER update_organizational_units_timestamp BEFORE UPDATE ON workflow_runtime.organizational_units FOR EACH ROW EXECUTE FUNCTION workflow_runtime.update_timestamp();


--
-- Name: permission_contexts update_permission_contexts_timestamp; Type: TRIGGER; Schema: workflow_runtime; Owner: postgres
--

CREATE TRIGGER update_permission_contexts_timestamp BEFORE UPDATE ON workflow_runtime.permission_contexts FOR EACH ROW EXECUTE FUNCTION workflow_runtime.update_timestamp();


--
-- Name: role_permissions update_role_permissions_timestamp; Type: TRIGGER; Schema: workflow_runtime; Owner: postgres
--

CREATE TRIGGER update_role_permissions_timestamp BEFORE UPDATE ON workflow_runtime.role_permissions FOR EACH ROW EXECUTE FUNCTION workflow_runtime.update_timestamp();


--
-- Name: role update_role_timestamp; Type: TRIGGER; Schema: workflow_runtime; Owner: postgres
--

CREATE TRIGGER update_role_timestamp BEFORE UPDATE ON workflow_runtime.role FOR EACH ROW EXECUTE FUNCTION workflow_runtime.update_modified_column();


--
-- Name: user_oauth_tokens update_user_oauth_tokens_timestamp; Type: TRIGGER; Schema: workflow_runtime; Owner: postgres
--

CREATE TRIGGER update_user_oauth_tokens_timestamp BEFORE UPDATE ON workflow_runtime.user_oauth_tokens FOR EACH ROW EXECUTE FUNCTION workflow_runtime.update_timestamp();


--
-- Name: user_organizations update_user_organizations_timestamp; Type: TRIGGER; Schema: workflow_runtime; Owner: postgres
--

CREATE TRIGGER update_user_organizations_timestamp BEFORE UPDATE ON workflow_runtime.user_organizations FOR EACH ROW EXECUTE FUNCTION workflow_runtime.update_timestamp();


--
-- Name: user_sessions update_user_sessions_timestamp; Type: TRIGGER; Schema: workflow_runtime; Owner: postgres
--

CREATE TRIGGER update_user_sessions_timestamp BEFORE UPDATE ON workflow_runtime.user_sessions FOR EACH ROW EXECUTE FUNCTION workflow_runtime.update_timestamp();


--
-- Name: user update_user_timestamp; Type: TRIGGER; Schema: workflow_runtime; Owner: postgres
--

CREATE TRIGGER update_user_timestamp BEFORE UPDATE ON workflow_runtime."user" FOR EACH ROW EXECUTE FUNCTION workflow_runtime.update_modified_column();


--
-- Name: users update_users_timestamp; Type: TRIGGER; Schema: workflow_runtime; Owner: postgres
--

CREATE TRIGGER update_users_timestamp BEFORE UPDATE ON workflow_runtime.users FOR EACH ROW EXECUTE FUNCTION workflow_runtime.update_timestamp();


--
-- Name: agent_rights agent_rights_agent_stack_id_fkey; Type: FK CONSTRAINT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.agent_rights
    ADD CONSTRAINT agent_rights_agent_stack_id_fkey FOREIGN KEY (agent_stack_id) REFERENCES workflow_runtime.agent_stack(id) ON DELETE CASCADE;


--
-- Name: agent_rights agent_rights_right_id_fkey; Type: FK CONSTRAINT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.agent_rights
    ADD CONSTRAINT agent_rights_right_id_fkey FOREIGN KEY (right_id) REFERENCES workflow_runtime.permission_types(permission_id) ON DELETE CASCADE;


--
-- Name: agent_rights agent_rights_role_id_fkey; Type: FK CONSTRAINT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.agent_rights
    ADD CONSTRAINT agent_rights_role_id_fkey FOREIGN KEY (role_id) REFERENCES workflow_runtime.roles(role_id) ON DELETE CASCADE;


--
-- Name: agent_stack agent_stack_lo_id_fkey; Type: FK CONSTRAINT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.agent_stack
    ADD CONSTRAINT agent_stack_lo_id_fkey FOREIGN KEY (lo_id) REFERENCES workflow_runtime.local_objectives(lo_id) ON DELETE CASCADE;


--
-- Name: conditional_success_messages conditional_success_messages_lo_id_fkey; Type: FK CONSTRAINT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.conditional_success_messages
    ADD CONSTRAINT conditional_success_messages_lo_id_fkey FOREIGN KEY (lo_id) REFERENCES workflow_runtime.local_objectives(lo_id) ON DELETE CASCADE;


--
-- Name: data_mapping_stack data_mapping_stack_go_id_fkey; Type: FK CONSTRAINT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.data_mapping_stack
    ADD CONSTRAINT data_mapping_stack_go_id_fkey FOREIGN KEY (go_id) REFERENCES workflow_runtime.global_objectives(go_id) ON DELETE CASCADE;


--
-- Name: data_mappings data_mappings_mapping_stack_id_fkey; Type: FK CONSTRAINT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.data_mappings
    ADD CONSTRAINT data_mappings_mapping_stack_id_fkey FOREIGN KEY (mapping_stack_id) REFERENCES workflow_runtime.data_mapping_stack(id) ON DELETE CASCADE;


--
-- Name: dropdown_data_sources dropdown_data_sources_input_item_id_input_stack_id_fkey; Type: FK CONSTRAINT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.dropdown_data_sources
    ADD CONSTRAINT dropdown_data_sources_input_item_id_input_stack_id_fkey FOREIGN KEY (input_item_id, input_stack_id) REFERENCES workflow_runtime.lo_input_items(id, input_stack_id) ON DELETE CASCADE;


--
-- Name: entity_permissions entity_permissions_permission_id_fkey; Type: FK CONSTRAINT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.entity_permissions
    ADD CONSTRAINT entity_permissions_permission_id_fkey FOREIGN KEY (permission_id) REFERENCES workflow_runtime.permission_types(permission_id) ON DELETE CASCADE;


--
-- Name: entity_permissions entity_permissions_role_id_fkey; Type: FK CONSTRAINT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.entity_permissions
    ADD CONSTRAINT entity_permissions_role_id_fkey FOREIGN KEY (role_id) REFERENCES workflow_runtime.roles(role_id) ON DELETE CASCADE;


--
-- Name: entity_relationships entity_relationships_target_entity_id_fkey; Type: FK CONSTRAINT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.entity_relationships
    ADD CONSTRAINT entity_relationships_target_entity_id_fkey FOREIGN KEY (target_entity_id) REFERENCES workflow_runtime.entities(entity_id) ON DELETE CASCADE;


--
-- Name: execution_path_tracking execution_path_tracking_metrics_stack_id_fkey; Type: FK CONSTRAINT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.execution_path_tracking
    ADD CONSTRAINT execution_path_tracking_metrics_stack_id_fkey FOREIGN KEY (metrics_stack_id) REFERENCES workflow_runtime.runtime_metrics_stack(id) ON DELETE CASCADE;


--
-- Name: execution_pathway_conditions execution_pathway_conditions_lo_id_fkey; Type: FK CONSTRAINT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.execution_pathway_conditions
    ADD CONSTRAINT execution_pathway_conditions_lo_id_fkey FOREIGN KEY (lo_id) REFERENCES workflow_runtime.local_objectives(lo_id) ON DELETE CASCADE;


--
-- Name: execution_pathway_conditions execution_pathway_conditions_next_lo_fkey; Type: FK CONSTRAINT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.execution_pathway_conditions
    ADD CONSTRAINT execution_pathway_conditions_next_lo_fkey FOREIGN KEY (next_lo) REFERENCES workflow_runtime.local_objectives(lo_id) ON DELETE CASCADE;


--
-- Name: execution_pathways execution_pathways_lo_id_fkey; Type: FK CONSTRAINT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.execution_pathways
    ADD CONSTRAINT execution_pathways_lo_id_fkey FOREIGN KEY (lo_id) REFERENCES workflow_runtime.local_objectives(lo_id) ON DELETE CASCADE;


--
-- Name: execution_pathways execution_pathways_next_lo_fkey; Type: FK CONSTRAINT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.execution_pathways
    ADD CONSTRAINT execution_pathways_next_lo_fkey FOREIGN KEY (next_lo) REFERENCES workflow_runtime.local_objectives(lo_id) ON DELETE SET NULL;


--
-- Name: execution_rules execution_rules_lo_id_fkey; Type: FK CONSTRAINT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.execution_rules
    ADD CONSTRAINT execution_rules_lo_id_fkey FOREIGN KEY (lo_id) REFERENCES workflow_runtime.local_objectives(lo_id) ON DELETE CASCADE;


--
-- Name: global_objectives global_objectives_tenant_id_fkey; Type: FK CONSTRAINT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.global_objectives
    ADD CONSTRAINT global_objectives_tenant_id_fkey FOREIGN KEY (tenant_id) REFERENCES workflow_runtime.tenants(tenant_id);


--
-- Name: input_data_sources input_data_sources_input_item_id_input_stack_id_fkey; Type: FK CONSTRAINT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.input_data_sources
    ADD CONSTRAINT input_data_sources_input_item_id_input_stack_id_fkey FOREIGN KEY (input_item_id, input_stack_id) REFERENCES workflow_runtime.lo_input_items(id, input_stack_id) ON DELETE CASCADE;


--
-- Name: input_dependencies input_dependencies_depends_on_id_depends_on_stack_id_fkey; Type: FK CONSTRAINT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.input_dependencies
    ADD CONSTRAINT input_dependencies_depends_on_id_depends_on_stack_id_fkey FOREIGN KEY (depends_on_id, depends_on_stack_id) REFERENCES workflow_runtime.lo_input_items(id, input_stack_id) ON DELETE CASCADE;


--
-- Name: input_dependencies input_dependencies_input_item_id_input_stack_id_fkey; Type: FK CONSTRAINT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.input_dependencies
    ADD CONSTRAINT input_dependencies_input_item_id_input_stack_id_fkey FOREIGN KEY (input_item_id, input_stack_id) REFERENCES workflow_runtime.lo_input_items(id, input_stack_id) ON DELETE CASCADE;


--
-- Name: input_items input_items_entity_reference_fkey; Type: FK CONSTRAINT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.input_items
    ADD CONSTRAINT input_items_entity_reference_fkey FOREIGN KEY (entity_reference) REFERENCES workflow_runtime.entities(entity_id) ON DELETE SET NULL;


--
-- Name: input_items input_items_input_stack_id_fkey; Type: FK CONSTRAINT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.input_items
    ADD CONSTRAINT input_items_input_stack_id_fkey FOREIGN KEY (input_stack_id) REFERENCES workflow_runtime.input_stack(id) ON DELETE CASCADE;


--
-- Name: input_stack input_stack_go_id_fkey; Type: FK CONSTRAINT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.input_stack
    ADD CONSTRAINT input_stack_go_id_fkey FOREIGN KEY (go_id) REFERENCES workflow_runtime.global_objectives(go_id) ON DELETE CASCADE;


--
-- Name: lo_data_mapping_stack lo_data_mapping_stack_lo_id_fkey; Type: FK CONSTRAINT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.lo_data_mapping_stack
    ADD CONSTRAINT lo_data_mapping_stack_lo_id_fkey FOREIGN KEY (lo_id) REFERENCES workflow_runtime.local_objectives(lo_id) ON DELETE CASCADE;


--
-- Name: lo_data_mappings lo_data_mappings_mapping_stack_id_fkey; Type: FK CONSTRAINT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.lo_data_mappings
    ADD CONSTRAINT lo_data_mappings_mapping_stack_id_fkey FOREIGN KEY (mapping_stack_id) REFERENCES workflow_runtime.lo_data_mapping_stack(id) ON DELETE CASCADE;


--
-- Name: lo_input_execution lo_input_execution_instance_id_fkey; Type: FK CONSTRAINT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.lo_input_execution
    ADD CONSTRAINT lo_input_execution_instance_id_fkey FOREIGN KEY (instance_id) REFERENCES workflow_runtime.workflow_instances(instance_id);


--
-- Name: lo_input_execution lo_input_execution_lo_id_fkey; Type: FK CONSTRAINT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.lo_input_execution
    ADD CONSTRAINT lo_input_execution_lo_id_fkey FOREIGN KEY (lo_id) REFERENCES workflow_runtime.local_objectives(lo_id);


--
-- Name: lo_input_items lo_input_items_input_stack_id_fkey; Type: FK CONSTRAINT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.lo_input_items
    ADD CONSTRAINT lo_input_items_input_stack_id_fkey FOREIGN KEY (input_stack_id) REFERENCES workflow_runtime.lo_input_stack(id) ON DELETE CASCADE;


--
-- Name: lo_input_stack lo_input_stack_lo_id_fkey; Type: FK CONSTRAINT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.lo_input_stack
    ADD CONSTRAINT lo_input_stack_lo_id_fkey FOREIGN KEY (lo_id) REFERENCES workflow_runtime.local_objectives(lo_id) ON DELETE CASCADE;


--
-- Name: lo_input_validations lo_input_validations_input_item_id_input_stack_id_fkey; Type: FK CONSTRAINT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.lo_input_validations
    ADD CONSTRAINT lo_input_validations_input_item_id_input_stack_id_fkey FOREIGN KEY (input_item_id, input_stack_id) REFERENCES workflow_runtime.lo_input_items(id, input_stack_id) ON DELETE CASCADE;


--
-- Name: lo_output_execution lo_output_execution_instance_id_fkey; Type: FK CONSTRAINT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.lo_output_execution
    ADD CONSTRAINT lo_output_execution_instance_id_fkey FOREIGN KEY (instance_id) REFERENCES workflow_runtime.workflow_instances(instance_id);


--
-- Name: lo_output_execution lo_output_execution_lo_id_fkey; Type: FK CONSTRAINT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.lo_output_execution
    ADD CONSTRAINT lo_output_execution_lo_id_fkey FOREIGN KEY (lo_id) REFERENCES workflow_runtime.local_objectives(lo_id);


--
-- Name: lo_output_items lo_output_items_output_stack_id_fkey; Type: FK CONSTRAINT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.lo_output_items
    ADD CONSTRAINT lo_output_items_output_stack_id_fkey FOREIGN KEY (output_stack_id) REFERENCES workflow_runtime.lo_output_stack(id) ON DELETE CASCADE;


--
-- Name: lo_output_stack lo_output_stack_lo_id_fkey; Type: FK CONSTRAINT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.lo_output_stack
    ADD CONSTRAINT lo_output_stack_lo_id_fkey FOREIGN KEY (lo_id) REFERENCES workflow_runtime.local_objectives(lo_id) ON DELETE CASCADE;


--
-- Name: lo_output_triggers lo_output_triggers_output_item_id_output_stack_id_fkey; Type: FK CONSTRAINT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.lo_output_triggers
    ADD CONSTRAINT lo_output_triggers_output_item_id_output_stack_id_fkey FOREIGN KEY (output_item_id, output_stack_id) REFERENCES workflow_runtime.lo_output_items(id, output_stack_id) ON DELETE CASCADE;


--
-- Name: lo_system_functions lo_system_functions_lo_id_fkey; Type: FK CONSTRAINT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.lo_system_functions
    ADD CONSTRAINT lo_system_functions_lo_id_fkey FOREIGN KEY (lo_id) REFERENCES workflow_runtime.local_objectives(lo_id) ON DELETE CASCADE;


--
-- Name: local_objectives local_objectives_go_id_fkey; Type: FK CONSTRAINT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.local_objectives
    ADD CONSTRAINT local_objectives_go_id_fkey FOREIGN KEY (go_id) REFERENCES workflow_runtime.global_objectives(go_id) ON DELETE CASCADE;


--
-- Name: mapping_rules mapping_rules_condition_entity_fkey; Type: FK CONSTRAINT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.mapping_rules
    ADD CONSTRAINT mapping_rules_condition_entity_fkey FOREIGN KEY (condition_entity) REFERENCES workflow_runtime.entities(entity_id) ON DELETE SET NULL;


--
-- Name: mapping_rules mapping_rules_mapping_stack_id_fkey; Type: FK CONSTRAINT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.mapping_rules
    ADD CONSTRAINT mapping_rules_mapping_stack_id_fkey FOREIGN KEY (mapping_stack_id) REFERENCES workflow_runtime.data_mapping_stack(id) ON DELETE CASCADE;


--
-- Name: metrics_aggregation metrics_aggregation_metrics_stack_id_fkey; Type: FK CONSTRAINT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.metrics_aggregation
    ADD CONSTRAINT metrics_aggregation_metrics_stack_id_fkey FOREIGN KEY (metrics_stack_id) REFERENCES workflow_runtime.runtime_metrics_stack(id) ON DELETE CASCADE;


--
-- Name: metrics_reporting metrics_reporting_metrics_stack_id_fkey; Type: FK CONSTRAINT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.metrics_reporting
    ADD CONSTRAINT metrics_reporting_metrics_stack_id_fkey FOREIGN KEY (metrics_stack_id) REFERENCES workflow_runtime.runtime_metrics_stack(id) ON DELETE CASCADE;


--
-- Name: objective_permissions objective_permissions_permission_id_fkey; Type: FK CONSTRAINT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.objective_permissions
    ADD CONSTRAINT objective_permissions_permission_id_fkey FOREIGN KEY (permission_id) REFERENCES workflow_runtime.permission_types(permission_id) ON DELETE CASCADE;


--
-- Name: objective_permissions objective_permissions_role_id_fkey; Type: FK CONSTRAINT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.objective_permissions
    ADD CONSTRAINT objective_permissions_role_id_fkey FOREIGN KEY (role_id) REFERENCES workflow_runtime.roles(role_id) ON DELETE CASCADE;


--
-- Name: organizational_units organizational_units_parent_org_unit_id_fkey; Type: FK CONSTRAINT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.organizational_units
    ADD CONSTRAINT organizational_units_parent_org_unit_id_fkey FOREIGN KEY (parent_org_unit_id) REFERENCES workflow_runtime.organizational_units(org_unit_id) ON DELETE SET NULL;


--
-- Name: organizational_units organizational_units_tenant_id_fkey; Type: FK CONSTRAINT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.organizational_units
    ADD CONSTRAINT organizational_units_tenant_id_fkey FOREIGN KEY (tenant_id) REFERENCES workflow_runtime.tenants(tenant_id) ON DELETE CASCADE;


--
-- Name: output_items output_items_output_entity_fkey; Type: FK CONSTRAINT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.output_items
    ADD CONSTRAINT output_items_output_entity_fkey FOREIGN KEY (output_entity) REFERENCES workflow_runtime.entities(entity_id) ON DELETE SET NULL;


--
-- Name: output_items output_items_output_stack_id_fkey; Type: FK CONSTRAINT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.output_items
    ADD CONSTRAINT output_items_output_stack_id_fkey FOREIGN KEY (output_stack_id) REFERENCES workflow_runtime.output_stack(id) ON DELETE CASCADE;


--
-- Name: output_stack output_stack_go_id_fkey; Type: FK CONSTRAINT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.output_stack
    ADD CONSTRAINT output_stack_go_id_fkey FOREIGN KEY (go_id) REFERENCES workflow_runtime.global_objectives(go_id) ON DELETE CASCADE;


--
-- Name: output_triggers output_triggers_output_item_id_output_stack_id_fkey; Type: FK CONSTRAINT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.output_triggers
    ADD CONSTRAINT output_triggers_output_item_id_output_stack_id_fkey FOREIGN KEY (output_item_id, output_stack_id) REFERENCES workflow_runtime.output_items(id, output_stack_id) ON DELETE CASCADE;


--
-- Name: output_triggers output_triggers_target_objective_fkey; Type: FK CONSTRAINT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.output_triggers
    ADD CONSTRAINT output_triggers_target_objective_fkey FOREIGN KEY (target_objective) REFERENCES workflow_runtime.global_objectives(go_id) ON DELETE CASCADE;


--
-- Name: permission_capabilities permission_capabilities_permission_id_fkey; Type: FK CONSTRAINT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.permission_capabilities
    ADD CONSTRAINT permission_capabilities_permission_id_fkey FOREIGN KEY (permission_id) REFERENCES workflow_runtime.permission_types(permission_id) ON DELETE CASCADE;


--
-- Name: role_permissions role_permissions_context_id_fkey; Type: FK CONSTRAINT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.role_permissions
    ADD CONSTRAINT role_permissions_context_id_fkey FOREIGN KEY (context_id) REFERENCES workflow_runtime.permission_contexts(context_id) ON DELETE CASCADE;


--
-- Name: role_permissions role_permissions_role_id_fkey; Type: FK CONSTRAINT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.role_permissions
    ADD CONSTRAINT role_permissions_role_id_fkey FOREIGN KEY (role_id) REFERENCES workflow_runtime.roles(role_id) ON DELETE CASCADE;


--
-- Name: roles roles_inherits_from_fkey; Type: FK CONSTRAINT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.roles
    ADD CONSTRAINT roles_inherits_from_fkey FOREIGN KEY (inherits_from) REFERENCES workflow_runtime.roles(role_id) ON DELETE SET NULL;


--
-- Name: roles roles_tenant_id_fkey; Type: FK CONSTRAINT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.roles
    ADD CONSTRAINT roles_tenant_id_fkey FOREIGN KEY (tenant_id) REFERENCES workflow_runtime.tenants(tenant_id) ON DELETE CASCADE;


--
-- Name: runtime_metrics_stack runtime_metrics_stack_go_id_fkey; Type: FK CONSTRAINT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.runtime_metrics_stack
    ADD CONSTRAINT runtime_metrics_stack_go_id_fkey FOREIGN KEY (go_id) REFERENCES workflow_runtime.global_objectives(go_id) ON DELETE CASCADE;


--
-- Name: runtime_metrics_stack runtime_metrics_stack_metrics_entity_fkey; Type: FK CONSTRAINT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.runtime_metrics_stack
    ADD CONSTRAINT runtime_metrics_stack_metrics_entity_fkey FOREIGN KEY (metrics_entity) REFERENCES workflow_runtime.entities(entity_id) ON DELETE CASCADE;


--
-- Name: success_messages success_messages_lo_id_fkey; Type: FK CONSTRAINT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.success_messages
    ADD CONSTRAINT success_messages_lo_id_fkey FOREIGN KEY (lo_id) REFERENCES workflow_runtime.local_objectives(lo_id) ON DELETE CASCADE;


--
-- Name: terminal_pathways terminal_pathways_lo_id_fkey; Type: FK CONSTRAINT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.terminal_pathways
    ADD CONSTRAINT terminal_pathways_lo_id_fkey FOREIGN KEY (lo_id) REFERENCES workflow_runtime.local_objectives(lo_id) ON DELETE CASCADE;


--
-- Name: ui_elements ui_elements_ui_stack_id_fkey; Type: FK CONSTRAINT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.ui_elements
    ADD CONSTRAINT ui_elements_ui_stack_id_fkey FOREIGN KEY (ui_stack_id) REFERENCES workflow_runtime.ui_stack(id) ON DELETE CASCADE;


--
-- Name: ui_stack ui_stack_lo_id_fkey; Type: FK CONSTRAINT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.ui_stack
    ADD CONSTRAINT ui_stack_lo_id_fkey FOREIGN KEY (lo_id) REFERENCES workflow_runtime.local_objectives(lo_id) ON DELETE CASCADE;


--
-- Name: user_oauth_tokens user_oauth_tokens_user_id_fkey; Type: FK CONSTRAINT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.user_oauth_tokens
    ADD CONSTRAINT user_oauth_tokens_user_id_fkey FOREIGN KEY (user_id) REFERENCES workflow_runtime.users(user_id) ON DELETE CASCADE;


--
-- Name: user_organizations user_organizations_org_unit_id_fkey; Type: FK CONSTRAINT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.user_organizations
    ADD CONSTRAINT user_organizations_org_unit_id_fkey FOREIGN KEY (org_unit_id) REFERENCES workflow_runtime.organizational_units(org_unit_id) ON DELETE CASCADE;


--
-- Name: user_organizations user_organizations_user_id_fkey; Type: FK CONSTRAINT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.user_organizations
    ADD CONSTRAINT user_organizations_user_id_fkey FOREIGN KEY (user_id) REFERENCES workflow_runtime.users(user_id) ON DELETE CASCADE;


--
-- Name: user_roles user_roles_role_fkey; Type: FK CONSTRAINT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.user_roles
    ADD CONSTRAINT user_roles_role_fkey FOREIGN KEY (role) REFERENCES workflow_runtime.roles(name);


--
-- Name: user_roles user_roles_tenant_id_fkey; Type: FK CONSTRAINT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.user_roles
    ADD CONSTRAINT user_roles_tenant_id_fkey FOREIGN KEY (tenant_id) REFERENCES workflow_runtime.tenants(tenant_id) ON DELETE CASCADE;


--
-- Name: user_sessions user_sessions_user_id_fkey; Type: FK CONSTRAINT; Schema: workflow_runtime; Owner: postgres
--

ALTER TABLE ONLY workflow_runtime.user_sessions
    ADD CONSTRAINT user_sessions_user_id_fkey FOREIGN KEY (user_id) REFERENCES workflow_runtime.users(user_id) ON DELETE CASCADE;


--
-- PostgreSQL database dump complete
--

