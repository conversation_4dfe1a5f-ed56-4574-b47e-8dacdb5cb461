"""
Updated functions for entity_deployer_v2.py

These are the updated versions of deploy_entity_relationships and add_foreign_key_constraints
that should be integrated into your existing entity_deployer_v2.py file.
"""
from typing import Dict, List, Tuple, Any, Optional

def deploy_entity_relationships(entity_id: str, relationships: Dict, schema_name: str) -> Tuple[bool, List[str]]:
    """
    Deploy entity relationships to the database.
    
    Args:
        entity_id: ID of the entity
        relationships: Dictionary of relationships
        schema_name: Schema name to deploy to
        
    Returns:
        Tuple containing:
            - Boolean indicating if deployment was successful
            - List of messages (warnings, errors, or success messages)
    """
    messages = []
    
    try:
        # Get entity name for better logging
        success, query_messages, entity_result = execute_query(
            f"SELECT name FROM {schema_name}.entities WHERE entity_id = %s",
            (entity_id,),
            schema_name
        )
        
        if not success or not entity_result:
            entity_name = entity_id  # Fallback to ID if name not found
        else:
            entity_name = entity_result[0][0]
            
        logger.info(f"Deploying relationships for entity '{entity_name}' (ID: {entity_id})")
        
        # Check if the entity_relationships table exists
        success, query_messages, table_exists = execute_query(
            f"""
            SELECT EXISTS (
                SELECT FROM information_schema.tables 
                WHERE table_schema = %s
                AND table_name = 'entity_relationships'
            )
            """,
            (schema_name,)
        )
        
        if not success or not table_exists or not table_exists[0][0]:
            error_msg = f"Error: entity_relationships table does not exist in schema {schema_name}"
            messages.append(error_msg)
            logger.error(error_msg)
            return False, messages
        
        # Process each relationship
        for rel_name, rel_def in relationships.items():
            # Get target entity name and relationship type
            target_entity_name = rel_def.get('entity')
            relationship_type = rel_def.get('type', 'association')
            
            if not target_entity_name:
                messages.append(f"Warning: Relationship '{rel_name}' is missing target entity")
                continue
            
            # Look up target entity ID by name
            success, query_messages, result = execute_query(
                f"SELECT entity_id FROM {schema_name}.entities WHERE name = %s",
                (target_entity_name,),
                schema_name
            )
            
            if not success or not result:
                warning_msg = f"Warning: Target entity '{target_entity_name}' not found"
                messages.append(warning_msg)
                logger.warning(warning_msg)
                continue
            
            target_entity_id = result[0][0]
            
            # Get source and target attributes
            source_attr_name = rel_def.get('source_attribute')
            target_attr_name = rel_def.get('target_attribute')
            
            if not source_attr_name or not target_attr_name:
                warning_msg = f"Warning: Missing source or target attribute for relationship '{rel_name}'"
                messages.append(warning_msg)
                logger.warning(warning_msg)
                continue
            
            # Get source attribute ID
            source_attr_id = get_attribute_id_by_name(entity_id, source_attr_name, schema_name)
            
            if not source_attr_id:
                # Try to create the attribute if it doesn't exist
                source_attr_id = get_next_attribute_id(entity_id, schema_name)
                
                query = f"""
                    INSERT INTO {schema_name}.entity_attributes (
                        attribute_id, entity_id, name, type, foreign_key
                    ) VALUES (%s, %s, %s, %s, %s)
                """
                params = (source_attr_id, entity_id, source_attr_name, 'string', True)
                
                success, query_messages, _ = execute_query(query, params, schema_name)
                
                if not success:
                    warning_msg = f"Warning: Failed to create source attribute '{source_attr_name}'"
                    messages.append(warning_msg)
                    logger.warning(warning_msg)
                    continue
                
                logger.info(f"Created source attribute {source_attr_name} with ID {source_attr_id} as a foreign key")
                
                # Also create attribute metadata
                query = f"""
                    INSERT INTO {schema_name}.entity_attribute_metadata (
                        entity_id, attribute_id, attribute_name
                    ) VALUES (%s, %s, %s)
                """
                params = (entity_id, source_attr_id, source_attr_name)
                execute_query(query, params, schema_name)
            else:
                # Update the attribute to mark it as a foreign key
                update_query = f"""
                    UPDATE {schema_name}.entity_attributes
                    SET foreign_key = TRUE
                    WHERE attribute_id = %s
                """
                execute_query(update_query, (source_attr_id,), schema_name)
                logger.info(f"Marked attribute {source_attr_name} as a foreign key")
            
            # Get target attribute ID
            target_attr_id = get_attribute_id_by_name(target_entity_id, target_attr_name, schema_name)
            
            if not target_attr_id:
                # Try to create the attribute if it doesn't exist
                target_attr_id = get_next_attribute_id(target_entity_id, schema_name)
                
                query = f"""
                    INSERT INTO {schema_name}.entity_attributes (
                        attribute_id, entity_id, name, type, primary_key
                    ) VALUES (%s, %s, %s, %s, %s)
                """
                params = (target_attr_id, target_entity_id, target_attr_name, 'string', True)
                
                success, query_messages, _ = execute_query(query, params, schema_name)
                
                if not success:
                    warning_msg = f"Warning: Failed to create target attribute '{target_attr_name}'"
                    messages.append(warning_msg)
                    logger.warning(warning_msg)
                    continue
                
                logger.info(f"Created target attribute {target_attr_name} with ID {target_attr_id} as a primary key")
                
                # Also create attribute metadata
                query = f"""
                    INSERT INTO {schema_name}.entity_attribute_metadata (
                        entity_id, attribute_id, attribute_name
                    ) VALUES (%s, %s, %s)
                """
                params = (target_entity_id, target_attr_id, target_attr_name)
                execute_query(query, params, schema_name)
            else:
                # Update the attribute to mark it as a primary key if it's referenced in a relationship
                update_query = f"""
                    UPDATE {schema_name}.entity_attributes
                    SET primary_key = TRUE
                    WHERE attribute_id = %s
                """
                execute_query(update_query, (target_attr_id,), schema_name)
                logger.info(f"Marked attribute {target_attr_name} as a primary key")
            
            # Check if relationship already exists
            success, check_messages, result = execute_query(
                f"""
                SELECT id FROM {schema_name}.entity_relationships 
                WHERE source_entity_id = %s AND target_entity_id = %s AND 
                      source_attribute_id = %s AND target_attribute_id = %s
                """,
                (entity_id, target_entity_id, source_attr_id, target_attr_id),
                schema_name
            )
            
            if success and result:
                # Relationship already exists, update it
                query = f"""
                    UPDATE {schema_name}.entity_relationships
                    SET relationship_type = %s,
                        updated_at = NOW()
                    WHERE source_entity_id = %s AND target_entity_id = %s AND 
                          source_attribute_id = %s AND target_attribute_id = %s
                """
                params = (
                    relationship_type,
                    entity_id,
                    target_entity_id,
                    source_attr_id,
                    target_attr_id
                )
                logger.info(f"Updating existing relationship between {entity_name}.{source_attr_name} and {target_entity_name}.{target_attr_name}")
            else:
                # Relationship doesn't exist, insert it
                query = f"""
                    INSERT INTO {schema_name}.entity_relationships (
                        source_entity_id, target_entity_id, relationship_type, 
                        source_attribute_id, target_attribute_id,
                        created_at, updated_at
                    ) VALUES (%s, %s, %s, %s, %s, NOW(), NOW())
                """
                params = (
                    entity_id,
                    target_entity_id,
                    relationship_type,
                    source_attr_id,
                    target_attr_id
                )
                logger.info(f"Inserting new relationship between {entity_name}.{source_attr_name} and {target_entity_name}.{target_attr_name}")
            
            # Execute the query with detailed error handling
            try:
                success, query_messages, _ = execute_query(query, params, schema_name)
                
                if not success:
                    messages.extend(query_messages)
                    logger.warning(f"Failed to insert relationship '{rel_name}': {query_messages}")
                    continue  # Continue with other relationships instead of failing
                
                # Verify the relationship was inserted
                verify_success, verify_messages, verify_result = execute_query(
                    f"""
                    SELECT id FROM {schema_name}.entity_relationships 
                    WHERE source_entity_id = %s AND target_entity_id = %s AND 
                          source_attribute_id = %s AND target_attribute_id = %s
                    """,
                    (entity_id, target_entity_id, source_attr_id, target_attr_id),
                    schema_name
                )
                
                if verify_success and verify_result:
                    messages.append(f"Inserted relationship '{rel_name}' from '{entity_name}' to '{target_entity_name}'")
                    logger.info(f"Verified relationship '{rel_name}' was inserted with ID {verify_result[0][0]}")
                else:
                    warning_msg = f"Warning: Relationship '{rel_name}' insertion could not be verified: {verify_messages}"
                    messages.append(warning_msg)
                    logger.warning(warning_msg)
            except Exception as e:
                error_msg = f"Error inserting relationship '{rel_name}': {str(e)}"
                logger.error(error_msg, exc_info=True)
                messages.append(error_msg)
                continue  # Continue with other relationships instead of failing
        
        # Verify all relationships were inserted
        success, query_messages, relationships_count = execute_query(
            f"SELECT COUNT(*) FROM {schema_name}.entity_relationships WHERE source_entity_id = %s",
            (entity_id,),
            schema_name
        )
        
        if success and relationships_count:
            logger.info(f"Found {relationships_count[0][0]} relationships for entity '{entity_name}' in the database")
        else:
            logger.warning(f"Could not verify relationships count for entity '{entity_name}': {query_messages}")
        
        return True, messages
    except Exception as e:
        error_msg = f"Error deploying relationships for entity '{entity_id}': {str(e)}"
        logger.error(error_msg, exc_info=True)
        messages.append(error_msg)
        return False, messages


def add_foreign_key_constraints(entity_id: str, entity_name: str, entity_def: Dict, schema_name: str, table_name: str) -> Tuple[bool, List[str]]:
    """
    Add foreign key constraints to an entity table based on relationships.
    
    Args:
        entity_id: ID of the entity
        entity_name: Name of the entity
        entity_def: Entity definition
        schema_name: Schema name
        table_name: Table name
        
    Returns:
        Tuple containing:
            - Boolean indicating if adding constraints was successful
            - List of messages (warnings, errors, or success messages)
    """
    messages = []
    
    try:
        logger.info(f"Adding foreign key constraints to table {schema_name}.{table_name}")
        
        # Check if relationships exist
        if 'relationships' not in entity_def:
            logger.info(f"No relationships defined for entity '{entity_name}'")
            return True, messages
        
        # Process each relationship
        for rel_name, rel_def in entity_def['relationships'].items():
            # Get target entity name
            target_entity_name = rel_def.get('entity')
            if not target_entity_name:
                warning_msg = f"Warning: Relationship '{rel_name}' in entity '{entity_name}' is missing 'entity'"
                messages.append(warning_msg)
                logger.warning(warning_msg)
                continue
            
            # Get source and target attribute names
            source_attr_name = rel_def.get('source_attribute')
            target_attr_name = rel_def.get('target_attribute')
            
            if not source_attr_name or not target_attr_name:
                warning_msg = f"Warning: Relationship '{rel_name}' in entity '{entity_name}' is missing source or target attribute"
                messages.append(warning_msg)
                logger.warning(warning_msg)
                continue
            
            # Get target entity ID
            success, query_messages, result = execute_query(
                f"SELECT entity_id FROM {schema_name}.entities WHERE name = %s",
                (target_entity_name,),
                schema_name
            )
            
            if not success or not result:
                warning_msg = f"Warning: Target entity '{target_entity_name}' for relationship '{rel_name}' not found"
                messages.append(warning_msg)
                logger.warning(warning_msg)
                continue
            
            target_entity_id = result[0][0]
            
            # Get target table name
            target_entity_num = target_entity_id[1:]  # Remove 'E' prefix
            target_table_name = f"e{target_entity_num}_{target_entity_name.lower()}"
            
            # Check if target table exists
            success, query_messages, result = execute_query(
                f"""
                SELECT EXISTS (
                    SELECT FROM information_schema.tables 
                    WHERE table_schema = %s
                    AND table_name = %s
                )
                """,
                (schema_name, target_table_name)
            )
            
            if not success or not result or not result[0][0]:
                warning_msg = f"Warning: Target table '{target_table_name}' for relationship '{rel_name}' not found"
                messages.append(warning_msg)
                logger.warning(warning_msg)
                continue
            
            # Sanitize attribute names for SQL
            source_attr_sql = sanitize_column_name(source_attr_name)
            target_attr_sql = sanitize_column_name(target_attr_name)
            
            # Check if source column exists
            success, query_messages, result = execute_query(
                f"""
                SELECT EXISTS (
                    SELECT FROM information_schema.columns 
                    WHERE table_schema = %s
                    AND table_name = %s
                    AND column_name = %s
                )
                """,
                (schema_name, table_name, source_attr_sql)
            )
            
            if not success or not result or not result[0][0]:
                warning_msg = f"Warning: Source column '{source_attr_sql}' for relationship '{rel_name}' not found in table '{table_name}'"
                messages.append(warning_msg)
                logger.warning(warning_msg)
                continue
            
            # Check if target column exists
            success, query_messages, result = execute_query(
                f"""
                SELECT EXISTS (
                    SELECT FROM information_schema.columns 
                    WHERE table_schema = %s
                    AND table_name = %s
                    AND column_name = %s
                )
                """,
                (schema_name, target_table_name, target_attr_sql)
            )
            
            if not success or not result or not result[0][0]:
                warning_msg = f"Warning: Target column '{target_attr_sql}' for relationship '{rel_name}' not found in table '{target_table_name}'"
                messages.append(warning_msg)
                logger.warning(warning_msg)
                continue
            
            # Generate constraint name
            constraint_name = f"fk_{table_name}_{source_attr_sql}_to_{target_table_name}_{target_attr_sql}"
            
            # Check if constraint already exists
            success, query_messages, result = execute_query(
                f"""
                SELECT EXISTS (
                    SELECT FROM information_schema.table_constraints
                    WHERE table_schema = %s
                    AND table_name = %s
                    AND constraint_name = %s
                )
                """,
                (schema_name, table_name, constraint_name)
            )
            
            if success and result and result[0][0]:
                logger.info(f"Foreign key constraint '{constraint_name}' already exists")
                continue
            
            # Add foreign key constraint
            query = f"""
                ALTER TABLE {schema_name}."{table_name}"
                ADD CONSTRAINT {constraint_name}
                FOREIGN KEY ({source_attr_sql})
                REFERENCES {schema_name}."{target_table_name}" ({target_attr_sql})
            """
            
            success, query_messages, _ = execute_query(query)
            
            if not success:
                warning_msg = f"Warning: Failed to add foreign key constraint for relationship '{rel_name}': {query_messages}"
                messages.append(warning_msg)
                logger.warning(warning_msg)
                continue
            
            messages.append(f"Added foreign key constraint for relationship '{rel_name}' from {table_name}.{source_attr_sql} to {target_table_name}.{target_attr_sql}")
            logger.info(f"Added foreign key constraint for relationship '{rel_name}' from {table_name}.{source_attr_sql} to {target_table_name}.{target_attr_sql}")
        
        return True, messages
    except Exception as e:
        error_msg = f"Error adding foreign key constraints to table '{table_name}': {str(e)}"
        logger.error(error_msg, exc_info=True)
        messages.append(error_msg)
        return False, messages