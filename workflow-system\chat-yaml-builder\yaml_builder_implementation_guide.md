# Comprehensive Implementation Guide for Enhanced YAML Builder

This guide provides detailed implementation instructions for modifying the Streamlit YAML builder to address context window limitations and enforce rigorous validations.

## Table of Contents

1. [Core Architecture Changes](#1-core-architecture-changes)
2. [Enhanced Validation Framework](#2-enhanced-validation-framework)
3. [Component-Specific Validation](#3-component-specific-validation)
4. [YAML Assembly System](#4-yaml-assembly-system)
5. [Prescriptive Sentence Generation](#5-prescriptive-sentence-generation)
6. [UI Implementation](#6-ui-implementation)
7. [Database Schema Updates](#7-database-schema-updates)
8. [Implementation Sequence](#8-implementation-sequence)

## 1. Core Architecture Changes

### 1.1 YAML Component Structure

Define the exact structure for our YAML components:

```
1. Roles Component:
   - tenant (with roles section only)
   - permission_types

2. Entities Component:
   - entities (with all attributes and metadata)

3. GO Definitions Component:
   - global_objectives
   - execution pathways
   - LO sequence (references only)

4. LO Definitions Component:
   - local_objectives (detailed definitions)
   - input/output stacks
   - data mappings
```

### 1.2 Session State Management

Update `streamlit_chat_yaml_builder.py` to include component-specific session state variables:

```python
# --- Enhanced Session State ---
if "chat_history" not in st.session_state:
    st.session_state.chat_history = []
if "current_component" not in st.session_state:
    st.session_state.current_component = "roles"  # Start with roles component
if "components" not in st.session_state:
    st.session_state.components = {
        "roles": {"yaml": None, "prescriptive": None, "validated": False, "errors": []},
        "entities": {"yaml": None, "prescriptive": None, "validated": False, "errors": []},
        "go_definitions": {"yaml": None, "prescriptive": None, "validated": False, "errors": []},
        "lo_definitions": {"yaml": None, "prescriptive": None, "validated": False, "errors": []}
    }
if "assembled_yaml" not in st.session_state:
    st.session_state.assembled_yaml = None
if "assembly_errors" not in st.session_state:
    st.session_state.assembly_errors = []
if "entity_registry" not in st.session_state:
    st.session_state.entity_registry = {}  # Track entities for validation
if "role_registry" not in st.session_state:
    st.session_state.role_registry = {}  # Track roles for validation
if "objective_registry" not in st.session_state:
    st.session_state.objective_registry = {}  # Track objectives for validation
```

### 1.3 Component-Specific Prompts

Create specialized system prompts for each YAML component:

#### 1.3.1 Roles Component Prompt (`roles_prompt.txt`)

```
You are a workflow system architect specializing in role-based access control (RBAC).

Your task is to generate ONLY the roles and permission_types sections of a workflow YAML configuration based on the user's requirements.

The output should include:
1. A tenant section with roles definitions
2. Permission types definitions

Focus exclusively on these sections and ensure they follow the exact structure:

```yaml
tenant:
  id: "t001"
  name: "WorkflowName"
  roles:
    - id: "r001"
      name: "RoleName"
      inherits_from: null
      access:
        entities:
          - entity_id: "e001"
            permissions: ["Read", "Create"]
        objectives:
          - objective_id: "go001.lo001"
            permissions: ["Execute"]

permission_types:
  - id: "read"
    description: "Can read entity data"
    capabilities: ["GET"]
  - id: "create"
    description: "Can create new entity records"
    capabilities: ["POST"]
```

Do not include any other sections like entities, global_objectives, or local_objectives.
```

#### 1.3.2 Entities Component Prompt (`entities_prompt.txt`)

```
You are a data architect specializing in entity modeling for workflow systems.

Your task is to generate ONLY the entities section of a workflow YAML configuration based on the user's requirements.

The output should include detailed entity definitions with:
1. Entity metadata (id, name, type, version, status)
2. Attributes metadata (prefix, map, required attributes)
3. Detailed attribute definitions

Focus exclusively on the entities section and ensure it follows the exact structure:

```yaml
entities:
  - id: "e001"
    name: "EntityName"
    type: "Master"
    version: "1.0"
    status: "Active"
    attributes_metadata:
      attribute_prefix: "at"
      attribute_map:
        "at101": "attributeName1"
        "at102": "attributeName2"
      required_attributes:
        - "at101"
        - "at102"
    attributes:
      - id: "at101"
        name: "attributeName1"
        display_name: "Attribute Display Name"
        datatype: "String"
        required: true
        version: "1.0"
        status: "Deployed"
```

Do not include any other sections like tenant, roles, global_objectives, or local_objectives.
```

#### 1.3.3 GO Definitions Component Prompt (`go_definitions_prompt.txt`)

```
You are a workflow architect specializing in process design.

Your task is to generate ONLY the global_objectives section of a workflow YAML configuration based on the user's requirements.

The output should include:
1. Global objective definitions
2. High-level workflow structure
3. Input/output/data mapping stacks for global objectives

Focus exclusively on the global_objectives section and ensure it follows the exact structure:

```yaml
global_objectives:
  - id: "go001"
    name: "Global Objective Name"
    version: "1.0"
    status: "Active"
    input_stack:
      description: "Global inputs"
      inputs: []
    output_stack:
      description: "Global outputs"
      outputs: []
    data_mapping_stack:
      description: "Data handover between GOs"
      mappings: []
```

Do not include detailed local_objectives definitions - only reference them in the workflow structure.
Do not include tenant, roles, or entities sections.
```

#### 1.3.4 LO Definitions Component Prompt (`lo_definitions_prompt.txt`)

```
You are a workflow implementation specialist focusing on detailed process steps.

Your task is to generate ONLY the local_objectives section of a workflow YAML configuration based on the user's requirements.

The output should include detailed local objective definitions with:
1. Basic metadata (id, contextual_id, name, function_type, workflow_source)
2. Execution pathway definitions
3. Agent stack with role permissions
4. Input stack with detailed input definitions
5. Output stack with detailed output definitions
6. Data mapping stack for handovers between local objectives

Focus exclusively on the local_objectives section and ensure it follows the exact structure:

```yaml
local_objectives:
  - id: "lo001"
    contextual_id: "go001.lo001"
    name: "Local Objective Name"
    workflow_source: "origin"
    function_type: "Create"
    agent_stack:
      agents:
        - role: "r001"
          rights: ["Execute"]
          users: []
    input_stack:
      description: "Input description"
      inputs:
        - id: "in001"
          slot_id: "e001.at101.in001"
          contextual_id: "go001.lo001.in001"
          source:
            type: "user"
            description: "Input description"
          required: true
          data_type: "string"
          ui_control: "oj-input-text"
          validations: []
    output_stack:
      description: "Output description"
      outputs:
        - id: "out001"
          slot_id: "e001.at101.out001"
          contextual_id: "go001.lo001.out001"
          source:
            type: "system"
            description: "Output description"
          data_type: "string"
    data_mapping_stack:
      description: "Data handover between LOs"
      mappings: []
    execution_pathway:
      type: "sequential"
      next_lo: "lo002"
```

Do not include tenant, roles, entities, or global_objectives sections.
```

## 2. Enhanced Validation Framework

### 2.1 Entity and Structure Registry

Create a new file `registry_validator.py` to track and validate entities and structures:

```python
import yaml
from typing import Dict, List, Any, Tuple, Set

class RegistryValidator:
    def __init__(self):
        self.entities = {}  # {entity_id: {name, attributes, version}}
        self.roles = {}     # {role_id: {name, permissions}}
        self.objectives = {}  # {objective_id: {name, type}}
        
    def register_entities(self, yaml_content: str) -> Tuple[bool, List[str]]:
        """Register entities from YAML and validate against existing registry"""
        try:
            data = yaml.safe_load(yaml_content)
            if not data or "entities" not in data:
                return False, ["No entities section found in YAML"]
                
            errors = []
            for entity in data["entities"]:
                entity_id = entity.get("id")
                entity_name = entity.get("name")
                
                if not entity_id or not entity_name:
                    errors.append(f"Entity missing id or name: {entity}")
                    continue
                    
                # Check if entity already exists
                if entity_id in self.entities:
                    # Validate entity hasn't changed
                    existing = self.entities[entity_id]
                    if existing["name"] != entity_name:
                        errors.append(f"Entity name changed: {existing['name']} -> {entity_name}")
                    
                    # Validate attributes haven't been removed
                    existing_attrs = {a["id"] for a in existing.get("attributes", [])}
                    new_attrs = {a["id"] for a in entity.get("attributes", [])}
                    
                    removed_attrs = existing_attrs - new_attrs
                    if removed_attrs:
                        errors.append(f"Attributes removed from entity {entity_name}: {removed_attrs}")
                    
                    # Validate attribute properties haven't changed
                    for attr in entity.get("attributes", []):
                        attr_id = attr.get("id")
                        if attr_id in existing_attrs:
                            existing_attr = next((a for a in existing.get("attributes", []) if a["id"] == attr_id), None)
                            if existing_attr:
                                # Check critical properties
                                if existing_attr.get("name") != attr.get("name"):
                                    errors.append(f"Attribute name changed in {entity_name}: {existing_attr.get('name')} -> {attr.get('name')}")
                                if existing_attr.get("datatype") != attr.get("datatype"):
                                    errors.append(f"Attribute datatype changed in {entity_name}.{attr.get('name')}: {existing_attr.get('datatype')} -> {attr.get('datatype')}")
                
                # Register or update entity
                self.entities[entity_id] = entity
                
            return len(errors) == 0, errors
            
        except yaml.YAMLError as e:
            return False, [f"YAML parsing error: {str(e)}"]
        except Exception as e:
            return False, [f"Unexpected error: {str(e)}"]
    
    def register_roles(self, yaml_content: str) -> Tuple[bool, List[str]]:
        """Register roles from YAML and validate against existing registry"""
        try:
            data = yaml.safe_load(yaml_content)
            if not data or "tenant" not in data or "roles" not in data["tenant"]:
                return False, ["No roles section found in YAML"]
                
            errors = []
            for role in data["tenant"]["roles"]:
                role_id = role.get("id")
                role_name = role.get("name")
                
                if not role_id or not role_name:
                    errors.append(f"Role missing id or name: {role}")
                    continue
                    
                # Check if role already exists
                if role_id in self.roles:
                    # Validate role hasn't changed
                    existing = self.roles[role_id]
                    if existing["name"] != role_name:
                        errors.append(f"Role name changed: {existing['name']} -> {role_name}")
                    
                    # Validate permissions haven't been removed
                    if "access" in existing and "access" in role:
                        # Check entity permissions
                        existing_entities = {e["entity_id"] for e in existing["access"].get("entities", [])}
                        new_entities = {e["entity_id"] for e in role["access"].get("entities", [])}
                        
                        removed_entities = existing_entities - new_entities
                        if removed_entities:
                            errors.append(f"Entity permissions removed from role {role_name}: {removed_entities}")
                        
                        # Check for permission downgrades on entities
                        for entity in role["access"].get("entities", []):
                            entity_id = entity.get("entity_id")
                            if entity_id in existing_entities:
                                existing_entity = next((e for e in existing["access"].get("entities", []) if e["entity_id"] == entity_id), None)
                                if existing_entity:
                                    existing_perms = set(existing_entity.get("permissions", []))
                                    new_perms = set(entity.get("permissions", []))
                                    
                                    removed_perms = existing_perms - new_perms
                                    if removed_perms:
                                        errors.append(f"Permissions removed from role {role_name} for entity {entity_id}: {removed_perms}")
                
                # Register or update role
                self.roles[role_id] = role
                
            return len(errors) == 0, errors
            
        except yaml.YAMLError as e:
            return False, [f"YAML parsing error: {str(e)}"]
        except Exception as e:
            return False, [f"Unexpected error: {str(e)}"]
    
    def register_objectives(self, yaml_content: str) -> Tuple[bool, List[str]]:
        """Register objectives from YAML and validate against existing registry"""
        try:
            data = yaml.safe_load(yaml_content)
            errors = []
            
            # Process global objectives
            if "global_objectives" in data:
                for go in data["global_objectives"]:
                    go_id = go.get("id")
                    go_name = go.get("name")
                    
                    if not go_id or not go_name:
                        errors.append(f"Global objective missing id or name: {go}")
                        continue
                        
                    # Check if objective already exists
                    if go_id in self.objectives:
                        # Validate objective hasn't changed
                        existing = self.objectives[go_id]
                        if existing["name"] != go_name:
                            errors.append(f"Global objective name changed: {existing['name']} -> {go_name}")
                        if existing["type"] != "global":
                            errors.append(f"Objective type changed: {existing['type']} -> global")
                    
                    # Register or update objective
                    self.objectives[go_id] = {"name": go_name, "type": "global"}
            
            # Process local objectives
            if "local_objectives" in data:
                for lo in data["local_objectives"]:
                    lo_id = lo.get("id")
                    contextual_id = lo.get("contextual_id")
                    lo_name = lo.get("name")
                    
                    if not lo_id or not lo_name:
                        errors.append(f"Local objective missing id or name: {lo}")
                        continue
                        
                    # Check if objective already exists
                    if lo_id in self.objectives:
                        # Validate objective hasn't changed
                        existing = self.objectives[lo_id]
                        if existing["name"] != lo_name:
                            errors.append(f"Local objective name changed: {existing['name']} -> {lo_name}")
                        if existing["type"] != "local":
                            errors.append(f"Objective type changed: {existing['type']} -> local")
                    
                    # Register or update objective
                    self.objectives[lo_id] = {"name": lo_name, "type": "local", "contextual_id": contextual_id}
                
            return len(errors) == 0, errors
            
        except yaml.YAMLError as e:
            return False, [f"YAML parsing error: {str(e)}"]
        except Exception as e:
            return False, [f"Unexpected error: {str(e)}"]
    
    def validate_references(self, yaml_content: str) -> Tuple[bool, List[str]]:
        """Validate that all references in the YAML exist in the registry"""
        try:
            data = yaml.safe_load(yaml_content)
            errors = []
            
            # Validate entity references in roles
            if "tenant" in data and "roles" in data["tenant"]:
                for role in data["tenant"]["roles"]:
                    if "access" in role and "entities" in role["access"]:
                        for entity_ref in role["access"]["entities"]:
                            entity_id = entity_ref.get("entity_id")
                            if entity_id and entity_id not in self.entities:
                                errors.append(f"Role {role.get('name')} references non-existent entity: {entity_id}")
            
            # Validate role references in local objectives
            if "local_objectives" in data:
                for lo in data["local_objectives"]:
                    if "agent_stack" in lo and "agents" in lo["agent_stack"]:
                        for agent in lo["agent_stack"]["agents"]:
                            role_id = agent.get("role")
                            if role_id and role_id not in self.roles and role_id != "":  # Empty role is allowed
                                errors.append(f"Local objective {lo.get('name')} references non-existent role: {role_id}")
            
            # Validate objective references in execution pathways
            if "local_objectives" in data:
                for lo in data["local_objectives"]:
                    if "execution_pathway" in lo:
                        pathway = lo["execution_pathway"]
                        
                        # Check next_lo reference
                        next_lo = pathway.get("next_lo")
                        if next_lo and next_lo not in [obj_id for obj_id in self.objectives if self.objectives[obj_id]["type"] == "local"]:
                            errors.append(f"Local objective {lo.get('name')} references non-existent next_lo: {next_lo}")
                        
                        # Check condition references
                        if "conditions" in pathway:
                            for condition in pathway["conditions"]:
                                next_lo = condition.get("next_lo")
                                if next_lo and next_lo not in [obj_id for obj_id in self.objectives if self.objectives[obj_id]["type"] == "local"]:
                                    errors.append(f"Condition in {lo.get('name')} references non-existent next_lo: {next_lo}")
            
            return len(errors) == 0, errors
            
        except yaml.YAMLError as e:
            return False, [f"YAML parsing error: {str(e)}"]
        except Exception as e:
            return False, [f"Unexpected error: {str(e)}"]
    
    def validate_slot_ids(self, yaml_content: str) -> Tuple[bool, List[str]]:
        """Validate slot_id format and consistency"""
        try:
            data = yaml.safe_load(yaml_content)
            errors = []
            
            # Track all slot_ids to check for duplicates
            slot_ids = set()
            
            # Check local objectives
            if "local_objectives" in data:
                for lo in data["local_objectives"]:
                    lo_name = lo.get("name", "unknown")
                    
                    # Check input stack
                    if "input_stack" in lo and "inputs" in lo["input_stack"]:
                        for input_item in lo["input_stack"]["inputs"]:
                            slot_id = input_item.get("slot_id")
                            if not slot_id:
                                errors.append(f"Missing slot_id in input for {lo_name}")
                                continue
                                
                            # Validate slot_id format (e.g., e001.at101.in001)
                            parts = slot_id.split(".")
                            if len(parts) != 3 or not parts[0].startswith("e") or not parts[1].startswith("at"):
                                errors.append(f"Invalid slot_id format in {lo_name}: {slot_id}")
                            
                            # Check for duplicates
                            if slot_id in slot_ids:
                                errors.append(f"Duplicate slot_id in {lo_name}: {slot_id}")
                            slot_ids.add(slot_id)
                            
                            # Validate entity reference exists
                            entity_id = parts[0]
                            if entity_id not in self.entities:
                                errors.append(f"Slot_id {slot_id} in {lo_name} references non-existent entity: {entity_id}")
                    
                    # Check output stack
                    if "output_stack" in lo and "outputs" in lo["output_stack"]:
                        for output_item in lo["output_stack"]["outputs"]:
                            slot_id = output_item.get("slot_id")
                            if not slot_id:
                                errors.append(f"Missing slot_id in output for {lo_name}")
                                continue
                                
                            # Validate slot_id format
                            if not slot_id.startswith("e") and not slot_id.startswith("executionstatus"):
                                errors.append(f"Invalid slot_id format in {lo_name}: {slot_id}")
                            
                            # Check for duplicates
                            if slot_id in slot_ids:
                                errors.append(f"Duplicate slot_id in {lo_name}: {slot_id}")
                            slot_ids.add(slot_id)
            
            return len(errors) == 0, errors
            
        except yaml.YAMLError as e:
            return False, [f"YAML parsing error: {str(e)}"]
        except Exception as e:
            return False, [f"Unexpected error: {str(e)}"]
```

## 3. Component-Specific Validation

Create a new file `component_validator.py` for component-specific validation:

```python
import yaml
from typing import Dict, List, Any, Tuple

def validate_roles_component(yaml_content: str) -> Tuple[bool, List[str]]:
    """Validate the roles component of the YAML"""
    try:
        data = yaml.safe_load(yaml_content)
        errors = []
        
        # Check required sections
        if "tenant" not in data:
            errors.append("Missing 'tenant' section in roles component")
        elif "roles" not in data["tenant"]:
            errors.append("Missing 'roles' section in tenant")
        
        if "permission_types" not in data:
            errors.append("Missing 'permission_types' section in roles component")
        
        # Validate tenant structure
        if "tenant" in data:
            tenant = data["tenant"]
            if "id" not in tenant:
                errors.append("Missing 'id' in tenant")
            if "name" not in tenant:
                errors.append("Missing 'name' in tenant")
            
            # Validate roles
            if "roles" in tenant:
                for i, role in enumerate(tenant["roles"]):
                    if "id" not in role:
                        errors.append(f"Missing 'id' in role at index {i}")
                    if "name" not in role:
                        errors.append(f"Missing 'name' in role at index {i}")
                    if "access" not in role:
                        errors.append(f"Missing 'access' in role at index {i}")
                    elif "entities" not in role["access"]:
                        errors.append(f"Missing 'entities' in role access at index {i}")
                    elif "objectives" not in role["access"]:
                        errors.append(f"Missing 'objectives' in role access at index {i}")
        
        # Validate permission types
        if "permission_types" in data:
            for i, perm in enumerate(data["permission_types"]):
                if "id" not in perm:
                    errors.append(f"Missing 'id' in permission_type at index {i}")
                if "description" not in perm:
                    errors.append(f"Missing 'description' in permission_type at index {i}")
                if "capabilities" not in perm:
                    errors.append(f"Missing 'capabilities' in permission_type at index {i}")
        
        return len(errors) == 0, errors
        
    except yaml.YAMLError as e:
        return False, [f"YAML parsing error: {str(e)}"]
    except Exception as e:
        return False, [f"Unexpected error: {str(e)}"]

def validate_entities_component(yaml_content: str) -> Tuple[bool, List[str]]:
    """Validate the entities component of the YAML"""
    try:
        data = yaml.safe_load(yaml_content)
        errors = []
        
        # Check required sections
        if "entities" not in data:
            errors.append("Missing 'entities' section in entities component")
            return False, errors
        
        # Validate entities
        for i, entity in enumerate(data["entities"]):
            if "id" not in entity:
                errors.append(f"Missing 'id' in entity at index {i}")
            if "name" not in entity:
                errors.append(f"Missing 'name' in entity at index {i}")
            if "type" not in entity:
                errors.append(f"Missing 'type' in entity at index {i}")
            if "version" not in entity:
                errors.append(f"Missing 'version' in entity at index {i}")
            if "status" not in entity:
                errors.append(f"Missing 'status' in entity at index {i}")
            
            # Validate attributes_metadata
            if "attributes_metadata" not in entity:
                errors.append(f"Missing 'attributes_metadata' in entity {entity.get('name', f'at index {i}')}")
            else:
                metadata = entity["attributes_metadata"]
                if "attribute_prefix" not in metadata:
                    errors.append(f"Missing 'attribute_prefix' in entity {entity.get('name', f'at index {i}')} metadata")
                if "attribute_map" not in metadata:
                    errors.append(f"Missing 'attribute_map' in entity {entity.get('name', f'at index {i}')} metadata")
                if "required_attributes" not in metadata:
                    errors.append(f"Missing 'required_attributes' in entity {entity.get('name', f'at index {i}')} metadata")
            
            # Validate attributes
            if "attributes" not in entity:
                errors.append(f"Missing 'attributes' in entity {entity.get('name', f'at index {i}')}")
            else:
                for j, attr in enumerate(entity["attributes"]):
                    if "id" not in attr:
                        errors.append(f"Missing 'id' in attribute at index {j} of entity {entity.get('name', f'at index {i}')}")
                    if "name" not in attr:
                        errors.append(f"Missing 'name' in attribute at index {j} of entity {entity.get('name', f'at index {i}')}")
                    if "display_name" not in attr:
                        errors.append(f"Missing 'display_name' in attribute at index {j} of entity {entity.get('name', f'at index {i}')}")
                    if "datatype" not in attr:
                        errors.append(f"Missing 'datatype' in attribute at index {j} of entity {entity.get('name', f'at index {i}')}")
                    if "required" not in attr:
                        errors.append(f"Missing 'required' in attribute at index {j} of entity {entity.get('name', f'at index {i}')}")
                    if "version" not in attr:
                        errors.append(f"Missing 'version' in attribute at index {j} of entity {entity.get('name', f'at index {i}')}")
                    if "status" not in attr:
                        errors.append(f"Missing 'status' in attribute at index {j} of entity {entity.get('name', f'at index {i}')}")
                    
                    # Validate enum attributes
                    if attr.get("datatype") == "Enum" and "values" not in attr:
                        errors.append(f"Missing 'values' in enum attribute {attr.get('name', f'at index {j}')} of entity {entity.get('name', f'at index {i}')}")
        
        return len(errors) == 0, errors
        
    except yaml.YAMLError as e:
        return False, [f"YAML parsing error: {str(e)}"]
    except Exception as e:
        return False, [f"Unexpected error: {str(e)}"]

def validate_go_definitions_component(yaml_content: str) -> Tuple[bool, List[str]]:
    """Validate the GO definitions component of the YAML"""
    try:
        data = yaml.safe_load(yaml_content)
        errors = []
        
        # Check required sections
        if "global_objectives" not in data:
            errors.append("Missing 'global_objectives' section in GO definitions component")
            return False, errors
        
        # Validate global objectives
        for i, go in enumerate(data["global_objectives"]):
            if "id" not in go:
                errors.append(f"Missing 'id' in global objective at index {i}")
            if "name" not in go:
                errors.append(f"Missing 'name' in global objective at index {i}")
            if "version" not in go:
                errors.append(f"Missing 'version' in global objective at index {i}")
            if "status" not in go:
                errors.append(f"Missing 'status' in global objective at index {i}")
            
            # Validate input stack
            if "input_stack" not in go:
                errors.append(f"Missing 'input_stack' in global objective {go.get('name', f'at index {i}')}")
            elif "description" not in go["input_stack"]:
                errors.append(f"Missing 'description' in input_stack of global objective {go.get('name', f'at index {i}')}")
            elif "inputs" not in go["input_stack"]:
                errors.append(f"Missing 'inputs' in input_stack of global objective {go.get('name', f'at index {i}')}")
            
            # Validate output stack
            if "output_stack" not in go:
                errors.append(f"Missing 'output_stack' in global objective {go.get('name', f'at index {i}')}")
            elif "description" not in go["output_stack"]:
                errors.append(f"Missing 'description' in output_stack of global objective {go.get('name', f'at index {i}')}")
            elif "outputs" not in go["output_stack"]:
                errors.append(f"Missing 'outputs' in output_stack of global objective
