"""
Component Validator for YAML Builder v2

This module provides functionality for validating parsed components before deployment.
"""

import os
import sys
import logging
from typing import Dict, List, Tuple, Any, Optional

# Import database utilities
from db_utils import execute_query

# Set up logging
logger = logging.getLogger('component_validator')

def validate_entities(entity_data: Dict) -> Tuple[bool, List[str]]:
    """
    Validate parsed entity data.
    
    Args:
        entity_data: Parsed entity data
        
    Returns:
        Tuple containing:
            - Boolean indicating if validation was successful
            - List of messages (warnings, errors, or success messages)
    """
    messages = []
    
    try:
        logger.info("Validating entity data")
        
        # Check if entity data is valid
        if not entity_data or 'entities' not in entity_data:
            logger.error("Invalid entity data: 'entities' key not found")
            return False, ["Invalid entity data: 'entities' key not found"]
        
        entities = entity_data['entities']
        
        # Check if entities is a dictionary
        if not isinstance(entities, dict):
            logger.error("Invalid entity data: 'entities' is not a dictionary")
            return False, ["Invalid entity data: 'entities' is not a dictionary"]
        
        # Validate each entity
        for entity_name, entity in entities.items():
            # Check if entity is a dictionary
            if not isinstance(entity, dict):
                logger.error(f"Invalid entity {entity_name}: not a dictionary")
                return False, [f"Invalid entity {entity_name}: not a dictionary"]
            
            # Check if entity has a name
            if 'name' not in entity:
                logger.error(f"Invalid entity {entity_name}: 'name' key not found")
                return False, [f"Invalid entity {entity_name}: 'name' key not found"]
            
            # Check if entity name matches key
            if entity['name'] != entity_name:
                logger.warning(f"Entity name '{entity['name']}' does not match key '{entity_name}'")
                messages.append(f"Warning: Entity name '{entity['name']}' does not match key '{entity_name}'")
            
            # Validate attributes
            if 'attributes' in entity:
                if not isinstance(entity['attributes'], dict):
                    logger.error(f"Invalid entity {entity_name}: 'attributes' is not a dictionary")
                    return False, [f"Invalid entity {entity_name}: 'attributes' is not a dictionary"]
                
                # Check for primary key
                has_primary_key = False
                for attr_name, attr in entity['attributes'].items():
                    if not isinstance(attr, dict):
                        logger.error(f"Invalid attribute {attr_name} in entity {entity_name}: not a dictionary")
                        return False, [f"Invalid attribute {attr_name} in entity {entity_name}: not a dictionary"]
                    
                    if 'primary_key' in attr and attr['primary_key']:
                        has_primary_key = True
                
                if not has_primary_key:
                    logger.warning(f"Entity {entity_name} has no primary key attribute")
                    messages.append(f"Warning: Entity {entity_name} has no primary key attribute")
            else:
                logger.warning(f"Entity {entity_name} has no attributes")
                messages.append(f"Warning: Entity {entity_name} has no attributes")
            
            # Validate relationships
            if 'relationships' in entity:
                if not isinstance(entity['relationships'], dict):
                    logger.error(f"Invalid entity {entity_name}: 'relationships' is not a dictionary")
                    return False, [f"Invalid entity {entity_name}: 'relationships' is not a dictionary"]
                
                for rel_id, rel in entity['relationships'].items():
                    if not isinstance(rel, dict):
                        logger.error(f"Invalid relationship {rel_id} in entity {entity_name}: not a dictionary")
                        return False, [f"Invalid relationship {rel_id} in entity {entity_name}: not a dictionary"]
                    
                    if 'entity' not in rel:
                        logger.error(f"Invalid relationship {rel_id} in entity {entity_name}: 'entity' key not found")
                        return False, [f"Invalid relationship {rel_id} in entity {entity_name}: 'entity' key not found"]
                    
                    if 'type' not in rel:
                        logger.warning(f"Relationship {rel_id} in entity {entity_name} has no type, defaulting to 'one-to-many'")
                        messages.append(f"Warning: Relationship {rel_id} in entity {entity_name} has no type, defaulting to 'one-to-many'")
                    
                    if 'source_attribute' not in rel:
                        logger.error(f"Invalid relationship {rel_id} in entity {entity_name}: 'source_attribute' key not found")
                        return False, [f"Invalid relationship {rel_id} in entity {entity_name}: 'source_attribute' key not found"]
                    
                    if 'target_attribute' not in rel:
                        logger.error(f"Invalid relationship {rel_id} in entity {entity_name}: 'target_attribute' key not found")
                        return False, [f"Invalid relationship {rel_id} in entity {entity_name}: 'target_attribute' key not found"]
            
            # Validate business rules
            if 'business_rules' in entity:
                if not isinstance(entity['business_rules'], dict):
                    logger.error(f"Invalid entity {entity_name}: 'business_rules' is not a dictionary")
                    return False, [f"Invalid entity {entity_name}: 'business_rules' is not a dictionary"]
                
                for rule_name, rule in entity['business_rules'].items():
                    if not isinstance(rule, dict):
                        logger.error(f"Invalid business rule {rule_name} in entity {entity_name}: not a dictionary")
                        return False, [f"Invalid business rule {rule_name} in entity {entity_name}: not a dictionary"]
                    
                    if 'operation' not in rule:
                        logger.warning(f"Business rule {rule_name} in entity {entity_name} has no operation")
                        messages.append(f"Warning: Business rule {rule_name} in entity {entity_name} has no operation")
            
            # Validate calculated fields
            if 'calculated_fields' in entity:
                if not isinstance(entity['calculated_fields'], dict):
                    logger.error(f"Invalid entity {entity_name}: 'calculated_fields' is not a dictionary")
                    return False, [f"Invalid entity {entity_name}: 'calculated_fields' is not a dictionary"]
                
                for field_id, field in entity['calculated_fields'].items():
                    if not isinstance(field, dict):
                        logger.error(f"Invalid calculated field {field_id} in entity {entity_name}: not a dictionary")
                        return False, [f"Invalid calculated field {field_id} in entity {entity_name}: not a dictionary"]
                    
                    if 'attribute' not in field:
                        logger.error(f"Invalid calculated field {field_id} in entity {entity_name}: 'attribute' key not found")
                        return False, [f"Invalid calculated field {field_id} in entity {entity_name}: 'attribute' key not found"]
                    
                    if 'formula' not in field:
                        logger.error(f"Invalid calculated field {field_id} in entity {entity_name}: 'formula' key not found")
                        return False, [f"Invalid calculated field {field_id} in entity {entity_name}: 'formula' key not found"]
            
            # Validate validations
            if 'validations' in entity:
                if not isinstance(entity['validations'], dict):
                    logger.error(f"Invalid entity {entity_name}: 'validations' is not a dictionary")
                    return False, [f"Invalid entity {entity_name}: 'validations' is not a dictionary"]
                
                for validation_id, validation in entity['validations'].items():
                    if not isinstance(validation, dict):
                        logger.error(f"Invalid validation {validation_id} in entity {entity_name}: not a dictionary")
                        return False, [f"Invalid validation {validation_id} in entity {entity_name}: not a dictionary"]
                    
                    if 'attribute' not in validation:
                        logger.error(f"Invalid validation {validation_id} in entity {entity_name}: 'attribute' key not found")
                        return False, [f"Invalid validation {validation_id} in entity {entity_name}: 'attribute' key not found"]
                    
                    if 'constraint' not in validation:
                        logger.error(f"Invalid validation {validation_id} in entity {entity_name}: 'constraint' key not found")
                        return False, [f"Invalid validation {validation_id} in entity {entity_name}: 'constraint' key not found"]
            
            # Validate constraints
            if 'constraints' in entity:
                if not isinstance(entity['constraints'], dict):
                    logger.error(f"Invalid entity {entity_name}: 'constraints' is not a dictionary")
                    return False, [f"Invalid entity {entity_name}: 'constraints' is not a dictionary"]
                
                for constraint_id, constraint in entity['constraints'].items():
                    if not isinstance(constraint, dict):
                        logger.error(f"Invalid constraint {constraint_id} in entity {entity_name}: not a dictionary")
                        return False, [f"Invalid constraint {constraint_id} in entity {entity_name}: not a dictionary"]
                    
                    if 'target_entity' not in constraint:
                        logger.error(f"Invalid constraint {constraint_id} in entity {entity_name}: 'target_entity' key not found")
                        return False, [f"Invalid constraint {constraint_id} in entity {entity_name}: 'target_entity' key not found"]
                    
                    if 'constrained_entity' not in constraint:
                        logger.error(f"Invalid constraint {constraint_id} in entity {entity_name}: 'constrained_entity' key not found")
                        return False, [f"Invalid constraint {constraint_id} in entity {entity_name}: 'constrained_entity' key not found"]
                    
                    if 'foreign_key_attributes' not in constraint:
                        logger.error(f"Invalid constraint {constraint_id} in entity {entity_name}: 'foreign_key_attributes' key not found")
                        return False, [f"Invalid constraint {constraint_id} in entity {entity_name}: 'foreign_key_attributes' key not found"]
        
        messages.append(f"Successfully validated {len(entities)} entities")
        return True, messages
    except Exception as e:
        logger.error(f"Error validating entity data: {str(e)}", exc_info=True)
        messages.append(f"Error validating entity data: {str(e)}")
        return False, messages

def validate_go_definitions(go_data: Dict) -> Tuple[bool, List[str]]:
    """
    Validate parsed GO data.
    
    Args:
        go_data: Parsed GO data
        
    Returns:
        Tuple containing:
            - Boolean indicating if validation was successful
            - List of messages (warnings, errors, or success messages)
    """
    messages = []
    
    try:
        logger.info("Validating global objective data")
        
        # Check if GO data is valid
        if not go_data or 'global_objectives' not in go_data:
            logger.error("Invalid GO data: 'global_objectives' key not found")
            return False, ["Invalid GO data: 'global_objectives' key not found"]
        
        global_objectives = go_data['global_objectives']
        
        # Check if global_objectives is a dictionary
        if not isinstance(global_objectives, dict):
            logger.error("Invalid GO data: 'global_objectives' is not a dictionary")
            return False, ["Invalid GO data: 'global_objectives' is not a dictionary"]
        
        # Validate each global objective
        for go_id, go in global_objectives.items():
            # Check if GO is a dictionary
            if not isinstance(go, dict):
                logger.error(f"Invalid global objective {go_id}: not a dictionary")
                return False, [f"Invalid global objective {go_id}: not a dictionary"]
            
            # Check if GO has a go_id
            if 'go_id' not in go:
                logger.error(f"Invalid global objective {go_id}: 'go_id' key not found")
                return False, [f"Invalid global objective {go_id}: 'go_id' key not found"]
            
            # Check if GO ID matches key
            if go['go_id'] != go_id:
                logger.warning(f"Global objective ID '{go['go_id']}' does not match key '{go_id}'")
                messages.append(f"Warning: Global objective ID '{go['go_id']}' does not match key '{go_id}'")
            
            # Check if GO has a name
            if 'name' not in go:
                logger.error(f"Invalid global objective {go_id}: 'name' key not found")
                return False, [f"Invalid global objective {go_id}: 'name' key not found"]
            
            # Validate input stack
            if 'input_stack' in go:
                if not isinstance(go['input_stack'], list):
                    logger.error(f"Invalid global objective {go_id}: 'input_stack' is not a list")
                    return False, [f"Invalid global objective {go_id}: 'input_stack' is not a list"]
                
                for input_item in go['input_stack']:
                    if not isinstance(input_item, dict):
                        logger.error(f"Invalid input stack item in global objective {go_id}: not a dictionary")
                        return False, [f"Invalid input stack item in global objective {go_id}: not a dictionary"]
                    
                    if 'slot_id' not in input_item:
                        logger.error(f"Invalid input stack item in global objective {go_id}: 'slot_id' key not found")
                        return False, [f"Invalid input stack item in global objective {go_id}: 'slot_id' key not found"]
                    
                    if 'entity_reference' not in input_item:
                        logger.error(f"Invalid input stack item {input_item.get('slot_id')} in global objective {go_id}: 'entity_reference' key not found")
                        return False, [f"Invalid input stack item {input_item.get('slot_id')} in global objective {go_id}: 'entity_reference' key not found"]
            else:
                logger.warning(f"Global objective {go_id} has no input stack")
                messages.append(f"Warning: Global objective {go_id} has no input stack")
            
            # Validate output stack
            if 'output_stack' in go:
                if not isinstance(go['output_stack'], list):
                    logger.error(f"Invalid global objective {go_id}: 'output_stack' is not a list")
                    return False, [f"Invalid global objective {go_id}: 'output_stack' is not a list"]
                
                for output_item in go['output_stack']:
                    if not isinstance(output_item, dict):
                        logger.error(f"Invalid output stack item in global objective {go_id}: not a dictionary")
                        return False, [f"Invalid output stack item in global objective {go_id}: not a dictionary"]
                    
                    if 'slot_id' not in output_item:
                        logger.error(f"Invalid output stack item in global objective {go_id}: 'slot_id' key not found")
                        return False, [f"Invalid output stack item in global objective {go_id}: 'slot_id' key not found"]
                    
                    if 'output_entity' not in output_item:
                        logger.error(f"Invalid output stack item {output_item.get('slot_id')} in global objective {go_id}: 'output_entity' key not found")
                        return False, [f"Invalid output stack item {output_item.get('slot_id')} in global objective {go_id}: 'output_entity' key not found"]
            else:
                logger.warning(f"Global objective {go_id} has no output stack")
                messages.append(f"Warning: Global objective {go_id} has no output stack")
            
            # Validate output triggers
            if 'output_triggers' in go:
                if not isinstance(go['output_triggers'], list):
                    logger.error(f"Invalid global objective {go_id}: 'output_triggers' is not a list")
                    return False, [f"Invalid global objective {go_id}: 'output_triggers' is not a list"]
                
                for trigger in go['output_triggers']:
                    if not isinstance(trigger, dict):
                        logger.error(f"Invalid output trigger in global objective {go_id}: not a dictionary")
                        return False, [f"Invalid output trigger in global objective {go_id}: not a dictionary"]
                    
                    if 'output_item_id' not in trigger:
                        logger.error(f"Invalid output trigger in global objective {go_id}: 'output_item_id' key not found")
                        return False, [f"Invalid output trigger in global objective {go_id}: 'output_item_id' key not found"]
                    
                    if 'target_objective' not in trigger:
                        logger.error(f"Invalid output trigger {trigger.get('output_item_id')} in global objective {go_id}: 'target_objective' key not found")
                        return False, [f"Invalid output trigger {trigger.get('output_item_id')} in global objective {go_id}: 'target_objective' key not found"]
                    
                    if 'target_input' not in trigger:
                        logger.error(f"Invalid output trigger {trigger.get('output_item_id')} in global objective {go_id}: 'target_input' key not found")
                        return False, [f"Invalid output trigger {trigger.get('output_item_id')} in global objective {go_id}: 'target_input' key not found"]
            
            # Validate process flow
            if 'process_flow' in go:
                if not isinstance(go['process_flow'], list):
                    logger.error(f"Invalid global objective {go_id}: 'process_flow' is not a list")
                    return False, [f"Invalid global objective {go_id}: 'process_flow' is not a list"]
                
                for flow_step in go['process_flow']:
                    if not isinstance(flow_step, dict):
                        logger.error(f"Invalid process flow step in global objective {go_id}: not a dictionary")
                        return False, [f"Invalid process flow step in global objective {go_id}: not a dictionary"]
                    
                    if 'sequence_number' not in flow_step:
                        logger.error(f"Invalid process flow step in global objective {go_id}: 'sequence_number' key not found")
                        return False, [f"Invalid process flow step in global objective {go_id}: 'sequence_number' key not found"]
                    
                    if 'lo_name' not in flow_step:
                        logger.error(f"Invalid process flow step {flow_step.get('sequence_number')} in global objective {go_id}: 'lo_name' key not found")
                        return False, [f"Invalid process flow step {flow_step.get('sequence_number')} in global objective {go_id}: 'lo_name' key not found"]
            else:
                logger.warning(f"Global objective {go_id} has no process flow")
                messages.append(f"Warning: Global objective {go_id} has no process flow")
            
            # Validate parallel flows
            if 'parallel_flows' in go:
                if not isinstance(go['parallel_flows'], list):
                    logger.error(f"Invalid global objective {go_id}: 'parallel_flows' is not a list")
                    return False, [f"Invalid global objective {go_id}: 'parallel_flows' is not a list"]
                
                for flow in go['parallel_flows']:
                    if not isinstance(flow, dict):
                        logger.error(f"Invalid parallel flow in global objective {go_id}: not a dictionary")
                        return False, [f"Invalid parallel flow in global objective {go_id}: not a dictionary"]
                    
                    if 'lo_name' not in flow:
                        logger.error(f"Invalid parallel flow in global objective {go_id}: 'lo_name' key not found")
                        return False, [f"Invalid parallel flow in global objective {go_id}: 'lo_name' key not found"]
                    
                    if 'join_at' not in flow:
                        logger.error(f"Invalid parallel flow {flow.get('lo_name')} in global objective {go_id}: 'join_at' key not found")
                        return False, [f"Invalid parallel flow {flow.get('lo_name')} in global objective {go_id}: 'join_at' key not found"]
            
            # Validate rollback pathways
            if 'rollback_pathways' in go:
                if not isinstance(go['rollback_pathways'], list):
                    logger.error(f"Invalid global objective {go_id}: 'rollback_pathways' is not a list")
                    return False, [f"Invalid global objective {go_id}: 'rollback_pathways' is not a list"]
                
                for pathway in go['rollback_pathways']:
                    if not isinstance(pathway, dict):
                        logger.error(f"Invalid rollback pathway in global objective {go_id}: not a dictionary")
                        return False, [f"Invalid rollback pathway in global objective {go_id}: not a dictionary"]
                    
                    if 'source_lo' not in pathway:
                        logger.error(f"Invalid rollback pathway in global objective {go_id}: 'source_lo' key not found")
                        return False, [f"Invalid rollback pathway in global objective {go_id}: 'source_lo' key not found"]
                    
                    if 'target_lo' not in pathway:
                        logger.error(f"Invalid rollback pathway {pathway.get('source_lo')} in global objective {go_id}: 'target_lo' key not found")
                        return False, [f"Invalid rollback pathway {pathway.get('source_lo')} in global objective {go_id}: 'target_lo' key not found"]
            
            # Validate business rules
            if 'business_rules' in go:
                if not isinstance(go['business_rules'], list):
                    logger.error(f"Invalid global objective {go_id}: 'business_rules' is not a list")
                    return False, [f"Invalid global objective {go_id}: 'business_rules' is not a list"]
                
                for rule in go['business_rules']:
                    if not isinstance(rule, dict):
                        logger.error(f"Invalid business rule in global objective {go_id}: not a dictionary")
                        return False, [f"Invalid business rule in global objective {go_id}: not a dictionary"]
                    
                    if 'description' not in rule:
                        logger.error(f"Invalid business rule in global objective {go_id}: 'description' key not found")
                        return False, [f"Invalid business rule in global objective {go_id}: 'description' key not found"]
                    
                    if 'enforced_by' not in rule:
                        logger.error(f"Invalid business rule in global objective {go_id}: 'enforced_by' key not found")
                        return False, [f"Invalid business rule in global objective {go_id}: 'enforced_by' key not found"]
            
            # Validate data constraints
            if 'data_constraints' in go:
                if not isinstance(go['data_constraints'], list):
                    logger.error(f"Invalid global objective {go_id}: 'data_constraints' is not a list")
                    return False, [f"Invalid global objective {go_id}: 'data_constraints' is not a list"]
                
                for constraint in go['data_constraints']:
                    if not isinstance(constraint, dict):
                        logger.error(f"Invalid data constraint in global objective {go_id}: not a dictionary")
                        return False, [f"Invalid data constraint in global objective {go_id}: not a dictionary"]
                    
                    if 'entity' not in constraint:
                        logger.error(f"Invalid data constraint in global objective {go_id}: 'entity' key not found")
                        return False, [f"Invalid data constraint in global objective {go_id}: 'entity' key not found"]
                    
                    if 'attribute' not in constraint:
                        logger.error(f"Invalid data constraint in global objective {go_id}: 'attribute' key not found")
                        return False, [f"Invalid data constraint in global objective {go_id}: 'attribute' key not found"]
                    
                    if 'rule' not in constraint:
                        logger.error(f"Invalid data constraint in global objective {go_id}: 'rule' key not found")
                        return False, [f"Invalid data constraint in global objective {go_id}: 'rule' key not found"]
        
        messages.append(f"Successfully validated {len(global_objectives)} global objectives")
        return True, messages
    except Exception as e:
        logger.error(f"Error validating global objective data: {str(e)}", exc_info=True)
        messages.append(f"Error validating global objective data: {str(e)}")
        return False, messages

def validate_lo_definitions(lo_data: Dict) -> Tuple[bool, List[str]]:
    """
    Validate parsed LO data.
    
    Args:
        lo_data: Parsed LO data
        
    Returns:
        Tuple containing:
            - Boolean indicating if validation was successful
            - List of messages (warnings, errors, or success messages)
    """
    messages = []
    
    try:
        logger.info("Validating local objective data")
        
        # Check if LO data is valid
        if not lo_data or 'local_objectives' not in lo_data:
            logger.error("Invalid LO data: 'local_objectives' key not found")
            return False, ["Invalid LO data: 'local_objectives' key not found"]
        
        local_objectives = lo_data['local_objectives']
        
        # Check if local_objectives is a dictionary
        if not isinstance(local_objectives, dict):
            logger.error("Invalid LO data: 'local_objectives' is not a dictionary")
            return False, ["Invalid LO data: 'local_objectives' is not a dictionary"]
        
        # Validate each local objective
        for lo_name, lo in local_objectives.items():
            # Check if LO is a dictionary
            if not isinstance(lo, dict):
                logger.error(f"Invalid local objective {lo_name}: not a dictionary")
                return False, [f"Invalid local objective {lo_name}: not a dictionary"]
            
            # Check if LO has a lo_name
            if 'lo_name' not in lo:
                logger.error(f"Invalid local objective {lo_name}: 'lo_name' key not found")
                return False, [f"Invalid local objective {lo_name}: 'lo_name' key not found"]
            
            # Check if LO name matches key
            if lo['lo_name'] != lo_name:
                logger.warning(f"Local objective name '{lo['lo_name']}' does not match key '{lo_name}'")
                messages.append(f"Warning: Local objective name '{lo['lo_name']}' does not match key '{lo_name}'")
            
            # Check if LO has a function_type
            if 'function_type' not in lo:
                logger.warning(f"Local objective {lo_name} has no function_type, defaulting to 'standard'")
                messages.append(f"Warning: Local objective {lo_name} has no function_type, defaulting to 'standard'")
            
            # Validate input stack
            if 'input_stack' in lo:
                if not isinstance(lo['input_stack'], list):
                    logger.error(f"Invalid local objective {lo_name}: 'input_stack' is not a list")
                    return False, [f"Invalid local objective {lo_name}: 'input_stack' is not a list"]
                
                for input_item in lo['input_stack']:
                    if not isinstance(input_item, dict):
                        logger.error(f"Invalid input stack item in local objective {lo_name}: not a dictionary")
                        return False, [f"Invalid input stack item in local objective {lo_name}: not a dictionary"]
                    
                    if 'slot_id' not in input_item:
                        logger.error(f"Invalid input stack item in local objective {lo_name}: 'slot_id' key not found")
                        return False, [f"Invalid input stack item in local objective {lo_name}: 'slot_id' key not found"]
                    
                    if 'entity_reference' not in input_item:
                        logger.error(f"Invalid input stack item {input_item.get('slot_id')} in local objective {lo_name}: 'entity_reference' key not found")
                        return False, [f"Invalid input stack item {input_item.get('slot_id')} in local objective {lo_name}: 'entity_reference' key not found"]
            else:
                logger.warning(f"Local objective {lo_name} has no input stack")
                messages.append(f"Warning: Local objective {lo_name} has no input stack")
            
            # Validate output stack
            if 'output_stack' in lo:
                if not isinstance(lo['output_stack'], list):
                    logger.error(f"Invalid local objective {lo_name}: 'output_stack' is not a list")
                    return False, [f"Invalid local objective {lo_name}: 'output_stack' is not a list"]
                
                for output_item in lo['output_stack']:
                    if not isinstance(output_item, dict):
                        logger.error(f"Invalid output stack item in local objective {lo_name}: not a dictionary")
                        return False, [f"Invalid output stack item in local objective {lo_name}: not a dictionary"]
                    
                    if 'slot_id' not in output_item:
                        logger.error(f"Invalid output stack item in local objective {lo_name}: 'slot_id' key not found")
                        return False, [f"Invalid output stack item in local objective {lo_name}: 'slot_id' key not found"]
                    
                    if 'output_entity' not in output_item:
                        logger.error(f"Invalid output stack item {output_item.get('slot_id')} in local objective {lo_name}: 'output_entity' key not found")
                        return False, [f"Invalid output stack item {output_item.get('slot_id')} in local objective {lo_name}: 'output_entity' key not found"]
            else:
                logger.warning(f"Local objective {lo_name} has no output stack")
                messages.append(f"Warning: Local objective {lo_name} has no output stack")
            
            # Validate execution pathways
            if 'execution_pathways' in lo:
                if not isinstance(lo['execution_pathways'], list):
                    logger.error(f"Invalid local objective {lo_name}: 'execution_pathways' is not a list")
                    return False, [f"Invalid local objective {lo_name}: 'execution_pathways' is not a list"]
                
                for pathway in lo['execution_pathways']:
                    if not isinstance(pathway, dict):
                        logger.error(f"Invalid execution pathway in local objective {lo_name}: not a dictionary")
                        return False, [f"Invalid execution pathway in local objective {lo_name}: not a dictionary"]
                    
                    if 'current_lo' not in pathway:
                        logger.error(f"Invalid execution pathway in local objective {lo_name}: 'current_lo' key not found")
                        return False, [f"Invalid execution pathway in local objective {lo_name}: 'current_lo' key not found"]
                    
                    if 'next_lo' not in pathway:
                        logger.error(f"Invalid execution pathway in local objective {lo_name}: 'next_lo' key not found")
                        return False, [f"Invalid execution pathway in local objective {lo_name}: 'next_lo' key not found"]
            
            # Validate execution pathway conditions
            if 'execution_pathway_conditions' in lo:
                if not isinstance(lo['execution_pathway_conditions'], list):
                    logger.error(f"Invalid local objective {lo_name}: 'execution_pathway_conditions' is not a list")
                    return False, [f"Invalid local objective {lo_name}: 'execution_pathway_conditions' is not a list"]
                
                for condition in lo['execution_pathway_conditions']:
                    if not isinstance(condition, dict):
                        logger.error(f"Invalid execution pathway condition in local objective {lo_name}: not a dictionary")
                        return False, [f"Invalid execution pathway condition in local objective {lo_name}: not a dictionary"]
                    
                    if 'condition' not in condition:
                        logger.error(f"Invalid execution pathway condition in local objective {lo_name}: 'condition' key not found")
                        return False, [f"Invalid execution pathway condition in local objective {lo_name}: 'condition' key not found"]
                    
                    if 'next_lo' not in condition:
                        logger.error(f"Invalid execution pathway condition in local objective {lo_name}: 'next_lo' key not found")
                        return False, [f"Invalid execution pathway condition in local objective {lo_name}: 'next_lo' key not found"]
            
            # Validate nested functions
            if 'nested_functions' in lo:
                if not isinstance(lo['nested_functions'], list):
                    logger.error(f"Invalid local objective {lo_name}: 'nested_functions' is not a list")
                    return False, [f"Invalid local objective {lo_name}: 'nested_functions' is not a list"]
                
                for function in lo['nested_functions']:
                    if not isinstance(function, dict):
                        logger.error(f"Invalid nested function in local objective {lo_name}: not a dictionary")
                        return False, [f"Invalid nested function in local objective {lo_name}: not a dictionary"]
                    
                    if 'function_name' not in function:
                        logger.error(f"Invalid nested function in local objective {lo_name}: 'function_name' key not found")
                        return False, [f"Invalid nested function in local objective {lo_name}: 'function_name' key not found"]
                    
                    if 'writes_to' not in function:
                        logger.error(f"Invalid nested function {function.get('function_name')} in local objective {lo_name}: 'writes_to' key not found")
                        return False, [f"Invalid nested function {function.get('function_name')} in local objective {lo_name}: 'writes_to' key not found"]
            
            # Validate system functions
            if 'system_functions' in lo:
                if not isinstance(lo['system_functions'], list):
                    logger.error(f"Invalid local objective {lo_name}: 'system_functions' is not a list")
                    return False, [f"Invalid local objective {lo_name}: 'system_functions' is not a list"]
                
                for function in lo['system_functions']:
                    if not isinstance(function, dict):
                        logger.error(f"Invalid system function in local objective {lo_name}: not a dictionary")
                        return False, [f"Invalid system function in local objective {lo_name}: not a dictionary"]
                    
                    if 'function_id' not in function:
                        logger.error(f"Invalid system function in local objective {lo_name}: 'function_id' key not found")
                        return False, [f"Invalid system function in local objective {lo_name}: 'function_id' key not found"]
                    
                    if 'function_name' not in function:
                        logger.error(f"Invalid system function {function.get('function_id')} in local objective {lo_name}: 'function_name' key not found")
                        return False, [f"Invalid system function {function.get('function_id')} in local objective {lo_name}: 'function_name' key not found"]
                    
                    if 'output_to' not in function:
                        logger.error(f"Invalid system function {function.get('function_id')} in local objective {lo_name}: 'output_to' key not found")
                        return False, [f"Invalid system function {function.get('function_id')} in local objective {lo_name}: 'output_to' key not found"]
            
            # Validate input validations
            if 'input_validations' in lo:
                if not isinstance(lo['input_validations'], list):
                    logger.error(f"Invalid local objective {lo_name}: 'input_validations' is not a list")
                    return False, [f"Invalid local objective {lo_name}: 'input_validations' is not a list"]
                
                for validation in lo['input_validations']:
                    if not isinstance(validation, dict):
                        logger.error(f"Invalid input validation in local objective {lo_name}: not a dictionary")
                        return False, [f"Invalid input validation in local objective {lo_name}: not a dictionary"]
                    
                    if 'entity' not in validation:
                        logger.error(f"Invalid input validation in local objective {lo_name}: 'entity' key not found")
                        return False, [f"Invalid input validation in local objective {lo_name}: 'entity' key not found"]
                    
                    if 'attribute' not in validation:
                        logger.error(f"Invalid input validation in local objective {lo_name}: 'attribute' key not found")
                        return False, [f"Invalid input validation in local objective {lo_name}: 'attribute' key not found"]
                    
                    if 'rule' not in validation:
                        logger.error(f"Invalid input validation in local objective {lo_name}: 'rule' key not found")
                        return False, [f"Invalid input validation in local objective {lo_name}: 'rule' key not found"]
        
        messages.append(f"Successfully validated {len(local_objectives)} local objectives")
        return True, messages
    except Exception as e:
        logger.error(f"Error validating local objective data: {str(e)}", exc_info=True)
        messages.append(f"Error validating local objective data: {str(e)}")
        return False, messages

def validate_roles(role_data: Dict) -> Tuple[bool, List[str]]:
    """
    Validate parsed Role data.
    
    Args:
        role_data: Parsed Role data
        
    Returns:
        Tuple containing:
            - Boolean indicating if validation was successful
            - List of messages (warnings, errors, or success messages)
    """
    messages = []
    
    try:
        logger.info("Validating role data")
        
        # Check if Role data is valid
        if not role_data or 'roles' not in role_data:
            logger.error("Invalid Role data: 'roles' key not found")
            return False, ["Invalid Role data: 'roles' key not found"]
        
        roles = role_data['roles']
        
        # Check if roles is a dictionary
        if not isinstance(roles, dict):
            logger.error("Invalid Role data: 'roles' is not a dictionary")
            return False, ["Invalid Role data: 'roles' is not a dictionary"]
        
        # Validate each role
        for role_id, role in roles.items():
            # Check if role is a dictionary
            if not isinstance(role, dict):
                logger.error(f"Invalid role {role_id}: not a dictionary")
                return False, [f"Invalid role {role_id}: not a dictionary"]
            
            # Check if role has a role_id
            if 'role_id' not in role:
                logger.error(f"Invalid role {role_id}: 'role_id' key not found")
                return False, [f"Invalid role {role_id}: 'role_id' key not found"]
            
            # Check if role ID matches key
            if role['role_id'] != role_id:
                logger.warning(f"Role ID '{role['role_id']}' does not match key '{role_id}'")
                messages.append(f"Warning: Role ID '{role['role_id']}' does not match key '{role_id}'")
            
            # Check if role has a name
            if 'name' not in role:
                logger.error(f"Invalid role {role_id}: 'name' key not found")
                return False, [f"Invalid role {role_id}: 'name' key not found"]
            
            # Validate permissions
            if 'permissions' in role:
                if not isinstance(role['permissions'], list):
                    logger.error(f"Invalid role {role_id}: 'permissions' is not a list")
                    return False, [f"Invalid role {role_id}: 'permissions' is not a list"]
                
                for permission in role['permissions']:
                    if not isinstance(permission, str):
                        logger.error(f"Invalid permission in role {role_id}: not a string")
                        return False, [f"Invalid permission in role {role_id}: not a string"]
            else:
                logger.warning(f"Role {role_id} has no permissions")
                messages.append(f"Warning: Role {role_id} has no permissions")
            
            # Validate special conditions
            if 'special_conditions' in role:
                if not isinstance(role['special_conditions'], list):
                    logger.error(f"Invalid role {role_id}: 'special_conditions' is not a list")
                    return False, [f"Invalid role {role_id}: 'special_conditions' is not a list"]
                
                for condition in role['special_conditions']:
                    if not isinstance(condition, str):
                        logger.error(f"Invalid special condition in role {role_id}: not a string")
                        return False, [f"Invalid special condition in role {role_id}: not a string"]
        
        messages.append(f"Successfully validated {len(roles)} roles")
        return True, messages
    except Exception as e:
        logger.error(f"Error validating role data: {str(e)}", exc_info=True)
        messages.append(f"Error validating role data: {str(e)}")
        return False, messages
