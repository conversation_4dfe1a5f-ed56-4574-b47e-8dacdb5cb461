{"timestamp": "2025-06-24T10:38:39.139795", "endpoint": "parse-validate-mongosave/constants", "input": {"natural_language": "Constant Name: MAX_LEAVE_DAYS\nConstant Value: 30\nData Type: integer\nDescription: Maximum number of leave days allowed per year\nCategory: Leave Management\nTenant: t999", "tenant_id": "t999", "edit_draft": true, "edit_temp": false, "edit_production": false}, "output": {"success": true, "constant_results": [], "operation": "parse_validate_mongosave", "total_constants": 0}, "status": "success"}
{"timestamp": "2025-06-24T12:36:31.758972", "endpoint": "parse-validate-mongosave/constants", "input": {"natural_language": "Constant Name: MAX_TEST_ITEMS\nConstant Value: 100\nData Type: integer\nDescription: Maximum number of test items allowed\nCategory: Testing\nTenant: t999", "tenant_id": "t999", "edit_draft": true, "edit_temp": false, "edit_production": false}, "output": {"success": true, "constant_results": [], "operation": "parse_validate_mongosave", "total_constants": 0}, "status": "success"}
{"timestamp": "2025-06-24T12:45:38.609708", "endpoint": "parse-validate-mongosave/constants", "input": {"natural_language": "Attributes:\nAttribute Name | Display Name | Data Type | Required | Unique | Default Type | Default Value | Description | Helper Text\nMAX_TEST_ITEMS | Maximum Test Items | integer | true | false | static value | 100 | Maximum number of test items allowed | Used for validation limits\nTenant: t999", "tenant_id": "t999", "edit_draft": true, "edit_temp": false, "edit_production": false}, "output": {"success": true, "constant_results": [{"success": true, "saved_data": {"constant_id": 1005, "attribute": "MAX_TEST_ITEMS", "value": "100", "description": "Maximum number of test items allowed", "tenant_id": "t999", "allow_override": false, "override_permissions": "", "status": "draft", "entity_name": "", "attribute_name": "", "created_at": "2025-06-24T12:45:38.607968", "updated_at": "2025-06-24T12:45:38.607976", "created_by": "Tarun", "updated_by": "Tarun", "constant_status": "new", "changes_detected": [], "version": 1, "_id": "685a9df2a30d43107e83f576"}, "validation_result": {"structure_errors": [], "required_field_errors": [], "data_type_errors": [], "custom_errors": []}, "dependency_errors": [], "uniqueness_result": {"is_unique": true, "status": "unique", "message": "Constant with constant_id 1005 and attribute MAX_TEST_ITEMS is unique"}}], "operation": "parse_validate_mongosave", "total_constants": 1}, "status": "success"}
