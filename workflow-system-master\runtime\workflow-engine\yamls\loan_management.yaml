tenant:
  id: "t001"
  name: "loanmanagement001"
  roles:
    - id: "r001"
      name: "applicant"
      inherits_from: null
      access:
        entities:
          - entity_id: "e001"
            permissions: ["create"]
        objectives:
          - objective_id: "go001.lo001"
            permissions: ["execute"]
    - id: "r002"
      name: "manager"
      inherits_from: null
      access:
        entities:
          - entity_id: "e001"
            permissions: ["read", "update"]
        objectives:
          - objective_id: "go001.lo002"
            permissions: ["execute", "update"]
    - id: "r003"
      name: "srmanager"
      inherits_from: null
      access:
        entities:
          - entity_id: "e001"
            permissions: ["read", "update"]
        objectives:
          - objective_id: "go001.lo003"
            permissions: ["execute", "update"]

workflow_data:
  software_type: "loan management"
  industry: "finance"
  version: "1.0"
  created_by: "system"
  created_on: "{{timestamp}}"

permission_types:
  - id: "read"
    description: "can read entity data"
    capabilities: ["get"]
  - id: "create"
    description: "can create new entity records"
    capabilities: ["post"]
  - id: "update"
    description: "can update existing records"
    capabilities: ["put"]
  - id: "delete"
    description: "can delete entity records"
    capabilities: ["delete"]
  - id: "execute"
    description: "can execute workflows"
    capabilities: ["execute"]

entities:
  - id: "e001"
    name: "loanapplication"
    type: "transactional"
    version: "1.0"
    status: "active"
    attributes_metadata:
      attribute_prefix: "at"
      attribute_map:
        "at101": "loanid"
        "at102": "applicantid"
        "at103": "loantype"
        "at104": "amount"
        "at105": "interestrate"
        "at106": "status"
      required_attributes:
        - "at101"
        - "at102"
        - "at103"
        - "at104"
        - "at106"
    attributes:
      - id: "at101"
        name: "loanid"
        display_name: "Loan ID"
        datatype: "string"
        required: true
        version: "1.0"
        status: "deployed"
      - id: "at102"
        name: "applicantid"
        display_name: "Applicant ID"
        datatype: "string"
        required: true
        version: "1.0"
        status: "deployed"
      - id: "at103"
        name: "loantype"
        display_name: "Loan Type"
        datatype: "enum"
        required: true
        values: ["car", "vehicle"]
        version: "1.0"
        status: "deployed"
      - id: "at104"
        name: "amount"
        display_name: "Amount"
        datatype: "number"
        required: true
        version: "1.0"
        status: "deployed"
      - id: "at105"
        name: "interestrate"
        display_name: "Interest Rate (%)"
        datatype: "number"
        required: false
        version: "1.0"
        status: "deployed"
      - id: "at106"
        name: "status"
        display_name: "Status"
        datatype: "enum"
        required: true
        values: ["pending", "approved", "rejected"]
        version: "1.0"
        status: "deployed"

  - id: "e002"
    name: "loanrate"
    type: "master"
    version: "1.0"
    status: "active"
    attributes_metadata:
      attribute_prefix: "at"
      attribute_map:
        "at201": "loantype"
        "at202": "rate"
      required_attributes:
        - "at201"
        - "at202"
    attributes:
      - id: "at201"
        name: "loantype"
        display_name: "Loan Type"
        datatype: "enum"
        required: true
        values: ["car", "vehicle"]
        version: "1.0"
        status: "deployed"
      - id: "at202"
        name: "rate"
        display_name: "Interest Rate (%)"
        datatype: "number"
        required: true
        version: "1.0"
        status: "deployed"

global_objectives:
  - id: "go001"
    name: "loan management workflow"
    version: "1.0"
    status: "active"
    input_stack:
      description: "global inputs"
      inputs: []
    output_stack:
      description: "global outputs"
      outputs: []
    data_mapping_stack:
      description: "data handover between gos"
      mappings: []

local_objectives:
  - id: "lo001"
    contextual_id: "go001.lo001"
    name: "apply for loan"
    workflow_source: "origin"
    function_type: "create"
    agent_stack:
      agents:
        - role: "r001"
          rights: ["execute"]
    input_stack:
      description: "capture loan request"
      inputs:
        - id: "in000"
          slot_id: "information.in000"
          contextual_id: "go001.lo001.in000"
          source:
            type: "information"
            description: "current interest rates are displayed based on loan type selected"
          required: false
          data_type: "string"
          ui_control: "oj-bind-text"
          metadata:
            usage: ""
          validations: []
        - id: "in001"
          slot_id: "e001.at101.in001"
          contextual_id: "go001.lo001.in001"
          source:
            type: "system"
            description: "auto‐generated loan id"
          required: true
          data_type: "string"
          ui_control: "oj-input-text"
          metadata:
            usage: ""
          validations:
            - rule: "loan id is required"
              rule_type: "validate_required"
              validation_method: "validate_required"
              error_message: "loan id required"
          nested_function:
            id: "nf001"
            function_name: "generate_id"
            function_type: "utility"
            parameters:
              entity: "e001"
              attribute: "at101"
              prefix: "ln"
            output_to: "at101"
        - id: "in002"
          slot_id: "e001.at102.in002"
          contextual_id: "go001.lo001.in002"
          source:
            type: "system"
            description: "applicant id from session"
          required: true
          data_type: "string"
          ui_control: "oj-input-text"
          metadata:
            usage: ""
          validations:
            - rule: "applicant id required"
              rule_type: "validate_required"
              validation_method: "validate_required"
              error_message: "applicant id required"
        - id: "in003"
          slot_id: "e001.at103.in003"
          contextual_id: "go001.lo001.in003"
          source:
            type: "user"
            description: "select loan type"
          required: true
          data_type: "enum"
          ui_control: "oj-combobox-one"
          has_dropdown_source: true
          metadata:
            usage: ""
          validations:
            - rule: "loan type required"
              rule_type: "validate_required"
              validation_method: "validate_required"
              error_message: "loan type required"
          dropdown_source:
            source_type: "function"
            function_name: "fetch_enum_values"
            function_params:
              entity_id: "e001"
              attribute_id: "at103"
            value_field: "value"
            display_field: "display"
        - id: "in004"
          slot_id: "e001.at104.in004"
          contextual_id: "go001.lo001.in004"
          source:
            type: "user"
            description: "enter loan amount"
          required: true
          data_type: "number"
          ui_control: "oj-input-number"
          metadata:
            usage: ""
          validations:
            - rule: "amount required"
              rule_type: "validate_required"
              validation_method: "validate_required"
              error_message: "amount required"
        - id: "in005"
          slot_id: "e001.at105.in005"
          contextual_id: "go001.lo001.in005"
          source:
            type: "system_dependent"
            description: "interest rate auto populated from loanrate table"
          required: false
          data_type: "number"
          ui_control: "oj-input-number"
          dependencies: ["in003"]
          dependency_type: "dropdown"
          has_dropdown_source: true
          metadata:
            usage: ""
          validations: []
          dropdown_source:
            source_type: "function"
            function_name: "fetch_filtered_records"
            function_params:
              table: "workflow_runtime.loanrate"
              filter_column: "loantype"
              filter_value: "${in003}"
              value_column: "rate"
              display_column: "rate"
            value_field: "value"
            display_field: "display"
            depends_on_fields: ["in003"]
        - id: "in006"
          slot_id: "e001.at106.in006"
          contextual_id: "go001.lo001.in006"
          source:
            type: "system"
            description: "status defaults to pending"
          required: true
          data_type: "enum"
          allowed_values: ["pending", "approved", "rejected"]
          ui_control: "oj-select-single"
          metadata:
            usage: ""
          validations:
            - rule: "status must be valid"
              rule_type: "enum_check"
              validation_method: "enum_check"
              allowed_values: ["pending", "approved", "rejected"]
              error_message: "invalid status"
          nested_function:
            id: "nf002"
            function_name: "to_uppercase"
            function_type: "transform"
            parameters:
              text: "pending"
            output_to: "at106"
    output_stack:
      description: "loan application created"
      outputs:
        - id: "out001"
          slot_id: "executionstatus.out001"
          contextual_id: "go001.lo001.out001"
          source:
            type: "system"
            description: "success or failure"
          data_type: "string"
        - id: "out002"
          slot_id: "e001.at101.out002"
          contextual_id: "go001.lo001.out002"
          source:
            type: "system"
            description: "loan id"
          data_type: "string"
        - id: "out003"
          slot_id: "e001.at102.out003"
          contextual_id: "go001.lo001.out003"
          source:
            type: "system"
            description: "applicant id"
          data_type: "string"
        - id: "out004"
          slot_id: "e001.at103.out004"
          contextual_id: "go001.lo001.out004"
          source:
            type: "system"
            description: "loan type"
          data_type: "enum"
        - id: "out005"
          slot_id: "e001.at104.out005"
          contextual_id: "go001.lo001.out005"
          source:
            type: "system"
            description: "amount"
          data_type: "number"
        - id: "out006"
          slot_id: "e001.at105.out006"
          contextual_id: "go001.lo001.out006"
          source:
            type: "system"
            description: "interest rate"
          data_type: "number"
        - id: "out007"
          slot_id: "e001.at106.out007"
          contextual_id: "go001.lo001.out007"
          source:
            type: "system"
            description: "status"
          data_type: "enum"
    data_mapping_stack:
      description: "data handover between los"
      mappings:
        - id: "map001"
          source: "lo001.out002"
          target: "lo002.in007"
          mapping_type: "direct"
        - id: "map002"
          source: "lo001.out002"
          target: "lo003.in007"
          mapping_type: "direct"
        - id: "map003"
          source: "lo001.out004"
          target: "lo002.in008"
          mapping_type: "direct"
        - id: "map004"
          source: "lo001.out004"
          target: "lo003.in008"
          mapping_type: "direct"
        - id: "map005"
          source: "lo001.out005"
          target: "lo002.in009"
          mapping_type: "direct"
        - id: "map006"
          source: "lo001.out005"
          target: "lo003.in009"
          mapping_type: "direct"
        - id: "map007"
          source: "lo001.out006"
          target: "lo002.in010"
          mapping_type: "direct"
        - id: "map008"
          source: "lo001.out006"
          target: "lo003.in010"
          mapping_type: "direct"
        - id: "map009"
          source: "lo001.out007"
          target: "lo002.in011"
          mapping_type: "direct"
        - id: "map010"
          source: "lo001.out007"
          target: "lo003.in011"
          mapping_type: "direct"
    execution_pathway:
      type: "alternate"
      conditions:
        - condition:
            condition_type: "attribute_comparison"
            entity: "e001"
            attribute: "at104"
            operator: "greater_than"
            value: 1000000
          next_lo: "lo003"
        - condition:
            condition_type: "attribute_comparison"
            entity: "e001"
            attribute: "at104"
            operator: "less_than_or_equal"
            value: 1000000
          next_lo: "lo002"

  - id: "lo002"
    contextual_id: "go001.lo002"
    name: "manager approval"
    workflow_source: "terminal"
    function_type: "update"
    agent_stack:
      agents:
        - role: "r002"
          rights: ["execute", "update"]
    input_stack:
      description: "manager reviews loan"
      inputs:
        - id: "in007"
          slot_id: "e001.at101.in007"
          contextual_id: "go001.lo002.in007"
          source:
            type: "system"
            description: "loan id"
          required: true
          data_type: "string"
          ui_control: "oj-input-text"
          metadata:
            usage: "lookup"
          validations:
            - rule: "loan id required"
              rule_type: "validate_required"
              validation_method: "validate_required"
              error_message: "loan id required"
        - id: "in008"
          slot_id: "e001.at103.in008"
          contextual_id: "go001.lo002.in008"
          source:
            type: "system"
            description: "loan type"
          required: true
          data_type: "enum"
          ui_control: "oj-input-text"
          metadata:
            usage: "lookup"
          validations: []
        - id: "in009"
          slot_id: "e001.at104.in009"
          contextual_id: "go001.lo002.in009"
          source:
            type: "system"
            description: "amount"
          required: true
          data_type: "number"
          ui_control: "oj-input-number"
          metadata:
            usage: "lookup"
          validations: []
        - id: "in010"
          slot_id: "e001.at105.in010"
          contextual_id: "go001.lo002.in010"
          source:
            type: "system"
            description: "interest rate"
          required: false
          data_type: "number"
          ui_control: "oj-input-number"
          metadata:
            usage: "lookup"
          validations: []
        - id: "in011"
          slot_id: "e001.at106.in011"
          contextual_id: "go001.lo002.in011"
          source:
            type: "user"
            description: "approval decision"
          required: true
          data_type: "enum"
          allowed_values: ["approved", "rejected"]
          ui_control: "oj-select-single"
          metadata:
            usage: "update"
          validations:
            - rule: "decision required"
              rule_type: "validate_required"
              validation_method: "validate_required"
              error_message: "decision required"
    output_stack:
      description: "manager decision recorded"
      outputs:
        - id: "out001"
          slot_id: "executionstatus.out001"
          contextual_id: "go001.lo002.out001"
          source:
            type: "system"
            description: "success or failure"
          data_type: "string"
        - id: "out002"
          slot_id: "e001.at101.out002"
          contextual_id: "go001.lo002.out002"
          source:
            type: "system"
            description: "loan id"
          data_type: "string"
        - id: "out003"
          slot_id: "e001.at103.out003"
          contextual_id: "go001.lo002.out003"
          source:
            type: "system"
            description: "loan type"
          data_type: "enum"
        - id: "out004"
          slot_id: "e001.at104.out004"
          contextual_id: "go001.lo002.out004"
          source:
            type: "system"
            description: "amount"
          data_type: "number"
        - id: "out005"
          slot_id: "e001.at105.out005"
          contextual_id: "go001.lo002.out005"
          source:
            type: "system"
            description: "interest rate"
          data_type: "number"
        - id: "out006"
          slot_id: "e001.at106.out006"
          contextual_id: "go001.lo002.out006"
          source:
            type: "system"
            description: "status"
          data_type: "enum"
    data_mapping_stack:
      description: "no downstream los"
      mappings: []
    execution_pathway:
      type: "terminal"
      terminal_type: "end"

  - id: "lo003"
    contextual_id: "go001.lo003"
    name: "sr manager approval"
    workflow_source: "terminal"
    function_type: "update"
    agent_stack:
      agents:
        - role: "r003"
          rights: ["execute", "update"]
    input_stack:
      description: "sr manager reviews high value loan"
      inputs:
        - id: "in007"
          slot_id: "e001.at101.in007"
          contextual_id: "go001.lo003.in007"
          source:
            type: "system"
            description: "loan id"
          required: true
          data_type: "string"
          ui_control: "oj-input-text"
          metadata:
            usage: "lookup"
          validations:
            - rule: "loan id required"
              rule_type: "validate_required"
              validation_method: "validate_required"
              error_message: "loan id required"
        - id: "in008"
          slot_id: "e001.at103.in008"
          contextual_id: "go001.lo003.in008"
          source:
            type: "system"
            description: "loan type"
          required: true
          data_type: "enum"
          ui_control: "oj-input-text"
          metadata:
            usage: "lookup"
          validations: []
        - id: "in009"
          slot_id: "e001.at104.in009"
          contextual_id: "go001.lo003.in009"
          source:
            type: "system"
            description: "amount"
          required: true
          data_type: "number"
          ui_control: "oj-input-number"
          metadata:
            usage: "lookup"
          validations: []
        - id: "in010"
          slot_id: "e001.at105.in010"
          contextual_id: "go001.lo003.in010"
          source:
            type: "system"
            description: "interest rate"
          required: false
          data_type: "number"
          ui_control: "oj-input-number"
          metadata:
            usage: "lookup"
          validations: []
        - id: "in011"
          slot_id: "e001.at106.in011"
          contextual_id: "go001.lo003.in011"
          source:
            type: "user"
            description: "approval decision"
          required: true
          data_type: "enum"
          allowed_values: ["approved", "rejected"]
          ui_control: "oj-select-single"
          metadata:
            usage: "update"
          validations:
            - rule: "decision required"
              rule_type: "validate_required"
              validation_method: "validate_required"
              error_message: "decision required"
    output_stack:
      description: "sr manager decision recorded"
      outputs:
        - id: "out001"
          slot_id: "executionstatus.out001"
          contextual_id: "go001.lo003.out001"
          source:
            type: "system"
            description: "success or failure"
          data_type: "string"
        - id: "out002"
          slot_id: "e001.at101.out002"
          contextual_id: "go001.lo003.out002"
          source:
            type: "system"
            description: "loan id"
          data_type: "string"
        - id: "out003"
          slot_id: "e001.at103.out003"
          contextual_id: "go001.lo003.out003"
          source:
            type: "system"
            description: "loan type"
          data_type: "enum"
        - id: "out004"
          slot_id: "e001.at104.out004"
          contextual_id: "go001.lo003.out004"
          source:
            type: "system"
            description: "amount"
          data_type: "number"
        - id: "out005"
          slot_id: "e001.at105.out005"
          contextual_id: "go001.lo003.out005"
          source:
            type: "system"
            description: "interest rate"
          data_type: "number"
        - id: "out006"
          slot_id: "e001.at106.out006"
          contextual_id: "go001.lo003.out006"
          source:
            type: "system"
            description: "status"
          data_type: "enum"
    data_mapping_stack:
      description: "no downstream los"
      mappings: []
    execution_pathway:
      type: "terminal"
      terminal_type: "end"
