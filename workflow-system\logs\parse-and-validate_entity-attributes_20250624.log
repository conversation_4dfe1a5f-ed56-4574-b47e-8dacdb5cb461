{"timestamp": "2025-06-24T05:28:00.174291", "endpoint": "parse-and-validate/entity-attributes", "input": {"natural_language": "LeaveApplication01 has leaveId^PK, employeeId^FK, startDate, endDate, numDays[derived], reason, leaveTypeName, leaveSubTypeName, status, requiresDocumentation, documentationProvided, submissionDate, approvalDate, approvedBy^FK, comments, isRetroactive[derived], insufficientBalance[derived], lowTeamAvailability[derived], allowedNumberOfDays.\n\nAttributes:\nAttribute Name | Display Name | Data Type | Required | Unique | Default Type | Default Value | Description | Helper Text\n\nleaveId | Leave ID | string | true | true | computation | LV-{YYYY}-{sequence} | Unique identifier for the leave application | Auto-generated leave identifier\n\nemployeeId | Employee ID | string | true | false | static value | | References the employee requesting leave | Select employee from dropdown\n\nstartDate | Leave Start Date | date | true | false | static value | | First day of the requested leave period | Select start date for leave\n\nendDate | Leave End Date | date | true | false | static value | | Last day of the requested leave period | Select end date for leave\n\nnumDays | Number of Days | integer | true | false | computation | DATEDIFF(endDate, startDate) + 1 | Total number of leave days requested | Automatically calculated based on start and end dates", "entity_context": {"entity_id": "E7", "entity_name": "LeaveApplication01", "tenant_id": "T2"}}, "output": {"success": true, "attribute_results": [{"parsed_data": {"attribute_id": "E7.At1", "entity_id": "E7", "name": "leaveId", "display_name": "Leave ID", "datatype": "string", "is_primary_key": true, "is_foreign_key": false, "is_required": true, "is_unique": true, "default_type": "computation", "default_value": "LV-{YYYY}-{sequence}", "description": "Unique identifier for the leave application", "helper_text": "Auto-generated leave identifier", "is_calculated": true, "calculation_formula": "LV-{YYYY}-{sequence}", "version": 1, "status": "draft", "natural_language": "Display Name: Leave ID\nName: leaveId\nData Type: string\nRequired: true\nUnique: true\nCalculated: true\nDefault Type: computation\nDefault Value: LV-{YYYY}-{sequence}\nCalculation Formula: LV-{YYYY}-{sequence}\nDescription: Unique identifier for the leave application\nHelper Text: Auto-generated leave identifier", "created_at": "2025-06-24T05:27:59.633707", "updated_at": "2025-06-24T05:27:59.633707", "created_by": "Tarun", "updated_by": "Tarun", "attribute_status": "existing", "changes_detected": []}, "validation_result": {"structure_errors": [], "required_field_errors": [], "data_type_errors": [], "custom_errors": []}, "dependency_errors": [], "uniqueness_result": {"is_unique": false, "status": "exists_in_postgres", "message": "Attribute with attribute_id E7.At1 already exists in PostgreSQL"}, "is_valid": true}, {"parsed_data": {"attribute_id": "E7.At2", "entity_id": "E7", "name": "employeeId", "display_name": "Employee ID", "datatype": "string", "is_primary_key": false, "is_foreign_key": true, "is_required": true, "is_unique": false, "default_type": "static value", "default_value": "", "description": "References the employee requesting leave", "helper_text": "Select employee from dropdown", "is_calculated": false, "calculation_formula": "", "version": 1, "status": "draft", "natural_language": "Display Name: Employee ID\nName: employeeId\nData Type: string\nRequired: true\nDefault Type: static value\nDescription: References the employee requesting leave\nHelper Text: Select employee from dropdown", "created_at": "2025-06-24T05:27:59.648084", "updated_at": "2025-06-24T05:27:59.648084", "created_by": "Tarun", "updated_by": "Tarun", "attribute_status": "existing", "changes_detected": []}, "validation_result": {"structure_errors": [], "required_field_errors": [], "data_type_errors": [], "custom_errors": []}, "dependency_errors": [], "uniqueness_result": {"is_unique": false, "status": "exists_in_postgres", "message": "Attribute with attribute_id E7.At2 already exists in PostgreSQL"}, "is_valid": true}, {"parsed_data": {"attribute_id": "E7.At3", "entity_id": "E7", "name": "startDate", "display_name": "Leave Start Date", "datatype": "string", "is_primary_key": false, "is_foreign_key": false, "is_required": true, "is_unique": false, "default_type": "static value", "default_value": "", "description": "First day of the requested leave period", "helper_text": "Select start date for leave", "is_calculated": false, "calculation_formula": "", "version": 1, "status": "draft", "natural_language": "Display Name: Leave Start Date\nName: startDate\nData Type: date\nRequired: true\nDefault Type: static value\nDescription: First day of the requested leave period\nHelper Text: Select start date for leave", "created_at": "2025-06-24T05:27:59.662994", "updated_at": "2025-06-24T05:27:59.662994", "created_by": "Tarun", "updated_by": "Tarun", "attribute_status": "existing", "changes_detected": []}, "validation_result": {"structure_errors": [], "required_field_errors": [], "data_type_errors": [], "custom_errors": []}, "dependency_errors": [], "uniqueness_result": {"is_unique": false, "status": "exists_in_postgres", "message": "Attribute with attribute_id E7.At3 already exists in PostgreSQL"}, "is_valid": true}, {"parsed_data": {"attribute_id": "E7.At4", "entity_id": "E7", "name": "endDate", "display_name": "Leave End Date", "datatype": "string", "is_primary_key": false, "is_foreign_key": false, "is_required": true, "is_unique": false, "default_type": "static value", "default_value": "", "description": "Last day of the requested leave period", "helper_text": "Select end date for leave", "is_calculated": false, "calculation_formula": "", "version": 1, "status": "draft", "natural_language": "Display Name: Leave End Date\nName: endDate\nData Type: date\nRequired: true\nDefault Type: static value\nDescription: Last day of the requested leave period\nHelper Text: Select end date for leave", "created_at": "2025-06-24T05:27:59.677627", "updated_at": "2025-06-24T05:27:59.677627", "created_by": "Tarun", "updated_by": "Tarun", "attribute_status": "existing", "changes_detected": []}, "validation_result": {"structure_errors": [], "required_field_errors": [], "data_type_errors": [], "custom_errors": []}, "dependency_errors": [], "uniqueness_result": {"is_unique": false, "status": "exists_in_postgres", "message": "Attribute with attribute_id E7.At4 already exists in PostgreSQL"}, "is_valid": true}, {"parsed_data": {"attribute_id": "E7.At5", "entity_id": "E7", "name": "numDays", "display_name": "Number of Days", "datatype": "string", "is_primary_key": false, "is_foreign_key": false, "is_required": true, "is_unique": false, "default_type": "computation", "default_value": "DATEDIFF(endDate, startDate) + 1", "description": "Total number of leave days requested", "helper_text": "Automatically calculated based on start and end dates", "is_calculated": true, "calculation_formula": "DATEDIFF(endDate, startDate) + 1", "version": 1, "status": "draft", "natural_language": "Display Name: Number of Days\nName: numDays\nData Type: integer\nRequired: true\nCalculated: true\nDefault Type: computation\nDefault Value: DATEDIFF(endDate, startDate) + 1\nCalculation Formula: DATEDIFF(endDate, startDate) + 1\nDescription: Total number of leave days requested\nHelper Text: Automatically calculated based on start and end dates", "created_at": "2025-06-24T05:27:59.691213", "updated_at": "2025-06-24T05:27:59.691213", "created_by": "Tarun", "updated_by": "Tarun", "attribute_status": "existing", "changes_detected": []}, "validation_result": {"structure_errors": [], "required_field_errors": [], "data_type_errors": [], "custom_errors": []}, "dependency_errors": [], "uniqueness_result": {"is_unique": false, "status": "exists_in_postgres", "message": "Attribute with attribute_id E7.At5 already exists in PostgreSQL"}, "is_valid": true}, {"parsed_data": {"attribute_id": "E7.At6", "entity_id": "E7", "name": "reason", "display_name": "reason", "datatype": "string", "is_primary_key": false, "is_foreign_key": false, "is_required": false, "is_unique": false, "default_type": "", "default_value": "", "description": "", "helper_text": "", "is_calculated": false, "calculation_formula": "", "version": 1, "status": "draft", "natural_language": "", "created_at": "2025-06-24T05:27:59.705071", "updated_at": "2025-06-24T05:27:59.705071", "created_by": "Tarun", "updated_by": "Tarun", "attribute_status": "existing", "changes_detected": []}, "validation_result": {"structure_errors": [], "required_field_errors": [], "data_type_errors": [], "custom_errors": []}, "dependency_errors": [], "uniqueness_result": {"is_unique": false, "status": "exists_in_postgres", "message": "Attribute with attribute_id E7.At6 already exists in PostgreSQL"}, "is_valid": true}, {"parsed_data": {"attribute_id": "E7.At7", "entity_id": "E7", "name": "leaveTypeName", "display_name": "leaveTypeName", "datatype": "string", "is_primary_key": false, "is_foreign_key": false, "is_required": false, "is_unique": false, "default_type": "", "default_value": "", "description": "", "helper_text": "", "is_calculated": false, "calculation_formula": "", "version": 1, "status": "draft", "natural_language": "", "created_at": "2025-06-24T05:27:59.718189", "updated_at": "2025-06-24T05:27:59.718189", "created_by": "Tarun", "updated_by": "Tarun", "attribute_status": "existing", "changes_detected": []}, "validation_result": {"structure_errors": [], "required_field_errors": [], "data_type_errors": [], "custom_errors": []}, "dependency_errors": [], "uniqueness_result": {"is_unique": false, "status": "exists_in_postgres", "message": "Attribute with attribute_id E7.At7 already exists in PostgreSQL"}, "is_valid": true}, {"parsed_data": {"attribute_id": "E7.At8", "entity_id": "E7", "name": "leaveSubTypeName", "display_name": "leaveSubTypeName", "datatype": "string", "is_primary_key": false, "is_foreign_key": false, "is_required": false, "is_unique": false, "default_type": "", "default_value": "", "description": "", "helper_text": "", "is_calculated": false, "calculation_formula": "", "version": 1, "status": "draft", "natural_language": "", "created_at": "2025-06-24T05:27:59.733195", "updated_at": "2025-06-24T05:27:59.733195", "created_by": "Tarun", "updated_by": "Tarun", "attribute_status": "existing", "changes_detected": []}, "validation_result": {"structure_errors": [], "required_field_errors": [], "data_type_errors": [], "custom_errors": []}, "dependency_errors": [], "uniqueness_result": {"is_unique": false, "status": "exists_in_postgres", "message": "Attribute with attribute_id E7.At8 already exists in PostgreSQL"}, "is_valid": true}, {"parsed_data": {"attribute_id": "E7.At9", "entity_id": "E7", "name": "status", "display_name": "status", "datatype": "string", "is_primary_key": false, "is_foreign_key": false, "is_required": false, "is_unique": false, "default_type": "", "default_value": "", "description": "", "helper_text": "", "is_calculated": false, "calculation_formula": "", "version": 1, "status": "draft", "natural_language": "", "created_at": "2025-06-24T05:27:59.747258", "updated_at": "2025-06-24T05:27:59.747258", "created_by": "Tarun", "updated_by": "Tarun", "attribute_status": "existing", "changes_detected": []}, "validation_result": {"structure_errors": [], "required_field_errors": [], "data_type_errors": [], "custom_errors": []}, "dependency_errors": [], "uniqueness_result": {"is_unique": false, "status": "exists_in_postgres", "message": "Attribute with attribute_id E7.At9 already exists in PostgreSQL"}, "is_valid": true}, {"parsed_data": {"attribute_id": "E7.At10", "entity_id": "E7", "name": "requiresDocumentation", "display_name": "requiresDocumentation", "datatype": "string", "is_primary_key": false, "is_foreign_key": false, "is_required": false, "is_unique": false, "default_type": "", "default_value": "", "description": "", "helper_text": "", "is_calculated": false, "calculation_formula": "", "version": 1, "status": "draft", "natural_language": "", "created_at": "2025-06-24T05:27:59.761128", "updated_at": "2025-06-24T05:27:59.761128", "created_by": "Tarun", "updated_by": "Tarun", "attribute_status": "existing", "changes_detected": []}, "validation_result": {"structure_errors": [], "required_field_errors": [], "data_type_errors": [], "custom_errors": []}, "dependency_errors": [], "uniqueness_result": {"is_unique": false, "status": "exists_in_postgres", "message": "Attribute with attribute_id E7.At10 already exists in PostgreSQL"}, "is_valid": true}, {"parsed_data": {"attribute_id": "E7.At11", "entity_id": "E7", "name": "documentationProvided", "display_name": "documentationProvided", "datatype": "string", "is_primary_key": false, "is_foreign_key": false, "is_required": false, "is_unique": false, "default_type": "", "default_value": "", "description": "", "helper_text": "", "is_calculated": false, "calculation_formula": "", "version": 1, "status": "draft", "natural_language": "", "created_at": "2025-06-24T05:27:59.775458", "updated_at": "2025-06-24T05:27:59.775458", "created_by": "Tarun", "updated_by": "Tarun", "attribute_status": "existing", "changes_detected": []}, "validation_result": {"structure_errors": [], "required_field_errors": [], "data_type_errors": [], "custom_errors": []}, "dependency_errors": [], "uniqueness_result": {"is_unique": false, "status": "exists_in_postgres", "message": "Attribute with attribute_id E7.At11 already exists in PostgreSQL"}, "is_valid": true}, {"parsed_data": {"attribute_id": "E7.At12", "entity_id": "E7", "name": "submissionDate", "display_name": "submissionDate", "datatype": "string", "is_primary_key": false, "is_foreign_key": false, "is_required": false, "is_unique": false, "default_type": "", "default_value": "", "description": "", "helper_text": "", "is_calculated": false, "calculation_formula": "", "version": 1, "status": "draft", "natural_language": "", "created_at": "2025-06-24T05:27:59.788755", "updated_at": "2025-06-24T05:27:59.788755", "created_by": "Tarun", "updated_by": "Tarun", "attribute_status": "existing", "changes_detected": []}, "validation_result": {"structure_errors": [], "required_field_errors": [], "data_type_errors": [], "custom_errors": []}, "dependency_errors": [], "uniqueness_result": {"is_unique": false, "status": "exists_in_postgres", "message": "Attribute with attribute_id E7.At12 already exists in PostgreSQL"}, "is_valid": true}, {"parsed_data": {"attribute_id": "E7.At13", "entity_id": "E7", "name": "approvalDate", "display_name": "approvalDate", "datatype": "string", "is_primary_key": false, "is_foreign_key": false, "is_required": false, "is_unique": false, "default_type": "", "default_value": "", "description": "", "helper_text": "", "is_calculated": false, "calculation_formula": "", "version": 1, "status": "draft", "natural_language": "", "created_at": "2025-06-24T05:27:59.802269", "updated_at": "2025-06-24T05:27:59.802269", "created_by": "Tarun", "updated_by": "Tarun", "attribute_status": "existing", "changes_detected": []}, "validation_result": {"structure_errors": [], "required_field_errors": [], "data_type_errors": [], "custom_errors": []}, "dependency_errors": [], "uniqueness_result": {"is_unique": false, "status": "exists_in_postgres", "message": "Attribute with attribute_id E7.At13 already exists in PostgreSQL"}, "is_valid": true}, {"parsed_data": {"attribute_id": "E7.At14", "entity_id": "E7", "name": "approvedBy", "display_name": "approvedBy", "datatype": "string", "is_primary_key": false, "is_foreign_key": true, "is_required": false, "is_unique": false, "default_type": "", "default_value": "", "description": "", "helper_text": "", "is_calculated": false, "calculation_formula": "", "version": 1, "status": "draft", "natural_language": "", "created_at": "2025-06-24T05:27:59.817320", "updated_at": "2025-06-24T05:27:59.817320", "created_by": "Tarun", "updated_by": "Tarun", "attribute_status": "existing", "changes_detected": []}, "validation_result": {"structure_errors": [], "required_field_errors": [], "data_type_errors": [], "custom_errors": []}, "dependency_errors": [], "uniqueness_result": {"is_unique": false, "status": "exists_in_postgres", "message": "Attribute with attribute_id E7.At14 already exists in PostgreSQL"}, "is_valid": true}, {"parsed_data": {"attribute_id": "E7.At15", "entity_id": "E7", "name": "comments", "display_name": "comments", "datatype": "string", "is_primary_key": false, "is_foreign_key": false, "is_required": false, "is_unique": false, "default_type": "", "default_value": "", "description": "", "helper_text": "", "is_calculated": false, "calculation_formula": "", "version": 1, "status": "draft", "natural_language": "", "created_at": "2025-06-24T05:27:59.831624", "updated_at": "2025-06-24T05:27:59.831624", "created_by": "Tarun", "updated_by": "Tarun", "attribute_status": "existing", "changes_detected": []}, "validation_result": {"structure_errors": [], "required_field_errors": [], "data_type_errors": [], "custom_errors": []}, "dependency_errors": [], "uniqueness_result": {"is_unique": false, "status": "exists_in_postgres", "message": "Attribute with attribute_id E7.At15 already exists in PostgreSQL"}, "is_valid": true}, {"parsed_data": {"attribute_id": "E7.At16", "entity_id": "E7", "name": "isRetroactive", "display_name": "isRetroactive", "datatype": "string", "is_primary_key": false, "is_foreign_key": false, "is_required": false, "is_unique": false, "default_type": "", "default_value": "", "description": "", "helper_text": "", "is_calculated": true, "calculation_formula": "", "version": 1, "status": "draft", "natural_language": "", "created_at": "2025-06-24T05:27:59.846502", "updated_at": "2025-06-24T05:27:59.846502", "created_by": "Tarun", "updated_by": "Tarun", "attribute_status": "existing", "changes_detected": []}, "validation_result": {"structure_errors": [], "required_field_errors": [], "data_type_errors": [], "custom_errors": []}, "dependency_errors": [], "uniqueness_result": {"is_unique": false, "status": "exists_in_postgres", "message": "Attribute with attribute_id E7.At16 already exists in PostgreSQL"}, "is_valid": true}, {"parsed_data": {"attribute_id": "E7.At17", "entity_id": "E7", "name": "insufficientBalance", "display_name": "insufficientBalance", "datatype": "string", "is_primary_key": false, "is_foreign_key": false, "is_required": false, "is_unique": false, "default_type": "", "default_value": "", "description": "", "helper_text": "", "is_calculated": true, "calculation_formula": "", "version": 1, "status": "draft", "natural_language": "", "created_at": "2025-06-24T05:27:59.858581", "updated_at": "2025-06-24T05:27:59.858581", "created_by": "Tarun", "updated_by": "Tarun", "attribute_status": "existing", "changes_detected": []}, "validation_result": {"structure_errors": [], "required_field_errors": [], "data_type_errors": [], "custom_errors": []}, "dependency_errors": [], "uniqueness_result": {"is_unique": false, "status": "exists_in_postgres", "message": "Attribute with attribute_id E7.At17 already exists in PostgreSQL"}, "is_valid": true}, {"parsed_data": {"attribute_id": "E7.At18", "entity_id": "E7", "name": "lowTeamAvailability", "display_name": "lowTeamAvailability", "datatype": "string", "is_primary_key": false, "is_foreign_key": false, "is_required": false, "is_unique": false, "default_type": "", "default_value": "", "description": "", "helper_text": "", "is_calculated": true, "calculation_formula": "", "version": 1, "status": "draft", "natural_language": "", "created_at": "2025-06-24T05:27:59.868674", "updated_at": "2025-06-24T05:27:59.868674", "created_by": "Tarun", "updated_by": "Tarun", "attribute_status": "existing", "changes_detected": []}, "validation_result": {"structure_errors": [], "required_field_errors": [], "data_type_errors": [], "custom_errors": []}, "dependency_errors": [], "uniqueness_result": {"is_unique": false, "status": "exists_in_postgres", "message": "Attribute with attribute_id E7.At18 already exists in PostgreSQL"}, "is_valid": true}, {"parsed_data": {"attribute_id": "E7.At19", "entity_id": "E7", "name": "allowedNumberOfDays", "display_name": "allowedNumberOfDays", "datatype": "string", "is_primary_key": false, "is_foreign_key": false, "is_required": false, "is_unique": false, "default_type": "", "default_value": "", "description": "", "helper_text": "", "is_calculated": false, "calculation_formula": "", "version": 1, "status": "draft", "natural_language": "", "created_at": "2025-06-24T05:27:59.880649", "updated_at": "2025-06-24T05:27:59.880649", "created_by": "Tarun", "updated_by": "Tarun", "attribute_status": "existing", "changes_detected": []}, "validation_result": {"structure_errors": [], "required_field_errors": [], "data_type_errors": [], "custom_errors": []}, "dependency_errors": [], "uniqueness_result": {"is_unique": false, "status": "exists_in_postgres", "message": "Attribute with attribute_id E7.At19 already exists in PostgreSQL"}, "is_valid": true}], "operation": "parse_and_validate", "total_attributes": 19}, "status": "success"}
{"timestamp": "2025-06-24T05:29:40.044188", "endpoint": "parse-and-validate/entity-attributes", "input": {"natural_language": "LeaveApplication01 has leaveId^PK, employeeId^FK, startDate, endDate, numDays[derived], reason, leaveTypeName, leaveSubTypeName, status, requiresDocumentation, documentationProvided, submissionDate, approvalDate, approvedBy^FK, comments, isRetroactive[derived], insufficientBalance[derived], lowTeamAvailability[derived], allowedNumberOfDays.\n\nAttributes:\nAttribute Name | Display Name | Data Type | Required | Unique | Default Type | Default Value | Description | Helper Text\n\nleaveId | Leave ID | string | true | true | computation | LV-{YYYY}-{sequence} | Unique identifier for the leave application | Auto-generated leave identifier\n\nemployeeId | Employee ID | string | true | false | static value | | References the employee requesting leave | Select employee from dropdown\n\nstartDate | Leave Start Date | date | true | false | static value | | First day of the requested leave period | Select start date for leave\n\nendDate | Leave End Date | date | true | false | static value | | Last day of the requested leave period | Select end date for leave\n\nnumDays | Number of Days | integer | true | false | computation | DATEDIFF(endDate, startDate) + 1 | Total number of leave days requested | Automatically calculated based on start and end dates", "entity_context": {"entity_id": "E8", "entity_name": "LeaveApplication01", "tenant_id": "T2"}}, "output": {"success": true, "attribute_results": [{"parsed_data": {"attribute_id": "E8.At15", "entity_id": "E8", "name": "leaveId", "display_name": "Leave ID", "datatype": "string", "is_primary_key": true, "is_foreign_key": false, "is_required": true, "is_unique": true, "default_type": "computation", "default_value": "LV-{YYYY}-{sequence}", "description": "Unique identifier for the leave application", "helper_text": "Auto-generated leave identifier", "is_calculated": true, "calculation_formula": "LV-{YYYY}-{sequence}", "version": 1, "status": "draft", "natural_language": "Display Name: Leave ID\nName: leaveId\nData Type: string\nRequired: true\nUnique: true\nCalculated: true\nDefault Type: computation\nDefault Value: LV-{YYYY}-{sequence}\nCalculation Formula: LV-{YYYY}-{sequence}\nDescription: Unique identifier for the leave application\nHelper Text: Auto-generated leave identifier", "created_at": "2025-06-24T05:29:39.596009", "updated_at": "2025-06-24T05:29:39.596009", "created_by": "Tarun", "updated_by": "Tarun", "attribute_status": "new", "changes_detected": []}, "validation_result": {"structure_errors": [], "required_field_errors": [], "data_type_errors": [], "custom_errors": []}, "dependency_errors": [], "uniqueness_result": {"is_unique": true, "status": "unique", "message": "Attribute with attribute_id E8.At15 is unique"}, "is_valid": true}, {"parsed_data": {"attribute_id": "E8.At16", "entity_id": "E8", "name": "employeeId", "display_name": "Employee ID", "datatype": "string", "is_primary_key": false, "is_foreign_key": true, "is_required": true, "is_unique": false, "default_type": "static value", "default_value": "", "description": "References the employee requesting leave", "helper_text": "Select employee from dropdown", "is_calculated": false, "calculation_formula": "", "version": 1, "status": "draft", "natural_language": "Display Name: Employee ID\nName: employeeId\nData Type: string\nRequired: true\nDefault Type: static value\nDescription: References the employee requesting leave\nHelper Text: Select employee from dropdown", "created_at": "2025-06-24T05:29:39.612101", "updated_at": "2025-06-24T05:29:39.612101", "created_by": "Tarun", "updated_by": "Tarun", "attribute_status": "new", "changes_detected": []}, "validation_result": {"structure_errors": [], "required_field_errors": [], "data_type_errors": [], "custom_errors": []}, "dependency_errors": [], "uniqueness_result": {"is_unique": true, "status": "unique", "message": "Attribute with attribute_id E8.At16 is unique"}, "is_valid": true}, {"parsed_data": {"attribute_id": "E8.At17", "entity_id": "E8", "name": "startDate", "display_name": "Leave Start Date", "datatype": "string", "is_primary_key": false, "is_foreign_key": false, "is_required": true, "is_unique": false, "default_type": "static value", "default_value": "", "description": "First day of the requested leave period", "helper_text": "Select start date for leave", "is_calculated": false, "calculation_formula": "", "version": 1, "status": "draft", "natural_language": "Display Name: Leave Start Date\nName: startDate\nData Type: date\nRequired: true\nDefault Type: static value\nDescription: First day of the requested leave period\nHelper Text: Select start date for leave", "created_at": "2025-06-24T05:29:39.622144", "updated_at": "2025-06-24T05:29:39.622144", "created_by": "Tarun", "updated_by": "Tarun", "attribute_status": "new", "changes_detected": []}, "validation_result": {"structure_errors": [], "required_field_errors": [], "data_type_errors": [], "custom_errors": []}, "dependency_errors": [], "uniqueness_result": {"is_unique": true, "status": "unique", "message": "Attribute with attribute_id E8.At17 is unique"}, "is_valid": true}, {"parsed_data": {"attribute_id": "E8.At18", "entity_id": "E8", "name": "endDate", "display_name": "Leave End Date", "datatype": "string", "is_primary_key": false, "is_foreign_key": false, "is_required": true, "is_unique": false, "default_type": "static value", "default_value": "", "description": "Last day of the requested leave period", "helper_text": "Select end date for leave", "is_calculated": false, "calculation_formula": "", "version": 1, "status": "draft", "natural_language": "Display Name: Leave End Date\nName: endDate\nData Type: date\nRequired: true\nDefault Type: static value\nDescription: Last day of the requested leave period\nHelper Text: Select end date for leave", "created_at": "2025-06-24T05:29:39.631331", "updated_at": "2025-06-24T05:29:39.631331", "created_by": "Tarun", "updated_by": "Tarun", "attribute_status": "new", "changes_detected": []}, "validation_result": {"structure_errors": [], "required_field_errors": [], "data_type_errors": [], "custom_errors": []}, "dependency_errors": [], "uniqueness_result": {"is_unique": true, "status": "unique", "message": "Attribute with attribute_id E8.At18 is unique"}, "is_valid": true}, {"parsed_data": {"attribute_id": "E8.At19", "entity_id": "E8", "name": "numDays", "display_name": "Number of Days", "datatype": "string", "is_primary_key": false, "is_foreign_key": false, "is_required": true, "is_unique": false, "default_type": "computation", "default_value": "DATEDIFF(endDate, startDate) + 1", "description": "Total number of leave days requested", "helper_text": "Automatically calculated based on start and end dates", "is_calculated": true, "calculation_formula": "DATEDIFF(endDate, startDate) + 1", "version": 1, "status": "draft", "natural_language": "Display Name: Number of Days\nName: numDays\nData Type: integer\nRequired: true\nCalculated: true\nDefault Type: computation\nDefault Value: DATEDIFF(endDate, startDate) + 1\nCalculation Formula: DATEDIFF(endDate, startDate) + 1\nDescription: Total number of leave days requested\nHelper Text: Automatically calculated based on start and end dates", "created_at": "2025-06-24T05:29:39.640727", "updated_at": "2025-06-24T05:29:39.640727", "created_by": "Tarun", "updated_by": "Tarun", "attribute_status": "new", "changes_detected": []}, "validation_result": {"structure_errors": [], "required_field_errors": [], "data_type_errors": [], "custom_errors": []}, "dependency_errors": [], "uniqueness_result": {"is_unique": true, "status": "unique", "message": "Attribute with attribute_id E8.At19 is unique"}, "is_valid": true}, {"parsed_data": {"attribute_id": "E8.At20", "entity_id": "E8", "name": "reason", "display_name": "reason", "datatype": "string", "is_primary_key": false, "is_foreign_key": false, "is_required": false, "is_unique": false, "default_type": "", "default_value": "", "description": "", "helper_text": "", "is_calculated": false, "calculation_formula": "", "version": 1, "status": "draft", "natural_language": "", "created_at": "2025-06-24T05:29:39.650212", "updated_at": "2025-06-24T05:29:39.650212", "created_by": "Tarun", "updated_by": "Tarun", "attribute_status": "new", "changes_detected": []}, "validation_result": {"structure_errors": [], "required_field_errors": [], "data_type_errors": [], "custom_errors": []}, "dependency_errors": [], "uniqueness_result": {"is_unique": true, "status": "unique", "message": "Attribute with attribute_id E8.At20 is unique"}, "is_valid": true}, {"parsed_data": {"attribute_id": "E8.At21", "entity_id": "E8", "name": "leaveTypeName", "display_name": "leaveTypeName", "datatype": "string", "is_primary_key": false, "is_foreign_key": false, "is_required": false, "is_unique": false, "default_type": "", "default_value": "", "description": "", "helper_text": "", "is_calculated": false, "calculation_formula": "", "version": 1, "status": "draft", "natural_language": "", "created_at": "2025-06-24T05:29:39.658622", "updated_at": "2025-06-24T05:29:39.658622", "created_by": "Tarun", "updated_by": "Tarun", "attribute_status": "new", "changes_detected": []}, "validation_result": {"structure_errors": [], "required_field_errors": [], "data_type_errors": [], "custom_errors": []}, "dependency_errors": [], "uniqueness_result": {"is_unique": true, "status": "unique", "message": "Attribute with attribute_id E8.At21 is unique"}, "is_valid": true}, {"parsed_data": {"attribute_id": "E8.At22", "entity_id": "E8", "name": "leaveSubTypeName", "display_name": "leaveSubTypeName", "datatype": "string", "is_primary_key": false, "is_foreign_key": false, "is_required": false, "is_unique": false, "default_type": "", "default_value": "", "description": "", "helper_text": "", "is_calculated": false, "calculation_formula": "", "version": 1, "status": "draft", "natural_language": "", "created_at": "2025-06-24T05:29:39.667226", "updated_at": "2025-06-24T05:29:39.667226", "created_by": "Tarun", "updated_by": "Tarun", "attribute_status": "new", "changes_detected": []}, "validation_result": {"structure_errors": [], "required_field_errors": [], "data_type_errors": [], "custom_errors": []}, "dependency_errors": [], "uniqueness_result": {"is_unique": true, "status": "unique", "message": "Attribute with attribute_id E8.At22 is unique"}, "is_valid": true}, {"parsed_data": {"attribute_id": "E8.At23", "entity_id": "E8", "name": "status", "display_name": "status", "datatype": "string", "is_primary_key": false, "is_foreign_key": false, "is_required": false, "is_unique": false, "default_type": "", "default_value": "", "description": "", "helper_text": "", "is_calculated": false, "calculation_formula": "", "version": 1, "status": "draft", "natural_language": "", "created_at": "2025-06-24T05:29:39.675731", "updated_at": "2025-06-24T05:29:39.675731", "created_by": "Tarun", "updated_by": "Tarun", "attribute_status": "new", "changes_detected": []}, "validation_result": {"structure_errors": [], "required_field_errors": [], "data_type_errors": [], "custom_errors": []}, "dependency_errors": [], "uniqueness_result": {"is_unique": true, "status": "unique", "message": "Attribute with attribute_id E8.At23 is unique"}, "is_valid": true}, {"parsed_data": {"attribute_id": "E8.At24", "entity_id": "E8", "name": "requiresDocumentation", "display_name": "requiresDocumentation", "datatype": "string", "is_primary_key": false, "is_foreign_key": false, "is_required": false, "is_unique": false, "default_type": "", "default_value": "", "description": "", "helper_text": "", "is_calculated": false, "calculation_formula": "", "version": 1, "status": "draft", "natural_language": "", "created_at": "2025-06-24T05:29:39.684269", "updated_at": "2025-06-24T05:29:39.684269", "created_by": "Tarun", "updated_by": "Tarun", "attribute_status": "new", "changes_detected": []}, "validation_result": {"structure_errors": [], "required_field_errors": [], "data_type_errors": [], "custom_errors": []}, "dependency_errors": [], "uniqueness_result": {"is_unique": true, "status": "unique", "message": "Attribute with attribute_id E8.At24 is unique"}, "is_valid": true}, {"parsed_data": {"attribute_id": "E8.At25", "entity_id": "E8", "name": "documentationProvided", "display_name": "documentationProvided", "datatype": "string", "is_primary_key": false, "is_foreign_key": false, "is_required": false, "is_unique": false, "default_type": "", "default_value": "", "description": "", "helper_text": "", "is_calculated": false, "calculation_formula": "", "version": 1, "status": "draft", "natural_language": "", "created_at": "2025-06-24T05:29:39.692183", "updated_at": "2025-06-24T05:29:39.692183", "created_by": "Tarun", "updated_by": "Tarun", "attribute_status": "new", "changes_detected": []}, "validation_result": {"structure_errors": [], "required_field_errors": [], "data_type_errors": [], "custom_errors": []}, "dependency_errors": [], "uniqueness_result": {"is_unique": true, "status": "unique", "message": "Attribute with attribute_id E8.At25 is unique"}, "is_valid": true}, {"parsed_data": {"attribute_id": "E8.At26", "entity_id": "E8", "name": "submissionDate", "display_name": "submissionDate", "datatype": "string", "is_primary_key": false, "is_foreign_key": false, "is_required": false, "is_unique": false, "default_type": "", "default_value": "", "description": "", "helper_text": "", "is_calculated": false, "calculation_formula": "", "version": 1, "status": "draft", "natural_language": "", "created_at": "2025-06-24T05:29:39.699963", "updated_at": "2025-06-24T05:29:39.699963", "created_by": "Tarun", "updated_by": "Tarun", "attribute_status": "new", "changes_detected": []}, "validation_result": {"structure_errors": [], "required_field_errors": [], "data_type_errors": [], "custom_errors": []}, "dependency_errors": [], "uniqueness_result": {"is_unique": true, "status": "unique", "message": "Attribute with attribute_id E8.At26 is unique"}, "is_valid": true}, {"parsed_data": {"attribute_id": "E8.At27", "entity_id": "E8", "name": "approvalDate", "display_name": "approvalDate", "datatype": "string", "is_primary_key": false, "is_foreign_key": false, "is_required": false, "is_unique": false, "default_type": "", "default_value": "", "description": "", "helper_text": "", "is_calculated": false, "calculation_formula": "", "version": 1, "status": "draft", "natural_language": "", "created_at": "2025-06-24T05:29:39.709284", "updated_at": "2025-06-24T05:29:39.709284", "created_by": "Tarun", "updated_by": "Tarun", "attribute_status": "new", "changes_detected": []}, "validation_result": {"structure_errors": [], "required_field_errors": [], "data_type_errors": [], "custom_errors": []}, "dependency_errors": [], "uniqueness_result": {"is_unique": true, "status": "unique", "message": "Attribute with attribute_id E8.At27 is unique"}, "is_valid": true}, {"parsed_data": {"attribute_id": "E8.At28", "entity_id": "E8", "name": "approvedBy", "display_name": "approvedBy", "datatype": "string", "is_primary_key": false, "is_foreign_key": true, "is_required": false, "is_unique": false, "default_type": "", "default_value": "", "description": "", "helper_text": "", "is_calculated": false, "calculation_formula": "", "version": 1, "status": "draft", "natural_language": "", "created_at": "2025-06-24T05:29:39.718003", "updated_at": "2025-06-24T05:29:39.718003", "created_by": "Tarun", "updated_by": "Tarun", "attribute_status": "new", "changes_detected": []}, "validation_result": {"structure_errors": [], "required_field_errors": [], "data_type_errors": [], "custom_errors": []}, "dependency_errors": [], "uniqueness_result": {"is_unique": true, "status": "unique", "message": "Attribute with attribute_id E8.At28 is unique"}, "is_valid": true}, {"parsed_data": {"attribute_id": "E8.At29", "entity_id": "E8", "name": "comments", "display_name": "comments", "datatype": "string", "is_primary_key": false, "is_foreign_key": false, "is_required": false, "is_unique": false, "default_type": "", "default_value": "", "description": "", "helper_text": "", "is_calculated": false, "calculation_formula": "", "version": 1, "status": "draft", "natural_language": "", "created_at": "2025-06-24T05:29:39.727748", "updated_at": "2025-06-24T05:29:39.727748", "created_by": "Tarun", "updated_by": "Tarun", "attribute_status": "new", "changes_detected": []}, "validation_result": {"structure_errors": [], "required_field_errors": [], "data_type_errors": [], "custom_errors": []}, "dependency_errors": [], "uniqueness_result": {"is_unique": true, "status": "unique", "message": "Attribute with attribute_id E8.At29 is unique"}, "is_valid": true}, {"parsed_data": {"attribute_id": "E8.At30", "entity_id": "E8", "name": "isRetroactive", "display_name": "isRetroactive", "datatype": "string", "is_primary_key": false, "is_foreign_key": false, "is_required": false, "is_unique": false, "default_type": "", "default_value": "", "description": "", "helper_text": "", "is_calculated": true, "calculation_formula": "", "version": 1, "status": "draft", "natural_language": "", "created_at": "2025-06-24T05:29:39.736683", "updated_at": "2025-06-24T05:29:39.736683", "created_by": "Tarun", "updated_by": "Tarun", "attribute_status": "new", "changes_detected": []}, "validation_result": {"structure_errors": [], "required_field_errors": [], "data_type_errors": [], "custom_errors": []}, "dependency_errors": [], "uniqueness_result": {"is_unique": true, "status": "unique", "message": "Attribute with attribute_id E8.At30 is unique"}, "is_valid": true}, {"parsed_data": {"attribute_id": "E8.At31", "entity_id": "E8", "name": "insufficientBalance", "display_name": "insufficientBalance", "datatype": "string", "is_primary_key": false, "is_foreign_key": false, "is_required": false, "is_unique": false, "default_type": "", "default_value": "", "description": "", "helper_text": "", "is_calculated": true, "calculation_formula": "", "version": 1, "status": "draft", "natural_language": "", "created_at": "2025-06-24T05:29:39.744777", "updated_at": "2025-06-24T05:29:39.744777", "created_by": "Tarun", "updated_by": "Tarun", "attribute_status": "new", "changes_detected": []}, "validation_result": {"structure_errors": [], "required_field_errors": [], "data_type_errors": [], "custom_errors": []}, "dependency_errors": [], "uniqueness_result": {"is_unique": true, "status": "unique", "message": "Attribute with attribute_id E8.At31 is unique"}, "is_valid": true}, {"parsed_data": {"attribute_id": "E8.At32", "entity_id": "E8", "name": "lowTeamAvailability", "display_name": "lowTeamAvailability", "datatype": "string", "is_primary_key": false, "is_foreign_key": false, "is_required": false, "is_unique": false, "default_type": "", "default_value": "", "description": "", "helper_text": "", "is_calculated": true, "calculation_formula": "", "version": 1, "status": "draft", "natural_language": "", "created_at": "2025-06-24T05:29:39.752813", "updated_at": "2025-06-24T05:29:39.752813", "created_by": "Tarun", "updated_by": "Tarun", "attribute_status": "new", "changes_detected": []}, "validation_result": {"structure_errors": [], "required_field_errors": [], "data_type_errors": [], "custom_errors": []}, "dependency_errors": [], "uniqueness_result": {"is_unique": true, "status": "unique", "message": "Attribute with attribute_id E8.At32 is unique"}, "is_valid": true}, {"parsed_data": {"attribute_id": "E8.At33", "entity_id": "E8", "name": "allowedNumberOfDays", "display_name": "allowedNumberOfDays", "datatype": "string", "is_primary_key": false, "is_foreign_key": false, "is_required": false, "is_unique": false, "default_type": "", "default_value": "", "description": "", "helper_text": "", "is_calculated": false, "calculation_formula": "", "version": 1, "status": "draft", "natural_language": "", "created_at": "2025-06-24T05:29:39.760080", "updated_at": "2025-06-24T05:29:39.760080", "created_by": "Tarun", "updated_by": "Tarun", "attribute_status": "new", "changes_detected": []}, "validation_result": {"structure_errors": [], "required_field_errors": [], "data_type_errors": [], "custom_errors": []}, "dependency_errors": [], "uniqueness_result": {"is_unique": true, "status": "unique", "message": "Attribute with attribute_id E8.At33 is unique"}, "is_valid": true}], "operation": "parse_and_validate", "total_attributes": 19}, "status": "success"}
{"timestamp": "2025-06-24T10:35:19.102237", "endpoint": "parse-and-validate/entity-attributes", "input": {"natural_language": "leaveId | Leave ID | string | true | true | computation | LV-{YYYY}-{sequence} | Unique identifier for the leave application | Auto-generated leave identifier\n\nemployeeId | Employee ID | string | true | false | static value | | References the employee requesting leave | Select employee from dropdown\n\nstartDate | Leave Start Date | date | true | false | static value | | First day of the requested leave period | Select start date for leave", "entity_context": {"entity_name": "LeaveApplication", "entity_id": "E_LeaveApplication"}}, "output": {"success": true, "attribute_results": [], "operation": "parse_and_validate", "total_attributes": 0}, "status": "success"}
