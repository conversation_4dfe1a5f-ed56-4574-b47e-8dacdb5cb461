{"timestamp": "2025-06-24T10:46:17.075004", "endpoint": "parse-validate-mongosave/entity-relationships", "input": {"natural_language": "Relationship: LeaveApplication belongs to Employee\nSource Entity: LeaveApplication\nTarget Entity: Employee\nRelationship Type: many-to-one\nForeign Key: employeeId\nDescription: Each leave application belongs to one employee", "tenant_id": "t999", "edit_draft": true, "edit_temp": false, "edit_production": false}, "output": {"success": true, "relationship_results": [], "operation": "parse_validate_mongosave", "total_relationships": 0}, "status": "success"}
{"timestamp": "2025-06-24T12:37:59.627139", "endpoint": "parse-validate-mongosave/entity-relationships", "input": {"natural_language": "Relationship: TestEntity2 has many TestItems\nSource Entity: TestEntity2\nTarget Entity: TestItem\nRelationship Type: one-to-many\nForeign Key: testEntityId\nDescription: Each test entity can have multiple test items", "tenant_id": "t999", "edit_draft": true, "edit_temp": false, "edit_production": false}, "output": {"success": true, "relationship_results": [], "operation": "parse_validate_mongosave", "total_relationships": 0}, "status": "success"}
{"timestamp": "2025-06-24T12:46:49.421501", "endpoint": "parse-validate-mongosave/entity-relationships", "input": {"natural_language": "Primary Entity | Related Entity | Primary Key | Foreign Key | Relationship Type | On Delete | On Update | Foreign Key Type | Description\nTestEntity2 | TestItem | TestEntity2.id | TestItem.testEntityId | one-to-many | cascade | cascade | integer | Each test entity can have multiple test items\nTenant: t999", "tenant_id": "t999", "edit_draft": true, "edit_temp": false, "edit_production": false}, "output": {"success": true, "relationship_results": [{"success": false, "errors": ["Source entity with entity_id E_TestEntity2_1750769209 does not exist", "Target entity with entity_id E_TestItem_1750769209 does not exist", "Source attribute with attribute_id A_E_TestEntity2_1750769209_id_1750769209 does not exist", "Target attribute with attribute_id A_E_TestItem_1750769209_testEntityId_1750769209 does not exist"], "parsed_data": {"relationship_id": 1, "source_entity_id": "E_TestEntity2_1750769209", "target_entity_id": "E_TestItem_1750769209", "relationship_type": "one-to-many", "source_attribute_id": "A_E_TestEntity2_1750769209_id_1750769209", "target_attribute_id": "A_E_TestItem_1750769209_testEntityId_1750769209", "on_delete": "cascade", "on_update": "cascade", "foreign_key_type": "integer", "description": "Each test entity can have multiple test items", "version": "1", "status": "draft", "created_at": "2025-06-24T12:46:49.359167", "updated_at": "2025-06-24T12:46:49.359167", "created_by": "Tarun", "updated_by": "Tarun"}}], "operation": "parse_validate_mongosave", "total_relationships": 1}, "status": "success"}
