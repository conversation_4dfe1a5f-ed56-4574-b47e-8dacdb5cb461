# Schema Migration Guide for YAML Builder v2

This guide explains how to update the workflow_runtime schema to support the enhanced definitions required by the YAML Builder v2 implementation.

## Overview

The v2 implementation requires additional tables and columns in the database schema to support enhanced role, entity, GO, and LO definitions. This guide provides a step-by-step process to:

1. Deploy sample components to a temporary schema
2. Generate a migration script to update the workflow_runtime schema
3. Apply the migration script to the workflow_runtime schema

## Prerequisites

- PostgreSQL database with the workflow_runtime schema
- Python 3.6 or higher
- Access to the chat-yaml-builder/v2 directory

## Step 1: Deploy Sample Components to a Temporary Schema

The first step is to deploy sample components to a temporary schema. This will create all the necessary tables and columns that the v2 implementation expects.

```bash
cd chat-yaml-builder/v2
python deploy_to_temp_schema.py
```

This script will:
- Create a temporary schema called workflow_temp
- Deploy sample entities, GO definitions, LO definitions, and roles to the temporary schema
- Query the schema to show the tables and data that were created

You can specify a different schema name if needed:

```bash
python deploy_to_temp_schema.py --schema-name my_temp_schema
```

If you just want to query an existing schema without deploying components:

```bash
python deploy_to_temp_schema.py --query-only
```

## Step 2: Generate a Migration Script

Once you have deployed the sample components to the temporary schema, you can generate a migration script to update the workflow_runtime schema:

```bash
python generate_migration_script.py
```

This script will:
- Compare the temporary schema (workflow_temp) with the target schema (workflow_runtime)
- Generate SQL statements to update the target schema with the necessary tables and columns
- Write the SQL statements to a file called migration_script.sql

You can specify different source and target schemas if needed:

```bash
python generate_migration_script.py --source-schema my_temp_schema --target-schema my_runtime_schema
```

You can also specify a different output file:

```bash
python generate_migration_script.py --output-file my_migration_script.sql
```

## Step 3: Review and Apply the Migration Script

Before applying the migration script, you should review it to make sure it's doing what you expect:

```bash
cat migration_script.sql
```

The migration script includes the following key changes:

### Entity-related changes
- Adds `metadata` and `lifecycle_management` columns to the `entities` table
- Adds `calculated_field` and `calculation_formula` columns to the `entity_attributes` table
- Creates a new `entity_business_rules` table

### Global Objective-related changes
- Adds `process_mining_schema` and `performance_metadata` columns to the `global_objectives` table
- Creates a new `go_lo_mapping` table
- Creates a new `go_performance_metrics` table

### Local Objective-related changes
- Adds `ui_stack` and `mapping_stack` columns to the `local_objectives` table
- Adds `contextual_id`, `function_type`, `workflow_source`, and `system_function` columns to the `local_objectives` table
- Adds several columns to the `lo_input_items` and `lo_output_items` tables
- Creates new tables for data mapping: `lo_data_mapping_stack` and `lo_data_mappings`

### Role-related changes
- Creates a new `role_inheritance` table
- Adds `permission_name` column to the `role_permissions` table

### Execution pathway-related changes
- Creates new tables: `execution_pathways` and `execution_pathway_conditions`

### Agent-related changes
- Creates new tables: `agent_stack` and `agent_rights`

### Nested functions-related changes
- Creates a new `lo_nested_functions` table

Once you're satisfied with the migration script, you can apply it to the workflow_runtime schema:

```bash
psql -U your_username -d your_database -f migration_script.sql
```

Replace `your_username` and `your_database` with your PostgreSQL username and database name.

## Step 4: Verify the Migration

After applying the migration script, you can verify that the workflow_runtime schema has been updated correctly:

```bash
python test_db_completeness.py --schema workflow_runtime
```

This will check if all required tables and columns exist in the workflow_runtime schema and if they have the correct data.

You can also query the workflow_runtime schema to see the tables and data:

```bash
python deploy_to_temp_schema.py --schema-name workflow_runtime --query-only
```

## Step 5: Populate the Schema with Sample Data

After applying the migration script, you need to populate the schema with sample data to test the v2 implementation:

```bash
python test_sample_files.py --deploy-only
```

This will deploy the sample components to the workflow_runtime schema, and you should see the data in the database.

## Troubleshooting

If you encounter any issues during the migration process, here are some common problems and solutions:

### Permission Denied

If you get a "permission denied" error when trying to create or modify the schema, make sure you have the necessary permissions:

```bash
psql -U your_username -d your_database -c "GRANT ALL PRIVILEGES ON SCHEMA workflow_runtime TO your_username;"
```

### Table Already Exists

If you get a "table already exists" error when trying to create a table, you can drop the table first:

```bash
psql -U your_username -d your_database -c "DROP TABLE workflow_runtime.table_name CASCADE;"
```

Replace `table_name` with the name of the table that already exists.

### Column Already Exists

If you get a "column already exists" error when trying to add a column, you can modify the migration script to remove that ALTER TABLE statement.

### Foreign Key Constraints

If you get a foreign key constraint error, it might be because the referenced table or column doesn't exist yet. Make sure to apply the migration script in the correct order, or modify it to create the referenced tables and columns first.

### Missing Tables or Columns

If the `test_db_completeness.py` script reports missing tables or columns, you can run the following command to see what's missing:

```bash
python run_deploy_and_test.py
```

This script will deploy sample components to a temporary schema, run the completeness test, and then check specific tables that might be empty.

## Conclusion

By following this guide, you should be able to update the workflow_runtime schema to support the enhanced definitions required by the YAML Builder v2 implementation. Once the schema has been updated, you can run the test_sample_files.py script without the --use-temp-schema flag to deploy components directly to the workflow_runtime schema.

The updated schema includes support for:
- Entity business rules and lifecycle management
- GO process mining schema and performance metrics
- LO data mapping and UI/mapping stacks
- Role inheritance and enhanced permissions
- Execution pathways and conditions
- Agent stack and rights
- Nested functions

These enhancements enable the v2 implementation to provide a more comprehensive and flexible workflow definition system.
